// Code generated by protoc-gen-gogo.
// source: src/usermatchsvr/matchuser.proto
// DO NOT EDIT!

/*
	Package matchuser is a generated protocol buffer package.

	It is generated from these files:
		src/usermatchsvr/matchuser.proto

	It has these top-level messages:
		SimpleTagInfo
		UserMatchInfo
		TagInfo
		MatchedUser
		GetMatchUsersReq
		GetMatchUsersResp
		MatchBeginReq
		UidSex
		MatchBeginNReq
		MatchBeginResp
		MatchEndReq
		MatchEndResp
		GetMatchScoreReq
		GetMatchScoreResp
*/
package matchuser

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 触发进匹配池的操作类型
type MatchBeginType int32

const (
	MatchBeginType_ENUM_MATCHBEGIN_UNVALID       MatchBeginType = 0
	MatchBeginType_ENUM_MATCHBEGIN_MATCH         MatchBeginType = 1
	MatchBeginType_ENUM_MATCHBEGIN_LBS           MatchBeginType = 2
	MatchBeginType_ENUM_MATCHBEGIN_QUICKMATCH    MatchBeginType = 3
	MatchBeginType_ENUM_MATCHBEGIN_SUPLLE        MatchBeginType = 4
	MatchBeginType_ENUM_MATCHBEGIN_OPENAPP       MatchBeginType = 5
	MatchBeginType_ENUM_MATCHBEGIN_SYS_RECOMMEND MatchBeginType = 6
)

var MatchBeginType_name = map[int32]string{
	0: "ENUM_MATCHBEGIN_UNVALID",
	1: "ENUM_MATCHBEGIN_MATCH",
	2: "ENUM_MATCHBEGIN_LBS",
	3: "ENUM_MATCHBEGIN_QUICKMATCH",
	4: "ENUM_MATCHBEGIN_SUPLLE",
	5: "ENUM_MATCHBEGIN_OPENAPP",
	6: "ENUM_MATCHBEGIN_SYS_RECOMMEND",
}
var MatchBeginType_value = map[string]int32{
	"ENUM_MATCHBEGIN_UNVALID":       0,
	"ENUM_MATCHBEGIN_MATCH":         1,
	"ENUM_MATCHBEGIN_LBS":           2,
	"ENUM_MATCHBEGIN_QUICKMATCH":    3,
	"ENUM_MATCHBEGIN_SUPLLE":        4,
	"ENUM_MATCHBEGIN_OPENAPP":       5,
	"ENUM_MATCHBEGIN_SYS_RECOMMEND": 6,
}

func (x MatchBeginType) Enum() *MatchBeginType {
	p := new(MatchBeginType)
	*p = x
	return p
}
func (x MatchBeginType) String() string {
	return proto.EnumName(MatchBeginType_name, int32(x))
}
func (x *MatchBeginType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MatchBeginType_value, data, "MatchBeginType")
	if err != nil {
		return err
	}
	*x = MatchBeginType(value)
	return nil
}
func (MatchBeginType) EnumDescriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{0} }

type SimpleTagInfo struct {
	TagId   uint32 `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id"`
	TagType uint32 `protobuf:"varint,2,req,name=tag_type,json=tagType" json:"tag_type"`
}

func (m *SimpleTagInfo) Reset()                    { *m = SimpleTagInfo{} }
func (m *SimpleTagInfo) String() string            { return proto.CompactTextString(m) }
func (*SimpleTagInfo) ProtoMessage()               {}
func (*SimpleTagInfo) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{0} }

func (m *SimpleTagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *SimpleTagInfo) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type UserMatchInfo struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex        uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
	PoolLevel  uint32 `protobuf:"varint,3,req,name=pool_level,json=poolLevel" json:"pool_level"`
	FriendsNum uint32 `protobuf:"varint,4,req,name=friends_num,json=friendsNum" json:"friends_num"`
	// repeated SimpleTagInfo tag_info = 5;
	TagInfo   []*TagInfo `protobuf:"bytes,5,rep,name=tag_info,json=tagInfo" json:"tag_info,omitempty"`
	MatchTime uint32     `protobuf:"varint,6,opt,name=match_time,json=matchTime" json:"match_time"`
}

func (m *UserMatchInfo) Reset()                    { *m = UserMatchInfo{} }
func (m *UserMatchInfo) String() string            { return proto.CompactTextString(m) }
func (*UserMatchInfo) ProtoMessage()               {}
func (*UserMatchInfo) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{1} }

func (m *UserMatchInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserMatchInfo) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserMatchInfo) GetPoolLevel() uint32 {
	if m != nil {
		return m.PoolLevel
	}
	return 0
}

func (m *UserMatchInfo) GetFriendsNum() uint32 {
	if m != nil {
		return m.FriendsNum
	}
	return 0
}

func (m *UserMatchInfo) GetTagInfo() []*TagInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *UserMatchInfo) GetMatchTime() uint32 {
	if m != nil {
		return m.MatchTime
	}
	return 0
}

type TagInfo struct {
	TagName    string `protobuf:"bytes,1,req,name=tag_name,json=tagName" json:"tag_name"`
	TagId      uint32 `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id"`
	TagType    uint32 `protobuf:"varint,3,req,name=tag_type,json=tagType" json:"tag_type"`
	TagExtInfo []byte `protobuf:"bytes,4,opt,name=tag_ext_info,json=tagExtInfo" json:"tag_ext_info"`
}

func (m *TagInfo) Reset()                    { *m = TagInfo{} }
func (m *TagInfo) String() string            { return proto.CompactTextString(m) }
func (*TagInfo) ProtoMessage()               {}
func (*TagInfo) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{2} }

func (m *TagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagInfo) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *TagInfo) GetTagExtInfo() []byte {
	if m != nil {
		return m.TagExtInfo
	}
	return nil
}

type MatchedUser struct {
	Uid     uint32     `protobuf:"varint,1,req,name=uid" json:"uid"`
	TagInfo []*TagInfo `protobuf:"bytes,2,rep,name=tag_info,json=tagInfo" json:"tag_info,omitempty"`
}

func (m *MatchedUser) Reset()                    { *m = MatchedUser{} }
func (m *MatchedUser) String() string            { return proto.CompactTextString(m) }
func (*MatchedUser) ProtoMessage()               {}
func (*MatchedUser) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{3} }

func (m *MatchedUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchedUser) GetTagInfo() []*TagInfo {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

type GetMatchUsersReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Start        uint32 `protobuf:"varint,2,req,name=start" json:"start"`
	Count        uint32 `protobuf:"varint,3,req,name=count" json:"count"`
	GetMatchType uint32 `protobuf:"varint,4,opt,name=get_match_type,json=getMatchType" json:"get_match_type"`
}

func (m *GetMatchUsersReq) Reset()                    { *m = GetMatchUsersReq{} }
func (m *GetMatchUsersReq) String() string            { return proto.CompactTextString(m) }
func (*GetMatchUsersReq) ProtoMessage()               {}
func (*GetMatchUsersReq) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{4} }

func (m *GetMatchUsersReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMatchUsersReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetMatchUsersReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetMatchUsersReq) GetGetMatchType() uint32 {
	if m != nil {
		return m.GetMatchType
	}
	return 0
}

type GetMatchUsersResp struct {
	Users     []uint32       `protobuf:"varint,1,rep,name=users" json:"users,omitempty"`
	ReachEnd  bool           `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end"`
	UsersInfo []*MatchedUser `protobuf:"bytes,3,rep,name=users_info,json=usersInfo" json:"users_info,omitempty"`
}

func (m *GetMatchUsersResp) Reset()                    { *m = GetMatchUsersResp{} }
func (m *GetMatchUsersResp) String() string            { return proto.CompactTextString(m) }
func (*GetMatchUsersResp) ProtoMessage()               {}
func (*GetMatchUsersResp) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{5} }

func (m *GetMatchUsersResp) GetUsers() []uint32 {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *GetMatchUsersResp) GetReachEnd() bool {
	if m != nil {
		return m.ReachEnd
	}
	return false
}

func (m *GetMatchUsersResp) GetUsersInfo() []*MatchedUser {
	if m != nil {
		return m.UsersInfo
	}
	return nil
}

// 触发match,进入 match pool
type MatchBeginReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex            uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
	FriendsNum     uint32 `protobuf:"varint,3,opt,name=friends_num,json=friendsNum" json:"friends_num"`
	MatchBeginType uint32 `protobuf:"varint,4,opt,name=match_begin_type,json=matchBeginType" json:"match_begin_type"`
}

func (m *MatchBeginReq) Reset()                    { *m = MatchBeginReq{} }
func (m *MatchBeginReq) String() string            { return proto.CompactTextString(m) }
func (*MatchBeginReq) ProtoMessage()               {}
func (*MatchBeginReq) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{6} }

func (m *MatchBeginReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MatchBeginReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MatchBeginReq) GetFriendsNum() uint32 {
	if m != nil {
		return m.FriendsNum
	}
	return 0
}

func (m *MatchBeginReq) GetMatchBeginType() uint32 {
	if m != nil {
		return m.MatchBeginType
	}
	return 0
}

type UidSex struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Sex uint32 `protobuf:"varint,2,req,name=sex" json:"sex"`
}

func (m *UidSex) Reset()                    { *m = UidSex{} }
func (m *UidSex) String() string            { return proto.CompactTextString(m) }
func (*UidSex) ProtoMessage()               {}
func (*UidSex) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{7} }

func (m *UidSex) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UidSex) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type MatchBeginNReq struct {
	Users          []*UidSex `protobuf:"bytes,1,rep,name=users" json:"users,omitempty"`
	MatchBeginType uint32    `protobuf:"varint,2,opt,name=match_begin_type,json=matchBeginType" json:"match_begin_type"`
}

func (m *MatchBeginNReq) Reset()                    { *m = MatchBeginNReq{} }
func (m *MatchBeginNReq) String() string            { return proto.CompactTextString(m) }
func (*MatchBeginNReq) ProtoMessage()               {}
func (*MatchBeginNReq) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{8} }

func (m *MatchBeginNReq) GetUsers() []*UidSex {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *MatchBeginNReq) GetMatchBeginType() uint32 {
	if m != nil {
		return m.MatchBeginType
	}
	return 0
}

type MatchBeginResp struct {
}

func (m *MatchBeginResp) Reset()                    { *m = MatchBeginResp{} }
func (m *MatchBeginResp) String() string            { return proto.CompactTextString(m) }
func (*MatchBeginResp) ProtoMessage()               {}
func (*MatchBeginResp) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{9} }

// 触发 退出 match pool,不想被匹配
type MatchEndReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *MatchEndReq) Reset()                    { *m = MatchEndReq{} }
func (m *MatchEndReq) String() string            { return proto.CompactTextString(m) }
func (*MatchEndReq) ProtoMessage()               {}
func (*MatchEndReq) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{10} }

func (m *MatchEndReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MatchEndResp struct {
}

func (m *MatchEndResp) Reset()                    { *m = MatchEndResp{} }
func (m *MatchEndResp) String() string            { return proto.CompactTextString(m) }
func (*MatchEndResp) ProtoMessage()               {}
func (*MatchEndResp) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{11} }

type GetMatchScoreReq struct {
	OpUid      uint32 `protobuf:"varint,1,req,name=op_uid,json=opUid" json:"op_uid"`
	MatchedUid uint32 `protobuf:"varint,2,req,name=matched_uid,json=matchedUid" json:"matched_uid"`
}

func (m *GetMatchScoreReq) Reset()                    { *m = GetMatchScoreReq{} }
func (m *GetMatchScoreReq) String() string            { return proto.CompactTextString(m) }
func (*GetMatchScoreReq) ProtoMessage()               {}
func (*GetMatchScoreReq) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{12} }

func (m *GetMatchScoreReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *GetMatchScoreReq) GetMatchedUid() uint32 {
	if m != nil {
		return m.MatchedUid
	}
	return 0
}

type GetMatchScoreResp struct {
	MatchScore uint32 `protobuf:"varint,1,req,name=match_score,json=matchScore" json:"match_score"`
}

func (m *GetMatchScoreResp) Reset()                    { *m = GetMatchScoreResp{} }
func (m *GetMatchScoreResp) String() string            { return proto.CompactTextString(m) }
func (*GetMatchScoreResp) ProtoMessage()               {}
func (*GetMatchScoreResp) Descriptor() ([]byte, []int) { return fileDescriptorMatchuser, []int{13} }

func (m *GetMatchScoreResp) GetMatchScore() uint32 {
	if m != nil {
		return m.MatchScore
	}
	return 0
}

func init() {
	proto.RegisterType((*SimpleTagInfo)(nil), "matchuser.SimpleTagInfo")
	proto.RegisterType((*UserMatchInfo)(nil), "matchuser.UserMatchInfo")
	proto.RegisterType((*TagInfo)(nil), "matchuser.TagInfo")
	proto.RegisterType((*MatchedUser)(nil), "matchuser.MatchedUser")
	proto.RegisterType((*GetMatchUsersReq)(nil), "matchuser.GetMatchUsersReq")
	proto.RegisterType((*GetMatchUsersResp)(nil), "matchuser.GetMatchUsersResp")
	proto.RegisterType((*MatchBeginReq)(nil), "matchuser.MatchBeginReq")
	proto.RegisterType((*UidSex)(nil), "matchuser.UidSex")
	proto.RegisterType((*MatchBeginNReq)(nil), "matchuser.MatchBeginNReq")
	proto.RegisterType((*MatchBeginResp)(nil), "matchuser.MatchBeginResp")
	proto.RegisterType((*MatchEndReq)(nil), "matchuser.MatchEndReq")
	proto.RegisterType((*MatchEndResp)(nil), "matchuser.MatchEndResp")
	proto.RegisterType((*GetMatchScoreReq)(nil), "matchuser.GetMatchScoreReq")
	proto.RegisterType((*GetMatchScoreResp)(nil), "matchuser.GetMatchScoreResp")
	proto.RegisterEnum("matchuser.MatchBeginType", MatchBeginType_name, MatchBeginType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Matchuser service

type MatchuserClient interface {
	GetMatchUsers(ctx context.Context, in *GetMatchUsersReq, opts ...grpc.CallOption) (*GetMatchUsersResp, error)
	MatchBegin(ctx context.Context, in *MatchBeginReq, opts ...grpc.CallOption) (*MatchBeginResp, error)
	MatchEnd(ctx context.Context, in *MatchEndReq, opts ...grpc.CallOption) (*MatchEndResp, error)
	GetMatchScore(ctx context.Context, in *GetMatchScoreReq, opts ...grpc.CallOption) (*GetMatchScoreResp, error)
	MatchBeginN(ctx context.Context, in *MatchBeginNReq, opts ...grpc.CallOption) (*MatchBeginResp, error)
}

type matchuserClient struct {
	cc *grpc.ClientConn
}

func NewMatchuserClient(cc *grpc.ClientConn) MatchuserClient {
	return &matchuserClient{cc}
}

func (c *matchuserClient) GetMatchUsers(ctx context.Context, in *GetMatchUsersReq, opts ...grpc.CallOption) (*GetMatchUsersResp, error) {
	out := new(GetMatchUsersResp)
	err := grpc.Invoke(ctx, "/matchuser.matchuser/GetMatchUsers", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *matchuserClient) MatchBegin(ctx context.Context, in *MatchBeginReq, opts ...grpc.CallOption) (*MatchBeginResp, error) {
	out := new(MatchBeginResp)
	err := grpc.Invoke(ctx, "/matchuser.matchuser/MatchBegin", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *matchuserClient) MatchEnd(ctx context.Context, in *MatchEndReq, opts ...grpc.CallOption) (*MatchEndResp, error) {
	out := new(MatchEndResp)
	err := grpc.Invoke(ctx, "/matchuser.matchuser/MatchEnd", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *matchuserClient) GetMatchScore(ctx context.Context, in *GetMatchScoreReq, opts ...grpc.CallOption) (*GetMatchScoreResp, error) {
	out := new(GetMatchScoreResp)
	err := grpc.Invoke(ctx, "/matchuser.matchuser/GetMatchScore", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *matchuserClient) MatchBeginN(ctx context.Context, in *MatchBeginNReq, opts ...grpc.CallOption) (*MatchBeginResp, error) {
	out := new(MatchBeginResp)
	err := grpc.Invoke(ctx, "/matchuser.matchuser/MatchBeginN", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Matchuser service

type MatchuserServer interface {
	GetMatchUsers(context.Context, *GetMatchUsersReq) (*GetMatchUsersResp, error)
	MatchBegin(context.Context, *MatchBeginReq) (*MatchBeginResp, error)
	MatchEnd(context.Context, *MatchEndReq) (*MatchEndResp, error)
	GetMatchScore(context.Context, *GetMatchScoreReq) (*GetMatchScoreResp, error)
	MatchBeginN(context.Context, *MatchBeginNReq) (*MatchBeginResp, error)
}

func RegisterMatchuserServer(s *grpc.Server, srv MatchuserServer) {
	s.RegisterService(&_Matchuser_serviceDesc, srv)
}

func _Matchuser_GetMatchUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMatchUsersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatchuserServer).GetMatchUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/matchuser.matchuser/GetMatchUsers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatchuserServer).GetMatchUsers(ctx, req.(*GetMatchUsersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Matchuser_MatchBegin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchBeginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatchuserServer).MatchBegin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/matchuser.matchuser/MatchBegin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatchuserServer).MatchBegin(ctx, req.(*MatchBeginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Matchuser_MatchEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchEndReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatchuserServer).MatchEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/matchuser.matchuser/MatchEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatchuserServer).MatchEnd(ctx, req.(*MatchEndReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Matchuser_GetMatchScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMatchScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatchuserServer).GetMatchScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/matchuser.matchuser/GetMatchScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatchuserServer).GetMatchScore(ctx, req.(*GetMatchScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Matchuser_MatchBeginN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MatchBeginNReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MatchuserServer).MatchBeginN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/matchuser.matchuser/MatchBeginN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MatchuserServer).MatchBeginN(ctx, req.(*MatchBeginNReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Matchuser_serviceDesc = grpc.ServiceDesc{
	ServiceName: "matchuser.matchuser",
	HandlerType: (*MatchuserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMatchUsers",
			Handler:    _Matchuser_GetMatchUsers_Handler,
		},
		{
			MethodName: "MatchBegin",
			Handler:    _Matchuser_MatchBegin_Handler,
		},
		{
			MethodName: "MatchEnd",
			Handler:    _Matchuser_MatchEnd_Handler,
		},
		{
			MethodName: "GetMatchScore",
			Handler:    _Matchuser_GetMatchScore_Handler,
		},
		{
			MethodName: "MatchBeginN",
			Handler:    _Matchuser_MatchBeginN_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/usermatchsvr/matchuser.proto",
}

func (m *SimpleTagInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SimpleTagInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.TagType))
	return i, nil
}

func (m *UserMatchInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserMatchInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.PoolLevel))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.FriendsNum))
	if len(m.TagInfo) > 0 {
		for _, msg := range m.TagInfo {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintMatchuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.MatchTime))
	return i, nil
}

func (m *TagInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.TagType))
	if m.TagExtInfo != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintMatchuser(dAtA, i, uint64(len(m.TagExtInfo)))
		i += copy(dAtA[i:], m.TagExtInfo)
	}
	return i, nil
}

func (m *MatchedUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchedUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Uid))
	if len(m.TagInfo) > 0 {
		for _, msg := range m.TagInfo {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMatchuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMatchUsersReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMatchUsersReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.GetMatchType))
	return i, nil
}

func (m *GetMatchUsersResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMatchUsersResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Users) > 0 {
		for _, num := range m.Users {
			dAtA[i] = 0x8
			i++
			i = encodeVarintMatchuser(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.ReachEnd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if len(m.UsersInfo) > 0 {
		for _, msg := range m.UsersInfo {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintMatchuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MatchBeginReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchBeginReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.FriendsNum))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.MatchBeginType))
	return i, nil
}

func (m *UidSex) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UidSex) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Sex))
	return i, nil
}

func (m *MatchBeginNReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchBeginNReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Users) > 0 {
		for _, msg := range m.Users {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMatchuser(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.MatchBeginType))
	return i, nil
}

func (m *MatchBeginResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchBeginResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *MatchEndReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchEndReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *MatchEndResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchEndResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetMatchScoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMatchScoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.MatchedUid))
	return i, nil
}

func (m *GetMatchScoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMatchScoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMatchuser(dAtA, i, uint64(m.MatchScore))
	return i, nil
}

func encodeFixed64Matchuser(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Matchuser(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMatchuser(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *SimpleTagInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.TagId))
	n += 1 + sovMatchuser(uint64(m.TagType))
	return n
}

func (m *UserMatchInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.Uid))
	n += 1 + sovMatchuser(uint64(m.Sex))
	n += 1 + sovMatchuser(uint64(m.PoolLevel))
	n += 1 + sovMatchuser(uint64(m.FriendsNum))
	if len(m.TagInfo) > 0 {
		for _, e := range m.TagInfo {
			l = e.Size()
			n += 1 + l + sovMatchuser(uint64(l))
		}
	}
	n += 1 + sovMatchuser(uint64(m.MatchTime))
	return n
}

func (m *TagInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.TagName)
	n += 1 + l + sovMatchuser(uint64(l))
	n += 1 + sovMatchuser(uint64(m.TagId))
	n += 1 + sovMatchuser(uint64(m.TagType))
	if m.TagExtInfo != nil {
		l = len(m.TagExtInfo)
		n += 1 + l + sovMatchuser(uint64(l))
	}
	return n
}

func (m *MatchedUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.Uid))
	if len(m.TagInfo) > 0 {
		for _, e := range m.TagInfo {
			l = e.Size()
			n += 1 + l + sovMatchuser(uint64(l))
		}
	}
	return n
}

func (m *GetMatchUsersReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.Uid))
	n += 1 + sovMatchuser(uint64(m.Start))
	n += 1 + sovMatchuser(uint64(m.Count))
	n += 1 + sovMatchuser(uint64(m.GetMatchType))
	return n
}

func (m *GetMatchUsersResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Users) > 0 {
		for _, e := range m.Users {
			n += 1 + sovMatchuser(uint64(e))
		}
	}
	n += 2
	if len(m.UsersInfo) > 0 {
		for _, e := range m.UsersInfo {
			l = e.Size()
			n += 1 + l + sovMatchuser(uint64(l))
		}
	}
	return n
}

func (m *MatchBeginReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.Uid))
	n += 1 + sovMatchuser(uint64(m.Sex))
	n += 1 + sovMatchuser(uint64(m.FriendsNum))
	n += 1 + sovMatchuser(uint64(m.MatchBeginType))
	return n
}

func (m *UidSex) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.Uid))
	n += 1 + sovMatchuser(uint64(m.Sex))
	return n
}

func (m *MatchBeginNReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Users) > 0 {
		for _, e := range m.Users {
			l = e.Size()
			n += 1 + l + sovMatchuser(uint64(l))
		}
	}
	n += 1 + sovMatchuser(uint64(m.MatchBeginType))
	return n
}

func (m *MatchBeginResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *MatchEndReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.Uid))
	return n
}

func (m *MatchEndResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetMatchScoreReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.OpUid))
	n += 1 + sovMatchuser(uint64(m.MatchedUid))
	return n
}

func (m *GetMatchScoreResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMatchuser(uint64(m.MatchScore))
	return n
}

func sovMatchuser(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMatchuser(x uint64) (n int) {
	return sovMatchuser(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *SimpleTagInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SimpleTagInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SimpleTagInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserMatchInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserMatchInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserMatchInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PoolLevel", wireType)
			}
			m.PoolLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PoolLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendsNum", wireType)
			}
			m.FriendsNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendsNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMatchuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagInfo = append(m.TagInfo, &TagInfo{})
			if err := m.TagInfo[len(m.TagInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchTime", wireType)
			}
			m.MatchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pool_level")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("friends_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TagInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TagInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TagInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMatchuser
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagExtInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMatchuser
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagExtInfo = append(m.TagExtInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.TagExtInfo == nil {
				m.TagExtInfo = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchedUser) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchedUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchedUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMatchuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagInfo = append(m.TagInfo, &TagInfo{})
			if err := m.TagInfo[len(m.TagInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMatchUsersReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMatchUsersReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMatchUsersReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetMatchType", wireType)
			}
			m.GetMatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GetMatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMatchUsersResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMatchUsersResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMatchUsersResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMatchuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.Users = append(m.Users, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMatchuser
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMatchuser
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMatchuser
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.Users = append(m.Users, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field Users", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReachEnd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReachEnd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UsersInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMatchuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UsersInfo = append(m.UsersInfo, &MatchedUser{})
			if err := m.UsersInfo[len(m.UsersInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("reach_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchBeginReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchBeginReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchBeginReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendsNum", wireType)
			}
			m.FriendsNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendsNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchBeginType", wireType)
			}
			m.MatchBeginType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchBeginType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UidSex) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UidSex: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UidSex: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchBeginNReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchBeginNReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchBeginNReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Users", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMatchuser
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Users = append(m.Users, &UidSex{})
			if err := m.Users[len(m.Users)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchBeginType", wireType)
			}
			m.MatchBeginType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchBeginType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchBeginResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchBeginResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchBeginResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchEndReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchEndReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchEndReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchEndResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchEndResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchEndResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMatchScoreReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMatchScoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMatchScoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchedUid", wireType)
			}
			m.MatchedUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchedUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("matched_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMatchScoreResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMatchScoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMatchScoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchScore", wireType)
			}
			m.MatchScore = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchScore |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMatchuser(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMatchuser
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("match_score")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMatchuser(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMatchuser
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMatchuser
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMatchuser
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMatchuser
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMatchuser(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMatchuser = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMatchuser   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/usermatchsvr/matchuser.proto", fileDescriptorMatchuser) }

var fileDescriptorMatchuser = []byte{
	// 902 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x95, 0x5d, 0x6f, 0xdb, 0x54,
	0x18, 0xc7, 0xeb, 0x38, 0xc9, 0x9a, 0xa7, 0x4d, 0xe5, 0x9d, 0x41, 0xeb, 0xa5, 0x90, 0x79, 0x1e,
	0x83, 0x6a, 0x52, 0x5b, 0x51, 0x09, 0x09, 0x4d, 0x08, 0xa9, 0xe9, 0xac, 0x11, 0x91, 0x78, 0x25,
	0x89, 0x27, 0x21, 0x84, 0x2c, 0x2f, 0x3e, 0xf5, 0xac, 0xc5, 0x2f, 0xd8, 0xc7, 0x55, 0xc7, 0x15,
	0x12, 0x37, 0x88, 0x0b, 0x34, 0x71, 0xc1, 0x27, 0xe8, 0x3d, 0x5f, 0x63, 0x97, 0x7c, 0x01, 0x5e,
	0x54, 0x6e, 0xfa, 0x21, 0xb8, 0x40, 0xe7, 0xd8, 0xae, 0x4f, 0x5c, 0x27, 0xb0, 0x3b, 0xe7, 0xf9,
	0x3f, 0xaf, 0x3f, 0x3f, 0x4f, 0x0c, 0x4a, 0x1c, 0x4d, 0xf7, 0x93, 0x18, 0x47, 0x9e, 0x45, 0xa6,
	0xcf, 0xe3, 0xd3, 0x68, 0x9f, 0x3d, 0x50, 0xcb, 0x5e, 0x18, 0x05, 0x24, 0x40, 0xad, 0x2b, 0x43,
	0xe7, 0xbd, 0x69, 0xe0, 0x79, 0x81, 0xbf, 0x4f, 0x66, 0xa7, 0xa1, 0x3b, 0x7d, 0x31, 0xc3, 0xfb,
	0xf1, 0x8b, 0x67, 0x89, 0x3b, 0x23, 0xae, 0x4f, 0x5e, 0x86, 0x38, 0x0d, 0x50, 0x87, 0xd0, 0x1e,
	0xbb, 0x5e, 0x38, 0xc3, 0x13, 0xcb, 0xe9, 0xfb, 0x27, 0x01, 0xda, 0x86, 0x26, 0xb1, 0x1c, 0xd3,
	0xb5, 0x65, 0x41, 0xa9, 0xed, 0xb4, 0x7b, 0xf5, 0xd7, 0x7f, 0xdc, 0x59, 0x19, 0x35, 0x88, 0xe5,
	0xf4, 0x6d, 0x74, 0x07, 0x56, 0xa9, 0x48, 0xe3, 0xe5, 0x1a, 0x27, 0xdf, 0x20, 0x96, 0x33, 0x79,
	0x19, 0x62, 0xf5, 0x4f, 0x01, 0xda, 0x46, 0x8c, 0xa3, 0x21, 0x6d, 0x83, 0xe5, 0xdb, 0x04, 0x31,
	0x29, 0x25, 0xa3, 0x06, 0x6a, 0x8f, 0xf1, 0xd9, 0x5c, 0x16, 0x6a, 0x40, 0xf7, 0x00, 0xc2, 0x20,
	0x98, 0x99, 0x33, 0x7c, 0x8a, 0x67, 0xb2, 0xc8, 0xc9, 0x2d, 0x6a, 0x1f, 0x50, 0x33, 0xba, 0x0f,
	0x6b, 0x27, 0x91, 0x8b, 0x7d, 0x3b, 0x36, 0xfd, 0xc4, 0x93, 0xeb, 0x9c, 0x17, 0x64, 0x82, 0x9e,
	0x78, 0x68, 0x37, 0x6d, 0xd7, 0xf5, 0x4f, 0x02, 0xb9, 0xa1, 0x88, 0x3b, 0x6b, 0x07, 0x68, 0xaf,
	0x20, 0x96, 0x4d, 0xcc, 0x9a, 0x67, 0xad, 0xde, 0x03, 0x60, 0xaa, 0x49, 0x5c, 0x0f, 0xcb, 0x4d,
	0x45, 0x28, 0x4a, 0x33, 0xfb, 0xc4, 0xf5, 0xb0, 0xfa, 0x4a, 0x80, 0x1b, 0x39, 0xab, 0x0c, 0x87,
	0x6f, 0x79, 0x98, 0x0d, 0xd8, 0xe2, 0x70, 0xe8, 0x96, 0x87, 0x39, 0x98, 0xb5, 0xe5, 0x30, 0xc5,
	0x0a, 0x98, 0xe8, 0x7d, 0x58, 0xa7, 0x0e, 0xf8, 0x8c, 0xa4, 0x23, 0xd4, 0x15, 0x61, 0x67, 0x3d,
	0x1f, 0x93, 0x58, 0x8e, 0x76, 0x46, 0x68, 0x1b, 0xea, 0x04, 0xd6, 0x18, 0x6f, 0x6c, 0x53, 0xf4,
	0x0b, 0x89, 0xf3, 0x34, 0x6a, 0xff, 0x49, 0x43, 0xfd, 0x49, 0x00, 0xe9, 0x31, 0x26, 0x2c, 0x33,
	0xcd, 0x1b, 0x8f, 0xf0, 0x37, 0x0b, 0x73, 0x77, 0xa0, 0x11, 0x13, 0x2b, 0x22, 0xf3, 0x73, 0x32,
	0x13, 0xd5, 0xa6, 0x41, 0xe2, 0x93, 0xb9, 0x21, 0x53, 0x13, 0x7a, 0x00, 0x1b, 0x0e, 0x26, 0x66,
	0x86, 0x9d, 0x92, 0xa8, 0x73, 0xd8, 0xd7, 0x9d, 0xac, 0x3e, 0xdb, 0xad, 0xef, 0x05, 0xb8, 0x59,
	0x6a, 0x28, 0x0e, 0xd1, 0x5b, 0xd0, 0xa0, 0xfd, 0xc7, 0xb2, 0xa0, 0x88, 0x3b, 0xed, 0x51, 0xfa,
	0x03, 0xdd, 0x85, 0x56, 0x84, 0xad, 0xe9, 0x73, 0x13, 0xfb, 0x29, 0xfb, 0xd5, 0x2c, 0xe5, 0x2a,
	0x33, 0x6b, 0xbe, 0x8d, 0x3e, 0x02, 0x60, 0xbe, 0x29, 0x10, 0x91, 0x01, 0xd9, 0xe4, 0x80, 0x70,
	0x48, 0x47, 0x2d, 0xe6, 0xc9, 0xb0, 0xfc, 0x22, 0x40, 0x9b, 0x49, 0x3d, 0xec, 0xb8, 0xfe, 0x32,
	0x26, 0x8b, 0x36, 0xbc, 0xb4, 0xbc, 0x22, 0x37, 0x30, 0xbf, 0xbc, 0x7b, 0x20, 0xa5, 0x58, 0x9e,
	0xd1, 0x42, 0xd7, 0xe1, 0x6c, 0x78, 0x57, 0x5d, 0x30, 0x3c, 0x1f, 0x43, 0xd3, 0x70, 0xed, 0x31,
	0x3e, 0x7b, 0xd3, 0x86, 0x54, 0x17, 0x36, 0x8a, 0x89, 0x74, 0x3a, 0xd2, 0x07, 0x3c, 0xd4, 0xb5,
	0x83, 0x9b, 0x1c, 0x96, 0xb4, 0x46, 0xce, 0xb9, 0xaa, 0xc9, 0xda, 0x92, 0x26, 0x25, 0xbe, 0x14,
	0x7d, 0x7f, 0xea, 0xfd, 0x6c, 0x79, 0x35, 0xdf, 0x5e, 0x02, 0x53, 0xdd, 0x80, 0xf5, 0xc2, 0x2d,
	0x0e, 0xd5, 0xa7, 0xc5, 0x72, 0x8e, 0xa7, 0x41, 0x84, 0x69, 0xec, 0x36, 0x34, 0x83, 0xd0, 0x2c,
	0x87, 0x37, 0x82, 0xd0, 0x70, 0x6d, 0x4a, 0xdd, 0x4b, 0xdf, 0x28, 0xf3, 0xe0, 0x21, 0x40, 0x26,
	0x18, 0xae, 0xad, 0x3e, 0x2c, 0x76, 0x2c, 0xcb, 0x1b, 0x87, 0x57, 0xb1, 0x66, 0x4c, 0x4d, 0x73,
	0xd9, 0xd3, 0x58, 0xe6, 0xfa, 0xe0, 0x77, 0x81, 0x9f, 0x8e, 0x9d, 0xf0, 0x36, 0x6c, 0x69, 0xba,
	0x31, 0x34, 0x87, 0x87, 0x93, 0xa3, 0xcf, 0x7a, 0xda, 0xe3, 0xbe, 0x6e, 0x1a, 0xfa, 0xd3, 0xc3,
	0x41, 0xff, 0x91, 0xb4, 0x82, 0x6e, 0xc3, 0xdb, 0x65, 0x91, 0x3d, 0x4a, 0x02, 0xda, 0x82, 0x5b,
	0x65, 0x69, 0xd0, 0x1b, 0x4b, 0x35, 0xd4, 0x85, 0x4e, 0x59, 0xf8, 0xc2, 0xe8, 0x1f, 0x7d, 0x9e,
	0x06, 0x8a, 0xa8, 0x03, 0x9b, 0x65, 0x7d, 0x6c, 0x1c, 0x0f, 0x06, 0x9a, 0x54, 0xaf, 0x6a, 0xe6,
	0xc9, 0xb1, 0xa6, 0x1f, 0x1e, 0x1f, 0x4b, 0x0d, 0x74, 0x17, 0xde, 0xbd, 0x16, 0xf8, 0xe5, 0xd8,
	0x1c, 0x69, 0x47, 0x4f, 0x86, 0x43, 0x4d, 0x7f, 0x24, 0x35, 0x0f, 0xfe, 0x11, 0xa1, 0xf8, 0xbe,
	0x20, 0x1b, 0xda, 0x73, 0xd7, 0x88, 0xb6, 0xb9, 0x2d, 0x29, 0xff, 0x71, 0x74, 0xde, 0x59, 0x2c,
	0xc6, 0xa1, 0x2a, 0x7f, 0x77, 0x7e, 0x29, 0x0a, 0x3f, 0x9e, 0x5f, 0x8a, 0xad, 0xdd, 0x44, 0xf9,
	0x24, 0x71, 0xed, 0x4f, 0x1f, 0xfe, 0x7c, 0x7e, 0x29, 0xae, 0xa0, 0xaf, 0x00, 0x0a, 0xa4, 0x48,
	0x2e, 0xdf, 0x67, 0x7e, 0x84, 0x9d, 0xdb, 0x0b, 0x94, 0x3c, 0x79, 0xad, 0x2a, 0xb9, 0x01, 0xab,
	0xf9, 0x52, 0xa1, 0x6b, 0xa7, 0x9f, 0x2e, 0x64, 0x67, 0xab, 0xd2, 0x9e, 0xa7, 0x15, 0xab, 0xd2,
	0xfa, 0x05, 0x19, 0xb6, 0x18, 0x95, 0x64, 0xf2, 0xad, 0xad, 0x24, 0x73, 0xb5, 0x7a, 0xaa, 0x4a,
	0xab, 0xd4, 0x69, 0x95, 0x5b, 0xbb, 0xc9, 0x87, 0x69, 0x19, 0x65, 0x37, 0x39, 0xe0, 0xeb, 0x7d,
	0x9d, 0x9d, 0x50, 0x7a, 0xbf, 0xa8, 0x1a, 0x85, 0xfe, 0x7f, 0x28, 0x35, 0x2a, 0xc6, 0xe9, 0x34,
	0x7f, 0x38, 0xbf, 0x14, 0x7f, 0xfd, 0xb6, 0x27, 0xbd, 0xbe, 0xe8, 0x0a, 0xbf, 0x5d, 0x74, 0x85,
	0xbf, 0x2e, 0xba, 0xc2, 0xab, 0xbf, 0xbb, 0x2b, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0xe8, 0xd6,
	0x22, 0xf3, 0x90, 0x08, 0x00, 0x00,
}
