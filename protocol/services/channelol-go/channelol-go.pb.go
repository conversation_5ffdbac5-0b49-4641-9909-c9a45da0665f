// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channelol-go/channelol-go.proto

package channelol_go // import "golang.52tt.com/protocol/services/channelol-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import event "golang.52tt.com/protocol/services/channelol-go/event"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 贵族相关信息
type NobilityInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Value                uint64   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	KeepValue            uint64   `protobuf:"varint,3,opt,name=keep_value,json=keepValue,proto3" json:"keep_value,omitempty"`
	CycleTs              uint32   `protobuf:"varint,4,opt,name=cycle_ts,json=cycleTs,proto3" json:"cycle_ts,omitempty"`
	Invisible            bool     `protobuf:"varint,5,opt,name=invisible,proto3" json:"invisible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NobilityInfo) Reset()         { *m = NobilityInfo{} }
func (m *NobilityInfo) String() string { return proto.CompactTextString(m) }
func (*NobilityInfo) ProtoMessage()    {}
func (*NobilityInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{0}
}
func (m *NobilityInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NobilityInfo.Unmarshal(m, b)
}
func (m *NobilityInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NobilityInfo.Marshal(b, m, deterministic)
}
func (dst *NobilityInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NobilityInfo.Merge(dst, src)
}
func (m *NobilityInfo) XXX_Size() int {
	return xxx_messageInfo_NobilityInfo.Size(m)
}
func (m *NobilityInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_NobilityInfo.DiscardUnknown(m)
}

var xxx_messageInfo_NobilityInfo proto.InternalMessageInfo

func (m *NobilityInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *NobilityInfo) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *NobilityInfo) GetKeepValue() uint64 {
	if m != nil {
		return m.KeepValue
	}
	return 0
}

func (m *NobilityInfo) GetCycleTs() uint32 {
	if m != nil {
		return m.CycleTs
	}
	return 0
}

func (m *NobilityInfo) GetInvisible() bool {
	if m != nil {
		return m.Invisible
	}
	return false
}

type AddChannelMemberReq struct {
	AppId       uint32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ChannelId   uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType uint32 `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Uid         uint32 `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	// 下面都是进房携带的附加信息 基本用来做push
	HistoryGiftCost      uint32        `protobuf:"varint,5,opt,name=history_gift_cost,json=historyGiftCost,proto3" json:"history_gift_cost,omitempty"`
	IsMute               bool          `protobuf:"varint,6,opt,name=is_mute,json=isMute,proto3" json:"is_mute,omitempty"`
	IsRobot              bool          `protobuf:"varint,7,opt,name=is_robot,json=isRobot,proto3" json:"is_robot,omitempty"`
	IsPcHelper           bool          `protobuf:"varint,8,opt,name=is_pc_helper,json=isPcHelper,proto3" json:"is_pc_helper,omitempty"`
	IsAdmin              bool          `protobuf:"varint,9,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin,omitempty"`
	ChannelDisplayId     uint32        `protobuf:"varint,10,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	IsRoomHasPwd         bool          `protobuf:"varint,11,opt,name=is_room_has_pwd,json=isRoomHasPwd,proto3" json:"is_room_has_pwd,omitempty"`
	MarketId             uint32        `protobuf:"varint,12,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	NobilityInfo         *NobilityInfo `protobuf:"bytes,13,opt,name=nobility_info,json=nobilityInfo,proto3" json:"nobility_info,omitempty"`
	Source               uint32        `protobuf:"varint,14,opt,name=source,proto3" json:"source,omitempty"`
	FollowFriendUid      uint32        `protobuf:"varint,15,opt,name=follow_friend_uid,json=followFriendUid,proto3" json:"follow_friend_uid,omitempty"`
	HideFootprint        bool          `protobuf:"varint,16,opt,name=hide_footprint,json=hideFootprint,proto3" json:"hide_footprint,omitempty"`
	LastChannelId        uint32        `protobuf:"varint,17,opt,name=last_channel_id,json=lastChannelId,proto3" json:"last_channel_id,omitempty"`
	ChannelViewId        string        `protobuf:"bytes,18,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddChannelMemberReq) Reset()         { *m = AddChannelMemberReq{} }
func (m *AddChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelMemberReq) ProtoMessage()    {}
func (*AddChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{1}
}
func (m *AddChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelMemberReq.Unmarshal(m, b)
}
func (m *AddChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *AddChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelMemberReq.Merge(dst, src)
}
func (m *AddChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_AddChannelMemberReq.Size(m)
}
func (m *AddChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelMemberReq proto.InternalMessageInfo

func (m *AddChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AddChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelMemberReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *AddChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddChannelMemberReq) GetHistoryGiftCost() uint32 {
	if m != nil {
		return m.HistoryGiftCost
	}
	return 0
}

func (m *AddChannelMemberReq) GetIsMute() bool {
	if m != nil {
		return m.IsMute
	}
	return false
}

func (m *AddChannelMemberReq) GetIsRobot() bool {
	if m != nil {
		return m.IsRobot
	}
	return false
}

func (m *AddChannelMemberReq) GetIsPcHelper() bool {
	if m != nil {
		return m.IsPcHelper
	}
	return false
}

func (m *AddChannelMemberReq) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *AddChannelMemberReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *AddChannelMemberReq) GetIsRoomHasPwd() bool {
	if m != nil {
		return m.IsRoomHasPwd
	}
	return false
}

func (m *AddChannelMemberReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *AddChannelMemberReq) GetNobilityInfo() *NobilityInfo {
	if m != nil {
		return m.NobilityInfo
	}
	return nil
}

func (m *AddChannelMemberReq) GetSource() uint32 {
	if m != nil {
		return m.Source
	}
	return 0
}

func (m *AddChannelMemberReq) GetFollowFriendUid() uint32 {
	if m != nil {
		return m.FollowFriendUid
	}
	return 0
}

func (m *AddChannelMemberReq) GetHideFootprint() bool {
	if m != nil {
		return m.HideFootprint
	}
	return false
}

func (m *AddChannelMemberReq) GetLastChannelId() uint32 {
	if m != nil {
		return m.LastChannelId
	}
	return 0
}

func (m *AddChannelMemberReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type AddChannelMemberResp struct {
	IsAlreadyIn          bool     `protobuf:"varint,1,opt,name=is_already_in,json=isAlreadyIn,proto3" json:"is_already_in,omitempty"`
	MemberSize           uint32   `protobuf:"varint,2,opt,name=member_size,json=memberSize,proto3" json:"member_size,omitempty"`
	AdminMemberSize      uint32   `protobuf:"varint,3,opt,name=admin_member_size,json=adminMemberSize,proto3" json:"admin_member_size,omitempty"`
	ServerMsTs           uint64   `protobuf:"varint,4,opt,name=server_ms_ts,json=serverMsTs,proto3" json:"server_ms_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddChannelMemberResp) Reset()         { *m = AddChannelMemberResp{} }
func (m *AddChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelMemberResp) ProtoMessage()    {}
func (*AddChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{2}
}
func (m *AddChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddChannelMemberResp.Unmarshal(m, b)
}
func (m *AddChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *AddChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddChannelMemberResp.Merge(dst, src)
}
func (m *AddChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_AddChannelMemberResp.Size(m)
}
func (m *AddChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddChannelMemberResp proto.InternalMessageInfo

func (m *AddChannelMemberResp) GetIsAlreadyIn() bool {
	if m != nil {
		return m.IsAlreadyIn
	}
	return false
}

func (m *AddChannelMemberResp) GetMemberSize() uint32 {
	if m != nil {
		return m.MemberSize
	}
	return 0
}

func (m *AddChannelMemberResp) GetAdminMemberSize() uint32 {
	if m != nil {
		return m.AdminMemberSize
	}
	return 0
}

func (m *AddChannelMemberResp) GetServerMsTs() uint64 {
	if m != nil {
		return m.ServerMsTs
	}
	return 0
}

type RemoveChannelMemberReq struct {
	AppId       uint32 `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ChannelId   uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType uint32 `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	Uid         uint32 `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	// 下面都是退房携带的附加信息 基本用来做push
	IsAdmin              bool     `protobuf:"varint,5,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin,omitempty"`
	IsChangeChannel      bool     `protobuf:"varint,6,opt,name=is_change_channel,json=isChangeChannel,proto3" json:"is_change_channel,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,7,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	ChannelCreateUid     uint32   `protobuf:"varint,8,opt,name=channel_create_uid,json=channelCreateUid,proto3" json:"channel_create_uid,omitempty"`
	OpUid                uint32   `protobuf:"varint,9,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,10,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveChannelMemberReq) Reset()         { *m = RemoveChannelMemberReq{} }
func (m *RemoveChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMemberReq) ProtoMessage()    {}
func (*RemoveChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{3}
}
func (m *RemoveChannelMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelMemberReq.Unmarshal(m, b)
}
func (m *RemoveChannelMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelMemberReq.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelMemberReq.Merge(dst, src)
}
func (m *RemoveChannelMemberReq) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelMemberReq.Size(m)
}
func (m *RemoveChannelMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelMemberReq proto.InternalMessageInfo

func (m *RemoveChannelMemberReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *RemoveChannelMemberReq) GetIsChangeChannel() bool {
	if m != nil {
		return m.IsChangeChannel
	}
	return false
}

func (m *RemoveChannelMemberReq) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelCreateUid() uint32 {
	if m != nil {
		return m.ChannelCreateUid
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *RemoveChannelMemberReq) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type RemoveChannelMemberResp struct {
	Exist                bool     `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
	Duration             uint32   `protobuf:"varint,2,opt,name=duration,proto3" json:"duration,omitempty"`
	LeftMemberCount      uint32   `protobuf:"varint,3,opt,name=left_member_count,json=leftMemberCount,proto3" json:"left_member_count,omitempty"`
	LeftAdminCount       uint32   `protobuf:"varint,4,opt,name=left_admin_count,json=leftAdminCount,proto3" json:"left_admin_count,omitempty"`
	ServerMsTs           uint64   `protobuf:"varint,5,opt,name=server_ms_ts,json=serverMsTs,proto3" json:"server_ms_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveChannelMemberResp) Reset()         { *m = RemoveChannelMemberResp{} }
func (m *RemoveChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMemberResp) ProtoMessage()    {}
func (*RemoveChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{4}
}
func (m *RemoveChannelMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveChannelMemberResp.Unmarshal(m, b)
}
func (m *RemoveChannelMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveChannelMemberResp.Marshal(b, m, deterministic)
}
func (dst *RemoveChannelMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveChannelMemberResp.Merge(dst, src)
}
func (m *RemoveChannelMemberResp) XXX_Size() int {
	return xxx_messageInfo_RemoveChannelMemberResp.Size(m)
}
func (m *RemoveChannelMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveChannelMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveChannelMemberResp proto.InternalMessageInfo

func (m *RemoveChannelMemberResp) GetExist() bool {
	if m != nil {
		return m.Exist
	}
	return false
}

func (m *RemoveChannelMemberResp) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *RemoveChannelMemberResp) GetLeftMemberCount() uint32 {
	if m != nil {
		return m.LeftMemberCount
	}
	return 0
}

func (m *RemoveChannelMemberResp) GetLeftAdminCount() uint32 {
	if m != nil {
		return m.LeftAdminCount
	}
	return 0
}

func (m *RemoveChannelMemberResp) GetServerMsTs() uint64 {
	if m != nil {
		return m.ServerMsTs
	}
	return 0
}

type ReportExpireRemoveEventReq struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid         uint32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	ChannelType uint32 `protobuf:"varint,3,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	// Types that are valid to be assigned to Data:
	//	*ReportExpireRemoveEventReq_LeaveEvent
	//	*ReportExpireRemoveEventReq_LeaveNotify
	Data                 isReportExpireRemoveEventReq_Data `protobuf_oneof:"data"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ReportExpireRemoveEventReq) Reset()         { *m = ReportExpireRemoveEventReq{} }
func (m *ReportExpireRemoveEventReq) String() string { return proto.CompactTextString(m) }
func (*ReportExpireRemoveEventReq) ProtoMessage()    {}
func (*ReportExpireRemoveEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{5}
}
func (m *ReportExpireRemoveEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportExpireRemoveEventReq.Unmarshal(m, b)
}
func (m *ReportExpireRemoveEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportExpireRemoveEventReq.Marshal(b, m, deterministic)
}
func (dst *ReportExpireRemoveEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportExpireRemoveEventReq.Merge(dst, src)
}
func (m *ReportExpireRemoveEventReq) XXX_Size() int {
	return xxx_messageInfo_ReportExpireRemoveEventReq.Size(m)
}
func (m *ReportExpireRemoveEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportExpireRemoveEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportExpireRemoveEventReq proto.InternalMessageInfo

func (m *ReportExpireRemoveEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportExpireRemoveEventReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ReportExpireRemoveEventReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type isReportExpireRemoveEventReq_Data interface {
	isReportExpireRemoveEventReq_Data()
}

type ReportExpireRemoveEventReq_LeaveEvent struct {
	LeaveEvent *event.ChannelOLLeaveEvent `protobuf:"bytes,4,opt,name=leave_event,json=leaveEvent,proto3,oneof"`
}

type ReportExpireRemoveEventReq_LeaveNotify struct {
	LeaveNotify *event.ChannelOLExpiredLeaveNotify `protobuf:"bytes,5,opt,name=leave_notify,json=leaveNotify,proto3,oneof"`
}

func (*ReportExpireRemoveEventReq_LeaveEvent) isReportExpireRemoveEventReq_Data() {}

func (*ReportExpireRemoveEventReq_LeaveNotify) isReportExpireRemoveEventReq_Data() {}

func (m *ReportExpireRemoveEventReq) GetData() isReportExpireRemoveEventReq_Data {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *ReportExpireRemoveEventReq) GetLeaveEvent() *event.ChannelOLLeaveEvent {
	if x, ok := m.GetData().(*ReportExpireRemoveEventReq_LeaveEvent); ok {
		return x.LeaveEvent
	}
	return nil
}

func (m *ReportExpireRemoveEventReq) GetLeaveNotify() *event.ChannelOLExpiredLeaveNotify {
	if x, ok := m.GetData().(*ReportExpireRemoveEventReq_LeaveNotify); ok {
		return x.LeaveNotify
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ReportExpireRemoveEventReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ReportExpireRemoveEventReq_OneofMarshaler, _ReportExpireRemoveEventReq_OneofUnmarshaler, _ReportExpireRemoveEventReq_OneofSizer, []interface{}{
		(*ReportExpireRemoveEventReq_LeaveEvent)(nil),
		(*ReportExpireRemoveEventReq_LeaveNotify)(nil),
	}
}

func _ReportExpireRemoveEventReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ReportExpireRemoveEventReq)
	// data
	switch x := m.Data.(type) {
	case *ReportExpireRemoveEventReq_LeaveEvent:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.LeaveEvent); err != nil {
			return err
		}
	case *ReportExpireRemoveEventReq_LeaveNotify:
		b.EncodeVarint(5<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.LeaveNotify); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("ReportExpireRemoveEventReq.Data has unexpected type %T", x)
	}
	return nil
}

func _ReportExpireRemoveEventReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ReportExpireRemoveEventReq)
	switch tag {
	case 4: // data.leave_event
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(event.ChannelOLLeaveEvent)
		err := b.DecodeMessage(msg)
		m.Data = &ReportExpireRemoveEventReq_LeaveEvent{msg}
		return true, err
	case 5: // data.leave_notify
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(event.ChannelOLExpiredLeaveNotify)
		err := b.DecodeMessage(msg)
		m.Data = &ReportExpireRemoveEventReq_LeaveNotify{msg}
		return true, err
	default:
		return false, nil
	}
}

func _ReportExpireRemoveEventReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ReportExpireRemoveEventReq)
	// data
	switch x := m.Data.(type) {
	case *ReportExpireRemoveEventReq_LeaveEvent:
		s := proto.Size(x.LeaveEvent)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ReportExpireRemoveEventReq_LeaveNotify:
		s := proto.Size(x.LeaveNotify)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type ReportExpireRemoveEventResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportExpireRemoveEventResp) Reset()         { *m = ReportExpireRemoveEventResp{} }
func (m *ReportExpireRemoveEventResp) String() string { return proto.CompactTextString(m) }
func (*ReportExpireRemoveEventResp) ProtoMessage()    {}
func (*ReportExpireRemoveEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channelol_go_117dd4f49cb44ef1, []int{6}
}
func (m *ReportExpireRemoveEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportExpireRemoveEventResp.Unmarshal(m, b)
}
func (m *ReportExpireRemoveEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportExpireRemoveEventResp.Marshal(b, m, deterministic)
}
func (dst *ReportExpireRemoveEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportExpireRemoveEventResp.Merge(dst, src)
}
func (m *ReportExpireRemoveEventResp) XXX_Size() int {
	return xxx_messageInfo_ReportExpireRemoveEventResp.Size(m)
}
func (m *ReportExpireRemoveEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportExpireRemoveEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportExpireRemoveEventResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*NobilityInfo)(nil), "channelol_go.NobilityInfo")
	proto.RegisterType((*AddChannelMemberReq)(nil), "channelol_go.AddChannelMemberReq")
	proto.RegisterType((*AddChannelMemberResp)(nil), "channelol_go.AddChannelMemberResp")
	proto.RegisterType((*RemoveChannelMemberReq)(nil), "channelol_go.RemoveChannelMemberReq")
	proto.RegisterType((*RemoveChannelMemberResp)(nil), "channelol_go.RemoveChannelMemberResp")
	proto.RegisterType((*ReportExpireRemoveEventReq)(nil), "channelol_go.ReportExpireRemoveEventReq")
	proto.RegisterType((*ReportExpireRemoveEventResp)(nil), "channelol_go.ReportExpireRemoveEventResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelolGoClient is the client API for ChannelolGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelolGoClient interface {
	AddChannelMember(ctx context.Context, in *AddChannelMemberReq, opts ...grpc.CallOption) (*AddChannelMemberResp, error)
	RemoveChannelMember(ctx context.Context, in *RemoveChannelMemberReq, opts ...grpc.CallOption) (*RemoveChannelMemberResp, error)
	// 以uid和cid两种分区方式上报房间超时事件，仅提供给channelol(c++)调用
	ReportExpireRemoveEvent(ctx context.Context, in *ReportExpireRemoveEventReq, opts ...grpc.CallOption) (*ReportExpireRemoveEventResp, error)
}

type channelolGoClient struct {
	cc *grpc.ClientConn
}

func NewChannelolGoClient(cc *grpc.ClientConn) ChannelolGoClient {
	return &channelolGoClient{cc}
}

func (c *channelolGoClient) AddChannelMember(ctx context.Context, in *AddChannelMemberReq, opts ...grpc.CallOption) (*AddChannelMemberResp, error) {
	out := new(AddChannelMemberResp)
	err := c.cc.Invoke(ctx, "/channelol_go.ChannelolGo/AddChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelolGoClient) RemoveChannelMember(ctx context.Context, in *RemoveChannelMemberReq, opts ...grpc.CallOption) (*RemoveChannelMemberResp, error) {
	out := new(RemoveChannelMemberResp)
	err := c.cc.Invoke(ctx, "/channelol_go.ChannelolGo/RemoveChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelolGoClient) ReportExpireRemoveEvent(ctx context.Context, in *ReportExpireRemoveEventReq, opts ...grpc.CallOption) (*ReportExpireRemoveEventResp, error) {
	out := new(ReportExpireRemoveEventResp)
	err := c.cc.Invoke(ctx, "/channelol_go.ChannelolGo/ReportExpireRemoveEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelolGoServer is the server API for ChannelolGo service.
type ChannelolGoServer interface {
	AddChannelMember(context.Context, *AddChannelMemberReq) (*AddChannelMemberResp, error)
	RemoveChannelMember(context.Context, *RemoveChannelMemberReq) (*RemoveChannelMemberResp, error)
	// 以uid和cid两种分区方式上报房间超时事件，仅提供给channelol(c++)调用
	ReportExpireRemoveEvent(context.Context, *ReportExpireRemoveEventReq) (*ReportExpireRemoveEventResp, error)
}

func RegisterChannelolGoServer(s *grpc.Server, srv ChannelolGoServer) {
	s.RegisterService(&_ChannelolGo_serviceDesc, srv)
}

func _ChannelolGo_AddChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelolGoServer).AddChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelol_go.ChannelolGo/AddChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelolGoServer).AddChannelMember(ctx, req.(*AddChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelolGo_RemoveChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelolGoServer).RemoveChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelol_go.ChannelolGo/RemoveChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelolGoServer).RemoveChannelMember(ctx, req.(*RemoveChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelolGo_ReportExpireRemoveEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportExpireRemoveEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelolGoServer).ReportExpireRemoveEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelol_go.ChannelolGo/ReportExpireRemoveEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelolGoServer).ReportExpireRemoveEvent(ctx, req.(*ReportExpireRemoveEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelolGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelol_go.ChannelolGo",
	HandlerType: (*ChannelolGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddChannelMember",
			Handler:    _ChannelolGo_AddChannelMember_Handler,
		},
		{
			MethodName: "RemoveChannelMember",
			Handler:    _ChannelolGo_RemoveChannelMember_Handler,
		},
		{
			MethodName: "ReportExpireRemoveEvent",
			Handler:    _ChannelolGo_ReportExpireRemoveEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channelol-go/channelol-go.proto",
}

func init() {
	proto.RegisterFile("channelol-go/channelol-go.proto", fileDescriptor_channelol_go_117dd4f49cb44ef1)
}

var fileDescriptor_channelol_go_117dd4f49cb44ef1 = []byte{
	// 1030 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x96, 0xcd, 0x72, 0x1b, 0x45,
	0x10, 0xc7, 0x2d, 0x7f, 0xc8, 0x52, 0x4b, 0xb2, 0xe4, 0x71, 0x3e, 0x16, 0x85, 0x60, 0x45, 0x45,
	0x82, 0x48, 0x11, 0x99, 0x32, 0xc5, 0x99, 0x72, 0x44, 0x12, 0x8b, 0x8a, 0x43, 0x6a, 0x31, 0x39,
	0xc0, 0x61, 0x6a, 0xbd, 0xdb, 0x92, 0xa7, 0xbc, 0xda, 0x19, 0x76, 0x46, 0xb2, 0x95, 0x57, 0xa0,
	0xb8, 0xf0, 0x16, 0x3c, 0x01, 0x0f, 0xc0, 0x95, 0x3b, 0x4f, 0xc0, 0x4b, 0x70, 0xa2, 0xa6, 0x67,
	0x57, 0x96, 0x1c, 0x19, 0x7c, 0xcb, 0x49, 0xea, 0x7f, 0xf7, 0xf4, 0x76, 0xf7, 0xfc, 0x76, 0x66,
	0x61, 0x37, 0x3c, 0x0d, 0x92, 0x04, 0x63, 0x19, 0x3f, 0x19, 0xca, 0xbd, 0x79, 0xa3, 0xab, 0x52,
	0x69, 0x24, 0xab, 0xce, 0x34, 0x3e, 0x94, 0xcd, 0x5d, 0xbc, 0x30, 0x98, 0x68, 0x21, 0x93, 0x3d,
	0xa9, 0x8c, 0x90, 0x89, 0xce, 0x7f, 0x5d, 0x78, 0xf3, 0xa3, 0x85, 0x7c, 0x67, 0xc1, 0xe0, 0x2c,
	0x78, 0x82, 0x13, 0x4c, 0x8c, 0xf3, 0xb7, 0x7f, 0x2d, 0x40, 0xf5, 0x95, 0x3c, 0x11, 0xb1, 0x30,
	0xd3, 0x7e, 0x32, 0x90, 0xec, 0x16, 0x6c, 0xc4, 0x38, 0xc1, 0xd8, 0x2b, 0xb4, 0x0a, 0x9d, 0x9a,
	0xef, 0x0c, 0xab, 0x4e, 0x82, 0x78, 0x8c, 0xde, 0x6a, 0xab, 0xd0, 0x59, 0xf7, 0x9d, 0xc1, 0xee,
	0x03, 0x9c, 0x21, 0x2a, 0xee, 0x5c, 0x6b, 0xe4, 0x2a, 0x5b, 0xe5, 0x0d, 0xb9, 0x3f, 0x80, 0x52,
	0x38, 0x0d, 0x63, 0xe4, 0x46, 0x7b, 0xeb, 0x94, 0x6d, 0x93, 0xec, 0x63, 0xcd, 0x3e, 0x84, 0xb2,
	0x48, 0x26, 0x42, 0x8b, 0x93, 0x18, 0xbd, 0x8d, 0x56, 0xa1, 0x53, 0xf2, 0x2f, 0x85, 0xf6, 0xcf,
	0x1b, 0xb0, 0x73, 0x10, 0x45, 0x3d, 0x57, 0xfa, 0x11, 0x8e, 0x4e, 0x30, 0xf5, 0xf1, 0x27, 0x76,
	0x1b, 0x8a, 0x81, 0x52, 0x5c, 0x44, 0x79, 0x71, 0x81, 0x52, 0xfd, 0xc8, 0x96, 0x91, 0x75, 0x69,
	0x5d, 0xab, 0xe4, 0x2a, 0x67, 0x4a, 0x3f, 0x62, 0x0f, 0x20, 0x9f, 0x19, 0x37, 0x53, 0xe5, 0xea,
	0xac, 0xf9, 0x95, 0x4c, 0x3b, 0x9e, 0x2a, 0x64, 0x0d, 0x58, 0x1b, 0x8b, 0x28, 0x2b, 0xd2, 0xfe,
	0x65, 0x8f, 0x61, 0xfb, 0x54, 0x68, 0x23, 0xd3, 0x29, 0x1f, 0x8a, 0x81, 0xe1, 0xa1, 0xd4, 0x86,
	0x0a, 0xad, 0xf9, 0xf5, 0xcc, 0xf1, 0x42, 0x0c, 0x4c, 0x4f, 0x6a, 0xc3, 0xee, 0xc2, 0xa6, 0xd0,
	0x7c, 0x34, 0x36, 0xe8, 0x15, 0xa9, 0x95, 0xa2, 0xd0, 0x47, 0x63, 0x43, 0x03, 0x10, 0x9a, 0xa7,
	0xf2, 0x44, 0x1a, 0x6f, 0x93, 0x3c, 0x9b, 0x42, 0xfb, 0xd6, 0x64, 0x2d, 0xa8, 0x0a, 0xcd, 0x55,
	0xc8, 0x4f, 0x31, 0x56, 0x98, 0x7a, 0x25, 0x72, 0x83, 0xd0, 0xaf, 0xc3, 0x43, 0x52, 0xb2, 0xc5,
	0x41, 0x34, 0x12, 0x89, 0x57, 0xce, 0x17, 0x1f, 0x58, 0x93, 0x7d, 0x06, 0x2c, 0xef, 0x28, 0x12,
	0x5a, 0xc5, 0xc1, 0xd4, 0x36, 0x0e, 0x54, 0x5d, 0x23, 0xf3, 0x7c, 0xed, 0x1c, 0xfd, 0x88, 0x3d,
	0x84, 0x3a, 0x55, 0x21, 0x47, 0xfc, 0x34, 0xd0, 0x5c, 0x9d, 0x47, 0x5e, 0x85, 0xf2, 0x55, 0x6d,
	0x31, 0x72, 0x74, 0x18, 0xe8, 0xd7, 0xe7, 0x11, 0xbb, 0x07, 0xe5, 0x51, 0x90, 0x9e, 0xa1, 0xb1,
	0xb9, 0xaa, 0x94, 0xab, 0xe4, 0x84, 0x7e, 0xc4, 0xbe, 0x82, 0x5a, 0x92, 0x51, 0xc2, 0x45, 0x32,
	0x90, 0x5e, 0xad, 0x55, 0xe8, 0x54, 0xf6, 0x9b, 0xdd, 0x79, 0x1a, 0xbb, 0xf3, 0x20, 0xf9, 0xd5,
	0x64, 0x1e, 0xab, 0x3b, 0x50, 0xd4, 0x72, 0x9c, 0x86, 0xe8, 0x6d, 0x51, 0xea, 0xcc, 0xb2, 0x73,
	0x1e, 0xc8, 0x38, 0x96, 0xe7, 0x7c, 0x90, 0x0a, 0x4c, 0x22, 0x6e, 0xf7, 0xa1, 0xee, 0xe6, 0xec,
	0x1c, 0xcf, 0x49, 0xff, 0x5e, 0xd8, 0x46, 0xb6, 0x4e, 0x45, 0x84, 0x7c, 0x20, 0xa5, 0x51, 0xa9,
	0x48, 0x8c, 0xd7, 0xa0, 0x3e, 0x6a, 0x56, 0x7d, 0x9e, 0x8b, 0xec, 0x11, 0xd4, 0xe3, 0x40, 0x1b,
	0x3e, 0xc7, 0xc4, 0x36, 0x25, 0xac, 0x59, 0xb9, 0x37, 0xe3, 0xe2, 0x11, 0xd4, 0xf3, 0x90, 0x89,
	0xc0, 0x73, 0x1b, 0xc7, 0x5a, 0x85, 0x4e, 0xd9, 0xaf, 0x65, 0xf2, 0x1b, 0x81, 0xe7, 0xfd, 0xa8,
	0xfd, 0x5b, 0x01, 0x6e, 0xbd, 0x4b, 0xa3, 0x56, 0xac, 0x0d, 0x35, 0xbb, 0x43, 0x71, 0x8a, 0x41,
	0x64, 0xc7, 0x42, 0x54, 0x96, 0xfc, 0x8a, 0xd0, 0x07, 0x4e, 0xeb, 0x27, 0x6c, 0x17, 0x2a, 0x23,
	0x5a, 0xc1, 0xb5, 0x78, 0x8b, 0x19, 0x9c, 0xe0, 0xa4, 0xef, 0xc4, 0x5b, 0x1a, 0x00, 0xed, 0x31,
	0x9f, 0x0f, 0x73, 0x88, 0xd6, 0xc9, 0x71, 0x74, 0x19, 0xdb, 0x82, 0xaa, 0xc6, 0x74, 0x82, 0x29,
	0x1f, 0xe9, 0xfc, 0xa5, 0x5a, 0xf7, 0xc1, 0x69, 0x47, 0xfa, 0x58, 0xb7, 0xff, 0x5e, 0x85, 0x3b,
	0x3e, 0x8e, 0xe4, 0x04, 0xdf, 0xe7, 0xcb, 0x33, 0x8f, 0xee, 0xc6, 0x22, 0xba, 0x8f, 0x61, 0x5b,
	0x68, 0xda, 0x9a, 0x21, 0xe6, 0x3b, 0x94, 0xbd, 0x35, 0x75, 0xa1, 0x7b, 0xa4, 0x67, 0xa5, 0x5f,
	0x83, 0xf9, 0xe6, 0x35, 0x98, 0xcf, 0x45, 0x87, 0x29, 0x06, 0x06, 0x09, 0xa5, 0xd2, 0x42, 0x74,
	0x8f, 0x1c, 0x96, 0xa5, 0xdb, 0x50, 0x94, 0x8a, 0x22, 0xca, 0x6e, 0x1a, 0x52, 0x59, 0x79, 0x09,
	0x13, 0xb0, 0x8c, 0x89, 0x3f, 0x0a, 0x70, 0x77, 0xe9, 0x9c, 0xb5, 0xb2, 0x67, 0x25, 0x5e, 0x08,
	0x6d, 0x32, 0x1c, 0x9c, 0xc1, 0x9a, 0x50, 0x8a, 0xc6, 0x69, 0x60, 0xcf, 0xe6, 0x6c, 0xca, 0x33,
	0xdb, 0x0e, 0x25, 0xc6, 0x81, 0xc9, 0x11, 0x08, 0xe5, 0x38, 0x31, 0x39, 0x03, 0xd6, 0xe1, 0x92,
	0xf7, 0xac, 0xcc, 0x3a, 0xd0, 0xa0, 0x58, 0x07, 0x8d, 0x0b, 0x75, 0xa3, 0xdf, 0xb2, 0x3a, 0x4d,
	0xd9, 0x45, 0x5e, 0xa5, 0x65, 0xe3, 0x1d, 0x5a, 0x7e, 0x59, 0x85, 0xa6, 0x8f, 0x4a, 0xa6, 0xe6,
	0xd9, 0x85, 0x12, 0x29, 0xba, 0x8e, 0x9e, 0xd9, 0xeb, 0xc1, 0x12, 0x93, 0x6d, 0x6c, 0xe1, 0x72,
	0x63, 0x1b, 0xb0, 0x16, 0xce, 0x28, 0xb1, 0x7f, 0x6f, 0xc2, 0xc7, 0x37, 0x50, 0x89, 0x31, 0x98,
	0x20, 0xa7, 0x7b, 0x87, 0x8a, 0xad, 0xec, 0x7f, 0xb2, 0x78, 0x72, 0xb8, 0x2b, 0x29, 0x9b, 0xe5,
	0xb7, 0x2f, 0x5f, 0xda, 0x78, 0xaa, 0xe3, 0x70, 0xc5, 0x87, 0x78, 0x66, 0xb1, 0x63, 0xa8, 0xba,
	0x5c, 0x89, 0x34, 0x62, 0x30, 0xa5, 0x9e, 0x2a, 0xfb, 0x7b, 0xff, 0x99, 0xcc, 0xf5, 0x16, 0x51,
	0xce, 0x57, 0xb4, 0xec, 0x70, 0xc5, 0x77, 0x25, 0x39, 0xf3, 0x69, 0x11, 0xd6, 0xa3, 0xc0, 0x04,
	0xed, 0xfb, 0x70, 0xef, 0xda, 0x71, 0x68, 0xb5, 0xff, 0xd7, 0x2a, 0x54, 0x7a, 0xf9, 0x83, 0x5e,
	0x48, 0xf6, 0x23, 0x34, 0xae, 0x9e, 0x0b, 0xec, 0xc1, 0x62, 0x29, 0x4b, 0x6e, 0xb1, 0x66, 0xfb,
	0xff, 0x42, 0xb4, 0x6a, 0xaf, 0xb0, 0x08, 0x76, 0x96, 0x00, 0xc6, 0x3e, 0x5e, 0x5c, 0xbc, 0xfc,
	0x5d, 0x6f, 0x3e, 0xbc, 0x41, 0x14, 0x3d, 0x45, 0x59, 0x8c, 0x97, 0x76, 0xcc, 0x3a, 0x57, 0x73,
	0x5c, 0xc7, 0x49, 0xf3, 0xd3, 0x1b, 0x46, 0xda, 0x27, 0x36, 0x77, 0xfe, 0xf9, 0xfd, 0xcf, 0xe3,
	0x2d, 0xa8, 0xce, 0x7f, 0x97, 0x3c, 0xfd, 0xfc, 0x87, 0xee, 0x50, 0xc6, 0x41, 0x32, 0xec, 0x7e,
	0xb9, 0x6f, 0x4c, 0x37, 0x94, 0xa3, 0x3d, 0xfa, 0x3c, 0x09, 0x65, 0xbc, 0x67, 0x79, 0x15, 0x21,
	0xea, 0x85, 0x8f, 0xa1, 0x93, 0x22, 0xf9, 0xbf, 0xf8, 0x37, 0x00, 0x00, 0xff, 0xff, 0x01, 0xb0,
	0x56, 0x9e, 0x30, 0x09, 0x00, 0x00,
}
