// Code generated by protoc-gen-go. DO NOT EDIT.
// source: timeline-v2/timeline-v2.proto

package timelinev2

/*
namespace
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TimelineMsg_TYPE int32

const (
	TimelineMsg_INVALID                   TimelineMsg_TYPE = 0
	TimelineMsg_IM_MSG                    TimelineMsg_TYPE = 1
	TimelineMsg_GUILD_APPLY               TimelineMsg_TYPE = 2
	TimelineMsg_DEL_IM_MSG                TimelineMsg_TYPE = 3
	TimelineMsg_GUILD_QUIT                TimelineMsg_TYPE = 4
	TimelineMsg_IM_GUILD_GROUP_MEM_MODIFY TimelineMsg_TYPE = 5
	TimelineMsg_GROUP_TL_INDEX            TimelineMsg_TYPE = 6
)

var TimelineMsg_TYPE_name = map[int32]string{
	0: "INVALID",
	1: "IM_MSG",
	2: "GUILD_APPLY",
	3: "DEL_IM_MSG",
	4: "GUILD_QUIT",
	5: "IM_GUILD_GROUP_MEM_MODIFY",
	6: "GROUP_TL_INDEX",
}
var TimelineMsg_TYPE_value = map[string]int32{
	"INVALID":                   0,
	"IM_MSG":                    1,
	"GUILD_APPLY":               2,
	"DEL_IM_MSG":                3,
	"GUILD_QUIT":                4,
	"IM_GUILD_GROUP_MEM_MODIFY": 5,
	"GROUP_TL_INDEX":            6,
}

func (x TimelineMsg_TYPE) String() string {
	return proto.EnumName(TimelineMsg_TYPE_name, int32(x))
}
func (TimelineMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{0, 0}
}

// ------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
// ------------------------------------------
type TimelineMsg struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Seqid                uint32   `protobuf:"varint,2,opt,name=seqid,proto3" json:"seqid,omitempty"`
	MsgBin               []byte   `protobuf:"bytes,3,opt,name=msg_bin,json=msgBin,proto3" json:"msg_bin,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimelineMsg) Reset()         { *m = TimelineMsg{} }
func (m *TimelineMsg) String() string { return proto.CompactTextString(m) }
func (*TimelineMsg) ProtoMessage()    {}
func (*TimelineMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{0}
}
func (m *TimelineMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimelineMsg.Unmarshal(m, b)
}
func (m *TimelineMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimelineMsg.Marshal(b, m, deterministic)
}
func (dst *TimelineMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimelineMsg.Merge(dst, src)
}
func (m *TimelineMsg) XXX_Size() int {
	return xxx_messageInfo_TimelineMsg.Size(m)
}
func (m *TimelineMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_TimelineMsg.DiscardUnknown(m)
}

var xxx_messageInfo_TimelineMsg proto.InternalMessageInfo

func (m *TimelineMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *TimelineMsg) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *TimelineMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

type WriteTimelineMsgReq struct {
	Id                   uint32       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Suffix               string       `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	Msg                  *TimelineMsg `protobuf:"bytes,3,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WriteTimelineMsgReq) Reset()         { *m = WriteTimelineMsgReq{} }
func (m *WriteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgReq) ProtoMessage()    {}
func (*WriteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{1}
}
func (m *WriteTimelineMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WriteTimelineMsgReq.Unmarshal(m, b)
}
func (m *WriteTimelineMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WriteTimelineMsgReq.Marshal(b, m, deterministic)
}
func (dst *WriteTimelineMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteTimelineMsgReq.Merge(dst, src)
}
func (m *WriteTimelineMsgReq) XXX_Size() int {
	return xxx_messageInfo_WriteTimelineMsgReq.Size(m)
}
func (m *WriteTimelineMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteTimelineMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_WriteTimelineMsgReq proto.InternalMessageInfo

func (m *WriteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WriteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *WriteTimelineMsgReq) GetMsg() *TimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type WriteTimelineMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WriteTimelineMsgResp) Reset()         { *m = WriteTimelineMsgResp{} }
func (m *WriteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgResp) ProtoMessage()    {}
func (*WriteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{2}
}
func (m *WriteTimelineMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WriteTimelineMsgResp.Unmarshal(m, b)
}
func (m *WriteTimelineMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WriteTimelineMsgResp.Marshal(b, m, deterministic)
}
func (dst *WriteTimelineMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WriteTimelineMsgResp.Merge(dst, src)
}
func (m *WriteTimelineMsgResp) XXX_Size() int {
	return xxx_messageInfo_WriteTimelineMsgResp.Size(m)
}
func (m *WriteTimelineMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_WriteTimelineMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_WriteTimelineMsgResp proto.InternalMessageInfo

type BatchWriteTimelineMsgReq struct {
	Id                   uint32         `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Suffix               string         `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	MsgList              []*TimelineMsg `protobuf:"bytes,3,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchWriteTimelineMsgReq) Reset()         { *m = BatchWriteTimelineMsgReq{} }
func (m *BatchWriteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*BatchWriteTimelineMsgReq) ProtoMessage()    {}
func (*BatchWriteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{3}
}
func (m *BatchWriteTimelineMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchWriteTimelineMsgReq.Unmarshal(m, b)
}
func (m *BatchWriteTimelineMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchWriteTimelineMsgReq.Marshal(b, m, deterministic)
}
func (dst *BatchWriteTimelineMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchWriteTimelineMsgReq.Merge(dst, src)
}
func (m *BatchWriteTimelineMsgReq) XXX_Size() int {
	return xxx_messageInfo_BatchWriteTimelineMsgReq.Size(m)
}
func (m *BatchWriteTimelineMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchWriteTimelineMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchWriteTimelineMsgReq proto.InternalMessageInfo

func (m *BatchWriteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BatchWriteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *BatchWriteTimelineMsgReq) GetMsgList() []*TimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type BatchWriteTimelineMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchWriteTimelineMsgResp) Reset()         { *m = BatchWriteTimelineMsgResp{} }
func (m *BatchWriteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*BatchWriteTimelineMsgResp) ProtoMessage()    {}
func (*BatchWriteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{4}
}
func (m *BatchWriteTimelineMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchWriteTimelineMsgResp.Unmarshal(m, b)
}
func (m *BatchWriteTimelineMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchWriteTimelineMsgResp.Marshal(b, m, deterministic)
}
func (dst *BatchWriteTimelineMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchWriteTimelineMsgResp.Merge(dst, src)
}
func (m *BatchWriteTimelineMsgResp) XXX_Size() int {
	return xxx_messageInfo_BatchWriteTimelineMsgResp.Size(m)
}
func (m *BatchWriteTimelineMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchWriteTimelineMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchWriteTimelineMsgResp proto.InternalMessageInfo

type PullTimelineMsgReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Suffix               string   `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	StartSeqid           uint32   `protobuf:"varint,3,opt,name=start_seqid,json=startSeqid,proto3" json:"start_seqid,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	SkipCache            bool     `protobuf:"varint,5,opt,name=skip_cache,json=skipCache,proto3" json:"skip_cache,omitempty"`
	Reverse              bool     `protobuf:"varint,6,opt,name=reverse,proto3" json:"reverse,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PullTimelineMsgReq) Reset()         { *m = PullTimelineMsgReq{} }
func (m *PullTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgReq) ProtoMessage()    {}
func (*PullTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{5}
}
func (m *PullTimelineMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullTimelineMsgReq.Unmarshal(m, b)
}
func (m *PullTimelineMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullTimelineMsgReq.Marshal(b, m, deterministic)
}
func (dst *PullTimelineMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullTimelineMsgReq.Merge(dst, src)
}
func (m *PullTimelineMsgReq) XXX_Size() int {
	return xxx_messageInfo_PullTimelineMsgReq.Size(m)
}
func (m *PullTimelineMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PullTimelineMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_PullTimelineMsgReq proto.InternalMessageInfo

func (m *PullTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PullTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *PullTimelineMsgReq) GetStartSeqid() uint32 {
	if m != nil {
		return m.StartSeqid
	}
	return 0
}

func (m *PullTimelineMsgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullTimelineMsgReq) GetSkipCache() bool {
	if m != nil {
		return m.SkipCache
	}
	return false
}

func (m *PullTimelineMsgReq) GetReverse() bool {
	if m != nil {
		return m.Reverse
	}
	return false
}

type PullTimelineMsgResp struct {
	MsgList              []*TimelineMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *PullTimelineMsgResp) Reset()         { *m = PullTimelineMsgResp{} }
func (m *PullTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgResp) ProtoMessage()    {}
func (*PullTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{6}
}
func (m *PullTimelineMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullTimelineMsgResp.Unmarshal(m, b)
}
func (m *PullTimelineMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullTimelineMsgResp.Marshal(b, m, deterministic)
}
func (dst *PullTimelineMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullTimelineMsgResp.Merge(dst, src)
}
func (m *PullTimelineMsgResp) XXX_Size() int {
	return xxx_messageInfo_PullTimelineMsgResp.Size(m)
}
func (m *PullTimelineMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PullTimelineMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_PullTimelineMsgResp proto.InternalMessageInfo

func (m *PullTimelineMsgResp) GetMsgList() []*TimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type BatchWriteTimelineMsgV2Req struct {
	MsgList              []*WriteTimelineMsgReq `protobuf:"bytes,1,rep,name=msg_list,json=msgList,proto3" json:"msg_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchWriteTimelineMsgV2Req) Reset()         { *m = BatchWriteTimelineMsgV2Req{} }
func (m *BatchWriteTimelineMsgV2Req) String() string { return proto.CompactTextString(m) }
func (*BatchWriteTimelineMsgV2Req) ProtoMessage()    {}
func (*BatchWriteTimelineMsgV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{7}
}
func (m *BatchWriteTimelineMsgV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchWriteTimelineMsgV2Req.Unmarshal(m, b)
}
func (m *BatchWriteTimelineMsgV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchWriteTimelineMsgV2Req.Marshal(b, m, deterministic)
}
func (dst *BatchWriteTimelineMsgV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchWriteTimelineMsgV2Req.Merge(dst, src)
}
func (m *BatchWriteTimelineMsgV2Req) XXX_Size() int {
	return xxx_messageInfo_BatchWriteTimelineMsgV2Req.Size(m)
}
func (m *BatchWriteTimelineMsgV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchWriteTimelineMsgV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_BatchWriteTimelineMsgV2Req proto.InternalMessageInfo

func (m *BatchWriteTimelineMsgV2Req) GetMsgList() []*WriteTimelineMsgReq {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type BatchWriteTimelineMsgV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchWriteTimelineMsgV2Resp) Reset()         { *m = BatchWriteTimelineMsgV2Resp{} }
func (m *BatchWriteTimelineMsgV2Resp) String() string { return proto.CompactTextString(m) }
func (*BatchWriteTimelineMsgV2Resp) ProtoMessage()    {}
func (*BatchWriteTimelineMsgV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{8}
}
func (m *BatchWriteTimelineMsgV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchWriteTimelineMsgV2Resp.Unmarshal(m, b)
}
func (m *BatchWriteTimelineMsgV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchWriteTimelineMsgV2Resp.Marshal(b, m, deterministic)
}
func (dst *BatchWriteTimelineMsgV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchWriteTimelineMsgV2Resp.Merge(dst, src)
}
func (m *BatchWriteTimelineMsgV2Resp) XXX_Size() int {
	return xxx_messageInfo_BatchWriteTimelineMsgV2Resp.Size(m)
}
func (m *BatchWriteTimelineMsgV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchWriteTimelineMsgV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchWriteTimelineMsgV2Resp proto.InternalMessageInfo

type DeleteTimelineMsgReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Suffix               string   `protobuf:"bytes,2,opt,name=suffix,proto3" json:"suffix,omitempty"`
	SeqId                uint32   `protobuf:"varint,3,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	TlKey                string   `protobuf:"bytes,4,opt,name=tl_key,json=tlKey,proto3" json:"tl_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTimelineMsgReq) Reset()         { *m = DeleteTimelineMsgReq{} }
func (m *DeleteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTimelineMsgReq) ProtoMessage()    {}
func (*DeleteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{9}
}
func (m *DeleteTimelineMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTimelineMsgReq.Unmarshal(m, b)
}
func (m *DeleteTimelineMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTimelineMsgReq.Marshal(b, m, deterministic)
}
func (dst *DeleteTimelineMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTimelineMsgReq.Merge(dst, src)
}
func (m *DeleteTimelineMsgReq) XXX_Size() int {
	return xxx_messageInfo_DeleteTimelineMsgReq.Size(m)
}
func (m *DeleteTimelineMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTimelineMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTimelineMsgReq proto.InternalMessageInfo

func (m *DeleteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DeleteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *DeleteTimelineMsgReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *DeleteTimelineMsgReq) GetTlKey() string {
	if m != nil {
		return m.TlKey
	}
	return ""
}

type DeleteTimelineMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTimelineMsgResp) Reset()         { *m = DeleteTimelineMsgResp{} }
func (m *DeleteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTimelineMsgResp) ProtoMessage()    {}
func (*DeleteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{10}
}
func (m *DeleteTimelineMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTimelineMsgResp.Unmarshal(m, b)
}
func (m *DeleteTimelineMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTimelineMsgResp.Marshal(b, m, deterministic)
}
func (dst *DeleteTimelineMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTimelineMsgResp.Merge(dst, src)
}
func (m *DeleteTimelineMsgResp) XXX_Size() int {
	return xxx_messageInfo_DeleteTimelineMsgResp.Size(m)
}
func (m *DeleteTimelineMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTimelineMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTimelineMsgResp proto.InternalMessageInfo

type SetPeerReadStatusReq struct {
	PeerUid              uint32   `protobuf:"varint,1,opt,name=peer_uid,json=peerUid,proto3" json:"peer_uid,omitempty"`
	SelfAccount          string   `protobuf:"bytes,2,opt,name=self_account,json=selfAccount,proto3" json:"self_account,omitempty"`
	MsgId                uint32   `protobuf:"varint,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPeerReadStatusReq) Reset()         { *m = SetPeerReadStatusReq{} }
func (m *SetPeerReadStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetPeerReadStatusReq) ProtoMessage()    {}
func (*SetPeerReadStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{11}
}
func (m *SetPeerReadStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPeerReadStatusReq.Unmarshal(m, b)
}
func (m *SetPeerReadStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPeerReadStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetPeerReadStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPeerReadStatusReq.Merge(dst, src)
}
func (m *SetPeerReadStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetPeerReadStatusReq.Size(m)
}
func (m *SetPeerReadStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPeerReadStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPeerReadStatusReq proto.InternalMessageInfo

func (m *SetPeerReadStatusReq) GetPeerUid() uint32 {
	if m != nil {
		return m.PeerUid
	}
	return 0
}

func (m *SetPeerReadStatusReq) GetSelfAccount() string {
	if m != nil {
		return m.SelfAccount
	}
	return ""
}

func (m *SetPeerReadStatusReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type SetPeerReadStatusResp struct {
	PeerSeqId            uint32   `protobuf:"varint,1,opt,name=peer_seq_id,json=peerSeqId,proto3" json:"peer_seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPeerReadStatusResp) Reset()         { *m = SetPeerReadStatusResp{} }
func (m *SetPeerReadStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetPeerReadStatusResp) ProtoMessage()    {}
func (*SetPeerReadStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{12}
}
func (m *SetPeerReadStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPeerReadStatusResp.Unmarshal(m, b)
}
func (m *SetPeerReadStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPeerReadStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetPeerReadStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPeerReadStatusResp.Merge(dst, src)
}
func (m *SetPeerReadStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetPeerReadStatusResp.Size(m)
}
func (m *SetPeerReadStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPeerReadStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPeerReadStatusResp proto.InternalMessageInfo

func (m *SetPeerReadStatusResp) GetPeerSeqId() uint32 {
	if m != nil {
		return m.PeerSeqId
	}
	return 0
}

type BatchGetPeerReadStatusReq struct {
	SelfUid              uint32   `protobuf:"varint,1,opt,name=self_uid,json=selfUid,proto3" json:"self_uid,omitempty"`
	PeerAccountList      []string `protobuf:"bytes,2,rep,name=peer_account_list,json=peerAccountList,proto3" json:"peer_account_list,omitempty"`
	SelfSeqId            uint32   `protobuf:"varint,3,opt,name=self_seq_id,json=selfSeqId,proto3" json:"self_seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetPeerReadStatusReq) Reset()         { *m = BatchGetPeerReadStatusReq{} }
func (m *BatchGetPeerReadStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPeerReadStatusReq) ProtoMessage()    {}
func (*BatchGetPeerReadStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{13}
}
func (m *BatchGetPeerReadStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPeerReadStatusReq.Unmarshal(m, b)
}
func (m *BatchGetPeerReadStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPeerReadStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetPeerReadStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPeerReadStatusReq.Merge(dst, src)
}
func (m *BatchGetPeerReadStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetPeerReadStatusReq.Size(m)
}
func (m *BatchGetPeerReadStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPeerReadStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPeerReadStatusReq proto.InternalMessageInfo

func (m *BatchGetPeerReadStatusReq) GetSelfUid() uint32 {
	if m != nil {
		return m.SelfUid
	}
	return 0
}

func (m *BatchGetPeerReadStatusReq) GetPeerAccountList() []string {
	if m != nil {
		return m.PeerAccountList
	}
	return nil
}

func (m *BatchGetPeerReadStatusReq) GetSelfSeqId() uint32 {
	if m != nil {
		return m.SelfSeqId
	}
	return 0
}

type BatchGetPeerReadStatusResp struct {
	PeerAccountList      []string          `protobuf:"bytes,1,rep,name=peer_account_list,json=peerAccountList,proto3" json:"peer_account_list,omitempty"`
	StatusList           []*PeerReadStatus `protobuf:"bytes,2,rep,name=status_list,json=statusList,proto3" json:"status_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetPeerReadStatusResp) Reset()         { *m = BatchGetPeerReadStatusResp{} }
func (m *BatchGetPeerReadStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPeerReadStatusResp) ProtoMessage()    {}
func (*BatchGetPeerReadStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{14}
}
func (m *BatchGetPeerReadStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetPeerReadStatusResp.Unmarshal(m, b)
}
func (m *BatchGetPeerReadStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetPeerReadStatusResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetPeerReadStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetPeerReadStatusResp.Merge(dst, src)
}
func (m *BatchGetPeerReadStatusResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetPeerReadStatusResp.Size(m)
}
func (m *BatchGetPeerReadStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetPeerReadStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetPeerReadStatusResp proto.InternalMessageInfo

func (m *BatchGetPeerReadStatusResp) GetPeerAccountList() []string {
	if m != nil {
		return m.PeerAccountList
	}
	return nil
}

func (m *BatchGetPeerReadStatusResp) GetStatusList() []*PeerReadStatus {
	if m != nil {
		return m.StatusList
	}
	return nil
}

type PeerReadStatus struct {
	MsgId                uint32   `protobuf:"varint,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id,omitempty"`
	SeqId                uint32   `protobuf:"varint,2,opt,name=seq_id,json=seqId,proto3" json:"seq_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PeerReadStatus) Reset()         { *m = PeerReadStatus{} }
func (m *PeerReadStatus) String() string { return proto.CompactTextString(m) }
func (*PeerReadStatus) ProtoMessage()    {}
func (*PeerReadStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_timeline_v2_7da778be2ae9a982, []int{15}
}
func (m *PeerReadStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PeerReadStatus.Unmarshal(m, b)
}
func (m *PeerReadStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PeerReadStatus.Marshal(b, m, deterministic)
}
func (dst *PeerReadStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PeerReadStatus.Merge(dst, src)
}
func (m *PeerReadStatus) XXX_Size() int {
	return xxx_messageInfo_PeerReadStatus.Size(m)
}
func (m *PeerReadStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_PeerReadStatus.DiscardUnknown(m)
}

var xxx_messageInfo_PeerReadStatus proto.InternalMessageInfo

func (m *PeerReadStatus) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *PeerReadStatus) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func init() {
	proto.RegisterType((*TimelineMsg)(nil), "timelinev2.TimelineMsg")
	proto.RegisterType((*WriteTimelineMsgReq)(nil), "timelinev2.WriteTimelineMsgReq")
	proto.RegisterType((*WriteTimelineMsgResp)(nil), "timelinev2.WriteTimelineMsgResp")
	proto.RegisterType((*BatchWriteTimelineMsgReq)(nil), "timelinev2.BatchWriteTimelineMsgReq")
	proto.RegisterType((*BatchWriteTimelineMsgResp)(nil), "timelinev2.BatchWriteTimelineMsgResp")
	proto.RegisterType((*PullTimelineMsgReq)(nil), "timelinev2.PullTimelineMsgReq")
	proto.RegisterType((*PullTimelineMsgResp)(nil), "timelinev2.PullTimelineMsgResp")
	proto.RegisterType((*BatchWriteTimelineMsgV2Req)(nil), "timelinev2.BatchWriteTimelineMsgV2Req")
	proto.RegisterType((*BatchWriteTimelineMsgV2Resp)(nil), "timelinev2.BatchWriteTimelineMsgV2Resp")
	proto.RegisterType((*DeleteTimelineMsgReq)(nil), "timelinev2.DeleteTimelineMsgReq")
	proto.RegisterType((*DeleteTimelineMsgResp)(nil), "timelinev2.DeleteTimelineMsgResp")
	proto.RegisterType((*SetPeerReadStatusReq)(nil), "timelinev2.SetPeerReadStatusReq")
	proto.RegisterType((*SetPeerReadStatusResp)(nil), "timelinev2.SetPeerReadStatusResp")
	proto.RegisterType((*BatchGetPeerReadStatusReq)(nil), "timelinev2.BatchGetPeerReadStatusReq")
	proto.RegisterType((*BatchGetPeerReadStatusResp)(nil), "timelinev2.BatchGetPeerReadStatusResp")
	proto.RegisterType((*PeerReadStatus)(nil), "timelinev2.PeerReadStatus")
	proto.RegisterEnum("timelinev2.TimelineMsg_TYPE", TimelineMsg_TYPE_name, TimelineMsg_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// TimeLineSvrClient is the client API for TimeLineSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type TimeLineSvrClient interface {
	// 写单条timeline
	WriteTimelineMsg(ctx context.Context, in *WriteTimelineMsgReq, opts ...grpc.CallOption) (*WriteTimelineMsgResp, error)
	// 拉多条timeline，建议数量控制在500条以下
	PullTimelineMsg(ctx context.Context, in *PullTimelineMsgReq, opts ...grpc.CallOption) (*PullTimelineMsgResp, error)
	// 批量写多条相同Id和suffix的timeline
	BatchWriteTimelineMsg(ctx context.Context, in *BatchWriteTimelineMsgReq, opts ...grpc.CallOption) (*BatchWriteTimelineMsgResp, error)
	// 批量写多条不同id和suffix的timeline
	BatchWriteTimelineMsgV2(ctx context.Context, in *BatchWriteTimelineMsgV2Req, opts ...grpc.CallOption) (*BatchWriteTimelineMsgV2Resp, error)
	// 删除单条timeline（业务勿用，仅限于消灭脏数据）
	DeleteTimelineMsg(ctx context.Context, in *DeleteTimelineMsgReq, opts ...grpc.CallOption) (*DeleteTimelineMsgResp, error)
}

type timeLineSvrClient struct {
	cc *grpc.ClientConn
}

func NewTimeLineSvrClient(cc *grpc.ClientConn) TimeLineSvrClient {
	return &timeLineSvrClient{cc}
}

func (c *timeLineSvrClient) WriteTimelineMsg(ctx context.Context, in *WriteTimelineMsgReq, opts ...grpc.CallOption) (*WriteTimelineMsgResp, error) {
	out := new(WriteTimelineMsgResp)
	err := c.cc.Invoke(ctx, "/timelinev2.TimeLineSvr/WriteTimelineMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) PullTimelineMsg(ctx context.Context, in *PullTimelineMsgReq, opts ...grpc.CallOption) (*PullTimelineMsgResp, error) {
	out := new(PullTimelineMsgResp)
	err := c.cc.Invoke(ctx, "/timelinev2.TimeLineSvr/PullTimelineMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) BatchWriteTimelineMsg(ctx context.Context, in *BatchWriteTimelineMsgReq, opts ...grpc.CallOption) (*BatchWriteTimelineMsgResp, error) {
	out := new(BatchWriteTimelineMsgResp)
	err := c.cc.Invoke(ctx, "/timelinev2.TimeLineSvr/BatchWriteTimelineMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) BatchWriteTimelineMsgV2(ctx context.Context, in *BatchWriteTimelineMsgV2Req, opts ...grpc.CallOption) (*BatchWriteTimelineMsgV2Resp, error) {
	out := new(BatchWriteTimelineMsgV2Resp)
	err := c.cc.Invoke(ctx, "/timelinev2.TimeLineSvr/BatchWriteTimelineMsgV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) DeleteTimelineMsg(ctx context.Context, in *DeleteTimelineMsgReq, opts ...grpc.CallOption) (*DeleteTimelineMsgResp, error) {
	out := new(DeleteTimelineMsgResp)
	err := c.cc.Invoke(ctx, "/timelinev2.TimeLineSvr/DeleteTimelineMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TimeLineSvrServer is the server API for TimeLineSvr service.
type TimeLineSvrServer interface {
	// 写单条timeline
	WriteTimelineMsg(context.Context, *WriteTimelineMsgReq) (*WriteTimelineMsgResp, error)
	// 拉多条timeline，建议数量控制在500条以下
	PullTimelineMsg(context.Context, *PullTimelineMsgReq) (*PullTimelineMsgResp, error)
	// 批量写多条相同Id和suffix的timeline
	BatchWriteTimelineMsg(context.Context, *BatchWriteTimelineMsgReq) (*BatchWriteTimelineMsgResp, error)
	// 批量写多条不同id和suffix的timeline
	BatchWriteTimelineMsgV2(context.Context, *BatchWriteTimelineMsgV2Req) (*BatchWriteTimelineMsgV2Resp, error)
	// 删除单条timeline（业务勿用，仅限于消灭脏数据）
	DeleteTimelineMsg(context.Context, *DeleteTimelineMsgReq) (*DeleteTimelineMsgResp, error)
}

func RegisterTimeLineSvrServer(s *grpc.Server, srv TimeLineSvrServer) {
	s.RegisterService(&_TimeLineSvr_serviceDesc, srv)
}

func _TimeLineSvr_WriteTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).WriteTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.TimeLineSvr/WriteTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).WriteTimelineMsg(ctx, req.(*WriteTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_PullTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).PullTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.TimeLineSvr/PullTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).PullTimelineMsg(ctx, req.(*PullTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_BatchWriteTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchWriteTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).BatchWriteTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.TimeLineSvr/BatchWriteTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).BatchWriteTimelineMsg(ctx, req.(*BatchWriteTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_BatchWriteTimelineMsgV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchWriteTimelineMsgV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).BatchWriteTimelineMsgV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.TimeLineSvr/BatchWriteTimelineMsgV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).BatchWriteTimelineMsgV2(ctx, req.(*BatchWriteTimelineMsgV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_DeleteTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).DeleteTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.TimeLineSvr/DeleteTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).DeleteTimelineMsg(ctx, req.(*DeleteTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TimeLineSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "timelinev2.TimeLineSvr",
	HandlerType: (*TimeLineSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WriteTimelineMsg",
			Handler:    _TimeLineSvr_WriteTimelineMsg_Handler,
		},
		{
			MethodName: "PullTimelineMsg",
			Handler:    _TimeLineSvr_PullTimelineMsg_Handler,
		},
		{
			MethodName: "BatchWriteTimelineMsg",
			Handler:    _TimeLineSvr_BatchWriteTimelineMsg_Handler,
		},
		{
			MethodName: "BatchWriteTimelineMsgV2",
			Handler:    _TimeLineSvr_BatchWriteTimelineMsgV2_Handler,
		},
		{
			MethodName: "DeleteTimelineMsg",
			Handler:    _TimeLineSvr_DeleteTimelineMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "timeline-v2/timeline-v2.proto",
}

// ReadStatusClient is the client API for ReadStatus service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ReadStatusClient interface {
	SetPeerReadStatus(ctx context.Context, in *SetPeerReadStatusReq, opts ...grpc.CallOption) (*SetPeerReadStatusResp, error)
	BatchGetPeerReadStatus(ctx context.Context, in *BatchGetPeerReadStatusReq, opts ...grpc.CallOption) (*BatchGetPeerReadStatusResp, error)
}

type readStatusClient struct {
	cc *grpc.ClientConn
}

func NewReadStatusClient(cc *grpc.ClientConn) ReadStatusClient {
	return &readStatusClient{cc}
}

func (c *readStatusClient) SetPeerReadStatus(ctx context.Context, in *SetPeerReadStatusReq, opts ...grpc.CallOption) (*SetPeerReadStatusResp, error) {
	out := new(SetPeerReadStatusResp)
	err := c.cc.Invoke(ctx, "/timelinev2.ReadStatus/SetPeerReadStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *readStatusClient) BatchGetPeerReadStatus(ctx context.Context, in *BatchGetPeerReadStatusReq, opts ...grpc.CallOption) (*BatchGetPeerReadStatusResp, error) {
	out := new(BatchGetPeerReadStatusResp)
	err := c.cc.Invoke(ctx, "/timelinev2.ReadStatus/BatchGetPeerReadStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ReadStatusServer is the server API for ReadStatus service.
type ReadStatusServer interface {
	SetPeerReadStatus(context.Context, *SetPeerReadStatusReq) (*SetPeerReadStatusResp, error)
	BatchGetPeerReadStatus(context.Context, *BatchGetPeerReadStatusReq) (*BatchGetPeerReadStatusResp, error)
}

func RegisterReadStatusServer(s *grpc.Server, srv ReadStatusServer) {
	s.RegisterService(&_ReadStatus_serviceDesc, srv)
}

func _ReadStatus_SetPeerReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPeerReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReadStatusServer).SetPeerReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.ReadStatus/SetPeerReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReadStatusServer).SetPeerReadStatus(ctx, req.(*SetPeerReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ReadStatus_BatchGetPeerReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPeerReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ReadStatusServer).BatchGetPeerReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/timelinev2.ReadStatus/BatchGetPeerReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ReadStatusServer).BatchGetPeerReadStatus(ctx, req.(*BatchGetPeerReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ReadStatus_serviceDesc = grpc.ServiceDesc{
	ServiceName: "timelinev2.ReadStatus",
	HandlerType: (*ReadStatusServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetPeerReadStatus",
			Handler:    _ReadStatus_SetPeerReadStatus_Handler,
		},
		{
			MethodName: "BatchGetPeerReadStatus",
			Handler:    _ReadStatus_BatchGetPeerReadStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "timeline-v2/timeline-v2.proto",
}

func init() {
	proto.RegisterFile("timeline-v2/timeline-v2.proto", fileDescriptor_timeline_v2_7da778be2ae9a982)
}

var fileDescriptor_timeline_v2_7da778be2ae9a982 = []byte{
	// 822 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xdd, 0x6e, 0xf2, 0x46,
	0x10, 0x8d, 0x31, 0x3f, 0x61, 0x9c, 0x12, 0xb2, 0x81, 0xe0, 0x38, 0x22, 0x21, 0x56, 0x9b, 0xd2,
	0x4a, 0x4d, 0x25, 0xf7, 0xa2, 0x52, 0x2b, 0x55, 0x4a, 0x0a, 0x45, 0x56, 0x21, 0xa1, 0x06, 0xf2,
	0x77, 0x63, 0x11, 0x58, 0xc8, 0x2a, 0x86, 0x38, 0xde, 0x05, 0x35, 0xb7, 0xad, 0xfa, 0x36, 0x7d,
	0x9b, 0x4a, 0x7d, 0x8c, 0x3e, 0x43, 0xb5, 0x6b, 0x27, 0x98, 0x60, 0x12, 0x94, 0xef, 0x8e, 0x99,
	0x1d, 0xcf, 0x39, 0x73, 0xf6, 0xcc, 0x0a, 0x28, 0x32, 0x32, 0xc2, 0x0e, 0x19, 0xe3, 0x6f, 0xa6,
	0xc6, 0xb7, 0xa1, 0xdf, 0xc7, 0xae, 0xf7, 0xc0, 0x1e, 0x10, 0x3c, 0xa7, 0xa6, 0x86, 0xfe, 0xaf,
	0x04, 0x4a, 0x3b, 0x08, 0x1b, 0x74, 0x88, 0x10, 0xc4, 0xd9, 0x93, 0x8b, 0x55, 0xa9, 0x24, 0x95,
	0x3f, 0xb3, 0xc4, 0x6f, 0x94, 0x83, 0x04, 0xc5, 0x8f, 0xa4, 0xaf, 0xc6, 0x44, 0xd2, 0x0f, 0x50,
	0x01, 0x52, 0x23, 0x3a, 0xb4, 0x6f, 0xc9, 0x58, 0x95, 0x4b, 0x52, 0x79, 0xc3, 0x4a, 0x8e, 0xe8,
	0xf0, 0x94, 0x8c, 0xf5, 0x3f, 0x25, 0x88, 0xb7, 0xaf, 0x9b, 0x55, 0xa4, 0x40, 0xca, 0x3c, 0xbb,
	0x38, 0xa9, 0x9b, 0x95, 0xec, 0x1a, 0x02, 0x48, 0x9a, 0x0d, 0xbb, 0xd1, 0xaa, 0x65, 0x25, 0xb4,
	0x09, 0x4a, 0xad, 0x63, 0xd6, 0x2b, 0xf6, 0x49, 0xb3, 0x59, 0xbf, 0xce, 0xc6, 0x50, 0x06, 0xa0,
	0x52, 0xad, 0xdb, 0x41, 0x81, 0xcc, 0x63, 0xbf, 0xe0, 0xb7, 0x8e, 0xd9, 0xce, 0xc6, 0x51, 0x11,
	0x76, 0xcd, 0x86, 0xed, 0xa7, 0x6a, 0xd6, 0x79, 0xa7, 0x69, 0x37, 0xaa, 0x0d, 0xbb, 0x71, 0x5e,
	0x31, 0x7f, 0xb9, 0xce, 0x26, 0x10, 0x82, 0x8c, 0x9f, 0x6d, 0xd7, 0x6d, 0xf3, 0xac, 0x52, 0xbd,
	0xca, 0x26, 0xf5, 0x3b, 0xd8, 0xbe, 0xf4, 0x08, 0xc3, 0xa1, 0xe1, 0x2c, 0xfc, 0x88, 0x32, 0x10,
	0x23, 0xfd, 0x60, 0xba, 0x18, 0xe9, 0xa3, 0x1d, 0x48, 0xd2, 0xc9, 0x60, 0x40, 0x7e, 0x17, 0xc3,
	0xa5, 0xad, 0x20, 0x42, 0x5f, 0x81, 0x3c, 0xa2, 0x43, 0x31, 0x99, 0x62, 0x14, 0x8e, 0x67, 0x8a,
	0x1d, 0x87, 0x1b, 0xf2, 0x1a, 0x7d, 0x07, 0x72, 0x8b, 0x48, 0xd4, 0xd5, 0xa7, 0xa0, 0x9e, 0x76,
	0x59, 0xef, 0xee, 0x53, 0x68, 0x18, 0xb0, 0xce, 0x45, 0x76, 0x08, 0x65, 0xaa, 0x5c, 0x92, 0xdf,
	0xe2, 0xc2, 0x6f, 0xa3, 0x4e, 0x28, 0xd3, 0xf7, 0x60, 0x77, 0x09, 0x2e, 0x75, 0xf5, 0xbf, 0x25,
	0x40, 0xcd, 0x89, 0xe3, 0x7c, 0x90, 0xcf, 0x01, 0x28, 0x94, 0x75, 0x3d, 0x66, 0xfb, 0x86, 0x90,
	0xc5, 0x07, 0x20, 0x52, 0x2d, 0xe1, 0x8a, 0x1c, 0x24, 0x1c, 0x32, 0x22, 0x4c, 0x8d, 0xfb, 0x5e,
	0x11, 0x01, 0x2a, 0x02, 0xd0, 0x7b, 0xe2, 0xda, 0xbd, 0x6e, 0xef, 0x0e, 0xab, 0x89, 0x92, 0x54,
	0x5e, 0xb7, 0xd2, 0x3c, 0xf3, 0x33, 0x4f, 0x20, 0x15, 0x52, 0x1e, 0x9e, 0x62, 0x8f, 0x62, 0x35,
	0x29, 0xce, 0x9e, 0x43, 0xdd, 0x84, 0xed, 0x05, 0xb6, 0xd4, 0x9d, 0x93, 0x45, 0x5a, 0x51, 0x96,
	0x2b, 0xd0, 0x22, 0x65, 0xb9, 0x30, 0xb8, 0x00, 0x3f, 0x2c, 0x74, 0x3c, 0x08, 0x77, 0x8c, 0xb8,
	0xc3, 0x59, 0xe7, 0x22, 0xec, 0x2d, 0xed, 0x4c, 0x5d, 0xdd, 0x81, 0x5c, 0x05, 0x3b, 0xf8, 0xc3,
	0x1e, 0xc8, 0x43, 0x92, 0xe2, 0x47, 0xfb, 0x45, 0x6e, 0xbe, 0x7f, 0x66, 0x9f, 0xa7, 0x99, 0x63,
	0xdf, 0xe3, 0x27, 0x21, 0x75, 0xda, 0x4a, 0x30, 0xe7, 0x57, 0xfc, 0xa4, 0x17, 0x20, 0x1f, 0x81,
	0x46, 0x5d, 0xfd, 0x1e, 0x72, 0x2d, 0xcc, 0x9a, 0x18, 0x7b, 0x16, 0xee, 0xf6, 0x5b, 0xac, 0xcb,
	0x26, 0x94, 0xd3, 0xd8, 0x85, 0x75, 0x17, 0x63, 0xcf, 0x9e, 0xbc, 0x90, 0x49, 0xf1, 0xb8, 0x43,
	0xfa, 0xe8, 0x10, 0x36, 0x28, 0x76, 0x06, 0x76, 0xb7, 0xd7, 0x7b, 0x98, 0x8c, 0x59, 0xc0, 0x4b,
	0xe1, 0xb9, 0x13, 0x3f, 0xc5, 0x59, 0x70, 0xdd, 0x66, 0xe4, 0x46, 0x74, 0x68, 0xf6, 0xf5, 0xef,
	0x21, 0x1f, 0x01, 0x46, 0x5d, 0xb4, 0x0f, 0x8a, 0x40, 0x0b, 0x26, 0xf2, 0x01, 0xd3, 0x3c, 0xd5,
	0xe2, 0x53, 0xe9, 0x7f, 0x48, 0x81, 0x7b, 0x6b, 0x4b, 0xb8, 0x0a, 0x42, 0x21, 0xae, 0x3c, 0xe6,
	0x5c, 0xbf, 0x86, 0x2d, 0xd1, 0x38, 0xe0, 0xea, 0xdf, 0x64, 0xac, 0x24, 0x97, 0xd3, 0xd6, 0x26,
	0x3f, 0x08, 0x08, 0xf3, 0x0b, 0xe3, 0x24, 0x44, 0x9b, 0x39, 0x59, 0xd3, 0x3c, 0xe5, 0x93, 0xf8,
	0x4b, 0x0a, 0xbc, 0x52, 0x8b, 0x9c, 0x21, 0x12, 0x4a, 0x8a, 0x86, 0xfa, 0x51, 0x2c, 0x0c, 0x9b,
	0xd0, 0x19, 0x21, 0xc5, 0xd0, 0xc2, 0xd6, 0x7a, 0x05, 0x00, 0x7e, 0xb9, 0x30, 0xd6, 0x4f, 0x90,
	0x99, 0x3f, 0x0d, 0xc9, 0x2d, 0x85, 0xe4, 0x0e, 0x59, 0x24, 0x16, 0xb2, 0x88, 0xf1, 0x9f, 0xec,
	0x3f, 0xee, 0x75, 0x32, 0xc6, 0xad, 0xa9, 0x87, 0x2e, 0x21, 0xfb, 0xda, 0xa3, 0xe8, 0x3d, 0x9b,
	0x6b, 0xa5, 0xb7, 0x0b, 0xa8, 0xab, 0xaf, 0xa1, 0x36, 0x6c, 0xbe, 0x5a, 0x53, 0xb4, 0x3f, 0x37,
	0xe3, 0xc2, 0x8b, 0xa3, 0x1d, 0xbc, 0x79, 0x2e, 0xba, 0x0e, 0x20, 0x1f, 0xb9, 0x57, 0xe8, 0xf3,
	0xf0, 0xb7, 0xcb, 0xde, 0x58, 0xed, 0x8b, 0x15, 0xaa, 0x04, 0x8e, 0x03, 0x85, 0x25, 0xfb, 0x8b,
	0x8e, 0xde, 0xed, 0x21, 0x9e, 0x0f, 0xed, 0xcb, 0x95, 0xea, 0x04, 0xda, 0x0d, 0x6c, 0x2d, 0x2c,
	0x28, 0x9a, 0x13, 0x39, 0xea, 0xb5, 0xd0, 0x0e, 0xdf, 0xa9, 0xe0, 0xbd, 0x8d, 0x7f, 0x24, 0x80,
	0x90, 0x5b, 0x6e, 0x60, 0x6b, 0x61, 0x0b, 0xe7, 0xa1, 0xa2, 0x5e, 0x84, 0x79, 0xa8, 0xc8, 0x35,
	0xd6, 0xd7, 0x10, 0x81, 0x9d, 0xe8, 0x15, 0x41, 0x8b, 0xba, 0x47, 0xed, 0xb2, 0x76, 0xb4, 0x4a,
	0x19, 0x87, 0xba, 0x4d, 0x8a, 0xbf, 0x2d, 0xdf, 0xfd, 0x1f, 0x00, 0x00, 0xff, 0xff, 0xab, 0xef,
	0xa4, 0xf0, 0xd7, 0x08, 0x00, 0x00,
}
