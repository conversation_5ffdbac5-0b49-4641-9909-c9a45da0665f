// Code generated by protoc-gen-go. DO NOT EDIT.
// source: new-recharge-act/new-recharge-act.proto

package new_recharge_act // import "golang.52tt.com/protocol/services/new-recharge-act"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RechargeActAwardInfo_ItemType int32

const (
	RechargeActAwardInfo_ITEM_TYPE_UNSPECIFIED             RechargeActAwardInfo_ItemType = 0
	RechargeActAwardInfo_ITEM_TYPE_TBEAN                   RechargeActAwardInfo_ItemType = 1
	RechargeActAwardInfo_ITEM_TYPE_RED_DIAMONDD            RechargeActAwardInfo_ItemType = 2
	RechargeActAwardInfo_ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS RechargeActAwardInfo_ItemType = 3
	RechargeActAwardInfo_ITEM_TYPE_HEADWEAR                RechargeActAwardInfo_ItemType = 4
	RechargeActAwardInfo_ITEM_TYPE_NAMEPLATE               RechargeActAwardInfo_ItemType = 5
	RechargeActAwardInfo_ITEM_TYPE_HORSE                   RechargeActAwardInfo_ItemType = 6
	RechargeActAwardInfo_ITEM_TYPE_OFFICIAL_CERT           RechargeActAwardInfo_ItemType = 7
	RechargeActAwardInfo_ITEM_TYPE_USER_DECORATION         RechargeActAwardInfo_ItemType = 8
	RechargeActAwardInfo_ITEM_TYPE_MEDAL                   RechargeActAwardInfo_ItemType = 9
)

var RechargeActAwardInfo_ItemType_name = map[int32]string{
	0: "ITEM_TYPE_UNSPECIFIED",
	1: "ITEM_TYPE_TBEAN",
	2: "ITEM_TYPE_RED_DIAMONDD",
	3: "ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS",
	4: "ITEM_TYPE_HEADWEAR",
	5: "ITEM_TYPE_NAMEPLATE",
	6: "ITEM_TYPE_HORSE",
	7: "ITEM_TYPE_OFFICIAL_CERT",
	8: "ITEM_TYPE_USER_DECORATION",
	9: "ITEM_TYPE_MEDAL",
}
var RechargeActAwardInfo_ItemType_value = map[string]int32{
	"ITEM_TYPE_UNSPECIFIED":             0,
	"ITEM_TYPE_TBEAN":                   1,
	"ITEM_TYPE_RED_DIAMONDD":            2,
	"ITEM_TYPE_GOLD_DIAMOND_WITH_BOUNS": 3,
	"ITEM_TYPE_HEADWEAR":                4,
	"ITEM_TYPE_NAMEPLATE":               5,
	"ITEM_TYPE_HORSE":                   6,
	"ITEM_TYPE_OFFICIAL_CERT":           7,
	"ITEM_TYPE_USER_DECORATION":         8,
	"ITEM_TYPE_MEDAL":                   9,
}

func (x RechargeActAwardInfo_ItemType) String() string {
	return proto.EnumName(RechargeActAwardInfo_ItemType_name, int32(x))
}
func (RechargeActAwardInfo_ItemType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{3, 0}
}

// 获取用户首充活动状态
type GetUserFirstRechargeActStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WithActBanner        bool     `protobuf:"varint,2,opt,name=with_act_banner,json=withActBanner,proto3" json:"with_act_banner,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFirstRechargeActStatusReq) Reset()         { *m = GetUserFirstRechargeActStatusReq{} }
func (m *GetUserFirstRechargeActStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFirstRechargeActStatusReq) ProtoMessage()    {}
func (*GetUserFirstRechargeActStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{0}
}
func (m *GetUserFirstRechargeActStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFirstRechargeActStatusReq.Unmarshal(m, b)
}
func (m *GetUserFirstRechargeActStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFirstRechargeActStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetUserFirstRechargeActStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFirstRechargeActStatusReq.Merge(dst, src)
}
func (m *GetUserFirstRechargeActStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFirstRechargeActStatusReq.Size(m)
}
func (m *GetUserFirstRechargeActStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFirstRechargeActStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFirstRechargeActStatusReq proto.InternalMessageInfo

func (m *GetUserFirstRechargeActStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserFirstRechargeActStatusReq) GetWithActBanner() bool {
	if m != nil {
		return m.WithActBanner
	}
	return false
}

type FirstRechargeBannerInfo struct {
	BannerUrl            string   `protobuf:"bytes,1,opt,name=banner_url,json=bannerUrl,proto3" json:"banner_url,omitempty"`
	JumpUrl              string   `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FirstRechargeBannerInfo) Reset()         { *m = FirstRechargeBannerInfo{} }
func (m *FirstRechargeBannerInfo) String() string { return proto.CompactTextString(m) }
func (*FirstRechargeBannerInfo) ProtoMessage()    {}
func (*FirstRechargeBannerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{1}
}
func (m *FirstRechargeBannerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FirstRechargeBannerInfo.Unmarshal(m, b)
}
func (m *FirstRechargeBannerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FirstRechargeBannerInfo.Marshal(b, m, deterministic)
}
func (dst *FirstRechargeBannerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FirstRechargeBannerInfo.Merge(dst, src)
}
func (m *FirstRechargeBannerInfo) XXX_Size() int {
	return xxx_messageInfo_FirstRechargeBannerInfo.Size(m)
}
func (m *FirstRechargeBannerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FirstRechargeBannerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FirstRechargeBannerInfo proto.InternalMessageInfo

func (m *FirstRechargeBannerInfo) GetBannerUrl() string {
	if m != nil {
		return m.BannerUrl
	}
	return ""
}

func (m *FirstRechargeBannerInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type GetUserFirstRechargeActStatusResp struct {
	IsActOpen            bool                     `protobuf:"varint,1,opt,name=is_act_open,json=isActOpen,proto3" json:"is_act_open,omitempty"`
	IsFirstRechargeFin   bool                     `protobuf:"varint,2,opt,name=is_first_recharge_fin,json=isFirstRechargeFin,proto3" json:"is_first_recharge_fin,omitempty"`
	FinishTs             int64                    `protobuf:"varint,3,opt,name=finish_ts,json=finishTs,proto3" json:"finish_ts,omitempty"`
	BannerInfo           *FirstRechargeBannerInfo `protobuf:"bytes,4,opt,name=banner_info,json=bannerInfo,proto3" json:"banner_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetUserFirstRechargeActStatusResp) Reset()         { *m = GetUserFirstRechargeActStatusResp{} }
func (m *GetUserFirstRechargeActStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetUserFirstRechargeActStatusResp) ProtoMessage()    {}
func (*GetUserFirstRechargeActStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{2}
}
func (m *GetUserFirstRechargeActStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFirstRechargeActStatusResp.Unmarshal(m, b)
}
func (m *GetUserFirstRechargeActStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFirstRechargeActStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetUserFirstRechargeActStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFirstRechargeActStatusResp.Merge(dst, src)
}
func (m *GetUserFirstRechargeActStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetUserFirstRechargeActStatusResp.Size(m)
}
func (m *GetUserFirstRechargeActStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFirstRechargeActStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFirstRechargeActStatusResp proto.InternalMessageInfo

func (m *GetUserFirstRechargeActStatusResp) GetIsActOpen() bool {
	if m != nil {
		return m.IsActOpen
	}
	return false
}

func (m *GetUserFirstRechargeActStatusResp) GetIsFirstRechargeFin() bool {
	if m != nil {
		return m.IsFirstRechargeFin
	}
	return false
}

func (m *GetUserFirstRechargeActStatusResp) GetFinishTs() int64 {
	if m != nil {
		return m.FinishTs
	}
	return 0
}

func (m *GetUserFirstRechargeActStatusResp) GetBannerInfo() *FirstRechargeBannerInfo {
	if m != nil {
		return m.BannerInfo
	}
	return nil
}

// 首充奖励信息
type RechargeActAwardInfo struct {
	ItemType             uint32   `protobuf:"varint,1,opt,name=item_type,json=itemType,proto3" json:"item_type,omitempty"`
	AwardId              string   `protobuf:"bytes,2,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	Amount               uint32   `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty"`
	Name                 string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	StaticImg            string   `protobuf:"bytes,5,opt,name=static_img,json=staticImg,proto3" json:"static_img,omitempty"`
	GifUrl               string   `protobuf:"bytes,6,opt,name=gif_url,json=gifUrl,proto3" json:"gif_url,omitempty"`
	GiftTypeDesc         string   `protobuf:"bytes,7,opt,name=gift_type_desc,json=giftTypeDesc,proto3" json:"gift_type_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RechargeActAwardInfo) Reset()         { *m = RechargeActAwardInfo{} }
func (m *RechargeActAwardInfo) String() string { return proto.CompactTextString(m) }
func (*RechargeActAwardInfo) ProtoMessage()    {}
func (*RechargeActAwardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{3}
}
func (m *RechargeActAwardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeActAwardInfo.Unmarshal(m, b)
}
func (m *RechargeActAwardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeActAwardInfo.Marshal(b, m, deterministic)
}
func (dst *RechargeActAwardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeActAwardInfo.Merge(dst, src)
}
func (m *RechargeActAwardInfo) XXX_Size() int {
	return xxx_messageInfo_RechargeActAwardInfo.Size(m)
}
func (m *RechargeActAwardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeActAwardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeActAwardInfo proto.InternalMessageInfo

func (m *RechargeActAwardInfo) GetItemType() uint32 {
	if m != nil {
		return m.ItemType
	}
	return 0
}

func (m *RechargeActAwardInfo) GetAwardId() string {
	if m != nil {
		return m.AwardId
	}
	return ""
}

func (m *RechargeActAwardInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *RechargeActAwardInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *RechargeActAwardInfo) GetStaticImg() string {
	if m != nil {
		return m.StaticImg
	}
	return ""
}

func (m *RechargeActAwardInfo) GetGifUrl() string {
	if m != nil {
		return m.GifUrl
	}
	return ""
}

func (m *RechargeActAwardInfo) GetGiftTypeDesc() string {
	if m != nil {
		return m.GiftTypeDesc
	}
	return ""
}

// 首充档位信息
type RechargeActLevelInfo struct {
	LevelId              uint32                  `protobuf:"varint,1,opt,name=level_id,json=levelId,proto3" json:"level_id,omitempty"`
	Price                float32                 `protobuf:"fixed32,2,opt,name=price,proto3" json:"price,omitempty"`
	SetDescUrl           string                  `protobuf:"bytes,3,opt,name=set_desc_url,json=setDescUrl,proto3" json:"set_desc_url,omitempty"`
	BeanSaved            uint32                  `protobuf:"varint,4,opt,name=bean_saved,json=beanSaved,proto3" json:"bean_saved,omitempty"`
	ProductId            string                  `protobuf:"bytes,5,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	AwardList            []*RechargeActAwardInfo `protobuf:"bytes,6,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *RechargeActLevelInfo) Reset()         { *m = RechargeActLevelInfo{} }
func (m *RechargeActLevelInfo) String() string { return proto.CompactTextString(m) }
func (*RechargeActLevelInfo) ProtoMessage()    {}
func (*RechargeActLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{4}
}
func (m *RechargeActLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RechargeActLevelInfo.Unmarshal(m, b)
}
func (m *RechargeActLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RechargeActLevelInfo.Marshal(b, m, deterministic)
}
func (dst *RechargeActLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RechargeActLevelInfo.Merge(dst, src)
}
func (m *RechargeActLevelInfo) XXX_Size() int {
	return xxx_messageInfo_RechargeActLevelInfo.Size(m)
}
func (m *RechargeActLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RechargeActLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RechargeActLevelInfo proto.InternalMessageInfo

func (m *RechargeActLevelInfo) GetLevelId() uint32 {
	if m != nil {
		return m.LevelId
	}
	return 0
}

func (m *RechargeActLevelInfo) GetPrice() float32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *RechargeActLevelInfo) GetSetDescUrl() string {
	if m != nil {
		return m.SetDescUrl
	}
	return ""
}

func (m *RechargeActLevelInfo) GetBeanSaved() uint32 {
	if m != nil {
		return m.BeanSaved
	}
	return 0
}

func (m *RechargeActLevelInfo) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *RechargeActLevelInfo) GetAwardList() []*RechargeActAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

// 获取首充活动弹窗信息
type GetNewRechargeActPopupInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewRechargeActPopupInfoReq) Reset()         { *m = GetNewRechargeActPopupInfoReq{} }
func (m *GetNewRechargeActPopupInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetNewRechargeActPopupInfoReq) ProtoMessage()    {}
func (*GetNewRechargeActPopupInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{5}
}
func (m *GetNewRechargeActPopupInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRechargeActPopupInfoReq.Unmarshal(m, b)
}
func (m *GetNewRechargeActPopupInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRechargeActPopupInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetNewRechargeActPopupInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRechargeActPopupInfoReq.Merge(dst, src)
}
func (m *GetNewRechargeActPopupInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetNewRechargeActPopupInfoReq.Size(m)
}
func (m *GetNewRechargeActPopupInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRechargeActPopupInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRechargeActPopupInfoReq proto.InternalMessageInfo

func (m *GetNewRechargeActPopupInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetNewRechargeActPopupInfoResp struct {
	InfoList             []*RechargeActLevelInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	RuleUrl              string                  `protobuf:"bytes,2,opt,name=rule_url,json=ruleUrl,proto3" json:"rule_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetNewRechargeActPopupInfoResp) Reset()         { *m = GetNewRechargeActPopupInfoResp{} }
func (m *GetNewRechargeActPopupInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetNewRechargeActPopupInfoResp) ProtoMessage()    {}
func (*GetNewRechargeActPopupInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{6}
}
func (m *GetNewRechargeActPopupInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewRechargeActPopupInfoResp.Unmarshal(m, b)
}
func (m *GetNewRechargeActPopupInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewRechargeActPopupInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetNewRechargeActPopupInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewRechargeActPopupInfoResp.Merge(dst, src)
}
func (m *GetNewRechargeActPopupInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetNewRechargeActPopupInfoResp.Size(m)
}
func (m *GetNewRechargeActPopupInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewRechargeActPopupInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewRechargeActPopupInfoResp proto.InternalMessageInfo

func (m *GetNewRechargeActPopupInfoResp) GetInfoList() []*RechargeActLevelInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetNewRechargeActPopupInfoResp) GetRuleUrl() string {
	if m != nil {
		return m.RuleUrl
	}
	return ""
}

type TestFirstRechargeEventReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RechargePrice        uint32   `protobuf:"varint,2,opt,name=recharge_price,json=rechargePrice,proto3" json:"recharge_price,omitempty"`
	IsReissue            bool     `protobuf:"varint,3,opt,name=is_reissue,json=isReissue,proto3" json:"is_reissue,omitempty"`
	ReissueSecret        string   `protobuf:"bytes,4,opt,name=reissue_secret,json=reissueSecret,proto3" json:"reissue_secret,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestFirstRechargeEventReq) Reset()         { *m = TestFirstRechargeEventReq{} }
func (m *TestFirstRechargeEventReq) String() string { return proto.CompactTextString(m) }
func (*TestFirstRechargeEventReq) ProtoMessage()    {}
func (*TestFirstRechargeEventReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{7}
}
func (m *TestFirstRechargeEventReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestFirstRechargeEventReq.Unmarshal(m, b)
}
func (m *TestFirstRechargeEventReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestFirstRechargeEventReq.Marshal(b, m, deterministic)
}
func (dst *TestFirstRechargeEventReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestFirstRechargeEventReq.Merge(dst, src)
}
func (m *TestFirstRechargeEventReq) XXX_Size() int {
	return xxx_messageInfo_TestFirstRechargeEventReq.Size(m)
}
func (m *TestFirstRechargeEventReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestFirstRechargeEventReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestFirstRechargeEventReq proto.InternalMessageInfo

func (m *TestFirstRechargeEventReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestFirstRechargeEventReq) GetRechargePrice() uint32 {
	if m != nil {
		return m.RechargePrice
	}
	return 0
}

func (m *TestFirstRechargeEventReq) GetIsReissue() bool {
	if m != nil {
		return m.IsReissue
	}
	return false
}

func (m *TestFirstRechargeEventReq) GetReissueSecret() string {
	if m != nil {
		return m.ReissueSecret
	}
	return ""
}

type TestFirstRechargeEventResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestFirstRechargeEventResp) Reset()         { *m = TestFirstRechargeEventResp{} }
func (m *TestFirstRechargeEventResp) String() string { return proto.CompactTextString(m) }
func (*TestFirstRechargeEventResp) ProtoMessage()    {}
func (*TestFirstRechargeEventResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{8}
}
func (m *TestFirstRechargeEventResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestFirstRechargeEventResp.Unmarshal(m, b)
}
func (m *TestFirstRechargeEventResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestFirstRechargeEventResp.Marshal(b, m, deterministic)
}
func (dst *TestFirstRechargeEventResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestFirstRechargeEventResp.Merge(dst, src)
}
func (m *TestFirstRechargeEventResp) XXX_Size() int {
	return xxx_messageInfo_TestFirstRechargeEventResp.Size(m)
}
func (m *TestFirstRechargeEventResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestFirstRechargeEventResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestFirstRechargeEventResp proto.InternalMessageInfo

type ClearUserFirstRechargeStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearUserFirstRechargeStatusReq) Reset()         { *m = ClearUserFirstRechargeStatusReq{} }
func (m *ClearUserFirstRechargeStatusReq) String() string { return proto.CompactTextString(m) }
func (*ClearUserFirstRechargeStatusReq) ProtoMessage()    {}
func (*ClearUserFirstRechargeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{9}
}
func (m *ClearUserFirstRechargeStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearUserFirstRechargeStatusReq.Unmarshal(m, b)
}
func (m *ClearUserFirstRechargeStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearUserFirstRechargeStatusReq.Marshal(b, m, deterministic)
}
func (dst *ClearUserFirstRechargeStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearUserFirstRechargeStatusReq.Merge(dst, src)
}
func (m *ClearUserFirstRechargeStatusReq) XXX_Size() int {
	return xxx_messageInfo_ClearUserFirstRechargeStatusReq.Size(m)
}
func (m *ClearUserFirstRechargeStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearUserFirstRechargeStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ClearUserFirstRechargeStatusReq proto.InternalMessageInfo

func (m *ClearUserFirstRechargeStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ClearUserFirstRechargeStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClearUserFirstRechargeStatusResp) Reset()         { *m = ClearUserFirstRechargeStatusResp{} }
func (m *ClearUserFirstRechargeStatusResp) String() string { return proto.CompactTextString(m) }
func (*ClearUserFirstRechargeStatusResp) ProtoMessage()    {}
func (*ClearUserFirstRechargeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_new_recharge_act_0fa69d42101b0c61, []int{10}
}
func (m *ClearUserFirstRechargeStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClearUserFirstRechargeStatusResp.Unmarshal(m, b)
}
func (m *ClearUserFirstRechargeStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClearUserFirstRechargeStatusResp.Marshal(b, m, deterministic)
}
func (dst *ClearUserFirstRechargeStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClearUserFirstRechargeStatusResp.Merge(dst, src)
}
func (m *ClearUserFirstRechargeStatusResp) XXX_Size() int {
	return xxx_messageInfo_ClearUserFirstRechargeStatusResp.Size(m)
}
func (m *ClearUserFirstRechargeStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ClearUserFirstRechargeStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ClearUserFirstRechargeStatusResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetUserFirstRechargeActStatusReq)(nil), "new_recharge_act.GetUserFirstRechargeActStatusReq")
	proto.RegisterType((*FirstRechargeBannerInfo)(nil), "new_recharge_act.FirstRechargeBannerInfo")
	proto.RegisterType((*GetUserFirstRechargeActStatusResp)(nil), "new_recharge_act.GetUserFirstRechargeActStatusResp")
	proto.RegisterType((*RechargeActAwardInfo)(nil), "new_recharge_act.RechargeActAwardInfo")
	proto.RegisterType((*RechargeActLevelInfo)(nil), "new_recharge_act.RechargeActLevelInfo")
	proto.RegisterType((*GetNewRechargeActPopupInfoReq)(nil), "new_recharge_act.GetNewRechargeActPopupInfoReq")
	proto.RegisterType((*GetNewRechargeActPopupInfoResp)(nil), "new_recharge_act.GetNewRechargeActPopupInfoResp")
	proto.RegisterType((*TestFirstRechargeEventReq)(nil), "new_recharge_act.TestFirstRechargeEventReq")
	proto.RegisterType((*TestFirstRechargeEventResp)(nil), "new_recharge_act.TestFirstRechargeEventResp")
	proto.RegisterType((*ClearUserFirstRechargeStatusReq)(nil), "new_recharge_act.ClearUserFirstRechargeStatusReq")
	proto.RegisterType((*ClearUserFirstRechargeStatusResp)(nil), "new_recharge_act.ClearUserFirstRechargeStatusResp")
	proto.RegisterEnum("new_recharge_act.RechargeActAwardInfo_ItemType", RechargeActAwardInfo_ItemType_name, RechargeActAwardInfo_ItemType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// NewRechargeActClient is the client API for NewRechargeAct service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type NewRechargeActClient interface {
	// 获取用户首充活动状态
	GetUserFirstRechargeActStatusInfo(ctx context.Context, in *GetUserFirstRechargeActStatusReq, opts ...grpc.CallOption) (*GetUserFirstRechargeActStatusResp, error)
	// 获取首充活动弹窗信息
	GetNewRechargeActPopupInfo(ctx context.Context, in *GetNewRechargeActPopupInfoReq, opts ...grpc.CallOption) (*GetNewRechargeActPopupInfoResp, error)
	// 模拟首充事件（测试）
	TestFirstRechargeEvent(ctx context.Context, in *TestFirstRechargeEventReq, opts ...grpc.CallOption) (*TestFirstRechargeEventResp, error)
	// 清除用户首充任务状态（测试）
	ClearUserFirstRechargeStatus(ctx context.Context, in *ClearUserFirstRechargeStatusReq, opts ...grpc.CallOption) (*ClearUserFirstRechargeStatusResp, error)
	// ======================== 对账接口 ===============================
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
}

type newRechargeActClient struct {
	cc *grpc.ClientConn
}

func NewNewRechargeActClient(cc *grpc.ClientConn) NewRechargeActClient {
	return &newRechargeActClient{cc}
}

func (c *newRechargeActClient) GetUserFirstRechargeActStatusInfo(ctx context.Context, in *GetUserFirstRechargeActStatusReq, opts ...grpc.CallOption) (*GetUserFirstRechargeActStatusResp, error) {
	out := new(GetUserFirstRechargeActStatusResp)
	err := c.cc.Invoke(ctx, "/new_recharge_act.NewRechargeAct/GetUserFirstRechargeActStatusInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newRechargeActClient) GetNewRechargeActPopupInfo(ctx context.Context, in *GetNewRechargeActPopupInfoReq, opts ...grpc.CallOption) (*GetNewRechargeActPopupInfoResp, error) {
	out := new(GetNewRechargeActPopupInfoResp)
	err := c.cc.Invoke(ctx, "/new_recharge_act.NewRechargeAct/GetNewRechargeActPopupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newRechargeActClient) TestFirstRechargeEvent(ctx context.Context, in *TestFirstRechargeEventReq, opts ...grpc.CallOption) (*TestFirstRechargeEventResp, error) {
	out := new(TestFirstRechargeEventResp)
	err := c.cc.Invoke(ctx, "/new_recharge_act.NewRechargeAct/TestFirstRechargeEvent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newRechargeActClient) ClearUserFirstRechargeStatus(ctx context.Context, in *ClearUserFirstRechargeStatusReq, opts ...grpc.CallOption) (*ClearUserFirstRechargeStatusResp, error) {
	out := new(ClearUserFirstRechargeStatusResp)
	err := c.cc.Invoke(ctx, "/new_recharge_act.NewRechargeAct/ClearUserFirstRechargeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newRechargeActClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/new_recharge_act.NewRechargeAct/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *newRechargeActClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/new_recharge_act.NewRechargeAct/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NewRechargeActServer is the server API for NewRechargeAct service.
type NewRechargeActServer interface {
	// 获取用户首充活动状态
	GetUserFirstRechargeActStatusInfo(context.Context, *GetUserFirstRechargeActStatusReq) (*GetUserFirstRechargeActStatusResp, error)
	// 获取首充活动弹窗信息
	GetNewRechargeActPopupInfo(context.Context, *GetNewRechargeActPopupInfoReq) (*GetNewRechargeActPopupInfoResp, error)
	// 模拟首充事件（测试）
	TestFirstRechargeEvent(context.Context, *TestFirstRechargeEventReq) (*TestFirstRechargeEventResp, error)
	// 清除用户首充任务状态（测试）
	ClearUserFirstRechargeStatus(context.Context, *ClearUserFirstRechargeStatusReq) (*ClearUserFirstRechargeStatusResp, error)
	// ======================== 对账接口 ===============================
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
}

func RegisterNewRechargeActServer(s *grpc.Server, srv NewRechargeActServer) {
	s.RegisterService(&_NewRechargeAct_serviceDesc, srv)
}

func _NewRechargeAct_GetUserFirstRechargeActStatusInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFirstRechargeActStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewRechargeActServer).GetUserFirstRechargeActStatusInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/new_recharge_act.NewRechargeAct/GetUserFirstRechargeActStatusInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewRechargeActServer).GetUserFirstRechargeActStatusInfo(ctx, req.(*GetUserFirstRechargeActStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewRechargeAct_GetNewRechargeActPopupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewRechargeActPopupInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewRechargeActServer).GetNewRechargeActPopupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/new_recharge_act.NewRechargeAct/GetNewRechargeActPopupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewRechargeActServer).GetNewRechargeActPopupInfo(ctx, req.(*GetNewRechargeActPopupInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewRechargeAct_TestFirstRechargeEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestFirstRechargeEventReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewRechargeActServer).TestFirstRechargeEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/new_recharge_act.NewRechargeAct/TestFirstRechargeEvent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewRechargeActServer).TestFirstRechargeEvent(ctx, req.(*TestFirstRechargeEventReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewRechargeAct_ClearUserFirstRechargeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearUserFirstRechargeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewRechargeActServer).ClearUserFirstRechargeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/new_recharge_act.NewRechargeAct/ClearUserFirstRechargeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewRechargeActServer).ClearUserFirstRechargeStatus(ctx, req.(*ClearUserFirstRechargeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewRechargeAct_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewRechargeActServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/new_recharge_act.NewRechargeAct/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewRechargeActServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _NewRechargeAct_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NewRechargeActServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/new_recharge_act.NewRechargeAct/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NewRechargeActServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _NewRechargeAct_serviceDesc = grpc.ServiceDesc{
	ServiceName: "new_recharge_act.NewRechargeAct",
	HandlerType: (*NewRechargeActServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserFirstRechargeActStatusInfo",
			Handler:    _NewRechargeAct_GetUserFirstRechargeActStatusInfo_Handler,
		},
		{
			MethodName: "GetNewRechargeActPopupInfo",
			Handler:    _NewRechargeAct_GetNewRechargeActPopupInfo_Handler,
		},
		{
			MethodName: "TestFirstRechargeEvent",
			Handler:    _NewRechargeAct_TestFirstRechargeEvent_Handler,
		},
		{
			MethodName: "ClearUserFirstRechargeStatus",
			Handler:    _NewRechargeAct_ClearUserFirstRechargeStatus_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _NewRechargeAct_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _NewRechargeAct_GetAwardOrderIds_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "new-recharge-act/new-recharge-act.proto",
}

func init() {
	proto.RegisterFile("new-recharge-act/new-recharge-act.proto", fileDescriptor_new_recharge_act_0fa69d42101b0c61)
}

var fileDescriptor_new_recharge_act_0fa69d42101b0c61 = []byte{
	// 1079 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0xcf, 0x52, 0xdb, 0xd6,
	0x17, 0x8e, 0x70, 0x30, 0xf6, 0x21, 0x4e, 0x34, 0x37, 0x09, 0x18, 0x13, 0x82, 0xa3, 0xf9, 0x25,
	0x3f, 0x3a, 0x2d, 0x76, 0x31, 0xed, 0x03, 0x08, 0x5b, 0x80, 0x32, 0xc6, 0x66, 0xae, 0x45, 0x33,
	0xed, 0x74, 0x46, 0xa3, 0xc8, 0xc7, 0xe6, 0x76, 0x64, 0x49, 0xd5, 0xbd, 0x86, 0x66, 0xd7, 0xae,
	0xba, 0xeb, 0xaa, 0xab, 0xbe, 0x48, 0x5f, 0xa2, 0xeb, 0x6e, 0xfb, 0x06, 0xed, 0xbe, 0xab, 0xce,
	0xbd, 0x92, 0x31, 0xc6, 0xfc, 0x09, 0x2b, 0xeb, 0x7c, 0xe7, 0xcf, 0xfd, 0x74, 0xbe, 0x73, 0x8f,
	0x05, 0xff, 0x0f, 0xf1, 0x7c, 0x3b, 0x41, 0xff, 0xd4, 0x4b, 0x86, 0xb8, 0xed, 0xf9, 0xa2, 0x7e,
	0x15, 0xa8, 0xc5, 0x49, 0x24, 0x22, 0xa2, 0x87, 0x78, 0xee, 0x4e, 0x70, 0xd7, 0xf3, 0x45, 0x65,
	0x13, 0x7f, 0x10, 0x18, 0x72, 0x16, 0x85, 0xf5, 0x28, 0x16, 0x2c, 0x0a, 0xf9, 0xe4, 0x37, 0x4d,
	0xa9, 0x6c, 0x26, 0xe8, 0x47, 0xa1, 0xcf, 0x02, 0xdc, 0x3e, 0x6b, 0xd4, 0x2f, 0x1b, 0x69, 0x80,
	0xf1, 0x2d, 0x54, 0x0f, 0x50, 0x9c, 0x70, 0x4c, 0xf6, 0x59, 0xc2, 0x05, 0xcd, 0xaa, 0x9b, 0xbe,
	0xe8, 0x09, 0x4f, 0x8c, 0x39, 0xc5, 0xef, 0x89, 0x0e, 0xb9, 0x31, 0xeb, 0x97, 0xb5, 0xaa, 0xb6,
	0x55, 0xa2, 0xf2, 0x91, 0xbc, 0x81, 0x27, 0xe7, 0x4c, 0x9c, 0x4a, 0x0e, 0xee, 0x7b, 0x2f, 0x0c,
	0x31, 0x29, 0x2f, 0x54, 0xb5, 0xad, 0x02, 0x2d, 0x49, 0xd8, 0xf4, 0xc5, 0x9e, 0x02, 0x8d, 0x1e,
	0xac, 0xce, 0x94, 0x4d, 0x61, 0x3b, 0x1c, 0x44, 0x64, 0x03, 0x20, 0xcd, 0x74, 0xc7, 0x49, 0xa0,
	0x6a, 0x17, 0x69, 0x31, 0x45, 0x4e, 0x92, 0x80, 0xac, 0x41, 0xe1, 0xbb, 0xf1, 0x28, 0x56, 0xce,
	0x05, 0xe5, 0x5c, 0x92, 0xf6, 0x49, 0x12, 0x18, 0x7f, 0x69, 0xf0, 0xea, 0x0e, 0xce, 0x3c, 0x26,
	0x2f, 0x61, 0x99, 0x71, 0x45, 0x30, 0x8a, 0x31, 0x54, 0x07, 0x14, 0x68, 0x91, 0x71, 0xd3, 0x17,
	0xdd, 0x18, 0x43, 0xb2, 0x03, 0xcf, 0x19, 0x77, 0x07, 0xb2, 0xc0, 0xb4, 0xa7, 0x03, 0x16, 0x66,
	0x2f, 0x42, 0x18, 0x9f, 0x29, 0xbe, 0xcf, 0x42, 0xb2, 0x0e, 0xc5, 0x01, 0x0b, 0x19, 0x3f, 0x75,
	0x05, 0x2f, 0xe7, 0xaa, 0xda, 0x56, 0x8e, 0x16, 0x52, 0xc0, 0xe1, 0xe4, 0x2d, 0x2c, 0x67, 0xef,
	0xc3, 0xc2, 0x41, 0x54, 0x7e, 0x58, 0xd5, 0xb6, 0x96, 0x1b, 0x9f, 0xd4, 0xae, 0x4a, 0x56, 0xbb,
	0xa1, 0x1f, 0x34, 0xeb, 0x86, 0x7c, 0x36, 0xfe, 0xc9, 0xc1, 0xb3, 0x4b, 0x6f, 0x65, 0x9e, 0x7b,
	0x49, 0x5f, 0x35, 0x6d, 0x1d, 0x8a, 0x4c, 0xe0, 0xc8, 0x15, 0x1f, 0x62, 0xcc, 0xf4, 0x28, 0x48,
	0xc0, 0xf9, 0x10, 0xa3, 0x6c, 0x99, 0x27, 0x23, 0x5d, 0xd6, 0x9f, 0xb4, 0x4c, 0xd9, 0x76, 0x9f,
	0xac, 0x40, 0xde, 0x1b, 0x45, 0xe3, 0x50, 0x28, 0xda, 0x25, 0x9a, 0x59, 0x84, 0xc0, 0xc3, 0xd0,
	0x1b, 0xa1, 0x62, 0x5b, 0xa4, 0xea, 0x59, 0x0a, 0xc3, 0x85, 0x27, 0x98, 0xef, 0xb2, 0xd1, 0xb0,
	0xbc, 0x98, 0x0a, 0x93, 0x22, 0xf6, 0x68, 0x48, 0x56, 0x61, 0x69, 0xc8, 0x06, 0x4a, 0x97, 0xbc,
	0xf2, 0xe5, 0x87, 0x6c, 0x20, 0x15, 0xfb, 0x1f, 0x3c, 0x1e, 0xb2, 0x81, 0x50, 0xdc, 0xdc, 0x3e,
	0x72, 0xbf, 0xbc, 0xa4, 0xfc, 0x8f, 0x24, 0x2a, 0x09, 0xb6, 0x90, 0xfb, 0xc6, 0xaf, 0x0b, 0x50,
	0xb0, 0xa7, 0x8c, 0x9f, 0xdb, 0x8e, 0x75, 0xe4, 0x3a, 0x5f, 0x1f, 0x5b, 0xee, 0x49, 0xa7, 0x77,
	0x6c, 0x35, 0xed, 0x7d, 0xdb, 0x6a, 0xe9, 0x0f, 0xc8, 0x53, 0x78, 0x32, 0x75, 0x39, 0x7b, 0x96,
	0xd9, 0xd1, 0x35, 0x52, 0x81, 0x95, 0x29, 0x48, 0xad, 0x96, 0xdb, 0xb2, 0xcd, 0xa3, 0x6e, 0xa7,
	0xd5, 0xd2, 0x17, 0xc8, 0x6b, 0x78, 0x35, 0xf5, 0x1d, 0x74, 0xdb, 0x17, 0x4e, 0xf7, 0x9d, 0xed,
	0x1c, 0xba, 0x7b, 0xdd, 0x93, 0x4e, 0x4f, 0xcf, 0x91, 0x15, 0x20, 0xd3, 0xb0, 0x43, 0xcb, 0x6c,
	0xbd, 0xb3, 0x4c, 0xaa, 0x3f, 0x24, 0xab, 0xf0, 0x74, 0x8a, 0x77, 0xcc, 0x23, 0xeb, 0xb8, 0x6d,
	0x3a, 0x96, 0xbe, 0x38, 0x4b, 0xe4, 0xb0, 0x4b, 0x7b, 0x96, 0x9e, 0x27, 0xeb, 0xb0, 0x3a, 0x05,
	0xbb, 0xfb, 0xfb, 0x76, 0xd3, 0x36, 0xdb, 0x6e, 0xd3, 0xa2, 0x8e, 0xbe, 0x44, 0x36, 0x60, 0xed,
	0xd2, 0x5b, 0xf5, 0x2c, 0xea, 0xb6, 0xac, 0x66, 0x97, 0x9a, 0x8e, 0xdd, 0xed, 0xe8, 0x85, 0xd9,
	0x82, 0x47, 0x56, 0xcb, 0x6c, 0xeb, 0x45, 0xe3, 0x6f, 0x6d, 0x46, 0xf1, 0x36, 0x9e, 0x61, 0xa0,
	0x14, 0x5f, 0x83, 0x42, 0x20, 0x0d, 0xf7, 0xe2, 0x02, 0x2e, 0x29, 0xdb, 0xee, 0x93, 0x67, 0xb0,
	0x18, 0x27, 0xcc, 0x47, 0x25, 0xf6, 0x02, 0x4d, 0x0d, 0x52, 0x85, 0x47, 0x1c, 0x85, 0x12, 0x40,
	0x89, 0x94, 0x53, 0x22, 0x00, 0x47, 0x21, 0xfb, 0x2f, 0x85, 0x92, 0x37, 0x0f, 0xbd, 0xd0, 0xe5,
	0xde, 0x19, 0xf6, 0x95, 0xf4, 0x25, 0x5a, 0x94, 0x48, 0x4f, 0x02, 0xd2, 0x1d, 0x27, 0x51, 0x7f,
	0xec, 0x0b, 0x79, 0x66, 0xa6, 0x7f, 0x86, 0xd8, 0x7d, 0x62, 0x01, 0xa4, 0x53, 0x16, 0x30, 0x2e,
	0xca, 0xf9, 0x6a, 0x6e, 0x6b, 0xb9, 0xf1, 0x66, 0x7e, 0xcc, 0xaf, 0x1b, 0x5f, 0x5a, 0x54, 0x99,
	0x6d, 0xc6, 0x85, 0xb1, 0x03, 0x1b, 0x07, 0x28, 0x3a, 0x78, 0x7e, 0x29, 0xf0, 0x38, 0x8a, 0xc7,
	0xb1, 0x0a, 0xbc, 0x6e, 0xe9, 0x18, 0x3f, 0x6a, 0xf0, 0xf2, 0xb6, 0x1c, 0x1e, 0x93, 0x26, 0x14,
	0xe5, 0xed, 0x4b, 0xb9, 0x69, 0x1f, 0xc1, 0xed, 0xa2, 0xd1, 0xb4, 0x20, 0x13, 0x25, 0x35, 0xd9,
	0xf2, 0x64, 0x1c, 0xe0, 0xe5, 0xd5, 0x23, 0x6d, 0xb9, 0x7a, 0x7e, 0xd3, 0x60, 0xcd, 0x41, 0x2e,
	0x66, 0x2e, 0xb1, 0x75, 0x86, 0xa1, 0xb8, 0x7e, 0x4f, 0xbe, 0x86, 0xc7, 0x17, 0x27, 0x4f, 0xb5,
	0x2a, 0xd1, 0xd2, 0x04, 0x3d, 0x56, 0x9a, 0x6d, 0x00, 0x30, 0xee, 0x26, 0xc8, 0x38, 0x1f, 0xa3,
	0x52, 0x4c, 0xad, 0x2a, 0x9a, 0x02, 0x69, 0x15, 0xf5, 0xe8, 0x72, 0xf4, 0x13, 0x14, 0xd9, 0x7d,
	0x2d, 0x65, 0x68, 0x4f, 0x81, 0xc6, 0x0b, 0xa8, 0xdc, 0xc4, 0x8d, 0xc7, 0xc6, 0x2e, 0x6c, 0x36,
	0x03, 0xf4, 0x92, 0xb9, 0xb5, 0x79, 0xcb, 0x9e, 0x37, 0x0c, 0xa8, 0xde, 0x9e, 0xc4, 0xe3, 0xc6,
	0x9f, 0x8b, 0xf0, 0x78, 0x56, 0x13, 0xf2, 0xcb, 0x5d, 0x1b, 0x5a, 0x8d, 0x76, 0x63, 0x5e, 0x99,
	0xbb, 0xfe, 0x8a, 0x2a, 0xbb, 0xf7, 0xce, 0xe1, 0xb1, 0xf1, 0x80, 0xfc, 0xa4, 0x41, 0xe5, 0xe6,
	0xd1, 0x21, 0xf5, 0x6b, 0xab, 0xde, 0x3c, 0x9c, 0x95, 0xcf, 0xef, 0x97, 0xa0, 0x38, 0x8c, 0x61,
	0xe5, 0x7a, 0x79, 0xc8, 0xa7, 0xf3, 0xd5, 0x6e, 0x1c, 0xb2, 0xca, 0x67, 0x1f, 0x1f, 0xac, 0x8e,
	0xfd, 0x59, 0x83, 0x17, 0xb7, 0x69, 0x48, 0x76, 0xe6, 0x0b, 0xde, 0x31, 0x28, 0x95, 0xc6, 0x7d,
	0x53, 0x14, 0x13, 0x1b, 0xc8, 0x01, 0xa6, 0xdb, 0xc0, 0x89, 0x84, 0x17, 0x34, 0xd5, 0x5f, 0xd0,
	0x9a, 0xbc, 0x8e, 0xe9, 0x57, 0xc9, 0x57, 0x8d, 0x9a, 0xc3, 0x46, 0x48, 0xbd, 0x70, 0x88, 0xf2,
	0x98, 0x95, 0x19, 0x97, 0x0a, 0xcf, 0x4a, 0xbd, 0x05, 0x7d, 0x52, 0xaa, 0x9b, 0xf4, 0x31, 0xb1,
	0xfb, 0xfc, 0xb6, 0x42, 0xb3, 0xae, 0x49, 0x46, 0x5a, 0xab, 0xb2, 0xfa, 0xef, 0xef, 0x7f, 0x38,
	0x04, 0xf4, 0xab, 0x1f, 0x5d, 0x7b, 0x5f, 0x7c, 0xd3, 0x18, 0x46, 0x81, 0x17, 0x0e, 0x6b, 0x5f,
	0x36, 0x84, 0xa8, 0xf9, 0xd1, 0xa8, 0xae, 0xbe, 0x99, 0xfc, 0x28, 0xa8, 0x73, 0x4c, 0xce, 0x98,
	0x8f, 0x7c, 0xee, 0x53, 0xed, 0x7d, 0x5e, 0xc5, 0xec, 0xfe, 0x17, 0x00, 0x00, 0xff, 0xff, 0xb3,
	0x29, 0xf4, 0x88, 0xd6, 0x09, 0x00, 0x00,
}
