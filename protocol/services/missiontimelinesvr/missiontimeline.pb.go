// Code generated by protoc-gen-gogo.
// source: src/missiontimelinesvr/missiontimeline.proto
// DO NOT EDIT!

/*
Package Mission is a generated protocol buffer package.

namespace

It is generated from these files:

	src/missiontimelinesvr/missiontimeline.proto

It has these top-level messages:

	MissionTimelineMsg
	GrowInfoTimelineMsg
	MissionFinishMessage
	GrowInfoMessage
	GuildMemContInfoMessage
	MissionGuideMessage
	NumericInfoMessage
	UserScoreMessage
	WriteTimelineMsgReq
	WriteTimelineMsgResp
	UpdateGrowInfoReq
	UpdateGrowInfoResp
	GetGrowInfoReq
	GetGrowInfoResp
	BatchDeleteTimelineReq
	BatchDeleteTimelineResp
	PullTimelineMsgReq
	PullTimelineMsgResp
	MarkReadedReq
	MarkReadedResp
	GetReadedReq
	GetReadedResp
*/
package Mission

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type MissionTimelineMsg_TYPE int32

const (
	MissionTimelineMsg_MISSION_FINISH_MSG        MissionTimelineMsg_TYPE = 1
	MissionTimelineMsg_EXP_CURRENCY_CHANGED_MSG  MissionTimelineMsg_TYPE = 2
	MissionTimelineMsg_MISSION_GUIDE_MSG         MissionTimelineMsg_TYPE = 3
	MissionTimelineMsg_GUILD_MEMBER_CONTRIBUTION MissionTimelineMsg_TYPE = 4
	MissionTimelineMsg_USER_SOCRE_CHANGED        MissionTimelineMsg_TYPE = 5
	MissionTimelineMsg_USER_CHARM_RICH_CHANGED   MissionTimelineMsg_TYPE = 6
)

var MissionTimelineMsg_TYPE_name = map[int32]string{
	1: "MISSION_FINISH_MSG",
	2: "EXP_CURRENCY_CHANGED_MSG",
	3: "MISSION_GUIDE_MSG",
	4: "GUILD_MEMBER_CONTRIBUTION",
	5: "USER_SOCRE_CHANGED",
	6: "USER_CHARM_RICH_CHANGED",
}
var MissionTimelineMsg_TYPE_value = map[string]int32{
	"MISSION_FINISH_MSG":        1,
	"EXP_CURRENCY_CHANGED_MSG":  2,
	"MISSION_GUIDE_MSG":         3,
	"GUILD_MEMBER_CONTRIBUTION": 4,
	"USER_SOCRE_CHANGED":        5,
	"USER_CHARM_RICH_CHANGED":   6,
}

func (x MissionTimelineMsg_TYPE) Enum() *MissionTimelineMsg_TYPE {
	p := new(MissionTimelineMsg_TYPE)
	*p = x
	return p
}
func (x MissionTimelineMsg_TYPE) String() string {
	return proto.EnumName(MissionTimelineMsg_TYPE_name, int32(x))
}
func (x *MissionTimelineMsg_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MissionTimelineMsg_TYPE_value, data, "MissionTimelineMsg_TYPE")
	if err != nil {
		return err
	}
	*x = MissionTimelineMsg_TYPE(value)
	return nil
}
func (MissionTimelineMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{0, 0}
}

type GrowInfoTimelineMsg_TYPE int32

const (
	GrowInfoTimelineMsg_EXP_CURRENCY_CHANGED_MSG GrowInfoTimelineMsg_TYPE = 1
	GrowInfoTimelineMsg_USER_SOCRE_CHANGED       GrowInfoTimelineMsg_TYPE = 2
	GrowInfoTimelineMsg_USER_CHARM_RICH_CHANGED  GrowInfoTimelineMsg_TYPE = 3
)

var GrowInfoTimelineMsg_TYPE_name = map[int32]string{
	1: "EXP_CURRENCY_CHANGED_MSG",
	2: "USER_SOCRE_CHANGED",
	3: "USER_CHARM_RICH_CHANGED",
}
var GrowInfoTimelineMsg_TYPE_value = map[string]int32{
	"EXP_CURRENCY_CHANGED_MSG": 1,
	"USER_SOCRE_CHANGED":       2,
	"USER_CHARM_RICH_CHANGED":  3,
}

func (x GrowInfoTimelineMsg_TYPE) Enum() *GrowInfoTimelineMsg_TYPE {
	p := new(GrowInfoTimelineMsg_TYPE)
	*p = x
	return p
}
func (x GrowInfoTimelineMsg_TYPE) String() string {
	return proto.EnumName(GrowInfoTimelineMsg_TYPE_name, int32(x))
}
func (x *GrowInfoTimelineMsg_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GrowInfoTimelineMsg_TYPE_value, data, "GrowInfoTimelineMsg_TYPE")
	if err != nil {
		return err
	}
	*x = GrowInfoTimelineMsg_TYPE(value)
	return nil
}
func (GrowInfoTimelineMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{1, 0}
}

type MissionFinishMessage_NOTIFY_MASK int32

const (
	MissionFinishMessage_NONE      MissionFinishMessage_NOTIFY_MASK = 0
	MissionFinishMessage_RED_POINT MissionFinishMessage_NOTIFY_MASK = 1
	MissionFinishMessage_ALTER     MissionFinishMessage_NOTIFY_MASK = 2
)

var MissionFinishMessage_NOTIFY_MASK_name = map[int32]string{
	0: "NONE",
	1: "RED_POINT",
	2: "ALTER",
}
var MissionFinishMessage_NOTIFY_MASK_value = map[string]int32{
	"NONE":      0,
	"RED_POINT": 1,
	"ALTER":     2,
}

func (x MissionFinishMessage_NOTIFY_MASK) Enum() *MissionFinishMessage_NOTIFY_MASK {
	p := new(MissionFinishMessage_NOTIFY_MASK)
	*p = x
	return p
}
func (x MissionFinishMessage_NOTIFY_MASK) String() string {
	return proto.EnumName(MissionFinishMessage_NOTIFY_MASK_name, int32(x))
}
func (x *MissionFinishMessage_NOTIFY_MASK) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MissionFinishMessage_NOTIFY_MASK_value, data, "MissionFinishMessage_NOTIFY_MASK")
	if err != nil {
		return err
	}
	*x = MissionFinishMessage_NOTIFY_MASK(value)
	return nil
}
func (MissionFinishMessage_NOTIFY_MASK) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{2, 0}
}

// ------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
// ------------------------------------------
type MissionTimelineMsg struct {
	Type   uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Seqid  uint32 `protobuf:"varint,2,req,name=seqid" json:"seqid"`
	MsgBin []byte `protobuf:"bytes,3,req,name=msg_bin,json=msgBin" json:"msg_bin"`
}

func (m *MissionTimelineMsg) Reset()         { *m = MissionTimelineMsg{} }
func (m *MissionTimelineMsg) String() string { return proto.CompactTextString(m) }
func (*MissionTimelineMsg) ProtoMessage()    {}
func (*MissionTimelineMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{0}
}

func (m *MissionTimelineMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MissionTimelineMsg) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *MissionTimelineMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

type GrowInfoTimelineMsg struct {
	Seqid  uint32 `protobuf:"varint,1,req,name=seqid" json:"seqid"`
	Type   uint32 `protobuf:"varint,2,req,name=type" json:"type"`
	MsgBin []byte `protobuf:"bytes,3,req,name=msg_bin,json=msgBin" json:"msg_bin"`
}

func (m *GrowInfoTimelineMsg) Reset()         { *m = GrowInfoTimelineMsg{} }
func (m *GrowInfoTimelineMsg) String() string { return proto.CompactTextString(m) }
func (*GrowInfoTimelineMsg) ProtoMessage()    {}
func (*GrowInfoTimelineMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{1}
}

func (m *GrowInfoTimelineMsg) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *GrowInfoTimelineMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GrowInfoTimelineMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

type MissionFinishMessage struct {
	MissionKey string `protobuf:"bytes,1,req,name=mission_key,json=missionKey" json:"mission_key"`
	Timestamp  uint32 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
	NotifyMask uint32 `protobuf:"varint,3,req,name=notify_mask,json=notifyMask" json:"notify_mask"`
	NewGuide   string `protobuf:"bytes,4,opt,name=new_guide,json=newGuide" json:"new_guide"`
}

func (m *MissionFinishMessage) Reset()         { *m = MissionFinishMessage{} }
func (m *MissionFinishMessage) String() string { return proto.CompactTextString(m) }
func (*MissionFinishMessage) ProtoMessage()    {}
func (*MissionFinishMessage) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{2}
}

func (m *MissionFinishMessage) GetMissionKey() string {
	if m != nil {
		return m.MissionKey
	}
	return ""
}

func (m *MissionFinishMessage) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *MissionFinishMessage) GetNotifyMask() uint32 {
	if m != nil {
		return m.NotifyMask
	}
	return 0
}

func (m *MissionFinishMessage) GetNewGuide() string {
	if m != nil {
		return m.NewGuide
	}
	return ""
}

type GrowInfoMessage struct {
	Exp                int32  `protobuf:"varint,1,req,name=exp" json:"exp"`
	Level              uint32 `protobuf:"varint,2,req,name=level" json:"level"`
	CurrentLevelExpMin uint32 `protobuf:"varint,3,req,name=current_level_exp_min,json=currentLevelExpMin" json:"current_level_exp_min"`
	CurrentLevelExpMax uint32 `protobuf:"varint,4,req,name=current_level_exp_max,json=currentLevelExpMax" json:"current_level_exp_max"`
	Currency           int32  `protobuf:"varint,5,req,name=currency" json:"currency"`
}

func (m *GrowInfoMessage) Reset()                    { *m = GrowInfoMessage{} }
func (m *GrowInfoMessage) String() string            { return proto.CompactTextString(m) }
func (*GrowInfoMessage) ProtoMessage()               {}
func (*GrowInfoMessage) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{3} }

func (m *GrowInfoMessage) GetExp() int32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func (m *GrowInfoMessage) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GrowInfoMessage) GetCurrentLevelExpMin() uint32 {
	if m != nil {
		return m.CurrentLevelExpMin
	}
	return 0
}

func (m *GrowInfoMessage) GetCurrentLevelExpMax() uint32 {
	if m != nil {
		return m.CurrentLevelExpMax
	}
	return 0
}

func (m *GrowInfoMessage) GetCurrency() int32 {
	if m != nil {
		return m.Currency
	}
	return 0
}

type GuildMemContInfoMessage struct {
	GuildId           uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TotalContribution uint32 `protobuf:"varint,2,req,name=total_contribution,json=totalContribution" json:"total_contribution"`
	ValidContribution uint32 `protobuf:"varint,3,req,name=valid_contribution,json=validContribution" json:"valid_contribution"`
	MemberLv          uint32 `protobuf:"varint,4,req,name=member_lv,json=memberLv" json:"member_lv"`
}

func (m *GuildMemContInfoMessage) Reset()         { *m = GuildMemContInfoMessage{} }
func (m *GuildMemContInfoMessage) String() string { return proto.CompactTextString(m) }
func (*GuildMemContInfoMessage) ProtoMessage()    {}
func (*GuildMemContInfoMessage) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{4}
}

func (m *GuildMemContInfoMessage) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildMemContInfoMessage) GetTotalContribution() uint32 {
	if m != nil {
		return m.TotalContribution
	}
	return 0
}

func (m *GuildMemContInfoMessage) GetValidContribution() uint32 {
	if m != nil {
		return m.ValidContribution
	}
	return 0
}

func (m *GuildMemContInfoMessage) GetMemberLv() uint32 {
	if m != nil {
		return m.MemberLv
	}
	return 0
}

type MissionGuideMessage struct {
	Guide string `protobuf:"bytes,1,req,name=guide" json:"guide"`
}

func (m *MissionGuideMessage) Reset()         { *m = MissionGuideMessage{} }
func (m *MissionGuideMessage) String() string { return proto.CompactTextString(m) }
func (*MissionGuideMessage) ProtoMessage()    {}
func (*MissionGuideMessage) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{5}
}

func (m *MissionGuideMessage) GetGuide() string {
	if m != nil {
		return m.Guide
	}
	return ""
}

type NumericInfoMessage struct {
	Charm   uint32 `protobuf:"varint,1,req,name=charm" json:"charm"`
	Rich    uint32 `protobuf:"varint,2,req,name=rich" json:"rich"`
	Charm64 uint64 `protobuf:"varint,3,opt,name=charm64" json:"charm64"`
	Rich64  uint64 `protobuf:"varint,4,opt,name=rich64" json:"rich64"`
}

func (m *NumericInfoMessage) Reset()         { *m = NumericInfoMessage{} }
func (m *NumericInfoMessage) String() string { return proto.CompactTextString(m) }
func (*NumericInfoMessage) ProtoMessage()    {}
func (*NumericInfoMessage) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{6}
}

func (m *NumericInfoMessage) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *NumericInfoMessage) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *NumericInfoMessage) GetCharm64() uint64 {
	if m != nil {
		return m.Charm64
	}
	return 0
}

func (m *NumericInfoMessage) GetRich64() uint64 {
	if m != nil {
		return m.Rich64
	}
	return 0
}

type UserScoreMessage struct {
	Score uint32 `protobuf:"varint,1,req,name=score" json:"score"`
}

func (m *UserScoreMessage) Reset()                    { *m = UserScoreMessage{} }
func (m *UserScoreMessage) String() string            { return proto.CompactTextString(m) }
func (*UserScoreMessage) ProtoMessage()               {}
func (*UserScoreMessage) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{7} }

func (m *UserScoreMessage) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// ------------------------------------------
// 读写协议
// ------------------------------------------
type WriteTimelineMsgReq struct {
	Id     uint32              `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string              `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	Msg    *MissionTimelineMsg `protobuf:"bytes,3,req,name=msg" json:"msg,omitempty"`
}

func (m *WriteTimelineMsgReq) Reset()         { *m = WriteTimelineMsgReq{} }
func (m *WriteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgReq) ProtoMessage()    {}
func (*WriteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{8}
}

func (m *WriteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WriteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *WriteTimelineMsgReq) GetMsg() *MissionTimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type WriteTimelineMsgResp struct {
}

func (m *WriteTimelineMsgResp) Reset()         { *m = WriteTimelineMsgResp{} }
func (m *WriteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgResp) ProtoMessage()    {}
func (*WriteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{9}
}

type UpdateGrowInfoReq struct {
	Uid uint32               `protobuf:"varint,1,req,name=uid" json:"uid"`
	Msg *GrowInfoTimelineMsg `protobuf:"bytes,2,req,name=msg" json:"msg,omitempty"`
}

func (m *UpdateGrowInfoReq) Reset()         { *m = UpdateGrowInfoReq{} }
func (m *UpdateGrowInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGrowInfoReq) ProtoMessage()    {}
func (*UpdateGrowInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{10}
}

func (m *UpdateGrowInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGrowInfoReq) GetMsg() *GrowInfoTimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type UpdateGrowInfoResp struct {
}

func (m *UpdateGrowInfoResp) Reset()         { *m = UpdateGrowInfoResp{} }
func (m *UpdateGrowInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGrowInfoResp) ProtoMessage()    {}
func (*UpdateGrowInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{11}
}

type GetGrowInfoReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Seqid uint32 `protobuf:"varint,2,req,name=seqid" json:"seqid"`
}

func (m *GetGrowInfoReq) Reset()                    { *m = GetGrowInfoReq{} }
func (m *GetGrowInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetGrowInfoReq) ProtoMessage()               {}
func (*GetGrowInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{12} }

func (m *GetGrowInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGrowInfoReq) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

type GetGrowInfoResp struct {
	Msgs []*GrowInfoTimelineMsg `protobuf:"bytes,1,rep,name=msgs" json:"msgs,omitempty"`
}

func (m *GetGrowInfoResp) Reset()                    { *m = GetGrowInfoResp{} }
func (m *GetGrowInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetGrowInfoResp) ProtoMessage()               {}
func (*GetGrowInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{13} }

func (m *GetGrowInfoResp) GetMsgs() []*GrowInfoTimelineMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

// ------------------------------------------
// 根据seq删除消息
// ------------------------------------------
type BatchDeleteTimelineReq struct {
	Id        uint32   `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix    string   `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	SeqIdList []uint32 `protobuf:"varint,3,rep,name=seq_id_list,json=seqIdList" json:"seq_id_list,omitempty"`
}

func (m *BatchDeleteTimelineReq) Reset()         { *m = BatchDeleteTimelineReq{} }
func (m *BatchDeleteTimelineReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteTimelineReq) ProtoMessage()    {}
func (*BatchDeleteTimelineReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{14}
}

func (m *BatchDeleteTimelineReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BatchDeleteTimelineReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *BatchDeleteTimelineReq) GetSeqIdList() []uint32 {
	if m != nil {
		return m.SeqIdList
	}
	return nil
}

type BatchDeleteTimelineResp struct {
}

func (m *BatchDeleteTimelineResp) Reset()         { *m = BatchDeleteTimelineResp{} }
func (m *BatchDeleteTimelineResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteTimelineResp) ProtoMessage()    {}
func (*BatchDeleteTimelineResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{15}
}

// ------------------------------------------
// 拉取timeline
// ------------------------------------------
type PullTimelineMsgReq struct {
	Id         uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix     string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	StartSeqid uint32 `protobuf:"varint,3,req,name=start_seqid,json=startSeqid" json:"start_seqid"`
	Limit      uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
	IsRev      uint32 `protobuf:"varint,5,opt,name=is_rev,json=isRev" json:"is_rev"`
}

func (m *PullTimelineMsgReq) Reset()         { *m = PullTimelineMsgReq{} }
func (m *PullTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgReq) ProtoMessage()    {}
func (*PullTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{16}
}

func (m *PullTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PullTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *PullTimelineMsgReq) GetStartSeqid() uint32 {
	if m != nil {
		return m.StartSeqid
	}
	return 0
}

func (m *PullTimelineMsgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullTimelineMsgReq) GetIsRev() uint32 {
	if m != nil {
		return m.IsRev
	}
	return 0
}

type PullTimelineMsgResp struct {
	MsgList []*MissionTimelineMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *PullTimelineMsgResp) Reset()         { *m = PullTimelineMsgResp{} }
func (m *PullTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgResp) ProtoMessage()    {}
func (*PullTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMissiontimeline, []int{17}
}

func (m *PullTimelineMsgResp) GetMsgList() []*MissionTimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

// ------------------------------------------
// 标记已读
// ------------------------------------------
type MarkReadedReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	Seqid  uint32 `protobuf:"varint,3,req,name=seqid" json:"seqid"`
}

func (m *MarkReadedReq) Reset()                    { *m = MarkReadedReq{} }
func (m *MarkReadedReq) String() string            { return proto.CompactTextString(m) }
func (*MarkReadedReq) ProtoMessage()               {}
func (*MarkReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{18} }

func (m *MarkReadedReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MarkReadedReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *MarkReadedReq) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

type MarkReadedResp struct {
}

func (m *MarkReadedResp) Reset()                    { *m = MarkReadedResp{} }
func (m *MarkReadedResp) String() string            { return proto.CompactTextString(m) }
func (*MarkReadedResp) ProtoMessage()               {}
func (*MarkReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{19} }

// ------------------------------------------
// 查已读
// ------------------------------------------
type GetReadedReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
}

func (m *GetReadedReq) Reset()                    { *m = GetReadedReq{} }
func (m *GetReadedReq) String() string            { return proto.CompactTextString(m) }
func (*GetReadedReq) ProtoMessage()               {}
func (*GetReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{20} }

func (m *GetReadedReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetReadedReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type GetReadedResp struct {
	Seqid uint32 `protobuf:"varint,1,req,name=seqid" json:"seqid"`
}

func (m *GetReadedResp) Reset()                    { *m = GetReadedResp{} }
func (m *GetReadedResp) String() string            { return proto.CompactTextString(m) }
func (*GetReadedResp) ProtoMessage()               {}
func (*GetReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorMissiontimeline, []int{21} }

func (m *GetReadedResp) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func init() {
	proto.RegisterType((*MissionTimelineMsg)(nil), "Mission.MissionTimelineMsg")
	proto.RegisterType((*GrowInfoTimelineMsg)(nil), "Mission.GrowInfoTimelineMsg")
	proto.RegisterType((*MissionFinishMessage)(nil), "Mission.MissionFinishMessage")
	proto.RegisterType((*GrowInfoMessage)(nil), "Mission.GrowInfoMessage")
	proto.RegisterType((*GuildMemContInfoMessage)(nil), "Mission.GuildMemContInfoMessage")
	proto.RegisterType((*MissionGuideMessage)(nil), "Mission.MissionGuideMessage")
	proto.RegisterType((*NumericInfoMessage)(nil), "Mission.NumericInfoMessage")
	proto.RegisterType((*UserScoreMessage)(nil), "Mission.UserScoreMessage")
	proto.RegisterType((*WriteTimelineMsgReq)(nil), "Mission.WriteTimelineMsgReq")
	proto.RegisterType((*WriteTimelineMsgResp)(nil), "Mission.WriteTimelineMsgResp")
	proto.RegisterType((*UpdateGrowInfoReq)(nil), "Mission.UpdateGrowInfoReq")
	proto.RegisterType((*UpdateGrowInfoResp)(nil), "Mission.UpdateGrowInfoResp")
	proto.RegisterType((*GetGrowInfoReq)(nil), "Mission.GetGrowInfoReq")
	proto.RegisterType((*GetGrowInfoResp)(nil), "Mission.GetGrowInfoResp")
	proto.RegisterType((*BatchDeleteTimelineReq)(nil), "Mission.BatchDeleteTimelineReq")
	proto.RegisterType((*BatchDeleteTimelineResp)(nil), "Mission.BatchDeleteTimelineResp")
	proto.RegisterType((*PullTimelineMsgReq)(nil), "Mission.PullTimelineMsgReq")
	proto.RegisterType((*PullTimelineMsgResp)(nil), "Mission.PullTimelineMsgResp")
	proto.RegisterType((*MarkReadedReq)(nil), "Mission.MarkReadedReq")
	proto.RegisterType((*MarkReadedResp)(nil), "Mission.MarkReadedResp")
	proto.RegisterType((*GetReadedReq)(nil), "Mission.GetReadedReq")
	proto.RegisterType((*GetReadedResp)(nil), "Mission.GetReadedResp")
	proto.RegisterEnum("Mission.MissionTimelineMsg_TYPE", MissionTimelineMsg_TYPE_name, MissionTimelineMsg_TYPE_value)
	proto.RegisterEnum("Mission.GrowInfoTimelineMsg_TYPE", GrowInfoTimelineMsg_TYPE_name, GrowInfoTimelineMsg_TYPE_value)
	proto.RegisterEnum("Mission.MissionFinishMessage_NOTIFY_MASK", MissionFinishMessage_NOTIFY_MASK_name, MissionFinishMessage_NOTIFY_MASK_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for MissionTimeline service

type MissionTimelineClient interface {
	WriteTimelineMsg(ctx context.Context, in *WriteTimelineMsgReq, opts ...grpc.CallOption) (*WriteTimelineMsgResp, error)
	BatchDeleteTimeline(ctx context.Context, in *BatchDeleteTimelineReq, opts ...grpc.CallOption) (*BatchDeleteTimelineResp, error)
	PullTimelineMsg(ctx context.Context, in *PullTimelineMsgReq, opts ...grpc.CallOption) (*PullTimelineMsgResp, error)
	MarkReaded(ctx context.Context, in *MarkReadedReq, opts ...grpc.CallOption) (*MarkReadedResp, error)
	GetReaded(ctx context.Context, in *GetReadedReq, opts ...grpc.CallOption) (*GetReadedResp, error)
	UpdateGrowInfo(ctx context.Context, in *UpdateGrowInfoReq, opts ...grpc.CallOption) (*UpdateGrowInfoResp, error)
	GetGrowInfo(ctx context.Context, in *GetGrowInfoReq, opts ...grpc.CallOption) (*GetGrowInfoResp, error)
}

type missionTimelineClient struct {
	cc *grpc.ClientConn
}

func NewMissionTimelineClient(cc *grpc.ClientConn) MissionTimelineClient {
	return &missionTimelineClient{cc}
}

func (c *missionTimelineClient) WriteTimelineMsg(ctx context.Context, in *WriteTimelineMsgReq, opts ...grpc.CallOption) (*WriteTimelineMsgResp, error) {
	out := new(WriteTimelineMsgResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/WriteTimelineMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionTimelineClient) BatchDeleteTimeline(ctx context.Context, in *BatchDeleteTimelineReq, opts ...grpc.CallOption) (*BatchDeleteTimelineResp, error) {
	out := new(BatchDeleteTimelineResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/BatchDeleteTimeline", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionTimelineClient) PullTimelineMsg(ctx context.Context, in *PullTimelineMsgReq, opts ...grpc.CallOption) (*PullTimelineMsgResp, error) {
	out := new(PullTimelineMsgResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/PullTimelineMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionTimelineClient) MarkReaded(ctx context.Context, in *MarkReadedReq, opts ...grpc.CallOption) (*MarkReadedResp, error) {
	out := new(MarkReadedResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/MarkReaded", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionTimelineClient) GetReaded(ctx context.Context, in *GetReadedReq, opts ...grpc.CallOption) (*GetReadedResp, error) {
	out := new(GetReadedResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/GetReaded", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionTimelineClient) UpdateGrowInfo(ctx context.Context, in *UpdateGrowInfoReq, opts ...grpc.CallOption) (*UpdateGrowInfoResp, error) {
	out := new(UpdateGrowInfoResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/UpdateGrowInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionTimelineClient) GetGrowInfo(ctx context.Context, in *GetGrowInfoReq, opts ...grpc.CallOption) (*GetGrowInfoResp, error) {
	out := new(GetGrowInfoResp)
	err := grpc.Invoke(ctx, "/Mission.MissionTimeline/GetGrowInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for MissionTimeline service

type MissionTimelineServer interface {
	WriteTimelineMsg(context.Context, *WriteTimelineMsgReq) (*WriteTimelineMsgResp, error)
	BatchDeleteTimeline(context.Context, *BatchDeleteTimelineReq) (*BatchDeleteTimelineResp, error)
	PullTimelineMsg(context.Context, *PullTimelineMsgReq) (*PullTimelineMsgResp, error)
	MarkReaded(context.Context, *MarkReadedReq) (*MarkReadedResp, error)
	GetReaded(context.Context, *GetReadedReq) (*GetReadedResp, error)
	UpdateGrowInfo(context.Context, *UpdateGrowInfoReq) (*UpdateGrowInfoResp, error)
	GetGrowInfo(context.Context, *GetGrowInfoReq) (*GetGrowInfoResp, error)
}

func RegisterMissionTimelineServer(s *grpc.Server, srv MissionTimelineServer) {
	s.RegisterService(&_MissionTimeline_serviceDesc, srv)
}

func _MissionTimeline_WriteTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).WriteTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/WriteTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).WriteTimelineMsg(ctx, req.(*WriteTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionTimeline_BatchDeleteTimeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteTimelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).BatchDeleteTimeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/BatchDeleteTimeline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).BatchDeleteTimeline(ctx, req.(*BatchDeleteTimelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionTimeline_PullTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).PullTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/PullTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).PullTimelineMsg(ctx, req.(*PullTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionTimeline_MarkReaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkReadedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).MarkReaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/MarkReaded",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).MarkReaded(ctx, req.(*MarkReadedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionTimeline_GetReaded_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReadedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).GetReaded(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/GetReaded",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).GetReaded(ctx, req.(*GetReadedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionTimeline_UpdateGrowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGrowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).UpdateGrowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/UpdateGrowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).UpdateGrowInfo(ctx, req.(*UpdateGrowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MissionTimeline_GetGrowInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGrowInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionTimelineServer).GetGrowInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.MissionTimeline/GetGrowInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionTimelineServer).GetGrowInfo(ctx, req.(*GetGrowInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MissionTimeline_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Mission.MissionTimeline",
	HandlerType: (*MissionTimelineServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WriteTimelineMsg",
			Handler:    _MissionTimeline_WriteTimelineMsg_Handler,
		},
		{
			MethodName: "BatchDeleteTimeline",
			Handler:    _MissionTimeline_BatchDeleteTimeline_Handler,
		},
		{
			MethodName: "PullTimelineMsg",
			Handler:    _MissionTimeline_PullTimelineMsg_Handler,
		},
		{
			MethodName: "MarkReaded",
			Handler:    _MissionTimeline_MarkReaded_Handler,
		},
		{
			MethodName: "GetReaded",
			Handler:    _MissionTimeline_GetReaded_Handler,
		},
		{
			MethodName: "UpdateGrowInfo",
			Handler:    _MissionTimeline_UpdateGrowInfo_Handler,
		},
		{
			MethodName: "GetGrowInfo",
			Handler:    _MissionTimeline_GetGrowInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/missiontimelinesvr/missiontimeline.proto",
}

func (m *MissionTimelineMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionTimelineMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Seqid))
	if m.MsgBin != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	return i, nil
}

func (m *GrowInfoTimelineMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrowInfoTimelineMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Seqid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Type))
	if m.MsgBin != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	return i, nil
}

func (m *MissionFinishMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionFinishMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.MissionKey)))
	i += copy(dAtA[i:], m.MissionKey)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Timestamp))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.NotifyMask))
	dAtA[i] = 0x22
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.NewGuide)))
	i += copy(dAtA[i:], m.NewGuide)
	return i, nil
}

func (m *GrowInfoMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GrowInfoMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Exp))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Level))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.CurrentLevelExpMin))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.CurrentLevelExpMax))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Currency))
	return i, nil
}

func (m *GuildMemContInfoMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildMemContInfoMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.TotalContribution))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.ValidContribution))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.MemberLv))
	return i, nil
}

func (m *MissionGuideMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MissionGuideMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.Guide)))
	i += copy(dAtA[i:], m.Guide)
	return i, nil
}

func (m *NumericInfoMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NumericInfoMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Charm))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Rich))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Charm64))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Rich64))
	return i, nil
}

func (m *UserScoreMessage) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserScoreMessage) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *WriteTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Msg.Size()))
		n1, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *WriteTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateGrowInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGrowInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Uid))
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Msg.Size()))
		n2, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *UpdateGrowInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGrowInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetGrowInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGrowInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func (m *GetGrowInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGrowInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Msgs) > 0 {
		for _, msg := range m.Msgs {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMissiontimeline(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchDeleteTimelineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeleteTimelineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	if len(m.SeqIdList) > 0 {
		for _, num := range m.SeqIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintMissiontimeline(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchDeleteTimelineResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeleteTimelineResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PullTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.StartSeqid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.IsRev))
	return i, nil
}

func (m *PullTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMissiontimeline(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MarkReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x18
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func (m *MarkReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *GetReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMissiontimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func encodeFixed64Missiontimeline(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Missiontimeline(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMissiontimeline(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *MissionTimelineMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Type))
	n += 1 + sovMissiontimeline(uint64(m.Seqid))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovMissiontimeline(uint64(l))
	}
	return n
}

func (m *GrowInfoTimelineMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Seqid))
	n += 1 + sovMissiontimeline(uint64(m.Type))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovMissiontimeline(uint64(l))
	}
	return n
}

func (m *MissionFinishMessage) Size() (n int) {
	var l int
	_ = l
	l = len(m.MissionKey)
	n += 1 + l + sovMissiontimeline(uint64(l))
	n += 1 + sovMissiontimeline(uint64(m.Timestamp))
	n += 1 + sovMissiontimeline(uint64(m.NotifyMask))
	l = len(m.NewGuide)
	n += 1 + l + sovMissiontimeline(uint64(l))
	return n
}

func (m *GrowInfoMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Exp))
	n += 1 + sovMissiontimeline(uint64(m.Level))
	n += 1 + sovMissiontimeline(uint64(m.CurrentLevelExpMin))
	n += 1 + sovMissiontimeline(uint64(m.CurrentLevelExpMax))
	n += 1 + sovMissiontimeline(uint64(m.Currency))
	return n
}

func (m *GuildMemContInfoMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.GuildId))
	n += 1 + sovMissiontimeline(uint64(m.TotalContribution))
	n += 1 + sovMissiontimeline(uint64(m.ValidContribution))
	n += 1 + sovMissiontimeline(uint64(m.MemberLv))
	return n
}

func (m *MissionGuideMessage) Size() (n int) {
	var l int
	_ = l
	l = len(m.Guide)
	n += 1 + l + sovMissiontimeline(uint64(l))
	return n
}

func (m *NumericInfoMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Charm))
	n += 1 + sovMissiontimeline(uint64(m.Rich))
	n += 1 + sovMissiontimeline(uint64(m.Charm64))
	n += 1 + sovMissiontimeline(uint64(m.Rich64))
	return n
}

func (m *UserScoreMessage) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Score))
	return n
}

func (m *WriteTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovMissiontimeline(uint64(l))
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovMissiontimeline(uint64(l))
	}
	return n
}

func (m *WriteTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateGrowInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Uid))
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovMissiontimeline(uint64(l))
	}
	return n
}

func (m *UpdateGrowInfoResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetGrowInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Uid))
	n += 1 + sovMissiontimeline(uint64(m.Seqid))
	return n
}

func (m *GetGrowInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.Msgs) > 0 {
		for _, e := range m.Msgs {
			l = e.Size()
			n += 1 + l + sovMissiontimeline(uint64(l))
		}
	}
	return n
}

func (m *BatchDeleteTimelineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovMissiontimeline(uint64(l))
	if len(m.SeqIdList) > 0 {
		for _, e := range m.SeqIdList {
			n += 1 + sovMissiontimeline(uint64(e))
		}
	}
	return n
}

func (m *BatchDeleteTimelineResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PullTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovMissiontimeline(uint64(l))
	n += 1 + sovMissiontimeline(uint64(m.StartSeqid))
	n += 1 + sovMissiontimeline(uint64(m.Limit))
	n += 1 + sovMissiontimeline(uint64(m.IsRev))
	return n
}

func (m *PullTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovMissiontimeline(uint64(l))
		}
	}
	return n
}

func (m *MarkReadedReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovMissiontimeline(uint64(l))
	n += 1 + sovMissiontimeline(uint64(m.Seqid))
	return n
}

func (m *MarkReadedResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetReadedReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovMissiontimeline(uint64(l))
	return n
}

func (m *GetReadedResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMissiontimeline(uint64(m.Seqid))
	return n
}

func sovMissiontimeline(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMissiontimeline(x uint64) (n int) {
	return sovMissiontimeline(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *MissionTimelineMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionTimelineMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionTimelineMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrowInfoTimelineMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GrowInfoTimelineMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GrowInfoTimelineMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MissionFinishMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionFinishMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionFinishMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MissionKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NotifyMask", wireType)
			}
			m.NotifyMask = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NotifyMask |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewGuide", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NewGuide = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("notify_mask")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GrowInfoMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GrowInfoMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GrowInfoMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Exp", wireType)
			}
			m.Exp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Exp |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentLevelExpMin", wireType)
			}
			m.CurrentLevelExpMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentLevelExpMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentLevelExpMax", wireType)
			}
			m.CurrentLevelExpMax = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentLevelExpMax |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Currency", wireType)
			}
			m.Currency = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Currency |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("exp")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("level")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current_level_exp_min")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("current_level_exp_max")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("currency")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildMemContInfoMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildMemContInfoMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildMemContInfoMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalContribution", wireType)
			}
			m.TotalContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidContribution", wireType)
			}
			m.ValidContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberLv", wireType)
			}
			m.MemberLv = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberLv |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_contribution")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("valid_contribution")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("member_lv")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MissionGuideMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MissionGuideMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MissionGuideMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Guide", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Guide = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guide")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NumericInfoMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NumericInfoMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NumericInfoMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm", wireType)
			}
			m.Charm = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich", wireType)
			}
			m.Rich = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Charm64", wireType)
			}
			m.Charm64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Charm64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Rich64", wireType)
			}
			m.Rich64 = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Rich64 |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("charm")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rich")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserScoreMessage) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserScoreMessage: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserScoreMessage: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &MissionTimelineMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGrowInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGrowInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGrowInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &GrowInfoTimelineMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGrowInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGrowInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGrowInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGrowInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGrowInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGrowInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGrowInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGrowInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGrowInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msgs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msgs = append(m.Msgs, &GrowInfoTimelineMsg{})
			if err := m.Msgs[len(m.Msgs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeleteTimelineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeleteTimelineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeleteTimelineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMissiontimeline
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SeqIdList = append(m.SeqIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowMissiontimeline
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthMissiontimeline
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowMissiontimeline
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SeqIdList = append(m.SeqIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeleteTimelineResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeleteTimelineResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeleteTimelineResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartSeqid", wireType)
			}
			m.StartSeqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSeqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRev", wireType)
			}
			m.IsRev = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsRev |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_seqid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &MissionTimelineMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkReadedResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReadedResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMissiontimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMissiontimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMissiontimeline(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMissiontimeline
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMissiontimeline
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMissiontimeline
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMissiontimeline
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMissiontimeline(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMissiontimeline = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMissiontimeline   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/missiontimelinesvr/missiontimeline.proto", fileDescriptorMissiontimeline)
}

var fileDescriptorMissiontimeline = []byte{
	// 1361 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x55, 0x4f, 0x4f, 0x1b, 0xd7,
	0x17, 0x65, 0x3c, 0xb6, 0xc1, 0x97, 0x10, 0xcc, 0x83, 0x80, 0x63, 0x08, 0x99, 0x8c, 0x7e, 0xf9,
	0x15, 0x29, 0x81, 0x34, 0x7f, 0x94, 0x4a, 0x11, 0x42, 0x05, 0xe3, 0x98, 0x51, 0x18, 0x83, 0xc6,
	0xb6, 0x5a, 0xda, 0xc5, 0x68, 0xec, 0x79, 0x98, 0x27, 0x66, 0xc6, 0xc3, 0xbc, 0xb1, 0x31, 0xbb,
	0x2e, 0xa3, 0xae, 0xaa, 0x74, 0xd7, 0x35, 0x52, 0xfb, 0x31, 0xba, 0xa9, 0x9a, 0x65, 0xb7, 0xdd,
	0x44, 0x55, 0xb2, 0xe1, 0x63, 0x54, 0xef, 0xcd, 0xd8, 0x1e, 0xff, 0xc1, 0xa9, 0xd2, 0xee, 0xec,
	0x73, 0xef, 0x7b, 0xf7, 0x9c, 0xfb, 0xce, 0xbd, 0x03, 0x0f, 0xa9, 0x57, 0x7b, 0x64, 0x13, 0x4a,
	0x49, 0xc3, 0xf1, 0x89, 0x8d, 0x2d, 0xe2, 0x60, 0xda, 0xf2, 0x06, 0xa1, 0x0d, 0xd7, 0x6b, 0xf8,
	0x0d, 0x34, 0xa9, 0x06, 0x70, 0xf6, 0x7f, 0xb5, 0x86, 0x6d, 0x37, 0x9c, 0x47, 0xbe, 0xd5, 0x72,
	0x49, 0xed, 0xd4, 0xc2, 0x8f, 0xe8, 0x69, 0xb5, 0x49, 0x2c, 0x9f, 0x38, 0xfe, 0x85, 0x1b, 0xa6,
	0xcb, 0x3f, 0xc6, 0x00, 0x85, 0x27, 0xca, 0xe1, 0x45, 0x2a, 0xad, 0xa3, 0x0c, 0xc4, 0x59, 0x52,
	0x46, 0x90, 0x62, 0x6b, 0x33, 0x3b, 0xf1, 0xb7, 0xef, 0xee, 0x4e, 0x68, 0x1c, 0x41, 0x59, 0x48,
	0x50, 0x7c, 0x46, 0xcc, 0x4c, 0x2c, 0x12, 0x0a, 0x20, 0x74, 0x07, 0x26, 0x6d, 0x5a, 0xd7, 0xab,
	0xc4, 0xc9, 0x88, 0x52, 0x6c, 0xed, 0x46, 0x18, 0x4d, 0xda, 0xb4, 0xbe, 0x43, 0x1c, 0xf9, 0x67,
	0x01, 0xe2, 0xe5, 0xa3, 0xc3, 0x3c, 0x5a, 0x04, 0xa4, 0x2a, 0xa5, 0x92, 0x72, 0x50, 0xd4, 0x5f,
	0x2a, 0x45, 0xa5, 0xb4, 0xa7, 0xab, 0xa5, 0x42, 0x5a, 0x40, 0x2b, 0x90, 0xc9, 0x7f, 0x7d, 0xa8,
	0xe7, 0x2a, 0x9a, 0x96, 0x2f, 0xe6, 0x8e, 0xf4, 0xdc, 0xde, 0x76, 0xb1, 0x90, 0xdf, 0xe5, 0xd1,
	0x18, 0xba, 0x05, 0x73, 0x9d, 0x53, 0x85, 0x8a, 0xb2, 0x9b, 0xe7, 0xb0, 0x88, 0xee, 0xc0, 0xed,
	0x42, 0x45, 0xd9, 0xdf, 0xd5, 0xd5, 0xbc, 0xba, 0x93, 0xd7, 0xf4, 0xdc, 0x41, 0xb1, 0xac, 0x29,
	0x3b, 0x95, 0xb2, 0x72, 0x50, 0x4c, 0xc7, 0x59, 0xad, 0x4a, 0x29, 0xaf, 0xe9, 0xa5, 0x83, 0x9c,
	0x96, 0xef, 0xdc, 0x98, 0x4e, 0xa0, 0x65, 0x58, 0xe2, 0x78, 0x6e, 0x6f, 0x5b, 0x53, 0x75, 0x4d,
	0xc9, 0xed, 0x75, 0x83, 0x49, 0xf9, 0x37, 0x01, 0xe6, 0x0b, 0x5e, 0xe3, 0x5c, 0x71, 0x8e, 0x1b,
	0xd1, 0xb6, 0x74, 0xc5, 0x0b, 0xc3, 0xe2, 0x3b, 0x2d, 0x8b, 0x0d, 0xb5, 0xec, 0x23, 0x6d, 0x39,
	0x0a, 0xbb, 0x32, 0x4e, 0xbd, 0x70, 0x8d, 0x8e, 0xd8, 0x38, 0x1d, 0xa2, 0xfc, 0x4e, 0x80, 0x85,
	0xf0, 0x75, 0x5f, 0x12, 0x87, 0xd0, 0x13, 0x15, 0x53, 0x6a, 0xd4, 0x31, 0xba, 0x0f, 0xd3, 0xa1,
	0x7d, 0xf4, 0x53, 0x7c, 0xc1, 0xe5, 0xa4, 0x42, 0x5a, 0x10, 0x06, 0x5e, 0xe1, 0x0b, 0x24, 0x43,
	0x8a, 0xd9, 0x8b, 0xfa, 0x86, 0xed, 0xf6, 0x09, 0xeb, 0xc1, 0xec, 0x2a, 0xa7, 0xe1, 0x93, 0xe3,
	0x0b, 0xdd, 0x36, 0xe8, 0x29, 0x57, 0xd8, 0xc9, 0x82, 0x20, 0xa0, 0x1a, 0xf4, 0x14, 0xdd, 0x83,
	0x94, 0x83, 0xcf, 0xf5, 0x7a, 0x93, 0x98, 0x38, 0x13, 0x97, 0x84, 0x6e, 0xbd, 0x29, 0x07, 0x9f,
	0x17, 0x18, 0x2a, 0x3f, 0x86, 0xe9, 0xe2, 0x41, 0x59, 0x79, 0x79, 0xa4, 0xab, 0xdb, 0xa5, 0x57,
	0x68, 0x0a, 0xe2, 0xc5, 0x83, 0x62, 0x3e, 0x3d, 0x81, 0x66, 0x20, 0xa5, 0xe5, 0x77, 0xf5, 0xc3,
	0x03, 0xa5, 0x58, 0x4e, 0x0b, 0x28, 0x05, 0x89, 0xed, 0xfd, 0x72, 0x5e, 0x4b, 0xc7, 0xe4, 0x3f,
	0x05, 0x98, 0xed, 0x3c, 0x54, 0x47, 0xdb, 0x22, 0x88, 0xb8, 0xed, 0x72, 0x4d, 0x89, 0xb0, 0x06,
	0x03, 0xd8, 0xe3, 0x59, 0xb8, 0x85, 0xad, 0x7e, 0xe7, 0x72, 0x08, 0x7d, 0x01, 0xb7, 0x6a, 0x4d,
	0xcf, 0xc3, 0x8e, 0xaf, 0x73, 0x40, 0xc7, 0x6d, 0x57, 0xb7, 0xc3, 0x07, 0xeb, 0xe4, 0xa2, 0x30,
	0x65, 0x9f, 0x65, 0xe4, 0xdb, 0xae, 0x4a, 0x9c, 0x6b, 0x0e, 0x1a, 0xed, 0x4c, 0x7c, 0xdc, 0x41,
	0xa3, 0x8d, 0x24, 0x98, 0x0a, 0xd0, 0xda, 0x45, 0x26, 0x11, 0xa1, 0xda, 0x45, 0xe5, 0xdf, 0x05,
	0x58, 0x2a, 0x34, 0x89, 0x65, 0xaa, 0xd8, 0xce, 0x35, 0x1c, 0x3f, 0xaa, 0xf1, 0x2e, 0x4c, 0xd5,
	0x59, 0x48, 0x1f, 0xf0, 0xe2, 0x24, 0x47, 0x15, 0x13, 0x3d, 0x05, 0xe4, 0x37, 0x7c, 0xc3, 0xd2,
	0x6b, 0x0d, 0xc7, 0xf7, 0x48, 0xb5, 0xe9, 0x93, 0x86, 0xd3, 0xa7, 0x7c, 0x8e, 0xc7, 0x73, 0x91,
	0x30, 0x3b, 0xd4, 0x32, 0x2c, 0x62, 0xf6, 0x1f, 0x8a, 0xb6, 0x60, 0x8e, 0xc7, 0xfb, 0x0e, 0xdd,
	0x83, 0x94, 0x8d, 0xed, 0x2a, 0xf6, 0x74, 0xab, 0xd5, 0xa7, 0x7a, 0x2a, 0x80, 0xf7, 0x5b, 0xf2,
	0x63, 0x98, 0x0f, 0x5d, 0xc8, 0x1f, 0xba, 0x23, 0x22, 0x0b, 0x89, 0xc0, 0x0e, 0x51, 0xfb, 0x05,
	0x90, 0xfc, 0x5a, 0x00, 0x54, 0x6c, 0xda, 0xd8, 0x23, 0xb5, 0xa8, 0xee, 0x2c, 0x24, 0x6a, 0x27,
	0x86, 0x67, 0xf7, 0x0f, 0x20, 0x87, 0xd8, 0x00, 0x7a, 0xa4, 0x76, 0xd2, 0x3f, 0x80, 0x0c, 0x41,
	0xab, 0x30, 0xc9, 0x53, 0x9e, 0x3f, 0xcb, 0x88, 0x92, 0xb0, 0x16, 0xef, 0x34, 0x2b, 0x04, 0xd1,
	0x0a, 0x24, 0x59, 0xde, 0xf3, 0x67, 0xdc, 0x98, 0x9d, 0x70, 0x88, 0xc9, 0x1b, 0x90, 0xae, 0x50,
	0xec, 0x95, 0x6a, 0x0d, 0x2f, 0x4a, 0x9d, 0xb2, 0xff, 0x03, 0x8b, 0x80, 0x41, 0x72, 0x1b, 0xe6,
	0xbf, 0xf2, 0x88, 0x8f, 0x23, 0x8b, 0x43, 0xc3, 0x67, 0x68, 0x01, 0x62, 0x03, 0x8f, 0x15, 0x23,
	0x26, 0x2b, 0x4d, 0x9b, 0xc7, 0xc7, 0xa4, 0xcd, 0x69, 0x77, 0x9a, 0x10, 0x62, 0x68, 0x1d, 0x44,
	0x9b, 0xd6, 0xf9, 0x0b, 0x4c, 0x3f, 0x59, 0xde, 0x08, 0x9b, 0xb8, 0x31, 0xbc, 0xb0, 0x35, 0x96,
	0x27, 0x2f, 0xc2, 0xc2, 0x70, 0x65, 0xea, 0xca, 0xdf, 0xc2, 0x5c, 0xc5, 0x35, 0x0d, 0x1f, 0x77,
	0x46, 0x85, 0xf1, 0x59, 0x04, 0xb1, 0x39, 0x40, 0x88, 0x01, 0x68, 0x23, 0xa8, 0x19, 0xe3, 0x35,
	0x57, 0xba, 0x35, 0x47, 0xac, 0xc3, 0xa0, 0xe8, 0x02, 0xa0, 0xc1, 0xcb, 0xa9, 0x2b, 0xef, 0xc2,
	0xcd, 0x02, 0xf6, 0xff, 0x49, 0xbd, 0x31, 0x1f, 0x14, 0x39, 0x07, 0xb3, 0x7d, 0xb7, 0x50, 0x17,
	0x7d, 0x0e, 0x71, 0x9b, 0xd6, 0x69, 0x46, 0x90, 0xc4, 0x8f, 0xf2, 0xe3, 0x99, 0xb2, 0x05, 0x8b,
	0x3b, 0x86, 0x5f, 0x3b, 0xd9, 0xc5, 0x16, 0xee, 0xf5, 0xe6, 0x53, 0x9f, 0x64, 0x15, 0xa6, 0x29,
	0x3e, 0xd3, 0x89, 0xa9, 0x5b, 0x84, 0xfa, 0x19, 0x51, 0x12, 0xd7, 0x66, 0xb4, 0x14, 0xc5, 0x67,
	0x8a, 0xb9, 0x4f, 0xa8, 0x2f, 0xdf, 0x86, 0xa5, 0x91, 0xd5, 0xa8, 0x2b, 0xff, 0x22, 0x00, 0x3a,
	0x6c, 0x5a, 0xd6, 0x7f, 0x60, 0x8c, 0xfb, 0x30, 0x4d, 0x7d, 0xc3, 0xf3, 0xf5, 0xa0, 0x75, 0x7d,
	0x4b, 0x97, 0x07, 0x4a, 0xfc, 0x9b, 0xc4, 0x56, 0x1e, 0xb1, 0x89, 0xdf, 0x37, 0x97, 0x01, 0x84,
	0x96, 0x21, 0x49, 0xa8, 0xee, 0xe1, 0x56, 0x26, 0x21, 0x09, 0xbd, 0x20, 0xa1, 0x1a, 0x6e, 0xc9,
	0x2a, 0xcc, 0x0f, 0x31, 0xa5, 0x2e, 0x7a, 0x0e, 0x53, 0xec, 0x4b, 0xc6, 0x95, 0x07, 0x0f, 0x30,
	0xd6, 0x94, 0xec, 0xb3, 0xc7, 0x9b, 0xa2, 0xc3, 0x8c, 0x6a, 0x78, 0xa7, 0x1a, 0x36, 0x4c, 0x6c,
	0x7e, 0xaa, 0xe6, 0xae, 0x51, 0xc4, 0x61, 0xa3, 0xa4, 0xe1, 0x66, 0xb4, 0x00, 0x75, 0xe5, 0x1d,
	0xb8, 0x51, 0xc0, 0xfe, 0xbf, 0xaa, 0x28, 0x3f, 0x80, 0x99, 0xc8, 0x1d, 0xd4, 0x1d, 0xf7, 0xfd,
	0x7f, 0xf2, 0x21, 0x09, 0xb3, 0x03, 0x3d, 0x40, 0xe7, 0x90, 0x1e, 0x1c, 0x48, 0xd4, 0xb3, 0xec,
	0x88, 0x2d, 0x91, 0xbd, 0x33, 0x26, 0x4a, 0x5d, 0xf9, 0xff, 0xdf, 0x5d, 0x5e, 0x89, 0xc2, 0xf7,
	0x97, 0x57, 0x62, 0x9c, 0xbc, 0xa0, 0x2f, 0xde, 0x5c, 0x5e, 0x89, 0xf3, 0xeb, 0x44, 0xda, 0x24,
	0xe6, 0x96, 0xb4, 0x4e, 0xa5, 0xcd, 0x80, 0xf8, 0x16, 0xfa, 0x49, 0x80, 0xf9, 0x11, 0x36, 0x44,
	0x77, 0xbb, 0xd7, 0x8f, 0x1e, 0x89, 0xac, 0x34, 0x3e, 0x81, 0xba, 0xf2, 0x97, 0x8c, 0x42, 0x8c,
	0x51, 0x48, 0x32, 0x0a, 0x16, 0x27, 0xf1, 0x60, 0x04, 0x09, 0x69, 0xdd, 0x92, 0x36, 0xc3, 0x59,
	0x79, 0xfc, 0x50, 0x0a, 0x7f, 0x3d, 0xd9, 0x42, 0x6f, 0x04, 0x98, 0x1d, 0x70, 0x17, 0xea, 0xf9,
	0x68, 0x78, 0x42, 0xb2, 0x2b, 0xd7, 0x07, 0xd9, 0xaa, 0x61, 0x84, 0x44, 0x46, 0x68, 0x8a, 0x11,
	0xaa, 0x86, 0x94, 0xd6, 0x47, 0x52, 0xaa, 0x4a, 0x9b, 0x55, 0x5c, 0x27, 0x0e, 0x1b, 0x9c, 0x80,
	0x21, 0x9f, 0x86, 0x2d, 0xe4, 0x01, 0xf4, 0x1c, 0x84, 0x16, 0x7b, 0xb6, 0x8e, 0xfa, 0x36, 0xbb,
	0x34, 0x12, 0xa7, 0xae, 0xfc, 0x94, 0x91, 0x88, 0x77, 0xbb, 0x62, 0x72, 0x0a, 0xab, 0x23, 0x29,
	0x98, 0xbc, 0x2b, 0xc4, 0xdc, 0x42, 0x55, 0x48, 0x75, 0xfd, 0x85, 0x6e, 0xf5, 0x56, 0x59, 0xc4,
	0xb7, 0xd9, 0xc5, 0x51, 0x70, 0xc7, 0x09, 0x89, 0x8f, 0x3b, 0xe1, 0x1b, 0xb8, 0xd9, 0xbf, 0x9e,
	0x51, 0xb6, 0x7b, 0xe3, 0xd0, 0x47, 0x21, 0xbb, 0x7c, 0x6d, 0x8c, 0xba, 0xf2, 0x2c, 0x2b, 0x99,
	0x64, 0x25, 0x27, 0x58, 0xb9, 0x09, 0x44, 0x60, 0x3a, 0xb2, 0x9e, 0xd1, 0x52, 0x94, 0x6a, 0xf4,
	0xd6, 0xcc, 0xe8, 0x00, 0x75, 0xe5, 0xcf, 0xd8, 0x95, 0x93, 0x5c, 0x45, 0x33, 0x54, 0xb1, 0xb0,
	0xde, 0x94, 0x36, 0x9b, 0x5d, 0x19, 0xdc, 0x35, 0x5b, 0xd9, 0xe4, 0xeb, 0xcb, 0x2b, 0xf1, 0xd7,
	0xe6, 0x4e, 0xfa, 0xed, 0xfb, 0x55, 0xe1, 0x8f, 0xf7, 0xab, 0xc2, 0x5f, 0xef, 0x57, 0x85, 0x1f,
	0x3e, 0xac, 0x4e, 0xfc, 0x1d, 0x00, 0x00, 0xff, 0xff, 0x11, 0x37, 0x2a, 0xf6, 0x1f, 0x0d, 0x00,
	0x00,
}
