// Code generated by protoc-gen-gogo.
// source: src/friendsvr/friend.proto
// DO NOT EDIT!

/*
	Package Friend is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/friendsvr/friend.proto

	It has these top-level messages:
		StFriend
		StFriendVerify
		StFriendGroup
		StPublicAccountSettings
		StPublicAccount
		AddFriendVerifyReq
		AddFriendVerifyRsp
		AddFriendReq
		AddFriendRsp
		UpdateFriendInfoReq
		UpdateFriendInfoRsp
		UpdateFriendVerifyReq
		UpdateFriendVerifyRsp
		DelFriendVerifyReq
		DelFriendVerifyRsp
		DelFriendReq
		DelFriendRsp
		GetOneFriendReq
		GetOneFriendRsp
		GetAllFriendInfoOrVerifyReq
		GetAllFriendInfoOrVerifyRsp
		AddBlackListReq
		DelBlackListReq
		GetBlackListReq
		GetBlackListResp
		GetFriendDataBySeqReq
		GetFriendDataBySeqResp
		UpdateFriendGroupReq
		UpdateFriendGroupResp
		GetFriendGroupReq
		GetFriendGroupResp
		GetAllMyGroupReq
		GetAllMyGroupResp
		UpdatePublicAccountReq
		UpdatePublicAccountResp
		DeleteAllPublicAccountsReq
		DeleteAllPublicAccountsResp
		GetPublicAccountReq
		GetPublicAccountResp
		GetUserSubscribingPublicAccountsReq
		GetUserSubscribingPublicAccountsResp
*/
package Friend

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type StFriend_STATUS int32

const (
	StFriend_NORMAL StFriend_STATUS = 0
	StFriend_BLACK  StFriend_STATUS = 1
)

var StFriend_STATUS_name = map[int32]string{
	0: "NORMAL",
	1: "BLACK",
}
var StFriend_STATUS_value = map[string]int32{
	"NORMAL": 0,
	"BLACK":  1,
}

func (x StFriend_STATUS) Enum() *StFriend_STATUS {
	p := new(StFriend_STATUS)
	*p = x
	return p
}
func (x StFriend_STATUS) String() string {
	return proto.EnumName(StFriend_STATUS_name, int32(x))
}
func (x *StFriend_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StFriend_STATUS_value, data, "StFriend_STATUS")
	if err != nil {
		return err
	}
	*x = StFriend_STATUS(value)
	return nil
}
func (StFriend_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorFriend, []int{0, 0} }

type StFriendGroup_STATUS int32

const (
	StFriendGroup_NORMAL StFriendGroup_STATUS = 0
)

var StFriendGroup_STATUS_name = map[int32]string{
	0: "NORMAL",
}
var StFriendGroup_STATUS_value = map[string]int32{
	"NORMAL": 0,
}

func (x StFriendGroup_STATUS) Enum() *StFriendGroup_STATUS {
	p := new(StFriendGroup_STATUS)
	*p = x
	return p
}
func (x StFriendGroup_STATUS) String() string {
	return proto.EnumName(StFriendGroup_STATUS_name, int32(x))
}
func (x *StFriendGroup_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(StFriendGroup_STATUS_value, data, "StFriendGroup_STATUS")
	if err != nil {
		return err
	}
	*x = StFriendGroup_STATUS(value)
	return nil
}
func (StFriendGroup_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorFriend, []int{2, 0} }

// 好友信息存储结构
type StFriend struct {
	// enum FRIEND_SRC_TYPE {
	// 	DEFAULT = 0;			// 默认，用户自己搜索账号添加
	// 	GUILD_AUTO_ADD = 1;		// 工会会长，系统自动添加
	// }
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	UserAccount       string `protobuf:"bytes,2,req,name=user_account,json=userAccount" json:"user_account"`
	Phone             string `protobuf:"bytes,3,req,name=phone" json:"phone"`
	Nick              string `protobuf:"bytes,4,req,name=nick" json:"nick"`
	Signature         string `protobuf:"bytes,5,req,name=signature" json:"signature"`
	InfoUpdateSeq     uint32 `protobuf:"varint,6,req,name=info_update_seq,json=infoUpdateSeq" json:"info_update_seq"`
	Status            uint32 `protobuf:"varint,7,req,name=status" json:"status"`
	FaceMd5           string `protobuf:"bytes,8,req,name=face_md5,json=faceMd5" json:"face_md5"`
	FriendSrcType     uint32 `protobuf:"varint,9,opt,name=friend_src_type,json=friendSrcType" json:"friend_src_type"`
	IsDelete          bool   `protobuf:"varint,10,req,name=is_delete,json=isDelete" json:"is_delete"`
	CoverMd5          string `protobuf:"bytes,11,req,name=cover_md5,json=coverMd5" json:"cover_md5"`
	AccountAlias      string `protobuf:"bytes,12,req,name=account_alias,json=accountAlias" json:"account_alias"`
	NickRemark        string `protobuf:"bytes,13,req,name=nick_remark,json=nickRemark" json:"nick_remark"`
	Sex               uint32 `protobuf:"varint,14,req,name=sex" json:"sex"`
	UserType          uint32 `protobuf:"varint,15,req,name=user_type,json=userType" json:"user_type"`
	SourceFlag        uint32 `protobuf:"varint,16,req,name=source_flag,json=sourceFlag" json:"source_flag"`
	FriendAddTime     uint32 `protobuf:"varint,17,req,name=friend_add_time,json=friendAddTime" json:"friend_add_time"`
	IsStarMarked      bool   `protobuf:"varint,18,req,name=is_star_marked,json=isStarMarked" json:"is_star_marked"`
	IsBanked          bool   `protobuf:"varint,19,req,name=is_banked,json=isBanked" json:"is_banked"`
	LastUpdateTime    uint32 `protobuf:"varint,20,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	CreateOrDeleteSeq uint64 `protobuf:"varint,21,opt,name=create_or_delete_seq,json=createOrDeleteSeq" json:"create_or_delete_seq"`
	NobilityLevel     uint32 `protobuf:"varint,22,opt,name=nobility_level,json=nobilityLevel" json:"nobility_level"`
	DynamicFaceMd5    string `protobuf:"bytes,23,opt,name=dynamic_face_md5,json=dynamicFaceMd5" json:"dynamic_face_md5"`
}

func (m *StFriend) Reset()                    { *m = StFriend{} }
func (m *StFriend) String() string            { return proto.CompactTextString(m) }
func (*StFriend) ProtoMessage()               {}
func (*StFriend) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{0} }

func (m *StFriend) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StFriend) GetUserAccount() string {
	if m != nil {
		return m.UserAccount
	}
	return ""
}

func (m *StFriend) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *StFriend) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *StFriend) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *StFriend) GetInfoUpdateSeq() uint32 {
	if m != nil {
		return m.InfoUpdateSeq
	}
	return 0
}

func (m *StFriend) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StFriend) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *StFriend) GetFriendSrcType() uint32 {
	if m != nil {
		return m.FriendSrcType
	}
	return 0
}

func (m *StFriend) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *StFriend) GetCoverMd5() string {
	if m != nil {
		return m.CoverMd5
	}
	return ""
}

func (m *StFriend) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *StFriend) GetNickRemark() string {
	if m != nil {
		return m.NickRemark
	}
	return ""
}

func (m *StFriend) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *StFriend) GetUserType() uint32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *StFriend) GetSourceFlag() uint32 {
	if m != nil {
		return m.SourceFlag
	}
	return 0
}

func (m *StFriend) GetFriendAddTime() uint32 {
	if m != nil {
		return m.FriendAddTime
	}
	return 0
}

func (m *StFriend) GetIsStarMarked() bool {
	if m != nil {
		return m.IsStarMarked
	}
	return false
}

func (m *StFriend) GetIsBanked() bool {
	if m != nil {
		return m.IsBanked
	}
	return false
}

func (m *StFriend) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StFriend) GetCreateOrDeleteSeq() uint64 {
	if m != nil {
		return m.CreateOrDeleteSeq
	}
	return 0
}

func (m *StFriend) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *StFriend) GetDynamicFaceMd5() string {
	if m != nil {
		return m.DynamicFaceMd5
	}
	return ""
}

// 待验证的好友消息
type StFriendVerify struct {
	Uid            uint32   `protobuf:"varint,1,req,name=uid" json:"uid"`
	UserAccount    string   `protobuf:"bytes,2,req,name=user_account,json=userAccount" json:"user_account"`
	Phone          string   `protobuf:"bytes,3,req,name=phone" json:"phone"`
	Nick           string   `protobuf:"bytes,4,req,name=nick" json:"nick"`
	Signature      string   `protobuf:"bytes,5,req,name=signature" json:"signature"`
	InfoUpdateSeq  uint32   `protobuf:"varint,6,req,name=info_update_seq,json=infoUpdateSeq" json:"info_update_seq"`
	Status         uint32   `protobuf:"varint,7,req,name=status" json:"status"`
	FaceMd5        string   `protobuf:"bytes,8,req,name=face_md5,json=faceMd5" json:"face_md5"`
	VerifyMsgList  []string `protobuf:"bytes,9,rep,name=verify_msg_list,json=verifyMsgList" json:"verify_msg_list,omitempty"`
	LastUpdateTime uint32   `protobuf:"varint,10,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	UserType       uint32   `protobuf:"varint,11,req,name=user_type,json=userType" json:"user_type"`
	SourceFlag     uint32   `protobuf:"varint,12,req,name=source_flag,json=sourceFlag" json:"source_flag"`
	AccountAlias   string   `protobuf:"bytes,13,req,name=account_alias,json=accountAlias" json:"account_alias"`
	Sex            uint32   `protobuf:"varint,14,req,name=sex" json:"sex"`
	CoverMd5       string   `protobuf:"bytes,15,req,name=cover_md5,json=coverMd5" json:"cover_md5"`
	FriendSrcType  uint32   `protobuf:"varint,16,opt,name=friend_src_type,json=friendSrcType" json:"friend_src_type"`
}

func (m *StFriendVerify) Reset()                    { *m = StFriendVerify{} }
func (m *StFriendVerify) String() string            { return proto.CompactTextString(m) }
func (*StFriendVerify) ProtoMessage()               {}
func (*StFriendVerify) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{1} }

func (m *StFriendVerify) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StFriendVerify) GetUserAccount() string {
	if m != nil {
		return m.UserAccount
	}
	return ""
}

func (m *StFriendVerify) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *StFriendVerify) GetNick() string {
	if m != nil {
		return m.Nick
	}
	return ""
}

func (m *StFriendVerify) GetSignature() string {
	if m != nil {
		return m.Signature
	}
	return ""
}

func (m *StFriendVerify) GetInfoUpdateSeq() uint32 {
	if m != nil {
		return m.InfoUpdateSeq
	}
	return 0
}

func (m *StFriendVerify) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StFriendVerify) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *StFriendVerify) GetVerifyMsgList() []string {
	if m != nil {
		return m.VerifyMsgList
	}
	return nil
}

func (m *StFriendVerify) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StFriendVerify) GetUserType() uint32 {
	if m != nil {
		return m.UserType
	}
	return 0
}

func (m *StFriendVerify) GetSourceFlag() uint32 {
	if m != nil {
		return m.SourceFlag
	}
	return 0
}

func (m *StFriendVerify) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *StFriendVerify) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *StFriendVerify) GetCoverMd5() string {
	if m != nil {
		return m.CoverMd5
	}
	return ""
}

func (m *StFriendVerify) GetFriendSrcType() uint32 {
	if m != nil {
		return m.FriendSrcType
	}
	return 0
}

// 好友列表中的群
type StFriendGroup struct {
	GroupId              uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	Status               uint32 `protobuf:"varint,2,req,name=status" json:"status"`
	IsDeleted            bool   `protobuf:"varint,3,req,name=is_deleted,json=isDeleted" json:"is_deleted"`
	InfoUpdateSeq        uint32 `protobuf:"varint,4,req,name=info_update_seq,json=infoUpdateSeq" json:"info_update_seq"`
	IsShow               bool   `protobuf:"varint,5,req,name=is_show,json=isShow" json:"is_show"`
	MemListLastUpdateSeq uint32 `protobuf:"varint,6,opt,name=mem_list_last_update_seq,json=memListLastUpdateSeq" json:"mem_list_last_update_seq"`
	Type                 uint32 `protobuf:"varint,7,opt,name=type" json:"type"`
}

func (m *StFriendGroup) Reset()                    { *m = StFriendGroup{} }
func (m *StFriendGroup) String() string            { return proto.CompactTextString(m) }
func (*StFriendGroup) ProtoMessage()               {}
func (*StFriendGroup) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{2} }

func (m *StFriendGroup) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *StFriendGroup) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StFriendGroup) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *StFriendGroup) GetInfoUpdateSeq() uint32 {
	if m != nil {
		return m.InfoUpdateSeq
	}
	return 0
}

func (m *StFriendGroup) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *StFriendGroup) GetMemListLastUpdateSeq() uint32 {
	if m != nil {
		return m.MemListLastUpdateSeq
	}
	return 0
}

func (m *StFriendGroup) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type StPublicAccountSettings struct {
	MessageNotification uint32 `protobuf:"varint,1,opt,name=message_notification,json=messageNotification" json:"message_notification"`
}

func (m *StPublicAccountSettings) Reset()                    { *m = StPublicAccountSettings{} }
func (m *StPublicAccountSettings) String() string            { return proto.CompactTextString(m) }
func (*StPublicAccountSettings) ProtoMessage()               {}
func (*StPublicAccountSettings) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{3} }

func (m *StPublicAccountSettings) GetMessageNotification() uint32 {
	if m != nil {
		return m.MessageNotification
	}
	return 0
}

// 公众号
type StPublicAccount struct {
	PublicId      uint32                   `protobuf:"varint,1,req,name=public_id,json=publicId" json:"public_id"`
	Account       string                   `protobuf:"bytes,2,req,name=account" json:"account"`
	Name          string                   `protobuf:"bytes,3,req,name=name" json:"name"`
	Type          uint32                   `protobuf:"varint,4,req,name=type" json:"type"`
	InfoUpdateSeq uint32                   `protobuf:"varint,5,req,name=info_update_seq,json=infoUpdateSeq" json:"info_update_seq"`
	FaceMd5       string                   `protobuf:"bytes,6,req,name=face_md5,json=faceMd5" json:"face_md5"`
	Active        bool                     `protobuf:"varint,7,req,name=active" json:"active"`
	ShowProfile   bool                     `protobuf:"varint,8,req,name=show_profile,json=showProfile" json:"show_profile"`
	IsDelete      bool                     `protobuf:"varint,9,req,name=is_delete,json=isDelete" json:"is_delete"`
	Settings      *StPublicAccountSettings `protobuf:"bytes,10,opt,name=settings" json:"settings,omitempty"`
	Intro         string                   `protobuf:"bytes,11,opt,name=intro" json:"intro"`
	UpdateTime    uint32                   `protobuf:"varint,12,opt,name=update_time,json=updateTime" json:"update_time"`
}

func (m *StPublicAccount) Reset()                    { *m = StPublicAccount{} }
func (m *StPublicAccount) String() string            { return proto.CompactTextString(m) }
func (*StPublicAccount) ProtoMessage()               {}
func (*StPublicAccount) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{4} }

func (m *StPublicAccount) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

func (m *StPublicAccount) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *StPublicAccount) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StPublicAccount) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *StPublicAccount) GetInfoUpdateSeq() uint32 {
	if m != nil {
		return m.InfoUpdateSeq
	}
	return 0
}

func (m *StPublicAccount) GetFaceMd5() string {
	if m != nil {
		return m.FaceMd5
	}
	return ""
}

func (m *StPublicAccount) GetActive() bool {
	if m != nil {
		return m.Active
	}
	return false
}

func (m *StPublicAccount) GetShowProfile() bool {
	if m != nil {
		return m.ShowProfile
	}
	return false
}

func (m *StPublicAccount) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *StPublicAccount) GetSettings() *StPublicAccountSettings {
	if m != nil {
		return m.Settings
	}
	return nil
}

func (m *StPublicAccount) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *StPublicAccount) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// ////////////////
// 添加好友验证
type AddFriendVerifyReq struct {
	Uid       uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid uint32          `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
	Verify    *StFriendVerify `protobuf:"bytes,3,req,name=verify" json:"verify,omitempty"`
}

func (m *AddFriendVerifyReq) Reset()                    { *m = AddFriendVerifyReq{} }
func (m *AddFriendVerifyReq) String() string            { return proto.CompactTextString(m) }
func (*AddFriendVerifyReq) ProtoMessage()               {}
func (*AddFriendVerifyReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{5} }

func (m *AddFriendVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFriendVerifyReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

func (m *AddFriendVerifyReq) GetVerify() *StFriendVerify {
	if m != nil {
		return m.Verify
	}
	return nil
}

type AddFriendVerifyRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *AddFriendVerifyRsp) Reset()                    { *m = AddFriendVerifyRsp{} }
func (m *AddFriendVerifyRsp) String() string            { return proto.CompactTextString(m) }
func (*AddFriendVerifyRsp) ProtoMessage()               {}
func (*AddFriendVerifyRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{6} }

func (m *AddFriendVerifyRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 添加好友
type AddFriendReq struct {
	Uid        uint32    `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid  uint32    `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
	FriendInfo *StFriend `protobuf:"bytes,3,req,name=friend_info,json=friendInfo" json:"friend_info,omitempty"`
}

func (m *AddFriendReq) Reset()                    { *m = AddFriendReq{} }
func (m *AddFriendReq) String() string            { return proto.CompactTextString(m) }
func (*AddFriendReq) ProtoMessage()               {}
func (*AddFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{7} }

func (m *AddFriendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddFriendReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

func (m *AddFriendReq) GetFriendInfo() *StFriend {
	if m != nil {
		return m.FriendInfo
	}
	return nil
}

type AddFriendRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *AddFriendRsp) Reset()                    { *m = AddFriendRsp{} }
func (m *AddFriendRsp) String() string            { return proto.CompactTextString(m) }
func (*AddFriendRsp) ProtoMessage()               {}
func (*AddFriendRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{8} }

func (m *AddFriendRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 更新好友信息
type UpdateFriendInfoReq struct {
	Uid        uint32    `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid  uint32    `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
	FriendInfo *StFriend `protobuf:"bytes,3,req,name=friend_info,json=friendInfo" json:"friend_info,omitempty"`
}

func (m *UpdateFriendInfoReq) Reset()                    { *m = UpdateFriendInfoReq{} }
func (m *UpdateFriendInfoReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateFriendInfoReq) ProtoMessage()               {}
func (*UpdateFriendInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{9} }

func (m *UpdateFriendInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFriendInfoReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

func (m *UpdateFriendInfoReq) GetFriendInfo() *StFriend {
	if m != nil {
		return m.FriendInfo
	}
	return nil
}

type UpdateFriendInfoRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *UpdateFriendInfoRsp) Reset()                    { *m = UpdateFriendInfoRsp{} }
func (m *UpdateFriendInfoRsp) String() string            { return proto.CompactTextString(m) }
func (*UpdateFriendInfoRsp) ProtoMessage()               {}
func (*UpdateFriendInfoRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{10} }

func (m *UpdateFriendInfoRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 更新好友验证信息
type UpdateFriendVerifyReq struct {
	Uid       uint32          `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid uint32          `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
	Verify    *StFriendVerify `protobuf:"bytes,3,req,name=verify" json:"verify,omitempty"`
}

func (m *UpdateFriendVerifyReq) Reset()                    { *m = UpdateFriendVerifyReq{} }
func (m *UpdateFriendVerifyReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateFriendVerifyReq) ProtoMessage()               {}
func (*UpdateFriendVerifyReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{11} }

func (m *UpdateFriendVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateFriendVerifyReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

func (m *UpdateFriendVerifyReq) GetVerify() *StFriendVerify {
	if m != nil {
		return m.Verify
	}
	return nil
}

type UpdateFriendVerifyRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *UpdateFriendVerifyRsp) Reset()                    { *m = UpdateFriendVerifyRsp{} }
func (m *UpdateFriendVerifyRsp) String() string            { return proto.CompactTextString(m) }
func (*UpdateFriendVerifyRsp) ProtoMessage()               {}
func (*UpdateFriendVerifyRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{12} }

func (m *UpdateFriendVerifyRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 删除好友验证
type DelFriendVerifyReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid uint32 `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
}

func (m *DelFriendVerifyReq) Reset()                    { *m = DelFriendVerifyReq{} }
func (m *DelFriendVerifyReq) String() string            { return proto.CompactTextString(m) }
func (*DelFriendVerifyReq) ProtoMessage()               {}
func (*DelFriendVerifyReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{13} }

func (m *DelFriendVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelFriendVerifyReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

type DelFriendVerifyRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *DelFriendVerifyRsp) Reset()                    { *m = DelFriendVerifyRsp{} }
func (m *DelFriendVerifyRsp) String() string            { return proto.CompactTextString(m) }
func (*DelFriendVerifyRsp) ProtoMessage()               {}
func (*DelFriendVerifyRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{14} }

func (m *DelFriendVerifyRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 删除好友
type DelFriendReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid uint32 `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
}

func (m *DelFriendReq) Reset()                    { *m = DelFriendReq{} }
func (m *DelFriendReq) String() string            { return proto.CompactTextString(m) }
func (*DelFriendReq) ProtoMessage()               {}
func (*DelFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{15} }

func (m *DelFriendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelFriendReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

type DelFriendRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *DelFriendRsp) Reset()                    { *m = DelFriendRsp{} }
func (m *DelFriendRsp) String() string            { return proto.CompactTextString(m) }
func (*DelFriendRsp) ProtoMessage()               {}
func (*DelFriendRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{16} }

func (m *DelFriendRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 查询单个朋友
type GetOneFriendReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	FriendUid uint32 `protobuf:"varint,2,req,name=friend_uid,json=friendUid" json:"friend_uid"`
}

func (m *GetOneFriendReq) Reset()                    { *m = GetOneFriendReq{} }
func (m *GetOneFriendReq) String() string            { return proto.CompactTextString(m) }
func (*GetOneFriendReq) ProtoMessage()               {}
func (*GetOneFriendReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{17} }

func (m *GetOneFriendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetOneFriendReq) GetFriendUid() uint32 {
	if m != nil {
		return m.FriendUid
	}
	return 0
}

type GetOneFriendRsp struct {
	Ret        uint32          `protobuf:"varint,1,req,name=ret" json:"ret"`
	FriendInfo *StFriend       `protobuf:"bytes,2,opt,name=friend_info,json=friendInfo" json:"friend_info,omitempty"`
	VerifyInfo *StFriendVerify `protobuf:"bytes,3,opt,name=verify_info,json=verifyInfo" json:"verify_info,omitempty"`
}

func (m *GetOneFriendRsp) Reset()                    { *m = GetOneFriendRsp{} }
func (m *GetOneFriendRsp) String() string            { return proto.CompactTextString(m) }
func (*GetOneFriendRsp) ProtoMessage()               {}
func (*GetOneFriendRsp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{18} }

func (m *GetOneFriendRsp) GetRet() uint32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *GetOneFriendRsp) GetFriendInfo() *StFriend {
	if m != nil {
		return m.FriendInfo
	}
	return nil
}

func (m *GetOneFriendRsp) GetVerifyInfo() *StFriendVerify {
	if m != nil {
		return m.VerifyInfo
	}
	return nil
}

// ////////////////
// 查询所有朋友信息
type GetAllFriendInfoOrVerifyReq struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GetFriendInfo bool   `protobuf:"varint,2,opt,name=getFriendInfo" json:"getFriendInfo"`
	GetVerifyInfo bool   `protobuf:"varint,3,opt,name=getVerifyInfo" json:"getVerifyInfo"`
	IsOnlyValid   bool   `protobuf:"varint,4,opt,name=is_only_valid,json=isOnlyValid" json:"is_only_valid"`
}

func (m *GetAllFriendInfoOrVerifyReq) Reset()         { *m = GetAllFriendInfoOrVerifyReq{} }
func (m *GetAllFriendInfoOrVerifyReq) String() string { return proto.CompactTextString(m) }
func (*GetAllFriendInfoOrVerifyReq) ProtoMessage()    {}
func (*GetAllFriendInfoOrVerifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriend, []int{19}
}

func (m *GetAllFriendInfoOrVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAllFriendInfoOrVerifyReq) GetGetFriendInfo() bool {
	if m != nil {
		return m.GetFriendInfo
	}
	return false
}

func (m *GetAllFriendInfoOrVerifyReq) GetGetVerifyInfo() bool {
	if m != nil {
		return m.GetVerifyInfo
	}
	return false
}

func (m *GetAllFriendInfoOrVerifyReq) GetIsOnlyValid() bool {
	if m != nil {
		return m.IsOnlyValid
	}
	return false
}

type GetAllFriendInfoOrVerifyRsp struct {
	Ret            int32             `protobuf:"varint,1,req,name=ret" json:"ret"`
	FriendInfoList []*StFriend       `protobuf:"bytes,2,rep,name=friend_info_list,json=friendInfoList" json:"friend_info_list,omitempty"`
	VerifyInfoList []*StFriendVerify `protobuf:"bytes,3,rep,name=verify_info_list,json=verifyInfoList" json:"verify_info_list,omitempty"`
}

func (m *GetAllFriendInfoOrVerifyRsp) Reset()         { *m = GetAllFriendInfoOrVerifyRsp{} }
func (m *GetAllFriendInfoOrVerifyRsp) String() string { return proto.CompactTextString(m) }
func (*GetAllFriendInfoOrVerifyRsp) ProtoMessage()    {}
func (*GetAllFriendInfoOrVerifyRsp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriend, []int{20}
}

func (m *GetAllFriendInfoOrVerifyRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *GetAllFriendInfoOrVerifyRsp) GetFriendInfoList() []*StFriend {
	if m != nil {
		return m.FriendInfoList
	}
	return nil
}

func (m *GetAllFriendInfoOrVerifyRsp) GetVerifyInfoList() []*StFriendVerify {
	if m != nil {
		return m.VerifyInfoList
	}
	return nil
}

// 黑名单
type AddBlackListReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	TargetUid uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *AddBlackListReq) Reset()                    { *m = AddBlackListReq{} }
func (m *AddBlackListReq) String() string            { return proto.CompactTextString(m) }
func (*AddBlackListReq) ProtoMessage()               {}
func (*AddBlackListReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{21} }

func (m *AddBlackListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddBlackListReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type DelBlackListReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	TargetUid uint32 `protobuf:"varint,2,req,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *DelBlackListReq) Reset()                    { *m = DelBlackListReq{} }
func (m *DelBlackListReq) String() string            { return proto.CompactTextString(m) }
func (*DelBlackListReq) ProtoMessage()               {}
func (*DelBlackListReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{22} }

func (m *DelBlackListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelBlackListReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type GetBlackListReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetBlackListReq) Reset()                    { *m = GetBlackListReq{} }
func (m *GetBlackListReq) String() string            { return proto.CompactTextString(m) }
func (*GetBlackListReq) ProtoMessage()               {}
func (*GetBlackListReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{23} }

func (m *GetBlackListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetBlackListResp struct {
	UidList []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetBlackListResp) Reset()                    { *m = GetBlackListResp{} }
func (m *GetBlackListResp) String() string            { return proto.CompactTextString(m) }
func (*GetBlackListResp) ProtoMessage()               {}
func (*GetBlackListResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{24} }

func (m *GetBlackListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// ////////////////
// 根据seq查询好友资料
type GetFriendDataBySeqReq struct {
	SeqId uint32 `protobuf:"varint,1,req,name=seq_id,json=seqId" json:"seq_id"`
}

func (m *GetFriendDataBySeqReq) Reset()                    { *m = GetFriendDataBySeqReq{} }
func (m *GetFriendDataBySeqReq) String() string            { return proto.CompactTextString(m) }
func (*GetFriendDataBySeqReq) ProtoMessage()               {}
func (*GetFriendDataBySeqReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{25} }

func (m *GetFriendDataBySeqReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type GetFriendDataBySeqResp struct {
	FriendList        []*StFriend        `protobuf:"bytes,1,rep,name=friend_list,json=friendList" json:"friend_list,omitempty"`
	VerifyList        []*StFriendVerify  `protobuf:"bytes,2,rep,name=verify_list,json=verifyList" json:"verify_list,omitempty"`
	Group             []*StFriendGroup   `protobuf:"bytes,3,rep,name=group" json:"group,omitempty"`
	PublicAccountList []*StPublicAccount `protobuf:"bytes,4,rep,name=public_account_list,json=publicAccountList" json:"public_account_list,omitempty"`
}

func (m *GetFriendDataBySeqResp) Reset()                    { *m = GetFriendDataBySeqResp{} }
func (m *GetFriendDataBySeqResp) String() string            { return proto.CompactTextString(m) }
func (*GetFriendDataBySeqResp) ProtoMessage()               {}
func (*GetFriendDataBySeqResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{26} }

func (m *GetFriendDataBySeqResp) GetFriendList() []*StFriend {
	if m != nil {
		return m.FriendList
	}
	return nil
}

func (m *GetFriendDataBySeqResp) GetVerifyList() []*StFriendVerify {
	if m != nil {
		return m.VerifyList
	}
	return nil
}

func (m *GetFriendDataBySeqResp) GetGroup() []*StFriendGroup {
	if m != nil {
		return m.Group
	}
	return nil
}

func (m *GetFriendDataBySeqResp) GetPublicAccountList() []*StPublicAccount {
	if m != nil {
		return m.PublicAccountList
	}
	return nil
}

type UpdateFriendGroupReq struct {
	GroupInfo *StFriendGroup `protobuf:"bytes,1,req,name=group_info,json=groupInfo" json:"group_info,omitempty"`
}

func (m *UpdateFriendGroupReq) Reset()                    { *m = UpdateFriendGroupReq{} }
func (m *UpdateFriendGroupReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateFriendGroupReq) ProtoMessage()               {}
func (*UpdateFriendGroupReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{27} }

func (m *UpdateFriendGroupReq) GetGroupInfo() *StFriendGroup {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

type UpdateFriendGroupResp struct {
}

func (m *UpdateFriendGroupResp) Reset()                    { *m = UpdateFriendGroupResp{} }
func (m *UpdateFriendGroupResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateFriendGroupResp) ProtoMessage()               {}
func (*UpdateFriendGroupResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{28} }

type GetFriendGroupReq struct {
	GroupId uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
}

func (m *GetFriendGroupReq) Reset()                    { *m = GetFriendGroupReq{} }
func (m *GetFriendGroupReq) String() string            { return proto.CompactTextString(m) }
func (*GetFriendGroupReq) ProtoMessage()               {}
func (*GetFriendGroupReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{29} }

func (m *GetFriendGroupReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

type GetFriendGroupResp struct {
	GroupInfo *StFriendGroup `protobuf:"bytes,1,req,name=group_info,json=groupInfo" json:"group_info,omitempty"`
}

func (m *GetFriendGroupResp) Reset()                    { *m = GetFriendGroupResp{} }
func (m *GetFriendGroupResp) String() string            { return proto.CompactTextString(m) }
func (*GetFriendGroupResp) ProtoMessage()               {}
func (*GetFriendGroupResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{30} }

func (m *GetFriendGroupResp) GetGroupInfo() *StFriendGroup {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

type GetAllMyGroupReq struct {
}

func (m *GetAllMyGroupReq) Reset()                    { *m = GetAllMyGroupReq{} }
func (m *GetAllMyGroupReq) String() string            { return proto.CompactTextString(m) }
func (*GetAllMyGroupReq) ProtoMessage()               {}
func (*GetAllMyGroupReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{31} }

type GetAllMyGroupResp struct {
	GroupList []*StFriendGroup `protobuf:"bytes,1,rep,name=group_list,json=groupList" json:"group_list,omitempty"`
}

func (m *GetAllMyGroupResp) Reset()                    { *m = GetAllMyGroupResp{} }
func (m *GetAllMyGroupResp) String() string            { return proto.CompactTextString(m) }
func (*GetAllMyGroupResp) ProtoMessage()               {}
func (*GetAllMyGroupResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{32} }

func (m *GetAllMyGroupResp) GetGroupList() []*StFriendGroup {
	if m != nil {
		return m.GroupList
	}
	return nil
}

// ////////////////
// 公众号
// ////////////////
type UpdatePublicAccountReq struct {
	PublicDetail *StPublicAccount `protobuf:"bytes,1,req,name=public_detail,json=publicDetail" json:"public_detail,omitempty"`
}

func (m *UpdatePublicAccountReq) Reset()                    { *m = UpdatePublicAccountReq{} }
func (m *UpdatePublicAccountReq) String() string            { return proto.CompactTextString(m) }
func (*UpdatePublicAccountReq) ProtoMessage()               {}
func (*UpdatePublicAccountReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{33} }

func (m *UpdatePublicAccountReq) GetPublicDetail() *StPublicAccount {
	if m != nil {
		return m.PublicDetail
	}
	return nil
}

type UpdatePublicAccountResp struct {
}

func (m *UpdatePublicAccountResp) Reset()                    { *m = UpdatePublicAccountResp{} }
func (m *UpdatePublicAccountResp) String() string            { return proto.CompactTextString(m) }
func (*UpdatePublicAccountResp) ProtoMessage()               {}
func (*UpdatePublicAccountResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{34} }

type DeleteAllPublicAccountsReq struct {
}

func (m *DeleteAllPublicAccountsReq) Reset()         { *m = DeleteAllPublicAccountsReq{} }
func (m *DeleteAllPublicAccountsReq) String() string { return proto.CompactTextString(m) }
func (*DeleteAllPublicAccountsReq) ProtoMessage()    {}
func (*DeleteAllPublicAccountsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriend, []int{35}
}

type DeleteAllPublicAccountsResp struct {
	Deleted uint64 `protobuf:"varint,1,req,name=deleted" json:"deleted"`
}

func (m *DeleteAllPublicAccountsResp) Reset()         { *m = DeleteAllPublicAccountsResp{} }
func (m *DeleteAllPublicAccountsResp) String() string { return proto.CompactTextString(m) }
func (*DeleteAllPublicAccountsResp) ProtoMessage()    {}
func (*DeleteAllPublicAccountsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriend, []int{36}
}

func (m *DeleteAllPublicAccountsResp) GetDeleted() uint64 {
	if m != nil {
		return m.Deleted
	}
	return 0
}

type GetPublicAccountReq struct {
	PublicId uint32 `protobuf:"varint,1,req,name=public_id,json=publicId" json:"public_id"`
}

func (m *GetPublicAccountReq) Reset()                    { *m = GetPublicAccountReq{} }
func (m *GetPublicAccountReq) String() string            { return proto.CompactTextString(m) }
func (*GetPublicAccountReq) ProtoMessage()               {}
func (*GetPublicAccountReq) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{37} }

func (m *GetPublicAccountReq) GetPublicId() uint32 {
	if m != nil {
		return m.PublicId
	}
	return 0
}

type GetPublicAccountResp struct {
	// 如果用户关注了公众号, 则返回详情; 否则该项为空
	PublicDetail *StPublicAccount `protobuf:"bytes,1,opt,name=public_detail,json=publicDetail" json:"public_detail,omitempty"`
}

func (m *GetPublicAccountResp) Reset()                    { *m = GetPublicAccountResp{} }
func (m *GetPublicAccountResp) String() string            { return proto.CompactTextString(m) }
func (*GetPublicAccountResp) ProtoMessage()               {}
func (*GetPublicAccountResp) Descriptor() ([]byte, []int) { return fileDescriptorFriend, []int{38} }

func (m *GetPublicAccountResp) GetPublicDetail() *StPublicAccount {
	if m != nil {
		return m.PublicDetail
	}
	return nil
}

// 取用户关注的公众号列表
type GetUserSubscribingPublicAccountsReq struct {
	IncludeDeleted bool `protobuf:"varint,1,req,name=include_deleted,json=includeDeleted" json:"include_deleted"`
}

func (m *GetUserSubscribingPublicAccountsReq) Reset()         { *m = GetUserSubscribingPublicAccountsReq{} }
func (m *GetUserSubscribingPublicAccountsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSubscribingPublicAccountsReq) ProtoMessage()    {}
func (*GetUserSubscribingPublicAccountsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorFriend, []int{39}
}

func (m *GetUserSubscribingPublicAccountsReq) GetIncludeDeleted() bool {
	if m != nil {
		return m.IncludeDeleted
	}
	return false
}

type GetUserSubscribingPublicAccountsResp struct {
	PublicAccountList []*StPublicAccount `protobuf:"bytes,1,rep,name=public_account_list,json=publicAccountList" json:"public_account_list,omitempty"`
}

func (m *GetUserSubscribingPublicAccountsResp) Reset()         { *m = GetUserSubscribingPublicAccountsResp{} }
func (m *GetUserSubscribingPublicAccountsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSubscribingPublicAccountsResp) ProtoMessage()    {}
func (*GetUserSubscribingPublicAccountsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorFriend, []int{40}
}

func (m *GetUserSubscribingPublicAccountsResp) GetPublicAccountList() []*StPublicAccount {
	if m != nil {
		return m.PublicAccountList
	}
	return nil
}

func init() {
	proto.RegisterType((*StFriend)(nil), "Friend.stFriend")
	proto.RegisterType((*StFriendVerify)(nil), "Friend.stFriendVerify")
	proto.RegisterType((*StFriendGroup)(nil), "Friend.stFriendGroup")
	proto.RegisterType((*StPublicAccountSettings)(nil), "Friend.stPublicAccountSettings")
	proto.RegisterType((*StPublicAccount)(nil), "Friend.stPublicAccount")
	proto.RegisterType((*AddFriendVerifyReq)(nil), "Friend.AddFriendVerifyReq")
	proto.RegisterType((*AddFriendVerifyRsp)(nil), "Friend.AddFriendVerifyRsp")
	proto.RegisterType((*AddFriendReq)(nil), "Friend.AddFriendReq")
	proto.RegisterType((*AddFriendRsp)(nil), "Friend.AddFriendRsp")
	proto.RegisterType((*UpdateFriendInfoReq)(nil), "Friend.UpdateFriendInfoReq")
	proto.RegisterType((*UpdateFriendInfoRsp)(nil), "Friend.UpdateFriendInfoRsp")
	proto.RegisterType((*UpdateFriendVerifyReq)(nil), "Friend.UpdateFriendVerifyReq")
	proto.RegisterType((*UpdateFriendVerifyRsp)(nil), "Friend.UpdateFriendVerifyRsp")
	proto.RegisterType((*DelFriendVerifyReq)(nil), "Friend.DelFriendVerifyReq")
	proto.RegisterType((*DelFriendVerifyRsp)(nil), "Friend.DelFriendVerifyRsp")
	proto.RegisterType((*DelFriendReq)(nil), "Friend.DelFriendReq")
	proto.RegisterType((*DelFriendRsp)(nil), "Friend.DelFriendRsp")
	proto.RegisterType((*GetOneFriendReq)(nil), "Friend.GetOneFriendReq")
	proto.RegisterType((*GetOneFriendRsp)(nil), "Friend.GetOneFriendRsp")
	proto.RegisterType((*GetAllFriendInfoOrVerifyReq)(nil), "Friend.GetAllFriendInfoOrVerifyReq")
	proto.RegisterType((*GetAllFriendInfoOrVerifyRsp)(nil), "Friend.GetAllFriendInfoOrVerifyRsp")
	proto.RegisterType((*AddBlackListReq)(nil), "Friend.AddBlackListReq")
	proto.RegisterType((*DelBlackListReq)(nil), "Friend.DelBlackListReq")
	proto.RegisterType((*GetBlackListReq)(nil), "Friend.GetBlackListReq")
	proto.RegisterType((*GetBlackListResp)(nil), "Friend.GetBlackListResp")
	proto.RegisterType((*GetFriendDataBySeqReq)(nil), "Friend.GetFriendDataBySeqReq")
	proto.RegisterType((*GetFriendDataBySeqResp)(nil), "Friend.GetFriendDataBySeqResp")
	proto.RegisterType((*UpdateFriendGroupReq)(nil), "Friend.UpdateFriendGroupReq")
	proto.RegisterType((*UpdateFriendGroupResp)(nil), "Friend.UpdateFriendGroupResp")
	proto.RegisterType((*GetFriendGroupReq)(nil), "Friend.GetFriendGroupReq")
	proto.RegisterType((*GetFriendGroupResp)(nil), "Friend.GetFriendGroupResp")
	proto.RegisterType((*GetAllMyGroupReq)(nil), "Friend.GetAllMyGroupReq")
	proto.RegisterType((*GetAllMyGroupResp)(nil), "Friend.GetAllMyGroupResp")
	proto.RegisterType((*UpdatePublicAccountReq)(nil), "Friend.UpdatePublicAccountReq")
	proto.RegisterType((*UpdatePublicAccountResp)(nil), "Friend.UpdatePublicAccountResp")
	proto.RegisterType((*DeleteAllPublicAccountsReq)(nil), "Friend.DeleteAllPublicAccountsReq")
	proto.RegisterType((*DeleteAllPublicAccountsResp)(nil), "Friend.DeleteAllPublicAccountsResp")
	proto.RegisterType((*GetPublicAccountReq)(nil), "Friend.GetPublicAccountReq")
	proto.RegisterType((*GetPublicAccountResp)(nil), "Friend.GetPublicAccountResp")
	proto.RegisterType((*GetUserSubscribingPublicAccountsReq)(nil), "Friend.GetUserSubscribingPublicAccountsReq")
	proto.RegisterType((*GetUserSubscribingPublicAccountsResp)(nil), "Friend.GetUserSubscribingPublicAccountsResp")
	proto.RegisterEnum("Friend.StFriend_STATUS", StFriend_STATUS_name, StFriend_STATUS_value)
	proto.RegisterEnum("Friend.StFriendGroup_STATUS", StFriendGroup_STATUS_name, StFriendGroup_STATUS_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Friend service

type FriendClient interface {
	AddFriendVerify(ctx context.Context, in *AddFriendVerifyReq, opts ...grpc.CallOption) (*AddFriendVerifyRsp, error)
	AddFriend(ctx context.Context, in *AddFriendReq, opts ...grpc.CallOption) (*AddFriendRsp, error)
	UpdateFriendInfo(ctx context.Context, in *UpdateFriendInfoReq, opts ...grpc.CallOption) (*UpdateFriendInfoRsp, error)
	UpdateFriendVerify(ctx context.Context, in *UpdateFriendVerifyReq, opts ...grpc.CallOption) (*UpdateFriendVerifyRsp, error)
	DelFriendVerify(ctx context.Context, in *DelFriendVerifyReq, opts ...grpc.CallOption) (*DelFriendVerifyRsp, error)
	DelFriend(ctx context.Context, in *DelFriendReq, opts ...grpc.CallOption) (*DelFriendRsp, error)
	GetOneFriend(ctx context.Context, in *GetOneFriendReq, opts ...grpc.CallOption) (*GetOneFriendRsp, error)
	GetAllFriendInfoOrVerify(ctx context.Context, in *GetAllFriendInfoOrVerifyReq, opts ...grpc.CallOption) (*GetAllFriendInfoOrVerifyRsp, error)
	GetFriendDataBySeq(ctx context.Context, in *GetFriendDataBySeqReq, opts ...grpc.CallOption) (*GetFriendDataBySeqResp, error)
	UpdateFriendGroup(ctx context.Context, in *UpdateFriendGroupReq, opts ...grpc.CallOption) (*UpdateFriendGroupResp, error)
	GetFriendGroup(ctx context.Context, in *GetFriendGroupReq, opts ...grpc.CallOption) (*GetFriendGroupResp, error)
	GetAllMyGroup(ctx context.Context, in *GetAllMyGroupReq, opts ...grpc.CallOption) (*GetAllMyGroupResp, error)
	UpdatePublicAccount(ctx context.Context, in *UpdatePublicAccountReq, opts ...grpc.CallOption) (*UpdatePublicAccountResp, error)
	DeleteAllPublicAccounts(ctx context.Context, in *DeleteAllPublicAccountsReq, opts ...grpc.CallOption) (*DeleteAllPublicAccountsResp, error)
	GetPublicAccount(ctx context.Context, in *GetPublicAccountReq, opts ...grpc.CallOption) (*GetPublicAccountResp, error)
	GetUserSubscribingPublicAccounts(ctx context.Context, in *GetUserSubscribingPublicAccountsReq, opts ...grpc.CallOption) (*GetUserSubscribingPublicAccountsResp, error)
	AddBlackList(ctx context.Context, in *AddBlackListReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetBlackList(ctx context.Context, in *GetBlackListReq, opts ...grpc.CallOption) (*GetBlackListResp, error)
	DelBlackList(ctx context.Context, in *DelBlackListReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type friendClient struct {
	cc *grpc.ClientConn
}

func NewFriendClient(cc *grpc.ClientConn) FriendClient {
	return &friendClient{cc}
}

func (c *friendClient) AddFriendVerify(ctx context.Context, in *AddFriendVerifyReq, opts ...grpc.CallOption) (*AddFriendVerifyRsp, error) {
	out := new(AddFriendVerifyRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/AddFriendVerify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) AddFriend(ctx context.Context, in *AddFriendReq, opts ...grpc.CallOption) (*AddFriendRsp, error) {
	out := new(AddFriendRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/AddFriend", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) UpdateFriendInfo(ctx context.Context, in *UpdateFriendInfoReq, opts ...grpc.CallOption) (*UpdateFriendInfoRsp, error) {
	out := new(UpdateFriendInfoRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/UpdateFriendInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) UpdateFriendVerify(ctx context.Context, in *UpdateFriendVerifyReq, opts ...grpc.CallOption) (*UpdateFriendVerifyRsp, error) {
	out := new(UpdateFriendVerifyRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/UpdateFriendVerify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) DelFriendVerify(ctx context.Context, in *DelFriendVerifyReq, opts ...grpc.CallOption) (*DelFriendVerifyRsp, error) {
	out := new(DelFriendVerifyRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/DelFriendVerify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) DelFriend(ctx context.Context, in *DelFriendReq, opts ...grpc.CallOption) (*DelFriendRsp, error) {
	out := new(DelFriendRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/DelFriend", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetOneFriend(ctx context.Context, in *GetOneFriendReq, opts ...grpc.CallOption) (*GetOneFriendRsp, error) {
	out := new(GetOneFriendRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetOneFriend", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetAllFriendInfoOrVerify(ctx context.Context, in *GetAllFriendInfoOrVerifyReq, opts ...grpc.CallOption) (*GetAllFriendInfoOrVerifyRsp, error) {
	out := new(GetAllFriendInfoOrVerifyRsp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetAllFriendInfoOrVerify", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetFriendDataBySeq(ctx context.Context, in *GetFriendDataBySeqReq, opts ...grpc.CallOption) (*GetFriendDataBySeqResp, error) {
	out := new(GetFriendDataBySeqResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetFriendDataBySeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) UpdateFriendGroup(ctx context.Context, in *UpdateFriendGroupReq, opts ...grpc.CallOption) (*UpdateFriendGroupResp, error) {
	out := new(UpdateFriendGroupResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/UpdateFriendGroup", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetFriendGroup(ctx context.Context, in *GetFriendGroupReq, opts ...grpc.CallOption) (*GetFriendGroupResp, error) {
	out := new(GetFriendGroupResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetFriendGroup", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetAllMyGroup(ctx context.Context, in *GetAllMyGroupReq, opts ...grpc.CallOption) (*GetAllMyGroupResp, error) {
	out := new(GetAllMyGroupResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetAllMyGroup", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) UpdatePublicAccount(ctx context.Context, in *UpdatePublicAccountReq, opts ...grpc.CallOption) (*UpdatePublicAccountResp, error) {
	out := new(UpdatePublicAccountResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/UpdatePublicAccount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) DeleteAllPublicAccounts(ctx context.Context, in *DeleteAllPublicAccountsReq, opts ...grpc.CallOption) (*DeleteAllPublicAccountsResp, error) {
	out := new(DeleteAllPublicAccountsResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/DeleteAllPublicAccounts", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetPublicAccount(ctx context.Context, in *GetPublicAccountReq, opts ...grpc.CallOption) (*GetPublicAccountResp, error) {
	out := new(GetPublicAccountResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetPublicAccount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetUserSubscribingPublicAccounts(ctx context.Context, in *GetUserSubscribingPublicAccountsReq, opts ...grpc.CallOption) (*GetUserSubscribingPublicAccountsResp, error) {
	out := new(GetUserSubscribingPublicAccountsResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetUserSubscribingPublicAccounts", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) AddBlackList(ctx context.Context, in *AddBlackListReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Friend.Friend/AddBlackList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) GetBlackList(ctx context.Context, in *GetBlackListReq, opts ...grpc.CallOption) (*GetBlackListResp, error) {
	out := new(GetBlackListResp)
	err := grpc.Invoke(ctx, "/Friend.Friend/GetBlackList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendClient) DelBlackList(ctx context.Context, in *DelBlackListReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/Friend.Friend/DelBlackList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Friend service

type FriendServer interface {
	AddFriendVerify(context.Context, *AddFriendVerifyReq) (*AddFriendVerifyRsp, error)
	AddFriend(context.Context, *AddFriendReq) (*AddFriendRsp, error)
	UpdateFriendInfo(context.Context, *UpdateFriendInfoReq) (*UpdateFriendInfoRsp, error)
	UpdateFriendVerify(context.Context, *UpdateFriendVerifyReq) (*UpdateFriendVerifyRsp, error)
	DelFriendVerify(context.Context, *DelFriendVerifyReq) (*DelFriendVerifyRsp, error)
	DelFriend(context.Context, *DelFriendReq) (*DelFriendRsp, error)
	GetOneFriend(context.Context, *GetOneFriendReq) (*GetOneFriendRsp, error)
	GetAllFriendInfoOrVerify(context.Context, *GetAllFriendInfoOrVerifyReq) (*GetAllFriendInfoOrVerifyRsp, error)
	GetFriendDataBySeq(context.Context, *GetFriendDataBySeqReq) (*GetFriendDataBySeqResp, error)
	UpdateFriendGroup(context.Context, *UpdateFriendGroupReq) (*UpdateFriendGroupResp, error)
	GetFriendGroup(context.Context, *GetFriendGroupReq) (*GetFriendGroupResp, error)
	GetAllMyGroup(context.Context, *GetAllMyGroupReq) (*GetAllMyGroupResp, error)
	UpdatePublicAccount(context.Context, *UpdatePublicAccountReq) (*UpdatePublicAccountResp, error)
	DeleteAllPublicAccounts(context.Context, *DeleteAllPublicAccountsReq) (*DeleteAllPublicAccountsResp, error)
	GetPublicAccount(context.Context, *GetPublicAccountReq) (*GetPublicAccountResp, error)
	GetUserSubscribingPublicAccounts(context.Context, *GetUserSubscribingPublicAccountsReq) (*GetUserSubscribingPublicAccountsResp, error)
	AddBlackList(context.Context, *AddBlackListReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetBlackList(context.Context, *GetBlackListReq) (*GetBlackListResp, error)
	DelBlackList(context.Context, *DelBlackListReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

func RegisterFriendServer(s *grpc.Server, srv FriendServer) {
	s.RegisterService(&_Friend_serviceDesc, srv)
}

func _Friend_AddFriendVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFriendVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).AddFriendVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/AddFriendVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).AddFriendVerify(ctx, req.(*AddFriendVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_AddFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFriendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).AddFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/AddFriend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).AddFriend(ctx, req.(*AddFriendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_UpdateFriendInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFriendInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).UpdateFriendInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/UpdateFriendInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).UpdateFriendInfo(ctx, req.(*UpdateFriendInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_UpdateFriendVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFriendVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).UpdateFriendVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/UpdateFriendVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).UpdateFriendVerify(ctx, req.(*UpdateFriendVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_DelFriendVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFriendVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).DelFriendVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/DelFriendVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).DelFriendVerify(ctx, req.(*DelFriendVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_DelFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFriendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).DelFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/DelFriend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).DelFriend(ctx, req.(*DelFriendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetOneFriend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOneFriendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetOneFriend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetOneFriend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetOneFriend(ctx, req.(*GetOneFriendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetAllFriendInfoOrVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllFriendInfoOrVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetAllFriendInfoOrVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetAllFriendInfoOrVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetAllFriendInfoOrVerify(ctx, req.(*GetAllFriendInfoOrVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetFriendDataBySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendDataBySeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetFriendDataBySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetFriendDataBySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetFriendDataBySeq(ctx, req.(*GetFriendDataBySeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_UpdateFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFriendGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).UpdateFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/UpdateFriendGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).UpdateFriendGroup(ctx, req.(*UpdateFriendGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetFriendGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetFriendGroup(ctx, req.(*GetFriendGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetAllMyGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMyGroupReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetAllMyGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetAllMyGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetAllMyGroup(ctx, req.(*GetAllMyGroupReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_UpdatePublicAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePublicAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).UpdatePublicAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/UpdatePublicAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).UpdatePublicAccount(ctx, req.(*UpdatePublicAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_DeleteAllPublicAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAllPublicAccountsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).DeleteAllPublicAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/DeleteAllPublicAccounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).DeleteAllPublicAccounts(ctx, req.(*DeleteAllPublicAccountsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetPublicAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPublicAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetPublicAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetPublicAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetPublicAccount(ctx, req.(*GetPublicAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetUserSubscribingPublicAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSubscribingPublicAccountsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetUserSubscribingPublicAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetUserSubscribingPublicAccounts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetUserSubscribingPublicAccounts(ctx, req.(*GetUserSubscribingPublicAccountsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_AddBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).AddBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/AddBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).AddBlackList(ctx, req.(*AddBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_GetBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).GetBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/GetBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).GetBlackList(ctx, req.(*GetBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friend_DelBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendServer).DelBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Friend.Friend/DelBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendServer).DelBlackList(ctx, req.(*DelBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Friend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Friend.Friend",
	HandlerType: (*FriendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddFriendVerify",
			Handler:    _Friend_AddFriendVerify_Handler,
		},
		{
			MethodName: "AddFriend",
			Handler:    _Friend_AddFriend_Handler,
		},
		{
			MethodName: "UpdateFriendInfo",
			Handler:    _Friend_UpdateFriendInfo_Handler,
		},
		{
			MethodName: "UpdateFriendVerify",
			Handler:    _Friend_UpdateFriendVerify_Handler,
		},
		{
			MethodName: "DelFriendVerify",
			Handler:    _Friend_DelFriendVerify_Handler,
		},
		{
			MethodName: "DelFriend",
			Handler:    _Friend_DelFriend_Handler,
		},
		{
			MethodName: "GetOneFriend",
			Handler:    _Friend_GetOneFriend_Handler,
		},
		{
			MethodName: "GetAllFriendInfoOrVerify",
			Handler:    _Friend_GetAllFriendInfoOrVerify_Handler,
		},
		{
			MethodName: "GetFriendDataBySeq",
			Handler:    _Friend_GetFriendDataBySeq_Handler,
		},
		{
			MethodName: "UpdateFriendGroup",
			Handler:    _Friend_UpdateFriendGroup_Handler,
		},
		{
			MethodName: "GetFriendGroup",
			Handler:    _Friend_GetFriendGroup_Handler,
		},
		{
			MethodName: "GetAllMyGroup",
			Handler:    _Friend_GetAllMyGroup_Handler,
		},
		{
			MethodName: "UpdatePublicAccount",
			Handler:    _Friend_UpdatePublicAccount_Handler,
		},
		{
			MethodName: "DeleteAllPublicAccounts",
			Handler:    _Friend_DeleteAllPublicAccounts_Handler,
		},
		{
			MethodName: "GetPublicAccount",
			Handler:    _Friend_GetPublicAccount_Handler,
		},
		{
			MethodName: "GetUserSubscribingPublicAccounts",
			Handler:    _Friend_GetUserSubscribingPublicAccounts_Handler,
		},
		{
			MethodName: "AddBlackList",
			Handler:    _Friend_AddBlackList_Handler,
		},
		{
			MethodName: "GetBlackList",
			Handler:    _Friend_GetBlackList_Handler,
		},
		{
			MethodName: "DelBlackList",
			Handler:    _Friend_DelBlackList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/friendsvr/friend.proto",
}

func (m *StFriend) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StFriend) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.UserAccount)))
	i += copy(dAtA[i:], m.UserAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Nick)))
	i += copy(dAtA[i:], m.Nick)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Signature)))
	i += copy(dAtA[i:], m.Signature)
	dAtA[i] = 0x30
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.InfoUpdateSeq))
	dAtA[i] = 0x38
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x42
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.FaceMd5)))
	i += copy(dAtA[i:], m.FaceMd5)
	dAtA[i] = 0x48
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendSrcType))
	dAtA[i] = 0x50
	i++
	if m.IsDelete {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x5a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.CoverMd5)))
	i += copy(dAtA[i:], m.CoverMd5)
	dAtA[i] = 0x62
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.AccountAlias)))
	i += copy(dAtA[i:], m.AccountAlias)
	dAtA[i] = 0x6a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.NickRemark)))
	i += copy(dAtA[i:], m.NickRemark)
	dAtA[i] = 0x70
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x78
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.UserType))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.SourceFlag))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendAddTime))
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x1
	i++
	if m.IsStarMarked {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x1
	i++
	if m.IsBanked {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.CreateOrDeleteSeq))
	dAtA[i] = 0xb0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.NobilityLevel))
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.DynamicFaceMd5)))
	i += copy(dAtA[i:], m.DynamicFaceMd5)
	return i, nil
}

func (m *StFriendVerify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StFriendVerify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.UserAccount)))
	i += copy(dAtA[i:], m.UserAccount)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	dAtA[i] = 0x22
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Nick)))
	i += copy(dAtA[i:], m.Nick)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Signature)))
	i += copy(dAtA[i:], m.Signature)
	dAtA[i] = 0x30
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.InfoUpdateSeq))
	dAtA[i] = 0x38
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x42
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.FaceMd5)))
	i += copy(dAtA[i:], m.FaceMd5)
	if len(m.VerifyMsgList) > 0 {
		for _, s := range m.VerifyMsgList {
			dAtA[i] = 0x4a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x50
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x58
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.UserType))
	dAtA[i] = 0x60
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.SourceFlag))
	dAtA[i] = 0x6a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.AccountAlias)))
	i += copy(dAtA[i:], m.AccountAlias)
	dAtA[i] = 0x70
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Sex))
	dAtA[i] = 0x7a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.CoverMd5)))
	i += copy(dAtA[i:], m.CoverMd5)
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendSrcType))
	return i, nil
}

func (m *StFriendGroup) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StFriendGroup) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x18
	i++
	if m.IsDeleted {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.InfoUpdateSeq))
	dAtA[i] = 0x28
	i++
	if m.IsShow {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.MemListLastUpdateSeq))
	dAtA[i] = 0x38
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Type))
	return i, nil
}

func (m *StPublicAccountSettings) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPublicAccountSettings) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.MessageNotification))
	return i, nil
}

func (m *StPublicAccount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StPublicAccount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.PublicId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x20
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x28
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.InfoUpdateSeq))
	dAtA[i] = 0x32
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.FaceMd5)))
	i += copy(dAtA[i:], m.FaceMd5)
	dAtA[i] = 0x38
	i++
	if m.Active {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x40
	i++
	if m.ShowProfile {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x48
	i++
	if m.IsDelete {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.Settings != nil {
		dAtA[i] = 0x52
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.Settings.Size()))
		n1, err := m.Settings.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x5a
	i++
	i = encodeVarintFriend(dAtA, i, uint64(len(m.Intro)))
	i += copy(dAtA[i:], m.Intro)
	dAtA[i] = 0x60
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.UpdateTime))
	return i, nil
}

func (m *AddFriendVerifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFriendVerifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	if m.Verify == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.Verify.Size()))
		n2, err := m.Verify.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *AddFriendVerifyRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFriendVerifyRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *AddFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	if m.FriendInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("friend_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.FriendInfo.Size()))
		n3, err := m.FriendInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *AddFriendRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddFriendRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *UpdateFriendInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFriendInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	if m.FriendInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("friend_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.FriendInfo.Size()))
		n4, err := m.FriendInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *UpdateFriendInfoRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFriendInfoRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *UpdateFriendVerifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFriendVerifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	if m.Verify == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("verify")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.Verify.Size()))
		n5, err := m.Verify.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *UpdateFriendVerifyRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFriendVerifyRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *DelFriendVerifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFriendVerifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	return i, nil
}

func (m *DelFriendVerifyRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFriendVerifyRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *DelFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	return i, nil
}

func (m *DelFriendRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelFriendRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *GetOneFriendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOneFriendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.FriendUid))
	return i, nil
}

func (m *GetOneFriendRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOneFriendRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	if m.FriendInfo != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.FriendInfo.Size()))
		n6, err := m.FriendInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	if m.VerifyInfo != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.VerifyInfo.Size()))
		n7, err := m.VerifyInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetAllFriendInfoOrVerifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllFriendInfoOrVerifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	if m.GetFriendInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	if m.GetVerifyInfo {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	if m.IsOnlyValid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetAllFriendInfoOrVerifyRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllFriendInfoOrVerifyRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Ret))
	if len(m.FriendInfoList) > 0 {
		for _, msg := range m.FriendInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.VerifyInfoList) > 0 {
		for _, msg := range m.VerifyInfoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddBlackListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddBlackListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *DelBlackListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelBlackListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *GetBlackListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBlackListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetBlackListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBlackListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintFriend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetFriendDataBySeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFriendDataBySeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.SeqId))
	return i, nil
}

func (m *GetFriendDataBySeqResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFriendDataBySeqResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.FriendList) > 0 {
		for _, msg := range m.FriendList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.VerifyList) > 0 {
		for _, msg := range m.VerifyList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.Group) > 0 {
		for _, msg := range m.Group {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.PublicAccountList) > 0 {
		for _, msg := range m.PublicAccountList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdateFriendGroupReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFriendGroupReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GroupInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("group_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.GroupInfo.Size()))
		n8, err := m.GroupInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *UpdateFriendGroupResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateFriendGroupResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetFriendGroupReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFriendGroupReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.GroupId))
	return i, nil
}

func (m *GetFriendGroupResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetFriendGroupResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GroupInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("group_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.GroupInfo.Size()))
		n9, err := m.GroupInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *GetAllMyGroupReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllMyGroupReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllMyGroupResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllMyGroupResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GroupList) > 0 {
		for _, msg := range m.GroupList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UpdatePublicAccountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePublicAccountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.PublicDetail == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("public_detail")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.PublicDetail.Size()))
		n10, err := m.PublicDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	return i, nil
}

func (m *UpdatePublicAccountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdatePublicAccountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeleteAllPublicAccountsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteAllPublicAccountsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DeleteAllPublicAccountsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteAllPublicAccountsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.Deleted))
	return i, nil
}

func (m *GetPublicAccountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPublicAccountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintFriend(dAtA, i, uint64(m.PublicId))
	return i, nil
}

func (m *GetPublicAccountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPublicAccountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.PublicDetail != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintFriend(dAtA, i, uint64(m.PublicDetail.Size()))
		n11, err := m.PublicDetail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	return i, nil
}

func (m *GetUserSubscribingPublicAccountsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserSubscribingPublicAccountsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IncludeDeleted {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetUserSubscribingPublicAccountsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserSubscribingPublicAccountsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PublicAccountList) > 0 {
		for _, msg := range m.PublicAccountList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintFriend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Friend(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Friend(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintFriend(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StFriend) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	l = len(m.UserAccount)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Nick)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Signature)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.InfoUpdateSeq))
	n += 1 + sovFriend(uint64(m.Status))
	l = len(m.FaceMd5)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.FriendSrcType))
	n += 2
	l = len(m.CoverMd5)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.AccountAlias)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.NickRemark)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.Sex))
	n += 1 + sovFriend(uint64(m.UserType))
	n += 2 + sovFriend(uint64(m.SourceFlag))
	n += 2 + sovFriend(uint64(m.FriendAddTime))
	n += 3
	n += 3
	n += 2 + sovFriend(uint64(m.LastUpdateTime))
	n += 2 + sovFriend(uint64(m.CreateOrDeleteSeq))
	n += 2 + sovFriend(uint64(m.NobilityLevel))
	l = len(m.DynamicFaceMd5)
	n += 2 + l + sovFriend(uint64(l))
	return n
}

func (m *StFriendVerify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	l = len(m.UserAccount)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Nick)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Signature)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.InfoUpdateSeq))
	n += 1 + sovFriend(uint64(m.Status))
	l = len(m.FaceMd5)
	n += 1 + l + sovFriend(uint64(l))
	if len(m.VerifyMsgList) > 0 {
		for _, s := range m.VerifyMsgList {
			l = len(s)
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	n += 1 + sovFriend(uint64(m.LastUpdateTime))
	n += 1 + sovFriend(uint64(m.UserType))
	n += 1 + sovFriend(uint64(m.SourceFlag))
	l = len(m.AccountAlias)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.Sex))
	l = len(m.CoverMd5)
	n += 1 + l + sovFriend(uint64(l))
	n += 2 + sovFriend(uint64(m.FriendSrcType))
	return n
}

func (m *StFriendGroup) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.GroupId))
	n += 1 + sovFriend(uint64(m.Status))
	n += 2
	n += 1 + sovFriend(uint64(m.InfoUpdateSeq))
	n += 2
	n += 1 + sovFriend(uint64(m.MemListLastUpdateSeq))
	n += 1 + sovFriend(uint64(m.Type))
	return n
}

func (m *StPublicAccountSettings) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.MessageNotification))
	return n
}

func (m *StPublicAccount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.PublicId))
	l = len(m.Account)
	n += 1 + l + sovFriend(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.Type))
	n += 1 + sovFriend(uint64(m.InfoUpdateSeq))
	l = len(m.FaceMd5)
	n += 1 + l + sovFriend(uint64(l))
	n += 2
	n += 2
	n += 2
	if m.Settings != nil {
		l = m.Settings.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	l = len(m.Intro)
	n += 1 + l + sovFriend(uint64(l))
	n += 1 + sovFriend(uint64(m.UpdateTime))
	return n
}

func (m *AddFriendVerifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	if m.Verify != nil {
		l = m.Verify.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *AddFriendVerifyRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	return n
}

func (m *AddFriendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	if m.FriendInfo != nil {
		l = m.FriendInfo.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *AddFriendRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	return n
}

func (m *UpdateFriendInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	if m.FriendInfo != nil {
		l = m.FriendInfo.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *UpdateFriendInfoRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	return n
}

func (m *UpdateFriendVerifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	if m.Verify != nil {
		l = m.Verify.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *UpdateFriendVerifyRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	return n
}

func (m *DelFriendVerifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	return n
}

func (m *DelFriendVerifyRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	return n
}

func (m *DelFriendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	return n
}

func (m *DelFriendRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	return n
}

func (m *GetOneFriendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.FriendUid))
	return n
}

func (m *GetOneFriendRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	if m.FriendInfo != nil {
		l = m.FriendInfo.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	if m.VerifyInfo != nil {
		l = m.VerifyInfo.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *GetAllFriendInfoOrVerifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 2
	n += 2
	n += 2
	return n
}

func (m *GetAllFriendInfoOrVerifyRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Ret))
	if len(m.FriendInfoList) > 0 {
		for _, e := range m.FriendInfoList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	if len(m.VerifyInfoList) > 0 {
		for _, e := range m.VerifyInfoList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	return n
}

func (m *AddBlackListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.TargetUid))
	return n
}

func (m *DelBlackListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	n += 1 + sovFriend(uint64(m.TargetUid))
	return n
}

func (m *GetBlackListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Uid))
	return n
}

func (m *GetBlackListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovFriend(uint64(e))
		}
	}
	return n
}

func (m *GetFriendDataBySeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.SeqId))
	return n
}

func (m *GetFriendDataBySeqResp) Size() (n int) {
	var l int
	_ = l
	if len(m.FriendList) > 0 {
		for _, e := range m.FriendList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	if len(m.VerifyList) > 0 {
		for _, e := range m.VerifyList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	if len(m.Group) > 0 {
		for _, e := range m.Group {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	if len(m.PublicAccountList) > 0 {
		for _, e := range m.PublicAccountList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	return n
}

func (m *UpdateFriendGroupReq) Size() (n int) {
	var l int
	_ = l
	if m.GroupInfo != nil {
		l = m.GroupInfo.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *UpdateFriendGroupResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetFriendGroupReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.GroupId))
	return n
}

func (m *GetFriendGroupResp) Size() (n int) {
	var l int
	_ = l
	if m.GroupInfo != nil {
		l = m.GroupInfo.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *GetAllMyGroupReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllMyGroupResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GroupList) > 0 {
		for _, e := range m.GroupList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	return n
}

func (m *UpdatePublicAccountReq) Size() (n int) {
	var l int
	_ = l
	if m.PublicDetail != nil {
		l = m.PublicDetail.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *UpdatePublicAccountResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeleteAllPublicAccountsReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DeleteAllPublicAccountsResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.Deleted))
	return n
}

func (m *GetPublicAccountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovFriend(uint64(m.PublicId))
	return n
}

func (m *GetPublicAccountResp) Size() (n int) {
	var l int
	_ = l
	if m.PublicDetail != nil {
		l = m.PublicDetail.Size()
		n += 1 + l + sovFriend(uint64(l))
	}
	return n
}

func (m *GetUserSubscribingPublicAccountsReq) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetUserSubscribingPublicAccountsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PublicAccountList) > 0 {
		for _, e := range m.PublicAccountList {
			l = e.Size()
			n += 1 + l + sovFriend(uint64(l))
		}
	}
	return n
}

func sovFriend(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozFriend(x uint64) (n int) {
	return sovFriend(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *StFriend) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stFriend: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stFriend: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nick", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nick = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Signature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Signature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoUpdateSeq", wireType)
			}
			m.InfoUpdateSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InfoUpdateSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendSrcType", wireType)
			}
			m.FriendSrcType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendSrcType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDelete", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDelete = bool(v != 0)
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CoverMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CoverMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccountAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccountAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000400)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NickRemark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.NickRemark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 15:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserType", wireType)
			}
			m.UserType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00002000)
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceFlag", wireType)
			}
			m.SourceFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00004000)
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendAddTime", wireType)
			}
			m.FriendAddTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendAddTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00008000)
		case 18:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsStarMarked", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsStarMarked = bool(v != 0)
			hasFields[0] |= uint64(0x00010000)
		case 19:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsBanked", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsBanked = bool(v != 0)
			hasFields[0] |= uint64(0x00020000)
		case 20:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00040000)
		case 21:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateOrDeleteSeq", wireType)
			}
			m.CreateOrDeleteSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateOrDeleteSeq |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 22:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NobilityLevel", wireType)
			}
			m.NobilityLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NobilityLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 23:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DynamicFaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DynamicFaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("nick")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signature")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info_update_seq")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("face_md5")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_delete")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cover_md5")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account_alias")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("nick_remark")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_type")
	}
	if hasFields[0]&uint64(0x00004000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_flag")
	}
	if hasFields[0]&uint64(0x00008000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_add_time")
	}
	if hasFields[0]&uint64(0x00010000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_star_marked")
	}
	if hasFields[0]&uint64(0x00020000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_banked")
	}
	if hasFields[0]&uint64(0x00040000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StFriendVerify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stFriendVerify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stFriendVerify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nick", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nick = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Signature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Signature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoUpdateSeq", wireType)
			}
			m.InfoUpdateSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InfoUpdateSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyMsgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyMsgList = append(m.VerifyMsgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserType", wireType)
			}
			m.UserType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceFlag", wireType)
			}
			m.SourceFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccountAlias", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccountAlias = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CoverMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CoverMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00002000)
		case 16:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendSrcType", wireType)
			}
			m.FriendSrcType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendSrcType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("phone")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("nick")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("signature")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info_update_seq")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("face_md5")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_type")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_flag")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account_alias")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sex")
	}
	if hasFields[0]&uint64(0x00002000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cover_md5")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StFriendGroup) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stFriendGroup: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stFriendGroup: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDeleted", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDeleted = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoUpdateSeq", wireType)
			}
			m.InfoUpdateSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InfoUpdateSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShow = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemListLastUpdateSeq", wireType)
			}
			m.MemListLastUpdateSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemListLastUpdateSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_deleted")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info_update_seq")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_show")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StPublicAccountSettings) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stPublicAccountSettings: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stPublicAccountSettings: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MessageNotification", wireType)
			}
			m.MessageNotification = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MessageNotification |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StPublicAccount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stPublicAccount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stPublicAccount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicId", wireType)
			}
			m.PublicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoUpdateSeq", wireType)
			}
			m.InfoUpdateSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InfoUpdateSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FaceMd5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FaceMd5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Active", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Active = bool(v != 0)
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ShowProfile", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ShowProfile = bool(v != 0)
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDelete", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDelete = bool(v != 0)
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Settings", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Settings == nil {
				m.Settings = &StPublicAccountSettings{}
			}
			if err := m.Settings.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Intro", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Intro = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UpdateTime", wireType)
			}
			m.UpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info_update_seq")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("face_md5")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("active")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("show_profile")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_delete")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFriendVerifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFriendVerifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFriendVerifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Verify", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Verify == nil {
				m.Verify = &StFriendVerify{}
			}
			if err := m.Verify.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("verify")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFriendVerifyRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFriendVerifyRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFriendVerifyRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FriendInfo == nil {
				m.FriendInfo = &StFriend{}
			}
			if err := m.FriendInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddFriendRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddFriendRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddFriendRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFriendInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFriendInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFriendInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FriendInfo == nil {
				m.FriendInfo = &StFriend{}
			}
			if err := m.FriendInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFriendInfoRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFriendInfoRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFriendInfoRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFriendVerifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFriendVerifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFriendVerifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Verify", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Verify == nil {
				m.Verify = &StFriendVerify{}
			}
			if err := m.Verify.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("verify")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFriendVerifyRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFriendVerifyRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFriendVerifyRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFriendVerifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFriendVerifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFriendVerifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFriendVerifyRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFriendVerifyRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFriendVerifyRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelFriendRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelFriendRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelFriendRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOneFriendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOneFriendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOneFriendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendUid", wireType)
			}
			m.FriendUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FriendUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("friend_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOneFriendRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOneFriendRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOneFriendRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FriendInfo == nil {
				m.FriendInfo = &StFriend{}
			}
			if err := m.FriendInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.VerifyInfo == nil {
				m.VerifyInfo = &StFriendVerify{}
			}
			if err := m.VerifyInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllFriendInfoOrVerifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllFriendInfoOrVerifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllFriendInfoOrVerifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetFriendInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetFriendInfo = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GetVerifyInfo", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GetVerifyInfo = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOnlyValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOnlyValid = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllFriendInfoOrVerifyRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllFriendInfoOrVerifyRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllFriendInfoOrVerifyRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FriendInfoList = append(m.FriendInfoList, &StFriend{})
			if err := m.FriendInfoList[len(m.FriendInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyInfoList = append(m.VerifyInfoList, &StFriendVerify{})
			if err := m.VerifyInfoList[len(m.VerifyInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddBlackListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddBlackListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddBlackListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelBlackListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelBlackListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelBlackListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBlackListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBlackListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBlackListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBlackListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBlackListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBlackListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowFriend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthFriend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowFriend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFriendDataBySeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFriendDataBySeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFriendDataBySeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFriendDataBySeqResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFriendDataBySeqResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFriendDataBySeqResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FriendList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FriendList = append(m.FriendList, &StFriend{})
			if err := m.FriendList[len(m.FriendList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyList = append(m.VerifyList, &StFriendVerify{})
			if err := m.VerifyList[len(m.VerifyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Group", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Group = append(m.Group, &StFriendGroup{})
			if err := m.Group[len(m.Group)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicAccountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PublicAccountList = append(m.PublicAccountList, &StPublicAccount{})
			if err := m.PublicAccountList[len(m.PublicAccountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFriendGroupReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFriendGroupReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFriendGroupReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GroupInfo == nil {
				m.GroupInfo = &StFriendGroup{}
			}
			if err := m.GroupInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateFriendGroupResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateFriendGroupResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateFriendGroupResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFriendGroupReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFriendGroupReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFriendGroupReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetFriendGroupResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetFriendGroupResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetFriendGroupResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GroupInfo == nil {
				m.GroupInfo = &StFriendGroup{}
			}
			if err := m.GroupInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllMyGroupReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllMyGroupReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllMyGroupReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllMyGroupResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllMyGroupResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllMyGroupResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupList = append(m.GroupList, &StFriendGroup{})
			if err := m.GroupList[len(m.GroupList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePublicAccountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePublicAccountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePublicAccountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PublicDetail == nil {
				m.PublicDetail = &StPublicAccount{}
			}
			if err := m.PublicDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_detail")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdatePublicAccountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdatePublicAccountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdatePublicAccountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteAllPublicAccountsReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteAllPublicAccountsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteAllPublicAccountsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteAllPublicAccountsResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteAllPublicAccountsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteAllPublicAccountsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Deleted", wireType)
			}
			m.Deleted = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Deleted |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("deleted")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPublicAccountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPublicAccountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPublicAccountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicId", wireType)
			}
			m.PublicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("public_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPublicAccountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPublicAccountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPublicAccountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicDetail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.PublicDetail == nil {
				m.PublicDetail = &StPublicAccount{}
			}
			if err := m.PublicDetail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserSubscribingPublicAccountsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserSubscribingPublicAccountsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserSubscribingPublicAccountsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncludeDeleted", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IncludeDeleted = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("include_deleted")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserSubscribingPublicAccountsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserSubscribingPublicAccountsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserSubscribingPublicAccountsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicAccountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFriend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PublicAccountList = append(m.PublicAccountList, &StPublicAccount{})
			if err := m.PublicAccountList[len(m.PublicAccountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFriend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthFriend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipFriend(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFriend
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFriend
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthFriend
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowFriend
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipFriend(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthFriend = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFriend   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/friendsvr/friend.proto", fileDescriptorFriend) }

var fileDescriptorFriend = []byte{
	// 2243 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0xcd, 0x6f, 0x1b, 0x5b,
	0x15, 0xef, 0xc4, 0x8e, 0x63, 0x1f, 0x7f, 0xc4, 0xb9, 0x49, 0x93, 0xa9, 0x9b, 0x26, 0x66, 0xda,
	0xd7, 0x97, 0x7e, 0x24, 0xe5, 0x55, 0xad, 0x1e, 0xca, 0x0b, 0x11, 0x0e, 0xa5, 0x51, 0x69, 0xfa,
	0x81, 0x93, 0x76, 0x85, 0x34, 0x4c, 0x3c, 0x37, 0xee, 0x55, 0xc6, 0x9e, 0xc9, 0xdc, 0x3b, 0x79,
	0xcf, 0x82, 0xc7, 0x87, 0x04, 0x12, 0x82, 0x0d, 0x20, 0xd8, 0x20, 0x36, 0x48, 0x5d, 0xc3, 0x82,
	0x05, 0x6b, 0x76, 0x6f, 0x85, 0xf8, 0x0b, 0x10, 0x2a, 0x9b, 0xfe, 0x0d, 0xac, 0xd0, 0xbd, 0xf3,
	0x75, 0xc7, 0x9e, 0xb1, 0x53, 0x52, 0xe9, 0x6d, 0xd8, 0xc5, 0xe7, 0x9c, 0x7b, 0x7e, 0xbf, 0x7b,
	0xbe, 0xe6, 0xcc, 0x04, 0x1a, 0xd4, 0xed, 0xdc, 0x39, 0x72, 0x09, 0xee, 0x9b, 0xf4, 0xd4, 0x0d,
	0xfe, 0xda, 0x70, 0x5c, 0x9b, 0xd9, 0xa8, 0xf0, 0x50, 0xfc, 0x6a, 0x5c, 0xeb, 0xd8, 0xbd, 0x9e,
	0xdd, 0xbf, 0xc3, 0xac, 0x53, 0x87, 0x74, 0x8e, 0x2d, 0x7c, 0x87, 0x1e, 0x1f, 0x7a, 0xc4, 0x62,
	0xa4, 0xcf, 0x06, 0x0e, 0xf6, 0xad, 0xb5, 0x3f, 0xcc, 0x40, 0x91, 0x32, 0xff, 0x08, 0x5a, 0x84,
	0x9c, 0x47, 0x4c, 0x55, 0x69, 0x4e, 0xad, 0x55, 0x77, 0xf2, 0x5f, 0xfc, 0x73, 0xf5, 0x42, 0x9b,
	0x0b, 0xd0, 0x87, 0x50, 0xf1, 0x28, 0x76, 0x75, 0xa3, 0xd3, 0xb1, 0xbd, 0x3e, 0x53, 0xa7, 0x9a,
	0x53, 0x6b, 0xa5, 0xc0, 0xa0, 0xcc, 0x35, 0x2d, 0x5f, 0x81, 0x1a, 0x30, 0xed, 0xbc, 0xb2, 0xfb,
	0x58, 0xcd, 0x49, 0x16, 0xbe, 0x08, 0xa9, 0x90, 0xef, 0x93, 0xce, 0xb1, 0x9a, 0x97, 0x54, 0x42,
	0x82, 0x34, 0x28, 0x51, 0xd2, 0xed, 0x1b, 0xcc, 0x73, 0xb1, 0x3a, 0x2d, 0xa9, 0x63, 0x31, 0xba,
	0x0d, 0xb3, 0xa4, 0x7f, 0x64, 0xeb, 0x9e, 0x63, 0x1a, 0x0c, 0xeb, 0x14, 0x9f, 0xa8, 0x05, 0x89,
	0x66, 0x95, 0x2b, 0x5f, 0x08, 0xdd, 0x3e, 0x3e, 0x41, 0xcb, 0x50, 0xa0, 0xcc, 0x60, 0x1e, 0x55,
	0x67, 0x24, 0xa3, 0x40, 0x86, 0x56, 0xa1, 0x78, 0x64, 0x74, 0xb0, 0xde, 0x33, 0xef, 0xab, 0x45,
	0x09, 0x6e, 0x86, 0x4b, 0x9f, 0x98, 0xf7, 0x39, 0x98, 0x1f, 0x52, 0x9d, 0xba, 0x1d, 0x9d, 0x47,
	0x4b, 0x2d, 0x35, 0x95, 0x18, 0xcc, 0x57, 0xee, 0xbb, 0x9d, 0x83, 0x81, 0x83, 0xd1, 0x57, 0xa0,
	0x44, 0xa8, 0x6e, 0x62, 0x0b, 0x33, 0xac, 0x42, 0x73, 0x6a, 0xad, 0x18, 0xd8, 0x15, 0x09, 0x7d,
	0x20, 0xa4, 0xdc, 0xa4, 0x63, 0x9f, 0x62, 0x57, 0x40, 0x96, 0x25, 0xc8, 0xa2, 0x10, 0x73, 0xcc,
	0x1b, 0x50, 0x0d, 0xc2, 0xab, 0x1b, 0x16, 0x31, 0xa8, 0x5a, 0x91, 0xcc, 0x2a, 0x81, 0xaa, 0xc5,
	0x35, 0xe8, 0x03, 0x28, 0xf3, 0xb8, 0xe9, 0x2e, 0xee, 0x19, 0xee, 0xb1, 0x5a, 0x95, 0x0c, 0x81,
	0x2b, 0xda, 0x42, 0xce, 0xb3, 0x49, 0xf1, 0x67, 0x6a, 0x4d, 0xce, 0x26, 0xc5, 0x9f, 0x71, 0x32,
	0x22, 0x9b, 0xe2, 0x5e, 0xb3, 0x92, 0xb6, 0xc8, 0xc5, 0xe2, 0x4a, 0x1f, 0x40, 0x99, 0xda, 0x9e,
	0xdb, 0xc1, 0xfa, 0x91, 0x65, 0x74, 0xd5, 0xba, 0x64, 0x04, 0xbe, 0xe2, 0xa1, 0x65, 0x74, 0xa5,
	0x38, 0x19, 0xa6, 0xa9, 0x33, 0xd2, 0xc3, 0xea, 0x9c, 0x9c, 0x14, 0x5f, 0xd9, 0x32, 0xcd, 0x03,
	0xd2, 0xc3, 0xe8, 0x26, 0xd4, 0x08, 0xd5, 0x29, 0x33, 0x5c, 0x9d, 0xf3, 0xc3, 0xa6, 0x8a, 0xa4,
	0x60, 0x55, 0x08, 0xdd, 0x67, 0x86, 0xfb, 0x44, 0x68, 0x82, 0x98, 0x1e, 0x1a, 0x7d, 0x6e, 0x36,
	0x9f, 0x8c, 0xe9, 0x8e, 0x90, 0xa2, 0x0d, 0xa8, 0x5b, 0x06, 0x65, 0x61, 0x45, 0x08, 0xf4, 0x05,
	0x09, 0xbd, 0xc6, 0xb5, 0x7e, 0x49, 0x08, 0xf8, 0xfb, 0xb0, 0xd0, 0x71, 0x31, 0x37, 0xb5, 0xdd,
	0x20, 0x5b, 0xa2, 0x8c, 0x2e, 0x36, 0x95, 0xb5, 0x7c, 0x70, 0x66, 0xce, 0xb7, 0x78, 0xe6, 0xfa,
	0x79, 0xe3, 0xa5, 0x74, 0x0b, 0x6a, 0x7d, 0xfb, 0x90, 0x58, 0x84, 0x0d, 0x74, 0x0b, 0x9f, 0x62,
	0x4b, 0x5d, 0x94, 0x4b, 0x21, 0xd4, 0xed, 0x71, 0x15, 0xe7, 0x64, 0x0e, 0xfa, 0x46, 0x8f, 0x74,
	0xf4, 0xa8, 0xc2, 0x96, 0x9a, 0x4a, 0x94, 0x9e, 0x5a, 0xa0, 0x7d, 0xe8, 0x17, 0x9a, 0xb6, 0x0a,
	0x85, 0xfd, 0x83, 0xd6, 0xc1, 0x8b, 0x7d, 0x04, 0x50, 0x78, 0xfa, 0xac, 0xfd, 0xa4, 0xb5, 0x57,
	0xbf, 0x80, 0x4a, 0x30, 0xbd, 0xb3, 0xd7, 0xfa, 0xe6, 0xe3, 0xba, 0xa2, 0xfd, 0x3d, 0x0f, 0xb5,
	0xb0, 0x3d, 0x5f, 0x62, 0x97, 0x1c, 0x0d, 0xfe, 0xdf, 0xa4, 0x41, 0x93, 0x5e, 0x87, 0xd9, 0x53,
	0x11, 0x11, 0xbd, 0x47, 0xbb, 0xba, 0x45, 0x28, 0x53, 0x4b, 0xcd, 0xdc, 0x5a, 0xa9, 0x5d, 0xf5,
	0xc5, 0x4f, 0x68, 0x77, 0x8f, 0x50, 0x96, 0x5a, 0x27, 0x30, 0xa6, 0x4e, 0x12, 0xed, 0x51, 0x3e,
	0x4b, 0x7b, 0x54, 0x32, 0xda, 0x63, 0xa4, 0xa5, 0xab, 0x99, 0x2d, 0x3d, 0xa6, 0x57, 0xe3, 0xc1,
	0x31, 0x9b, 0x3a, 0x38, 0x52, 0x86, 0x55, 0x3d, 0x73, 0x58, 0x69, 0x7f, 0x9a, 0x82, 0x6a, 0x58,
	0x50, 0xbb, 0xae, 0xed, 0x39, 0x3c, 0xd0, 0x5d, 0xfe, 0x87, 0x3e, 0x54, 0x54, 0x33, 0x42, 0xfa,
	0xc8, 0x94, 0xf2, 0x34, 0x95, 0x92, 0xa7, 0xab, 0x00, 0xd1, 0xf4, 0x33, 0x45, 0x49, 0x85, 0xad,
	0x5a, 0x0a, 0xc7, 0x9f, 0x99, 0x56, 0x18, 0xf9, 0xec, 0xc2, 0xb8, 0x02, 0x33, 0x7c, 0x50, 0xbc,
	0xb2, 0x3f, 0x15, 0x85, 0x16, 0xfa, 0x2b, 0x10, 0xba, 0xff, 0xca, 0xfe, 0x14, 0x6d, 0x81, 0xda,
	0xc3, 0x3d, 0x91, 0x71, 0x5d, 0xce, 0xac, 0x5f, 0x6e, 0xf1, 0xcd, 0x17, 0x7a, 0xb8, 0xc7, 0x2b,
	0x60, 0x2f, 0xca, 0x2f, 0x77, 0xae, 0x42, 0x5e, 0xc4, 0x68, 0x46, 0xb2, 0x14, 0x12, 0x6d, 0x21,
	0xad, 0x19, 0xb5, 0x36, 0x2c, 0x51, 0xf6, 0xdc, 0x3b, 0xb4, 0x48, 0x27, 0x68, 0xa0, 0x7d, 0xcc,
	0x18, 0xe9, 0x77, 0x29, 0xfa, 0x18, 0x16, 0x7a, 0x98, 0x52, 0xa3, 0x8b, 0xf5, 0xbe, 0xcd, 0xc8,
	0x11, 0xe9, 0x18, 0x8c, 0xd8, 0x7d, 0x55, 0x91, 0x5c, 0xcf, 0x07, 0x16, 0x4f, 0x25, 0x03, 0xed,
	0x6f, 0x39, 0x98, 0x1d, 0x72, 0xca, 0x33, 0xed, 0x08, 0xc1, 0x70, 0x1e, 0x8a, 0xbe, 0xf8, 0x91,
	0x89, 0x56, 0x60, 0x26, 0xad, 0xb9, 0x43, 0xa1, 0x68, 0x5e, 0xa3, 0x97, 0xec, 0x6b, 0x21, 0x89,
	0x2e, 0x2d, 0x07, 0x5d, 0x48, 0xd2, 0x32, 0x33, 0x9d, 0x9d, 0x19, 0xb9, 0x29, 0x0b, 0x69, 0x4d,
	0xb9, 0x0c, 0x05, 0xa3, 0xc3, 0xc8, 0x29, 0x16, 0x3d, 0x1d, 0x65, 0xce, 0x97, 0xf1, 0x11, 0xc5,
	0xb3, 0xaa, 0x3b, 0xae, 0x7d, 0x44, 0x2c, 0x2c, 0xfa, 0x3a, 0xb4, 0x29, 0x73, 0xcd, 0x73, 0x5f,
	0x91, 0x7c, 0xa4, 0x96, 0x52, 0x1f, 0xa9, 0x9f, 0x40, 0x91, 0x06, 0x89, 0x50, 0xa1, 0xa9, 0xac,
	0x95, 0xef, 0xae, 0x6e, 0xf8, 0x65, 0xbd, 0x91, 0x91, 0xaf, 0x76, 0x74, 0x80, 0x8f, 0x40, 0xd2,
	0x67, 0xae, 0xad, 0x96, 0xa5, 0xe1, 0xec, 0x8b, 0x78, 0x73, 0xcb, 0xa3, 0xa2, 0x22, 0x25, 0x13,
	0xbc, 0x68, 0x4c, 0x68, 0x3f, 0x51, 0x00, 0xb5, 0x4c, 0x53, 0x1e, 0xcd, 0x6d, 0x7c, 0x92, 0x39,
	0x9d, 0xaf, 0x02, 0x04, 0x5d, 0xca, 0xd5, 0x72, 0x23, 0x95, 0x7c, 0xf9, 0x0b, 0xc2, 0x1f, 0x69,
	0x05, 0x7f, 0x76, 0x89, 0x14, 0x96, 0xef, 0x2e, 0xc6, 0x37, 0x4a, 0xe0, 0x04, 0x56, 0xda, 0xed,
	0x51, 0x0a, 0xd4, 0xe1, 0x14, 0x5c, 0xcc, 0x04, 0x85, 0xe9, 0x90, 0x82, 0x8b, 0x99, 0xf6, 0x43,
	0xa8, 0x44, 0xd6, 0xe7, 0xa6, 0xfa, 0x11, 0x94, 0x03, 0x23, 0x5e, 0x21, 0x01, 0xdf, 0xfa, 0x30,
	0xdf, 0x76, 0xe0, 0xe9, 0x51, 0xff, 0xc8, 0xd6, 0xae, 0xcb, 0xf8, 0x63, 0x78, 0xfe, 0x4c, 0x81,
	0x79, 0xbf, 0xe4, 0x1e, 0x46, 0x87, 0xbf, 0x0c, 0xbe, 0xeb, 0x29, 0x34, 0xc6, 0xd0, 0xfe, 0xa9,
	0x02, 0x17, 0x65, 0xfb, 0x2f, 0xa9, 0x26, 0xee, 0xa4, 0xb2, 0x18, 0xc3, 0xfb, 0x3b, 0x80, 0x1e,
	0x60, 0xeb, 0x7d, 0x72, 0xe6, 0x75, 0x39, 0xec, 0x72, 0x0c, 0x81, 0xc7, 0x50, 0x89, 0xac, 0xcf,
	0x0d, 0x7d, 0x5d, 0x76, 0x36, 0x06, 0xf4, 0x29, 0xcc, 0xee, 0x62, 0xf6, 0xac, 0x8f, 0xdf, 0x13,
	0xee, 0xef, 0x94, 0x21, 0x87, 0x49, 0xec, 0xaa, 0x84, 0x3d, 0x5c, 0x8b, 0x53, 0x62, 0x7a, 0x8d,
	0xad, 0x45, 0xf4, 0x31, 0x94, 0x83, 0x65, 0x27, 0x28, 0x5f, 0x65, 0x4c, 0x29, 0x80, 0x6f, 0x2a,
	0x8a, 0xf8, 0xaf, 0x0a, 0x5c, 0xde, 0xc5, 0xac, 0x65, 0x59, 0x71, 0x15, 0x3f, 0x73, 0x27, 0xe7,
	0xf9, 0x26, 0x54, 0xbb, 0x98, 0xc5, 0x67, 0x04, 0xcb, 0x70, 0x0a, 0x27, 0x55, 0x81, 0xed, 0xcb,
	0x08, 0x54, 0xd0, 0x93, 0x6d, 0x63, 0x15, 0x5a, 0x83, 0x2a, 0xa1, 0xba, 0xdd, 0xb7, 0x06, 0xfa,
	0xa9, 0x61, 0x11, 0x53, 0xcd, 0x4b, 0xb6, 0x65, 0x42, 0x9f, 0xf5, 0xad, 0xc1, 0x4b, 0xae, 0xd0,
	0xfe, 0x32, 0x8e, 0x79, 0x76, 0x66, 0xd1, 0x26, 0xd4, 0xa5, 0xe8, 0xfa, 0x8b, 0xe1, 0x54, 0x33,
	0x97, 0x1a, 0xe2, 0x5a, 0x1c, 0x62, 0xb1, 0x2b, 0x7e, 0x03, 0xea, 0x52, 0x98, 0xfd, 0xb3, 0x39,
	0x71, 0x36, 0x2b, 0xd6, 0xb5, 0x38, 0xd6, 0xdc, 0x03, 0xaf, 0xab, 0x96, 0x69, 0xee, 0x58, 0x46,
	0xe7, 0x98, 0xff, 0x9e, 0x50, 0x57, 0xcc, 0x70, 0xbb, 0x98, 0x8d, 0xd6, 0x95, 0x2f, 0xe7, 0x75,
	0xf5, 0x14, 0x66, 0x1f, 0x60, 0xeb, 0xfd, 0xf9, 0xbb, 0x21, 0xca, 0xf4, 0x2c, 0xfe, 0xb4, 0x75,
	0xa8, 0x27, 0x4d, 0xa9, 0x83, 0x2e, 0x41, 0xd1, 0x23, 0x66, 0x1c, 0xd4, 0x6a, 0x7b, 0xc6, 0x23,
	0xa6, 0xb8, 0xf9, 0x3d, 0xb8, 0xb8, 0x1b, 0x96, 0xc5, 0x03, 0x83, 0x19, 0x3b, 0x83, 0x7d, 0x7c,
	0xc2, 0xfd, 0x5f, 0x86, 0x02, 0xc5, 0x27, 0xc3, 0x6b, 0xcd, 0x34, 0xc5, 0x27, 0x8f, 0x4c, 0xed,
	0x3f, 0x0a, 0x2c, 0xa6, 0x1d, 0xa3, 0x8e, 0xd4, 0x26, 0x02, 0x4e, 0xc9, 0xc8, 0x61, 0xd0, 0x26,
	0x22, 0x7f, 0x71, 0x9b, 0x48, 0x69, 0x9f, 0xd0, 0x26, 0xe2, 0xe0, 0x2d, 0x98, 0x16, 0xeb, 0x6e,
	0x90, 0xed, 0x8b, 0xc3, 0x47, 0xc4, 0xaa, 0xdc, 0xf6, 0x6d, 0xd0, 0x2e, 0xcc, 0x07, 0xab, 0x5a,
	0xb8, 0xde, 0x0b, 0xb4, 0xbc, 0x38, 0xba, 0x94, 0xb1, 0x85, 0xb4, 0xe7, 0x1c, 0xf9, 0xa7, 0x08,
	0xd9, 0x1e, 0x2c, 0xc8, 0xb3, 0xda, 0x07, 0xc1, 0x27, 0xe8, 0x1e, 0x40, 0xb0, 0x92, 0xf3, 0x6e,
	0x52, 0xc4, 0xdc, 0xcf, 0xa0, 0x54, 0xf2, 0xb7, 0x74, 0xde, 0xea, 0x4b, 0xc9, 0xc9, 0x1f, 0x78,
	0xa3, 0x8e, 0x76, 0x0f, 0xe6, 0xa2, 0x10, 0x47, 0x18, 0x93, 0xd6, 0x7e, 0xed, 0xdb, 0x80, 0x86,
	0x4f, 0x51, 0xe7, 0x7f, 0xa4, 0x86, 0x44, 0x29, 0xb5, 0x2c, 0xeb, 0xc9, 0x20, 0x24, 0xa0, 0x3d,
	0x12, 0xac, 0x64, 0x99, 0xec, 0x5e, 0x4a, 0xf9, 0x58, 0xf7, 0x22, 0x8e, 0x2f, 0x61, 0xd1, 0xbf,
	0x79, 0x32, 0xe2, 0xf8, 0x04, 0x6d, 0x41, 0x35, 0x48, 0x95, 0x89, 0x99, 0x41, 0xac, 0x80, 0x71,
	0x66, 0x92, 0x2a, 0xbe, 0xf5, 0x03, 0x61, 0xac, 0x5d, 0x82, 0xa5, 0x54, 0xbf, 0xd4, 0xd1, 0x96,
	0xa1, 0xe1, 0x2f, 0xa2, 0x2d, 0xcb, 0x4a, 0x68, 0x29, 0xbf, 0xdb, 0xd7, 0xe1, 0x72, 0xa6, 0x96,
	0x3a, 0x7c, 0x91, 0x0f, 0x5f, 0x98, 0x38, 0x9f, 0xf0, 0xeb, 0x43, 0x28, 0xd4, 0xbe, 0x06, 0xf3,
	0xbb, 0x98, 0x8d, 0x5c, 0x66, 0xf2, 0x2b, 0x82, 0x76, 0x00, 0x0b, 0xa3, 0x27, 0xa9, 0x93, 0x16,
	0x07, 0xe5, 0xec, 0x71, 0x38, 0x80, 0xab, 0xbb, 0x98, 0xbd, 0xa0, 0xd8, 0xdd, 0xf7, 0x0e, 0x69,
	0xc7, 0x25, 0x87, 0xa4, 0xdf, 0x1d, 0xb9, 0x35, 0x5a, 0xe7, 0xef, 0x12, 0x1d, 0xcb, 0x33, 0xb1,
	0x2e, 0x5f, 0x2f, 0x9c, 0xee, 0xb5, 0x40, 0x19, 0xbc, 0x14, 0x6a, 0x36, 0x5c, 0x9b, 0xec, 0x95,
	0x66, 0xb6, 0x9b, 0xf2, 0xae, 0xed, 0x76, 0xf7, 0x97, 0xf3, 0x10, 0x7c, 0x1c, 0x45, 0xbf, 0x56,
	0xc4, 0x9c, 0x4e, 0x7c, 0x58, 0x69, 0x84, 0xae, 0x46, 0xd7, 0xfa, 0x46, 0xa6, 0x8e, 0x3a, 0xda,
	0xc3, 0x1f, 0xbf, 0x7e, 0x9b, 0x53, 0x7e, 0xf1, 0xfa, 0x6d, 0xae, 0xe0, 0x6d, 0xb2, 0x4d, 0x63,
	0xf3, 0x37, 0xaf, 0xdf, 0xe6, 0x3e, 0x5a, 0xf7, 0x9a, 0x5b, 0x1e, 0x31, 0xb7, 0x9b, 0xeb, 0xac,
	0xb9, 0x15, 0xcc, 0xe5, 0x78, 0x8d, 0xd8, 0x6e, 0xae, 0x1b, 0xcd, 0xad, 0xf0, 0x3e, 0xfc, 0x1d,
	0x6d, 0x1b, 0xfd, 0x08, 0x4a, 0x91, 0x77, 0xb4, 0x30, 0x02, 0xc8, 0x69, 0xa4, 0x48, 0x43, 0x02,
	0x53, 0xe7, 0x27, 0xf0, 0x5b, 0x05, 0xea, 0xc3, 0x1b, 0x2f, 0xba, 0x1c, 0x42, 0xa6, 0xac, 0xe4,
	0x8d, 0x6c, 0x65, 0x48, 0x2b, 0x77, 0x7e, 0x5a, 0xbf, 0x57, 0x00, 0x8d, 0xae, 0xb4, 0xe8, 0x4a,
	0x1a, 0x76, 0x9c, 0xb1, 0x71, 0xea, 0x90, 0x5c, 0xfe, 0xfc, 0xe4, 0x3e, 0x17, 0xcf, 0xe7, 0xf4,
	0x3a, 0x1a, 0x5d, 0xab, 0x1b, 0x99, 0x3a, 0xea, 0x68, 0x77, 0x39, 0xa5, 0x69, 0x4e, 0x29, 0xcf,
	0x29, 0x71, 0x42, 0xab, 0x13, 0x08, 0xa1, 0x63, 0x28, 0x45, 0x9e, 0xe2, 0x9a, 0x91, 0xd7, 0xe9,
	0x46, 0x8a, 0x34, 0x04, 0x2b, 0xbc, 0x1b, 0x18, 0x83, 0x8a, 0xbc, 0xe2, 0xa2, 0xa8, 0xf7, 0x86,
	0x36, 0xe9, 0x46, 0xba, 0x22, 0x44, 0x2d, 0xbe, 0x1b, 0xea, 0x1f, 0x15, 0x50, 0xb3, 0xf6, 0x40,
	0x74, 0x55, 0x42, 0xca, 0xda, 0x71, 0x1b, 0x93, 0x8d, 0xa8, 0xa3, 0x7d, 0xc2, 0xa9, 0x95, 0x38,
	0xb5, 0xa2, 0xb7, 0x49, 0x36, 0x07, 0x9b, 0x8e, 0xa0, 0x77, 0x2d, 0xa6, 0x47, 0x9a, 0x5b, 0x5d,
	0xcc, 0xf8, 0xb9, 0xed, 0xe6, 0xfa, 0x40, 0xfc, 0xf0, 0x8f, 0x6f, 0xa3, 0x1f, 0x48, 0xcf, 0xca,
	0x68, 0x89, 0x89, 0x2b, 0x34, 0x75, 0x2f, 0x6a, 0xac, 0x8c, 0x53, 0x53, 0x47, 0xfb, 0x90, 0x33,
	0x82, 0x20, 0x58, 0x54, 0xb0, 0x59, 0x88, 0xd9, 0xd0, 0xe6, 0x96, 0xbf, 0x55, 0x6d, 0xa3, 0xef,
	0xc3, 0xdc, 0xc8, 0x83, 0x1f, 0x2d, 0xa7, 0xd5, 0x7f, 0xf8, 0xf0, 0x4d, 0xef, 0x8e, 0x78, 0x63,
	0xb8, 0xc1, 0xa1, 0xcb, 0x01, 0x74, 0x57, 0x40, 0x2f, 0xc6, 0xd0, 0xdd, 0xe6, 0x56, 0xb8, 0x3a,
	0x6c, 0x23, 0x17, 0x6a, 0xc9, 0x35, 0x01, 0x5d, 0x1a, 0xb9, 0x57, 0x04, 0xdb, 0xc8, 0x52, 0x85,
	0x98, 0x95, 0x33, 0x61, 0x7e, 0x0f, 0xaa, 0x89, 0xd5, 0x01, 0xa9, 0xc9, 0x0c, 0xc7, 0x5b, 0x46,
	0xe3, 0x52, 0x86, 0x86, 0x3a, 0x5a, 0x83, 0x03, 0x56, 0x39, 0xe0, 0x94, 0x27, 0xe0, 0x4a, 0x11,
	0x1c, 0xea, 0x86, 0xef, 0xfe, 0xc9, 0x8f, 0x74, 0x2b, 0xc9, 0xb0, 0x0d, 0x3f, 0xa1, 0x1b, 0xab,
	0x63, 0xf5, 0xd4, 0xd1, 0x66, 0x39, 0x66, 0x8d, 0x63, 0x5e, 0xe0, 0x88, 0x17, 0xd0, 0xe7, 0xb0,
	0x94, 0xb1, 0x29, 0x20, 0x4d, 0x6a, 0xdc, 0x8c, 0x45, 0x23, 0x2e, 0xed, 0x31, 0xeb, 0x86, 0x76,
	0x89, 0x83, 0xce, 0x4a, 0x17, 0x2d, 0x86, 0x17, 0x45, 0x03, 0xb1, 0x98, 0x25, 0x2f, 0x79, 0x59,
	0x0a, 0xd9, 0xc8, 0x0d, 0x97, 0xb3, 0x95, 0xd4, 0xd1, 0x6e, 0x72, 0xa4, 0x7a, 0x90, 0x43, 0xbf,
	0x81, 0x96, 0xe2, 0x1c, 0x3a, 0xcd, 0xad, 0x68, 0x7f, 0xd9, 0x46, 0x7f, 0x56, 0xa0, 0x39, 0xe9,
	0xf9, 0x8f, 0x6e, 0x49, 0x70, 0x93, 0xf6, 0x8f, 0xc6, 0xed, 0xb3, 0x1b, 0x53, 0x47, 0xfb, 0x2a,
	0xe7, 0x3a, 0x17, 0x70, 0x25, 0x82, 0xeb, 0x95, 0x44, 0xb3, 0x0f, 0xed, 0x32, 0xbc, 0xd4, 0x2b,
	0xf2, 0xbb, 0x5d, 0x3c, 0xff, 0x86, 0xde, 0xf8, 0x1a, 0xcb, 0x1b, 0xd1, 0x3f, 0x5d, 0x37, 0xf6,
	0x1f, 0xef, 0xf8, 0xff, 0x74, 0xfd, 0x56, 0xcf, 0x61, 0x03, 0xfd, 0xf9, 0x8e, 0x76, 0x8b, 0x03,
	0xa3, 0xc4, 0x10, 0x54, 0xd3, 0x86, 0xa0, 0x48, 0xd0, 0x77, 0xc5, 0xcc, 0x4d, 0xc1, 0x1c, 0x7a,
	0x8b, 0x6b, 0xa8, 0xe9, 0x8a, 0x30, 0xfd, 0xf3, 0xa9, 0xe9, 0x77, 0xc5, 0xd7, 0x92, 0x14, 0xef,
	0x43, 0xef, 0x9c, 0x67, 0xb9, 0xd1, 0xc2, 0xd9, 0x6e, 0xd4, 0x28, 0xfc, 0xfc, 0xf5, 0xdb, 0xdc,
	0xeb, 0x93, 0x9d, 0xfa, 0x17, 0x6f, 0x56, 0x94, 0x7f, 0xbc, 0x59, 0x51, 0xfe, 0xf5, 0x66, 0x45,
	0xf9, 0xd5, 0xbf, 0x57, 0x2e, 0xfc, 0x37, 0x00, 0x00, 0xff, 0xff, 0x8b, 0x11, 0xad, 0x85, 0xd6,
	0x1e, 0x00, 0x00,
}
