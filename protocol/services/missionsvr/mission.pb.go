// Code generated by protoc-gen-gogo.
// source: src/missionsvr/mission.proto
// DO NOT EDIT!

/*
	Package Mission is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/missionsvr/mission.proto

	It has these top-level messages:
		StUserMission
		GetUserMissionListReq
		GetUserMissionListResp
		GetUserMissionByIdentifierReq
		GetUserMissionByIdentifierResp
		GetUserMissionsByIdentifierListReq
		GetUserMissionsByIdentifierListResp
		IncreaseUserMissionFinishCountReq
		IncreaseUserMissionFinishCountResp
		IncreaseUserMissionToFinishCountReq
		IncreaseUserMissionToFinishCountResp
		UpdateUserMissionStatusByIdentifierReq
		UpdateUserMissionStatusByIdentifierResp
		UpdateUserMilestoneReq
		UpdateUserMilestoneResp
		GetUserMilestoneReq
		GetUserMilestoneResp
		GetLatestUserMissionReq
		GetLatestUserMissionResp
		UpdateUserMissionReq
		UpdateUserMissionResp
		GetMissionTotalFinishCountReq
		GetMissionTotalFinishCountResp
		GetMissionTotalCollectCountReq
		GetMissionTotalCollectCountResp
		RecordUserContinuousDayOperReq
		GetUserContinuousDayOperReq
		GetUserContinuousDayOperResp
		ClearUserContinuousDayOperReq
		RecordUserRechargeReq
		GetUserRechargeReq
		GetUserRechargeResp
		ClearUserRechargeReq
*/
package Mission

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 用户任务状态
type USER_MISSION_STATUS int32

const (
	USER_MISSION_STATUS_IN_PROGRESS USER_MISSION_STATUS = 1
	USER_MISSION_STATUS_FINISHED    USER_MISSION_STATUS = 2
	USER_MISSION_STATUS_COLLECTED   USER_MISSION_STATUS = 3
)

var USER_MISSION_STATUS_name = map[int32]string{
	1: "IN_PROGRESS",
	2: "FINISHED",
	3: "COLLECTED",
}
var USER_MISSION_STATUS_value = map[string]int32{
	"IN_PROGRESS": 1,
	"FINISHED":    2,
	"COLLECTED":   3,
}

func (x USER_MISSION_STATUS) Enum() *USER_MISSION_STATUS {
	p := new(USER_MISSION_STATUS)
	*p = x
	return p
}
func (x USER_MISSION_STATUS) String() string {
	return proto.EnumName(USER_MISSION_STATUS_name, int32(x))
}
func (x *USER_MISSION_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(USER_MISSION_STATUS_value, data, "USER_MISSION_STATUS")
	if err != nil {
		return err
	}
	*x = USER_MISSION_STATUS(value)
	return nil
}
func (USER_MISSION_STATUS) EnumDescriptor() ([]byte, []int) { return fileDescriptorMission, []int{0} }

// 里程碑(MASK, 各枚举字段需要用位移计算)
type MILESTONE int32

const (
	MILESTONE_MILESTONE_NONE    MILESTONE = 1
	MILESTONE_GREENER_COMPLETED MILESTONE = 2
)

var MILESTONE_name = map[int32]string{
	1: "MILESTONE_NONE",
	2: "GREENER_COMPLETED",
}
var MILESTONE_value = map[string]int32{
	"MILESTONE_NONE":    1,
	"GREENER_COMPLETED": 2,
}

func (x MILESTONE) Enum() *MILESTONE {
	p := new(MILESTONE)
	*p = x
	return p
}
func (x MILESTONE) String() string {
	return proto.EnumName(MILESTONE_name, int32(x))
}
func (x *MILESTONE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MILESTONE_value, data, "MILESTONE")
	if err != nil {
		return err
	}
	*x = MILESTONE(value)
	return nil
}
func (MILESTONE) EnumDescriptor() ([]byte, []int) { return fileDescriptorMission, []int{1} }

// 数据来源
type DAY_OPER_TYPE int32

const (
	DAY_OPER_TYPE_GAME_LOGIN     DAY_OPER_TYPE = 1
	DAY_OPER_TYPE_GAME_RECHARGE  DAY_OPER_TYPE = 2
	DAY_OPER_TYPE_TT_LOGIN       DAY_OPER_TYPE = 3
	DAY_OPER_TYPE_GUILD_CHECK_IN DAY_OPER_TYPE = 4
)

var DAY_OPER_TYPE_name = map[int32]string{
	1: "GAME_LOGIN",
	2: "GAME_RECHARGE",
	3: "TT_LOGIN",
	4: "GUILD_CHECK_IN",
}
var DAY_OPER_TYPE_value = map[string]int32{
	"GAME_LOGIN":     1,
	"GAME_RECHARGE":  2,
	"TT_LOGIN":       3,
	"GUILD_CHECK_IN": 4,
}

func (x DAY_OPER_TYPE) Enum() *DAY_OPER_TYPE {
	p := new(DAY_OPER_TYPE)
	*p = x
	return p
}
func (x DAY_OPER_TYPE) String() string {
	return proto.EnumName(DAY_OPER_TYPE_name, int32(x))
}
func (x *DAY_OPER_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(DAY_OPER_TYPE_value, data, "DAY_OPER_TYPE")
	if err != nil {
		return err
	}
	*x = DAY_OPER_TYPE(value)
	return nil
}
func (DAY_OPER_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorMission, []int{2} }

// 数据来源
type RECHARGE_SOURCE int32

const (
	RECHARGE_SOURCE_GAME         RECHARGE_SOURCE = 1
	RECHARGE_SOURCE_HAPPY_CENTER RECHARGE_SOURCE = 2
)

var RECHARGE_SOURCE_name = map[int32]string{
	1: "GAME",
	2: "HAPPY_CENTER",
}
var RECHARGE_SOURCE_value = map[string]int32{
	"GAME":         1,
	"HAPPY_CENTER": 2,
}

func (x RECHARGE_SOURCE) Enum() *RECHARGE_SOURCE {
	p := new(RECHARGE_SOURCE)
	*p = x
	return p
}
func (x RECHARGE_SOURCE) String() string {
	return proto.EnumName(RECHARGE_SOURCE_name, int32(x))
}
func (x *RECHARGE_SOURCE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RECHARGE_SOURCE_value, data, "RECHARGE_SOURCE")
	if err != nil {
		return err
	}
	*x = RECHARGE_SOURCE(value)
	return nil
}
func (RECHARGE_SOURCE) EnumDescriptor() ([]byte, []int) { return fileDescriptorMission, []int{3} }

// 用户进行中或已经完成的任务列表
type StUserMission struct {
	MissionId       uint32 `protobuf:"varint,1,req,name=mission_id,json=missionId" json:"mission_id"`
	Uid             uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	StartTime       uint32 `protobuf:"varint,3,req,name=start_time,json=startTime" json:"start_time"`
	ExpireTime      uint32 `protobuf:"varint,4,opt,name=expire_time,json=expireTime" json:"expire_time"`
	BonusExpireTime uint32 `protobuf:"varint,5,opt,name=bonus_expire_time,json=bonusExpireTime" json:"bonus_expire_time"`
	Status          uint32 `protobuf:"varint,6,req,name=status" json:"status"`
	FinishCount     uint32 `protobuf:"varint,7,req,name=finish_count,json=finishCount" json:"finish_count"`
	Identifier      string `protobuf:"bytes,8,req,name=identifier" json:"identifier"`
	RewardCount     uint32 `protobuf:"varint,9,opt,name=reward_count,json=rewardCount" json:"reward_count"`
	AcceptTime      uint32 `protobuf:"varint,10,opt,name=accept_time,json=acceptTime" json:"accept_time"`
}

func (m *StUserMission) Reset()                    { *m = StUserMission{} }
func (m *StUserMission) String() string            { return proto.CompactTextString(m) }
func (*StUserMission) ProtoMessage()               {}
func (*StUserMission) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{0} }

func (m *StUserMission) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

func (m *StUserMission) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StUserMission) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *StUserMission) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *StUserMission) GetBonusExpireTime() uint32 {
	if m != nil {
		return m.BonusExpireTime
	}
	return 0
}

func (m *StUserMission) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *StUserMission) GetFinishCount() uint32 {
	if m != nil {
		return m.FinishCount
	}
	return 0
}

func (m *StUserMission) GetIdentifier() string {
	if m != nil {
		return m.Identifier
	}
	return ""
}

func (m *StUserMission) GetRewardCount() uint32 {
	if m != nil {
		return m.RewardCount
	}
	return 0
}

func (m *StUserMission) GetAcceptTime() uint32 {
	if m != nil {
		return m.AcceptTime
	}
	return 0
}

// 获取任务配置以及用户任务列表
type GetUserMissionListReq struct {
}

func (m *GetUserMissionListReq) Reset()                    { *m = GetUserMissionListReq{} }
func (m *GetUserMissionListReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserMissionListReq) ProtoMessage()               {}
func (*GetUserMissionListReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{1} }

type GetUserMissionListResp struct {
	UserMissionList []*StUserMission `protobuf:"bytes,1,rep,name=user_mission_list,json=userMissionList" json:"user_mission_list,omitempty"`
}

func (m *GetUserMissionListResp) Reset()                    { *m = GetUserMissionListResp{} }
func (m *GetUserMissionListResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserMissionListResp) ProtoMessage()               {}
func (*GetUserMissionListResp) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{2} }

func (m *GetUserMissionListResp) GetUserMissionList() []*StUserMission {
	if m != nil {
		return m.UserMissionList
	}
	return nil
}

// 根据UserMissionIdentifier获取任务数据
type GetUserMissionByIdentifierReq struct {
	UserMissionIdentifier string `protobuf:"bytes,1,req,name=user_mission_identifier,json=userMissionIdentifier" json:"user_mission_identifier"`
}

func (m *GetUserMissionByIdentifierReq) Reset()         { *m = GetUserMissionByIdentifierReq{} }
func (m *GetUserMissionByIdentifierReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionByIdentifierReq) ProtoMessage()    {}
func (*GetUserMissionByIdentifierReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{3}
}

func (m *GetUserMissionByIdentifierReq) GetUserMissionIdentifier() string {
	if m != nil {
		return m.UserMissionIdentifier
	}
	return ""
}

type GetUserMissionByIdentifierResp struct {
	// 若不存在该字段, 表示该任务未被触发
	UserMission *StUserMission `protobuf:"bytes,1,opt,name=user_mission,json=userMission" json:"user_mission,omitempty"`
}

func (m *GetUserMissionByIdentifierResp) Reset()         { *m = GetUserMissionByIdentifierResp{} }
func (m *GetUserMissionByIdentifierResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionByIdentifierResp) ProtoMessage()    {}
func (*GetUserMissionByIdentifierResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{4}
}

func (m *GetUserMissionByIdentifierResp) GetUserMission() *StUserMission {
	if m != nil {
		return m.UserMission
	}
	return nil
}

type GetUserMissionsByIdentifierListReq struct {
	UserMissionIdentifierList []string `protobuf:"bytes,1,rep,name=user_mission_identifier_list,json=userMissionIdentifierList" json:"user_mission_identifier_list,omitempty"`
}

func (m *GetUserMissionsByIdentifierListReq) Reset()         { *m = GetUserMissionsByIdentifierListReq{} }
func (m *GetUserMissionsByIdentifierListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionsByIdentifierListReq) ProtoMessage()    {}
func (*GetUserMissionsByIdentifierListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{5}
}

func (m *GetUserMissionsByIdentifierListReq) GetUserMissionIdentifierList() []string {
	if m != nil {
		return m.UserMissionIdentifierList
	}
	return nil
}

type GetUserMissionsByIdentifierListResp struct {
	UserMissionList []*StUserMission `protobuf:"bytes,2,rep,name=user_mission_list,json=userMissionList" json:"user_mission_list,omitempty"`
}

func (m *GetUserMissionsByIdentifierListResp) Reset()         { *m = GetUserMissionsByIdentifierListResp{} }
func (m *GetUserMissionsByIdentifierListResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMissionsByIdentifierListResp) ProtoMessage()    {}
func (*GetUserMissionsByIdentifierListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{6}
}

func (m *GetUserMissionsByIdentifierListResp) GetUserMissionList() []*StUserMission {
	if m != nil {
		return m.UserMissionList
	}
	return nil
}

// 增加任务完成次数, 若不存在对应的任务, 则会创建任务并增加相应次数
type IncreaseUserMissionFinishCountReq struct {
	UserMission *StUserMission `protobuf:"bytes,1,req,name=user_mission,json=userMission" json:"user_mission,omitempty"`
	FinishCount uint32         `protobuf:"varint,2,req,name=finish_count,json=finishCount" json:"finish_count"`
}

func (m *IncreaseUserMissionFinishCountReq) Reset()         { *m = IncreaseUserMissionFinishCountReq{} }
func (m *IncreaseUserMissionFinishCountReq) String() string { return proto.CompactTextString(m) }
func (*IncreaseUserMissionFinishCountReq) ProtoMessage()    {}
func (*IncreaseUserMissionFinishCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{7}
}

func (m *IncreaseUserMissionFinishCountReq) GetUserMission() *StUserMission {
	if m != nil {
		return m.UserMission
	}
	return nil
}

func (m *IncreaseUserMissionFinishCountReq) GetFinishCount() uint32 {
	if m != nil {
		return m.FinishCount
	}
	return 0
}

type IncreaseUserMissionFinishCountResp struct {
	UserMission *StUserMission `protobuf:"bytes,1,req,name=user_mission,json=userMission" json:"user_mission,omitempty"`
}

func (m *IncreaseUserMissionFinishCountResp) Reset()         { *m = IncreaseUserMissionFinishCountResp{} }
func (m *IncreaseUserMissionFinishCountResp) String() string { return proto.CompactTextString(m) }
func (*IncreaseUserMissionFinishCountResp) ProtoMessage()    {}
func (*IncreaseUserMissionFinishCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{8}
}

func (m *IncreaseUserMissionFinishCountResp) GetUserMission() *StUserMission {
	if m != nil {
		return m.UserMission
	}
	return nil
}

// 增加任务完成至次数, 若不存在对应的任务, 则会创建并写入相应次数
// 此接口只能增加完成次数不能减少
type IncreaseUserMissionToFinishCountReq struct {
	UserMission *StUserMission `protobuf:"bytes,1,req,name=user_mission,json=userMission" json:"user_mission,omitempty"`
	FinishCount uint32         `protobuf:"varint,2,req,name=finish_count,json=finishCount" json:"finish_count"`
	ForceUpdate bool           `protobuf:"varint,3,opt,name=force_update,json=forceUpdate" json:"force_update"`
}

func (m *IncreaseUserMissionToFinishCountReq) Reset()         { *m = IncreaseUserMissionToFinishCountReq{} }
func (m *IncreaseUserMissionToFinishCountReq) String() string { return proto.CompactTextString(m) }
func (*IncreaseUserMissionToFinishCountReq) ProtoMessage()    {}
func (*IncreaseUserMissionToFinishCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{9}
}

func (m *IncreaseUserMissionToFinishCountReq) GetUserMission() *StUserMission {
	if m != nil {
		return m.UserMission
	}
	return nil
}

func (m *IncreaseUserMissionToFinishCountReq) GetFinishCount() uint32 {
	if m != nil {
		return m.FinishCount
	}
	return 0
}

func (m *IncreaseUserMissionToFinishCountReq) GetForceUpdate() bool {
	if m != nil {
		return m.ForceUpdate
	}
	return false
}

// 如果请求指定的完成次数比库中的次数还小或相等, 则success=false; 否则为true
type IncreaseUserMissionToFinishCountResp struct {
	Success     bool           `protobuf:"varint,1,req,name=success" json:"success"`
	UserMission *StUserMission `protobuf:"bytes,2,opt,name=user_mission,json=userMission" json:"user_mission,omitempty"`
}

func (m *IncreaseUserMissionToFinishCountResp) Reset()         { *m = IncreaseUserMissionToFinishCountResp{} }
func (m *IncreaseUserMissionToFinishCountResp) String() string { return proto.CompactTextString(m) }
func (*IncreaseUserMissionToFinishCountResp) ProtoMessage()    {}
func (*IncreaseUserMissionToFinishCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{10}
}

func (m *IncreaseUserMissionToFinishCountResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *IncreaseUserMissionToFinishCountResp) GetUserMission() *StUserMission {
	if m != nil {
		return m.UserMission
	}
	return nil
}

type UpdateUserMissionStatusByIdentifierReq struct {
	UserMissionIdentifier string `protobuf:"bytes,1,req,name=user_mission_identifier,json=userMissionIdentifier" json:"user_mission_identifier"`
	NewStatus             uint32 `protobuf:"varint,2,req,name=new_status,json=newStatus" json:"new_status"`
	AddRewardCount        uint32 `protobuf:"varint,3,opt,name=add_reward_count,json=addRewardCount" json:"add_reward_count"`
	ForceUpdateStatus     uint32 `protobuf:"varint,4,opt,name=force_update_status,json=forceUpdateStatus" json:"force_update_status"`
}

func (m *UpdateUserMissionStatusByIdentifierReq) Reset() {
	*m = UpdateUserMissionStatusByIdentifierReq{}
}
func (m *UpdateUserMissionStatusByIdentifierReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserMissionStatusByIdentifierReq) ProtoMessage()    {}
func (*UpdateUserMissionStatusByIdentifierReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{11}
}

func (m *UpdateUserMissionStatusByIdentifierReq) GetUserMissionIdentifier() string {
	if m != nil {
		return m.UserMissionIdentifier
	}
	return ""
}

func (m *UpdateUserMissionStatusByIdentifierReq) GetNewStatus() uint32 {
	if m != nil {
		return m.NewStatus
	}
	return 0
}

func (m *UpdateUserMissionStatusByIdentifierReq) GetAddRewardCount() uint32 {
	if m != nil {
		return m.AddRewardCount
	}
	return 0
}

func (m *UpdateUserMissionStatusByIdentifierReq) GetForceUpdateStatus() uint32 {
	if m != nil {
		return m.ForceUpdateStatus
	}
	return 0
}

type UpdateUserMissionStatusByIdentifierResp struct {
}

func (m *UpdateUserMissionStatusByIdentifierResp) Reset() {
	*m = UpdateUserMissionStatusByIdentifierResp{}
}
func (m *UpdateUserMissionStatusByIdentifierResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserMissionStatusByIdentifierResp) ProtoMessage()    {}
func (*UpdateUserMissionStatusByIdentifierResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{12}
}

// 更新用户的成长里程碑
type UpdateUserMilestoneReq struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	NewMilestone uint32 `protobuf:"varint,2,req,name=new_milestone,json=newMilestone" json:"new_milestone"`
}

func (m *UpdateUserMilestoneReq) Reset()                    { *m = UpdateUserMilestoneReq{} }
func (m *UpdateUserMilestoneReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserMilestoneReq) ProtoMessage()               {}
func (*UpdateUserMilestoneReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{13} }

func (m *UpdateUserMilestoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserMilestoneReq) GetNewMilestone() uint32 {
	if m != nil {
		return m.NewMilestone
	}
	return 0
}

type UpdateUserMilestoneResp struct {
	Success   bool   `protobuf:"varint,1,req,name=success" json:"success"`
	Milestone uint32 `protobuf:"varint,2,req,name=milestone" json:"milestone"`
}

func (m *UpdateUserMilestoneResp) Reset()                    { *m = UpdateUserMilestoneResp{} }
func (m *UpdateUserMilestoneResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserMilestoneResp) ProtoMessage()               {}
func (*UpdateUserMilestoneResp) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{14} }

func (m *UpdateUserMilestoneResp) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *UpdateUserMilestoneResp) GetMilestone() uint32 {
	if m != nil {
		return m.Milestone
	}
	return 0
}

type GetUserMilestoneReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserMilestoneReq) Reset()                    { *m = GetUserMilestoneReq{} }
func (m *GetUserMilestoneReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserMilestoneReq) ProtoMessage()               {}
func (*GetUserMilestoneReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{15} }

func (m *GetUserMilestoneReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMilestoneResp struct {
	Milestone uint32 `protobuf:"varint,1,req,name=milestone" json:"milestone"`
}

func (m *GetUserMilestoneResp) Reset()                    { *m = GetUserMilestoneResp{} }
func (m *GetUserMilestoneResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserMilestoneResp) ProtoMessage()               {}
func (*GetUserMilestoneResp) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{16} }

func (m *GetUserMilestoneResp) GetMilestone() uint32 {
	if m != nil {
		return m.Milestone
	}
	return 0
}

// 取用户任务
type GetLatestUserMissionReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
}

func (m *GetLatestUserMissionReq) Reset()                    { *m = GetLatestUserMissionReq{} }
func (m *GetLatestUserMissionReq) String() string            { return proto.CompactTextString(m) }
func (*GetLatestUserMissionReq) ProtoMessage()               {}
func (*GetLatestUserMissionReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{17} }

func (m *GetLatestUserMissionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLatestUserMissionReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

type GetLatestUserMissionResp struct {
	UserMission *StUserMission `protobuf:"bytes,1,opt,name=user_mission,json=userMission" json:"user_mission,omitempty"`
}

func (m *GetLatestUserMissionResp) Reset()                    { *m = GetLatestUserMissionResp{} }
func (m *GetLatestUserMissionResp) String() string            { return proto.CompactTextString(m) }
func (*GetLatestUserMissionResp) ProtoMessage()               {}
func (*GetLatestUserMissionResp) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{18} }

func (m *GetLatestUserMissionResp) GetUserMission() *StUserMission {
	if m != nil {
		return m.UserMission
	}
	return nil
}

// 更新用户任务
type UpdateUserMissionReq struct {
	UserMissionIdentifier string `protobuf:"bytes,1,req,name=user_mission_identifier,json=userMissionIdentifier" json:"user_mission_identifier"`
	ExpireTime            uint32 `protobuf:"varint,2,opt,name=expire_time,json=expireTime" json:"expire_time"`
	BonusExpireTime       uint32 `protobuf:"varint,3,opt,name=bonus_expire_time,json=bonusExpireTime" json:"bonus_expire_time"`
	Status                uint32 `protobuf:"varint,4,opt,name=status" json:"status"`
	AcceptTime            uint32 `protobuf:"varint,5,opt,name=accept_time,json=acceptTime" json:"accept_time"`
}

func (m *UpdateUserMissionReq) Reset()                    { *m = UpdateUserMissionReq{} }
func (m *UpdateUserMissionReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserMissionReq) ProtoMessage()               {}
func (*UpdateUserMissionReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{19} }

func (m *UpdateUserMissionReq) GetUserMissionIdentifier() string {
	if m != nil {
		return m.UserMissionIdentifier
	}
	return ""
}

func (m *UpdateUserMissionReq) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *UpdateUserMissionReq) GetBonusExpireTime() uint32 {
	if m != nil {
		return m.BonusExpireTime
	}
	return 0
}

func (m *UpdateUserMissionReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateUserMissionReq) GetAcceptTime() uint32 {
	if m != nil {
		return m.AcceptTime
	}
	return 0
}

type UpdateUserMissionResp struct {
	Updated bool `protobuf:"varint,1,req,name=updated" json:"updated"`
}

func (m *UpdateUserMissionResp) Reset()                    { *m = UpdateUserMissionResp{} }
func (m *UpdateUserMissionResp) String() string            { return proto.CompactTextString(m) }
func (*UpdateUserMissionResp) ProtoMessage()               {}
func (*UpdateUserMissionResp) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{20} }

func (m *UpdateUserMissionResp) GetUpdated() bool {
	if m != nil {
		return m.Updated
	}
	return false
}

type GetMissionTotalFinishCountReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
}

func (m *GetMissionTotalFinishCountReq) Reset()         { *m = GetMissionTotalFinishCountReq{} }
func (m *GetMissionTotalFinishCountReq) String() string { return proto.CompactTextString(m) }
func (*GetMissionTotalFinishCountReq) ProtoMessage()    {}
func (*GetMissionTotalFinishCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{21}
}

func (m *GetMissionTotalFinishCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMissionTotalFinishCountReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

type GetMissionTotalFinishCountResp struct {
	TotalFinishCount uint32 `protobuf:"varint,1,req,name=total_finish_count,json=totalFinishCount" json:"total_finish_count"`
}

func (m *GetMissionTotalFinishCountResp) Reset()         { *m = GetMissionTotalFinishCountResp{} }
func (m *GetMissionTotalFinishCountResp) String() string { return proto.CompactTextString(m) }
func (*GetMissionTotalFinishCountResp) ProtoMessage()    {}
func (*GetMissionTotalFinishCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{22}
}

func (m *GetMissionTotalFinishCountResp) GetTotalFinishCount() uint32 {
	if m != nil {
		return m.TotalFinishCount
	}
	return 0
}

type GetMissionTotalCollectCountReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MissionId uint32 `protobuf:"varint,2,req,name=mission_id,json=missionId" json:"mission_id"`
}

func (m *GetMissionTotalCollectCountReq) Reset()         { *m = GetMissionTotalCollectCountReq{} }
func (m *GetMissionTotalCollectCountReq) String() string { return proto.CompactTextString(m) }
func (*GetMissionTotalCollectCountReq) ProtoMessage()    {}
func (*GetMissionTotalCollectCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{23}
}

func (m *GetMissionTotalCollectCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMissionTotalCollectCountReq) GetMissionId() uint32 {
	if m != nil {
		return m.MissionId
	}
	return 0
}

type GetMissionTotalCollectCountResp struct {
	TotalCollectCount uint32 `protobuf:"varint,1,req,name=total_collect_count,json=totalCollectCount" json:"total_collect_count"`
}

func (m *GetMissionTotalCollectCountResp) Reset()         { *m = GetMissionTotalCollectCountResp{} }
func (m *GetMissionTotalCollectCountResp) String() string { return proto.CompactTextString(m) }
func (*GetMissionTotalCollectCountResp) ProtoMessage()    {}
func (*GetMissionTotalCollectCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{24}
}

func (m *GetMissionTotalCollectCountResp) GetTotalCollectCount() uint32 {
	if m != nil {
		return m.TotalCollectCount
	}
	return 0
}

// 用户每日连续行为（如游戏连续登陆x天）
type RecordUserContinuousDayOperReq struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OperType      uint32 `protobuf:"varint,2,req,name=oper_type,json=operType" json:"oper_type"`
	LyGameId      uint64 `protobuf:"varint,3,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
	ForceAddAccum uint32 `protobuf:"varint,4,opt,name=force_add_accum,json=forceAddAccum" json:"force_add_accum"`
}

func (m *RecordUserContinuousDayOperReq) Reset()         { *m = RecordUserContinuousDayOperReq{} }
func (m *RecordUserContinuousDayOperReq) String() string { return proto.CompactTextString(m) }
func (*RecordUserContinuousDayOperReq) ProtoMessage()    {}
func (*RecordUserContinuousDayOperReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{25}
}

func (m *RecordUserContinuousDayOperReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUserContinuousDayOperReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *RecordUserContinuousDayOperReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func (m *RecordUserContinuousDayOperReq) GetForceAddAccum() uint32 {
	if m != nil {
		return m.ForceAddAccum
	}
	return 0
}

type GetUserContinuousDayOperReq struct {
	Uid      uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	OperType uint32 `protobuf:"varint,2,req,name=oper_type,json=operType" json:"oper_type"`
	LyGameId uint64 `protobuf:"varint,3,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *GetUserContinuousDayOperReq) Reset()         { *m = GetUserContinuousDayOperReq{} }
func (m *GetUserContinuousDayOperReq) String() string { return proto.CompactTextString(m) }
func (*GetUserContinuousDayOperReq) ProtoMessage()    {}
func (*GetUserContinuousDayOperReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{26}
}

func (m *GetUserContinuousDayOperReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserContinuousDayOperReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *GetUserContinuousDayOperReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetUserContinuousDayOperResp struct {
	LastOperAt     uint32 `protobuf:"varint,1,req,name=last_oper_at,json=lastOperAt" json:"last_oper_at"`
	OperCount      uint32 `protobuf:"varint,2,req,name=oper_count,json=operCount" json:"oper_count"`
	AccumOperCount uint32 `protobuf:"varint,3,req,name=accum_oper_count,json=accumOperCount" json:"accum_oper_count"`
}

func (m *GetUserContinuousDayOperResp) Reset()         { *m = GetUserContinuousDayOperResp{} }
func (m *GetUserContinuousDayOperResp) String() string { return proto.CompactTextString(m) }
func (*GetUserContinuousDayOperResp) ProtoMessage()    {}
func (*GetUserContinuousDayOperResp) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{27}
}

func (m *GetUserContinuousDayOperResp) GetLastOperAt() uint32 {
	if m != nil {
		return m.LastOperAt
	}
	return 0
}

func (m *GetUserContinuousDayOperResp) GetOperCount() uint32 {
	if m != nil {
		return m.OperCount
	}
	return 0
}

func (m *GetUserContinuousDayOperResp) GetAccumOperCount() uint32 {
	if m != nil {
		return m.AccumOperCount
	}
	return 0
}

type ClearUserContinuousDayOperReq struct {
	OperType uint32 `protobuf:"varint,1,req,name=oper_type,json=operType" json:"oper_type"`
	LyGameId uint64 `protobuf:"varint,2,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *ClearUserContinuousDayOperReq) Reset()         { *m = ClearUserContinuousDayOperReq{} }
func (m *ClearUserContinuousDayOperReq) String() string { return proto.CompactTextString(m) }
func (*ClearUserContinuousDayOperReq) ProtoMessage()    {}
func (*ClearUserContinuousDayOperReq) Descriptor() ([]byte, []int) {
	return fileDescriptorMission, []int{28}
}

func (m *ClearUserContinuousDayOperReq) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *ClearUserContinuousDayOperReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

// 用户充值行为
type RecordUserRechargeReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	RechargeSource uint32 `protobuf:"varint,2,req,name=recharge_source,json=rechargeSource" json:"recharge_source"`
	RechargePenny  uint32 `protobuf:"varint,3,req,name=recharge_penny,json=rechargePenny" json:"recharge_penny"`
	LyGameId       uint64 `protobuf:"varint,4,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *RecordUserRechargeReq) Reset()                    { *m = RecordUserRechargeReq{} }
func (m *RecordUserRechargeReq) String() string            { return proto.CompactTextString(m) }
func (*RecordUserRechargeReq) ProtoMessage()               {}
func (*RecordUserRechargeReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{29} }

func (m *RecordUserRechargeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordUserRechargeReq) GetRechargeSource() uint32 {
	if m != nil {
		return m.RechargeSource
	}
	return 0
}

func (m *RecordUserRechargeReq) GetRechargePenny() uint32 {
	if m != nil {
		return m.RechargePenny
	}
	return 0
}

func (m *RecordUserRechargeReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetUserRechargeReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	RechargeSource uint32 `protobuf:"varint,2,req,name=recharge_source,json=rechargeSource" json:"recharge_source"`
	LyGameId       uint64 `protobuf:"varint,3,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *GetUserRechargeReq) Reset()                    { *m = GetUserRechargeReq{} }
func (m *GetUserRechargeReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserRechargeReq) ProtoMessage()               {}
func (*GetUserRechargeReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{30} }

func (m *GetUserRechargeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRechargeReq) GetRechargeSource() uint32 {
	if m != nil {
		return m.RechargeSource
	}
	return 0
}

func (m *GetUserRechargeReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

type GetUserRechargeResp struct {
	LastRechargeAt     uint32 `protobuf:"varint,1,req,name=last_recharge_at,json=lastRechargeAt" json:"last_recharge_at"`
	AccumRechargePenny uint32 `protobuf:"varint,2,req,name=accum_recharge_penny,json=accumRechargePenny" json:"accum_recharge_penny"`
}

func (m *GetUserRechargeResp) Reset()                    { *m = GetUserRechargeResp{} }
func (m *GetUserRechargeResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserRechargeResp) ProtoMessage()               {}
func (*GetUserRechargeResp) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{31} }

func (m *GetUserRechargeResp) GetLastRechargeAt() uint32 {
	if m != nil {
		return m.LastRechargeAt
	}
	return 0
}

func (m *GetUserRechargeResp) GetAccumRechargePenny() uint32 {
	if m != nil {
		return m.AccumRechargePenny
	}
	return 0
}

type ClearUserRechargeReq struct {
	RechargeSource uint32 `protobuf:"varint,1,req,name=recharge_source,json=rechargeSource" json:"recharge_source"`
	LyGameId       uint64 `protobuf:"varint,2,opt,name=ly_game_id,json=lyGameId" json:"ly_game_id"`
}

func (m *ClearUserRechargeReq) Reset()                    { *m = ClearUserRechargeReq{} }
func (m *ClearUserRechargeReq) String() string            { return proto.CompactTextString(m) }
func (*ClearUserRechargeReq) ProtoMessage()               {}
func (*ClearUserRechargeReq) Descriptor() ([]byte, []int) { return fileDescriptorMission, []int{32} }

func (m *ClearUserRechargeReq) GetRechargeSource() uint32 {
	if m != nil {
		return m.RechargeSource
	}
	return 0
}

func (m *ClearUserRechargeReq) GetLyGameId() uint64 {
	if m != nil {
		return m.LyGameId
	}
	return 0
}

func init() {
	proto.RegisterType((*StUserMission)(nil), "Mission.StUserMission")
	proto.RegisterType((*GetUserMissionListReq)(nil), "Mission.GetUserMissionListReq")
	proto.RegisterType((*GetUserMissionListResp)(nil), "Mission.GetUserMissionListResp")
	proto.RegisterType((*GetUserMissionByIdentifierReq)(nil), "Mission.GetUserMissionByIdentifierReq")
	proto.RegisterType((*GetUserMissionByIdentifierResp)(nil), "Mission.GetUserMissionByIdentifierResp")
	proto.RegisterType((*GetUserMissionsByIdentifierListReq)(nil), "Mission.GetUserMissionsByIdentifierListReq")
	proto.RegisterType((*GetUserMissionsByIdentifierListResp)(nil), "Mission.GetUserMissionsByIdentifierListResp")
	proto.RegisterType((*IncreaseUserMissionFinishCountReq)(nil), "Mission.IncreaseUserMissionFinishCountReq")
	proto.RegisterType((*IncreaseUserMissionFinishCountResp)(nil), "Mission.IncreaseUserMissionFinishCountResp")
	proto.RegisterType((*IncreaseUserMissionToFinishCountReq)(nil), "Mission.IncreaseUserMissionToFinishCountReq")
	proto.RegisterType((*IncreaseUserMissionToFinishCountResp)(nil), "Mission.IncreaseUserMissionToFinishCountResp")
	proto.RegisterType((*UpdateUserMissionStatusByIdentifierReq)(nil), "Mission.UpdateUserMissionStatusByIdentifierReq")
	proto.RegisterType((*UpdateUserMissionStatusByIdentifierResp)(nil), "Mission.UpdateUserMissionStatusByIdentifierResp")
	proto.RegisterType((*UpdateUserMilestoneReq)(nil), "Mission.UpdateUserMilestoneReq")
	proto.RegisterType((*UpdateUserMilestoneResp)(nil), "Mission.UpdateUserMilestoneResp")
	proto.RegisterType((*GetUserMilestoneReq)(nil), "Mission.GetUserMilestoneReq")
	proto.RegisterType((*GetUserMilestoneResp)(nil), "Mission.GetUserMilestoneResp")
	proto.RegisterType((*GetLatestUserMissionReq)(nil), "Mission.GetLatestUserMissionReq")
	proto.RegisterType((*GetLatestUserMissionResp)(nil), "Mission.GetLatestUserMissionResp")
	proto.RegisterType((*UpdateUserMissionReq)(nil), "Mission.UpdateUserMissionReq")
	proto.RegisterType((*UpdateUserMissionResp)(nil), "Mission.UpdateUserMissionResp")
	proto.RegisterType((*GetMissionTotalFinishCountReq)(nil), "Mission.GetMissionTotalFinishCountReq")
	proto.RegisterType((*GetMissionTotalFinishCountResp)(nil), "Mission.GetMissionTotalFinishCountResp")
	proto.RegisterType((*GetMissionTotalCollectCountReq)(nil), "Mission.GetMissionTotalCollectCountReq")
	proto.RegisterType((*GetMissionTotalCollectCountResp)(nil), "Mission.GetMissionTotalCollectCountResp")
	proto.RegisterType((*RecordUserContinuousDayOperReq)(nil), "Mission.RecordUserContinuousDayOperReq")
	proto.RegisterType((*GetUserContinuousDayOperReq)(nil), "Mission.GetUserContinuousDayOperReq")
	proto.RegisterType((*GetUserContinuousDayOperResp)(nil), "Mission.GetUserContinuousDayOperResp")
	proto.RegisterType((*ClearUserContinuousDayOperReq)(nil), "Mission.ClearUserContinuousDayOperReq")
	proto.RegisterType((*RecordUserRechargeReq)(nil), "Mission.RecordUserRechargeReq")
	proto.RegisterType((*GetUserRechargeReq)(nil), "Mission.GetUserRechargeReq")
	proto.RegisterType((*GetUserRechargeResp)(nil), "Mission.GetUserRechargeResp")
	proto.RegisterType((*ClearUserRechargeReq)(nil), "Mission.ClearUserRechargeReq")
	proto.RegisterEnum("Mission.USER_MISSION_STATUS", USER_MISSION_STATUS_name, USER_MISSION_STATUS_value)
	proto.RegisterEnum("Mission.MILESTONE", MILESTONE_name, MILESTONE_value)
	proto.RegisterEnum("Mission.DAY_OPER_TYPE", DAY_OPER_TYPE_name, DAY_OPER_TYPE_value)
	proto.RegisterEnum("Mission.RECHARGE_SOURCE", RECHARGE_SOURCE_name, RECHARGE_SOURCE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Mission service

type MissionClient interface {
	// 获取用户的任务列表
	GetUserMissionList(ctx context.Context, in *GetUserMissionListReq, opts ...grpc.CallOption) (*GetUserMissionListResp, error)
	// 单查用户任务
	GetUserMissionByIdentifier(ctx context.Context, in *GetUserMissionByIdentifierReq, opts ...grpc.CallOption) (*GetUserMissionByIdentifierResp, error)
	// 批量查用户任务
	GetUserMissionsByIdentifierList(ctx context.Context, in *GetUserMissionsByIdentifierListReq, opts ...grpc.CallOption) (*GetUserMissionsByIdentifierListResp, error)
	// 增加任务完成次数
	IncreaseUserMissionFinishCount(ctx context.Context, in *IncreaseUserMissionFinishCountReq, opts ...grpc.CallOption) (*IncreaseUserMissionFinishCountResp, error)
	UpdateUserMissionStatusByIdentifier(ctx context.Context, in *UpdateUserMissionStatusByIdentifierReq, opts ...grpc.CallOption) (*UpdateUserMissionStatusByIdentifierResp, error)
	IncreaseUserMissionToFinishCount(ctx context.Context, in *IncreaseUserMissionToFinishCountReq, opts ...grpc.CallOption) (*IncreaseUserMissionToFinishCountResp, error)
	UpdateUserMilestone(ctx context.Context, in *UpdateUserMilestoneReq, opts ...grpc.CallOption) (*UpdateUserMilestoneResp, error)
	GetUserMilestone(ctx context.Context, in *GetUserMilestoneReq, opts ...grpc.CallOption) (*GetUserMilestoneResp, error)
	GetLatestUserMission(ctx context.Context, in *GetLatestUserMissionReq, opts ...grpc.CallOption) (*GetLatestUserMissionResp, error)
	UpdateUserMission(ctx context.Context, in *UpdateUserMissionReq, opts ...grpc.CallOption) (*UpdateUserMissionResp, error)
	GetMissionTotalFinishCount(ctx context.Context, in *GetMissionTotalFinishCountReq, opts ...grpc.CallOption) (*GetMissionTotalFinishCountResp, error)
	GetMissionTotalCollectCount(ctx context.Context, in *GetMissionTotalCollectCountReq, opts ...grpc.CallOption) (*GetMissionTotalCollectCountResp, error)
}

type missionClient struct {
	cc *grpc.ClientConn
}

func NewMissionClient(cc *grpc.ClientConn) MissionClient {
	return &missionClient{cc}
}

func (c *missionClient) GetUserMissionList(ctx context.Context, in *GetUserMissionListReq, opts ...grpc.CallOption) (*GetUserMissionListResp, error) {
	out := new(GetUserMissionListResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetUserMissionList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetUserMissionByIdentifier(ctx context.Context, in *GetUserMissionByIdentifierReq, opts ...grpc.CallOption) (*GetUserMissionByIdentifierResp, error) {
	out := new(GetUserMissionByIdentifierResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetUserMissionByIdentifier", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetUserMissionsByIdentifierList(ctx context.Context, in *GetUserMissionsByIdentifierListReq, opts ...grpc.CallOption) (*GetUserMissionsByIdentifierListResp, error) {
	out := new(GetUserMissionsByIdentifierListResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetUserMissionsByIdentifierList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) IncreaseUserMissionFinishCount(ctx context.Context, in *IncreaseUserMissionFinishCountReq, opts ...grpc.CallOption) (*IncreaseUserMissionFinishCountResp, error) {
	out := new(IncreaseUserMissionFinishCountResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/IncreaseUserMissionFinishCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) UpdateUserMissionStatusByIdentifier(ctx context.Context, in *UpdateUserMissionStatusByIdentifierReq, opts ...grpc.CallOption) (*UpdateUserMissionStatusByIdentifierResp, error) {
	out := new(UpdateUserMissionStatusByIdentifierResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/UpdateUserMissionStatusByIdentifier", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) IncreaseUserMissionToFinishCount(ctx context.Context, in *IncreaseUserMissionToFinishCountReq, opts ...grpc.CallOption) (*IncreaseUserMissionToFinishCountResp, error) {
	out := new(IncreaseUserMissionToFinishCountResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/IncreaseUserMissionToFinishCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) UpdateUserMilestone(ctx context.Context, in *UpdateUserMilestoneReq, opts ...grpc.CallOption) (*UpdateUserMilestoneResp, error) {
	out := new(UpdateUserMilestoneResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/UpdateUserMilestone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetUserMilestone(ctx context.Context, in *GetUserMilestoneReq, opts ...grpc.CallOption) (*GetUserMilestoneResp, error) {
	out := new(GetUserMilestoneResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetUserMilestone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetLatestUserMission(ctx context.Context, in *GetLatestUserMissionReq, opts ...grpc.CallOption) (*GetLatestUserMissionResp, error) {
	out := new(GetLatestUserMissionResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetLatestUserMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) UpdateUserMission(ctx context.Context, in *UpdateUserMissionReq, opts ...grpc.CallOption) (*UpdateUserMissionResp, error) {
	out := new(UpdateUserMissionResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/UpdateUserMission", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetMissionTotalFinishCount(ctx context.Context, in *GetMissionTotalFinishCountReq, opts ...grpc.CallOption) (*GetMissionTotalFinishCountResp, error) {
	out := new(GetMissionTotalFinishCountResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetMissionTotalFinishCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *missionClient) GetMissionTotalCollectCount(ctx context.Context, in *GetMissionTotalCollectCountReq, opts ...grpc.CallOption) (*GetMissionTotalCollectCountResp, error) {
	out := new(GetMissionTotalCollectCountResp)
	err := grpc.Invoke(ctx, "/Mission.Mission/GetMissionTotalCollectCount", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Mission service

type MissionServer interface {
	// 获取用户的任务列表
	GetUserMissionList(context.Context, *GetUserMissionListReq) (*GetUserMissionListResp, error)
	// 单查用户任务
	GetUserMissionByIdentifier(context.Context, *GetUserMissionByIdentifierReq) (*GetUserMissionByIdentifierResp, error)
	// 批量查用户任务
	GetUserMissionsByIdentifierList(context.Context, *GetUserMissionsByIdentifierListReq) (*GetUserMissionsByIdentifierListResp, error)
	// 增加任务完成次数
	IncreaseUserMissionFinishCount(context.Context, *IncreaseUserMissionFinishCountReq) (*IncreaseUserMissionFinishCountResp, error)
	UpdateUserMissionStatusByIdentifier(context.Context, *UpdateUserMissionStatusByIdentifierReq) (*UpdateUserMissionStatusByIdentifierResp, error)
	IncreaseUserMissionToFinishCount(context.Context, *IncreaseUserMissionToFinishCountReq) (*IncreaseUserMissionToFinishCountResp, error)
	UpdateUserMilestone(context.Context, *UpdateUserMilestoneReq) (*UpdateUserMilestoneResp, error)
	GetUserMilestone(context.Context, *GetUserMilestoneReq) (*GetUserMilestoneResp, error)
	GetLatestUserMission(context.Context, *GetLatestUserMissionReq) (*GetLatestUserMissionResp, error)
	UpdateUserMission(context.Context, *UpdateUserMissionReq) (*UpdateUserMissionResp, error)
	GetMissionTotalFinishCount(context.Context, *GetMissionTotalFinishCountReq) (*GetMissionTotalFinishCountResp, error)
	GetMissionTotalCollectCount(context.Context, *GetMissionTotalCollectCountReq) (*GetMissionTotalCollectCountResp, error)
}

func RegisterMissionServer(s *grpc.Server, srv MissionServer) {
	s.RegisterService(&_Mission_serviceDesc, srv)
}

func _Mission_GetUserMissionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMissionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetUserMissionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetUserMissionList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetUserMissionList(ctx, req.(*GetUserMissionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetUserMissionByIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMissionByIdentifierReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetUserMissionByIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetUserMissionByIdentifier",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetUserMissionByIdentifier(ctx, req.(*GetUserMissionByIdentifierReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetUserMissionsByIdentifierList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMissionsByIdentifierListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetUserMissionsByIdentifierList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetUserMissionsByIdentifierList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetUserMissionsByIdentifierList(ctx, req.(*GetUserMissionsByIdentifierListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_IncreaseUserMissionFinishCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncreaseUserMissionFinishCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).IncreaseUserMissionFinishCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/IncreaseUserMissionFinishCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).IncreaseUserMissionFinishCount(ctx, req.(*IncreaseUserMissionFinishCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_UpdateUserMissionStatusByIdentifier_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserMissionStatusByIdentifierReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).UpdateUserMissionStatusByIdentifier(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/UpdateUserMissionStatusByIdentifier",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).UpdateUserMissionStatusByIdentifier(ctx, req.(*UpdateUserMissionStatusByIdentifierReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_IncreaseUserMissionToFinishCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncreaseUserMissionToFinishCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).IncreaseUserMissionToFinishCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/IncreaseUserMissionToFinishCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).IncreaseUserMissionToFinishCount(ctx, req.(*IncreaseUserMissionToFinishCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_UpdateUserMilestone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserMilestoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).UpdateUserMilestone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/UpdateUserMilestone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).UpdateUserMilestone(ctx, req.(*UpdateUserMilestoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetUserMilestone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMilestoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetUserMilestone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetUserMilestone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetUserMilestone(ctx, req.(*GetUserMilestoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetLatestUserMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLatestUserMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetLatestUserMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetLatestUserMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetLatestUserMission(ctx, req.(*GetLatestUserMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_UpdateUserMission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserMissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).UpdateUserMission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/UpdateUserMission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).UpdateUserMission(ctx, req.(*UpdateUserMissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetMissionTotalFinishCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMissionTotalFinishCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetMissionTotalFinishCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetMissionTotalFinishCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetMissionTotalFinishCount(ctx, req.(*GetMissionTotalFinishCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Mission_GetMissionTotalCollectCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMissionTotalCollectCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionServer).GetMissionTotalCollectCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Mission.Mission/GetMissionTotalCollectCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionServer).GetMissionTotalCollectCount(ctx, req.(*GetMissionTotalCollectCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Mission_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Mission.Mission",
	HandlerType: (*MissionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserMissionList",
			Handler:    _Mission_GetUserMissionList_Handler,
		},
		{
			MethodName: "GetUserMissionByIdentifier",
			Handler:    _Mission_GetUserMissionByIdentifier_Handler,
		},
		{
			MethodName: "GetUserMissionsByIdentifierList",
			Handler:    _Mission_GetUserMissionsByIdentifierList_Handler,
		},
		{
			MethodName: "IncreaseUserMissionFinishCount",
			Handler:    _Mission_IncreaseUserMissionFinishCount_Handler,
		},
		{
			MethodName: "UpdateUserMissionStatusByIdentifier",
			Handler:    _Mission_UpdateUserMissionStatusByIdentifier_Handler,
		},
		{
			MethodName: "IncreaseUserMissionToFinishCount",
			Handler:    _Mission_IncreaseUserMissionToFinishCount_Handler,
		},
		{
			MethodName: "UpdateUserMilestone",
			Handler:    _Mission_UpdateUserMilestone_Handler,
		},
		{
			MethodName: "GetUserMilestone",
			Handler:    _Mission_GetUserMilestone_Handler,
		},
		{
			MethodName: "GetLatestUserMission",
			Handler:    _Mission_GetLatestUserMission_Handler,
		},
		{
			MethodName: "UpdateUserMission",
			Handler:    _Mission_UpdateUserMission_Handler,
		},
		{
			MethodName: "GetMissionTotalFinishCount",
			Handler:    _Mission_GetMissionTotalFinishCount_Handler,
		},
		{
			MethodName: "GetMissionTotalCollectCount",
			Handler:    _Mission_GetMissionTotalCollectCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/missionsvr/mission.proto",
}

func (m *StUserMission) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StUserMission) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.MissionId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.BonusExpireTime))
	dAtA[i] = 0x30
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x38
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.FinishCount))
	dAtA[i] = 0x42
	i++
	i = encodeVarintMission(dAtA, i, uint64(len(m.Identifier)))
	i += copy(dAtA[i:], m.Identifier)
	dAtA[i] = 0x48
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.RewardCount))
	dAtA[i] = 0x50
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.AcceptTime))
	return i, nil
}

func (m *GetUserMissionListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserMissionListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMissionList) > 0 {
		for _, msg := range m.UserMissionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintMission(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserMissionByIdentifierReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionByIdentifierReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMission(dAtA, i, uint64(len(m.UserMissionIdentifier)))
	i += copy(dAtA[i:], m.UserMissionIdentifier)
	return i, nil
}

func (m *GetUserMissionByIdentifierResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionByIdentifierResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserMission != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMission(dAtA, i, uint64(m.UserMission.Size()))
		n1, err := m.UserMission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetUserMissionsByIdentifierListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionsByIdentifierListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMissionIdentifierList) > 0 {
		for _, s := range m.UserMissionIdentifierList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetUserMissionsByIdentifierListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMissionsByIdentifierListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserMissionList) > 0 {
		for _, msg := range m.UserMissionList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintMission(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *IncreaseUserMissionFinishCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncreaseUserMissionFinishCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserMission == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("user_mission")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMission(dAtA, i, uint64(m.UserMission.Size()))
		n2, err := m.UserMission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.FinishCount))
	return i, nil
}

func (m *IncreaseUserMissionFinishCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncreaseUserMissionFinishCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserMission == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("user_mission")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMission(dAtA, i, uint64(m.UserMission.Size()))
		n3, err := m.UserMission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *IncreaseUserMissionToFinishCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncreaseUserMissionToFinishCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserMission == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("user_mission")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMission(dAtA, i, uint64(m.UserMission.Size()))
		n4, err := m.UserMission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.FinishCount))
	dAtA[i] = 0x18
	i++
	if m.ForceUpdate {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *IncreaseUserMissionToFinishCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncreaseUserMissionToFinishCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Success {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.UserMission != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintMission(dAtA, i, uint64(m.UserMission.Size()))
		n5, err := m.UserMission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *UpdateUserMissionStatusByIdentifierReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserMissionStatusByIdentifierReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMission(dAtA, i, uint64(len(m.UserMissionIdentifier)))
	i += copy(dAtA[i:], m.UserMissionIdentifier)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.NewStatus))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.AddRewardCount))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.ForceUpdateStatus))
	return i, nil
}

func (m *UpdateUserMissionStatusByIdentifierResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserMissionStatusByIdentifierResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateUserMilestoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserMilestoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.NewMilestone))
	return i, nil
}

func (m *UpdateUserMilestoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserMilestoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Success {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Milestone))
	return i, nil
}

func (m *GetUserMilestoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMilestoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserMilestoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserMilestoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Milestone))
	return i, nil
}

func (m *GetLatestUserMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLatestUserMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.MissionId))
	return i, nil
}

func (m *GetLatestUserMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLatestUserMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.UserMission != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintMission(dAtA, i, uint64(m.UserMission.Size()))
		n6, err := m.UserMission.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *UpdateUserMissionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserMissionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintMission(dAtA, i, uint64(len(m.UserMissionIdentifier)))
	i += copy(dAtA[i:], m.UserMissionIdentifier)
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.ExpireTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.BonusExpireTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x28
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.AcceptTime))
	return i, nil
}

func (m *UpdateUserMissionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateUserMissionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.Updated {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetMissionTotalFinishCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMissionTotalFinishCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.MissionId))
	return i, nil
}

func (m *GetMissionTotalFinishCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMissionTotalFinishCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.TotalFinishCount))
	return i, nil
}

func (m *GetMissionTotalCollectCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMissionTotalCollectCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.MissionId))
	return i, nil
}

func (m *GetMissionTotalCollectCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMissionTotalCollectCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.TotalCollectCount))
	return i, nil
}

func (m *RecordUserContinuousDayOperReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserContinuousDayOperReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LyGameId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.ForceAddAccum))
	return i, nil
}

func (m *GetUserContinuousDayOperReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserContinuousDayOperReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetUserContinuousDayOperResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserContinuousDayOperResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LastOperAt))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.OperCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.AccumOperCount))
	return i, nil
}

func (m *ClearUserContinuousDayOperReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearUserContinuousDayOperReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *RecordUserRechargeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RecordUserRechargeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.RechargeSource))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.RechargePenny))
	dAtA[i] = 0x20
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetUserRechargeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRechargeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.RechargeSource))
	dAtA[i] = 0x18
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func (m *GetUserRechargeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRechargeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LastRechargeAt))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.AccumRechargePenny))
	return i, nil
}

func (m *ClearUserRechargeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearUserRechargeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.RechargeSource))
	dAtA[i] = 0x10
	i++
	i = encodeVarintMission(dAtA, i, uint64(m.LyGameId))
	return i, nil
}

func encodeFixed64Mission(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Mission(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintMission(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StUserMission) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.MissionId))
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.StartTime))
	n += 1 + sovMission(uint64(m.ExpireTime))
	n += 1 + sovMission(uint64(m.BonusExpireTime))
	n += 1 + sovMission(uint64(m.Status))
	n += 1 + sovMission(uint64(m.FinishCount))
	l = len(m.Identifier)
	n += 1 + l + sovMission(uint64(l))
	n += 1 + sovMission(uint64(m.RewardCount))
	n += 1 + sovMission(uint64(m.AcceptTime))
	return n
}

func (m *GetUserMissionListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserMissionListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMissionList) > 0 {
		for _, e := range m.UserMissionList {
			l = e.Size()
			n += 1 + l + sovMission(uint64(l))
		}
	}
	return n
}

func (m *GetUserMissionByIdentifierReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.UserMissionIdentifier)
	n += 1 + l + sovMission(uint64(l))
	return n
}

func (m *GetUserMissionByIdentifierResp) Size() (n int) {
	var l int
	_ = l
	if m.UserMission != nil {
		l = m.UserMission.Size()
		n += 1 + l + sovMission(uint64(l))
	}
	return n
}

func (m *GetUserMissionsByIdentifierListReq) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMissionIdentifierList) > 0 {
		for _, s := range m.UserMissionIdentifierList {
			l = len(s)
			n += 1 + l + sovMission(uint64(l))
		}
	}
	return n
}

func (m *GetUserMissionsByIdentifierListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserMissionList) > 0 {
		for _, e := range m.UserMissionList {
			l = e.Size()
			n += 1 + l + sovMission(uint64(l))
		}
	}
	return n
}

func (m *IncreaseUserMissionFinishCountReq) Size() (n int) {
	var l int
	_ = l
	if m.UserMission != nil {
		l = m.UserMission.Size()
		n += 1 + l + sovMission(uint64(l))
	}
	n += 1 + sovMission(uint64(m.FinishCount))
	return n
}

func (m *IncreaseUserMissionFinishCountResp) Size() (n int) {
	var l int
	_ = l
	if m.UserMission != nil {
		l = m.UserMission.Size()
		n += 1 + l + sovMission(uint64(l))
	}
	return n
}

func (m *IncreaseUserMissionToFinishCountReq) Size() (n int) {
	var l int
	_ = l
	if m.UserMission != nil {
		l = m.UserMission.Size()
		n += 1 + l + sovMission(uint64(l))
	}
	n += 1 + sovMission(uint64(m.FinishCount))
	n += 2
	return n
}

func (m *IncreaseUserMissionToFinishCountResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	if m.UserMission != nil {
		l = m.UserMission.Size()
		n += 1 + l + sovMission(uint64(l))
	}
	return n
}

func (m *UpdateUserMissionStatusByIdentifierReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.UserMissionIdentifier)
	n += 1 + l + sovMission(uint64(l))
	n += 1 + sovMission(uint64(m.NewStatus))
	n += 1 + sovMission(uint64(m.AddRewardCount))
	n += 1 + sovMission(uint64(m.ForceUpdateStatus))
	return n
}

func (m *UpdateUserMissionStatusByIdentifierResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateUserMilestoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.NewMilestone))
	return n
}

func (m *UpdateUserMilestoneResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 1 + sovMission(uint64(m.Milestone))
	return n
}

func (m *GetUserMilestoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	return n
}

func (m *GetUserMilestoneResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Milestone))
	return n
}

func (m *GetLatestUserMissionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.MissionId))
	return n
}

func (m *GetLatestUserMissionResp) Size() (n int) {
	var l int
	_ = l
	if m.UserMission != nil {
		l = m.UserMission.Size()
		n += 1 + l + sovMission(uint64(l))
	}
	return n
}

func (m *UpdateUserMissionReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.UserMissionIdentifier)
	n += 1 + l + sovMission(uint64(l))
	n += 1 + sovMission(uint64(m.ExpireTime))
	n += 1 + sovMission(uint64(m.BonusExpireTime))
	n += 1 + sovMission(uint64(m.Status))
	n += 1 + sovMission(uint64(m.AcceptTime))
	return n
}

func (m *UpdateUserMissionResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *GetMissionTotalFinishCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.MissionId))
	return n
}

func (m *GetMissionTotalFinishCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.TotalFinishCount))
	return n
}

func (m *GetMissionTotalCollectCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.MissionId))
	return n
}

func (m *GetMissionTotalCollectCountResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.TotalCollectCount))
	return n
}

func (m *RecordUserContinuousDayOperReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.OperType))
	n += 1 + sovMission(uint64(m.LyGameId))
	n += 1 + sovMission(uint64(m.ForceAddAccum))
	return n
}

func (m *GetUserContinuousDayOperReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.OperType))
	n += 1 + sovMission(uint64(m.LyGameId))
	return n
}

func (m *GetUserContinuousDayOperResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.LastOperAt))
	n += 1 + sovMission(uint64(m.OperCount))
	n += 1 + sovMission(uint64(m.AccumOperCount))
	return n
}

func (m *ClearUserContinuousDayOperReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.OperType))
	n += 1 + sovMission(uint64(m.LyGameId))
	return n
}

func (m *RecordUserRechargeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.RechargeSource))
	n += 1 + sovMission(uint64(m.RechargePenny))
	n += 1 + sovMission(uint64(m.LyGameId))
	return n
}

func (m *GetUserRechargeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.Uid))
	n += 1 + sovMission(uint64(m.RechargeSource))
	n += 1 + sovMission(uint64(m.LyGameId))
	return n
}

func (m *GetUserRechargeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.LastRechargeAt))
	n += 1 + sovMission(uint64(m.AccumRechargePenny))
	return n
}

func (m *ClearUserRechargeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovMission(uint64(m.RechargeSource))
	n += 1 + sovMission(uint64(m.LyGameId))
	return n
}

func sovMission(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozMission(x uint64) (n int) {
	return sovMission(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *StUserMission) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StUserMission: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StUserMission: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BonusExpireTime", wireType)
			}
			m.BonusExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BonusExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishCount", wireType)
			}
			m.FinishCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Identifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Identifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RewardCount", wireType)
			}
			m.RewardCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RewardCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AcceptTime", wireType)
			}
			m.AcceptTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AcceptTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("finish_count")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMissionList = append(m.UserMissionList, &StUserMission{})
			if err := m.UserMissionList[len(m.UserMissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionByIdentifierReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionByIdentifierReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionByIdentifierReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMissionIdentifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMissionIdentifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_mission_identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionByIdentifierResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionByIdentifierResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionByIdentifierResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserMission == nil {
				m.UserMission = &StUserMission{}
			}
			if err := m.UserMission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionsByIdentifierListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionsByIdentifierListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionsByIdentifierListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMissionIdentifierList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMissionIdentifierList = append(m.UserMissionIdentifierList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMissionsByIdentifierListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMissionsByIdentifierListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMissionsByIdentifierListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMissionList = append(m.UserMissionList, &StUserMission{})
			if err := m.UserMissionList[len(m.UserMissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncreaseUserMissionFinishCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncreaseUserMissionFinishCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncreaseUserMissionFinishCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserMission == nil {
				m.UserMission = &StUserMission{}
			}
			if err := m.UserMission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishCount", wireType)
			}
			m.FinishCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_mission")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("finish_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncreaseUserMissionFinishCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncreaseUserMissionFinishCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncreaseUserMissionFinishCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserMission == nil {
				m.UserMission = &StUserMission{}
			}
			if err := m.UserMission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_mission")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncreaseUserMissionToFinishCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncreaseUserMissionToFinishCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncreaseUserMissionToFinishCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserMission == nil {
				m.UserMission = &StUserMission{}
			}
			if err := m.UserMission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FinishCount", wireType)
			}
			m.FinishCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FinishCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceUpdate", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForceUpdate = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_mission")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("finish_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncreaseUserMissionToFinishCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncreaseUserMissionToFinishCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncreaseUserMissionToFinishCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Success", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Success = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserMission == nil {
				m.UserMission = &StUserMission{}
			}
			if err := m.UserMission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("success")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserMissionStatusByIdentifierReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserMissionStatusByIdentifierReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserMissionStatusByIdentifierReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMissionIdentifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMissionIdentifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewStatus", wireType)
			}
			m.NewStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AddRewardCount", wireType)
			}
			m.AddRewardCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AddRewardCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceUpdateStatus", wireType)
			}
			m.ForceUpdateStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ForceUpdateStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_mission_identifier")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("new_status")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserMissionStatusByIdentifierResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserMissionStatusByIdentifierResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserMissionStatusByIdentifierResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserMilestoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserMilestoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserMilestoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NewMilestone", wireType)
			}
			m.NewMilestone = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.NewMilestone |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("new_milestone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserMilestoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserMilestoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserMilestoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Success", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Success = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Milestone", wireType)
			}
			m.Milestone = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Milestone |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("success")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("milestone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMilestoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMilestoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMilestoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserMilestoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserMilestoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserMilestoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Milestone", wireType)
			}
			m.Milestone = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Milestone |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("milestone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLatestUserMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLatestUserMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLatestUserMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLatestUserMissionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLatestUserMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLatestUserMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMission", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.UserMission == nil {
				m.UserMission = &StUserMission{}
			}
			if err := m.UserMission.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserMissionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserMissionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserMissionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserMissionIdentifier", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthMission
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserMissionIdentifier = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BonusExpireTime", wireType)
			}
			m.BonusExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BonusExpireTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AcceptTime", wireType)
			}
			m.AcceptTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AcceptTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_mission_identifier")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateUserMissionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateUserMissionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateUserMissionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Updated", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Updated = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("updated")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMissionTotalFinishCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMissionTotalFinishCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMissionTotalFinishCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMissionTotalFinishCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMissionTotalFinishCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMissionTotalFinishCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalFinishCount", wireType)
			}
			m.TotalFinishCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalFinishCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_finish_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMissionTotalCollectCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMissionTotalCollectCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMissionTotalCollectCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MissionId", wireType)
			}
			m.MissionId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MissionId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mission_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMissionTotalCollectCountResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMissionTotalCollectCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMissionTotalCollectCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCollectCount", wireType)
			}
			m.TotalCollectCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCollectCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_collect_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserContinuousDayOperReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserContinuousDayOperReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserContinuousDayOperReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceAddAccum", wireType)
			}
			m.ForceAddAccum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ForceAddAccum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserContinuousDayOperReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserContinuousDayOperReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserContinuousDayOperReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserContinuousDayOperResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserContinuousDayOperResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserContinuousDayOperResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastOperAt", wireType)
			}
			m.LastOperAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastOperAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperCount", wireType)
			}
			m.OperCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccumOperCount", wireType)
			}
			m.AccumOperCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccumOperCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_oper_at")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("accum_oper_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearUserContinuousDayOperReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearUserContinuousDayOperReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearUserContinuousDayOperReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RecordUserRechargeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RecordUserRechargeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RecordUserRechargeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeSource", wireType)
			}
			m.RechargeSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargePenny", wireType)
			}
			m.RechargePenny = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargePenny |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_source")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_penny")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRechargeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRechargeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRechargeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeSource", wireType)
			}
			m.RechargeSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_source")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRechargeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRechargeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRechargeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastRechargeAt", wireType)
			}
			m.LastRechargeAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastRechargeAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccumRechargePenny", wireType)
			}
			m.AccumRechargePenny = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AccumRechargePenny |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_recharge_at")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("accum_recharge_penny")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearUserRechargeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowMission
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearUserRechargeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearUserRechargeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeSource", wireType)
			}
			m.RechargeSource = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeSource |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LyGameId", wireType)
			}
			m.LyGameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowMission
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LyGameId |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipMission(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthMission
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recharge_source")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipMission(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowMission
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMission
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowMission
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthMission
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowMission
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipMission(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthMission = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowMission   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/missionsvr/mission.proto", fileDescriptorMission) }

var fileDescriptorMission = []byte{
	// 1744 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x58, 0x5f, 0x6f, 0xdb, 0xd6,
	0x15, 0xcf, 0x95, 0x9c, 0xc4, 0x3a, 0x92, 0x6c, 0xf9, 0x26, 0x8e, 0x55, 0xd5, 0x56, 0x18, 0x26,
	0x4d, 0x5c, 0x27, 0x4a, 0x8a, 0x60, 0xe8, 0x30, 0xc3, 0xf0, 0x20, 0xcb, 0xac, 0x22, 0x4c, 0x96,
	0x0c, 0x4a, 0xde, 0x10, 0xb4, 0x01, 0xc1, 0x90, 0x37, 0x2d, 0x57, 0x89, 0x64, 0x79, 0xc9, 0x66,
	0x06, 0xf6, 0x17, 0x18, 0xd0, 0xa2, 0xc0, 0x80, 0x62, 0x6f, 0xdb, 0xb0, 0x97, 0x21, 0xaf, 0x43,
	0x81, 0x7d, 0x82, 0x3d, 0xf6, 0x71, 0x9f, 0x60, 0x18, 0xb2, 0x97, 0xbc, 0x6e, 0x9f, 0x60, 0xb8,
	0xa4, 0x48, 0x5d, 0x4a, 0xa4, 0x4c, 0x23, 0xd9, 0xf6, 0x12, 0x44, 0xf7, 0xfc, 0xee, 0x39, 0xbf,
	0xf3, 0x87, 0xe7, 0x9e, 0x63, 0xd8, 0xa4, 0x8e, 0xf6, 0x60, 0x6c, 0x50, 0x6a, 0x58, 0x26, 0xfd,
	0xdc, 0x09, 0xff, 0x7b, 0xdf, 0x76, 0x2c, 0xd7, 0xc2, 0x97, 0x8f, 0x82, 0x9f, 0xb5, 0x5b, 0x9a,
	0x35, 0x1e, 0x5b, 0xe6, 0x03, 0x77, 0xf4, 0xb9, 0x6d, 0x68, 0x9f, 0x8e, 0xc8, 0x03, 0xfa, 0xe9,
	0x53, 0xcf, 0x18, 0xb9, 0x86, 0xe9, 0x9e, 0xda, 0x24, 0x80, 0x8b, 0x5f, 0xe4, 0xa1, 0x3c, 0x70,
	0x4f, 0x28, 0x71, 0x26, 0xf7, 0xf0, 0x4d, 0x80, 0x89, 0x46, 0xc5, 0xd0, 0xab, 0x48, 0xc8, 0x6d,
	0x97, 0x0f, 0x96, 0xbe, 0xfd, 0xfb, 0xf5, 0x0b, 0x72, 0x61, 0x72, 0xde, 0xd1, 0xf1, 0x35, 0xc8,
	0x7b, 0x86, 0x5e, 0xcd, 0x71, 0x52, 0x76, 0xc0, 0x2e, 0x53, 0x57, 0x75, 0x5c, 0xc5, 0x35, 0xc6,
	0xa4, 0x9a, 0xe7, 0x2f, 0xfb, 0xe7, 0x43, 0x63, 0x4c, 0xf0, 0x3b, 0x50, 0x24, 0x3f, 0xb1, 0x0d,
	0x87, 0x04, 0xa8, 0x25, 0x01, 0x45, 0x28, 0x08, 0x04, 0x3e, 0xec, 0x3d, 0x58, 0x7b, 0x6a, 0x99,
	0x1e, 0x55, 0x78, 0xf0, 0x45, 0x0e, 0xbc, 0xea, 0x8b, 0xa5, 0xe9, 0x8d, 0x4d, 0xb8, 0x44, 0x5d,
	0xd5, 0xf5, 0x68, 0xf5, 0x12, 0x67, 0x79, 0x72, 0x86, 0xef, 0x40, 0xe9, 0x99, 0x61, 0x1a, 0xf4,
	0x13, 0x45, 0xb3, 0x3c, 0xd3, 0xad, 0x5e, 0xe6, 0x30, 0xc5, 0x40, 0xd2, 0x62, 0x02, 0x7c, 0x0b,
	0xc0, 0xd0, 0x89, 0xe9, 0x1a, 0xcf, 0x0c, 0xe2, 0x54, 0x97, 0x85, 0xdc, 0x76, 0x21, 0xa4, 0x37,
	0x3d, 0x67, 0xea, 0x1c, 0xf2, 0x5c, 0x75, 0xf4, 0x89, 0xba, 0x02, 0xc7, 0xac, 0x18, 0x48, 0x02,
	0x75, 0xef, 0x40, 0x51, 0xd5, 0x34, 0x62, 0x4f, 0x82, 0x02, 0xbc, 0xbb, 0x81, 0x80, 0x91, 0x17,
	0x37, 0x60, 0xbd, 0x4d, 0xf8, 0x4c, 0x74, 0x0d, 0xea, 0xca, 0xe4, 0x33, 0xf1, 0x23, 0xb8, 0x96,
	0x24, 0xa0, 0x36, 0x3e, 0x80, 0x35, 0x8f, 0x12, 0x47, 0x09, 0xf3, 0x35, 0x32, 0xa8, 0x5b, 0x45,
	0x42, 0x7e, 0xbb, 0xf8, 0xf0, 0xda, 0xfd, 0x09, 0xf8, 0x7e, 0x2c, 0xbb, 0xf2, 0xaa, 0x17, 0xd7,
	0x23, 0x3e, 0x81, 0xad, 0xb8, 0xf6, 0x83, 0xd3, 0x4e, 0xe4, 0xa4, 0x4c, 0x3e, 0xc3, 0x7b, 0xb0,
	0x11, 0x33, 0xc2, 0x85, 0x06, 0x71, 0xa1, 0x59, 0xe7, 0x14, 0x4f, 0x15, 0x88, 0x1f, 0x42, 0x7d,
	0x91, 0x7a, 0x6a, 0xe3, 0xef, 0x41, 0x89, 0xd7, 0x5f, 0x45, 0x02, 0x5a, 0xc0, 0xbf, 0xc8, 0x99,
	0x11, 0x09, 0x88, 0x71, 0xe5, 0x94, 0xd7, 0x3e, 0x89, 0x1f, 0xfe, 0x3e, 0x6c, 0xa6, 0x38, 0x30,
	0x0d, 0x58, 0x41, 0x7e, 0x2b, 0x91, 0xbf, 0x1f, 0x22, 0x03, 0x6e, 0x9e, 0x69, 0x26, 0x2d, 0x1b,
	0xb9, 0xf3, 0x65, 0xe3, 0x0b, 0x04, 0x37, 0x3a, 0xa6, 0xe6, 0x10, 0x95, 0x12, 0x0e, 0xf8, 0xc1,
	0xb4, 0x3a, 0x99, 0x47, 0xf3, 0x21, 0xcb, 0x65, 0x0c, 0xd9, 0xdc, 0x47, 0x90, 0x4b, 0xf9, 0x08,
	0x44, 0x05, 0xc4, 0xb3, 0x88, 0x24, 0x26, 0x2f, 0x2b, 0x13, 0xf1, 0x2f, 0x08, 0x6e, 0x26, 0x58,
	0x18, 0x5a, 0xff, 0x7b, 0x67, 0x7d, 0xa0, 0xe5, 0x68, 0x44, 0xf1, 0x6c, 0x5d, 0x75, 0x59, 0xe3,
	0x42, 0xdb, 0xcb, 0x11, 0x90, 0x49, 0x4e, 0x7c, 0x81, 0xf8, 0x2b, 0x04, 0xb7, 0xce, 0x26, 0x4d,
	0x6d, 0x5c, 0x87, 0xcb, 0xd4, 0xd3, 0x34, 0x42, 0xa9, 0x4f, 0x38, 0x54, 0x16, 0x1e, 0xce, 0x79,
	0x95, 0xcb, 0x5e, 0xf5, 0xff, 0x42, 0x70, 0x3b, 0xa0, 0xc3, 0x41, 0x06, 0x7e, 0x8f, 0x7b, 0xa3,
	0xdf, 0x2e, 0x6b, 0xe6, 0x26, 0x79, 0xae, 0x4c, 0x5a, 0x2a, 0x1f, 0xbc, 0x82, 0x49, 0x9e, 0x07,
	0x16, 0xf1, 0x7d, 0xa8, 0xa8, 0xba, 0xae, 0xc4, 0x5a, 0x61, 0x9e, 0x6b, 0x71, 0x2b, 0xaa, 0xae,
	0xcb, 0x5c, 0x37, 0xfc, 0x0e, 0x5c, 0xe1, 0x43, 0x1d, 0x6a, 0xe7, 0x1f, 0x81, 0x35, 0x2e, 0xe2,
	0x81, 0x15, 0xf1, 0x5d, 0xb8, 0x93, 0xc9, 0x65, 0x6a, 0x8b, 0x1f, 0xc2, 0x35, 0x1e, 0x3a, 0x22,
	0xd4, 0xb5, 0x4c, 0xc2, 0xa2, 0x31, 0x79, 0xb4, 0xd0, 0xec, 0xa3, 0xf5, 0x2e, 0x94, 0x99, 0x9f,
	0xe3, 0x10, 0x1b, 0x73, 0xb5, 0x64, 0x92, 0xe7, 0x91, 0x16, 0xf1, 0x09, 0x6c, 0x24, 0x2a, 0xcf,
	0x90, 0x71, 0x11, 0x0a, 0xc9, 0x16, 0xa6, 0xc7, 0x62, 0x03, 0xae, 0x44, 0x9d, 0xe6, 0x6c, 0xe2,
	0xe2, 0x2e, 0x5c, 0x9d, 0x87, 0x53, 0x3b, 0x6e, 0x0a, 0x25, 0x9b, 0xfa, 0x21, 0x6c, 0xb4, 0x89,
	0xdb, 0x55, 0x5d, 0x42, 0x63, 0xa5, 0xb6, 0x20, 0x4e, 0xf1, 0xc9, 0x20, 0x97, 0x38, 0x19, 0x88,
	0x27, 0x50, 0x4d, 0xd6, 0xfb, 0x7a, 0xad, 0xfe, 0xdf, 0x08, 0xae, 0xce, 0x55, 0xc0, 0xeb, 0x97,
	0xf8, 0xcc, 0x28, 0x92, 0x3b, 0xcf, 0x28, 0x92, 0xcf, 0x36, 0x8a, 0xf0, 0x95, 0x1d, 0x8e, 0x22,
	0x33, 0x23, 0xc1, 0xc5, 0x94, 0x91, 0xe0, 0xbb, 0xb0, 0x9e, 0xe0, 0x73, 0x50, 0x6b, 0xc1, 0xe7,
	0xa3, 0xc7, 0x6b, 0x6d, 0x72, 0x28, 0x7e, 0xe4, 0x3f, 0xea, 0x51, 0x73, 0x72, 0xd5, 0xd1, 0x4c,
	0x53, 0x7d, 0xad, 0x14, 0x0f, 0xfd, 0x37, 0x3d, 0x55, 0x3b, 0xb5, 0xf1, 0x43, 0xc0, 0x2e, 0x3b,
	0x57, 0x62, 0xed, 0x97, 0xb7, 0x56, 0x71, 0x67, 0xee, 0x89, 0x4f, 0xe6, 0xb4, 0xb6, 0xac, 0xd1,
	0x88, 0x68, 0xee, 0x9b, 0x21, 0xfd, 0x23, 0xb8, 0xbe, 0x50, 0x3d, 0xb5, 0x59, 0x6b, 0x0a, 0x58,
	0x6b, 0x81, 0x24, 0x81, 0xf6, 0x9a, 0x3b, 0x7b, 0x53, 0xfc, 0x33, 0x82, 0xba, 0x4c, 0x34, 0xcb,
	0xd1, 0x59, 0x96, 0x5a, 0x96, 0xe9, 0x1a, 0xa6, 0x67, 0x79, 0xf4, 0x50, 0x3d, 0xed, 0xdb, 0x41,
	0x1b, 0x4e, 0x23, 0x7e, 0x03, 0x0a, 0x96, 0x4d, 0x1c, 0x85, 0xcd, 0xe3, 0x31, 0xde, 0xcb, 0xec,
	0x78, 0x78, 0x6a, 0x13, 0x2c, 0x02, 0x8c, 0x4e, 0x95, 0x8f, 0xd5, 0x31, 0x61, 0xbe, 0xb1, 0x92,
	0x5b, 0x0a, 0x31, 0xa3, 0xd3, 0xb6, 0x3a, 0x26, 0x1d, 0x1d, 0xdf, 0x83, 0xd5, 0xa0, 0xa5, 0xb2,
	0x46, 0xac, 0x6a, 0x9a, 0x37, 0x8e, 0x15, 0x5d, 0xd9, 0x17, 0x36, 0x75, 0xbd, 0xc9, 0x44, 0xe2,
	0x4f, 0xe1, 0xed, 0x49, 0xd3, 0xf8, 0x3f, 0x70, 0x15, 0xff, 0x80, 0x60, 0x33, 0xdd, 0x3c, 0xb5,
	0xf1, 0x6d, 0x28, 0x8d, 0x54, 0xea, 0x2a, 0xbe, 0x31, 0x35, 0x1e, 0x7d, 0x60, 0x12, 0x86, 0x6c,
	0xba, 0x2c, 0xe9, 0x3e, 0x64, 0xfe, 0x65, 0xf7, 0x79, 0x06, 0x8f, 0x0d, 0x7b, 0x9c, 0x98, 0xd3,
	0x0a, 0x07, 0xe5, 0x97, 0x92, 0x15, 0x5f, 0xda, 0x0f, 0xf1, 0xe2, 0x33, 0xd8, 0x6a, 0x8d, 0x88,
	0xea, 0xa4, 0x46, 0x27, 0x16, 0x05, 0x94, 0x21, 0x0a, 0xb9, 0xc4, 0x28, 0x7c, 0x83, 0x60, 0x7d,
	0x5a, 0x33, 0x32, 0xd1, 0x3e, 0x51, 0x9d, 0x8f, 0x17, 0xbe, 0x51, 0x0d, 0x58, 0x75, 0x26, 0x30,
	0x85, 0x5a, 0x9e, 0xa3, 0xc5, 0x93, 0xb0, 0x12, 0x0a, 0x07, 0xbe, 0x0c, 0xdf, 0x85, 0xe8, 0x44,
	0xb1, 0x89, 0x69, 0x9e, 0xc6, 0xdc, 0x2e, 0x87, 0xb2, 0x63, 0x26, 0x9a, 0x61, 0xbc, 0x94, 0xc8,
	0xf8, 0x17, 0x80, 0x27, 0x69, 0xfb, 0x2f, 0xb0, 0xcd, 0x52, 0x38, 0x3f, 0x8b, 0x9e, 0xc6, 0x29,
	0x01, 0x6a, 0xb3, 0x0c, 0xfb, 0xe5, 0x12, 0x99, 0x9b, 0x29, 0x99, 0x15, 0x26, 0x0d, 0x6f, 0x34,
	0x5d, 0xfc, 0x3e, 0x5c, 0x0d, 0x2a, 0x62, 0x26, 0x3c, 0x3c, 0x3d, 0xec, 0x23, 0x64, 0x3e, 0x46,
	0xa2, 0x01, 0x57, 0xa3, 0xca, 0xe0, 0x23, 0x90, 0xe0, 0x29, 0xca, 0xec, 0x69, 0x62, 0x71, 0xec,
	0xb4, 0xe0, 0xca, 0xc9, 0x40, 0x92, 0x95, 0xa3, 0xce, 0x60, 0xd0, 0xe9, 0xf7, 0x94, 0xc1, 0xb0,
	0x39, 0x3c, 0x19, 0xe0, 0x55, 0x28, 0x76, 0x7a, 0xca, 0xb1, 0xdc, 0x6f, 0xcb, 0xd2, 0x60, 0x50,
	0x41, 0xb8, 0x04, 0xcb, 0x1f, 0x74, 0x7a, 0x9d, 0xc1, 0x23, 0xe9, 0xb0, 0x92, 0xc3, 0x65, 0x28,
	0xb4, 0xfa, 0xdd, 0xae, 0xd4, 0x1a, 0x4a, 0x87, 0x95, 0xfc, 0xce, 0xfb, 0x50, 0x38, 0xea, 0x74,
	0xa5, 0xc1, 0xb0, 0xdf, 0x93, 0x30, 0x86, 0x95, 0xe8, 0x87, 0xd2, 0xeb, 0xf7, 0xa4, 0x0a, 0xc2,
	0xeb, 0xb0, 0xd6, 0x96, 0x25, 0xa9, 0x27, 0xc9, 0x4a, 0xab, 0x7f, 0x74, 0xdc, 0x95, 0xd8, 0xbd,
	0xdc, 0xce, 0x10, 0xca, 0x87, 0xcd, 0xc7, 0x4a, 0xff, 0x58, 0x92, 0x95, 0xe1, 0xe3, 0x63, 0x09,
	0xaf, 0x00, 0xb4, 0x9b, 0x47, 0x92, 0xd2, 0xed, 0xb7, 0x3b, 0xbd, 0x0a, 0xc2, 0x6b, 0x50, 0xf6,
	0x7f, 0xcb, 0x52, 0xeb, 0x51, 0x53, 0x6e, 0x4b, 0x95, 0x1c, 0x23, 0x32, 0x1c, 0x4e, 0x00, 0x79,
	0x66, 0xac, 0x7d, 0xd2, 0xe9, 0x1e, 0x2a, 0xad, 0x47, 0x52, 0xeb, 0x07, 0x4a, 0xa7, 0x57, 0x59,
	0xda, 0x69, 0xc0, 0x6a, 0x88, 0x57, 0x06, 0xfd, 0x13, 0xb9, 0x25, 0xe1, 0x65, 0x58, 0x62, 0x7a,
	0x2a, 0x08, 0x57, 0xa0, 0xf4, 0xa8, 0x79, 0x7c, 0xfc, 0x58, 0x69, 0x49, 0xbd, 0xa1, 0x24, 0x57,
	0x72, 0x0f, 0xff, 0x5a, 0x86, 0xf0, 0xcf, 0x18, 0xd8, 0x8d, 0x0a, 0x8f, 0xdb, 0x93, 0x70, 0x3d,
	0x9a, 0x19, 0x12, 0x77, 0xe6, 0xda, 0xf5, 0x85, 0x72, 0x6a, 0x8b, 0x5b, 0xbf, 0x7c, 0xf1, 0x2a,
	0x9f, 0xfb, 0x8a, 0xfd, 0xe3, 0xed, 0xfe, 0xf6, 0xc5, 0xab, 0x7c, 0xa9, 0xe1, 0x09, 0x7b, 0xfe,
	0x24, 0x61, 0xe8, 0xfb, 0xf8, 0xe7, 0x50, 0x4b, 0x5f, 0x5b, 0xf1, 0xed, 0x14, 0xed, 0x33, 0xe3,
	0x77, 0xed, 0x4e, 0x26, 0x1c, 0xb5, 0xc5, 0x55, 0xc6, 0x26, 0xcf, 0xd8, 0x5c, 0x60, 0x5c, 0x2e,
	0xe0, 0xdf, 0x20, 0xff, 0xb9, 0x5a, 0xb4, 0x73, 0xe2, 0xbb, 0x29, 0xda, 0x93, 0x96, 0xe0, 0xda,
	0xbd, 0xec, 0xe0, 0x90, 0xcf, 0x12, 0xc7, 0xe7, 0x2b, 0x04, 0xf5, 0xc5, 0xeb, 0x20, 0xde, 0x89,
	0x2c, 0x9c, 0xb9, 0xc0, 0xd6, 0xee, 0x66, 0xc6, 0x86, 0x64, 0x2e, 0x72, 0x64, 0x7e, 0x87, 0xe0,
	0x66, 0x86, 0x6d, 0x00, 0x3f, 0x88, 0xac, 0x64, 0x5b, 0x97, 0x6a, 0xef, 0x9d, 0xef, 0x42, 0xc8,
	0xed, 0x12, 0xc7, 0xed, 0x6b, 0x04, 0xc2, 0x59, 0x0b, 0x22, 0xbe, 0xb7, 0xc8, 0xfd, 0xd9, 0x05,
	0xb8, 0xd6, 0x38, 0x07, 0x3a, 0xa4, 0x74, 0x99, 0xa3, 0xf4, 0x6b, 0x04, 0x57, 0x12, 0x96, 0x16,
	0x7c, 0x3d, 0xd1, 0xdb, 0xe9, 0xda, 0x51, 0x13, 0x16, 0x03, 0xa8, 0x2d, 0xee, 0x30, 0x5b, 0xcb,
	0xcc, 0xd6, 0x92, 0xb7, 0x3b, 0xf6, 0xbf, 0xa3, 0x0d, 0xff, 0x3b, 0x32, 0xf4, 0x7d, 0xa1, 0x31,
	0x16, 0xf6, 0xa2, 0x7d, 0x63, 0x1f, 0xff, 0x18, 0x2a, 0xb3, 0xcb, 0x0a, 0xde, 0x9c, 0xaf, 0x4a,
	0xce, 0xfe, 0xd6, 0x02, 0x29, 0xb5, 0xc5, 0xb7, 0x98, 0xf1, 0x02, 0xf7, 0x09, 0x2f, 0x87, 0xa6,
	0xf1, 0x97, 0xc8, 0xdf, 0x8c, 0xe6, 0xb6, 0x10, 0x2c, 0xf0, 0x2a, 0x93, 0x96, 0x9f, 0xda, 0x8d,
	0x33, 0x10, 0xd4, 0x16, 0xef, 0x32, 0xc3, 0x10, 0xf3, 0xba, 0x3a, 0xe3, 0x75, 0x38, 0x8d, 0xee,
	0xe3, 0x6f, 0x10, 0xac, 0xcd, 0x55, 0x13, 0xde, 0x4a, 0xaf, 0x34, 0x46, 0xa2, 0xbe, 0x48, 0xcc,
	0x76, 0x5c, 0xc6, 0xa0, 0xc8, 0x18, 0x00, 0x63, 0x40, 0x76, 0x9f, 0xee, 0x52, 0x9f, 0xc7, 0x41,
	0x1a, 0x0f, 0xa1, 0x41, 0x84, 0xbd, 0x60, 0x7b, 0xd9, 0x17, 0x1a, 0x4f, 0x85, 0x3d, 0x7e, 0x9f,
	0xd9, 0x17, 0x1a, 0x54, 0xd8, 0x0b, 0x76, 0x93, 0x7d, 0xfc, 0x7b, 0xe4, 0x37, 0xbf, 0x94, 0xf9,
	0x3e, 0xde, 0xfc, 0xd2, 0x57, 0x8c, 0x78, 0xf3, 0x5b, 0xb0, 0x2c, 0x04, 0xe1, 0x2c, 0x65, 0x0c,
	0xe7, 0x1f, 0x91, 0x3f, 0xbe, 0xa6, 0xcd, 0xf1, 0x38, 0xd5, 0xea, 0xcc, 0x32, 0x51, 0xdb, 0xce,
	0x06, 0x0c, 0xf9, 0xbd, 0x9d, 0x8d, 0x5f, 0xed, 0xd2, 0x97, 0x2f, 0x5e, 0xe5, 0xff, 0xe4, 0x1d,
	0x54, 0xbe, 0x7d, 0x59, 0x47, 0x7f, 0x7b, 0x59, 0x47, 0xff, 0x78, 0x59, 0x47, 0x5f, 0xff, 0xb3,
	0x7e, 0xe1, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x24, 0xc6, 0xcd, 0x65, 0xb7, 0x17, 0x00, 0x00,
}
