package Mission

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/missionsvr/mission.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for Mission service
const MissionMagic = uint16(15010)

// SvrkitClient API for Mission service

type MissionClientInterface interface {
	// 获取用户的任务列表
	GetUserMissionList(ctx context.Context, uin uint32, in *GetUserMissionListReq, opts ...svrkit.CallOption) (*GetUserMissionListResp, error)
	// 单查用户任务
	GetUserMissionByIdentifier(ctx context.Context, uin uint32, in *GetUserMissionByIdentifierReq, opts ...svrkit.CallOption) (*GetUserMissionByIdentifierResp, error)
	// 批量查用户任务
	GetUserMissionsByIdentifierList(ctx context.Context, uin uint32, in *GetUserMissionsByIdentifierListReq, opts ...svrkit.CallOption) (*GetUserMissionsByIdentifierListResp, error)
	// 增加任务完成次数
	IncreaseUserMissionFinishCount(ctx context.Context, uin uint32, in *IncreaseUserMissionFinishCountReq, opts ...svrkit.CallOption) (*IncreaseUserMissionFinishCountResp, error)
	UpdateUserMissionStatusByIdentifier(ctx context.Context, uin uint32, in *UpdateUserMissionStatusByIdentifierReq, opts ...svrkit.CallOption) (*UpdateUserMissionStatusByIdentifierResp, error)
	IncreaseUserMissionToFinishCount(ctx context.Context, uin uint32, in *IncreaseUserMissionToFinishCountReq, opts ...svrkit.CallOption) (*IncreaseUserMissionToFinishCountResp, error)
	UpdateUserMilestone(ctx context.Context, uin uint32, in *UpdateUserMilestoneReq, opts ...svrkit.CallOption) (*UpdateUserMilestoneResp, error)
	GetUserMilestone(ctx context.Context, uin uint32, in *GetUserMilestoneReq, opts ...svrkit.CallOption) (*GetUserMilestoneResp, error)
	GetLatestUserMission(ctx context.Context, uin uint32, in *GetLatestUserMissionReq, opts ...svrkit.CallOption) (*GetLatestUserMissionResp, error)
	UpdateUserMission(ctx context.Context, uin uint32, in *UpdateUserMissionReq, opts ...svrkit.CallOption) (*UpdateUserMissionResp, error)
	GetMissionTotalFinishCount(ctx context.Context, uin uint32, in *GetMissionTotalFinishCountReq, opts ...svrkit.CallOption) (*GetMissionTotalFinishCountResp, error)
	GetMissionTotalCollectCount(ctx context.Context, uin uint32, in *GetMissionTotalCollectCountReq, opts ...svrkit.CallOption) (*GetMissionTotalCollectCountResp, error)
}

type MissionSvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewMissionSvrkitClient(cc *svrkit.ClientConn) MissionClientInterface {
	return &MissionSvrkitClient{cc}
}

const (
	commandMissionGetSelfSvnInfo                      = 9995
	commandMissionEcho                                = 9999
	commandMissionGetUserMissionList                  = 2
	commandMissionGetUserMissionByIdentifier          = 3
	commandMissionGetUserMissionsByIdentifierList     = 4
	commandMissionIncreaseUserMissionFinishCount      = 5
	commandMissionUpdateUserMissionStatusByIdentifier = 6
	commandMissionIncreaseUserMissionToFinishCount    = 7
	commandMissionUpdateUserMilestone                 = 8
	commandMissionGetUserMilestone                    = 9
	commandMissionGetLatestUserMission                = 10
	commandMissionUpdateUserMission                   = 11
	commandMissionGetMissionTotalFinishCount          = 12
	commandMissionGetMissionTotalCollectCount         = 27
)

func (c *MissionSvrkitClient) GetUserMissionList(ctx context.Context, uin uint32, in *GetUserMissionListReq, opts ...svrkit.CallOption) (*GetUserMissionListResp, error) {
	out := new(GetUserMissionListResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetUserMissionList, "/Mission.Mission/GetUserMissionList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) GetUserMissionByIdentifier(ctx context.Context, uin uint32, in *GetUserMissionByIdentifierReq, opts ...svrkit.CallOption) (*GetUserMissionByIdentifierResp, error) {
	out := new(GetUserMissionByIdentifierResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetUserMissionByIdentifier, "/Mission.Mission/GetUserMissionByIdentifier", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) GetUserMissionsByIdentifierList(ctx context.Context, uin uint32, in *GetUserMissionsByIdentifierListReq, opts ...svrkit.CallOption) (*GetUserMissionsByIdentifierListResp, error) {
	out := new(GetUserMissionsByIdentifierListResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetUserMissionsByIdentifierList, "/Mission.Mission/GetUserMissionsByIdentifierList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) IncreaseUserMissionFinishCount(ctx context.Context, uin uint32, in *IncreaseUserMissionFinishCountReq, opts ...svrkit.CallOption) (*IncreaseUserMissionFinishCountResp, error) {
	out := new(IncreaseUserMissionFinishCountResp)
	err := c.cc.Invoke(ctx, uin, commandMissionIncreaseUserMissionFinishCount, "/Mission.Mission/IncreaseUserMissionFinishCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) UpdateUserMissionStatusByIdentifier(ctx context.Context, uin uint32, in *UpdateUserMissionStatusByIdentifierReq, opts ...svrkit.CallOption) (*UpdateUserMissionStatusByIdentifierResp, error) {
	out := new(UpdateUserMissionStatusByIdentifierResp)
	err := c.cc.Invoke(ctx, uin, commandMissionUpdateUserMissionStatusByIdentifier, "/Mission.Mission/UpdateUserMissionStatusByIdentifier", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) IncreaseUserMissionToFinishCount(ctx context.Context, uin uint32, in *IncreaseUserMissionToFinishCountReq, opts ...svrkit.CallOption) (*IncreaseUserMissionToFinishCountResp, error) {
	out := new(IncreaseUserMissionToFinishCountResp)
	err := c.cc.Invoke(ctx, uin, commandMissionIncreaseUserMissionToFinishCount, "/Mission.Mission/IncreaseUserMissionToFinishCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) UpdateUserMilestone(ctx context.Context, uin uint32, in *UpdateUserMilestoneReq, opts ...svrkit.CallOption) (*UpdateUserMilestoneResp, error) {
	out := new(UpdateUserMilestoneResp)
	err := c.cc.Invoke(ctx, uin, commandMissionUpdateUserMilestone, "/Mission.Mission/UpdateUserMilestone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) GetUserMilestone(ctx context.Context, uin uint32, in *GetUserMilestoneReq, opts ...svrkit.CallOption) (*GetUserMilestoneResp, error) {
	out := new(GetUserMilestoneResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetUserMilestone, "/Mission.Mission/GetUserMilestone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) GetLatestUserMission(ctx context.Context, uin uint32, in *GetLatestUserMissionReq, opts ...svrkit.CallOption) (*GetLatestUserMissionResp, error) {
	out := new(GetLatestUserMissionResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetLatestUserMission, "/Mission.Mission/GetLatestUserMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) UpdateUserMission(ctx context.Context, uin uint32, in *UpdateUserMissionReq, opts ...svrkit.CallOption) (*UpdateUserMissionResp, error) {
	out := new(UpdateUserMissionResp)
	err := c.cc.Invoke(ctx, uin, commandMissionUpdateUserMission, "/Mission.Mission/UpdateUserMission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) GetMissionTotalFinishCount(ctx context.Context, uin uint32, in *GetMissionTotalFinishCountReq, opts ...svrkit.CallOption) (*GetMissionTotalFinishCountResp, error) {
	out := new(GetMissionTotalFinishCountResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetMissionTotalFinishCount, "/Mission.Mission/GetMissionTotalFinishCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *MissionSvrkitClient) GetMissionTotalCollectCount(ctx context.Context, uin uint32, in *GetMissionTotalCollectCountReq, opts ...svrkit.CallOption) (*GetMissionTotalCollectCountResp, error) {
	out := new(GetMissionTotalCollectCountResp)
	err := c.cc.Invoke(ctx, uin, commandMissionGetMissionTotalCollectCount, "/Mission.Mission/GetMissionTotalCollectCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
