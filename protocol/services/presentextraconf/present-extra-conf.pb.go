// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/presentextraconf/present-extra-conf.proto

package presentextraconf // import "golang.52tt.com/protocol/services/presentextraconf"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EffectStatus int32

const (
	EffectStatus_EffectStatus_Unknown   EffectStatus = 0
	EffectStatus_EffectStatus_Waiting   EffectStatus = 1
	EffectStatus_EffectStatus_Effective EffectStatus = 2
	EffectStatus_EffectStatus_Outdated  EffectStatus = 3
)

var EffectStatus_name = map[int32]string{
	0: "EffectStatus_Unknown",
	1: "EffectStatus_Waiting",
	2: "EffectStatus_Effective",
	3: "EffectStatus_Outdated",
}
var EffectStatus_value = map[string]int32{
	"EffectStatus_Unknown":   0,
	"EffectStatus_Waiting":   1,
	"EffectStatus_Effective": 2,
	"EffectStatus_Outdated":  3,
}

func (x EffectStatus) String() string {
	return proto.EnumName(EffectStatus_name, int32(x))
}
func (EffectStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{0}
}

type CustomPreviewType int32

const (
	CustomPreviewType_CustomPreviewImg CustomPreviewType = 0
	CustomPreviewType_CustomPreviewMp4 CustomPreviewType = 1
)

var CustomPreviewType_name = map[int32]string{
	0: "CustomPreviewImg",
	1: "CustomPreviewMp4",
}
var CustomPreviewType_value = map[string]int32{
	"CustomPreviewImg": 0,
	"CustomPreviewMp4": 1,
}

func (x CustomPreviewType) String() string {
	return proto.EnumName(CustomPreviewType_name, int32(x))
}
func (CustomPreviewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{1}
}

type ValueType int32

const (
	ValueType_ValueTypeNone ValueType = 0
	ValueType_ValueTypeRich ValueType = 1
)

var ValueType_name = map[int32]string{
	0: "ValueTypeNone",
	1: "ValueTypeRich",
}
var ValueType_value = map[string]int32{
	"ValueTypeNone": 0,
	"ValueTypeRich": 1,
}

func (x ValueType) String() string {
	return proto.EnumName(ValueType_name, int32(x))
}
func (ValueType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{2}
}

type PresentFloatLayer_ChannelType int32

const (
	PresentFloatLayer_INVALID_CHANNEL_TYPE          PresentFloatLayer_ChannelType = 0
	PresentFloatLayer_GUILD_TYPE                    PresentFloatLayer_ChannelType = 1
	PresentFloatLayer_FREE_TYPE                     PresentFloatLayer_ChannelType = 2
	PresentFloatLayer_USER_CHANNEL_TYPE             PresentFloatLayer_ChannelType = 3
	PresentFloatLayer_GUILD_PUBLIC_FUN_CHANNEL_TYPE PresentFloatLayer_ChannelType = 4
	PresentFloatLayer_TRIVIA_GAME_CHANNEL_TYPE      PresentFloatLayer_ChannelType = 5
	PresentFloatLayer_TEMP_KH_CHANNEL_TYPE          PresentFloatLayer_ChannelType = 6
	PresentFloatLayer_RADIO_LIVE_CHANNEL_TYPE       PresentFloatLayer_ChannelType = 7
	PresentFloatLayer_GUILD_HOME_CHANNEL_TYPE       PresentFloatLayer_ChannelType = 8
	PresentFloatLayer_OFFICIAL_LIVE_CHANNEL_TYPE    PresentFloatLayer_ChannelType = 9
	PresentFloatLayer_CPL_SUPER_CHANNEL_TYPE        PresentFloatLayer_ChannelType = 10
	PresentFloatLayer_COMMUNITY_CHANNEL_TYPE        PresentFloatLayer_ChannelType = 11
)

var PresentFloatLayer_ChannelType_name = map[int32]string{
	0:  "INVALID_CHANNEL_TYPE",
	1:  "GUILD_TYPE",
	2:  "FREE_TYPE",
	3:  "USER_CHANNEL_TYPE",
	4:  "GUILD_PUBLIC_FUN_CHANNEL_TYPE",
	5:  "TRIVIA_GAME_CHANNEL_TYPE",
	6:  "TEMP_KH_CHANNEL_TYPE",
	7:  "RADIO_LIVE_CHANNEL_TYPE",
	8:  "GUILD_HOME_CHANNEL_TYPE",
	9:  "OFFICIAL_LIVE_CHANNEL_TYPE",
	10: "CPL_SUPER_CHANNEL_TYPE",
	11: "COMMUNITY_CHANNEL_TYPE",
}
var PresentFloatLayer_ChannelType_value = map[string]int32{
	"INVALID_CHANNEL_TYPE":          0,
	"GUILD_TYPE":                    1,
	"FREE_TYPE":                     2,
	"USER_CHANNEL_TYPE":             3,
	"GUILD_PUBLIC_FUN_CHANNEL_TYPE": 4,
	"TRIVIA_GAME_CHANNEL_TYPE":      5,
	"TEMP_KH_CHANNEL_TYPE":          6,
	"RADIO_LIVE_CHANNEL_TYPE":       7,
	"GUILD_HOME_CHANNEL_TYPE":       8,
	"OFFICIAL_LIVE_CHANNEL_TYPE":    9,
	"CPL_SUPER_CHANNEL_TYPE":        10,
	"COMMUNITY_CHANNEL_TYPE":        11,
}

func (x PresentFloatLayer_ChannelType) String() string {
	return proto.EnumName(PresentFloatLayer_ChannelType_name, int32(x))
}
func (PresentFloatLayer_ChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{13, 0}
}

type PresentFloatLayer_AppType int32

const (
	PresentFloatLayer_APP_TYPE_INVALID         PresentFloatLayer_AppType = 0
	PresentFloatLayer_APP_TYPE_PC              PresentFloatLayer_AppType = 1
	PresentFloatLayer_APP_TYPE_TT_ANDROID      PresentFloatLayer_AppType = 2
	PresentFloatLayer_APP_TYPE_TT_IOS          PresentFloatLayer_AppType = 3
	PresentFloatLayer_APP_TYPE_HUANYOU_IOS     PresentFloatLayer_AppType = 4
	PresentFloatLayer_APP_TYPE_HUANYOU_ANDROID PresentFloatLayer_AppType = 5
	PresentFloatLayer_APP_TYPE_MAIKE_IOS       PresentFloatLayer_AppType = 6
	PresentFloatLayer_APP_TYPE_MAIKE_ANDROID   PresentFloatLayer_AppType = 7
)

var PresentFloatLayer_AppType_name = map[int32]string{
	0: "APP_TYPE_INVALID",
	1: "APP_TYPE_PC",
	2: "APP_TYPE_TT_ANDROID",
	3: "APP_TYPE_TT_IOS",
	4: "APP_TYPE_HUANYOU_IOS",
	5: "APP_TYPE_HUANYOU_ANDROID",
	6: "APP_TYPE_MAIKE_IOS",
	7: "APP_TYPE_MAIKE_ANDROID",
}
var PresentFloatLayer_AppType_value = map[string]int32{
	"APP_TYPE_INVALID":         0,
	"APP_TYPE_PC":              1,
	"APP_TYPE_TT_ANDROID":      2,
	"APP_TYPE_TT_IOS":          3,
	"APP_TYPE_HUANYOU_IOS":     4,
	"APP_TYPE_HUANYOU_ANDROID": 5,
	"APP_TYPE_MAIKE_IOS":       6,
	"APP_TYPE_MAIKE_ANDROID":   7,
}

func (x PresentFloatLayer_AppType) String() string {
	return proto.EnumName(PresentFloatLayer_AppType_name, int32(x))
}
func (PresentFloatLayer_AppType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{13, 1}
}

type PresentFloatLayer_ActivityType int32

const (
	PresentFloatLayer_ACTIVITY_TYPE_INVALID PresentFloatLayer_ActivityType = 0
	PresentFloatLayer_ACTIVITY_TYPE_USER    PresentFloatLayer_ActivityType = 1
	PresentFloatLayer_ACTIVITY_TYPE_REVENUE PresentFloatLayer_ActivityType = 2
)

var PresentFloatLayer_ActivityType_name = map[int32]string{
	0: "ACTIVITY_TYPE_INVALID",
	1: "ACTIVITY_TYPE_USER",
	2: "ACTIVITY_TYPE_REVENUE",
}
var PresentFloatLayer_ActivityType_value = map[string]int32{
	"ACTIVITY_TYPE_INVALID": 0,
	"ACTIVITY_TYPE_USER":    1,
	"ACTIVITY_TYPE_REVENUE": 2,
}

func (x PresentFloatLayer_ActivityType) String() string {
	return proto.EnumName(PresentFloatLayer_ActivityType_name, int32(x))
}
func (PresentFloatLayer_ActivityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{13, 2}
}

type PresentFloatLayer_SubActivityType int32

const (
	//     1. 营收福利活动
	//    2. 付费人数活动
	//    3. 主播玩法活动
	//    4. 大型赛事活动
	PresentFloatLayer_SUB_ACTIVITY_TYPE_INVALID         PresentFloatLayer_SubActivityType = 0
	PresentFloatLayer_SUB_ACTIVITY_TYPE_REVENUE_BENEFIT PresentFloatLayer_SubActivityType = 1
	PresentFloatLayer_SUB_ACTIVITY_TYPE_PAY             PresentFloatLayer_SubActivityType = 2
	PresentFloatLayer_SUB_ACTIVITY_TYPE_ANCHOR          PresentFloatLayer_SubActivityType = 3
	PresentFloatLayer_SUB_ACTIVITY_TYPE_BIG_EVENT       PresentFloatLayer_SubActivityType = 4
)

var PresentFloatLayer_SubActivityType_name = map[int32]string{
	0: "SUB_ACTIVITY_TYPE_INVALID",
	1: "SUB_ACTIVITY_TYPE_REVENUE_BENEFIT",
	2: "SUB_ACTIVITY_TYPE_PAY",
	3: "SUB_ACTIVITY_TYPE_ANCHOR",
	4: "SUB_ACTIVITY_TYPE_BIG_EVENT",
}
var PresentFloatLayer_SubActivityType_value = map[string]int32{
	"SUB_ACTIVITY_TYPE_INVALID":         0,
	"SUB_ACTIVITY_TYPE_REVENUE_BENEFIT": 1,
	"SUB_ACTIVITY_TYPE_PAY":             2,
	"SUB_ACTIVITY_TYPE_ANCHOR":          3,
	"SUB_ACTIVITY_TYPE_BIG_EVENT":       4,
}

func (x PresentFloatLayer_SubActivityType) String() string {
	return proto.EnumName(PresentFloatLayer_SubActivityType_name, int32(x))
}
func (PresentFloatLayer_SubActivityType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{13, 3}
}

type AppUrl_UrlType int32

const (
	AppUrl_URL_TYPE_INVALID         AppUrl_UrlType = 0
	AppUrl_URL_TYPE_TT              AppUrl_UrlType = 1
	AppUrl_URL_TYPE_HUANYOU_IOS     AppUrl_UrlType = 2
	AppUrl_URL_TYPE_HUANYOU_ANDROID AppUrl_UrlType = 3
	AppUrl_URL_TYPE_MAIKE_IOS       AppUrl_UrlType = 4
	AppUrl_URL_TYPE_MAIKE_ANDROID   AppUrl_UrlType = 5
)

var AppUrl_UrlType_name = map[int32]string{
	0: "URL_TYPE_INVALID",
	1: "URL_TYPE_TT",
	2: "URL_TYPE_HUANYOU_IOS",
	3: "URL_TYPE_HUANYOU_ANDROID",
	4: "URL_TYPE_MAIKE_IOS",
	5: "URL_TYPE_MAIKE_ANDROID",
}
var AppUrl_UrlType_value = map[string]int32{
	"URL_TYPE_INVALID":         0,
	"URL_TYPE_TT":              1,
	"URL_TYPE_HUANYOU_IOS":     2,
	"URL_TYPE_HUANYOU_ANDROID": 3,
	"URL_TYPE_MAIKE_IOS":       4,
	"URL_TYPE_MAIKE_ANDROID":   5,
}

func (x AppUrl_UrlType) String() string {
	return proto.EnumName(AppUrl_UrlType_name, int32(x))
}
func (AppUrl_UrlType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{14, 0}
}

// 获取送礼需要二次弹窗的礼物列表
type GetPopUpPresentListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPopUpPresentListReq) Reset()         { *m = GetPopUpPresentListReq{} }
func (m *GetPopUpPresentListReq) String() string { return proto.CompactTextString(m) }
func (*GetPopUpPresentListReq) ProtoMessage()    {}
func (*GetPopUpPresentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{0}
}
func (m *GetPopUpPresentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPopUpPresentListReq.Unmarshal(m, b)
}
func (m *GetPopUpPresentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPopUpPresentListReq.Marshal(b, m, deterministic)
}
func (dst *GetPopUpPresentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPopUpPresentListReq.Merge(dst, src)
}
func (m *GetPopUpPresentListReq) XXX_Size() int {
	return xxx_messageInfo_GetPopUpPresentListReq.Size(m)
}
func (m *GetPopUpPresentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPopUpPresentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPopUpPresentListReq proto.InternalMessageInfo

type GetPopUpPresentListResp struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPopUpPresentListResp) Reset()         { *m = GetPopUpPresentListResp{} }
func (m *GetPopUpPresentListResp) String() string { return proto.CompactTextString(m) }
func (*GetPopUpPresentListResp) ProtoMessage()    {}
func (*GetPopUpPresentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{1}
}
func (m *GetPopUpPresentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPopUpPresentListResp.Unmarshal(m, b)
}
func (m *GetPopUpPresentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPopUpPresentListResp.Marshal(b, m, deterministic)
}
func (dst *GetPopUpPresentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPopUpPresentListResp.Merge(dst, src)
}
func (m *GetPopUpPresentListResp) XXX_Size() int {
	return xxx_messageInfo_GetPopUpPresentListResp.Size(m)
}
func (m *GetPopUpPresentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPopUpPresentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPopUpPresentListResp proto.InternalMessageInfo

func (m *GetPopUpPresentListResp) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type SearchPresentReq struct {
	KeyId                uint32   `protobuf:"varint,1,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	KeyName              string   `protobuf:"bytes,2,opt,name=key_name,json=keyName,proto3" json:"key_name,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchPresentReq) Reset()         { *m = SearchPresentReq{} }
func (m *SearchPresentReq) String() string { return proto.CompactTextString(m) }
func (*SearchPresentReq) ProtoMessage()    {}
func (*SearchPresentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{2}
}
func (m *SearchPresentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPresentReq.Unmarshal(m, b)
}
func (m *SearchPresentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPresentReq.Marshal(b, m, deterministic)
}
func (dst *SearchPresentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPresentReq.Merge(dst, src)
}
func (m *SearchPresentReq) XXX_Size() int {
	return xxx_messageInfo_SearchPresentReq.Size(m)
}
func (m *SearchPresentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPresentReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPresentReq proto.InternalMessageInfo

func (m *SearchPresentReq) GetKeyId() uint32 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *SearchPresentReq) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

func (m *SearchPresentReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchPresentReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type SearchPresentResp struct {
	PresentConfigs       []*PresentBaseConfig `protobuf:"bytes,1,rep,name=present_configs,json=presentConfigs,proto3" json:"present_configs,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SearchPresentResp) Reset()         { *m = SearchPresentResp{} }
func (m *SearchPresentResp) String() string { return proto.CompactTextString(m) }
func (*SearchPresentResp) ProtoMessage()    {}
func (*SearchPresentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{3}
}
func (m *SearchPresentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchPresentResp.Unmarshal(m, b)
}
func (m *SearchPresentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchPresentResp.Marshal(b, m, deterministic)
}
func (dst *SearchPresentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchPresentResp.Merge(dst, src)
}
func (m *SearchPresentResp) XXX_Size() int {
	return xxx_messageInfo_SearchPresentResp.Size(m)
}
func (m *SearchPresentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchPresentResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchPresentResp proto.InternalMessageInfo

func (m *SearchPresentResp) GetPresentConfigs() []*PresentBaseConfig {
	if m != nil {
		return m.PresentConfigs
	}
	return nil
}

func (m *SearchPresentResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取已有礼物浮层的礼物列表
type GetPresentFloatLayerReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	KeyId                uint32   `protobuf:"varint,3,opt,name=key_id,json=keyId,proto3" json:"key_id,omitempty"`
	KeyName              string   `protobuf:"bytes,4,opt,name=key_name,json=keyName,proto3" json:"key_name,omitempty"`
	Status               uint32   `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFloatLayerReq) Reset()         { *m = GetPresentFloatLayerReq{} }
func (m *GetPresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFloatLayerReq) ProtoMessage()    {}
func (*GetPresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{4}
}
func (m *GetPresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFloatLayerReq.Unmarshal(m, b)
}
func (m *GetPresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFloatLayerReq.Merge(dst, src)
}
func (m *GetPresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFloatLayerReq.Size(m)
}
func (m *GetPresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFloatLayerReq proto.InternalMessageInfo

func (m *GetPresentFloatLayerReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPresentFloatLayerReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetPresentFloatLayerReq) GetKeyId() uint32 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *GetPresentFloatLayerReq) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

func (m *GetPresentFloatLayerReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type GetPresentFloatLayerResp struct {
	LayerInfos           []*PresentFloatInfo `protobuf:"bytes,1,rep,name=layer_infos,json=layerInfos,proto3" json:"layer_infos,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	LastUpdateTime       uint32              `protobuf:"varint,3,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentFloatLayerResp) Reset()         { *m = GetPresentFloatLayerResp{} }
func (m *GetPresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFloatLayerResp) ProtoMessage()    {}
func (*GetPresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{5}
}
func (m *GetPresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFloatLayerResp.Unmarshal(m, b)
}
func (m *GetPresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFloatLayerResp.Merge(dst, src)
}
func (m *GetPresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFloatLayerResp.Size(m)
}
func (m *GetPresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFloatLayerResp proto.InternalMessageInfo

func (m *GetPresentFloatLayerResp) GetLayerInfos() []*PresentFloatInfo {
	if m != nil {
		return m.LayerInfos
	}
	return nil
}

func (m *GetPresentFloatLayerResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetPresentFloatLayerResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

// 给礼物添加礼物浮层
type AddPresentFloatLayerReq struct {
	Layer                *PresentFloatLayer `protobuf:"bytes,1,opt,name=layer,proto3" json:"layer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddPresentFloatLayerReq) Reset()         { *m = AddPresentFloatLayerReq{} }
func (m *AddPresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*AddPresentFloatLayerReq) ProtoMessage()    {}
func (*AddPresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{6}
}
func (m *AddPresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentFloatLayerReq.Unmarshal(m, b)
}
func (m *AddPresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *AddPresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentFloatLayerReq.Merge(dst, src)
}
func (m *AddPresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_AddPresentFloatLayerReq.Size(m)
}
func (m *AddPresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentFloatLayerReq proto.InternalMessageInfo

func (m *AddPresentFloatLayerReq) GetLayer() *PresentFloatLayer {
	if m != nil {
		return m.Layer
	}
	return nil
}

type AddPresentFloatLayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddPresentFloatLayerResp) Reset()         { *m = AddPresentFloatLayerResp{} }
func (m *AddPresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*AddPresentFloatLayerResp) ProtoMessage()    {}
func (*AddPresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{7}
}
func (m *AddPresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddPresentFloatLayerResp.Unmarshal(m, b)
}
func (m *AddPresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddPresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *AddPresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddPresentFloatLayerResp.Merge(dst, src)
}
func (m *AddPresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_AddPresentFloatLayerResp.Size(m)
}
func (m *AddPresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddPresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddPresentFloatLayerResp proto.InternalMessageInfo

// 更新礼物的礼物浮层
type UpdatePresentFloatLayerReq struct {
	Layer                *PresentFloatLayer `protobuf:"bytes,1,opt,name=layer,proto3" json:"layer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdatePresentFloatLayerReq) Reset()         { *m = UpdatePresentFloatLayerReq{} }
func (m *UpdatePresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentFloatLayerReq) ProtoMessage()    {}
func (*UpdatePresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{8}
}
func (m *UpdatePresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentFloatLayerReq.Unmarshal(m, b)
}
func (m *UpdatePresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentFloatLayerReq.Merge(dst, src)
}
func (m *UpdatePresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentFloatLayerReq.Size(m)
}
func (m *UpdatePresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentFloatLayerReq proto.InternalMessageInfo

func (m *UpdatePresentFloatLayerReq) GetLayer() *PresentFloatLayer {
	if m != nil {
		return m.Layer
	}
	return nil
}

type UpdatePresentFloatLayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdatePresentFloatLayerResp) Reset()         { *m = UpdatePresentFloatLayerResp{} }
func (m *UpdatePresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*UpdatePresentFloatLayerResp) ProtoMessage()    {}
func (*UpdatePresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{9}
}
func (m *UpdatePresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdatePresentFloatLayerResp.Unmarshal(m, b)
}
func (m *UpdatePresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdatePresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *UpdatePresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdatePresentFloatLayerResp.Merge(dst, src)
}
func (m *UpdatePresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_UpdatePresentFloatLayerResp.Size(m)
}
func (m *UpdatePresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdatePresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdatePresentFloatLayerResp proto.InternalMessageInfo

// 删除礼物的礼物浮层
type DelPresentFloatLayerReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentFloatLayerReq) Reset()         { *m = DelPresentFloatLayerReq{} }
func (m *DelPresentFloatLayerReq) String() string { return proto.CompactTextString(m) }
func (*DelPresentFloatLayerReq) ProtoMessage()    {}
func (*DelPresentFloatLayerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{10}
}
func (m *DelPresentFloatLayerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentFloatLayerReq.Unmarshal(m, b)
}
func (m *DelPresentFloatLayerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentFloatLayerReq.Marshal(b, m, deterministic)
}
func (dst *DelPresentFloatLayerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentFloatLayerReq.Merge(dst, src)
}
func (m *DelPresentFloatLayerReq) XXX_Size() int {
	return xxx_messageInfo_DelPresentFloatLayerReq.Size(m)
}
func (m *DelPresentFloatLayerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentFloatLayerReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentFloatLayerReq proto.InternalMessageInfo

func (m *DelPresentFloatLayerReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type DelPresentFloatLayerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelPresentFloatLayerResp) Reset()         { *m = DelPresentFloatLayerResp{} }
func (m *DelPresentFloatLayerResp) String() string { return proto.CompactTextString(m) }
func (*DelPresentFloatLayerResp) ProtoMessage()    {}
func (*DelPresentFloatLayerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{11}
}
func (m *DelPresentFloatLayerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelPresentFloatLayerResp.Unmarshal(m, b)
}
func (m *DelPresentFloatLayerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelPresentFloatLayerResp.Marshal(b, m, deterministic)
}
func (dst *DelPresentFloatLayerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelPresentFloatLayerResp.Merge(dst, src)
}
func (m *DelPresentFloatLayerResp) XXX_Size() int {
	return xxx_messageInfo_DelPresentFloatLayerResp.Size(m)
}
func (m *DelPresentFloatLayerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelPresentFloatLayerResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelPresentFloatLayerResp proto.InternalMessageInfo

type PresentFloatInfo struct {
	LayerInfo            *PresentFloatLayer `protobuf:"bytes,1,opt,name=layer_info,json=layerInfo,proto3" json:"layer_info,omitempty"`
	PresentConfig        *PresentBaseConfig `protobuf:"bytes,2,opt,name=present_config,json=presentConfig,proto3" json:"present_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PresentFloatInfo) Reset()         { *m = PresentFloatInfo{} }
func (m *PresentFloatInfo) String() string { return proto.CompactTextString(m) }
func (*PresentFloatInfo) ProtoMessage()    {}
func (*PresentFloatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{12}
}
func (m *PresentFloatInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFloatInfo.Unmarshal(m, b)
}
func (m *PresentFloatInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFloatInfo.Marshal(b, m, deterministic)
}
func (dst *PresentFloatInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFloatInfo.Merge(dst, src)
}
func (m *PresentFloatInfo) XXX_Size() int {
	return xxx_messageInfo_PresentFloatInfo.Size(m)
}
func (m *PresentFloatInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFloatInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFloatInfo proto.InternalMessageInfo

func (m *PresentFloatInfo) GetLayerInfo() *PresentFloatLayer {
	if m != nil {
		return m.LayerInfo
	}
	return nil
}

func (m *PresentFloatInfo) GetPresentConfig() *PresentBaseConfig {
	if m != nil {
		return m.PresentConfig
	}
	return nil
}

type PresentFloatLayer struct {
	GiftId               uint32                            `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FloatImageUrl        string                            `protobuf:"bytes,2,opt,name=float_image_url,json=floatImageUrl,proto3" json:"float_image_url,omitempty"`
	JumpUrl              string                            `protobuf:"bytes,3,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	IsActivityUrl        bool                              `protobuf:"varint,4,opt,name=is_activity_url,json=isActivityUrl,proto3" json:"is_activity_url,omitempty"`
	EffectBegin          uint32                            `protobuf:"varint,5,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                            `protobuf:"varint,6,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	Operator             string                            `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator,omitempty"`
	EffectStatus         uint32                            `protobuf:"varint,8,opt,name=effect_status,json=effectStatus,proto3" json:"effect_status,omitempty"`
	UpdateTime           uint32                            `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	ShowChannelType      []PresentFloatLayer_ChannelType   `protobuf:"varint,10,rep,packed,name=show_channel_type,json=showChannelType,proto3,enum=presentextraconf.PresentFloatLayer_ChannelType" json:"show_channel_type,omitempty"`
	ShowAppType          []PresentFloatLayer_AppType       `protobuf:"varint,11,rep,packed,name=show_app_type,json=showAppType,proto3,enum=presentextraconf.PresentFloatLayer_AppType" json:"show_app_type,omitempty"`
	AppJumpUrl           []*AppUrl                         `protobuf:"bytes,12,rep,name=app_jump_url,json=appJumpUrl,proto3" json:"app_jump_url,omitempty"`
	ActivityType         PresentFloatLayer_ActivityType    `protobuf:"varint,13,opt,name=activity_type,json=activityType,proto3,enum=presentextraconf.PresentFloatLayer_ActivityType" json:"activity_type,omitempty"`
	SubActivityType      PresentFloatLayer_SubActivityType `protobuf:"varint,14,opt,name=sub_activity_type,json=subActivityType,proto3,enum=presentextraconf.PresentFloatLayer_SubActivityType" json:"sub_activity_type,omitempty"`
	ActivityName         string                            `protobuf:"bytes,15,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *PresentFloatLayer) Reset()         { *m = PresentFloatLayer{} }
func (m *PresentFloatLayer) String() string { return proto.CompactTextString(m) }
func (*PresentFloatLayer) ProtoMessage()    {}
func (*PresentFloatLayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{13}
}
func (m *PresentFloatLayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFloatLayer.Unmarshal(m, b)
}
func (m *PresentFloatLayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFloatLayer.Marshal(b, m, deterministic)
}
func (dst *PresentFloatLayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFloatLayer.Merge(dst, src)
}
func (m *PresentFloatLayer) XXX_Size() int {
	return xxx_messageInfo_PresentFloatLayer.Size(m)
}
func (m *PresentFloatLayer) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFloatLayer.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFloatLayer proto.InternalMessageInfo

func (m *PresentFloatLayer) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentFloatLayer) GetFloatImageUrl() string {
	if m != nil {
		return m.FloatImageUrl
	}
	return ""
}

func (m *PresentFloatLayer) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *PresentFloatLayer) GetIsActivityUrl() bool {
	if m != nil {
		return m.IsActivityUrl
	}
	return false
}

func (m *PresentFloatLayer) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentFloatLayer) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentFloatLayer) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *PresentFloatLayer) GetEffectStatus() uint32 {
	if m != nil {
		return m.EffectStatus
	}
	return 0
}

func (m *PresentFloatLayer) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *PresentFloatLayer) GetShowChannelType() []PresentFloatLayer_ChannelType {
	if m != nil {
		return m.ShowChannelType
	}
	return nil
}

func (m *PresentFloatLayer) GetShowAppType() []PresentFloatLayer_AppType {
	if m != nil {
		return m.ShowAppType
	}
	return nil
}

func (m *PresentFloatLayer) GetAppJumpUrl() []*AppUrl {
	if m != nil {
		return m.AppJumpUrl
	}
	return nil
}

func (m *PresentFloatLayer) GetActivityType() PresentFloatLayer_ActivityType {
	if m != nil {
		return m.ActivityType
	}
	return PresentFloatLayer_ACTIVITY_TYPE_INVALID
}

func (m *PresentFloatLayer) GetSubActivityType() PresentFloatLayer_SubActivityType {
	if m != nil {
		return m.SubActivityType
	}
	return PresentFloatLayer_SUB_ACTIVITY_TYPE_INVALID
}

func (m *PresentFloatLayer) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

type AppUrl struct {
	UrlType              AppUrl_UrlType `protobuf:"varint,1,opt,name=url_type,json=urlType,proto3,enum=presentextraconf.AppUrl_UrlType" json:"url_type,omitempty"`
	Url                  string         `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *AppUrl) Reset()         { *m = AppUrl{} }
func (m *AppUrl) String() string { return proto.CompactTextString(m) }
func (*AppUrl) ProtoMessage()    {}
func (*AppUrl) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{14}
}
func (m *AppUrl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AppUrl.Unmarshal(m, b)
}
func (m *AppUrl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AppUrl.Marshal(b, m, deterministic)
}
func (dst *AppUrl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AppUrl.Merge(dst, src)
}
func (m *AppUrl) XXX_Size() int {
	return xxx_messageInfo_AppUrl.Size(m)
}
func (m *AppUrl) XXX_DiscardUnknown() {
	xxx_messageInfo_AppUrl.DiscardUnknown(m)
}

var xxx_messageInfo_AppUrl proto.InternalMessageInfo

func (m *AppUrl) GetUrlType() AppUrl_UrlType {
	if m != nil {
		return m.UrlType
	}
	return AppUrl_URL_TYPE_INVALID
}

func (m *AppUrl) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type PresentBaseConfig struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftName             string   `protobuf:"bytes,2,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	PriceValue           uint32   `protobuf:"varint,3,opt,name=price_value,json=priceValue,proto3" json:"price_value,omitempty"`
	PriceType            uint32   `protobuf:"varint,4,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	GiftImage            string   `protobuf:"bytes,5,opt,name=gift_image,json=giftImage,proto3" json:"gift_image,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentBaseConfig) Reset()         { *m = PresentBaseConfig{} }
func (m *PresentBaseConfig) String() string { return proto.CompactTextString(m) }
func (*PresentBaseConfig) ProtoMessage()    {}
func (*PresentBaseConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{15}
}
func (m *PresentBaseConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentBaseConfig.Unmarshal(m, b)
}
func (m *PresentBaseConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentBaseConfig.Marshal(b, m, deterministic)
}
func (dst *PresentBaseConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentBaseConfig.Merge(dst, src)
}
func (m *PresentBaseConfig) XXX_Size() int {
	return xxx_messageInfo_PresentBaseConfig.Size(m)
}
func (m *PresentBaseConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentBaseConfig.DiscardUnknown(m)
}

var xxx_messageInfo_PresentBaseConfig proto.InternalMessageInfo

func (m *PresentBaseConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentBaseConfig) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *PresentBaseConfig) GetPriceValue() uint32 {
	if m != nil {
		return m.PriceValue
	}
	return 0
}

func (m *PresentBaseConfig) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *PresentBaseConfig) GetGiftImage() string {
	if m != nil {
		return m.GiftImage
	}
	return ""
}

type PresentFlashEffect struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FlashId              uint32   `protobuf:"varint,2,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentFlashEffect) Reset()         { *m = PresentFlashEffect{} }
func (m *PresentFlashEffect) String() string { return proto.CompactTextString(m) }
func (*PresentFlashEffect) ProtoMessage()    {}
func (*PresentFlashEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{16}
}
func (m *PresentFlashEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFlashEffect.Unmarshal(m, b)
}
func (m *PresentFlashEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFlashEffect.Marshal(b, m, deterministic)
}
func (dst *PresentFlashEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFlashEffect.Merge(dst, src)
}
func (m *PresentFlashEffect) XXX_Size() int {
	return xxx_messageInfo_PresentFlashEffect.Size(m)
}
func (m *PresentFlashEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFlashEffect.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFlashEffect proto.InternalMessageInfo

func (m *PresentFlashEffect) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentFlashEffect) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

type FlashEffectConfig struct {
	FlashId              uint32   `protobuf:"varint,1,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	FlashName            string   `protobuf:"bytes,2,opt,name=flash_name,json=flashName,proto3" json:"flash_name,omitempty"`
	FlashUrl             string   `protobuf:"bytes,3,opt,name=flash_url,json=flashUrl,proto3" json:"flash_url,omitempty"`
	FlashMd5             string   `protobuf:"bytes,4,opt,name=flash_md5,json=flashMd5,proto3" json:"flash_md5,omitempty"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FlashEffectConfig) Reset()         { *m = FlashEffectConfig{} }
func (m *FlashEffectConfig) String() string { return proto.CompactTextString(m) }
func (*FlashEffectConfig) ProtoMessage()    {}
func (*FlashEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{17}
}
func (m *FlashEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FlashEffectConfig.Unmarshal(m, b)
}
func (m *FlashEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FlashEffectConfig.Marshal(b, m, deterministic)
}
func (dst *FlashEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FlashEffectConfig.Merge(dst, src)
}
func (m *FlashEffectConfig) XXX_Size() int {
	return xxx_messageInfo_FlashEffectConfig.Size(m)
}
func (m *FlashEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_FlashEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_FlashEffectConfig proto.InternalMessageInfo

func (m *FlashEffectConfig) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

func (m *FlashEffectConfig) GetFlashName() string {
	if m != nil {
		return m.FlashName
	}
	return ""
}

func (m *FlashEffectConfig) GetFlashUrl() string {
	if m != nil {
		return m.FlashUrl
	}
	return ""
}

func (m *FlashEffectConfig) GetFlashMd5() string {
	if m != nil {
		return m.FlashMd5
	}
	return ""
}

func (m *FlashEffectConfig) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *FlashEffectConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *FlashEffectConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetFlashEffectConfigReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	KeyName              string   `protobuf:"bytes,3,opt,name=key_name,json=keyName,proto3" json:"key_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetFlashEffectConfigReq) Reset()         { *m = GetFlashEffectConfigReq{} }
func (m *GetFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetFlashEffectConfigReq) ProtoMessage()    {}
func (*GetFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{18}
}
func (m *GetFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *GetFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlashEffectConfigReq.Merge(dst, src)
}
func (m *GetFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetFlashEffectConfigReq.Size(m)
}
func (m *GetFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlashEffectConfigReq proto.InternalMessageInfo

func (m *GetFlashEffectConfigReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetFlashEffectConfigReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetFlashEffectConfigReq) GetKeyName() string {
	if m != nil {
		return m.KeyName
	}
	return ""
}

type GetFlashEffectConfigResp struct {
	FlashEffects         []*FlashEffectConfig `protobuf:"bytes,1,rep,name=flash_effects,json=flashEffects,proto3" json:"flash_effects,omitempty"`
	Total                uint32               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetFlashEffectConfigResp) Reset()         { *m = GetFlashEffectConfigResp{} }
func (m *GetFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetFlashEffectConfigResp) ProtoMessage()    {}
func (*GetFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{19}
}
func (m *GetFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *GetFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetFlashEffectConfigResp.Merge(dst, src)
}
func (m *GetFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetFlashEffectConfigResp.Size(m)
}
func (m *GetFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetFlashEffectConfigResp proto.InternalMessageInfo

func (m *GetFlashEffectConfigResp) GetFlashEffects() []*FlashEffectConfig {
	if m != nil {
		return m.FlashEffects
	}
	return nil
}

func (m *GetFlashEffectConfigResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type AddFlashEffectConfigReq struct {
	FlashEffect          *FlashEffectConfig `protobuf:"bytes,1,opt,name=flash_effect,json=flashEffect,proto3" json:"flash_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddFlashEffectConfigReq) Reset()         { *m = AddFlashEffectConfigReq{} }
func (m *AddFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddFlashEffectConfigReq) ProtoMessage()    {}
func (*AddFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{20}
}
func (m *AddFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *AddFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlashEffectConfigReq.Merge(dst, src)
}
func (m *AddFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddFlashEffectConfigReq.Size(m)
}
func (m *AddFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlashEffectConfigReq proto.InternalMessageInfo

func (m *AddFlashEffectConfigReq) GetFlashEffect() *FlashEffectConfig {
	if m != nil {
		return m.FlashEffect
	}
	return nil
}

type AddFlashEffectConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddFlashEffectConfigResp) Reset()         { *m = AddFlashEffectConfigResp{} }
func (m *AddFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddFlashEffectConfigResp) ProtoMessage()    {}
func (*AddFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{21}
}
func (m *AddFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *AddFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddFlashEffectConfigResp.Merge(dst, src)
}
func (m *AddFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddFlashEffectConfigResp.Size(m)
}
func (m *AddFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddFlashEffectConfigResp proto.InternalMessageInfo

type UpdateFlashEffectConfigReq struct {
	FlashEffect          *FlashEffectConfig `protobuf:"bytes,1,opt,name=flash_effect,json=flashEffect,proto3" json:"flash_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateFlashEffectConfigReq) Reset()         { *m = UpdateFlashEffectConfigReq{} }
func (m *UpdateFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateFlashEffectConfigReq) ProtoMessage()    {}
func (*UpdateFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{22}
}
func (m *UpdateFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *UpdateFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlashEffectConfigReq.Merge(dst, src)
}
func (m *UpdateFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateFlashEffectConfigReq.Size(m)
}
func (m *UpdateFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlashEffectConfigReq proto.InternalMessageInfo

func (m *UpdateFlashEffectConfigReq) GetFlashEffect() *FlashEffectConfig {
	if m != nil {
		return m.FlashEffect
	}
	return nil
}

type UpdateFlashEffectConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateFlashEffectConfigResp) Reset()         { *m = UpdateFlashEffectConfigResp{} }
func (m *UpdateFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateFlashEffectConfigResp) ProtoMessage()    {}
func (*UpdateFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{23}
}
func (m *UpdateFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *UpdateFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateFlashEffectConfigResp.Merge(dst, src)
}
func (m *UpdateFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateFlashEffectConfigResp.Size(m)
}
func (m *UpdateFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateFlashEffectConfigResp proto.InternalMessageInfo

type DelFlashEffectConfigReq struct {
	FlashId              uint32   `protobuf:"varint,1,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlashEffectConfigReq) Reset()         { *m = DelFlashEffectConfigReq{} }
func (m *DelFlashEffectConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelFlashEffectConfigReq) ProtoMessage()    {}
func (*DelFlashEffectConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{24}
}
func (m *DelFlashEffectConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlashEffectConfigReq.Unmarshal(m, b)
}
func (m *DelFlashEffectConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlashEffectConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelFlashEffectConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlashEffectConfigReq.Merge(dst, src)
}
func (m *DelFlashEffectConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelFlashEffectConfigReq.Size(m)
}
func (m *DelFlashEffectConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlashEffectConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlashEffectConfigReq proto.InternalMessageInfo

func (m *DelFlashEffectConfigReq) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

type DelFlashEffectConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelFlashEffectConfigResp) Reset()         { *m = DelFlashEffectConfigResp{} }
func (m *DelFlashEffectConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelFlashEffectConfigResp) ProtoMessage()    {}
func (*DelFlashEffectConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{25}
}
func (m *DelFlashEffectConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelFlashEffectConfigResp.Unmarshal(m, b)
}
func (m *DelFlashEffectConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelFlashEffectConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelFlashEffectConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelFlashEffectConfigResp.Merge(dst, src)
}
func (m *DelFlashEffectConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelFlashEffectConfigResp.Size(m)
}
func (m *DelFlashEffectConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelFlashEffectConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelFlashEffectConfigResp proto.InternalMessageInfo

type PresentFlashInfo struct {
	FlashInfo            *FlashEffectConfig `protobuf:"bytes,1,opt,name=flash_info,json=flashInfo,proto3" json:"flash_info,omitempty"`
	PresentConfig        *PresentBaseConfig `protobuf:"bytes,2,opt,name=present_config,json=presentConfig,proto3" json:"present_config,omitempty"`
	EffectBegin          uint32             `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32             `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	CreateTime           uint32             `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string             `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	EffectStatus         uint32             `protobuf:"varint,7,opt,name=effect_status,json=effectStatus,proto3" json:"effect_status,omitempty"`
	UpdateTime           uint32             `protobuf:"varint,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PresentFlashInfo) Reset()         { *m = PresentFlashInfo{} }
func (m *PresentFlashInfo) String() string { return proto.CompactTextString(m) }
func (*PresentFlashInfo) ProtoMessage()    {}
func (*PresentFlashInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{26}
}
func (m *PresentFlashInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentFlashInfo.Unmarshal(m, b)
}
func (m *PresentFlashInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentFlashInfo.Marshal(b, m, deterministic)
}
func (dst *PresentFlashInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentFlashInfo.Merge(dst, src)
}
func (m *PresentFlashInfo) XXX_Size() int {
	return xxx_messageInfo_PresentFlashInfo.Size(m)
}
func (m *PresentFlashInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentFlashInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentFlashInfo proto.InternalMessageInfo

func (m *PresentFlashInfo) GetFlashInfo() *FlashEffectConfig {
	if m != nil {
		return m.FlashInfo
	}
	return nil
}

func (m *PresentFlashInfo) GetPresentConfig() *PresentBaseConfig {
	if m != nil {
		return m.PresentConfig
	}
	return nil
}

func (m *PresentFlashInfo) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentFlashInfo) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentFlashInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *PresentFlashInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *PresentFlashInfo) GetEffectStatus() uint32 {
	if m != nil {
		return m.EffectStatus
	}
	return 0
}

func (m *PresentFlashInfo) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetPresentFlashEffectReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	KeyEffectName        string   `protobuf:"bytes,3,opt,name=key_effect_name,json=keyEffectName,proto3" json:"key_effect_name,omitempty"`
	KeyGiftId            uint32   `protobuf:"varint,4,opt,name=key_gift_id,json=keyGiftId,proto3" json:"key_gift_id,omitempty"`
	KeyGiftName          string   `protobuf:"bytes,5,opt,name=key_gift_name,json=keyGiftName,proto3" json:"key_gift_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentFlashEffectReq) Reset()         { *m = GetPresentFlashEffectReq{} }
func (m *GetPresentFlashEffectReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlashEffectReq) ProtoMessage()    {}
func (*GetPresentFlashEffectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{27}
}
func (m *GetPresentFlashEffectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlashEffectReq.Unmarshal(m, b)
}
func (m *GetPresentFlashEffectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlashEffectReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlashEffectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlashEffectReq.Merge(dst, src)
}
func (m *GetPresentFlashEffectReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlashEffectReq.Size(m)
}
func (m *GetPresentFlashEffectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlashEffectReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlashEffectReq proto.InternalMessageInfo

func (m *GetPresentFlashEffectReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetPresentFlashEffectReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetPresentFlashEffectReq) GetKeyEffectName() string {
	if m != nil {
		return m.KeyEffectName
	}
	return ""
}

func (m *GetPresentFlashEffectReq) GetKeyGiftId() uint32 {
	if m != nil {
		return m.KeyGiftId
	}
	return 0
}

func (m *GetPresentFlashEffectReq) GetKeyGiftName() string {
	if m != nil {
		return m.KeyGiftName
	}
	return ""
}

type GetPresentFlashEffectResp struct {
	PresentEffects       []*PresentFlashInfo `protobuf:"bytes,1,rep,name=present_effects,json=presentEffects,proto3" json:"present_effects,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	LastUpdateTime       uint32              `protobuf:"varint,3,opt,name=last_update_time,json=lastUpdateTime,proto3" json:"last_update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetPresentFlashEffectResp) Reset()         { *m = GetPresentFlashEffectResp{} }
func (m *GetPresentFlashEffectResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentFlashEffectResp) ProtoMessage()    {}
func (*GetPresentFlashEffectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{28}
}
func (m *GetPresentFlashEffectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentFlashEffectResp.Unmarshal(m, b)
}
func (m *GetPresentFlashEffectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentFlashEffectResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentFlashEffectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentFlashEffectResp.Merge(dst, src)
}
func (m *GetPresentFlashEffectResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentFlashEffectResp.Size(m)
}
func (m *GetPresentFlashEffectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentFlashEffectResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentFlashEffectResp proto.InternalMessageInfo

func (m *GetPresentFlashEffectResp) GetPresentEffects() []*PresentFlashInfo {
	if m != nil {
		return m.PresentEffects
	}
	return nil
}

func (m *GetPresentFlashEffectResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetPresentFlashEffectResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

// 绑定接口，新增/修改/解绑都用这个
type BoundPresentFlashEffectReq struct {
	GiftId               []uint32 `protobuf:"varint,1,rep,packed,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	FlashId              uint32   `protobuf:"varint,2,opt,name=flash_id,json=flashId,proto3" json:"flash_id,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoundPresentFlashEffectReq) Reset()         { *m = BoundPresentFlashEffectReq{} }
func (m *BoundPresentFlashEffectReq) String() string { return proto.CompactTextString(m) }
func (*BoundPresentFlashEffectReq) ProtoMessage()    {}
func (*BoundPresentFlashEffectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{29}
}
func (m *BoundPresentFlashEffectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoundPresentFlashEffectReq.Unmarshal(m, b)
}
func (m *BoundPresentFlashEffectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoundPresentFlashEffectReq.Marshal(b, m, deterministic)
}
func (dst *BoundPresentFlashEffectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoundPresentFlashEffectReq.Merge(dst, src)
}
func (m *BoundPresentFlashEffectReq) XXX_Size() int {
	return xxx_messageInfo_BoundPresentFlashEffectReq.Size(m)
}
func (m *BoundPresentFlashEffectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BoundPresentFlashEffectReq.DiscardUnknown(m)
}

var xxx_messageInfo_BoundPresentFlashEffectReq proto.InternalMessageInfo

func (m *BoundPresentFlashEffectReq) GetGiftId() []uint32 {
	if m != nil {
		return m.GiftId
	}
	return nil
}

func (m *BoundPresentFlashEffectReq) GetFlashId() uint32 {
	if m != nil {
		return m.FlashId
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *BoundPresentFlashEffectReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type BoundPresentFlashEffectResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BoundPresentFlashEffectResp) Reset()         { *m = BoundPresentFlashEffectResp{} }
func (m *BoundPresentFlashEffectResp) String() string { return proto.CompactTextString(m) }
func (*BoundPresentFlashEffectResp) ProtoMessage()    {}
func (*BoundPresentFlashEffectResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{30}
}
func (m *BoundPresentFlashEffectResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BoundPresentFlashEffectResp.Unmarshal(m, b)
}
func (m *BoundPresentFlashEffectResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BoundPresentFlashEffectResp.Marshal(b, m, deterministic)
}
func (dst *BoundPresentFlashEffectResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BoundPresentFlashEffectResp.Merge(dst, src)
}
func (m *BoundPresentFlashEffectResp) XXX_Size() int {
	return xxx_messageInfo_BoundPresentFlashEffectResp.Size(m)
}
func (m *BoundPresentFlashEffectResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BoundPresentFlashEffectResp.DiscardUnknown(m)
}

var xxx_messageInfo_BoundPresentFlashEffectResp proto.InternalMessageInfo

type GetUserCustomizedInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCustomizedInfoReq) Reset()         { *m = GetUserCustomizedInfoReq{} }
func (m *GetUserCustomizedInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCustomizedInfoReq) ProtoMessage()    {}
func (*GetUserCustomizedInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{31}
}
func (m *GetUserCustomizedInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCustomizedInfoReq.Unmarshal(m, b)
}
func (m *GetUserCustomizedInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCustomizedInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCustomizedInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCustomizedInfoReq.Merge(dst, src)
}
func (m *GetUserCustomizedInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCustomizedInfoReq.Size(m)
}
func (m *GetUserCustomizedInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCustomizedInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCustomizedInfoReq proto.InternalMessageInfo

func (m *GetUserCustomizedInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserCustomizedInfoResp struct {
	PresentInfo          []*CustomizedPresentInfo `protobuf:"bytes,1,rep,name=present_info,json=presentInfo,proto3" json:"present_info,omitempty"`
	LastUpdateTs         uint32                   `protobuf:"varint,2,opt,name=last_update_ts,json=lastUpdateTs,proto3" json:"last_update_ts,omitempty"`
	PresentEffectAppend  []*PresentEffectAppend   `protobuf:"bytes,3,rep,name=present_effect_append,json=presentEffectAppend,proto3" json:"present_effect_append,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetUserCustomizedInfoResp) Reset()         { *m = GetUserCustomizedInfoResp{} }
func (m *GetUserCustomizedInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCustomizedInfoResp) ProtoMessage()    {}
func (*GetUserCustomizedInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{32}
}
func (m *GetUserCustomizedInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCustomizedInfoResp.Unmarshal(m, b)
}
func (m *GetUserCustomizedInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCustomizedInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCustomizedInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCustomizedInfoResp.Merge(dst, src)
}
func (m *GetUserCustomizedInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCustomizedInfoResp.Size(m)
}
func (m *GetUserCustomizedInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCustomizedInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCustomizedInfoResp proto.InternalMessageInfo

func (m *GetUserCustomizedInfoResp) GetPresentInfo() []*CustomizedPresentInfo {
	if m != nil {
		return m.PresentInfo
	}
	return nil
}

func (m *GetUserCustomizedInfoResp) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *GetUserCustomizedInfoResp) GetPresentEffectAppend() []*PresentEffectAppend {
	if m != nil {
		return m.PresentEffectAppend
	}
	return nil
}

// 礼物权限发放
type PresentEffectAppend struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,2,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,3,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEffectAppend) Reset()         { *m = PresentEffectAppend{} }
func (m *PresentEffectAppend) String() string { return proto.CompactTextString(m) }
func (*PresentEffectAppend) ProtoMessage()    {}
func (*PresentEffectAppend) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{33}
}
func (m *PresentEffectAppend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectAppend.Unmarshal(m, b)
}
func (m *PresentEffectAppend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectAppend.Marshal(b, m, deterministic)
}
func (dst *PresentEffectAppend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectAppend.Merge(dst, src)
}
func (m *PresentEffectAppend) XXX_Size() int {
	return xxx_messageInfo_PresentEffectAppend.Size(m)
}
func (m *PresentEffectAppend) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectAppend.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectAppend proto.InternalMessageInfo

func (m *PresentEffectAppend) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentEffectAppend) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *PresentEffectAppend) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

// 自定义礼物用户部分的信息
type UserCustomizedInfo struct {
	Id                   uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Option               []*CustomOption `protobuf:"bytes,2,rep,name=option,proto3" json:"option,omitempty"`
	AuthorityLevel       uint32          `protobuf:"varint,3,opt,name=authority_level,json=authorityLevel,proto3" json:"authority_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UserCustomizedInfo) Reset()         { *m = UserCustomizedInfo{} }
func (m *UserCustomizedInfo) String() string { return proto.CompactTextString(m) }
func (*UserCustomizedInfo) ProtoMessage()    {}
func (*UserCustomizedInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{34}
}
func (m *UserCustomizedInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCustomizedInfo.Unmarshal(m, b)
}
func (m *UserCustomizedInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCustomizedInfo.Marshal(b, m, deterministic)
}
func (dst *UserCustomizedInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCustomizedInfo.Merge(dst, src)
}
func (m *UserCustomizedInfo) XXX_Size() int {
	return xxx_messageInfo_UserCustomizedInfo.Size(m)
}
func (m *UserCustomizedInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCustomizedInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserCustomizedInfo proto.InternalMessageInfo

func (m *UserCustomizedInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserCustomizedInfo) GetOption() []*CustomOption {
	if m != nil {
		return m.Option
	}
	return nil
}

func (m *UserCustomizedInfo) GetAuthorityLevel() uint32 {
	if m != nil {
		return m.AuthorityLevel
	}
	return 0
}

// 自定义礼物的信息，部分礼物本身的配置信息可以直接从礼物缓存中获取
type CustomizedPresentInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomOption         []string `protobuf:"bytes,2,rep,name=custom_option,json=customOption,proto3" json:"custom_option,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	CmsUrl               string   `protobuf:"bytes,4,opt,name=cms_url,json=cmsUrl,proto3" json:"cms_url,omitempty"`
	HasAuthority         bool     `protobuf:"varint,5,opt,name=has_authority,json=hasAuthority,proto3" json:"has_authority,omitempty"`
	HasNewCustom         bool     `protobuf:"varint,6,opt,name=has_new_custom,json=hasNewCustom,proto3" json:"has_new_custom,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,7,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,8,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomizedPresentInfo) Reset()         { *m = CustomizedPresentInfo{} }
func (m *CustomizedPresentInfo) String() string { return proto.CompactTextString(m) }
func (*CustomizedPresentInfo) ProtoMessage()    {}
func (*CustomizedPresentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{35}
}
func (m *CustomizedPresentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizedPresentInfo.Unmarshal(m, b)
}
func (m *CustomizedPresentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizedPresentInfo.Marshal(b, m, deterministic)
}
func (dst *CustomizedPresentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizedPresentInfo.Merge(dst, src)
}
func (m *CustomizedPresentInfo) XXX_Size() int {
	return xxx_messageInfo_CustomizedPresentInfo.Size(m)
}
func (m *CustomizedPresentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizedPresentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizedPresentInfo proto.InternalMessageInfo

func (m *CustomizedPresentInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomizedPresentInfo) GetCustomOption() []string {
	if m != nil {
		return m.CustomOption
	}
	return nil
}

func (m *CustomizedPresentInfo) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *CustomizedPresentInfo) GetCmsUrl() string {
	if m != nil {
		return m.CmsUrl
	}
	return ""
}

func (m *CustomizedPresentInfo) GetHasAuthority() bool {
	if m != nil {
		return m.HasAuthority
	}
	return false
}

func (m *CustomizedPresentInfo) GetHasNewCustom() bool {
	if m != nil {
		return m.HasNewCustom
	}
	return false
}

func (m *CustomizedPresentInfo) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *CustomizedPresentInfo) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type GetUserCustomizedInfoByGiftIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCustomizedInfoByGiftIdReq) Reset()         { *m = GetUserCustomizedInfoByGiftIdReq{} }
func (m *GetUserCustomizedInfoByGiftIdReq) String() string { return proto.CompactTextString(m) }
func (*GetUserCustomizedInfoByGiftIdReq) ProtoMessage()    {}
func (*GetUserCustomizedInfoByGiftIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{36}
}
func (m *GetUserCustomizedInfoByGiftIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCustomizedInfoByGiftIdReq.Unmarshal(m, b)
}
func (m *GetUserCustomizedInfoByGiftIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCustomizedInfoByGiftIdReq.Marshal(b, m, deterministic)
}
func (dst *GetUserCustomizedInfoByGiftIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCustomizedInfoByGiftIdReq.Merge(dst, src)
}
func (m *GetUserCustomizedInfoByGiftIdReq) XXX_Size() int {
	return xxx_messageInfo_GetUserCustomizedInfoByGiftIdReq.Size(m)
}
func (m *GetUserCustomizedInfoByGiftIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCustomizedInfoByGiftIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCustomizedInfoByGiftIdReq proto.InternalMessageInfo

func (m *GetUserCustomizedInfoByGiftIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCustomizedInfoByGiftIdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetUserCustomizedInfoByGiftIdResp struct {
	PresentDetail        *CustomizedPresentDetail `protobuf:"bytes,1,opt,name=present_detail,json=presentDetail,proto3" json:"present_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetUserCustomizedInfoByGiftIdResp) Reset()         { *m = GetUserCustomizedInfoByGiftIdResp{} }
func (m *GetUserCustomizedInfoByGiftIdResp) String() string { return proto.CompactTextString(m) }
func (*GetUserCustomizedInfoByGiftIdResp) ProtoMessage()    {}
func (*GetUserCustomizedInfoByGiftIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{37}
}
func (m *GetUserCustomizedInfoByGiftIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCustomizedInfoByGiftIdResp.Unmarshal(m, b)
}
func (m *GetUserCustomizedInfoByGiftIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCustomizedInfoByGiftIdResp.Marshal(b, m, deterministic)
}
func (dst *GetUserCustomizedInfoByGiftIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCustomizedInfoByGiftIdResp.Merge(dst, src)
}
func (m *GetUserCustomizedInfoByGiftIdResp) XXX_Size() int {
	return xxx_messageInfo_GetUserCustomizedInfoByGiftIdResp.Size(m)
}
func (m *GetUserCustomizedInfoByGiftIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCustomizedInfoByGiftIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCustomizedInfoByGiftIdResp proto.InternalMessageInfo

func (m *GetUserCustomizedInfoByGiftIdResp) GetPresentDetail() *CustomizedPresentDetail {
	if m != nil {
		return m.PresentDetail
	}
	return nil
}

type CustomizedPresentDetail struct {
	Id           uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CustomOption []*CustomOption `protobuf:"bytes,2,rep,name=custom_option,json=customOption,proto3" json:"custom_option,omitempty"`
	// uint32 present_id = 3; // 子礼物id
	// 另一个做法，客户端本地计算，好处延迟低，坏处是逻辑不方便更改：
	CustomMethod map[string]uint32 `protobuf:"bytes,3,rep,name=custom_method,json=customMethod,proto3" json:"custom_method,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// 组件选择与子礼物id的对应公式, key: 类似 1_2_3 的字符串，下划线分割，代表组件id1、2、3分别选择样式1、2、3; value: 对应的子礼物id
	UserLevel            uint32                           `protobuf:"varint,4,opt,name=user_level,json=userLevel,proto3" json:"user_level,omitempty"`
	LevelText            string                           `protobuf:"bytes,5,opt,name=level_text,json=levelText,proto3" json:"level_text,omitempty"`
	ColorfulText         string                           `protobuf:"bytes,6,opt,name=colorful_text,json=colorfulText,proto3" json:"colorful_text,omitempty"`
	PreviewMap           map[uint32]*CustomPresentPreview `protobuf:"bytes,7,rep,name=preview_map,json=previewMap,proto3" json:"preview_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-"`
	XXX_unrecognized     []byte                           `json:"-"`
	XXX_sizecache        int32                            `json:"-"`
}

func (m *CustomizedPresentDetail) Reset()         { *m = CustomizedPresentDetail{} }
func (m *CustomizedPresentDetail) String() string { return proto.CompactTextString(m) }
func (*CustomizedPresentDetail) ProtoMessage()    {}
func (*CustomizedPresentDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{38}
}
func (m *CustomizedPresentDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizedPresentDetail.Unmarshal(m, b)
}
func (m *CustomizedPresentDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizedPresentDetail.Marshal(b, m, deterministic)
}
func (dst *CustomizedPresentDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizedPresentDetail.Merge(dst, src)
}
func (m *CustomizedPresentDetail) XXX_Size() int {
	return xxx_messageInfo_CustomizedPresentDetail.Size(m)
}
func (m *CustomizedPresentDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizedPresentDetail.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizedPresentDetail proto.InternalMessageInfo

func (m *CustomizedPresentDetail) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomizedPresentDetail) GetCustomOption() []*CustomOption {
	if m != nil {
		return m.CustomOption
	}
	return nil
}

func (m *CustomizedPresentDetail) GetCustomMethod() map[string]uint32 {
	if m != nil {
		return m.CustomMethod
	}
	return nil
}

func (m *CustomizedPresentDetail) GetUserLevel() uint32 {
	if m != nil {
		return m.UserLevel
	}
	return 0
}

func (m *CustomizedPresentDetail) GetLevelText() string {
	if m != nil {
		return m.LevelText
	}
	return ""
}

func (m *CustomizedPresentDetail) GetColorfulText() string {
	if m != nil {
		return m.ColorfulText
	}
	return ""
}

func (m *CustomizedPresentDetail) GetPreviewMap() map[uint32]*CustomPresentPreview {
	if m != nil {
		return m.PreviewMap
	}
	return nil
}

type CustomPresentPreview struct {
	PreviewType          uint32   `protobuf:"varint,1,opt,name=preview_type,json=previewType,proto3" json:"preview_type,omitempty"`
	PreviewUrl           string   `protobuf:"bytes,2,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	PreviewMd5           string   `protobuf:"bytes,3,opt,name=preview_md5,json=previewMd5,proto3" json:"preview_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomPresentPreview) Reset()         { *m = CustomPresentPreview{} }
func (m *CustomPresentPreview) String() string { return proto.CompactTextString(m) }
func (*CustomPresentPreview) ProtoMessage()    {}
func (*CustomPresentPreview) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{39}
}
func (m *CustomPresentPreview) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomPresentPreview.Unmarshal(m, b)
}
func (m *CustomPresentPreview) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomPresentPreview.Marshal(b, m, deterministic)
}
func (dst *CustomPresentPreview) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomPresentPreview.Merge(dst, src)
}
func (m *CustomPresentPreview) XXX_Size() int {
	return xxx_messageInfo_CustomPresentPreview.Size(m)
}
func (m *CustomPresentPreview) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomPresentPreview.DiscardUnknown(m)
}

var xxx_messageInfo_CustomPresentPreview proto.InternalMessageInfo

func (m *CustomPresentPreview) GetPreviewType() uint32 {
	if m != nil {
		return m.PreviewType
	}
	return 0
}

func (m *CustomPresentPreview) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *CustomPresentPreview) GetPreviewMd5() string {
	if m != nil {
		return m.PreviewMd5
	}
	return ""
}

type CustomOption struct {
	CustomId             uint32        `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	CustomName           string        `protobuf:"bytes,2,opt,name=custom_name,json=customName,proto3" json:"custom_name,omitempty"`
	OptionInfo           []*OptionInfo `protobuf:"bytes,3,rep,name=option_info,json=optionInfo,proto3" json:"option_info,omitempty"`
	CustomText           string        `protobuf:"bytes,4,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *CustomOption) Reset()         { *m = CustomOption{} }
func (m *CustomOption) String() string { return proto.CompactTextString(m) }
func (*CustomOption) ProtoMessage()    {}
func (*CustomOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{40}
}
func (m *CustomOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomOption.Unmarshal(m, b)
}
func (m *CustomOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomOption.Marshal(b, m, deterministic)
}
func (dst *CustomOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomOption.Merge(dst, src)
}
func (m *CustomOption) XXX_Size() int {
	return xxx_messageInfo_CustomOption.Size(m)
}
func (m *CustomOption) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomOption.DiscardUnknown(m)
}

var xxx_messageInfo_CustomOption proto.InternalMessageInfo

func (m *CustomOption) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomOption) GetCustomName() string {
	if m != nil {
		return m.CustomName
	}
	return ""
}

func (m *CustomOption) GetOptionInfo() []*OptionInfo {
	if m != nil {
		return m.OptionInfo
	}
	return nil
}

func (m *CustomOption) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

type OptionInfo struct {
	OptionId             uint32   `protobuf:"varint,1,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	OptionName           string   `protobuf:"bytes,2,opt,name=option_name,json=optionName,proto3" json:"option_name,omitempty"`
	OptionLevel          uint32   `protobuf:"varint,3,opt,name=option_level,json=optionLevel,proto3" json:"option_level,omitempty"`
	IsNew                bool     `protobuf:"varint,4,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	IsActive             bool     `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OptionInfo) Reset()         { *m = OptionInfo{} }
func (m *OptionInfo) String() string { return proto.CompactTextString(m) }
func (*OptionInfo) ProtoMessage()    {}
func (*OptionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{41}
}
func (m *OptionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OptionInfo.Unmarshal(m, b)
}
func (m *OptionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OptionInfo.Marshal(b, m, deterministic)
}
func (dst *OptionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OptionInfo.Merge(dst, src)
}
func (m *OptionInfo) XXX_Size() int {
	return xxx_messageInfo_OptionInfo.Size(m)
}
func (m *OptionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OptionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OptionInfo proto.InternalMessageInfo

func (m *OptionInfo) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

func (m *OptionInfo) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

func (m *OptionInfo) GetOptionLevel() uint32 {
	if m != nil {
		return m.OptionLevel
	}
	return 0
}

func (m *OptionInfo) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

func (m *OptionInfo) GetIsActive() bool {
	if m != nil {
		return m.IsActive
	}
	return false
}

type CustomOptionConfig struct {
	CustomId             uint32   `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	OptionId             uint32   `protobuf:"varint,2,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomOptionConfig) Reset()         { *m = CustomOptionConfig{} }
func (m *CustomOptionConfig) String() string { return proto.CompactTextString(m) }
func (*CustomOptionConfig) ProtoMessage()    {}
func (*CustomOptionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{42}
}
func (m *CustomOptionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomOptionConfig.Unmarshal(m, b)
}
func (m *CustomOptionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomOptionConfig.Marshal(b, m, deterministic)
}
func (dst *CustomOptionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomOptionConfig.Merge(dst, src)
}
func (m *CustomOptionConfig) XXX_Size() int {
	return xxx_messageInfo_CustomOptionConfig.Size(m)
}
func (m *CustomOptionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomOptionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CustomOptionConfig proto.InternalMessageInfo

func (m *CustomOptionConfig) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomOptionConfig) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

type ReportCustomOptionChooseReq struct {
	Uid                  uint32        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32        `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	CustomPair           []*CustomPair `protobuf:"bytes,3,rep,name=custom_pair,json=customPair,proto3" json:"custom_pair,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *ReportCustomOptionChooseReq) Reset()         { *m = ReportCustomOptionChooseReq{} }
func (m *ReportCustomOptionChooseReq) String() string { return proto.CompactTextString(m) }
func (*ReportCustomOptionChooseReq) ProtoMessage()    {}
func (*ReportCustomOptionChooseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{43}
}
func (m *ReportCustomOptionChooseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCustomOptionChooseReq.Unmarshal(m, b)
}
func (m *ReportCustomOptionChooseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCustomOptionChooseReq.Marshal(b, m, deterministic)
}
func (dst *ReportCustomOptionChooseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCustomOptionChooseReq.Merge(dst, src)
}
func (m *ReportCustomOptionChooseReq) XXX_Size() int {
	return xxx_messageInfo_ReportCustomOptionChooseReq.Size(m)
}
func (m *ReportCustomOptionChooseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCustomOptionChooseReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCustomOptionChooseReq proto.InternalMessageInfo

func (m *ReportCustomOptionChooseReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportCustomOptionChooseReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ReportCustomOptionChooseReq) GetCustomPair() []*CustomPair {
	if m != nil {
		return m.CustomPair
	}
	return nil
}

type ReportCustomOptionChooseResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportCustomOptionChooseResp) Reset()         { *m = ReportCustomOptionChooseResp{} }
func (m *ReportCustomOptionChooseResp) String() string { return proto.CompactTextString(m) }
func (*ReportCustomOptionChooseResp) ProtoMessage()    {}
func (*ReportCustomOptionChooseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{44}
}
func (m *ReportCustomOptionChooseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCustomOptionChooseResp.Unmarshal(m, b)
}
func (m *ReportCustomOptionChooseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCustomOptionChooseResp.Marshal(b, m, deterministic)
}
func (dst *ReportCustomOptionChooseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCustomOptionChooseResp.Merge(dst, src)
}
func (m *ReportCustomOptionChooseResp) XXX_Size() int {
	return xxx_messageInfo_ReportCustomOptionChooseResp.Size(m)
}
func (m *ReportCustomOptionChooseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCustomOptionChooseResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCustomOptionChooseResp proto.InternalMessageInfo

type CustomPair struct {
	CustomId             uint32   `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	OptionId             uint32   `protobuf:"varint,2,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CustomPair) Reset()         { *m = CustomPair{} }
func (m *CustomPair) String() string { return proto.CompactTextString(m) }
func (*CustomPair) ProtoMessage()    {}
func (*CustomPair) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{45}
}
func (m *CustomPair) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomPair.Unmarshal(m, b)
}
func (m *CustomPair) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomPair.Marshal(b, m, deterministic)
}
func (dst *CustomPair) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomPair.Merge(dst, src)
}
func (m *CustomPair) XXX_Size() int {
	return xxx_messageInfo_CustomPair.Size(m)
}
func (m *CustomPair) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomPair.DiscardUnknown(m)
}

var xxx_messageInfo_CustomPair proto.InternalMessageInfo

func (m *CustomPair) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomPair) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

// 获取是否是限时礼物，以及是否有限时礼物权限
type CheckCustomizedGiftReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCustomizedGiftReq) Reset()         { *m = CheckCustomizedGiftReq{} }
func (m *CheckCustomizedGiftReq) String() string { return proto.CompactTextString(m) }
func (*CheckCustomizedGiftReq) ProtoMessage()    {}
func (*CheckCustomizedGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{46}
}
func (m *CheckCustomizedGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCustomizedGiftReq.Unmarshal(m, b)
}
func (m *CheckCustomizedGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCustomizedGiftReq.Marshal(b, m, deterministic)
}
func (dst *CheckCustomizedGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCustomizedGiftReq.Merge(dst, src)
}
func (m *CheckCustomizedGiftReq) XXX_Size() int {
	return xxx_messageInfo_CheckCustomizedGiftReq.Size(m)
}
func (m *CheckCustomizedGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCustomizedGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCustomizedGiftReq proto.InternalMessageInfo

func (m *CheckCustomizedGiftReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckCustomizedGiftReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CheckCustomizedGiftResp struct {
	IsAble               bool     `protobuf:"varint,1,opt,name=is_able,json=isAble,proto3" json:"is_able,omitempty"`
	PrimaryGiftId        uint32   `protobuf:"varint,2,opt,name=primary_gift_id,json=primaryGiftId,proto3" json:"primary_gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckCustomizedGiftResp) Reset()         { *m = CheckCustomizedGiftResp{} }
func (m *CheckCustomizedGiftResp) String() string { return proto.CompactTextString(m) }
func (*CheckCustomizedGiftResp) ProtoMessage()    {}
func (*CheckCustomizedGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{47}
}
func (m *CheckCustomizedGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckCustomizedGiftResp.Unmarshal(m, b)
}
func (m *CheckCustomizedGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckCustomizedGiftResp.Marshal(b, m, deterministic)
}
func (dst *CheckCustomizedGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckCustomizedGiftResp.Merge(dst, src)
}
func (m *CheckCustomizedGiftResp) XXX_Size() int {
	return xxx_messageInfo_CheckCustomizedGiftResp.Size(m)
}
func (m *CheckCustomizedGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckCustomizedGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckCustomizedGiftResp proto.InternalMessageInfo

func (m *CheckCustomizedGiftResp) GetIsAble() bool {
	if m != nil {
		return m.IsAble
	}
	return false
}

func (m *CheckCustomizedGiftResp) GetPrimaryGiftId() uint32 {
	if m != nil {
		return m.PrimaryGiftId
	}
	return 0
}

// 增加礼物配置
type AddCustomizedPresentConfigReq struct {
	Config               *CustomizedPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *AddCustomizedPresentConfigReq) Reset()         { *m = AddCustomizedPresentConfigReq{} }
func (m *AddCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddCustomizedPresentConfigReq) ProtoMessage()    {}
func (*AddCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{48}
}
func (m *AddCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *AddCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *AddCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddCustomizedPresentConfigReq.Size(m)
}
func (m *AddCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddCustomizedPresentConfigReq proto.InternalMessageInfo

func (m *AddCustomizedPresentConfigReq) GetConfig() *CustomizedPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddCustomizedPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddCustomizedPresentConfigResp) Reset()         { *m = AddCustomizedPresentConfigResp{} }
func (m *AddCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddCustomizedPresentConfigResp) ProtoMessage()    {}
func (*AddCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{49}
}
func (m *AddCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *AddCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *AddCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddCustomizedPresentConfigResp.Size(m)
}
func (m *AddCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddCustomizedPresentConfigResp proto.InternalMessageInfo

type CustomizedPresentConfig struct {
	GiftId               uint32               `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	CmdUrl               string               `protobuf:"bytes,2,opt,name=cmd_url,json=cmdUrl,proto3" json:"cmd_url,omitempty"`
	MaxLevel             uint32               `protobuf:"varint,3,opt,name=max_level,json=maxLevel,proto3" json:"max_level,omitempty"`
	EffectConfig         []*LevelEffectConfig `protobuf:"bytes,4,rep,name=effect_config,json=effectConfig,proto3" json:"effect_config,omitempty"`
	CustomConfig         []*CustomConfig      `protobuf:"bytes,5,rep,name=custom_config,json=customConfig,proto3" json:"custom_config,omitempty"`
	LevelConfig          []*LevelConfig       `protobuf:"bytes,6,rep,name=level_config,json=levelConfig,proto3" json:"level_config,omitempty"`
	CustomMethod         []*CustomMethod      `protobuf:"bytes,7,rep,name=custom_method,json=customMethod,proto3" json:"custom_method,omitempty"`
	LevelText            string               `protobuf:"bytes,8,opt,name=level_text,json=levelText,proto3" json:"level_text,omitempty"`
	UpdateTime           uint32               `protobuf:"varint,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	CreateTime           uint32               `protobuf:"varint,10,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *CustomizedPresentConfig) Reset()         { *m = CustomizedPresentConfig{} }
func (m *CustomizedPresentConfig) String() string { return proto.CompactTextString(m) }
func (*CustomizedPresentConfig) ProtoMessage()    {}
func (*CustomizedPresentConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{50}
}
func (m *CustomizedPresentConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomizedPresentConfig.Unmarshal(m, b)
}
func (m *CustomizedPresentConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomizedPresentConfig.Marshal(b, m, deterministic)
}
func (dst *CustomizedPresentConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomizedPresentConfig.Merge(dst, src)
}
func (m *CustomizedPresentConfig) XXX_Size() int {
	return xxx_messageInfo_CustomizedPresentConfig.Size(m)
}
func (m *CustomizedPresentConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomizedPresentConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CustomizedPresentConfig proto.InternalMessageInfo

func (m *CustomizedPresentConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CustomizedPresentConfig) GetCmdUrl() string {
	if m != nil {
		return m.CmdUrl
	}
	return ""
}

func (m *CustomizedPresentConfig) GetMaxLevel() uint32 {
	if m != nil {
		return m.MaxLevel
	}
	return 0
}

func (m *CustomizedPresentConfig) GetEffectConfig() []*LevelEffectConfig {
	if m != nil {
		return m.EffectConfig
	}
	return nil
}

func (m *CustomizedPresentConfig) GetCustomConfig() []*CustomConfig {
	if m != nil {
		return m.CustomConfig
	}
	return nil
}

func (m *CustomizedPresentConfig) GetLevelConfig() []*LevelConfig {
	if m != nil {
		return m.LevelConfig
	}
	return nil
}

func (m *CustomizedPresentConfig) GetCustomMethod() []*CustomMethod {
	if m != nil {
		return m.CustomMethod
	}
	return nil
}

func (m *CustomizedPresentConfig) GetLevelText() string {
	if m != nil {
		return m.LevelText
	}
	return ""
}

func (m *CustomizedPresentConfig) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *CustomizedPresentConfig) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CustomConfig struct {
	CustomId             uint32          `protobuf:"varint,1,opt,name=custom_id,json=customId,proto3" json:"custom_id,omitempty"`
	CustomName           string          `protobuf:"bytes,2,opt,name=custom_name,json=customName,proto3" json:"custom_name,omitempty"`
	CustomText           string          `protobuf:"bytes,3,opt,name=custom_text,json=customText,proto3" json:"custom_text,omitempty"`
	OptionConfig         []*OptionConfig `protobuf:"bytes,4,rep,name=OptionConfig,proto3" json:"OptionConfig,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CustomConfig) Reset()         { *m = CustomConfig{} }
func (m *CustomConfig) String() string { return proto.CompactTextString(m) }
func (*CustomConfig) ProtoMessage()    {}
func (*CustomConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{51}
}
func (m *CustomConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomConfig.Unmarshal(m, b)
}
func (m *CustomConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomConfig.Marshal(b, m, deterministic)
}
func (dst *CustomConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomConfig.Merge(dst, src)
}
func (m *CustomConfig) XXX_Size() int {
	return xxx_messageInfo_CustomConfig.Size(m)
}
func (m *CustomConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomConfig.DiscardUnknown(m)
}

var xxx_messageInfo_CustomConfig proto.InternalMessageInfo

func (m *CustomConfig) GetCustomId() uint32 {
	if m != nil {
		return m.CustomId
	}
	return 0
}

func (m *CustomConfig) GetCustomName() string {
	if m != nil {
		return m.CustomName
	}
	return ""
}

func (m *CustomConfig) GetCustomText() string {
	if m != nil {
		return m.CustomText
	}
	return ""
}

func (m *CustomConfig) GetOptionConfig() []*OptionConfig {
	if m != nil {
		return m.OptionConfig
	}
	return nil
}

type OptionConfig struct {
	OptionId             uint32   `protobuf:"varint,1,opt,name=option_id,json=optionId,proto3" json:"option_id,omitempty"`
	OptionName           string   `protobuf:"bytes,2,opt,name=option_name,json=optionName,proto3" json:"option_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OptionConfig) Reset()         { *m = OptionConfig{} }
func (m *OptionConfig) String() string { return proto.CompactTextString(m) }
func (*OptionConfig) ProtoMessage()    {}
func (*OptionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{52}
}
func (m *OptionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OptionConfig.Unmarshal(m, b)
}
func (m *OptionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OptionConfig.Marshal(b, m, deterministic)
}
func (dst *OptionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OptionConfig.Merge(dst, src)
}
func (m *OptionConfig) XXX_Size() int {
	return xxx_messageInfo_OptionConfig.Size(m)
}
func (m *OptionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_OptionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_OptionConfig proto.InternalMessageInfo

func (m *OptionConfig) GetOptionId() uint32 {
	if m != nil {
		return m.OptionId
	}
	return 0
}

func (m *OptionConfig) GetOptionName() string {
	if m != nil {
		return m.OptionName
	}
	return ""
}

type LevelConfig struct {
	Level                uint32                `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	CustomOption         []*CustomOptionConfig `protobuf:"bytes,2,rep,name=custom_option,json=customOption,proto3" json:"custom_option,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *LevelConfig) Reset()         { *m = LevelConfig{} }
func (m *LevelConfig) String() string { return proto.CompactTextString(m) }
func (*LevelConfig) ProtoMessage()    {}
func (*LevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{53}
}
func (m *LevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelConfig.Unmarshal(m, b)
}
func (m *LevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelConfig.Marshal(b, m, deterministic)
}
func (dst *LevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelConfig.Merge(dst, src)
}
func (m *LevelConfig) XXX_Size() int {
	return xxx_messageInfo_LevelConfig.Size(m)
}
func (m *LevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelConfig proto.InternalMessageInfo

func (m *LevelConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelConfig) GetCustomOption() []*CustomOptionConfig {
	if m != nil {
		return m.CustomOption
	}
	return nil
}

type CustomMethod struct {
	Options              []*CustomOptionConfig `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
	GiftId               uint32                `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	PreviewType          uint32                `protobuf:"varint,3,opt,name=preview_type,json=previewType,proto3" json:"preview_type,omitempty"`
	PreviewUrl           string                `protobuf:"bytes,4,opt,name=preview_url,json=previewUrl,proto3" json:"preview_url,omitempty"`
	PreviewMd5           string                `protobuf:"bytes,5,opt,name=preview_md5,json=previewMd5,proto3" json:"preview_md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *CustomMethod) Reset()         { *m = CustomMethod{} }
func (m *CustomMethod) String() string { return proto.CompactTextString(m) }
func (*CustomMethod) ProtoMessage()    {}
func (*CustomMethod) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{54}
}
func (m *CustomMethod) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomMethod.Unmarshal(m, b)
}
func (m *CustomMethod) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomMethod.Marshal(b, m, deterministic)
}
func (dst *CustomMethod) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomMethod.Merge(dst, src)
}
func (m *CustomMethod) XXX_Size() int {
	return xxx_messageInfo_CustomMethod.Size(m)
}
func (m *CustomMethod) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomMethod.DiscardUnknown(m)
}

var xxx_messageInfo_CustomMethod proto.InternalMessageInfo

func (m *CustomMethod) GetOptions() []*CustomOptionConfig {
	if m != nil {
		return m.Options
	}
	return nil
}

func (m *CustomMethod) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CustomMethod) GetPreviewType() uint32 {
	if m != nil {
		return m.PreviewType
	}
	return 0
}

func (m *CustomMethod) GetPreviewUrl() string {
	if m != nil {
		return m.PreviewUrl
	}
	return ""
}

func (m *CustomMethod) GetPreviewMd5() string {
	if m != nil {
		return m.PreviewMd5
	}
	return ""
}

type LevelEffectConfig struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	EffectBegin          uint32   `protobuf:"varint,2,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32   `protobuf:"varint,3,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LevelEffectConfig) Reset()         { *m = LevelEffectConfig{} }
func (m *LevelEffectConfig) String() string { return proto.CompactTextString(m) }
func (*LevelEffectConfig) ProtoMessage()    {}
func (*LevelEffectConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{55}
}
func (m *LevelEffectConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LevelEffectConfig.Unmarshal(m, b)
}
func (m *LevelEffectConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LevelEffectConfig.Marshal(b, m, deterministic)
}
func (dst *LevelEffectConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LevelEffectConfig.Merge(dst, src)
}
func (m *LevelEffectConfig) XXX_Size() int {
	return xxx_messageInfo_LevelEffectConfig.Size(m)
}
func (m *LevelEffectConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LevelEffectConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LevelEffectConfig proto.InternalMessageInfo

func (m *LevelEffectConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *LevelEffectConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *LevelEffectConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

// 更新礼物配置
type UpdateCustomizedPresentConfigReq struct {
	Config               *CustomizedPresentConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *UpdateCustomizedPresentConfigReq) Reset()         { *m = UpdateCustomizedPresentConfigReq{} }
func (m *UpdateCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCustomizedPresentConfigReq) ProtoMessage()    {}
func (*UpdateCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{56}
}
func (m *UpdateCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *UpdateCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *UpdateCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCustomizedPresentConfigReq.Size(m)
}
func (m *UpdateCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCustomizedPresentConfigReq proto.InternalMessageInfo

func (m *UpdateCustomizedPresentConfigReq) GetConfig() *CustomizedPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 更新礼物配置
type UpdateCustomizedPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCustomizedPresentConfigResp) Reset()         { *m = UpdateCustomizedPresentConfigResp{} }
func (m *UpdateCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCustomizedPresentConfigResp) ProtoMessage()    {}
func (*UpdateCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{57}
}
func (m *UpdateCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *UpdateCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *UpdateCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCustomizedPresentConfigResp.Size(m)
}
func (m *UpdateCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCustomizedPresentConfigResp proto.InternalMessageInfo

// 获取礼物配置
type GetAllCustomizedPresentConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllCustomizedPresentConfigReq) Reset()         { *m = GetAllCustomizedPresentConfigReq{} }
func (m *GetAllCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAllCustomizedPresentConfigReq) ProtoMessage()    {}
func (*GetAllCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{58}
}
func (m *GetAllCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *GetAllCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAllCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *GetAllCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAllCustomizedPresentConfigReq.Size(m)
}
func (m *GetAllCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCustomizedPresentConfigReq proto.InternalMessageInfo

type GetAllCustomizedPresentConfigResp struct {
	Config               []*CustomizedPresentConfig `protobuf:"bytes,1,rep,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetAllCustomizedPresentConfigResp) Reset()         { *m = GetAllCustomizedPresentConfigResp{} }
func (m *GetAllCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAllCustomizedPresentConfigResp) ProtoMessage()    {}
func (*GetAllCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{59}
}
func (m *GetAllCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *GetAllCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAllCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *GetAllCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAllCustomizedPresentConfigResp.Size(m)
}
func (m *GetAllCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllCustomizedPresentConfigResp proto.InternalMessageInfo

func (m *GetAllCustomizedPresentConfigResp) GetConfig() []*CustomizedPresentConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

// 删除礼物配置
type DelCustomizedPresentConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCustomizedPresentConfigReq) Reset()         { *m = DelCustomizedPresentConfigReq{} }
func (m *DelCustomizedPresentConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelCustomizedPresentConfigReq) ProtoMessage()    {}
func (*DelCustomizedPresentConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{60}
}
func (m *DelCustomizedPresentConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCustomizedPresentConfigReq.Unmarshal(m, b)
}
func (m *DelCustomizedPresentConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCustomizedPresentConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelCustomizedPresentConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCustomizedPresentConfigReq.Merge(dst, src)
}
func (m *DelCustomizedPresentConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelCustomizedPresentConfigReq.Size(m)
}
func (m *DelCustomizedPresentConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCustomizedPresentConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelCustomizedPresentConfigReq proto.InternalMessageInfo

func (m *DelCustomizedPresentConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelCustomizedPresentConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelCustomizedPresentConfigResp) Reset()         { *m = DelCustomizedPresentConfigResp{} }
func (m *DelCustomizedPresentConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelCustomizedPresentConfigResp) ProtoMessage()    {}
func (*DelCustomizedPresentConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{61}
}
func (m *DelCustomizedPresentConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelCustomizedPresentConfigResp.Unmarshal(m, b)
}
func (m *DelCustomizedPresentConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelCustomizedPresentConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelCustomizedPresentConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelCustomizedPresentConfigResp.Merge(dst, src)
}
func (m *DelCustomizedPresentConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelCustomizedPresentConfigResp.Size(m)
}
func (m *DelCustomizedPresentConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelCustomizedPresentConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelCustomizedPresentConfigResp proto.InternalMessageInfo

type NotifyPrivilegeLevelChangeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ValueType            uint32   `protobuf:"varint,2,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
	BeforeValue          uint64   `protobuf:"varint,3,opt,name=before_value,json=beforeValue,proto3" json:"before_value,omitempty"`
	AfterValue           uint64   `protobuf:"varint,4,opt,name=after_value,json=afterValue,proto3" json:"after_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyPrivilegeLevelChangeReq) Reset()         { *m = NotifyPrivilegeLevelChangeReq{} }
func (m *NotifyPrivilegeLevelChangeReq) String() string { return proto.CompactTextString(m) }
func (*NotifyPrivilegeLevelChangeReq) ProtoMessage()    {}
func (*NotifyPrivilegeLevelChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{62}
}
func (m *NotifyPrivilegeLevelChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyPrivilegeLevelChangeReq.Unmarshal(m, b)
}
func (m *NotifyPrivilegeLevelChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyPrivilegeLevelChangeReq.Marshal(b, m, deterministic)
}
func (dst *NotifyPrivilegeLevelChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyPrivilegeLevelChangeReq.Merge(dst, src)
}
func (m *NotifyPrivilegeLevelChangeReq) XXX_Size() int {
	return xxx_messageInfo_NotifyPrivilegeLevelChangeReq.Size(m)
}
func (m *NotifyPrivilegeLevelChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyPrivilegeLevelChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyPrivilegeLevelChangeReq proto.InternalMessageInfo

func (m *NotifyPrivilegeLevelChangeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyPrivilegeLevelChangeReq) GetValueType() uint32 {
	if m != nil {
		return m.ValueType
	}
	return 0
}

func (m *NotifyPrivilegeLevelChangeReq) GetBeforeValue() uint64 {
	if m != nil {
		return m.BeforeValue
	}
	return 0
}

func (m *NotifyPrivilegeLevelChangeReq) GetAfterValue() uint64 {
	if m != nil {
		return m.AfterValue
	}
	return 0
}

type NotifyPrivilegeLevelChangeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyPrivilegeLevelChangeResp) Reset()         { *m = NotifyPrivilegeLevelChangeResp{} }
func (m *NotifyPrivilegeLevelChangeResp) String() string { return proto.CompactTextString(m) }
func (*NotifyPrivilegeLevelChangeResp) ProtoMessage()    {}
func (*NotifyPrivilegeLevelChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{63}
}
func (m *NotifyPrivilegeLevelChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyPrivilegeLevelChangeResp.Unmarshal(m, b)
}
func (m *NotifyPrivilegeLevelChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyPrivilegeLevelChangeResp.Marshal(b, m, deterministic)
}
func (dst *NotifyPrivilegeLevelChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyPrivilegeLevelChangeResp.Merge(dst, src)
}
func (m *NotifyPrivilegeLevelChangeResp) XXX_Size() int {
	return xxx_messageInfo_NotifyPrivilegeLevelChangeResp.Size(m)
}
func (m *NotifyPrivilegeLevelChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyPrivilegeLevelChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyPrivilegeLevelChangeResp proto.InternalMessageInfo

type GetCustomPresentEffectTimeReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Level                uint32   `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCustomPresentEffectTimeReq) Reset()         { *m = GetCustomPresentEffectTimeReq{} }
func (m *GetCustomPresentEffectTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetCustomPresentEffectTimeReq) ProtoMessage()    {}
func (*GetCustomPresentEffectTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{64}
}
func (m *GetCustomPresentEffectTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomPresentEffectTimeReq.Unmarshal(m, b)
}
func (m *GetCustomPresentEffectTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomPresentEffectTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetCustomPresentEffectTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomPresentEffectTimeReq.Merge(dst, src)
}
func (m *GetCustomPresentEffectTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetCustomPresentEffectTimeReq.Size(m)
}
func (m *GetCustomPresentEffectTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomPresentEffectTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomPresentEffectTimeReq proto.InternalMessageInfo

func (m *GetCustomPresentEffectTimeReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GetCustomPresentEffectTimeReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type GetCustomPresentEffectTimeResp struct {
	Begin                uint32   `protobuf:"varint,1,opt,name=begin,proto3" json:"begin,omitempty"`
	End                  uint32   `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCustomPresentEffectTimeResp) Reset()         { *m = GetCustomPresentEffectTimeResp{} }
func (m *GetCustomPresentEffectTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetCustomPresentEffectTimeResp) ProtoMessage()    {}
func (*GetCustomPresentEffectTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{65}
}
func (m *GetCustomPresentEffectTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCustomPresentEffectTimeResp.Unmarshal(m, b)
}
func (m *GetCustomPresentEffectTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCustomPresentEffectTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetCustomPresentEffectTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCustomPresentEffectTimeResp.Merge(dst, src)
}
func (m *GetCustomPresentEffectTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetCustomPresentEffectTimeResp.Size(m)
}
func (m *GetCustomPresentEffectTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCustomPresentEffectTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCustomPresentEffectTimeResp proto.InternalMessageInfo

func (m *GetCustomPresentEffectTimeResp) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetCustomPresentEffectTimeResp) GetEnd() uint32 {
	if m != nil {
		return m.End
	}
	return 0
}

type GetPresentEffectTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentEffectTimeReq) Reset()         { *m = GetPresentEffectTimeReq{} }
func (m *GetPresentEffectTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTimeReq) ProtoMessage()    {}
func (*GetPresentEffectTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{66}
}
func (m *GetPresentEffectTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTimeReq.Unmarshal(m, b)
}
func (m *GetPresentEffectTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTimeReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTimeReq.Merge(dst, src)
}
func (m *GetPresentEffectTimeReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTimeReq.Size(m)
}
func (m *GetPresentEffectTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTimeReq proto.InternalMessageInfo

func (m *GetPresentEffectTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPresentEffectTimeResp struct {
	PresentEffectTimeInfos []*PresentEffectTime `protobuf:"bytes,1,rep,name=present_effect_time_infos,json=presentEffectTimeInfos,proto3" json:"present_effect_time_infos,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}             `json:"-"`
	XXX_unrecognized       []byte               `json:"-"`
	XXX_sizecache          int32                `json:"-"`
}

func (m *GetPresentEffectTimeResp) Reset()         { *m = GetPresentEffectTimeResp{} }
func (m *GetPresentEffectTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTimeResp) ProtoMessage()    {}
func (*GetPresentEffectTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{67}
}
func (m *GetPresentEffectTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTimeResp.Unmarshal(m, b)
}
func (m *GetPresentEffectTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTimeResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTimeResp.Merge(dst, src)
}
func (m *GetPresentEffectTimeResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTimeResp.Size(m)
}
func (m *GetPresentEffectTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTimeResp proto.InternalMessageInfo

func (m *GetPresentEffectTimeResp) GetPresentEffectTimeInfos() []*PresentEffectTime {
	if m != nil {
		return m.PresentEffectTimeInfos
	}
	return nil
}

type PresentEffectTime struct {
	GiftId               uint32                 `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	EffectEnd            uint32                 `protobuf:"varint,2,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	EffectInfo           *PresentEffectTimeInfo `protobuf:"bytes,3,opt,name=effect_info,json=effectInfo,proto3" json:"effect_info,omitempty"`
	IsNewLevel           bool                   `protobuf:"varint,4,opt,name=is_new_level,json=isNewLevel,proto3" json:"is_new_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PresentEffectTime) Reset()         { *m = PresentEffectTime{} }
func (m *PresentEffectTime) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTime) ProtoMessage()    {}
func (*PresentEffectTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{68}
}
func (m *PresentEffectTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTime.Unmarshal(m, b)
}
func (m *PresentEffectTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTime.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTime.Merge(dst, src)
}
func (m *PresentEffectTime) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTime.Size(m)
}
func (m *PresentEffectTime) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTime.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTime proto.InternalMessageInfo

func (m *PresentEffectTime) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *PresentEffectTime) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

func (m *PresentEffectTime) GetEffectInfo() *PresentEffectTimeInfo {
	if m != nil {
		return m.EffectInfo
	}
	return nil
}

func (m *PresentEffectTime) GetIsNewLevel() bool {
	if m != nil {
		return m.IsNewLevel
	}
	return false
}

type PresentEffectTimeInfo struct {
	NowCount              uint32   `protobuf:"varint,1,opt,name=now_count,json=nowCount,proto3" json:"now_count,omitempty"`
	NextLevelSendCount    uint32   `protobuf:"varint,2,opt,name=next_level_send_count,json=nextLevelSendCount,proto3" json:"next_level_send_count,omitempty"`
	NextLevelDayCount     uint32   `protobuf:"varint,3,opt,name=next_level_day_count,json=nextLevelDayCount,proto3" json:"next_level_day_count,omitempty"`
	MaxLevelSendCount     uint32   `protobuf:"varint,4,opt,name=max_level_send_count,json=maxLevelSendCount,proto3" json:"max_level_send_count,omitempty"`
	IsMaxLevel            bool     `protobuf:"varint,5,opt,name=is_max_level,json=isMaxLevel,proto3" json:"is_max_level,omitempty"`
	NoLimitExpireDayCount uint32   `protobuf:"varint,6,opt,name=no_limit_expire_day_count,json=noLimitExpireDayCount,proto3" json:"no_limit_expire_day_count,omitempty"`
	LastSendTs            uint32   `protobuf:"varint,7,opt,name=last_send_ts,json=lastSendTs,proto3" json:"last_send_ts,omitempty"`
	MaxLevelDayCount      uint32   `protobuf:"varint,8,opt,name=max_level_day_count,json=maxLevelDayCount,proto3" json:"max_level_day_count,omitempty"`
	EffectEndOnShelf      uint32   `protobuf:"varint,9,opt,name=effect_end_on_shelf,json=effectEndOnShelf,proto3" json:"effect_end_on_shelf,omitempty"`
	NowLevelDayCount      uint32   `protobuf:"varint,10,opt,name=now_level_day_count,json=nowLevelDayCount,proto3" json:"now_level_day_count,omitempty"`
	NoticeNoLimitExpire   bool     `protobuf:"varint,11,opt,name=notice_no_limit_expire,json=noticeNoLimitExpire,proto3" json:"notice_no_limit_expire,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *PresentEffectTimeInfo) Reset()         { *m = PresentEffectTimeInfo{} }
func (m *PresentEffectTimeInfo) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTimeInfo) ProtoMessage()    {}
func (*PresentEffectTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{69}
}
func (m *PresentEffectTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTimeInfo.Unmarshal(m, b)
}
func (m *PresentEffectTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTimeInfo.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTimeInfo.Merge(dst, src)
}
func (m *PresentEffectTimeInfo) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTimeInfo.Size(m)
}
func (m *PresentEffectTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTimeInfo proto.InternalMessageInfo

func (m *PresentEffectTimeInfo) GetNowCount() uint32 {
	if m != nil {
		return m.NowCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNextLevelSendCount() uint32 {
	if m != nil {
		return m.NextLevelSendCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNextLevelDayCount() uint32 {
	if m != nil {
		return m.NextLevelDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetMaxLevelSendCount() uint32 {
	if m != nil {
		return m.MaxLevelSendCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetIsMaxLevel() bool {
	if m != nil {
		return m.IsMaxLevel
	}
	return false
}

func (m *PresentEffectTimeInfo) GetNoLimitExpireDayCount() uint32 {
	if m != nil {
		return m.NoLimitExpireDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetLastSendTs() uint32 {
	if m != nil {
		return m.LastSendTs
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetMaxLevelDayCount() uint32 {
	if m != nil {
		return m.MaxLevelDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetEffectEndOnShelf() uint32 {
	if m != nil {
		return m.EffectEndOnShelf
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNowLevelDayCount() uint32 {
	if m != nil {
		return m.NowLevelDayCount
	}
	return 0
}

func (m *PresentEffectTimeInfo) GetNoticeNoLimitExpire() bool {
	if m != nil {
		return m.NoticeNoLimitExpire
	}
	return false
}

type GetPresentEffectTimeDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPresentEffectTimeDetailReq) Reset()         { *m = GetPresentEffectTimeDetailReq{} }
func (m *GetPresentEffectTimeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTimeDetailReq) ProtoMessage()    {}
func (*GetPresentEffectTimeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{70}
}
func (m *GetPresentEffectTimeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTimeDetailReq.Unmarshal(m, b)
}
func (m *GetPresentEffectTimeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTimeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTimeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTimeDetailReq.Merge(dst, src)
}
func (m *GetPresentEffectTimeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTimeDetailReq.Size(m)
}
func (m *GetPresentEffectTimeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTimeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTimeDetailReq proto.InternalMessageInfo

func (m *GetPresentEffectTimeDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPresentEffectTimeDetailReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type GetPresentEffectTimeDetailResp struct {
	GiftId                uint32                        `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	NowCount              uint32                        `protobuf:"varint,2,opt,name=now_count,json=nowCount,proto3" json:"now_count,omitempty"`
	LevelInfo             []*PresentEffectTimeLevelInfo `protobuf:"bytes,3,rep,name=level_info,json=levelInfo,proto3" json:"level_info,omitempty"`
	NoLimitExpireDayCount uint32                        `protobuf:"varint,4,opt,name=no_limit_expire_day_count,json=noLimitExpireDayCount,proto3" json:"no_limit_expire_day_count,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                      `json:"-"`
	XXX_unrecognized      []byte                        `json:"-"`
	XXX_sizecache         int32                         `json:"-"`
}

func (m *GetPresentEffectTimeDetailResp) Reset()         { *m = GetPresentEffectTimeDetailResp{} }
func (m *GetPresentEffectTimeDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetPresentEffectTimeDetailResp) ProtoMessage()    {}
func (*GetPresentEffectTimeDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{71}
}
func (m *GetPresentEffectTimeDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPresentEffectTimeDetailResp.Unmarshal(m, b)
}
func (m *GetPresentEffectTimeDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPresentEffectTimeDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetPresentEffectTimeDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPresentEffectTimeDetailResp.Merge(dst, src)
}
func (m *GetPresentEffectTimeDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetPresentEffectTimeDetailResp.Size(m)
}
func (m *GetPresentEffectTimeDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPresentEffectTimeDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPresentEffectTimeDetailResp proto.InternalMessageInfo

func (m *GetPresentEffectTimeDetailResp) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *GetPresentEffectTimeDetailResp) GetNowCount() uint32 {
	if m != nil {
		return m.NowCount
	}
	return 0
}

func (m *GetPresentEffectTimeDetailResp) GetLevelInfo() []*PresentEffectTimeLevelInfo {
	if m != nil {
		return m.LevelInfo
	}
	return nil
}

func (m *GetPresentEffectTimeDetailResp) GetNoLimitExpireDayCount() uint32 {
	if m != nil {
		return m.NoLimitExpireDayCount
	}
	return 0
}

type PresentEffectTimeLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelSendCount       uint32   `protobuf:"varint,2,opt,name=level_send_count,json=levelSendCount,proto3" json:"level_send_count,omitempty"`
	LevelDayCount        uint32   `protobuf:"varint,3,opt,name=level_day_count,json=levelDayCount,proto3" json:"level_day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PresentEffectTimeLevelInfo) Reset()         { *m = PresentEffectTimeLevelInfo{} }
func (m *PresentEffectTimeLevelInfo) String() string { return proto.CompactTextString(m) }
func (*PresentEffectTimeLevelInfo) ProtoMessage()    {}
func (*PresentEffectTimeLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{72}
}
func (m *PresentEffectTimeLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PresentEffectTimeLevelInfo.Unmarshal(m, b)
}
func (m *PresentEffectTimeLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PresentEffectTimeLevelInfo.Marshal(b, m, deterministic)
}
func (dst *PresentEffectTimeLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PresentEffectTimeLevelInfo.Merge(dst, src)
}
func (m *PresentEffectTimeLevelInfo) XXX_Size() int {
	return xxx_messageInfo_PresentEffectTimeLevelInfo.Size(m)
}
func (m *PresentEffectTimeLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PresentEffectTimeLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PresentEffectTimeLevelInfo proto.InternalMessageInfo

func (m *PresentEffectTimeLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *PresentEffectTimeLevelInfo) GetLevelSendCount() uint32 {
	if m != nil {
		return m.LevelSendCount
	}
	return 0
}

func (m *PresentEffectTimeLevelInfo) GetLevelDayCount() uint32 {
	if m != nil {
		return m.LevelDayCount
	}
	return 0
}

// 添加限时参数
type AddEffectDelayLevelReq struct {
	GiftId               uint32                  `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo `protobuf:"bytes,2,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	EffectBegin          uint32                  `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                  `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AddEffectDelayLevelReq) Reset()         { *m = AddEffectDelayLevelReq{} }
func (m *AddEffectDelayLevelReq) String() string { return proto.CompactTextString(m) }
func (*AddEffectDelayLevelReq) ProtoMessage()    {}
func (*AddEffectDelayLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{73}
}
func (m *AddEffectDelayLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEffectDelayLevelReq.Unmarshal(m, b)
}
func (m *AddEffectDelayLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEffectDelayLevelReq.Marshal(b, m, deterministic)
}
func (dst *AddEffectDelayLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEffectDelayLevelReq.Merge(dst, src)
}
func (m *AddEffectDelayLevelReq) XXX_Size() int {
	return xxx_messageInfo_AddEffectDelayLevelReq.Size(m)
}
func (m *AddEffectDelayLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEffectDelayLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddEffectDelayLevelReq proto.InternalMessageInfo

func (m *AddEffectDelayLevelReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *AddEffectDelayLevelReq) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *AddEffectDelayLevelReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *AddEffectDelayLevelReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type EffectDelayLevelInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	SendCount            uint32   `protobuf:"varint,2,opt,name=send_count,json=sendCount,proto3" json:"send_count,omitempty"`
	DayCount             uint32   `protobuf:"varint,3,opt,name=day_count,json=dayCount,proto3" json:"day_count,omitempty"`
	ExpireDayCount       uint32   `protobuf:"varint,4,opt,name=expire_day_count,json=expireDayCount,proto3" json:"expire_day_count,omitempty"`
	NoticeDayCount       uint32   `protobuf:"varint,5,opt,name=notice_day_count,json=noticeDayCount,proto3" json:"notice_day_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EffectDelayLevelInfo) Reset()         { *m = EffectDelayLevelInfo{} }
func (m *EffectDelayLevelInfo) String() string { return proto.CompactTextString(m) }
func (*EffectDelayLevelInfo) ProtoMessage()    {}
func (*EffectDelayLevelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{74}
}
func (m *EffectDelayLevelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectDelayLevelInfo.Unmarshal(m, b)
}
func (m *EffectDelayLevelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectDelayLevelInfo.Marshal(b, m, deterministic)
}
func (dst *EffectDelayLevelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectDelayLevelInfo.Merge(dst, src)
}
func (m *EffectDelayLevelInfo) XXX_Size() int {
	return xxx_messageInfo_EffectDelayLevelInfo.Size(m)
}
func (m *EffectDelayLevelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectDelayLevelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_EffectDelayLevelInfo proto.InternalMessageInfo

func (m *EffectDelayLevelInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetSendCount() uint32 {
	if m != nil {
		return m.SendCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetDayCount() uint32 {
	if m != nil {
		return m.DayCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetExpireDayCount() uint32 {
	if m != nil {
		return m.ExpireDayCount
	}
	return 0
}

func (m *EffectDelayLevelInfo) GetNoticeDayCount() uint32 {
	if m != nil {
		return m.NoticeDayCount
	}
	return 0
}

type AddEffectDelayLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddEffectDelayLevelResp) Reset()         { *m = AddEffectDelayLevelResp{} }
func (m *AddEffectDelayLevelResp) String() string { return proto.CompactTextString(m) }
func (*AddEffectDelayLevelResp) ProtoMessage()    {}
func (*AddEffectDelayLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{75}
}
func (m *AddEffectDelayLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddEffectDelayLevelResp.Unmarshal(m, b)
}
func (m *AddEffectDelayLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddEffectDelayLevelResp.Marshal(b, m, deterministic)
}
func (dst *AddEffectDelayLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddEffectDelayLevelResp.Merge(dst, src)
}
func (m *AddEffectDelayLevelResp) XXX_Size() int {
	return xxx_messageInfo_AddEffectDelayLevelResp.Size(m)
}
func (m *AddEffectDelayLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddEffectDelayLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddEffectDelayLevelResp proto.InternalMessageInfo

// 获取限时参数
type GetEffectDelayLevelReq struct {
	ItemId               uint32   `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEffectDelayLevelReq) Reset()         { *m = GetEffectDelayLevelReq{} }
func (m *GetEffectDelayLevelReq) String() string { return proto.CompactTextString(m) }
func (*GetEffectDelayLevelReq) ProtoMessage()    {}
func (*GetEffectDelayLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{76}
}
func (m *GetEffectDelayLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEffectDelayLevelReq.Unmarshal(m, b)
}
func (m *GetEffectDelayLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEffectDelayLevelReq.Marshal(b, m, deterministic)
}
func (dst *GetEffectDelayLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEffectDelayLevelReq.Merge(dst, src)
}
func (m *GetEffectDelayLevelReq) XXX_Size() int {
	return xxx_messageInfo_GetEffectDelayLevelReq.Size(m)
}
func (m *GetEffectDelayLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEffectDelayLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEffectDelayLevelReq proto.InternalMessageInfo

func (m *GetEffectDelayLevelReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

type GetEffectDelayLevelResp struct {
	DelayConfig          []*EffectLevelConfig `protobuf:"bytes,1,rep,name=delay_config,json=delayConfig,proto3" json:"delay_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetEffectDelayLevelResp) Reset()         { *m = GetEffectDelayLevelResp{} }
func (m *GetEffectDelayLevelResp) String() string { return proto.CompactTextString(m) }
func (*GetEffectDelayLevelResp) ProtoMessage()    {}
func (*GetEffectDelayLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{77}
}
func (m *GetEffectDelayLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEffectDelayLevelResp.Unmarshal(m, b)
}
func (m *GetEffectDelayLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEffectDelayLevelResp.Marshal(b, m, deterministic)
}
func (dst *GetEffectDelayLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEffectDelayLevelResp.Merge(dst, src)
}
func (m *GetEffectDelayLevelResp) XXX_Size() int {
	return xxx_messageInfo_GetEffectDelayLevelResp.Size(m)
}
func (m *GetEffectDelayLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEffectDelayLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEffectDelayLevelResp proto.InternalMessageInfo

func (m *GetEffectDelayLevelResp) GetDelayConfig() []*EffectLevelConfig {
	if m != nil {
		return m.DelayConfig
	}
	return nil
}

type EffectLevelConfig struct {
	GiftId               uint32                  `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo `protobuf:"bytes,2,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	EffectBegin          uint32                  `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                  `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *EffectLevelConfig) Reset()         { *m = EffectLevelConfig{} }
func (m *EffectLevelConfig) String() string { return proto.CompactTextString(m) }
func (*EffectLevelConfig) ProtoMessage()    {}
func (*EffectLevelConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{78}
}
func (m *EffectLevelConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EffectLevelConfig.Unmarshal(m, b)
}
func (m *EffectLevelConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EffectLevelConfig.Marshal(b, m, deterministic)
}
func (dst *EffectLevelConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EffectLevelConfig.Merge(dst, src)
}
func (m *EffectLevelConfig) XXX_Size() int {
	return xxx_messageInfo_EffectLevelConfig.Size(m)
}
func (m *EffectLevelConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_EffectLevelConfig.DiscardUnknown(m)
}

var xxx_messageInfo_EffectLevelConfig proto.InternalMessageInfo

func (m *EffectLevelConfig) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *EffectLevelConfig) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *EffectLevelConfig) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *EffectLevelConfig) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

// 更新限时参数
type UpdateEffectDelayLevelReq struct {
	GiftId               uint32                  `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	DelayInfo            []*EffectDelayLevelInfo `protobuf:"bytes,2,rep,name=delay_info,json=delayInfo,proto3" json:"delay_info,omitempty"`
	EffectBegin          uint32                  `protobuf:"varint,3,opt,name=effect_begin,json=effectBegin,proto3" json:"effect_begin,omitempty"`
	EffectEnd            uint32                  `protobuf:"varint,4,opt,name=effect_end,json=effectEnd,proto3" json:"effect_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateEffectDelayLevelReq) Reset()         { *m = UpdateEffectDelayLevelReq{} }
func (m *UpdateEffectDelayLevelReq) String() string { return proto.CompactTextString(m) }
func (*UpdateEffectDelayLevelReq) ProtoMessage()    {}
func (*UpdateEffectDelayLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{79}
}
func (m *UpdateEffectDelayLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEffectDelayLevelReq.Unmarshal(m, b)
}
func (m *UpdateEffectDelayLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEffectDelayLevelReq.Marshal(b, m, deterministic)
}
func (dst *UpdateEffectDelayLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEffectDelayLevelReq.Merge(dst, src)
}
func (m *UpdateEffectDelayLevelReq) XXX_Size() int {
	return xxx_messageInfo_UpdateEffectDelayLevelReq.Size(m)
}
func (m *UpdateEffectDelayLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEffectDelayLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEffectDelayLevelReq proto.InternalMessageInfo

func (m *UpdateEffectDelayLevelReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *UpdateEffectDelayLevelReq) GetDelayInfo() []*EffectDelayLevelInfo {
	if m != nil {
		return m.DelayInfo
	}
	return nil
}

func (m *UpdateEffectDelayLevelReq) GetEffectBegin() uint32 {
	if m != nil {
		return m.EffectBegin
	}
	return 0
}

func (m *UpdateEffectDelayLevelReq) GetEffectEnd() uint32 {
	if m != nil {
		return m.EffectEnd
	}
	return 0
}

type UpdateEffectDelayLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEffectDelayLevelResp) Reset()         { *m = UpdateEffectDelayLevelResp{} }
func (m *UpdateEffectDelayLevelResp) String() string { return proto.CompactTextString(m) }
func (*UpdateEffectDelayLevelResp) ProtoMessage()    {}
func (*UpdateEffectDelayLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{80}
}
func (m *UpdateEffectDelayLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEffectDelayLevelResp.Unmarshal(m, b)
}
func (m *UpdateEffectDelayLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEffectDelayLevelResp.Marshal(b, m, deterministic)
}
func (dst *UpdateEffectDelayLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEffectDelayLevelResp.Merge(dst, src)
}
func (m *UpdateEffectDelayLevelResp) XXX_Size() int {
	return xxx_messageInfo_UpdateEffectDelayLevelResp.Size(m)
}
func (m *UpdateEffectDelayLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEffectDelayLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEffectDelayLevelResp proto.InternalMessageInfo

type NotifyPresentSendReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyPresentSendReq) Reset()         { *m = NotifyPresentSendReq{} }
func (m *NotifyPresentSendReq) String() string { return proto.CompactTextString(m) }
func (*NotifyPresentSendReq) ProtoMessage()    {}
func (*NotifyPresentSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{81}
}
func (m *NotifyPresentSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyPresentSendReq.Unmarshal(m, b)
}
func (m *NotifyPresentSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyPresentSendReq.Marshal(b, m, deterministic)
}
func (dst *NotifyPresentSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyPresentSendReq.Merge(dst, src)
}
func (m *NotifyPresentSendReq) XXX_Size() int {
	return xxx_messageInfo_NotifyPresentSendReq.Size(m)
}
func (m *NotifyPresentSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyPresentSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyPresentSendReq proto.InternalMessageInfo

func (m *NotifyPresentSendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NotifyPresentSendReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *NotifyPresentSendReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type NotifyPresentSendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyPresentSendResp) Reset()         { *m = NotifyPresentSendResp{} }
func (m *NotifyPresentSendResp) String() string { return proto.CompactTextString(m) }
func (*NotifyPresentSendResp) ProtoMessage()    {}
func (*NotifyPresentSendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{82}
}
func (m *NotifyPresentSendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyPresentSendResp.Unmarshal(m, b)
}
func (m *NotifyPresentSendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyPresentSendResp.Marshal(b, m, deterministic)
}
func (dst *NotifyPresentSendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyPresentSendResp.Merge(dst, src)
}
func (m *NotifyPresentSendResp) XXX_Size() int {
	return xxx_messageInfo_NotifyPresentSendResp.Size(m)
}
func (m *NotifyPresentSendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyPresentSendResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyPresentSendResp proto.InternalMessageInfo

type DelEffectDelayLevelReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelEffectDelayLevelReq) Reset()         { *m = DelEffectDelayLevelReq{} }
func (m *DelEffectDelayLevelReq) String() string { return proto.CompactTextString(m) }
func (*DelEffectDelayLevelReq) ProtoMessage()    {}
func (*DelEffectDelayLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{83}
}
func (m *DelEffectDelayLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelEffectDelayLevelReq.Unmarshal(m, b)
}
func (m *DelEffectDelayLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelEffectDelayLevelReq.Marshal(b, m, deterministic)
}
func (dst *DelEffectDelayLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelEffectDelayLevelReq.Merge(dst, src)
}
func (m *DelEffectDelayLevelReq) XXX_Size() int {
	return xxx_messageInfo_DelEffectDelayLevelReq.Size(m)
}
func (m *DelEffectDelayLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelEffectDelayLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelEffectDelayLevelReq proto.InternalMessageInfo

func (m *DelEffectDelayLevelReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type DelEffectDelayLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelEffectDelayLevelResp) Reset()         { *m = DelEffectDelayLevelResp{} }
func (m *DelEffectDelayLevelResp) String() string { return proto.CompactTextString(m) }
func (*DelEffectDelayLevelResp) ProtoMessage()    {}
func (*DelEffectDelayLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{84}
}
func (m *DelEffectDelayLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelEffectDelayLevelResp.Unmarshal(m, b)
}
func (m *DelEffectDelayLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelEffectDelayLevelResp.Marshal(b, m, deterministic)
}
func (dst *DelEffectDelayLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelEffectDelayLevelResp.Merge(dst, src)
}
func (m *DelEffectDelayLevelResp) XXX_Size() int {
	return xxx_messageInfo_DelEffectDelayLevelResp.Size(m)
}
func (m *DelEffectDelayLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelEffectDelayLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelEffectDelayLevelResp proto.InternalMessageInfo

type ReportCustomPresentSendReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,4,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	OrderId              string   `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportCustomPresentSendReq) Reset()         { *m = ReportCustomPresentSendReq{} }
func (m *ReportCustomPresentSendReq) String() string { return proto.CompactTextString(m) }
func (*ReportCustomPresentSendReq) ProtoMessage()    {}
func (*ReportCustomPresentSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{85}
}
func (m *ReportCustomPresentSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCustomPresentSendReq.Unmarshal(m, b)
}
func (m *ReportCustomPresentSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCustomPresentSendReq.Marshal(b, m, deterministic)
}
func (dst *ReportCustomPresentSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCustomPresentSendReq.Merge(dst, src)
}
func (m *ReportCustomPresentSendReq) XXX_Size() int {
	return xxx_messageInfo_ReportCustomPresentSendReq.Size(m)
}
func (m *ReportCustomPresentSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCustomPresentSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCustomPresentSendReq proto.InternalMessageInfo

func (m *ReportCustomPresentSendReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportCustomPresentSendReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *ReportCustomPresentSendReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportCustomPresentSendReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *ReportCustomPresentSendReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ReportCustomPresentSendReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type ReportCustomPresentSendResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportCustomPresentSendResp) Reset()         { *m = ReportCustomPresentSendResp{} }
func (m *ReportCustomPresentSendResp) String() string { return proto.CompactTextString(m) }
func (*ReportCustomPresentSendResp) ProtoMessage()    {}
func (*ReportCustomPresentSendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_present_extra_conf_ce33501c7383c61c, []int{86}
}
func (m *ReportCustomPresentSendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportCustomPresentSendResp.Unmarshal(m, b)
}
func (m *ReportCustomPresentSendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportCustomPresentSendResp.Marshal(b, m, deterministic)
}
func (dst *ReportCustomPresentSendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportCustomPresentSendResp.Merge(dst, src)
}
func (m *ReportCustomPresentSendResp) XXX_Size() int {
	return xxx_messageInfo_ReportCustomPresentSendResp.Size(m)
}
func (m *ReportCustomPresentSendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportCustomPresentSendResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportCustomPresentSendResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetPopUpPresentListReq)(nil), "presentextraconf.GetPopUpPresentListReq")
	proto.RegisterType((*GetPopUpPresentListResp)(nil), "presentextraconf.GetPopUpPresentListResp")
	proto.RegisterType((*SearchPresentReq)(nil), "presentextraconf.SearchPresentReq")
	proto.RegisterType((*SearchPresentResp)(nil), "presentextraconf.SearchPresentResp")
	proto.RegisterType((*GetPresentFloatLayerReq)(nil), "presentextraconf.GetPresentFloatLayerReq")
	proto.RegisterType((*GetPresentFloatLayerResp)(nil), "presentextraconf.GetPresentFloatLayerResp")
	proto.RegisterType((*AddPresentFloatLayerReq)(nil), "presentextraconf.AddPresentFloatLayerReq")
	proto.RegisterType((*AddPresentFloatLayerResp)(nil), "presentextraconf.AddPresentFloatLayerResp")
	proto.RegisterType((*UpdatePresentFloatLayerReq)(nil), "presentextraconf.UpdatePresentFloatLayerReq")
	proto.RegisterType((*UpdatePresentFloatLayerResp)(nil), "presentextraconf.UpdatePresentFloatLayerResp")
	proto.RegisterType((*DelPresentFloatLayerReq)(nil), "presentextraconf.DelPresentFloatLayerReq")
	proto.RegisterType((*DelPresentFloatLayerResp)(nil), "presentextraconf.DelPresentFloatLayerResp")
	proto.RegisterType((*PresentFloatInfo)(nil), "presentextraconf.PresentFloatInfo")
	proto.RegisterType((*PresentFloatLayer)(nil), "presentextraconf.PresentFloatLayer")
	proto.RegisterType((*AppUrl)(nil), "presentextraconf.AppUrl")
	proto.RegisterType((*PresentBaseConfig)(nil), "presentextraconf.PresentBaseConfig")
	proto.RegisterType((*PresentFlashEffect)(nil), "presentextraconf.PresentFlashEffect")
	proto.RegisterType((*FlashEffectConfig)(nil), "presentextraconf.FlashEffectConfig")
	proto.RegisterType((*GetFlashEffectConfigReq)(nil), "presentextraconf.GetFlashEffectConfigReq")
	proto.RegisterType((*GetFlashEffectConfigResp)(nil), "presentextraconf.GetFlashEffectConfigResp")
	proto.RegisterType((*AddFlashEffectConfigReq)(nil), "presentextraconf.AddFlashEffectConfigReq")
	proto.RegisterType((*AddFlashEffectConfigResp)(nil), "presentextraconf.AddFlashEffectConfigResp")
	proto.RegisterType((*UpdateFlashEffectConfigReq)(nil), "presentextraconf.UpdateFlashEffectConfigReq")
	proto.RegisterType((*UpdateFlashEffectConfigResp)(nil), "presentextraconf.UpdateFlashEffectConfigResp")
	proto.RegisterType((*DelFlashEffectConfigReq)(nil), "presentextraconf.DelFlashEffectConfigReq")
	proto.RegisterType((*DelFlashEffectConfigResp)(nil), "presentextraconf.DelFlashEffectConfigResp")
	proto.RegisterType((*PresentFlashInfo)(nil), "presentextraconf.PresentFlashInfo")
	proto.RegisterType((*GetPresentFlashEffectReq)(nil), "presentextraconf.GetPresentFlashEffectReq")
	proto.RegisterType((*GetPresentFlashEffectResp)(nil), "presentextraconf.GetPresentFlashEffectResp")
	proto.RegisterType((*BoundPresentFlashEffectReq)(nil), "presentextraconf.BoundPresentFlashEffectReq")
	proto.RegisterType((*BoundPresentFlashEffectResp)(nil), "presentextraconf.BoundPresentFlashEffectResp")
	proto.RegisterType((*GetUserCustomizedInfoReq)(nil), "presentextraconf.GetUserCustomizedInfoReq")
	proto.RegisterType((*GetUserCustomizedInfoResp)(nil), "presentextraconf.GetUserCustomizedInfoResp")
	proto.RegisterType((*PresentEffectAppend)(nil), "presentextraconf.PresentEffectAppend")
	proto.RegisterType((*UserCustomizedInfo)(nil), "presentextraconf.UserCustomizedInfo")
	proto.RegisterType((*CustomizedPresentInfo)(nil), "presentextraconf.CustomizedPresentInfo")
	proto.RegisterType((*GetUserCustomizedInfoByGiftIdReq)(nil), "presentextraconf.GetUserCustomizedInfoByGiftIdReq")
	proto.RegisterType((*GetUserCustomizedInfoByGiftIdResp)(nil), "presentextraconf.GetUserCustomizedInfoByGiftIdResp")
	proto.RegisterType((*CustomizedPresentDetail)(nil), "presentextraconf.CustomizedPresentDetail")
	proto.RegisterMapType((map[string]uint32)(nil), "presentextraconf.CustomizedPresentDetail.CustomMethodEntry")
	proto.RegisterMapType((map[uint32]*CustomPresentPreview)(nil), "presentextraconf.CustomizedPresentDetail.PreviewMapEntry")
	proto.RegisterType((*CustomPresentPreview)(nil), "presentextraconf.CustomPresentPreview")
	proto.RegisterType((*CustomOption)(nil), "presentextraconf.CustomOption")
	proto.RegisterType((*OptionInfo)(nil), "presentextraconf.OptionInfo")
	proto.RegisterType((*CustomOptionConfig)(nil), "presentextraconf.CustomOptionConfig")
	proto.RegisterType((*ReportCustomOptionChooseReq)(nil), "presentextraconf.ReportCustomOptionChooseReq")
	proto.RegisterType((*ReportCustomOptionChooseResp)(nil), "presentextraconf.ReportCustomOptionChooseResp")
	proto.RegisterType((*CustomPair)(nil), "presentextraconf.CustomPair")
	proto.RegisterType((*CheckCustomizedGiftReq)(nil), "presentextraconf.CheckCustomizedGiftReq")
	proto.RegisterType((*CheckCustomizedGiftResp)(nil), "presentextraconf.CheckCustomizedGiftResp")
	proto.RegisterType((*AddCustomizedPresentConfigReq)(nil), "presentextraconf.AddCustomizedPresentConfigReq")
	proto.RegisterType((*AddCustomizedPresentConfigResp)(nil), "presentextraconf.AddCustomizedPresentConfigResp")
	proto.RegisterType((*CustomizedPresentConfig)(nil), "presentextraconf.CustomizedPresentConfig")
	proto.RegisterType((*CustomConfig)(nil), "presentextraconf.CustomConfig")
	proto.RegisterType((*OptionConfig)(nil), "presentextraconf.OptionConfig")
	proto.RegisterType((*LevelConfig)(nil), "presentextraconf.LevelConfig")
	proto.RegisterType((*CustomMethod)(nil), "presentextraconf.CustomMethod")
	proto.RegisterType((*LevelEffectConfig)(nil), "presentextraconf.LevelEffectConfig")
	proto.RegisterType((*UpdateCustomizedPresentConfigReq)(nil), "presentextraconf.UpdateCustomizedPresentConfigReq")
	proto.RegisterType((*UpdateCustomizedPresentConfigResp)(nil), "presentextraconf.UpdateCustomizedPresentConfigResp")
	proto.RegisterType((*GetAllCustomizedPresentConfigReq)(nil), "presentextraconf.GetAllCustomizedPresentConfigReq")
	proto.RegisterType((*GetAllCustomizedPresentConfigResp)(nil), "presentextraconf.GetAllCustomizedPresentConfigResp")
	proto.RegisterType((*DelCustomizedPresentConfigReq)(nil), "presentextraconf.DelCustomizedPresentConfigReq")
	proto.RegisterType((*DelCustomizedPresentConfigResp)(nil), "presentextraconf.DelCustomizedPresentConfigResp")
	proto.RegisterType((*NotifyPrivilegeLevelChangeReq)(nil), "presentextraconf.NotifyPrivilegeLevelChangeReq")
	proto.RegisterType((*NotifyPrivilegeLevelChangeResp)(nil), "presentextraconf.NotifyPrivilegeLevelChangeResp")
	proto.RegisterType((*GetCustomPresentEffectTimeReq)(nil), "presentextraconf.GetCustomPresentEffectTimeReq")
	proto.RegisterType((*GetCustomPresentEffectTimeResp)(nil), "presentextraconf.GetCustomPresentEffectTimeResp")
	proto.RegisterType((*GetPresentEffectTimeReq)(nil), "presentextraconf.GetPresentEffectTimeReq")
	proto.RegisterType((*GetPresentEffectTimeResp)(nil), "presentextraconf.GetPresentEffectTimeResp")
	proto.RegisterType((*PresentEffectTime)(nil), "presentextraconf.PresentEffectTime")
	proto.RegisterType((*PresentEffectTimeInfo)(nil), "presentextraconf.PresentEffectTimeInfo")
	proto.RegisterType((*GetPresentEffectTimeDetailReq)(nil), "presentextraconf.GetPresentEffectTimeDetailReq")
	proto.RegisterType((*GetPresentEffectTimeDetailResp)(nil), "presentextraconf.GetPresentEffectTimeDetailResp")
	proto.RegisterType((*PresentEffectTimeLevelInfo)(nil), "presentextraconf.PresentEffectTimeLevelInfo")
	proto.RegisterType((*AddEffectDelayLevelReq)(nil), "presentextraconf.AddEffectDelayLevelReq")
	proto.RegisterType((*EffectDelayLevelInfo)(nil), "presentextraconf.EffectDelayLevelInfo")
	proto.RegisterType((*AddEffectDelayLevelResp)(nil), "presentextraconf.AddEffectDelayLevelResp")
	proto.RegisterType((*GetEffectDelayLevelReq)(nil), "presentextraconf.GetEffectDelayLevelReq")
	proto.RegisterType((*GetEffectDelayLevelResp)(nil), "presentextraconf.GetEffectDelayLevelResp")
	proto.RegisterType((*EffectLevelConfig)(nil), "presentextraconf.EffectLevelConfig")
	proto.RegisterType((*UpdateEffectDelayLevelReq)(nil), "presentextraconf.UpdateEffectDelayLevelReq")
	proto.RegisterType((*UpdateEffectDelayLevelResp)(nil), "presentextraconf.UpdateEffectDelayLevelResp")
	proto.RegisterType((*NotifyPresentSendReq)(nil), "presentextraconf.NotifyPresentSendReq")
	proto.RegisterType((*NotifyPresentSendResp)(nil), "presentextraconf.NotifyPresentSendResp")
	proto.RegisterType((*DelEffectDelayLevelReq)(nil), "presentextraconf.DelEffectDelayLevelReq")
	proto.RegisterType((*DelEffectDelayLevelResp)(nil), "presentextraconf.DelEffectDelayLevelResp")
	proto.RegisterType((*ReportCustomPresentSendReq)(nil), "presentextraconf.ReportCustomPresentSendReq")
	proto.RegisterType((*ReportCustomPresentSendResp)(nil), "presentextraconf.ReportCustomPresentSendResp")
	proto.RegisterEnum("presentextraconf.EffectStatus", EffectStatus_name, EffectStatus_value)
	proto.RegisterEnum("presentextraconf.CustomPreviewType", CustomPreviewType_name, CustomPreviewType_value)
	proto.RegisterEnum("presentextraconf.ValueType", ValueType_name, ValueType_value)
	proto.RegisterEnum("presentextraconf.PresentFloatLayer_ChannelType", PresentFloatLayer_ChannelType_name, PresentFloatLayer_ChannelType_value)
	proto.RegisterEnum("presentextraconf.PresentFloatLayer_AppType", PresentFloatLayer_AppType_name, PresentFloatLayer_AppType_value)
	proto.RegisterEnum("presentextraconf.PresentFloatLayer_ActivityType", PresentFloatLayer_ActivityType_name, PresentFloatLayer_ActivityType_value)
	proto.RegisterEnum("presentextraconf.PresentFloatLayer_SubActivityType", PresentFloatLayer_SubActivityType_name, PresentFloatLayer_SubActivityType_value)
	proto.RegisterEnum("presentextraconf.AppUrl_UrlType", AppUrl_UrlType_name, AppUrl_UrlType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PresentExtraConfClient is the client API for PresentExtraConf service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PresentExtraConfClient interface {
	GetPopUpPresentList(ctx context.Context, in *GetPopUpPresentListReq, opts ...grpc.CallOption) (*GetPopUpPresentListResp, error)
	// 按id/名称搜索礼物
	SearchPresent(ctx context.Context, in *SearchPresentReq, opts ...grpc.CallOption) (*SearchPresentResp, error)
	// 获取已有礼物浮层的礼物列表
	GetPresentFloatLayer(ctx context.Context, in *GetPresentFloatLayerReq, opts ...grpc.CallOption) (*GetPresentFloatLayerResp, error)
	// 添加礼物浮层
	AddPresentFloatLayer(ctx context.Context, in *AddPresentFloatLayerReq, opts ...grpc.CallOption) (*AddPresentFloatLayerResp, error)
	// 修改礼物浮层
	UpdatePresentFloatLayer(ctx context.Context, in *UpdatePresentFloatLayerReq, opts ...grpc.CallOption) (*UpdatePresentFloatLayerResp, error)
	// 删除礼物浮层
	DelPresentFloatLayer(ctx context.Context, in *DelPresentFloatLayerReq, opts ...grpc.CallOption) (*DelPresentFloatLayerResp, error)
	// 获取闪光效果库
	GetFlashEffectConfig(ctx context.Context, in *GetFlashEffectConfigReq, opts ...grpc.CallOption) (*GetFlashEffectConfigResp, error)
	// 添加闪光效果
	AddFlashEffectConfig(ctx context.Context, in *AddFlashEffectConfigReq, opts ...grpc.CallOption) (*AddFlashEffectConfigResp, error)
	// 更新闪光效果
	UpdateFlashEffectConfig(ctx context.Context, in *UpdateFlashEffectConfigReq, opts ...grpc.CallOption) (*UpdateFlashEffectConfigResp, error)
	// 删除闪光效果
	DelFlashEffectConfig(ctx context.Context, in *DelFlashEffectConfigReq, opts ...grpc.CallOption) (*DelFlashEffectConfigResp, error)
	// 获取已绑定闪光效果的礼物列表
	GetPresentFlashEffect(ctx context.Context, in *GetPresentFlashEffectReq, opts ...grpc.CallOption) (*GetPresentFlashEffectResp, error)
	// 绑定闪光效果(更新、删除都用此接口)
	BoundPresentFlashEffect(ctx context.Context, in *BoundPresentFlashEffectReq, opts ...grpc.CallOption) (*BoundPresentFlashEffectResp, error)
	// 专属定制礼物相关
	GetUserCustomizedInfo(ctx context.Context, in *GetUserCustomizedInfoReq, opts ...grpc.CallOption) (*GetUserCustomizedInfoResp, error)
	GetUserCustomizedInfoByGiftId(ctx context.Context, in *GetUserCustomizedInfoByGiftIdReq, opts ...grpc.CallOption) (*GetUserCustomizedInfoByGiftIdResp, error)
	ReportCustomOptionChoose(ctx context.Context, in *ReportCustomOptionChooseReq, opts ...grpc.CallOption) (*ReportCustomOptionChooseResp, error)
	ReportCustomPresentSend(ctx context.Context, in *ReportCustomPresentSendReq, opts ...grpc.CallOption) (*ReportCustomPresentSendResp, error)
	CheckCustomizedGift(ctx context.Context, in *CheckCustomizedGiftReq, opts ...grpc.CallOption) (*CheckCustomizedGiftResp, error)
	NotifyPrivilegeLevelChange(ctx context.Context, in *NotifyPrivilegeLevelChangeReq, opts ...grpc.CallOption) (*NotifyPrivilegeLevelChangeResp, error)
	GetCustomPresentEffectTime(ctx context.Context, in *GetCustomPresentEffectTimeReq, opts ...grpc.CallOption) (*GetCustomPresentEffectTimeResp, error)
	// 运营后台用
	AddCustomizedPresentConfig(ctx context.Context, in *AddCustomizedPresentConfigReq, opts ...grpc.CallOption) (*AddCustomizedPresentConfigResp, error)
	DelCustomizedPresentConfig(ctx context.Context, in *DelCustomizedPresentConfigReq, opts ...grpc.CallOption) (*DelCustomizedPresentConfigResp, error)
	UpdateCustomizedPresentConfig(ctx context.Context, in *UpdateCustomizedPresentConfigReq, opts ...grpc.CallOption) (*UpdateCustomizedPresentConfigResp, error)
	GetCustomizedPresentConfig(ctx context.Context, in *GetAllCustomizedPresentConfigReq, opts ...grpc.CallOption) (*GetAllCustomizedPresentConfigResp, error)
	// 获取限时礼物信息
	GetPresentEffectTime(ctx context.Context, in *GetPresentEffectTimeReq, opts ...grpc.CallOption) (*GetPresentEffectTimeResp, error)
	// 获取限时礼物详情
	GetPresentEffectTimeDetail(ctx context.Context, in *GetPresentEffectTimeDetailReq, opts ...grpc.CallOption) (*GetPresentEffectTimeDetailResp, error)
	// 通知限时礼物送出
	NotifyPresentSend(ctx context.Context, in *NotifyPresentSendReq, opts ...grpc.CallOption) (*NotifyPresentSendResp, error)
	// 运营后台用
	AddEffectDelayLevel(ctx context.Context, in *AddEffectDelayLevelReq, opts ...grpc.CallOption) (*AddEffectDelayLevelResp, error)
	GetEffectDelayLevel(ctx context.Context, in *GetEffectDelayLevelReq, opts ...grpc.CallOption) (*GetEffectDelayLevelResp, error)
	UpdateEffectDelayLevel(ctx context.Context, in *UpdateEffectDelayLevelReq, opts ...grpc.CallOption) (*UpdateEffectDelayLevelResp, error)
	DelEffectDelayLevel(ctx context.Context, in *DelEffectDelayLevelReq, opts ...grpc.CallOption) (*DelEffectDelayLevelResp, error)
}

type presentExtraConfClient struct {
	cc *grpc.ClientConn
}

func NewPresentExtraConfClient(cc *grpc.ClientConn) PresentExtraConfClient {
	return &presentExtraConfClient{cc}
}

func (c *presentExtraConfClient) GetPopUpPresentList(ctx context.Context, in *GetPopUpPresentListReq, opts ...grpc.CallOption) (*GetPopUpPresentListResp, error) {
	out := new(GetPopUpPresentListResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetPopUpPresentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) SearchPresent(ctx context.Context, in *SearchPresentReq, opts ...grpc.CallOption) (*SearchPresentResp, error) {
	out := new(SearchPresentResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/SearchPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetPresentFloatLayer(ctx context.Context, in *GetPresentFloatLayerReq, opts ...grpc.CallOption) (*GetPresentFloatLayerResp, error) {
	out := new(GetPresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetPresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) AddPresentFloatLayer(ctx context.Context, in *AddPresentFloatLayerReq, opts ...grpc.CallOption) (*AddPresentFloatLayerResp, error) {
	out := new(AddPresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/AddPresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) UpdatePresentFloatLayer(ctx context.Context, in *UpdatePresentFloatLayerReq, opts ...grpc.CallOption) (*UpdatePresentFloatLayerResp, error) {
	out := new(UpdatePresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/UpdatePresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) DelPresentFloatLayer(ctx context.Context, in *DelPresentFloatLayerReq, opts ...grpc.CallOption) (*DelPresentFloatLayerResp, error) {
	out := new(DelPresentFloatLayerResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/DelPresentFloatLayer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetFlashEffectConfig(ctx context.Context, in *GetFlashEffectConfigReq, opts ...grpc.CallOption) (*GetFlashEffectConfigResp, error) {
	out := new(GetFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) AddFlashEffectConfig(ctx context.Context, in *AddFlashEffectConfigReq, opts ...grpc.CallOption) (*AddFlashEffectConfigResp, error) {
	out := new(AddFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/AddFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) UpdateFlashEffectConfig(ctx context.Context, in *UpdateFlashEffectConfigReq, opts ...grpc.CallOption) (*UpdateFlashEffectConfigResp, error) {
	out := new(UpdateFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/UpdateFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) DelFlashEffectConfig(ctx context.Context, in *DelFlashEffectConfigReq, opts ...grpc.CallOption) (*DelFlashEffectConfigResp, error) {
	out := new(DelFlashEffectConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/DelFlashEffectConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetPresentFlashEffect(ctx context.Context, in *GetPresentFlashEffectReq, opts ...grpc.CallOption) (*GetPresentFlashEffectResp, error) {
	out := new(GetPresentFlashEffectResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetPresentFlashEffect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) BoundPresentFlashEffect(ctx context.Context, in *BoundPresentFlashEffectReq, opts ...grpc.CallOption) (*BoundPresentFlashEffectResp, error) {
	out := new(BoundPresentFlashEffectResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/BoundPresentFlashEffect", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetUserCustomizedInfo(ctx context.Context, in *GetUserCustomizedInfoReq, opts ...grpc.CallOption) (*GetUserCustomizedInfoResp, error) {
	out := new(GetUserCustomizedInfoResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetUserCustomizedInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetUserCustomizedInfoByGiftId(ctx context.Context, in *GetUserCustomizedInfoByGiftIdReq, opts ...grpc.CallOption) (*GetUserCustomizedInfoByGiftIdResp, error) {
	out := new(GetUserCustomizedInfoByGiftIdResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetUserCustomizedInfoByGiftId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) ReportCustomOptionChoose(ctx context.Context, in *ReportCustomOptionChooseReq, opts ...grpc.CallOption) (*ReportCustomOptionChooseResp, error) {
	out := new(ReportCustomOptionChooseResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/ReportCustomOptionChoose", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) ReportCustomPresentSend(ctx context.Context, in *ReportCustomPresentSendReq, opts ...grpc.CallOption) (*ReportCustomPresentSendResp, error) {
	out := new(ReportCustomPresentSendResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/ReportCustomPresentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) CheckCustomizedGift(ctx context.Context, in *CheckCustomizedGiftReq, opts ...grpc.CallOption) (*CheckCustomizedGiftResp, error) {
	out := new(CheckCustomizedGiftResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/CheckCustomizedGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) NotifyPrivilegeLevelChange(ctx context.Context, in *NotifyPrivilegeLevelChangeReq, opts ...grpc.CallOption) (*NotifyPrivilegeLevelChangeResp, error) {
	out := new(NotifyPrivilegeLevelChangeResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/NotifyPrivilegeLevelChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetCustomPresentEffectTime(ctx context.Context, in *GetCustomPresentEffectTimeReq, opts ...grpc.CallOption) (*GetCustomPresentEffectTimeResp, error) {
	out := new(GetCustomPresentEffectTimeResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetCustomPresentEffectTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) AddCustomizedPresentConfig(ctx context.Context, in *AddCustomizedPresentConfigReq, opts ...grpc.CallOption) (*AddCustomizedPresentConfigResp, error) {
	out := new(AddCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/AddCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) DelCustomizedPresentConfig(ctx context.Context, in *DelCustomizedPresentConfigReq, opts ...grpc.CallOption) (*DelCustomizedPresentConfigResp, error) {
	out := new(DelCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/DelCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) UpdateCustomizedPresentConfig(ctx context.Context, in *UpdateCustomizedPresentConfigReq, opts ...grpc.CallOption) (*UpdateCustomizedPresentConfigResp, error) {
	out := new(UpdateCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/UpdateCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetCustomizedPresentConfig(ctx context.Context, in *GetAllCustomizedPresentConfigReq, opts ...grpc.CallOption) (*GetAllCustomizedPresentConfigResp, error) {
	out := new(GetAllCustomizedPresentConfigResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetCustomizedPresentConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetPresentEffectTime(ctx context.Context, in *GetPresentEffectTimeReq, opts ...grpc.CallOption) (*GetPresentEffectTimeResp, error) {
	out := new(GetPresentEffectTimeResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetPresentEffectTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetPresentEffectTimeDetail(ctx context.Context, in *GetPresentEffectTimeDetailReq, opts ...grpc.CallOption) (*GetPresentEffectTimeDetailResp, error) {
	out := new(GetPresentEffectTimeDetailResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetPresentEffectTimeDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) NotifyPresentSend(ctx context.Context, in *NotifyPresentSendReq, opts ...grpc.CallOption) (*NotifyPresentSendResp, error) {
	out := new(NotifyPresentSendResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/NotifyPresentSend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) AddEffectDelayLevel(ctx context.Context, in *AddEffectDelayLevelReq, opts ...grpc.CallOption) (*AddEffectDelayLevelResp, error) {
	out := new(AddEffectDelayLevelResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/AddEffectDelayLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) GetEffectDelayLevel(ctx context.Context, in *GetEffectDelayLevelReq, opts ...grpc.CallOption) (*GetEffectDelayLevelResp, error) {
	out := new(GetEffectDelayLevelResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/GetEffectDelayLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) UpdateEffectDelayLevel(ctx context.Context, in *UpdateEffectDelayLevelReq, opts ...grpc.CallOption) (*UpdateEffectDelayLevelResp, error) {
	out := new(UpdateEffectDelayLevelResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/UpdateEffectDelayLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *presentExtraConfClient) DelEffectDelayLevel(ctx context.Context, in *DelEffectDelayLevelReq, opts ...grpc.CallOption) (*DelEffectDelayLevelResp, error) {
	out := new(DelEffectDelayLevelResp)
	err := c.cc.Invoke(ctx, "/presentextraconf.PresentExtraConf/DelEffectDelayLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PresentExtraConfServer is the server API for PresentExtraConf service.
type PresentExtraConfServer interface {
	GetPopUpPresentList(context.Context, *GetPopUpPresentListReq) (*GetPopUpPresentListResp, error)
	// 按id/名称搜索礼物
	SearchPresent(context.Context, *SearchPresentReq) (*SearchPresentResp, error)
	// 获取已有礼物浮层的礼物列表
	GetPresentFloatLayer(context.Context, *GetPresentFloatLayerReq) (*GetPresentFloatLayerResp, error)
	// 添加礼物浮层
	AddPresentFloatLayer(context.Context, *AddPresentFloatLayerReq) (*AddPresentFloatLayerResp, error)
	// 修改礼物浮层
	UpdatePresentFloatLayer(context.Context, *UpdatePresentFloatLayerReq) (*UpdatePresentFloatLayerResp, error)
	// 删除礼物浮层
	DelPresentFloatLayer(context.Context, *DelPresentFloatLayerReq) (*DelPresentFloatLayerResp, error)
	// 获取闪光效果库
	GetFlashEffectConfig(context.Context, *GetFlashEffectConfigReq) (*GetFlashEffectConfigResp, error)
	// 添加闪光效果
	AddFlashEffectConfig(context.Context, *AddFlashEffectConfigReq) (*AddFlashEffectConfigResp, error)
	// 更新闪光效果
	UpdateFlashEffectConfig(context.Context, *UpdateFlashEffectConfigReq) (*UpdateFlashEffectConfigResp, error)
	// 删除闪光效果
	DelFlashEffectConfig(context.Context, *DelFlashEffectConfigReq) (*DelFlashEffectConfigResp, error)
	// 获取已绑定闪光效果的礼物列表
	GetPresentFlashEffect(context.Context, *GetPresentFlashEffectReq) (*GetPresentFlashEffectResp, error)
	// 绑定闪光效果(更新、删除都用此接口)
	BoundPresentFlashEffect(context.Context, *BoundPresentFlashEffectReq) (*BoundPresentFlashEffectResp, error)
	// 专属定制礼物相关
	GetUserCustomizedInfo(context.Context, *GetUserCustomizedInfoReq) (*GetUserCustomizedInfoResp, error)
	GetUserCustomizedInfoByGiftId(context.Context, *GetUserCustomizedInfoByGiftIdReq) (*GetUserCustomizedInfoByGiftIdResp, error)
	ReportCustomOptionChoose(context.Context, *ReportCustomOptionChooseReq) (*ReportCustomOptionChooseResp, error)
	ReportCustomPresentSend(context.Context, *ReportCustomPresentSendReq) (*ReportCustomPresentSendResp, error)
	CheckCustomizedGift(context.Context, *CheckCustomizedGiftReq) (*CheckCustomizedGiftResp, error)
	NotifyPrivilegeLevelChange(context.Context, *NotifyPrivilegeLevelChangeReq) (*NotifyPrivilegeLevelChangeResp, error)
	GetCustomPresentEffectTime(context.Context, *GetCustomPresentEffectTimeReq) (*GetCustomPresentEffectTimeResp, error)
	// 运营后台用
	AddCustomizedPresentConfig(context.Context, *AddCustomizedPresentConfigReq) (*AddCustomizedPresentConfigResp, error)
	DelCustomizedPresentConfig(context.Context, *DelCustomizedPresentConfigReq) (*DelCustomizedPresentConfigResp, error)
	UpdateCustomizedPresentConfig(context.Context, *UpdateCustomizedPresentConfigReq) (*UpdateCustomizedPresentConfigResp, error)
	GetCustomizedPresentConfig(context.Context, *GetAllCustomizedPresentConfigReq) (*GetAllCustomizedPresentConfigResp, error)
	// 获取限时礼物信息
	GetPresentEffectTime(context.Context, *GetPresentEffectTimeReq) (*GetPresentEffectTimeResp, error)
	// 获取限时礼物详情
	GetPresentEffectTimeDetail(context.Context, *GetPresentEffectTimeDetailReq) (*GetPresentEffectTimeDetailResp, error)
	// 通知限时礼物送出
	NotifyPresentSend(context.Context, *NotifyPresentSendReq) (*NotifyPresentSendResp, error)
	// 运营后台用
	AddEffectDelayLevel(context.Context, *AddEffectDelayLevelReq) (*AddEffectDelayLevelResp, error)
	GetEffectDelayLevel(context.Context, *GetEffectDelayLevelReq) (*GetEffectDelayLevelResp, error)
	UpdateEffectDelayLevel(context.Context, *UpdateEffectDelayLevelReq) (*UpdateEffectDelayLevelResp, error)
	DelEffectDelayLevel(context.Context, *DelEffectDelayLevelReq) (*DelEffectDelayLevelResp, error)
}

func RegisterPresentExtraConfServer(s *grpc.Server, srv PresentExtraConfServer) {
	s.RegisterService(&_PresentExtraConf_serviceDesc, srv)
}

func _PresentExtraConf_GetPopUpPresentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPopUpPresentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetPopUpPresentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetPopUpPresentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetPopUpPresentList(ctx, req.(*GetPopUpPresentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_SearchPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPresentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).SearchPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/SearchPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).SearchPresent(ctx, req.(*SearchPresentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetPresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetPresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetPresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetPresentFloatLayer(ctx, req.(*GetPresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_AddPresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).AddPresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/AddPresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).AddPresentFloatLayer(ctx, req.(*AddPresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_UpdatePresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).UpdatePresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/UpdatePresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).UpdatePresentFloatLayer(ctx, req.(*UpdatePresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_DelPresentFloatLayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelPresentFloatLayerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).DelPresentFloatLayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/DelPresentFloatLayer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).DelPresentFloatLayer(ctx, req.(*DelPresentFloatLayerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetFlashEffectConfig(ctx, req.(*GetFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_AddFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).AddFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/AddFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).AddFlashEffectConfig(ctx, req.(*AddFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_UpdateFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).UpdateFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/UpdateFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).UpdateFlashEffectConfig(ctx, req.(*UpdateFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_DelFlashEffectConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFlashEffectConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).DelFlashEffectConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/DelFlashEffectConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).DelFlashEffectConfig(ctx, req.(*DelFlashEffectConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetPresentFlashEffect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentFlashEffectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetPresentFlashEffect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetPresentFlashEffect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetPresentFlashEffect(ctx, req.(*GetPresentFlashEffectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_BoundPresentFlashEffect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BoundPresentFlashEffectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).BoundPresentFlashEffect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/BoundPresentFlashEffect",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).BoundPresentFlashEffect(ctx, req.(*BoundPresentFlashEffectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetUserCustomizedInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCustomizedInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetUserCustomizedInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetUserCustomizedInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetUserCustomizedInfo(ctx, req.(*GetUserCustomizedInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetUserCustomizedInfoByGiftId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCustomizedInfoByGiftIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetUserCustomizedInfoByGiftId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetUserCustomizedInfoByGiftId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetUserCustomizedInfoByGiftId(ctx, req.(*GetUserCustomizedInfoByGiftIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_ReportCustomOptionChoose_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportCustomOptionChooseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).ReportCustomOptionChoose(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/ReportCustomOptionChoose",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).ReportCustomOptionChoose(ctx, req.(*ReportCustomOptionChooseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_ReportCustomPresentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportCustomPresentSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).ReportCustomPresentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/ReportCustomPresentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).ReportCustomPresentSend(ctx, req.(*ReportCustomPresentSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_CheckCustomizedGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckCustomizedGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).CheckCustomizedGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/CheckCustomizedGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).CheckCustomizedGift(ctx, req.(*CheckCustomizedGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_NotifyPrivilegeLevelChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPrivilegeLevelChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).NotifyPrivilegeLevelChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/NotifyPrivilegeLevelChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).NotifyPrivilegeLevelChange(ctx, req.(*NotifyPrivilegeLevelChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetCustomPresentEffectTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomPresentEffectTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetCustomPresentEffectTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetCustomPresentEffectTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetCustomPresentEffectTime(ctx, req.(*GetCustomPresentEffectTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_AddCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).AddCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/AddCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).AddCustomizedPresentConfig(ctx, req.(*AddCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_DelCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).DelCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/DelCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).DelCustomizedPresentConfig(ctx, req.(*DelCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_UpdateCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).UpdateCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/UpdateCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).UpdateCustomizedPresentConfig(ctx, req.(*UpdateCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetCustomizedPresentConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCustomizedPresentConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetCustomizedPresentConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetCustomizedPresentConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetCustomizedPresentConfig(ctx, req.(*GetAllCustomizedPresentConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetPresentEffectTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentEffectTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetPresentEffectTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetPresentEffectTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetPresentEffectTime(ctx, req.(*GetPresentEffectTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetPresentEffectTimeDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPresentEffectTimeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetPresentEffectTimeDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetPresentEffectTimeDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetPresentEffectTimeDetail(ctx, req.(*GetPresentEffectTimeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_NotifyPresentSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyPresentSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).NotifyPresentSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/NotifyPresentSend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).NotifyPresentSend(ctx, req.(*NotifyPresentSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_AddEffectDelayLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddEffectDelayLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).AddEffectDelayLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/AddEffectDelayLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).AddEffectDelayLevel(ctx, req.(*AddEffectDelayLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_GetEffectDelayLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEffectDelayLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).GetEffectDelayLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/GetEffectDelayLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).GetEffectDelayLevel(ctx, req.(*GetEffectDelayLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_UpdateEffectDelayLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEffectDelayLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).UpdateEffectDelayLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/UpdateEffectDelayLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).UpdateEffectDelayLevel(ctx, req.(*UpdateEffectDelayLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PresentExtraConf_DelEffectDelayLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelEffectDelayLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PresentExtraConfServer).DelEffectDelayLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/presentextraconf.PresentExtraConf/DelEffectDelayLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PresentExtraConfServer).DelEffectDelayLevel(ctx, req.(*DelEffectDelayLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PresentExtraConf_serviceDesc = grpc.ServiceDesc{
	ServiceName: "presentextraconf.PresentExtraConf",
	HandlerType: (*PresentExtraConfServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPopUpPresentList",
			Handler:    _PresentExtraConf_GetPopUpPresentList_Handler,
		},
		{
			MethodName: "SearchPresent",
			Handler:    _PresentExtraConf_SearchPresent_Handler,
		},
		{
			MethodName: "GetPresentFloatLayer",
			Handler:    _PresentExtraConf_GetPresentFloatLayer_Handler,
		},
		{
			MethodName: "AddPresentFloatLayer",
			Handler:    _PresentExtraConf_AddPresentFloatLayer_Handler,
		},
		{
			MethodName: "UpdatePresentFloatLayer",
			Handler:    _PresentExtraConf_UpdatePresentFloatLayer_Handler,
		},
		{
			MethodName: "DelPresentFloatLayer",
			Handler:    _PresentExtraConf_DelPresentFloatLayer_Handler,
		},
		{
			MethodName: "GetFlashEffectConfig",
			Handler:    _PresentExtraConf_GetFlashEffectConfig_Handler,
		},
		{
			MethodName: "AddFlashEffectConfig",
			Handler:    _PresentExtraConf_AddFlashEffectConfig_Handler,
		},
		{
			MethodName: "UpdateFlashEffectConfig",
			Handler:    _PresentExtraConf_UpdateFlashEffectConfig_Handler,
		},
		{
			MethodName: "DelFlashEffectConfig",
			Handler:    _PresentExtraConf_DelFlashEffectConfig_Handler,
		},
		{
			MethodName: "GetPresentFlashEffect",
			Handler:    _PresentExtraConf_GetPresentFlashEffect_Handler,
		},
		{
			MethodName: "BoundPresentFlashEffect",
			Handler:    _PresentExtraConf_BoundPresentFlashEffect_Handler,
		},
		{
			MethodName: "GetUserCustomizedInfo",
			Handler:    _PresentExtraConf_GetUserCustomizedInfo_Handler,
		},
		{
			MethodName: "GetUserCustomizedInfoByGiftId",
			Handler:    _PresentExtraConf_GetUserCustomizedInfoByGiftId_Handler,
		},
		{
			MethodName: "ReportCustomOptionChoose",
			Handler:    _PresentExtraConf_ReportCustomOptionChoose_Handler,
		},
		{
			MethodName: "ReportCustomPresentSend",
			Handler:    _PresentExtraConf_ReportCustomPresentSend_Handler,
		},
		{
			MethodName: "CheckCustomizedGift",
			Handler:    _PresentExtraConf_CheckCustomizedGift_Handler,
		},
		{
			MethodName: "NotifyPrivilegeLevelChange",
			Handler:    _PresentExtraConf_NotifyPrivilegeLevelChange_Handler,
		},
		{
			MethodName: "GetCustomPresentEffectTime",
			Handler:    _PresentExtraConf_GetCustomPresentEffectTime_Handler,
		},
		{
			MethodName: "AddCustomizedPresentConfig",
			Handler:    _PresentExtraConf_AddCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "DelCustomizedPresentConfig",
			Handler:    _PresentExtraConf_DelCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "UpdateCustomizedPresentConfig",
			Handler:    _PresentExtraConf_UpdateCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "GetCustomizedPresentConfig",
			Handler:    _PresentExtraConf_GetCustomizedPresentConfig_Handler,
		},
		{
			MethodName: "GetPresentEffectTime",
			Handler:    _PresentExtraConf_GetPresentEffectTime_Handler,
		},
		{
			MethodName: "GetPresentEffectTimeDetail",
			Handler:    _PresentExtraConf_GetPresentEffectTimeDetail_Handler,
		},
		{
			MethodName: "NotifyPresentSend",
			Handler:    _PresentExtraConf_NotifyPresentSend_Handler,
		},
		{
			MethodName: "AddEffectDelayLevel",
			Handler:    _PresentExtraConf_AddEffectDelayLevel_Handler,
		},
		{
			MethodName: "GetEffectDelayLevel",
			Handler:    _PresentExtraConf_GetEffectDelayLevel_Handler,
		},
		{
			MethodName: "UpdateEffectDelayLevel",
			Handler:    _PresentExtraConf_UpdateEffectDelayLevel_Handler,
		},
		{
			MethodName: "DelEffectDelayLevel",
			Handler:    _PresentExtraConf_DelEffectDelayLevel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/presentextraconf/present-extra-conf.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/presentextraconf/present-extra-conf.proto", fileDescriptor_present_extra_conf_ce33501c7383c61c)
}

var fileDescriptor_present_extra_conf_ce33501c7383c61c = []byte{
	// 4095 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3b, 0x5d, 0x6f, 0x23, 0x59,
	0x56, 0x29, 0x3b, 0x71, 0xec, 0x63, 0x3b, 0x71, 0x6e, 0xbe, 0x1c, 0xf7, 0xa4, 0x3b, 0x7d, 0x7b,
	0xb7, 0x27, 0xd3, 0x33, 0xdd, 0xbd, 0x93, 0x99, 0x41, 0xb3, 0xb3, 0x7c, 0x39, 0x89, 0xd3, 0xf1,
	0x4c, 0xe2, 0x44, 0x4e, 0xdc, 0xa3, 0x1e, 0xd0, 0x16, 0xd5, 0xae, 0x9b, 0xa4, 0x88, 0x5d, 0x55,
	0xe3, 0x2a, 0x77, 0x3a, 0xb3, 0x12, 0x12, 0x12, 0x08, 0x81, 0x84, 0x10, 0x12, 0x8f, 0x48, 0x08,
	0x9e, 0xf6, 0x01, 0x21, 0xf6, 0x01, 0x56, 0x42, 0xf0, 0x07, 0x56, 0x48, 0xfc, 0x02, 0xc4, 0x13,
	0x82, 0x5f, 0xc0, 0x33, 0xba, 0x1f, 0x55, 0xae, 0x8f, 0x7b, 0x6d, 0x87, 0xd9, 0x15, 0xda, 0x37,
	0xd7, 0xb9, 0xe7, 0x9e, 0x73, 0xee, 0xf9, 0xbe, 0x1f, 0x86, 0x4f, 0x7d, 0xff, 0xf9, 0xd7, 0x43,
	0xab, 0x7b, 0xed, 0x59, 0xbd, 0x37, 0x64, 0xf0, 0xdc, 0x1d, 0x10, 0x8f, 0xd8, 0x3e, 0x79, 0xeb,
	0x0f, 0x8c, 0xae, 0x63, 0x5f, 0x04, 0x80, 0xa7, 0x0c, 0xf2, 0x94, 0x82, 0x9e, 0xb9, 0x03, 0xc7,
	0x77, 0x50, 0x25, 0x89, 0x8a, 0xab, 0xb0, 0xf6, 0x82, 0xf8, 0xa7, 0x8e, 0xdb, 0x71, 0x4f, 0xf9,
	0xd8, 0x91, 0xe5, 0xf9, 0x6d, 0xf2, 0x35, 0xde, 0x81, 0x75, 0xe9, 0x88, 0xe7, 0xa2, 0x75, 0x98,
	0xb7, 0x4c, 0xbd, 0x67, 0x79, 0x7e, 0x55, 0xdb, 0xca, 0x6e, 0x97, 0xdb, 0x39, 0xcb, 0xa4, 0x83,
	0xd8, 0x86, 0xca, 0x19, 0x31, 0x06, 0xdd, 0x2b, 0x31, 0xa3, 0x4d, 0xbe, 0x46, 0xab, 0x90, 0xbb,
	0x26, 0xb7, 0xba, 0x65, 0x56, 0xb5, 0x2d, 0x6d, 0xbb, 0xdc, 0x9e, 0xbb, 0x26, 0xb7, 0x4d, 0x13,
	0x6d, 0x40, 0x9e, 0x82, 0x6d, 0xa3, 0x4f, 0xaa, 0x99, 0x2d, 0x6d, 0xbb, 0xd0, 0x9e, 0xbf, 0x26,
	0xb7, 0x2d, 0xa3, 0x4f, 0x10, 0x82, 0x59, 0xd7, 0xb8, 0x24, 0xd5, 0x2c, 0xc3, 0x67, 0xbf, 0xd1,
	0x0a, 0xcc, 0x75, 0x9d, 0xa1, 0xed, 0x57, 0x67, 0x39, 0x11, 0xf6, 0x81, 0x6f, 0x60, 0x29, 0xc1,
	0xcf, 0x73, 0xd1, 0x11, 0x2c, 0x8a, 0x65, 0xea, 0x74, 0x89, 0xd6, 0xa5, 0xc7, 0xa4, 0x2c, 0xee,
	0x3c, 0x7a, 0x96, 0x5c, 0xfe, 0x33, 0x31, 0x6f, 0xd7, 0xf0, 0xc8, 0x1e, 0xc3, 0x6d, 0x2f, 0x08,
	0x1c, 0xfe, 0xe9, 0x51, 0xc6, 0xbe, 0xe3, 0x1b, 0x3d, 0x26, 0x64, 0xb9, 0xcd, 0x3f, 0xf0, 0x9f,
	0x6a, 0x5c, 0x3b, 0x1c, 0xf7, 0xa0, 0xe7, 0x18, 0xfe, 0x91, 0x71, 0x4b, 0x06, 0x74, 0xc1, 0x81,
	0xf8, 0x9a, 0x4c, 0xfc, 0x4c, 0x44, 0xfc, 0x88, 0x6a, 0xb2, 0x2a, 0xd5, 0xcc, 0xc6, 0x55, 0xb3,
	0x06, 0x39, 0xcf, 0x37, 0xfc, 0xa1, 0x57, 0x9d, 0x63, 0x33, 0xc4, 0x17, 0xfe, 0x2b, 0x0d, 0xaa,
	0x72, 0x79, 0x3c, 0x17, 0xed, 0x41, 0xb1, 0x47, 0x3f, 0x74, 0xcb, 0xbe, 0x70, 0x02, 0x65, 0x60,
	0xa5, 0x32, 0xd8, 0xec, 0xa6, 0x7d, 0xe1, 0xb4, 0x81, 0x4d, 0xa3, 0x3f, 0x15, 0x7a, 0x40, 0xdb,
	0x50, 0xe9, 0x19, 0x9e, 0xaf, 0x0f, 0x5d, 0xd3, 0xf0, 0x89, 0xee, 0x5b, 0xfd, 0xc0, 0x6c, 0x0b,
	0x14, 0xde, 0x61, 0xe0, 0x73, 0xab, 0x4f, 0xf0, 0x39, 0xac, 0xd7, 0x4d, 0x53, 0xaa, 0xb0, 0xef,
	0xc3, 0x1c, 0x63, 0xc4, 0x34, 0x36, 0xce, 0x4c, 0x91, 0x69, 0x7c, 0x06, 0xae, 0x41, 0x55, 0x4e,
	0xd5, 0x73, 0xf1, 0x97, 0x50, 0xe3, 0xfc, 0x7f, 0xde, 0x4c, 0x37, 0xe1, 0x9e, 0x92, 0xb0, 0xe7,
	0xd2, 0xc0, 0xd9, 0x27, 0x3d, 0x29, 0xd3, 0x75, 0x98, 0xbf, 0xb4, 0x2e, 0xfc, 0x51, 0x30, 0xe4,
	0xe8, 0x67, 0xd3, 0xa4, 0xeb, 0x90, 0xcf, 0xf1, 0x5c, 0xfc, 0x37, 0x1a, 0x54, 0x92, 0xa6, 0x41,
	0xbb, 0x00, 0x23, 0x9b, 0xde, 0x65, 0x0d, 0x85, 0xd0, 0xa6, 0xe8, 0x73, 0x58, 0x88, 0x07, 0x0a,
	0xb3, 0xed, 0x94, 0x71, 0x52, 0x8e, 0xc5, 0x09, 0xfe, 0xbb, 0x12, 0x2c, 0xa5, 0x98, 0x29, 0xd7,
	0x8b, 0x1e, 0xc3, 0xe2, 0x05, 0x45, 0xd3, 0xad, 0xbe, 0x71, 0x49, 0xf4, 0xe1, 0xa0, 0x27, 0x92,
	0x40, 0x99, 0x81, 0x9b, 0x14, 0xda, 0x19, 0xf4, 0x68, 0x28, 0xfc, 0xee, 0xb0, 0xef, 0x32, 0x84,
	0x2c, 0x0f, 0x05, 0xfa, 0x4d, 0x87, 0x1e, 0xc3, 0xa2, 0xe5, 0xe9, 0x46, 0xd7, 0xb7, 0xde, 0x58,
	0xfe, 0x2d, 0xc3, 0xa0, 0xc1, 0x92, 0x6f, 0x97, 0x2d, 0xaf, 0x2e, 0xa0, 0x14, 0xef, 0x21, 0x94,
	0xc8, 0xc5, 0x05, 0xe9, 0xfa, 0xfa, 0x6b, 0x72, 0x69, 0xd9, 0x22, 0x70, 0x8a, 0x1c, 0xb6, 0x4b,
	0x41, 0x68, 0x13, 0x40, 0xa0, 0x10, 0xdb, 0xac, 0xe6, 0x18, 0x42, 0x81, 0x43, 0x1a, 0xb6, 0x89,
	0x6a, 0x90, 0x77, 0x5c, 0x32, 0x30, 0x7c, 0x67, 0x50, 0x9d, 0x67, 0x42, 0x84, 0xdf, 0xe8, 0x11,
	0x94, 0xc5, 0x54, 0x11, 0x97, 0x79, 0x36, 0x5b, 0xb0, 0x3c, 0x63, 0x30, 0xf4, 0x00, 0x8a, 0xd1,
	0x00, 0x29, 0x30, 0x14, 0x18, 0x86, 0xc1, 0x81, 0x7e, 0x0b, 0x96, 0xbc, 0x2b, 0xe7, 0x46, 0xef,
	0x5e, 0x19, 0xb6, 0x4d, 0x7a, 0xba, 0x7f, 0xeb, 0x92, 0x2a, 0x6c, 0x65, 0xb7, 0x17, 0x76, 0x9e,
	0x4f, 0x61, 0xd4, 0x67, 0x7b, 0x7c, 0xde, 0xf9, 0xad, 0x4b, 0xda, 0x8b, 0x94, 0x52, 0x04, 0x80,
	0x4e, 0xa0, 0xcc, 0x88, 0x1b, 0xae, 0xcb, 0x09, 0x17, 0x19, 0xe1, 0xf7, 0xa7, 0x21, 0x5c, 0x77,
	0x5d, 0x46, 0xb4, 0x48, 0x29, 0x88, 0x0f, 0xf4, 0x19, 0x94, 0x28, 0xad, 0xd0, 0x30, 0x25, 0x96,
	0x50, 0xaa, 0x69, 0x7a, 0x75, 0x97, 0x5a, 0xaa, 0x0d, 0x86, 0xeb, 0x7e, 0x2e, 0xac, 0xd6, 0x81,
	0x72, 0x68, 0x32, 0x26, 0x4c, 0x79, 0x4b, 0xdb, 0x5e, 0xd8, 0xf9, 0xde, 0x54, 0xc2, 0x88, 0x89,
	0x4c, 0xa2, 0x92, 0x11, 0xf9, 0x42, 0x3a, 0x2c, 0x79, 0xc3, 0xd7, 0x7a, 0x9c, 0xf4, 0x02, 0x23,
	0xfd, 0xd1, 0x34, 0xa4, 0xcf, 0x86, 0xaf, 0x63, 0xd4, 0x17, 0xbd, 0x38, 0x80, 0xda, 0x39, 0x24,
	0xce, 0x12, 0xf3, 0x22, 0x73, 0x84, 0x50, 0x0a, 0x9a, 0x9d, 0xf1, 0xcf, 0x32, 0x50, 0x8c, 0x6a,
	0xbe, 0x0a, 0x2b, 0xcd, 0xd6, 0xcb, 0xfa, 0x51, 0x73, 0x5f, 0xdf, 0x3b, 0xac, 0xb7, 0x5a, 0x8d,
	0x23, 0xfd, 0xfc, 0xd5, 0x69, 0xa3, 0x32, 0x83, 0x16, 0x00, 0x5e, 0x74, 0x9a, 0x47, 0xfb, 0xfc,
	0x5b, 0x43, 0x65, 0x28, 0x1c, 0xb4, 0x1b, 0x0d, 0xfe, 0x99, 0x41, 0xab, 0xb0, 0xd4, 0x39, 0x6b,
	0xb4, 0xe3, 0xb3, 0xb2, 0xe8, 0x21, 0x6c, 0xf2, 0x59, 0xa7, 0x9d, 0xdd, 0xa3, 0xe6, 0x9e, 0x7e,
	0xd0, 0x69, 0xc5, 0x51, 0x66, 0xd1, 0x3b, 0x50, 0x3d, 0x6f, 0x37, 0x5f, 0x36, 0xeb, 0xfa, 0x8b,
	0xfa, 0x71, 0x23, 0x3e, 0x3a, 0x47, 0x05, 0x3a, 0x6f, 0x1c, 0x9f, 0xea, 0x5f, 0x1c, 0xc6, 0x47,
	0x72, 0xe8, 0x1e, 0xac, 0xb7, 0xeb, 0xfb, 0xcd, 0x13, 0xfd, 0xa8, 0xf9, 0x32, 0x31, 0x6d, 0x9e,
	0x0e, 0x72, 0xbe, 0x87, 0x27, 0x49, 0x9a, 0x79, 0x74, 0x1f, 0x6a, 0x27, 0x07, 0x07, 0xcd, 0xbd,
	0x66, 0xfd, 0x48, 0x32, 0xb9, 0x80, 0x6a, 0xb0, 0xb6, 0x77, 0x7a, 0xa4, 0x9f, 0x75, 0x4e, 0x93,
	0x0b, 0x02, 0x36, 0x76, 0x72, 0x7c, 0xdc, 0x69, 0x35, 0xcf, 0x5f, 0xc5, 0xc7, 0x8a, 0xf8, 0x67,
	0x1a, 0xcc, 0x07, 0x1e, 0xb7, 0x02, 0x95, 0xfa, 0xe9, 0x29, 0x1b, 0xd1, 0x85, 0x46, 0x2b, 0x33,
	0x68, 0x11, 0x8a, 0x21, 0xf4, 0x74, 0xaf, 0xa2, 0xa1, 0x75, 0x58, 0x0e, 0x01, 0xe7, 0xe7, 0x7a,
	0xbd, 0xb5, 0xdf, 0x3e, 0x69, 0xee, 0x57, 0x32, 0x68, 0x19, 0x16, 0xa3, 0x03, 0xcd, 0x93, 0xb3,
	0x4a, 0x96, 0x2a, 0x23, 0x04, 0x1e, 0x76, 0xea, 0xad, 0x57, 0x27, 0x1d, 0x36, 0xc2, 0x94, 0x98,
	0x1a, 0x09, 0x88, 0xcd, 0xa1, 0x35, 0x40, 0xe1, 0xe8, 0x71, 0xbd, 0xf9, 0x45, 0x83, 0xcd, 0xca,
	0xd1, 0xc5, 0x24, 0xe0, 0xc1, 0x9c, 0x79, 0xfc, 0xdb, 0x50, 0x8a, 0xb9, 0xd3, 0x06, 0xac, 0xd6,
	0xf7, 0xce, 0x9b, 0x2f, 0xe9, 0xba, 0x13, 0xab, 0xa2, 0xe4, 0x63, 0x43, 0xd4, 0x13, 0x2a, 0x5a,
	0x7a, 0x4a, 0xbb, 0xf1, 0xb2, 0xd1, 0xea, 0x34, 0x2a, 0x19, 0xfc, 0x13, 0x0d, 0x16, 0x13, 0x1e,
	0x8c, 0x36, 0x61, 0xe3, 0xac, 0xb3, 0xab, 0xab, 0xb8, 0x7c, 0x17, 0x1e, 0xa6, 0x87, 0x05, 0x45,
	0x7d, 0xb7, 0xd1, 0x6a, 0x1c, 0x34, 0xcf, 0x39, 0xd3, 0x34, 0xda, 0x69, 0xfd, 0x55, 0x25, 0x43,
	0x95, 0x94, 0x1e, 0xaa, 0xb7, 0xf6, 0x0e, 0x4f, 0xda, 0x95, 0x2c, 0x7a, 0x00, 0xf7, 0xd2, 0xa3,
	0xbb, 0xcd, 0x17, 0x3a, 0x65, 0x71, 0x5e, 0x99, 0xc5, 0xff, 0xa3, 0x41, 0x8e, 0xe7, 0x07, 0xf4,
	0x03, 0xc8, 0x0f, 0x07, 0x22, 0xe9, 0x69, 0x2c, 0x66, 0xb7, 0x54, 0xb9, 0xe4, 0x59, 0x67, 0xc0,
	0xb3, 0xdc, 0xfc, 0x90, 0xff, 0x40, 0x15, 0xc8, 0x8e, 0xaa, 0x07, 0xfd, 0x89, 0xff, 0x52, 0x83,
	0x79, 0x81, 0x46, 0x1d, 0xa7, 0xd3, 0x3e, 0x92, 0x38, 0x4e, 0x08, 0x3d, 0xa7, 0xcb, 0xac, 0xc2,
	0x4a, 0x08, 0x88, 0xba, 0x02, 0x5b, 0x65, 0x6a, 0x24, 0x30, 0x6b, 0x96, 0xda, 0x2a, 0x1c, 0x1d,
	0xb9, 0xc2, 0x2c, 0x75, 0x85, 0x04, 0x3c, 0x74, 0x1f, 0xfc, 0x63, 0x2d, 0xac, 0x94, 0xa3, 0x72,
	0xaa, 0xae, 0x94, 0xf7, 0xa0, 0xc0, 0x06, 0x22, 0x8d, 0x72, 0x9e, 0x02, 0x58, 0x3b, 0xf8, 0x00,
	0x8a, 0xee, 0xc0, 0xea, 0x12, 0xfd, 0x8d, 0xd1, 0x1b, 0x06, 0x9d, 0x17, 0x30, 0xd0, 0x4b, 0x0a,
	0xa1, 0x95, 0x8d, 0x23, 0x30, 0xe5, 0xf2, 0xde, 0xb9, 0xc0, 0x20, 0xc2, 0x49, 0x80, 0x73, 0xa5,
	0xf5, 0x96, 0x55, 0xc6, 0x42, 0x9b, 0xb1, 0x63, 0x05, 0x18, 0x1f, 0x02, 0x0a, 0x53, 0xa5, 0xe1,
	0x5d, 0x35, 0x58, 0x4d, 0x53, 0x8b, 0xba, 0x01, 0xf9, 0x0b, 0x8a, 0x47, 0x47, 0x78, 0x97, 0x38,
	0xcf, 0xbe, 0x9b, 0x26, 0xfe, 0x4f, 0x0d, 0x96, 0x22, 0x34, 0xc4, 0xa2, 0xa3, 0x13, 0xb4, 0xd8,
	0x04, 0x2a, 0x19, 0x1f, 0x8a, 0xac, 0xbb, 0xc0, 0x20, 0x6c, 0xe1, 0xf7, 0x80, 0x7f, 0x44, 0x1a,
	0x03, 0x4e, 0x8a, 0xfa, 0x53, 0x38, 0xd8, 0x37, 0x3f, 0x11, 0x0d, 0x34, 0x1f, 0x3c, 0x36, 0x3f,
	0x89, 0x15, 0xf3, 0xb9, 0x44, 0x31, 0x7f, 0x00, 0xc5, 0xee, 0x80, 0x84, 0x75, 0x9a, 0x37, 0x02,
	0xc0, 0x41, 0xac, 0x4e, 0x27, 0x0a, 0xf9, 0x7c, 0xb2, 0x90, 0xe3, 0x1f, 0xb2, 0x6d, 0x41, 0x6a,
	0xa5, 0x77, 0xdb, 0x16, 0x44, 0xfb, 0xff, 0x6c, 0xac, 0xff, 0xc7, 0xdf, 0xb0, 0x36, 0x5f, 0x42,
	0xdf, 0x73, 0xd1, 0x21, 0x94, 0xf9, 0xb2, 0x79, 0xef, 0x31, 0x66, 0xd7, 0x93, 0x9e, 0x5f, 0xba,
	0x18, 0x81, 0x54, 0x7b, 0x1e, 0x83, 0x75, 0xf0, 0xd2, 0xb5, 0x1d, 0x40, 0x29, 0xca, 0x5a, 0xdd,
	0x8f, 0xa6, 0x67, 0x17, 0x23, 0x9c, 0x45, 0x3b, 0x2f, 0x5d, 0x1e, 0x36, 0x83, 0x76, 0xfe, 0x17,
	0x2a, 0x41, 0xd8, 0xdb, 0xcb, 0x85, 0xf8, 0x98, 0xf5, 0xf6, 0x52, 0x09, 0xd4, 0xce, 0x2c, 0xba,
	0x7b, 0x39, 0xc5, 0xff, 0xca, 0x44, 0xba, 0x7b, 0x8a, 0x2e, 0xba, 0x7b, 0x41, 0x6b, 0x6c, 0x77,
	0x9f, 0xa6, 0xc8, 0x1d, 0xff, 0xe7, 0xdd, 0xdd, 0xa7, 0x7a, 0xe8, 0xec, 0xa4, 0x1e, 0x7a, 0x36,
	0xd9, 0x43, 0x27, 0x42, 0x6b, 0x2e, 0x15, 0x5a, 0xd1, 0xb8, 0xcc, 0x4d, 0x6a, 0xb2, 0xe7, 0x27,
	0x37, 0xd9, 0xf9, 0x54, 0x6c, 0xfe, 0x7d, 0x62, 0x8f, 0x1c, 0xea, 0xee, 0x6e, 0xd1, 0xf9, 0x18,
	0x16, 0x69, 0x74, 0x0a, 0x81, 0x22, 0x41, 0x5a, 0xbe, 0x26, 0xb7, 0x9c, 0x20, 0x4b, 0x51, 0xf7,
	0xa1, 0x48, 0xf1, 0x82, 0x54, 0x29, 0x34, 0x72, 0x4d, 0x6e, 0x5f, 0xf0, 0x6c, 0x89, 0xa1, 0x1c,
	0x8e, 0x33, 0x2a, 0x3c, 0x1b, 0x15, 0x05, 0x06, 0x0b, 0xf7, 0x1f, 0x6b, 0xb0, 0xa1, 0x10, 0xd9,
	0x73, 0xd1, 0x17, 0xa3, 0x83, 0x8e, 0x78, 0xc8, 0x8f, 0xdb, 0xdb, 0x0b, 0xf7, 0x08, 0xcf, 0x39,
	0xc6, 0xc6, 0xfc, 0x1d, 0xf6, 0xf7, 0xff, 0xa6, 0x41, 0x6d, 0xd7, 0x19, 0xda, 0xa6, 0x5c, 0xbf,
	0xb1, 0xa2, 0x91, 0x9d, 0xaa, 0x68, 0xfc, 0x3f, 0x7b, 0x1d, 0x4d, 0x05, 0xca, 0x05, 0x79, 0x2e,
	0xfe, 0x80, 0x79, 0x53, 0xc7, 0x23, 0x83, 0xbd, 0xa1, 0xe7, 0x3b, 0x7d, 0xeb, 0x1b, 0x62, 0x32,
	0xcd, 0x92, 0xaf, 0x59, 0x53, 0x12, 0xa6, 0x01, 0xfa, 0x13, 0xff, 0x37, 0xb7, 0xa4, 0x0c, 0xdd,
	0x73, 0xd1, 0xe7, 0x50, 0x0a, 0x2c, 0x29, 0x22, 0x9e, 0x9a, 0xf1, 0xdd, 0xb4, 0x19, 0x47, 0x73,
	0x85, 0x54, 0x8c, 0x44, 0xd1, 0x1d, 0x7d, 0xa0, 0xef, 0xc0, 0x42, 0xcc, 0x64, 0x9e, 0x50, 0x6b,
	0x29, 0x62, 0x30, 0x0f, 0xbd, 0x82, 0xd5, 0xb8, 0xef, 0xd0, 0xed, 0x21, 0xd5, 0x61, 0x96, 0xb1,
	0xfe, 0xae, 0xd2, 0x83, 0xb8, 0x06, 0xea, 0x0c, 0xb9, 0xbd, 0xec, 0xa6, 0x81, 0xd8, 0x85, 0x65,
	0x09, 0xae, 0xba, 0x6d, 0x48, 0x9a, 0x39, 0x33, 0xc9, 0xcc, 0xd9, 0x84, 0x99, 0xf1, 0x1f, 0x6a,
	0x80, 0xd2, 0x9a, 0x45, 0x0b, 0x90, 0x09, 0x99, 0x65, 0x2c, 0x13, 0xfd, 0x0a, 0xe4, 0x1c, 0xd7,
	0xb7, 0x1c, 0xca, 0x82, 0x2e, 0xf2, 0xbe, 0x4a, 0xbf, 0x27, 0x0c, 0xab, 0x2d, 0xb0, 0xd1, 0xbb,
	0xb0, 0x68, 0x0c, 0xfd, 0x2b, 0x67, 0x40, 0x37, 0x7f, 0x3d, 0xf2, 0x86, 0xf4, 0x82, 0x18, 0x08,
	0xc1, 0x47, 0x14, 0x8a, 0xff, 0x3c, 0x03, 0xab, 0x52, 0x0b, 0xa5, 0x44, 0x79, 0x04, 0xe5, 0x2e,
	0x43, 0xd4, 0x23, 0x12, 0x15, 0xda, 0xa5, 0x6e, 0x84, 0x3f, 0x6f, 0xde, 0x84, 0x57, 0x84, 0xab,
	0x0e, 0x4c, 0xcd, 0x14, 0xda, 0xed, 0x7b, 0xe1, 0xc1, 0x47, 0xa1, 0x9d, 0xeb, 0xf6, 0x3d, 0xda,
	0xff, 0x3c, 0x82, 0xf2, 0x95, 0xe1, 0xe9, 0xa1, 0x70, 0xcc, 0xef, 0xf3, 0xed, 0xd2, 0x95, 0xe1,
	0xd5, 0x03, 0x18, 0x75, 0x13, 0x8a, 0x64, 0x93, 0x1b, 0x9d, 0x33, 0x65, 0xfe, 0xcf, 0xb1, 0x5a,
	0xe4, 0x86, 0x2f, 0x23, 0x65, 0x9b, 0xf9, 0x49, 0xb6, 0xc9, 0x27, 0x6d, 0xb3, 0x0f, 0x5b, 0x52,
	0xbf, 0xdf, 0x15, 0x79, 0x50, 0x1a, 0x2e, 0x42, 0x5f, 0x99, 0x40, 0x5f, 0x78, 0x08, 0x0f, 0x27,
	0x50, 0xf1, 0x5c, 0x74, 0x3a, 0xaa, 0x78, 0x26, 0xf1, 0x0d, 0xab, 0x27, 0x2a, 0xe7, 0x7b, 0x53,
	0xc4, 0xd1, 0x3e, 0x9b, 0x10, 0xd6, 0x3d, 0xfe, 0x89, 0x7f, 0x3a, 0x0b, 0xeb, 0x0a, 0xd4, 0x94,
	0x49, 0xf7, 0x64, 0x26, 0x9d, 0xec, 0x64, 0x71, 0x93, 0xff, 0x4e, 0x48, 0xa4, 0x4f, 0xfc, 0x2b,
	0x27, 0x08, 0xc7, 0x1f, 0x4c, 0xbd, 0x02, 0x01, 0x3f, 0x66, 0xb3, 0x1b, 0xb6, 0x3f, 0xb8, 0x0d,
	0x38, 0x70, 0x10, 0x35, 0xd7, 0xd0, 0x23, 0x03, 0xe1, 0xc7, 0x22, 0x63, 0x52, 0x08, 0x73, 0x61,
	0x3a, 0xcc, 0x46, 0x74, 0xca, 0x2a, 0xd8, 0x11, 0x30, 0xc8, 0x39, 0x79, 0xeb, 0x33, 0xbf, 0x75,
	0x7a, 0xce, 0xe0, 0x62, 0x28, 0x30, 0x78, 0xd2, 0x2c, 0x05, 0x40, 0x86, 0xf4, 0x15, 0xdd, 0x95,
	0x90, 0x37, 0x16, 0xb9, 0xd1, 0xfb, 0x86, 0x5b, 0x9d, 0x67, 0x4b, 0xf8, 0xfe, 0xf4, 0x4b, 0x38,
	0xe5, 0x93, 0x8f, 0x0d, 0x97, 0x2f, 0x00, 0xdc, 0x10, 0x50, 0xfb, 0x0d, 0x58, 0x4a, 0xad, 0x90,
	0xfa, 0xcf, 0x35, 0xb9, 0x65, 0xb6, 0x28, 0xb4, 0xe9, 0x4f, 0x5a, 0xcd, 0xf8, 0x96, 0x48, 0x54,
	0x33, 0xf6, 0xf1, 0x59, 0xe6, 0x53, 0xad, 0x46, 0x60, 0x31, 0x41, 0x3f, 0x3a, 0xbd, 0xcc, 0xa7,
	0xff, 0x6a, 0x74, 0x7a, 0x71, 0xe7, 0xb1, 0x4a, 0x76, 0x21, 0xb7, 0x20, 0x18, 0x61, 0x83, 0x7f,
	0x04, 0x2b, 0x32, 0x14, 0x1a, 0x50, 0x81, 0x6e, 0xc2, 0xfd, 0x6e, 0xb9, 0x1d, 0xe8, 0x8b, 0x6d,
	0xca, 0x1e, 0x8c, 0xd4, 0x37, 0xda, 0xd9, 0x06, 0x3a, 0xa0, 0xf1, 0x1d, 0x41, 0xa0, 0x3b, 0x9c,
	0x6c, 0x0c, 0xe1, 0xd8, 0xfc, 0x04, 0xff, 0xad, 0x06, 0xa5, 0xa8, 0x93, 0xd1, 0x1d, 0x91, 0x70,
	0xab, 0xd0, 0x65, 0xf3, 0x1c, 0xd0, 0xe4, 0x45, 0x92, 0x0f, 0x46, 0xf6, 0x5a, 0xc0, 0x41, 0xac,
	0x93, 0xf9, 0x35, 0x28, 0x72, 0x97, 0xe6, 0xc5, 0x89, 0xbb, 0xe4, 0x3b, 0x69, 0x9d, 0x70, 0x66,
	0xfc, 0xe6, 0xc0, 0x09, 0x7f, 0x47, 0xe8, 0x33, 0x8f, 0x99, 0x8d, 0xd2, 0xa7, 0xfe, 0x82, 0xff,
	0x5a, 0x03, 0x18, 0xcd, 0xa5, 0xc2, 0x06, 0xec, 0x42, 0x61, 0x05, 0x39, 0x26, 0xac, 0x18, 0x8c,
	0x0a, 0xcb, 0x41, 0x4c, 0xd8, 0x87, 0x50, 0x12, 0x08, 0xd1, 0x4c, 0x2d, 0x26, 0x71, 0x1f, 0x5f,
	0x85, 0x9c, 0xc5, 0x32, 0x9f, 0x38, 0x30, 0x9e, 0xb3, 0x68, 0xc6, 0xa3, 0x7c, 0x83, 0x03, 0x65,
	0x22, 0x52, 0x66, 0x5e, 0x1c, 0x25, 0x13, 0xdc, 0x02, 0x14, 0xd5, 0xa8, 0xe8, 0x8b, 0xc7, 0xea,
	0x35, 0xb6, 0x8e, 0x4c, 0x7c, 0x1d, 0xf8, 0xf7, 0xe0, 0x5e, 0x9b, 0xb8, 0xce, 0xc0, 0x8f, 0x51,
	0xbd, 0x72, 0x1c, 0x8f, 0x4c, 0x95, 0x11, 0xa9, 0x51, 0x04, 0x6b, 0xd7, 0xb0, 0x06, 0x6a, 0xa3,
	0x08, 0x2f, 0x34, 0xac, 0x41, 0xa0, 0x73, 0xfa, 0x1b, 0xdf, 0x87, 0x77, 0xd4, 0xfc, 0x3d, 0x17,
	0x1f, 0x00, 0x8c, 0x66, 0x7e, 0x8b, 0x75, 0x7e, 0x06, 0x6b, 0x7b, 0x57, 0xa4, 0x7b, 0x3d, 0x8a,
	0x75, 0x9a, 0xb0, 0xa7, 0x4b, 0xfa, 0x5f, 0xc1, 0xba, 0x74, 0xae, 0xb8, 0x81, 0xf4, 0x74, 0xe3,
	0x75, 0x8f, 0x47, 0x50, 0xbe, 0x9d, 0xb3, 0xbc, 0xfa, 0xeb, 0x1e, 0xa1, 0xdd, 0xb9, 0x3b, 0xb0,
	0xfa, 0xc6, 0x60, 0xd4, 0x79, 0x73, 0x82, 0x65, 0x01, 0xe6, 0xf5, 0x02, 0xbf, 0x86, 0xcd, 0xba,
	0x69, 0xa6, 0x32, 0xd0, 0x68, 0x3b, 0x57, 0x87, 0x9c, 0xd8, 0x36, 0x4d, 0x5f, 0x44, 0xc4, 0x6c,
	0x31, 0x11, 0x6f, 0xc1, 0xfd, 0x71, 0x3c, 0x3c, 0x17, 0xff, 0x47, 0x56, 0x52, 0x5f, 0x26, 0x9d,
	0x08, 0xb1, 0xba, 0x6f, 0x46, 0x72, 0x43, 0xae, 0xdb, 0x37, 0xc5, 0xb9, 0x47, 0xdf, 0x78, 0x1b,
	0xf3, 0xfb, 0x7c, 0xdf, 0x78, 0xcb, 0x9d, 0xfe, 0x30, 0xdc, 0x43, 0x89, 0x65, 0xcd, 0xaa, 0x4e,
	0x07, 0x18, 0x7e, 0xfc, 0x74, 0x80, 0x44, 0x4f, 0x6d, 0x46, 0x85, 0x4e, 0x50, 0x9a, 0x1b, 0x5f,
	0xe8, 0x02, 0x22, 0xdd, 0xc8, 0x17, 0xfa, 0x4d, 0x28, 0xf1, 0x3a, 0x23, 0x68, 0xe4, 0x18, 0x8d,
	0x4d, 0x85, 0x34, 0xc1, 0x4e, 0xbd, 0x37, 0xfa, 0x88, 0x88, 0x21, 0x4a, 0xe5, 0xfc, 0x78, 0x31,
	0x78, 0xc1, 0x48, 0x57, 0xc3, 0x48, 0xb9, 0xcb, 0x27, 0xcb, 0xdd, 0xc4, 0x8b, 0x9b, 0xc4, 0x06,
	0x03, 0x92, 0x1b, 0x0c, 0xfc, 0x93, 0x30, 0x15, 0x4f, 0x93, 0x32, 0x26, 0xa6, 0xe2, 0x44, 0x2e,
	0xcd, 0x26, 0x73, 0x29, 0xda, 0x85, 0x52, 0x34, 0x43, 0x09, 0x2b, 0xdf, 0x57, 0x25, 0xeb, 0xc0,
	0x36, 0xd1, 0x2f, 0x7c, 0x14, 0xa7, 0xf1, 0xed, 0x12, 0x32, 0xb6, 0xa1, 0x18, 0xb1, 0x21, 0xad,
	0xcc, 0xdc, 0x41, 0xc5, 0x6b, 0x00, 0xf6, 0x81, 0x9a, 0xf2, 0xe6, 0xe9, 0x3b, 0xe3, 0x9b, 0xa7,
	0xb8, 0x67, 0x71, 0x18, 0xfe, 0xd7, 0x50, 0xe3, 0xc2, 0xc6, 0xbf, 0x0e, 0xf3, 0x9c, 0x68, 0xb0,
	0x3d, 0x9e, 0x8e, 0x6a, 0x30, 0x29, 0x1a, 0x88, 0x99, 0xe4, 0xc6, 0x25, 0x56, 0xcb, 0xb3, 0x13,
	0x6b, 0xf9, 0xec, 0xa4, 0x5a, 0x3e, 0x97, 0xaa, 0xe5, 0xd7, 0xb0, 0x94, 0x0a, 0x48, 0x85, 0x12,
	0xbf, 0xfd, 0x46, 0x8a, 0xc0, 0x16, 0xdf, 0x21, 0xfe, 0x62, 0x13, 0xe3, 0x23, 0x78, 0x38, 0x81,
	0x8d, 0xe7, 0x62, 0xcc, 0x36, 0x0e, 0xf5, 0x5e, 0x4f, 0x2d, 0x0b, 0xbe, 0x60, 0xdb, 0x82, 0x71,
	0x38, 0x9e, 0x1b, 0x13, 0x38, 0xfb, 0x7f, 0x13, 0xf8, 0x39, 0x6c, 0xee, 0x93, 0x31, 0x82, 0x24,
	0x37, 0x03, 0x34, 0xf5, 0x8f, 0x9b, 0xe0, 0xb9, 0xf8, 0x2f, 0x34, 0xd8, 0x6c, 0x39, 0xbe, 0x75,
	0x71, 0x7b, 0x3a, 0xb0, 0xde, 0x58, 0x3d, 0x72, 0x49, 0x78, 0x98, 0x5c, 0x19, 0xf6, 0xa5, 0xa2,
	0x07, 0xd8, 0x04, 0x60, 0x1d, 0x26, 0x77, 0x37, 0x6e, 0xde, 0x02, 0x83, 0x30, 0x67, 0x7b, 0x08,
	0xa5, 0xd7, 0xe4, 0xc2, 0x19, 0x44, 0xaf, 0x03, 0x66, 0xdb, 0x45, 0x0e, 0xe3, 0xf7, 0x01, 0x0f,
	0xa0, 0x68, 0x5c, 0xf8, 0x64, 0x20, 0x30, 0x66, 0x19, 0x06, 0x30, 0x10, 0x43, 0xa0, 0x82, 0x8f,
	0x93, 0xca, 0x73, 0x71, 0x0b, 0x36, 0x5f, 0x10, 0x3f, 0xd6, 0xdc, 0x72, 0xdf, 0xa4, 0xf9, 0x6e,
	0xdc, 0x23, 0x87, 0x91, 0xd7, 0x66, 0x22, 0x5e, 0x8b, 0x0f, 0xe1, 0xfe, 0x38, 0x7a, 0x9e, 0x4b,
	0xe7, 0x71, 0x87, 0x16, 0xde, 0xce, 0x3e, 0xa8, 0x7a, 0xa8, 0x0f, 0x73, 0x5a, 0xf4, 0x27, 0x7e,
	0x3f, 0xfa, 0x26, 0x27, 0x2e, 0x53, 0xfa, 0x40, 0xe6, 0x9b, 0xe8, 0x61, 0x60, 0x82, 0xe1, 0x0f,
	0x61, 0x23, 0x71, 0x38, 0x42, 0xb3, 0x7b, 0xec, 0xf9, 0xcc, 0xa3, 0x09, 0x07, 0x24, 0x8c, 0xd6,
	0x9a, 0x9b, 0x04, 0xb1, 0xb7, 0x34, 0xf8, 0xa7, 0xa3, 0x2b, 0xa0, 0xd1, 0x90, 0x5a, 0x6f, 0xf1,
	0xa0, 0xcd, 0x24, 0x0f, 0xb9, 0x0e, 0x41, 0x84, 0x78, 0xd0, 0x9e, 0x6b, 0xf2, 0xb3, 0xa3, 0x53,
	0x99, 0x30, 0x6d, 0x41, 0x9a, 0x75, 0xde, 0x5b, 0x50, 0xe2, 0x8d, 0x71, 0x64, 0x77, 0x98, 0x6f,
	0x03, 0x6b, 0x8f, 0xf9, 0x09, 0xc7, 0x1f, 0xcf, 0xc2, 0xaa, 0x94, 0x0e, 0x2d, 0x12, 0xb6, 0x73,
	0xa3, 0xf3, 0x03, 0x53, 0x51, 0x24, 0x6c, 0xe7, 0x66, 0x8f, 0x9d, 0x99, 0x7e, 0x08, 0xab, 0x36,
	0x79, 0xeb, 0x73, 0xb2, 0xba, 0x47, 0x6c, 0x53, 0x8f, 0x9e, 0xac, 0x22, 0x3a, 0xc8, 0x18, 0x9c,
	0x11, 0xdb, 0xe4, 0x53, 0x9e, 0xc3, 0x4a, 0x64, 0x8a, 0x69, 0xdc, 0x8a, 0x19, 0x3c, 0x67, 0x2d,
	0x85, 0x33, 0xf6, 0x8d, 0xdb, 0x70, 0x42, 0xd8, 0xfd, 0x44, 0x59, 0xf0, 0x2d, 0xee, 0x52, 0xd0,
	0x08, 0x8d, 0x38, 0xf0, 0xd5, 0x8e, 0x3a, 0xa6, 0xb9, 0x60, 0xb5, 0xc7, 0x41, 0xcf, 0xf4, 0x29,
	0x6c, 0xd8, 0x8e, 0xde, 0xb3, 0xfa, 0x96, 0xaf, 0x93, 0xb7, 0xae, 0x35, 0x20, 0x11, 0x41, 0xf8,
	0xed, 0xd0, 0xaa, 0xed, 0x1c, 0xd1, 0xf1, 0x06, 0x1b, 0x0e, 0x85, 0xd9, 0x02, 0x76, 0xdc, 0xc6,
	0xe5, 0xf0, 0x83, 0x03, 0x6b, 0xa0, 0x30, 0x2a, 0xc0, 0xb9, 0x87, 0x9e, 0xc2, 0xf2, 0x48, 0xdc,
	0x11, 0x55, 0x7e, 0x7e, 0x52, 0x09, 0xa4, 0x0d, 0x09, 0x3e, 0x85, 0xe5, 0x91, 0x0f, 0xe8, 0x8e,
	0xad, 0x7b, 0x57, 0xa4, 0x77, 0x21, 0x3a, 0x92, 0x4a, 0xe8, 0x0c, 0x27, 0xf6, 0x19, 0x85, 0x53,
	0x74, 0x6a, 0x8d, 0x24, 0x75, 0xde, 0x9f, 0x54, 0x6c, 0xe7, 0x26, 0x4e, 0xfd, 0x23, 0x58, 0xb3,
	0x1d, 0xdf, 0xea, 0x12, 0x3d, 0xb1, 0xde, 0x6a, 0x91, 0x29, 0x65, 0x99, 0x8f, 0xb6, 0xa2, 0x6b,
	0xc5, 0x9f, 0xb3, 0x44, 0x90, 0xf2, 0x06, 0x71, 0x92, 0x22, 0x4d, 0x60, 0xaa, 0x52, 0x8a, 0xff,
	0x5d, 0x63, 0x59, 0x40, 0x49, 0x8c, 0xb7, 0xfc, 0xca, 0x1b, 0xd2, 0x91, 0xe7, 0x65, 0x12, 0x9e,
	0xf7, 0x45, 0xd0, 0xe0, 0x45, 0xb6, 0xae, 0x1f, 0x4c, 0x11, 0x1b, 0x4c, 0x3f, 0x2c, 0x40, 0x78,
	0x3b, 0xc8, 0x7c, 0x7c, 0xac, 0x3f, 0xcc, 0x8e, 0xf1, 0x07, 0xfc, 0x07, 0x1a, 0xd4, 0xd4, 0x3c,
	0x14, 0xf5, 0x7c, 0x1b, 0x2a, 0x8a, 0x80, 0x59, 0xe8, 0xc5, 0x5d, 0xf9, 0x31, 0x2c, 0xca, 0xe3,
	0xa4, 0xdc, 0x8b, 0xda, 0x19, 0xff, 0x93, 0x06, 0x6b, 0x75, 0xd3, 0xe4, 0x22, 0xec, 0x93, 0x9e,
	0xc1, 0x0f, 0x2e, 0xc7, 0x66, 0xed, 0x06, 0x80, 0x49, 0x31, 0xb9, 0x06, 0x79, 0x5f, 0x26, 0x39,
	0x10, 0x49, 0xd2, 0xe4, 0xba, 0x63, 0x33, 0xd9, 0x12, 0xbf, 0xf5, 0x61, 0x3e, 0xfe, 0x67, 0x0d,
	0x56, 0x64, 0x5c, 0x14, 0xda, 0xdb, 0x04, 0x48, 0xe9, 0xad, 0xe0, 0x85, 0x2a, 0xbb, 0x07, 0x85,
	0xa4, 0xb2, 0xf2, 0x66, 0x10, 0x0f, 0xdb, 0x50, 0x51, 0xd8, 0x77, 0x81, 0xc4, 0x03, 0x7d, 0x1b,
	0x2a, 0x22, 0x72, 0x46, 0x98, 0xfc, 0x9a, 0x61, 0x81, 0xc3, 0x43, 0xdd, 0x6f, 0xb0, 0xeb, 0xd3,
	0xb4, 0xea, 0x3d, 0x17, 0x7f, 0xc8, 0x1e, 0xe1, 0x2a, 0xac, 0x62, 0xf9, 0x24, 0xb2, 0x57, 0xc8,
	0xd1, 0xcf, 0xa6, 0x89, 0x0d, 0x56, 0xeb, 0x64, 0xd4, 0xd0, 0x01, 0x94, 0xb8, 0xc1, 0x62, 0x5d,
	0xcf, 0x23, 0x95, 0xc9, 0x62, 0x1b, 0x2c, 0x36, 0x51, 0x6c, 0x03, 0xfe, 0x41, 0x83, 0xa5, 0x14,
	0xca, 0x2f, 0x81, 0x9f, 0xfc, 0x8b, 0x06, 0x1b, 0xbc, 0xbd, 0xfc, 0xe5, 0xf4, 0xf3, 0x77, 0x82,
	0x8b, 0x6e, 0xa9, 0xaf, 0x7c, 0x09, 0x2b, 0x41, 0x83, 0xc6, 0x24, 0xa3, 0x49, 0xe0, 0x6e, 0xc9,
	0x76, 0x74, 0xaf, 0x99, 0x8d, 0xbe, 0xa5, 0x5e, 0x87, 0x55, 0x09, 0x61, 0xee, 0x9d, 0xfb, 0xc1,
	0xfe, 0x63, 0x3a, 0x5d, 0x52, 0x5f, 0x97, 0x4e, 0xf1, 0x5c, 0xfc, 0x8f, 0x1a, 0xd4, 0xa2, 0x27,
	0x4f, 0x13, 0x97, 0xb1, 0x09, 0xe0, 0x1b, 0x83, 0x4b, 0xe2, 0xeb, 0xc3, 0x70, 0x25, 0x05, 0x0e,
	0xe9, 0xf0, 0xe1, 0xe0, 0xd5, 0xe4, 0xe8, 0x92, 0x44, 0x40, 0x9a, 0x31, 0x25, 0xcc, 0xca, 0x95,
	0x30, 0x97, 0x78, 0x7a, 0xe1, 0x0c, 0x4c, 0x32, 0xa0, 0xf8, 0xfc, 0x68, 0x7b, 0x9e, 0x7d, 0x37,
	0x4d, 0xbc, 0x19, 0x3f, 0xb1, 0x4b, 0x68, 0xe9, 0xc9, 0x2d, 0x94, 0x1a, 0xd1, 0xeb, 0xe8, 0x6a,
	0x90, 0xac, 0xf8, 0xb7, 0xde, 0xb1, 0xaf, 0x6d, 0xe7, 0xc6, 0xae, 0xcc, 0xa4, 0x46, 0xbe, 0x34,
	0x2c, 0xdf, 0xb2, 0x2f, 0x2b, 0x1a, 0xaa, 0xc1, 0x5a, 0x6c, 0x84, 0x7f, 0x58, 0x6f, 0x48, 0x25,
	0x83, 0x36, 0x60, 0x35, 0x36, 0x76, 0x32, 0xf4, 0xa9, 0x8f, 0x98, 0x95, 0xec, 0x93, 0xf0, 0x4c,
	0xfc, 0x34, 0xb2, 0xf3, 0x5c, 0x81, 0x4a, 0x0c, 0xd8, 0xec, 0x5f, 0x56, 0x66, 0x52, 0xd0, 0x63,
	0xf7, 0xe3, 0x8a, 0xf6, 0xe4, 0x43, 0x28, 0xbc, 0x0c, 0x77, 0x11, 0x4b, 0x50, 0x0e, 0x3f, 0x5a,
	0x8e, 0x4d, 0x2a, 0x33, 0x31, 0x50, 0xdb, 0xea, 0x5e, 0x55, 0xb4, 0x9d, 0x3f, 0x7b, 0x27, 0x7c,
	0xb6, 0xd0, 0xa0, 0xb1, 0x41, 0xb3, 0x03, 0xea, 0xc1, 0xb2, 0xe4, 0x2f, 0x03, 0x68, 0x3b, 0x1d,
	0x45, 0xf2, 0xff, 0x1c, 0xd4, 0xde, 0x9b, 0x12, 0xd3, 0x73, 0xf1, 0x0c, 0xfa, 0x0a, 0xca, 0xb1,
	0xc7, 0xff, 0x48, 0x72, 0xed, 0x9d, 0xfc, 0x37, 0x42, 0xed, 0xd1, 0x44, 0x1c, 0x46, 0xdb, 0x81,
	0x15, 0xd9, 0x73, 0x7a, 0xa4, 0x10, 0x50, 0xf2, 0xd6, 0xbb, 0xf6, 0x64, 0x5a, 0xd4, 0x80, 0xa1,
	0xec, 0x21, 0xbb, 0x8c, 0xa1, 0xe2, 0x19, 0xbd, 0x8c, 0xa1, 0xf2, 0x6d, 0xfc, 0x0c, 0x7a, 0x0b,
	0xeb, 0x8a, 0x47, 0xec, 0x48, 0xd2, 0x1f, 0xa9, 0x1f, 0xd2, 0xd7, 0x9e, 0xde, 0x01, 0x3b, 0x58,
	0xaa, 0xec, 0xad, 0xbb, 0x6c, 0xa9, 0x8a, 0x77, 0xf4, 0xb2, 0xa5, 0x2a, 0x9f, 0xcf, 0x07, 0xc6,
	0x4c, 0x3f, 0x3f, 0x93, 0x1b, 0x53, 0xf6, 0xb8, 0x47, 0x61, 0x4c, 0xf9, 0x8b, 0x9e, 0xc0, 0x98,
	0x53, 0x31, 0x54, 0xbc, 0xa8, 0x52, 0x18, 0x53, 0xc5, 0x30, 0x34, 0x66, 0x9a, 0xa7, 0xd2, 0x98,
	0x52, 0xb6, 0x4f, 0xef, 0x80, 0x1d, 0x31, 0xe6, 0x54, 0x4b, 0x55, 0x3c, 0x9c, 0x52, 0x18, 0x53,
	0xc5, 0x70, 0x00, 0xab, 0xd2, 0x17, 0x31, 0x68, 0x42, 0xbc, 0x45, 0x5f, 0xa3, 0xd4, 0xde, 0x9f,
	0x1a, 0x37, 0x50, 0xaf, 0xe2, 0x25, 0x88, 0x4c, 0xbd, 0xea, 0x57, 0x30, 0x32, 0xf5, 0x8e, 0x7b,
	0x62, 0x12, 0xac, 0x56, 0xf2, 0xb6, 0x41, 0xbe, 0x5a, 0xe9, 0x6b, 0x14, 0xc5, 0x6a, 0xe5, 0x4f,
	0x51, 0xf0, 0x0c, 0xfa, 0x13, 0x8d, 0x6d, 0xec, 0xd4, 0x97, 0xed, 0x68, 0x67, 0x4a, 0x82, 0x91,
	0x3b, 0xfe, 0xda, 0x47, 0x77, 0x9e, 0xc3, 0x84, 0xf9, 0x11, 0x54, 0x55, 0xf7, 0x54, 0x48, 0xa2,
	0xcd, 0x31, 0x77, 0x6a, 0xb5, 0x67, 0x77, 0x41, 0x0f, 0xec, 0xae, 0x28, 0xf9, 0x32, 0xbb, 0xab,
	0xbb, 0x9a, 0xda, 0xd3, 0x3b, 0x60, 0x33, 0xce, 0x3d, 0x58, 0x96, 0x5c, 0x7d, 0xc9, 0x2a, 0xa9,
	0xfc, 0x76, 0x4d, 0x56, 0x49, 0x15, 0x77, 0x69, 0x78, 0x06, 0xfd, 0xbe, 0x06, 0x35, 0xf5, 0xa9,
	0x1f, 0x92, 0xfc, 0x05, 0x65, 0xec, 0xc9, 0x65, 0xed, 0x7b, 0x77, 0x9b, 0x10, 0xca, 0xa0, 0x3e,
	0x07, 0x94, 0xc9, 0x30, 0xf6, 0x14, 0x52, 0x26, 0xc3, 0xf8, 0x63, 0x46, 0x21, 0x83, 0xfa, 0xc6,
	0x4e, 0x26, 0xc3, 0xd8, 0x3b, 0x44, 0x99, 0x0c, 0x13, 0x2e, 0x04, 0xb9, 0x0c, 0xea, 0xa3, 0x63,
	0x99, 0x0c, 0x63, 0x4f, 0xa6, 0x65, 0x32, 0x4c, 0x38, 0x99, 0xe6, 0x19, 0x60, 0xec, 0x01, 0xbd,
	0x2c, 0x03, 0x4c, 0xba, 0x38, 0x90, 0x65, 0x80, 0xc9, 0xb7, 0x00, 0x33, 0xe8, 0x8f, 0xa2, 0x8e,
	0x31, 0x95, 0x24, 0x93, 0xae, 0x0d, 0x14, 0xb9, 0x68, 0xfc, 0x35, 0x42, 0xb2, 0x29, 0x8c, 0xf8,
	0xe6, 0xd8, 0xa6, 0x30, 0xee, 0x95, 0x4f, 0xa6, 0x45, 0x8d, 0xc6, 0x84, 0xe2, 0x54, 0x4c, 0x11,
	0x13, 0xea, 0x03, 0x39, 0x45, 0x4c, 0x8c, 0x39, 0x74, 0xc3, 0x33, 0xe8, 0x02, 0x96, 0x52, 0xdb,
	0x42, 0xf4, 0x58, 0x1d, 0xe0, 0xb1, 0xbc, 0xf7, 0xee, 0x54, 0x78, 0x41, 0xc6, 0x93, 0x1c, 0x8f,
	0xc8, 0x32, 0x9e, 0xfc, 0x00, 0xab, 0xf6, 0xde, 0x94, 0x98, 0x01, 0x37, 0xc9, 0xf1, 0x89, 0x62,
	0xa7, 0x32, 0x25, 0x37, 0xc5, 0x79, 0x0c, 0x9e, 0x41, 0x43, 0x58, 0x93, 0xef, 0xe8, 0xd1, 0xfb,
	0xaa, 0x98, 0x90, 0xf1, 0xfc, 0x60, 0x7a, 0xe4, 0x60, 0x91, 0x92, 0x5d, 0xb8, 0x6c, 0x91, 0xf2,
	0xfd, 0x7d, 0xed, 0xbd, 0x29, 0x31, 0x29, 0xb7, 0xdd, 0x8f, 0xbf, 0xda, 0xb9, 0x74, 0x7a, 0x86,
	0x7d, 0xf9, 0xec, 0x93, 0x1d, 0xdf, 0x7f, 0xd6, 0x75, 0xfa, 0xcf, 0xd9, 0x9f, 0xce, 0xbb, 0x4e,
	0xef, 0xb9, 0x47, 0x06, 0x6f, 0xac, 0x2e, 0xf1, 0x52, 0x7f, 0x55, 0x7f, 0x9d, 0x63, 0x38, 0x1f,
	0xfd, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xb7, 0x9f, 0x57, 0xbb, 0xd4, 0x3e, 0x00, 0x00,
}
