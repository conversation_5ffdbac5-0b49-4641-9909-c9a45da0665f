// Code generated by protoc-gen-gogo.
// source: src/channelrecommendsvr/channelrecommend.proto
// DO NOT EDIT!

/*
	Package channelrecommend is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelrecommendsvr/channelrecommend.proto

	It has these top-level messages:
		UserEnterChannelReq
		UserEnterChannelResp
		AddChannelGiftReq
		AddChannelGiftResp
		GetRecommendChannelReq
		ChannelInfo
		GetRecommendChannelResp
		GetChannelTagColorReq
		GetChannelTagColorResp
		AddChannelReq
		AddChannelResp
		GetChannelReq
		GetChannelResp
		ChannelTypeInfo
		BatchGetChannelTypeReq
		BatchGetChannelTypeResp
		ChannelInfoEx
		SetChannelInfoExReq
		SetChannelInfoExResp
		GetChannelInfoExReq
		GetChannelInfoExResp
		RemoveChannelReq
		RemoveChannelResp
		RemoveChannelByTypeReq
		RemoveChannelByTypeResp
		AddUserLikeChannelReq
		AddUserLikeChannelResp
		GetChannelInfoTagReq
		ChannelInfoTag
		GetChannelInfoTagResp
		GetHotChannelReq
		ChannelHotInfo
		GetHotChannelResp
		SetHotChannelCfgReq
		GetHotChannelCfgReq
		GetHotChannelCfgResp
		SetHotChannelTypeReq
		HotChannelInfo
		BatchGetHotChannelTypeReq
		BatchGetHotChannelTypeResp
		GetHotChannelListByTypeReq
		GetHotChannelListByTypeResp
		BatchDelHotChannelReq
		ColorInfo
		AddColorReq
		AddColorResp
		GetAllColorReq
		GetAllColorResp
		CardInfo
		RootTag
		SubTag
		TagTypes
		GetAllTagTypeReq
		GetAllTagTypeResp
		GetChannelByTagReq
		GetChannelByTagResp
		GetChannelCardReq
		GetChannelCardResp
		RefreshChannelTimeReq
		RefreshChannelTimeResp
		GetChannelRefreshCDReq
		GetChannelRefreshCDResp
		SetChannelTagIdReq
		SetChannelTagIdResp
		GetChannelTagIdReq
		GetChannelTagIdResp
		ChannelTagAdv
		AddChannelTagAdvReq
		AddChannelTagAdvResp
		RemoveChannelTagAdvReq
		RemoveChannelTagAdvResp
		GetChannelTagAdvReq
		GetChannelTagAdvResp
		TagTopChannel
		AddTagTopChannelReq
		AddTagTopChannelResp
		GetTagTopChannelReq
		GetTagTopChannelResp
		RemoveTagTopChannelReq
		RemoveTagTopChannelResp
		CommonRecommendChannel
		AddCommonRecommendReq
		AddCommonRecommendResp
		RemoveCommonRecommendReq
		RemoveCommonRecommendResp
		GetCommonRecommendReq
		GetCommonRecommendResp
		RandomChannelInfo
		AddRandomRecommendChannelReq
		AddRandomRecommendChannelResp
		GetRandomRecommendChannelReq
		GetRandomRecommendChannelResp
		RemoveRandomRecommendChannelReq
		RemoveRandomRecommendChannelResp
		GetChannelTypeByTagReq
		GetChannelTypeByTagResp
		GetTagInfoReq
		GetTagInfoResp
		GetMajorLiveChannelReq
		GetMajorLiveChannelResp
		LiveChannelEx
		AddLiveRecommendChannelReq
		AddLiveRecommendChannelResp
		DelLiveRecommendChannelReq
		DelLiveRecommendChannelResp
		GetLiveRecommendChannelReq
		GetLiveRecommendChannelResp
		GetLiveChannelListReq
		GetLiveChannelListResp
*/
package channelrecommend

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type AddChannelType int32

const (
	AddChannelType_ACT_OFFICIAL_ACTIVITY  AddChannelType = 1
	AddChannelType_ACT_BLACKLIST          AddChannelType = 2
	AddChannelType_ACT_OFFICIAL_RECOMMEND AddChannelType = 3
	AddChannelType_ACT_FIX_RECOMMEND      AddChannelType = 4
	AddChannelType_ACT_RECOMMENDV2        AddChannelType = 5
	AddChannelType_ACT_LIVE_RECOMMEND     AddChannelType = 6
	AddChannelType_ACT_RECOMMEND_NEWUSER  AddChannelType = 7
	AddChannelType_ACT_BLOCKLIST          AddChannelType = 101
)

var AddChannelType_name = map[int32]string{
	1:   "ACT_OFFICIAL_ACTIVITY",
	2:   "ACT_BLACKLIST",
	3:   "ACT_OFFICIAL_RECOMMEND",
	4:   "ACT_FIX_RECOMMEND",
	5:   "ACT_RECOMMENDV2",
	6:   "ACT_LIVE_RECOMMEND",
	7:   "ACT_RECOMMEND_NEWUSER",
	101: "ACT_BLOCKLIST",
}
var AddChannelType_value = map[string]int32{
	"ACT_OFFICIAL_ACTIVITY":  1,
	"ACT_BLACKLIST":          2,
	"ACT_OFFICIAL_RECOMMEND": 3,
	"ACT_FIX_RECOMMEND":      4,
	"ACT_RECOMMENDV2":        5,
	"ACT_LIVE_RECOMMEND":     6,
	"ACT_RECOMMEND_NEWUSER":  7,
	"ACT_BLOCKLIST":          101,
}

func (x AddChannelType) Enum() *AddChannelType {
	p := new(AddChannelType)
	*p = x
	return p
}
func (x AddChannelType) String() string {
	return proto.EnumName(AddChannelType_name, int32(x))
}
func (x *AddChannelType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(AddChannelType_value, data, "AddChannelType")
	if err != nil {
		return err
	}
	*x = AddChannelType(value)
	return nil
}
func (AddChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{0}
}

// 后续要为2幂
type ChannelInfoExFlag int32

const (
	ChannelInfoExFlag_EXFLAG_CANBETOP  ChannelInfoExFlag = 1
	ChannelInfoExFlag_EXFLAG_ISESSENCE ChannelInfoExFlag = 2
)

var ChannelInfoExFlag_name = map[int32]string{
	1: "EXFLAG_CANBETOP",
	2: "EXFLAG_ISESSENCE",
}
var ChannelInfoExFlag_value = map[string]int32{
	"EXFLAG_CANBETOP":  1,
	"EXFLAG_ISESSENCE": 2,
}

func (x ChannelInfoExFlag) Enum() *ChannelInfoExFlag {
	p := new(ChannelInfoExFlag)
	*p = x
	return p
}
func (x ChannelInfoExFlag) String() string {
	return proto.EnumName(ChannelInfoExFlag_name, int32(x))
}
func (x *ChannelInfoExFlag) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChannelInfoExFlag_value, data, "ChannelInfoExFlag")
	if err != nil {
		return err
	}
	*x = ChannelInfoExFlag(value)
	return nil
}
func (ChannelInfoExFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{1}
}

type HotChannelType int32

const (
	HotChannelType_HOT_BLACKLIST HotChannelType = 1
	HotChannelType_HOT_BLOCKLIST HotChannelType = 101
)

var HotChannelType_name = map[int32]string{
	1:   "HOT_BLACKLIST",
	101: "HOT_BLOCKLIST",
}
var HotChannelType_value = map[string]int32{
	"HOT_BLACKLIST": 1,
	"HOT_BLOCKLIST": 101,
}

func (x HotChannelType) Enum() *HotChannelType {
	p := new(HotChannelType)
	*p = x
	return p
}
func (x HotChannelType) String() string {
	return proto.EnumName(HotChannelType_name, int32(x))
}
func (x *HotChannelType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(HotChannelType_value, data, "HotChannelType")
	if err != nil {
		return err
	}
	*x = HotChannelType(value)
	return nil
}
func (HotChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{2}
}

type RootTagType int32

const (
	RootTagType_RTT_INVALID_TAG RootTagType = 0
	RootTagType_RTT_FUNNY_ROOM  RootTagType = 1
	RootTagType_RTT_GAME        RootTagType = 2
)

var RootTagType_name = map[int32]string{
	0: "RTT_INVALID_TAG",
	1: "RTT_FUNNY_ROOM",
	2: "RTT_GAME",
}
var RootTagType_value = map[string]int32{
	"RTT_INVALID_TAG": 0,
	"RTT_FUNNY_ROOM":  1,
	"RTT_GAME":        2,
}

func (x RootTagType) Enum() *RootTagType {
	p := new(RootTagType)
	*p = x
	return p
}
func (x RootTagType) String() string {
	return proto.EnumName(RootTagType_name, int32(x))
}
func (x *RootTagType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RootTagType_value, data, "RootTagType")
	if err != nil {
		return err
	}
	*x = RootTagType(value)
	return nil
}
func (RootTagType) EnumDescriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{3} }

type ADV_TYPE int32

const (
	ADV_TYPE_AT_CHANNEL_FUNNY    ADV_TYPE = 0
	ADV_TYPE_AT_CHANNEL_HOMEPAGE ADV_TYPE = 1
)

var ADV_TYPE_name = map[int32]string{
	0: "AT_CHANNEL_FUNNY",
	1: "AT_CHANNEL_HOMEPAGE",
}
var ADV_TYPE_value = map[string]int32{
	"AT_CHANNEL_FUNNY":    0,
	"AT_CHANNEL_HOMEPAGE": 1,
}

func (x ADV_TYPE) Enum() *ADV_TYPE {
	p := new(ADV_TYPE)
	*p = x
	return p
}
func (x ADV_TYPE) String() string {
	return proto.EnumName(ADV_TYPE_name, int32(x))
}
func (x *ADV_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ADV_TYPE_value, data, "ADV_TYPE")
	if err != nil {
		return err
	}
	*x = ADV_TYPE(value)
	return nil
}
func (ADV_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{4} }

type RecommendChannelType int32

const (
	RecommendChannelType_GAME_ROOM RecommendChannelType = 1
)

var RecommendChannelType_name = map[int32]string{
	1: "GAME_ROOM",
}
var RecommendChannelType_value = map[string]int32{
	"GAME_ROOM": 1,
}

func (x RecommendChannelType) Enum() *RecommendChannelType {
	p := new(RecommendChannelType)
	*p = x
	return p
}
func (x RecommendChannelType) String() string {
	return proto.EnumName(RecommendChannelType_name, int32(x))
}
func (x *RecommendChannelType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(RecommendChannelType_value, data, "RecommendChannelType")
	if err != nil {
		return err
	}
	*x = RecommendChannelType(value)
	return nil
}
func (RecommendChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{5}
}

type SubTag_SubTagFlag int32

const (
	SubTag_STF_HIDE_SETTING SubTag_SubTagFlag = 1
	SubTag_STF_HIDE_TAB     SubTag_SubTagFlag = 2
)

var SubTag_SubTagFlag_name = map[int32]string{
	1: "STF_HIDE_SETTING",
	2: "STF_HIDE_TAB",
}
var SubTag_SubTagFlag_value = map[string]int32{
	"STF_HIDE_SETTING": 1,
	"STF_HIDE_TAB":     2,
}

func (x SubTag_SubTagFlag) Enum() *SubTag_SubTagFlag {
	p := new(SubTag_SubTagFlag)
	*p = x
	return p
}
func (x SubTag_SubTagFlag) String() string {
	return proto.EnumName(SubTag_SubTagFlag_name, int32(x))
}
func (x *SubTag_SubTagFlag) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(SubTag_SubTagFlag_value, data, "SubTag_SubTagFlag")
	if err != nil {
		return err
	}
	*x = SubTag_SubTagFlag(value)
	return nil
}
func (SubTag_SubTagFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{50, 0}
}

// 进出房间事件
type UserEnterChannelReq struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	UserCount   uint32 `protobuf:"varint,2,req,name=user_count,json=userCount" json:"user_count"`
	IsAdmin     bool   `protobuf:"varint,3,opt,name=is_admin,json=isAdmin" json:"is_admin"`
	IsEnter     bool   `protobuf:"varint,4,opt,name=is_enter,json=isEnter" json:"is_enter"`
	AdminCount  uint32 `protobuf:"varint,5,opt,name=admin_count,json=adminCount" json:"admin_count"`
	IsNormal    bool   `protobuf:"varint,6,opt,name=is_normal,json=isNormal" json:"is_normal"`
	ChannelType uint32 `protobuf:"varint,7,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *UserEnterChannelReq) Reset()         { *m = UserEnterChannelReq{} }
func (m *UserEnterChannelReq) String() string { return proto.CompactTextString(m) }
func (*UserEnterChannelReq) ProtoMessage()    {}
func (*UserEnterChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{0}
}

func (m *UserEnterChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserEnterChannelReq) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *UserEnterChannelReq) GetIsAdmin() bool {
	if m != nil {
		return m.IsAdmin
	}
	return false
}

func (m *UserEnterChannelReq) GetIsEnter() bool {
	if m != nil {
		return m.IsEnter
	}
	return false
}

func (m *UserEnterChannelReq) GetAdminCount() uint32 {
	if m != nil {
		return m.AdminCount
	}
	return 0
}

func (m *UserEnterChannelReq) GetIsNormal() bool {
	if m != nil {
		return m.IsNormal
	}
	return false
}

func (m *UserEnterChannelReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type UserEnterChannelResp struct {
}

func (m *UserEnterChannelResp) Reset()         { *m = UserEnterChannelResp{} }
func (m *UserEnterChannelResp) String() string { return proto.CompactTextString(m) }
func (*UserEnterChannelResp) ProtoMessage()    {}
func (*UserEnterChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{1}
}

// 房间礼物
type AddChannelGiftReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	GiftValue uint32 `protobuf:"varint,2,req,name=gift_value,json=giftValue" json:"gift_value"`
}

func (m *AddChannelGiftReq) Reset()         { *m = AddChannelGiftReq{} }
func (m *AddChannelGiftReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelGiftReq) ProtoMessage()    {}
func (*AddChannelGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{2}
}

func (m *AddChannelGiftReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelGiftReq) GetGiftValue() uint32 {
	if m != nil {
		return m.GiftValue
	}
	return 0
}

type AddChannelGiftResp struct {
}

func (m *AddChannelGiftResp) Reset()         { *m = AddChannelGiftResp{} }
func (m *AddChannelGiftResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelGiftResp) ProtoMessage()    {}
func (*AddChannelGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{3}
}

// 获取接口
type GetRecommendChannelReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Start     uint32 `protobuf:"varint,2,req,name=start" json:"start"`
	Count     uint32 `protobuf:"varint,3,req,name=count" json:"count"`
	IsNewUser bool   `protobuf:"varint,4,req,name=is_new_user,json=isNewUser" json:"is_new_user"`
}

func (m *GetRecommendChannelReq) Reset()         { *m = GetRecommendChannelReq{} }
func (m *GetRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelReq) ProtoMessage()    {}
func (*GetRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{4}
}

func (m *GetRecommendChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRecommendChannelReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetRecommendChannelReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetRecommendChannelReq) GetIsNewUser() bool {
	if m != nil {
		return m.IsNewUser
	}
	return false
}

type ChannelInfo struct {
	ChannelId  uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Tag        uint32 `protobuf:"varint,2,opt,name=tag" json:"tag"`
	InfoTag    string `protobuf:"bytes,3,opt,name=info_tag,json=infoTag" json:"info_tag"`
	BkColor    string `protobuf:"bytes,4,opt,name=bk_color,json=bkColor" json:"bk_color"`
	InfoExFlag uint32 `protobuf:"varint,5,opt,name=info_ex_flag,json=infoExFlag" json:"info_ex_flag"`
}

func (m *ChannelInfo) Reset()                    { *m = ChannelInfo{} }
func (m *ChannelInfo) String() string            { return proto.CompactTextString(m) }
func (*ChannelInfo) ProtoMessage()               {}
func (*ChannelInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{5} }

func (m *ChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelInfo) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *ChannelInfo) GetInfoTag() string {
	if m != nil {
		return m.InfoTag
	}
	return ""
}

func (m *ChannelInfo) GetBkColor() string {
	if m != nil {
		return m.BkColor
	}
	return ""
}

func (m *ChannelInfo) GetInfoExFlag() uint32 {
	if m != nil {
		return m.InfoExFlag
	}
	return 0
}

type GetRecommendChannelResp struct {
	ChannelIdList   []uint32       `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ReachEnd        bool           `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end"`
	ChannelInfoList []*ChannelInfo `protobuf:"bytes,3,rep,name=channel_info_list,json=channelInfoList" json:"channel_info_list,omitempty"`
	MayFixTopIdList []uint32       `protobuf:"varint,4,rep,name=may_fix_top_id_list,json=mayFixTopIdList" json:"may_fix_top_id_list,omitempty"`
}

func (m *GetRecommendChannelResp) Reset()         { *m = GetRecommendChannelResp{} }
func (m *GetRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendChannelResp) ProtoMessage()    {}
func (*GetRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{6}
}

func (m *GetRecommendChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetRecommendChannelResp) GetReachEnd() bool {
	if m != nil {
		return m.ReachEnd
	}
	return false
}

func (m *GetRecommendChannelResp) GetChannelInfoList() []*ChannelInfo {
	if m != nil {
		return m.ChannelInfoList
	}
	return nil
}

func (m *GetRecommendChannelResp) GetMayFixTopIdList() []uint32 {
	if m != nil {
		return m.MayFixTopIdList
	}
	return nil
}

type GetChannelTagColorReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetChannelTagColorReq) Reset()         { *m = GetChannelTagColorReq{} }
func (m *GetChannelTagColorReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagColorReq) ProtoMessage()    {}
func (*GetChannelTagColorReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{7}
}

func (m *GetChannelTagColorReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type GetChannelTagColorResp struct {
	// 只包含info_tag, bk_color
	ChannelInfoList []*ChannelInfo `protobuf:"bytes,1,rep,name=channel_info_list,json=channelInfoList" json:"channel_info_list,omitempty"`
}

func (m *GetChannelTagColorResp) Reset()         { *m = GetChannelTagColorResp{} }
func (m *GetChannelTagColorResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagColorResp) ProtoMessage()    {}
func (*GetChannelTagColorResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{8}
}

func (m *GetChannelTagColorResp) GetChannelInfoList() []*ChannelInfo {
	if m != nil {
		return m.ChannelInfoList
	}
	return nil
}

type AddChannelReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	IsAdd         bool     `protobuf:"varint,2,req,name=is_add,json=isAdd" json:"is_add"`
	ChannelType   uint32   `protobuf:"varint,3,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *AddChannelReq) Reset()                    { *m = AddChannelReq{} }
func (m *AddChannelReq) String() string            { return proto.CompactTextString(m) }
func (*AddChannelReq) ProtoMessage()               {}
func (*AddChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{9} }

func (m *AddChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *AddChannelReq) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

func (m *AddChannelReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type AddChannelResp struct {
}

func (m *AddChannelResp) Reset()                    { *m = AddChannelResp{} }
func (m *AddChannelResp) String() string            { return proto.CompactTextString(m) }
func (*AddChannelResp) ProtoMessage()               {}
func (*AddChannelResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{10} }

type GetChannelReq struct {
	ChannelType uint32 `protobuf:"varint,1,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *GetChannelReq) Reset()                    { *m = GetChannelReq{} }
func (m *GetChannelReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelReq) ProtoMessage()               {}
func (*GetChannelReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{11} }

func (m *GetChannelReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetChannelResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetChannelResp) Reset()                    { *m = GetChannelResp{} }
func (m *GetChannelResp) String() string            { return proto.CompactTextString(m) }
func (*GetChannelResp) ProtoMessage()               {}
func (*GetChannelResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{12} }

func (m *GetChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type ChannelTypeInfo struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *ChannelTypeInfo) Reset()                    { *m = ChannelTypeInfo{} }
func (m *ChannelTypeInfo) String() string            { return proto.CompactTextString(m) }
func (*ChannelTypeInfo) ProtoMessage()               {}
func (*ChannelTypeInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{13} }

func (m *ChannelTypeInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelTypeInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type BatchGetChannelTypeReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchGetChannelTypeReq) Reset()         { *m = BatchGetChannelTypeReq{} }
func (m *BatchGetChannelTypeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTypeReq) ProtoMessage()    {}
func (*BatchGetChannelTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{14}
}

func (m *BatchGetChannelTypeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetChannelTypeResp struct {
	ChannelTypeList []*ChannelTypeInfo `protobuf:"bytes,1,rep,name=channel_type_list,json=channelTypeList" json:"channel_type_list,omitempty"`
}

func (m *BatchGetChannelTypeResp) Reset()         { *m = BatchGetChannelTypeResp{} }
func (m *BatchGetChannelTypeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelTypeResp) ProtoMessage()    {}
func (*BatchGetChannelTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{15}
}

func (m *BatchGetChannelTypeResp) GetChannelTypeList() []*ChannelTypeInfo {
	if m != nil {
		return m.ChannelTypeList
	}
	return nil
}

type ChannelInfoEx struct {
	ChannelId      uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StartTs        uint32   `protobuf:"varint,2,req,name=start_ts,json=startTs" json:"start_ts"`
	EndTs          uint32   `protobuf:"varint,3,req,name=end_ts,json=endTs" json:"end_ts"`
	Desc           string   `protobuf:"bytes,4,req,name=desc" json:"desc"`
	UserCount      uint32   `protobuf:"varint,5,opt,name=user_count,json=userCount" json:"user_count"`
	HoursToAddList []uint32 `protobuf:"varint,6,rep,name=hours_to_add_list,json=hoursToAddList" json:"hours_to_add_list,omitempty"`
	Flags          uint32   `protobuf:"varint,7,opt,name=flags" json:"flags"`
	InfoTag        string   `protobuf:"bytes,8,opt,name=info_tag,json=infoTag" json:"info_tag"`
	BkColor        string   `protobuf:"bytes,9,opt,name=bk_color,json=bkColor" json:"bk_color"`
}

func (m *ChannelInfoEx) Reset()                    { *m = ChannelInfoEx{} }
func (m *ChannelInfoEx) String() string            { return proto.CompactTextString(m) }
func (*ChannelInfoEx) ProtoMessage()               {}
func (*ChannelInfoEx) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{16} }

func (m *ChannelInfoEx) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelInfoEx) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *ChannelInfoEx) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ChannelInfoEx) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ChannelInfoEx) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *ChannelInfoEx) GetHoursToAddList() []uint32 {
	if m != nil {
		return m.HoursToAddList
	}
	return nil
}

func (m *ChannelInfoEx) GetFlags() uint32 {
	if m != nil {
		return m.Flags
	}
	return 0
}

func (m *ChannelInfoEx) GetInfoTag() string {
	if m != nil {
		return m.InfoTag
	}
	return ""
}

func (m *ChannelInfoEx) GetBkColor() string {
	if m != nil {
		return m.BkColor
	}
	return ""
}

type SetChannelInfoExReq struct {
	ChannelType   uint32           `protobuf:"varint,1,req,name=channel_type,json=channelType" json:"channel_type"`
	ChannelExList []*ChannelInfoEx `protobuf:"bytes,2,rep,name=channel_ex_list,json=channelExList" json:"channel_ex_list,omitempty"`
}

func (m *SetChannelInfoExReq) Reset()         { *m = SetChannelInfoExReq{} }
func (m *SetChannelInfoExReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelInfoExReq) ProtoMessage()    {}
func (*SetChannelInfoExReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{17}
}

func (m *SetChannelInfoExReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *SetChannelInfoExReq) GetChannelExList() []*ChannelInfoEx {
	if m != nil {
		return m.ChannelExList
	}
	return nil
}

type SetChannelInfoExResp struct {
}

func (m *SetChannelInfoExResp) Reset()         { *m = SetChannelInfoExResp{} }
func (m *SetChannelInfoExResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelInfoExResp) ProtoMessage()    {}
func (*SetChannelInfoExResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{18}
}

type GetChannelInfoExReq struct {
	ChannelType   uint32   `protobuf:"varint,1,opt,name=channel_type,json=channelType" json:"channel_type"`
	ChannelIdList []uint32 `protobuf:"varint,2,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetChannelInfoExReq) Reset()         { *m = GetChannelInfoExReq{} }
func (m *GetChannelInfoExReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelInfoExReq) ProtoMessage()    {}
func (*GetChannelInfoExReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{19}
}

func (m *GetChannelInfoExReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *GetChannelInfoExReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type GetChannelInfoExResp struct {
	ChannelExList []*ChannelInfoEx `protobuf:"bytes,2,rep,name=channel_ex_list,json=channelExList" json:"channel_ex_list,omitempty"`
}

func (m *GetChannelInfoExResp) Reset()         { *m = GetChannelInfoExResp{} }
func (m *GetChannelInfoExResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelInfoExResp) ProtoMessage()    {}
func (*GetChannelInfoExResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{20}
}

func (m *GetChannelInfoExResp) GetChannelExList() []*ChannelInfoEx {
	if m != nil {
		return m.ChannelExList
	}
	return nil
}

type RemoveChannelReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *RemoveChannelReq) Reset()         { *m = RemoveChannelReq{} }
func (m *RemoveChannelReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelReq) ProtoMessage()    {}
func (*RemoveChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{21}
}

func (m *RemoveChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type RemoveChannelResp struct {
}

func (m *RemoveChannelResp) Reset()         { *m = RemoveChannelResp{} }
func (m *RemoveChannelResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelResp) ProtoMessage()    {}
func (*RemoveChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{22}
}

type RemoveChannelByTypeReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ChannelType   uint32   `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *RemoveChannelByTypeReq) Reset()         { *m = RemoveChannelByTypeReq{} }
func (m *RemoveChannelByTypeReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelByTypeReq) ProtoMessage()    {}
func (*RemoveChannelByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{23}
}

func (m *RemoveChannelByTypeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *RemoveChannelByTypeReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type RemoveChannelByTypeResp struct {
}

func (m *RemoveChannelByTypeResp) Reset()         { *m = RemoveChannelByTypeResp{} }
func (m *RemoveChannelByTypeResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelByTypeResp) ProtoMessage()    {}
func (*RemoveChannelByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{24}
}

type AddUserLikeChannelReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *AddUserLikeChannelReq) Reset()         { *m = AddUserLikeChannelReq{} }
func (m *AddUserLikeChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddUserLikeChannelReq) ProtoMessage()    {}
func (*AddUserLikeChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{25}
}

func (m *AddUserLikeChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type AddUserLikeChannelResp struct {
}

func (m *AddUserLikeChannelResp) Reset()         { *m = AddUserLikeChannelResp{} }
func (m *AddUserLikeChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddUserLikeChannelResp) ProtoMessage()    {}
func (*AddUserLikeChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{26}
}

type GetChannelInfoTagReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetChannelInfoTagReq) Reset()         { *m = GetChannelInfoTagReq{} }
func (m *GetChannelInfoTagReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelInfoTagReq) ProtoMessage()    {}
func (*GetChannelInfoTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{27}
}

func (m *GetChannelInfoTagReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type ChannelInfoTag struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	InfoTag     string `protobuf:"bytes,2,opt,name=info_tag,json=infoTag" json:"info_tag"`
	BkColor     string `protobuf:"bytes,3,opt,name=bk_color,json=bkColor" json:"bk_color"`
	ChannelType uint32 `protobuf:"varint,4,opt,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *ChannelInfoTag) Reset()                    { *m = ChannelInfoTag{} }
func (m *ChannelInfoTag) String() string            { return proto.CompactTextString(m) }
func (*ChannelInfoTag) ProtoMessage()               {}
func (*ChannelInfoTag) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{28} }

func (m *ChannelInfoTag) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelInfoTag) GetInfoTag() string {
	if m != nil {
		return m.InfoTag
	}
	return ""
}

func (m *ChannelInfoTag) GetBkColor() string {
	if m != nil {
		return m.BkColor
	}
	return ""
}

func (m *ChannelInfoTag) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetChannelInfoTagResp struct {
	ChannelInfotagList []*ChannelInfoTag `protobuf:"bytes,1,rep,name=channel_infotag_list,json=channelInfotagList" json:"channel_infotag_list,omitempty"`
}

func (m *GetChannelInfoTagResp) Reset()         { *m = GetChannelInfoTagResp{} }
func (m *GetChannelInfoTagResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelInfoTagResp) ProtoMessage()    {}
func (*GetChannelInfoTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{29}
}

func (m *GetChannelInfoTagResp) GetChannelInfotagList() []*ChannelInfoTag {
	if m != nil {
		return m.ChannelInfotagList
	}
	return nil
}

type GetHotChannelReq struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Start uint32 `protobuf:"varint,2,req,name=start" json:"start"`
	Count uint32 `protobuf:"varint,3,req,name=count" json:"count"`
	Ts    uint32 `protobuf:"varint,4,opt,name=ts" json:"ts"`
}

func (m *GetHotChannelReq) Reset()         { *m = GetHotChannelReq{} }
func (m *GetHotChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelReq) ProtoMessage()    {}
func (*GetHotChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{30}
}

func (m *GetHotChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetHotChannelReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetHotChannelReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetHotChannelReq) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type ChannelHotInfo struct {
	ChannelId     uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	DayGiftValue  uint32 `protobuf:"varint,2,opt,name=day_gift_value,json=dayGiftValue" json:"day_gift_value"`
	RankGiftValue uint32 `protobuf:"varint,3,opt,name=rank_gift_value,json=rankGiftValue" json:"rank_gift_value"`
}

func (m *ChannelHotInfo) Reset()                    { *m = ChannelHotInfo{} }
func (m *ChannelHotInfo) String() string            { return proto.CompactTextString(m) }
func (*ChannelHotInfo) ProtoMessage()               {}
func (*ChannelHotInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{31} }

func (m *ChannelHotInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelHotInfo) GetDayGiftValue() uint32 {
	if m != nil {
		return m.DayGiftValue
	}
	return 0
}

func (m *ChannelHotInfo) GetRankGiftValue() uint32 {
	if m != nil {
		return m.RankGiftValue
	}
	return 0
}

type GetHotChannelResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ReachEnd      bool     `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end"`
}

func (m *GetHotChannelResp) Reset()         { *m = GetHotChannelResp{} }
func (m *GetHotChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelResp) ProtoMessage()    {}
func (*GetHotChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{32}
}

func (m *GetHotChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetHotChannelResp) GetReachEnd() bool {
	if m != nil {
		return m.ReachEnd
	}
	return false
}

type SetHotChannelCfgReq struct {
	TotalGiftValue uint32 `protobuf:"varint,1,req,name=total_gift_value,json=totalGiftValue" json:"total_gift_value"`
}

func (m *SetHotChannelCfgReq) Reset()         { *m = SetHotChannelCfgReq{} }
func (m *SetHotChannelCfgReq) String() string { return proto.CompactTextString(m) }
func (*SetHotChannelCfgReq) ProtoMessage()    {}
func (*SetHotChannelCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{33}
}

func (m *SetHotChannelCfgReq) GetTotalGiftValue() uint32 {
	if m != nil {
		return m.TotalGiftValue
	}
	return 0
}

type GetHotChannelCfgReq struct {
}

func (m *GetHotChannelCfgReq) Reset()         { *m = GetHotChannelCfgReq{} }
func (m *GetHotChannelCfgReq) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelCfgReq) ProtoMessage()    {}
func (*GetHotChannelCfgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{34}
}

type GetHotChannelCfgResp struct {
	TotalGiftValue uint32 `protobuf:"varint,1,req,name=total_gift_value,json=totalGiftValue" json:"total_gift_value"`
}

func (m *GetHotChannelCfgResp) Reset()         { *m = GetHotChannelCfgResp{} }
func (m *GetHotChannelCfgResp) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelCfgResp) ProtoMessage()    {}
func (*GetHotChannelCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{35}
}

func (m *GetHotChannelCfgResp) GetTotalGiftValue() uint32 {
	if m != nil {
		return m.TotalGiftValue
	}
	return 0
}

// 设置热门房间的类型
type SetHotChannelTypeReq struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *SetHotChannelTypeReq) Reset()         { *m = SetHotChannelTypeReq{} }
func (m *SetHotChannelTypeReq) String() string { return proto.CompactTextString(m) }
func (*SetHotChannelTypeReq) ProtoMessage()    {}
func (*SetHotChannelTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{36}
}

func (m *SetHotChannelTypeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetHotChannelTypeReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type HotChannelInfo struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelType uint32 `protobuf:"varint,2,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *HotChannelInfo) Reset()                    { *m = HotChannelInfo{} }
func (m *HotChannelInfo) String() string            { return proto.CompactTextString(m) }
func (*HotChannelInfo) ProtoMessage()               {}
func (*HotChannelInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{37} }

func (m *HotChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *HotChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

// 获取热门房间的类型
type BatchGetHotChannelTypeReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchGetHotChannelTypeReq) Reset()         { *m = BatchGetHotChannelTypeReq{} }
func (m *BatchGetHotChannelTypeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetHotChannelTypeReq) ProtoMessage()    {}
func (*BatchGetHotChannelTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{38}
}

func (m *BatchGetHotChannelTypeReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatchGetHotChannelTypeResp struct {
	ChannelList []*HotChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
}

func (m *BatchGetHotChannelTypeResp) Reset()         { *m = BatchGetHotChannelTypeResp{} }
func (m *BatchGetHotChannelTypeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetHotChannelTypeResp) ProtoMessage()    {}
func (*BatchGetHotChannelTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{39}
}

func (m *BatchGetHotChannelTypeResp) GetChannelList() []*HotChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

// 获取指定类型的热门房间列表
type GetHotChannelListByTypeReq struct {
	ChannelType uint32 `protobuf:"varint,1,req,name=channel_type,json=channelType" json:"channel_type"`
}

func (m *GetHotChannelListByTypeReq) Reset()         { *m = GetHotChannelListByTypeReq{} }
func (m *GetHotChannelListByTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelListByTypeReq) ProtoMessage()    {}
func (*GetHotChannelListByTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{40}
}

func (m *GetHotChannelListByTypeReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetHotChannelListByTypeResp struct {
	ChannelList []*HotChannelInfo `protobuf:"bytes,1,rep,name=channel_list,json=channelList" json:"channel_list,omitempty"`
}

func (m *GetHotChannelListByTypeResp) Reset()         { *m = GetHotChannelListByTypeResp{} }
func (m *GetHotChannelListByTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetHotChannelListByTypeResp) ProtoMessage()    {}
func (*GetHotChannelListByTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{41}
}

func (m *GetHotChannelListByTypeResp) GetChannelList() []*HotChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type BatchDelHotChannelReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatchDelHotChannelReq) Reset()         { *m = BatchDelHotChannelReq{} }
func (m *BatchDelHotChannelReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelHotChannelReq) ProtoMessage()    {}
func (*BatchDelHotChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{42}
}

func (m *BatchDelHotChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type ColorInfo struct {
	TagName string `protobuf:"bytes,1,req,name=tag_name,json=tagName" json:"tag_name"`
	Color   string `protobuf:"bytes,2,req,name=color" json:"color"`
}

func (m *ColorInfo) Reset()                    { *m = ColorInfo{} }
func (m *ColorInfo) String() string            { return proto.CompactTextString(m) }
func (*ColorInfo) ProtoMessage()               {}
func (*ColorInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{43} }

func (m *ColorInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *ColorInfo) GetColor() string {
	if m != nil {
		return m.Color
	}
	return ""
}

type AddColorReq struct {
	ColorInfoList []*ColorInfo `protobuf:"bytes,1,rep,name=color_info_list,json=colorInfoList" json:"color_info_list,omitempty"`
	IsAdd         bool         `protobuf:"varint,2,req,name=is_add,json=isAdd" json:"is_add"`
}

func (m *AddColorReq) Reset()                    { *m = AddColorReq{} }
func (m *AddColorReq) String() string            { return proto.CompactTextString(m) }
func (*AddColorReq) ProtoMessage()               {}
func (*AddColorReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{44} }

func (m *AddColorReq) GetColorInfoList() []*ColorInfo {
	if m != nil {
		return m.ColorInfoList
	}
	return nil
}

func (m *AddColorReq) GetIsAdd() bool {
	if m != nil {
		return m.IsAdd
	}
	return false
}

type AddColorResp struct {
}

func (m *AddColorResp) Reset()                    { *m = AddColorResp{} }
func (m *AddColorResp) String() string            { return proto.CompactTextString(m) }
func (*AddColorResp) ProtoMessage()               {}
func (*AddColorResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{45} }

type GetAllColorReq struct {
}

func (m *GetAllColorReq) Reset()                    { *m = GetAllColorReq{} }
func (m *GetAllColorReq) String() string            { return proto.CompactTextString(m) }
func (*GetAllColorReq) ProtoMessage()               {}
func (*GetAllColorReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{46} }

type GetAllColorResp struct {
	ColorInfoList []*ColorInfo `protobuf:"bytes,1,rep,name=color_info_list,json=colorInfoList" json:"color_info_list,omitempty"`
}

func (m *GetAllColorResp) Reset()                    { *m = GetAllColorResp{} }
func (m *GetAllColorResp) String() string            { return proto.CompactTextString(m) }
func (*GetAllColorResp) ProtoMessage()               {}
func (*GetAllColorResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{47} }

func (m *GetAllColorResp) GetColorInfoList() []*ColorInfo {
	if m != nil {
		return m.ColorInfoList
	}
	return nil
}

type CardInfo struct {
	CardName    string `protobuf:"bytes,1,req,name=card_name,json=cardName" json:"card_name"`
	CardImage   string `protobuf:"bytes,2,req,name=card_image,json=cardImage" json:"card_image"`
	BackImage   string `protobuf:"bytes,3,req,name=back_image,json=backImage" json:"back_image"`
	ClientLink  string `protobuf:"bytes,4,req,name=client_link,json=clientLink" json:"client_link"`
	TagId       uint32 `protobuf:"varint,5,opt,name=tag_id,json=tagId" json:"tag_id"`
	UserCount   uint32 `protobuf:"varint,6,opt,name=user_count,json=userCount" json:"user_count"`
	GameId      uint32 `protobuf:"varint,7,opt,name=game_id,json=gameId" json:"game_id"`
	FixPosition bool   `protobuf:"varint,8,opt,name=fix_position,json=fixPosition" json:"fix_position"`
}

func (m *CardInfo) Reset()                    { *m = CardInfo{} }
func (m *CardInfo) String() string            { return proto.CompactTextString(m) }
func (*CardInfo) ProtoMessage()               {}
func (*CardInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{48} }

func (m *CardInfo) GetCardName() string {
	if m != nil {
		return m.CardName
	}
	return ""
}

func (m *CardInfo) GetCardImage() string {
	if m != nil {
		return m.CardImage
	}
	return ""
}

func (m *CardInfo) GetBackImage() string {
	if m != nil {
		return m.BackImage
	}
	return ""
}

func (m *CardInfo) GetClientLink() string {
	if m != nil {
		return m.ClientLink
	}
	return ""
}

func (m *CardInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *CardInfo) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func (m *CardInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CardInfo) GetFixPosition() bool {
	if m != nil {
		return m.FixPosition
	}
	return false
}

type RootTag struct {
	TagName string `protobuf:"bytes,1,req,name=tag_name,json=tagName" json:"tag_name"`
	TagId   uint32 `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id"`
	CalcSub uint32 `protobuf:"varint,3,opt,name=calc_sub,json=calcSub" json:"calc_sub"`
	TagType uint32 `protobuf:"varint,4,opt,name=tag_type,json=tagType" json:"tag_type"`
}

func (m *RootTag) Reset()                    { *m = RootTag{} }
func (m *RootTag) String() string            { return proto.CompactTextString(m) }
func (*RootTag) ProtoMessage()               {}
func (*RootTag) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{49} }

func (m *RootTag) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *RootTag) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *RootTag) GetCalcSub() uint32 {
	if m != nil {
		return m.CalcSub
	}
	return 0
}

func (m *RootTag) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type SubTag struct {
	TagName   string `protobuf:"bytes,1,req,name=tag_name,json=tagName" json:"tag_name"`
	Icon      string `protobuf:"bytes,2,req,name=icon" json:"icon"`
	TagId     uint32 `protobuf:"varint,3,req,name=tag_id,json=tagId" json:"tag_id"`
	IsShow    bool   `protobuf:"varint,4,opt,name=is_show,json=isShow" json:"is_show"`
	JumpUrl   string `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl" json:"jump_url"`
	RootTagId uint32 `protobuf:"varint,6,opt,name=root_tag_id,json=rootTagId" json:"root_tag_id"`
	BkColor   string `protobuf:"bytes,7,opt,name=bk_color,json=bkColor" json:"bk_color"`
	ExtFlag   uint32 `protobuf:"varint,8,opt,name=ext_flag,json=extFlag" json:"ext_flag"`
}

func (m *SubTag) Reset()                    { *m = SubTag{} }
func (m *SubTag) String() string            { return proto.CompactTextString(m) }
func (*SubTag) ProtoMessage()               {}
func (*SubTag) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{50} }

func (m *SubTag) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *SubTag) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *SubTag) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *SubTag) GetIsShow() bool {
	if m != nil {
		return m.IsShow
	}
	return false
}

func (m *SubTag) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

func (m *SubTag) GetRootTagId() uint32 {
	if m != nil {
		return m.RootTagId
	}
	return 0
}

func (m *SubTag) GetBkColor() string {
	if m != nil {
		return m.BkColor
	}
	return ""
}

func (m *SubTag) GetExtFlag() uint32 {
	if m != nil {
		return m.ExtFlag
	}
	return 0
}

type TagTypes struct {
	RootTag    *RootTag  `protobuf:"bytes,1,req,name=root_tag,json=rootTag" json:"root_tag,omitempty"`
	SubTagList []*SubTag `protobuf:"bytes,2,rep,name=sub_tag_list,json=subTagList" json:"sub_tag_list,omitempty"`
}

func (m *TagTypes) Reset()                    { *m = TagTypes{} }
func (m *TagTypes) String() string            { return proto.CompactTextString(m) }
func (*TagTypes) ProtoMessage()               {}
func (*TagTypes) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{51} }

func (m *TagTypes) GetRootTag() *RootTag {
	if m != nil {
		return m.RootTag
	}
	return nil
}

func (m *TagTypes) GetSubTagList() []*SubTag {
	if m != nil {
		return m.SubTagList
	}
	return nil
}

type GetAllTagTypeReq struct {
}

func (m *GetAllTagTypeReq) Reset()         { *m = GetAllTagTypeReq{} }
func (m *GetAllTagTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTagTypeReq) ProtoMessage()    {}
func (*GetAllTagTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{52}
}

type GetAllTagTypeResp struct {
	TagTypesList []*TagTypes `protobuf:"bytes,1,rep,name=tag_types_list,json=tagTypesList" json:"tag_types_list,omitempty"`
}

func (m *GetAllTagTypeResp) Reset()         { *m = GetAllTagTypeResp{} }
func (m *GetAllTagTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetAllTagTypeResp) ProtoMessage()    {}
func (*GetAllTagTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{53}
}

func (m *GetAllTagTypeResp) GetTagTypesList() []*TagTypes {
	if m != nil {
		return m.TagTypesList
	}
	return nil
}

type GetChannelByTagReq struct {
	TagId uint32 `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id"`
	Start uint32 `protobuf:"varint,2,req,name=start" json:"start"`
	Count uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetChannelByTagReq) Reset()         { *m = GetChannelByTagReq{} }
func (m *GetChannelByTagReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelByTagReq) ProtoMessage()    {}
func (*GetChannelByTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{54}
}

func (m *GetChannelByTagReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetChannelByTagReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetChannelByTagReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetChannelByTagResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ReachEnd      bool     `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end"`
	UserCount     uint32   `protobuf:"varint,3,opt,name=user_count,json=userCount" json:"user_count"`
}

func (m *GetChannelByTagResp) Reset()         { *m = GetChannelByTagResp{} }
func (m *GetChannelByTagResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelByTagResp) ProtoMessage()    {}
func (*GetChannelByTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{55}
}

func (m *GetChannelByTagResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetChannelByTagResp) GetReachEnd() bool {
	if m != nil {
		return m.ReachEnd
	}
	return false
}

func (m *GetChannelByTagResp) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

type GetChannelCardReq struct {
}

func (m *GetChannelCardReq) Reset()         { *m = GetChannelCardReq{} }
func (m *GetChannelCardReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelCardReq) ProtoMessage()    {}
func (*GetChannelCardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{56}
}

type GetChannelCardResp struct {
	CardInfoList []*CardInfo `protobuf:"bytes,1,rep,name=card_info_list,json=cardInfoList" json:"card_info_list,omitempty"`
	GameSubTag   *SubTag     `protobuf:"bytes,2,req,name=game_sub_tag,json=gameSubTag" json:"game_sub_tag,omitempty"`
	GameOnTop    bool        `protobuf:"varint,3,req,name=game_on_top,json=gameOnTop" json:"game_on_top"`
	HasVisitGame bool        `protobuf:"varint,4,req,name=has_visit_game,json=hasVisitGame" json:"has_visit_game"`
}

func (m *GetChannelCardResp) Reset()         { *m = GetChannelCardResp{} }
func (m *GetChannelCardResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelCardResp) ProtoMessage()    {}
func (*GetChannelCardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{57}
}

func (m *GetChannelCardResp) GetCardInfoList() []*CardInfo {
	if m != nil {
		return m.CardInfoList
	}
	return nil
}

func (m *GetChannelCardResp) GetGameSubTag() *SubTag {
	if m != nil {
		return m.GameSubTag
	}
	return nil
}

func (m *GetChannelCardResp) GetGameOnTop() bool {
	if m != nil {
		return m.GameOnTop
	}
	return false
}

func (m *GetChannelCardResp) GetHasVisitGame() bool {
	if m != nil {
		return m.HasVisitGame
	}
	return false
}

type RefreshChannelTimeReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *RefreshChannelTimeReq) Reset()         { *m = RefreshChannelTimeReq{} }
func (m *RefreshChannelTimeReq) String() string { return proto.CompactTextString(m) }
func (*RefreshChannelTimeReq) ProtoMessage()    {}
func (*RefreshChannelTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{58}
}

func (m *RefreshChannelTimeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RefreshChannelTimeResp struct {
	RemainSeconds uint32 `protobuf:"varint,1,req,name=remain_seconds,json=remainSeconds" json:"remain_seconds"`
}

func (m *RefreshChannelTimeResp) Reset()         { *m = RefreshChannelTimeResp{} }
func (m *RefreshChannelTimeResp) String() string { return proto.CompactTextString(m) }
func (*RefreshChannelTimeResp) ProtoMessage()    {}
func (*RefreshChannelTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{59}
}

func (m *RefreshChannelTimeResp) GetRemainSeconds() uint32 {
	if m != nil {
		return m.RemainSeconds
	}
	return 0
}

type GetChannelRefreshCDReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelRefreshCDReq) Reset()         { *m = GetChannelRefreshCDReq{} }
func (m *GetChannelRefreshCDReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRefreshCDReq) ProtoMessage()    {}
func (*GetChannelRefreshCDReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{60}
}

func (m *GetChannelRefreshCDReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelRefreshCDResp struct {
	RemainSeconds uint32 `protobuf:"varint,1,req,name=remain_seconds,json=remainSeconds" json:"remain_seconds"`
}

func (m *GetChannelRefreshCDResp) Reset()         { *m = GetChannelRefreshCDResp{} }
func (m *GetChannelRefreshCDResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRefreshCDResp) ProtoMessage()    {}
func (*GetChannelRefreshCDResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{61}
}

func (m *GetChannelRefreshCDResp) GetRemainSeconds() uint32 {
	if m != nil {
		return m.RemainSeconds
	}
	return 0
}

type SetChannelTagIdReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	TagId     uint32 `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *SetChannelTagIdReq) Reset()         { *m = SetChannelTagIdReq{} }
func (m *SetChannelTagIdReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelTagIdReq) ProtoMessage()    {}
func (*SetChannelTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{62}
}

func (m *SetChannelTagIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelTagIdReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type SetChannelTagIdResp struct {
	OldTagId uint32 `protobuf:"varint,1,opt,name=old_tag_id,json=oldTagId" json:"old_tag_id"`
}

func (m *SetChannelTagIdResp) Reset()         { *m = SetChannelTagIdResp{} }
func (m *SetChannelTagIdResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelTagIdResp) ProtoMessage()    {}
func (*SetChannelTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{63}
}

func (m *SetChannelTagIdResp) GetOldTagId() uint32 {
	if m != nil {
		return m.OldTagId
	}
	return 0
}

type GetChannelTagIdReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelTagIdReq) Reset()         { *m = GetChannelTagIdReq{} }
func (m *GetChannelTagIdReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagIdReq) ProtoMessage()    {}
func (*GetChannelTagIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{64}
}

func (m *GetChannelTagIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelTagIdResp struct {
	SubTag *SubTag `protobuf:"bytes,1,req,name=sub_tag,json=subTag" json:"sub_tag,omitempty"`
	IsSet  bool    `protobuf:"varint,2,req,name=is_set,json=isSet" json:"is_set"`
}

func (m *GetChannelTagIdResp) Reset()         { *m = GetChannelTagIdResp{} }
func (m *GetChannelTagIdResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagIdResp) ProtoMessage()    {}
func (*GetChannelTagIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{65}
}

func (m *GetChannelTagIdResp) GetSubTag() *SubTag {
	if m != nil {
		return m.SubTag
	}
	return nil
}

func (m *GetChannelTagIdResp) GetIsSet() bool {
	if m != nil {
		return m.IsSet
	}
	return false
}

type ChannelTagAdv struct {
	PicUrl   string `protobuf:"bytes,1,req,name=pic_url,json=picUrl" json:"pic_url"`
	AdvUrl   string `protobuf:"bytes,2,req,name=adv_url,json=advUrl" json:"adv_url"`
	ScoreIdx uint32 `protobuf:"varint,3,opt,name=score_idx,json=scoreIdx" json:"score_idx"`
}

func (m *ChannelTagAdv) Reset()                    { *m = ChannelTagAdv{} }
func (m *ChannelTagAdv) String() string            { return proto.CompactTextString(m) }
func (*ChannelTagAdv) ProtoMessage()               {}
func (*ChannelTagAdv) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{66} }

func (m *ChannelTagAdv) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *ChannelTagAdv) GetAdvUrl() string {
	if m != nil {
		return m.AdvUrl
	}
	return ""
}

func (m *ChannelTagAdv) GetScoreIdx() uint32 {
	if m != nil {
		return m.ScoreIdx
	}
	return 0
}

type AddChannelTagAdvReq struct {
	TagAdvList []*ChannelTagAdv `protobuf:"bytes,1,rep,name=tag_adv_list,json=tagAdvList" json:"tag_adv_list,omitempty"`
	CleanOld   bool             `protobuf:"varint,2,opt,name=clean_old,json=cleanOld" json:"clean_old"`
	AdvType    uint32           `protobuf:"varint,3,opt,name=adv_type,json=advType" json:"adv_type"`
}

func (m *AddChannelTagAdvReq) Reset()         { *m = AddChannelTagAdvReq{} }
func (m *AddChannelTagAdvReq) String() string { return proto.CompactTextString(m) }
func (*AddChannelTagAdvReq) ProtoMessage()    {}
func (*AddChannelTagAdvReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{67}
}

func (m *AddChannelTagAdvReq) GetTagAdvList() []*ChannelTagAdv {
	if m != nil {
		return m.TagAdvList
	}
	return nil
}

func (m *AddChannelTagAdvReq) GetCleanOld() bool {
	if m != nil {
		return m.CleanOld
	}
	return false
}

func (m *AddChannelTagAdvReq) GetAdvType() uint32 {
	if m != nil {
		return m.AdvType
	}
	return 0
}

type AddChannelTagAdvResp struct {
}

func (m *AddChannelTagAdvResp) Reset()         { *m = AddChannelTagAdvResp{} }
func (m *AddChannelTagAdvResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelTagAdvResp) ProtoMessage()    {}
func (*AddChannelTagAdvResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{68}
}

type RemoveChannelTagAdvReq struct {
	TagAdv  *ChannelTagAdv `protobuf:"bytes,1,req,name=tag_adv,json=tagAdv" json:"tag_adv,omitempty"`
	AdvType uint32         `protobuf:"varint,2,opt,name=adv_type,json=advType" json:"adv_type"`
}

func (m *RemoveChannelTagAdvReq) Reset()         { *m = RemoveChannelTagAdvReq{} }
func (m *RemoveChannelTagAdvReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelTagAdvReq) ProtoMessage()    {}
func (*RemoveChannelTagAdvReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{69}
}

func (m *RemoveChannelTagAdvReq) GetTagAdv() *ChannelTagAdv {
	if m != nil {
		return m.TagAdv
	}
	return nil
}

func (m *RemoveChannelTagAdvReq) GetAdvType() uint32 {
	if m != nil {
		return m.AdvType
	}
	return 0
}

type RemoveChannelTagAdvResp struct {
}

func (m *RemoveChannelTagAdvResp) Reset()         { *m = RemoveChannelTagAdvResp{} }
func (m *RemoveChannelTagAdvResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelTagAdvResp) ProtoMessage()    {}
func (*RemoveChannelTagAdvResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{70}
}

type GetChannelTagAdvReq struct {
	AdvType uint32 `protobuf:"varint,1,opt,name=adv_type,json=advType" json:"adv_type"`
}

func (m *GetChannelTagAdvReq) Reset()         { *m = GetChannelTagAdvReq{} }
func (m *GetChannelTagAdvReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagAdvReq) ProtoMessage()    {}
func (*GetChannelTagAdvReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{71}
}

func (m *GetChannelTagAdvReq) GetAdvType() uint32 {
	if m != nil {
		return m.AdvType
	}
	return 0
}

type GetChannelTagAdvResp struct {
	TagAdvList []*ChannelTagAdv `protobuf:"bytes,1,rep,name=tag_adv_list,json=tagAdvList" json:"tag_adv_list,omitempty"`
}

func (m *GetChannelTagAdvResp) Reset()         { *m = GetChannelTagAdvResp{} }
func (m *GetChannelTagAdvResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTagAdvResp) ProtoMessage()    {}
func (*GetChannelTagAdvResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{72}
}

func (m *GetChannelTagAdvResp) GetTagAdvList() []*ChannelTagAdv {
	if m != nil {
		return m.TagAdvList
	}
	return nil
}

type TagTopChannel struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Score     uint32 `protobuf:"varint,2,req,name=score" json:"score"`
}

func (m *TagTopChannel) Reset()                    { *m = TagTopChannel{} }
func (m *TagTopChannel) String() string            { return proto.CompactTextString(m) }
func (*TagTopChannel) ProtoMessage()               {}
func (*TagTopChannel) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{73} }

func (m *TagTopChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TagTopChannel) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type AddTagTopChannelReq struct {
	TopChannelList []*TagTopChannel `protobuf:"bytes,1,rep,name=top_channel_list,json=topChannelList" json:"top_channel_list,omitempty"`
	TagId          uint32           `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *AddTagTopChannelReq) Reset()         { *m = AddTagTopChannelReq{} }
func (m *AddTagTopChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddTagTopChannelReq) ProtoMessage()    {}
func (*AddTagTopChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{74}
}

func (m *AddTagTopChannelReq) GetTopChannelList() []*TagTopChannel {
	if m != nil {
		return m.TopChannelList
	}
	return nil
}

func (m *AddTagTopChannelReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type AddTagTopChannelResp struct {
}

func (m *AddTagTopChannelResp) Reset()         { *m = AddTagTopChannelResp{} }
func (m *AddTagTopChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddTagTopChannelResp) ProtoMessage()    {}
func (*AddTagTopChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{75}
}

type GetTagTopChannelReq struct {
	TagId uint32 `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *GetTagTopChannelReq) Reset()         { *m = GetTagTopChannelReq{} }
func (m *GetTagTopChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetTagTopChannelReq) ProtoMessage()    {}
func (*GetTagTopChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{76}
}

func (m *GetTagTopChannelReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type GetTagTopChannelResp struct {
	TopChannelList []*TagTopChannel `protobuf:"bytes,1,rep,name=top_channel_list,json=topChannelList" json:"top_channel_list,omitempty"`
}

func (m *GetTagTopChannelResp) Reset()         { *m = GetTagTopChannelResp{} }
func (m *GetTagTopChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetTagTopChannelResp) ProtoMessage()    {}
func (*GetTagTopChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{77}
}

func (m *GetTagTopChannelResp) GetTopChannelList() []*TagTopChannel {
	if m != nil {
		return m.TopChannelList
	}
	return nil
}

type RemoveTagTopChannelReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *RemoveTagTopChannelReq) Reset()         { *m = RemoveTagTopChannelReq{} }
func (m *RemoveTagTopChannelReq) String() string { return proto.CompactTextString(m) }
func (*RemoveTagTopChannelReq) ProtoMessage()    {}
func (*RemoveTagTopChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{78}
}

func (m *RemoveTagTopChannelReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type RemoveTagTopChannelResp struct {
}

func (m *RemoveTagTopChannelResp) Reset()         { *m = RemoveTagTopChannelResp{} }
func (m *RemoveTagTopChannelResp) String() string { return proto.CompactTextString(m) }
func (*RemoveTagTopChannelResp) ProtoMessage()    {}
func (*RemoveTagTopChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{79}
}

type CommonRecommendChannel struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CommonRecommendChannel) Reset()         { *m = CommonRecommendChannel{} }
func (m *CommonRecommendChannel) String() string { return proto.CompactTextString(m) }
func (*CommonRecommendChannel) ProtoMessage()    {}
func (*CommonRecommendChannel) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{80}
}

func (m *CommonRecommendChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AddCommonRecommendReq struct {
	RecommendChannel *CommonRecommendChannel `protobuf:"bytes,1,req,name=recommend_channel,json=recommendChannel" json:"recommend_channel,omitempty"`
	FunType          uint32                  `protobuf:"varint,2,req,name=fun_type,json=funType" json:"fun_type"`
}

func (m *AddCommonRecommendReq) Reset()         { *m = AddCommonRecommendReq{} }
func (m *AddCommonRecommendReq) String() string { return proto.CompactTextString(m) }
func (*AddCommonRecommendReq) ProtoMessage()    {}
func (*AddCommonRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{81}
}

func (m *AddCommonRecommendReq) GetRecommendChannel() *CommonRecommendChannel {
	if m != nil {
		return m.RecommendChannel
	}
	return nil
}

func (m *AddCommonRecommendReq) GetFunType() uint32 {
	if m != nil {
		return m.FunType
	}
	return 0
}

type AddCommonRecommendResp struct {
}

func (m *AddCommonRecommendResp) Reset()         { *m = AddCommonRecommendResp{} }
func (m *AddCommonRecommendResp) String() string { return proto.CompactTextString(m) }
func (*AddCommonRecommendResp) ProtoMessage()    {}
func (*AddCommonRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{82}
}

type RemoveCommonRecommendReq struct {
	RecommendChannel *CommonRecommendChannel `protobuf:"bytes,1,req,name=recommend_channel,json=recommendChannel" json:"recommend_channel,omitempty"`
	FunType          uint32                  `protobuf:"varint,2,req,name=fun_type,json=funType" json:"fun_type"`
}

func (m *RemoveCommonRecommendReq) Reset()         { *m = RemoveCommonRecommendReq{} }
func (m *RemoveCommonRecommendReq) String() string { return proto.CompactTextString(m) }
func (*RemoveCommonRecommendReq) ProtoMessage()    {}
func (*RemoveCommonRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{83}
}

func (m *RemoveCommonRecommendReq) GetRecommendChannel() *CommonRecommendChannel {
	if m != nil {
		return m.RecommendChannel
	}
	return nil
}

func (m *RemoveCommonRecommendReq) GetFunType() uint32 {
	if m != nil {
		return m.FunType
	}
	return 0
}

type RemoveCommonRecommendResp struct {
}

func (m *RemoveCommonRecommendResp) Reset()         { *m = RemoveCommonRecommendResp{} }
func (m *RemoveCommonRecommendResp) String() string { return proto.CompactTextString(m) }
func (*RemoveCommonRecommendResp) ProtoMessage()    {}
func (*RemoveCommonRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{84}
}

type GetCommonRecommendReq struct {
	FunType uint32 `protobuf:"varint,1,req,name=fun_type,json=funType" json:"fun_type"`
}

func (m *GetCommonRecommendReq) Reset()         { *m = GetCommonRecommendReq{} }
func (m *GetCommonRecommendReq) String() string { return proto.CompactTextString(m) }
func (*GetCommonRecommendReq) ProtoMessage()    {}
func (*GetCommonRecommendReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{85}
}

func (m *GetCommonRecommendReq) GetFunType() uint32 {
	if m != nil {
		return m.FunType
	}
	return 0
}

type GetCommonRecommendResp struct {
	RecommendChannelList []*CommonRecommendChannel `protobuf:"bytes,1,rep,name=recommend_channel_list,json=recommendChannelList" json:"recommend_channel_list,omitempty"`
}

func (m *GetCommonRecommendResp) Reset()         { *m = GetCommonRecommendResp{} }
func (m *GetCommonRecommendResp) String() string { return proto.CompactTextString(m) }
func (*GetCommonRecommendResp) ProtoMessage()    {}
func (*GetCommonRecommendResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{86}
}

func (m *GetCommonRecommendResp) GetRecommendChannelList() []*CommonRecommendChannel {
	if m != nil {
		return m.RecommendChannelList
	}
	return nil
}

type RandomChannelInfo struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	BlockTag  uint32 `protobuf:"varint,2,req,name=block_tag,json=blockTag" json:"block_tag"`
}

func (m *RandomChannelInfo) Reset()         { *m = RandomChannelInfo{} }
func (m *RandomChannelInfo) String() string { return proto.CompactTextString(m) }
func (*RandomChannelInfo) ProtoMessage()    {}
func (*RandomChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{87}
}

func (m *RandomChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RandomChannelInfo) GetBlockTag() uint32 {
	if m != nil {
		return m.BlockTag
	}
	return 0
}

type AddRandomRecommendChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *AddRandomRecommendChannelReq) Reset()         { *m = AddRandomRecommendChannelReq{} }
func (m *AddRandomRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddRandomRecommendChannelReq) ProtoMessage()    {}
func (*AddRandomRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{88}
}

func (m *AddRandomRecommendChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AddRandomRecommendChannelResp struct {
}

func (m *AddRandomRecommendChannelResp) Reset()         { *m = AddRandomRecommendChannelResp{} }
func (m *AddRandomRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddRandomRecommendChannelResp) ProtoMessage()    {}
func (*AddRandomRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{89}
}

type GetRandomRecommendChannelReq struct {
	Count uint32 `protobuf:"varint,1,req,name=count" json:"count"`
}

func (m *GetRandomRecommendChannelReq) Reset()         { *m = GetRandomRecommendChannelReq{} }
func (m *GetRandomRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetRandomRecommendChannelReq) ProtoMessage()    {}
func (*GetRandomRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{90}
}

func (m *GetRandomRecommendChannelReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetRandomRecommendChannelResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetRandomRecommendChannelResp) Reset()         { *m = GetRandomRecommendChannelResp{} }
func (m *GetRandomRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetRandomRecommendChannelResp) ProtoMessage()    {}
func (*GetRandomRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{91}
}

func (m *GetRandomRecommendChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type RemoveRandomRecommendChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *RemoveRandomRecommendChannelReq) Reset()         { *m = RemoveRandomRecommendChannelReq{} }
func (m *RemoveRandomRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*RemoveRandomRecommendChannelReq) ProtoMessage()    {}
func (*RemoveRandomRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{92}
}

func (m *RemoveRandomRecommendChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RemoveRandomRecommendChannelResp struct {
}

func (m *RemoveRandomRecommendChannelResp) Reset()         { *m = RemoveRandomRecommendChannelResp{} }
func (m *RemoveRandomRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*RemoveRandomRecommendChannelResp) ProtoMessage()    {}
func (*RemoveRandomRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{93}
}

type GetChannelTypeByTagReq struct {
	TagId     uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId" json:"tag_id"`
	ChannelId uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelTypeByTagReq) Reset()         { *m = GetChannelTypeByTagReq{} }
func (m *GetChannelTypeByTagReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelTypeByTagReq) ProtoMessage()    {}
func (*GetChannelTypeByTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{94}
}

func (m *GetChannelTypeByTagReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *GetChannelTypeByTagReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelTypeByTagResp struct {
	TagType uint32 `protobuf:"varint,1,req,name=tag_type,json=tagType" json:"tag_type"`
	TagId   uint32 `protobuf:"varint,2,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *GetChannelTypeByTagResp) Reset()         { *m = GetChannelTypeByTagResp{} }
func (m *GetChannelTypeByTagResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelTypeByTagResp) ProtoMessage()    {}
func (*GetChannelTypeByTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{95}
}

func (m *GetChannelTypeByTagResp) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *GetChannelTypeByTagResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type GetTagInfoReq struct {
	TagId uint32 `protobuf:"varint,1,req,name=tag_id,json=tagId" json:"tag_id"`
}

func (m *GetTagInfoReq) Reset()                    { *m = GetTagInfoReq{} }
func (m *GetTagInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetTagInfoReq) ProtoMessage()               {}
func (*GetTagInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{96} }

func (m *GetTagInfoReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

type GetTagInfoResp struct {
	SubTagInfo *SubTag `protobuf:"bytes,1,req,name=sub_tag_info,json=subTagInfo" json:"sub_tag_info,omitempty"`
}

func (m *GetTagInfoResp) Reset()                    { *m = GetTagInfoResp{} }
func (m *GetTagInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetTagInfoResp) ProtoMessage()               {}
func (*GetTagInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{97} }

func (m *GetTagInfoResp) GetSubTagInfo() *SubTag {
	if m != nil {
		return m.SubTagInfo
	}
	return nil
}

type GetMajorLiveChannelReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetMajorLiveChannelReq) Reset()         { *m = GetMajorLiveChannelReq{} }
func (m *GetMajorLiveChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetMajorLiveChannelReq) ProtoMessage()    {}
func (*GetMajorLiveChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{98}
}

func (m *GetMajorLiveChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMajorLiveChannelResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetMajorLiveChannelResp) Reset()         { *m = GetMajorLiveChannelResp{} }
func (m *GetMajorLiveChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetMajorLiveChannelResp) ProtoMessage()    {}
func (*GetMajorLiveChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{99}
}

func (m *GetMajorLiveChannelResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type LiveChannelEx struct {
	ChannelId      uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ScoreIdx       uint32   `protobuf:"varint,2,req,name=score_idx,json=scoreIdx" json:"score_idx"`
	HasAdmin       uint32   `protobuf:"varint,3,opt,name=has_admin,json=hasAdmin" json:"has_admin"`
	HoursToAddList []uint32 `protobuf:"varint,4,rep,name=hours_to_add_list,json=hoursToAddList" json:"hours_to_add_list,omitempty"`
}

func (m *LiveChannelEx) Reset()                    { *m = LiveChannelEx{} }
func (m *LiveChannelEx) String() string            { return proto.CompactTextString(m) }
func (*LiveChannelEx) ProtoMessage()               {}
func (*LiveChannelEx) Descriptor() ([]byte, []int) { return fileDescriptorChannelrecommend, []int{100} }

func (m *LiveChannelEx) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LiveChannelEx) GetScoreIdx() uint32 {
	if m != nil {
		return m.ScoreIdx
	}
	return 0
}

func (m *LiveChannelEx) GetHasAdmin() uint32 {
	if m != nil {
		return m.HasAdmin
	}
	return 0
}

func (m *LiveChannelEx) GetHoursToAddList() []uint32 {
	if m != nil {
		return m.HoursToAddList
	}
	return nil
}

type AddLiveRecommendChannelReq struct {
	ChannelId      uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ScoreIdx       uint32   `protobuf:"varint,2,req,name=score_idx,json=scoreIdx" json:"score_idx"`
	HoursToAddList []uint32 `protobuf:"varint,3,rep,name=hours_to_add_list,json=hoursToAddList" json:"hours_to_add_list,omitempty"`
}

func (m *AddLiveRecommendChannelReq) Reset()         { *m = AddLiveRecommendChannelReq{} }
func (m *AddLiveRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*AddLiveRecommendChannelReq) ProtoMessage()    {}
func (*AddLiveRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{101}
}

func (m *AddLiveRecommendChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddLiveRecommendChannelReq) GetScoreIdx() uint32 {
	if m != nil {
		return m.ScoreIdx
	}
	return 0
}

func (m *AddLiveRecommendChannelReq) GetHoursToAddList() []uint32 {
	if m != nil {
		return m.HoursToAddList
	}
	return nil
}

type AddLiveRecommendChannelResp struct {
}

func (m *AddLiveRecommendChannelResp) Reset()         { *m = AddLiveRecommendChannelResp{} }
func (m *AddLiveRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*AddLiveRecommendChannelResp) ProtoMessage()    {}
func (*AddLiveRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{102}
}

type DelLiveRecommendChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *DelLiveRecommendChannelReq) Reset()         { *m = DelLiveRecommendChannelReq{} }
func (m *DelLiveRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*DelLiveRecommendChannelReq) ProtoMessage()    {}
func (*DelLiveRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{103}
}

func (m *DelLiveRecommendChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelLiveRecommendChannelResp struct {
}

func (m *DelLiveRecommendChannelResp) Reset()         { *m = DelLiveRecommendChannelResp{} }
func (m *DelLiveRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*DelLiveRecommendChannelResp) ProtoMessage()    {}
func (*DelLiveRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{104}
}

type GetLiveRecommendChannelReq struct {
}

func (m *GetLiveRecommendChannelReq) Reset()         { *m = GetLiveRecommendChannelReq{} }
func (m *GetLiveRecommendChannelReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveRecommendChannelReq) ProtoMessage()    {}
func (*GetLiveRecommendChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{105}
}

type GetLiveRecommendChannelResp struct {
	ChannelExList []*LiveChannelEx `protobuf:"bytes,1,rep,name=channel_ex_list,json=channelExList" json:"channel_ex_list,omitempty"`
}

func (m *GetLiveRecommendChannelResp) Reset()         { *m = GetLiveRecommendChannelResp{} }
func (m *GetLiveRecommendChannelResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveRecommendChannelResp) ProtoMessage()    {}
func (*GetLiveRecommendChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{106}
}

func (m *GetLiveRecommendChannelResp) GetChannelExList() []*LiveChannelEx {
	if m != nil {
		return m.ChannelExList
	}
	return nil
}

type GetLiveChannelListReq struct {
	Start uint32 `protobuf:"varint,1,req,name=start" json:"start"`
	Count uint32 `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *GetLiveChannelListReq) Reset()         { *m = GetLiveChannelListReq{} }
func (m *GetLiveChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelListReq) ProtoMessage()    {}
func (*GetLiveChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{107}
}

func (m *GetLiveChannelListReq) GetStart() uint32 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *GetLiveChannelListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetLiveChannelListResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
	ReachEnd      bool     `protobuf:"varint,2,req,name=reach_end,json=reachEnd" json:"reach_end"`
	UserCount     uint32   `protobuf:"varint,3,opt,name=user_count,json=userCount" json:"user_count"`
}

func (m *GetLiveChannelListResp) Reset()         { *m = GetLiveChannelListResp{} }
func (m *GetLiveChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveChannelListResp) ProtoMessage()    {}
func (*GetLiveChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelrecommend, []int{108}
}

func (m *GetLiveChannelListResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetLiveChannelListResp) GetReachEnd() bool {
	if m != nil {
		return m.ReachEnd
	}
	return false
}

func (m *GetLiveChannelListResp) GetUserCount() uint32 {
	if m != nil {
		return m.UserCount
	}
	return 0
}

func init() {
	proto.RegisterType((*UserEnterChannelReq)(nil), "channelrecommend.UserEnterChannelReq")
	proto.RegisterType((*UserEnterChannelResp)(nil), "channelrecommend.UserEnterChannelResp")
	proto.RegisterType((*AddChannelGiftReq)(nil), "channelrecommend.AddChannelGiftReq")
	proto.RegisterType((*AddChannelGiftResp)(nil), "channelrecommend.AddChannelGiftResp")
	proto.RegisterType((*GetRecommendChannelReq)(nil), "channelrecommend.GetRecommendChannelReq")
	proto.RegisterType((*ChannelInfo)(nil), "channelrecommend.ChannelInfo")
	proto.RegisterType((*GetRecommendChannelResp)(nil), "channelrecommend.GetRecommendChannelResp")
	proto.RegisterType((*GetChannelTagColorReq)(nil), "channelrecommend.GetChannelTagColorReq")
	proto.RegisterType((*GetChannelTagColorResp)(nil), "channelrecommend.GetChannelTagColorResp")
	proto.RegisterType((*AddChannelReq)(nil), "channelrecommend.AddChannelReq")
	proto.RegisterType((*AddChannelResp)(nil), "channelrecommend.AddChannelResp")
	proto.RegisterType((*GetChannelReq)(nil), "channelrecommend.GetChannelReq")
	proto.RegisterType((*GetChannelResp)(nil), "channelrecommend.GetChannelResp")
	proto.RegisterType((*ChannelTypeInfo)(nil), "channelrecommend.ChannelTypeInfo")
	proto.RegisterType((*BatchGetChannelTypeReq)(nil), "channelrecommend.BatchGetChannelTypeReq")
	proto.RegisterType((*BatchGetChannelTypeResp)(nil), "channelrecommend.BatchGetChannelTypeResp")
	proto.RegisterType((*ChannelInfoEx)(nil), "channelrecommend.ChannelInfoEx")
	proto.RegisterType((*SetChannelInfoExReq)(nil), "channelrecommend.SetChannelInfoExReq")
	proto.RegisterType((*SetChannelInfoExResp)(nil), "channelrecommend.SetChannelInfoExResp")
	proto.RegisterType((*GetChannelInfoExReq)(nil), "channelrecommend.GetChannelInfoExReq")
	proto.RegisterType((*GetChannelInfoExResp)(nil), "channelrecommend.GetChannelInfoExResp")
	proto.RegisterType((*RemoveChannelReq)(nil), "channelrecommend.RemoveChannelReq")
	proto.RegisterType((*RemoveChannelResp)(nil), "channelrecommend.RemoveChannelResp")
	proto.RegisterType((*RemoveChannelByTypeReq)(nil), "channelrecommend.RemoveChannelByTypeReq")
	proto.RegisterType((*RemoveChannelByTypeResp)(nil), "channelrecommend.RemoveChannelByTypeResp")
	proto.RegisterType((*AddUserLikeChannelReq)(nil), "channelrecommend.AddUserLikeChannelReq")
	proto.RegisterType((*AddUserLikeChannelResp)(nil), "channelrecommend.AddUserLikeChannelResp")
	proto.RegisterType((*GetChannelInfoTagReq)(nil), "channelrecommend.GetChannelInfoTagReq")
	proto.RegisterType((*ChannelInfoTag)(nil), "channelrecommend.ChannelInfoTag")
	proto.RegisterType((*GetChannelInfoTagResp)(nil), "channelrecommend.GetChannelInfoTagResp")
	proto.RegisterType((*GetHotChannelReq)(nil), "channelrecommend.GetHotChannelReq")
	proto.RegisterType((*ChannelHotInfo)(nil), "channelrecommend.ChannelHotInfo")
	proto.RegisterType((*GetHotChannelResp)(nil), "channelrecommend.GetHotChannelResp")
	proto.RegisterType((*SetHotChannelCfgReq)(nil), "channelrecommend.SetHotChannelCfgReq")
	proto.RegisterType((*GetHotChannelCfgReq)(nil), "channelrecommend.GetHotChannelCfgReq")
	proto.RegisterType((*GetHotChannelCfgResp)(nil), "channelrecommend.GetHotChannelCfgResp")
	proto.RegisterType((*SetHotChannelTypeReq)(nil), "channelrecommend.SetHotChannelTypeReq")
	proto.RegisterType((*HotChannelInfo)(nil), "channelrecommend.HotChannelInfo")
	proto.RegisterType((*BatchGetHotChannelTypeReq)(nil), "channelrecommend.BatchGetHotChannelTypeReq")
	proto.RegisterType((*BatchGetHotChannelTypeResp)(nil), "channelrecommend.BatchGetHotChannelTypeResp")
	proto.RegisterType((*GetHotChannelListByTypeReq)(nil), "channelrecommend.GetHotChannelListByTypeReq")
	proto.RegisterType((*GetHotChannelListByTypeResp)(nil), "channelrecommend.GetHotChannelListByTypeResp")
	proto.RegisterType((*BatchDelHotChannelReq)(nil), "channelrecommend.BatchDelHotChannelReq")
	proto.RegisterType((*ColorInfo)(nil), "channelrecommend.ColorInfo")
	proto.RegisterType((*AddColorReq)(nil), "channelrecommend.AddColorReq")
	proto.RegisterType((*AddColorResp)(nil), "channelrecommend.AddColorResp")
	proto.RegisterType((*GetAllColorReq)(nil), "channelrecommend.GetAllColorReq")
	proto.RegisterType((*GetAllColorResp)(nil), "channelrecommend.GetAllColorResp")
	proto.RegisterType((*CardInfo)(nil), "channelrecommend.CardInfo")
	proto.RegisterType((*RootTag)(nil), "channelrecommend.RootTag")
	proto.RegisterType((*SubTag)(nil), "channelrecommend.SubTag")
	proto.RegisterType((*TagTypes)(nil), "channelrecommend.TagTypes")
	proto.RegisterType((*GetAllTagTypeReq)(nil), "channelrecommend.GetAllTagTypeReq")
	proto.RegisterType((*GetAllTagTypeResp)(nil), "channelrecommend.GetAllTagTypeResp")
	proto.RegisterType((*GetChannelByTagReq)(nil), "channelrecommend.GetChannelByTagReq")
	proto.RegisterType((*GetChannelByTagResp)(nil), "channelrecommend.GetChannelByTagResp")
	proto.RegisterType((*GetChannelCardReq)(nil), "channelrecommend.GetChannelCardReq")
	proto.RegisterType((*GetChannelCardResp)(nil), "channelrecommend.GetChannelCardResp")
	proto.RegisterType((*RefreshChannelTimeReq)(nil), "channelrecommend.RefreshChannelTimeReq")
	proto.RegisterType((*RefreshChannelTimeResp)(nil), "channelrecommend.RefreshChannelTimeResp")
	proto.RegisterType((*GetChannelRefreshCDReq)(nil), "channelrecommend.GetChannelRefreshCDReq")
	proto.RegisterType((*GetChannelRefreshCDResp)(nil), "channelrecommend.GetChannelRefreshCDResp")
	proto.RegisterType((*SetChannelTagIdReq)(nil), "channelrecommend.SetChannelTagIdReq")
	proto.RegisterType((*SetChannelTagIdResp)(nil), "channelrecommend.SetChannelTagIdResp")
	proto.RegisterType((*GetChannelTagIdReq)(nil), "channelrecommend.GetChannelTagIdReq")
	proto.RegisterType((*GetChannelTagIdResp)(nil), "channelrecommend.GetChannelTagIdResp")
	proto.RegisterType((*ChannelTagAdv)(nil), "channelrecommend.ChannelTagAdv")
	proto.RegisterType((*AddChannelTagAdvReq)(nil), "channelrecommend.AddChannelTagAdvReq")
	proto.RegisterType((*AddChannelTagAdvResp)(nil), "channelrecommend.AddChannelTagAdvResp")
	proto.RegisterType((*RemoveChannelTagAdvReq)(nil), "channelrecommend.RemoveChannelTagAdvReq")
	proto.RegisterType((*RemoveChannelTagAdvResp)(nil), "channelrecommend.RemoveChannelTagAdvResp")
	proto.RegisterType((*GetChannelTagAdvReq)(nil), "channelrecommend.GetChannelTagAdvReq")
	proto.RegisterType((*GetChannelTagAdvResp)(nil), "channelrecommend.GetChannelTagAdvResp")
	proto.RegisterType((*TagTopChannel)(nil), "channelrecommend.TagTopChannel")
	proto.RegisterType((*AddTagTopChannelReq)(nil), "channelrecommend.AddTagTopChannelReq")
	proto.RegisterType((*AddTagTopChannelResp)(nil), "channelrecommend.AddTagTopChannelResp")
	proto.RegisterType((*GetTagTopChannelReq)(nil), "channelrecommend.GetTagTopChannelReq")
	proto.RegisterType((*GetTagTopChannelResp)(nil), "channelrecommend.GetTagTopChannelResp")
	proto.RegisterType((*RemoveTagTopChannelReq)(nil), "channelrecommend.RemoveTagTopChannelReq")
	proto.RegisterType((*RemoveTagTopChannelResp)(nil), "channelrecommend.RemoveTagTopChannelResp")
	proto.RegisterType((*CommonRecommendChannel)(nil), "channelrecommend.CommonRecommendChannel")
	proto.RegisterType((*AddCommonRecommendReq)(nil), "channelrecommend.AddCommonRecommendReq")
	proto.RegisterType((*AddCommonRecommendResp)(nil), "channelrecommend.AddCommonRecommendResp")
	proto.RegisterType((*RemoveCommonRecommendReq)(nil), "channelrecommend.RemoveCommonRecommendReq")
	proto.RegisterType((*RemoveCommonRecommendResp)(nil), "channelrecommend.RemoveCommonRecommendResp")
	proto.RegisterType((*GetCommonRecommendReq)(nil), "channelrecommend.GetCommonRecommendReq")
	proto.RegisterType((*GetCommonRecommendResp)(nil), "channelrecommend.GetCommonRecommendResp")
	proto.RegisterType((*RandomChannelInfo)(nil), "channelrecommend.RandomChannelInfo")
	proto.RegisterType((*AddRandomRecommendChannelReq)(nil), "channelrecommend.AddRandomRecommendChannelReq")
	proto.RegisterType((*AddRandomRecommendChannelResp)(nil), "channelrecommend.AddRandomRecommendChannelResp")
	proto.RegisterType((*GetRandomRecommendChannelReq)(nil), "channelrecommend.GetRandomRecommendChannelReq")
	proto.RegisterType((*GetRandomRecommendChannelResp)(nil), "channelrecommend.GetRandomRecommendChannelResp")
	proto.RegisterType((*RemoveRandomRecommendChannelReq)(nil), "channelrecommend.RemoveRandomRecommendChannelReq")
	proto.RegisterType((*RemoveRandomRecommendChannelResp)(nil), "channelrecommend.RemoveRandomRecommendChannelResp")
	proto.RegisterType((*GetChannelTypeByTagReq)(nil), "channelrecommend.GetChannelTypeByTagReq")
	proto.RegisterType((*GetChannelTypeByTagResp)(nil), "channelrecommend.GetChannelTypeByTagResp")
	proto.RegisterType((*GetTagInfoReq)(nil), "channelrecommend.GetTagInfoReq")
	proto.RegisterType((*GetTagInfoResp)(nil), "channelrecommend.GetTagInfoResp")
	proto.RegisterType((*GetMajorLiveChannelReq)(nil), "channelrecommend.GetMajorLiveChannelReq")
	proto.RegisterType((*GetMajorLiveChannelResp)(nil), "channelrecommend.GetMajorLiveChannelResp")
	proto.RegisterType((*LiveChannelEx)(nil), "channelrecommend.LiveChannelEx")
	proto.RegisterType((*AddLiveRecommendChannelReq)(nil), "channelrecommend.AddLiveRecommendChannelReq")
	proto.RegisterType((*AddLiveRecommendChannelResp)(nil), "channelrecommend.AddLiveRecommendChannelResp")
	proto.RegisterType((*DelLiveRecommendChannelReq)(nil), "channelrecommend.DelLiveRecommendChannelReq")
	proto.RegisterType((*DelLiveRecommendChannelResp)(nil), "channelrecommend.DelLiveRecommendChannelResp")
	proto.RegisterType((*GetLiveRecommendChannelReq)(nil), "channelrecommend.GetLiveRecommendChannelReq")
	proto.RegisterType((*GetLiveRecommendChannelResp)(nil), "channelrecommend.GetLiveRecommendChannelResp")
	proto.RegisterType((*GetLiveChannelListReq)(nil), "channelrecommend.GetLiveChannelListReq")
	proto.RegisterType((*GetLiveChannelListResp)(nil), "channelrecommend.GetLiveChannelListResp")
	proto.RegisterEnum("channelrecommend.AddChannelType", AddChannelType_name, AddChannelType_value)
	proto.RegisterEnum("channelrecommend.ChannelInfoExFlag", ChannelInfoExFlag_name, ChannelInfoExFlag_value)
	proto.RegisterEnum("channelrecommend.HotChannelType", HotChannelType_name, HotChannelType_value)
	proto.RegisterEnum("channelrecommend.RootTagType", RootTagType_name, RootTagType_value)
	proto.RegisterEnum("channelrecommend.ADV_TYPE", ADV_TYPE_name, ADV_TYPE_value)
	proto.RegisterEnum("channelrecommend.RecommendChannelType", RecommendChannelType_name, RecommendChannelType_value)
	proto.RegisterEnum("channelrecommend.SubTag_SubTagFlag", SubTag_SubTagFlag_name, SubTag_SubTagFlag_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelRecommend service

type ChannelRecommendClient interface {
	UserEnterChannel(ctx context.Context, in *UserEnterChannelReq, opts ...grpc.CallOption) (*UserEnterChannelResp, error)
	GetRecommendChannel(ctx context.Context, in *GetRecommendChannelReq, opts ...grpc.CallOption) (*GetRecommendChannelResp, error)
	AddChannel(ctx context.Context, in *AddChannelReq, opts ...grpc.CallOption) (*AddChannelResp, error)
	GetChannel(ctx context.Context, in *GetChannelReq, opts ...grpc.CallOption) (*GetChannelResp, error)
	AddChannelGift(ctx context.Context, in *AddChannelGiftReq, opts ...grpc.CallOption) (*AddChannelGiftResp, error)
	SetChannelInfoEx(ctx context.Context, in *SetChannelInfoExReq, opts ...grpc.CallOption) (*SetChannelInfoExResp, error)
	GetChannelInfoEx(ctx context.Context, in *GetChannelInfoExReq, opts ...grpc.CallOption) (*GetChannelInfoExResp, error)
	RemoveChannel(ctx context.Context, in *RemoveChannelReq, opts ...grpc.CallOption) (*RemoveChannelResp, error)
	GetHotChannel(ctx context.Context, in *GetHotChannelReq, opts ...grpc.CallOption) (*GetHotChannelResp, error)
	AddUserLikeChannel(ctx context.Context, in *AddUserLikeChannelReq, opts ...grpc.CallOption) (*AddUserLikeChannelResp, error)
	GetChannelInfoTag(ctx context.Context, in *GetChannelInfoTagReq, opts ...grpc.CallOption) (*GetChannelInfoTagResp, error)
	SetHotChannelCfg(ctx context.Context, in *SetHotChannelCfgReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetHotChannelCfg(ctx context.Context, in *GetHotChannelCfgReq, opts ...grpc.CallOption) (*GetHotChannelCfgResp, error)
	BatchGetChannelType(ctx context.Context, in *BatchGetChannelTypeReq, opts ...grpc.CallOption) (*BatchGetChannelTypeResp, error)
	// 废弃
	AddColor(ctx context.Context, in *AddColorReq, opts ...grpc.CallOption) (*AddColorResp, error)
	// 废弃
	GetAllColor(ctx context.Context, in *GetAllColorReq, opts ...grpc.CallOption) (*GetAllColorResp, error)
	SetHotChannelType(ctx context.Context, in *SetHotChannelTypeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatchGetHotChannelType(ctx context.Context, in *BatchGetHotChannelTypeReq, opts ...grpc.CallOption) (*BatchGetHotChannelTypeResp, error)
	GetHotChannelListByType(ctx context.Context, in *GetHotChannelListByTypeReq, opts ...grpc.CallOption) (*GetHotChannelListByTypeResp, error)
	GetAllTagType(ctx context.Context, in *GetAllTagTypeReq, opts ...grpc.CallOption) (*GetAllTagTypeResp, error)
	GetChannelCard(ctx context.Context, in *GetChannelCardReq, opts ...grpc.CallOption) (*GetChannelCardResp, error)
	GetChannelByTag(ctx context.Context, in *GetChannelByTagReq, opts ...grpc.CallOption) (*GetChannelByTagResp, error)
	RefreshChannelTime(ctx context.Context, in *RefreshChannelTimeReq, opts ...grpc.CallOption) (*RefreshChannelTimeResp, error)
	SetChannelTagId(ctx context.Context, in *SetChannelTagIdReq, opts ...grpc.CallOption) (*SetChannelTagIdResp, error)
	GetChannelTagId(ctx context.Context, in *GetChannelTagIdReq, opts ...grpc.CallOption) (*GetChannelTagIdResp, error)
	AddChannelTagAdv(ctx context.Context, in *AddChannelTagAdvReq, opts ...grpc.CallOption) (*AddChannelTagAdvResp, error)
	RemoveChannelTagAdv(ctx context.Context, in *RemoveChannelTagAdvReq, opts ...grpc.CallOption) (*RemoveChannelTagAdvResp, error)
	GetChannelTagAdv(ctx context.Context, in *GetChannelTagAdvReq, opts ...grpc.CallOption) (*GetChannelTagAdvResp, error)
	GetChannelRefreshCD(ctx context.Context, in *GetChannelRefreshCDReq, opts ...grpc.CallOption) (*GetChannelRefreshCDResp, error)
	GetChannelTagColor(ctx context.Context, in *GetChannelTagColorReq, opts ...grpc.CallOption) (*GetChannelTagColorResp, error)
	AddTagTopChannel(ctx context.Context, in *AddTagTopChannelReq, opts ...grpc.CallOption) (*AddTagTopChannelResp, error)
	GetTagTopChannel(ctx context.Context, in *GetTagTopChannelReq, opts ...grpc.CallOption) (*GetTagTopChannelResp, error)
	RemoveTagTopChannel(ctx context.Context, in *RemoveTagTopChannelReq, opts ...grpc.CallOption) (*RemoveTagTopChannelResp, error)
	AddCommonRecommend(ctx context.Context, in *AddCommonRecommendReq, opts ...grpc.CallOption) (*AddCommonRecommendResp, error)
	RemoveCommonRecommend(ctx context.Context, in *RemoveCommonRecommendReq, opts ...grpc.CallOption) (*RemoveCommonRecommendResp, error)
	GetCommonRecommend(ctx context.Context, in *GetCommonRecommendReq, opts ...grpc.CallOption) (*GetCommonRecommendResp, error)
	AddRandomRecommendChannel(ctx context.Context, in *AddRandomRecommendChannelReq, opts ...grpc.CallOption) (*AddRandomRecommendChannelResp, error)
	GetRandomRecommendChannel(ctx context.Context, in *GetRandomRecommendChannelReq, opts ...grpc.CallOption) (*GetRandomRecommendChannelResp, error)
	RemoveRandomRecommendChannel(ctx context.Context, in *RemoveRandomRecommendChannelReq, opts ...grpc.CallOption) (*RemoveRandomRecommendChannelResp, error)
	BatchDelHotChannel(ctx context.Context, in *BatchDelHotChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelTypeByTag(ctx context.Context, in *GetChannelTypeByTagReq, opts ...grpc.CallOption) (*GetChannelTypeByTagResp, error)
	GetTagInfo(ctx context.Context, in *GetTagInfoReq, opts ...grpc.CallOption) (*GetTagInfoResp, error)
	GetMajorLiveChannel(ctx context.Context, in *GetMajorLiveChannelReq, opts ...grpc.CallOption) (*GetMajorLiveChannelResp, error)
	AddLiveRecommendChannel(ctx context.Context, in *AddLiveRecommendChannelReq, opts ...grpc.CallOption) (*AddLiveRecommendChannelResp, error)
	DelLiveRecommendChannel(ctx context.Context, in *DelLiveRecommendChannelReq, opts ...grpc.CallOption) (*DelLiveRecommendChannelResp, error)
	GetLiveRecommendChannel(ctx context.Context, in *GetLiveRecommendChannelReq, opts ...grpc.CallOption) (*GetLiveRecommendChannelResp, error)
	GetLiveChannelList(ctx context.Context, in *GetLiveChannelListReq, opts ...grpc.CallOption) (*GetLiveChannelListResp, error)
	RemoveChannelByType(ctx context.Context, in *RemoveChannelByTypeReq, opts ...grpc.CallOption) (*RemoveChannelByTypeResp, error)
	SetRecommendChannelTag(ctx context.Context, in *SetChannelInfoExReq, opts ...grpc.CallOption) (*SetChannelInfoExResp, error)
}

type channelRecommendClient struct {
	cc *grpc.ClientConn
}

func NewChannelRecommendClient(cc *grpc.ClientConn) ChannelRecommendClient {
	return &channelRecommendClient{cc}
}

func (c *channelRecommendClient) UserEnterChannel(ctx context.Context, in *UserEnterChannelReq, opts ...grpc.CallOption) (*UserEnterChannelResp, error) {
	out := new(UserEnterChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/UserEnterChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetRecommendChannel(ctx context.Context, in *GetRecommendChannelReq, opts ...grpc.CallOption) (*GetRecommendChannelResp, error) {
	out := new(GetRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddChannel(ctx context.Context, in *AddChannelReq, opts ...grpc.CallOption) (*AddChannelResp, error) {
	out := new(AddChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannel(ctx context.Context, in *GetChannelReq, opts ...grpc.CallOption) (*GetChannelResp, error) {
	out := new(GetChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddChannelGift(ctx context.Context, in *AddChannelGiftReq, opts ...grpc.CallOption) (*AddChannelGiftResp, error) {
	out := new(AddChannelGiftResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddChannelGift", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) SetChannelInfoEx(ctx context.Context, in *SetChannelInfoExReq, opts ...grpc.CallOption) (*SetChannelInfoExResp, error) {
	out := new(SetChannelInfoExResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/SetChannelInfoEx", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelInfoEx(ctx context.Context, in *GetChannelInfoExReq, opts ...grpc.CallOption) (*GetChannelInfoExResp, error) {
	out := new(GetChannelInfoExResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelInfoEx", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RemoveChannel(ctx context.Context, in *RemoveChannelReq, opts ...grpc.CallOption) (*RemoveChannelResp, error) {
	out := new(RemoveChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RemoveChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetHotChannel(ctx context.Context, in *GetHotChannelReq, opts ...grpc.CallOption) (*GetHotChannelResp, error) {
	out := new(GetHotChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetHotChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddUserLikeChannel(ctx context.Context, in *AddUserLikeChannelReq, opts ...grpc.CallOption) (*AddUserLikeChannelResp, error) {
	out := new(AddUserLikeChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddUserLikeChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelInfoTag(ctx context.Context, in *GetChannelInfoTagReq, opts ...grpc.CallOption) (*GetChannelInfoTagResp, error) {
	out := new(GetChannelInfoTagResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelInfoTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) SetHotChannelCfg(ctx context.Context, in *SetHotChannelCfgReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/SetHotChannelCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetHotChannelCfg(ctx context.Context, in *GetHotChannelCfgReq, opts ...grpc.CallOption) (*GetHotChannelCfgResp, error) {
	out := new(GetHotChannelCfgResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetHotChannelCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) BatchGetChannelType(ctx context.Context, in *BatchGetChannelTypeReq, opts ...grpc.CallOption) (*BatchGetChannelTypeResp, error) {
	out := new(BatchGetChannelTypeResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/BatchGetChannelType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddColor(ctx context.Context, in *AddColorReq, opts ...grpc.CallOption) (*AddColorResp, error) {
	out := new(AddColorResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddColor", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetAllColor(ctx context.Context, in *GetAllColorReq, opts ...grpc.CallOption) (*GetAllColorResp, error) {
	out := new(GetAllColorResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetAllColor", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) SetHotChannelType(ctx context.Context, in *SetHotChannelTypeReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/SetHotChannelType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) BatchGetHotChannelType(ctx context.Context, in *BatchGetHotChannelTypeReq, opts ...grpc.CallOption) (*BatchGetHotChannelTypeResp, error) {
	out := new(BatchGetHotChannelTypeResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/BatchGetHotChannelType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetHotChannelListByType(ctx context.Context, in *GetHotChannelListByTypeReq, opts ...grpc.CallOption) (*GetHotChannelListByTypeResp, error) {
	out := new(GetHotChannelListByTypeResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetHotChannelListByType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetAllTagType(ctx context.Context, in *GetAllTagTypeReq, opts ...grpc.CallOption) (*GetAllTagTypeResp, error) {
	out := new(GetAllTagTypeResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetAllTagType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelCard(ctx context.Context, in *GetChannelCardReq, opts ...grpc.CallOption) (*GetChannelCardResp, error) {
	out := new(GetChannelCardResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelCard", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelByTag(ctx context.Context, in *GetChannelByTagReq, opts ...grpc.CallOption) (*GetChannelByTagResp, error) {
	out := new(GetChannelByTagResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelByTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RefreshChannelTime(ctx context.Context, in *RefreshChannelTimeReq, opts ...grpc.CallOption) (*RefreshChannelTimeResp, error) {
	out := new(RefreshChannelTimeResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RefreshChannelTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) SetChannelTagId(ctx context.Context, in *SetChannelTagIdReq, opts ...grpc.CallOption) (*SetChannelTagIdResp, error) {
	out := new(SetChannelTagIdResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/SetChannelTagId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelTagId(ctx context.Context, in *GetChannelTagIdReq, opts ...grpc.CallOption) (*GetChannelTagIdResp, error) {
	out := new(GetChannelTagIdResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelTagId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddChannelTagAdv(ctx context.Context, in *AddChannelTagAdvReq, opts ...grpc.CallOption) (*AddChannelTagAdvResp, error) {
	out := new(AddChannelTagAdvResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddChannelTagAdv", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RemoveChannelTagAdv(ctx context.Context, in *RemoveChannelTagAdvReq, opts ...grpc.CallOption) (*RemoveChannelTagAdvResp, error) {
	out := new(RemoveChannelTagAdvResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RemoveChannelTagAdv", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelTagAdv(ctx context.Context, in *GetChannelTagAdvReq, opts ...grpc.CallOption) (*GetChannelTagAdvResp, error) {
	out := new(GetChannelTagAdvResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelTagAdv", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelRefreshCD(ctx context.Context, in *GetChannelRefreshCDReq, opts ...grpc.CallOption) (*GetChannelRefreshCDResp, error) {
	out := new(GetChannelRefreshCDResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelRefreshCD", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelTagColor(ctx context.Context, in *GetChannelTagColorReq, opts ...grpc.CallOption) (*GetChannelTagColorResp, error) {
	out := new(GetChannelTagColorResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelTagColor", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddTagTopChannel(ctx context.Context, in *AddTagTopChannelReq, opts ...grpc.CallOption) (*AddTagTopChannelResp, error) {
	out := new(AddTagTopChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddTagTopChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetTagTopChannel(ctx context.Context, in *GetTagTopChannelReq, opts ...grpc.CallOption) (*GetTagTopChannelResp, error) {
	out := new(GetTagTopChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetTagTopChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RemoveTagTopChannel(ctx context.Context, in *RemoveTagTopChannelReq, opts ...grpc.CallOption) (*RemoveTagTopChannelResp, error) {
	out := new(RemoveTagTopChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RemoveTagTopChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddCommonRecommend(ctx context.Context, in *AddCommonRecommendReq, opts ...grpc.CallOption) (*AddCommonRecommendResp, error) {
	out := new(AddCommonRecommendResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddCommonRecommend", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RemoveCommonRecommend(ctx context.Context, in *RemoveCommonRecommendReq, opts ...grpc.CallOption) (*RemoveCommonRecommendResp, error) {
	out := new(RemoveCommonRecommendResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RemoveCommonRecommend", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetCommonRecommend(ctx context.Context, in *GetCommonRecommendReq, opts ...grpc.CallOption) (*GetCommonRecommendResp, error) {
	out := new(GetCommonRecommendResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetCommonRecommend", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddRandomRecommendChannel(ctx context.Context, in *AddRandomRecommendChannelReq, opts ...grpc.CallOption) (*AddRandomRecommendChannelResp, error) {
	out := new(AddRandomRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddRandomRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetRandomRecommendChannel(ctx context.Context, in *GetRandomRecommendChannelReq, opts ...grpc.CallOption) (*GetRandomRecommendChannelResp, error) {
	out := new(GetRandomRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetRandomRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RemoveRandomRecommendChannel(ctx context.Context, in *RemoveRandomRecommendChannelReq, opts ...grpc.CallOption) (*RemoveRandomRecommendChannelResp, error) {
	out := new(RemoveRandomRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RemoveRandomRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) BatchDelHotChannel(ctx context.Context, in *BatchDelHotChannelReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/BatchDelHotChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetChannelTypeByTag(ctx context.Context, in *GetChannelTypeByTagReq, opts ...grpc.CallOption) (*GetChannelTypeByTagResp, error) {
	out := new(GetChannelTypeByTagResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetChannelTypeByTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetTagInfo(ctx context.Context, in *GetTagInfoReq, opts ...grpc.CallOption) (*GetTagInfoResp, error) {
	out := new(GetTagInfoResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetTagInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetMajorLiveChannel(ctx context.Context, in *GetMajorLiveChannelReq, opts ...grpc.CallOption) (*GetMajorLiveChannelResp, error) {
	out := new(GetMajorLiveChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetMajorLiveChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) AddLiveRecommendChannel(ctx context.Context, in *AddLiveRecommendChannelReq, opts ...grpc.CallOption) (*AddLiveRecommendChannelResp, error) {
	out := new(AddLiveRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/AddLiveRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) DelLiveRecommendChannel(ctx context.Context, in *DelLiveRecommendChannelReq, opts ...grpc.CallOption) (*DelLiveRecommendChannelResp, error) {
	out := new(DelLiveRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/DelLiveRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetLiveRecommendChannel(ctx context.Context, in *GetLiveRecommendChannelReq, opts ...grpc.CallOption) (*GetLiveRecommendChannelResp, error) {
	out := new(GetLiveRecommendChannelResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetLiveRecommendChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) GetLiveChannelList(ctx context.Context, in *GetLiveChannelListReq, opts ...grpc.CallOption) (*GetLiveChannelListResp, error) {
	out := new(GetLiveChannelListResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/GetLiveChannelList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) RemoveChannelByType(ctx context.Context, in *RemoveChannelByTypeReq, opts ...grpc.CallOption) (*RemoveChannelByTypeResp, error) {
	out := new(RemoveChannelByTypeResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/RemoveChannelByType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelRecommendClient) SetRecommendChannelTag(ctx context.Context, in *SetChannelInfoExReq, opts ...grpc.CallOption) (*SetChannelInfoExResp, error) {
	out := new(SetChannelInfoExResp)
	err := grpc.Invoke(ctx, "/channelrecommend.ChannelRecommend/SetRecommendChannelTag", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelRecommend service

type ChannelRecommendServer interface {
	UserEnterChannel(context.Context, *UserEnterChannelReq) (*UserEnterChannelResp, error)
	GetRecommendChannel(context.Context, *GetRecommendChannelReq) (*GetRecommendChannelResp, error)
	AddChannel(context.Context, *AddChannelReq) (*AddChannelResp, error)
	GetChannel(context.Context, *GetChannelReq) (*GetChannelResp, error)
	AddChannelGift(context.Context, *AddChannelGiftReq) (*AddChannelGiftResp, error)
	SetChannelInfoEx(context.Context, *SetChannelInfoExReq) (*SetChannelInfoExResp, error)
	GetChannelInfoEx(context.Context, *GetChannelInfoExReq) (*GetChannelInfoExResp, error)
	RemoveChannel(context.Context, *RemoveChannelReq) (*RemoveChannelResp, error)
	GetHotChannel(context.Context, *GetHotChannelReq) (*GetHotChannelResp, error)
	AddUserLikeChannel(context.Context, *AddUserLikeChannelReq) (*AddUserLikeChannelResp, error)
	GetChannelInfoTag(context.Context, *GetChannelInfoTagReq) (*GetChannelInfoTagResp, error)
	SetHotChannelCfg(context.Context, *SetHotChannelCfgReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetHotChannelCfg(context.Context, *GetHotChannelCfgReq) (*GetHotChannelCfgResp, error)
	BatchGetChannelType(context.Context, *BatchGetChannelTypeReq) (*BatchGetChannelTypeResp, error)
	// 废弃
	AddColor(context.Context, *AddColorReq) (*AddColorResp, error)
	// 废弃
	GetAllColor(context.Context, *GetAllColorReq) (*GetAllColorResp, error)
	SetHotChannelType(context.Context, *SetHotChannelTypeReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	BatchGetHotChannelType(context.Context, *BatchGetHotChannelTypeReq) (*BatchGetHotChannelTypeResp, error)
	GetHotChannelListByType(context.Context, *GetHotChannelListByTypeReq) (*GetHotChannelListByTypeResp, error)
	GetAllTagType(context.Context, *GetAllTagTypeReq) (*GetAllTagTypeResp, error)
	GetChannelCard(context.Context, *GetChannelCardReq) (*GetChannelCardResp, error)
	GetChannelByTag(context.Context, *GetChannelByTagReq) (*GetChannelByTagResp, error)
	RefreshChannelTime(context.Context, *RefreshChannelTimeReq) (*RefreshChannelTimeResp, error)
	SetChannelTagId(context.Context, *SetChannelTagIdReq) (*SetChannelTagIdResp, error)
	GetChannelTagId(context.Context, *GetChannelTagIdReq) (*GetChannelTagIdResp, error)
	AddChannelTagAdv(context.Context, *AddChannelTagAdvReq) (*AddChannelTagAdvResp, error)
	RemoveChannelTagAdv(context.Context, *RemoveChannelTagAdvReq) (*RemoveChannelTagAdvResp, error)
	GetChannelTagAdv(context.Context, *GetChannelTagAdvReq) (*GetChannelTagAdvResp, error)
	GetChannelRefreshCD(context.Context, *GetChannelRefreshCDReq) (*GetChannelRefreshCDResp, error)
	GetChannelTagColor(context.Context, *GetChannelTagColorReq) (*GetChannelTagColorResp, error)
	AddTagTopChannel(context.Context, *AddTagTopChannelReq) (*AddTagTopChannelResp, error)
	GetTagTopChannel(context.Context, *GetTagTopChannelReq) (*GetTagTopChannelResp, error)
	RemoveTagTopChannel(context.Context, *RemoveTagTopChannelReq) (*RemoveTagTopChannelResp, error)
	AddCommonRecommend(context.Context, *AddCommonRecommendReq) (*AddCommonRecommendResp, error)
	RemoveCommonRecommend(context.Context, *RemoveCommonRecommendReq) (*RemoveCommonRecommendResp, error)
	GetCommonRecommend(context.Context, *GetCommonRecommendReq) (*GetCommonRecommendResp, error)
	AddRandomRecommendChannel(context.Context, *AddRandomRecommendChannelReq) (*AddRandomRecommendChannelResp, error)
	GetRandomRecommendChannel(context.Context, *GetRandomRecommendChannelReq) (*GetRandomRecommendChannelResp, error)
	RemoveRandomRecommendChannel(context.Context, *RemoveRandomRecommendChannelReq) (*RemoveRandomRecommendChannelResp, error)
	BatchDelHotChannel(context.Context, *BatchDelHotChannelReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelTypeByTag(context.Context, *GetChannelTypeByTagReq) (*GetChannelTypeByTagResp, error)
	GetTagInfo(context.Context, *GetTagInfoReq) (*GetTagInfoResp, error)
	GetMajorLiveChannel(context.Context, *GetMajorLiveChannelReq) (*GetMajorLiveChannelResp, error)
	AddLiveRecommendChannel(context.Context, *AddLiveRecommendChannelReq) (*AddLiveRecommendChannelResp, error)
	DelLiveRecommendChannel(context.Context, *DelLiveRecommendChannelReq) (*DelLiveRecommendChannelResp, error)
	GetLiveRecommendChannel(context.Context, *GetLiveRecommendChannelReq) (*GetLiveRecommendChannelResp, error)
	GetLiveChannelList(context.Context, *GetLiveChannelListReq) (*GetLiveChannelListResp, error)
	RemoveChannelByType(context.Context, *RemoveChannelByTypeReq) (*RemoveChannelByTypeResp, error)
	SetRecommendChannelTag(context.Context, *SetChannelInfoExReq) (*SetChannelInfoExResp, error)
}

func RegisterChannelRecommendServer(s *grpc.Server, srv ChannelRecommendServer) {
	s.RegisterService(&_ChannelRecommend_serviceDesc, srv)
}

func _ChannelRecommend_UserEnterChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserEnterChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).UserEnterChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/UserEnterChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).UserEnterChannel(ctx, req.(*UserEnterChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetRecommendChannel(ctx, req.(*GetRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddChannel(ctx, req.(*AddChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannel(ctx, req.(*GetChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddChannelGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddChannelGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddChannelGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddChannelGift(ctx, req.(*AddChannelGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_SetChannelInfoEx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelInfoExReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).SetChannelInfoEx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/SetChannelInfoEx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).SetChannelInfoEx(ctx, req.(*SetChannelInfoExReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelInfoEx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelInfoExReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelInfoEx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelInfoEx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelInfoEx(ctx, req.(*GetChannelInfoExReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RemoveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RemoveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RemoveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RemoveChannel(ctx, req.(*RemoveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetHotChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHotChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetHotChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetHotChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetHotChannel(ctx, req.(*GetHotChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddUserLikeChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserLikeChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddUserLikeChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddUserLikeChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddUserLikeChannel(ctx, req.(*AddUserLikeChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelInfoTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelInfoTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelInfoTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelInfoTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelInfoTag(ctx, req.(*GetChannelInfoTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_SetHotChannelCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHotChannelCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).SetHotChannelCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/SetHotChannelCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).SetHotChannelCfg(ctx, req.(*SetHotChannelCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetHotChannelCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHotChannelCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetHotChannelCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetHotChannelCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetHotChannelCfg(ctx, req.(*GetHotChannelCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_BatchGetChannelType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).BatchGetChannelType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/BatchGetChannelType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).BatchGetChannelType(ctx, req.(*BatchGetChannelTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddColor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddColorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddColor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddColor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddColor(ctx, req.(*AddColorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetAllColor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllColorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetAllColor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetAllColor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetAllColor(ctx, req.(*GetAllColorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_SetHotChannelType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetHotChannelTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).SetHotChannelType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/SetHotChannelType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).SetHotChannelType(ctx, req.(*SetHotChannelTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_BatchGetHotChannelType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetHotChannelTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).BatchGetHotChannelType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/BatchGetHotChannelType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).BatchGetHotChannelType(ctx, req.(*BatchGetHotChannelTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetHotChannelListByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHotChannelListByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetHotChannelListByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetHotChannelListByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetHotChannelListByType(ctx, req.(*GetHotChannelListByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetAllTagType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTagTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetAllTagType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetAllTagType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetAllTagType(ctx, req.(*GetAllTagTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelCardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelCard(ctx, req.(*GetChannelCardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelByTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelByTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelByTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelByTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelByTag(ctx, req.(*GetChannelByTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RefreshChannelTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefreshChannelTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RefreshChannelTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RefreshChannelTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RefreshChannelTime(ctx, req.(*RefreshChannelTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_SetChannelTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).SetChannelTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/SetChannelTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).SetChannelTagId(ctx, req.(*SetChannelTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelTagId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelTagId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelTagId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelTagId(ctx, req.(*GetChannelTagIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddChannelTagAdv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelTagAdvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddChannelTagAdv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddChannelTagAdv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddChannelTagAdv(ctx, req.(*AddChannelTagAdvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RemoveChannelTagAdv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelTagAdvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RemoveChannelTagAdv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RemoveChannelTagAdv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RemoveChannelTagAdv(ctx, req.(*RemoveChannelTagAdvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelTagAdv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagAdvReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelTagAdv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelTagAdv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelTagAdv(ctx, req.(*GetChannelTagAdvReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelRefreshCD_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRefreshCDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelRefreshCD(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelRefreshCD",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelRefreshCD(ctx, req.(*GetChannelRefreshCDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelTagColor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTagColorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelTagColor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelTagColor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelTagColor(ctx, req.(*GetChannelTagColorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddTagTopChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTagTopChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddTagTopChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddTagTopChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddTagTopChannel(ctx, req.(*AddTagTopChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetTagTopChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagTopChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetTagTopChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetTagTopChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetTagTopChannel(ctx, req.(*GetTagTopChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RemoveTagTopChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveTagTopChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RemoveTagTopChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RemoveTagTopChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RemoveTagTopChannel(ctx, req.(*RemoveTagTopChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddCommonRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddCommonRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddCommonRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddCommonRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddCommonRecommend(ctx, req.(*AddCommonRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RemoveCommonRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveCommonRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RemoveCommonRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RemoveCommonRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RemoveCommonRecommend(ctx, req.(*RemoveCommonRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetCommonRecommend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommonRecommendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetCommonRecommend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetCommonRecommend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetCommonRecommend(ctx, req.(*GetCommonRecommendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddRandomRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRandomRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddRandomRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddRandomRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddRandomRecommendChannel(ctx, req.(*AddRandomRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetRandomRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRandomRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetRandomRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetRandomRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetRandomRecommendChannel(ctx, req.(*GetRandomRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RemoveRandomRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRandomRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RemoveRandomRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RemoveRandomRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RemoveRandomRecommendChannel(ctx, req.(*RemoveRandomRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_BatchDelHotChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelHotChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).BatchDelHotChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/BatchDelHotChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).BatchDelHotChannel(ctx, req.(*BatchDelHotChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetChannelTypeByTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelTypeByTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetChannelTypeByTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetChannelTypeByTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetChannelTypeByTag(ctx, req.(*GetChannelTypeByTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetTagInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTagInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetTagInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetTagInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetTagInfo(ctx, req.(*GetTagInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetMajorLiveChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMajorLiveChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetMajorLiveChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetMajorLiveChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetMajorLiveChannel(ctx, req.(*GetMajorLiveChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_AddLiveRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddLiveRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).AddLiveRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/AddLiveRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).AddLiveRecommendChannel(ctx, req.(*AddLiveRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_DelLiveRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLiveRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).DelLiveRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/DelLiveRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).DelLiveRecommendChannel(ctx, req.(*DelLiveRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetLiveRecommendChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveRecommendChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetLiveRecommendChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetLiveRecommendChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetLiveRecommendChannel(ctx, req.(*GetLiveRecommendChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_GetLiveChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).GetLiveChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/GetLiveChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).GetLiveChannelList(ctx, req.(*GetLiveChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_RemoveChannelByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelByTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).RemoveChannelByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/RemoveChannelByType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).RemoveChannelByType(ctx, req.(*RemoveChannelByTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelRecommend_SetRecommendChannelTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelInfoExReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelRecommendServer).SetRecommendChannelTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelrecommend.ChannelRecommend/SetRecommendChannelTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelRecommendServer).SetRecommendChannelTag(ctx, req.(*SetChannelInfoExReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelRecommend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelrecommend.ChannelRecommend",
	HandlerType: (*ChannelRecommendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserEnterChannel",
			Handler:    _ChannelRecommend_UserEnterChannel_Handler,
		},
		{
			MethodName: "GetRecommendChannel",
			Handler:    _ChannelRecommend_GetRecommendChannel_Handler,
		},
		{
			MethodName: "AddChannel",
			Handler:    _ChannelRecommend_AddChannel_Handler,
		},
		{
			MethodName: "GetChannel",
			Handler:    _ChannelRecommend_GetChannel_Handler,
		},
		{
			MethodName: "AddChannelGift",
			Handler:    _ChannelRecommend_AddChannelGift_Handler,
		},
		{
			MethodName: "SetChannelInfoEx",
			Handler:    _ChannelRecommend_SetChannelInfoEx_Handler,
		},
		{
			MethodName: "GetChannelInfoEx",
			Handler:    _ChannelRecommend_GetChannelInfoEx_Handler,
		},
		{
			MethodName: "RemoveChannel",
			Handler:    _ChannelRecommend_RemoveChannel_Handler,
		},
		{
			MethodName: "GetHotChannel",
			Handler:    _ChannelRecommend_GetHotChannel_Handler,
		},
		{
			MethodName: "AddUserLikeChannel",
			Handler:    _ChannelRecommend_AddUserLikeChannel_Handler,
		},
		{
			MethodName: "GetChannelInfoTag",
			Handler:    _ChannelRecommend_GetChannelInfoTag_Handler,
		},
		{
			MethodName: "SetHotChannelCfg",
			Handler:    _ChannelRecommend_SetHotChannelCfg_Handler,
		},
		{
			MethodName: "GetHotChannelCfg",
			Handler:    _ChannelRecommend_GetHotChannelCfg_Handler,
		},
		{
			MethodName: "BatchGetChannelType",
			Handler:    _ChannelRecommend_BatchGetChannelType_Handler,
		},
		{
			MethodName: "AddColor",
			Handler:    _ChannelRecommend_AddColor_Handler,
		},
		{
			MethodName: "GetAllColor",
			Handler:    _ChannelRecommend_GetAllColor_Handler,
		},
		{
			MethodName: "SetHotChannelType",
			Handler:    _ChannelRecommend_SetHotChannelType_Handler,
		},
		{
			MethodName: "BatchGetHotChannelType",
			Handler:    _ChannelRecommend_BatchGetHotChannelType_Handler,
		},
		{
			MethodName: "GetHotChannelListByType",
			Handler:    _ChannelRecommend_GetHotChannelListByType_Handler,
		},
		{
			MethodName: "GetAllTagType",
			Handler:    _ChannelRecommend_GetAllTagType_Handler,
		},
		{
			MethodName: "GetChannelCard",
			Handler:    _ChannelRecommend_GetChannelCard_Handler,
		},
		{
			MethodName: "GetChannelByTag",
			Handler:    _ChannelRecommend_GetChannelByTag_Handler,
		},
		{
			MethodName: "RefreshChannelTime",
			Handler:    _ChannelRecommend_RefreshChannelTime_Handler,
		},
		{
			MethodName: "SetChannelTagId",
			Handler:    _ChannelRecommend_SetChannelTagId_Handler,
		},
		{
			MethodName: "GetChannelTagId",
			Handler:    _ChannelRecommend_GetChannelTagId_Handler,
		},
		{
			MethodName: "AddChannelTagAdv",
			Handler:    _ChannelRecommend_AddChannelTagAdv_Handler,
		},
		{
			MethodName: "RemoveChannelTagAdv",
			Handler:    _ChannelRecommend_RemoveChannelTagAdv_Handler,
		},
		{
			MethodName: "GetChannelTagAdv",
			Handler:    _ChannelRecommend_GetChannelTagAdv_Handler,
		},
		{
			MethodName: "GetChannelRefreshCD",
			Handler:    _ChannelRecommend_GetChannelRefreshCD_Handler,
		},
		{
			MethodName: "GetChannelTagColor",
			Handler:    _ChannelRecommend_GetChannelTagColor_Handler,
		},
		{
			MethodName: "AddTagTopChannel",
			Handler:    _ChannelRecommend_AddTagTopChannel_Handler,
		},
		{
			MethodName: "GetTagTopChannel",
			Handler:    _ChannelRecommend_GetTagTopChannel_Handler,
		},
		{
			MethodName: "RemoveTagTopChannel",
			Handler:    _ChannelRecommend_RemoveTagTopChannel_Handler,
		},
		{
			MethodName: "AddCommonRecommend",
			Handler:    _ChannelRecommend_AddCommonRecommend_Handler,
		},
		{
			MethodName: "RemoveCommonRecommend",
			Handler:    _ChannelRecommend_RemoveCommonRecommend_Handler,
		},
		{
			MethodName: "GetCommonRecommend",
			Handler:    _ChannelRecommend_GetCommonRecommend_Handler,
		},
		{
			MethodName: "AddRandomRecommendChannel",
			Handler:    _ChannelRecommend_AddRandomRecommendChannel_Handler,
		},
		{
			MethodName: "GetRandomRecommendChannel",
			Handler:    _ChannelRecommend_GetRandomRecommendChannel_Handler,
		},
		{
			MethodName: "RemoveRandomRecommendChannel",
			Handler:    _ChannelRecommend_RemoveRandomRecommendChannel_Handler,
		},
		{
			MethodName: "BatchDelHotChannel",
			Handler:    _ChannelRecommend_BatchDelHotChannel_Handler,
		},
		{
			MethodName: "GetChannelTypeByTag",
			Handler:    _ChannelRecommend_GetChannelTypeByTag_Handler,
		},
		{
			MethodName: "GetTagInfo",
			Handler:    _ChannelRecommend_GetTagInfo_Handler,
		},
		{
			MethodName: "GetMajorLiveChannel",
			Handler:    _ChannelRecommend_GetMajorLiveChannel_Handler,
		},
		{
			MethodName: "AddLiveRecommendChannel",
			Handler:    _ChannelRecommend_AddLiveRecommendChannel_Handler,
		},
		{
			MethodName: "DelLiveRecommendChannel",
			Handler:    _ChannelRecommend_DelLiveRecommendChannel_Handler,
		},
		{
			MethodName: "GetLiveRecommendChannel",
			Handler:    _ChannelRecommend_GetLiveRecommendChannel_Handler,
		},
		{
			MethodName: "GetLiveChannelList",
			Handler:    _ChannelRecommend_GetLiveChannelList_Handler,
		},
		{
			MethodName: "RemoveChannelByType",
			Handler:    _ChannelRecommend_RemoveChannelByType_Handler,
		},
		{
			MethodName: "SetRecommendChannelTag",
			Handler:    _ChannelRecommend_SetRecommendChannelTag_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelrecommendsvr/channelrecommend.proto",
}

func (m *UserEnterChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserEnterChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.UserCount))
	dAtA[i] = 0x18
	i++
	if m.IsAdmin {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	if m.IsEnter {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.AdminCount))
	dAtA[i] = 0x30
	i++
	if m.IsNormal {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *UserEnterChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserEnterChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddChannelGiftReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelGiftReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.GiftValue))
	return i, nil
}

func (m *AddChannelGiftResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelGiftResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	if m.IsNewUser {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Tag))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.InfoTag)))
	i += copy(dAtA[i:], m.InfoTag)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.BkColor)))
	i += copy(dAtA[i:], m.BkColor)
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.InfoExFlag))
	return i, nil
}

func (m *GetRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.ReachEnd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if len(m.ChannelInfoList) > 0 {
		for _, msg := range m.ChannelInfoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.MayFixTopIdList) > 0 {
		for _, num := range m.MayFixTopIdList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelTagColorReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTagColorReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelTagColorResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTagColorResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelInfoList) > 0 {
		for _, msg := range m.ChannelInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.IsAdd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *AddChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *GetChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ChannelTypeInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTypeInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *BatchGetChannelTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetChannelTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetChannelTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelTypeList) > 0 {
		for _, msg := range m.ChannelTypeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelInfoEx) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelInfoEx) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.StartTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.EndTs))
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.UserCount))
	if len(m.HoursToAddList) > 0 {
		for _, num := range m.HoursToAddList {
			dAtA[i] = 0x30
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Flags))
	dAtA[i] = 0x42
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.InfoTag)))
	i += copy(dAtA[i:], m.InfoTag)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.BkColor)))
	i += copy(dAtA[i:], m.BkColor)
	return i, nil
}

func (m *SetChannelInfoExReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelInfoExReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	if len(m.ChannelExList) > 0 {
		for _, msg := range m.ChannelExList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetChannelInfoExResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelInfoExResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelInfoExReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelInfoExReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetChannelInfoExResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelInfoExResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelExList) > 0 {
		for _, msg := range m.ChannelExList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RemoveChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RemoveChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemoveChannelByTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelByTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *RemoveChannelByTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelByTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddUserLikeChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserLikeChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *AddUserLikeChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddUserLikeChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelInfoTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelInfoTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ChannelInfoTag) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelInfoTag) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.InfoTag)))
	i += copy(dAtA[i:], m.InfoTag)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.BkColor)))
	i += copy(dAtA[i:], m.BkColor)
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *GetChannelInfoTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelInfoTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelInfotagList) > 0 {
		for _, msg := range m.ChannelInfotagList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetHotChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHotChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Count))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Ts))
	return i, nil
}

func (m *ChannelHotInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelHotInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.DayGiftValue))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RankGiftValue))
	return i, nil
}

func (m *GetHotChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHotChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.ReachEnd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetHotChannelCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetHotChannelCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TotalGiftValue))
	return i, nil
}

func (m *GetHotChannelCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHotChannelCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetHotChannelCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHotChannelCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TotalGiftValue))
	return i, nil
}

func (m *SetHotChannelTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetHotChannelTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *HotChannelInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HotChannelInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *BatchGetHotChannelTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetHotChannelTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetHotChannelTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetHotChannelTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelList) > 0 {
		for _, msg := range m.ChannelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetHotChannelListByTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHotChannelListByTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelType))
	return i, nil
}

func (m *GetHotChannelListByTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetHotChannelListByTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelList) > 0 {
		for _, msg := range m.ChannelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchDelHotChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDelHotChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *ColorInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ColorInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.Color)))
	i += copy(dAtA[i:], m.Color)
	return i, nil
}

func (m *AddColorReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddColorReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ColorInfoList) > 0 {
		for _, msg := range m.ColorInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	if m.IsAdd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AddColorResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddColorResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllColorReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllColorReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllColorResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllColorResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ColorInfoList) > 0 {
		for _, msg := range m.ColorInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.CardName)))
	i += copy(dAtA[i:], m.CardName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.CardImage)))
	i += copy(dAtA[i:], m.CardImage)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.BackImage)))
	i += copy(dAtA[i:], m.BackImage)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.ClientLink)))
	i += copy(dAtA[i:], m.ClientLink)
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.UserCount))
	dAtA[i] = 0x38
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x40
	i++
	if m.FixPosition {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RootTag) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RootTag) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.CalcSub))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagType))
	return i, nil
}

func (m *SubTag) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SubTag) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.TagName)))
	i += copy(dAtA[i:], m.TagName)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.Icon)))
	i += copy(dAtA[i:], m.Icon)
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x20
	i++
	if m.IsShow {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x2a
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.JumpUrl)))
	i += copy(dAtA[i:], m.JumpUrl)
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RootTagId))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.BkColor)))
	i += copy(dAtA[i:], m.BkColor)
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ExtFlag))
	return i, nil
}

func (m *TagTypes) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagTypes) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RootTag == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("root_tag")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RootTag.Size()))
		n1, err := m.RootTag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.SubTagList) > 0 {
		for _, msg := range m.SubTagList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetAllTagTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTagTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAllTagTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTagTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagTypesList) > 0 {
		for _, msg := range m.TagTypesList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetChannelByTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelByTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetChannelByTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelByTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.ReachEnd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.UserCount))
	return i, nil
}

func (m *GetChannelCardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelCardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelCardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CardInfoList) > 0 {
		for _, msg := range m.CardInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if m.GameSubTag == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("game_sub_tag")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.GameSubTag.Size()))
		n2, err := m.GameSubTag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x18
	i++
	if m.GameOnTop {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	if m.HasVisitGame {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RefreshChannelTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RefreshChannelTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *RefreshChannelTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RefreshChannelTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RemainSeconds))
	return i, nil
}

func (m *GetChannelRefreshCDReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRefreshCDReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelRefreshCDResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelRefreshCDResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RemainSeconds))
	return i, nil
}

func (m *SetChannelTagIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelTagIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *SetChannelTagIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelTagIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.OldTagId))
	return i, nil
}

func (m *GetChannelTagIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTagIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelTagIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTagIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SubTag == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sub_tag")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.SubTag.Size()))
		n3, err := m.SubTag.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x10
	i++
	if m.IsSet {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *ChannelTagAdv) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelTagAdv) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(len(m.AdvUrl)))
	i += copy(dAtA[i:], m.AdvUrl)
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ScoreIdx))
	return i, nil
}

func (m *AddChannelTagAdvReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelTagAdvReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagAdvList) > 0 {
		for _, msg := range m.TagAdvList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	if m.CleanOld {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.AdvType))
	return i, nil
}

func (m *AddChannelTagAdvResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelTagAdvResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemoveChannelTagAdvReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelTagAdvReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.TagAdv == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tag_adv")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagAdv.Size()))
		n4, err := m.TagAdv.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.AdvType))
	return i, nil
}

func (m *RemoveChannelTagAdvResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelTagAdvResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelTagAdvReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTagAdvReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.AdvType))
	return i, nil
}

func (m *GetChannelTagAdvResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTagAdvResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TagAdvList) > 0 {
		for _, msg := range m.TagAdvList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *TagTopChannel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TagTopChannel) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Score))
	return i, nil
}

func (m *AddTagTopChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTagTopChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopChannelList) > 0 {
		for _, msg := range m.TopChannelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *AddTagTopChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddTagTopChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetTagTopChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagTopChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *GetTagTopChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagTopChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TopChannelList) > 0 {
		for _, msg := range m.TopChannelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RemoveTagTopChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveTagTopChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RemoveTagTopChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveTagTopChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CommonRecommendChannel) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommonRecommendChannel) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *AddCommonRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddCommonRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RecommendChannel == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("recommend_channel")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RecommendChannel.Size()))
		n5, err := m.RecommendChannel.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.FunType))
	return i, nil
}

func (m *AddCommonRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddCommonRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemoveCommonRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveCommonRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.RecommendChannel == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("recommend_channel")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.RecommendChannel.Size()))
		n6, err := m.RecommendChannel.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.FunType))
	return i, nil
}

func (m *RemoveCommonRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveCommonRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetCommonRecommendReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommonRecommendReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.FunType))
	return i, nil
}

func (m *GetCommonRecommendResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommonRecommendResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RecommendChannelList) > 0 {
		for _, msg := range m.RecommendChannelList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *RandomChannelInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RandomChannelInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.BlockTag))
	return i, nil
}

func (m *AddRandomRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddRandomRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *AddRandomRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddRandomRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetRandomRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRandomRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetRandomRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRandomRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RemoveRandomRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveRandomRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *RemoveRandomRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveRandomRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelTypeByTagReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTypeByTagReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelTypeByTagResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelTypeByTagResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *GetTagInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.TagId))
	return i, nil
}

func (m *GetTagInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTagInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.SubTagInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sub_tag_info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelrecommend(dAtA, i, uint64(m.SubTagInfo.Size()))
		n7, err := m.SubTagInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *GetMajorLiveChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMajorLiveChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetMajorLiveChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMajorLiveChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *LiveChannelEx) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LiveChannelEx) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ScoreIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.HasAdmin))
	if len(m.HoursToAddList) > 0 {
		for _, num := range m.HoursToAddList {
			dAtA[i] = 0x20
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *AddLiveRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLiveRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ScoreIdx))
	if len(m.HoursToAddList) > 0 {
		for _, num := range m.HoursToAddList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *AddLiveRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLiveRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelLiveRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelLiveRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *DelLiveRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelLiveRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLiveRecommendChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveRecommendChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetLiveRecommendChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveRecommendChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelExList) > 0 {
		for _, msg := range m.ChannelExList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLiveChannelListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveChannelListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Start))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetLiveChannelListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLiveChannelListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelrecommend(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	if m.ReachEnd {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelrecommend(dAtA, i, uint64(m.UserCount))
	return i, nil
}

func encodeFixed64Channelrecommend(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelrecommend(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelrecommend(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *UserEnterChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.UserCount))
	n += 2
	n += 2
	n += 1 + sovChannelrecommend(uint64(m.AdminCount))
	n += 2
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *UserEnterChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddChannelGiftReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.GiftValue))
	return n
}

func (m *AddChannelGiftResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.Uid))
	n += 1 + sovChannelrecommend(uint64(m.Start))
	n += 1 + sovChannelrecommend(uint64(m.Count))
	n += 2
	return n
}

func (m *ChannelInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.Tag))
	l = len(m.InfoTag)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.BkColor)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.InfoExFlag))
	return n
}

func (m *GetRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 2
	if len(m.ChannelInfoList) > 0 {
		for _, e := range m.ChannelInfoList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	if len(m.MayFixTopIdList) > 0 {
		for _, e := range m.MayFixTopIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *GetChannelTagColorReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *GetChannelTagColorResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelInfoList) > 0 {
		for _, e := range m.ChannelInfoList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *AddChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 2
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *AddChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *GetChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *ChannelTypeInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *BatchGetChannelTypeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *BatchGetChannelTypeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelTypeList) > 0 {
		for _, e := range m.ChannelTypeList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *ChannelInfoEx) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.StartTs))
	n += 1 + sovChannelrecommend(uint64(m.EndTs))
	l = len(m.Desc)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.UserCount))
	if len(m.HoursToAddList) > 0 {
		for _, e := range m.HoursToAddList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 1 + sovChannelrecommend(uint64(m.Flags))
	l = len(m.InfoTag)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.BkColor)
	n += 1 + l + sovChannelrecommend(uint64(l))
	return n
}

func (m *SetChannelInfoExReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	if len(m.ChannelExList) > 0 {
		for _, e := range m.ChannelExList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *SetChannelInfoExResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelInfoExReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *GetChannelInfoExResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelExList) > 0 {
		for _, e := range m.ChannelExList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *RemoveChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *RemoveChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemoveChannelByTypeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *RemoveChannelByTypeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddUserLikeChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *AddUserLikeChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelInfoTagReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *ChannelInfoTag) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	l = len(m.InfoTag)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.BkColor)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *GetChannelInfoTagResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelInfotagList) > 0 {
		for _, e := range m.ChannelInfotagList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *GetHotChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.Uid))
	n += 1 + sovChannelrecommend(uint64(m.Start))
	n += 1 + sovChannelrecommend(uint64(m.Count))
	n += 1 + sovChannelrecommend(uint64(m.Ts))
	return n
}

func (m *ChannelHotInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.DayGiftValue))
	n += 1 + sovChannelrecommend(uint64(m.RankGiftValue))
	return n
}

func (m *GetHotChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *SetHotChannelCfgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TotalGiftValue))
	return n
}

func (m *GetHotChannelCfgReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetHotChannelCfgResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TotalGiftValue))
	return n
}

func (m *SetHotChannelTypeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *HotChannelInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *BatchGetHotChannelTypeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *BatchGetHotChannelTypeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelList) > 0 {
		for _, e := range m.ChannelList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *GetHotChannelListByTypeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelType))
	return n
}

func (m *GetHotChannelListByTypeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelList) > 0 {
		for _, e := range m.ChannelList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *BatchDelHotChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *ColorInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.TagName)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.Color)
	n += 1 + l + sovChannelrecommend(uint64(l))
	return n
}

func (m *AddColorReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ColorInfoList) > 0 {
		for _, e := range m.ColorInfoList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *AddColorResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllColorReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllColorResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ColorInfoList) > 0 {
		for _, e := range m.ColorInfoList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *CardInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.CardName)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.CardImage)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.BackImage)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.ClientLink)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	n += 1 + sovChannelrecommend(uint64(m.UserCount))
	n += 1 + sovChannelrecommend(uint64(m.GameId))
	n += 2
	return n
}

func (m *RootTag) Size() (n int) {
	var l int
	_ = l
	l = len(m.TagName)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	n += 1 + sovChannelrecommend(uint64(m.CalcSub))
	n += 1 + sovChannelrecommend(uint64(m.TagType))
	return n
}

func (m *SubTag) Size() (n int) {
	var l int
	_ = l
	l = len(m.TagName)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.Icon)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	n += 2
	l = len(m.JumpUrl)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.RootTagId))
	l = len(m.BkColor)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.ExtFlag))
	return n
}

func (m *TagTypes) Size() (n int) {
	var l int
	_ = l
	if m.RootTag != nil {
		l = m.RootTag.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	if len(m.SubTagList) > 0 {
		for _, e := range m.SubTagList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *GetAllTagTypeReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAllTagTypeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TagTypesList) > 0 {
		for _, e := range m.TagTypesList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *GetChannelByTagReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	n += 1 + sovChannelrecommend(uint64(m.Start))
	n += 1 + sovChannelrecommend(uint64(m.Count))
	return n
}

func (m *GetChannelByTagResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 2
	n += 1 + sovChannelrecommend(uint64(m.UserCount))
	return n
}

func (m *GetChannelCardReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelCardResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CardInfoList) > 0 {
		for _, e := range m.CardInfoList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	if m.GameSubTag != nil {
		l = m.GameSubTag.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	n += 2
	n += 2
	return n
}

func (m *RefreshChannelTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *RefreshChannelTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.RemainSeconds))
	return n
}

func (m *GetChannelRefreshCDReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *GetChannelRefreshCDResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.RemainSeconds))
	return n
}

func (m *SetChannelTagIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	return n
}

func (m *SetChannelTagIdResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.OldTagId))
	return n
}

func (m *GetChannelTagIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *GetChannelTagIdResp) Size() (n int) {
	var l int
	_ = l
	if m.SubTag != nil {
		l = m.SubTag.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	n += 2
	return n
}

func (m *ChannelTagAdv) Size() (n int) {
	var l int
	_ = l
	l = len(m.PicUrl)
	n += 1 + l + sovChannelrecommend(uint64(l))
	l = len(m.AdvUrl)
	n += 1 + l + sovChannelrecommend(uint64(l))
	n += 1 + sovChannelrecommend(uint64(m.ScoreIdx))
	return n
}

func (m *AddChannelTagAdvReq) Size() (n int) {
	var l int
	_ = l
	if len(m.TagAdvList) > 0 {
		for _, e := range m.TagAdvList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	n += 2
	n += 1 + sovChannelrecommend(uint64(m.AdvType))
	return n
}

func (m *AddChannelTagAdvResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemoveChannelTagAdvReq) Size() (n int) {
	var l int
	_ = l
	if m.TagAdv != nil {
		l = m.TagAdv.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	n += 1 + sovChannelrecommend(uint64(m.AdvType))
	return n
}

func (m *RemoveChannelTagAdvResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelTagAdvReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.AdvType))
	return n
}

func (m *GetChannelTagAdvResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TagAdvList) > 0 {
		for _, e := range m.TagAdvList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *TagTopChannel) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.Score))
	return n
}

func (m *AddTagTopChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.TopChannelList) > 0 {
		for _, e := range m.TopChannelList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	return n
}

func (m *AddTagTopChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetTagTopChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	return n
}

func (m *GetTagTopChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TopChannelList) > 0 {
		for _, e := range m.TopChannelList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *RemoveTagTopChannelReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *RemoveTagTopChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CommonRecommendChannel) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *AddCommonRecommendReq) Size() (n int) {
	var l int
	_ = l
	if m.RecommendChannel != nil {
		l = m.RecommendChannel.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	n += 1 + sovChannelrecommend(uint64(m.FunType))
	return n
}

func (m *AddCommonRecommendResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemoveCommonRecommendReq) Size() (n int) {
	var l int
	_ = l
	if m.RecommendChannel != nil {
		l = m.RecommendChannel.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	n += 1 + sovChannelrecommend(uint64(m.FunType))
	return n
}

func (m *RemoveCommonRecommendResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetCommonRecommendReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.FunType))
	return n
}

func (m *GetCommonRecommendResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RecommendChannelList) > 0 {
		for _, e := range m.RecommendChannelList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *RandomChannelInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.BlockTag))
	return n
}

func (m *AddRandomRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *AddRandomRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetRandomRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.Count))
	return n
}

func (m *GetRandomRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *RemoveRandomRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *RemoveRandomRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelTypeByTagReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *GetChannelTypeByTagResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TagType))
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	return n
}

func (m *GetTagInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.TagId))
	return n
}

func (m *GetTagInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.SubTagInfo != nil {
		l = m.SubTagInfo.Size()
		n += 1 + l + sovChannelrecommend(uint64(l))
	}
	return n
}

func (m *GetMajorLiveChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.Uid))
	return n
}

func (m *GetMajorLiveChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *LiveChannelEx) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.ScoreIdx))
	n += 1 + sovChannelrecommend(uint64(m.HasAdmin))
	if len(m.HoursToAddList) > 0 {
		for _, e := range m.HoursToAddList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *AddLiveRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	n += 1 + sovChannelrecommend(uint64(m.ScoreIdx))
	if len(m.HoursToAddList) > 0 {
		for _, e := range m.HoursToAddList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	return n
}

func (m *AddLiveRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelLiveRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.ChannelId))
	return n
}

func (m *DelLiveRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLiveRecommendChannelReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetLiveRecommendChannelResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelExList) > 0 {
		for _, e := range m.ChannelExList {
			l = e.Size()
			n += 1 + l + sovChannelrecommend(uint64(l))
		}
	}
	return n
}

func (m *GetLiveChannelListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelrecommend(uint64(m.Start))
	n += 1 + sovChannelrecommend(uint64(m.Count))
	return n
}

func (m *GetLiveChannelListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelrecommend(uint64(e))
		}
	}
	n += 2
	n += 1 + sovChannelrecommend(uint64(m.UserCount))
	return n
}

func sovChannelrecommend(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelrecommend(x uint64) (n int) {
	return sovChannelrecommend(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *UserEnterChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserEnterChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserEnterChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCount", wireType)
			}
			m.UserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdmin", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdmin = bool(v != 0)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsEnter", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsEnter = bool(v != 0)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdminCount", wireType)
			}
			m.AdminCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdminCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNormal", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNormal = bool(v != 0)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("user_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserEnterChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserEnterChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserEnterChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelGiftReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelGiftReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelGiftReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GiftValue", wireType)
			}
			m.GiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("gift_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelGiftResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelGiftResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelGiftResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNewUser", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNewUser = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_new_user")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Tag", wireType)
			}
			m.Tag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Tag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoTag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoTag = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BkColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BkColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoExFlag", wireType)
			}
			m.InfoExFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.InfoExFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRecommendChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReachEnd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReachEnd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelInfoList = append(m.ChannelInfoList, &ChannelInfo{})
			if err := m.ChannelInfoList[len(m.ChannelInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MayFixTopIdList = append(m.MayFixTopIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MayFixTopIdList = append(m.MayFixTopIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MayFixTopIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reach_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTagColorReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTagColorReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTagColorReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTagColorResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTagColorResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTagColorResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelInfoList = append(m.ChannelInfoList, &ChannelInfo{})
			if err := m.ChannelInfoList[len(m.ChannelInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_add")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTypeInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTypeInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTypeInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelTypeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetChannelTypeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetChannelTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetChannelTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelTypeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelTypeList = append(m.ChannelTypeList, &ChannelTypeInfo{})
			if err := m.ChannelTypeList[len(m.ChannelTypeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelInfoEx) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelInfoEx: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelInfoEx: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTs", wireType)
			}
			m.StartTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCount", wireType)
			}
			m.UserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.HoursToAddList = append(m.HoursToAddList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.HoursToAddList = append(m.HoursToAddList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoursToAddList", wireType)
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Flags", wireType)
			}
			m.Flags = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Flags |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoTag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoTag = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BkColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BkColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_ts")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_ts")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("desc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelInfoExReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelInfoExReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelInfoExReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelExList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelExList = append(m.ChannelExList, &ChannelInfoEx{})
			if err := m.ChannelExList[len(m.ChannelExList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelInfoExResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelInfoExResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelInfoExResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelInfoExReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelInfoExReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelInfoExReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelInfoExResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelInfoExResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelInfoExResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelExList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelExList = append(m.ChannelExList, &ChannelInfoEx{})
			if err := m.ChannelExList[len(m.ChannelExList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelByTypeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelByTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelByTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelByTypeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelByTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelByTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserLikeChannelReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserLikeChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserLikeChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddUserLikeChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddUserLikeChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddUserLikeChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelInfoTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelInfoTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelInfoTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelInfoTag) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelInfoTag: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelInfoTag: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoTag", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoTag = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BkColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BkColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelInfoTagResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelInfoTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelInfoTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelInfotagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelInfotagList = append(m.ChannelInfotagList, &ChannelInfoTag{})
			if err := m.ChannelInfotagList[len(m.ChannelInfotagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHotChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHotChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHotChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelHotInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelHotInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelHotInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayGiftValue", wireType)
			}
			m.DayGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankGiftValue", wireType)
			}
			m.RankGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHotChannelResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHotChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHotChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReachEnd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReachEnd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reach_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetHotChannelCfgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetHotChannelCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetHotChannelCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalGiftValue", wireType)
			}
			m.TotalGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_gift_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHotChannelCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHotChannelCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHotChannelCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHotChannelCfgResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHotChannelCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHotChannelCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalGiftValue", wireType)
			}
			m.TotalGiftValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalGiftValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_gift_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetHotChannelTypeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetHotChannelTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetHotChannelTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HotChannelInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HotChannelInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HotChannelInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetHotChannelTypeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetHotChannelTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetHotChannelTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetHotChannelTypeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetHotChannelTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetHotChannelTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelList = append(m.ChannelList, &HotChannelInfo{})
			if err := m.ChannelList[len(m.ChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHotChannelListByTypeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHotChannelListByTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHotChannelListByTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelType", wireType)
			}
			m.ChannelType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetHotChannelListByTypeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetHotChannelListByTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetHotChannelListByTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelList = append(m.ChannelList, &HotChannelInfo{})
			if err := m.ChannelList[len(m.ChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDelHotChannelReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDelHotChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDelHotChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ColorInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ColorInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ColorInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Color", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Color = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("color")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddColorReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddColorReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddColorReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ColorInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ColorInfoList = append(m.ColorInfoList, &ColorInfo{})
			if err := m.ColorInfoList[len(m.ColorInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsAdd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsAdd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_add")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddColorResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddColorResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddColorResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllColorReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllColorReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllColorReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllColorResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllColorResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllColorResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ColorInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ColorInfoList = append(m.ColorInfoList, &ColorInfo{})
			if err := m.ColorInfoList[len(m.ColorInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CardInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardImage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardImage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackImage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackImage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientLink", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientLink = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCount", wireType)
			}
			m.UserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FixPosition", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FixPosition = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("card_image")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("back_image")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_link")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RootTag) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RootTag: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RootTag: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CalcSub", wireType)
			}
			m.CalcSub = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CalcSub |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SubTag) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SubTag: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SubTag: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Icon", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Icon = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsShow", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsShow = bool(v != 0)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JumpUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.JumpUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RootTagId", wireType)
			}
			m.RootTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RootTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BkColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BkColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtFlag", wireType)
			}
			m.ExtFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_name")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TagTypes) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TagTypes: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TagTypes: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RootTag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RootTag == nil {
				m.RootTag = &RootTag{}
			}
			if err := m.RootTag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTagList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SubTagList = append(m.SubTagList, &SubTag{})
			if err := m.SubTagList[len(m.SubTagList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("root_tag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTagTypeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTagTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTagTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTagTypeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTagTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTagTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagTypesList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagTypesList = append(m.TagTypesList, &TagTypes{})
			if err := m.TagTypesList[len(m.TagTypesList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelByTagReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelByTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelByTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelByTagResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelByTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelByTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReachEnd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReachEnd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCount", wireType)
			}
			m.UserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reach_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCardReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelCardResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelCardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelCardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CardInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CardInfoList = append(m.CardInfoList, &CardInfo{})
			if err := m.CardInfoList[len(m.CardInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameSubTag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GameSubTag == nil {
				m.GameSubTag = &SubTag{}
			}
			if err := m.GameSubTag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameOnTop", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.GameOnTop = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasVisitGame", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasVisitGame = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_sub_tag")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_on_top")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("has_visit_game")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RefreshChannelTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RefreshChannelTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RefreshChannelTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RefreshChannelTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RefreshChannelTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RefreshChannelTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainSeconds", wireType)
			}
			m.RemainSeconds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainSeconds |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain_seconds")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRefreshCDReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRefreshCDReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRefreshCDReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelRefreshCDResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelRefreshCDResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelRefreshCDResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainSeconds", wireType)
			}
			m.RemainSeconds = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainSeconds |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remain_seconds")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelTagIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelTagIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelTagIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelTagIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelTagIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelTagIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OldTagId", wireType)
			}
			m.OldTagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OldTagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTagIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTagIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTagIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTagIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTagIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTagIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTag", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SubTag == nil {
				m.SubTag = &SubTag{}
			}
			if err := m.SubTag.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsSet", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsSet = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_tag")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_set")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelTagAdv) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelTagAdv: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelTagAdv: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AdvUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreIdx", wireType)
			}
			m.ScoreIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScoreIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("pic_url")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("adv_url")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelTagAdvReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelTagAdvReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelTagAdvReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagAdvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagAdvList = append(m.TagAdvList, &ChannelTagAdv{})
			if err := m.TagAdvList[len(m.TagAdvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CleanOld", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CleanOld = bool(v != 0)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvType", wireType)
			}
			m.AdvType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelTagAdvResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelTagAdvResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelTagAdvResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelTagAdvReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelTagAdvReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelTagAdvReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagAdv", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TagAdv == nil {
				m.TagAdv = &ChannelTagAdv{}
			}
			if err := m.TagAdv.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvType", wireType)
			}
			m.AdvType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_adv")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelTagAdvResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelTagAdvResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelTagAdvResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTagAdvReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTagAdvReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTagAdvReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AdvType", wireType)
			}
			m.AdvType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AdvType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTagAdvResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTagAdvResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTagAdvResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagAdvList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TagAdvList = append(m.TagAdvList, &ChannelTagAdv{})
			if err := m.TagAdvList[len(m.TagAdvList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TagTopChannel) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TagTopChannel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TagTopChannel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTagTopChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTagTopChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTagTopChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopChannelList = append(m.TopChannelList, &TagTopChannel{})
			if err := m.TopChannelList[len(m.TopChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddTagTopChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddTagTopChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddTagTopChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagTopChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTagTopChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTagTopChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagTopChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTagTopChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTagTopChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TopChannelList = append(m.TopChannelList, &TagTopChannel{})
			if err := m.TopChannelList[len(m.TopChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveTagTopChannelReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveTagTopChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveTagTopChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveTagTopChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveTagTopChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveTagTopChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommonRecommendChannel) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommonRecommendChannel: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommonRecommendChannel: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddCommonRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddCommonRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddCommonRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RecommendChannel == nil {
				m.RecommendChannel = &CommonRecommendChannel{}
			}
			if err := m.RecommendChannel.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FunType", wireType)
			}
			m.FunType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FunType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recommend_channel")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fun_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddCommonRecommendResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddCommonRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddCommonRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveCommonRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveCommonRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveCommonRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendChannel", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.RecommendChannel == nil {
				m.RecommendChannel = &CommonRecommendChannel{}
			}
			if err := m.RecommendChannel.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FunType", wireType)
			}
			m.FunType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FunType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("recommend_channel")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fun_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveCommonRecommendResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveCommonRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveCommonRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommonRecommendReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommonRecommendReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommonRecommendReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FunType", wireType)
			}
			m.FunType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FunType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fun_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommonRecommendResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommonRecommendResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommonRecommendResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RecommendChannelList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RecommendChannelList = append(m.RecommendChannelList, &CommonRecommendChannel{})
			if err := m.RecommendChannelList[len(m.RecommendChannelList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RandomChannelInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RandomChannelInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RandomChannelInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BlockTag", wireType)
			}
			m.BlockTag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BlockTag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("block_tag")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddRandomRecommendChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddRandomRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddRandomRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddRandomRecommendChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddRandomRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddRandomRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRandomRecommendChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRandomRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRandomRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRandomRecommendChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRandomRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRandomRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveRandomRecommendChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveRandomRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveRandomRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveRandomRecommendChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveRandomRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveRandomRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTypeByTagReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTypeByTagReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTypeByTagReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelTypeByTagResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelTypeByTagResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelTypeByTagResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagType", wireType)
			}
			m.TagType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTagInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTagInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TagId", wireType)
			}
			m.TagId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TagId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("tag_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTagInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTagInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTagInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubTagInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SubTagInfo == nil {
				m.SubTagInfo = &SubTag{}
			}
			if err := m.SubTagInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sub_tag_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMajorLiveChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMajorLiveChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMajorLiveChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMajorLiveChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMajorLiveChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMajorLiveChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LiveChannelEx) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LiveChannelEx: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LiveChannelEx: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreIdx", wireType)
			}
			m.ScoreIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScoreIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasAdmin", wireType)
			}
			m.HasAdmin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HasAdmin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.HoursToAddList = append(m.HoursToAddList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.HoursToAddList = append(m.HoursToAddList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoursToAddList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score_idx")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLiveRecommendChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLiveRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLiveRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScoreIdx", wireType)
			}
			m.ScoreIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ScoreIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.HoursToAddList = append(m.HoursToAddList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.HoursToAddList = append(m.HoursToAddList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field HoursToAddList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score_idx")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLiveRecommendChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLiveRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLiveRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelLiveRecommendChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelLiveRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelLiveRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelLiveRecommendChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelLiveRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelLiveRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveRecommendChannelReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveRecommendChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveRecommendChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveRecommendChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveRecommendChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveRecommendChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelExList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ChannelExList = append(m.ChannelExList, &LiveChannelEx{})
			if err := m.ChannelExList[len(m.ChannelExList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveChannelListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveChannelListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveChannelListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			m.Start = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Start |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLiveChannelListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLiveChannelListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLiveChannelListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelrecommend
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelrecommend
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReachEnd", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ReachEnd = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserCount", wireType)
			}
			m.UserCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelrecommend(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelrecommend
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("reach_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelrecommend(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelrecommend
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelrecommend
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelrecommend
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelrecommend
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelrecommend(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelrecommend = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelrecommend   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channelrecommendsvr/channelrecommend.proto", fileDescriptorChannelrecommend)
}

var fileDescriptorChannelrecommend = []byte{
	// 4442 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x73, 0x23, 0x49,
	0x56, 0xef, 0x92, 0xdc, 0xb6, 0xfc, 0xfc, 0x25, 0xa7, 0xdd, 0x6e, 0x5b, 0xdd, 0x6e, 0xab, 0xb3,
	0x3f, 0xc6, 0xfd, 0x61, 0xf7, 0x4c, 0xc7, 0xc6, 0x46, 0xaf, 0xf0, 0x78, 0x46, 0x56, 0xcb, 0x6a,
	0xc5, 0xba, 0xed, 0x0e, 0x59, 0xed, 0xdd, 0x81, 0x60, 0x2a, 0xca, 0xaa, 0xb2, 0x5d, 0x6b, 0x49,
	0x55, 0x28, 0x4b, 0x1e, 0x99, 0x60, 0x60, 0x39, 0xb0, 0xdb, 0xb0, 0x2c, 0x33, 0xb1, 0x40, 0x2c,
	0x01, 0x0c, 0x6c, 0x10, 0x03, 0xc1, 0x1d, 0x4e, 0x1c, 0x38, 0xef, 0x81, 0x00, 0x6e, 0x9c, 0x20,
	0x88, 0x81, 0xc3, 0xfc, 0x01, 0x9c, 0x38, 0x11, 0x99, 0xf5, 0x95, 0x55, 0x95, 0x55, 0x2a, 0xf7,
	0xf6, 0x10, 0x73, 0xb2, 0xfc, 0x32, 0x2b, 0xf3, 0x97, 0x2f, 0xdf, 0x57, 0xbe, 0xf7, 0x60, 0x9d,
	0xf4, 0x5a, 0x8f, 0x5a, 0x27, 0x4a, 0xb7, 0xab, 0xb5, 0x7b, 0x5a, 0xcb, 0xe8, 0x74, 0xb4, 0xae,
	0x4a, 0xce, 0x7a, 0x11, 0xda, 0xba, 0xd9, 0x33, 0x2c, 0x03, 0xe5, 0xc3, 0xf4, 0xc2, 0x6d, 0xfa,
	0xc3, 0xe8, 0x3e, 0xb2, 0xda, 0x67, 0xa6, 0xde, 0x3a, 0x6d, 0x6b, 0x8f, 0xc8, 0xe9, 0x61, 0x5f,
	0x6f, 0x5b, 0x7a, 0xd7, 0x3a, 0x37, 0x35, 0xfb, 0x3b, 0xfc, 0x47, 0x19, 0x98, 0x7b, 0x49, 0xb4,
	0x5e, 0xb5, 0x6b, 0x69, 0xbd, 0x8a, 0xbd, 0x46, 0x43, 0xfb, 0x35, 0x74, 0x0b, 0xc0, 0x59, 0x51,
	0xd6, 0xd5, 0x45, 0xa9, 0x98, 0x59, 0x9d, 0xda, 0x1a, 0xf9, 0xf9, 0x7f, 0xac, 0x5c, 0x6a, 0x8c,
	0x3b, 0xf4, 0xba, 0x4a, 0x27, 0xf5, 0x89, 0xd6, 0x93, 0x5b, 0x46, 0xbf, 0x6b, 0x2d, 0x66, 0xf8,
	0x49, 0x94, 0x5e, 0xa1, 0x64, 0xb4, 0x02, 0x39, 0x9d, 0xc8, 0x8a, 0xda, 0xd1, 0xbb, 0x8b, 0xd9,
	0xa2, 0xb4, 0x9a, 0x73, 0xa6, 0x8c, 0xe9, 0xa4, 0x4c, 0x89, 0xce, 0x04, 0x8d, 0x02, 0x58, 0x1c,
	0x09, 0x4e, 0x60, 0xa8, 0xd0, 0x1d, 0x98, 0x60, 0x9f, 0x3b, 0xfb, 0x5c, 0x2e, 0x4a, 0xde, 0x3e,
	0xc0, 0x06, 0xec, 0x8d, 0x6e, 0xc2, 0xb8, 0x4e, 0xe4, 0xae, 0xd1, 0xeb, 0x28, 0xed, 0xc5, 0x51,
	0x6e, 0xa1, 0x9c, 0x4e, 0x76, 0x19, 0x15, 0xbd, 0x05, 0x93, 0xee, 0xa9, 0x28, 0x0f, 0x16, 0xc7,
	0xb8, 0xa5, 0x26, 0x9c, 0x91, 0xe6, 0xb9, 0xa9, 0xe1, 0x05, 0x98, 0x8f, 0x72, 0x85, 0x98, 0xf8,
	0x57, 0x61, 0xb6, 0xac, 0xaa, 0x0e, 0xa5, 0xa6, 0x1f, 0x59, 0x17, 0xe1, 0xd5, 0xb1, 0x7e, 0x64,
	0xc9, 0x67, 0x4a, 0xbb, 0xaf, 0x05, 0x79, 0x45, 0xe9, 0x07, 0x94, 0x8c, 0xe7, 0x01, 0x85, 0x97,
	0x27, 0x26, 0xfe, 0x03, 0x09, 0x16, 0x6a, 0x9a, 0xd5, 0x70, 0xaf, 0x96, 0xbb, 0xa6, 0x05, 0xc8,
	0xf6, 0x43, 0x7b, 0x52, 0x02, 0x2a, 0xc0, 0x65, 0x62, 0x29, 0xbd, 0xe0, 0xa5, 0xd8, 0x24, 0x3a,
	0x66, 0x33, 0x32, 0xcb, 0x8f, 0x31, 0x12, 0xba, 0x0d, 0x13, 0x94, 0x87, 0xda, 0x47, 0x32, 0xbd,
	0xc0, 0xc5, 0x91, 0x62, 0xc6, 0xe3, 0xe2, 0xb8, 0x4e, 0x76, 0xb5, 0x8f, 0x28, 0x57, 0xf0, 0xdf,
	0x4b, 0x30, 0xe1, 0x80, 0xa8, 0x77, 0x8f, 0x8c, 0x74, 0x0c, 0x58, 0x80, 0xac, 0xa5, 0x1c, 0x2f,
	0x66, 0x38, 0x96, 0x53, 0x02, 0xbb, 0xfe, 0xee, 0x91, 0x21, 0xd3, 0x41, 0x2a, 0x1f, 0xe3, 0xde,
	0xf5, 0x77, 0x8f, 0x8c, 0xa6, 0x3d, 0xe1, 0xf0, 0x54, 0x6e, 0x19, 0x6d, 0xc3, 0x96, 0x0f, 0x6f,
	0xc2, 0xe1, 0x69, 0x85, 0x12, 0xd1, 0x5d, 0x98, 0x64, 0x2b, 0x68, 0x03, 0xf9, 0xa8, 0xad, 0x1c,
	0x07, 0x05, 0x84, 0x8e, 0x54, 0x07, 0xdb, 0x6d, 0xe5, 0x18, 0xff, 0xbb, 0x04, 0x57, 0x85, 0x7c,
	0x24, 0x26, 0xba, 0x0b, 0x33, 0xfe, 0x11, 0xe4, 0xb6, 0x4e, 0xac, 0x45, 0xa9, 0x98, 0x5d, 0x9d,
	0x6a, 0x4c, 0x79, 0x27, 0xd8, 0xd1, 0x09, 0x13, 0xb2, 0x9e, 0xa6, 0xb4, 0x4e, 0x64, 0xad, 0xab,
	0x32, 0xe6, 0x7a, 0x42, 0xc6, 0xc8, 0xd5, 0xae, 0x8a, 0xea, 0x30, 0xeb, 0x2d, 0x45, 0x61, 0xb1,
	0xc5, 0xb2, 0xc5, 0xec, 0xea, 0xc4, 0xe3, 0xe5, 0xf5, 0x88, 0xfa, 0x72, 0x7c, 0x6c, 0xb8, 0x10,
	0xe8, 0x3f, 0x6c, 0xb7, 0x87, 0x30, 0xd7, 0x51, 0xce, 0xe5, 0x23, 0x7d, 0x20, 0x5b, 0x86, 0xe9,
	0x21, 0x1b, 0x61, 0xc8, 0x66, 0x3a, 0xca, 0xf9, 0xb6, 0x3e, 0x68, 0x1a, 0xa6, 0x8d, 0x0d, 0xbf,
	0x07, 0x57, 0x6a, 0x9a, 0xe5, 0x2c, 0xd8, 0x54, 0x8e, 0x19, 0x77, 0xa8, 0x94, 0xa4, 0x3c, 0x1c,
	0x6e, 0x31, 0x39, 0x8b, 0x2c, 0x40, 0x4c, 0xf1, 0x99, 0xa4, 0xd7, 0x39, 0x13, 0xfe, 0x18, 0xa6,
	0x7c, 0x19, 0xbf, 0x00, 0x3a, 0x74, 0x0d, 0x46, 0x99, 0x21, 0x09, 0xf2, 0xfd, 0x32, 0x35, 0x23,
	0x6a, 0x44, 0xb3, 0x79, 0xd9, 0x0e, 0x68, 0x76, 0x1e, 0xa6, 0xf9, 0xed, 0x89, 0x89, 0x9f, 0xc0,
	0x94, 0x7f, 0x6a, 0x0a, 0x28, 0xbc, 0x96, 0x14, 0xb7, 0xd6, 0x13, 0x98, 0xe6, 0xbf, 0x4c, 0x2f,
	0x46, 0x58, 0x86, 0x99, 0x8a, 0xbf, 0x50, 0x7a, 0x25, 0x0a, 0x43, 0xcb, 0xc4, 0x41, 0x7b, 0x1f,
	0x16, 0xb6, 0x14, 0xab, 0x75, 0xc2, 0xdd, 0xe7, 0xb9, 0xa9, 0x5d, 0x44, 0x18, 0x4e, 0xe0, 0xaa,
	0x70, 0x05, 0x62, 0xa2, 0xe7, 0xbe, 0x34, 0x50, 0x14, 0xbc, 0x34, 0xdc, 0x8c, 0x95, 0x06, 0xf7,
	0xa0, 0x9e, 0x44, 0x50, 0x02, 0xdb, 0xe9, 0x1f, 0x32, 0x30, 0xc5, 0x89, 0x4c, 0x75, 0x90, 0x8e,
	0x17, 0x2b, 0x90, 0x63, 0x06, 0x4d, 0xb6, 0x48, 0x80, 0x0f, 0x63, 0x8c, 0xda, 0x24, 0x54, 0x60,
	0xb4, 0xae, 0x4a, 0x87, 0x03, 0x96, 0x4e, 0xeb, 0xaa, 0x4d, 0x82, 0x16, 0x61, 0x44, 0xd5, 0x48,
	0x8b, 0x99, 0x38, 0xd7, 0xa2, 0x30, 0x4a, 0xc8, 0xab, 0xf1, 0xc6, 0x84, 0xf3, 0x6a, 0xf7, 0x60,
	0xf6, 0xc4, 0xe8, 0xf7, 0x88, 0x6c, 0x19, 0x54, 0x24, 0x6d, 0x16, 0x8c, 0x32, 0x3e, 0x4e, 0xb3,
	0x81, 0xa6, 0x51, 0x56, 0x6d, 0xb9, 0x2d, 0xc0, 0x65, 0x6a, 0x96, 0x48, 0xc0, 0xdb, 0xd8, 0xa4,
	0x80, 0xf1, 0xcb, 0x0d, 0x33, 0x7e, 0xe3, 0x02, 0xe3, 0x87, 0x7f, 0x28, 0xc1, 0xdc, 0xbe, 0x77,
	0x45, 0x36, 0xff, 0x2e, 0x22, 0xc4, 0xa8, 0xe6, 0xcb, 0x83, 0x36, 0xb0, 0xcf, 0x91, 0x61, 0x57,
	0xb9, 0x92, 0xa8, 0xd8, 0xd5, 0x81, 0x27, 0x30, 0xd5, 0x01, 0xbb, 0xc6, 0x05, 0x98, 0x8f, 0x02,
	0x21, 0x26, 0x3e, 0x82, 0xb9, 0x5a, 0x2a, 0x80, 0x62, 0x5f, 0x2c, 0x12, 0xd8, 0x8c, 0x58, 0xa7,
	0xe6, 0x6b, 0x82, 0xfd, 0xdf, 0xdc, 0x01, 0x4b, 0x90, 0x6f, 0x68, 0x1d, 0xe3, 0x4c, 0xbb, 0xb8,
	0xf1, 0xc2, 0x73, 0x30, 0x1b, 0xfa, 0x96, 0x98, 0x58, 0x87, 0x85, 0x00, 0x71, 0xeb, 0xfc, 0x82,
	0x4a, 0x9a, 0xde, 0x1e, 0x2c, 0xc1, 0x55, 0xe1, 0x56, 0xc4, 0xa4, 0x6e, 0xa3, 0xac, 0xaa, 0xd4,
	0xb1, 0xef, 0xe8, 0xa7, 0xaf, 0x73, 0xb6, 0x45, 0x58, 0x10, 0x2d, 0x40, 0x4c, 0xbc, 0x19, 0xbe,
	0x92, 0xa6, 0x72, 0x7c, 0x91, 0x95, 0x7f, 0x26, 0xc1, 0x74, 0xf0, 0xeb, 0xd4, 0xa6, 0xc1, 0x53,
	0xab, 0xcc, 0x30, 0xb5, 0xca, 0x8a, 0x62, 0x8a, 0x30, 0x63, 0x47, 0xe2, 0x22, 0xc5, 0x53, 0xde,
	0xe9, 0x7a, 0x47, 0x24, 0x26, 0x6a, 0xc0, 0x3c, 0xef, 0x32, 0x2d, 0xe5, 0x98, 0xb7, 0x93, 0xc5,
	0x44, 0xd9, 0xa3, 0x6b, 0x20, 0xce, 0x71, 0x5a, 0xca, 0x31, 0xe3, 0xc7, 0x00, 0xf2, 0x35, 0xcd,
	0x7a, 0x66, 0x58, 0x5f, 0x61, 0x08, 0x38, 0x0f, 0x19, 0x8b, 0x04, 0xce, 0x9b, 0xb1, 0x08, 0xfe,
	0xc4, 0xbf, 0x89, 0x67, 0x86, 0x95, 0xde, 0x61, 0xdd, 0x87, 0x69, 0x55, 0x39, 0x97, 0x03, 0xa1,
	0xaf, 0xbf, 0xf2, 0xa4, 0xaa, 0x9c, 0xd7, 0xdc, 0xe8, 0x17, 0x3d, 0x84, 0x99, 0x9e, 0xd2, 0x3d,
	0xe5, 0x27, 0x67, 0xb9, 0xc9, 0x53, 0x74, 0xd0, 0x9b, 0x8d, 0x3f, 0x84, 0xd9, 0x10, 0x2f, 0xde,
	0x68, 0x18, 0x87, 0xab, 0xcc, 0xae, 0xfa, 0xeb, 0x57, 0x8e, 0x98, 0xe8, 0xae, 0x43, 0xde, 0x32,
	0x2c, 0xa5, 0xcd, 0xa3, 0xe4, 0xcf, 0x3e, 0xcd, 0x46, 0x7d, 0x98, 0x57, 0x98, 0xf5, 0x0b, 0x2f,
	0x83, 0xb7, 0x99, 0x66, 0x84, 0xc8, 0xc4, 0xbc, 0xf0, 0xf2, 0x2a, 0x33, 0xba, 0xfe, 0x3a, 0xae,
	0x01, 0x79, 0xb3, 0xd1, 0xc4, 0x87, 0x30, 0xed, 0x6f, 0xf1, 0x15, 0x44, 0x2b, 0x15, 0x58, 0x72,
	0x63, 0x8d, 0xe8, 0x51, 0xd2, 0x1a, 0x0b, 0x05, 0x0a, 0x71, 0x8b, 0x10, 0x13, 0x55, 0x7c, 0x2c,
	0xc9, 0x6a, 0x18, 0x3c, 0xa8, 0x87, 0x93, 0x6d, 0x51, 0x85, 0x42, 0x60, 0x75, 0x4a, 0xf4, 0x8d,
	0x76, 0xea, 0xb8, 0xf1, 0x10, 0xae, 0xc5, 0x2e, 0xf3, 0xa6, 0xa0, 0xbe, 0x07, 0x57, 0x18, 0x37,
	0x9e, 0x32, 0x85, 0x7d, 0x0d, 0xab, 0xfe, 0x0c, 0xc6, 0x99, 0x29, 0x64, 0xd7, 0xbd, 0x02, 0x39,
	0x6a, 0xc0, 0xba, 0x4a, 0xc7, 0x3e, 0x96, 0x67, 0x2f, 0x2d, 0xe5, 0x78, 0x57, 0xe9, 0x68, 0xb6,
	0x45, 0xa1, 0xd6, 0x34, 0xc3, 0x8d, 0xda, 0x24, 0x6c, 0xc0, 0x04, 0x0d, 0xb9, 0xdd, 0xd7, 0x48,
	0x05, 0x66, 0x18, 0x3d, 0xf2, 0x92, 0xb8, 0x26, 0xb0, 0x89, 0x2e, 0x82, 0xc6, 0x54, 0xcb, 0xfd,
	0x39, 0xf4, 0x31, 0x80, 0xa7, 0x61, 0xd2, 0xdf, 0x90, 0x98, 0x34, 0xe6, 0xaf, 0x69, 0x56, 0xb9,
	0xdd, 0x76, 0x31, 0xe0, 0x03, 0x98, 0x09, 0x50, 0x18, 0xd7, 0x7f, 0x71, 0x58, 0xf8, 0x6f, 0x33,
	0x90, 0xab, 0x28, 0x3d, 0x95, 0x31, 0xed, 0x26, 0x8c, 0xb7, 0x94, 0x9e, 0x1a, 0xe5, 0x5a, 0x8e,
	0x92, 0x19, 0xdb, 0xa8, 0x1a, 0xd1, 0x29, 0x7a, 0x47, 0x39, 0xd6, 0x02, 0xbc, 0x63, 0x9f, 0xd6,
	0x29, 0x99, 0x4e, 0x3a, 0x54, 0x5a, 0xa7, 0xce, 0xa4, 0x2c, 0x3f, 0x89, 0xd2, 0xed, 0x49, 0x77,
	0x60, 0xa2, 0xd5, 0xd6, 0xb5, 0xae, 0x25, 0xb7, 0xf5, 0xee, 0x69, 0x20, 0xac, 0x05, 0x7b, 0x60,
	0x47, 0xef, 0x9e, 0x52, 0xbe, 0xd1, 0x8b, 0xd4, 0xd5, 0x40, 0x60, 0x7b, 0xd9, 0x52, 0x8e, 0x23,
	0xf9, 0x9c, 0x51, 0x71, 0xe4, 0xbb, 0x0c, 0x63, 0xc7, 0x4a, 0x47, 0xa3, 0x4b, 0xf0, 0x01, 0xed,
	0x28, 0x25, 0xda, 0x3a, 0x4f, 0x9f, 0xab, 0xa6, 0x41, 0x74, 0x4b, 0x37, 0xba, 0x2c, 0xaa, 0x75,
	0xaf, 0x67, 0xe2, 0x48, 0x1f, 0xbc, 0x70, 0x06, 0xf0, 0xef, 0x4a, 0x30, 0xd6, 0x30, 0x0c, 0xcb,
	0x71, 0xc7, 0xc9, 0xe2, 0xe5, 0xc3, 0x0e, 0x78, 0x33, 0x1b, 0xf6, 0x0a, 0xe4, 0x5a, 0x4a, 0xbb,
	0x25, 0x93, 0xfe, 0x61, 0xc0, 0x61, 0x8c, 0x51, 0xea, 0x7e, 0xff, 0xd0, 0x5d, 0x3e, 0xe2, 0xc8,
	0xe9, 0xf2, 0x4c, 0x21, 0xff, 0x31, 0x03, 0xa3, 0xfb, 0xfd, 0xc3, 0x54, 0x50, 0x16, 0x61, 0x44,
	0x6f, 0x19, 0xdd, 0xc0, 0x65, 0x31, 0x0a, 0x07, 0x32, 0x1b, 0x05, 0xb9, 0x0c, 0x63, 0x3a, 0x91,
	0xc9, 0x89, 0xf1, 0x51, 0x20, 0xc9, 0x35, 0xaa, 0x93, 0xfd, 0x13, 0xe3, 0x23, 0xba, 0xed, 0xf7,
	0xfa, 0x1d, 0x53, 0xee, 0xf7, 0xda, 0xec, 0x66, 0xbc, 0x6d, 0x29, 0xf5, 0x65, 0xaf, 0x8d, 0x6e,
	0xc3, 0x44, 0xcf, 0x30, 0x2c, 0xd9, 0xd9, 0x21, 0x70, 0x39, 0x3d, 0x9b, 0x8d, 0x36, 0x2b, 0xbc,
	0xb8, 0x66, 0x4c, 0x14, 0xd7, 0xac, 0x40, 0x4e, 0x1b, 0x58, 0x76, 0x9e, 0x24, 0xc7, 0xb3, 0x42,
	0x1b, 0x58, 0x2c, 0x49, 0xf2, 0x0d, 0x00, 0x9b, 0x13, 0xf4, 0x3f, 0x34, 0x0f, 0xf9, 0xfd, 0xe6,
	0xb6, 0xfc, 0xac, 0xfe, 0xb4, 0x2a, 0xef, 0x57, 0x9b, 0xcd, 0xfa, 0x6e, 0x2d, 0x2f, 0xa1, 0x3c,
	0x4c, 0x7a, 0xd4, 0x66, 0x79, 0x2b, 0x9f, 0xc1, 0xbf, 0x01, 0xb9, 0xa6, 0xcd, 0x4b, 0x82, 0xbe,
	0x01, 0x39, 0x17, 0x29, 0xe3, 0xe0, 0xc4, 0xe3, 0xa5, 0xa8, 0x06, 0x39, 0x37, 0xdf, 0x18, 0x73,
	0xb0, 0xa3, 0x12, 0x4c, 0x92, 0xfe, 0xa1, 0xec, 0x85, 0x49, 0x76, 0x88, 0xbe, 0x18, 0xfd, 0xd2,
	0x46, 0xd7, 0x00, 0xc2, 0xfe, 0x32, 0xad, 0x43, 0x2c, 0x2c, 0x2a, 0xb7, 0xdb, 0x0e, 0x06, 0xaa,
	0xe1, 0x2f, 0x59, 0x78, 0xc0, 0xd3, 0x88, 0x89, 0xde, 0x87, 0x69, 0x57, 0x10, 0x08, 0xaf, 0xe2,
	0x85, 0xe8, 0x36, 0xee, 0x71, 0x1a, 0x93, 0x8e, 0x90, 0x10, 0xb6, 0x95, 0x0e, 0xc8, 0x0f, 0xf7,
	0xb6, 0xce, 0x9d, 0x78, 0xd6, 0xbf, 0x79, 0x29, 0x7a, 0xf3, 0xaf, 0x19, 0x88, 0xe1, 0xdf, 0x91,
	0xf8, 0x87, 0x93, 0xb3, 0xd7, 0x9b, 0x4d, 0x55, 0x05, 0x15, 0x3e, 0x2b, 0x54, 0x78, 0xfa, 0x74,
	0xf1, 0x61, 0x50, 0xe3, 0x46, 0xd9, 0xfb, 0xdf, 0x12, 0xcf, 0x08, 0x9b, 0x6a, 0x33, 0xd8, 0xb6,
	0x67, 0x21, 0x1b, 0x2a, 0x60, 0xb0, 0x6b, 0x26, 0x1b, 0x93, 0x2d, 0xe7, 0x17, 0x43, 0x5d, 0x82,
	0x49, 0x66, 0x5e, 0x1c, 0x61, 0x60, 0xc0, 0x13, 0xe5, 0x80, 0xce, 0x76, 0x74, 0xf7, 0x36, 0x4c,
	0xb0, 0x6f, 0x8d, 0xae, 0x6c, 0x19, 0x26, 0xe3, 0xa9, 0x97, 0xbd, 0xa4, 0x03, 0x7b, 0xdd, 0xa6,
	0x61, 0xd2, 0x90, 0xf4, 0x44, 0x21, 0xf2, 0x99, 0x4e, 0x74, 0x4b, 0xa6, 0xe4, 0x40, 0x9a, 0x73,
	0xf2, 0x44, 0x21, 0x07, 0x74, 0xa8, 0xa6, 0x74, 0x34, 0xbc, 0x01, 0x57, 0x1a, 0xda, 0x51, 0x4f,
	0x23, 0x27, 0x6e, 0x3c, 0xa1, 0x77, 0x52, 0xc7, 0x57, 0xb8, 0x4a, 0xdf, 0x77, 0xd1, 0xaf, 0x89,
	0x89, 0x1e, 0xc0, 0x74, 0x4f, 0xeb, 0x28, 0x7a, 0x57, 0x26, 0x5a, 0xcb, 0xe8, 0xaa, 0x24, 0xb0,
	0xc4, 0x94, 0x3d, 0xb6, 0x6f, 0x0f, 0xe1, 0x77, 0xf9, 0xb4, 0x9c, 0xbb, 0xe0, 0xd3, 0xd4, 0x28,
	0xb6, 0x59, 0xd6, 0x33, 0xfa, 0xf9, 0x45, 0x61, 0x1c, 0x00, 0xda, 0xe7, 0xb3, 0x83, 0x75, 0x35,
	0x75, 0xa0, 0x99, 0x64, 0xbe, 0xf1, 0xb7, 0xf8, 0x04, 0x86, 0xb3, 0x2e, 0x31, 0x11, 0x06, 0x30,
	0xda, 0xaa, 0xec, 0xe9, 0x95, 0x2f, 0x9b, 0x39, 0xa3, 0xad, 0x36, 0x9d, 0x4f, 0x51, 0xed, 0xf5,
	0x20, 0x61, 0x8d, 0x57, 0x2e, 0x7f, 0xd7, 0x77, 0x60, 0xcc, 0x95, 0x3c, 0x69, 0x88, 0xe4, 0x8d,
	0xda, 0x16, 0xc8, 0x09, 0x45, 0x88, 0x66, 0x85, 0x43, 0x91, 0x7d, 0xcd, 0xc2, 0xa6, 0x97, 0xda,
	0x6a, 0x2a, 0xc7, 0x65, 0xf5, 0x8c, 0xfa, 0x01, 0x53, 0x6f, 0x31, 0x3b, 0xcf, 0xbb, 0x97, 0x51,
	0x53, 0x6f, 0x51, 0x33, 0xbf, 0x0c, 0x63, 0x8a, 0x7a, 0xc6, 0x86, 0x79, 0x07, 0x33, 0xaa, 0xa8,
	0x67, 0x74, 0xf8, 0x26, 0x8c, 0x93, 0x96, 0xd1, 0xa3, 0xde, 0x77, 0x10, 0xd0, 0xd7, 0x1c, 0x23,
	0xd7, 0xd5, 0x01, 0xfe, 0x73, 0x09, 0xe6, 0xfc, 0x0c, 0xa7, 0xbd, 0x2b, 0xe5, 0x4a, 0x19, 0xa8,
	0x25, 0x93, 0xe9, 0xea, 0x9c, 0x62, 0xc6, 0xe7, 0x40, 0x9c, 0x2f, 0xc1, 0x62, 0x7f, 0x5d, 0x8b,
	0xd2, 0x6a, 0x6b, 0x4a, 0x57, 0x36, 0xda, 0x2a, 0x7b, 0xc7, 0x79, 0x16, 0x85, 0x91, 0xf7, 0xda,
	0xcc, 0x01, 0xd1, 0x1d, 0x9c, 0x1c, 0x2c, 0xe7, 0x5f, 0x14, 0xf5, 0xcc, 0xad, 0xac, 0x44, 0xd1,
	0x11, 0x13, 0x93, 0x50, 0x2e, 0xc4, 0x07, 0xfe, 0x04, 0xc6, 0x1c, 0xe0, 0xce, 0x95, 0x0c, 0xc5,
	0x3c, 0x6a, 0x63, 0x0e, 0x80, 0xc9, 0x88, 0xc0, 0x84, 0xb3, 0x22, 0x1c, 0x9e, 0x6f, 0x86, 0xe4,
	0xc3, 0x01, 0xc3, 0x2f, 0x29, 0x89, 0x96, 0xfc, 0x80, 0x4f, 0x79, 0xf8, 0xeb, 0xbd, 0x01, 0xf6,
	0xe3, 0x17, 0x30, 0x45, 0xbd, 0x92, 0x61, 0x3a, 0x53, 0xd2, 0xe9, 0x1e, 0x75, 0x3f, 0x54, 0x36,
	0x42, 0xee, 0x87, 0x92, 0xf0, 0xc7, 0x4c, 0x54, 0x02, 0x8b, 0xd2, 0x43, 0xd6, 0xe9, 0x23, 0xd4,
	0x94, 0x05, 0x8f, 0x90, 0x15, 0xb1, 0xa3, 0xf4, 0xbf, 0x9e, 0xb6, 0xbc, 0xdf, 0x6e, 0x9c, 0x1e,
	0xaf, 0xf9, 0xb6, 0x2c, 0x84, 0xb6, 0x27, 0x26, 0x7e, 0xcc, 0x78, 0x1f, 0x81, 0x95, 0xe4, 0x65,
	0xb1, 0xc2, 0xf8, 0x1e, 0x59, 0xeb, 0x0d, 0x9e, 0x05, 0xbf, 0xef, 0x8a, 0x68, 0x04, 0x59, 0xda,
	0x37, 0x95, 0x27, 0x6f, 0xd1, 0x33, 0xbf, 0x0b, 0x0b, 0x15, 0x56, 0xb0, 0x0d, 0x97, 0xa7, 0xd2,
	0x99, 0xb3, 0x4f, 0x24, 0x96, 0xc5, 0x0b, 0x2d, 0x41, 0xb1, 0xbd, 0x84, 0x59, 0xef, 0x80, 0x2e,
	0x1b, 0x1c, 0x45, 0x5a, 0x15, 0xbd, 0x6c, 0x44, 0x18, 0x1a, 0xf9, 0x5e, 0x18, 0xd5, 0x0a, 0xe4,
	0x8e, 0xfa, 0xdd, 0xe8, 0xbb, 0x7e, 0xec, 0xa8, 0xdf, 0x65, 0x8a, 0x60, 0x67, 0x05, 0x23, 0x80,
	0x88, 0x89, 0x7f, 0x22, 0xc1, 0xa2, 0xa3, 0x76, 0x5f, 0x1f, 0xb8, 0xd7, 0x60, 0x29, 0x06, 0x13,
	0x2b, 0x11, 0xb1, 0x24, 0x5f, 0x14, 0x2d, 0xbf, 0xac, 0x24, 0x5a, 0x76, 0x60, 0xfb, 0xee, 0xe8,
	0x9a, 0xe8, 0x43, 0x58, 0x88, 0x1c, 0x94, 0x17, 0xcf, 0xf4, 0xa7, 0x9d, 0x0f, 0x9f, 0x96, 0xc9,
	0xda, 0xaf, 0xc0, 0x6c, 0x43, 0xe9, 0xaa, 0x46, 0xe7, 0xc2, 0x69, 0x9b, 0x9b, 0x30, 0x7e, 0xd8,
	0x36, 0x5a, 0xa7, 0x5e, 0xfc, 0xe5, 0x39, 0x19, 0x46, 0x6e, 0x2a, 0xc7, 0xb8, 0x02, 0xd7, 0xcb,
	0xaa, 0x6a, 0xaf, 0x2f, 0xaa, 0x4b, 0xa7, 0x92, 0xd9, 0x15, 0x58, 0x4e, 0x58, 0x84, 0x98, 0xb8,
	0x04, 0xd7, 0x6b, 0x9a, 0x15, 0xbf, 0x8b, 0x17, 0x3d, 0x4b, 0xd1, 0xe8, 0xb9, 0x06, 0xcb, 0x09,
	0xdf, 0x5e, 0xa0, 0x54, 0xb7, 0x0d, 0x2b, 0xb6, 0x60, 0xfc, 0x82, 0xa7, 0xc5, 0x50, 0x4c, 0x5e,
	0x87, 0x98, 0xf8, 0x97, 0x03, 0x05, 0xd8, 0x73, 0x53, 0x13, 0xbe, 0x30, 0x44, 0xef, 0x76, 0x6e,
	0x7f, 0xde, 0xd3, 0x71, 0xfb, 0x7f, 0x87, 0x0f, 0x03, 0xb9, 0xb5, 0x89, 0x19, 0x78, 0x1f, 0x07,
	0xa4, 0xd8, 0x79, 0xfa, 0x24, 0x5b, 0xf1, 0x87, 0xac, 0x7e, 0x4a, 0x43, 0x28, 0x1a, 0xcd, 0x0f,
	0xb3, 0xd3, 0x3b, 0x2c, 0x17, 0xe3, 0xcd, 0x26, 0x26, 0xff, 0xf2, 0xa3, 0xcf, 0x86, 0xa1, 0x71,
	0x97, 0xf3, 0xf2, 0xa3, 0xdf, 0xe3, 0xb7, 0x19, 0xc3, 0x9e, 0x2b, 0xdf, 0x33, 0x7a, 0x3b, 0x7a,
	0xa0, 0x30, 0x13, 0x93, 0x16, 0xc7, 0x65, 0xc6, 0x86, 0xe8, 0x17, 0x17, 0x90, 0x88, 0xbf, 0x96,
	0x60, 0x8a, 0xfb, 0x36, 0x6d, 0xbd, 0x32, 0x10, 0xbb, 0x05, 0xd4, 0xca, 0x8d, 0xdd, 0xe8, 0x14,
	0xfa, 0x34, 0xf1, 0x9b, 0x65, 0xbc, 0x29, 0x27, 0x8a, 0xd3, 0x2d, 0x23, 0x2c, 0x3c, 0x8e, 0x88,
	0x0a, 0x8f, 0xd4, 0x27, 0x14, 0xd8, 0xef, 0x33, 0xed, 0x75, 0xa5, 0x36, 0x0d, 0x68, 0x21, 0xa2,
	0xac, 0x10, 0xd1, 0x32, 0x5c, 0x8b, 0x05, 0x44, 0x4c, 0x5c, 0x86, 0xc2, 0x53, 0x6a, 0xbd, 0x5e,
	0x1f, 0x2f, 0xdd, 0x21, 0x76, 0x09, 0x62, 0xe2, 0xeb, 0x2c, 0x81, 0x1b, 0xb3, 0x03, 0x3e, 0x62,
	0x79, 0xd9, 0xb8, 0x8f, 0x45, 0x85, 0xc4, 0xd8, 0x48, 0x22, 0x20, 0x1f, 0xe1, 0x42, 0xe2, 0x1e,
	0x73, 0x27, 0xdc, 0x14, 0x4a, 0x75, 0x0c, 0x9a, 0x9d, 0x2a, 0x90, 0x12, 0x52, 0x05, 0x99, 0xa8,
	0xb1, 0x7b, 0x65, 0x77, 0x08, 0x45, 0x56, 0xfc, 0xff, 0xcf, 0x16, 0xdc, 0xff, 0x27, 0x89, 0x6f,
	0xb0, 0x60, 0xd6, 0x63, 0x09, 0xae, 0x94, 0x2b, 0x4d, 0x79, 0x6f, 0x7b, 0xbb, 0x5e, 0xa9, 0x97,
	0x77, 0xe4, 0x72, 0xa5, 0x59, 0x3f, 0xa8, 0x37, 0x3f, 0xc8, 0x4b, 0x68, 0x16, 0xa6, 0xe8, 0xd0,
	0xd6, 0x4e, 0xb9, 0xf2, 0xed, 0x9d, 0xfa, 0x7e, 0x33, 0x9f, 0x41, 0x05, 0x58, 0x08, 0xcc, 0x6e,
	0x54, 0x2b, 0x7b, 0xcf, 0x9f, 0x57, 0x77, 0x9f, 0xe6, 0xb3, 0xe8, 0x0a, 0xcc, 0xd2, 0xb1, 0xed,
	0xfa, 0x77, 0x39, 0xf2, 0x08, 0x9a, 0x83, 0x19, 0x4a, 0xf6, 0x48, 0x07, 0x8f, 0xf3, 0x97, 0xd1,
	0x02, 0x20, 0x4a, 0xdc, 0xa9, 0x1f, 0x54, 0xb9, 0xc9, 0xa3, 0x2e, 0x1a, 0x8f, 0x24, 0xef, 0x56,
	0xbf, 0xf3, 0x72, 0xbf, 0xda, 0xc8, 0x8f, 0xf9, 0x68, 0xf6, 0x1c, 0x34, 0xda, 0xfd, 0x4d, 0x98,
	0x0d, 0xd4, 0x84, 0x59, 0x56, 0x6c, 0x0e, 0x66, 0xaa, 0xdf, 0xdd, 0xde, 0x29, 0xd7, 0xe4, 0x4a,
	0x79, 0x77, 0xab, 0xda, 0xdc, 0x7b, 0x91, 0x97, 0xd0, 0x3c, 0xe4, 0x1d, 0x62, 0x7d, 0xbf, 0xba,
	0xbf, 0x5f, 0xdd, 0xad, 0x54, 0xf3, 0x99, 0xfb, 0xdf, 0xe4, 0x2b, 0x27, 0x8c, 0x1b, 0xb3, 0x30,
	0xf5, 0x6c, 0x8f, 0x3f, 0xb2, 0xe4, 0x93, 0xfc, 0x7d, 0x9f, 0xc2, 0x84, 0x93, 0x22, 0x63, 0x1f,
	0xcd, 0xc1, 0x4c, 0xa3, 0xd9, 0x94, 0xeb, 0xbb, 0x07, 0xe5, 0x9d, 0xfa, 0x53, 0xb9, 0x59, 0xae,
	0xe5, 0x2f, 0x21, 0x04, 0xd3, 0x94, 0xb8, 0xfd, 0x72, 0x77, 0xf7, 0x03, 0xb9, 0xb1, 0xb7, 0xf7,
	0x3c, 0x2f, 0xa1, 0x49, 0xc8, 0x51, 0x5a, 0xad, 0xfc, 0x9c, 0xee, 0xfe, 0x2d, 0xc8, 0x95, 0x9f,
	0x1e, 0xc8, 0xcd, 0x0f, 0x5e, 0x54, 0x29, 0xbe, 0x72, 0x53, 0xae, 0x3c, 0x2b, 0xef, 0xee, 0x56,
	0x77, 0xec, 0x8f, 0xf2, 0x97, 0xd0, 0x55, 0x98, 0xe3, 0xa8, 0xcf, 0xf6, 0x9e, 0x57, 0x5f, 0x94,
	0x6b, 0xd5, 0xbc, 0x74, 0xff, 0x0e, 0xcc, 0x87, 0x95, 0x80, 0x21, 0x99, 0x82, 0x71, 0xba, 0xb8,
	0xb3, 0xdf, 0xe3, 0x7f, 0x7b, 0x02, 0x79, 0x4f, 0x47, 0x9c, 0xe9, 0xe8, 0x9f, 0x25, 0xc8, 0x87,
	0xdb, 0xe7, 0xd0, 0x9d, 0xa8, 0x92, 0x08, 0x1a, 0x0f, 0x0b, 0x77, 0xd3, 0x4c, 0x23, 0x26, 0x3e,
	0xfd, 0xfe, 0xe7, 0x5f, 0x66, 0xa5, 0xdf, 0xfb, 0xfc, 0xcb, 0xec, 0x64, 0xbf, 0xa4, 0x97, 0xba,
	0x25, 0xa5, 0xa4, 0x95, 0x06, 0xa5, 0x9f, 0x7c, 0xfe, 0x65, 0xf6, 0xc5, 0x5a, 0x7f, 0xa3, 0xaf,
	0xab, 0x9b, 0xc5, 0x35, 0xbd, 0xb8, 0xe1, 0x0b, 0xff, 0x66, 0x71, 0xad, 0xbb, 0xe1, 0x0b, 0xf0,
	0x66, 0x71, 0x4d, 0xd9, 0x70, 0x3b, 0x13, 0x37, 0x8b, 0x6b, 0xda, 0x86, 0xdb, 0x85, 0xb8, 0x59,
	0x5c, 0x1b, 0x6c, 0x70, 0x1d, 0x87, 0x9b, 0xe8, 0xaf, 0xec, 0x54, 0x5c, 0x24, 0x34, 0x17, 0xc4,
	0x68, 0xe2, 0x46, 0xbd, 0xc2, 0xbd, 0x94, 0x33, 0xe9, 0xcb, 0x93, 0x9e, 0x2c, 0x43, 0x4f, 0x36,
	0xda, 0x2f, 0x91, 0x52, 0x97, 0x9d, 0xe9, 0xa6, 0x77, 0x26, 0xb2, 0xc1, 0xec, 0x41, 0xe4, 0x34,
	0xe8, 0x53, 0x09, 0xc0, 0xd7, 0x3c, 0x24, 0x30, 0x4a, 0x81, 0xbe, 0xab, 0x42, 0x31, 0x79, 0x02,
	0x31, 0xf1, 0x7b, 0x14, 0x49, 0x96, 0x21, 0x19, 0x94, 0x94, 0x92, 0xc5, 0x90, 0xdc, 0x5f, 0x1b,
	0x04, 0x99, 0xea, 0x70, 0x91, 0xfe, 0xb4, 0x36, 0xf8, 0x02, 0xd8, 0x26, 0xea, 0x01, 0xf8, 0x31,
	0x87, 0x08, 0x51, 0xa0, 0xf1, 0x4a, 0x84, 0x28, 0xd8, 0x5f, 0x85, 0x6f, 0x52, 0x44, 0x23, 0x14,
	0x51, 0xc6, 0x46, 0x93, 0x8f, 0xec, 0xf9, 0xe3, 0x80, 0x01, 0xaa, 0xe9, 0x47, 0x16, 0xba, 0x95,
	0x74, 0x52, 0xa7, 0x8b, 0xb3, 0x70, 0x7b, 0xf8, 0x24, 0x62, 0xe2, 0xb7, 0x29, 0x80, 0xcb, 0x14,
	0xc0, 0xc8, 0xa0, 0x74, 0xcc, 0x20, 0x2c, 0x87, 0x19, 0x72, 0xbc, 0xe1, 0x17, 0x6f, 0x37, 0xd1,
	0xff, 0x48, 0x90, 0x0f, 0xf7, 0xc5, 0x88, 0x94, 0x41, 0xd0, 0xc4, 0x23, 0x52, 0x06, 0x61, 0x8b,
	0xcd, 0xa7, 0x12, 0x85, 0x35, 0x4a, 0x61, 0x21, 0xab, 0xa4, 0x94, 0x48, 0x49, 0x2b, 0xa9, 0xa5,
	0x7e, 0xe9, 0xa4, 0xa4, 0x3b, 0x3a, 0x61, 0x84, 0xf9, 0x44, 0xef, 0x2d, 0x80, 0xda, 0x91, 0x2b,
	0xd9, 0x22, 0x4c, 0x19, 0xec, 0xce, 0xa9, 0xcd, 0xe2, 0x9a, 0xba, 0xa1, 0x6a, 0xa4, 0xb5, 0x59,
	0xa4, 0x02, 0xc8, 0x2b, 0xcf, 0xc9, 0x06, 0x75, 0xee, 0x54, 0xc9, 0x36, 0xdc, 0x5e, 0x0b, 0xa6,
	0x38, 0xac, 0xb1, 0x69, 0x13, 0xfd, 0xa5, 0xc4, 0x92, 0xf2, 0x43, 0x8f, 0x5d, 0x4b, 0x77, 0x6c,
	0x51, 0x67, 0x0f, 0xde, 0xa0, 0xa7, 0x1e, 0x63, 0x97, 0x61, 0x39, 0xe7, 0xbc, 0x17, 0x3a, 0xe7,
	0xaa, 0x61, 0x5a, 0xf7, 0x18, 0x2e, 0xff, 0xb0, 0x36, 0x0d, 0xfd, 0x3a, 0x4c, 0x05, 0x92, 0x3f,
	0x08, 0x0b, 0xca, 0x14, 0xa1, 0x7e, 0x9f, 0xc2, 0xad, 0xa1, 0x73, 0x88, 0x89, 0x8b, 0x14, 0x57,
	0x8e, 0x49, 0xa9, 0x8d, 0x6a, 0x26, 0x24, 0x22, 0xe8, 0xfb, 0x12, 0x0b, 0x9a, 0x7d, 0xd7, 0x20,
	0xda, 0x3c, 0xdc, 0xea, 0x21, 0xda, 0x3c, 0xd2, 0x02, 0x81, 0xef, 0xd2, 0xcd, 0xc7, 0x19, 0x53,
	0x5c, 0xe3, 0x31, 0x17, 0x30, 0x1a, 0x8e, 0x4d, 0xfb, 0x43, 0x89, 0x35, 0x1b, 0x87, 0xda, 0x76,
	0xd0, 0x5b, 0x42, 0x35, 0x88, 0x76, 0x07, 0x15, 0x56, 0xd3, 0x4d, 0x24, 0x26, 0xbe, 0x4f, 0x11,
	0x01, 0x43, 0xd4, 0x77, 0xae, 0xe9, 0xaa, 0x67, 0xce, 0x42, 0x8c, 0xf9, 0x81, 0xc4, 0x57, 0x1b,
	0xdc, 0xa6, 0x9f, 0xa1, 0x02, 0x61, 0xbf, 0x92, 0x0a, 0x6f, 0xa5, 0x9a, 0xe7, 0xde, 0xd0, 0x44,
	0xd2, 0x0d, 0xfd, 0x16, 0xd3, 0xda, 0x40, 0x83, 0x46, 0x8c, 0xd6, 0x86, 0x7b, 0x3b, 0x0a, 0xd7,
	0xd7, 0xbd, 0x9e, 0xfb, 0xf5, 0xfd, 0x6f, 0x6f, 0xd9, 0x3d, 0xf7, 0xd5, 0x8e, 0x69, 0x9d, 0xcb,
	0x2f, 0xb6, 0xec, 0xfb, 0x99, 0x64, 0x5b, 0xdb, 0xb7, 0x73, 0x65, 0xad, 0xbb, 0x11, 0xee, 0xf9,
	0xd8, 0x2c, 0xa2, 0x5e, 0xa8, 0xd7, 0x27, 0x06, 0x80, 0xa0, 0xb9, 0x24, 0x46, 0x7f, 0x22, 0xcd,
	0x26, 0x78, 0x86, 0x42, 0x99, 0xa2, 0x50, 0x2e, 0x51, 0x20, 0x97, 0xd0, 0x8f, 0x24, 0x98, 0x13,
	0x34, 0x7d, 0x8a, 0xfc, 0x9c, 0xb8, 0xbb, 0x54, 0xe4, 0xe7, 0x62, 0xba, 0x48, 0xf1, 0x0a, 0xdd,
	0x7d, 0x9a, 0x31, 0x42, 0x67, 0x8c, 0x98, 0x5e, 0xd3, 0xf9, 0x3b, 0x40, 0xbf, 0x2d, 0x41, 0xce,
	0xad, 0xe3, 0xa3, 0x65, 0xb1, 0x79, 0x76, 0x0a, 0xfa, 0x85, 0x1b, 0x49, 0xc3, 0xae, 0x53, 0x9d,
	0x61, 0xae, 0xcc, 0x28, 0x51, 0x13, 0x69, 0x3b, 0x55, 0x63, 0x83, 0xd5, 0x4a, 0x99, 0xeb, 0x72,
	0xcb, 0xbe, 0xbc, 0x4b, 0x43, 0x2d, 0x98, 0xe0, 0x1a, 0x05, 0x90, 0xd8, 0x43, 0x71, 0x9d, 0x05,
	0x85, 0x9b, 0x43, 0x66, 0xb8, 0x6c, 0xcf, 0x73, 0x6c, 0xff, 0xb1, 0x04, 0xb3, 0x91, 0x2e, 0x1e,
	0x74, 0x77, 0x88, 0xb4, 0xb9, 0x2c, 0x4f, 0x16, 0xb7, 0xc7, 0x74, 0xb3, 0x59, 0xc7, 0x61, 0xd9,
	0x3e, 0x73, 0x25, 0xec, 0xb0, 0xc2, 0x2e, 0xf4, 0x8f, 0x25, 0xbf, 0x7b, 0x38, 0x04, 0xea, 0x41,
	0xfc, 0xfd, 0x46, 0x91, 0x3d, 0x4c, 0x3f, 0xd9, 0xd5, 0x49, 0x94, 0xa4, 0x93, 0x7f, 0x6a, 0x37,
	0xf0, 0x8b, 0x1a, 0x67, 0xd0, 0xc3, 0x21, 0x32, 0x1f, 0x68, 0xd5, 0x29, 0xac, 0x5d, 0x60, 0x36,
	0x31, 0x31, 0xa6, 0xd0, 0xe6, 0xb8, 0xb0, 0x63, 0x36, 0xe2, 0x4e, 0x91, 0xce, 0x2c, 0xba, 0x5f,
	0x70, 0x8e, 0xb1, 0xe8, 0x81, 0x2a, 0x75, 0x8c, 0x45, 0x0f, 0x56, 0xad, 0x6d, 0x79, 0x99, 0xe7,
	0xe4, 0xa5, 0xcf, 0xf7, 0x9d, 0x57, 0x94, 0x9e, 0x8a, 0x6e, 0x25, 0x19, 0x3e, 0xa7, 0x66, 0x2b,
	0x8a, 0x70, 0xa2, 0x25, 0x5c, 0xbc, 0x48, 0x77, 0xbb, 0xc2, 0xce, 0xda, 0x67, 0x67, 0x1d, 0x73,
	0x6c, 0x35, 0xfa, 0x4c, 0x62, 0x5d, 0x33, 0x7c, 0x41, 0x1a, 0x25, 0xae, 0xe9, 0x66, 0xaf, 0x0a,
	0x77, 0x52, 0xcc, 0x22, 0x26, 0x7e, 0x97, 0x6e, 0xbd, 0x40, 0xb7, 0xce, 0xf5, 0x4b, 0x96, 0x17,
	0xfb, 0xde, 0xf5, 0x9c, 0x85, 0xad, 0xa4, 0x91, 0x30, 0xd8, 0xf1, 0x68, 0xaf, 0x24, 0x40, 0xd1,
	0x7a, 0xab, 0xc8, 0xa3, 0x09, 0x6b, 0xba, 0x22, 0x8f, 0x26, 0x2e, 0xdf, 0xda, 0xa6, 0xeb, 0x2a,
	0x27, 0xaa, 0xd3, 0x41, 0x51, 0x45, 0xbf, 0x2f, 0xc1, 0x4c, 0xa8, 0xa8, 0x29, 0x62, 0x55, 0xb4,
	0x9e, 0x5a, 0xb8, 0x93, 0x62, 0x16, 0x31, 0xf1, 0x43, 0x8a, 0x60, 0x31, 0xa0, 0xd6, 0x4b, 0x51,
	0xb5, 0x76, 0xb8, 0x45, 0xc3, 0x8d, 0x99, 0xda, 0x70, 0x38, 0xb5, 0x54, 0x70, 0x04, 0x65, 0x53,
	0x5b, 0x77, 0x97, 0x92, 0x74, 0xf7, 0x33, 0x09, 0xf2, 0xe1, 0xc2, 0x9f, 0xc8, 0x9f, 0x09, 0x4a,
	0x97, 0x22, 0x7f, 0x26, 0xac, 0x21, 0x96, 0x28, 0x8a, 0x02, 0x33, 0xf2, 0x26, 0x8b, 0x82, 0x29,
	0x92, 0x3b, 0x6b, 0xe6, 0x86, 0x53, 0x71, 0x65, 0x96, 0xdd, 0x29, 0xaf, 0xda, 0x22, 0xe4, 0xa6,
	0xb6, 0x36, 0xd1, 0x4f, 0x25, 0x98, 0x13, 0xd4, 0x02, 0xd1, 0xea, 0x90, 0x80, 0xcf, 0x47, 0x79,
	0x2f, 0xe5, 0x4c, 0x62, 0xe2, 0x07, 0x14, 0xe8, 0x35, 0x76, 0x7b, 0xa6, 0xe3, 0x8b, 0x16, 0xe3,
	0x60, 0x3a, 0x81, 0xc0, 0x50, 0xc6, 0x09, 0xaa, 0x95, 0xc9, 0x81, 0x34, 0x87, 0x87, 0x59, 0x98,
	0xeb, 0xa1, 0x40, 0x40, 0xd0, 0x34, 0x10, 0xf3, 0xe0, 0x15, 0xb4, 0x26, 0xc4, 0x3c, 0x78, 0x45,
	0x5d, 0x08, 0xb6, 0x36, 0x2d, 0x27, 0x68, 0xd3, 0x2b, 0x29, 0x54, 0xe7, 0x77, 0x7a, 0xb4, 0x87,
	0x9c, 0xce, 0xf3, 0xc9, 0xab, 0xe9, 0x26, 0xba, 0x50, 0x6e, 0x24, 0x40, 0xf9, 0x33, 0x5b, 0x8c,
	0x83, 0x75, 0x58, 0xb1, 0x18, 0x87, 0xab, 0x84, 0x31, 0x62, 0x1c, 0x2d, 0x05, 0x3e, 0xa1, 0x20,
	0x56, 0x9c, 0x67, 0x37, 0x71, 0xb4, 0xfb, 0x56, 0x58, 0xbb, 0x1d, 0xe9, 0x0d, 0xe8, 0xf9, 0x6f,
	0x32, 0x51, 0x19, 0x0a, 0x4e, 0x50, 0x5c, 0x8d, 0x11, 0x95, 0x28, 0xb8, 0x6b, 0x14, 0x5c, 0x91,
	0x73, 0x85, 0xc0, 0xed, 0xff, 0x23, 0x4f, 0x89, 0x82, 0x18, 0x62, 0x95, 0x28, 0x02, 0xe3, 0x5e,
	0xca, 0x99, 0xee, 0x5d, 0xdd, 0x4c, 0xb8, 0xab, 0x9f, 0xda, 0x2f, 0x9c, 0x50, 0xd5, 0x2c, 0xe6,
	0x85, 0x13, 0x2d, 0xee, 0xc5, 0xbc, 0x70, 0x44, 0xf5, 0xc1, 0x75, 0x0a, 0x05, 0x07, 0xac, 0xf1,
	0xb5, 0xa8, 0x35, 0x76, 0x2b, 0x85, 0x9b, 0xe8, 0x67, 0x12, 0x5c, 0x11, 0x56, 0x1b, 0xd1, 0xfd,
	0x58, 0x23, 0x12, 0xc5, 0xf7, 0x20, 0xf5, 0x5c, 0x17, 0xe2, 0xad, 0xf4, 0x10, 0x7f, 0xe0, 0xe8,
	0xdc, 0x70, 0xe6, 0x09, 0x2b, 0xa3, 0x71, 0x3a, 0x27, 0x40, 0xb6, 0x4c, 0x91, 0xdd, 0xe6, 0x24,
	0x6a, 0x32, 0x00, 0xe4, 0x33, 0x09, 0x96, 0x62, 0xcb, 0x84, 0x68, 0x5d, 0x78, 0x47, 0xb1, 0xa5,
	0xba, 0xc2, 0xa3, 0x0b, 0xcd, 0x77, 0xa5, 0xec, 0x4e, 0x82, 0x94, 0xfd, 0x89, 0x04, 0x4b, 0xb1,
	0x95, 0x46, 0x11, 0xbe, 0xa4, 0x92, 0xa6, 0x08, 0x5f, 0x62, 0x19, 0x13, 0x17, 0x28, 0xbe, 0xbb,
	0xdc, 0x73, 0x72, 0xdc, 0x0f, 0x88, 0xfe, 0x46, 0x82, 0xeb, 0x49, 0x35, 0x47, 0xf4, 0x4e, 0x9c,
	0x04, 0xc5, 0x03, 0x7c, 0x7c, 0xd1, 0x4f, 0x5c, 0x1e, 0xbe, 0x95, 0xc0, 0xc3, 0x8f, 0x01, 0x45,
	0x9b, 0xd5, 0x45, 0xb2, 0x26, 0x6c, 0x69, 0x1f, 0xf2, 0x02, 0x62, 0xb1, 0xc9, 0x6a, 0x52, 0x6c,
	0xf2, 0x2a, 0xe0, 0xed, 0xbc, 0xda, 0x68, 0xb2, 0xb7, 0xe3, 0xcb, 0xb3, 0xc9, 0xde, 0x2e, 0x50,
	0x6c, 0xc5, 0xd7, 0x29, 0x9c, 0x7b, 0x9c, 0xb8, 0x4f, 0x70, 0x61, 0x2d, 0xea, 0xb0, 0x8c, 0xa9,
	0x53, 0xde, 0x8c, 0xc9, 0x98, 0xfa, 0xa5, 0xd6, 0x98, 0x8c, 0x29, 0x57, 0x5d, 0xb5, 0xb7, 0xbb,
	0x1f, 0xb7, 0x9d, 0xd3, 0x63, 0x1a, 0x2e, 0x87, 0xc6, 0x9c, 0x5c, 0x50, 0x67, 0x8d, 0x39, 0xb9,
	0xa8, 0xbe, 0x8a, 0x97, 0x28, 0x94, 0x07, 0xdc, 0xcb, 0x22, 0xb7, 0xd6, 0x2f, 0xda, 0x4f, 0x8b,
	0xbf, 0x93, 0xe0, 0x6a, 0x4c, 0x65, 0x50, 0xf4, 0xb2, 0x8b, 0xaf, 0x6a, 0x8a, 0x5e, 0x76, 0x49,
	0x25, 0xc7, 0x5f, 0xa2, 0x98, 0x1e, 0x86, 0x7c, 0xed, 0xdd, 0x18, 0x5f, 0xcb, 0x22, 0x45, 0x6a,
	0x26, 0x59, 0x59, 0x93, 0xa9, 0xfe, 0xd5, 0x98, 0x6a, 0xa3, 0x08, 0x75, 0x7c, 0x6d, 0x53, 0x84,
	0x3a, 0xa9, 0x8c, 0xc9, 0x44, 0x7a, 0x2d, 0x49, 0xa4, 0x7f, 0x68, 0x3f, 0x95, 0xd3, 0x42, 0x8b,
	0x2f, 0x8a, 0xc6, 0x3c, 0x95, 0x63, 0xa1, 0xb1, 0x50, 0x72, 0x9d, 0x0b, 0x25, 0x3f, 0xb1, 0x1d,
	0x49, 0xa8, 0x36, 0x19, 0xe3, 0x48, 0xa2, 0x35, 0xd1, 0x18, 0x47, 0x22, 0x28, 0x75, 0xda, 0x99,
	0xb5, 0x47, 0x4e, 0xe6, 0xb3, 0x1d, 0xc9, 0x7c, 0xb6, 0x5d, 0xb3, 0xf8, 0x17, 0xe1, 0x50, 0xdf,
	0x49, 0x21, 0x0c, 0x0b, 0xf5, 0xfd, 0xf4, 0xc1, 0xbd, 0x94, 0x33, 0x89, 0x69, 0xe7, 0x5f, 0xde,
	0x76, 0x72, 0xd4, 0xba, 0x93, 0x7f, 0x89, 0xe4, 0xe2, 0x83, 0x89, 0xaf, 0xff, 0x95, 0x60, 0x61,
	0x3f, 0x5a, 0x1d, 0xa2, 0x26, 0xe9, 0x2b, 0x29, 0x1c, 0xbc, 0xf3, 0x35, 0x2a, 0x1c, 0x14, 0x46,
	0x5f, 0x7d, 0xfe, 0x65, 0xf6, 0x5f, 0xce, 0xb7, 0xf2, 0x3f, 0xff, 0xe2, 0x86, 0xf4, 0xaf, 0x5f,
	0xdc, 0x90, 0xfe, 0xf3, 0x8b, 0x1b, 0xd2, 0xa7, 0xff, 0x75, 0xe3, 0xd2, 0xff, 0x05, 0x00, 0x00,
	0xff, 0xff, 0x9f, 0x65, 0x95, 0xf3, 0x14, 0x45, 0x00, 0x00,
}
