// Code generated by protoc-gen-gogo.
// source: src/guildMemberLvSvr/guildmemberlv.proto
// DO NOT EDIT!

/*
	Package guildmemberlv is a generated protocol buffer package.

	It is generated from these files:
		src/guildMemberLvSvr/guildmemberlv.proto

	It has these top-level messages:
		StMemberContributionOper
		StMemberContributionOrder
		StMemberContributionDetail
		StMemberContributionInfo
		AddMemberContributionReq
		AddMemberContributionResp
		GetMemberContributionReq
		GetMemberContributionResp
		BatchGetMemberContributionReq
		BatchGetMemberContributionResp
		GetMemberContributionListInOrderReq
		GetMemberContributionDetailListReq
		GetMemberContributionDetailListResp
		ClearMemberContributionReq
		StGuildDonateOption
		GetGuildDonateOptionReq
		GetGuildDonateOptionResp
		GetMemberSpecContTypeByDateReq
*/
package guildmemberlv

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 与下面的CONTRIBUTION_TYPE保持一致
type OPER_TYPE int32

const (
	OPER_TYPE_OPER_CHECK_IN             OPER_TYPE = 1
	OPER_TYPE_OPER_CHECK_IN_SUPPLEMENT  OPER_TYPE = 2
	OPER_TYPE_OPER_DONATE               OPER_TYPE = 3
	OPER_TYPE_OPER_CAPITAL_INJECTION    OPER_TYPE = 4
	OPER_TYPE_OPER_CONSUME_GAME         OPER_TYPE = 5
	OPER_TYPE_OPER_CONSUME_HAPPY_CENTER OPER_TYPE = 6
	OPER_TYPE_OPER_BUY_GIFT             OPER_TYPE = 7
	OPER_TYPE_OPER_SEND_PRESENT         OPER_TYPE = 8
	OPER_TYPE_OPER_LOTTO                OPER_TYPE = 9
	OPER_TYPE_OPER_SEND_BACK            OPER_TYPE = 101
	OPER_TYPE_OPER_OFFICIAL_AWARD       OPER_TYPE = 102
)

var OPER_TYPE_name = map[int32]string{
	1:   "OPER_CHECK_IN",
	2:   "OPER_CHECK_IN_SUPPLEMENT",
	3:   "OPER_DONATE",
	4:   "OPER_CAPITAL_INJECTION",
	5:   "OPER_CONSUME_GAME",
	6:   "OPER_CONSUME_HAPPY_CENTER",
	7:   "OPER_BUY_GIFT",
	8:   "OPER_SEND_PRESENT",
	9:   "OPER_LOTTO",
	101: "OPER_SEND_BACK",
	102: "OPER_OFFICIAL_AWARD",
}
var OPER_TYPE_value = map[string]int32{
	"OPER_CHECK_IN":             1,
	"OPER_CHECK_IN_SUPPLEMENT":  2,
	"OPER_DONATE":               3,
	"OPER_CAPITAL_INJECTION":    4,
	"OPER_CONSUME_GAME":         5,
	"OPER_CONSUME_HAPPY_CENTER": 6,
	"OPER_BUY_GIFT":             7,
	"OPER_SEND_PRESENT":         8,
	"OPER_LOTTO":                9,
	"OPER_SEND_BACK":            101,
	"OPER_OFFICIAL_AWARD":       102,
}

func (x OPER_TYPE) Enum() *OPER_TYPE {
	p := new(OPER_TYPE)
	*p = x
	return p
}
func (x OPER_TYPE) String() string {
	return proto.EnumName(OPER_TYPE_name, int32(x))
}
func (x *OPER_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(OPER_TYPE_value, data, "OPER_TYPE")
	if err != nil {
		return err
	}
	*x = OPER_TYPE(value)
	return nil
}
func (OPER_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorGuildmemberlv, []int{0} }

// 与上面的OPER_TYPE保持一致
type CONTRIBUTION_TYPE int32

const (
	CONTRIBUTION_TYPE_CONT_CHECK_IN_DAILY       CONTRIBUTION_TYPE = 1
	CONTRIBUTION_TYPE_CONT_CHECK_IN_SUPPLEMENT  CONTRIBUTION_TYPE = 2
	CONTRIBUTION_TYPE_CONT_DONATE_DAILY         CONTRIBUTION_TYPE = 3
	CONTRIBUTION_TYPE_CONT_CAPITAL_INJECTION    CONTRIBUTION_TYPE = 4
	CONTRIBUTION_TYPE_CONT_CONSUME_GAME         CONTRIBUTION_TYPE = 5
	CONTRIBUTION_TYPE_CONT_CONSUME_HAPPY_CENTER CONTRIBUTION_TYPE = 6
	CONTRIBUTION_TYPE_CONT_BUY_GIFT             CONTRIBUTION_TYPE = 7
	CONTRIBUTION_TYPE_CONT_SEND_PRESENT         CONTRIBUTION_TYPE = 8
	CONTRIBUTION_TYPE_CONT_LOTTO                CONTRIBUTION_TYPE = 9
	CONTRIBUTION_TYPE_CONT_SEND_BACK            CONTRIBUTION_TYPE = 101
	CONTRIBUTION_TYPE_CONT_OFFICIAL_AWARD       CONTRIBUTION_TYPE = 102
	CONTRIBUTION_TYPE_CONT_CHECK_IN_CONTINUOUS  CONTRIBUTION_TYPE = 201
	CONTRIBUTION_TYPE_CONT_DONATE_CONTINUOUS    CONTRIBUTION_TYPE = 202
)

var CONTRIBUTION_TYPE_name = map[int32]string{
	1:   "CONT_CHECK_IN_DAILY",
	2:   "CONT_CHECK_IN_SUPPLEMENT",
	3:   "CONT_DONATE_DAILY",
	4:   "CONT_CAPITAL_INJECTION",
	5:   "CONT_CONSUME_GAME",
	6:   "CONT_CONSUME_HAPPY_CENTER",
	7:   "CONT_BUY_GIFT",
	8:   "CONT_SEND_PRESENT",
	9:   "CONT_LOTTO",
	101: "CONT_SEND_BACK",
	102: "CONT_OFFICIAL_AWARD",
	201: "CONT_CHECK_IN_CONTINUOUS",
	202: "CONT_DONATE_CONTINUOUS",
}
var CONTRIBUTION_TYPE_value = map[string]int32{
	"CONT_CHECK_IN_DAILY":       1,
	"CONT_CHECK_IN_SUPPLEMENT":  2,
	"CONT_DONATE_DAILY":         3,
	"CONT_CAPITAL_INJECTION":    4,
	"CONT_CONSUME_GAME":         5,
	"CONT_CONSUME_HAPPY_CENTER": 6,
	"CONT_BUY_GIFT":             7,
	"CONT_SEND_PRESENT":         8,
	"CONT_LOTTO":                9,
	"CONT_SEND_BACK":            101,
	"CONT_OFFICIAL_AWARD":       102,
	"CONT_CHECK_IN_CONTINUOUS":  201,
	"CONT_DONATE_CONTINUOUS":    202,
}

func (x CONTRIBUTION_TYPE) Enum() *CONTRIBUTION_TYPE {
	p := new(CONTRIBUTION_TYPE)
	*p = x
	return p
}
func (x CONTRIBUTION_TYPE) String() string {
	return proto.EnumName(CONTRIBUTION_TYPE_name, int32(x))
}
func (x *CONTRIBUTION_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CONTRIBUTION_TYPE_value, data, "CONTRIBUTION_TYPE")
	if err != nil {
		return err
	}
	*x = CONTRIBUTION_TYPE(value)
	return nil
}
func (CONTRIBUTION_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{1}
}

type CONTRIBUTION_RANK_TYPE int32

const (
	CONTRIBUTION_RANK_TYPE_TOT_DESC   CONTRIBUTION_RANK_TYPE = 0
	CONTRIBUTION_RANK_TYPE_TOT_ASC    CONTRIBUTION_RANK_TYPE = 1
	CONTRIBUTION_RANK_TYPE_VALID_DESC CONTRIBUTION_RANK_TYPE = 2
	CONTRIBUTION_RANK_TYPE_VALID_ASC  CONTRIBUTION_RANK_TYPE = 3
)

var CONTRIBUTION_RANK_TYPE_name = map[int32]string{
	0: "TOT_DESC",
	1: "TOT_ASC",
	2: "VALID_DESC",
	3: "VALID_ASC",
}
var CONTRIBUTION_RANK_TYPE_value = map[string]int32{
	"TOT_DESC":   0,
	"TOT_ASC":    1,
	"VALID_DESC": 2,
	"VALID_ASC":  3,
}

func (x CONTRIBUTION_RANK_TYPE) Enum() *CONTRIBUTION_RANK_TYPE {
	p := new(CONTRIBUTION_RANK_TYPE)
	*p = x
	return p
}
func (x CONTRIBUTION_RANK_TYPE) String() string {
	return proto.EnumName(CONTRIBUTION_RANK_TYPE_name, int32(x))
}
func (x *CONTRIBUTION_RANK_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CONTRIBUTION_RANK_TYPE_value, data, "CONTRIBUTION_RANK_TYPE")
	if err != nil {
		return err
	}
	*x = CONTRIBUTION_RANK_TYPE(value)
	return nil
}
func (CONTRIBUTION_RANK_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{2}
}

// 个人贡献的相关操作
type StMemberContributionOper struct {
	OperType          uint32   `protobuf:"varint,1,req,name=oper_type,json=operType" json:"oper_type"`
	OperValue         int32    `protobuf:"varint,2,req,name=oper_value,json=operValue" json:"oper_value"`
	OrderId           string   `protobuf:"bytes,3,opt,name=order_id,json=orderId" json:"order_id"`
	OrderDesc         string   `protobuf:"bytes,4,opt,name=order_desc,json=orderDesc" json:"order_desc"`
	Extend            string   `protobuf:"bytes,5,opt,name=extend" json:"extend"`
	ContinuousDays    uint32   `protobuf:"varint,6,opt,name=continuous_days,json=continuousDays" json:"continuous_days"`
	SupplementDayList []uint32 `protobuf:"varint,7,rep,name=supplement_day_list,json=supplementDayList" json:"supplement_day_list,omitempty"`
	OptInvalid        bool     `protobuf:"varint,8,opt,name=opt_invalid,json=optInvalid" json:"opt_invalid"`
}

func (m *StMemberContributionOper) Reset()         { *m = StMemberContributionOper{} }
func (m *StMemberContributionOper) String() string { return proto.CompactTextString(m) }
func (*StMemberContributionOper) ProtoMessage()    {}
func (*StMemberContributionOper) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{0}
}

func (m *StMemberContributionOper) GetOperType() uint32 {
	if m != nil {
		return m.OperType
	}
	return 0
}

func (m *StMemberContributionOper) GetOperValue() int32 {
	if m != nil {
		return m.OperValue
	}
	return 0
}

func (m *StMemberContributionOper) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StMemberContributionOper) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *StMemberContributionOper) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

func (m *StMemberContributionOper) GetContinuousDays() uint32 {
	if m != nil {
		return m.ContinuousDays
	}
	return 0
}

func (m *StMemberContributionOper) GetSupplementDayList() []uint32 {
	if m != nil {
		return m.SupplementDayList
	}
	return nil
}

func (m *StMemberContributionOper) GetOptInvalid() bool {
	if m != nil {
		return m.OptInvalid
	}
	return false
}

// 个人贡献订单
type StMemberContributionOrder struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId           uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ContributionType  uint32 `protobuf:"varint,3,req,name=contribution_type,json=contributionType" json:"contribution_type"`
	ContributionValue int32  `protobuf:"varint,4,req,name=contribution_value,json=contributionValue" json:"contribution_value"`
	OrderId           string `protobuf:"bytes,5,req,name=order_id,json=orderId" json:"order_id"`
	OrderDesc         string `protobuf:"bytes,6,opt,name=order_desc,json=orderDesc" json:"order_desc"`
	Extend            string `protobuf:"bytes,7,opt,name=extend" json:"extend"`
	OptInvalid        bool   `protobuf:"varint,8,opt,name=opt_invalid,json=optInvalid" json:"opt_invalid"`
}

func (m *StMemberContributionOrder) Reset()         { *m = StMemberContributionOrder{} }
func (m *StMemberContributionOrder) String() string { return proto.CompactTextString(m) }
func (*StMemberContributionOrder) ProtoMessage()    {}
func (*StMemberContributionOrder) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{1}
}

func (m *StMemberContributionOrder) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StMemberContributionOrder) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StMemberContributionOrder) GetContributionType() uint32 {
	if m != nil {
		return m.ContributionType
	}
	return 0
}

func (m *StMemberContributionOrder) GetContributionValue() int32 {
	if m != nil {
		return m.ContributionValue
	}
	return 0
}

func (m *StMemberContributionOrder) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *StMemberContributionOrder) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *StMemberContributionOrder) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

func (m *StMemberContributionOrder) GetOptInvalid() bool {
	if m != nil {
		return m.OptInvalid
	}
	return false
}

// 个人贡献明细
type StMemberContributionDetail struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId           uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	ContributionType  uint32 `protobuf:"varint,3,req,name=contribution_type,json=contributionType" json:"contribution_type"`
	ContributionValue int32  `protobuf:"varint,4,req,name=contribution_value,json=contributionValue" json:"contribution_value"`
	CreateTs          uint32 `protobuf:"varint,5,opt,name=create_ts,json=createTs" json:"create_ts"`
	OrderDesc         string `protobuf:"bytes,6,opt,name=order_desc,json=orderDesc" json:"order_desc"`
	Extend            string `protobuf:"bytes,7,opt,name=extend" json:"extend"`
}

func (m *StMemberContributionDetail) Reset()         { *m = StMemberContributionDetail{} }
func (m *StMemberContributionDetail) String() string { return proto.CompactTextString(m) }
func (*StMemberContributionDetail) ProtoMessage()    {}
func (*StMemberContributionDetail) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{2}
}

func (m *StMemberContributionDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StMemberContributionDetail) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StMemberContributionDetail) GetContributionType() uint32 {
	if m != nil {
		return m.ContributionType
	}
	return 0
}

func (m *StMemberContributionDetail) GetContributionValue() int32 {
	if m != nil {
		return m.ContributionValue
	}
	return 0
}

func (m *StMemberContributionDetail) GetCreateTs() uint32 {
	if m != nil {
		return m.CreateTs
	}
	return 0
}

func (m *StMemberContributionDetail) GetOrderDesc() string {
	if m != nil {
		return m.OrderDesc
	}
	return ""
}

func (m *StMemberContributionDetail) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

// 个人贡献信息
type StMemberContributionInfo struct {
	Uid               uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	GuildId           uint32 `protobuf:"varint,2,req,name=guild_id,json=guildId" json:"guild_id"`
	TotalContribution uint32 `protobuf:"varint,3,req,name=total_contribution,json=totalContribution" json:"total_contribution"`
	ValidContribution uint32 `protobuf:"varint,4,req,name=valid_contribution,json=validContribution" json:"valid_contribution"`
	MemberLv          uint32 `protobuf:"varint,5,req,name=member_lv,json=memberLv" json:"member_lv"`
	MaxMemberLv       uint32 `protobuf:"varint,6,opt,name=max_member_lv,json=maxMemberLv" json:"max_member_lv"`
	CurLvContribution uint32 `protobuf:"varint,7,opt,name=cur_lv_contribution,json=curLvContribution" json:"cur_lv_contribution"`
	MaxLvContribution uint32 `protobuf:"varint,8,opt,name=max_lv_contribution,json=maxLvContribution" json:"max_lv_contribution"`
}

func (m *StMemberContributionInfo) Reset()         { *m = StMemberContributionInfo{} }
func (m *StMemberContributionInfo) String() string { return proto.CompactTextString(m) }
func (*StMemberContributionInfo) ProtoMessage()    {}
func (*StMemberContributionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{3}
}

func (m *StMemberContributionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StMemberContributionInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StMemberContributionInfo) GetTotalContribution() uint32 {
	if m != nil {
		return m.TotalContribution
	}
	return 0
}

func (m *StMemberContributionInfo) GetValidContribution() uint32 {
	if m != nil {
		return m.ValidContribution
	}
	return 0
}

func (m *StMemberContributionInfo) GetMemberLv() uint32 {
	if m != nil {
		return m.MemberLv
	}
	return 0
}

func (m *StMemberContributionInfo) GetMaxMemberLv() uint32 {
	if m != nil {
		return m.MaxMemberLv
	}
	return 0
}

func (m *StMemberContributionInfo) GetCurLvContribution() uint32 {
	if m != nil {
		return m.CurLvContribution
	}
	return 0
}

func (m *StMemberContributionInfo) GetMaxLvContribution() uint32 {
	if m != nil {
		return m.MaxLvContribution
	}
	return 0
}

// 增加个人贡献
type AddMemberContributionReq struct {
	GuildId  uint32                    `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	OperInfo *StMemberContributionOper `protobuf:"bytes,2,req,name=oper_info,json=operInfo" json:"oper_info,omitempty"`
}

func (m *AddMemberContributionReq) Reset()         { *m = AddMemberContributionReq{} }
func (m *AddMemberContributionReq) String() string { return proto.CompactTextString(m) }
func (*AddMemberContributionReq) ProtoMessage()    {}
func (*AddMemberContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{4}
}

func (m *AddMemberContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddMemberContributionReq) GetOperInfo() *StMemberContributionOper {
	if m != nil {
		return m.OperInfo
	}
	return nil
}

type AddMemberContributionResp struct {
	Contribution        *StMemberContributionInfo `protobuf:"bytes,1,req,name=contribution" json:"contribution,omitempty"`
	ContributionAdded   int32                     `protobuf:"varint,2,req,name=contribution_added,json=contributionAdded" json:"contribution_added"`
	ToBonusPeriod       uint32                    `protobuf:"varint,3,opt,name=to_bonus_period,json=toBonusPeriod" json:"to_bonus_period"`
	ToBonusContribution int32                     `protobuf:"varint,4,opt,name=to_bonus_contribution,json=toBonusContribution" json:"to_bonus_contribution"`
}

func (m *AddMemberContributionResp) Reset()         { *m = AddMemberContributionResp{} }
func (m *AddMemberContributionResp) String() string { return proto.CompactTextString(m) }
func (*AddMemberContributionResp) ProtoMessage()    {}
func (*AddMemberContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{5}
}

func (m *AddMemberContributionResp) GetContribution() *StMemberContributionInfo {
	if m != nil {
		return m.Contribution
	}
	return nil
}

func (m *AddMemberContributionResp) GetContributionAdded() int32 {
	if m != nil {
		return m.ContributionAdded
	}
	return 0
}

func (m *AddMemberContributionResp) GetToBonusPeriod() uint32 {
	if m != nil {
		return m.ToBonusPeriod
	}
	return 0
}

func (m *AddMemberContributionResp) GetToBonusContribution() int32 {
	if m != nil {
		return m.ToBonusContribution
	}
	return 0
}

// 获取个人贡献
type GetMemberContributionReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetMemberContributionReq) Reset()         { *m = GetMemberContributionReq{} }
func (m *GetMemberContributionReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionReq) ProtoMessage()    {}
func (*GetMemberContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{6}
}

func (m *GetMemberContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetMemberContributionResp struct {
	Contribution *StMemberContributionInfo `protobuf:"bytes,1,req,name=contribution" json:"contribution,omitempty"`
}

func (m *GetMemberContributionResp) Reset()         { *m = GetMemberContributionResp{} }
func (m *GetMemberContributionResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionResp) ProtoMessage()    {}
func (*GetMemberContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{7}
}

func (m *GetMemberContributionResp) GetContribution() *StMemberContributionInfo {
	if m != nil {
		return m.Contribution
	}
	return nil
}

// 批量查询个人贡献
type BatchGetMemberContributionReq struct {
	GuildId uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	UidList []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *BatchGetMemberContributionReq) Reset()         { *m = BatchGetMemberContributionReq{} }
func (m *BatchGetMemberContributionReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetMemberContributionReq) ProtoMessage()    {}
func (*BatchGetMemberContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{8}
}

func (m *BatchGetMemberContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetMemberContributionReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetMemberContributionResp struct {
	InfoList []*StMemberContributionInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *BatchGetMemberContributionResp) Reset()         { *m = BatchGetMemberContributionResp{} }
func (m *BatchGetMemberContributionResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetMemberContributionResp) ProtoMessage()    {}
func (*BatchGetMemberContributionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{9}
}

func (m *BatchGetMemberContributionResp) GetInfoList() []*StMemberContributionInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 根据排序获取成员列表
type GetMemberContributionListInOrderReq struct {
	GuildId  uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Offset   uint32 `protobuf:"varint,2,opt,name=offset" json:"offset"`
	Limit    uint32 `protobuf:"varint,3,opt,name=limit" json:"limit"`
	RankType uint32 `protobuf:"varint,4,opt,name=rank_type,json=rankType" json:"rank_type"`
}

func (m *GetMemberContributionListInOrderReq) Reset()         { *m = GetMemberContributionListInOrderReq{} }
func (m *GetMemberContributionListInOrderReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionListInOrderReq) ProtoMessage()    {}
func (*GetMemberContributionListInOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{10}
}

func (m *GetMemberContributionListInOrderReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberContributionListInOrderReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMemberContributionListInOrderReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetMemberContributionListInOrderReq) GetRankType() uint32 {
	if m != nil {
		return m.RankType
	}
	return 0
}

// 获取个人贡献明细列表
type GetMemberContributionDetailListReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Offset  uint32 `protobuf:"varint,2,opt,name=offset" json:"offset"`
	Limit   uint32 `protobuf:"varint,3,opt,name=limit" json:"limit"`
}

func (m *GetMemberContributionDetailListReq) Reset()         { *m = GetMemberContributionDetailListReq{} }
func (m *GetMemberContributionDetailListReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionDetailListReq) ProtoMessage()    {}
func (*GetMemberContributionDetailListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{11}
}

func (m *GetMemberContributionDetailListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberContributionDetailListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMemberContributionDetailListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetMemberContributionDetailListResp struct {
	DetailList []*StMemberContributionDetail `protobuf:"bytes,1,rep,name=detail_list,json=detailList" json:"detail_list,omitempty"`
}

func (m *GetMemberContributionDetailListResp) Reset()         { *m = GetMemberContributionDetailListResp{} }
func (m *GetMemberContributionDetailListResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberContributionDetailListResp) ProtoMessage()    {}
func (*GetMemberContributionDetailListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{12}
}

func (m *GetMemberContributionDetailListResp) GetDetailList() []*StMemberContributionDetail {
	if m != nil {
		return m.DetailList
	}
	return nil
}

// 清除个人贡献(离开公会)
type ClearMemberContributionReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *ClearMemberContributionReq) Reset()         { *m = ClearMemberContributionReq{} }
func (m *ClearMemberContributionReq) String() string { return proto.CompactTextString(m) }
func (*ClearMemberContributionReq) ProtoMessage()    {}
func (*ClearMemberContributionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{13}
}

func (m *ClearMemberContributionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 捐献选项
type StGuildDonateOption struct {
	DonateValue             uint32 `protobuf:"varint,1,req,name=donate_value,json=donateValue" json:"donate_value"`
	MemberContributionAdded uint32 `protobuf:"varint,2,req,name=member_contribution_added,json=memberContributionAdded" json:"member_contribution_added"`
	IsValid                 bool   `protobuf:"varint,3,req,name=is_valid,json=isValid" json:"is_valid"`
}

func (m *StGuildDonateOption) Reset()         { *m = StGuildDonateOption{} }
func (m *StGuildDonateOption) String() string { return proto.CompactTextString(m) }
func (*StGuildDonateOption) ProtoMessage()    {}
func (*StGuildDonateOption) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{14}
}

func (m *StGuildDonateOption) GetDonateValue() uint32 {
	if m != nil {
		return m.DonateValue
	}
	return 0
}

func (m *StGuildDonateOption) GetMemberContributionAdded() uint32 {
	if m != nil {
		return m.MemberContributionAdded
	}
	return 0
}

func (m *StGuildDonateOption) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

// 查询捐献选项
type GetGuildDonateOptionReq struct {
	MemberLv uint32 `protobuf:"varint,1,req,name=member_lv,json=memberLv" json:"member_lv"`
}

func (m *GetGuildDonateOptionReq) Reset()         { *m = GetGuildDonateOptionReq{} }
func (m *GetGuildDonateOptionReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateOptionReq) ProtoMessage()    {}
func (*GetGuildDonateOptionReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{15}
}

func (m *GetGuildDonateOptionReq) GetMemberLv() uint32 {
	if m != nil {
		return m.MemberLv
	}
	return 0
}

type GetGuildDonateOptionResp struct {
	OptionList []*StGuildDonateOption `protobuf:"bytes,1,rep,name=option_list,json=optionList" json:"option_list,omitempty"`
}

func (m *GetGuildDonateOptionResp) Reset()         { *m = GetGuildDonateOptionResp{} }
func (m *GetGuildDonateOptionResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateOptionResp) ProtoMessage()    {}
func (*GetGuildDonateOptionResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{16}
}

func (m *GetGuildDonateOptionResp) GetOptionList() []*StGuildDonateOption {
	if m != nil {
		return m.OptionList
	}
	return nil
}

// 获取用户在指定日期的特定的贡献记录
type GetMemberSpecContTypeByDateReq struct {
	GuildId          uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	DateTs           uint32 `protobuf:"varint,2,req,name=date_ts,json=dateTs" json:"date_ts"`
	ContributionType uint32 `protobuf:"varint,3,req,name=contribution_type,json=contributionType" json:"contribution_type"`
}

func (m *GetMemberSpecContTypeByDateReq) Reset()         { *m = GetMemberSpecContTypeByDateReq{} }
func (m *GetMemberSpecContTypeByDateReq) String() string { return proto.CompactTextString(m) }
func (*GetMemberSpecContTypeByDateReq) ProtoMessage()    {}
func (*GetMemberSpecContTypeByDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildmemberlv, []int{17}
}

func (m *GetMemberSpecContTypeByDateReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMemberSpecContTypeByDateReq) GetDateTs() uint32 {
	if m != nil {
		return m.DateTs
	}
	return 0
}

func (m *GetMemberSpecContTypeByDateReq) GetContributionType() uint32 {
	if m != nil {
		return m.ContributionType
	}
	return 0
}

func init() {
	proto.RegisterType((*StMemberContributionOper)(nil), "guildmemberlv.StMemberContributionOper")
	proto.RegisterType((*StMemberContributionOrder)(nil), "guildmemberlv.StMemberContributionOrder")
	proto.RegisterType((*StMemberContributionDetail)(nil), "guildmemberlv.StMemberContributionDetail")
	proto.RegisterType((*StMemberContributionInfo)(nil), "guildmemberlv.StMemberContributionInfo")
	proto.RegisterType((*AddMemberContributionReq)(nil), "guildmemberlv.AddMemberContributionReq")
	proto.RegisterType((*AddMemberContributionResp)(nil), "guildmemberlv.AddMemberContributionResp")
	proto.RegisterType((*GetMemberContributionReq)(nil), "guildmemberlv.GetMemberContributionReq")
	proto.RegisterType((*GetMemberContributionResp)(nil), "guildmemberlv.GetMemberContributionResp")
	proto.RegisterType((*BatchGetMemberContributionReq)(nil), "guildmemberlv.BatchGetMemberContributionReq")
	proto.RegisterType((*BatchGetMemberContributionResp)(nil), "guildmemberlv.BatchGetMemberContributionResp")
	proto.RegisterType((*GetMemberContributionListInOrderReq)(nil), "guildmemberlv.GetMemberContributionListInOrderReq")
	proto.RegisterType((*GetMemberContributionDetailListReq)(nil), "guildmemberlv.GetMemberContributionDetailListReq")
	proto.RegisterType((*GetMemberContributionDetailListResp)(nil), "guildmemberlv.GetMemberContributionDetailListResp")
	proto.RegisterType((*ClearMemberContributionReq)(nil), "guildmemberlv.ClearMemberContributionReq")
	proto.RegisterType((*StGuildDonateOption)(nil), "guildmemberlv.StGuildDonateOption")
	proto.RegisterType((*GetGuildDonateOptionReq)(nil), "guildmemberlv.GetGuildDonateOptionReq")
	proto.RegisterType((*GetGuildDonateOptionResp)(nil), "guildmemberlv.GetGuildDonateOptionResp")
	proto.RegisterType((*GetMemberSpecContTypeByDateReq)(nil), "guildmemberlv.GetMemberSpecContTypeByDateReq")
	proto.RegisterEnum("guildmemberlv.OPER_TYPE", OPER_TYPE_name, OPER_TYPE_value)
	proto.RegisterEnum("guildmemberlv.CONTRIBUTION_TYPE", CONTRIBUTION_TYPE_name, CONTRIBUTION_TYPE_value)
	proto.RegisterEnum("guildmemberlv.CONTRIBUTION_RANK_TYPE", CONTRIBUTION_RANK_TYPE_name, CONTRIBUTION_RANK_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Guildmemberlv service

type GuildmemberlvClient interface {
	AddMemberContribution(ctx context.Context, in *AddMemberContributionReq, opts ...grpc.CallOption) (*AddMemberContributionResp, error)
	GetMemberContribution(ctx context.Context, in *GetMemberContributionReq, opts ...grpc.CallOption) (*GetMemberContributionResp, error)
	BatchGetMemberContribution(ctx context.Context, in *BatchGetMemberContributionReq, opts ...grpc.CallOption) (*BatchGetMemberContributionResp, error)
	GetMemberContributionListInOrder(ctx context.Context, in *GetMemberContributionListInOrderReq, opts ...grpc.CallOption) (*BatchGetMemberContributionResp, error)
	GetMemberContributionDetailList(ctx context.Context, in *GetMemberContributionDetailListReq, opts ...grpc.CallOption) (*GetMemberContributionDetailListResp, error)
	ClearMemberContribution(ctx context.Context, in *ClearMemberContributionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildDonateOption(ctx context.Context, in *GetGuildDonateOptionReq, opts ...grpc.CallOption) (*GetGuildDonateOptionResp, error)
	GetMemberSpecContTypeByDate(ctx context.Context, in *GetMemberSpecContTypeByDateReq, opts ...grpc.CallOption) (*GetMemberContributionDetailListResp, error)
}

type guildmemberlvClient struct {
	cc *grpc.ClientConn
}

func NewGuildmemberlvClient(cc *grpc.ClientConn) GuildmemberlvClient {
	return &guildmemberlvClient{cc}
}

func (c *guildmemberlvClient) AddMemberContribution(ctx context.Context, in *AddMemberContributionReq, opts ...grpc.CallOption) (*AddMemberContributionResp, error) {
	out := new(AddMemberContributionResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/AddMemberContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) GetMemberContribution(ctx context.Context, in *GetMemberContributionReq, opts ...grpc.CallOption) (*GetMemberContributionResp, error) {
	out := new(GetMemberContributionResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/GetMemberContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) BatchGetMemberContribution(ctx context.Context, in *BatchGetMemberContributionReq, opts ...grpc.CallOption) (*BatchGetMemberContributionResp, error) {
	out := new(BatchGetMemberContributionResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/BatchGetMemberContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) GetMemberContributionListInOrder(ctx context.Context, in *GetMemberContributionListInOrderReq, opts ...grpc.CallOption) (*BatchGetMemberContributionResp, error) {
	out := new(BatchGetMemberContributionResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/GetMemberContributionListInOrder", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) GetMemberContributionDetailList(ctx context.Context, in *GetMemberContributionDetailListReq, opts ...grpc.CallOption) (*GetMemberContributionDetailListResp, error) {
	out := new(GetMemberContributionDetailListResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/GetMemberContributionDetailList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) ClearMemberContribution(ctx context.Context, in *ClearMemberContributionReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/ClearMemberContribution", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) GetGuildDonateOption(ctx context.Context, in *GetGuildDonateOptionReq, opts ...grpc.CallOption) (*GetGuildDonateOptionResp, error) {
	out := new(GetGuildDonateOptionResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/GetGuildDonateOption", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildmemberlvClient) GetMemberSpecContTypeByDate(ctx context.Context, in *GetMemberSpecContTypeByDateReq, opts ...grpc.CallOption) (*GetMemberContributionDetailListResp, error) {
	out := new(GetMemberContributionDetailListResp)
	err := grpc.Invoke(ctx, "/guildmemberlv.guildmemberlv/GetMemberSpecContTypeByDate", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Guildmemberlv service

type GuildmemberlvServer interface {
	AddMemberContribution(context.Context, *AddMemberContributionReq) (*AddMemberContributionResp, error)
	GetMemberContribution(context.Context, *GetMemberContributionReq) (*GetMemberContributionResp, error)
	BatchGetMemberContribution(context.Context, *BatchGetMemberContributionReq) (*BatchGetMemberContributionResp, error)
	GetMemberContributionListInOrder(context.Context, *GetMemberContributionListInOrderReq) (*BatchGetMemberContributionResp, error)
	GetMemberContributionDetailList(context.Context, *GetMemberContributionDetailListReq) (*GetMemberContributionDetailListResp, error)
	ClearMemberContribution(context.Context, *ClearMemberContributionReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildDonateOption(context.Context, *GetGuildDonateOptionReq) (*GetGuildDonateOptionResp, error)
	GetMemberSpecContTypeByDate(context.Context, *GetMemberSpecContTypeByDateReq) (*GetMemberContributionDetailListResp, error)
}

func RegisterGuildmemberlvServer(s *grpc.Server, srv GuildmemberlvServer) {
	s.RegisterService(&_Guildmemberlv_serviceDesc, srv)
}

func _Guildmemberlv_AddMemberContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddMemberContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).AddMemberContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/AddMemberContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).AddMemberContribution(ctx, req.(*AddMemberContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_GetMemberContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).GetMemberContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/GetMemberContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).GetMemberContribution(ctx, req.(*GetMemberContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_BatchGetMemberContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMemberContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).BatchGetMemberContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/BatchGetMemberContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).BatchGetMemberContribution(ctx, req.(*BatchGetMemberContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_GetMemberContributionListInOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberContributionListInOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).GetMemberContributionListInOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/GetMemberContributionListInOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).GetMemberContributionListInOrder(ctx, req.(*GetMemberContributionListInOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_GetMemberContributionDetailList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberContributionDetailListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).GetMemberContributionDetailList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/GetMemberContributionDetailList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).GetMemberContributionDetailList(ctx, req.(*GetMemberContributionDetailListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_ClearMemberContribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearMemberContributionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).ClearMemberContribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/ClearMemberContribution",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).ClearMemberContribution(ctx, req.(*ClearMemberContributionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_GetGuildDonateOption_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDonateOptionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).GetGuildDonateOption(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/GetGuildDonateOption",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).GetGuildDonateOption(ctx, req.(*GetGuildDonateOptionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Guildmemberlv_GetMemberSpecContTypeByDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMemberSpecContTypeByDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildmemberlvServer).GetMemberSpecContTypeByDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guildmemberlv.guildmemberlv/GetMemberSpecContTypeByDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildmemberlvServer).GetMemberSpecContTypeByDate(ctx, req.(*GetMemberSpecContTypeByDateReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Guildmemberlv_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guildmemberlv.guildmemberlv",
	HandlerType: (*GuildmemberlvServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddMemberContribution",
			Handler:    _Guildmemberlv_AddMemberContribution_Handler,
		},
		{
			MethodName: "GetMemberContribution",
			Handler:    _Guildmemberlv_GetMemberContribution_Handler,
		},
		{
			MethodName: "BatchGetMemberContribution",
			Handler:    _Guildmemberlv_BatchGetMemberContribution_Handler,
		},
		{
			MethodName: "GetMemberContributionListInOrder",
			Handler:    _Guildmemberlv_GetMemberContributionListInOrder_Handler,
		},
		{
			MethodName: "GetMemberContributionDetailList",
			Handler:    _Guildmemberlv_GetMemberContributionDetailList_Handler,
		},
		{
			MethodName: "ClearMemberContribution",
			Handler:    _Guildmemberlv_ClearMemberContribution_Handler,
		},
		{
			MethodName: "GetGuildDonateOption",
			Handler:    _Guildmemberlv_GetGuildDonateOption_Handler,
		},
		{
			MethodName: "GetMemberSpecContTypeByDate",
			Handler:    _Guildmemberlv_GetMemberSpecContTypeByDate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/guildMemberLvSvr/guildmemberlv.proto",
}

func (m *StMemberContributionOper) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StMemberContributionOper) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.OperType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.OperValue))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.OrderDesc)))
	i += copy(dAtA[i:], m.OrderDesc)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.Extend)))
	i += copy(dAtA[i:], m.Extend)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContinuousDays))
	if len(m.SupplementDayList) > 0 {
		for _, num := range m.SupplementDayList {
			dAtA[i] = 0x38
			i++
			i = encodeVarintGuildmemberlv(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x40
	i++
	if m.OptInvalid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *StMemberContributionOrder) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StMemberContributionOrder) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContributionType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContributionValue))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.OrderDesc)))
	i += copy(dAtA[i:], m.OrderDesc)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.Extend)))
	i += copy(dAtA[i:], m.Extend)
	dAtA[i] = 0x40
	i++
	if m.OptInvalid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *StMemberContributionDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StMemberContributionDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContributionType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContributionValue))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.CreateTs))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.OrderDesc)))
	i += copy(dAtA[i:], m.OrderDesc)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(len(m.Extend)))
	i += copy(dAtA[i:], m.Extend)
	return i, nil
}

func (m *StMemberContributionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StMemberContributionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.TotalContribution))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ValidContribution))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.MemberLv))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.MaxMemberLv))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.CurLvContribution))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.MaxLvContribution))
	return i, nil
}

func (m *AddMemberContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddMemberContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	if m.OperInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("oper_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.OperInfo.Size()))
		n1, err := m.OperInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *AddMemberContributionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddMemberContributionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Contribution == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Contribution.Size()))
		n2, err := m.Contribution.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContributionAdded))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ToBonusPeriod))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ToBonusContribution))
	return i, nil
}

func (m *GetMemberContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetMemberContributionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Contribution == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("contribution")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Contribution.Size()))
		n3, err := m.Contribution.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *BatchGetMemberContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetMemberContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGuildmemberlv(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchGetMemberContributionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetMemberContributionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildmemberlv(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMemberContributionListInOrderReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionListInOrderReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.RankType))
	return i, nil
}

func (m *GetMemberContributionDetailListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionDetailListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Offset))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetMemberContributionDetailListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberContributionDetailListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, msg := range m.DetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildmemberlv(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ClearMemberContributionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearMemberContributionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *StGuildDonateOption) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildDonateOption) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.DonateValue))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.MemberContributionAdded))
	dAtA[i] = 0x18
	i++
	if m.IsValid {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetGuildDonateOptionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildDonateOptionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.MemberLv))
	return i, nil
}

func (m *GetGuildDonateOptionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildDonateOptionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OptionList) > 0 {
		for _, msg := range m.OptionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildmemberlv(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetMemberSpecContTypeByDateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberSpecContTypeByDateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.DateTs))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildmemberlv(dAtA, i, uint64(m.ContributionType))
	return i, nil
}

func encodeFixed64Guildmemberlv(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Guildmemberlv(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGuildmemberlv(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StMemberContributionOper) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.OperType))
	n += 1 + sovGuildmemberlv(uint64(m.OperValue))
	l = len(m.OrderId)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	l = len(m.OrderDesc)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	l = len(m.Extend)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	n += 1 + sovGuildmemberlv(uint64(m.ContinuousDays))
	if len(m.SupplementDayList) > 0 {
		for _, e := range m.SupplementDayList {
			n += 1 + sovGuildmemberlv(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *StMemberContributionOrder) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.Uid))
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	n += 1 + sovGuildmemberlv(uint64(m.ContributionType))
	n += 1 + sovGuildmemberlv(uint64(m.ContributionValue))
	l = len(m.OrderId)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	l = len(m.OrderDesc)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	l = len(m.Extend)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	n += 2
	return n
}

func (m *StMemberContributionDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.Uid))
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	n += 1 + sovGuildmemberlv(uint64(m.ContributionType))
	n += 1 + sovGuildmemberlv(uint64(m.ContributionValue))
	n += 1 + sovGuildmemberlv(uint64(m.CreateTs))
	l = len(m.OrderDesc)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	l = len(m.Extend)
	n += 1 + l + sovGuildmemberlv(uint64(l))
	return n
}

func (m *StMemberContributionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.Uid))
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	n += 1 + sovGuildmemberlv(uint64(m.TotalContribution))
	n += 1 + sovGuildmemberlv(uint64(m.ValidContribution))
	n += 1 + sovGuildmemberlv(uint64(m.MemberLv))
	n += 1 + sovGuildmemberlv(uint64(m.MaxMemberLv))
	n += 1 + sovGuildmemberlv(uint64(m.CurLvContribution))
	n += 1 + sovGuildmemberlv(uint64(m.MaxLvContribution))
	return n
}

func (m *AddMemberContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	if m.OperInfo != nil {
		l = m.OperInfo.Size()
		n += 1 + l + sovGuildmemberlv(uint64(l))
	}
	return n
}

func (m *AddMemberContributionResp) Size() (n int) {
	var l int
	_ = l
	if m.Contribution != nil {
		l = m.Contribution.Size()
		n += 1 + l + sovGuildmemberlv(uint64(l))
	}
	n += 1 + sovGuildmemberlv(uint64(m.ContributionAdded))
	n += 1 + sovGuildmemberlv(uint64(m.ToBonusPeriod))
	n += 1 + sovGuildmemberlv(uint64(m.ToBonusContribution))
	return n
}

func (m *GetMemberContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	return n
}

func (m *GetMemberContributionResp) Size() (n int) {
	var l int
	_ = l
	if m.Contribution != nil {
		l = m.Contribution.Size()
		n += 1 + l + sovGuildmemberlv(uint64(l))
	}
	return n
}

func (m *BatchGetMemberContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovGuildmemberlv(uint64(e))
		}
	}
	return n
}

func (m *BatchGetMemberContributionResp) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovGuildmemberlv(uint64(l))
		}
	}
	return n
}

func (m *GetMemberContributionListInOrderReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	n += 1 + sovGuildmemberlv(uint64(m.Offset))
	n += 1 + sovGuildmemberlv(uint64(m.Limit))
	n += 1 + sovGuildmemberlv(uint64(m.RankType))
	return n
}

func (m *GetMemberContributionDetailListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	n += 1 + sovGuildmemberlv(uint64(m.Offset))
	n += 1 + sovGuildmemberlv(uint64(m.Limit))
	return n
}

func (m *GetMemberContributionDetailListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailList) > 0 {
		for _, e := range m.DetailList {
			l = e.Size()
			n += 1 + l + sovGuildmemberlv(uint64(l))
		}
	}
	return n
}

func (m *ClearMemberContributionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	return n
}

func (m *StGuildDonateOption) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.DonateValue))
	n += 1 + sovGuildmemberlv(uint64(m.MemberContributionAdded))
	n += 2
	return n
}

func (m *GetGuildDonateOptionReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.MemberLv))
	return n
}

func (m *GetGuildDonateOptionResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OptionList) > 0 {
		for _, e := range m.OptionList {
			l = e.Size()
			n += 1 + l + sovGuildmemberlv(uint64(l))
		}
	}
	return n
}

func (m *GetMemberSpecContTypeByDateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildmemberlv(uint64(m.GuildId))
	n += 1 + sovGuildmemberlv(uint64(m.DateTs))
	n += 1 + sovGuildmemberlv(uint64(m.ContributionType))
	return n
}

func sovGuildmemberlv(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGuildmemberlv(x uint64) (n int) {
	return sovGuildmemberlv(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *StMemberContributionOper) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StMemberContributionOper: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StMemberContributionOper: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperType", wireType)
			}
			m.OperType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperValue", wireType)
			}
			m.OperValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperValue |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContinuousDays", wireType)
			}
			m.ContinuousDays = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContinuousDays |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildmemberlv
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SupplementDayList = append(m.SupplementDayList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildmemberlv
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildmemberlv
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildmemberlv
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SupplementDayList = append(m.SupplementDayList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SupplementDayList", wireType)
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptInvalid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OptInvalid = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StMemberContributionOrder) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StMemberContributionOrder: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StMemberContributionOrder: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionType", wireType)
			}
			m.ContributionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionValue", wireType)
			}
			m.ContributionValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionValue |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptInvalid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.OptInvalid = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution_value")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StMemberContributionDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StMemberContributionDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StMemberContributionDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionType", wireType)
			}
			m.ContributionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionValue", wireType)
			}
			m.ContributionValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionValue |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateTs", wireType)
			}
			m.CreateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extend", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extend = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution_value")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StMemberContributionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StMemberContributionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StMemberContributionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalContribution", wireType)
			}
			m.TotalContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ValidContribution", wireType)
			}
			m.ValidContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ValidContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberLv", wireType)
			}
			m.MemberLv = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberLv |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxMemberLv", wireType)
			}
			m.MaxMemberLv = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxMemberLv |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurLvContribution", wireType)
			}
			m.CurLvContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurLvContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxLvContribution", wireType)
			}
			m.MaxLvContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxLvContribution |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total_contribution")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("valid_contribution")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("member_lv")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddMemberContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddMemberContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddMemberContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.OperInfo == nil {
				m.OperInfo = &StMemberContributionOper{}
			}
			if err := m.OperInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("oper_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddMemberContributionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddMemberContributionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddMemberContributionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Contribution", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Contribution == nil {
				m.Contribution = &StMemberContributionInfo{}
			}
			if err := m.Contribution.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionAdded", wireType)
			}
			m.ContributionAdded = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionAdded |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToBonusPeriod", wireType)
			}
			m.ToBonusPeriod = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToBonusPeriod |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToBonusContribution", wireType)
			}
			m.ToBonusContribution = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToBonusContribution |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution_added")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Contribution", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Contribution == nil {
				m.Contribution = &StMemberContributionInfo{}
			}
			if err := m.Contribution.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetMemberContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetMemberContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetMemberContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildmemberlv
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildmemberlv
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildmemberlv
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildmemberlv
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetMemberContributionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetMemberContributionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetMemberContributionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &StMemberContributionInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionListInOrderReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionListInOrderReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionListInOrderReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RankType", wireType)
			}
			m.RankType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RankType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionDetailListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionDetailListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionDetailListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Offset", wireType)
			}
			m.Offset = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Offset |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberContributionDetailListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberContributionDetailListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberContributionDetailListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailList = append(m.DetailList, &StMemberContributionDetail{})
			if err := m.DetailList[len(m.DetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearMemberContributionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearMemberContributionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearMemberContributionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildDonateOption) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: StGuildDonateOption: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: StGuildDonateOption: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DonateValue", wireType)
			}
			m.DonateValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DonateValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberContributionAdded", wireType)
			}
			m.MemberContributionAdded = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberContributionAdded |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsValid", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsValid = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("donate_value")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("member_contribution_added")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_valid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildDonateOptionReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildDonateOptionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildDonateOptionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberLv", wireType)
			}
			m.MemberLv = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberLv |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("member_lv")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildDonateOptionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildDonateOptionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildDonateOptionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OptionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OptionList = append(m.OptionList, &StGuildDonateOption{})
			if err := m.OptionList[len(m.OptionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberSpecContTypeByDateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberSpecContTypeByDateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberSpecContTypeByDateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DateTs", wireType)
			}
			m.DateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContributionType", wireType)
			}
			m.ContributionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContributionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildmemberlv(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildmemberlv
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("date_ts")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("contribution_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGuildmemberlv(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGuildmemberlv
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildmemberlv
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGuildmemberlv
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGuildmemberlv
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGuildmemberlv(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGuildmemberlv = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGuildmemberlv   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/guildMemberLvSvr/guildmemberlv.proto", fileDescriptorGuildmemberlv)
}

var fileDescriptorGuildmemberlv = []byte{
	// 1765 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x58, 0x5f, 0x6f, 0x1b, 0x59,
	0x15, 0xef, 0x8c, 0x1d, 0xff, 0x39, 0x69, 0x5a, 0xe7, 0x86, 0x34, 0x8e, 0xb7, 0x4d, 0xa6, 0xb3,
	0x40, 0xbc, 0xa5, 0x4e, 0xb5, 0xed, 0x3e, 0x20, 0x13, 0x2c, 0xfc, 0xaf, 0xd9, 0x69, 0x5c, 0xdb,
	0xd8, 0x4e, 0x51, 0xb5, 0x42, 0xa3, 0xa9, 0x67, 0x92, 0x8e, 0x3a, 0x9e, 0x99, 0xf5, 0xdc, 0x31,
	0xf1, 0x13, 0xf0, 0x00, 0x5a, 0x21, 0x21, 0x21, 0x10, 0x1f, 0x00, 0x35, 0xdf, 0x80, 0x07, 0xc4,
	0x17, 0x80, 0x05, 0x21, 0x04, 0x42, 0xe2, 0x11, 0xa1, 0xf2, 0x92, 0x4f, 0xc0, 0x33, 0xba, 0xf7,
	0x4e, 0xc6, 0x33, 0xf6, 0x38, 0x71, 0xa2, 0x45, 0xda, 0xc7, 0x7b, 0xfe, 0xdc, 0x39, 0xe7, 0x77,
	0xce, 0x3d, 0xf7, 0x77, 0x07, 0xf2, 0xce, 0xb0, 0xff, 0xe8, 0xd8, 0xd5, 0x0d, 0xf5, 0xb9, 0x36,
	0x78, 0xa5, 0x0d, 0x1b, 0xa3, 0xee, 0x68, 0xc8, 0x04, 0x03, 0x2a, 0x30, 0x46, 0xbb, 0xf6, 0xd0,
	0xc2, 0x16, 0x5a, 0x09, 0x09, 0x73, 0x5f, 0xed, 0x5b, 0x83, 0x81, 0x65, 0x3e, 0xc2, 0xc6, 0xc8,
	0xd6, 0xfb, 0x6f, 0x0c, 0xed, 0x91, 0xf3, 0xe6, 0x95, 0xab, 0x1b, 0x58, 0x37, 0xf1, 0xd8, 0xd6,
	0x98, 0x93, 0xf8, 0x17, 0x1e, 0xb2, 0x5d, 0xcc, 0xb6, 0xae, 0x5a, 0x26, 0x1e, 0xea, 0xaf, 0x5c,
	0xac, 0x5b, 0x66, 0xcb, 0xd6, 0x86, 0xe8, 0x3e, 0xa4, 0x2d, 0x5b, 0x1b, 0xca, 0xc4, 0x3e, 0xcb,
	0x09, 0x7c, 0x7e, 0xa5, 0x12, 0xff, 0xfc, 0x5f, 0xdb, 0x37, 0x3a, 0x29, 0x22, 0xee, 0x8d, 0x6d,
	0x0d, 0xbd, 0x0f, 0x40, 0x4d, 0x46, 0x8a, 0xe1, 0x6a, 0x59, 0x5e, 0xe0, 0xf3, 0x4b, 0x9e, 0x0d,
	0x75, 0x7d, 0x41, 0xc4, 0x68, 0x1b, 0x52, 0xd6, 0x50, 0xd5, 0x86, 0xb2, 0xae, 0x66, 0x63, 0x02,
	0x97, 0x4f, 0x7b, 0x26, 0x49, 0x2a, 0x95, 0x54, 0xba, 0x0b, 0x35, 0x50, 0x35, 0xa7, 0x9f, 0x8d,
	0x07, 0x4c, 0xd2, 0x54, 0x5e, 0xd3, 0x9c, 0x3e, 0xba, 0x0b, 0x09, 0xed, 0x04, 0x6b, 0xa6, 0x9a,
	0x5d, 0x0a, 0x18, 0x78, 0x32, 0x54, 0x80, 0xdb, 0x7d, 0xcb, 0xc4, 0xba, 0xe9, 0x5a, 0xae, 0x23,
	0xab, 0xca, 0xd8, 0xc9, 0x26, 0x04, 0xce, 0x8f, 0xf8, 0xd6, 0x44, 0x59, 0x53, 0xc6, 0x0e, 0xda,
	0x85, 0x35, 0xc7, 0xb5, 0x6d, 0x43, 0x1b, 0x68, 0x26, 0x26, 0xe6, 0xb2, 0xa1, 0x3b, 0x38, 0x9b,
	0x14, 0x62, 0xf9, 0x95, 0xce, 0xea, 0x44, 0x55, 0x53, 0xc6, 0x0d, 0xdd, 0xc1, 0xe8, 0x6b, 0xb0,
	0x6c, 0xd9, 0x58, 0xd6, 0xcd, 0x91, 0x62, 0xe8, 0x6a, 0x36, 0x25, 0x70, 0xf9, 0x94, 0xb7, 0x35,
	0x58, 0x36, 0x96, 0x98, 0x5c, 0xfc, 0x23, 0x0f, 0x9b, 0x91, 0x70, 0x92, 0x2c, 0xd0, 0x1d, 0x88,
	0xb9, 0xba, 0x1a, 0x42, 0x92, 0x08, 0x08, 0x3e, 0xb4, 0x76, 0x04, 0x1f, 0x3e, 0xa0, 0x4c, 0x52,
	0xa9, 0xa4, 0xa2, 0x0f, 0x61, 0xb5, 0x1f, 0xd8, 0x8d, 0x15, 0x24, 0x16, 0xb0, 0xcc, 0x04, 0xd5,
	0xb4, 0x30, 0x4f, 0x00, 0x85, 0x5c, 0x58, 0x81, 0xe2, 0x81, 0x02, 0x85, 0xb6, 0x9c, 0x2d, 0xd4,
	0x92, 0xc0, 0x5f, 0x56, 0xa8, 0xc4, 0x65, 0x85, 0x4a, 0x46, 0x14, 0x6a, 0x41, 0x24, 0xdf, 0xf2,
	0x90, 0x8b, 0x42, 0xb2, 0xa6, 0x61, 0x45, 0x37, 0xbe, 0xfc, 0x50, 0xde, 0x87, 0x74, 0x7f, 0xa8,
	0x29, 0x58, 0x93, 0xb1, 0x43, 0x1b, 0xd6, 0x3f, 0x3b, 0x4c, 0xdc, 0x73, 0xbe, 0x00, 0x30, 0xc5,
	0xff, 0xce, 0x39, 0xbe, 0x92, 0x79, 0x64, 0x5d, 0x1f, 0xa3, 0x27, 0x80, 0xb0, 0x85, 0x15, 0x43,
	0x0e, 0xa6, 0x15, 0x02, 0x69, 0x95, 0xea, 0x83, 0x5f, 0x24, 0x4e, 0xb4, 0x72, 0x61, 0xa7, 0x78,
	0xd0, 0x89, 0xea, 0x43, 0x4e, 0xf7, 0x21, 0xcd, 0x06, 0x96, 0x6c, 0x8c, 0x68, 0xc7, 0xf9, 0x28,
	0x0d, 0xbc, 0x69, 0x87, 0xf2, 0xb0, 0x32, 0x50, 0x4e, 0xe4, 0x89, 0x59, 0xf0, 0x58, 0x2f, 0x0f,
	0x94, 0x93, 0xf3, 0xb9, 0x88, 0x3e, 0x82, 0xb5, 0xbe, 0x4b, 0x4c, 0xc2, 0x21, 0x24, 0x03, 0xf6,
	0xab, 0x7d, 0x77, 0xd8, 0x18, 0x85, 0x42, 0xf8, 0x08, 0xd6, 0xc8, 0xfe, 0xd3, 0x5e, 0xa9, 0xa0,
	0xd7, 0x40, 0x39, 0x09, 0x7b, 0x89, 0x3f, 0xe6, 0x20, 0x5b, 0x56, 0xd5, 0x59, 0xe4, 0x3b, 0xda,
	0xa7, 0x21, 0x80, 0xb9, 0x28, 0x80, 0x6b, 0xde, 0x60, 0xd5, 0xcd, 0x23, 0x8b, 0x96, 0x60, 0xf9,
	0xf1, 0xce, 0x6e, 0x78, 0xa6, 0xcf, 0x1b, 0xca, 0x6c, 0xf6, 0x92, 0xfa, 0x8a, 0x3f, 0xe1, 0x61,
	0x73, 0x4e, 0x0c, 0x8e, 0x8d, 0x0e, 0xe0, 0x66, 0x28, 0x21, 0x6e, 0xe1, 0xcf, 0x90, 0xcd, 0x3b,
	0x21, 0xe7, 0x99, 0x23, 0xa0, 0xa8, 0xaa, 0xa6, 0x86, 0xc6, 0x7d, 0xe8, 0x08, 0x94, 0x89, 0x1a,
	0x3d, 0x84, 0xdb, 0xd8, 0x92, 0x5f, 0x59, 0xa6, 0xeb, 0xc8, 0xb6, 0x36, 0xd4, 0x2d, 0x36, 0xfd,
	0xcf, 0xd1, 0x58, 0xc1, 0x56, 0x85, 0xe8, 0xda, 0x54, 0x85, 0xbe, 0x09, 0xeb, 0xbe, 0xf5, 0x54,
	0x0b, 0x71, 0xfe, 0x57, 0xd6, 0x3c, 0x9f, 0x50, 0x2d, 0xbe, 0x05, 0xd9, 0x7d, 0x0d, 0x5f, 0xaf,
	0x14, 0xe2, 0x6b, 0xd8, 0x9c, 0xe3, 0xfc, 0x05, 0x63, 0x28, 0x7e, 0x02, 0xf7, 0x2a, 0x0a, 0xee,
	0xbf, 0xbe, 0x76, 0xac, 0x68, 0x13, 0x52, 0xae, 0xae, 0xb2, 0x9b, 0x8a, 0xa7, 0x37, 0x55, 0xd2,
	0xd5, 0x55, 0x72, 0x3f, 0x89, 0x47, 0xb0, 0x75, 0xd1, 0xe6, 0x8e, 0x4d, 0x7a, 0x8e, 0xb4, 0x1b,
	0xf3, 0xe6, 0x84, 0xd8, 0x55, 0x12, 0x49, 0x11, 0x4f, 0xfa, 0x9d, 0xb7, 0x1c, 0xbc, 0x1f, 0xf9,
	0x0d, 0xa2, 0x95, 0xd8, 0x3d, 0xb7, 0x50, 0x2e, 0x77, 0x21, 0x61, 0x1d, 0x1d, 0x39, 0x1a, 0xc9,
	0x64, 0xd2, 0x13, 0x9e, 0x0c, 0xe5, 0x60, 0xc9, 0xd0, 0x07, 0x3a, 0x0e, 0x35, 0x0c, 0x13, 0x91,
	0x99, 0x31, 0x54, 0xcc, 0x37, 0x6c, 0x72, 0xc7, 0x83, 0x93, 0x95, 0x88, 0xc9, 0xc4, 0x16, 0x7f,
	0x08, 0x62, 0x64, 0x90, 0xec, 0xf2, 0x20, 0xa1, 0xfe, 0x7f, 0x63, 0x14, 0x3f, 0x9d, 0x83, 0x52,
	0x30, 0x00, 0xc7, 0x46, 0xcf, 0x60, 0x59, 0xa5, 0x92, 0x60, 0x55, 0x3e, 0x58, 0xa0, 0x2a, 0x6c,
	0x9f, 0x0e, 0xa8, 0xfe, 0x7e, 0xe2, 0xb7, 0x21, 0x57, 0x35, 0x34, 0x65, 0x78, 0xcd, 0x73, 0xf0,
	0x1b, 0x0e, 0xd6, 0xba, 0x78, 0x9f, 0xac, 0x6a, 0x96, 0xa9, 0x60, 0xad, 0x65, 0xd3, 0x93, 0xbf,
	0x03, 0x37, 0x55, 0xba, 0xf6, 0xae, 0xbd, 0xa0, 0xf3, 0x32, 0xd3, 0xb0, 0x0b, 0xef, 0x3b, 0xb0,
	0xe9, 0xcd, 0xe8, 0x39, 0x93, 0xe2, 0xdc, 0x6b, 0x63, 0x30, 0x13, 0x21, 0x9b, 0x17, 0xdb, 0x90,
	0xd2, 0x1d, 0x99, 0xd1, 0x02, 0x72, 0xd9, 0x9c, 0xd3, 0x82, 0xa4, 0xee, 0xbc, 0xa0, 0x9c, 0x60,
	0x0f, 0x36, 0xf6, 0xb5, 0xd9, 0x18, 0x49, 0x7e, 0xa1, 0x8b, 0x84, 0x8b, 0xba, 0x48, 0x44, 0x99,
	0x8e, 0x89, 0x08, 0x6f, 0xc7, 0x46, 0x55, 0x4a, 0x4a, 0x48, 0xbc, 0x81, 0x42, 0x88, 0x33, 0x85,
	0x98, 0x75, 0x06, 0xe6, 0x46, 0x2b, 0xf0, 0x2b, 0x0e, 0xb6, 0xfc, 0xaa, 0x77, 0x6d, 0xad, 0x4f,
	0x52, 0x24, 0xfd, 0x58, 0x19, 0xd7, 0x14, 0xac, 0x2d, 0xd4, 0x72, 0xf7, 0x20, 0xa9, 0x7a, 0xa4,
	0x21, 0x88, 0x59, 0x42, 0x65, 0x94, 0xe1, 0xea, 0xec, 0xe5, 0xc1, 0x4f, 0x79, 0x48, 0xb7, 0xda,
	0xf5, 0x8e, 0xdc, 0x7b, 0xd9, 0xae, 0xa3, 0x55, 0x58, 0xa1, 0x8b, 0xea, 0xc7, 0xf5, 0xea, 0x81,
	0x2c, 0x35, 0x33, 0x1c, 0xba, 0x0b, 0xd9, 0x90, 0x48, 0xee, 0x1e, 0xb6, 0xdb, 0x8d, 0xfa, 0xf3,
	0x7a, 0xb3, 0x97, 0xe1, 0xd1, 0x6d, 0x58, 0xa6, 0xda, 0x5a, 0xab, 0x59, 0xee, 0xd5, 0x33, 0x31,
	0x94, 0x83, 0x3b, 0xcc, 0xbc, 0xdc, 0x96, 0x7a, 0xe5, 0x86, 0x2c, 0x35, 0x9f, 0xd5, 0xab, 0x3d,
	0xa9, 0xd5, 0xcc, 0xc4, 0xd1, 0x3a, 0xac, 0x32, 0x5d, 0xab, 0xd9, 0x3d, 0x7c, 0x5e, 0x97, 0xf7,
	0xcb, 0xcf, 0xeb, 0x99, 0x25, 0x74, 0x0f, 0x36, 0x43, 0xe2, 0x8f, 0xcb, 0xed, 0xf6, 0x4b, 0xb9,
	0x5a, 0x6f, 0xf6, 0xea, 0x9d, 0x4c, 0xc2, 0x8f, 0xa9, 0x72, 0xf8, 0x52, 0xde, 0x97, 0x9e, 0xf6,
	0x32, 0x49, 0x7f, 0xa3, 0x6e, 0xbd, 0x59, 0x93, 0xdb, 0x9d, 0x7a, 0x97, 0x04, 0x93, 0x42, 0xb7,
	0x00, 0xa8, 0xb8, 0xd1, 0xea, 0xf5, 0x5a, 0x99, 0x34, 0x42, 0x70, 0x6b, 0x62, 0x56, 0x29, 0x57,
	0x0f, 0x32, 0x1a, 0xda, 0x80, 0x35, 0x2a, 0x6b, 0x3d, 0x7d, 0x2a, 0x55, 0xa5, 0x72, 0x43, 0x2e,
	0x7f, 0xaf, 0xdc, 0xa9, 0x65, 0x8e, 0x1e, 0xfc, 0x93, 0x87, 0xd5, 0x6a, 0xab, 0xd9, 0xeb, 0x48,
	0x95, 0x43, 0x12, 0x2f, 0x03, 0x64, 0x03, 0xd6, 0x88, 0x70, 0x92, 0x7d, 0xad, 0x2c, 0x35, 0x5e,
	0x32, 0x58, 0xc2, 0x8a, 0x10, 0x2c, 0xeb, 0x6c, 0x2f, 0x0f, 0x16, 0xcf, 0x89, 0x82, 0xc3, 0x9c,
	0xa2, 0xc1, 0x61, 0xba, 0x19, 0x70, 0x42, 0xe2, 0x59, 0x70, 0xa8, 0x3a, 0x0c, 0x0e, 0x15, 0xcd,
	0x82, 0x43, 0xc5, 0x01, 0x70, 0x26, 0x66, 0x13, 0x70, 0xa8, 0x6c, 0x1a, 0x1c, 0x74, 0x6f, 0x3a,
	0x5b, 0xb2, 0x92, 0x9a, 0x87, 0xad, 0xc3, 0x6e, 0xe6, 0x4f, 0x1c, 0x7a, 0xcf, 0xcb, 0xcb, 0x4b,
	0x37, 0xa0, 0xfc, 0x33, 0xf7, 0xa0, 0xc3, 0x94, 0x3e, 0xae, 0x9d, 0x72, 0xf3, 0x80, 0x81, 0x7b,
	0x13, 0x52, 0xbd, 0x56, 0x4f, 0xae, 0xd5, 0xbb, 0xd5, 0xcc, 0x0d, 0xb4, 0x0c, 0x49, 0xb2, 0x2a,
	0x77, 0xab, 0x19, 0x8e, 0x44, 0xfb, 0xa2, 0xdc, 0x90, 0x6a, 0x4c, 0xc9, 0xa3, 0x15, 0x48, 0xb3,
	0x35, 0x51, 0xc7, 0x1e, 0xff, 0xee, 0x26, 0x84, 0xdf, 0xb3, 0xe8, 0xb7, 0x3c, 0xac, 0x47, 0xb2,
	0x1d, 0x34, 0x7d, 0x8d, 0xcd, 0xe3, 0x65, 0xb9, 0xfc, 0x62, 0x86, 0x8e, 0x2d, 0xfe, 0x81, 0xfb,
	0xd1, 0xe9, 0x59, 0x8c, 0xfb, 0xd9, 0xe9, 0x59, 0x0c, 0xb9, 0xc5, 0xe3, 0x22, 0x2e, 0x9a, 0x45,
	0xab, 0xa8, 0x16, 0x95, 0xe2, 0xb8, 0x68, 0x14, 0x7f, 0x79, 0x7a, 0x16, 0x3b, 0xe5, 0x0a, 0xae,
	0xb0, 0xe7, 0xea, 0x6a, 0x49, 0x28, 0x1c, 0x0b, 0x7b, 0xe7, 0xa7, 0xba, 0x24, 0x14, 0xb0, 0xb0,
	0xe7, 0xbf, 0x9a, 0x4b, 0x42, 0xc1, 0xf4, 0x96, 0x74, 0x7c, 0x96, 0x84, 0x4f, 0x0a, 0x96, 0xb0,
	0x77, 0xfe, 0xc8, 0x2a, 0x09, 0x05, 0xf5, 0x7c, 0x45, 0x1e, 0x01, 0x25, 0xa1, 0xa0, 0x10, 0x73,
	0xff, 0x79, 0x54, 0x12, 0x0a, 0x63, 0x61, 0x6f, 0xea, 0x61, 0x5b, 0x12, 0x0a, 0x86, 0xb0, 0x17,
	0xf1, 0x7c, 0x7d, 0x28, 0x38, 0xb6, 0xa1, 0x63, 0xe1, 0x07, 0x3a, 0x7e, 0x2d, 0xec, 0x3c, 0xdc,
	0x29, 0x7d, 0x1f, 0xfd, 0x9a, 0x83, 0xf5, 0xc8, 0x9b, 0x68, 0x06, 0xb5, 0x79, 0xb4, 0x64, 0x06,
	0xb5, 0xb9, 0x14, 0x43, 0xfc, 0x80, 0x80, 0xc6, 0x13, 0xd0, 0xe2, 0x04, 0x34, 0x02, 0xd3, 0x9d,
	0x68, 0x94, 0xd0, 0xef, 0x39, 0xc8, 0xcd, 0x27, 0x2c, 0xe8, 0xe1, 0xd4, 0x37, 0x2f, 0x24, 0x4e,
	0xb9, 0xc2, 0x15, 0xac, 0x1d, 0x5b, 0x2c, 0x91, 0x30, 0x63, 0xa1, 0x30, 0xbf, 0xe1, 0x85, 0x19,
	0x0d, 0xe3, 0x54, 0xec, 0x7f, 0xe7, 0x40, 0xb8, 0x8c, 0x03, 0xa1, 0xc7, 0x8b, 0xa0, 0x16, 0x26,
	0x4d, 0x57, 0xcd, 0xe3, 0x19, 0xc9, 0x23, 0x4e, 0xf2, 0x48, 0x1d, 0x17, 0xad, 0xa2, 0x51, 0xc4,
	0x34, 0x97, 0x27, 0x53, 0xfd, 0x48, 0xfa, 0x8d, 0x32, 0x16, 0xd6, 0x37, 0x94, 0xa0, 0xb0, 0x36,
	0xf5, 0x69, 0x54, 0x09, 0xfd, 0x95, 0x83, 0xed, 0x4b, 0x18, 0x0b, 0xfa, 0x70, 0x91, 0x94, 0x42,
	0x14, 0x2b, 0xf7, 0xf8, 0xaa, 0x2e, 0x8e, 0x2d, 0xd6, 0x48, 0x5a, 0x4b, 0x34, 0x2d, 0x52, 0x1e,
	0xcb, 0x3b, 0x70, 0x85, 0x79, 0xe7, 0x2d, 0x32, 0x3f, 0xf4, 0x73, 0x0e, 0x36, 0xe6, 0xf0, 0x21,
	0x34, 0xcd, 0xb0, 0xe6, 0xf3, 0xa6, 0xdc, 0xdd, 0x5d, 0xff, 0xff, 0xd9, 0x6e, 0xf7, 0xa0, 0xc2,
	0xfe, 0x9f, 0xd5, 0x07, 0x36, 0x1e, 0xcb, 0xed, 0x0a, 0x6b, 0xf8, 0xc4, 0x42, 0x0d, 0xff, 0x19,
	0x07, 0x5f, 0x89, 0xa2, 0x1f, 0xe8, 0xeb, 0xb3, 0x10, 0x45, 0x31, 0x9c, 0xdc, 0xce, 0x42, 0x76,
	0x8e, 0x2d, 0x6e, 0x93, 0xa0, 0x92, 0x24, 0x28, 0xde, 0xa4, 0x21, 0xdd, 0x22, 0x33, 0xc8, 0x67,
	0x47, 0x25, 0xf4, 0x0f, 0x0e, 0xde, 0xbb, 0x80, 0xa7, 0xa0, 0xc2, 0xbc, 0xa2, 0x45, 0x72, 0x9a,
	0x6b, 0xd5, 0xf8, 0xbb, 0x24, 0xc6, 0x94, 0x5f, 0x63, 0x5c, 0xb4, 0x69, 0xa4, 0xc5, 0x0b, 0x66,
	0xaa, 0x47, 0x8b, 0x4a, 0x42, 0xc1, 0x66, 0x13, 0x31, 0x44, 0x82, 0x4a, 0xb9, 0xc4, 0x67, 0xa7,
	0x67, 0xb1, 0xb7, 0x27, 0x95, 0xcc, 0xe7, 0xef, 0xb6, 0xb8, 0xbf, 0xbd, 0xdb, 0xe2, 0xfe, 0xfd,
	0x6e, 0x8b, 0xfb, 0xc5, 0x7f, 0xb6, 0x6e, 0xfc, 0x2f, 0x00, 0x00, 0xff, 0xff, 0xd7, 0x61, 0xab,
	0x3c, 0x43, 0x15, 0x00, 0x00,
}
