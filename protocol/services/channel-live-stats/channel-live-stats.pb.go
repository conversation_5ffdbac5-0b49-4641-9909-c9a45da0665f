// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-live-stats/channel-live-stats.proto

package channel_live_stats // import "golang.52tt.com/protocol/services/channel-live-stats"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AnchorFlag int32

const (
	AnchorFlag_Common          AnchorFlag = 0
	AnchorFlag_Valid           AnchorFlag = 1
	AnchorFlag_NewValid        AnchorFlag = 2
	AnchorFlag_Live            AnchorFlag = 3
	AnchorFlag_Potential       AnchorFlag = 4
	AnchorFlag_NewAdd          AnchorFlag = 5
	AnchorFlag_TodayLive       AnchorFlag = 6
	AnchorFlag_BreakLive       AnchorFlag = 7
	AnchorFlag_QUALITY         AnchorFlag = 8
	AnchorFlag_ACTIVE          AnchorFlag = 9
	AnchorFlag_NEW_ACTIVE      AnchorFlag = 10
	AnchorFlag_HIGH_INCOME     AnchorFlag = 11
	AnchorFlag_Profession_Prac AnchorFlag = 12
	AnchorFlag_Is_Living       AnchorFlag = 13
)

var AnchorFlag_name = map[int32]string{
	0:  "Common",
	1:  "Valid",
	2:  "NewValid",
	3:  "Live",
	4:  "Potential",
	5:  "NewAdd",
	6:  "TodayLive",
	7:  "BreakLive",
	8:  "QUALITY",
	9:  "ACTIVE",
	10: "NEW_ACTIVE",
	11: "HIGH_INCOME",
	12: "Profession_Prac",
	13: "Is_Living",
}
var AnchorFlag_value = map[string]int32{
	"Common":          0,
	"Valid":           1,
	"NewValid":        2,
	"Live":            3,
	"Potential":       4,
	"NewAdd":          5,
	"TodayLive":       6,
	"BreakLive":       7,
	"QUALITY":         8,
	"ACTIVE":          9,
	"NEW_ACTIVE":      10,
	"HIGH_INCOME":     11,
	"Profession_Prac": 12,
	"Is_Living":       13,
}

func (x AnchorFlag) String() string {
	return proto.EnumName(AnchorFlag_name, int32(x))
}
func (AnchorFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{0}
}

// 关键指标类型
type KpiType int32

const (
	KpiType_Kpi_Type_Invalid           KpiType = 0
	KpiType_Kpi_Type_Guild_Income      KpiType = 1
	KpiType_Kpi_Type_New_Sign_Anchor   KpiType = 2
	KpiType_Kpi_Type_Live_Anchor       KpiType = 3
	KpiType_Kpi_Type_Two_Wan_Anchor    KpiType = 4
	KpiType_Kpi_Type_Ten_Wan_Anchor    KpiType = 5
	KpiType_Kpi_Type_Pro_Anchor        KpiType = 6
	KpiType_Kpi_Type_Mature_Anchor     KpiType = 7
	KpiType_Kpi_Type_Pot_Active_Anchor KpiType = 8
)

var KpiType_name = map[int32]string{
	0: "Kpi_Type_Invalid",
	1: "Kpi_Type_Guild_Income",
	2: "Kpi_Type_New_Sign_Anchor",
	3: "Kpi_Type_Live_Anchor",
	4: "Kpi_Type_Two_Wan_Anchor",
	5: "Kpi_Type_Ten_Wan_Anchor",
	6: "Kpi_Type_Pro_Anchor",
	7: "Kpi_Type_Mature_Anchor",
	8: "Kpi_Type_Pot_Active_Anchor",
}
var KpiType_value = map[string]int32{
	"Kpi_Type_Invalid":           0,
	"Kpi_Type_Guild_Income":      1,
	"Kpi_Type_New_Sign_Anchor":   2,
	"Kpi_Type_Live_Anchor":       3,
	"Kpi_Type_Two_Wan_Anchor":    4,
	"Kpi_Type_Ten_Wan_Anchor":    5,
	"Kpi_Type_Pro_Anchor":        6,
	"Kpi_Type_Mature_Anchor":     7,
	"Kpi_Type_Pot_Active_Anchor": 8,
}

func (x KpiType) String() string {
	return proto.EnumName(KpiType_name, int32(x))
}
func (KpiType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{1}
}

type GetGuildDailyAnchorListReq_Condition int32

const (
	GetGuildDailyAnchorListReq_Live    GetGuildDailyAnchorListReq_Condition = 0
	GetGuildDailyAnchorListReq_Valid   GetGuildDailyAnchorListReq_Condition = 1
	GetGuildDailyAnchorListReq_Invalid GetGuildDailyAnchorListReq_Condition = 2
)

var GetGuildDailyAnchorListReq_Condition_name = map[int32]string{
	0: "Live",
	1: "Valid",
	2: "Invalid",
}
var GetGuildDailyAnchorListReq_Condition_value = map[string]int32{
	"Live":    0,
	"Valid":   1,
	"Invalid": 2,
}

func (x GetGuildDailyAnchorListReq_Condition) String() string {
	return proto.EnumName(GetGuildDailyAnchorListReq_Condition_name, int32(x))
}
func (GetGuildDailyAnchorListReq_Condition) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{15, 0}
}

type GetGuildWeeklyAnchorListReq_Condition int32

const (
	GetGuildWeeklyAnchorListReq_Live                GetGuildWeeklyAnchorListReq_Condition = 0
	GetGuildWeeklyAnchorListReq_Valid               GetGuildWeeklyAnchorListReq_Condition = 1
	GetGuildWeeklyAnchorListReq_Invalid             GetGuildWeeklyAnchorListReq_Condition = 2
	GetGuildWeeklyAnchorListReq_AppointActiveMinCnt GetGuildWeeklyAnchorListReq_Condition = 3
)

var GetGuildWeeklyAnchorListReq_Condition_name = map[int32]string{
	0: "Live",
	1: "Valid",
	2: "Invalid",
	3: "AppointActiveMinCnt",
}
var GetGuildWeeklyAnchorListReq_Condition_value = map[string]int32{
	"Live":                0,
	"Valid":               1,
	"Invalid":             2,
	"AppointActiveMinCnt": 3,
}

func (x GetGuildWeeklyAnchorListReq_Condition) String() string {
	return proto.EnumName(GetGuildWeeklyAnchorListReq_Condition_name, int32(x))
}
func (GetGuildWeeklyAnchorListReq_Condition) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{31, 0}
}

type GetGuildAnchorListReq_SortType int32

const (
	GetGuildAnchorListReq_Sort_Type_Invalid   GetGuildAnchorListReq_SortType = 0
	GetGuildAnchorListReq_Sort_Type_Fee       GetGuildAnchorListReq_SortType = 1
	GetGuildAnchorListReq_Sort_Type_Income    GetGuildAnchorListReq_SortType = 2
	GetGuildAnchorListReq_Sort_Type_Min       GetGuildAnchorListReq_SortType = 3
	GetGuildAnchorListReq_Sort_Type_Pkg_Ratio GetGuildAnchorListReq_SortType = 4
)

var GetGuildAnchorListReq_SortType_name = map[int32]string{
	0: "Sort_Type_Invalid",
	1: "Sort_Type_Fee",
	2: "Sort_Type_Income",
	3: "Sort_Type_Min",
	4: "Sort_Type_Pkg_Ratio",
}
var GetGuildAnchorListReq_SortType_value = map[string]int32{
	"Sort_Type_Invalid":   0,
	"Sort_Type_Fee":       1,
	"Sort_Type_Income":    2,
	"Sort_Type_Min":       3,
	"Sort_Type_Pkg_Ratio": 4,
}

func (x GetGuildAnchorListReq_SortType) String() string {
	return proto.EnumName(GetGuildAnchorListReq_SortType_name, int32(x))
}
func (GetGuildAnchorListReq_SortType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{56, 0}
}

// 定时任务类型
type TriggerTimerReq_TimerType int32

const (
	TriggerTimerReq_Timer_Type_Invalid                    TriggerTimerReq_TimerType = 0
	TriggerTimerReq_Timer_Type_GuildWeekBusinessAnalysis  TriggerTimerReq_TimerType = 1
	TriggerTimerReq_Timer_Type_GuildMonthBusinessAnalysis TriggerTimerReq_TimerType = 2
)

var TriggerTimerReq_TimerType_name = map[int32]string{
	0: "Timer_Type_Invalid",
	1: "Timer_Type_GuildWeekBusinessAnalysis",
	2: "Timer_Type_GuildMonthBusinessAnalysis",
}
var TriggerTimerReq_TimerType_value = map[string]int32{
	"Timer_Type_Invalid":                    0,
	"Timer_Type_GuildWeekBusinessAnalysis":  1,
	"Timer_Type_GuildMonthBusinessAnalysis": 2,
}

func (x TriggerTimerReq_TimerType) String() string {
	return proto.EnumName(TriggerTimerReq_TimerType_name, int32(x))
}
func (TriggerTimerReq_TimerType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{74, 0}
}

type AnchorBaseInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SignGuildId          uint32   `protobuf:"varint,2,opt,name=sign_guild_id,json=signGuildId,proto3" json:"sign_guild_id,omitempty"`
	LiveRoomId           uint32   `protobuf:"varint,3,opt,name=live_room_id,json=liveRoomId,proto3" json:"live_room_id,omitempty"`
	LastLiveAt           uint32   `protobuf:"varint,4,opt,name=last_live_at,json=lastLiveAt,proto3" json:"last_live_at,omitempty"`
	AgentUid             uint32   `protobuf:"varint,5,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	FirstLiveTs          uint32   `protobuf:"varint,6,opt,name=first_live_ts,json=firstLiveTs,proto3" json:"first_live_ts,omitempty"`
	ChannelLiveId        uint32   `protobuf:"varint,7,opt,name=channel_live_id,json=channelLiveId,proto3" json:"channel_live_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorBaseInfo) Reset()         { *m = AnchorBaseInfo{} }
func (m *AnchorBaseInfo) String() string { return proto.CompactTextString(m) }
func (*AnchorBaseInfo) ProtoMessage()    {}
func (*AnchorBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{0}
}
func (m *AnchorBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorBaseInfo.Unmarshal(m, b)
}
func (m *AnchorBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorBaseInfo.Marshal(b, m, deterministic)
}
func (dst *AnchorBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorBaseInfo.Merge(dst, src)
}
func (m *AnchorBaseInfo) XXX_Size() int {
	return xxx_messageInfo_AnchorBaseInfo.Size(m)
}
func (m *AnchorBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorBaseInfo proto.InternalMessageInfo

func (m *AnchorBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorBaseInfo) GetSignGuildId() uint32 {
	if m != nil {
		return m.SignGuildId
	}
	return 0
}

func (m *AnchorBaseInfo) GetLiveRoomId() uint32 {
	if m != nil {
		return m.LiveRoomId
	}
	return 0
}

func (m *AnchorBaseInfo) GetLastLiveAt() uint32 {
	if m != nil {
		return m.LastLiveAt
	}
	return 0
}

func (m *AnchorBaseInfo) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *AnchorBaseInfo) GetFirstLiveTs() uint32 {
	if m != nil {
		return m.FirstLiveTs
	}
	return 0
}

func (m *AnchorBaseInfo) GetChannelLiveId() uint32 {
	if m != nil {
		return m.ChannelLiveId
	}
	return 0
}

type GetAnchorBaseInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorBaseInfoReq) Reset()         { *m = GetAnchorBaseInfoReq{} }
func (m *GetAnchorBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorBaseInfoReq) ProtoMessage()    {}
func (*GetAnchorBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{1}
}
func (m *GetAnchorBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorBaseInfoReq.Unmarshal(m, b)
}
func (m *GetAnchorBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorBaseInfoReq.Merge(dst, src)
}
func (m *GetAnchorBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorBaseInfoReq.Size(m)
}
func (m *GetAnchorBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorBaseInfoReq proto.InternalMessageInfo

func (m *GetAnchorBaseInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAnchorBaseInfoResp struct {
	Info                 *AnchorBaseInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAnchorBaseInfoResp) Reset()         { *m = GetAnchorBaseInfoResp{} }
func (m *GetAnchorBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorBaseInfoResp) ProtoMessage()    {}
func (*GetAnchorBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{2}
}
func (m *GetAnchorBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorBaseInfoResp.Unmarshal(m, b)
}
func (m *GetAnchorBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorBaseInfoResp.Merge(dst, src)
}
func (m *GetAnchorBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorBaseInfoResp.Size(m)
}
func (m *GetAnchorBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorBaseInfoResp proto.InternalMessageInfo

func (m *GetAnchorBaseInfoResp) GetInfo() *AnchorBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchGetAnchorBaseInfoReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorBaseInfoReq) Reset()         { *m = BatchGetAnchorBaseInfoReq{} }
func (m *BatchGetAnchorBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorBaseInfoReq) ProtoMessage()    {}
func (*BatchGetAnchorBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{3}
}
func (m *BatchGetAnchorBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorBaseInfoReq.Unmarshal(m, b)
}
func (m *BatchGetAnchorBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorBaseInfoReq.Merge(dst, src)
}
func (m *BatchGetAnchorBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorBaseInfoReq.Size(m)
}
func (m *BatchGetAnchorBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorBaseInfoReq proto.InternalMessageInfo

func (m *BatchGetAnchorBaseInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetAnchorBaseInfoResp struct {
	InfoList             []*AnchorBaseInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetAnchorBaseInfoResp) Reset()         { *m = BatchGetAnchorBaseInfoResp{} }
func (m *BatchGetAnchorBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorBaseInfoResp) ProtoMessage()    {}
func (*BatchGetAnchorBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{4}
}
func (m *BatchGetAnchorBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorBaseInfoResp.Unmarshal(m, b)
}
func (m *BatchGetAnchorBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorBaseInfoResp.Merge(dst, src)
}
func (m *BatchGetAnchorBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorBaseInfoResp.Size(m)
}
func (m *BatchGetAnchorBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorBaseInfoResp proto.InternalMessageInfo

func (m *BatchGetAnchorBaseInfoResp) GetInfoList() []*AnchorBaseInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type GetAnchorBaseInfoListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	AnchorFlag           uint32   `protobuf:"varint,4,opt,name=anchor_flag,json=anchorFlag,proto3" json:"anchor_flag,omitempty"`
	AgentUid             uint32   `protobuf:"varint,5,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	MinFirstLiveTs       uint32   `protobuf:"varint,6,opt,name=min_first_live_ts,json=minFirstLiveTs,proto3" json:"min_first_live_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorBaseInfoListReq) Reset()         { *m = GetAnchorBaseInfoListReq{} }
func (m *GetAnchorBaseInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorBaseInfoListReq) ProtoMessage()    {}
func (*GetAnchorBaseInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{5}
}
func (m *GetAnchorBaseInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorBaseInfoListReq.Unmarshal(m, b)
}
func (m *GetAnchorBaseInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorBaseInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorBaseInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorBaseInfoListReq.Merge(dst, src)
}
func (m *GetAnchorBaseInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorBaseInfoListReq.Size(m)
}
func (m *GetAnchorBaseInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorBaseInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorBaseInfoListReq proto.InternalMessageInfo

func (m *GetAnchorBaseInfoListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAnchorBaseInfoListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAnchorBaseInfoListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAnchorBaseInfoListReq) GetAnchorFlag() uint32 {
	if m != nil {
		return m.AnchorFlag
	}
	return 0
}

func (m *GetAnchorBaseInfoListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetAnchorBaseInfoListReq) GetMinFirstLiveTs() uint32 {
	if m != nil {
		return m.MinFirstLiveTs
	}
	return 0
}

type GetAnchorBaseInfoListResp struct {
	InfoList             []*AnchorBaseInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32            `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAnchorBaseInfoListResp) Reset()         { *m = GetAnchorBaseInfoListResp{} }
func (m *GetAnchorBaseInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorBaseInfoListResp) ProtoMessage()    {}
func (*GetAnchorBaseInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{6}
}
func (m *GetAnchorBaseInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorBaseInfoListResp.Unmarshal(m, b)
}
func (m *GetAnchorBaseInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorBaseInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorBaseInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorBaseInfoListResp.Merge(dst, src)
}
func (m *GetAnchorBaseInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorBaseInfoListResp.Size(m)
}
func (m *GetAnchorBaseInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorBaseInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorBaseInfoListResp proto.InternalMessageInfo

func (m *GetAnchorBaseInfoListResp) GetInfoList() []*AnchorBaseInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetAnchorBaseInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type GetGuildAnchorBaseInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	AgentUid             uint32   `protobuf:"varint,3,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAnchorBaseInfoReq) Reset()         { *m = GetGuildAnchorBaseInfoReq{} }
func (m *GetGuildAnchorBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorBaseInfoReq) ProtoMessage()    {}
func (*GetGuildAnchorBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{7}
}
func (m *GetGuildAnchorBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorBaseInfoReq.Unmarshal(m, b)
}
func (m *GetGuildAnchorBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorBaseInfoReq.Merge(dst, src)
}
func (m *GetGuildAnchorBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorBaseInfoReq.Size(m)
}
func (m *GetGuildAnchorBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorBaseInfoReq proto.InternalMessageInfo

func (m *GetGuildAnchorBaseInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildAnchorBaseInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetGuildAnchorBaseInfoReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

type GetGuildAnchorBaseInfoResp struct {
	Info                 *AnchorBaseInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetGuildAnchorBaseInfoResp) Reset()         { *m = GetGuildAnchorBaseInfoResp{} }
func (m *GetGuildAnchorBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorBaseInfoResp) ProtoMessage()    {}
func (*GetGuildAnchorBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{8}
}
func (m *GetGuildAnchorBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorBaseInfoResp.Unmarshal(m, b)
}
func (m *GetGuildAnchorBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorBaseInfoResp.Merge(dst, src)
}
func (m *GetGuildAnchorBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorBaseInfoResp.Size(m)
}
func (m *GetGuildAnchorBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorBaseInfoResp proto.InternalMessageInfo

func (m *GetGuildAnchorBaseInfoResp) GetInfo() *AnchorBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 公会汇总数据
type GuildTotalStats struct {
	LiveAnchorCnt        uint32   `protobuf:"varint,1,opt,name=live_anchor_cnt,json=liveAnchorCnt,proto3" json:"live_anchor_cnt,omitempty"`
	TotalAnchorIncome    uint32   `protobuf:"varint,2,opt,name=total_anchor_income,json=totalAnchorIncome,proto3" json:"total_anchor_income,omitempty"`
	TotalChannelFee      uint32   `protobuf:"varint,3,opt,name=total_channel_fee,json=totalChannelFee,proto3" json:"total_channel_fee,omitempty"`
	ValidAnchorCnt       uint32   `protobuf:"varint,4,opt,name=valid_anchor_cnt,json=validAnchorCnt,proto3" json:"valid_anchor_cnt,omitempty"`
	NewAddAnchorCnt      uint32   `protobuf:"varint,5,opt,name=new_add_anchor_cnt,json=newAddAnchorCnt,proto3" json:"new_add_anchor_cnt,omitempty"`
	AnchorKnightIncome   uint32   `protobuf:"varint,6,opt,name=anchor_knight_income,json=anchorKnightIncome,proto3" json:"anchor_knight_income,omitempty"`
	ChannelPkgFee        uint32   `protobuf:"varint,7,opt,name=channel_pkg_fee,json=channelPkgFee,proto3" json:"channel_pkg_fee,omitempty"`
	VirtualFee           uint32   `protobuf:"varint,8,opt,name=virtual_fee,json=virtualFee,proto3" json:"virtual_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTotalStats) Reset()         { *m = GuildTotalStats{} }
func (m *GuildTotalStats) String() string { return proto.CompactTextString(m) }
func (*GuildTotalStats) ProtoMessage()    {}
func (*GuildTotalStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{9}
}
func (m *GuildTotalStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTotalStats.Unmarshal(m, b)
}
func (m *GuildTotalStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTotalStats.Marshal(b, m, deterministic)
}
func (dst *GuildTotalStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTotalStats.Merge(dst, src)
}
func (m *GuildTotalStats) XXX_Size() int {
	return xxx_messageInfo_GuildTotalStats.Size(m)
}
func (m *GuildTotalStats) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTotalStats.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTotalStats proto.InternalMessageInfo

func (m *GuildTotalStats) GetLiveAnchorCnt() uint32 {
	if m != nil {
		return m.LiveAnchorCnt
	}
	return 0
}

func (m *GuildTotalStats) GetTotalAnchorIncome() uint32 {
	if m != nil {
		return m.TotalAnchorIncome
	}
	return 0
}

func (m *GuildTotalStats) GetTotalChannelFee() uint32 {
	if m != nil {
		return m.TotalChannelFee
	}
	return 0
}

func (m *GuildTotalStats) GetValidAnchorCnt() uint32 {
	if m != nil {
		return m.ValidAnchorCnt
	}
	return 0
}

func (m *GuildTotalStats) GetNewAddAnchorCnt() uint32 {
	if m != nil {
		return m.NewAddAnchorCnt
	}
	return 0
}

func (m *GuildTotalStats) GetAnchorKnightIncome() uint32 {
	if m != nil {
		return m.AnchorKnightIncome
	}
	return 0
}

func (m *GuildTotalStats) GetChannelPkgFee() uint32 {
	if m != nil {
		return m.ChannelPkgFee
	}
	return 0
}

func (m *GuildTotalStats) GetVirtualFee() uint32 {
	if m != nil {
		return m.VirtualFee
	}
	return 0
}

type GetGuildDailyTotalStatsReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginDate            uint32   `protobuf:"varint,2,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	EndDate              uint32   `protobuf:"varint,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDailyTotalStatsReq) Reset()         { *m = GetGuildDailyTotalStatsReq{} }
func (m *GetGuildDailyTotalStatsReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDailyTotalStatsReq) ProtoMessage()    {}
func (*GetGuildDailyTotalStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{10}
}
func (m *GetGuildDailyTotalStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDailyTotalStatsReq.Unmarshal(m, b)
}
func (m *GetGuildDailyTotalStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDailyTotalStatsReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDailyTotalStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDailyTotalStatsReq.Merge(dst, src)
}
func (m *GetGuildDailyTotalStatsReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDailyTotalStatsReq.Size(m)
}
func (m *GetGuildDailyTotalStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDailyTotalStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDailyTotalStatsReq proto.InternalMessageInfo

func (m *GetGuildDailyTotalStatsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDailyTotalStatsReq) GetBeginDate() uint32 {
	if m != nil {
		return m.BeginDate
	}
	return 0
}

func (m *GetGuildDailyTotalStatsReq) GetEndDate() uint32 {
	if m != nil {
		return m.EndDate
	}
	return 0
}

type GetGuildDailyTotalStatsResp struct {
	Stats                *GuildTotalStats `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGuildDailyTotalStatsResp) Reset()         { *m = GetGuildDailyTotalStatsResp{} }
func (m *GetGuildDailyTotalStatsResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDailyTotalStatsResp) ProtoMessage()    {}
func (*GetGuildDailyTotalStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{11}
}
func (m *GetGuildDailyTotalStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDailyTotalStatsResp.Unmarshal(m, b)
}
func (m *GetGuildDailyTotalStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDailyTotalStatsResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildDailyTotalStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDailyTotalStatsResp.Merge(dst, src)
}
func (m *GetGuildDailyTotalStatsResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildDailyTotalStatsResp.Size(m)
}
func (m *GetGuildDailyTotalStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDailyTotalStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDailyTotalStatsResp proto.InternalMessageInfo

func (m *GetGuildDailyTotalStatsResp) GetStats() *GuildTotalStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

type GuildDailyStats struct {
	Stats                *GuildTotalStats `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	DateTime             uint32           `protobuf:"varint,2,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GuildDailyStats) Reset()         { *m = GuildDailyStats{} }
func (m *GuildDailyStats) String() string { return proto.CompactTextString(m) }
func (*GuildDailyStats) ProtoMessage()    {}
func (*GuildDailyStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{12}
}
func (m *GuildDailyStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDailyStats.Unmarshal(m, b)
}
func (m *GuildDailyStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDailyStats.Marshal(b, m, deterministic)
}
func (dst *GuildDailyStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDailyStats.Merge(dst, src)
}
func (m *GuildDailyStats) XXX_Size() int {
	return xxx_messageInfo_GuildDailyStats.Size(m)
}
func (m *GuildDailyStats) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDailyStats.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDailyStats proto.InternalMessageInfo

func (m *GuildDailyStats) GetStats() *GuildTotalStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

func (m *GuildDailyStats) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

type GetGuildDailyStatsListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginDate            uint32   `protobuf:"varint,2,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	EndDate              uint32   `protobuf:"varint,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Begin                uint32   `protobuf:"varint,4,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDailyStatsListReq) Reset()         { *m = GetGuildDailyStatsListReq{} }
func (m *GetGuildDailyStatsListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDailyStatsListReq) ProtoMessage()    {}
func (*GetGuildDailyStatsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{13}
}
func (m *GetGuildDailyStatsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDailyStatsListReq.Unmarshal(m, b)
}
func (m *GetGuildDailyStatsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDailyStatsListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDailyStatsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDailyStatsListReq.Merge(dst, src)
}
func (m *GetGuildDailyStatsListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDailyStatsListReq.Size(m)
}
func (m *GetGuildDailyStatsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDailyStatsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDailyStatsListReq proto.InternalMessageInfo

func (m *GetGuildDailyStatsListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDailyStatsListReq) GetBeginDate() uint32 {
	if m != nil {
		return m.BeginDate
	}
	return 0
}

func (m *GetGuildDailyStatsListReq) GetEndDate() uint32 {
	if m != nil {
		return m.EndDate
	}
	return 0
}

func (m *GetGuildDailyStatsListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetGuildDailyStatsListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetGuildDailyStatsListResp struct {
	StatsList            []*GuildDailyStats `protobuf:"bytes,1,rep,name=stats_list,json=statsList,proto3" json:"stats_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildDailyStatsListResp) Reset()         { *m = GetGuildDailyStatsListResp{} }
func (m *GetGuildDailyStatsListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDailyStatsListResp) ProtoMessage()    {}
func (*GetGuildDailyStatsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{14}
}
func (m *GetGuildDailyStatsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDailyStatsListResp.Unmarshal(m, b)
}
func (m *GetGuildDailyStatsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDailyStatsListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildDailyStatsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDailyStatsListResp.Merge(dst, src)
}
func (m *GetGuildDailyStatsListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildDailyStatsListResp.Size(m)
}
func (m *GetGuildDailyStatsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDailyStatsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDailyStatsListResp proto.InternalMessageInfo

func (m *GetGuildDailyStatsListResp) GetStatsList() []*GuildDailyStats {
	if m != nil {
		return m.StatsList
	}
	return nil
}

// 按日期和指定条件获取记录中的主播列表
type GetGuildDailyAnchorListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Date                 uint32   `protobuf:"varint,2,opt,name=date,proto3" json:"date,omitempty"`
	Condition            uint32   `protobuf:"varint,3,opt,name=condition,proto3" json:"condition,omitempty"`
	Begin                uint32   `protobuf:"varint,4,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDailyAnchorListReq) Reset()         { *m = GetGuildDailyAnchorListReq{} }
func (m *GetGuildDailyAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDailyAnchorListReq) ProtoMessage()    {}
func (*GetGuildDailyAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{15}
}
func (m *GetGuildDailyAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDailyAnchorListReq.Unmarshal(m, b)
}
func (m *GetGuildDailyAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDailyAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildDailyAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDailyAnchorListReq.Merge(dst, src)
}
func (m *GetGuildDailyAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildDailyAnchorListReq.Size(m)
}
func (m *GetGuildDailyAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDailyAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDailyAnchorListReq proto.InternalMessageInfo

func (m *GetGuildDailyAnchorListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDailyAnchorListReq) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *GetGuildDailyAnchorListReq) GetCondition() uint32 {
	if m != nil {
		return m.Condition
	}
	return 0
}

func (m *GetGuildDailyAnchorListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetGuildDailyAnchorListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAnchorTotalStatsBetweenDateReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginDate            uint32   `protobuf:"varint,3,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	EndDate              uint32   `protobuf:"varint,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorTotalStatsBetweenDateReq) Reset()         { *m = GetAnchorTotalStatsBetweenDateReq{} }
func (m *GetAnchorTotalStatsBetweenDateReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorTotalStatsBetweenDateReq) ProtoMessage()    {}
func (*GetAnchorTotalStatsBetweenDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{16}
}
func (m *GetAnchorTotalStatsBetweenDateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorTotalStatsBetweenDateReq.Unmarshal(m, b)
}
func (m *GetAnchorTotalStatsBetweenDateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorTotalStatsBetweenDateReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorTotalStatsBetweenDateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorTotalStatsBetweenDateReq.Merge(dst, src)
}
func (m *GetAnchorTotalStatsBetweenDateReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorTotalStatsBetweenDateReq.Size(m)
}
func (m *GetAnchorTotalStatsBetweenDateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorTotalStatsBetweenDateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorTotalStatsBetweenDateReq proto.InternalMessageInfo

func (m *GetAnchorTotalStatsBetweenDateReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetAnchorTotalStatsBetweenDateReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAnchorTotalStatsBetweenDateReq) GetBeginDate() uint32 {
	if m != nil {
		return m.BeginDate
	}
	return 0
}

func (m *GetAnchorTotalStatsBetweenDateReq) GetEndDate() uint32 {
	if m != nil {
		return m.EndDate
	}
	return 0
}

type GetAnchorTotalStatsBetweenDateResp struct {
	TotalAnchorIncome    uint32   `protobuf:"varint,1,opt,name=total_anchor_income,json=totalAnchorIncome,proto3" json:"total_anchor_income,omitempty"`
	TotalChannelFee      uint32   `protobuf:"varint,2,opt,name=total_channel_fee,json=totalChannelFee,proto3" json:"total_channel_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorTotalStatsBetweenDateResp) Reset()         { *m = GetAnchorTotalStatsBetweenDateResp{} }
func (m *GetAnchorTotalStatsBetweenDateResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorTotalStatsBetweenDateResp) ProtoMessage()    {}
func (*GetAnchorTotalStatsBetweenDateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{17}
}
func (m *GetAnchorTotalStatsBetweenDateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorTotalStatsBetweenDateResp.Unmarshal(m, b)
}
func (m *GetAnchorTotalStatsBetweenDateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorTotalStatsBetweenDateResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorTotalStatsBetweenDateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorTotalStatsBetweenDateResp.Merge(dst, src)
}
func (m *GetAnchorTotalStatsBetweenDateResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorTotalStatsBetweenDateResp.Size(m)
}
func (m *GetAnchorTotalStatsBetweenDateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorTotalStatsBetweenDateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorTotalStatsBetweenDateResp proto.InternalMessageInfo

func (m *GetAnchorTotalStatsBetweenDateResp) GetTotalAnchorIncome() uint32 {
	if m != nil {
		return m.TotalAnchorIncome
	}
	return 0
}

func (m *GetAnchorTotalStatsBetweenDateResp) GetTotalChannelFee() uint32 {
	if m != nil {
		return m.TotalChannelFee
	}
	return 0
}

type GetGuildDailyAnchorListResp struct {
	AnchorList           []uint32 `protobuf:"varint,1,rep,packed,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildDailyAnchorListResp) Reset()         { *m = GetGuildDailyAnchorListResp{} }
func (m *GetGuildDailyAnchorListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDailyAnchorListResp) ProtoMessage()    {}
func (*GetGuildDailyAnchorListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{18}
}
func (m *GetGuildDailyAnchorListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildDailyAnchorListResp.Unmarshal(m, b)
}
func (m *GetGuildDailyAnchorListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildDailyAnchorListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildDailyAnchorListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildDailyAnchorListResp.Merge(dst, src)
}
func (m *GetGuildDailyAnchorListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildDailyAnchorListResp.Size(m)
}
func (m *GetGuildDailyAnchorListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildDailyAnchorListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildDailyAnchorListResp proto.InternalMessageInfo

func (m *GetGuildDailyAnchorListResp) GetAnchorList() []uint32 {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type AnchorDailyStats struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Date                 uint32   `protobuf:"varint,2,opt,name=date,proto3" json:"date,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,3,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	ChannelFee           uint32   `protobuf:"varint,4,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	LiveValidMinutes     uint32   `protobuf:"varint,5,opt,name=live_valid_minutes,json=liveValidMinutes,proto3" json:"live_valid_minutes,omitempty"`
	IsValidDay           bool     `protobuf:"varint,6,opt,name=is_valid_day,json=isValidDay,proto3" json:"is_valid_day,omitempty"`
	DayActiveFans        uint32   `protobuf:"varint,7,opt,name=day_active_fans,json=dayActiveFans,proto3" json:"day_active_fans,omitempty"`
	DaySpFans            uint32   `protobuf:"varint,8,opt,name=day_sp_fans,json=daySpFans,proto3" json:"day_sp_fans,omitempty"`
	FansSendFee          uint32   `protobuf:"varint,9,opt,name=fans_send_fee,json=fansSendFee,proto3" json:"fans_send_fee,omitempty"`
	DayFollowCnt         uint32   `protobuf:"varint,10,opt,name=day_follow_cnt,json=dayFollowCnt,proto3" json:"day_follow_cnt,omitempty"`
	NewAddFans           uint32   `protobuf:"varint,11,opt,name=new_add_fans,json=newAddFans,proto3" json:"new_add_fans,omitempty"`
	LiveMinutes          uint32   `protobuf:"varint,12,opt,name=live_minutes,json=liveMinutes,proto3" json:"live_minutes,omitempty"`
	AgentUid             uint32   `protobuf:"varint,13,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	KnightIncome         uint32   `protobuf:"varint,14,opt,name=knight_income,json=knightIncome,proto3" json:"knight_income,omitempty"`
	IsLiveActiveDay      bool     `protobuf:"varint,15,opt,name=is_live_active_day,json=isLiveActiveDay,proto3" json:"is_live_active_day,omitempty"`
	ConsumerCnt          uint32   `protobuf:"varint,16,opt,name=consumer_cnt,json=consumerCnt,proto3" json:"consumer_cnt,omitempty"`
	AudienceCnt          uint32   `protobuf:"varint,17,opt,name=audience_cnt,json=audienceCnt,proto3" json:"audience_cnt,omitempty"`
	GameFee              uint32   `protobuf:"varint,18,opt,name=game_fee,json=gameFee,proto3" json:"game_fee,omitempty"`
	GameMin              uint32   `protobuf:"varint,19,opt,name=game_min,json=gameMin,proto3" json:"game_min,omitempty"`
	IsGameActiveDay      bool     `protobuf:"varint,20,opt,name=is_game_active_day,json=isGameActiveDay,proto3" json:"is_game_active_day,omitempty"`
	GameChannelFee       uint32   `protobuf:"varint,21,opt,name=game_channel_fee,json=gameChannelFee,proto3" json:"game_channel_fee,omitempty"`
	VirtualFee           uint32   `protobuf:"varint,22,opt,name=virtual_fee,json=virtualFee,proto3" json:"virtual_fee,omitempty"`
	VirtualIncome        uint32   `protobuf:"varint,23,opt,name=virtual_income,json=virtualIncome,proto3" json:"virtual_income,omitempty"`
	VirtualMin           uint32   `protobuf:"varint,24,opt,name=virtual_min,json=virtualMin,proto3" json:"virtual_min,omitempty"`
	IsVirtualActiveDay   bool     `protobuf:"varint,25,opt,name=is_virtual_active_day,json=isVirtualActiveDay,proto3" json:"is_virtual_active_day,omitempty"`
	WeekNewAddFans       uint32   `protobuf:"varint,26,opt,name=week_new_add_fans,json=weekNewAddFans,proto3" json:"week_new_add_fans,omitempty"`
	WeekConsumerCnt      uint32   `protobuf:"varint,27,opt,name=week_consumer_cnt,json=weekConsumerCnt,proto3" json:"week_consumer_cnt,omitempty"`
	WeekAudienceCnt      uint32   `protobuf:"varint,28,opt,name=week_audience_cnt,json=weekAudienceCnt,proto3" json:"week_audience_cnt,omitempty"`
	WeekFollowCnt        uint32   `protobuf:"varint,29,opt,name=week_follow_cnt,json=weekFollowCnt,proto3" json:"week_follow_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorDailyStats) Reset()         { *m = AnchorDailyStats{} }
func (m *AnchorDailyStats) String() string { return proto.CompactTextString(m) }
func (*AnchorDailyStats) ProtoMessage()    {}
func (*AnchorDailyStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{19}
}
func (m *AnchorDailyStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorDailyStats.Unmarshal(m, b)
}
func (m *AnchorDailyStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorDailyStats.Marshal(b, m, deterministic)
}
func (dst *AnchorDailyStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorDailyStats.Merge(dst, src)
}
func (m *AnchorDailyStats) XXX_Size() int {
	return xxx_messageInfo_AnchorDailyStats.Size(m)
}
func (m *AnchorDailyStats) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorDailyStats.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorDailyStats proto.InternalMessageInfo

func (m *AnchorDailyStats) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorDailyStats) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

func (m *AnchorDailyStats) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *AnchorDailyStats) GetChannelFee() uint32 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *AnchorDailyStats) GetLiveValidMinutes() uint32 {
	if m != nil {
		return m.LiveValidMinutes
	}
	return 0
}

func (m *AnchorDailyStats) GetIsValidDay() bool {
	if m != nil {
		return m.IsValidDay
	}
	return false
}

func (m *AnchorDailyStats) GetDayActiveFans() uint32 {
	if m != nil {
		return m.DayActiveFans
	}
	return 0
}

func (m *AnchorDailyStats) GetDaySpFans() uint32 {
	if m != nil {
		return m.DaySpFans
	}
	return 0
}

func (m *AnchorDailyStats) GetFansSendFee() uint32 {
	if m != nil {
		return m.FansSendFee
	}
	return 0
}

func (m *AnchorDailyStats) GetDayFollowCnt() uint32 {
	if m != nil {
		return m.DayFollowCnt
	}
	return 0
}

func (m *AnchorDailyStats) GetNewAddFans() uint32 {
	if m != nil {
		return m.NewAddFans
	}
	return 0
}

func (m *AnchorDailyStats) GetLiveMinutes() uint32 {
	if m != nil {
		return m.LiveMinutes
	}
	return 0
}

func (m *AnchorDailyStats) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *AnchorDailyStats) GetKnightIncome() uint32 {
	if m != nil {
		return m.KnightIncome
	}
	return 0
}

func (m *AnchorDailyStats) GetIsLiveActiveDay() bool {
	if m != nil {
		return m.IsLiveActiveDay
	}
	return false
}

func (m *AnchorDailyStats) GetConsumerCnt() uint32 {
	if m != nil {
		return m.ConsumerCnt
	}
	return 0
}

func (m *AnchorDailyStats) GetAudienceCnt() uint32 {
	if m != nil {
		return m.AudienceCnt
	}
	return 0
}

func (m *AnchorDailyStats) GetGameFee() uint32 {
	if m != nil {
		return m.GameFee
	}
	return 0
}

func (m *AnchorDailyStats) GetGameMin() uint32 {
	if m != nil {
		return m.GameMin
	}
	return 0
}

func (m *AnchorDailyStats) GetIsGameActiveDay() bool {
	if m != nil {
		return m.IsGameActiveDay
	}
	return false
}

func (m *AnchorDailyStats) GetGameChannelFee() uint32 {
	if m != nil {
		return m.GameChannelFee
	}
	return 0
}

func (m *AnchorDailyStats) GetVirtualFee() uint32 {
	if m != nil {
		return m.VirtualFee
	}
	return 0
}

func (m *AnchorDailyStats) GetVirtualIncome() uint32 {
	if m != nil {
		return m.VirtualIncome
	}
	return 0
}

func (m *AnchorDailyStats) GetVirtualMin() uint32 {
	if m != nil {
		return m.VirtualMin
	}
	return 0
}

func (m *AnchorDailyStats) GetIsVirtualActiveDay() bool {
	if m != nil {
		return m.IsVirtualActiveDay
	}
	return false
}

func (m *AnchorDailyStats) GetWeekNewAddFans() uint32 {
	if m != nil {
		return m.WeekNewAddFans
	}
	return 0
}

func (m *AnchorDailyStats) GetWeekConsumerCnt() uint32 {
	if m != nil {
		return m.WeekConsumerCnt
	}
	return 0
}

func (m *AnchorDailyStats) GetWeekAudienceCnt() uint32 {
	if m != nil {
		return m.WeekAudienceCnt
	}
	return 0
}

func (m *AnchorDailyStats) GetWeekFollowCnt() uint32 {
	if m != nil {
		return m.WeekFollowCnt
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorDailyRecordWithDateListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorDailyRecordWithDateListReq) Reset()         { *m = GetAnchorDailyRecordWithDateListReq{} }
func (m *GetAnchorDailyRecordWithDateListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorDailyRecordWithDateListReq) ProtoMessage()    {}
func (*GetAnchorDailyRecordWithDateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{20}
}
func (m *GetAnchorDailyRecordWithDateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorDailyRecordWithDateListReq.Unmarshal(m, b)
}
func (m *GetAnchorDailyRecordWithDateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorDailyRecordWithDateListReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorDailyRecordWithDateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorDailyRecordWithDateListReq.Merge(dst, src)
}
func (m *GetAnchorDailyRecordWithDateListReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorDailyRecordWithDateListReq.Size(m)
}
func (m *GetAnchorDailyRecordWithDateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorDailyRecordWithDateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorDailyRecordWithDateListReq proto.InternalMessageInfo

func (m *GetAnchorDailyRecordWithDateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAnchorDailyRecordWithDateListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAnchorDailyRecordWithDateListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAnchorDailyRecordWithDateListResp struct {
	List                 []*AnchorDailyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAnchorDailyRecordWithDateListResp) Reset()         { *m = GetAnchorDailyRecordWithDateListResp{} }
func (m *GetAnchorDailyRecordWithDateListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorDailyRecordWithDateListResp) ProtoMessage()    {}
func (*GetAnchorDailyRecordWithDateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{21}
}
func (m *GetAnchorDailyRecordWithDateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorDailyRecordWithDateListResp.Unmarshal(m, b)
}
func (m *GetAnchorDailyRecordWithDateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorDailyRecordWithDateListResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorDailyRecordWithDateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorDailyRecordWithDateListResp.Merge(dst, src)
}
func (m *GetAnchorDailyRecordWithDateListResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorDailyRecordWithDateListResp.Size(m)
}
func (m *GetAnchorDailyRecordWithDateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorDailyRecordWithDateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorDailyRecordWithDateListResp proto.InternalMessageInfo

func (m *GetAnchorDailyRecordWithDateListResp) GetList() []*AnchorDailyStats {
	if m != nil {
		return m.List
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type BatchGetAnchorDailyRecordReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	AgentUid             uint32   `protobuf:"varint,5,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorDailyRecordReq) Reset()         { *m = BatchGetAnchorDailyRecordReq{} }
func (m *BatchGetAnchorDailyRecordReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorDailyRecordReq) ProtoMessage()    {}
func (*BatchGetAnchorDailyRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{22}
}
func (m *BatchGetAnchorDailyRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorDailyRecordReq.Unmarshal(m, b)
}
func (m *BatchGetAnchorDailyRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorDailyRecordReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorDailyRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorDailyRecordReq.Merge(dst, src)
}
func (m *BatchGetAnchorDailyRecordReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorDailyRecordReq.Size(m)
}
func (m *BatchGetAnchorDailyRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorDailyRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorDailyRecordReq proto.InternalMessageInfo

func (m *BatchGetAnchorDailyRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAnchorDailyRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetAnchorDailyRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BatchGetAnchorDailyRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatchGetAnchorDailyRecordReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

type BatchGetAnchorDailyRecordResp struct {
	List                 []*AnchorDailyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetAnchorDailyRecordResp) Reset()         { *m = BatchGetAnchorDailyRecordResp{} }
func (m *BatchGetAnchorDailyRecordResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorDailyRecordResp) ProtoMessage()    {}
func (*BatchGetAnchorDailyRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{23}
}
func (m *BatchGetAnchorDailyRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorDailyRecordResp.Unmarshal(m, b)
}
func (m *BatchGetAnchorDailyRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorDailyRecordResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorDailyRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorDailyRecordResp.Merge(dst, src)
}
func (m *BatchGetAnchorDailyRecordResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorDailyRecordResp.Size(m)
}
func (m *BatchGetAnchorDailyRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorDailyRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorDailyRecordResp proto.InternalMessageInfo

func (m *BatchGetAnchorDailyRecordResp) GetList() []*AnchorDailyStats {
	if m != nil {
		return m.List
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorDailyStatsListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	AgentUid             uint32   `protobuf:"varint,6,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	UidList              []uint32 `protobuf:"varint,7,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorDailyStatsListReq) Reset()         { *m = GetAnchorDailyStatsListReq{} }
func (m *GetAnchorDailyStatsListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorDailyStatsListReq) ProtoMessage()    {}
func (*GetAnchorDailyStatsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{24}
}
func (m *GetAnchorDailyStatsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorDailyStatsListReq.Unmarshal(m, b)
}
func (m *GetAnchorDailyStatsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorDailyStatsListReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorDailyStatsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorDailyStatsListReq.Merge(dst, src)
}
func (m *GetAnchorDailyStatsListReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorDailyStatsListReq.Size(m)
}
func (m *GetAnchorDailyStatsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorDailyStatsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorDailyStatsListReq proto.InternalMessageInfo

func (m *GetAnchorDailyStatsListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAnchorDailyStatsListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAnchorDailyStatsListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetAnchorDailyStatsListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAnchorDailyStatsListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAnchorDailyStatsListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetAnchorDailyStatsListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorDailyStatsListResp struct {
	List                 []*AnchorDailyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32              `protobuf:"varint,2,opt,name=totalCnt,proto3" json:"totalCnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAnchorDailyStatsListResp) Reset()         { *m = GetAnchorDailyStatsListResp{} }
func (m *GetAnchorDailyStatsListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorDailyStatsListResp) ProtoMessage()    {}
func (*GetAnchorDailyStatsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{25}
}
func (m *GetAnchorDailyStatsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorDailyStatsListResp.Unmarshal(m, b)
}
func (m *GetAnchorDailyStatsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorDailyStatsListResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorDailyStatsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorDailyStatsListResp.Merge(dst, src)
}
func (m *GetAnchorDailyStatsListResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorDailyStatsListResp.Size(m)
}
func (m *GetAnchorDailyStatsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorDailyStatsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorDailyStatsListResp proto.InternalMessageInfo

func (m *GetAnchorDailyStatsListResp) GetList() []*AnchorDailyStats {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetAnchorDailyStatsListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type AnchorWeeklyStats struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,3,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	ChannelFee           uint32   `protobuf:"varint,4,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	LiveValidMinutes     uint32   `protobuf:"varint,5,opt,name=live_valid_minutes,json=liveValidMinutes,proto3" json:"live_valid_minutes,omitempty"`
	ValidDaysCnt         uint32   `protobuf:"varint,6,opt,name=valid_days_cnt,json=validDaysCnt,proto3" json:"valid_days_cnt,omitempty"`
	WeekActiveFans       uint32   `protobuf:"varint,7,opt,name=week_active_fans,json=weekActiveFans,proto3" json:"week_active_fans,omitempty"`
	WeekSpFans           uint32   `protobuf:"varint,8,opt,name=week_sp_fans,json=weekSpFans,proto3" json:"week_sp_fans,omitempty"`
	FansSendFee          uint32   `protobuf:"varint,9,opt,name=fans_send_fee,json=fansSendFee,proto3" json:"fans_send_fee,omitempty"`
	WeekFollowCnt        uint32   `protobuf:"varint,10,opt,name=week_follow_cnt,json=weekFollowCnt,proto3" json:"week_follow_cnt,omitempty"`
	NewAddFans           uint32   `protobuf:"varint,11,opt,name=new_add_fans,json=newAddFans,proto3" json:"new_add_fans,omitempty"`
	LiveMinutes          uint32   `protobuf:"varint,12,opt,name=live_minutes,json=liveMinutes,proto3" json:"live_minutes,omitempty"`
	AgentUid             uint32   `protobuf:"varint,13,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AnchorKnightIncome   uint32   `protobuf:"varint,14,opt,name=anchor_knight_income,json=anchorKnightIncome,proto3" json:"anchor_knight_income,omitempty"`
	LiveActiveDays       uint32   `protobuf:"varint,15,opt,name=live_active_days,json=liveActiveDays,proto3" json:"live_active_days,omitempty"`
	ConsumerCnt          uint32   `protobuf:"varint,16,opt,name=consumer_cnt,json=consumerCnt,proto3" json:"consumer_cnt,omitempty"`
	AudienceCnt          uint32   `protobuf:"varint,17,opt,name=audience_cnt,json=audienceCnt,proto3" json:"audience_cnt,omitempty"`
	GameFee              uint32   `protobuf:"varint,18,opt,name=game_fee,json=gameFee,proto3" json:"game_fee,omitempty"`
	GameMin              uint32   `protobuf:"varint,19,opt,name=game_min,json=gameMin,proto3" json:"game_min,omitempty"`
	GameActiveDays       uint32   `protobuf:"varint,20,opt,name=game_active_days,json=gameActiveDays,proto3" json:"game_active_days,omitempty"`
	GameChannelFee       uint32   `protobuf:"varint,21,opt,name=game_channel_fee,json=gameChannelFee,proto3" json:"game_channel_fee,omitempty"`
	VirtualFee           uint32   `protobuf:"varint,22,opt,name=virtual_fee,json=virtualFee,proto3" json:"virtual_fee,omitempty"`
	VirtualIncome        uint32   `protobuf:"varint,23,opt,name=virtual_income,json=virtualIncome,proto3" json:"virtual_income,omitempty"`
	VirtualMin           uint32   `protobuf:"varint,24,opt,name=virtual_min,json=virtualMin,proto3" json:"virtual_min,omitempty"`
	VirtualActiveDays    uint32   `protobuf:"varint,25,opt,name=virtual_active_days,json=virtualActiveDays,proto3" json:"virtual_active_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorWeeklyStats) Reset()         { *m = AnchorWeeklyStats{} }
func (m *AnchorWeeklyStats) String() string { return proto.CompactTextString(m) }
func (*AnchorWeeklyStats) ProtoMessage()    {}
func (*AnchorWeeklyStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{26}
}
func (m *AnchorWeeklyStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorWeeklyStats.Unmarshal(m, b)
}
func (m *AnchorWeeklyStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorWeeklyStats.Marshal(b, m, deterministic)
}
func (dst *AnchorWeeklyStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorWeeklyStats.Merge(dst, src)
}
func (m *AnchorWeeklyStats) XXX_Size() int {
	return xxx_messageInfo_AnchorWeeklyStats.Size(m)
}
func (m *AnchorWeeklyStats) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorWeeklyStats.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorWeeklyStats proto.InternalMessageInfo

func (m *AnchorWeeklyStats) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorWeeklyStats) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *AnchorWeeklyStats) GetChannelFee() uint32 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *AnchorWeeklyStats) GetLiveValidMinutes() uint32 {
	if m != nil {
		return m.LiveValidMinutes
	}
	return 0
}

func (m *AnchorWeeklyStats) GetValidDaysCnt() uint32 {
	if m != nil {
		return m.ValidDaysCnt
	}
	return 0
}

func (m *AnchorWeeklyStats) GetWeekActiveFans() uint32 {
	if m != nil {
		return m.WeekActiveFans
	}
	return 0
}

func (m *AnchorWeeklyStats) GetWeekSpFans() uint32 {
	if m != nil {
		return m.WeekSpFans
	}
	return 0
}

func (m *AnchorWeeklyStats) GetFansSendFee() uint32 {
	if m != nil {
		return m.FansSendFee
	}
	return 0
}

func (m *AnchorWeeklyStats) GetWeekFollowCnt() uint32 {
	if m != nil {
		return m.WeekFollowCnt
	}
	return 0
}

func (m *AnchorWeeklyStats) GetNewAddFans() uint32 {
	if m != nil {
		return m.NewAddFans
	}
	return 0
}

func (m *AnchorWeeklyStats) GetLiveMinutes() uint32 {
	if m != nil {
		return m.LiveMinutes
	}
	return 0
}

func (m *AnchorWeeklyStats) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *AnchorWeeklyStats) GetAnchorKnightIncome() uint32 {
	if m != nil {
		return m.AnchorKnightIncome
	}
	return 0
}

func (m *AnchorWeeklyStats) GetLiveActiveDays() uint32 {
	if m != nil {
		return m.LiveActiveDays
	}
	return 0
}

func (m *AnchorWeeklyStats) GetConsumerCnt() uint32 {
	if m != nil {
		return m.ConsumerCnt
	}
	return 0
}

func (m *AnchorWeeklyStats) GetAudienceCnt() uint32 {
	if m != nil {
		return m.AudienceCnt
	}
	return 0
}

func (m *AnchorWeeklyStats) GetGameFee() uint32 {
	if m != nil {
		return m.GameFee
	}
	return 0
}

func (m *AnchorWeeklyStats) GetGameMin() uint32 {
	if m != nil {
		return m.GameMin
	}
	return 0
}

func (m *AnchorWeeklyStats) GetGameActiveDays() uint32 {
	if m != nil {
		return m.GameActiveDays
	}
	return 0
}

func (m *AnchorWeeklyStats) GetGameChannelFee() uint32 {
	if m != nil {
		return m.GameChannelFee
	}
	return 0
}

func (m *AnchorWeeklyStats) GetVirtualFee() uint32 {
	if m != nil {
		return m.VirtualFee
	}
	return 0
}

func (m *AnchorWeeklyStats) GetVirtualIncome() uint32 {
	if m != nil {
		return m.VirtualIncome
	}
	return 0
}

func (m *AnchorWeeklyStats) GetVirtualMin() uint32 {
	if m != nil {
		return m.VirtualMin
	}
	return 0
}

func (m *AnchorWeeklyStats) GetVirtualActiveDays() uint32 {
	if m != nil {
		return m.VirtualActiveDays
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type BatchGetAnchorWeeklyRecordReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BeginTime            uint32   `protobuf:"varint,3,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	AgentUid             uint32   `protobuf:"varint,5,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorWeeklyRecordReq) Reset()         { *m = BatchGetAnchorWeeklyRecordReq{} }
func (m *BatchGetAnchorWeeklyRecordReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorWeeklyRecordReq) ProtoMessage()    {}
func (*BatchGetAnchorWeeklyRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{27}
}
func (m *BatchGetAnchorWeeklyRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorWeeklyRecordReq.Unmarshal(m, b)
}
func (m *BatchGetAnchorWeeklyRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorWeeklyRecordReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorWeeklyRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorWeeklyRecordReq.Merge(dst, src)
}
func (m *BatchGetAnchorWeeklyRecordReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorWeeklyRecordReq.Size(m)
}
func (m *BatchGetAnchorWeeklyRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorWeeklyRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorWeeklyRecordReq proto.InternalMessageInfo

func (m *BatchGetAnchorWeeklyRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAnchorWeeklyRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetAnchorWeeklyRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BatchGetAnchorWeeklyRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatchGetAnchorWeeklyRecordReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

type BatchGetAnchorWeeklyRecordResp struct {
	List                 []*AnchorWeeklyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *BatchGetAnchorWeeklyRecordResp) Reset()         { *m = BatchGetAnchorWeeklyRecordResp{} }
func (m *BatchGetAnchorWeeklyRecordResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorWeeklyRecordResp) ProtoMessage()    {}
func (*BatchGetAnchorWeeklyRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{28}
}
func (m *BatchGetAnchorWeeklyRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorWeeklyRecordResp.Unmarshal(m, b)
}
func (m *BatchGetAnchorWeeklyRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorWeeklyRecordResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorWeeklyRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorWeeklyRecordResp.Merge(dst, src)
}
func (m *BatchGetAnchorWeeklyRecordResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorWeeklyRecordResp.Size(m)
}
func (m *BatchGetAnchorWeeklyRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorWeeklyRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorWeeklyRecordResp proto.InternalMessageInfo

func (m *BatchGetAnchorWeeklyRecordResp) GetList() []*AnchorWeeklyStats {
	if m != nil {
		return m.List
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorWeeklyStatsListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	AgentUid             uint32   `protobuf:"varint,6,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AnchorList           []uint32 `protobuf:"varint,7,rep,packed,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorWeeklyStatsListReq) Reset()         { *m = GetAnchorWeeklyStatsListReq{} }
func (m *GetAnchorWeeklyStatsListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorWeeklyStatsListReq) ProtoMessage()    {}
func (*GetAnchorWeeklyStatsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{29}
}
func (m *GetAnchorWeeklyStatsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorWeeklyStatsListReq.Unmarshal(m, b)
}
func (m *GetAnchorWeeklyStatsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorWeeklyStatsListReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorWeeklyStatsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorWeeklyStatsListReq.Merge(dst, src)
}
func (m *GetAnchorWeeklyStatsListReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorWeeklyStatsListReq.Size(m)
}
func (m *GetAnchorWeeklyStatsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorWeeklyStatsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorWeeklyStatsListReq proto.InternalMessageInfo

func (m *GetAnchorWeeklyStatsListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAnchorWeeklyStatsListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAnchorWeeklyStatsListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetAnchorWeeklyStatsListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAnchorWeeklyStatsListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAnchorWeeklyStatsListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetAnchorWeeklyStatsListReq) GetAnchorList() []uint32 {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorWeeklyStatsListResp struct {
	List                 []*AnchorWeeklyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=totalCnt,proto3" json:"totalCnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAnchorWeeklyStatsListResp) Reset()         { *m = GetAnchorWeeklyStatsListResp{} }
func (m *GetAnchorWeeklyStatsListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorWeeklyStatsListResp) ProtoMessage()    {}
func (*GetAnchorWeeklyStatsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{30}
}
func (m *GetAnchorWeeklyStatsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorWeeklyStatsListResp.Unmarshal(m, b)
}
func (m *GetAnchorWeeklyStatsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorWeeklyStatsListResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorWeeklyStatsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorWeeklyStatsListResp.Merge(dst, src)
}
func (m *GetAnchorWeeklyStatsListResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorWeeklyStatsListResp.Size(m)
}
func (m *GetAnchorWeeklyStatsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorWeeklyStatsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorWeeklyStatsListResp proto.InternalMessageInfo

func (m *GetAnchorWeeklyStatsListResp) GetList() []*AnchorWeeklyStats {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetAnchorWeeklyStatsListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 按周和指定条件获取记录中的主播列表
type GetGuildWeeklyAnchorListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	WeekBeginDate        uint32   `protobuf:"varint,2,opt,name=week_begin_date,json=weekBeginDate,proto3" json:"week_begin_date,omitempty"`
	WeekEndDate          uint32   `protobuf:"varint,3,opt,name=week_end_date,json=weekEndDate,proto3" json:"week_end_date,omitempty"`
	Condition            uint32   `protobuf:"varint,4,opt,name=condition,proto3" json:"condition,omitempty"`
	Begin                uint32   `protobuf:"varint,5,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	ActiveMinCnt         uint32   `protobuf:"varint,7,opt,name=active_min_cnt,json=activeMinCnt,proto3" json:"active_min_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildWeeklyAnchorListReq) Reset()         { *m = GetGuildWeeklyAnchorListReq{} }
func (m *GetGuildWeeklyAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildWeeklyAnchorListReq) ProtoMessage()    {}
func (*GetGuildWeeklyAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{31}
}
func (m *GetGuildWeeklyAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildWeeklyAnchorListReq.Unmarshal(m, b)
}
func (m *GetGuildWeeklyAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildWeeklyAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildWeeklyAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildWeeklyAnchorListReq.Merge(dst, src)
}
func (m *GetGuildWeeklyAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildWeeklyAnchorListReq.Size(m)
}
func (m *GetGuildWeeklyAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildWeeklyAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildWeeklyAnchorListReq proto.InternalMessageInfo

func (m *GetGuildWeeklyAnchorListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildWeeklyAnchorListReq) GetWeekBeginDate() uint32 {
	if m != nil {
		return m.WeekBeginDate
	}
	return 0
}

func (m *GetGuildWeeklyAnchorListReq) GetWeekEndDate() uint32 {
	if m != nil {
		return m.WeekEndDate
	}
	return 0
}

func (m *GetGuildWeeklyAnchorListReq) GetCondition() uint32 {
	if m != nil {
		return m.Condition
	}
	return 0
}

func (m *GetGuildWeeklyAnchorListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetGuildWeeklyAnchorListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGuildWeeklyAnchorListReq) GetActiveMinCnt() uint32 {
	if m != nil {
		return m.ActiveMinCnt
	}
	return 0
}

type GetGuildWeeklyAnchorListResp struct {
	AnchorList           []uint32 `protobuf:"varint,1,rep,packed,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildWeeklyAnchorListResp) Reset()         { *m = GetGuildWeeklyAnchorListResp{} }
func (m *GetGuildWeeklyAnchorListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildWeeklyAnchorListResp) ProtoMessage()    {}
func (*GetGuildWeeklyAnchorListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{32}
}
func (m *GetGuildWeeklyAnchorListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildWeeklyAnchorListResp.Unmarshal(m, b)
}
func (m *GetGuildWeeklyAnchorListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildWeeklyAnchorListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildWeeklyAnchorListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildWeeklyAnchorListResp.Merge(dst, src)
}
func (m *GetGuildWeeklyAnchorListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildWeeklyAnchorListResp.Size(m)
}
func (m *GetGuildWeeklyAnchorListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildWeeklyAnchorListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildWeeklyAnchorListResp proto.InternalMessageInfo

func (m *GetGuildWeeklyAnchorListResp) GetAnchorList() []uint32 {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

// 公会直播月汇总数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GuildMonthlyStats struct {
	LiveAnchorCnt        uint32   `protobuf:"varint,1,opt,name=live_anchor_cnt,json=liveAnchorCnt,proto3" json:"live_anchor_cnt,omitempty"`
	TotalAnchorIncome    uint64   `protobuf:"varint,2,opt,name=total_anchor_income,json=totalAnchorIncome,proto3" json:"total_anchor_income,omitempty"`
	TotalChannelFee      uint64   `protobuf:"varint,3,opt,name=total_channel_fee,json=totalChannelFee,proto3" json:"total_channel_fee,omitempty"`
	ValidAnchorCnt       uint32   `protobuf:"varint,4,opt,name=valid_anchor_cnt,json=validAnchorCnt,proto3" json:"valid_anchor_cnt,omitempty"`
	NewValidAnchorCnt    uint32   `protobuf:"varint,5,opt,name=new_valid_anchor_cnt,json=newValidAnchorCnt,proto3" json:"new_valid_anchor_cnt,omitempty"`
	PotentialAnchorCnt   uint32   `protobuf:"varint,6,opt,name=potential_anchor_cnt,json=potentialAnchorCnt,proto3" json:"potential_anchor_cnt,omitempty"`
	NewAddAnchorCnt      uint32   `protobuf:"varint,7,opt,name=new_add_anchor_cnt,json=newAddAnchorCnt,proto3" json:"new_add_anchor_cnt,omitempty"`
	AnchorKnightIncome   uint64   `protobuf:"varint,8,opt,name=anchor_knight_income,json=anchorKnightIncome,proto3" json:"anchor_knight_income,omitempty"`
	QualityAnchorCnt     uint32   `protobuf:"varint,9,opt,name=qualityAnchorCnt,proto3" json:"qualityAnchorCnt,omitempty"`
	ActiveAnchorCnt      uint32   `protobuf:"varint,10,opt,name=activeAnchorCnt,proto3" json:"activeAnchorCnt,omitempty"`
	NewActiveAnchorCnt   uint32   `protobuf:"varint,11,opt,name=newActiveAnchorCnt,proto3" json:"newActiveAnchorCnt,omitempty"`
	HighIncomeAnchorCnt  uint32   `protobuf:"varint,12,opt,name=highIncomeAnchorCnt,proto3" json:"highIncomeAnchorCnt,omitempty"`
	ChannelPkgFee        uint64   `protobuf:"varint,13,opt,name=channel_pkg_fee,json=channelPkgFee,proto3" json:"channel_pkg_fee,omitempty"`
	ProfessionPracCnt    uint32   `protobuf:"varint,14,opt,name=profession_prac_cnt,json=professionPracCnt,proto3" json:"profession_prac_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildMonthlyStats) Reset()         { *m = GuildMonthlyStats{} }
func (m *GuildMonthlyStats) String() string { return proto.CompactTextString(m) }
func (*GuildMonthlyStats) ProtoMessage()    {}
func (*GuildMonthlyStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{33}
}
func (m *GuildMonthlyStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildMonthlyStats.Unmarshal(m, b)
}
func (m *GuildMonthlyStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildMonthlyStats.Marshal(b, m, deterministic)
}
func (dst *GuildMonthlyStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildMonthlyStats.Merge(dst, src)
}
func (m *GuildMonthlyStats) XXX_Size() int {
	return xxx_messageInfo_GuildMonthlyStats.Size(m)
}
func (m *GuildMonthlyStats) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildMonthlyStats.DiscardUnknown(m)
}

var xxx_messageInfo_GuildMonthlyStats proto.InternalMessageInfo

func (m *GuildMonthlyStats) GetLiveAnchorCnt() uint32 {
	if m != nil {
		return m.LiveAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetTotalAnchorIncome() uint64 {
	if m != nil {
		return m.TotalAnchorIncome
	}
	return 0
}

func (m *GuildMonthlyStats) GetTotalChannelFee() uint64 {
	if m != nil {
		return m.TotalChannelFee
	}
	return 0
}

func (m *GuildMonthlyStats) GetValidAnchorCnt() uint32 {
	if m != nil {
		return m.ValidAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetNewValidAnchorCnt() uint32 {
	if m != nil {
		return m.NewValidAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetPotentialAnchorCnt() uint32 {
	if m != nil {
		return m.PotentialAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetNewAddAnchorCnt() uint32 {
	if m != nil {
		return m.NewAddAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetAnchorKnightIncome() uint64 {
	if m != nil {
		return m.AnchorKnightIncome
	}
	return 0
}

func (m *GuildMonthlyStats) GetQualityAnchorCnt() uint32 {
	if m != nil {
		return m.QualityAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetActiveAnchorCnt() uint32 {
	if m != nil {
		return m.ActiveAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetNewActiveAnchorCnt() uint32 {
	if m != nil {
		return m.NewActiveAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetHighIncomeAnchorCnt() uint32 {
	if m != nil {
		return m.HighIncomeAnchorCnt
	}
	return 0
}

func (m *GuildMonthlyStats) GetChannelPkgFee() uint64 {
	if m != nil {
		return m.ChannelPkgFee
	}
	return 0
}

func (m *GuildMonthlyStats) GetProfessionPracCnt() uint32 {
	if m != nil {
		return m.ProfessionPracCnt
	}
	return 0
}

type GetGuildMonthlyStatsReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	MonthTime            uint32   `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildMonthlyStatsReq) Reset()         { *m = GetGuildMonthlyStatsReq{} }
func (m *GetGuildMonthlyStatsReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthlyStatsReq) ProtoMessage()    {}
func (*GetGuildMonthlyStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{34}
}
func (m *GetGuildMonthlyStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthlyStatsReq.Unmarshal(m, b)
}
func (m *GetGuildMonthlyStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthlyStatsReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthlyStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthlyStatsReq.Merge(dst, src)
}
func (m *GetGuildMonthlyStatsReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthlyStatsReq.Size(m)
}
func (m *GetGuildMonthlyStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthlyStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthlyStatsReq proto.InternalMessageInfo

func (m *GetGuildMonthlyStatsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildMonthlyStatsReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type GetGuildMonthlyStatsResp struct {
	Stats                *GuildMonthlyStats `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildMonthlyStatsResp) Reset()         { *m = GetGuildMonthlyStatsResp{} }
func (m *GetGuildMonthlyStatsResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildMonthlyStatsResp) ProtoMessage()    {}
func (*GetGuildMonthlyStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{35}
}
func (m *GetGuildMonthlyStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildMonthlyStatsResp.Unmarshal(m, b)
}
func (m *GetGuildMonthlyStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildMonthlyStatsResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildMonthlyStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildMonthlyStatsResp.Merge(dst, src)
}
func (m *GetGuildMonthlyStatsResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildMonthlyStatsResp.Size(m)
}
func (m *GetGuildMonthlyStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildMonthlyStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildMonthlyStatsResp proto.InternalMessageInfo

func (m *GetGuildMonthlyStatsResp) GetStats() *GuildMonthlyStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

type AnchorMonthlyStats struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelFee           uint32   `protobuf:"varint,2,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,3,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	DayLiveValidCnt      uint32   `protobuf:"varint,4,opt,name=day_live_valid_cnt,json=dayLiveValidCnt,proto3" json:"day_live_valid_cnt,omitempty"`
	LiveValidMinutes     uint32   `protobuf:"varint,5,opt,name=live_valid_minutes,json=liveValidMinutes,proto3" json:"live_valid_minutes,omitempty"`
	NewFlag              uint32   `protobuf:"varint,6,opt,name=new_flag,json=newFlag,proto3" json:"new_flag,omitempty"`
	LastMonthFansCnt     uint32   `protobuf:"varint,7,opt,name=last_month_fans_cnt,json=lastMonthFansCnt,proto3" json:"last_month_fans_cnt,omitempty"`
	NewFansCnt           uint32   `protobuf:"varint,8,opt,name=new_fans_cnt,json=newFansCnt,proto3" json:"new_fans_cnt,omitempty"`
	ActiveFans           uint32   `protobuf:"varint,9,opt,name=active_fans,json=activeFans,proto3" json:"active_fans,omitempty"`
	SpFans               uint32   `protobuf:"varint,10,opt,name=sp_fans,json=spFans,proto3" json:"sp_fans,omitempty"`
	FansSendFee          uint32   `protobuf:"varint,11,opt,name=fans_send_fee,json=fansSendFee,proto3" json:"fans_send_fee,omitempty"`
	FollowCnt            uint32   `protobuf:"varint,12,opt,name=follow_cnt,json=followCnt,proto3" json:"follow_cnt,omitempty"`
	DateTs               uint32   `protobuf:"varint,13,opt,name=date_ts,json=dateTs,proto3" json:"date_ts,omitempty"`
	LiveMinutes          uint32   `protobuf:"varint,14,opt,name=live_minutes,json=liveMinutes,proto3" json:"live_minutes,omitempty"`
	AgentUid             uint32   `protobuf:"varint,15,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AnchorKnightIncome   uint32   `protobuf:"varint,16,opt,name=anchor_knight_income,json=anchorKnightIncome,proto3" json:"anchor_knight_income,omitempty"`
	LiveActiveDays       uint32   `protobuf:"varint,17,opt,name=live_active_days,json=liveActiveDays,proto3" json:"live_active_days,omitempty"`
	IsQualityAnchor      bool     `protobuf:"varint,18,opt,name=is_quality_anchor,json=isQualityAnchor,proto3" json:"is_quality_anchor,omitempty"`
	IsActiveAnchor       bool     `protobuf:"varint,19,opt,name=is_active_anchor,json=isActiveAnchor,proto3" json:"is_active_anchor,omitempty"`
	IsNewActiveAnchor    bool     `protobuf:"varint,20,opt,name=is_new_active_anchor,json=isNewActiveAnchor,proto3" json:"is_new_active_anchor,omitempty"`
	ChannelPkgFee        uint32   `protobuf:"varint,21,opt,name=channel_pkg_fee,json=channelPkgFee,proto3" json:"channel_pkg_fee,omitempty"`
	AnchorPkgIncome      uint32   `protobuf:"varint,22,opt,name=anchor_pkg_income,json=anchorPkgIncome,proto3" json:"anchor_pkg_income,omitempty"`
	IsProfessionPrac     bool     `protobuf:"varint,23,opt,name=is_profession_prac,json=isProfessionPrac,proto3" json:"is_profession_prac,omitempty"`
	GameFee              uint32   `protobuf:"varint,24,opt,name=game_fee,json=gameFee,proto3" json:"game_fee,omitempty"`
	GameMin              uint32   `protobuf:"varint,25,opt,name=game_min,json=gameMin,proto3" json:"game_min,omitempty"`
	GameActiveDays       uint32   `protobuf:"varint,26,opt,name=game_active_days,json=gameActiveDays,proto3" json:"game_active_days,omitempty"`
	GameChannelFee       uint32   `protobuf:"varint,27,opt,name=game_channel_fee,json=gameChannelFee,proto3" json:"game_channel_fee,omitempty"`
	VirtualFee           uint32   `protobuf:"varint,28,opt,name=virtual_fee,json=virtualFee,proto3" json:"virtual_fee,omitempty"`
	VirtualIncome        uint32   `protobuf:"varint,29,opt,name=virtual_income,json=virtualIncome,proto3" json:"virtual_income,omitempty"`
	VirtualMin           uint32   `protobuf:"varint,30,opt,name=virtual_min,json=virtualMin,proto3" json:"virtual_min,omitempty"`
	VirtualActiveDays    uint32   `protobuf:"varint,31,opt,name=virtual_active_days,json=virtualActiveDays,proto3" json:"virtual_active_days,omitempty"`
	ConsumerCnt          uint32   `protobuf:"varint,32,opt,name=consumer_cnt,json=consumerCnt,proto3" json:"consumer_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorMonthlyStats) Reset()         { *m = AnchorMonthlyStats{} }
func (m *AnchorMonthlyStats) String() string { return proto.CompactTextString(m) }
func (*AnchorMonthlyStats) ProtoMessage()    {}
func (*AnchorMonthlyStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{36}
}
func (m *AnchorMonthlyStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorMonthlyStats.Unmarshal(m, b)
}
func (m *AnchorMonthlyStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorMonthlyStats.Marshal(b, m, deterministic)
}
func (dst *AnchorMonthlyStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorMonthlyStats.Merge(dst, src)
}
func (m *AnchorMonthlyStats) XXX_Size() int {
	return xxx_messageInfo_AnchorMonthlyStats.Size(m)
}
func (m *AnchorMonthlyStats) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorMonthlyStats.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorMonthlyStats proto.InternalMessageInfo

func (m *AnchorMonthlyStats) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorMonthlyStats) GetChannelFee() uint32 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *AnchorMonthlyStats) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *AnchorMonthlyStats) GetDayLiveValidCnt() uint32 {
	if m != nil {
		return m.DayLiveValidCnt
	}
	return 0
}

func (m *AnchorMonthlyStats) GetLiveValidMinutes() uint32 {
	if m != nil {
		return m.LiveValidMinutes
	}
	return 0
}

func (m *AnchorMonthlyStats) GetNewFlag() uint32 {
	if m != nil {
		return m.NewFlag
	}
	return 0
}

func (m *AnchorMonthlyStats) GetLastMonthFansCnt() uint32 {
	if m != nil {
		return m.LastMonthFansCnt
	}
	return 0
}

func (m *AnchorMonthlyStats) GetNewFansCnt() uint32 {
	if m != nil {
		return m.NewFansCnt
	}
	return 0
}

func (m *AnchorMonthlyStats) GetActiveFans() uint32 {
	if m != nil {
		return m.ActiveFans
	}
	return 0
}

func (m *AnchorMonthlyStats) GetSpFans() uint32 {
	if m != nil {
		return m.SpFans
	}
	return 0
}

func (m *AnchorMonthlyStats) GetFansSendFee() uint32 {
	if m != nil {
		return m.FansSendFee
	}
	return 0
}

func (m *AnchorMonthlyStats) GetFollowCnt() uint32 {
	if m != nil {
		return m.FollowCnt
	}
	return 0
}

func (m *AnchorMonthlyStats) GetDateTs() uint32 {
	if m != nil {
		return m.DateTs
	}
	return 0
}

func (m *AnchorMonthlyStats) GetLiveMinutes() uint32 {
	if m != nil {
		return m.LiveMinutes
	}
	return 0
}

func (m *AnchorMonthlyStats) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *AnchorMonthlyStats) GetAnchorKnightIncome() uint32 {
	if m != nil {
		return m.AnchorKnightIncome
	}
	return 0
}

func (m *AnchorMonthlyStats) GetLiveActiveDays() uint32 {
	if m != nil {
		return m.LiveActiveDays
	}
	return 0
}

func (m *AnchorMonthlyStats) GetIsQualityAnchor() bool {
	if m != nil {
		return m.IsQualityAnchor
	}
	return false
}

func (m *AnchorMonthlyStats) GetIsActiveAnchor() bool {
	if m != nil {
		return m.IsActiveAnchor
	}
	return false
}

func (m *AnchorMonthlyStats) GetIsNewActiveAnchor() bool {
	if m != nil {
		return m.IsNewActiveAnchor
	}
	return false
}

func (m *AnchorMonthlyStats) GetChannelPkgFee() uint32 {
	if m != nil {
		return m.ChannelPkgFee
	}
	return 0
}

func (m *AnchorMonthlyStats) GetAnchorPkgIncome() uint32 {
	if m != nil {
		return m.AnchorPkgIncome
	}
	return 0
}

func (m *AnchorMonthlyStats) GetIsProfessionPrac() bool {
	if m != nil {
		return m.IsProfessionPrac
	}
	return false
}

func (m *AnchorMonthlyStats) GetGameFee() uint32 {
	if m != nil {
		return m.GameFee
	}
	return 0
}

func (m *AnchorMonthlyStats) GetGameMin() uint32 {
	if m != nil {
		return m.GameMin
	}
	return 0
}

func (m *AnchorMonthlyStats) GetGameActiveDays() uint32 {
	if m != nil {
		return m.GameActiveDays
	}
	return 0
}

func (m *AnchorMonthlyStats) GetGameChannelFee() uint32 {
	if m != nil {
		return m.GameChannelFee
	}
	return 0
}

func (m *AnchorMonthlyStats) GetVirtualFee() uint32 {
	if m != nil {
		return m.VirtualFee
	}
	return 0
}

func (m *AnchorMonthlyStats) GetVirtualIncome() uint32 {
	if m != nil {
		return m.VirtualIncome
	}
	return 0
}

func (m *AnchorMonthlyStats) GetVirtualMin() uint32 {
	if m != nil {
		return m.VirtualMin
	}
	return 0
}

func (m *AnchorMonthlyStats) GetVirtualActiveDays() uint32 {
	if m != nil {
		return m.VirtualActiveDays
	}
	return 0
}

func (m *AnchorMonthlyStats) GetConsumerCnt() uint32 {
	if m != nil {
		return m.ConsumerCnt
	}
	return 0
}

type GetAnchorMonthlyStatsReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	MonthTime            uint32   `protobuf:"varint,3,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorMonthlyStatsReq) Reset()         { *m = GetAnchorMonthlyStatsReq{} }
func (m *GetAnchorMonthlyStatsReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMonthlyStatsReq) ProtoMessage()    {}
func (*GetAnchorMonthlyStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{37}
}
func (m *GetAnchorMonthlyStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMonthlyStatsReq.Unmarshal(m, b)
}
func (m *GetAnchorMonthlyStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMonthlyStatsReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMonthlyStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMonthlyStatsReq.Merge(dst, src)
}
func (m *GetAnchorMonthlyStatsReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMonthlyStatsReq.Size(m)
}
func (m *GetAnchorMonthlyStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMonthlyStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMonthlyStatsReq proto.InternalMessageInfo

func (m *GetAnchorMonthlyStatsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAnchorMonthlyStatsReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetAnchorMonthlyStatsReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type GetAnchorMonthlyStatsResp struct {
	Stats                *AnchorMonthlyStats `protobuf:"bytes,1,opt,name=stats,proto3" json:"stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAnchorMonthlyStatsResp) Reset()         { *m = GetAnchorMonthlyStatsResp{} }
func (m *GetAnchorMonthlyStatsResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMonthlyStatsResp) ProtoMessage()    {}
func (*GetAnchorMonthlyStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{38}
}
func (m *GetAnchorMonthlyStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMonthlyStatsResp.Unmarshal(m, b)
}
func (m *GetAnchorMonthlyStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMonthlyStatsResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMonthlyStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMonthlyStatsResp.Merge(dst, src)
}
func (m *GetAnchorMonthlyStatsResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMonthlyStatsResp.Size(m)
}
func (m *GetAnchorMonthlyStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMonthlyStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMonthlyStatsResp proto.InternalMessageInfo

func (m *GetAnchorMonthlyStatsResp) GetStats() *AnchorMonthlyStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorMonthlyStatsListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginMonth           uint32   `protobuf:"varint,2,opt,name=beginMonth,proto3" json:"beginMonth,omitempty"`
	EndMonth             uint32   `protobuf:"varint,3,opt,name=endMonth,proto3" json:"endMonth,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	AgentUid             uint32   `protobuf:"varint,6,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AnchorList           []uint32 `protobuf:"varint,7,rep,packed,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorMonthlyStatsListReq) Reset()         { *m = GetAnchorMonthlyStatsListReq{} }
func (m *GetAnchorMonthlyStatsListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMonthlyStatsListReq) ProtoMessage()    {}
func (*GetAnchorMonthlyStatsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{39}
}
func (m *GetAnchorMonthlyStatsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMonthlyStatsListReq.Unmarshal(m, b)
}
func (m *GetAnchorMonthlyStatsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMonthlyStatsListReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMonthlyStatsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMonthlyStatsListReq.Merge(dst, src)
}
func (m *GetAnchorMonthlyStatsListReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMonthlyStatsListReq.Size(m)
}
func (m *GetAnchorMonthlyStatsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMonthlyStatsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMonthlyStatsListReq proto.InternalMessageInfo

func (m *GetAnchorMonthlyStatsListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetAnchorMonthlyStatsListReq) GetBeginMonth() uint32 {
	if m != nil {
		return m.BeginMonth
	}
	return 0
}

func (m *GetAnchorMonthlyStatsListReq) GetEndMonth() uint32 {
	if m != nil {
		return m.EndMonth
	}
	return 0
}

func (m *GetAnchorMonthlyStatsListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAnchorMonthlyStatsListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAnchorMonthlyStatsListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetAnchorMonthlyStatsListReq) GetAnchorList() []uint32 {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type GetAnchorMonthlyStatsListResp struct {
	List                 []*AnchorMonthlyStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32                `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetAnchorMonthlyStatsListResp) Reset()         { *m = GetAnchorMonthlyStatsListResp{} }
func (m *GetAnchorMonthlyStatsListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMonthlyStatsListResp) ProtoMessage()    {}
func (*GetAnchorMonthlyStatsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{40}
}
func (m *GetAnchorMonthlyStatsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMonthlyStatsListResp.Unmarshal(m, b)
}
func (m *GetAnchorMonthlyStatsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMonthlyStatsListResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMonthlyStatsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMonthlyStatsListResp.Merge(dst, src)
}
func (m *GetAnchorMonthlyStatsListResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMonthlyStatsListResp.Size(m)
}
func (m *GetAnchorMonthlyStatsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMonthlyStatsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMonthlyStatsListResp proto.InternalMessageInfo

func (m *GetAnchorMonthlyStatsListResp) GetList() []*AnchorMonthlyStats {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetAnchorMonthlyStatsListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type GetGuildAnchorMonthlyStatsListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Condition            uint32   `protobuf:"varint,2,opt,name=condition,proto3" json:"condition,omitempty"`
	Begin                uint32   `protobuf:"varint,3,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	MonthTime            uint32   `protobuf:"varint,5,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	AgentUid             uint32   `protobuf:"varint,6,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAnchorMonthlyStatsListReq) Reset()         { *m = GetGuildAnchorMonthlyStatsListReq{} }
func (m *GetGuildAnchorMonthlyStatsListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorMonthlyStatsListReq) ProtoMessage()    {}
func (*GetGuildAnchorMonthlyStatsListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{41}
}
func (m *GetGuildAnchorMonthlyStatsListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorMonthlyStatsListReq.Unmarshal(m, b)
}
func (m *GetGuildAnchorMonthlyStatsListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorMonthlyStatsListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorMonthlyStatsListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorMonthlyStatsListReq.Merge(dst, src)
}
func (m *GetGuildAnchorMonthlyStatsListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorMonthlyStatsListReq.Size(m)
}
func (m *GetGuildAnchorMonthlyStatsListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorMonthlyStatsListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorMonthlyStatsListReq proto.InternalMessageInfo

func (m *GetGuildAnchorMonthlyStatsListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildAnchorMonthlyStatsListReq) GetCondition() uint32 {
	if m != nil {
		return m.Condition
	}
	return 0
}

func (m *GetGuildAnchorMonthlyStatsListReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetGuildAnchorMonthlyStatsListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetGuildAnchorMonthlyStatsListReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

func (m *GetGuildAnchorMonthlyStatsListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

type GetGuildAnchorMonthlyStatsListResp struct {
	StatsList            []*AnchorMonthlyStats `protobuf:"bytes,1,rep,name=stats_list,json=statsList,proto3" json:"stats_list,omitempty"`
	TotalCnt             uint32                `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetGuildAnchorMonthlyStatsListResp) Reset()         { *m = GetGuildAnchorMonthlyStatsListResp{} }
func (m *GetGuildAnchorMonthlyStatsListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorMonthlyStatsListResp) ProtoMessage()    {}
func (*GetGuildAnchorMonthlyStatsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{42}
}
func (m *GetGuildAnchorMonthlyStatsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorMonthlyStatsListResp.Unmarshal(m, b)
}
func (m *GetGuildAnchorMonthlyStatsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorMonthlyStatsListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorMonthlyStatsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorMonthlyStatsListResp.Merge(dst, src)
}
func (m *GetGuildAnchorMonthlyStatsListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorMonthlyStatsListResp.Size(m)
}
func (m *GetGuildAnchorMonthlyStatsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorMonthlyStatsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorMonthlyStatsListResp proto.InternalMessageInfo

func (m *GetGuildAnchorMonthlyStatsListResp) GetStatsList() []*AnchorMonthlyStats {
	if m != nil {
		return m.StatsList
	}
	return nil
}

func (m *GetGuildAnchorMonthlyStatsListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type BatchGetAnchorMonthlyStatsReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	MonthTs              uint32   `protobuf:"varint,2,opt,name=month_ts,json=monthTs,proto3" json:"month_ts,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsReq) Reset()         { *m = BatchGetAnchorMonthlyStatsReq{} }
func (m *BatchGetAnchorMonthlyStatsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorMonthlyStatsReq) ProtoMessage()    {}
func (*BatchGetAnchorMonthlyStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{43}
}
func (m *BatchGetAnchorMonthlyStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsReq.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsReq.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsReq.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsReq proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetAnchorMonthlyStatsReq) GetMonthTs() uint32 {
	if m != nil {
		return m.MonthTs
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type BatchGetAnchorMonthlyStatsResp struct {
	StatsList            []*AnchorMonthlyStats `protobuf:"bytes,1,rep,name=stats_list,json=statsList,proto3" json:"stats_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsResp) Reset()         { *m = BatchGetAnchorMonthlyStatsResp{} }
func (m *BatchGetAnchorMonthlyStatsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorMonthlyStatsResp) ProtoMessage()    {}
func (*BatchGetAnchorMonthlyStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{44}
}
func (m *BatchGetAnchorMonthlyStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsResp.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsResp.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsResp.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsResp proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsResp) GetStatsList() []*AnchorMonthlyStats {
	if m != nil {
		return m.StatsList
	}
	return nil
}

type BatchGetAnchorMonthlyStatsWithDateReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	AgentUid             uint32   `protobuf:"varint,5,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsWithDateReq) Reset()         { *m = BatchGetAnchorMonthlyStatsWithDateReq{} }
func (m *BatchGetAnchorMonthlyStatsWithDateReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorMonthlyStatsWithDateReq) ProtoMessage()    {}
func (*BatchGetAnchorMonthlyStatsWithDateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{45}
}
func (m *BatchGetAnchorMonthlyStatsWithDateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateReq.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsWithDateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsWithDateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateReq.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsWithDateReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateReq.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsWithDateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateReq proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsWithDateReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsWithDateReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetAnchorMonthlyStatsWithDateReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsWithDateReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsWithDateReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

type BatchGetAnchorMonthlyStatsWithDateResp struct {
	StatsList            []*AnchorMonthlyStats `protobuf:"bytes,1,rep,name=stats_list,json=statsList,proto3" json:"stats_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsWithDateResp) Reset() {
	*m = BatchGetAnchorMonthlyStatsWithDateResp{}
}
func (m *BatchGetAnchorMonthlyStatsWithDateResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorMonthlyStatsWithDateResp) ProtoMessage()    {}
func (*BatchGetAnchorMonthlyStatsWithDateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{46}
}
func (m *BatchGetAnchorMonthlyStatsWithDateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateResp.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsWithDateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsWithDateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateResp.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsWithDateResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateResp.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsWithDateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsWithDateResp proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsWithDateResp) GetStatsList() []*AnchorMonthlyStats {
	if m != nil {
		return m.StatsList
	}
	return nil
}

type MonthlyTotalStats struct {
	MonthTime            uint32   `protobuf:"varint,1,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,2,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	DayLiveValidCnt      uint32   `protobuf:"varint,3,opt,name=day_live_valid_cnt,json=dayLiveValidCnt,proto3" json:"day_live_valid_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthlyTotalStats) Reset()         { *m = MonthlyTotalStats{} }
func (m *MonthlyTotalStats) String() string { return proto.CompactTextString(m) }
func (*MonthlyTotalStats) ProtoMessage()    {}
func (*MonthlyTotalStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{47}
}
func (m *MonthlyTotalStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthlyTotalStats.Unmarshal(m, b)
}
func (m *MonthlyTotalStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthlyTotalStats.Marshal(b, m, deterministic)
}
func (dst *MonthlyTotalStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthlyTotalStats.Merge(dst, src)
}
func (m *MonthlyTotalStats) XXX_Size() int {
	return xxx_messageInfo_MonthlyTotalStats.Size(m)
}
func (m *MonthlyTotalStats) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthlyTotalStats.DiscardUnknown(m)
}

var xxx_messageInfo_MonthlyTotalStats proto.InternalMessageInfo

func (m *MonthlyTotalStats) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

func (m *MonthlyTotalStats) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *MonthlyTotalStats) GetDayLiveValidCnt() uint32 {
	if m != nil {
		return m.DayLiveValidCnt
	}
	return 0
}

type GetAnchorMonthlyTotalStatsReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	MonthTimeList        []uint32 `protobuf:"varint,2,rep,packed,name=month_time_list,json=monthTimeList,proto3" json:"month_time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorMonthlyTotalStatsReq) Reset()         { *m = GetAnchorMonthlyTotalStatsReq{} }
func (m *GetAnchorMonthlyTotalStatsReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMonthlyTotalStatsReq) ProtoMessage()    {}
func (*GetAnchorMonthlyTotalStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{48}
}
func (m *GetAnchorMonthlyTotalStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMonthlyTotalStatsReq.Unmarshal(m, b)
}
func (m *GetAnchorMonthlyTotalStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMonthlyTotalStatsReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMonthlyTotalStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMonthlyTotalStatsReq.Merge(dst, src)
}
func (m *GetAnchorMonthlyTotalStatsReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMonthlyTotalStatsReq.Size(m)
}
func (m *GetAnchorMonthlyTotalStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMonthlyTotalStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMonthlyTotalStatsReq proto.InternalMessageInfo

func (m *GetAnchorMonthlyTotalStatsReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetAnchorMonthlyTotalStatsReq) GetMonthTimeList() []uint32 {
	if m != nil {
		return m.MonthTimeList
	}
	return nil
}

type GetAnchorMonthlyTotalStatsResp struct {
	Stats                []*MonthlyTotalStats `protobuf:"bytes,1,rep,name=stats,proto3" json:"stats,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetAnchorMonthlyTotalStatsResp) Reset()         { *m = GetAnchorMonthlyTotalStatsResp{} }
func (m *GetAnchorMonthlyTotalStatsResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMonthlyTotalStatsResp) ProtoMessage()    {}
func (*GetAnchorMonthlyTotalStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{49}
}
func (m *GetAnchorMonthlyTotalStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMonthlyTotalStatsResp.Unmarshal(m, b)
}
func (m *GetAnchorMonthlyTotalStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMonthlyTotalStatsResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMonthlyTotalStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMonthlyTotalStatsResp.Merge(dst, src)
}
func (m *GetAnchorMonthlyTotalStatsResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMonthlyTotalStatsResp.Size(m)
}
func (m *GetAnchorMonthlyTotalStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMonthlyTotalStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMonthlyTotalStatsResp proto.InternalMessageInfo

func (m *GetAnchorMonthlyTotalStatsResp) GetStats() []*MonthlyTotalStats {
	if m != nil {
		return m.Stats
	}
	return nil
}

// 更新公会主播的经纪人信息
type UpdateGuildAnchorAgentInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AgentUid             uint32   `protobuf:"varint,2,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AnchorList           []uint32 `protobuf:"varint,3,rep,packed,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGuildAnchorAgentInfoReq) Reset()         { *m = UpdateGuildAnchorAgentInfoReq{} }
func (m *UpdateGuildAnchorAgentInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildAnchorAgentInfoReq) ProtoMessage()    {}
func (*UpdateGuildAnchorAgentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{50}
}
func (m *UpdateGuildAnchorAgentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGuildAnchorAgentInfoReq.Unmarshal(m, b)
}
func (m *UpdateGuildAnchorAgentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGuildAnchorAgentInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGuildAnchorAgentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGuildAnchorAgentInfoReq.Merge(dst, src)
}
func (m *UpdateGuildAnchorAgentInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGuildAnchorAgentInfoReq.Size(m)
}
func (m *UpdateGuildAnchorAgentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGuildAnchorAgentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGuildAnchorAgentInfoReq proto.InternalMessageInfo

func (m *UpdateGuildAnchorAgentInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildAnchorAgentInfoReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *UpdateGuildAnchorAgentInfoReq) GetAnchorList() []uint32 {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

type UpdateGuildAnchorAgentInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGuildAnchorAgentInfoResp) Reset()         { *m = UpdateGuildAnchorAgentInfoResp{} }
func (m *UpdateGuildAnchorAgentInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildAnchorAgentInfoResp) ProtoMessage()    {}
func (*UpdateGuildAnchorAgentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{51}
}
func (m *UpdateGuildAnchorAgentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGuildAnchorAgentInfoResp.Unmarshal(m, b)
}
func (m *UpdateGuildAnchorAgentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGuildAnchorAgentInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGuildAnchorAgentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGuildAnchorAgentInfoResp.Merge(dst, src)
}
func (m *UpdateGuildAnchorAgentInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGuildAnchorAgentInfoResp.Size(m)
}
func (m *UpdateGuildAnchorAgentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGuildAnchorAgentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGuildAnchorAgentInfoResp proto.InternalMessageInfo

type BatchGetAnchorMonthlyStatsByUidReq struct {
	AnchorUids           []uint32 `protobuf:"varint,1,rep,packed,name=anchor_uids,json=anchorUids,proto3" json:"anchor_uids,omitempty"`
	MonthTime            uint32   `protobuf:"varint,2,opt,name=month_time,json=monthTime,proto3" json:"month_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsByUidReq) Reset()         { *m = BatchGetAnchorMonthlyStatsByUidReq{} }
func (m *BatchGetAnchorMonthlyStatsByUidReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorMonthlyStatsByUidReq) ProtoMessage()    {}
func (*BatchGetAnchorMonthlyStatsByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{52}
}
func (m *BatchGetAnchorMonthlyStatsByUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidReq.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsByUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsByUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidReq.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsByUidReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidReq.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsByUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidReq proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsByUidReq) GetAnchorUids() []uint32 {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func (m *BatchGetAnchorMonthlyStatsByUidReq) GetMonthTime() uint32 {
	if m != nil {
		return m.MonthTime
	}
	return 0
}

type BatchGetAnchorMonthlyStatsByUidResp struct {
	List                 []*BatchGetAnchorMonthlyStatsByUidRespMonthStats `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                         `json:"-"`
	XXX_unrecognized     []byte                                           `json:"-"`
	XXX_sizecache        int32                                            `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsByUidResp) Reset()         { *m = BatchGetAnchorMonthlyStatsByUidResp{} }
func (m *BatchGetAnchorMonthlyStatsByUidResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetAnchorMonthlyStatsByUidResp) ProtoMessage()    {}
func (*BatchGetAnchorMonthlyStatsByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{53}
}
func (m *BatchGetAnchorMonthlyStatsByUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidResp.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsByUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsByUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidResp.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsByUidResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidResp.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsByUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidResp proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsByUidResp) GetList() []*BatchGetAnchorMonthlyStatsByUidRespMonthStats {
	if m != nil {
		return m.List
	}
	return nil
}

// buf:lint:ignore MESSAGE_PASCAL_CASE
type BatchGetAnchorMonthlyStatsByUidRespMonthStats struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	Yearmonth            string   `protobuf:"bytes,2,opt,name=yearmonth,proto3" json:"yearmonth,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,3,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	NewFansCnt           uint32   `protobuf:"varint,4,opt,name=new_fans_cnt,json=newFansCnt,proto3" json:"new_fans_cnt,omitempty"`
	LiveActiveCnt        uint32   `protobuf:"varint,5,opt,name=live_active_cnt,json=liveActiveCnt,proto3" json:"live_active_cnt,omitempty"`
	ConsumerCnt          uint32   `protobuf:"varint,6,opt,name=consumer_cnt,json=consumerCnt,proto3" json:"consumer_cnt,omitempty"`
	KnightIncome         uint32   `protobuf:"varint,7,opt,name=knight_income,json=knightIncome,proto3" json:"knight_income,omitempty"`
	AudienceCnt          uint32   `protobuf:"varint,8,opt,name=audience_cnt,json=audienceCnt,proto3" json:"audience_cnt,omitempty"`
	LiveValidMinutes     uint32   `protobuf:"varint,9,opt,name=live_valid_minutes,json=liveValidMinutes,proto3" json:"live_valid_minutes,omitempty"`
	DayLiveValidCnt      uint32   `protobuf:"varint,10,opt,name=day_live_valid_cnt,json=dayLiveValidCnt,proto3" json:"day_live_valid_cnt,omitempty"`
	ChannelFee           uint32   `protobuf:"varint,11,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	FollowCnt            uint32   `protobuf:"varint,12,opt,name=follow_cnt,json=followCnt,proto3" json:"follow_cnt,omitempty"`
	GameFee              uint32   `protobuf:"varint,13,opt,name=game_fee,json=gameFee,proto3" json:"game_fee,omitempty"`
	GameMin              uint32   `protobuf:"varint,14,opt,name=game_min,json=gameMin,proto3" json:"game_min,omitempty"`
	GameActiveDays       uint32   `protobuf:"varint,15,opt,name=game_active_days,json=gameActiveDays,proto3" json:"game_active_days,omitempty"`
	GameChannelFee       uint32   `protobuf:"varint,16,opt,name=game_channel_fee,json=gameChannelFee,proto3" json:"game_channel_fee,omitempty"`
	VirtualFee           uint32   `protobuf:"varint,17,opt,name=virtual_fee,json=virtualFee,proto3" json:"virtual_fee,omitempty"`
	VirtualIncome        uint32   `protobuf:"varint,18,opt,name=virtual_income,json=virtualIncome,proto3" json:"virtual_income,omitempty"`
	VirtualMin           uint32   `protobuf:"varint,19,opt,name=virtual_min,json=virtualMin,proto3" json:"virtual_min,omitempty"`
	VirtualActiveDays    uint32   `protobuf:"varint,20,opt,name=virtual_active_days,json=virtualActiveDays,proto3" json:"virtual_active_days,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) Reset() {
	*m = BatchGetAnchorMonthlyStatsByUidRespMonthStats{}
}
func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetAnchorMonthlyStatsByUidRespMonthStats) ProtoMessage() {}
func (*BatchGetAnchorMonthlyStatsByUidRespMonthStats) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{53, 0}
}
func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidRespMonthStats.Unmarshal(m, b)
}
func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidRespMonthStats.Marshal(b, m, deterministic)
}
func (dst *BatchGetAnchorMonthlyStatsByUidRespMonthStats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidRespMonthStats.Merge(dst, src)
}
func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) XXX_Size() int {
	return xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidRespMonthStats.Size(m)
}
func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidRespMonthStats.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetAnchorMonthlyStatsByUidRespMonthStats proto.InternalMessageInfo

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetYearmonth() string {
	if m != nil {
		return m.Yearmonth
	}
	return ""
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetNewFansCnt() uint32 {
	if m != nil {
		return m.NewFansCnt
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetLiveActiveCnt() uint32 {
	if m != nil {
		return m.LiveActiveCnt
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetConsumerCnt() uint32 {
	if m != nil {
		return m.ConsumerCnt
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetKnightIncome() uint32 {
	if m != nil {
		return m.KnightIncome
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetAudienceCnt() uint32 {
	if m != nil {
		return m.AudienceCnt
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetLiveValidMinutes() uint32 {
	if m != nil {
		return m.LiveValidMinutes
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetDayLiveValidCnt() uint32 {
	if m != nil {
		return m.DayLiveValidCnt
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetChannelFee() uint32 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetFollowCnt() uint32 {
	if m != nil {
		return m.FollowCnt
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetGameFee() uint32 {
	if m != nil {
		return m.GameFee
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetGameMin() uint32 {
	if m != nil {
		return m.GameMin
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetGameActiveDays() uint32 {
	if m != nil {
		return m.GameActiveDays
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetGameChannelFee() uint32 {
	if m != nil {
		return m.GameChannelFee
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetVirtualFee() uint32 {
	if m != nil {
		return m.VirtualFee
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetVirtualIncome() uint32 {
	if m != nil {
		return m.VirtualIncome
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetVirtualMin() uint32 {
	if m != nil {
		return m.VirtualMin
	}
	return 0
}

func (m *BatchGetAnchorMonthlyStatsByUidRespMonthStats) GetVirtualActiveDays() uint32 {
	if m != nil {
		return m.VirtualActiveDays
	}
	return 0
}

// 场次数据
type MatchData struct {
	ChannelFee           uint32   `protobuf:"varint,1,opt,name=channel_fee,json=channelFee,proto3" json:"channel_fee,omitempty"`
	AnchorIncome         uint32   `protobuf:"varint,2,opt,name=anchor_income,json=anchorIncome,proto3" json:"anchor_income,omitempty"`
	LiveMin              uint32   `protobuf:"varint,3,opt,name=live_min,json=liveMin,proto3" json:"live_min,omitempty"`
	PkgGiftRatio         float32  `protobuf:"fixed32,4,opt,name=pkg_gift_ratio,json=pkgGiftRatio,proto3" json:"pkg_gift_ratio,omitempty"`
	LiveTs               uint32   `protobuf:"varint,5,opt,name=live_ts,json=liveTs,proto3" json:"live_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MatchData) Reset()         { *m = MatchData{} }
func (m *MatchData) String() string { return proto.CompactTextString(m) }
func (*MatchData) ProtoMessage()    {}
func (*MatchData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{54}
}
func (m *MatchData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MatchData.Unmarshal(m, b)
}
func (m *MatchData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MatchData.Marshal(b, m, deterministic)
}
func (dst *MatchData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MatchData.Merge(dst, src)
}
func (m *MatchData) XXX_Size() int {
	return xxx_messageInfo_MatchData.Size(m)
}
func (m *MatchData) XXX_DiscardUnknown() {
	xxx_messageInfo_MatchData.DiscardUnknown(m)
}

var xxx_messageInfo_MatchData proto.InternalMessageInfo

func (m *MatchData) GetChannelFee() uint32 {
	if m != nil {
		return m.ChannelFee
	}
	return 0
}

func (m *MatchData) GetAnchorIncome() uint32 {
	if m != nil {
		return m.AnchorIncome
	}
	return 0
}

func (m *MatchData) GetLiveMin() uint32 {
	if m != nil {
		return m.LiveMin
	}
	return 0
}

func (m *MatchData) GetPkgGiftRatio() float32 {
	if m != nil {
		return m.PkgGiftRatio
	}
	return 0
}

func (m *MatchData) GetLiveTs() uint32 {
	if m != nil {
		return m.LiveTs
	}
	return 0
}

type AnchorMatchData struct {
	BaseInfo             *AnchorBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	MatchData            *MatchData      `protobuf:"bytes,2,opt,name=match_data,json=matchData,proto3" json:"match_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AnchorMatchData) Reset()         { *m = AnchorMatchData{} }
func (m *AnchorMatchData) String() string { return proto.CompactTextString(m) }
func (*AnchorMatchData) ProtoMessage()    {}
func (*AnchorMatchData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{55}
}
func (m *AnchorMatchData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorMatchData.Unmarshal(m, b)
}
func (m *AnchorMatchData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorMatchData.Marshal(b, m, deterministic)
}
func (dst *AnchorMatchData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorMatchData.Merge(dst, src)
}
func (m *AnchorMatchData) XXX_Size() int {
	return xxx_messageInfo_AnchorMatchData.Size(m)
}
func (m *AnchorMatchData) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorMatchData.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorMatchData proto.InternalMessageInfo

func (m *AnchorMatchData) GetBaseInfo() *AnchorBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *AnchorMatchData) GetMatchData() *MatchData {
	if m != nil {
		return m.MatchData
	}
	return nil
}

type GetGuildAnchorListReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AgentUid             uint32   `protobuf:"varint,2,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	LiveType             uint32   `protobuf:"varint,3,opt,name=live_type,json=liveType,proto3" json:"live_type,omitempty"`
	SortType             uint32   `protobuf:"varint,4,opt,name=sort_type,json=sortType,proto3" json:"sort_type,omitempty"`
	Sort                 uint32   `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	GuildId              uint32   `protobuf:"varint,8,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,9,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAnchorListReq) Reset()         { *m = GetGuildAnchorListReq{} }
func (m *GetGuildAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorListReq) ProtoMessage()    {}
func (*GetGuildAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{56}
}
func (m *GetGuildAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorListReq.Unmarshal(m, b)
}
func (m *GetGuildAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorListReq.Merge(dst, src)
}
func (m *GetGuildAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorListReq.Size(m)
}
func (m *GetGuildAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorListReq proto.InternalMessageInfo

func (m *GetGuildAnchorListReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetLiveType() uint32 {
	if m != nil {
		return m.LiveType
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetSortType() uint32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetSort() uint32 {
	if m != nil {
		return m.Sort
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildAnchorListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGuildAnchorListResp struct {
	AnchorList           []*AnchorMatchData `protobuf:"bytes,1,rep,name=anchor_list,json=anchorList,proto3" json:"anchor_list,omitempty"`
	TotalCnt             uint32             `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	LiveAnchorCnt        uint32             `protobuf:"varint,3,opt,name=live_anchor_cnt,json=liveAnchorCnt,proto3" json:"live_anchor_cnt,omitempty"`
	GuildAnchorCnt       uint32             `protobuf:"varint,4,opt,name=guild_anchor_cnt,json=guildAnchorCnt,proto3" json:"guild_anchor_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetGuildAnchorListResp) Reset()         { *m = GetGuildAnchorListResp{} }
func (m *GetGuildAnchorListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAnchorListResp) ProtoMessage()    {}
func (*GetGuildAnchorListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{57}
}
func (m *GetGuildAnchorListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAnchorListResp.Unmarshal(m, b)
}
func (m *GetGuildAnchorListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAnchorListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAnchorListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAnchorListResp.Merge(dst, src)
}
func (m *GetGuildAnchorListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAnchorListResp.Size(m)
}
func (m *GetGuildAnchorListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAnchorListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAnchorListResp proto.InternalMessageInfo

func (m *GetGuildAnchorListResp) GetAnchorList() []*AnchorMatchData {
	if m != nil {
		return m.AnchorList
	}
	return nil
}

func (m *GetGuildAnchorListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *GetGuildAnchorListResp) GetLiveAnchorCnt() uint32 {
	if m != nil {
		return m.LiveAnchorCnt
	}
	return 0
}

func (m *GetGuildAnchorListResp) GetGuildAnchorCnt() uint32 {
	if m != nil {
		return m.GuildAnchorCnt
	}
	return 0
}

// 获取主播的场次数据
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorMatchListReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Guild_Id             uint32   `protobuf:"varint,6,opt,name=guild_Id,json=guildId,proto3" json:"guild_Id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorMatchListReq) Reset()         { *m = GetAnchorMatchListReq{} }
func (m *GetAnchorMatchListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMatchListReq) ProtoMessage()    {}
func (*GetAnchorMatchListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{58}
}
func (m *GetAnchorMatchListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMatchListReq.Unmarshal(m, b)
}
func (m *GetAnchorMatchListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMatchListReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMatchListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMatchListReq.Merge(dst, src)
}
func (m *GetAnchorMatchListReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMatchListReq.Size(m)
}
func (m *GetAnchorMatchListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMatchListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMatchListReq proto.InternalMessageInfo

func (m *GetAnchorMatchListReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetAnchorMatchListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetAnchorMatchListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *GetAnchorMatchListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAnchorMatchListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetAnchorMatchListReq) GetGuild_Id() uint32 {
	if m != nil {
		return m.Guild_Id
	}
	return 0
}

type GetAnchorMatchListResp struct {
	AnchorInfo           *AnchorBaseInfo `protobuf:"bytes,1,opt,name=anchor_info,json=anchorInfo,proto3" json:"anchor_info,omitempty"`
	DataList             []*MatchData    `protobuf:"bytes,2,rep,name=data_list,json=dataList,proto3" json:"data_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAnchorMatchListResp) Reset()         { *m = GetAnchorMatchListResp{} }
func (m *GetAnchorMatchListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorMatchListResp) ProtoMessage()    {}
func (*GetAnchorMatchListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{59}
}
func (m *GetAnchorMatchListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorMatchListResp.Unmarshal(m, b)
}
func (m *GetAnchorMatchListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorMatchListResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorMatchListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorMatchListResp.Merge(dst, src)
}
func (m *GetAnchorMatchListResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorMatchListResp.Size(m)
}
func (m *GetAnchorMatchListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorMatchListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorMatchListResp proto.InternalMessageInfo

func (m *GetAnchorMatchListResp) GetAnchorInfo() *AnchorBaseInfo {
	if m != nil {
		return m.AnchorInfo
	}
	return nil
}

func (m *GetAnchorMatchListResp) GetDataList() []*MatchData {
	if m != nil {
		return m.DataList
	}
	return nil
}

func (m *GetAnchorMatchListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 公会运营能力
type GuildOperationalData struct {
	GuildId                 uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	RevenueLevel            uint32   `protobuf:"varint,2,opt,name=revenue_level,json=revenueLevel,proto3" json:"revenue_level,omitempty"`
	NewSignAnchorCnt        uint32   `protobuf:"varint,4,opt,name=new_sign_anchor_cnt,json=newSignAnchorCnt,proto3" json:"new_sign_anchor_cnt,omitempty"`
	EnabledLiveUserCount    uint32   `protobuf:"varint,5,opt,name=enabled_live_user_count,json=enabledLiveUserCount,proto3" json:"enabled_live_user_count,omitempty"`
	EnabledLiveNewUserCount uint32   `protobuf:"varint,8,opt,name=enabled_live_new_user_count,json=enabledLiveNewUserCount,proto3" json:"enabled_live_new_user_count,omitempty"`
	NewActiveAnchorCnt      uint32   `protobuf:"varint,9,opt,name=new_active_anchor_cnt,json=newActiveAnchorCnt,proto3" json:"new_active_anchor_cnt,omitempty"`
	ProAnchorCnt            uint32   `protobuf:"varint,10,opt,name=pro_anchor_cnt,json=proAnchorCnt,proto3" json:"pro_anchor_cnt,omitempty"`
	MatureAnchorCnt         uint32   `protobuf:"varint,11,opt,name=mature_anchor_cnt,json=matureAnchorCnt,proto3" json:"mature_anchor_cnt,omitempty"`
	PotActiveAnchorCnt      uint32   `protobuf:"varint,12,opt,name=pot_active_anchor_cnt,json=potActiveAnchorCnt,proto3" json:"pot_active_anchor_cnt,omitempty"`
	NewSignProAnchor        uint32   `protobuf:"varint,13,opt,name=new_sign_pro_anchor,json=newSignProAnchor,proto3" json:"new_sign_pro_anchor,omitempty"`
	Revenue                 uint64   `protobuf:"varint,14,opt,name=revenue,proto3" json:"revenue,omitempty"`
	RevenueScore            uint32   `protobuf:"varint,15,opt,name=revenue_score,json=revenueScore,proto3" json:"revenue_score,omitempty"`
	ProAnchorScore          uint32   `protobuf:"varint,16,opt,name=pro_anchor_score,json=proAnchorScore,proto3" json:"pro_anchor_score,omitempty"`
	NewActiveAnchorScore    uint32   `protobuf:"varint,17,opt,name=new_active_anchor_score,json=newActiveAnchorScore,proto3" json:"new_active_anchor_score,omitempty"`
	MatureAnchorScore       uint32   `protobuf:"varint,18,opt,name=mature_anchor_score,json=matureAnchorScore,proto3" json:"mature_anchor_score,omitempty"`
	PotActiveAnchorScore    uint32   `protobuf:"varint,19,opt,name=pot_active_anchor_score,json=potActiveAnchorScore,proto3" json:"pot_active_anchor_score,omitempty"`
	TotalScore              uint32   `protobuf:"varint,20,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	RevenueTime             uint32   `protobuf:"varint,21,opt,name=revenue_time,json=revenueTime,proto3" json:"revenue_time,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GuildOperationalData) Reset()         { *m = GuildOperationalData{} }
func (m *GuildOperationalData) String() string { return proto.CompactTextString(m) }
func (*GuildOperationalData) ProtoMessage()    {}
func (*GuildOperationalData) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{60}
}
func (m *GuildOperationalData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildOperationalData.Unmarshal(m, b)
}
func (m *GuildOperationalData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildOperationalData.Marshal(b, m, deterministic)
}
func (dst *GuildOperationalData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildOperationalData.Merge(dst, src)
}
func (m *GuildOperationalData) XXX_Size() int {
	return xxx_messageInfo_GuildOperationalData.Size(m)
}
func (m *GuildOperationalData) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildOperationalData.DiscardUnknown(m)
}

var xxx_messageInfo_GuildOperationalData proto.InternalMessageInfo

func (m *GuildOperationalData) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildOperationalData) GetRevenueLevel() uint32 {
	if m != nil {
		return m.RevenueLevel
	}
	return 0
}

func (m *GuildOperationalData) GetNewSignAnchorCnt() uint32 {
	if m != nil {
		return m.NewSignAnchorCnt
	}
	return 0
}

func (m *GuildOperationalData) GetEnabledLiveUserCount() uint32 {
	if m != nil {
		return m.EnabledLiveUserCount
	}
	return 0
}

func (m *GuildOperationalData) GetEnabledLiveNewUserCount() uint32 {
	if m != nil {
		return m.EnabledLiveNewUserCount
	}
	return 0
}

func (m *GuildOperationalData) GetNewActiveAnchorCnt() uint32 {
	if m != nil {
		return m.NewActiveAnchorCnt
	}
	return 0
}

func (m *GuildOperationalData) GetProAnchorCnt() uint32 {
	if m != nil {
		return m.ProAnchorCnt
	}
	return 0
}

func (m *GuildOperationalData) GetMatureAnchorCnt() uint32 {
	if m != nil {
		return m.MatureAnchorCnt
	}
	return 0
}

func (m *GuildOperationalData) GetPotActiveAnchorCnt() uint32 {
	if m != nil {
		return m.PotActiveAnchorCnt
	}
	return 0
}

func (m *GuildOperationalData) GetNewSignProAnchor() uint32 {
	if m != nil {
		return m.NewSignProAnchor
	}
	return 0
}

func (m *GuildOperationalData) GetRevenue() uint64 {
	if m != nil {
		return m.Revenue
	}
	return 0
}

func (m *GuildOperationalData) GetRevenueScore() uint32 {
	if m != nil {
		return m.RevenueScore
	}
	return 0
}

func (m *GuildOperationalData) GetProAnchorScore() uint32 {
	if m != nil {
		return m.ProAnchorScore
	}
	return 0
}

func (m *GuildOperationalData) GetNewActiveAnchorScore() uint32 {
	if m != nil {
		return m.NewActiveAnchorScore
	}
	return 0
}

func (m *GuildOperationalData) GetMatureAnchorScore() uint32 {
	if m != nil {
		return m.MatureAnchorScore
	}
	return 0
}

func (m *GuildOperationalData) GetPotActiveAnchorScore() uint32 {
	if m != nil {
		return m.PotActiveAnchorScore
	}
	return 0
}

func (m *GuildOperationalData) GetTotalScore() uint32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *GuildOperationalData) GetRevenueTime() uint32 {
	if m != nil {
		return m.RevenueTime
	}
	return 0
}

// 获取公会的运营能力
type GetGuildOperationalCapabilitiesReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	RevenueLevel         uint32   `protobuf:"varint,2,opt,name=revenue_level,json=revenueLevel,proto3" json:"revenue_level,omitempty"`
	EndDate              int64    `protobuf:"varint,3,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildOperationalCapabilitiesReq) Reset()         { *m = GetGuildOperationalCapabilitiesReq{} }
func (m *GetGuildOperationalCapabilitiesReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildOperationalCapabilitiesReq) ProtoMessage()    {}
func (*GetGuildOperationalCapabilitiesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{61}
}
func (m *GetGuildOperationalCapabilitiesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildOperationalCapabilitiesReq.Unmarshal(m, b)
}
func (m *GetGuildOperationalCapabilitiesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildOperationalCapabilitiesReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildOperationalCapabilitiesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildOperationalCapabilitiesReq.Merge(dst, src)
}
func (m *GetGuildOperationalCapabilitiesReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildOperationalCapabilitiesReq.Size(m)
}
func (m *GetGuildOperationalCapabilitiesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildOperationalCapabilitiesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildOperationalCapabilitiesReq proto.InternalMessageInfo

func (m *GetGuildOperationalCapabilitiesReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildOperationalCapabilitiesReq) GetRevenueLevel() uint32 {
	if m != nil {
		return m.RevenueLevel
	}
	return 0
}

func (m *GetGuildOperationalCapabilitiesReq) GetEndDate() int64 {
	if m != nil {
		return m.EndDate
	}
	return 0
}

type GetGuildOperationalCapabilitiesResp struct {
	DataList             []*GuildOperationalData `protobuf:"bytes,1,rep,name=data_list,json=dataList,proto3" json:"data_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetGuildOperationalCapabilitiesResp) Reset()         { *m = GetGuildOperationalCapabilitiesResp{} }
func (m *GetGuildOperationalCapabilitiesResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildOperationalCapabilitiesResp) ProtoMessage()    {}
func (*GetGuildOperationalCapabilitiesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{62}
}
func (m *GetGuildOperationalCapabilitiesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildOperationalCapabilitiesResp.Unmarshal(m, b)
}
func (m *GetGuildOperationalCapabilitiesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildOperationalCapabilitiesResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildOperationalCapabilitiesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildOperationalCapabilitiesResp.Merge(dst, src)
}
func (m *GetGuildOperationalCapabilitiesResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildOperationalCapabilitiesResp.Size(m)
}
func (m *GetGuildOperationalCapabilitiesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildOperationalCapabilitiesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildOperationalCapabilitiesResp proto.InternalMessageInfo

func (m *GetGuildOperationalCapabilitiesResp) GetDataList() []*GuildOperationalData {
	if m != nil {
		return m.DataList
	}
	return nil
}

type UpdateGuildOperationalCapabilitiesReq struct {
	UpdateTime           uint32   `protobuf:"varint,22,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGuildOperationalCapabilitiesReq) Reset()         { *m = UpdateGuildOperationalCapabilitiesReq{} }
func (m *UpdateGuildOperationalCapabilitiesReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildOperationalCapabilitiesReq) ProtoMessage()    {}
func (*UpdateGuildOperationalCapabilitiesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{63}
}
func (m *UpdateGuildOperationalCapabilitiesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGuildOperationalCapabilitiesReq.Unmarshal(m, b)
}
func (m *UpdateGuildOperationalCapabilitiesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGuildOperationalCapabilitiesReq.Marshal(b, m, deterministic)
}
func (dst *UpdateGuildOperationalCapabilitiesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGuildOperationalCapabilitiesReq.Merge(dst, src)
}
func (m *UpdateGuildOperationalCapabilitiesReq) XXX_Size() int {
	return xxx_messageInfo_UpdateGuildOperationalCapabilitiesReq.Size(m)
}
func (m *UpdateGuildOperationalCapabilitiesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGuildOperationalCapabilitiesReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGuildOperationalCapabilitiesReq proto.InternalMessageInfo

func (m *UpdateGuildOperationalCapabilitiesReq) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type UpdateGuildOperationalCapabilitiesResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateGuildOperationalCapabilitiesResp) Reset() {
	*m = UpdateGuildOperationalCapabilitiesResp{}
}
func (m *UpdateGuildOperationalCapabilitiesResp) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildOperationalCapabilitiesResp) ProtoMessage()    {}
func (*UpdateGuildOperationalCapabilitiesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{64}
}
func (m *UpdateGuildOperationalCapabilitiesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateGuildOperationalCapabilitiesResp.Unmarshal(m, b)
}
func (m *UpdateGuildOperationalCapabilitiesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateGuildOperationalCapabilitiesResp.Marshal(b, m, deterministic)
}
func (dst *UpdateGuildOperationalCapabilitiesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateGuildOperationalCapabilitiesResp.Merge(dst, src)
}
func (m *UpdateGuildOperationalCapabilitiesResp) XXX_Size() int {
	return xxx_messageInfo_UpdateGuildOperationalCapabilitiesResp.Size(m)
}
func (m *UpdateGuildOperationalCapabilitiesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateGuildOperationalCapabilitiesResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateGuildOperationalCapabilitiesResp proto.InternalMessageInfo

// 经营分析
type BusinessAnalysis struct {
	KpiType              uint32   `protobuf:"varint,1,opt,name=kpi_type,json=kpiType,proto3" json:"kpi_type,omitempty"`
	Value                uint64   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	RatioValue           int64    `protobuf:"varint,3,opt,name=ratio_value,json=ratioValue,proto3" json:"ratio_value,omitempty"`
	Rank                 uint32   `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	RatioRank            int32    `protobuf:"varint,5,opt,name=ratio_rank,json=ratioRank,proto3" json:"ratio_rank,omitempty"`
	AllRank              uint32   `protobuf:"varint,6,opt,name=all_rank,json=allRank,proto3" json:"all_rank,omitempty"`
	LevelAve             uint64   `protobuf:"varint,7,opt,name=level_ave,json=levelAve,proto3" json:"level_ave,omitempty"`
	NewValue             uint64   `protobuf:"varint,8,opt,name=new_value,json=newValue,proto3" json:"new_value,omitempty"`
	OldValue             uint64   `protobuf:"varint,9,opt,name=old_value,json=oldValue,proto3" json:"old_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessAnalysis) Reset()         { *m = BusinessAnalysis{} }
func (m *BusinessAnalysis) String() string { return proto.CompactTextString(m) }
func (*BusinessAnalysis) ProtoMessage()    {}
func (*BusinessAnalysis) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{65}
}
func (m *BusinessAnalysis) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessAnalysis.Unmarshal(m, b)
}
func (m *BusinessAnalysis) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessAnalysis.Marshal(b, m, deterministic)
}
func (dst *BusinessAnalysis) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessAnalysis.Merge(dst, src)
}
func (m *BusinessAnalysis) XXX_Size() int {
	return xxx_messageInfo_BusinessAnalysis.Size(m)
}
func (m *BusinessAnalysis) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessAnalysis.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessAnalysis proto.InternalMessageInfo

func (m *BusinessAnalysis) GetKpiType() uint32 {
	if m != nil {
		return m.KpiType
	}
	return 0
}

func (m *BusinessAnalysis) GetValue() uint64 {
	if m != nil {
		return m.Value
	}
	return 0
}

func (m *BusinessAnalysis) GetRatioValue() int64 {
	if m != nil {
		return m.RatioValue
	}
	return 0
}

func (m *BusinessAnalysis) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *BusinessAnalysis) GetRatioRank() int32 {
	if m != nil {
		return m.RatioRank
	}
	return 0
}

func (m *BusinessAnalysis) GetAllRank() uint32 {
	if m != nil {
		return m.AllRank
	}
	return 0
}

func (m *BusinessAnalysis) GetLevelAve() uint64 {
	if m != nil {
		return m.LevelAve
	}
	return 0
}

func (m *BusinessAnalysis) GetNewValue() uint64 {
	if m != nil {
		return m.NewValue
	}
	return 0
}

func (m *BusinessAnalysis) GetOldValue() uint64 {
	if m != nil {
		return m.OldValue
	}
	return 0
}

// 经营诊断
type BusinessDiagnosis struct {
	KpiType              uint32   `protobuf:"varint,1,opt,name=kpi_type,json=kpiType,proto3" json:"kpi_type,omitempty"`
	Diag                 string   `protobuf:"bytes,2,opt,name=diag,proto3" json:"diag,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BusinessDiagnosis) Reset()         { *m = BusinessDiagnosis{} }
func (m *BusinessDiagnosis) String() string { return proto.CompactTextString(m) }
func (*BusinessDiagnosis) ProtoMessage()    {}
func (*BusinessDiagnosis) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{66}
}
func (m *BusinessDiagnosis) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BusinessDiagnosis.Unmarshal(m, b)
}
func (m *BusinessDiagnosis) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BusinessDiagnosis.Marshal(b, m, deterministic)
}
func (dst *BusinessDiagnosis) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BusinessDiagnosis.Merge(dst, src)
}
func (m *BusinessDiagnosis) XXX_Size() int {
	return xxx_messageInfo_BusinessDiagnosis.Size(m)
}
func (m *BusinessDiagnosis) XXX_DiscardUnknown() {
	xxx_messageInfo_BusinessDiagnosis.DiscardUnknown(m)
}

var xxx_messageInfo_BusinessDiagnosis proto.InternalMessageInfo

func (m *BusinessDiagnosis) GetKpiType() uint32 {
	if m != nil {
		return m.KpiType
	}
	return 0
}

func (m *BusinessDiagnosis) GetDiag() string {
	if m != nil {
		return m.Diag
	}
	return ""
}

// 周维度经营分析
type GetWeekBusinessAnalysisReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginTs              uint32   `protobuf:"varint,2,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWeekBusinessAnalysisReq) Reset()         { *m = GetWeekBusinessAnalysisReq{} }
func (m *GetWeekBusinessAnalysisReq) String() string { return proto.CompactTextString(m) }
func (*GetWeekBusinessAnalysisReq) ProtoMessage()    {}
func (*GetWeekBusinessAnalysisReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{67}
}
func (m *GetWeekBusinessAnalysisReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekBusinessAnalysisReq.Unmarshal(m, b)
}
func (m *GetWeekBusinessAnalysisReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekBusinessAnalysisReq.Marshal(b, m, deterministic)
}
func (dst *GetWeekBusinessAnalysisReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekBusinessAnalysisReq.Merge(dst, src)
}
func (m *GetWeekBusinessAnalysisReq) XXX_Size() int {
	return xxx_messageInfo_GetWeekBusinessAnalysisReq.Size(m)
}
func (m *GetWeekBusinessAnalysisReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekBusinessAnalysisReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekBusinessAnalysisReq proto.InternalMessageInfo

func (m *GetWeekBusinessAnalysisReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetWeekBusinessAnalysisReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *GetWeekBusinessAnalysisReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type GetWeekBusinessAnalysisResp struct {
	AnalysisList         []*BusinessAnalysis  `protobuf:"bytes,1,rep,name=analysis_list,json=analysisList,proto3" json:"analysis_list,omitempty"`
	DiagnosisList        []*BusinessDiagnosis `protobuf:"bytes,2,rep,name=diagnosis_list,json=diagnosisList,proto3" json:"diagnosis_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetWeekBusinessAnalysisResp) Reset()         { *m = GetWeekBusinessAnalysisResp{} }
func (m *GetWeekBusinessAnalysisResp) String() string { return proto.CompactTextString(m) }
func (*GetWeekBusinessAnalysisResp) ProtoMessage()    {}
func (*GetWeekBusinessAnalysisResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{68}
}
func (m *GetWeekBusinessAnalysisResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWeekBusinessAnalysisResp.Unmarshal(m, b)
}
func (m *GetWeekBusinessAnalysisResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWeekBusinessAnalysisResp.Marshal(b, m, deterministic)
}
func (dst *GetWeekBusinessAnalysisResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWeekBusinessAnalysisResp.Merge(dst, src)
}
func (m *GetWeekBusinessAnalysisResp) XXX_Size() int {
	return xxx_messageInfo_GetWeekBusinessAnalysisResp.Size(m)
}
func (m *GetWeekBusinessAnalysisResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWeekBusinessAnalysisResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWeekBusinessAnalysisResp proto.InternalMessageInfo

func (m *GetWeekBusinessAnalysisResp) GetAnalysisList() []*BusinessAnalysis {
	if m != nil {
		return m.AnalysisList
	}
	return nil
}

func (m *GetWeekBusinessAnalysisResp) GetDiagnosisList() []*BusinessDiagnosis {
	if m != nil {
		return m.DiagnosisList
	}
	return nil
}

// 月维度经营分析
type GetMonthBusinessAnalysisReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	MonthTs              uint32   `protobuf:"varint,2,opt,name=month_ts,json=monthTs,proto3" json:"month_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMonthBusinessAnalysisReq) Reset()         { *m = GetMonthBusinessAnalysisReq{} }
func (m *GetMonthBusinessAnalysisReq) String() string { return proto.CompactTextString(m) }
func (*GetMonthBusinessAnalysisReq) ProtoMessage()    {}
func (*GetMonthBusinessAnalysisReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{69}
}
func (m *GetMonthBusinessAnalysisReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthBusinessAnalysisReq.Unmarshal(m, b)
}
func (m *GetMonthBusinessAnalysisReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthBusinessAnalysisReq.Marshal(b, m, deterministic)
}
func (dst *GetMonthBusinessAnalysisReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthBusinessAnalysisReq.Merge(dst, src)
}
func (m *GetMonthBusinessAnalysisReq) XXX_Size() int {
	return xxx_messageInfo_GetMonthBusinessAnalysisReq.Size(m)
}
func (m *GetMonthBusinessAnalysisReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthBusinessAnalysisReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthBusinessAnalysisReq proto.InternalMessageInfo

func (m *GetMonthBusinessAnalysisReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetMonthBusinessAnalysisReq) GetMonthTs() uint32 {
	if m != nil {
		return m.MonthTs
	}
	return 0
}

type GetMonthBusinessAnalysisResp struct {
	KpiList              []*BusinessAnalysis  `protobuf:"bytes,1,rep,name=kpi_list,json=kpiList,proto3" json:"kpi_list,omitempty"`
	DiagnosisList        []*BusinessDiagnosis `protobuf:"bytes,2,rep,name=diagnosis_list,json=diagnosisList,proto3" json:"diagnosis_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetMonthBusinessAnalysisResp) Reset()         { *m = GetMonthBusinessAnalysisResp{} }
func (m *GetMonthBusinessAnalysisResp) String() string { return proto.CompactTextString(m) }
func (*GetMonthBusinessAnalysisResp) ProtoMessage()    {}
func (*GetMonthBusinessAnalysisResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{70}
}
func (m *GetMonthBusinessAnalysisResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthBusinessAnalysisResp.Unmarshal(m, b)
}
func (m *GetMonthBusinessAnalysisResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthBusinessAnalysisResp.Marshal(b, m, deterministic)
}
func (dst *GetMonthBusinessAnalysisResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthBusinessAnalysisResp.Merge(dst, src)
}
func (m *GetMonthBusinessAnalysisResp) XXX_Size() int {
	return xxx_messageInfo_GetMonthBusinessAnalysisResp.Size(m)
}
func (m *GetMonthBusinessAnalysisResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthBusinessAnalysisResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthBusinessAnalysisResp proto.InternalMessageInfo

func (m *GetMonthBusinessAnalysisResp) GetKpiList() []*BusinessAnalysis {
	if m != nil {
		return m.KpiList
	}
	return nil
}

func (m *GetMonthBusinessAnalysisResp) GetDiagnosisList() []*BusinessDiagnosis {
	if m != nil {
		return m.DiagnosisList
	}
	return nil
}

// 知识标题信息
type CurriculumMessage struct {
	CurriculumTitle      string   `protobuf:"bytes,1,opt,name=curriculum_title,json=curriculumTitle,proto3" json:"curriculum_title,omitempty"`
	CurriculumDesc       string   `protobuf:"bytes,2,opt,name=curriculum_desc,json=curriculumDesc,proto3" json:"curriculum_desc,omitempty"`
	FileType             string   `protobuf:"bytes,3,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileUrl              string   `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CurriculumMessage) Reset()         { *m = CurriculumMessage{} }
func (m *CurriculumMessage) String() string { return proto.CompactTextString(m) }
func (*CurriculumMessage) ProtoMessage()    {}
func (*CurriculumMessage) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{71}
}
func (m *CurriculumMessage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CurriculumMessage.Unmarshal(m, b)
}
func (m *CurriculumMessage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CurriculumMessage.Marshal(b, m, deterministic)
}
func (dst *CurriculumMessage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CurriculumMessage.Merge(dst, src)
}
func (m *CurriculumMessage) XXX_Size() int {
	return xxx_messageInfo_CurriculumMessage.Size(m)
}
func (m *CurriculumMessage) XXX_DiscardUnknown() {
	xxx_messageInfo_CurriculumMessage.DiscardUnknown(m)
}

var xxx_messageInfo_CurriculumMessage proto.InternalMessageInfo

func (m *CurriculumMessage) GetCurriculumTitle() string {
	if m != nil {
		return m.CurriculumTitle
	}
	return ""
}

func (m *CurriculumMessage) GetCurriculumDesc() string {
	if m != nil {
		return m.CurriculumDesc
	}
	return ""
}

func (m *CurriculumMessage) GetFileType() string {
	if m != nil {
		return m.FileType
	}
	return ""
}

func (m *CurriculumMessage) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

// 获取知识标题课程列表
type GetCurriculumMessageListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurriculumMessageListReq) Reset()         { *m = GetCurriculumMessageListReq{} }
func (m *GetCurriculumMessageListReq) String() string { return proto.CompactTextString(m) }
func (*GetCurriculumMessageListReq) ProtoMessage()    {}
func (*GetCurriculumMessageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{72}
}
func (m *GetCurriculumMessageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurriculumMessageListReq.Unmarshal(m, b)
}
func (m *GetCurriculumMessageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurriculumMessageListReq.Marshal(b, m, deterministic)
}
func (dst *GetCurriculumMessageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurriculumMessageListReq.Merge(dst, src)
}
func (m *GetCurriculumMessageListReq) XXX_Size() int {
	return xxx_messageInfo_GetCurriculumMessageListReq.Size(m)
}
func (m *GetCurriculumMessageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurriculumMessageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurriculumMessageListReq proto.InternalMessageInfo

func (m *GetCurriculumMessageListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetCurriculumMessageListResp struct {
	CurriculumList       []*CurriculumMessage `protobuf:"bytes,1,rep,name=curriculum_list,json=curriculumList,proto3" json:"curriculum_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetCurriculumMessageListResp) Reset()         { *m = GetCurriculumMessageListResp{} }
func (m *GetCurriculumMessageListResp) String() string { return proto.CompactTextString(m) }
func (*GetCurriculumMessageListResp) ProtoMessage()    {}
func (*GetCurriculumMessageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{73}
}
func (m *GetCurriculumMessageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurriculumMessageListResp.Unmarshal(m, b)
}
func (m *GetCurriculumMessageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurriculumMessageListResp.Marshal(b, m, deterministic)
}
func (dst *GetCurriculumMessageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurriculumMessageListResp.Merge(dst, src)
}
func (m *GetCurriculumMessageListResp) XXX_Size() int {
	return xxx_messageInfo_GetCurriculumMessageListResp.Size(m)
}
func (m *GetCurriculumMessageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurriculumMessageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurriculumMessageListResp proto.InternalMessageInfo

func (m *GetCurriculumMessageListResp) GetCurriculumList() []*CurriculumMessage {
	if m != nil {
		return m.CurriculumList
	}
	return nil
}

// 触发定时任务
type TriggerTimerReq struct {
	TimerType            TriggerTimerReq_TimerType `protobuf:"varint,1,opt,name=timer_type,json=timerType,proto3,enum=channel_live_stats.TriggerTimerReq_TimerType" json:"timer_type,omitempty"`
	Ts                   uint32                    `protobuf:"varint,2,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *TriggerTimerReq) Reset()         { *m = TriggerTimerReq{} }
func (m *TriggerTimerReq) String() string { return proto.CompactTextString(m) }
func (*TriggerTimerReq) ProtoMessage()    {}
func (*TriggerTimerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{74}
}
func (m *TriggerTimerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerTimerReq.Unmarshal(m, b)
}
func (m *TriggerTimerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerTimerReq.Marshal(b, m, deterministic)
}
func (dst *TriggerTimerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerTimerReq.Merge(dst, src)
}
func (m *TriggerTimerReq) XXX_Size() int {
	return xxx_messageInfo_TriggerTimerReq.Size(m)
}
func (m *TriggerTimerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerTimerReq.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerTimerReq proto.InternalMessageInfo

func (m *TriggerTimerReq) GetTimerType() TriggerTimerReq_TimerType {
	if m != nil {
		return m.TimerType
	}
	return TriggerTimerReq_Timer_Type_Invalid
}

func (m *TriggerTimerReq) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type TriggerTimerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TriggerTimerResp) Reset()         { *m = TriggerTimerResp{} }
func (m *TriggerTimerResp) String() string { return proto.CompactTextString(m) }
func (*TriggerTimerResp) ProtoMessage()    {}
func (*TriggerTimerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_live_stats_132d541bcac3ce02, []int{75}
}
func (m *TriggerTimerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TriggerTimerResp.Unmarshal(m, b)
}
func (m *TriggerTimerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TriggerTimerResp.Marshal(b, m, deterministic)
}
func (dst *TriggerTimerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TriggerTimerResp.Merge(dst, src)
}
func (m *TriggerTimerResp) XXX_Size() int {
	return xxx_messageInfo_TriggerTimerResp.Size(m)
}
func (m *TriggerTimerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TriggerTimerResp.DiscardUnknown(m)
}

var xxx_messageInfo_TriggerTimerResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*AnchorBaseInfo)(nil), "channel_live_stats.AnchorBaseInfo")
	proto.RegisterType((*GetAnchorBaseInfoReq)(nil), "channel_live_stats.GetAnchorBaseInfoReq")
	proto.RegisterType((*GetAnchorBaseInfoResp)(nil), "channel_live_stats.GetAnchorBaseInfoResp")
	proto.RegisterType((*BatchGetAnchorBaseInfoReq)(nil), "channel_live_stats.BatchGetAnchorBaseInfoReq")
	proto.RegisterType((*BatchGetAnchorBaseInfoResp)(nil), "channel_live_stats.BatchGetAnchorBaseInfoResp")
	proto.RegisterType((*GetAnchorBaseInfoListReq)(nil), "channel_live_stats.GetAnchorBaseInfoListReq")
	proto.RegisterType((*GetAnchorBaseInfoListResp)(nil), "channel_live_stats.GetAnchorBaseInfoListResp")
	proto.RegisterType((*GetGuildAnchorBaseInfoReq)(nil), "channel_live_stats.GetGuildAnchorBaseInfoReq")
	proto.RegisterType((*GetGuildAnchorBaseInfoResp)(nil), "channel_live_stats.GetGuildAnchorBaseInfoResp")
	proto.RegisterType((*GuildTotalStats)(nil), "channel_live_stats.GuildTotalStats")
	proto.RegisterType((*GetGuildDailyTotalStatsReq)(nil), "channel_live_stats.GetGuildDailyTotalStatsReq")
	proto.RegisterType((*GetGuildDailyTotalStatsResp)(nil), "channel_live_stats.GetGuildDailyTotalStatsResp")
	proto.RegisterType((*GuildDailyStats)(nil), "channel_live_stats.GuildDailyStats")
	proto.RegisterType((*GetGuildDailyStatsListReq)(nil), "channel_live_stats.GetGuildDailyStatsListReq")
	proto.RegisterType((*GetGuildDailyStatsListResp)(nil), "channel_live_stats.GetGuildDailyStatsListResp")
	proto.RegisterType((*GetGuildDailyAnchorListReq)(nil), "channel_live_stats.GetGuildDailyAnchorListReq")
	proto.RegisterType((*GetAnchorTotalStatsBetweenDateReq)(nil), "channel_live_stats.GetAnchorTotalStatsBetweenDateReq")
	proto.RegisterType((*GetAnchorTotalStatsBetweenDateResp)(nil), "channel_live_stats.GetAnchorTotalStatsBetweenDateResp")
	proto.RegisterType((*GetGuildDailyAnchorListResp)(nil), "channel_live_stats.GetGuildDailyAnchorListResp")
	proto.RegisterType((*AnchorDailyStats)(nil), "channel_live_stats.AnchorDailyStats")
	proto.RegisterType((*GetAnchorDailyRecordWithDateListReq)(nil), "channel_live_stats.GetAnchorDailyRecordWithDateListReq")
	proto.RegisterType((*GetAnchorDailyRecordWithDateListResp)(nil), "channel_live_stats.GetAnchorDailyRecordWithDateListResp")
	proto.RegisterType((*BatchGetAnchorDailyRecordReq)(nil), "channel_live_stats.BatchGetAnchorDailyRecordReq")
	proto.RegisterType((*BatchGetAnchorDailyRecordResp)(nil), "channel_live_stats.BatchGetAnchorDailyRecordResp")
	proto.RegisterType((*GetAnchorDailyStatsListReq)(nil), "channel_live_stats.GetAnchorDailyStatsListReq")
	proto.RegisterType((*GetAnchorDailyStatsListResp)(nil), "channel_live_stats.GetAnchorDailyStatsListResp")
	proto.RegisterType((*AnchorWeeklyStats)(nil), "channel_live_stats.AnchorWeeklyStats")
	proto.RegisterType((*BatchGetAnchorWeeklyRecordReq)(nil), "channel_live_stats.BatchGetAnchorWeeklyRecordReq")
	proto.RegisterType((*BatchGetAnchorWeeklyRecordResp)(nil), "channel_live_stats.BatchGetAnchorWeeklyRecordResp")
	proto.RegisterType((*GetAnchorWeeklyStatsListReq)(nil), "channel_live_stats.GetAnchorWeeklyStatsListReq")
	proto.RegisterType((*GetAnchorWeeklyStatsListResp)(nil), "channel_live_stats.GetAnchorWeeklyStatsListResp")
	proto.RegisterType((*GetGuildWeeklyAnchorListReq)(nil), "channel_live_stats.GetGuildWeeklyAnchorListReq")
	proto.RegisterType((*GetGuildWeeklyAnchorListResp)(nil), "channel_live_stats.GetGuildWeeklyAnchorListResp")
	proto.RegisterType((*GuildMonthlyStats)(nil), "channel_live_stats.GuildMonthlyStats")
	proto.RegisterType((*GetGuildMonthlyStatsReq)(nil), "channel_live_stats.GetGuildMonthlyStatsReq")
	proto.RegisterType((*GetGuildMonthlyStatsResp)(nil), "channel_live_stats.GetGuildMonthlyStatsResp")
	proto.RegisterType((*AnchorMonthlyStats)(nil), "channel_live_stats.AnchorMonthlyStats")
	proto.RegisterType((*GetAnchorMonthlyStatsReq)(nil), "channel_live_stats.GetAnchorMonthlyStatsReq")
	proto.RegisterType((*GetAnchorMonthlyStatsResp)(nil), "channel_live_stats.GetAnchorMonthlyStatsResp")
	proto.RegisterType((*GetAnchorMonthlyStatsListReq)(nil), "channel_live_stats.GetAnchorMonthlyStatsListReq")
	proto.RegisterType((*GetAnchorMonthlyStatsListResp)(nil), "channel_live_stats.GetAnchorMonthlyStatsListResp")
	proto.RegisterType((*GetGuildAnchorMonthlyStatsListReq)(nil), "channel_live_stats.GetGuildAnchorMonthlyStatsListReq")
	proto.RegisterType((*GetGuildAnchorMonthlyStatsListResp)(nil), "channel_live_stats.GetGuildAnchorMonthlyStatsListResp")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsReq)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsReq")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsResp)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsResp")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsWithDateReq)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsWithDateReq")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsWithDateResp)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsWithDateResp")
	proto.RegisterType((*MonthlyTotalStats)(nil), "channel_live_stats.MonthlyTotalStats")
	proto.RegisterType((*GetAnchorMonthlyTotalStatsReq)(nil), "channel_live_stats.GetAnchorMonthlyTotalStatsReq")
	proto.RegisterType((*GetAnchorMonthlyTotalStatsResp)(nil), "channel_live_stats.GetAnchorMonthlyTotalStatsResp")
	proto.RegisterType((*UpdateGuildAnchorAgentInfoReq)(nil), "channel_live_stats.UpdateGuildAnchorAgentInfoReq")
	proto.RegisterType((*UpdateGuildAnchorAgentInfoResp)(nil), "channel_live_stats.UpdateGuildAnchorAgentInfoResp")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsByUidReq)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsByUidReq")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsByUidResp)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp")
	proto.RegisterType((*BatchGetAnchorMonthlyStatsByUidRespMonthStats)(nil), "channel_live_stats.BatchGetAnchorMonthlyStatsByUidResp.monthStats")
	proto.RegisterType((*MatchData)(nil), "channel_live_stats.MatchData")
	proto.RegisterType((*AnchorMatchData)(nil), "channel_live_stats.AnchorMatchData")
	proto.RegisterType((*GetGuildAnchorListReq)(nil), "channel_live_stats.GetGuildAnchorListReq")
	proto.RegisterType((*GetGuildAnchorListResp)(nil), "channel_live_stats.GetGuildAnchorListResp")
	proto.RegisterType((*GetAnchorMatchListReq)(nil), "channel_live_stats.GetAnchorMatchListReq")
	proto.RegisterType((*GetAnchorMatchListResp)(nil), "channel_live_stats.GetAnchorMatchListResp")
	proto.RegisterType((*GuildOperationalData)(nil), "channel_live_stats.GuildOperationalData")
	proto.RegisterType((*GetGuildOperationalCapabilitiesReq)(nil), "channel_live_stats.GetGuildOperationalCapabilitiesReq")
	proto.RegisterType((*GetGuildOperationalCapabilitiesResp)(nil), "channel_live_stats.GetGuildOperationalCapabilitiesResp")
	proto.RegisterType((*UpdateGuildOperationalCapabilitiesReq)(nil), "channel_live_stats.UpdateGuildOperationalCapabilitiesReq")
	proto.RegisterType((*UpdateGuildOperationalCapabilitiesResp)(nil), "channel_live_stats.UpdateGuildOperationalCapabilitiesResp")
	proto.RegisterType((*BusinessAnalysis)(nil), "channel_live_stats.BusinessAnalysis")
	proto.RegisterType((*BusinessDiagnosis)(nil), "channel_live_stats.BusinessDiagnosis")
	proto.RegisterType((*GetWeekBusinessAnalysisReq)(nil), "channel_live_stats.GetWeekBusinessAnalysisReq")
	proto.RegisterType((*GetWeekBusinessAnalysisResp)(nil), "channel_live_stats.GetWeekBusinessAnalysisResp")
	proto.RegisterType((*GetMonthBusinessAnalysisReq)(nil), "channel_live_stats.GetMonthBusinessAnalysisReq")
	proto.RegisterType((*GetMonthBusinessAnalysisResp)(nil), "channel_live_stats.GetMonthBusinessAnalysisResp")
	proto.RegisterType((*CurriculumMessage)(nil), "channel_live_stats.CurriculumMessage")
	proto.RegisterType((*GetCurriculumMessageListReq)(nil), "channel_live_stats.GetCurriculumMessageListReq")
	proto.RegisterType((*GetCurriculumMessageListResp)(nil), "channel_live_stats.GetCurriculumMessageListResp")
	proto.RegisterType((*TriggerTimerReq)(nil), "channel_live_stats.TriggerTimerReq")
	proto.RegisterType((*TriggerTimerResp)(nil), "channel_live_stats.TriggerTimerResp")
	proto.RegisterEnum("channel_live_stats.AnchorFlag", AnchorFlag_name, AnchorFlag_value)
	proto.RegisterEnum("channel_live_stats.KpiType", KpiType_name, KpiType_value)
	proto.RegisterEnum("channel_live_stats.GetGuildDailyAnchorListReq_Condition", GetGuildDailyAnchorListReq_Condition_name, GetGuildDailyAnchorListReq_Condition_value)
	proto.RegisterEnum("channel_live_stats.GetGuildWeeklyAnchorListReq_Condition", GetGuildWeeklyAnchorListReq_Condition_name, GetGuildWeeklyAnchorListReq_Condition_value)
	proto.RegisterEnum("channel_live_stats.GetGuildAnchorListReq_SortType", GetGuildAnchorListReq_SortType_name, GetGuildAnchorListReq_SortType_value)
	proto.RegisterEnum("channel_live_stats.TriggerTimerReq_TimerType", TriggerTimerReq_TimerType_name, TriggerTimerReq_TimerType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelLiveStatsClient is the client API for ChannelLiveStats service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelLiveStatsClient interface {
	GetAnchorBaseInfo(ctx context.Context, in *GetAnchorBaseInfoReq, opts ...grpc.CallOption) (*GetAnchorBaseInfoResp, error)
	BatchGetAnchorBaseInfo(ctx context.Context, in *BatchGetAnchorBaseInfoReq, opts ...grpc.CallOption) (*BatchGetAnchorBaseInfoResp, error)
	GetAnchorBaseInfoList(ctx context.Context, in *GetAnchorBaseInfoListReq, opts ...grpc.CallOption) (*GetAnchorBaseInfoListResp, error)
	GetGuildAnchorBaseInfo(ctx context.Context, in *GetGuildAnchorBaseInfoReq, opts ...grpc.CallOption) (*GetGuildAnchorBaseInfoResp, error)
	GetGuildDailyTotalStats(ctx context.Context, in *GetGuildDailyTotalStatsReq, opts ...grpc.CallOption) (*GetGuildDailyTotalStatsResp, error)
	GetAnchorTotalStatsBetweenDate(ctx context.Context, in *GetAnchorTotalStatsBetweenDateReq, opts ...grpc.CallOption) (*GetAnchorTotalStatsBetweenDateResp, error)
	GetGuildDailyStatsList(ctx context.Context, in *GetGuildDailyStatsListReq, opts ...grpc.CallOption) (*GetGuildDailyStatsListResp, error)
	GetAnchorDailyRecordWithDateList(ctx context.Context, in *GetAnchorDailyRecordWithDateListReq, opts ...grpc.CallOption) (*GetAnchorDailyRecordWithDateListResp, error)
	BatchGetAnchorDailyRecord(ctx context.Context, in *BatchGetAnchorDailyRecordReq, opts ...grpc.CallOption) (*BatchGetAnchorDailyRecordResp, error)
	GetGuildDailyAnchorList(ctx context.Context, in *GetGuildDailyAnchorListReq, opts ...grpc.CallOption) (*GetGuildDailyAnchorListResp, error)
	GetAnchorDailyStatsList(ctx context.Context, in *GetAnchorDailyStatsListReq, opts ...grpc.CallOption) (*GetAnchorDailyStatsListResp, error)
	BatchGetAnchorWeeklyRecord(ctx context.Context, in *BatchGetAnchorWeeklyRecordReq, opts ...grpc.CallOption) (*BatchGetAnchorWeeklyRecordResp, error)
	GetGuildWeeklyAnchorList(ctx context.Context, in *GetGuildWeeklyAnchorListReq, opts ...grpc.CallOption) (*GetGuildWeeklyAnchorListResp, error)
	GetAnchorWeeklyStatsList(ctx context.Context, in *GetAnchorWeeklyStatsListReq, opts ...grpc.CallOption) (*GetAnchorWeeklyStatsListResp, error)
	GetGuildMonthlyStats(ctx context.Context, in *GetGuildMonthlyStatsReq, opts ...grpc.CallOption) (*GetGuildMonthlyStatsResp, error)
	GetAnchorMonthlyStats(ctx context.Context, in *GetAnchorMonthlyStatsReq, opts ...grpc.CallOption) (*GetAnchorMonthlyStatsResp, error)
	GetGuildAnchorMonthlyStatsList(ctx context.Context, in *GetGuildAnchorMonthlyStatsListReq, opts ...grpc.CallOption) (*GetGuildAnchorMonthlyStatsListResp, error)
	GetAnchorMonthlyTotalStats(ctx context.Context, in *GetAnchorMonthlyTotalStatsReq, opts ...grpc.CallOption) (*GetAnchorMonthlyTotalStatsResp, error)
	BatchGetAnchorMonthlyStats(ctx context.Context, in *BatchGetAnchorMonthlyStatsReq, opts ...grpc.CallOption) (*BatchGetAnchorMonthlyStatsResp, error)
	BatchGetAnchorMonthlyStatsWithDate(ctx context.Context, in *BatchGetAnchorMonthlyStatsWithDateReq, opts ...grpc.CallOption) (*BatchGetAnchorMonthlyStatsWithDateResp, error)
	GetAnchorMonthlyStatsList(ctx context.Context, in *GetAnchorMonthlyStatsListReq, opts ...grpc.CallOption) (*GetAnchorMonthlyStatsListResp, error)
	UpdateGuildAnchorAgentInfo(ctx context.Context, in *UpdateGuildAnchorAgentInfoReq, opts ...grpc.CallOption) (*UpdateGuildAnchorAgentInfoResp, error)
	BatchGetAnchorMonthlyStatsByUid(ctx context.Context, in *BatchGetAnchorMonthlyStatsByUidReq, opts ...grpc.CallOption) (*BatchGetAnchorMonthlyStatsByUidResp, error)
	GetGuildAnchorList(ctx context.Context, in *GetGuildAnchorListReq, opts ...grpc.CallOption) (*GetGuildAnchorListResp, error)
	GetAnchorMatchList(ctx context.Context, in *GetAnchorMatchListReq, opts ...grpc.CallOption) (*GetAnchorMatchListResp, error)
	// 获取公会运营能力
	GetGuildOperationalCapabilities(ctx context.Context, in *GetGuildOperationalCapabilitiesReq, opts ...grpc.CallOption) (*GetGuildOperationalCapabilitiesResp, error)
	// 更新公会运营能力
	UpdateGuildOperationalCapabilities(ctx context.Context, in *UpdateGuildOperationalCapabilitiesReq, opts ...grpc.CallOption) (*UpdateGuildOperationalCapabilitiesResp, error)
	// 周维度经营分析
	GetWeekBusinessAnalysis(ctx context.Context, in *GetWeekBusinessAnalysisReq, opts ...grpc.CallOption) (*GetWeekBusinessAnalysisResp, error)
	// 月维度经营分析
	GetMonthBusinessAnalysis(ctx context.Context, in *GetMonthBusinessAnalysisReq, opts ...grpc.CallOption) (*GetMonthBusinessAnalysisResp, error)
	// 获取知识标题课程列表
	GetCurriculumMessageList(ctx context.Context, in *GetCurriculumMessageListReq, opts ...grpc.CallOption) (*GetCurriculumMessageListResp, error)
	// 触发定时任务
	TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error)
}

type channelLiveStatsClient struct {
	cc *grpc.ClientConn
}

func NewChannelLiveStatsClient(cc *grpc.ClientConn) ChannelLiveStatsClient {
	return &channelLiveStatsClient{cc}
}

func (c *channelLiveStatsClient) GetAnchorBaseInfo(ctx context.Context, in *GetAnchorBaseInfoReq, opts ...grpc.CallOption) (*GetAnchorBaseInfoResp, error) {
	out := new(GetAnchorBaseInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) BatchGetAnchorBaseInfo(ctx context.Context, in *BatchGetAnchorBaseInfoReq, opts ...grpc.CallOption) (*BatchGetAnchorBaseInfoResp, error) {
	out := new(BatchGetAnchorBaseInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/BatchGetAnchorBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorBaseInfoList(ctx context.Context, in *GetAnchorBaseInfoListReq, opts ...grpc.CallOption) (*GetAnchorBaseInfoListResp, error) {
	out := new(GetAnchorBaseInfoListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorBaseInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildAnchorBaseInfo(ctx context.Context, in *GetGuildAnchorBaseInfoReq, opts ...grpc.CallOption) (*GetGuildAnchorBaseInfoResp, error) {
	out := new(GetGuildAnchorBaseInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildAnchorBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildDailyTotalStats(ctx context.Context, in *GetGuildDailyTotalStatsReq, opts ...grpc.CallOption) (*GetGuildDailyTotalStatsResp, error) {
	out := new(GetGuildDailyTotalStatsResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildDailyTotalStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorTotalStatsBetweenDate(ctx context.Context, in *GetAnchorTotalStatsBetweenDateReq, opts ...grpc.CallOption) (*GetAnchorTotalStatsBetweenDateResp, error) {
	out := new(GetAnchorTotalStatsBetweenDateResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorTotalStatsBetweenDate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildDailyStatsList(ctx context.Context, in *GetGuildDailyStatsListReq, opts ...grpc.CallOption) (*GetGuildDailyStatsListResp, error) {
	out := new(GetGuildDailyStatsListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildDailyStatsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorDailyRecordWithDateList(ctx context.Context, in *GetAnchorDailyRecordWithDateListReq, opts ...grpc.CallOption) (*GetAnchorDailyRecordWithDateListResp, error) {
	out := new(GetAnchorDailyRecordWithDateListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorDailyRecordWithDateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) BatchGetAnchorDailyRecord(ctx context.Context, in *BatchGetAnchorDailyRecordReq, opts ...grpc.CallOption) (*BatchGetAnchorDailyRecordResp, error) {
	out := new(BatchGetAnchorDailyRecordResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/BatchGetAnchorDailyRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildDailyAnchorList(ctx context.Context, in *GetGuildDailyAnchorListReq, opts ...grpc.CallOption) (*GetGuildDailyAnchorListResp, error) {
	out := new(GetGuildDailyAnchorListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildDailyAnchorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorDailyStatsList(ctx context.Context, in *GetAnchorDailyStatsListReq, opts ...grpc.CallOption) (*GetAnchorDailyStatsListResp, error) {
	out := new(GetAnchorDailyStatsListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorDailyStatsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) BatchGetAnchorWeeklyRecord(ctx context.Context, in *BatchGetAnchorWeeklyRecordReq, opts ...grpc.CallOption) (*BatchGetAnchorWeeklyRecordResp, error) {
	out := new(BatchGetAnchorWeeklyRecordResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/BatchGetAnchorWeeklyRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildWeeklyAnchorList(ctx context.Context, in *GetGuildWeeklyAnchorListReq, opts ...grpc.CallOption) (*GetGuildWeeklyAnchorListResp, error) {
	out := new(GetGuildWeeklyAnchorListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildWeeklyAnchorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorWeeklyStatsList(ctx context.Context, in *GetAnchorWeeklyStatsListReq, opts ...grpc.CallOption) (*GetAnchorWeeklyStatsListResp, error) {
	out := new(GetAnchorWeeklyStatsListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorWeeklyStatsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildMonthlyStats(ctx context.Context, in *GetGuildMonthlyStatsReq, opts ...grpc.CallOption) (*GetGuildMonthlyStatsResp, error) {
	out := new(GetGuildMonthlyStatsResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildMonthlyStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorMonthlyStats(ctx context.Context, in *GetAnchorMonthlyStatsReq, opts ...grpc.CallOption) (*GetAnchorMonthlyStatsResp, error) {
	out := new(GetAnchorMonthlyStatsResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorMonthlyStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildAnchorMonthlyStatsList(ctx context.Context, in *GetGuildAnchorMonthlyStatsListReq, opts ...grpc.CallOption) (*GetGuildAnchorMonthlyStatsListResp, error) {
	out := new(GetGuildAnchorMonthlyStatsListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildAnchorMonthlyStatsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorMonthlyTotalStats(ctx context.Context, in *GetAnchorMonthlyTotalStatsReq, opts ...grpc.CallOption) (*GetAnchorMonthlyTotalStatsResp, error) {
	out := new(GetAnchorMonthlyTotalStatsResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorMonthlyTotalStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) BatchGetAnchorMonthlyStats(ctx context.Context, in *BatchGetAnchorMonthlyStatsReq, opts ...grpc.CallOption) (*BatchGetAnchorMonthlyStatsResp, error) {
	out := new(BatchGetAnchorMonthlyStatsResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/BatchGetAnchorMonthlyStats", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) BatchGetAnchorMonthlyStatsWithDate(ctx context.Context, in *BatchGetAnchorMonthlyStatsWithDateReq, opts ...grpc.CallOption) (*BatchGetAnchorMonthlyStatsWithDateResp, error) {
	out := new(BatchGetAnchorMonthlyStatsWithDateResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/BatchGetAnchorMonthlyStatsWithDate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorMonthlyStatsList(ctx context.Context, in *GetAnchorMonthlyStatsListReq, opts ...grpc.CallOption) (*GetAnchorMonthlyStatsListResp, error) {
	out := new(GetAnchorMonthlyStatsListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorMonthlyStatsList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) UpdateGuildAnchorAgentInfo(ctx context.Context, in *UpdateGuildAnchorAgentInfoReq, opts ...grpc.CallOption) (*UpdateGuildAnchorAgentInfoResp, error) {
	out := new(UpdateGuildAnchorAgentInfoResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/UpdateGuildAnchorAgentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) BatchGetAnchorMonthlyStatsByUid(ctx context.Context, in *BatchGetAnchorMonthlyStatsByUidReq, opts ...grpc.CallOption) (*BatchGetAnchorMonthlyStatsByUidResp, error) {
	out := new(BatchGetAnchorMonthlyStatsByUidResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/BatchGetAnchorMonthlyStatsByUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildAnchorList(ctx context.Context, in *GetGuildAnchorListReq, opts ...grpc.CallOption) (*GetGuildAnchorListResp, error) {
	out := new(GetGuildAnchorListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildAnchorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetAnchorMatchList(ctx context.Context, in *GetAnchorMatchListReq, opts ...grpc.CallOption) (*GetAnchorMatchListResp, error) {
	out := new(GetAnchorMatchListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetAnchorMatchList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetGuildOperationalCapabilities(ctx context.Context, in *GetGuildOperationalCapabilitiesReq, opts ...grpc.CallOption) (*GetGuildOperationalCapabilitiesResp, error) {
	out := new(GetGuildOperationalCapabilitiesResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetGuildOperationalCapabilities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) UpdateGuildOperationalCapabilities(ctx context.Context, in *UpdateGuildOperationalCapabilitiesReq, opts ...grpc.CallOption) (*UpdateGuildOperationalCapabilitiesResp, error) {
	out := new(UpdateGuildOperationalCapabilitiesResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/UpdateGuildOperationalCapabilities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetWeekBusinessAnalysis(ctx context.Context, in *GetWeekBusinessAnalysisReq, opts ...grpc.CallOption) (*GetWeekBusinessAnalysisResp, error) {
	out := new(GetWeekBusinessAnalysisResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetWeekBusinessAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetMonthBusinessAnalysis(ctx context.Context, in *GetMonthBusinessAnalysisReq, opts ...grpc.CallOption) (*GetMonthBusinessAnalysisResp, error) {
	out := new(GetMonthBusinessAnalysisResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetMonthBusinessAnalysis", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) GetCurriculumMessageList(ctx context.Context, in *GetCurriculumMessageListReq, opts ...grpc.CallOption) (*GetCurriculumMessageListResp, error) {
	out := new(GetCurriculumMessageListResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/GetCurriculumMessageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelLiveStatsClient) TriggerTimer(ctx context.Context, in *TriggerTimerReq, opts ...grpc.CallOption) (*TriggerTimerResp, error) {
	out := new(TriggerTimerResp)
	err := c.cc.Invoke(ctx, "/channel_live_stats.ChannelLiveStats/TriggerTimer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelLiveStatsServer is the server API for ChannelLiveStats service.
type ChannelLiveStatsServer interface {
	GetAnchorBaseInfo(context.Context, *GetAnchorBaseInfoReq) (*GetAnchorBaseInfoResp, error)
	BatchGetAnchorBaseInfo(context.Context, *BatchGetAnchorBaseInfoReq) (*BatchGetAnchorBaseInfoResp, error)
	GetAnchorBaseInfoList(context.Context, *GetAnchorBaseInfoListReq) (*GetAnchorBaseInfoListResp, error)
	GetGuildAnchorBaseInfo(context.Context, *GetGuildAnchorBaseInfoReq) (*GetGuildAnchorBaseInfoResp, error)
	GetGuildDailyTotalStats(context.Context, *GetGuildDailyTotalStatsReq) (*GetGuildDailyTotalStatsResp, error)
	GetAnchorTotalStatsBetweenDate(context.Context, *GetAnchorTotalStatsBetweenDateReq) (*GetAnchorTotalStatsBetweenDateResp, error)
	GetGuildDailyStatsList(context.Context, *GetGuildDailyStatsListReq) (*GetGuildDailyStatsListResp, error)
	GetAnchorDailyRecordWithDateList(context.Context, *GetAnchorDailyRecordWithDateListReq) (*GetAnchorDailyRecordWithDateListResp, error)
	BatchGetAnchorDailyRecord(context.Context, *BatchGetAnchorDailyRecordReq) (*BatchGetAnchorDailyRecordResp, error)
	GetGuildDailyAnchorList(context.Context, *GetGuildDailyAnchorListReq) (*GetGuildDailyAnchorListResp, error)
	GetAnchorDailyStatsList(context.Context, *GetAnchorDailyStatsListReq) (*GetAnchorDailyStatsListResp, error)
	BatchGetAnchorWeeklyRecord(context.Context, *BatchGetAnchorWeeklyRecordReq) (*BatchGetAnchorWeeklyRecordResp, error)
	GetGuildWeeklyAnchorList(context.Context, *GetGuildWeeklyAnchorListReq) (*GetGuildWeeklyAnchorListResp, error)
	GetAnchorWeeklyStatsList(context.Context, *GetAnchorWeeklyStatsListReq) (*GetAnchorWeeklyStatsListResp, error)
	GetGuildMonthlyStats(context.Context, *GetGuildMonthlyStatsReq) (*GetGuildMonthlyStatsResp, error)
	GetAnchorMonthlyStats(context.Context, *GetAnchorMonthlyStatsReq) (*GetAnchorMonthlyStatsResp, error)
	GetGuildAnchorMonthlyStatsList(context.Context, *GetGuildAnchorMonthlyStatsListReq) (*GetGuildAnchorMonthlyStatsListResp, error)
	GetAnchorMonthlyTotalStats(context.Context, *GetAnchorMonthlyTotalStatsReq) (*GetAnchorMonthlyTotalStatsResp, error)
	BatchGetAnchorMonthlyStats(context.Context, *BatchGetAnchorMonthlyStatsReq) (*BatchGetAnchorMonthlyStatsResp, error)
	BatchGetAnchorMonthlyStatsWithDate(context.Context, *BatchGetAnchorMonthlyStatsWithDateReq) (*BatchGetAnchorMonthlyStatsWithDateResp, error)
	GetAnchorMonthlyStatsList(context.Context, *GetAnchorMonthlyStatsListReq) (*GetAnchorMonthlyStatsListResp, error)
	UpdateGuildAnchorAgentInfo(context.Context, *UpdateGuildAnchorAgentInfoReq) (*UpdateGuildAnchorAgentInfoResp, error)
	BatchGetAnchorMonthlyStatsByUid(context.Context, *BatchGetAnchorMonthlyStatsByUidReq) (*BatchGetAnchorMonthlyStatsByUidResp, error)
	GetGuildAnchorList(context.Context, *GetGuildAnchorListReq) (*GetGuildAnchorListResp, error)
	GetAnchorMatchList(context.Context, *GetAnchorMatchListReq) (*GetAnchorMatchListResp, error)
	// 获取公会运营能力
	GetGuildOperationalCapabilities(context.Context, *GetGuildOperationalCapabilitiesReq) (*GetGuildOperationalCapabilitiesResp, error)
	// 更新公会运营能力
	UpdateGuildOperationalCapabilities(context.Context, *UpdateGuildOperationalCapabilitiesReq) (*UpdateGuildOperationalCapabilitiesResp, error)
	// 周维度经营分析
	GetWeekBusinessAnalysis(context.Context, *GetWeekBusinessAnalysisReq) (*GetWeekBusinessAnalysisResp, error)
	// 月维度经营分析
	GetMonthBusinessAnalysis(context.Context, *GetMonthBusinessAnalysisReq) (*GetMonthBusinessAnalysisResp, error)
	// 获取知识标题课程列表
	GetCurriculumMessageList(context.Context, *GetCurriculumMessageListReq) (*GetCurriculumMessageListResp, error)
	// 触发定时任务
	TriggerTimer(context.Context, *TriggerTimerReq) (*TriggerTimerResp, error)
}

func RegisterChannelLiveStatsServer(s *grpc.Server, srv ChannelLiveStatsServer) {
	s.RegisterService(&_ChannelLiveStats_serviceDesc, srv)
}

func _ChannelLiveStats_GetAnchorBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorBaseInfo(ctx, req.(*GetAnchorBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_BatchGetAnchorBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnchorBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/BatchGetAnchorBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorBaseInfo(ctx, req.(*BatchGetAnchorBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorBaseInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorBaseInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorBaseInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorBaseInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorBaseInfoList(ctx, req.(*GetAnchorBaseInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildAnchorBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildAnchorBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildAnchorBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildAnchorBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildAnchorBaseInfo(ctx, req.(*GetGuildAnchorBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildDailyTotalStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDailyTotalStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildDailyTotalStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildDailyTotalStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildDailyTotalStats(ctx, req.(*GetGuildDailyTotalStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorTotalStatsBetweenDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorTotalStatsBetweenDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorTotalStatsBetweenDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorTotalStatsBetweenDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorTotalStatsBetweenDate(ctx, req.(*GetAnchorTotalStatsBetweenDateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildDailyStatsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDailyStatsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildDailyStatsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildDailyStatsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildDailyStatsList(ctx, req.(*GetGuildDailyStatsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorDailyRecordWithDateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorDailyRecordWithDateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorDailyRecordWithDateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorDailyRecordWithDateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorDailyRecordWithDateList(ctx, req.(*GetAnchorDailyRecordWithDateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_BatchGetAnchorDailyRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnchorDailyRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorDailyRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/BatchGetAnchorDailyRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorDailyRecord(ctx, req.(*BatchGetAnchorDailyRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildDailyAnchorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDailyAnchorListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildDailyAnchorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildDailyAnchorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildDailyAnchorList(ctx, req.(*GetGuildDailyAnchorListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorDailyStatsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorDailyStatsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorDailyStatsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorDailyStatsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorDailyStatsList(ctx, req.(*GetAnchorDailyStatsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_BatchGetAnchorWeeklyRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnchorWeeklyRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorWeeklyRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/BatchGetAnchorWeeklyRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorWeeklyRecord(ctx, req.(*BatchGetAnchorWeeklyRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildWeeklyAnchorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildWeeklyAnchorListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildWeeklyAnchorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildWeeklyAnchorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildWeeklyAnchorList(ctx, req.(*GetGuildWeeklyAnchorListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorWeeklyStatsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorWeeklyStatsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorWeeklyStatsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorWeeklyStatsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorWeeklyStatsList(ctx, req.(*GetAnchorWeeklyStatsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildMonthlyStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildMonthlyStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildMonthlyStats(ctx, req.(*GetGuildMonthlyStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorMonthlyStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorMonthlyStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorMonthlyStats(ctx, req.(*GetAnchorMonthlyStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildAnchorMonthlyStatsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildAnchorMonthlyStatsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildAnchorMonthlyStatsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildAnchorMonthlyStatsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildAnchorMonthlyStatsList(ctx, req.(*GetGuildAnchorMonthlyStatsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorMonthlyTotalStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorMonthlyTotalStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorMonthlyTotalStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorMonthlyTotalStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorMonthlyTotalStats(ctx, req.(*GetAnchorMonthlyTotalStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_BatchGetAnchorMonthlyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnchorMonthlyStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorMonthlyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/BatchGetAnchorMonthlyStats",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorMonthlyStats(ctx, req.(*BatchGetAnchorMonthlyStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_BatchGetAnchorMonthlyStatsWithDate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnchorMonthlyStatsWithDateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorMonthlyStatsWithDate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/BatchGetAnchorMonthlyStatsWithDate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorMonthlyStatsWithDate(ctx, req.(*BatchGetAnchorMonthlyStatsWithDateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorMonthlyStatsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorMonthlyStatsListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorMonthlyStatsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorMonthlyStatsList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorMonthlyStatsList(ctx, req.(*GetAnchorMonthlyStatsListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_UpdateGuildAnchorAgentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildAnchorAgentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).UpdateGuildAnchorAgentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/UpdateGuildAnchorAgentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).UpdateGuildAnchorAgentInfo(ctx, req.(*UpdateGuildAnchorAgentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_BatchGetAnchorMonthlyStatsByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetAnchorMonthlyStatsByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorMonthlyStatsByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/BatchGetAnchorMonthlyStatsByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).BatchGetAnchorMonthlyStatsByUid(ctx, req.(*BatchGetAnchorMonthlyStatsByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildAnchorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildAnchorListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildAnchorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildAnchorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildAnchorList(ctx, req.(*GetGuildAnchorListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetAnchorMatchList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorMatchListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetAnchorMatchList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetAnchorMatchList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetAnchorMatchList(ctx, req.(*GetAnchorMatchListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetGuildOperationalCapabilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildOperationalCapabilitiesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetGuildOperationalCapabilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetGuildOperationalCapabilities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetGuildOperationalCapabilities(ctx, req.(*GetGuildOperationalCapabilitiesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_UpdateGuildOperationalCapabilities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildOperationalCapabilitiesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).UpdateGuildOperationalCapabilities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/UpdateGuildOperationalCapabilities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).UpdateGuildOperationalCapabilities(ctx, req.(*UpdateGuildOperationalCapabilitiesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetWeekBusinessAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeekBusinessAnalysisReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetWeekBusinessAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetWeekBusinessAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetWeekBusinessAnalysis(ctx, req.(*GetWeekBusinessAnalysisReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetMonthBusinessAnalysis_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthBusinessAnalysisReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetMonthBusinessAnalysis(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetMonthBusinessAnalysis",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetMonthBusinessAnalysis(ctx, req.(*GetMonthBusinessAnalysisReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_GetCurriculumMessageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurriculumMessageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).GetCurriculumMessageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/GetCurriculumMessageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).GetCurriculumMessageList(ctx, req.(*GetCurriculumMessageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelLiveStats_TriggerTimer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TriggerTimerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelLiveStatsServer).TriggerTimer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_live_stats.ChannelLiveStats/TriggerTimer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelLiveStatsServer).TriggerTimer(ctx, req.(*TriggerTimerReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelLiveStats_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_live_stats.ChannelLiveStats",
	HandlerType: (*ChannelLiveStatsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAnchorBaseInfo",
			Handler:    _ChannelLiveStats_GetAnchorBaseInfo_Handler,
		},
		{
			MethodName: "BatchGetAnchorBaseInfo",
			Handler:    _ChannelLiveStats_BatchGetAnchorBaseInfo_Handler,
		},
		{
			MethodName: "GetAnchorBaseInfoList",
			Handler:    _ChannelLiveStats_GetAnchorBaseInfoList_Handler,
		},
		{
			MethodName: "GetGuildAnchorBaseInfo",
			Handler:    _ChannelLiveStats_GetGuildAnchorBaseInfo_Handler,
		},
		{
			MethodName: "GetGuildDailyTotalStats",
			Handler:    _ChannelLiveStats_GetGuildDailyTotalStats_Handler,
		},
		{
			MethodName: "GetAnchorTotalStatsBetweenDate",
			Handler:    _ChannelLiveStats_GetAnchorTotalStatsBetweenDate_Handler,
		},
		{
			MethodName: "GetGuildDailyStatsList",
			Handler:    _ChannelLiveStats_GetGuildDailyStatsList_Handler,
		},
		{
			MethodName: "GetAnchorDailyRecordWithDateList",
			Handler:    _ChannelLiveStats_GetAnchorDailyRecordWithDateList_Handler,
		},
		{
			MethodName: "BatchGetAnchorDailyRecord",
			Handler:    _ChannelLiveStats_BatchGetAnchorDailyRecord_Handler,
		},
		{
			MethodName: "GetGuildDailyAnchorList",
			Handler:    _ChannelLiveStats_GetGuildDailyAnchorList_Handler,
		},
		{
			MethodName: "GetAnchorDailyStatsList",
			Handler:    _ChannelLiveStats_GetAnchorDailyStatsList_Handler,
		},
		{
			MethodName: "BatchGetAnchorWeeklyRecord",
			Handler:    _ChannelLiveStats_BatchGetAnchorWeeklyRecord_Handler,
		},
		{
			MethodName: "GetGuildWeeklyAnchorList",
			Handler:    _ChannelLiveStats_GetGuildWeeklyAnchorList_Handler,
		},
		{
			MethodName: "GetAnchorWeeklyStatsList",
			Handler:    _ChannelLiveStats_GetAnchorWeeklyStatsList_Handler,
		},
		{
			MethodName: "GetGuildMonthlyStats",
			Handler:    _ChannelLiveStats_GetGuildMonthlyStats_Handler,
		},
		{
			MethodName: "GetAnchorMonthlyStats",
			Handler:    _ChannelLiveStats_GetAnchorMonthlyStats_Handler,
		},
		{
			MethodName: "GetGuildAnchorMonthlyStatsList",
			Handler:    _ChannelLiveStats_GetGuildAnchorMonthlyStatsList_Handler,
		},
		{
			MethodName: "GetAnchorMonthlyTotalStats",
			Handler:    _ChannelLiveStats_GetAnchorMonthlyTotalStats_Handler,
		},
		{
			MethodName: "BatchGetAnchorMonthlyStats",
			Handler:    _ChannelLiveStats_BatchGetAnchorMonthlyStats_Handler,
		},
		{
			MethodName: "BatchGetAnchorMonthlyStatsWithDate",
			Handler:    _ChannelLiveStats_BatchGetAnchorMonthlyStatsWithDate_Handler,
		},
		{
			MethodName: "GetAnchorMonthlyStatsList",
			Handler:    _ChannelLiveStats_GetAnchorMonthlyStatsList_Handler,
		},
		{
			MethodName: "UpdateGuildAnchorAgentInfo",
			Handler:    _ChannelLiveStats_UpdateGuildAnchorAgentInfo_Handler,
		},
		{
			MethodName: "BatchGetAnchorMonthlyStatsByUid",
			Handler:    _ChannelLiveStats_BatchGetAnchorMonthlyStatsByUid_Handler,
		},
		{
			MethodName: "GetGuildAnchorList",
			Handler:    _ChannelLiveStats_GetGuildAnchorList_Handler,
		},
		{
			MethodName: "GetAnchorMatchList",
			Handler:    _ChannelLiveStats_GetAnchorMatchList_Handler,
		},
		{
			MethodName: "GetGuildOperationalCapabilities",
			Handler:    _ChannelLiveStats_GetGuildOperationalCapabilities_Handler,
		},
		{
			MethodName: "UpdateGuildOperationalCapabilities",
			Handler:    _ChannelLiveStats_UpdateGuildOperationalCapabilities_Handler,
		},
		{
			MethodName: "GetWeekBusinessAnalysis",
			Handler:    _ChannelLiveStats_GetWeekBusinessAnalysis_Handler,
		},
		{
			MethodName: "GetMonthBusinessAnalysis",
			Handler:    _ChannelLiveStats_GetMonthBusinessAnalysis_Handler,
		},
		{
			MethodName: "GetCurriculumMessageList",
			Handler:    _ChannelLiveStats_GetCurriculumMessageList_Handler,
		},
		{
			MethodName: "TriggerTimer",
			Handler:    _ChannelLiveStats_TriggerTimer_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-live-stats/channel-live-stats.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-live-stats/channel-live-stats.proto", fileDescriptor_channel_live_stats_132d541bcac3ce02)
}

var fileDescriptor_channel_live_stats_132d541bcac3ce02 = []byte{
	// 4698 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3c, 0x4b, 0x70, 0x1c, 0x49,
	0x56, 0xee, 0x8f, 0xd4, 0xdd, 0x4f, 0xea, 0x56, 0x75, 0x4a, 0xb6, 0xa4, 0xf6, 0xbf, 0xfc, 0x41,
	0xf6, 0x8c, 0xe5, 0x0f, 0x8c, 0x67, 0x77, 0x98, 0x60, 0x42, 0x92, 0x3f, 0xa3, 0x18, 0xdb, 0xe3,
	0x6d, 0xcb, 0x36, 0xc3, 0x06, 0x51, 0x5b, 0xee, 0x2e, 0xb5, 0x13, 0x75, 0x57, 0x95, 0x3b, 0xab,
	0xa5, 0xd5, 0x06, 0x11, 0x0b, 0x04, 0x44, 0xc0, 0x0d, 0x02, 0x22, 0xf6, 0x02, 0xc1, 0x19, 0x58,
	0x38, 0x6d, 0x70, 0xe2, 0xb8, 0x47, 0xb8, 0x10, 0xc1, 0x8d, 0xcb, 0x1c, 0x08, 0x82, 0x33, 0x07,
	0x6e, 0x04, 0x91, 0x2f, 0xb3, 0xaa, 0xb2, 0xbe, 0x5d, 0x2d, 0x0f, 0xfb, 0x39, 0xa9, 0xeb, 0xbd,
	0x97, 0x99, 0x2f, 0x5f, 0xbe, 0x7c, 0xbf, 0xcc, 0x14, 0x7c, 0xe2, 0x79, 0xb7, 0xdf, 0x4d, 0x68,
	0xef, 0x80, 0xd1, 0xe1, 0xa1, 0x35, 0xbe, 0xdd, 0x7b, 0x6b, 0xda, 0xb6, 0x35, 0xbc, 0x35, 0xa4,
	0x87, 0xd6, 0x2d, 0xe6, 0x99, 0x1e, 0x4b, 0x01, 0x6d, 0xba, 0x63, 0xc7, 0x73, 0x08, 0x91, 0x18,
	0x83, 0x63, 0x0c, 0xc4, 0xe8, 0xff, 0x5d, 0x82, 0xd6, 0x96, 0xdd, 0x7b, 0xeb, 0x8c, 0xb7, 0x4d,
	0x66, 0xed, 0xda, 0xfb, 0x0e, 0xd1, 0xa0, 0x32, 0xa1, 0xfd, 0xb5, 0xd2, 0xa5, 0xd2, 0x46, 0xb3,
	0xcb, 0x7f, 0x12, 0x1d, 0x9a, 0x8c, 0x0e, 0x6c, 0x63, 0x30, 0xa1, 0xc3, 0xbe, 0x41, 0xfb, 0x6b,
	0x65, 0xc4, 0x2d, 0x70, 0xe0, 0x63, 0x0e, 0xdb, 0xed, 0x93, 0x4b, 0xb0, 0x88, 0xdd, 0x8e, 0x1d,
	0x67, 0xc4, 0x49, 0x2a, 0x48, 0x02, 0x1c, 0xd6, 0x75, 0x9c, 0x91, 0xa4, 0x30, 0x99, 0x27, 0x46,
	0x37, 0xbd, 0xb5, 0xaa, 0xa4, 0x30, 0x99, 0xf7, 0x84, 0x1e, 0x5a, 0x5b, 0x1e, 0x39, 0x0b, 0x0d,
	0x73, 0x60, 0xd9, 0x9e, 0xc1, 0xc7, 0x9f, 0x43, 0x74, 0x1d, 0x01, 0x2f, 0x05, 0x13, 0xfb, 0x74,
	0xec, 0xb7, 0xf7, 0xd8, 0xda, 0xbc, 0x60, 0x02, 0x81, 0xbc, 0x83, 0x3d, 0x46, 0xae, 0xc3, 0x52,
	0x64, 0x8e, 0xb4, 0xbf, 0x56, 0x43, 0xaa, 0xa6, 0x04, 0x73, 0xba, 0xdd, 0xbe, 0xbe, 0x01, 0x2b,
	0x8f, 0x2d, 0x2f, 0x3a, 0xef, 0xae, 0xf5, 0x2e, 0x39, 0x75, 0xfd, 0x4b, 0x38, 0x9d, 0x42, 0xc9,
	0x5c, 0x72, 0x1f, 0xaa, 0xd4, 0xde, 0x77, 0x90, 0x76, 0xe1, 0x9e, 0xbe, 0x99, 0x94, 0xed, 0x66,
	0xac, 0x15, 0xd2, 0xeb, 0xf7, 0x61, 0x7d, 0xdb, 0xf4, 0x7a, 0x6f, 0x53, 0xc7, 0x5f, 0x87, 0xfa,
	0x84, 0xf6, 0x8d, 0x21, 0x65, 0xde, 0x5a, 0xe9, 0x52, 0x65, 0xa3, 0xd9, 0xad, 0x4d, 0x68, 0xff,
	0x09, 0x65, 0x9e, 0xfe, 0xdb, 0xd0, 0xc9, 0x6a, 0xc7, 0x5c, 0xf2, 0x19, 0x34, 0x78, 0xef, 0x61,
	0xcb, 0x62, 0x2c, 0xd5, 0x79, 0x23, 0xec, 0xfe, 0x9f, 0x4b, 0xb0, 0x96, 0xe8, 0x9a, 0x63, 0x24,
	0x5b, 0xc1, 0xd2, 0x0b, 0xd9, 0xd4, 0x06, 0x72, 0xd9, 0xcf, 0xc0, 0xbc, 0xb3, 0xbf, 0xcf, 0x2c,
	0x4f, 0xea, 0x84, 0xfc, 0x22, 0x2b, 0x30, 0x37, 0xa4, 0x23, 0xea, 0x49, 0x3d, 0x10, 0x1f, 0xe4,
	0x22, 0x2c, 0x98, 0x38, 0x82, 0xb1, 0x3f, 0x34, 0x07, 0xbe, 0x06, 0x08, 0xd0, 0xa3, 0xa1, 0x39,
	0xc8, 0xd7, 0x80, 0x1b, 0xd0, 0x1e, 0x51, 0xdb, 0x48, 0xd3, 0x82, 0xd6, 0x88, 0xda, 0x8f, 0x42,
	0x45, 0xd0, 0x8f, 0x61, 0x3d, 0x63, 0x36, 0xdf, 0x80, 0xb0, 0x38, 0x97, 0x9e, 0xe3, 0x99, 0x43,
	0xa3, 0x67, 0xfb, 0xf3, 0xae, 0x23, 0x60, 0xc7, 0xf6, 0x74, 0x0b, 0x87, 0xc6, 0x6d, 0x91, 0xba,
	0xc0, 0x59, 0x92, 0x94, 0xba, 0x57, 0x0e, 0xb7, 0x5d, 0x44, 0x18, 0x95, 0xa8, 0x30, 0xf4, 0x3d,
	0xe8, 0x64, 0x0d, 0xf3, 0x1e, 0xda, 0xf9, 0x9f, 0x65, 0x58, 0xc2, 0x3e, 0xf7, 0xf8, 0x74, 0x5e,
	0x70, 0x42, 0xbe, 0xa9, 0xc4, 0x96, 0x15, 0x2b, 0xc7, 0xe7, 0x2c, 0x58, 0x6f, 0x72, 0xb0, 0xe8,
	0x66, 0xc7, 0xf6, 0xc8, 0x26, 0x2c, 0x0b, 0xa9, 0x48, 0x42, 0x6a, 0xf7, 0x9c, 0x91, 0x25, 0x27,
	0xd4, 0x46, 0x94, 0x20, 0xde, 0x45, 0x04, 0xb9, 0x09, 0x6d, 0x29, 0x45, 0xc9, 0xdc, 0xbe, 0x65,
	0xc9, 0x69, 0x2e, 0x09, 0x69, 0x0a, 0xf8, 0x23, 0xcb, 0x22, 0x1b, 0xa0, 0x1d, 0x9a, 0x43, 0xda,
	0x57, 0x99, 0x10, 0xda, 0xd3, 0x42, 0x78, 0xc8, 0xc5, 0x07, 0x40, 0x6c, 0xeb, 0xc8, 0x30, 0xfb,
	0x11, 0x5a, 0xa1, 0x4a, 0x4b, 0xb6, 0x75, 0xb4, 0xd5, 0x57, 0x88, 0xef, 0xc0, 0x8a, 0x24, 0x3a,
	0xb0, 0xe9, 0xe0, 0xad, 0xe7, 0xf3, 0x2c, 0x94, 0x8a, 0x08, 0xdc, 0x17, 0x88, 0x92, 0x4c, 0x2b,
	0x16, 0xc6, 0x3d, 0x18, 0x20, 0xcb, 0x51, 0x0b, 0xf3, 0xfc, 0x60, 0xc0, 0x19, 0xbe, 0x08, 0x0b,
	0x87, 0x74, 0xec, 0x4d, 0x4c, 0x31, 0xad, 0xba, 0xd0, 0x74, 0x09, 0x7a, 0x64, 0x59, 0xfa, 0xbb,
	0x70, 0xfd, 0x1e, 0x98, 0x74, 0x78, 0x1c, 0x0a, 0x7c, 0x8a, 0x9e, 0x9c, 0x07, 0x78, 0x63, 0x0d,
	0xa8, 0x6d, 0xf4, 0x4d, 0xcf, 0x97, 0x6e, 0x03, 0x21, 0x0f, 0x4c, 0xcf, 0xe2, 0x2d, 0x2d, 0xbb,
	0x2f, 0x90, 0x42, 0x98, 0x35, 0xcb, 0xee, 0x73, 0x94, 0xfe, 0x9b, 0x70, 0x36, 0x73, 0x48, 0xe6,
	0x92, 0x6f, 0xc3, 0x1c, 0x6a, 0x86, 0x54, 0x9a, 0x2b, 0x69, 0x4a, 0x13, 0xd3, 0x8d, 0xae, 0x68,
	0xa1, 0x53, 0xa9, 0x35, 0xd8, 0xad, 0xd0, 0x9a, 0x93, 0xf7, 0xc6, 0xf5, 0x9e, 0xb3, 0x6f, 0x78,
	0x34, 0x50, 0x9f, 0x3a, 0x07, 0xec, 0xd1, 0x91, 0xa5, 0xff, 0x65, 0x29, 0xdc, 0x5f, 0xe1, 0x70,
	0x05, 0x2c, 0xd5, 0x89, 0xe5, 0xc6, 0x6d, 0x19, 0xd2, 0x49, 0x8d, 0x13, 0x1f, 0xa1, 0x85, 0x9b,
	0x53, 0x2c, 0x9c, 0xfe, 0xbd, 0xd8, 0xb2, 0x2a, 0xdc, 0x31, 0x97, 0x6c, 0x03, 0xe0, 0x14, 0x55,
	0xd3, 0x93, 0x2d, 0x99, 0xb0, 0x83, 0x6e, 0x83, 0xf9, 0xfd, 0xe8, 0x3f, 0x2d, 0xc5, 0x86, 0x10,
	0xea, 0x5c, 0x40, 0x02, 0x04, 0xaa, 0xca, 0xdc, 0xf1, 0x37, 0x39, 0x07, 0x8d, 0x9e, 0x63, 0xf7,
	0xa9, 0x47, 0x1d, 0x5b, 0xce, 0x3b, 0x04, 0xcc, 0x34, 0xf3, 0x5b, 0xd0, 0xd8, 0x09, 0x1a, 0xd6,
	0xa1, 0xca, 0x2d, 0xb1, 0x76, 0x8a, 0x34, 0x60, 0xee, 0x15, 0xdf, 0xa1, 0x5a, 0x89, 0x2c, 0x40,
	0x6d, 0xd7, 0xc6, 0xed, 0xaa, 0x95, 0xf5, 0x1f, 0x95, 0xe0, 0x72, 0x60, 0xa2, 0x43, 0x1d, 0xd8,
	0xb6, 0xbc, 0x23, 0xcb, 0xc2, 0x25, 0xe1, 0xb3, 0x39, 0x0f, 0xd2, 0x3b, 0x18, 0xa1, 0x5f, 0x6e,
	0x08, 0x08, 0xf7, 0x08, 0xea, 0x64, 0xcb, 0x79, 0xcb, 0x5d, 0xc9, 0x5b, 0xee, 0x6a, 0x74, 0x9b,
	0xfc, 0x5e, 0x09, 0xf4, 0x69, 0x9c, 0x31, 0x37, 0xcb, 0xdc, 0x95, 0x66, 0x32, 0x77, 0xe5, 0x54,
	0x73, 0xa7, 0xff, 0x46, 0x6c, 0xa7, 0xaa, 0x4b, 0xcc, 0x5c, 0xc5, 0x8d, 0x2a, 0x91, 0x82, 0x14,
	0x14, 0xea, 0xc8, 0x3f, 0xd4, 0x41, 0x13, 0x6d, 0x94, 0x1d, 0x99, 0x8c, 0xeb, 0xd2, 0x14, 0xe2,
	0x0a, 0x34, 0xa3, 0x13, 0x12, 0xa2, 0x5b, 0x34, 0xd5, 0xb9, 0x5c, 0x84, 0x05, 0x75, 0x16, 0xd2,
	0x8f, 0xf7, 0x42, 0x7b, 0xfd, 0x21, 0x10, 0xd4, 0x66, 0x61, 0xb4, 0x47, 0xd4, 0x9e, 0x78, 0x16,
	0x93, 0xfa, 0xa2, 0x71, 0x0c, 0xea, 0xc4, 0x53, 0x01, 0xe7, 0x91, 0x21, 0x65, 0x92, 0xb6, 0x6f,
	0x1e, 0xa3, 0xf9, 0xad, 0x77, 0x81, 0x32, 0xa4, 0x7a, 0x60, 0x1e, 0x73, 0xb3, 0xdb, 0x37, 0x8f,
	0x0d, 0xb3, 0xe7, 0xf1, 0x5e, 0xf7, 0x4d, 0x9b, 0xf9, 0x66, 0xb7, 0x6f, 0x1e, 0x6f, 0x21, 0xf4,
	0x91, 0x69, 0x33, 0x72, 0x01, 0x16, 0x38, 0x1d, 0x73, 0x05, 0x8d, 0x30, 0xbb, 0x8d, 0xbe, 0x79,
	0xfc, 0xc2, 0x45, 0x3c, 0x0f, 0x22, 0x4d, 0x9b, 0x19, 0x8c, 0x2f, 0x3e, 0x67, 0xbd, 0x21, 0x83,
	0x48, 0xd3, 0x66, 0x2f, 0x2c, 0xbb, 0xcf, 0x79, 0xbf, 0x0a, 0x2d, 0xde, 0xc7, 0xbe, 0x33, 0x1c,
	0x3a, 0x47, 0xe8, 0x3d, 0x40, 0x88, 0xa0, 0x6f, 0x1e, 0x3f, 0x42, 0x20, 0x77, 0x1d, 0x97, 0x60,
	0xd1, 0xf7, 0x33, 0x38, 0xd4, 0x82, 0x90, 0x81, 0xf0, 0x30, 0x38, 0xd6, 0x65, 0x19, 0x11, 0xfb,
	0xb3, 0x5f, 0x14, 0x43, 0x71, 0x98, 0x3f, 0xf1, 0x88, 0x87, 0x6f, 0xc6, 0xc2, 0x9d, 0x2b, 0xd0,
	0x8c, 0x7a, 0xa5, 0x96, 0x60, 0xe3, 0x40, 0xf5, 0x47, 0x1f, 0x00, 0xa1, 0x4c, 0x86, 0xd4, 0x42,
	0x38, 0x5c, 0x80, 0x4b, 0x28, 0xc0, 0x25, 0xca, 0x30, 0xb0, 0x46, 0x38, 0x97, 0xe2, 0x65, 0x58,
	0xec, 0x39, 0x36, 0x9b, 0x8c, 0x2c, 0xe1, 0x15, 0x35, 0xc1, 0x91, 0x0f, 0xe3, 0xd3, 0xba, 0x0c,
	0x8b, 0xe6, 0xa4, 0x4f, 0x2d, 0xbb, 0x67, 0x21, 0x49, 0x5b, 0x90, 0xf8, 0x30, 0x4e, 0xc2, 0x37,
	0x9d, 0x39, 0xb2, 0x50, 0x7c, 0x44, 0x6e, 0x3a, 0x73, 0x64, 0x71, 0xd1, 0xf9, 0xa8, 0x11, 0xb5,
	0xd7, 0x96, 0x43, 0xd4, 0x53, 0x6a, 0x4b, 0x46, 0x11, 0xab, 0x30, 0xba, 0xe2, 0x33, 0xfa, 0xd8,
	0x1c, 0x29, 0x8c, 0x6e, 0x80, 0x86, 0x94, 0xaa, 0x92, 0x9d, 0x16, 0xee, 0x9e, 0xc3, 0x95, 0xc0,
	0x20, 0xe6, 0x67, 0xcf, 0xc4, 0xfd, 0x2c, 0xb9, 0x06, 0x2d, 0x9f, 0x40, 0x8a, 0x71, 0x55, 0x28,
	0x8e, 0x84, 0x86, 0x1a, 0xed, 0x93, 0x71, 0xe6, 0xd7, 0x22, 0xfd, 0x70, 0xfe, 0xef, 0xc2, 0x69,
	0xae, 0xa3, 0x92, 0x46, 0x99, 0xc2, 0x3a, 0x4e, 0x81, 0x50, 0xf6, 0x4a, 0xe0, 0xc2, 0x59, 0xdc,
	0x80, 0xf6, 0x91, 0x65, 0x1d, 0x18, 0x11, 0x3d, 0xe9, 0x88, 0x69, 0x70, 0xc4, 0xb3, 0x50, 0x57,
	0x6e, 0x4a, 0xd2, 0xc8, 0xf2, 0x9c, 0x15, 0xc6, 0x81, 0x23, 0x76, 0x94, 0x25, 0xf2, 0x69, 0x23,
	0xeb, 0x74, 0x2e, 0xa4, 0xdd, 0x52, 0xd6, 0xea, 0x3a, 0x20, 0x48, 0x55, 0xe6, 0xf3, 0x62, 0xfa,
	0x1c, 0x1c, 0x68, 0xb3, 0xee, 0xc0, 0x95, 0xc0, 0xe4, 0xa1, 0xc9, 0xe8, 0x5a, 0x3d, 0x67, 0xdc,
	0x7f, 0x4d, 0xbd, 0xb7, 0xdc, 0xe0, 0xf9, 0xce, 0x25, 0x69, 0x42, 0xce, 0x81, 0x30, 0xaa, 0x7b,
	0xa1, 0xaf, 0x0e, 0x01, 0x64, 0x0d, 0xb8, 0x55, 0x45, 0x5c, 0xe8, 0x53, 0xd1, 0x8d, 0x7f, 0x0f,
	0xae, 0x4e, 0x1f, 0x90, 0xb9, 0xe4, 0x5b, 0x50, 0x55, 0x7c, 0xe5, 0xd5, 0xec, 0x40, 0x56, 0x71,
	0x96, 0xd8, 0x42, 0xff, 0x9b, 0x12, 0x9c, 0x8b, 0x66, 0x4c, 0xca, 0x38, 0x53, 0x3c, 0xa5, 0x9a,
	0x87, 0x95, 0x23, 0x79, 0x58, 0x74, 0xc2, 0x95, 0x9c, 0x09, 0x57, 0x23, 0x13, 0xce, 0xcd, 0x6c,
	0xf4, 0xaf, 0xe0, 0x7c, 0x0e, 0xab, 0xef, 0x25, 0x86, 0x7f, 0x13, 0xe1, 0x42, 0x1c, 0x5b, 0x20,
	0x5c, 0x38, 0xe1, 0xd2, 0x2a, 0x29, 0x61, 0x35, 0x3d, 0x25, 0x54, 0xc3, 0x86, 0xa8, 0x5c, 0xe6,
	0x63, 0x26, 0x50, 0x5d, 0x87, 0x5a, 0x34, 0x1f, 0x66, 0xe8, 0x22, 0xd3, 0xa7, 0xf5, 0x3e, 0x02,
	0x23, 0x1d, 0x08, 0x72, 0xb9, 0x44, 0x6e, 0xf7, 0xbf, 0xf3, 0xd0, 0x16, 0xcd, 0x5e, 0x5b, 0xd6,
	0x41, 0xb6, 0x63, 0xfd, 0x79, 0x38, 0xd1, 0xab, 0xd0, 0x0a, 0x3c, 0x28, 0xc3, 0x9d, 0x2e, 0xa4,
	0xb9, 0x78, 0x28, 0x9d, 0x28, 0xe3, 0x06, 0x61, 0x03, 0x34, 0x61, 0x3c, 0x12, 0x9e, 0x14, 0x4d,
	0x92, 0xe2, 0x4a, 0x2f, 0xc1, 0x22, 0x52, 0x46, 0x7d, 0x29, 0x70, 0xd8, 0x0c, 0xce, 0x34, 0xc5,
	0x00, 0x41, 0x8a, 0x01, 0xfa, 0x19, 0xb8, 0xd3, 0xac, 0x5c, 0xaf, 0x95, 0x99, 0xeb, 0x6d, 0x80,
	0x16, 0x73, 0xac, 0x0c, 0x3d, 0x6b, 0xb3, 0xdb, 0x1a, 0xaa, 0x7e, 0x95, 0xfd, 0x5c, 0x1d, 0xab,
	0xef, 0x2b, 0x55, 0x2e, 0x57, 0x42, 0x5f, 0xa9, 0x70, 0xf9, 0x0b, 0xe8, 0x55, 0x37, 0x61, 0x39,
	0xe9, 0x52, 0x19, 0xfa, 0xd4, 0x66, 0xb7, 0x7d, 0x18, 0xf3, 0xa8, 0x4c, 0xff, 0xdb, 0x52, 0xdc,
	0x52, 0x8a, 0x8d, 0xf8, 0x0b, 0x69, 0xd5, 0xbf, 0x0b, 0x17, 0xf2, 0x78, 0xc5, 0x94, 0x5b, 0xb5,
	0x52, 0xd7, 0xb2, 0xad, 0x94, 0x62, 0x6e, 0xa4, 0x5d, 0xff, 0xf7, 0x92, 0x62, 0x00, 0x15, 0xf4,
	0x2f, 0x87, 0x61, 0x8f, 0x65, 0x30, 0xb5, 0x44, 0x06, 0x33, 0x81, 0x73, 0xd9, 0xb3, 0x7b, 0x2f,
	0xc9, 0xe5, 0x1a, 0xf8, 0x7f, 0x2a, 0x87, 0x99, 0x97, 0x68, 0x59, 0x38, 0xbb, 0xf6, 0x2d, 0x5d,
	0xa2, 0xc8, 0x80, 0x96, 0x6e, 0x3b, 0xc8, 0x3c, 0x75, 0x40, 0x80, 0x11, 0xab, 0x36, 0x2c, 0x70,
	0xe0, 0x43, 0x59, 0x71, 0x88, 0x64, 0xe5, 0xd5, 0xcc, 0xac, 0x7c, 0x2e, 0x35, 0x2b, 0x9f, 0x57,
	0x57, 0xe1, 0x2a, 0xb4, 0xe4, 0xc6, 0x1a, 0x51, 0x1b, 0x0d, 0x4f, 0x4d, 0xba, 0x22, 0x84, 0x3e,
	0xa5, 0x36, 0x9f, 0xf6, 0xa3, 0x99, 0x72, 0x77, 0xb2, 0x0a, 0xcb, 0x5b, 0xae, 0xeb, 0x50, 0xdb,
	0xdb, 0x52, 0x7a, 0xd1, 0x2a, 0xfa, 0x67, 0xb8, 0x6a, 0x19, 0xd2, 0x2b, 0x92, 0xb8, 0xfe, 0xf9,
	0x1c, 0xb4, 0xb1, 0xf9, 0x53, 0xc7, 0xf6, 0xde, 0xfa, 0x0e, 0xf6, 0x1b, 0xa8, 0x40, 0x56, 0x67,
	0xaa, 0x40, 0x56, 0xdf, 0xa7, 0x02, 0x79, 0x1b, 0x56, 0xb8, 0x2b, 0x4b, 0x50, 0x8b, 0xd5, 0x6a,
	0xdb, 0xd6, 0xd1, 0xab, 0x68, 0x83, 0x3b, 0xb0, 0xe2, 0x3a, 0x9e, 0x65, 0x7b, 0x34, 0x64, 0x3d,
	0xf4, 0xdf, 0x24, 0xc0, 0x4d, 0x2b, 0x72, 0xd6, 0x66, 0x2b, 0x72, 0xd6, 0x71, 0xa2, 0x69, 0x8e,
	0xef, 0x26, 0x68, 0xef, 0x26, 0xe6, 0x90, 0x7a, 0xc7, 0x41, 0x2f, 0xd2, 0xb7, 0x27, 0xe0, 0x64,
	0x03, 0x96, 0x84, 0x2a, 0x85, 0xa4, 0xc2, 0xc1, 0xc7, 0xc1, 0x64, 0x13, 0x99, 0xde, 0x8a, 0x11,
	0x0b, 0x47, 0x9f, 0x82, 0x21, 0x77, 0x60, 0xf9, 0x2d, 0x1d, 0xbc, 0x15, 0x3c, 0x85, 0x0d, 0x84,
	0xdf, 0x4f, 0x43, 0xa5, 0x15, 0x67, 0x9b, 0x38, 0xc9, 0x58, 0x71, 0x76, 0x13, 0x96, 0xdd, 0xb1,
	0xb3, 0x6f, 0x31, 0x46, 0x1d, 0xdb, 0x70, 0xc7, 0x66, 0x0f, 0xe5, 0x27, 0x22, 0x81, 0x76, 0x88,
	0x7a, 0x3e, 0x36, 0x7b, 0x7c, 0x7b, 0xbc, 0x80, 0x55, 0x5f, 0xad, 0x55, 0xbd, 0x9c, 0x5e, 0x70,
	0x1c, 0x71, 0x6a, 0xb5, 0x8e, 0xd9, 0x40, 0x08, 0x66, 0x40, 0xaf, 0xf1, 0xc0, 0x25, 0xa5, 0x53,
	0xe6, 0x92, 0x5f, 0x8f, 0x16, 0x4f, 0xaf, 0x65, 0x96, 0x08, 0x23, 0x2d, 0x65, 0x31, 0xf6, 0x27,
	0x0d, 0x20, 0x42, 0x26, 0x91, 0x4d, 0x94, 0x8c, 0x52, 0x63, 0x01, 0x68, 0x39, 0x11, 0x80, 0x16,
	0x0a, 0x63, 0x3f, 0x00, 0xd2, 0x37, 0x8f, 0x0d, 0x25, 0x52, 0x0d, 0xb7, 0xc6, 0x52, 0xdf, 0x3c,
	0x7e, 0xe2, 0x07, 0xaa, 0x7c, 0x85, 0x66, 0x0b, 0x69, 0xd7, 0xa1, 0xce, 0xd5, 0x1c, 0xcf, 0x8a,
	0xc4, 0x66, 0xa8, 0xd9, 0xd6, 0x11, 0x1e, 0x14, 0xdd, 0x82, 0x65, 0x3c, 0x4c, 0x14, 0x12, 0xc6,
	0x30, 0x34, 0xdc, 0x02, 0x1a, 0x47, 0xe1, 0xe4, 0x79, 0xe4, 0xa8, 0x84, 0x97, 0x01, 0x5d, 0x3d,
	0x08, 0x2f, 0x7d, 0x0a, 0x6e, 0x9a, 0x94, 0x98, 0xb8, 0x21, 0x8f, 0xa6, 0xc2, 0x78, 0x78, 0x15,
	0x6a, 0x7e, 0x28, 0x2c, 0x14, 0x7c, 0x9e, 0x65, 0x84, 0xc1, 0x0b, 0xc9, 0x30, 0xf8, 0x3c, 0x80,
	0x12, 0x01, 0x0b, 0x15, 0x6e, 0xec, 0x07, 0xd1, 0xef, 0x2a, 0xd4, 0x44, 0xc5, 0x9b, 0xc9, 0xb0,
	0x75, 0x1e, 0xeb, 0xdd, 0xc9, 0xa0, 0xb7, 0x35, 0x25, 0xe8, 0x5d, 0x2a, 0x18, 0xf4, 0x6a, 0x33,
	0x05, 0xbd, 0xed, 0xd4, 0xa0, 0xf7, 0x26, 0xb4, 0x29, 0x33, 0xa4, 0x41, 0x90, 0x76, 0x08, 0xe3,
	0x56, 0x2c, 0xe8, 0x7c, 0x47, 0x35, 0x14, 0xbc, 0x57, 0xca, 0xfc, 0x3e, 0x25, 0xe9, 0x32, 0x92,
	0xb6, 0x28, 0x53, 0x37, 0x3e, 0xb7, 0x9e, 0x94, 0x89, 0x92, 0x49, 0x84, 0x5a, 0x54, 0x8a, 0xda,
	0x94, 0x3d, 0x8b, 0x5a, 0x8a, 0xb4, 0x4d, 0x7f, 0x3a, 0xed, 0x44, 0xe6, 0x26, 0xb4, 0xa5, 0x28,
	0x38, 0x99, 0x94, 0xc3, 0x19, 0x69, 0xaa, 0x10, 0xf1, 0xfc, 0x60, 0x20, 0x85, 0xf0, 0x21, 0x16,
	0xab, 0x62, 0x36, 0x02, 0x43, 0xdc, 0x7a, 0x57, 0xa3, 0xec, 0x79, 0xc4, 0x42, 0x44, 0xe2, 0xf6,
	0xb5, 0xec, 0xb8, 0x7d, 0x7d, 0x7a, 0xdc, 0xde, 0x29, 0x1c, 0xb7, 0x9f, 0x2d, 0x12, 0xb7, 0x9f,
	0x2b, 0x10, 0xb7, 0x9f, 0x2f, 0x10, 0xb7, 0x5f, 0x28, 0x1a, 0xb7, 0x5f, 0xcc, 0x88, 0xdb, 0x13,
	0x09, 0xd2, 0xa5, 0x44, 0x82, 0xa4, 0x33, 0xe5, 0x00, 0x7a, 0x36, 0x2b, 0xab, 0x9c, 0x10, 0x94,
	0xe3, 0x27, 0x04, 0x51, 0x23, 0x5c, 0x89, 0x1b, 0xe1, 0xaf, 0x94, 0x73, 0xe2, 0x84, 0x15, 0xfe,
	0x34, 0x6a, 0x85, 0xaf, 0x67, 0x07, 0x99, 0x69, 0x66, 0xf8, 0xeb, 0x92, 0x12, 0xc2, 0xaa, 0x04,
	0x05, 0x62, 0xc9, 0x0b, 0xf2, 0xf0, 0x02, 0x9b, 0xf9, 0x86, 0x39, 0x84, 0xf0, 0x10, 0xd6, 0xb2,
	0x85, 0xf1, 0xf7, 0x0f, 0x86, 0xfd, 0xef, 0x9f, 0x69, 0x94, 0xfe, 0x7d, 0x38, 0x9f, 0x33, 0x45,
	0xe6, 0x92, 0x4f, 0x22, 0x61, 0x7a, 0x51, 0x09, 0x8a, 0x38, 0x3d, 0xf7, 0x94, 0xfd, 0xa7, 0xe2,
	0xf8, 0x48, 0x39, 0xff, 0x9e, 0x51, 0xc4, 0x91, 0x10, 0xbb, 0x9c, 0x19, 0x62, 0x57, 0x52, 0x43,
	0xec, 0xaa, 0x2a, 0xc2, 0xa8, 0x8a, 0xcd, 0xc5, 0x54, 0x2c, 0x57, 0xc2, 0xfa, 0x1f, 0x8b, 0xb3,
	0xa6, 0xdc, 0x69, 0x30, 0x97, 0x3c, 0x4c, 0x39, 0x37, 0x2c, 0x2a, 0xcc, 0xf0, 0xe8, 0x30, 0x5f,
	0xa2, 0x6e, 0x3c, 0xb3, 0x4e, 0xd9, 0x84, 0x19, 0x97, 0x53, 0x38, 0x4a, 0x8a, 0x80, 0xf9, 0xe7,
	0x70, 0x42, 0x00, 0x2c, 0xb2, 0x04, 0x95, 0xc8, 0x12, 0xe8, 0x83, 0x78, 0x7e, 0x9c, 0xd8, 0x81,
	0xdf, 0xcc, 0xbc, 0xf5, 0x1f, 0x97, 0xe0, 0x5a, 0xf6, 0x48, 0x7e, 0xcd, 0xf9, 0xe4, 0xd5, 0x83,
	0x75, 0xa8, 0x8b, 0xac, 0xcf, 0x63, 0xfe, 0x1c, 0x45, 0x3e, 0xcd, 0xc8, 0x69, 0x98, 0xe7, 0x61,
	0x81, 0xc7, 0x7c, 0x9d, 0xe1, 0xc9, 0x34, 0xcb, 0xaf, 0x1b, 0x38, 0x70, 0xbd, 0x08, 0xb7, 0xdf,
	0x9c, 0x7c, 0xfe, 0xb0, 0x04, 0x6d, 0x89, 0x53, 0xee, 0x7d, 0x44, 0xf5, 0xba, 0x14, 0xd7, 0xeb,
	0x44, 0x70, 0x58, 0x2e, 0x1c, 0x1c, 0x56, 0x52, 0x83, 0x43, 0x7d, 0x3f, 0x69, 0x4d, 0xa2, 0xb7,
	0x22, 0xa6, 0x9c, 0x06, 0x5f, 0x87, 0xa5, 0x90, 0x61, 0x75, 0xa1, 0x9a, 0x01, 0xd7, 0xf2, 0x2a,
	0xd5, 0x85, 0xbc, 0x71, 0xa2, 0xf1, 0x77, 0x66, 0x79, 0x21, 0xd9, 0x52, 0x1a, 0xfe, 0xef, 0xc3,
	0xf9, 0x97, 0x2e, 0x8f, 0xdf, 0x94, 0x5d, 0xbd, 0xc5, 0xd7, 0xb6, 0xc0, 0x25, 0xa0, 0x88, 0x5e,
	0x94, 0xf3, 0xcd, 0x71, 0x25, 0x61, 0x8e, 0x2f, 0xc1, 0x85, 0xbc, 0x91, 0x99, 0xab, 0xf7, 0x41,
	0xcf, 0x56, 0xad, 0xed, 0xe3, 0x97, 0x14, 0x6b, 0x68, 0xe1, 0x40, 0x13, 0xda, 0x67, 0xd1, 0x34,
	0xfd, 0x25, 0xed, 0xb3, 0x69, 0xa9, 0xcd, 0xd7, 0xf3, 0x70, 0x65, 0xea, 0x30, 0xcc, 0x25, 0xaf,
	0x22, 0xde, 0x61, 0x3b, 0x4d, 0xca, 0x05, 0xba, 0xd9, 0xc4, 0x81, 0x15, 0xcf, 0xd1, 0xf9, 0x7a,
	0x4e, 0xf2, 0x17, 0x28, 0x72, 0x9e, 0xda, 0x9c, 0x83, 0xc6, 0xb1, 0x65, 0x8e, 0x47, 0x81, 0xaf,
	0x6d, 0x74, 0x43, 0x40, 0xb1, 0x1c, 0x28, 0x9e, 0x5e, 0x54, 0x13, 0xe9, 0x45, 0x50, 0xc2, 0x10,
	0xd1, 0x52, 0x58, 0x0f, 0x68, 0x86, 0x51, 0xb5, 0x2c, 0x13, 0x47, 0x02, 0xa5, 0xf9, 0x64, 0x25,
	0x39, 0x71, 0x2e, 0x5c, 0x4b, 0x39, 0x17, 0x8e, 0x97, 0x9b, 0xeb, 0xc9, 0x72, 0x73, 0x7a, 0x2e,
	0xd6, 0xc8, 0xc8, 0xc5, 0xd2, 0x77, 0x32, 0xa4, 0xa7, 0x79, 0xb1, 0xcc, 0x72, 0x21, 0x91, 0x59,
	0x4e, 0xc9, 0x87, 0xd4, 0x88, 0xba, 0x99, 0x1d, 0x51, 0xb7, 0xa6, 0x47, 0xd4, 0x4b, 0x85, 0x23,
	0x6a, 0xad, 0x48, 0x44, 0xdd, 0x2e, 0x10, 0x51, 0x93, 0x02, 0x11, 0xf5, 0x72, 0xd1, 0x88, 0x7a,
	0x25, 0xab, 0x12, 0xfe, 0x77, 0x25, 0x68, 0x3c, 0xe5, 0x9b, 0xe3, 0x81, 0xe9, 0x99, 0x71, 0x81,
	0x97, 0xa6, 0xa7, 0xf2, 0x69, 0xd6, 0x7a, 0x1d, 0xea, 0x7e, 0xb6, 0xe9, 0xfb, 0x31, 0x99, 0x69,
	0x92, 0xab, 0xd0, 0xe2, 0x69, 0xd3, 0x80, 0xee, 0x7b, 0xc6, 0xd8, 0xf4, 0xa8, 0x83, 0x3a, 0x5e,
	0xee, 0x2e, 0xba, 0x07, 0x83, 0xc7, 0x74, 0xdf, 0xeb, 0x72, 0x18, 0xcf, 0x63, 0xfd, 0x7b, 0x99,
	0x42, 0xbb, 0xe7, 0x87, 0xe2, 0x3e, 0xe6, 0x9f, 0x96, 0x60, 0x49, 0x6e, 0xe1, 0x80, 0xe7, 0xcf,
	0xa0, 0xf1, 0xc6, 0x64, 0x96, 0x31, 0xe3, 0x45, 0xc5, 0xfa, 0x1b, 0xff, 0xa2, 0xf2, 0xa7, 0x00,
	0x23, 0xde, 0x9b, 0xd1, 0x37, 0x3d, 0x13, 0x27, 0xb4, 0x70, 0xef, 0x7c, 0xaa, 0xa9, 0xf6, 0xc7,
	0xec, 0x36, 0x46, 0xfe, 0x4f, 0xfd, 0x7f, 0xca, 0x78, 0xb5, 0x57, 0x31, 0x95, 0x7e, 0xd4, 0x38,
	0xc5, 0x5e, 0xe4, 0xda, 0xe8, 0xb3, 0xd0, 0x10, 0x12, 0x38, 0x76, 0x7d, 0x53, 0x81, 0x32, 0xdd,
	0x3b, 0x76, 0x31, 0x14, 0x64, 0xce, 0xd8, 0x13, 0x48, 0x61, 0x23, 0xea, 0x1c, 0x80, 0x48, 0x02,
	0x55, 0xfe, 0x5b, 0x0a, 0x0e, 0x7f, 0x73, 0x98, 0x6b, 0x0e, 0xfc, 0xfb, 0x88, 0xf8, 0x9b, 0x77,
	0xc2, 0xff, 0x1a, 0x8c, 0xfe, 0xc0, 0xdf, 0xfa, 0x75, 0x0e, 0x78, 0x41, 0x7f, 0x60, 0x45, 0x5c,
	0x4b, 0x3d, 0xf5, 0x7e, 0x69, 0x23, 0xbc, 0xdb, 0x7c, 0x08, 0xf5, 0x17, 0xfe, 0xe8, 0xa7, 0xa1,
	0xcd, 0x7f, 0x1b, 0xfc, 0xc3, 0xf0, 0x2b, 0xbd, 0xa7, 0x48, 0x1b, 0x9a, 0x21, 0xf8, 0x91, 0x65,
	0x69, 0x25, 0xb2, 0x02, 0x9a, 0x4a, 0xc9, 0x15, 0x47, 0x2b, 0x47, 0x09, 0x9f, 0x52, 0x5b, 0xab,
	0x90, 0x55, 0x58, 0x0e, 0x41, 0xcf, 0x0f, 0x06, 0x06, 0xea, 0x88, 0x56, 0xd5, 0xff, 0xa5, 0x04,
	0x67, 0xd2, 0x24, 0xcf, 0x5c, 0xf2, 0x20, 0x59, 0x20, 0xce, 0xb8, 0x21, 0x17, 0xd3, 0x26, 0xd5,
	0x0f, 0xe6, 0xc6, 0xb9, 0x69, 0xc5, 0xe4, 0x4a, 0x5a, 0x31, 0x99, 0xdb, 0x08, 0x14, 0x65, 0xb2,
	0xe0, 0x3b, 0x08, 0x39, 0xe7, 0x71, 0xcb, 0x4f, 0x4a, 0xca, 0x25, 0x71, 0xe4, 0xa8, 0xa0, 0x26,
	0xa9, 0x71, 0x63, 0x39, 0x2b, 0x6e, 0xac, 0xa8, 0x71, 0xa3, 0xaf, 0x10, 0xd5, 0x2c, 0x85, 0x98,
	0xcb, 0x52, 0x88, 0x5d, 0x3f, 0xf9, 0x08, 0xc2, 0xef, 0x7f, 0x14, 0xcb, 0x90, 0x60, 0x9b, 0xb9,
	0x64, 0x27, 0x58, 0x86, 0x19, 0x37, 0x27, 0xf8, 0xf6, 0x64, 0xdf, 0x21, 0x9f, 0xe0, 0x35, 0x4e,
	0x33, 0x0c, 0xc4, 0xa6, 0xee, 0xce, 0x3a, 0xa7, 0x4f, 0xae, 0x60, 0x25, 0x96, 0xa9, 0xfc, 0x78,
	0x1e, 0x56, 0x50, 0x79, 0xbe, 0x74, 0x2d, 0xb4, 0x45, 0xb6, 0x39, 0x44, 0x8b, 0x92, 0x13, 0x58,
	0x5d, 0x81, 0xe6, 0xd8, 0x3a, 0xb4, 0xec, 0x89, 0x65, 0x0c, 0xad, 0x43, 0x6b, 0xe8, 0xdb, 0x3f,
	0x09, 0x7c, 0xc2, 0x61, 0xe4, 0x16, 0x2c, 0x73, 0x37, 0x8e, 0x6f, 0x1d, 0x12, 0xab, 0xae, 0xd9,
	0xd6, 0xd1, 0x0b, 0x3a, 0xb0, 0x43, 0x0d, 0xf9, 0x08, 0x56, 0x2d, 0xdb, 0x7c, 0x33, 0xb4, 0xfa,
	0x62, 0x3a, 0x13, 0xc6, 0x9d, 0xb6, 0x33, 0x09, 0x7c, 0xfb, 0x8a, 0x44, 0x73, 0xdf, 0xf8, 0x92,
	0x59, 0xe3, 0x1d, 0x8e, 0x23, 0x9f, 0xc2, 0xd9, 0x48, 0x33, 0x3e, 0xa4, 0xd2, 0x54, 0x6c, 0xdb,
	0x55, 0xa5, 0xe9, 0x33, 0xeb, 0x28, 0x6c, 0x7d, 0x17, 0x4e, 0x27, 0x8a, 0x63, 0xc8, 0x65, 0x23,
	0xb3, 0x90, 0xce, 0x6d, 0xf7, 0xd8, 0x51, 0x69, 0xe5, 0x85, 0x36, 0x77, 0xec, 0x84, 0x54, 0x37,
	0xa1, 0x3d, 0x32, 0xbd, 0xc9, 0x38, 0xd2, 0xa9, 0xf0, 0xdc, 0x4b, 0x02, 0x11, 0xd2, 0xde, 0x85,
	0xd3, 0xae, 0xe3, 0xa5, 0x30, 0xb1, 0x18, 0x1c, 0x59, 0xc4, 0x99, 0x50, 0x65, 0x1b, 0x72, 0x23,
	0xbd, 0xbb, 0x2f, 0xdb, 0xe7, 0x3e, 0x43, 0x64, 0x0d, 0x6a, 0x72, 0x69, 0xd0, 0xcb, 0x57, 0xbb,
	0xfe, 0xa7, 0xba, 0x92, 0xac, 0xe7, 0x8c, 0x2d, 0xe9, 0xe2, 0xfd, 0x95, 0x7c, 0xc1, 0x61, 0x7c,
	0xf3, 0x2a, 0x53, 0x16, 0x74, 0xd2, 0xc1, 0x07, 0x93, 0x16, 0x94, 0x1f, 0xc1, 0x6a, 0x52, 0x9e,
	0xa2, 0x81, 0x70, 0xf6, 0x2b, 0x31, 0x89, 0x8a, 0x66, 0x9b, 0xb0, 0x1c, 0x95, 0x96, 0x68, 0x22,
	0x7c, 0x7f, 0x5b, 0x95, 0x57, 0x30, 0x4c, 0x52, 0x62, 0xa2, 0x8d, 0x88, 0x05, 0x56, 0x62, 0x32,
	0x13, 0xcd, 0x2e, 0xc2, 0x82, 0xd8, 0x07, 0x82, 0x54, 0x44, 0x03, 0x80, 0x20, 0x41, 0x70, 0x19,
	0xfc, 0x89, 0x8b, 0x58, 0x5c, 0x94, 0x3e, 0x17, 0x24, 0x0c, 0xa3, 0xf1, 0x1f, 0x86, 0x25, 0x06,
	0x65, 0xc3, 0xec, 0x98, 0xae, 0xf9, 0x86, 0x0e, 0xa9, 0x47, 0xad, 0x69, 0x25, 0xb6, 0x42, 0x7b,
	0x27, 0x7e, 0x7f, 0xba, 0x12, 0x5e, 0xa8, 0x1d, 0xe2, 0xe5, 0xb2, 0x7c, 0x06, 0x30, 0x99, 0x55,
	0xec, 0x85, 0xb0, 0xfc, 0x1b, 0x99, 0x07, 0x1f, 0xb1, 0xad, 0x1f, 0x9a, 0x0e, 0xfd, 0x73, 0xb8,
	0xa6, 0x24, 0x41, 0x39, 0x33, 0xbe, 0x08, 0x0b, 0x13, 0x37, 0xbc, 0x68, 0x2e, 0x2f, 0x39, 0x08,
	0x10, 0x0a, 0x6e, 0x03, 0xae, 0x17, 0xe9, 0x89, 0xb9, 0xfa, 0x9f, 0x94, 0x41, 0xdb, 0x9e, 0x30,
	0x6a, 0x5b, 0x8c, 0x6d, 0xd9, 0xe6, 0xf0, 0x98, 0x51, 0xac, 0x7c, 0x1c, 0xb8, 0x54, 0x38, 0x7b,
	0x29, 0xd1, 0x03, 0x97, 0xa2, 0xb7, 0x5d, 0x81, 0xb9, 0x43, 0x73, 0x38, 0xf1, 0x8f, 0x26, 0xc5,
	0x07, 0x67, 0x08, 0x47, 0x30, 0x04, 0x4e, 0x48, 0x11, 0x10, 0xf4, 0x0a, 0x09, 0x08, 0x54, 0xc7,
	0xa6, 0x7d, 0xe0, 0x5b, 0x7f, 0xfe, 0x9b, 0xbb, 0x18, 0xd1, 0x08, 0x31, 0xdc, 0xee, 0xcc, 0x75,
	0x1b, 0x08, 0xe9, 0x72, 0xf4, 0x3a, 0xd4, 0xcd, 0xe1, 0x50, 0x20, 0xa5, 0xfd, 0x37, 0x87, 0x43,
	0x44, 0xf1, 0x50, 0x85, 0x2f, 0x9d, 0x61, 0x1e, 0x8a, 0x40, 0xa2, 0xda, 0xad, 0x23, 0x60, 0xeb,
	0x10, 0x9d, 0x8a, 0x3c, 0xc4, 0x9c, 0xf8, 0x27, 0x85, 0x75, 0x71, 0x72, 0x39, 0x41, 0xa4, 0x33,
	0xec, 0x4b, 0x64, 0x43, 0x20, 0x9d, 0x61, 0x1f, 0x91, 0xfa, 0x36, 0xb4, 0x7d, 0x51, 0x3c, 0xa0,
	0xe6, 0xc0, 0x76, 0xa6, 0xc8, 0x82, 0x40, 0xb5, 0x4f, 0xcd, 0x81, 0xcc, 0xbc, 0xf0, 0xb7, 0x4e,
	0xf1, 0xce, 0xda, 0x6b, 0xcb, 0x3a, 0x88, 0x4b, 0x75, 0x7a, 0x91, 0x66, 0x36, 0x8f, 0xca, 0xbd,
	0xe0, 0xd9, 0xcc, 0xb1, 0x98, 0x4b, 0x76, 0x79, 0xe0, 0x2c, 0xbe, 0x8d, 0x69, 0x37, 0xca, 0x12,
	0x1d, 0x2c, 0xfa, 0x4d, 0xd1, 0xa9, 0x3d, 0x81, 0x56, 0xdf, 0x97, 0x88, 0xea, 0x15, 0xaf, 0xe5,
	0xf5, 0x15, 0xc8, 0xb0, 0xdb, 0x0c, 0x1a, 0xa3, 0x9e, 0xbf, 0x40, 0xbe, 0x31, 0x23, 0x9e, 0x5d,
	0x48, 0x19, 0xd5, 0x3a, 0xfd, 0xef, 0x45, 0xd1, 0x3a, 0xa3, 0x57, 0x7c, 0x3b, 0x85, 0x0b, 0x39,
	0xb3, 0x24, 0xf8, 0x72, 0xff, 0x3f, 0x08, 0xe1, 0xaf, 0x4b, 0xd0, 0xde, 0x99, 0x8c, 0xc7, 0xb4,
	0x37, 0x19, 0x4e, 0x46, 0x4f, 0x2d, 0xc6, 0x78, 0x44, 0x74, 0x03, 0xb4, 0x5e, 0x00, 0x34, 0x3c,
	0xea, 0x0d, 0x85, 0xd6, 0x35, 0xba, 0x4b, 0x21, 0x7c, 0x8f, 0x83, 0xc9, 0xaf, 0x80, 0x02, 0x32,
	0xfa, 0x16, 0xeb, 0x49, 0x45, 0x6c, 0x85, 0xe0, 0x07, 0x16, 0xeb, 0x71, 0x9d, 0xdf, 0xa7, 0x43,
	0x25, 0xb0, 0x6f, 0x74, 0xeb, 0x1c, 0x80, 0x3a, 0xbc, 0x0e, 0xf8, 0xdb, 0x98, 0x8c, 0x87, 0xb8,
	0x39, 0x1b, 0xdd, 0x1a, 0xff, 0x7e, 0x39, 0x1e, 0xea, 0x77, 0x71, 0x99, 0x12, 0x3c, 0xfa, 0x11,
	0x22, 0x81, 0xaa, 0xb2, 0x29, 0xf0, 0xb7, 0x6e, 0xe3, 0x1a, 0x64, 0x34, 0x61, 0x2e, 0x79, 0x16,
	0xe1, 0x79, 0xda, 0x35, 0x98, 0x44, 0x3f, 0xea, 0xd4, 0x50, 0x88, 0xff, 0x55, 0x82, 0xa5, 0xbd,
	0x31, 0x1d, 0x0c, 0xac, 0x31, 0xb7, 0x7b, 0x63, 0xce, 0xd7, 0x13, 0x00, 0x6e, 0x15, 0xc7, 0xe1,
	0x96, 0x6d, 0xdd, 0xbb, 0x95, 0xd6, 0x7d, 0xac, 0xe1, 0x26, 0xfe, 0xe0, 0x42, 0xe9, 0x36, 0x3c,
	0xff, 0x27, 0x69, 0x41, 0x39, 0xd0, 0xb5, 0x32, 0x96, 0xc8, 0x1a, 0x01, 0x1d, 0x39, 0x03, 0x04,
	0x3f, 0xe2, 0xb9, 0xc7, 0x06, 0x5c, 0x55, 0xe0, 0xc1, 0xa5, 0x92, 0xb8, 0x6a, 0x69, 0x25, 0x72,
	0x03, 0xae, 0xc5, 0x29, 0x53, 0x35, 0x58, 0x2b, 0xeb, 0x04, 0xb4, 0x28, 0xc7, 0xcc, 0xbd, 0xf9,
	0xaf, 0x25, 0x80, 0xad, 0xf0, 0x0d, 0x22, 0xc0, 0xfc, 0x8e, 0x33, 0x1a, 0x39, 0x76, 0xf4, 0x06,
	0xcc, 0x22, 0xd4, 0x9f, 0xc9, 0xab, 0x1b, 0x5a, 0x39, 0xb8, 0x24, 0x53, 0x21, 0x4d, 0x68, 0x3c,
	0xf7, 0x6f, 0x68, 0x68, 0x55, 0xde, 0x5a, 0xdc, 0xeb, 0xd6, 0xe6, 0x38, 0x6a, 0xcf, 0x91, 0xb5,
	0x11, 0x6d, 0x9e, 0x7f, 0x6e, 0x8f, 0x2d, 0xf3, 0x00, 0x3f, 0x6b, 0x64, 0x01, 0x6a, 0xdf, 0x79,
	0xb9, 0xf5, 0x64, 0x77, 0xef, 0x2b, 0xad, 0xce, 0x9b, 0x6d, 0xed, 0xec, 0xed, 0xbe, 0x7a, 0xa8,
	0x35, 0x48, 0x0b, 0xe0, 0xd9, 0xc3, 0xd7, 0x86, 0xfc, 0x06, 0xb2, 0x04, 0x0b, 0x9f, 0xef, 0x3e,
	0xfe, 0xdc, 0xd8, 0x7d, 0xb6, 0xf3, 0xe5, 0xd3, 0x87, 0xda, 0x02, 0x59, 0x86, 0xa5, 0xf0, 0x08,
	0xd2, 0x78, 0x3e, 0x36, 0x7b, 0xda, 0x22, 0xef, 0x7d, 0x97, 0x19, 0x4f, 0xe8, 0x21, 0xb5, 0x07,
	0x5a, 0xf3, 0xe6, 0xef, 0x97, 0xa1, 0xf6, 0x45, 0xe0, 0x6e, 0xb4, 0x2f, 0x5c, 0x1a, 0x97, 0xef,
	0x3a, 0x9c, 0x0e, 0xa0, 0x8f, 0x45, 0x8e, 0x20, 0xb2, 0xb9, 0x12, 0x39, 0x07, 0x6b, 0x01, 0xea,
	0x99, 0x75, 0x64, 0xf0, 0xd8, 0xcc, 0x10, 0x22, 0xd2, 0xca, 0x64, 0x0d, 0x56, 0x02, 0x2c, 0x9f,
	0x8b, 0x8f, 0xa9, 0x90, 0xb3, 0xb0, 0x1a, 0x60, 0xf6, 0x8e, 0x1c, 0xe3, 0xb5, 0x19, 0x34, 0xab,
	0x46, 0x91, 0x96, 0xad, 0x22, 0xe7, 0x78, 0xb2, 0x18, 0x20, 0x9f, 0x8f, 0x1d, 0x1f, 0x31, 0x4f,
	0x3a, 0x70, 0x26, 0x40, 0x3c, 0x15, 0x11, 0x97, 0xc4, 0xd5, 0xc8, 0x05, 0xe8, 0x84, 0x8d, 0x1c,
	0xcf, 0x10, 0xf1, 0x93, 0x8f, 0xaf, 0xdf, 0xfb, 0x8f, 0x8b, 0xa0, 0xed, 0x84, 0x0f, 0x7f, 0x45,
	0x35, 0xf0, 0x77, 0xa0, 0x9d, 0x78, 0x1a, 0x4a, 0xd2, 0x03, 0x8d, 0x94, 0x27, 0xba, 0x9d, 0x1b,
	0x05, 0x29, 0x99, 0xab, 0x9f, 0x22, 0xc7, 0x70, 0x26, 0xfd, 0xd1, 0x2e, 0xb9, 0x35, 0xbd, 0xd8,
	0xa9, 0x8e, 0xba, 0x39, 0x0b, 0x39, 0x0e, 0x7d, 0x98, 0xf2, 0x70, 0x19, 0x0d, 0xf0, 0x87, 0x85,
	0x26, 0x20, 0xed, 0x53, 0xe7, 0xd6, 0x0c, 0xd4, 0xfe, 0x94, 0xd3, 0xdf, 0xa5, 0x92, 0xac, 0xae,
	0xd2, 0x9f, 0xca, 0xa6, 0x4f, 0x39, 0xfb, 0xc9, 0xab, 0x7e, 0x8a, 0xfc, 0x6e, 0x78, 0x4d, 0x27,
	0xf6, 0xbe, 0x91, 0xe4, 0x76, 0x96, 0x7c, 0x7f, 0xd9, 0xb9, 0x3d, 0x13, 0x3d, 0x8e, 0xfe, 0x67,
	0x25, 0xe5, 0x58, 0x21, 0xf5, 0xd9, 0x18, 0xf9, 0x28, 0x57, 0x98, 0x59, 0x8f, 0xe0, 0x3a, 0xf7,
	0x4f, 0xd2, 0x2c, 0xbe, 0x18, 0xd1, 0x3b, 0xf2, 0xf9, 0x8b, 0x91, 0x78, 0x26, 0xd0, 0xd9, 0x9c,
	0x85, 0x1c, 0x87, 0xfe, 0x51, 0x09, 0x2e, 0x4d, 0x7b, 0xe1, 0x41, 0x3e, 0xce, 0x9d, 0x59, 0xf6,
	0x43, 0x94, 0xce, 0xb7, 0x4e, 0xd6, 0x10, 0x39, 0xfb, 0x83, 0x52, 0xfc, 0x09, 0xbe, 0x42, 0x4f,
	0xee, 0x4c, 0xdf, 0x69, 0xd1, 0x77, 0x24, 0x9d, 0xbb, 0x33, 0xb6, 0x48, 0xd5, 0xd5, 0xb0, 0x0e,
	0x56, 0x40, 0x57, 0x23, 0xe5, 0xca, 0x02, 0xba, 0x1a, 0x2d, 0xb2, 0x05, 0xa3, 0xa7, 0x3d, 0x9e,
	0xc8, 0x1c, 0x3d, 0xe3, 0x01, 0x49, 0xe6, 0xe8, 0x59, 0x2f, 0x33, 0xf4, 0x53, 0xe4, 0x8f, 0x4a,
	0xf1, 0xff, 0x65, 0xa0, 0x5e, 0x8c, 0x26, 0x05, 0xe4, 0x19, 0xbb, 0xf4, 0xdd, 0xb9, 0x37, 0x6b,
	0x13, 0xe4, 0xe3, 0x87, 0xe1, 0x0d, 0xbc, 0xf8, 0x6d, 0x55, 0x92, 0x2b, 0xd4, 0x94, 0x9b, 0xc1,
	0x9d, 0x3b, 0xb3, 0x35, 0x50, 0x18, 0x48, 0xbd, 0xe4, 0x4c, 0xf2, 0xe5, 0x9a, 0xbc, 0xf0, 0x9d,
	0xc9, 0x40, 0xe6, 0x1d, 0x6a, 0xfd, 0x14, 0x61, 0xf8, 0x7f, 0x30, 0x92, 0x17, 0x6e, 0x3f, 0xc8,
	0x9b, 0x4c, 0xec, 0x5e, 0x40, 0xe7, 0xc3, 0xe2, 0xc4, 0x09, 0xcf, 0x14, 0x19, 0x35, 0xdf, 0x33,
	0xc5, 0x87, 0xbd, 0x35, 0x03, 0xb5, 0x6a, 0xa0, 0x73, 0xee, 0x5a, 0x64, 0x1a, 0xe8, 0xfc, 0x6b,
	0x26, 0x99, 0x06, 0x7a, 0xca, 0xb5, 0x0e, 0xb9, 0x15, 0xb2, 0xcf, 0xa2, 0xd3, 0xb7, 0x42, 0xee,
	0x19, 0x79, 0xfa, 0x56, 0xc8, 0x3f, 0xee, 0x4e, 0xdd, 0x92, 0x91, 0x95, 0xb9, 0x3b, 0xdb, 0xd1,
	0x6c, 0xc1, 0x2d, 0x99, 0xb2, 0x46, 0x7f, 0x55, 0xca, 0x3b, 0xa0, 0xf6, 0x8d, 0x39, 0xf9, 0xf6,
	0x6c, 0x9d, 0x2b, 0x37, 0x3c, 0x3a, 0x9f, 0x9c, 0xb4, 0x69, 0xe0, 0x3b, 0x32, 0x6f, 0x3c, 0x91,
	0x3b, 0x85, 0x55, 0xd2, 0xd7, 0x9c, 0xbb, 0x33, 0xb6, 0x08, 0x16, 0x2b, 0xfb, 0x9c, 0x3f, 0x7d,
	0xb1, 0x72, 0x6f, 0x24, 0xa4, 0x2f, 0xd6, 0x94, 0xab, 0x04, 0xa7, 0xc8, 0x5f, 0x94, 0xe0, 0xe2,
	0x94, 0xf3, 0x79, 0x72, 0xff, 0x44, 0x87, 0xfa, 0xef, 0x3a, 0x1f, 0x9f, 0xf0, 0x32, 0x80, 0x7e,
	0x8a, 0x8c, 0x80, 0x24, 0x4f, 0x97, 0xc8, 0x8d, 0xe9, 0x7b, 0xd4, 0x5f, 0x94, 0x9b, 0x45, 0x49,
	0x95, 0xe1, 0x62, 0xa7, 0x28, 0x24, 0x3f, 0x4d, 0x50, 0x0f, 0x89, 0x32, 0x87, 0x4b, 0x39, 0x98,
	0x91, 0x42, 0x9f, 0x52, 0x4d, 0x25, 0xb9, 0xf6, 0x28, 0xbb, 0x22, 0xda, 0xf9, 0xf8, 0x44, 0xed,
	0x82, 0x8d, 0x3b, 0xbd, 0x58, 0x9a, 0xbe, 0x71, 0x0b, 0x95, 0x6b, 0xd3, 0x37, 0x6e, 0xc1, 0xfa,
	0xac, 0x1f, 0xf1, 0xa4, 0xd5, 0x0f, 0x32, 0x23, 0x9e, 0x8c, 0xf2, 0x63, 0x66, 0xc4, 0x93, 0x55,
	0x42, 0x0c, 0x1c, 0x7d, 0x6a, 0x4d, 0x22, 0xd3, 0xd1, 0x67, 0x55, 0xf6, 0x32, 0x1d, 0x7d, 0x66,
	0xd1, 0x2e, 0x60, 0x20, 0xb5, 0xa4, 0x94, 0xc9, 0x40, 0x56, 0xcd, 0x2a, 0x93, 0x81, 0xcc, 0x8a,
	0x95, 0x7e, 0x8a, 0x7c, 0x17, 0x16, 0xd5, 0xba, 0x0b, 0xb9, 0x52, 0xa0, 0x96, 0xd4, 0xb9, 0x3a,
	0x9d, 0x88, 0x77, 0xbe, 0x7d, 0xff, 0xb7, 0x7e, 0x6d, 0xe0, 0x0c, 0x4d, 0x7b, 0xb0, 0xf9, 0xd1,
	0x3d, 0xcf, 0xdb, 0xec, 0x39, 0xa3, 0xdb, 0xf8, 0x1f, 0xcf, 0x7a, 0xce, 0xf0, 0x36, 0xb3, 0xc6,
	0x87, 0xb4, 0x67, 0xa5, 0xfd, 0x5b, 0xb4, 0x37, 0xf3, 0x48, 0xf5, 0xab, 0xff, 0x17, 0x00, 0x00,
	0xff, 0xff, 0x76, 0x4b, 0xc7, 0x9b, 0x55, 0x4d, 0x00, 0x00,
}
