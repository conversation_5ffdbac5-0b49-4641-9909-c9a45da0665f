// Code generated by protoc-gen-gogo.
// source: src/thirdpartyauthgw/thirdpartyauthgw.proto
// DO NOT EDIT!

/*
	Package thirdpartyauthgw is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/thirdpartyauthgw/thirdpartyauthgw.proto

	It has these top-level messages:
		BaseResult
		VerifyAppleUserReq
		VerifyAppleUserResp
		VerifyWeixinUserReq
		VerifyWeixinUserResp
		VerifyQQUserReq
		VerifyQQUserResp
		VerifyQQMiniUserReq
		VerifyQQMiniUserResp
		VerifyWeixinMiniUserReq
		VerifyWeixinMiniUserResp
		LocalCheckPhoneReq
		LocalCheckPhoneResp
		HttpGetPhoneReq
		HttpGetPhoneResp
		LocalGetPhoneByTokenReq
		LocalGetPhoneByTokenResp
		LocalCleanTokenPhoneReq
		LocalCleanTokenPhoneResp
		GetCMCCPhoneReq
		GetCMCCPhoneResp
		GetCUCCPhoneReq
		GetCUCCPhoneResp
		GetChuanglanPhoneReq
		GetChuanglanPhoneResp
		VerifyQQUserExReq
		VerifyQQUserExResp
		GetQQUserInfoReq
		GetQQUserInfoResp
		QQUserInfo
		GetWeChatUserInfoReq
		GetWeChatUserInfoResp
		WeChatUserInfo
		VerifyQQMiniUserExReq
		VerifyQQMiniUserExResp
		VerifyWeChatMiniUserExReq
		VerifyWeChatMiniUserExResp
*/
package thirdpartyauthgw

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type PHONE_TYPE int32

const (
	PHONE_TYPE_PHONE_UNSPECIFIED  PHONE_TYPE = 0
	PHONE_TYPE_PHONE_CHUANG_LAN   PHONE_TYPE = 1
	PHONE_TYPE_PHONE_CHINA_MOBILE PHONE_TYPE = 2
)

var PHONE_TYPE_name = map[int32]string{
	0: "PHONE_UNSPECIFIED",
	1: "PHONE_CHUANG_LAN",
	2: "PHONE_CHINA_MOBILE",
}
var PHONE_TYPE_value = map[string]int32{
	"PHONE_UNSPECIFIED":  0,
	"PHONE_CHUANG_LAN":   1,
	"PHONE_CHINA_MOBILE": 2,
}

func (x PHONE_TYPE) Enum() *PHONE_TYPE {
	p := new(PHONE_TYPE)
	*p = x
	return p
}
func (x PHONE_TYPE) String() string {
	return proto.EnumName(PHONE_TYPE_name, int32(x))
}
func (x *PHONE_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PHONE_TYPE_value, data, "PHONE_TYPE")
	if err != nil {
		return err
	}
	*x = PHONE_TYPE(value)
	return nil
}
func (PHONE_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{0} }

type BaseResult struct {
	Code int32  `protobuf:"varint,1,req,name=code" json:"code"`
	Msg  string `protobuf:"bytes,2,opt,name=msg" json:"msg"`
}

func (m *BaseResult) Reset()                    { *m = BaseResult{} }
func (m *BaseResult) String() string            { return proto.CompactTextString(m) }
func (*BaseResult) ProtoMessage()               {}
func (*BaseResult) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{0} }

func (m *BaseResult) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BaseResult) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type VerifyAppleUserReq struct {
	AuthCode string `protobuf:"bytes,1,req,name=auth_code,json=authCode" json:"auth_code"`
	IdToken  string `protobuf:"bytes,2,opt,name=id_token,json=idToken" json:"id_token"`
	UserId   string `protobuf:"bytes,3,opt,name=user_id,json=userId" json:"user_id"`
}

func (m *VerifyAppleUserReq) Reset()         { *m = VerifyAppleUserReq{} }
func (m *VerifyAppleUserReq) String() string { return proto.CompactTextString(m) }
func (*VerifyAppleUserReq) ProtoMessage()    {}
func (*VerifyAppleUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{1}
}

func (m *VerifyAppleUserReq) GetAuthCode() string {
	if m != nil {
		return m.AuthCode
	}
	return ""
}

func (m *VerifyAppleUserReq) GetIdToken() string {
	if m != nil {
		return m.IdToken
	}
	return ""
}

func (m *VerifyAppleUserReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type VerifyAppleUserResp struct {
	BaseResult *BaseResult `protobuf:"bytes,1,opt,name=base_result,json=baseResult" json:"base_result,omitempty"`
	UserId     string      `protobuf:"bytes,2,opt,name=user_id,json=userId" json:"user_id"`
}

func (m *VerifyAppleUserResp) Reset()         { *m = VerifyAppleUserResp{} }
func (m *VerifyAppleUserResp) String() string { return proto.CompactTextString(m) }
func (*VerifyAppleUserResp) ProtoMessage()    {}
func (*VerifyAppleUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{2}
}

func (m *VerifyAppleUserResp) GetBaseResult() *BaseResult {
	if m != nil {
		return m.BaseResult
	}
	return nil
}

func (m *VerifyAppleUserResp) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

type VerifyWeixinUserReq struct {
	AccessToken string `protobuf:"bytes,1,req,name=access_token,json=accessToken" json:"access_token"`
	OpenId      []byte `protobuf:"bytes,2,req,name=open_id,json=openId" json:"open_id"`
}

func (m *VerifyWeixinUserReq) Reset()         { *m = VerifyWeixinUserReq{} }
func (m *VerifyWeixinUserReq) String() string { return proto.CompactTextString(m) }
func (*VerifyWeixinUserReq) ProtoMessage()    {}
func (*VerifyWeixinUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{3}
}

func (m *VerifyWeixinUserReq) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

func (m *VerifyWeixinUserReq) GetOpenId() []byte {
	if m != nil {
		return m.OpenId
	}
	return nil
}

type VerifyWeixinUserResp struct {
	Data []byte `protobuf:"bytes,1,opt,name=data" json:"data"`
}

func (m *VerifyWeixinUserResp) Reset()         { *m = VerifyWeixinUserResp{} }
func (m *VerifyWeixinUserResp) String() string { return proto.CompactTextString(m) }
func (*VerifyWeixinUserResp) ProtoMessage()    {}
func (*VerifyWeixinUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{4}
}

func (m *VerifyWeixinUserResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type VerifyQQUserReq struct {
	AppId       string `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id"`
	AccessToken string `protobuf:"bytes,2,req,name=access_token,json=accessToken" json:"access_token"`
	OpenId      []byte `protobuf:"bytes,3,req,name=open_id,json=openId" json:"open_id"`
}

func (m *VerifyQQUserReq) Reset()                    { *m = VerifyQQUserReq{} }
func (m *VerifyQQUserReq) String() string            { return proto.CompactTextString(m) }
func (*VerifyQQUserReq) ProtoMessage()               {}
func (*VerifyQQUserReq) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{5} }

func (m *VerifyQQUserReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *VerifyQQUserReq) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

func (m *VerifyQQUserReq) GetOpenId() []byte {
	if m != nil {
		return m.OpenId
	}
	return nil
}

type VerifyQQUserResp struct {
	OauthMe []byte `protobuf:"bytes,1,opt,name=oauth_me,json=oauthMe" json:"oauth_me"`
	Data    []byte `protobuf:"bytes,2,opt,name=data" json:"data"`
}

func (m *VerifyQQUserResp) Reset()                    { *m = VerifyQQUserResp{} }
func (m *VerifyQQUserResp) String() string            { return proto.CompactTextString(m) }
func (*VerifyQQUserResp) ProtoMessage()               {}
func (*VerifyQQUserResp) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{6} }

func (m *VerifyQQUserResp) GetOauthMe() []byte {
	if m != nil {
		return m.OauthMe
	}
	return nil
}

func (m *VerifyQQUserResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type VerifyQQMiniUserReq struct {
	AppId  string `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id"`
	Secret string `protobuf:"bytes,2,req,name=secret" json:"secret"`
	Jscode string `protobuf:"bytes,3,req,name=jscode" json:"jscode"`
}

func (m *VerifyQQMiniUserReq) Reset()         { *m = VerifyQQMiniUserReq{} }
func (m *VerifyQQMiniUserReq) String() string { return proto.CompactTextString(m) }
func (*VerifyQQMiniUserReq) ProtoMessage()    {}
func (*VerifyQQMiniUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{7}
}

func (m *VerifyQQMiniUserReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *VerifyQQMiniUserReq) GetSecret() string {
	if m != nil {
		return m.Secret
	}
	return ""
}

func (m *VerifyQQMiniUserReq) GetJscode() string {
	if m != nil {
		return m.Jscode
	}
	return ""
}

type VerifyQQMiniUserResp struct {
	Data []byte `protobuf:"bytes,1,opt,name=data" json:"data"`
}

func (m *VerifyQQMiniUserResp) Reset()         { *m = VerifyQQMiniUserResp{} }
func (m *VerifyQQMiniUserResp) String() string { return proto.CompactTextString(m) }
func (*VerifyQQMiniUserResp) ProtoMessage()    {}
func (*VerifyQQMiniUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{8}
}

func (m *VerifyQQMiniUserResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type VerifyWeixinMiniUserReq struct {
	AppId  string `protobuf:"bytes,1,req,name=app_id,json=appId" json:"app_id"`
	Secret string `protobuf:"bytes,2,req,name=secret" json:"secret"`
	Jscode string `protobuf:"bytes,3,req,name=jscode" json:"jscode"`
}

func (m *VerifyWeixinMiniUserReq) Reset()         { *m = VerifyWeixinMiniUserReq{} }
func (m *VerifyWeixinMiniUserReq) String() string { return proto.CompactTextString(m) }
func (*VerifyWeixinMiniUserReq) ProtoMessage()    {}
func (*VerifyWeixinMiniUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{9}
}

func (m *VerifyWeixinMiniUserReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *VerifyWeixinMiniUserReq) GetSecret() string {
	if m != nil {
		return m.Secret
	}
	return ""
}

func (m *VerifyWeixinMiniUserReq) GetJscode() string {
	if m != nil {
		return m.Jscode
	}
	return ""
}

type VerifyWeixinMiniUserResp struct {
	Data []byte `protobuf:"bytes,1,opt,name=data" json:"data"`
}

func (m *VerifyWeixinMiniUserResp) Reset()         { *m = VerifyWeixinMiniUserResp{} }
func (m *VerifyWeixinMiniUserResp) String() string { return proto.CompactTextString(m) }
func (*VerifyWeixinMiniUserResp) ProtoMessage()    {}
func (*VerifyWeixinMiniUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{10}
}

func (m *VerifyWeixinMiniUserResp) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

type LocalCheckPhoneReq struct {
	Type  PHONE_TYPE `protobuf:"varint,1,req,name=type,enum=thirdpartyauthgw.PHONE_TYPE" json:"type"`
	Token string     `protobuf:"bytes,2,req,name=token" json:"token"`
	Phone string     `protobuf:"bytes,3,req,name=phone" json:"phone"`
}

func (m *LocalCheckPhoneReq) Reset()         { *m = LocalCheckPhoneReq{} }
func (m *LocalCheckPhoneReq) String() string { return proto.CompactTextString(m) }
func (*LocalCheckPhoneReq) ProtoMessage()    {}
func (*LocalCheckPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{11}
}

func (m *LocalCheckPhoneReq) GetType() PHONE_TYPE {
	if m != nil {
		return m.Type
	}
	return PHONE_TYPE_PHONE_UNSPECIFIED
}

func (m *LocalCheckPhoneReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *LocalCheckPhoneReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type LocalCheckPhoneResp struct {
	IsPass bool `protobuf:"varint,1,req,name=is_pass,json=isPass" json:"is_pass"`
}

func (m *LocalCheckPhoneResp) Reset()         { *m = LocalCheckPhoneResp{} }
func (m *LocalCheckPhoneResp) String() string { return proto.CompactTextString(m) }
func (*LocalCheckPhoneResp) ProtoMessage()    {}
func (*LocalCheckPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{12}
}

func (m *LocalCheckPhoneResp) GetIsPass() bool {
	if m != nil {
		return m.IsPass
	}
	return false
}

type HttpGetPhoneReq struct {
	Type         PHONE_TYPE `protobuf:"varint,1,req,name=type,enum=thirdpartyauthgw.PHONE_TYPE" json:"type"`
	SpecialAppid string     `protobuf:"bytes,2,opt,name=special_appid,json=specialAppid" json:"special_appid"`
	ClientType   int32      `protobuf:"varint,3,req,name=client_type,json=clientType" json:"client_type"`
	Token        string     `protobuf:"bytes,4,req,name=token" json:"token"`
}

func (m *HttpGetPhoneReq) Reset()                    { *m = HttpGetPhoneReq{} }
func (m *HttpGetPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*HttpGetPhoneReq) ProtoMessage()               {}
func (*HttpGetPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{13} }

func (m *HttpGetPhoneReq) GetType() PHONE_TYPE {
	if m != nil {
		return m.Type
	}
	return PHONE_TYPE_PHONE_UNSPECIFIED
}

func (m *HttpGetPhoneReq) GetSpecialAppid() string {
	if m != nil {
		return m.SpecialAppid
	}
	return ""
}

func (m *HttpGetPhoneReq) GetClientType() int32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *HttpGetPhoneReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type HttpGetPhoneResp struct {
	Phone string `protobuf:"bytes,1,req,name=phone" json:"phone"`
}

func (m *HttpGetPhoneResp) Reset()         { *m = HttpGetPhoneResp{} }
func (m *HttpGetPhoneResp) String() string { return proto.CompactTextString(m) }
func (*HttpGetPhoneResp) ProtoMessage()    {}
func (*HttpGetPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{14}
}

func (m *HttpGetPhoneResp) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type LocalGetPhoneByTokenReq struct {
	Type  PHONE_TYPE `protobuf:"varint,1,req,name=type,enum=thirdpartyauthgw.PHONE_TYPE" json:"type"`
	Token string     `protobuf:"bytes,2,req,name=token" json:"token"`
}

func (m *LocalGetPhoneByTokenReq) Reset()         { *m = LocalGetPhoneByTokenReq{} }
func (m *LocalGetPhoneByTokenReq) String() string { return proto.CompactTextString(m) }
func (*LocalGetPhoneByTokenReq) ProtoMessage()    {}
func (*LocalGetPhoneByTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{15}
}

func (m *LocalGetPhoneByTokenReq) GetType() PHONE_TYPE {
	if m != nil {
		return m.Type
	}
	return PHONE_TYPE_PHONE_UNSPECIFIED
}

func (m *LocalGetPhoneByTokenReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type LocalGetPhoneByTokenResp struct {
	Phone string `protobuf:"bytes,1,req,name=phone" json:"phone"`
}

func (m *LocalGetPhoneByTokenResp) Reset()         { *m = LocalGetPhoneByTokenResp{} }
func (m *LocalGetPhoneByTokenResp) String() string { return proto.CompactTextString(m) }
func (*LocalGetPhoneByTokenResp) ProtoMessage()    {}
func (*LocalGetPhoneByTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{16}
}

func (m *LocalGetPhoneByTokenResp) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type LocalCleanTokenPhoneReq struct {
	Type  PHONE_TYPE `protobuf:"varint,1,req,name=type,enum=thirdpartyauthgw.PHONE_TYPE" json:"type"`
	Token string     `protobuf:"bytes,2,req,name=token" json:"token"`
	Phone string     `protobuf:"bytes,3,req,name=phone" json:"phone"`
}

func (m *LocalCleanTokenPhoneReq) Reset()         { *m = LocalCleanTokenPhoneReq{} }
func (m *LocalCleanTokenPhoneReq) String() string { return proto.CompactTextString(m) }
func (*LocalCleanTokenPhoneReq) ProtoMessage()    {}
func (*LocalCleanTokenPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{17}
}

func (m *LocalCleanTokenPhoneReq) GetType() PHONE_TYPE {
	if m != nil {
		return m.Type
	}
	return PHONE_TYPE_PHONE_UNSPECIFIED
}

func (m *LocalCleanTokenPhoneReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *LocalCleanTokenPhoneReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type LocalCleanTokenPhoneResp struct {
}

func (m *LocalCleanTokenPhoneResp) Reset()         { *m = LocalCleanTokenPhoneResp{} }
func (m *LocalCleanTokenPhoneResp) String() string { return proto.CompactTextString(m) }
func (*LocalCleanTokenPhoneResp) ProtoMessage()    {}
func (*LocalCleanTokenPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{18}
}

type GetCMCCPhoneReq struct {
	Appid string `protobuf:"bytes,1,req,name=appid" json:"appid"`
	Token string `protobuf:"bytes,2,req,name=token" json:"token"`
}

func (m *GetCMCCPhoneReq) Reset()                    { *m = GetCMCCPhoneReq{} }
func (m *GetCMCCPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*GetCMCCPhoneReq) ProtoMessage()               {}
func (*GetCMCCPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{19} }

func (m *GetCMCCPhoneReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *GetCMCCPhoneReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetCMCCPhoneResp struct {
	Phone string `protobuf:"bytes,1,opt,name=phone" json:"phone"`
}

func (m *GetCMCCPhoneResp) Reset()         { *m = GetCMCCPhoneResp{} }
func (m *GetCMCCPhoneResp) String() string { return proto.CompactTextString(m) }
func (*GetCMCCPhoneResp) ProtoMessage()    {}
func (*GetCMCCPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{20}
}

func (m *GetCMCCPhoneResp) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type GetCUCCPhoneReq struct {
	AccessCode string `protobuf:"bytes,1,req,name=access_code,json=accessCode" json:"access_code"`
	Apikey     string `protobuf:"bytes,2,opt,name=apikey" json:"apikey"`
	Md5        string `protobuf:"bytes,3,opt,name=md5" json:"md5"`
}

func (m *GetCUCCPhoneReq) Reset()                    { *m = GetCUCCPhoneReq{} }
func (m *GetCUCCPhoneReq) String() string            { return proto.CompactTextString(m) }
func (*GetCUCCPhoneReq) ProtoMessage()               {}
func (*GetCUCCPhoneReq) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{21} }

func (m *GetCUCCPhoneReq) GetAccessCode() string {
	if m != nil {
		return m.AccessCode
	}
	return ""
}

func (m *GetCUCCPhoneReq) GetApikey() string {
	if m != nil {
		return m.Apikey
	}
	return ""
}

func (m *GetCUCCPhoneReq) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type GetCUCCPhoneResp struct {
	Phone string `protobuf:"bytes,1,opt,name=phone" json:"phone"`
}

func (m *GetCUCCPhoneResp) Reset()         { *m = GetCUCCPhoneResp{} }
func (m *GetCUCCPhoneResp) String() string { return proto.CompactTextString(m) }
func (*GetCUCCPhoneResp) ProtoMessage()    {}
func (*GetCUCCPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{22}
}

func (m *GetCUCCPhoneResp) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type GetChuanglanPhoneReq struct {
	Appid string `protobuf:"bytes,1,req,name=appid" json:"appid"`
	Token string `protobuf:"bytes,2,req,name=token" json:"token"`
}

func (m *GetChuanglanPhoneReq) Reset()         { *m = GetChuanglanPhoneReq{} }
func (m *GetChuanglanPhoneReq) String() string { return proto.CompactTextString(m) }
func (*GetChuanglanPhoneReq) ProtoMessage()    {}
func (*GetChuanglanPhoneReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{23}
}

func (m *GetChuanglanPhoneReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *GetChuanglanPhoneReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type GetChuanglanPhoneResp struct {
	Phone string `protobuf:"bytes,1,opt,name=phone" json:"phone"`
}

func (m *GetChuanglanPhoneResp) Reset()         { *m = GetChuanglanPhoneResp{} }
func (m *GetChuanglanPhoneResp) String() string { return proto.CompactTextString(m) }
func (*GetChuanglanPhoneResp) ProtoMessage()    {}
func (*GetChuanglanPhoneResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{24}
}

func (m *GetChuanglanPhoneResp) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

type VerifyQQUserExReq struct {
	AccessToken string `protobuf:"bytes,1,opt,name=access_token,json=accessToken" json:"access_token"`
}

func (m *VerifyQQUserExReq) Reset()         { *m = VerifyQQUserExReq{} }
func (m *VerifyQQUserExReq) String() string { return proto.CompactTextString(m) }
func (*VerifyQQUserExReq) ProtoMessage()    {}
func (*VerifyQQUserExReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{25}
}

func (m *VerifyQQUserExReq) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

type VerifyQQUserExResp struct {
	Appid   string `protobuf:"bytes,1,opt,name=appid" json:"appid"`
	Openid  string `protobuf:"bytes,2,opt,name=openid" json:"openid"`
	Unionid string `protobuf:"bytes,3,opt,name=unionid" json:"unionid"`
}

func (m *VerifyQQUserExResp) Reset()         { *m = VerifyQQUserExResp{} }
func (m *VerifyQQUserExResp) String() string { return proto.CompactTextString(m) }
func (*VerifyQQUserExResp) ProtoMessage()    {}
func (*VerifyQQUserExResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{26}
}

func (m *VerifyQQUserExResp) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *VerifyQQUserExResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *VerifyQQUserExResp) GetUnionid() string {
	if m != nil {
		return m.Unionid
	}
	return ""
}

type GetQQUserInfoReq struct {
	Appid       string `protobuf:"bytes,1,opt,name=appid" json:"appid"`
	Openid      string `protobuf:"bytes,2,opt,name=openid" json:"openid"`
	AccessToken string `protobuf:"bytes,3,opt,name=access_token,json=accessToken" json:"access_token"`
}

func (m *GetQQUserInfoReq) Reset()         { *m = GetQQUserInfoReq{} }
func (m *GetQQUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetQQUserInfoReq) ProtoMessage()    {}
func (*GetQQUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{27}
}

func (m *GetQQUserInfoReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *GetQQUserInfoReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetQQUserInfoReq) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

type GetQQUserInfoResp struct {
	Info *QQUserInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
}

func (m *GetQQUserInfoResp) Reset()         { *m = GetQQUserInfoResp{} }
func (m *GetQQUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetQQUserInfoResp) ProtoMessage()    {}
func (*GetQQUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{28}
}

func (m *GetQQUserInfoResp) GetInfo() *QQUserInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type QQUserInfo struct {
	Appid         string `protobuf:"bytes,1,opt,name=appid" json:"appid"`
	Openid        string `protobuf:"bytes,2,opt,name=openid" json:"openid"`
	Nickname      string `protobuf:"bytes,3,opt,name=nickname" json:"nickname"`
	Gender        string `protobuf:"bytes,4,opt,name=gender" json:"gender"`
	FigureurlQq_1 string `protobuf:"bytes,5,opt,name=figureurl_qq_1,json=figureurlQq1" json:"figureurl_qq_1"`
	FigureurlQq_2 string `protobuf:"bytes,6,opt,name=figureurl_qq_2,json=figureurlQq2" json:"figureurl_qq_2"`
}

func (m *QQUserInfo) Reset()                    { *m = QQUserInfo{} }
func (m *QQUserInfo) String() string            { return proto.CompactTextString(m) }
func (*QQUserInfo) ProtoMessage()               {}
func (*QQUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{29} }

func (m *QQUserInfo) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *QQUserInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *QQUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *QQUserInfo) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *QQUserInfo) GetFigureurlQq_1() string {
	if m != nil {
		return m.FigureurlQq_1
	}
	return ""
}

func (m *QQUserInfo) GetFigureurlQq_2() string {
	if m != nil {
		return m.FigureurlQq_2
	}
	return ""
}

type GetWeChatUserInfoReq struct {
	Openid      string `protobuf:"bytes,1,opt,name=openid" json:"openid"`
	AccessToken string `protobuf:"bytes,2,opt,name=access_token,json=accessToken" json:"access_token"`
}

func (m *GetWeChatUserInfoReq) Reset()         { *m = GetWeChatUserInfoReq{} }
func (m *GetWeChatUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetWeChatUserInfoReq) ProtoMessage()    {}
func (*GetWeChatUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{30}
}

func (m *GetWeChatUserInfoReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *GetWeChatUserInfoReq) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

type GetWeChatUserInfoResp struct {
	Info *WeChatUserInfo `protobuf:"bytes,1,opt,name=info" json:"info,omitempty"`
}

func (m *GetWeChatUserInfoResp) Reset()         { *m = GetWeChatUserInfoResp{} }
func (m *GetWeChatUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetWeChatUserInfoResp) ProtoMessage()    {}
func (*GetWeChatUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{31}
}

func (m *GetWeChatUserInfoResp) GetInfo() *WeChatUserInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type WeChatUserInfo struct {
	Openid    string `protobuf:"bytes,1,opt,name=openid" json:"openid"`
	Unionid   string `protobuf:"bytes,2,opt,name=unionid" json:"unionid"`
	Nickname  string `protobuf:"bytes,3,opt,name=nickname" json:"nickname"`
	Gender    string `protobuf:"bytes,4,opt,name=gender" json:"gender"`
	Headimage string `protobuf:"bytes,5,opt,name=headimage" json:"headimage"`
}

func (m *WeChatUserInfo) Reset()                    { *m = WeChatUserInfo{} }
func (m *WeChatUserInfo) String() string            { return proto.CompactTextString(m) }
func (*WeChatUserInfo) ProtoMessage()               {}
func (*WeChatUserInfo) Descriptor() ([]byte, []int) { return fileDescriptorThirdpartyauthgw, []int{32} }

func (m *WeChatUserInfo) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *WeChatUserInfo) GetUnionid() string {
	if m != nil {
		return m.Unionid
	}
	return ""
}

func (m *WeChatUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *WeChatUserInfo) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *WeChatUserInfo) GetHeadimage() string {
	if m != nil {
		return m.Headimage
	}
	return ""
}

type VerifyQQMiniUserExReq struct {
	Appid  string `protobuf:"bytes,1,opt,name=appid" json:"appid"`
	Jscode string `protobuf:"bytes,2,opt,name=jscode" json:"jscode"`
}

func (m *VerifyQQMiniUserExReq) Reset()         { *m = VerifyQQMiniUserExReq{} }
func (m *VerifyQQMiniUserExReq) String() string { return proto.CompactTextString(m) }
func (*VerifyQQMiniUserExReq) ProtoMessage()    {}
func (*VerifyQQMiniUserExReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{33}
}

func (m *VerifyQQMiniUserExReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *VerifyQQMiniUserExReq) GetJscode() string {
	if m != nil {
		return m.Jscode
	}
	return ""
}

type VerifyQQMiniUserExResp struct {
	Openid  string `protobuf:"bytes,1,opt,name=openid" json:"openid"`
	Unionid string `protobuf:"bytes,2,opt,name=unionid" json:"unionid"`
}

func (m *VerifyQQMiniUserExResp) Reset()         { *m = VerifyQQMiniUserExResp{} }
func (m *VerifyQQMiniUserExResp) String() string { return proto.CompactTextString(m) }
func (*VerifyQQMiniUserExResp) ProtoMessage()    {}
func (*VerifyQQMiniUserExResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{34}
}

func (m *VerifyQQMiniUserExResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *VerifyQQMiniUserExResp) GetUnionid() string {
	if m != nil {
		return m.Unionid
	}
	return ""
}

type VerifyWeChatMiniUserExReq struct {
	Appid  string `protobuf:"bytes,1,opt,name=appid" json:"appid"`
	Jscode string `protobuf:"bytes,2,opt,name=jscode" json:"jscode"`
}

func (m *VerifyWeChatMiniUserExReq) Reset()         { *m = VerifyWeChatMiniUserExReq{} }
func (m *VerifyWeChatMiniUserExReq) String() string { return proto.CompactTextString(m) }
func (*VerifyWeChatMiniUserExReq) ProtoMessage()    {}
func (*VerifyWeChatMiniUserExReq) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{35}
}

func (m *VerifyWeChatMiniUserExReq) GetAppid() string {
	if m != nil {
		return m.Appid
	}
	return ""
}

func (m *VerifyWeChatMiniUserExReq) GetJscode() string {
	if m != nil {
		return m.Jscode
	}
	return ""
}

type VerifyWeChatMiniUserExResp struct {
	Openid  string `protobuf:"bytes,1,opt,name=openid" json:"openid"`
	Unionid string `protobuf:"bytes,2,opt,name=unionid" json:"unionid"`
}

func (m *VerifyWeChatMiniUserExResp) Reset()         { *m = VerifyWeChatMiniUserExResp{} }
func (m *VerifyWeChatMiniUserExResp) String() string { return proto.CompactTextString(m) }
func (*VerifyWeChatMiniUserExResp) ProtoMessage()    {}
func (*VerifyWeChatMiniUserExResp) Descriptor() ([]byte, []int) {
	return fileDescriptorThirdpartyauthgw, []int{36}
}

func (m *VerifyWeChatMiniUserExResp) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *VerifyWeChatMiniUserExResp) GetUnionid() string {
	if m != nil {
		return m.Unionid
	}
	return ""
}

func init() {
	proto.RegisterType((*BaseResult)(nil), "thirdpartyauthgw.BaseResult")
	proto.RegisterType((*VerifyAppleUserReq)(nil), "thirdpartyauthgw.VerifyAppleUserReq")
	proto.RegisterType((*VerifyAppleUserResp)(nil), "thirdpartyauthgw.VerifyAppleUserResp")
	proto.RegisterType((*VerifyWeixinUserReq)(nil), "thirdpartyauthgw.VerifyWeixinUserReq")
	proto.RegisterType((*VerifyWeixinUserResp)(nil), "thirdpartyauthgw.VerifyWeixinUserResp")
	proto.RegisterType((*VerifyQQUserReq)(nil), "thirdpartyauthgw.VerifyQQUserReq")
	proto.RegisterType((*VerifyQQUserResp)(nil), "thirdpartyauthgw.VerifyQQUserResp")
	proto.RegisterType((*VerifyQQMiniUserReq)(nil), "thirdpartyauthgw.VerifyQQMiniUserReq")
	proto.RegisterType((*VerifyQQMiniUserResp)(nil), "thirdpartyauthgw.VerifyQQMiniUserResp")
	proto.RegisterType((*VerifyWeixinMiniUserReq)(nil), "thirdpartyauthgw.VerifyWeixinMiniUserReq")
	proto.RegisterType((*VerifyWeixinMiniUserResp)(nil), "thirdpartyauthgw.VerifyWeixinMiniUserResp")
	proto.RegisterType((*LocalCheckPhoneReq)(nil), "thirdpartyauthgw.LocalCheckPhoneReq")
	proto.RegisterType((*LocalCheckPhoneResp)(nil), "thirdpartyauthgw.LocalCheckPhoneResp")
	proto.RegisterType((*HttpGetPhoneReq)(nil), "thirdpartyauthgw.HttpGetPhoneReq")
	proto.RegisterType((*HttpGetPhoneResp)(nil), "thirdpartyauthgw.HttpGetPhoneResp")
	proto.RegisterType((*LocalGetPhoneByTokenReq)(nil), "thirdpartyauthgw.LocalGetPhoneByTokenReq")
	proto.RegisterType((*LocalGetPhoneByTokenResp)(nil), "thirdpartyauthgw.LocalGetPhoneByTokenResp")
	proto.RegisterType((*LocalCleanTokenPhoneReq)(nil), "thirdpartyauthgw.LocalCleanTokenPhoneReq")
	proto.RegisterType((*LocalCleanTokenPhoneResp)(nil), "thirdpartyauthgw.LocalCleanTokenPhoneResp")
	proto.RegisterType((*GetCMCCPhoneReq)(nil), "thirdpartyauthgw.GetCMCCPhoneReq")
	proto.RegisterType((*GetCMCCPhoneResp)(nil), "thirdpartyauthgw.GetCMCCPhoneResp")
	proto.RegisterType((*GetCUCCPhoneReq)(nil), "thirdpartyauthgw.GetCUCCPhoneReq")
	proto.RegisterType((*GetCUCCPhoneResp)(nil), "thirdpartyauthgw.GetCUCCPhoneResp")
	proto.RegisterType((*GetChuanglanPhoneReq)(nil), "thirdpartyauthgw.GetChuanglanPhoneReq")
	proto.RegisterType((*GetChuanglanPhoneResp)(nil), "thirdpartyauthgw.GetChuanglanPhoneResp")
	proto.RegisterType((*VerifyQQUserExReq)(nil), "thirdpartyauthgw.VerifyQQUserExReq")
	proto.RegisterType((*VerifyQQUserExResp)(nil), "thirdpartyauthgw.VerifyQQUserExResp")
	proto.RegisterType((*GetQQUserInfoReq)(nil), "thirdpartyauthgw.GetQQUserInfoReq")
	proto.RegisterType((*GetQQUserInfoResp)(nil), "thirdpartyauthgw.GetQQUserInfoResp")
	proto.RegisterType((*QQUserInfo)(nil), "thirdpartyauthgw.QQUserInfo")
	proto.RegisterType((*GetWeChatUserInfoReq)(nil), "thirdpartyauthgw.GetWeChatUserInfoReq")
	proto.RegisterType((*GetWeChatUserInfoResp)(nil), "thirdpartyauthgw.GetWeChatUserInfoResp")
	proto.RegisterType((*WeChatUserInfo)(nil), "thirdpartyauthgw.WeChatUserInfo")
	proto.RegisterType((*VerifyQQMiniUserExReq)(nil), "thirdpartyauthgw.VerifyQQMiniUserExReq")
	proto.RegisterType((*VerifyQQMiniUserExResp)(nil), "thirdpartyauthgw.VerifyQQMiniUserExResp")
	proto.RegisterType((*VerifyWeChatMiniUserExReq)(nil), "thirdpartyauthgw.VerifyWeChatMiniUserExReq")
	proto.RegisterType((*VerifyWeChatMiniUserExResp)(nil), "thirdpartyauthgw.VerifyWeChatMiniUserExResp")
	proto.RegisterEnum("thirdpartyauthgw.PHONE_TYPE", PHONE_TYPE_name, PHONE_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ThirdpartyAuthGw service

type ThirdpartyAuthGwClient interface {
	VerifyAppleUser(ctx context.Context, in *VerifyAppleUserReq, opts ...grpc.CallOption) (*VerifyAppleUserResp, error)
	VerifyWeixinUser(ctx context.Context, in *VerifyWeixinUserReq, opts ...grpc.CallOption) (*VerifyWeixinUserResp, error)
	VerifyQQUser(ctx context.Context, in *VerifyQQUserReq, opts ...grpc.CallOption) (*VerifyQQUserResp, error)
	VerifyQQMiniUser(ctx context.Context, in *VerifyQQMiniUserReq, opts ...grpc.CallOption) (*VerifyQQMiniUserResp, error)
	VerifyWeixinMiniUser(ctx context.Context, in *VerifyWeixinMiniUserReq, opts ...grpc.CallOption) (*VerifyWeixinMiniUserResp, error)
	LocalCheckPhone(ctx context.Context, in *LocalCheckPhoneReq, opts ...grpc.CallOption) (*LocalCheckPhoneResp, error)
	HttpGetPhone(ctx context.Context, in *HttpGetPhoneReq, opts ...grpc.CallOption) (*HttpGetPhoneResp, error)
	LocalGetPhoneByToken(ctx context.Context, in *LocalGetPhoneByTokenReq, opts ...grpc.CallOption) (*LocalGetPhoneByTokenResp, error)
	LocalCleanTokenPhone(ctx context.Context, in *LocalCleanTokenPhoneReq, opts ...grpc.CallOption) (*LocalCleanTokenPhoneResp, error)
	// 中国移动 获取手机号
	GetCMCCPhone(ctx context.Context, in *GetCMCCPhoneReq, opts ...grpc.CallOption) (*GetCMCCPhoneResp, error)
	// 中国联通 置换手机号
	GetCUCCPhone(ctx context.Context, in *GetCUCCPhoneReq, opts ...grpc.CallOption) (*GetCUCCPhoneResp, error)
	// 创蓝
	GetChuanglanPhone(ctx context.Context, in *GetChuanglanPhoneReq, opts ...grpc.CallOption) (*GetChuanglanPhoneResp, error)
	VerifyQQUserEx(ctx context.Context, in *VerifyQQUserExReq, opts ...grpc.CallOption) (*VerifyQQUserExResp, error)
	GetQQUserInfo(ctx context.Context, in *GetQQUserInfoReq, opts ...grpc.CallOption) (*GetQQUserInfoResp, error)
	// reserved : WeChat verify
	//
	GetWeChatUserInfo(ctx context.Context, in *GetWeChatUserInfoReq, opts ...grpc.CallOption) (*GetWeChatUserInfoResp, error)
	// Mini
	VerifyQQMiniUserEx(ctx context.Context, in *VerifyQQMiniUserExReq, opts ...grpc.CallOption) (*VerifyQQMiniUserExResp, error)
	VerifyWeChatMiniUserEx(ctx context.Context, in *VerifyWeChatMiniUserExReq, opts ...grpc.CallOption) (*VerifyWeChatMiniUserExResp, error)
}

type thirdpartyAuthGwClient struct {
	cc *grpc.ClientConn
}

func NewThirdpartyAuthGwClient(cc *grpc.ClientConn) ThirdpartyAuthGwClient {
	return &thirdpartyAuthGwClient{cc}
}

func (c *thirdpartyAuthGwClient) VerifyAppleUser(ctx context.Context, in *VerifyAppleUserReq, opts ...grpc.CallOption) (*VerifyAppleUserResp, error) {
	out := new(VerifyAppleUserResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyAppleUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyWeixinUser(ctx context.Context, in *VerifyWeixinUserReq, opts ...grpc.CallOption) (*VerifyWeixinUserResp, error) {
	out := new(VerifyWeixinUserResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyWeixinUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyQQUser(ctx context.Context, in *VerifyQQUserReq, opts ...grpc.CallOption) (*VerifyQQUserResp, error) {
	out := new(VerifyQQUserResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyQQMiniUser(ctx context.Context, in *VerifyQQMiniUserReq, opts ...grpc.CallOption) (*VerifyQQMiniUserResp, error) {
	out := new(VerifyQQMiniUserResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQMiniUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyWeixinMiniUser(ctx context.Context, in *VerifyWeixinMiniUserReq, opts ...grpc.CallOption) (*VerifyWeixinMiniUserResp, error) {
	out := new(VerifyWeixinMiniUserResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyWeixinMiniUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) LocalCheckPhone(ctx context.Context, in *LocalCheckPhoneReq, opts ...grpc.CallOption) (*LocalCheckPhoneResp, error) {
	out := new(LocalCheckPhoneResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/LocalCheckPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) HttpGetPhone(ctx context.Context, in *HttpGetPhoneReq, opts ...grpc.CallOption) (*HttpGetPhoneResp, error) {
	out := new(HttpGetPhoneResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/HttpGetPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) LocalGetPhoneByToken(ctx context.Context, in *LocalGetPhoneByTokenReq, opts ...grpc.CallOption) (*LocalGetPhoneByTokenResp, error) {
	out := new(LocalGetPhoneByTokenResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/LocalGetPhoneByToken", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) LocalCleanTokenPhone(ctx context.Context, in *LocalCleanTokenPhoneReq, opts ...grpc.CallOption) (*LocalCleanTokenPhoneResp, error) {
	out := new(LocalCleanTokenPhoneResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/LocalCleanTokenPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) GetCMCCPhone(ctx context.Context, in *GetCMCCPhoneReq, opts ...grpc.CallOption) (*GetCMCCPhoneResp, error) {
	out := new(GetCMCCPhoneResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/GetCMCCPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) GetCUCCPhone(ctx context.Context, in *GetCUCCPhoneReq, opts ...grpc.CallOption) (*GetCUCCPhoneResp, error) {
	out := new(GetCUCCPhoneResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/GetCUCCPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) GetChuanglanPhone(ctx context.Context, in *GetChuanglanPhoneReq, opts ...grpc.CallOption) (*GetChuanglanPhoneResp, error) {
	out := new(GetChuanglanPhoneResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/GetChuanglanPhone", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyQQUserEx(ctx context.Context, in *VerifyQQUserExReq, opts ...grpc.CallOption) (*VerifyQQUserExResp, error) {
	out := new(VerifyQQUserExResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQUserEx", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) GetQQUserInfo(ctx context.Context, in *GetQQUserInfoReq, opts ...grpc.CallOption) (*GetQQUserInfoResp, error) {
	out := new(GetQQUserInfoResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/GetQQUserInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) GetWeChatUserInfo(ctx context.Context, in *GetWeChatUserInfoReq, opts ...grpc.CallOption) (*GetWeChatUserInfoResp, error) {
	out := new(GetWeChatUserInfoResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/GetWeChatUserInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyQQMiniUserEx(ctx context.Context, in *VerifyQQMiniUserExReq, opts ...grpc.CallOption) (*VerifyQQMiniUserExResp, error) {
	out := new(VerifyQQMiniUserExResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQMiniUserEx", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *thirdpartyAuthGwClient) VerifyWeChatMiniUserEx(ctx context.Context, in *VerifyWeChatMiniUserExReq, opts ...grpc.CallOption) (*VerifyWeChatMiniUserExResp, error) {
	out := new(VerifyWeChatMiniUserExResp)
	err := grpc.Invoke(ctx, "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyWeChatMiniUserEx", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ThirdpartyAuthGw service

type ThirdpartyAuthGwServer interface {
	VerifyAppleUser(context.Context, *VerifyAppleUserReq) (*VerifyAppleUserResp, error)
	VerifyWeixinUser(context.Context, *VerifyWeixinUserReq) (*VerifyWeixinUserResp, error)
	VerifyQQUser(context.Context, *VerifyQQUserReq) (*VerifyQQUserResp, error)
	VerifyQQMiniUser(context.Context, *VerifyQQMiniUserReq) (*VerifyQQMiniUserResp, error)
	VerifyWeixinMiniUser(context.Context, *VerifyWeixinMiniUserReq) (*VerifyWeixinMiniUserResp, error)
	LocalCheckPhone(context.Context, *LocalCheckPhoneReq) (*LocalCheckPhoneResp, error)
	HttpGetPhone(context.Context, *HttpGetPhoneReq) (*HttpGetPhoneResp, error)
	LocalGetPhoneByToken(context.Context, *LocalGetPhoneByTokenReq) (*LocalGetPhoneByTokenResp, error)
	LocalCleanTokenPhone(context.Context, *LocalCleanTokenPhoneReq) (*LocalCleanTokenPhoneResp, error)
	// 中国移动 获取手机号
	GetCMCCPhone(context.Context, *GetCMCCPhoneReq) (*GetCMCCPhoneResp, error)
	// 中国联通 置换手机号
	GetCUCCPhone(context.Context, *GetCUCCPhoneReq) (*GetCUCCPhoneResp, error)
	// 创蓝
	GetChuanglanPhone(context.Context, *GetChuanglanPhoneReq) (*GetChuanglanPhoneResp, error)
	VerifyQQUserEx(context.Context, *VerifyQQUserExReq) (*VerifyQQUserExResp, error)
	GetQQUserInfo(context.Context, *GetQQUserInfoReq) (*GetQQUserInfoResp, error)
	// reserved : WeChat verify
	//
	GetWeChatUserInfo(context.Context, *GetWeChatUserInfoReq) (*GetWeChatUserInfoResp, error)
	// Mini
	VerifyQQMiniUserEx(context.Context, *VerifyQQMiniUserExReq) (*VerifyQQMiniUserExResp, error)
	VerifyWeChatMiniUserEx(context.Context, *VerifyWeChatMiniUserExReq) (*VerifyWeChatMiniUserExResp, error)
}

func RegisterThirdpartyAuthGwServer(s *grpc.Server, srv ThirdpartyAuthGwServer) {
	s.RegisterService(&_ThirdpartyAuthGw_serviceDesc, srv)
}

func _ThirdpartyAuthGw_VerifyAppleUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyAppleUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyAppleUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyAppleUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyAppleUser(ctx, req.(*VerifyAppleUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyWeixinUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyWeixinUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyWeixinUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyWeixinUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyWeixinUser(ctx, req.(*VerifyWeixinUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyQQUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyQQUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyQQUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyQQUser(ctx, req.(*VerifyQQUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyQQMiniUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyQQMiniUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyQQMiniUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQMiniUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyQQMiniUser(ctx, req.(*VerifyQQMiniUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyWeixinMiniUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyWeixinMiniUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyWeixinMiniUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyWeixinMiniUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyWeixinMiniUser(ctx, req.(*VerifyWeixinMiniUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_LocalCheckPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocalCheckPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).LocalCheckPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/LocalCheckPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).LocalCheckPhone(ctx, req.(*LocalCheckPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_HttpGetPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HttpGetPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).HttpGetPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/HttpGetPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).HttpGetPhone(ctx, req.(*HttpGetPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_LocalGetPhoneByToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocalGetPhoneByTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).LocalGetPhoneByToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/LocalGetPhoneByToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).LocalGetPhoneByToken(ctx, req.(*LocalGetPhoneByTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_LocalCleanTokenPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LocalCleanTokenPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).LocalCleanTokenPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/LocalCleanTokenPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).LocalCleanTokenPhone(ctx, req.(*LocalCleanTokenPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_GetCMCCPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCMCCPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).GetCMCCPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/GetCMCCPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).GetCMCCPhone(ctx, req.(*GetCMCCPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_GetCUCCPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCUCCPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).GetCUCCPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/GetCUCCPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).GetCUCCPhone(ctx, req.(*GetCUCCPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_GetChuanglanPhone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChuanglanPhoneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).GetChuanglanPhone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/GetChuanglanPhone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).GetChuanglanPhone(ctx, req.(*GetChuanglanPhoneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyQQUserEx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyQQUserExReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyQQUserEx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQUserEx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyQQUserEx(ctx, req.(*VerifyQQUserExReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_GetQQUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQQUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).GetQQUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/GetQQUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).GetQQUserInfo(ctx, req.(*GetQQUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_GetWeChatUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWeChatUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).GetWeChatUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/GetWeChatUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).GetWeChatUserInfo(ctx, req.(*GetWeChatUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyQQMiniUserEx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyQQMiniUserExReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyQQMiniUserEx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyQQMiniUserEx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyQQMiniUserEx(ctx, req.(*VerifyQQMiniUserExReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ThirdpartyAuthGw_VerifyWeChatMiniUserEx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyWeChatMiniUserExReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ThirdpartyAuthGwServer).VerifyWeChatMiniUserEx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/thirdpartyauthgw.ThirdpartyAuthGw/VerifyWeChatMiniUserEx",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ThirdpartyAuthGwServer).VerifyWeChatMiniUserEx(ctx, req.(*VerifyWeChatMiniUserExReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ThirdpartyAuthGw_serviceDesc = grpc.ServiceDesc{
	ServiceName: "thirdpartyauthgw.ThirdpartyAuthGw",
	HandlerType: (*ThirdpartyAuthGwServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "VerifyAppleUser",
			Handler:    _ThirdpartyAuthGw_VerifyAppleUser_Handler,
		},
		{
			MethodName: "VerifyWeixinUser",
			Handler:    _ThirdpartyAuthGw_VerifyWeixinUser_Handler,
		},
		{
			MethodName: "VerifyQQUser",
			Handler:    _ThirdpartyAuthGw_VerifyQQUser_Handler,
		},
		{
			MethodName: "VerifyQQMiniUser",
			Handler:    _ThirdpartyAuthGw_VerifyQQMiniUser_Handler,
		},
		{
			MethodName: "VerifyWeixinMiniUser",
			Handler:    _ThirdpartyAuthGw_VerifyWeixinMiniUser_Handler,
		},
		{
			MethodName: "LocalCheckPhone",
			Handler:    _ThirdpartyAuthGw_LocalCheckPhone_Handler,
		},
		{
			MethodName: "HttpGetPhone",
			Handler:    _ThirdpartyAuthGw_HttpGetPhone_Handler,
		},
		{
			MethodName: "LocalGetPhoneByToken",
			Handler:    _ThirdpartyAuthGw_LocalGetPhoneByToken_Handler,
		},
		{
			MethodName: "LocalCleanTokenPhone",
			Handler:    _ThirdpartyAuthGw_LocalCleanTokenPhone_Handler,
		},
		{
			MethodName: "GetCMCCPhone",
			Handler:    _ThirdpartyAuthGw_GetCMCCPhone_Handler,
		},
		{
			MethodName: "GetCUCCPhone",
			Handler:    _ThirdpartyAuthGw_GetCUCCPhone_Handler,
		},
		{
			MethodName: "GetChuanglanPhone",
			Handler:    _ThirdpartyAuthGw_GetChuanglanPhone_Handler,
		},
		{
			MethodName: "VerifyQQUserEx",
			Handler:    _ThirdpartyAuthGw_VerifyQQUserEx_Handler,
		},
		{
			MethodName: "GetQQUserInfo",
			Handler:    _ThirdpartyAuthGw_GetQQUserInfo_Handler,
		},
		{
			MethodName: "GetWeChatUserInfo",
			Handler:    _ThirdpartyAuthGw_GetWeChatUserInfo_Handler,
		},
		{
			MethodName: "VerifyQQMiniUserEx",
			Handler:    _ThirdpartyAuthGw_VerifyQQMiniUserEx_Handler,
		},
		{
			MethodName: "VerifyWeChatMiniUserEx",
			Handler:    _ThirdpartyAuthGw_VerifyWeChatMiniUserEx_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/thirdpartyauthgw/thirdpartyauthgw.proto",
}

func (m *BaseResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BaseResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Code))
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Msg)))
	i += copy(dAtA[i:], m.Msg)
	return i, nil
}

func (m *VerifyAppleUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyAppleUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AuthCode)))
	i += copy(dAtA[i:], m.AuthCode)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.IdToken)))
	i += copy(dAtA[i:], m.IdToken)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.UserId)))
	i += copy(dAtA[i:], m.UserId)
	return i, nil
}

func (m *VerifyAppleUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyAppleUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BaseResult != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.BaseResult.Size()))
		n1, err := m.BaseResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.UserId)))
	i += copy(dAtA[i:], m.UserId)
	return i, nil
}

func (m *VerifyWeixinUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyWeixinUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AccessToken)))
	i += copy(dAtA[i:], m.AccessToken)
	if m.OpenId != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.OpenId)))
		i += copy(dAtA[i:], m.OpenId)
	}
	return i, nil
}

func (m *VerifyWeixinUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyWeixinUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *VerifyQQUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AccessToken)))
	i += copy(dAtA[i:], m.AccessToken)
	if m.OpenId != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.OpenId)))
		i += copy(dAtA[i:], m.OpenId)
	}
	return i, nil
}

func (m *VerifyQQUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.OauthMe != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.OauthMe)))
		i += copy(dAtA[i:], m.OauthMe)
	}
	if m.Data != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *VerifyQQMiniUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQMiniUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Secret)))
	i += copy(dAtA[i:], m.Secret)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Jscode)))
	i += copy(dAtA[i:], m.Jscode)
	return i, nil
}

func (m *VerifyQQMiniUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQMiniUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *VerifyWeixinMiniUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyWeixinMiniUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AppId)))
	i += copy(dAtA[i:], m.AppId)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Secret)))
	i += copy(dAtA[i:], m.Secret)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Jscode)))
	i += copy(dAtA[i:], m.Jscode)
	return i, nil
}

func (m *VerifyWeixinMiniUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyWeixinMiniUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Data != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Data)))
		i += copy(dAtA[i:], m.Data)
	}
	return i, nil
}

func (m *LocalCheckPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocalCheckPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *LocalCheckPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocalCheckPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsPass {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *HttpGetPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HttpGetPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.SpecialAppid)))
	i += copy(dAtA[i:], m.SpecialAppid)
	dAtA[i] = 0x18
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.ClientType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	return i, nil
}

func (m *HttpGetPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HttpGetPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *LocalGetPhoneByTokenReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocalGetPhoneByTokenReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	return i, nil
}

func (m *LocalGetPhoneByTokenResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocalGetPhoneByTokenResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *LocalCleanTokenPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocalCleanTokenPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *LocalCleanTokenPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LocalCleanTokenPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetCMCCPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCMCCPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	return i, nil
}

func (m *GetCMCCPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCMCCPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *GetCUCCPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCUCCPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AccessCode)))
	i += copy(dAtA[i:], m.AccessCode)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Apikey)))
	i += copy(dAtA[i:], m.Apikey)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Md5)))
	i += copy(dAtA[i:], m.Md5)
	return i, nil
}

func (m *GetCUCCPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCUCCPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *GetChuanglanPhoneReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChuanglanPhoneReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Token)))
	i += copy(dAtA[i:], m.Token)
	return i, nil
}

func (m *GetChuanglanPhoneResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChuanglanPhoneResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Phone)))
	i += copy(dAtA[i:], m.Phone)
	return i, nil
}

func (m *VerifyQQUserExReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQUserExReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AccessToken)))
	i += copy(dAtA[i:], m.AccessToken)
	return i, nil
}

func (m *VerifyQQUserExResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQUserExResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Unionid)))
	i += copy(dAtA[i:], m.Unionid)
	return i, nil
}

func (m *GetQQUserInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQQUserInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AccessToken)))
	i += copy(dAtA[i:], m.AccessToken)
	return i, nil
}

func (m *GetQQUserInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQQUserInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Info.Size()))
		n2, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *QQUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QQUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Gender)))
	i += copy(dAtA[i:], m.Gender)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.FigureurlQq_1)))
	i += copy(dAtA[i:], m.FigureurlQq_1)
	dAtA[i] = 0x32
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.FigureurlQq_2)))
	i += copy(dAtA[i:], m.FigureurlQq_2)
	return i, nil
}

func (m *GetWeChatUserInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetWeChatUserInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.AccessToken)))
	i += copy(dAtA[i:], m.AccessToken)
	return i, nil
}

func (m *GetWeChatUserInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetWeChatUserInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(m.Info.Size()))
		n3, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *WeChatUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WeChatUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Unionid)))
	i += copy(dAtA[i:], m.Unionid)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Nickname)))
	i += copy(dAtA[i:], m.Nickname)
	dAtA[i] = 0x22
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Gender)))
	i += copy(dAtA[i:], m.Gender)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Headimage)))
	i += copy(dAtA[i:], m.Headimage)
	return i, nil
}

func (m *VerifyQQMiniUserExReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQMiniUserExReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Jscode)))
	i += copy(dAtA[i:], m.Jscode)
	return i, nil
}

func (m *VerifyQQMiniUserExResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyQQMiniUserExResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Unionid)))
	i += copy(dAtA[i:], m.Unionid)
	return i, nil
}

func (m *VerifyWeChatMiniUserExReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyWeChatMiniUserExReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Appid)))
	i += copy(dAtA[i:], m.Appid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Jscode)))
	i += copy(dAtA[i:], m.Jscode)
	return i, nil
}

func (m *VerifyWeChatMiniUserExResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *VerifyWeChatMiniUserExResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Openid)))
	i += copy(dAtA[i:], m.Openid)
	dAtA[i] = 0x12
	i++
	i = encodeVarintThirdpartyauthgw(dAtA, i, uint64(len(m.Unionid)))
	i += copy(dAtA[i:], m.Unionid)
	return i, nil
}

func encodeFixed64Thirdpartyauthgw(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Thirdpartyauthgw(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintThirdpartyauthgw(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *BaseResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovThirdpartyauthgw(uint64(m.Code))
	l = len(m.Msg)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyAppleUserReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AuthCode)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.IdToken)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.UserId)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyAppleUserResp) Size() (n int) {
	var l int
	_ = l
	if m.BaseResult != nil {
		l = m.BaseResult.Size()
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	l = len(m.UserId)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyWeixinUserReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AccessToken)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	if m.OpenId != nil {
		l = len(m.OpenId)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *VerifyWeixinUserResp) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *VerifyQQUserReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.AccessToken)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	if m.OpenId != nil {
		l = len(m.OpenId)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *VerifyQQUserResp) Size() (n int) {
	var l int
	_ = l
	if m.OauthMe != nil {
		l = len(m.OauthMe)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *VerifyQQMiniUserReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Secret)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Jscode)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyQQMiniUserResp) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *VerifyWeixinMiniUserReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AppId)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Secret)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Jscode)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyWeixinMiniUserResp) Size() (n int) {
	var l int
	_ = l
	if m.Data != nil {
		l = len(m.Data)
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *LocalCheckPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovThirdpartyauthgw(uint64(m.Type))
	l = len(m.Token)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *LocalCheckPhoneResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *HttpGetPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovThirdpartyauthgw(uint64(m.Type))
	l = len(m.SpecialAppid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	n += 1 + sovThirdpartyauthgw(uint64(m.ClientType))
	l = len(m.Token)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *HttpGetPhoneResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *LocalGetPhoneByTokenReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovThirdpartyauthgw(uint64(m.Type))
	l = len(m.Token)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *LocalGetPhoneByTokenResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *LocalCleanTokenPhoneReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovThirdpartyauthgw(uint64(m.Type))
	l = len(m.Token)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *LocalCleanTokenPhoneResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetCMCCPhoneReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Token)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetCMCCPhoneResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetCUCCPhoneReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AccessCode)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Apikey)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Md5)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetCUCCPhoneResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetChuanglanPhoneReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Token)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetChuanglanPhoneResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Phone)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyQQUserExReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.AccessToken)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyQQUserExResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Unionid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetQQUserInfoReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.AccessToken)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetQQUserInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *QQUserInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Gender)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.FigureurlQq_1)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.FigureurlQq_2)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetWeChatUserInfoReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.AccessToken)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *GetWeChatUserInfoResp) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovThirdpartyauthgw(uint64(l))
	}
	return n
}

func (m *WeChatUserInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Unionid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Nickname)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Gender)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Headimage)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyQQMiniUserExReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Jscode)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyQQMiniUserExResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Unionid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyWeChatMiniUserExReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Appid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Jscode)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func (m *VerifyWeChatMiniUserExResp) Size() (n int) {
	var l int
	_ = l
	l = len(m.Openid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	l = len(m.Unionid)
	n += 1 + l + sovThirdpartyauthgw(uint64(l))
	return n
}

func sovThirdpartyauthgw(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozThirdpartyauthgw(x uint64) (n int) {
	return sovThirdpartyauthgw(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *BaseResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BaseResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BaseResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyAppleUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyAppleUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyAppleUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuthCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AuthCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IdToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("auth_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyAppleUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyAppleUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyAppleUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BaseResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BaseResult == nil {
				m.BaseResult = &BaseResult{}
			}
			if err := m.BaseResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyWeixinUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyWeixinUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyWeixinUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpenId = append(m.OpenId[:0], dAtA[iNdEx:postIndex]...)
			if m.OpenId == nil {
				m.OpenId = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("access_token")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("open_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyWeixinUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyWeixinUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyWeixinUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenId", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpenId = append(m.OpenId[:0], dAtA[iNdEx:postIndex]...)
			if m.OpenId == nil {
				m.OpenId = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("access_token")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("open_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OauthMe", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OauthMe = append(m.OauthMe[:0], dAtA[iNdEx:postIndex]...)
			if m.OauthMe == nil {
				m.OauthMe = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQMiniUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQMiniUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQMiniUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Secret", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Secret = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Jscode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Jscode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("secret")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("jscode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQMiniUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQMiniUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQMiniUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyWeixinMiniUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyWeixinMiniUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyWeixinMiniUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Secret", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Secret = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Jscode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Jscode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("app_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("secret")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("jscode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyWeixinMiniUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyWeixinMiniUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyWeixinMiniUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Data = append(m.Data[:0], dAtA[iNdEx:postIndex]...)
			if m.Data == nil {
				m.Data = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocalCheckPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocalCheckPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocalCheckPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (PHONE_TYPE(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocalCheckPhoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocalCheckPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocalCheckPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPass", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPass = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_pass")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HttpGetPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HttpGetPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HttpGetPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (PHONE_TYPE(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecialAppid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SpecialAppid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientType", wireType)
			}
			m.ClientType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientType |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("client_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *HttpGetPhoneResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HttpGetPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HttpGetPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocalGetPhoneByTokenReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocalGetPhoneByTokenReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocalGetPhoneByTokenReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (PHONE_TYPE(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocalGetPhoneByTokenResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocalGetPhoneByTokenResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocalGetPhoneByTokenResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocalCleanTokenPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocalCleanTokenPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocalCleanTokenPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (PHONE_TYPE(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phone")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LocalCleanTokenPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LocalCleanTokenPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LocalCleanTokenPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCMCCPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCMCCPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCMCCPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCMCCPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCMCCPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCMCCPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCUCCPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCUCCPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCUCCPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessCode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessCode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Apikey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Apikey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Md5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Md5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("access_code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCUCCPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCUCCPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCUCCPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChuanglanPhoneReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChuanglanPhoneReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChuanglanPhoneReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("appid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("token")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChuanglanPhoneResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChuanglanPhoneResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChuanglanPhoneResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phone", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Phone = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQUserExReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQUserExReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQUserExReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQUserExResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQUserExResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQUserExResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Unionid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Unionid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQQUserInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQQUserInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQQUserInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQQUserInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQQUserInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQQUserInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &QQUserInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QQUserInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QQUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QQUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gender", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Gender = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FigureurlQq_1", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FigureurlQq_1 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FigureurlQq_2", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FigureurlQq_2 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetWeChatUserInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetWeChatUserInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetWeChatUserInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AccessToken", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AccessToken = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetWeChatUserInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetWeChatUserInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetWeChatUserInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &WeChatUserInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WeChatUserInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WeChatUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WeChatUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Unionid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Unionid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Nickname", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Nickname = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Gender", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Gender = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Headimage", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Headimage = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQMiniUserExReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQMiniUserExReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQMiniUserExReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Jscode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Jscode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyQQMiniUserExResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyQQMiniUserExResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyQQMiniUserExResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Unionid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Unionid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyWeChatMiniUserExReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyWeChatMiniUserExReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyWeChatMiniUserExReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Appid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Appid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Jscode", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Jscode = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *VerifyWeChatMiniUserExResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: VerifyWeChatMiniUserExResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: VerifyWeChatMiniUserExResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Openid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Openid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Unionid", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Unionid = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipThirdpartyauthgw(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthThirdpartyauthgw
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipThirdpartyauthgw(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowThirdpartyauthgw
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowThirdpartyauthgw
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthThirdpartyauthgw
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowThirdpartyauthgw
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipThirdpartyauthgw(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthThirdpartyauthgw = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowThirdpartyauthgw   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/thirdpartyauthgw/thirdpartyauthgw.proto", fileDescriptorThirdpartyauthgw)
}

var fileDescriptorThirdpartyauthgw = []byte{
	// 1686 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x97, 0xcb, 0x72, 0x13, 0x47,
	0x17, 0xc7, 0x19, 0x49, 0xbe, 0x1d, 0x1b, 0x5b, 0x6e, 0x5f, 0xd0, 0x27, 0x8c, 0x19, 0x86, 0x8b,
	0x6d, 0xf8, 0x6c, 0xc0, 0x01, 0x16, 0x2a, 0xa1, 0x2a, 0x5b, 0x71, 0x8c, 0xaa, 0xb0, 0xb1, 0x1d,
	0x0b, 0x2a, 0xa9, 0xa2, 0x54, 0x83, 0xd4, 0xb6, 0xc6, 0x92, 0x66, 0xda, 0xea, 0x11, 0xe0, 0x15,
	0x59, 0x64, 0x41, 0x58, 0xa4, 0x02, 0xa9, 0x4a, 0x56, 0xc9, 0xca, 0x9b, 0x6c, 0xb3, 0xe1, 0x15,
	0x58, 0xe6, 0x09, 0xa8, 0x14, 0x59, 0xc4, 0x8f, 0x91, 0xea, 0xee, 0x19, 0xcd, 0xdd, 0x52, 0xe2,
	0x5c, 0x96, 0x73, 0xfa, 0x72, 0x7e, 0x7d, 0xce, 0xe9, 0xe9, 0xf3, 0x87, 0x6b, 0xb4, 0x59, 0xbe,
	0x6e, 0x56, 0xb5, 0x66, 0x85, 0xa8, 0x4d, 0xf3, 0x40, 0x6d, 0x99, 0xd5, 0xdd, 0x67, 0x01, 0xc3,
	0x02, 0x69, 0x1a, 0xa6, 0x81, 0x92, 0x7e, 0x7b, 0xfa, 0x52, 0xd9, 0x68, 0x34, 0x0c, 0xfd, 0xba,
	0x59, 0x7f, 0x4a, 0xb4, 0x72, 0xad, 0x8e, 0xaf, 0xd3, 0xda, 0x93, 0x96, 0x56, 0x37, 0x35, 0xdd,
	0x3c, 0x20, 0x58, 0xac, 0x53, 0x72, 0x00, 0xcb, 0x2a, 0xc5, 0x5b, 0x98, 0xb6, 0xea, 0x26, 0x4a,
	0x41, 0xa2, 0x6c, 0x54, 0x70, 0x4a, 0x92, 0x63, 0xb3, 0x3d, 0xcb, 0x89, 0x77, 0xef, 0xcf, 0x9f,
	0xda, 0xe2, 0x16, 0x34, 0x09, 0xf1, 0x06, 0xdd, 0x4d, 0xc5, 0x64, 0x69, 0x76, 0xc0, 0x1a, 0x60,
	0x06, 0xe5, 0x19, 0xa0, 0x87, 0xb8, 0xa9, 0xed, 0x1c, 0x2c, 0x11, 0x52, 0xc7, 0x45, 0x8a, 0x9b,
	0x5b, 0x78, 0x1f, 0x5d, 0x80, 0x01, 0x46, 0x51, 0x6a, 0x6f, 0x66, 0xaf, 0xe9, 0x67, 0xe6, 0x3c,
	0xdb, 0xf0, 0x3c, 0xf4, 0x6b, 0x95, 0x92, 0x69, 0xd4, 0xb0, 0xee, 0xd9, 0xb5, 0x4f, 0xab, 0x6c,
	0x33, 0x23, 0x3a, 0x07, 0x7d, 0x2d, 0x8a, 0x9b, 0x25, 0xad, 0x92, 0x8a, 0xbb, 0xc6, 0x7b, 0x99,
	0xb1, 0x50, 0x51, 0x28, 0x8c, 0x05, 0x1c, 0x53, 0x82, 0xee, 0xc2, 0xe0, 0x13, 0x95, 0xe2, 0x52,
	0x93, 0x1f, 0x28, 0x25, 0xc9, 0xd2, 0xec, 0xe0, 0xe2, 0xd4, 0x42, 0x20, 0x6a, 0xce, 0xa1, 0xb7,
	0xe0, 0x89, 0x13, 0x00, 0x97, 0xd3, 0x58, 0x88, 0xd3, 0xc7, 0xb6, 0xd3, 0x47, 0x58, 0x7b, 0xae,
	0xe9, 0xf6, 0x71, 0x67, 0x60, 0x48, 0x2d, 0x97, 0x31, 0xa5, 0xd6, 0x79, 0xdc, 0x27, 0x1e, 0x14,
	0x23, 0xed, 0x33, 0x19, 0x04, 0xeb, 0x62, 0xfb, 0xd8, 0xec, 0x90, 0xbd, 0x3d, 0x33, 0x16, 0x2a,
	0xca, 0x0d, 0x18, 0x0f, 0x6e, 0x4f, 0x09, 0x4b, 0x4b, 0x45, 0x35, 0x55, 0x7e, 0x1a, 0x7b, 0x0d,
	0xb7, 0x28, 0x4f, 0x61, 0x44, 0xac, 0xd8, 0xdc, 0xb4, 0x61, 0xce, 0x42, 0xaf, 0x4a, 0x08, 0x73,
	0xe1, 0xc6, 0xe8, 0x51, 0x09, 0x29, 0x54, 0x02, 0xa4, 0xb1, 0x2e, 0x48, 0xe3, 0x21, 0xa4, 0x6b,
	0x90, 0xf4, 0xfa, 0xa5, 0x84, 0x65, 0xd4, 0xe0, 0x59, 0x6f, 0x60, 0x0f, 0x69, 0x1f, 0xb7, 0xae,
	0xe1, 0xf6, 0x31, 0x62, 0x81, 0x63, 0xe8, 0x76, 0x5c, 0x37, 0x37, 0xd7, 0x34, 0x5d, 0xeb, 0xea,
	0x28, 0x53, 0xd0, 0x4b, 0x71, 0xb9, 0x89, 0x4d, 0xcf, 0x21, 0x2c, 0x1b, 0x1b, 0xdd, 0xa3, 0xbc,
	0xfc, 0xe2, 0xee, 0x51, 0x61, 0x73, 0x02, 0xed, 0xf6, 0x77, 0x6c, 0xa0, 0x9b, 0x70, 0xc6, 0x9d,
	0x9a, 0x7f, 0x85, 0xf2, 0x16, 0xa4, 0xc2, 0x7d, 0x1e, 0x4b, 0xfa, 0xa5, 0x04, 0xe8, 0xbe, 0x51,
	0x56, 0xeb, 0xf9, 0x2a, 0x2e, 0xd7, 0x36, 0xaa, 0x86, 0x8e, 0x19, 0xe5, 0x1d, 0x48, 0xb0, 0x6b,
	0xcf, 0x19, 0x87, 0xc3, 0x6e, 0xc4, 0xc6, 0xbd, 0x07, 0xeb, 0x2b, 0xa5, 0xed, 0xcf, 0x36, 0x56,
	0xec, 0xed, 0xd8, 0x7c, 0x94, 0x86, 0x9e, 0x60, 0xa9, 0x08, 0x13, 0x1b, 0x23, 0x6c, 0x7f, 0x0f,
	0xbd, 0x30, 0x29, 0xb7, 0x60, 0x2c, 0x40, 0x41, 0x09, 0xab, 0x2b, 0x8d, 0x96, 0x88, 0x4a, 0x29,
	0x27, 0xe9, 0xb7, 0x8f, 0xac, 0xd1, 0x0d, 0x95, 0x52, 0xe5, 0xad, 0x04, 0x23, 0xf7, 0x4c, 0x93,
	0xac, 0x62, 0xf3, 0xc4, 0xe4, 0x73, 0x70, 0x9a, 0x12, 0x5c, 0xd6, 0xd4, 0x7a, 0x49, 0x25, 0xc4,
	0x77, 0xa3, 0x87, 0xac, 0xa1, 0x25, 0x36, 0x82, 0x2e, 0xc3, 0x60, 0xb9, 0xae, 0x61, 0xdd, 0x2c,
	0x71, 0x4f, 0x71, 0xd7, 0xef, 0x0f, 0xc4, 0xc0, 0xb6, 0x27, 0x16, 0x89, 0x40, 0x2c, 0x94, 0x05,
	0x48, 0x7a, 0xc1, 0x29, 0x71, 0xe2, 0x23, 0x05, 0xe3, 0xd3, 0x80, 0x33, 0x3c, 0x3e, 0xf6, 0x82,
	0xe5, 0x03, 0x7e, 0xf1, 0xfe, 0xa1, 0x54, 0x29, 0x77, 0x20, 0x15, 0xee, 0xae, 0x03, 0xe6, 0x57,
	0x92, 0xc5, 0x99, 0xaf, 0x63, 0x55, 0xe7, 0x6b, 0xfe, 0xb3, 0x92, 0x4a, 0x5b, 0x67, 0x08, 0xa0,
	0x50, 0xa2, 0x14, 0x60, 0x64, 0x15, 0x9b, 0xf9, 0xb5, 0x7c, 0xbe, 0x8d, 0x97, 0x86, 0x1e, 0x91,
	0x77, 0xff, 0xb5, 0xd4, 0x2a, 0xc7, 0x86, 0x6a, 0x01, 0x92, 0xde, 0xad, 0xbc, 0x21, 0x92, 0xfc,
	0x58, 0xba, 0x70, 0x5d, 0x74, 0xb9, 0xbe, 0x0c, 0xd6, 0xcf, 0x34, 0xf8, 0x02, 0x82, 0x18, 0xe0,
	0x6f, 0xe0, 0x14, 0xfb, 0x73, 0x68, 0x35, 0x7c, 0xe0, 0x7d, 0x6c, 0x84, 0x8d, 0x3f, 0xb9, 0x95,
	0xdb, 0x9e, 0xc7, 0x8f, 0x19, 0x6c, 0xbe, 0x62, 0xb7, 0x7c, 0xeb, 0x30, 0xce, 0xe6, 0x57, 0x5b,
	0xaa, 0xbe, 0x5b, 0x57, 0xf5, 0x13, 0xc7, 0xe7, 0x23, 0x98, 0x08, 0xd9, 0xaf, 0x03, 0x44, 0x16,
	0x46, 0xdd, 0x0f, 0xc6, 0xca, 0xf3, 0xf0, 0x77, 0x53, 0x0a, 0x7d, 0x8d, 0x14, 0xdd, 0xee, 0x32,
	0x9c, 0xd5, 0xc2, 0x9f, 0x7d, 0x00, 0xc9, 0x7f, 0x80, 0x29, 0xe0, 0x4f, 0x95, 0xff, 0x1d, 0x17,
	0x36, 0x34, 0x0d, 0x7d, 0x2d, 0x5d, 0x33, 0x74, 0x5f, 0x6f, 0x61, 0x1b, 0x95, 0x16, 0x0f, 0xb1,
	0x70, 0x56, 0xd0, 0x77, 0x0c, 0x5f, 0xb8, 0xfe, 0xa4, 0x37, 0xff, 0x31, 0xe3, 0x51, 0xc7, 0x5c,
	0x81, 0x51, 0x9f, 0x5b, 0x4a, 0xd0, 0x0d, 0x48, 0x68, 0xfa, 0x8e, 0x11, 0xdd, 0xca, 0xb8, 0xe6,
	0xf3, 0x99, 0xca, 0x7b, 0x09, 0xc0, 0x31, 0x9e, 0x00, 0x5c, 0x86, 0x7e, 0x5d, 0x2b, 0xd7, 0x74,
	0xb5, 0x81, 0x3d, 0xd0, 0x6d, 0x2b, 0x5b, 0xbf, 0x8b, 0xf5, 0x0a, 0x6e, 0xa6, 0x12, 0xee, 0xf5,
	0xc2, 0x86, 0xae, 0xc2, 0xf0, 0x8e, 0xb6, 0xdb, 0x6a, 0xe2, 0x56, 0xb3, 0x5e, 0xda, 0xdf, 0x2f,
	0xdd, 0x4c, 0xf5, 0xb8, 0x7f, 0xc1, 0xed, 0xb1, 0xcd, 0xfd, 0x9b, 0x81, 0xb9, 0x8b, 0xa9, 0xde,
	0x88, 0xb9, 0x8b, 0xca, 0x63, 0x5e, 0xd1, 0x8f, 0x70, 0xbe, 0xaa, 0x9a, 0xee, 0x14, 0x39, 0xa7,
	0x91, 0xba, 0x48, 0x43, 0x2c, 0x2a, 0x0d, 0x6b, 0xbc, 0xc0, 0xfd, 0xdb, 0x53, 0x82, 0x6e, 0x79,
	0x52, 0x21, 0x07, 0x53, 0xe1, 0x5b, 0x23, 0xd2, 0xf1, 0xb3, 0x04, 0xc3, 0xde, 0x81, 0x0e, 0xa0,
	0xae, 0xea, 0x8c, 0x85, 0x54, 0xe7, 0x89, 0xd3, 0xa2, 0xc0, 0x40, 0x15, 0xab, 0x15, 0xad, 0xa1,
	0xee, 0x62, 0x4f, 0x46, 0x1c, 0xb3, 0xb2, 0x09, 0x13, 0xfe, 0x0e, 0x49, 0xdc, 0xd9, 0x0e, 0xd5,
	0x64, 0xb5, 0x33, 0x9e, 0x6a, 0xb2, 0xda, 0x99, 0x87, 0x30, 0x19, 0xb6, 0x25, 0x25, 0x27, 0x0b,
	0x87, 0x52, 0x84, 0xff, 0xd9, 0x6d, 0x12, 0x0b, 0xf2, 0xdf, 0x85, 0xfb, 0x39, 0xa4, 0xa3, 0xb6,
	0x3d, 0x29, 0xf2, 0xd5, 0x4d, 0x00, 0xe7, 0x6d, 0x44, 0x13, 0x30, 0x2a, 0xbe, 0x8a, 0xeb, 0x9f,
	0x6e, 0xac, 0xe4, 0x0b, 0x9f, 0x14, 0x56, 0x3e, 0x4e, 0x9e, 0x42, 0xe3, 0x90, 0x14, 0xe6, 0xfc,
	0xbd, 0xe2, 0xd2, 0xfa, 0x6a, 0xe9, 0xfe, 0xd2, 0x7a, 0x52, 0x42, 0x93, 0x80, 0x6c, 0x6b, 0x61,
	0x7d, 0xa9, 0xb4, 0xf6, 0x60, 0xb9, 0x70, 0x7f, 0x25, 0x19, 0x5b, 0xfc, 0x7d, 0x0c, 0x92, 0xdb,
	0xed, 0x7a, 0x5c, 0x6a, 0x99, 0xd5, 0xd5, 0x67, 0xe8, 0x47, 0xc9, 0xd6, 0x07, 0x6d, 0x95, 0x84,
	0x2e, 0x05, 0xcb, 0x36, 0xa8, 0xe0, 0xd2, 0x97, 0xbb, 0x98, 0x45, 0x89, 0x92, 0xfb, 0xe2, 0xf0,
	0x28, 0x2e, 0xbd, 0x3a, 0x3c, 0x8a, 0xf7, 0xaa, 0x99, 0x56, 0xc6, 0xcc, 0xbc, 0x39, 0x3c, 0x8a,
	0xcf, 0xcd, 0xab, 0x72, 0xb6, 0xad, 0xff, 0x72, 0xf2, 0xbc, 0x29, 0x67, 0x6d, 0xad, 0x97, 0x93,
	0xe7, 0x5b, 0x72, 0xd6, 0xd2, 0x58, 0x39, 0xf4, 0x5a, 0xb2, 0x85, 0x84, 0x23, 0x79, 0x50, 0xa4,
	0x6f, 0x8f, 0xea, 0x4a, 0x5f, 0xe9, 0x66, 0x1a, 0x25, 0xca, 0x02, 0x63, 0x8c, 0x31, 0xc6, 0x84,
	0x9a, 0x31, 0x38, 0xe1, 0x59, 0x4e, 0x58, 0x2e, 0xb7, 0x99, 0x0c, 0x39, 0x6b, 0xc9, 0x9d, 0x1c,
	0xfa, 0x56, 0x82, 0x21, 0xf7, 0x6b, 0x83, 0x2e, 0x44, 0x39, 0x6a, 0x8b, 0xae, 0xb4, 0xd2, 0x69,
	0x0a, 0x25, 0x4a, 0x96, 0x71, 0xc4, 0xad, 0x58, 0x19, 0x19, 0x8d, 0x93, 0xcc, 0xcc, 0x6b, 0x72,
	0x56, 0xc8, 0x87, 0x9c, 0x7c, 0x1c, 0xd5, 0x0f, 0x92, 0x23, 0xb9, 0xec, 0x5a, 0x8c, 0x8e, 0x94,
	0x47, 0x47, 0x45, 0x47, 0xca, 0x2b, 0x7f, 0x94, 0x0c, 0x23, 0x4c, 0x30, 0xc2, 0x7e, 0x41, 0x48,
	0x39, 0xe3, 0x45, 0x0f, 0x23, 0x95, 0xb3, 0x42, 0xbd, 0xe4, 0xe4, 0xf9, 0x3d, 0x39, 0xbb, 0xc7,
	0x5b, 0x99, 0x1c, 0xfa, 0x49, 0xf2, 0x8a, 0xd7, 0x36, 0xe3, 0xdc, 0xf1, 0x69, 0x72, 0x73, 0x5e,
	0xed, 0x76, 0xaa, 0xcd, 0xda, 0xf3, 0xd7, 0x58, 0x5f, 0x4a, 0x30, 0xe2, 0x13, 0x27, 0x61, 0xd7,
	0x22, 0xa8, 0xa2, 0xc2, 0xae, 0x45, 0x88, 0xca, 0x51, 0x66, 0x19, 0x5c, 0x2f, 0x2f, 0x39, 0x33,
	0x43, 0x38, 0xd8, 0x04, 0xbb, 0x05, 0x76, 0x62, 0x89, 0x9c, 0xe5, 0x6d, 0x51, 0x0e, 0x35, 0x61,
	0xc8, 0x2d, 0x1b, 0xc2, 0x6a, 0xcd, 0xa7, 0x87, 0xc2, 0x6a, 0xcd, 0xaf, 0x3c, 0x94, 0xb3, 0x0c,
	0xa0, 0x8f, 0x01, 0xc4, 0xc4, 0x9d, 0x04, 0xc7, 0x3d, 0x7a, 0x25, 0xc1, 0x78, 0x98, 0x18, 0x08,
	0x4b, 0x55, 0x84, 0x46, 0x09, 0x4b, 0x55, 0x94, 0xbe, 0x10, 0x30, 0xfd, 0x11, 0x30, 0xdf, 0xdb,
	0x30, 0xbe, 0xae, 0x3e, 0x12, 0x26, 0x28, 0x44, 0x22, 0x61, 0xc2, 0x84, 0x02, 0x4f, 0xcd, 0x40,
	0x37, 0xa9, 0x79, 0x01, 0x43, 0x6e, 0x1d, 0x10, 0x96, 0x1a, 0x9f, 0xe4, 0x08, 0x4b, 0x8d, 0x5f,
	0x4a, 0x08, 0x00, 0xb0, 0x7e, 0x47, 0xa6, 0x05, 0xa0, 0xf2, 0xa2, 0xe5, 0x35, 0xeb, 0x84, 0xe6,
	0xb5, 0x24, 0x08, 0x8a, 0x1d, 0x08, 0x8a, 0x9d, 0x09, 0xdc, 0x62, 0x41, 0x5c, 0x9d, 0x41, 0x46,
	0xd0, 0x53, 0xcb, 0x34, 0x32, 0x2a, 0x43, 0xb8, 0x32, 0x5f, 0x63, 0x08, 0x4c, 0x78, 0xb4, 0xff,
	0x43, 0x98, 0x52, 0xd9, 0xfa, 0x83, 0x37, 0xe4, 0x6c, 0xa3, 0x72, 0x3b, 0x27, 0xa3, 0xaf, 0x25,
	0xde, 0xa3, 0x7a, 0xbb, 0x7f, 0x74, 0x25, 0xdc, 0xab, 0x5f, 0x72, 0xa4, 0x67, 0xba, 0x9a, 0x67,
	0x07, 0xe9, 0x74, 0x37, 0x41, 0x7a, 0x01, 0xc3, 0x5e, 0x69, 0x80, 0x2e, 0x1e, 0xff, 0x2f, 0xe6,
	0x7d, 0x41, 0xfa, 0x52, 0xe7, 0x49, 0x94, 0x28, 0x0a, 0xc3, 0x18, 0x71, 0x55, 0xee, 0x28, 0xf3,
	0xec, 0xee, 0x22, 0x79, 0x01, 0x9f, 0xf6, 0x74, 0xed, 0x28, 0x3c, 0x07, 0x1e, 0x35, 0x91, 0xbe,
	0xd8, 0x71, 0x0e, 0x25, 0xca, 0x5d, 0xe6, 0x3e, 0xd9, 0x7e, 0x31, 0x04, 0xc2, 0xac, 0x3b, 0x0e,
	0xd6, 0x0b, 0x61, 0xc7, 0xc4, 0x4a, 0x9a, 0x45, 0xf6, 0x9d, 0xc8, 0x95, 0xaf, 0xf7, 0x0c, 0xcf,
	0x55, 0xa0, 0x99, 0x8e, 0xc8, 0x55, 0xb0, 0x2b, 0x56, 0x6e, 0x30, 0x4a, 0xc4, 0x73, 0x65, 0x33,
	0x9e, 0x3b, 0x1e, 0xec, 0x8d, 0xe4, 0xe8, 0x39, 0xa7, 0xaf, 0x42, 0x33, 0x9d, 0x9f, 0x29, 0x91,
	0xbc, 0xd9, 0xee, 0x26, 0x52, 0xa2, 0xcc, 0x31, 0xb6, 0x31, 0xab, 0x8e, 0xf6, 0x38, 0xdb, 0xa4,
	0x3b, 0x7e, 0xfc, 0x51, 0xe0, 0x35, 0xce, 0x7a, 0xa5, 0xc9, 0xf0, 0x86, 0x0f, 0x5d, 0x8b, 0x7e,
	0x97, 0x02, 0x1d, 0x67, 0xfa, 0xff, 0xdd, 0x4f, 0xb6, 0x01, 0xc7, 0xbb, 0x01, 0x4c, 0xf7, 0xbe,
	0x3c, 0x3c, 0x8a, 0xbf, 0x6d, 0x2c, 0x27, 0xdf, 0x7d, 0x98, 0x96, 0x7e, 0xf9, 0x30, 0x2d, 0xfd,
	0xfa, 0x61, 0x5a, 0xfa, 0xe6, 0xb7, 0xe9, 0x53, 0x7f, 0x04, 0x00, 0x00, 0xff, 0xff, 0x43, 0x91,
	0x4f, 0xc8, 0x2a, 0x18, 0x00, 0x00,
}
