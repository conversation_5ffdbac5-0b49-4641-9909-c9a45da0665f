// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-open-game-playmate/channel-open-game-playmate.proto

package channel_open_game_playmate // import "golang.52tt.com/protocol/services/channel-open-game-playmate"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Playmate struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LastPlayTime         int64    `protobuf:"varint,2,opt,name=last_play_time,json=lastPlayTime,proto3" json:"last_play_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Playmate) Reset()         { *m = Playmate{} }
func (m *Playmate) String() string { return proto.CompactTextString(m) }
func (*Playmate) ProtoMessage()    {}
func (*Playmate) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_playmate_f13da001efdc439c, []int{0}
}
func (m *Playmate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Playmate.Unmarshal(m, b)
}
func (m *Playmate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Playmate.Marshal(b, m, deterministic)
}
func (dst *Playmate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Playmate.Merge(dst, src)
}
func (m *Playmate) XXX_Size() int {
	return xxx_messageInfo_Playmate.Size(m)
}
func (m *Playmate) XXX_DiscardUnknown() {
	xxx_messageInfo_Playmate.DiscardUnknown(m)
}

var xxx_messageInfo_Playmate proto.InternalMessageInfo

func (m *Playmate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Playmate) GetLastPlayTime() int64 {
	if m != nil {
		return m.LastPlayTime
	}
	return 0
}

type GetPlaymateListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Next                 int64    `protobuf:"varint,2,opt,name=next,proto3" json:"next,omitempty"`
	Count                int64    `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	GameId               uint32   `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPlaymateListReq) Reset()         { *m = GetPlaymateListReq{} }
func (m *GetPlaymateListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateListReq) ProtoMessage()    {}
func (*GetPlaymateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_playmate_f13da001efdc439c, []int{1}
}
func (m *GetPlaymateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateListReq.Unmarshal(m, b)
}
func (m *GetPlaymateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateListReq.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateListReq.Merge(dst, src)
}
func (m *GetPlaymateListReq) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateListReq.Size(m)
}
func (m *GetPlaymateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateListReq proto.InternalMessageInfo

func (m *GetPlaymateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPlaymateListReq) GetNext() int64 {
	if m != nil {
		return m.Next
	}
	return 0
}

func (m *GetPlaymateListReq) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetPlaymateListReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetPlaymateListResp struct {
	PlaymateList         []*Playmate `protobuf:"bytes,1,rep,name=playmate_list,json=playmateList,proto3" json:"playmate_list,omitempty"`
	Next                 int64       `protobuf:"varint,2,opt,name=next,proto3" json:"next,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetPlaymateListResp) Reset()         { *m = GetPlaymateListResp{} }
func (m *GetPlaymateListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlaymateListResp) ProtoMessage()    {}
func (*GetPlaymateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_playmate_f13da001efdc439c, []int{2}
}
func (m *GetPlaymateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPlaymateListResp.Unmarshal(m, b)
}
func (m *GetPlaymateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPlaymateListResp.Marshal(b, m, deterministic)
}
func (dst *GetPlaymateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPlaymateListResp.Merge(dst, src)
}
func (m *GetPlaymateListResp) XXX_Size() int {
	return xxx_messageInfo_GetPlaymateListResp.Size(m)
}
func (m *GetPlaymateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPlaymateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPlaymateListResp proto.InternalMessageInfo

func (m *GetPlaymateListResp) GetPlaymateList() []*Playmate {
	if m != nil {
		return m.PlaymateList
	}
	return nil
}

func (m *GetPlaymateListResp) GetNext() int64 {
	if m != nil {
		return m.Next
	}
	return 0
}

func init() {
	proto.RegisterType((*Playmate)(nil), "channel_open_game_playmate.Playmate")
	proto.RegisterType((*GetPlaymateListReq)(nil), "channel_open_game_playmate.GetPlaymateListReq")
	proto.RegisterType((*GetPlaymateListResp)(nil), "channel_open_game_playmate.GetPlaymateListResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelOpenGamePlaymateClient is the client API for ChannelOpenGamePlaymate service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelOpenGamePlaymateClient interface {
	GetPlaymateList(ctx context.Context, in *GetPlaymateListReq, opts ...grpc.CallOption) (*GetPlaymateListResp, error)
}

type channelOpenGamePlaymateClient struct {
	cc *grpc.ClientConn
}

func NewChannelOpenGamePlaymateClient(cc *grpc.ClientConn) ChannelOpenGamePlaymateClient {
	return &channelOpenGamePlaymateClient{cc}
}

func (c *channelOpenGamePlaymateClient) GetPlaymateList(ctx context.Context, in *GetPlaymateListReq, opts ...grpc.CallOption) (*GetPlaymateListResp, error) {
	out := new(GetPlaymateListResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_playmate.ChannelOpenGamePlaymate/GetPlaymateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelOpenGamePlaymateServer is the server API for ChannelOpenGamePlaymate service.
type ChannelOpenGamePlaymateServer interface {
	GetPlaymateList(context.Context, *GetPlaymateListReq) (*GetPlaymateListResp, error)
}

func RegisterChannelOpenGamePlaymateServer(s *grpc.Server, srv ChannelOpenGamePlaymateServer) {
	s.RegisterService(&_ChannelOpenGamePlaymate_serviceDesc, srv)
}

func _ChannelOpenGamePlaymate_GetPlaymateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlaymateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGamePlaymateServer).GetPlaymateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_playmate.ChannelOpenGamePlaymate/GetPlaymateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGamePlaymateServer).GetPlaymateList(ctx, req.(*GetPlaymateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOpenGamePlaymate_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_open_game_playmate.ChannelOpenGamePlaymate",
	HandlerType: (*ChannelOpenGamePlaymateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPlaymateList",
			Handler:    _ChannelOpenGamePlaymate_GetPlaymateList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-open-game-playmate/channel-open-game-playmate.proto",
}

func init() {
	proto.RegisterFile("channel-open-game-playmate/channel-open-game-playmate.proto", fileDescriptor_channel_open_game_playmate_f13da001efdc439c)
}

var fileDescriptor_channel_open_game_playmate_f13da001efdc439c = []byte{
	// 304 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x92, 0x4d, 0x4b, 0xc3, 0x40,
	0x10, 0x86, 0x8d, 0xa9, 0x55, 0xc6, 0x56, 0x65, 0x15, 0x1a, 0x7a, 0x2a, 0xa1, 0x87, 0x5e, 0x9a,
	0x40, 0xc5, 0x93, 0xe2, 0xa1, 0x1e, 0x4a, 0x41, 0x50, 0x82, 0x27, 0x2f, 0x61, 0x4d, 0x87, 0xba,
	0x90, 0xfd, 0xb0, 0x3b, 0x15, 0xfd, 0x15, 0xfe, 0x65, 0xd9, 0x6d, 0x22, 0x62, 0x3f, 0xc0, 0xdb,
	0xcc, 0xe4, 0x99, 0x77, 0xde, 0x99, 0x2c, 0x5c, 0x17, 0xaf, 0x5c, 0x29, 0x2c, 0x87, 0xda, 0xa0,
	0x1a, 0xce, 0xb9, 0xc4, 0xa1, 0x29, 0xf9, 0xa7, 0xe4, 0x84, 0xe9, 0xf6, 0x4f, 0x89, 0x59, 0x68,
	0xd2, 0xac, 0x5b, 0x11, 0xb9, 0x23, 0x72, 0x47, 0xe4, 0x35, 0x11, 0x8f, 0xe1, 0xe8, 0xb1, 0x8a,
	0xd9, 0x19, 0x84, 0x4b, 0x31, 0x8b, 0x82, 0x5e, 0x30, 0x68, 0x67, 0x2e, 0x64, 0x7d, 0x38, 0x29,
	0xb9, 0x25, 0x8f, 0xe7, 0x24, 0x24, 0x46, 0xfb, 0xbd, 0x60, 0x10, 0x66, 0x2d, 0x57, 0x75, 0x7d,
	0x4f, 0x42, 0x62, 0x2c, 0x80, 0x4d, 0x90, 0x6a, 0x99, 0x7b, 0x61, 0x29, 0xc3, 0xb7, 0x0d, 0x6a,
	0x0c, 0x1a, 0x0a, 0x3f, 0xa8, 0xd2, 0xf0, 0x31, 0xbb, 0x80, 0x83, 0x42, 0x2f, 0x15, 0x45, 0xa1,
	0x2f, 0xae, 0x12, 0xd6, 0x81, 0x43, 0x6f, 0x53, 0xcc, 0xa2, 0x86, 0xef, 0x6f, 0xba, 0x74, 0x3a,
	0x8b, 0x09, 0xce, 0xd7, 0x46, 0x59, 0xc3, 0xa6, 0xd0, 0xae, 0x37, 0xca, 0x4b, 0x61, 0x29, 0x0a,
	0x7a, 0xe1, 0xe0, 0x78, 0xd4, 0x4f, 0xb6, 0x6f, 0x9e, 0xd4, 0x22, 0x59, 0xcb, 0xfc, 0x92, 0xdb,
	0x64, 0x72, 0xf4, 0x15, 0x40, 0xe7, 0x6e, 0xa5, 0xf4, 0x60, 0x50, 0x4d, 0xb8, 0xc4, 0x9f, 0xa3,
	0x11, 0x9c, 0xfe, 0x71, 0xc4, 0x92, 0x5d, 0x63, 0xd7, 0x2f, 0xd5, 0x4d, 0xff, 0xc5, 0x5b, 0x13,
	0xef, 0x8d, 0x6f, 0x9f, 0x6f, 0xe6, 0xba, 0xe4, 0x6a, 0x9e, 0x5c, 0x8d, 0x88, 0x92, 0x42, 0xcb,
	0xd4, 0xff, 0xeb, 0x42, 0x97, 0xa9, 0xc5, 0xc5, 0xbb, 0x28, 0xd0, 0xee, 0x78, 0x18, 0x2f, 0x4d,
	0x4f, 0x5f, 0x7e, 0x07, 0x00, 0x00, 0xff, 0xff, 0x99, 0x9e, 0xff, 0x97, 0x58, 0x02, 0x00, 0x00,
}
