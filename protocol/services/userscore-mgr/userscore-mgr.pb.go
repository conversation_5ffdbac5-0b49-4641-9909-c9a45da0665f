// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/userscore-mgr/userscore-mgr.proto

package userscore_mgr // import "golang.52tt.com/protocol/services/userscore-mgr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RunHistoryScoreAmountReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Replace              bool     `protobuf:"varint,3,opt,name=replace,proto3" json:"replace,omitempty"`
	Async                bool     `protobuf:"varint,4,opt,name=async,proto3" json:"async,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RunHistoryScoreAmountReq) Reset()         { *m = RunHistoryScoreAmountReq{} }
func (m *RunHistoryScoreAmountReq) String() string { return proto.CompactTextString(m) }
func (*RunHistoryScoreAmountReq) ProtoMessage()    {}
func (*RunHistoryScoreAmountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{0}
}
func (m *RunHistoryScoreAmountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RunHistoryScoreAmountReq.Unmarshal(m, b)
}
func (m *RunHistoryScoreAmountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RunHistoryScoreAmountReq.Marshal(b, m, deterministic)
}
func (dst *RunHistoryScoreAmountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunHistoryScoreAmountReq.Merge(dst, src)
}
func (m *RunHistoryScoreAmountReq) XXX_Size() int {
	return xxx_messageInfo_RunHistoryScoreAmountReq.Size(m)
}
func (m *RunHistoryScoreAmountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RunHistoryScoreAmountReq.DiscardUnknown(m)
}

var xxx_messageInfo_RunHistoryScoreAmountReq proto.InternalMessageInfo

func (m *RunHistoryScoreAmountReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *RunHistoryScoreAmountReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *RunHistoryScoreAmountReq) GetReplace() bool {
	if m != nil {
		return m.Replace
	}
	return false
}

func (m *RunHistoryScoreAmountReq) GetAsync() bool {
	if m != nil {
		return m.Async
	}
	return false
}

type GetScoreAmountResp struct {
	Amount               uint64   `protobuf:"varint,1,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetScoreAmountResp) Reset()         { *m = GetScoreAmountResp{} }
func (m *GetScoreAmountResp) String() string { return proto.CompactTextString(m) }
func (*GetScoreAmountResp) ProtoMessage()    {}
func (*GetScoreAmountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{1}
}
func (m *GetScoreAmountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetScoreAmountResp.Unmarshal(m, b)
}
func (m *GetScoreAmountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetScoreAmountResp.Marshal(b, m, deterministic)
}
func (dst *GetScoreAmountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetScoreAmountResp.Merge(dst, src)
}
func (m *GetScoreAmountResp) XXX_Size() int {
	return xxx_messageInfo_GetScoreAmountResp.Size(m)
}
func (m *GetScoreAmountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetScoreAmountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetScoreAmountResp proto.InternalMessageInfo

func (m *GetScoreAmountResp) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type GenerateInventoryReq struct {
	InventoryTime        int64    `protobuf:"varint,1,opt,name=inventory_time,json=inventoryTime,proto3" json:"inventory_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenerateInventoryReq) Reset()         { *m = GenerateInventoryReq{} }
func (m *GenerateInventoryReq) String() string { return proto.CompactTextString(m) }
func (*GenerateInventoryReq) ProtoMessage()    {}
func (*GenerateInventoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{2}
}
func (m *GenerateInventoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenerateInventoryReq.Unmarshal(m, b)
}
func (m *GenerateInventoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenerateInventoryReq.Marshal(b, m, deterministic)
}
func (dst *GenerateInventoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenerateInventoryReq.Merge(dst, src)
}
func (m *GenerateInventoryReq) XXX_Size() int {
	return xxx_messageInfo_GenerateInventoryReq.Size(m)
}
func (m *GenerateInventoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenerateInventoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenerateInventoryReq proto.InternalMessageInfo

func (m *GenerateInventoryReq) GetInventoryTime() int64 {
	if m != nil {
		return m.InventoryTime
	}
	return 0
}

type GetInventoryReq struct {
	YearMonth            uint32   `protobuf:"varint,1,opt,name=year_month,json=yearMonth,proto3" json:"year_month,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInventoryReq) Reset()         { *m = GetInventoryReq{} }
func (m *GetInventoryReq) String() string { return proto.CompactTextString(m) }
func (*GetInventoryReq) ProtoMessage()    {}
func (*GetInventoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{3}
}
func (m *GetInventoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInventoryReq.Unmarshal(m, b)
}
func (m *GetInventoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInventoryReq.Marshal(b, m, deterministic)
}
func (dst *GetInventoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInventoryReq.Merge(dst, src)
}
func (m *GetInventoryReq) XXX_Size() int {
	return xxx_messageInfo_GetInventoryReq.Size(m)
}
func (m *GetInventoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInventoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetInventoryReq proto.InternalMessageInfo

func (m *GetInventoryReq) GetYearMonth() uint32 {
	if m != nil {
		return m.YearMonth
	}
	return 0
}

// 期末计算价值 = begin_shortcut + increase - decrease
// 差异价值 = 期末计算价值 - end_shortcut
type GetInventoryResp struct {
	BeginShortcut        uint64   `protobuf:"varint,1,opt,name=begin_shortcut,json=beginShortcut,proto3" json:"begin_shortcut,omitempty"`
	Increase             uint64   `protobuf:"varint,2,opt,name=increase,proto3" json:"increase,omitempty"`
	Decrease             uint64   `protobuf:"varint,3,opt,name=decrease,proto3" json:"decrease,omitempty"`
	EndShortcut          uint64   `protobuf:"varint,4,opt,name=end_shortcut,json=endShortcut,proto3" json:"end_shortcut,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetInventoryResp) Reset()         { *m = GetInventoryResp{} }
func (m *GetInventoryResp) String() string { return proto.CompactTextString(m) }
func (*GetInventoryResp) ProtoMessage()    {}
func (*GetInventoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{4}
}
func (m *GetInventoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetInventoryResp.Unmarshal(m, b)
}
func (m *GetInventoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetInventoryResp.Marshal(b, m, deterministic)
}
func (dst *GetInventoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetInventoryResp.Merge(dst, src)
}
func (m *GetInventoryResp) XXX_Size() int {
	return xxx_messageInfo_GetInventoryResp.Size(m)
}
func (m *GetInventoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetInventoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetInventoryResp proto.InternalMessageInfo

func (m *GetInventoryResp) GetBeginShortcut() uint64 {
	if m != nil {
		return m.BeginShortcut
	}
	return 0
}

func (m *GetInventoryResp) GetIncrease() uint64 {
	if m != nil {
		return m.Increase
	}
	return 0
}

func (m *GetInventoryResp) GetDecrease() uint64 {
	if m != nil {
		return m.Decrease
	}
	return 0
}

func (m *GetInventoryResp) GetEndShortcut() uint64 {
	if m != nil {
		return m.EndShortcut
	}
	return 0
}

type IdReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IdReq) Reset()         { *m = IdReq{} }
func (m *IdReq) String() string { return proto.CompactTextString(m) }
func (*IdReq) ProtoMessage()    {}
func (*IdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{5}
}
func (m *IdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IdReq.Unmarshal(m, b)
}
func (m *IdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IdReq.Marshal(b, m, deterministic)
}
func (dst *IdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IdReq.Merge(dst, src)
}
func (m *IdReq) XXX_Size() int {
	return xxx_messageInfo_IdReq.Size(m)
}
func (m *IdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IdReq.DiscardUnknown(m)
}

var xxx_messageInfo_IdReq proto.InternalMessageInfo

func (m *IdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type TempZipUrlResp struct {
	TempZipUrl           string   `protobuf:"bytes,1,opt,name=temp_zip_url,json=tempZipUrl,proto3" json:"temp_zip_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TempZipUrlResp) Reset()         { *m = TempZipUrlResp{} }
func (m *TempZipUrlResp) String() string { return proto.CompactTextString(m) }
func (*TempZipUrlResp) ProtoMessage()    {}
func (*TempZipUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{6}
}
func (m *TempZipUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TempZipUrlResp.Unmarshal(m, b)
}
func (m *TempZipUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TempZipUrlResp.Marshal(b, m, deterministic)
}
func (dst *TempZipUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TempZipUrlResp.Merge(dst, src)
}
func (m *TempZipUrlResp) XXX_Size() int {
	return xxx_messageInfo_TempZipUrlResp.Size(m)
}
func (m *TempZipUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TempZipUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_TempZipUrlResp proto.InternalMessageInfo

func (m *TempZipUrlResp) GetTempZipUrl() string {
	if m != nil {
		return m.TempZipUrl
	}
	return ""
}

type ExportAllUserScoreTaskDataList struct {
	List                 []*ExportAllUserScoreTaskData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *ExportAllUserScoreTaskDataList) Reset()         { *m = ExportAllUserScoreTaskDataList{} }
func (m *ExportAllUserScoreTaskDataList) String() string { return proto.CompactTextString(m) }
func (*ExportAllUserScoreTaskDataList) ProtoMessage()    {}
func (*ExportAllUserScoreTaskDataList) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{7}
}
func (m *ExportAllUserScoreTaskDataList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportAllUserScoreTaskDataList.Unmarshal(m, b)
}
func (m *ExportAllUserScoreTaskDataList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportAllUserScoreTaskDataList.Marshal(b, m, deterministic)
}
func (dst *ExportAllUserScoreTaskDataList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportAllUserScoreTaskDataList.Merge(dst, src)
}
func (m *ExportAllUserScoreTaskDataList) XXX_Size() int {
	return xxx_messageInfo_ExportAllUserScoreTaskDataList.Size(m)
}
func (m *ExportAllUserScoreTaskDataList) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportAllUserScoreTaskDataList.DiscardUnknown(m)
}

var xxx_messageInfo_ExportAllUserScoreTaskDataList proto.InternalMessageInfo

func (m *ExportAllUserScoreTaskDataList) GetList() []*ExportAllUserScoreTaskData {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ExportAllUserScoreTaskDataList) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ExportAllUserScoreTaskData struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Type                 uint32   `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	Percent              uint32   `protobuf:"varint,5,opt,name=percent,proto3" json:"percent,omitempty"`
	CreateTime           uint32   `protobuf:"varint,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ErrData              string   `protobuf:"bytes,7,opt,name=err_data,json=errData,proto3" json:"err_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportAllUserScoreTaskData) Reset()         { *m = ExportAllUserScoreTaskData{} }
func (m *ExportAllUserScoreTaskData) String() string { return proto.CompactTextString(m) }
func (*ExportAllUserScoreTaskData) ProtoMessage()    {}
func (*ExportAllUserScoreTaskData) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{8}
}
func (m *ExportAllUserScoreTaskData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportAllUserScoreTaskData.Unmarshal(m, b)
}
func (m *ExportAllUserScoreTaskData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportAllUserScoreTaskData.Marshal(b, m, deterministic)
}
func (dst *ExportAllUserScoreTaskData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportAllUserScoreTaskData.Merge(dst, src)
}
func (m *ExportAllUserScoreTaskData) XXX_Size() int {
	return xxx_messageInfo_ExportAllUserScoreTaskData.Size(m)
}
func (m *ExportAllUserScoreTaskData) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportAllUserScoreTaskData.DiscardUnknown(m)
}

var xxx_messageInfo_ExportAllUserScoreTaskData proto.InternalMessageInfo

func (m *ExportAllUserScoreTaskData) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExportAllUserScoreTaskData) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ExportAllUserScoreTaskData) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ExportAllUserScoreTaskData) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ExportAllUserScoreTaskData) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

func (m *ExportAllUserScoreTaskData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ExportAllUserScoreTaskData) GetErrData() string {
	if m != nil {
		return m.ErrData
	}
	return ""
}

type OffsetReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OffsetReq) Reset()         { *m = OffsetReq{} }
func (m *OffsetReq) String() string { return proto.CompactTextString(m) }
func (*OffsetReq) ProtoMessage()    {}
func (*OffsetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{9}
}
func (m *OffsetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OffsetReq.Unmarshal(m, b)
}
func (m *OffsetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OffsetReq.Marshal(b, m, deterministic)
}
func (dst *OffsetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OffsetReq.Merge(dst, src)
}
func (m *OffsetReq) XXX_Size() int {
	return xxx_messageInfo_OffsetReq.Size(m)
}
func (m *OffsetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OffsetReq.DiscardUnknown(m)
}

var xxx_messageInfo_OffsetReq proto.InternalMessageInfo

func (m *OffsetReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *OffsetReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type EmptyMsg struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyMsg) Reset()         { *m = EmptyMsg{} }
func (m *EmptyMsg) String() string { return proto.CompactTextString(m) }
func (*EmptyMsg) ProtoMessage()    {}
func (*EmptyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{10}
}
func (m *EmptyMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyMsg.Unmarshal(m, b)
}
func (m *EmptyMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyMsg.Marshal(b, m, deterministic)
}
func (dst *EmptyMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyMsg.Merge(dst, src)
}
func (m *EmptyMsg) XXX_Size() int {
	return xxx_messageInfo_EmptyMsg.Size(m)
}
func (m *EmptyMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyMsg.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyMsg proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type TimeRangeReq struct {
	BeginTime            uint32   `protobuf:"varint,1,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeReq) Reset()         { *m = TimeRangeReq{} }
func (m *TimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*TimeRangeReq) ProtoMessage()    {}
func (*TimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{11}
}
func (m *TimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeReq.Unmarshal(m, b)
}
func (m *TimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *TimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeReq.Merge(dst, src)
}
func (m *TimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_TimeRangeReq.Size(m)
}
func (m *TimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeReq proto.InternalMessageInfo

func (m *TimeRangeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRangeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeRangeReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type UidListReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidListReq) Reset()         { *m = UidListReq{} }
func (m *UidListReq) String() string { return proto.CompactTextString(m) }
func (*UidListReq) ProtoMessage()    {}
func (*UidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{12}
}
func (m *UidListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidListReq.Unmarshal(m, b)
}
func (m *UidListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidListReq.Marshal(b, m, deterministic)
}
func (dst *UidListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidListReq.Merge(dst, src)
}
func (m *UidListReq) XXX_Size() int {
	return xxx_messageInfo_UidListReq.Size(m)
}
func (m *UidListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidListReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidListReq proto.InternalMessageInfo

func (m *UidListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *UidListReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UidListReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type UidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=beginTime,proto3" json:"beginTime,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UidReq) Reset()         { *m = UidReq{} }
func (m *UidReq) String() string { return proto.CompactTextString(m) }
func (*UidReq) ProtoMessage()    {}
func (*UidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{13}
}
func (m *UidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UidReq.Unmarshal(m, b)
}
func (m *UidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UidReq.Marshal(b, m, deterministic)
}
func (dst *UidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UidReq.Merge(dst, src)
}
func (m *UidReq) XXX_Size() int {
	return xxx_messageInfo_UidReq.Size(m)
}
func (m *UidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UidReq.DiscardUnknown(m)
}

var xxx_messageInfo_UidReq proto.InternalMessageInfo

func (m *UidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UidReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *UidReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *UidReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *UidReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type UserScoreSimpleData struct {
	Uid                      uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LastRemainScore          int64    `protobuf:"varint,2,opt,name=last_remain_score,json=lastRemainScore,proto3" json:"last_remain_score,omitempty"`
	SumScore                 int64    `protobuf:"varint,3,opt,name=sum_score,json=sumScore,proto3" json:"sum_score,omitempty"`
	SettlementScore          int64    `protobuf:"varint,4,opt,name=settlement_score,json=settlementScore,proto3" json:"settlement_score,omitempty"`
	GuildExchangeScore       int64    `protobuf:"varint,5,opt,name=guild_exchange_score,json=guildExchangeScore,proto3" json:"guild_exchange_score,omitempty"`
	FailedRollScore          int64    `protobuf:"varint,6,opt,name=failed_roll_score,json=failedRollScore,proto3" json:"failed_roll_score,omitempty"`
	ExchangeTbeanScore       int64    `protobuf:"varint,7,opt,name=exchange_tbean_score,json=exchangeTbeanScore,proto3" json:"exchange_tbean_score,omitempty"`
	OfficialRewardScore      int64    `protobuf:"varint,8,opt,name=official_reward_score,json=officialRewardScore,proto3" json:"official_reward_score,omitempty"`
	OfficialRecycleScore     int64    `protobuf:"varint,9,opt,name=official_recycle_score,json=officialRecycleScore,proto3" json:"official_recycle_score,omitempty"`
	WerewolfScore            int64    `protobuf:"varint,10,opt,name=werewolf_score,json=werewolfScore,proto3" json:"werewolf_score,omitempty"`
	LastRemainTbeanOnlyScore int64    `protobuf:"varint,11,opt,name=last_remain_tbean_only_score,json=lastRemainTbeanOnlyScore,proto3" json:"last_remain_tbean_only_score,omitempty"`
	ExchangeTbeanScore_2     int64    `protobuf:"varint,12,opt,name=exchange_tbean_score_2,json=exchangeTbeanScore2,proto3" json:"exchange_tbean_score_2,omitempty"`
	RemainScore              int64    `protobuf:"varint,13,opt,name=remain_score,json=remainScore,proto3" json:"remain_score,omitempty"`
	RemainTbean_2            int64    `protobuf:"varint,14,opt,name=remain_tbean_2,json=remainTbean2,proto3" json:"remain_tbean_2,omitempty"`
	SumScore_2               int64    `protobuf:"varint,15,opt,name=sum_score_2,json=sumScore2,proto3" json:"sum_score_2,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *UserScoreSimpleData) Reset()         { *m = UserScoreSimpleData{} }
func (m *UserScoreSimpleData) String() string { return proto.CompactTextString(m) }
func (*UserScoreSimpleData) ProtoMessage()    {}
func (*UserScoreSimpleData) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{14}
}
func (m *UserScoreSimpleData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserScoreSimpleData.Unmarshal(m, b)
}
func (m *UserScoreSimpleData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserScoreSimpleData.Marshal(b, m, deterministic)
}
func (dst *UserScoreSimpleData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserScoreSimpleData.Merge(dst, src)
}
func (m *UserScoreSimpleData) XXX_Size() int {
	return xxx_messageInfo_UserScoreSimpleData.Size(m)
}
func (m *UserScoreSimpleData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserScoreSimpleData.DiscardUnknown(m)
}

var xxx_messageInfo_UserScoreSimpleData proto.InternalMessageInfo

func (m *UserScoreSimpleData) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserScoreSimpleData) GetLastRemainScore() int64 {
	if m != nil {
		return m.LastRemainScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetSumScore() int64 {
	if m != nil {
		return m.SumScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetSettlementScore() int64 {
	if m != nil {
		return m.SettlementScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetGuildExchangeScore() int64 {
	if m != nil {
		return m.GuildExchangeScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetFailedRollScore() int64 {
	if m != nil {
		return m.FailedRollScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetExchangeTbeanScore() int64 {
	if m != nil {
		return m.ExchangeTbeanScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetOfficialRewardScore() int64 {
	if m != nil {
		return m.OfficialRewardScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetOfficialRecycleScore() int64 {
	if m != nil {
		return m.OfficialRecycleScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetWerewolfScore() int64 {
	if m != nil {
		return m.WerewolfScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetLastRemainTbeanOnlyScore() int64 {
	if m != nil {
		return m.LastRemainTbeanOnlyScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetExchangeTbeanScore_2() int64 {
	if m != nil {
		return m.ExchangeTbeanScore_2
	}
	return 0
}

func (m *UserScoreSimpleData) GetRemainScore() int64 {
	if m != nil {
		return m.RemainScore
	}
	return 0
}

func (m *UserScoreSimpleData) GetRemainTbean_2() int64 {
	if m != nil {
		return m.RemainTbean_2
	}
	return 0
}

func (m *UserScoreSimpleData) GetSumScore_2() int64 {
	if m != nil {
		return m.SumScore_2
	}
	return 0
}

type UserScoreData struct {
	SimpleData           *UserScoreSimpleData `protobuf:"bytes,1,opt,name=simple_data,json=simpleData,proto3" json:"simple_data,omitempty"`
	Ttid                 string               `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string               `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	FreezeStatus         uint32               `protobuf:"varint,4,opt,name=freeze_status,json=freezeStatus,proto3" json:"freeze_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserScoreData) Reset()         { *m = UserScoreData{} }
func (m *UserScoreData) String() string { return proto.CompactTextString(m) }
func (*UserScoreData) ProtoMessage()    {}
func (*UserScoreData) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{15}
}
func (m *UserScoreData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserScoreData.Unmarshal(m, b)
}
func (m *UserScoreData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserScoreData.Marshal(b, m, deterministic)
}
func (dst *UserScoreData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserScoreData.Merge(dst, src)
}
func (m *UserScoreData) XXX_Size() int {
	return xxx_messageInfo_UserScoreData.Size(m)
}
func (m *UserScoreData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserScoreData.DiscardUnknown(m)
}

var xxx_messageInfo_UserScoreData proto.InternalMessageInfo

func (m *UserScoreData) GetSimpleData() *UserScoreSimpleData {
	if m != nil {
		return m.SimpleData
	}
	return nil
}

func (m *UserScoreData) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *UserScoreData) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserScoreData) GetFreezeStatus() uint32 {
	if m != nil {
		return m.FreezeStatus
	}
	return 0
}

type UserScoreList struct {
	List                 []*UserScoreData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserScoreList) Reset()         { *m = UserScoreList{} }
func (m *UserScoreList) String() string { return proto.CompactTextString(m) }
func (*UserScoreList) ProtoMessage()    {}
func (*UserScoreList) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{16}
}
func (m *UserScoreList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserScoreList.Unmarshal(m, b)
}
func (m *UserScoreList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserScoreList.Marshal(b, m, deterministic)
}
func (dst *UserScoreList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserScoreList.Merge(dst, src)
}
func (m *UserScoreList) XXX_Size() int {
	return xxx_messageInfo_UserScoreList.Size(m)
}
func (m *UserScoreList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserScoreList.DiscardUnknown(m)
}

var xxx_messageInfo_UserScoreList proto.InternalMessageInfo

func (m *UserScoreList) GetList() []*UserScoreData {
	if m != nil {
		return m.List
	}
	return nil
}

type UserPresentData struct {
	ToUid                uint32   `protobuf:"varint,1,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToTtid               string   `protobuf:"bytes,2,opt,name=to_ttid,json=toTtid,proto3" json:"to_ttid,omitempty"`
	ToNickname           string   `protobuf:"bytes,3,opt,name=to_nickname,json=toNickname,proto3" json:"to_nickname,omitempty"`
	CreateTime           uint32   `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ChangeScore          int32    `protobuf:"varint,5,opt,name=change_score,json=changeScore,proto3" json:"change_score,omitempty"`
	FinallyScore         uint32   `protobuf:"varint,6,opt,name=finally_score,json=finallyScore,proto3" json:"finally_score,omitempty"`
	FromTtid             string   `protobuf:"bytes,7,opt,name=from_ttid,json=fromTtid,proto3" json:"from_ttid,omitempty"`
	FromNickname         string   `protobuf:"bytes,8,opt,name=from_nickname,json=fromNickname,proto3" json:"from_nickname,omitempty"`
	DisplayId            uint32   `protobuf:"varint,9,opt,name=display_id,json=displayId,proto3" json:"display_id,omitempty"`
	ChannelName          string   `protobuf:"bytes,10,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	ItemId               uint32   `protobuf:"varint,11,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,12,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	ItemPrice            uint32   `protobuf:"varint,13,opt,name=item_price,json=itemPrice,proto3" json:"item_price,omitempty"`
	ItemCount            uint32   `protobuf:"varint,14,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	ChannelViewId        string   `protobuf:"bytes,15,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	ScoreType            uint32   `protobuf:"varint,16,opt,name=score_type,json=scoreType,proto3" json:"score_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentData) Reset()         { *m = UserPresentData{} }
func (m *UserPresentData) String() string { return proto.CompactTextString(m) }
func (*UserPresentData) ProtoMessage()    {}
func (*UserPresentData) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{17}
}
func (m *UserPresentData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentData.Unmarshal(m, b)
}
func (m *UserPresentData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentData.Marshal(b, m, deterministic)
}
func (dst *UserPresentData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentData.Merge(dst, src)
}
func (m *UserPresentData) XXX_Size() int {
	return xxx_messageInfo_UserPresentData.Size(m)
}
func (m *UserPresentData) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentData.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentData proto.InternalMessageInfo

func (m *UserPresentData) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *UserPresentData) GetToTtid() string {
	if m != nil {
		return m.ToTtid
	}
	return ""
}

func (m *UserPresentData) GetToNickname() string {
	if m != nil {
		return m.ToNickname
	}
	return ""
}

func (m *UserPresentData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *UserPresentData) GetChangeScore() int32 {
	if m != nil {
		return m.ChangeScore
	}
	return 0
}

func (m *UserPresentData) GetFinallyScore() uint32 {
	if m != nil {
		return m.FinallyScore
	}
	return 0
}

func (m *UserPresentData) GetFromTtid() string {
	if m != nil {
		return m.FromTtid
	}
	return ""
}

func (m *UserPresentData) GetFromNickname() string {
	if m != nil {
		return m.FromNickname
	}
	return ""
}

func (m *UserPresentData) GetDisplayId() uint32 {
	if m != nil {
		return m.DisplayId
	}
	return 0
}

func (m *UserPresentData) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *UserPresentData) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *UserPresentData) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *UserPresentData) GetItemPrice() uint32 {
	if m != nil {
		return m.ItemPrice
	}
	return 0
}

func (m *UserPresentData) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *UserPresentData) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

func (m *UserPresentData) GetScoreType() uint32 {
	if m != nil {
		return m.ScoreType
	}
	return 0
}

type UserPresentList struct {
	List                 []*UserPresentData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32             `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UserPresentList) Reset()         { *m = UserPresentList{} }
func (m *UserPresentList) String() string { return proto.CompactTextString(m) }
func (*UserPresentList) ProtoMessage()    {}
func (*UserPresentList) Descriptor() ([]byte, []int) {
	return fileDescriptor_userscore_mgr_6fe064f72da162fe, []int{18}
}
func (m *UserPresentList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentList.Unmarshal(m, b)
}
func (m *UserPresentList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentList.Marshal(b, m, deterministic)
}
func (dst *UserPresentList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentList.Merge(dst, src)
}
func (m *UserPresentList) XXX_Size() int {
	return xxx_messageInfo_UserPresentList.Size(m)
}
func (m *UserPresentList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentList.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentList proto.InternalMessageInfo

func (m *UserPresentList) GetList() []*UserPresentData {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *UserPresentList) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*RunHistoryScoreAmountReq)(nil), "userscore_mgr.RunHistoryScoreAmountReq")
	proto.RegisterType((*GetScoreAmountResp)(nil), "userscore_mgr.GetScoreAmountResp")
	proto.RegisterType((*GenerateInventoryReq)(nil), "userscore_mgr.GenerateInventoryReq")
	proto.RegisterType((*GetInventoryReq)(nil), "userscore_mgr.GetInventoryReq")
	proto.RegisterType((*GetInventoryResp)(nil), "userscore_mgr.GetInventoryResp")
	proto.RegisterType((*IdReq)(nil), "userscore_mgr.IdReq")
	proto.RegisterType((*TempZipUrlResp)(nil), "userscore_mgr.TempZipUrlResp")
	proto.RegisterType((*ExportAllUserScoreTaskDataList)(nil), "userscore_mgr.ExportAllUserScoreTaskDataList")
	proto.RegisterType((*ExportAllUserScoreTaskData)(nil), "userscore_mgr.ExportAllUserScoreTaskData")
	proto.RegisterType((*OffsetReq)(nil), "userscore_mgr.OffsetReq")
	proto.RegisterType((*EmptyMsg)(nil), "userscore_mgr.EmptyMsg")
	proto.RegisterType((*TimeRangeReq)(nil), "userscore_mgr.TimeRangeReq")
	proto.RegisterType((*UidListReq)(nil), "userscore_mgr.UidListReq")
	proto.RegisterType((*UidReq)(nil), "userscore_mgr.UidReq")
	proto.RegisterType((*UserScoreSimpleData)(nil), "userscore_mgr.UserScoreSimpleData")
	proto.RegisterType((*UserScoreData)(nil), "userscore_mgr.UserScoreData")
	proto.RegisterType((*UserScoreList)(nil), "userscore_mgr.UserScoreList")
	proto.RegisterType((*UserPresentData)(nil), "userscore_mgr.UserPresentData")
	proto.RegisterType((*UserPresentList)(nil), "userscore_mgr.UserPresentList")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserScoreMgrClient is the client API for UserScoreMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserScoreMgrClient interface {
	// 通过uid查询用户积分
	GetUserScoreByUidList(ctx context.Context, in *UidListReq, opts ...grpc.CallOption) (*UserScoreList, error)
	// 创建导出所有用户积分任务
	CreateExportAllUserScoreTask(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 查看导出任务列表
	GetExportAllUserScoreTaskList(ctx context.Context, in *OffsetReq, opts ...grpc.CallOption) (*ExportAllUserScoreTaskDataList, error)
	// 获取临时下载地址
	GetTempZipUrl(ctx context.Context, in *IdReq, opts ...grpc.CallOption) (*TempZipUrlResp, error)
	// 收礼明细
	GetUserPresentByUidList(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*UserPresentList, error)
	// 礼物积分库存
	GetInventory(ctx context.Context, in *GetInventoryReq, opts ...grpc.CallOption) (*GetInventoryResp, error)
	// 跑礼物积分库存快照
	GenerateInventory(ctx context.Context, in *GenerateInventoryReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 跑时间段里的累计积分
	RunHistoryScoreAmount(ctx context.Context, in *RunHistoryScoreAmountReq, opts ...grpc.CallOption) (*EmptyMsg, error)
	// 获取某个uid的累计积分
	GetScoreAmount(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetScoreAmountResp, error)
}

type userScoreMgrClient struct {
	cc *grpc.ClientConn
}

func NewUserScoreMgrClient(cc *grpc.ClientConn) UserScoreMgrClient {
	return &userScoreMgrClient{cc}
}

func (c *userScoreMgrClient) GetUserScoreByUidList(ctx context.Context, in *UidListReq, opts ...grpc.CallOption) (*UserScoreList, error) {
	out := new(UserScoreList)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GetUserScoreByUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) CreateExportAllUserScoreTask(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/CreateExportAllUserScoreTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) GetExportAllUserScoreTaskList(ctx context.Context, in *OffsetReq, opts ...grpc.CallOption) (*ExportAllUserScoreTaskDataList, error) {
	out := new(ExportAllUserScoreTaskDataList)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GetExportAllUserScoreTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) GetTempZipUrl(ctx context.Context, in *IdReq, opts ...grpc.CallOption) (*TempZipUrlResp, error) {
	out := new(TempZipUrlResp)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GetTempZipUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) GetUserPresentByUidList(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*UserPresentList, error) {
	out := new(UserPresentList)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GetUserPresentByUidList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) GetInventory(ctx context.Context, in *GetInventoryReq, opts ...grpc.CallOption) (*GetInventoryResp, error) {
	out := new(GetInventoryResp)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GetInventory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) GenerateInventory(ctx context.Context, in *GenerateInventoryReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GenerateInventory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) RunHistoryScoreAmount(ctx context.Context, in *RunHistoryScoreAmountReq, opts ...grpc.CallOption) (*EmptyMsg, error) {
	out := new(EmptyMsg)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/RunHistoryScoreAmount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userScoreMgrClient) GetScoreAmount(ctx context.Context, in *UidReq, opts ...grpc.CallOption) (*GetScoreAmountResp, error) {
	out := new(GetScoreAmountResp)
	err := c.cc.Invoke(ctx, "/userscore_mgr.UserScoreMgr/GetScoreAmount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserScoreMgrServer is the server API for UserScoreMgr service.
type UserScoreMgrServer interface {
	// 通过uid查询用户积分
	GetUserScoreByUidList(context.Context, *UidListReq) (*UserScoreList, error)
	// 创建导出所有用户积分任务
	CreateExportAllUserScoreTask(context.Context, *TimeRangeReq) (*EmptyMsg, error)
	// 查看导出任务列表
	GetExportAllUserScoreTaskList(context.Context, *OffsetReq) (*ExportAllUserScoreTaskDataList, error)
	// 获取临时下载地址
	GetTempZipUrl(context.Context, *IdReq) (*TempZipUrlResp, error)
	// 收礼明细
	GetUserPresentByUidList(context.Context, *UidReq) (*UserPresentList, error)
	// 礼物积分库存
	GetInventory(context.Context, *GetInventoryReq) (*GetInventoryResp, error)
	// 跑礼物积分库存快照
	GenerateInventory(context.Context, *GenerateInventoryReq) (*EmptyMsg, error)
	// 跑时间段里的累计积分
	RunHistoryScoreAmount(context.Context, *RunHistoryScoreAmountReq) (*EmptyMsg, error)
	// 获取某个uid的累计积分
	GetScoreAmount(context.Context, *UidReq) (*GetScoreAmountResp, error)
}

func RegisterUserScoreMgrServer(s *grpc.Server, srv UserScoreMgrServer) {
	s.RegisterService(&_UserScoreMgr_serviceDesc, srv)
}

func _UserScoreMgr_GetUserScoreByUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GetUserScoreByUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GetUserScoreByUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GetUserScoreByUidList(ctx, req.(*UidListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_CreateExportAllUserScoreTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).CreateExportAllUserScoreTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/CreateExportAllUserScoreTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).CreateExportAllUserScoreTask(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_GetExportAllUserScoreTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OffsetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GetExportAllUserScoreTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GetExportAllUserScoreTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GetExportAllUserScoreTaskList(ctx, req.(*OffsetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_GetTempZipUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GetTempZipUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GetTempZipUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GetTempZipUrl(ctx, req.(*IdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_GetUserPresentByUidList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GetUserPresentByUidList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GetUserPresentByUidList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GetUserPresentByUidList(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_GetInventory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetInventoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GetInventory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GetInventory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GetInventory(ctx, req.(*GetInventoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_GenerateInventory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateInventoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GenerateInventory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GenerateInventory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GenerateInventory(ctx, req.(*GenerateInventoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_RunHistoryScoreAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunHistoryScoreAmountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).RunHistoryScoreAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/RunHistoryScoreAmount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).RunHistoryScoreAmount(ctx, req.(*RunHistoryScoreAmountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserScoreMgr_GetScoreAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserScoreMgrServer).GetScoreAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/userscore_mgr.UserScoreMgr/GetScoreAmount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserScoreMgrServer).GetScoreAmount(ctx, req.(*UidReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserScoreMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "userscore_mgr.UserScoreMgr",
	HandlerType: (*UserScoreMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetUserScoreByUidList",
			Handler:    _UserScoreMgr_GetUserScoreByUidList_Handler,
		},
		{
			MethodName: "CreateExportAllUserScoreTask",
			Handler:    _UserScoreMgr_CreateExportAllUserScoreTask_Handler,
		},
		{
			MethodName: "GetExportAllUserScoreTaskList",
			Handler:    _UserScoreMgr_GetExportAllUserScoreTaskList_Handler,
		},
		{
			MethodName: "GetTempZipUrl",
			Handler:    _UserScoreMgr_GetTempZipUrl_Handler,
		},
		{
			MethodName: "GetUserPresentByUidList",
			Handler:    _UserScoreMgr_GetUserPresentByUidList_Handler,
		},
		{
			MethodName: "GetInventory",
			Handler:    _UserScoreMgr_GetInventory_Handler,
		},
		{
			MethodName: "GenerateInventory",
			Handler:    _UserScoreMgr_GenerateInventory_Handler,
		},
		{
			MethodName: "RunHistoryScoreAmount",
			Handler:    _UserScoreMgr_RunHistoryScoreAmount_Handler,
		},
		{
			MethodName: "GetScoreAmount",
			Handler:    _UserScoreMgr_GetScoreAmount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/userscore-mgr/userscore-mgr.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/userscore-mgr/userscore-mgr.proto", fileDescriptor_userscore_mgr_6fe064f72da162fe)
}

var fileDescriptor_userscore_mgr_6fe064f72da162fe = []byte{
	// 1477 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x57, 0x4b, 0x73, 0x13, 0x49,
	0x12, 0xb6, 0xac, 0x77, 0xea, 0x61, 0xd3, 0xf8, 0x21, 0x0b, 0x9b, 0x47, 0x03, 0xbb, 0x40, 0x2c,
	0xb6, 0x57, 0xec, 0x1e, 0xf6, 0xc0, 0x46, 0x00, 0xcb, 0x7a, 0x1d, 0xc1, 0x6b, 0xdb, 0x36, 0x07,
	0x38, 0x74, 0x34, 0xdd, 0x29, 0x51, 0x43, 0xbf, 0xa8, 0x2a, 0xd9, 0x88, 0xd3, 0x1c, 0xe6, 0x34,
	0xe7, 0x39, 0xcd, 0x3f, 0x98, 0xfb, 0xfc, 0x90, 0x89, 0xf9, 0x25, 0xf3, 0x13, 0x26, 0x2a, 0xab,
	0xba, 0x25, 0xb5, 0x6c, 0xc7, 0xc4, 0x9c, 0xa4, 0x7c, 0x7c, 0x59, 0x99, 0x59, 0x5f, 0x65, 0x55,
	0xc3, 0xbe, 0x94, 0x7b, 0x9f, 0xc7, 0xcc, 0xff, 0x24, 0x58, 0x78, 0x8a, 0x7c, 0x6f, 0x2c, 0x90,
	0x0b, 0x3f, 0xe1, 0xf8, 0x30, 0x1a, 0x15, 0xa4, 0xdd, 0x94, 0x27, 0x32, 0xb1, 0x3a, 0xb9, 0xd2,
	0x8d, 0x46, 0xbc, 0xbf, 0x5b, 0x08, 0x80, 0x5f, 0x24, 0xc6, 0x82, 0x25, 0xf1, 0x5e, 0x92, 0x4a,
	0x96, 0xc4, 0x22, 0xfb, 0xd5, 0x70, 0xfb, 0xbb, 0x12, 0xf4, 0x9c, 0x71, 0xfc, 0x3f, 0x26, 0x64,
	0xc2, 0x27, 0x47, 0x2a, 0xce, 0x93, 0x28, 0x19, 0xc7, 0xd2, 0xc1, 0xcf, 0xd6, 0x0e, 0xc0, 0x07,
	0x1c, 0xb1, 0xd8, 0x95, 0x2c, 0xc2, 0x5e, 0xe9, 0x66, 0xe9, 0x5e, 0xd9, 0x69, 0x92, 0xe6, 0x98,
	0x45, 0x68, 0x6d, 0x41, 0x03, 0xe3, 0x40, 0x1b, 0x97, 0xc9, 0x58, 0xc7, 0x38, 0x20, 0x53, 0x0f,
	0xea, 0x1c, 0xd3, 0xd0, 0xf3, 0xb1, 0x57, 0xbe, 0x59, 0xba, 0xd7, 0x70, 0x32, 0xd1, 0x5a, 0x83,
	0xaa, 0x27, 0x26, 0xb1, 0xdf, 0xab, 0x90, 0x5e, 0x0b, 0xf6, 0xdf, 0xc0, 0x3a, 0x40, 0x39, 0xb7,
	0xbc, 0x48, 0xad, 0x0d, 0xa8, 0x79, 0x24, 0xd1, 0xda, 0x15, 0xc7, 0x48, 0xf6, 0x63, 0x58, 0x3b,
	0xc0, 0x18, 0xb9, 0x27, 0xf1, 0x30, 0x3e, 0xc5, 0x58, 0xa5, 0xae, 0xf2, 0xbd, 0x0b, 0x5d, 0x96,
	0xc9, 0xb3, 0x39, 0x77, 0x72, 0xad, 0x4a, 0xce, 0xde, 0x87, 0x95, 0x03, 0x94, 0x73, 0xc8, 0x1d,
	0x80, 0x09, 0x7a, 0xdc, 0x8d, 0x92, 0x58, 0x7e, 0x24, 0x54, 0xc7, 0x69, 0x2a, 0xcd, 0x4b, 0xa5,
	0xb0, 0x7f, 0x28, 0xc1, 0xea, 0x3c, 0x44, 0xa4, 0x6a, 0x35, 0xdd, 0x1d, 0xf1, 0x31, 0xe1, 0xd2,
	0x1f, 0x67, 0x59, 0x76, 0x48, 0x7b, 0x64, 0x94, 0x56, 0x1f, 0x1a, 0x2c, 0xf6, 0x39, 0x7a, 0x42,
	0x77, 0xa9, 0xe2, 0xe4, 0xb2, 0xb2, 0x05, 0x68, 0x6c, 0x65, 0x6d, 0xcb, 0x64, 0xeb, 0x16, 0xb4,
	0x55, 0x77, 0xf3, 0xe0, 0x15, 0xb2, 0xb7, 0x30, 0x0e, 0xb2, 0xd0, 0xf6, 0x26, 0x54, 0x0f, 0x03,
	0x95, 0x7e, 0x17, 0x96, 0x59, 0x60, 0xd2, 0x5e, 0x66, 0x81, 0x3d, 0x80, 0xee, 0x31, 0x46, 0xe9,
	0x3b, 0x96, 0x9e, 0xf0, 0x90, 0x92, 0xbd, 0x09, 0x6d, 0x89, 0x51, 0xea, 0x7e, 0x65, 0xa9, 0x3b,
	0xe6, 0x21, 0xf9, 0x36, 0x1d, 0x90, 0xb9, 0x97, 0x3d, 0x86, 0xeb, 0xcf, 0xbf, 0xa4, 0x09, 0x97,
	0x4f, 0xc2, 0xf0, 0x44, 0x20, 0xa7, 0xcd, 0x38, 0xf6, 0xc4, 0xa7, 0xff, 0x78, 0xd2, 0x7b, 0xc1,
	0x84, 0xb4, 0x1e, 0x43, 0x25, 0x64, 0x42, 0x95, 0x59, 0xbe, 0xd7, 0x1a, 0xdc, 0xdf, 0x9d, 0x63,
	0xde, 0xee, 0xc5, 0x60, 0x87, 0x60, 0x6a, 0xe7, 0x65, 0x22, 0xbd, 0x90, 0xba, 0xd0, 0x71, 0xb4,
	0x60, 0xff, 0x52, 0x82, 0xfe, 0xc5, 0xd0, 0x62, 0x65, 0x05, 0x4a, 0xea, 0x48, 0x17, 0x50, 0xb2,
	0x4c, 0xc6, 0x9c, 0x92, 0x16, 0x54, 0xe4, 0x24, 0x45, 0xea, 0x63, 0xc7, 0xa1, 0xff, 0x8a, 0xa6,
	0x29, 0x72, 0x1f, 0x63, 0xd9, 0xab, 0x6a, 0x6f, 0x23, 0x5a, 0x37, 0xa0, 0xa5, 0xf6, 0x41, 0xa2,
	0x8e, 0x55, 0x23, 0x2b, 0x68, 0x55, 0xbe, 0x12, 0xe7, 0x6e, 0xe0, 0x49, 0xaf, 0x57, 0xa7, 0x66,
	0xd6, 0x91, 0x73, 0x95, 0xb3, 0xfd, 0x2f, 0x68, 0xbe, 0x1e, 0x0e, 0x05, 0xd2, 0x19, 0xda, 0x80,
	0x5a, 0x42, 0x82, 0x29, 0xc2, 0x48, 0xaa, 0x1b, 0x21, 0x8b, 0x98, 0xcc, 0xba, 0x41, 0x82, 0x0d,
	0xd0, 0x78, 0x1e, 0xa5, 0x72, 0xf2, 0x52, 0x8c, 0xec, 0x77, 0xd0, 0x56, 0x2b, 0x39, 0x5e, 0x3c,
	0x42, 0x15, 0x69, 0x1b, 0xa6, 0x85, 0x66, 0x14, 0x9d, 0x56, 0xde, 0x83, 0xac, 0x52, 0x13, 0x71,
	0xa1, 0xf0, 0xf2, 0xb4, 0x70, 0xdb, 0x05, 0x38, 0x61, 0x81, 0xda, 0x55, 0x15, 0x79, 0x0b, 0x1a,
	0x63, 0x16, 0xb8, 0xf9, 0xe6, 0x76, 0x9c, 0xfa, 0x58, 0x5b, 0xe7, 0x17, 0x5d, 0xbe, 0x64, 0xd1,
	0xf9, 0x6e, 0xdb, 0xdf, 0x96, 0xa0, 0x76, 0xc2, 0x88, 0x9c, 0xab, 0x50, 0x1e, 0xe7, 0x7b, 0xa8,
	0xfe, 0xfe, 0xd9, 0xa0, 0x33, 0xbd, 0xac, 0x9c, 0xdf, 0xcb, 0xea, 0x6c, 0x2f, 0x7f, 0xac, 0xc2,
	0xd5, 0x9c, 0x50, 0x47, 0x2c, 0x4a, 0x43, 0x24, 0x4a, 0x2d, 0xe6, 0xf3, 0x00, 0xae, 0x84, 0x9e,
	0x90, 0x2e, 0xc7, 0xc8, 0x53, 0xe7, 0x59, 0x01, 0xcc, 0x44, 0x5b, 0x51, 0x06, 0x87, 0xf4, 0x14,
	0xc7, 0xba, 0x06, 0x4d, 0x31, 0x8e, 0x8c, 0x4f, 0x99, 0x7c, 0x1a, 0x62, 0x1c, 0x69, 0xe3, 0x7d,
	0x58, 0x15, 0x28, 0x65, 0x88, 0x11, 0xc6, 0xd2, 0xf8, 0x54, 0x74, 0x9c, 0xa9, 0x5e, 0xbb, 0xee,
	0xc3, 0xda, 0x68, 0xcc, 0xc2, 0xc0, 0xc5, 0x2f, 0xfe, 0x47, 0xb5, 0xc5, 0xc6, 0xbd, 0x4a, 0xee,
	0x16, 0xd9, 0x9e, 0x1b, 0x93, 0x46, 0x3c, 0x80, 0x2b, 0x43, 0x8f, 0x85, 0x18, 0xb8, 0x3c, 0x09,
	0x43, 0xe3, 0x5e, 0xd3, 0xd1, 0xb5, 0xc1, 0x49, 0xc2, 0x30, 0x8f, 0x9e, 0xc7, 0x95, 0x1f, 0xd0,
	0xcb, 0x8a, 0xaa, 0xeb, 0xe8, 0x99, 0xed, 0x58, 0x99, 0x34, 0x62, 0x00, 0xeb, 0xc9, 0x70, 0xc8,
	0x7c, 0xe6, 0x85, 0x2e, 0xc7, 0x33, 0x8f, 0x07, 0x06, 0xd2, 0x20, 0xc8, 0xd5, 0xcc, 0xe8, 0x90,
	0x4d, 0x63, 0xfe, 0x01, 0x1b, 0x33, 0x18, 0x7f, 0xe2, 0x87, 0x59, 0x15, 0x4d, 0x02, 0xad, 0x4d,
	0x41, 0x64, 0xd4, 0xa8, 0xbb, 0xd0, 0x3d, 0x43, 0x8e, 0x67, 0x49, 0x38, 0x34, 0xde, 0xa0, 0xa7,
	0x74, 0xa6, 0xd5, 0x6e, 0xff, 0x86, 0xed, 0xd9, 0x4d, 0xd1, 0x55, 0x24, 0x71, 0x38, 0x31, 0xa0,
	0x16, 0x81, 0x7a, 0xd3, 0xfd, 0xa1, 0x62, 0x5e, 0xc7, 0xa1, 0xbe, 0xc5, 0xac, 0x47, 0xb0, 0x71,
	0x5e, 0x0b, 0xdc, 0x41, 0xaf, 0xad, 0x2b, 0x5a, 0x6c, 0xc2, 0x40, 0x0d, 0xdd, 0x39, 0x12, 0x74,
	0xc8, 0xb5, 0xc5, 0x67, 0x08, 0x70, 0x07, 0xba, 0x73, 0x29, 0x0d, 0x7a, 0x5d, 0x72, 0x32, 0x40,
	0x8a, 0x36, 0xb0, 0xae, 0x43, 0x2b, 0xa7, 0x89, 0x3b, 0xe8, 0xad, 0xe8, 0xbb, 0x33, 0x23, 0xca,
	0xc0, 0xfe, 0xa9, 0x04, 0x9d, 0x9c, 0x9c, 0x44, 0xcb, 0x67, 0xd0, 0x12, 0x44, 0x52, 0x3d, 0x53,
	0x14, 0x3d, 0x5b, 0x03, 0xbb, 0x30, 0x64, 0xcf, 0xe1, 0xb3, 0x03, 0x62, 0xca, 0x6d, 0x75, 0xd6,
	0x25, 0x0b, 0x88, 0xbc, 0x4d, 0x87, 0xfe, 0xab, 0x4b, 0x26, 0x66, 0xfe, 0xa7, 0xd8, 0x33, 0x07,
	0xaa, 0xe9, 0xe4, 0xb2, 0x75, 0x1b, 0x3a, 0x43, 0x8e, 0xf8, 0x15, 0x5d, 0x21, 0x3d, 0x39, 0x16,
	0xe6, 0x60, 0xb5, 0xb5, 0xf2, 0x88, 0x74, 0xf6, 0x93, 0x99, 0x54, 0x69, 0x28, 0xec, 0xcf, 0x5d,
	0x04, 0xdb, 0x17, 0xe5, 0x38, 0x9d, 0xfd, 0xf6, 0xf7, 0x15, 0x58, 0x51, 0xfa, 0x37, 0x1c, 0x05,
	0xc6, 0x92, 0x72, 0x5d, 0x87, 0x9a, 0x4c, 0xdc, 0xe9, 0x51, 0xac, 0xca, 0xe4, 0x84, 0x05, 0xd6,
	0x26, 0xd4, 0x65, 0xe2, 0xce, 0x54, 0x51, 0x93, 0xc9, 0xb1, 0xaa, 0xe3, 0x06, 0xb4, 0x64, 0xe2,
	0x16, 0x4a, 0x01, 0x99, 0xbc, 0xca, 0x8a, 0x29, 0xcc, 0xec, 0xca, 0xc2, 0xcc, 0xbe, 0x05, 0xed,
	0x85, 0xb3, 0x56, 0x75, 0x5a, 0xb3, 0x87, 0x4c, 0x35, 0x84, 0xc5, 0x5e, 0x98, 0xd3, 0xac, 0x66,
	0x1a, 0xa2, 0x95, 0xf9, 0x0c, 0x18, 0xf2, 0x24, 0xd2, 0x49, 0xea, 0xe1, 0xdf, 0x50, 0x0a, 0x4a,
	0x93, 0x5a, 0x9a, 0x44, 0xd3, 0x44, 0x1b, 0xe4, 0xd0, 0x56, 0xca, 0x3c, 0xd5, 0x1d, 0x80, 0x80,
	0x89, 0x34, 0xf4, 0x26, 0x2e, 0x0b, 0xe8, 0xb4, 0x74, 0x9c, 0xa6, 0xd1, 0x1c, 0x06, 0x59, 0xa2,
	0x31, 0x86, 0x2e, 0x85, 0x00, 0x0a, 0xd1, 0x32, 0xba, 0x57, 0x2a, 0xc2, 0x26, 0xd4, 0x99, 0xc4,
	0x48, 0xc1, 0x5b, 0x7a, 0x18, 0x2a, 0xf1, 0x30, 0x50, 0xc9, 0x91, 0x81, 0x80, 0x6d, 0x9d, 0x9c,
	0x52, 0xbc, 0x32, 0xeb, 0x92, 0x31, 0xe5, 0xcc, 0xd7, 0xec, 0xee, 0x38, 0xe4, 0xfe, 0x46, 0x29,
	0x72, 0xb3, 0x4f, 0x8f, 0xae, 0xee, 0xd4, 0xfc, 0x4c, 0x29, 0xac, 0xbf, 0xc0, 0x4a, 0x96, 0xd6,
	0x29, 0xc3, 0x33, 0xb5, 0xf6, 0x0a, 0x2d, 0xd0, 0x31, 0xea, 0xb7, 0x0c, 0xcf, 0x0e, 0xe9, 0x92,
	0xd6, 0x74, 0xa0, 0x7b, 0x67, 0x55, 0x87, 0x21, 0xcd, 0xb1, 0xba, 0x7c, 0xde, 0xcf, 0x71, 0x81,
	0x18, 0x35, 0x98, 0x63, 0xd4, 0xf5, 0x73, 0x18, 0x35, 0xc3, 0x9c, 0xcb, 0xde, 0x13, 0x83, 0x9f,
	0x6b, 0xd0, 0xce, 0x19, 0xf8, 0x72, 0xc4, 0x2d, 0x07, 0xd6, 0x0f, 0x50, 0xe6, 0xaa, 0xa7, 0x13,
	0x73, 0xf1, 0x59, 0x5b, 0xc5, 0x55, 0xf2, 0x0b, 0xb1, 0x7f, 0x21, 0xa5, 0x95, 0x83, 0xbd, 0x64,
	0xbd, 0x85, 0xed, 0x67, 0x44, 0xab, 0xf3, 0x5f, 0x2e, 0xd6, 0xb5, 0x02, 0x7e, 0xf6, 0x1e, 0xef,
	0x6f, 0x16, 0x1f, 0x4e, 0xd9, 0x85, 0xbf, 0x64, 0x7d, 0x03, 0x3b, 0x07, 0x28, 0xcf, 0x0f, 0x4a,
	0x39, 0xf7, 0x0a, 0xd8, 0xfc, 0x9d, 0xd1, 0x7f, 0xf8, 0x87, 0x9f, 0x63, 0xa6, 0x86, 0xff, 0x42,
	0xe7, 0x00, 0xe5, 0xf4, 0x99, 0x68, 0xad, 0x15, 0x22, 0xd0, 0xd3, 0xb2, 0xbf, 0x53, 0x2c, 0x65,
	0xee, 0x5d, 0x69, 0x2f, 0x59, 0x0e, 0x6c, 0x9a, 0xfe, 0x9a, 0x2d, 0x9a, 0x76, 0x78, 0x7d, 0xb1,
	0xc3, 0x2a, 0xe4, 0x25, 0xdb, 0x6b, 0x72, 0xfb, 0x3f, 0xb4, 0x67, 0x9f, 0xdb, 0x56, 0x11, 0x51,
	0x78, 0xbe, 0xf7, 0x6f, 0x5c, 0x6a, 0xa7, 0x34, 0x8f, 0xe0, 0xca, 0xc2, 0x37, 0x83, 0x75, 0x7b,
	0x01, 0xb7, 0xf8, 0x55, 0x71, 0xd9, 0x7e, 0xbd, 0x87, 0xf5, 0x73, 0x3f, 0x9e, 0xac, 0xbf, 0x16,
	0x30, 0x17, 0x7d, 0x62, 0x5d, 0x16, 0xfc, 0x05, 0x74, 0xe7, 0xbf, 0x89, 0x2e, 0xea, 0xe7, 0xad,
	0xc5, 0xea, 0x0b, 0x5f, 0x52, 0xf6, 0x52, 0x7f, 0xe7, 0xd7, 0xdf, 0xee, 0x6c, 0xcd, 0x1e, 0x8c,
	0xdd, 0x59, 0xe1, 0xe9, 0xdf, 0xdf, 0xed, 0x8d, 0x92, 0xd0, 0x8b, 0x47, 0xbb, 0xff, 0x1c, 0x48,
	0xb9, 0xeb, 0x27, 0xd1, 0x1e, 0x7d, 0x20, 0xfa, 0x49, 0xb8, 0x27, 0x90, 0x9f, 0x32, 0x1f, 0xc5,
	0xfc, 0xf7, 0xe7, 0x87, 0x1a, 0x39, 0x3c, 0xfa, 0x3d, 0x00, 0x00, 0xff, 0xff, 0x00, 0xb8, 0xb8,
	0x68, 0xb4, 0x0e, 0x00, 0x00,
}
