// Code generated by protoc-gen-go. DO NOT EDIT.
// source: official-live-channel/official-live-channel-creator.proto

package official_live_channel // import "golang.52tt.com/protocol/services/official-live-channel"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelAdminRole int32

const (
	ChannelAdminRole_CHANNEL_INVALID_ROLE ChannelAdminRole = 0
	ChannelAdminRole_CHANNEL_OWNER        ChannelAdminRole = 1
	ChannelAdminRole_CHANNEL_ADMIN        ChannelAdminRole = 2
	ChannelAdminRole_CHANNEL_NORMAL       ChannelAdminRole = 3
	ChannelAdminRole_CHANNEL_ADMIN_SUPER  ChannelAdminRole = 4
)

var ChannelAdminRole_name = map[int32]string{
	0: "CHANNEL_INVALID_ROLE",
	1: "CHANNEL_OWNER",
	2: "CHANNEL_ADMIN",
	3: "CHANNEL_NORMAL",
	4: "CHANNEL_ADMIN_SUPER",
}
var ChannelAdminRole_value = map[string]int32{
	"CHANNEL_INVALID_ROLE": 0,
	"CHANNEL_OWNER":        1,
	"CHANNEL_ADMIN":        2,
	"CHANNEL_NORMAL":       3,
	"CHANNEL_ADMIN_SUPER":  4,
}

func (x ChannelAdminRole) String() string {
	return proto.EnumName(ChannelAdminRole_name, int32(x))
}
func (ChannelAdminRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{0}
}

type CreateChannelReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelReq) Reset()         { *m = CreateChannelReq{} }
func (m *CreateChannelReq) String() string { return proto.CompactTextString(m) }
func (*CreateChannelReq) ProtoMessage()    {}
func (*CreateChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{0}
}
func (m *CreateChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelReq.Unmarshal(m, b)
}
func (m *CreateChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelReq.Marshal(b, m, deterministic)
}
func (dst *CreateChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelReq.Merge(dst, src)
}
func (m *CreateChannelReq) XXX_Size() int {
	return xxx_messageInfo_CreateChannelReq.Size(m)
}
func (m *CreateChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelReq proto.InternalMessageInfo

func (m *CreateChannelReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type CreateChannelResp struct {
	OfficialChannelId    uint32   `protobuf:"varint,1,opt,name=official_channel_id,json=officialChannelId,proto3" json:"official_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateChannelResp) Reset()         { *m = CreateChannelResp{} }
func (m *CreateChannelResp) String() string { return proto.CompactTextString(m) }
func (*CreateChannelResp) ProtoMessage()    {}
func (*CreateChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{1}
}
func (m *CreateChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateChannelResp.Unmarshal(m, b)
}
func (m *CreateChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateChannelResp.Marshal(b, m, deterministic)
}
func (dst *CreateChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateChannelResp.Merge(dst, src)
}
func (m *CreateChannelResp) XXX_Size() int {
	return xxx_messageInfo_CreateChannelResp.Size(m)
}
func (m *CreateChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateChannelResp proto.InternalMessageInfo

func (m *CreateChannelResp) GetOfficialChannelId() uint32 {
	if m != nil {
		return m.OfficialChannelId
	}
	return 0
}

type ListChannelReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListChannelReq) Reset()         { *m = ListChannelReq{} }
func (m *ListChannelReq) String() string { return proto.CompactTextString(m) }
func (*ListChannelReq) ProtoMessage()    {}
func (*ListChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{2}
}
func (m *ListChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelReq.Unmarshal(m, b)
}
func (m *ListChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelReq.Marshal(b, m, deterministic)
}
func (dst *ListChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelReq.Merge(dst, src)
}
func (m *ListChannelReq) XXX_Size() int {
	return xxx_messageInfo_ListChannelReq.Size(m)
}
func (m *ListChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelReq proto.InternalMessageInfo

type ListChannelResp struct {
	OfficialChannelIds   []uint32 `protobuf:"varint,1,rep,packed,name=official_channel_ids,json=officialChannelIds,proto3" json:"official_channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListChannelResp) Reset()         { *m = ListChannelResp{} }
func (m *ListChannelResp) String() string { return proto.CompactTextString(m) }
func (*ListChannelResp) ProtoMessage()    {}
func (*ListChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{3}
}
func (m *ListChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListChannelResp.Unmarshal(m, b)
}
func (m *ListChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListChannelResp.Marshal(b, m, deterministic)
}
func (dst *ListChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListChannelResp.Merge(dst, src)
}
func (m *ListChannelResp) XXX_Size() int {
	return xxx_messageInfo_ListChannelResp.Size(m)
}
func (m *ListChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListChannelResp proto.InternalMessageInfo

func (m *ListChannelResp) GetOfficialChannelIds() []uint32 {
	if m != nil {
		return m.OfficialChannelIds
	}
	return nil
}

type ModifyChannelReq struct {
	OfficialChannelId    uint32   `protobuf:"varint,1,opt,name=official_channel_id,json=officialChannelId,proto3" json:"official_channel_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DescTitle            string   `protobuf:"bytes,3,opt,name=desc_title,json=descTitle,proto3" json:"desc_title,omitempty"`
	DescImage            string   `protobuf:"bytes,4,opt,name=desc_image,json=descImage,proto3" json:"desc_image,omitempty"`
	DescSmallImage       string   `protobuf:"bytes,5,opt,name=desc_small_image,json=descSmallImage,proto3" json:"desc_small_image,omitempty"`
	IdentityText         string   `protobuf:"bytes,6,opt,name=identity_text,json=identityText,proto3" json:"identity_text,omitempty"`
	FollowText           string   `protobuf:"bytes,7,opt,name=follow_text,json=followText,proto3" json:"follow_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelReq) Reset()         { *m = ModifyChannelReq{} }
func (m *ModifyChannelReq) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelReq) ProtoMessage()    {}
func (*ModifyChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{4}
}
func (m *ModifyChannelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelReq.Unmarshal(m, b)
}
func (m *ModifyChannelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelReq.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelReq.Merge(dst, src)
}
func (m *ModifyChannelReq) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelReq.Size(m)
}
func (m *ModifyChannelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelReq proto.InternalMessageInfo

func (m *ModifyChannelReq) GetOfficialChannelId() uint32 {
	if m != nil {
		return m.OfficialChannelId
	}
	return 0
}

func (m *ModifyChannelReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ModifyChannelReq) GetDescTitle() string {
	if m != nil {
		return m.DescTitle
	}
	return ""
}

func (m *ModifyChannelReq) GetDescImage() string {
	if m != nil {
		return m.DescImage
	}
	return ""
}

func (m *ModifyChannelReq) GetDescSmallImage() string {
	if m != nil {
		return m.DescSmallImage
	}
	return ""
}

func (m *ModifyChannelReq) GetIdentityText() string {
	if m != nil {
		return m.IdentityText
	}
	return ""
}

func (m *ModifyChannelReq) GetFollowText() string {
	if m != nil {
		return m.FollowText
	}
	return ""
}

type ModifyChannelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyChannelResp) Reset()         { *m = ModifyChannelResp{} }
func (m *ModifyChannelResp) String() string { return proto.CompactTextString(m) }
func (*ModifyChannelResp) ProtoMessage()    {}
func (*ModifyChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{5}
}
func (m *ModifyChannelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyChannelResp.Unmarshal(m, b)
}
func (m *ModifyChannelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyChannelResp.Marshal(b, m, deterministic)
}
func (dst *ModifyChannelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyChannelResp.Merge(dst, src)
}
func (m *ModifyChannelResp) XXX_Size() int {
	return xxx_messageInfo_ModifyChannelResp.Size(m)
}
func (m *ModifyChannelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyChannelResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyChannelResp proto.InternalMessageInfo

type GetChannelAdminReq struct {
	OfficialChannelId    uint32   `protobuf:"varint,1,opt,name=official_channel_id,json=officialChannelId,proto3" json:"official_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelAdminReq) Reset()         { *m = GetChannelAdminReq{} }
func (m *GetChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminReq) ProtoMessage()    {}
func (*GetChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{6}
}
func (m *GetChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAdminReq.Unmarshal(m, b)
}
func (m *GetChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAdminReq.Merge(dst, src)
}
func (m *GetChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelAdminReq.Size(m)
}
func (m *GetChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAdminReq proto.InternalMessageInfo

func (m *GetChannelAdminReq) GetOfficialChannelId() uint32 {
	if m != nil {
		return m.OfficialChannelId
	}
	return 0
}

type GetChannelAdminResp struct {
	Admins               map[uint32]ChannelAdminRole `protobuf:"bytes,1,rep,name=admins,proto3" json:"admins,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=official_live_channel.ChannelAdminRole"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetChannelAdminResp) Reset()         { *m = GetChannelAdminResp{} }
func (m *GetChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelAdminResp) ProtoMessage()    {}
func (*GetChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{7}
}
func (m *GetChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelAdminResp.Unmarshal(m, b)
}
func (m *GetChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelAdminResp.Merge(dst, src)
}
func (m *GetChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelAdminResp.Size(m)
}
func (m *GetChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelAdminResp proto.InternalMessageInfo

func (m *GetChannelAdminResp) GetAdmins() map[uint32]ChannelAdminRole {
	if m != nil {
		return m.Admins
	}
	return nil
}

type SetChannelAdminReq struct {
	OfficialChannelId    uint32           `protobuf:"varint,1,opt,name=official_channel_id,json=officialChannelId,proto3" json:"official_channel_id,omitempty"`
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 ChannelAdminRole `protobuf:"varint,3,opt,name=role,proto3,enum=official_live_channel.ChannelAdminRole" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetChannelAdminReq) Reset()         { *m = SetChannelAdminReq{} }
func (m *SetChannelAdminReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelAdminReq) ProtoMessage()    {}
func (*SetChannelAdminReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{8}
}
func (m *SetChannelAdminReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelAdminReq.Unmarshal(m, b)
}
func (m *SetChannelAdminReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelAdminReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelAdminReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelAdminReq.Merge(dst, src)
}
func (m *SetChannelAdminReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelAdminReq.Size(m)
}
func (m *SetChannelAdminReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelAdminReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelAdminReq proto.InternalMessageInfo

func (m *SetChannelAdminReq) GetOfficialChannelId() uint32 {
	if m != nil {
		return m.OfficialChannelId
	}
	return 0
}

func (m *SetChannelAdminReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelAdminReq) GetRole() ChannelAdminRole {
	if m != nil {
		return m.Role
	}
	return ChannelAdminRole_CHANNEL_INVALID_ROLE
}

type SetChannelAdminResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelAdminResp) Reset()         { *m = SetChannelAdminResp{} }
func (m *SetChannelAdminResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelAdminResp) ProtoMessage()    {}
func (*SetChannelAdminResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{9}
}
func (m *SetChannelAdminResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelAdminResp.Unmarshal(m, b)
}
func (m *SetChannelAdminResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelAdminResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelAdminResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelAdminResp.Merge(dst, src)
}
func (m *SetChannelAdminResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelAdminResp.Size(m)
}
func (m *SetChannelAdminResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelAdminResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelAdminResp proto.InternalMessageInfo

type GetOfficialChannelInfoReq struct {
	OfficialChannelId    uint32   `protobuf:"varint,1,opt,name=official_channel_id,json=officialChannelId,proto3" json:"official_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOfficialChannelInfoReq) Reset()         { *m = GetOfficialChannelInfoReq{} }
func (m *GetOfficialChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetOfficialChannelInfoReq) ProtoMessage()    {}
func (*GetOfficialChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{10}
}
func (m *GetOfficialChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialChannelInfoReq.Unmarshal(m, b)
}
func (m *GetOfficialChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetOfficialChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialChannelInfoReq.Merge(dst, src)
}
func (m *GetOfficialChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetOfficialChannelInfoReq.Size(m)
}
func (m *GetOfficialChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialChannelInfoReq proto.InternalMessageInfo

func (m *GetOfficialChannelInfoReq) GetOfficialChannelId() uint32 {
	if m != nil {
		return m.OfficialChannelId
	}
	return 0
}

type GetOfficialChannelInfoResp struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DescTitle            string   `protobuf:"bytes,2,opt,name=desc_title,json=descTitle,proto3" json:"desc_title,omitempty"`
	DescImage            string   `protobuf:"bytes,3,opt,name=desc_image,json=descImage,proto3" json:"desc_image,omitempty"`
	DescSmallImage       string   `protobuf:"bytes,4,opt,name=desc_small_image,json=descSmallImage,proto3" json:"desc_small_image,omitempty"`
	IdentityText         string   `protobuf:"bytes,5,opt,name=identity_text,json=identityText,proto3" json:"identity_text,omitempty"`
	FollowText           string   `protobuf:"bytes,6,opt,name=follow_text,json=followText,proto3" json:"follow_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOfficialChannelInfoResp) Reset()         { *m = GetOfficialChannelInfoResp{} }
func (m *GetOfficialChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetOfficialChannelInfoResp) ProtoMessage()    {}
func (*GetOfficialChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_official_live_channel_creator_0c7ba22745336909, []int{11}
}
func (m *GetOfficialChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfficialChannelInfoResp.Unmarshal(m, b)
}
func (m *GetOfficialChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfficialChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetOfficialChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfficialChannelInfoResp.Merge(dst, src)
}
func (m *GetOfficialChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetOfficialChannelInfoResp.Size(m)
}
func (m *GetOfficialChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfficialChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfficialChannelInfoResp proto.InternalMessageInfo

func (m *GetOfficialChannelInfoResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetOfficialChannelInfoResp) GetDescTitle() string {
	if m != nil {
		return m.DescTitle
	}
	return ""
}

func (m *GetOfficialChannelInfoResp) GetDescImage() string {
	if m != nil {
		return m.DescImage
	}
	return ""
}

func (m *GetOfficialChannelInfoResp) GetDescSmallImage() string {
	if m != nil {
		return m.DescSmallImage
	}
	return ""
}

func (m *GetOfficialChannelInfoResp) GetIdentityText() string {
	if m != nil {
		return m.IdentityText
	}
	return ""
}

func (m *GetOfficialChannelInfoResp) GetFollowText() string {
	if m != nil {
		return m.FollowText
	}
	return ""
}

func init() {
	proto.RegisterType((*CreateChannelReq)(nil), "official_live_channel.CreateChannelReq")
	proto.RegisterType((*CreateChannelResp)(nil), "official_live_channel.CreateChannelResp")
	proto.RegisterType((*ListChannelReq)(nil), "official_live_channel.ListChannelReq")
	proto.RegisterType((*ListChannelResp)(nil), "official_live_channel.ListChannelResp")
	proto.RegisterType((*ModifyChannelReq)(nil), "official_live_channel.ModifyChannelReq")
	proto.RegisterType((*ModifyChannelResp)(nil), "official_live_channel.ModifyChannelResp")
	proto.RegisterType((*GetChannelAdminReq)(nil), "official_live_channel.GetChannelAdminReq")
	proto.RegisterType((*GetChannelAdminResp)(nil), "official_live_channel.GetChannelAdminResp")
	proto.RegisterMapType((map[uint32]ChannelAdminRole)(nil), "official_live_channel.GetChannelAdminResp.AdminsEntry")
	proto.RegisterType((*SetChannelAdminReq)(nil), "official_live_channel.SetChannelAdminReq")
	proto.RegisterType((*SetChannelAdminResp)(nil), "official_live_channel.SetChannelAdminResp")
	proto.RegisterType((*GetOfficialChannelInfoReq)(nil), "official_live_channel.GetOfficialChannelInfoReq")
	proto.RegisterType((*GetOfficialChannelInfoResp)(nil), "official_live_channel.GetOfficialChannelInfoResp")
	proto.RegisterEnum("official_live_channel.ChannelAdminRole", ChannelAdminRole_name, ChannelAdminRole_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OfficialLiveChannelCreatorClient is the client API for OfficialLiveChannelCreator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OfficialLiveChannelCreatorClient interface {
	// ----------------- 房间 ----------------------
	// 创建官频
	CreateChannel(ctx context.Context, in *CreateChannelReq, opts ...grpc.CallOption) (*CreateChannelResp, error)
	// 获取官频列表
	ListChannel(ctx context.Context, in *ListChannelReq, opts ...grpc.CallOption) (*ListChannelResp, error)
	// 修改房间信息
	ModifyChannel(ctx context.Context, in *ModifyChannelReq, opts ...grpc.CallOption) (*ModifyChannelResp, error)
	// 获取房间信息
	GetOfficialChannelInfo(ctx context.Context, in *GetOfficialChannelInfoReq, opts ...grpc.CallOption) (*GetOfficialChannelInfoResp, error)
	// 获取官频管理员列表
	GetChannelAdmin(ctx context.Context, in *GetChannelAdminReq, opts ...grpc.CallOption) (*GetChannelAdminResp, error)
	// 设置官频管理员
	SetChannelAdmin(ctx context.Context, in *SetChannelAdminReq, opts ...grpc.CallOption) (*SetChannelAdminResp, error)
}

type officialLiveChannelCreatorClient struct {
	cc *grpc.ClientConn
}

func NewOfficialLiveChannelCreatorClient(cc *grpc.ClientConn) OfficialLiveChannelCreatorClient {
	return &officialLiveChannelCreatorClient{cc}
}

func (c *officialLiveChannelCreatorClient) CreateChannel(ctx context.Context, in *CreateChannelReq, opts ...grpc.CallOption) (*CreateChannelResp, error) {
	out := new(CreateChannelResp)
	err := c.cc.Invoke(ctx, "/official_live_channel.OfficialLiveChannelCreator/CreateChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialLiveChannelCreatorClient) ListChannel(ctx context.Context, in *ListChannelReq, opts ...grpc.CallOption) (*ListChannelResp, error) {
	out := new(ListChannelResp)
	err := c.cc.Invoke(ctx, "/official_live_channel.OfficialLiveChannelCreator/ListChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialLiveChannelCreatorClient) ModifyChannel(ctx context.Context, in *ModifyChannelReq, opts ...grpc.CallOption) (*ModifyChannelResp, error) {
	out := new(ModifyChannelResp)
	err := c.cc.Invoke(ctx, "/official_live_channel.OfficialLiveChannelCreator/ModifyChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialLiveChannelCreatorClient) GetOfficialChannelInfo(ctx context.Context, in *GetOfficialChannelInfoReq, opts ...grpc.CallOption) (*GetOfficialChannelInfoResp, error) {
	out := new(GetOfficialChannelInfoResp)
	err := c.cc.Invoke(ctx, "/official_live_channel.OfficialLiveChannelCreator/GetOfficialChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialLiveChannelCreatorClient) GetChannelAdmin(ctx context.Context, in *GetChannelAdminReq, opts ...grpc.CallOption) (*GetChannelAdminResp, error) {
	out := new(GetChannelAdminResp)
	err := c.cc.Invoke(ctx, "/official_live_channel.OfficialLiveChannelCreator/GetChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *officialLiveChannelCreatorClient) SetChannelAdmin(ctx context.Context, in *SetChannelAdminReq, opts ...grpc.CallOption) (*SetChannelAdminResp, error) {
	out := new(SetChannelAdminResp)
	err := c.cc.Invoke(ctx, "/official_live_channel.OfficialLiveChannelCreator/SetChannelAdmin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OfficialLiveChannelCreatorServer is the server API for OfficialLiveChannelCreator service.
type OfficialLiveChannelCreatorServer interface {
	// ----------------- 房间 ----------------------
	// 创建官频
	CreateChannel(context.Context, *CreateChannelReq) (*CreateChannelResp, error)
	// 获取官频列表
	ListChannel(context.Context, *ListChannelReq) (*ListChannelResp, error)
	// 修改房间信息
	ModifyChannel(context.Context, *ModifyChannelReq) (*ModifyChannelResp, error)
	// 获取房间信息
	GetOfficialChannelInfo(context.Context, *GetOfficialChannelInfoReq) (*GetOfficialChannelInfoResp, error)
	// 获取官频管理员列表
	GetChannelAdmin(context.Context, *GetChannelAdminReq) (*GetChannelAdminResp, error)
	// 设置官频管理员
	SetChannelAdmin(context.Context, *SetChannelAdminReq) (*SetChannelAdminResp, error)
}

func RegisterOfficialLiveChannelCreatorServer(s *grpc.Server, srv OfficialLiveChannelCreatorServer) {
	s.RegisterService(&_OfficialLiveChannelCreator_serviceDesc, srv)
}

func _OfficialLiveChannelCreator_CreateChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialLiveChannelCreatorServer).CreateChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/official_live_channel.OfficialLiveChannelCreator/CreateChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialLiveChannelCreatorServer).CreateChannel(ctx, req.(*CreateChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialLiveChannelCreator_ListChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialLiveChannelCreatorServer).ListChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/official_live_channel.OfficialLiveChannelCreator/ListChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialLiveChannelCreatorServer).ListChannel(ctx, req.(*ListChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialLiveChannelCreator_ModifyChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialLiveChannelCreatorServer).ModifyChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/official_live_channel.OfficialLiveChannelCreator/ModifyChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialLiveChannelCreatorServer).ModifyChannel(ctx, req.(*ModifyChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialLiveChannelCreator_GetOfficialChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfficialChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialLiveChannelCreatorServer).GetOfficialChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/official_live_channel.OfficialLiveChannelCreator/GetOfficialChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialLiveChannelCreatorServer).GetOfficialChannelInfo(ctx, req.(*GetOfficialChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialLiveChannelCreator_GetChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialLiveChannelCreatorServer).GetChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/official_live_channel.OfficialLiveChannelCreator/GetChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialLiveChannelCreatorServer).GetChannelAdmin(ctx, req.(*GetChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfficialLiveChannelCreator_SetChannelAdmin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelAdminReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfficialLiveChannelCreatorServer).SetChannelAdmin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/official_live_channel.OfficialLiveChannelCreator/SetChannelAdmin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfficialLiveChannelCreatorServer).SetChannelAdmin(ctx, req.(*SetChannelAdminReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _OfficialLiveChannelCreator_serviceDesc = grpc.ServiceDesc{
	ServiceName: "official_live_channel.OfficialLiveChannelCreator",
	HandlerType: (*OfficialLiveChannelCreatorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateChannel",
			Handler:    _OfficialLiveChannelCreator_CreateChannel_Handler,
		},
		{
			MethodName: "ListChannel",
			Handler:    _OfficialLiveChannelCreator_ListChannel_Handler,
		},
		{
			MethodName: "ModifyChannel",
			Handler:    _OfficialLiveChannelCreator_ModifyChannel_Handler,
		},
		{
			MethodName: "GetOfficialChannelInfo",
			Handler:    _OfficialLiveChannelCreator_GetOfficialChannelInfo_Handler,
		},
		{
			MethodName: "GetChannelAdmin",
			Handler:    _OfficialLiveChannelCreator_GetChannelAdmin_Handler,
		},
		{
			MethodName: "SetChannelAdmin",
			Handler:    _OfficialLiveChannelCreator_SetChannelAdmin_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "official-live-channel/official-live-channel-creator.proto",
}

func init() {
	proto.RegisterFile("official-live-channel/official-live-channel-creator.proto", fileDescriptor_official_live_channel_creator_0c7ba22745336909)
}

var fileDescriptor_official_live_channel_creator_0c7ba22745336909 = []byte{
	// 705 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0x5b, 0x6e, 0xd3, 0x40,
	0x14, 0x8d, 0xf3, 0x42, 0xbd, 0x21, 0xa9, 0x33, 0x69, 0x21, 0x44, 0x42, 0x54, 0x46, 0x94, 0x50,
	0xa9, 0x69, 0x09, 0xe2, 0x51, 0x10, 0x1f, 0x21, 0x8d, 0x4a, 0x44, 0xea, 0x20, 0xa7, 0x80, 0xc4,
	0x07, 0x96, 0x6b, 0x4f, 0xca, 0x80, 0x63, 0xbb, 0x99, 0x69, 0x68, 0xbe, 0xfa, 0xc3, 0x0e, 0x58,
	0x01, 0xcb, 0x61, 0x13, 0xac, 0x05, 0x79, 0x6c, 0xb7, 0x89, 0x63, 0x83, 0x5b, 0xfe, 0xc6, 0xe7,
	0x9e, 0xfb, 0x9c, 0xb9, 0x47, 0x86, 0x1d, 0x7b, 0x38, 0x24, 0x3a, 0xd1, 0xcc, 0x4d, 0x93, 0x4c,
	0xf0, 0xa6, 0xfe, 0x59, 0xb3, 0x2c, 0x6c, 0x6e, 0x45, 0xa2, 0x9b, 0xfa, 0x18, 0x6b, 0xcc, 0x1e,
	0x37, 0x9c, 0xb1, 0xcd, 0x6c, 0xb4, 0x1a, 0x90, 0x54, 0x97, 0xa4, 0xfa, 0x24, 0x69, 0x1d, 0xc4,
	0xb6, 0xcb, 0xc3, 0x6d, 0x0f, 0x50, 0xf0, 0x31, 0x42, 0x90, 0xb5, 0xb4, 0x11, 0xae, 0x0a, 0x6b,
	0x42, 0x7d, 0x49, 0xe1, 0x67, 0xa9, 0x0d, 0xe5, 0x10, 0x8f, 0x3a, 0xa8, 0x01, 0x95, 0xf3, 0xa8,
	0x7e, 0x40, 0x95, 0x18, 0xdc, 0xaf, 0xa8, 0x94, 0x03, 0x93, 0xef, 0xd1, 0x35, 0x24, 0x11, 0x4a,
	0x3d, 0x42, 0xd9, 0x45, 0x2a, 0xa9, 0x0d, 0xcb, 0x73, 0x08, 0x75, 0xd0, 0x36, 0xac, 0x44, 0x04,
	0xa5, 0x55, 0x61, 0x2d, 0x53, 0x2f, 0x2a, 0x68, 0x21, 0x2a, 0x95, 0xbe, 0xa7, 0x41, 0xdc, 0xb7,
	0x0d, 0x32, 0x9c, 0xce, 0x34, 0x71, 0xc9, 0xda, 0xce, 0x9b, 0x4e, 0x5f, 0x34, 0x8d, 0x6e, 0x03,
	0x18, 0x98, 0xea, 0x2a, 0x23, 0xcc, 0xc4, 0xd5, 0x0c, 0xb7, 0x2c, 0xb9, 0xc8, 0x81, 0x0b, 0x9c,
	0x9b, 0xc9, 0x48, 0x3b, 0xc2, 0xd5, 0xec, 0x85, 0xb9, 0xeb, 0x02, 0xa8, 0x0e, 0x22, 0x37, 0xd3,
	0x91, 0x66, 0x9a, 0x3e, 0x29, 0xc7, 0x49, 0x25, 0x17, 0x1f, 0xb8, 0xb0, 0xc7, 0xbc, 0x0b, 0x45,
	0x62, 0x60, 0x8b, 0x11, 0x36, 0x55, 0x19, 0x3e, 0x65, 0xd5, 0x3c, 0xa7, 0x5d, 0x0f, 0xc0, 0x03,
	0x7c, 0xca, 0xd0, 0x1d, 0x28, 0x0c, 0x6d, 0xd3, 0xb4, 0xbf, 0x79, 0x94, 0x6b, 0x9c, 0x02, 0x1e,
	0xe4, 0x12, 0xa4, 0x0a, 0x94, 0x43, 0x53, 0xa0, 0x8e, 0xb4, 0x0b, 0x68, 0x0f, 0x07, 0xf3, 0x6d,
	0x19, 0x23, 0x62, 0x5d, 0x61, 0x38, 0xd2, 0x2f, 0x01, 0x2a, 0x0b, 0x61, 0xa8, 0x83, 0x64, 0xc8,
	0x6b, 0xee, 0x87, 0x77, 0x3b, 0x85, 0xe6, 0x93, 0x46, 0xe4, 0x2b, 0x6b, 0x44, 0xf8, 0x36, 0xf8,
	0x89, 0x76, 0x2c, 0x36, 0x9e, 0x2a, 0x7e, 0x94, 0xda, 0x21, 0x14, 0x66, 0x60, 0x24, 0x42, 0xe6,
	0x2b, 0x9e, 0xfa, 0x65, 0xb9, 0x47, 0xf4, 0x12, 0x72, 0x13, 0xcd, 0x3c, 0xf1, 0xae, 0xa9, 0xd4,
	0xbc, 0x1f, 0x93, 0x6f, 0x2e, 0x99, 0x6d, 0x62, 0xc5, 0xf3, 0x7a, 0x9e, 0x7e, 0x26, 0x48, 0x3f,
	0x04, 0x40, 0x83, 0xff, 0x1e, 0x89, 0x5b, 0xdb, 0x09, 0x31, 0x78, 0x1d, 0x45, 0xc5, 0x3d, 0xa2,
	0x17, 0x90, 0x1d, 0xdb, 0xfe, 0x3b, 0xb9, 0x44, 0x69, 0xdc, 0x49, 0x5a, 0x85, 0xca, 0x60, 0x71,
	0x48, 0xd2, 0x1b, 0xb8, 0xb5, 0x87, 0x59, 0x3f, 0x94, 0xdd, 0x1a, 0xda, 0x57, 0xb9, 0xc5, 0xdf,
	0x02, 0xd4, 0xe2, 0xa2, 0x51, 0x27, 0x6a, 0xed, 0x43, 0x1b, 0x90, 0xfe, 0xfb, 0x06, 0x64, 0x92,
	0x6c, 0x40, 0x36, 0xd9, 0x06, 0xe4, 0xfe, 0xbd, 0x01, 0xf9, 0xf0, 0x06, 0x6c, 0x9c, 0x81, 0x18,
	0x1e, 0x2f, 0xaa, 0xc2, 0x4a, 0xfb, 0x75, 0x4b, 0x96, 0x3b, 0x3d, 0xb5, 0x2b, 0xbf, 0x6f, 0xf5,
	0xba, 0xbb, 0xaa, 0xd2, 0xef, 0x75, 0xc4, 0x14, 0x2a, 0x43, 0x31, 0xb0, 0xf4, 0x3f, 0xc8, 0x1d,
	0x45, 0x14, 0x66, 0xa1, 0xd6, 0xee, 0x7e, 0x57, 0x16, 0xd3, 0x08, 0x41, 0x29, 0x80, 0xe4, 0xbe,
	0xb2, 0xdf, 0xea, 0x89, 0x19, 0x74, 0x13, 0x2a, 0x73, 0x34, 0x75, 0xf0, 0xee, 0x6d, 0x47, 0x11,
	0xb3, 0xcd, 0x9f, 0x39, 0xa8, 0x05, 0xe3, 0xed, 0x91, 0x49, 0x20, 0x96, 0x6d, 0x4f, 0x89, 0x91,
	0x01, 0xc5, 0x39, 0x11, 0x45, 0xb1, 0x8f, 0x24, 0x24, 0xc9, 0xb5, 0x7a, 0x32, 0x22, 0x75, 0xa4,
	0x14, 0xfa, 0x04, 0x85, 0x19, 0x4d, 0x45, 0xf7, 0x62, 0x5c, 0xe7, 0x95, 0xb8, 0xb6, 0x9e, 0x84,
	0xc6, 0xe3, 0x1b, 0x50, 0x9c, 0xd3, 0x99, 0xd8, 0x2e, 0xc2, 0x9a, 0x1c, 0xdb, 0xc5, 0xa2, 0x6c,
	0xa5, 0xd0, 0x19, 0xdc, 0x88, 0x7e, 0xab, 0x68, 0x3b, 0x5e, 0x64, 0xa2, 0x17, 0xa5, 0xf6, 0xf0,
	0x92, 0x1e, 0xbc, 0x80, 0x2f, 0xb0, 0x1c, 0x92, 0x2d, 0xf4, 0x20, 0xa9, 0xbc, 0x1d, 0xd7, 0x36,
	0x92, 0x2b, 0xa1, 0x97, 0x6b, 0x90, 0x30, 0xd7, 0x20, 0x79, 0xae, 0x28, 0x41, 0x49, 0xbd, 0xda,
	0xf9, 0xf8, 0xf4, 0xc8, 0x36, 0x35, 0xeb, 0xa8, 0xf1, 0xb8, 0xc9, 0x58, 0x43, 0xb7, 0x47, 0x5b,
	0xfc, 0x0f, 0x41, 0xb7, 0xcd, 0x2d, 0x8a, 0xc7, 0x13, 0xa2, 0x63, 0x1a, 0xfd, 0x47, 0x71, 0x98,
	0xe7, 0xc4, 0x47, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x67, 0x3f, 0x9f, 0xbf, 0x87, 0x08, 0x00,
	0x00,
}
