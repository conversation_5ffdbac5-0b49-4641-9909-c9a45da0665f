// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/settlement-bill/settlement-bill.proto

package settlement_bill // import "golang.52tt.com/protocol/services/settlement-bill"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 结算单类型
type SettlementBillType int32

const (
	SettlementBillType_UnKnownBillType             SettlementBillType = 0
	SettlementBillType_GiftScore                   SettlementBillType = 1
	SettlementBillType_AwardScore                  SettlementBillType = 2
	SettlementBillType_MaskPKScore                 SettlementBillType = 3
	SettlementBillType_AmuseCommission             SettlementBillType = 4
	SettlementBillType_YuyinBaseCommission         SettlementBillType = 5
	SettlementBillType_MonthMiddle                 SettlementBillType = 6
	SettlementBillType_DeepCoop                    SettlementBillType = 7
	SettlementBillType_YuyinSubsidy                SettlementBillType = 8
	SettlementBillType_KnightScore                 SettlementBillType = 9
	SettlementBillType_AmuseExtra                  SettlementBillType = 10
	SettlementBillType_InteractGameCommission      SettlementBillType = 11
	SettlementBillType_InteractGameExtraCommission SettlementBillType = 12
	SettlementBillType_ESportScore                 SettlementBillType = 13
	SettlementBillType_ESportCommission            SettlementBillType = 14
)

var SettlementBillType_name = map[int32]string{
	0:  "UnKnownBillType",
	1:  "GiftScore",
	2:  "AwardScore",
	3:  "MaskPKScore",
	4:  "AmuseCommission",
	5:  "YuyinBaseCommission",
	6:  "MonthMiddle",
	7:  "DeepCoop",
	8:  "YuyinSubsidy",
	9:  "KnightScore",
	10: "AmuseExtra",
	11: "InteractGameCommission",
	12: "InteractGameExtraCommission",
	13: "ESportScore",
	14: "ESportCommission",
}
var SettlementBillType_value = map[string]int32{
	"UnKnownBillType":             0,
	"GiftScore":                   1,
	"AwardScore":                  2,
	"MaskPKScore":                 3,
	"AmuseCommission":             4,
	"YuyinBaseCommission":         5,
	"MonthMiddle":                 6,
	"DeepCoop":                    7,
	"YuyinSubsidy":                8,
	"KnightScore":                 9,
	"AmuseExtra":                  10,
	"InteractGameCommission":      11,
	"InteractGameExtraCommission": 12,
	"ESportScore":                 13,
	"ESportCommission":            14,
}

func (x SettlementBillType) String() string {
	return proto.EnumName(SettlementBillType_name, int32(x))
}
func (SettlementBillType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{0}
}

// 结算周期
type SettlementCycle int32

const (
	SettlementCycle_UnKnownSettlementCycle SettlementCycle = 0
	SettlementCycle_SettlementCycleWeekly  SettlementCycle = 1
	SettlementCycle_SettlementCycleMonthly SettlementCycle = 2
)

var SettlementCycle_name = map[int32]string{
	0: "UnKnownSettlementCycle",
	1: "SettlementCycleWeekly",
	2: "SettlementCycleMonthly",
}
var SettlementCycle_value = map[string]int32{
	"UnKnownSettlementCycle": 0,
	"SettlementCycleWeekly":  1,
	"SettlementCycleMonthly": 2,
}

func (x SettlementCycle) String() string {
	return proto.EnumName(SettlementCycle_name, int32(x))
}
func (SettlementCycle) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{1}
}

// 结算单状态
type SettlementBillStatus int32

const (
	SettlementBillStatus_Creating          SettlementBillStatus = 0
	SettlementBillStatus_WaitWithdraw      SettlementBillStatus = 1
	SettlementBillStatus_WaitReceipt       SettlementBillStatus = 3
	SettlementBillStatus_WaitAudit         SettlementBillStatus = 5
	SettlementBillStatus_WaitAdjustReceipt SettlementBillStatus = 6
	SettlementBillStatus_Finished          SettlementBillStatus = 7
)

var SettlementBillStatus_name = map[int32]string{
	0: "Creating",
	1: "WaitWithdraw",
	3: "WaitReceipt",
	5: "WaitAudit",
	6: "WaitAdjustReceipt",
	7: "Finished",
}
var SettlementBillStatus_value = map[string]int32{
	"Creating":          0,
	"WaitWithdraw":      1,
	"WaitReceipt":       3,
	"WaitAudit":         5,
	"WaitAdjustReceipt": 6,
	"Finished":          7,
}

func (x SettlementBillStatus) String() string {
	return proto.EnumName(SettlementBillStatus_name, int32(x))
}
func (SettlementBillStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{2}
}

// 灵犀审核状态
type OAAuditBillStatus int32

const (
	OAAuditBillStatus_OAAuditCreating OAAuditBillStatus = 0
	OAAuditBillStatus_OAFirstViewing  OAAuditBillStatus = 1
	OAAuditBillStatus_OAReviewing     OAAuditBillStatus = 2
	OAAuditBillStatus_OARefused       OAAuditBillStatus = 3
	OAAuditBillStatus_OAFinished      OAAuditBillStatus = 4
)

var OAAuditBillStatus_name = map[int32]string{
	0: "OAAuditCreating",
	1: "OAFirstViewing",
	2: "OAReviewing",
	3: "OARefused",
	4: "OAFinished",
}
var OAAuditBillStatus_value = map[string]int32{
	"OAAuditCreating": 0,
	"OAFirstViewing":  1,
	"OAReviewing":     2,
	"OARefused":       3,
	"OAFinished":      4,
}

func (x OAAuditBillStatus) String() string {
	return proto.EnumName(OAAuditBillStatus_name, int32(x))
}
func (OAAuditBillStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{3}
}

type PrivateWithdrawStatus int32

const (
	PrivateWithdrawStatus_PWSInvalid       PrivateWithdrawStatus = 0
	PrivateWithdrawStatus_PWSCreating      PrivateWithdrawStatus = 1
	PrivateWithdrawStatus_PWSSuccess       PrivateWithdrawStatus = 2
	PrivateWithdrawStatus_PWSFail          PrivateWithdrawStatus = 3
	PrivateWithdrawStatus_PWSLimitReqFail  PrivateWithdrawStatus = 4
	PrivateWithdrawStatus_PWSLimitOverFail PrivateWithdrawStatus = 5
	PrivateWithdrawStatus_PWSComReqFail    PrivateWithdrawStatus = 6
	PrivateWithdrawStatus_PWSComFail       PrivateWithdrawStatus = 7
	PrivateWithdrawStatus_PWSTBeanRiskFail PrivateWithdrawStatus = 8
)

var PrivateWithdrawStatus_name = map[int32]string{
	0: "PWSInvalid",
	1: "PWSCreating",
	2: "PWSSuccess",
	3: "PWSFail",
	4: "PWSLimitReqFail",
	5: "PWSLimitOverFail",
	6: "PWSComReqFail",
	7: "PWSComFail",
	8: "PWSTBeanRiskFail",
}
var PrivateWithdrawStatus_value = map[string]int32{
	"PWSInvalid":       0,
	"PWSCreating":      1,
	"PWSSuccess":       2,
	"PWSFail":          3,
	"PWSLimitReqFail":  4,
	"PWSLimitOverFail": 5,
	"PWSComReqFail":    6,
	"PWSComFail":       7,
	"PWSTBeanRiskFail": 8,
}

func (x PrivateWithdrawStatus) String() string {
	return proto.EnumName(PrivateWithdrawStatus_name, int32(x))
}
func (PrivateWithdrawStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{4}
}

// 发票提交状态，用于检查重复发票
type ReceiptStatus int32

const (
	ReceiptStatus_ReceiptWaitSubmit ReceiptStatus = 0
	ReceiptStatus_ReceiptSubmitted  ReceiptStatus = 1
)

var ReceiptStatus_name = map[int32]string{
	0: "ReceiptWaitSubmit",
	1: "ReceiptSubmitted",
}
var ReceiptStatus_value = map[string]int32{
	"ReceiptWaitSubmit": 0,
	"ReceiptSubmitted":  1,
}

func (x ReceiptStatus) String() string {
	return proto.EnumName(ReceiptStatus_name, int32(x))
}
func (ReceiptStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{5}
}

// 风控状态
type RiskStatus int32

const (
	RiskStatus_RiskNo    RiskStatus = 0
	RiskStatus_RiskTBean RiskStatus = 1
)

var RiskStatus_name = map[int32]string{
	0: "RiskNo",
	1: "RiskTBean",
}
var RiskStatus_value = map[string]int32{
	"RiskNo":    0,
	"RiskTBean": 1,
}

func (x RiskStatus) String() string {
	return proto.EnumName(RiskStatus_name, int32(x))
}
func (RiskStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{6}
}

// 额外收益类型
type ExtraIncomeType int32

const (
	ExtraIncomeType_UnknownExtraType    ExtraIncomeType = 0
	ExtraIncomeType_DeepCooperation     ExtraIncomeType = 1
	ExtraIncomeType_YuyinChannelSubsidy ExtraIncomeType = 2
	ExtraIncomeType_Deduction           ExtraIncomeType = 3
	ExtraIncomeType_AmuseExtraPrepaid   ExtraIncomeType = 4
)

var ExtraIncomeType_name = map[int32]string{
	0: "UnknownExtraType",
	1: "DeepCooperation",
	2: "YuyinChannelSubsidy",
	3: "Deduction",
	4: "AmuseExtraPrepaid",
}
var ExtraIncomeType_value = map[string]int32{
	"UnknownExtraType":    0,
	"DeepCooperation":     1,
	"YuyinChannelSubsidy": 2,
	"Deduction":           3,
	"AmuseExtraPrepaid":   4,
}

func (x ExtraIncomeType) String() string {
	return proto.EnumName(ExtraIncomeType_name, int32(x))
}
func (ExtraIncomeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{7}
}

// 额外收益类型详情查询类型
type ExtraIncomeDetailType int32

const (
	ExtraIncomeDetailType_UnknownExtraDetailType     ExtraIncomeDetailType = 0
	ExtraIncomeDetailType_DeepCooperationDetail      ExtraIncomeDetailType = 1
	ExtraIncomeDetailType_YuyinChannelSubsidyDetail  ExtraIncomeDetailType = 2
	ExtraIncomeDetailType_YuyinNewGuildSubsidyDetail ExtraIncomeDetailType = 3
	ExtraIncomeDetailType_AmuseExtraPrepaidDetail    ExtraIncomeDetailType = 4
)

var ExtraIncomeDetailType_name = map[int32]string{
	0: "UnknownExtraDetailType",
	1: "DeepCooperationDetail",
	2: "YuyinChannelSubsidyDetail",
	3: "YuyinNewGuildSubsidyDetail",
	4: "AmuseExtraPrepaidDetail",
}
var ExtraIncomeDetailType_value = map[string]int32{
	"UnknownExtraDetailType":     0,
	"DeepCooperationDetail":      1,
	"YuyinChannelSubsidyDetail":  2,
	"YuyinNewGuildSubsidyDetail": 3,
	"AmuseExtraPrepaidDetail":    4,
}

func (x ExtraIncomeDetailType) String() string {
	return proto.EnumName(ExtraIncomeDetailType_name, int32(x))
}
func (ExtraIncomeDetailType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{8}
}

type GiftScoreBill struct {
	GiftScore            uint64   `protobuf:"varint,1,opt,name=gift_score,json=giftScore,proto3" json:"gift_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftScoreBill) Reset()         { *m = GiftScoreBill{} }
func (m *GiftScoreBill) String() string { return proto.CompactTextString(m) }
func (*GiftScoreBill) ProtoMessage()    {}
func (*GiftScoreBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{0}
}
func (m *GiftScoreBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftScoreBill.Unmarshal(m, b)
}
func (m *GiftScoreBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftScoreBill.Marshal(b, m, deterministic)
}
func (dst *GiftScoreBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftScoreBill.Merge(dst, src)
}
func (m *GiftScoreBill) XXX_Size() int {
	return xxx_messageInfo_GiftScoreBill.Size(m)
}
func (m *GiftScoreBill) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftScoreBill.DiscardUnknown(m)
}

var xxx_messageInfo_GiftScoreBill proto.InternalMessageInfo

func (m *GiftScoreBill) GetGiftScore() uint64 {
	if m != nil {
		return m.GiftScore
	}
	return 0
}

type AwardScoreBill struct {
	AwardScore           uint64   `protobuf:"varint,1,opt,name=award_score,json=awardScore,proto3" json:"award_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardScoreBill) Reset()         { *m = AwardScoreBill{} }
func (m *AwardScoreBill) String() string { return proto.CompactTextString(m) }
func (*AwardScoreBill) ProtoMessage()    {}
func (*AwardScoreBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{1}
}
func (m *AwardScoreBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardScoreBill.Unmarshal(m, b)
}
func (m *AwardScoreBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardScoreBill.Marshal(b, m, deterministic)
}
func (dst *AwardScoreBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardScoreBill.Merge(dst, src)
}
func (m *AwardScoreBill) XXX_Size() int {
	return xxx_messageInfo_AwardScoreBill.Size(m)
}
func (m *AwardScoreBill) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardScoreBill.DiscardUnknown(m)
}

var xxx_messageInfo_AwardScoreBill proto.InternalMessageInfo

func (m *AwardScoreBill) GetAwardScore() uint64 {
	if m != nil {
		return m.AwardScore
	}
	return 0
}

type AmuseCommissionBill struct {
	AmuseCommission      uint64   `protobuf:"varint,1,opt,name=amuse_commission,json=amuseCommission,proto3" json:"amuse_commission,omitempty"`
	DeductMoney          uint64   `protobuf:"varint,2,opt,name=deduct_money,json=deductMoney,proto3" json:"deduct_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseCommissionBill) Reset()         { *m = AmuseCommissionBill{} }
func (m *AmuseCommissionBill) String() string { return proto.CompactTextString(m) }
func (*AmuseCommissionBill) ProtoMessage()    {}
func (*AmuseCommissionBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{2}
}
func (m *AmuseCommissionBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseCommissionBill.Unmarshal(m, b)
}
func (m *AmuseCommissionBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseCommissionBill.Marshal(b, m, deterministic)
}
func (dst *AmuseCommissionBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseCommissionBill.Merge(dst, src)
}
func (m *AmuseCommissionBill) XXX_Size() int {
	return xxx_messageInfo_AmuseCommissionBill.Size(m)
}
func (m *AmuseCommissionBill) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseCommissionBill.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseCommissionBill proto.InternalMessageInfo

func (m *AmuseCommissionBill) GetAmuseCommission() uint64 {
	if m != nil {
		return m.AmuseCommission
	}
	return 0
}

func (m *AmuseCommissionBill) GetDeductMoney() uint64 {
	if m != nil {
		return m.DeductMoney
	}
	return 0
}

type YuyinBaseCommissionBill struct {
	YuyinBaseCommission  uint64   `protobuf:"varint,1,opt,name=yuyin_base_commission,json=yuyinBaseCommission,proto3" json:"yuyin_base_commission,omitempty"`
	DeductMoney          uint64   `protobuf:"varint,2,opt,name=deduct_money,json=deductMoney,proto3" json:"deduct_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinBaseCommissionBill) Reset()         { *m = YuyinBaseCommissionBill{} }
func (m *YuyinBaseCommissionBill) String() string { return proto.CompactTextString(m) }
func (*YuyinBaseCommissionBill) ProtoMessage()    {}
func (*YuyinBaseCommissionBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{3}
}
func (m *YuyinBaseCommissionBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinBaseCommissionBill.Unmarshal(m, b)
}
func (m *YuyinBaseCommissionBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinBaseCommissionBill.Marshal(b, m, deterministic)
}
func (dst *YuyinBaseCommissionBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinBaseCommissionBill.Merge(dst, src)
}
func (m *YuyinBaseCommissionBill) XXX_Size() int {
	return xxx_messageInfo_YuyinBaseCommissionBill.Size(m)
}
func (m *YuyinBaseCommissionBill) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinBaseCommissionBill.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinBaseCommissionBill proto.InternalMessageInfo

func (m *YuyinBaseCommissionBill) GetYuyinBaseCommission() uint64 {
	if m != nil {
		return m.YuyinBaseCommission
	}
	return 0
}

func (m *YuyinBaseCommissionBill) GetDeductMoney() uint64 {
	if m != nil {
		return m.DeductMoney
	}
	return 0
}

// 对公蒙面pk积分结算单
type MaskPKScoreBill struct {
	MaskPkScore          uint64   `protobuf:"varint,1,opt,name=mask_pk_score,json=maskPkScore,proto3" json:"mask_pk_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MaskPKScoreBill) Reset()         { *m = MaskPKScoreBill{} }
func (m *MaskPKScoreBill) String() string { return proto.CompactTextString(m) }
func (*MaskPKScoreBill) ProtoMessage()    {}
func (*MaskPKScoreBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{4}
}
func (m *MaskPKScoreBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaskPKScoreBill.Unmarshal(m, b)
}
func (m *MaskPKScoreBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaskPKScoreBill.Marshal(b, m, deterministic)
}
func (dst *MaskPKScoreBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaskPKScoreBill.Merge(dst, src)
}
func (m *MaskPKScoreBill) XXX_Size() int {
	return xxx_messageInfo_MaskPKScoreBill.Size(m)
}
func (m *MaskPKScoreBill) XXX_DiscardUnknown() {
	xxx_messageInfo_MaskPKScoreBill.DiscardUnknown(m)
}

var xxx_messageInfo_MaskPKScoreBill proto.InternalMessageInfo

func (m *MaskPKScoreBill) GetMaskPkScore() uint64 {
	if m != nil {
		return m.MaskPkScore
	}
	return 0
}

// 月中会长佣金收益结算单
type MonthMiddleBill struct {
	YuyinAwardCommission uint64   `protobuf:"varint,1,opt,name=yuyin_award_commission,json=yuyinAwardCommission,proto3" json:"yuyin_award_commission,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MonthMiddleBill) Reset()         { *m = MonthMiddleBill{} }
func (m *MonthMiddleBill) String() string { return proto.CompactTextString(m) }
func (*MonthMiddleBill) ProtoMessage()    {}
func (*MonthMiddleBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{5}
}
func (m *MonthMiddleBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MonthMiddleBill.Unmarshal(m, b)
}
func (m *MonthMiddleBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MonthMiddleBill.Marshal(b, m, deterministic)
}
func (dst *MonthMiddleBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MonthMiddleBill.Merge(dst, src)
}
func (m *MonthMiddleBill) XXX_Size() int {
	return xxx_messageInfo_MonthMiddleBill.Size(m)
}
func (m *MonthMiddleBill) XXX_DiscardUnknown() {
	xxx_messageInfo_MonthMiddleBill.DiscardUnknown(m)
}

var xxx_messageInfo_MonthMiddleBill proto.InternalMessageInfo

func (m *MonthMiddleBill) GetYuyinAwardCommission() uint64 {
	if m != nil {
		return m.YuyinAwardCommission
	}
	return 0
}

// 多人互动深度合作结算单
type DeepCoopBill struct {
	DeepCoopMoney        uint64   `protobuf:"varint,1,opt,name=deep_coop_money,json=deepCoopMoney,proto3" json:"deep_coop_money,omitempty"`
	PrepaidMoney         uint64   `protobuf:"varint,2,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeepCoopBill) Reset()         { *m = DeepCoopBill{} }
func (m *DeepCoopBill) String() string { return proto.CompactTextString(m) }
func (*DeepCoopBill) ProtoMessage()    {}
func (*DeepCoopBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{6}
}
func (m *DeepCoopBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeepCoopBill.Unmarshal(m, b)
}
func (m *DeepCoopBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeepCoopBill.Marshal(b, m, deterministic)
}
func (dst *DeepCoopBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeepCoopBill.Merge(dst, src)
}
func (m *DeepCoopBill) XXX_Size() int {
	return xxx_messageInfo_DeepCoopBill.Size(m)
}
func (m *DeepCoopBill) XXX_DiscardUnknown() {
	xxx_messageInfo_DeepCoopBill.DiscardUnknown(m)
}

var xxx_messageInfo_DeepCoopBill proto.InternalMessageInfo

func (m *DeepCoopBill) GetDeepCoopMoney() uint64 {
	if m != nil {
		return m.DeepCoopMoney
	}
	return 0
}

func (m *DeepCoopBill) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

// 语音直播补贴结算单
type YuyinSubsidyBill struct {
	YuyinAnchorSubsidy   uint64   `protobuf:"varint,1,opt,name=yuyin_anchor_subsidy,json=yuyinAnchorSubsidy,proto3" json:"yuyin_anchor_subsidy,omitempty"`
	YuyinNewGuildSubsidy uint64   `protobuf:"varint,2,opt,name=yuyin_new_guild_subsidy,json=yuyinNewGuildSubsidy,proto3" json:"yuyin_new_guild_subsidy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinSubsidyBill) Reset()         { *m = YuyinSubsidyBill{} }
func (m *YuyinSubsidyBill) String() string { return proto.CompactTextString(m) }
func (*YuyinSubsidyBill) ProtoMessage()    {}
func (*YuyinSubsidyBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{7}
}
func (m *YuyinSubsidyBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinSubsidyBill.Unmarshal(m, b)
}
func (m *YuyinSubsidyBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinSubsidyBill.Marshal(b, m, deterministic)
}
func (dst *YuyinSubsidyBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinSubsidyBill.Merge(dst, src)
}
func (m *YuyinSubsidyBill) XXX_Size() int {
	return xxx_messageInfo_YuyinSubsidyBill.Size(m)
}
func (m *YuyinSubsidyBill) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinSubsidyBill.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinSubsidyBill proto.InternalMessageInfo

func (m *YuyinSubsidyBill) GetYuyinAnchorSubsidy() uint64 {
	if m != nil {
		return m.YuyinAnchorSubsidy
	}
	return 0
}

func (m *YuyinSubsidyBill) GetYuyinNewGuildSubsidy() uint64 {
	if m != nil {
		return m.YuyinNewGuildSubsidy
	}
	return 0
}

type GeneralBill struct {
	Money                uint64   `protobuf:"varint,1,opt,name=money,proto3" json:"money,omitempty"`
	DeductMoney          uint64   `protobuf:"varint,2,opt,name=deduct_money,json=deductMoney,proto3" json:"deduct_money,omitempty"`
	PrepaidMoney         uint64   `protobuf:"varint,3,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GeneralBill) Reset()         { *m = GeneralBill{} }
func (m *GeneralBill) String() string { return proto.CompactTextString(m) }
func (*GeneralBill) ProtoMessage()    {}
func (*GeneralBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{8}
}
func (m *GeneralBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GeneralBill.Unmarshal(m, b)
}
func (m *GeneralBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GeneralBill.Marshal(b, m, deterministic)
}
func (dst *GeneralBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GeneralBill.Merge(dst, src)
}
func (m *GeneralBill) XXX_Size() int {
	return xxx_messageInfo_GeneralBill.Size(m)
}
func (m *GeneralBill) XXX_DiscardUnknown() {
	xxx_messageInfo_GeneralBill.DiscardUnknown(m)
}

var xxx_messageInfo_GeneralBill proto.InternalMessageInfo

func (m *GeneralBill) GetMoney() uint64 {
	if m != nil {
		return m.Money
	}
	return 0
}

func (m *GeneralBill) GetDeductMoney() uint64 {
	if m != nil {
		return m.DeductMoney
	}
	return 0
}

func (m *GeneralBill) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CreateSettlementBillReq struct {
	// 运营录入特殊处理
	DeepCoopBill         *GeneralBill       `protobuf:"bytes,7,opt,name=deep_coop_bill,json=deepCoopBill,proto3" json:"deep_coop_bill,omitempty"`
	YuyinSubsidy         *YuyinSubsidyBill  `protobuf:"bytes,8,opt,name=yuyin_subsidy,json=yuyinSubsidy,proto3" json:"yuyin_subsidy,omitempty"`
	BillType             SettlementBillType `protobuf:"varint,20,opt,name=Bill_type,json=BillType,proto3,enum=settlement_bill.SettlementBillType" json:"Bill_type,omitempty"`
	Uid                  uint32             `protobuf:"varint,22,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32             `protobuf:"varint,23,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SettleStart          uint32             `protobuf:"varint,24,opt,name=settle_start,json=settleStart,proto3" json:"settle_start,omitempty"`
	SettleEnd            uint32             `protobuf:"varint,25,opt,name=settle_end,json=settleEnd,proto3" json:"settle_end,omitempty"`
	LastBalance          uint64             `protobuf:"varint,26,opt,name=last_balance,json=lastBalance,proto3" json:"last_balance,omitempty"`
	PrepaidMoney         uint64             `protobuf:"varint,27,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	SettleMoney          uint64             `protobuf:"varint,88,opt,name=settle_money,json=settleMoney,proto3" json:"settle_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CreateSettlementBillReq) Reset()         { *m = CreateSettlementBillReq{} }
func (m *CreateSettlementBillReq) String() string { return proto.CompactTextString(m) }
func (*CreateSettlementBillReq) ProtoMessage()    {}
func (*CreateSettlementBillReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{9}
}
func (m *CreateSettlementBillReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSettlementBillReq.Unmarshal(m, b)
}
func (m *CreateSettlementBillReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSettlementBillReq.Marshal(b, m, deterministic)
}
func (dst *CreateSettlementBillReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSettlementBillReq.Merge(dst, src)
}
func (m *CreateSettlementBillReq) XXX_Size() int {
	return xxx_messageInfo_CreateSettlementBillReq.Size(m)
}
func (m *CreateSettlementBillReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSettlementBillReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSettlementBillReq proto.InternalMessageInfo

func (m *CreateSettlementBillReq) GetDeepCoopBill() *GeneralBill {
	if m != nil {
		return m.DeepCoopBill
	}
	return nil
}

func (m *CreateSettlementBillReq) GetYuyinSubsidy() *YuyinSubsidyBill {
	if m != nil {
		return m.YuyinSubsidy
	}
	return nil
}

func (m *CreateSettlementBillReq) GetBillType() SettlementBillType {
	if m != nil {
		return m.BillType
	}
	return SettlementBillType_UnKnownBillType
}

func (m *CreateSettlementBillReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CreateSettlementBillReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateSettlementBillReq) GetSettleStart() uint32 {
	if m != nil {
		return m.SettleStart
	}
	return 0
}

func (m *CreateSettlementBillReq) GetSettleEnd() uint32 {
	if m != nil {
		return m.SettleEnd
	}
	return 0
}

func (m *CreateSettlementBillReq) GetLastBalance() uint64 {
	if m != nil {
		return m.LastBalance
	}
	return 0
}

func (m *CreateSettlementBillReq) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *CreateSettlementBillReq) GetSettleMoney() uint64 {
	if m != nil {
		return m.SettleMoney
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type CreateSettlementBillResp struct {
	BillId               string   `protobuf:"bytes,1,opt,name=Bill_id,json=BillId,proto3" json:"Bill_id,omitempty"`
	IsMerged             bool     `protobuf:"varint,2,opt,name=is_merged,json=isMerged,proto3" json:"is_merged,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateSettlementBillResp) Reset()         { *m = CreateSettlementBillResp{} }
func (m *CreateSettlementBillResp) String() string { return proto.CompactTextString(m) }
func (*CreateSettlementBillResp) ProtoMessage()    {}
func (*CreateSettlementBillResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{10}
}
func (m *CreateSettlementBillResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateSettlementBillResp.Unmarshal(m, b)
}
func (m *CreateSettlementBillResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateSettlementBillResp.Marshal(b, m, deterministic)
}
func (dst *CreateSettlementBillResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateSettlementBillResp.Merge(dst, src)
}
func (m *CreateSettlementBillResp) XXX_Size() int {
	return xxx_messageInfo_CreateSettlementBillResp.Size(m)
}
func (m *CreateSettlementBillResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateSettlementBillResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateSettlementBillResp proto.InternalMessageInfo

func (m *CreateSettlementBillResp) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *CreateSettlementBillResp) GetIsMerged() bool {
	if m != nil {
		return m.IsMerged
	}
	return false
}

// 结算单详情结构
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SettlementBillDetail struct {
	BillId               string               `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	Type                 SettlementBillType   `protobuf:"varint,2,opt,name=type,proto3,enum=settlement_bill.SettlementBillType" json:"type,omitempty"`
	Uid                  uint32               `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               SettlementBillStatus `protobuf:"varint,4,opt,name=status,proto3,enum=settlement_bill.SettlementBillStatus" json:"status,omitempty"`
	TaxRate              uint32               `protobuf:"varint,5,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	IncomeSum            uint64               `protobuf:"varint,6,opt,name=income_sum,json=incomeSum,proto3" json:"income_sum,omitempty"`
	ActualIncomePay      uint64               `protobuf:"varint,7,opt,name=actual_income_pay,json=actualIncomePay,proto3" json:"actual_income_pay,omitempty"`
	CreateTime           uint32               `protobuf:"varint,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	FinishedTime         uint32               `protobuf:"varint,9,opt,name=finished_time,json=finishedTime,proto3" json:"finished_time,omitempty"`
	FileId               string               `protobuf:"bytes,11,opt,name=fileId,proto3" json:"fileId,omitempty"`
	FileUrl              string               `protobuf:"bytes,12,opt,name=fileUrl,proto3" json:"fileUrl,omitempty"`
	SettleStart          uint32               `protobuf:"varint,14,opt,name=settle_start,json=settleStart,proto3" json:"settle_start,omitempty"`
	SettleEnd            uint32               `protobuf:"varint,15,opt,name=settle_end,json=settleEnd,proto3" json:"settle_end,omitempty"`
	SettleDate           uint32               `protobuf:"varint,16,opt,name=settle_date,json=settleDate,proto3" json:"settle_date,omitempty"`
	AllowWithdraw        bool                 `protobuf:"varint,17,opt,name=allow_withdraw,json=allowWithdraw,proto3" json:"allow_withdraw,omitempty"`
	GeneralBillData      *GeneralBill         `protobuf:"bytes,101,opt,name=general_bill_data,json=generalBillData,proto3" json:"general_bill_data,omitempty"`
	DeepCoopBill         *GeneralBill         `protobuf:"bytes,107,opt,name=deep_coop_bill,json=deepCoopBill,proto3" json:"deep_coop_bill,omitempty"`
	YuyinSubsidy         *YuyinSubsidyBill    `protobuf:"bytes,108,opt,name=yuyin_subsidy,json=yuyinSubsidy,proto3" json:"yuyin_subsidy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SettlementBillDetail) Reset()         { *m = SettlementBillDetail{} }
func (m *SettlementBillDetail) String() string { return proto.CompactTextString(m) }
func (*SettlementBillDetail) ProtoMessage()    {}
func (*SettlementBillDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{11}
}
func (m *SettlementBillDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettlementBillDetail.Unmarshal(m, b)
}
func (m *SettlementBillDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettlementBillDetail.Marshal(b, m, deterministic)
}
func (dst *SettlementBillDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettlementBillDetail.Merge(dst, src)
}
func (m *SettlementBillDetail) XXX_Size() int {
	return xxx_messageInfo_SettlementBillDetail.Size(m)
}
func (m *SettlementBillDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SettlementBillDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SettlementBillDetail proto.InternalMessageInfo

func (m *SettlementBillDetail) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *SettlementBillDetail) GetType() SettlementBillType {
	if m != nil {
		return m.Type
	}
	return SettlementBillType_UnKnownBillType
}

func (m *SettlementBillDetail) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SettlementBillDetail) GetStatus() SettlementBillStatus {
	if m != nil {
		return m.Status
	}
	return SettlementBillStatus_Creating
}

func (m *SettlementBillDetail) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *SettlementBillDetail) GetIncomeSum() uint64 {
	if m != nil {
		return m.IncomeSum
	}
	return 0
}

func (m *SettlementBillDetail) GetActualIncomePay() uint64 {
	if m != nil {
		return m.ActualIncomePay
	}
	return 0
}

func (m *SettlementBillDetail) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SettlementBillDetail) GetFinishedTime() uint32 {
	if m != nil {
		return m.FinishedTime
	}
	return 0
}

func (m *SettlementBillDetail) GetFileId() string {
	if m != nil {
		return m.FileId
	}
	return ""
}

func (m *SettlementBillDetail) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *SettlementBillDetail) GetSettleStart() uint32 {
	if m != nil {
		return m.SettleStart
	}
	return 0
}

func (m *SettlementBillDetail) GetSettleEnd() uint32 {
	if m != nil {
		return m.SettleEnd
	}
	return 0
}

func (m *SettlementBillDetail) GetSettleDate() uint32 {
	if m != nil {
		return m.SettleDate
	}
	return 0
}

func (m *SettlementBillDetail) GetAllowWithdraw() bool {
	if m != nil {
		return m.AllowWithdraw
	}
	return false
}

func (m *SettlementBillDetail) GetGeneralBillData() *GeneralBill {
	if m != nil {
		return m.GeneralBillData
	}
	return nil
}

func (m *SettlementBillDetail) GetDeepCoopBill() *GeneralBill {
	if m != nil {
		return m.DeepCoopBill
	}
	return nil
}

func (m *SettlementBillDetail) GetYuyinSubsidy() *YuyinSubsidyBill {
	if m != nil {
		return m.YuyinSubsidy
	}
	return nil
}

type GetGuildTaxRateReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTaxRateReq) Reset()         { *m = GetGuildTaxRateReq{} }
func (m *GetGuildTaxRateReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildTaxRateReq) ProtoMessage()    {}
func (*GetGuildTaxRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{12}
}
func (m *GetGuildTaxRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTaxRateReq.Unmarshal(m, b)
}
func (m *GetGuildTaxRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTaxRateReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildTaxRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTaxRateReq.Merge(dst, src)
}
func (m *GetGuildTaxRateReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildTaxRateReq.Size(m)
}
func (m *GetGuildTaxRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTaxRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTaxRateReq proto.InternalMessageInfo

func (m *GetGuildTaxRateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetGuildTaxRateResp struct {
	TaxRate              uint32   `protobuf:"varint,1,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTaxRateResp) Reset()         { *m = GetGuildTaxRateResp{} }
func (m *GetGuildTaxRateResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildTaxRateResp) ProtoMessage()    {}
func (*GetGuildTaxRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{13}
}
func (m *GetGuildTaxRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTaxRateResp.Unmarshal(m, b)
}
func (m *GetGuildTaxRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTaxRateResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildTaxRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTaxRateResp.Merge(dst, src)
}
func (m *GetGuildTaxRateResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildTaxRateResp.Size(m)
}
func (m *GetGuildTaxRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTaxRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTaxRateResp proto.InternalMessageInfo

func (m *GetGuildTaxRateResp) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *GetGuildTaxRateResp) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

type TaxRate struct {
	TaxRate   uint32 `protobuf:"varint,1,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	Uid       uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	StartTime uint32 `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// 仅列表展示
	CreateTime           uint32   `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operator             string   `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TaxRate) Reset()         { *m = TaxRate{} }
func (m *TaxRate) String() string { return proto.CompactTextString(m) }
func (*TaxRate) ProtoMessage()    {}
func (*TaxRate) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{14}
}
func (m *TaxRate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TaxRate.Unmarshal(m, b)
}
func (m *TaxRate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TaxRate.Marshal(b, m, deterministic)
}
func (dst *TaxRate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TaxRate.Merge(dst, src)
}
func (m *TaxRate) XXX_Size() int {
	return xxx_messageInfo_TaxRate.Size(m)
}
func (m *TaxRate) XXX_DiscardUnknown() {
	xxx_messageInfo_TaxRate.DiscardUnknown(m)
}

var xxx_messageInfo_TaxRate proto.InternalMessageInfo

func (m *TaxRate) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *TaxRate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TaxRate) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *TaxRate) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *TaxRate) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *TaxRate) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type RecordTaxRateReq struct {
	Operator             string     `protobuf:"bytes,1,opt,name=operator,proto3" json:"operator,omitempty"`
	TaxRateList          []*TaxRate `protobuf:"bytes,4,rep,name=tax_rate_list,json=taxRateList,proto3" json:"tax_rate_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RecordTaxRateReq) Reset()         { *m = RecordTaxRateReq{} }
func (m *RecordTaxRateReq) String() string { return proto.CompactTextString(m) }
func (*RecordTaxRateReq) ProtoMessage()    {}
func (*RecordTaxRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{15}
}
func (m *RecordTaxRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordTaxRateReq.Unmarshal(m, b)
}
func (m *RecordTaxRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordTaxRateReq.Marshal(b, m, deterministic)
}
func (dst *RecordTaxRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordTaxRateReq.Merge(dst, src)
}
func (m *RecordTaxRateReq) XXX_Size() int {
	return xxx_messageInfo_RecordTaxRateReq.Size(m)
}
func (m *RecordTaxRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordTaxRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordTaxRateReq proto.InternalMessageInfo

func (m *RecordTaxRateReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *RecordTaxRateReq) GetTaxRateList() []*TaxRate {
	if m != nil {
		return m.TaxRateList
	}
	return nil
}

type RecordTaxRateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordTaxRateResp) Reset()         { *m = RecordTaxRateResp{} }
func (m *RecordTaxRateResp) String() string { return proto.CompactTextString(m) }
func (*RecordTaxRateResp) ProtoMessage()    {}
func (*RecordTaxRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{16}
}
func (m *RecordTaxRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordTaxRateResp.Unmarshal(m, b)
}
func (m *RecordTaxRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordTaxRateResp.Marshal(b, m, deterministic)
}
func (dst *RecordTaxRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordTaxRateResp.Merge(dst, src)
}
func (m *RecordTaxRateResp) XXX_Size() int {
	return xxx_messageInfo_RecordTaxRateResp.Size(m)
}
func (m *RecordTaxRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordTaxRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordTaxRateResp proto.InternalMessageInfo

type SetTaxRateReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaxRate              uint32   `protobuf:"varint,2,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	StartTime            uint32   `protobuf:"varint,3,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTaxRateReq) Reset()         { *m = SetTaxRateReq{} }
func (m *SetTaxRateReq) String() string { return proto.CompactTextString(m) }
func (*SetTaxRateReq) ProtoMessage()    {}
func (*SetTaxRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{17}
}
func (m *SetTaxRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTaxRateReq.Unmarshal(m, b)
}
func (m *SetTaxRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTaxRateReq.Marshal(b, m, deterministic)
}
func (dst *SetTaxRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTaxRateReq.Merge(dst, src)
}
func (m *SetTaxRateReq) XXX_Size() int {
	return xxx_messageInfo_SetTaxRateReq.Size(m)
}
func (m *SetTaxRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTaxRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetTaxRateReq proto.InternalMessageInfo

func (m *SetTaxRateReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetTaxRateReq) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *SetTaxRateReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SetTaxRateReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type SetTaxRateResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetTaxRateResp) Reset()         { *m = SetTaxRateResp{} }
func (m *SetTaxRateResp) String() string { return proto.CompactTextString(m) }
func (*SetTaxRateResp) ProtoMessage()    {}
func (*SetTaxRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{18}
}
func (m *SetTaxRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetTaxRateResp.Unmarshal(m, b)
}
func (m *SetTaxRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetTaxRateResp.Marshal(b, m, deterministic)
}
func (dst *SetTaxRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetTaxRateResp.Merge(dst, src)
}
func (m *SetTaxRateResp) XXX_Size() int {
	return xxx_messageInfo_SetTaxRateResp.Size(m)
}
func (m *SetTaxRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetTaxRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetTaxRateResp proto.InternalMessageInfo

type AssociateReceiptReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReceiptIds           []string `protobuf:"bytes,2,rep,name=receipt_ids,json=receiptIds,proto3" json:"receipt_ids,omitempty"`
	SettlementBillIds    []string `protobuf:"bytes,3,rep,name=settlement_bill_ids,json=settlementBillIds,proto3" json:"settlement_bill_ids,omitempty"`
	ExpressOrderIds      []string `protobuf:"bytes,4,rep,name=express_order_ids,json=expressOrderIds,proto3" json:"express_order_ids,omitempty"`
	ReceiptBillId        string   `protobuf:"bytes,5,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssociateReceiptReq) Reset()         { *m = AssociateReceiptReq{} }
func (m *AssociateReceiptReq) String() string { return proto.CompactTextString(m) }
func (*AssociateReceiptReq) ProtoMessage()    {}
func (*AssociateReceiptReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{19}
}
func (m *AssociateReceiptReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssociateReceiptReq.Unmarshal(m, b)
}
func (m *AssociateReceiptReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssociateReceiptReq.Marshal(b, m, deterministic)
}
func (dst *AssociateReceiptReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssociateReceiptReq.Merge(dst, src)
}
func (m *AssociateReceiptReq) XXX_Size() int {
	return xxx_messageInfo_AssociateReceiptReq.Size(m)
}
func (m *AssociateReceiptReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AssociateReceiptReq.DiscardUnknown(m)
}

var xxx_messageInfo_AssociateReceiptReq proto.InternalMessageInfo

func (m *AssociateReceiptReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AssociateReceiptReq) GetReceiptIds() []string {
	if m != nil {
		return m.ReceiptIds
	}
	return nil
}

func (m *AssociateReceiptReq) GetSettlementBillIds() []string {
	if m != nil {
		return m.SettlementBillIds
	}
	return nil
}

func (m *AssociateReceiptReq) GetExpressOrderIds() []string {
	if m != nil {
		return m.ExpressOrderIds
	}
	return nil
}

func (m *AssociateReceiptReq) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

type AssociateReceiptResp struct {
	ReceiptBillId        string   `protobuf:"bytes,1,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AssociateReceiptResp) Reset()         { *m = AssociateReceiptResp{} }
func (m *AssociateReceiptResp) String() string { return proto.CompactTextString(m) }
func (*AssociateReceiptResp) ProtoMessage()    {}
func (*AssociateReceiptResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{20}
}
func (m *AssociateReceiptResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssociateReceiptResp.Unmarshal(m, b)
}
func (m *AssociateReceiptResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssociateReceiptResp.Marshal(b, m, deterministic)
}
func (dst *AssociateReceiptResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssociateReceiptResp.Merge(dst, src)
}
func (m *AssociateReceiptResp) XXX_Size() int {
	return xxx_messageInfo_AssociateReceiptResp.Size(m)
}
func (m *AssociateReceiptResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AssociateReceiptResp.DiscardUnknown(m)
}

var xxx_messageInfo_AssociateReceiptResp proto.InternalMessageInfo

func (m *AssociateReceiptResp) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

// 深度合作单个房间收益信息
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type DeepCooperationChannelIncome struct {
	FlowThisMonth        uint64   `protobuf:"varint,1,opt,name=flow_this_month,json=flowThisMonth,proto3" json:"flow_this_month,omitempty"`
	FlowLastMonth        uint64   `protobuf:"varint,2,opt,name=flow_last_month,json=flowLastMonth,proto3" json:"flow_last_month,omitempty"`
	GrowRate             int32    `protobuf:"varint,3,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	GuildName            string   `protobuf:"bytes,5,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	GuildDisplayId       uint32   `protobuf:"varint,6,opt,name=guild_display_id,json=guildDisplayId,proto3" json:"guild_display_id,omitempty"`
	ChannelType          string   `protobuf:"bytes,7,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	SettlementRate       uint32   `protobuf:"varint,8,opt,name=settlement_rate,json=settlementRate,proto3" json:"settlement_rate,omitempty"`
	SettlementMoney      uint64   `protobuf:"varint,9,opt,name=settlement_money,json=settlementMoney,proto3" json:"settlement_money,omitempty"`
	ID                   uint32   `protobuf:"varint,10,opt,name=ID,proto3" json:"ID,omitempty"`
	ChannelId            uint32   `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeepCooperationChannelIncome) Reset()         { *m = DeepCooperationChannelIncome{} }
func (m *DeepCooperationChannelIncome) String() string { return proto.CompactTextString(m) }
func (*DeepCooperationChannelIncome) ProtoMessage()    {}
func (*DeepCooperationChannelIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{21}
}
func (m *DeepCooperationChannelIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeepCooperationChannelIncome.Unmarshal(m, b)
}
func (m *DeepCooperationChannelIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeepCooperationChannelIncome.Marshal(b, m, deterministic)
}
func (dst *DeepCooperationChannelIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeepCooperationChannelIncome.Merge(dst, src)
}
func (m *DeepCooperationChannelIncome) XXX_Size() int {
	return xxx_messageInfo_DeepCooperationChannelIncome.Size(m)
}
func (m *DeepCooperationChannelIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_DeepCooperationChannelIncome.DiscardUnknown(m)
}

var xxx_messageInfo_DeepCooperationChannelIncome proto.InternalMessageInfo

func (m *DeepCooperationChannelIncome) GetFlowThisMonth() uint64 {
	if m != nil {
		return m.FlowThisMonth
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetFlowLastMonth() uint64 {
	if m != nil {
		return m.FlowLastMonth
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetGrowRate() int32 {
	if m != nil {
		return m.GrowRate
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *DeepCooperationChannelIncome) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *DeepCooperationChannelIncome) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetChannelType() string {
	if m != nil {
		return m.ChannelType
	}
	return ""
}

func (m *DeepCooperationChannelIncome) GetSettlementRate() uint32 {
	if m != nil {
		return m.SettlementRate
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetSettlementMoney() uint64 {
	if m != nil {
		return m.SettlementMoney
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetID() uint32 {
	if m != nil {
		return m.ID
	}
	return 0
}

func (m *DeepCooperationChannelIncome) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 深度合作收益信息
type DeepCooperationIncome struct {
	Uid                   uint32                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TotalFlowThisMonth    uint64                          `protobuf:"varint,2,opt,name=total_flow_this_month,json=totalFlowThisMonth,proto3" json:"total_flow_this_month,omitempty"`
	TotalFlowLastMonth    uint64                          `protobuf:"varint,3,opt,name=total_flow_last_month,json=totalFlowLastMonth,proto3" json:"total_flow_last_month,omitempty"`
	GrowRate              int32                           `protobuf:"varint,4,opt,name=grow_rate,json=growRate,proto3" json:"grow_rate,omitempty"`
	SettlementMoney       uint64                          `protobuf:"varint,5,opt,name=settlement_money,json=settlementMoney,proto3" json:"settlement_money,omitempty"`
	PrepaidMoney          uint64                          `protobuf:"varint,6,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	ActualSettlementMoney uint64                          `protobuf:"varint,7,opt,name=actual_settlement_money,json=actualSettlementMoney,proto3" json:"actual_settlement_money,omitempty"`
	Remark                string                          `protobuf:"bytes,8,opt,name=remark,proto3" json:"remark,omitempty"`
	Channels              []*DeepCooperationChannelIncome `protobuf:"bytes,9,rep,name=channels,proto3" json:"channels,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                        `json:"-"`
	XXX_unrecognized      []byte                          `json:"-"`
	XXX_sizecache         int32                           `json:"-"`
}

func (m *DeepCooperationIncome) Reset()         { *m = DeepCooperationIncome{} }
func (m *DeepCooperationIncome) String() string { return proto.CompactTextString(m) }
func (*DeepCooperationIncome) ProtoMessage()    {}
func (*DeepCooperationIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{22}
}
func (m *DeepCooperationIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeepCooperationIncome.Unmarshal(m, b)
}
func (m *DeepCooperationIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeepCooperationIncome.Marshal(b, m, deterministic)
}
func (dst *DeepCooperationIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeepCooperationIncome.Merge(dst, src)
}
func (m *DeepCooperationIncome) XXX_Size() int {
	return xxx_messageInfo_DeepCooperationIncome.Size(m)
}
func (m *DeepCooperationIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_DeepCooperationIncome.DiscardUnknown(m)
}

var xxx_messageInfo_DeepCooperationIncome proto.InternalMessageInfo

func (m *DeepCooperationIncome) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeepCooperationIncome) GetTotalFlowThisMonth() uint64 {
	if m != nil {
		return m.TotalFlowThisMonth
	}
	return 0
}

func (m *DeepCooperationIncome) GetTotalFlowLastMonth() uint64 {
	if m != nil {
		return m.TotalFlowLastMonth
	}
	return 0
}

func (m *DeepCooperationIncome) GetGrowRate() int32 {
	if m != nil {
		return m.GrowRate
	}
	return 0
}

func (m *DeepCooperationIncome) GetSettlementMoney() uint64 {
	if m != nil {
		return m.SettlementMoney
	}
	return 0
}

func (m *DeepCooperationIncome) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *DeepCooperationIncome) GetActualSettlementMoney() uint64 {
	if m != nil {
		return m.ActualSettlementMoney
	}
	return 0
}

func (m *DeepCooperationIncome) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DeepCooperationIncome) GetChannels() []*DeepCooperationChannelIncome {
	if m != nil {
		return m.Channels
	}
	return nil
}

// 单个主播补贴
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type AnchorSubsidyIncome struct {
	Ttid                 uint32   `protobuf:"varint,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Tag                  string   `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag,omitempty"`
	AnchorFlag           string   `protobuf:"bytes,4,opt,name=anchor_flag,json=anchorFlag,proto3" json:"anchor_flag,omitempty"`
	GuildName            string   `protobuf:"bytes,5,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	PlanDate             string   `protobuf:"bytes,6,opt,name=plan_date,json=planDate,proto3" json:"plan_date,omitempty"`
	GiftFlow             uint64   `protobuf:"varint,7,opt,name=gift_flow,json=giftFlow,proto3" json:"gift_flow,omitempty"`
	ValidLiveDays        uint32   `protobuf:"varint,8,opt,name=valid_live_days,json=validLiveDays,proto3" json:"valid_live_days,omitempty"`
	SubsidyMoney         uint64   `protobuf:"varint,9,opt,name=subsidy_money,json=subsidyMoney,proto3" json:"subsidy_money,omitempty"`
	ID                   uint32   `protobuf:"varint,10,opt,name=ID,proto3" json:"ID,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorSubsidyIncome) Reset()         { *m = AnchorSubsidyIncome{} }
func (m *AnchorSubsidyIncome) String() string { return proto.CompactTextString(m) }
func (*AnchorSubsidyIncome) ProtoMessage()    {}
func (*AnchorSubsidyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{23}
}
func (m *AnchorSubsidyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorSubsidyIncome.Unmarshal(m, b)
}
func (m *AnchorSubsidyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorSubsidyIncome.Marshal(b, m, deterministic)
}
func (dst *AnchorSubsidyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorSubsidyIncome.Merge(dst, src)
}
func (m *AnchorSubsidyIncome) XXX_Size() int {
	return xxx_messageInfo_AnchorSubsidyIncome.Size(m)
}
func (m *AnchorSubsidyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorSubsidyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorSubsidyIncome proto.InternalMessageInfo

func (m *AnchorSubsidyIncome) GetTtid() uint32 {
	if m != nil {
		return m.Ttid
	}
	return 0
}

func (m *AnchorSubsidyIncome) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *AnchorSubsidyIncome) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *AnchorSubsidyIncome) GetAnchorFlag() string {
	if m != nil {
		return m.AnchorFlag
	}
	return ""
}

func (m *AnchorSubsidyIncome) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *AnchorSubsidyIncome) GetPlanDate() string {
	if m != nil {
		return m.PlanDate
	}
	return ""
}

func (m *AnchorSubsidyIncome) GetGiftFlow() uint64 {
	if m != nil {
		return m.GiftFlow
	}
	return 0
}

func (m *AnchorSubsidyIncome) GetValidLiveDays() uint32 {
	if m != nil {
		return m.ValidLiveDays
	}
	return 0
}

func (m *AnchorSubsidyIncome) GetSubsidyMoney() uint64 {
	if m != nil {
		return m.SubsidyMoney
	}
	return 0
}

func (m *AnchorSubsidyIncome) GetID() uint32 {
	if m != nil {
		return m.ID
	}
	return 0
}

// 语音直播主播补贴
type YuyinAnchorSubsidyIncome struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SubsidySum           uint64                 `protobuf:"varint,2,opt,name=subsidy_sum,json=subsidySum,proto3" json:"subsidy_sum,omitempty"`
	Anchors              []*AnchorSubsidyIncome `protobuf:"bytes,3,rep,name=anchors,proto3" json:"anchors,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *YuyinAnchorSubsidyIncome) Reset()         { *m = YuyinAnchorSubsidyIncome{} }
func (m *YuyinAnchorSubsidyIncome) String() string { return proto.CompactTextString(m) }
func (*YuyinAnchorSubsidyIncome) ProtoMessage()    {}
func (*YuyinAnchorSubsidyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{24}
}
func (m *YuyinAnchorSubsidyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinAnchorSubsidyIncome.Unmarshal(m, b)
}
func (m *YuyinAnchorSubsidyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinAnchorSubsidyIncome.Marshal(b, m, deterministic)
}
func (dst *YuyinAnchorSubsidyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinAnchorSubsidyIncome.Merge(dst, src)
}
func (m *YuyinAnchorSubsidyIncome) XXX_Size() int {
	return xxx_messageInfo_YuyinAnchorSubsidyIncome.Size(m)
}
func (m *YuyinAnchorSubsidyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinAnchorSubsidyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinAnchorSubsidyIncome proto.InternalMessageInfo

func (m *YuyinAnchorSubsidyIncome) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *YuyinAnchorSubsidyIncome) GetSubsidySum() uint64 {
	if m != nil {
		return m.SubsidySum
	}
	return 0
}

func (m *YuyinAnchorSubsidyIncome) GetAnchors() []*AnchorSubsidyIncome {
	if m != nil {
		return m.Anchors
	}
	return nil
}

// 新公会补贴收益信息
type YuyinNewGuildSubsidyIncome struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildName            string   `protobuf:"bytes,2,opt,name=guild_name,json=guildName,proto3" json:"guild_name,omitempty"`
	JoinTime             string   `protobuf:"bytes,3,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	FlowThisMonth        uint64   `protobuf:"varint,4,opt,name=flow_this_month,json=flowThisMonth,proto3" json:"flow_this_month,omitempty"`
	SubsidyDate          string   `protobuf:"bytes,5,opt,name=subsidy_date,json=subsidyDate,proto3" json:"subsidy_date,omitempty"`
	SubsidyMoney         uint64   `protobuf:"varint,6,opt,name=subsidy_money,json=subsidyMoney,proto3" json:"subsidy_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinNewGuildSubsidyIncome) Reset()         { *m = YuyinNewGuildSubsidyIncome{} }
func (m *YuyinNewGuildSubsidyIncome) String() string { return proto.CompactTextString(m) }
func (*YuyinNewGuildSubsidyIncome) ProtoMessage()    {}
func (*YuyinNewGuildSubsidyIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{25}
}
func (m *YuyinNewGuildSubsidyIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinNewGuildSubsidyIncome.Unmarshal(m, b)
}
func (m *YuyinNewGuildSubsidyIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinNewGuildSubsidyIncome.Marshal(b, m, deterministic)
}
func (dst *YuyinNewGuildSubsidyIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinNewGuildSubsidyIncome.Merge(dst, src)
}
func (m *YuyinNewGuildSubsidyIncome) XXX_Size() int {
	return xxx_messageInfo_YuyinNewGuildSubsidyIncome.Size(m)
}
func (m *YuyinNewGuildSubsidyIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinNewGuildSubsidyIncome.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinNewGuildSubsidyIncome proto.InternalMessageInfo

func (m *YuyinNewGuildSubsidyIncome) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *YuyinNewGuildSubsidyIncome) GetGuildName() string {
	if m != nil {
		return m.GuildName
	}
	return ""
}

func (m *YuyinNewGuildSubsidyIncome) GetJoinTime() string {
	if m != nil {
		return m.JoinTime
	}
	return ""
}

func (m *YuyinNewGuildSubsidyIncome) GetFlowThisMonth() uint64 {
	if m != nil {
		return m.FlowThisMonth
	}
	return 0
}

func (m *YuyinNewGuildSubsidyIncome) GetSubsidyDate() string {
	if m != nil {
		return m.SubsidyDate
	}
	return ""
}

func (m *YuyinNewGuildSubsidyIncome) GetSubsidyMoney() uint64 {
	if m != nil {
		return m.SubsidyMoney
	}
	return 0
}

// 扣款信息
type DeductMoney struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	AmuseDeductMoney     uint64   `protobuf:"varint,2,opt,name=amuse_deduct_money,json=amuseDeductMoney,proto3" json:"amuse_deduct_money,omitempty"`
	AmuseRemark          string   `protobuf:"bytes,3,opt,name=amuse_remark,json=amuseRemark,proto3" json:"amuse_remark,omitempty"`
	YuyinDeductMoney     uint64   `protobuf:"varint,4,opt,name=yuyin_deduct_money,json=yuyinDeductMoney,proto3" json:"yuyin_deduct_money,omitempty"`
	YuyinRemark          string   `protobuf:"bytes,5,opt,name=yuyin_remark,json=yuyinRemark,proto3" json:"yuyin_remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductMoney) Reset()         { *m = DeductMoney{} }
func (m *DeductMoney) String() string { return proto.CompactTextString(m) }
func (*DeductMoney) ProtoMessage()    {}
func (*DeductMoney) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{26}
}
func (m *DeductMoney) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductMoney.Unmarshal(m, b)
}
func (m *DeductMoney) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductMoney.Marshal(b, m, deterministic)
}
func (dst *DeductMoney) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductMoney.Merge(dst, src)
}
func (m *DeductMoney) XXX_Size() int {
	return xxx_messageInfo_DeductMoney.Size(m)
}
func (m *DeductMoney) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductMoney.DiscardUnknown(m)
}

var xxx_messageInfo_DeductMoney proto.InternalMessageInfo

func (m *DeductMoney) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeductMoney) GetAmuseDeductMoney() uint64 {
	if m != nil {
		return m.AmuseDeductMoney
	}
	return 0
}

func (m *DeductMoney) GetAmuseRemark() string {
	if m != nil {
		return m.AmuseRemark
	}
	return ""
}

func (m *DeductMoney) GetYuyinDeductMoney() uint64 {
	if m != nil {
		return m.YuyinDeductMoney
	}
	return 0
}

func (m *DeductMoney) GetYuyinRemark() string {
	if m != nil {
		return m.YuyinRemark
	}
	return ""
}

// 多人互动额外奖励预付款
type AmuseExtraPrepaidIncome struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PrepaidMoney         uint64   `protobuf:"varint,2,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	Remark               string   `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseExtraPrepaidIncome) Reset()         { *m = AmuseExtraPrepaidIncome{} }
func (m *AmuseExtraPrepaidIncome) String() string { return proto.CompactTextString(m) }
func (*AmuseExtraPrepaidIncome) ProtoMessage()    {}
func (*AmuseExtraPrepaidIncome) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{27}
}
func (m *AmuseExtraPrepaidIncome) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseExtraPrepaidIncome.Unmarshal(m, b)
}
func (m *AmuseExtraPrepaidIncome) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseExtraPrepaidIncome.Marshal(b, m, deterministic)
}
func (dst *AmuseExtraPrepaidIncome) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseExtraPrepaidIncome.Merge(dst, src)
}
func (m *AmuseExtraPrepaidIncome) XXX_Size() int {
	return xxx_messageInfo_AmuseExtraPrepaidIncome.Size(m)
}
func (m *AmuseExtraPrepaidIncome) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseExtraPrepaidIncome.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseExtraPrepaidIncome proto.InternalMessageInfo

func (m *AmuseExtraPrepaidIncome) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AmuseExtraPrepaidIncome) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *AmuseExtraPrepaidIncome) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type RecordExtraIncomeReq struct {
	SettlementDate           uint32                        `protobuf:"varint,2,opt,name=settlement_date,json=settlementDate,proto3" json:"settlement_date,omitempty"`
	IncomeType               ExtraIncomeType               `protobuf:"varint,3,opt,name=income_type,json=incomeType,proto3,enum=settlement_bill.ExtraIncomeType" json:"income_type,omitempty"`
	Operator                 string                        `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	FileName                 string                        `protobuf:"bytes,5,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileMd5                  string                        `protobuf:"bytes,6,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	FileUrl                  string                        `protobuf:"bytes,7,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	DeepCooperationList      []*DeepCooperationIncome      `protobuf:"bytes,10,rep,name=deep_cooperation_list,json=deepCooperationList,proto3" json:"deep_cooperation_list,omitempty"`
	YuyinAnchorSubsidyList   []*YuyinAnchorSubsidyIncome   `protobuf:"bytes,11,rep,name=yuyin_anchor_subsidy_list,json=yuyinAnchorSubsidyList,proto3" json:"yuyin_anchor_subsidy_list,omitempty"`
	YuyinNewGuildSubsidyList []*YuyinNewGuildSubsidyIncome `protobuf:"bytes,12,rep,name=yuyin_new_guild_subsidy_list,json=yuyinNewGuildSubsidyList,proto3" json:"yuyin_new_guild_subsidy_list,omitempty"`
	DeductList               []*DeductMoney                `protobuf:"bytes,13,rep,name=deduct_list,json=deductList,proto3" json:"deduct_list,omitempty"`
	AmuseExtraPrepaid        []*AmuseExtraPrepaidIncome    `protobuf:"bytes,14,rep,name=amuse_extra_prepaid,json=amuseExtraPrepaid,proto3" json:"amuse_extra_prepaid,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}                      `json:"-"`
	XXX_unrecognized         []byte                        `json:"-"`
	XXX_sizecache            int32                         `json:"-"`
}

func (m *RecordExtraIncomeReq) Reset()         { *m = RecordExtraIncomeReq{} }
func (m *RecordExtraIncomeReq) String() string { return proto.CompactTextString(m) }
func (*RecordExtraIncomeReq) ProtoMessage()    {}
func (*RecordExtraIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{28}
}
func (m *RecordExtraIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordExtraIncomeReq.Unmarshal(m, b)
}
func (m *RecordExtraIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordExtraIncomeReq.Marshal(b, m, deterministic)
}
func (dst *RecordExtraIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordExtraIncomeReq.Merge(dst, src)
}
func (m *RecordExtraIncomeReq) XXX_Size() int {
	return xxx_messageInfo_RecordExtraIncomeReq.Size(m)
}
func (m *RecordExtraIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordExtraIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordExtraIncomeReq proto.InternalMessageInfo

func (m *RecordExtraIncomeReq) GetSettlementDate() uint32 {
	if m != nil {
		return m.SettlementDate
	}
	return 0
}

func (m *RecordExtraIncomeReq) GetIncomeType() ExtraIncomeType {
	if m != nil {
		return m.IncomeType
	}
	return ExtraIncomeType_UnknownExtraType
}

func (m *RecordExtraIncomeReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *RecordExtraIncomeReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *RecordExtraIncomeReq) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *RecordExtraIncomeReq) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *RecordExtraIncomeReq) GetDeepCooperationList() []*DeepCooperationIncome {
	if m != nil {
		return m.DeepCooperationList
	}
	return nil
}

func (m *RecordExtraIncomeReq) GetYuyinAnchorSubsidyList() []*YuyinAnchorSubsidyIncome {
	if m != nil {
		return m.YuyinAnchorSubsidyList
	}
	return nil
}

func (m *RecordExtraIncomeReq) GetYuyinNewGuildSubsidyList() []*YuyinNewGuildSubsidyIncome {
	if m != nil {
		return m.YuyinNewGuildSubsidyList
	}
	return nil
}

func (m *RecordExtraIncomeReq) GetDeductList() []*DeductMoney {
	if m != nil {
		return m.DeductList
	}
	return nil
}

func (m *RecordExtraIncomeReq) GetAmuseExtraPrepaid() []*AmuseExtraPrepaidIncome {
	if m != nil {
		return m.AmuseExtraPrepaid
	}
	return nil
}

type RecordExtraIncomeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordExtraIncomeResp) Reset()         { *m = RecordExtraIncomeResp{} }
func (m *RecordExtraIncomeResp) String() string { return proto.CompactTextString(m) }
func (*RecordExtraIncomeResp) ProtoMessage()    {}
func (*RecordExtraIncomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{29}
}
func (m *RecordExtraIncomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordExtraIncomeResp.Unmarshal(m, b)
}
func (m *RecordExtraIncomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordExtraIncomeResp.Marshal(b, m, deterministic)
}
func (dst *RecordExtraIncomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordExtraIncomeResp.Merge(dst, src)
}
func (m *RecordExtraIncomeResp) XXX_Size() int {
	return xxx_messageInfo_RecordExtraIncomeResp.Size(m)
}
func (m *RecordExtraIncomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordExtraIncomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordExtraIncomeResp proto.InternalMessageInfo

type GetTaxRateListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTaxRateListReq) Reset()         { *m = GetTaxRateListReq{} }
func (m *GetTaxRateListReq) String() string { return proto.CompactTextString(m) }
func (*GetTaxRateListReq) ProtoMessage()    {}
func (*GetTaxRateListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{30}
}
func (m *GetTaxRateListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTaxRateListReq.Unmarshal(m, b)
}
func (m *GetTaxRateListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTaxRateListReq.Marshal(b, m, deterministic)
}
func (dst *GetTaxRateListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTaxRateListReq.Merge(dst, src)
}
func (m *GetTaxRateListReq) XXX_Size() int {
	return xxx_messageInfo_GetTaxRateListReq.Size(m)
}
func (m *GetTaxRateListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTaxRateListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTaxRateListReq proto.InternalMessageInfo

func (m *GetTaxRateListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTaxRateListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTaxRateListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTaxRateListResp struct {
	List                 []*TaxRate `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTaxRateListResp) Reset()         { *m = GetTaxRateListResp{} }
func (m *GetTaxRateListResp) String() string { return proto.CompactTextString(m) }
func (*GetTaxRateListResp) ProtoMessage()    {}
func (*GetTaxRateListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{31}
}
func (m *GetTaxRateListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTaxRateListResp.Unmarshal(m, b)
}
func (m *GetTaxRateListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTaxRateListResp.Marshal(b, m, deterministic)
}
func (dst *GetTaxRateListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTaxRateListResp.Merge(dst, src)
}
func (m *GetTaxRateListResp) XXX_Size() int {
	return xxx_messageInfo_GetTaxRateListResp.Size(m)
}
func (m *GetTaxRateListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTaxRateListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTaxRateListResp proto.InternalMessageInfo

func (m *GetTaxRateListResp) GetList() []*TaxRate {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetTaxRateListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type BatchGetTaxRateReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetTaxRateReq) Reset()         { *m = BatchGetTaxRateReq{} }
func (m *BatchGetTaxRateReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetTaxRateReq) ProtoMessage()    {}
func (*BatchGetTaxRateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{32}
}
func (m *BatchGetTaxRateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTaxRateReq.Unmarshal(m, b)
}
func (m *BatchGetTaxRateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTaxRateReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetTaxRateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTaxRateReq.Merge(dst, src)
}
func (m *BatchGetTaxRateReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetTaxRateReq.Size(m)
}
func (m *BatchGetTaxRateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTaxRateReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTaxRateReq proto.InternalMessageInfo

func (m *BatchGetTaxRateReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetTaxRateResp struct {
	TaxRateMap           map[uint32]*TaxRate `protobuf:"bytes,1,rep,name=tax_rate_map,json=taxRateMap,proto3" json:"tax_rate_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *BatchGetTaxRateResp) Reset()         { *m = BatchGetTaxRateResp{} }
func (m *BatchGetTaxRateResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetTaxRateResp) ProtoMessage()    {}
func (*BatchGetTaxRateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{33}
}
func (m *BatchGetTaxRateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTaxRateResp.Unmarshal(m, b)
}
func (m *BatchGetTaxRateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTaxRateResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetTaxRateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTaxRateResp.Merge(dst, src)
}
func (m *BatchGetTaxRateResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetTaxRateResp.Size(m)
}
func (m *BatchGetTaxRateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTaxRateResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTaxRateResp proto.InternalMessageInfo

func (m *BatchGetTaxRateResp) GetTaxRateMap() map[uint32]*TaxRate {
	if m != nil {
		return m.TaxRateMap
	}
	return nil
}

type GetExtraIncomeRecordListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraIncomeRecordListReq) Reset()         { *m = GetExtraIncomeRecordListReq{} }
func (m *GetExtraIncomeRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeRecordListReq) ProtoMessage()    {}
func (*GetExtraIncomeRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{34}
}
func (m *GetExtraIncomeRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeRecordListReq.Unmarshal(m, b)
}
func (m *GetExtraIncomeRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeRecordListReq.Merge(dst, src)
}
func (m *GetExtraIncomeRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeRecordListReq.Size(m)
}
func (m *GetExtraIncomeRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeRecordListReq proto.InternalMessageInfo

func (m *GetExtraIncomeRecordListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetExtraIncomeRecordListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetExtraIncomeRecordListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ExtraIncomeRecordItem struct {
	ID                   uint32          `protobuf:"varint,1,opt,name=ID,proto3" json:"ID,omitempty"`
	Operator             string          `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator,omitempty"`
	CreateTime           uint32          `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	IncomeType           ExtraIncomeType `protobuf:"varint,4,opt,name=income_type,json=incomeType,proto3,enum=settlement_bill.ExtraIncomeType" json:"income_type,omitempty"`
	SettlementDate       uint32          `protobuf:"varint,5,opt,name=settlement_date,json=settlementDate,proto3" json:"settlement_date,omitempty"`
	FileName             string          `protobuf:"bytes,6,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileUrl              string          `protobuf:"bytes,7,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ExtraIncomeRecordItem) Reset()         { *m = ExtraIncomeRecordItem{} }
func (m *ExtraIncomeRecordItem) String() string { return proto.CompactTextString(m) }
func (*ExtraIncomeRecordItem) ProtoMessage()    {}
func (*ExtraIncomeRecordItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{35}
}
func (m *ExtraIncomeRecordItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExtraIncomeRecordItem.Unmarshal(m, b)
}
func (m *ExtraIncomeRecordItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExtraIncomeRecordItem.Marshal(b, m, deterministic)
}
func (dst *ExtraIncomeRecordItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExtraIncomeRecordItem.Merge(dst, src)
}
func (m *ExtraIncomeRecordItem) XXX_Size() int {
	return xxx_messageInfo_ExtraIncomeRecordItem.Size(m)
}
func (m *ExtraIncomeRecordItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ExtraIncomeRecordItem.DiscardUnknown(m)
}

var xxx_messageInfo_ExtraIncomeRecordItem proto.InternalMessageInfo

func (m *ExtraIncomeRecordItem) GetID() uint32 {
	if m != nil {
		return m.ID
	}
	return 0
}

func (m *ExtraIncomeRecordItem) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ExtraIncomeRecordItem) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ExtraIncomeRecordItem) GetIncomeType() ExtraIncomeType {
	if m != nil {
		return m.IncomeType
	}
	return ExtraIncomeType_UnknownExtraType
}

func (m *ExtraIncomeRecordItem) GetSettlementDate() uint32 {
	if m != nil {
		return m.SettlementDate
	}
	return 0
}

func (m *ExtraIncomeRecordItem) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *ExtraIncomeRecordItem) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

type GetExtraIncomeRecordListResp struct {
	List                 []*ExtraIncomeRecordItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetExtraIncomeRecordListResp) Reset()         { *m = GetExtraIncomeRecordListResp{} }
func (m *GetExtraIncomeRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeRecordListResp) ProtoMessage()    {}
func (*GetExtraIncomeRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{36}
}
func (m *GetExtraIncomeRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeRecordListResp.Unmarshal(m, b)
}
func (m *GetExtraIncomeRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeRecordListResp.Merge(dst, src)
}
func (m *GetExtraIncomeRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeRecordListResp.Size(m)
}
func (m *GetExtraIncomeRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeRecordListResp proto.InternalMessageInfo

func (m *GetExtraIncomeRecordListResp) GetList() []*ExtraIncomeRecordItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetExtraIncomeRecordListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetExtraIncomeDetailReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	SettlementDate       uint32   `protobuf:"varint,4,opt,name=settlement_date,json=settlementDate,proto3" json:"settlement_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraIncomeDetailReq) Reset()         { *m = GetExtraIncomeDetailReq{} }
func (m *GetExtraIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeDetailReq) ProtoMessage()    {}
func (*GetExtraIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{37}
}
func (m *GetExtraIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeDetailReq.Unmarshal(m, b)
}
func (m *GetExtraIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeDetailReq.Merge(dst, src)
}
func (m *GetExtraIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeDetailReq.Size(m)
}
func (m *GetExtraIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeDetailReq proto.InternalMessageInfo

func (m *GetExtraIncomeDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetExtraIncomeDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetExtraIncomeDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetExtraIncomeDetailReq) GetSettlementDate() uint32 {
	if m != nil {
		return m.SettlementDate
	}
	return 0
}

type GetExtraIncomeDetailDeepCoopResp struct {
	Total                uint32                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	DeepCooperation      *DeepCooperationIncome `protobuf:"bytes,6,opt,name=deep_cooperation,json=deepCooperation,proto3" json:"deep_cooperation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetExtraIncomeDetailDeepCoopResp) Reset()         { *m = GetExtraIncomeDetailDeepCoopResp{} }
func (m *GetExtraIncomeDetailDeepCoopResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeDetailDeepCoopResp) ProtoMessage()    {}
func (*GetExtraIncomeDetailDeepCoopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{38}
}
func (m *GetExtraIncomeDetailDeepCoopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeDetailDeepCoopResp.Unmarshal(m, b)
}
func (m *GetExtraIncomeDetailDeepCoopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeDetailDeepCoopResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeDetailDeepCoopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeDetailDeepCoopResp.Merge(dst, src)
}
func (m *GetExtraIncomeDetailDeepCoopResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeDetailDeepCoopResp.Size(m)
}
func (m *GetExtraIncomeDetailDeepCoopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeDetailDeepCoopResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeDetailDeepCoopResp proto.InternalMessageInfo

func (m *GetExtraIncomeDetailDeepCoopResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetExtraIncomeDetailDeepCoopResp) GetDeepCooperation() *DeepCooperationIncome {
	if m != nil {
		return m.DeepCooperation
	}
	return nil
}

type GetExtraIncomeDetailChannelSubsidyResp struct {
	Total                uint32                    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	YuyinAnchorSubsidy   *YuyinAnchorSubsidyIncome `protobuf:"bytes,7,opt,name=yuyin_anchor_subsidy,json=yuyinAnchorSubsidy,proto3" json:"yuyin_anchor_subsidy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetExtraIncomeDetailChannelSubsidyResp) Reset() {
	*m = GetExtraIncomeDetailChannelSubsidyResp{}
}
func (m *GetExtraIncomeDetailChannelSubsidyResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeDetailChannelSubsidyResp) ProtoMessage()    {}
func (*GetExtraIncomeDetailChannelSubsidyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{39}
}
func (m *GetExtraIncomeDetailChannelSubsidyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeDetailChannelSubsidyResp.Unmarshal(m, b)
}
func (m *GetExtraIncomeDetailChannelSubsidyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeDetailChannelSubsidyResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeDetailChannelSubsidyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeDetailChannelSubsidyResp.Merge(dst, src)
}
func (m *GetExtraIncomeDetailChannelSubsidyResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeDetailChannelSubsidyResp.Size(m)
}
func (m *GetExtraIncomeDetailChannelSubsidyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeDetailChannelSubsidyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeDetailChannelSubsidyResp proto.InternalMessageInfo

func (m *GetExtraIncomeDetailChannelSubsidyResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetExtraIncomeDetailChannelSubsidyResp) GetYuyinAnchorSubsidy() *YuyinAnchorSubsidyIncome {
	if m != nil {
		return m.YuyinAnchorSubsidy
	}
	return nil
}

type GetExtraIncomeDetailNewGuildSubsidyResp struct {
	Total                uint32                      `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	YuyinNewGuildSubsidy *YuyinNewGuildSubsidyIncome `protobuf:"bytes,8,opt,name=yuyin_new_guild_subsidy,json=yuyinNewGuildSubsidy,proto3" json:"yuyin_new_guild_subsidy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetExtraIncomeDetailNewGuildSubsidyResp) Reset() {
	*m = GetExtraIncomeDetailNewGuildSubsidyResp{}
}
func (m *GetExtraIncomeDetailNewGuildSubsidyResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeDetailNewGuildSubsidyResp) ProtoMessage()    {}
func (*GetExtraIncomeDetailNewGuildSubsidyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{40}
}
func (m *GetExtraIncomeDetailNewGuildSubsidyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeDetailNewGuildSubsidyResp.Unmarshal(m, b)
}
func (m *GetExtraIncomeDetailNewGuildSubsidyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeDetailNewGuildSubsidyResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeDetailNewGuildSubsidyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeDetailNewGuildSubsidyResp.Merge(dst, src)
}
func (m *GetExtraIncomeDetailNewGuildSubsidyResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeDetailNewGuildSubsidyResp.Size(m)
}
func (m *GetExtraIncomeDetailNewGuildSubsidyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeDetailNewGuildSubsidyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeDetailNewGuildSubsidyResp proto.InternalMessageInfo

func (m *GetExtraIncomeDetailNewGuildSubsidyResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GetExtraIncomeDetailNewGuildSubsidyResp) GetYuyinNewGuildSubsidy() *YuyinNewGuildSubsidyIncome {
	if m != nil {
		return m.YuyinNewGuildSubsidy
	}
	return nil
}

type GetSettlementBillReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BillId               string   `protobuf:"bytes,2,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSettlementBillReq) Reset()         { *m = GetSettlementBillReq{} }
func (m *GetSettlementBillReq) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillReq) ProtoMessage()    {}
func (*GetSettlementBillReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{41}
}
func (m *GetSettlementBillReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillReq.Unmarshal(m, b)
}
func (m *GetSettlementBillReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillReq.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillReq.Merge(dst, src)
}
func (m *GetSettlementBillReq) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillReq.Size(m)
}
func (m *GetSettlementBillReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillReq proto.InternalMessageInfo

func (m *GetSettlementBillReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSettlementBillReq) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

type GetSettlementBillResp struct {
	Bill                 *SettlementBillDetail `protobuf:"bytes,1,opt,name=bill,proto3" json:"bill,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetSettlementBillResp) Reset()         { *m = GetSettlementBillResp{} }
func (m *GetSettlementBillResp) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillResp) ProtoMessage()    {}
func (*GetSettlementBillResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{42}
}
func (m *GetSettlementBillResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillResp.Unmarshal(m, b)
}
func (m *GetSettlementBillResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillResp.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillResp.Merge(dst, src)
}
func (m *GetSettlementBillResp) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillResp.Size(m)
}
func (m *GetSettlementBillResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillResp proto.InternalMessageInfo

func (m *GetSettlementBillResp) GetBill() *SettlementBillDetail {
	if m != nil {
		return m.Bill
	}
	return nil
}

type GetSettlementBillWaitReceiptReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReceiptBillId        string   `protobuf:"bytes,2,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSettlementBillWaitReceiptReq) Reset()         { *m = GetSettlementBillWaitReceiptReq{} }
func (m *GetSettlementBillWaitReceiptReq) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillWaitReceiptReq) ProtoMessage()    {}
func (*GetSettlementBillWaitReceiptReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{43}
}
func (m *GetSettlementBillWaitReceiptReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillWaitReceiptReq.Unmarshal(m, b)
}
func (m *GetSettlementBillWaitReceiptReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillWaitReceiptReq.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillWaitReceiptReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillWaitReceiptReq.Merge(dst, src)
}
func (m *GetSettlementBillWaitReceiptReq) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillWaitReceiptReq.Size(m)
}
func (m *GetSettlementBillWaitReceiptReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillWaitReceiptReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillWaitReceiptReq proto.InternalMessageInfo

func (m *GetSettlementBillWaitReceiptReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSettlementBillWaitReceiptReq) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

type GetSettlementBillWaitReceiptResp struct {
	Bills                []*SettlementBillDetail `protobuf:"bytes,1,rep,name=bills,proto3" json:"bills,omitempty"`
	DisableVerify        bool                    `protobuf:"varint,2,opt,name=disable_verify,json=disableVerify,proto3" json:"disable_verify,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetSettlementBillWaitReceiptResp) Reset()         { *m = GetSettlementBillWaitReceiptResp{} }
func (m *GetSettlementBillWaitReceiptResp) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillWaitReceiptResp) ProtoMessage()    {}
func (*GetSettlementBillWaitReceiptResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{44}
}
func (m *GetSettlementBillWaitReceiptResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillWaitReceiptResp.Unmarshal(m, b)
}
func (m *GetSettlementBillWaitReceiptResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillWaitReceiptResp.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillWaitReceiptResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillWaitReceiptResp.Merge(dst, src)
}
func (m *GetSettlementBillWaitReceiptResp) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillWaitReceiptResp.Size(m)
}
func (m *GetSettlementBillWaitReceiptResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillWaitReceiptResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillWaitReceiptResp proto.InternalMessageInfo

func (m *GetSettlementBillWaitReceiptResp) GetBills() []*SettlementBillDetail {
	if m != nil {
		return m.Bills
	}
	return nil
}

func (m *GetSettlementBillWaitReceiptResp) GetDisableVerify() bool {
	if m != nil {
		return m.DisableVerify
	}
	return false
}

type GetPrepaidMoneyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepaidMoneyReq) Reset()         { *m = GetPrepaidMoneyReq{} }
func (m *GetPrepaidMoneyReq) String() string { return proto.CompactTextString(m) }
func (*GetPrepaidMoneyReq) ProtoMessage()    {}
func (*GetPrepaidMoneyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{45}
}
func (m *GetPrepaidMoneyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepaidMoneyReq.Unmarshal(m, b)
}
func (m *GetPrepaidMoneyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepaidMoneyReq.Marshal(b, m, deterministic)
}
func (dst *GetPrepaidMoneyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepaidMoneyReq.Merge(dst, src)
}
func (m *GetPrepaidMoneyReq) XXX_Size() int {
	return xxx_messageInfo_GetPrepaidMoneyReq.Size(m)
}
func (m *GetPrepaidMoneyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepaidMoneyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepaidMoneyReq proto.InternalMessageInfo

func (m *GetPrepaidMoneyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPrepaidMoneyResp struct {
	PrepaidMoney         uint64   `protobuf:"varint,1,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPrepaidMoneyResp) Reset()         { *m = GetPrepaidMoneyResp{} }
func (m *GetPrepaidMoneyResp) String() string { return proto.CompactTextString(m) }
func (*GetPrepaidMoneyResp) ProtoMessage()    {}
func (*GetPrepaidMoneyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{46}
}
func (m *GetPrepaidMoneyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPrepaidMoneyResp.Unmarshal(m, b)
}
func (m *GetPrepaidMoneyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPrepaidMoneyResp.Marshal(b, m, deterministic)
}
func (dst *GetPrepaidMoneyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPrepaidMoneyResp.Merge(dst, src)
}
func (m *GetPrepaidMoneyResp) XXX_Size() int {
	return xxx_messageInfo_GetPrepaidMoneyResp.Size(m)
}
func (m *GetPrepaidMoneyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPrepaidMoneyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPrepaidMoneyResp proto.InternalMessageInfo

func (m *GetPrepaidMoneyResp) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

type GetDeductMoneyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeductMoneyReq) Reset()         { *m = GetDeductMoneyReq{} }
func (m *GetDeductMoneyReq) String() string { return proto.CompactTextString(m) }
func (*GetDeductMoneyReq) ProtoMessage()    {}
func (*GetDeductMoneyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{47}
}
func (m *GetDeductMoneyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeductMoneyReq.Unmarshal(m, b)
}
func (m *GetDeductMoneyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeductMoneyReq.Marshal(b, m, deterministic)
}
func (dst *GetDeductMoneyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeductMoneyReq.Merge(dst, src)
}
func (m *GetDeductMoneyReq) XXX_Size() int {
	return xxx_messageInfo_GetDeductMoneyReq.Size(m)
}
func (m *GetDeductMoneyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeductMoneyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeductMoneyReq proto.InternalMessageInfo

func (m *GetDeductMoneyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetDeductMoneyResp struct {
	AmuseDeductMoney     uint64   `protobuf:"varint,1,opt,name=amuse_deduct_money,json=amuseDeductMoney,proto3" json:"amuse_deduct_money,omitempty"`
	YuyinDeductMoney     uint64   `protobuf:"varint,2,opt,name=yuyin_deduct_money,json=yuyinDeductMoney,proto3" json:"yuyin_deduct_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeductMoneyResp) Reset()         { *m = GetDeductMoneyResp{} }
func (m *GetDeductMoneyResp) String() string { return proto.CompactTextString(m) }
func (*GetDeductMoneyResp) ProtoMessage()    {}
func (*GetDeductMoneyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{48}
}
func (m *GetDeductMoneyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeductMoneyResp.Unmarshal(m, b)
}
func (m *GetDeductMoneyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeductMoneyResp.Marshal(b, m, deterministic)
}
func (dst *GetDeductMoneyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeductMoneyResp.Merge(dst, src)
}
func (m *GetDeductMoneyResp) XXX_Size() int {
	return xxx_messageInfo_GetDeductMoneyResp.Size(m)
}
func (m *GetDeductMoneyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeductMoneyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeductMoneyResp proto.InternalMessageInfo

func (m *GetDeductMoneyResp) GetAmuseDeductMoney() uint64 {
	if m != nil {
		return m.AmuseDeductMoney
	}
	return 0
}

func (m *GetDeductMoneyResp) GetYuyinDeductMoney() uint64 {
	if m != nil {
		return m.YuyinDeductMoney
	}
	return 0
}

type GetReceiptBillListReq struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               uint32              `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32              `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Status               []OAAuditBillStatus `protobuf:"varint,4,rep,packed,name=status,proto3,enum=settlement_bill.OAAuditBillStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetReceiptBillListReq) Reset()         { *m = GetReceiptBillListReq{} }
func (m *GetReceiptBillListReq) String() string { return proto.CompactTextString(m) }
func (*GetReceiptBillListReq) ProtoMessage()    {}
func (*GetReceiptBillListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{49}
}
func (m *GetReceiptBillListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptBillListReq.Unmarshal(m, b)
}
func (m *GetReceiptBillListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptBillListReq.Marshal(b, m, deterministic)
}
func (dst *GetReceiptBillListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptBillListReq.Merge(dst, src)
}
func (m *GetReceiptBillListReq) XXX_Size() int {
	return xxx_messageInfo_GetReceiptBillListReq.Size(m)
}
func (m *GetReceiptBillListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptBillListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptBillListReq proto.InternalMessageInfo

func (m *GetReceiptBillListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetReceiptBillListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetReceiptBillListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetReceiptBillListReq) GetStatus() []OAAuditBillStatus {
	if m != nil {
		return m.Status
	}
	return nil
}

type GetReceiptBillListResp struct {
	ReceiptBillList      []*GetReceiptBillListRespReceiptBill `protobuf:"bytes,1,rep,name=receipt_bill_list,json=receiptBillList,proto3" json:"receipt_bill_list,omitempty"`
	Total                uint32                               `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-"`
	XXX_unrecognized     []byte                               `json:"-"`
	XXX_sizecache        int32                                `json:"-"`
}

func (m *GetReceiptBillListResp) Reset()         { *m = GetReceiptBillListResp{} }
func (m *GetReceiptBillListResp) String() string { return proto.CompactTextString(m) }
func (*GetReceiptBillListResp) ProtoMessage()    {}
func (*GetReceiptBillListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{50}
}
func (m *GetReceiptBillListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptBillListResp.Unmarshal(m, b)
}
func (m *GetReceiptBillListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptBillListResp.Marshal(b, m, deterministic)
}
func (dst *GetReceiptBillListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptBillListResp.Merge(dst, src)
}
func (m *GetReceiptBillListResp) XXX_Size() int {
	return xxx_messageInfo_GetReceiptBillListResp.Size(m)
}
func (m *GetReceiptBillListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptBillListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptBillListResp proto.InternalMessageInfo

func (m *GetReceiptBillListResp) GetReceiptBillList() []*GetReceiptBillListRespReceiptBill {
	if m != nil {
		return m.ReceiptBillList
	}
	return nil
}

func (m *GetReceiptBillListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 单个TTFPXXX
// buf:lint:ignore MESSAGE_PASCAL_CASE
type GetReceiptBillListRespReceiptBill struct {
	ReceiptBillId        string   `protobuf:"bytes,1,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	SettlementBillId     []string `protobuf:"bytes,3,rep,name=settlement_bill_id,json=settlementBillId,proto3" json:"settlement_bill_id,omitempty"`
	Remark               string   `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiptBillListRespReceiptBill) Reset()         { *m = GetReceiptBillListRespReceiptBill{} }
func (m *GetReceiptBillListRespReceiptBill) String() string { return proto.CompactTextString(m) }
func (*GetReceiptBillListRespReceiptBill) ProtoMessage()    {}
func (*GetReceiptBillListRespReceiptBill) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{50, 0}
}
func (m *GetReceiptBillListRespReceiptBill) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptBillListRespReceiptBill.Unmarshal(m, b)
}
func (m *GetReceiptBillListRespReceiptBill) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptBillListRespReceiptBill.Marshal(b, m, deterministic)
}
func (dst *GetReceiptBillListRespReceiptBill) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptBillListRespReceiptBill.Merge(dst, src)
}
func (m *GetReceiptBillListRespReceiptBill) XXX_Size() int {
	return xxx_messageInfo_GetReceiptBillListRespReceiptBill.Size(m)
}
func (m *GetReceiptBillListRespReceiptBill) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptBillListRespReceiptBill.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptBillListRespReceiptBill proto.InternalMessageInfo

func (m *GetReceiptBillListRespReceiptBill) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

func (m *GetReceiptBillListRespReceiptBill) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetReceiptBillListRespReceiptBill) GetSettlementBillId() []string {
	if m != nil {
		return m.SettlementBillId
	}
	return nil
}

func (m *GetReceiptBillListRespReceiptBill) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type GetReceiptListReq struct {
	ReceiptBillId        string   `protobuf:"bytes,1,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiptListReq) Reset()         { *m = GetReceiptListReq{} }
func (m *GetReceiptListReq) String() string { return proto.CompactTextString(m) }
func (*GetReceiptListReq) ProtoMessage()    {}
func (*GetReceiptListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{51}
}
func (m *GetReceiptListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptListReq.Unmarshal(m, b)
}
func (m *GetReceiptListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptListReq.Marshal(b, m, deterministic)
}
func (dst *GetReceiptListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptListReq.Merge(dst, src)
}
func (m *GetReceiptListReq) XXX_Size() int {
	return xxx_messageInfo_GetReceiptListReq.Size(m)
}
func (m *GetReceiptListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptListReq proto.InternalMessageInfo

func (m *GetReceiptListReq) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

type GetReceiptListResp struct {
	Receipts             []*GetReceiptListResp_ReceiptItem `protobuf:"bytes,1,rep,name=receipts,proto3" json:"receipts,omitempty"`
	ExpressOrderIds      []string                          `protobuf:"bytes,2,rep,name=express_order_ids,json=expressOrderIds,proto3" json:"express_order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetReceiptListResp) Reset()         { *m = GetReceiptListResp{} }
func (m *GetReceiptListResp) String() string { return proto.CompactTextString(m) }
func (*GetReceiptListResp) ProtoMessage()    {}
func (*GetReceiptListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{52}
}
func (m *GetReceiptListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptListResp.Unmarshal(m, b)
}
func (m *GetReceiptListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptListResp.Marshal(b, m, deterministic)
}
func (dst *GetReceiptListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptListResp.Merge(dst, src)
}
func (m *GetReceiptListResp) XXX_Size() int {
	return xxx_messageInfo_GetReceiptListResp.Size(m)
}
func (m *GetReceiptListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptListResp proto.InternalMessageInfo

func (m *GetReceiptListResp) GetReceipts() []*GetReceiptListResp_ReceiptItem {
	if m != nil {
		return m.Receipts
	}
	return nil
}

func (m *GetReceiptListResp) GetExpressOrderIds() []string {
	if m != nil {
		return m.ExpressOrderIds
	}
	return nil
}

// 一张发票
type GetReceiptListResp_ReceiptItem struct {
	ReceiptId            string   `protobuf:"bytes,1,opt,name=receipt_id,json=receiptId,proto3" json:"receipt_id,omitempty"`
	FileName             string   `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileUrl              string   `protobuf:"bytes,3,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiptListResp_ReceiptItem) Reset()         { *m = GetReceiptListResp_ReceiptItem{} }
func (m *GetReceiptListResp_ReceiptItem) String() string { return proto.CompactTextString(m) }
func (*GetReceiptListResp_ReceiptItem) ProtoMessage()    {}
func (*GetReceiptListResp_ReceiptItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{52, 0}
}
func (m *GetReceiptListResp_ReceiptItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptListResp_ReceiptItem.Unmarshal(m, b)
}
func (m *GetReceiptListResp_ReceiptItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptListResp_ReceiptItem.Marshal(b, m, deterministic)
}
func (dst *GetReceiptListResp_ReceiptItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptListResp_ReceiptItem.Merge(dst, src)
}
func (m *GetReceiptListResp_ReceiptItem) XXX_Size() int {
	return xxx_messageInfo_GetReceiptListResp_ReceiptItem.Size(m)
}
func (m *GetReceiptListResp_ReceiptItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptListResp_ReceiptItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptListResp_ReceiptItem proto.InternalMessageInfo

func (m *GetReceiptListResp_ReceiptItem) GetReceiptId() string {
	if m != nil {
		return m.ReceiptId
	}
	return ""
}

func (m *GetReceiptListResp_ReceiptItem) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *GetReceiptListResp_ReceiptItem) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

type GetAssociatedBillItemsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReceiptBillId        string   `protobuf:"bytes,2,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAssociatedBillItemsReq) Reset()         { *m = GetAssociatedBillItemsReq{} }
func (m *GetAssociatedBillItemsReq) String() string { return proto.CompactTextString(m) }
func (*GetAssociatedBillItemsReq) ProtoMessage()    {}
func (*GetAssociatedBillItemsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{53}
}
func (m *GetAssociatedBillItemsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssociatedBillItemsReq.Unmarshal(m, b)
}
func (m *GetAssociatedBillItemsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssociatedBillItemsReq.Marshal(b, m, deterministic)
}
func (dst *GetAssociatedBillItemsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssociatedBillItemsReq.Merge(dst, src)
}
func (m *GetAssociatedBillItemsReq) XXX_Size() int {
	return xxx_messageInfo_GetAssociatedBillItemsReq.Size(m)
}
func (m *GetAssociatedBillItemsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssociatedBillItemsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssociatedBillItemsReq proto.InternalMessageInfo

func (m *GetAssociatedBillItemsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAssociatedBillItemsReq) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

type GetAssociatedBillItemsResp struct {
	List                 []*SettlementBillDetail `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAssociatedBillItemsResp) Reset()         { *m = GetAssociatedBillItemsResp{} }
func (m *GetAssociatedBillItemsResp) String() string { return proto.CompactTextString(m) }
func (*GetAssociatedBillItemsResp) ProtoMessage()    {}
func (*GetAssociatedBillItemsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{54}
}
func (m *GetAssociatedBillItemsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAssociatedBillItemsResp.Unmarshal(m, b)
}
func (m *GetAssociatedBillItemsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAssociatedBillItemsResp.Marshal(b, m, deterministic)
}
func (dst *GetAssociatedBillItemsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAssociatedBillItemsResp.Merge(dst, src)
}
func (m *GetAssociatedBillItemsResp) XXX_Size() int {
	return xxx_messageInfo_GetAssociatedBillItemsResp.Size(m)
}
func (m *GetAssociatedBillItemsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAssociatedBillItemsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAssociatedBillItemsResp proto.InternalMessageInfo

func (m *GetAssociatedBillItemsResp) GetList() []*SettlementBillDetail {
	if m != nil {
		return m.List
	}
	return nil
}

type RecordReceiptFileReq struct {
	ReceiptId            string   `protobuf:"bytes,1,opt,name=receipt_id,json=receiptId,proto3" json:"receipt_id,omitempty"`
	FileName             string   `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	Size                 uint64   `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	ShowSize             string   `protobuf:"bytes,4,opt,name=show_size,json=showSize,proto3" json:"show_size,omitempty"`
	FileClass            string   `protobuf:"bytes,5,opt,name=file_class,json=fileClass,proto3" json:"file_class,omitempty"`
	Uid                  uint32   `protobuf:"varint,6,opt,name=uid,proto3" json:"uid,omitempty"`
	ReceiptDate          string   `protobuf:"bytes,7,opt,name=receipt_date,json=receiptDate,proto3" json:"receipt_date,omitempty"`
	ReceiptCode          string   `protobuf:"bytes,8,opt,name=receipt_code,json=receiptCode,proto3" json:"receipt_code,omitempty"`
	ReceiptNo            string   `protobuf:"bytes,9,opt,name=receipt_no,json=receiptNo,proto3" json:"receipt_no,omitempty"`
	TaxRate              string   `protobuf:"bytes,10,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	TaxAmount            uint64   `protobuf:"varint,11,opt,name=tax_amount,json=taxAmount,proto3" json:"tax_amount,omitempty"`
	Amount               uint64   `protobuf:"varint,12,opt,name=amount,proto3" json:"amount,omitempty"`
	ExTaxAmount          uint64   `protobuf:"varint,13,opt,name=ex_tax_amount,json=exTaxAmount,proto3" json:"ex_tax_amount,omitempty"`
	VCode                string   `protobuf:"bytes,14,opt,name=v_code,json=vCode,proto3" json:"v_code,omitempty"`
	Content              string   `protobuf:"bytes,15,opt,name=content,proto3" json:"content,omitempty"`
	SellerName           string   `protobuf:"bytes,16,opt,name=seller_name,json=sellerName,proto3" json:"seller_name,omitempty"`
	SellerTaxNo          string   `protobuf:"bytes,17,opt,name=seller_tax_no,json=sellerTaxNo,proto3" json:"seller_tax_no,omitempty"`
	PurchaserName        string   `protobuf:"bytes,18,opt,name=purchaser_name,json=purchaserName,proto3" json:"purchaser_name,omitempty"`
	PurchaserTaxNo       string   `protobuf:"bytes,19,opt,name=purchaser_tax_no,json=purchaserTaxNo,proto3" json:"purchaser_tax_no,omitempty"`
	VerifyResult         string   `protobuf:"bytes,20,opt,name=verify_result,json=verifyResult,proto3" json:"verify_result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecordReceiptFileReq) Reset()         { *m = RecordReceiptFileReq{} }
func (m *RecordReceiptFileReq) String() string { return proto.CompactTextString(m) }
func (*RecordReceiptFileReq) ProtoMessage()    {}
func (*RecordReceiptFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{55}
}
func (m *RecordReceiptFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordReceiptFileReq.Unmarshal(m, b)
}
func (m *RecordReceiptFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordReceiptFileReq.Marshal(b, m, deterministic)
}
func (dst *RecordReceiptFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordReceiptFileReq.Merge(dst, src)
}
func (m *RecordReceiptFileReq) XXX_Size() int {
	return xxx_messageInfo_RecordReceiptFileReq.Size(m)
}
func (m *RecordReceiptFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordReceiptFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecordReceiptFileReq proto.InternalMessageInfo

func (m *RecordReceiptFileReq) GetReceiptId() string {
	if m != nil {
		return m.ReceiptId
	}
	return ""
}

func (m *RecordReceiptFileReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *RecordReceiptFileReq) GetSize() uint64 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *RecordReceiptFileReq) GetShowSize() string {
	if m != nil {
		return m.ShowSize
	}
	return ""
}

func (m *RecordReceiptFileReq) GetFileClass() string {
	if m != nil {
		return m.FileClass
	}
	return ""
}

func (m *RecordReceiptFileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecordReceiptFileReq) GetReceiptDate() string {
	if m != nil {
		return m.ReceiptDate
	}
	return ""
}

func (m *RecordReceiptFileReq) GetReceiptCode() string {
	if m != nil {
		return m.ReceiptCode
	}
	return ""
}

func (m *RecordReceiptFileReq) GetReceiptNo() string {
	if m != nil {
		return m.ReceiptNo
	}
	return ""
}

func (m *RecordReceiptFileReq) GetTaxRate() string {
	if m != nil {
		return m.TaxRate
	}
	return ""
}

func (m *RecordReceiptFileReq) GetTaxAmount() uint64 {
	if m != nil {
		return m.TaxAmount
	}
	return 0
}

func (m *RecordReceiptFileReq) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *RecordReceiptFileReq) GetExTaxAmount() uint64 {
	if m != nil {
		return m.ExTaxAmount
	}
	return 0
}

func (m *RecordReceiptFileReq) GetVCode() string {
	if m != nil {
		return m.VCode
	}
	return ""
}

func (m *RecordReceiptFileReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *RecordReceiptFileReq) GetSellerName() string {
	if m != nil {
		return m.SellerName
	}
	return ""
}

func (m *RecordReceiptFileReq) GetSellerTaxNo() string {
	if m != nil {
		return m.SellerTaxNo
	}
	return ""
}

func (m *RecordReceiptFileReq) GetPurchaserName() string {
	if m != nil {
		return m.PurchaserName
	}
	return ""
}

func (m *RecordReceiptFileReq) GetPurchaserTaxNo() string {
	if m != nil {
		return m.PurchaserTaxNo
	}
	return ""
}

func (m *RecordReceiptFileReq) GetVerifyResult() string {
	if m != nil {
		return m.VerifyResult
	}
	return ""
}

type RecordReceiptFileResp struct {
	ReceiptSign          string              `protobuf:"bytes,1,opt,name=receipt_sign,json=receiptSign,proto3" json:"receipt_sign,omitempty"`
	ErrorList            []*ReceiptErrorInfo `protobuf:"bytes,2,rep,name=error_list,json=errorList,proto3" json:"error_list,omitempty"`
	Result               bool                `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *RecordReceiptFileResp) Reset()         { *m = RecordReceiptFileResp{} }
func (m *RecordReceiptFileResp) String() string { return proto.CompactTextString(m) }
func (*RecordReceiptFileResp) ProtoMessage()    {}
func (*RecordReceiptFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{56}
}
func (m *RecordReceiptFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecordReceiptFileResp.Unmarshal(m, b)
}
func (m *RecordReceiptFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecordReceiptFileResp.Marshal(b, m, deterministic)
}
func (dst *RecordReceiptFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecordReceiptFileResp.Merge(dst, src)
}
func (m *RecordReceiptFileResp) XXX_Size() int {
	return xxx_messageInfo_RecordReceiptFileResp.Size(m)
}
func (m *RecordReceiptFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecordReceiptFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecordReceiptFileResp proto.InternalMessageInfo

func (m *RecordReceiptFileResp) GetReceiptSign() string {
	if m != nil {
		return m.ReceiptSign
	}
	return ""
}

func (m *RecordReceiptFileResp) GetErrorList() []*ReceiptErrorInfo {
	if m != nil {
		return m.ErrorList
	}
	return nil
}

func (m *RecordReceiptFileResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

type GenSettleBillPdfReq struct {
	BillId               string   `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenSettleBillPdfReq) Reset()         { *m = GenSettleBillPdfReq{} }
func (m *GenSettleBillPdfReq) String() string { return proto.CompactTextString(m) }
func (*GenSettleBillPdfReq) ProtoMessage()    {}
func (*GenSettleBillPdfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{57}
}
func (m *GenSettleBillPdfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenSettleBillPdfReq.Unmarshal(m, b)
}
func (m *GenSettleBillPdfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenSettleBillPdfReq.Marshal(b, m, deterministic)
}
func (dst *GenSettleBillPdfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenSettleBillPdfReq.Merge(dst, src)
}
func (m *GenSettleBillPdfReq) XXX_Size() int {
	return xxx_messageInfo_GenSettleBillPdfReq.Size(m)
}
func (m *GenSettleBillPdfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GenSettleBillPdfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GenSettleBillPdfReq proto.InternalMessageInfo

func (m *GenSettleBillPdfReq) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

type GenSettleBillPdfResp struct {
	FileId               string   `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GenSettleBillPdfResp) Reset()         { *m = GenSettleBillPdfResp{} }
func (m *GenSettleBillPdfResp) String() string { return proto.CompactTextString(m) }
func (*GenSettleBillPdfResp) ProtoMessage()    {}
func (*GenSettleBillPdfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{58}
}
func (m *GenSettleBillPdfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GenSettleBillPdfResp.Unmarshal(m, b)
}
func (m *GenSettleBillPdfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GenSettleBillPdfResp.Marshal(b, m, deterministic)
}
func (dst *GenSettleBillPdfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GenSettleBillPdfResp.Merge(dst, src)
}
func (m *GenSettleBillPdfResp) XXX_Size() int {
	return xxx_messageInfo_GenSettleBillPdfResp.Size(m)
}
func (m *GenSettleBillPdfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GenSettleBillPdfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GenSettleBillPdfResp proto.InternalMessageInfo

func (m *GenSettleBillPdfResp) GetFileId() string {
	if m != nil {
		return m.FileId
	}
	return ""
}

type GetWaitWithdrawDeepCoopReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWaitWithdrawDeepCoopReq) Reset()         { *m = GetWaitWithdrawDeepCoopReq{} }
func (m *GetWaitWithdrawDeepCoopReq) String() string { return proto.CompactTextString(m) }
func (*GetWaitWithdrawDeepCoopReq) ProtoMessage()    {}
func (*GetWaitWithdrawDeepCoopReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{59}
}
func (m *GetWaitWithdrawDeepCoopReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitWithdrawDeepCoopReq.Unmarshal(m, b)
}
func (m *GetWaitWithdrawDeepCoopReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitWithdrawDeepCoopReq.Marshal(b, m, deterministic)
}
func (dst *GetWaitWithdrawDeepCoopReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitWithdrawDeepCoopReq.Merge(dst, src)
}
func (m *GetWaitWithdrawDeepCoopReq) XXX_Size() int {
	return xxx_messageInfo_GetWaitWithdrawDeepCoopReq.Size(m)
}
func (m *GetWaitWithdrawDeepCoopReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitWithdrawDeepCoopReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitWithdrawDeepCoopReq proto.InternalMessageInfo

func (m *GetWaitWithdrawDeepCoopReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWaitWithdrawDeepCoopResp struct {
	TaxRate              uint32       `protobuf:"varint,1,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	IncomeSum            uint64       `protobuf:"varint,2,opt,name=income_sum,json=incomeSum,proto3" json:"income_sum,omitempty"`
	ActualIncomePay      uint64       `protobuf:"varint,3,opt,name=actual_income_pay,json=actualIncomePay,proto3" json:"actual_income_pay,omitempty"`
	DeepCoopBill         *GeneralBill `protobuf:"bytes,15,opt,name=deep_coop_bill,json=deepCoopBill,proto3" json:"deep_coop_bill,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetWaitWithdrawDeepCoopResp) Reset()         { *m = GetWaitWithdrawDeepCoopResp{} }
func (m *GetWaitWithdrawDeepCoopResp) String() string { return proto.CompactTextString(m) }
func (*GetWaitWithdrawDeepCoopResp) ProtoMessage()    {}
func (*GetWaitWithdrawDeepCoopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{60}
}
func (m *GetWaitWithdrawDeepCoopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitWithdrawDeepCoopResp.Unmarshal(m, b)
}
func (m *GetWaitWithdrawDeepCoopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitWithdrawDeepCoopResp.Marshal(b, m, deterministic)
}
func (dst *GetWaitWithdrawDeepCoopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitWithdrawDeepCoopResp.Merge(dst, src)
}
func (m *GetWaitWithdrawDeepCoopResp) XXX_Size() int {
	return xxx_messageInfo_GetWaitWithdrawDeepCoopResp.Size(m)
}
func (m *GetWaitWithdrawDeepCoopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitWithdrawDeepCoopResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitWithdrawDeepCoopResp proto.InternalMessageInfo

func (m *GetWaitWithdrawDeepCoopResp) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *GetWaitWithdrawDeepCoopResp) GetIncomeSum() uint64 {
	if m != nil {
		return m.IncomeSum
	}
	return 0
}

func (m *GetWaitWithdrawDeepCoopResp) GetActualIncomePay() uint64 {
	if m != nil {
		return m.ActualIncomePay
	}
	return 0
}

func (m *GetWaitWithdrawDeepCoopResp) GetDeepCoopBill() *GeneralBill {
	if m != nil {
		return m.DeepCoopBill
	}
	return nil
}

type GetWaitWithdrawYuyinSubsidyReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWaitWithdrawYuyinSubsidyReq) Reset()         { *m = GetWaitWithdrawYuyinSubsidyReq{} }
func (m *GetWaitWithdrawYuyinSubsidyReq) String() string { return proto.CompactTextString(m) }
func (*GetWaitWithdrawYuyinSubsidyReq) ProtoMessage()    {}
func (*GetWaitWithdrawYuyinSubsidyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{61}
}
func (m *GetWaitWithdrawYuyinSubsidyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitWithdrawYuyinSubsidyReq.Unmarshal(m, b)
}
func (m *GetWaitWithdrawYuyinSubsidyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitWithdrawYuyinSubsidyReq.Marshal(b, m, deterministic)
}
func (dst *GetWaitWithdrawYuyinSubsidyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitWithdrawYuyinSubsidyReq.Merge(dst, src)
}
func (m *GetWaitWithdrawYuyinSubsidyReq) XXX_Size() int {
	return xxx_messageInfo_GetWaitWithdrawYuyinSubsidyReq.Size(m)
}
func (m *GetWaitWithdrawYuyinSubsidyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitWithdrawYuyinSubsidyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitWithdrawYuyinSubsidyReq proto.InternalMessageInfo

func (m *GetWaitWithdrawYuyinSubsidyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetWaitWithdrawYuyinSubsidyResp struct {
	TaxRate              uint32            `protobuf:"varint,1,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	IncomeSum            uint64            `protobuf:"varint,2,opt,name=income_sum,json=incomeSum,proto3" json:"income_sum,omitempty"`
	ActualIncomePay      uint64            `protobuf:"varint,3,opt,name=actual_income_pay,json=actualIncomePay,proto3" json:"actual_income_pay,omitempty"`
	YuyinSubsidy         *YuyinSubsidyBill `protobuf:"bytes,16,opt,name=yuyin_subsidy,json=yuyinSubsidy,proto3" json:"yuyin_subsidy,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetWaitWithdrawYuyinSubsidyResp) Reset()         { *m = GetWaitWithdrawYuyinSubsidyResp{} }
func (m *GetWaitWithdrawYuyinSubsidyResp) String() string { return proto.CompactTextString(m) }
func (*GetWaitWithdrawYuyinSubsidyResp) ProtoMessage()    {}
func (*GetWaitWithdrawYuyinSubsidyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{62}
}
func (m *GetWaitWithdrawYuyinSubsidyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitWithdrawYuyinSubsidyResp.Unmarshal(m, b)
}
func (m *GetWaitWithdrawYuyinSubsidyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitWithdrawYuyinSubsidyResp.Marshal(b, m, deterministic)
}
func (dst *GetWaitWithdrawYuyinSubsidyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitWithdrawYuyinSubsidyResp.Merge(dst, src)
}
func (m *GetWaitWithdrawYuyinSubsidyResp) XXX_Size() int {
	return xxx_messageInfo_GetWaitWithdrawYuyinSubsidyResp.Size(m)
}
func (m *GetWaitWithdrawYuyinSubsidyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitWithdrawYuyinSubsidyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitWithdrawYuyinSubsidyResp proto.InternalMessageInfo

func (m *GetWaitWithdrawYuyinSubsidyResp) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *GetWaitWithdrawYuyinSubsidyResp) GetIncomeSum() uint64 {
	if m != nil {
		return m.IncomeSum
	}
	return 0
}

func (m *GetWaitWithdrawYuyinSubsidyResp) GetActualIncomePay() uint64 {
	if m != nil {
		return m.ActualIncomePay
	}
	return 0
}

func (m *GetWaitWithdrawYuyinSubsidyResp) GetYuyinSubsidy() *YuyinSubsidyBill {
	if m != nil {
		return m.YuyinSubsidy
	}
	return nil
}

type GetWaitWithdrawMonthsReq struct {
	Uid                  uint32             `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BillType             SettlementBillType `protobuf:"varint,2,opt,name=bill_type,json=billType,proto3,enum=settlement_bill.SettlementBillType" json:"bill_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetWaitWithdrawMonthsReq) Reset()         { *m = GetWaitWithdrawMonthsReq{} }
func (m *GetWaitWithdrawMonthsReq) String() string { return proto.CompactTextString(m) }
func (*GetWaitWithdrawMonthsReq) ProtoMessage()    {}
func (*GetWaitWithdrawMonthsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{63}
}
func (m *GetWaitWithdrawMonthsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitWithdrawMonthsReq.Unmarshal(m, b)
}
func (m *GetWaitWithdrawMonthsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitWithdrawMonthsReq.Marshal(b, m, deterministic)
}
func (dst *GetWaitWithdrawMonthsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitWithdrawMonthsReq.Merge(dst, src)
}
func (m *GetWaitWithdrawMonthsReq) XXX_Size() int {
	return xxx_messageInfo_GetWaitWithdrawMonthsReq.Size(m)
}
func (m *GetWaitWithdrawMonthsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitWithdrawMonthsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitWithdrawMonthsReq proto.InternalMessageInfo

func (m *GetWaitWithdrawMonthsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWaitWithdrawMonthsReq) GetBillType() SettlementBillType {
	if m != nil {
		return m.BillType
	}
	return SettlementBillType_UnKnownBillType
}

type GetWaitWithdrawMonthsResp struct {
	Months               []uint32 `protobuf:"varint,1,rep,packed,name=months,proto3" json:"months,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWaitWithdrawMonthsResp) Reset()         { *m = GetWaitWithdrawMonthsResp{} }
func (m *GetWaitWithdrawMonthsResp) String() string { return proto.CompactTextString(m) }
func (*GetWaitWithdrawMonthsResp) ProtoMessage()    {}
func (*GetWaitWithdrawMonthsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{64}
}
func (m *GetWaitWithdrawMonthsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWaitWithdrawMonthsResp.Unmarshal(m, b)
}
func (m *GetWaitWithdrawMonthsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWaitWithdrawMonthsResp.Marshal(b, m, deterministic)
}
func (dst *GetWaitWithdrawMonthsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWaitWithdrawMonthsResp.Merge(dst, src)
}
func (m *GetWaitWithdrawMonthsResp) XXX_Size() int {
	return xxx_messageInfo_GetWaitWithdrawMonthsResp.Size(m)
}
func (m *GetWaitWithdrawMonthsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWaitWithdrawMonthsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWaitWithdrawMonthsResp proto.InternalMessageInfo

func (m *GetWaitWithdrawMonthsResp) GetMonths() []uint32 {
	if m != nil {
		return m.Months
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetMonthsBySettleBillIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BillId               string   `protobuf:"bytes,2,opt,name=billId,proto3" json:"billId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMonthsBySettleBillIdReq) Reset()         { *m = GetMonthsBySettleBillIdReq{} }
func (m *GetMonthsBySettleBillIdReq) String() string { return proto.CompactTextString(m) }
func (*GetMonthsBySettleBillIdReq) ProtoMessage()    {}
func (*GetMonthsBySettleBillIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{65}
}
func (m *GetMonthsBySettleBillIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthsBySettleBillIdReq.Unmarshal(m, b)
}
func (m *GetMonthsBySettleBillIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthsBySettleBillIdReq.Marshal(b, m, deterministic)
}
func (dst *GetMonthsBySettleBillIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthsBySettleBillIdReq.Merge(dst, src)
}
func (m *GetMonthsBySettleBillIdReq) XXX_Size() int {
	return xxx_messageInfo_GetMonthsBySettleBillIdReq.Size(m)
}
func (m *GetMonthsBySettleBillIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthsBySettleBillIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthsBySettleBillIdReq proto.InternalMessageInfo

func (m *GetMonthsBySettleBillIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMonthsBySettleBillIdReq) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

type GetMonthsBySettleBillIdResp struct {
	Months               []uint32 `protobuf:"varint,1,rep,packed,name=months,proto3" json:"months,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMonthsBySettleBillIdResp) Reset()         { *m = GetMonthsBySettleBillIdResp{} }
func (m *GetMonthsBySettleBillIdResp) String() string { return proto.CompactTextString(m) }
func (*GetMonthsBySettleBillIdResp) ProtoMessage()    {}
func (*GetMonthsBySettleBillIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{66}
}
func (m *GetMonthsBySettleBillIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMonthsBySettleBillIdResp.Unmarshal(m, b)
}
func (m *GetMonthsBySettleBillIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMonthsBySettleBillIdResp.Marshal(b, m, deterministic)
}
func (dst *GetMonthsBySettleBillIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMonthsBySettleBillIdResp.Merge(dst, src)
}
func (m *GetMonthsBySettleBillIdResp) XXX_Size() int {
	return xxx_messageInfo_GetMonthsBySettleBillIdResp.Size(m)
}
func (m *GetMonthsBySettleBillIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMonthsBySettleBillIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMonthsBySettleBillIdResp proto.InternalMessageInfo

func (m *GetMonthsBySettleBillIdResp) GetMonths() []uint32 {
	if m != nil {
		return m.Months
	}
	return nil
}

type GetExtraIncomeDetailDeductResp struct {
	List                 []*GetExtraIncomeDetailDeductResp_DeductItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetExtraIncomeDetailDeductResp) Reset()         { *m = GetExtraIncomeDetailDeductResp{} }
func (m *GetExtraIncomeDetailDeductResp) String() string { return proto.CompactTextString(m) }
func (*GetExtraIncomeDetailDeductResp) ProtoMessage()    {}
func (*GetExtraIncomeDetailDeductResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{67}
}
func (m *GetExtraIncomeDetailDeductResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeDetailDeductResp.Unmarshal(m, b)
}
func (m *GetExtraIncomeDetailDeductResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeDetailDeductResp.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeDetailDeductResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeDetailDeductResp.Merge(dst, src)
}
func (m *GetExtraIncomeDetailDeductResp) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeDetailDeductResp.Size(m)
}
func (m *GetExtraIncomeDetailDeductResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeDetailDeductResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeDetailDeductResp proto.InternalMessageInfo

func (m *GetExtraIncomeDetailDeductResp) GetList() []*GetExtraIncomeDetailDeductResp_DeductItem {
	if m != nil {
		return m.List
	}
	return nil
}

type GetExtraIncomeDetailDeductResp_DeductItem struct {
	AmuseDeductMoney     uint64   `protobuf:"varint,1,opt,name=amuse_deduct_money,json=amuseDeductMoney,proto3" json:"amuse_deduct_money,omitempty"`
	AmuseRemark          string   `protobuf:"bytes,2,opt,name=amuse_remark,json=amuseRemark,proto3" json:"amuse_remark,omitempty"`
	YuyinDeductMoney     uint64   `protobuf:"varint,3,opt,name=yuyin_deduct_money,json=yuyinDeductMoney,proto3" json:"yuyin_deduct_money,omitempty"`
	YuyinRemark          string   `protobuf:"bytes,4,opt,name=yuyin_remark,json=yuyinRemark,proto3" json:"yuyin_remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExtraIncomeDetailDeductResp_DeductItem) Reset() {
	*m = GetExtraIncomeDetailDeductResp_DeductItem{}
}
func (m *GetExtraIncomeDetailDeductResp_DeductItem) String() string {
	return proto.CompactTextString(m)
}
func (*GetExtraIncomeDetailDeductResp_DeductItem) ProtoMessage() {}
func (*GetExtraIncomeDetailDeductResp_DeductItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{67, 0}
}
func (m *GetExtraIncomeDetailDeductResp_DeductItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExtraIncomeDetailDeductResp_DeductItem.Unmarshal(m, b)
}
func (m *GetExtraIncomeDetailDeductResp_DeductItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExtraIncomeDetailDeductResp_DeductItem.Marshal(b, m, deterministic)
}
func (dst *GetExtraIncomeDetailDeductResp_DeductItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExtraIncomeDetailDeductResp_DeductItem.Merge(dst, src)
}
func (m *GetExtraIncomeDetailDeductResp_DeductItem) XXX_Size() int {
	return xxx_messageInfo_GetExtraIncomeDetailDeductResp_DeductItem.Size(m)
}
func (m *GetExtraIncomeDetailDeductResp_DeductItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExtraIncomeDetailDeductResp_DeductItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetExtraIncomeDetailDeductResp_DeductItem proto.InternalMessageInfo

func (m *GetExtraIncomeDetailDeductResp_DeductItem) GetAmuseDeductMoney() uint64 {
	if m != nil {
		return m.AmuseDeductMoney
	}
	return 0
}

func (m *GetExtraIncomeDetailDeductResp_DeductItem) GetAmuseRemark() string {
	if m != nil {
		return m.AmuseRemark
	}
	return ""
}

func (m *GetExtraIncomeDetailDeductResp_DeductItem) GetYuyinDeductMoney() uint64 {
	if m != nil {
		return m.YuyinDeductMoney
	}
	return 0
}

func (m *GetExtraIncomeDetailDeductResp_DeductItem) GetYuyinRemark() string {
	if m != nil {
		return m.YuyinRemark
	}
	return ""
}

type ConfirmWithdrawReq struct {
	BillId               string   `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	AlwaysAllow          bool     `protobuf:"varint,2,opt,name=always_allow,json=alwaysAllow,proto3" json:"always_allow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmWithdrawReq) Reset()         { *m = ConfirmWithdrawReq{} }
func (m *ConfirmWithdrawReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmWithdrawReq) ProtoMessage()    {}
func (*ConfirmWithdrawReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{68}
}
func (m *ConfirmWithdrawReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmWithdrawReq.Unmarshal(m, b)
}
func (m *ConfirmWithdrawReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmWithdrawReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmWithdrawReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmWithdrawReq.Merge(dst, src)
}
func (m *ConfirmWithdrawReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmWithdrawReq.Size(m)
}
func (m *ConfirmWithdrawReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmWithdrawReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmWithdrawReq proto.InternalMessageInfo

func (m *ConfirmWithdrawReq) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *ConfirmWithdrawReq) GetAlwaysAllow() bool {
	if m != nil {
		return m.AlwaysAllow
	}
	return false
}

type ConfirmWithdrawResp struct {
	BillId               string   `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	WithdrawMoney        uint64   `protobuf:"varint,2,opt,name=withdraw_money,json=withdrawMoney,proto3" json:"withdraw_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmWithdrawResp) Reset()         { *m = ConfirmWithdrawResp{} }
func (m *ConfirmWithdrawResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmWithdrawResp) ProtoMessage()    {}
func (*ConfirmWithdrawResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{69}
}
func (m *ConfirmWithdrawResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmWithdrawResp.Unmarshal(m, b)
}
func (m *ConfirmWithdrawResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmWithdrawResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmWithdrawResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmWithdrawResp.Merge(dst, src)
}
func (m *ConfirmWithdrawResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmWithdrawResp.Size(m)
}
func (m *ConfirmWithdrawResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmWithdrawResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmWithdrawResp proto.InternalMessageInfo

func (m *ConfirmWithdrawResp) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *ConfirmWithdrawResp) GetWithdrawMoney() uint64 {
	if m != nil {
		return m.WithdrawMoney
	}
	return 0
}

type GetSettlementBillWaitWithdrawReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSettlementBillWaitWithdrawReq) Reset()         { *m = GetSettlementBillWaitWithdrawReq{} }
func (m *GetSettlementBillWaitWithdrawReq) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillWaitWithdrawReq) ProtoMessage()    {}
func (*GetSettlementBillWaitWithdrawReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{70}
}
func (m *GetSettlementBillWaitWithdrawReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillWaitWithdrawReq.Unmarshal(m, b)
}
func (m *GetSettlementBillWaitWithdrawReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillWaitWithdrawReq.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillWaitWithdrawReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillWaitWithdrawReq.Merge(dst, src)
}
func (m *GetSettlementBillWaitWithdrawReq) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillWaitWithdrawReq.Size(m)
}
func (m *GetSettlementBillWaitWithdrawReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillWaitWithdrawReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillWaitWithdrawReq proto.InternalMessageInfo

func (m *GetSettlementBillWaitWithdrawReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetSettlementBillWaitWithdrawResp struct {
	List                    []*SettlementBillDetail `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TaxCompensationRate     uint32                  `protobuf:"varint,2,opt,name=taxCompensation_rate,json=taxCompensationRate,proto3" json:"taxCompensation_rate,omitempty"`
	TaxCompensationRateText string                  `protobuf:"bytes,3,opt,name=taxCompensation_rate_text,json=taxCompensationRateText,proto3" json:"taxCompensation_rate_text,omitempty"`
	CompensationPoint       uint32                  `protobuf:"varint,4,opt,name=compensation_point,json=compensationPoint,proto3" json:"compensation_point,omitempty"`
	CompensationPointText   string                  `protobuf:"bytes,5,opt,name=compensation_point_text,json=compensationPointText,proto3" json:"compensation_point_text,omitempty"`
	TaxRate                 uint32                  `protobuf:"varint,6,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                `json:"-"`
	XXX_unrecognized        []byte                  `json:"-"`
	XXX_sizecache           int32                   `json:"-"`
}

func (m *GetSettlementBillWaitWithdrawResp) Reset()         { *m = GetSettlementBillWaitWithdrawResp{} }
func (m *GetSettlementBillWaitWithdrawResp) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillWaitWithdrawResp) ProtoMessage()    {}
func (*GetSettlementBillWaitWithdrawResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{71}
}
func (m *GetSettlementBillWaitWithdrawResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillWaitWithdrawResp.Unmarshal(m, b)
}
func (m *GetSettlementBillWaitWithdrawResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillWaitWithdrawResp.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillWaitWithdrawResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillWaitWithdrawResp.Merge(dst, src)
}
func (m *GetSettlementBillWaitWithdrawResp) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillWaitWithdrawResp.Size(m)
}
func (m *GetSettlementBillWaitWithdrawResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillWaitWithdrawResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillWaitWithdrawResp proto.InternalMessageInfo

func (m *GetSettlementBillWaitWithdrawResp) GetList() []*SettlementBillDetail {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetSettlementBillWaitWithdrawResp) GetTaxCompensationRate() uint32 {
	if m != nil {
		return m.TaxCompensationRate
	}
	return 0
}

func (m *GetSettlementBillWaitWithdrawResp) GetTaxCompensationRateText() string {
	if m != nil {
		return m.TaxCompensationRateText
	}
	return ""
}

func (m *GetSettlementBillWaitWithdrawResp) GetCompensationPoint() uint32 {
	if m != nil {
		return m.CompensationPoint
	}
	return 0
}

func (m *GetSettlementBillWaitWithdrawResp) GetCompensationPointText() string {
	if m != nil {
		return m.CompensationPointText
	}
	return ""
}

func (m *GetSettlementBillWaitWithdrawResp) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

type GetDeductMoneyListByBillIdReq struct {
	BillId               string   `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetDeductMoneyListByBillIdReq) Reset()         { *m = GetDeductMoneyListByBillIdReq{} }
func (m *GetDeductMoneyListByBillIdReq) String() string { return proto.CompactTextString(m) }
func (*GetDeductMoneyListByBillIdReq) ProtoMessage()    {}
func (*GetDeductMoneyListByBillIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{72}
}
func (m *GetDeductMoneyListByBillIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeductMoneyListByBillIdReq.Unmarshal(m, b)
}
func (m *GetDeductMoneyListByBillIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeductMoneyListByBillIdReq.Marshal(b, m, deterministic)
}
func (dst *GetDeductMoneyListByBillIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeductMoneyListByBillIdReq.Merge(dst, src)
}
func (m *GetDeductMoneyListByBillIdReq) XXX_Size() int {
	return xxx_messageInfo_GetDeductMoneyListByBillIdReq.Size(m)
}
func (m *GetDeductMoneyListByBillIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeductMoneyListByBillIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeductMoneyListByBillIdReq proto.InternalMessageInfo

func (m *GetDeductMoneyListByBillIdReq) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

type DeductMoneyDetail struct {
	DeductMoney          uint64   `protobuf:"varint,1,opt,name=deduct_money,json=deductMoney,proto3" json:"deduct_money,omitempty"`
	Remark               string   `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	SettlementDate       uint32   `protobuf:"varint,3,opt,name=settlement_date,json=settlementDate,proto3" json:"settlement_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeductMoneyDetail) Reset()         { *m = DeductMoneyDetail{} }
func (m *DeductMoneyDetail) String() string { return proto.CompactTextString(m) }
func (*DeductMoneyDetail) ProtoMessage()    {}
func (*DeductMoneyDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{73}
}
func (m *DeductMoneyDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeductMoneyDetail.Unmarshal(m, b)
}
func (m *DeductMoneyDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeductMoneyDetail.Marshal(b, m, deterministic)
}
func (dst *DeductMoneyDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeductMoneyDetail.Merge(dst, src)
}
func (m *DeductMoneyDetail) XXX_Size() int {
	return xxx_messageInfo_DeductMoneyDetail.Size(m)
}
func (m *DeductMoneyDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DeductMoneyDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DeductMoneyDetail proto.InternalMessageInfo

func (m *DeductMoneyDetail) GetDeductMoney() uint64 {
	if m != nil {
		return m.DeductMoney
	}
	return 0
}

func (m *DeductMoneyDetail) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DeductMoneyDetail) GetSettlementDate() uint32 {
	if m != nil {
		return m.SettlementDate
	}
	return 0
}

type GetDeductMoneyListByBillIdResp struct {
	List                 []*DeductMoneyDetail `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetDeductMoneyListByBillIdResp) Reset()         { *m = GetDeductMoneyListByBillIdResp{} }
func (m *GetDeductMoneyListByBillIdResp) String() string { return proto.CompactTextString(m) }
func (*GetDeductMoneyListByBillIdResp) ProtoMessage()    {}
func (*GetDeductMoneyListByBillIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{74}
}
func (m *GetDeductMoneyListByBillIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetDeductMoneyListByBillIdResp.Unmarshal(m, b)
}
func (m *GetDeductMoneyListByBillIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetDeductMoneyListByBillIdResp.Marshal(b, m, deterministic)
}
func (dst *GetDeductMoneyListByBillIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetDeductMoneyListByBillIdResp.Merge(dst, src)
}
func (m *GetDeductMoneyListByBillIdResp) XXX_Size() int {
	return xxx_messageInfo_GetDeductMoneyListByBillIdResp.Size(m)
}
func (m *GetDeductMoneyListByBillIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetDeductMoneyListByBillIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetDeductMoneyListByBillIdResp proto.InternalMessageInfo

func (m *GetDeductMoneyListByBillIdResp) GetList() []*DeductMoneyDetail {
	if m != nil {
		return m.List
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ReportConfirmReq struct {
	StartTime            uint64   `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint64   `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	ConfirmSendEmail     bool     `protobuf:"varint,3,opt,name=confirmSendEmail,proto3" json:"confirmSendEmail,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportConfirmReq) Reset()         { *m = ReportConfirmReq{} }
func (m *ReportConfirmReq) String() string { return proto.CompactTextString(m) }
func (*ReportConfirmReq) ProtoMessage()    {}
func (*ReportConfirmReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{75}
}
func (m *ReportConfirmReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConfirmReq.Unmarshal(m, b)
}
func (m *ReportConfirmReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConfirmReq.Marshal(b, m, deterministic)
}
func (dst *ReportConfirmReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConfirmReq.Merge(dst, src)
}
func (m *ReportConfirmReq) XXX_Size() int {
	return xxx_messageInfo_ReportConfirmReq.Size(m)
}
func (m *ReportConfirmReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConfirmReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConfirmReq proto.InternalMessageInfo

func (m *ReportConfirmReq) GetStartTime() uint64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ReportConfirmReq) GetEndTime() uint64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ReportConfirmReq) GetConfirmSendEmail() bool {
	if m != nil {
		return m.ConfirmSendEmail
	}
	return false
}

type ReportConfirmResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportConfirmResp) Reset()         { *m = ReportConfirmResp{} }
func (m *ReportConfirmResp) String() string { return proto.CompactTextString(m) }
func (*ReportConfirmResp) ProtoMessage()    {}
func (*ReportConfirmResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{76}
}
func (m *ReportConfirmResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConfirmResp.Unmarshal(m, b)
}
func (m *ReportConfirmResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConfirmResp.Marshal(b, m, deterministic)
}
func (dst *ReportConfirmResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConfirmResp.Merge(dst, src)
}
func (m *ReportConfirmResp) XXX_Size() int {
	return xxx_messageInfo_ReportConfirmResp.Size(m)
}
func (m *ReportConfirmResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConfirmResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConfirmResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type ReportConfirmWithReq struct {
	BillType             []SettlementBillType `protobuf:"varint,1,rep,packed,name=Bill_type,json=BillType,proto3,enum=settlement_bill.SettlementBillType" json:"Bill_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *ReportConfirmWithReq) Reset()         { *m = ReportConfirmWithReq{} }
func (m *ReportConfirmWithReq) String() string { return proto.CompactTextString(m) }
func (*ReportConfirmWithReq) ProtoMessage()    {}
func (*ReportConfirmWithReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{77}
}
func (m *ReportConfirmWithReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConfirmWithReq.Unmarshal(m, b)
}
func (m *ReportConfirmWithReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConfirmWithReq.Marshal(b, m, deterministic)
}
func (dst *ReportConfirmWithReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConfirmWithReq.Merge(dst, src)
}
func (m *ReportConfirmWithReq) XXX_Size() int {
	return xxx_messageInfo_ReportConfirmWithReq.Size(m)
}
func (m *ReportConfirmWithReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConfirmWithReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConfirmWithReq proto.InternalMessageInfo

func (m *ReportConfirmWithReq) GetBillType() []SettlementBillType {
	if m != nil {
		return m.BillType
	}
	return nil
}

type ReportConfirmWithResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportConfirmWithResp) Reset()         { *m = ReportConfirmWithResp{} }
func (m *ReportConfirmWithResp) String() string { return proto.CompactTextString(m) }
func (*ReportConfirmWithResp) ProtoMessage()    {}
func (*ReportConfirmWithResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{78}
}
func (m *ReportConfirmWithResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConfirmWithResp.Unmarshal(m, b)
}
func (m *ReportConfirmWithResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConfirmWithResp.Marshal(b, m, deterministic)
}
func (dst *ReportConfirmWithResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConfirmWithResp.Merge(dst, src)
}
func (m *ReportConfirmWithResp) XXX_Size() int {
	return xxx_messageInfo_ReportConfirmWithResp.Size(m)
}
func (m *ReportConfirmWithResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConfirmWithResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConfirmWithResp proto.InternalMessageInfo

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type IsAllowWithdrawReq struct {
	BillType             SettlementBillType `protobuf:"varint,1,opt,name=Bill_type,json=BillType,proto3,enum=settlement_bill.SettlementBillType" json:"Bill_type,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Time                 uint32             `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *IsAllowWithdrawReq) Reset()         { *m = IsAllowWithdrawReq{} }
func (m *IsAllowWithdrawReq) String() string { return proto.CompactTextString(m) }
func (*IsAllowWithdrawReq) ProtoMessage()    {}
func (*IsAllowWithdrawReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{79}
}
func (m *IsAllowWithdrawReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsAllowWithdrawReq.Unmarshal(m, b)
}
func (m *IsAllowWithdrawReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsAllowWithdrawReq.Marshal(b, m, deterministic)
}
func (dst *IsAllowWithdrawReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsAllowWithdrawReq.Merge(dst, src)
}
func (m *IsAllowWithdrawReq) XXX_Size() int {
	return xxx_messageInfo_IsAllowWithdrawReq.Size(m)
}
func (m *IsAllowWithdrawReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsAllowWithdrawReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsAllowWithdrawReq proto.InternalMessageInfo

func (m *IsAllowWithdrawReq) GetBillType() SettlementBillType {
	if m != nil {
		return m.BillType
	}
	return SettlementBillType_UnKnownBillType
}

func (m *IsAllowWithdrawReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *IsAllowWithdrawReq) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

type IsAllowWithdrawResp struct {
	AllowWithdraw        bool     `protobuf:"varint,1,opt,name=allow_withdraw,json=allowWithdraw,proto3" json:"allow_withdraw,omitempty"`
	LimitBalance         uint64   `protobuf:"varint,2,opt,name=limit_balance,json=limitBalance,proto3" json:"limit_balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsAllowWithdrawResp) Reset()         { *m = IsAllowWithdrawResp{} }
func (m *IsAllowWithdrawResp) String() string { return proto.CompactTextString(m) }
func (*IsAllowWithdrawResp) ProtoMessage()    {}
func (*IsAllowWithdrawResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{80}
}
func (m *IsAllowWithdrawResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsAllowWithdrawResp.Unmarshal(m, b)
}
func (m *IsAllowWithdrawResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsAllowWithdrawResp.Marshal(b, m, deterministic)
}
func (dst *IsAllowWithdrawResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsAllowWithdrawResp.Merge(dst, src)
}
func (m *IsAllowWithdrawResp) XXX_Size() int {
	return xxx_messageInfo_IsAllowWithdrawResp.Size(m)
}
func (m *IsAllowWithdrawResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsAllowWithdrawResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsAllowWithdrawResp proto.InternalMessageInfo

func (m *IsAllowWithdrawResp) GetAllowWithdraw() bool {
	if m != nil {
		return m.AllowWithdraw
	}
	return false
}

func (m *IsAllowWithdrawResp) GetLimitBalance() uint64 {
	if m != nil {
		return m.LimitBalance
	}
	return 0
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type PrivateWithdrawReq struct {
	BillType             SettlementBillType `protobuf:"varint,1,opt,name=Bill_type,json=BillType,proto3,enum=settlement_bill.SettlementBillType" json:"Bill_type,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Time                 uint32             `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	Money                uint64             `protobuf:"varint,4,opt,name=money,proto3" json:"money,omitempty"`
	Remark               string             `protobuf:"bytes,5,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *PrivateWithdrawReq) Reset()         { *m = PrivateWithdrawReq{} }
func (m *PrivateWithdrawReq) String() string { return proto.CompactTextString(m) }
func (*PrivateWithdrawReq) ProtoMessage()    {}
func (*PrivateWithdrawReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{81}
}
func (m *PrivateWithdrawReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrivateWithdrawReq.Unmarshal(m, b)
}
func (m *PrivateWithdrawReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrivateWithdrawReq.Marshal(b, m, deterministic)
}
func (dst *PrivateWithdrawReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivateWithdrawReq.Merge(dst, src)
}
func (m *PrivateWithdrawReq) XXX_Size() int {
	return xxx_messageInfo_PrivateWithdrawReq.Size(m)
}
func (m *PrivateWithdrawReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivateWithdrawReq.DiscardUnknown(m)
}

var xxx_messageInfo_PrivateWithdrawReq proto.InternalMessageInfo

func (m *PrivateWithdrawReq) GetBillType() SettlementBillType {
	if m != nil {
		return m.BillType
	}
	return SettlementBillType_UnKnownBillType
}

func (m *PrivateWithdrawReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PrivateWithdrawReq) GetTime() uint32 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *PrivateWithdrawReq) GetMoney() uint64 {
	if m != nil {
		return m.Money
	}
	return 0
}

func (m *PrivateWithdrawReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type PrivateWithdrawResp struct {
	RealMoney            uint64   `protobuf:"varint,1,opt,name=real_money,json=realMoney,proto3" json:"real_money,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PrivateWithdrawResp) Reset()         { *m = PrivateWithdrawResp{} }
func (m *PrivateWithdrawResp) String() string { return proto.CompactTextString(m) }
func (*PrivateWithdrawResp) ProtoMessage()    {}
func (*PrivateWithdrawResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{82}
}
func (m *PrivateWithdrawResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrivateWithdrawResp.Unmarshal(m, b)
}
func (m *PrivateWithdrawResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrivateWithdrawResp.Marshal(b, m, deterministic)
}
func (dst *PrivateWithdrawResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrivateWithdrawResp.Merge(dst, src)
}
func (m *PrivateWithdrawResp) XXX_Size() int {
	return xxx_messageInfo_PrivateWithdrawResp.Size(m)
}
func (m *PrivateWithdrawResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PrivateWithdrawResp.DiscardUnknown(m)
}

var xxx_messageInfo_PrivateWithdrawResp proto.InternalMessageInfo

func (m *PrivateWithdrawResp) GetRealMoney() uint64 {
	if m != nil {
		return m.RealMoney
	}
	return 0
}

type GetAmuseExtraIncomePrepaidReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SettlementDate       uint32   `protobuf:"varint,2,opt,name=settlement_date,json=settlementDate,proto3" json:"settlement_date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseExtraIncomePrepaidReq) Reset()         { *m = GetAmuseExtraIncomePrepaidReq{} }
func (m *GetAmuseExtraIncomePrepaidReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraIncomePrepaidReq) ProtoMessage()    {}
func (*GetAmuseExtraIncomePrepaidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{83}
}
func (m *GetAmuseExtraIncomePrepaidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraIncomePrepaidReq.Unmarshal(m, b)
}
func (m *GetAmuseExtraIncomePrepaidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraIncomePrepaidReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraIncomePrepaidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraIncomePrepaidReq.Merge(dst, src)
}
func (m *GetAmuseExtraIncomePrepaidReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraIncomePrepaidReq.Size(m)
}
func (m *GetAmuseExtraIncomePrepaidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraIncomePrepaidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraIncomePrepaidReq proto.InternalMessageInfo

func (m *GetAmuseExtraIncomePrepaidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetAmuseExtraIncomePrepaidReq) GetSettlementDate() uint32 {
	if m != nil {
		return m.SettlementDate
	}
	return 0
}

type GetAmuseExtraIncomePrepaidResp struct {
	Prepaid              uint64   `protobuf:"varint,1,opt,name=prepaid,proto3" json:"prepaid,omitempty"`
	Remark               string   `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAmuseExtraIncomePrepaidResp) Reset()         { *m = GetAmuseExtraIncomePrepaidResp{} }
func (m *GetAmuseExtraIncomePrepaidResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseExtraIncomePrepaidResp) ProtoMessage()    {}
func (*GetAmuseExtraIncomePrepaidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{84}
}
func (m *GetAmuseExtraIncomePrepaidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseExtraIncomePrepaidResp.Unmarshal(m, b)
}
func (m *GetAmuseExtraIncomePrepaidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseExtraIncomePrepaidResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseExtraIncomePrepaidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseExtraIncomePrepaidResp.Merge(dst, src)
}
func (m *GetAmuseExtraIncomePrepaidResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseExtraIncomePrepaidResp.Size(m)
}
func (m *GetAmuseExtraIncomePrepaidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseExtraIncomePrepaidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseExtraIncomePrepaidResp proto.InternalMessageInfo

func (m *GetAmuseExtraIncomePrepaidResp) GetPrepaid() uint64 {
	if m != nil {
		return m.Prepaid
	}
	return 0
}

func (m *GetAmuseExtraIncomePrepaidResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetAnchorScoreWithdrawRecordsReq struct {
	BillType             SettlementBillType `protobuf:"varint,1,opt,name=Bill_type,json=BillType,proto3,enum=settlement_bill.SettlementBillType" json:"Bill_type,omitempty"`
	AnchorUid            uint32             `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	Offset               uint32             `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32             `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAnchorScoreWithdrawRecordsReq) Reset()         { *m = GetAnchorScoreWithdrawRecordsReq{} }
func (m *GetAnchorScoreWithdrawRecordsReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorScoreWithdrawRecordsReq) ProtoMessage()    {}
func (*GetAnchorScoreWithdrawRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{85}
}
func (m *GetAnchorScoreWithdrawRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsReq.Unmarshal(m, b)
}
func (m *GetAnchorScoreWithdrawRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorScoreWithdrawRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorScoreWithdrawRecordsReq.Merge(dst, src)
}
func (m *GetAnchorScoreWithdrawRecordsReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsReq.Size(m)
}
func (m *GetAnchorScoreWithdrawRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorScoreWithdrawRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorScoreWithdrawRecordsReq proto.InternalMessageInfo

func (m *GetAnchorScoreWithdrawRecordsReq) GetBillType() SettlementBillType {
	if m != nil {
		return m.BillType
	}
	return SettlementBillType_UnKnownBillType
}

func (m *GetAnchorScoreWithdrawRecordsReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *GetAnchorScoreWithdrawRecordsReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAnchorScoreWithdrawRecordsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AnchorScoreWithdrawRecord struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	GuildOwner           uint32   `protobuf:"varint,2,opt,name=guild_owner,json=guildOwner,proto3" json:"guild_owner,omitempty"`
	BillId               string   `protobuf:"bytes,3,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	GuildOwnerWdMoney    uint64   `protobuf:"varint,4,opt,name=guild_owner_wd_money,json=guildOwnerWdMoney,proto3" json:"guild_owner_wd_money,omitempty"`
	GuildOwnerWdMoneyCny string   `protobuf:"bytes,5,opt,name=guild_owner_wd_money_cny,json=guildOwnerWdMoneyCny,proto3" json:"guild_owner_wd_money_cny,omitempty"`
	AnchorWdMoney        uint64   `protobuf:"varint,6,opt,name=anchor_wd_money,json=anchorWdMoney,proto3" json:"anchor_wd_money,omitempty"`
	AnchorWdMoneyCny     string   `protobuf:"bytes,7,opt,name=anchor_wd_money_cny,json=anchorWdMoneyCny,proto3" json:"anchor_wd_money_cny,omitempty"`
	SettleStart          uint64   `protobuf:"varint,8,opt,name=settle_start,json=settleStart,proto3" json:"settle_start,omitempty"`
	SettleEnd            uint64   `protobuf:"varint,9,opt,name=settle_end,json=settleEnd,proto3" json:"settle_end,omitempty"`
	WithdrawTime         uint64   `protobuf:"varint,10,opt,name=withdraw_time,json=withdrawTime,proto3" json:"withdraw_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorScoreWithdrawRecord) Reset()         { *m = AnchorScoreWithdrawRecord{} }
func (m *AnchorScoreWithdrawRecord) String() string { return proto.CompactTextString(m) }
func (*AnchorScoreWithdrawRecord) ProtoMessage()    {}
func (*AnchorScoreWithdrawRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{86}
}
func (m *AnchorScoreWithdrawRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorScoreWithdrawRecord.Unmarshal(m, b)
}
func (m *AnchorScoreWithdrawRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorScoreWithdrawRecord.Marshal(b, m, deterministic)
}
func (dst *AnchorScoreWithdrawRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorScoreWithdrawRecord.Merge(dst, src)
}
func (m *AnchorScoreWithdrawRecord) XXX_Size() int {
	return xxx_messageInfo_AnchorScoreWithdrawRecord.Size(m)
}
func (m *AnchorScoreWithdrawRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorScoreWithdrawRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorScoreWithdrawRecord proto.InternalMessageInfo

func (m *AnchorScoreWithdrawRecord) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetGuildOwner() uint32 {
	if m != nil {
		return m.GuildOwner
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *AnchorScoreWithdrawRecord) GetGuildOwnerWdMoney() uint64 {
	if m != nil {
		return m.GuildOwnerWdMoney
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetGuildOwnerWdMoneyCny() string {
	if m != nil {
		return m.GuildOwnerWdMoneyCny
	}
	return ""
}

func (m *AnchorScoreWithdrawRecord) GetAnchorWdMoney() uint64 {
	if m != nil {
		return m.AnchorWdMoney
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetAnchorWdMoneyCny() string {
	if m != nil {
		return m.AnchorWdMoneyCny
	}
	return ""
}

func (m *AnchorScoreWithdrawRecord) GetSettleStart() uint64 {
	if m != nil {
		return m.SettleStart
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetSettleEnd() uint64 {
	if m != nil {
		return m.SettleEnd
	}
	return 0
}

func (m *AnchorScoreWithdrawRecord) GetWithdrawTime() uint64 {
	if m != nil {
		return m.WithdrawTime
	}
	return 0
}

type GetAnchorScoreWithdrawRecordsResp struct {
	AnchorWithdrawList   []*AnchorScoreWithdrawRecord `protobuf:"bytes,1,rep,name=anchor_withdraw_list,json=anchorWithdrawList,proto3" json:"anchor_withdraw_list,omitempty"`
	Total                uint32                       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetAnchorScoreWithdrawRecordsResp) Reset()         { *m = GetAnchorScoreWithdrawRecordsResp{} }
func (m *GetAnchorScoreWithdrawRecordsResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorScoreWithdrawRecordsResp) ProtoMessage()    {}
func (*GetAnchorScoreWithdrawRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{87}
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Unmarshal(m, b)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorScoreWithdrawRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Merge(dst, src)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.Size(m)
}
func (m *GetAnchorScoreWithdrawRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorScoreWithdrawRecordsResp proto.InternalMessageInfo

func (m *GetAnchorScoreWithdrawRecordsResp) GetAnchorWithdrawList() []*AnchorScoreWithdrawRecord {
	if m != nil {
		return m.AnchorWithdrawList
	}
	return nil
}

func (m *GetAnchorScoreWithdrawRecordsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取发票有效总金额
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetReceiptTotalAmountReq struct {
	ReceiptIds           []string `protobuf:"bytes,1,rep,name=receiptIds,proto3" json:"receiptIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReceiptTotalAmountReq) Reset()         { *m = GetReceiptTotalAmountReq{} }
func (m *GetReceiptTotalAmountReq) String() string { return proto.CompactTextString(m) }
func (*GetReceiptTotalAmountReq) ProtoMessage()    {}
func (*GetReceiptTotalAmountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{88}
}
func (m *GetReceiptTotalAmountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptTotalAmountReq.Unmarshal(m, b)
}
func (m *GetReceiptTotalAmountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptTotalAmountReq.Marshal(b, m, deterministic)
}
func (dst *GetReceiptTotalAmountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptTotalAmountReq.Merge(dst, src)
}
func (m *GetReceiptTotalAmountReq) XXX_Size() int {
	return xxx_messageInfo_GetReceiptTotalAmountReq.Size(m)
}
func (m *GetReceiptTotalAmountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptTotalAmountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptTotalAmountReq proto.InternalMessageInfo

func (m *GetReceiptTotalAmountReq) GetReceiptIds() []string {
	if m != nil {
		return m.ReceiptIds
	}
	return nil
}

// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GetReceiptTotalAmountResp struct {
	List                 map[string]uint64 `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	TotalAmount          uint64            `protobuf:"varint,2,opt,name=totalAmount,proto3" json:"totalAmount,omitempty"`
	ValidTotalAmount     uint64            `protobuf:"varint,3,opt,name=validTotalAmount,proto3" json:"validTotalAmount,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetReceiptTotalAmountResp) Reset()         { *m = GetReceiptTotalAmountResp{} }
func (m *GetReceiptTotalAmountResp) String() string { return proto.CompactTextString(m) }
func (*GetReceiptTotalAmountResp) ProtoMessage()    {}
func (*GetReceiptTotalAmountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{89}
}
func (m *GetReceiptTotalAmountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReceiptTotalAmountResp.Unmarshal(m, b)
}
func (m *GetReceiptTotalAmountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReceiptTotalAmountResp.Marshal(b, m, deterministic)
}
func (dst *GetReceiptTotalAmountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReceiptTotalAmountResp.Merge(dst, src)
}
func (m *GetReceiptTotalAmountResp) XXX_Size() int {
	return xxx_messageInfo_GetReceiptTotalAmountResp.Size(m)
}
func (m *GetReceiptTotalAmountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReceiptTotalAmountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReceiptTotalAmountResp proto.InternalMessageInfo

func (m *GetReceiptTotalAmountResp) GetList() map[string]uint64 {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetReceiptTotalAmountResp) GetTotalAmount() uint64 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

func (m *GetReceiptTotalAmountResp) GetValidTotalAmount() uint64 {
	if m != nil {
		return m.ValidTotalAmount
	}
	return 0
}

type ReceiptErrorInfo struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Result               string   `protobuf:"bytes,2,opt,name=result,proto3" json:"result,omitempty"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiptErrorInfo) Reset()         { *m = ReceiptErrorInfo{} }
func (m *ReceiptErrorInfo) String() string { return proto.CompactTextString(m) }
func (*ReceiptErrorInfo) ProtoMessage()    {}
func (*ReceiptErrorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{90}
}
func (m *ReceiptErrorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiptErrorInfo.Unmarshal(m, b)
}
func (m *ReceiptErrorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiptErrorInfo.Marshal(b, m, deterministic)
}
func (dst *ReceiptErrorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiptErrorInfo.Merge(dst, src)
}
func (m *ReceiptErrorInfo) XXX_Size() int {
	return xxx_messageInfo_ReceiptErrorInfo.Size(m)
}
func (m *ReceiptErrorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiptErrorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiptErrorInfo proto.InternalMessageInfo

func (m *ReceiptErrorInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *ReceiptErrorInfo) GetResult() string {
	if m != nil {
		return m.Result
	}
	return ""
}

func (m *ReceiptErrorInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 结算单详情
type SettlementBillDetailV2 struct {
	BillId               string               `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	Type                 SettlementBillType   `protobuf:"varint,2,opt,name=type,proto3,enum=settlement_bill.SettlementBillType" json:"type,omitempty"`
	TypeDesc             string               `protobuf:"bytes,3,opt,name=type_desc,json=typeDesc,proto3" json:"type_desc,omitempty"`
	Uid                  uint32               `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               SettlementBillStatus `protobuf:"varint,5,opt,name=status,proto3,enum=settlement_bill.SettlementBillStatus" json:"status,omitempty"`
	SettleDate           uint32               `protobuf:"varint,6,opt,name=settle_date,json=settleDate,proto3" json:"settle_date,omitempty"`
	SettleStart          uint32               `protobuf:"varint,7,opt,name=settle_start,json=settleStart,proto3" json:"settle_start,omitempty"`
	SettleEnd            uint32               `protobuf:"varint,8,opt,name=settle_end,json=settleEnd,proto3" json:"settle_end,omitempty"`
	CompanyName          string               `protobuf:"bytes,9,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	IncomeSum            uint64               `protobuf:"varint,10,opt,name=income_sum,json=incomeSum,proto3" json:"income_sum,omitempty"`
	ActualIncomePay      uint64               `protobuf:"varint,11,opt,name=actual_income_pay,json=actualIncomePay,proto3" json:"actual_income_pay,omitempty"`
	PrepaidMoney         uint64               `protobuf:"varint,12,opt,name=prepaid_money,json=prepaidMoney,proto3" json:"prepaid_money,omitempty"`
	DeductMoney          uint64               `protobuf:"varint,13,opt,name=deduct_money,json=deductMoney,proto3" json:"deduct_money,omitempty"`
	TaxRate              uint32               `protobuf:"varint,14,opt,name=tax_rate,json=taxRate,proto3" json:"tax_rate,omitempty"`
	TaxCompensationRate  float32              `protobuf:"fixed32,15,opt,name=tax_compensation_rate,json=taxCompensationRate,proto3" json:"tax_compensation_rate,omitempty"`
	CompensationPoint    float32              `protobuf:"fixed32,16,opt,name=compensation_point,json=compensationPoint,proto3" json:"compensation_point,omitempty"`
	CreateTime           uint32               `protobuf:"varint,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	WithdrawTime         uint32               `protobuf:"varint,18,opt,name=withdraw_time,json=withdrawTime,proto3" json:"withdraw_time,omitempty"`
	FinishedTime         uint32               `protobuf:"varint,19,opt,name=finished_time,json=finishedTime,proto3" json:"finished_time,omitempty"`
	FileId               string               `protobuf:"bytes,20,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	FileUrl              string               `protobuf:"bytes,21,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SettlementBillDetailV2) Reset()         { *m = SettlementBillDetailV2{} }
func (m *SettlementBillDetailV2) String() string { return proto.CompactTextString(m) }
func (*SettlementBillDetailV2) ProtoMessage()    {}
func (*SettlementBillDetailV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{91}
}
func (m *SettlementBillDetailV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettlementBillDetailV2.Unmarshal(m, b)
}
func (m *SettlementBillDetailV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettlementBillDetailV2.Marshal(b, m, deterministic)
}
func (dst *SettlementBillDetailV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettlementBillDetailV2.Merge(dst, src)
}
func (m *SettlementBillDetailV2) XXX_Size() int {
	return xxx_messageInfo_SettlementBillDetailV2.Size(m)
}
func (m *SettlementBillDetailV2) XXX_DiscardUnknown() {
	xxx_messageInfo_SettlementBillDetailV2.DiscardUnknown(m)
}

var xxx_messageInfo_SettlementBillDetailV2 proto.InternalMessageInfo

func (m *SettlementBillDetailV2) GetBillId() string {
	if m != nil {
		return m.BillId
	}
	return ""
}

func (m *SettlementBillDetailV2) GetType() SettlementBillType {
	if m != nil {
		return m.Type
	}
	return SettlementBillType_UnKnownBillType
}

func (m *SettlementBillDetailV2) GetTypeDesc() string {
	if m != nil {
		return m.TypeDesc
	}
	return ""
}

func (m *SettlementBillDetailV2) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SettlementBillDetailV2) GetStatus() SettlementBillStatus {
	if m != nil {
		return m.Status
	}
	return SettlementBillStatus_Creating
}

func (m *SettlementBillDetailV2) GetSettleDate() uint32 {
	if m != nil {
		return m.SettleDate
	}
	return 0
}

func (m *SettlementBillDetailV2) GetSettleStart() uint32 {
	if m != nil {
		return m.SettleStart
	}
	return 0
}

func (m *SettlementBillDetailV2) GetSettleEnd() uint32 {
	if m != nil {
		return m.SettleEnd
	}
	return 0
}

func (m *SettlementBillDetailV2) GetCompanyName() string {
	if m != nil {
		return m.CompanyName
	}
	return ""
}

func (m *SettlementBillDetailV2) GetIncomeSum() uint64 {
	if m != nil {
		return m.IncomeSum
	}
	return 0
}

func (m *SettlementBillDetailV2) GetActualIncomePay() uint64 {
	if m != nil {
		return m.ActualIncomePay
	}
	return 0
}

func (m *SettlementBillDetailV2) GetPrepaidMoney() uint64 {
	if m != nil {
		return m.PrepaidMoney
	}
	return 0
}

func (m *SettlementBillDetailV2) GetDeductMoney() uint64 {
	if m != nil {
		return m.DeductMoney
	}
	return 0
}

func (m *SettlementBillDetailV2) GetTaxRate() uint32 {
	if m != nil {
		return m.TaxRate
	}
	return 0
}

func (m *SettlementBillDetailV2) GetTaxCompensationRate() float32 {
	if m != nil {
		return m.TaxCompensationRate
	}
	return 0
}

func (m *SettlementBillDetailV2) GetCompensationPoint() float32 {
	if m != nil {
		return m.CompensationPoint
	}
	return 0
}

func (m *SettlementBillDetailV2) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *SettlementBillDetailV2) GetWithdrawTime() uint32 {
	if m != nil {
		return m.WithdrawTime
	}
	return 0
}

func (m *SettlementBillDetailV2) GetFinishedTime() uint32 {
	if m != nil {
		return m.FinishedTime
	}
	return 0
}

func (m *SettlementBillDetailV2) GetFileId() string {
	if m != nil {
		return m.FileId
	}
	return ""
}

func (m *SettlementBillDetailV2) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

// 审批单详情
type ReceiptBillDetailV2 struct {
	ReceiptBillId        string                    `protobuf:"bytes,1,opt,name=receipt_bill_id,json=receiptBillId,proto3" json:"receipt_bill_id,omitempty"`
	InstId               string                    `protobuf:"bytes,2,opt,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	SeqNo                string                    `protobuf:"bytes,3,opt,name=seq_no,json=seqNo,proto3" json:"seq_no,omitempty"`
	Status               OAAuditBillStatus         `protobuf:"varint,4,opt,name=status,proto3,enum=settlement_bill.OAAuditBillStatus" json:"status,omitempty"`
	SettlementBillList   []*SettlementBillDetailV2 `protobuf:"bytes,5,rep,name=settlement_bill_list,json=settlementBillList,proto3" json:"settlement_bill_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ReceiptBillDetailV2) Reset()         { *m = ReceiptBillDetailV2{} }
func (m *ReceiptBillDetailV2) String() string { return proto.CompactTextString(m) }
func (*ReceiptBillDetailV2) ProtoMessage()    {}
func (*ReceiptBillDetailV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{92}
}
func (m *ReceiptBillDetailV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiptBillDetailV2.Unmarshal(m, b)
}
func (m *ReceiptBillDetailV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiptBillDetailV2.Marshal(b, m, deterministic)
}
func (dst *ReceiptBillDetailV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiptBillDetailV2.Merge(dst, src)
}
func (m *ReceiptBillDetailV2) XXX_Size() int {
	return xxx_messageInfo_ReceiptBillDetailV2.Size(m)
}
func (m *ReceiptBillDetailV2) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiptBillDetailV2.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiptBillDetailV2 proto.InternalMessageInfo

func (m *ReceiptBillDetailV2) GetReceiptBillId() string {
	if m != nil {
		return m.ReceiptBillId
	}
	return ""
}

func (m *ReceiptBillDetailV2) GetInstId() string {
	if m != nil {
		return m.InstId
	}
	return ""
}

func (m *ReceiptBillDetailV2) GetSeqNo() string {
	if m != nil {
		return m.SeqNo
	}
	return ""
}

func (m *ReceiptBillDetailV2) GetStatus() OAAuditBillStatus {
	if m != nil {
		return m.Status
	}
	return OAAuditBillStatus_OAAuditCreating
}

func (m *ReceiptBillDetailV2) GetSettlementBillList() []*SettlementBillDetailV2 {
	if m != nil {
		return m.SettlementBillList
	}
	return nil
}

type GetSettlementBillDetailByInstIdReq struct {
	InstId               []string `protobuf:"bytes,1,rep,name=inst_id,json=instId,proto3" json:"inst_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSettlementBillDetailByInstIdReq) Reset()         { *m = GetSettlementBillDetailByInstIdReq{} }
func (m *GetSettlementBillDetailByInstIdReq) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillDetailByInstIdReq) ProtoMessage()    {}
func (*GetSettlementBillDetailByInstIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{93}
}
func (m *GetSettlementBillDetailByInstIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillDetailByInstIdReq.Unmarshal(m, b)
}
func (m *GetSettlementBillDetailByInstIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillDetailByInstIdReq.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillDetailByInstIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillDetailByInstIdReq.Merge(dst, src)
}
func (m *GetSettlementBillDetailByInstIdReq) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillDetailByInstIdReq.Size(m)
}
func (m *GetSettlementBillDetailByInstIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillDetailByInstIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillDetailByInstIdReq proto.InternalMessageInfo

func (m *GetSettlementBillDetailByInstIdReq) GetInstId() []string {
	if m != nil {
		return m.InstId
	}
	return nil
}

type GetSettlementBillDetailByInstIdResp struct {
	ReceiptBillList      []*ReceiptBillDetailV2 `protobuf:"bytes,1,rep,name=receipt_bill_list,json=receiptBillList,proto3" json:"receipt_bill_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSettlementBillDetailByInstIdResp) Reset()         { *m = GetSettlementBillDetailByInstIdResp{} }
func (m *GetSettlementBillDetailByInstIdResp) String() string { return proto.CompactTextString(m) }
func (*GetSettlementBillDetailByInstIdResp) ProtoMessage()    {}
func (*GetSettlementBillDetailByInstIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_settlement_bill_b97b34d6794596b4, []int{94}
}
func (m *GetSettlementBillDetailByInstIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSettlementBillDetailByInstIdResp.Unmarshal(m, b)
}
func (m *GetSettlementBillDetailByInstIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSettlementBillDetailByInstIdResp.Marshal(b, m, deterministic)
}
func (dst *GetSettlementBillDetailByInstIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSettlementBillDetailByInstIdResp.Merge(dst, src)
}
func (m *GetSettlementBillDetailByInstIdResp) XXX_Size() int {
	return xxx_messageInfo_GetSettlementBillDetailByInstIdResp.Size(m)
}
func (m *GetSettlementBillDetailByInstIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSettlementBillDetailByInstIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSettlementBillDetailByInstIdResp proto.InternalMessageInfo

func (m *GetSettlementBillDetailByInstIdResp) GetReceiptBillList() []*ReceiptBillDetailV2 {
	if m != nil {
		return m.ReceiptBillList
	}
	return nil
}

func init() {
	proto.RegisterType((*GiftScoreBill)(nil), "settlement_bill.GiftScoreBill")
	proto.RegisterType((*AwardScoreBill)(nil), "settlement_bill.AwardScoreBill")
	proto.RegisterType((*AmuseCommissionBill)(nil), "settlement_bill.AmuseCommissionBill")
	proto.RegisterType((*YuyinBaseCommissionBill)(nil), "settlement_bill.YuyinBaseCommissionBill")
	proto.RegisterType((*MaskPKScoreBill)(nil), "settlement_bill.MaskPKScoreBill")
	proto.RegisterType((*MonthMiddleBill)(nil), "settlement_bill.MonthMiddleBill")
	proto.RegisterType((*DeepCoopBill)(nil), "settlement_bill.DeepCoopBill")
	proto.RegisterType((*YuyinSubsidyBill)(nil), "settlement_bill.YuyinSubsidyBill")
	proto.RegisterType((*GeneralBill)(nil), "settlement_bill.GeneralBill")
	proto.RegisterType((*CreateSettlementBillReq)(nil), "settlement_bill.CreateSettlementBillReq")
	proto.RegisterType((*CreateSettlementBillResp)(nil), "settlement_bill.CreateSettlementBillResp")
	proto.RegisterType((*SettlementBillDetail)(nil), "settlement_bill.SettlementBillDetail")
	proto.RegisterType((*GetGuildTaxRateReq)(nil), "settlement_bill.GetGuildTaxRateReq")
	proto.RegisterType((*GetGuildTaxRateResp)(nil), "settlement_bill.GetGuildTaxRateResp")
	proto.RegisterType((*TaxRate)(nil), "settlement_bill.TaxRate")
	proto.RegisterType((*RecordTaxRateReq)(nil), "settlement_bill.RecordTaxRateReq")
	proto.RegisterType((*RecordTaxRateResp)(nil), "settlement_bill.RecordTaxRateResp")
	proto.RegisterType((*SetTaxRateReq)(nil), "settlement_bill.SetTaxRateReq")
	proto.RegisterType((*SetTaxRateResp)(nil), "settlement_bill.SetTaxRateResp")
	proto.RegisterType((*AssociateReceiptReq)(nil), "settlement_bill.AssociateReceiptReq")
	proto.RegisterType((*AssociateReceiptResp)(nil), "settlement_bill.AssociateReceiptResp")
	proto.RegisterType((*DeepCooperationChannelIncome)(nil), "settlement_bill.DeepCooperationChannelIncome")
	proto.RegisterType((*DeepCooperationIncome)(nil), "settlement_bill.DeepCooperationIncome")
	proto.RegisterType((*AnchorSubsidyIncome)(nil), "settlement_bill.AnchorSubsidyIncome")
	proto.RegisterType((*YuyinAnchorSubsidyIncome)(nil), "settlement_bill.YuyinAnchorSubsidyIncome")
	proto.RegisterType((*YuyinNewGuildSubsidyIncome)(nil), "settlement_bill.YuyinNewGuildSubsidyIncome")
	proto.RegisterType((*DeductMoney)(nil), "settlement_bill.DeductMoney")
	proto.RegisterType((*AmuseExtraPrepaidIncome)(nil), "settlement_bill.AmuseExtraPrepaidIncome")
	proto.RegisterType((*RecordExtraIncomeReq)(nil), "settlement_bill.RecordExtraIncomeReq")
	proto.RegisterType((*RecordExtraIncomeResp)(nil), "settlement_bill.RecordExtraIncomeResp")
	proto.RegisterType((*GetTaxRateListReq)(nil), "settlement_bill.GetTaxRateListReq")
	proto.RegisterType((*GetTaxRateListResp)(nil), "settlement_bill.GetTaxRateListResp")
	proto.RegisterType((*BatchGetTaxRateReq)(nil), "settlement_bill.BatchGetTaxRateReq")
	proto.RegisterType((*BatchGetTaxRateResp)(nil), "settlement_bill.BatchGetTaxRateResp")
	proto.RegisterMapType((map[uint32]*TaxRate)(nil), "settlement_bill.BatchGetTaxRateResp.TaxRateMapEntry")
	proto.RegisterType((*GetExtraIncomeRecordListReq)(nil), "settlement_bill.GetExtraIncomeRecordListReq")
	proto.RegisterType((*ExtraIncomeRecordItem)(nil), "settlement_bill.ExtraIncomeRecordItem")
	proto.RegisterType((*GetExtraIncomeRecordListResp)(nil), "settlement_bill.GetExtraIncomeRecordListResp")
	proto.RegisterType((*GetExtraIncomeDetailReq)(nil), "settlement_bill.GetExtraIncomeDetailReq")
	proto.RegisterType((*GetExtraIncomeDetailDeepCoopResp)(nil), "settlement_bill.GetExtraIncomeDetailDeepCoopResp")
	proto.RegisterType((*GetExtraIncomeDetailChannelSubsidyResp)(nil), "settlement_bill.GetExtraIncomeDetailChannelSubsidyResp")
	proto.RegisterType((*GetExtraIncomeDetailNewGuildSubsidyResp)(nil), "settlement_bill.GetExtraIncomeDetailNewGuildSubsidyResp")
	proto.RegisterType((*GetSettlementBillReq)(nil), "settlement_bill.GetSettlementBillReq")
	proto.RegisterType((*GetSettlementBillResp)(nil), "settlement_bill.GetSettlementBillResp")
	proto.RegisterType((*GetSettlementBillWaitReceiptReq)(nil), "settlement_bill.GetSettlementBillWaitReceiptReq")
	proto.RegisterType((*GetSettlementBillWaitReceiptResp)(nil), "settlement_bill.GetSettlementBillWaitReceiptResp")
	proto.RegisterType((*GetPrepaidMoneyReq)(nil), "settlement_bill.GetPrepaidMoneyReq")
	proto.RegisterType((*GetPrepaidMoneyResp)(nil), "settlement_bill.GetPrepaidMoneyResp")
	proto.RegisterType((*GetDeductMoneyReq)(nil), "settlement_bill.GetDeductMoneyReq")
	proto.RegisterType((*GetDeductMoneyResp)(nil), "settlement_bill.GetDeductMoneyResp")
	proto.RegisterType((*GetReceiptBillListReq)(nil), "settlement_bill.GetReceiptBillListReq")
	proto.RegisterType((*GetReceiptBillListResp)(nil), "settlement_bill.GetReceiptBillListResp")
	proto.RegisterType((*GetReceiptBillListRespReceiptBill)(nil), "settlement_bill.GetReceiptBillListResp.receiptBill")
	proto.RegisterType((*GetReceiptListReq)(nil), "settlement_bill.GetReceiptListReq")
	proto.RegisterType((*GetReceiptListResp)(nil), "settlement_bill.GetReceiptListResp")
	proto.RegisterType((*GetReceiptListResp_ReceiptItem)(nil), "settlement_bill.GetReceiptListResp.ReceiptItem")
	proto.RegisterType((*GetAssociatedBillItemsReq)(nil), "settlement_bill.GetAssociatedBillItemsReq")
	proto.RegisterType((*GetAssociatedBillItemsResp)(nil), "settlement_bill.GetAssociatedBillItemsResp")
	proto.RegisterType((*RecordReceiptFileReq)(nil), "settlement_bill.RecordReceiptFileReq")
	proto.RegisterType((*RecordReceiptFileResp)(nil), "settlement_bill.RecordReceiptFileResp")
	proto.RegisterType((*GenSettleBillPdfReq)(nil), "settlement_bill.GenSettleBillPdfReq")
	proto.RegisterType((*GenSettleBillPdfResp)(nil), "settlement_bill.GenSettleBillPdfResp")
	proto.RegisterType((*GetWaitWithdrawDeepCoopReq)(nil), "settlement_bill.GetWaitWithdrawDeepCoopReq")
	proto.RegisterType((*GetWaitWithdrawDeepCoopResp)(nil), "settlement_bill.GetWaitWithdrawDeepCoopResp")
	proto.RegisterType((*GetWaitWithdrawYuyinSubsidyReq)(nil), "settlement_bill.GetWaitWithdrawYuyinSubsidyReq")
	proto.RegisterType((*GetWaitWithdrawYuyinSubsidyResp)(nil), "settlement_bill.GetWaitWithdrawYuyinSubsidyResp")
	proto.RegisterType((*GetWaitWithdrawMonthsReq)(nil), "settlement_bill.GetWaitWithdrawMonthsReq")
	proto.RegisterType((*GetWaitWithdrawMonthsResp)(nil), "settlement_bill.GetWaitWithdrawMonthsResp")
	proto.RegisterType((*GetMonthsBySettleBillIdReq)(nil), "settlement_bill.GetMonthsBySettleBillIdReq")
	proto.RegisterType((*GetMonthsBySettleBillIdResp)(nil), "settlement_bill.GetMonthsBySettleBillIdResp")
	proto.RegisterType((*GetExtraIncomeDetailDeductResp)(nil), "settlement_bill.GetExtraIncomeDetailDeductResp")
	proto.RegisterType((*GetExtraIncomeDetailDeductResp_DeductItem)(nil), "settlement_bill.GetExtraIncomeDetailDeductResp.DeductItem")
	proto.RegisterType((*ConfirmWithdrawReq)(nil), "settlement_bill.ConfirmWithdrawReq")
	proto.RegisterType((*ConfirmWithdrawResp)(nil), "settlement_bill.ConfirmWithdrawResp")
	proto.RegisterType((*GetSettlementBillWaitWithdrawReq)(nil), "settlement_bill.GetSettlementBillWaitWithdrawReq")
	proto.RegisterType((*GetSettlementBillWaitWithdrawResp)(nil), "settlement_bill.GetSettlementBillWaitWithdrawResp")
	proto.RegisterType((*GetDeductMoneyListByBillIdReq)(nil), "settlement_bill.GetDeductMoneyListByBillIdReq")
	proto.RegisterType((*DeductMoneyDetail)(nil), "settlement_bill.DeductMoneyDetail")
	proto.RegisterType((*GetDeductMoneyListByBillIdResp)(nil), "settlement_bill.GetDeductMoneyListByBillIdResp")
	proto.RegisterType((*ReportConfirmReq)(nil), "settlement_bill.ReportConfirmReq")
	proto.RegisterType((*ReportConfirmResp)(nil), "settlement_bill.ReportConfirmResp")
	proto.RegisterType((*ReportConfirmWithReq)(nil), "settlement_bill.ReportConfirmWithReq")
	proto.RegisterType((*ReportConfirmWithResp)(nil), "settlement_bill.ReportConfirmWithResp")
	proto.RegisterType((*IsAllowWithdrawReq)(nil), "settlement_bill.IsAllowWithdrawReq")
	proto.RegisterType((*IsAllowWithdrawResp)(nil), "settlement_bill.IsAllowWithdrawResp")
	proto.RegisterType((*PrivateWithdrawReq)(nil), "settlement_bill.PrivateWithdrawReq")
	proto.RegisterType((*PrivateWithdrawResp)(nil), "settlement_bill.PrivateWithdrawResp")
	proto.RegisterType((*GetAmuseExtraIncomePrepaidReq)(nil), "settlement_bill.GetAmuseExtraIncomePrepaidReq")
	proto.RegisterType((*GetAmuseExtraIncomePrepaidResp)(nil), "settlement_bill.GetAmuseExtraIncomePrepaidResp")
	proto.RegisterType((*GetAnchorScoreWithdrawRecordsReq)(nil), "settlement_bill.GetAnchorScoreWithdrawRecordsReq")
	proto.RegisterType((*AnchorScoreWithdrawRecord)(nil), "settlement_bill.AnchorScoreWithdrawRecord")
	proto.RegisterType((*GetAnchorScoreWithdrawRecordsResp)(nil), "settlement_bill.GetAnchorScoreWithdrawRecordsResp")
	proto.RegisterType((*GetReceiptTotalAmountReq)(nil), "settlement_bill.GetReceiptTotalAmountReq")
	proto.RegisterType((*GetReceiptTotalAmountResp)(nil), "settlement_bill.GetReceiptTotalAmountResp")
	proto.RegisterMapType((map[string]uint64)(nil), "settlement_bill.GetReceiptTotalAmountResp.ListEntry")
	proto.RegisterType((*ReceiptErrorInfo)(nil), "settlement_bill.ReceiptErrorInfo")
	proto.RegisterType((*SettlementBillDetailV2)(nil), "settlement_bill.SettlementBillDetailV2")
	proto.RegisterType((*ReceiptBillDetailV2)(nil), "settlement_bill.ReceiptBillDetailV2")
	proto.RegisterType((*GetSettlementBillDetailByInstIdReq)(nil), "settlement_bill.GetSettlementBillDetailByInstIdReq")
	proto.RegisterType((*GetSettlementBillDetailByInstIdResp)(nil), "settlement_bill.GetSettlementBillDetailByInstIdResp")
	proto.RegisterEnum("settlement_bill.SettlementBillType", SettlementBillType_name, SettlementBillType_value)
	proto.RegisterEnum("settlement_bill.SettlementCycle", SettlementCycle_name, SettlementCycle_value)
	proto.RegisterEnum("settlement_bill.SettlementBillStatus", SettlementBillStatus_name, SettlementBillStatus_value)
	proto.RegisterEnum("settlement_bill.OAAuditBillStatus", OAAuditBillStatus_name, OAAuditBillStatus_value)
	proto.RegisterEnum("settlement_bill.PrivateWithdrawStatus", PrivateWithdrawStatus_name, PrivateWithdrawStatus_value)
	proto.RegisterEnum("settlement_bill.ReceiptStatus", ReceiptStatus_name, ReceiptStatus_value)
	proto.RegisterEnum("settlement_bill.RiskStatus", RiskStatus_name, RiskStatus_value)
	proto.RegisterEnum("settlement_bill.ExtraIncomeType", ExtraIncomeType_name, ExtraIncomeType_value)
	proto.RegisterEnum("settlement_bill.ExtraIncomeDetailType", ExtraIncomeDetailType_name, ExtraIncomeDetailType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SettlementBillClient is the client API for SettlementBill service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SettlementBillClient interface {
	// 创建结算单
	CreateSettlementBill(ctx context.Context, in *CreateSettlementBillReq, opts ...grpc.CallOption) (*CreateSettlementBillResp, error)
	// 税点录入
	RecordTaxRate(ctx context.Context, in *RecordTaxRateReq, opts ...grpc.CallOption) (*RecordTaxRateResp, error)
	// 获取当前时间点会长税点
	GetGuildTaxRate(ctx context.Context, in *GetGuildTaxRateReq, opts ...grpc.CallOption) (*GetGuildTaxRateResp, error)
	// 获取税点列表
	GetTaxRateList(ctx context.Context, in *GetTaxRateListReq, opts ...grpc.CallOption) (*GetTaxRateListResp, error)
	// 批量获取税点
	BatchGetTaxRate(ctx context.Context, in *BatchGetTaxRateReq, opts ...grpc.CallOption) (*BatchGetTaxRateResp, error)
	// 修改税点
	SetTaxRate(ctx context.Context, in *SetTaxRateReq, opts ...grpc.CallOption) (*SetTaxRateResp, error)
	// 额外收益录入
	RecordExtraIncome(ctx context.Context, in *RecordExtraIncomeReq, opts ...grpc.CallOption) (*RecordExtraIncomeResp, error)
	// 关联发票到结算单
	AssociateReceipt(ctx context.Context, in *AssociateReceiptReq, opts ...grpc.CallOption) (*AssociateReceiptResp, error)
	// 获取额外收益录入列表
	GetExtraIncomeRecordList(ctx context.Context, in *GetExtraIncomeRecordListReq, opts ...grpc.CallOption) (*GetExtraIncomeRecordListResp, error)
	// 获取发票有效总金额
	GetReceiptTotalAmount(ctx context.Context, in *GetReceiptTotalAmountReq, opts ...grpc.CallOption) (*GetReceiptTotalAmountResp, error)
	// 获取额外收益详情列表 - 深度
	GetExtraIncomeDetailDeepCoop(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailDeepCoopResp, error)
	// 获取额外收益详情列表 - 主播补贴
	GetExtraIncomeDetailChannelSubsidy(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailChannelSubsidyResp, error)
	// 获取额外收益详情列表 - 新公会补贴
	GetExtraIncomeDetailNewGuildSubsidy(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailNewGuildSubsidyResp, error)
	// 获取额外收益详情列表 - 扣款
	GetExtraIncomeDetailDeduct(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailDeductResp, error)
	// 获取多人互动额外收益预付款金额（用于结算）
	GetAmuseExtraIncomePrepaid(ctx context.Context, in *GetAmuseExtraIncomePrepaidReq, opts ...grpc.CallOption) (*GetAmuseExtraIncomePrepaidResp, error)
	// 获取结算单详情
	GetSettlementBill(ctx context.Context, in *GetSettlementBillReq, opts ...grpc.CallOption) (*GetSettlementBillResp, error)
	// 获取待上传发票的结算单
	GetSettlementBillWaitReceipt(ctx context.Context, in *GetSettlementBillWaitReceiptReq, opts ...grpc.CallOption) (*GetSettlementBillWaitReceiptResp, error)
	// 获取待提现的结算单列表
	GetSettlementBillWaitWithdraw(ctx context.Context, in *GetSettlementBillWaitWithdrawReq, opts ...grpc.CallOption) (*GetSettlementBillWaitWithdrawResp, error)
	// 获取会长预付金额
	GetPrepaidMoney(ctx context.Context, in *GetPrepaidMoneyReq, opts ...grpc.CallOption) (*GetPrepaidMoneyResp, error)
	// 获取会长扣款金额
	GetDeductMoney(ctx context.Context, in *GetDeductMoneyReq, opts ...grpc.CallOption) (*GetDeductMoneyResp, error)
	// 获取发票单列表（分页）
	GetReceiptBillList(ctx context.Context, in *GetReceiptBillListReq, opts ...grpc.CallOption) (*GetReceiptBillListResp, error)
	// 获取发票单关联的所有发票与运单
	GetReceiptList(ctx context.Context, in *GetReceiptListReq, opts ...grpc.CallOption) (*GetReceiptListResp, error)
	// 获取发票单关联的所有结算单数据
	GetAssociatedBillItems(ctx context.Context, in *GetAssociatedBillItemsReq, opts ...grpc.CallOption) (*GetAssociatedBillItemsResp, error)
	// 记录发票文件
	RecordReceiptFile(ctx context.Context, in *RecordReceiptFileReq, opts ...grpc.CallOption) (*RecordReceiptFileResp, error)
	// 生成结算单PDF
	GenSettleBillPdf(ctx context.Context, in *GenSettleBillPdfReq, opts ...grpc.CallOption) (*GenSettleBillPdfResp, error)
	// 多人互动深度合作待提现数据
	GetWaitWithdrawDeepCoop(ctx context.Context, in *GetWaitWithdrawDeepCoopReq, opts ...grpc.CallOption) (*GetWaitWithdrawDeepCoopResp, error)
	// 语音主播新公会补贴待提现数据
	GetWaitWithdrawYuyinSubsidy(ctx context.Context, in *GetWaitWithdrawYuyinSubsidyReq, opts ...grpc.CallOption) (*GetWaitWithdrawYuyinSubsidyResp, error)
	// 获取待提现的所有周期（月份）
	GetWaitWithdrawMonths(ctx context.Context, in *GetWaitWithdrawMonthsReq, opts ...grpc.CallOption) (*GetWaitWithdrawMonthsResp, error)
	// 获取单个结算单所有周期（月份）
	GetMonthsBySettleBillId(ctx context.Context, in *GetMonthsBySettleBillIdReq, opts ...grpc.CallOption) (*GetMonthsBySettleBillIdResp, error)
	// 结算单确认提现
	ConfirmWithdraw(ctx context.Context, in *ConfirmWithdrawReq, opts ...grpc.CallOption) (*ConfirmWithdrawResp, error)
	// 获取扣款详情
	GetDeductMoneyDetailByBillId(ctx context.Context, in *GetDeductMoneyListByBillIdReq, opts ...grpc.CallOption) (*GetDeductMoneyListByBillIdResp, error)
	// 获取主播积分提现记录
	GetAnchorScoreWithdrawRecords(ctx context.Context, in *GetAnchorScoreWithdrawRecordsReq, opts ...grpc.CallOption) (*GetAnchorScoreWithdrawRecordsResp, error)
	// 手动推送确认扣款报表
	ReportConfirmDeductMoney(ctx context.Context, in *ReportConfirmReq, opts ...grpc.CallOption) (*ReportConfirmResp, error)
	// 手动推送确认深度合作收益报表
	ReportConfirmDeepCoop(ctx context.Context, in *ReportConfirmReq, opts ...grpc.CallOption) (*ReportConfirmResp, error)
	// 手动推送确认语音补贴报表
	ReportConfirmYuyinSubsidy(ctx context.Context, in *ReportConfirmReq, opts ...grpc.CallOption) (*ReportConfirmResp, error)
	// 结算报表
	ReportConfirmWith(ctx context.Context, in *ReportConfirmWithReq, opts ...grpc.CallOption) (*ReportConfirmWithResp, error)
	// 查询是否允许提现
	IsAllowWithdraw(ctx context.Context, in *IsAllowWithdrawReq, opts ...grpc.CallOption) (*IsAllowWithdrawResp, error)
	// 非对公提现
	PrivateWithdraw(ctx context.Context, in *PrivateWithdrawReq, opts ...grpc.CallOption) (*PrivateWithdrawResp, error)
	// 通过灵犀审批单ID获取结算单详情
	GetSettlementBillDetailByInstId(ctx context.Context, in *GetSettlementBillDetailByInstIdReq, opts ...grpc.CallOption) (*GetSettlementBillDetailByInstIdResp, error)
}

type settlementBillClient struct {
	cc *grpc.ClientConn
}

func NewSettlementBillClient(cc *grpc.ClientConn) SettlementBillClient {
	return &settlementBillClient{cc}
}

func (c *settlementBillClient) CreateSettlementBill(ctx context.Context, in *CreateSettlementBillReq, opts ...grpc.CallOption) (*CreateSettlementBillResp, error) {
	out := new(CreateSettlementBillResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/CreateSettlementBill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) RecordTaxRate(ctx context.Context, in *RecordTaxRateReq, opts ...grpc.CallOption) (*RecordTaxRateResp, error) {
	out := new(RecordTaxRateResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/RecordTaxRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetGuildTaxRate(ctx context.Context, in *GetGuildTaxRateReq, opts ...grpc.CallOption) (*GetGuildTaxRateResp, error) {
	out := new(GetGuildTaxRateResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetGuildTaxRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetTaxRateList(ctx context.Context, in *GetTaxRateListReq, opts ...grpc.CallOption) (*GetTaxRateListResp, error) {
	out := new(GetTaxRateListResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetTaxRateList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) BatchGetTaxRate(ctx context.Context, in *BatchGetTaxRateReq, opts ...grpc.CallOption) (*BatchGetTaxRateResp, error) {
	out := new(BatchGetTaxRateResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/BatchGetTaxRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) SetTaxRate(ctx context.Context, in *SetTaxRateReq, opts ...grpc.CallOption) (*SetTaxRateResp, error) {
	out := new(SetTaxRateResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/SetTaxRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) RecordExtraIncome(ctx context.Context, in *RecordExtraIncomeReq, opts ...grpc.CallOption) (*RecordExtraIncomeResp, error) {
	out := new(RecordExtraIncomeResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/RecordExtraIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) AssociateReceipt(ctx context.Context, in *AssociateReceiptReq, opts ...grpc.CallOption) (*AssociateReceiptResp, error) {
	out := new(AssociateReceiptResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/AssociateReceipt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetExtraIncomeRecordList(ctx context.Context, in *GetExtraIncomeRecordListReq, opts ...grpc.CallOption) (*GetExtraIncomeRecordListResp, error) {
	out := new(GetExtraIncomeRecordListResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetExtraIncomeRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetReceiptTotalAmount(ctx context.Context, in *GetReceiptTotalAmountReq, opts ...grpc.CallOption) (*GetReceiptTotalAmountResp, error) {
	out := new(GetReceiptTotalAmountResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetReceiptTotalAmount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetExtraIncomeDetailDeepCoop(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailDeepCoopResp, error) {
	out := new(GetExtraIncomeDetailDeepCoopResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetExtraIncomeDetailDeepCoop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetExtraIncomeDetailChannelSubsidy(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailChannelSubsidyResp, error) {
	out := new(GetExtraIncomeDetailChannelSubsidyResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetExtraIncomeDetailChannelSubsidy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetExtraIncomeDetailNewGuildSubsidy(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailNewGuildSubsidyResp, error) {
	out := new(GetExtraIncomeDetailNewGuildSubsidyResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetExtraIncomeDetailNewGuildSubsidy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetExtraIncomeDetailDeduct(ctx context.Context, in *GetExtraIncomeDetailReq, opts ...grpc.CallOption) (*GetExtraIncomeDetailDeductResp, error) {
	out := new(GetExtraIncomeDetailDeductResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetExtraIncomeDetailDeduct", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetAmuseExtraIncomePrepaid(ctx context.Context, in *GetAmuseExtraIncomePrepaidReq, opts ...grpc.CallOption) (*GetAmuseExtraIncomePrepaidResp, error) {
	out := new(GetAmuseExtraIncomePrepaidResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetAmuseExtraIncomePrepaid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetSettlementBill(ctx context.Context, in *GetSettlementBillReq, opts ...grpc.CallOption) (*GetSettlementBillResp, error) {
	out := new(GetSettlementBillResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetSettlementBill", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetSettlementBillWaitReceipt(ctx context.Context, in *GetSettlementBillWaitReceiptReq, opts ...grpc.CallOption) (*GetSettlementBillWaitReceiptResp, error) {
	out := new(GetSettlementBillWaitReceiptResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetSettlementBillWaitReceipt", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetSettlementBillWaitWithdraw(ctx context.Context, in *GetSettlementBillWaitWithdrawReq, opts ...grpc.CallOption) (*GetSettlementBillWaitWithdrawResp, error) {
	out := new(GetSettlementBillWaitWithdrawResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetSettlementBillWaitWithdraw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetPrepaidMoney(ctx context.Context, in *GetPrepaidMoneyReq, opts ...grpc.CallOption) (*GetPrepaidMoneyResp, error) {
	out := new(GetPrepaidMoneyResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetPrepaidMoney", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetDeductMoney(ctx context.Context, in *GetDeductMoneyReq, opts ...grpc.CallOption) (*GetDeductMoneyResp, error) {
	out := new(GetDeductMoneyResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetDeductMoney", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetReceiptBillList(ctx context.Context, in *GetReceiptBillListReq, opts ...grpc.CallOption) (*GetReceiptBillListResp, error) {
	out := new(GetReceiptBillListResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetReceiptBillList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetReceiptList(ctx context.Context, in *GetReceiptListReq, opts ...grpc.CallOption) (*GetReceiptListResp, error) {
	out := new(GetReceiptListResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetReceiptList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetAssociatedBillItems(ctx context.Context, in *GetAssociatedBillItemsReq, opts ...grpc.CallOption) (*GetAssociatedBillItemsResp, error) {
	out := new(GetAssociatedBillItemsResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetAssociatedBillItems", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) RecordReceiptFile(ctx context.Context, in *RecordReceiptFileReq, opts ...grpc.CallOption) (*RecordReceiptFileResp, error) {
	out := new(RecordReceiptFileResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/RecordReceiptFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GenSettleBillPdf(ctx context.Context, in *GenSettleBillPdfReq, opts ...grpc.CallOption) (*GenSettleBillPdfResp, error) {
	out := new(GenSettleBillPdfResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GenSettleBillPdf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetWaitWithdrawDeepCoop(ctx context.Context, in *GetWaitWithdrawDeepCoopReq, opts ...grpc.CallOption) (*GetWaitWithdrawDeepCoopResp, error) {
	out := new(GetWaitWithdrawDeepCoopResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetWaitWithdrawDeepCoop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetWaitWithdrawYuyinSubsidy(ctx context.Context, in *GetWaitWithdrawYuyinSubsidyReq, opts ...grpc.CallOption) (*GetWaitWithdrawYuyinSubsidyResp, error) {
	out := new(GetWaitWithdrawYuyinSubsidyResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetWaitWithdrawYuyinSubsidy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetWaitWithdrawMonths(ctx context.Context, in *GetWaitWithdrawMonthsReq, opts ...grpc.CallOption) (*GetWaitWithdrawMonthsResp, error) {
	out := new(GetWaitWithdrawMonthsResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetWaitWithdrawMonths", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetMonthsBySettleBillId(ctx context.Context, in *GetMonthsBySettleBillIdReq, opts ...grpc.CallOption) (*GetMonthsBySettleBillIdResp, error) {
	out := new(GetMonthsBySettleBillIdResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetMonthsBySettleBillId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) ConfirmWithdraw(ctx context.Context, in *ConfirmWithdrawReq, opts ...grpc.CallOption) (*ConfirmWithdrawResp, error) {
	out := new(ConfirmWithdrawResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/ConfirmWithdraw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetDeductMoneyDetailByBillId(ctx context.Context, in *GetDeductMoneyListByBillIdReq, opts ...grpc.CallOption) (*GetDeductMoneyListByBillIdResp, error) {
	out := new(GetDeductMoneyListByBillIdResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetDeductMoneyDetailByBillId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetAnchorScoreWithdrawRecords(ctx context.Context, in *GetAnchorScoreWithdrawRecordsReq, opts ...grpc.CallOption) (*GetAnchorScoreWithdrawRecordsResp, error) {
	out := new(GetAnchorScoreWithdrawRecordsResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetAnchorScoreWithdrawRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) ReportConfirmDeductMoney(ctx context.Context, in *ReportConfirmReq, opts ...grpc.CallOption) (*ReportConfirmResp, error) {
	out := new(ReportConfirmResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/ReportConfirmDeductMoney", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) ReportConfirmDeepCoop(ctx context.Context, in *ReportConfirmReq, opts ...grpc.CallOption) (*ReportConfirmResp, error) {
	out := new(ReportConfirmResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/ReportConfirmDeepCoop", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) ReportConfirmYuyinSubsidy(ctx context.Context, in *ReportConfirmReq, opts ...grpc.CallOption) (*ReportConfirmResp, error) {
	out := new(ReportConfirmResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/ReportConfirmYuyinSubsidy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) ReportConfirmWith(ctx context.Context, in *ReportConfirmWithReq, opts ...grpc.CallOption) (*ReportConfirmWithResp, error) {
	out := new(ReportConfirmWithResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/ReportConfirmWith", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) IsAllowWithdraw(ctx context.Context, in *IsAllowWithdrawReq, opts ...grpc.CallOption) (*IsAllowWithdrawResp, error) {
	out := new(IsAllowWithdrawResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/IsAllowWithdraw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) PrivateWithdraw(ctx context.Context, in *PrivateWithdrawReq, opts ...grpc.CallOption) (*PrivateWithdrawResp, error) {
	out := new(PrivateWithdrawResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/PrivateWithdraw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *settlementBillClient) GetSettlementBillDetailByInstId(ctx context.Context, in *GetSettlementBillDetailByInstIdReq, opts ...grpc.CallOption) (*GetSettlementBillDetailByInstIdResp, error) {
	out := new(GetSettlementBillDetailByInstIdResp)
	err := c.cc.Invoke(ctx, "/settlement_bill.SettlementBill/GetSettlementBillDetailByInstId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SettlementBillServer is the server API for SettlementBill service.
type SettlementBillServer interface {
	// 创建结算单
	CreateSettlementBill(context.Context, *CreateSettlementBillReq) (*CreateSettlementBillResp, error)
	// 税点录入
	RecordTaxRate(context.Context, *RecordTaxRateReq) (*RecordTaxRateResp, error)
	// 获取当前时间点会长税点
	GetGuildTaxRate(context.Context, *GetGuildTaxRateReq) (*GetGuildTaxRateResp, error)
	// 获取税点列表
	GetTaxRateList(context.Context, *GetTaxRateListReq) (*GetTaxRateListResp, error)
	// 批量获取税点
	BatchGetTaxRate(context.Context, *BatchGetTaxRateReq) (*BatchGetTaxRateResp, error)
	// 修改税点
	SetTaxRate(context.Context, *SetTaxRateReq) (*SetTaxRateResp, error)
	// 额外收益录入
	RecordExtraIncome(context.Context, *RecordExtraIncomeReq) (*RecordExtraIncomeResp, error)
	// 关联发票到结算单
	AssociateReceipt(context.Context, *AssociateReceiptReq) (*AssociateReceiptResp, error)
	// 获取额外收益录入列表
	GetExtraIncomeRecordList(context.Context, *GetExtraIncomeRecordListReq) (*GetExtraIncomeRecordListResp, error)
	// 获取发票有效总金额
	GetReceiptTotalAmount(context.Context, *GetReceiptTotalAmountReq) (*GetReceiptTotalAmountResp, error)
	// 获取额外收益详情列表 - 深度
	GetExtraIncomeDetailDeepCoop(context.Context, *GetExtraIncomeDetailReq) (*GetExtraIncomeDetailDeepCoopResp, error)
	// 获取额外收益详情列表 - 主播补贴
	GetExtraIncomeDetailChannelSubsidy(context.Context, *GetExtraIncomeDetailReq) (*GetExtraIncomeDetailChannelSubsidyResp, error)
	// 获取额外收益详情列表 - 新公会补贴
	GetExtraIncomeDetailNewGuildSubsidy(context.Context, *GetExtraIncomeDetailReq) (*GetExtraIncomeDetailNewGuildSubsidyResp, error)
	// 获取额外收益详情列表 - 扣款
	GetExtraIncomeDetailDeduct(context.Context, *GetExtraIncomeDetailReq) (*GetExtraIncomeDetailDeductResp, error)
	// 获取多人互动额外收益预付款金额（用于结算）
	GetAmuseExtraIncomePrepaid(context.Context, *GetAmuseExtraIncomePrepaidReq) (*GetAmuseExtraIncomePrepaidResp, error)
	// 获取结算单详情
	GetSettlementBill(context.Context, *GetSettlementBillReq) (*GetSettlementBillResp, error)
	// 获取待上传发票的结算单
	GetSettlementBillWaitReceipt(context.Context, *GetSettlementBillWaitReceiptReq) (*GetSettlementBillWaitReceiptResp, error)
	// 获取待提现的结算单列表
	GetSettlementBillWaitWithdraw(context.Context, *GetSettlementBillWaitWithdrawReq) (*GetSettlementBillWaitWithdrawResp, error)
	// 获取会长预付金额
	GetPrepaidMoney(context.Context, *GetPrepaidMoneyReq) (*GetPrepaidMoneyResp, error)
	// 获取会长扣款金额
	GetDeductMoney(context.Context, *GetDeductMoneyReq) (*GetDeductMoneyResp, error)
	// 获取发票单列表（分页）
	GetReceiptBillList(context.Context, *GetReceiptBillListReq) (*GetReceiptBillListResp, error)
	// 获取发票单关联的所有发票与运单
	GetReceiptList(context.Context, *GetReceiptListReq) (*GetReceiptListResp, error)
	// 获取发票单关联的所有结算单数据
	GetAssociatedBillItems(context.Context, *GetAssociatedBillItemsReq) (*GetAssociatedBillItemsResp, error)
	// 记录发票文件
	RecordReceiptFile(context.Context, *RecordReceiptFileReq) (*RecordReceiptFileResp, error)
	// 生成结算单PDF
	GenSettleBillPdf(context.Context, *GenSettleBillPdfReq) (*GenSettleBillPdfResp, error)
	// 多人互动深度合作待提现数据
	GetWaitWithdrawDeepCoop(context.Context, *GetWaitWithdrawDeepCoopReq) (*GetWaitWithdrawDeepCoopResp, error)
	// 语音主播新公会补贴待提现数据
	GetWaitWithdrawYuyinSubsidy(context.Context, *GetWaitWithdrawYuyinSubsidyReq) (*GetWaitWithdrawYuyinSubsidyResp, error)
	// 获取待提现的所有周期（月份）
	GetWaitWithdrawMonths(context.Context, *GetWaitWithdrawMonthsReq) (*GetWaitWithdrawMonthsResp, error)
	// 获取单个结算单所有周期（月份）
	GetMonthsBySettleBillId(context.Context, *GetMonthsBySettleBillIdReq) (*GetMonthsBySettleBillIdResp, error)
	// 结算单确认提现
	ConfirmWithdraw(context.Context, *ConfirmWithdrawReq) (*ConfirmWithdrawResp, error)
	// 获取扣款详情
	GetDeductMoneyDetailByBillId(context.Context, *GetDeductMoneyListByBillIdReq) (*GetDeductMoneyListByBillIdResp, error)
	// 获取主播积分提现记录
	GetAnchorScoreWithdrawRecords(context.Context, *GetAnchorScoreWithdrawRecordsReq) (*GetAnchorScoreWithdrawRecordsResp, error)
	// 手动推送确认扣款报表
	ReportConfirmDeductMoney(context.Context, *ReportConfirmReq) (*ReportConfirmResp, error)
	// 手动推送确认深度合作收益报表
	ReportConfirmDeepCoop(context.Context, *ReportConfirmReq) (*ReportConfirmResp, error)
	// 手动推送确认语音补贴报表
	ReportConfirmYuyinSubsidy(context.Context, *ReportConfirmReq) (*ReportConfirmResp, error)
	// 结算报表
	ReportConfirmWith(context.Context, *ReportConfirmWithReq) (*ReportConfirmWithResp, error)
	// 查询是否允许提现
	IsAllowWithdraw(context.Context, *IsAllowWithdrawReq) (*IsAllowWithdrawResp, error)
	// 非对公提现
	PrivateWithdraw(context.Context, *PrivateWithdrawReq) (*PrivateWithdrawResp, error)
	// 通过灵犀审批单ID获取结算单详情
	GetSettlementBillDetailByInstId(context.Context, *GetSettlementBillDetailByInstIdReq) (*GetSettlementBillDetailByInstIdResp, error)
}

func RegisterSettlementBillServer(s *grpc.Server, srv SettlementBillServer) {
	s.RegisterService(&_SettlementBill_serviceDesc, srv)
}

func _SettlementBill_CreateSettlementBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSettlementBillReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).CreateSettlementBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/CreateSettlementBill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).CreateSettlementBill(ctx, req.(*CreateSettlementBillReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_RecordTaxRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordTaxRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).RecordTaxRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/RecordTaxRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).RecordTaxRate(ctx, req.(*RecordTaxRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetGuildTaxRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildTaxRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetGuildTaxRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetGuildTaxRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetGuildTaxRate(ctx, req.(*GetGuildTaxRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetTaxRateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTaxRateListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetTaxRateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetTaxRateList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetTaxRateList(ctx, req.(*GetTaxRateListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_BatchGetTaxRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTaxRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).BatchGetTaxRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/BatchGetTaxRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).BatchGetTaxRate(ctx, req.(*BatchGetTaxRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_SetTaxRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetTaxRateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).SetTaxRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/SetTaxRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).SetTaxRate(ctx, req.(*SetTaxRateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_RecordExtraIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordExtraIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).RecordExtraIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/RecordExtraIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).RecordExtraIncome(ctx, req.(*RecordExtraIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_AssociateReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssociateReceiptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).AssociateReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/AssociateReceipt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).AssociateReceipt(ctx, req.(*AssociateReceiptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetExtraIncomeRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraIncomeRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetExtraIncomeRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetExtraIncomeRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetExtraIncomeRecordList(ctx, req.(*GetExtraIncomeRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetReceiptTotalAmount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReceiptTotalAmountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetReceiptTotalAmount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetReceiptTotalAmount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetReceiptTotalAmount(ctx, req.(*GetReceiptTotalAmountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetExtraIncomeDetailDeepCoop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetExtraIncomeDetailDeepCoop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetExtraIncomeDetailDeepCoop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetExtraIncomeDetailDeepCoop(ctx, req.(*GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetExtraIncomeDetailChannelSubsidy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetExtraIncomeDetailChannelSubsidy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetExtraIncomeDetailChannelSubsidy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetExtraIncomeDetailChannelSubsidy(ctx, req.(*GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetExtraIncomeDetailNewGuildSubsidy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetExtraIncomeDetailNewGuildSubsidy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetExtraIncomeDetailNewGuildSubsidy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetExtraIncomeDetailNewGuildSubsidy(ctx, req.(*GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetExtraIncomeDetailDeduct_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExtraIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetExtraIncomeDetailDeduct(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetExtraIncomeDetailDeduct",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetExtraIncomeDetailDeduct(ctx, req.(*GetExtraIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetAmuseExtraIncomePrepaid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseExtraIncomePrepaidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetAmuseExtraIncomePrepaid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetAmuseExtraIncomePrepaid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetAmuseExtraIncomePrepaid(ctx, req.(*GetAmuseExtraIncomePrepaidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetSettlementBill_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettlementBillReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetSettlementBill(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetSettlementBill",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetSettlementBill(ctx, req.(*GetSettlementBillReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetSettlementBillWaitReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettlementBillWaitReceiptReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetSettlementBillWaitReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetSettlementBillWaitReceipt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetSettlementBillWaitReceipt(ctx, req.(*GetSettlementBillWaitReceiptReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetSettlementBillWaitWithdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettlementBillWaitWithdrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetSettlementBillWaitWithdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetSettlementBillWaitWithdraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetSettlementBillWaitWithdraw(ctx, req.(*GetSettlementBillWaitWithdrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetPrepaidMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrepaidMoneyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetPrepaidMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetPrepaidMoney",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetPrepaidMoney(ctx, req.(*GetPrepaidMoneyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetDeductMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeductMoneyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetDeductMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetDeductMoney",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetDeductMoney(ctx, req.(*GetDeductMoneyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetReceiptBillList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReceiptBillListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetReceiptBillList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetReceiptBillList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetReceiptBillList(ctx, req.(*GetReceiptBillListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetReceiptList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReceiptListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetReceiptList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetReceiptList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetReceiptList(ctx, req.(*GetReceiptListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetAssociatedBillItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAssociatedBillItemsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetAssociatedBillItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetAssociatedBillItems",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetAssociatedBillItems(ctx, req.(*GetAssociatedBillItemsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_RecordReceiptFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordReceiptFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).RecordReceiptFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/RecordReceiptFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).RecordReceiptFile(ctx, req.(*RecordReceiptFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GenSettleBillPdf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenSettleBillPdfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GenSettleBillPdf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GenSettleBillPdf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GenSettleBillPdf(ctx, req.(*GenSettleBillPdfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetWaitWithdrawDeepCoop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaitWithdrawDeepCoopReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetWaitWithdrawDeepCoop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetWaitWithdrawDeepCoop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetWaitWithdrawDeepCoop(ctx, req.(*GetWaitWithdrawDeepCoopReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetWaitWithdrawYuyinSubsidy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaitWithdrawYuyinSubsidyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetWaitWithdrawYuyinSubsidy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetWaitWithdrawYuyinSubsidy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetWaitWithdrawYuyinSubsidy(ctx, req.(*GetWaitWithdrawYuyinSubsidyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetWaitWithdrawMonths_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWaitWithdrawMonthsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetWaitWithdrawMonths(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetWaitWithdrawMonths",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetWaitWithdrawMonths(ctx, req.(*GetWaitWithdrawMonthsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetMonthsBySettleBillId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMonthsBySettleBillIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetMonthsBySettleBillId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetMonthsBySettleBillId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetMonthsBySettleBillId(ctx, req.(*GetMonthsBySettleBillIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_ConfirmWithdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmWithdrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).ConfirmWithdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/ConfirmWithdraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).ConfirmWithdraw(ctx, req.(*ConfirmWithdrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetDeductMoneyDetailByBillId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeductMoneyListByBillIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetDeductMoneyDetailByBillId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetDeductMoneyDetailByBillId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetDeductMoneyDetailByBillId(ctx, req.(*GetDeductMoneyListByBillIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetAnchorScoreWithdrawRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorScoreWithdrawRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetAnchorScoreWithdrawRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetAnchorScoreWithdrawRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetAnchorScoreWithdrawRecords(ctx, req.(*GetAnchorScoreWithdrawRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_ReportConfirmDeductMoney_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportConfirmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).ReportConfirmDeductMoney(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/ReportConfirmDeductMoney",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).ReportConfirmDeductMoney(ctx, req.(*ReportConfirmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_ReportConfirmDeepCoop_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportConfirmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).ReportConfirmDeepCoop(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/ReportConfirmDeepCoop",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).ReportConfirmDeepCoop(ctx, req.(*ReportConfirmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_ReportConfirmYuyinSubsidy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportConfirmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).ReportConfirmYuyinSubsidy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/ReportConfirmYuyinSubsidy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).ReportConfirmYuyinSubsidy(ctx, req.(*ReportConfirmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_ReportConfirmWith_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportConfirmWithReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).ReportConfirmWith(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/ReportConfirmWith",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).ReportConfirmWith(ctx, req.(*ReportConfirmWithReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_IsAllowWithdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsAllowWithdrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).IsAllowWithdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/IsAllowWithdraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).IsAllowWithdraw(ctx, req.(*IsAllowWithdrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_PrivateWithdraw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PrivateWithdrawReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).PrivateWithdraw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/PrivateWithdraw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).PrivateWithdraw(ctx, req.(*PrivateWithdrawReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SettlementBill_GetSettlementBillDetailByInstId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettlementBillDetailByInstIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SettlementBillServer).GetSettlementBillDetailByInstId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/settlement_bill.SettlementBill/GetSettlementBillDetailByInstId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SettlementBillServer).GetSettlementBillDetailByInstId(ctx, req.(*GetSettlementBillDetailByInstIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SettlementBill_serviceDesc = grpc.ServiceDesc{
	ServiceName: "settlement_bill.SettlementBill",
	HandlerType: (*SettlementBillServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSettlementBill",
			Handler:    _SettlementBill_CreateSettlementBill_Handler,
		},
		{
			MethodName: "RecordTaxRate",
			Handler:    _SettlementBill_RecordTaxRate_Handler,
		},
		{
			MethodName: "GetGuildTaxRate",
			Handler:    _SettlementBill_GetGuildTaxRate_Handler,
		},
		{
			MethodName: "GetTaxRateList",
			Handler:    _SettlementBill_GetTaxRateList_Handler,
		},
		{
			MethodName: "BatchGetTaxRate",
			Handler:    _SettlementBill_BatchGetTaxRate_Handler,
		},
		{
			MethodName: "SetTaxRate",
			Handler:    _SettlementBill_SetTaxRate_Handler,
		},
		{
			MethodName: "RecordExtraIncome",
			Handler:    _SettlementBill_RecordExtraIncome_Handler,
		},
		{
			MethodName: "AssociateReceipt",
			Handler:    _SettlementBill_AssociateReceipt_Handler,
		},
		{
			MethodName: "GetExtraIncomeRecordList",
			Handler:    _SettlementBill_GetExtraIncomeRecordList_Handler,
		},
		{
			MethodName: "GetReceiptTotalAmount",
			Handler:    _SettlementBill_GetReceiptTotalAmount_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailDeepCoop",
			Handler:    _SettlementBill_GetExtraIncomeDetailDeepCoop_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailChannelSubsidy",
			Handler:    _SettlementBill_GetExtraIncomeDetailChannelSubsidy_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailNewGuildSubsidy",
			Handler:    _SettlementBill_GetExtraIncomeDetailNewGuildSubsidy_Handler,
		},
		{
			MethodName: "GetExtraIncomeDetailDeduct",
			Handler:    _SettlementBill_GetExtraIncomeDetailDeduct_Handler,
		},
		{
			MethodName: "GetAmuseExtraIncomePrepaid",
			Handler:    _SettlementBill_GetAmuseExtraIncomePrepaid_Handler,
		},
		{
			MethodName: "GetSettlementBill",
			Handler:    _SettlementBill_GetSettlementBill_Handler,
		},
		{
			MethodName: "GetSettlementBillWaitReceipt",
			Handler:    _SettlementBill_GetSettlementBillWaitReceipt_Handler,
		},
		{
			MethodName: "GetSettlementBillWaitWithdraw",
			Handler:    _SettlementBill_GetSettlementBillWaitWithdraw_Handler,
		},
		{
			MethodName: "GetPrepaidMoney",
			Handler:    _SettlementBill_GetPrepaidMoney_Handler,
		},
		{
			MethodName: "GetDeductMoney",
			Handler:    _SettlementBill_GetDeductMoney_Handler,
		},
		{
			MethodName: "GetReceiptBillList",
			Handler:    _SettlementBill_GetReceiptBillList_Handler,
		},
		{
			MethodName: "GetReceiptList",
			Handler:    _SettlementBill_GetReceiptList_Handler,
		},
		{
			MethodName: "GetAssociatedBillItems",
			Handler:    _SettlementBill_GetAssociatedBillItems_Handler,
		},
		{
			MethodName: "RecordReceiptFile",
			Handler:    _SettlementBill_RecordReceiptFile_Handler,
		},
		{
			MethodName: "GenSettleBillPdf",
			Handler:    _SettlementBill_GenSettleBillPdf_Handler,
		},
		{
			MethodName: "GetWaitWithdrawDeepCoop",
			Handler:    _SettlementBill_GetWaitWithdrawDeepCoop_Handler,
		},
		{
			MethodName: "GetWaitWithdrawYuyinSubsidy",
			Handler:    _SettlementBill_GetWaitWithdrawYuyinSubsidy_Handler,
		},
		{
			MethodName: "GetWaitWithdrawMonths",
			Handler:    _SettlementBill_GetWaitWithdrawMonths_Handler,
		},
		{
			MethodName: "GetMonthsBySettleBillId",
			Handler:    _SettlementBill_GetMonthsBySettleBillId_Handler,
		},
		{
			MethodName: "ConfirmWithdraw",
			Handler:    _SettlementBill_ConfirmWithdraw_Handler,
		},
		{
			MethodName: "GetDeductMoneyDetailByBillId",
			Handler:    _SettlementBill_GetDeductMoneyDetailByBillId_Handler,
		},
		{
			MethodName: "GetAnchorScoreWithdrawRecords",
			Handler:    _SettlementBill_GetAnchorScoreWithdrawRecords_Handler,
		},
		{
			MethodName: "ReportConfirmDeductMoney",
			Handler:    _SettlementBill_ReportConfirmDeductMoney_Handler,
		},
		{
			MethodName: "ReportConfirmDeepCoop",
			Handler:    _SettlementBill_ReportConfirmDeepCoop_Handler,
		},
		{
			MethodName: "ReportConfirmYuyinSubsidy",
			Handler:    _SettlementBill_ReportConfirmYuyinSubsidy_Handler,
		},
		{
			MethodName: "ReportConfirmWith",
			Handler:    _SettlementBill_ReportConfirmWith_Handler,
		},
		{
			MethodName: "IsAllowWithdraw",
			Handler:    _SettlementBill_IsAllowWithdraw_Handler,
		},
		{
			MethodName: "PrivateWithdraw",
			Handler:    _SettlementBill_PrivateWithdraw_Handler,
		},
		{
			MethodName: "GetSettlementBillDetailByInstId",
			Handler:    _SettlementBill_GetSettlementBillDetailByInstId_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/settlement-bill/settlement-bill.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/settlement-bill/settlement-bill.proto", fileDescriptor_settlement_bill_b97b34d6794596b4)
}

var fileDescriptor_settlement_bill_b97b34d6794596b4 = []byte{
	// 5462 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x3c, 0x4d, 0x73, 0x1b, 0x47,
	0x76, 0x1c, 0x00, 0xfc, 0xc0, 0x03, 0x41, 0x0e, 0x9b, 0xa4, 0x08, 0x42, 0xb6, 0x25, 0x8d, 0x2c,
	0x59, 0xe6, 0xca, 0x94, 0x4d, 0x4b, 0xb6, 0xa3, 0x5d, 0x6f, 0x59, 0x9f, 0x5c, 0x96, 0x2d, 0x89,
	0x19, 0x4a, 0x96, 0x6c, 0x27, 0x41, 0x86, 0x98, 0x26, 0x39, 0xcb, 0xc1, 0xcc, 0x08, 0x3d, 0x20,
	0x89, 0x38, 0xa9, 0x54, 0xb6, 0x92, 0xad, 0x4d, 0x6d, 0x65, 0x2f, 0x9b, 0xaa, 0x3d, 0x6c, 0xe5,
	0x90, 0x4a, 0xe5, 0xb2, 0x87, 0xad, 0x4a, 0xe5, 0xb4, 0x87, 0x1c, 0xf6, 0x92, 0x5c, 0x52, 0x95,
	0x7b, 0x7e, 0x43, 0x6e, 0x39, 0xe4, 0x90, 0x4b, 0xaa, 0x5f, 0xf7, 0x60, 0x7a, 0x66, 0x1a, 0x20,
	0x68, 0x2b, 0xc9, 0x89, 0x98, 0xd7, 0xaf, 0x5f, 0x77, 0xbf, 0xf7, 0xfa, 0xf5, 0xeb, 0xf7, 0x5e,
	0x13, 0x6e, 0xc6, 0xf1, 0x8d, 0x97, 0x3d, 0xaf, 0x7d, 0xc8, 0x3c, 0xff, 0x88, 0x76, 0x6f, 0x30,
	0x1a, 0xc7, 0x3e, 0xed, 0xd0, 0x20, 0x7e, 0x67, 0xd7, 0xf3, 0xfd, 0xfc, 0xf7, 0x7a, 0xd4, 0x0d,
	0xe3, 0x90, 0xcc, 0xa7, 0xe0, 0x16, 0x07, 0x5b, 0xeb, 0x50, 0xdf, 0xf4, 0xf6, 0xe2, 0x9d, 0x76,
	0xd8, 0xa5, 0x77, 0x3d, 0xdf, 0x27, 0xaf, 0x03, 0xec, 0x7b, 0x7b, 0x71, 0x8b, 0x71, 0x48, 0xc3,
	0xb8, 0x68, 0x5c, 0xab, 0xd8, 0xd5, 0xfd, 0x04, 0xc5, 0x7a, 0x0f, 0xe6, 0xee, 0x1c, 0x3b, 0x5d,
	0x37, 0xed, 0x70, 0x01, 0x6a, 0x0e, 0x87, 0x64, 0x7a, 0x80, 0x33, 0x40, 0xb2, 0xda, 0xb0, 0x78,
	0xa7, 0xd3, 0x63, 0xf4, 0x5e, 0xd8, 0xe9, 0x78, 0x8c, 0x79, 0x61, 0x80, 0xfd, 0xde, 0x06, 0xd3,
	0xe1, 0xe0, 0x56, 0x7b, 0x00, 0x97, 0x9d, 0xe7, 0x9d, 0x2c, 0x3a, 0xb9, 0x04, 0xb3, 0x2e, 0x75,
	0x7b, 0xed, 0xb8, 0xd5, 0x09, 0x03, 0xda, 0x6f, 0x94, 0x10, 0xad, 0x26, 0x60, 0x8f, 0x38, 0xc8,
	0x8a, 0x60, 0xe5, 0x8b, 0x5e, 0xdf, 0x0b, 0xee, 0x3a, 0x85, 0x81, 0x36, 0x60, 0xb9, 0xcf, 0x9b,
	0x5a, 0xbb, 0x8e, 0x6e, 0xb4, 0xc5, 0x7e, 0xb1, 0xdf, 0x38, 0x23, 0xde, 0x82, 0xf9, 0x47, 0x0e,
	0x3b, 0xdc, 0xfe, 0x34, 0x65, 0x85, 0x05, 0xf5, 0x8e, 0xc3, 0x0e, 0x5b, 0xd1, 0x61, 0x86, 0x19,
	0x35, 0x0e, 0xdc, 0x3e, 0x14, 0xdc, 0xd8, 0x84, 0xf9, 0x47, 0x61, 0x10, 0x1f, 0x3c, 0xf2, 0x5c,
	0xd7, 0x17, 0xdd, 0x6e, 0xc2, 0x39, 0x31, 0x41, 0xc1, 0xc7, 0xc2, 0x0c, 0x97, 0xb0, 0x15, 0xd9,
	0x9e, 0x4e, 0xd1, 0xfa, 0x0a, 0x66, 0xef, 0x53, 0x1a, 0xdd, 0x0b, 0xc3, 0x08, 0xa9, 0x5c, 0x85,
	0x79, 0x97, 0xd2, 0xa8, 0xd5, 0x0e, 0xc3, 0x48, 0xce, 0x5a, 0x74, 0xaf, 0xbb, 0x12, 0x0d, 0xe7,
	0x4d, 0x2e, 0x43, 0x3d, 0xea, 0xd2, 0xc8, 0xf1, 0xdc, 0xcc, 0xda, 0x66, 0x25, 0x50, 0x2c, 0xee,
	0x6b, 0x30, 0x91, 0x9d, 0x3b, 0xbd, 0x5d, 0xe6, 0xb9, 0x7d, 0x1c, 0xe0, 0x5d, 0x58, 0x92, 0xd3,
	0x0c, 0xda, 0x07, 0x61, 0xb7, 0xc5, 0x44, 0x9b, 0x1c, 0x85, 0x88, 0x49, 0x62, 0x93, 0xec, 0x45,
	0x6e, 0xc1, 0x8a, 0xe8, 0x11, 0xd0, 0xe3, 0xd6, 0x7e, 0xcf, 0xf3, 0xdd, 0x41, 0xa7, 0x92, 0xb2,
	0xb2, 0xc7, 0xf4, 0x78, 0x93, 0x37, 0xca, 0x6e, 0xd6, 0x21, 0xd4, 0x36, 0x69, 0x40, 0xbb, 0x8e,
	0x8f, 0xe3, 0x2e, 0xc1, 0xa4, 0xba, 0x1c, 0xf1, 0x31, 0x86, 0x84, 0x8a, 0x2b, 0x2d, 0x6b, 0x56,
	0xfa, 0xdb, 0x32, 0xac, 0xdc, 0xeb, 0x52, 0x27, 0xa6, 0x3b, 0x83, 0xad, 0xc1, 0x87, 0xb5, 0xe9,
	0x4b, 0x72, 0x17, 0xe6, 0x52, 0x96, 0xf2, 0xed, 0xd2, 0x98, 0xbe, 0x68, 0x5c, 0xab, 0x6d, 0xbc,
	0xb6, 0x9e, 0xdb, 0x46, 0xeb, 0xca, 0x7c, 0xed, 0x59, 0x57, 0x15, 0xcb, 0x43, 0xa8, 0x0b, 0x1e,
	0x24, 0x2b, 0x9f, 0x41, 0x12, 0x97, 0x0a, 0x24, 0xf2, 0xfc, 0xb6, 0x67, 0xfb, 0x0a, 0x84, 0x7c,
	0x02, 0x55, 0x0e, 0x6d, 0xc5, 0xfd, 0x88, 0x36, 0x96, 0x2e, 0x1a, 0xd7, 0xe6, 0x36, 0x2e, 0x17,
	0x68, 0x64, 0x97, 0xf0, 0xb4, 0x1f, 0x51, 0x7b, 0x26, 0xf9, 0x45, 0x4c, 0x28, 0xf7, 0x3c, 0xb7,
	0x71, 0xee, 0xa2, 0x71, 0xad, 0x6e, 0xf3, 0x9f, 0x64, 0x15, 0x66, 0x84, 0x54, 0x3c, 0xb7, 0xb1,
	0x82, 0xe0, 0x69, 0xfc, 0xde, 0x72, 0x39, 0x7b, 0x05, 0xf1, 0x16, 0x8b, 0x9d, 0x6e, 0xdc, 0x68,
	0x60, 0x73, 0x4d, 0xc0, 0x76, 0x38, 0x88, 0x5b, 0x0a, 0x89, 0x42, 0x03, 0xb7, 0xb1, 0x8a, 0x08,
	0x55, 0x01, 0x79, 0x10, 0x20, 0x05, 0xdf, 0x61, 0x71, 0x6b, 0xd7, 0xf1, 0x9d, 0xa0, 0x4d, 0x1b,
	0x4d, 0x21, 0x20, 0x0e, 0xbb, 0x2b, 0x40, 0x45, 0x01, 0x9d, 0x2f, 0x0a, 0x48, 0x99, 0x89, 0xc0,
	0x79, 0x21, 0xe8, 0x08, 0x98, 0x90, 0xe1, 0x36, 0x34, 0xf4, 0x22, 0x64, 0x11, 0x59, 0x81, 0x69,
	0xe4, 0x9b, 0xe7, 0xa2, 0xfe, 0x54, 0xed, 0x29, 0xfe, 0xb9, 0xe5, 0x92, 0xf3, 0x50, 0xf5, 0x58,
	0xab, 0x43, 0xbb, 0xfb, 0xd4, 0x45, 0xed, 0x99, 0xb1, 0x67, 0x3c, 0xf6, 0x08, 0xbf, 0xad, 0xff,
	0x9a, 0x84, 0xa5, 0x2c, 0xb1, 0xfb, 0x34, 0x76, 0x3c, 0x9f, 0x93, 0xdb, 0xcd, 0x92, 0xdb, 0x15,
	0xe4, 0x3e, 0x84, 0x0a, 0x8a, 0xa6, 0x34, 0xbe, 0x68, 0xb0, 0x43, 0x22, 0x96, 0x72, 0x2a, 0x96,
	0x8f, 0x61, 0x8a, 0xc5, 0x4e, 0xdc, 0x63, 0x8d, 0x0a, 0x12, 0xbb, 0x72, 0x0a, 0xb1, 0x1d, 0x44,
	0xb6, 0x65, 0x27, 0x2e, 0xd5, 0xd8, 0x39, 0x69, 0x75, 0x9d, 0x98, 0x36, 0x26, 0x85, 0x54, 0x63,
	0xe7, 0xc4, 0x76, 0x62, 0xca, 0x45, 0xe6, 0x05, 0xed, 0xb0, 0x43, 0x5b, 0xac, 0xd7, 0x69, 0x4c,
	0x09, 0xe3, 0x2e, 0x20, 0x3b, 0xbd, 0x0e, 0x59, 0x83, 0x05, 0xa7, 0x1d, 0xf7, 0x1c, 0xbf, 0x25,
	0xb1, 0x22, 0xa7, 0x8f, 0x2a, 0xcf, 0x6d, 0x32, 0x36, 0x6c, 0x21, 0x7c, 0xdb, 0xe9, 0x73, 0xb3,
	0xdf, 0x46, 0x9e, 0xb7, 0x62, 0xaf, 0x43, 0x51, 0xab, 0xeb, 0x36, 0x08, 0xd0, 0x53, 0xaf, 0x83,
	0xc2, 0xdd, 0xf3, 0x02, 0x8f, 0x1d, 0x50, 0x57, 0xa0, 0x54, 0x11, 0x65, 0x36, 0x01, 0x22, 0xd2,
	0x39, 0x98, 0xda, 0xf3, 0x7c, 0xba, 0xe5, 0x36, 0x6a, 0x82, 0x9b, 0xe2, 0x8b, 0x34, 0x60, 0x9a,
	0xff, 0x7a, 0xd6, 0xf5, 0x1b, 0xb3, 0xd8, 0x90, 0x7c, 0x16, 0x14, 0x73, 0xee, 0x34, 0xc5, 0x9c,
	0xcf, 0x2b, 0xe6, 0x05, 0x90, 0xd8, 0x2d, 0x97, 0xb3, 0xc8, 0x14, 0x33, 0x17, 0xa0, 0xfb, 0x9c,
	0x4b, 0x57, 0x60, 0xce, 0xf1, 0xfd, 0xf0, 0xb8, 0x75, 0xec, 0xc5, 0x07, 0x6e, 0xd7, 0x39, 0x6e,
	0x2c, 0xa0, 0x7a, 0xd4, 0x11, 0xfa, 0x5c, 0x02, 0xc9, 0x0f, 0x60, 0x61, 0x5f, 0x6c, 0x7b, 0x14,
	0x0a, 0xa7, 0xe6, 0x34, 0xe8, 0x18, 0x06, 0x62, 0x7e, 0x3f, 0xfd, 0xb8, 0xef, 0xc4, 0x8e, 0xc6,
	0xce, 0x1c, 0x7e, 0x7b, 0x3b, 0xe3, 0x7f, 0x23, 0x3b, 0x63, 0x5d, 0x05, 0xb2, 0x49, 0x63, 0xb4,
	0xc7, 0x4f, 0x85, 0xd6, 0x70, 0x4b, 0x28, 0x95, 0xd4, 0x18, 0x28, 0xa9, 0xf5, 0x04, 0x16, 0x0b,
	0x78, 0x2c, 0xca, 0x28, 0x9f, 0x51, 0x50, 0x3e, 0x14, 0x99, 0xd0, 0x86, 0x92, 0x14, 0x0b, 0x87,
	0x70, 0x55, 0xb0, 0xfe, 0xd1, 0x80, 0x69, 0x49, 0x69, 0x14, 0x15, 0x39, 0x93, 0x52, 0xba, 0x5d,
	0xb2, 0x74, 0xcb, 0x39, 0xba, 0x79, 0x45, 0xad, 0x14, 0x14, 0xf5, 0x02, 0xd4, 0x7a, 0x91, 0x3b,
	0x40, 0x10, 0x5b, 0x06, 0x04, 0x08, 0x11, 0x9a, 0x30, 0x13, 0x46, 0xb4, 0xeb, 0xc4, 0x61, 0x17,
	0xf7, 0x4c, 0xd5, 0x1e, 0x7c, 0x5b, 0x3e, 0x98, 0x36, 0x6d, 0x87, 0x5d, 0x95, 0x59, 0x2a, 0xbe,
	0x91, 0xc5, 0x27, 0xdf, 0x83, 0x7a, 0xb2, 0xb2, 0x96, 0xef, 0xb1, 0xb8, 0x51, 0xb9, 0x58, 0xbe,
	0x56, 0xdb, 0x68, 0x14, 0xc4, 0x94, 0xd0, 0xab, 0xc9, 0x85, 0x7f, 0xe6, 0xb1, 0xd8, 0x5a, 0x84,
	0x85, 0xdc, 0x68, 0x2c, 0xb2, 0x8e, 0xa1, 0xbe, 0x43, 0xe3, 0x51, 0xc2, 0xca, 0xf0, 0xb3, 0x34,
	0x4a, 0x2a, 0x05, 0xee, 0xa9, 0x6b, 0xa9, 0xe4, 0xd6, 0x6e, 0xc2, 0x9c, 0x3a, 0x30, 0x8b, 0xac,
	0x7f, 0x35, 0x60, 0xf1, 0x0e, 0x63, 0x61, 0xdb, 0x43, 0x48, 0x9b, 0x7a, 0x51, 0xac, 0x9f, 0xd1,
	0x05, 0xa8, 0x75, 0x45, 0x7b, 0xcb, 0x73, 0x59, 0xa3, 0x74, 0xb1, 0x7c, 0xad, 0x6a, 0x83, 0x04,
	0x6d, 0xb9, 0x8c, 0xac, 0xc3, 0x62, 0x8e, 0x25, 0x88, 0x58, 0x46, 0xc4, 0x05, 0x96, 0x31, 0x80,
	0x1c, 0x7f, 0x0d, 0x16, 0xe8, 0x49, 0xd4, 0xa5, 0x8c, 0xb5, 0xc2, 0xae, 0x4b, 0xbb, 0x88, 0x5d,
	0x41, 0xec, 0x79, 0xd9, 0xf0, 0x84, 0xc3, 0x39, 0xee, 0x55, 0x98, 0x4f, 0x06, 0x4f, 0x8c, 0xf9,
	0x24, 0xae, 0xad, 0x2e, 0xc1, 0x82, 0xa8, 0xf5, 0x7d, 0x58, 0x2a, 0xae, 0x86, 0x45, 0xba, 0xfe,
	0x86, 0xae, 0xff, 0xdf, 0x94, 0xe1, 0xb5, 0xc4, 0x47, 0xe3, 0x3c, 0xf3, 0xc2, 0xe0, 0xde, 0x81,
	0x13, 0x04, 0x54, 0xda, 0x51, 0x4e, 0x68, 0x8f, 0x1b, 0x9a, 0xf8, 0x80, 0x1f, 0x45, 0xdc, 0x2d,
	0x4c, 0x7c, 0x36, 0x0e, 0x7e, 0x7a, 0xe0, 0x31, 0xf4, 0x15, 0x07, 0x78, 0x78, 0xa0, 0x0a, 0xbc,
	0x52, 0x8a, 0xf7, 0x99, 0xc3, 0x62, 0x81, 0x77, 0x1e, 0xaa, 0xfb, 0xdd, 0xf0, 0x58, 0x08, 0x9a,
	0xcb, 0x72, 0xd2, 0x9e, 0xe1, 0x00, 0x94, 0xf4, 0x25, 0x98, 0x6d, 0x8b, 0xd1, 0x5b, 0x81, 0x23,
	0x77, 0x42, 0xd5, 0xae, 0x49, 0xd8, 0x63, 0xa7, 0x83, 0xca, 0x20, 0x1c, 0x02, 0x44, 0x10, 0x3c,
	0xa9, 0x22, 0x04, 0x9b, 0xaf, 0x81, 0x29, 0x9a, 0x5d, 0x8f, 0x45, 0xbe, 0xd3, 0xe7, 0x0b, 0x9f,
	0x42, 0x99, 0xce, 0x21, 0xfc, 0xbe, 0x00, 0x0b, 0xf7, 0x21, 0x19, 0x0b, 0x4f, 0xc5, 0xe9, 0xcc,
	0x58, 0xe8, 0x8e, 0xbc, 0x05, 0xea, 0x65, 0x04, 0x67, 0x2c, 0x0e, 0x91, 0xb9, 0x14, 0x8c, 0xf3,
	0x7e, 0x1b, 0x4c, 0x05, 0x51, 0x38, 0x01, 0x55, 0x71, 0x28, 0xa5, 0x70, 0xe1, 0x2b, 0xcc, 0x41,
	0x69, 0xeb, 0x7e, 0x03, 0x90, 0x4c, 0x69, 0xeb, 0x3e, 0x5f, 0x4f, 0x32, 0x0d, 0x4f, 0x1c, 0x31,
	0x75, 0xbb, 0x2a, 0x21, 0x5b, 0xae, 0xf5, 0x8b, 0x32, 0x2c, 0xe7, 0xe4, 0x23, 0x05, 0x53, 0x54,
	0xd8, 0xf7, 0x60, 0x39, 0x0e, 0x63, 0xc7, 0x6f, 0xe5, 0x05, 0x26, 0x04, 0x41, 0xb0, 0xf1, 0x61,
	0x46, 0x6a, 0xd9, 0x2e, 0x8a, 0xec, 0xca, 0xb9, 0x2e, 0x43, 0x04, 0x58, 0xc9, 0x09, 0x50, 0xc7,
	0x88, 0x49, 0x3d, 0x23, 0x0a, 0x9e, 0xd5, 0x94, 0xc6, 0xb3, 0xfa, 0x00, 0x56, 0xe4, 0x71, 0x5f,
	0x20, 0x2b, 0x0e, 0xfd, 0x65, 0xd1, 0xbc, 0x93, 0x23, 0x7e, 0x0e, 0xa6, 0xba, 0xb4, 0xe3, 0x74,
	0x0f, 0x51, 0x60, 0x55, 0x5b, 0x7e, 0x91, 0x2d, 0x98, 0x91, 0xbc, 0x65, 0x8d, 0x2a, 0x9a, 0xb5,
	0x77, 0x0a, 0x66, 0x6d, 0xd4, 0x76, 0xb0, 0x07, 0xdd, 0xad, 0x5f, 0x97, 0x60, 0x31, 0x73, 0x97,
	0x90, 0x72, 0x21, 0x50, 0x89, 0xe3, 0x81, 0x60, 0xf0, 0x37, 0x37, 0x51, 0x81, 0xd7, 0x3e, 0x44,
	0x95, 0x2d, 0x09, 0x13, 0x95, 0x7c, 0x73, 0x39, 0xc6, 0xce, 0x3e, 0x32, 0xbc, 0x6a, 0xf3, 0x9f,
	0x78, 0x5d, 0x15, 0xf7, 0x97, 0x3d, 0xdf, 0xd9, 0x97, 0x9b, 0x00, 0x04, 0xe8, 0xa1, 0xef, 0xec,
	0x9f, 0xb6, 0x07, 0xce, 0x43, 0x35, 0xf2, 0x9d, 0x40, 0xf8, 0x0e, 0xf2, 0x34, 0xe0, 0x00, 0xf4,
	0x1c, 0xb8, 0xf8, 0xf8, 0xe5, 0x99, 0x0b, 0x5c, 0xf2, 0x70, 0x86, 0x03, 0xb8, 0x90, 0xf9, 0x26,
	0x3e, 0x72, 0x7c, 0xcf, 0x6d, 0xf9, 0xde, 0x11, 0xf7, 0x3d, 0xfa, 0x4c, 0x2a, 0x7c, 0x1d, 0xc1,
	0x9f, 0x79, 0x47, 0xf4, 0xbe, 0xd3, 0x67, 0x5c, 0x76, 0xf2, 0x0c, 0xcf, 0x28, 0xfb, 0xac, 0x04,
	0x6a, 0x35, 0xdd, 0xfa, 0x2b, 0x03, 0x1a, 0x5f, 0x14, 0x6e, 0x60, 0x43, 0xb5, 0x99, 0xfb, 0x40,
	0x72, 0x0c, 0xee, 0x09, 0x0a, 0x1d, 0x06, 0x09, 0xe2, 0xae, 0xe0, 0xf7, 0x61, 0x5a, 0xf0, 0x44,
	0x98, 0xdc, 0xda, 0xc6, 0x9b, 0x05, 0x51, 0x6a, 0x46, 0xb2, 0x93, 0x4e, 0xd6, 0xbf, 0x1b, 0xd0,
	0xfc, 0x42, 0x73, 0xb9, 0x1b, 0x3a, 0xa3, 0x2c, 0xdb, 0x4b, 0x1a, 0xb6, 0xff, 0x30, 0xf4, 0x82,
	0xf4, 0x94, 0xaa, 0xda, 0x33, 0x1c, 0x80, 0x87, 0x94, 0xc6, 0x8c, 0x56, 0x74, 0x66, 0x94, 0xfb,
	0x8e, 0x72, 0xd5, 0x6e, 0xe2, 0x1d, 0x57, 0xed, 0x84, 0x13, 0x28, 0xc1, 0x02, 0xf3, 0xa7, 0x8a,
	0xcc, 0xb7, 0x7e, 0x6b, 0x40, 0xed, 0xbe, 0x72, 0xd1, 0x2c, 0xae, 0xe6, 0x3a, 0x10, 0x11, 0xdc,
	0xd0, 0xdc, 0x51, 0x45, 0xd8, 0x43, 0xed, 0x7f, 0x09, 0x66, 0x05, 0xb6, 0xdc, 0x56, 0x62, 0x7d,
	0x35, 0x84, 0xd9, 0x62, 0x6f, 0x5d, 0x07, 0x71, 0xc1, 0xce, 0x12, 0x14, 0xab, 0x34, 0xb1, 0x25,
	0x47, 0x50, 0x60, 0x4b, 0x82, 0x72, 0xa1, 0x08, 0x13, 0x04, 0xad, 0x03, 0x58, 0xc1, 0xa8, 0xcc,
	0x83, 0x93, 0xb8, 0xeb, 0x6c, 0x0b, 0xb3, 0x30, 0x54, 0x38, 0xe3, 0xc4, 0x0c, 0x14, 0xb3, 0x50,
	0x56, 0xcd, 0x82, 0xf5, 0x9b, 0x49, 0x58, 0x12, 0x5e, 0x0b, 0x8e, 0x25, 0x35, 0x85, 0xbe, 0xcc,
	0x9d, 0x00, 0x6e, 0xea, 0x9c, 0x28, 0x27, 0x00, 0x0a, 0xe5, 0x0e, 0xd4, 0xe4, 0x85, 0x04, 0x0f,
	0x93, 0x32, 0xde, 0x8a, 0x2e, 0x16, 0x14, 0x52, 0x21, 0x8f, 0xf7, 0x2b, 0x79, 0xd7, 0xc1, 0xd3,
	0x66, 0x84, 0x1f, 0xc3, 0x75, 0x8b, 0xdf, 0x2e, 0xd4, 0x0d, 0x3f, 0xc3, 0x01, 0xa8, 0x78, 0xab,
	0x80, 0xbf, 0x5b, 0x1d, 0xf7, 0x96, 0xdc, 0xee, 0x78, 0x15, 0x79, 0xe4, 0xde, 0x1a, 0x34, 0xf5,
	0xba, 0xbe, 0x3c, 0xe0, 0x06, 0xb7, 0x94, 0x2f, 0x61, 0x79, 0xe0, 0xd1, 0x4b, 0x53, 0x27, 0xdc,
	0x3d, 0xc0, 0xcd, 0x74, 0xf5, 0x34, 0xbb, 0x28, 0x99, 0xb4, 0xe8, 0x66, 0xc1, 0xdc, 0x09, 0x24,
	0x2e, 0xac, 0xea, 0xe2, 0x30, 0x82, 0x7e, 0x0d, 0xe9, 0xbf, 0xad, 0xf7, 0xfa, 0x75, 0x3b, 0xf6,
	0x5c, 0x31, 0x6e, 0x83, 0xa3, 0x1c, 0xc2, 0x6b, 0x43, 0x62, 0x37, 0x62, 0xa0, 0x59, 0x1c, 0xe8,
	0x3b, 0xfa, 0x81, 0xb4, 0x9b, 0xde, 0x6e, 0xe8, 0xa2, 0x3d, 0x38, 0xd8, 0xc7, 0x20, 0x03, 0x37,
	0x82, 0x76, 0x1d, 0x69, 0xbf, 0xa6, 0x61, 0xd2, 0x40, 0xc5, 0x6d, 0x10, 0x1d, 0xb0, 0xfb, 0x0b,
	0x58, 0x14, 0xfb, 0x87, 0x72, 0x0d, 0x68, 0x49, 0xad, 0x6c, 0xcc, 0x21, 0x99, 0x6b, 0x45, 0xc3,
	0xa5, 0xd7, 0x7b, 0x7b, 0xc1, 0xc9, 0x37, 0x58, 0x2b, 0xb0, 0xac, 0x51, 0x5d, 0x16, 0x59, 0x3b,
	0xb0, 0xb0, 0x39, 0xf0, 0x7d, 0xf9, 0x24, 0xf4, 0x6e, 0xee, 0x39, 0x98, 0x0a, 0xf7, 0xf6, 0x18,
	0x8d, 0xa5, 0x66, 0xcb, 0x2f, 0xb2, 0x04, 0x93, 0xbe, 0xd7, 0xf1, 0x62, 0xe9, 0x70, 0x8b, 0x0f,
	0xeb, 0x05, 0xde, 0xbd, 0x32, 0x44, 0x59, 0x44, 0xae, 0x43, 0x05, 0xb9, 0x62, 0x9c, 0x72, 0x53,
	0x40, 0x2c, 0x4e, 0x19, 0xfd, 0x0a, 0x39, 0xa0, 0xf8, 0xb0, 0x6e, 0x00, 0xb9, 0xeb, 0xc4, 0xed,
	0x83, 0xcd, 0xcc, 0x45, 0x61, 0x15, 0x66, 0x7a, 0x78, 0x1e, 0x49, 0xea, 0x75, 0x7b, 0xba, 0xc7,
	0x0f, 0x22, 0x16, 0x5b, 0xff, 0x6c, 0xc0, 0x62, 0xa1, 0x07, 0x8b, 0xc8, 0xe7, 0x30, 0x3b, 0xb8,
	0xbf, 0x74, 0x9c, 0x48, 0x4e, 0xea, 0x66, 0x61, 0x52, 0x9a, 0xbe, 0xc9, 0x44, 0x1f, 0x39, 0xd1,
	0x83, 0x20, 0xee, 0xf6, 0x6d, 0x88, 0x07, 0x80, 0xe6, 0x73, 0x98, 0xcf, 0x35, 0x73, 0x6e, 0x1e,
	0xca, 0xa8, 0x5f, 0xdd, 0xe6, 0x3f, 0xc9, 0x3a, 0x4c, 0x1e, 0x39, 0x7e, 0x4f, 0x98, 0x89, 0x51,
	0xac, 0x10, 0x68, 0xb7, 0x4b, 0x1f, 0x19, 0xd6, 0xef, 0xc3, 0xf9, 0x4d, 0x1a, 0x67, 0xc4, 0xc7,
	0xe5, 0xf9, 0xaa, 0x44, 0xf6, 0x93, 0x12, 0x2c, 0x17, 0x88, 0x6f, 0xc5, 0xb4, 0x23, 0x4f, 0x68,
	0x63, 0xe0, 0x8b, 0xaa, 0x16, 0xa8, 0x94, 0xb3, 0x40, 0xb9, 0x3b, 0x6a, 0xb9, 0x70, 0x47, 0xcd,
	0x59, 0xc0, 0xca, 0x37, 0xb0, 0x80, 0x1a, 0x6b, 0x3b, 0xa9, 0xb5, 0xb6, 0x19, 0x73, 0x38, 0x35,
	0xc4, 0x1c, 0x16, 0x6d, 0x9e, 0x15, 0xc1, 0x6b, 0xc3, 0x39, 0xcd, 0x22, 0x72, 0x3b, 0xa3, 0xc7,
	0x57, 0x47, 0x4d, 0x3e, 0x65, 0xe3, 0x48, 0xad, 0xfe, 0x91, 0x01, 0x2b, 0xd9, 0x21, 0x45, 0x94,
	0xee, 0x15, 0x08, 0x56, 0xc7, 0xae, 0x8a, 0x8e, 0x5d, 0xd6, 0x4f, 0x0d, 0xb8, 0xa8, 0x9b, 0x44,
	0x62, 0xd1, 0x71, 0xed, 0x83, 0xf9, 0x1b, 0xca, 0xfc, 0xc9, 0xef, 0x82, 0x99, 0x3f, 0x25, 0x90,
	0xe1, 0xe3, 0x1f, 0x10, 0xf3, 0xb9, 0x03, 0xc2, 0xfa, 0xa5, 0x01, 0x57, 0x75, 0xb3, 0x91, 0x8e,
	0xb6, 0x34, 0xba, 0x23, 0xe6, 0xf4, 0xd5, 0x90, 0x28, 0xbf, 0x88, 0x7c, 0x9f, 0xe1, 0x60, 0xd1,
	0x24, 0x04, 0xac, 0xbf, 0x33, 0xe0, 0x2d, 0xdd, 0xec, 0x72, 0x67, 0xc2, 0x88, 0xe9, 0xed, 0x0e,
	0x4f, 0x29, 0x88, 0xc0, 0xfa, 0x99, 0x4e, 0x24, 0x7d, 0xfe, 0xe1, 0x0e, 0x2c, 0x6d, 0xd2, 0xb8,
	0x98, 0x0e, 0x28, 0xaa, 0x94, 0x12, 0x0d, 0x2e, 0xa9, 0xd1, 0x60, 0xcb, 0x86, 0x65, 0x0d, 0x09,
	0x16, 0x91, 0xdf, 0x81, 0x0a, 0x06, 0xf8, 0x0c, 0x9c, 0xec, 0x69, 0x91, 0x5d, 0xa9, 0xce, 0xd8,
	0xc5, 0xfa, 0x0a, 0x2e, 0x14, 0x68, 0x3e, 0x77, 0xbc, 0x78, 0x64, 0x9c, 0x45, 0x13, 0xaa, 0x28,
	0xe9, 0x42, 0x15, 0x3f, 0x16, 0x5a, 0x3c, 0x82, 0x3a, 0x8b, 0xc8, 0x77, 0x61, 0x92, 0x13, 0x61,
	0x72, 0x0b, 0x8f, 0x39, 0x7b, 0xd1, 0x87, 0x5c, 0x81, 0x39, 0xd7, 0x63, 0xce, 0xae, 0x4f, 0x5b,
	0x47, 0xb4, 0xeb, 0xed, 0xf5, 0x65, 0xd0, 0xbd, 0x2e, 0xa1, 0x9f, 0x23, 0x50, 0xc6, 0x1f, 0xb7,
	0x15, 0xc7, 0x52, 0x1f, 0x7f, 0xbc, 0x8d, 0xf1, 0xc7, 0x2c, 0x1e, 0x8b, 0x8a, 0x9e, 0xaa, 0xa1,
	0xc9, 0xf9, 0x5c, 0xc1, 0xc3, 0x5b, 0xf5, 0x26, 0xb4, 0x43, 0x44, 0x38, 0x95, 0x0c, 0x1a, 0x1e,
	0xc7, 0x3a, 0xd7, 0xde, 0x18, 0xe2, 0xda, 0xeb, 0xfd, 0xf6, 0x92, 0xde, 0x6f, 0xb7, 0x7e, 0x6e,
	0xa0, 0xde, 0xd8, 0xa9, 0x68, 0x5e, 0xd1, 0x39, 0x45, 0x6e, 0x2b, 0x39, 0x85, 0xf2, 0xb5, 0xb9,
	0x0d, 0xab, 0x20, 0xbb, 0x27, 0x77, 0xee, 0xf4, 0x5c, 0x4f, 0x93, 0x50, 0xb0, 0xfe, 0xbe, 0x04,
	0xe7, 0x74, 0xb3, 0x62, 0x11, 0x69, 0xc1, 0x42, 0x46, 0xbd, 0x14, 0x03, 0xff, 0xbe, 0x26, 0x78,
	0xad, 0xa3, 0xb1, 0xae, 0x28, 0xa2, 0x3d, 0xdf, 0xcd, 0x22, 0xe8, 0x0d, 0x7f, 0xf3, 0xe7, 0xc6,
	0x20, 0x7c, 0x98, 0xe4, 0x3e, 0xc7, 0x09, 0xc8, 0x71, 0x9e, 0x49, 0x2e, 0x48, 0x9e, 0xc9, 0x94,
	0xc9, 0x75, 0x20, 0xc5, 0x60, 0xa3, 0x8c, 0x35, 0x9a, 0xf9, 0x58, 0xa3, 0x72, 0xd1, 0xa9, 0x64,
	0x2e, 0x3a, 0xdf, 0x45, 0xb5, 0x92, 0x4b, 0x4c, 0x04, 0x37, 0x6e, 0xac, 0xf0, 0x3f, 0x0d, 0xd4,
	0xb6, 0x4c, 0x6f, 0x16, 0x91, 0x4f, 0x61, 0x46, 0xe2, 0x25, 0xbb, 0xee, 0xc6, 0x08, 0xbe, 0x0e,
	0x78, 0x2a, 0xbf, 0xf1, 0x04, 0x1d, 0x10, 0xd0, 0xc7, 0x48, 0x4b, 0xda, 0x18, 0x69, 0xd3, 0x85,
	0x9a, 0x42, 0x84, 0x5f, 0xcf, 0xd3, 0x78, 0xad, 0x5c, 0x41, 0x75, 0x10, 0xae, 0xcd, 0xfa, 0x0c,
	0xa5, 0x11, 0x3e, 0x43, 0x39, 0xeb, 0x33, 0x3c, 0x83, 0xd5, 0x4d, 0x1a, 0x0f, 0x82, 0xac, 0x2e,
	0x32, 0x23, 0xa6, 0x1d, 0xf6, 0xed, 0xac, 0xd9, 0x73, 0x68, 0x0e, 0x23, 0x2b, 0x6c, 0xb0, 0xa2,
	0xa7, 0xe3, 0xda, 0x60, 0xde, 0xc5, 0xfa, 0xef, 0x4a, 0x72, 0x97, 0x95, 0xcc, 0x79, 0xe8, 0xf9,
	0xe8, 0x4a, 0x7f, 0x1b, 0xfe, 0x10, 0xa8, 0x30, 0xef, 0x8f, 0xa8, 0x0c, 0x0b, 0xe2, 0x6f, 0xde,
	0x81, 0x1d, 0x84, 0xc7, 0x2d, 0x6c, 0x90, 0x17, 0x56, 0x0e, 0xd8, 0xe1, 0x8d, 0xaf, 0x03, 0x20,
	0xb5, 0xb6, 0xef, 0x30, 0x96, 0x84, 0xa8, 0x38, 0xe4, 0x1e, 0x07, 0x24, 0x7c, 0x9b, 0x4a, 0xf9,
	0x76, 0x09, 0x66, 0x93, 0xd9, 0xa1, 0x27, 0x23, 0xc3, 0xb1, 0x12, 0x76, 0x5f, 0x46, 0x87, 0x13,
	0x94, 0x76, 0xe8, 0x52, 0x19, 0xda, 0x4b, 0x50, 0xee, 0x85, 0x2e, 0x55, 0xd7, 0x18, 0x84, 0x18,
	0x95, 0x4a, 0xd7, 0xf8, 0x38, 0xcc, 0x24, 0x19, 0x40, 0x88, 0x59, 0x49, 0x32, 0xf0, 0x26, 0xa7,
	0x13, 0xf6, 0x82, 0x18, 0xe3, 0xb0, 0x15, 0xbb, 0x1a, 0x3b, 0x27, 0x77, 0x10, 0xc0, 0x37, 0x94,
	0x6c, 0x9a, 0xc5, 0x26, 0xf9, 0x45, 0x2c, 0xa8, 0xd3, 0x93, 0x96, 0xd2, 0xb3, 0x2e, 0x72, 0xbf,
	0xf4, 0xe4, 0xe9, 0xa0, 0xef, 0x32, 0x4c, 0x1d, 0x89, 0x19, 0xcf, 0xe1, 0x98, 0x93, 0x47, 0x38,
	0xd7, 0x06, 0x4c, 0xb7, 0xc3, 0x20, 0xa6, 0x41, 0x8c, 0x09, 0xc0, 0xaa, 0x9d, 0x7c, 0x8a, 0xf4,
	0x9f, 0xef, 0xd3, 0xae, 0x10, 0x86, 0x29, 0x02, 0x80, 0x02, 0x84, 0xe2, 0xb0, 0xa0, 0x2e, 0x11,
	0xf8, 0xc8, 0x41, 0x88, 0xd9, 0xbf, 0xaa, 0x2d, 0x7b, 0x3d, 0x75, 0x4e, 0x1e, 0x87, 0xfc, 0x30,
	0x8b, 0x7a, 0xdd, 0xf6, 0x81, 0xc3, 0x12, 0x3a, 0x44, 0xe8, 0xe1, 0x00, 0x9a, 0x04, 0xcc, 0x53,
	0x34, 0x49, 0x6d, 0x11, 0x11, 0xd3, 0xee, 0x82, 0xe0, 0x65, 0xa8, 0x8b, 0x53, 0xb1, 0xd5, 0xa5,
	0xac, 0xe7, 0xc7, 0x98, 0xe2, 0xaf, 0xda, 0xb3, 0x02, 0x68, 0x23, 0xcc, 0xfa, 0x6b, 0x23, 0xb9,
	0x8e, 0x66, 0xb4, 0x8f, 0x45, 0xaa, 0xf4, 0x98, 0xb7, 0x1f, 0x48, 0x05, 0x4c, 0xa4, 0xb7, 0xe3,
	0xed, 0x07, 0xe4, 0x13, 0x00, 0xda, 0xed, 0x86, 0x5d, 0x61, 0xa3, 0x4b, 0xa8, 0xfb, 0xc5, 0xec,
	0xa0, 0x24, 0xfc, 0x80, 0x63, 0x6e, 0x05, 0x7b, 0xa1, 0x5d, 0xc5, 0x4e, 0x68, 0x8b, 0xd1, 0xee,
	0xe1, 0xe4, 0xca, 0x78, 0x72, 0xcb, 0x2f, 0x6b, 0x9d, 0x1f, 0xc5, 0x81, 0xd8, 0x35, 0x7c, 0xc7,
	0x6c, 0xbb, 0x7b, 0x7c, 0x4b, 0x0c, 0x4b, 0x95, 0x5b, 0x37, 0xb8, 0x7f, 0x95, 0xc7, 0x17, 0xa9,
	0x7a, 0x54, 0xeb, 0xb4, 0x83, 0xc8, 0x06, 0x5b, 0xeb, 0xb8, 0x9d, 0xb9, 0x37, 0x92, 0x24, 0x5f,
	0x53, 0xe7, 0x5a, 0x77, 0x70, 0xff, 0x8b, 0x81, 0x97, 0x3e, 0x7d, 0x87, 0x53, 0x93, 0x94, 0x4a,
	0x86, 0xbc, 0x34, 0x56, 0x86, 0xbc, 0xac, 0xcf, 0x90, 0x17, 0xb3, 0xba, 0xf3, 0x67, 0xcd, 0xea,
	0x5a, 0x1b, 0xf0, 0x46, 0x6e, 0x21, 0x6a, 0xfa, 0x56, 0xbf, 0xfa, 0x7f, 0x33, 0xd0, 0x51, 0x1c,
	0xde, 0xe9, 0xff, 0x8c, 0x03, 0x85, 0x9c, 0xb4, 0xf9, 0xcd, 0x72, 0xd2, 0x01, 0x34, 0x72, 0x0b,
	0xc2, 0x78, 0xee, 0x90, 0x43, 0xe2, 0x13, 0xa8, 0xee, 0x0e, 0x2a, 0x65, 0xce, 0x50, 0x8e, 0x31,
	0xb3, 0x2b, 0x7f, 0x59, 0xef, 0xe3, 0xa9, 0xa4, 0x1b, 0x8f, 0x45, 0x7c, 0x17, 0x60, 0x88, 0x99,
	0xc9, 0x90, 0x89, 0xfc, 0xb2, 0x1e, 0xa2, 0x92, 0x0a, 0xc4, 0xbb, 0xfd, 0x54, 0xbb, 0xb7, 0xdc,
	0xa1, 0xfe, 0x9b, 0xd8, 0x0f, 0xb9, 0xab, 0xc3, 0x2d, 0xd4, 0x5d, 0x3d, 0x9d, 0x11, 0xc3, 0xff,
	0x6d, 0x09, 0x55, 0x45, 0x73, 0x0d, 0xe5, 0xee, 0x25, 0x76, 0x7d, 0x9c, 0x39, 0xf7, 0x6e, 0xeb,
	0xfc, 0x88, 0x11, 0xdd, 0x65, 0xf4, 0x2d, 0xbd, 0x94, 0x37, 0x7f, 0x6d, 0x00, 0xa4, 0xc0, 0x33,
	0x3a, 0xc6, 0xf9, 0x98, 0x77, 0x69, 0xdc, 0x98, 0x77, 0x79, 0xcc, 0x98, 0x77, 0xa5, 0x18, 0xf3,
	0xde, 0x06, 0x72, 0x2f, 0x0c, 0xf6, 0xbc, 0x6e, 0x27, 0x91, 0xeb, 0x28, 0x3b, 0x85, 0x53, 0xf4,
	0x8f, 0x9d, 0x3e, 0x6b, 0x61, 0xe1, 0x87, 0xbc, 0xaf, 0xd4, 0x04, 0xec, 0x0e, 0x07, 0x59, 0xcf,
	0x60, 0xb1, 0x40, 0x51, 0x58, 0x32, 0x3d, 0xc9, 0x2b, 0x30, 0x97, 0x14, 0x95, 0x64, 0xae, 0x02,
	0xf5, 0xe3, 0x54, 0xd1, 0x68, 0xdf, 0xba, 0x39, 0xe4, 0x32, 0xa6, 0x4e, 0x5b, 0xb3, 0xf1, 0x4b,
	0x70, 0xe9, 0x94, 0x6e, 0xdf, 0xca, 0xfb, 0x21, 0xef, 0xc1, 0x52, 0xec, 0x9c, 0xdc, 0x0b, 0x3b,
	0x11, 0x0d, 0x98, 0x08, 0x6a, 0x2b, 0x25, 0x05, 0x8b, 0xb9, 0x36, 0xb4, 0x26, 0xb7, 0x61, 0x55,
	0xd7, 0xa5, 0x15, 0xd3, 0x93, 0x58, 0x3a, 0x83, 0x2b, 0x9a, 0x7e, 0x4f, 0xe9, 0x49, 0x4c, 0xde,
	0x01, 0xd2, 0x56, 0x3b, 0x46, 0xa1, 0x17, 0xc4, 0x32, 0x0a, 0xb3, 0xa0, 0xb6, 0x6c, 0xf3, 0x06,
	0xf2, 0x01, 0xac, 0x14, 0xd1, 0xc5, 0x40, 0xc2, 0x45, 0x5a, 0x2e, 0xf4, 0xc1, 0x61, 0x54, 0x5b,
	0x38, 0x95, 0xb1, 0x85, 0xd6, 0x47, 0xf0, 0x7a, 0xf6, 0x06, 0xc8, 0xcf, 0xc1, 0xbb, 0xfd, 0x74,
	0x5b, 0x0f, 0x3d, 0xe3, 0x8e, 0x61, 0x41, 0xe9, 0x26, 0x8b, 0xc7, 0xf2, 0x35, 0x8b, 0x46, 0xb1,
	0x66, 0x31, 0xbd, 0x5b, 0x94, 0x32, 0xb9, 0x55, 0x4d, 0x38, 0xaa, 0xac, 0x0d, 0x47, 0xbd, 0x40,
	0x33, 0x30, 0x74, 0xca, 0x2c, 0x22, 0x1f, 0x64, 0x14, 0xc0, 0x1a, 0x15, 0x65, 0xcf, 0xf8, 0xbe,
	0x27, 0x60, 0xda, 0x34, 0x0a, 0xbb, 0xb1, 0xd4, 0x78, 0xe9, 0xf6, 0x2a, 0xd5, 0x23, 0xb2, 0x5a,
	0x38, 0xad, 0x1e, 0x59, 0x85, 0x19, 0x1a, 0xb8, 0x69, 0xc1, 0x4f, 0xc5, 0x9e, 0xa6, 0x81, 0xa8,
	0xfc, 0x5a, 0x03, 0xb3, 0x2d, 0xe8, 0xec, 0xd0, 0xc0, 0x7d, 0xd0, 0x71, 0x3c, 0x5f, 0xba, 0x15,
	0x05, 0xb8, 0x28, 0x7b, 0xc9, 0x8c, 0xcc, 0x22, 0xeb, 0x05, 0xf7, 0xc4, 0x15, 0x20, 0x57, 0x72,
	0x3e, 0xa5, 0x4c, 0xa1, 0xa4, 0x81, 0x97, 0xdd, 0xb3, 0x15, 0x4a, 0x8a, 0xa0, 0x7f, 0x81, 0x32,
	0x8b, 0xac, 0x3f, 0x06, 0xb2, 0x25, 0x36, 0xbe, 0xba, 0x11, 0x73, 0x03, 0x7e, 0xf3, 0xca, 0x4c,
	0xa5, 0xa6, 0x89, 0x40, 0x45, 0x89, 0x04, 0xe3, 0x6f, 0xcb, 0x81, 0xc5, 0xc2, 0xe8, 0x2c, 0xd2,
	0x54, 0xab, 0x19, 0xba, 0x6a, 0xb5, 0xcb, 0x50, 0xc7, 0x48, 0xc0, 0xa0, 0x1e, 0x53, 0xa6, 0xf0,
	0x10, 0x28, 0x0b, 0x32, 0xad, 0x5f, 0x19, 0x40, 0xb6, 0xbb, 0xde, 0x91, 0x13, 0xd3, 0xff, 0x87,
	0x15, 0xa6, 0x95, 0xbe, 0x15, 0xb5, 0xd2, 0x37, 0xdd, 0x12, 0x93, 0x99, 0xeb, 0xf6, 0x4d, 0x58,
	0x2c, 0xcc, 0x95, 0x45, 0xe2, 0x96, 0xe2, 0xf8, 0x99, 0x2d, 0x56, 0xe5, 0x10, 0x61, 0x5a, 0xbf,
	0xc4, 0x2d, 0x9d, 0xa6, 0x80, 0xa4, 0xb7, 0x22, 0xc2, 0x43, 0xfa, 0x93, 0x7a, 0xdc, 0x3c, 0xa5,
	0x65, 0xe3, 0xde, 0x1b, 0x4a, 0x9b, 0x45, 0xfc, 0x5a, 0x92, 0x64, 0xa7, 0xc4, 0xcc, 0x92, 0xcf,
	0x61, 0x1b, 0xdf, 0xfa, 0x07, 0x11, 0x98, 0x93, 0x71, 0xd4, 0x76, 0xd8, 0x55, 0x56, 0xcb, 0x6f,
	0x02, 0xec, 0xd5, 0x08, 0xe8, 0x75, 0x90, 0x35, 0x10, 0xad, 0x54, 0x4e, 0x55, 0x01, 0x79, 0x96,
	0x09, 0x36, 0x95, 0xf5, 0xc1, 0xa6, 0x8a, 0x9a, 0x14, 0xf9, 0x65, 0x19, 0x56, 0x87, 0x4e, 0x38,
	0x37, 0x94, 0x91, 0x1f, 0xea, 0x02, 0xd4, 0x44, 0x5c, 0x37, 0x3c, 0x0e, 0x68, 0x57, 0x4e, 0x45,
	0xd4, 0x06, 0x3c, 0xe1, 0x10, 0xd5, 0xe6, 0x96, 0x33, 0x87, 0xeb, 0x0d, 0x58, 0x52, 0x7a, 0xb6,
	0x8e, 0xdd, 0x4c, 0x96, 0x7c, 0x21, 0x25, 0xf1, 0x7c, 0x50, 0x00, 0xd3, 0xd0, 0x75, 0x68, 0xb5,
	0x83, 0xbe, 0xd4, 0xb5, 0xa5, 0x42, 0xa7, 0x7b, 0x41, 0x9f, 0x5c, 0x85, 0x79, 0xb9, 0x82, 0xe3,
	0x6c, 0x7d, 0x4d, 0x5d, 0x80, 0x13, 0xfa, 0xef, 0xc0, 0x62, 0x0e, 0x0f, 0x49, 0x8b, 0xdb, 0xb7,
	0x99, 0xc1, 0xe5, 0x64, 0xf3, 0xa5, 0xad, 0x33, 0x6a, 0xa5, 0xb3, 0xae, 0xb4, 0xb5, 0x2a, 0xed,
	0xed, 0xa0, 0xb4, 0xf5, 0x32, 0x0c, 0x1c, 0x09, 0x61, 0x74, 0x41, 0x6c, 0xf2, 0x04, 0x88, 0x85,
	0x96, 0xbf, 0x30, 0xd0, 0x4d, 0x18, 0xa5, 0x51, 0x2c, 0x22, 0xbf, 0x07, 0x4b, 0xc9, 0xdc, 0x13,
	0x8a, 0xca, 0xa9, 0xb1, 0x36, 0xac, 0x1a, 0xa4, 0x48, 0xce, 0x26, 0x72, 0xa1, 0x12, 0x3a, 0x3c,
	0xac, 0x67, 0xdd, 0x46, 0x3f, 0x5f, 0x5e, 0x41, 0x9f, 0x72, 0x90, 0xb8, 0xe4, 0x73, 0x15, 0x7f,
	0x03, 0x94, 0xea, 0x40, 0x9c, 0x45, 0xa6, 0x5e, 0xd0, 0xfa, 0x0f, 0x03, 0x9d, 0x76, 0x5d, 0x67,
	0x16, 0x91, 0x1f, 0x64, 0xce, 0xbc, 0x9b, 0x23, 0x42, 0x68, 0xb9, 0x9e, 0xeb, 0x7c, 0xba, 0x22,
	0x5d, 0x29, 0x7c, 0xa0, 0x8b, 0x50, 0x8b, 0x53, 0x94, 0xe4, 0xd9, 0x81, 0x02, 0xe2, 0x27, 0x1b,
	0x16, 0xf4, 0x28, 0x94, 0x12, 0xa7, 0x35, 0x0f, 0x6f, 0x7e, 0x08, 0xd5, 0xc1, 0x00, 0x6a, 0xc2,
	0xb3, 0x2a, 0x12, 0x9e, 0x4b, 0x6a, 0xc2, 0xb3, 0xa2, 0xa6, 0x35, 0x5f, 0x60, 0xdd, 0x69, 0xe6,
	0xaa, 0x8e, 0x4c, 0xf5, 0x62, 0x9f, 0x4a, 0x0a, 0xe2, 0x43, 0xb9, 0xb5, 0x0f, 0x0c, 0x0b, 0xff,
	0x12, 0x70, 0x87, 0x85, 0x41, 0x5a, 0xae, 0xc1, 0xbf, 0xac, 0x9f, 0x4d, 0xc1, 0x39, 0x9d, 0x0f,
	0xf8, 0xf9, 0xc6, 0xff, 0x42, 0xf1, 0xfb, 0x79, 0xa8, 0xf2, 0xbf, 0x2d, 0x97, 0xb2, 0x76, 0x52,
	0xd6, 0xc3, 0x01, 0xf7, 0x29, 0x6b, 0x27, 0x96, 0xb8, 0xa2, 0xab, 0x8c, 0x9f, 0xfc, 0x26, 0x95,
	0xf1, 0xb9, 0xca, 0xef, 0xa9, 0x42, 0xe5, 0x77, 0x7e, 0x07, 0x4e, 0x9f, 0x56, 0x5c, 0x3e, 0xa3,
	0x79, 0xf5, 0xc0, 0xbd, 0x4c, 0x27, 0xe8, 0x8b, 0xb0, 0x50, 0x55, 0x16, 0x3e, 0x0a, 0x58, 0x52,
	0x64, 0xa9, 0x5c, 0xb0, 0x61, 0xac, 0x0b, 0x76, 0x4d, 0x7f, 0xc1, 0x2e, 0x64, 0x3b, 0x66, 0xf5,
	0x0f, 0x28, 0x32, 0x5e, 0x67, 0xbd, 0xe8, 0x75, 0xaa, 0x2e, 0xf0, 0x5c, 0x36, 0x1c, 0xb0, 0x01,
	0xcb, 0xbc, 0xa9, 0x5d, 0x70, 0xfa, 0xe7, 0x2f, 0x1a, 0xd7, 0x4a, 0x7a, 0xa7, 0x5f, 0xef, 0xb8,
	0x9b, 0xd8, 0x41, 0xe3, 0xb8, 0xe7, 0xb2, 0xdf, 0x0b, 0xba, 0xa7, 0x04, 0x59, 0xb3, 0x46, 0xc4,
	0x53, 0x02, 0xd5, 0xac, 0x15, 0xdf, 0x1b, 0x2c, 0x6a, 0xde, 0x1b, 0x28, 0x21, 0xa6, 0xa5, 0xcc,
	0x83, 0x03, 0x35, 0x46, 0xbd, 0x9c, 0x8d, 0x51, 0xff, 0x79, 0x09, 0x16, 0x95, 0xbc, 0xc5, 0x60,
	0x37, 0x8c, 0x9b, 0x74, 0x58, 0x81, 0x69, 0x2f, 0x60, 0xb1, 0x92, 0x24, 0xe4, 0x9f, 0x5b, 0x2e,
	0x59, 0x86, 0x29, 0x46, 0x5f, 0xb6, 0x82, 0x50, 0x6a, 0xfe, 0x24, 0xa3, 0x2f, 0x1f, 0x87, 0x99,
	0x54, 0x8d, 0x71, 0xb6, 0x54, 0x0d, 0xf9, 0x02, 0x96, 0xf2, 0x89, 0x0c, 0xb4, 0x7b, 0x93, 0x68,
	0xf7, 0xde, 0x1a, 0xeb, 0xb2, 0xf7, 0xf9, 0x86, 0x4d, 0xb2, 0x39, 0x0f, 0xac, 0x08, 0xf9, 0x18,
	0xac, 0xc2, 0xe5, 0x52, 0x74, 0xb8, 0xdb, 0xdf, 0xc2, 0x05, 0xc9, 0x0b, 0x51, 0xb2, 0x58, 0x61,
	0xa3, 0xe5, 0x62, 0xad, 0x63, 0xb8, 0x7c, 0x6a, 0x77, 0x16, 0x91, 0xed, 0xe1, 0x09, 0xa5, 0x37,
	0x87, 0x05, 0x2b, 0x33, 0x53, 0xcf, 0x67, 0x90, 0xd6, 0xfe, 0xa9, 0x04, 0xa4, 0x68, 0x7f, 0xc8,
	0x22, 0xcc, 0x3f, 0x0b, 0x3e, 0x0d, 0xc2, 0xe3, 0x20, 0x01, 0x99, 0x13, 0xa4, 0x0e, 0xd5, 0xc1,
	0x6b, 0x48, 0xd3, 0x20, 0x73, 0x00, 0xe9, 0x63, 0x47, 0xb3, 0x44, 0xe6, 0xa1, 0xa6, 0x3c, 0xf9,
	0x33, 0xcb, 0x9c, 0x48, 0xee, 0x69, 0xa3, 0x59, 0x21, 0x2b, 0xb0, 0xa8, 0x79, 0x8a, 0x68, 0x4e,
	0x62, 0xf7, 0xf4, 0xe9, 0x9f, 0x39, 0x45, 0x66, 0x61, 0x26, 0x89, 0x4b, 0x9a, 0xd3, 0xc4, 0x84,
	0x59, 0x35, 0x0e, 0x66, 0xce, 0xf0, 0x0e, 0x9f, 0x06, 0xde, 0xfe, 0x81, 0x9c, 0x50, 0x15, 0x27,
	0x34, 0xf0, 0x2e, 0x4d, 0x20, 0x4d, 0x38, 0xb7, 0x15, 0xc4, 0xb4, 0xeb, 0xb4, 0xe3, 0x4d, 0xa7,
	0xa3, 0x8e, 0x56, 0x23, 0x17, 0xe0, 0xbc, 0xda, 0x86, 0x5d, 0x14, 0x84, 0x59, 0x4e, 0xfd, 0xc1,
	0x0e, 0xbf, 0xe6, 0x08, 0xea, 0x75, 0xb2, 0x04, 0xa6, 0x00, 0x28, 0x68, 0x73, 0x6b, 0x2e, 0xcc,
	0xa7, 0xec, 0xbb, 0xd7, 0x6f, 0xfb, 0x94, 0x0f, 0x2b, 0x79, 0x97, 0x6b, 0x31, 0x27, 0xc8, 0x2a,
	0x2c, 0xe7, 0x80, 0xcf, 0x29, 0x3d, 0xf4, 0xfb, 0xa6, 0xc1, 0xbb, 0xe5, 0x9a, 0x90, 0x1d, 0x7e,
	0xdf, 0x2c, 0xad, 0x7d, 0x9d, 0x7f, 0x6f, 0x25, 0x14, 0x9b, 0xb3, 0x08, 0x9f, 0x76, 0x79, 0xc1,
	0xbe, 0x39, 0xc1, 0x59, 0xa4, 0xc6, 0x33, 0x4c, 0x83, 0x2f, 0x42, 0xc9, 0x52, 0x9b, 0x65, 0x2e,
	0x42, 0x0e, 0xc0, 0x0d, 0x62, 0x4e, 0x92, 0x65, 0x58, 0xc0, 0x4f, 0xf7, 0x87, 0x3d, 0x36, 0xc0,
	0x42, 0xce, 0x3f, 0x94, 0x76, 0xc1, 0x9c, 0x5e, 0xf3, 0x61, 0xa1, 0xb0, 0xa5, 0xb8, 0x6c, 0x25,
	0x50, 0x99, 0x00, 0x81, 0xb9, 0x27, 0x77, 0x1e, 0x7a, 0x5d, 0x16, 0x7f, 0xee, 0xd1, 0x63, 0x0e,
	0xc3, 0x29, 0x3c, 0xb9, 0x63, 0xd3, 0x23, 0x09, 0x28, 0xf1, 0x29, 0x70, 0xc0, 0x5e, 0x8f, 0x51,
	0xd7, 0x2c, 0x73, 0xa1, 0xf1, 0x3e, 0x72, 0xb4, 0xca, 0xda, 0x6f, 0x0c, 0x58, 0xce, 0x5d, 0x5c,
	0xe4, 0x90, 0x73, 0x00, 0xdb, 0xcf, 0x77, 0xb6, 0x02, 0x74, 0x13, 0xcc, 0x09, 0x4e, 0x79, 0xfb,
	0xf9, 0xce, 0x60, 0x78, 0x43, 0x22, 0xec, 0xf4, 0xda, 0x6d, 0xca, 0x98, 0x59, 0x22, 0x35, 0x98,
	0xde, 0x7e, 0xbe, 0xf3, 0xd0, 0xf1, 0x7c, 0xa1, 0x8c, 0xdb, 0xcf, 0x77, 0x3e, 0xe3, 0x1e, 0xb8,
	0x4d, 0x5f, 0x22, 0xb0, 0xc2, 0x65, 0x9a, 0x00, 0x9f, 0x1c, 0xd1, 0x2e, 0x42, 0x27, 0xc9, 0x02,
	0xd4, 0x39, 0xe1, 0xb0, 0x93, 0x20, 0x4e, 0x49, 0xd2, 0xf7, 0xc2, 0x0e, 0x7e, 0x4f, 0xcb, 0x8e,
	0x4f, 0xef, 0x52, 0x27, 0xb0, 0x3d, 0x76, 0x88, 0xd0, 0x99, 0xb5, 0xef, 0x41, 0x5d, 0x32, 0x51,
	0x4e, 0x79, 0x19, 0x5f, 0xa4, 0x70, 0x00, 0x67, 0xf3, 0x4e, 0x6f, 0xb7, 0xe3, 0xc5, 0xe6, 0x04,
	0xef, 0x9d, 0xe0, 0x21, 0x28, 0xa6, 0xae, 0x69, 0xac, 0xbd, 0x05, 0xc0, 0x69, 0xc9, 0xae, 0x00,
	0x53, 0xfc, 0xeb, 0x71, 0x28, 0x36, 0x1e, 0xff, 0x8d, 0xc3, 0x99, 0xc6, 0xda, 0xd7, 0x30, 0x9f,
	0x2b, 0x65, 0xe2, 0x14, 0x9f, 0x05, 0x87, 0x5c, 0xe7, 0xb0, 0x45, 0x6e, 0xd8, 0x45, 0x98, 0xcf,
	0x15, 0xc6, 0x98, 0xc6, 0x60, 0x03, 0x66, 0x6b, 0x5e, 0x84, 0x60, 0x44, 0x70, 0x83, 0xe3, 0x95,
	0xf9, 0xdc, 0x0b, 0xa5, 0x80, 0x66, 0x65, 0xed, 0x57, 0x46, 0xa6, 0xa4, 0x4b, 0x58, 0x16, 0x59,
	0x44, 0x7a, 0x4e, 0x9d, 0x43, 0xda, 0x22, 0xf4, 0x3e, 0x37, 0x13, 0xd1, 0x6c, 0x1a, 0xe4, 0x75,
	0x58, 0xd5, 0xcc, 0x47, 0x36, 0x97, 0xc8, 0x1b, 0xfa, 0x4a, 0x69, 0xd9, 0x5e, 0x26, 0xe7, 0x35,
	0x95, 0xba, 0xb2, 0xb1, 0xb2, 0xf1, 0x63, 0x0b, 0x1f, 0xe1, 0x28, 0x1b, 0x87, 0x74, 0x60, 0x49,
	0xf7, 0x1a, 0x92, 0x14, 0x0b, 0x21, 0x87, 0xbc, 0x7b, 0x6d, 0xbe, 0x3d, 0x26, 0x26, 0x8b, 0xac,
	0x09, 0xf2, 0x02, 0x55, 0x22, 0x7d, 0x93, 0x44, 0xb4, 0x49, 0xa5, 0xcc, 0x0b, 0xa9, 0xa6, 0x75,
	0x1a, 0x0a, 0x52, 0xfe, 0x03, 0x98, 0xcf, 0x3d, 0x31, 0x23, 0x97, 0x75, 0x9e, 0x7b, 0xee, 0xb1,
	0x5a, 0xf3, 0xcd, 0xd3, 0x91, 0x90, 0xfe, 0x57, 0x30, 0x97, 0x2d, 0xb7, 0x24, 0x96, 0xae, 0x67,
	0xb6, 0xc8, 0xb3, 0x79, 0xf9, 0x54, 0x9c, 0x64, 0xf2, 0xb9, 0x1a, 0x48, 0xcd, 0xe4, 0x8b, 0x35,
	0x99, 0x9a, 0xc9, 0x6b, 0x4a, 0x29, 0xad, 0x09, 0xf2, 0x04, 0x20, 0x7d, 0x7c, 0x45, 0xde, 0xd0,
	0x9d, 0xec, 0x0a, 0xd5, 0x0b, 0x23, 0xdb, 0x91, 0xa0, 0x9b, 0xbc, 0x2d, 0x53, 0x74, 0x9f, 0x5c,
	0x19, 0x22, 0xa8, 0x6c, 0x25, 0x77, 0xf3, 0xea, 0x38, 0x68, 0x38, 0x8a, 0x03, 0x66, 0xfe, 0x49,
	0x15, 0xd1, 0x3c, 0x2d, 0x28, 0xbe, 0x21, 0x6b, 0x5e, 0x19, 0x03, 0x0b, 0x87, 0xe8, 0xe3, 0x2d,
	0x52, 0x5b, 0x87, 0x48, 0xae, 0x9f, 0x92, 0xf4, 0xc8, 0x14, 0x87, 0x36, 0xdf, 0x39, 0x03, 0x36,
	0x0e, 0x1d, 0xa9, 0xe5, 0x3b, 0xca, 0x3d, 0x8f, 0xbc, 0x3d, 0xee, 0x8d, 0xf3, 0x65, 0x73, 0x6d,
	0xfc, 0xcb, 0xa9, 0x35, 0x41, 0xfe, 0x24, 0x5f, 0x74, 0x99, 0x2d, 0x3e, 0xd4, 0x6c, 0xfa, 0x21,
	0x05, 0x93, 0xcd, 0xf7, 0xc6, 0xcc, 0x07, 0xa5, 0x79, 0x54, 0x6b, 0x82, 0xfc, 0xa5, 0x81, 0x5e,
	0xe1, 0x29, 0xe5, 0x86, 0x67, 0x98, 0xc5, 0x87, 0x63, 0x61, 0x16, 0xab, 0x19, 0xad, 0x09, 0xf2,
	0x53, 0x03, 0x5d, 0xcc, 0xd3, 0x8a, 0x0b, 0xcf, 0x30, 0x99, 0x8f, 0xc6, 0xc2, 0xd4, 0x14, 0x2f,
	0xa2, 0x16, 0x36, 0x87, 0xe7, 0xd3, 0xce, 0x30, 0x87, 0x1b, 0x67, 0x4c, 0xd3, 0x59, 0x13, 0xe4,
	0x4f, 0x45, 0xf5, 0x8b, 0x3e, 0x0c, 0x49, 0xd6, 0x75, 0x04, 0x87, 0xc7, 0x43, 0xf5, 0x13, 0x18,
	0x11, 0xe3, 0x14, 0xa6, 0xa4, 0xe0, 0xeb, 0x6b, 0x4c, 0x89, 0xae, 0xc8, 0x52, 0x63, 0x4a, 0xb4,
	0x85, 0x94, 0xd6, 0x04, 0xf9, 0x0b, 0x03, 0x75, 0x7f, 0x68, 0xc9, 0x22, 0x79, 0xf7, 0x74, 0x52,
	0xd9, 0xfa, 0x49, 0xfd, 0x1e, 0x18, 0x59, 0x13, 0x69, 0x4d, 0x90, 0x9f, 0x18, 0x18, 0x52, 0x1e,
	0x9e, 0x76, 0x23, 0x63, 0x92, 0x55, 0x42, 0xee, 0xcd, 0x8d, 0xb3, 0x76, 0x51, 0x4e, 0x4c, 0xb5,
	0x28, 0x52, 0x7f, 0x62, 0xe6, 0xca, 0x2b, 0xf5, 0x27, 0x66, 0xbe, 0xb6, 0x72, 0x70, 0x62, 0xaa,
	0x59, 0x57, 0xed, 0x89, 0x99, 0xad, 0xac, 0xd4, 0x9f, 0x98, 0xb9, 0xb2, 0x4a, 0x6b, 0x82, 0xec,
	0xab, 0x05, 0x70, 0x83, 0x02, 0xc0, 0xab, 0x63, 0x95, 0x11, 0xbe, 0x6c, 0xbe, 0x35, 0x66, 0xb9,
	0xe1, 0x60, 0x15, 0x4a, 0xc9, 0x9c, 0x7e, 0x15, 0xd9, 0x42, 0x3e, 0xfd, 0x2a, 0x72, 0x75, 0x77,
	0xd6, 0x04, 0x61, 0x58, 0x2b, 0xa9, 0x29, 0x3d, 0x23, 0x5a, 0xc3, 0xae, 0x2f, 0x7d, 0x6b, 0x7e,
	0x67, 0x6c, 0xdc, 0xec, 0xd9, 0xad, 0xd4, 0x05, 0x0d, 0x3d, 0xbb, 0xb3, 0x95, 0x6b, 0x43, 0xcf,
	0xee, 0x5c, 0x89, 0x91, 0x38, 0xbb, 0xf3, 0x75, 0x3b, 0x44, 0xa7, 0x39, 0x85, 0x52, 0xa0, 0xe6,
	0x95, 0x31, 0xb0, 0x70, 0x88, 0x23, 0x2c, 0xe8, 0xd7, 0x15, 0xee, 0x10, 0x2d, 0x4b, 0x86, 0xd4,
	0x04, 0x35, 0xaf, 0x8f, 0x8f, 0x8c, 0xe3, 0xfe, 0xa8, 0x58, 0x31, 0xa4, 0xde, 0xc5, 0xc9, 0x8d,
	0xd3, 0xe8, 0xe5, 0xca, 0x72, 0x9a, 0xef, 0x9e, 0xad, 0x83, 0xe2, 0x3d, 0x14, 0xcb, 0x4e, 0xf4,
	0xde, 0x83, 0xb6, 0x1c, 0x46, 0xef, 0x3d, 0xe8, 0x2b, 0x59, 0x06, 0xec, 0xd6, 0xd5, 0x9a, 0xe8,
	0xd9, 0x3d, 0xa4, 0xba, 0x45, 0xcf, 0xee, 0x61, 0x25, 0x2c, 0xc2, 0x4e, 0xe5, 0xca, 0x26, 0x34,
	0x76, 0xaa, 0x58, 0xaa, 0xa1, 0xb1, 0x53, 0x9a, 0xea, 0x0b, 0x6b, 0x82, 0xfc, 0x99, 0x38, 0x1a,
	0x0a, 0x99, 0xec, 0x24, 0x0f, 0xae, 0x3f, 0x04, 0x87, 0xe7, 0xf9, 0xf5, 0x87, 0xe0, 0x88, 0x24,
	0x7b, 0x7a, 0x2c, 0x0c, 0x4f, 0xb3, 0xe8, 0x8f, 0x85, 0x91, 0x89, 0x3e, 0xfd, 0xb1, 0x30, 0x3a,
	0x93, 0x63, 0x4d, 0x90, 0x36, 0x34, 0x32, 0x09, 0xed, 0x4c, 0xd9, 0x8c, 0x66, 0xfb, 0x67, 0x93,
	0xfc, 0xda, 0xdb, 0x5a, 0x3e, 0x1b, 0x3f, 0x41, 0xfe, 0x30, 0x97, 0x35, 0x1f, 0x6c, 0xdc, 0x57,
	0x36, 0x82, 0x0b, 0xab, 0x19, 0x70, 0x66, 0x87, 0xbe, 0xc2, 0x51, 0x16, 0x0a, 0xd9, 0x7f, 0xad,
	0x2d, 0x2d, 0xd6, 0x1e, 0x68, 0x6d, 0xa9, 0xae, 0x90, 0x00, 0x77, 0x40, 0x2e, 0x99, 0xaf, 0xd9,
	0x01, 0xc5, 0x62, 0x03, 0xcd, 0x0e, 0xd0, 0xd4, 0x04, 0x08, 0xfa, 0xb9, 0x18, 0x93, 0x86, 0x7e,
	0x31, 0xd5, 0xaf, 0xa1, 0xaf, 0xc9, 0xb1, 0x5b, 0x13, 0xe4, 0x67, 0x86, 0xe6, 0x35, 0x4a, 0x36,
	0x9e, 0x4b, 0xde, 0x3f, 0xdd, 0x87, 0x29, 0x04, 0x90, 0x9b, 0x37, 0xcf, 0xde, 0x89, 0x4f, 0xe8,
	0xee, 0xfb, 0x5f, 0xbe, 0xb7, 0x1f, 0xfa, 0x4e, 0xb0, 0xbf, 0x7e, 0x6b, 0x23, 0x8e, 0xd7, 0xdb,
	0x61, 0xe7, 0x06, 0xfe, 0xcb, 0xbb, 0x76, 0xe8, 0xdf, 0x60, 0xb4, 0x7b, 0xe4, 0xb5, 0x29, 0xcb,
	0xff, 0x53, 0xbc, 0xdd, 0x29, 0x44, 0x79, 0xff, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x85, 0x40,
	0xe8, 0x91, 0x4d, 0x4f, 0x00, 0x00,
}
