// Code generated by protoc-gen-gogo.
// source: services/league/leaguesvr.proto
// DO NOT EDIT!

/*
	Package leaguesvr is a generated protocol buffer package.

	It is generated from these files:
		services/league/leaguesvr.proto

	It has these top-level messages:
		CreateLeagueReq
		CreateLeagueResp
		CreateLeagueTeamReq
		LeagueResult
		LeagueResultSet
		AddLeagueResultReq
		GetTeamResultReq
		GetTeamResultResp
		GetAllTeamScoreReq
		TeamScore
		TeamScoreSet
		GetAllTeamScoreResp
		GetTeamScoreByIdReq
		GetTeamScoreByIdResp
		GetLeagueResultByStateReq
		GetLeagueResultByStateResp
		GetLeagueResultByIdReq
		GetLeagueResultByIdResp
		LeagueSchedule
		LeagueScheduleSet
		GetScheduleByTeamIdReq
		GetScheduleByTeamIdResp
		GetScheduleByDateReq
		GetScheduleByDateResp
		GetLeagueScheduleByStateReq
		GetLeagueScheduleByStateResp
		GenLeagueScheduleReq
		GetLeagueInfoReq
		GetLeagueInfoResp
		UploadSnapshotReq
		GetSnapshotReq
		GetSnapshotResp
		LeagueTeamCount
		GetLeagueTeamCountReq
		GetLeagueTeamCountResp
		GetExtraMatchTeamReq
		ExtraMatchTeamSet
		GetExtraMatchTeamResp
		SetNextMatchTypeReq
		GetAllMatchByStateReq
		GetAllMatchByStateResp
		AuditNotResultMatchReq
*/
package leaguesvr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type GroupType int32

const (
	GroupType_GROUP_FINAL   GroupType = 0
	GroupType_QQAndroid     GroupType = 1
	GroupType_QQiOS         GroupType = 2
	GroupType_WechatAndroid GroupType = 3
	GroupType_WechatiOS     GroupType = 4
)

var GroupType_name = map[int32]string{
	0: "GROUP_FINAL",
	1: "QQAndroid",
	2: "QQiOS",
	3: "WechatAndroid",
	4: "WechatiOS",
}
var GroupType_value = map[string]int32{
	"GROUP_FINAL":   0,
	"QQAndroid":     1,
	"QQiOS":         2,
	"WechatAndroid": 3,
	"WechatiOS":     4,
}

func (x GroupType) Enum() *GroupType {
	p := new(GroupType)
	*p = x
	return p
}
func (x GroupType) String() string {
	return proto.EnumName(GroupType_name, int32(x))
}
func (x *GroupType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GroupType_value, data, "GroupType")
	if err != nil {
		return err
	}
	*x = GroupType(value)
	return nil
}
func (GroupType) EnumDescriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{0} }

type LeagueState int32

const (
	LeagueState_Regular      LeagueState = 1
	LeagueState_RegularExtra LeagueState = 2
	LeagueState_Promotion    LeagueState = 3
	LeagueState_SemiFinal    LeagueState = 4
	LeagueState_Final        LeagueState = 5
	LeagueState_ThirdWinner  LeagueState = 6
	LeagueState_Complete     LeagueState = 7
)

var LeagueState_name = map[int32]string{
	1: "Regular",
	2: "RegularExtra",
	3: "Promotion",
	4: "SemiFinal",
	5: "Final",
	6: "ThirdWinner",
	7: "Complete",
}
var LeagueState_value = map[string]int32{
	"Regular":      1,
	"RegularExtra": 2,
	"Promotion":    3,
	"SemiFinal":    4,
	"Final":        5,
	"ThirdWinner":  6,
	"Complete":     7,
}

func (x LeagueState) Enum() *LeagueState {
	p := new(LeagueState)
	*p = x
	return p
}
func (x LeagueState) String() string {
	return proto.EnumName(LeagueState_name, int32(x))
}
func (x *LeagueState) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(LeagueState_value, data, "LeagueState")
	if err != nil {
		return err
	}
	*x = LeagueState(value)
	return nil
}
func (LeagueState) EnumDescriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{1} }

type MatchState int32

const (
	MatchState_NotStart  MatchState = 0
	MatchState_Finished  MatchState = 1
	MatchState_DirectWin MatchState = 2
)

var MatchState_name = map[int32]string{
	0: "NotStart",
	1: "Finished",
	2: "DirectWin",
}
var MatchState_value = map[string]int32{
	"NotStart":  0,
	"Finished":  1,
	"DirectWin": 2,
}

func (x MatchState) Enum() *MatchState {
	p := new(MatchState)
	*p = x
	return p
}
func (x MatchState) String() string {
	return proto.EnumName(MatchState_name, int32(x))
}
func (x *MatchState) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MatchState_value, data, "MatchState")
	if err != nil {
		return err
	}
	*x = MatchState(value)
	return nil
}
func (MatchState) EnumDescriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{2} }

// --比赛信息
type CreateLeagueReq struct {
	GameId   uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	TimeFrom uint64 `protobuf:"varint,2,req,name=time_from,json=timeFrom" json:"time_from"`
	TimeTo   uint64 `protobuf:"varint,3,req,name=time_to,json=timeTo" json:"time_to"`
}

func (m *CreateLeagueReq) Reset()                    { *m = CreateLeagueReq{} }
func (m *CreateLeagueReq) String() string            { return proto.CompactTextString(m) }
func (*CreateLeagueReq) ProtoMessage()               {}
func (*CreateLeagueReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{0} }

func (m *CreateLeagueReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateLeagueReq) GetTimeFrom() uint64 {
	if m != nil {
		return m.TimeFrom
	}
	return 0
}

func (m *CreateLeagueReq) GetTimeTo() uint64 {
	if m != nil {
		return m.TimeTo
	}
	return 0
}

type CreateLeagueResp struct {
	LeagueId uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
}

func (m *CreateLeagueResp) Reset()                    { *m = CreateLeagueResp{} }
func (m *CreateLeagueResp) String() string            { return proto.CompactTextString(m) }
func (*CreateLeagueResp) ProtoMessage()               {}
func (*CreateLeagueResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{1} }

func (m *CreateLeagueResp) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

type CreateLeagueTeamReq struct {
	LeagueId  uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TeamId    uint32 `protobuf:"varint,2,req,name=team_id,json=teamId" json:"team_id"`
	GroupType uint32 `protobuf:"varint,3,req,name=group_type,json=groupType" json:"group_type"`
	IsSeed    bool   `protobuf:"varint,4,req,name=is_seed,json=isSeed" json:"is_seed"`
}

func (m *CreateLeagueTeamReq) Reset()                    { *m = CreateLeagueTeamReq{} }
func (m *CreateLeagueTeamReq) String() string            { return proto.CompactTextString(m) }
func (*CreateLeagueTeamReq) ProtoMessage()               {}
func (*CreateLeagueTeamReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{2} }

func (m *CreateLeagueTeamReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *CreateLeagueTeamReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *CreateLeagueTeamReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *CreateLeagueTeamReq) GetIsSeed() bool {
	if m != nil {
		return m.IsSeed
	}
	return false
}

type LeagueResult struct {
	TeamFrom    uint32   `protobuf:"varint,1,req,name=team_from,json=teamFrom" json:"team_from"`
	TeamTo      uint32   `protobuf:"varint,2,req,name=team_to,json=teamTo" json:"team_to"`
	FromWins    uint32   `protobuf:"varint,3,opt,name=from_wins,json=fromWins" json:"from_wins"`
	ToWins      uint32   `protobuf:"varint,4,opt,name=to_wins,json=toWins" json:"to_wins"`
	MatchType   uint32   `protobuf:"varint,5,opt,name=match_type,json=matchType" json:"match_type"`
	MatchRound  uint32   `protobuf:"varint,6,opt,name=match_round,json=matchRound" json:"match_round"`
	FromImgList []string `protobuf:"bytes,7,rep,name=from_img_list,json=fromImgList" json:"from_img_list,omitempty"`
	ToImgList   []string `protobuf:"bytes,8,rep,name=to_img_list,json=toImgList" json:"to_img_list,omitempty"`
	AuditState  uint32   `protobuf:"varint,9,opt,name=audit_state,json=auditState" json:"audit_state"`
	TimeFrom    uint64   `protobuf:"varint,10,opt,name=time_from,json=timeFrom" json:"time_from"`
	TimeTo      uint64   `protobuf:"varint,11,opt,name=time_to,json=timeTo" json:"time_to"`
	MatchState  uint32   `protobuf:"varint,12,opt,name=match_state,json=matchState" json:"match_state"`
	MatchId     uint32   `protobuf:"varint,13,opt,name=match_id,json=matchId" json:"match_id"`
}

func (m *LeagueResult) Reset()                    { *m = LeagueResult{} }
func (m *LeagueResult) String() string            { return proto.CompactTextString(m) }
func (*LeagueResult) ProtoMessage()               {}
func (*LeagueResult) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{3} }

func (m *LeagueResult) GetTeamFrom() uint32 {
	if m != nil {
		return m.TeamFrom
	}
	return 0
}

func (m *LeagueResult) GetTeamTo() uint32 {
	if m != nil {
		return m.TeamTo
	}
	return 0
}

func (m *LeagueResult) GetFromWins() uint32 {
	if m != nil {
		return m.FromWins
	}
	return 0
}

func (m *LeagueResult) GetToWins() uint32 {
	if m != nil {
		return m.ToWins
	}
	return 0
}

func (m *LeagueResult) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *LeagueResult) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *LeagueResult) GetFromImgList() []string {
	if m != nil {
		return m.FromImgList
	}
	return nil
}

func (m *LeagueResult) GetToImgList() []string {
	if m != nil {
		return m.ToImgList
	}
	return nil
}

func (m *LeagueResult) GetAuditState() uint32 {
	if m != nil {
		return m.AuditState
	}
	return 0
}

func (m *LeagueResult) GetTimeFrom() uint64 {
	if m != nil {
		return m.TimeFrom
	}
	return 0
}

func (m *LeagueResult) GetTimeTo() uint64 {
	if m != nil {
		return m.TimeTo
	}
	return 0
}

func (m *LeagueResult) GetMatchState() uint32 {
	if m != nil {
		return m.MatchState
	}
	return 0
}

func (m *LeagueResult) GetMatchId() uint32 {
	if m != nil {
		return m.MatchId
	}
	return 0
}

type LeagueResultSet struct {
	GroupType  GroupType       `protobuf:"varint,1,req,name=group_type,json=groupType,enum=leaguesvr.GroupType" json:"group_type"`
	ResultList []*LeagueResult `protobuf:"bytes,2,rep,name=result_list,json=resultList" json:"result_list,omitempty"`
}

func (m *LeagueResultSet) Reset()                    { *m = LeagueResultSet{} }
func (m *LeagueResultSet) String() string            { return proto.CompactTextString(m) }
func (*LeagueResultSet) ProtoMessage()               {}
func (*LeagueResultSet) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{4} }

func (m *LeagueResultSet) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_FINAL
}

func (m *LeagueResultSet) GetResultList() []*LeagueResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

type AddLeagueResultReq struct {
	LeagueId uint32        `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	Result   *LeagueResult `protobuf:"bytes,2,req,name=result" json:"result,omitempty"`
}

func (m *AddLeagueResultReq) Reset()                    { *m = AddLeagueResultReq{} }
func (m *AddLeagueResultReq) String() string            { return proto.CompactTextString(m) }
func (*AddLeagueResultReq) ProtoMessage()               {}
func (*AddLeagueResultReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{5} }

func (m *AddLeagueResultReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *AddLeagueResultReq) GetResult() *LeagueResult {
	if m != nil {
		return m.Result
	}
	return nil
}

// 获取比赛结果
type GetTeamResultReq struct {
	LeagueId uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TeamId   uint32 `protobuf:"varint,2,req,name=team_id,json=teamId" json:"team_id"`
}

func (m *GetTeamResultReq) Reset()                    { *m = GetTeamResultReq{} }
func (m *GetTeamResultReq) String() string            { return proto.CompactTextString(m) }
func (*GetTeamResultReq) ProtoMessage()               {}
func (*GetTeamResultReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{6} }

func (m *GetTeamResultReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetTeamResultReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

type GetTeamResultResp struct {
	ResultList []*LeagueResult `protobuf:"bytes,1,rep,name=result_list,json=resultList" json:"result_list,omitempty"`
	Score      uint32          `protobuf:"varint,2,req,name=score" json:"score"`
	Win        uint32          `protobuf:"varint,3,req,name=win" json:"win"`
	Lose       uint32          `protobuf:"varint,4,req,name=lose" json:"lose"`
	Draw       uint32          `protobuf:"varint,5,req,name=draw" json:"draw"`
	MatchType  uint32          `protobuf:"varint,6,req,name=match_type,json=matchType" json:"match_type"`
	MatchRound uint32          `protobuf:"varint,7,req,name=match_round,json=matchRound" json:"match_round"`
	GroupType  uint32          `protobuf:"varint,8,req,name=group_type,json=groupType" json:"group_type"`
}

func (m *GetTeamResultResp) Reset()                    { *m = GetTeamResultResp{} }
func (m *GetTeamResultResp) String() string            { return proto.CompactTextString(m) }
func (*GetTeamResultResp) ProtoMessage()               {}
func (*GetTeamResultResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{7} }

func (m *GetTeamResultResp) GetResultList() []*LeagueResult {
	if m != nil {
		return m.ResultList
	}
	return nil
}

func (m *GetTeamResultResp) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *GetTeamResultResp) GetWin() uint32 {
	if m != nil {
		return m.Win
	}
	return 0
}

func (m *GetTeamResultResp) GetLose() uint32 {
	if m != nil {
		return m.Lose
	}
	return 0
}

func (m *GetTeamResultResp) GetDraw() uint32 {
	if m != nil {
		return m.Draw
	}
	return 0
}

func (m *GetTeamResultResp) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetTeamResultResp) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *GetTeamResultResp) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

type GetAllTeamScoreReq struct {
	LeagueId  uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	GroupType uint32 `protobuf:"varint,2,opt,name=group_type,json=groupType" json:"group_type"`
	FromIndex uint32 `protobuf:"varint,3,opt,name=from_index,json=fromIndex" json:"from_index"`
	ToIndex   uint32 `protobuf:"varint,4,opt,name=to_index,json=toIndex" json:"to_index"`
}

func (m *GetAllTeamScoreReq) Reset()                    { *m = GetAllTeamScoreReq{} }
func (m *GetAllTeamScoreReq) String() string            { return proto.CompactTextString(m) }
func (*GetAllTeamScoreReq) ProtoMessage()               {}
func (*GetAllTeamScoreReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{8} }

func (m *GetAllTeamScoreReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetAllTeamScoreReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GetAllTeamScoreReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetAllTeamScoreReq) GetToIndex() uint32 {
	if m != nil {
		return m.ToIndex
	}
	return 0
}

type TeamScore struct {
	TeamId uint32 `protobuf:"varint,1,req,name=team_id,json=teamId" json:"team_id"`
	Score  uint32 `protobuf:"varint,2,req,name=score" json:"score"`
	Win    uint32 `protobuf:"varint,3,req,name=win" json:"win"`
	Lose   uint32 `protobuf:"varint,4,req,name=lose" json:"lose"`
	Draw   uint32 `protobuf:"varint,5,req,name=draw" json:"draw"`
}

func (m *TeamScore) Reset()                    { *m = TeamScore{} }
func (m *TeamScore) String() string            { return proto.CompactTextString(m) }
func (*TeamScore) ProtoMessage()               {}
func (*TeamScore) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{9} }

func (m *TeamScore) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *TeamScore) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *TeamScore) GetWin() uint32 {
	if m != nil {
		return m.Win
	}
	return 0
}

func (m *TeamScore) GetLose() uint32 {
	if m != nil {
		return m.Lose
	}
	return 0
}

func (m *TeamScore) GetDraw() uint32 {
	if m != nil {
		return m.Draw
	}
	return 0
}

type TeamScoreSet struct {
	GroupType     uint32       `protobuf:"varint,1,req,name=group_type,json=groupType" json:"group_type"`
	TeamScoreList []*TeamScore `protobuf:"bytes,2,rep,name=team_score_list,json=teamScoreList" json:"team_score_list,omitempty"`
}

func (m *TeamScoreSet) Reset()                    { *m = TeamScoreSet{} }
func (m *TeamScoreSet) String() string            { return proto.CompactTextString(m) }
func (*TeamScoreSet) ProtoMessage()               {}
func (*TeamScoreSet) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{10} }

func (m *TeamScoreSet) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *TeamScoreSet) GetTeamScoreList() []*TeamScore {
	if m != nil {
		return m.TeamScoreList
	}
	return nil
}

type GetAllTeamScoreResp struct {
	TeamScoreSetList []*TeamScoreSet `protobuf:"bytes,1,rep,name=team_score_set_list,json=teamScoreSetList" json:"team_score_set_list,omitempty"`
}

func (m *GetAllTeamScoreResp) Reset()                    { *m = GetAllTeamScoreResp{} }
func (m *GetAllTeamScoreResp) String() string            { return proto.CompactTextString(m) }
func (*GetAllTeamScoreResp) ProtoMessage()               {}
func (*GetAllTeamScoreResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{11} }

func (m *GetAllTeamScoreResp) GetTeamScoreSetList() []*TeamScoreSet {
	if m != nil {
		return m.TeamScoreSetList
	}
	return nil
}

type GetTeamScoreByIdReq struct {
	LeagueId   uint32   `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TeamIdList []uint32 `protobuf:"varint,2,rep,name=team_id_list,json=teamIdList" json:"team_id_list,omitempty"`
}

func (m *GetTeamScoreByIdReq) Reset()                    { *m = GetTeamScoreByIdReq{} }
func (m *GetTeamScoreByIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetTeamScoreByIdReq) ProtoMessage()               {}
func (*GetTeamScoreByIdReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{12} }

func (m *GetTeamScoreByIdReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetTeamScoreByIdReq) GetTeamIdList() []uint32 {
	if m != nil {
		return m.TeamIdList
	}
	return nil
}

type GetTeamScoreByIdResp struct {
	TeamScoreList []*TeamScore `protobuf:"bytes,2,rep,name=team_score_list,json=teamScoreList" json:"team_score_list,omitempty"`
}

func (m *GetTeamScoreByIdResp) Reset()                    { *m = GetTeamScoreByIdResp{} }
func (m *GetTeamScoreByIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetTeamScoreByIdResp) ProtoMessage()               {}
func (*GetTeamScoreByIdResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{13} }

func (m *GetTeamScoreByIdResp) GetTeamScoreList() []*TeamScore {
	if m != nil {
		return m.TeamScoreList
	}
	return nil
}

type GetLeagueResultByStateReq struct {
	LeagueId   uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	MatchType  uint32 `protobuf:"varint,2,opt,name=match_type,json=matchType" json:"match_type"`
	MatchRound uint32 `protobuf:"varint,3,opt,name=match_round,json=matchRound" json:"match_round"`
	GroupType  uint32 `protobuf:"varint,4,opt,name=group_type,json=groupType" json:"group_type"`
	FromIndex  uint32 `protobuf:"varint,5,opt,name=from_index,json=fromIndex" json:"from_index"`
	ToIndex    uint32 `protobuf:"varint,6,opt,name=to_index,json=toIndex" json:"to_index"`
}

func (m *GetLeagueResultByStateReq) Reset()         { *m = GetLeagueResultByStateReq{} }
func (m *GetLeagueResultByStateReq) String() string { return proto.CompactTextString(m) }
func (*GetLeagueResultByStateReq) ProtoMessage()    {}
func (*GetLeagueResultByStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorLeaguesvr, []int{14}
}

func (m *GetLeagueResultByStateReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetLeagueResultByStateReq) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetLeagueResultByStateReq) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *GetLeagueResultByStateReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GetLeagueResultByStateReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetLeagueResultByStateReq) GetToIndex() uint32 {
	if m != nil {
		return m.ToIndex
	}
	return 0
}

type GetLeagueResultByStateResp struct {
	ResultSetList []*LeagueResultSet `protobuf:"bytes,1,rep,name=result_set_list,json=resultSetList" json:"result_set_list,omitempty"`
}

func (m *GetLeagueResultByStateResp) Reset()         { *m = GetLeagueResultByStateResp{} }
func (m *GetLeagueResultByStateResp) String() string { return proto.CompactTextString(m) }
func (*GetLeagueResultByStateResp) ProtoMessage()    {}
func (*GetLeagueResultByStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorLeaguesvr, []int{15}
}

func (m *GetLeagueResultByStateResp) GetResultSetList() []*LeagueResultSet {
	if m != nil {
		return m.ResultSetList
	}
	return nil
}

type GetLeagueResultByIdReq struct {
	LeagueId   uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	MatchType  uint32 `protobuf:"varint,2,opt,name=match_type,json=matchType" json:"match_type"`
	MatchRound uint32 `protobuf:"varint,3,opt,name=match_round,json=matchRound" json:"match_round"`
	GroupType  uint32 `protobuf:"varint,4,opt,name=group_type,json=groupType" json:"group_type"`
	TeamId     uint32 `protobuf:"varint,5,req,name=team_id,json=teamId" json:"team_id"`
}

func (m *GetLeagueResultByIdReq) Reset()                    { *m = GetLeagueResultByIdReq{} }
func (m *GetLeagueResultByIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetLeagueResultByIdReq) ProtoMessage()               {}
func (*GetLeagueResultByIdReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{16} }

func (m *GetLeagueResultByIdReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetLeagueResultByIdReq) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetLeagueResultByIdReq) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *GetLeagueResultByIdReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GetLeagueResultByIdReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

type GetLeagueResultByIdResp struct {
	LeagueResult *LeagueResult `protobuf:"bytes,1,req,name=league_result,json=leagueResult" json:"league_result,omitempty"`
}

func (m *GetLeagueResultByIdResp) Reset()         { *m = GetLeagueResultByIdResp{} }
func (m *GetLeagueResultByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetLeagueResultByIdResp) ProtoMessage()    {}
func (*GetLeagueResultByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorLeaguesvr, []int{17}
}

func (m *GetLeagueResultByIdResp) GetLeagueResult() *LeagueResult {
	if m != nil {
		return m.LeagueResult
	}
	return nil
}

type LeagueSchedule struct {
	TeamFrom      uint32 `protobuf:"varint,1,req,name=team_from,json=teamFrom" json:"team_from"`
	TeamTo        uint32 `protobuf:"varint,2,req,name=team_to,json=teamTo" json:"team_to"`
	TimestampFrom uint64 `protobuf:"varint,3,req,name=timestamp_from,json=timestampFrom" json:"timestamp_from"`
	TimestampTo   uint64 `protobuf:"varint,4,req,name=timestamp_to,json=timestampTo" json:"timestamp_to"`
	MatchType     uint32 `protobuf:"varint,5,req,name=match_type,json=matchType" json:"match_type"`
	MatchRound    uint32 `protobuf:"varint,6,req,name=match_round,json=matchRound" json:"match_round"`
}

func (m *LeagueSchedule) Reset()                    { *m = LeagueSchedule{} }
func (m *LeagueSchedule) String() string            { return proto.CompactTextString(m) }
func (*LeagueSchedule) ProtoMessage()               {}
func (*LeagueSchedule) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{18} }

func (m *LeagueSchedule) GetTeamFrom() uint32 {
	if m != nil {
		return m.TeamFrom
	}
	return 0
}

func (m *LeagueSchedule) GetTeamTo() uint32 {
	if m != nil {
		return m.TeamTo
	}
	return 0
}

func (m *LeagueSchedule) GetTimestampFrom() uint64 {
	if m != nil {
		return m.TimestampFrom
	}
	return 0
}

func (m *LeagueSchedule) GetTimestampTo() uint64 {
	if m != nil {
		return m.TimestampTo
	}
	return 0
}

func (m *LeagueSchedule) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *LeagueSchedule) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

type LeagueScheduleSet struct {
	GroupType    GroupType         `protobuf:"varint,1,req,name=group_type,json=groupType,enum=leaguesvr.GroupType" json:"group_type"`
	ScheduleList []*LeagueSchedule `protobuf:"bytes,2,rep,name=schedule_list,json=scheduleList" json:"schedule_list,omitempty"`
}

func (m *LeagueScheduleSet) Reset()                    { *m = LeagueScheduleSet{} }
func (m *LeagueScheduleSet) String() string            { return proto.CompactTextString(m) }
func (*LeagueScheduleSet) ProtoMessage()               {}
func (*LeagueScheduleSet) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{19} }

func (m *LeagueScheduleSet) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_FINAL
}

func (m *LeagueScheduleSet) GetScheduleList() []*LeagueSchedule {
	if m != nil {
		return m.ScheduleList
	}
	return nil
}

type GetScheduleByTeamIdReq struct {
	LeagueId uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TeamId   uint32 `protobuf:"varint,2,req,name=team_id,json=teamId" json:"team_id"`
}

func (m *GetScheduleByTeamIdReq) Reset()                    { *m = GetScheduleByTeamIdReq{} }
func (m *GetScheduleByTeamIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetScheduleByTeamIdReq) ProtoMessage()               {}
func (*GetScheduleByTeamIdReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{20} }

func (m *GetScheduleByTeamIdReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetScheduleByTeamIdReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

type GetScheduleByTeamIdResp struct {
	ScheduleList []*LeagueSchedule `protobuf:"bytes,1,rep,name=schedule_list,json=scheduleList" json:"schedule_list,omitempty"`
}

func (m *GetScheduleByTeamIdResp) Reset()         { *m = GetScheduleByTeamIdResp{} }
func (m *GetScheduleByTeamIdResp) String() string { return proto.CompactTextString(m) }
func (*GetScheduleByTeamIdResp) ProtoMessage()    {}
func (*GetScheduleByTeamIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorLeaguesvr, []int{21}
}

func (m *GetScheduleByTeamIdResp) GetScheduleList() []*LeagueSchedule {
	if m != nil {
		return m.ScheduleList
	}
	return nil
}

type GetScheduleByDateReq struct {
	LeagueId  uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	Timestamp uint64 `protobuf:"varint,2,req,name=timestamp" json:"timestamp"`
}

func (m *GetScheduleByDateReq) Reset()                    { *m = GetScheduleByDateReq{} }
func (m *GetScheduleByDateReq) String() string            { return proto.CompactTextString(m) }
func (*GetScheduleByDateReq) ProtoMessage()               {}
func (*GetScheduleByDateReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{22} }

func (m *GetScheduleByDateReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetScheduleByDateReq) GetTimestamp() uint64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type GetScheduleByDateResp struct {
	ScheduleSetList []*LeagueScheduleSet `protobuf:"bytes,1,rep,name=schedule_set_list,json=scheduleSetList" json:"schedule_set_list,omitempty"`
}

func (m *GetScheduleByDateResp) Reset()                    { *m = GetScheduleByDateResp{} }
func (m *GetScheduleByDateResp) String() string            { return proto.CompactTextString(m) }
func (*GetScheduleByDateResp) ProtoMessage()               {}
func (*GetScheduleByDateResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{23} }

func (m *GetScheduleByDateResp) GetScheduleSetList() []*LeagueScheduleSet {
	if m != nil {
		return m.ScheduleSetList
	}
	return nil
}

type GetLeagueScheduleByStateReq struct {
	LeagueId  uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	GroupType uint32 `protobuf:"varint,2,opt,name=group_type,json=groupType" json:"group_type"`
	FromIndex uint32 `protobuf:"varint,3,opt,name=from_index,json=fromIndex" json:"from_index"`
	ToIndex   uint32 `protobuf:"varint,4,opt,name=to_index,json=toIndex" json:"to_index"`
	ForceGet  bool   `protobuf:"varint,5,opt,name=force_get,json=forceGet" json:"force_get"`
	TimeLimit bool   `protobuf:"varint,6,opt,name=time_limit,json=timeLimit" json:"time_limit"`
}

func (m *GetLeagueScheduleByStateReq) Reset()         { *m = GetLeagueScheduleByStateReq{} }
func (m *GetLeagueScheduleByStateReq) String() string { return proto.CompactTextString(m) }
func (*GetLeagueScheduleByStateReq) ProtoMessage()    {}
func (*GetLeagueScheduleByStateReq) Descriptor() ([]byte, []int) {
	return fileDescriptorLeaguesvr, []int{24}
}

func (m *GetLeagueScheduleByStateReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetLeagueScheduleByStateReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GetLeagueScheduleByStateReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetLeagueScheduleByStateReq) GetToIndex() uint32 {
	if m != nil {
		return m.ToIndex
	}
	return 0
}

func (m *GetLeagueScheduleByStateReq) GetForceGet() bool {
	if m != nil {
		return m.ForceGet
	}
	return false
}

func (m *GetLeagueScheduleByStateReq) GetTimeLimit() bool {
	if m != nil {
		return m.TimeLimit
	}
	return false
}

type GetLeagueScheduleByStateResp struct {
	ScheduleSetList []*LeagueScheduleSet `protobuf:"bytes,1,rep,name=schedule_set_list,json=scheduleSetList" json:"schedule_set_list,omitempty"`
}

func (m *GetLeagueScheduleByStateResp) Reset()         { *m = GetLeagueScheduleByStateResp{} }
func (m *GetLeagueScheduleByStateResp) String() string { return proto.CompactTextString(m) }
func (*GetLeagueScheduleByStateResp) ProtoMessage()    {}
func (*GetLeagueScheduleByStateResp) Descriptor() ([]byte, []int) {
	return fileDescriptorLeaguesvr, []int{25}
}

func (m *GetLeagueScheduleByStateResp) GetScheduleSetList() []*LeagueScheduleSet {
	if m != nil {
		return m.ScheduleSetList
	}
	return nil
}

type GenLeagueScheduleReq struct {
	LeagueId      uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TimestampFrom uint64 `protobuf:"varint,2,req,name=timestamp_from,json=timestampFrom" json:"timestamp_from"`
	TimestampTo   uint64 `protobuf:"varint,3,req,name=timestamp_to,json=timestampTo" json:"timestamp_to"`
}

func (m *GenLeagueScheduleReq) Reset()                    { *m = GenLeagueScheduleReq{} }
func (m *GenLeagueScheduleReq) String() string            { return proto.CompactTextString(m) }
func (*GenLeagueScheduleReq) ProtoMessage()               {}
func (*GenLeagueScheduleReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{26} }

func (m *GenLeagueScheduleReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GenLeagueScheduleReq) GetTimestampFrom() uint64 {
	if m != nil {
		return m.TimestampFrom
	}
	return 0
}

func (m *GenLeagueScheduleReq) GetTimestampTo() uint64 {
	if m != nil {
		return m.TimestampTo
	}
	return 0
}

// 联赛信息
type GetLeagueInfoReq struct {
	LeagueId uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
}

func (m *GetLeagueInfoReq) Reset()                    { *m = GetLeagueInfoReq{} }
func (m *GetLeagueInfoReq) String() string            { return proto.CompactTextString(m) }
func (*GetLeagueInfoReq) ProtoMessage()               {}
func (*GetLeagueInfoReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{27} }

func (m *GetLeagueInfoReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

type GetLeagueInfoResp struct {
	GameId             uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	MatchType          uint32 `protobuf:"varint,2,req,name=match_type,json=matchType" json:"match_type"`
	MatchRound         uint32 `protobuf:"varint,3,req,name=match_round,json=matchRound" json:"match_round"`
	LeagueState        uint32 `protobuf:"varint,4,req,name=league_state,json=leagueState" json:"league_state"`
	TimeFrom           uint64 `protobuf:"varint,5,req,name=time_from,json=timeFrom" json:"time_from"`
	TimeTo             uint64 `protobuf:"varint,6,req,name=time_to,json=timeTo" json:"time_to"`
	RegularMatchRound  uint32 `protobuf:"varint,7,req,name=regular_match_round,json=regularMatchRound" json:"regular_match_round"`
	PromotionTeamCount uint32 `protobuf:"varint,8,req,name=promotion_team_count,json=promotionTeamCount" json:"promotion_team_count"`
}

func (m *GetLeagueInfoResp) Reset()                    { *m = GetLeagueInfoResp{} }
func (m *GetLeagueInfoResp) String() string            { return proto.CompactTextString(m) }
func (*GetLeagueInfoResp) ProtoMessage()               {}
func (*GetLeagueInfoResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{28} }

func (m *GetLeagueInfoResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetLeagueInfoResp) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetLeagueInfoResp) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *GetLeagueInfoResp) GetLeagueState() uint32 {
	if m != nil {
		return m.LeagueState
	}
	return 0
}

func (m *GetLeagueInfoResp) GetTimeFrom() uint64 {
	if m != nil {
		return m.TimeFrom
	}
	return 0
}

func (m *GetLeagueInfoResp) GetTimeTo() uint64 {
	if m != nil {
		return m.TimeTo
	}
	return 0
}

func (m *GetLeagueInfoResp) GetRegularMatchRound() uint32 {
	if m != nil {
		return m.RegularMatchRound
	}
	return 0
}

func (m *GetLeagueInfoResp) GetPromotionTeamCount() uint32 {
	if m != nil {
		return m.PromotionTeamCount
	}
	return 0
}

// 更新上传的比赛截图
type UploadSnapshotReq struct {
	LeagueId    uint32   `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TeamId      uint32   `protobuf:"varint,2,req,name=team_id,json=teamId" json:"team_id"`
	ImgList     []string `protobuf:"bytes,3,rep,name=img_list,json=imgList" json:"img_list,omitempty"`
	MatchType   uint32   `protobuf:"varint,4,opt,name=match_type,json=matchType" json:"match_type"`
	MatchRound  uint32   `protobuf:"varint,5,opt,name=match_round,json=matchRound" json:"match_round"`
	ForceUpdate bool     `protobuf:"varint,6,req,name=force_update,json=forceUpdate" json:"force_update"`
}

func (m *UploadSnapshotReq) Reset()                    { *m = UploadSnapshotReq{} }
func (m *UploadSnapshotReq) String() string            { return proto.CompactTextString(m) }
func (*UploadSnapshotReq) ProtoMessage()               {}
func (*UploadSnapshotReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{29} }

func (m *UploadSnapshotReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *UploadSnapshotReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *UploadSnapshotReq) GetImgList() []string {
	if m != nil {
		return m.ImgList
	}
	return nil
}

func (m *UploadSnapshotReq) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *UploadSnapshotReq) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *UploadSnapshotReq) GetForceUpdate() bool {
	if m != nil {
		return m.ForceUpdate
	}
	return false
}

type GetSnapshotReq struct {
	LeagueId      uint32    `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TimestampFrom uint64    `protobuf:"varint,2,opt,name=timestamp_from,json=timestampFrom" json:"timestamp_from"`
	TimestampTo   uint64    `protobuf:"varint,3,opt,name=timestamp_to,json=timestampTo" json:"timestamp_to"`
	TeamId        uint32    `protobuf:"varint,4,opt,name=team_id,json=teamId" json:"team_id"`
	MatchType     uint32    `protobuf:"varint,5,opt,name=match_type,json=matchType" json:"match_type"`
	MatchRound    uint32    `protobuf:"varint,6,opt,name=match_round,json=matchRound" json:"match_round"`
	GroupType     GroupType `protobuf:"varint,7,opt,name=group_type,json=groupType,enum=leaguesvr.GroupType" json:"group_type"`
}

func (m *GetSnapshotReq) Reset()                    { *m = GetSnapshotReq{} }
func (m *GetSnapshotReq) String() string            { return proto.CompactTextString(m) }
func (*GetSnapshotReq) ProtoMessage()               {}
func (*GetSnapshotReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{30} }

func (m *GetSnapshotReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetSnapshotReq) GetTimestampFrom() uint64 {
	if m != nil {
		return m.TimestampFrom
	}
	return 0
}

func (m *GetSnapshotReq) GetTimestampTo() uint64 {
	if m != nil {
		return m.TimestampTo
	}
	return 0
}

func (m *GetSnapshotReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *GetSnapshotReq) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetSnapshotReq) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *GetSnapshotReq) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_FINAL
}

type GetSnapshotResp struct {
	ResultSetList []*LeagueResultSet `protobuf:"bytes,1,rep,name=result_set_list,json=resultSetList" json:"result_set_list,omitempty"`
}

func (m *GetSnapshotResp) Reset()                    { *m = GetSnapshotResp{} }
func (m *GetSnapshotResp) String() string            { return proto.CompactTextString(m) }
func (*GetSnapshotResp) ProtoMessage()               {}
func (*GetSnapshotResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{31} }

func (m *GetSnapshotResp) GetResultSetList() []*LeagueResultSet {
	if m != nil {
		return m.ResultSetList
	}
	return nil
}

type LeagueTeamCount struct {
	GroupType GroupType `protobuf:"varint,1,req,name=group_type,json=groupType,enum=leaguesvr.GroupType" json:"group_type"`
	Count     uint32    `protobuf:"varint,2,req,name=count" json:"count"`
}

func (m *LeagueTeamCount) Reset()                    { *m = LeagueTeamCount{} }
func (m *LeagueTeamCount) String() string            { return proto.CompactTextString(m) }
func (*LeagueTeamCount) ProtoMessage()               {}
func (*LeagueTeamCount) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{32} }

func (m *LeagueTeamCount) GetGroupType() GroupType {
	if m != nil {
		return m.GroupType
	}
	return GroupType_GROUP_FINAL
}

func (m *LeagueTeamCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetLeagueTeamCountReq struct {
	LeagueId   uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	MatchType  uint32 `protobuf:"varint,2,opt,name=match_type,json=matchType" json:"match_type"`
	MatchRound uint32 `protobuf:"varint,3,opt,name=match_round,json=matchRound" json:"match_round"`
}

func (m *GetLeagueTeamCountReq) Reset()                    { *m = GetLeagueTeamCountReq{} }
func (m *GetLeagueTeamCountReq) String() string            { return proto.CompactTextString(m) }
func (*GetLeagueTeamCountReq) ProtoMessage()               {}
func (*GetLeagueTeamCountReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{33} }

func (m *GetLeagueTeamCountReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetLeagueTeamCountReq) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetLeagueTeamCountReq) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

type GetLeagueTeamCountResp struct {
	TeamCountList []*LeagueTeamCount `protobuf:"bytes,1,rep,name=team_count_list,json=teamCountList" json:"team_count_list,omitempty"`
}

func (m *GetLeagueTeamCountResp) Reset()                    { *m = GetLeagueTeamCountResp{} }
func (m *GetLeagueTeamCountResp) String() string            { return proto.CompactTextString(m) }
func (*GetLeagueTeamCountResp) ProtoMessage()               {}
func (*GetLeagueTeamCountResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{34} }

func (m *GetLeagueTeamCountResp) GetTeamCountList() []*LeagueTeamCount {
	if m != nil {
		return m.TeamCountList
	}
	return nil
}

type GetExtraMatchTeamReq struct {
	LeagueId uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
}

func (m *GetExtraMatchTeamReq) Reset()                    { *m = GetExtraMatchTeamReq{} }
func (m *GetExtraMatchTeamReq) String() string            { return proto.CompactTextString(m) }
func (*GetExtraMatchTeamReq) ProtoMessage()               {}
func (*GetExtraMatchTeamReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{35} }

func (m *GetExtraMatchTeamReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

type ExtraMatchTeamSet struct {
	GroupType uint32   `protobuf:"varint,1,req,name=group_type,json=groupType" json:"group_type"`
	Quota     uint32   `protobuf:"varint,2,opt,name=quota" json:"quota"`
	TeamList  []uint32 `protobuf:"varint,3,rep,name=team_list,json=teamList" json:"team_list,omitempty"`
}

func (m *ExtraMatchTeamSet) Reset()                    { *m = ExtraMatchTeamSet{} }
func (m *ExtraMatchTeamSet) String() string            { return proto.CompactTextString(m) }
func (*ExtraMatchTeamSet) ProtoMessage()               {}
func (*ExtraMatchTeamSet) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{36} }

func (m *ExtraMatchTeamSet) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *ExtraMatchTeamSet) GetQuota() uint32 {
	if m != nil {
		return m.Quota
	}
	return 0
}

func (m *ExtraMatchTeamSet) GetTeamList() []uint32 {
	if m != nil {
		return m.TeamList
	}
	return nil
}

type GetExtraMatchTeamResp struct {
	TeamSetList []*ExtraMatchTeamSet `protobuf:"bytes,1,rep,name=team_set_list,json=teamSetList" json:"team_set_list,omitempty"`
}

func (m *GetExtraMatchTeamResp) Reset()                    { *m = GetExtraMatchTeamResp{} }
func (m *GetExtraMatchTeamResp) String() string            { return proto.CompactTextString(m) }
func (*GetExtraMatchTeamResp) ProtoMessage()               {}
func (*GetExtraMatchTeamResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{37} }

func (m *GetExtraMatchTeamResp) GetTeamSetList() []*ExtraMatchTeamSet {
	if m != nil {
		return m.TeamSetList
	}
	return nil
}

type SetNextMatchTypeReq struct {
	LeagueId    uint32               `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	TeamSetList []*ExtraMatchTeamSet `protobuf:"bytes,2,rep,name=team_set_list,json=teamSetList" json:"team_set_list,omitempty"`
	TimeFrom    uint64               `protobuf:"varint,3,req,name=time_from,json=timeFrom" json:"time_from"`
	TimeTo      uint64               `protobuf:"varint,4,req,name=time_to,json=timeTo" json:"time_to"`
}

func (m *SetNextMatchTypeReq) Reset()                    { *m = SetNextMatchTypeReq{} }
func (m *SetNextMatchTypeReq) String() string            { return proto.CompactTextString(m) }
func (*SetNextMatchTypeReq) ProtoMessage()               {}
func (*SetNextMatchTypeReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{38} }

func (m *SetNextMatchTypeReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *SetNextMatchTypeReq) GetTeamSetList() []*ExtraMatchTeamSet {
	if m != nil {
		return m.TeamSetList
	}
	return nil
}

func (m *SetNextMatchTypeReq) GetTimeFrom() uint64 {
	if m != nil {
		return m.TimeFrom
	}
	return 0
}

func (m *SetNextMatchTypeReq) GetTimeTo() uint64 {
	if m != nil {
		return m.TimeTo
	}
	return 0
}

type GetAllMatchByStateReq struct {
	LeagueId   uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
	MatchType  uint32 `protobuf:"varint,2,req,name=match_type,json=matchType" json:"match_type"`
	MatchRound uint32 `protobuf:"varint,3,req,name=match_round,json=matchRound" json:"match_round"`
	GroupType  uint32 `protobuf:"varint,4,opt,name=group_type,json=groupType" json:"group_type"`
	FromIndex  uint32 `protobuf:"varint,5,opt,name=from_index,json=fromIndex" json:"from_index"`
	ToIndex    uint32 `protobuf:"varint,6,opt,name=to_index,json=toIndex" json:"to_index"`
}

func (m *GetAllMatchByStateReq) Reset()                    { *m = GetAllMatchByStateReq{} }
func (m *GetAllMatchByStateReq) String() string            { return proto.CompactTextString(m) }
func (*GetAllMatchByStateReq) ProtoMessage()               {}
func (*GetAllMatchByStateReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{39} }

func (m *GetAllMatchByStateReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func (m *GetAllMatchByStateReq) GetMatchType() uint32 {
	if m != nil {
		return m.MatchType
	}
	return 0
}

func (m *GetAllMatchByStateReq) GetMatchRound() uint32 {
	if m != nil {
		return m.MatchRound
	}
	return 0
}

func (m *GetAllMatchByStateReq) GetGroupType() uint32 {
	if m != nil {
		return m.GroupType
	}
	return 0
}

func (m *GetAllMatchByStateReq) GetFromIndex() uint32 {
	if m != nil {
		return m.FromIndex
	}
	return 0
}

func (m *GetAllMatchByStateReq) GetToIndex() uint32 {
	if m != nil {
		return m.ToIndex
	}
	return 0
}

type GetAllMatchByStateResp struct {
	ResultSetList []*LeagueResultSet `protobuf:"bytes,1,rep,name=result_set_list,json=resultSetList" json:"result_set_list,omitempty"`
}

func (m *GetAllMatchByStateResp) Reset()                    { *m = GetAllMatchByStateResp{} }
func (m *GetAllMatchByStateResp) String() string            { return proto.CompactTextString(m) }
func (*GetAllMatchByStateResp) ProtoMessage()               {}
func (*GetAllMatchByStateResp) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{40} }

func (m *GetAllMatchByStateResp) GetResultSetList() []*LeagueResultSet {
	if m != nil {
		return m.ResultSetList
	}
	return nil
}

type AuditNotResultMatchReq struct {
	LeagueId uint32 `protobuf:"varint,1,req,name=league_id,json=leagueId" json:"league_id"`
}

func (m *AuditNotResultMatchReq) Reset()                    { *m = AuditNotResultMatchReq{} }
func (m *AuditNotResultMatchReq) String() string            { return proto.CompactTextString(m) }
func (*AuditNotResultMatchReq) ProtoMessage()               {}
func (*AuditNotResultMatchReq) Descriptor() ([]byte, []int) { return fileDescriptorLeaguesvr, []int{41} }

func (m *AuditNotResultMatchReq) GetLeagueId() uint32 {
	if m != nil {
		return m.LeagueId
	}
	return 0
}

func init() {
	proto.RegisterType((*CreateLeagueReq)(nil), "leaguesvr.CreateLeagueReq")
	proto.RegisterType((*CreateLeagueResp)(nil), "leaguesvr.CreateLeagueResp")
	proto.RegisterType((*CreateLeagueTeamReq)(nil), "leaguesvr.CreateLeagueTeamReq")
	proto.RegisterType((*LeagueResult)(nil), "leaguesvr.LeagueResult")
	proto.RegisterType((*LeagueResultSet)(nil), "leaguesvr.LeagueResultSet")
	proto.RegisterType((*AddLeagueResultReq)(nil), "leaguesvr.AddLeagueResultReq")
	proto.RegisterType((*GetTeamResultReq)(nil), "leaguesvr.GetTeamResultReq")
	proto.RegisterType((*GetTeamResultResp)(nil), "leaguesvr.GetTeamResultResp")
	proto.RegisterType((*GetAllTeamScoreReq)(nil), "leaguesvr.GetAllTeamScoreReq")
	proto.RegisterType((*TeamScore)(nil), "leaguesvr.TeamScore")
	proto.RegisterType((*TeamScoreSet)(nil), "leaguesvr.TeamScoreSet")
	proto.RegisterType((*GetAllTeamScoreResp)(nil), "leaguesvr.GetAllTeamScoreResp")
	proto.RegisterType((*GetTeamScoreByIdReq)(nil), "leaguesvr.GetTeamScoreByIdReq")
	proto.RegisterType((*GetTeamScoreByIdResp)(nil), "leaguesvr.GetTeamScoreByIdResp")
	proto.RegisterType((*GetLeagueResultByStateReq)(nil), "leaguesvr.GetLeagueResultByStateReq")
	proto.RegisterType((*GetLeagueResultByStateResp)(nil), "leaguesvr.GetLeagueResultByStateResp")
	proto.RegisterType((*GetLeagueResultByIdReq)(nil), "leaguesvr.GetLeagueResultByIdReq")
	proto.RegisterType((*GetLeagueResultByIdResp)(nil), "leaguesvr.GetLeagueResultByIdResp")
	proto.RegisterType((*LeagueSchedule)(nil), "leaguesvr.LeagueSchedule")
	proto.RegisterType((*LeagueScheduleSet)(nil), "leaguesvr.LeagueScheduleSet")
	proto.RegisterType((*GetScheduleByTeamIdReq)(nil), "leaguesvr.GetScheduleByTeamIdReq")
	proto.RegisterType((*GetScheduleByTeamIdResp)(nil), "leaguesvr.GetScheduleByTeamIdResp")
	proto.RegisterType((*GetScheduleByDateReq)(nil), "leaguesvr.GetScheduleByDateReq")
	proto.RegisterType((*GetScheduleByDateResp)(nil), "leaguesvr.GetScheduleByDateResp")
	proto.RegisterType((*GetLeagueScheduleByStateReq)(nil), "leaguesvr.GetLeagueScheduleByStateReq")
	proto.RegisterType((*GetLeagueScheduleByStateResp)(nil), "leaguesvr.GetLeagueScheduleByStateResp")
	proto.RegisterType((*GenLeagueScheduleReq)(nil), "leaguesvr.GenLeagueScheduleReq")
	proto.RegisterType((*GetLeagueInfoReq)(nil), "leaguesvr.GetLeagueInfoReq")
	proto.RegisterType((*GetLeagueInfoResp)(nil), "leaguesvr.GetLeagueInfoResp")
	proto.RegisterType((*UploadSnapshotReq)(nil), "leaguesvr.UploadSnapshotReq")
	proto.RegisterType((*GetSnapshotReq)(nil), "leaguesvr.GetSnapshotReq")
	proto.RegisterType((*GetSnapshotResp)(nil), "leaguesvr.GetSnapshotResp")
	proto.RegisterType((*LeagueTeamCount)(nil), "leaguesvr.LeagueTeamCount")
	proto.RegisterType((*GetLeagueTeamCountReq)(nil), "leaguesvr.GetLeagueTeamCountReq")
	proto.RegisterType((*GetLeagueTeamCountResp)(nil), "leaguesvr.GetLeagueTeamCountResp")
	proto.RegisterType((*GetExtraMatchTeamReq)(nil), "leaguesvr.GetExtraMatchTeamReq")
	proto.RegisterType((*ExtraMatchTeamSet)(nil), "leaguesvr.ExtraMatchTeamSet")
	proto.RegisterType((*GetExtraMatchTeamResp)(nil), "leaguesvr.GetExtraMatchTeamResp")
	proto.RegisterType((*SetNextMatchTypeReq)(nil), "leaguesvr.SetNextMatchTypeReq")
	proto.RegisterType((*GetAllMatchByStateReq)(nil), "leaguesvr.GetAllMatchByStateReq")
	proto.RegisterType((*GetAllMatchByStateResp)(nil), "leaguesvr.GetAllMatchByStateResp")
	proto.RegisterType((*AuditNotResultMatchReq)(nil), "leaguesvr.AuditNotResultMatchReq")
	proto.RegisterEnum("leaguesvr.GroupType", GroupType_name, GroupType_value)
	proto.RegisterEnum("leaguesvr.LeagueState", LeagueState_name, LeagueState_value)
	proto.RegisterEnum("leaguesvr.MatchState", MatchState_name, MatchState_value)
}
func (m *CreateLeagueReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateLeagueReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeFrom))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeTo))
	return i, nil
}

func (m *CreateLeagueResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateLeagueResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	return i, nil
}

func (m *CreateLeagueTeamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CreateLeagueTeamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x20
	i++
	if m.IsSeed {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *LeagueResult) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LeagueResult) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamFrom))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamTo))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.FromWins))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.ToWins))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	if len(m.FromImgList) > 0 {
		for _, s := range m.FromImgList {
			dAtA[i] = 0x3a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.ToImgList) > 0 {
		for _, s := range m.ToImgList {
			dAtA[i] = 0x42
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x48
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.AuditState))
	dAtA[i] = 0x50
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeFrom))
	dAtA[i] = 0x58
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeTo))
	dAtA[i] = 0x60
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchState))
	dAtA[i] = 0x68
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchId))
	return i, nil
}

func (m *LeagueResultSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LeagueResultSet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	if len(m.ResultList) > 0 {
		for _, msg := range m.ResultList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddLeagueResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddLeagueResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	if m.Result == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("result")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Result.Size()))
		n1, err := m.Result.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetTeamResultReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTeamResultReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	return i, nil
}

func (m *GetTeamResultResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTeamResultResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, msg := range m.ResultList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Score))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Win))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Lose))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Draw))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x38
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x40
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	return i, nil
}

func (m *GetAllTeamScoreReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTeamScoreReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.ToIndex))
	return i, nil
}

func (m *TeamScore) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TeamScore) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Score))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Win))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Lose))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Draw))
	return i, nil
}

func (m *TeamScoreSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TeamScoreSet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	if len(m.TeamScoreList) > 0 {
		for _, msg := range m.TeamScoreList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetAllTeamScoreResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllTeamScoreResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TeamScoreSetList) > 0 {
		for _, msg := range m.TeamScoreSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetTeamScoreByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTeamScoreByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	if len(m.TeamIdList) > 0 {
		for _, num := range m.TeamIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetTeamScoreByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetTeamScoreByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TeamScoreList) > 0 {
		for _, msg := range m.TeamScoreList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLeagueResultByStateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueResultByStateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.ToIndex))
	return i, nil
}

func (m *GetLeagueResultByStateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueResultByStateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultSetList) > 0 {
		for _, msg := range m.ResultSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLeagueResultByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueResultByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	return i, nil
}

func (m *GetLeagueResultByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueResultByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.LeagueResult == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("league_result")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueResult.Size()))
		n2, err := m.LeagueResult.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *LeagueSchedule) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LeagueSchedule) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamFrom))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamTo))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimestampFrom))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimestampTo))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	return i, nil
}

func (m *LeagueScheduleSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LeagueScheduleSet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	if len(m.ScheduleList) > 0 {
		for _, msg := range m.ScheduleList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetScheduleByTeamIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScheduleByTeamIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	return i, nil
}

func (m *GetScheduleByTeamIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScheduleByTeamIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ScheduleList) > 0 {
		for _, msg := range m.ScheduleList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetScheduleByDateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScheduleByDateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Timestamp))
	return i, nil
}

func (m *GetScheduleByDateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetScheduleByDateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ScheduleSetList) > 0 {
		for _, msg := range m.ScheduleSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetLeagueScheduleByStateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueScheduleByStateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.ToIndex))
	dAtA[i] = 0x28
	i++
	if m.ForceGet {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	if m.TimeLimit {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetLeagueScheduleByStateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueScheduleByStateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ScheduleSetList) > 0 {
		for _, msg := range m.ScheduleSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GenLeagueScheduleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GenLeagueScheduleReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimestampFrom))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimestampTo))
	return i, nil
}

func (m *GetLeagueInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	return i, nil
}

func (m *GetLeagueInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueState))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeFrom))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeTo))
	dAtA[i] = 0x38
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.RegularMatchRound))
	dAtA[i] = 0x40
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.PromotionTeamCount))
	return i, nil
}

func (m *UploadSnapshotReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UploadSnapshotReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			dAtA[i] = 0x1a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x30
	i++
	if m.ForceUpdate {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetSnapshotReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSnapshotReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimestampFrom))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimestampTo))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TeamId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x38
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	return i, nil
}

func (m *GetSnapshotResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSnapshotResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultSetList) > 0 {
		for _, msg := range m.ResultSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *LeagueTeamCount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LeagueTeamCount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetLeagueTeamCountReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueTeamCountReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	return i, nil
}

func (m *GetLeagueTeamCountResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetLeagueTeamCountResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TeamCountList) > 0 {
		for _, msg := range m.TeamCountList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetExtraMatchTeamReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetExtraMatchTeamReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	return i, nil
}

func (m *ExtraMatchTeamSet) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtraMatchTeamSet) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.Quota))
	if len(m.TeamList) > 0 {
		for _, num := range m.TeamList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetExtraMatchTeamResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetExtraMatchTeamResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TeamSetList) > 0 {
		for _, msg := range m.TeamSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SetNextMatchTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetNextMatchTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	if len(m.TeamSetList) > 0 {
		for _, msg := range m.TeamSetList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeFrom))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.TimeTo))
	return i, nil
}

func (m *GetAllMatchByStateReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllMatchByStateReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.MatchRound))
	dAtA[i] = 0x20
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.GroupType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.FromIndex))
	dAtA[i] = 0x30
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.ToIndex))
	return i, nil
}

func (m *GetAllMatchByStateResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAllMatchByStateResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ResultSetList) > 0 {
		for _, msg := range m.ResultSetList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintLeaguesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AuditNotResultMatchReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AuditNotResultMatchReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintLeaguesvr(dAtA, i, uint64(m.LeagueId))
	return i, nil
}

func encodeFixed64Leaguesvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Leaguesvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintLeaguesvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CreateLeagueReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GameId))
	n += 1 + sovLeaguesvr(uint64(m.TimeFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimeTo))
	return n
}

func (m *CreateLeagueResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	return n
}

func (m *CreateLeagueTeamReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 2
	return n
}

func (m *LeagueResult) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.TeamFrom))
	n += 1 + sovLeaguesvr(uint64(m.TeamTo))
	n += 1 + sovLeaguesvr(uint64(m.FromWins))
	n += 1 + sovLeaguesvr(uint64(m.ToWins))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	if len(m.FromImgList) > 0 {
		for _, s := range m.FromImgList {
			l = len(s)
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	if len(m.ToImgList) > 0 {
		for _, s := range m.ToImgList {
			l = len(s)
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	n += 1 + sovLeaguesvr(uint64(m.AuditState))
	n += 1 + sovLeaguesvr(uint64(m.TimeFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimeTo))
	n += 1 + sovLeaguesvr(uint64(m.MatchState))
	n += 1 + sovLeaguesvr(uint64(m.MatchId))
	return n
}

func (m *LeagueResultSet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	if len(m.ResultList) > 0 {
		for _, e := range m.ResultList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *AddLeagueResultReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	if m.Result != nil {
		l = m.Result.Size()
		n += 1 + l + sovLeaguesvr(uint64(l))
	}
	return n
}

func (m *GetTeamResultReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	return n
}

func (m *GetTeamResultResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultList) > 0 {
		for _, e := range m.ResultList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	n += 1 + sovLeaguesvr(uint64(m.Score))
	n += 1 + sovLeaguesvr(uint64(m.Win))
	n += 1 + sovLeaguesvr(uint64(m.Lose))
	n += 1 + sovLeaguesvr(uint64(m.Draw))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	return n
}

func (m *GetAllTeamScoreReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.FromIndex))
	n += 1 + sovLeaguesvr(uint64(m.ToIndex))
	return n
}

func (m *TeamScore) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	n += 1 + sovLeaguesvr(uint64(m.Score))
	n += 1 + sovLeaguesvr(uint64(m.Win))
	n += 1 + sovLeaguesvr(uint64(m.Lose))
	n += 1 + sovLeaguesvr(uint64(m.Draw))
	return n
}

func (m *TeamScoreSet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	if len(m.TeamScoreList) > 0 {
		for _, e := range m.TeamScoreList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetAllTeamScoreResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TeamScoreSetList) > 0 {
		for _, e := range m.TeamScoreSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetTeamScoreByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	if len(m.TeamIdList) > 0 {
		for _, e := range m.TeamIdList {
			n += 1 + sovLeaguesvr(uint64(e))
		}
	}
	return n
}

func (m *GetTeamScoreByIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TeamScoreList) > 0 {
		for _, e := range m.TeamScoreList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetLeagueResultByStateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.FromIndex))
	n += 1 + sovLeaguesvr(uint64(m.ToIndex))
	return n
}

func (m *GetLeagueResultByStateResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultSetList) > 0 {
		for _, e := range m.ResultSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetLeagueResultByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	return n
}

func (m *GetLeagueResultByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.LeagueResult != nil {
		l = m.LeagueResult.Size()
		n += 1 + l + sovLeaguesvr(uint64(l))
	}
	return n
}

func (m *LeagueSchedule) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.TeamFrom))
	n += 1 + sovLeaguesvr(uint64(m.TeamTo))
	n += 1 + sovLeaguesvr(uint64(m.TimestampFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimestampTo))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	return n
}

func (m *LeagueScheduleSet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	if len(m.ScheduleList) > 0 {
		for _, e := range m.ScheduleList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetScheduleByTeamIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	return n
}

func (m *GetScheduleByTeamIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ScheduleList) > 0 {
		for _, e := range m.ScheduleList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetScheduleByDateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.Timestamp))
	return n
}

func (m *GetScheduleByDateResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ScheduleSetList) > 0 {
		for _, e := range m.ScheduleSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetLeagueScheduleByStateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.FromIndex))
	n += 1 + sovLeaguesvr(uint64(m.ToIndex))
	n += 2
	n += 2
	return n
}

func (m *GetLeagueScheduleByStateResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ScheduleSetList) > 0 {
		for _, e := range m.ScheduleSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GenLeagueScheduleReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.TimestampFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimestampTo))
	return n
}

func (m *GetLeagueInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	return n
}

func (m *GetLeagueInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GameId))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 1 + sovLeaguesvr(uint64(m.LeagueState))
	n += 1 + sovLeaguesvr(uint64(m.TimeFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimeTo))
	n += 1 + sovLeaguesvr(uint64(m.RegularMatchRound))
	n += 1 + sovLeaguesvr(uint64(m.PromotionTeamCount))
	return n
}

func (m *UploadSnapshotReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	if len(m.ImgList) > 0 {
		for _, s := range m.ImgList {
			l = len(s)
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 2
	return n
}

func (m *GetSnapshotReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.TimestampFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimestampTo))
	n += 1 + sovLeaguesvr(uint64(m.TeamId))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	return n
}

func (m *GetSnapshotResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultSetList) > 0 {
		for _, e := range m.ResultSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *LeagueTeamCount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.Count))
	return n
}

func (m *GetLeagueTeamCountReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	return n
}

func (m *GetLeagueTeamCountResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TeamCountList) > 0 {
		for _, e := range m.TeamCountList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *GetExtraMatchTeamReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	return n
}

func (m *ExtraMatchTeamSet) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.Quota))
	if len(m.TeamList) > 0 {
		for _, e := range m.TeamList {
			n += 1 + sovLeaguesvr(uint64(e))
		}
	}
	return n
}

func (m *GetExtraMatchTeamResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TeamSetList) > 0 {
		for _, e := range m.TeamSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *SetNextMatchTypeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	if len(m.TeamSetList) > 0 {
		for _, e := range m.TeamSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	n += 1 + sovLeaguesvr(uint64(m.TimeFrom))
	n += 1 + sovLeaguesvr(uint64(m.TimeTo))
	return n
}

func (m *GetAllMatchByStateReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	n += 1 + sovLeaguesvr(uint64(m.MatchType))
	n += 1 + sovLeaguesvr(uint64(m.MatchRound))
	n += 1 + sovLeaguesvr(uint64(m.GroupType))
	n += 1 + sovLeaguesvr(uint64(m.FromIndex))
	n += 1 + sovLeaguesvr(uint64(m.ToIndex))
	return n
}

func (m *GetAllMatchByStateResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ResultSetList) > 0 {
		for _, e := range m.ResultSetList {
			l = e.Size()
			n += 1 + l + sovLeaguesvr(uint64(l))
		}
	}
	return n
}

func (m *AuditNotResultMatchReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovLeaguesvr(uint64(m.LeagueId))
	return n
}

func sovLeaguesvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozLeaguesvr(x uint64) (n int) {
	return sovLeaguesvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CreateLeagueReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateLeagueReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateLeagueReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeFrom", wireType)
			}
			m.TimeFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeTo", wireType)
			}
			m.TimeTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time_from")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time_to")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateLeagueResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateLeagueResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateLeagueResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CreateLeagueTeamReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CreateLeagueTeamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CreateLeagueTeamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsSeed", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsSeed = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_seed")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LeagueResult) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LeagueResult: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LeagueResult: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamFrom", wireType)
			}
			m.TeamFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamFrom |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamTo", wireType)
			}
			m.TeamTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamTo |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromWins", wireType)
			}
			m.FromWins = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromWins |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToWins", wireType)
			}
			m.ToWins = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToWins |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromImgList = append(m.FromImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToImgList = append(m.ToImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AuditState", wireType)
			}
			m.AuditState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AuditState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeFrom", wireType)
			}
			m.TimeFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeTo", wireType)
			}
			m.TimeTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchState", wireType)
			}
			m.MatchState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchId", wireType)
			}
			m.MatchId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_from")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_to")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LeagueResultSet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LeagueResultSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LeagueResultSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (GroupType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultList = append(m.ResultList, &LeagueResult{})
			if err := m.ResultList[len(m.ResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddLeagueResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddLeagueResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddLeagueResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Result", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Result == nil {
				m.Result = &LeagueResult{}
			}
			if err := m.Result.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTeamResultReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTeamResultReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTeamResultReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTeamResultResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTeamResultResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTeamResultResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultList = append(m.ResultList, &LeagueResult{})
			if err := m.ResultList[len(m.ResultList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Win", wireType)
			}
			m.Win = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Win |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Lose", wireType)
			}
			m.Lose = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Lose |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Draw", wireType)
			}
			m.Draw = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Draw |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("win")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lose")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("draw")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_round")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTeamScoreReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTeamScoreReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTeamScoreReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToIndex", wireType)
			}
			m.ToIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TeamScore) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TeamScore: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TeamScore: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Score", wireType)
			}
			m.Score = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Score |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Win", wireType)
			}
			m.Win = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Win |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Lose", wireType)
			}
			m.Lose = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Lose |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Draw", wireType)
			}
			m.Draw = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Draw |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("score")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("win")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("lose")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("draw")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TeamScoreSet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TeamScoreSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TeamScoreSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamScoreList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamScoreList = append(m.TeamScoreList, &TeamScore{})
			if err := m.TeamScoreList[len(m.TeamScoreList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllTeamScoreResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllTeamScoreResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllTeamScoreResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamScoreSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamScoreSetList = append(m.TeamScoreSetList, &TeamScoreSet{})
			if err := m.TeamScoreSetList[len(m.TeamScoreSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTeamScoreByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTeamScoreByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTeamScoreByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowLeaguesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TeamIdList = append(m.TeamIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowLeaguesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthLeaguesvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowLeaguesvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TeamIdList = append(m.TeamIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetTeamScoreByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetTeamScoreByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetTeamScoreByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamScoreList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamScoreList = append(m.TeamScoreList, &TeamScore{})
			if err := m.TeamScoreList[len(m.TeamScoreList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueResultByStateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueResultByStateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueResultByStateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToIndex", wireType)
			}
			m.ToIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueResultByStateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueResultByStateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueResultByStateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultSetList = append(m.ResultSetList, &LeagueResultSet{})
			if err := m.ResultSetList[len(m.ResultSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueResultByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueResultByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueResultByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueResultByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueResultByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueResultByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueResult", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.LeagueResult == nil {
				m.LeagueResult = &LeagueResult{}
			}
			if err := m.LeagueResult.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_result")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LeagueSchedule) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LeagueSchedule: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LeagueSchedule: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamFrom", wireType)
			}
			m.TeamFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamFrom |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamTo", wireType)
			}
			m.TeamTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamTo |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimestampFrom", wireType)
			}
			m.TimestampFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimestampTo", wireType)
			}
			m.TimestampTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_from")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_to")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp_from")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp_to")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_type")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_round")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LeagueScheduleSet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LeagueScheduleSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LeagueScheduleSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (GroupType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScheduleList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScheduleList = append(m.ScheduleList, &LeagueSchedule{})
			if err := m.ScheduleList[len(m.ScheduleList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScheduleByTeamIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScheduleByTeamIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScheduleByTeamIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScheduleByTeamIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScheduleByTeamIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScheduleByTeamIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScheduleList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScheduleList = append(m.ScheduleList, &LeagueSchedule{})
			if err := m.ScheduleList[len(m.ScheduleList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScheduleByDateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScheduleByDateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScheduleByDateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Timestamp", wireType)
			}
			m.Timestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Timestamp |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetScheduleByDateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetScheduleByDateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetScheduleByDateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScheduleSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScheduleSetList = append(m.ScheduleSetList, &LeagueScheduleSet{})
			if err := m.ScheduleSetList[len(m.ScheduleSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueScheduleByStateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueScheduleByStateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueScheduleByStateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToIndex", wireType)
			}
			m.ToIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceGet", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForceGet = bool(v != 0)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeLimit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.TimeLimit = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueScheduleByStateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueScheduleByStateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueScheduleByStateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ScheduleSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ScheduleSetList = append(m.ScheduleSetList, &LeagueScheduleSet{})
			if err := m.ScheduleSetList[len(m.ScheduleSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GenLeagueScheduleReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GenLeagueScheduleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GenLeagueScheduleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimestampFrom", wireType)
			}
			m.TimestampFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimestampTo", wireType)
			}
			m.TimestampTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp_from")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("timestamp_to")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueState", wireType)
			}
			m.LeagueState = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueState |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeFrom", wireType)
			}
			m.TimeFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeTo", wireType)
			}
			m.TimeTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RegularMatchRound", wireType)
			}
			m.RegularMatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RegularMatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PromotionTeamCount", wireType)
			}
			m.PromotionTeamCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PromotionTeamCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_round")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_state")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time_from")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time_to")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("regular_match_round")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("promotion_team_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UploadSnapshotReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UploadSnapshotReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UploadSnapshotReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgList = append(m.ImgList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceUpdate", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForceUpdate = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("team_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("force_update")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSnapshotReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSnapshotReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSnapshotReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimestampFrom", wireType)
			}
			m.TimestampFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimestampTo", wireType)
			}
			m.TimestampTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimestampTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamId", wireType)
			}
			m.TeamId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TeamId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (GroupType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSnapshotResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSnapshotResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSnapshotResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultSetList = append(m.ResultSetList, &LeagueResultSet{})
			if err := m.ResultSetList[len(m.ResultSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LeagueTeamCount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LeagueTeamCount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LeagueTeamCount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (GroupType(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueTeamCountReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueTeamCountReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueTeamCountReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetLeagueTeamCountResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetLeagueTeamCountResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetLeagueTeamCountResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamCountList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamCountList = append(m.TeamCountList, &LeagueTeamCount{})
			if err := m.TeamCountList[len(m.TeamCountList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetExtraMatchTeamReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetExtraMatchTeamReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetExtraMatchTeamReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExtraMatchTeamSet) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExtraMatchTeamSet: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExtraMatchTeamSet: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Quota", wireType)
			}
			m.Quota = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Quota |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowLeaguesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TeamList = append(m.TeamList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowLeaguesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthLeaguesvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowLeaguesvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TeamList = append(m.TeamList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetExtraMatchTeamResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetExtraMatchTeamResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetExtraMatchTeamResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamSetList = append(m.TeamSetList, &ExtraMatchTeamSet{})
			if err := m.TeamSetList[len(m.TeamSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetNextMatchTypeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetNextMatchTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetNextMatchTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TeamSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TeamSetList = append(m.TeamSetList, &ExtraMatchTeamSet{})
			if err := m.TeamSetList[len(m.TeamSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeFrom", wireType)
			}
			m.TimeFrom = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeFrom |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeTo", wireType)
			}
			m.TimeTo = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TimeTo |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time_from")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("time_to")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllMatchByStateReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllMatchByStateReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllMatchByStateReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchType", wireType)
			}
			m.MatchType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MatchRound", wireType)
			}
			m.MatchRound = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MatchRound |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupType", wireType)
			}
			m.GroupType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromIndex", wireType)
			}
			m.FromIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToIndex", wireType)
			}
			m.ToIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToIndex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("match_round")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAllMatchByStateResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAllMatchByStateResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAllMatchByStateResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResultSetList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ResultSetList = append(m.ResultSetList, &LeagueResultSet{})
			if err := m.ResultSetList[len(m.ResultSetList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AuditNotResultMatchReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AuditNotResultMatchReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AuditNotResultMatchReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeagueId", wireType)
			}
			m.LeagueId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeagueId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipLeaguesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthLeaguesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("league_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipLeaguesvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowLeaguesvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowLeaguesvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthLeaguesvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowLeaguesvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipLeaguesvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthLeaguesvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowLeaguesvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("services/league/leaguesvr.proto", fileDescriptorLeaguesvr) }

var fileDescriptorLeaguesvr = []byte{
	// 2593 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0x4d, 0x6c, 0x1c, 0x49,
	0x15, 0x4e, 0xcf, 0x9f, 0x67, 0xde, 0xcc, 0xd8, 0xe3, 0x72, 0xd8, 0x4c, 0x26, 0x59, 0xbb, 0xd3,
	0xd9, 0x6c, 0x4c, 0x60, 0x1c, 0x13, 0x20, 0x6c, 0xcc, 0x30, 0x60, 0x27, 0x6b, 0x63, 0x91, 0x5f,
	0xdb, 0x51, 0xd8, 0xd5, 0x46, 0xc3, 0xac, 0xbb, 0x76, 0xdc, 0xa4, 0x67, 0xba, 0xdd, 0x5d, 0x13,
	0xc7, 0x2b, 0x0e, 0x88, 0x43, 0x84, 0x10, 0x5a, 0xd0, 0x72, 0x42, 0x7b, 0x02, 0x45, 0x5c, 0x38,
	0xec, 0x95, 0x03, 0x9c, 0x59, 0x09, 0x0e, 0x48, 0x08, 0x71, 0x43, 0x28, 0x08, 0x29, 0x27, 0x24,
	0xc4, 0xcf, 0x19, 0xbd, 0xaa, 0x9e, 0x9e, 0xea, 0x9f, 0x99, 0xe9, 0x10, 0x2b, 0x92, 0x25, 0x4f,
	0x57, 0xbd, 0xae, 0xfa, 0xde, 0xab, 0xf7, 0xbe, 0xf7, 0x5e, 0x35, 0x2c, 0xb8, 0xd4, 0x79, 0x68,
	0xec, 0x52, 0xf7, 0xa2, 0x49, 0xdb, 0x9d, 0x3e, 0xf5, 0xfe, 0xb9, 0x0f, 0x9d, 0x25, 0xdb, 0xb1,
	0x98, 0x45, 0x0a, 0xfe, 0x40, 0xed, 0xb5, 0x5d, 0xab, 0xdb, 0xb5, 0x7a, 0x17, 0x99, 0xf9, 0xd0,
	0x36, 0x76, 0x1f, 0x98, 0xf4, 0xa2, 0xfb, 0xe0, 0xdd, 0xbe, 0x61, 0x32, 0xa3, 0xc7, 0x0e, 0x6d,
	0x2a, 0x5e, 0xd0, 0x1c, 0x98, 0xb9, 0xea, 0xd0, 0x36, 0xa3, 0xd7, 0xf9, 0x8b, 0x5b, 0x74, 0x9f,
	0xbc, 0x0a, 0x53, 0x9d, 0x76, 0x97, 0xb6, 0x0c, 0xbd, 0xaa, 0xa8, 0xa9, 0xc5, 0xf2, 0x5a, 0xe6,
	0x93, 0xbf, 0x2c, 0x1c, 0xdb, 0xca, 0xe1, 0xe0, 0xa6, 0x4e, 0xce, 0x40, 0x81, 0x19, 0x5d, 0xda,
	0x7a, 0xcf, 0xb1, 0xba, 0xd5, 0x94, 0x9a, 0x5a, 0xcc, 0x78, 0x02, 0x79, 0x1c, 0x5e, 0x77, 0xac,
	0x2e, 0xae, 0xc0, 0x45, 0x98, 0x55, 0x4d, 0x4b, 0x02, 0x39, 0x1c, 0xdc, 0xb1, 0xb4, 0x2f, 0x42,
	0x25, 0xb8, 0xa7, 0x6b, 0xe3, 0xaa, 0x02, 0x7a, 0x78, 0xdb, 0xbc, 0x18, 0xde, 0xd4, 0xb5, 0x8f,
	0x14, 0x98, 0x93, 0xdf, 0xdb, 0xa1, 0xed, 0x2e, 0xe2, 0x9d, 0xfc, 0x2a, 0x07, 0x44, 0xdb, 0x5d,
	0x14, 0x48, 0xc9, 0x2a, 0xe1, 0xe0, 0xa6, 0x4e, 0xce, 0x02, 0x74, 0x1c, 0xab, 0x6f, 0xb7, 0xd0,
	0x30, 0x1c, 0xf2, 0x40, 0xa2, 0xc0, 0xc7, 0x77, 0x0e, 0x6d, 0x8a, 0x6b, 0x18, 0x6e, 0xcb, 0xa5,
	0x54, 0xaf, 0x66, 0xd4, 0xd4, 0x62, 0x7e, 0xb0, 0x86, 0xe1, 0x6e, 0x53, 0xaa, 0x6b, 0x7f, 0x4c,
	0x43, 0xc9, 0xd7, 0xa7, 0x6f, 0x32, 0x6e, 0x27, 0xdc, 0x93, 0xdb, 0x29, 0x00, 0x0b, 0x87, 0x7d,
	0x3b, 0xa1, 0x08, 0xb3, 0xa2, 0xb0, 0x76, 0x2c, 0x5c, 0x01, 0x5f, 0x6e, 0x1d, 0x18, 0x3d, 0xb7,
	0x9a, 0x56, 0x95, 0xe1, 0x0a, 0x38, 0x7c, 0xcf, 0xe8, 0xb9, 0x7c, 0x05, 0x4b, 0x08, 0x64, 0x24,
	0x81, 0x1c, 0xb3, 0xf8, 0xf4, 0x59, 0x80, 0x6e, 0x9b, 0xed, 0xee, 0x09, 0xc5, 0xb2, 0x92, 0x44,
	0x81, 0x8f, 0x73, 0xc5, 0xce, 0x41, 0x51, 0x08, 0x39, 0x56, 0xbf, 0xa7, 0x57, 0x73, 0x92, 0x94,
	0x78, 0x7b, 0x0b, 0xc7, 0x89, 0x06, 0x65, 0x8e, 0xc6, 0xe8, 0x76, 0x5a, 0xa6, 0xe1, 0xb2, 0xea,
	0x94, 0x9a, 0x5e, 0x2c, 0x6c, 0x15, 0x71, 0x70, 0xb3, 0xdb, 0xb9, 0x6e, 0xb8, 0x8c, 0xcc, 0x43,
	0x91, 0x59, 0x43, 0x89, 0x3c, 0x97, 0x28, 0x30, 0x6b, 0x30, 0x7f, 0x0e, 0x8a, 0xed, 0xbe, 0x6e,
	0xb0, 0x96, 0xcb, 0xda, 0x8c, 0x56, 0x0b, 0xf2, 0x56, 0x7c, 0x62, 0x1b, 0xc7, 0x83, 0x2e, 0x06,
	0xaa, 0x32, 0xde, 0xc5, 0x8a, 0x92, 0x80, 0xe7, 0x62, 0x43, 0x9d, 0xc4, 0x46, 0xa5, 0x88, 0x4e,
	0x62, 0xa3, 0x05, 0xc8, 0x0b, 0x31, 0x43, 0xaf, 0x96, 0x25, 0x99, 0x29, 0x3e, 0xba, 0xa9, 0x6b,
	0x8f, 0x15, 0x98, 0x91, 0x4f, 0x75, 0x9b, 0x32, 0x72, 0x25, 0xe0, 0x2d, 0x78, 0xb2, 0xd3, 0x97,
	0x8e, 0x2f, 0x0d, 0x23, 0x71, 0x63, 0xe0, 0x32, 0x51, 0x1f, 0x7a, 0x03, 0x8a, 0x0e, 0x5f, 0x47,
	0xd8, 0x27, 0xa5, 0xa6, 0x17, 0x8b, 0x97, 0x4e, 0x48, 0xef, 0xca, 0x7b, 0x6d, 0x81, 0x90, 0x45,
	0xcb, 0x69, 0x7b, 0x40, 0x56, 0x75, 0x3d, 0x30, 0x9d, 0xcc, 0xf5, 0x2f, 0x42, 0x4e, 0x2c, 0xc3,
	0x5d, 0x6c, 0xcc, 0x6e, 0x9e, 0x98, 0xb6, 0x03, 0x95, 0x0d, 0xca, 0x44, 0x70, 0x3d, 0xc7, 0x3e,
	0xe3, 0x43, 0x4c, 0xfb, 0x45, 0x0a, 0x66, 0x43, 0xcb, 0xba, 0x76, 0xd8, 0x1e, 0x4a, 0x62, 0x7b,
	0x90, 0x1a, 0x64, 0xdd, 0x5d, 0xcb, 0xa1, 0x81, 0xcd, 0xc4, 0x10, 0x79, 0x05, 0xd2, 0x07, 0x46,
	0x2f, 0x10, 0xc7, 0x38, 0x40, 0xaa, 0x90, 0x31, 0x2d, 0x97, 0xf2, 0xf0, 0x1d, 0x4c, 0xf0, 0x11,
	0x9c, 0xd1, 0x9d, 0xf6, 0x41, 0x35, 0x2b, 0xcf, 0xe0, 0x48, 0x28, 0x82, 0x72, 0x32, 0x35, 0x8c,
	0x8c, 0xa0, 0x29, 0x49, 0x4a, 0x8e, 0xa0, 0x20, 0xcd, 0xe4, 0x63, 0x69, 0x46, 0xfb, 0xb9, 0x02,
	0x64, 0x83, 0xb2, 0x55, 0xd3, 0x44, 0x5b, 0x6d, 0xa3, 0x42, 0x09, 0x4f, 0x20, 0xb8, 0x7c, 0x4a,
	0x0e, 0xf6, 0xa1, 0x07, 0x9e, 0x05, 0x10, 0x51, 0xdc, 0xd3, 0xe9, 0xa3, 0x00, 0xa9, 0x70, 0xae,
	0xd9, 0xc4, 0x61, 0x0c, 0x0b, 0x0c, 0x63, 0x2e, 0x22, 0xd3, 0xca, 0x14, 0xb3, 0xb8, 0x80, 0xf6,
	0xa1, 0x02, 0x05, 0x1f, 0x9e, 0x7c, 0xf4, 0x4a, 0x0c, 0xbb, 0xbe, 0xa4, 0xa3, 0xd2, 0xf6, 0xa1,
	0xe4, 0x63, 0xc2, 0x38, 0x3d, 0x1b, 0x89, 0xd3, 0x18, 0x7b, 0x34, 0x60, 0x86, 0x63, 0xe7, 0x70,
	0xe4, 0xa8, 0x94, 0x23, 0x7a, 0x78, 0x12, 0x65, 0x36, 0xf8, 0xc9, 0xa3, 0xf2, 0x3e, 0xcc, 0x45,
	0xce, 0xca, 0xb5, 0xc9, 0x3a, 0xcc, 0x49, 0x8b, 0xba, 0x74, 0xa4, 0x7b, 0xcb, 0x78, 0xb7, 0x2a,
	0x4c, 0x7a, 0xe2, 0xcb, 0xbf, 0xcd, 0x97, 0xf7, 0x85, 0xd6, 0x0e, 0x37, 0xf5, 0x84, 0xbe, 0xa0,
	0x42, 0xc9, 0x3b, 0x92, 0xa1, 0x4e, 0xe5, 0x2d, 0x10, 0x27, 0xc2, 0xd7, 0xde, 0x81, 0xe3, 0xd1,
	0xb5, 0x5d, 0xfb, 0x05, 0x0d, 0xf2, 0x1f, 0x05, 0x4e, 0x6e, 0x50, 0x26, 0x87, 0xed, 0xda, 0x21,
	0xe7, 0xda, 0xe4, 0x4e, 0x2c, 0xc5, 0x5b, 0x2a, 0x51, 0xc6, 0x4a, 0x8f, 0xc8, 0x58, 0x41, 0x07,
	0xc8, 0x24, 0x09, 0x88, 0xec, 0xe4, 0x80, 0xc8, 0xc5, 0x05, 0xc4, 0xb7, 0xa0, 0x36, 0x4a, 0x6d,
	0xd7, 0x26, 0x6b, 0x30, 0xe3, 0xd1, 0x5c, 0xc8, 0x17, 0x6a, 0x23, 0xa8, 0x0e, 0xdd, 0xa1, 0xec,
	0x0c, 0x7e, 0x72, 0xcb, 0xfe, 0x4e, 0x81, 0x57, 0x22, 0x5b, 0x24, 0xf6, 0x87, 0x97, 0x6e, 0x56,
	0x89, 0x13, 0xb2, 0x31, 0xe9, 0xe0, 0x1e, 0x9c, 0x88, 0x55, 0x86, 0x3b, 0x60, 0xd9, 0xd3, 0xc6,
	0xcb, 0x5b, 0xca, 0xf8, 0xbc, 0x55, 0x32, 0xa5, 0x27, 0xed, 0xdf, 0x0a, 0x4c, 0x8b, 0xe9, 0xed,
	0xdd, 0x3d, 0xaa, 0xf7, 0x4d, 0x7a, 0x04, 0x85, 0xd8, 0x67, 0x60, 0x1a, 0xeb, 0x0a, 0x97, 0xb5,
	0xbb, 0xb6, 0x58, 0x46, 0x2e, 0x6b, 0xcb, 0xfe, 0x1c, 0x5f, 0xeb, 0x3c, 0x94, 0x86, 0xc2, 0xcc,
	0xe2, 0x14, 0x36, 0x10, 0x2d, 0xfa, 0x33, 0x3b, 0x56, 0xa4, 0x38, 0x4b, 0x25, 0x2a, 0xce, 0x62,
	0x53, 0x8b, 0xf6, 0x81, 0x02, 0xb3, 0x41, 0xb5, 0x5f, 0xb0, 0x52, 0x69, 0x42, 0xd9, 0xf5, 0x56,
	0x92, 0x49, 0xe0, 0x64, 0xe4, 0x14, 0x06, 0xfb, 0x6d, 0x95, 0x06, 0xf2, 0x1e, 0x75, 0xa1, 0xb7,
	0x0e, 0x26, 0xd7, 0x0e, 0x77, 0xf8, 0xb9, 0x1f, 0x4d, 0x2d, 0xf1, 0x16, 0x77, 0x9e, 0xe8, 0xda,
	0xae, 0x1d, 0x85, 0xad, 0x3c, 0x1f, 0xec, 0xfb, 0x9c, 0x15, 0x87, 0x4b, 0x5f, 0x4b, 0xce, 0x5c,
	0x9a, 0x28, 0x5a, 0xf9, 0xe9, 0x06, 0xfa, 0xa2, 0xe1, 0xb0, 0xd6, 0x86, 0x4f, 0xc5, 0x2c, 0xef,
	0xda, 0xe4, 0xeb, 0x30, 0xeb, 0xe3, 0x0e, 0x71, 0xc4, 0xe9, 0x91, 0xd8, 0x91, 0x25, 0x66, 0xdc,
	0xe1, 0x03, 0xd7, 0xe0, 0x5f, 0x0a, 0x9c, 0xf2, 0x43, 0x6b, 0xb8, 0xd3, 0x73, 0x72, 0xf0, 0x4b,
	0x2a, 0x24, 0x78, 0x8b, 0x63, 0x39, 0xbb, 0xb4, 0xd5, 0xa1, 0x8c, 0x93, 0x6f, 0xde, 0x6f, 0x71,
	0x70, 0x78, 0x43, 0xa4, 0x71, 0x5e, 0xe9, 0x9b, 0x46, 0xd7, 0x60, 0x9c, 0x7d, 0xf3, 0xb2, 0x61,
	0xaf, 0xe3, 0xb0, 0xb6, 0x07, 0xa7, 0x47, 0x2b, 0x7d, 0xa4, 0xf6, 0xfd, 0x91, 0x82, 0x2e, 0xd2,
	0x0b, 0x79, 0x51, 0x32, 0xc3, 0x46, 0x79, 0x24, 0x95, 0x9c, 0x47, 0xd2, 0x23, 0x78, 0x04, 0xdb,
	0x69, 0x5f, 0xf7, 0xcd, 0xde, 0x7b, 0x56, 0x32, 0x30, 0xda, 0x9f, 0x45, 0x45, 0x2e, 0xbf, 0xe7,
	0xda, 0x93, 0x9a, 0xff, 0x70, 0x1e, 0x49, 0x25, 0xca, 0x23, 0xf1, 0xe5, 0xf0, 0x79, 0xf0, 0xa8,
	0xdb, 0x6b, 0xd2, 0xe4, 0x5a, 0xaf, 0x28, 0x66, 0x62, 0xda, 0xc1, 0xec, 0xa4, 0x1b, 0x87, 0x5c,
	0xf4, 0xc6, 0x81, 0x7c, 0x01, 0xe6, 0x1c, 0xda, 0xe9, 0x9b, 0x6d, 0xa7, 0x35, 0xaa, 0x50, 0x9f,
	0xf5, 0x04, 0x6e, 0x0c, 0x01, 0x5e, 0x86, 0xe3, 0xb6, 0x63, 0x75, 0x2d, 0x66, 0x58, 0xbd, 0x16,
	0x27, 0xa4, 0x5d, 0xab, 0xdf, 0x63, 0x81, 0xca, 0x9d, 0xf8, 0x12, 0xc8, 0x42, 0x57, 0x71, 0x5e,
	0xfb, 0xbb, 0x02, 0xb3, 0x77, 0x6d, 0xd3, 0x6a, 0xeb, 0xdb, 0xbd, 0xb6, 0xed, 0xee, 0x59, 0x47,
	0xd3, 0x43, 0x91, 0x93, 0x90, 0xf7, 0x5b, 0xeb, 0x34, 0x6f, 0xad, 0xa7, 0x0c, 0xaf, 0xb1, 0x0e,
	0x9e, 0x4b, 0x26, 0x51, 0x7e, 0xcf, 0x8e, 0xc8, 0xef, 0xe7, 0xa1, 0x24, 0x62, 0xb2, 0x6f, 0xeb,
	0x78, 0x2e, 0x39, 0xe9, 0xb6, 0xa3, 0xc8, 0x67, 0xee, 0xf2, 0x09, 0xed, 0xe3, 0x14, 0x4c, 0x23,
	0x9d, 0x3d, 0x9f, 0x92, 0x71, 0x41, 0xa0, 0x24, 0x0f, 0x02, 0x25, 0x3e, 0x99, 0x4a, 0xa6, 0x0b,
	0x5e, 0x84, 0xf8, 0x37, 0x3c, 0x47, 0x76, 0x11, 0x12, 0xcc, 0xaa, 0x53, 0xaa, 0x92, 0x38, 0xab,
	0x6a, 0x77, 0x61, 0x26, 0x60, 0xb0, 0x23, 0xaa, 0x0d, 0xf7, 0x06, 0x97, 0x14, 0xbe, 0x0f, 0xbe,
	0x48, 0xea, 0xaf, 0x41, 0x56, 0xf8, 0x79, 0xa0, 0x5f, 0xe3, 0x43, 0xda, 0x63, 0x85, 0x67, 0xb0,
	0xd0, 0x6e, 0x2f, 0xbf, 0x08, 0xd5, 0xde, 0x91, 0xaa, 0x61, 0x09, 0x87, 0x30, 0xe8, 0x30, 0x56,
	0xc7, 0x1b, 0x74, 0xf8, 0x22, 0x6f, 0x63, 0xf8, 0x4f, 0x6e, 0xd0, 0x2b, 0xbc, 0x0c, 0x78, 0xf3,
	0x11, 0x73, 0xda, 0x9c, 0x0f, 0x92, 0x5f, 0x35, 0x6a, 0xfb, 0x30, 0x1b, 0x7c, 0x2f, 0x71, 0x2b,
	0x5a, 0x83, 0xec, 0x7e, 0xdf, 0x62, 0xed, 0x80, 0x65, 0xc4, 0x10, 0x39, 0xe5, 0xd5, 0xb0, 0x7e,
	0xec, 0x97, 0x45, 0xf5, 0xca, 0xd1, 0xbe, 0xc5, 0xcf, 0x24, 0x8c, 0xd6, 0xb5, 0xc9, 0xd7, 0xa0,
	0x2c, 0x7a, 0xb9, 0xd1, 0x19, 0x2f, 0x82, 0x75, 0xab, 0xc8, 0xc4, 0x0f, 0xbe, 0xf4, 0xaf, 0x15,
	0x98, 0xdb, 0xa6, 0xec, 0x26, 0x7d, 0xc4, 0x6e, 0x0c, 0x8e, 0x28, 0xe1, 0x69, 0x47, 0x36, 0x4f,
	0x3d, 0xe7, 0xe6, 0x41, 0xde, 0x4f, 0x4f, 0xe2, 0xfd, 0x4c, 0xcc, 0x4d, 0xf3, 0x3f, 0x85, 0xbb,
	0xae, 0x9a, 0x26, 0xdf, 0xe5, 0xc5, 0x5a, 0xd1, 0x17, 0xc9, 0x75, 0x2f, 0xb1, 0x15, 0x15, 0x91,
	0x11, 0x51, 0xf9, 0x88, 0xa8, 0xe6, 0xcb, 0xf0, 0xca, 0x6a, 0x5f, 0x37, 0xd8, 0x4d, 0x4e, 0x5f,
	0x7d, 0x53, 0xb8, 0x45, 0x32, 0x8b, 0x5e, 0xf8, 0x26, 0x14, 0x7c, 0xde, 0x21, 0x33, 0x50, 0xdc,
	0xd8, 0xba, 0x75, 0xf7, 0x76, 0x6b, 0x7d, 0xf3, 0xe6, 0xea, 0xf5, 0xca, 0x31, 0x52, 0x86, 0xc2,
	0x9d, 0x3b, 0xab, 0x3d, 0xdd, 0xb1, 0x0c, 0xbd, 0xa2, 0x90, 0x02, 0x64, 0xef, 0xdc, 0x31, 0x6e,
	0x6d, 0x57, 0x52, 0x64, 0x16, 0xca, 0xf7, 0xe8, 0xee, 0x5e, 0x9b, 0x0d, 0x66, 0xd3, 0x28, 0x2c,
	0x86, 0x50, 0x22, 0x73, 0x81, 0x41, 0xf1, 0xba, 0x54, 0x31, 0x14, 0x61, 0x6a, 0x4b, 0xa4, 0xf3,
	0x8a, 0x42, 0x2a, 0x50, 0xf2, 0x1e, 0xb8, 0xbf, 0x55, 0x52, 0xf8, 0xf2, 0xed, 0x41, 0xda, 0x16,
	0x6b, 0x6d, 0xd3, 0xae, 0xb1, 0x6e, 0xf4, 0xda, 0x66, 0x25, 0x83, 0x1b, 0x8b, 0x9f, 0x59, 0xc4,
	0xb8, 0xb3, 0x67, 0x38, 0xfa, 0x3d, 0xa3, 0xd7, 0xa3, 0x4e, 0x25, 0x47, 0x4a, 0x90, 0xbf, 0x6a,
	0x75, 0x6d, 0x93, 0x32, 0x5a, 0x99, 0xba, 0xf0, 0x25, 0x80, 0x1b, 0xc3, 0xcb, 0xe4, 0x12, 0xe4,
	0x6f, 0x5a, 0x6c, 0x9b, 0xb5, 0x1d, 0x56, 0x39, 0x86, 0x4f, 0xeb, 0x46, 0xcf, 0x70, 0xf7, 0x28,
	0x2a, 0x53, 0x86, 0xc2, 0x35, 0xc3, 0xa1, 0xbb, 0xec, 0x9e, 0xd1, 0xab, 0xa4, 0x2e, 0xfd, 0xe3,
	0x24, 0x14, 0x3c, 0xbc, 0x0f, 0x1d, 0xf2, 0x58, 0x81, 0x92, 0xfc, 0x61, 0x83, 0xc8, 0xe7, 0x11,
	0xfa, 0x3a, 0x53, 0x3b, 0x35, 0x72, 0xce, 0xb5, 0xb5, 0xaf, 0x7c, 0xf7, 0xc9, 0xb3, 0xb4, 0xf2,
	0x83, 0x27, 0xcf, 0xd2, 0xb9, 0xce, 0x8a, 0xbb, 0x42, 0x57, 0x3e, 0x7c, 0xf2, 0x2c, 0xbd, 0x58,
	0xef, 0xa8, 0x0d, 0xaf, 0xa8, 0x6b, 0xaa, 0x75, 0x57, 0x6d, 0xf8, 0x51, 0xd5, 0x54, 0xeb, 0xd4,
	0x7b, 0x64, 0x56, 0x93, 0xfc, 0x5e, 0x81, 0x99, 0xd0, 0x2d, 0x33, 0x79, 0x55, 0xda, 0x2f, 0x7a,
	0x03, 0x5d, 0x3b, 0xbd, 0xe4, 0x7f, 0x5f, 0x5a, 0xda, 0xfe, 0xc6, 0x9a, 0xf8, 0xbe, 0xf4, 0x66,
	0xd7, 0x66, 0x87, 0xad, 0xdb, 0x6b, 0xda, 0x77, 0x10, 0x4f, 0x0a, 0xf1, 0x4c, 0x9b, 0x1c, 0xcf,
	0xfe, 0x0a, 0x5b, 0xe9, 0xae, 0x38, 0x1c, 0x57, 0xab, 0x6e, 0xaa, 0x0d, 0xdf, 0x65, 0x04, 0x32,
	0xe1, 0xff, 0xba, 0x87, 0xcb, 0x12, 0x3f, 0xf7, 0xbd, 0xf1, 0x03, 0xa3, 0xd7, 0x54, 0xeb, 0x8c,
	0x4f, 0x88, 0xdf, 0xdd, 0xc6, 0x30, 0x42, 0x9b, 0x6a, 0xdd, 0x69, 0x48, 0xb1, 0xd8, 0x24, 0xef,
	0x43, 0x39, 0x70, 0xe5, 0x4c, 0x64, 0xdb, 0x85, 0xef, 0xb8, 0x6b, 0xa7, 0x47, 0x4f, 0xba, 0xb6,
	0xb6, 0x84, 0x9a, 0xa4, 0x51, 0x93, 0x8c, 0xb9, 0xc2, 0x38, 0xfe, 0x53, 0x61, 0xfc, 0x88, 0x4c,
	0xd4, 0x20, 0x4d, 0xf2, 0x43, 0x85, 0xdf, 0xdd, 0x85, 0x9b, 0x54, 0x72, 0x26, 0xb8, 0x4b, 0x4c,
	0x83, 0x5c, 0xd3, 0x26, 0x89, 0x0c, 0xe0, 0x64, 0x92, 0xc3, 0xf9, 0x48, 0xe1, 0xc5, 0x7e, 0xb0,
	0xf3, 0x24, 0x0b, 0xa3, 0x76, 0xf2, 0xda, 0xde, 0x9a, 0x3a, 0x5e, 0xc0, 0xb5, 0xb5, 0xab, 0x08,
	0x24, 0x1b, 0x00, 0xb2, 0x1c, 0x03, 0x04, 0xeb, 0x45, 0x95, 0x2e, 0x75, 0x96, 0x2e, 0x2d, 0x7f,
	0xee, 0x72, 0x7d, 0xf9, 0x72, 0xfd, 0xf3, 0xcb, 0xea, 0xf2, 0xf2, 0x0a, 0xff, 0x6b, 0x92, 0x9f,
	0x70, 0x74, 0xa1, 0x9e, 0x2a, 0x84, 0x2e, 0xda, 0x71, 0x4d, 0xf0, 0xbd, 0xaf, 0x22, 0xb2, 0x1c,
	0x8f, 0x05, 0xd3, 0x8f, 0x85, 0x0b, 0x31, 0x3e, 0x37, 0x2a, 0x1a, 0x7e, 0xa5, 0xc0, 0x74, 0xb0,
	0x8c, 0x27, 0xb2, 0x8f, 0x44, 0x2a, 0xfc, 0x09, 0x78, 0xbe, 0x8d, 0x78, 0xa6, 0x10, 0x4f, 0x09,
	0x2d, 0xf5, 0x80, 0xc7, 0x81, 0xcd, 0x51, 0xdd, 0x1a, 0x73, 0x74, 0x6a, 0xfd, 0x81, 0xda, 0x78,
	0x40, 0x0f, 0xe3, 0xbc, 0x5d, 0xf8, 0xb9, 0x5a, 0xb7, 0xd5, 0x86, 0x5c, 0xb1, 0x37, 0xc9, 0xcf,
	0x94, 0xe0, 0x27, 0x56, 0xf4, 0x1c, 0x32, 0x3f, 0x82, 0x39, 0xbc, 0xe2, 0x66, 0x02, 0xfc, 0x5b,
	0x08, 0x3f, 0x8f, 0xf0, 0xf3, 0x08, 0x1f, 0xe9, 0x05, 0xa1, 0xbf, 0x31, 0x16, 0x3a, 0x12, 0x8f,
	0x9f, 0xf6, 0x84, 0xb5, 0xbd, 0x6f, 0xa8, 0x4d, 0x62, 0xf2, 0xe8, 0x1c, 0xb6, 0x9f, 0xe1, 0xe8,
	0x0c, 0x34, 0xb4, 0xe1, 0xe8, 0x0c, 0x76, 0xad, 0x9a, 0x8a, 0xe0, 0x0a, 0x08, 0x2e, 0x65, 0x72,
	0x58, 0x33, 0x21, 0x58, 0xe4, 0x97, 0x71, 0xd7, 0xa7, 0x82, 0xb7, 0x5f, 0x8b, 0x5b, 0x3a, 0x7c,
	0x77, 0x5d, 0x3b, 0x97, 0x40, 0xca, 0xb5, 0xb5, 0x6b, 0x88, 0x04, 0x3c, 0xaf, 0x1b, 0x30, 0xdd,
	0xc5, 0xb0, 0x91, 0xba, 0x6a, 0xe8, 0x28, 0xd5, 0x20, 0x73, 0xfd, 0x46, 0x81, 0xea, 0xa8, 0xfb,
	0x0c, 0xf2, 0x7a, 0x1c, 0x92, 0xe8, 0x4d, 0x4f, 0xed, 0x7c, 0x22, 0x39, 0xd7, 0xd6, 0x6e, 0x23,
	0xe6, 0xa2, 0x77, 0xb4, 0xc3, 0xbc, 0x71, 0x25, 0x8c, 0xba, 0x13, 0x3a, 0xcc, 0xc6, 0xb0, 0x5a,
	0xc1, 0xd0, 0x69, 0x0c, 0xea, 0x12, 0x8e, 0xbf, 0x28, 0x35, 0x3a, 0xe4, 0x64, 0x88, 0x47, 0xa4,
	0xa0, 0xa9, 0x8d, 0x9a, 0x72, 0x6d, 0xed, 0x00, 0x81, 0x95, 0xbc, 0x90, 0x41, 0x58, 0xc3, 0xe4,
	0xf1, 0xce, 0xb8, 0x40, 0x56, 0x97, 0x55, 0xa3, 0xf7, 0xb0, 0x6d, 0xfa, 0x89, 0xc4, 0x0b, 0xe9,
	0x90, 0x77, 0xc6, 0x9c, 0x82, 0x67, 0xff, 0x8f, 0x15, 0xde, 0xa8, 0xc9, 0x1f, 0x76, 0x02, 0x89,
	0x30, 0xfa, 0x81, 0xae, 0x36, 0x3f, 0x6e, 0xda, 0xb5, 0xb5, 0xfb, 0xa8, 0x4b, 0x39, 0x62, 0xe4,
	0xf5, 0x88, 0x91, 0xe5, 0x90, 0x09, 0x28, 0xe2, 0xa7, 0xc7, 0x81, 0xc1, 0x55, 0xc9, 0xe2, 0xdf,
	0x13, 0x04, 0x1f, 0xac, 0xa8, 0xc3, 0x04, 0x1f, 0x69, 0x68, 0xc2, 0x04, 0x1f, 0xed, 0x21, 0xb4,
	0x0b, 0x88, 0x7b, 0x9a, 0x13, 0x7c, 0x7f, 0x45, 0x04, 0xd7, 0x89, 0x7a, 0x5f, 0x6d, 0xf4, 0x39,
	0xa6, 0x00, 0x78, 0xf2, 0x5b, 0x05, 0x2a, 0xe1, 0x6e, 0x21, 0x40, 0x3b, 0x31, 0xad, 0xc4, 0x04,
	0xda, 0xe9, 0xe3, 0xf6, 0x33, 0xdc, 0x05, 0x70, 0xfb, 0x8e, 0xe7, 0x06, 0x08, 0xe3, 0xed, 0x11,
	0x30, 0x62, 0x69, 0x87, 0x5b, 0x0e, 0xfd, 0xc0, 0x37, 0x9c, 0xf7, 0xc0, 0x1a, 0x86, 0xae, 0x62,
	0x2d, 0xfc, 0x59, 0xd5, 0xb5, 0x4d, 0x83, 0xa9, 0xef, 0x1e, 0xd6, 0x9b, 0xe4, 0xa7, 0xe2, 0x2b,
	0x6c, 0xb8, 0xab, 0x56, 0xe3, 0x42, 0x4a, 0x6e, 0x83, 0x6b, 0x67, 0x26, 0x48, 0xb8, 0xb6, 0xd6,
	0x44, 0x95, 0x2a, 0x21, 0x8a, 0xf8, 0x74, 0x02, 0x8a, 0xf0, 0x9c, 0xf3, 0x03, 0xc5, 0xff, 0x42,
	0xef, 0x7f, 0xba, 0x23, 0xf3, 0xd1, 0xea, 0x45, 0xfe, 0x66, 0x58, 0x5b, 0x18, 0x3b, 0xef, 0xda,
	0xda, 0x15, 0x44, 0x35, 0x1b, 0x48, 0xe4, 0xaf, 0xc7, 0x70, 0x7b, 0x9c, 0xb1, 0xfe, 0xe4, 0x7f,
	0xb2, 0x96, 0x5b, 0x8e, 0xb0, 0xb1, 0xa2, 0x4d, 0x58, 0xd8, 0x58, 0x31, 0x3d, 0x8b, 0xf6, 0x3e,
	0xc2, 0x22, 0x1e, 0x05, 0x88, 0xd3, 0x1f, 0x59, 0x3f, 0xfe, 0x7f, 0xa1, 0x33, 0x32, 0xaf, 0xa2,
	0x3b, 0xcf, 0xc5, 0x7c, 0xa5, 0x22, 0x67, 0xc6, 0xa5, 0x82, 0xd8, 0x1a, 0x2e, 0xee, 0x43, 0x97,
	0x66, 0xa0, 0x6a, 0x73, 0xa8, 0x1a, 0xa0, 0x6a, 0x43, 0x6e, 0xbb, 0x9d, 0x5c, 0xb1, 0x84, 0x7c,
	0x76, 0x00, 0x73, 0x31, 0x5d, 0x5b, 0x40, 0x91, 0xf8, 0xae, 0x6e, 0x42, 0x74, 0x2e, 0xa0, 0x0a,
	0xc7, 0xa5, 0xbc, 0x3b, 0x1d, 0x84, 0x5e, 0xcb, 0x7d, 0xff, 0xc9, 0xb3, 0xf4, 0x7f, 0x0f, 0xd6,
	0x2a, 0x9f, 0x3c, 0x9d, 0x57, 0xfe, 0xf0, 0x74, 0x5e, 0xf9, 0xeb, 0xd3, 0x79, 0xe5, 0xc7, 0x7f,
	0x9b, 0x3f, 0xf6, 0xbf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xbe, 0xae, 0xc5, 0xcd, 0xcb, 0x26, 0x00,
	0x00,
}
