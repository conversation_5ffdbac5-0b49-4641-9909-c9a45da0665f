// Code generated by protoc-gen-gogo.
// source: src/greenBaba/greenbaba.proto
// DO NOT EDIT!

/*
	Package greenBaba is a generated protocol buffer package.

	It is generated from these files:
		src/greenBaba/greenbaba.proto

	It has these top-level messages:
		GreenBabaSanctionInfo
		SetSanctionReq
		SetSanctionResp
		GetCurrBannedStatByIdReq
		GetCurrBannedStatByIdResp
		BatchGetCurrBannedStatByIdReq
		CurrBannedStatInfo
		BatchGetCurrBannedStatByIdResp
		GetBannedListByTargetTypeReq
		GetBannedListByTargetTypeResp
		SendReportReq
		SendReportResp
		BaseReportInfo
		ComplexSanctionInfo
		ScanAllReportPorcHistoryListReq
		ScanAllReportPorcHistoryListResp
		GetSanctionStatByIdReq
		GetSanctionStatByIdResp
		ScanSanctionHistoryListByIdReq
		ScanSanctionHistoryListByIdResp
		CleanHistoryRecordByIdReq
		CleanHistoryRecordByIdResp
		AddChannelWhiteListReq
		AddChannelWhiteListResp
		DelChannelFromWhiteListReq
		DelChannelFromWhiteListResp
		GetChannelWhiteListReq
		GetChannelWhiteListResp
		GetUserReportHistoryReq
		GetUserReportHistoryResp
		UserReportInfo
		UserReportList
		MaliciousReportInfo
		MaliciousReportInfoList
		SendCommonReportReq
		SendCommonReportResp
		CheckUserIsImLimitReq
		CheckUserIsImLimitResp
		MuteChannelMemberReq
		MuteChannelMemberResp
		UnmuteChannelMemberReq
		UnmuteChannelMemberResp
		GetChannelMuteListReq
		ChannelMemberBaseInfo
		GetChannelMuteListResp
		CheckUserIsMuteReq
		CheckUserIsMuteResp
		CleanChannelMuteListReq
		CleanChannelMuteListResp
		CheckUserKickoutFromChannelReq
		CheckUserKickoutFromChannelResp
		KickoutChannelMemberReq
		KickoutChannelMemberResp
*/
package greenBaba

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ENUM_TARGET_TYPE int32

const (
	ENUM_TARGET_TYPE_E_TARGET_TYPE_CHANNEL   ENUM_TARGET_TYPE = 1
	ENUM_TARGET_TYPE_E_TARGET_TYPE_USER      ENUM_TARGET_TYPE = 2
	ENUM_TARGET_TYPE_E_TARGET_TYPE_MUSIC     ENUM_TARGET_TYPE = 3
	ENUM_TARGET_TYPE_E_TARGET_TYPE_GROUP     ENUM_TARGET_TYPE = 4
	ENUM_TARGET_TYPE_E_TARGET_TYPE_CHAT      ENUM_TARGET_TYPE = 5
	ENUM_TARGET_TYPE_E_TARGET_TYPE_DRAW_GAME ENUM_TARGET_TYPE = 6
)

var ENUM_TARGET_TYPE_name = map[int32]string{
	1: "E_TARGET_TYPE_CHANNEL",
	2: "E_TARGET_TYPE_USER",
	3: "E_TARGET_TYPE_MUSIC",
	4: "E_TARGET_TYPE_GROUP",
	5: "E_TARGET_TYPE_CHAT",
	6: "E_TARGET_TYPE_DRAW_GAME",
}
var ENUM_TARGET_TYPE_value = map[string]int32{
	"E_TARGET_TYPE_CHANNEL":   1,
	"E_TARGET_TYPE_USER":      2,
	"E_TARGET_TYPE_MUSIC":     3,
	"E_TARGET_TYPE_GROUP":     4,
	"E_TARGET_TYPE_CHAT":      5,
	"E_TARGET_TYPE_DRAW_GAME": 6,
}

func (x ENUM_TARGET_TYPE) Enum() *ENUM_TARGET_TYPE {
	p := new(ENUM_TARGET_TYPE)
	*p = x
	return p
}
func (x ENUM_TARGET_TYPE) String() string {
	return proto.EnumName(ENUM_TARGET_TYPE_name, int32(x))
}
func (x *ENUM_TARGET_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_TARGET_TYPE_value, data, "ENUM_TARGET_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_TARGET_TYPE(value)
	return nil
}
func (ENUM_TARGET_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{0} }

// 制裁类型
type ENUM_SANCTION_OP_TYPE int32

const (
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_NONE    ENUM_SANCTION_OP_TYPE = 0
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_WARNING ENUM_SANCTION_OP_TYPE = 3
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_BANNED  ENUM_SANCTION_OP_TYPE = 4
	ENUM_SANCTION_OP_TYPE_E_SANCTION_OP_TYPE_IGNORE  ENUM_SANCTION_OP_TYPE = 25
)

var ENUM_SANCTION_OP_TYPE_name = map[int32]string{
	0:  "E_SANCTION_OP_TYPE_NONE",
	3:  "E_SANCTION_OP_TYPE_WARNING",
	4:  "E_SANCTION_OP_TYPE_BANNED",
	25: "E_SANCTION_OP_TYPE_IGNORE",
}
var ENUM_SANCTION_OP_TYPE_value = map[string]int32{
	"E_SANCTION_OP_TYPE_NONE":    0,
	"E_SANCTION_OP_TYPE_WARNING": 3,
	"E_SANCTION_OP_TYPE_BANNED":  4,
	"E_SANCTION_OP_TYPE_IGNORE":  25,
}

func (x ENUM_SANCTION_OP_TYPE) Enum() *ENUM_SANCTION_OP_TYPE {
	p := new(ENUM_SANCTION_OP_TYPE)
	*p = x
	return p
}
func (x ENUM_SANCTION_OP_TYPE) String() string {
	return proto.EnumName(ENUM_SANCTION_OP_TYPE_name, int32(x))
}
func (x *ENUM_SANCTION_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_SANCTION_OP_TYPE_value, data, "ENUM_SANCTION_OP_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_SANCTION_OP_TYPE(value)
	return nil
}
func (ENUM_SANCTION_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{1}
}

// 制裁类型中 封禁情况下 的封禁类型
type ENUM_BANNED_TYPE int32

const (
	ENUM_BANNED_TYPE_E_BANNED_NONE    ENUM_BANNED_TYPE = 0
	ENUM_BANNED_TYPE_E_BANNED_LOGIN   ENUM_BANNED_TYPE = 1
	ENUM_BANNED_TYPE_E_BANNED_CHANNEL ENUM_BANNED_TYPE = 2
)

var ENUM_BANNED_TYPE_name = map[int32]string{
	0: "E_BANNED_NONE",
	1: "E_BANNED_LOGIN",
	2: "E_BANNED_CHANNEL",
}
var ENUM_BANNED_TYPE_value = map[string]int32{
	"E_BANNED_NONE":    0,
	"E_BANNED_LOGIN":   1,
	"E_BANNED_CHANNEL": 2,
}

func (x ENUM_BANNED_TYPE) Enum() *ENUM_BANNED_TYPE {
	p := new(ENUM_BANNED_TYPE)
	*p = x
	return p
}
func (x ENUM_BANNED_TYPE) String() string {
	return proto.EnumName(ENUM_BANNED_TYPE_name, int32(x))
}
func (x *ENUM_BANNED_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_BANNED_TYPE_value, data, "ENUM_BANNED_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_BANNED_TYPE(value)
	return nil
}
func (ENUM_BANNED_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{2} }

type ENUM_REPORT_TYPE int32

const (
	ENUM_REPORT_TYPE_E_REPORT_PRIVACY         ENUM_REPORT_TYPE = 0
	ENUM_REPORT_TYPE_E_REPORT_PERSONAL_ATTACK ENUM_REPORT_TYPE = 1
	ENUM_REPORT_TYPE_E_REPORT_PORNOGRAPHY     ENUM_REPORT_TYPE = 2
	ENUM_REPORT_TYPE_E_REPORT_AD              ENUM_REPORT_TYPE = 3
	ENUM_REPORT_TYPE_E_REPORT_SENSITIVE_INFO  ENUM_REPORT_TYPE = 4
	ENUM_REPORT_TYPE_E_REPORT_TYPE_ALL        ENUM_REPORT_TYPE = 255
)

var ENUM_REPORT_TYPE_name = map[int32]string{
	0:   "E_REPORT_PRIVACY",
	1:   "E_REPORT_PERSONAL_ATTACK",
	2:   "E_REPORT_PORNOGRAPHY",
	3:   "E_REPORT_AD",
	4:   "E_REPORT_SENSITIVE_INFO",
	255: "E_REPORT_TYPE_ALL",
}
var ENUM_REPORT_TYPE_value = map[string]int32{
	"E_REPORT_PRIVACY":         0,
	"E_REPORT_PERSONAL_ATTACK": 1,
	"E_REPORT_PORNOGRAPHY":     2,
	"E_REPORT_AD":              3,
	"E_REPORT_SENSITIVE_INFO":  4,
	"E_REPORT_TYPE_ALL":        255,
}

func (x ENUM_REPORT_TYPE) Enum() *ENUM_REPORT_TYPE {
	p := new(ENUM_REPORT_TYPE)
	*p = x
	return p
}
func (x ENUM_REPORT_TYPE) String() string {
	return proto.EnumName(ENUM_REPORT_TYPE_name, int32(x))
}
func (x *ENUM_REPORT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ENUM_REPORT_TYPE_value, data, "ENUM_REPORT_TYPE")
	if err != nil {
		return err
	}
	*x = ENUM_REPORT_TYPE(value)
	return nil
}
func (ENUM_REPORT_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{3} }

type GreenBabaSanctionInfo struct {
	TargetType     uint32 `protobuf:"varint,1,req,name=target_type,json=targetType" json:"target_type"`
	Id             uint32 `protobuf:"varint,2,req,name=id" json:"id"`
	BannedType     uint32 `protobuf:"varint,3,opt,name=banned_type,json=bannedType" json:"banned_type"`
	RemainSecond   uint32 `protobuf:"varint,4,opt,name=remain_second,json=remainSecond" json:"remain_second"`
	SanctionType   uint32 `protobuf:"varint,5,opt,name=sanction_type,json=sanctionType" json:"sanction_type"`
	SanctionReason string `protobuf:"bytes,6,opt,name=sanction_reason,json=sanctionReason" json:"sanction_reason"`
}

func (m *GreenBabaSanctionInfo) Reset()                    { *m = GreenBabaSanctionInfo{} }
func (m *GreenBabaSanctionInfo) String() string            { return proto.CompactTextString(m) }
func (*GreenBabaSanctionInfo) ProtoMessage()               {}
func (*GreenBabaSanctionInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{0} }

func (m *GreenBabaSanctionInfo) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *GreenBabaSanctionInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GreenBabaSanctionInfo) GetBannedType() uint32 {
	if m != nil {
		return m.BannedType
	}
	return 0
}

func (m *GreenBabaSanctionInfo) GetRemainSecond() uint32 {
	if m != nil {
		return m.RemainSecond
	}
	return 0
}

func (m *GreenBabaSanctionInfo) GetSanctionType() uint32 {
	if m != nil {
		return m.SanctionType
	}
	return 0
}

func (m *GreenBabaSanctionInfo) GetSanctionReason() string {
	if m != nil {
		return m.SanctionReason
	}
	return ""
}

// 设置制裁
type SetSanctionReq struct {
	SanctionList []*GreenBabaSanctionInfo `protobuf:"bytes,1,rep,name=sanction_list,json=sanctionList" json:"sanction_list,omitempty"`
	OpUid        uint32                   `protobuf:"varint,2,opt,name=op_uid,json=opUid" json:"op_uid"`
	OpUserinfo   string                   `protobuf:"bytes,3,opt,name=op_userinfo,json=opUserinfo" json:"op_userinfo"`
	SanctionObj  uint32                   `protobuf:"varint,4,opt,name=sanction_obj,json=sanctionObj" json:"sanction_obj"`
}

func (m *SetSanctionReq) Reset()                    { *m = SetSanctionReq{} }
func (m *SetSanctionReq) String() string            { return proto.CompactTextString(m) }
func (*SetSanctionReq) ProtoMessage()               {}
func (*SetSanctionReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{1} }

func (m *SetSanctionReq) GetSanctionList() []*GreenBabaSanctionInfo {
	if m != nil {
		return m.SanctionList
	}
	return nil
}

func (m *SetSanctionReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SetSanctionReq) GetOpUserinfo() string {
	if m != nil {
		return m.OpUserinfo
	}
	return ""
}

func (m *SetSanctionReq) GetSanctionObj() uint32 {
	if m != nil {
		return m.SanctionObj
	}
	return 0
}

type SetSanctionResp struct {
}

func (m *SetSanctionResp) Reset()                    { *m = SetSanctionResp{} }
func (m *SetSanctionResp) String() string            { return proto.CompactTextString(m) }
func (*SetSanctionResp) ProtoMessage()               {}
func (*SetSanctionResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{2} }

type GetCurrBannedStatByIdReq struct {
	SanctionTargetType uint32 `protobuf:"varint,1,req,name=sanction_target_type,json=sanctionTargetType" json:"sanction_target_type"`
	ReqId              uint32 `protobuf:"varint,2,req,name=req_id,json=reqId" json:"req_id"`
}

func (m *GetCurrBannedStatByIdReq) Reset()         { *m = GetCurrBannedStatByIdReq{} }
func (m *GetCurrBannedStatByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetCurrBannedStatByIdReq) ProtoMessage()    {}
func (*GetCurrBannedStatByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{3}
}

func (m *GetCurrBannedStatByIdReq) GetSanctionTargetType() uint32 {
	if m != nil {
		return m.SanctionTargetType
	}
	return 0
}

func (m *GetCurrBannedStatByIdReq) GetReqId() uint32 {
	if m != nil {
		return m.ReqId
	}
	return 0
}

type GetCurrBannedStatByIdResp struct {
	BannedList []*GreenBabaSanctionInfo `protobuf:"bytes,1,rep,name=banned_list,json=bannedList" json:"banned_list,omitempty"`
}

func (m *GetCurrBannedStatByIdResp) Reset()         { *m = GetCurrBannedStatByIdResp{} }
func (m *GetCurrBannedStatByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetCurrBannedStatByIdResp) ProtoMessage()    {}
func (*GetCurrBannedStatByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{4}
}

func (m *GetCurrBannedStatByIdResp) GetBannedList() []*GreenBabaSanctionInfo {
	if m != nil {
		return m.BannedList
	}
	return nil
}

type BatchGetCurrBannedStatByIdReq struct {
	SanctionTargetType uint32   `protobuf:"varint,1,req,name=sanction_target_type,json=sanctionTargetType" json:"sanction_target_type"`
	ReqIdList          []uint32 `protobuf:"varint,2,rep,name=req_id_list,json=reqIdList" json:"req_id_list,omitempty"`
}

func (m *BatchGetCurrBannedStatByIdReq) Reset()         { *m = BatchGetCurrBannedStatByIdReq{} }
func (m *BatchGetCurrBannedStatByIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetCurrBannedStatByIdReq) ProtoMessage()    {}
func (*BatchGetCurrBannedStatByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{5}
}

func (m *BatchGetCurrBannedStatByIdReq) GetSanctionTargetType() uint32 {
	if m != nil {
		return m.SanctionTargetType
	}
	return 0
}

func (m *BatchGetCurrBannedStatByIdReq) GetReqIdList() []uint32 {
	if m != nil {
		return m.ReqIdList
	}
	return nil
}

type CurrBannedStatInfo struct {
	Id                 uint32                   `protobuf:"varint,1,req,name=id" json:"id"`
	SanctionTargetType uint32                   `protobuf:"varint,2,req,name=sanction_target_type,json=sanctionTargetType" json:"sanction_target_type"`
	BannedList         []*GreenBabaSanctionInfo `protobuf:"bytes,3,rep,name=banned_list,json=bannedList" json:"banned_list,omitempty"`
}

func (m *CurrBannedStatInfo) Reset()                    { *m = CurrBannedStatInfo{} }
func (m *CurrBannedStatInfo) String() string            { return proto.CompactTextString(m) }
func (*CurrBannedStatInfo) ProtoMessage()               {}
func (*CurrBannedStatInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{6} }

func (m *CurrBannedStatInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CurrBannedStatInfo) GetSanctionTargetType() uint32 {
	if m != nil {
		return m.SanctionTargetType
	}
	return 0
}

func (m *CurrBannedStatInfo) GetBannedList() []*GreenBabaSanctionInfo {
	if m != nil {
		return m.BannedList
	}
	return nil
}

type BatchGetCurrBannedStatByIdResp struct {
	CurrbannedList []*CurrBannedStatInfo `protobuf:"bytes,1,rep,name=currbanned_list,json=currbannedList" json:"currbanned_list,omitempty"`
}

func (m *BatchGetCurrBannedStatByIdResp) Reset()         { *m = BatchGetCurrBannedStatByIdResp{} }
func (m *BatchGetCurrBannedStatByIdResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetCurrBannedStatByIdResp) ProtoMessage()    {}
func (*BatchGetCurrBannedStatByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{7}
}

func (m *BatchGetCurrBannedStatByIdResp) GetCurrbannedList() []*CurrBannedStatInfo {
	if m != nil {
		return m.CurrbannedList
	}
	return nil
}

// 根据目标类型 获取当前的封禁列表
type GetBannedListByTargetTypeReq struct {
	SanctionTargetType uint32 `protobuf:"varint,1,req,name=sanction_target_type,json=sanctionTargetType" json:"sanction_target_type"`
	BeginIdx           uint32 `protobuf:"varint,2,req,name=begin_idx,json=beginIdx" json:"begin_idx"`
	Limit              uint32 `protobuf:"varint,3,req,name=limit" json:"limit"`
}

func (m *GetBannedListByTargetTypeReq) Reset()         { *m = GetBannedListByTargetTypeReq{} }
func (m *GetBannedListByTargetTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedListByTargetTypeReq) ProtoMessage()    {}
func (*GetBannedListByTargetTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{8}
}

func (m *GetBannedListByTargetTypeReq) GetSanctionTargetType() uint32 {
	if m != nil {
		return m.SanctionTargetType
	}
	return 0
}

func (m *GetBannedListByTargetTypeReq) GetBeginIdx() uint32 {
	if m != nil {
		return m.BeginIdx
	}
	return 0
}

func (m *GetBannedListByTargetTypeReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBannedListByTargetTypeResp struct {
	BannedList []*GreenBabaSanctionInfo `protobuf:"bytes,1,rep,name=banned_list,json=bannedList" json:"banned_list,omitempty"`
	TotalSize  uint32                   `protobuf:"varint,2,req,name=total_size,json=totalSize" json:"total_size"`
}

func (m *GetBannedListByTargetTypeResp) Reset()         { *m = GetBannedListByTargetTypeResp{} }
func (m *GetBannedListByTargetTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedListByTargetTypeResp) ProtoMessage()    {}
func (*GetBannedListByTargetTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{9}
}

func (m *GetBannedListByTargetTypeResp) GetBannedList() []*GreenBabaSanctionInfo {
	if m != nil {
		return m.BannedList
	}
	return nil
}

func (m *GetBannedListByTargetTypeResp) GetTotalSize() uint32 {
	if m != nil {
		return m.TotalSize
	}
	return 0
}

// 发举报
type SendReportReq struct {
	OpTtAcc       string `protobuf:"bytes,1,req,name=op_tt_acc,json=opTtAcc" json:"op_tt_acc"`
	TargetChannel uint32 `protobuf:"varint,2,opt,name=target_channel,json=targetChannel" json:"target_channel"`
	TargetUid     uint32 `protobuf:"varint,3,opt,name=target_uid,json=targetUid" json:"target_uid"`
	ReportType    uint32 `protobuf:"varint,4,opt,name=report_type,json=reportType" json:"report_type"`
	ReportReason  string `protobuf:"bytes,5,opt,name=report_reason,json=reportReason" json:"report_reason"`
	PicUrl        string `protobuf:"bytes,6,opt,name=pic_url,json=picUrl" json:"pic_url"`
	ReportUid     uint32 `protobuf:"varint,7,opt,name=report_uid,json=reportUid" json:"report_uid"`
}

func (m *SendReportReq) Reset()                    { *m = SendReportReq{} }
func (m *SendReportReq) String() string            { return proto.CompactTextString(m) }
func (*SendReportReq) ProtoMessage()               {}
func (*SendReportReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{10} }

func (m *SendReportReq) GetOpTtAcc() string {
	if m != nil {
		return m.OpTtAcc
	}
	return ""
}

func (m *SendReportReq) GetTargetChannel() uint32 {
	if m != nil {
		return m.TargetChannel
	}
	return 0
}

func (m *SendReportReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SendReportReq) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *SendReportReq) GetReportReason() string {
	if m != nil {
		return m.ReportReason
	}
	return ""
}

func (m *SendReportReq) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *SendReportReq) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

type SendReportResp struct {
}

func (m *SendReportResp) Reset()                    { *m = SendReportResp{} }
func (m *SendReportResp) String() string            { return proto.CompactTextString(m) }
func (*SendReportResp) ProtoMessage()               {}
func (*SendReportResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{11} }

// 举报
type BaseReportInfo struct {
	OpTtAcc      string `protobuf:"bytes,1,req,name=op_tt_acc,json=opTtAcc" json:"op_tt_acc"`
	ReportType   uint32 `protobuf:"varint,2,opt,name=report_type,json=reportType" json:"report_type"`
	ReportReason string `protobuf:"bytes,3,opt,name=report_reason,json=reportReason" json:"report_reason"`
	ReportUid    uint32 `protobuf:"varint,4,opt,name=report_uid,json=reportUid" json:"report_uid"`
	PicUrl       string `protobuf:"bytes,5,opt,name=pic_url,json=picUrl" json:"pic_url"`
	ReportTs     uint32 `protobuf:"varint,6,opt,name=report_ts,json=reportTs" json:"report_ts"`
	TargetUid    uint32 `protobuf:"varint,7,opt,name=target_uid,json=targetUid" json:"target_uid"`
}

func (m *BaseReportInfo) Reset()                    { *m = BaseReportInfo{} }
func (m *BaseReportInfo) String() string            { return proto.CompactTextString(m) }
func (*BaseReportInfo) ProtoMessage()               {}
func (*BaseReportInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{12} }

func (m *BaseReportInfo) GetOpTtAcc() string {
	if m != nil {
		return m.OpTtAcc
	}
	return ""
}

func (m *BaseReportInfo) GetReportType() uint32 {
	if m != nil {
		return m.ReportType
	}
	return 0
}

func (m *BaseReportInfo) GetReportReason() string {
	if m != nil {
		return m.ReportReason
	}
	return ""
}

func (m *BaseReportInfo) GetReportUid() uint32 {
	if m != nil {
		return m.ReportUid
	}
	return 0
}

func (m *BaseReportInfo) GetPicUrl() string {
	if m != nil {
		return m.PicUrl
	}
	return ""
}

func (m *BaseReportInfo) GetReportTs() uint32 {
	if m != nil {
		return m.ReportTs
	}
	return 0
}

func (m *BaseReportInfo) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

// 组合的制裁信息
type ComplexSanctionInfo struct {
	TargetId       uint32            `protobuf:"varint,1,req,name=target_id,json=targetId" json:"target_id"`
	ReportBaseList []*BaseReportInfo `protobuf:"bytes,2,rep,name=report_base_list,json=reportBaseList" json:"report_base_list,omitempty"`
	ReprotRealCnt  uint32            `protobuf:"varint,3,opt,name=reprot_real_cnt,json=reprotRealCnt" json:"reprot_real_cnt"`
	SanctionOpType uint32            `protobuf:"varint,4,opt,name=sanction_op_type,json=sanctionOpType" json:"sanction_op_type"`
	BannedOpType   uint32            `protobuf:"varint,5,opt,name=banned_op_type,json=bannedOpType" json:"banned_op_type"`
	BannedSecond   uint32            `protobuf:"varint,6,opt,name=banned_second,json=bannedSecond" json:"banned_second"`
	LastUpdateTs   uint32            `protobuf:"varint,7,opt,name=last_update_ts,json=lastUpdateTs" json:"last_update_ts"`
	SanctionOpUser string            `protobuf:"bytes,8,opt,name=sanction_op_user,json=sanctionOpUser" json:"sanction_op_user"`
	SanctionReason string            `protobuf:"bytes,9,opt,name=sanction_reason,json=sanctionReason" json:"sanction_reason"`
}

func (m *ComplexSanctionInfo) Reset()                    { *m = ComplexSanctionInfo{} }
func (m *ComplexSanctionInfo) String() string            { return proto.CompactTextString(m) }
func (*ComplexSanctionInfo) ProtoMessage()               {}
func (*ComplexSanctionInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{13} }

func (m *ComplexSanctionInfo) GetTargetId() uint32 {
	if m != nil {
		return m.TargetId
	}
	return 0
}

func (m *ComplexSanctionInfo) GetReportBaseList() []*BaseReportInfo {
	if m != nil {
		return m.ReportBaseList
	}
	return nil
}

func (m *ComplexSanctionInfo) GetReprotRealCnt() uint32 {
	if m != nil {
		return m.ReprotRealCnt
	}
	return 0
}

func (m *ComplexSanctionInfo) GetSanctionOpType() uint32 {
	if m != nil {
		return m.SanctionOpType
	}
	return 0
}

func (m *ComplexSanctionInfo) GetBannedOpType() uint32 {
	if m != nil {
		return m.BannedOpType
	}
	return 0
}

func (m *ComplexSanctionInfo) GetBannedSecond() uint32 {
	if m != nil {
		return m.BannedSecond
	}
	return 0
}

func (m *ComplexSanctionInfo) GetLastUpdateTs() uint32 {
	if m != nil {
		return m.LastUpdateTs
	}
	return 0
}

func (m *ComplexSanctionInfo) GetSanctionOpUser() string {
	if m != nil {
		return m.SanctionOpUser
	}
	return ""
}

func (m *ComplexSanctionInfo) GetSanctionReason() string {
	if m != nil {
		return m.SanctionReason
	}
	return ""
}

// 根据目标类型 遍历 举报处理的历史列表
type ScanAllReportPorcHistoryListReq struct {
	TargetType     uint32 `protobuf:"varint,1,req,name=target_type,json=targetType" json:"target_type"`
	IsScanWaitproc bool   `protobuf:"varint,2,req,name=is_scan_waitproc,json=isScanWaitproc" json:"is_scan_waitproc"`
	PageId         uint32 `protobuf:"varint,3,req,name=page_id,json=pageId" json:"page_id"`
	PageCnt        uint32 `protobuf:"varint,4,req,name=page_cnt,json=pageCnt" json:"page_cnt"`
	BeginTs        uint32 `protobuf:"varint,5,opt,name=begin_ts,json=beginTs" json:"begin_ts"`
	EndTs          uint32 `protobuf:"varint,6,opt,name=end_ts,json=endTs" json:"end_ts"`
}

func (m *ScanAllReportPorcHistoryListReq) Reset()         { *m = ScanAllReportPorcHistoryListReq{} }
func (m *ScanAllReportPorcHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*ScanAllReportPorcHistoryListReq) ProtoMessage()    {}
func (*ScanAllReportPorcHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{14}
}

func (m *ScanAllReportPorcHistoryListReq) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *ScanAllReportPorcHistoryListReq) GetIsScanWaitproc() bool {
	if m != nil {
		return m.IsScanWaitproc
	}
	return false
}

func (m *ScanAllReportPorcHistoryListReq) GetPageId() uint32 {
	if m != nil {
		return m.PageId
	}
	return 0
}

func (m *ScanAllReportPorcHistoryListReq) GetPageCnt() uint32 {
	if m != nil {
		return m.PageCnt
	}
	return 0
}

func (m *ScanAllReportPorcHistoryListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ScanAllReportPorcHistoryListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type ScanAllReportPorcHistoryListResp struct {
	SanctionList []*ComplexSanctionInfo `protobuf:"bytes,1,rep,name=sanction_list,json=sanctionList" json:"sanction_list,omitempty"`
	AllCount     uint32                 `protobuf:"varint,2,opt,name=all_count,json=allCount" json:"all_count"`
}

func (m *ScanAllReportPorcHistoryListResp) Reset()         { *m = ScanAllReportPorcHistoryListResp{} }
func (m *ScanAllReportPorcHistoryListResp) String() string { return proto.CompactTextString(m) }
func (*ScanAllReportPorcHistoryListResp) ProtoMessage()    {}
func (*ScanAllReportPorcHistoryListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{15}
}

func (m *ScanAllReportPorcHistoryListResp) GetSanctionList() []*ComplexSanctionInfo {
	if m != nil {
		return m.SanctionList
	}
	return nil
}

func (m *ScanAllReportPorcHistoryListResp) GetAllCount() uint32 {
	if m != nil {
		return m.AllCount
	}
	return 0
}

// 根据目标类型 和 目标ID 获取指定ID的 举报和处理统计
type GetSanctionStatByIdReq struct {
	TargetType uint32 `protobuf:"varint,1,req,name=target_type,json=targetType" json:"target_type"`
	ReqId      uint32 `protobuf:"varint,2,req,name=req_id,json=reqId" json:"req_id"`
}

func (m *GetSanctionStatByIdReq) Reset()                    { *m = GetSanctionStatByIdReq{} }
func (m *GetSanctionStatByIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetSanctionStatByIdReq) ProtoMessage()               {}
func (*GetSanctionStatByIdReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{16} }

func (m *GetSanctionStatByIdReq) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *GetSanctionStatByIdReq) GetReqId() uint32 {
	if m != nil {
		return m.ReqId
	}
	return 0
}

type GetSanctionStatByIdResp struct {
	CurrBannedType     uint32 `protobuf:"varint,1,req,name=curr_banned_type,json=currBannedType" json:"curr_banned_type"`
	RemainBannedSecond uint32 `protobuf:"varint,2,opt,name=remain_banned_second,json=remainBannedSecond" json:"remain_banned_second"`
	ReportStatCnt      uint32 `protobuf:"varint,3,opt,name=report_stat_cnt,json=reportStatCnt" json:"report_stat_cnt"`
	BannedStatCnt      uint32 `protobuf:"varint,4,opt,name=banned_stat_cnt,json=bannedStatCnt" json:"banned_stat_cnt"`
	WarningStatCnt     uint32 `protobuf:"varint,5,opt,name=warning_stat_cnt,json=warningStatCnt" json:"warning_stat_cnt"`
	FalseReportCnt     uint32 `protobuf:"varint,6,opt,name=false_report_cnt,json=falseReportCnt" json:"false_report_cnt"`
	// report user
	ChannelStatCnt    uint32 `protobuf:"varint,7,opt,name=channel_stat_cnt,json=channelStatCnt" json:"channel_stat_cnt"`
	UserStatCnt       uint32 `protobuf:"varint,8,opt,name=user_stat_cnt,json=userStatCnt" json:"user_stat_cnt"`
	DisChannelStatCnt uint32 `protobuf:"varint,9,opt,name=dis_channel_stat_cnt,json=disChannelStatCnt" json:"dis_channel_stat_cnt"`
	DisUserStatCnt    uint32 `protobuf:"varint,10,opt,name=dis_user_stat_cnt,json=disUserStatCnt" json:"dis_user_stat_cnt"`
}

func (m *GetSanctionStatByIdResp) Reset()         { *m = GetSanctionStatByIdResp{} }
func (m *GetSanctionStatByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetSanctionStatByIdResp) ProtoMessage()    {}
func (*GetSanctionStatByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{17}
}

func (m *GetSanctionStatByIdResp) GetCurrBannedType() uint32 {
	if m != nil {
		return m.CurrBannedType
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetRemainBannedSecond() uint32 {
	if m != nil {
		return m.RemainBannedSecond
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetReportStatCnt() uint32 {
	if m != nil {
		return m.ReportStatCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetBannedStatCnt() uint32 {
	if m != nil {
		return m.BannedStatCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetWarningStatCnt() uint32 {
	if m != nil {
		return m.WarningStatCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetFalseReportCnt() uint32 {
	if m != nil {
		return m.FalseReportCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetChannelStatCnt() uint32 {
	if m != nil {
		return m.ChannelStatCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetUserStatCnt() uint32 {
	if m != nil {
		return m.UserStatCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetDisChannelStatCnt() uint32 {
	if m != nil {
		return m.DisChannelStatCnt
	}
	return 0
}

func (m *GetSanctionStatByIdResp) GetDisUserStatCnt() uint32 {
	if m != nil {
		return m.DisUserStatCnt
	}
	return 0
}

// 根据目标类型 和 目标ID 遍历获取指定ID的 举报的历史列表
type ScanSanctionHistoryListByIdReq struct {
	TargetType     uint32 `protobuf:"varint,1,req,name=target_type,json=targetType" json:"target_type"`
	ReqId          uint32 `protobuf:"varint,2,req,name=req_id,json=reqId" json:"req_id"`
	IsScanWaitproc bool   `protobuf:"varint,3,req,name=is_scan_waitproc,json=isScanWaitproc" json:"is_scan_waitproc"`
	PageId         uint32 `protobuf:"varint,4,req,name=page_id,json=pageId" json:"page_id"`
	PageCnt        uint32 `protobuf:"varint,5,req,name=page_cnt,json=pageCnt" json:"page_cnt"`
}

func (m *ScanSanctionHistoryListByIdReq) Reset()         { *m = ScanSanctionHistoryListByIdReq{} }
func (m *ScanSanctionHistoryListByIdReq) String() string { return proto.CompactTextString(m) }
func (*ScanSanctionHistoryListByIdReq) ProtoMessage()    {}
func (*ScanSanctionHistoryListByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{18}
}

func (m *ScanSanctionHistoryListByIdReq) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *ScanSanctionHistoryListByIdReq) GetReqId() uint32 {
	if m != nil {
		return m.ReqId
	}
	return 0
}

func (m *ScanSanctionHistoryListByIdReq) GetIsScanWaitproc() bool {
	if m != nil {
		return m.IsScanWaitproc
	}
	return false
}

func (m *ScanSanctionHistoryListByIdReq) GetPageId() uint32 {
	if m != nil {
		return m.PageId
	}
	return 0
}

func (m *ScanSanctionHistoryListByIdReq) GetPageCnt() uint32 {
	if m != nil {
		return m.PageCnt
	}
	return 0
}

type ScanSanctionHistoryListByIdResp struct {
	SanctionList []*ComplexSanctionInfo `protobuf:"bytes,1,rep,name=sanction_list,json=sanctionList" json:"sanction_list,omitempty"`
	AllCount     uint32                 `protobuf:"varint,2,opt,name=all_count,json=allCount" json:"all_count"`
}

func (m *ScanSanctionHistoryListByIdResp) Reset()         { *m = ScanSanctionHistoryListByIdResp{} }
func (m *ScanSanctionHistoryListByIdResp) String() string { return proto.CompactTextString(m) }
func (*ScanSanctionHistoryListByIdResp) ProtoMessage()    {}
func (*ScanSanctionHistoryListByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{19}
}

func (m *ScanSanctionHistoryListByIdResp) GetSanctionList() []*ComplexSanctionInfo {
	if m != nil {
		return m.SanctionList
	}
	return nil
}

func (m *ScanSanctionHistoryListByIdResp) GetAllCount() uint32 {
	if m != nil {
		return m.AllCount
	}
	return 0
}

// 清理举报的历史列表
type CleanHistoryRecordByIdReq struct {
	IdList     []uint32 `protobuf:"varint,1,rep,name=id_list,json=idList" json:"id_list,omitempty"`
	TargetType uint32   `protobuf:"varint,2,req,name=target_type,json=targetType" json:"target_type"`
}

func (m *CleanHistoryRecordByIdReq) Reset()         { *m = CleanHistoryRecordByIdReq{} }
func (m *CleanHistoryRecordByIdReq) String() string { return proto.CompactTextString(m) }
func (*CleanHistoryRecordByIdReq) ProtoMessage()    {}
func (*CleanHistoryRecordByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{20}
}

func (m *CleanHistoryRecordByIdReq) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *CleanHistoryRecordByIdReq) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

type CleanHistoryRecordByIdResp struct {
}

func (m *CleanHistoryRecordByIdResp) Reset()         { *m = CleanHistoryRecordByIdResp{} }
func (m *CleanHistoryRecordByIdResp) String() string { return proto.CompactTextString(m) }
func (*CleanHistoryRecordByIdResp) ProtoMessage()    {}
func (*CleanHistoryRecordByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{21}
}

// 房间白名单
type AddChannelWhiteListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *AddChannelWhiteListReq) Reset()                    { *m = AddChannelWhiteListReq{} }
func (m *AddChannelWhiteListReq) String() string            { return proto.CompactTextString(m) }
func (*AddChannelWhiteListReq) ProtoMessage()               {}
func (*AddChannelWhiteListReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{22} }

func (m *AddChannelWhiteListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type AddChannelWhiteListResp struct {
}

func (m *AddChannelWhiteListResp) Reset()         { *m = AddChannelWhiteListResp{} }
func (m *AddChannelWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*AddChannelWhiteListResp) ProtoMessage()    {}
func (*AddChannelWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{23}
}

type DelChannelFromWhiteListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *DelChannelFromWhiteListReq) Reset()         { *m = DelChannelFromWhiteListReq{} }
func (m *DelChannelFromWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*DelChannelFromWhiteListReq) ProtoMessage()    {}
func (*DelChannelFromWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{24}
}

func (m *DelChannelFromWhiteListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type DelChannelFromWhiteListResp struct {
}

func (m *DelChannelFromWhiteListResp) Reset()         { *m = DelChannelFromWhiteListResp{} }
func (m *DelChannelFromWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*DelChannelFromWhiteListResp) ProtoMessage()    {}
func (*DelChannelFromWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{25}
}

type GetChannelWhiteListReq struct {
}

func (m *GetChannelWhiteListReq) Reset()                    { *m = GetChannelWhiteListReq{} }
func (m *GetChannelWhiteListReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelWhiteListReq) ProtoMessage()               {}
func (*GetChannelWhiteListReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{26} }

type GetChannelWhiteListResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetChannelWhiteListResp) Reset()         { *m = GetChannelWhiteListResp{} }
func (m *GetChannelWhiteListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelWhiteListResp) ProtoMessage()    {}
func (*GetChannelWhiteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{27}
}

func (m *GetChannelWhiteListResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type GetUserReportHistoryReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	PageIdx   uint32 `protobuf:"varint,2,req,name=page_idx,json=pageIdx" json:"page_idx"`
	PageLimit uint32 `protobuf:"varint,3,req,name=page_limit,json=pageLimit" json:"page_limit"`
}

func (m *GetUserReportHistoryReq) Reset()         { *m = GetUserReportHistoryReq{} }
func (m *GetUserReportHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserReportHistoryReq) ProtoMessage()    {}
func (*GetUserReportHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{28}
}

func (m *GetUserReportHistoryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserReportHistoryReq) GetPageIdx() uint32 {
	if m != nil {
		return m.PageIdx
	}
	return 0
}

func (m *GetUserReportHistoryReq) GetPageLimit() uint32 {
	if m != nil {
		return m.PageLimit
	}
	return 0
}

type GetUserReportHistoryResp struct {
	UserReportList []*UserReportInfo `protobuf:"bytes,1,rep,name=user_report_list,json=userReportList" json:"user_report_list,omitempty"`
}

func (m *GetUserReportHistoryResp) Reset()         { *m = GetUserReportHistoryResp{} }
func (m *GetUserReportHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserReportHistoryResp) ProtoMessage()    {}
func (*GetUserReportHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{29}
}

func (m *GetUserReportHistoryResp) GetUserReportList() []*UserReportInfo {
	if m != nil {
		return m.UserReportList
	}
	return nil
}

type UserReportInfo struct {
	TargetId       uint32 `protobuf:"varint,1,req,name=target_id,json=targetId" json:"target_id"`
	TargetType     uint32 `protobuf:"varint,2,req,name=target_type,json=targetType" json:"target_type"`
	ReportReason   string `protobuf:"bytes,3,req,name=report_reason,json=reportReason" json:"report_reason"`
	ReportTs       uint32 `protobuf:"varint,4,req,name=report_ts,json=reportTs" json:"report_ts"`
	SanctionOpUser string `protobuf:"bytes,5,opt,name=sanction_op_user,json=sanctionOpUser" json:"sanction_op_user"`
	OperTs         uint32 `protobuf:"varint,6,opt,name=oper_ts,json=operTs" json:"oper_ts"`
}

func (m *UserReportInfo) Reset()                    { *m = UserReportInfo{} }
func (m *UserReportInfo) String() string            { return proto.CompactTextString(m) }
func (*UserReportInfo) ProtoMessage()               {}
func (*UserReportInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{30} }

func (m *UserReportInfo) GetTargetId() uint32 {
	if m != nil {
		return m.TargetId
	}
	return 0
}

func (m *UserReportInfo) GetTargetType() uint32 {
	if m != nil {
		return m.TargetType
	}
	return 0
}

func (m *UserReportInfo) GetReportReason() string {
	if m != nil {
		return m.ReportReason
	}
	return ""
}

func (m *UserReportInfo) GetReportTs() uint32 {
	if m != nil {
		return m.ReportTs
	}
	return 0
}

func (m *UserReportInfo) GetSanctionOpUser() string {
	if m != nil {
		return m.SanctionOpUser
	}
	return ""
}

func (m *UserReportInfo) GetOperTs() uint32 {
	if m != nil {
		return m.OperTs
	}
	return 0
}

type UserReportList struct {
	UserReportList []*UserReportInfo `protobuf:"bytes,1,rep,name=user_report_list,json=userReportList" json:"user_report_list,omitempty"`
}

func (m *UserReportList) Reset()                    { *m = UserReportList{} }
func (m *UserReportList) String() string            { return proto.CompactTextString(m) }
func (*UserReportList) ProtoMessage()               {}
func (*UserReportList) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{31} }

func (m *UserReportList) GetUserReportList() []*UserReportInfo {
	if m != nil {
		return m.UserReportList
	}
	return nil
}

// 恶意举报
type MaliciousReportInfo struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Type           uint32 `protobuf:"varint,2,opt,name=type" json:"type"`
	SanctionType   uint32 `protobuf:"varint,3,opt,name=sanction_type,json=sanctionType" json:"sanction_type"`
	SanctionOpUser string `protobuf:"bytes,4,opt,name=sanction_op_user,json=sanctionOpUser" json:"sanction_op_user"`
	BannedType     uint32 `protobuf:"varint,5,opt,name=banned_type,json=bannedType" json:"banned_type"`
	BannedSecond   uint32 `protobuf:"varint,6,opt,name=banned_second,json=bannedSecond" json:"banned_second"`
}

func (m *MaliciousReportInfo) Reset()                    { *m = MaliciousReportInfo{} }
func (m *MaliciousReportInfo) String() string            { return proto.CompactTextString(m) }
func (*MaliciousReportInfo) ProtoMessage()               {}
func (*MaliciousReportInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{32} }

func (m *MaliciousReportInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MaliciousReportInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MaliciousReportInfo) GetSanctionType() uint32 {
	if m != nil {
		return m.SanctionType
	}
	return 0
}

func (m *MaliciousReportInfo) GetSanctionOpUser() string {
	if m != nil {
		return m.SanctionOpUser
	}
	return ""
}

func (m *MaliciousReportInfo) GetBannedType() uint32 {
	if m != nil {
		return m.BannedType
	}
	return 0
}

func (m *MaliciousReportInfo) GetBannedSecond() uint32 {
	if m != nil {
		return m.BannedSecond
	}
	return 0
}

type MaliciousReportInfoList struct {
	InfoList []*MaliciousReportInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList" json:"info_list,omitempty"`
}

func (m *MaliciousReportInfoList) Reset()         { *m = MaliciousReportInfoList{} }
func (m *MaliciousReportInfoList) String() string { return proto.CompactTextString(m) }
func (*MaliciousReportInfoList) ProtoMessage()    {}
func (*MaliciousReportInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{33}
}

func (m *MaliciousReportInfoList) GetInfoList() []*MaliciousReportInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type SendCommonReportReq struct {
	ReportEventBin []byte `protobuf:"bytes,1,req,name=report_event_bin,json=reportEventBin" json:"report_event_bin"`
}

func (m *SendCommonReportReq) Reset()                    { *m = SendCommonReportReq{} }
func (m *SendCommonReportReq) String() string            { return proto.CompactTextString(m) }
func (*SendCommonReportReq) ProtoMessage()               {}
func (*SendCommonReportReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{34} }

func (m *SendCommonReportReq) GetReportEventBin() []byte {
	if m != nil {
		return m.ReportEventBin
	}
	return nil
}

type SendCommonReportResp struct {
}

func (m *SendCommonReportResp) Reset()                    { *m = SendCommonReportResp{} }
func (m *SendCommonReportResp) String() string            { return proto.CompactTextString(m) }
func (*SendCommonReportResp) ProtoMessage()               {}
func (*SendCommonReportResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{35} }

type CheckUserIsImLimitReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *CheckUserIsImLimitReq) Reset()                    { *m = CheckUserIsImLimitReq{} }
func (m *CheckUserIsImLimitReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserIsImLimitReq) ProtoMessage()               {}
func (*CheckUserIsImLimitReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{36} }

func (m *CheckUserIsImLimitReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserIsImLimitResp struct {
	IsLimit bool `protobuf:"varint,2,req,name=is_limit,json=isLimit" json:"is_limit"`
}

func (m *CheckUserIsImLimitResp) Reset()                    { *m = CheckUserIsImLimitResp{} }
func (m *CheckUserIsImLimitResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUserIsImLimitResp) ProtoMessage()               {}
func (*CheckUserIsImLimitResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{37} }

func (m *CheckUserIsImLimitResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

// 频道禁言
type MuteChannelMemberReq struct {
	ChannelId  uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	TargetUids []uint32 `protobuf:"varint,2,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
}

func (m *MuteChannelMemberReq) Reset()                    { *m = MuteChannelMemberReq{} }
func (m *MuteChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*MuteChannelMemberReq) ProtoMessage()               {}
func (*MuteChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{38} }

func (m *MuteChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuteChannelMemberReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type MuteChannelMemberResp struct {
	ChannelId uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MutedUids []uint32 `protobuf:"varint,2,rep,name=muted_uids,json=mutedUids" json:"muted_uids,omitempty"`
}

func (m *MuteChannelMemberResp) Reset()                    { *m = MuteChannelMemberResp{} }
func (m *MuteChannelMemberResp) String() string            { return proto.CompactTextString(m) }
func (*MuteChannelMemberResp) ProtoMessage()               {}
func (*MuteChannelMemberResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{39} }

func (m *MuteChannelMemberResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MuteChannelMemberResp) GetMutedUids() []uint32 {
	if m != nil {
		return m.MutedUids
	}
	return nil
}

type UnmuteChannelMemberReq struct {
	ChannelId  uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	TargetUids []uint32 `protobuf:"varint,2,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
}

func (m *UnmuteChannelMemberReq) Reset()                    { *m = UnmuteChannelMemberReq{} }
func (m *UnmuteChannelMemberReq) String() string            { return proto.CompactTextString(m) }
func (*UnmuteChannelMemberReq) ProtoMessage()               {}
func (*UnmuteChannelMemberReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{40} }

func (m *UnmuteChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UnmuteChannelMemberReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

type UnmuteChannelMemberResp struct {
}

func (m *UnmuteChannelMemberResp) Reset()         { *m = UnmuteChannelMemberResp{} }
func (m *UnmuteChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*UnmuteChannelMemberResp) ProtoMessage()    {}
func (*UnmuteChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{41}
}

// 获取频道禁言成员列表
type GetChannelMuteListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpUid     uint32 `protobuf:"varint,2,req,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *GetChannelMuteListReq) Reset()                    { *m = GetChannelMuteListReq{} }
func (m *GetChannelMuteListReq) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMuteListReq) ProtoMessage()               {}
func (*GetChannelMuteListReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{42} }

func (m *GetChannelMuteListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMuteListReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

// 房间成员
type ChannelMemberBaseInfo struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Ts  uint32 `protobuf:"varint,2,req,name=ts" json:"ts"`
}

func (m *ChannelMemberBaseInfo) Reset()                    { *m = ChannelMemberBaseInfo{} }
func (m *ChannelMemberBaseInfo) String() string            { return proto.CompactTextString(m) }
func (*ChannelMemberBaseInfo) ProtoMessage()               {}
func (*ChannelMemberBaseInfo) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{43} }

func (m *ChannelMemberBaseInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMemberBaseInfo) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

type GetChannelMuteListResp struct {
	ChannelId uint32                   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MuteList  []*ChannelMemberBaseInfo `protobuf:"bytes,2,rep,name=mute_list,json=muteList" json:"mute_list,omitempty"`
}

func (m *GetChannelMuteListResp) Reset()                    { *m = GetChannelMuteListResp{} }
func (m *GetChannelMuteListResp) String() string            { return proto.CompactTextString(m) }
func (*GetChannelMuteListResp) ProtoMessage()               {}
func (*GetChannelMuteListResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{44} }

func (m *GetChannelMuteListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelMuteListResp) GetMuteList() []*ChannelMemberBaseInfo {
	if m != nil {
		return m.MuteList
	}
	return nil
}

// 检查用户是否被禁言
type CheckUserIsMuteReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CheckUserIsMuteReq) Reset()                    { *m = CheckUserIsMuteReq{} }
func (m *CheckUserIsMuteReq) String() string            { return proto.CompactTextString(m) }
func (*CheckUserIsMuteReq) ProtoMessage()               {}
func (*CheckUserIsMuteReq) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{45} }

func (m *CheckUserIsMuteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserIsMuteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckUserIsMuteResp struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	IsMute    bool   `protobuf:"varint,3,req,name=is_mute,json=isMute" json:"is_mute"`
}

func (m *CheckUserIsMuteResp) Reset()                    { *m = CheckUserIsMuteResp{} }
func (m *CheckUserIsMuteResp) String() string            { return proto.CompactTextString(m) }
func (*CheckUserIsMuteResp) ProtoMessage()               {}
func (*CheckUserIsMuteResp) Descriptor() ([]byte, []int) { return fileDescriptorGreenbaba, []int{46} }

func (m *CheckUserIsMuteResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserIsMuteResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckUserIsMuteResp) GetIsMute() bool {
	if m != nil {
		return m.IsMute
	}
	return false
}

type CleanChannelMuteListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CleanChannelMuteListReq) Reset()         { *m = CleanChannelMuteListReq{} }
func (m *CleanChannelMuteListReq) String() string { return proto.CompactTextString(m) }
func (*CleanChannelMuteListReq) ProtoMessage()    {}
func (*CleanChannelMuteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{47}
}

func (m *CleanChannelMuteListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CleanChannelMuteListResp struct {
}

func (m *CleanChannelMuteListResp) Reset()         { *m = CleanChannelMuteListResp{} }
func (m *CleanChannelMuteListResp) String() string { return proto.CompactTextString(m) }
func (*CleanChannelMuteListResp) ProtoMessage()    {}
func (*CleanChannelMuteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{48}
}

type CheckUserKickoutFromChannelReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *CheckUserKickoutFromChannelReq) Reset()         { *m = CheckUserKickoutFromChannelReq{} }
func (m *CheckUserKickoutFromChannelReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserKickoutFromChannelReq) ProtoMessage()    {}
func (*CheckUserKickoutFromChannelReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{49}
}

func (m *CheckUserKickoutFromChannelReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CheckUserKickoutFromChannelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserKickoutFromChannelResp struct {
	IsKicked bool `protobuf:"varint,1,opt,name=is_kicked,json=isKicked" json:"is_kicked"`
}

func (m *CheckUserKickoutFromChannelResp) Reset()         { *m = CheckUserKickoutFromChannelResp{} }
func (m *CheckUserKickoutFromChannelResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserKickoutFromChannelResp) ProtoMessage()    {}
func (*CheckUserKickoutFromChannelResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{50}
}

func (m *CheckUserKickoutFromChannelResp) GetIsKicked() bool {
	if m != nil {
		return m.IsKicked
	}
	return false
}

type KickoutChannelMemberReq struct {
	ChannelId   uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	TargetUids  []uint32 `protobuf:"varint,2,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
	BanDuration uint32   `protobuf:"varint,3,opt,name=ban_duration,json=banDuration" json:"ban_duration"`
}

func (m *KickoutChannelMemberReq) Reset()         { *m = KickoutChannelMemberReq{} }
func (m *KickoutChannelMemberReq) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMemberReq) ProtoMessage()    {}
func (*KickoutChannelMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{51}
}

func (m *KickoutChannelMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMemberReq) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

func (m *KickoutChannelMemberReq) GetBanDuration() uint32 {
	if m != nil {
		return m.BanDuration
	}
	return 0
}

type KickoutChannelMemberResp struct {
	ChannelId     uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	KickoutedUids []uint32 `protobuf:"varint,2,rep,name=kickouted_uids,json=kickoutedUids" json:"kickouted_uids,omitempty"`
}

func (m *KickoutChannelMemberResp) Reset()         { *m = KickoutChannelMemberResp{} }
func (m *KickoutChannelMemberResp) String() string { return proto.CompactTextString(m) }
func (*KickoutChannelMemberResp) ProtoMessage()    {}
func (*KickoutChannelMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGreenbaba, []int{52}
}

func (m *KickoutChannelMemberResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *KickoutChannelMemberResp) GetKickoutedUids() []uint32 {
	if m != nil {
		return m.KickoutedUids
	}
	return nil
}

func init() {
	proto.RegisterType((*GreenBabaSanctionInfo)(nil), "greenBaba.GreenBabaSanctionInfo")
	proto.RegisterType((*SetSanctionReq)(nil), "greenBaba.SetSanctionReq")
	proto.RegisterType((*SetSanctionResp)(nil), "greenBaba.SetSanctionResp")
	proto.RegisterType((*GetCurrBannedStatByIdReq)(nil), "greenBaba.GetCurrBannedStatByIdReq")
	proto.RegisterType((*GetCurrBannedStatByIdResp)(nil), "greenBaba.GetCurrBannedStatByIdResp")
	proto.RegisterType((*BatchGetCurrBannedStatByIdReq)(nil), "greenBaba.BatchGetCurrBannedStatByIdReq")
	proto.RegisterType((*CurrBannedStatInfo)(nil), "greenBaba.CurrBannedStatInfo")
	proto.RegisterType((*BatchGetCurrBannedStatByIdResp)(nil), "greenBaba.BatchGetCurrBannedStatByIdResp")
	proto.RegisterType((*GetBannedListByTargetTypeReq)(nil), "greenBaba.GetBannedListByTargetTypeReq")
	proto.RegisterType((*GetBannedListByTargetTypeResp)(nil), "greenBaba.GetBannedListByTargetTypeResp")
	proto.RegisterType((*SendReportReq)(nil), "greenBaba.SendReportReq")
	proto.RegisterType((*SendReportResp)(nil), "greenBaba.SendReportResp")
	proto.RegisterType((*BaseReportInfo)(nil), "greenBaba.BaseReportInfo")
	proto.RegisterType((*ComplexSanctionInfo)(nil), "greenBaba.ComplexSanctionInfo")
	proto.RegisterType((*ScanAllReportPorcHistoryListReq)(nil), "greenBaba.ScanAllReportPorcHistoryListReq")
	proto.RegisterType((*ScanAllReportPorcHistoryListResp)(nil), "greenBaba.ScanAllReportPorcHistoryListResp")
	proto.RegisterType((*GetSanctionStatByIdReq)(nil), "greenBaba.GetSanctionStatByIdReq")
	proto.RegisterType((*GetSanctionStatByIdResp)(nil), "greenBaba.GetSanctionStatByIdResp")
	proto.RegisterType((*ScanSanctionHistoryListByIdReq)(nil), "greenBaba.ScanSanctionHistoryListByIdReq")
	proto.RegisterType((*ScanSanctionHistoryListByIdResp)(nil), "greenBaba.ScanSanctionHistoryListByIdResp")
	proto.RegisterType((*CleanHistoryRecordByIdReq)(nil), "greenBaba.CleanHistoryRecordByIdReq")
	proto.RegisterType((*CleanHistoryRecordByIdResp)(nil), "greenBaba.CleanHistoryRecordByIdResp")
	proto.RegisterType((*AddChannelWhiteListReq)(nil), "greenBaba.AddChannelWhiteListReq")
	proto.RegisterType((*AddChannelWhiteListResp)(nil), "greenBaba.AddChannelWhiteListResp")
	proto.RegisterType((*DelChannelFromWhiteListReq)(nil), "greenBaba.DelChannelFromWhiteListReq")
	proto.RegisterType((*DelChannelFromWhiteListResp)(nil), "greenBaba.DelChannelFromWhiteListResp")
	proto.RegisterType((*GetChannelWhiteListReq)(nil), "greenBaba.GetChannelWhiteListReq")
	proto.RegisterType((*GetChannelWhiteListResp)(nil), "greenBaba.GetChannelWhiteListResp")
	proto.RegisterType((*GetUserReportHistoryReq)(nil), "greenBaba.GetUserReportHistoryReq")
	proto.RegisterType((*GetUserReportHistoryResp)(nil), "greenBaba.GetUserReportHistoryResp")
	proto.RegisterType((*UserReportInfo)(nil), "greenBaba.UserReportInfo")
	proto.RegisterType((*UserReportList)(nil), "greenBaba.UserReportList")
	proto.RegisterType((*MaliciousReportInfo)(nil), "greenBaba.MaliciousReportInfo")
	proto.RegisterType((*MaliciousReportInfoList)(nil), "greenBaba.MaliciousReportInfoList")
	proto.RegisterType((*SendCommonReportReq)(nil), "greenBaba.SendCommonReportReq")
	proto.RegisterType((*SendCommonReportResp)(nil), "greenBaba.SendCommonReportResp")
	proto.RegisterType((*CheckUserIsImLimitReq)(nil), "greenBaba.CheckUserIsImLimitReq")
	proto.RegisterType((*CheckUserIsImLimitResp)(nil), "greenBaba.CheckUserIsImLimitResp")
	proto.RegisterType((*MuteChannelMemberReq)(nil), "greenBaba.MuteChannelMemberReq")
	proto.RegisterType((*MuteChannelMemberResp)(nil), "greenBaba.MuteChannelMemberResp")
	proto.RegisterType((*UnmuteChannelMemberReq)(nil), "greenBaba.UnmuteChannelMemberReq")
	proto.RegisterType((*UnmuteChannelMemberResp)(nil), "greenBaba.UnmuteChannelMemberResp")
	proto.RegisterType((*GetChannelMuteListReq)(nil), "greenBaba.GetChannelMuteListReq")
	proto.RegisterType((*ChannelMemberBaseInfo)(nil), "greenBaba.ChannelMemberBaseInfo")
	proto.RegisterType((*GetChannelMuteListResp)(nil), "greenBaba.GetChannelMuteListResp")
	proto.RegisterType((*CheckUserIsMuteReq)(nil), "greenBaba.CheckUserIsMuteReq")
	proto.RegisterType((*CheckUserIsMuteResp)(nil), "greenBaba.CheckUserIsMuteResp")
	proto.RegisterType((*CleanChannelMuteListReq)(nil), "greenBaba.CleanChannelMuteListReq")
	proto.RegisterType((*CleanChannelMuteListResp)(nil), "greenBaba.CleanChannelMuteListResp")
	proto.RegisterType((*CheckUserKickoutFromChannelReq)(nil), "greenBaba.CheckUserKickoutFromChannelReq")
	proto.RegisterType((*CheckUserKickoutFromChannelResp)(nil), "greenBaba.CheckUserKickoutFromChannelResp")
	proto.RegisterType((*KickoutChannelMemberReq)(nil), "greenBaba.KickoutChannelMemberReq")
	proto.RegisterType((*KickoutChannelMemberResp)(nil), "greenBaba.KickoutChannelMemberResp")
	proto.RegisterEnum("greenBaba.ENUM_TARGET_TYPE", ENUM_TARGET_TYPE_name, ENUM_TARGET_TYPE_value)
	proto.RegisterEnum("greenBaba.ENUM_SANCTION_OP_TYPE", ENUM_SANCTION_OP_TYPE_name, ENUM_SANCTION_OP_TYPE_value)
	proto.RegisterEnum("greenBaba.ENUM_BANNED_TYPE", ENUM_BANNED_TYPE_name, ENUM_BANNED_TYPE_value)
	proto.RegisterEnum("greenBaba.ENUM_REPORT_TYPE", ENUM_REPORT_TYPE_name, ENUM_REPORT_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Greenbaba service

type GreenbabaClient interface {
	// 对目标实施制裁
	SetSanction(ctx context.Context, in *SetSanctionReq, opts ...grpc.CallOption) (*SetSanctionResp, error)
	// 根据目标ID 获取 当前封禁状态列表
	GetCurrBannedStatById(ctx context.Context, in *GetCurrBannedStatByIdReq, opts ...grpc.CallOption) (*GetCurrBannedStatByIdResp, error)
	// 根据制裁目标类型 获取 当前被封禁的列表
	GetBannedListByTargetType(ctx context.Context, in *GetBannedListByTargetTypeReq, opts ...grpc.CallOption) (*GetBannedListByTargetTypeResp, error)
	// 举报
	SendReport(ctx context.Context, in *SendReportReq, opts ...grpc.CallOption) (*SendReportResp, error)
	// 根据目标类型 遍历 已经处理的历史列表(按时间排序)
	ScanAllReportPorcHistoryList(ctx context.Context, in *ScanAllReportPorcHistoryListReq, opts ...grpc.CallOption) (*ScanAllReportPorcHistoryListResp, error)
	// 根据目标类型 和 目标ID 获取指定ID的 举报和处理统计
	GetSanctionStatById(ctx context.Context, in *GetSanctionStatByIdReq, opts ...grpc.CallOption) (*GetSanctionStatByIdResp, error)
	// 根据目标类型 和 目标ID 遍历获取指定ID的 历史处理列表
	ScanSanctionHistoryListById(ctx context.Context, in *ScanSanctionHistoryListByIdReq, opts ...grpc.CallOption) (*ScanSanctionHistoryListByIdResp, error)
	// 清除记录
	CleanHistoryRecordById(ctx context.Context, in *CleanHistoryRecordByIdReq, opts ...grpc.CallOption) (*CleanHistoryRecordByIdResp, error)
	// 加白名单
	AddChannelWhiteList(ctx context.Context, in *AddChannelWhiteListReq, opts ...grpc.CallOption) (*AddChannelWhiteListResp, error)
	// 删白名单
	DelChannelFromWhiteList(ctx context.Context, in *DelChannelFromWhiteListReq, opts ...grpc.CallOption) (*DelChannelFromWhiteListResp, error)
	// 获取白名单
	GetChannelWhiteList(ctx context.Context, in *GetChannelWhiteListReq, opts ...grpc.CallOption) (*GetChannelWhiteListResp, error)
	// 获取用户举报记录
	GetUserReportHistory(ctx context.Context, in *GetUserReportHistoryReq, opts ...grpc.CallOption) (*GetUserReportHistoryResp, error)
	// 获取对话
	SendCommonReport(ctx context.Context, in *SendCommonReportReq, opts ...grpc.CallOption) (*SendCommonReportResp, error)
	BatchGetCurrBannedStatById(ctx context.Context, in *BatchGetCurrBannedStatByIdReq, opts ...grpc.CallOption) (*BatchGetCurrBannedStatByIdResp, error)
	CheckUserIsImLimit(ctx context.Context, in *CheckUserIsImLimitReq, opts ...grpc.CallOption) (*CheckUserIsImLimitResp, error)
	// 新的简单禁言接口
	MuteChannelMember(ctx context.Context, in *MuteChannelMemberReq, opts ...grpc.CallOption) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(ctx context.Context, in *UnmuteChannelMemberReq, opts ...grpc.CallOption) (*UnmuteChannelMemberResp, error)
	CheckUserIsMute(ctx context.Context, in *CheckUserIsMuteReq, opts ...grpc.CallOption) (*CheckUserIsMuteResp, error)
	GetChannelMuteList(ctx context.Context, in *GetChannelMuteListReq, opts ...grpc.CallOption) (*GetChannelMuteListResp, error)
	CleanChannelMuteList(ctx context.Context, in *CleanChannelMuteListReq, opts ...grpc.CallOption) (*CleanChannelMuteListResp, error)
	// 检测用户是否被踢
	CheckUserKickoutFromChannel(ctx context.Context, in *CheckUserKickoutFromChannelReq, opts ...grpc.CallOption) (*CheckUserKickoutFromChannelResp, error)
	KickoutChannelMember(ctx context.Context, in *KickoutChannelMemberReq, opts ...grpc.CallOption) (*KickoutChannelMemberResp, error)
}

type greenbabaClient struct {
	cc *grpc.ClientConn
}

func NewGreenbabaClient(cc *grpc.ClientConn) GreenbabaClient {
	return &greenbabaClient{cc}
}

func (c *greenbabaClient) SetSanction(ctx context.Context, in *SetSanctionReq, opts ...grpc.CallOption) (*SetSanctionResp, error) {
	out := new(SetSanctionResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/SetSanction", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) GetCurrBannedStatById(ctx context.Context, in *GetCurrBannedStatByIdReq, opts ...grpc.CallOption) (*GetCurrBannedStatByIdResp, error) {
	out := new(GetCurrBannedStatByIdResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/GetCurrBannedStatById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) GetBannedListByTargetType(ctx context.Context, in *GetBannedListByTargetTypeReq, opts ...grpc.CallOption) (*GetBannedListByTargetTypeResp, error) {
	out := new(GetBannedListByTargetTypeResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/GetBannedListByTargetType", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) SendReport(ctx context.Context, in *SendReportReq, opts ...grpc.CallOption) (*SendReportResp, error) {
	out := new(SendReportResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/SendReport", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) ScanAllReportPorcHistoryList(ctx context.Context, in *ScanAllReportPorcHistoryListReq, opts ...grpc.CallOption) (*ScanAllReportPorcHistoryListResp, error) {
	out := new(ScanAllReportPorcHistoryListResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/ScanAllReportPorcHistoryList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) GetSanctionStatById(ctx context.Context, in *GetSanctionStatByIdReq, opts ...grpc.CallOption) (*GetSanctionStatByIdResp, error) {
	out := new(GetSanctionStatByIdResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/GetSanctionStatById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) ScanSanctionHistoryListById(ctx context.Context, in *ScanSanctionHistoryListByIdReq, opts ...grpc.CallOption) (*ScanSanctionHistoryListByIdResp, error) {
	out := new(ScanSanctionHistoryListByIdResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/ScanSanctionHistoryListById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) CleanHistoryRecordById(ctx context.Context, in *CleanHistoryRecordByIdReq, opts ...grpc.CallOption) (*CleanHistoryRecordByIdResp, error) {
	out := new(CleanHistoryRecordByIdResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/CleanHistoryRecordById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) AddChannelWhiteList(ctx context.Context, in *AddChannelWhiteListReq, opts ...grpc.CallOption) (*AddChannelWhiteListResp, error) {
	out := new(AddChannelWhiteListResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/AddChannelWhiteList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) DelChannelFromWhiteList(ctx context.Context, in *DelChannelFromWhiteListReq, opts ...grpc.CallOption) (*DelChannelFromWhiteListResp, error) {
	out := new(DelChannelFromWhiteListResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/DelChannelFromWhiteList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) GetChannelWhiteList(ctx context.Context, in *GetChannelWhiteListReq, opts ...grpc.CallOption) (*GetChannelWhiteListResp, error) {
	out := new(GetChannelWhiteListResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/GetChannelWhiteList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) GetUserReportHistory(ctx context.Context, in *GetUserReportHistoryReq, opts ...grpc.CallOption) (*GetUserReportHistoryResp, error) {
	out := new(GetUserReportHistoryResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/GetUserReportHistory", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) SendCommonReport(ctx context.Context, in *SendCommonReportReq, opts ...grpc.CallOption) (*SendCommonReportResp, error) {
	out := new(SendCommonReportResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/SendCommonReport", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) BatchGetCurrBannedStatById(ctx context.Context, in *BatchGetCurrBannedStatByIdReq, opts ...grpc.CallOption) (*BatchGetCurrBannedStatByIdResp, error) {
	out := new(BatchGetCurrBannedStatByIdResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/BatchGetCurrBannedStatById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) CheckUserIsImLimit(ctx context.Context, in *CheckUserIsImLimitReq, opts ...grpc.CallOption) (*CheckUserIsImLimitResp, error) {
	out := new(CheckUserIsImLimitResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/CheckUserIsImLimit", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) MuteChannelMember(ctx context.Context, in *MuteChannelMemberReq, opts ...grpc.CallOption) (*MuteChannelMemberResp, error) {
	out := new(MuteChannelMemberResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/MuteChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) UnmuteChannelMember(ctx context.Context, in *UnmuteChannelMemberReq, opts ...grpc.CallOption) (*UnmuteChannelMemberResp, error) {
	out := new(UnmuteChannelMemberResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/UnmuteChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) CheckUserIsMute(ctx context.Context, in *CheckUserIsMuteReq, opts ...grpc.CallOption) (*CheckUserIsMuteResp, error) {
	out := new(CheckUserIsMuteResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/CheckUserIsMute", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) GetChannelMuteList(ctx context.Context, in *GetChannelMuteListReq, opts ...grpc.CallOption) (*GetChannelMuteListResp, error) {
	out := new(GetChannelMuteListResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/GetChannelMuteList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) CleanChannelMuteList(ctx context.Context, in *CleanChannelMuteListReq, opts ...grpc.CallOption) (*CleanChannelMuteListResp, error) {
	out := new(CleanChannelMuteListResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/CleanChannelMuteList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) CheckUserKickoutFromChannel(ctx context.Context, in *CheckUserKickoutFromChannelReq, opts ...grpc.CallOption) (*CheckUserKickoutFromChannelResp, error) {
	out := new(CheckUserKickoutFromChannelResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/CheckUserKickoutFromChannel", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *greenbabaClient) KickoutChannelMember(ctx context.Context, in *KickoutChannelMemberReq, opts ...grpc.CallOption) (*KickoutChannelMemberResp, error) {
	out := new(KickoutChannelMemberResp)
	err := grpc.Invoke(ctx, "/greenBaba.greenbaba/KickoutChannelMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Greenbaba service

type GreenbabaServer interface {
	// 对目标实施制裁
	SetSanction(context.Context, *SetSanctionReq) (*SetSanctionResp, error)
	// 根据目标ID 获取 当前封禁状态列表
	GetCurrBannedStatById(context.Context, *GetCurrBannedStatByIdReq) (*GetCurrBannedStatByIdResp, error)
	// 根据制裁目标类型 获取 当前被封禁的列表
	GetBannedListByTargetType(context.Context, *GetBannedListByTargetTypeReq) (*GetBannedListByTargetTypeResp, error)
	// 举报
	SendReport(context.Context, *SendReportReq) (*SendReportResp, error)
	// 根据目标类型 遍历 已经处理的历史列表(按时间排序)
	ScanAllReportPorcHistoryList(context.Context, *ScanAllReportPorcHistoryListReq) (*ScanAllReportPorcHistoryListResp, error)
	// 根据目标类型 和 目标ID 获取指定ID的 举报和处理统计
	GetSanctionStatById(context.Context, *GetSanctionStatByIdReq) (*GetSanctionStatByIdResp, error)
	// 根据目标类型 和 目标ID 遍历获取指定ID的 历史处理列表
	ScanSanctionHistoryListById(context.Context, *ScanSanctionHistoryListByIdReq) (*ScanSanctionHistoryListByIdResp, error)
	// 清除记录
	CleanHistoryRecordById(context.Context, *CleanHistoryRecordByIdReq) (*CleanHistoryRecordByIdResp, error)
	// 加白名单
	AddChannelWhiteList(context.Context, *AddChannelWhiteListReq) (*AddChannelWhiteListResp, error)
	// 删白名单
	DelChannelFromWhiteList(context.Context, *DelChannelFromWhiteListReq) (*DelChannelFromWhiteListResp, error)
	// 获取白名单
	GetChannelWhiteList(context.Context, *GetChannelWhiteListReq) (*GetChannelWhiteListResp, error)
	// 获取用户举报记录
	GetUserReportHistory(context.Context, *GetUserReportHistoryReq) (*GetUserReportHistoryResp, error)
	// 获取对话
	SendCommonReport(context.Context, *SendCommonReportReq) (*SendCommonReportResp, error)
	BatchGetCurrBannedStatById(context.Context, *BatchGetCurrBannedStatByIdReq) (*BatchGetCurrBannedStatByIdResp, error)
	CheckUserIsImLimit(context.Context, *CheckUserIsImLimitReq) (*CheckUserIsImLimitResp, error)
	// 新的简单禁言接口
	MuteChannelMember(context.Context, *MuteChannelMemberReq) (*MuteChannelMemberResp, error)
	UnmuteChannelMember(context.Context, *UnmuteChannelMemberReq) (*UnmuteChannelMemberResp, error)
	CheckUserIsMute(context.Context, *CheckUserIsMuteReq) (*CheckUserIsMuteResp, error)
	GetChannelMuteList(context.Context, *GetChannelMuteListReq) (*GetChannelMuteListResp, error)
	CleanChannelMuteList(context.Context, *CleanChannelMuteListReq) (*CleanChannelMuteListResp, error)
	// 检测用户是否被踢
	CheckUserKickoutFromChannel(context.Context, *CheckUserKickoutFromChannelReq) (*CheckUserKickoutFromChannelResp, error)
	KickoutChannelMember(context.Context, *KickoutChannelMemberReq) (*KickoutChannelMemberResp, error)
}

func RegisterGreenbabaServer(s *grpc.Server, srv GreenbabaServer) {
	s.RegisterService(&_Greenbaba_serviceDesc, srv)
}

func _Greenbaba_SetSanction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSanctionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).SetSanction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/SetSanction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).SetSanction(ctx, req.(*SetSanctionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_GetCurrBannedStatById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurrBannedStatByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).GetCurrBannedStatById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/GetCurrBannedStatById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).GetCurrBannedStatById(ctx, req.(*GetCurrBannedStatByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_GetBannedListByTargetType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedListByTargetTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).GetBannedListByTargetType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/GetBannedListByTargetType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).GetBannedListByTargetType(ctx, req.(*GetBannedListByTargetTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_SendReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).SendReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/SendReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).SendReport(ctx, req.(*SendReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_ScanAllReportPorcHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanAllReportPorcHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).ScanAllReportPorcHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/ScanAllReportPorcHistoryList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).ScanAllReportPorcHistoryList(ctx, req.(*ScanAllReportPorcHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_GetSanctionStatById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSanctionStatByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).GetSanctionStatById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/GetSanctionStatById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).GetSanctionStatById(ctx, req.(*GetSanctionStatByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_ScanSanctionHistoryListById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScanSanctionHistoryListByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).ScanSanctionHistoryListById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/ScanSanctionHistoryListById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).ScanSanctionHistoryListById(ctx, req.(*ScanSanctionHistoryListByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_CleanHistoryRecordById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanHistoryRecordByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).CleanHistoryRecordById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/CleanHistoryRecordById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).CleanHistoryRecordById(ctx, req.(*CleanHistoryRecordByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_AddChannelWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).AddChannelWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/AddChannelWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).AddChannelWhiteList(ctx, req.(*AddChannelWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_DelChannelFromWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelChannelFromWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).DelChannelFromWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/DelChannelFromWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).DelChannelFromWhiteList(ctx, req.(*DelChannelFromWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_GetChannelWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).GetChannelWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/GetChannelWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).GetChannelWhiteList(ctx, req.(*GetChannelWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_GetUserReportHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserReportHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).GetUserReportHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/GetUserReportHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).GetUserReportHistory(ctx, req.(*GetUserReportHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_SendCommonReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCommonReportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).SendCommonReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/SendCommonReport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).SendCommonReport(ctx, req.(*SendCommonReportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_BatchGetCurrBannedStatById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCurrBannedStatByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).BatchGetCurrBannedStatById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/BatchGetCurrBannedStatById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).BatchGetCurrBannedStatById(ctx, req.(*BatchGetCurrBannedStatByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_CheckUserIsImLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIsImLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).CheckUserIsImLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/CheckUserIsImLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).CheckUserIsImLimit(ctx, req.(*CheckUserIsImLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_MuteChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MuteChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).MuteChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/MuteChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).MuteChannelMember(ctx, req.(*MuteChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_UnmuteChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnmuteChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).UnmuteChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/UnmuteChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).UnmuteChannelMember(ctx, req.(*UnmuteChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_CheckUserIsMute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserIsMuteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).CheckUserIsMute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/CheckUserIsMute",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).CheckUserIsMute(ctx, req.(*CheckUserIsMuteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_GetChannelMuteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMuteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).GetChannelMuteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/GetChannelMuteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).GetChannelMuteList(ctx, req.(*GetChannelMuteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_CleanChannelMuteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanChannelMuteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).CleanChannelMuteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/CleanChannelMuteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).CleanChannelMuteList(ctx, req.(*CleanChannelMuteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_CheckUserKickoutFromChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserKickoutFromChannelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).CheckUserKickoutFromChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/CheckUserKickoutFromChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).CheckUserKickoutFromChannel(ctx, req.(*CheckUserKickoutFromChannelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Greenbaba_KickoutChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KickoutChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GreenbabaServer).KickoutChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/greenBaba.greenbaba/KickoutChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GreenbabaServer).KickoutChannelMember(ctx, req.(*KickoutChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Greenbaba_serviceDesc = grpc.ServiceDesc{
	ServiceName: "greenBaba.greenbaba",
	HandlerType: (*GreenbabaServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetSanction",
			Handler:    _Greenbaba_SetSanction_Handler,
		},
		{
			MethodName: "GetCurrBannedStatById",
			Handler:    _Greenbaba_GetCurrBannedStatById_Handler,
		},
		{
			MethodName: "GetBannedListByTargetType",
			Handler:    _Greenbaba_GetBannedListByTargetType_Handler,
		},
		{
			MethodName: "SendReport",
			Handler:    _Greenbaba_SendReport_Handler,
		},
		{
			MethodName: "ScanAllReportPorcHistoryList",
			Handler:    _Greenbaba_ScanAllReportPorcHistoryList_Handler,
		},
		{
			MethodName: "GetSanctionStatById",
			Handler:    _Greenbaba_GetSanctionStatById_Handler,
		},
		{
			MethodName: "ScanSanctionHistoryListById",
			Handler:    _Greenbaba_ScanSanctionHistoryListById_Handler,
		},
		{
			MethodName: "CleanHistoryRecordById",
			Handler:    _Greenbaba_CleanHistoryRecordById_Handler,
		},
		{
			MethodName: "AddChannelWhiteList",
			Handler:    _Greenbaba_AddChannelWhiteList_Handler,
		},
		{
			MethodName: "DelChannelFromWhiteList",
			Handler:    _Greenbaba_DelChannelFromWhiteList_Handler,
		},
		{
			MethodName: "GetChannelWhiteList",
			Handler:    _Greenbaba_GetChannelWhiteList_Handler,
		},
		{
			MethodName: "GetUserReportHistory",
			Handler:    _Greenbaba_GetUserReportHistory_Handler,
		},
		{
			MethodName: "SendCommonReport",
			Handler:    _Greenbaba_SendCommonReport_Handler,
		},
		{
			MethodName: "BatchGetCurrBannedStatById",
			Handler:    _Greenbaba_BatchGetCurrBannedStatById_Handler,
		},
		{
			MethodName: "CheckUserIsImLimit",
			Handler:    _Greenbaba_CheckUserIsImLimit_Handler,
		},
		{
			MethodName: "MuteChannelMember",
			Handler:    _Greenbaba_MuteChannelMember_Handler,
		},
		{
			MethodName: "UnmuteChannelMember",
			Handler:    _Greenbaba_UnmuteChannelMember_Handler,
		},
		{
			MethodName: "CheckUserIsMute",
			Handler:    _Greenbaba_CheckUserIsMute_Handler,
		},
		{
			MethodName: "GetChannelMuteList",
			Handler:    _Greenbaba_GetChannelMuteList_Handler,
		},
		{
			MethodName: "CleanChannelMuteList",
			Handler:    _Greenbaba_CleanChannelMuteList_Handler,
		},
		{
			MethodName: "CheckUserKickoutFromChannel",
			Handler:    _Greenbaba_CheckUserKickoutFromChannel_Handler,
		},
		{
			MethodName: "KickoutChannelMember",
			Handler:    _Greenbaba_KickoutChannelMember_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/greenBaba/greenbaba.proto",
}

func (m *GreenBabaSanctionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GreenBabaSanctionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BannedType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.RemainSecond))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionType))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.SanctionReason)))
	i += copy(dAtA[i:], m.SanctionReason)
	return i, nil
}

func (m *SetSanctionReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetSanctionReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SanctionList) > 0 {
		for _, msg := range m.SanctionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.OpUserinfo)))
	i += copy(dAtA[i:], m.OpUserinfo)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionObj))
	return i, nil
}

func (m *SetSanctionResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetSanctionResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetCurrBannedStatByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCurrBannedStatByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionTargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReqId))
	return i, nil
}

func (m *GetCurrBannedStatByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCurrBannedStatByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.BannedList) > 0 {
		for _, msg := range m.BannedList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetCurrBannedStatByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetCurrBannedStatByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionTargetType))
	if len(m.ReqIdList) > 0 {
		for _, num := range m.ReqIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *CurrBannedStatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CurrBannedStatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionTargetType))
	if len(m.BannedList) > 0 {
		for _, msg := range m.BannedList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchGetCurrBannedStatByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetCurrBannedStatByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CurrbannedList) > 0 {
		for _, msg := range m.CurrbannedList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetBannedListByTargetTypeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedListByTargetTypeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionTargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BeginIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetBannedListByTargetTypeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetBannedListByTargetTypeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.BannedList) > 0 {
		for _, msg := range m.BannedList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TotalSize))
	return i, nil
}

func (m *SendReportReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendReportReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.OpTtAcc)))
	i += copy(dAtA[i:], m.OpTtAcc)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetChannel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetUid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportType))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.ReportReason)))
	i += copy(dAtA[i:], m.ReportReason)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x38
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportUid))
	return i, nil
}

func (m *SendReportResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendReportResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BaseReportInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BaseReportInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.OpTtAcc)))
	i += copy(dAtA[i:], m.OpTtAcc)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.ReportReason)))
	i += copy(dAtA[i:], m.ReportReason)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportUid))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.PicUrl)))
	i += copy(dAtA[i:], m.PicUrl)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportTs))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetUid))
	return i, nil
}

func (m *ComplexSanctionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ComplexSanctionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetId))
	if len(m.ReportBaseList) > 0 {
		for _, msg := range m.ReportBaseList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReprotRealCnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionOpType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BannedOpType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BannedSecond))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.LastUpdateTs))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.SanctionOpUser)))
	i += copy(dAtA[i:], m.SanctionOpUser)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.SanctionReason)))
	i += copy(dAtA[i:], m.SanctionReason)
	return i, nil
}

func (m *ScanAllReportPorcHistoryListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanAllReportPorcHistoryListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x10
	i++
	if m.IsScanWaitproc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.PageId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.PageCnt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BeginTs))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.EndTs))
	return i, nil
}

func (m *ScanAllReportPorcHistoryListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanAllReportPorcHistoryListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SanctionList) > 0 {
		for _, msg := range m.SanctionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.AllCount))
	return i, nil
}

func (m *GetSanctionStatByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSanctionStatByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReqId))
	return i, nil
}

func (m *GetSanctionStatByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSanctionStatByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.CurrBannedType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.RemainBannedSecond))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportStatCnt))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BannedStatCnt))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.WarningStatCnt))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.FalseReportCnt))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelStatCnt))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.UserStatCnt))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.DisChannelStatCnt))
	dAtA[i] = 0x50
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.DisUserStatCnt))
	return i, nil
}

func (m *ScanSanctionHistoryListByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanSanctionHistoryListByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReqId))
	dAtA[i] = 0x18
	i++
	if m.IsScanWaitproc {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.PageId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.PageCnt))
	return i, nil
}

func (m *ScanSanctionHistoryListByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ScanSanctionHistoryListByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SanctionList) > 0 {
		for _, msg := range m.SanctionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.AllCount))
	return i, nil
}

func (m *CleanHistoryRecordByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanHistoryRecordByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.IdList) > 0 {
		for _, num := range m.IdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetType))
	return i, nil
}

func (m *CleanHistoryRecordByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanHistoryRecordByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *AddChannelWhiteListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelWhiteListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *AddChannelWhiteListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelWhiteListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *DelChannelFromWhiteListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelChannelFromWhiteListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *DelChannelFromWhiteListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelChannelFromWhiteListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelWhiteListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelWhiteListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelWhiteListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelWhiteListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetUserReportHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserReportHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.PageIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.PageLimit))
	return i, nil
}

func (m *GetUserReportHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserReportHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserReportList) > 0 {
		for _, msg := range m.UserReportList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserReportInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserReportInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.TargetType))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.ReportReason)))
	i += copy(dAtA[i:], m.ReportReason)
	dAtA[i] = 0x20
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ReportTs))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.SanctionOpUser)))
	i += copy(dAtA[i:], m.SanctionOpUser)
	dAtA[i] = 0x30
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.OperTs))
	return i, nil
}

func (m *UserReportList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserReportList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UserReportList) > 0 {
		for _, msg := range m.UserReportList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MaliciousReportInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaliciousReportInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.SanctionType))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.SanctionOpUser)))
	i += copy(dAtA[i:], m.SanctionOpUser)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BannedType))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BannedSecond))
	return i, nil
}

func (m *MaliciousReportInfoList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MaliciousReportInfoList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, msg := range m.InfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *SendCommonReportReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendCommonReportReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.ReportEventBin != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGreenbaba(dAtA, i, uint64(len(m.ReportEventBin)))
		i += copy(dAtA[i:], m.ReportEventBin)
	}
	return i, nil
}

func (m *SendCommonReportResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SendCommonReportResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckUserIsImLimitReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsImLimitReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckUserIsImLimitResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsImLimitResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x10
	i++
	if m.IsLimit {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *MuteChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MuteChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	if len(m.TargetUids) > 0 {
		for _, num := range m.TargetUids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MuteChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MuteChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	if len(m.MutedUids) > 0 {
		for _, num := range m.MutedUids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UnmuteChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnmuteChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	if len(m.TargetUids) > 0 {
		for _, num := range m.TargetUids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *UnmuteChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UnmuteChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelMuteListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMuteListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *ChannelMemberBaseInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMemberBaseInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Ts))
	return i, nil
}

func (m *GetChannelMuteListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMuteListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	if len(m.MuteList) > 0 {
		for _, msg := range m.MuteList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckUserIsMuteReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsMuteReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *CheckUserIsMuteResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserIsMuteResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.IsMute {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CleanChannelMuteListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanChannelMuteListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *CleanChannelMuteListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanChannelMuteListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckUserKickoutFromChannelReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserKickoutFromChannelReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckUserKickoutFromChannelResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserKickoutFromChannelResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsKicked {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *KickoutChannelMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KickoutChannelMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	if len(m.TargetUids) > 0 {
		for _, num := range m.TargetUids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.BanDuration))
	return i, nil
}

func (m *KickoutChannelMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *KickoutChannelMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGreenbaba(dAtA, i, uint64(m.ChannelId))
	if len(m.KickoutedUids) > 0 {
		for _, num := range m.KickoutedUids {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGreenbaba(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func encodeFixed64Greenbaba(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Greenbaba(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGreenbaba(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *GreenBabaSanctionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.TargetType))
	n += 1 + sovGreenbaba(uint64(m.Id))
	n += 1 + sovGreenbaba(uint64(m.BannedType))
	n += 1 + sovGreenbaba(uint64(m.RemainSecond))
	n += 1 + sovGreenbaba(uint64(m.SanctionType))
	l = len(m.SanctionReason)
	n += 1 + l + sovGreenbaba(uint64(l))
	return n
}

func (m *SetSanctionReq) Size() (n int) {
	var l int
	_ = l
	if len(m.SanctionList) > 0 {
		for _, e := range m.SanctionList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.OpUid))
	l = len(m.OpUserinfo)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.SanctionObj))
	return n
}

func (m *SetSanctionResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetCurrBannedStatByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.SanctionTargetType))
	n += 1 + sovGreenbaba(uint64(m.ReqId))
	return n
}

func (m *GetCurrBannedStatByIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.BannedList) > 0 {
		for _, e := range m.BannedList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *BatchGetCurrBannedStatByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.SanctionTargetType))
	if len(m.ReqIdList) > 0 {
		for _, e := range m.ReqIdList {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	return n
}

func (m *CurrBannedStatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Id))
	n += 1 + sovGreenbaba(uint64(m.SanctionTargetType))
	if len(m.BannedList) > 0 {
		for _, e := range m.BannedList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *BatchGetCurrBannedStatByIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CurrbannedList) > 0 {
		for _, e := range m.CurrbannedList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *GetBannedListByTargetTypeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.SanctionTargetType))
	n += 1 + sovGreenbaba(uint64(m.BeginIdx))
	n += 1 + sovGreenbaba(uint64(m.Limit))
	return n
}

func (m *GetBannedListByTargetTypeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.BannedList) > 0 {
		for _, e := range m.BannedList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.TotalSize))
	return n
}

func (m *SendReportReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.OpTtAcc)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.TargetChannel))
	n += 1 + sovGreenbaba(uint64(m.TargetUid))
	n += 1 + sovGreenbaba(uint64(m.ReportType))
	l = len(m.ReportReason)
	n += 1 + l + sovGreenbaba(uint64(l))
	l = len(m.PicUrl)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.ReportUid))
	return n
}

func (m *SendReportResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BaseReportInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.OpTtAcc)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.ReportType))
	l = len(m.ReportReason)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.ReportUid))
	l = len(m.PicUrl)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.ReportTs))
	n += 1 + sovGreenbaba(uint64(m.TargetUid))
	return n
}

func (m *ComplexSanctionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.TargetId))
	if len(m.ReportBaseList) > 0 {
		for _, e := range m.ReportBaseList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.ReprotRealCnt))
	n += 1 + sovGreenbaba(uint64(m.SanctionOpType))
	n += 1 + sovGreenbaba(uint64(m.BannedOpType))
	n += 1 + sovGreenbaba(uint64(m.BannedSecond))
	n += 1 + sovGreenbaba(uint64(m.LastUpdateTs))
	l = len(m.SanctionOpUser)
	n += 1 + l + sovGreenbaba(uint64(l))
	l = len(m.SanctionReason)
	n += 1 + l + sovGreenbaba(uint64(l))
	return n
}

func (m *ScanAllReportPorcHistoryListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.TargetType))
	n += 2
	n += 1 + sovGreenbaba(uint64(m.PageId))
	n += 1 + sovGreenbaba(uint64(m.PageCnt))
	n += 1 + sovGreenbaba(uint64(m.BeginTs))
	n += 1 + sovGreenbaba(uint64(m.EndTs))
	return n
}

func (m *ScanAllReportPorcHistoryListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SanctionList) > 0 {
		for _, e := range m.SanctionList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.AllCount))
	return n
}

func (m *GetSanctionStatByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.TargetType))
	n += 1 + sovGreenbaba(uint64(m.ReqId))
	return n
}

func (m *GetSanctionStatByIdResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.CurrBannedType))
	n += 1 + sovGreenbaba(uint64(m.RemainBannedSecond))
	n += 1 + sovGreenbaba(uint64(m.ReportStatCnt))
	n += 1 + sovGreenbaba(uint64(m.BannedStatCnt))
	n += 1 + sovGreenbaba(uint64(m.WarningStatCnt))
	n += 1 + sovGreenbaba(uint64(m.FalseReportCnt))
	n += 1 + sovGreenbaba(uint64(m.ChannelStatCnt))
	n += 1 + sovGreenbaba(uint64(m.UserStatCnt))
	n += 1 + sovGreenbaba(uint64(m.DisChannelStatCnt))
	n += 1 + sovGreenbaba(uint64(m.DisUserStatCnt))
	return n
}

func (m *ScanSanctionHistoryListByIdReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.TargetType))
	n += 1 + sovGreenbaba(uint64(m.ReqId))
	n += 2
	n += 1 + sovGreenbaba(uint64(m.PageId))
	n += 1 + sovGreenbaba(uint64(m.PageCnt))
	return n
}

func (m *ScanSanctionHistoryListByIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SanctionList) > 0 {
		for _, e := range m.SanctionList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.AllCount))
	return n
}

func (m *CleanHistoryRecordByIdReq) Size() (n int) {
	var l int
	_ = l
	if len(m.IdList) > 0 {
		for _, e := range m.IdList {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.TargetType))
	return n
}

func (m *CleanHistoryRecordByIdResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *AddChannelWhiteListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	return n
}

func (m *AddChannelWhiteListResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *DelChannelFromWhiteListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	return n
}

func (m *DelChannelFromWhiteListResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelWhiteListReq) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelWhiteListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	return n
}

func (m *GetUserReportHistoryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Uid))
	n += 1 + sovGreenbaba(uint64(m.PageIdx))
	n += 1 + sovGreenbaba(uint64(m.PageLimit))
	return n
}

func (m *GetUserReportHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UserReportList) > 0 {
		for _, e := range m.UserReportList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *UserReportInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.TargetId))
	n += 1 + sovGreenbaba(uint64(m.TargetType))
	l = len(m.ReportReason)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.ReportTs))
	l = len(m.SanctionOpUser)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.OperTs))
	return n
}

func (m *UserReportList) Size() (n int) {
	var l int
	_ = l
	if len(m.UserReportList) > 0 {
		for _, e := range m.UserReportList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *MaliciousReportInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Uid))
	n += 1 + sovGreenbaba(uint64(m.Type))
	n += 1 + sovGreenbaba(uint64(m.SanctionType))
	l = len(m.SanctionOpUser)
	n += 1 + l + sovGreenbaba(uint64(l))
	n += 1 + sovGreenbaba(uint64(m.BannedType))
	n += 1 + sovGreenbaba(uint64(m.BannedSecond))
	return n
}

func (m *MaliciousReportInfoList) Size() (n int) {
	var l int
	_ = l
	if len(m.InfoList) > 0 {
		for _, e := range m.InfoList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *SendCommonReportReq) Size() (n int) {
	var l int
	_ = l
	if m.ReportEventBin != nil {
		l = len(m.ReportEventBin)
		n += 1 + l + sovGreenbaba(uint64(l))
	}
	return n
}

func (m *SendCommonReportResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckUserIsImLimitReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Uid))
	return n
}

func (m *CheckUserIsImLimitResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *MuteChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	if len(m.TargetUids) > 0 {
		for _, e := range m.TargetUids {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	return n
}

func (m *MuteChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	if len(m.MutedUids) > 0 {
		for _, e := range m.MutedUids {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	return n
}

func (m *UnmuteChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	if len(m.TargetUids) > 0 {
		for _, e := range m.TargetUids {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	return n
}

func (m *UnmuteChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelMuteListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	n += 1 + sovGreenbaba(uint64(m.OpUid))
	return n
}

func (m *ChannelMemberBaseInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Uid))
	n += 1 + sovGreenbaba(uint64(m.Ts))
	return n
}

func (m *GetChannelMuteListResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	if len(m.MuteList) > 0 {
		for _, e := range m.MuteList {
			l = e.Size()
			n += 1 + l + sovGreenbaba(uint64(l))
		}
	}
	return n
}

func (m *CheckUserIsMuteReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Uid))
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	return n
}

func (m *CheckUserIsMuteResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.Uid))
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *CleanChannelMuteListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	return n
}

func (m *CleanChannelMuteListResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckUserKickoutFromChannelReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	n += 1 + sovGreenbaba(uint64(m.Uid))
	return n
}

func (m *CheckUserKickoutFromChannelResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *KickoutChannelMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	if len(m.TargetUids) > 0 {
		for _, e := range m.TargetUids {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	n += 1 + sovGreenbaba(uint64(m.BanDuration))
	return n
}

func (m *KickoutChannelMemberResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGreenbaba(uint64(m.ChannelId))
	if len(m.KickoutedUids) > 0 {
		for _, e := range m.KickoutedUids {
			n += 1 + sovGreenbaba(uint64(e))
		}
	}
	return n
}

func sovGreenbaba(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGreenbaba(x uint64) (n int) {
	return sovGreenbaba(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *GreenBabaSanctionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GreenBabaSanctionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GreenBabaSanctionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedType", wireType)
			}
			m.BannedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainSecond", wireType)
			}
			m.RemainSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionType", wireType)
			}
			m.SanctionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetSanctionReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetSanctionReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetSanctionReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionList = append(m.SanctionList, &GreenBabaSanctionInfo{})
			if err := m.SanctionList[len(m.SanctionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUserinfo", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpUserinfo = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionObj", wireType)
			}
			m.SanctionObj = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionObj |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetSanctionResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetSanctionResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetSanctionResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCurrBannedStatByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCurrBannedStatByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCurrBannedStatByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionTargetType", wireType)
			}
			m.SanctionTargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionTargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqId", wireType)
			}
			m.ReqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sanction_target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCurrBannedStatByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCurrBannedStatByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCurrBannedStatByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannedList = append(m.BannedList, &GreenBabaSanctionInfo{})
			if err := m.BannedList[len(m.BannedList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetCurrBannedStatByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetCurrBannedStatByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetCurrBannedStatByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionTargetType", wireType)
			}
			m.SanctionTargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionTargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ReqIdList = append(m.ReqIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ReqIdList = append(m.ReqIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sanction_target_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CurrBannedStatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CurrBannedStatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CurrBannedStatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionTargetType", wireType)
			}
			m.SanctionTargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionTargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannedList = append(m.BannedList, &GreenBabaSanctionInfo{})
			if err := m.BannedList[len(m.BannedList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sanction_target_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetCurrBannedStatByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetCurrBannedStatByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetCurrBannedStatByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrbannedList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CurrbannedList = append(m.CurrbannedList, &CurrBannedStatInfo{})
			if err := m.CurrbannedList[len(m.CurrbannedList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedListByTargetTypeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedListByTargetTypeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedListByTargetTypeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionTargetType", wireType)
			}
			m.SanctionTargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionTargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginIdx", wireType)
			}
			m.BeginIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sanction_target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("begin_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetBannedListByTargetTypeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetBannedListByTargetTypeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetBannedListByTargetTypeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BannedList = append(m.BannedList, &GreenBabaSanctionInfo{})
			if err := m.BannedList[len(m.BannedList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalSize", wireType)
			}
			m.TotalSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalSize |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("total_size")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendReportReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendReportReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendReportReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTtAcc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpTtAcc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetChannel", wireType)
			}
			m.TargetChannel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetChannel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportType", wireType)
			}
			m.ReportType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportUid", wireType)
			}
			m.ReportUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("op_tt_acc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendReportResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendReportResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendReportResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BaseReportInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BaseReportInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BaseReportInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpTtAcc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpTtAcc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportType", wireType)
			}
			m.ReportType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportUid", wireType)
			}
			m.ReportUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PicUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PicUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportTs", wireType)
			}
			m.ReportTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUid", wireType)
			}
			m.TargetUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("op_tt_acc")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ComplexSanctionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ComplexSanctionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ComplexSanctionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetId", wireType)
			}
			m.TargetId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportBaseList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportBaseList = append(m.ReportBaseList, &BaseReportInfo{})
			if err := m.ReportBaseList[len(m.ReportBaseList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReprotRealCnt", wireType)
			}
			m.ReprotRealCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReprotRealCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionOpType", wireType)
			}
			m.SanctionOpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionOpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedOpType", wireType)
			}
			m.BannedOpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedOpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedSecond", wireType)
			}
			m.BannedSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTs", wireType)
			}
			m.LastUpdateTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionOpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionOpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanAllReportPorcHistoryListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ScanAllReportPorcHistoryListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ScanAllReportPorcHistoryListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsScanWaitproc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsScanWaitproc = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageId", wireType)
			}
			m.PageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageCnt", wireType)
			}
			m.PageCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginTs", wireType)
			}
			m.BeginTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTs", wireType)
			}
			m.EndTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_scan_waitproc")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("page_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("page_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanAllReportPorcHistoryListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ScanAllReportPorcHistoryListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ScanAllReportPorcHistoryListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionList = append(m.SanctionList, &ComplexSanctionInfo{})
			if err := m.SanctionList[len(m.SanctionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllCount", wireType)
			}
			m.AllCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSanctionStatByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSanctionStatByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSanctionStatByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqId", wireType)
			}
			m.ReqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSanctionStatByIdResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSanctionStatByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSanctionStatByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrBannedType", wireType)
			}
			m.CurrBannedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrBannedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemainBannedSecond", wireType)
			}
			m.RemainBannedSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RemainBannedSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportStatCnt", wireType)
			}
			m.ReportStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedStatCnt", wireType)
			}
			m.BannedStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WarningStatCnt", wireType)
			}
			m.WarningStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WarningStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FalseReportCnt", wireType)
			}
			m.FalseReportCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FalseReportCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelStatCnt", wireType)
			}
			m.ChannelStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserStatCnt", wireType)
			}
			m.UserStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UserStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisChannelStatCnt", wireType)
			}
			m.DisChannelStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisChannelStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DisUserStatCnt", wireType)
			}
			m.DisUserStatCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DisUserStatCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("curr_banned_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanSanctionHistoryListByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ScanSanctionHistoryListByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ScanSanctionHistoryListByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReqId", wireType)
			}
			m.ReqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsScanWaitproc", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsScanWaitproc = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageId", wireType)
			}
			m.PageId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageCnt", wireType)
			}
			m.PageCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("req_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_scan_waitproc")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("page_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("page_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ScanSanctionHistoryListByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ScanSanctionHistoryListByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ScanSanctionHistoryListByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionList = append(m.SanctionList, &ComplexSanctionInfo{})
			if err := m.SanctionList[len(m.SanctionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AllCount", wireType)
			}
			m.AllCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AllCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanHistoryRecordByIdReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanHistoryRecordByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanHistoryRecordByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.IdList = append(m.IdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.IdList = append(m.IdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field IdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanHistoryRecordByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanHistoryRecordByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanHistoryRecordByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelWhiteListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelWhiteListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelWhiteListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelWhiteListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelWhiteListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelWhiteListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelChannelFromWhiteListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelChannelFromWhiteListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelChannelFromWhiteListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelChannelFromWhiteListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelChannelFromWhiteListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelChannelFromWhiteListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelWhiteListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelWhiteListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelWhiteListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelWhiteListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelWhiteListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelWhiteListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserReportHistoryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserReportHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserReportHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageIdx", wireType)
			}
			m.PageIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PageLimit", wireType)
			}
			m.PageLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PageLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("page_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("page_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserReportHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserReportHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserReportHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserReportList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserReportList = append(m.UserReportList, &UserReportInfo{})
			if err := m.UserReportList[len(m.UserReportList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserReportInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserReportInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserReportInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetId", wireType)
			}
			m.TargetId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetType", wireType)
			}
			m.TargetType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportReason", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportReason = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportTs", wireType)
			}
			m.ReportTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ReportTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionOpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionOpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OperTs", wireType)
			}
			m.OperTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OperTs |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_type")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("report_reason")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("report_ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserReportList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserReportList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserReportList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UserReportList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UserReportList = append(m.UserReportList, &UserReportInfo{})
			if err := m.UserReportList[len(m.UserReportList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaliciousReportInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MaliciousReportInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MaliciousReportInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionType", wireType)
			}
			m.SanctionType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SanctionType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SanctionOpUser", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SanctionOpUser = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedType", wireType)
			}
			m.BannedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BannedSecond", wireType)
			}
			m.BannedSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BannedSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MaliciousReportInfoList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MaliciousReportInfoList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MaliciousReportInfoList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field InfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.InfoList = append(m.InfoList, &MaliciousReportInfo{})
			if err := m.InfoList[len(m.InfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendCommonReportReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendCommonReportReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendCommonReportReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportEventBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportEventBin = append(m.ReportEventBin[:0], dAtA[iNdEx:postIndex]...)
			if m.ReportEventBin == nil {
				m.ReportEventBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("report_event_bin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SendCommonReportResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SendCommonReportResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SendCommonReportResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsImLimitReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsImLimitReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsImLimitReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsImLimitResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsImLimitResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsImLimitResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLimit", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsLimit = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MuteChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MuteChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MuteChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUids = append(m.TargetUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUids = append(m.TargetUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MuteChannelMemberResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MuteChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MuteChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MutedUids = append(m.MutedUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MutedUids = append(m.MutedUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MutedUids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnmuteChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnmuteChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnmuteChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUids = append(m.TargetUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUids = append(m.TargetUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UnmuteChannelMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UnmuteChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UnmuteChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMuteListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMuteListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMuteListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("op_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMemberBaseInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMemberBaseInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMemberBaseInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ts", wireType)
			}
			m.Ts = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ts |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("ts")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMuteListResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMuteListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMuteListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MuteList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGreenbaba
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MuteList = append(m.MuteList, &ChannelMemberBaseInfo{})
			if err := m.MuteList[len(m.MuteList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsMuteReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsMuteReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsMuteReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserIsMuteResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserIsMuteResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserIsMuteResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMute", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMute = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_mute")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanChannelMuteListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanChannelMuteListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanChannelMuteListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanChannelMuteListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanChannelMuteListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanChannelMuteListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserKickoutFromChannelReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserKickoutFromChannelReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserKickoutFromChannelReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserKickoutFromChannelResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserKickoutFromChannelResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserKickoutFromChannelResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsKicked", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsKicked = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *KickoutChannelMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: KickoutChannelMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: KickoutChannelMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUids = append(m.TargetUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUids = append(m.TargetUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUids", wireType)
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BanDuration", wireType)
			}
			m.BanDuration = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BanDuration |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *KickoutChannelMemberResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: KickoutChannelMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: KickoutChannelMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.KickoutedUids = append(m.KickoutedUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGreenbaba
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGreenbaba
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.KickoutedUids = append(m.KickoutedUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field KickoutedUids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGreenbaba(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGreenbaba
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGreenbaba(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGreenbaba
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGreenbaba
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGreenbaba
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGreenbaba
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGreenbaba(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGreenbaba = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGreenbaba   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/greenBaba/greenbaba.proto", fileDescriptorGreenbaba) }

var fileDescriptorGreenbaba = []byte{
	// 3048 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x5a, 0xcd, 0x6f, 0x1b, 0xd7,
	0xb5, 0xf7, 0x90, 0xfa, 0xe2, 0x91, 0x45, 0xd1, 0x57, 0x5f, 0xd4, 0xc8, 0x92, 0x46, 0xe3, 0x7c,
	0xc8, 0x8a, 0x25, 0xbf, 0xe7, 0xe0, 0x05, 0x08, 0xa3, 0xe8, 0x81, 0xa2, 0x18, 0x99, 0xb0, 0x4c,
	0xea, 0x91, 0x54, 0x0c, 0xbf, 0xa4, 0x19, 0x0c, 0x39, 0x63, 0x7b, 0x2c, 0x72, 0x66, 0x3c, 0x77,
	0x98, 0xc8, 0x69, 0x8b, 0xa6, 0xdd, 0x34, 0xcd, 0xa2, 0x08, 0x02, 0x74, 0x91, 0x55, 0x37, 0x6e,
	0x81, 0x02, 0x5d, 0x75, 0xd5, 0x4d, 0x03, 0x14, 0xe8, 0x22, 0xcb, 0x2e, 0x5a, 0xa0, 0xab, 0xa2,
	0x48, 0x37, 0xfe, 0x03, 0x8a, 0x76, 0x99, 0xe2, 0xde, 0x99, 0xe1, 0xdc, 0x19, 0xde, 0xa1, 0x98,
	0xc4, 0xe9, 0x4e, 0x3a, 0xe7, 0xdc, 0x7b, 0xce, 0xf9, 0x9d, 0x73, 0xcf, 0x3d, 0xf7, 0x0c, 0x61,
	0x15, 0x3b, 0xed, 0xeb, 0xf7, 0x1d, 0x5d, 0x37, 0xf7, 0xd5, 0x96, 0xea, 0xfd, 0xd5, 0x52, 0x5b,
	0xea, 0x8e, 0xed, 0x58, 0xae, 0x85, 0x32, 0x7d, 0x96, 0xf8, 0x5c, 0xdb, 0xea, 0x76, 0x2d, 0xf3,
	0xba, 0xdb, 0x79, 0xd7, 0x36, 0xda, 0xa7, 0x1d, 0xfd, 0x3a, 0x3e, 0x6d, 0xf5, 0x8c, 0x8e, 0x6b,
	0x98, 0xee, 0x63, 0x5b, 0xf7, 0x16, 0xc8, 0x1f, 0xa4, 0x60, 0xe1, 0x30, 0x58, 0xd3, 0x50, 0xcd,
	0xb6, 0x6b, 0x58, 0x66, 0xc5, 0xbc, 0x67, 0xa1, 0xe7, 0x61, 0xda, 0x55, 0x9d, 0xfb, 0xba, 0xab,
	0x10, 0xf1, 0xbc, 0x20, 0xa5, 0x36, 0x67, 0xf6, 0xc7, 0x3e, 0xff, 0xeb, 0xfa, 0x85, 0x3a, 0x78,
	0x8c, 0xe6, 0x63, 0x5b, 0x47, 0xf3, 0x90, 0x32, 0xb4, 0x7c, 0x8a, 0xe1, 0xa6, 0x0c, 0x8d, 0x2c,
	0x6e, 0xa9, 0xa6, 0xa9, 0x6b, 0xde, 0xe2, 0xb4, 0x24, 0x84, 0x8b, 0x3d, 0x06, 0x5d, 0x7c, 0x15,
	0x66, 0x1c, 0xbd, 0xab, 0x1a, 0xa6, 0x82, 0xf5, 0xb6, 0x65, 0x6a, 0xf9, 0x31, 0x46, 0xf0, 0xa2,
	0xc7, 0x6a, 0x50, 0x0e, 0x11, 0xc5, 0xbe, 0x79, 0xde, 0x9e, 0xe3, 0xac, 0x68, 0xc0, 0xa2, 0xbb,
	0x6e, 0xc3, 0x6c, 0x5f, 0xd4, 0xd1, 0x55, 0x6c, 0x99, 0xf9, 0x09, 0x49, 0xd8, 0xcc, 0xf8, 0xc2,
	0xd9, 0x80, 0x59, 0xa7, 0x3c, 0xf9, 0x0f, 0x02, 0x64, 0x1b, 0xba, 0xdb, 0xe8, 0x53, 0x1f, 0xa1,
	0x32, 0xa3, 0xac, 0x63, 0x60, 0x37, 0x2f, 0x48, 0xe9, 0xcd, 0xe9, 0x1b, 0xd2, 0x4e, 0x1f, 0xde,
	0x1d, 0x2e, 0x68, 0xa1, 0x21, 0x47, 0x06, 0x76, 0xd1, 0x0a, 0x4c, 0x58, 0xb6, 0xd2, 0xa3, 0xf8,
	0x84, 0xc6, 0x8e, 0x5b, 0xf6, 0x89, 0x07, 0x11, 0x61, 0x62, 0xdd, 0x31, 0xcc, 0x7b, 0x16, 0x85,
	0x28, 0xb0, 0x10, 0x2c, 0xfb, 0xc4, 0xa7, 0xa3, 0x17, 0xa1, 0xbf, 0xa7, 0x62, 0xb5, 0x1e, 0x46,
	0x10, 0x9a, 0x0e, 0x38, 0xb5, 0xd6, 0x43, 0xf9, 0x12, 0xcc, 0x46, 0xbc, 0xc0, 0xb6, 0x6c, 0x41,
	0xfe, 0x50, 0x77, 0x4b, 0x3d, 0xc7, 0xd9, 0xa7, 0x98, 0x37, 0x5c, 0xd5, 0xdd, 0x7f, 0x5c, 0xd1,
	0x88, 0x8b, 0xaf, 0xc0, 0x7c, 0x88, 0x67, 0x42, 0x9c, 0x51, 0x1f, 0xd6, 0x30, 0xde, 0x2b, 0x30,
	0xe1, 0xe8, 0x8f, 0x94, 0x58, 0xcc, 0xc7, 0x1d, 0xfd, 0x51, 0x45, 0x93, 0xdf, 0x81, 0xe5, 0x04,
	0x85, 0xd8, 0x46, 0xc5, 0x7e, 0x4e, 0x7c, 0x25, 0x48, 0xfd, 0x7c, 0x21, 0x80, 0xca, 0xef, 0xc1,
	0xea, 0xbe, 0xea, 0xb6, 0x1f, 0x3c, 0x73, 0xaf, 0xd6, 0x60, 0xda, 0xf3, 0xca, 0xb3, 0x2d, 0x25,
	0xa5, 0x37, 0x67, 0xea, 0x19, 0xea, 0x14, 0x55, 0xfc, 0x0b, 0x01, 0x50, 0x54, 0x23, 0x3d, 0x23,
	0x5e, 0xf2, 0x0b, 0xb1, 0xe4, 0x4f, 0x32, 0x22, 0x75, 0x8e, 0x11, 0x31, 0x80, 0xd2, 0x5f, 0x03,
	0xa0, 0x07, 0xb0, 0x36, 0x0c, 0x20, 0x6c, 0xa3, 0x37, 0x60, 0xb6, 0xdd, 0x73, 0x9c, 0xc1, 0x48,
	0xac, 0x32, 0x8a, 0x06, 0x5d, 0xad, 0x67, 0xc3, 0x55, 0x54, 0xd3, 0xcf, 0x04, 0xb8, 0x7c, 0xa8,
	0xbb, 0xfb, 0x7d, 0xca, 0xfe, 0xe3, 0xd0, 0x95, 0x6f, 0x12, 0x8a, 0x0d, 0xc8, 0xb4, 0xf4, 0xfb,
	0x86, 0xa9, 0x18, 0xda, 0x59, 0x04, 0xb2, 0x29, 0x4a, 0xae, 0x68, 0x67, 0x48, 0x84, 0xf1, 0x8e,
	0xd1, 0x35, 0x08, 0x44, 0x4c, 0x0a, 0x52, 0x92, 0xfc, 0x63, 0x01, 0x56, 0x87, 0xd8, 0xf5, 0x4c,
	0xf2, 0x10, 0x5d, 0x01, 0x70, 0x2d, 0x57, 0xed, 0x28, 0xd8, 0x78, 0x3f, 0x1a, 0xd7, 0x0c, 0xa5,
	0x37, 0x8c, 0xf7, 0x75, 0xf9, 0xd3, 0x14, 0xcc, 0x34, 0x74, 0x53, 0xab, 0xeb, 0xb6, 0xe5, 0xb8,
	0x04, 0x12, 0x09, 0x32, 0x96, 0xad, 0xb8, 0xae, 0xa2, 0xb6, 0xdb, 0x14, 0x87, 0xe0, 0xc0, 0x4f,
	0x5a, 0x76, 0xd3, 0x2d, 0xb6, 0xdb, 0xe8, 0x25, 0xc8, 0xfa, 0x58, 0xb5, 0x1f, 0x10, 0x75, 0x9d,
	0x48, 0xe5, 0x98, 0xf1, 0x78, 0x25, 0x8f, 0x45, 0xad, 0xf0, 0x84, 0x49, 0x89, 0x61, 0x6b, 0x6c,
	0xc6, 0xa3, 0xfb, 0x65, 0xc6, 0xa1, 0x06, 0x78, 0xe8, 0xb3, 0xe5, 0x03, 0x3c, 0x46, 0x58, 0x89,
	0xa9, 0x98, 0x5f, 0x31, 0xc7, 0x99, 0x7a, 0x74, 0xd1, 0xf1, 0x5d, 0x20, 0x1c, 0xb4, 0x0a, 0x93,
	0xb6, 0xd1, 0x56, 0x7a, 0x4e, 0x27, 0x52, 0x56, 0x27, 0x6c, 0xa3, 0x7d, 0xe2, 0x50, 0xab, 0xfc,
	0x9d, 0x88, 0x55, 0x93, 0xac, 0x55, 0x1e, 0xfd, 0xc4, 0xd0, 0xe4, 0x1c, 0x29, 0xb9, 0x21, 0x34,
	0xd8, 0x96, 0x3f, 0x4e, 0x41, 0x76, 0x5f, 0xc5, 0xba, 0x47, 0xa2, 0xa7, 0xeb, 0x7c, 0xb8, 0x62,
	0xce, 0xa5, 0x46, 0x75, 0x2e, 0x9d, 0xe8, 0x5c, 0xd4, 0xfa, 0x31, 0xae, 0xf5, 0x2c, 0x02, 0xe3,
	0x1c, 0x04, 0x36, 0x20, 0x13, 0x58, 0x85, 0x29, 0x44, 0xfd, 0x0c, 0xf6, 0x6d, 0xc2, 0xb1, 0xd0,
	0x4d, 0x72, 0x43, 0x27, 0xff, 0x2e, 0x0d, 0x73, 0x25, 0xab, 0x6b, 0x77, 0xf4, 0xb3, 0xc8, 0xcd,
	0xbc, 0x01, 0xbe, 0x90, 0x12, 0x2b, 0x3e, 0x53, 0x1e, 0xb9, 0xa2, 0xa1, 0x12, 0xe4, 0x7c, 0x13,
	0x5a, 0x2a, 0xd6, 0xc3, 0xa2, 0x36, 0x7d, 0x63, 0x99, 0x49, 0xf4, 0x28, 0xde, 0xf5, 0xac, 0xb7,
	0x84, 0x50, 0x69, 0x96, 0x5f, 0x83, 0x59, 0x47, 0x27, 0x6d, 0x02, 0x81, 0xad, 0xa3, 0xb4, 0x4d,
	0x37, 0x92, 0x64, 0x33, 0x1e, 0xb3, 0xae, 0xab, 0x9d, 0x92, 0xe9, 0xa2, 0x1d, 0xc8, 0x85, 0x17,
	0x95, 0x3d, 0x98, 0x6d, 0xfd, 0x6b, 0xb7, 0x66, 0xd3, 0xa0, 0x6c, 0x41, 0xd6, 0x3f, 0x86, 0x81,
	0x74, 0xe4, 0x46, 0xf7, 0x78, 0xbe, 0xec, 0x55, 0x98, 0xf1, 0x65, 0xfd, 0x3e, 0x61, 0x62, 0x50,
	0xd4, 0xef, 0x13, 0xb6, 0x20, 0xdb, 0x51, 0xb1, 0xab, 0xf4, 0x6c, 0x4d, 0x75, 0x75, 0x12, 0x01,
	0x16, 0xdd, 0x8b, 0x84, 0x77, 0x42, 0x59, 0x4d, 0x1c, 0x37, 0x99, 0xdc, 0xc5, 0xf9, 0x29, 0x5e,
	0xa7, 0x50, 0xa3, 0xf7, 0x31, 0xaf, 0xb1, 0xc8, 0x0c, 0x69, 0x2c, 0xfe, 0x29, 0xc0, 0x7a, 0xa3,
	0xad, 0x9a, 0xc5, 0x4e, 0xc7, 0x43, 0xf9, 0xd8, 0x72, 0xda, 0x37, 0x0d, 0xec, 0x5a, 0xce, 0x63,
	0x02, 0x30, 0x29, 0x09, 0x23, 0x76, 0x59, 0x3b, 0x90, 0x33, 0xb0, 0x82, 0xdb, 0xaa, 0xa9, 0xbc,
	0xa7, 0x1a, 0xae, 0xed, 0x58, 0x6d, 0x5a, 0x76, 0xa6, 0x02, 0xd5, 0x06, 0x26, 0x9a, 0xee, 0xf8,
	0x3c, 0x9a, 0xa1, 0xea, 0x7d, 0x5d, 0xa1, 0x75, 0x21, 0xdc, 0x72, 0x82, 0x10, 0x2b, 0x1a, 0x5a,
	0x87, 0x29, 0xca, 0x26, 0x21, 0x1d, 0x63, 0xf8, 0x74, 0x11, 0x09, 0xe6, 0x3a, 0x78, 0xd5, 0x96,
	0xe0, 0xc7, 0x86, 0x65, 0x92, 0x52, 0x9b, 0x98, 0xb4, 0x01, 0xba, 0xa9, 0xc5, 0x13, 0x7c, 0x5c,
	0x37, 0xb5, 0x26, 0x96, 0x3f, 0x12, 0x40, 0x1a, 0xee, 0x38, 0xb6, 0x51, 0x89, 0xdf, 0x63, 0xad,
	0xb1, 0xd7, 0xd0, 0x60, 0xf2, 0xc7, 0x3a, 0xac, 0x0d, 0xc8, 0xa8, 0x9d, 0x8e, 0xd2, 0xb6, 0x7a,
	0xa6, 0x1b, 0x39, 0xfe, 0x53, 0x6a, 0xa7, 0x53, 0x22, 0x54, 0xf9, 0x6d, 0x58, 0x3c, 0x0c, 0xfb,
	0x22, 0xb6, 0x59, 0x18, 0x11, 0xfb, 0xa1, 0x1d, 0xcf, 0x97, 0x69, 0x58, 0xe2, 0x6e, 0x8f, 0x6d,
	0x12, 0x34, 0x72, 0x69, 0x2a, 0x6c, 0x27, 0xcc, 0x2a, 0xa1, 0x57, 0xea, 0x7e, 0xd8, 0x0d, 0xbf,
	0x02, 0xf3, 0x7e, 0x37, 0x1c, 0x4d, 0x76, 0xd6, 0x2f, 0xe4, 0x49, 0xec, 0xb3, 0x29, 0xef, 0x9d,
	0x53, 0x72, 0xd8, 0xb1, 0xab, 0xba, 0xdc, 0x73, 0x6a, 0x39, 0x2e, 0x31, 0x8d, 0x84, 0xf6, 0x1a,
	0xcc, 0x06, 0xdb, 0x07, 0xd2, 0xec, 0x31, 0xf5, 0x0f, 0x5a, 0x20, 0xbd, 0x03, 0xb9, 0xf7, 0x54,
	0xc7, 0x34, 0xcc, 0xfb, 0xa1, 0x38, 0x9b, 0x10, 0x59, 0x9f, 0xcb, 0xc8, 0xdf, 0x53, 0x3b, 0x58,
	0x57, 0x7c, 0x8b, 0x88, 0x3c, 0x9b, 0x21, 0x59, 0xca, 0xf5, 0xb2, 0xc2, 0x97, 0xf7, 0x6f, 0xba,
	0x70, 0x7f, 0xf6, 0xc0, 0x66, 0x7d, 0x6e, 0xb0, 0xff, 0x26, 0xcc, 0x90, 0x63, 0x1a, 0x0a, 0x4f,
	0xb1, 0xfd, 0x30, 0x61, 0x05, 0x92, 0xff, 0x03, 0xf3, 0x9a, 0x81, 0x95, 0x81, 0xdd, 0x33, 0xcc,
	0x82, 0x4b, 0x9a, 0x81, 0x4b, 0x51, 0x05, 0xd7, 0x81, 0x10, 0x95, 0xa8, 0x12, 0x60, 0x2d, 0xd2,
	0x0c, 0x7c, 0x12, 0xea, 0x91, 0xff, 0x24, 0xc0, 0x1a, 0x49, 0xf6, 0x20, 0x05, 0x98, 0x3c, 0x7f,
	0x86, 0x89, 0xc6, 0xad, 0x00, 0xe9, 0xd1, 0x2a, 0xc0, 0xd8, 0x39, 0x15, 0x60, 0x9c, 0x53, 0x01,
	0xe4, 0x9f, 0xf8, 0xc5, 0x2b, 0xd1, 0xad, 0xff, 0xe0, 0x11, 0x7e, 0x0b, 0x96, 0x4b, 0x1d, 0x5d,
	0x0d, 0x6c, 0xa8, 0xeb, 0x6d, 0xcb, 0xd1, 0x02, 0x70, 0x97, 0x60, 0xd2, 0x60, 0x5a, 0xb9, 0x99,
	0xfa, 0x84, 0xe1, 0x35, 0x69, 0x31, 0xd4, 0x53, 0x7c, 0xd4, 0xe5, 0xcb, 0x20, 0x26, 0x6d, 0x8e,
	0x6d, 0xf9, 0x75, 0x58, 0x2c, 0x6a, 0x9a, 0x9f, 0x23, 0x77, 0x1e, 0x18, 0xae, 0x1e, 0x54, 0xee,
	0x2b, 0x00, 0x41, 0x6e, 0xc5, 0xae, 0xe1, 0x8c, 0x4f, 0xaf, 0x68, 0xf2, 0x32, 0x2c, 0x71, 0x97,
	0x63, 0x5b, 0x2e, 0x82, 0x78, 0xa0, 0x77, 0x7c, 0xd6, 0x1b, 0x8e, 0xd5, 0xfd, 0xea, 0xbb, 0xaf,
	0xc2, 0x4a, 0xe2, 0x16, 0xd8, 0x96, 0xf3, 0xb4, 0xf2, 0x71, 0x6c, 0x97, 0x8b, 0xb4, 0x68, 0xf1,
	0xcc, 0x42, 0x2f, 0xc0, 0x6c, 0xa8, 0x98, 0x85, 0x75, 0xa6, 0xaf, 0xd7, 0x7f, 0x8a, 0x91, 0x2d,
	0xc8, 0x41, 0xf0, 0x0e, 0x73, 0x1f, 0xbe, 0x47, 0x68, 0x11, 0xd2, 0xbd, 0x98, 0xd1, 0x84, 0xd0,
	0xcf, 0xb9, 0x78, 0x63, 0x3f, 0xe9, 0xe5, 0xe4, 0x19, 0x71, 0x9a, 0x0a, 0x0c, 0x36, 0xf7, 0x19,
	0x42, 0x3f, 0xa2, 0x0d, 0xbe, 0x42, 0x1f, 0xb5, 0x1c, 0xc5, 0x34, 0x21, 0x73, 0xf4, 0xe0, 0xfa,
	0xc5, 0x87, 0xc9, 0x49, 0xb6, 0xed, 0x09, 0xd7, 0x7a, 0x6d, 0x4f, 0xaf, 0xff, 0x3f, 0xf5, 0xec,
	0x5f, 0x02, 0x64, 0xa3, 0x22, 0xa3, 0x74, 0x5c, 0xa3, 0x65, 0x1b, 0xaf, 0x15, 0x4d, 0x25, 0xb4,
	0xa2, 0x91, 0x36, 0x92, 0x3d, 0xc3, 0x61, 0x1b, 0xc9, 0x6b, 0x60, 0xc6, 0x87, 0x34, 0x30, 0xab,
	0x30, 0x69, 0xd9, 0xba, 0x13, 0xbf, 0xb6, 0x27, 0x08, 0xb1, 0x89, 0xe5, 0x13, 0xd6, 0x71, 0x7a,
	0x86, 0x9e, 0x09, 0xa0, 0xff, 0x10, 0x60, 0xee, 0xb6, 0xda, 0x31, 0xda, 0x86, 0xd5, 0xc3, 0x0c,
	0xaa, 0x49, 0x79, 0x92, 0x87, 0xb1, 0x81, 0x76, 0x9e, 0x52, 0x06, 0x87, 0x40, 0xe9, 0xc4, 0x21,
	0x10, 0x0f, 0x9a, 0xb1, 0x21, 0xd0, 0xc4, 0x26, 0x56, 0xe3, 0xc9, 0x13, 0xab, 0x11, 0x3b, 0x51,
	0xf9, 0x4d, 0x58, 0xe2, 0x78, 0x4d, 0x61, 0x7d, 0x0d, 0x32, 0x86, 0x79, 0xcf, 0x4a, 0x2a, 0x9a,
	0x9c, 0x65, 0xf5, 0x29, 0xc3, 0x5f, 0x2c, 0x97, 0x61, 0x8e, 0xbc, 0x9d, 0x4a, 0x74, 0xbc, 0x17,
	0x3e, 0x2e, 0x77, 0xfa, 0x2d, 0xbf, 0xfe, 0xae, 0x6e, 0xba, 0x4a, 0xcb, 0x30, 0x29, 0xb4, 0x17,
	0x03, 0x87, 0x3d, 0x6e, 0x99, 0x30, 0xf7, 0x0d, 0x53, 0x5e, 0x84, 0xf9, 0xc1, 0x6d, 0xb0, 0x2d,
	0x5f, 0x87, 0x85, 0xd2, 0x03, 0xbd, 0x7d, 0x4a, 0x50, 0xa9, 0xe0, 0x4a, 0x97, 0x9e, 0xba, 0x21,
	0xc7, 0x5a, 0x7e, 0x15, 0x16, 0x79, 0x0b, 0xb0, 0x4d, 0x0e, 0xbc, 0x81, 0xfd, 0xd3, 0xcc, 0x76,
	0xab, 0x93, 0x06, 0xf6, 0xce, 0xf2, 0xdb, 0x30, 0x7f, 0xbb, 0xe7, 0xea, 0x7e, 0x21, 0xba, 0xad,
	0x77, 0x5b, 0x24, 0x71, 0x46, 0xab, 0x7e, 0x68, 0xbd, 0x7f, 0xe2, 0x7a, 0x86, 0x86, 0xfd, 0x99,
	0x0d, 0xf4, 0x9f, 0x4f, 0x58, 0x7e, 0x0b, 0x16, 0x38, 0xbb, 0x63, 0x7b, 0xb4, 0xed, 0x57, 0x01,
	0xba, 0x3d, 0x57, 0xd7, 0xd8, 0xdd, 0x33, 0x94, 0x42, 0x37, 0x7f, 0x07, 0x16, 0x4f, 0xcc, 0xee,
	0xb7, 0x67, 0xfc, 0x32, 0x2c, 0x71, 0xf7, 0xc7, 0xb6, 0x7c, 0x17, 0x16, 0xc2, 0xea, 0x4d, 0x3c,
	0xfc, 0x2a, 0x97, 0x46, 0x64, 0x28, 0x99, 0x8a, 0x0d, 0x25, 0xe5, 0x32, 0x09, 0x3e, 0xa3, 0x8f,
	0xbc, 0x05, 0x87, 0x9e, 0xd5, 0x79, 0x48, 0xb9, 0x38, 0x3a, 0xfe, 0x75, 0xb1, 0xfc, 0x3d, 0xf6,
	0xe6, 0x09, 0x2d, 0x1c, 0x15, 0xfa, 0xd7, 0x81, 0x02, 0xcd, 0x3e, 0x5b, 0xd9, 0xf9, 0x0c, 0xd7,
	0xc2, 0xfa, 0x54, 0xd7, 0xd7, 0x23, 0xff, 0x1f, 0x20, 0x26, 0x21, 0x89, 0xfa, 0x61, 0xb7, 0x52,
	0xd4, 0xa2, 0x14, 0xff, 0xa6, 0x7d, 0x04, 0x73, 0x03, 0x5b, 0x62, 0xfb, 0x1b, 0xed, 0x49, 0x8a,
	0xb1, 0x81, 0x15, 0x62, 0x75, 0xa4, 0x91, 0x9b, 0x30, 0xe8, 0xfe, 0xf2, 0x1e, 0x2c, 0xd1, 0xbe,
	0xe4, 0x6b, 0xc6, 0x59, 0x16, 0x21, 0xcf, 0x5f, 0x8f, 0x6d, 0xf9, 0x3b, 0xb0, 0xd6, 0x77, 0xe7,
	0x96, 0xd1, 0x3e, 0xb5, 0x7a, 0x2e, 0x69, 0x1f, 0x7c, 0xd1, 0x91, 0x53, 0xc9, 0x77, 0x3f, 0x15,
	0xaf, 0x08, 0x07, 0xb0, 0x3e, 0x74, 0x7b, 0x6c, 0x93, 0xcb, 0xcd, 0xc0, 0xca, 0xa9, 0xd1, 0x3e,
	0xd5, 0xc9, 0xf6, 0x42, 0xdf, 0xfd, 0x29, 0x03, 0xdf, 0xa2, 0x54, 0xf9, 0x43, 0x01, 0x96, 0xfc,
	0xd5, 0xdf, 0xce, 0x19, 0x43, 0x2f, 0x02, 0xa9, 0xd8, 0x8a, 0xd6, 0x73, 0x54, 0x72, 0x13, 0x44,
	0x6e, 0x13, 0x72, 0x1b, 0x1c, 0xf8, 0x0c, 0xf9, 0x1e, 0xe4, 0xf9, 0x96, 0x8c, 0x9a, 0xd1, 0xcf,
	0x43, 0xf6, 0xd4, 0xdb, 0x20, 0x5a, 0x50, 0x66, 0xfa, 0x54, 0x62, 0xd0, 0xd6, 0xaf, 0x05, 0xc8,
	0x95, 0xab, 0x27, 0xb7, 0x95, 0x66, 0xb1, 0x7e, 0x58, 0x6e, 0x2a, 0xcd, 0xbb, 0xc7, 0x65, 0xb4,
	0x0c, 0x0b, 0x65, 0x96, 0xa0, 0x94, 0x6e, 0x16, 0xab, 0xd5, 0xf2, 0x51, 0x4e, 0x40, 0x8b, 0x80,
	0xa2, 0xac, 0x93, 0x46, 0xb9, 0x9e, 0x4b, 0xa1, 0x25, 0x98, 0x8b, 0xd2, 0x6f, 0x9f, 0x34, 0x2a,
	0xa5, 0x5c, 0x7a, 0x90, 0x71, 0x58, 0xaf, 0x9d, 0x1c, 0xe7, 0xc6, 0x06, 0x77, 0x2a, 0xdd, 0x2c,
	0x36, 0x73, 0xe3, 0x68, 0x05, 0x96, 0xa2, 0xf4, 0x83, 0x7a, 0xf1, 0x8e, 0x72, 0x58, 0xbc, 0x5d,
	0xce, 0x4d, 0x6c, 0x7d, 0x22, 0xc0, 0x02, 0x35, 0xb7, 0x51, 0xac, 0x96, 0x9a, 0x95, 0x5a, 0x55,
	0xa9, 0x1d, 0x7b, 0x36, 0xd3, 0x65, 0x71, 0xaa, 0x52, 0xad, 0x55, 0xcb, 0xb9, 0x0b, 0x68, 0x0d,
	0x44, 0x0e, 0xf3, 0x4e, 0xb1, 0x5e, 0xad, 0x54, 0x0f, 0x73, 0x69, 0xb4, 0x0a, 0xcb, 0x1c, 0xfe,
	0x3e, 0x71, 0xfa, 0x20, 0x37, 0x96, 0xc0, 0xae, 0x1c, 0x56, 0x6b, 0xf5, 0x72, 0x6e, 0x79, 0xab,
	0xe6, 0x43, 0xe8, 0xc9, 0x7b, 0xe6, 0x5c, 0x82, 0x99, 0x60, 0x83, 0xc0, 0x08, 0x04, 0xd9, 0x3e,
	0xe9, 0xa8, 0x76, 0x58, 0xa9, 0xe6, 0x04, 0x34, 0x0f, 0xb9, 0x3e, 0x2d, 0x00, 0x39, 0xb5, 0xf5,
	0xcb, 0x20, 0x28, 0xf5, 0xf2, 0x71, 0xad, 0xee, 0x07, 0x85, 0x8a, 0xfa, 0x84, 0xe3, 0x7a, 0xe5,
	0xcd, 0x62, 0xe9, 0x6e, 0xee, 0x02, 0xba, 0x0c, 0xf9, 0x90, 0x5a, 0xae, 0x37, 0x6a, 0xd5, 0xe2,
	0x91, 0x52, 0x6c, 0x36, 0x8b, 0xa5, 0x5b, 0x39, 0x01, 0xe5, 0x61, 0x3e, 0xe4, 0xd6, 0xea, 0xd5,
	0xda, 0x61, 0xbd, 0x78, 0x7c, 0xf3, 0x6e, 0x2e, 0x85, 0x66, 0x61, 0xba, 0xcf, 0x29, 0x1e, 0xe4,
	0xd2, 0x1e, 0x7e, 0x3e, 0xa1, 0x51, 0xae, 0x36, 0x2a, 0xcd, 0xca, 0x9b, 0x65, 0xa5, 0x52, 0x7d,
	0xa3, 0x46, 0x63, 0x75, 0xa9, 0xcc, 0x1a, 0xa3, 0x14, 0x8f, 0x8e, 0x72, 0x5f, 0x0a, 0x37, 0x3e,
	0x5b, 0x86, 0x4c, 0xff, 0x83, 0x20, 0xfa, 0xb3, 0x00, 0xd3, 0xcc, 0x07, 0x21, 0xc4, 0x36, 0x6c,
	0xd1, 0xcf, 0x5d, 0xa2, 0x98, 0xc4, 0xc2, 0xb6, 0xfc, 0x53, 0xe1, 0x83, 0x27, 0x4f, 0xd3, 0xc2,
	0x47, 0x4f, 0x9e, 0xa6, 0x2f, 0xba, 0x85, 0xb3, 0x82, 0x55, 0x68, 0x15, 0x70, 0xe1, 0x61, 0xe1,
	0x93, 0x27, 0x4f, 0xd3, 0x78, 0xdb, 0x95, 0x76, 0xbd, 0xf3, 0x24, 0x91, 0xae, 0xe9, 0x9a, 0xf4,
	0xdf, 0x7e, 0xca, 0x4b, 0x37, 0x48, 0xc3, 0xb5, 0x27, 0x6d, 0x9f, 0x49, 0xbb, 0x86, 0xb6, 0x27,
	0x6d, 0x5b, 0xd2, 0x6e, 0xd0, 0x70, 0x49, 0x96, 0x4d, 0xa5, 0xf7, 0xa4, 0xed, 0x96, 0xb4, 0xeb,
	0xb5, 0x4c, 0x01, 0x01, 0x4b, 0xbb, 0x5e, 0x5f, 0x75, 0xed, 0xbf, 0x5e, 0xd7, 0xf4, 0xce, 0x9e,
	0xb4, 0xfd, 0x30, 0x5c, 0xa8, 0x58, 0xad, 0x87, 0x7b, 0xe8, 0xe7, 0x82, 0x77, 0xfd, 0x0d, 0x7c,
	0xde, 0x40, 0x57, 0xd8, 0x19, 0x7e, 0xc2, 0x17, 0x22, 0xf1, 0xb9, 0xf3, 0x85, 0xb0, 0x2d, 0xbf,
	0x46, 0x9c, 0x4e, 0x11, 0xa7, 0xc7, 0x88, 0xd3, 0xc4, 0xd9, 0xcd, 0x51, 0x9d, 0x45, 0x9f, 0x09,
	0xf4, 0x33, 0x18, 0xff, 0x13, 0x04, 0x7a, 0x31, 0x6a, 0x40, 0xe2, 0x07, 0x14, 0x71, 0x73, 0x34,
	0x41, 0x6c, 0xcb, 0x55, 0x62, 0xed, 0x18, 0xb1, 0x76, 0xc2, 0x2d, 0xd8, 0x85, 0x0e, 0xb5, 0xf7,
	0xd5, 0xf3, 0xed, 0xb5, 0xa5, 0x5d, 0xf2, 0xae, 0x92, 0x0c, 0xed, 0x6c, 0x4f, 0xda, 0xee, 0x48,
	0xbb, 0xf4, 0x11, 0xbe, 0x87, 0x7e, 0x2b, 0x00, 0x84, 0xe3, 0x79, 0x94, 0x8f, 0xa4, 0x07, 0xf3,
	0x41, 0x43, 0x5c, 0x4e, 0xe0, 0x60, 0x5b, 0xee, 0x11, 0x9b, 0xc6, 0x69, 0xda, 0x9c, 0x15, 0x70,
	0xa1, 0x57, 0x70, 0x0b, 0x4e, 0xc1, 0xa6, 0x96, 0xfd, 0x3f, 0x01, 0xca, 0xb7, 0x2c, 0x30, 0xa9,
	0x72, 0x20, 0x79, 0xe1, 0x0f, 0xa7, 0xef, 0x7b, 0xd2, 0x76, 0xaf, 0x2f, 0x77, 0x52, 0x39, 0xd8,
	0x93, 0xa8, 0x47, 0x5e, 0xa2, 0x38, 0x44, 0x92, 0x3c, 0x93, 0x7c, 0x37, 0xbc, 0x71, 0xfc, 0x1e,
	0xfa, 0xbd, 0x00, 0x97, 0x87, 0x8d, 0x1e, 0xd1, 0x16, 0x6b, 0xf2, 0xf0, 0xe1, 0xac, 0xf8, 0xd2,
	0xc8, 0xb2, 0xd8, 0x96, 0x6f, 0x12, 0x87, 0x27, 0x68, 0x10, 0xce, 0x0a, 0x2d, 0x3f, 0x08, 0x2f,
	0x33, 0xae, 0x12, 0xcb, 0x25, 0x2f, 0xe9, 0x83, 0x67, 0xb0, 0x87, 0x7a, 0x30, 0x88, 0xf1, 0x3a,
	0xe5, 0x3d, 0xf4, 0xa9, 0x00, 0x73, 0x9c, 0x99, 0x22, 0xda, 0x88, 0x26, 0x04, 0x67, 0xa4, 0x29,
	0xca, 0xe7, 0x89, 0x04, 0xb9, 0x3d, 0xf5, 0x35, 0x73, 0xfb, 0x2f, 0x02, 0xac, 0x0c, 0x19, 0x0b,
	0xa1, 0xab, 0x31, 0xc8, 0x92, 0xa7, 0x62, 0xe2, 0xd6, 0xa8, 0xa2, 0xd8, 0x96, 0x5b, 0xc4, 0xe6,
	0x0c, 0xb1, 0x79, 0x8a, 0xd8, 0x1c, 0xc0, 0x7b, 0x6b, 0xe4, 0x02, 0x34, 0x02, 0xec, 0x3f, 0x80,
	0x45, 0xfe, 0x20, 0x08, 0xb1, 0x35, 0x23, 0x71, 0x10, 0x25, 0x3e, 0x3f, 0x82, 0x14, 0xb6, 0xe5,
	0x3c, 0x71, 0x05, 0x88, 0x2b, 0x29, 0x0f, 0xfc, 0xc9, 0x00, 0xdb, 0x77, 0x61, 0x8e, 0x33, 0x2c,
	0x8a, 0x84, 0x9d, 0x3f, 0x8b, 0x8a, 0x84, 0x3d, 0x69, 0xde, 0x44, 0xf5, 0x4e, 0xf3, 0xf4, 0xfe,
	0x48, 0x80, 0xa5, 0x84, 0x39, 0x12, 0x62, 0x9d, 0x4a, 0x1e, 0x57, 0x89, 0x2f, 0x8c, 0x22, 0x16,
	0x18, 0x71, 0x91, 0x67, 0x44, 0x97, 0xe6, 0xfc, 0x50, 0xe7, 0xf9, 0xc3, 0xac, 0x78, 0xce, 0x73,
	0x9d, 0x9f, 0x25, 0x7a, 0x67, 0x88, 0xde, 0x0b, 0x44, 0xeb, 0x05, 0xf4, 0x3e, 0xcc, 0xf3, 0xa6,
	0x48, 0x28, 0xb6, 0x19, 0x6f, 0xbe, 0x25, 0x5e, 0x39, 0x57, 0x06, 0xdb, 0xf2, 0x32, 0xd1, 0x98,
	0xa5, 0x9e, 0xf6, 0xa8, 0xa7, 0x53, 0xa4, 0x9a, 0x91, 0xb2, 0x86, 0x4c, 0xc8, 0xc5, 0x5f, 0xde,
	0x68, 0x2d, 0x56, 0x49, 0x63, 0xaf, 0x7b, 0x71, 0x7d, 0x28, 0x3f, 0xd0, 0x87, 0xb8, 0xfa, 0x7e,
	0x23, 0x80, 0x98, 0xfc, 0xab, 0x00, 0xb4, 0x19, 0xf9, 0x22, 0x38, 0xe4, 0xd7, 0x15, 0xe2, 0xd5,
	0x11, 0x25, 0xb1, 0x2d, 0xff, 0x2f, 0x31, 0x67, 0x2e, 0x52, 0x64, 0xae, 0x8d, 0x78, 0x58, 0xaf,
	0x19, 0xda, 0x8d, 0x3d, 0xe4, 0x46, 0x1e, 0x71, 0xfe, 0x54, 0x01, 0x45, 0x9f, 0x81, 0x9c, 0x29,
	0x85, 0xb8, 0x71, 0x8e, 0x44, 0x00, 0xd5, 0x3c, 0x17, 0xaa, 0xfb, 0x70, 0x69, 0x60, 0x64, 0x80,
	0x58, 0xec, 0x79, 0xe3, 0x0a, 0x51, 0x1a, 0x2e, 0x10, 0xe4, 0xdf, 0x02, 0x93, 0x7f, 0x5d, 0x98,
	0xe3, 0x3c, 0xef, 0x23, 0xe9, 0xce, 0x1f, 0x2f, 0x44, 0xd2, 0x3d, 0x69, 0x42, 0x40, 0xd5, 0x2d,
	0x32, 0xea, 0xbe, 0x0f, 0xb3, 0xb1, 0xf7, 0x2b, 0x5a, 0xe5, 0x03, 0xe5, 0x3f, 0x97, 0xc5, 0xb5,
	0x61, 0x6c, 0x6c, 0xcb, 0x2f, 0x11, 0x15, 0x4b, 0x34, 0xc0, 0x3d, 0x3f, 0xc0, 0xf9, 0x00, 0x46,
	0x1a, 0xc8, 0x20, 0xb6, 0x04, 0xd6, 0xef, 0x02, 0x1a, 0x9c, 0x07, 0x44, 0x82, 0xc9, 0x1d, 0x68,
	0x88, 0x1b, 0xe7, 0x48, 0x60, 0x5b, 0x96, 0x88, 0x1d, 0x79, 0xa6, 0xa2, 0xcc, 0xc6, 0x95, 0xff,
	0x50, 0x80, 0x79, 0xde, 0x4b, 0x38, 0x72, 0xd6, 0x13, 0x9e, 0xda, 0x91, 0xb3, 0x9e, 0xf8, 0x9c,
	0xa6, 0x36, 0x2c, 0x0f, 0xb3, 0xe1, 0x57, 0x02, 0xac, 0x0c, 0x79, 0x12, 0x47, 0xae, 0xcd, 0xe1,
	0x2f, 0xf3, 0xc8, 0xb5, 0x79, 0xce, 0x2b, 0x5b, 0x7e, 0x99, 0x18, 0x26, 0xd2, 0x20, 0x9d, 0x15,
	0xbc, 0x5c, 0x8f, 0xc7, 0x86, 0x6d, 0xb2, 0x68, 0xec, 0x90, 0x0d, 0xf3, 0xbc, 0xc7, 0x6e, 0x04,
	0xae, 0x84, 0x77, 0x79, 0x04, 0xae, 0xa4, 0x17, 0xb3, 0x97, 0x9d, 0x2b, 0x61, 0x76, 0x8a, 0x13,
	0x1f, 0x3e, 0x79, 0x9a, 0x7e, 0xfa, 0x78, 0x3f, 0xf7, 0xf9, 0x17, 0x6b, 0xc2, 0x1f, 0xbf, 0x58,
	0x13, 0xfe, 0xf6, 0xc5, 0x9a, 0xf0, 0xf1, 0xdf, 0xd7, 0x2e, 0xfc, 0x3b, 0x00, 0x00, 0xff, 0xff,
	0x60, 0xce, 0xdc, 0x69, 0xef, 0x28, 0x00, 0x00,
}
