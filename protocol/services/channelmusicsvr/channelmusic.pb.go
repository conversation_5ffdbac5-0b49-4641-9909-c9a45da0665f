// Code generated by protoc-gen-gogo.
// source: src/channelmusicsvr/channelmusic.proto
// DO NOT EDIT!

/*
	Package channelmusic is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelmusicsvr/channelmusic.proto

	It has these top-level messages:
		MusicInfo
		AddChannelMusicReq
		AddChannelMusicResp
		RemoveChannelMusicReq
		RemoveChannelMusicResp
		RemoveChannelMusicByUidReq
		RemoveChannelMusicByUidResp
		RemoveChannelReportedMusicReq
		RemoveChannelReportedMusicResp
		GetReportedMusicListReq
		GetReportedMusicListResp
		GetChannelMusicListReq
		GetChannelMusicListResp
		ChannelMusicCtrlReq
		ChannelMusicCtrlResp
		ChannelMusicHeartBeatReq
		ChannelMusicHeartBeatResp
		SetChannelMusicPlayModeReq
		SetChannelMusicPlayModeResp
		SetChannelMusicVolumeReq
		SetChannelMusicVolumeResp
		SetChannelMusicCanShareReq
		SetChannelMusicCanShareResp
		SetChannelMusicFreeModeReq
		SetChannelMusicFreeModeResp
		SetChannelMusicNextReq
		SetChannelMusicNextResp
		GetChannelMusicStatusReq
		GetChannelMusicStatusResp
		SetChannelMusicCurrentReq
		SetChannelMusicCurrentResp
		ChannelMusicListChangedNotify
		ChannelMusicVolumeChangedNotify
		ChannelMusicPlayTheMusic
		ChannelMusicCanShareNotify
		GetPlayingMusicChannelListReq
		GetPlayingMusicChannelListResp
		PlayingMusicInfo
		BatGetChannelPlayingMusicInfoReq
		BatGetChannelPlayingMusicInfoResp
*/
package channelmusic

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type ChangeType int32

const (
	ChangeType_MUSIC_LIST_CHANGED ChangeType = 1
	ChangeType_PLAY_MODE_CHANGED  ChangeType = 2
	ChangeType_FREE_MODE_CHANGED  ChangeType = 3
)

var ChangeType_name = map[int32]string{
	1: "MUSIC_LIST_CHANGED",
	2: "PLAY_MODE_CHANGED",
	3: "FREE_MODE_CHANGED",
}
var ChangeType_value = map[string]int32{
	"MUSIC_LIST_CHANGED": 1,
	"PLAY_MODE_CHANGED":  2,
	"FREE_MODE_CHANGED":  3,
}

func (x ChangeType) Enum() *ChangeType {
	p := new(ChangeType)
	*p = x
	return p
}
func (x ChangeType) String() string {
	return proto.EnumName(ChangeType_name, int32(x))
}
func (x *ChangeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ChangeType_value, data, "ChangeType")
	if err != nil {
		return err
	}
	*x = ChangeType(value)
	return nil
}
func (ChangeType) EnumDescriptor() ([]byte, []int) { return fileDescriptorChannelmusic, []int{0} }

// 添加歌曲
type MusicInfo struct {
	ClientKey string `protobuf:"bytes,1,req,name=client_key,json=clientKey" json:"client_key"`
	Name      string `protobuf:"bytes,2,req,name=name" json:"name"`
	Author    string `protobuf:"bytes,3,req,name=author" json:"author"`
	Uid       uint32 `protobuf:"varint,4,req,name=uid" json:"uid"`
	Volume    uint32 `protobuf:"varint,5,opt,name=volume" json:"volume"`
	Key       int64  `protobuf:"varint,6,opt,name=key" json:"key"`
	IsLocal   uint32 `protobuf:"varint,8,opt,name=is_local,json=isLocal" json:"is_local"`
	Status    uint32 `protobuf:"varint,9,opt,name=status" json:"status"`
	MusicType uint32 `protobuf:"varint,10,opt,name=music_type,json=musicType" json:"music_type"`
	StartTime uint32 `protobuf:"varint,11,opt,name=start_time,json=startTime" json:"start_time"`
}

func (m *MusicInfo) Reset()                    { *m = MusicInfo{} }
func (m *MusicInfo) String() string            { return proto.CompactTextString(m) }
func (*MusicInfo) ProtoMessage()               {}
func (*MusicInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelmusic, []int{0} }

func (m *MusicInfo) GetClientKey() string {
	if m != nil {
		return m.ClientKey
	}
	return ""
}

func (m *MusicInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MusicInfo) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *MusicInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MusicInfo) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *MusicInfo) GetKey() int64 {
	if m != nil {
		return m.Key
	}
	return 0
}

func (m *MusicInfo) GetIsLocal() uint32 {
	if m != nil {
		return m.IsLocal
	}
	return 0
}

func (m *MusicInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *MusicInfo) GetMusicType() uint32 {
	if m != nil {
		return m.MusicType
	}
	return 0
}

func (m *MusicInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

type AddChannelMusicReq struct {
	Uid           uint32       `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId     uint32       `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicInfoList []*MusicInfo `protobuf:"bytes,3,rep,name=music_info_list,json=musicInfoList" json:"music_info_list,omitempty"`
}

func (m *AddChannelMusicReq) Reset()                    { *m = AddChannelMusicReq{} }
func (m *AddChannelMusicReq) String() string            { return proto.CompactTextString(m) }
func (*AddChannelMusicReq) ProtoMessage()               {}
func (*AddChannelMusicReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmusic, []int{1} }

func (m *AddChannelMusicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddChannelMusicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddChannelMusicReq) GetMusicInfoList() []*MusicInfo {
	if m != nil {
		return m.MusicInfoList
	}
	return nil
}

type AddChannelMusicResp struct {
	MusicAddedList    []*MusicInfo `protobuf:"bytes,1,rep,name=music_added_list,json=musicAddedList" json:"music_added_list,omitempty"`
	MaxMusicCount     uint32       `protobuf:"varint,2,opt,name=max_music_count,json=maxMusicCount" json:"max_music_count"`
	CurrentMusicCount uint32       `protobuf:"varint,3,opt,name=current_music_count,json=currentMusicCount" json:"current_music_count"`
}

func (m *AddChannelMusicResp) Reset()                    { *m = AddChannelMusicResp{} }
func (m *AddChannelMusicResp) String() string            { return proto.CompactTextString(m) }
func (*AddChannelMusicResp) ProtoMessage()               {}
func (*AddChannelMusicResp) Descriptor() ([]byte, []int) { return fileDescriptorChannelmusic, []int{2} }

func (m *AddChannelMusicResp) GetMusicAddedList() []*MusicInfo {
	if m != nil {
		return m.MusicAddedList
	}
	return nil
}

func (m *AddChannelMusicResp) GetMaxMusicCount() uint32 {
	if m != nil {
		return m.MaxMusicCount
	}
	return 0
}

func (m *AddChannelMusicResp) GetCurrentMusicCount() uint32 {
	if m != nil {
		return m.CurrentMusicCount
	}
	return 0
}

type RemoveChannelMusicReq struct {
	Uid         uint32  `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId   uint32  `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicIdList []int64 `protobuf:"varint,3,rep,name=music_id_list,json=musicIdList" json:"music_id_list,omitempty"`
	RemoveSelf  bool    `protobuf:"varint,4,opt,name=remove_self,json=removeSelf" json:"remove_self"`
}

func (m *RemoveChannelMusicReq) Reset()         { *m = RemoveChannelMusicReq{} }
func (m *RemoveChannelMusicReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMusicReq) ProtoMessage()    {}
func (*RemoveChannelMusicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{3}
}

func (m *RemoveChannelMusicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveChannelMusicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveChannelMusicReq) GetMusicIdList() []int64 {
	if m != nil {
		return m.MusicIdList
	}
	return nil
}

func (m *RemoveChannelMusicReq) GetRemoveSelf() bool {
	if m != nil {
		return m.RemoveSelf
	}
	return false
}

type RemoveChannelMusicResp struct {
	MusicRemovedList []int64 `protobuf:"varint,1,rep,name=music_removed_list,json=musicRemovedList" json:"music_removed_list,omitempty"`
}

func (m *RemoveChannelMusicResp) Reset()         { *m = RemoveChannelMusicResp{} }
func (m *RemoveChannelMusicResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMusicResp) ProtoMessage()    {}
func (*RemoveChannelMusicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{4}
}

func (m *RemoveChannelMusicResp) GetMusicRemovedList() []int64 {
	if m != nil {
		return m.MusicRemovedList
	}
	return nil
}

type RemoveChannelMusicByUidReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *RemoveChannelMusicByUidReq) Reset()         { *m = RemoveChannelMusicByUidReq{} }
func (m *RemoveChannelMusicByUidReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMusicByUidReq) ProtoMessage()    {}
func (*RemoveChannelMusicByUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{5}
}

func (m *RemoveChannelMusicByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveChannelMusicByUidReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type RemoveChannelMusicByUidResp struct {
}

func (m *RemoveChannelMusicByUidResp) Reset()         { *m = RemoveChannelMusicByUidResp{} }
func (m *RemoveChannelMusicByUidResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelMusicByUidResp) ProtoMessage()    {}
func (*RemoveChannelMusicByUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{6}
}

// 从房间播放列表中删除被举报成功的音乐
type RemoveChannelReportedMusicReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicName string `protobuf:"bytes,3,req,name=music_name,json=musicName" json:"music_name"`
	Author    string `protobuf:"bytes,4,req,name=author" json:"author"`
}

func (m *RemoveChannelReportedMusicReq) Reset()         { *m = RemoveChannelReportedMusicReq{} }
func (m *RemoveChannelReportedMusicReq) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelReportedMusicReq) ProtoMessage()    {}
func (*RemoveChannelReportedMusicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{7}
}

func (m *RemoveChannelReportedMusicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveChannelReportedMusicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RemoveChannelReportedMusicReq) GetMusicName() string {
	if m != nil {
		return m.MusicName
	}
	return ""
}

func (m *RemoveChannelReportedMusicReq) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

type RemoveChannelReportedMusicResp struct {
	MusicId int64 `protobuf:"varint,1,req,name=music_id,json=musicId" json:"music_id"`
}

func (m *RemoveChannelReportedMusicResp) Reset()         { *m = RemoveChannelReportedMusicResp{} }
func (m *RemoveChannelReportedMusicResp) String() string { return proto.CompactTextString(m) }
func (*RemoveChannelReportedMusicResp) ProtoMessage()    {}
func (*RemoveChannelReportedMusicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{8}
}

func (m *RemoveChannelReportedMusicResp) GetMusicId() int64 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

type GetReportedMusicListReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetReportedMusicListReq) Reset()         { *m = GetReportedMusicListReq{} }
func (m *GetReportedMusicListReq) String() string { return proto.CompactTextString(m) }
func (*GetReportedMusicListReq) ProtoMessage()    {}
func (*GetReportedMusicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{9}
}

func (m *GetReportedMusicListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetReportedMusicListResp struct {
	ReportedMusicList []string `protobuf:"bytes,1,rep,name=reported_music_list,json=reportedMusicList" json:"reported_music_list,omitempty"`
}

func (m *GetReportedMusicListResp) Reset()         { *m = GetReportedMusicListResp{} }
func (m *GetReportedMusicListResp) String() string { return proto.CompactTextString(m) }
func (*GetReportedMusicListResp) ProtoMessage()    {}
func (*GetReportedMusicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{10}
}

func (m *GetReportedMusicListResp) GetReportedMusicList() []string {
	if m != nil {
		return m.ReportedMusicList
	}
	return nil
}

type GetChannelMusicListReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelMusicListReq) Reset()         { *m = GetChannelMusicListReq{} }
func (m *GetChannelMusicListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicListReq) ProtoMessage()    {}
func (*GetChannelMusicListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{11}
}

func (m *GetChannelMusicListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMusicListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMusicListResp struct {
	MusicList []*MusicInfo `protobuf:"bytes,1,rep,name=music_list,json=musicList" json:"music_list,omitempty"`
}

func (m *GetChannelMusicListResp) Reset()         { *m = GetChannelMusicListResp{} }
func (m *GetChannelMusicListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicListResp) ProtoMessage()    {}
func (*GetChannelMusicListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{12}
}

func (m *GetChannelMusicListResp) GetMusicList() []*MusicInfo {
	if m != nil {
		return m.MusicList
	}
	return nil
}

// 控制
type ChannelMusicCtrlReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	ChannelCtrl uint32 `protobuf:"varint,3,req,name=channel_ctrl,json=channelCtrl" json:"channel_ctrl"`
}

func (m *ChannelMusicCtrlReq) Reset()                    { *m = ChannelMusicCtrlReq{} }
func (m *ChannelMusicCtrlReq) String() string            { return proto.CompactTextString(m) }
func (*ChannelMusicCtrlReq) ProtoMessage()               {}
func (*ChannelMusicCtrlReq) Descriptor() ([]byte, []int) { return fileDescriptorChannelmusic, []int{13} }

func (m *ChannelMusicCtrlReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMusicCtrlReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicCtrlReq) GetChannelCtrl() uint32 {
	if m != nil {
		return m.ChannelCtrl
	}
	return 0
}

type ChannelMusicCtrlResp struct {
	NextMusic *MusicInfo `protobuf:"bytes,1,opt,name=next_music,json=nextMusic" json:"next_music,omitempty"`
}

func (m *ChannelMusicCtrlResp) Reset()         { *m = ChannelMusicCtrlResp{} }
func (m *ChannelMusicCtrlResp) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicCtrlResp) ProtoMessage()    {}
func (*ChannelMusicCtrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{14}
}

func (m *ChannelMusicCtrlResp) GetNextMusic() *MusicInfo {
	if m != nil {
		return m.NextMusic
	}
	return nil
}

type ChannelMusicHeartBeatReq struct {
	Uid         uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId   uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicKey    int64  `protobuf:"varint,3,req,name=music_key,json=musicKey" json:"music_key"`
	Volume      uint32 `protobuf:"varint,4,req,name=volume" json:"volume"`
	Percent     uint32 `protobuf:"varint,5,req,name=percent" json:"percent"`
	ClientEvent uint32 `protobuf:"varint,6,opt,name=client_event,json=clientEvent" json:"client_event"`
}

func (m *ChannelMusicHeartBeatReq) Reset()         { *m = ChannelMusicHeartBeatReq{} }
func (m *ChannelMusicHeartBeatReq) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicHeartBeatReq) ProtoMessage()    {}
func (*ChannelMusicHeartBeatReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{15}
}

func (m *ChannelMusicHeartBeatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMusicHeartBeatReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicHeartBeatReq) GetMusicKey() int64 {
	if m != nil {
		return m.MusicKey
	}
	return 0
}

func (m *ChannelMusicHeartBeatReq) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *ChannelMusicHeartBeatReq) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

func (m *ChannelMusicHeartBeatReq) GetClientEvent() uint32 {
	if m != nil {
		return m.ClientEvent
	}
	return 0
}

type ChannelMusicHeartBeatResp struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicId   int64  `protobuf:"varint,3,opt,name=music_id,json=musicId" json:"music_id"`
	Volume    uint32 `protobuf:"varint,4,opt,name=volume" json:"volume"`
}

func (m *ChannelMusicHeartBeatResp) Reset()         { *m = ChannelMusicHeartBeatResp{} }
func (m *ChannelMusicHeartBeatResp) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicHeartBeatResp) ProtoMessage()    {}
func (*ChannelMusicHeartBeatResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{16}
}

func (m *ChannelMusicHeartBeatResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMusicHeartBeatResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicHeartBeatResp) GetMusicId() int64 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

func (m *ChannelMusicHeartBeatResp) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

type SetChannelMusicPlayModeReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	PlayMode  uint32 `protobuf:"varint,3,req,name=play_mode,json=playMode" json:"play_mode"`
}

func (m *SetChannelMusicPlayModeReq) Reset()         { *m = SetChannelMusicPlayModeReq{} }
func (m *SetChannelMusicPlayModeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicPlayModeReq) ProtoMessage()    {}
func (*SetChannelMusicPlayModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{17}
}

func (m *SetChannelMusicPlayModeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicPlayModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicPlayModeReq) GetPlayMode() uint32 {
	if m != nil {
		return m.PlayMode
	}
	return 0
}

type SetChannelMusicPlayModeResp struct {
}

func (m *SetChannelMusicPlayModeResp) Reset()         { *m = SetChannelMusicPlayModeResp{} }
func (m *SetChannelMusicPlayModeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicPlayModeResp) ProtoMessage()    {}
func (*SetChannelMusicPlayModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{18}
}

type SetChannelMusicVolumeReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	Volume    uint32 `protobuf:"varint,3,req,name=volume" json:"volume"`
}

func (m *SetChannelMusicVolumeReq) Reset()         { *m = SetChannelMusicVolumeReq{} }
func (m *SetChannelMusicVolumeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicVolumeReq) ProtoMessage()    {}
func (*SetChannelMusicVolumeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{19}
}

func (m *SetChannelMusicVolumeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicVolumeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicVolumeReq) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

type SetChannelMusicVolumeResp struct {
}

func (m *SetChannelMusicVolumeResp) Reset()         { *m = SetChannelMusicVolumeResp{} }
func (m *SetChannelMusicVolumeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicVolumeResp) ProtoMessage()    {}
func (*SetChannelMusicVolumeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{20}
}

type SetChannelMusicCanShareReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	CanShare  bool   `protobuf:"varint,3,req,name=can_share,json=canShare" json:"can_share"`
}

func (m *SetChannelMusicCanShareReq) Reset()         { *m = SetChannelMusicCanShareReq{} }
func (m *SetChannelMusicCanShareReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicCanShareReq) ProtoMessage()    {}
func (*SetChannelMusicCanShareReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{21}
}

func (m *SetChannelMusicCanShareReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicCanShareReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicCanShareReq) GetCanShare() bool {
	if m != nil {
		return m.CanShare
	}
	return false
}

type SetChannelMusicCanShareResp struct {
}

func (m *SetChannelMusicCanShareResp) Reset()         { *m = SetChannelMusicCanShareResp{} }
func (m *SetChannelMusicCanShareResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicCanShareResp) ProtoMessage()    {}
func (*SetChannelMusicCanShareResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{22}
}

type SetChannelMusicFreeModeReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	FreeMode  bool   `protobuf:"varint,3,req,name=free_mode,json=freeMode" json:"free_mode"`
}

func (m *SetChannelMusicFreeModeReq) Reset()         { *m = SetChannelMusicFreeModeReq{} }
func (m *SetChannelMusicFreeModeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicFreeModeReq) ProtoMessage()    {}
func (*SetChannelMusicFreeModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{23}
}

func (m *SetChannelMusicFreeModeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicFreeModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicFreeModeReq) GetFreeMode() bool {
	if m != nil {
		return m.FreeMode
	}
	return false
}

type SetChannelMusicFreeModeResp struct {
}

func (m *SetChannelMusicFreeModeResp) Reset()         { *m = SetChannelMusicFreeModeResp{} }
func (m *SetChannelMusicFreeModeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicFreeModeResp) ProtoMessage()    {}
func (*SetChannelMusicFreeModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{24}
}

// 设置下一首
type SetChannelMusicNextReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicId   int64  `protobuf:"varint,3,req,name=music_id,json=musicId" json:"music_id"`
}

func (m *SetChannelMusicNextReq) Reset()         { *m = SetChannelMusicNextReq{} }
func (m *SetChannelMusicNextReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicNextReq) ProtoMessage()    {}
func (*SetChannelMusicNextReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{25}
}

func (m *SetChannelMusicNextReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicNextReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicNextReq) GetMusicId() int64 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

type SetChannelMusicNextResp struct {
}

func (m *SetChannelMusicNextResp) Reset()         { *m = SetChannelMusicNextResp{} }
func (m *SetChannelMusicNextResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicNextResp) ProtoMessage()    {}
func (*SetChannelMusicNextResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{26}
}

// 播放器状态
type GetChannelMusicStatusReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetChannelMusicStatusReq) Reset()         { *m = GetChannelMusicStatusReq{} }
func (m *GetChannelMusicStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicStatusReq) ProtoMessage()    {}
func (*GetChannelMusicStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{27}
}

func (m *GetChannelMusicStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetChannelMusicStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelMusicStatusResp struct {
	PlayMode       uint32 `protobuf:"varint,1,req,name=play_mode,json=playMode" json:"play_mode"`
	Volume         uint32 `protobuf:"varint,2,req,name=volume" json:"volume"`
	CanShare       bool   `protobuf:"varint,3,req,name=can_share,json=canShare" json:"can_share"`
	IsPlaying      bool   `protobuf:"varint,4,req,name=is_playing,json=isPlaying" json:"is_playing"`
	CurrentPlaying int64  `protobuf:"varint,5,opt,name=current_playing,json=currentPlaying" json:"current_playing"`
	IsFreeMode     bool   `protobuf:"varint,6,opt,name=is_free_mode,json=isFreeMode" json:"is_free_mode"`
	MusicName      string `protobuf:"bytes,7,opt,name=music_name,json=musicName" json:"music_name"`
	MusicAuthor    string `protobuf:"bytes,8,opt,name=music_author,json=musicAuthor" json:"music_author"`
}

func (m *GetChannelMusicStatusResp) Reset()         { *m = GetChannelMusicStatusResp{} }
func (m *GetChannelMusicStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelMusicStatusResp) ProtoMessage()    {}
func (*GetChannelMusicStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{28}
}

func (m *GetChannelMusicStatusResp) GetPlayMode() uint32 {
	if m != nil {
		return m.PlayMode
	}
	return 0
}

func (m *GetChannelMusicStatusResp) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *GetChannelMusicStatusResp) GetCanShare() bool {
	if m != nil {
		return m.CanShare
	}
	return false
}

func (m *GetChannelMusicStatusResp) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

func (m *GetChannelMusicStatusResp) GetCurrentPlaying() int64 {
	if m != nil {
		return m.CurrentPlaying
	}
	return 0
}

func (m *GetChannelMusicStatusResp) GetIsFreeMode() bool {
	if m != nil {
		return m.IsFreeMode
	}
	return false
}

func (m *GetChannelMusicStatusResp) GetMusicName() string {
	if m != nil {
		return m.MusicName
	}
	return ""
}

func (m *GetChannelMusicStatusResp) GetMusicAuthor() string {
	if m != nil {
		return m.MusicAuthor
	}
	return ""
}

type SetChannelMusicCurrentReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	MusicId   uint32 `protobuf:"varint,3,req,name=music_id,json=musicId" json:"music_id"`
}

func (m *SetChannelMusicCurrentReq) Reset()         { *m = SetChannelMusicCurrentReq{} }
func (m *SetChannelMusicCurrentReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicCurrentReq) ProtoMessage()    {}
func (*SetChannelMusicCurrentReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{29}
}

func (m *SetChannelMusicCurrentReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetChannelMusicCurrentReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelMusicCurrentReq) GetMusicId() uint32 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

type SetChannelMusicCurrentResp struct {
}

func (m *SetChannelMusicCurrentResp) Reset()         { *m = SetChannelMusicCurrentResp{} }
func (m *SetChannelMusicCurrentResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelMusicCurrentResp) ProtoMessage()    {}
func (*SetChannelMusicCurrentResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{30}
}

// 推送相关
type ChannelMusicListChangedNotify struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	ChangeType  uint32 `protobuf:"varint,2,req,name=change_type,json=changeType" json:"change_type"`
	ChangeValue uint32 `protobuf:"varint,3,opt,name=change_value,json=changeValue" json:"change_value"`
}

func (m *ChannelMusicListChangedNotify) Reset()         { *m = ChannelMusicListChangedNotify{} }
func (m *ChannelMusicListChangedNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicListChangedNotify) ProtoMessage()    {}
func (*ChannelMusicListChangedNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{31}
}

func (m *ChannelMusicListChangedNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicListChangedNotify) GetChangeType() uint32 {
	if m != nil {
		return m.ChangeType
	}
	return 0
}

func (m *ChannelMusicListChangedNotify) GetChangeValue() uint32 {
	if m != nil {
		return m.ChangeValue
	}
	return 0
}

type ChannelMusicVolumeChangedNotify struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Volume    uint32 `protobuf:"varint,2,req,name=volume" json:"volume"`
}

func (m *ChannelMusicVolumeChangedNotify) Reset()         { *m = ChannelMusicVolumeChangedNotify{} }
func (m *ChannelMusicVolumeChangedNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicVolumeChangedNotify) ProtoMessage()    {}
func (*ChannelMusicVolumeChangedNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{32}
}

func (m *ChannelMusicVolumeChangedNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicVolumeChangedNotify) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

type ChannelMusicPlayTheMusic struct {
	ChannelId        uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid              uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	MusicId          int64  `protobuf:"varint,3,req,name=music_id,json=musicId" json:"music_id"`
	Volume           uint32 `protobuf:"varint,4,req,name=volume" json:"volume"`
	Start            bool   `protobuf:"varint,5,req,name=start" json:"start"`
	Percent          uint32 `protobuf:"varint,6,req,name=percent" json:"percent"`
	MusicName        string `protobuf:"bytes,7,opt,name=music_name,json=musicName" json:"music_name"`
	MusicChangedType uint32 `protobuf:"varint,8,opt,name=music_changed_type,json=musicChangedType" json:"music_changed_type"`
	SwitcherUid      uint32 `protobuf:"varint,9,opt,name=switcher_uid,json=switcherUid" json:"switcher_uid"`
}

func (m *ChannelMusicPlayTheMusic) Reset()         { *m = ChannelMusicPlayTheMusic{} }
func (m *ChannelMusicPlayTheMusic) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicPlayTheMusic) ProtoMessage()    {}
func (*ChannelMusicPlayTheMusic) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{33}
}

func (m *ChannelMusicPlayTheMusic) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicPlayTheMusic) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelMusicPlayTheMusic) GetMusicId() int64 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

func (m *ChannelMusicPlayTheMusic) GetVolume() uint32 {
	if m != nil {
		return m.Volume
	}
	return 0
}

func (m *ChannelMusicPlayTheMusic) GetStart() bool {
	if m != nil {
		return m.Start
	}
	return false
}

func (m *ChannelMusicPlayTheMusic) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

func (m *ChannelMusicPlayTheMusic) GetMusicName() string {
	if m != nil {
		return m.MusicName
	}
	return ""
}

func (m *ChannelMusicPlayTheMusic) GetMusicChangedType() uint32 {
	if m != nil {
		return m.MusicChangedType
	}
	return 0
}

func (m *ChannelMusicPlayTheMusic) GetSwitcherUid() uint32 {
	if m != nil {
		return m.SwitcherUid
	}
	return 0
}

type ChannelMusicCanShareNotify struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	CanShare  bool   `protobuf:"varint,2,req,name=can_share,json=canShare" json:"can_share"`
}

func (m *ChannelMusicCanShareNotify) Reset()         { *m = ChannelMusicCanShareNotify{} }
func (m *ChannelMusicCanShareNotify) String() string { return proto.CompactTextString(m) }
func (*ChannelMusicCanShareNotify) ProtoMessage()    {}
func (*ChannelMusicCanShareNotify) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{34}
}

func (m *ChannelMusicCanShareNotify) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelMusicCanShareNotify) GetCanShare() bool {
	if m != nil {
		return m.CanShare
	}
	return false
}

// 获取所有正在播放歌曲的房间id
type GetPlayingMusicChannelListReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetPlayingMusicChannelListReq) Reset()         { *m = GetPlayingMusicChannelListReq{} }
func (m *GetPlayingMusicChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetPlayingMusicChannelListReq) ProtoMessage()    {}
func (*GetPlayingMusicChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{35}
}

func (m *GetPlayingMusicChannelListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetPlayingMusicChannelListResp struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *GetPlayingMusicChannelListResp) Reset()         { *m = GetPlayingMusicChannelListResp{} }
func (m *GetPlayingMusicChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetPlayingMusicChannelListResp) ProtoMessage()    {}
func (*GetPlayingMusicChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{36}
}

func (m *GetPlayingMusicChannelListResp) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

// 房间正在播放歌曲信息
type PlayingMusicInfo struct {
	ChannelId   uint32 `protobuf:"varint,1,opt,name=channel_id,json=channelId" json:"channel_id"`
	MusicId     int64  `protobuf:"varint,2,opt,name=music_id,json=musicId" json:"music_id"`
	MusicName   string `protobuf:"bytes,3,opt,name=music_name,json=musicName" json:"music_name"`
	MusicAuthor string `protobuf:"bytes,4,opt,name=music_author,json=musicAuthor" json:"music_author"`
}

func (m *PlayingMusicInfo) Reset()                    { *m = PlayingMusicInfo{} }
func (m *PlayingMusicInfo) String() string            { return proto.CompactTextString(m) }
func (*PlayingMusicInfo) ProtoMessage()               {}
func (*PlayingMusicInfo) Descriptor() ([]byte, []int) { return fileDescriptorChannelmusic, []int{37} }

func (m *PlayingMusicInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PlayingMusicInfo) GetMusicId() int64 {
	if m != nil {
		return m.MusicId
	}
	return 0
}

func (m *PlayingMusicInfo) GetMusicName() string {
	if m != nil {
		return m.MusicName
	}
	return ""
}

func (m *PlayingMusicInfo) GetMusicAuthor() string {
	if m != nil {
		return m.MusicAuthor
	}
	return ""
}

// 批量获取房间正在播放的歌曲信息
type BatGetChannelPlayingMusicInfoReq struct {
	ChannelIdList []uint32 `protobuf:"varint,1,rep,name=channel_id_list,json=channelIdList" json:"channel_id_list,omitempty"`
}

func (m *BatGetChannelPlayingMusicInfoReq) Reset()         { *m = BatGetChannelPlayingMusicInfoReq{} }
func (m *BatGetChannelPlayingMusicInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelPlayingMusicInfoReq) ProtoMessage()    {}
func (*BatGetChannelPlayingMusicInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{38}
}

func (m *BatGetChannelPlayingMusicInfoReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

type BatGetChannelPlayingMusicInfoResp struct {
	MusicList []*PlayingMusicInfo `protobuf:"bytes,1,rep,name=music_list,json=musicList" json:"music_list,omitempty"`
}

func (m *BatGetChannelPlayingMusicInfoResp) Reset()         { *m = BatGetChannelPlayingMusicInfoResp{} }
func (m *BatGetChannelPlayingMusicInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetChannelPlayingMusicInfoResp) ProtoMessage()    {}
func (*BatGetChannelPlayingMusicInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelmusic, []int{39}
}

func (m *BatGetChannelPlayingMusicInfoResp) GetMusicList() []*PlayingMusicInfo {
	if m != nil {
		return m.MusicList
	}
	return nil
}

func init() {
	proto.RegisterType((*MusicInfo)(nil), "channelmusic.MusicInfo")
	proto.RegisterType((*AddChannelMusicReq)(nil), "channelmusic.AddChannelMusicReq")
	proto.RegisterType((*AddChannelMusicResp)(nil), "channelmusic.AddChannelMusicResp")
	proto.RegisterType((*RemoveChannelMusicReq)(nil), "channelmusic.RemoveChannelMusicReq")
	proto.RegisterType((*RemoveChannelMusicResp)(nil), "channelmusic.RemoveChannelMusicResp")
	proto.RegisterType((*RemoveChannelMusicByUidReq)(nil), "channelmusic.RemoveChannelMusicByUidReq")
	proto.RegisterType((*RemoveChannelMusicByUidResp)(nil), "channelmusic.RemoveChannelMusicByUidResp")
	proto.RegisterType((*RemoveChannelReportedMusicReq)(nil), "channelmusic.RemoveChannelReportedMusicReq")
	proto.RegisterType((*RemoveChannelReportedMusicResp)(nil), "channelmusic.RemoveChannelReportedMusicResp")
	proto.RegisterType((*GetReportedMusicListReq)(nil), "channelmusic.GetReportedMusicListReq")
	proto.RegisterType((*GetReportedMusicListResp)(nil), "channelmusic.GetReportedMusicListResp")
	proto.RegisterType((*GetChannelMusicListReq)(nil), "channelmusic.GetChannelMusicListReq")
	proto.RegisterType((*GetChannelMusicListResp)(nil), "channelmusic.GetChannelMusicListResp")
	proto.RegisterType((*ChannelMusicCtrlReq)(nil), "channelmusic.ChannelMusicCtrlReq")
	proto.RegisterType((*ChannelMusicCtrlResp)(nil), "channelmusic.ChannelMusicCtrlResp")
	proto.RegisterType((*ChannelMusicHeartBeatReq)(nil), "channelmusic.ChannelMusicHeartBeatReq")
	proto.RegisterType((*ChannelMusicHeartBeatResp)(nil), "channelmusic.ChannelMusicHeartBeatResp")
	proto.RegisterType((*SetChannelMusicPlayModeReq)(nil), "channelmusic.SetChannelMusicPlayModeReq")
	proto.RegisterType((*SetChannelMusicPlayModeResp)(nil), "channelmusic.SetChannelMusicPlayModeResp")
	proto.RegisterType((*SetChannelMusicVolumeReq)(nil), "channelmusic.SetChannelMusicVolumeReq")
	proto.RegisterType((*SetChannelMusicVolumeResp)(nil), "channelmusic.SetChannelMusicVolumeResp")
	proto.RegisterType((*SetChannelMusicCanShareReq)(nil), "channelmusic.SetChannelMusicCanShareReq")
	proto.RegisterType((*SetChannelMusicCanShareResp)(nil), "channelmusic.SetChannelMusicCanShareResp")
	proto.RegisterType((*SetChannelMusicFreeModeReq)(nil), "channelmusic.SetChannelMusicFreeModeReq")
	proto.RegisterType((*SetChannelMusicFreeModeResp)(nil), "channelmusic.SetChannelMusicFreeModeResp")
	proto.RegisterType((*SetChannelMusicNextReq)(nil), "channelmusic.SetChannelMusicNextReq")
	proto.RegisterType((*SetChannelMusicNextResp)(nil), "channelmusic.SetChannelMusicNextResp")
	proto.RegisterType((*GetChannelMusicStatusReq)(nil), "channelmusic.GetChannelMusicStatusReq")
	proto.RegisterType((*GetChannelMusicStatusResp)(nil), "channelmusic.GetChannelMusicStatusResp")
	proto.RegisterType((*SetChannelMusicCurrentReq)(nil), "channelmusic.SetChannelMusicCurrentReq")
	proto.RegisterType((*SetChannelMusicCurrentResp)(nil), "channelmusic.SetChannelMusicCurrentResp")
	proto.RegisterType((*ChannelMusicListChangedNotify)(nil), "channelmusic.ChannelMusicListChangedNotify")
	proto.RegisterType((*ChannelMusicVolumeChangedNotify)(nil), "channelmusic.ChannelMusicVolumeChangedNotify")
	proto.RegisterType((*ChannelMusicPlayTheMusic)(nil), "channelmusic.ChannelMusicPlayTheMusic")
	proto.RegisterType((*ChannelMusicCanShareNotify)(nil), "channelmusic.ChannelMusicCanShareNotify")
	proto.RegisterType((*GetPlayingMusicChannelListReq)(nil), "channelmusic.GetPlayingMusicChannelListReq")
	proto.RegisterType((*GetPlayingMusicChannelListResp)(nil), "channelmusic.GetPlayingMusicChannelListResp")
	proto.RegisterType((*PlayingMusicInfo)(nil), "channelmusic.PlayingMusicInfo")
	proto.RegisterType((*BatGetChannelPlayingMusicInfoReq)(nil), "channelmusic.BatGetChannelPlayingMusicInfoReq")
	proto.RegisterType((*BatGetChannelPlayingMusicInfoResp)(nil), "channelmusic.BatGetChannelPlayingMusicInfoResp")
	proto.RegisterEnum("channelmusic.ChangeType", ChangeType_name, ChangeType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelMusic service

type ChannelMusicClient interface {
	AddChannelMusic(ctx context.Context, in *AddChannelMusicReq, opts ...grpc.CallOption) (*AddChannelMusicResp, error)
	RemoveChannelMusic(ctx context.Context, in *RemoveChannelMusicReq, opts ...grpc.CallOption) (*RemoveChannelMusicResp, error)
	ChannelMusicCtrl(ctx context.Context, in *ChannelMusicCtrlReq, opts ...grpc.CallOption) (*ChannelMusicCtrlResp, error)
	ChannelMusicHeartBeat(ctx context.Context, in *ChannelMusicHeartBeatReq, opts ...grpc.CallOption) (*ChannelMusicHeartBeatResp, error)
	SetChannelMusicPlayMode(ctx context.Context, in *SetChannelMusicPlayModeReq, opts ...grpc.CallOption) (*SetChannelMusicPlayModeResp, error)
	SetChannelMusicVolume(ctx context.Context, in *SetChannelMusicVolumeReq, opts ...grpc.CallOption) (*SetChannelMusicVolumeResp, error)
	GetChannelMusicList(ctx context.Context, in *GetChannelMusicListReq, opts ...grpc.CallOption) (*GetChannelMusicListResp, error)
	SetChannelMusicNext(ctx context.Context, in *SetChannelMusicNextReq, opts ...grpc.CallOption) (*SetChannelMusicNextResp, error)
	SetChannelMusicCanShare(ctx context.Context, in *SetChannelMusicCanShareReq, opts ...grpc.CallOption) (*SetChannelMusicCanShareResp, error)
	GetChannelMusicStatus(ctx context.Context, in *GetChannelMusicStatusReq, opts ...grpc.CallOption) (*GetChannelMusicStatusResp, error)
	SetChannelMusicCurrent(ctx context.Context, in *SetChannelMusicCurrentReq, opts ...grpc.CallOption) (*SetChannelMusicCurrentResp, error)
	RemoveChannelMusicByUid(ctx context.Context, in *RemoveChannelMusicByUidReq, opts ...grpc.CallOption) (*RemoveChannelMusicByUidResp, error)
	SetChannelMusicFreeMode(ctx context.Context, in *SetChannelMusicFreeModeReq, opts ...grpc.CallOption) (*SetChannelMusicFreeModeResp, error)
	GetPlayingMusicChannelList(ctx context.Context, in *GetPlayingMusicChannelListReq, opts ...grpc.CallOption) (*GetPlayingMusicChannelListResp, error)
	RemoveChannelReportedMusic(ctx context.Context, in *RemoveChannelReportedMusicReq, opts ...grpc.CallOption) (*RemoveChannelReportedMusicResp, error)
	GetReportedMusicList(ctx context.Context, in *GetReportedMusicListReq, opts ...grpc.CallOption) (*GetReportedMusicListResp, error)
	BatGetChannelPlayingMusicInfo(ctx context.Context, in *BatGetChannelPlayingMusicInfoReq, opts ...grpc.CallOption) (*BatGetChannelPlayingMusicInfoResp, error)
}

type channelMusicClient struct {
	cc *grpc.ClientConn
}

func NewChannelMusicClient(cc *grpc.ClientConn) ChannelMusicClient {
	return &channelMusicClient{cc}
}

func (c *channelMusicClient) AddChannelMusic(ctx context.Context, in *AddChannelMusicReq, opts ...grpc.CallOption) (*AddChannelMusicResp, error) {
	out := new(AddChannelMusicResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/AddChannelMusic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) RemoveChannelMusic(ctx context.Context, in *RemoveChannelMusicReq, opts ...grpc.CallOption) (*RemoveChannelMusicResp, error) {
	out := new(RemoveChannelMusicResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/RemoveChannelMusic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) ChannelMusicCtrl(ctx context.Context, in *ChannelMusicCtrlReq, opts ...grpc.CallOption) (*ChannelMusicCtrlResp, error) {
	out := new(ChannelMusicCtrlResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/ChannelMusicCtrl", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) ChannelMusicHeartBeat(ctx context.Context, in *ChannelMusicHeartBeatReq, opts ...grpc.CallOption) (*ChannelMusicHeartBeatResp, error) {
	out := new(ChannelMusicHeartBeatResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/ChannelMusicHeartBeat", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) SetChannelMusicPlayMode(ctx context.Context, in *SetChannelMusicPlayModeReq, opts ...grpc.CallOption) (*SetChannelMusicPlayModeResp, error) {
	out := new(SetChannelMusicPlayModeResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/SetChannelMusicPlayMode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) SetChannelMusicVolume(ctx context.Context, in *SetChannelMusicVolumeReq, opts ...grpc.CallOption) (*SetChannelMusicVolumeResp, error) {
	out := new(SetChannelMusicVolumeResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/SetChannelMusicVolume", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) GetChannelMusicList(ctx context.Context, in *GetChannelMusicListReq, opts ...grpc.CallOption) (*GetChannelMusicListResp, error) {
	out := new(GetChannelMusicListResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/GetChannelMusicList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) SetChannelMusicNext(ctx context.Context, in *SetChannelMusicNextReq, opts ...grpc.CallOption) (*SetChannelMusicNextResp, error) {
	out := new(SetChannelMusicNextResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/SetChannelMusicNext", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) SetChannelMusicCanShare(ctx context.Context, in *SetChannelMusicCanShareReq, opts ...grpc.CallOption) (*SetChannelMusicCanShareResp, error) {
	out := new(SetChannelMusicCanShareResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/SetChannelMusicCanShare", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) GetChannelMusicStatus(ctx context.Context, in *GetChannelMusicStatusReq, opts ...grpc.CallOption) (*GetChannelMusicStatusResp, error) {
	out := new(GetChannelMusicStatusResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/GetChannelMusicStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) SetChannelMusicCurrent(ctx context.Context, in *SetChannelMusicCurrentReq, opts ...grpc.CallOption) (*SetChannelMusicCurrentResp, error) {
	out := new(SetChannelMusicCurrentResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/SetChannelMusicCurrent", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) RemoveChannelMusicByUid(ctx context.Context, in *RemoveChannelMusicByUidReq, opts ...grpc.CallOption) (*RemoveChannelMusicByUidResp, error) {
	out := new(RemoveChannelMusicByUidResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/RemoveChannelMusicByUid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) SetChannelMusicFreeMode(ctx context.Context, in *SetChannelMusicFreeModeReq, opts ...grpc.CallOption) (*SetChannelMusicFreeModeResp, error) {
	out := new(SetChannelMusicFreeModeResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/SetChannelMusicFreeMode", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) GetPlayingMusicChannelList(ctx context.Context, in *GetPlayingMusicChannelListReq, opts ...grpc.CallOption) (*GetPlayingMusicChannelListResp, error) {
	out := new(GetPlayingMusicChannelListResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/GetPlayingMusicChannelList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) RemoveChannelReportedMusic(ctx context.Context, in *RemoveChannelReportedMusicReq, opts ...grpc.CallOption) (*RemoveChannelReportedMusicResp, error) {
	out := new(RemoveChannelReportedMusicResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/RemoveChannelReportedMusic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) GetReportedMusicList(ctx context.Context, in *GetReportedMusicListReq, opts ...grpc.CallOption) (*GetReportedMusicListResp, error) {
	out := new(GetReportedMusicListResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/GetReportedMusicList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelMusicClient) BatGetChannelPlayingMusicInfo(ctx context.Context, in *BatGetChannelPlayingMusicInfoReq, opts ...grpc.CallOption) (*BatGetChannelPlayingMusicInfoResp, error) {
	out := new(BatGetChannelPlayingMusicInfoResp)
	err := grpc.Invoke(ctx, "/channelmusic.ChannelMusic/BatGetChannelPlayingMusicInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelMusic service

type ChannelMusicServer interface {
	AddChannelMusic(context.Context, *AddChannelMusicReq) (*AddChannelMusicResp, error)
	RemoveChannelMusic(context.Context, *RemoveChannelMusicReq) (*RemoveChannelMusicResp, error)
	ChannelMusicCtrl(context.Context, *ChannelMusicCtrlReq) (*ChannelMusicCtrlResp, error)
	ChannelMusicHeartBeat(context.Context, *ChannelMusicHeartBeatReq) (*ChannelMusicHeartBeatResp, error)
	SetChannelMusicPlayMode(context.Context, *SetChannelMusicPlayModeReq) (*SetChannelMusicPlayModeResp, error)
	SetChannelMusicVolume(context.Context, *SetChannelMusicVolumeReq) (*SetChannelMusicVolumeResp, error)
	GetChannelMusicList(context.Context, *GetChannelMusicListReq) (*GetChannelMusicListResp, error)
	SetChannelMusicNext(context.Context, *SetChannelMusicNextReq) (*SetChannelMusicNextResp, error)
	SetChannelMusicCanShare(context.Context, *SetChannelMusicCanShareReq) (*SetChannelMusicCanShareResp, error)
	GetChannelMusicStatus(context.Context, *GetChannelMusicStatusReq) (*GetChannelMusicStatusResp, error)
	SetChannelMusicCurrent(context.Context, *SetChannelMusicCurrentReq) (*SetChannelMusicCurrentResp, error)
	RemoveChannelMusicByUid(context.Context, *RemoveChannelMusicByUidReq) (*RemoveChannelMusicByUidResp, error)
	SetChannelMusicFreeMode(context.Context, *SetChannelMusicFreeModeReq) (*SetChannelMusicFreeModeResp, error)
	GetPlayingMusicChannelList(context.Context, *GetPlayingMusicChannelListReq) (*GetPlayingMusicChannelListResp, error)
	RemoveChannelReportedMusic(context.Context, *RemoveChannelReportedMusicReq) (*RemoveChannelReportedMusicResp, error)
	GetReportedMusicList(context.Context, *GetReportedMusicListReq) (*GetReportedMusicListResp, error)
	BatGetChannelPlayingMusicInfo(context.Context, *BatGetChannelPlayingMusicInfoReq) (*BatGetChannelPlayingMusicInfoResp, error)
}

func RegisterChannelMusicServer(s *grpc.Server, srv ChannelMusicServer) {
	s.RegisterService(&_ChannelMusic_serviceDesc, srv)
}

func _ChannelMusic_AddChannelMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddChannelMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).AddChannelMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/AddChannelMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).AddChannelMusic(ctx, req.(*AddChannelMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_RemoveChannelMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).RemoveChannelMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/RemoveChannelMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).RemoveChannelMusic(ctx, req.(*RemoveChannelMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_ChannelMusicCtrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelMusicCtrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).ChannelMusicCtrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/ChannelMusicCtrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).ChannelMusicCtrl(ctx, req.(*ChannelMusicCtrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_ChannelMusicHeartBeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelMusicHeartBeatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).ChannelMusicHeartBeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/ChannelMusicHeartBeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).ChannelMusicHeartBeat(ctx, req.(*ChannelMusicHeartBeatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_SetChannelMusicPlayMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicPlayModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).SetChannelMusicPlayMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/SetChannelMusicPlayMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).SetChannelMusicPlayMode(ctx, req.(*SetChannelMusicPlayModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_SetChannelMusicVolume_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicVolumeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).SetChannelMusicVolume(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/SetChannelMusicVolume",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).SetChannelMusicVolume(ctx, req.(*SetChannelMusicVolumeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_GetChannelMusicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMusicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).GetChannelMusicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/GetChannelMusicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).GetChannelMusicList(ctx, req.(*GetChannelMusicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_SetChannelMusicNext_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicNextReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).SetChannelMusicNext(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/SetChannelMusicNext",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).SetChannelMusicNext(ctx, req.(*SetChannelMusicNextReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_SetChannelMusicCanShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicCanShareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).SetChannelMusicCanShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/SetChannelMusicCanShare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).SetChannelMusicCanShare(ctx, req.(*SetChannelMusicCanShareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_GetChannelMusicStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelMusicStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).GetChannelMusicStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/GetChannelMusicStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).GetChannelMusicStatus(ctx, req.(*GetChannelMusicStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_SetChannelMusicCurrent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicCurrentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).SetChannelMusicCurrent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/SetChannelMusicCurrent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).SetChannelMusicCurrent(ctx, req.(*SetChannelMusicCurrentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_RemoveChannelMusicByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelMusicByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).RemoveChannelMusicByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/RemoveChannelMusicByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).RemoveChannelMusicByUid(ctx, req.(*RemoveChannelMusicByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_SetChannelMusicFreeMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelMusicFreeModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).SetChannelMusicFreeMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/SetChannelMusicFreeMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).SetChannelMusicFreeMode(ctx, req.(*SetChannelMusicFreeModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_GetPlayingMusicChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayingMusicChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).GetPlayingMusicChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/GetPlayingMusicChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).GetPlayingMusicChannelList(ctx, req.(*GetPlayingMusicChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_RemoveChannelReportedMusic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveChannelReportedMusicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).RemoveChannelReportedMusic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/RemoveChannelReportedMusic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).RemoveChannelReportedMusic(ctx, req.(*RemoveChannelReportedMusicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_GetReportedMusicList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReportedMusicListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).GetReportedMusicList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/GetReportedMusicList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).GetReportedMusicList(ctx, req.(*GetReportedMusicListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelMusic_BatGetChannelPlayingMusicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetChannelPlayingMusicInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelMusicServer).BatGetChannelPlayingMusicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelmusic.ChannelMusic/BatGetChannelPlayingMusicInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelMusicServer).BatGetChannelPlayingMusicInfo(ctx, req.(*BatGetChannelPlayingMusicInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelMusic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelmusic.ChannelMusic",
	HandlerType: (*ChannelMusicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddChannelMusic",
			Handler:    _ChannelMusic_AddChannelMusic_Handler,
		},
		{
			MethodName: "RemoveChannelMusic",
			Handler:    _ChannelMusic_RemoveChannelMusic_Handler,
		},
		{
			MethodName: "ChannelMusicCtrl",
			Handler:    _ChannelMusic_ChannelMusicCtrl_Handler,
		},
		{
			MethodName: "ChannelMusicHeartBeat",
			Handler:    _ChannelMusic_ChannelMusicHeartBeat_Handler,
		},
		{
			MethodName: "SetChannelMusicPlayMode",
			Handler:    _ChannelMusic_SetChannelMusicPlayMode_Handler,
		},
		{
			MethodName: "SetChannelMusicVolume",
			Handler:    _ChannelMusic_SetChannelMusicVolume_Handler,
		},
		{
			MethodName: "GetChannelMusicList",
			Handler:    _ChannelMusic_GetChannelMusicList_Handler,
		},
		{
			MethodName: "SetChannelMusicNext",
			Handler:    _ChannelMusic_SetChannelMusicNext_Handler,
		},
		{
			MethodName: "SetChannelMusicCanShare",
			Handler:    _ChannelMusic_SetChannelMusicCanShare_Handler,
		},
		{
			MethodName: "GetChannelMusicStatus",
			Handler:    _ChannelMusic_GetChannelMusicStatus_Handler,
		},
		{
			MethodName: "SetChannelMusicCurrent",
			Handler:    _ChannelMusic_SetChannelMusicCurrent_Handler,
		},
		{
			MethodName: "RemoveChannelMusicByUid",
			Handler:    _ChannelMusic_RemoveChannelMusicByUid_Handler,
		},
		{
			MethodName: "SetChannelMusicFreeMode",
			Handler:    _ChannelMusic_SetChannelMusicFreeMode_Handler,
		},
		{
			MethodName: "GetPlayingMusicChannelList",
			Handler:    _ChannelMusic_GetPlayingMusicChannelList_Handler,
		},
		{
			MethodName: "RemoveChannelReportedMusic",
			Handler:    _ChannelMusic_RemoveChannelReportedMusic_Handler,
		},
		{
			MethodName: "GetReportedMusicList",
			Handler:    _ChannelMusic_GetReportedMusicList_Handler,
		},
		{
			MethodName: "BatGetChannelPlayingMusicInfo",
			Handler:    _ChannelMusic_BatGetChannelPlayingMusicInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelmusicsvr/channelmusic.proto",
}

func (m *MusicInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MusicInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.ClientKey)))
	i += copy(dAtA[i:], m.ClientKey)
	dAtA[i] = 0x12
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.Author)))
	i += copy(dAtA[i:], m.Author)
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Key))
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.IsLocal))
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x50
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicType))
	dAtA[i] = 0x58
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.StartTime))
	return i, nil
}

func (m *AddChannelMusicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelMusicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	if len(m.MusicInfoList) > 0 {
		for _, msg := range m.MusicInfoList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddChannelMusicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddChannelMusicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MusicAddedList) > 0 {
		for _, msg := range m.MusicAddedList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MaxMusicCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.CurrentMusicCount))
	return i, nil
}

func (m *RemoveChannelMusicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelMusicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	if len(m.MusicIdList) > 0 {
		for _, num := range m.MusicIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x20
	i++
	if m.RemoveSelf {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *RemoveChannelMusicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelMusicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MusicRemovedList) > 0 {
		for _, num := range m.MusicRemovedList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *RemoveChannelMusicByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelMusicByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *RemoveChannelMusicByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelMusicByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *RemoveChannelReportedMusicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelReportedMusicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.MusicName)))
	i += copy(dAtA[i:], m.MusicName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.Author)))
	i += copy(dAtA[i:], m.Author)
	return i, nil
}

func (m *RemoveChannelReportedMusicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RemoveChannelReportedMusicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicId))
	return i, nil
}

func (m *GetReportedMusicListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReportedMusicListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetReportedMusicListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReportedMusicListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ReportedMusicList) > 0 {
		for _, s := range m.ReportedMusicList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *GetChannelMusicListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMusicListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelMusicListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMusicListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MusicList) > 0 {
		for _, msg := range m.MusicList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ChannelMusicCtrlReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicCtrlReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelCtrl))
	return i, nil
}

func (m *ChannelMusicCtrlResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicCtrlResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.NextMusic != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelmusic(dAtA, i, uint64(m.NextMusic.Size()))
		n1, err := m.NextMusic.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *ChannelMusicHeartBeatReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicHeartBeatReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicKey))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Percent))
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ClientEvent))
	return i, nil
}

func (m *ChannelMusicHeartBeatResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicHeartBeatResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	return i, nil
}

func (m *SetChannelMusicPlayModeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicPlayModeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.PlayMode))
	return i, nil
}

func (m *SetChannelMusicPlayModeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicPlayModeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetChannelMusicVolumeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicVolumeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	return i, nil
}

func (m *SetChannelMusicVolumeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicVolumeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetChannelMusicCanShareReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicCanShareReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.CanShare {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetChannelMusicCanShareResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicCanShareResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetChannelMusicFreeModeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicFreeModeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	if m.FreeMode {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetChannelMusicFreeModeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicFreeModeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetChannelMusicNextReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicNextReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicId))
	return i, nil
}

func (m *SetChannelMusicNextResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicNextResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetChannelMusicStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMusicStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetChannelMusicStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetChannelMusicStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.PlayMode))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	dAtA[i] = 0x18
	i++
	if m.CanShare {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x20
	i++
	if m.IsPlaying {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.CurrentPlaying))
	dAtA[i] = 0x30
	i++
	if m.IsFreeMode {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x3a
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.MusicName)))
	i += copy(dAtA[i:], m.MusicName)
	dAtA[i] = 0x42
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.MusicAuthor)))
	i += copy(dAtA[i:], m.MusicAuthor)
	return i, nil
}

func (m *SetChannelMusicCurrentReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicCurrentReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicId))
	return i, nil
}

func (m *SetChannelMusicCurrentResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelMusicCurrentResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *ChannelMusicListChangedNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicListChangedNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChangeType))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChangeValue))
	return i, nil
}

func (m *ChannelMusicVolumeChangedNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicVolumeChangedNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	return i, nil
}

func (m *ChannelMusicPlayTheMusic) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicPlayTheMusic) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Volume))
	dAtA[i] = 0x28
	i++
	if m.Start {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Percent))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.MusicName)))
	i += copy(dAtA[i:], m.MusicName)
	dAtA[i] = 0x40
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicChangedType))
	dAtA[i] = 0x48
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.SwitcherUid))
	return i, nil
}

func (m *ChannelMusicCanShareNotify) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelMusicCanShareNotify) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	if m.CanShare {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *GetPlayingMusicChannelListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPlayingMusicChannelListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetPlayingMusicChannelListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetPlayingMusicChannelListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *PlayingMusicInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PlayingMusicInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(m.MusicId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.MusicName)))
	i += copy(dAtA[i:], m.MusicName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintChannelmusic(dAtA, i, uint64(len(m.MusicAuthor)))
	i += copy(dAtA[i:], m.MusicAuthor)
	return i, nil
}

func (m *BatGetChannelPlayingMusicInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetChannelPlayingMusicInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, num := range m.ChannelIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatGetChannelPlayingMusicInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatGetChannelPlayingMusicInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MusicList) > 0 {
		for _, msg := range m.MusicList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChannelmusic(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func encodeFixed64Channelmusic(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelmusic(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelmusic(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *MusicInfo) Size() (n int) {
	var l int
	_ = l
	l = len(m.ClientKey)
	n += 1 + l + sovChannelmusic(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovChannelmusic(uint64(l))
	l = len(m.Author)
	n += 1 + l + sovChannelmusic(uint64(l))
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	n += 1 + sovChannelmusic(uint64(m.Key))
	n += 1 + sovChannelmusic(uint64(m.IsLocal))
	n += 1 + sovChannelmusic(uint64(m.Status))
	n += 1 + sovChannelmusic(uint64(m.MusicType))
	n += 1 + sovChannelmusic(uint64(m.StartTime))
	return n
}

func (m *AddChannelMusicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	if len(m.MusicInfoList) > 0 {
		for _, e := range m.MusicInfoList {
			l = e.Size()
			n += 1 + l + sovChannelmusic(uint64(l))
		}
	}
	return n
}

func (m *AddChannelMusicResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MusicAddedList) > 0 {
		for _, e := range m.MusicAddedList {
			l = e.Size()
			n += 1 + l + sovChannelmusic(uint64(l))
		}
	}
	n += 1 + sovChannelmusic(uint64(m.MaxMusicCount))
	n += 1 + sovChannelmusic(uint64(m.CurrentMusicCount))
	return n
}

func (m *RemoveChannelMusicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	if len(m.MusicIdList) > 0 {
		for _, e := range m.MusicIdList {
			n += 1 + sovChannelmusic(uint64(e))
		}
	}
	n += 2
	return n
}

func (m *RemoveChannelMusicResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MusicRemovedList) > 0 {
		for _, e := range m.MusicRemovedList {
			n += 1 + sovChannelmusic(uint64(e))
		}
	}
	return n
}

func (m *RemoveChannelMusicByUidReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	return n
}

func (m *RemoveChannelMusicByUidResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *RemoveChannelReportedMusicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	l = len(m.MusicName)
	n += 1 + l + sovChannelmusic(uint64(l))
	l = len(m.Author)
	n += 1 + l + sovChannelmusic(uint64(l))
	return n
}

func (m *RemoveChannelReportedMusicResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.MusicId))
	return n
}

func (m *GetReportedMusicListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	return n
}

func (m *GetReportedMusicListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ReportedMusicList) > 0 {
		for _, s := range m.ReportedMusicList {
			l = len(s)
			n += 1 + l + sovChannelmusic(uint64(l))
		}
	}
	return n
}

func (m *GetChannelMusicListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	return n
}

func (m *GetChannelMusicListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MusicList) > 0 {
		for _, e := range m.MusicList {
			l = e.Size()
			n += 1 + l + sovChannelmusic(uint64(l))
		}
	}
	return n
}

func (m *ChannelMusicCtrlReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.ChannelCtrl))
	return n
}

func (m *ChannelMusicCtrlResp) Size() (n int) {
	var l int
	_ = l
	if m.NextMusic != nil {
		l = m.NextMusic.Size()
		n += 1 + l + sovChannelmusic(uint64(l))
	}
	return n
}

func (m *ChannelMusicHeartBeatReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.MusicKey))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	n += 1 + sovChannelmusic(uint64(m.Percent))
	n += 1 + sovChannelmusic(uint64(m.ClientEvent))
	return n
}

func (m *ChannelMusicHeartBeatResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.MusicId))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	return n
}

func (m *SetChannelMusicPlayModeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.PlayMode))
	return n
}

func (m *SetChannelMusicPlayModeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetChannelMusicVolumeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	return n
}

func (m *SetChannelMusicVolumeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetChannelMusicCanShareReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *SetChannelMusicCanShareResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetChannelMusicFreeModeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *SetChannelMusicFreeModeResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetChannelMusicNextReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.MusicId))
	return n
}

func (m *SetChannelMusicNextResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetChannelMusicStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	return n
}

func (m *GetChannelMusicStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.PlayMode))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	n += 2
	n += 2
	n += 1 + sovChannelmusic(uint64(m.CurrentPlaying))
	n += 2
	l = len(m.MusicName)
	n += 1 + l + sovChannelmusic(uint64(l))
	l = len(m.MusicAuthor)
	n += 1 + l + sovChannelmusic(uint64(l))
	return n
}

func (m *SetChannelMusicCurrentReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.MusicId))
	return n
}

func (m *SetChannelMusicCurrentResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *ChannelMusicListChangedNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.ChangeType))
	n += 1 + sovChannelmusic(uint64(m.ChangeValue))
	return n
}

func (m *ChannelMusicVolumeChangedNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	return n
}

func (m *ChannelMusicPlayTheMusic) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.Uid))
	n += 1 + sovChannelmusic(uint64(m.MusicId))
	n += 1 + sovChannelmusic(uint64(m.Volume))
	n += 2
	n += 1 + sovChannelmusic(uint64(m.Percent))
	l = len(m.MusicName)
	n += 1 + l + sovChannelmusic(uint64(l))
	n += 1 + sovChannelmusic(uint64(m.MusicChangedType))
	n += 1 + sovChannelmusic(uint64(m.SwitcherUid))
	return n
}

func (m *ChannelMusicCanShareNotify) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 2
	return n
}

func (m *GetPlayingMusicChannelListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.Uid))
	return n
}

func (m *GetPlayingMusicChannelListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelmusic(uint64(e))
		}
	}
	return n
}

func (m *PlayingMusicInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelmusic(uint64(m.ChannelId))
	n += 1 + sovChannelmusic(uint64(m.MusicId))
	l = len(m.MusicName)
	n += 1 + l + sovChannelmusic(uint64(l))
	l = len(m.MusicAuthor)
	n += 1 + l + sovChannelmusic(uint64(l))
	return n
}

func (m *BatGetChannelPlayingMusicInfoReq) Size() (n int) {
	var l int
	_ = l
	if len(m.ChannelIdList) > 0 {
		for _, e := range m.ChannelIdList {
			n += 1 + sovChannelmusic(uint64(e))
		}
	}
	return n
}

func (m *BatGetChannelPlayingMusicInfoResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MusicList) > 0 {
		for _, e := range m.MusicList {
			l = e.Size()
			n += 1 + l + sovChannelmusic(uint64(l))
		}
	}
	return n
}

func sovChannelmusic(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelmusic(x uint64) (n int) {
	return sovChannelmusic(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *MusicInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MusicInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MusicInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ClientKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Author", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Author = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			m.Key = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Key |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsLocal", wireType)
			}
			m.IsLocal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsLocal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicType", wireType)
			}
			m.MusicType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("client_key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("author")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelMusicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelMusicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelMusicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicInfoList = append(m.MusicInfoList, &MusicInfo{})
			if err := m.MusicInfoList[len(m.MusicInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddChannelMusicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddChannelMusicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddChannelMusicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicAddedList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicAddedList = append(m.MusicAddedList, &MusicInfo{})
			if err := m.MusicAddedList[len(m.MusicAddedList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxMusicCount", wireType)
			}
			m.MaxMusicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxMusicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentMusicCount", wireType)
			}
			m.CurrentMusicCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentMusicCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelMusicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelMusicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelMusicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (int64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MusicIdList = append(m.MusicIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmusic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmusic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (int64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MusicIdList = append(m.MusicIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicIdList", wireType)
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RemoveSelf", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.RemoveSelf = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelMusicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelMusicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelMusicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v int64
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (int64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MusicRemovedList = append(m.MusicRemovedList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmusic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v int64
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmusic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (int64(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MusicRemovedList = append(m.MusicRemovedList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicRemovedList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelMusicByUidReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelMusicByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelMusicByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelMusicByUidResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelMusicByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelMusicByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelReportedMusicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelReportedMusicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelReportedMusicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Author", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Author = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("music_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("author")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RemoveChannelReportedMusicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RemoveChannelReportedMusicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RemoveChannelReportedMusicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			m.MusicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("music_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReportedMusicListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReportedMusicListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReportedMusicListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReportedMusicListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReportedMusicListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReportedMusicListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ReportedMusicList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ReportedMusicList = append(m.ReportedMusicList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMusicListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMusicListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMusicListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMusicListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMusicListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMusicListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicList = append(m.MusicList, &MusicInfo{})
			if err := m.MusicList[len(m.MusicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicCtrlReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicCtrlReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicCtrlReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelCtrl", wireType)
			}
			m.ChannelCtrl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelCtrl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_ctrl")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicCtrlResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicCtrlResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicCtrlResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NextMusic", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NextMusic == nil {
				m.NextMusic = &MusicInfo{}
			}
			if err := m.NextMusic.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicHeartBeatReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicHeartBeatReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicHeartBeatReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicKey", wireType)
			}
			m.MusicKey = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicKey |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Percent", wireType)
			}
			m.Percent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Percent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientEvent", wireType)
			}
			m.ClientEvent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientEvent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("music_key")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("volume")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("percent")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicHeartBeatResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicHeartBeatResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicHeartBeatResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			m.MusicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicPlayModeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicPlayModeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicPlayModeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlayMode", wireType)
			}
			m.PlayMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlayMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("play_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicPlayModeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicPlayModeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicPlayModeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicVolumeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicVolumeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicVolumeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("volume")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicVolumeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicVolumeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicVolumeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicCanShareReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicCanShareReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicCanShareReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanShare", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanShare = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("can_share")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicCanShareResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicCanShareResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicCanShareResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicFreeModeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicFreeModeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicFreeModeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FreeMode", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.FreeMode = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("free_mode")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicFreeModeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicFreeModeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicFreeModeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicNextReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicNextReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicNextReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			m.MusicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("music_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicNextResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicNextResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicNextResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMusicStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMusicStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMusicStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetChannelMusicStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetChannelMusicStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetChannelMusicStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PlayMode", wireType)
			}
			m.PlayMode = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PlayMode |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanShare", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanShare = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsPlaying", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsPlaying = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrentPlaying", wireType)
			}
			m.CurrentPlaying = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrentPlaying |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsFreeMode", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsFreeMode = bool(v != 0)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicAuthor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicAuthor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("play_mode")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("volume")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("can_share")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_playing")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicCurrentReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicCurrentReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicCurrentReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			m.MusicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("music_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelMusicCurrentResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelMusicCurrentResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelMusicCurrentResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicListChangedNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicListChangedNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicListChangedNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeType", wireType)
			}
			m.ChangeType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChangeValue", wireType)
			}
			m.ChangeValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChangeValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("change_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicVolumeChangedNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicVolumeChangedNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicVolumeChangedNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("volume")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicPlayTheMusic) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicPlayTheMusic: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicPlayTheMusic: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			m.MusicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Volume", wireType)
			}
			m.Volume = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Volume |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Start", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Start = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Percent", wireType)
			}
			m.Percent = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Percent |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicChangedType", wireType)
			}
			m.MusicChangedType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicChangedType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SwitcherUid", wireType)
			}
			m.SwitcherUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SwitcherUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("music_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("volume")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("start")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("percent")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelMusicCanShareNotify) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelMusicCanShareNotify: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelMusicCanShareNotify: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CanShare", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.CanShare = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("can_share")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPlayingMusicChannelListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPlayingMusicChannelListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPlayingMusicChannelListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetPlayingMusicChannelListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetPlayingMusicChannelListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetPlayingMusicChannelListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmusic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmusic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PlayingMusicInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PlayingMusicInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PlayingMusicInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicId", wireType)
			}
			m.MusicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MusicId |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicAuthor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicAuthor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetChannelPlayingMusicInfoReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetChannelPlayingMusicInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetChannelPlayingMusicInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.ChannelIdList = append(m.ChannelIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChannelmusic
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChannelmusic
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.ChannelIdList = append(m.ChannelIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatGetChannelPlayingMusicInfoResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatGetChannelPlayingMusicInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatGetChannelPlayingMusicInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MusicList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelmusic
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MusicList = append(m.MusicList, &PlayingMusicInfo{})
			if err := m.MusicList[len(m.MusicList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChannelmusic(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelmusic
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelmusic(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelmusic
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelmusic
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelmusic
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelmusic
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelmusic(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelmusic = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelmusic   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/channelmusicsvr/channelmusic.proto", fileDescriptorChannelmusic) }

var fileDescriptorChannelmusic = []byte{
	// 2080 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x59, 0xcf, 0x6f, 0x1c, 0x49,
	0xf5, 0xdf, 0x9a, 0xf1, 0xcf, 0x67, 0x3b, 0x76, 0xca, 0x6b, 0xbb, 0x3d, 0xbb, 0x76, 0x3a, 0x9d,
	0xc4, 0xf1, 0x77, 0xbf, 0x99, 0x84, 0x98, 0x85, 0x85, 0xd1, 0xec, 0x20, 0xdb, 0xeb, 0x38, 0xd9,
	0xb5, 0x4d, 0x18, 0x27, 0x8b, 0xf6, 0xd4, 0xea, 0x9d, 0xae, 0x89, 0x5b, 0xee, 0x99, 0x29, 0xba,
	0x7a, 0x8c, 0x47, 0x70, 0xd8, 0x23, 0xe2, 0x80, 0x50, 0xb4, 0x12, 0x12, 0x42, 0x42, 0x48, 0x01,
	0x09, 0x24, 0x2e, 0x5c, 0xb8, 0x20, 0xc1, 0x71, 0xb9, 0xc0, 0x1e, 0x38, 0x23, 0x08, 0x97, 0x9c,
	0xf8, 0x1b, 0x50, 0x55, 0x75, 0x4f, 0x57, 0xff, 0x9a, 0x99, 0x5d, 0xfb, 0x36, 0x5d, 0xf5, 0xaa,
	0xea, 0xf3, 0x3e, 0xef, 0xd5, 0xfb, 0x51, 0x03, 0x1b, 0xcc, 0x6b, 0xdc, 0x6b, 0x9c, 0x58, 0xed,
	0x36, 0x71, 0x5b, 0x5d, 0xe6, 0x34, 0xd8, 0x99, 0x17, 0xfb, 0xbe, 0x4b, 0xbd, 0x8e, 0xdf, 0xc1,
	0xb3, 0xea, 0x58, 0xe9, 0x66, 0xa3, 0xd3, 0x6a, 0x75, 0xda, 0xf7, 0x7c, 0xf7, 0x8c, 0x3a, 0x8d,
	0x53, 0x97, 0xdc, 0x63, 0xa7, 0x1f, 0x77, 0x1d, 0xd7, 0x77, 0xda, 0x7e, 0x8f, 0x12, 0xb9, 0xc6,
	0xf8, 0x73, 0x01, 0xa6, 0x0f, 0xb9, 0xfc, 0xa3, 0x76, 0xb3, 0x83, 0x6f, 0x00, 0x34, 0x5c, 0x87,
	0xb4, 0x7d, 0xf3, 0x94, 0xf4, 0x34, 0xa4, 0x17, 0x36, 0xa7, 0x77, 0xc6, 0x3e, 0xfb, 0xe7, 0xb5,
	0xd7, 0xea, 0xd3, 0x72, 0xfc, 0x03, 0xd2, 0xc3, 0x1a, 0x8c, 0xb5, 0xad, 0x16, 0xd1, 0x0a, 0xca,
	0xb4, 0x18, 0xc1, 0x6f, 0xc2, 0x84, 0xd5, 0xf5, 0x4f, 0x3a, 0x9e, 0x56, 0x54, 0xe6, 0x82, 0x31,
	0xbc, 0x0c, 0xc5, 0xae, 0x63, 0x6b, 0x63, 0x7a, 0x61, 0x73, 0x2e, 0x98, 0xe2, 0x03, 0x7c, 0xd5,
	0x59, 0xc7, 0xed, 0xb6, 0x88, 0x36, 0xae, 0xa3, 0xfe, 0x54, 0x30, 0xc6, 0x57, 0x71, 0x2c, 0x13,
	0x3a, 0xda, 0x2c, 0x86, 0xab, 0x4e, 0x49, 0x0f, 0x5f, 0x83, 0x29, 0x87, 0x99, 0x6e, 0xa7, 0x61,
	0xb9, 0xda, 0x94, 0xb2, 0x6e, 0xd2, 0x61, 0x07, 0x7c, 0x90, 0x6f, 0xcb, 0x7c, 0xcb, 0xef, 0x32,
	0x6d, 0x5a, 0xdd, 0x56, 0x8e, 0x71, 0x4d, 0x05, 0x4d, 0x26, 0xe7, 0x42, 0x03, 0x45, 0x62, 0x5a,
	0x8c, 0x3f, 0xe9, 0x51, 0xc2, 0x85, 0x98, 0x6f, 0x79, 0xbe, 0xe9, 0x3b, 0x2d, 0xa2, 0xcd, 0xa8,
	0x42, 0x62, 0xfc, 0x89, 0xd3, 0x22, 0xc6, 0x73, 0x04, 0x78, 0xdb, 0xb6, 0x77, 0x25, 0xf7, 0x82,
	0xcb, 0x3a, 0xf9, 0x5e, 0xa8, 0x2d, 0x4a, 0x6a, 0xcb, 0x29, 0x96, 0xa2, 0xa6, 0x63, 0x0b, 0x0e,
	0xfb, 0x7b, 0x06, 0xe3, 0x8f, 0x6c, 0xfc, 0x2d, 0x98, 0x97, 0xe8, 0x9c, 0x76, 0xb3, 0x63, 0xba,
	0x0e, 0xf3, 0xb5, 0xa2, 0x5e, 0xdc, 0x9c, 0xd9, 0x5a, 0xb9, 0x1b, 0xb3, 0x7b, 0xdf, 0x72, 0xf5,
	0xb9, 0x56, 0xf8, 0xf3, 0xc0, 0x61, 0xbe, 0xf1, 0x27, 0x04, 0x8b, 0x29, 0x50, 0x8c, 0xe2, 0x6d,
	0x58, 0x90, 0x1b, 0x5b, 0xb6, 0x4d, 0x6c, 0xb9, 0x33, 0x1a, 0xbc, 0xf3, 0x15, 0x31, 0xb0, 0xcd,
	0xe5, 0xf9, 0xd6, 0xf8, 0x0e, 0xcc, 0xb7, 0xac, 0x73, 0x53, 0x6e, 0xd3, 0xe8, 0x74, 0xdb, 0xbe,
	0x56, 0x50, 0x98, 0x99, 0x6b, 0x59, 0xe7, 0x62, 0xf1, 0x2e, 0x9f, 0xc2, 0x6f, 0xc3, 0x62, 0xa3,
	0xeb, 0x79, 0xdc, 0xa5, 0xd4, 0x15, 0x45, 0x65, 0xc5, 0xd5, 0x40, 0x20, 0x5a, 0x65, 0xfc, 0x12,
	0xc1, 0x52, 0x9d, 0xb4, 0x3a, 0x67, 0xe4, 0x52, 0x69, 0x35, 0x60, 0x2e, 0xa0, 0xd5, 0x8e, 0x48,
	0x2d, 0xd6, 0x67, 0x24, 0x77, 0x52, 0xbd, 0x5b, 0x30, 0xe3, 0x89, 0x93, 0x4d, 0x46, 0xdc, 0xa6,
	0x36, 0xa6, 0xa3, 0xcd, 0xa9, 0x60, 0x27, 0x90, 0x13, 0xc7, 0xc4, 0x6d, 0x1a, 0x0f, 0x60, 0x39,
	0x0b, 0x20, 0xa3, 0xf8, 0x0e, 0x60, 0x79, 0x88, 0x94, 0x56, 0x48, 0x2e, 0xd6, 0x25, 0xf9, 0x72,
	0xa1, 0x38, 0xce, 0xf8, 0x08, 0x4a, 0xe9, 0x7d, 0x76, 0x7a, 0x4f, 0x1d, 0xfb, 0xa2, 0xda, 0x1a,
	0x6b, 0xf0, 0x46, 0xee, 0xd6, 0x8c, 0x72, 0x8e, 0xd7, 0x62, 0xf3, 0x75, 0x42, 0x3b, 0x9e, 0x4f,
	0xec, 0xcb, 0xe1, 0xba, 0x7f, 0xc1, 0x44, 0xac, 0x50, 0xe3, 0x81, 0xbc, 0x60, 0x47, 0xf1, 0x80,
	0x31, 0x96, 0x0e, 0x18, 0xc6, 0x36, 0xac, 0x0f, 0x02, 0xc8, 0x28, 0x0f, 0x02, 0xa1, 0x41, 0x05,
	0xcc, 0x30, 0x42, 0x4c, 0x06, 0x16, 0x35, 0xee, 0xc3, 0xca, 0x3e, 0xf1, 0x63, 0x0b, 0x39, 0xed,
	0x03, 0xb4, 0x33, 0xde, 0x07, 0x2d, 0x7b, 0x09, 0xa3, 0xf8, 0x2e, 0x2c, 0x7a, 0xc1, 0x44, 0xe0,
	0xce, 0x7d, 0xe3, 0x4e, 0xd7, 0xaf, 0x7a, 0xc9, 0x35, 0xc6, 0x53, 0x58, 0xde, 0x27, 0xbe, 0xca,
	0xff, 0x90, 0xd3, 0x47, 0xb3, 0xec, 0x77, 0x84, 0x56, 0xe9, 0x6d, 0x19, 0xc5, 0x5f, 0x0f, 0x69,
	0x1f, 0xe5, 0x6a, 0x4b, 0x4b, 0x08, 0xa4, 0x3f, 0x80, 0x45, 0x75, 0xbf, 0x5d, 0xdf, 0x73, 0x2f,
	0xec, 0x02, 0xb7, 0x21, 0xcc, 0x48, 0x66, 0xc3, 0xf7, 0x5c, 0xe1, 0x04, 0xa1, 0xd8, 0x4c, 0x30,
	0xc3, 0x0f, 0x32, 0x8e, 0xe0, 0xf5, 0xf4, 0xe1, 0x52, 0x99, 0x36, 0x39, 0x0f, 0x22, 0x87, 0x86,
	0x74, 0x34, 0x50, 0x19, 0x2e, 0x2a, 0x3e, 0x8d, 0x7f, 0x23, 0xd0, 0xd4, 0x0d, 0x1f, 0x12, 0xcb,
	0xf3, 0x77, 0x88, 0x75, 0x61, 0xe6, 0xf1, 0x75, 0x90, 0x9c, 0x89, 0xfc, 0x58, 0x54, 0x3c, 0x4e,
	0xfa, 0x21, 0x4f, 0x8f, 0x51, 0x3a, 0x53, 0x33, 0x5d, 0x98, 0xce, 0xd6, 0x61, 0x92, 0x12, 0xaf,
	0x41, 0xda, 0xbe, 0x36, 0xae, 0x4c, 0x87, 0x83, 0x82, 0x33, 0x99, 0x81, 0xc9, 0x19, 0x17, 0x9a,
	0x50, 0x02, 0xe5, 0x8c, 0x9c, 0xd9, 0xe3, 0x13, 0xc6, 0xcf, 0x10, 0xac, 0xe6, 0xe8, 0xc8, 0xe8,
	0xc5, 0x94, 0x54, 0x6f, 0x55, 0x51, 0xc9, 0xbb, 0xe1, 0xad, 0x8a, 0xa9, 0x98, 0xca, 0xd8, 0xc6,
	0x0f, 0xa1, 0x74, 0x1c, 0xf7, 0xce, 0xc7, 0xae, 0xd5, 0x3b, 0xec, 0xd8, 0xe4, 0x32, 0xe8, 0xa7,
	0xae, 0xd5, 0x33, 0x5b, 0x1d, 0x9b, 0xc4, 0xdc, 0x69, 0x8a, 0x06, 0x47, 0xf0, 0xa8, 0x97, 0x7b,
	0x3a, 0xa3, 0x46, 0x17, 0xb4, 0xc4, 0xf4, 0x87, 0x02, 0xf5, 0x85, 0xa1, 0x45, 0x9c, 0x14, 0xd3,
	0x66, 0x37, 0xde, 0x80, 0xd5, 0x9c, 0x63, 0x19, 0xcd, 0x20, 0x6c, 0xd7, 0x6a, 0x1f, 0x9f, 0x58,
	0xde, 0xa5, 0x10, 0xd6, 0xb0, 0xda, 0x26, 0xe3, 0x9b, 0x09, 0x60, 0x61, 0x2e, 0x9b, 0x6a, 0x04,
	0x47, 0x64, 0x10, 0x16, 0x9d, 0x9e, 0x09, 0xee, 0x81, 0x47, 0xc8, 0x65, 0x59, 0xb3, 0xe9, 0x11,
	0x12, 0x59, 0xb3, 0x0f, 0xae, 0x19, 0x1c, 0x91, 0x01, 0x2e, 0x3a, 0x9d, 0x51, 0xe3, 0x0c, 0x96,
	0x13, 0xd3, 0x47, 0xe4, 0xfc, 0xe2, 0xb7, 0x3c, 0x7e, 0x01, 0x32, 0xd2, 0xca, 0x2a, 0xac, 0x64,
	0x9e, 0xcb, 0xa8, 0xf1, 0x5d, 0x91, 0x3e, 0xd4, 0xa9, 0x63, 0x51, 0x71, 0x5e, 0x38, 0xe8, 0xff,
	0xb5, 0x00, 0xab, 0x39, 0x3b, 0x33, 0x1a, 0xbf, 0x19, 0x28, 0xeb, 0x66, 0x28, 0x1e, 0x5a, 0xc8,
	0x08, 0x4c, 0xc3, 0x3d, 0x85, 0xc3, 0x74, 0x98, 0xc9, 0xf7, 0x73, 0xda, 0xcf, 0x44, 0x74, 0x0b,
	0x65, 0xa6, 0x1d, 0xf6, 0x58, 0x0e, 0xe3, 0x32, 0xcc, 0x87, 0x05, 0x5f, 0x28, 0x39, 0xae, 0xc4,
	0x90, 0x2b, 0xc1, 0x64, 0x28, 0xbe, 0x01, 0xb3, 0x0e, 0x33, 0x23, 0x37, 0x98, 0x50, 0xeb, 0x2d,
	0x87, 0x85, 0xd6, 0x4e, 0x94, 0x13, 0x93, 0x3a, 0xca, 0x2a, 0x27, 0x6e, 0xc3, 0x6c, 0x50, 0xdd,
	0xca, 0xa2, 0x62, 0x4a, 0x11, 0x93, 0x45, 0xde, 0xb6, 0xac, 0x2c, 0x7a, 0xa9, 0xeb, 0xb8, 0x2b,
	0x61, 0x5d, 0xba, 0xeb, 0xcc, 0x25, 0x5d, 0xe7, 0xcd, 0xf4, 0x65, 0x0f, 0x8f, 0x66, 0xd4, 0xf8,
	0x14, 0xc1, 0x5a, 0x32, 0xaf, 0xf3, 0xef, 0x67, 0xc4, 0x3e, 0xea, 0xf8, 0x4e, 0xb3, 0x97, 0x40,
	0x81, 0xb2, 0x51, 0xdc, 0x02, 0x91, 0x5f, 0x9f, 0x11, 0xd9, 0xde, 0xa8, 0x58, 0x41, 0x4e, 0x88,
	0xfe, 0x26, 0x48, 0xd0, 0xcf, 0x88, 0x79, 0x66, 0xb9, 0x5d, 0x12, 0xab, 0xca, 0x83, 0x0d, 0x3e,
	0xe4, 0x13, 0x86, 0x0d, 0xd7, 0xd2, 0xb1, 0xeb, 0x4b, 0xe0, 0x1a, 0xe8, 0x82, 0xc6, 0x3f, 0x0a,
	0xf1, 0xb4, 0xcd, 0x7d, 0xe4, 0xc9, 0x09, 0x11, 0xbf, 0x47, 0xdb, 0x3f, 0x30, 0x5d, 0x21, 0x69,
	0xba, 0x61, 0x17, 0x7a, 0x48, 0xd2, 0x2e, 0xc1, 0xb8, 0xe8, 0xf7, 0x44, 0xca, 0x0e, 0xbd, 0x53,
	0x0e, 0xa9, 0x09, 0x7d, 0x22, 0x2b, 0xa1, 0x8f, 0xe4, 0xb8, 0x5b, 0x61, 0xcf, 0x20, 0x49, 0xb7,
	0xa5, 0xd9, 0xd4, 0xb6, 0x56, 0x76, 0x0e, 0x01, 0xe5, 0xa1, 0xf1, 0xd8, 0xf7, 0x1d, 0xbf, 0x71,
	0x42, 0x3c, 0x93, 0x2b, 0xad, 0x76, 0xb9, 0x33, 0xe1, 0xcc, 0x53, 0xc7, 0x36, 0x6c, 0x28, 0x65,
	0x45, 0xf7, 0x2f, 0x62, 0xb7, 0x58, 0x70, 0x28, 0x64, 0xa6, 0x91, 0x77, 0x60, 0x6d, 0x9f, 0x84,
	0xd7, 0xfa, 0x30, 0x04, 0xdb, 0x26, 0xee, 0xb0, 0x7a, 0xfb, 0x21, 0xac, 0x0f, 0x5a, 0xc8, 0x28,
	0xde, 0x80, 0xf9, 0x08, 0x62, 0x54, 0xd8, 0xce, 0xd5, 0xe7, 0xfa, 0x08, 0x45, 0x0d, 0xfb, 0x1b,
	0x04, 0x0b, 0xea, 0x3e, 0xfd, 0x27, 0x0d, 0x55, 0x3f, 0x34, 0xec, 0xd6, 0x16, 0xb2, 0x2a, 0x9e,
	0x64, 0x37, 0x33, 0x52, 0xf8, 0x19, 0xcb, 0x0b, 0x3f, 0xef, 0x83, 0xbe, 0x63, 0xf9, 0x51, 0x30,
	0x4f, 0x82, 0xe6, 0x74, 0x8d, 0xaa, 0xf4, 0xc7, 0x70, 0x7d, 0xc8, 0x5e, 0x8c, 0xe2, 0x77, 0x33,
	0xba, 0x82, 0xf5, 0x78, 0x21, 0x9d, 0x5a, 0x17, 0x35, 0x07, 0x6f, 0xd5, 0x01, 0x76, 0xa3, 0xa8,
	0xb1, 0x0c, 0xf8, 0xf0, 0xe9, 0xf1, 0xa3, 0x5d, 0xf3, 0xe0, 0xd1, 0xf1, 0x13, 0x73, 0xf7, 0xe1,
	0xf6, 0xd1, 0xfe, 0xde, 0x7b, 0x0b, 0x08, 0x2f, 0xc1, 0xd5, 0xc7, 0x07, 0xdb, 0x1f, 0x99, 0x87,
	0xdf, 0x7e, 0x6f, 0xaf, 0x3f, 0x5c, 0xe0, 0xc3, 0x0f, 0xea, 0x7b, 0x7b, 0xf1, 0xe1, 0xe2, 0xd6,
	0x7f, 0x97, 0x61, 0x56, 0x75, 0x4b, 0xfc, 0x47, 0x04, 0xf3, 0x89, 0x27, 0x0b, 0xac, 0xc7, 0x31,
	0xa6, 0x9f, 0x59, 0x4a, 0xd7, 0x87, 0x48, 0x30, 0x6a, 0x98, 0x9f, 0xbc, 0x78, 0x55, 0x44, 0x3f,
	0x7e, 0xf1, 0xaa, 0x08, 0xdd, 0x8a, 0x53, 0x39, 0xad, 0xb4, 0x2b, 0x56, 0xe5, 0xf9, 0x8b, 0x57,
	0xc5, 0x07, 0xe5, 0xae, 0x5e, 0xed, 0x3a, 0x76, 0x4d, 0x2f, 0x3b, 0xd5, 0x88, 0xec, 0x9a, 0x5e,
	0x3e, 0xad, 0x46, 0xcf, 0x60, 0x35, 0xbd, 0xdc, 0xae, 0x46, 0xc6, 0xaf, 0xe9, 0x65, 0xab, 0xaa,
	0x9a, 0xb9, 0x86, 0x7f, 0x8f, 0x00, 0xa7, 0x3b, 0x6d, 0x7c, 0x23, 0x0e, 0x2d, 0xf3, 0x3d, 0xa3,
	0x74, 0x73, 0xb8, 0x10, 0xa3, 0xc6, 0x01, 0x57, 0xa1, 0xc0, 0x55, 0x98, 0xe2, 0x2a, 0xb4, 0x2a,
	0x9e, 0x50, 0xe0, 0x6b, 0xb9, 0x0a, 0xb4, 0xaa, 0xa1, 0x3f, 0xd7, 0xf4, 0xb2, 0x57, 0x6d, 0x76,
	0xbc, 0x06, 0x09, 0x1e, 0x24, 0x6a, 0xf8, 0x0f, 0x08, 0x16, 0x92, 0xfd, 0x16, 0x4e, 0x10, 0x99,
	0xd1, 0x0c, 0x96, 0x8c, 0x61, 0x22, 0xbc, 0xfc, 0xe1, 0x48, 0x8b, 0x1c, 0xe9, 0x04, 0x47, 0xea,
	0x0b, 0x9c, 0x3b, 0xe5, 0x6e, 0x36, 0x4c, 0xbf, 0xca, 0x1b, 0x43, 0x11, 0xed, 0xf4, 0xfb, 0x65,
	0x11, 0x50, 0xf5, 0xad, 0x32, 0xf3, 0x3b, 0x54, 0xff, 0x6a, 0x99, 0xf7, 0x73, 0xfa, 0xdb, 0x65,
	0xea, 0x91, 0xb3, 0x9a, 0x8e, 0x3f, 0x47, 0xb0, 0x94, 0xd9, 0xef, 0xe0, 0x8d, 0x7c, 0x58, 0x6a,
	0xe3, 0x57, 0xba, 0x3d, 0x92, 0x1c, 0xa3, 0x46, 0x93, 0xeb, 0x30, 0xc6, 0x75, 0x98, 0x95, 0x6c,
	0xd3, 0x0a, 0xa9, 0x9c, 0x08, 0x4d, 0x3e, 0xc8, 0xd3, 0x24, 0x46, 0x38, 0xad, 0x06, 0xd1, 0xbf,
	0xa6, 0x97, 0x49, 0x55, 0x26, 0x91, 0x9a, 0x5e, 0x3e, 0xa9, 0xaa, 0xad, 0x5d, 0x0d, 0xff, 0x0e,
	0xa5, 0xca, 0xc8, 0xb0, 0x57, 0xc1, 0x9b, 0x71, 0xb0, 0xf9, 0x0d, 0x55, 0xe9, 0xff, 0x46, 0x94,
	0x64, 0xd4, 0xa8, 0x70, 0xc5, 0xc6, 0xfb, 0xc6, 0xa1, 0x42, 0xa5, 0x5b, 0x79, 0x2a, 0xd1, 0x6a,
	0xbf, 0xa2, 0xac, 0xe9, 0xf8, 0x57, 0x08, 0x96, 0x32, 0x5b, 0x98, 0x24, 0xff, 0x79, 0xed, 0x55,
	0x92, 0xff, 0xfc, 0x7e, 0xe8, 0x1d, 0x0e, 0x73, 0xa2, 0x0f, 0x93, 0x08, 0x98, 0x46, 0x1e, 0xcc,
	0x88, 0x60, 0xfc, 0x13, 0x04, 0x8b, 0x19, 0x0f, 0x23, 0x38, 0x71, 0xc9, 0xb2, 0x9f, 0x64, 0x4a,
	0xb7, 0x46, 0x90, 0x62, 0xd4, 0x78, 0x8b, 0xa3, 0x9b, 0xe4, 0xe8, 0xc6, 0x38, 0x3a, 0x8e, 0x6d,
	0x25, 0x07, 0x1b, 0xfe, 0x05, 0x82, 0xc5, 0x8c, 0x46, 0x21, 0x09, 0x28, 0xbb, 0x87, 0x49, 0x02,
	0xca, 0xeb, 0x38, 0xbe, 0xc1, 0x01, 0x4d, 0xf5, 0xe9, 0x6a, 0x09, 0x48, 0x37, 0x46, 0x70, 0x54,
	0xfc, 0xdb, 0xb4, 0x03, 0x86, 0xd5, 0xc1, 0x10, 0x07, 0x54, 0x1a, 0xd4, 0x21, 0x0e, 0x18, 0x6b,
	0x26, 0xbf, 0xc9, 0xa1, 0x4e, 0xf7, 0xa1, 0x32, 0x01, 0xf5, 0x66, 0x1e, 0x54, 0x56, 0xed, 0x17,
	0x1d, 0x35, 0xfc, 0x29, 0x82, 0xa5, 0xcc, 0xf6, 0x27, 0xe9, 0x7f, 0x79, 0xdd, 0x57, 0xd2, 0xff,
	0x72, 0x7b, 0x29, 0x69, 0x61, 0x18, 0xcd, 0xc2, 0xbf, 0x46, 0xa9, 0x16, 0x34, 0xa8, 0xe7, 0xf1,
	0x60, 0x7f, 0x8f, 0x1a, 0x8e, 0xd2, 0xe6, 0x68, 0x82, 0xa1, 0xa9, 0x67, 0xbe, 0x8c, 0xa9, 0x7f,
	0x8e, 0x60, 0x25, 0xe7, 0x35, 0x38, 0x69, 0xea, 0xfc, 0xf7, 0xe8, 0xa4, 0xa9, 0x07, 0x3d, 0x2f,
	0x0b, 0x12, 0x67, 0x93, 0x24, 0x66, 0xa6, 0x2b, 0x9e, 0x40, 0x57, 0x72, 0xda, 0xfc, 0x21, 0x7e,
	0xa8, 0xbc, 0x45, 0x0c, 0xf1, 0xc3, 0xd8, 0xbb, 0xc1, 0xbb, 0x1c, 0xdc, 0x5c, 0x9f, 0xc7, 0x73,
	0x01, 0x6f, 0x33, 0x37, 0x9b, 0x9e, 0x57, 0xd5, 0x1e, 0xb5, 0x86, 0x9f, 0x23, 0x28, 0xe5, 0xd7,
	0xac, 0xf8, 0xff, 0x53, 0x8e, 0x96, 0x5f, 0x16, 0x97, 0xee, 0x8c, 0x2e, 0xcc, 0xa8, 0xb1, 0xca,
	0x81, 0x5f, 0xe1, 0xc0, 0x0b, 0x5d, 0x01, 0x7a, 0x2a, 0x04, 0x8d, 0xff, 0x82, 0x12, 0x7f, 0x25,
	0xc4, 0x9e, 0xb0, 0x93, 0xa0, 0x06, 0xbe, 0xfc, 0x27, 0x41, 0x0d, 0x7e, 0x85, 0x37, 0xf6, 0x39,
	0xa8, 0xf9, 0x7e, 0x75, 0x12, 0x96, 0x57, 0x5f, 0xc9, 0xf3, 0xcb, 0x54, 0x39, 0x15, 0x16, 0x52,
	0x9f, 0x20, 0x78, 0x3d, 0xeb, 0xed, 0x1d, 0xa7, 0x43, 0x73, 0xd6, 0x93, 0x7e, 0x69, 0x63, 0x14,
	0xb1, 0x90, 0xc5, 0x85, 0x4c, 0x16, 0xff, 0x8e, 0x60, 0x6d, 0x60, 0x3d, 0x8d, 0xef, 0xc6, 0x0f,
	0x19, 0x56, 0xc8, 0x97, 0xee, 0x7d, 0x21, 0x79, 0x46, 0x8d, 0x43, 0x8e, 0xee, 0x6a, 0x70, 0x73,
	0xa4, 0x6b, 0x56, 0x22, 0xd7, 0x3c, 0xd7, 0xab, 0x89, 0xbe, 0x40, 0x77, 0x9d, 0x53, 0xa2, 0x47,
	0x83, 0xf7, 0x95, 0xdf, 0x5b, 0xb5, 0xd2, 0xc4, 0x8f, 0x5e, 0xbc, 0x2a, 0xfe, 0xad, 0xb7, 0xb3,
	0xf0, 0xd9, 0xcb, 0x75, 0xf4, 0xf9, 0xcb, 0x75, 0xf4, 0xaf, 0x97, 0xeb, 0xe8, 0xa7, 0xff, 0x59,
	0x7f, 0xed, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x43, 0xd2, 0xe7, 0x34, 0x58, 0x1e, 0x00, 0x00,
}
