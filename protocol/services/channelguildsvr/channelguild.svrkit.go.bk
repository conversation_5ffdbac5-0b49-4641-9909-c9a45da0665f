package channelguild

// Code generated by protoc-gen-svrkit-go. DO NOT EDIT.
// source: src/channelguildsvr/channelguild.proto

/*
 This is a generated svrkit golang dev toolkit.
*/

import svrkit "gitlab.ttyuyin.com/golang/svrkit"

import context "golang.org/x/net/context"

import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

var _ = context.TODO

// Magic Number for ChannelGuild service
const ChannelGuildMagic = uint16(15222)

// SvrkitClient API for ChannelGuild service

type ChannelGuildClientInterface interface {
	AddChannelGuild(ctx context.Context, uin uint32, in *AddChannelGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelChannelGuild(ctx context.Context, uin uint32, in *DelChannelGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetChannelGuildList(ctx context.Context, uin uint32, in *GetChannelGuildListReq, opts ...svrkit.CallOption) (*GetChannelGuildListResp, error)
	AddPartnerChannelGuild(ctx context.Context, uin uint32, in *PartnerChannelGuildInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelPartnerChannelGuild(ctx context.Context, uin uint32, in *PartnerChannelGuildInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetPartnerChannelGuildList(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetPartnerChannelGuildListResp, error)
	BatGetChannelGuildList(ctx context.Context, uin uint32, in *BatGetChannelGuildListReq, opts ...svrkit.CallOption) (*BatGetChannelGuildListResp, error)
	DismissChannelGuild(ctx context.Context, uin uint32, in *DismissChannelGuildReq, opts ...svrkit.CallOption) (*DismissChannelGuildResp, error)
	GetPartnerChannelGuildListByGuild(ctx context.Context, uin uint32, in *GetPartnerChannelGuildListByGuildReq, opts ...svrkit.CallOption) (*GetPartnerChannelGuildListByGuildResp, error)
	AddGuildMemberChannel(ctx context.Context, uin uint32, in *AddGuildMemberChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	DelGuildMemberChannel(ctx context.Context, uin uint32, in *DelGuildMemberChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetGuildMemberChannel(ctx context.Context, uin uint32, in *GetGuildMemberChannelReq, opts ...svrkit.CallOption) (*GetGuildMemberChannelResp, error)
	// 检查公会房间的数量限制
	CheckGuildChannelCntLimit(ctx context.Context, uin uint32, in *CheckGuildChannelCntLimitReq, opts ...svrkit.CallOption) (*CheckGuildChannelCntLimitResp, error)
}

type ChannelGuildSvrkitClient struct {
	cc *svrkit.ClientConn
}

func NewChannelGuildSvrkitClient(cc *svrkit.ClientConn) ChannelGuildClientInterface {
	return &ChannelGuildSvrkitClient{cc}
}

const (
	commandChannelGuildGetSelfSvnInfo                    = 9995
	commandChannelGuildEcho                              = 9999
	commandChannelGuildAddChannelGuild                   = 1
	commandChannelGuildDelChannelGuild                   = 2
	commandChannelGuildGetChannelGuildList               = 3
	commandChannelGuildAddPartnerChannelGuild            = 4
	commandChannelGuildDelPartnerChannelGuild            = 5
	commandChannelGuildGetPartnerChannelGuildList        = 6
	commandChannelGuildBatGetChannelGuildList            = 7
	commandChannelGuildDismissChannelGuild               = 8
	commandChannelGuildGetPartnerChannelGuildListByGuild = 9
	commandChannelGuildAddGuildMemberChannel             = 10
	commandChannelGuildDelGuildMemberChannel             = 11
	commandChannelGuildGetGuildMemberChannel             = 12
	commandChannelGuildCheckGuildChannelCntLimit         = 13
)

func (c *ChannelGuildSvrkitClient) AddChannelGuild(ctx context.Context, uin uint32, in *AddChannelGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildAddChannelGuild, "/channelguild.ChannelGuild/AddChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) DelChannelGuild(ctx context.Context, uin uint32, in *DelChannelGuildReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildDelChannelGuild, "/channelguild.ChannelGuild/DelChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) GetChannelGuildList(ctx context.Context, uin uint32, in *GetChannelGuildListReq, opts ...svrkit.CallOption) (*GetChannelGuildListResp, error) {
	out := new(GetChannelGuildListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildGetChannelGuildList, "/channelguild.ChannelGuild/GetChannelGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) AddPartnerChannelGuild(ctx context.Context, uin uint32, in *PartnerChannelGuildInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildAddPartnerChannelGuild, "/channelguild.ChannelGuild/AddPartnerChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) DelPartnerChannelGuild(ctx context.Context, uin uint32, in *PartnerChannelGuildInfo, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildDelPartnerChannelGuild, "/channelguild.ChannelGuild/DelPartnerChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) GetPartnerChannelGuildList(ctx context.Context, uin uint32, in *tlvpickle.SKBuiltinEmpty_PB, opts ...svrkit.CallOption) (*GetPartnerChannelGuildListResp, error) {
	out := new(GetPartnerChannelGuildListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildGetPartnerChannelGuildList, "/channelguild.ChannelGuild/GetPartnerChannelGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) BatGetChannelGuildList(ctx context.Context, uin uint32, in *BatGetChannelGuildListReq, opts ...svrkit.CallOption) (*BatGetChannelGuildListResp, error) {
	out := new(BatGetChannelGuildListResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildBatGetChannelGuildList, "/channelguild.ChannelGuild/BatGetChannelGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) DismissChannelGuild(ctx context.Context, uin uint32, in *DismissChannelGuildReq, opts ...svrkit.CallOption) (*DismissChannelGuildResp, error) {
	out := new(DismissChannelGuildResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildDismissChannelGuild, "/channelguild.ChannelGuild/DismissChannelGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) GetPartnerChannelGuildListByGuild(ctx context.Context, uin uint32, in *GetPartnerChannelGuildListByGuildReq, opts ...svrkit.CallOption) (*GetPartnerChannelGuildListByGuildResp, error) {
	out := new(GetPartnerChannelGuildListByGuildResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildGetPartnerChannelGuildListByGuild, "/channelguild.ChannelGuild/GetPartnerChannelGuildListByGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) AddGuildMemberChannel(ctx context.Context, uin uint32, in *AddGuildMemberChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildAddGuildMemberChannel, "/channelguild.ChannelGuild/AddGuildMemberChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) DelGuildMemberChannel(ctx context.Context, uin uint32, in *DelGuildMemberChannelReq, opts ...svrkit.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildDelGuildMemberChannel, "/channelguild.ChannelGuild/DelGuildMemberChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) GetGuildMemberChannel(ctx context.Context, uin uint32, in *GetGuildMemberChannelReq, opts ...svrkit.CallOption) (*GetGuildMemberChannelResp, error) {
	out := new(GetGuildMemberChannelResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildGetGuildMemberChannel, "/channelguild.ChannelGuild/GetGuildMemberChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ChannelGuildSvrkitClient) CheckGuildChannelCntLimit(ctx context.Context, uin uint32, in *CheckGuildChannelCntLimitReq, opts ...svrkit.CallOption) (*CheckGuildChannelCntLimitResp, error) {
	out := new(CheckGuildChannelCntLimitResp)
	err := c.cc.Invoke(ctx, uin, commandChannelGuildCheckGuildChannelCntLimit, "/channelguild.ChannelGuild/CheckGuildChannelCntLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}
