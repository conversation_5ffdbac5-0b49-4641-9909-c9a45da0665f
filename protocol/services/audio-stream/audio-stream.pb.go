// Code generated by protoc-gen-go. DO NOT EDIT.
// source: audio-stream/audio-stream.proto

package audio_stream // import "golang.52tt.com/protocol/services/audio-stream"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AudioInfo struct {
	StreamId             string            `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	ChannelId            string            `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Account              string            `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Uid                  uint32            `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	Title                string            `protobuf:"bytes,16,opt,name=title,proto3" json:"title,omitempty"`
	PublishName          string            `protobuf:"bytes,17,opt,name=publish_name,json=publishName,proto3" json:"publish_name,omitempty"`
	RtmpUrl              []string          `protobuf:"bytes,18,rep,name=rtmp_url,json=rtmpUrl,proto3" json:"rtmp_url,omitempty"`
	HlsUrl               []string          `protobuf:"bytes,19,rep,name=hls_url,json=hlsUrl,proto3" json:"hls_url,omitempty"`
	HdlUrl               []string          `protobuf:"bytes,20,rep,name=hdl_url,json=hdlUrl,proto3" json:"hdl_url,omitempty"`
	PicUrl               []string          `protobuf:"bytes,21,rep,name=pic_url,json=picUrl,proto3" json:"pic_url,omitempty"`
	Kwargs               map[string]string `protobuf:"bytes,22,rep,name=kwargs,proto3" json:"kwargs,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AudioInfo) Reset()         { *m = AudioInfo{} }
func (m *AudioInfo) String() string { return proto.CompactTextString(m) }
func (*AudioInfo) ProtoMessage()    {}
func (*AudioInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{0}
}
func (m *AudioInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AudioInfo.Unmarshal(m, b)
}
func (m *AudioInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AudioInfo.Marshal(b, m, deterministic)
}
func (dst *AudioInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AudioInfo.Merge(dst, src)
}
func (m *AudioInfo) XXX_Size() int {
	return xxx_messageInfo_AudioInfo.Size(m)
}
func (m *AudioInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AudioInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AudioInfo proto.InternalMessageInfo

func (m *AudioInfo) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

func (m *AudioInfo) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *AudioInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *AudioInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AudioInfo) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AudioInfo) GetPublishName() string {
	if m != nil {
		return m.PublishName
	}
	return ""
}

func (m *AudioInfo) GetRtmpUrl() []string {
	if m != nil {
		return m.RtmpUrl
	}
	return nil
}

func (m *AudioInfo) GetHlsUrl() []string {
	if m != nil {
		return m.HlsUrl
	}
	return nil
}

func (m *AudioInfo) GetHdlUrl() []string {
	if m != nil {
		return m.HdlUrl
	}
	return nil
}

func (m *AudioInfo) GetPicUrl() []string {
	if m != nil {
		return m.PicUrl
	}
	return nil
}

func (m *AudioInfo) GetKwargs() map[string]string {
	if m != nil {
		return m.Kwargs
	}
	return nil
}

type InsertReq struct {
	AudioInfo            *AudioInfo `protobuf:"bytes,1,opt,name=audio_info,json=audioInfo,proto3" json:"audio_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *InsertReq) Reset()         { *m = InsertReq{} }
func (m *InsertReq) String() string { return proto.CompactTextString(m) }
func (*InsertReq) ProtoMessage()    {}
func (*InsertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{1}
}
func (m *InsertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertReq.Unmarshal(m, b)
}
func (m *InsertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertReq.Marshal(b, m, deterministic)
}
func (dst *InsertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertReq.Merge(dst, src)
}
func (m *InsertReq) XXX_Size() int {
	return xxx_messageInfo_InsertReq.Size(m)
}
func (m *InsertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertReq proto.InternalMessageInfo

func (m *InsertReq) GetAudioInfo() *AudioInfo {
	if m != nil {
		return m.AudioInfo
	}
	return nil
}

type InsertResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertResp) Reset()         { *m = InsertResp{} }
func (m *InsertResp) String() string { return proto.CompactTextString(m) }
func (*InsertResp) ProtoMessage()    {}
func (*InsertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{2}
}
func (m *InsertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertResp.Unmarshal(m, b)
}
func (m *InsertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertResp.Marshal(b, m, deterministic)
}
func (dst *InsertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertResp.Merge(dst, src)
}
func (m *InsertResp) XXX_Size() int {
	return xxx_messageInfo_InsertResp.Size(m)
}
func (m *InsertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertResp proto.InternalMessageInfo

type RemoveReq struct {
	StreamId             string   `protobuf:"bytes,1,opt,name=stream_id,json=streamId,proto3" json:"stream_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveReq) Reset()         { *m = RemoveReq{} }
func (m *RemoveReq) String() string { return proto.CompactTextString(m) }
func (*RemoveReq) ProtoMessage()    {}
func (*RemoveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{3}
}
func (m *RemoveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveReq.Unmarshal(m, b)
}
func (m *RemoveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveReq.Marshal(b, m, deterministic)
}
func (dst *RemoveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveReq.Merge(dst, src)
}
func (m *RemoveReq) XXX_Size() int {
	return xxx_messageInfo_RemoveReq.Size(m)
}
func (m *RemoveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveReq proto.InternalMessageInfo

func (m *RemoveReq) GetStreamId() string {
	if m != nil {
		return m.StreamId
	}
	return ""
}

type RemoveResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveResp) Reset()         { *m = RemoveResp{} }
func (m *RemoveResp) String() string { return proto.CompactTextString(m) }
func (*RemoveResp) ProtoMessage()    {}
func (*RemoveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{4}
}
func (m *RemoveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveResp.Unmarshal(m, b)
}
func (m *RemoveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveResp.Marshal(b, m, deterministic)
}
func (dst *RemoveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveResp.Merge(dst, src)
}
func (m *RemoveResp) XXX_Size() int {
	return xxx_messageInfo_RemoveResp.Size(m)
}
func (m *RemoveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveResp proto.InternalMessageInfo

type ChannelInfoReq struct {
	ChannelId            string   `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelInfoReq) Reset()         { *m = ChannelInfoReq{} }
func (m *ChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*ChannelInfoReq) ProtoMessage()    {}
func (*ChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{5}
}
func (m *ChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfoReq.Unmarshal(m, b)
}
func (m *ChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *ChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfoReq.Merge(dst, src)
}
func (m *ChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_ChannelInfoReq.Size(m)
}
func (m *ChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfoReq proto.InternalMessageInfo

func (m *ChannelInfoReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

type ChannelInfoResp struct {
	AudioInfos           []*AudioInfo `protobuf:"bytes,1,rep,name=audio_infos,json=audioInfos,proto3" json:"audio_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ChannelInfoResp) Reset()         { *m = ChannelInfoResp{} }
func (m *ChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*ChannelInfoResp) ProtoMessage()    {}
func (*ChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{6}
}
func (m *ChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelInfoResp.Unmarshal(m, b)
}
func (m *ChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *ChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelInfoResp.Merge(dst, src)
}
func (m *ChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_ChannelInfoResp.Size(m)
}
func (m *ChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelInfoResp proto.InternalMessageInfo

func (m *ChannelInfoResp) GetAudioInfos() []*AudioInfo {
	if m != nil {
		return m.AudioInfos
	}
	return nil
}

type InsertCheckInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TaskId               string   `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertCheckInfoReq) Reset()         { *m = InsertCheckInfoReq{} }
func (m *InsertCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*InsertCheckInfoReq) ProtoMessage()    {}
func (*InsertCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{7}
}
func (m *InsertCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertCheckInfoReq.Unmarshal(m, b)
}
func (m *InsertCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *InsertCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertCheckInfoReq.Merge(dst, src)
}
func (m *InsertCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_InsertCheckInfoReq.Size(m)
}
func (m *InsertCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertCheckInfoReq proto.InternalMessageInfo

func (m *InsertCheckInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *InsertCheckInfoReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type InsertCheckInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertCheckInfoResp) Reset()         { *m = InsertCheckInfoResp{} }
func (m *InsertCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*InsertCheckInfoResp) ProtoMessage()    {}
func (*InsertCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{8}
}
func (m *InsertCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertCheckInfoResp.Unmarshal(m, b)
}
func (m *InsertCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *InsertCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertCheckInfoResp.Merge(dst, src)
}
func (m *InsertCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_InsertCheckInfoResp.Size(m)
}
func (m *InsertCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertCheckInfoResp proto.InternalMessageInfo

type UpdateCheckInfoReq struct {
	ProviderTaskId       string   `protobuf:"bytes,1,opt,name=provider_task_id,json=providerTaskId,proto3" json:"provider_task_id,omitempty"`
	TaskId               string   `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCheckInfoReq) Reset()         { *m = UpdateCheckInfoReq{} }
func (m *UpdateCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCheckInfoReq) ProtoMessage()    {}
func (*UpdateCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{9}
}
func (m *UpdateCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCheckInfoReq.Unmarshal(m, b)
}
func (m *UpdateCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCheckInfoReq.Merge(dst, src)
}
func (m *UpdateCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateCheckInfoReq.Size(m)
}
func (m *UpdateCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCheckInfoReq proto.InternalMessageInfo

func (m *UpdateCheckInfoReq) GetProviderTaskId() string {
	if m != nil {
		return m.ProviderTaskId
	}
	return ""
}

func (m *UpdateCheckInfoReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type UpdateCheckInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCheckInfoResp) Reset()         { *m = UpdateCheckInfoResp{} }
func (m *UpdateCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCheckInfoResp) ProtoMessage()    {}
func (*UpdateCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{10}
}
func (m *UpdateCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCheckInfoResp.Unmarshal(m, b)
}
func (m *UpdateCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCheckInfoResp.Merge(dst, src)
}
func (m *UpdateCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateCheckInfoResp.Size(m)
}
func (m *UpdateCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCheckInfoResp proto.InternalMessageInfo

type RemoveCheckInfoReq struct {
	TaskId               string   `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveCheckInfoReq) Reset()         { *m = RemoveCheckInfoReq{} }
func (m *RemoveCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*RemoveCheckInfoReq) ProtoMessage()    {}
func (*RemoveCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{11}
}
func (m *RemoveCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveCheckInfoReq.Unmarshal(m, b)
}
func (m *RemoveCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *RemoveCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveCheckInfoReq.Merge(dst, src)
}
func (m *RemoveCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_RemoveCheckInfoReq.Size(m)
}
func (m *RemoveCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveCheckInfoReq proto.InternalMessageInfo

func (m *RemoveCheckInfoReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

type RemoveCheckInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveCheckInfoResp) Reset()         { *m = RemoveCheckInfoResp{} }
func (m *RemoveCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*RemoveCheckInfoResp) ProtoMessage()    {}
func (*RemoveCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{12}
}
func (m *RemoveCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveCheckInfoResp.Unmarshal(m, b)
}
func (m *RemoveCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *RemoveCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveCheckInfoResp.Merge(dst, src)
}
func (m *RemoveCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_RemoveCheckInfoResp.Size(m)
}
func (m *RemoveCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveCheckInfoResp proto.InternalMessageInfo

type CheckInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInfoReq) Reset()         { *m = CheckInfoReq{} }
func (m *CheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*CheckInfoReq) ProtoMessage()    {}
func (*CheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{13}
}
func (m *CheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInfoReq.Unmarshal(m, b)
}
func (m *CheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *CheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInfoReq.Merge(dst, src)
}
func (m *CheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_CheckInfoReq.Size(m)
}
func (m *CheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInfoReq proto.InternalMessageInfo

func (m *CheckInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckInfoResp struct {
	TaskIds              []string `protobuf:"bytes,1,rep,name=task_ids,json=taskIds,proto3" json:"task_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInfoResp) Reset()         { *m = CheckInfoResp{} }
func (m *CheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*CheckInfoResp) ProtoMessage()    {}
func (*CheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{14}
}
func (m *CheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInfoResp.Unmarshal(m, b)
}
func (m *CheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *CheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInfoResp.Merge(dst, src)
}
func (m *CheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_CheckInfoResp.Size(m)
}
func (m *CheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInfoResp proto.InternalMessageInfo

func (m *CheckInfoResp) GetTaskIds() []string {
	if m != nil {
		return m.TaskIds
	}
	return nil
}

type TokenReq struct {
	Key                  string   `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenReq) Reset()         { *m = TokenReq{} }
func (m *TokenReq) String() string { return proto.CompactTextString(m) }
func (*TokenReq) ProtoMessage()    {}
func (*TokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{15}
}
func (m *TokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenReq.Unmarshal(m, b)
}
func (m *TokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenReq.Marshal(b, m, deterministic)
}
func (dst *TokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenReq.Merge(dst, src)
}
func (m *TokenReq) XXX_Size() int {
	return xxx_messageInfo_TokenReq.Size(m)
}
func (m *TokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_TokenReq proto.InternalMessageInfo

func (m *TokenReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type TokenResp struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenResp) Reset()         { *m = TokenResp{} }
func (m *TokenResp) String() string { return proto.CompactTextString(m) }
func (*TokenResp) ProtoMessage()    {}
func (*TokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_audio_stream_c907ee00873273f1, []int{16}
}
func (m *TokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenResp.Unmarshal(m, b)
}
func (m *TokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenResp.Marshal(b, m, deterministic)
}
func (dst *TokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenResp.Merge(dst, src)
}
func (m *TokenResp) XXX_Size() int {
	return xxx_messageInfo_TokenResp.Size(m)
}
func (m *TokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_TokenResp proto.InternalMessageInfo

func (m *TokenResp) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func init() {
	proto.RegisterType((*AudioInfo)(nil), "audio_stream.AudioInfo")
	proto.RegisterMapType((map[string]string)(nil), "audio_stream.AudioInfo.KwargsEntry")
	proto.RegisterType((*InsertReq)(nil), "audio_stream.InsertReq")
	proto.RegisterType((*InsertResp)(nil), "audio_stream.InsertResp")
	proto.RegisterType((*RemoveReq)(nil), "audio_stream.RemoveReq")
	proto.RegisterType((*RemoveResp)(nil), "audio_stream.RemoveResp")
	proto.RegisterType((*ChannelInfoReq)(nil), "audio_stream.ChannelInfoReq")
	proto.RegisterType((*ChannelInfoResp)(nil), "audio_stream.ChannelInfoResp")
	proto.RegisterType((*InsertCheckInfoReq)(nil), "audio_stream.InsertCheckInfoReq")
	proto.RegisterType((*InsertCheckInfoResp)(nil), "audio_stream.InsertCheckInfoResp")
	proto.RegisterType((*UpdateCheckInfoReq)(nil), "audio_stream.UpdateCheckInfoReq")
	proto.RegisterType((*UpdateCheckInfoResp)(nil), "audio_stream.UpdateCheckInfoResp")
	proto.RegisterType((*RemoveCheckInfoReq)(nil), "audio_stream.RemoveCheckInfoReq")
	proto.RegisterType((*RemoveCheckInfoResp)(nil), "audio_stream.RemoveCheckInfoResp")
	proto.RegisterType((*CheckInfoReq)(nil), "audio_stream.CheckInfoReq")
	proto.RegisterType((*CheckInfoResp)(nil), "audio_stream.CheckInfoResp")
	proto.RegisterType((*TokenReq)(nil), "audio_stream.TokenReq")
	proto.RegisterType((*TokenResp)(nil), "audio_stream.TokenResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AudioStreamClient is the client API for AudioStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AudioStreamClient interface {
	Insert(ctx context.Context, in *InsertReq, opts ...grpc.CallOption) (*InsertResp, error)
	Remove(ctx context.Context, in *RemoveReq, opts ...grpc.CallOption) (*RemoveResp, error)
	ChannelInfo(ctx context.Context, in *ChannelInfoReq, opts ...grpc.CallOption) (*ChannelInfoResp, error)
	InsertCheckInfo(ctx context.Context, in *InsertCheckInfoReq, opts ...grpc.CallOption) (*InsertCheckInfoResp, error)
	UpdateCheckInfo(ctx context.Context, in *UpdateCheckInfoReq, opts ...grpc.CallOption) (*UpdateCheckInfoResp, error)
	CheckInfo(ctx context.Context, in *CheckInfoReq, opts ...grpc.CallOption) (*CheckInfoResp, error)
	RemoveCheckInfo(ctx context.Context, in *RemoveCheckInfoReq, opts ...grpc.CallOption) (*RemoveCheckInfoResp, error)
	Token(ctx context.Context, in *TokenReq, opts ...grpc.CallOption) (*TokenResp, error)
}

type audioStreamClient struct {
	cc *grpc.ClientConn
}

func NewAudioStreamClient(cc *grpc.ClientConn) AudioStreamClient {
	return &audioStreamClient{cc}
}

func (c *audioStreamClient) Insert(ctx context.Context, in *InsertReq, opts ...grpc.CallOption) (*InsertResp, error) {
	out := new(InsertResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/Insert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) Remove(ctx context.Context, in *RemoveReq, opts ...grpc.CallOption) (*RemoveResp, error) {
	out := new(RemoveResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/Remove", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) ChannelInfo(ctx context.Context, in *ChannelInfoReq, opts ...grpc.CallOption) (*ChannelInfoResp, error) {
	out := new(ChannelInfoResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/ChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) InsertCheckInfo(ctx context.Context, in *InsertCheckInfoReq, opts ...grpc.CallOption) (*InsertCheckInfoResp, error) {
	out := new(InsertCheckInfoResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/InsertCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) UpdateCheckInfo(ctx context.Context, in *UpdateCheckInfoReq, opts ...grpc.CallOption) (*UpdateCheckInfoResp, error) {
	out := new(UpdateCheckInfoResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/UpdateCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) CheckInfo(ctx context.Context, in *CheckInfoReq, opts ...grpc.CallOption) (*CheckInfoResp, error) {
	out := new(CheckInfoResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/CheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) RemoveCheckInfo(ctx context.Context, in *RemoveCheckInfoReq, opts ...grpc.CallOption) (*RemoveCheckInfoResp, error) {
	out := new(RemoveCheckInfoResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/RemoveCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *audioStreamClient) Token(ctx context.Context, in *TokenReq, opts ...grpc.CallOption) (*TokenResp, error) {
	out := new(TokenResp)
	err := c.cc.Invoke(ctx, "/audio_stream.AudioStream/Token", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AudioStreamServer is the server API for AudioStream service.
type AudioStreamServer interface {
	Insert(context.Context, *InsertReq) (*InsertResp, error)
	Remove(context.Context, *RemoveReq) (*RemoveResp, error)
	ChannelInfo(context.Context, *ChannelInfoReq) (*ChannelInfoResp, error)
	InsertCheckInfo(context.Context, *InsertCheckInfoReq) (*InsertCheckInfoResp, error)
	UpdateCheckInfo(context.Context, *UpdateCheckInfoReq) (*UpdateCheckInfoResp, error)
	CheckInfo(context.Context, *CheckInfoReq) (*CheckInfoResp, error)
	RemoveCheckInfo(context.Context, *RemoveCheckInfoReq) (*RemoveCheckInfoResp, error)
	Token(context.Context, *TokenReq) (*TokenResp, error)
}

func RegisterAudioStreamServer(s *grpc.Server, srv AudioStreamServer) {
	s.RegisterService(&_AudioStream_serviceDesc, srv)
}

func _AudioStream_Insert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).Insert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/Insert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).Insert(ctx, req.(*InsertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_Remove_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).Remove(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/Remove",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).Remove(ctx, req.(*RemoveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_ChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).ChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/ChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).ChannelInfo(ctx, req.(*ChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_InsertCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).InsertCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/InsertCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).InsertCheckInfo(ctx, req.(*InsertCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_UpdateCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).UpdateCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/UpdateCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).UpdateCheckInfo(ctx, req.(*UpdateCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_CheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).CheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/CheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).CheckInfo(ctx, req.(*CheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_RemoveCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).RemoveCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/RemoveCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).RemoveCheckInfo(ctx, req.(*RemoveCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AudioStream_Token_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AudioStreamServer).Token(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/audio_stream.AudioStream/Token",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AudioStreamServer).Token(ctx, req.(*TokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AudioStream_serviceDesc = grpc.ServiceDesc{
	ServiceName: "audio_stream.AudioStream",
	HandlerType: (*AudioStreamServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Insert",
			Handler:    _AudioStream_Insert_Handler,
		},
		{
			MethodName: "Remove",
			Handler:    _AudioStream_Remove_Handler,
		},
		{
			MethodName: "ChannelInfo",
			Handler:    _AudioStream_ChannelInfo_Handler,
		},
		{
			MethodName: "InsertCheckInfo",
			Handler:    _AudioStream_InsertCheckInfo_Handler,
		},
		{
			MethodName: "UpdateCheckInfo",
			Handler:    _AudioStream_UpdateCheckInfo_Handler,
		},
		{
			MethodName: "CheckInfo",
			Handler:    _AudioStream_CheckInfo_Handler,
		},
		{
			MethodName: "RemoveCheckInfo",
			Handler:    _AudioStream_RemoveCheckInfo_Handler,
		},
		{
			MethodName: "Token",
			Handler:    _AudioStream_Token_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "audio-stream/audio-stream.proto",
}

func init() {
	proto.RegisterFile("audio-stream/audio-stream.proto", fileDescriptor_audio_stream_c907ee00873273f1)
}

var fileDescriptor_audio_stream_c907ee00873273f1 = []byte{
	// 686 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0x4d, 0x6f, 0xd3, 0x4c,
	0x10, 0x96, 0x9b, 0x36, 0xa9, 0xc7, 0xe9, 0xc7, 0xbb, 0xfd, 0x88, 0xdf, 0xb4, 0x15, 0xe9, 0x72,
	0x89, 0x90, 0xea, 0xa0, 0x20, 0x50, 0x81, 0x13, 0x14, 0x0e, 0xa1, 0x88, 0x83, 0x69, 0x41, 0xe2,
	0x12, 0xb9, 0xf6, 0xb6, 0xb1, 0xe2, 0xd8, 0x5b, 0xef, 0x26, 0xa8, 0x7f, 0x81, 0xbf, 0xc8, 0x9f,
	0x41, 0xbb, 0x6b, 0x27, 0xf6, 0x3a, 0x29, 0xe2, 0xe6, 0x99, 0x67, 0xe6, 0x99, 0xaf, 0x67, 0x65,
	0x78, 0xe2, 0x4d, 0x83, 0x30, 0x39, 0x63, 0x3c, 0x25, 0xde, 0xa4, 0x57, 0x34, 0x1c, 0x9a, 0x26,
	0x3c, 0x41, 0x4d, 0xe9, 0x1b, 0x2a, 0x1f, 0xfe, 0x55, 0x03, 0xf3, 0x9d, 0x70, 0x0c, 0xe2, 0xdb,
	0x04, 0x1d, 0x81, 0xa9, 0xfc, 0xc3, 0x30, 0xb0, 0x8d, 0x8e, 0xd1, 0x35, 0xdd, 0x4d, 0xe5, 0x18,
	0x04, 0xe8, 0x04, 0xc0, 0x1f, 0x79, 0x71, 0x4c, 0x22, 0x81, 0xae, 0x49, 0xd4, 0xcc, 0x3c, 0x83,
	0x00, 0xd9, 0xd0, 0xf0, 0x7c, 0x3f, 0x99, 0xc6, 0xdc, 0xae, 0x49, 0x2c, 0x37, 0xd1, 0x2e, 0xd4,
	0xa6, 0x61, 0x60, 0xaf, 0x77, 0x8c, 0xee, 0x96, 0x2b, 0x3e, 0xd1, 0x3e, 0x6c, 0xf0, 0x90, 0x47,
	0xc4, 0xde, 0x95, 0x91, 0xca, 0x40, 0xa7, 0xd0, 0xa4, 0xd3, 0x9b, 0x28, 0x64, 0xa3, 0x61, 0xec,
	0x4d, 0x88, 0xfd, 0x9f, 0x04, 0xad, 0xcc, 0xf7, 0xc5, 0x9b, 0x10, 0xf4, 0x3f, 0x6c, 0xa6, 0x7c,
	0x42, 0x87, 0xd3, 0x34, 0xb2, 0x51, 0xa7, 0x26, 0xaa, 0x08, 0xfb, 0x3a, 0x8d, 0x50, 0x0b, 0x1a,
	0xa3, 0x88, 0x49, 0x64, 0x4f, 0x22, 0xf5, 0x51, 0xc4, 0x72, 0x20, 0x88, 0x24, 0xb0, 0x9f, 0x01,
	0x41, 0x94, 0x01, 0x34, 0xf4, 0x25, 0x70, 0xa0, 0x00, 0x1a, 0xfa, 0x02, 0x78, 0x0b, 0xf5, 0xf1,
	0x4f, 0x2f, 0xbd, 0x63, 0xf6, 0x61, 0xa7, 0xd6, 0xb5, 0xfa, 0x4f, 0x9d, 0xe2, 0xce, 0x9c, 0xf9,
	0xbe, 0x9c, 0x4b, 0x19, 0xf5, 0x31, 0xe6, 0xe9, 0x83, 0x9b, 0xa5, 0xb4, 0x5f, 0x83, 0x55, 0x70,
	0x8b, 0xe1, 0xc7, 0xe4, 0x21, 0x5b, 0xa6, 0xf8, 0x14, 0xc3, 0xcf, 0xbc, 0x68, 0x4a, 0xb2, 0x15,
	0x2a, 0xe3, 0xcd, 0xda, 0xb9, 0x81, 0x2f, 0xc0, 0x1c, 0xc4, 0x8c, 0xa4, 0xdc, 0x25, 0xf7, 0xe8,
	0x15, 0x80, 0xaa, 0x1a, 0xc6, 0xb7, 0x89, 0xcc, 0xb7, 0xfa, 0xad, 0x15, 0x8d, 0xb8, 0xa6, 0x97,
	0x7f, 0xe2, 0x26, 0x40, 0x4e, 0xc2, 0x28, 0xee, 0x82, 0xe9, 0x92, 0x49, 0x32, 0x23, 0x82, 0xf2,
	0xb1, 0xf3, 0x8a, 0xbc, 0x3c, 0x92, 0x51, 0xdc, 0x83, 0xed, 0x8b, 0xec, 0xb4, 0x82, 0x9f, 0xdc,
	0x6b, 0xe7, 0x37, 0xb4, 0xf3, 0xe3, 0x4b, 0xd8, 0x29, 0x25, 0x30, 0x8a, 0xce, 0xc1, 0x5a, 0x4c,
	0xc0, 0x6c, 0x43, 0xee, 0x72, 0xe5, 0x08, 0x30, 0x1f, 0x81, 0xe1, 0xcf, 0x80, 0xd4, 0x0c, 0x17,
	0x23, 0xe2, 0x8f, 0x57, 0x77, 0xb0, 0x55, 0x14, 0x60, 0x0b, 0x1a, 0xdc, 0x63, 0xe3, 0x85, 0x38,
	0xeb, 0xc2, 0x1c, 0x04, 0xf8, 0x00, 0xf6, 0x2a, 0x6c, 0x8c, 0xe2, 0xef, 0x80, 0xae, 0x69, 0xe0,
	0x71, 0x52, 0x2a, 0xd2, 0x85, 0x5d, 0x9a, 0x26, 0xb3, 0x30, 0x20, 0xe9, 0x30, 0xa7, 0x53, 0xc3,
	0x6e, 0xe7, 0xfe, 0x2b, 0x49, 0xfb, 0x68, 0xbd, 0x0a, 0x31, 0xa3, 0xf8, 0x0c, 0x90, 0x5a, 0x70,
	0xa9, 0x5e, 0x81, 0xc5, 0xd0, 0x59, 0x2a, 0xe1, 0x92, 0xa5, 0xf9, 0x0f, 0x4b, 0xc1, 0xcf, 0x60,
	0xab, 0x94, 0x2f, 0x5e, 0x50, 0x56, 0x4f, 0x5d, 0xc4, 0x74, 0x1b, 0xaa, 0x20, 0xc3, 0xc7, 0xb0,
	0x79, 0x95, 0x8c, 0x49, 0x2c, 0x68, 0x2b, 0xb2, 0xc5, 0xa7, 0x60, 0x66, 0x28, 0xa3, 0xf2, 0x01,
	0x0b, 0x23, 0x0b, 0x50, 0x46, 0xff, 0xf7, 0x3a, 0x58, 0xf2, 0xa0, 0x5f, 0xe5, 0x71, 0xc5, 0x3b,
	0x52, 0x8b, 0x47, 0xda, 0xd5, 0xe7, 0x2a, 0x6f, 0xdb, 0xcb, 0x01, 0x46, 0x45, 0xb2, 0x9a, 0x5f,
	0x4f, 0x9e, 0xeb, 0x59, 0x4f, 0x5e, 0xc8, 0x17, 0x7d, 0x02, 0xab, 0xa0, 0x46, 0x74, 0x5c, 0x0e,
	0x2c, 0x2b, 0xbb, 0x7d, 0xf2, 0x08, 0xca, 0x28, 0xfa, 0x06, 0x3b, 0x9a, 0x7c, 0x50, 0x67, 0x59,
	0xd7, 0xc5, 0xb3, 0xb4, 0x4f, 0xff, 0x12, 0xa1, 0x78, 0x35, 0x99, 0xe8, 0xbc, 0x55, 0x79, 0xea,
	0xbc, 0x4b, 0x74, 0x86, 0x3e, 0x80, 0xb9, 0x60, 0x6c, 0xeb, 0xb3, 0x15, 0xb8, 0x8e, 0x56, 0x62,
	0xaa, 0x3b, 0x4d, 0x7e, 0x7a, 0x77, 0x55, 0x31, 0xeb, 0xdd, 0x2d, 0xd1, 0x2f, 0x3a, 0x87, 0x0d,
	0x29, 0x23, 0x74, 0x58, 0x8e, 0xcd, 0x95, 0xd7, 0x6e, 0x2d, 0xf5, 0x33, 0xfa, 0xfe, 0xf9, 0x0f,
	0xe7, 0x2e, 0x89, 0xbc, 0xf8, 0xce, 0x79, 0xd9, 0xe7, 0xdc, 0xf1, 0x93, 0x49, 0x4f, 0xfe, 0xd1,
	0xfc, 0x24, 0xea, 0x31, 0x92, 0xce, 0x42, 0x9f, 0xb0, 0xd2, 0x0f, 0xef, 0xa6, 0x2e, 0xf1, 0x17,
	0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0xf5, 0xff, 0x50, 0xac, 0x14, 0x07, 0x00, 0x00,
}
