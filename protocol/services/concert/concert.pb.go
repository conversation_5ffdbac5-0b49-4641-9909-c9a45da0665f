// Code generated by protoc-gen-go. DO NOT EDIT.
// source: concert/concert.proto

package concert // import "golang.52tt.com/protocol/services/concert"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 舞台类型
type Stagetype int32

const (
	Stagetype_Stagetype_UNDEFINED Stagetype = 0
	Stagetype_Stagetype_mild      Stagetype = 1
	Stagetype_Stagetype_Excited   Stagetype = 2
)

var Stagetype_name = map[int32]string{
	0: "Stagetype_UNDEFINED",
	1: "Stagetype_mild",
	2: "Stagetype_Excited",
}
var Stagetype_value = map[string]int32{
	"Stagetype_UNDEFINED": 0,
	"Stagetype_mild":      1,
	"Stagetype_Excited":   2,
}

func (x Stagetype) String() string {
	return proto.EnumName(Stagetype_name, int32(x))
}
func (Stagetype) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{0}
}

type ConcertGameStage int32

const (
	ConcertGameStage_ConcertGameStage_UNDEFINED   ConcertGameStage = 0
	ConcertGameStage_ConcertGameStage_DOWNLOADING ConcertGameStage = 1
	ConcertGameStage_ConcertGameStage_Singing     ConcertGameStage = 2
)

var ConcertGameStage_name = map[int32]string{
	0: "ConcertGameStage_UNDEFINED",
	1: "ConcertGameStage_DOWNLOADING",
	2: "ConcertGameStage_Singing",
}
var ConcertGameStage_value = map[string]int32{
	"ConcertGameStage_UNDEFINED":   0,
	"ConcertGameStage_DOWNLOADING": 1,
	"ConcertGameStage_Singing":     2,
}

func (x ConcertGameStage) String() string {
	return proto.EnumName(ConcertGameStage_name, int32(x))
}
func (ConcertGameStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{1}
}

type KeyMapType int32

const (
	KeyMapType_KeyMapType_UNDEFINED KeyMapType = 0
	KeyMapType_KeyMapType_CLICK     KeyMapType = 1
	KeyMapType_KeyMapType_PRESS     KeyMapType = 2
	KeyMapType_KeyMapType_SLIDE     KeyMapType = 3
)

var KeyMapType_name = map[int32]string{
	0: "KeyMapType_UNDEFINED",
	1: "KeyMapType_CLICK",
	2: "KeyMapType_PRESS",
	3: "KeyMapType_SLIDE",
}
var KeyMapType_value = map[string]int32{
	"KeyMapType_UNDEFINED": 0,
	"KeyMapType_CLICK":     1,
	"KeyMapType_PRESS":     2,
	"KeyMapType_SLIDE":     3,
}

func (x KeyMapType) String() string {
	return proto.EnumName(KeyMapType_name, int32(x))
}
func (KeyMapType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{2}
}

type BandRole int32

const (
	BandRole_BandRole_UNDEFINED     BandRole = 0
	BandRole_BandRole_MAIN_SINGER   BandRole = 1
	BandRole_BandRole_LEAD_GUITAR   BandRole = 2
	BandRole_BandRole_KEYBOARD      BandRole = 3
	BandRole_BandRole_BASSIST       BandRole = 4
	BandRole_BandRole_DRUMMER       BandRole = 5
	BandRole_BandRole_RHYTHM_GUITAR BandRole = 6
)

var BandRole_name = map[int32]string{
	0: "BandRole_UNDEFINED",
	1: "BandRole_MAIN_SINGER",
	2: "BandRole_LEAD_GUITAR",
	3: "BandRole_KEYBOARD",
	4: "BandRole_BASSIST",
	5: "BandRole_DRUMMER",
	6: "BandRole_RHYTHM_GUITAR",
}
var BandRole_value = map[string]int32{
	"BandRole_UNDEFINED":     0,
	"BandRole_MAIN_SINGER":   1,
	"BandRole_LEAD_GUITAR":   2,
	"BandRole_KEYBOARD":      3,
	"BandRole_BASSIST":       4,
	"BandRole_DRUMMER":       5,
	"BandRole_RHYTHM_GUITAR": 6,
}

func (x BandRole) String() string {
	return proto.EnumName(BandRole_name, int32(x))
}
func (BandRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{3}
}

type Sex int32

const (
	Sex_All    Sex = 0
	Sex_Male   Sex = 1
	Sex_Female Sex = 2
)

var Sex_name = map[int32]string{
	0: "All",
	1: "Male",
	2: "Female",
}
var Sex_value = map[string]int32{
	"All":    0,
	"Male":   1,
	"Female": 2,
}

func (x Sex) String() string {
	return proto.EnumName(Sex_name, int32(x))
}
func (Sex) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{4}
}

type ReportConcertGradeType int32

const (
	ReportConcertGradeType_MISS    ReportConcertGradeType = 0
	ReportConcertGradeType_PERFECT ReportConcertGradeType = 1
	ReportConcertGradeType_GREAT   ReportConcertGradeType = 2
)

var ReportConcertGradeType_name = map[int32]string{
	0: "MISS",
	1: "PERFECT",
	2: "GREAT",
}
var ReportConcertGradeType_value = map[string]int32{
	"MISS":    0,
	"PERFECT": 1,
	"GREAT":   2,
}

func (x ReportConcertGradeType) String() string {
	return proto.EnumName(ReportConcertGradeType_name, int32(x))
}
func (ReportConcertGradeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{5}
}

type ListActivityCodeReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListActivityCodeReq) Reset()         { *m = ListActivityCodeReq{} }
func (m *ListActivityCodeReq) String() string { return proto.CompactTextString(m) }
func (*ListActivityCodeReq) ProtoMessage()    {}
func (*ListActivityCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{0}
}
func (m *ListActivityCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListActivityCodeReq.Unmarshal(m, b)
}
func (m *ListActivityCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListActivityCodeReq.Marshal(b, m, deterministic)
}
func (dst *ListActivityCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListActivityCodeReq.Merge(dst, src)
}
func (m *ListActivityCodeReq) XXX_Size() int {
	return xxx_messageInfo_ListActivityCodeReq.Size(m)
}
func (m *ListActivityCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListActivityCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListActivityCodeReq proto.InternalMessageInfo

type ListActivityCodeResp struct {
	Codes                []*ActivityCode `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ListActivityCodeResp) Reset()         { *m = ListActivityCodeResp{} }
func (m *ListActivityCodeResp) String() string { return proto.CompactTextString(m) }
func (*ListActivityCodeResp) ProtoMessage()    {}
func (*ListActivityCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{1}
}
func (m *ListActivityCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListActivityCodeResp.Unmarshal(m, b)
}
func (m *ListActivityCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListActivityCodeResp.Marshal(b, m, deterministic)
}
func (dst *ListActivityCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListActivityCodeResp.Merge(dst, src)
}
func (m *ListActivityCodeResp) XXX_Size() int {
	return xxx_messageInfo_ListActivityCodeResp.Size(m)
}
func (m *ListActivityCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListActivityCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListActivityCodeResp proto.InternalMessageInfo

func (m *ListActivityCodeResp) GetCodes() []*ActivityCode {
	if m != nil {
		return m.Codes
	}
	return nil
}

type ActivityCode struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Note                 string   `protobuf:"bytes,3,opt,name=note,proto3" json:"note,omitempty"`
	Url                  string   `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	Prefix               string   `protobuf:"bytes,5,opt,name=prefix,proto3" json:"prefix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityCode) Reset()         { *m = ActivityCode{} }
func (m *ActivityCode) String() string { return proto.CompactTextString(m) }
func (*ActivityCode) ProtoMessage()    {}
func (*ActivityCode) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{2}
}
func (m *ActivityCode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityCode.Unmarshal(m, b)
}
func (m *ActivityCode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityCode.Marshal(b, m, deterministic)
}
func (dst *ActivityCode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityCode.Merge(dst, src)
}
func (m *ActivityCode) XXX_Size() int {
	return xxx_messageInfo_ActivityCode.Size(m)
}
func (m *ActivityCode) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityCode.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityCode proto.InternalMessageInfo

func (m *ActivityCode) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *ActivityCode) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *ActivityCode) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ActivityCode) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ActivityCode) GetPrefix() string {
	if m != nil {
		return m.Prefix
	}
	return ""
}

type ExchangeActivityCodeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeActivityCodeReq) Reset()         { *m = ExchangeActivityCodeReq{} }
func (m *ExchangeActivityCodeReq) String() string { return proto.CompactTextString(m) }
func (*ExchangeActivityCodeReq) ProtoMessage()    {}
func (*ExchangeActivityCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{3}
}
func (m *ExchangeActivityCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeActivityCodeReq.Unmarshal(m, b)
}
func (m *ExchangeActivityCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeActivityCodeReq.Marshal(b, m, deterministic)
}
func (dst *ExchangeActivityCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeActivityCodeReq.Merge(dst, src)
}
func (m *ExchangeActivityCodeReq) XXX_Size() int {
	return xxx_messageInfo_ExchangeActivityCodeReq.Size(m)
}
func (m *ExchangeActivityCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeActivityCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeActivityCodeReq proto.InternalMessageInfo

func (m *ExchangeActivityCodeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExchangeActivityCodeReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

type ExchangeActivityCodeResp struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExchangeActivityCodeResp) Reset()         { *m = ExchangeActivityCodeResp{} }
func (m *ExchangeActivityCodeResp) String() string { return proto.CompactTextString(m) }
func (*ExchangeActivityCodeResp) ProtoMessage()    {}
func (*ExchangeActivityCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{4}
}
func (m *ExchangeActivityCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExchangeActivityCodeResp.Unmarshal(m, b)
}
func (m *ExchangeActivityCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExchangeActivityCodeResp.Marshal(b, m, deterministic)
}
func (dst *ExchangeActivityCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExchangeActivityCodeResp.Merge(dst, src)
}
func (m *ExchangeActivityCodeResp) XXX_Size() int {
	return xxx_messageInfo_ExchangeActivityCodeResp.Size(m)
}
func (m *ExchangeActivityCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExchangeActivityCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExchangeActivityCodeResp proto.InternalMessageInfo

func (m *ExchangeActivityCodeResp) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

type UpdateActivityCodeReq struct {
	Codes                []*ActivityCode `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateActivityCodeReq) Reset()         { *m = UpdateActivityCodeReq{} }
func (m *UpdateActivityCodeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityCodeReq) ProtoMessage()    {}
func (*UpdateActivityCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{5}
}
func (m *UpdateActivityCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityCodeReq.Unmarshal(m, b)
}
func (m *UpdateActivityCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityCodeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityCodeReq.Merge(dst, src)
}
func (m *UpdateActivityCodeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityCodeReq.Size(m)
}
func (m *UpdateActivityCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityCodeReq proto.InternalMessageInfo

func (m *UpdateActivityCodeReq) GetCodes() []*ActivityCode {
	if m != nil {
		return m.Codes
	}
	return nil
}

type UpdateActivityCodeResp struct {
	Codes                []*ActivityCode `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UpdateActivityCodeResp) Reset()         { *m = UpdateActivityCodeResp{} }
func (m *UpdateActivityCodeResp) String() string { return proto.CompactTextString(m) }
func (*UpdateActivityCodeResp) ProtoMessage()    {}
func (*UpdateActivityCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{6}
}
func (m *UpdateActivityCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateActivityCodeResp.Unmarshal(m, b)
}
func (m *UpdateActivityCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateActivityCodeResp.Marshal(b, m, deterministic)
}
func (dst *UpdateActivityCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateActivityCodeResp.Merge(dst, src)
}
func (m *UpdateActivityCodeResp) XXX_Size() int {
	return xxx_messageInfo_UpdateActivityCodeResp.Size(m)
}
func (m *UpdateActivityCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateActivityCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateActivityCodeResp proto.InternalMessageInfo

func (m *UpdateActivityCodeResp) GetCodes() []*ActivityCode {
	if m != nil {
		return m.Codes
	}
	return nil
}

type AddActivityCodeReq struct {
	Codes                []*ActivityCode `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddActivityCodeReq) Reset()         { *m = AddActivityCodeReq{} }
func (m *AddActivityCodeReq) String() string { return proto.CompactTextString(m) }
func (*AddActivityCodeReq) ProtoMessage()    {}
func (*AddActivityCodeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{7}
}
func (m *AddActivityCodeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddActivityCodeReq.Unmarshal(m, b)
}
func (m *AddActivityCodeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddActivityCodeReq.Marshal(b, m, deterministic)
}
func (dst *AddActivityCodeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddActivityCodeReq.Merge(dst, src)
}
func (m *AddActivityCodeReq) XXX_Size() int {
	return xxx_messageInfo_AddActivityCodeReq.Size(m)
}
func (m *AddActivityCodeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddActivityCodeReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddActivityCodeReq proto.InternalMessageInfo

func (m *AddActivityCodeReq) GetCodes() []*ActivityCode {
	if m != nil {
		return m.Codes
	}
	return nil
}

type AddActivityCodeResp struct {
	Codes                []*ActivityCode `protobuf:"bytes,1,rep,name=codes,proto3" json:"codes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AddActivityCodeResp) Reset()         { *m = AddActivityCodeResp{} }
func (m *AddActivityCodeResp) String() string { return proto.CompactTextString(m) }
func (*AddActivityCodeResp) ProtoMessage()    {}
func (*AddActivityCodeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{8}
}
func (m *AddActivityCodeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddActivityCodeResp.Unmarshal(m, b)
}
func (m *AddActivityCodeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddActivityCodeResp.Marshal(b, m, deterministic)
}
func (dst *AddActivityCodeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddActivityCodeResp.Merge(dst, src)
}
func (m *AddActivityCodeResp) XXX_Size() int {
	return xxx_messageInfo_AddActivityCodeResp.Size(m)
}
func (m *AddActivityCodeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddActivityCodeResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddActivityCodeResp proto.InternalMessageInfo

func (m *AddActivityCodeResp) GetCodes() []*ActivityCode {
	if m != nil {
		return m.Codes
	}
	return nil
}

type UpdateBackingTrackStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	OpenBackingTrack     bool     `protobuf:"varint,4,opt,name=open_backing_track,json=openBackingTrack,proto3" json:"open_backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBackingTrackStatusReq) Reset()         { *m = UpdateBackingTrackStatusReq{} }
func (m *UpdateBackingTrackStatusReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBackingTrackStatusReq) ProtoMessage()    {}
func (*UpdateBackingTrackStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{9}
}
func (m *UpdateBackingTrackStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBackingTrackStatusReq.Unmarshal(m, b)
}
func (m *UpdateBackingTrackStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBackingTrackStatusReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBackingTrackStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBackingTrackStatusReq.Merge(dst, src)
}
func (m *UpdateBackingTrackStatusReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBackingTrackStatusReq.Size(m)
}
func (m *UpdateBackingTrackStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBackingTrackStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBackingTrackStatusReq proto.InternalMessageInfo

func (m *UpdateBackingTrackStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBackingTrackStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UpdateBackingTrackStatusReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UpdateBackingTrackStatusReq) GetOpenBackingTrack() bool {
	if m != nil {
		return m.OpenBackingTrack
	}
	return false
}

type UpdateBackingTrackStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBackingTrackStatusResp) Reset()         { *m = UpdateBackingTrackStatusResp{} }
func (m *UpdateBackingTrackStatusResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBackingTrackStatusResp) ProtoMessage()    {}
func (*UpdateBackingTrackStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{10}
}
func (m *UpdateBackingTrackStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBackingTrackStatusResp.Unmarshal(m, b)
}
func (m *UpdateBackingTrackStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBackingTrackStatusResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBackingTrackStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBackingTrackStatusResp.Merge(dst, src)
}
func (m *UpdateBackingTrackStatusResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBackingTrackStatusResp.Size(m)
}
func (m *UpdateBackingTrackStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBackingTrackStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBackingTrackStatusResp proto.InternalMessageInfo

// 获取歌曲选项
type GetConcertSongOptsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConcertSongOptsReq) Reset()         { *m = GetConcertSongOptsReq{} }
func (m *GetConcertSongOptsReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongOptsReq) ProtoMessage()    {}
func (*GetConcertSongOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{11}
}
func (m *GetConcertSongOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongOptsReq.Unmarshal(m, b)
}
func (m *GetConcertSongOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongOptsReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongOptsReq.Merge(dst, src)
}
func (m *GetConcertSongOptsReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongOptsReq.Size(m)
}
func (m *GetConcertSongOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongOptsReq proto.InternalMessageInfo

type GetConcertSongOptsResp struct {
	Opts                 []*ConcertSongOpt `protobuf:"bytes,1,rep,name=opts,proto3" json:"opts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetConcertSongOptsResp) Reset()         { *m = GetConcertSongOptsResp{} }
func (m *GetConcertSongOptsResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongOptsResp) ProtoMessage()    {}
func (*GetConcertSongOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{12}
}
func (m *GetConcertSongOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongOptsResp.Unmarshal(m, b)
}
func (m *GetConcertSongOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongOptsResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongOptsResp.Merge(dst, src)
}
func (m *GetConcertSongOptsResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongOptsResp.Size(m)
}
func (m *GetConcertSongOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongOptsResp proto.InternalMessageInfo

func (m *GetConcertSongOptsResp) GetOpts() []*ConcertSongOpt {
	if m != nil {
		return m.Opts
	}
	return nil
}

type SearchConcertSongReq struct {
	KeyWord              string   `protobuf:"bytes,1,opt,name=key_word,json=keyWord,proto3" json:"key_word,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchConcertSongReq) Reset()         { *m = SearchConcertSongReq{} }
func (m *SearchConcertSongReq) String() string { return proto.CompactTextString(m) }
func (*SearchConcertSongReq) ProtoMessage()    {}
func (*SearchConcertSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{13}
}
func (m *SearchConcertSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchConcertSongReq.Unmarshal(m, b)
}
func (m *SearchConcertSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchConcertSongReq.Marshal(b, m, deterministic)
}
func (dst *SearchConcertSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchConcertSongReq.Merge(dst, src)
}
func (m *SearchConcertSongReq) XXX_Size() int {
	return xxx_messageInfo_SearchConcertSongReq.Size(m)
}
func (m *SearchConcertSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchConcertSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchConcertSongReq proto.InternalMessageInfo

func (m *SearchConcertSongReq) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

type SearchConcertSongResp struct {
	Songs                []*ConcertSong `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchConcertSongResp) Reset()         { *m = SearchConcertSongResp{} }
func (m *SearchConcertSongResp) String() string { return proto.CompactTextString(m) }
func (*SearchConcertSongResp) ProtoMessage()    {}
func (*SearchConcertSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{14}
}
func (m *SearchConcertSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchConcertSongResp.Unmarshal(m, b)
}
func (m *SearchConcertSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchConcertSongResp.Marshal(b, m, deterministic)
}
func (dst *SearchConcertSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchConcertSongResp.Merge(dst, src)
}
func (m *SearchConcertSongResp) XXX_Size() int {
	return xxx_messageInfo_SearchConcertSongResp.Size(m)
}
func (m *SearchConcertSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchConcertSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchConcertSongResp proto.InternalMessageInfo

func (m *SearchConcertSongResp) GetSongs() []*ConcertSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

type GetConcertSongListReq struct {
	Opts                 []*ConcertSongOpt `protobuf:"bytes,1,rep,name=opts,proto3" json:"opts,omitempty"`
	LoadMore             *ConcertLoadMore  `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	Count                uint32            `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetConcertSongListReq) Reset()         { *m = GetConcertSongListReq{} }
func (m *GetConcertSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongListReq) ProtoMessage()    {}
func (*GetConcertSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{15}
}
func (m *GetConcertSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongListReq.Unmarshal(m, b)
}
func (m *GetConcertSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongListReq.Merge(dst, src)
}
func (m *GetConcertSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongListReq.Size(m)
}
func (m *GetConcertSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongListReq proto.InternalMessageInfo

func (m *GetConcertSongListReq) GetOpts() []*ConcertSongOpt {
	if m != nil {
		return m.Opts
	}
	return nil
}

func (m *GetConcertSongListReq) GetLoadMore() *ConcertLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

func (m *GetConcertSongListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetConcertSongListResp struct {
	Songs                []*ConcertSong   `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	LoadMore             *ConcertLoadMore `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetConcertSongListResp) Reset()         { *m = GetConcertSongListResp{} }
func (m *GetConcertSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongListResp) ProtoMessage()    {}
func (*GetConcertSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{16}
}
func (m *GetConcertSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongListResp.Unmarshal(m, b)
}
func (m *GetConcertSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongListResp.Merge(dst, src)
}
func (m *GetConcertSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongListResp.Size(m)
}
func (m *GetConcertSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongListResp proto.InternalMessageInfo

func (m *GetConcertSongListResp) GetSongs() []*ConcertSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetConcertSongListResp) GetLoadMore() *ConcertLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type ConcertSong struct {
	Id                   string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Bg                   string                 `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	Author               string                 `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	HotVal               uint32                 `protobuf:"varint,5,opt,name=hot_val,json=hotVal,proto3" json:"hot_val,omitempty"`
	Lrc                  string                 `protobuf:"bytes,6,opt,name=lrc,proto3" json:"lrc,omitempty"`
	SheetMusicMap        map[string]*SheetMusic `protobuf:"bytes,7,rep,name=sheet_music_map,json=sheetMusicMap,proto3" json:"sheet_music_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	Language             string                 `protobuf:"bytes,8,opt,name=language,proto3" json:"language,omitempty"`
	Sex                  Sex                    `protobuf:"varint,9,opt,name=sex,proto3,enum=concert.Sex" json:"sex,omitempty"`
	Instrument           string                 `protobuf:"bytes,10,opt,name=instrument,proto3" json:"instrument,omitempty"`
	Stage                Stagetype              `protobuf:"varint,11,opt,name=stage,proto3,enum=concert.Stagetype" json:"stage,omitempty"`
	Duration             uint32                 `protobuf:"varint,12,opt,name=duration,proto3" json:"duration,omitempty"`
	LrcBeginMs           uint32                 `protobuf:"varint,13,opt,name=lrc_begin_ms,json=lrcBeginMs,proto3" json:"lrc_begin_ms,omitempty"`
	Singer               string                 `protobuf:"bytes,14,opt,name=singer,proto3" json:"singer,omitempty"`
	AccompanyUrl         string                 `protobuf:"bytes,15,opt,name=accompany_url,json=accompanyUrl,proto3" json:"accompany_url,omitempty"`
	AccompanyMd5         string                 `protobuf:"bytes,16,opt,name=accompany_md5,json=accompanyMd5,proto3" json:"accompany_md5,omitempty"`
	Bpm                  uint32                 `protobuf:"varint,17,opt,name=bpm,proto3" json:"bpm,omitempty"`
	IsOnline             bool                   `protobuf:"varint,18,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	Res                  []*Instrument          `protobuf:"bytes,19,rep,name=res,proto3" json:"res,omitempty"`
	IsNew                bool                   `protobuf:"varint,20,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ConcertSong) Reset()         { *m = ConcertSong{} }
func (m *ConcertSong) String() string { return proto.CompactTextString(m) }
func (*ConcertSong) ProtoMessage()    {}
func (*ConcertSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{17}
}
func (m *ConcertSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSong.Unmarshal(m, b)
}
func (m *ConcertSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSong.Marshal(b, m, deterministic)
}
func (dst *ConcertSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSong.Merge(dst, src)
}
func (m *ConcertSong) XXX_Size() int {
	return xxx_messageInfo_ConcertSong.Size(m)
}
func (m *ConcertSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSong.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSong proto.InternalMessageInfo

func (m *ConcertSong) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertSong) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ConcertSong) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *ConcertSong) GetAuthor() string {
	if m != nil {
		return m.Author
	}
	return ""
}

func (m *ConcertSong) GetHotVal() uint32 {
	if m != nil {
		return m.HotVal
	}
	return 0
}

func (m *ConcertSong) GetLrc() string {
	if m != nil {
		return m.Lrc
	}
	return ""
}

func (m *ConcertSong) GetSheetMusicMap() map[string]*SheetMusic {
	if m != nil {
		return m.SheetMusicMap
	}
	return nil
}

func (m *ConcertSong) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *ConcertSong) GetSex() Sex {
	if m != nil {
		return m.Sex
	}
	return Sex_All
}

func (m *ConcertSong) GetInstrument() string {
	if m != nil {
		return m.Instrument
	}
	return ""
}

func (m *ConcertSong) GetStage() Stagetype {
	if m != nil {
		return m.Stage
	}
	return Stagetype_Stagetype_UNDEFINED
}

func (m *ConcertSong) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *ConcertSong) GetLrcBeginMs() uint32 {
	if m != nil {
		return m.LrcBeginMs
	}
	return 0
}

func (m *ConcertSong) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *ConcertSong) GetAccompanyUrl() string {
	if m != nil {
		return m.AccompanyUrl
	}
	return ""
}

func (m *ConcertSong) GetAccompanyMd5() string {
	if m != nil {
		return m.AccompanyMd5
	}
	return ""
}

func (m *ConcertSong) GetBpm() uint32 {
	if m != nil {
		return m.Bpm
	}
	return 0
}

func (m *ConcertSong) GetIsOnline() bool {
	if m != nil {
		return m.IsOnline
	}
	return false
}

func (m *ConcertSong) GetRes() []*Instrument {
	if m != nil {
		return m.Res
	}
	return nil
}

func (m *ConcertSong) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type Instrument struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Midi                 string   `protobuf:"bytes,2,opt,name=midi,proto3" json:"midi,omitempty"`
	AccompanyUrl         string   `protobuf:"bytes,3,opt,name=accompany_url,json=accompanyUrl,proto3" json:"accompany_url,omitempty"`
	AccompanyMd5         string   `protobuf:"bytes,4,opt,name=accompany_md5,json=accompanyMd5,proto3" json:"accompany_md5,omitempty"`
	SameKeyInterval      uint32   `protobuf:"varint,5,opt,name=same_key_interval,json=sameKeyInterval,proto3" json:"same_key_interval,omitempty"`
	DifferentKeyInterval uint32   `protobuf:"varint,6,opt,name=different_key_interval,json=differentKeyInterval,proto3" json:"different_key_interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Instrument) Reset()         { *m = Instrument{} }
func (m *Instrument) String() string { return proto.CompactTextString(m) }
func (*Instrument) ProtoMessage()    {}
func (*Instrument) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{18}
}
func (m *Instrument) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Instrument.Unmarshal(m, b)
}
func (m *Instrument) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Instrument.Marshal(b, m, deterministic)
}
func (dst *Instrument) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Instrument.Merge(dst, src)
}
func (m *Instrument) XXX_Size() int {
	return xxx_messageInfo_Instrument.Size(m)
}
func (m *Instrument) XXX_DiscardUnknown() {
	xxx_messageInfo_Instrument.DiscardUnknown(m)
}

var xxx_messageInfo_Instrument proto.InternalMessageInfo

func (m *Instrument) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Instrument) GetMidi() string {
	if m != nil {
		return m.Midi
	}
	return ""
}

func (m *Instrument) GetAccompanyUrl() string {
	if m != nil {
		return m.AccompanyUrl
	}
	return ""
}

func (m *Instrument) GetAccompanyMd5() string {
	if m != nil {
		return m.AccompanyMd5
	}
	return ""
}

func (m *Instrument) GetSameKeyInterval() uint32 {
	if m != nil {
		return m.SameKeyInterval
	}
	return 0
}

func (m *Instrument) GetDifferentKeyInterval() uint32 {
	if m != nil {
		return m.DifferentKeyInterval
	}
	return 0
}

// 乐谱
type SheetMusic struct {
	RhythmPointList      []*RhythmPoint `protobuf:"bytes,1,rep,name=rhythm_point_list,json=rhythmPointList,proto3" json:"rhythm_point_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SheetMusic) Reset()         { *m = SheetMusic{} }
func (m *SheetMusic) String() string { return proto.CompactTextString(m) }
func (*SheetMusic) ProtoMessage()    {}
func (*SheetMusic) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{19}
}
func (m *SheetMusic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SheetMusic.Unmarshal(m, b)
}
func (m *SheetMusic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SheetMusic.Marshal(b, m, deterministic)
}
func (dst *SheetMusic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SheetMusic.Merge(dst, src)
}
func (m *SheetMusic) XXX_Size() int {
	return xxx_messageInfo_SheetMusic.Size(m)
}
func (m *SheetMusic) XXX_DiscardUnknown() {
	xxx_messageInfo_SheetMusic.DiscardUnknown(m)
}

var xxx_messageInfo_SheetMusic proto.InternalMessageInfo

func (m *SheetMusic) GetRhythmPointList() []*RhythmPoint {
	if m != nil {
		return m.RhythmPointList
	}
	return nil
}

type ConcertLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertLoadMore) Reset()         { *m = ConcertLoadMore{} }
func (m *ConcertLoadMore) String() string { return proto.CompactTextString(m) }
func (*ConcertLoadMore) ProtoMessage()    {}
func (*ConcertLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{20}
}
func (m *ConcertLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertLoadMore.Unmarshal(m, b)
}
func (m *ConcertLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertLoadMore.Marshal(b, m, deterministic)
}
func (dst *ConcertLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertLoadMore.Merge(dst, src)
}
func (m *ConcertLoadMore) XXX_Size() int {
	return xxx_messageInfo_ConcertLoadMore.Size(m)
}
func (m *ConcertLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertLoadMore proto.InternalMessageInfo

func (m *ConcertLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *ConcertLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

type ConcertSongOpt struct {
	Id                   string                `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string                `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Elems                []*ConcertSongOptElem `protobuf:"bytes,3,rep,name=elems,proto3" json:"elems,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ConcertSongOpt) Reset()         { *m = ConcertSongOpt{} }
func (m *ConcertSongOpt) String() string { return proto.CompactTextString(m) }
func (*ConcertSongOpt) ProtoMessage()    {}
func (*ConcertSongOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{21}
}
func (m *ConcertSongOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongOpt.Unmarshal(m, b)
}
func (m *ConcertSongOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongOpt.Marshal(b, m, deterministic)
}
func (dst *ConcertSongOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongOpt.Merge(dst, src)
}
func (m *ConcertSongOpt) XXX_Size() int {
	return xxx_messageInfo_ConcertSongOpt.Size(m)
}
func (m *ConcertSongOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongOpt.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongOpt proto.InternalMessageInfo

func (m *ConcertSongOpt) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertSongOpt) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ConcertSongOpt) GetElems() []*ConcertSongOptElem {
	if m != nil {
		return m.Elems
	}
	return nil
}

type ConcertSongOptElem struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertSongOptElem) Reset()         { *m = ConcertSongOptElem{} }
func (m *ConcertSongOptElem) String() string { return proto.CompactTextString(m) }
func (*ConcertSongOptElem) ProtoMessage()    {}
func (*ConcertSongOptElem) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{22}
}
func (m *ConcertSongOptElem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSongOptElem.Unmarshal(m, b)
}
func (m *ConcertSongOptElem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSongOptElem.Marshal(b, m, deterministic)
}
func (dst *ConcertSongOptElem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSongOptElem.Merge(dst, src)
}
func (m *ConcertSongOptElem) XXX_Size() int {
	return xxx_messageInfo_ConcertSongOptElem.Size(m)
}
func (m *ConcertSongOptElem) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSongOptElem.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSongOptElem proto.InternalMessageInfo

func (m *ConcertSongOptElem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertSongOptElem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// 获取演唱资源
type GetAllConcertResourceReq struct {
	Version              uint32   `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllConcertResourceReq) Reset()         { *m = GetAllConcertResourceReq{} }
func (m *GetAllConcertResourceReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertResourceReq) ProtoMessage()    {}
func (*GetAllConcertResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{23}
}
func (m *GetAllConcertResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertResourceReq.Unmarshal(m, b)
}
func (m *GetAllConcertResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertResourceReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertResourceReq.Merge(dst, src)
}
func (m *GetAllConcertResourceReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertResourceReq.Size(m)
}
func (m *GetAllConcertResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertResourceReq proto.InternalMessageInfo

func (m *GetAllConcertResourceReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetAllConcertResourceResp struct {
	Version              uint32                    `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	ResList              []*InstrumentRes          `protobuf:"bytes,2,rep,name=res_list,json=resList,proto3" json:"res_list,omitempty"`
	Decorations          []*ConcertStageDecoration `protobuf:"bytes,3,rep,name=decorations,proto3" json:"decorations,omitempty"`
	DefaultUrl           string                    `protobuf:"bytes,4,opt,name=default_url,json=defaultUrl,proto3" json:"default_url,omitempty"`
	DefaultMd5           string                    `protobuf:"bytes,5,opt,name=default_md5,json=defaultMd5,proto3" json:"default_md5,omitempty"`
	DefaultTransitionImg string                    `protobuf:"bytes,6,opt,name=default_transition_img,json=defaultTransitionImg,proto3" json:"default_transition_img,omitempty"`
	SonicWave            *ConcertRes               `protobuf:"bytes,7,opt,name=sonic_wave,json=sonicWave,proto3" json:"sonic_wave,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetAllConcertResourceResp) Reset()         { *m = GetAllConcertResourceResp{} }
func (m *GetAllConcertResourceResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertResourceResp) ProtoMessage()    {}
func (*GetAllConcertResourceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{24}
}
func (m *GetAllConcertResourceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertResourceResp.Unmarshal(m, b)
}
func (m *GetAllConcertResourceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertResourceResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertResourceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertResourceResp.Merge(dst, src)
}
func (m *GetAllConcertResourceResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertResourceResp.Size(m)
}
func (m *GetAllConcertResourceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertResourceResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertResourceResp proto.InternalMessageInfo

func (m *GetAllConcertResourceResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *GetAllConcertResourceResp) GetResList() []*InstrumentRes {
	if m != nil {
		return m.ResList
	}
	return nil
}

func (m *GetAllConcertResourceResp) GetDecorations() []*ConcertStageDecoration {
	if m != nil {
		return m.Decorations
	}
	return nil
}

func (m *GetAllConcertResourceResp) GetDefaultUrl() string {
	if m != nil {
		return m.DefaultUrl
	}
	return ""
}

func (m *GetAllConcertResourceResp) GetDefaultMd5() string {
	if m != nil {
		return m.DefaultMd5
	}
	return ""
}

func (m *GetAllConcertResourceResp) GetDefaultTransitionImg() string {
	if m != nil {
		return m.DefaultTransitionImg
	}
	return ""
}

func (m *GetAllConcertResourceResp) GetSonicWave() *ConcertRes {
	if m != nil {
		return m.SonicWave
	}
	return nil
}

type ConcertRes struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertRes) Reset()         { *m = ConcertRes{} }
func (m *ConcertRes) String() string { return proto.CompactTextString(m) }
func (*ConcertRes) ProtoMessage()    {}
func (*ConcertRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{25}
}
func (m *ConcertRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertRes.Unmarshal(m, b)
}
func (m *ConcertRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertRes.Marshal(b, m, deterministic)
}
func (dst *ConcertRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertRes.Merge(dst, src)
}
func (m *ConcertRes) XXX_Size() int {
	return xxx_messageInfo_ConcertRes.Size(m)
}
func (m *ConcertRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertRes.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertRes proto.InternalMessageInfo

func (m *ConcertRes) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ConcertRes) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type InstrumentRes struct {
	Role                 BandRole           `protobuf:"varint,1,opt,name=role,proto3,enum=concert.BandRole" json:"role,omitempty"`
	MicId                uint32             `protobuf:"varint,2,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	Name                 string             `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Audios               []*InstrumentAudio `protobuf:"bytes,4,rep,name=audios,proto3" json:"audios,omitempty"`
	HoldMicAudio         *InstrumentAudio   `protobuf:"bytes,5,opt,name=hold_mic_audio,json=holdMicAudio,proto3" json:"hold_mic_audio,omitempty"`
	NormalMicSeat        string             `protobuf:"bytes,6,opt,name=normal_mic_seat,json=normalMicSeat,proto3" json:"normal_mic_seat,omitempty"`
	HoldMicSeat          string             `protobuf:"bytes,7,opt,name=hold_mic_seat,json=holdMicSeat,proto3" json:"hold_mic_seat,omitempty"`
	HoldMicLottie        *ConcertRes        `protobuf:"bytes,8,opt,name=hold_mic_lottie,json=holdMicLottie,proto3" json:"hold_mic_lottie,omitempty"`
	OnMicLottie          *ConcertRes        `protobuf:"bytes,9,opt,name=on_mic_lottie,json=onMicLottie,proto3" json:"on_mic_lottie,omitempty"`
	EmptySeatBg          string             `protobuf:"bytes,10,opt,name=empty_seat_bg,json=emptySeatBg,proto3" json:"empty_seat_bg,omitempty"`
	HoldSeatBg           string             `protobuf:"bytes,11,opt,name=hold_seat_bg,json=holdSeatBg,proto3" json:"hold_seat_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *InstrumentRes) Reset()         { *m = InstrumentRes{} }
func (m *InstrumentRes) String() string { return proto.CompactTextString(m) }
func (*InstrumentRes) ProtoMessage()    {}
func (*InstrumentRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{26}
}
func (m *InstrumentRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstrumentRes.Unmarshal(m, b)
}
func (m *InstrumentRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstrumentRes.Marshal(b, m, deterministic)
}
func (dst *InstrumentRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstrumentRes.Merge(dst, src)
}
func (m *InstrumentRes) XXX_Size() int {
	return xxx_messageInfo_InstrumentRes.Size(m)
}
func (m *InstrumentRes) XXX_DiscardUnknown() {
	xxx_messageInfo_InstrumentRes.DiscardUnknown(m)
}

var xxx_messageInfo_InstrumentRes proto.InternalMessageInfo

func (m *InstrumentRes) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *InstrumentRes) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *InstrumentRes) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InstrumentRes) GetAudios() []*InstrumentAudio {
	if m != nil {
		return m.Audios
	}
	return nil
}

func (m *InstrumentRes) GetHoldMicAudio() *InstrumentAudio {
	if m != nil {
		return m.HoldMicAudio
	}
	return nil
}

func (m *InstrumentRes) GetNormalMicSeat() string {
	if m != nil {
		return m.NormalMicSeat
	}
	return ""
}

func (m *InstrumentRes) GetHoldMicSeat() string {
	if m != nil {
		return m.HoldMicSeat
	}
	return ""
}

func (m *InstrumentRes) GetHoldMicLottie() *ConcertRes {
	if m != nil {
		return m.HoldMicLottie
	}
	return nil
}

func (m *InstrumentRes) GetOnMicLottie() *ConcertRes {
	if m != nil {
		return m.OnMicLottie
	}
	return nil
}

func (m *InstrumentRes) GetEmptySeatBg() string {
	if m != nil {
		return m.EmptySeatBg
	}
	return ""
}

func (m *InstrumentRes) GetHoldSeatBg() string {
	if m != nil {
		return m.HoldSeatBg
	}
	return ""
}

type ConcertStageDecoration struct {
	Stagetype            Stagetype       `protobuf:"varint,1,opt,name=stagetype,proto3,enum=concert.Stagetype" json:"stagetype,omitempty"`
	LightOnBg            *ConcertStageBg `protobuf:"bytes,2,opt,name=light_on_bg,json=lightOnBg,proto3" json:"light_on_bg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ConcertStageDecoration) Reset()         { *m = ConcertStageDecoration{} }
func (m *ConcertStageDecoration) String() string { return proto.CompactTextString(m) }
func (*ConcertStageDecoration) ProtoMessage()    {}
func (*ConcertStageDecoration) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{27}
}
func (m *ConcertStageDecoration) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertStageDecoration.Unmarshal(m, b)
}
func (m *ConcertStageDecoration) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertStageDecoration.Marshal(b, m, deterministic)
}
func (dst *ConcertStageDecoration) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertStageDecoration.Merge(dst, src)
}
func (m *ConcertStageDecoration) XXX_Size() int {
	return xxx_messageInfo_ConcertStageDecoration.Size(m)
}
func (m *ConcertStageDecoration) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertStageDecoration.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertStageDecoration proto.InternalMessageInfo

func (m *ConcertStageDecoration) GetStagetype() Stagetype {
	if m != nil {
		return m.Stagetype
	}
	return Stagetype_Stagetype_UNDEFINED
}

func (m *ConcertStageDecoration) GetLightOnBg() *ConcertStageBg {
	if m != nil {
		return m.LightOnBg
	}
	return nil
}

type ConcertStageBg struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,2,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConcertStageBg) Reset()         { *m = ConcertStageBg{} }
func (m *ConcertStageBg) String() string { return proto.CompactTextString(m) }
func (*ConcertStageBg) ProtoMessage()    {}
func (*ConcertStageBg) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{28}
}
func (m *ConcertStageBg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertStageBg.Unmarshal(m, b)
}
func (m *ConcertStageBg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertStageBg.Marshal(b, m, deterministic)
}
func (dst *ConcertStageBg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertStageBg.Merge(dst, src)
}
func (m *ConcertStageBg) XXX_Size() int {
	return xxx_messageInfo_ConcertStageBg.Size(m)
}
func (m *ConcertStageBg) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertStageBg.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertStageBg proto.InternalMessageInfo

func (m *ConcertStageBg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ConcertStageBg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

// 点歌
type StartConcertSingingReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartConcertSingingReq) Reset()         { *m = StartConcertSingingReq{} }
func (m *StartConcertSingingReq) String() string { return proto.CompactTextString(m) }
func (*StartConcertSingingReq) ProtoMessage()    {}
func (*StartConcertSingingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{29}
}
func (m *StartConcertSingingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartConcertSingingReq.Unmarshal(m, b)
}
func (m *StartConcertSingingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartConcertSingingReq.Marshal(b, m, deterministic)
}
func (dst *StartConcertSingingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartConcertSingingReq.Merge(dst, src)
}
func (m *StartConcertSingingReq) XXX_Size() int {
	return xxx_messageInfo_StartConcertSingingReq.Size(m)
}
func (m *StartConcertSingingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartConcertSingingReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartConcertSingingReq proto.InternalMessageInfo

func (m *StartConcertSingingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartConcertSingingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartConcertSingingReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type StartConcertSingingResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartConcertSingingResp) Reset()         { *m = StartConcertSingingResp{} }
func (m *StartConcertSingingResp) String() string { return proto.CompactTextString(m) }
func (*StartConcertSingingResp) ProtoMessage()    {}
func (*StartConcertSingingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{30}
}
func (m *StartConcertSingingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartConcertSingingResp.Unmarshal(m, b)
}
func (m *StartConcertSingingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartConcertSingingResp.Marshal(b, m, deterministic)
}
func (dst *StartConcertSingingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartConcertSingingResp.Merge(dst, src)
}
func (m *StartConcertSingingResp) XXX_Size() int {
	return xxx_messageInfo_StartConcertSingingResp.Size(m)
}
func (m *StartConcertSingingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartConcertSingingResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartConcertSingingResp proto.InternalMessageInfo

// 获取乐谱
type GetMusicBookReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicBookReq) Reset()         { *m = GetMusicBookReq{} }
func (m *GetMusicBookReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicBookReq) ProtoMessage()    {}
func (*GetMusicBookReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{31}
}
func (m *GetMusicBookReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBookReq.Unmarshal(m, b)
}
func (m *GetMusicBookReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBookReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicBookReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBookReq.Merge(dst, src)
}
func (m *GetMusicBookReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicBookReq.Size(m)
}
func (m *GetMusicBookReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBookReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBookReq proto.InternalMessageInfo

func (m *GetMusicBookReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetMusicBookReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetMusicBookReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type GetMusicBookResp struct {
	MusicBook            *MusicBook `protobuf:"bytes,1,opt,name=music_book,json=musicBook,proto3" json:"music_book,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetMusicBookResp) Reset()         { *m = GetMusicBookResp{} }
func (m *GetMusicBookResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicBookResp) ProtoMessage()    {}
func (*GetMusicBookResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{32}
}
func (m *GetMusicBookResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicBookResp.Unmarshal(m, b)
}
func (m *GetMusicBookResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicBookResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicBookResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicBookResp.Merge(dst, src)
}
func (m *GetMusicBookResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicBookResp.Size(m)
}
func (m *GetMusicBookResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicBookResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicBookResp proto.InternalMessageInfo

func (m *GetMusicBookResp) GetMusicBook() *MusicBook {
	if m != nil {
		return m.MusicBook
	}
	return nil
}

// 完成下载
type CompleteDownloadingMusicBookReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompleteDownloadingMusicBookReq) Reset()         { *m = CompleteDownloadingMusicBookReq{} }
func (m *CompleteDownloadingMusicBookReq) String() string { return proto.CompactTextString(m) }
func (*CompleteDownloadingMusicBookReq) ProtoMessage()    {}
func (*CompleteDownloadingMusicBookReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{33}
}
func (m *CompleteDownloadingMusicBookReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteDownloadingMusicBookReq.Unmarshal(m, b)
}
func (m *CompleteDownloadingMusicBookReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteDownloadingMusicBookReq.Marshal(b, m, deterministic)
}
func (dst *CompleteDownloadingMusicBookReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteDownloadingMusicBookReq.Merge(dst, src)
}
func (m *CompleteDownloadingMusicBookReq) XXX_Size() int {
	return xxx_messageInfo_CompleteDownloadingMusicBookReq.Size(m)
}
func (m *CompleteDownloadingMusicBookReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteDownloadingMusicBookReq.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteDownloadingMusicBookReq proto.InternalMessageInfo

func (m *CompleteDownloadingMusicBookReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CompleteDownloadingMusicBookReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CompleteDownloadingMusicBookReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type CompleteDownloadingMusicBookResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompleteDownloadingMusicBookResp) Reset()         { *m = CompleteDownloadingMusicBookResp{} }
func (m *CompleteDownloadingMusicBookResp) String() string { return proto.CompactTextString(m) }
func (*CompleteDownloadingMusicBookResp) ProtoMessage()    {}
func (*CompleteDownloadingMusicBookResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{34}
}
func (m *CompleteDownloadingMusicBookResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteDownloadingMusicBookResp.Unmarshal(m, b)
}
func (m *CompleteDownloadingMusicBookResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteDownloadingMusicBookResp.Marshal(b, m, deterministic)
}
func (dst *CompleteDownloadingMusicBookResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteDownloadingMusicBookResp.Merge(dst, src)
}
func (m *CompleteDownloadingMusicBookResp) XXX_Size() int {
	return xxx_messageInfo_CompleteDownloadingMusicBookResp.Size(m)
}
func (m *CompleteDownloadingMusicBookResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteDownloadingMusicBookResp.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteDownloadingMusicBookResp proto.InternalMessageInfo

// 停止演唱
type StopConcertSingingReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopConcertSingingReq) Reset()         { *m = StopConcertSingingReq{} }
func (m *StopConcertSingingReq) String() string { return proto.CompactTextString(m) }
func (*StopConcertSingingReq) ProtoMessage()    {}
func (*StopConcertSingingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{35}
}
func (m *StopConcertSingingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopConcertSingingReq.Unmarshal(m, b)
}
func (m *StopConcertSingingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopConcertSingingReq.Marshal(b, m, deterministic)
}
func (dst *StopConcertSingingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopConcertSingingReq.Merge(dst, src)
}
func (m *StopConcertSingingReq) XXX_Size() int {
	return xxx_messageInfo_StopConcertSingingReq.Size(m)
}
func (m *StopConcertSingingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopConcertSingingReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopConcertSingingReq proto.InternalMessageInfo

func (m *StopConcertSingingReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StopConcertSingingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StopConcertSingingReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type StopConcertSingingResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopConcertSingingResp) Reset()         { *m = StopConcertSingingResp{} }
func (m *StopConcertSingingResp) String() string { return proto.CompactTextString(m) }
func (*StopConcertSingingResp) ProtoMessage()    {}
func (*StopConcertSingingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{36}
}
func (m *StopConcertSingingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopConcertSingingResp.Unmarshal(m, b)
}
func (m *StopConcertSingingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopConcertSingingResp.Marshal(b, m, deterministic)
}
func (dst *StopConcertSingingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopConcertSingingResp.Merge(dst, src)
}
func (m *StopConcertSingingResp) XXX_Size() int {
	return xxx_messageInfo_StopConcertSingingResp.Size(m)
}
func (m *StopConcertSingingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopConcertSingingResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopConcertSingingResp proto.InternalMessageInfo

// 获取演唱信息
type GetConcertInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConcertInfoReq) Reset()         { *m = GetConcertInfoReq{} }
func (m *GetConcertInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertInfoReq) ProtoMessage()    {}
func (*GetConcertInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{37}
}
func (m *GetConcertInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertInfoReq.Unmarshal(m, b)
}
func (m *GetConcertInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertInfoReq.Merge(dst, src)
}
func (m *GetConcertInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertInfoReq.Size(m)
}
func (m *GetConcertInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertInfoReq proto.InternalMessageInfo

func (m *GetConcertInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetConcertInfoResp struct {
	Info                 *SingingInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Lrc                  string       `protobuf:"bytes,2,opt,name=lrc,proto3" json:"lrc,omitempty"`
	BackingTrack         *ConcertRes  `protobuf:"bytes,3,opt,name=backing_track,json=backingTrack,proto3" json:"backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConcertInfoResp) Reset()         { *m = GetConcertInfoResp{} }
func (m *GetConcertInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertInfoResp) ProtoMessage()    {}
func (*GetConcertInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{38}
}
func (m *GetConcertInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertInfoResp.Unmarshal(m, b)
}
func (m *GetConcertInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertInfoResp.Merge(dst, src)
}
func (m *GetConcertInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertInfoResp.Size(m)
}
func (m *GetConcertInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertInfoResp proto.InternalMessageInfo

func (m *GetConcertInfoResp) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetConcertInfoResp) GetLrc() string {
	if m != nil {
		return m.Lrc
	}
	return ""
}

func (m *GetConcertInfoResp) GetBackingTrack() *ConcertRes {
	if m != nil {
		return m.BackingTrack
	}
	return nil
}

// 批量获取演唱信息
type BatchGetConcertInfoReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetConcertInfoReq) Reset()         { *m = BatchGetConcertInfoReq{} }
func (m *BatchGetConcertInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetConcertInfoReq) ProtoMessage()    {}
func (*BatchGetConcertInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{39}
}
func (m *BatchGetConcertInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetConcertInfoReq.Unmarshal(m, b)
}
func (m *BatchGetConcertInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetConcertInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetConcertInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetConcertInfoReq.Merge(dst, src)
}
func (m *BatchGetConcertInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetConcertInfoReq.Size(m)
}
func (m *BatchGetConcertInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetConcertInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetConcertInfoReq proto.InternalMessageInfo

func (m *BatchGetConcertInfoReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type BatchGetConcertInfoResp struct {
	Infos                []*SingingInfo   `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	MicIdToRole          map[uint32]*Role `protobuf:"bytes,2,rep,name=mic_id_to_role,json=micIdToRole,proto3" json:"mic_id_to_role,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetConcertInfoResp) Reset()         { *m = BatchGetConcertInfoResp{} }
func (m *BatchGetConcertInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetConcertInfoResp) ProtoMessage()    {}
func (*BatchGetConcertInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{40}
}
func (m *BatchGetConcertInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetConcertInfoResp.Unmarshal(m, b)
}
func (m *BatchGetConcertInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetConcertInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetConcertInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetConcertInfoResp.Merge(dst, src)
}
func (m *BatchGetConcertInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetConcertInfoResp.Size(m)
}
func (m *BatchGetConcertInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetConcertInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetConcertInfoResp proto.InternalMessageInfo

func (m *BatchGetConcertInfoResp) GetInfos() []*SingingInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *BatchGetConcertInfoResp) GetMicIdToRole() map[uint32]*Role {
	if m != nil {
		return m.MicIdToRole
	}
	return nil
}

type Role struct {
	Role                 BandRole `protobuf:"varint,1,opt,name=role,proto3,enum=concert.BandRole" json:"role,omitempty"`
	RoleName             string   `protobuf:"bytes,2,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Role) Reset()         { *m = Role{} }
func (m *Role) String() string { return proto.CompactTextString(m) }
func (*Role) ProtoMessage()    {}
func (*Role) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{41}
}
func (m *Role) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Role.Unmarshal(m, b)
}
func (m *Role) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Role.Marshal(b, m, deterministic)
}
func (dst *Role) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Role.Merge(dst, src)
}
func (m *Role) XXX_Size() int {
	return xxx_messageInfo_Role.Size(m)
}
func (m *Role) XXX_DiscardUnknown() {
	xxx_messageInfo_Role.DiscardUnknown(m)
}

var xxx_messageInfo_Role proto.InternalMessageInfo

func (m *Role) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *Role) GetRoleName() string {
	if m != nil {
		return m.RoleName
	}
	return ""
}

type SingingInfo struct {
	ChannelId            uint32             `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32             `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Song                 *ConcertSimpleSong `protobuf:"bytes,3,opt,name=song,proto3" json:"song,omitempty"`
	Members              []*BandMember      `protobuf:"bytes,4,rep,name=members,proto3" json:"members,omitempty"`
	Stage                ConcertGameStage   `protobuf:"varint,5,opt,name=stage,proto3,enum=concert.ConcertGameStage" json:"stage,omitempty"`
	StageUpdatedAt       uint32             `protobuf:"varint,6,opt,name=stage_updated_at,json=stageUpdatedAt,proto3" json:"stage_updated_at,omitempty"`
	Version              uint64             `protobuf:"varint,7,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SingingInfo) Reset()         { *m = SingingInfo{} }
func (m *SingingInfo) String() string { return proto.CompactTextString(m) }
func (*SingingInfo) ProtoMessage()    {}
func (*SingingInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{42}
}
func (m *SingingInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingInfo.Unmarshal(m, b)
}
func (m *SingingInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingInfo.Marshal(b, m, deterministic)
}
func (dst *SingingInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingInfo.Merge(dst, src)
}
func (m *SingingInfo) XXX_Size() int {
	return xxx_messageInfo_SingingInfo.Size(m)
}
func (m *SingingInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingingInfo proto.InternalMessageInfo

func (m *SingingInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SingingInfo) GetSong() *ConcertSimpleSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func (m *SingingInfo) GetMembers() []*BandMember {
	if m != nil {
		return m.Members
	}
	return nil
}

func (m *SingingInfo) GetStage() ConcertGameStage {
	if m != nil {
		return m.Stage
	}
	return ConcertGameStage_ConcertGameStage_UNDEFINED
}

func (m *SingingInfo) GetStageUpdatedAt() uint32 {
	if m != nil {
		return m.StageUpdatedAt
	}
	return 0
}

func (m *SingingInfo) GetVersion() uint64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type ConcertSimpleSong struct {
	SongId               string     `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string     `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	StageOpenDuration    uint32     `protobuf:"varint,3,opt,name=stage_open_duration,json=stageOpenDuration,proto3" json:"stage_open_duration,omitempty"`
	FirstWordMs          uint32     `protobuf:"varint,4,opt,name=first_word_ms,json=firstWordMs,proto3" json:"first_word_ms,omitempty"`
	TotalDuration        uint32     `protobuf:"varint,5,opt,name=total_duration,json=totalDuration,proto3" json:"total_duration,omitempty"`
	Stagetype            Stagetype  `protobuf:"varint,6,opt,name=stagetype,proto3,enum=concert.Stagetype" json:"stagetype,omitempty"`
	ChosenBy             uint32     `protobuf:"varint,7,opt,name=ChosenBy,proto3" json:"ChosenBy,omitempty"`
	OpenBackingTrack     bool       `protobuf:"varint,8,opt,name=open_backing_track,json=openBackingTrack,proto3" json:"open_backing_track,omitempty"`
	BandRoles            []BandRole `protobuf:"varint,9,rep,packed,name=band_roles,json=bandRoles,proto3,enum=concert.BandRole" json:"band_roles,omitempty"`
	Deviation            uint32     `protobuf:"varint,10,opt,name=deviation,proto3" json:"deviation,omitempty"`
	ReactionTime         uint32     `protobuf:"varint,11,opt,name=reaction_time,json=reactionTime,proto3" json:"reaction_time,omitempty"`
	SecondaryDeviation   uint32     `protobuf:"varint,12,opt,name=secondary_deviation,json=secondaryDeviation,proto3" json:"secondary_deviation,omitempty"`
	LongDecisionInterval uint32     `protobuf:"varint,13,opt,name=long_decision_interval,json=longDecisionInterval,proto3" json:"long_decision_interval,omitempty"`
	JudgeAllCtrlZero     bool       `protobuf:"varint,14,opt,name=judge_all_ctrl_zero,json=judgeAllCtrlZero,proto3" json:"judge_all_ctrl_zero,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ConcertSimpleSong) Reset()         { *m = ConcertSimpleSong{} }
func (m *ConcertSimpleSong) String() string { return proto.CompactTextString(m) }
func (*ConcertSimpleSong) ProtoMessage()    {}
func (*ConcertSimpleSong) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{43}
}
func (m *ConcertSimpleSong) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertSimpleSong.Unmarshal(m, b)
}
func (m *ConcertSimpleSong) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertSimpleSong.Marshal(b, m, deterministic)
}
func (dst *ConcertSimpleSong) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertSimpleSong.Merge(dst, src)
}
func (m *ConcertSimpleSong) XXX_Size() int {
	return xxx_messageInfo_ConcertSimpleSong.Size(m)
}
func (m *ConcertSimpleSong) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertSimpleSong.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertSimpleSong proto.InternalMessageInfo

func (m *ConcertSimpleSong) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ConcertSimpleSong) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *ConcertSimpleSong) GetStageOpenDuration() uint32 {
	if m != nil {
		return m.StageOpenDuration
	}
	return 0
}

func (m *ConcertSimpleSong) GetFirstWordMs() uint32 {
	if m != nil {
		return m.FirstWordMs
	}
	return 0
}

func (m *ConcertSimpleSong) GetTotalDuration() uint32 {
	if m != nil {
		return m.TotalDuration
	}
	return 0
}

func (m *ConcertSimpleSong) GetStagetype() Stagetype {
	if m != nil {
		return m.Stagetype
	}
	return Stagetype_Stagetype_UNDEFINED
}

func (m *ConcertSimpleSong) GetChosenBy() uint32 {
	if m != nil {
		return m.ChosenBy
	}
	return 0
}

func (m *ConcertSimpleSong) GetOpenBackingTrack() bool {
	if m != nil {
		return m.OpenBackingTrack
	}
	return false
}

func (m *ConcertSimpleSong) GetBandRoles() []BandRole {
	if m != nil {
		return m.BandRoles
	}
	return nil
}

func (m *ConcertSimpleSong) GetDeviation() uint32 {
	if m != nil {
		return m.Deviation
	}
	return 0
}

func (m *ConcertSimpleSong) GetReactionTime() uint32 {
	if m != nil {
		return m.ReactionTime
	}
	return 0
}

func (m *ConcertSimpleSong) GetSecondaryDeviation() uint32 {
	if m != nil {
		return m.SecondaryDeviation
	}
	return 0
}

func (m *ConcertSimpleSong) GetLongDecisionInterval() uint32 {
	if m != nil {
		return m.LongDecisionInterval
	}
	return 0
}

func (m *ConcertSimpleSong) GetJudgeAllCtrlZero() bool {
	if m != nil {
		return m.JudgeAllCtrlZero
	}
	return false
}

type BandMember struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Role                 BandRole `protobuf:"varint,2,opt,name=role,proto3,enum=concert.BandRole" json:"role,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	CanPushStream        bool     `protobuf:"varint,6,opt,name=can_push_stream,json=canPushStream,proto3" json:"can_push_stream,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BandMember) Reset()         { *m = BandMember{} }
func (m *BandMember) String() string { return proto.CompactTextString(m) }
func (*BandMember) ProtoMessage()    {}
func (*BandMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{44}
}
func (m *BandMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BandMember.Unmarshal(m, b)
}
func (m *BandMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BandMember.Marshal(b, m, deterministic)
}
func (dst *BandMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BandMember.Merge(dst, src)
}
func (m *BandMember) XXX_Size() int {
	return xxx_messageInfo_BandMember.Size(m)
}
func (m *BandMember) XXX_DiscardUnknown() {
	xxx_messageInfo_BandMember.DiscardUnknown(m)
}

var xxx_messageInfo_BandMember proto.InternalMessageInfo

func (m *BandMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BandMember) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *BandMember) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BandMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *BandMember) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *BandMember) GetCanPushStream() bool {
	if m != nil {
		return m.CanPushStream
	}
	return false
}

type MusicBook struct {
	Role                 BandRole             `protobuf:"varint,1,opt,name=role,proto3,enum=concert.BandRole" json:"role,omitempty"`
	KeyMapRhythmPoints   []*KeyMapRhythmPoint `protobuf:"bytes,2,rep,name=key_map_rhythm_points,json=keyMapRhythmPoints,proto3" json:"key_map_rhythm_points,omitempty"`
	BackingTrack         *ConcertRes          `protobuf:"bytes,3,opt,name=backing_track,json=backingTrack,proto3" json:"backing_track,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *MusicBook) Reset()         { *m = MusicBook{} }
func (m *MusicBook) String() string { return proto.CompactTextString(m) }
func (*MusicBook) ProtoMessage()    {}
func (*MusicBook) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{45}
}
func (m *MusicBook) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MusicBook.Unmarshal(m, b)
}
func (m *MusicBook) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MusicBook.Marshal(b, m, deterministic)
}
func (dst *MusicBook) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MusicBook.Merge(dst, src)
}
func (m *MusicBook) XXX_Size() int {
	return xxx_messageInfo_MusicBook.Size(m)
}
func (m *MusicBook) XXX_DiscardUnknown() {
	xxx_messageInfo_MusicBook.DiscardUnknown(m)
}

var xxx_messageInfo_MusicBook proto.InternalMessageInfo

func (m *MusicBook) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *MusicBook) GetKeyMapRhythmPoints() []*KeyMapRhythmPoint {
	if m != nil {
		return m.KeyMapRhythmPoints
	}
	return nil
}

func (m *MusicBook) GetBackingTrack() *ConcertRes {
	if m != nil {
		return m.BackingTrack
	}
	return nil
}

type KeyMapRhythmPoint struct {
	KeyMapType           KeyMapType `protobuf:"varint,1,opt,name=key_map_type,json=keyMapType,proto3,enum=concert.KeyMapType" json:"key_map_type,omitempty"`
	NoteOnAt             uint32     `protobuf:"varint,2,opt,name=note_on_at,json=noteOnAt,proto3" json:"note_on_at,omitempty"`
	NoteOffAt            uint32     `protobuf:"varint,3,opt,name=note_off_at,json=noteOffAt,proto3" json:"note_off_at,omitempty"`
	FromKeyMap           uint32     `protobuf:"varint,4,opt,name=from_key_map,json=fromKeyMap,proto3" json:"from_key_map,omitempty"`
	ToKeyMap             uint32     `protobuf:"varint,5,opt,name=to_key_map,json=toKeyMap,proto3" json:"to_key_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *KeyMapRhythmPoint) Reset()         { *m = KeyMapRhythmPoint{} }
func (m *KeyMapRhythmPoint) String() string { return proto.CompactTextString(m) }
func (*KeyMapRhythmPoint) ProtoMessage()    {}
func (*KeyMapRhythmPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{46}
}
func (m *KeyMapRhythmPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_KeyMapRhythmPoint.Unmarshal(m, b)
}
func (m *KeyMapRhythmPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_KeyMapRhythmPoint.Marshal(b, m, deterministic)
}
func (dst *KeyMapRhythmPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_KeyMapRhythmPoint.Merge(dst, src)
}
func (m *KeyMapRhythmPoint) XXX_Size() int {
	return xxx_messageInfo_KeyMapRhythmPoint.Size(m)
}
func (m *KeyMapRhythmPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_KeyMapRhythmPoint.DiscardUnknown(m)
}

var xxx_messageInfo_KeyMapRhythmPoint proto.InternalMessageInfo

func (m *KeyMapRhythmPoint) GetKeyMapType() KeyMapType {
	if m != nil {
		return m.KeyMapType
	}
	return KeyMapType_KeyMapType_UNDEFINED
}

func (m *KeyMapRhythmPoint) GetNoteOnAt() uint32 {
	if m != nil {
		return m.NoteOnAt
	}
	return 0
}

func (m *KeyMapRhythmPoint) GetNoteOffAt() uint32 {
	if m != nil {
		return m.NoteOffAt
	}
	return 0
}

func (m *KeyMapRhythmPoint) GetFromKeyMap() uint32 {
	if m != nil {
		return m.FromKeyMap
	}
	return 0
}

func (m *KeyMapRhythmPoint) GetToKeyMap() uint32 {
	if m != nil {
		return m.ToKeyMap
	}
	return 0
}

type InstrumentAudio struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Tone                 string   `protobuf:"bytes,3,opt,name=tone,proto3" json:"tone,omitempty"`
	Chord                string   `protobuf:"bytes,4,opt,name=chord,proto3" json:"chord,omitempty"`
	KeyMap               uint32   `protobuf:"varint,5,opt,name=key_map,json=keyMap,proto3" json:"key_map,omitempty"`
	Url                  string   `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,7,opt,name=md5,proto3" json:"md5,omitempty"`
	ConcertSongId        string   `protobuf:"bytes,8,opt,name=concert_song_id,json=concertSongId,proto3" json:"concert_song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InstrumentAudio) Reset()         { *m = InstrumentAudio{} }
func (m *InstrumentAudio) String() string { return proto.CompactTextString(m) }
func (*InstrumentAudio) ProtoMessage()    {}
func (*InstrumentAudio) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{47}
}
func (m *InstrumentAudio) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InstrumentAudio.Unmarshal(m, b)
}
func (m *InstrumentAudio) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InstrumentAudio.Marshal(b, m, deterministic)
}
func (dst *InstrumentAudio) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InstrumentAudio.Merge(dst, src)
}
func (m *InstrumentAudio) XXX_Size() int {
	return xxx_messageInfo_InstrumentAudio.Size(m)
}
func (m *InstrumentAudio) XXX_DiscardUnknown() {
	xxx_messageInfo_InstrumentAudio.DiscardUnknown(m)
}

var xxx_messageInfo_InstrumentAudio proto.InternalMessageInfo

func (m *InstrumentAudio) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *InstrumentAudio) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *InstrumentAudio) GetTone() string {
	if m != nil {
		return m.Tone
	}
	return ""
}

func (m *InstrumentAudio) GetChord() string {
	if m != nil {
		return m.Chord
	}
	return ""
}

func (m *InstrumentAudio) GetKeyMap() uint32 {
	if m != nil {
		return m.KeyMap
	}
	return 0
}

func (m *InstrumentAudio) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *InstrumentAudio) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *InstrumentAudio) GetConcertSongId() string {
	if m != nil {
		return m.ConcertSongId
	}
	return ""
}

type RhythmPoint struct {
	Ms                   uint32   `protobuf:"varint,1,opt,name=ms,proto3" json:"ms,omitempty"`
	Chord                string   `protobuf:"bytes,2,opt,name=chord,proto3" json:"chord,omitempty"`
	KeyMap               uint32   `protobuf:"varint,3,opt,name=key_map,json=keyMap,proto3" json:"key_map,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RhythmPoint) Reset()         { *m = RhythmPoint{} }
func (m *RhythmPoint) String() string { return proto.CompactTextString(m) }
func (*RhythmPoint) ProtoMessage()    {}
func (*RhythmPoint) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{48}
}
func (m *RhythmPoint) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RhythmPoint.Unmarshal(m, b)
}
func (m *RhythmPoint) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RhythmPoint.Marshal(b, m, deterministic)
}
func (dst *RhythmPoint) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RhythmPoint.Merge(dst, src)
}
func (m *RhythmPoint) XXX_Size() int {
	return xxx_messageInfo_RhythmPoint.Size(m)
}
func (m *RhythmPoint) XXX_DiscardUnknown() {
	xxx_messageInfo_RhythmPoint.DiscardUnknown(m)
}

var xxx_messageInfo_RhythmPoint proto.InternalMessageInfo

func (m *RhythmPoint) GetMs() uint32 {
	if m != nil {
		return m.Ms
	}
	return 0
}

func (m *RhythmPoint) GetChord() string {
	if m != nil {
		return m.Chord
	}
	return ""
}

func (m *RhythmPoint) GetKeyMap() uint32 {
	if m != nil {
		return m.KeyMap
	}
	return 0
}

type LostHeartReq struct {
	List                 []*LostHeartItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *LostHeartReq) Reset()         { *m = LostHeartReq{} }
func (m *LostHeartReq) String() string { return proto.CompactTextString(m) }
func (*LostHeartReq) ProtoMessage()    {}
func (*LostHeartReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{49}
}
func (m *LostHeartReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LostHeartReq.Unmarshal(m, b)
}
func (m *LostHeartReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LostHeartReq.Marshal(b, m, deterministic)
}
func (dst *LostHeartReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LostHeartReq.Merge(dst, src)
}
func (m *LostHeartReq) XXX_Size() int {
	return xxx_messageInfo_LostHeartReq.Size(m)
}
func (m *LostHeartReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LostHeartReq.DiscardUnknown(m)
}

var xxx_messageInfo_LostHeartReq proto.InternalMessageInfo

func (m *LostHeartReq) GetList() []*LostHeartItem {
	if m != nil {
		return m.List
	}
	return nil
}

type LostHeartItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LostHeartItem) Reset()         { *m = LostHeartItem{} }
func (m *LostHeartItem) String() string { return proto.CompactTextString(m) }
func (*LostHeartItem) ProtoMessage()    {}
func (*LostHeartItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{50}
}
func (m *LostHeartItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LostHeartItem.Unmarshal(m, b)
}
func (m *LostHeartItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LostHeartItem.Marshal(b, m, deterministic)
}
func (dst *LostHeartItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LostHeartItem.Merge(dst, src)
}
func (m *LostHeartItem) XXX_Size() int {
	return xxx_messageInfo_LostHeartItem.Size(m)
}
func (m *LostHeartItem) XXX_DiscardUnknown() {
	xxx_messageInfo_LostHeartItem.DiscardUnknown(m)
}

var xxx_messageInfo_LostHeartItem proto.InternalMessageInfo

func (m *LostHeartItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *LostHeartItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type LostHeartResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LostHeartResp) Reset()         { *m = LostHeartResp{} }
func (m *LostHeartResp) String() string { return proto.CompactTextString(m) }
func (*LostHeartResp) ProtoMessage()    {}
func (*LostHeartResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{51}
}
func (m *LostHeartResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LostHeartResp.Unmarshal(m, b)
}
func (m *LostHeartResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LostHeartResp.Marshal(b, m, deterministic)
}
func (dst *LostHeartResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LostHeartResp.Merge(dst, src)
}
func (m *LostHeartResp) XXX_Size() int {
	return xxx_messageInfo_LostHeartResp.Size(m)
}
func (m *LostHeartResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LostHeartResp.DiscardUnknown(m)
}

var xxx_messageInfo_LostHeartResp proto.InternalMessageInfo

type ConfirmMainReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConfirmMainReq) Reset()         { *m = ConfirmMainReq{} }
func (m *ConfirmMainReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmMainReq) ProtoMessage()    {}
func (*ConfirmMainReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{52}
}
func (m *ConfirmMainReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmMainReq.Unmarshal(m, b)
}
func (m *ConfirmMainReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmMainReq.Marshal(b, m, deterministic)
}
func (dst *ConfirmMainReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmMainReq.Merge(dst, src)
}
func (m *ConfirmMainReq) XXX_Size() int {
	return xxx_messageInfo_ConfirmMainReq.Size(m)
}
func (m *ConfirmMainReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmMainReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmMainReq proto.InternalMessageInfo

func (m *ConfirmMainReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ConfirmMainReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmMainReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ConfirmMainResp struct {
	Info                 *SingingInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ConfirmMainResp) Reset()         { *m = ConfirmMainResp{} }
func (m *ConfirmMainResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmMainResp) ProtoMessage()    {}
func (*ConfirmMainResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{53}
}
func (m *ConfirmMainResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConfirmMainResp.Unmarshal(m, b)
}
func (m *ConfirmMainResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConfirmMainResp.Marshal(b, m, deterministic)
}
func (dst *ConfirmMainResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConfirmMainResp.Merge(dst, src)
}
func (m *ConfirmMainResp) XXX_Size() int {
	return xxx_messageInfo_ConfirmMainResp.Size(m)
}
func (m *ConfirmMainResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConfirmMainResp.DiscardUnknown(m)
}

var xxx_messageInfo_ConfirmMainResp proto.InternalMessageInfo

func (m *ConfirmMainResp) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SearchSongsReq struct {
	KeyWord              string   `protobuf:"bytes,1,opt,name=key_word,json=keyWord,proto3" json:"key_word,omitempty"`
	Language             string   `protobuf:"bytes,2,opt,name=language,proto3" json:"language,omitempty"`
	Sex                  Sex      `protobuf:"varint,3,opt,name=sex,proto3,enum=concert.Sex" json:"sex,omitempty"`
	Instrument           string   `protobuf:"bytes,4,opt,name=instrument,proto3" json:"instrument,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	Id                   string   `protobuf:"bytes,7,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchSongsReq) Reset()         { *m = SearchSongsReq{} }
func (m *SearchSongsReq) String() string { return proto.CompactTextString(m) }
func (*SearchSongsReq) ProtoMessage()    {}
func (*SearchSongsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{54}
}
func (m *SearchSongsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchSongsReq.Unmarshal(m, b)
}
func (m *SearchSongsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchSongsReq.Marshal(b, m, deterministic)
}
func (dst *SearchSongsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchSongsReq.Merge(dst, src)
}
func (m *SearchSongsReq) XXX_Size() int {
	return xxx_messageInfo_SearchSongsReq.Size(m)
}
func (m *SearchSongsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchSongsReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchSongsReq proto.InternalMessageInfo

func (m *SearchSongsReq) GetKeyWord() string {
	if m != nil {
		return m.KeyWord
	}
	return ""
}

func (m *SearchSongsReq) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *SearchSongsReq) GetSex() Sex {
	if m != nil {
		return m.Sex
	}
	return Sex_All
}

func (m *SearchSongsReq) GetInstrument() string {
	if m != nil {
		return m.Instrument
	}
	return ""
}

func (m *SearchSongsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchSongsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchSongsReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type SearchSongsResp struct {
	Songs                []*ConcertSong `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	Count                uint32         `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SearchSongsResp) Reset()         { *m = SearchSongsResp{} }
func (m *SearchSongsResp) String() string { return proto.CompactTextString(m) }
func (*SearchSongsResp) ProtoMessage()    {}
func (*SearchSongsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{55}
}
func (m *SearchSongsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchSongsResp.Unmarshal(m, b)
}
func (m *SearchSongsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchSongsResp.Marshal(b, m, deterministic)
}
func (dst *SearchSongsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchSongsResp.Merge(dst, src)
}
func (m *SearchSongsResp) XXX_Size() int {
	return xxx_messageInfo_SearchSongsResp.Size(m)
}
func (m *SearchSongsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchSongsResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchSongsResp proto.InternalMessageInfo

func (m *SearchSongsResp) GetSongs() []*ConcertSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *SearchSongsResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatchUpsertSongsReq struct {
	Songs                []*ConcertSong `protobuf:"bytes,1,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatchUpsertSongsReq) Reset()         { *m = BatchUpsertSongsReq{} }
func (m *BatchUpsertSongsReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertSongsReq) ProtoMessage()    {}
func (*BatchUpsertSongsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{56}
}
func (m *BatchUpsertSongsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertSongsReq.Unmarshal(m, b)
}
func (m *BatchUpsertSongsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertSongsReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertSongsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertSongsReq.Merge(dst, src)
}
func (m *BatchUpsertSongsReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertSongsReq.Size(m)
}
func (m *BatchUpsertSongsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertSongsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertSongsReq proto.InternalMessageInfo

func (m *BatchUpsertSongsReq) GetSongs() []*ConcertSong {
	if m != nil {
		return m.Songs
	}
	return nil
}

type BatchUpsertSongsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpsertSongsResp) Reset()         { *m = BatchUpsertSongsResp{} }
func (m *BatchUpsertSongsResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertSongsResp) ProtoMessage()    {}
func (*BatchUpsertSongsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{57}
}
func (m *BatchUpsertSongsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertSongsResp.Unmarshal(m, b)
}
func (m *BatchUpsertSongsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertSongsResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertSongsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertSongsResp.Merge(dst, src)
}
func (m *BatchUpsertSongsResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertSongsResp.Size(m)
}
func (m *BatchUpsertSongsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertSongsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertSongsResp proto.InternalMessageInfo

type SearchInstrumentAudiosReq struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Tone                 string   `protobuf:"bytes,2,opt,name=tone,proto3" json:"tone,omitempty"`
	Chord                string   `protobuf:"bytes,3,opt,name=chord,proto3" json:"chord,omitempty"`
	KeyMap               uint32   `protobuf:"varint,4,opt,name=key_map,json=keyMap,proto3" json:"key_map,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32   `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	SongId               string   `protobuf:"bytes,7,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchInstrumentAudiosReq) Reset()         { *m = SearchInstrumentAudiosReq{} }
func (m *SearchInstrumentAudiosReq) String() string { return proto.CompactTextString(m) }
func (*SearchInstrumentAudiosReq) ProtoMessage()    {}
func (*SearchInstrumentAudiosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{58}
}
func (m *SearchInstrumentAudiosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchInstrumentAudiosReq.Unmarshal(m, b)
}
func (m *SearchInstrumentAudiosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchInstrumentAudiosReq.Marshal(b, m, deterministic)
}
func (dst *SearchInstrumentAudiosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchInstrumentAudiosReq.Merge(dst, src)
}
func (m *SearchInstrumentAudiosReq) XXX_Size() int {
	return xxx_messageInfo_SearchInstrumentAudiosReq.Size(m)
}
func (m *SearchInstrumentAudiosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchInstrumentAudiosReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchInstrumentAudiosReq proto.InternalMessageInfo

func (m *SearchInstrumentAudiosReq) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SearchInstrumentAudiosReq) GetTone() string {
	if m != nil {
		return m.Tone
	}
	return ""
}

func (m *SearchInstrumentAudiosReq) GetChord() string {
	if m != nil {
		return m.Chord
	}
	return ""
}

func (m *SearchInstrumentAudiosReq) GetKeyMap() uint32 {
	if m != nil {
		return m.KeyMap
	}
	return 0
}

func (m *SearchInstrumentAudiosReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SearchInstrumentAudiosReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *SearchInstrumentAudiosReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type SearchInstrumentAudiosResp struct {
	InstrumentAudioList  []*InstrumentAudio `protobuf:"bytes,1,rep,name=instrument_audio_list,json=instrumentAudioList,proto3" json:"instrument_audio_list,omitempty"`
	Count                uint32             `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SearchInstrumentAudiosResp) Reset()         { *m = SearchInstrumentAudiosResp{} }
func (m *SearchInstrumentAudiosResp) String() string { return proto.CompactTextString(m) }
func (*SearchInstrumentAudiosResp) ProtoMessage()    {}
func (*SearchInstrumentAudiosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{59}
}
func (m *SearchInstrumentAudiosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchInstrumentAudiosResp.Unmarshal(m, b)
}
func (m *SearchInstrumentAudiosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchInstrumentAudiosResp.Marshal(b, m, deterministic)
}
func (dst *SearchInstrumentAudiosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchInstrumentAudiosResp.Merge(dst, src)
}
func (m *SearchInstrumentAudiosResp) XXX_Size() int {
	return xxx_messageInfo_SearchInstrumentAudiosResp.Size(m)
}
func (m *SearchInstrumentAudiosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchInstrumentAudiosResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchInstrumentAudiosResp proto.InternalMessageInfo

func (m *SearchInstrumentAudiosResp) GetInstrumentAudioList() []*InstrumentAudio {
	if m != nil {
		return m.InstrumentAudioList
	}
	return nil
}

func (m *SearchInstrumentAudiosResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type BatchUpsertInstrumentAudiosReq struct {
	InstrumentAudioList  []*InstrumentAudio `protobuf:"bytes,1,rep,name=instrument_audio_list,json=instrumentAudioList,proto3" json:"instrument_audio_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchUpsertInstrumentAudiosReq) Reset()         { *m = BatchUpsertInstrumentAudiosReq{} }
func (m *BatchUpsertInstrumentAudiosReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertInstrumentAudiosReq) ProtoMessage()    {}
func (*BatchUpsertInstrumentAudiosReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{60}
}
func (m *BatchUpsertInstrumentAudiosReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertInstrumentAudiosReq.Unmarshal(m, b)
}
func (m *BatchUpsertInstrumentAudiosReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertInstrumentAudiosReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertInstrumentAudiosReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertInstrumentAudiosReq.Merge(dst, src)
}
func (m *BatchUpsertInstrumentAudiosReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertInstrumentAudiosReq.Size(m)
}
func (m *BatchUpsertInstrumentAudiosReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertInstrumentAudiosReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertInstrumentAudiosReq proto.InternalMessageInfo

func (m *BatchUpsertInstrumentAudiosReq) GetInstrumentAudioList() []*InstrumentAudio {
	if m != nil {
		return m.InstrumentAudioList
	}
	return nil
}

type BatchUpsertInstrumentAudiosResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpsertInstrumentAudiosResp) Reset()         { *m = BatchUpsertInstrumentAudiosResp{} }
func (m *BatchUpsertInstrumentAudiosResp) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertInstrumentAudiosResp) ProtoMessage()    {}
func (*BatchUpsertInstrumentAudiosResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{61}
}
func (m *BatchUpsertInstrumentAudiosResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertInstrumentAudiosResp.Unmarshal(m, b)
}
func (m *BatchUpsertInstrumentAudiosResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertInstrumentAudiosResp.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertInstrumentAudiosResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertInstrumentAudiosResp.Merge(dst, src)
}
func (m *BatchUpsertInstrumentAudiosResp) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertInstrumentAudiosResp.Size(m)
}
func (m *BatchUpsertInstrumentAudiosResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertInstrumentAudiosResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertInstrumentAudiosResp proto.InternalMessageInfo

type DeleteSongReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSongReq) Reset()         { *m = DeleteSongReq{} }
func (m *DeleteSongReq) String() string { return proto.CompactTextString(m) }
func (*DeleteSongReq) ProtoMessage()    {}
func (*DeleteSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{62}
}
func (m *DeleteSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSongReq.Unmarshal(m, b)
}
func (m *DeleteSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSongReq.Marshal(b, m, deterministic)
}
func (dst *DeleteSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSongReq.Merge(dst, src)
}
func (m *DeleteSongReq) XXX_Size() int {
	return xxx_messageInfo_DeleteSongReq.Size(m)
}
func (m *DeleteSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSongReq proto.InternalMessageInfo

func (m *DeleteSongReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSongResp) Reset()         { *m = DeleteSongResp{} }
func (m *DeleteSongResp) String() string { return proto.CompactTextString(m) }
func (*DeleteSongResp) ProtoMessage()    {}
func (*DeleteSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{63}
}
func (m *DeleteSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSongResp.Unmarshal(m, b)
}
func (m *DeleteSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSongResp.Marshal(b, m, deterministic)
}
func (dst *DeleteSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSongResp.Merge(dst, src)
}
func (m *DeleteSongResp) XXX_Size() int {
	return xxx_messageInfo_DeleteSongResp.Size(m)
}
func (m *DeleteSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSongResp proto.InternalMessageInfo

type DeleteInstrumentAudioReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteInstrumentAudioReq) Reset()         { *m = DeleteInstrumentAudioReq{} }
func (m *DeleteInstrumentAudioReq) String() string { return proto.CompactTextString(m) }
func (*DeleteInstrumentAudioReq) ProtoMessage()    {}
func (*DeleteInstrumentAudioReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{64}
}
func (m *DeleteInstrumentAudioReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteInstrumentAudioReq.Unmarshal(m, b)
}
func (m *DeleteInstrumentAudioReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteInstrumentAudioReq.Marshal(b, m, deterministic)
}
func (dst *DeleteInstrumentAudioReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteInstrumentAudioReq.Merge(dst, src)
}
func (m *DeleteInstrumentAudioReq) XXX_Size() int {
	return xxx_messageInfo_DeleteInstrumentAudioReq.Size(m)
}
func (m *DeleteInstrumentAudioReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteInstrumentAudioReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteInstrumentAudioReq proto.InternalMessageInfo

func (m *DeleteInstrumentAudioReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteInstrumentAudioResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteInstrumentAudioResp) Reset()         { *m = DeleteInstrumentAudioResp{} }
func (m *DeleteInstrumentAudioResp) String() string { return proto.CompactTextString(m) }
func (*DeleteInstrumentAudioResp) ProtoMessage()    {}
func (*DeleteInstrumentAudioResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{65}
}
func (m *DeleteInstrumentAudioResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteInstrumentAudioResp.Unmarshal(m, b)
}
func (m *DeleteInstrumentAudioResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteInstrumentAudioResp.Marshal(b, m, deterministic)
}
func (dst *DeleteInstrumentAudioResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteInstrumentAudioResp.Merge(dst, src)
}
func (m *DeleteInstrumentAudioResp) XXX_Size() int {
	return xxx_messageInfo_DeleteInstrumentAudioResp.Size(m)
}
func (m *DeleteInstrumentAudioResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteInstrumentAudioResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteInstrumentAudioResp proto.InternalMessageInfo

type ReportConcertSuccCountReq struct {
	Uid                  uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32                   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32                   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GradeList            []ReportConcertGradeType `protobuf:"varint,4,rep,packed,name=grade_list,json=gradeList,proto3,enum=concert.ReportConcertGradeType" json:"grade_list,omitempty"`
	IsReconnect          bool                     `protobuf:"varint,5,opt,name=is_reconnect,json=isReconnect,proto3" json:"is_reconnect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ReportConcertSuccCountReq) Reset()         { *m = ReportConcertSuccCountReq{} }
func (m *ReportConcertSuccCountReq) String() string { return proto.CompactTextString(m) }
func (*ReportConcertSuccCountReq) ProtoMessage()    {}
func (*ReportConcertSuccCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{66}
}
func (m *ReportConcertSuccCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConcertSuccCountReq.Unmarshal(m, b)
}
func (m *ReportConcertSuccCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConcertSuccCountReq.Marshal(b, m, deterministic)
}
func (dst *ReportConcertSuccCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConcertSuccCountReq.Merge(dst, src)
}
func (m *ReportConcertSuccCountReq) XXX_Size() int {
	return xxx_messageInfo_ReportConcertSuccCountReq.Size(m)
}
func (m *ReportConcertSuccCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConcertSuccCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConcertSuccCountReq proto.InternalMessageInfo

func (m *ReportConcertSuccCountReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportConcertSuccCountReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportConcertSuccCountReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *ReportConcertSuccCountReq) GetGradeList() []ReportConcertGradeType {
	if m != nil {
		return m.GradeList
	}
	return nil
}

func (m *ReportConcertSuccCountReq) GetIsReconnect() bool {
	if m != nil {
		return m.IsReconnect
	}
	return false
}

type ReportConcertSuccCountResp struct {
	Interval             uint32   `protobuf:"varint,1,opt,name=interval,proto3" json:"interval,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportConcertSuccCountResp) Reset()         { *m = ReportConcertSuccCountResp{} }
func (m *ReportConcertSuccCountResp) String() string { return proto.CompactTextString(m) }
func (*ReportConcertSuccCountResp) ProtoMessage()    {}
func (*ReportConcertSuccCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{67}
}
func (m *ReportConcertSuccCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportConcertSuccCountResp.Unmarshal(m, b)
}
func (m *ReportConcertSuccCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportConcertSuccCountResp.Marshal(b, m, deterministic)
}
func (dst *ReportConcertSuccCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportConcertSuccCountResp.Merge(dst, src)
}
func (m *ReportConcertSuccCountResp) XXX_Size() int {
	return xxx_messageInfo_ReportConcertSuccCountResp.Size(m)
}
func (m *ReportConcertSuccCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportConcertSuccCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportConcertSuccCountResp proto.InternalMessageInfo

func (m *ReportConcertSuccCountResp) GetInterval() uint32 {
	if m != nil {
		return m.Interval
	}
	return 0
}

type JoinConcertReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinConcertReq) Reset()         { *m = JoinConcertReq{} }
func (m *JoinConcertReq) String() string { return proto.CompactTextString(m) }
func (*JoinConcertReq) ProtoMessage()    {}
func (*JoinConcertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{68}
}
func (m *JoinConcertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinConcertReq.Unmarshal(m, b)
}
func (m *JoinConcertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinConcertReq.Marshal(b, m, deterministic)
}
func (dst *JoinConcertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinConcertReq.Merge(dst, src)
}
func (m *JoinConcertReq) XXX_Size() int {
	return xxx_messageInfo_JoinConcertReq.Size(m)
}
func (m *JoinConcertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinConcertReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinConcertReq proto.InternalMessageInfo

func (m *JoinConcertReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *JoinConcertReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinConcertReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type JoinConcertResp struct {
	Info                 *SingingInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *JoinConcertResp) Reset()         { *m = JoinConcertResp{} }
func (m *JoinConcertResp) String() string { return proto.CompactTextString(m) }
func (*JoinConcertResp) ProtoMessage()    {}
func (*JoinConcertResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{69}
}
func (m *JoinConcertResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinConcertResp.Unmarshal(m, b)
}
func (m *JoinConcertResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinConcertResp.Marshal(b, m, deterministic)
}
func (dst *JoinConcertResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinConcertResp.Merge(dst, src)
}
func (m *JoinConcertResp) XXX_Size() int {
	return xxx_messageInfo_JoinConcertResp.Size(m)
}
func (m *JoinConcertResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinConcertResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinConcertResp proto.InternalMessageInfo

func (m *JoinConcertResp) GetInfo() *SingingInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type BatchUpsertSongsV2Req struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpsertSongsV2Req) Reset()         { *m = BatchUpsertSongsV2Req{} }
func (m *BatchUpsertSongsV2Req) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertSongsV2Req) ProtoMessage()    {}
func (*BatchUpsertSongsV2Req) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{70}
}
func (m *BatchUpsertSongsV2Req) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertSongsV2Req.Unmarshal(m, b)
}
func (m *BatchUpsertSongsV2Req) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertSongsV2Req.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertSongsV2Req) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertSongsV2Req.Merge(dst, src)
}
func (m *BatchUpsertSongsV2Req) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertSongsV2Req.Size(m)
}
func (m *BatchUpsertSongsV2Req) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertSongsV2Req.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertSongsV2Req proto.InternalMessageInfo

type BatchUpsertSongsV2Resp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpsertSongsV2Resp) Reset()         { *m = BatchUpsertSongsV2Resp{} }
func (m *BatchUpsertSongsV2Resp) String() string { return proto.CompactTextString(m) }
func (*BatchUpsertSongsV2Resp) ProtoMessage()    {}
func (*BatchUpsertSongsV2Resp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{71}
}
func (m *BatchUpsertSongsV2Resp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpsertSongsV2Resp.Unmarshal(m, b)
}
func (m *BatchUpsertSongsV2Resp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpsertSongsV2Resp.Marshal(b, m, deterministic)
}
func (dst *BatchUpsertSongsV2Resp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpsertSongsV2Resp.Merge(dst, src)
}
func (m *BatchUpsertSongsV2Resp) XXX_Size() int {
	return xxx_messageInfo_BatchUpsertSongsV2Resp.Size(m)
}
func (m *BatchUpsertSongsV2Resp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpsertSongsV2Resp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpsertSongsV2Resp proto.InternalMessageInfo

type ConcertImage struct {
	Id                   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Img                  string             `protobuf:"bytes,2,opt,name=img,proto3" json:"img,omitempty"`
	UnselectedImg        string             `protobuf:"bytes,3,opt,name=unselected_img,json=unselectedImg,proto3" json:"unselected_img,omitempty"`
	Thumbnail            string             `protobuf:"bytes,4,opt,name=thumbnail,proto3" json:"thumbnail,omitempty"`
	Sex                  uint32             `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Images               []*ConcertImageRes `protobuf:"bytes,6,rep,name=images,proto3" json:"images,omitempty"`
	IsDefault            bool               `protobuf:"varint,7,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	Name                 string             `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ConcertImage) Reset()         { *m = ConcertImage{} }
func (m *ConcertImage) String() string { return proto.CompactTextString(m) }
func (*ConcertImage) ProtoMessage()    {}
func (*ConcertImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{72}
}
func (m *ConcertImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertImage.Unmarshal(m, b)
}
func (m *ConcertImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertImage.Marshal(b, m, deterministic)
}
func (dst *ConcertImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertImage.Merge(dst, src)
}
func (m *ConcertImage) XXX_Size() int {
	return xxx_messageInfo_ConcertImage.Size(m)
}
func (m *ConcertImage) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertImage.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertImage proto.InternalMessageInfo

func (m *ConcertImage) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ConcertImage) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *ConcertImage) GetUnselectedImg() string {
	if m != nil {
		return m.UnselectedImg
	}
	return ""
}

func (m *ConcertImage) GetThumbnail() string {
	if m != nil {
		return m.Thumbnail
	}
	return ""
}

func (m *ConcertImage) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ConcertImage) GetImages() []*ConcertImageRes {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *ConcertImage) GetIsDefault() bool {
	if m != nil {
		return m.IsDefault
	}
	return false
}

func (m *ConcertImage) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type ConcertImageRes struct {
	Role                 BandRole    `protobuf:"varint,1,opt,name=role,proto3,enum=concert.BandRole" json:"role,omitempty"`
	Res_1                *ConcertRes `protobuf:"bytes,2,opt,name=res_1,json=res1,proto3" json:"res_1,omitempty"`
	Res_2                *ConcertRes `protobuf:"bytes,3,opt,name=res_2,json=res2,proto3" json:"res_2,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ConcertImageRes) Reset()         { *m = ConcertImageRes{} }
func (m *ConcertImageRes) String() string { return proto.CompactTextString(m) }
func (*ConcertImageRes) ProtoMessage()    {}
func (*ConcertImageRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{73}
}
func (m *ConcertImageRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConcertImageRes.Unmarshal(m, b)
}
func (m *ConcertImageRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConcertImageRes.Marshal(b, m, deterministic)
}
func (dst *ConcertImageRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConcertImageRes.Merge(dst, src)
}
func (m *ConcertImageRes) XXX_Size() int {
	return xxx_messageInfo_ConcertImageRes.Size(m)
}
func (m *ConcertImageRes) XXX_DiscardUnknown() {
	xxx_messageInfo_ConcertImageRes.DiscardUnknown(m)
}

var xxx_messageInfo_ConcertImageRes proto.InternalMessageInfo

func (m *ConcertImageRes) GetRole() BandRole {
	if m != nil {
		return m.Role
	}
	return BandRole_BandRole_UNDEFINED
}

func (m *ConcertImageRes) GetRes_1() *ConcertRes {
	if m != nil {
		return m.Res_1
	}
	return nil
}

func (m *ConcertImageRes) GetRes_2() *ConcertRes {
	if m != nil {
		return m.Res_2
	}
	return nil
}

// 获取形象配置
type GetAllConcertImageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllConcertImageReq) Reset()         { *m = GetAllConcertImageReq{} }
func (m *GetAllConcertImageReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertImageReq) ProtoMessage()    {}
func (*GetAllConcertImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{74}
}
func (m *GetAllConcertImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertImageReq.Unmarshal(m, b)
}
func (m *GetAllConcertImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertImageReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertImageReq.Merge(dst, src)
}
func (m *GetAllConcertImageReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertImageReq.Size(m)
}
func (m *GetAllConcertImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertImageReq proto.InternalMessageInfo

func (m *GetAllConcertImageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllConcertImageResp struct {
	Images               []*ConcertImage `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	ChosenImageId        string          `protobuf:"bytes,2,opt,name=chosen_image_id,json=chosenImageId,proto3" json:"chosen_image_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllConcertImageResp) Reset()         { *m = GetAllConcertImageResp{} }
func (m *GetAllConcertImageResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertImageResp) ProtoMessage()    {}
func (*GetAllConcertImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{75}
}
func (m *GetAllConcertImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertImageResp.Unmarshal(m, b)
}
func (m *GetAllConcertImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertImageResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertImageResp.Merge(dst, src)
}
func (m *GetAllConcertImageResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertImageResp.Size(m)
}
func (m *GetAllConcertImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertImageResp proto.InternalMessageInfo

func (m *GetAllConcertImageResp) GetImages() []*ConcertImage {
	if m != nil {
		return m.Images
	}
	return nil
}

func (m *GetAllConcertImageResp) GetChosenImageId() string {
	if m != nil {
		return m.ChosenImageId
	}
	return ""
}

// 设置用户形象
type SetConcertUserImageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ImageId              string   `protobuf:"bytes,3,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetConcertUserImageReq) Reset()         { *m = SetConcertUserImageReq{} }
func (m *SetConcertUserImageReq) String() string { return proto.CompactTextString(m) }
func (*SetConcertUserImageReq) ProtoMessage()    {}
func (*SetConcertUserImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{76}
}
func (m *SetConcertUserImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConcertUserImageReq.Unmarshal(m, b)
}
func (m *SetConcertUserImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConcertUserImageReq.Marshal(b, m, deterministic)
}
func (dst *SetConcertUserImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConcertUserImageReq.Merge(dst, src)
}
func (m *SetConcertUserImageReq) XXX_Size() int {
	return xxx_messageInfo_SetConcertUserImageReq.Size(m)
}
func (m *SetConcertUserImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConcertUserImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetConcertUserImageReq proto.InternalMessageInfo

func (m *SetConcertUserImageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetConcertUserImageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetConcertUserImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

type SetConcertUserImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetConcertUserImageResp) Reset()         { *m = SetConcertUserImageResp{} }
func (m *SetConcertUserImageResp) String() string { return proto.CompactTextString(m) }
func (*SetConcertUserImageResp) ProtoMessage()    {}
func (*SetConcertUserImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{77}
}
func (m *SetConcertUserImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetConcertUserImageResp.Unmarshal(m, b)
}
func (m *SetConcertUserImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetConcertUserImageResp.Marshal(b, m, deterministic)
}
func (dst *SetConcertUserImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetConcertUserImageResp.Merge(dst, src)
}
func (m *SetConcertUserImageResp) XXX_Size() int {
	return xxx_messageInfo_SetConcertUserImageResp.Size(m)
}
func (m *SetConcertUserImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetConcertUserImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetConcertUserImageResp proto.InternalMessageInfo

// 获取麦上用户形象
type GetAllConcertOnMicUserImageReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllConcertOnMicUserImageReq) Reset()         { *m = GetAllConcertOnMicUserImageReq{} }
func (m *GetAllConcertOnMicUserImageReq) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertOnMicUserImageReq) ProtoMessage()    {}
func (*GetAllConcertOnMicUserImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{78}
}
func (m *GetAllConcertOnMicUserImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertOnMicUserImageReq.Unmarshal(m, b)
}
func (m *GetAllConcertOnMicUserImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertOnMicUserImageReq.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertOnMicUserImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertOnMicUserImageReq.Merge(dst, src)
}
func (m *GetAllConcertOnMicUserImageReq) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertOnMicUserImageReq.Size(m)
}
func (m *GetAllConcertOnMicUserImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertOnMicUserImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertOnMicUserImageReq proto.InternalMessageInfo

func (m *GetAllConcertOnMicUserImageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetAllConcertOnMicUserImageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetAllConcertOnMicUserImageResp struct {
	UserImage            map[uint32]*ConcertImage `protobuf:"bytes,1,rep,name=user_image,json=userImage,proto3" json:"user_image,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAllConcertOnMicUserImageResp) Reset()         { *m = GetAllConcertOnMicUserImageResp{} }
func (m *GetAllConcertOnMicUserImageResp) String() string { return proto.CompactTextString(m) }
func (*GetAllConcertOnMicUserImageResp) ProtoMessage()    {}
func (*GetAllConcertOnMicUserImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{79}
}
func (m *GetAllConcertOnMicUserImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllConcertOnMicUserImageResp.Unmarshal(m, b)
}
func (m *GetAllConcertOnMicUserImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllConcertOnMicUserImageResp.Marshal(b, m, deterministic)
}
func (dst *GetAllConcertOnMicUserImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllConcertOnMicUserImageResp.Merge(dst, src)
}
func (m *GetAllConcertOnMicUserImageResp) XXX_Size() int {
	return xxx_messageInfo_GetAllConcertOnMicUserImageResp.Size(m)
}
func (m *GetAllConcertOnMicUserImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllConcertOnMicUserImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllConcertOnMicUserImageResp proto.InternalMessageInfo

func (m *GetAllConcertOnMicUserImageResp) GetUserImage() map[uint32]*ConcertImage {
	if m != nil {
		return m.UserImage
	}
	return nil
}

// 重排序形象
type ReorderConcertImageReq struct {
	ImageIds             []string `protobuf:"bytes,1,rep,name=image_ids,json=imageIds,proto3" json:"image_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderConcertImageReq) Reset()         { *m = ReorderConcertImageReq{} }
func (m *ReorderConcertImageReq) String() string { return proto.CompactTextString(m) }
func (*ReorderConcertImageReq) ProtoMessage()    {}
func (*ReorderConcertImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{80}
}
func (m *ReorderConcertImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderConcertImageReq.Unmarshal(m, b)
}
func (m *ReorderConcertImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderConcertImageReq.Marshal(b, m, deterministic)
}
func (dst *ReorderConcertImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderConcertImageReq.Merge(dst, src)
}
func (m *ReorderConcertImageReq) XXX_Size() int {
	return xxx_messageInfo_ReorderConcertImageReq.Size(m)
}
func (m *ReorderConcertImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderConcertImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderConcertImageReq proto.InternalMessageInfo

func (m *ReorderConcertImageReq) GetImageIds() []string {
	if m != nil {
		return m.ImageIds
	}
	return nil
}

type ReorderConcertImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReorderConcertImageResp) Reset()         { *m = ReorderConcertImageResp{} }
func (m *ReorderConcertImageResp) String() string { return proto.CompactTextString(m) }
func (*ReorderConcertImageResp) ProtoMessage()    {}
func (*ReorderConcertImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{81}
}
func (m *ReorderConcertImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReorderConcertImageResp.Unmarshal(m, b)
}
func (m *ReorderConcertImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReorderConcertImageResp.Marshal(b, m, deterministic)
}
func (dst *ReorderConcertImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReorderConcertImageResp.Merge(dst, src)
}
func (m *ReorderConcertImageResp) XXX_Size() int {
	return xxx_messageInfo_ReorderConcertImageResp.Size(m)
}
func (m *ReorderConcertImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReorderConcertImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReorderConcertImageResp proto.InternalMessageInfo

// 设置默认形象
type SetDefaultImageReq struct {
	ImageId              string   `protobuf:"bytes,1,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	Sex                  uint32   `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDefaultImageReq) Reset()         { *m = SetDefaultImageReq{} }
func (m *SetDefaultImageReq) String() string { return proto.CompactTextString(m) }
func (*SetDefaultImageReq) ProtoMessage()    {}
func (*SetDefaultImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{82}
}
func (m *SetDefaultImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDefaultImageReq.Unmarshal(m, b)
}
func (m *SetDefaultImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDefaultImageReq.Marshal(b, m, deterministic)
}
func (dst *SetDefaultImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDefaultImageReq.Merge(dst, src)
}
func (m *SetDefaultImageReq) XXX_Size() int {
	return xxx_messageInfo_SetDefaultImageReq.Size(m)
}
func (m *SetDefaultImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDefaultImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetDefaultImageReq proto.InternalMessageInfo

func (m *SetDefaultImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *SetDefaultImageReq) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type SetDefaultImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetDefaultImageResp) Reset()         { *m = SetDefaultImageResp{} }
func (m *SetDefaultImageResp) String() string { return proto.CompactTextString(m) }
func (*SetDefaultImageResp) ProtoMessage()    {}
func (*SetDefaultImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{83}
}
func (m *SetDefaultImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetDefaultImageResp.Unmarshal(m, b)
}
func (m *SetDefaultImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetDefaultImageResp.Marshal(b, m, deterministic)
}
func (dst *SetDefaultImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetDefaultImageResp.Merge(dst, src)
}
func (m *SetDefaultImageResp) XXX_Size() int {
	return xxx_messageInfo_SetDefaultImageResp.Size(m)
}
func (m *SetDefaultImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetDefaultImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetDefaultImageResp proto.InternalMessageInfo

// 获取形象
type GetConcertImageListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConcertImageListReq) Reset()         { *m = GetConcertImageListReq{} }
func (m *GetConcertImageListReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertImageListReq) ProtoMessage()    {}
func (*GetConcertImageListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{84}
}
func (m *GetConcertImageListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertImageListReq.Unmarshal(m, b)
}
func (m *GetConcertImageListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertImageListReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertImageListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertImageListReq.Merge(dst, src)
}
func (m *GetConcertImageListReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertImageListReq.Size(m)
}
func (m *GetConcertImageListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertImageListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertImageListReq proto.InternalMessageInfo

type GetConcertImageListResp struct {
	Images               []*ConcertImage `protobuf:"bytes,1,rep,name=images,proto3" json:"images,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetConcertImageListResp) Reset()         { *m = GetConcertImageListResp{} }
func (m *GetConcertImageListResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertImageListResp) ProtoMessage()    {}
func (*GetConcertImageListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{85}
}
func (m *GetConcertImageListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertImageListResp.Unmarshal(m, b)
}
func (m *GetConcertImageListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertImageListResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertImageListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertImageListResp.Merge(dst, src)
}
func (m *GetConcertImageListResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertImageListResp.Size(m)
}
func (m *GetConcertImageListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertImageListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertImageListResp proto.InternalMessageInfo

func (m *GetConcertImageListResp) GetImages() []*ConcertImage {
	if m != nil {
		return m.Images
	}
	return nil
}

// 删除形象
type DeleteConcertImageReq struct {
	ImageId              string   `protobuf:"bytes,1,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteConcertImageReq) Reset()         { *m = DeleteConcertImageReq{} }
func (m *DeleteConcertImageReq) String() string { return proto.CompactTextString(m) }
func (*DeleteConcertImageReq) ProtoMessage()    {}
func (*DeleteConcertImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{86}
}
func (m *DeleteConcertImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteConcertImageReq.Unmarshal(m, b)
}
func (m *DeleteConcertImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteConcertImageReq.Marshal(b, m, deterministic)
}
func (dst *DeleteConcertImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteConcertImageReq.Merge(dst, src)
}
func (m *DeleteConcertImageReq) XXX_Size() int {
	return xxx_messageInfo_DeleteConcertImageReq.Size(m)
}
func (m *DeleteConcertImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteConcertImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteConcertImageReq proto.InternalMessageInfo

func (m *DeleteConcertImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

type DeleteConcertImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteConcertImageResp) Reset()         { *m = DeleteConcertImageResp{} }
func (m *DeleteConcertImageResp) String() string { return proto.CompactTextString(m) }
func (*DeleteConcertImageResp) ProtoMessage()    {}
func (*DeleteConcertImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{87}
}
func (m *DeleteConcertImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteConcertImageResp.Unmarshal(m, b)
}
func (m *DeleteConcertImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteConcertImageResp.Marshal(b, m, deterministic)
}
func (dst *DeleteConcertImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteConcertImageResp.Merge(dst, src)
}
func (m *DeleteConcertImageResp) XXX_Size() int {
	return xxx_messageInfo_DeleteConcertImageResp.Size(m)
}
func (m *DeleteConcertImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteConcertImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteConcertImageResp proto.InternalMessageInfo

// 新增形象
type AddConcertImageReq struct {
	Image                *ConcertImage `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *AddConcertImageReq) Reset()         { *m = AddConcertImageReq{} }
func (m *AddConcertImageReq) String() string { return proto.CompactTextString(m) }
func (*AddConcertImageReq) ProtoMessage()    {}
func (*AddConcertImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{88}
}
func (m *AddConcertImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConcertImageReq.Unmarshal(m, b)
}
func (m *AddConcertImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConcertImageReq.Marshal(b, m, deterministic)
}
func (dst *AddConcertImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConcertImageReq.Merge(dst, src)
}
func (m *AddConcertImageReq) XXX_Size() int {
	return xxx_messageInfo_AddConcertImageReq.Size(m)
}
func (m *AddConcertImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConcertImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddConcertImageReq proto.InternalMessageInfo

func (m *AddConcertImageReq) GetImage() *ConcertImage {
	if m != nil {
		return m.Image
	}
	return nil
}

type AddConcertImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddConcertImageResp) Reset()         { *m = AddConcertImageResp{} }
func (m *AddConcertImageResp) String() string { return proto.CompactTextString(m) }
func (*AddConcertImageResp) ProtoMessage()    {}
func (*AddConcertImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{89}
}
func (m *AddConcertImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddConcertImageResp.Unmarshal(m, b)
}
func (m *AddConcertImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddConcertImageResp.Marshal(b, m, deterministic)
}
func (dst *AddConcertImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddConcertImageResp.Merge(dst, src)
}
func (m *AddConcertImageResp) XXX_Size() int {
	return xxx_messageInfo_AddConcertImageResp.Size(m)
}
func (m *AddConcertImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddConcertImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddConcertImageResp proto.InternalMessageInfo

// 用于强插到点歌列表
type GetConcertSongByIdReq struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetConcertSongByIdReq) Reset()         { *m = GetConcertSongByIdReq{} }
func (m *GetConcertSongByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongByIdReq) ProtoMessage()    {}
func (*GetConcertSongByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{90}
}
func (m *GetConcertSongByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongByIdReq.Unmarshal(m, b)
}
func (m *GetConcertSongByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongByIdReq.Merge(dst, src)
}
func (m *GetConcertSongByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongByIdReq.Size(m)
}
func (m *GetConcertSongByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongByIdReq proto.InternalMessageInfo

func (m *GetConcertSongByIdReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GetConcertSongByIdResp struct {
	Song                 *ConcertSong `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetConcertSongByIdResp) Reset()         { *m = GetConcertSongByIdResp{} }
func (m *GetConcertSongByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetConcertSongByIdResp) ProtoMessage()    {}
func (*GetConcertSongByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{91}
}
func (m *GetConcertSongByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetConcertSongByIdResp.Unmarshal(m, b)
}
func (m *GetConcertSongByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetConcertSongByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetConcertSongByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetConcertSongByIdResp.Merge(dst, src)
}
func (m *GetConcertSongByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetConcertSongByIdResp.Size(m)
}
func (m *GetConcertSongByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetConcertSongByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetConcertSongByIdResp proto.InternalMessageInfo

func (m *GetConcertSongByIdResp) GetSong() *ConcertSong {
	if m != nil {
		return m.Song
	}
	return nil
}

type GetRecentUploadedSongReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecentUploadedSongReq) Reset()         { *m = GetRecentUploadedSongReq{} }
func (m *GetRecentUploadedSongReq) String() string { return proto.CompactTextString(m) }
func (*GetRecentUploadedSongReq) ProtoMessage()    {}
func (*GetRecentUploadedSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{92}
}
func (m *GetRecentUploadedSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentUploadedSongReq.Unmarshal(m, b)
}
func (m *GetRecentUploadedSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentUploadedSongReq.Marshal(b, m, deterministic)
}
func (dst *GetRecentUploadedSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentUploadedSongReq.Merge(dst, src)
}
func (m *GetRecentUploadedSongReq) XXX_Size() int {
	return xxx_messageInfo_GetRecentUploadedSongReq.Size(m)
}
func (m *GetRecentUploadedSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentUploadedSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentUploadedSongReq proto.InternalMessageInfo

type GetRecentUploadedSongResp struct {
	Song                 *ConcertSong `protobuf:"bytes,1,opt,name=song,proto3" json:"song,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetRecentUploadedSongResp) Reset()         { *m = GetRecentUploadedSongResp{} }
func (m *GetRecentUploadedSongResp) String() string { return proto.CompactTextString(m) }
func (*GetRecentUploadedSongResp) ProtoMessage()    {}
func (*GetRecentUploadedSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_concert_5ced15a6310fe11a, []int{93}
}
func (m *GetRecentUploadedSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecentUploadedSongResp.Unmarshal(m, b)
}
func (m *GetRecentUploadedSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecentUploadedSongResp.Marshal(b, m, deterministic)
}
func (dst *GetRecentUploadedSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecentUploadedSongResp.Merge(dst, src)
}
func (m *GetRecentUploadedSongResp) XXX_Size() int {
	return xxx_messageInfo_GetRecentUploadedSongResp.Size(m)
}
func (m *GetRecentUploadedSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecentUploadedSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecentUploadedSongResp proto.InternalMessageInfo

func (m *GetRecentUploadedSongResp) GetSong() *ConcertSong {
	if m != nil {
		return m.Song
	}
	return nil
}

func init() {
	proto.RegisterType((*ListActivityCodeReq)(nil), "concert.ListActivityCodeReq")
	proto.RegisterType((*ListActivityCodeResp)(nil), "concert.ListActivityCodeResp")
	proto.RegisterType((*ActivityCode)(nil), "concert.ActivityCode")
	proto.RegisterType((*ExchangeActivityCodeReq)(nil), "concert.ExchangeActivityCodeReq")
	proto.RegisterType((*ExchangeActivityCodeResp)(nil), "concert.ExchangeActivityCodeResp")
	proto.RegisterType((*UpdateActivityCodeReq)(nil), "concert.UpdateActivityCodeReq")
	proto.RegisterType((*UpdateActivityCodeResp)(nil), "concert.UpdateActivityCodeResp")
	proto.RegisterType((*AddActivityCodeReq)(nil), "concert.AddActivityCodeReq")
	proto.RegisterType((*AddActivityCodeResp)(nil), "concert.AddActivityCodeResp")
	proto.RegisterType((*UpdateBackingTrackStatusReq)(nil), "concert.UpdateBackingTrackStatusReq")
	proto.RegisterType((*UpdateBackingTrackStatusResp)(nil), "concert.UpdateBackingTrackStatusResp")
	proto.RegisterType((*GetConcertSongOptsReq)(nil), "concert.GetConcertSongOptsReq")
	proto.RegisterType((*GetConcertSongOptsResp)(nil), "concert.GetConcertSongOptsResp")
	proto.RegisterType((*SearchConcertSongReq)(nil), "concert.SearchConcertSongReq")
	proto.RegisterType((*SearchConcertSongResp)(nil), "concert.SearchConcertSongResp")
	proto.RegisterType((*GetConcertSongListReq)(nil), "concert.GetConcertSongListReq")
	proto.RegisterType((*GetConcertSongListResp)(nil), "concert.GetConcertSongListResp")
	proto.RegisterType((*ConcertSong)(nil), "concert.ConcertSong")
	proto.RegisterMapType((map[string]*SheetMusic)(nil), "concert.ConcertSong.SheetMusicMapEntry")
	proto.RegisterType((*Instrument)(nil), "concert.Instrument")
	proto.RegisterType((*SheetMusic)(nil), "concert.SheetMusic")
	proto.RegisterType((*ConcertLoadMore)(nil), "concert.ConcertLoadMore")
	proto.RegisterType((*ConcertSongOpt)(nil), "concert.ConcertSongOpt")
	proto.RegisterType((*ConcertSongOptElem)(nil), "concert.ConcertSongOptElem")
	proto.RegisterType((*GetAllConcertResourceReq)(nil), "concert.GetAllConcertResourceReq")
	proto.RegisterType((*GetAllConcertResourceResp)(nil), "concert.GetAllConcertResourceResp")
	proto.RegisterType((*ConcertRes)(nil), "concert.ConcertRes")
	proto.RegisterType((*InstrumentRes)(nil), "concert.InstrumentRes")
	proto.RegisterType((*ConcertStageDecoration)(nil), "concert.ConcertStageDecoration")
	proto.RegisterType((*ConcertStageBg)(nil), "concert.ConcertStageBg")
	proto.RegisterType((*StartConcertSingingReq)(nil), "concert.StartConcertSingingReq")
	proto.RegisterType((*StartConcertSingingResp)(nil), "concert.StartConcertSingingResp")
	proto.RegisterType((*GetMusicBookReq)(nil), "concert.GetMusicBookReq")
	proto.RegisterType((*GetMusicBookResp)(nil), "concert.GetMusicBookResp")
	proto.RegisterType((*CompleteDownloadingMusicBookReq)(nil), "concert.CompleteDownloadingMusicBookReq")
	proto.RegisterType((*CompleteDownloadingMusicBookResp)(nil), "concert.CompleteDownloadingMusicBookResp")
	proto.RegisterType((*StopConcertSingingReq)(nil), "concert.StopConcertSingingReq")
	proto.RegisterType((*StopConcertSingingResp)(nil), "concert.StopConcertSingingResp")
	proto.RegisterType((*GetConcertInfoReq)(nil), "concert.GetConcertInfoReq")
	proto.RegisterType((*GetConcertInfoResp)(nil), "concert.GetConcertInfoResp")
	proto.RegisterType((*BatchGetConcertInfoReq)(nil), "concert.BatchGetConcertInfoReq")
	proto.RegisterType((*BatchGetConcertInfoResp)(nil), "concert.BatchGetConcertInfoResp")
	proto.RegisterMapType((map[uint32]*Role)(nil), "concert.BatchGetConcertInfoResp.MicIdToRoleEntry")
	proto.RegisterType((*Role)(nil), "concert.Role")
	proto.RegisterType((*SingingInfo)(nil), "concert.SingingInfo")
	proto.RegisterType((*ConcertSimpleSong)(nil), "concert.ConcertSimpleSong")
	proto.RegisterType((*BandMember)(nil), "concert.BandMember")
	proto.RegisterType((*MusicBook)(nil), "concert.MusicBook")
	proto.RegisterType((*KeyMapRhythmPoint)(nil), "concert.KeyMapRhythmPoint")
	proto.RegisterType((*InstrumentAudio)(nil), "concert.InstrumentAudio")
	proto.RegisterType((*RhythmPoint)(nil), "concert.RhythmPoint")
	proto.RegisterType((*LostHeartReq)(nil), "concert.LostHeartReq")
	proto.RegisterType((*LostHeartItem)(nil), "concert.LostHeartItem")
	proto.RegisterType((*LostHeartResp)(nil), "concert.LostHeartResp")
	proto.RegisterType((*ConfirmMainReq)(nil), "concert.ConfirmMainReq")
	proto.RegisterType((*ConfirmMainResp)(nil), "concert.ConfirmMainResp")
	proto.RegisterType((*SearchSongsReq)(nil), "concert.SearchSongsReq")
	proto.RegisterType((*SearchSongsResp)(nil), "concert.SearchSongsResp")
	proto.RegisterType((*BatchUpsertSongsReq)(nil), "concert.BatchUpsertSongsReq")
	proto.RegisterType((*BatchUpsertSongsResp)(nil), "concert.BatchUpsertSongsResp")
	proto.RegisterType((*SearchInstrumentAudiosReq)(nil), "concert.SearchInstrumentAudiosReq")
	proto.RegisterType((*SearchInstrumentAudiosResp)(nil), "concert.SearchInstrumentAudiosResp")
	proto.RegisterType((*BatchUpsertInstrumentAudiosReq)(nil), "concert.BatchUpsertInstrumentAudiosReq")
	proto.RegisterType((*BatchUpsertInstrumentAudiosResp)(nil), "concert.BatchUpsertInstrumentAudiosResp")
	proto.RegisterType((*DeleteSongReq)(nil), "concert.DeleteSongReq")
	proto.RegisterType((*DeleteSongResp)(nil), "concert.DeleteSongResp")
	proto.RegisterType((*DeleteInstrumentAudioReq)(nil), "concert.DeleteInstrumentAudioReq")
	proto.RegisterType((*DeleteInstrumentAudioResp)(nil), "concert.DeleteInstrumentAudioResp")
	proto.RegisterType((*ReportConcertSuccCountReq)(nil), "concert.ReportConcertSuccCountReq")
	proto.RegisterType((*ReportConcertSuccCountResp)(nil), "concert.ReportConcertSuccCountResp")
	proto.RegisterType((*JoinConcertReq)(nil), "concert.JoinConcertReq")
	proto.RegisterType((*JoinConcertResp)(nil), "concert.JoinConcertResp")
	proto.RegisterType((*BatchUpsertSongsV2Req)(nil), "concert.BatchUpsertSongsV2Req")
	proto.RegisterType((*BatchUpsertSongsV2Resp)(nil), "concert.BatchUpsertSongsV2Resp")
	proto.RegisterType((*ConcertImage)(nil), "concert.ConcertImage")
	proto.RegisterType((*ConcertImageRes)(nil), "concert.ConcertImageRes")
	proto.RegisterType((*GetAllConcertImageReq)(nil), "concert.GetAllConcertImageReq")
	proto.RegisterType((*GetAllConcertImageResp)(nil), "concert.GetAllConcertImageResp")
	proto.RegisterType((*SetConcertUserImageReq)(nil), "concert.SetConcertUserImageReq")
	proto.RegisterType((*SetConcertUserImageResp)(nil), "concert.SetConcertUserImageResp")
	proto.RegisterType((*GetAllConcertOnMicUserImageReq)(nil), "concert.GetAllConcertOnMicUserImageReq")
	proto.RegisterType((*GetAllConcertOnMicUserImageResp)(nil), "concert.GetAllConcertOnMicUserImageResp")
	proto.RegisterMapType((map[uint32]*ConcertImage)(nil), "concert.GetAllConcertOnMicUserImageResp.UserImageEntry")
	proto.RegisterType((*ReorderConcertImageReq)(nil), "concert.ReorderConcertImageReq")
	proto.RegisterType((*ReorderConcertImageResp)(nil), "concert.ReorderConcertImageResp")
	proto.RegisterType((*SetDefaultImageReq)(nil), "concert.SetDefaultImageReq")
	proto.RegisterType((*SetDefaultImageResp)(nil), "concert.SetDefaultImageResp")
	proto.RegisterType((*GetConcertImageListReq)(nil), "concert.GetConcertImageListReq")
	proto.RegisterType((*GetConcertImageListResp)(nil), "concert.GetConcertImageListResp")
	proto.RegisterType((*DeleteConcertImageReq)(nil), "concert.DeleteConcertImageReq")
	proto.RegisterType((*DeleteConcertImageResp)(nil), "concert.DeleteConcertImageResp")
	proto.RegisterType((*AddConcertImageReq)(nil), "concert.AddConcertImageReq")
	proto.RegisterType((*AddConcertImageResp)(nil), "concert.AddConcertImageResp")
	proto.RegisterType((*GetConcertSongByIdReq)(nil), "concert.GetConcertSongByIdReq")
	proto.RegisterType((*GetConcertSongByIdResp)(nil), "concert.GetConcertSongByIdResp")
	proto.RegisterType((*GetRecentUploadedSongReq)(nil), "concert.GetRecentUploadedSongReq")
	proto.RegisterType((*GetRecentUploadedSongResp)(nil), "concert.GetRecentUploadedSongResp")
	proto.RegisterEnum("concert.Stagetype", Stagetype_name, Stagetype_value)
	proto.RegisterEnum("concert.ConcertGameStage", ConcertGameStage_name, ConcertGameStage_value)
	proto.RegisterEnum("concert.KeyMapType", KeyMapType_name, KeyMapType_value)
	proto.RegisterEnum("concert.BandRole", BandRole_name, BandRole_value)
	proto.RegisterEnum("concert.Sex", Sex_name, Sex_value)
	proto.RegisterEnum("concert.ReportConcertGradeType", ReportConcertGradeType_name, ReportConcertGradeType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ConcertClient is the client API for Concert service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ConcertClient interface {
	// 获取演唱资源
	GetConcertSongOpts(ctx context.Context, in *GetConcertSongOptsReq, opts ...grpc.CallOption) (*GetConcertSongOptsResp, error)
	// 搜索歌曲
	SearchConcertSong(ctx context.Context, in *SearchConcertSongReq, opts ...grpc.CallOption) (*SearchConcertSongResp, error)
	// 获取歌曲
	GetConcertSongList(ctx context.Context, in *GetConcertSongListReq, opts ...grpc.CallOption) (*GetConcertSongListResp, error)
	// 点歌
	StartConcertSinging(ctx context.Context, in *StartConcertSingingReq, opts ...grpc.CallOption) (*StartConcertSingingResp, error)
	// 获取乐谱
	GetMusicBook(ctx context.Context, in *GetMusicBookReq, opts ...grpc.CallOption) (*GetMusicBookResp, error)
	// 完成下载
	CompleteDownloadingMusicBook(ctx context.Context, in *CompleteDownloadingMusicBookReq, opts ...grpc.CallOption) (*CompleteDownloadingMusicBookResp, error)
	// 停止演唱
	StopConcertSinging(ctx context.Context, in *StopConcertSingingReq, opts ...grpc.CallOption) (*StopConcertSingingResp, error)
	// 获取演唱信息
	GetConcertInfo(ctx context.Context, in *GetConcertInfoReq, opts ...grpc.CallOption) (*GetConcertInfoResp, error)
	// 批量获取演唱信息
	BatchGetConcertInfo(ctx context.Context, in *BatchGetConcertInfoReq, opts ...grpc.CallOption) (*BatchGetConcertInfoResp, error)
	// 获取资源
	GetAllConcertResource(ctx context.Context, in *GetAllConcertResourceReq, opts ...grpc.CallOption) (*GetAllConcertResourceResp, error)
	// 丢失心跳
	LostHeart(ctx context.Context, in *LostHeartReq, opts ...grpc.CallOption) (*LostHeartResp, error)
	// 确认推流
	ConfirmMain(ctx context.Context, in *ConfirmMainReq, opts ...grpc.CallOption) (*ConfirmMainResp, error)
	// 更新伴奏状态
	UpdateBackingTrackStatus(ctx context.Context, in *UpdateBackingTrackStatusReq, opts ...grpc.CallOption) (*UpdateBackingTrackStatusResp, error)
	// 上报成功次数
	ReportConcertSuccCount(ctx context.Context, in *ReportConcertSuccCountReq, opts ...grpc.CallOption) (*ReportConcertSuccCountResp, error)
	// 中途加入
	JoinConcert(ctx context.Context, in *JoinConcertReq, opts ...grpc.CallOption) (*JoinConcertResp, error)
	// 运营后台接口
	// 查询乐谱
	SearchSongs(ctx context.Context, in *SearchSongsReq, opts ...grpc.CallOption) (*SearchSongsResp, error)
	// 批量修改新增乐谱
	BatchUpsertSongs(ctx context.Context, in *BatchUpsertSongsReq, opts ...grpc.CallOption) (*BatchUpsertSongsResp, error)
	// 批量修改新增乐谱（自动）
	BatchUpsertSongsV2(ctx context.Context, in *BatchUpsertSongsV2Req, opts ...grpc.CallOption) (*BatchUpsertSongsV2Resp, error)
	// 删除
	DeleteSong(ctx context.Context, in *DeleteSongReq, opts ...grpc.CallOption) (*DeleteSongResp, error)
	// 查询音频
	SearchInstrumentAudios(ctx context.Context, in *SearchInstrumentAudiosReq, opts ...grpc.CallOption) (*SearchInstrumentAudiosResp, error)
	// 批量修改新增音频
	BatchUpsertInstrumentAudios(ctx context.Context, in *BatchUpsertInstrumentAudiosReq, opts ...grpc.CallOption) (*BatchUpsertInstrumentAudiosResp, error)
	// 删除
	DeleteInstrumentAudio(ctx context.Context, in *DeleteInstrumentAudioReq, opts ...grpc.CallOption) (*DeleteInstrumentAudioResp, error)
	AddActivityCode(ctx context.Context, in *AddActivityCodeReq, opts ...grpc.CallOption) (*AddActivityCodeResp, error)
	UpdateActivityCode(ctx context.Context, in *UpdateActivityCodeReq, opts ...grpc.CallOption) (*UpdateActivityCodeResp, error)
	ExchangeActivityCode(ctx context.Context, in *ExchangeActivityCodeReq, opts ...grpc.CallOption) (*ExchangeActivityCodeResp, error)
	ListActivityCode(ctx context.Context, in *ListActivityCodeReq, opts ...grpc.CallOption) (*ListActivityCodeResp, error)
	// 获取形象配置
	GetAllConcertImage(ctx context.Context, in *GetAllConcertImageReq, opts ...grpc.CallOption) (*GetAllConcertImageResp, error)
	// 设置形象
	SetConcertUserImage(ctx context.Context, in *SetConcertUserImageReq, opts ...grpc.CallOption) (*SetConcertUserImageResp, error)
	// 获取麦上用户形象
	GetAllConcertOnMicUserImage(ctx context.Context, in *GetAllConcertOnMicUserImageReq, opts ...grpc.CallOption) (*GetAllConcertOnMicUserImageResp, error)
	// 新增形象
	AddConcertImage(ctx context.Context, in *AddConcertImageReq, opts ...grpc.CallOption) (*AddConcertImageResp, error)
	// 删除形象
	DeleteConcertImage(ctx context.Context, in *DeleteConcertImageReq, opts ...grpc.CallOption) (*DeleteConcertImageResp, error)
	// 获取形象列表
	GetConcertImageList(ctx context.Context, in *GetConcertImageListReq, opts ...grpc.CallOption) (*GetConcertImageListResp, error)
	// 排序形象
	ReorderConcertImage(ctx context.Context, in *ReorderConcertImageReq, opts ...grpc.CallOption) (*ReorderConcertImageResp, error)
	// 设置默认形象
	SetDefaultImage(ctx context.Context, in *SetDefaultImageReq, opts ...grpc.CallOption) (*SetDefaultImageResp, error)
	GetConcertSongById(ctx context.Context, in *GetConcertSongByIdReq, opts ...grpc.CallOption) (*GetConcertSongByIdResp, error)
	// 获取最新上传的歌曲
	GetRecentUploadedSong(ctx context.Context, in *GetRecentUploadedSongReq, opts ...grpc.CallOption) (*GetRecentUploadedSongResp, error)
}

type concertClient struct {
	cc *grpc.ClientConn
}

func NewConcertClient(cc *grpc.ClientConn) ConcertClient {
	return &concertClient{cc}
}

func (c *concertClient) GetConcertSongOpts(ctx context.Context, in *GetConcertSongOptsReq, opts ...grpc.CallOption) (*GetConcertSongOptsResp, error) {
	out := new(GetConcertSongOptsResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetConcertSongOpts", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) SearchConcertSong(ctx context.Context, in *SearchConcertSongReq, opts ...grpc.CallOption) (*SearchConcertSongResp, error) {
	out := new(SearchConcertSongResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/SearchConcertSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetConcertSongList(ctx context.Context, in *GetConcertSongListReq, opts ...grpc.CallOption) (*GetConcertSongListResp, error) {
	out := new(GetConcertSongListResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetConcertSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) StartConcertSinging(ctx context.Context, in *StartConcertSingingReq, opts ...grpc.CallOption) (*StartConcertSingingResp, error) {
	out := new(StartConcertSingingResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/StartConcertSinging", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetMusicBook(ctx context.Context, in *GetMusicBookReq, opts ...grpc.CallOption) (*GetMusicBookResp, error) {
	out := new(GetMusicBookResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetMusicBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) CompleteDownloadingMusicBook(ctx context.Context, in *CompleteDownloadingMusicBookReq, opts ...grpc.CallOption) (*CompleteDownloadingMusicBookResp, error) {
	out := new(CompleteDownloadingMusicBookResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/CompleteDownloadingMusicBook", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) StopConcertSinging(ctx context.Context, in *StopConcertSingingReq, opts ...grpc.CallOption) (*StopConcertSingingResp, error) {
	out := new(StopConcertSingingResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/StopConcertSinging", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetConcertInfo(ctx context.Context, in *GetConcertInfoReq, opts ...grpc.CallOption) (*GetConcertInfoResp, error) {
	out := new(GetConcertInfoResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetConcertInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) BatchGetConcertInfo(ctx context.Context, in *BatchGetConcertInfoReq, opts ...grpc.CallOption) (*BatchGetConcertInfoResp, error) {
	out := new(BatchGetConcertInfoResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/BatchGetConcertInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetAllConcertResource(ctx context.Context, in *GetAllConcertResourceReq, opts ...grpc.CallOption) (*GetAllConcertResourceResp, error) {
	out := new(GetAllConcertResourceResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetAllConcertResource", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) LostHeart(ctx context.Context, in *LostHeartReq, opts ...grpc.CallOption) (*LostHeartResp, error) {
	out := new(LostHeartResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/LostHeart", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) ConfirmMain(ctx context.Context, in *ConfirmMainReq, opts ...grpc.CallOption) (*ConfirmMainResp, error) {
	out := new(ConfirmMainResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/ConfirmMain", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) UpdateBackingTrackStatus(ctx context.Context, in *UpdateBackingTrackStatusReq, opts ...grpc.CallOption) (*UpdateBackingTrackStatusResp, error) {
	out := new(UpdateBackingTrackStatusResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/UpdateBackingTrackStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) ReportConcertSuccCount(ctx context.Context, in *ReportConcertSuccCountReq, opts ...grpc.CallOption) (*ReportConcertSuccCountResp, error) {
	out := new(ReportConcertSuccCountResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/ReportConcertSuccCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) JoinConcert(ctx context.Context, in *JoinConcertReq, opts ...grpc.CallOption) (*JoinConcertResp, error) {
	out := new(JoinConcertResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/JoinConcert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) SearchSongs(ctx context.Context, in *SearchSongsReq, opts ...grpc.CallOption) (*SearchSongsResp, error) {
	out := new(SearchSongsResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/SearchSongs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) BatchUpsertSongs(ctx context.Context, in *BatchUpsertSongsReq, opts ...grpc.CallOption) (*BatchUpsertSongsResp, error) {
	out := new(BatchUpsertSongsResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/BatchUpsertSongs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) BatchUpsertSongsV2(ctx context.Context, in *BatchUpsertSongsV2Req, opts ...grpc.CallOption) (*BatchUpsertSongsV2Resp, error) {
	out := new(BatchUpsertSongsV2Resp)
	err := c.cc.Invoke(ctx, "/concert.Concert/BatchUpsertSongsV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) DeleteSong(ctx context.Context, in *DeleteSongReq, opts ...grpc.CallOption) (*DeleteSongResp, error) {
	out := new(DeleteSongResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/DeleteSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) SearchInstrumentAudios(ctx context.Context, in *SearchInstrumentAudiosReq, opts ...grpc.CallOption) (*SearchInstrumentAudiosResp, error) {
	out := new(SearchInstrumentAudiosResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/SearchInstrumentAudios", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) BatchUpsertInstrumentAudios(ctx context.Context, in *BatchUpsertInstrumentAudiosReq, opts ...grpc.CallOption) (*BatchUpsertInstrumentAudiosResp, error) {
	out := new(BatchUpsertInstrumentAudiosResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/BatchUpsertInstrumentAudios", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) DeleteInstrumentAudio(ctx context.Context, in *DeleteInstrumentAudioReq, opts ...grpc.CallOption) (*DeleteInstrumentAudioResp, error) {
	out := new(DeleteInstrumentAudioResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/DeleteInstrumentAudio", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) AddActivityCode(ctx context.Context, in *AddActivityCodeReq, opts ...grpc.CallOption) (*AddActivityCodeResp, error) {
	out := new(AddActivityCodeResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/AddActivityCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) UpdateActivityCode(ctx context.Context, in *UpdateActivityCodeReq, opts ...grpc.CallOption) (*UpdateActivityCodeResp, error) {
	out := new(UpdateActivityCodeResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/UpdateActivityCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) ExchangeActivityCode(ctx context.Context, in *ExchangeActivityCodeReq, opts ...grpc.CallOption) (*ExchangeActivityCodeResp, error) {
	out := new(ExchangeActivityCodeResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/ExchangeActivityCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) ListActivityCode(ctx context.Context, in *ListActivityCodeReq, opts ...grpc.CallOption) (*ListActivityCodeResp, error) {
	out := new(ListActivityCodeResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/ListActivityCode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetAllConcertImage(ctx context.Context, in *GetAllConcertImageReq, opts ...grpc.CallOption) (*GetAllConcertImageResp, error) {
	out := new(GetAllConcertImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetAllConcertImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) SetConcertUserImage(ctx context.Context, in *SetConcertUserImageReq, opts ...grpc.CallOption) (*SetConcertUserImageResp, error) {
	out := new(SetConcertUserImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/SetConcertUserImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetAllConcertOnMicUserImage(ctx context.Context, in *GetAllConcertOnMicUserImageReq, opts ...grpc.CallOption) (*GetAllConcertOnMicUserImageResp, error) {
	out := new(GetAllConcertOnMicUserImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetAllConcertOnMicUserImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) AddConcertImage(ctx context.Context, in *AddConcertImageReq, opts ...grpc.CallOption) (*AddConcertImageResp, error) {
	out := new(AddConcertImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/AddConcertImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) DeleteConcertImage(ctx context.Context, in *DeleteConcertImageReq, opts ...grpc.CallOption) (*DeleteConcertImageResp, error) {
	out := new(DeleteConcertImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/DeleteConcertImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetConcertImageList(ctx context.Context, in *GetConcertImageListReq, opts ...grpc.CallOption) (*GetConcertImageListResp, error) {
	out := new(GetConcertImageListResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetConcertImageList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) ReorderConcertImage(ctx context.Context, in *ReorderConcertImageReq, opts ...grpc.CallOption) (*ReorderConcertImageResp, error) {
	out := new(ReorderConcertImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/ReorderConcertImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) SetDefaultImage(ctx context.Context, in *SetDefaultImageReq, opts ...grpc.CallOption) (*SetDefaultImageResp, error) {
	out := new(SetDefaultImageResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/SetDefaultImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetConcertSongById(ctx context.Context, in *GetConcertSongByIdReq, opts ...grpc.CallOption) (*GetConcertSongByIdResp, error) {
	out := new(GetConcertSongByIdResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetConcertSongById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *concertClient) GetRecentUploadedSong(ctx context.Context, in *GetRecentUploadedSongReq, opts ...grpc.CallOption) (*GetRecentUploadedSongResp, error) {
	out := new(GetRecentUploadedSongResp)
	err := c.cc.Invoke(ctx, "/concert.Concert/GetRecentUploadedSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConcertServer is the server API for Concert service.
type ConcertServer interface {
	// 获取演唱资源
	GetConcertSongOpts(context.Context, *GetConcertSongOptsReq) (*GetConcertSongOptsResp, error)
	// 搜索歌曲
	SearchConcertSong(context.Context, *SearchConcertSongReq) (*SearchConcertSongResp, error)
	// 获取歌曲
	GetConcertSongList(context.Context, *GetConcertSongListReq) (*GetConcertSongListResp, error)
	// 点歌
	StartConcertSinging(context.Context, *StartConcertSingingReq) (*StartConcertSingingResp, error)
	// 获取乐谱
	GetMusicBook(context.Context, *GetMusicBookReq) (*GetMusicBookResp, error)
	// 完成下载
	CompleteDownloadingMusicBook(context.Context, *CompleteDownloadingMusicBookReq) (*CompleteDownloadingMusicBookResp, error)
	// 停止演唱
	StopConcertSinging(context.Context, *StopConcertSingingReq) (*StopConcertSingingResp, error)
	// 获取演唱信息
	GetConcertInfo(context.Context, *GetConcertInfoReq) (*GetConcertInfoResp, error)
	// 批量获取演唱信息
	BatchGetConcertInfo(context.Context, *BatchGetConcertInfoReq) (*BatchGetConcertInfoResp, error)
	// 获取资源
	GetAllConcertResource(context.Context, *GetAllConcertResourceReq) (*GetAllConcertResourceResp, error)
	// 丢失心跳
	LostHeart(context.Context, *LostHeartReq) (*LostHeartResp, error)
	// 确认推流
	ConfirmMain(context.Context, *ConfirmMainReq) (*ConfirmMainResp, error)
	// 更新伴奏状态
	UpdateBackingTrackStatus(context.Context, *UpdateBackingTrackStatusReq) (*UpdateBackingTrackStatusResp, error)
	// 上报成功次数
	ReportConcertSuccCount(context.Context, *ReportConcertSuccCountReq) (*ReportConcertSuccCountResp, error)
	// 中途加入
	JoinConcert(context.Context, *JoinConcertReq) (*JoinConcertResp, error)
	// 运营后台接口
	// 查询乐谱
	SearchSongs(context.Context, *SearchSongsReq) (*SearchSongsResp, error)
	// 批量修改新增乐谱
	BatchUpsertSongs(context.Context, *BatchUpsertSongsReq) (*BatchUpsertSongsResp, error)
	// 批量修改新增乐谱（自动）
	BatchUpsertSongsV2(context.Context, *BatchUpsertSongsV2Req) (*BatchUpsertSongsV2Resp, error)
	// 删除
	DeleteSong(context.Context, *DeleteSongReq) (*DeleteSongResp, error)
	// 查询音频
	SearchInstrumentAudios(context.Context, *SearchInstrumentAudiosReq) (*SearchInstrumentAudiosResp, error)
	// 批量修改新增音频
	BatchUpsertInstrumentAudios(context.Context, *BatchUpsertInstrumentAudiosReq) (*BatchUpsertInstrumentAudiosResp, error)
	// 删除
	DeleteInstrumentAudio(context.Context, *DeleteInstrumentAudioReq) (*DeleteInstrumentAudioResp, error)
	AddActivityCode(context.Context, *AddActivityCodeReq) (*AddActivityCodeResp, error)
	UpdateActivityCode(context.Context, *UpdateActivityCodeReq) (*UpdateActivityCodeResp, error)
	ExchangeActivityCode(context.Context, *ExchangeActivityCodeReq) (*ExchangeActivityCodeResp, error)
	ListActivityCode(context.Context, *ListActivityCodeReq) (*ListActivityCodeResp, error)
	// 获取形象配置
	GetAllConcertImage(context.Context, *GetAllConcertImageReq) (*GetAllConcertImageResp, error)
	// 设置形象
	SetConcertUserImage(context.Context, *SetConcertUserImageReq) (*SetConcertUserImageResp, error)
	// 获取麦上用户形象
	GetAllConcertOnMicUserImage(context.Context, *GetAllConcertOnMicUserImageReq) (*GetAllConcertOnMicUserImageResp, error)
	// 新增形象
	AddConcertImage(context.Context, *AddConcertImageReq) (*AddConcertImageResp, error)
	// 删除形象
	DeleteConcertImage(context.Context, *DeleteConcertImageReq) (*DeleteConcertImageResp, error)
	// 获取形象列表
	GetConcertImageList(context.Context, *GetConcertImageListReq) (*GetConcertImageListResp, error)
	// 排序形象
	ReorderConcertImage(context.Context, *ReorderConcertImageReq) (*ReorderConcertImageResp, error)
	// 设置默认形象
	SetDefaultImage(context.Context, *SetDefaultImageReq) (*SetDefaultImageResp, error)
	GetConcertSongById(context.Context, *GetConcertSongByIdReq) (*GetConcertSongByIdResp, error)
	// 获取最新上传的歌曲
	GetRecentUploadedSong(context.Context, *GetRecentUploadedSongReq) (*GetRecentUploadedSongResp, error)
}

func RegisterConcertServer(s *grpc.Server, srv ConcertServer) {
	s.RegisterService(&_Concert_serviceDesc, srv)
}

func _Concert_GetConcertSongOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConcertSongOptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetConcertSongOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetConcertSongOpts",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetConcertSongOpts(ctx, req.(*GetConcertSongOptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_SearchConcertSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchConcertSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).SearchConcertSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/SearchConcertSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).SearchConcertSong(ctx, req.(*SearchConcertSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetConcertSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConcertSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetConcertSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetConcertSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetConcertSongList(ctx, req.(*GetConcertSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_StartConcertSinging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartConcertSingingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).StartConcertSinging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/StartConcertSinging",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).StartConcertSinging(ctx, req.(*StartConcertSingingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetMusicBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetMusicBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetMusicBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetMusicBook(ctx, req.(*GetMusicBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_CompleteDownloadingMusicBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteDownloadingMusicBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).CompleteDownloadingMusicBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/CompleteDownloadingMusicBook",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).CompleteDownloadingMusicBook(ctx, req.(*CompleteDownloadingMusicBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_StopConcertSinging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopConcertSingingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).StopConcertSinging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/StopConcertSinging",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).StopConcertSinging(ctx, req.(*StopConcertSingingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetConcertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConcertInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetConcertInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetConcertInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetConcertInfo(ctx, req.(*GetConcertInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_BatchGetConcertInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetConcertInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).BatchGetConcertInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/BatchGetConcertInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).BatchGetConcertInfo(ctx, req.(*BatchGetConcertInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetAllConcertResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllConcertResourceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetAllConcertResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetAllConcertResource",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetAllConcertResource(ctx, req.(*GetAllConcertResourceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_LostHeart_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LostHeartReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).LostHeart(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/LostHeart",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).LostHeart(ctx, req.(*LostHeartReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_ConfirmMain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmMainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).ConfirmMain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/ConfirmMain",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).ConfirmMain(ctx, req.(*ConfirmMainReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_UpdateBackingTrackStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBackingTrackStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).UpdateBackingTrackStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/UpdateBackingTrackStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).UpdateBackingTrackStatus(ctx, req.(*UpdateBackingTrackStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_ReportConcertSuccCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportConcertSuccCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).ReportConcertSuccCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/ReportConcertSuccCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).ReportConcertSuccCount(ctx, req.(*ReportConcertSuccCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_JoinConcert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinConcertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).JoinConcert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/JoinConcert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).JoinConcert(ctx, req.(*JoinConcertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_SearchSongs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchSongsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).SearchSongs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/SearchSongs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).SearchSongs(ctx, req.(*SearchSongsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_BatchUpsertSongs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertSongsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).BatchUpsertSongs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/BatchUpsertSongs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).BatchUpsertSongs(ctx, req.(*BatchUpsertSongsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_BatchUpsertSongsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertSongsV2Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).BatchUpsertSongsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/BatchUpsertSongsV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).BatchUpsertSongsV2(ctx, req.(*BatchUpsertSongsV2Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_DeleteSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).DeleteSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/DeleteSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).DeleteSong(ctx, req.(*DeleteSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_SearchInstrumentAudios_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchInstrumentAudiosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).SearchInstrumentAudios(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/SearchInstrumentAudios",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).SearchInstrumentAudios(ctx, req.(*SearchInstrumentAudiosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_BatchUpsertInstrumentAudios_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUpsertInstrumentAudiosReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).BatchUpsertInstrumentAudios(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/BatchUpsertInstrumentAudios",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).BatchUpsertInstrumentAudios(ctx, req.(*BatchUpsertInstrumentAudiosReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_DeleteInstrumentAudio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteInstrumentAudioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).DeleteInstrumentAudio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/DeleteInstrumentAudio",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).DeleteInstrumentAudio(ctx, req.(*DeleteInstrumentAudioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_AddActivityCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddActivityCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).AddActivityCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/AddActivityCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).AddActivityCode(ctx, req.(*AddActivityCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_UpdateActivityCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActivityCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).UpdateActivityCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/UpdateActivityCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).UpdateActivityCode(ctx, req.(*UpdateActivityCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_ExchangeActivityCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExchangeActivityCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).ExchangeActivityCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/ExchangeActivityCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).ExchangeActivityCode(ctx, req.(*ExchangeActivityCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_ListActivityCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListActivityCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).ListActivityCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/ListActivityCode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).ListActivityCode(ctx, req.(*ListActivityCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetAllConcertImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllConcertImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetAllConcertImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetAllConcertImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetAllConcertImage(ctx, req.(*GetAllConcertImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_SetConcertUserImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetConcertUserImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).SetConcertUserImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/SetConcertUserImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).SetConcertUserImage(ctx, req.(*SetConcertUserImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetAllConcertOnMicUserImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllConcertOnMicUserImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetAllConcertOnMicUserImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetAllConcertOnMicUserImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetAllConcertOnMicUserImage(ctx, req.(*GetAllConcertOnMicUserImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_AddConcertImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddConcertImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).AddConcertImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/AddConcertImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).AddConcertImage(ctx, req.(*AddConcertImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_DeleteConcertImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConcertImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).DeleteConcertImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/DeleteConcertImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).DeleteConcertImage(ctx, req.(*DeleteConcertImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetConcertImageList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConcertImageListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetConcertImageList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetConcertImageList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetConcertImageList(ctx, req.(*GetConcertImageListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_ReorderConcertImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReorderConcertImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).ReorderConcertImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/ReorderConcertImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).ReorderConcertImage(ctx, req.(*ReorderConcertImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_SetDefaultImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetDefaultImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).SetDefaultImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/SetDefaultImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).SetDefaultImage(ctx, req.(*SetDefaultImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetConcertSongById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConcertSongByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetConcertSongById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetConcertSongById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetConcertSongById(ctx, req.(*GetConcertSongByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Concert_GetRecentUploadedSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecentUploadedSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConcertServer).GetRecentUploadedSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/concert.Concert/GetRecentUploadedSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConcertServer).GetRecentUploadedSong(ctx, req.(*GetRecentUploadedSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Concert_serviceDesc = grpc.ServiceDesc{
	ServiceName: "concert.Concert",
	HandlerType: (*ConcertServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetConcertSongOpts",
			Handler:    _Concert_GetConcertSongOpts_Handler,
		},
		{
			MethodName: "SearchConcertSong",
			Handler:    _Concert_SearchConcertSong_Handler,
		},
		{
			MethodName: "GetConcertSongList",
			Handler:    _Concert_GetConcertSongList_Handler,
		},
		{
			MethodName: "StartConcertSinging",
			Handler:    _Concert_StartConcertSinging_Handler,
		},
		{
			MethodName: "GetMusicBook",
			Handler:    _Concert_GetMusicBook_Handler,
		},
		{
			MethodName: "CompleteDownloadingMusicBook",
			Handler:    _Concert_CompleteDownloadingMusicBook_Handler,
		},
		{
			MethodName: "StopConcertSinging",
			Handler:    _Concert_StopConcertSinging_Handler,
		},
		{
			MethodName: "GetConcertInfo",
			Handler:    _Concert_GetConcertInfo_Handler,
		},
		{
			MethodName: "BatchGetConcertInfo",
			Handler:    _Concert_BatchGetConcertInfo_Handler,
		},
		{
			MethodName: "GetAllConcertResource",
			Handler:    _Concert_GetAllConcertResource_Handler,
		},
		{
			MethodName: "LostHeart",
			Handler:    _Concert_LostHeart_Handler,
		},
		{
			MethodName: "ConfirmMain",
			Handler:    _Concert_ConfirmMain_Handler,
		},
		{
			MethodName: "UpdateBackingTrackStatus",
			Handler:    _Concert_UpdateBackingTrackStatus_Handler,
		},
		{
			MethodName: "ReportConcertSuccCount",
			Handler:    _Concert_ReportConcertSuccCount_Handler,
		},
		{
			MethodName: "JoinConcert",
			Handler:    _Concert_JoinConcert_Handler,
		},
		{
			MethodName: "SearchSongs",
			Handler:    _Concert_SearchSongs_Handler,
		},
		{
			MethodName: "BatchUpsertSongs",
			Handler:    _Concert_BatchUpsertSongs_Handler,
		},
		{
			MethodName: "BatchUpsertSongsV2",
			Handler:    _Concert_BatchUpsertSongsV2_Handler,
		},
		{
			MethodName: "DeleteSong",
			Handler:    _Concert_DeleteSong_Handler,
		},
		{
			MethodName: "SearchInstrumentAudios",
			Handler:    _Concert_SearchInstrumentAudios_Handler,
		},
		{
			MethodName: "BatchUpsertInstrumentAudios",
			Handler:    _Concert_BatchUpsertInstrumentAudios_Handler,
		},
		{
			MethodName: "DeleteInstrumentAudio",
			Handler:    _Concert_DeleteInstrumentAudio_Handler,
		},
		{
			MethodName: "AddActivityCode",
			Handler:    _Concert_AddActivityCode_Handler,
		},
		{
			MethodName: "UpdateActivityCode",
			Handler:    _Concert_UpdateActivityCode_Handler,
		},
		{
			MethodName: "ExchangeActivityCode",
			Handler:    _Concert_ExchangeActivityCode_Handler,
		},
		{
			MethodName: "ListActivityCode",
			Handler:    _Concert_ListActivityCode_Handler,
		},
		{
			MethodName: "GetAllConcertImage",
			Handler:    _Concert_GetAllConcertImage_Handler,
		},
		{
			MethodName: "SetConcertUserImage",
			Handler:    _Concert_SetConcertUserImage_Handler,
		},
		{
			MethodName: "GetAllConcertOnMicUserImage",
			Handler:    _Concert_GetAllConcertOnMicUserImage_Handler,
		},
		{
			MethodName: "AddConcertImage",
			Handler:    _Concert_AddConcertImage_Handler,
		},
		{
			MethodName: "DeleteConcertImage",
			Handler:    _Concert_DeleteConcertImage_Handler,
		},
		{
			MethodName: "GetConcertImageList",
			Handler:    _Concert_GetConcertImageList_Handler,
		},
		{
			MethodName: "ReorderConcertImage",
			Handler:    _Concert_ReorderConcertImage_Handler,
		},
		{
			MethodName: "SetDefaultImage",
			Handler:    _Concert_SetDefaultImage_Handler,
		},
		{
			MethodName: "GetConcertSongById",
			Handler:    _Concert_GetConcertSongById_Handler,
		},
		{
			MethodName: "GetRecentUploadedSong",
			Handler:    _Concert_GetRecentUploadedSong_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "concert/concert.proto",
}

func init() { proto.RegisterFile("concert/concert.proto", fileDescriptor_concert_5ced15a6310fe11a) }

var fileDescriptor_concert_5ced15a6310fe11a = []byte{
	// 4116 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x3b, 0xcb, 0x52, 0x24, 0x49,
	0x72, 0xd4, 0x03, 0xa8, 0xf2, 0xa2, 0xa0, 0x08, 0x5e, 0x49, 0x75, 0x0f, 0x30, 0x39, 0x3b, 0xbb,
	0x0c, 0xb3, 0x43, 0x77, 0xa3, 0x69, 0x9b, 0xd1, 0xac, 0x6c, 0x77, 0x0b, 0xa8, 0x61, 0x6a, 0x9b,
	0x82, 0xde, 0x2c, 0x98, 0xb6, 0x1d, 0x99, 0x2c, 0x95, 0x64, 0x06, 0x45, 0x8a, 0x7c, 0x6d, 0x46,
	0x16, 0xdd, 0x48, 0x17, 0xc9, 0x74, 0x93, 0xe9, 0xa2, 0x83, 0x0e, 0xfa, 0x90, 0x3d, 0xe9, 0x20,
	0x93, 0x99, 0x0e, 0x32, 0x93, 0x99, 0xae, 0x3a, 0xe8, 0xa2, 0x0f, 0xd0, 0x47, 0xc8, 0x22, 0x22,
	0x1f, 0x91, 0xaf, 0x6a, 0xd8, 0x6d, 0x9d, 0x2a, 0xc3, 0xdd, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3, 0xc3,
	0xdd, 0x23, 0x0a, 0xd6, 0x74, 0xd7, 0xd1, 0xb1, 0x1f, 0x3c, 0x0b, 0x7f, 0xf7, 0x3d, 0xdf, 0x0d,
	0x5c, 0x34, 0x1f, 0x36, 0xe5, 0x35, 0x58, 0x39, 0x35, 0x49, 0xd0, 0xd3, 0x03, 0xf3, 0xce, 0x0c,
	0xee, 0x8f, 0x5c, 0x03, 0x2b, 0xf8, 0xb7, 0xf2, 0x11, 0xac, 0xe6, 0xc1, 0xc4, 0x43, 0x9f, 0xc3,
	0xac, 0xee, 0x1a, 0x98, 0x48, 0x95, 0x9d, 0xda, 0x6e, 0xeb, 0x60, 0x6d, 0x3f, 0x62, 0x9b, 0xa2,
	0xe4, 0x34, 0xf2, 0x1d, 0x2c, 0x88, 0x60, 0x84, 0xa0, 0x4e, 0x11, 0x52, 0x65, 0xa7, 0xb2, 0xdb,
	0x54, 0xd8, 0x37, 0x5a, 0xa5, 0x0c, 0x27, 0x4e, 0x20, 0x55, 0x77, 0x2a, 0xbb, 0x6d, 0x85, 0x37,
	0x28, 0xa5, 0xe3, 0x06, 0x58, 0xaa, 0x71, 0x4a, 0xfa, 0x8d, 0x3a, 0x50, 0x9b, 0xf8, 0x96, 0x54,
	0x67, 0x20, 0xfa, 0x89, 0xd6, 0x61, 0xce, 0xf3, 0xf1, 0xb5, 0xf9, 0x4e, 0x9a, 0x65, 0xc0, 0xb0,
	0x25, 0xff, 0x02, 0x36, 0xfa, 0xef, 0xf4, 0x1b, 0xcd, 0x19, 0xe3, 0xcc, 0xbc, 0x18, 0x13, 0xd3,
	0x60, 0x12, 0xb4, 0x15, 0xfa, 0x19, 0x0b, 0x55, 0x4d, 0x84, 0x92, 0x7f, 0x0a, 0x52, 0x31, 0x03,
	0xe2, 0x45, 0x62, 0x54, 0x62, 0x31, 0xe4, 0x63, 0x58, 0xbb, 0xf4, 0x0c, 0x2d, 0xc8, 0x0d, 0xf6,
	0x28, 0x65, 0xf5, 0x61, 0xbd, 0x88, 0xcb, 0x63, 0x75, 0xde, 0x03, 0xd4, 0x33, 0x8c, 0x3f, 0x48,
	0x92, 0x43, 0x58, 0xc9, 0xb1, 0x78, 0xac, 0x18, 0xff, 0x58, 0x81, 0x27, 0x7c, 0x3a, 0x87, 0x9a,
	0x7e, 0x6b, 0x3a, 0xe3, 0x0b, 0x5f, 0xd3, 0x6f, 0x47, 0x81, 0x16, 0x4c, 0x48, 0xf1, 0x3a, 0x7c,
	0x04, 0x40, 0x35, 0xee, 0x60, 0x4b, 0x35, 0x8d, 0xd0, 0x1a, 0x9a, 0x21, 0x64, 0x60, 0xa0, 0x0d,
	0x98, 0x1f, 0x6b, 0x36, 0xa6, 0xb8, 0x1a, 0xc3, 0xcd, 0xd1, 0xe6, 0xc0, 0x40, 0x3f, 0x05, 0xe4,
	0x7a, 0xd8, 0x51, 0xaf, 0xf8, 0x38, 0x6a, 0x40, 0x07, 0x62, 0x56, 0xd2, 0x50, 0x3a, 0x14, 0x23,
	0x0a, 0x20, 0x6f, 0xc1, 0xd3, 0x72, 0xb1, 0x88, 0x27, 0x6f, 0xc0, 0xda, 0x09, 0x0e, 0x8e, 0xf8,
	0xcc, 0x46, 0xae, 0x33, 0x3e, 0xf7, 0x02, 0x2a, 0x30, 0x5d, 0x9e, 0x22, 0x04, 0xd3, 0x4b, 0xdd,
	0xf5, 0x82, 0x48, 0x2d, 0x1b, 0xb1, 0x5a, 0xd2, 0xb4, 0x0a, 0x23, 0x92, 0x5f, 0xc0, 0xea, 0x08,
	0x6b, 0xbe, 0x7e, 0x23, 0x60, 0xa9, 0x3e, 0x36, 0xa1, 0x71, 0x8b, 0xef, 0xd5, 0xb7, 0xae, 0x6f,
	0x84, 0xa6, 0x35, 0x7f, 0x8b, 0xef, 0xdf, 0xb8, 0xbe, 0x21, 0x1f, 0xc1, 0x5a, 0x41, 0x17, 0xe2,
	0xa1, 0x3d, 0x98, 0x25, 0xae, 0x33, 0x8e, 0x46, 0x5e, 0x2d, 0x1a, 0x59, 0xe1, 0x24, 0xf2, 0x3f,
	0x54, 0xb2, 0x13, 0xa3, 0xdb, 0x9b, 0x9b, 0xc6, 0xc3, 0xc5, 0x47, 0x2f, 0xa1, 0x69, 0xb9, 0x9a,
	0xa1, 0xda, 0xae, 0xcf, 0x77, 0x4c, 0xeb, 0x40, 0xca, 0xf6, 0x38, 0x75, 0x35, 0x63, 0xe8, 0xfa,
	0x58, 0x69, 0x58, 0xe1, 0x57, 0xb2, 0xc9, 0x6b, 0xc2, 0x26, 0x97, 0xff, 0x2a, 0xab, 0x52, 0x2e,
	0xd2, 0xe3, 0x66, 0xf6, 0x7b, 0x8a, 0x24, 0xff, 0xcb, 0x2c, 0xb4, 0x04, 0x6e, 0x68, 0x11, 0xaa,
	0x66, 0xa4, 0xfa, 0x2a, 0x77, 0x0b, 0x8e, 0x66, 0xc7, 0x6e, 0x81, 0x7e, 0x53, 0x9a, 0xab, 0x71,
	0xe8, 0x93, 0xaa, 0x57, 0x63, 0xea, 0x7f, 0xb4, 0x49, 0x70, 0xe3, 0xfa, 0xa1, 0x53, 0x0a, 0x5b,
	0xd4, 0x56, 0x6f, 0xdc, 0x40, 0xbd, 0xd3, 0x2c, 0xe6, 0x98, 0xda, 0xca, 0xdc, 0x8d, 0x1b, 0x7c,
	0xaf, 0x59, 0xd4, 0xea, 0x2d, 0x5f, 0x97, 0xe6, 0xb8, 0xef, 0xb0, 0x7c, 0x1d, 0x9d, 0xc3, 0x12,
	0xb9, 0xc1, 0x38, 0x50, 0xed, 0x09, 0x31, 0x75, 0xd5, 0xd6, 0x3c, 0x69, 0x9e, 0xcd, 0xf9, 0x27,
	0x45, 0x73, 0xde, 0x1f, 0x51, 0xda, 0x21, 0x25, 0x1d, 0x6a, 0x5e, 0xdf, 0x09, 0xfc, 0x7b, 0xa5,
	0x4d, 0x44, 0x18, 0xea, 0x42, 0xc3, 0xd2, 0x9c, 0xf1, 0x44, 0x1b, 0x63, 0xa9, 0xc1, 0xc6, 0x89,
	0xdb, 0x68, 0x0b, 0x6a, 0x04, 0xbf, 0x93, 0x9a, 0x3b, 0x95, 0xdd, 0xc5, 0x83, 0x85, 0x78, 0x80,
	0x11, 0x7e, 0xa7, 0x50, 0x04, 0xda, 0x02, 0x30, 0x1d, 0x12, 0xf8, 0x13, 0x1b, 0x3b, 0x81, 0x04,
	0xac, 0xb7, 0x00, 0x41, 0xbb, 0x30, 0x4b, 0x02, 0xca, 0xb8, 0xc5, 0x38, 0xa0, 0x84, 0x03, 0x85,
	0x06, 0xf7, 0x1e, 0x56, 0x38, 0x01, 0x95, 0xc2, 0x98, 0xf8, 0x5a, 0x60, 0xba, 0x8e, 0xb4, 0xc0,
	0x54, 0x10, 0xb7, 0xd1, 0x0e, 0x2c, 0x58, 0xbe, 0xae, 0x5e, 0xe1, 0xb1, 0xe9, 0xa8, 0x36, 0x91,
	0xda, 0x0c, 0x0f, 0x96, 0xaf, 0x1f, 0x52, 0xd0, 0x90, 0x50, 0xbd, 0x12, 0xd3, 0x19, 0x63, 0x5f,
	0x5a, 0xe4, 0x7a, 0xe5, 0x2d, 0xf4, 0x09, 0xb4, 0x35, 0x5d, 0x77, 0x6d, 0x4f, 0x73, 0xee, 0x55,
	0xea, 0x84, 0x97, 0x18, 0x7a, 0x21, 0x06, 0x5e, 0xfa, 0x56, 0x9a, 0xc8, 0x36, 0x5e, 0x4a, 0x9d,
	0x0c, 0xd1, 0xd0, 0x78, 0x49, 0x17, 0xe2, 0xca, 0xb3, 0xa5, 0x65, 0xee, 0x7e, 0xae, 0x3c, 0x1b,
	0x3d, 0x81, 0xa6, 0x49, 0x54, 0xd7, 0xb1, 0x4c, 0x07, 0x4b, 0x88, 0x79, 0x8f, 0x86, 0x49, 0xce,
	0x59, 0x1b, 0x7d, 0x0a, 0x35, 0x1f, 0x13, 0x69, 0x85, 0xad, 0xcc, 0x4a, 0x3c, 0xed, 0x41, 0xac,
	0x1a, 0x85, 0xe2, 0xd1, 0x1a, 0xcc, 0x99, 0x44, 0x75, 0xf0, 0x5b, 0x69, 0x95, 0x31, 0x98, 0x35,
	0xc9, 0x19, 0x7e, 0xdb, 0xbd, 0x04, 0x94, 0x5f, 0x37, 0x2a, 0xc2, 0x2d, 0xbe, 0x8f, 0xce, 0x91,
	0x5b, 0x7c, 0x8f, 0x3e, 0x83, 0xd9, 0x3b, 0xcd, 0x9a, 0x44, 0x56, 0x9c, 0x8c, 0x93, 0xf4, 0x56,
	0x38, 0xc5, 0x37, 0xd5, 0xaf, 0x2b, 0xf2, 0xff, 0x54, 0x00, 0x12, 0x09, 0x62, 0x83, 0xad, 0x08,
	0x06, 0x8b, 0xa0, 0x6e, 0x9b, 0x86, 0x19, 0x19, 0x31, 0xfd, 0xce, 0x2b, 0xb1, 0xf6, 0x10, 0x25,
	0xd6, 0x0b, 0x94, 0xb8, 0x07, 0xcb, 0x84, 0xba, 0x64, 0xea, 0xb8, 0x4c, 0x27, 0xc0, 0x7e, 0x62,
	0xf0, 0x4b, 0x14, 0xf1, 0x0a, 0xdf, 0x0f, 0x42, 0x30, 0xfa, 0x12, 0xd6, 0x0d, 0xf3, 0xfa, 0x1a,
	0xfb, 0xd8, 0x09, 0xd2, 0x1d, 0xe6, 0x58, 0x87, 0xd5, 0x18, 0x2b, 0xf4, 0x92, 0xcf, 0x00, 0x92,
	0xb9, 0xa3, 0x5f, 0xc2, 0xb2, 0x7f, 0x73, 0x1f, 0xdc, 0xd8, 0xaa, 0xe7, 0x9a, 0x4e, 0xa0, 0x5a,
	0x26, 0x09, 0x72, 0x1e, 0x42, 0x61, 0x14, 0xaf, 0x29, 0x81, 0xb2, 0xe4, 0x27, 0x0d, 0xea, 0x5b,
	0xe4, 0x21, 0x2c, 0x65, 0x3c, 0x02, 0x5d, 0x77, 0x4b, 0x23, 0x81, 0xea, 0x51, 0xbb, 0xe6, 0xc7,
	0x51, 0x83, 0x02, 0x5e, 0x53, 0x33, 0xfe, 0x08, 0x80, 0x21, 0xc5, 0x08, 0x85, 0x91, 0x1f, 0x31,
	0x07, 0x36, 0x86, 0xc5, 0xb4, 0x97, 0x7c, 0x90, 0x17, 0x79, 0x01, 0xb3, 0xd8, 0xc2, 0x36, 0x91,
	0x6a, 0x4c, 0xf4, 0x27, 0x25, 0x1e, 0xb7, 0x6f, 0x61, 0x5b, 0xe1, 0x94, 0xf2, 0xd7, 0x80, 0xf2,
	0xc8, 0x87, 0x0c, 0x26, 0x7f, 0x09, 0xd2, 0x09, 0x0e, 0x7a, 0x96, 0x15, 0xf6, 0x57, 0x30, 0x71,
	0x27, 0xbe, 0xce, 0x82, 0x02, 0x09, 0xe6, 0xef, 0xb0, 0x4f, 0xe8, 0x1e, 0xe5, 0x13, 0x8f, 0x9a,
	0xf2, 0x7f, 0x55, 0x61, 0xb3, 0xa4, 0x1b, 0xf1, 0xca, 0xfb, 0xa1, 0x17, 0xd0, 0xf0, 0x31, 0xe1,
	0x0b, 0x53, 0x65, 0xb3, 0x5b, 0x2f, 0xda, 0x2c, 0x98, 0x28, 0xf3, 0x3e, 0x26, 0x74, 0x49, 0x50,
	0x0f, 0x5a, 0x06, 0xd6, 0x5d, 0xee, 0x1b, 0x22, 0x9d, 0x6c, 0xe7, 0x74, 0x42, 0xbd, 0xca, 0x71,
	0x4c, 0xa7, 0x88, 0x7d, 0xd0, 0x36, 0x65, 0x71, 0xad, 0x4d, 0xac, 0x40, 0x4d, 0x02, 0x44, 0x08,
	0x41, 0xd4, 0x9a, 0x05, 0x02, 0x6a, 0xcb, 0xb3, 0x29, 0x02, 0x6a, 0xc9, 0xd4, 0x3a, 0x43, 0x82,
	0xc0, 0xd7, 0x1c, 0x62, 0x52, 0xc6, 0xaa, 0x69, 0x8f, 0x43, 0x57, 0xbd, 0x1a, 0x62, 0x2f, 0x62,
	0xe4, 0xc0, 0x1e, 0xa3, 0x03, 0x00, 0xe2, 0x3a, 0xa6, 0xae, 0xbe, 0xd5, 0xee, 0xb0, 0x34, 0x9f,
	0xd9, 0xb4, 0x89, 0xe6, 0x94, 0x26, 0x23, 0x7b, 0xa3, 0xdd, 0x61, 0xf9, 0x39, 0x40, 0x82, 0xc8,
	0xc7, 0x92, 0x14, 0x42, 0x45, 0xe4, 0x4b, 0x48, 0x3f, 0xe5, 0xff, 0xae, 0x41, 0x3b, 0xa5, 0x3b,
	0xf4, 0x29, 0xd4, 0x7d, 0xd7, 0xe2, 0xd6, 0xba, 0x78, 0xb0, 0x1c, 0x8f, 0x78, 0xa8, 0x39, 0x86,
	0xe2, 0x5a, 0x58, 0x61, 0x68, 0xea, 0x8d, 0x6c, 0x53, 0x4f, 0x82, 0xa9, 0x59, 0xdb, 0xd4, 0x07,
	0x89, 0x95, 0xd4, 0x04, 0x93, 0x7c, 0x4e, 0x0f, 0x32, 0xc3, 0x74, 0x89, 0x54, 0x67, 0xfa, 0x97,
	0x0a, 0x56, 0xad, 0x47, 0x09, 0x94, 0x90, 0x0e, 0xfd, 0x1c, 0x16, 0x6f, 0x5c, 0xcb, 0x50, 0xe9,
	0x08, 0x0c, 0xc4, 0xb4, 0x3a, 0xad, 0xe7, 0x02, 0xa5, 0x1f, 0x9a, 0x3a, 0x6b, 0xa1, 0x1f, 0xc3,
	0x92, 0xe3, 0xfa, 0xb6, 0x66, 0x31, 0x0e, 0x04, 0x6b, 0x41, 0xa8, 0xea, 0x36, 0x07, 0x0f, 0x4d,
	0x7d, 0x84, 0xb5, 0x00, 0xc9, 0xd0, 0x8e, 0xc7, 0x61, 0x54, 0xf3, 0x8c, 0xaa, 0x15, 0x32, 0x63,
	0x34, 0x3f, 0x83, 0xa5, 0x98, 0xc6, 0x72, 0x83, 0xc0, 0xe4, 0x27, 0x5f, 0xc9, 0x62, 0xb4, 0xc3,
	0xae, 0xa7, 0x8c, 0x12, 0x7d, 0x05, 0x6d, 0xd7, 0x11, 0xbb, 0x36, 0xcb, 0xbb, 0xb6, 0x5c, 0x27,
	0xe9, 0x28, 0x43, 0x1b, 0xdb, 0x5e, 0x70, 0xcf, 0xc4, 0x52, 0xaf, 0xc6, 0xe1, 0x79, 0xd9, 0x62,
	0x40, 0x2a, 0xd7, 0xe1, 0x98, 0x1e, 0x75, 0x4c, 0xb2, 0x88, 0xa4, 0xc5, 0x2d, 0x8f, 0xc2, 0x38,
	0x85, 0xfc, 0xb7, 0x15, 0x58, 0x2f, 0xb6, 0x71, 0xf4, 0x1c, 0x9a, 0x24, 0x3a, 0x57, 0xc3, 0xb5,
	0x2e, 0x3a, 0x71, 0x13, 0x22, 0xf4, 0x15, 0xb4, 0x2c, 0x73, 0x7c, 0x13, 0xa8, 0xae, 0x43, 0x47,
	0xe3, 0xc7, 0xc8, 0x46, 0xe1, 0x5e, 0x3a, 0x1c, 0x2b, 0x4d, 0x46, 0x7b, 0xee, 0x1c, 0x8e, 0xe5,
	0x2f, 0x13, 0x47, 0xc6, 0x91, 0x0f, 0xb2, 0xcc, 0x2b, 0x58, 0x1f, 0x05, 0x9a, 0x1f, 0x47, 0x70,
	0xa6, 0x33, 0x36, 0x79, 0x34, 0x9b, 0x8e, 0xe5, 0x2b, 0xd9, 0x58, 0x3e, 0x0c, 0xfe, 0xab, 0x49,
	0xf0, 0xbf, 0x01, 0xf3, 0x34, 0x9a, 0x8b, 0xa2, 0x7b, 0x7a, 0xe4, 0xbb, 0xce, 0x78, 0x60, 0xc8,
	0x9b, 0xb0, 0x51, 0x38, 0x06, 0xf1, 0xe4, 0x3f, 0x85, 0xa5, 0x93, 0xf0, 0x68, 0x38, 0x74, 0xdd,
	0xdb, 0xdf, 0x77, 0xdc, 0xc2, 0xac, 0x42, 0xee, 0x43, 0x27, 0xcd, 0x9c, 0x78, 0xe8, 0x05, 0x00,
	0x8f, 0xd2, 0xae, 0x5c, 0xf7, 0x96, 0x71, 0x6f, 0x09, 0x2b, 0x92, 0xd0, 0x36, 0xed, 0xe8, 0x53,
	0xbe, 0x85, 0xed, 0x23, 0xd7, 0xf6, 0x2c, 0x1c, 0xe0, 0x63, 0xf7, 0xad, 0x43, 0xa3, 0x4f, 0xd3,
	0x19, 0xff, 0x3f, 0xc9, 0x2c, 0xc3, 0xce, 0xf4, 0xc1, 0x88, 0x27, 0x6b, 0xb0, 0x36, 0x0a, 0x5c,
	0xef, 0xc3, 0x2c, 0x59, 0xb1, 0x18, 0x12, 0x35, 0x8b, 0xfc, 0x10, 0xc4, 0x93, 0x0f, 0x60, 0x39,
	0x09, 0xf8, 0x07, 0xce, 0xb5, 0xfb, 0xfe, 0x81, 0xe5, 0xbf, 0xab, 0x00, 0xca, 0x76, 0x22, 0x1e,
	0xda, 0x85, 0xba, 0xe9, 0x5c, 0xbb, 0xe1, 0x2a, 0x24, 0xc7, 0x7f, 0x38, 0x1c, 0xa3, 0x63, 0x14,
	0x51, 0xcc, 0x5d, 0x4d, 0x62, 0xee, 0xaf, 0xa1, 0x9d, 0x4e, 0x16, 0x6b, 0xe5, 0x5b, 0x7e, 0xe1,
	0x4a, 0xcc, 0x1e, 0xff, 0x18, 0xd6, 0x0f, 0xb5, 0x40, 0xbf, 0xc9, 0xcf, 0x62, 0x1b, 0x5a, 0xc9,
	0x2c, 0x78, 0xde, 0xd2, 0x56, 0x20, 0x9e, 0x06, 0x91, 0xff, 0xb7, 0x02, 0x1b, 0x85, 0x7d, 0x79,
	0xba, 0x43, 0x45, 0xcd, 0xa7, 0x3b, 0xe2, 0x6c, 0x38, 0x09, 0xfa, 0x1e, 0x16, 0xb9, 0x57, 0x57,
	0x03, 0x57, 0x65, 0xc7, 0x00, 0x3f, 0x68, 0x5f, 0x08, 0xc7, 0x40, 0xe1, 0x28, 0xfb, 0x43, 0xea,
	0xfe, 0x2f, 0x5c, 0x7a, 0x42, 0xf0, 0xcc, 0xa1, 0x65, 0x27, 0x90, 0xee, 0x10, 0x3a, 0x59, 0x02,
	0x31, 0x44, 0x6d, 0xf3, 0x10, 0xf5, 0x93, 0x74, 0x88, 0xda, 0x4e, 0xc2, 0x2e, 0x7a, 0xee, 0x08,
	0xc1, 0xe9, 0xaf, 0xa0, 0x4e, 0x41, 0x0f, 0x3d, 0xab, 0x9e, 0x40, 0x93, 0xfe, 0xaa, 0x42, 0xfc,
	0xd2, 0xa0, 0x80, 0x33, 0x1a, 0xc3, 0xfc, 0x53, 0x15, 0x5a, 0x82, 0x26, 0xde, 0x67, 0xaa, 0x82,
	0x61, 0x56, 0x53, 0x95, 0x82, 0x7d, 0xa8, 0x53, 0xaf, 0x12, 0x2e, 0x77, 0x37, 0xe7, 0x17, 0x4d,
	0xba, 0x77, 0x58, 0x6a, 0xc9, 0xe8, 0xd0, 0x17, 0x30, 0x6f, 0x63, 0xfb, 0x0a, 0xfb, 0xd1, 0xb1,
	0xb8, 0x92, 0x12, 0x7f, 0xc8, 0x70, 0x4a, 0x44, 0x83, 0x9e, 0x45, 0xd9, 0xd1, 0x2c, 0x9b, 0xeb,
	0x66, 0x96, 0xff, 0x89, 0x66, 0x63, 0xe6, 0x5e, 0xa3, 0x24, 0x69, 0x17, 0x3a, 0xec, 0x43, 0x9d,
	0xb0, 0x8a, 0x84, 0xa1, 0x86, 0x87, 0x60, 0x5b, 0x59, 0x64, 0x70, 0x5e, 0xa8, 0x30, 0x7a, 0x81,
	0x18, 0x71, 0xd1, 0xf3, 0xaf, 0x9e, 0x44, 0x6a, 0xff, 0x51, 0x87, 0xe5, 0x9c, 0xfc, 0xa2, 0x3b,
	0xad, 0x88, 0xee, 0x94, 0xea, 0x99, 0x21, 0x44, 0x3d, 0x53, 0x00, 0xd5, 0x33, 0xda, 0x87, 0x15,
	0x2e, 0x0f, 0xab, 0xa7, 0xc4, 0xf9, 0x1b, 0xdf, 0xdd, 0xcb, 0x0c, 0x75, 0xee, 0x61, 0xe7, 0x38,
	0x4a, 0xe4, 0x64, 0x68, 0x5f, 0x9b, 0x3e, 0x09, 0x58, 0xd5, 0x82, 0x66, 0x72, 0x75, 0x46, 0xd9,
	0x62, 0xc0, 0x37, 0xae, 0x6f, 0x0c, 0x69, 0xac, 0xb2, 0x18, 0xb8, 0x81, 0x66, 0x25, 0xec, 0x78,
	0x82, 0xd0, 0x66, 0xd0, 0x98, 0x55, 0xea, 0xac, 0x9b, 0x7b, 0xc8, 0x59, 0xd7, 0x85, 0xc6, 0xd1,
	0x8d, 0x4b, 0xb0, 0x73, 0x78, 0xcf, 0x74, 0xd2, 0x56, 0xe2, 0x76, 0x49, 0x49, 0xa8, 0x51, 0x5c,
	0x12, 0x42, 0xcf, 0x01, 0xae, 0x34, 0xc7, 0x60, 0x9b, 0x89, 0x48, 0xcd, 0x9d, 0x5a, 0xb1, 0xa1,
	0x36, 0xaf, 0xc2, 0x2f, 0x82, 0x9e, 0x42, 0xd3, 0xc0, 0x77, 0x26, 0x9f, 0x0f, 0x70, 0xfb, 0x8b,
	0x01, 0x34, 0x77, 0xf2, 0xb1, 0xa6, 0xb3, 0x10, 0x32, 0x30, 0x6d, 0x9e, 0x2d, 0xb7, 0x95, 0x85,
	0x08, 0x78, 0x61, 0xda, 0x18, 0x3d, 0x83, 0x15, 0x82, 0x75, 0xd7, 0x31, 0x34, 0xff, 0x5e, 0x4d,
	0x98, 0xf1, 0x5c, 0x19, 0xc5, 0xa8, 0xe3, 0x98, 0xeb, 0x97, 0xb0, 0x6e, 0xd1, 0x95, 0x33, 0xb0,
	0x6e, 0x12, 0x16, 0x9d, 0x46, 0x09, 0x14, 0xcf, 0x9f, 0x57, 0x29, 0xf6, 0x38, 0x44, 0xc6, 0x69,
	0xd7, 0x17, 0xb0, 0xf2, 0x17, 0x13, 0x63, 0x8c, 0x55, 0xcd, 0xb2, 0x54, 0x3d, 0xf0, 0x2d, 0xf5,
	0x2f, 0xb1, 0xef, 0xb2, 0xb4, 0xba, 0xa1, 0x74, 0x18, 0x8a, 0x06, 0xf9, 0x81, 0x6f, 0xfd, 0x80,
	0x7d, 0x57, 0xfe, 0x5d, 0x05, 0x20, 0x31, 0xed, 0x82, 0x22, 0x5d, 0xb4, 0x9d, 0xab, 0xd3, 0xb7,
	0x73, 0x17, 0x1a, 0x8e, 0xa9, 0xdf, 0x0a, 0x71, 0x66, 0xdc, 0xa6, 0xb6, 0x4c, 0xb3, 0x48, 0x9a,
	0x50, 0xf1, 0x48, 0x3d, 0x6a, 0xd2, 0xe1, 0x08, 0x7e, 0x17, 0x1a, 0x08, 0x2b, 0x48, 0xfc, 0x18,
	0x96, 0x74, 0xcd, 0x51, 0xbd, 0x09, 0xb9, 0x51, 0x49, 0xe0, 0x63, 0xcd, 0x66, 0xc6, 0xd1, 0x50,
	0xda, 0xba, 0xe6, 0xbc, 0x9e, 0x90, 0x9b, 0x11, 0x03, 0xca, 0xff, 0x5c, 0x81, 0x66, 0x7c, 0xce,
	0x3d, 0xd4, 0xe7, 0x0c, 0x61, 0x8d, 0x26, 0xa2, 0xb6, 0xe6, 0xa9, 0x62, 0x5a, 0x49, 0x42, 0x87,
	0x9a, 0xf8, 0x87, 0x57, 0xf8, 0x7e, 0xa8, 0x79, 0x62, 0x62, 0x89, 0x6e, 0xb3, 0x20, 0xf2, 0x07,
	0x9c, 0x2a, 0xff, 0x5e, 0x81, 0xe5, 0xdc, 0x18, 0xe8, 0x25, 0x2c, 0x44, 0xe2, 0x09, 0x11, 0xe0,
	0x4a, 0x46, 0xaa, 0x0b, 0xba, 0x2d, 0xe0, 0x36, 0xfe, 0x46, 0x4f, 0x01, 0x1c, 0x37, 0xc0, 0x34,
	0x04, 0xd4, 0xa2, 0x94, 0xb5, 0x41, 0x21, 0xe7, 0x4e, 0x2f, 0x40, 0x5b, 0xd0, 0xe2, 0xd8, 0xeb,
	0x6b, 0x8a, 0xe6, 0x5b, 0xbb, 0xc9, 0xd0, 0xd7, 0xd7, 0xbd, 0x80, 0x06, 0xac, 0xd7, 0xbe, 0x6b,
	0xab, 0xe1, 0xc8, 0xe1, 0x8e, 0x06, 0x0a, 0xe3, 0xe3, 0x51, 0xfe, 0x81, 0x1b, 0xe3, 0xf9, 0x5a,
	0x35, 0x02, 0x97, 0x63, 0xe5, 0x7f, 0xab, 0xc0, 0x52, 0x26, 0xf0, 0x7f, 0x50, 0x4e, 0x8c, 0xa0,
	0x1e, 0xb8, 0x4e, 0x9c, 0x94, 0xd0, 0x6f, 0x56, 0x34, 0xbc, 0x71, 0x7d, 0x23, 0x34, 0x13, 0xde,
	0xa0, 0xae, 0x2d, 0x3d, 0xf8, 0x1c, 0x9f, 0x7c, 0x14, 0xb1, 0xce, 0xe5, 0x22, 0xd6, 0xf9, 0x38,
	0x62, 0x65, 0xf6, 0xc4, 0xd5, 0xa7, 0x46, 0xfe, 0x91, 0xd7, 0xc8, 0xda, 0x7a, 0x92, 0x5e, 0x0f,
	0x0c, 0xf9, 0x14, 0x5a, 0xe2, 0x52, 0x2c, 0x42, 0xd5, 0x26, 0xe1, 0x36, 0xa8, 0xda, 0x24, 0x91,
	0xac, 0x5a, 0x22, 0x59, 0x4d, 0x94, 0x4c, 0xfe, 0x06, 0x16, 0x4e, 0x5d, 0x12, 0x7c, 0x87, 0x35,
	0x9f, 0x55, 0x5c, 0xf7, 0xa0, 0x2e, 0x94, 0x2e, 0x92, 0x0c, 0x39, 0x26, 0x1a, 0x04, 0xd8, 0x56,
	0x18, 0x8d, 0xfc, 0x4b, 0x68, 0xa7, 0xc0, 0x8f, 0x8e, 0xd3, 0xe4, 0x25, 0x81, 0x03, 0x8b, 0xc2,
	0x7e, 0x60, 0xc1, 0xfe, 0xb5, 0xe9, 0xdb, 0x43, 0xcd, 0x74, 0x3e, 0x6c, 0xec, 0xf7, 0x33, 0x56,
	0x60, 0x49, 0x78, 0x3f, 0x26, 0x52, 0x93, 0xff, 0xb5, 0x02, 0x8b, 0xbc, 0xd2, 0x4d, 0x97, 0x81,
	0x4c, 0x2f, 0x8b, 0xa7, 0x0a, 0x9d, 0xd5, 0xe2, 0x42, 0x67, 0xed, 0x61, 0x85, 0xce, 0x7a, 0xae,
	0xd0, 0x89, 0xa0, 0xee, 0x45, 0x27, 0x79, 0x5b, 0x61, 0xdf, 0x74, 0xd1, 0x2d, 0xd3, 0x36, 0xa3,
	0x23, 0x9a, 0x37, 0x42, 0xe3, 0x9e, 0x8f, 0x8c, 0x5b, 0x1e, 0xc1, 0x52, 0x6a, 0x0a, 0x8f, 0x2c,
	0x66, 0x17, 0xde, 0x86, 0xc9, 0x3d, 0x58, 0x61, 0x41, 0xdd, 0xa5, 0x47, 0x42, 0x7a, 0xc2, 0xed,
	0xe8, 0xe1, 0xf5, 0xff, 0x75, 0x58, 0xcd, 0xb3, 0x20, 0x1e, 0xf5, 0xf8, 0x9b, 0x5c, 0xe0, 0xcc,
	0xb6, 0x65, 0x23, 0x94, 0xd4, 0x14, 0xd9, 0x56, 0xad, 0x16, 0x6d, 0xd5, 0x5a, 0xc9, 0x86, 0xa8,
	0xa7, 0xb6, 0xea, 0xc3, 0xd5, 0x2b, 0x04, 0x32, 0xf3, 0xa9, 0xbc, 0xf0, 0xaf, 0x2b, 0xd0, 0x2d,
	0x93, 0x9b, 0x78, 0xe8, 0x14, 0xd6, 0x92, 0xe5, 0xe4, 0x05, 0x0a, 0xb1, 0x5c, 0x58, 0x5e, 0xa5,
	0x58, 0x31, 0xd3, 0x00, 0x56, 0xa3, 0x2a, 0x5e, 0x15, 0x07, 0xb6, 0x04, 0x95, 0x16, 0xa9, 0xef,
	0x83, 0x4a, 0x21, 0x7f, 0x0c, 0xdb, 0x53, 0xc7, 0x23, 0x9e, 0xbc, 0x0d, 0xed, 0x63, 0x4c, 0xf3,
	0xbf, 0xe8, 0x5a, 0x29, 0xe3, 0x7b, 0xe5, 0x0e, 0x2c, 0x8a, 0x04, 0xc4, 0x93, 0xf7, 0x40, 0xe2,
	0x90, 0xac, 0x0c, 0x05, 0xbd, 0x9f, 0xc0, 0x66, 0x09, 0x2d, 0xf1, 0xe8, 0x29, 0xb6, 0xa9, 0x60,
	0xcf, 0x4d, 0x72, 0xf5, 0x89, 0xae, 0xb3, 0x3a, 0xe9, 0x87, 0xbd, 0xef, 0xfb, 0x39, 0xc0, 0xd8,
	0xd7, 0x0c, 0xcc, 0x35, 0x59, 0x67, 0xe1, 0x5a, 0x52, 0x2f, 0x4c, 0x49, 0x70, 0x42, 0xe9, 0xd8,
	0x09, 0xd9, 0x64, 0x5d, 0xd8, 0x62, 0x7e, 0x0c, 0x0b, 0x26, 0x51, 0x7d, 0x1a, 0x61, 0x39, 0x58,
	0x0f, 0x98, 0x11, 0x36, 0x94, 0x96, 0x49, 0x94, 0x08, 0x24, 0x7f, 0x0d, 0xdd, 0xb2, 0x99, 0x10,
	0x76, 0xc3, 0x12, 0xc7, 0x5e, 0x61, 0xc1, 0x38, 0x6a, 0x53, 0xdf, 0xfa, 0x2b, 0xd7, 0x74, 0xe2,
	0xa3, 0xfe, 0x03, 0x4e, 0x9c, 0xfa, 0xd6, 0x14, 0xef, 0x47, 0xf9, 0xd6, 0x0d, 0x58, 0xcb, 0xee,
	0xff, 0xef, 0x0f, 0x14, 0xfc, 0x5b, 0x9a, 0xad, 0x17, 0x21, 0x88, 0x47, 0x33, 0xd6, 0x85, 0x28,
	0x87, 0xb4, 0xe9, 0x16, 0xcd, 0x1e, 0xe4, 0x1d, 0xa8, 0x99, 0xf6, 0x38, 0xca, 0xac, 0x4d, 0x7b,
	0x4c, 0xa3, 0xfd, 0x89, 0x43, 0xb0, 0x85, 0x75, 0x9a, 0xce, 0x50, 0x24, 0x77, 0x08, 0xed, 0x04,
	0x3a, 0xb0, 0xc7, 0x34, 0x7e, 0x0e, 0x6e, 0x26, 0xf6, 0x95, 0xa3, 0x99, 0x51, 0xb9, 0x36, 0x01,
	0x14, 0x84, 0x81, 0xcf, 0x61, 0xce, 0xa4, 0x12, 0x10, 0x69, 0x2e, 0xb3, 0x71, 0x44, 0xf9, 0x68,
	0x60, 0x15, 0xd2, 0x51, 0x1d, 0x9b, 0x44, 0x0d, 0xab, 0xb6, 0xcc, 0x75, 0x34, 0x94, 0xa6, 0x49,
	0x8e, 0x39, 0x20, 0xf6, 0x6b, 0x0d, 0xa1, 0x52, 0xfe, 0xf7, 0x95, 0xf8, 0x72, 0x20, 0x62, 0xf7,
	0xd0, 0x48, 0x72, 0x17, 0x66, 0x7d, 0x4c, 0xd4, 0x17, 0xb9, 0x8b, 0x1b, 0x21, 0xe4, 0xab, 0xfb,
	0x98, 0xbc, 0x88, 0x28, 0x0f, 0xa6, 0x05, 0x87, 0x94, 0xf2, 0x40, 0xfe, 0x8c, 0xdd, 0xd7, 0x26,
	0x15, 0xf8, 0x50, 0xa6, 0x02, 0x83, 0x92, 0x5d, 0x76, 0x8f, 0x9a, 0x23, 0x25, 0x1e, 0xfa, 0x22,
	0x56, 0x5c, 0xf6, 0xce, 0x3e, 0x45, 0x1a, 0x69, 0x8d, 0x86, 0x47, 0x2c, 0x87, 0x52, 0x19, 0x20,
	0x32, 0x4f, 0x1a, 0x1e, 0x31, 0x30, 0xa3, 0x1e, 0x18, 0xb2, 0x01, 0xeb, 0xa3, 0xb8, 0xbe, 0x70,
	0x49, 0xb0, 0x5f, 0x2e, 0xdc, 0xfb, 0xac, 0x7d, 0x13, 0x1a, 0xf1, 0x58, 0xdc, 0x56, 0xe6, 0xcd,
	0x70, 0x94, 0x4d, 0xd8, 0x28, 0x1c, 0x85, 0x78, 0xf2, 0xaf, 0x61, 0x2b, 0x35, 0xe3, 0x73, 0x67,
	0x68, 0xea, 0x29, 0x41, 0x1e, 0x1d, 0x26, 0xfd, 0x67, 0x05, 0xb6, 0xa7, 0xf2, 0x24, 0x1e, 0xfa,
	0x1e, 0x60, 0x42, 0xb0, 0xcf, 0xb5, 0x13, 0xaa, 0xf4, 0xab, 0x58, 0xa5, 0xef, 0xe9, 0xbd, 0x1f,
	0xb7, 0x78, 0xf5, 0xa5, 0x39, 0x89, 0xda, 0xdd, 0x11, 0x2c, 0xa6, 0x91, 0x05, 0x95, 0x97, 0xcf,
	0xd3, 0x95, 0x97, 0x92, 0x95, 0x14, 0x2a, 0x30, 0x2f, 0x61, 0x5d, 0xc1, 0xae, 0x6f, 0x60, 0x3f,
	0x6b, 0x41, 0x4f, 0xa0, 0x19, 0xe9, 0x9c, 0x1b, 0x46, 0x53, 0x69, 0x84, 0x4a, 0x27, 0x54, 0xeb,
	0x85, 0xdd, 0x88, 0x27, 0xf7, 0x00, 0x8d, 0x70, 0x10, 0xee, 0xa1, 0x98, 0x9b, 0xb8, 0x82, 0x95,
	0xd4, 0x0a, 0x46, 0x3b, 0xb9, 0x1a, 0xef, 0x64, 0x79, 0x0d, 0x56, 0x72, 0x2c, 0x88, 0x47, 0x9d,
	0x90, 0x50, 0xb0, 0xa2, 0xe0, 0xf0, 0x75, 0x82, 0xfc, 0x1d, 0x6c, 0x14, 0x62, 0x1e, 0x6d, 0xdc,
	0xf2, 0x01, 0xac, 0xf1, 0xc3, 0x2b, 0xab, 0x8e, 0xf2, 0x09, 0x50, 0xb9, 0x8a, 0xfa, 0x70, 0x5d,
	0xf4, 0x0c, 0x23, 0xcb, 0xea, 0x73, 0x98, 0x8d, 0x6c, 0x63, 0xda, 0x22, 0x31, 0x1a, 0xaa, 0x8b,
	0x1c, 0x0b, 0xe2, 0xc9, 0xcf, 0xb3, 0x0f, 0x35, 0x0e, 0xef, 0x07, 0x06, 0x65, 0x5e, 0x56, 0xd4,
	0x91, 0x0f, 0xb3, 0xef, 0x28, 0x78, 0x0f, 0x7e, 0x3e, 0xb0, 0x8a, 0x57, 0xf6, 0x7c, 0x10, 0x03,
	0x44, 0x46, 0x21, 0x77, 0xd9, 0x3d, 0xa1, 0x82, 0x75, 0xec, 0x04, 0x97, 0x9e, 0xe5, 0x6a, 0x06,
	0x36, 0xc2, 0x20, 0x42, 0xee, 0xb3, 0xcb, 0xc0, 0x22, 0xdc, 0x63, 0x86, 0xd8, 0x3b, 0x87, 0x66,
	0x5c, 0xc9, 0x41, 0x1b, 0xb0, 0x12, 0x37, 0xd4, 0xcb, 0xb3, 0xe3, 0xfe, 0xb7, 0x83, 0xb3, 0xfe,
	0x71, 0x67, 0x06, 0x21, 0x58, 0x4c, 0x10, 0xb6, 0x69, 0x19, 0x9d, 0x0a, 0x5a, 0x83, 0xe5, 0x04,
	0xd6, 0x7f, 0xa7, 0x9b, 0x01, 0x36, 0x3a, 0xd5, 0x3d, 0x1f, 0x3a, 0xd9, 0xd2, 0x1a, 0xda, 0x82,
	0x6e, 0x16, 0x96, 0x62, 0xbf, 0x03, 0x4f, 0x73, 0xf8, 0xe3, 0xf3, 0x37, 0x67, 0xa7, 0xe7, 0xbd,
	0xe3, 0xc1, 0xd9, 0x49, 0xa7, 0x82, 0x9e, 0x82, 0x94, 0xa3, 0x08, 0xcf, 0xd3, 0x4e, 0x75, 0xef,
	0x06, 0x20, 0x49, 0xbc, 0x91, 0x04, 0xab, 0x49, 0x2b, 0x35, 0xce, 0x2a, 0x74, 0x04, 0xcc, 0xd1,
	0xe9, 0xe0, 0xe8, 0x55, 0xa7, 0x92, 0x81, 0xbe, 0x56, 0xfa, 0xa3, 0x51, 0xa7, 0x9a, 0x81, 0x8e,
	0x4e, 0x07, 0xc7, 0xfd, 0x4e, 0x6d, 0xef, 0x77, 0x15, 0x68, 0x44, 0xe7, 0x0c, 0x5a, 0x07, 0x14,
	0x7d, 0xa7, 0x86, 0x91, 0x68, 0x58, 0x1f, 0xc2, 0x87, 0xbd, 0xc1, 0x99, 0x3a, 0x1a, 0x9c, 0x9d,
	0xf4, 0x95, 0x4e, 0x25, 0x85, 0x39, 0xed, 0xf7, 0x8e, 0xd5, 0x93, 0xcb, 0xc1, 0x45, 0x4f, 0xe9,
	0x54, 0xa9, 0x36, 0x63, 0xcc, 0xab, 0xfe, 0x6f, 0x0e, 0xcf, 0x7b, 0xca, 0x71, 0xa7, 0x46, 0xa5,
	0x88, 0xc1, 0x87, 0xbd, 0xd1, 0x68, 0x30, 0xba, 0xe8, 0xd4, 0x53, 0xd0, 0x63, 0xe5, 0x72, 0x38,
	0xec, 0x2b, 0x9d, 0x59, 0xd4, 0xa5, 0x41, 0x43, 0x08, 0x55, 0xbe, 0xfb, 0xcd, 0xc5, 0x77, 0xc3,
	0x88, 0xfd, 0xdc, 0xde, 0x8f, 0xa0, 0x36, 0xc2, 0xef, 0xd0, 0x3c, 0xd4, 0x7a, 0x96, 0xd5, 0x99,
	0x41, 0x0d, 0xa8, 0x0f, 0x35, 0x0b, 0x77, 0x2a, 0x08, 0x60, 0xee, 0x5b, 0x6c, 0xd3, 0xef, 0xea,
	0xde, 0x37, 0xd4, 0x3b, 0x15, 0x85, 0x6a, 0x8c, 0x7e, 0x30, 0x1a, 0x75, 0x66, 0x50, 0x0b, 0xe6,
	0x5f, 0xf7, 0x95, 0x6f, 0xfb, 0x47, 0x17, 0x9d, 0x0a, 0x6a, 0xc2, 0xec, 0x89, 0xd2, 0xef, 0x5d,
	0x74, 0xaa, 0x07, 0x7f, 0xb3, 0x09, 0xf3, 0x61, 0x37, 0xf4, 0x46, 0xbc, 0x1d, 0x88, 0x9e, 0x65,
	0xa1, 0x2d, 0xd1, 0x29, 0xe7, 0x1f, 0x73, 0x75, 0xb7, 0xa7, 0xe2, 0x89, 0x27, 0xcf, 0xa0, 0x0b,
	0x58, 0xce, 0xbd, 0xba, 0x42, 0x1f, 0x09, 0xa9, 0x64, 0xfe, 0x11, 0x57, 0x77, 0x6b, 0x1a, 0x9a,
	0x71, 0xcd, 0x89, 0xcb, 0x42, 0xd2, 0x32, 0x71, 0x43, 0x27, 0x58, 0x2a, 0x6e, 0xe4, 0x0a, 0xe5,
	0x19, 0xf4, 0x03, 0xdb, 0x4f, 0xd9, 0x7b, 0x32, 0xb4, 0x2d, 0x16, 0x51, 0x0b, 0x6e, 0xea, 0xba,
	0x3b, 0xd3, 0x09, 0x18, 0xef, 0x3e, 0x2c, 0x88, 0x77, 0x61, 0x48, 0x12, 0xc5, 0x11, 0xef, 0xb2,
	0xba, 0x9b, 0x25, 0x18, 0xc6, 0x66, 0x42, 0xb7, 0x5e, 0xf9, 0xf5, 0x14, 0xda, 0x15, 0x7c, 0xc7,
	0xd4, 0x2b, 0xb3, 0xee, 0x67, 0x0f, 0xa4, 0x8c, 0x54, 0x9e, 0xbf, 0x8e, 0x12, 0x54, 0x5e, 0x78,
	0x1d, 0xd6, 0xdd, 0x9e, 0x8a, 0x67, 0x8c, 0x5f, 0xc1, 0x62, 0xfa, 0x96, 0x05, 0x75, 0x0b, 0xd6,
	0x29, 0xbc, 0x20, 0xea, 0x3e, 0x29, 0xc5, 0x45, 0xeb, 0x57, 0x70, 0x6f, 0x23, 0xac, 0x5f, 0xf1,
	0xbd, 0x93, 0xb0, 0x7e, 0x25, 0xd7, 0x3e, 0xf2, 0x0c, 0xfa, 0xf3, 0x4c, 0x28, 0x19, 0x3d, 0xe6,
	0x40, 0x1f, 0x17, 0xc7, 0x2e, 0xc2, 0x1b, 0x91, 0xae, 0xfc, 0x3e, 0x12, 0x36, 0xc2, 0x9f, 0x40,
	0x33, 0xae, 0x31, 0xa1, 0xb5, 0x7c, 0x41, 0x8b, 0x72, 0x5a, 0x2f, 0x02, 0xb3, 0xde, 0x87, 0xec,
	0x25, 0x5e, 0x54, 0x34, 0x42, 0xa9, 0x0b, 0x6b, 0xa1, 0x4c, 0xd5, 0x95, 0x8a, 0x11, 0x8c, 0x87,
	0x09, 0x52, 0xd9, 0xbb, 0x4e, 0xf4, 0xa3, 0xb8, 0xdf, 0x94, 0x17, 0xa9, 0xdd, 0x4f, 0x1f, 0x40,
	0xc5, 0x86, 0xd2, 0x33, 0xae, 0x2b, 0xce, 0x0e, 0x91, 0x5c, 0x9c, 0x86, 0x8a, 0x89, 0x70, 0xf7,
	0x93, 0xf7, 0xd2, 0x44, 0x3a, 0x11, 0x92, 0x3d, 0x41, 0x27, 0xe9, 0xf4, 0x52, 0xd0, 0x49, 0x26,
	0x37, 0xe4, 0x3c, 0x84, 0x5a, 0x94, 0xc0, 0x23, 0x5d, 0x64, 0x13, 0x78, 0x64, 0x4a, 0x57, 0xf2,
	0x0c, 0xfa, 0x35, 0xf5, 0xff, 0xe9, 0xf4, 0x10, 0x3d, 0x4d, 0xdb, 0x5c, 0xba, 0x2a, 0xd5, 0xfd,
	0x68, 0x0a, 0x36, 0xda, 0x90, 0xf9, 0x8c, 0x53, 0xd8, 0x90, 0x85, 0x79, 0x6a, 0x77, 0x7b, 0x2a,
	0x9e, 0x31, 0xfe, 0x05, 0x40, 0x52, 0xdc, 0x40, 0x89, 0xbd, 0xa5, 0x4a, 0x22, 0xdd, 0x8d, 0x42,
	0x78, 0xb4, 0xb2, 0xc5, 0x35, 0x25, 0x61, 0x65, 0x4b, 0x8b, 0x65, 0xc2, 0xca, 0x96, 0x17, 0xa6,
	0xe4, 0x19, 0xe4, 0xc3, 0x93, 0x29, 0x65, 0x1c, 0xf4, 0x93, 0xa2, 0x79, 0x16, 0x0d, 0xb7, 0xfb,
	0x30, 0xc2, 0xc8, 0x03, 0x14, 0x16, 0x6e, 0x04, 0x0f, 0x50, 0x56, 0x04, 0x12, 0x3c, 0x40, 0x79,
	0xed, 0x67, 0x06, 0x9d, 0xc1, 0x52, 0xe6, 0xcd, 0x38, 0x4a, 0x3c, 0x5e, 0xfe, 0x41, 0x7a, 0xf7,
	0x69, 0x39, 0x32, 0x32, 0x92, 0xfc, 0x6b, 0x78, 0xc1, 0x48, 0x0a, 0x1f, 0xdc, 0x0b, 0x46, 0x52,
	0xfc, 0x94, 0x5e, 0x9e, 0x41, 0x7f, 0x06, 0xab, 0x45, 0x4f, 0xfb, 0x51, 0xe2, 0x48, 0x4b, 0xfe,
	0x3a, 0xd0, 0xfd, 0xf8, 0x3d, 0x14, 0xd1, 0x7e, 0xc9, 0xfe, 0x6f, 0x42, 0xd8, 0x2f, 0x05, 0xff,
	0xb4, 0x10, 0xf6, 0x4b, 0xd1, 0x1f, 0x2e, 0xe2, 0x98, 0x21, 0x93, 0xde, 0xa7, 0x63, 0x86, 0x7c,
	0x99, 0x20, 0x1d, 0x33, 0x14, 0xd4, 0x06, 0xc2, 0x98, 0x21, 0x9f, 0x60, 0x8b, 0x31, 0x43, 0x61,
	0x92, 0x2f, 0xc6, 0x0c, 0x25, 0xf9, 0x39, 0xb3, 0xf2, 0x29, 0xf9, 0xb0, 0x60, 0xe5, 0xd3, 0xf3,
	0x78, 0xc1, 0xca, 0xdf, 0x93, 0x5e, 0xc7, 0x36, 0x98, 0xd2, 0x52, 0xca, 0x06, 0xb3, 0x2a, 0x7a,
	0x5a, 0x8e, 0x8c, 0x14, 0x9f, 0xcf, 0xfe, 0x04, 0xc5, 0x17, 0xa6, 0x93, 0x82, 0xe2, 0x4b, 0x52,
	0x47, 0xa6, 0xf8, 0x82, 0xa4, 0x16, 0x15, 0x85, 0x79, 0x62, 0x32, 0x2c, 0x28, 0xbe, 0x24, 0x27,
	0xe6, 0xbc, 0x0b, 0xf2, 0x77, 0x24, 0x56, 0x48, 0x8b, 0x8a, 0x02, 0x02, 0xef, 0xb2, 0xf4, 0x9f,
	0x29, 0x38, 0x93, 0xbd, 0x0b, 0x0a, 0xce, 0x97, 0x06, 0x04, 0x05, 0x17, 0x25, 0xfd, 0x05, 0xd1,
	0x30, 0x4d, 0x5c, 0x4b, 0xa3, 0xe1, 0x30, 0x0f, 0x2e, 0x8d, 0x86, 0xa3, 0xac, 0x37, 0x8e, 0x78,
	0xf2, 0x19, 0x6b, 0x3a, 0xe2, 0x29, 0xcc, 0x76, 0xd3, 0x11, 0x4f, 0x71, 0xd2, 0x2b, 0xcf, 0x1c,
	0x7e, 0xfe, 0xc3, 0x67, 0x63, 0xd7, 0xd2, 0x9c, 0xf1, 0xfe, 0xcb, 0x83, 0x20, 0xd8, 0xd7, 0x5d,
	0xfb, 0x19, 0xfb, 0x63, 0x95, 0xee, 0x5a, 0xcf, 0x08, 0xf6, 0xef, 0x4c, 0x1d, 0x93, 0xe8, 0x2f,
	0x57, 0x57, 0x73, 0x0c, 0xf5, 0x47, 0xff, 0x17, 0x00, 0x00, 0xff, 0xff, 0xcb, 0xee, 0x04, 0x73,
	0x8c, 0x35, 0x00, 0x00,
}
