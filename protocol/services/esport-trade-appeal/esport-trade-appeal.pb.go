// Code generated by protoc-gen-go. DO NOT EDIT.
// source: esport-trade-appeal/esport-trade-appeal.proto

package esport_trade_appeal // import "golang.52tt.com/protocol/services/esport-trade-appeal"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 退款类型
type RefundType int32

const (
	RefundType_REFUND_TYPE_FULL    RefundType = 0
	RefundType_REFUND_TYPE_PARTIAL RefundType = 1
)

var RefundType_name = map[int32]string{
	0: "REFUND_TYPE_FULL",
	1: "REFUND_TYPE_PARTIAL",
}
var RefundType_value = map[string]int32{
	"REFUND_TYPE_FULL":    0,
	"REFUND_TYPE_PARTIAL": 1,
}

func (x RefundType) String() string {
	return proto.EnumName(RefundType_name, int32(x))
}
func (RefundType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{0}
}

// 退款途径
type RefundWay int32

const (
	RefundWay_REFUND_WAY_NORMAL_UNSPECIFIED RefundWay = 0
	RefundWay_REFUND_WAY_FAST               RefundWay = 1
)

var RefundWay_name = map[int32]string{
	0: "REFUND_WAY_NORMAL_UNSPECIFIED",
	1: "REFUND_WAY_FAST",
}
var RefundWay_value = map[string]int32{
	"REFUND_WAY_NORMAL_UNSPECIFIED": 0,
	"REFUND_WAY_FAST":               1,
}

func (x RefundWay) String() string {
	return proto.EnumName(RefundWay_name, int32(x))
}
func (RefundWay) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{1}
}

// 订单售后状态
type RefundStatus int32

const (
	RefundStatus_REFUND_STATUS_UNSPECIFIED      RefundStatus = 0
	RefundStatus_REFUND_STATUS_REFUNDING        RefundStatus = 1
	RefundStatus_REFUND_STATUS_REFUND_ACCEPT    RefundStatus = 2
	RefundStatus_REFUND_STATUS_REFUND_REJECT    RefundStatus = 3
	RefundStatus_REFUND_STATUS_APPEALING        RefundStatus = 4
	RefundStatus_REFUND_STATUS_APPEALING_ACCEPT RefundStatus = 5
	RefundStatus_REFUND_STATUS_APPEALING_REJECT RefundStatus = 6
)

var RefundStatus_name = map[int32]string{
	0: "REFUND_STATUS_UNSPECIFIED",
	1: "REFUND_STATUS_REFUNDING",
	2: "REFUND_STATUS_REFUND_ACCEPT",
	3: "REFUND_STATUS_REFUND_REJECT",
	4: "REFUND_STATUS_APPEALING",
	5: "REFUND_STATUS_APPEALING_ACCEPT",
	6: "REFUND_STATUS_APPEALING_REJECT",
}
var RefundStatus_value = map[string]int32{
	"REFUND_STATUS_UNSPECIFIED":      0,
	"REFUND_STATUS_REFUNDING":        1,
	"REFUND_STATUS_REFUND_ACCEPT":    2,
	"REFUND_STATUS_REFUND_REJECT":    3,
	"REFUND_STATUS_APPEALING":        4,
	"REFUND_STATUS_APPEALING_ACCEPT": 5,
	"REFUND_STATUS_APPEALING_REJECT": 6,
}

func (x RefundStatus) String() string {
	return proto.EnumName(RefundStatus_name, int32(x))
}
func (RefundStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{2}
}

// 创建退款请求
type CreateRefundRequest struct {
	OrderId              string     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Quantity             uint32     `protobuf:"varint,2,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Reason               string     `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	Instructions         string     `protobuf:"bytes,4,opt,name=instructions,proto3" json:"instructions,omitempty"`
	Type                 RefundType `protobuf:"varint,5,opt,name=type,proto3,enum=esport_trade_appeal.RefundType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *CreateRefundRequest) Reset()         { *m = CreateRefundRequest{} }
func (m *CreateRefundRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRefundRequest) ProtoMessage()    {}
func (*CreateRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{0}
}
func (m *CreateRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRefundRequest.Unmarshal(m, b)
}
func (m *CreateRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRefundRequest.Marshal(b, m, deterministic)
}
func (dst *CreateRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRefundRequest.Merge(dst, src)
}
func (m *CreateRefundRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRefundRequest.Size(m)
}
func (m *CreateRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRefundRequest proto.InternalMessageInfo

func (m *CreateRefundRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *CreateRefundRequest) GetQuantity() uint32 {
	if m != nil {
		return m.Quantity
	}
	return 0
}

func (m *CreateRefundRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *CreateRefundRequest) GetInstructions() string {
	if m != nil {
		return m.Instructions
	}
	return ""
}

func (m *CreateRefundRequest) GetType() RefundType {
	if m != nil {
		return m.Type
	}
	return RefundType_REFUND_TYPE_FULL
}

// 创建退款请求响应
type CreateRefundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRefundResponse) Reset()         { *m = CreateRefundResponse{} }
func (m *CreateRefundResponse) String() string { return proto.CompactTextString(m) }
func (*CreateRefundResponse) ProtoMessage()    {}
func (*CreateRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{1}
}
func (m *CreateRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRefundResponse.Unmarshal(m, b)
}
func (m *CreateRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRefundResponse.Marshal(b, m, deterministic)
}
func (dst *CreateRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRefundResponse.Merge(dst, src)
}
func (m *CreateRefundResponse) XXX_Size() int {
	return xxx_messageInfo_CreateRefundResponse.Size(m)
}
func (m *CreateRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRefundResponse proto.InternalMessageInfo

// 接受退款请求
type AcceptRefundRequest struct {
	RefundRequestId      string   `protobuf:"bytes,1,opt,name=refund_request_id,json=refundRequestId,proto3" json:"refund_request_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptRefundRequest) Reset()         { *m = AcceptRefundRequest{} }
func (m *AcceptRefundRequest) String() string { return proto.CompactTextString(m) }
func (*AcceptRefundRequest) ProtoMessage()    {}
func (*AcceptRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{2}
}
func (m *AcceptRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptRefundRequest.Unmarshal(m, b)
}
func (m *AcceptRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptRefundRequest.Marshal(b, m, deterministic)
}
func (dst *AcceptRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptRefundRequest.Merge(dst, src)
}
func (m *AcceptRefundRequest) XXX_Size() int {
	return xxx_messageInfo_AcceptRefundRequest.Size(m)
}
func (m *AcceptRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptRefundRequest proto.InternalMessageInfo

func (m *AcceptRefundRequest) GetRefundRequestId() string {
	if m != nil {
		return m.RefundRequestId
	}
	return ""
}

// 接受退款请求响应
type AcceptRefundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptRefundResponse) Reset()         { *m = AcceptRefundResponse{} }
func (m *AcceptRefundResponse) String() string { return proto.CompactTextString(m) }
func (*AcceptRefundResponse) ProtoMessage()    {}
func (*AcceptRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{3}
}
func (m *AcceptRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptRefundResponse.Unmarshal(m, b)
}
func (m *AcceptRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptRefundResponse.Marshal(b, m, deterministic)
}
func (dst *AcceptRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptRefundResponse.Merge(dst, src)
}
func (m *AcceptRefundResponse) XXX_Size() int {
	return xxx_messageInfo_AcceptRefundResponse.Size(m)
}
func (m *AcceptRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptRefundResponse proto.InternalMessageInfo

// 拒绝退款请求
type RejectRefundRequest struct {
	RefundRequestId      string   `protobuf:"bytes,1,opt,name=refund_request_id,json=refundRequestId,proto3" json:"refund_request_id,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	Instructions         string   `protobuf:"bytes,3,opt,name=instructions,proto3" json:"instructions,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RejectRefundRequest) Reset()         { *m = RejectRefundRequest{} }
func (m *RejectRefundRequest) String() string { return proto.CompactTextString(m) }
func (*RejectRefundRequest) ProtoMessage()    {}
func (*RejectRefundRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{4}
}
func (m *RejectRefundRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectRefundRequest.Unmarshal(m, b)
}
func (m *RejectRefundRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectRefundRequest.Marshal(b, m, deterministic)
}
func (dst *RejectRefundRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectRefundRequest.Merge(dst, src)
}
func (m *RejectRefundRequest) XXX_Size() int {
	return xxx_messageInfo_RejectRefundRequest.Size(m)
}
func (m *RejectRefundRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectRefundRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RejectRefundRequest proto.InternalMessageInfo

func (m *RejectRefundRequest) GetRefundRequestId() string {
	if m != nil {
		return m.RefundRequestId
	}
	return ""
}

func (m *RejectRefundRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *RejectRefundRequest) GetInstructions() string {
	if m != nil {
		return m.Instructions
	}
	return ""
}

// 拒绝退款请求响应
type RejectRefundResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RejectRefundResponse) Reset()         { *m = RejectRefundResponse{} }
func (m *RejectRefundResponse) String() string { return proto.CompactTextString(m) }
func (*RejectRefundResponse) ProtoMessage()    {}
func (*RejectRefundResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{5}
}
func (m *RejectRefundResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RejectRefundResponse.Unmarshal(m, b)
}
func (m *RejectRefundResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RejectRefundResponse.Marshal(b, m, deterministic)
}
func (dst *RejectRefundResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RejectRefundResponse.Merge(dst, src)
}
func (m *RejectRefundResponse) XXX_Size() int {
	return xxx_messageInfo_RejectRefundResponse.Size(m)
}
func (m *RejectRefundResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RejectRefundResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RejectRefundResponse proto.InternalMessageInfo

// 发起申诉请求
type InitiateAppealRequest struct {
	RefundId             string   `protobuf:"bytes,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	ProofImages          []string `protobuf:"bytes,3,rep,name=proof_images,json=proofImages,proto3" json:"proof_images,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitiateAppealRequest) Reset()         { *m = InitiateAppealRequest{} }
func (m *InitiateAppealRequest) String() string { return proto.CompactTextString(m) }
func (*InitiateAppealRequest) ProtoMessage()    {}
func (*InitiateAppealRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{6}
}
func (m *InitiateAppealRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitiateAppealRequest.Unmarshal(m, b)
}
func (m *InitiateAppealRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitiateAppealRequest.Marshal(b, m, deterministic)
}
func (dst *InitiateAppealRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitiateAppealRequest.Merge(dst, src)
}
func (m *InitiateAppealRequest) XXX_Size() int {
	return xxx_messageInfo_InitiateAppealRequest.Size(m)
}
func (m *InitiateAppealRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InitiateAppealRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InitiateAppealRequest proto.InternalMessageInfo

func (m *InitiateAppealRequest) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *InitiateAppealRequest) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *InitiateAppealRequest) GetProofImages() []string {
	if m != nil {
		return m.ProofImages
	}
	return nil
}

// 发起申诉请求响应
type InitiateAppealResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitiateAppealResponse) Reset()         { *m = InitiateAppealResponse{} }
func (m *InitiateAppealResponse) String() string { return proto.CompactTextString(m) }
func (*InitiateAppealResponse) ProtoMessage()    {}
func (*InitiateAppealResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{7}
}
func (m *InitiateAppealResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitiateAppealResponse.Unmarshal(m, b)
}
func (m *InitiateAppealResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitiateAppealResponse.Marshal(b, m, deterministic)
}
func (dst *InitiateAppealResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitiateAppealResponse.Merge(dst, src)
}
func (m *InitiateAppealResponse) XXX_Size() int {
	return xxx_messageInfo_InitiateAppealResponse.Size(m)
}
func (m *InitiateAppealResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InitiateAppealResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InitiateAppealResponse proto.InternalMessageInfo

// 电竞指导者提交申诉信息
type SubmitGuideAppealInfoRequest struct {
	AppealId               string   `protobuf:"bytes,1,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id,omitempty"`
	CoachAppealDescription string   `protobuf:"bytes,2,opt,name=coach_appeal_description,json=coachAppealDescription,proto3" json:"coach_appeal_description,omitempty"`
	CoachAppealImages      []string `protobuf:"bytes,3,rep,name=coach_appeal_images,json=coachAppealImages,proto3" json:"coach_appeal_images,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *SubmitGuideAppealInfoRequest) Reset()         { *m = SubmitGuideAppealInfoRequest{} }
func (m *SubmitGuideAppealInfoRequest) String() string { return proto.CompactTextString(m) }
func (*SubmitGuideAppealInfoRequest) ProtoMessage()    {}
func (*SubmitGuideAppealInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{8}
}
func (m *SubmitGuideAppealInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGuideAppealInfoRequest.Unmarshal(m, b)
}
func (m *SubmitGuideAppealInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGuideAppealInfoRequest.Marshal(b, m, deterministic)
}
func (dst *SubmitGuideAppealInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGuideAppealInfoRequest.Merge(dst, src)
}
func (m *SubmitGuideAppealInfoRequest) XXX_Size() int {
	return xxx_messageInfo_SubmitGuideAppealInfoRequest.Size(m)
}
func (m *SubmitGuideAppealInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGuideAppealInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGuideAppealInfoRequest proto.InternalMessageInfo

func (m *SubmitGuideAppealInfoRequest) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

func (m *SubmitGuideAppealInfoRequest) GetCoachAppealDescription() string {
	if m != nil {
		return m.CoachAppealDescription
	}
	return ""
}

func (m *SubmitGuideAppealInfoRequest) GetCoachAppealImages() []string {
	if m != nil {
		return m.CoachAppealImages
	}
	return nil
}

// 电竞指导者提交申诉信息响应
type SubmitGuideAppealInfoResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitGuideAppealInfoResponse) Reset()         { *m = SubmitGuideAppealInfoResponse{} }
func (m *SubmitGuideAppealInfoResponse) String() string { return proto.CompactTextString(m) }
func (*SubmitGuideAppealInfoResponse) ProtoMessage()    {}
func (*SubmitGuideAppealInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{9}
}
func (m *SubmitGuideAppealInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitGuideAppealInfoResponse.Unmarshal(m, b)
}
func (m *SubmitGuideAppealInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitGuideAppealInfoResponse.Marshal(b, m, deterministic)
}
func (dst *SubmitGuideAppealInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitGuideAppealInfoResponse.Merge(dst, src)
}
func (m *SubmitGuideAppealInfoResponse) XXX_Size() int {
	return xxx_messageInfo_SubmitGuideAppealInfoResponse.Size(m)
}
func (m *SubmitGuideAppealInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitGuideAppealInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitGuideAppealInfoResponse proto.InternalMessageInfo

// 退款信息
type OrderRefundView struct {
	RefundId                  string     `protobuf:"bytes,1,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	Status                    uint32     `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	RefundType                RefundType `protobuf:"varint,3,opt,name=refund_type,json=refundType,proto3,enum=esport_trade_appeal.RefundType" json:"refund_type,omitempty"`
	RefundReason              string     `protobuf:"bytes,4,opt,name=refund_reason,json=refundReason,proto3" json:"refund_reason,omitempty"`
	RefundPrice               uint32     `protobuf:"varint,5,opt,name=refund_price,json=refundPrice,proto3" json:"refund_price,omitempty"`
	RefundCount               uint32     `protobuf:"varint,6,opt,name=refund_count,json=refundCount,proto3" json:"refund_count,omitempty"`
	RefundDesc                string     `protobuf:"bytes,7,opt,name=refund_desc,json=refundDesc,proto3" json:"refund_desc,omitempty"`
	EndTime                   int64      `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	RefundRejectReason        string     `protobuf:"bytes,10,opt,name=refund_reject_reason,json=refundRejectReason,proto3" json:"refund_reject_reason,omitempty"`
	RefundRejectDesc          string     `protobuf:"bytes,11,opt,name=refund_reject_desc,json=refundRejectDesc,proto3" json:"refund_reject_desc,omitempty"`
	AppealDesc                string     `protobuf:"bytes,9,opt,name=appeal_desc,json=appealDesc,proto3" json:"appeal_desc,omitempty"`
	AppealReason              string     `protobuf:"bytes,12,opt,name=appeal_reason,json=appealReason,proto3" json:"appeal_reason,omitempty"`
	CanCoachUploadAppeal      bool       `protobuf:"varint,13,opt,name=can_coach_upload_appeal,json=canCoachUploadAppeal,proto3" json:"can_coach_upload_appeal,omitempty"`
	AppealId                  string     `protobuf:"bytes,14,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id,omitempty"`
	CoachUploadAppealDeadline int64      `protobuf:"varint,15,opt,name=coach_upload_appeal_deadline,json=coachUploadAppealDeadline,proto3" json:"coach_upload_appeal_deadline,omitempty"`
	CanAppeal                 bool       `protobuf:"varint,16,opt,name=can_appeal,json=canAppeal,proto3" json:"can_appeal,omitempty"`
	RefundWay                 RefundWay  `protobuf:"varint,17,opt,name=refund_way,json=refundWay,proto3,enum=esport_trade_appeal.RefundWay" json:"refund_way,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}   `json:"-"`
	XXX_unrecognized          []byte     `json:"-"`
	XXX_sizecache             int32      `json:"-"`
}

func (m *OrderRefundView) Reset()         { *m = OrderRefundView{} }
func (m *OrderRefundView) String() string { return proto.CompactTextString(m) }
func (*OrderRefundView) ProtoMessage()    {}
func (*OrderRefundView) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{10}
}
func (m *OrderRefundView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderRefundView.Unmarshal(m, b)
}
func (m *OrderRefundView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderRefundView.Marshal(b, m, deterministic)
}
func (dst *OrderRefundView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderRefundView.Merge(dst, src)
}
func (m *OrderRefundView) XXX_Size() int {
	return xxx_messageInfo_OrderRefundView.Size(m)
}
func (m *OrderRefundView) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderRefundView.DiscardUnknown(m)
}

var xxx_messageInfo_OrderRefundView proto.InternalMessageInfo

func (m *OrderRefundView) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *OrderRefundView) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OrderRefundView) GetRefundType() RefundType {
	if m != nil {
		return m.RefundType
	}
	return RefundType_REFUND_TYPE_FULL
}

func (m *OrderRefundView) GetRefundReason() string {
	if m != nil {
		return m.RefundReason
	}
	return ""
}

func (m *OrderRefundView) GetRefundPrice() uint32 {
	if m != nil {
		return m.RefundPrice
	}
	return 0
}

func (m *OrderRefundView) GetRefundCount() uint32 {
	if m != nil {
		return m.RefundCount
	}
	return 0
}

func (m *OrderRefundView) GetRefundDesc() string {
	if m != nil {
		return m.RefundDesc
	}
	return ""
}

func (m *OrderRefundView) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *OrderRefundView) GetRefundRejectReason() string {
	if m != nil {
		return m.RefundRejectReason
	}
	return ""
}

func (m *OrderRefundView) GetRefundRejectDesc() string {
	if m != nil {
		return m.RefundRejectDesc
	}
	return ""
}

func (m *OrderRefundView) GetAppealDesc() string {
	if m != nil {
		return m.AppealDesc
	}
	return ""
}

func (m *OrderRefundView) GetAppealReason() string {
	if m != nil {
		return m.AppealReason
	}
	return ""
}

func (m *OrderRefundView) GetCanCoachUploadAppeal() bool {
	if m != nil {
		return m.CanCoachUploadAppeal
	}
	return false
}

func (m *OrderRefundView) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

func (m *OrderRefundView) GetCoachUploadAppealDeadline() int64 {
	if m != nil {
		return m.CoachUploadAppealDeadline
	}
	return 0
}

func (m *OrderRefundView) GetCanAppeal() bool {
	if m != nil {
		return m.CanAppeal
	}
	return false
}

func (m *OrderRefundView) GetRefundWay() RefundWay {
	if m != nil {
		return m.RefundWay
	}
	return RefundWay_REFUND_WAY_NORMAL_UNSPECIFIED
}

type GetOrderRefundViewRequest struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderRefundViewRequest) Reset()         { *m = GetOrderRefundViewRequest{} }
func (m *GetOrderRefundViewRequest) String() string { return proto.CompactTextString(m) }
func (*GetOrderRefundViewRequest) ProtoMessage()    {}
func (*GetOrderRefundViewRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{11}
}
func (m *GetOrderRefundViewRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderRefundViewRequest.Unmarshal(m, b)
}
func (m *GetOrderRefundViewRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderRefundViewRequest.Marshal(b, m, deterministic)
}
func (dst *GetOrderRefundViewRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderRefundViewRequest.Merge(dst, src)
}
func (m *GetOrderRefundViewRequest) XXX_Size() int {
	return xxx_messageInfo_GetOrderRefundViewRequest.Size(m)
}
func (m *GetOrderRefundViewRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderRefundViewRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderRefundViewRequest proto.InternalMessageInfo

func (m *GetOrderRefundViewRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderRefundViewResponse struct {
	OrderRefundView      *OrderRefundView `protobuf:"bytes,1,opt,name=order_refund_view,json=orderRefundView,proto3" json:"order_refund_view,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetOrderRefundViewResponse) Reset()         { *m = GetOrderRefundViewResponse{} }
func (m *GetOrderRefundViewResponse) String() string { return proto.CompactTextString(m) }
func (*GetOrderRefundViewResponse) ProtoMessage()    {}
func (*GetOrderRefundViewResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{12}
}
func (m *GetOrderRefundViewResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderRefundViewResponse.Unmarshal(m, b)
}
func (m *GetOrderRefundViewResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderRefundViewResponse.Marshal(b, m, deterministic)
}
func (dst *GetOrderRefundViewResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderRefundViewResponse.Merge(dst, src)
}
func (m *GetOrderRefundViewResponse) XXX_Size() int {
	return xxx_messageInfo_GetOrderRefundViewResponse.Size(m)
}
func (m *GetOrderRefundViewResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderRefundViewResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderRefundViewResponse proto.InternalMessageInfo

func (m *GetOrderRefundViewResponse) GetOrderRefundView() *OrderRefundView {
	if m != nil {
		return m.OrderRefundView
	}
	return nil
}

type BatchGetOrderRefundStatusRequest struct {
	OrderIds             []string `protobuf:"bytes,1,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetOrderRefundStatusRequest) Reset()         { *m = BatchGetOrderRefundStatusRequest{} }
func (m *BatchGetOrderRefundStatusRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetOrderRefundStatusRequest) ProtoMessage()    {}
func (*BatchGetOrderRefundStatusRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{13}
}
func (m *BatchGetOrderRefundStatusRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOrderRefundStatusRequest.Unmarshal(m, b)
}
func (m *BatchGetOrderRefundStatusRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOrderRefundStatusRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetOrderRefundStatusRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOrderRefundStatusRequest.Merge(dst, src)
}
func (m *BatchGetOrderRefundStatusRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetOrderRefundStatusRequest.Size(m)
}
func (m *BatchGetOrderRefundStatusRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOrderRefundStatusRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOrderRefundStatusRequest proto.InternalMessageInfo

func (m *BatchGetOrderRefundStatusRequest) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

type BatchGetOrderRefundStatusResponse struct {
	OrderRefundStatus    []*BatchGetOrderRefundStatusResponse_OrderRefundStatus `protobuf:"bytes,1,rep,name=order_refund_status,json=orderRefundStatus,proto3" json:"order_refund_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                               `json:"-"`
	XXX_unrecognized     []byte                                                 `json:"-"`
	XXX_sizecache        int32                                                  `json:"-"`
}

func (m *BatchGetOrderRefundStatusResponse) Reset()         { *m = BatchGetOrderRefundStatusResponse{} }
func (m *BatchGetOrderRefundStatusResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetOrderRefundStatusResponse) ProtoMessage()    {}
func (*BatchGetOrderRefundStatusResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{14}
}
func (m *BatchGetOrderRefundStatusResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOrderRefundStatusResponse.Unmarshal(m, b)
}
func (m *BatchGetOrderRefundStatusResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOrderRefundStatusResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetOrderRefundStatusResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOrderRefundStatusResponse.Merge(dst, src)
}
func (m *BatchGetOrderRefundStatusResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetOrderRefundStatusResponse.Size(m)
}
func (m *BatchGetOrderRefundStatusResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOrderRefundStatusResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOrderRefundStatusResponse proto.InternalMessageInfo

func (m *BatchGetOrderRefundStatusResponse) GetOrderRefundStatus() []*BatchGetOrderRefundStatusResponse_OrderRefundStatus {
	if m != nil {
		return m.OrderRefundStatus
	}
	return nil
}

type BatchGetOrderRefundStatusResponse_OrderRefundStatus struct {
	OrderId                   string     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status                    uint32     `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	EndTime                   int64      `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CanCoachUploadAppeal      bool       `protobuf:"varint,4,opt,name=can_coach_upload_appeal,json=canCoachUploadAppeal,proto3" json:"can_coach_upload_appeal,omitempty"`
	RefundId                  string     `protobuf:"bytes,5,opt,name=refund_id,json=refundId,proto3" json:"refund_id,omitempty"`
	AppealId                  string     `protobuf:"bytes,6,opt,name=appeal_id,json=appealId,proto3" json:"appeal_id,omitempty"`
	RefundType                RefundType `protobuf:"varint,7,opt,name=refund_type,json=refundType,proto3,enum=esport_trade_appeal.RefundType" json:"refund_type,omitempty"`
	CoachUploadAppealDeadline int64      `protobuf:"varint,8,opt,name=coach_upload_appeal_deadline,json=coachUploadAppealDeadline,proto3" json:"coach_upload_appeal_deadline,omitempty"`
	CanAppeal                 bool       `protobuf:"varint,9,opt,name=can_appeal,json=canAppeal,proto3" json:"can_appeal,omitempty"`
	StatusUpdateTime          int64      `protobuf:"varint,10,opt,name=status_update_time,json=statusUpdateTime,proto3" json:"status_update_time,omitempty"`
	XXX_NoUnkeyedLiteral      struct{}   `json:"-"`
	XXX_unrecognized          []byte     `json:"-"`
	XXX_sizecache             int32      `json:"-"`
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) Reset() {
	*m = BatchGetOrderRefundStatusResponse_OrderRefundStatus{}
}
func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetOrderRefundStatusResponse_OrderRefundStatus) ProtoMessage() {}
func (*BatchGetOrderRefundStatusResponse_OrderRefundStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{14, 0}
}
func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOrderRefundStatusResponse_OrderRefundStatus.Unmarshal(m, b)
}
func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOrderRefundStatusResponse_OrderRefundStatus.Marshal(b, m, deterministic)
}
func (dst *BatchGetOrderRefundStatusResponse_OrderRefundStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOrderRefundStatusResponse_OrderRefundStatus.Merge(dst, src)
}
func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) XXX_Size() int {
	return xxx_messageInfo_BatchGetOrderRefundStatusResponse_OrderRefundStatus.Size(m)
}
func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOrderRefundStatusResponse_OrderRefundStatus.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOrderRefundStatusResponse_OrderRefundStatus proto.InternalMessageInfo

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetCanCoachUploadAppeal() bool {
	if m != nil {
		return m.CanCoachUploadAppeal
	}
	return false
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetRefundId() string {
	if m != nil {
		return m.RefundId
	}
	return ""
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetAppealId() string {
	if m != nil {
		return m.AppealId
	}
	return ""
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetRefundType() RefundType {
	if m != nil {
		return m.RefundType
	}
	return RefundType_REFUND_TYPE_FULL
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetCoachUploadAppealDeadline() int64 {
	if m != nil {
		return m.CoachUploadAppealDeadline
	}
	return 0
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetCanAppeal() bool {
	if m != nil {
		return m.CanAppeal
	}
	return false
}

func (m *BatchGetOrderRefundStatusResponse_OrderRefundStatus) GetStatusUpdateTime() int64 {
	if m != nil {
		return m.StatusUpdateTime
	}
	return 0
}

type CheckFastRefundPermissionRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFastRefundPermissionRequest) Reset()         { *m = CheckFastRefundPermissionRequest{} }
func (m *CheckFastRefundPermissionRequest) String() string { return proto.CompactTextString(m) }
func (*CheckFastRefundPermissionRequest) ProtoMessage()    {}
func (*CheckFastRefundPermissionRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{15}
}
func (m *CheckFastRefundPermissionRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFastRefundPermissionRequest.Unmarshal(m, b)
}
func (m *CheckFastRefundPermissionRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFastRefundPermissionRequest.Marshal(b, m, deterministic)
}
func (dst *CheckFastRefundPermissionRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFastRefundPermissionRequest.Merge(dst, src)
}
func (m *CheckFastRefundPermissionRequest) XXX_Size() int {
	return xxx_messageInfo_CheckFastRefundPermissionRequest.Size(m)
}
func (m *CheckFastRefundPermissionRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFastRefundPermissionRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFastRefundPermissionRequest proto.InternalMessageInfo

func (m *CheckFastRefundPermissionRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckFastRefundPermissionRequest) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type CheckFastRefundPermissionResponse struct {
	Has                  bool     `protobuf:"varint,1,opt,name=has,proto3" json:"has,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckFastRefundPermissionResponse) Reset()         { *m = CheckFastRefundPermissionResponse{} }
func (m *CheckFastRefundPermissionResponse) String() string { return proto.CompactTextString(m) }
func (*CheckFastRefundPermissionResponse) ProtoMessage()    {}
func (*CheckFastRefundPermissionResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_trade_appeal_7404901863826c66, []int{16}
}
func (m *CheckFastRefundPermissionResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckFastRefundPermissionResponse.Unmarshal(m, b)
}
func (m *CheckFastRefundPermissionResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckFastRefundPermissionResponse.Marshal(b, m, deterministic)
}
func (dst *CheckFastRefundPermissionResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckFastRefundPermissionResponse.Merge(dst, src)
}
func (m *CheckFastRefundPermissionResponse) XXX_Size() int {
	return xxx_messageInfo_CheckFastRefundPermissionResponse.Size(m)
}
func (m *CheckFastRefundPermissionResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckFastRefundPermissionResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckFastRefundPermissionResponse proto.InternalMessageInfo

func (m *CheckFastRefundPermissionResponse) GetHas() bool {
	if m != nil {
		return m.Has
	}
	return false
}

func init() {
	proto.RegisterType((*CreateRefundRequest)(nil), "esport_trade_appeal.CreateRefundRequest")
	proto.RegisterType((*CreateRefundResponse)(nil), "esport_trade_appeal.CreateRefundResponse")
	proto.RegisterType((*AcceptRefundRequest)(nil), "esport_trade_appeal.AcceptRefundRequest")
	proto.RegisterType((*AcceptRefundResponse)(nil), "esport_trade_appeal.AcceptRefundResponse")
	proto.RegisterType((*RejectRefundRequest)(nil), "esport_trade_appeal.RejectRefundRequest")
	proto.RegisterType((*RejectRefundResponse)(nil), "esport_trade_appeal.RejectRefundResponse")
	proto.RegisterType((*InitiateAppealRequest)(nil), "esport_trade_appeal.InitiateAppealRequest")
	proto.RegisterType((*InitiateAppealResponse)(nil), "esport_trade_appeal.InitiateAppealResponse")
	proto.RegisterType((*SubmitGuideAppealInfoRequest)(nil), "esport_trade_appeal.SubmitGuideAppealInfoRequest")
	proto.RegisterType((*SubmitGuideAppealInfoResponse)(nil), "esport_trade_appeal.SubmitGuideAppealInfoResponse")
	proto.RegisterType((*OrderRefundView)(nil), "esport_trade_appeal.OrderRefundView")
	proto.RegisterType((*GetOrderRefundViewRequest)(nil), "esport_trade_appeal.GetOrderRefundViewRequest")
	proto.RegisterType((*GetOrderRefundViewResponse)(nil), "esport_trade_appeal.GetOrderRefundViewResponse")
	proto.RegisterType((*BatchGetOrderRefundStatusRequest)(nil), "esport_trade_appeal.BatchGetOrderRefundStatusRequest")
	proto.RegisterType((*BatchGetOrderRefundStatusResponse)(nil), "esport_trade_appeal.BatchGetOrderRefundStatusResponse")
	proto.RegisterType((*BatchGetOrderRefundStatusResponse_OrderRefundStatus)(nil), "esport_trade_appeal.BatchGetOrderRefundStatusResponse.OrderRefundStatus")
	proto.RegisterType((*CheckFastRefundPermissionRequest)(nil), "esport_trade_appeal.CheckFastRefundPermissionRequest")
	proto.RegisterType((*CheckFastRefundPermissionResponse)(nil), "esport_trade_appeal.CheckFastRefundPermissionResponse")
	proto.RegisterEnum("esport_trade_appeal.RefundType", RefundType_name, RefundType_value)
	proto.RegisterEnum("esport_trade_appeal.RefundWay", RefundWay_name, RefundWay_value)
	proto.RegisterEnum("esport_trade_appeal.RefundStatus", RefundStatus_name, RefundStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RefundServiceClient is the client API for RefundService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RefundServiceClient interface {
	// 创建退款请求
	CreateRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc.CallOption) (*CreateRefundResponse, error)
	// 接受退款请求
	AcceptRefund(ctx context.Context, in *AcceptRefundRequest, opts ...grpc.CallOption) (*AcceptRefundResponse, error)
	// 拒绝退款请求
	RejectRefund(ctx context.Context, in *RejectRefundRequest, opts ...grpc.CallOption) (*RejectRefundResponse, error)
	// 发起申诉
	InitiateAppeal(ctx context.Context, in *InitiateAppealRequest, opts ...grpc.CallOption) (*InitiateAppealResponse, error)
	// 电竞指导者提交申诉信息
	SubmitGuideAppealInfo(ctx context.Context, in *SubmitGuideAppealInfoRequest, opts ...grpc.CallOption) (*SubmitGuideAppealInfoResponse, error)
	// 获取订单的退款信息视图
	GetOrderRefundView(ctx context.Context, in *GetOrderRefundViewRequest, opts ...grpc.CallOption) (*GetOrderRefundViewResponse, error)
	// 批量获取订单退款状态
	BatchGetOrderRefundStatus(ctx context.Context, in *BatchGetOrderRefundStatusRequest, opts ...grpc.CallOption) (*BatchGetOrderRefundStatusResponse, error)
	// 创建急速退款请求
	CreateFastRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc.CallOption) (*CreateRefundResponse, error)
	// 检查是否有急速退款权限
	CheckFastRefundPermission(ctx context.Context, in *CheckFastRefundPermissionRequest, opts ...grpc.CallOption) (*CheckFastRefundPermissionResponse, error)
}

type refundServiceClient struct {
	cc *grpc.ClientConn
}

func NewRefundServiceClient(cc *grpc.ClientConn) RefundServiceClient {
	return &refundServiceClient{cc}
}

func (c *refundServiceClient) CreateRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc.CallOption) (*CreateRefundResponse, error) {
	out := new(CreateRefundResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/CreateRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) AcceptRefund(ctx context.Context, in *AcceptRefundRequest, opts ...grpc.CallOption) (*AcceptRefundResponse, error) {
	out := new(AcceptRefundResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/AcceptRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) RejectRefund(ctx context.Context, in *RejectRefundRequest, opts ...grpc.CallOption) (*RejectRefundResponse, error) {
	out := new(RejectRefundResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/RejectRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) InitiateAppeal(ctx context.Context, in *InitiateAppealRequest, opts ...grpc.CallOption) (*InitiateAppealResponse, error) {
	out := new(InitiateAppealResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/InitiateAppeal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) SubmitGuideAppealInfo(ctx context.Context, in *SubmitGuideAppealInfoRequest, opts ...grpc.CallOption) (*SubmitGuideAppealInfoResponse, error) {
	out := new(SubmitGuideAppealInfoResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/SubmitGuideAppealInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) GetOrderRefundView(ctx context.Context, in *GetOrderRefundViewRequest, opts ...grpc.CallOption) (*GetOrderRefundViewResponse, error) {
	out := new(GetOrderRefundViewResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/GetOrderRefundView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) BatchGetOrderRefundStatus(ctx context.Context, in *BatchGetOrderRefundStatusRequest, opts ...grpc.CallOption) (*BatchGetOrderRefundStatusResponse, error) {
	out := new(BatchGetOrderRefundStatusResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/BatchGetOrderRefundStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) CreateFastRefund(ctx context.Context, in *CreateRefundRequest, opts ...grpc.CallOption) (*CreateRefundResponse, error) {
	out := new(CreateRefundResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/CreateFastRefund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *refundServiceClient) CheckFastRefundPermission(ctx context.Context, in *CheckFastRefundPermissionRequest, opts ...grpc.CallOption) (*CheckFastRefundPermissionResponse, error) {
	out := new(CheckFastRefundPermissionResponse)
	err := c.cc.Invoke(ctx, "/esport_trade_appeal.RefundService/CheckFastRefundPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RefundServiceServer is the server API for RefundService service.
type RefundServiceServer interface {
	// 创建退款请求
	CreateRefund(context.Context, *CreateRefundRequest) (*CreateRefundResponse, error)
	// 接受退款请求
	AcceptRefund(context.Context, *AcceptRefundRequest) (*AcceptRefundResponse, error)
	// 拒绝退款请求
	RejectRefund(context.Context, *RejectRefundRequest) (*RejectRefundResponse, error)
	// 发起申诉
	InitiateAppeal(context.Context, *InitiateAppealRequest) (*InitiateAppealResponse, error)
	// 电竞指导者提交申诉信息
	SubmitGuideAppealInfo(context.Context, *SubmitGuideAppealInfoRequest) (*SubmitGuideAppealInfoResponse, error)
	// 获取订单的退款信息视图
	GetOrderRefundView(context.Context, *GetOrderRefundViewRequest) (*GetOrderRefundViewResponse, error)
	// 批量获取订单退款状态
	BatchGetOrderRefundStatus(context.Context, *BatchGetOrderRefundStatusRequest) (*BatchGetOrderRefundStatusResponse, error)
	// 创建急速退款请求
	CreateFastRefund(context.Context, *CreateRefundRequest) (*CreateRefundResponse, error)
	// 检查是否有急速退款权限
	CheckFastRefundPermission(context.Context, *CheckFastRefundPermissionRequest) (*CheckFastRefundPermissionResponse, error)
}

func RegisterRefundServiceServer(s *grpc.Server, srv RefundServiceServer) {
	s.RegisterService(&_RefundService_serviceDesc, srv)
}

func _RefundService_CreateRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).CreateRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/CreateRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).CreateRefund(ctx, req.(*CreateRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_AcceptRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).AcceptRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/AcceptRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).AcceptRefund(ctx, req.(*AcceptRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_RejectRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RejectRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).RejectRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/RejectRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).RejectRefund(ctx, req.(*RejectRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_InitiateAppeal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateAppealRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).InitiateAppeal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/InitiateAppeal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).InitiateAppeal(ctx, req.(*InitiateAppealRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_SubmitGuideAppealInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitGuideAppealInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).SubmitGuideAppealInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/SubmitGuideAppealInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).SubmitGuideAppealInfo(ctx, req.(*SubmitGuideAppealInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_GetOrderRefundView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderRefundViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).GetOrderRefundView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/GetOrderRefundView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).GetOrderRefundView(ctx, req.(*GetOrderRefundViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_BatchGetOrderRefundStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetOrderRefundStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).BatchGetOrderRefundStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/BatchGetOrderRefundStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).BatchGetOrderRefundStatus(ctx, req.(*BatchGetOrderRefundStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_CreateFastRefund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRefundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).CreateFastRefund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/CreateFastRefund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).CreateFastRefund(ctx, req.(*CreateRefundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RefundService_CheckFastRefundPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckFastRefundPermissionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RefundServiceServer).CheckFastRefundPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_trade_appeal.RefundService/CheckFastRefundPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RefundServiceServer).CheckFastRefundPermission(ctx, req.(*CheckFastRefundPermissionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _RefundService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_trade_appeal.RefundService",
	HandlerType: (*RefundServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateRefund",
			Handler:    _RefundService_CreateRefund_Handler,
		},
		{
			MethodName: "AcceptRefund",
			Handler:    _RefundService_AcceptRefund_Handler,
		},
		{
			MethodName: "RejectRefund",
			Handler:    _RefundService_RejectRefund_Handler,
		},
		{
			MethodName: "InitiateAppeal",
			Handler:    _RefundService_InitiateAppeal_Handler,
		},
		{
			MethodName: "SubmitGuideAppealInfo",
			Handler:    _RefundService_SubmitGuideAppealInfo_Handler,
		},
		{
			MethodName: "GetOrderRefundView",
			Handler:    _RefundService_GetOrderRefundView_Handler,
		},
		{
			MethodName: "BatchGetOrderRefundStatus",
			Handler:    _RefundService_BatchGetOrderRefundStatus_Handler,
		},
		{
			MethodName: "CreateFastRefund",
			Handler:    _RefundService_CreateFastRefund_Handler,
		},
		{
			MethodName: "CheckFastRefundPermission",
			Handler:    _RefundService_CheckFastRefundPermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "esport-trade-appeal/esport-trade-appeal.proto",
}

func init() {
	proto.RegisterFile("esport-trade-appeal/esport-trade-appeal.proto", fileDescriptor_esport_trade_appeal_7404901863826c66)
}

var fileDescriptor_esport_trade_appeal_7404901863826c66 = []byte{
	// 1295 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x57, 0xcd, 0x72, 0xe3, 0x44,
	0x10, 0xb6, 0xe2, 0xfc, 0xd8, 0xed, 0x78, 0x23, 0x8f, 0xb3, 0x89, 0xe2, 0x6c, 0xd6, 0x8e, 0xe0,
	0x60, 0xc2, 0xc6, 0x01, 0x6f, 0x65, 0xa1, 0x8a, 0xa2, 0x16, 0xad, 0xe3, 0x04, 0x53, 0x21, 0x71,
	0xc9, 0x36, 0xa9, 0xe5, 0xa2, 0x9a, 0x95, 0x26, 0x89, 0xd8, 0x58, 0xd2, 0x4a, 0xf2, 0x66, 0x7d,
	0xa0, 0x8a, 0x07, 0xe0, 0x29, 0xa8, 0xe2, 0x19, 0x38, 0x71, 0xe1, 0xc4, 0x81, 0x13, 0x6f, 0xc2,
	0x95, 0x13, 0xa5, 0x99, 0xb1, 0x23, 0x39, 0x92, 0x9d, 0x6c, 0x15, 0x27, 0x7b, 0xba, 0x7b, 0xba,
	0xbf, 0xe9, 0xf9, 0xba, 0xa7, 0x05, 0xbb, 0xc4, 0x73, 0x6c, 0xd7, 0xdf, 0xf5, 0x5d, 0x6c, 0x90,
	0x5d, 0xec, 0x38, 0x04, 0x5f, 0xed, 0xc5, 0xc8, 0x6a, 0x8e, 0x6b, 0xfb, 0x36, 0x2a, 0x32, 0x95,
	0x46, 0x55, 0x1a, 0x53, 0x95, 0xca, 0xe4, 0x9d, 0x4f, 0x2c, 0xcf, 0xb4, 0xad, 0x3d, 0xdb, 0xf1,
	0x4d, 0xdb, 0xf2, 0x46, 0xbf, 0x6c, 0x97, 0xfc, 0xbb, 0x00, 0xc5, 0x86, 0x4b, 0xb0, 0x4f, 0x54,
	0x72, 0x3e, 0xb0, 0x0c, 0x95, 0xbc, 0x19, 0x10, 0xcf, 0x47, 0x1b, 0x90, 0xb1, 0x5d, 0x83, 0xb8,
	0x9a, 0x69, 0x48, 0x42, 0x45, 0xa8, 0x66, 0xd5, 0x25, 0xba, 0x6e, 0x19, 0xa8, 0x04, 0x99, 0x37,
	0x03, 0x6c, 0xf9, 0xa6, 0x3f, 0x94, 0xe6, 0x2a, 0x42, 0x35, 0xaf, 0x8e, 0xd7, 0x68, 0x0d, 0x16,
	0x5d, 0x82, 0x3d, 0xdb, 0x92, 0xd2, 0x74, 0x13, 0x5f, 0x21, 0x19, 0x96, 0x4d, 0xcb, 0xf3, 0xdd,
	0x81, 0x4e, 0x83, 0x4b, 0xf3, 0x54, 0x1b, 0x91, 0xa1, 0xa7, 0x30, 0xef, 0x0f, 0x1d, 0x22, 0x2d,
	0x54, 0x84, 0xea, 0x83, 0x7a, 0xb9, 0x16, 0x73, 0x9e, 0x1a, 0x03, 0xd9, 0x1d, 0x3a, 0x44, 0xa5,
	0xc6, 0xf2, 0x1a, 0xac, 0x46, 0xe1, 0x7b, 0x8e, 0x6d, 0x79, 0x44, 0x56, 0xa0, 0xa8, 0xe8, 0x3a,
	0x71, 0xfc, 0xe8, 0xb1, 0x76, 0xa0, 0xe0, 0x52, 0x81, 0xe6, 0x32, 0xc9, 0xcd, 0xf9, 0x56, 0xdc,
	0xb0, 0x65, 0xcb, 0x08, 0x5c, 0x47, 0x5d, 0x70, 0xd7, 0x3f, 0x42, 0x51, 0x25, 0x3f, 0x10, 0xfd,
	0xfd, 0x5d, 0x87, 0xd2, 0x34, 0x37, 0x35, 0x4d, 0xe9, 0xdb, 0x69, 0x0a, 0x60, 0x45, 0xc3, 0x73,
	0x58, 0x36, 0x3c, 0x6c, 0x59, 0xa6, 0x6f, 0x62, 0x9f, 0x28, 0x34, 0x59, 0x23, 0x60, 0x9b, 0x90,
	0xe5, 0xc0, 0xc6, 0x80, 0x32, 0x4c, 0x30, 0x05, 0xc9, 0x36, 0x2c, 0x3b, 0xae, 0x6d, 0x9f, 0x6b,
	0x66, 0x1f, 0x5f, 0x90, 0x00, 0x49, 0xba, 0x9a, 0x55, 0x73, 0x54, 0xd6, 0xa2, 0x22, 0x59, 0x82,
	0xb5, 0xc9, 0x80, 0x1c, 0xca, 0xaf, 0x02, 0x3c, 0xea, 0x0c, 0x5e, 0xf5, 0x4d, 0xff, 0x68, 0x60,
	0x1a, 0x5c, 0xdb, 0xb2, 0xce, 0xed, 0x10, 0x24, 0x76, 0xa1, 0x21, 0x48, 0x4c, 0xd0, 0x32, 0xd0,
	0xe7, 0x20, 0xe9, 0x36, 0xd6, 0x2f, 0xf9, 0x9d, 0x6b, 0x06, 0xf1, 0x74, 0xd7, 0xa4, 0xac, 0xe5,
	0x20, 0xd7, 0xa8, 0x9e, 0xb9, 0x3d, 0xb8, 0xd1, 0xa2, 0x1a, 0x14, 0x23, 0x3b, 0x23, 0xd8, 0x0b,
	0xa1, 0x4d, 0xfc, 0x04, 0x65, 0xd8, 0x4a, 0x80, 0xc9, 0x0f, 0xf2, 0xe7, 0x02, 0xac, 0x9c, 0x06,
	0xb4, 0x67, 0xb9, 0xfe, 0xce, 0x24, 0xd7, 0x33, 0xd3, 0xe9, 0xf9, 0xd8, 0x1f, 0x78, 0xbc, 0x32,
	0xf8, 0x0a, 0x7d, 0x05, 0x39, 0xbe, 0x89, 0x52, 0x3c, 0x7d, 0x37, 0x8a, 0x83, 0x3b, 0xfe, 0x8f,
	0x3e, 0x80, 0xfc, 0x98, 0x5e, 0xf4, 0xbe, 0x78, 0x09, 0x8d, 0xa8, 0x35, 0xba, 0x35, 0x6e, 0xe4,
	0xb8, 0xa6, 0xce, 0x4a, 0x29, 0xaf, 0xf2, 0xd0, 0xed, 0x40, 0x14, 0x32, 0xd1, 0xed, 0x81, 0xe5,
	0x4b, 0x8b, 0x61, 0x93, 0x46, 0x20, 0x42, 0xe5, 0x31, 0xd8, 0x20, 0xf5, 0xd2, 0x12, 0x0d, 0xc4,
	0xb1, 0x04, 0xe9, 0x0e, 0x9a, 0x03, 0x09, 0x8e, 0x62, 0xf6, 0x89, 0x94, 0xa9, 0x08, 0xd5, 0xb4,
	0xba, 0x44, 0x2c, 0xa3, 0x6b, 0xf6, 0x09, 0xfa, 0x04, 0x56, 0xc7, 0x30, 0x03, 0x92, 0x8e, 0xd0,
	0x02, 0x75, 0x82, 0x46, 0x68, 0x19, 0x7f, 0x29, 0xe6, 0x27, 0x80, 0xa2, 0x3b, 0x68, 0xd0, 0x1c,
	0xb5, 0x17, 0xc3, 0xf6, 0x34, 0x74, 0x19, 0x72, 0x21, 0x5a, 0x48, 0x59, 0x86, 0x0d, 0x8f, 0xa9,
	0x10, 0xe4, 0x89, 0x1b, 0xf0, 0xc8, 0xcb, 0x2c, 0x4f, 0x98, 0x53, 0x94, 0xc6, 0xdc, 0x87, 0x75,
	0x1d, 0x5b, 0x1a, 0x23, 0xcb, 0xc0, 0xb9, 0xb2, 0xb1, 0xc1, 0xd3, 0x2f, 0xe5, 0x2b, 0x42, 0x35,
	0xa3, 0xae, 0xea, 0xd8, 0x6a, 0x04, 0xda, 0x1e, 0x55, 0x32, 0x6a, 0x44, 0x69, 0xfb, 0x60, 0x82,
	0xb6, 0xcf, 0xe1, 0x51, 0x8c, 0x3f, 0xcd, 0x20, 0xd8, 0xb8, 0x32, 0x2d, 0x22, 0xad, 0xd0, 0x44,
	0x6d, 0xe8, 0x93, 0x5e, 0x0f, 0xb8, 0x01, 0xda, 0x02, 0x08, 0x40, 0x71, 0x1c, 0x22, 0xc5, 0x91,
	0xd5, 0xb1, 0xc5, 0x83, 0x7f, 0x09, 0xfc, 0x0a, 0xb4, 0x6b, 0x3c, 0x94, 0x0a, 0x94, 0x41, 0x8f,
	0xa7, 0x30, 0xe8, 0x0c, 0x0f, 0x55, 0xce, 0xd4, 0x33, 0x3c, 0x94, 0x9f, 0xc1, 0xc6, 0x11, 0xf1,
	0x27, 0xc8, 0x3c, 0xbb, 0xdb, 0xcb, 0x16, 0x94, 0xe2, 0xf6, 0xb1, 0x02, 0x41, 0x6d, 0x28, 0xb0,
	0x8d, 0x1c, 0xda, 0x5b, 0x93, 0x5c, 0x53, 0x0f, 0xb9, 0xfa, 0x87, 0xb1, 0xd8, 0x26, 0x1d, 0xad,
	0xd8, 0x51, 0x81, 0xfc, 0x1c, 0x2a, 0x2f, 0xb0, 0xaf, 0x5f, 0x46, 0x83, 0x76, 0x68, 0x19, 0x85,
	0xda, 0xc7, 0x08, 0xae, 0x27, 0x09, 0xb4, 0xba, 0x33, 0x1c, 0xaf, 0x27, 0xff, 0x31, 0x0f, 0xdb,
	0x53, 0x3c, 0x70, 0xe0, 0xef, 0xa0, 0x18, 0x01, 0xce, 0xab, 0x36, 0x70, 0x96, 0xab, 0x7f, 0x1d,
	0x0b, 0x7d, 0xa6, 0xd3, 0xda, 0x6d, 0x4d, 0xc1, 0x9e, 0x14, 0x95, 0x7e, 0x49, 0x43, 0xe1, 0x96,
	0xe1, 0xb4, 0xf7, 0x36, 0xa9, 0xa7, 0x84, 0xab, 0x30, 0x1d, 0xad, 0xc2, 0x29, 0xfc, 0x9e, 0x9f,
	0xce, 0xef, 0x9b, 0xd6, 0xb6, 0x30, 0xd1, 0xda, 0x22, 0xe4, 0x5f, 0x9c, 0x20, 0xff, 0x44, 0x7f,
	0x5b, 0xba, 0x7f, 0x7f, 0x9b, 0x55, 0x3e, 0x99, 0xfb, 0x95, 0x4f, 0x76, 0xb2, 0x7c, 0x9e, 0x00,
	0x62, 0x79, 0xd3, 0x06, 0x8e, 0x81, 0x7d, 0xc2, 0xf2, 0x06, 0xd4, 0xab, 0xc8, 0x34, 0x3d, 0xaa,
	0x08, 0x12, 0x28, 0x9f, 0x42, 0xa5, 0x71, 0x49, 0xf4, 0xd7, 0x87, 0xd8, 0xe3, 0xef, 0x6c, 0x9b,
	0xb8, 0x7d, 0xd3, 0x0b, 0x26, 0xa9, 0x11, 0x0b, 0x45, 0x48, 0x0f, 0xf8, 0x6d, 0xe5, 0xd5, 0xe0,
	0x6f, 0xe4, 0x12, 0xe7, 0xa2, 0x65, 0xb4, 0x0f, 0xdb, 0x53, 0x1c, 0x72, 0x52, 0x8a, 0x90, 0xbe,
	0xc4, 0x1e, 0xf5, 0x98, 0x51, 0x83, 0xbf, 0x3b, 0x5f, 0x00, 0xdc, 0xe4, 0x0b, 0xad, 0x82, 0xa8,
	0x36, 0x0f, 0x7b, 0x27, 0x07, 0x5a, 0xf7, 0x65, 0xbb, 0xa9, 0x1d, 0xf6, 0x8e, 0x8f, 0xc5, 0x14,
	0x5a, 0x87, 0x62, 0x58, 0xda, 0x56, 0xd4, 0x6e, 0x4b, 0x39, 0x16, 0x85, 0x9d, 0x06, 0x64, 0xc7,
	0xad, 0x00, 0x6d, 0xc3, 0x16, 0xb7, 0x3a, 0x53, 0x5e, 0x6a, 0x27, 0xa7, 0xea, 0xb7, 0xca, 0xb1,
	0xd6, 0x3b, 0xe9, 0xb4, 0x9b, 0x8d, 0xd6, 0x61, 0xab, 0x79, 0x20, 0xa6, 0x50, 0x11, 0x56, 0x42,
	0x26, 0x87, 0x4a, 0xa7, 0x2b, 0x0a, 0x3b, 0xff, 0x08, 0xb0, 0x1c, 0x61, 0xea, 0x16, 0x6c, 0x70,
	0xab, 0x4e, 0x57, 0xe9, 0xf6, 0x3a, 0x13, 0x4e, 0x36, 0x61, 0x3d, 0xaa, 0x66, 0xab, 0xd6, 0xc9,
	0x91, 0x28, 0xa0, 0x32, 0x6c, 0xc6, 0x29, 0x35, 0xa5, 0xd1, 0x68, 0xb6, 0xbb, 0xe2, 0x5c, 0xa2,
	0x81, 0xda, 0xfc, 0xa6, 0xd9, 0xe8, 0x8a, 0xe9, 0xdb, 0xee, 0x95, 0x76, 0xbb, 0xa9, 0x1c, 0x07,
	0xee, 0xe7, 0x91, 0x0c, 0x8f, 0x13, 0x94, 0xa3, 0x08, 0x0b, 0xd3, 0x6c, 0x78, 0x90, 0xc5, 0xfa,
	0xdf, 0x19, 0xc8, 0xf3, 0x33, 0x13, 0xf7, 0x6d, 0xf0, 0x6a, 0x12, 0x58, 0x0e, 0x8f, 0x99, 0xa8,
	0x1a, 0x4b, 0xed, 0x98, 0x41, 0xba, 0xf4, 0xd1, 0x1d, 0x2c, 0xf9, 0xb4, 0x91, 0x0a, 0xc2, 0x84,
	0x47, 0xce, 0x84, 0x30, 0x31, 0x83, 0x6d, 0x42, 0x98, 0xd8, 0xf9, 0x95, 0x86, 0x09, 0x8f, 0x90,
	0x09, 0x61, 0x62, 0x86, 0xdc, 0x84, 0x30, 0xb1, 0xf3, 0x68, 0x0a, 0xbd, 0x86, 0x07, 0xd1, 0x01,
	0x11, 0xed, 0xc4, 0x6e, 0x8f, 0x1d, 0x5b, 0x4b, 0x1f, 0xdf, 0xc9, 0x76, 0x1c, 0xec, 0x27, 0x01,
	0x1e, 0xc6, 0x0e, 0x73, 0xe8, 0xd3, 0x58, 0x47, 0xd3, 0xe6, 0xd3, 0x52, 0xfd, 0x3e, 0x5b, 0xc6,
	0x10, 0xae, 0x01, 0xdd, 0x7e, 0x2a, 0x51, 0x2d, 0xd6, 0x57, 0xe2, 0x5b, 0x5c, 0xda, 0xbb, 0xb3,
	0xfd, 0x38, 0xf0, 0xcf, 0x02, 0x6c, 0x24, 0xbe, 0x4e, 0x68, 0xff, 0xbe, 0xaf, 0x19, 0xc3, 0xf1,
	0xec, 0xfd, 0x1e, 0x41, 0x39, 0x85, 0x4c, 0x10, 0x19, 0xbf, 0x6f, 0x9a, 0xdd, 0xff, 0x55, 0x30,
	0xc1, 0xc9, 0x13, 0xfb, 0x6a, 0xc2, 0xc9, 0x67, 0x35, 0xf6, 0x84, 0x93, 0xcf, 0x6c, 0xdf, 0x72,
	0xaa, 0x54, 0xfa, 0xf7, 0xb7, 0xbf, 0xba, 0x0f, 0xa1, 0x18, 0xf3, 0x95, 0xfe, 0xe2, 0xb3, 0xef,
	0xf7, 0x2f, 0xec, 0x2b, 0x6c, 0x5d, 0xd4, 0xf6, 0xeb, 0xbe, 0x5f, 0xd3, 0xed, 0xfe, 0x1e, 0xfd,
	0x04, 0xd7, 0xed, 0xab, 0x3d, 0x8f, 0xb5, 0x19, 0x2f, 0xee, 0xf3, 0xfe, 0xd5, 0x22, 0x35, 0x7b,
	0xfa, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x96, 0x2a, 0x26, 0xab, 0x10, 0x10, 0x00, 0x00,
}
