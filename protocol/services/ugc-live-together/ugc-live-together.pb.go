// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ugc-live-together/ugc-live-together.proto

package ugc_live_together // import "golang.52tt.com/protocol/services/ugc-live-together"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 产品
type App int32

const (
	App_AppNone    App = 0
	App_AppTT      App = 1
	App_AppHuanYou App = 2
	App_AppMaiKe   App = 3
	App_AppMiJing  App = 6
)

var App_name = map[int32]string{
	0: "AppNone",
	1: "AppTT",
	2: "AppHuanYou",
	3: "AppMaiKe",
	6: "AppMiJing",
}
var App_value = map[string]int32{
	"AppNone":    0,
	"AppTT":      1,
	"AppHuanYou": 2,
	"AppMaiKe":   3,
	"AppMiJing":  6,
}

func (x App) String() string {
	return proto.EnumName(App_name, int32(x))
}
func (App) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{0}
}

// 平台
type Platform int32

const (
	Platform_PlatformAll     Platform = 0
	Platform_PlatformAndroid Platform = 1
	Platform_PlatformIOS     Platform = 2
)

var Platform_name = map[int32]string{
	0: "PlatformAll",
	1: "PlatformAndroid",
	2: "PlatformIOS",
}
var Platform_value = map[string]int32{
	"PlatformAll":     0,
	"PlatformAndroid": 1,
	"PlatformIOS":     2,
}

func (x Platform) String() string {
	return proto.EnumName(Platform_name, int32(x))
}
func (Platform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{1}
}

// 展示对象
type UserType int32

const (
	UserType_UserTypeAll       UserType = 0
	UserType_UserTypeSpecified UserType = 1
	UserType_UserTypeCrowdPkg  UserType = 2
)

var UserType_name = map[int32]string{
	0: "UserTypeAll",
	1: "UserTypeSpecified",
	2: "UserTypeCrowdPkg",
}
var UserType_value = map[string]int32{
	"UserTypeAll":       0,
	"UserTypeSpecified": 1,
	"UserTypeCrowdPkg":  2,
}

func (x UserType) String() string {
	return proto.EnumName(UserType_name, int32(x))
}
func (UserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{2}
}

// 配置状态
type ConfStatus int32

const (
	ConfStatus_ConfStatusNone     ConfStatus = 0
	ConfStatus_ConfStatusInactive ConfStatus = 1
	ConfStatus_ConfStatusActive   ConfStatus = 2
	ConfStatus_ConfStatusExpired  ConfStatus = 3
)

var ConfStatus_name = map[int32]string{
	0: "ConfStatusNone",
	1: "ConfStatusInactive",
	2: "ConfStatusActive",
	3: "ConfStatusExpired",
}
var ConfStatus_value = map[string]int32{
	"ConfStatusNone":     0,
	"ConfStatusInactive": 1,
	"ConfStatusActive":   2,
	"ConfStatusExpired":  3,
}

func (x ConfStatus) String() string {
	return proto.EnumName(ConfStatus_name, int32(x))
}
func (ConfStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{3}
}

type UGCLiveTogetherStatus int32

const (
	UGCLiveTogetherStatus_NONE  UGCLiveTogetherStatus = 0
	UGCLiveTogetherStatus_CLOSE UGCLiveTogetherStatus = 1
	UGCLiveTogetherStatus_OPEN  UGCLiveTogetherStatus = 2
)

var UGCLiveTogetherStatus_name = map[int32]string{
	0: "NONE",
	1: "CLOSE",
	2: "OPEN",
}
var UGCLiveTogetherStatus_value = map[string]int32{
	"NONE":  0,
	"CLOSE": 1,
	"OPEN":  2,
}

func (x UGCLiveTogetherStatus) String() string {
	return proto.EnumName(UGCLiveTogetherStatus_name, int32(x))
}
func (UGCLiveTogetherStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{4}
}

// 广告推送状态
type AdsConfig_PushState int32

const (
	AdsConfig_PushStateNone       AdsConfig_PushState = 0
	AdsConfig_PushStateInit       AdsConfig_PushState = 1
	AdsConfig_PushStateProcessing AdsConfig_PushState = 2
)

var AdsConfig_PushState_name = map[int32]string{
	0: "PushStateNone",
	1: "PushStateInit",
	2: "PushStateProcessing",
}
var AdsConfig_PushState_value = map[string]int32{
	"PushStateNone":       0,
	"PushStateInit":       1,
	"PushStateProcessing": 2,
}

func (x AdsConfig_PushState) String() string {
	return proto.EnumName(AdsConfig_PushState_name, int32(x))
}
func (AdsConfig_PushState) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{1, 0}
}

type LiveConfig_PlayFormat int32

const (
	LiveConfig_PlayFormatOther LiveConfig_PlayFormat = 0
	LiveConfig_PlayFormatRtmp  LiveConfig_PlayFormat = 1
)

var LiveConfig_PlayFormat_name = map[int32]string{
	0: "PlayFormatOther",
	1: "PlayFormatRtmp",
}
var LiveConfig_PlayFormat_value = map[string]int32{
	"PlayFormatOther": 0,
	"PlayFormatRtmp":  1,
}

func (x LiveConfig_PlayFormat) String() string {
	return proto.EnumName(LiveConfig_PlayFormat_name, int32(x))
}
func (LiveConfig_PlayFormat) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{2, 0}
}

type PushAdsReq_Op int32

const (
	PushAdsReq_OpNone   PushAdsReq_Op = 0
	PushAdsReq_OpPush   PushAdsReq_Op = 1
	PushAdsReq_OpCancel PushAdsReq_Op = 2
)

var PushAdsReq_Op_name = map[int32]string{
	0: "OpNone",
	1: "OpPush",
	2: "OpCancel",
}
var PushAdsReq_Op_value = map[string]int32{
	"OpNone":   0,
	"OpPush":   1,
	"OpCancel": 2,
}

func (x PushAdsReq_Op) String() string {
	return proto.EnumName(PushAdsReq_Op_name, int32(x))
}
func (PushAdsReq_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{17, 0}
}

// 房主信息
type ChannelOwner struct {
	// 房主uid
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 房主房间channel id
	Cid uint32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	// 房主tt id
	Tid string `protobuf:"bytes,3,opt,name=tid,proto3" json:"tid,omitempty"`
	// 房主昵称
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelOwner) Reset()         { *m = ChannelOwner{} }
func (m *ChannelOwner) String() string { return proto.CompactTextString(m) }
func (*ChannelOwner) ProtoMessage()    {}
func (*ChannelOwner) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{0}
}
func (m *ChannelOwner) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelOwner.Unmarshal(m, b)
}
func (m *ChannelOwner) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelOwner.Marshal(b, m, deterministic)
}
func (dst *ChannelOwner) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelOwner.Merge(dst, src)
}
func (m *ChannelOwner) XXX_Size() int {
	return xxx_messageInfo_ChannelOwner.Size(m)
}
func (m *ChannelOwner) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelOwner.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelOwner proto.InternalMessageInfo

func (m *ChannelOwner) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelOwner) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *ChannelOwner) GetTid() string {
	if m != nil {
		return m.Tid
	}
	return ""
}

func (m *ChannelOwner) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

// 插播广告配置
type AdsConfig struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 广告链接
	Link string `protobuf:"bytes,2,opt,name=link,proto3" json:"link,omitempty"`
	// 广告推送状态
	State AdsConfig_PushState `protobuf:"varint,3,opt,name=state,proto3,enum=ugc_live_together.AdsConfig_PushState" json:"state,omitempty"`
	// 最近更新时间
	UpdatedAt uint32 `protobuf:"varint,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 关联的直播配置id
	LiveId               uint32   `protobuf:"varint,5,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AdsConfig) Reset()         { *m = AdsConfig{} }
func (m *AdsConfig) String() string { return proto.CompactTextString(m) }
func (*AdsConfig) ProtoMessage()    {}
func (*AdsConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{1}
}
func (m *AdsConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AdsConfig.Unmarshal(m, b)
}
func (m *AdsConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AdsConfig.Marshal(b, m, deterministic)
}
func (dst *AdsConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AdsConfig.Merge(dst, src)
}
func (m *AdsConfig) XXX_Size() int {
	return xxx_messageInfo_AdsConfig.Size(m)
}
func (m *AdsConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AdsConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AdsConfig proto.InternalMessageInfo

func (m *AdsConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AdsConfig) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

func (m *AdsConfig) GetState() AdsConfig_PushState {
	if m != nil {
		return m.State
	}
	return AdsConfig_PushStateNone
}

func (m *AdsConfig) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *AdsConfig) GetLiveId() uint32 {
	if m != nil {
		return m.LiveId
	}
	return 0
}

// 直播配置
type LiveConfig struct {
	Id        uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UpdatedAt uint32 `protobuf:"varint,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 产品
	AppList []App `protobuf:"varint,3,rep,packed,name=app_list,json=appList,proto3,enum=ugc_live_together.App" json:"app_list,omitempty"`
	// 平台
	Platform Platform `protobuf:"varint,4,opt,name=platform,proto3,enum=ugc_live_together.Platform" json:"platform,omitempty"`
	// 生效开始时间
	BeginAt uint32 `protobuf:"varint,5,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	// 生效结束时间
	EndAt uint32 `protobuf:"varint,6,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	// 房间主题id
	TabId uint32 `protobuf:"varint,7,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 房间主题名称
	TabName string `protobuf:"bytes,19,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	// 展示对象类型
	UserType UserType `protobuf:"varint,8,opt,name=user_type,json=userType,proto3,enum=ugc_live_together.UserType" json:"user_type,omitempty"`
	// 房主信息
	OwnerList []*ChannelOwner `protobuf:"bytes,9,rep,name=owner_list,json=ownerList,proto3" json:"owner_list,omitempty"`
	// 直播链接
	LiveLink string `protobuf:"bytes,10,opt,name=live_link,json=liveLink,proto3" json:"live_link,omitempty"`
	// 直播入口文案
	EntranceText string `protobuf:"bytes,11,opt,name=entrance_text,json=entranceText,proto3" json:"entrance_text,omitempty"`
	// 直播入口配图
	EntrancePic string `protobuf:"bytes,12,opt,name=entrance_pic,json=entrancePic,proto3" json:"entrance_pic,omitempty"`
	// 直播悬浮球配图
	FloatPic string `protobuf:"bytes,13,opt,name=float_pic,json=floatPic,proto3" json:"float_pic,omitempty"`
	// 视频标题
	VideoTitle string `protobuf:"bytes,14,opt,name=video_title,json=videoTitle,proto3" json:"video_title,omitempty"`
	// 视频比例：长度
	VideoLength uint32 `protobuf:"varint,15,opt,name=video_length,json=videoLength,proto3" json:"video_length,omitempty"`
	// 视频比例：宽度
	VideoWidth uint32 `protobuf:"varint,16,opt,name=video_width,json=videoWidth,proto3" json:"video_width,omitempty"`
	// 插播广告列表
	AdsList []*AdsConfig `protobuf:"bytes,17,rep,name=ads_list,json=adsList,proto3" json:"ads_list,omitempty"`
	// 配置状态
	Status ConfStatus `protobuf:"varint,18,opt,name=status,proto3,enum=ugc_live_together.ConfStatus" json:"status,omitempty"`
	// 人群包id
	CrowedPkgId string `protobuf:"bytes,20,opt,name=crowed_pkg_id,json=crowedPkgId,proto3" json:"crowed_pkg_id,omitempty"`
	// 视频播放格式
	PlayFormat LiveConfig_PlayFormat `protobuf:"varint,21,opt,name=play_format,json=playFormat,proto3,enum=ugc_live_together.LiveConfig_PlayFormat" json:"play_format,omitempty"`
	// 是否有强引导
	HasStrongGuide       bool     `protobuf:"varint,22,opt,name=has_strong_guide,json=hasStrongGuide,proto3" json:"has_strong_guide,omitempty"`
	GuideGif             string   `protobuf:"bytes,23,opt,name=guide_gif,json=guideGif,proto3" json:"guide_gif,omitempty"`
	GuideTitle           string   `protobuf:"bytes,24,opt,name=guide_title,json=guideTitle,proto3" json:"guide_title,omitempty"`
	LimitCloseSecond     uint32   `protobuf:"varint,25,opt,name=limit_close_second,json=limitCloseSecond,proto3" json:"limit_close_second,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LiveConfig) Reset()         { *m = LiveConfig{} }
func (m *LiveConfig) String() string { return proto.CompactTextString(m) }
func (*LiveConfig) ProtoMessage()    {}
func (*LiveConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{2}
}
func (m *LiveConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveConfig.Unmarshal(m, b)
}
func (m *LiveConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveConfig.Marshal(b, m, deterministic)
}
func (dst *LiveConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveConfig.Merge(dst, src)
}
func (m *LiveConfig) XXX_Size() int {
	return xxx_messageInfo_LiveConfig.Size(m)
}
func (m *LiveConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LiveConfig proto.InternalMessageInfo

func (m *LiveConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LiveConfig) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *LiveConfig) GetAppList() []App {
	if m != nil {
		return m.AppList
	}
	return nil
}

func (m *LiveConfig) GetPlatform() Platform {
	if m != nil {
		return m.Platform
	}
	return Platform_PlatformAll
}

func (m *LiveConfig) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *LiveConfig) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *LiveConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *LiveConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *LiveConfig) GetUserType() UserType {
	if m != nil {
		return m.UserType
	}
	return UserType_UserTypeAll
}

func (m *LiveConfig) GetOwnerList() []*ChannelOwner {
	if m != nil {
		return m.OwnerList
	}
	return nil
}

func (m *LiveConfig) GetLiveLink() string {
	if m != nil {
		return m.LiveLink
	}
	return ""
}

func (m *LiveConfig) GetEntranceText() string {
	if m != nil {
		return m.EntranceText
	}
	return ""
}

func (m *LiveConfig) GetEntrancePic() string {
	if m != nil {
		return m.EntrancePic
	}
	return ""
}

func (m *LiveConfig) GetFloatPic() string {
	if m != nil {
		return m.FloatPic
	}
	return ""
}

func (m *LiveConfig) GetVideoTitle() string {
	if m != nil {
		return m.VideoTitle
	}
	return ""
}

func (m *LiveConfig) GetVideoLength() uint32 {
	if m != nil {
		return m.VideoLength
	}
	return 0
}

func (m *LiveConfig) GetVideoWidth() uint32 {
	if m != nil {
		return m.VideoWidth
	}
	return 0
}

func (m *LiveConfig) GetAdsList() []*AdsConfig {
	if m != nil {
		return m.AdsList
	}
	return nil
}

func (m *LiveConfig) GetStatus() ConfStatus {
	if m != nil {
		return m.Status
	}
	return ConfStatus_ConfStatusNone
}

func (m *LiveConfig) GetCrowedPkgId() string {
	if m != nil {
		return m.CrowedPkgId
	}
	return ""
}

func (m *LiveConfig) GetPlayFormat() LiveConfig_PlayFormat {
	if m != nil {
		return m.PlayFormat
	}
	return LiveConfig_PlayFormatOther
}

func (m *LiveConfig) GetHasStrongGuide() bool {
	if m != nil {
		return m.HasStrongGuide
	}
	return false
}

func (m *LiveConfig) GetGuideGif() string {
	if m != nil {
		return m.GuideGif
	}
	return ""
}

func (m *LiveConfig) GetGuideTitle() string {
	if m != nil {
		return m.GuideTitle
	}
	return ""
}

func (m *LiveConfig) GetLimitCloseSecond() uint32 {
	if m != nil {
		return m.LimitCloseSecond
	}
	return 0
}

// 直播状态
type LiveStatus struct {
	LiveStatus           UGCLiveTogetherStatus `protobuf:"varint,3,opt,name=live_status,json=liveStatus,proto3,enum=ugc_live_together.UGCLiveTogetherStatus" json:"live_status,omitempty"`
	StatusChangeAt       uint32                `protobuf:"varint,4,opt,name=status_change_at,json=statusChangeAt,proto3" json:"status_change_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *LiveStatus) Reset()         { *m = LiveStatus{} }
func (m *LiveStatus) String() string { return proto.CompactTextString(m) }
func (*LiveStatus) ProtoMessage()    {}
func (*LiveStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{3}
}
func (m *LiveStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveStatus.Unmarshal(m, b)
}
func (m *LiveStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveStatus.Marshal(b, m, deterministic)
}
func (dst *LiveStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveStatus.Merge(dst, src)
}
func (m *LiveStatus) XXX_Size() int {
	return xxx_messageInfo_LiveStatus.Size(m)
}
func (m *LiveStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveStatus.DiscardUnknown(m)
}

var xxx_messageInfo_LiveStatus proto.InternalMessageInfo

func (m *LiveStatus) GetLiveStatus() UGCLiveTogetherStatus {
	if m != nil {
		return m.LiveStatus
	}
	return UGCLiveTogetherStatus_NONE
}

func (m *LiveStatus) GetStatusChangeAt() uint32 {
	if m != nil {
		return m.StatusChangeAt
	}
	return 0
}

// 气泡配置
type BubbleConfig struct {
	Id        uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UpdatedAt uint32 `protobuf:"varint,2,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 产品
	AppList []App `protobuf:"varint,3,rep,packed,name=app_list,json=appList,proto3,enum=ugc_live_together.App" json:"app_list,omitempty"`
	// 平台
	Platform Platform `protobuf:"varint,4,opt,name=platform,proto3,enum=ugc_live_together.Platform" json:"platform,omitempty"`
	// 房间主题id
	TabId uint32 `protobuf:"varint,5,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	// 房间主题名称
	TabName string `protobuf:"bytes,11,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	// 生效开始时间
	BeginAt uint32 `protobuf:"varint,6,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	// 生效结束时间
	EndAt uint32 `protobuf:"varint,7,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	// 展示对象
	UserType UserType `protobuf:"varint,8,opt,name=user_type,json=userType,proto3,enum=ugc_live_together.UserType" json:"user_type,omitempty"`
	// 房主信息
	OwnerList []*ChannelOwner `protobuf:"bytes,9,rep,name=owner_list,json=ownerList,proto3" json:"owner_list,omitempty"`
	// 气泡文案
	Text string `protobuf:"bytes,10,opt,name=text,proto3" json:"text,omitempty"`
	// 配置状态
	Status ConfStatus `protobuf:"varint,18,opt,name=status,proto3,enum=ugc_live_together.ConfStatus" json:"status,omitempty"`
	// 人群包id
	CrowedPkgId          string   `protobuf:"bytes,19,opt,name=crowed_pkg_id,json=crowedPkgId,proto3" json:"crowed_pkg_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BubbleConfig) Reset()         { *m = BubbleConfig{} }
func (m *BubbleConfig) String() string { return proto.CompactTextString(m) }
func (*BubbleConfig) ProtoMessage()    {}
func (*BubbleConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{4}
}
func (m *BubbleConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BubbleConfig.Unmarshal(m, b)
}
func (m *BubbleConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BubbleConfig.Marshal(b, m, deterministic)
}
func (dst *BubbleConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BubbleConfig.Merge(dst, src)
}
func (m *BubbleConfig) XXX_Size() int {
	return xxx_messageInfo_BubbleConfig.Size(m)
}
func (m *BubbleConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_BubbleConfig.DiscardUnknown(m)
}

var xxx_messageInfo_BubbleConfig proto.InternalMessageInfo

func (m *BubbleConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BubbleConfig) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *BubbleConfig) GetAppList() []App {
	if m != nil {
		return m.AppList
	}
	return nil
}

func (m *BubbleConfig) GetPlatform() Platform {
	if m != nil {
		return m.Platform
	}
	return Platform_PlatformAll
}

func (m *BubbleConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *BubbleConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *BubbleConfig) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *BubbleConfig) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *BubbleConfig) GetUserType() UserType {
	if m != nil {
		return m.UserType
	}
	return UserType_UserTypeAll
}

func (m *BubbleConfig) GetOwnerList() []*ChannelOwner {
	if m != nil {
		return m.OwnerList
	}
	return nil
}

func (m *BubbleConfig) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func (m *BubbleConfig) GetStatus() ConfStatus {
	if m != nil {
		return m.Status
	}
	return ConfStatus_ConfStatusNone
}

func (m *BubbleConfig) GetCrowedPkgId() string {
	if m != nil {
		return m.CrowedPkgId
	}
	return ""
}

type UpsertLiveConfigReq struct {
	Config               *LiveConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpsertLiveConfigReq) Reset()         { *m = UpsertLiveConfigReq{} }
func (m *UpsertLiveConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertLiveConfigReq) ProtoMessage()    {}
func (*UpsertLiveConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{5}
}
func (m *UpsertLiveConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertLiveConfigReq.Unmarshal(m, b)
}
func (m *UpsertLiveConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertLiveConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertLiveConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertLiveConfigReq.Merge(dst, src)
}
func (m *UpsertLiveConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertLiveConfigReq.Size(m)
}
func (m *UpsertLiveConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertLiveConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertLiveConfigReq proto.InternalMessageInfo

func (m *UpsertLiveConfigReq) GetConfig() *LiveConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertLiveConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertLiveConfigResp) Reset()         { *m = UpsertLiveConfigResp{} }
func (m *UpsertLiveConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertLiveConfigResp) ProtoMessage()    {}
func (*UpsertLiveConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{6}
}
func (m *UpsertLiveConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertLiveConfigResp.Unmarshal(m, b)
}
func (m *UpsertLiveConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertLiveConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertLiveConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertLiveConfigResp.Merge(dst, src)
}
func (m *UpsertLiveConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertLiveConfigResp.Size(m)
}
func (m *UpsertLiveConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertLiveConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertLiveConfigResp proto.InternalMessageInfo

type DelLiveConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLiveConfigReq) Reset()         { *m = DelLiveConfigReq{} }
func (m *DelLiveConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelLiveConfigReq) ProtoMessage()    {}
func (*DelLiveConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{7}
}
func (m *DelLiveConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLiveConfigReq.Unmarshal(m, b)
}
func (m *DelLiveConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLiveConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelLiveConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLiveConfigReq.Merge(dst, src)
}
func (m *DelLiveConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelLiveConfigReq.Size(m)
}
func (m *DelLiveConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLiveConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelLiveConfigReq proto.InternalMessageInfo

func (m *DelLiveConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelLiveConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelLiveConfigResp) Reset()         { *m = DelLiveConfigResp{} }
func (m *DelLiveConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelLiveConfigResp) ProtoMessage()    {}
func (*DelLiveConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{8}
}
func (m *DelLiveConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelLiveConfigResp.Unmarshal(m, b)
}
func (m *DelLiveConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelLiveConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelLiveConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelLiveConfigResp.Merge(dst, src)
}
func (m *DelLiveConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelLiveConfigResp.Size(m)
}
func (m *DelLiveConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelLiveConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelLiveConfigResp proto.InternalMessageInfo

type GetLiveConfigListReq struct {
	// 生效开始时间
	BeginAt uint32 `protobuf:"varint,1,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	// 生效结束时间
	EndAt uint32 `protobuf:"varint,2,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	// 生效状态
	Status ConfStatus `protobuf:"varint,3,opt,name=status,proto3,enum=ugc_live_together.ConfStatus" json:"status,omitempty"`
	// 推送产品
	AppList []App `protobuf:"varint,4,rep,packed,name=app_list,json=appList,proto3,enum=ugc_live_together.App" json:"app_list,omitempty"`
	// 房间主题id
	TabIds []uint32 `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	// 直播链接
	LiveLink             string   `protobuf:"bytes,6,opt,name=live_link,json=liveLink,proto3" json:"live_link,omitempty"`
	Offset               uint32   `protobuf:"varint,7,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,8,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveConfigListReq) Reset()         { *m = GetLiveConfigListReq{} }
func (m *GetLiveConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveConfigListReq) ProtoMessage()    {}
func (*GetLiveConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{9}
}
func (m *GetLiveConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveConfigListReq.Unmarshal(m, b)
}
func (m *GetLiveConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveConfigListReq.Merge(dst, src)
}
func (m *GetLiveConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveConfigListReq.Size(m)
}
func (m *GetLiveConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveConfigListReq proto.InternalMessageInfo

func (m *GetLiveConfigListReq) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *GetLiveConfigListReq) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *GetLiveConfigListReq) GetStatus() ConfStatus {
	if m != nil {
		return m.Status
	}
	return ConfStatus_ConfStatusNone
}

func (m *GetLiveConfigListReq) GetAppList() []App {
	if m != nil {
		return m.AppList
	}
	return nil
}

func (m *GetLiveConfigListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetLiveConfigListReq) GetLiveLink() string {
	if m != nil {
		return m.LiveLink
	}
	return ""
}

func (m *GetLiveConfigListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetLiveConfigListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetLiveConfigListResp struct {
	List                 []*LiveConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetLiveConfigListResp) Reset()         { *m = GetLiveConfigListResp{} }
func (m *GetLiveConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveConfigListResp) ProtoMessage()    {}
func (*GetLiveConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{10}
}
func (m *GetLiveConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveConfigListResp.Unmarshal(m, b)
}
func (m *GetLiveConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveConfigListResp.Merge(dst, src)
}
func (m *GetLiveConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveConfigListResp.Size(m)
}
func (m *GetLiveConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveConfigListResp proto.InternalMessageInfo

func (m *GetLiveConfigListResp) GetList() []*LiveConfig {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetLiveConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type StopLiveReq struct {
	// 直播配置id
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopLiveReq) Reset()         { *m = StopLiveReq{} }
func (m *StopLiveReq) String() string { return proto.CompactTextString(m) }
func (*StopLiveReq) ProtoMessage()    {}
func (*StopLiveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{11}
}
func (m *StopLiveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopLiveReq.Unmarshal(m, b)
}
func (m *StopLiveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopLiveReq.Marshal(b, m, deterministic)
}
func (dst *StopLiveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopLiveReq.Merge(dst, src)
}
func (m *StopLiveReq) XXX_Size() int {
	return xxx_messageInfo_StopLiveReq.Size(m)
}
func (m *StopLiveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StopLiveReq.DiscardUnknown(m)
}

var xxx_messageInfo_StopLiveReq proto.InternalMessageInfo

func (m *StopLiveReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type StopLiveResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StopLiveResp) Reset()         { *m = StopLiveResp{} }
func (m *StopLiveResp) String() string { return proto.CompactTextString(m) }
func (*StopLiveResp) ProtoMessage()    {}
func (*StopLiveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{12}
}
func (m *StopLiveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StopLiveResp.Unmarshal(m, b)
}
func (m *StopLiveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StopLiveResp.Marshal(b, m, deterministic)
}
func (dst *StopLiveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StopLiveResp.Merge(dst, src)
}
func (m *StopLiveResp) XXX_Size() int {
	return xxx_messageInfo_StopLiveResp.Size(m)
}
func (m *StopLiveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StopLiveResp.DiscardUnknown(m)
}

var xxx_messageInfo_StopLiveResp proto.InternalMessageInfo

type BatchSetAdsConfigReq struct {
	// 关联的直播配置id
	LiveId               uint32       `protobuf:"varint,1,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	AdsList              []*AdsConfig `protobuf:"bytes,2,rep,name=ads_list,json=adsList,proto3" json:"ads_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchSetAdsConfigReq) Reset()         { *m = BatchSetAdsConfigReq{} }
func (m *BatchSetAdsConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetAdsConfigReq) ProtoMessage()    {}
func (*BatchSetAdsConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{13}
}
func (m *BatchSetAdsConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetAdsConfigReq.Unmarshal(m, b)
}
func (m *BatchSetAdsConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetAdsConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetAdsConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetAdsConfigReq.Merge(dst, src)
}
func (m *BatchSetAdsConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetAdsConfigReq.Size(m)
}
func (m *BatchSetAdsConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetAdsConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetAdsConfigReq proto.InternalMessageInfo

func (m *BatchSetAdsConfigReq) GetLiveId() uint32 {
	if m != nil {
		return m.LiveId
	}
	return 0
}

func (m *BatchSetAdsConfigReq) GetAdsList() []*AdsConfig {
	if m != nil {
		return m.AdsList
	}
	return nil
}

type BatchSetAdsConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetAdsConfigResp) Reset()         { *m = BatchSetAdsConfigResp{} }
func (m *BatchSetAdsConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetAdsConfigResp) ProtoMessage()    {}
func (*BatchSetAdsConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{14}
}
func (m *BatchSetAdsConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetAdsConfigResp.Unmarshal(m, b)
}
func (m *BatchSetAdsConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetAdsConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetAdsConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetAdsConfigResp.Merge(dst, src)
}
func (m *BatchSetAdsConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetAdsConfigResp.Size(m)
}
func (m *BatchSetAdsConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetAdsConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetAdsConfigResp proto.InternalMessageInfo

type GetLiveAdsConfigListReq struct {
	LiveId               uint32   `protobuf:"varint,1,opt,name=live_id,json=liveId,proto3" json:"live_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLiveAdsConfigListReq) Reset()         { *m = GetLiveAdsConfigListReq{} }
func (m *GetLiveAdsConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetLiveAdsConfigListReq) ProtoMessage()    {}
func (*GetLiveAdsConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{15}
}
func (m *GetLiveAdsConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAdsConfigListReq.Unmarshal(m, b)
}
func (m *GetLiveAdsConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAdsConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetLiveAdsConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAdsConfigListReq.Merge(dst, src)
}
func (m *GetLiveAdsConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetLiveAdsConfigListReq.Size(m)
}
func (m *GetLiveAdsConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAdsConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAdsConfigListReq proto.InternalMessageInfo

func (m *GetLiveAdsConfigListReq) GetLiveId() uint32 {
	if m != nil {
		return m.LiveId
	}
	return 0
}

type GetLiveAdsConfigListResp struct {
	List                 []*AdsConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetLiveAdsConfigListResp) Reset()         { *m = GetLiveAdsConfigListResp{} }
func (m *GetLiveAdsConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetLiveAdsConfigListResp) ProtoMessage()    {}
func (*GetLiveAdsConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{16}
}
func (m *GetLiveAdsConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLiveAdsConfigListResp.Unmarshal(m, b)
}
func (m *GetLiveAdsConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLiveAdsConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetLiveAdsConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLiveAdsConfigListResp.Merge(dst, src)
}
func (m *GetLiveAdsConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetLiveAdsConfigListResp.Size(m)
}
func (m *GetLiveAdsConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLiveAdsConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLiveAdsConfigListResp proto.InternalMessageInfo

func (m *GetLiveAdsConfigListResp) GetList() []*AdsConfig {
	if m != nil {
		return m.List
	}
	return nil
}

type PushAdsReq struct {
	Op PushAdsReq_Op `protobuf:"varint,1,opt,name=op,proto3,enum=ugc_live_together.PushAdsReq_Op" json:"op,omitempty"`
	// 插播广告配置id
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushAdsReq) Reset()         { *m = PushAdsReq{} }
func (m *PushAdsReq) String() string { return proto.CompactTextString(m) }
func (*PushAdsReq) ProtoMessage()    {}
func (*PushAdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{17}
}
func (m *PushAdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushAdsReq.Unmarshal(m, b)
}
func (m *PushAdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushAdsReq.Marshal(b, m, deterministic)
}
func (dst *PushAdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushAdsReq.Merge(dst, src)
}
func (m *PushAdsReq) XXX_Size() int {
	return xxx_messageInfo_PushAdsReq.Size(m)
}
func (m *PushAdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushAdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushAdsReq proto.InternalMessageInfo

func (m *PushAdsReq) GetOp() PushAdsReq_Op {
	if m != nil {
		return m.Op
	}
	return PushAdsReq_OpNone
}

func (m *PushAdsReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type PushAdsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushAdsResp) Reset()         { *m = PushAdsResp{} }
func (m *PushAdsResp) String() string { return proto.CompactTextString(m) }
func (*PushAdsResp) ProtoMessage()    {}
func (*PushAdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{18}
}
func (m *PushAdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushAdsResp.Unmarshal(m, b)
}
func (m *PushAdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushAdsResp.Marshal(b, m, deterministic)
}
func (dst *PushAdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushAdsResp.Merge(dst, src)
}
func (m *PushAdsResp) XXX_Size() int {
	return xxx_messageInfo_PushAdsResp.Size(m)
}
func (m *PushAdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PushAdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_PushAdsResp proto.InternalMessageInfo

type UpsertBubbleConfigReq struct {
	Config               *BubbleConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpsertBubbleConfigReq) Reset()         { *m = UpsertBubbleConfigReq{} }
func (m *UpsertBubbleConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertBubbleConfigReq) ProtoMessage()    {}
func (*UpsertBubbleConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{19}
}
func (m *UpsertBubbleConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBubbleConfigReq.Unmarshal(m, b)
}
func (m *UpsertBubbleConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBubbleConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertBubbleConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBubbleConfigReq.Merge(dst, src)
}
func (m *UpsertBubbleConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertBubbleConfigReq.Size(m)
}
func (m *UpsertBubbleConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBubbleConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBubbleConfigReq proto.InternalMessageInfo

func (m *UpsertBubbleConfigReq) GetConfig() *BubbleConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertBubbleConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertBubbleConfigResp) Reset()         { *m = UpsertBubbleConfigResp{} }
func (m *UpsertBubbleConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertBubbleConfigResp) ProtoMessage()    {}
func (*UpsertBubbleConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{20}
}
func (m *UpsertBubbleConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertBubbleConfigResp.Unmarshal(m, b)
}
func (m *UpsertBubbleConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertBubbleConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertBubbleConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertBubbleConfigResp.Merge(dst, src)
}
func (m *UpsertBubbleConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertBubbleConfigResp.Size(m)
}
func (m *UpsertBubbleConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertBubbleConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertBubbleConfigResp proto.InternalMessageInfo

type DelBubbleConfigReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBubbleConfigReq) Reset()         { *m = DelBubbleConfigReq{} }
func (m *DelBubbleConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelBubbleConfigReq) ProtoMessage()    {}
func (*DelBubbleConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{21}
}
func (m *DelBubbleConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBubbleConfigReq.Unmarshal(m, b)
}
func (m *DelBubbleConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBubbleConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelBubbleConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBubbleConfigReq.Merge(dst, src)
}
func (m *DelBubbleConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelBubbleConfigReq.Size(m)
}
func (m *DelBubbleConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBubbleConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelBubbleConfigReq proto.InternalMessageInfo

func (m *DelBubbleConfigReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DelBubbleConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelBubbleConfigResp) Reset()         { *m = DelBubbleConfigResp{} }
func (m *DelBubbleConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelBubbleConfigResp) ProtoMessage()    {}
func (*DelBubbleConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{22}
}
func (m *DelBubbleConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelBubbleConfigResp.Unmarshal(m, b)
}
func (m *DelBubbleConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelBubbleConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelBubbleConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelBubbleConfigResp.Merge(dst, src)
}
func (m *DelBubbleConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelBubbleConfigResp.Size(m)
}
func (m *DelBubbleConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelBubbleConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelBubbleConfigResp proto.InternalMessageInfo

type GetBubbleConfigListReq struct {
	// 生效开始时间
	BeginAt uint32 `protobuf:"varint,1,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	// 生效结束时间
	EndAt uint32 `protobuf:"varint,2,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	// 配置状态
	Status ConfStatus `protobuf:"varint,3,opt,name=status,proto3,enum=ugc_live_together.ConfStatus" json:"status,omitempty"`
	// 推送产品
	AppList []App `protobuf:"varint,4,rep,packed,name=app_list,json=appList,proto3,enum=ugc_live_together.App" json:"app_list,omitempty"`
	// 房间主题id
	TabIds               []uint32 `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Offset               uint32   `protobuf:"varint,6,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,7,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBubbleConfigListReq) Reset()         { *m = GetBubbleConfigListReq{} }
func (m *GetBubbleConfigListReq) String() string { return proto.CompactTextString(m) }
func (*GetBubbleConfigListReq) ProtoMessage()    {}
func (*GetBubbleConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{23}
}
func (m *GetBubbleConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBubbleConfigListReq.Unmarshal(m, b)
}
func (m *GetBubbleConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBubbleConfigListReq.Marshal(b, m, deterministic)
}
func (dst *GetBubbleConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBubbleConfigListReq.Merge(dst, src)
}
func (m *GetBubbleConfigListReq) XXX_Size() int {
	return xxx_messageInfo_GetBubbleConfigListReq.Size(m)
}
func (m *GetBubbleConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBubbleConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBubbleConfigListReq proto.InternalMessageInfo

func (m *GetBubbleConfigListReq) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *GetBubbleConfigListReq) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *GetBubbleConfigListReq) GetStatus() ConfStatus {
	if m != nil {
		return m.Status
	}
	return ConfStatus_ConfStatusNone
}

func (m *GetBubbleConfigListReq) GetAppList() []App {
	if m != nil {
		return m.AppList
	}
	return nil
}

func (m *GetBubbleConfigListReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *GetBubbleConfigListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBubbleConfigListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBubbleConfigListResp struct {
	List                 []*BubbleConfig `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetBubbleConfigListResp) Reset()         { *m = GetBubbleConfigListResp{} }
func (m *GetBubbleConfigListResp) String() string { return proto.CompactTextString(m) }
func (*GetBubbleConfigListResp) ProtoMessage()    {}
func (*GetBubbleConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{24}
}
func (m *GetBubbleConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBubbleConfigListResp.Unmarshal(m, b)
}
func (m *GetBubbleConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBubbleConfigListResp.Marshal(b, m, deterministic)
}
func (dst *GetBubbleConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBubbleConfigListResp.Merge(dst, src)
}
func (m *GetBubbleConfigListResp) XXX_Size() int {
	return xxx_messageInfo_GetBubbleConfigListResp.Size(m)
}
func (m *GetBubbleConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBubbleConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBubbleConfigListResp proto.InternalMessageInfo

func (m *GetBubbleConfigListResp) GetList() []*BubbleConfig {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetBubbleConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetChannelLiveInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Role                 uint32   `protobuf:"varint,3,opt,name=role,proto3" json:"role,omitempty"`
	App                  App      `protobuf:"varint,4,opt,name=app,proto3,enum=ugc_live_together.App" json:"app,omitempty"`
	Platform             Platform `protobuf:"varint,5,opt,name=platform,proto3,enum=ugc_live_together.Platform" json:"platform,omitempty"`
	OwnerUid             uint32   `protobuf:"varint,6,opt,name=owner_uid,json=ownerUid,proto3" json:"owner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelLiveInfoReq) Reset()         { *m = GetChannelLiveInfoReq{} }
func (m *GetChannelLiveInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveInfoReq) ProtoMessage()    {}
func (*GetChannelLiveInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{25}
}
func (m *GetChannelLiveInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveInfoReq.Unmarshal(m, b)
}
func (m *GetChannelLiveInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveInfoReq.Merge(dst, src)
}
func (m *GetChannelLiveInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveInfoReq.Size(m)
}
func (m *GetChannelLiveInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveInfoReq proto.InternalMessageInfo

func (m *GetChannelLiveInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelLiveInfoReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *GetChannelLiveInfoReq) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *GetChannelLiveInfoReq) GetApp() App {
	if m != nil {
		return m.App
	}
	return App_AppNone
}

func (m *GetChannelLiveInfoReq) GetPlatform() Platform {
	if m != nil {
		return m.Platform
	}
	return Platform_PlatformAll
}

func (m *GetChannelLiveInfoReq) GetOwnerUid() uint32 {
	if m != nil {
		return m.OwnerUid
	}
	return 0
}

type GetChannelLiveInfoResp struct {
	LiveConfig           *LiveConfig   `protobuf:"bytes,1,opt,name=live_config,json=liveConfig,proto3" json:"live_config,omitempty"`
	AdsConfig            *AdsConfig    `protobuf:"bytes,2,opt,name=ads_config,json=adsConfig,proto3" json:"ads_config,omitempty"`
	BubbleConfig         *BubbleConfig `protobuf:"bytes,3,opt,name=bubble_config,json=bubbleConfig,proto3" json:"bubble_config,omitempty"`
	LiveStatus           *LiveStatus   `protobuf:"bytes,4,opt,name=live_status,json=liveStatus,proto3" json:"live_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetChannelLiveInfoResp) Reset()         { *m = GetChannelLiveInfoResp{} }
func (m *GetChannelLiveInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelLiveInfoResp) ProtoMessage()    {}
func (*GetChannelLiveInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{26}
}
func (m *GetChannelLiveInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelLiveInfoResp.Unmarshal(m, b)
}
func (m *GetChannelLiveInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelLiveInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelLiveInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelLiveInfoResp.Merge(dst, src)
}
func (m *GetChannelLiveInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelLiveInfoResp.Size(m)
}
func (m *GetChannelLiveInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelLiveInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelLiveInfoResp proto.InternalMessageInfo

func (m *GetChannelLiveInfoResp) GetLiveConfig() *LiveConfig {
	if m != nil {
		return m.LiveConfig
	}
	return nil
}

func (m *GetChannelLiveInfoResp) GetAdsConfig() *AdsConfig {
	if m != nil {
		return m.AdsConfig
	}
	return nil
}

func (m *GetChannelLiveInfoResp) GetBubbleConfig() *BubbleConfig {
	if m != nil {
		return m.BubbleConfig
	}
	return nil
}

func (m *GetChannelLiveInfoResp) GetLiveStatus() *LiveStatus {
	if m != nil {
		return m.LiveStatus
	}
	return nil
}

type SetChannelLiveStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LiveStatus           uint32   `protobuf:"varint,2,opt,name=live_status,json=liveStatus,proto3" json:"live_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelLiveStatusReq) Reset()         { *m = SetChannelLiveStatusReq{} }
func (m *SetChannelLiveStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelLiveStatusReq) ProtoMessage()    {}
func (*SetChannelLiveStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{27}
}
func (m *SetChannelLiveStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelLiveStatusReq.Unmarshal(m, b)
}
func (m *SetChannelLiveStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelLiveStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelLiveStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelLiveStatusReq.Merge(dst, src)
}
func (m *SetChannelLiveStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelLiveStatusReq.Size(m)
}
func (m *SetChannelLiveStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelLiveStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelLiveStatusReq proto.InternalMessageInfo

func (m *SetChannelLiveStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelLiveStatusReq) GetLiveStatus() uint32 {
	if m != nil {
		return m.LiveStatus
	}
	return 0
}

type SetChannelLiveStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelLiveStatusResp) Reset()         { *m = SetChannelLiveStatusResp{} }
func (m *SetChannelLiveStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelLiveStatusResp) ProtoMessage()    {}
func (*SetChannelLiveStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ugc_live_together_38c49833ad7a732b, []int{28}
}
func (m *SetChannelLiveStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelLiveStatusResp.Unmarshal(m, b)
}
func (m *SetChannelLiveStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelLiveStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelLiveStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelLiveStatusResp.Merge(dst, src)
}
func (m *SetChannelLiveStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelLiveStatusResp.Size(m)
}
func (m *SetChannelLiveStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelLiveStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelLiveStatusResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*ChannelOwner)(nil), "ugc_live_together.ChannelOwner")
	proto.RegisterType((*AdsConfig)(nil), "ugc_live_together.AdsConfig")
	proto.RegisterType((*LiveConfig)(nil), "ugc_live_together.LiveConfig")
	proto.RegisterType((*LiveStatus)(nil), "ugc_live_together.LiveStatus")
	proto.RegisterType((*BubbleConfig)(nil), "ugc_live_together.BubbleConfig")
	proto.RegisterType((*UpsertLiveConfigReq)(nil), "ugc_live_together.UpsertLiveConfigReq")
	proto.RegisterType((*UpsertLiveConfigResp)(nil), "ugc_live_together.UpsertLiveConfigResp")
	proto.RegisterType((*DelLiveConfigReq)(nil), "ugc_live_together.DelLiveConfigReq")
	proto.RegisterType((*DelLiveConfigResp)(nil), "ugc_live_together.DelLiveConfigResp")
	proto.RegisterType((*GetLiveConfigListReq)(nil), "ugc_live_together.GetLiveConfigListReq")
	proto.RegisterType((*GetLiveConfigListResp)(nil), "ugc_live_together.GetLiveConfigListResp")
	proto.RegisterType((*StopLiveReq)(nil), "ugc_live_together.StopLiveReq")
	proto.RegisterType((*StopLiveResp)(nil), "ugc_live_together.StopLiveResp")
	proto.RegisterType((*BatchSetAdsConfigReq)(nil), "ugc_live_together.BatchSetAdsConfigReq")
	proto.RegisterType((*BatchSetAdsConfigResp)(nil), "ugc_live_together.BatchSetAdsConfigResp")
	proto.RegisterType((*GetLiveAdsConfigListReq)(nil), "ugc_live_together.GetLiveAdsConfigListReq")
	proto.RegisterType((*GetLiveAdsConfigListResp)(nil), "ugc_live_together.GetLiveAdsConfigListResp")
	proto.RegisterType((*PushAdsReq)(nil), "ugc_live_together.PushAdsReq")
	proto.RegisterType((*PushAdsResp)(nil), "ugc_live_together.PushAdsResp")
	proto.RegisterType((*UpsertBubbleConfigReq)(nil), "ugc_live_together.UpsertBubbleConfigReq")
	proto.RegisterType((*UpsertBubbleConfigResp)(nil), "ugc_live_together.UpsertBubbleConfigResp")
	proto.RegisterType((*DelBubbleConfigReq)(nil), "ugc_live_together.DelBubbleConfigReq")
	proto.RegisterType((*DelBubbleConfigResp)(nil), "ugc_live_together.DelBubbleConfigResp")
	proto.RegisterType((*GetBubbleConfigListReq)(nil), "ugc_live_together.GetBubbleConfigListReq")
	proto.RegisterType((*GetBubbleConfigListResp)(nil), "ugc_live_together.GetBubbleConfigListResp")
	proto.RegisterType((*GetChannelLiveInfoReq)(nil), "ugc_live_together.GetChannelLiveInfoReq")
	proto.RegisterType((*GetChannelLiveInfoResp)(nil), "ugc_live_together.GetChannelLiveInfoResp")
	proto.RegisterType((*SetChannelLiveStatusReq)(nil), "ugc_live_together.SetChannelLiveStatusReq")
	proto.RegisterType((*SetChannelLiveStatusResp)(nil), "ugc_live_together.SetChannelLiveStatusResp")
	proto.RegisterEnum("ugc_live_together.App", App_name, App_value)
	proto.RegisterEnum("ugc_live_together.Platform", Platform_name, Platform_value)
	proto.RegisterEnum("ugc_live_together.UserType", UserType_name, UserType_value)
	proto.RegisterEnum("ugc_live_together.ConfStatus", ConfStatus_name, ConfStatus_value)
	proto.RegisterEnum("ugc_live_together.UGCLiveTogetherStatus", UGCLiveTogetherStatus_name, UGCLiveTogetherStatus_value)
	proto.RegisterEnum("ugc_live_together.AdsConfig_PushState", AdsConfig_PushState_name, AdsConfig_PushState_value)
	proto.RegisterEnum("ugc_live_together.LiveConfig_PlayFormat", LiveConfig_PlayFormat_name, LiveConfig_PlayFormat_value)
	proto.RegisterEnum("ugc_live_together.PushAdsReq_Op", PushAdsReq_Op_name, PushAdsReq_Op_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UgcLiveTogetherClient is the client API for UgcLiveTogether service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UgcLiveTogetherClient interface {
	// 创建/修改直播配置
	UpsertLiveConfig(ctx context.Context, in *UpsertLiveConfigReq, opts ...grpc.CallOption) (*UpsertLiveConfigResp, error)
	// 删除直播配置
	DelLiveConfig(ctx context.Context, in *DelLiveConfigReq, opts ...grpc.CallOption) (*DelLiveConfigResp, error)
	// 获取直播配置列表
	GetLiveConfigList(ctx context.Context, in *GetLiveConfigListReq, opts ...grpc.CallOption) (*GetLiveConfigListResp, error)
	// 停止直播
	StopLive(ctx context.Context, in *StopLiveReq, opts ...grpc.CallOption) (*StopLiveResp, error)
	// 批量设置插播广告
	BatchSetAdsConfig(ctx context.Context, in *BatchSetAdsConfigReq, opts ...grpc.CallOption) (*BatchSetAdsConfigResp, error)
	// 获取直播的广告列表
	GetLiveAdsConfigList(ctx context.Context, in *GetLiveAdsConfigListReq, opts ...grpc.CallOption) (*GetLiveAdsConfigListResp, error)
	// 立即/取消推送插播广告
	PushAds(ctx context.Context, in *PushAdsReq, opts ...grpc.CallOption) (*PushAdsResp, error)
	// 创建/修改气泡配置
	UpsertBubbleConfig(ctx context.Context, in *UpsertBubbleConfigReq, opts ...grpc.CallOption) (*UpsertBubbleConfigResp, error)
	// 删除气泡配置
	DelBubbleConfig(ctx context.Context, in *DelBubbleConfigReq, opts ...grpc.CallOption) (*DelBubbleConfigResp, error)
	// 获取气泡配置列表
	GetBubbleConfigList(ctx context.Context, in *GetBubbleConfigListReq, opts ...grpc.CallOption) (*GetBubbleConfigListResp, error)
	// 获取房间直播配置
	GetChannelLiveInfo(ctx context.Context, in *GetChannelLiveInfoReq, opts ...grpc.CallOption) (*GetChannelLiveInfoResp, error)
	// 设置房间直播状态
	SetChannelLiveStatus(ctx context.Context, in *SetChannelLiveStatusReq, opts ...grpc.CallOption) (*SetChannelLiveStatusResp, error)
}

type ugcLiveTogetherClient struct {
	cc *grpc.ClientConn
}

func NewUgcLiveTogetherClient(cc *grpc.ClientConn) UgcLiveTogetherClient {
	return &ugcLiveTogetherClient{cc}
}

func (c *ugcLiveTogetherClient) UpsertLiveConfig(ctx context.Context, in *UpsertLiveConfigReq, opts ...grpc.CallOption) (*UpsertLiveConfigResp, error) {
	out := new(UpsertLiveConfigResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/UpsertLiveConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) DelLiveConfig(ctx context.Context, in *DelLiveConfigReq, opts ...grpc.CallOption) (*DelLiveConfigResp, error) {
	out := new(DelLiveConfigResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/DelLiveConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) GetLiveConfigList(ctx context.Context, in *GetLiveConfigListReq, opts ...grpc.CallOption) (*GetLiveConfigListResp, error) {
	out := new(GetLiveConfigListResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/GetLiveConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) StopLive(ctx context.Context, in *StopLiveReq, opts ...grpc.CallOption) (*StopLiveResp, error) {
	out := new(StopLiveResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/StopLive", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) BatchSetAdsConfig(ctx context.Context, in *BatchSetAdsConfigReq, opts ...grpc.CallOption) (*BatchSetAdsConfigResp, error) {
	out := new(BatchSetAdsConfigResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/BatchSetAdsConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) GetLiveAdsConfigList(ctx context.Context, in *GetLiveAdsConfigListReq, opts ...grpc.CallOption) (*GetLiveAdsConfigListResp, error) {
	out := new(GetLiveAdsConfigListResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/GetLiveAdsConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) PushAds(ctx context.Context, in *PushAdsReq, opts ...grpc.CallOption) (*PushAdsResp, error) {
	out := new(PushAdsResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/PushAds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) UpsertBubbleConfig(ctx context.Context, in *UpsertBubbleConfigReq, opts ...grpc.CallOption) (*UpsertBubbleConfigResp, error) {
	out := new(UpsertBubbleConfigResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/UpsertBubbleConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) DelBubbleConfig(ctx context.Context, in *DelBubbleConfigReq, opts ...grpc.CallOption) (*DelBubbleConfigResp, error) {
	out := new(DelBubbleConfigResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/DelBubbleConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) GetBubbleConfigList(ctx context.Context, in *GetBubbleConfigListReq, opts ...grpc.CallOption) (*GetBubbleConfigListResp, error) {
	out := new(GetBubbleConfigListResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/GetBubbleConfigList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) GetChannelLiveInfo(ctx context.Context, in *GetChannelLiveInfoReq, opts ...grpc.CallOption) (*GetChannelLiveInfoResp, error) {
	out := new(GetChannelLiveInfoResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/GetChannelLiveInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ugcLiveTogetherClient) SetChannelLiveStatus(ctx context.Context, in *SetChannelLiveStatusReq, opts ...grpc.CallOption) (*SetChannelLiveStatusResp, error) {
	out := new(SetChannelLiveStatusResp)
	err := c.cc.Invoke(ctx, "/ugc_live_together.UgcLiveTogether/SetChannelLiveStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UgcLiveTogetherServer is the server API for UgcLiveTogether service.
type UgcLiveTogetherServer interface {
	// 创建/修改直播配置
	UpsertLiveConfig(context.Context, *UpsertLiveConfigReq) (*UpsertLiveConfigResp, error)
	// 删除直播配置
	DelLiveConfig(context.Context, *DelLiveConfigReq) (*DelLiveConfigResp, error)
	// 获取直播配置列表
	GetLiveConfigList(context.Context, *GetLiveConfigListReq) (*GetLiveConfigListResp, error)
	// 停止直播
	StopLive(context.Context, *StopLiveReq) (*StopLiveResp, error)
	// 批量设置插播广告
	BatchSetAdsConfig(context.Context, *BatchSetAdsConfigReq) (*BatchSetAdsConfigResp, error)
	// 获取直播的广告列表
	GetLiveAdsConfigList(context.Context, *GetLiveAdsConfigListReq) (*GetLiveAdsConfigListResp, error)
	// 立即/取消推送插播广告
	PushAds(context.Context, *PushAdsReq) (*PushAdsResp, error)
	// 创建/修改气泡配置
	UpsertBubbleConfig(context.Context, *UpsertBubbleConfigReq) (*UpsertBubbleConfigResp, error)
	// 删除气泡配置
	DelBubbleConfig(context.Context, *DelBubbleConfigReq) (*DelBubbleConfigResp, error)
	// 获取气泡配置列表
	GetBubbleConfigList(context.Context, *GetBubbleConfigListReq) (*GetBubbleConfigListResp, error)
	// 获取房间直播配置
	GetChannelLiveInfo(context.Context, *GetChannelLiveInfoReq) (*GetChannelLiveInfoResp, error)
	// 设置房间直播状态
	SetChannelLiveStatus(context.Context, *SetChannelLiveStatusReq) (*SetChannelLiveStatusResp, error)
}

func RegisterUgcLiveTogetherServer(s *grpc.Server, srv UgcLiveTogetherServer) {
	s.RegisterService(&_UgcLiveTogether_serviceDesc, srv)
}

func _UgcLiveTogether_UpsertLiveConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertLiveConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).UpsertLiveConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/UpsertLiveConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).UpsertLiveConfig(ctx, req.(*UpsertLiveConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_DelLiveConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelLiveConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).DelLiveConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/DelLiveConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).DelLiveConfig(ctx, req.(*DelLiveConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_GetLiveConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).GetLiveConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/GetLiveConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).GetLiveConfigList(ctx, req.(*GetLiveConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_StopLive_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopLiveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).StopLive(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/StopLive",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).StopLive(ctx, req.(*StopLiveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_BatchSetAdsConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetAdsConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).BatchSetAdsConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/BatchSetAdsConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).BatchSetAdsConfig(ctx, req.(*BatchSetAdsConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_GetLiveAdsConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLiveAdsConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).GetLiveAdsConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/GetLiveAdsConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).GetLiveAdsConfigList(ctx, req.(*GetLiveAdsConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_PushAds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushAdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).PushAds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/PushAds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).PushAds(ctx, req.(*PushAdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_UpsertBubbleConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertBubbleConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).UpsertBubbleConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/UpsertBubbleConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).UpsertBubbleConfig(ctx, req.(*UpsertBubbleConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_DelBubbleConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelBubbleConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).DelBubbleConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/DelBubbleConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).DelBubbleConfig(ctx, req.(*DelBubbleConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_GetBubbleConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBubbleConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).GetBubbleConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/GetBubbleConfigList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).GetBubbleConfigList(ctx, req.(*GetBubbleConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_GetChannelLiveInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelLiveInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).GetChannelLiveInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/GetChannelLiveInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).GetChannelLiveInfo(ctx, req.(*GetChannelLiveInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UgcLiveTogether_SetChannelLiveStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelLiveStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UgcLiveTogetherServer).SetChannelLiveStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/ugc_live_together.UgcLiveTogether/SetChannelLiveStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UgcLiveTogetherServer).SetChannelLiveStatus(ctx, req.(*SetChannelLiveStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UgcLiveTogether_serviceDesc = grpc.ServiceDesc{
	ServiceName: "ugc_live_together.UgcLiveTogether",
	HandlerType: (*UgcLiveTogetherServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpsertLiveConfig",
			Handler:    _UgcLiveTogether_UpsertLiveConfig_Handler,
		},
		{
			MethodName: "DelLiveConfig",
			Handler:    _UgcLiveTogether_DelLiveConfig_Handler,
		},
		{
			MethodName: "GetLiveConfigList",
			Handler:    _UgcLiveTogether_GetLiveConfigList_Handler,
		},
		{
			MethodName: "StopLive",
			Handler:    _UgcLiveTogether_StopLive_Handler,
		},
		{
			MethodName: "BatchSetAdsConfig",
			Handler:    _UgcLiveTogether_BatchSetAdsConfig_Handler,
		},
		{
			MethodName: "GetLiveAdsConfigList",
			Handler:    _UgcLiveTogether_GetLiveAdsConfigList_Handler,
		},
		{
			MethodName: "PushAds",
			Handler:    _UgcLiveTogether_PushAds_Handler,
		},
		{
			MethodName: "UpsertBubbleConfig",
			Handler:    _UgcLiveTogether_UpsertBubbleConfig_Handler,
		},
		{
			MethodName: "DelBubbleConfig",
			Handler:    _UgcLiveTogether_DelBubbleConfig_Handler,
		},
		{
			MethodName: "GetBubbleConfigList",
			Handler:    _UgcLiveTogether_GetBubbleConfigList_Handler,
		},
		{
			MethodName: "GetChannelLiveInfo",
			Handler:    _UgcLiveTogether_GetChannelLiveInfo_Handler,
		},
		{
			MethodName: "SetChannelLiveStatus",
			Handler:    _UgcLiveTogether_SetChannelLiveStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "ugc-live-together/ugc-live-together.proto",
}

func init() {
	proto.RegisterFile("ugc-live-together/ugc-live-together.proto", fileDescriptor_ugc_live_together_38c49833ad7a732b)
}

var fileDescriptor_ugc_live_together_38c49833ad7a732b = []byte{
	// 1852 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x58, 0x5f, 0x6f, 0xdb, 0xc8,
	0x11, 0xb7, 0x28, 0x5b, 0x7f, 0x46, 0x96, 0x42, 0xaf, 0xff, 0xf1, 0x94, 0xfa, 0xe2, 0xf2, 0xae,
	0x57, 0xc5, 0xed, 0x39, 0x77, 0x0e, 0xd2, 0x2b, 0xd0, 0x22, 0x80, 0xe2, 0xa4, 0x3e, 0xa5, 0x3e,
	0xcb, 0x90, 0x6c, 0x14, 0x57, 0x14, 0xd0, 0x51, 0xe4, 0x4a, 0x5a, 0x98, 0x26, 0x37, 0xda, 0x95,
	0x93, 0xf4, 0xad, 0x5f, 0xa2, 0x28, 0xd0, 0x4f, 0xd6, 0xc7, 0x7e, 0x84, 0xbe, 0xf4, 0xa5, 0x2f,
	0xc5, 0xce, 0x92, 0x22, 0x25, 0x51, 0x8e, 0x02, 0x14, 0x28, 0xee, 0x6d, 0xf7, 0xb7, 0xb3, 0x33,
	0xcb, 0xdf, 0xce, 0xce, 0xfc, 0x24, 0x78, 0x3c, 0x19, 0xba, 0x5f, 0xfa, 0xec, 0x8e, 0x7e, 0x29,
	0xc3, 0x21, 0x95, 0x23, 0x3a, 0x7e, 0xb2, 0x80, 0x1c, 0xf3, 0x71, 0x28, 0x43, 0xb2, 0x35, 0x19,
	0xba, 0x3d, 0xb5, 0xd0, 0x8b, 0x17, 0xec, 0x1f, 0x60, 0xf3, 0x74, 0xe4, 0x04, 0x01, 0xf5, 0xdb,
	0x6f, 0x03, 0x3a, 0x26, 0x26, 0xe4, 0x27, 0xcc, 0xb3, 0x72, 0x87, 0xb9, 0x46, 0xb5, 0xa3, 0x86,
	0x0a, 0x71, 0x99, 0x67, 0x19, 0x1a, 0x71, 0x35, 0x22, 0x99, 0x67, 0xe5, 0x0f, 0x73, 0x8d, 0x72,
	0x47, 0x0d, 0x49, 0x1d, 0x4a, 0x01, 0x73, 0x6f, 0x02, 0xe7, 0x96, 0x5a, 0xeb, 0x08, 0x4f, 0xe7,
	0xf6, 0xbf, 0x72, 0x50, 0x6e, 0x7a, 0xe2, 0x34, 0x0c, 0x06, 0x6c, 0x48, 0x6a, 0x60, 0x4c, 0xdd,
	0x1b, 0xcc, 0x23, 0x04, 0xd6, 0x7d, 0x16, 0xdc, 0xa0, 0xfb, 0x72, 0x07, 0xc7, 0xe4, 0xb7, 0xb0,
	0x21, 0xa4, 0x23, 0x29, 0x46, 0xa8, 0x9d, 0x7c, 0x71, 0xbc, 0x70, 0xec, 0xe3, 0xa9, 0xc3, 0xe3,
	0xcb, 0x89, 0x18, 0x75, 0x95, 0x75, 0x47, 0x6f, 0x22, 0x07, 0x00, 0x13, 0xee, 0x39, 0x92, 0x7a,
	0x3d, 0x47, 0xe2, 0x69, 0xaa, 0x9d, 0x72, 0x84, 0x34, 0x25, 0xd9, 0x87, 0x22, 0xba, 0x62, 0x9e,
	0xb5, 0x81, 0x6b, 0x05, 0x35, 0x6d, 0x79, 0xf6, 0x6b, 0x28, 0x4f, 0x7d, 0x91, 0x2d, 0xa8, 0x4e,
	0x27, 0x17, 0x61, 0x40, 0xcd, 0xb5, 0x19, 0xa8, 0x15, 0x30, 0x69, 0xe6, 0xc8, 0x3e, 0x6c, 0x4f,
	0xa1, 0xcb, 0x71, 0xe8, 0x52, 0x21, 0x58, 0x30, 0x34, 0x0d, 0xfb, 0x6f, 0x25, 0x80, 0x73, 0x76,
	0x47, 0x97, 0x7c, 0xf4, 0xec, 0x11, 0x8d, 0xf9, 0x23, 0x7e, 0x0d, 0x25, 0x87, 0xf3, 0x9e, 0xcf,
	0x84, 0xb4, 0xf2, 0x87, 0xf9, 0x46, 0xed, 0x64, 0x2f, 0x8b, 0x02, 0xce, 0x3b, 0x45, 0x87, 0xf3,
	0x73, 0x26, 0x24, 0xf9, 0x06, 0x4a, 0xdc, 0x77, 0xe4, 0x20, 0x1c, 0xdf, 0xe2, 0x27, 0xd7, 0x4e,
	0x1e, 0x66, 0x6c, 0xb9, 0x8c, 0x4c, 0x3a, 0x53, 0x63, 0xf2, 0x09, 0x94, 0xfa, 0x74, 0xc8, 0x02,
	0x75, 0x10, 0xcd, 0x47, 0x11, 0xe7, 0x4d, 0x49, 0x76, 0xa1, 0x40, 0x03, 0x3c, 0x61, 0x01, 0x17,
	0x36, 0x68, 0xe0, 0x69, 0x58, 0x3a, 0x7d, 0xc5, 0x5f, 0x51, 0xc3, 0xd2, 0xe9, 0xb7, 0x3c, 0xe5,
	0x48, 0xc1, 0x98, 0x02, 0xdb, 0x78, 0x99, 0x45, 0xe9, 0xf4, 0x2f, 0x9c, 0x5b, 0x4a, 0x7e, 0x0d,
	0xe5, 0x89, 0xa0, 0xe3, 0x9e, 0x7c, 0xcf, 0xa9, 0x55, 0x5a, 0x7a, 0xba, 0x6b, 0x41, 0xc7, 0x57,
	0xef, 0x39, 0xed, 0x94, 0x26, 0xd1, 0x88, 0x3c, 0x07, 0x08, 0x55, 0x5a, 0x6a, 0x2e, 0xca, 0x87,
	0xf9, 0x46, 0xe5, 0xe4, 0x51, 0xc6, 0xd6, 0x74, 0x0a, 0x77, 0xca, 0xb8, 0x05, 0x69, 0x79, 0x08,
	0x65, 0x34, 0xc4, 0x14, 0x03, 0x9d, 0x98, 0x0a, 0x38, 0x57, 0x69, 0xf6, 0x19, 0x54, 0x69, 0x20,
	0xc7, 0x4e, 0xe0, 0xd2, 0x9e, 0xa4, 0xef, 0xa4, 0x55, 0x41, 0x83, 0xcd, 0x18, 0xbc, 0xa2, 0xef,
	0x24, 0xf9, 0x29, 0x4c, 0xe7, 0x3d, 0xce, 0x5c, 0x6b, 0x13, 0x6d, 0x2a, 0x31, 0x76, 0xc9, 0x5c,
	0x15, 0x64, 0xe0, 0x87, 0x8e, 0xc4, 0xf5, 0xaa, 0x0e, 0x82, 0x80, 0x5a, 0x7c, 0x04, 0x95, 0x3b,
	0xe6, 0xd1, 0xb0, 0x27, 0x99, 0xf4, 0xa9, 0x55, 0xc3, 0x65, 0x40, 0xe8, 0x4a, 0x21, 0x2a, 0x80,
	0x36, 0xf0, 0x69, 0x30, 0x94, 0x23, 0xeb, 0x01, 0x92, 0xaa, 0x37, 0x9d, 0x23, 0x94, 0xf8, 0x78,
	0xcb, 0x3c, 0x39, 0xb2, 0x4c, 0xb4, 0xd0, 0x3e, 0xfe, 0xa0, 0x10, 0x75, 0xfb, 0x8e, 0x27, 0x34,
	0x49, 0x5b, 0x48, 0xd2, 0x4f, 0xee, 0x7b, 0x33, 0x9d, 0xa2, 0xe3, 0x09, 0xe4, 0xe7, 0x19, 0x14,
	0xd4, 0xa3, 0x99, 0x08, 0x8b, 0xe0, 0xb5, 0x1c, 0x64, 0x71, 0x1b, 0x06, 0x83, 0x2e, 0x1a, 0x75,
	0x22, 0x63, 0x62, 0x43, 0xd5, 0x1d, 0x87, 0x6f, 0xa9, 0xd7, 0xe3, 0x37, 0x43, 0x95, 0x09, 0x3b,
	0x9a, 0x15, 0x0d, 0x5e, 0xde, 0x0c, 0x5b, 0x1e, 0x69, 0x41, 0x85, 0xfb, 0xce, 0xfb, 0x9e, 0xca,
	0x32, 0x47, 0x5a, 0xbb, 0xe8, 0xbf, 0x91, 0xe1, 0x3f, 0x79, 0x27, 0x2a, 0x3f, 0xdf, 0xff, 0x0e,
	0xed, 0x3b, 0xc0, 0xa7, 0x63, 0xd2, 0x00, 0x73, 0xe4, 0x88, 0x9e, 0x90, 0xe3, 0x30, 0x18, 0xf6,
	0x86, 0x13, 0xe6, 0x51, 0x6b, 0xef, 0x30, 0xd7, 0x28, 0x75, 0x6a, 0x23, 0x47, 0x74, 0x11, 0x3e,
	0x53, 0xa8, 0xba, 0x0a, 0x5c, 0xee, 0x0d, 0xd9, 0xc0, 0xda, 0xd7, 0x57, 0x81, 0xc0, 0x19, 0x1b,
	0x28, 0x1a, 0xf5, 0xa2, 0xbe, 0x0a, 0x4b, 0x5f, 0x05, 0x42, 0xfa, 0x2a, 0x7e, 0x09, 0xc4, 0x67,
	0xb7, 0x4c, 0xf6, 0x5c, 0x3f, 0x14, 0xb4, 0x27, 0xa8, 0x1b, 0x06, 0x9e, 0xf5, 0x09, 0xd2, 0x6d,
	0xe2, 0xca, 0xa9, 0x5a, 0xe8, 0x22, 0x6e, 0x3f, 0x03, 0x48, 0xce, 0x4b, 0xb6, 0xe1, 0x41, 0x32,
	0x6b, 0xab, 0x0f, 0x33, 0xd7, 0x08, 0x81, 0x5a, 0xea, 0x93, 0xe4, 0x2d, 0x37, 0x73, 0xf6, 0x5f,
	0x72, 0xba, 0x34, 0x68, 0x4a, 0x15, 0x4d, 0x48, 0x47, 0x74, 0x0d, 0xf9, 0xa5, 0x34, 0x5d, 0x9f,
	0x9d, 0xaa, 0x6d, 0x57, 0xd1, 0x3c, 0xba, 0x11, 0xf0, 0x13, 0x57, 0x0d, 0x30, 0xb5, 0x97, 0x9e,
	0x3b, 0x72, 0x82, 0x21, 0x4d, 0xca, 0x5f, 0x4d, 0xe3, 0xa7, 0x08, 0x37, 0xa5, 0xfd, 0xef, 0x3c,
	0x6c, 0xbe, 0x98, 0xf4, 0xfb, 0xfe, 0x8f, 0xa0, 0x40, 0x25, 0xe5, 0x66, 0x63, 0x59, 0xb9, 0xa9,
	0xcc, 0x96, 0x9b, 0x74, 0x49, 0x2b, 0x2c, 0x2b, 0x69, 0xc5, 0x74, 0x49, 0xfb, 0xff, 0x15, 0x28,
	0x02, 0xeb, 0x58, 0x7a, 0x74, 0x6d, 0xc2, 0xf1, 0xff, 0xec, 0x51, 0x6e, 0x2f, 0x3c, 0x4a, 0xfb,
	0x1c, 0xb6, 0xaf, 0xb9, 0xa0, 0x63, 0x99, 0x3c, 0xba, 0x0e, 0x7d, 0xa3, 0x22, 0xba, 0x38, 0xc1,
	0x14, 0xa8, 0x64, 0x46, 0x4c, 0xed, 0x88, 0x8c, 0xed, 0x3d, 0xd8, 0x59, 0xf4, 0x26, 0xb8, 0x6d,
	0x83, 0xf9, 0x92, 0xfa, 0xb3, 0x21, 0xe6, 0x32, 0xcc, 0xde, 0x86, 0xad, 0x39, 0x1b, 0xc1, 0xed,
	0xbf, 0x1a, 0xb0, 0x73, 0x46, 0x53, 0xee, 0x14, 0x47, 0x6a, 0x77, 0xfa, 0x4a, 0x73, 0xcb, 0xae,
	0xd4, 0x48, 0x5f, 0x69, 0x42, 0x62, 0xfe, 0x63, 0x48, 0x4c, 0x67, 0xf6, 0xfa, 0x6a, 0x99, 0xbd,
	0x0f, 0x45, 0x9d, 0xa0, 0xc2, 0xda, 0x38, 0xcc, 0x2b, 0x41, 0x81, 0x19, 0x2a, 0x66, 0x9b, 0x4f,
	0x61, 0xae, 0xf9, 0xec, 0x41, 0x21, 0x1c, 0x0c, 0x04, 0x8d, 0x33, 0x31, 0x9a, 0x91, 0x1d, 0xd8,
	0xc0, 0x4a, 0x83, 0x69, 0x58, 0xed, 0xe8, 0x89, 0xfd, 0x03, 0xec, 0x66, 0xf0, 0x22, 0x38, 0xf9,
	0x5a, 0xc9, 0x27, 0xa1, 0x48, 0xc9, 0x7f, 0xf8, 0xde, 0xd0, 0x54, 0x45, 0x90, 0xa1, 0x74, 0xfc,
	0x98, 0x2f, 0x9c, 0xd8, 0x07, 0x50, 0xe9, 0xca, 0x90, 0x2b, 0xeb, 0xac, 0xeb, 0xaa, 0xc1, 0x66,
	0xb2, 0x2c, 0xb8, 0x3d, 0x82, 0x9d, 0x17, 0x8e, 0x74, 0x47, 0x5d, 0x2a, 0x93, 0xb6, 0x42, 0xdf,
	0xa4, 0xd5, 0x55, 0x2e, 0xad, 0xae, 0x66, 0x5a, 0x94, 0xf1, 0x11, 0x2d, 0xca, 0xde, 0x87, 0xdd,
	0x8c, 0x48, 0x82, 0xdb, 0x27, 0xb0, 0x1f, 0x71, 0x32, 0xc5, 0xe3, 0x74, 0x59, 0x76, 0x0a, 0xfb,
	0x1c, 0xac, 0xec, 0x3d, 0x82, 0x93, 0xaf, 0x66, 0xa8, 0xbc, 0xff, 0x74, 0x68, 0x69, 0xff, 0x19,
	0x40, 0xc9, 0xbf, 0xa6, 0x27, 0x54, 0xd0, 0xaf, 0xc0, 0x08, 0x39, 0xc6, 0xab, 0x9d, 0x1c, 0x66,
	0xd5, 0xb6, 0xa9, 0xe9, 0x71, 0x9b, 0x77, 0x8c, 0x90, 0x47, 0x24, 0x1b, 0x53, 0x92, 0x8f, 0xc0,
	0x68, 0x73, 0x02, 0x50, 0x68, 0xf3, 0x48, 0x73, 0xe2, 0x58, 0x6d, 0x34, 0x73, 0x64, 0x13, 0x4a,
	0x6d, 0x7e, 0xaa, 0x44, 0x87, 0x6f, 0x1a, 0x76, 0x15, 0x2a, 0x53, 0x87, 0x82, 0xdb, 0x97, 0xb0,
	0xab, 0x9f, 0x62, 0xba, 0xac, 0xab, 0x53, 0x7d, 0x33, 0xf7, 0xb4, 0xb3, 0x8a, 0xd3, 0xcc, 0x9e,
	0xf8, 0x71, 0x5b, 0xb0, 0x97, 0xe5, 0x51, 0x70, 0xfb, 0x73, 0x20, 0x2f, 0xa9, 0x3f, 0x1f, 0x68,
	0x3e, 0x63, 0x76, 0x61, 0x7b, 0xc1, 0x4a, 0x70, 0xfb, 0x3f, 0x39, 0xd8, 0x3b, 0xa3, 0x33, 0x4e,
	0x7f, 0xd4, 0x8f, 0x3c, 0x79, 0xc7, 0x85, 0xec, 0x77, 0x5c, 0x4c, 0xbf, 0x63, 0x0f, 0x73, 0x76,
	0xf1, 0xe3, 0x05, 0x27, 0x4f, 0x67, 0xd2, 0xef, 0x83, 0xd7, 0x74, 0xdf, 0x5b, 0xfe, 0x67, 0x0e,
	0xcb, 0x45, 0xd4, 0x73, 0x54, 0xb6, 0xb7, 0x82, 0x41, 0xa8, 0x28, 0x3e, 0x00, 0x70, 0x35, 0x9a,
	0xbc, 0x8d, 0x72, 0x84, 0xb4, 0xbc, 0x54, 0xaf, 0x35, 0xd2, 0xbd, 0x96, 0xc0, 0xfa, 0x38, 0xf4,
	0xf5, 0xcf, 0xb1, 0x6a, 0x07, 0xc7, 0xa4, 0x01, 0x79, 0x87, 0xf3, 0xa8, 0x95, 0x2f, 0xa3, 0x4f,
	0x99, 0xcc, 0x74, 0xfe, 0x8d, 0x8f, 0xe9, 0xfc, 0x0f, 0x41, 0x37, 0xca, 0x9e, 0xfa, 0x41, 0xaa,
	0xd9, 0x2d, 0x21, 0x70, 0xcd, 0x3c, 0xfb, 0xef, 0x06, 0xe6, 0xd1, 0xc2, 0x37, 0x0a, 0x4e, 0x9e,
	0x47, 0x92, 0xea, 0x63, 0x5a, 0x1a, 0xea, 0xa8, 0x48, 0x0c, 0xfd, 0x06, 0x40, 0x95, 0xaa, 0x68,
	0xbb, 0x81, 0xdb, 0xef, 0x2f, 0x07, 0x65, 0x67, 0xfa, 0xfb, 0xf6, 0x25, 0x54, 0xfb, 0x78, 0x4f,
	0xf1, 0xfe, 0xfc, 0x6a, 0xcf, 0x6e, 0xb3, 0x9f, 0xd6, 0x63, 0xcf, 0x67, 0x55, 0xe1, 0xfa, 0xbd,
	0x9f, 0xb0, 0x28, 0x05, 0xed, 0xef, 0x61, 0xbf, 0x3b, 0x43, 0x4e, 0x64, 0xf3, 0xe1, 0x14, 0x78,
	0x34, 0x1b, 0x59, 0xe7, 0x41, 0xda, 0x75, 0x1d, 0xac, 0x6c, 0xd7, 0x82, 0x1f, 0xbd, 0x86, 0x7c,
	0x93, 0x73, 0x52, 0x81, 0x62, 0x93, 0xc7, 0x25, 0xac, 0x0c, 0x1b, 0x4d, 0xce, 0xaf, 0xae, 0xcc,
	0x1c, 0xa9, 0x01, 0x34, 0x39, 0xff, 0x76, 0xe2, 0x04, 0xdf, 0x87, 0x13, 0xd3, 0x50, 0x15, 0xad,
	0xc9, 0xf9, 0x77, 0x0e, 0xfb, 0x3d, 0x35, 0xf3, 0xa4, 0x0a, 0x65, 0x35, 0x63, 0xaf, 0xd5, 0x4f,
	0xe8, 0xc2, 0x51, 0x13, 0x4a, 0x71, 0x4e, 0x90, 0x07, 0x50, 0x89, 0xc7, 0x4d, 0xdf, 0x37, 0xd7,
	0x22, 0xb5, 0xad, 0x81, 0xc0, 0x1b, 0x87, 0xcc, 0x33, 0x73, 0x69, 0xab, 0x56, 0xbb, 0x6b, 0x1a,
	0x47, 0xdf, 0x42, 0x29, 0x96, 0x6c, 0x6a, 0x31, 0x1e, 0x6b, 0x17, 0xbb, 0xb0, 0x15, 0x03, 0x5d,
	0x4e, 0x5d, 0x36, 0x60, 0x54, 0x39, 0xd9, 0x01, 0x33, 0x86, 0x4f, 0xc7, 0xe1, 0x5b, 0xa5, 0x9b,
	0x4c, 0xe3, 0x88, 0x02, 0x24, 0x75, 0x44, 0xc9, 0xfa, 0x64, 0x16, 0x7d, 0xe6, 0x1e, 0x90, 0x04,
	0x6b, 0x05, 0x8e, 0x2b, 0xd9, 0x1d, 0xd5, 0xfe, 0x12, 0xbc, 0xa9, 0x51, 0x43, 0x05, 0x4f, 0xd0,
	0x57, 0xef, 0x38, 0x1b, 0x53, 0xcf, 0xcc, 0x1f, 0xfd, 0x0a, 0x76, 0x33, 0x65, 0x3e, 0x29, 0xc1,
	0xfa, 0x45, 0xfb, 0xe2, 0x95, 0xa6, 0xf3, 0xf4, 0xbc, 0xdd, 0x7d, 0x65, 0xe6, 0x14, 0xd8, 0xbe,
	0x7c, 0x75, 0x61, 0x1a, 0x27, 0xff, 0x28, 0xc3, 0x83, 0xeb, 0xa1, 0x9b, 0xde, 0x48, 0x28, 0x98,
	0xf3, 0xe2, 0x8c, 0x64, 0xfd, 0x93, 0x92, 0xa1, 0x07, 0xeb, 0x3f, 0x5f, 0xc9, 0x4e, 0x70, 0x7b,
	0x8d, 0xfc, 0x09, 0xaa, 0x33, 0x3a, 0x8e, 0x7c, 0x96, 0xb1, 0x77, 0x5e, 0x0d, 0xd6, 0x3f, 0xff,
	0xb0, 0x11, 0x7a, 0x1f, 0xc1, 0xd6, 0x82, 0xee, 0x21, 0x59, 0xa7, 0xcb, 0x52, 0x8d, 0xf5, 0xc6,
	0x6a, 0x86, 0x18, 0xe9, 0x3b, 0x28, 0xc5, 0x02, 0x87, 0x7c, 0x9a, 0xb1, 0x2f, 0x25, 0x8e, 0xea,
	0x8f, 0xee, 0x5d, 0x8f, 0x0f, 0xbe, 0xa0, 0x5a, 0x32, 0x0f, 0x9e, 0xa5, 0xa2, 0x32, 0x0f, 0x9e,
	0x2d, 0x82, 0xd6, 0xc8, 0x9b, 0xa9, 0x64, 0x9e, 0x91, 0x34, 0xe4, 0x68, 0xf9, 0xc7, 0xcf, 0xeb,
	0xa5, 0xfa, 0x2f, 0x56, 0xb6, 0xc5, 0x90, 0xaf, 0xa1, 0x18, 0x69, 0x0f, 0x72, 0x70, 0xaf, 0xd0,
	0xa9, 0x7f, 0x7a, 0xdf, 0x32, 0xfa, 0xba, 0x01, 0xb2, 0x28, 0x33, 0x48, 0x63, 0x69, 0x02, 0xce,
	0xc9, 0x8e, 0xfa, 0xe3, 0x15, 0x2d, 0x31, 0x58, 0x1f, 0x1e, 0xcc, 0x69, 0x12, 0xf2, 0xb3, 0xec,
	0x4c, 0x9c, 0x0f, 0xf3, 0xc5, 0x2a, 0x66, 0x18, 0x23, 0x80, 0xed, 0x8c, 0x16, 0x4f, 0x1e, 0x67,
	0x53, 0x9c, 0xa1, 0x83, 0xea, 0x47, 0xab, 0x9a, 0xc6, 0x04, 0x2e, 0xf6, 0x41, 0xb2, 0x24, 0xf5,
	0x17, 0x25, 0x41, 0xfd, 0xf1, 0x8a, 0x96, 0x71, 0xb2, 0x65, 0x15, 0xff, 0xcc, 0x64, 0x5b, 0xd2,
	0x80, 0x32, 0x93, 0x6d, 0x59, 0x47, 0xb1, 0xd7, 0x5e, 0x3c, 0xfb, 0xe3, 0xd3, 0x61, 0xe8, 0x3b,
	0xc1, 0xf0, 0xf8, 0xd9, 0x89, 0x94, 0xc7, 0x6e, 0x78, 0xfb, 0x04, 0xff, 0xcc, 0x76, 0x43, 0xff,
	0x89, 0xa0, 0xe3, 0x3b, 0xe6, 0x52, 0xb1, 0xf8, 0x87, 0x77, 0xbf, 0x80, 0x46, 0x4f, 0xff, 0x1b,
	0x00, 0x00, 0xff, 0xff, 0xd8, 0x6b, 0xb4, 0x22, 0x1e, 0x17, 0x00, 0x00,
}
