// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-school/muse-school.proto

package muse_school // import "golang.52tt.com/protocol/services/muse-school"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UpdateSchoolsReq struct {
	Schools              []*FullSchool `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *UpdateSchoolsReq) Reset()         { *m = UpdateSchoolsReq{} }
func (m *UpdateSchoolsReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSchoolsReq) ProtoMessage()    {}
func (*UpdateSchoolsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{0}
}
func (m *UpdateSchoolsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchoolsReq.Unmarshal(m, b)
}
func (m *UpdateSchoolsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchoolsReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSchoolsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchoolsReq.Merge(dst, src)
}
func (m *UpdateSchoolsReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSchoolsReq.Size(m)
}
func (m *UpdateSchoolsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchoolsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchoolsReq proto.InternalMessageInfo

func (m *UpdateSchoolsReq) GetSchools() []*FullSchool {
	if m != nil {
		return m.Schools
	}
	return nil
}

type FullSchool struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	CityId               string   `protobuf:"bytes,4,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	ObjId                string   `protobuf:"bytes,5,opt,name=obj_id,json=objId,proto3" json:"obj_id,omitempty"`
	SchoolCode           string   `protobuf:"bytes,6,opt,name=school_code,json=schoolCode,proto3" json:"school_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FullSchool) Reset()         { *m = FullSchool{} }
func (m *FullSchool) String() string { return proto.CompactTextString(m) }
func (*FullSchool) ProtoMessage()    {}
func (*FullSchool) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{1}
}
func (m *FullSchool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FullSchool.Unmarshal(m, b)
}
func (m *FullSchool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FullSchool.Marshal(b, m, deterministic)
}
func (dst *FullSchool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FullSchool.Merge(dst, src)
}
func (m *FullSchool) XXX_Size() int {
	return xxx_messageInfo_FullSchool.Size(m)
}
func (m *FullSchool) XXX_DiscardUnknown() {
	xxx_messageInfo_FullSchool.DiscardUnknown(m)
}

var xxx_messageInfo_FullSchool proto.InternalMessageInfo

func (m *FullSchool) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *FullSchool) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *FullSchool) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *FullSchool) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *FullSchool) GetObjId() string {
	if m != nil {
		return m.ObjId
	}
	return ""
}

func (m *FullSchool) GetSchoolCode() string {
	if m != nil {
		return m.SchoolCode
	}
	return ""
}

type UpdateSchoolsResp struct {
	FailedSchoolName     []string `protobuf:"bytes,1,rep,name=failed_school_name,json=failedSchoolName,proto3" json:"failed_school_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSchoolsResp) Reset()         { *m = UpdateSchoolsResp{} }
func (m *UpdateSchoolsResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSchoolsResp) ProtoMessage()    {}
func (*UpdateSchoolsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{2}
}
func (m *UpdateSchoolsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchoolsResp.Unmarshal(m, b)
}
func (m *UpdateSchoolsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchoolsResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSchoolsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchoolsResp.Merge(dst, src)
}
func (m *UpdateSchoolsResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSchoolsResp.Size(m)
}
func (m *UpdateSchoolsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchoolsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchoolsResp proto.InternalMessageInfo

func (m *UpdateSchoolsResp) GetFailedSchoolName() []string {
	if m != nil {
		return m.FailedSchoolName
	}
	return nil
}

type UnBindSchoolReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnBindSchoolReq) Reset()         { *m = UnBindSchoolReq{} }
func (m *UnBindSchoolReq) String() string { return proto.CompactTextString(m) }
func (*UnBindSchoolReq) ProtoMessage()    {}
func (*UnBindSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{3}
}
func (m *UnBindSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnBindSchoolReq.Unmarshal(m, b)
}
func (m *UnBindSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnBindSchoolReq.Marshal(b, m, deterministic)
}
func (dst *UnBindSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnBindSchoolReq.Merge(dst, src)
}
func (m *UnBindSchoolReq) XXX_Size() int {
	return xxx_messageInfo_UnBindSchoolReq.Size(m)
}
func (m *UnBindSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnBindSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnBindSchoolReq proto.InternalMessageInfo

func (m *UnBindSchoolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type UnBindSchoolResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnBindSchoolResp) Reset()         { *m = UnBindSchoolResp{} }
func (m *UnBindSchoolResp) String() string { return proto.CompactTextString(m) }
func (*UnBindSchoolResp) ProtoMessage()    {}
func (*UnBindSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{4}
}
func (m *UnBindSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnBindSchoolResp.Unmarshal(m, b)
}
func (m *UnBindSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnBindSchoolResp.Marshal(b, m, deterministic)
}
func (dst *UnBindSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnBindSchoolResp.Merge(dst, src)
}
func (m *UnBindSchoolResp) XXX_Size() int {
	return xxx_messageInfo_UnBindSchoolResp.Size(m)
}
func (m *UnBindSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UnBindSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_UnBindSchoolResp proto.InternalMessageInfo

type ListSchoolsReq struct {
	ForceRefresh         bool     `protobuf:"varint,1,opt,name=force_refresh,json=forceRefresh,proto3" json:"force_refresh,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListSchoolsReq) Reset()         { *m = ListSchoolsReq{} }
func (m *ListSchoolsReq) String() string { return proto.CompactTextString(m) }
func (*ListSchoolsReq) ProtoMessage()    {}
func (*ListSchoolsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{5}
}
func (m *ListSchoolsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolsReq.Unmarshal(m, b)
}
func (m *ListSchoolsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolsReq.Marshal(b, m, deterministic)
}
func (dst *ListSchoolsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolsReq.Merge(dst, src)
}
func (m *ListSchoolsReq) XXX_Size() int {
	return xxx_messageInfo_ListSchoolsReq.Size(m)
}
func (m *ListSchoolsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolsReq proto.InternalMessageInfo

func (m *ListSchoolsReq) GetForceRefresh() bool {
	if m != nil {
		return m.ForceRefresh
	}
	return false
}

type ListSchoolsResp struct {
	Schools              []*School `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ListSchoolsResp) Reset()         { *m = ListSchoolsResp{} }
func (m *ListSchoolsResp) String() string { return proto.CompactTextString(m) }
func (*ListSchoolsResp) ProtoMessage()    {}
func (*ListSchoolsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{6}
}
func (m *ListSchoolsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolsResp.Unmarshal(m, b)
}
func (m *ListSchoolsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolsResp.Marshal(b, m, deterministic)
}
func (dst *ListSchoolsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolsResp.Merge(dst, src)
}
func (m *ListSchoolsResp) XXX_Size() int {
	return xxx_messageInfo_ListSchoolsResp.Size(m)
}
func (m *ListSchoolsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolsResp proto.InternalMessageInfo

func (m *ListSchoolsResp) GetSchools() []*School {
	if m != nil {
		return m.Schools
	}
	return nil
}

type School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	CityId               string   `protobuf:"bytes,4,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string   `protobuf:"bytes,5,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	RealName             string   `protobuf:"bytes,6,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	Phone                string   `protobuf:"bytes,7,opt,name=phone,proto3" json:"phone,omitempty"`
	SourceList           []string `protobuf:"bytes,8,rep,name=source_list,json=sourceList,proto3" json:"source_list,omitempty"`
	ObjId                string   `protobuf:"bytes,9,opt,name=obj_id,json=objId,proto3" json:"obj_id,omitempty"`
	SchoolCode           string   `protobuf:"bytes,10,opt,name=school_code,json=schoolCode,proto3" json:"school_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *School) Reset()         { *m = School{} }
func (m *School) String() string { return proto.CompactTextString(m) }
func (*School) ProtoMessage()    {}
func (*School) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{7}
}
func (m *School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_School.Unmarshal(m, b)
}
func (m *School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_School.Marshal(b, m, deterministic)
}
func (dst *School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_School.Merge(dst, src)
}
func (m *School) XXX_Size() int {
	return xxx_messageInfo_School.Size(m)
}
func (m *School) XXX_DiscardUnknown() {
	xxx_messageInfo_School.DiscardUnknown(m)
}

var xxx_messageInfo_School proto.InternalMessageInfo

func (m *School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *School) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *School) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *School) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *School) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *School) GetSourceList() []string {
	if m != nil {
		return m.SourceList
	}
	return nil
}

func (m *School) GetObjId() string {
	if m != nil {
		return m.ObjId
	}
	return ""
}

func (m *School) GetSchoolCode() string {
	if m != nil {
		return m.SchoolCode
	}
	return ""
}

type BindSchoolReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SchoolId             string   `protobuf:"bytes,2,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	RealName             string   `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	Phone                string   `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	App_ID               uint32   `protobuf:"varint,5,opt,name=app_ID,json=appID,proto3" json:"app_ID,omitempty"`
	Os                   uint32   `protobuf:"varint,6,opt,name=os,proto3" json:"os,omitempty"`
	Source               string   `protobuf:"bytes,7,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindSchoolReq) Reset()         { *m = BindSchoolReq{} }
func (m *BindSchoolReq) String() string { return proto.CompactTextString(m) }
func (*BindSchoolReq) ProtoMessage()    {}
func (*BindSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{8}
}
func (m *BindSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindSchoolReq.Unmarshal(m, b)
}
func (m *BindSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindSchoolReq.Marshal(b, m, deterministic)
}
func (dst *BindSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindSchoolReq.Merge(dst, src)
}
func (m *BindSchoolReq) XXX_Size() int {
	return xxx_messageInfo_BindSchoolReq.Size(m)
}
func (m *BindSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindSchoolReq proto.InternalMessageInfo

func (m *BindSchoolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BindSchoolReq) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

func (m *BindSchoolReq) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *BindSchoolReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *BindSchoolReq) GetApp_ID() uint32 {
	if m != nil {
		return m.App_ID
	}
	return 0
}

func (m *BindSchoolReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

func (m *BindSchoolReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type BindSchoolResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindSchoolResp) Reset()         { *m = BindSchoolResp{} }
func (m *BindSchoolResp) String() string { return proto.CompactTextString(m) }
func (*BindSchoolResp) ProtoMessage()    {}
func (*BindSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{9}
}
func (m *BindSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindSchoolResp.Unmarshal(m, b)
}
func (m *BindSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindSchoolResp.Marshal(b, m, deterministic)
}
func (dst *BindSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindSchoolResp.Merge(dst, src)
}
func (m *BindSchoolResp) XXX_Size() int {
	return xxx_messageInfo_BindSchoolResp.Size(m)
}
func (m *BindSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BindSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_BindSchoolResp proto.InternalMessageInfo

type BatchUsersSchoolReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUsersSchoolReq) Reset()         { *m = BatchUsersSchoolReq{} }
func (m *BatchUsersSchoolReq) String() string { return proto.CompactTextString(m) }
func (*BatchUsersSchoolReq) ProtoMessage()    {}
func (*BatchUsersSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{10}
}
func (m *BatchUsersSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUsersSchoolReq.Unmarshal(m, b)
}
func (m *BatchUsersSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUsersSchoolReq.Marshal(b, m, deterministic)
}
func (dst *BatchUsersSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUsersSchoolReq.Merge(dst, src)
}
func (m *BatchUsersSchoolReq) XXX_Size() int {
	return xxx_messageInfo_BatchUsersSchoolReq.Size(m)
}
func (m *BatchUsersSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUsersSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUsersSchoolReq proto.InternalMessageInfo

func (m *BatchUsersSchoolReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchUsersSchoolResp struct {
	UserSchool           map[uint32]*School `protobuf:"bytes,1,rep,name=user_school,json=userSchool,proto3" json:"user_school,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchUsersSchoolResp) Reset()         { *m = BatchUsersSchoolResp{} }
func (m *BatchUsersSchoolResp) String() string { return proto.CompactTextString(m) }
func (*BatchUsersSchoolResp) ProtoMessage()    {}
func (*BatchUsersSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{11}
}
func (m *BatchUsersSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUsersSchoolResp.Unmarshal(m, b)
}
func (m *BatchUsersSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUsersSchoolResp.Marshal(b, m, deterministic)
}
func (dst *BatchUsersSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUsersSchoolResp.Merge(dst, src)
}
func (m *BatchUsersSchoolResp) XXX_Size() int {
	return xxx_messageInfo_BatchUsersSchoolResp.Size(m)
}
func (m *BatchUsersSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUsersSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUsersSchoolResp proto.InternalMessageInfo

func (m *BatchUsersSchoolResp) GetUserSchool() map[uint32]*School {
	if m != nil {
		return m.UserSchool
	}
	return nil
}

type BatchSchoolReq struct {
	SchoolIds            []string `protobuf:"bytes,1,rep,name=school_ids,json=schoolIds,proto3" json:"school_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSchoolReq) Reset()         { *m = BatchSchoolReq{} }
func (m *BatchSchoolReq) String() string { return proto.CompactTextString(m) }
func (*BatchSchoolReq) ProtoMessage()    {}
func (*BatchSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{12}
}
func (m *BatchSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSchoolReq.Unmarshal(m, b)
}
func (m *BatchSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSchoolReq.Marshal(b, m, deterministic)
}
func (dst *BatchSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSchoolReq.Merge(dst, src)
}
func (m *BatchSchoolReq) XXX_Size() int {
	return xxx_messageInfo_BatchSchoolReq.Size(m)
}
func (m *BatchSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSchoolReq proto.InternalMessageInfo

func (m *BatchSchoolReq) GetSchoolIds() []string {
	if m != nil {
		return m.SchoolIds
	}
	return nil
}

type BatchSchoolResp struct {
	SchoolMap            map[string]*School `protobuf:"bytes,1,rep,name=school_map,json=schoolMap,proto3" json:"school_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *BatchSchoolResp) Reset()         { *m = BatchSchoolResp{} }
func (m *BatchSchoolResp) String() string { return proto.CompactTextString(m) }
func (*BatchSchoolResp) ProtoMessage()    {}
func (*BatchSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_school_b57b30a4e4ae2033, []int{13}
}
func (m *BatchSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSchoolResp.Unmarshal(m, b)
}
func (m *BatchSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSchoolResp.Marshal(b, m, deterministic)
}
func (dst *BatchSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSchoolResp.Merge(dst, src)
}
func (m *BatchSchoolResp) XXX_Size() int {
	return xxx_messageInfo_BatchSchoolResp.Size(m)
}
func (m *BatchSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSchoolResp proto.InternalMessageInfo

func (m *BatchSchoolResp) GetSchoolMap() map[string]*School {
	if m != nil {
		return m.SchoolMap
	}
	return nil
}

func init() {
	proto.RegisterType((*UpdateSchoolsReq)(nil), "muse_school.UpdateSchoolsReq")
	proto.RegisterType((*FullSchool)(nil), "muse_school.FullSchool")
	proto.RegisterType((*UpdateSchoolsResp)(nil), "muse_school.UpdateSchoolsResp")
	proto.RegisterType((*UnBindSchoolReq)(nil), "muse_school.UnBindSchoolReq")
	proto.RegisterType((*UnBindSchoolResp)(nil), "muse_school.UnBindSchoolResp")
	proto.RegisterType((*ListSchoolsReq)(nil), "muse_school.ListSchoolsReq")
	proto.RegisterType((*ListSchoolsResp)(nil), "muse_school.ListSchoolsResp")
	proto.RegisterType((*School)(nil), "muse_school.School")
	proto.RegisterType((*BindSchoolReq)(nil), "muse_school.BindSchoolReq")
	proto.RegisterType((*BindSchoolResp)(nil), "muse_school.BindSchoolResp")
	proto.RegisterType((*BatchUsersSchoolReq)(nil), "muse_school.BatchUsersSchoolReq")
	proto.RegisterType((*BatchUsersSchoolResp)(nil), "muse_school.BatchUsersSchoolResp")
	proto.RegisterMapType((map[uint32]*School)(nil), "muse_school.BatchUsersSchoolResp.UserSchoolEntry")
	proto.RegisterType((*BatchSchoolReq)(nil), "muse_school.BatchSchoolReq")
	proto.RegisterType((*BatchSchoolResp)(nil), "muse_school.BatchSchoolResp")
	proto.RegisterMapType((map[string]*School)(nil), "muse_school.BatchSchoolResp.SchoolMapEntry")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseSchoolClient is the client API for MuseSchool service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseSchoolClient interface {
	ListSchools(ctx context.Context, in *ListSchoolsReq, opts ...grpc.CallOption) (*ListSchoolsResp, error)
	BindSchool(ctx context.Context, in *BindSchoolReq, opts ...grpc.CallOption) (*BindSchoolResp, error)
	BatchUsersSchool(ctx context.Context, in *BatchUsersSchoolReq, opts ...grpc.CallOption) (*BatchUsersSchoolResp, error)
	BatchSchool(ctx context.Context, in *BatchSchoolReq, opts ...grpc.CallOption) (*BatchSchoolResp, error)
	UnBindSchool(ctx context.Context, in *UnBindSchoolReq, opts ...grpc.CallOption) (*UnBindSchoolResp, error)
	UpdateSchools(ctx context.Context, in *UpdateSchoolsReq, opts ...grpc.CallOption) (*UpdateSchoolsResp, error)
}

type museSchoolClient struct {
	cc *grpc.ClientConn
}

func NewMuseSchoolClient(cc *grpc.ClientConn) MuseSchoolClient {
	return &museSchoolClient{cc}
}

func (c *museSchoolClient) ListSchools(ctx context.Context, in *ListSchoolsReq, opts ...grpc.CallOption) (*ListSchoolsResp, error) {
	out := new(ListSchoolsResp)
	err := c.cc.Invoke(ctx, "/muse_school.MuseSchool/ListSchools", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSchoolClient) BindSchool(ctx context.Context, in *BindSchoolReq, opts ...grpc.CallOption) (*BindSchoolResp, error) {
	out := new(BindSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_school.MuseSchool/BindSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSchoolClient) BatchUsersSchool(ctx context.Context, in *BatchUsersSchoolReq, opts ...grpc.CallOption) (*BatchUsersSchoolResp, error) {
	out := new(BatchUsersSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_school.MuseSchool/BatchUsersSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSchoolClient) BatchSchool(ctx context.Context, in *BatchSchoolReq, opts ...grpc.CallOption) (*BatchSchoolResp, error) {
	out := new(BatchSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_school.MuseSchool/BatchSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSchoolClient) UnBindSchool(ctx context.Context, in *UnBindSchoolReq, opts ...grpc.CallOption) (*UnBindSchoolResp, error) {
	out := new(UnBindSchoolResp)
	err := c.cc.Invoke(ctx, "/muse_school.MuseSchool/UnBindSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSchoolClient) UpdateSchools(ctx context.Context, in *UpdateSchoolsReq, opts ...grpc.CallOption) (*UpdateSchoolsResp, error) {
	out := new(UpdateSchoolsResp)
	err := c.cc.Invoke(ctx, "/muse_school.MuseSchool/UpdateSchools", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseSchoolServer is the server API for MuseSchool service.
type MuseSchoolServer interface {
	ListSchools(context.Context, *ListSchoolsReq) (*ListSchoolsResp, error)
	BindSchool(context.Context, *BindSchoolReq) (*BindSchoolResp, error)
	BatchUsersSchool(context.Context, *BatchUsersSchoolReq) (*BatchUsersSchoolResp, error)
	BatchSchool(context.Context, *BatchSchoolReq) (*BatchSchoolResp, error)
	UnBindSchool(context.Context, *UnBindSchoolReq) (*UnBindSchoolResp, error)
	UpdateSchools(context.Context, *UpdateSchoolsReq) (*UpdateSchoolsResp, error)
}

func RegisterMuseSchoolServer(s *grpc.Server, srv MuseSchoolServer) {
	s.RegisterService(&_MuseSchool_serviceDesc, srv)
}

func _MuseSchool_ListSchools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchoolsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSchoolServer).ListSchools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_school.MuseSchool/ListSchools",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSchoolServer).ListSchools(ctx, req.(*ListSchoolsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSchool_BindSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSchoolServer).BindSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_school.MuseSchool/BindSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSchoolServer).BindSchool(ctx, req.(*BindSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSchool_BatchUsersSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUsersSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSchoolServer).BatchUsersSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_school.MuseSchool/BatchUsersSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSchoolServer).BatchUsersSchool(ctx, req.(*BatchUsersSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSchool_BatchSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSchoolServer).BatchSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_school.MuseSchool/BatchSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSchoolServer).BatchSchool(ctx, req.(*BatchSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSchool_UnBindSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnBindSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSchoolServer).UnBindSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_school.MuseSchool/UnBindSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSchoolServer).UnBindSchool(ctx, req.(*UnBindSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSchool_UpdateSchools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSchoolsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSchoolServer).UpdateSchools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_school.MuseSchool/UpdateSchools",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSchoolServer).UpdateSchools(ctx, req.(*UpdateSchoolsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseSchool_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_school.MuseSchool",
	HandlerType: (*MuseSchoolServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListSchools",
			Handler:    _MuseSchool_ListSchools_Handler,
		},
		{
			MethodName: "BindSchool",
			Handler:    _MuseSchool_BindSchool_Handler,
		},
		{
			MethodName: "BatchUsersSchool",
			Handler:    _MuseSchool_BatchUsersSchool_Handler,
		},
		{
			MethodName: "BatchSchool",
			Handler:    _MuseSchool_BatchSchool_Handler,
		},
		{
			MethodName: "UnBindSchool",
			Handler:    _MuseSchool_UnBindSchool_Handler,
		},
		{
			MethodName: "UpdateSchools",
			Handler:    _MuseSchool_UpdateSchools_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-school/muse-school.proto",
}

func init() {
	proto.RegisterFile("muse-school/muse-school.proto", fileDescriptor_muse_school_b57b30a4e4ae2033)
}

var fileDescriptor_muse_school_b57b30a4e4ae2033 = []byte{
	// 742 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x55, 0xdd, 0x4e, 0xd4, 0x40,
	0x18, 0xcd, 0xfe, 0xb2, 0xfb, 0x2d, 0xdd, 0xad, 0x1f, 0x20, 0x4d, 0x11, 0xc5, 0x72, 0x03, 0x51,
	0x76, 0x03, 0x86, 0xc4, 0x78, 0xa5, 0x08, 0xc6, 0x55, 0x21, 0xb1, 0x86, 0x98, 0x78, 0xb3, 0x29,
	0xed, 0xc0, 0x16, 0xba, 0x3b, 0x43, 0xa7, 0x25, 0xd9, 0x3b, 0xdf, 0xc1, 0x17, 0xf1, 0x09, 0x7c,
	0x13, 0xdf, 0xc5, 0xcc, 0x0f, 0x6c, 0x5b, 0x5c, 0x48, 0x4c, 0xbc, 0x9b, 0x39, 0xe7, 0x9b, 0x69,
	0xcf, 0x39, 0xdf, 0xcc, 0xc0, 0xea, 0x28, 0xe5, 0x64, 0x8b, 0xfb, 0x43, 0x4a, 0xa3, 0x5e, 0x66,
	0xdc, 0x65, 0x31, 0x4d, 0x28, 0xb6, 0x04, 0x34, 0x50, 0x90, 0x73, 0x00, 0xe6, 0x31, 0x0b, 0xbc,
	0x84, 0x7c, 0x91, 0x73, 0xee, 0x92, 0x4b, 0xdc, 0x86, 0x39, 0xc5, 0x72, 0xab, 0xb4, 0x56, 0xd9,
	0x68, 0xed, 0x2c, 0x77, 0x33, 0x4b, 0xba, 0xef, 0xd2, 0x28, 0x52, 0xd5, 0xee, 0x75, 0x9d, 0xf3,
	0xa3, 0x04, 0x30, 0xc5, 0xb1, 0x0d, 0xe5, 0x30, 0xb0, 0x4a, 0x6b, 0xa5, 0x8d, 0xa6, 0x5b, 0x0e,
	0x03, 0x44, 0xa8, 0x8e, 0xbd, 0x11, 0xb1, 0xca, 0x12, 0x91, 0x63, 0x34, 0xa1, 0x12, 0x8e, 0xce,
	0xac, 0x8a, 0x84, 0xc4, 0x10, 0x97, 0x61, 0xce, 0x0f, 0x93, 0xc9, 0x20, 0x0c, 0xac, 0xaa, 0x44,
	0xeb, 0x62, 0xda, 0x0f, 0x70, 0x09, 0xea, 0xf4, 0xe4, 0x5c, 0xe0, 0x35, 0x89, 0xd7, 0xe8, 0xc9,
	0x79, 0x3f, 0xc0, 0x27, 0xd0, 0x52, 0xdf, 0x1f, 0xf8, 0x34, 0x20, 0x56, 0x5d, 0x72, 0xa0, 0xa0,
	0xb7, 0x34, 0x20, 0xce, 0x1b, 0x78, 0x50, 0x10, 0xc7, 0x19, 0x3e, 0x07, 0x3c, 0xf5, 0xc2, 0x88,
	0x04, 0x5a, 0xcf, 0x40, 0xfe, 0x99, 0x10, 0xda, 0x74, 0x4d, 0xc5, 0xa8, 0xf2, 0x23, 0x6f, 0x44,
	0x9c, 0x75, 0xe8, 0x1c, 0x8f, 0xf7, 0xc2, 0xb1, 0xc6, 0x84, 0x3d, 0x26, 0x54, 0x52, 0xad, 0xce,
	0x70, 0xc5, 0xd0, 0x41, 0x30, 0xf3, 0x45, 0x9c, 0x39, 0xbb, 0xd0, 0xfe, 0x14, 0xf2, 0x24, 0x63,
	0xeb, 0x3a, 0x18, 0xa7, 0x34, 0xf6, 0xc9, 0x20, 0x26, 0xa7, 0x31, 0xe1, 0x43, 0xb9, 0x43, 0xc3,
	0x9d, 0x97, 0xa0, 0xab, 0x30, 0xe7, 0x35, 0x74, 0x72, 0xcb, 0x38, 0xc3, 0xad, 0x62, 0x1c, 0x0b,
	0xb9, 0x38, 0x8a, 0x51, 0x7c, 0x2f, 0x43, 0xfd, 0xff, 0xc4, 0xb0, 0x02, 0x4d, 0x49, 0xc8, 0x3d,
	0x54, 0x12, 0x0d, 0x01, 0x08, 0xa3, 0x04, 0x19, 0x13, 0x4f, 0xbb, 0xa9, 0xa2, 0x68, 0x08, 0x40,
	0x92, 0x8b, 0x50, 0x63, 0x43, 0x3a, 0x26, 0xd6, 0x9c, 0xca, 0x4f, 0x4e, 0x64, 0x7e, 0x34, 0x15,
	0x8e, 0x44, 0x21, 0x4f, 0xac, 0x86, 0x8c, 0x00, 0x14, 0x24, 0x4c, 0xc8, 0xe4, 0xde, 0xbc, 0x23,
	0x77, 0xb8, 0x95, 0xfb, 0xcf, 0x12, 0x18, 0xf7, 0x64, 0x26, 0xfe, 0x57, 0x6f, 0x12, 0x06, 0xda,
	0x90, 0x86, 0x02, 0x94, 0xd2, 0xa9, 0x98, 0xca, 0x2c, 0x31, 0xd5, 0xac, 0x98, 0x25, 0xa8, 0x7b,
	0x8c, 0x0d, 0xfa, 0xfb, 0xd2, 0x19, 0xc3, 0xad, 0x79, 0x8c, 0xf5, 0xf7, 0x45, 0x04, 0x94, 0x4b,
	0x3f, 0x0c, 0xb7, 0x4c, 0x39, 0x3e, 0x84, 0xba, 0x12, 0xa8, 0xad, 0xd0, 0x33, 0xc7, 0x84, 0x76,
	0xa1, 0x81, 0x36, 0x61, 0x61, 0xcf, 0x4b, 0xfc, 0xe1, 0x31, 0x27, 0x31, 0x9f, 0x2a, 0x41, 0xa8,
	0xa6, 0x61, 0xa0, 0x5a, 0xc1, 0x70, 0xe5, 0xd8, 0xf9, 0x55, 0x82, 0xc5, 0xdb, 0xb5, 0x9c, 0xa1,
	0x0b, 0xad, 0x94, 0x93, 0x58, 0xb7, 0x8a, 0x6e, 0x9f, 0xed, 0x5c, 0xfb, 0xfc, 0x6d, 0x5d, 0x57,
	0xcc, 0xd5, 0xf4, 0x60, 0x9c, 0xc4, 0x13, 0x17, 0xd2, 0x1b, 0xc0, 0x76, 0xa1, 0x53, 0xa0, 0x85,
	0xbb, 0x17, 0x64, 0x72, 0xed, 0xee, 0x05, 0x99, 0xe0, 0x26, 0xd4, 0xae, 0xbc, 0x28, 0x55, 0xad,
	0x36, 0xa3, 0x63, 0x55, 0xc5, 0xab, 0xf2, 0xcb, 0x92, 0xd3, 0x83, 0xb6, 0xfc, 0x8f, 0xa9, 0xcc,
	0x55, 0x80, 0x9b, 0x78, 0xb8, 0x3e, 0x9d, 0xcd, 0xeb, 0x7c, 0xb8, 0x48, 0xb8, 0x93, 0x5b, 0xc1,
	0x19, 0x7e, 0xb8, 0x59, 0x32, 0xf2, 0x98, 0xd6, 0xfa, 0xec, 0xb6, 0xd6, 0x8c, 0x4c, 0x35, 0x3c,
	0xf4, 0x98, 0x52, 0xa9, 0xf7, 0x3f, 0xf4, 0x98, 0xfd, 0x19, 0xda, 0x79, 0x32, 0xab, 0xb1, 0xf9,
	0x2f, 0x1a, 0x77, 0x7e, 0x57, 0x00, 0x0e, 0x53, 0xae, 0xef, 0x22, 0x7c, 0x0f, 0xad, 0xcc, 0x41,
	0xc7, 0x95, 0xdc, 0xea, 0xfc, 0xcd, 0x61, 0x3f, 0x9a, 0x4d, 0x72, 0x86, 0x07, 0x00, 0xd3, 0xd6,
	0x41, 0x3b, 0xaf, 0x38, 0x7b, 0x0a, 0xec, 0x95, 0x99, 0x1c, 0x67, 0xf8, 0x15, 0xcc, 0x62, 0x2f,
	0xe0, 0xda, 0x3d, 0xad, 0x72, 0x69, 0x3f, 0xbd, 0xb7, 0x99, 0x84, 0xd2, 0x8c, 0xf1, 0x05, 0xa5,
	0xf9, 0xd8, 0x0b, 0x4a, 0x8b, 0x09, 0x7f, 0x84, 0xf9, 0xec, 0x3d, 0x8b, 0xf9, 0xea, 0xc2, 0x3d,
	0x6d, 0xaf, 0xde, 0xc1, 0x72, 0x86, 0x47, 0x60, 0xe4, 0x1e, 0x07, 0x2c, 0xd4, 0x17, 0x5e, 0x45,
	0xfb, 0xf1, 0x5d, 0x34, 0x67, 0x7b, 0xbd, 0x6f, 0x5b, 0x67, 0x34, 0xf2, 0xc6, 0x67, 0xdd, 0xdd,
	0x9d, 0x24, 0xe9, 0xfa, 0x74, 0xd4, 0x93, 0xef, 0xad, 0x4f, 0xa3, 0x1e, 0x27, 0xf1, 0x55, 0xe8,
	0x13, 0x9e, 0x7d, 0x8d, 0x4f, 0xea, 0x92, 0x7e, 0xf1, 0x27, 0x00, 0x00, 0xff, 0xff, 0x2d, 0x86,
	0x20, 0x00, 0xaf, 0x07, 0x00, 0x00,
}
