// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/tt-rev-channel-mode-mgr/tt-rev-channel-mode-mgr.proto

package tt_rev_channel_mode_mgr // import "golang.52tt.com/protocol/services/tt-rev-channel-mode-mgr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type OperationType int32

const (
	OperationType_UNKNOWN OperationType = 0
	OperationType_OPEN    OperationType = 1
	OperationType_CLOSE   OperationType = 2
)

var OperationType_name = map[int32]string{
	0: "UNKNOWN",
	1: "OPEN",
	2: "CLOSE",
}
var OperationType_value = map[string]int32{
	"UNKNOWN": 0,
	"OPEN":    1,
	"CLOSE":   2,
}

func (x OperationType) String() string {
	return proto.EnumName(OperationType_name, int32(x))
}
func (OperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{0}
}

// 获取房间玩法列表状态
type GetChannelModeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelModeReq) Reset()         { *m = GetChannelModeReq{} }
func (m *GetChannelModeReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelModeReq) ProtoMessage()    {}
func (*GetChannelModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{0}
}
func (m *GetChannelModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelModeReq.Unmarshal(m, b)
}
func (m *GetChannelModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelModeReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelModeReq.Merge(dst, src)
}
func (m *GetChannelModeReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelModeReq.Size(m)
}
func (m *GetChannelModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelModeReq proto.InternalMessageInfo

func (m *GetChannelModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ChannelScheme struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName           string   `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	LayoutType           uint32   `protobuf:"varint,3,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	IconSelect           string   `protobuf:"bytes,4,opt,name=icon_select,json=iconSelect,proto3" json:"icon_select,omitempty"`
	IconUnSelect         string   `protobuf:"bytes,5,opt,name=icon_un_select,json=iconUnSelect,proto3" json:"icon_un_select,omitempty"`
	IsOpen               bool     `protobuf:"varint,6,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	PcIcon               string   `protobuf:"bytes,7,opt,name=pc_icon,json=pcIcon,proto3" json:"pc_icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelScheme) Reset()         { *m = ChannelScheme{} }
func (m *ChannelScheme) String() string { return proto.CompactTextString(m) }
func (*ChannelScheme) ProtoMessage()    {}
func (*ChannelScheme) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{1}
}
func (m *ChannelScheme) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelScheme.Unmarshal(m, b)
}
func (m *ChannelScheme) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelScheme.Marshal(b, m, deterministic)
}
func (dst *ChannelScheme) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelScheme.Merge(dst, src)
}
func (m *ChannelScheme) XXX_Size() int {
	return xxx_messageInfo_ChannelScheme.Size(m)
}
func (m *ChannelScheme) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelScheme.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelScheme proto.InternalMessageInfo

func (m *ChannelScheme) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ChannelScheme) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *ChannelScheme) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

func (m *ChannelScheme) GetIconSelect() string {
	if m != nil {
		return m.IconSelect
	}
	return ""
}

func (m *ChannelScheme) GetIconUnSelect() string {
	if m != nil {
		return m.IconUnSelect
	}
	return ""
}

func (m *ChannelScheme) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *ChannelScheme) GetPcIcon() string {
	if m != nil {
		return m.PcIcon
	}
	return ""
}

// 获取房间玩法列表状态应
type GetChannelModeResp struct {
	ChannelId            uint32           `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Schemes              []*ChannelScheme `protobuf:"bytes,2,rep,name=schemes,proto3" json:"schemes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetChannelModeResp) Reset()         { *m = GetChannelModeResp{} }
func (m *GetChannelModeResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelModeResp) ProtoMessage()    {}
func (*GetChannelModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{2}
}
func (m *GetChannelModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelModeResp.Unmarshal(m, b)
}
func (m *GetChannelModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelModeResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelModeResp.Merge(dst, src)
}
func (m *GetChannelModeResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelModeResp.Size(m)
}
func (m *GetChannelModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelModeResp proto.InternalMessageInfo

func (m *GetChannelModeResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelModeResp) GetSchemes() []*ChannelScheme {
	if m != nil {
		return m.Schemes
	}
	return nil
}

// 设置房间当前玩法
type SetChannelModeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SchemeId             uint32   `protobuf:"varint,2,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelModeReq) Reset()         { *m = SetChannelModeReq{} }
func (m *SetChannelModeReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelModeReq) ProtoMessage()    {}
func (*SetChannelModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{3}
}
func (m *SetChannelModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelModeReq.Unmarshal(m, b)
}
func (m *SetChannelModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelModeReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelModeReq.Merge(dst, src)
}
func (m *SetChannelModeReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelModeReq.Size(m)
}
func (m *SetChannelModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelModeReq proto.InternalMessageInfo

func (m *SetChannelModeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelModeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

type SetChannelModeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelModeResp) Reset()         { *m = SetChannelModeResp{} }
func (m *SetChannelModeResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelModeResp) ProtoMessage()    {}
func (*SetChannelModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{4}
}
func (m *SetChannelModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelModeResp.Unmarshal(m, b)
}
func (m *SetChannelModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelModeResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelModeResp.Merge(dst, src)
}
func (m *SetChannelModeResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelModeResp.Size(m)
}
func (m *SetChannelModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelModeResp proto.InternalMessageInfo

type SwitchChannelMode struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	IsOpen               bool     `protobuf:"varint,2,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchChannelMode) Reset()         { *m = SwitchChannelMode{} }
func (m *SwitchChannelMode) String() string { return proto.CompactTextString(m) }
func (*SwitchChannelMode) ProtoMessage()    {}
func (*SwitchChannelMode) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{5}
}
func (m *SwitchChannelMode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchChannelMode.Unmarshal(m, b)
}
func (m *SwitchChannelMode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchChannelMode.Marshal(b, m, deterministic)
}
func (dst *SwitchChannelMode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchChannelMode.Merge(dst, src)
}
func (m *SwitchChannelMode) XXX_Size() int {
	return xxx_messageInfo_SwitchChannelMode.Size(m)
}
func (m *SwitchChannelMode) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchChannelMode.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchChannelMode proto.InternalMessageInfo

func (m *SwitchChannelMode) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SwitchChannelMode) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *SwitchChannelMode) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetChannelOperatorReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelOperatorReq) Reset()         { *m = GetChannelOperatorReq{} }
func (m *GetChannelOperatorReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelOperatorReq) ProtoMessage()    {}
func (*GetChannelOperatorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{6}
}
func (m *GetChannelOperatorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelOperatorReq.Unmarshal(m, b)
}
func (m *GetChannelOperatorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelOperatorReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelOperatorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelOperatorReq.Merge(dst, src)
}
func (m *GetChannelOperatorReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelOperatorReq.Size(m)
}
func (m *GetChannelOperatorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelOperatorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelOperatorReq proto.InternalMessageInfo

func (m *GetChannelOperatorReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelOperatorResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Operator             uint32   `protobuf:"varint,2,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelOperatorResp) Reset()         { *m = GetChannelOperatorResp{} }
func (m *GetChannelOperatorResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelOperatorResp) ProtoMessage()    {}
func (*GetChannelOperatorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{7}
}
func (m *GetChannelOperatorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelOperatorResp.Unmarshal(m, b)
}
func (m *GetChannelOperatorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelOperatorResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelOperatorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelOperatorResp.Merge(dst, src)
}
func (m *GetChannelOperatorResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelOperatorResp.Size(m)
}
func (m *GetChannelOperatorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelOperatorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelOperatorResp proto.InternalMessageInfo

func (m *GetChannelOperatorResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelOperatorResp) GetOperator() uint32 {
	if m != nil {
		return m.Operator
	}
	return 0
}

// =================运营后台相关================
type BatchGetChannelModeEntryReq struct {
	ChannelId            []uint32 `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	LayoutType           uint32   `protobuf:"varint,2,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelModeEntryReq) Reset()         { *m = BatchGetChannelModeEntryReq{} }
func (m *BatchGetChannelModeEntryReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelModeEntryReq) ProtoMessage()    {}
func (*BatchGetChannelModeEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{8}
}
func (m *BatchGetChannelModeEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelModeEntryReq.Unmarshal(m, b)
}
func (m *BatchGetChannelModeEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelModeEntryReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelModeEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelModeEntryReq.Merge(dst, src)
}
func (m *BatchGetChannelModeEntryReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelModeEntryReq.Size(m)
}
func (m *BatchGetChannelModeEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelModeEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelModeEntryReq proto.InternalMessageInfo

func (m *BatchGetChannelModeEntryReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *BatchGetChannelModeEntryReq) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

type BatchGetChannelModeEntryResp struct {
	HasEntryChannel      []uint32 `protobuf:"varint,1,rep,packed,name=has_entry_channel,json=hasEntryChannel,proto3" json:"has_entry_channel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelModeEntryResp) Reset()         { *m = BatchGetChannelModeEntryResp{} }
func (m *BatchGetChannelModeEntryResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelModeEntryResp) ProtoMessage()    {}
func (*BatchGetChannelModeEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{9}
}
func (m *BatchGetChannelModeEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelModeEntryResp.Unmarshal(m, b)
}
func (m *BatchGetChannelModeEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelModeEntryResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelModeEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelModeEntryResp.Merge(dst, src)
}
func (m *BatchGetChannelModeEntryResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelModeEntryResp.Size(m)
}
func (m *BatchGetChannelModeEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelModeEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelModeEntryResp proto.InternalMessageInfo

func (m *BatchGetChannelModeEntryResp) GetHasEntryChannel() []uint32 {
	if m != nil {
		return m.HasEntryChannel
	}
	return nil
}

type BatchOperateChannelModeReq struct {
	CidList              []uint32      `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	LayoutType           uint32        `protobuf:"varint,2,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	OperationType        OperationType `protobuf:"varint,3,opt,name=operation_type,json=operationType,proto3,enum=tt_rev_channel_mode_mgr.OperationType" json:"operation_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchOperateChannelModeReq) Reset()         { *m = BatchOperateChannelModeReq{} }
func (m *BatchOperateChannelModeReq) String() string { return proto.CompactTextString(m) }
func (*BatchOperateChannelModeReq) ProtoMessage()    {}
func (*BatchOperateChannelModeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{10}
}
func (m *BatchOperateChannelModeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchOperateChannelModeReq.Unmarshal(m, b)
}
func (m *BatchOperateChannelModeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchOperateChannelModeReq.Marshal(b, m, deterministic)
}
func (dst *BatchOperateChannelModeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchOperateChannelModeReq.Merge(dst, src)
}
func (m *BatchOperateChannelModeReq) XXX_Size() int {
	return xxx_messageInfo_BatchOperateChannelModeReq.Size(m)
}
func (m *BatchOperateChannelModeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchOperateChannelModeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchOperateChannelModeReq proto.InternalMessageInfo

func (m *BatchOperateChannelModeReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

func (m *BatchOperateChannelModeReq) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

func (m *BatchOperateChannelModeReq) GetOperationType() OperationType {
	if m != nil {
		return m.OperationType
	}
	return OperationType_UNKNOWN
}

type BatchOperateChannelModeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchOperateChannelModeResp) Reset()         { *m = BatchOperateChannelModeResp{} }
func (m *BatchOperateChannelModeResp) String() string { return proto.CompactTextString(m) }
func (*BatchOperateChannelModeResp) ProtoMessage()    {}
func (*BatchOperateChannelModeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{11}
}
func (m *BatchOperateChannelModeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchOperateChannelModeResp.Unmarshal(m, b)
}
func (m *BatchOperateChannelModeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchOperateChannelModeResp.Marshal(b, m, deterministic)
}
func (dst *BatchOperateChannelModeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchOperateChannelModeResp.Merge(dst, src)
}
func (m *BatchOperateChannelModeResp) XXX_Size() int {
	return xxx_messageInfo_BatchOperateChannelModeResp.Size(m)
}
func (m *BatchOperateChannelModeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchOperateChannelModeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchOperateChannelModeResp proto.InternalMessageInfo

type BuyWerwolfItemReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	OpenId               string   `protobuf:"bytes,2,opt,name=open_id,json=openId,proto3" json:"open_id,omitempty"`
	SetId                string   `protobuf:"bytes,3,opt,name=set_id,json=setId,proto3" json:"set_id,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	ItemId               uint32   `protobuf:"varint,5,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemName             string   `protobuf:"bytes,6,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	GameId               uint32   `protobuf:"varint,7,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BuyWerwolfItemReq) Reset()         { *m = BuyWerwolfItemReq{} }
func (m *BuyWerwolfItemReq) String() string { return proto.CompactTextString(m) }
func (*BuyWerwolfItemReq) ProtoMessage()    {}
func (*BuyWerwolfItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{12}
}
func (m *BuyWerwolfItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyWerwolfItemReq.Unmarshal(m, b)
}
func (m *BuyWerwolfItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyWerwolfItemReq.Marshal(b, m, deterministic)
}
func (dst *BuyWerwolfItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyWerwolfItemReq.Merge(dst, src)
}
func (m *BuyWerwolfItemReq) XXX_Size() int {
	return xxx_messageInfo_BuyWerwolfItemReq.Size(m)
}
func (m *BuyWerwolfItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyWerwolfItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_BuyWerwolfItemReq proto.InternalMessageInfo

func (m *BuyWerwolfItemReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BuyWerwolfItemReq) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *BuyWerwolfItemReq) GetSetId() string {
	if m != nil {
		return m.SetId
	}
	return ""
}

func (m *BuyWerwolfItemReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *BuyWerwolfItemReq) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *BuyWerwolfItemReq) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *BuyWerwolfItemReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ErrorInfo struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ErrorInfo) Reset()         { *m = ErrorInfo{} }
func (m *ErrorInfo) String() string { return proto.CompactTextString(m) }
func (*ErrorInfo) ProtoMessage()    {}
func (*ErrorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{13}
}
func (m *ErrorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ErrorInfo.Unmarshal(m, b)
}
func (m *ErrorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ErrorInfo.Marshal(b, m, deterministic)
}
func (dst *ErrorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ErrorInfo.Merge(dst, src)
}
func (m *ErrorInfo) XXX_Size() int {
	return xxx_messageInfo_ErrorInfo.Size(m)
}
func (m *ErrorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ErrorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ErrorInfo proto.InternalMessageInfo

func (m *ErrorInfo) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ErrorInfo) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type BuyWerwolfItemResp struct {
	OrderId              string     `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ErrorInfo            *ErrorInfo `protobuf:"bytes,2,opt,name=error_info,json=errorInfo,proto3" json:"error_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *BuyWerwolfItemResp) Reset()         { *m = BuyWerwolfItemResp{} }
func (m *BuyWerwolfItemResp) String() string { return proto.CompactTextString(m) }
func (*BuyWerwolfItemResp) ProtoMessage()    {}
func (*BuyWerwolfItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{14}
}
func (m *BuyWerwolfItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BuyWerwolfItemResp.Unmarshal(m, b)
}
func (m *BuyWerwolfItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BuyWerwolfItemResp.Marshal(b, m, deterministic)
}
func (dst *BuyWerwolfItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BuyWerwolfItemResp.Merge(dst, src)
}
func (m *BuyWerwolfItemResp) XXX_Size() int {
	return xxx_messageInfo_BuyWerwolfItemResp.Size(m)
}
func (m *BuyWerwolfItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BuyWerwolfItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_BuyWerwolfItemResp proto.InternalMessageInfo

func (m *BuyWerwolfItemResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *BuyWerwolfItemResp) GetErrorInfo() *ErrorInfo {
	if m != nil {
		return m.ErrorInfo
	}
	return nil
}

type GetWerwolfOrderSummaryReq struct {
	CidList              uint32   `protobuf:"varint,1,opt,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	LayoutType           uint32   `protobuf:"varint,2,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWerwolfOrderSummaryReq) Reset()         { *m = GetWerwolfOrderSummaryReq{} }
func (m *GetWerwolfOrderSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetWerwolfOrderSummaryReq) ProtoMessage()    {}
func (*GetWerwolfOrderSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{15}
}
func (m *GetWerwolfOrderSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWerwolfOrderSummaryReq.Unmarshal(m, b)
}
func (m *GetWerwolfOrderSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWerwolfOrderSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetWerwolfOrderSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWerwolfOrderSummaryReq.Merge(dst, src)
}
func (m *GetWerwolfOrderSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetWerwolfOrderSummaryReq.Size(m)
}
func (m *GetWerwolfOrderSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWerwolfOrderSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWerwolfOrderSummaryReq proto.InternalMessageInfo

func (m *GetWerwolfOrderSummaryReq) GetCidList() uint32 {
	if m != nil {
		return m.CidList
	}
	return 0
}

func (m *GetWerwolfOrderSummaryReq) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

type WerwolfOrderLog struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	ChangeScore          uint32   `protobuf:"varint,3,opt,name=change_score,json=changeScore,proto3" json:"change_score,omitempty"`
	OrderId              string   `protobuf:"bytes,4,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	DealToken            string   `protobuf:"bytes,6,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	ChannelId            uint32   `protobuf:"varint,7,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Price                uint32   `protobuf:"varint,8,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WerwolfOrderLog) Reset()         { *m = WerwolfOrderLog{} }
func (m *WerwolfOrderLog) String() string { return proto.CompactTextString(m) }
func (*WerwolfOrderLog) ProtoMessage()    {}
func (*WerwolfOrderLog) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{16}
}
func (m *WerwolfOrderLog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WerwolfOrderLog.Unmarshal(m, b)
}
func (m *WerwolfOrderLog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WerwolfOrderLog.Marshal(b, m, deterministic)
}
func (dst *WerwolfOrderLog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WerwolfOrderLog.Merge(dst, src)
}
func (m *WerwolfOrderLog) XXX_Size() int {
	return xxx_messageInfo_WerwolfOrderLog.Size(m)
}
func (m *WerwolfOrderLog) XXX_DiscardUnknown() {
	xxx_messageInfo_WerwolfOrderLog.DiscardUnknown(m)
}

var xxx_messageInfo_WerwolfOrderLog proto.InternalMessageInfo

func (m *WerwolfOrderLog) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *WerwolfOrderLog) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *WerwolfOrderLog) GetChangeScore() uint32 {
	if m != nil {
		return m.ChangeScore
	}
	return 0
}

func (m *WerwolfOrderLog) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *WerwolfOrderLog) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *WerwolfOrderLog) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *WerwolfOrderLog) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WerwolfOrderLog) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// (对账用) 根据orderId获取订单
type GetOrderLogByOrderIdsReq struct {
	OrderIdList          []string `protobuf:"bytes,1,rep,name=order_id_list,json=orderIdList,proto3" json:"order_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderLogByOrderIdsReq) Reset()         { *m = GetOrderLogByOrderIdsReq{} }
func (m *GetOrderLogByOrderIdsReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderLogByOrderIdsReq) ProtoMessage()    {}
func (*GetOrderLogByOrderIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{17}
}
func (m *GetOrderLogByOrderIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderLogByOrderIdsReq.Unmarshal(m, b)
}
func (m *GetOrderLogByOrderIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderLogByOrderIdsReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderLogByOrderIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderLogByOrderIdsReq.Merge(dst, src)
}
func (m *GetOrderLogByOrderIdsReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderLogByOrderIdsReq.Size(m)
}
func (m *GetOrderLogByOrderIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderLogByOrderIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderLogByOrderIdsReq proto.InternalMessageInfo

func (m *GetOrderLogByOrderIdsReq) GetOrderIdList() []string {
	if m != nil {
		return m.OrderIdList
	}
	return nil
}

type GetOrderLogByOrderIdsResp struct {
	OrderLogList         []*WerwolfOrderLog `protobuf:"bytes,1,rep,name=order_log_list,json=orderLogList,proto3" json:"order_log_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetOrderLogByOrderIdsResp) Reset()         { *m = GetOrderLogByOrderIdsResp{} }
func (m *GetOrderLogByOrderIdsResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderLogByOrderIdsResp) ProtoMessage()    {}
func (*GetOrderLogByOrderIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{18}
}
func (m *GetOrderLogByOrderIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderLogByOrderIdsResp.Unmarshal(m, b)
}
func (m *GetOrderLogByOrderIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderLogByOrderIdsResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderLogByOrderIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderLogByOrderIdsResp.Merge(dst, src)
}
func (m *GetOrderLogByOrderIdsResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderLogByOrderIdsResp.Size(m)
}
func (m *GetOrderLogByOrderIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderLogByOrderIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderLogByOrderIdsResp proto.InternalMessageInfo

func (m *GetOrderLogByOrderIdsResp) GetOrderLogList() []*WerwolfOrderLog {
	if m != nil {
		return m.OrderLogList
	}
	return nil
}

type BatchGetChannelIdByLayoutTypeReq struct {
	LayoutType           uint32   `protobuf:"varint,1,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelIdByLayoutTypeReq) Reset()         { *m = BatchGetChannelIdByLayoutTypeReq{} }
func (m *BatchGetChannelIdByLayoutTypeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelIdByLayoutTypeReq) ProtoMessage()    {}
func (*BatchGetChannelIdByLayoutTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{19}
}
func (m *BatchGetChannelIdByLayoutTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelIdByLayoutTypeReq.Unmarshal(m, b)
}
func (m *BatchGetChannelIdByLayoutTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelIdByLayoutTypeReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelIdByLayoutTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelIdByLayoutTypeReq.Merge(dst, src)
}
func (m *BatchGetChannelIdByLayoutTypeReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelIdByLayoutTypeReq.Size(m)
}
func (m *BatchGetChannelIdByLayoutTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelIdByLayoutTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelIdByLayoutTypeReq proto.InternalMessageInfo

func (m *BatchGetChannelIdByLayoutTypeReq) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

type BatchGetChannelIdByLayoutTypeResp struct {
	ChannelId            []uint32 `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelIdByLayoutTypeResp) Reset()         { *m = BatchGetChannelIdByLayoutTypeResp{} }
func (m *BatchGetChannelIdByLayoutTypeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelIdByLayoutTypeResp) ProtoMessage()    {}
func (*BatchGetChannelIdByLayoutTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb, []int{20}
}
func (m *BatchGetChannelIdByLayoutTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelIdByLayoutTypeResp.Unmarshal(m, b)
}
func (m *BatchGetChannelIdByLayoutTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelIdByLayoutTypeResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelIdByLayoutTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelIdByLayoutTypeResp.Merge(dst, src)
}
func (m *BatchGetChannelIdByLayoutTypeResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelIdByLayoutTypeResp.Size(m)
}
func (m *BatchGetChannelIdByLayoutTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelIdByLayoutTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelIdByLayoutTypeResp proto.InternalMessageInfo

func (m *BatchGetChannelIdByLayoutTypeResp) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func init() {
	proto.RegisterType((*GetChannelModeReq)(nil), "tt_rev_channel_mode_mgr.GetChannelModeReq")
	proto.RegisterType((*ChannelScheme)(nil), "tt_rev_channel_mode_mgr.ChannelScheme")
	proto.RegisterType((*GetChannelModeResp)(nil), "tt_rev_channel_mode_mgr.GetChannelModeResp")
	proto.RegisterType((*SetChannelModeReq)(nil), "tt_rev_channel_mode_mgr.SetChannelModeReq")
	proto.RegisterType((*SetChannelModeResp)(nil), "tt_rev_channel_mode_mgr.SetChannelModeResp")
	proto.RegisterType((*SwitchChannelMode)(nil), "tt_rev_channel_mode_mgr.SwitchChannelMode")
	proto.RegisterType((*GetChannelOperatorReq)(nil), "tt_rev_channel_mode_mgr.GetChannelOperatorReq")
	proto.RegisterType((*GetChannelOperatorResp)(nil), "tt_rev_channel_mode_mgr.GetChannelOperatorResp")
	proto.RegisterType((*BatchGetChannelModeEntryReq)(nil), "tt_rev_channel_mode_mgr.BatchGetChannelModeEntryReq")
	proto.RegisterType((*BatchGetChannelModeEntryResp)(nil), "tt_rev_channel_mode_mgr.BatchGetChannelModeEntryResp")
	proto.RegisterType((*BatchOperateChannelModeReq)(nil), "tt_rev_channel_mode_mgr.BatchOperateChannelModeReq")
	proto.RegisterType((*BatchOperateChannelModeResp)(nil), "tt_rev_channel_mode_mgr.BatchOperateChannelModeResp")
	proto.RegisterType((*BuyWerwolfItemReq)(nil), "tt_rev_channel_mode_mgr.BuyWerwolfItemReq")
	proto.RegisterType((*ErrorInfo)(nil), "tt_rev_channel_mode_mgr.ErrorInfo")
	proto.RegisterType((*BuyWerwolfItemResp)(nil), "tt_rev_channel_mode_mgr.BuyWerwolfItemResp")
	proto.RegisterType((*GetWerwolfOrderSummaryReq)(nil), "tt_rev_channel_mode_mgr.GetWerwolfOrderSummaryReq")
	proto.RegisterType((*WerwolfOrderLog)(nil), "tt_rev_channel_mode_mgr.WerwolfOrderLog")
	proto.RegisterType((*GetOrderLogByOrderIdsReq)(nil), "tt_rev_channel_mode_mgr.GetOrderLogByOrderIdsReq")
	proto.RegisterType((*GetOrderLogByOrderIdsResp)(nil), "tt_rev_channel_mode_mgr.GetOrderLogByOrderIdsResp")
	proto.RegisterType((*BatchGetChannelIdByLayoutTypeReq)(nil), "tt_rev_channel_mode_mgr.BatchGetChannelIdByLayoutTypeReq")
	proto.RegisterType((*BatchGetChannelIdByLayoutTypeResp)(nil), "tt_rev_channel_mode_mgr.BatchGetChannelIdByLayoutTypeResp")
	proto.RegisterEnum("tt_rev_channel_mode_mgr.OperationType", OperationType_name, OperationType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelModeMgrClient is the client API for ChannelModeMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelModeMgrClient interface {
	// 获取房间开启的玩法列表
	GetChannelMode(ctx context.Context, in *GetChannelModeReq, opts ...grpc.CallOption) (*GetChannelModeResp, error)
	// 设置房间开启的玩法列表
	SetChannelMode(ctx context.Context, in *SetChannelModeReq, opts ...grpc.CallOption) (*SetChannelModeResp, error)
	// 获取房间管理员
	GetChannelOperator(ctx context.Context, in *GetChannelOperatorReq, opts ...grpc.CallOption) (*GetChannelOperatorResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error)
	// 获取订单明细
	GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error)
	// 花费T豆购买身份/发言次数
	BuyWerwolfItem(ctx context.Context, in *BuyWerwolfItemReq, opts ...grpc.CallOption) (*BuyWerwolfItemResp, error)
	// 获取房间玩法入口
	BatchGetChannelModeEntry(ctx context.Context, in *BatchGetChannelModeEntryReq, opts ...grpc.CallOption) (*BatchGetChannelModeEntryResp, error)
	// 管理房间玩法入口
	BatchOperateChannelMode(ctx context.Context, in *BatchOperateChannelModeReq, opts ...grpc.CallOption) (*BatchOperateChannelModeResp, error)
	// 获取玩法的房间列表
	BatchGetChannelIdByLayoutType(ctx context.Context, in *BatchGetChannelIdByLayoutTypeReq, opts ...grpc.CallOption) (*BatchGetChannelIdByLayoutTypeResp, error)
}

type channelModeMgrClient struct {
	cc *grpc.ClientConn
}

func NewChannelModeMgrClient(cc *grpc.ClientConn) ChannelModeMgrClient {
	return &channelModeMgrClient{cc}
}

func (c *channelModeMgrClient) GetChannelMode(ctx context.Context, in *GetChannelModeReq, opts ...grpc.CallOption) (*GetChannelModeResp, error) {
	out := new(GetChannelModeResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetChannelMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) SetChannelMode(ctx context.Context, in *SetChannelModeReq, opts ...grpc.CallOption) (*SetChannelModeResp, error) {
	out := new(SetChannelModeResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/SetChannelMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) GetChannelOperator(ctx context.Context, in *GetChannelOperatorReq, opts ...grpc.CallOption) (*GetChannelOperatorResp, error) {
	out := new(GetChannelOperatorResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetChannelOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) GetConsumeTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetConsumeTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) GetConsumeOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetConsumeOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) GenFinancialFile(ctx context.Context, in *reconcile_v2.GenFinancialFileReq, opts ...grpc.CallOption) (*reconcile_v2.GenFinancialFileResp, error) {
	out := new(reconcile_v2.GenFinancialFileResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/GenFinancialFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) GetOrderLogByOrderIds(ctx context.Context, in *GetOrderLogByOrderIdsReq, opts ...grpc.CallOption) (*GetOrderLogByOrderIdsResp, error) {
	out := new(GetOrderLogByOrderIdsResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetOrderLogByOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) BuyWerwolfItem(ctx context.Context, in *BuyWerwolfItemReq, opts ...grpc.CallOption) (*BuyWerwolfItemResp, error) {
	out := new(BuyWerwolfItemResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/BuyWerwolfItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) BatchGetChannelModeEntry(ctx context.Context, in *BatchGetChannelModeEntryReq, opts ...grpc.CallOption) (*BatchGetChannelModeEntryResp, error) {
	out := new(BatchGetChannelModeEntryResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/BatchGetChannelModeEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) BatchOperateChannelMode(ctx context.Context, in *BatchOperateChannelModeReq, opts ...grpc.CallOption) (*BatchOperateChannelModeResp, error) {
	out := new(BatchOperateChannelModeResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/BatchOperateChannelMode", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelModeMgrClient) BatchGetChannelIdByLayoutType(ctx context.Context, in *BatchGetChannelIdByLayoutTypeReq, opts ...grpc.CallOption) (*BatchGetChannelIdByLayoutTypeResp, error) {
	out := new(BatchGetChannelIdByLayoutTypeResp)
	err := c.cc.Invoke(ctx, "/tt_rev_channel_mode_mgr.ChannelModeMgr/BatchGetChannelIdByLayoutType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelModeMgrServer is the server API for ChannelModeMgr service.
type ChannelModeMgrServer interface {
	// 获取房间开启的玩法列表
	GetChannelMode(context.Context, *GetChannelModeReq) (*GetChannelModeResp, error)
	// 设置房间开启的玩法列表
	SetChannelMode(context.Context, *SetChannelModeReq) (*SetChannelModeResp, error)
	// 获取房间管理员
	GetChannelOperator(context.Context, *GetChannelOperatorReq) (*GetChannelOperatorResp, error)
	// T豆消费数据对账
	GetConsumeTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetConsumeOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	GenFinancialFile(context.Context, *reconcile_v2.GenFinancialFileReq) (*reconcile_v2.GenFinancialFileResp, error)
	// 获取订单明细
	GetOrderLogByOrderIds(context.Context, *GetOrderLogByOrderIdsReq) (*GetOrderLogByOrderIdsResp, error)
	// 花费T豆购买身份/发言次数
	BuyWerwolfItem(context.Context, *BuyWerwolfItemReq) (*BuyWerwolfItemResp, error)
	// 获取房间玩法入口
	BatchGetChannelModeEntry(context.Context, *BatchGetChannelModeEntryReq) (*BatchGetChannelModeEntryResp, error)
	// 管理房间玩法入口
	BatchOperateChannelMode(context.Context, *BatchOperateChannelModeReq) (*BatchOperateChannelModeResp, error)
	// 获取玩法的房间列表
	BatchGetChannelIdByLayoutType(context.Context, *BatchGetChannelIdByLayoutTypeReq) (*BatchGetChannelIdByLayoutTypeResp, error)
}

func RegisterChannelModeMgrServer(s *grpc.Server, srv ChannelModeMgrServer) {
	s.RegisterService(&_ChannelModeMgr_serviceDesc, srv)
}

func _ChannelModeMgr_GetChannelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).GetChannelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetChannelMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).GetChannelMode(ctx, req.(*GetChannelModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_SetChannelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).SetChannelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/SetChannelMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).SetChannelMode(ctx, req.(*SetChannelModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_GetChannelOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelOperatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).GetChannelOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetChannelOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).GetChannelOperator(ctx, req.(*GetChannelOperatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_GetConsumeTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).GetConsumeTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetConsumeTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).GetConsumeTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_GetConsumeOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).GetConsumeOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetConsumeOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).GetConsumeOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_GenFinancialFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.GenFinancialFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).GenFinancialFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/GenFinancialFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).GenFinancialFile(ctx, req.(*reconcile_v2.GenFinancialFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_GetOrderLogByOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderLogByOrderIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).GetOrderLogByOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/GetOrderLogByOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).GetOrderLogByOrderIds(ctx, req.(*GetOrderLogByOrderIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_BuyWerwolfItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BuyWerwolfItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).BuyWerwolfItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/BuyWerwolfItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).BuyWerwolfItem(ctx, req.(*BuyWerwolfItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_BatchGetChannelModeEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelModeEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).BatchGetChannelModeEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/BatchGetChannelModeEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).BatchGetChannelModeEntry(ctx, req.(*BatchGetChannelModeEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_BatchOperateChannelMode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchOperateChannelModeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).BatchOperateChannelMode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/BatchOperateChannelMode",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).BatchOperateChannelMode(ctx, req.(*BatchOperateChannelModeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelModeMgr_BatchGetChannelIdByLayoutType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelIdByLayoutTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelModeMgrServer).BatchGetChannelIdByLayoutType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/tt_rev_channel_mode_mgr.ChannelModeMgr/BatchGetChannelIdByLayoutType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelModeMgrServer).BatchGetChannelIdByLayoutType(ctx, req.(*BatchGetChannelIdByLayoutTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelModeMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "tt_rev_channel_mode_mgr.ChannelModeMgr",
	HandlerType: (*ChannelModeMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelMode",
			Handler:    _ChannelModeMgr_GetChannelMode_Handler,
		},
		{
			MethodName: "SetChannelMode",
			Handler:    _ChannelModeMgr_SetChannelMode_Handler,
		},
		{
			MethodName: "GetChannelOperator",
			Handler:    _ChannelModeMgr_GetChannelOperator_Handler,
		},
		{
			MethodName: "GetConsumeTotalCount",
			Handler:    _ChannelModeMgr_GetConsumeTotalCount_Handler,
		},
		{
			MethodName: "GetConsumeOrderIds",
			Handler:    _ChannelModeMgr_GetConsumeOrderIds_Handler,
		},
		{
			MethodName: "GenFinancialFile",
			Handler:    _ChannelModeMgr_GenFinancialFile_Handler,
		},
		{
			MethodName: "GetOrderLogByOrderIds",
			Handler:    _ChannelModeMgr_GetOrderLogByOrderIds_Handler,
		},
		{
			MethodName: "BuyWerwolfItem",
			Handler:    _ChannelModeMgr_BuyWerwolfItem_Handler,
		},
		{
			MethodName: "BatchGetChannelModeEntry",
			Handler:    _ChannelModeMgr_BatchGetChannelModeEntry_Handler,
		},
		{
			MethodName: "BatchOperateChannelMode",
			Handler:    _ChannelModeMgr_BatchOperateChannelMode_Handler,
		},
		{
			MethodName: "BatchGetChannelIdByLayoutType",
			Handler:    _ChannelModeMgr_BatchGetChannelIdByLayoutType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/tt-rev-channel-mode-mgr/tt-rev-channel-mode-mgr.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/tt-rev-channel-mode-mgr/tt-rev-channel-mode-mgr.proto", fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb)
}

var fileDescriptor_tt_rev_channel_mode_mgr_727da7c22e2f41cb = []byte{
	// 1258 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x57, 0x5d, 0x6e, 0xdb, 0x46,
	0x10, 0x36, 0xfd, 0x23, 0x89, 0xa3, 0xd8, 0xb1, 0x17, 0x49, 0x2c, 0x2b, 0x4d, 0xe3, 0x10, 0x45,
	0x61, 0xa4, 0xb0, 0x8c, 0x28, 0x49, 0x81, 0xb4, 0x40, 0xd1, 0xda, 0x75, 0x0c, 0x35, 0x8e, 0x55,
	0x50, 0x4e, 0x83, 0x16, 0x08, 0x08, 0x96, 0x1c, 0xcb, 0x0b, 0x93, 0x5c, 0x86, 0xbb, 0x72, 0xaa,
	0xb7, 0x3e, 0x15, 0xe8, 0x0d, 0xda, 0x13, 0xf4, 0x08, 0x3d, 0x42, 0x5f, 0x7a, 0x8f, 0xde, 0xa1,
	0x0f, 0x45, 0xb1, 0xbb, 0xa4, 0x2d, 0x52, 0xa2, 0x6c, 0xe5, 0x49, 0xbb, 0xf3, 0xf3, 0xcd, 0xec,
	0xcc, 0xb7, 0xc3, 0x15, 0x7c, 0x2d, 0xc4, 0xce, 0xdb, 0x01, 0xf5, 0xce, 0x38, 0x0d, 0xce, 0x31,
	0xd9, 0x11, 0x62, 0x3b, 0xc1, 0xf3, 0x6d, 0xef, 0xd4, 0x8d, 0x22, 0x0c, 0xb6, 0x43, 0xe6, 0xe3,
	0x76, 0xd8, 0x2f, 0x95, 0xb7, 0xe2, 0x84, 0x09, 0x46, 0xd6, 0x85, 0x70, 0x12, 0x3c, 0x77, 0x52,
	0xb5, 0x23, 0xd5, 0x4e, 0xd8, 0x4f, 0x9a, 0xad, 0x02, 0x3c, 0xfe, 0x24, 0x30, 0xe2, 0x94, 0x45,
	0x3b, 0x2c, 0x16, 0x94, 0x45, 0x3c, 0xfb, 0xd5, 0x40, 0x63, 0xf6, 0x09, 0x7a, 0x2c, 0xf2, 0x68,
	0x80, 0xdb, 0xe7, 0xed, 0xdc, 0x46, 0xdb, 0x5b, 0x6d, 0x58, 0x3b, 0x40, 0xb1, 0xa7, 0xc3, 0xbe,
	0x64, 0x3e, 0xda, 0xf8, 0x96, 0xdc, 0x03, 0xc8, 0x12, 0xa1, 0x7e, 0xc3, 0xd8, 0x34, 0xb6, 0x96,
	0x6d, 0x33, 0x95, 0x74, 0x7c, 0xeb, 0x1f, 0x03, 0x96, 0x53, 0x8f, 0x9e, 0x77, 0x8a, 0x21, 0x92,
	0xbb, 0x60, 0x72, 0xb5, 0xba, 0xb4, 0xaf, 0x69, 0x41, 0xc7, 0x27, 0xf7, 0xa1, 0x9e, 0x2a, 0x23,
	0x37, 0xc4, 0xc6, 0xfc, 0xa6, 0xb1, 0x65, 0xda, 0xa0, 0x45, 0x47, 0x6e, 0x88, 0xd2, 0x20, 0x70,
	0x87, 0x6c, 0x20, 0x1c, 0x31, 0x8c, 0xb1, 0xb1, 0xa0, 0xfc, 0x41, 0x8b, 0x8e, 0x87, 0xb1, 0x32,
	0xa0, 0x1e, 0x8b, 0x1c, 0x8e, 0x01, 0x7a, 0xa2, 0xb1, 0xa8, 0x11, 0xa4, 0xa8, 0xa7, 0x24, 0xe4,
	0x23, 0x58, 0x51, 0x06, 0x83, 0x0b, 0x9b, 0x25, 0x65, 0x73, 0x43, 0x4a, 0x5f, 0x65, 0x56, 0xeb,
	0x50, 0xa5, 0xdc, 0x61, 0x31, 0x46, 0x8d, 0xca, 0xa6, 0xb1, 0x55, 0xb3, 0x2b, 0x94, 0x77, 0x63,
	0x8c, 0xa4, 0x22, 0xf6, 0x1c, 0x69, 0xdb, 0xa8, 0x2a, 0xbf, 0x4a, 0xec, 0x75, 0x3c, 0x16, 0x59,
	0x03, 0x20, 0xc5, 0xea, 0xf0, 0xf8, 0x8a, 0xf2, 0x90, 0x2f, 0xa1, 0xaa, 0x0f, 0xc7, 0x1b, 0xf3,
	0x9b, 0x0b, 0x5b, 0xf5, 0xf6, 0xc7, 0xad, 0x92, 0xee, 0xb6, 0x72, 0x55, 0xb4, 0x33, 0x37, 0xab,
	0x0b, 0x6b, 0xbd, 0x19, 0x9b, 0x92, 0x6f, 0xc1, 0x7c, 0xbe, 0x05, 0xd6, 0x2d, 0x20, 0xbd, 0xb1,
	0x73, 0x58, 0x6f, 0x60, 0xad, 0xf7, 0x8e, 0x0a, 0xef, 0x74, 0x44, 0x31, 0xbd, 0x95, 0x23, 0x15,
	0x9c, 0xcf, 0x55, 0x90, 0xc0, 0xa2, 0x8f, 0xdc, 0x53, 0xbd, 0x33, 0x6d, 0xb5, 0xb6, 0x3e, 0x85,
	0xdb, 0x97, 0xc5, 0xeb, 0xc6, 0x98, 0xb8, 0x82, 0x25, 0xd7, 0xa0, 0x57, 0x0f, 0xee, 0x4c, 0xf2,
	0xbb, 0xba, 0xf0, 0x4d, 0xa8, 0xb1, 0xd4, 0x3c, 0xab, 0x40, 0xb6, 0xb7, 0xde, 0xc0, 0xdd, 0x5d,
	0x57, 0x78, 0xa7, 0xf9, 0x76, 0xee, 0x47, 0x22, 0x19, 0x4e, 0x4a, 0x69, 0x21, 0x8f, 0x5c, 0x60,
	0xe8, 0x7c, 0x91, 0xa1, 0xd6, 0x37, 0xf0, 0x41, 0x39, 0x3c, 0x8f, 0xc9, 0x43, 0x58, 0x3b, 0x75,
	0xb9, 0x83, 0x52, 0x90, 0xd1, 0x20, 0x0d, 0x73, 0xf3, 0xd4, 0xe5, 0xca, 0x30, 0x75, 0xb4, 0xfe,
	0x30, 0xa0, 0xa9, 0xc0, 0xf4, 0xd9, 0xb1, 0xc0, 0x83, 0x0d, 0xa8, 0x79, 0xd4, 0x77, 0x02, 0xca,
	0x45, 0x8a, 0x50, 0xf5, 0xa8, 0x7f, 0x48, 0xb9, 0xb8, 0x32, 0x4d, 0xf2, 0x12, 0x56, 0x74, 0x45,
	0x28, 0x8b, 0x2e, 0x2f, 0xdb, 0xca, 0x14, 0x86, 0x76, 0x33, 0x73, 0xe9, 0x6f, 0x2f, 0xb3, 0xd1,
	0xad, 0x75, 0x2f, 0x2d, 0xea, 0xa4, 0x44, 0x79, 0x6c, 0xfd, 0x65, 0xc0, 0xda, 0xee, 0x60, 0xf8,
	0x1a, 0x93, 0x77, 0x2c, 0x38, 0xe9, 0x08, 0x0c, 0xaf, 0xc1, 0xe3, 0x75, 0xa8, 0x4a, 0x7e, 0x65,
	0x2c, 0x36, 0xed, 0x8a, 0xdc, 0x76, 0x7c, 0x72, 0x1b, 0x2a, 0x1c, 0x85, 0x94, 0x6b, 0x92, 0x2d,
	0x71, 0x14, 0x1d, 0x9f, 0xdc, 0x82, 0xa5, 0x38, 0xa1, 0x1e, 0xaa, 0xa9, 0xb0, 0x6c, 0xeb, 0x8d,
	0x22, 0xaa, 0xc0, 0x50, 0x5a, 0x2f, 0x29, 0x79, 0x45, 0x6e, 0xf5, 0x35, 0x51, 0x0a, 0x35, 0x8a,
	0x2a, 0x0a, 0xa8, 0x26, 0x05, 0x6a, 0x10, 0xad, 0x43, 0xb5, 0xef, 0x6a, 0xe6, 0x57, 0xb5, 0x97,
	0xdc, 0x76, 0x7c, 0xeb, 0x11, 0x98, 0xfb, 0x49, 0xc2, 0x92, 0x4e, 0x74, 0xc2, 0x24, 0xd7, 0x3d,
	0xe6, 0xa3, 0x4a, 0x7d, 0xc9, 0x56, 0x6b, 0xb2, 0x0a, 0x0b, 0x21, 0xef, 0xa7, 0x19, 0xcb, 0xa5,
	0x95, 0x00, 0x29, 0x9e, 0x9d, 0xc7, 0xb2, 0x79, 0x2c, 0xf1, 0x31, 0xc9, 0x8e, 0x6e, 0xda, 0x55,
	0xb5, 0xef, 0xf8, 0xe4, 0x2b, 0x00, 0x94, 0x31, 0x1c, 0x1a, 0x9d, 0x30, 0x85, 0x54, 0x6f, 0x5b,
	0xa5, 0x7d, 0xb9, 0x48, 0xc7, 0x36, 0x31, 0x5b, 0x5a, 0xaf, 0x61, 0xe3, 0x00, 0x45, 0x1a, 0xb3,
	0x2b, 0x71, 0x7b, 0x83, 0x30, 0x74, 0x35, 0xc5, 0xf3, 0xbc, 0x31, 0x66, 0xe1, 0x8d, 0xf5, 0x9f,
	0x01, 0x37, 0x47, 0x61, 0x0f, 0x59, 0x5f, 0xe2, 0x9d, 0x24, 0x2c, 0x74, 0x06, 0x17, 0x5d, 0xac,
	0xca, 0xfd, 0x2b, 0xea, 0xcb, 0x16, 0x0b, 0x37, 0xe9, 0xa3, 0x50, 0x4a, 0x0d, 0x67, 0x6a, 0x89,
	0x54, 0x3f, 0x80, 0x1b, 0xf2, 0x3c, 0x7d, 0x74, 0xb8, 0xc7, 0x92, 0x6c, 0xe0, 0xd7, 0xb5, 0xac,
	0x27, 0x45, 0xb9, 0x3a, 0x2d, 0xe6, 0xeb, 0x74, 0x1f, 0xea, 0x5e, 0x82, 0xae, 0x40, 0x47, 0xd0,
	0x10, 0xd3, 0xf6, 0x82, 0x16, 0x1d, 0xd3, 0x10, 0x65, 0x74, 0x1f, 0xdd, 0xc0, 0x11, 0xec, 0x2c,
	0x9d, 0xf4, 0xa6, 0x6d, 0x4a, 0xc9, 0xb1, 0x14, 0x14, 0xf8, 0x57, 0x2d, 0xf2, 0xef, 0x82, 0x4f,
	0xb5, 0x11, 0x3e, 0x59, 0x5f, 0x40, 0xe3, 0x00, 0x45, 0x76, 0xf6, 0xdd, 0x61, 0x57, 0x27, 0xc3,
	0x65, 0x61, 0x2d, 0x58, 0xce, 0x72, 0xbd, 0xbc, 0x95, 0xa6, 0x5d, 0x4f, 0x13, 0x96, 0x15, 0xb6,
	0xce, 0x54, 0x67, 0x26, 0xf9, 0xf3, 0x98, 0x1c, 0xc1, 0x8a, 0x06, 0x08, 0x58, 0xff, 0x12, 0xa1,
	0xde, 0xde, 0x2a, 0xed, 0x7e, 0xa1, 0x17, 0xf6, 0x0d, 0x96, 0xae, 0x54, 0xb0, 0x3d, 0xd8, 0x2c,
	0x0c, 0xa3, 0x8e, 0xbf, 0x3b, 0x3c, 0xbc, 0x68, 0xa7, 0x4c, 0xba, 0xd0, 0x72, 0x63, 0xac, 0xe5,
	0xbb, 0xf0, 0xe0, 0x0a, 0x90, 0x09, 0x03, 0x39, 0x3f, 0x36, 0x1f, 0x3e, 0x82, 0xe5, 0xdc, 0xfc,
	0x20, 0x75, 0xa8, 0xbe, 0x3a, 0x7a, 0x71, 0xd4, 0x7d, 0x7d, 0xb4, 0x3a, 0x47, 0x6a, 0xb0, 0xd8,
	0xfd, 0x76, 0xff, 0x68, 0xd5, 0x20, 0x26, 0x2c, 0xed, 0x1d, 0x76, 0x7b, 0xfb, 0xab, 0xf3, 0xed,
	0xdf, 0x01, 0x56, 0x46, 0xe6, 0xc8, 0xcb, 0x7e, 0x42, 0xce, 0x60, 0x25, 0x3f, 0x56, 0xc9, 0xc3,
	0xd2, 0xc2, 0x8c, 0xbd, 0x65, 0x9a, 0x9f, 0x5c, 0xdb, 0x96, 0xc7, 0xd6, 0x9c, 0x0c, 0xd6, 0xbb,
	0x6e, 0xb0, 0xde, 0x0c, 0xc1, 0x7a, 0x93, 0x82, 0xbd, 0x1b, 0x7d, 0x5e, 0x64, 0x5f, 0x3a, 0xd2,
	0xba, 0x46, 0xc6, 0x23, 0x9f, 0xd3, 0xe6, 0xce, 0x4c, 0xf6, 0x2a, 0xf0, 0x0b, 0xb8, 0x25, 0x75,
	0x2c, 0xe2, 0x83, 0x10, 0x8f, 0x99, 0x70, 0x83, 0x3d, 0x36, 0x88, 0x04, 0xd9, 0x68, 0xd9, 0xd9,
	0x13, 0xf1, 0xbb, 0x76, 0x4b, 0xde, 0x26, 0x5b, 0xde, 0x49, 0x19, 0xe5, 0x4e, 0x4e, 0xa5, 0xcc,
	0x53, 0xb0, 0x43, 0x7d, 0x0a, 0x0d, 0x96, 0x11, 0x7b, 0x1a, 0x54, 0x5e, 0x35, 0x7a, 0x15, 0xac,
	0x39, 0xf2, 0x3d, 0xac, 0x1e, 0x60, 0xf4, 0x9c, 0x46, 0x6e, 0xe4, 0x51, 0x37, 0x78, 0x4e, 0x03,
	0x24, 0x9b, 0x39, 0x87, 0xa2, 0x5a, 0x42, 0x3e, 0xb8, 0xc2, 0x42, 0x41, 0xff, 0x6c, 0xa8, 0x17,
	0xc9, 0xf8, 0x2d, 0x24, 0x8f, 0xa6, 0x95, 0x70, 0xe2, 0xad, 0x6f, 0xb6, 0x67, 0x75, 0xc9, 0xe8,
	0x95, 0xff, 0x2a, 0x4c, 0xa1, 0xd7, 0xd8, 0xa7, 0x73, 0x0a, 0xbd, 0xc6, 0x3f, 0x35, 0xd6, 0x1c,
	0xf9, 0xd5, 0x80, 0x46, 0xd9, 0xab, 0x84, 0x3c, 0x29, 0xc7, 0x2a, 0x7f, 0x27, 0x35, 0x9f, 0xbe,
	0x87, 0x97, 0xca, 0xe5, 0x17, 0x03, 0xd6, 0x4b, 0xde, 0x0a, 0xe4, 0xf1, 0x74, 0xd0, 0x89, 0xcf,
	0xa0, 0xe6, 0x93, 0xd9, 0x9d, 0x54, 0x22, 0xbf, 0x19, 0x70, 0x6f, 0xea, 0x60, 0x23, 0xcf, 0xae,
	0x7b, 0xc6, 0xb1, 0xa9, 0xda, 0xfc, 0xec, 0x7d, 0x5d, 0x65, 0x6a, 0xcd, 0x0f, 0xff, 0xfd, 0xf3,
	0xef, 0xe3, 0x0d, 0x58, 0x2f, 0xf9, 0xab, 0xb8, 0xfb, 0xf9, 0x0f, 0xcf, 0xfa, 0x2c, 0x70, 0xa3,
	0x7e, 0xeb, 0x69, 0x5b, 0x88, 0x96, 0xc7, 0xc2, 0x1d, 0xf5, 0x27, 0xce, 0x63, 0xc1, 0x0e, 0xc7,
	0xe4, 0x9c, 0x7a, 0xc8, 0xcb, 0xfe, 0x67, 0xfe, 0x58, 0x51, 0xa6, 0x8f, 0xff, 0x0f, 0x00, 0x00,
	0xff, 0xff, 0x7f, 0x20, 0x44, 0x8b, 0xb0, 0x0e, 0x00, 0x00,
}
