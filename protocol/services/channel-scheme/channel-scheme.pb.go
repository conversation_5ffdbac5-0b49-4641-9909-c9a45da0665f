// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/channel-scheme/channel-scheme.proto

package channel_scheme // import "golang.52tt.com/protocol/services/channel-scheme"

/*
buf:lint:ignore DIRECTORY_SAME_PACKAGE
*/

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type Source int32

const (
	Source_UNKNOWN                     Source = 0
	Source_CREATE                      Source = 1
	Source_SWITCH                      Source = 2
	Source_TEMP_CREATE                 Source = 3
	Source_PGC_SWITCH                  Source = 4
	Source_UGC_GAME_DETAIL_TYPE_SWITCH Source = 5
)

var Source_name = map[int32]string{
	0: "UNKNOWN",
	1: "CREATE",
	2: "SWITCH",
	3: "TEMP_CREATE",
	4: "PGC_SWITCH",
	5: "UGC_GAME_DETAIL_TYPE_SWITCH",
}
var Source_value = map[string]int32{
	"UNKNOWN":                     0,
	"CREATE":                      1,
	"SWITCH":                      2,
	"TEMP_CREATE":                 3,
	"PGC_SWITCH":                  4,
	"UGC_GAME_DETAIL_TYPE_SWITCH": 5,
}

func (x Source) String() string {
	return proto.EnumName(Source_name, int32(x))
}
func (Source) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{0}
}

// 玩法业务分类，将业务指定的多个玩法关联起来，形成一个分类
// 同个分类下的玩法共享指定的配置
// 如MT指定群聊派对分类(玩法a,b)，ab玩法共享麦位数(资源)
type SchemeBusinessRelatedType int32

const (
	// Unknown
	SchemeBusinessRelatedType_SchemeBusinessRelatedType_Unknown SchemeBusinessRelatedType = 0
	// MT-群聊派对分类
	SchemeBusinessRelatedType_SchemeBusinessRelatedType_MT_Party SchemeBusinessRelatedType = 1
)

var SchemeBusinessRelatedType_name = map[int32]string{
	0: "SchemeBusinessRelatedType_Unknown",
	1: "SchemeBusinessRelatedType_MT_Party",
}
var SchemeBusinessRelatedType_value = map[string]int32{
	"SchemeBusinessRelatedType_Unknown":  0,
	"SchemeBusinessRelatedType_MT_Party": 1,
}

func (x SchemeBusinessRelatedType) String() string {
	return proto.EnumName(SchemeBusinessRelatedType_name, int32(x))
}
func (SchemeBusinessRelatedType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{1}
}

type SchemeBusinessRelatedResource int32

const (
	// Unknown
	SchemeBusinessRelatedResource_SchemeBusinessRelatedResource_Unknown SchemeBusinessRelatedResource = 0
	// 麦位数
	SchemeBusinessRelatedResource_SchemeBusinessRelatedResource_MicSize SchemeBusinessRelatedResource = 1
)

var SchemeBusinessRelatedResource_name = map[int32]string{
	0: "SchemeBusinessRelatedResource_Unknown",
	1: "SchemeBusinessRelatedResource_MicSize",
}
var SchemeBusinessRelatedResource_value = map[string]int32{
	"SchemeBusinessRelatedResource_Unknown": 0,
	"SchemeBusinessRelatedResource_MicSize": 1,
}

func (x SchemeBusinessRelatedResource) String() string {
	return proto.EnumName(SchemeBusinessRelatedResource_name, int32(x))
}
func (SchemeBusinessRelatedResource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{2}
}

type ChannelSchemeInfo struct {
	SchemeId                 uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName               string   `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	SchemeType               uint32   `protobuf:"varint,3,opt,name=scheme_type,json=schemeType,proto3" json:"scheme_type,omitempty"`
	LayoutType               uint32   `protobuf:"varint,4,opt,name=layout_type,json=layoutType,proto3" json:"layout_type,omitempty"`
	CurMicSize               uint32   `protobuf:"varint,5,opt,name=cur_mic_size,json=curMicSize,proto3" json:"cur_mic_size,omitempty"`
	MicMode                  uint32   `protobuf:"varint,6,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	SchemeSvrDetailType      uint32   `protobuf:"varint,7,opt,name=scheme_svr_detail_type,json=schemeSvrDetailType,proto3" json:"scheme_svr_detail_type,omitempty"`
	MicAudioType             uint32   `protobuf:"varint,8,opt,name=mic_audio_type,json=micAudioType,proto3" json:"mic_audio_type,omitempty"`
	MsTs                     uint64   `protobuf:"varint,9,opt,name=ms_ts,json=msTs,proto3" json:"ms_ts,omitempty"`
	BusinessCategory         uint32   `protobuf:"varint,10,opt,name=business_category,json=businessCategory,proto3" json:"business_category,omitempty"`
	SchemeDetailType         uint32   `protobuf:"varint,11,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	FallbackSchemeDetailType uint32   `protobuf:"varint,12,opt,name=fallback_scheme_detail_type,json=fallbackSchemeDetailType,proto3" json:"fallback_scheme_detail_type,omitempty"`
	ExpectMicSize            uint32   `protobuf:"varint,13,opt,name=expect_mic_size,json=expectMicSize,proto3" json:"expect_mic_size,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *ChannelSchemeInfo) Reset()         { *m = ChannelSchemeInfo{} }
func (m *ChannelSchemeInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelSchemeInfo) ProtoMessage()    {}
func (*ChannelSchemeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{0}
}
func (m *ChannelSchemeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelSchemeInfo.Unmarshal(m, b)
}
func (m *ChannelSchemeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelSchemeInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelSchemeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelSchemeInfo.Merge(dst, src)
}
func (m *ChannelSchemeInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelSchemeInfo.Size(m)
}
func (m *ChannelSchemeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelSchemeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelSchemeInfo proto.InternalMessageInfo

func (m *ChannelSchemeInfo) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *ChannelSchemeInfo) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *ChannelSchemeInfo) GetSchemeType() uint32 {
	if m != nil {
		return m.SchemeType
	}
	return 0
}

func (m *ChannelSchemeInfo) GetLayoutType() uint32 {
	if m != nil {
		return m.LayoutType
	}
	return 0
}

func (m *ChannelSchemeInfo) GetCurMicSize() uint32 {
	if m != nil {
		return m.CurMicSize
	}
	return 0
}

func (m *ChannelSchemeInfo) GetMicMode() uint32 {
	if m != nil {
		return m.MicMode
	}
	return 0
}

func (m *ChannelSchemeInfo) GetSchemeSvrDetailType() uint32 {
	if m != nil {
		return m.SchemeSvrDetailType
	}
	return 0
}

func (m *ChannelSchemeInfo) GetMicAudioType() uint32 {
	if m != nil {
		return m.MicAudioType
	}
	return 0
}

func (m *ChannelSchemeInfo) GetMsTs() uint64 {
	if m != nil {
		return m.MsTs
	}
	return 0
}

func (m *ChannelSchemeInfo) GetBusinessCategory() uint32 {
	if m != nil {
		return m.BusinessCategory
	}
	return 0
}

func (m *ChannelSchemeInfo) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *ChannelSchemeInfo) GetFallbackSchemeDetailType() uint32 {
	if m != nil {
		return m.FallbackSchemeDetailType
	}
	return 0
}

func (m *ChannelSchemeInfo) GetExpectMicSize() uint32 {
	if m != nil {
		return m.ExpectMicSize
	}
	return 0
}

type GetCurChannelSchemeInfoReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurChannelSchemeInfoReq) Reset()         { *m = GetCurChannelSchemeInfoReq{} }
func (m *GetCurChannelSchemeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCurChannelSchemeInfoReq) ProtoMessage()    {}
func (*GetCurChannelSchemeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{1}
}
func (m *GetCurChannelSchemeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurChannelSchemeInfoReq.Unmarshal(m, b)
}
func (m *GetCurChannelSchemeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurChannelSchemeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetCurChannelSchemeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurChannelSchemeInfoReq.Merge(dst, src)
}
func (m *GetCurChannelSchemeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetCurChannelSchemeInfoReq.Size(m)
}
func (m *GetCurChannelSchemeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurChannelSchemeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurChannelSchemeInfoReq proto.InternalMessageInfo

func (m *GetCurChannelSchemeInfoReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetCurChannelSchemeInfoReq) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

type GetCurChannelSchemeInfoResp struct {
	Cid                  uint32             `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeInfo           *ChannelSchemeInfo `protobuf:"bytes,2,opt,name=scheme_info,json=schemeInfo,proto3" json:"scheme_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetCurChannelSchemeInfoResp) Reset()         { *m = GetCurChannelSchemeInfoResp{} }
func (m *GetCurChannelSchemeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetCurChannelSchemeInfoResp) ProtoMessage()    {}
func (*GetCurChannelSchemeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{2}
}
func (m *GetCurChannelSchemeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurChannelSchemeInfoResp.Unmarshal(m, b)
}
func (m *GetCurChannelSchemeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurChannelSchemeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetCurChannelSchemeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurChannelSchemeInfoResp.Merge(dst, src)
}
func (m *GetCurChannelSchemeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetCurChannelSchemeInfoResp.Size(m)
}
func (m *GetCurChannelSchemeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurChannelSchemeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurChannelSchemeInfoResp proto.InternalMessageInfo

func (m *GetCurChannelSchemeInfoResp) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetCurChannelSchemeInfoResp) GetSchemeInfo() *ChannelSchemeInfo {
	if m != nil {
		return m.SchemeInfo
	}
	return nil
}

// 因为目前ugc和pgc玩法信息分开存储了，所以分开提供批量接口
type BatGetUgcChannelSchemeInfoReq struct {
	CidList              []uint32 `protobuf:"varint,1,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUgcChannelSchemeInfoReq) Reset()         { *m = BatGetUgcChannelSchemeInfoReq{} }
func (m *BatGetUgcChannelSchemeInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUgcChannelSchemeInfoReq) ProtoMessage()    {}
func (*BatGetUgcChannelSchemeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{3}
}
func (m *BatGetUgcChannelSchemeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUgcChannelSchemeInfoReq.Unmarshal(m, b)
}
func (m *BatGetUgcChannelSchemeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUgcChannelSchemeInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatGetUgcChannelSchemeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUgcChannelSchemeInfoReq.Merge(dst, src)
}
func (m *BatGetUgcChannelSchemeInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatGetUgcChannelSchemeInfoReq.Size(m)
}
func (m *BatGetUgcChannelSchemeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUgcChannelSchemeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUgcChannelSchemeInfoReq proto.InternalMessageInfo

func (m *BatGetUgcChannelSchemeInfoReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type BatGetUgcChannelSchemeInfoResp struct {
	SchemeInfoList       map[uint32]*ChannelSchemeInfo `protobuf:"bytes,1,rep,name=scheme_info_list,json=schemeInfoList,proto3" json:"scheme_info_list,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatGetUgcChannelSchemeInfoResp) Reset()         { *m = BatGetUgcChannelSchemeInfoResp{} }
func (m *BatGetUgcChannelSchemeInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUgcChannelSchemeInfoResp) ProtoMessage()    {}
func (*BatGetUgcChannelSchemeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{4}
}
func (m *BatGetUgcChannelSchemeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUgcChannelSchemeInfoResp.Unmarshal(m, b)
}
func (m *BatGetUgcChannelSchemeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUgcChannelSchemeInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatGetUgcChannelSchemeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUgcChannelSchemeInfoResp.Merge(dst, src)
}
func (m *BatGetUgcChannelSchemeInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatGetUgcChannelSchemeInfoResp.Size(m)
}
func (m *BatGetUgcChannelSchemeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUgcChannelSchemeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUgcChannelSchemeInfoResp proto.InternalMessageInfo

func (m *BatGetUgcChannelSchemeInfoResp) GetSchemeInfoList() map[uint32]*ChannelSchemeInfo {
	if m != nil {
		return m.SchemeInfoList
	}
	return nil
}

type SetCurChannelSchemeReq struct {
	OpUid                uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeId             uint32   `protobuf:"varint,3,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	Source               Source   `protobuf:"varint,4,opt,name=source,proto3,enum=channel_scheme.Source" json:"source,omitempty"`
	SchemeDetailType     uint32   `protobuf:"varint,5,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	NewSource            uint32   `protobuf:"varint,6,opt,name=new_source,json=newSource,proto3" json:"new_source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCurChannelSchemeReq) Reset()         { *m = SetCurChannelSchemeReq{} }
func (m *SetCurChannelSchemeReq) String() string { return proto.CompactTextString(m) }
func (*SetCurChannelSchemeReq) ProtoMessage()    {}
func (*SetCurChannelSchemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{5}
}
func (m *SetCurChannelSchemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCurChannelSchemeReq.Unmarshal(m, b)
}
func (m *SetCurChannelSchemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCurChannelSchemeReq.Marshal(b, m, deterministic)
}
func (dst *SetCurChannelSchemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCurChannelSchemeReq.Merge(dst, src)
}
func (m *SetCurChannelSchemeReq) XXX_Size() int {
	return xxx_messageInfo_SetCurChannelSchemeReq.Size(m)
}
func (m *SetCurChannelSchemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCurChannelSchemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetCurChannelSchemeReq proto.InternalMessageInfo

func (m *SetCurChannelSchemeReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SetCurChannelSchemeReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetCurChannelSchemeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SetCurChannelSchemeReq) GetSource() Source {
	if m != nil {
		return m.Source
	}
	return Source_UNKNOWN
}

func (m *SetCurChannelSchemeReq) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *SetCurChannelSchemeReq) GetNewSource() uint32 {
	if m != nil {
		return m.NewSource
	}
	return 0
}

type SetCurChannelSchemeResp struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName           string   `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	SchemeType           uint32   `protobuf:"varint,3,opt,name=scheme_type,json=schemeType,proto3" json:"scheme_type,omitempty"`
	SwitchMsTs           uint64   `protobuf:"varint,4,opt,name=switch_ms_ts,json=switchMsTs,proto3" json:"switch_ms_ts,omitempty"`
	SchemeDetailType     uint32   `protobuf:"varint,5,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetCurChannelSchemeResp) Reset()         { *m = SetCurChannelSchemeResp{} }
func (m *SetCurChannelSchemeResp) String() string { return proto.CompactTextString(m) }
func (*SetCurChannelSchemeResp) ProtoMessage()    {}
func (*SetCurChannelSchemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{6}
}
func (m *SetCurChannelSchemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetCurChannelSchemeResp.Unmarshal(m, b)
}
func (m *SetCurChannelSchemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetCurChannelSchemeResp.Marshal(b, m, deterministic)
}
func (dst *SetCurChannelSchemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetCurChannelSchemeResp.Merge(dst, src)
}
func (m *SetCurChannelSchemeResp) XXX_Size() int {
	return xxx_messageInfo_SetCurChannelSchemeResp.Size(m)
}
func (m *SetCurChannelSchemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetCurChannelSchemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetCurChannelSchemeResp proto.InternalMessageInfo

func (m *SetCurChannelSchemeResp) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SetCurChannelSchemeResp) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *SetCurChannelSchemeResp) GetSchemeType() uint32 {
	if m != nil {
		return m.SchemeType
	}
	return 0
}

func (m *SetCurChannelSchemeResp) GetSwitchMsTs() uint64 {
	if m != nil {
		return m.SwitchMsTs
	}
	return 0
}

func (m *SetCurChannelSchemeResp) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

type GetUgcSchemeMemberSizeLimitReq struct {
	Cid                  uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeId             uint32   `protobuf:"varint,2,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUgcSchemeMemberSizeLimitReq) Reset()         { *m = GetUgcSchemeMemberSizeLimitReq{} }
func (m *GetUgcSchemeMemberSizeLimitReq) String() string { return proto.CompactTextString(m) }
func (*GetUgcSchemeMemberSizeLimitReq) ProtoMessage()    {}
func (*GetUgcSchemeMemberSizeLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{7}
}
func (m *GetUgcSchemeMemberSizeLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUgcSchemeMemberSizeLimitReq.Unmarshal(m, b)
}
func (m *GetUgcSchemeMemberSizeLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUgcSchemeMemberSizeLimitReq.Marshal(b, m, deterministic)
}
func (dst *GetUgcSchemeMemberSizeLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUgcSchemeMemberSizeLimitReq.Merge(dst, src)
}
func (m *GetUgcSchemeMemberSizeLimitReq) XXX_Size() int {
	return xxx_messageInfo_GetUgcSchemeMemberSizeLimitReq.Size(m)
}
func (m *GetUgcSchemeMemberSizeLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUgcSchemeMemberSizeLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUgcSchemeMemberSizeLimitReq proto.InternalMessageInfo

func (m *GetUgcSchemeMemberSizeLimitReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetUgcSchemeMemberSizeLimitReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

type GetUgcSchemeMemberSizeLimitResp struct {
	MaxMemberSize        uint32   `protobuf:"varint,1,opt,name=max_member_size,json=maxMemberSize,proto3" json:"max_member_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUgcSchemeMemberSizeLimitResp) Reset()         { *m = GetUgcSchemeMemberSizeLimitResp{} }
func (m *GetUgcSchemeMemberSizeLimitResp) String() string { return proto.CompactTextString(m) }
func (*GetUgcSchemeMemberSizeLimitResp) ProtoMessage()    {}
func (*GetUgcSchemeMemberSizeLimitResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{8}
}
func (m *GetUgcSchemeMemberSizeLimitResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUgcSchemeMemberSizeLimitResp.Unmarshal(m, b)
}
func (m *GetUgcSchemeMemberSizeLimitResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUgcSchemeMemberSizeLimitResp.Marshal(b, m, deterministic)
}
func (dst *GetUgcSchemeMemberSizeLimitResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUgcSchemeMemberSizeLimitResp.Merge(dst, src)
}
func (m *GetUgcSchemeMemberSizeLimitResp) XXX_Size() int {
	return xxx_messageInfo_GetUgcSchemeMemberSizeLimitResp.Size(m)
}
func (m *GetUgcSchemeMemberSizeLimitResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUgcSchemeMemberSizeLimitResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUgcSchemeMemberSizeLimitResp proto.InternalMessageInfo

func (m *GetUgcSchemeMemberSizeLimitResp) GetMaxMemberSize() uint32 {
	if m != nil {
		return m.MaxMemberSize
	}
	return 0
}

type SwitchSchemeDetailTypeByVirtualTypeReq struct {
	OpUid                     uint32   `protobuf:"varint,1,opt,name=op_uid,json=opUid,proto3" json:"op_uid,omitempty"`
	Cid                       uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeId                  uint32   `protobuf:"varint,3,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeDetailType          uint32   `protobuf:"varint,4,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	SchemeVirtualType         uint32   `protobuf:"varint,5,opt,name=scheme_virtual_type,json=schemeVirtualType,proto3" json:"scheme_virtual_type,omitempty"`
	VirtualTypeFollowSchemeId bool     `protobuf:"varint,6,opt,name=virtual_type_follow_scheme_id,json=virtualTypeFollowSchemeId,proto3" json:"virtual_type_follow_scheme_id,omitempty"`
	NewSource                 uint32   `protobuf:"varint,7,opt,name=new_source,json=newSource,proto3" json:"new_source,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) Reset() {
	*m = SwitchSchemeDetailTypeByVirtualTypeReq{}
}
func (m *SwitchSchemeDetailTypeByVirtualTypeReq) String() string { return proto.CompactTextString(m) }
func (*SwitchSchemeDetailTypeByVirtualTypeReq) ProtoMessage()    {}
func (*SwitchSchemeDetailTypeByVirtualTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{9}
}
func (m *SwitchSchemeDetailTypeByVirtualTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeReq.Unmarshal(m, b)
}
func (m *SwitchSchemeDetailTypeByVirtualTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeReq.Marshal(b, m, deterministic)
}
func (dst *SwitchSchemeDetailTypeByVirtualTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeReq.Merge(dst, src)
}
func (m *SwitchSchemeDetailTypeByVirtualTypeReq) XXX_Size() int {
	return xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeReq.Size(m)
}
func (m *SwitchSchemeDetailTypeByVirtualTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeReq proto.InternalMessageInfo

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetSchemeVirtualType() uint32 {
	if m != nil {
		return m.SchemeVirtualType
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetVirtualTypeFollowSchemeId() bool {
	if m != nil {
		return m.VirtualTypeFollowSchemeId
	}
	return false
}

func (m *SwitchSchemeDetailTypeByVirtualTypeReq) GetNewSource() uint32 {
	if m != nil {
		return m.NewSource
	}
	return 0
}

type SwitchSchemeDetailTypeByVirtualTypeResp struct {
	SchemeId             uint32   `protobuf:"varint,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	SchemeName           string   `protobuf:"bytes,2,opt,name=scheme_name,json=schemeName,proto3" json:"scheme_name,omitempty"`
	SchemeType           uint32   `protobuf:"varint,3,opt,name=scheme_type,json=schemeType,proto3" json:"scheme_type,omitempty"`
	SchemeDetailType     uint32   `protobuf:"varint,4,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	SwitchMsTs           uint64   `protobuf:"varint,5,opt,name=switch_ms_ts,json=switchMsTs,proto3" json:"switch_ms_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchSchemeDetailTypeByVirtualTypeResp) Reset() {
	*m = SwitchSchemeDetailTypeByVirtualTypeResp{}
}
func (m *SwitchSchemeDetailTypeByVirtualTypeResp) String() string { return proto.CompactTextString(m) }
func (*SwitchSchemeDetailTypeByVirtualTypeResp) ProtoMessage()    {}
func (*SwitchSchemeDetailTypeByVirtualTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{10}
}
func (m *SwitchSchemeDetailTypeByVirtualTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeResp.Unmarshal(m, b)
}
func (m *SwitchSchemeDetailTypeByVirtualTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeResp.Marshal(b, m, deterministic)
}
func (dst *SwitchSchemeDetailTypeByVirtualTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeResp.Merge(dst, src)
}
func (m *SwitchSchemeDetailTypeByVirtualTypeResp) XXX_Size() int {
	return xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeResp.Size(m)
}
func (m *SwitchSchemeDetailTypeByVirtualTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSchemeDetailTypeByVirtualTypeResp proto.InternalMessageInfo

func (m *SwitchSchemeDetailTypeByVirtualTypeResp) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeResp) GetSchemeName() string {
	if m != nil {
		return m.SchemeName
	}
	return ""
}

func (m *SwitchSchemeDetailTypeByVirtualTypeResp) GetSchemeType() uint32 {
	if m != nil {
		return m.SchemeType
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeResp) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *SwitchSchemeDetailTypeByVirtualTypeResp) GetSwitchMsTs() uint64 {
	if m != nil {
		return m.SwitchMsTs
	}
	return 0
}

type GetSchemeDetailTypeByVirtualTypeReq struct {
	Cid                       uint32   `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	SchemeVirtualType         uint32   `protobuf:"varint,2,opt,name=scheme_virtual_type,json=schemeVirtualType,proto3" json:"scheme_virtual_type,omitempty"`
	VirtualTypeFollowSchemeId bool     `protobuf:"varint,3,opt,name=virtual_type_follow_scheme_id,json=virtualTypeFollowSchemeId,proto3" json:"virtual_type_follow_scheme_id,omitempty"`
	SchemeId                  uint32   `protobuf:"varint,4,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *GetSchemeDetailTypeByVirtualTypeReq) Reset()         { *m = GetSchemeDetailTypeByVirtualTypeReq{} }
func (m *GetSchemeDetailTypeByVirtualTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetSchemeDetailTypeByVirtualTypeReq) ProtoMessage()    {}
func (*GetSchemeDetailTypeByVirtualTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{11}
}
func (m *GetSchemeDetailTypeByVirtualTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeReq.Unmarshal(m, b)
}
func (m *GetSchemeDetailTypeByVirtualTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetSchemeDetailTypeByVirtualTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeReq.Merge(dst, src)
}
func (m *GetSchemeDetailTypeByVirtualTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeReq.Size(m)
}
func (m *GetSchemeDetailTypeByVirtualTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeReq proto.InternalMessageInfo

func (m *GetSchemeDetailTypeByVirtualTypeReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetSchemeDetailTypeByVirtualTypeReq) GetSchemeVirtualType() uint32 {
	if m != nil {
		return m.SchemeVirtualType
	}
	return 0
}

func (m *GetSchemeDetailTypeByVirtualTypeReq) GetVirtualTypeFollowSchemeId() bool {
	if m != nil {
		return m.VirtualTypeFollowSchemeId
	}
	return false
}

func (m *GetSchemeDetailTypeByVirtualTypeReq) GetSchemeId() uint32 {
	if m != nil {
		return m.SchemeId
	}
	return 0
}

type GetSchemeDetailTypeByVirtualTypeResp struct {
	SchemeDetailType     uint32   `protobuf:"varint,1,opt,name=scheme_detail_type,json=schemeDetailType,proto3" json:"scheme_detail_type,omitempty"`
	SwitchMsTs           uint64   `protobuf:"varint,2,opt,name=switch_ms_ts,json=switchMsTs,proto3" json:"switch_ms_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSchemeDetailTypeByVirtualTypeResp) Reset()         { *m = GetSchemeDetailTypeByVirtualTypeResp{} }
func (m *GetSchemeDetailTypeByVirtualTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetSchemeDetailTypeByVirtualTypeResp) ProtoMessage()    {}
func (*GetSchemeDetailTypeByVirtualTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{12}
}
func (m *GetSchemeDetailTypeByVirtualTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeResp.Unmarshal(m, b)
}
func (m *GetSchemeDetailTypeByVirtualTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetSchemeDetailTypeByVirtualTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeResp.Merge(dst, src)
}
func (m *GetSchemeDetailTypeByVirtualTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeResp.Size(m)
}
func (m *GetSchemeDetailTypeByVirtualTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchemeDetailTypeByVirtualTypeResp proto.InternalMessageInfo

func (m *GetSchemeDetailTypeByVirtualTypeResp) GetSchemeDetailType() uint32 {
	if m != nil {
		return m.SchemeDetailType
	}
	return 0
}

func (m *GetSchemeDetailTypeByVirtualTypeResp) GetSwitchMsTs() uint64 {
	if m != nil {
		return m.SwitchMsTs
	}
	return 0
}

// 设置分类下的玩法的麦位数
type SetSchemeBusinessRelatedMicSizeReq struct {
	// cid
	Cid uint32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	// 麦位数
	MicSize uint32 `protobuf:"varint,2,opt,name=mic_size,json=micSize,proto3" json:"mic_size,omitempty"`
	// 业务分类see SchemeBusinessRelatedType
	SchemeRelatedType    SchemeBusinessRelatedType `protobuf:"varint,3,opt,name=scheme_related_type,json=schemeRelatedType,proto3,enum=channel_scheme.SchemeBusinessRelatedType" json:"scheme_related_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *SetSchemeBusinessRelatedMicSizeReq) Reset()         { *m = SetSchemeBusinessRelatedMicSizeReq{} }
func (m *SetSchemeBusinessRelatedMicSizeReq) String() string { return proto.CompactTextString(m) }
func (*SetSchemeBusinessRelatedMicSizeReq) ProtoMessage()    {}
func (*SetSchemeBusinessRelatedMicSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{13}
}
func (m *SetSchemeBusinessRelatedMicSizeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSchemeBusinessRelatedMicSizeReq.Unmarshal(m, b)
}
func (m *SetSchemeBusinessRelatedMicSizeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSchemeBusinessRelatedMicSizeReq.Marshal(b, m, deterministic)
}
func (dst *SetSchemeBusinessRelatedMicSizeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSchemeBusinessRelatedMicSizeReq.Merge(dst, src)
}
func (m *SetSchemeBusinessRelatedMicSizeReq) XXX_Size() int {
	return xxx_messageInfo_SetSchemeBusinessRelatedMicSizeReq.Size(m)
}
func (m *SetSchemeBusinessRelatedMicSizeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSchemeBusinessRelatedMicSizeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSchemeBusinessRelatedMicSizeReq proto.InternalMessageInfo

func (m *SetSchemeBusinessRelatedMicSizeReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *SetSchemeBusinessRelatedMicSizeReq) GetMicSize() uint32 {
	if m != nil {
		return m.MicSize
	}
	return 0
}

func (m *SetSchemeBusinessRelatedMicSizeReq) GetSchemeRelatedType() SchemeBusinessRelatedType {
	if m != nil {
		return m.SchemeRelatedType
	}
	return SchemeBusinessRelatedType_SchemeBusinessRelatedType_Unknown
}

type SetSchemeBusinessRelatedMicSizeResp struct {
	// 设置时服务端毫秒时间戳
	Ts                   uint64   `protobuf:"varint,1,opt,name=ts,proto3" json:"ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSchemeBusinessRelatedMicSizeResp) Reset()         { *m = SetSchemeBusinessRelatedMicSizeResp{} }
func (m *SetSchemeBusinessRelatedMicSizeResp) String() string { return proto.CompactTextString(m) }
func (*SetSchemeBusinessRelatedMicSizeResp) ProtoMessage()    {}
func (*SetSchemeBusinessRelatedMicSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{14}
}
func (m *SetSchemeBusinessRelatedMicSizeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSchemeBusinessRelatedMicSizeResp.Unmarshal(m, b)
}
func (m *SetSchemeBusinessRelatedMicSizeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSchemeBusinessRelatedMicSizeResp.Marshal(b, m, deterministic)
}
func (dst *SetSchemeBusinessRelatedMicSizeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSchemeBusinessRelatedMicSizeResp.Merge(dst, src)
}
func (m *SetSchemeBusinessRelatedMicSizeResp) XXX_Size() int {
	return xxx_messageInfo_SetSchemeBusinessRelatedMicSizeResp.Size(m)
}
func (m *SetSchemeBusinessRelatedMicSizeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSchemeBusinessRelatedMicSizeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSchemeBusinessRelatedMicSizeResp proto.InternalMessageInfo

func (m *SetSchemeBusinessRelatedMicSizeResp) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

// 获取分类下的玩法的麦位数
type GetSchemeBusinessRelatedMicSizeReq struct {
	// cid
	Cid uint32 `protobuf:"varint,1,opt,name=cid,proto3" json:"cid,omitempty"`
	// 业务分类see SchemeBusinessRelatedType
	SchemeRelatedType    SchemeBusinessRelatedType `protobuf:"varint,2,opt,name=scheme_related_type,json=schemeRelatedType,proto3,enum=channel_scheme.SchemeBusinessRelatedType" json:"scheme_related_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetSchemeBusinessRelatedMicSizeReq) Reset()         { *m = GetSchemeBusinessRelatedMicSizeReq{} }
func (m *GetSchemeBusinessRelatedMicSizeReq) String() string { return proto.CompactTextString(m) }
func (*GetSchemeBusinessRelatedMicSizeReq) ProtoMessage()    {}
func (*GetSchemeBusinessRelatedMicSizeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{15}
}
func (m *GetSchemeBusinessRelatedMicSizeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchemeBusinessRelatedMicSizeReq.Unmarshal(m, b)
}
func (m *GetSchemeBusinessRelatedMicSizeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchemeBusinessRelatedMicSizeReq.Marshal(b, m, deterministic)
}
func (dst *GetSchemeBusinessRelatedMicSizeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchemeBusinessRelatedMicSizeReq.Merge(dst, src)
}
func (m *GetSchemeBusinessRelatedMicSizeReq) XXX_Size() int {
	return xxx_messageInfo_GetSchemeBusinessRelatedMicSizeReq.Size(m)
}
func (m *GetSchemeBusinessRelatedMicSizeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchemeBusinessRelatedMicSizeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchemeBusinessRelatedMicSizeReq proto.InternalMessageInfo

func (m *GetSchemeBusinessRelatedMicSizeReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *GetSchemeBusinessRelatedMicSizeReq) GetSchemeRelatedType() SchemeBusinessRelatedType {
	if m != nil {
		return m.SchemeRelatedType
	}
	return SchemeBusinessRelatedType_SchemeBusinessRelatedType_Unknown
}

type GetSchemeBusinessRelatedMicSizeResp struct {
	// 麦位数
	MicSize              uint32   `protobuf:"varint,1,opt,name=mic_size,json=micSize,proto3" json:"mic_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSchemeBusinessRelatedMicSizeResp) Reset()         { *m = GetSchemeBusinessRelatedMicSizeResp{} }
func (m *GetSchemeBusinessRelatedMicSizeResp) String() string { return proto.CompactTextString(m) }
func (*GetSchemeBusinessRelatedMicSizeResp) ProtoMessage()    {}
func (*GetSchemeBusinessRelatedMicSizeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{16}
}
func (m *GetSchemeBusinessRelatedMicSizeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchemeBusinessRelatedMicSizeResp.Unmarshal(m, b)
}
func (m *GetSchemeBusinessRelatedMicSizeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchemeBusinessRelatedMicSizeResp.Marshal(b, m, deterministic)
}
func (dst *GetSchemeBusinessRelatedMicSizeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchemeBusinessRelatedMicSizeResp.Merge(dst, src)
}
func (m *GetSchemeBusinessRelatedMicSizeResp) XXX_Size() int {
	return xxx_messageInfo_GetSchemeBusinessRelatedMicSizeResp.Size(m)
}
func (m *GetSchemeBusinessRelatedMicSizeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchemeBusinessRelatedMicSizeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchemeBusinessRelatedMicSizeResp proto.InternalMessageInfo

func (m *GetSchemeBusinessRelatedMicSizeResp) GetMicSize() uint32 {
	if m != nil {
		return m.MicSize
	}
	return 0
}

// 更新分类包含的玩法
type UpdateBusinessRelatedSchemeReq struct {
	// 业务分类
	SchemeRelatedType SchemeBusinessRelatedType `protobuf:"varint,1,opt,name=scheme_related_type,json=schemeRelatedType,proto3,enum=channel_scheme.SchemeBusinessRelatedType" json:"scheme_related_type,omitempty"`
	// 资源类型
	ResourceType SchemeBusinessRelatedResource `protobuf:"varint,2,opt,name=resource_type,json=resourceType,proto3,enum=channel_scheme.SchemeBusinessRelatedResource" json:"resource_type,omitempty"`
	// 玩法id列表
	SchemeIdList         []uint32 `protobuf:"varint,3,rep,packed,name=scheme_id_list,json=schemeIdList,proto3" json:"scheme_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBusinessRelatedSchemeReq) Reset()         { *m = UpdateBusinessRelatedSchemeReq{} }
func (m *UpdateBusinessRelatedSchemeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBusinessRelatedSchemeReq) ProtoMessage()    {}
func (*UpdateBusinessRelatedSchemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{17}
}
func (m *UpdateBusinessRelatedSchemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBusinessRelatedSchemeReq.Unmarshal(m, b)
}
func (m *UpdateBusinessRelatedSchemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBusinessRelatedSchemeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBusinessRelatedSchemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBusinessRelatedSchemeReq.Merge(dst, src)
}
func (m *UpdateBusinessRelatedSchemeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBusinessRelatedSchemeReq.Size(m)
}
func (m *UpdateBusinessRelatedSchemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBusinessRelatedSchemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBusinessRelatedSchemeReq proto.InternalMessageInfo

func (m *UpdateBusinessRelatedSchemeReq) GetSchemeRelatedType() SchemeBusinessRelatedType {
	if m != nil {
		return m.SchemeRelatedType
	}
	return SchemeBusinessRelatedType_SchemeBusinessRelatedType_Unknown
}

func (m *UpdateBusinessRelatedSchemeReq) GetResourceType() SchemeBusinessRelatedResource {
	if m != nil {
		return m.ResourceType
	}
	return SchemeBusinessRelatedResource_SchemeBusinessRelatedResource_Unknown
}

func (m *UpdateBusinessRelatedSchemeReq) GetSchemeIdList() []uint32 {
	if m != nil {
		return m.SchemeIdList
	}
	return nil
}

type UpdateBusinessRelatedSchemeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBusinessRelatedSchemeResp) Reset()         { *m = UpdateBusinessRelatedSchemeResp{} }
func (m *UpdateBusinessRelatedSchemeResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBusinessRelatedSchemeResp) ProtoMessage()    {}
func (*UpdateBusinessRelatedSchemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{18}
}
func (m *UpdateBusinessRelatedSchemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBusinessRelatedSchemeResp.Unmarshal(m, b)
}
func (m *UpdateBusinessRelatedSchemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBusinessRelatedSchemeResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBusinessRelatedSchemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBusinessRelatedSchemeResp.Merge(dst, src)
}
func (m *UpdateBusinessRelatedSchemeResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBusinessRelatedSchemeResp.Size(m)
}
func (m *UpdateBusinessRelatedSchemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBusinessRelatedSchemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBusinessRelatedSchemeResp proto.InternalMessageInfo

type GetBusinessRelatedSchemeReq struct {
	// 业务分类
	SchemeRelatedType SchemeBusinessRelatedType `protobuf:"varint,1,opt,name=scheme_related_type,json=schemeRelatedType,proto3,enum=channel_scheme.SchemeBusinessRelatedType" json:"scheme_related_type,omitempty"`
	// 资源类型
	ResourceType         SchemeBusinessRelatedResource `protobuf:"varint,2,opt,name=resource_type,json=resourceType,proto3,enum=channel_scheme.SchemeBusinessRelatedResource" json:"resource_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetBusinessRelatedSchemeReq) Reset()         { *m = GetBusinessRelatedSchemeReq{} }
func (m *GetBusinessRelatedSchemeReq) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRelatedSchemeReq) ProtoMessage()    {}
func (*GetBusinessRelatedSchemeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{19}
}
func (m *GetBusinessRelatedSchemeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRelatedSchemeReq.Unmarshal(m, b)
}
func (m *GetBusinessRelatedSchemeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRelatedSchemeReq.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRelatedSchemeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRelatedSchemeReq.Merge(dst, src)
}
func (m *GetBusinessRelatedSchemeReq) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRelatedSchemeReq.Size(m)
}
func (m *GetBusinessRelatedSchemeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRelatedSchemeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRelatedSchemeReq proto.InternalMessageInfo

func (m *GetBusinessRelatedSchemeReq) GetSchemeRelatedType() SchemeBusinessRelatedType {
	if m != nil {
		return m.SchemeRelatedType
	}
	return SchemeBusinessRelatedType_SchemeBusinessRelatedType_Unknown
}

func (m *GetBusinessRelatedSchemeReq) GetResourceType() SchemeBusinessRelatedResource {
	if m != nil {
		return m.ResourceType
	}
	return SchemeBusinessRelatedResource_SchemeBusinessRelatedResource_Unknown
}

type GetBusinessRelatedSchemeResp struct {
	// 玩法id列表
	SchemeIdList         []uint32 `protobuf:"varint,1,rep,packed,name=scheme_id_list,json=schemeIdList,proto3" json:"scheme_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBusinessRelatedSchemeResp) Reset()         { *m = GetBusinessRelatedSchemeResp{} }
func (m *GetBusinessRelatedSchemeResp) String() string { return proto.CompactTextString(m) }
func (*GetBusinessRelatedSchemeResp) ProtoMessage()    {}
func (*GetBusinessRelatedSchemeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_scheme_8b6f18121b101c09, []int{20}
}
func (m *GetBusinessRelatedSchemeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBusinessRelatedSchemeResp.Unmarshal(m, b)
}
func (m *GetBusinessRelatedSchemeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBusinessRelatedSchemeResp.Marshal(b, m, deterministic)
}
func (dst *GetBusinessRelatedSchemeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBusinessRelatedSchemeResp.Merge(dst, src)
}
func (m *GetBusinessRelatedSchemeResp) XXX_Size() int {
	return xxx_messageInfo_GetBusinessRelatedSchemeResp.Size(m)
}
func (m *GetBusinessRelatedSchemeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBusinessRelatedSchemeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBusinessRelatedSchemeResp proto.InternalMessageInfo

func (m *GetBusinessRelatedSchemeResp) GetSchemeIdList() []uint32 {
	if m != nil {
		return m.SchemeIdList
	}
	return nil
}

func init() {
	proto.RegisterType((*ChannelSchemeInfo)(nil), "channel_scheme.ChannelSchemeInfo")
	proto.RegisterType((*GetCurChannelSchemeInfoReq)(nil), "channel_scheme.GetCurChannelSchemeInfoReq")
	proto.RegisterType((*GetCurChannelSchemeInfoResp)(nil), "channel_scheme.GetCurChannelSchemeInfoResp")
	proto.RegisterType((*BatGetUgcChannelSchemeInfoReq)(nil), "channel_scheme.BatGetUgcChannelSchemeInfoReq")
	proto.RegisterType((*BatGetUgcChannelSchemeInfoResp)(nil), "channel_scheme.BatGetUgcChannelSchemeInfoResp")
	proto.RegisterMapType((map[uint32]*ChannelSchemeInfo)(nil), "channel_scheme.BatGetUgcChannelSchemeInfoResp.SchemeInfoListEntry")
	proto.RegisterType((*SetCurChannelSchemeReq)(nil), "channel_scheme.SetCurChannelSchemeReq")
	proto.RegisterType((*SetCurChannelSchemeResp)(nil), "channel_scheme.SetCurChannelSchemeResp")
	proto.RegisterType((*GetUgcSchemeMemberSizeLimitReq)(nil), "channel_scheme.GetUgcSchemeMemberSizeLimitReq")
	proto.RegisterType((*GetUgcSchemeMemberSizeLimitResp)(nil), "channel_scheme.GetUgcSchemeMemberSizeLimitResp")
	proto.RegisterType((*SwitchSchemeDetailTypeByVirtualTypeReq)(nil), "channel_scheme.SwitchSchemeDetailTypeByVirtualTypeReq")
	proto.RegisterType((*SwitchSchemeDetailTypeByVirtualTypeResp)(nil), "channel_scheme.SwitchSchemeDetailTypeByVirtualTypeResp")
	proto.RegisterType((*GetSchemeDetailTypeByVirtualTypeReq)(nil), "channel_scheme.GetSchemeDetailTypeByVirtualTypeReq")
	proto.RegisterType((*GetSchemeDetailTypeByVirtualTypeResp)(nil), "channel_scheme.GetSchemeDetailTypeByVirtualTypeResp")
	proto.RegisterType((*SetSchemeBusinessRelatedMicSizeReq)(nil), "channel_scheme.SetSchemeBusinessRelatedMicSizeReq")
	proto.RegisterType((*SetSchemeBusinessRelatedMicSizeResp)(nil), "channel_scheme.SetSchemeBusinessRelatedMicSizeResp")
	proto.RegisterType((*GetSchemeBusinessRelatedMicSizeReq)(nil), "channel_scheme.GetSchemeBusinessRelatedMicSizeReq")
	proto.RegisterType((*GetSchemeBusinessRelatedMicSizeResp)(nil), "channel_scheme.GetSchemeBusinessRelatedMicSizeResp")
	proto.RegisterType((*UpdateBusinessRelatedSchemeReq)(nil), "channel_scheme.UpdateBusinessRelatedSchemeReq")
	proto.RegisterType((*UpdateBusinessRelatedSchemeResp)(nil), "channel_scheme.UpdateBusinessRelatedSchemeResp")
	proto.RegisterType((*GetBusinessRelatedSchemeReq)(nil), "channel_scheme.GetBusinessRelatedSchemeReq")
	proto.RegisterType((*GetBusinessRelatedSchemeResp)(nil), "channel_scheme.GetBusinessRelatedSchemeResp")
	proto.RegisterEnum("channel_scheme.Source", Source_name, Source_value)
	proto.RegisterEnum("channel_scheme.SchemeBusinessRelatedType", SchemeBusinessRelatedType_name, SchemeBusinessRelatedType_value)
	proto.RegisterEnum("channel_scheme.SchemeBusinessRelatedResource", SchemeBusinessRelatedResource_name, SchemeBusinessRelatedResource_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelSchemeClient is the client API for ChannelScheme service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelSchemeClient interface {
	SetCurChannelScheme(ctx context.Context, in *SetCurChannelSchemeReq, opts ...grpc.CallOption) (*SetCurChannelSchemeResp, error)
	GetCurChannelSchemeInfo(ctx context.Context, in *GetCurChannelSchemeInfoReq, opts ...grpc.CallOption) (*GetCurChannelSchemeInfoResp, error)
	// 当前玩法的人数限制
	GetUgcSchemeMemberSizeLimit(ctx context.Context, in *GetUgcSchemeMemberSizeLimitReq, opts ...grpc.CallOption) (*GetUgcSchemeMemberSizeLimitResp, error)
	BatGetUgcChannelSchemeInfo(ctx context.Context, in *BatGetUgcChannelSchemeInfoReq, opts ...grpc.CallOption) (*BatGetUgcChannelSchemeInfoResp, error)
	// 切换玩法虚拟类型
	SwitchSchemeDetailTypeByVirtualType(ctx context.Context, in *SwitchSchemeDetailTypeByVirtualTypeReq, opts ...grpc.CallOption) (*SwitchSchemeDetailTypeByVirtualTypeResp, error)
	// 获取房间当前某个虚拟类型的玩法类型
	GetSchemeDetailTypeByVirtualType(ctx context.Context, in *GetSchemeDetailTypeByVirtualTypeReq, opts ...grpc.CallOption) (*GetSchemeDetailTypeByVirtualTypeResp, error)
	// 设置业务分类下的玩法的麦位数
	SetSchemeBusinessRelatedMicSize(ctx context.Context, in *SetSchemeBusinessRelatedMicSizeReq, opts ...grpc.CallOption) (*SetSchemeBusinessRelatedMicSizeResp, error)
	// 获取业务分类下的玩法的麦位数
	GetSchemeBusinessRelatedMicSize(ctx context.Context, in *GetSchemeBusinessRelatedMicSizeReq, opts ...grpc.CallOption) (*GetSchemeBusinessRelatedMicSizeResp, error)
	// 更新业务分类包含的玩法
	UpdateBusinessRelatedScheme(ctx context.Context, in *UpdateBusinessRelatedSchemeReq, opts ...grpc.CallOption) (*UpdateBusinessRelatedSchemeResp, error)
	// 获取业务分类包含的玩法
	GetBusinessRelatedScheme(ctx context.Context, in *GetBusinessRelatedSchemeReq, opts ...grpc.CallOption) (*GetBusinessRelatedSchemeResp, error)
}

type channelSchemeClient struct {
	cc *grpc.ClientConn
}

func NewChannelSchemeClient(cc *grpc.ClientConn) ChannelSchemeClient {
	return &channelSchemeClient{cc}
}

func (c *channelSchemeClient) SetCurChannelScheme(ctx context.Context, in *SetCurChannelSchemeReq, opts ...grpc.CallOption) (*SetCurChannelSchemeResp, error) {
	out := new(SetCurChannelSchemeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/SetCurChannelScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) GetCurChannelSchemeInfo(ctx context.Context, in *GetCurChannelSchemeInfoReq, opts ...grpc.CallOption) (*GetCurChannelSchemeInfoResp, error) {
	out := new(GetCurChannelSchemeInfoResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/GetCurChannelSchemeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) GetUgcSchemeMemberSizeLimit(ctx context.Context, in *GetUgcSchemeMemberSizeLimitReq, opts ...grpc.CallOption) (*GetUgcSchemeMemberSizeLimitResp, error) {
	out := new(GetUgcSchemeMemberSizeLimitResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/GetUgcSchemeMemberSizeLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) BatGetUgcChannelSchemeInfo(ctx context.Context, in *BatGetUgcChannelSchemeInfoReq, opts ...grpc.CallOption) (*BatGetUgcChannelSchemeInfoResp, error) {
	out := new(BatGetUgcChannelSchemeInfoResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/BatGetUgcChannelSchemeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) SwitchSchemeDetailTypeByVirtualType(ctx context.Context, in *SwitchSchemeDetailTypeByVirtualTypeReq, opts ...grpc.CallOption) (*SwitchSchemeDetailTypeByVirtualTypeResp, error) {
	out := new(SwitchSchemeDetailTypeByVirtualTypeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/SwitchSchemeDetailTypeByVirtualType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) GetSchemeDetailTypeByVirtualType(ctx context.Context, in *GetSchemeDetailTypeByVirtualTypeReq, opts ...grpc.CallOption) (*GetSchemeDetailTypeByVirtualTypeResp, error) {
	out := new(GetSchemeDetailTypeByVirtualTypeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/GetSchemeDetailTypeByVirtualType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) SetSchemeBusinessRelatedMicSize(ctx context.Context, in *SetSchemeBusinessRelatedMicSizeReq, opts ...grpc.CallOption) (*SetSchemeBusinessRelatedMicSizeResp, error) {
	out := new(SetSchemeBusinessRelatedMicSizeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/SetSchemeBusinessRelatedMicSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) GetSchemeBusinessRelatedMicSize(ctx context.Context, in *GetSchemeBusinessRelatedMicSizeReq, opts ...grpc.CallOption) (*GetSchemeBusinessRelatedMicSizeResp, error) {
	out := new(GetSchemeBusinessRelatedMicSizeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/GetSchemeBusinessRelatedMicSize", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) UpdateBusinessRelatedScheme(ctx context.Context, in *UpdateBusinessRelatedSchemeReq, opts ...grpc.CallOption) (*UpdateBusinessRelatedSchemeResp, error) {
	out := new(UpdateBusinessRelatedSchemeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/UpdateBusinessRelatedScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelSchemeClient) GetBusinessRelatedScheme(ctx context.Context, in *GetBusinessRelatedSchemeReq, opts ...grpc.CallOption) (*GetBusinessRelatedSchemeResp, error) {
	out := new(GetBusinessRelatedSchemeResp)
	err := c.cc.Invoke(ctx, "/channel_scheme.ChannelScheme/GetBusinessRelatedScheme", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelSchemeServer is the server API for ChannelScheme service.
type ChannelSchemeServer interface {
	SetCurChannelScheme(context.Context, *SetCurChannelSchemeReq) (*SetCurChannelSchemeResp, error)
	GetCurChannelSchemeInfo(context.Context, *GetCurChannelSchemeInfoReq) (*GetCurChannelSchemeInfoResp, error)
	// 当前玩法的人数限制
	GetUgcSchemeMemberSizeLimit(context.Context, *GetUgcSchemeMemberSizeLimitReq) (*GetUgcSchemeMemberSizeLimitResp, error)
	BatGetUgcChannelSchemeInfo(context.Context, *BatGetUgcChannelSchemeInfoReq) (*BatGetUgcChannelSchemeInfoResp, error)
	// 切换玩法虚拟类型
	SwitchSchemeDetailTypeByVirtualType(context.Context, *SwitchSchemeDetailTypeByVirtualTypeReq) (*SwitchSchemeDetailTypeByVirtualTypeResp, error)
	// 获取房间当前某个虚拟类型的玩法类型
	GetSchemeDetailTypeByVirtualType(context.Context, *GetSchemeDetailTypeByVirtualTypeReq) (*GetSchemeDetailTypeByVirtualTypeResp, error)
	// 设置业务分类下的玩法的麦位数
	SetSchemeBusinessRelatedMicSize(context.Context, *SetSchemeBusinessRelatedMicSizeReq) (*SetSchemeBusinessRelatedMicSizeResp, error)
	// 获取业务分类下的玩法的麦位数
	GetSchemeBusinessRelatedMicSize(context.Context, *GetSchemeBusinessRelatedMicSizeReq) (*GetSchemeBusinessRelatedMicSizeResp, error)
	// 更新业务分类包含的玩法
	UpdateBusinessRelatedScheme(context.Context, *UpdateBusinessRelatedSchemeReq) (*UpdateBusinessRelatedSchemeResp, error)
	// 获取业务分类包含的玩法
	GetBusinessRelatedScheme(context.Context, *GetBusinessRelatedSchemeReq) (*GetBusinessRelatedSchemeResp, error)
}

func RegisterChannelSchemeServer(s *grpc.Server, srv ChannelSchemeServer) {
	s.RegisterService(&_ChannelScheme_serviceDesc, srv)
}

func _ChannelScheme_SetCurChannelScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetCurChannelSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).SetCurChannelScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/SetCurChannelScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).SetCurChannelScheme(ctx, req.(*SetCurChannelSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_GetCurChannelSchemeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurChannelSchemeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).GetCurChannelSchemeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/GetCurChannelSchemeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).GetCurChannelSchemeInfo(ctx, req.(*GetCurChannelSchemeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_GetUgcSchemeMemberSizeLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUgcSchemeMemberSizeLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).GetUgcSchemeMemberSizeLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/GetUgcSchemeMemberSizeLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).GetUgcSchemeMemberSizeLimit(ctx, req.(*GetUgcSchemeMemberSizeLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_BatGetUgcChannelSchemeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUgcChannelSchemeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).BatGetUgcChannelSchemeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/BatGetUgcChannelSchemeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).BatGetUgcChannelSchemeInfo(ctx, req.(*BatGetUgcChannelSchemeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_SwitchSchemeDetailTypeByVirtualType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchSchemeDetailTypeByVirtualTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).SwitchSchemeDetailTypeByVirtualType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/SwitchSchemeDetailTypeByVirtualType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).SwitchSchemeDetailTypeByVirtualType(ctx, req.(*SwitchSchemeDetailTypeByVirtualTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_GetSchemeDetailTypeByVirtualType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchemeDetailTypeByVirtualTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).GetSchemeDetailTypeByVirtualType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/GetSchemeDetailTypeByVirtualType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).GetSchemeDetailTypeByVirtualType(ctx, req.(*GetSchemeDetailTypeByVirtualTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_SetSchemeBusinessRelatedMicSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSchemeBusinessRelatedMicSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).SetSchemeBusinessRelatedMicSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/SetSchemeBusinessRelatedMicSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).SetSchemeBusinessRelatedMicSize(ctx, req.(*SetSchemeBusinessRelatedMicSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_GetSchemeBusinessRelatedMicSize_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchemeBusinessRelatedMicSizeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).GetSchemeBusinessRelatedMicSize(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/GetSchemeBusinessRelatedMicSize",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).GetSchemeBusinessRelatedMicSize(ctx, req.(*GetSchemeBusinessRelatedMicSizeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_UpdateBusinessRelatedScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBusinessRelatedSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).UpdateBusinessRelatedScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/UpdateBusinessRelatedScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).UpdateBusinessRelatedScheme(ctx, req.(*UpdateBusinessRelatedSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelScheme_GetBusinessRelatedScheme_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBusinessRelatedSchemeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelSchemeServer).GetBusinessRelatedScheme(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_scheme.ChannelScheme/GetBusinessRelatedScheme",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelSchemeServer).GetBusinessRelatedScheme(ctx, req.(*GetBusinessRelatedSchemeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelScheme_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_scheme.ChannelScheme",
	HandlerType: (*ChannelSchemeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetCurChannelScheme",
			Handler:    _ChannelScheme_SetCurChannelScheme_Handler,
		},
		{
			MethodName: "GetCurChannelSchemeInfo",
			Handler:    _ChannelScheme_GetCurChannelSchemeInfo_Handler,
		},
		{
			MethodName: "GetUgcSchemeMemberSizeLimit",
			Handler:    _ChannelScheme_GetUgcSchemeMemberSizeLimit_Handler,
		},
		{
			MethodName: "BatGetUgcChannelSchemeInfo",
			Handler:    _ChannelScheme_BatGetUgcChannelSchemeInfo_Handler,
		},
		{
			MethodName: "SwitchSchemeDetailTypeByVirtualType",
			Handler:    _ChannelScheme_SwitchSchemeDetailTypeByVirtualType_Handler,
		},
		{
			MethodName: "GetSchemeDetailTypeByVirtualType",
			Handler:    _ChannelScheme_GetSchemeDetailTypeByVirtualType_Handler,
		},
		{
			MethodName: "SetSchemeBusinessRelatedMicSize",
			Handler:    _ChannelScheme_SetSchemeBusinessRelatedMicSize_Handler,
		},
		{
			MethodName: "GetSchemeBusinessRelatedMicSize",
			Handler:    _ChannelScheme_GetSchemeBusinessRelatedMicSize_Handler,
		},
		{
			MethodName: "UpdateBusinessRelatedScheme",
			Handler:    _ChannelScheme_UpdateBusinessRelatedScheme_Handler,
		},
		{
			MethodName: "GetBusinessRelatedScheme",
			Handler:    _ChannelScheme_GetBusinessRelatedScheme_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/channel-scheme/channel-scheme.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/channel-scheme/channel-scheme.proto", fileDescriptor_channel_scheme_8b6f18121b101c09)
}

var fileDescriptor_channel_scheme_8b6f18121b101c09 = []byte{
	// 1381 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x58, 0xdd, 0x6e, 0x1b, 0x55,
	0x10, 0xce, 0xae, 0xed, 0xfc, 0x8c, 0x1d, 0xd7, 0x3d, 0x11, 0xad, 0xe3, 0x90, 0xc6, 0xd9, 0xb6,
	0x69, 0x9b, 0xb6, 0x0e, 0x72, 0x28, 0x45, 0x95, 0x90, 0x9a, 0xb8, 0xc6, 0x44, 0xd4, 0x69, 0x58,
	0x3b, 0x54, 0xe5, 0x66, 0xb5, 0x59, 0x9f, 0x24, 0x4b, 0xf6, 0xaf, 0x7b, 0x8e, 0x9d, 0xb8, 0x12,
	0x12, 0xe2, 0x1a, 0x09, 0x71, 0xc7, 0x43, 0xc0, 0x3d, 0x4f, 0x80, 0xb8, 0xe0, 0x01, 0x90, 0x78,
	0x0d, 0xb8, 0x47, 0x7b, 0xce, 0xfa, 0x6f, 0xbd, 0xeb, 0x9f, 0xd0, 0xde, 0x70, 0x17, 0xcf, 0x7c,
	0x33, 0x67, 0xe6, 0x3b, 0x33, 0x73, 0x66, 0x03, 0x45, 0x4a, 0xb7, 0x5e, 0x37, 0x75, 0xed, 0x8c,
	0xe8, 0x46, 0x0b, 0xbb, 0x5b, 0xda, 0xa9, 0x6a, 0x59, 0xd8, 0x78, 0x48, 0xb4, 0x53, 0x6c, 0xe2,
	0xc0, 0xcf, 0x82, 0xe3, 0xda, 0xd4, 0x46, 0x69, 0x5f, 0xaa, 0x70, 0xa9, 0xf4, 0x5d, 0x1c, 0xae,
	0x96, 0xb8, 0xa8, 0xc6, 0x24, 0x7b, 0xd6, 0xb1, 0x8d, 0x56, 0x60, 0x81, 0xeb, 0x15, 0xbd, 0x91,
	0x15, 0xf2, 0xc2, 0xdd, 0x45, 0x79, 0x9e, 0x0b, 0xf6, 0x1a, 0x68, 0x0d, 0x92, 0xbe, 0xd2, 0x52,
	0x4d, 0x9c, 0x15, 0xf3, 0xc2, 0xdd, 0x05, 0x19, 0xb8, 0x68, 0x5f, 0x35, 0x71, 0x1f, 0x80, 0xb6,
	0x1d, 0x9c, 0x8d, 0x31, 0x7b, 0x1f, 0x50, 0x6f, 0x3b, 0x0c, 0x60, 0xa8, 0x6d, 0xbb, 0x49, 0x39,
	0x20, 0xce, 0x01, 0x5c, 0xc4, 0x00, 0x79, 0x48, 0x69, 0x4d, 0x57, 0x31, 0x75, 0x4d, 0x21, 0xfa,
	0x1b, 0x9c, 0x4d, 0x70, 0x84, 0xd6, 0x74, 0xab, 0xba, 0x56, 0xd3, 0xdf, 0x60, 0xb4, 0x0c, 0xf3,
	0x9e, 0xd6, 0xb4, 0x1b, 0x38, 0x3b, 0xcb, 0xb4, 0x73, 0xa6, 0xae, 0x55, 0xed, 0x06, 0x46, 0xdb,
	0x70, 0xcd, 0x3f, 0x9e, 0xb4, 0x5c, 0xa5, 0x81, 0xa9, 0xaa, 0x1b, 0xfc, 0xa0, 0x39, 0x06, 0x5c,
	0xe2, 0xda, 0x5a, 0xcb, 0x7d, 0xc6, 0x74, 0xec, 0xc4, 0x5b, 0x90, 0xf6, 0xfc, 0xa9, 0xcd, 0x86,
	0x6e, 0x73, 0xf0, 0x3c, 0x03, 0xa7, 0x4c, 0x5d, 0xdb, 0xf1, 0x84, 0x0c, 0xb5, 0x04, 0x09, 0x93,
	0x28, 0x94, 0x64, 0x17, 0xf2, 0xc2, 0xdd, 0xb8, 0x1c, 0x37, 0x49, 0x9d, 0xa0, 0xfb, 0x70, 0xf5,
	0xa8, 0x49, 0x74, 0x0b, 0x13, 0xa2, 0x68, 0x2a, 0xc5, 0x27, 0xb6, 0xdb, 0xce, 0x02, 0xb3, 0xce,
	0x74, 0x14, 0x25, 0x5f, 0x8e, 0x1e, 0x00, 0xf2, 0x83, 0xeb, 0x0f, 0x2c, 0xc9, 0xd1, 0x5c, 0xd3,
	0x17, 0xd5, 0x27, 0xb0, 0x72, 0xac, 0x1a, 0xc6, 0x91, 0xaa, 0x9d, 0x29, 0x21, 0x66, 0x29, 0x66,
	0x96, 0xed, 0x40, 0x6a, 0x41, 0xf3, 0x0d, 0xb8, 0x82, 0x2f, 0x1c, 0xac, 0xd1, 0x1e, 0x93, 0x8b,
	0xcc, 0x64, 0x91, 0x8b, 0x7d, 0x32, 0xa5, 0x2f, 0x20, 0x57, 0xc1, 0xb4, 0xd4, 0x74, 0x87, 0x2a,
	0x41, 0xc6, 0xaf, 0x51, 0x06, 0x62, 0x5a, 0xb7, 0x0c, 0xbc, 0x3f, 0xd1, 0x3a, 0xa4, 0x3a, 0x65,
	0xc4, 0xe2, 0x10, 0x99, 0x2a, 0xe9, 0xcb, 0xbc, 0xa3, 0x25, 0x02, 0x2b, 0x91, 0x2e, 0x89, 0x13,
	0xe2, 0x73, 0xb7, 0x5b, 0x34, 0xba, 0x75, 0x6c, 0x33, 0x97, 0xc9, 0xe2, 0x7a, 0x61, 0xb0, 0x5c,
	0x0b, 0xc3, 0xde, 0xfc, 0xba, 0xf2, 0xfe, 0x96, 0x9e, 0xc0, 0xea, 0xae, 0x4a, 0x2b, 0x98, 0x1e,
	0x9e, 0x68, 0xa1, 0xa9, 0x2c, 0xc3, 0xbc, 0xa6, 0x37, 0x14, 0x43, 0x27, 0x34, 0x2b, 0xe4, 0x63,
	0x5e, 0xd5, 0x68, 0x7a, 0xe3, 0xb9, 0x4e, 0xa8, 0xf4, 0xb7, 0x00, 0x37, 0x46, 0x19, 0x13, 0x07,
	0x19, 0x90, 0xe9, 0x0b, 0xb1, 0xe7, 0x25, 0x59, 0xdc, 0x0d, 0xc6, 0x39, 0xda, 0x53, 0xa1, 0xf7,
	0xd3, 0x3b, 0xbb, 0x6c, 0x51, 0xb7, 0x2d, 0xa7, 0xc9, 0x80, 0x30, 0xd7, 0x80, 0xa5, 0x10, 0x98,
	0xc7, 0xdc, 0x19, 0x6e, 0x77, 0x98, 0x3b, 0xc3, 0x6d, 0xf4, 0x18, 0x12, 0x2d, 0xd5, 0x68, 0xe2,
	0xc9, 0x39, 0xe3, 0xf8, 0x27, 0xe2, 0xc7, 0x82, 0xf4, 0x97, 0x00, 0xd7, 0x6a, 0xc3, 0x17, 0xe5,
	0x91, 0xf5, 0x1e, 0xcc, 0xda, 0x8e, 0xd2, 0xec, 0x5e, 0x53, 0xc2, 0x76, 0x0e, 0xf5, 0x46, 0xe7,
	0xea, 0xc4, 0xde, 0xd5, 0x0d, 0x4c, 0x8b, 0x58, 0x60, 0x5a, 0x14, 0x60, 0x96, 0xd8, 0x4d, 0x57,
	0xe3, 0x6d, 0x9e, 0x2e, 0x5e, 0x0b, 0x86, 0x57, 0x63, 0x5a, 0xd9, 0x47, 0x45, 0x34, 0x48, 0x22,
	0xa2, 0x41, 0x56, 0x01, 0x2c, 0x7c, 0xae, 0xf8, 0x27, 0xf0, 0x41, 0xb0, 0x60, 0xe1, 0x73, 0xee,
	0x54, 0xfa, 0x5d, 0x80, 0xeb, 0xa1, 0xd9, 0x11, 0xe7, 0x5d, 0xcf, 0xb8, 0x3c, 0xa4, 0xc8, 0xb9,
	0x4e, 0xb5, 0x53, 0x85, 0x4f, 0x8c, 0x38, 0x9b, 0x18, 0xc0, 0x65, 0x55, 0x6f, 0x6e, 0x4c, 0x95,
	0xa9, 0xf4, 0x02, 0x6e, 0xf0, 0x8a, 0xe2, 0x29, 0x54, 0xb1, 0x79, 0x84, 0x5d, 0xaf, 0x7b, 0x9f,
	0xeb, 0xa6, 0x4e, 0xc3, 0xfb, 0x74, 0x20, 0x45, 0x71, 0x30, 0x45, 0x69, 0x0f, 0xd6, 0x46, 0x3a,
	0x24, 0x8e, 0x37, 0x3f, 0x4c, 0xf5, 0x42, 0x31, 0x99, 0x8a, 0xcf, 0x0f, 0xee, 0x7d, 0xd1, 0x54,
	0x2f, 0x7a, 0x06, 0xd2, 0x2f, 0x22, 0x6c, 0xd4, 0x58, 0x62, 0xc1, 0x11, 0xb4, 0xdb, 0xfe, 0x52,
	0x77, 0x69, 0x53, 0x65, 0x3f, 0xde, 0x5e, 0x51, 0x85, 0x53, 0x17, 0x8f, 0x28, 0x92, 0x02, 0xf8,
	0x23, 0x5f, 0x69, 0xf1, 0x60, 0xfa, 0x99, 0xbe, 0xca, 0x55, 0x7d, 0x61, 0xa2, 0xa7, 0xb0, 0xda,
	0x0f, 0x54, 0x8e, 0x6d, 0xc3, 0xb0, 0xcf, 0x95, 0x5e, 0x38, 0x5e, 0x9d, 0xcd, 0xcb, 0xcb, 0xad,
	0x9e, 0xcd, 0xa7, 0x0c, 0x52, 0xeb, 0xc4, 0x37, 0x58, 0x96, 0x73, 0xc1, 0xb2, 0xfc, 0x53, 0x80,
	0x3b, 0x13, 0xf1, 0xf5, 0xee, 0xcb, 0x74, 0x3a, 0x26, 0x83, 0x45, 0x9d, 0x08, 0x16, 0xb5, 0xf4,
	0x9b, 0x00, 0x37, 0x2b, 0x98, 0x8e, 0xad, 0x83, 0xe1, 0x62, 0x8d, 0xb8, 0x25, 0xf1, 0xd2, 0xb7,
	0x14, 0x1b, 0x77, 0x4b, 0x03, 0xd4, 0xc6, 0x03, 0xed, 0xd1, 0x82, 0x5b, 0xe3, 0xf3, 0x20, 0x4e,
	0x04, 0x81, 0xc2, 0x84, 0x04, 0x8a, 0x43, 0x04, 0xfe, 0x2c, 0x80, 0x54, 0xeb, 0x1c, 0xbc, 0xeb,
	0xaf, 0x0f, 0x32, 0x36, 0x54, 0x8a, 0x1b, 0xfe, 0x7b, 0x1d, 0xce, 0x9f, 0xbf, 0x11, 0xb1, 0x2e,
	0x15, 0xbb, 0x1b, 0x11, 0x5b, 0x96, 0x5e, 0x75, 0xa9, 0x75, 0xb9, 0xa3, 0x5e, 0x35, 0xa4, 0x8b,
	0xf7, 0x86, 0x06, 0x72, 0xd8, 0xd1, 0x2c, 0x5f, 0xff, 0x16, 0xfa, 0x44, 0xd2, 0x23, 0xb8, 0x39,
	0x36, 0x5a, 0xe2, 0xa0, 0x34, 0x88, 0x94, 0xb0, 0x68, 0xe3, 0xb2, 0x48, 0x89, 0xf4, 0xa3, 0x00,
	0x52, 0xe5, 0x32, 0x59, 0x46, 0xa4, 0x22, 0xbe, 0x85, 0x54, 0x9e, 0xf6, 0x55, 0xee, 0x88, 0x54,
	0xfa, 0x79, 0x16, 0x06, 0x78, 0x96, 0xfe, 0x11, 0xe0, 0xc6, 0xa1, 0xd3, 0x50, 0x69, 0xd0, 0xbe,
	0xf7, 0xa8, 0x46, 0xc4, 0x2f, 0xfc, 0xf7, 0xf8, 0x91, 0x0c, 0x8b, 0x2e, 0xe6, 0x23, 0xa7, 0x9f,
	0x94, 0x87, 0x13, 0x39, 0x95, 0x7d, 0x4b, 0x39, 0xd5, 0xf1, 0xd1, 0x59, 0x8b, 0xbb, 0x2d, 0xc2,
	0x17, 0x9e, 0x18, 0x5b, 0x9b, 0x52, 0x9d, 0x3e, 0x61, 0xbb, 0xd3, 0x3a, 0xac, 0x8d, 0x4c, 0x9b,
	0x38, 0xd2, 0x1f, 0x02, 0x5b, 0x08, 0xff, 0x27, 0xbc, 0x48, 0xcf, 0xe0, 0xfd, 0xe8, 0x6c, 0x88,
	0x13, 0xc2, 0x9b, 0x30, 0xcc, 0xdb, 0xa6, 0x0d, 0xb3, 0xfc, 0x45, 0x40, 0x49, 0x98, 0x3b, 0xdc,
	0xff, 0x7c, 0xff, 0xc5, 0xcb, 0xfd, 0xcc, 0x0c, 0x02, 0x98, 0x2d, 0xc9, 0xe5, 0x9d, 0x7a, 0x39,
	0x23, 0x78, 0x7f, 0xd7, 0x5e, 0xee, 0xd5, 0x4b, 0x9f, 0x65, 0x44, 0x74, 0x05, 0x92, 0xf5, 0x72,
	0xf5, 0x40, 0xf1, 0x95, 0x31, 0x94, 0x06, 0x38, 0xa8, 0x94, 0x14, 0x1f, 0x10, 0x47, 0x6b, 0xb0,
	0x72, 0x58, 0x29, 0x29, 0x95, 0x9d, 0x6a, 0x59, 0x79, 0x56, 0xae, 0xef, 0xec, 0x3d, 0x57, 0xea,
	0xaf, 0x0e, 0xca, 0x1d, 0x40, 0x62, 0xf3, 0x6b, 0x58, 0x8e, 0xa4, 0x0e, 0xdd, 0x86, 0xf5, 0x48,
	0xa5, 0x72, 0x68, 0x9d, 0x59, 0xf6, 0xb9, 0x95, 0x99, 0x41, 0x1b, 0x20, 0x45, 0xc3, 0xaa, 0x75,
	0xe5, 0x40, 0x75, 0x69, 0x3b, 0x23, 0x6c, 0x36, 0x61, 0x75, 0x24, 0xa3, 0xe8, 0x1e, 0xdc, 0x1e,
	0x09, 0xe8, 0x3b, 0x73, 0x2c, 0xd4, 0x6f, 0xd0, 0x8c, 0x50, 0xfc, 0x15, 0x60, 0x71, 0x60, 0xd9,
	0x43, 0xa7, 0xb0, 0x14, 0xb2, 0x03, 0xa2, 0x8d, 0xa1, 0xfb, 0x0f, 0x5d, 0x83, 0x73, 0x77, 0x26,
	0xc2, 0x11, 0x47, 0x9a, 0x41, 0x14, 0xae, 0x47, 0x7c, 0xf4, 0xa0, 0xcd, 0xa0, 0x97, 0xe8, 0x0f,
	0xae, 0xdc, 0xfd, 0x89, 0xb1, 0xec, 0xd4, 0x6f, 0x79, 0x6b, 0x45, 0x6d, 0x72, 0xa8, 0x10, 0xe2,
	0x6e, 0xc4, 0x1e, 0x99, 0xdb, 0x9a, 0x0a, 0xcf, 0x42, 0xf8, 0x06, 0x72, 0xd1, 0x5f, 0x3c, 0xe8,
	0xe1, 0x34, 0x5f, 0x47, 0xaf, 0x73, 0x85, 0xe9, 0x3e, 0xa6, 0xa4, 0x19, 0xf4, 0x93, 0x00, 0x37,
	0x27, 0xd8, 0xa7, 0xd0, 0x47, 0x43, 0x57, 0x39, 0xd1, 0xd2, 0x9a, 0x7b, 0x7c, 0x29, 0x3b, 0x16,
	0xda, 0x0f, 0x02, 0xe4, 0xc7, 0xed, 0x11, 0x68, 0x3b, 0x84, 0xf1, 0xb1, 0x41, 0x7d, 0x38, 0xbd,
	0x11, 0x8b, 0xe8, 0x7b, 0x01, 0xd6, 0xc6, 0x3c, 0xd9, 0xa8, 0x18, 0x52, 0xf3, 0x63, 0xde, 0xea,
	0xdc, 0xf6, 0xd4, 0x36, 0xdd, 0x70, 0x2a, 0xd3, 0x86, 0x53, 0xb9, 0x44, 0x38, 0x95, 0x89, 0xc2,
	0xf1, 0x9a, 0x69, 0xc4, 0x5b, 0x36, 0xdc, 0x4c, 0xa3, 0xdf, 0xfb, 0xe1, 0x66, 0x1a, 0xf7, 0x50,
	0xce, 0xa0, 0x73, 0xc8, 0x46, 0xbd, 0x2d, 0x28, 0x6c, 0x34, 0x44, 0x9e, 0xfd, 0x60, 0x72, 0xb0,
	0x77, 0xf0, 0x6e, 0xf1, 0xab, 0x0f, 0x4e, 0x6c, 0x43, 0xb5, 0x4e, 0x0a, 0x8f, 0x8a, 0x94, 0x16,
	0x34, 0xdb, 0xdc, 0x62, 0xff, 0x34, 0xd4, 0x6c, 0x63, 0x8b, 0x60, 0xb7, 0xa5, 0x6b, 0x98, 0x04,
	0xfe, 0xab, 0x78, 0x34, 0xcb, 0x10, 0xdb, 0xff, 0x06, 0x00, 0x00, 0xff, 0xff, 0x6d, 0xb1, 0x14,
	0x50, 0x8c, 0x14, 0x00, 0x00,
}
