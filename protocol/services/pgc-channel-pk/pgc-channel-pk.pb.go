// Code generated by protoc-gen-go. DO NOT EDIT.
// source: pgc-channel-pk/pgc-channel-pk.proto

package pgc_channel_pk // import "golang.52tt.com/protocol/services/pgc-channel-pk"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EUserPrivilegeType int32

const (
	EUserPrivilegeType_ENUM_USER_PRIVILEGE_UNKNOWN EUserPrivilegeType = 0
	EUserPrivilegeType_ENUM_USER_PRIVILEGE_UKW     EUserPrivilegeType = 1
)

var EUserPrivilegeType_name = map[int32]string{
	0: "ENUM_USER_PRIVILEGE_UNKNOWN",
	1: "ENUM_USER_PRIVILEGE_UKW",
}
var EUserPrivilegeType_value = map[string]int32{
	"ENUM_USER_PRIVILEGE_UNKNOWN": 0,
	"ENUM_USER_PRIVILEGE_UKW":     1,
}

func (x EUserPrivilegeType) String() string {
	return proto.EnumName(EUserPrivilegeType_name, int32(x))
}
func (EUserPrivilegeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{0}
}

type PgcChannelPKSwitchEnum int32

const (
	PgcChannelPKSwitchEnum_ON  PgcChannelPKSwitchEnum = 0
	PgcChannelPKSwitchEnum_OFF PgcChannelPKSwitchEnum = 1
)

var PgcChannelPKSwitchEnum_name = map[int32]string{
	0: "ON",
	1: "OFF",
}
var PgcChannelPKSwitchEnum_value = map[string]int32{
	"ON":  0,
	"OFF": 1,
}

func (x PgcChannelPKSwitchEnum) String() string {
	return proto.EnumName(PgcChannelPKSwitchEnum_name, int32(x))
}
func (PgcChannelPKSwitchEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{1}
}

// PK中，对面房间语音状态
type PgcChannelPKOpponentMicFlag int32

const (
	PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN  PgcChannelPKOpponentMicFlag = 0
	PgcChannelPKOpponentMicFlag_PgcPKMic_CLOSE PgcChannelPKOpponentMicFlag = 1
)

var PgcChannelPKOpponentMicFlag_name = map[int32]string{
	0: "PgcPKMic_OPEN",
	1: "PgcPKMic_CLOSE",
}
var PgcChannelPKOpponentMicFlag_value = map[string]int32{
	"PgcPKMic_OPEN":  0,
	"PgcPKMic_CLOSE": 1,
}

func (x PgcChannelPKOpponentMicFlag) String() string {
	return proto.EnumName(PgcChannelPKOpponentMicFlag_name, int32(x))
}
func (PgcChannelPKOpponentMicFlag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{2}
}

type DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE int32

const (
	DownloadSourceInfo_DOWNLOAD_SOURCE_NONE                      DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE = 0
	DownloadSourceInfo_DOWNLOAD_SOURCE_DATING_GAME_HAT           DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE = 1
	DownloadSourceInfo_DOWNLOAD_SOURCE_EFFECTS                   DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE = 2
	DownloadSourceInfo_DOWNLOAD_SOURCE_PRESENT_RUNWAY_ROCKET     DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE = 3
	DownloadSourceInfo_DOWNLOAD_SOURCE_PGC_CHANNEL_PK_RESULT_MVP DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE = 4
)

var DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE_name = map[int32]string{
	0: "DOWNLOAD_SOURCE_NONE",
	1: "DOWNLOAD_SOURCE_DATING_GAME_HAT",
	2: "DOWNLOAD_SOURCE_EFFECTS",
	3: "DOWNLOAD_SOURCE_PRESENT_RUNWAY_ROCKET",
	4: "DOWNLOAD_SOURCE_PGC_CHANNEL_PK_RESULT_MVP",
}
var DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE_value = map[string]int32{
	"DOWNLOAD_SOURCE_NONE":                      0,
	"DOWNLOAD_SOURCE_DATING_GAME_HAT":           1,
	"DOWNLOAD_SOURCE_EFFECTS":                   2,
	"DOWNLOAD_SOURCE_PRESENT_RUNWAY_ROCKET":     3,
	"DOWNLOAD_SOURCE_PGC_CHANNEL_PK_RESULT_MVP": 4,
}

func (x DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE) String() string {
	return proto.EnumName(DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE_name, int32(x))
}
func (DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{0, 0}
}

type PgcChannelPKChannelInfo_Status int32

const (
	PgcChannelPKChannelInfo_NotPK PgcChannelPKChannelInfo_Status = 0
	PgcChannelPKChannelInfo_InPK  PgcChannelPKChannelInfo_Status = 1
)

var PgcChannelPKChannelInfo_Status_name = map[int32]string{
	0: "NotPK",
	1: "InPK",
}
var PgcChannelPKChannelInfo_Status_value = map[string]int32{
	"NotPK": 0,
	"InPK":  1,
}

func (x PgcChannelPKChannelInfo_Status) String() string {
	return proto.EnumName(PgcChannelPKChannelInfo_Status_name, int32(x))
}
func (PgcChannelPKChannelInfo_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{12, 0}
}

type PgcChannelPKBattle_Status int32

const (
	PgcChannelPKBattle_NotPK    PgcChannelPKBattle_Status = 0
	PgcChannelPKBattle_InvitePK PgcChannelPKBattle_Status = 1
	PgcChannelPKBattle_InPKing  PgcChannelPKBattle_Status = 2
	// QuickKill = 3;  // 斩杀
	// PeakPk = 4;     // 巅峰对决
	PgcChannelPKBattle_ChoseInteraction PgcChannelPKBattle_Status = 5
	PgcChannelPKBattle_Interaction      PgcChannelPKBattle_Status = 6
)

var PgcChannelPKBattle_Status_name = map[int32]string{
	0: "NotPK",
	1: "InvitePK",
	2: "InPKing",
	5: "ChoseInteraction",
	6: "Interaction",
}
var PgcChannelPKBattle_Status_value = map[string]int32{
	"NotPK":            0,
	"InvitePK":         1,
	"InPKing":          2,
	"ChoseInteraction": 5,
	"Interaction":      6,
}

func (x PgcChannelPKBattle_Status) String() string {
	return proto.EnumName(PgcChannelPKBattle_Status_name, int32(x))
}
func (PgcChannelPKBattle_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{21, 0}
}

// 客户端去下载的资源文件
type DownloadSourceInfo struct {
	SourceType           uint32   `protobuf:"varint,1,opt,name=source_type,json=sourceType,proto3" json:"source_type,omitempty"`
	SourceId             uint32   `protobuf:"varint,2,opt,name=source_id,json=sourceId,proto3" json:"source_id,omitempty"`
	Url                  string   `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DownloadSourceInfo) Reset()         { *m = DownloadSourceInfo{} }
func (m *DownloadSourceInfo) String() string { return proto.CompactTextString(m) }
func (*DownloadSourceInfo) ProtoMessage()    {}
func (*DownloadSourceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{0}
}
func (m *DownloadSourceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DownloadSourceInfo.Unmarshal(m, b)
}
func (m *DownloadSourceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DownloadSourceInfo.Marshal(b, m, deterministic)
}
func (dst *DownloadSourceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DownloadSourceInfo.Merge(dst, src)
}
func (m *DownloadSourceInfo) XXX_Size() int {
	return xxx_messageInfo_DownloadSourceInfo.Size(m)
}
func (m *DownloadSourceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DownloadSourceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DownloadSourceInfo proto.InternalMessageInfo

func (m *DownloadSourceInfo) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *DownloadSourceInfo) GetSourceId() uint32 {
	if m != nil {
		return m.SourceId
	}
	return 0
}

func (m *DownloadSourceInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DownloadSourceInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type UserProfile struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Account              string         `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string         `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	AccountAlias         string         `protobuf:"bytes,4,opt,name=account_alias,json=accountAlias,proto3" json:"account_alias,omitempty"`
	Sex                  uint32         `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Privilege            *UserPrivilege `protobuf:"bytes,6,opt,name=privilege,proto3" json:"privilege,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserProfile) Reset()         { *m = UserProfile{} }
func (m *UserProfile) String() string { return proto.CompactTextString(m) }
func (*UserProfile) ProtoMessage()    {}
func (*UserProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{1}
}
func (m *UserProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserProfile.Unmarshal(m, b)
}
func (m *UserProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserProfile.Marshal(b, m, deterministic)
}
func (dst *UserProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserProfile.Merge(dst, src)
}
func (m *UserProfile) XXX_Size() int {
	return xxx_messageInfo_UserProfile.Size(m)
}
func (m *UserProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserProfile proto.InternalMessageInfo

func (m *UserProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserProfile) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserProfile) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserProfile) GetAccountAlias() string {
	if m != nil {
		return m.AccountAlias
	}
	return ""
}

func (m *UserProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserProfile) GetPrivilege() *UserPrivilege {
	if m != nil {
		return m.Privilege
	}
	return nil
}

type UserPrivilege struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Type                 uint32   `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Options              []byte   `protobuf:"bytes,4,opt,name=options,proto3" json:"options,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPrivilege) Reset()         { *m = UserPrivilege{} }
func (m *UserPrivilege) String() string { return proto.CompactTextString(m) }
func (*UserPrivilege) ProtoMessage()    {}
func (*UserPrivilege) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{2}
}
func (m *UserPrivilege) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPrivilege.Unmarshal(m, b)
}
func (m *UserPrivilege) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPrivilege.Marshal(b, m, deterministic)
}
func (dst *UserPrivilege) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPrivilege.Merge(dst, src)
}
func (m *UserPrivilege) XXX_Size() int {
	return xxx_messageInfo_UserPrivilege.Size(m)
}
func (m *UserPrivilege) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPrivilege.DiscardUnknown(m)
}

var xxx_messageInfo_UserPrivilege proto.InternalMessageInfo

func (m *UserPrivilege) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserPrivilege) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserPrivilege) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserPrivilege) GetOptions() []byte {
	if m != nil {
		return m.Options
	}
	return nil
}

// 神秘人结构
type UserUKWInfo struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	Medal                string   `protobuf:"bytes,2,opt,name=medal,proto3" json:"medal,omitempty"`
	HeadFrame            string   `protobuf:"bytes,3,opt,name=head_frame,json=headFrame,proto3" json:"head_frame,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserUKWInfo) Reset()         { *m = UserUKWInfo{} }
func (m *UserUKWInfo) String() string { return proto.CompactTextString(m) }
func (*UserUKWInfo) ProtoMessage()    {}
func (*UserUKWInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{3}
}
func (m *UserUKWInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserUKWInfo.Unmarshal(m, b)
}
func (m *UserUKWInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserUKWInfo.Marshal(b, m, deterministic)
}
func (dst *UserUKWInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserUKWInfo.Merge(dst, src)
}
func (m *UserUKWInfo) XXX_Size() int {
	return xxx_messageInfo_UserUKWInfo.Size(m)
}
func (m *UserUKWInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserUKWInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserUKWInfo proto.InternalMessageInfo

func (m *UserUKWInfo) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *UserUKWInfo) GetMedal() string {
	if m != nil {
		return m.Medal
	}
	return ""
}

func (m *UserUKWInfo) GetHeadFrame() string {
	if m != nil {
		return m.HeadFrame
	}
	return ""
}

// PgcChannelPK用户简略信息
type PgcChannelPKAnchor struct {
	UserProfile          *UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	IsMvp                bool         `protobuf:"varint,2,opt,name=is_mvp,json=isMvp,proto3" json:"is_mvp,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PgcChannelPKAnchor) Reset()         { *m = PgcChannelPKAnchor{} }
func (m *PgcChannelPKAnchor) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKAnchor) ProtoMessage()    {}
func (*PgcChannelPKAnchor) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{4}
}
func (m *PgcChannelPKAnchor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKAnchor.Unmarshal(m, b)
}
func (m *PgcChannelPKAnchor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKAnchor.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKAnchor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKAnchor.Merge(dst, src)
}
func (m *PgcChannelPKAnchor) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKAnchor.Size(m)
}
func (m *PgcChannelPKAnchor) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKAnchor.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKAnchor proto.InternalMessageInfo

func (m *PgcChannelPKAnchor) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PgcChannelPKAnchor) GetIsMvp() bool {
	if m != nil {
		return m.IsMvp
	}
	return false
}

// 麦上收礼1-3名跟送礼者用户连线
type PgcChannelPKMicRankInfo struct {
	MicUser              *PgcChannelPKAnchor `protobuf:"bytes,1,opt,name=mic_user,json=micUser,proto3" json:"mic_user,omitempty"`
	FromUser             *PgcChannelPKAnchor `protobuf:"bytes,2,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Score                uint32              `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	Rank                 uint32              `protobuf:"varint,4,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PgcChannelPKMicRankInfo) Reset()         { *m = PgcChannelPKMicRankInfo{} }
func (m *PgcChannelPKMicRankInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKMicRankInfo) ProtoMessage()    {}
func (*PgcChannelPKMicRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{5}
}
func (m *PgcChannelPKMicRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKMicRankInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKMicRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKMicRankInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKMicRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKMicRankInfo.Merge(dst, src)
}
func (m *PgcChannelPKMicRankInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKMicRankInfo.Size(m)
}
func (m *PgcChannelPKMicRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKMicRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKMicRankInfo proto.InternalMessageInfo

func (m *PgcChannelPKMicRankInfo) GetMicUser() *PgcChannelPKAnchor {
	if m != nil {
		return m.MicUser
	}
	return nil
}

func (m *PgcChannelPKMicRankInfo) GetFromUser() *PgcChannelPKAnchor {
	if m != nil {
		return m.FromUser
	}
	return nil
}

func (m *PgcChannelPKMicRankInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PgcChannelPKMicRankInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

// 进行PK中的信息
type PgcChannelPKInfo struct {
	ChannelInfo          *PgcChannelPKChannelInfo   `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Score                uint32                     `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	TopAnchorList        []*PgcChannelPKAnchor      `protobuf:"bytes,3,rep,name=top_anchor_list,json=topAnchorList,proto3" json:"top_anchor_list,omitempty"`
	MvpInfo              []*PgcChannelPKMicRankInfo `protobuf:"bytes,4,rep,name=mvp_info,json=mvpInfo,proto3" json:"mvp_info,omitempty"`
	ChannelClientId      string                     `protobuf:"bytes,5,opt,name=channel_client_id,json=channelClientId,proto3" json:"channel_client_id,omitempty"`
	MinScoreText         string                     `protobuf:"bytes,6,opt,name=min_score_text,json=minScoreText,proto3" json:"min_score_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *PgcChannelPKInfo) Reset()         { *m = PgcChannelPKInfo{} }
func (m *PgcChannelPKInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKInfo) ProtoMessage()    {}
func (*PgcChannelPKInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{6}
}
func (m *PgcChannelPKInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKInfo.Merge(dst, src)
}
func (m *PgcChannelPKInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKInfo.Size(m)
}
func (m *PgcChannelPKInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKInfo proto.InternalMessageInfo

func (m *PgcChannelPKInfo) GetChannelInfo() *PgcChannelPKChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *PgcChannelPKInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PgcChannelPKInfo) GetTopAnchorList() []*PgcChannelPKAnchor {
	if m != nil {
		return m.TopAnchorList
	}
	return nil
}

func (m *PgcChannelPKInfo) GetMvpInfo() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.MvpInfo
	}
	return nil
}

func (m *PgcChannelPKInfo) GetChannelClientId() string {
	if m != nil {
		return m.ChannelClientId
	}
	return ""
}

func (m *PgcChannelPKInfo) GetMinScoreText() string {
	if m != nil {
		return m.MinScoreText
	}
	return ""
}

// 斩杀信息
type PgcChannelPKQuickKillInfo struct {
	QuickKillDescPrefix  string   `protobuf:"bytes,1,opt,name=quick_kill_desc_prefix,json=quickKillDescPrefix,proto3" json:"quick_kill_desc_prefix,omitempty"`
	QuickKillDesc        string   `protobuf:"bytes,2,opt,name=quick_kill_desc,json=quickKillDesc,proto3" json:"quick_kill_desc,omitempty"`
	QuickKillEndTs       uint32   `protobuf:"varint,3,opt,name=quick_kill_end_ts,json=quickKillEndTs,proto3" json:"quick_kill_end_ts,omitempty"`
	EnableMinPkSec       uint32   `protobuf:"varint,4,opt,name=enable_min_pk_sec,json=enableMinPkSec,proto3" json:"enable_min_pk_sec,omitempty"`
	EnableMaxPkSec       uint32   `protobuf:"varint,5,opt,name=enable_max_pk_sec,json=enableMaxPkSec,proto3" json:"enable_max_pk_sec,omitempty"`
	ConditionValue       uint32   `protobuf:"varint,6,opt,name=condition_value,json=conditionValue,proto3" json:"condition_value,omitempty"`
	InQuickKill          bool     `protobuf:"varint,7,opt,name=in_quick_kill,json=inQuickKill,proto3" json:"in_quick_kill,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKQuickKillInfo) Reset()         { *m = PgcChannelPKQuickKillInfo{} }
func (m *PgcChannelPKQuickKillInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKQuickKillInfo) ProtoMessage()    {}
func (*PgcChannelPKQuickKillInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{7}
}
func (m *PgcChannelPKQuickKillInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKQuickKillInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKQuickKillInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKQuickKillInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKQuickKillInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKQuickKillInfo.Merge(dst, src)
}
func (m *PgcChannelPKQuickKillInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKQuickKillInfo.Size(m)
}
func (m *PgcChannelPKQuickKillInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKQuickKillInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKQuickKillInfo proto.InternalMessageInfo

func (m *PgcChannelPKQuickKillInfo) GetQuickKillDescPrefix() string {
	if m != nil {
		return m.QuickKillDescPrefix
	}
	return ""
}

func (m *PgcChannelPKQuickKillInfo) GetQuickKillDesc() string {
	if m != nil {
		return m.QuickKillDesc
	}
	return ""
}

func (m *PgcChannelPKQuickKillInfo) GetQuickKillEndTs() uint32 {
	if m != nil {
		return m.QuickKillEndTs
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetEnableMinPkSec() uint32 {
	if m != nil {
		return m.EnableMinPkSec
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetEnableMaxPkSec() uint32 {
	if m != nil {
		return m.EnableMaxPkSec
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetConditionValue() uint32 {
	if m != nil {
		return m.ConditionValue
	}
	return 0
}

func (m *PgcChannelPKQuickKillInfo) GetInQuickKill() bool {
	if m != nil {
		return m.InQuickKill
	}
	return false
}

// 巅峰对决信息
type PgcChannelPKPeakInfo struct {
	PeakDesc             string   `protobuf:"bytes,1,opt,name=peak_desc,json=peakDesc,proto3" json:"peak_desc,omitempty"`
	InPeakPk             bool     `protobuf:"varint,2,opt,name=in_peak_pk,json=inPeakPk,proto3" json:"in_peak_pk,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKPeakInfo) Reset()         { *m = PgcChannelPKPeakInfo{} }
func (m *PgcChannelPKPeakInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKPeakInfo) ProtoMessage()    {}
func (*PgcChannelPKPeakInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{8}
}
func (m *PgcChannelPKPeakInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKPeakInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKPeakInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKPeakInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKPeakInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKPeakInfo.Merge(dst, src)
}
func (m *PgcChannelPKPeakInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKPeakInfo.Size(m)
}
func (m *PgcChannelPKPeakInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKPeakInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKPeakInfo proto.InternalMessageInfo

func (m *PgcChannelPKPeakInfo) GetPeakDesc() string {
	if m != nil {
		return m.PeakDesc
	}
	return ""
}

func (m *PgcChannelPKPeakInfo) GetInPeakPk() bool {
	if m != nil {
		return m.InPeakPk
	}
	return false
}

// 获取PK入口
type GetPgcChannelPKEntryReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKEntryReq) Reset()         { *m = GetPgcChannelPKEntryReq{} }
func (m *GetPgcChannelPKEntryReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKEntryReq) ProtoMessage()    {}
func (*GetPgcChannelPKEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{9}
}
func (m *GetPgcChannelPKEntryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKEntryReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKEntryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKEntryReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKEntryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKEntryReq.Merge(dst, src)
}
func (m *GetPgcChannelPKEntryReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKEntryReq.Size(m)
}
func (m *GetPgcChannelPKEntryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKEntryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKEntryReq proto.InternalMessageInfo

func (m *GetPgcChannelPKEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 获取PK入口响应
type GetPgcChannelPKEntryResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HasEntry             bool     `protobuf:"varint,2,opt,name=has_entry,json=hasEntry,proto3" json:"has_entry,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKEntryResp) Reset()         { *m = GetPgcChannelPKEntryResp{} }
func (m *GetPgcChannelPKEntryResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKEntryResp) ProtoMessage()    {}
func (*GetPgcChannelPKEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{10}
}
func (m *GetPgcChannelPKEntryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKEntryResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKEntryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKEntryResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKEntryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKEntryResp.Merge(dst, src)
}
func (m *GetPgcChannelPKEntryResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKEntryResp.Size(m)
}
func (m *GetPgcChannelPKEntryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKEntryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKEntryResp proto.InternalMessageInfo

func (m *GetPgcChannelPKEntryResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKEntryResp) GetHasEntry() bool {
	if m != nil {
		return m.HasEntry
	}
	return false
}

// 获取PK房间列表
type GetPgcChannelPKChannelListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKChannelListReq) Reset()         { *m = GetPgcChannelPKChannelListReq{} }
func (m *GetPgcChannelPKChannelListReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKChannelListReq) ProtoMessage()    {}
func (*GetPgcChannelPKChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{11}
}
func (m *GetPgcChannelPKChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKChannelListReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKChannelListReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKChannelListReq.Merge(dst, src)
}
func (m *GetPgcChannelPKChannelListReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKChannelListReq.Size(m)
}
func (m *GetPgcChannelPKChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKChannelListReq proto.InternalMessageInfo

func (m *GetPgcChannelPKChannelListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type PgcChannelPKChannelInfo struct {
	ChannelId            uint32                         `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32                         `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	ChannelIcon          string                         `protobuf:"bytes,3,opt,name=channel_icon,json=channelIcon,proto3" json:"channel_icon,omitempty"`
	ChannelName          string                         `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	BindGuildId          uint32                         `protobuf:"varint,5,opt,name=bind_guild_id,json=bindGuildId,proto3" json:"bind_guild_id,omitempty"`
	CreatorUid           uint32                         `protobuf:"varint,6,opt,name=creator_uid,json=creatorUid,proto3" json:"creator_uid,omitempty"`
	AnchorUserProfile    *UserProfile                   `protobuf:"bytes,7,opt,name=anchor_user_profile,json=anchorUserProfile,proto3" json:"anchor_user_profile,omitempty"`
	TabName              string                         `protobuf:"bytes,8,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Hot                  uint32                         `protobuf:"varint,9,opt,name=hot,proto3" json:"hot,omitempty"`
	PkStatus             PgcChannelPKChannelInfo_Status `protobuf:"varint,10,opt,name=pk_status,json=pkStatus,proto3,enum=pgc_channel_pk.PgcChannelPKChannelInfo_Status" json:"pk_status,omitempty"`
	MicFlag              PgcChannelPKOpponentMicFlag    `protobuf:"varint,11,opt,name=mic_flag,json=micFlag,proto3,enum=pgc_channel_pk.PgcChannelPKOpponentMicFlag" json:"mic_flag,omitempty"`
	TabColor             []string                       `protobuf:"bytes,12,rep,name=tab_color,json=tabColor,proto3" json:"tab_color,omitempty"`
	InteractionMicFlag   PgcChannelPKOpponentMicFlag    `protobuf:"varint,13,opt,name=interaction_mic_flag,json=interactionMicFlag,proto3,enum=pgc_channel_pk.PgcChannelPKOpponentMicFlag" json:"interaction_mic_flag,omitempty"`
	InteractionUser      *PgcChannelPKMicSpace          `protobuf:"bytes,14,opt,name=interaction_user,json=interactionUser,proto3" json:"interaction_user,omitempty"`
	ResultType           uint32                         `protobuf:"varint,15,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *PgcChannelPKChannelInfo) Reset()         { *m = PgcChannelPKChannelInfo{} }
func (m *PgcChannelPKChannelInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKChannelInfo) ProtoMessage()    {}
func (*PgcChannelPKChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{12}
}
func (m *PgcChannelPKChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKChannelInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKChannelInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKChannelInfo.Merge(dst, src)
}
func (m *PgcChannelPKChannelInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKChannelInfo.Size(m)
}
func (m *PgcChannelPKChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKChannelInfo proto.InternalMessageInfo

func (m *PgcChannelPKChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetChannelIcon() string {
	if m != nil {
		return m.ChannelIcon
	}
	return ""
}

func (m *PgcChannelPKChannelInfo) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *PgcChannelPKChannelInfo) GetBindGuildId() uint32 {
	if m != nil {
		return m.BindGuildId
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetCreatorUid() uint32 {
	if m != nil {
		return m.CreatorUid
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetAnchorUserProfile() *UserProfile {
	if m != nil {
		return m.AnchorUserProfile
	}
	return nil
}

func (m *PgcChannelPKChannelInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *PgcChannelPKChannelInfo) GetHot() uint32 {
	if m != nil {
		return m.Hot
	}
	return 0
}

func (m *PgcChannelPKChannelInfo) GetPkStatus() PgcChannelPKChannelInfo_Status {
	if m != nil {
		return m.PkStatus
	}
	return PgcChannelPKChannelInfo_NotPK
}

func (m *PgcChannelPKChannelInfo) GetMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.MicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *PgcChannelPKChannelInfo) GetTabColor() []string {
	if m != nil {
		return m.TabColor
	}
	return nil
}

func (m *PgcChannelPKChannelInfo) GetInteractionMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.InteractionMicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *PgcChannelPKChannelInfo) GetInteractionUser() *PgcChannelPKMicSpace {
	if m != nil {
		return m.InteractionUser
	}
	return nil
}

func (m *PgcChannelPKChannelInfo) GetResultType() uint32 {
	if m != nil {
		return m.ResultType
	}
	return 0
}

type GetPgcChannelPKChannelListResp struct {
	ChannelId            uint32                     `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GuildId              uint32                     `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelList          []*PgcChannelPKChannelInfo `protobuf:"bytes,3,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	PkSwitch             uint32                     `protobuf:"varint,4,opt,name=pk_switch,json=pkSwitch,proto3" json:"pk_switch,omitempty"`
	PkLimitText          string                     `protobuf:"bytes,5,opt,name=pk_limit_text,json=pkLimitText,proto3" json:"pk_limit_text,omitempty"`
	LimitPk              bool                       `protobuf:"varint,6,opt,name=limit_pk,json=limitPk,proto3" json:"limit_pk,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPgcChannelPKChannelListResp) Reset()         { *m = GetPgcChannelPKChannelListResp{} }
func (m *GetPgcChannelPKChannelListResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKChannelListResp) ProtoMessage()    {}
func (*GetPgcChannelPKChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{13}
}
func (m *GetPgcChannelPKChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKChannelListResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKChannelListResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKChannelListResp.Merge(dst, src)
}
func (m *GetPgcChannelPKChannelListResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKChannelListResp.Size(m)
}
func (m *GetPgcChannelPKChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKChannelListResp proto.InternalMessageInfo

func (m *GetPgcChannelPKChannelListResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKChannelListResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetPgcChannelPKChannelListResp) GetChannelList() []*PgcChannelPKChannelInfo {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *GetPgcChannelPKChannelListResp) GetPkSwitch() uint32 {
	if m != nil {
		return m.PkSwitch
	}
	return 0
}

func (m *GetPgcChannelPKChannelListResp) GetPkLimitText() string {
	if m != nil {
		return m.PkLimitText
	}
	return ""
}

func (m *GetPgcChannelPKChannelListResp) GetLimitPk() bool {
	if m != nil {
		return m.LimitPk
	}
	return false
}

// 切换PK状态
type SetPgcChannelPKSwitchReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkSwitch             uint32   `protobuf:"varint,2,opt,name=pk_switch,json=pkSwitch,proto3" json:"pk_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPgcChannelPKSwitchReq) Reset()         { *m = SetPgcChannelPKSwitchReq{} }
func (m *SetPgcChannelPKSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKSwitchReq) ProtoMessage()    {}
func (*SetPgcChannelPKSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{14}
}
func (m *SetPgcChannelPKSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKSwitchReq.Unmarshal(m, b)
}
func (m *SetPgcChannelPKSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKSwitchReq.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKSwitchReq.Merge(dst, src)
}
func (m *SetPgcChannelPKSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKSwitchReq.Size(m)
}
func (m *SetPgcChannelPKSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKSwitchReq proto.InternalMessageInfo

func (m *SetPgcChannelPKSwitchReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKSwitchReq) GetPkSwitch() uint32 {
	if m != nil {
		return m.PkSwitch
	}
	return 0
}

type SetPgcChannelPKSwitchResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkSwitch             uint32   `protobuf:"varint,2,opt,name=pk_switch,json=pkSwitch,proto3" json:"pk_switch,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPgcChannelPKSwitchResp) Reset()         { *m = SetPgcChannelPKSwitchResp{} }
func (m *SetPgcChannelPKSwitchResp) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKSwitchResp) ProtoMessage()    {}
func (*SetPgcChannelPKSwitchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{15}
}
func (m *SetPgcChannelPKSwitchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKSwitchResp.Unmarshal(m, b)
}
func (m *SetPgcChannelPKSwitchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKSwitchResp.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKSwitchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKSwitchResp.Merge(dst, src)
}
func (m *SetPgcChannelPKSwitchResp) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKSwitchResp.Size(m)
}
func (m *SetPgcChannelPKSwitchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKSwitchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKSwitchResp proto.InternalMessageInfo

func (m *SetPgcChannelPKSwitchResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKSwitchResp) GetPkSwitch() uint32 {
	if m != nil {
		return m.PkSwitch
	}
	return 0
}

// 发起PK
type StartPgcChannelPKReq struct {
	MyChannelId          uint32   `protobuf:"varint,1,opt,name=my_channel_id,json=myChannelId,proto3" json:"my_channel_id,omitempty"`
	ToChannelId          uint32   `protobuf:"varint,2,opt,name=to_channel_id,json=toChannelId,proto3" json:"to_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartPgcChannelPKReq) Reset()         { *m = StartPgcChannelPKReq{} }
func (m *StartPgcChannelPKReq) String() string { return proto.CompactTextString(m) }
func (*StartPgcChannelPKReq) ProtoMessage()    {}
func (*StartPgcChannelPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{16}
}
func (m *StartPgcChannelPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPgcChannelPKReq.Unmarshal(m, b)
}
func (m *StartPgcChannelPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPgcChannelPKReq.Marshal(b, m, deterministic)
}
func (dst *StartPgcChannelPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPgcChannelPKReq.Merge(dst, src)
}
func (m *StartPgcChannelPKReq) XXX_Size() int {
	return xxx_messageInfo_StartPgcChannelPKReq.Size(m)
}
func (m *StartPgcChannelPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPgcChannelPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartPgcChannelPKReq proto.InternalMessageInfo

func (m *StartPgcChannelPKReq) GetMyChannelId() uint32 {
	if m != nil {
		return m.MyChannelId
	}
	return 0
}

func (m *StartPgcChannelPKReq) GetToChannelId() uint32 {
	if m != nil {
		return m.ToChannelId
	}
	return 0
}

type StartPgcChannelPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartPgcChannelPKResp) Reset()         { *m = StartPgcChannelPKResp{} }
func (m *StartPgcChannelPKResp) String() string { return proto.CompactTextString(m) }
func (*StartPgcChannelPKResp) ProtoMessage()    {}
func (*StartPgcChannelPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{17}
}
func (m *StartPgcChannelPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartPgcChannelPKResp.Unmarshal(m, b)
}
func (m *StartPgcChannelPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartPgcChannelPKResp.Marshal(b, m, deterministic)
}
func (dst *StartPgcChannelPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartPgcChannelPKResp.Merge(dst, src)
}
func (m *StartPgcChannelPKResp) XXX_Size() int {
	return xxx_messageInfo_StartPgcChannelPKResp.Size(m)
}
func (m *StartPgcChannelPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartPgcChannelPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartPgcChannelPKResp proto.InternalMessageInfo

// 处理收到PK邀请
type AcceptPgcChannelPKReq struct {
	MyChannelId          uint32   `protobuf:"varint,1,opt,name=my_channel_id,json=myChannelId,proto3" json:"my_channel_id,omitempty"`
	FromChannelId        uint32   `protobuf:"varint,2,opt,name=from_channel_id,json=fromChannelId,proto3" json:"from_channel_id,omitempty"`
	Accept               bool     `protobuf:"varint,3,opt,name=accept,proto3" json:"accept,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptPgcChannelPKReq) Reset()         { *m = AcceptPgcChannelPKReq{} }
func (m *AcceptPgcChannelPKReq) String() string { return proto.CompactTextString(m) }
func (*AcceptPgcChannelPKReq) ProtoMessage()    {}
func (*AcceptPgcChannelPKReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{18}
}
func (m *AcceptPgcChannelPKReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptPgcChannelPKReq.Unmarshal(m, b)
}
func (m *AcceptPgcChannelPKReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptPgcChannelPKReq.Marshal(b, m, deterministic)
}
func (dst *AcceptPgcChannelPKReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptPgcChannelPKReq.Merge(dst, src)
}
func (m *AcceptPgcChannelPKReq) XXX_Size() int {
	return xxx_messageInfo_AcceptPgcChannelPKReq.Size(m)
}
func (m *AcceptPgcChannelPKReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptPgcChannelPKReq.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptPgcChannelPKReq proto.InternalMessageInfo

func (m *AcceptPgcChannelPKReq) GetMyChannelId() uint32 {
	if m != nil {
		return m.MyChannelId
	}
	return 0
}

func (m *AcceptPgcChannelPKReq) GetFromChannelId() uint32 {
	if m != nil {
		return m.FromChannelId
	}
	return 0
}

func (m *AcceptPgcChannelPKReq) GetAccept() bool {
	if m != nil {
		return m.Accept
	}
	return false
}

type AcceptPgcChannelPKResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AcceptPgcChannelPKResp) Reset()         { *m = AcceptPgcChannelPKResp{} }
func (m *AcceptPgcChannelPKResp) String() string { return proto.CompactTextString(m) }
func (*AcceptPgcChannelPKResp) ProtoMessage()    {}
func (*AcceptPgcChannelPKResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{19}
}
func (m *AcceptPgcChannelPKResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AcceptPgcChannelPKResp.Unmarshal(m, b)
}
func (m *AcceptPgcChannelPKResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AcceptPgcChannelPKResp.Marshal(b, m, deterministic)
}
func (dst *AcceptPgcChannelPKResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AcceptPgcChannelPKResp.Merge(dst, src)
}
func (m *AcceptPgcChannelPKResp) XXX_Size() int {
	return xxx_messageInfo_AcceptPgcChannelPKResp.Size(m)
}
func (m *AcceptPgcChannelPKResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AcceptPgcChannelPKResp.DiscardUnknown(m)
}

var xxx_messageInfo_AcceptPgcChannelPKResp proto.InternalMessageInfo

type PgcChannelPKMicSpace struct {
	MicId                uint32       `protobuf:"varint,1,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	UserProfile          *UserProfile `protobuf:"bytes,2,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	ChannelClientId      string       `protobuf:"bytes,3,opt,name=channel_client_id,json=channelClientId,proto3" json:"channel_client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PgcChannelPKMicSpace) Reset()         { *m = PgcChannelPKMicSpace{} }
func (m *PgcChannelPKMicSpace) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKMicSpace) ProtoMessage()    {}
func (*PgcChannelPKMicSpace) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{20}
}
func (m *PgcChannelPKMicSpace) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKMicSpace.Unmarshal(m, b)
}
func (m *PgcChannelPKMicSpace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKMicSpace.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKMicSpace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKMicSpace.Merge(dst, src)
}
func (m *PgcChannelPKMicSpace) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKMicSpace.Size(m)
}
func (m *PgcChannelPKMicSpace) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKMicSpace.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKMicSpace proto.InternalMessageInfo

func (m *PgcChannelPKMicSpace) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *PgcChannelPKMicSpace) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PgcChannelPKMicSpace) GetChannelClientId() string {
	if m != nil {
		return m.ChannelClientId
	}
	return ""
}

// PK实时战况
type PgcChannelPKBattle struct {
	PkList               []*PgcChannelPKInfo `protobuf:"bytes,1,rep,name=pk_list,json=pkList,proto3" json:"pk_list,omitempty"`
	PkEndTs              uint32              `protobuf:"varint,2,opt,name=pk_end_ts,json=pkEndTs,proto3" json:"pk_end_ts,omitempty"`
	ServerNs             int64               `protobuf:"varint,3,opt,name=server_ns,json=serverNs,proto3" json:"server_ns,omitempty"`
	ValidPkDesc          string              `protobuf:"bytes,4,opt,name=valid_pk_desc,json=validPkDesc,proto3" json:"valid_pk_desc,omitempty"`
	ValidPk              bool                `protobuf:"varint,5,opt,name=valid_pk,json=validPk,proto3" json:"valid_pk,omitempty"`
	Prase                uint32              `protobuf:"varint,6,opt,name=prase,proto3" json:"prase,omitempty"`
	PkId                 uint32              `protobuf:"varint,7,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	StatusEndTs          uint32              `protobuf:"varint,8,opt,name=status_end_ts,json=statusEndTs,proto3" json:"status_end_ts,omitempty"`
	StatusDesc           string              `protobuf:"bytes,9,opt,name=status_desc,json=statusDesc,proto3" json:"status_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *PgcChannelPKBattle) Reset()         { *m = PgcChannelPKBattle{} }
func (m *PgcChannelPKBattle) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKBattle) ProtoMessage()    {}
func (*PgcChannelPKBattle) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{21}
}
func (m *PgcChannelPKBattle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKBattle.Unmarshal(m, b)
}
func (m *PgcChannelPKBattle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKBattle.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKBattle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKBattle.Merge(dst, src)
}
func (m *PgcChannelPKBattle) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKBattle.Size(m)
}
func (m *PgcChannelPKBattle) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKBattle.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKBattle proto.InternalMessageInfo

func (m *PgcChannelPKBattle) GetPkList() []*PgcChannelPKInfo {
	if m != nil {
		return m.PkList
	}
	return nil
}

func (m *PgcChannelPKBattle) GetPkEndTs() uint32 {
	if m != nil {
		return m.PkEndTs
	}
	return 0
}

func (m *PgcChannelPKBattle) GetServerNs() int64 {
	if m != nil {
		return m.ServerNs
	}
	return 0
}

func (m *PgcChannelPKBattle) GetValidPkDesc() string {
	if m != nil {
		return m.ValidPkDesc
	}
	return ""
}

func (m *PgcChannelPKBattle) GetValidPk() bool {
	if m != nil {
		return m.ValidPk
	}
	return false
}

func (m *PgcChannelPKBattle) GetPrase() uint32 {
	if m != nil {
		return m.Prase
	}
	return 0
}

func (m *PgcChannelPKBattle) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *PgcChannelPKBattle) GetStatusEndTs() uint32 {
	if m != nil {
		return m.StatusEndTs
	}
	return 0
}

func (m *PgcChannelPKBattle) GetStatusDesc() string {
	if m != nil {
		return m.StatusDesc
	}
	return ""
}

type HeadIconSourceConf struct {
	MinScore             uint32   `protobuf:"varint,1,opt,name=min_score,json=minScore,proto3" json:"min_score,omitempty"`
	MaxScore             uint32   `protobuf:"varint,2,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	PicList              []string `protobuf:"bytes,3,rep,name=pic_list,json=picList,proto3" json:"pic_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HeadIconSourceConf) Reset()         { *m = HeadIconSourceConf{} }
func (m *HeadIconSourceConf) String() string { return proto.CompactTextString(m) }
func (*HeadIconSourceConf) ProtoMessage()    {}
func (*HeadIconSourceConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{22}
}
func (m *HeadIconSourceConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HeadIconSourceConf.Unmarshal(m, b)
}
func (m *HeadIconSourceConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HeadIconSourceConf.Marshal(b, m, deterministic)
}
func (dst *HeadIconSourceConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HeadIconSourceConf.Merge(dst, src)
}
func (m *HeadIconSourceConf) XXX_Size() int {
	return xxx_messageInfo_HeadIconSourceConf.Size(m)
}
func (m *HeadIconSourceConf) XXX_DiscardUnknown() {
	xxx_messageInfo_HeadIconSourceConf.DiscardUnknown(m)
}

var xxx_messageInfo_HeadIconSourceConf proto.InternalMessageInfo

func (m *HeadIconSourceConf) GetMinScore() uint32 {
	if m != nil {
		return m.MinScore
	}
	return 0
}

func (m *HeadIconSourceConf) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *HeadIconSourceConf) GetPicList() []string {
	if m != nil {
		return m.PicList
	}
	return nil
}

// 获取房间PK信息
type GetPgcChannelPKInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKInfoReq) Reset()         { *m = GetPgcChannelPKInfoReq{} }
func (m *GetPgcChannelPKInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKInfoReq) ProtoMessage()    {}
func (*GetPgcChannelPKInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{23}
}
func (m *GetPgcChannelPKInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKInfoReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKInfoReq.Merge(dst, src)
}
func (m *GetPgcChannelPKInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKInfoReq.Size(m)
}
func (m *GetPgcChannelPKInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKInfoReq proto.InternalMessageInfo

func (m *GetPgcChannelPKInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetPgcChannelPKInfoResp struct {
	PkBattleInfo         *PgcChannelPKBattle        `protobuf:"bytes,1,opt,name=pk_battle_info,json=pkBattleInfo,proto3" json:"pk_battle_info,omitempty"`
	QuickKillInfo        *PgcChannelPKQuickKillInfo `protobuf:"bytes,2,opt,name=quick_kill_info,json=quickKillInfo,proto3" json:"quick_kill_info,omitempty"`
	PeakInfo             *PgcChannelPKPeakInfo      `protobuf:"bytes,3,opt,name=peak_info,json=peakInfo,proto3" json:"peak_info,omitempty"`
	LastMvpInfo          []*PgcChannelPKMicRankInfo `protobuf:"bytes,4,rep,name=last_mvp_info,json=lastMvpInfo,proto3" json:"last_mvp_info,omitempty"`
	LastMvpExpired       uint32                     `protobuf:"varint,5,opt,name=last_mvp_expired,json=lastMvpExpired,proto3" json:"last_mvp_expired,omitempty"`
	IconConfList         []*HeadIconSourceConf      `protobuf:"bytes,6,rep,name=icon_conf_list,json=iconConfList,proto3" json:"icon_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetPgcChannelPKInfoResp) Reset()         { *m = GetPgcChannelPKInfoResp{} }
func (m *GetPgcChannelPKInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKInfoResp) ProtoMessage()    {}
func (*GetPgcChannelPKInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{24}
}
func (m *GetPgcChannelPKInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKInfoResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKInfoResp.Merge(dst, src)
}
func (m *GetPgcChannelPKInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKInfoResp.Size(m)
}
func (m *GetPgcChannelPKInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKInfoResp proto.InternalMessageInfo

func (m *GetPgcChannelPKInfoResp) GetPkBattleInfo() *PgcChannelPKBattle {
	if m != nil {
		return m.PkBattleInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetQuickKillInfo() *PgcChannelPKQuickKillInfo {
	if m != nil {
		return m.QuickKillInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetPeakInfo() *PgcChannelPKPeakInfo {
	if m != nil {
		return m.PeakInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetLastMvpInfo() []*PgcChannelPKMicRankInfo {
	if m != nil {
		return m.LastMvpInfo
	}
	return nil
}

func (m *GetPgcChannelPKInfoResp) GetLastMvpExpired() uint32 {
	if m != nil {
		return m.LastMvpExpired
	}
	return 0
}

func (m *GetPgcChannelPKInfoResp) GetIconConfList() []*HeadIconSourceConf {
	if m != nil {
		return m.IconConfList
	}
	return nil
}

// 进入PK房间，在麦位上的上报一次，之后有新用户上麦，或者端语音流变化上报
type PgcChannelPKReportClientIDChangeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MicId                uint32   `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,4,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	ClientId             string   `protobuf:"bytes,5,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKReportClientIDChangeReq) Reset()         { *m = PgcChannelPKReportClientIDChangeReq{} }
func (m *PgcChannelPKReportClientIDChangeReq) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKReportClientIDChangeReq) ProtoMessage()    {}
func (*PgcChannelPKReportClientIDChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{25}
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Unmarshal(m, b)
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKReportClientIDChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Merge(dst, src)
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.Size(m)
}
func (m *PgcChannelPKReportClientIDChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKReportClientIDChangeReq proto.InternalMessageInfo

func (m *PgcChannelPKReportClientIDChangeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *PgcChannelPKReportClientIDChangeReq) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

type PgcChannelPKReportClientIDChangeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PgcChannelPKReportClientIDChangeResp) Reset()         { *m = PgcChannelPKReportClientIDChangeResp{} }
func (m *PgcChannelPKReportClientIDChangeResp) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKReportClientIDChangeResp) ProtoMessage()    {}
func (*PgcChannelPKReportClientIDChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{26}
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Unmarshal(m, b)
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKReportClientIDChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Merge(dst, src)
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.Size(m)
}
func (m *PgcChannelPKReportClientIDChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKReportClientIDChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKReportClientIDChangeResp proto.InternalMessageInfo

// 主播设置PK中是否能听到对面语音
type SetPgcChannelPKOpponentMicFlagReq struct {
	ChannelId            uint32                      `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32                      `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	OptMicFlag           PgcChannelPKOpponentMicFlag `protobuf:"varint,3,opt,name=opt_mic_flag,json=optMicFlag,proto3,enum=pgc_channel_pk.PgcChannelPKOpponentMicFlag" json:"opt_mic_flag,omitempty"`
	MicId                uint32                      `protobuf:"varint,4,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *SetPgcChannelPKOpponentMicFlagReq) Reset()         { *m = SetPgcChannelPKOpponentMicFlagReq{} }
func (m *SetPgcChannelPKOpponentMicFlagReq) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKOpponentMicFlagReq) ProtoMessage()    {}
func (*SetPgcChannelPKOpponentMicFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{27}
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Unmarshal(m, b)
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKOpponentMicFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Merge(dst, src)
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.Size(m)
}
func (m *SetPgcChannelPKOpponentMicFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKOpponentMicFlagReq proto.InternalMessageInfo

func (m *SetPgcChannelPKOpponentMicFlagReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetOptMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.OptMicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *SetPgcChannelPKOpponentMicFlagReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type SetPgcChannelPKOpponentMicFlagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPgcChannelPKOpponentMicFlagResp) Reset()         { *m = SetPgcChannelPKOpponentMicFlagResp{} }
func (m *SetPgcChannelPKOpponentMicFlagResp) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKOpponentMicFlagResp) ProtoMessage()    {}
func (*SetPgcChannelPKOpponentMicFlagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{28}
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Unmarshal(m, b)
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKOpponentMicFlagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Merge(dst, src)
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.Size(m)
}
func (m *SetPgcChannelPKOpponentMicFlagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKOpponentMicFlagResp proto.InternalMessageInfo

// 主播设置PK中能否听到对方房间语音房间推送，所有人根据这个推送确定能否听到对面房间声音。包括主播自己
type PgcChannelPKOpponentMicFlagPushMsg struct {
	OptMicFlag           PgcChannelPKOpponentMicFlag `protobuf:"varint,1,opt,name=opt_mic_flag,json=optMicFlag,proto3,enum=pgc_channel_pk.PgcChannelPKOpponentMicFlag" json:"opt_mic_flag,omitempty"`
	ChannelId            uint32                      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	BeBanMicChannelId    uint32                      `protobuf:"varint,3,opt,name=be_ban_mic_channel_id,json=beBanMicChannelId,proto3" json:"be_ban_mic_channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) Reset()         { *m = PgcChannelPKOpponentMicFlagPushMsg{} }
func (m *PgcChannelPKOpponentMicFlagPushMsg) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKOpponentMicFlagPushMsg) ProtoMessage()    {}
func (*PgcChannelPKOpponentMicFlagPushMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{29}
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Unmarshal(m, b)
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKOpponentMicFlagPushMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Merge(dst, src)
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.Size(m)
}
func (m *PgcChannelPKOpponentMicFlagPushMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKOpponentMicFlagPushMsg proto.InternalMessageInfo

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetOptMicFlag() PgcChannelPKOpponentMicFlag {
	if m != nil {
		return m.OptMicFlag
	}
	return PgcChannelPKOpponentMicFlag_PgcPKMic_OPEN
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *PgcChannelPKOpponentMicFlagPushMsg) GetBeBanMicChannelId() uint32 {
	if m != nil {
		return m.BeBanMicChannelId
	}
	return 0
}

// 获取自己在当前PK中送给to_uid的送礼值
type GetPgcChannelPKSendGiftScoreReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MyUid                uint32   `protobuf:"varint,3,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,4,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKSendGiftScoreReq) Reset()         { *m = GetPgcChannelPKSendGiftScoreReq{} }
func (m *GetPgcChannelPKSendGiftScoreReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKSendGiftScoreReq) ProtoMessage()    {}
func (*GetPgcChannelPKSendGiftScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{30}
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKSendGiftScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Merge(dst, src)
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.Size(m)
}
func (m *GetPgcChannelPKSendGiftScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKSendGiftScoreReq proto.InternalMessageInfo

func (m *GetPgcChannelPKSendGiftScoreReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type GetPgcChannelPKSendGiftScoreResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MyUid                uint32   `protobuf:"varint,3,opt,name=my_uid,json=myUid,proto3" json:"my_uid,omitempty"`
	ToUid                uint32   `protobuf:"varint,4,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	Score                uint32   `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKSendGiftScoreResp) Reset()         { *m = GetPgcChannelPKSendGiftScoreResp{} }
func (m *GetPgcChannelPKSendGiftScoreResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKSendGiftScoreResp) ProtoMessage()    {}
func (*GetPgcChannelPKSendGiftScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{31}
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKSendGiftScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Merge(dst, src)
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.Size(m)
}
func (m *GetPgcChannelPKSendGiftScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKSendGiftScoreResp proto.InternalMessageInfo

func (m *GetPgcChannelPKSendGiftScoreResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetMyUid() uint32 {
	if m != nil {
		return m.MyUid
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *GetPgcChannelPKSendGiftScoreResp) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

// 获取PK观众火力榜
type GetPgcChannelPKAudienceRankReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	Begin                uint32   `protobuf:"varint,3,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKAudienceRankReq) Reset()         { *m = GetPgcChannelPKAudienceRankReq{} }
func (m *GetPgcChannelPKAudienceRankReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKAudienceRankReq) ProtoMessage()    {}
func (*GetPgcChannelPKAudienceRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{32}
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKAudienceRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Merge(dst, src)
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankReq.Size(m)
}
func (m *GetPgcChannelPKAudienceRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKAudienceRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKAudienceRankReq proto.InternalMessageInfo

func (m *GetPgcChannelPKAudienceRankReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKAudienceRankReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *GetPgcChannelPKAudienceRankReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *GetPgcChannelPKAudienceRankReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type PgcChannelPKAudienceInfo struct {
	UserProfile          *UserProfile `protobuf:"bytes,1,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	Score                uint32       `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	Rich                 uint32       `protobuf:"varint,3,opt,name=rich,proto3" json:"rich,omitempty"`
	Charm                uint32       `protobuf:"varint,4,opt,name=charm,proto3" json:"charm,omitempty"`
	NobilityLevel        uint32       `protobuf:"varint,5,opt,name=nobility_level,json=nobilityLevel,proto3" json:"nobility_level,omitempty"`
	GroupFansLevel       uint32       `protobuf:"varint,6,opt,name=group_fans_level,json=groupFansLevel,proto3" json:"group_fans_level,omitempty"`
	ChannelMemLevel      uint32       `protobuf:"varint,7,opt,name=channel_mem_level,json=channelMemLevel,proto3" json:"channel_mem_level,omitempty"`
	GroupName            string       `protobuf:"bytes,8,opt,name=group_name,json=groupName,proto3" json:"group_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *PgcChannelPKAudienceInfo) Reset()         { *m = PgcChannelPKAudienceInfo{} }
func (m *PgcChannelPKAudienceInfo) String() string { return proto.CompactTextString(m) }
func (*PgcChannelPKAudienceInfo) ProtoMessage()    {}
func (*PgcChannelPKAudienceInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{33}
}
func (m *PgcChannelPKAudienceInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PgcChannelPKAudienceInfo.Unmarshal(m, b)
}
func (m *PgcChannelPKAudienceInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PgcChannelPKAudienceInfo.Marshal(b, m, deterministic)
}
func (dst *PgcChannelPKAudienceInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PgcChannelPKAudienceInfo.Merge(dst, src)
}
func (m *PgcChannelPKAudienceInfo) XXX_Size() int {
	return xxx_messageInfo_PgcChannelPKAudienceInfo.Size(m)
}
func (m *PgcChannelPKAudienceInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PgcChannelPKAudienceInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PgcChannelPKAudienceInfo proto.InternalMessageInfo

func (m *PgcChannelPKAudienceInfo) GetUserProfile() *UserProfile {
	if m != nil {
		return m.UserProfile
	}
	return nil
}

func (m *PgcChannelPKAudienceInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetRich() uint32 {
	if m != nil {
		return m.Rich
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetCharm() uint32 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetNobilityLevel() uint32 {
	if m != nil {
		return m.NobilityLevel
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetGroupFansLevel() uint32 {
	if m != nil {
		return m.GroupFansLevel
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetChannelMemLevel() uint32 {
	if m != nil {
		return m.ChannelMemLevel
	}
	return 0
}

func (m *PgcChannelPKAudienceInfo) GetGroupName() string {
	if m != nil {
		return m.GroupName
	}
	return ""
}

type GetPgcChannelPKAudienceRankResp struct {
	RankList             []*PgcChannelPKAudienceInfo `protobuf:"bytes,1,rep,name=rank_list,json=rankList,proto3" json:"rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetPgcChannelPKAudienceRankResp) Reset()         { *m = GetPgcChannelPKAudienceRankResp{} }
func (m *GetPgcChannelPKAudienceRankResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKAudienceRankResp) ProtoMessage()    {}
func (*GetPgcChannelPKAudienceRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{34}
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKAudienceRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Merge(dst, src)
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKAudienceRankResp.Size(m)
}
func (m *GetPgcChannelPKAudienceRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKAudienceRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKAudienceRankResp proto.InternalMessageInfo

func (m *GetPgcChannelPKAudienceRankResp) GetRankList() []*PgcChannelPKAudienceInfo {
	if m != nil {
		return m.RankList
	}
	return nil
}

type TestSetPgcChannelPKStatusReq struct {
	FromChannelId        uint32   `protobuf:"varint,1,opt,name=from_channel_id,json=fromChannelId,proto3" json:"from_channel_id,omitempty"`
	ToChannelId          uint32   `protobuf:"varint,2,opt,name=to_channel_id,json=toChannelId,proto3" json:"to_channel_id,omitempty"`
	Status               uint32   `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	FromScore            uint32   `protobuf:"varint,4,opt,name=from_score,json=fromScore,proto3" json:"from_score,omitempty"`
	ToScore              uint32   `protobuf:"varint,5,opt,name=to_score,json=toScore,proto3" json:"to_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSetPgcChannelPKStatusReq) Reset()         { *m = TestSetPgcChannelPKStatusReq{} }
func (m *TestSetPgcChannelPKStatusReq) String() string { return proto.CompactTextString(m) }
func (*TestSetPgcChannelPKStatusReq) ProtoMessage()    {}
func (*TestSetPgcChannelPKStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{35}
}
func (m *TestSetPgcChannelPKStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSetPgcChannelPKStatusReq.Unmarshal(m, b)
}
func (m *TestSetPgcChannelPKStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSetPgcChannelPKStatusReq.Marshal(b, m, deterministic)
}
func (dst *TestSetPgcChannelPKStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSetPgcChannelPKStatusReq.Merge(dst, src)
}
func (m *TestSetPgcChannelPKStatusReq) XXX_Size() int {
	return xxx_messageInfo_TestSetPgcChannelPKStatusReq.Size(m)
}
func (m *TestSetPgcChannelPKStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSetPgcChannelPKStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestSetPgcChannelPKStatusReq proto.InternalMessageInfo

func (m *TestSetPgcChannelPKStatusReq) GetFromChannelId() uint32 {
	if m != nil {
		return m.FromChannelId
	}
	return 0
}

func (m *TestSetPgcChannelPKStatusReq) GetToChannelId() uint32 {
	if m != nil {
		return m.ToChannelId
	}
	return 0
}

func (m *TestSetPgcChannelPKStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TestSetPgcChannelPKStatusReq) GetFromScore() uint32 {
	if m != nil {
		return m.FromScore
	}
	return 0
}

func (m *TestSetPgcChannelPKStatusReq) GetToScore() uint32 {
	if m != nil {
		return m.ToScore
	}
	return 0
}

type TestSetPgcChannelPKStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestSetPgcChannelPKStatusResp) Reset()         { *m = TestSetPgcChannelPKStatusResp{} }
func (m *TestSetPgcChannelPKStatusResp) String() string { return proto.CompactTextString(m) }
func (*TestSetPgcChannelPKStatusResp) ProtoMessage()    {}
func (*TestSetPgcChannelPKStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{36}
}
func (m *TestSetPgcChannelPKStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestSetPgcChannelPKStatusResp.Unmarshal(m, b)
}
func (m *TestSetPgcChannelPKStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestSetPgcChannelPKStatusResp.Marshal(b, m, deterministic)
}
func (dst *TestSetPgcChannelPKStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestSetPgcChannelPKStatusResp.Merge(dst, src)
}
func (m *TestSetPgcChannelPKStatusResp) XXX_Size() int {
	return xxx_messageInfo_TestSetPgcChannelPKStatusResp.Size(m)
}
func (m *TestSetPgcChannelPKStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestSetPgcChannelPKStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestSetPgcChannelPKStatusResp proto.InternalMessageInfo

type GetPgcChannelPKIdReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKIdReq) Reset()         { *m = GetPgcChannelPKIdReq{} }
func (m *GetPgcChannelPKIdReq) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKIdReq) ProtoMessage()    {}
func (*GetPgcChannelPKIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{37}
}
func (m *GetPgcChannelPKIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKIdReq.Unmarshal(m, b)
}
func (m *GetPgcChannelPKIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKIdReq.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKIdReq.Merge(dst, src)
}
func (m *GetPgcChannelPKIdReq) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKIdReq.Size(m)
}
func (m *GetPgcChannelPKIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKIdReq proto.InternalMessageInfo

func (m *GetPgcChannelPKIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetPgcChannelPKIdResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPgcChannelPKIdResp) Reset()         { *m = GetPgcChannelPKIdResp{} }
func (m *GetPgcChannelPKIdResp) String() string { return proto.CompactTextString(m) }
func (*GetPgcChannelPKIdResp) ProtoMessage()    {}
func (*GetPgcChannelPKIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{38}
}
func (m *GetPgcChannelPKIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPgcChannelPKIdResp.Unmarshal(m, b)
}
func (m *GetPgcChannelPKIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPgcChannelPKIdResp.Marshal(b, m, deterministic)
}
func (dst *GetPgcChannelPKIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPgcChannelPKIdResp.Merge(dst, src)
}
func (m *GetPgcChannelPKIdResp) XXX_Size() int {
	return xxx_messageInfo_GetPgcChannelPKIdResp.Size(m)
}
func (m *GetPgcChannelPKIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPgcChannelPKIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPgcChannelPKIdResp proto.InternalMessageInfo

func (m *GetPgcChannelPKIdResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetPgcChannelPKIdResp) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

type TestPushMvpImMsgReq struct {
	UidA                 uint32   `protobuf:"varint,1,opt,name=uid_a,json=uidA,proto3" json:"uid_a,omitempty"`
	UidB                 uint32   `protobuf:"varint,2,opt,name=uid_b,json=uidB,proto3" json:"uid_b,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	Rank                 uint32   `protobuf:"varint,5,opt,name=rank,proto3" json:"rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushMvpImMsgReq) Reset()         { *m = TestPushMvpImMsgReq{} }
func (m *TestPushMvpImMsgReq) String() string { return proto.CompactTextString(m) }
func (*TestPushMvpImMsgReq) ProtoMessage()    {}
func (*TestPushMvpImMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{39}
}
func (m *TestPushMvpImMsgReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushMvpImMsgReq.Unmarshal(m, b)
}
func (m *TestPushMvpImMsgReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushMvpImMsgReq.Marshal(b, m, deterministic)
}
func (dst *TestPushMvpImMsgReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushMvpImMsgReq.Merge(dst, src)
}
func (m *TestPushMvpImMsgReq) XXX_Size() int {
	return xxx_messageInfo_TestPushMvpImMsgReq.Size(m)
}
func (m *TestPushMvpImMsgReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushMvpImMsgReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushMvpImMsgReq proto.InternalMessageInfo

func (m *TestPushMvpImMsgReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *TestPushMvpImMsgReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *TestPushMvpImMsgReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *TestPushMvpImMsgReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *TestPushMvpImMsgReq) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

type TestPushMvpImMsgResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestPushMvpImMsgResp) Reset()         { *m = TestPushMvpImMsgResp{} }
func (m *TestPushMvpImMsgResp) String() string { return proto.CompactTextString(m) }
func (*TestPushMvpImMsgResp) ProtoMessage()    {}
func (*TestPushMvpImMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{40}
}
func (m *TestPushMvpImMsgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestPushMvpImMsgResp.Unmarshal(m, b)
}
func (m *TestPushMvpImMsgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestPushMvpImMsgResp.Marshal(b, m, deterministic)
}
func (dst *TestPushMvpImMsgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestPushMvpImMsgResp.Merge(dst, src)
}
func (m *TestPushMvpImMsgResp) XXX_Size() int {
	return xxx_messageInfo_TestPushMvpImMsgResp.Size(m)
}
func (m *TestPushMvpImMsgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestPushMvpImMsgResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestPushMvpImMsgResp proto.InternalMessageInfo

// 选择互动玩家
type ChoseInteractionReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	MicId                uint32   `protobuf:"varint,3,opt,name=mic_id,json=micId,proto3" json:"mic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChoseInteractionReq) Reset()         { *m = ChoseInteractionReq{} }
func (m *ChoseInteractionReq) String() string { return proto.CompactTextString(m) }
func (*ChoseInteractionReq) ProtoMessage()    {}
func (*ChoseInteractionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{41}
}
func (m *ChoseInteractionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseInteractionReq.Unmarshal(m, b)
}
func (m *ChoseInteractionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseInteractionReq.Marshal(b, m, deterministic)
}
func (dst *ChoseInteractionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseInteractionReq.Merge(dst, src)
}
func (m *ChoseInteractionReq) XXX_Size() int {
	return xxx_messageInfo_ChoseInteractionReq.Size(m)
}
func (m *ChoseInteractionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseInteractionReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseInteractionReq proto.InternalMessageInfo

func (m *ChoseInteractionReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChoseInteractionReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

func (m *ChoseInteractionReq) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

type ChoseInteractionResp struct {
	Toast                string   `protobuf:"bytes,1,opt,name=toast,proto3" json:"toast,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChoseInteractionResp) Reset()         { *m = ChoseInteractionResp{} }
func (m *ChoseInteractionResp) String() string { return proto.CompactTextString(m) }
func (*ChoseInteractionResp) ProtoMessage()    {}
func (*ChoseInteractionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{42}
}
func (m *ChoseInteractionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChoseInteractionResp.Unmarshal(m, b)
}
func (m *ChoseInteractionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChoseInteractionResp.Marshal(b, m, deterministic)
}
func (dst *ChoseInteractionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChoseInteractionResp.Merge(dst, src)
}
func (m *ChoseInteractionResp) XXX_Size() int {
	return xxx_messageInfo_ChoseInteractionResp.Size(m)
}
func (m *ChoseInteractionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChoseInteractionResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChoseInteractionResp proto.InternalMessageInfo

func (m *ChoseInteractionResp) GetToast() string {
	if m != nil {
		return m.Toast
	}
	return ""
}

type SetPgcChannelPKEndReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PkId                 uint32   `protobuf:"varint,2,opt,name=pk_id,json=pkId,proto3" json:"pk_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPgcChannelPKEndReq) Reset()         { *m = SetPgcChannelPKEndReq{} }
func (m *SetPgcChannelPKEndReq) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKEndReq) ProtoMessage()    {}
func (*SetPgcChannelPKEndReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{43}
}
func (m *SetPgcChannelPKEndReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKEndReq.Unmarshal(m, b)
}
func (m *SetPgcChannelPKEndReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKEndReq.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKEndReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKEndReq.Merge(dst, src)
}
func (m *SetPgcChannelPKEndReq) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKEndReq.Size(m)
}
func (m *SetPgcChannelPKEndReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKEndReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKEndReq proto.InternalMessageInfo

func (m *SetPgcChannelPKEndReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetPgcChannelPKEndReq) GetPkId() uint32 {
	if m != nil {
		return m.PkId
	}
	return 0
}

type SetPgcChannelPKEndResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPgcChannelPKEndResp) Reset()         { *m = SetPgcChannelPKEndResp{} }
func (m *SetPgcChannelPKEndResp) String() string { return proto.CompactTextString(m) }
func (*SetPgcChannelPKEndResp) ProtoMessage()    {}
func (*SetPgcChannelPKEndResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0, []int{44}
}
func (m *SetPgcChannelPKEndResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPgcChannelPKEndResp.Unmarshal(m, b)
}
func (m *SetPgcChannelPKEndResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPgcChannelPKEndResp.Marshal(b, m, deterministic)
}
func (dst *SetPgcChannelPKEndResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPgcChannelPKEndResp.Merge(dst, src)
}
func (m *SetPgcChannelPKEndResp) XXX_Size() int {
	return xxx_messageInfo_SetPgcChannelPKEndResp.Size(m)
}
func (m *SetPgcChannelPKEndResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPgcChannelPKEndResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetPgcChannelPKEndResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*DownloadSourceInfo)(nil), "pgc_channel_pk.DownloadSourceInfo")
	proto.RegisterType((*UserProfile)(nil), "pgc_channel_pk.UserProfile")
	proto.RegisterType((*UserPrivilege)(nil), "pgc_channel_pk.UserPrivilege")
	proto.RegisterType((*UserUKWInfo)(nil), "pgc_channel_pk.UserUKWInfo")
	proto.RegisterType((*PgcChannelPKAnchor)(nil), "pgc_channel_pk.PgcChannelPKAnchor")
	proto.RegisterType((*PgcChannelPKMicRankInfo)(nil), "pgc_channel_pk.PgcChannelPKMicRankInfo")
	proto.RegisterType((*PgcChannelPKInfo)(nil), "pgc_channel_pk.PgcChannelPKInfo")
	proto.RegisterType((*PgcChannelPKQuickKillInfo)(nil), "pgc_channel_pk.PgcChannelPKQuickKillInfo")
	proto.RegisterType((*PgcChannelPKPeakInfo)(nil), "pgc_channel_pk.PgcChannelPKPeakInfo")
	proto.RegisterType((*GetPgcChannelPKEntryReq)(nil), "pgc_channel_pk.GetPgcChannelPKEntryReq")
	proto.RegisterType((*GetPgcChannelPKEntryResp)(nil), "pgc_channel_pk.GetPgcChannelPKEntryResp")
	proto.RegisterType((*GetPgcChannelPKChannelListReq)(nil), "pgc_channel_pk.GetPgcChannelPKChannelListReq")
	proto.RegisterType((*PgcChannelPKChannelInfo)(nil), "pgc_channel_pk.PgcChannelPKChannelInfo")
	proto.RegisterType((*GetPgcChannelPKChannelListResp)(nil), "pgc_channel_pk.GetPgcChannelPKChannelListResp")
	proto.RegisterType((*SetPgcChannelPKSwitchReq)(nil), "pgc_channel_pk.SetPgcChannelPKSwitchReq")
	proto.RegisterType((*SetPgcChannelPKSwitchResp)(nil), "pgc_channel_pk.SetPgcChannelPKSwitchResp")
	proto.RegisterType((*StartPgcChannelPKReq)(nil), "pgc_channel_pk.StartPgcChannelPKReq")
	proto.RegisterType((*StartPgcChannelPKResp)(nil), "pgc_channel_pk.StartPgcChannelPKResp")
	proto.RegisterType((*AcceptPgcChannelPKReq)(nil), "pgc_channel_pk.AcceptPgcChannelPKReq")
	proto.RegisterType((*AcceptPgcChannelPKResp)(nil), "pgc_channel_pk.AcceptPgcChannelPKResp")
	proto.RegisterType((*PgcChannelPKMicSpace)(nil), "pgc_channel_pk.PgcChannelPKMicSpace")
	proto.RegisterType((*PgcChannelPKBattle)(nil), "pgc_channel_pk.PgcChannelPKBattle")
	proto.RegisterType((*HeadIconSourceConf)(nil), "pgc_channel_pk.HeadIconSourceConf")
	proto.RegisterType((*GetPgcChannelPKInfoReq)(nil), "pgc_channel_pk.GetPgcChannelPKInfoReq")
	proto.RegisterType((*GetPgcChannelPKInfoResp)(nil), "pgc_channel_pk.GetPgcChannelPKInfoResp")
	proto.RegisterType((*PgcChannelPKReportClientIDChangeReq)(nil), "pgc_channel_pk.PgcChannelPKReportClientIDChangeReq")
	proto.RegisterType((*PgcChannelPKReportClientIDChangeResp)(nil), "pgc_channel_pk.PgcChannelPKReportClientIDChangeResp")
	proto.RegisterType((*SetPgcChannelPKOpponentMicFlagReq)(nil), "pgc_channel_pk.SetPgcChannelPKOpponentMicFlagReq")
	proto.RegisterType((*SetPgcChannelPKOpponentMicFlagResp)(nil), "pgc_channel_pk.SetPgcChannelPKOpponentMicFlagResp")
	proto.RegisterType((*PgcChannelPKOpponentMicFlagPushMsg)(nil), "pgc_channel_pk.PgcChannelPKOpponentMicFlagPushMsg")
	proto.RegisterType((*GetPgcChannelPKSendGiftScoreReq)(nil), "pgc_channel_pk.GetPgcChannelPKSendGiftScoreReq")
	proto.RegisterType((*GetPgcChannelPKSendGiftScoreResp)(nil), "pgc_channel_pk.GetPgcChannelPKSendGiftScoreResp")
	proto.RegisterType((*GetPgcChannelPKAudienceRankReq)(nil), "pgc_channel_pk.GetPgcChannelPKAudienceRankReq")
	proto.RegisterType((*PgcChannelPKAudienceInfo)(nil), "pgc_channel_pk.PgcChannelPKAudienceInfo")
	proto.RegisterType((*GetPgcChannelPKAudienceRankResp)(nil), "pgc_channel_pk.GetPgcChannelPKAudienceRankResp")
	proto.RegisterType((*TestSetPgcChannelPKStatusReq)(nil), "pgc_channel_pk.TestSetPgcChannelPKStatusReq")
	proto.RegisterType((*TestSetPgcChannelPKStatusResp)(nil), "pgc_channel_pk.TestSetPgcChannelPKStatusResp")
	proto.RegisterType((*GetPgcChannelPKIdReq)(nil), "pgc_channel_pk.GetPgcChannelPKIdReq")
	proto.RegisterType((*GetPgcChannelPKIdResp)(nil), "pgc_channel_pk.GetPgcChannelPKIdResp")
	proto.RegisterType((*TestPushMvpImMsgReq)(nil), "pgc_channel_pk.TestPushMvpImMsgReq")
	proto.RegisterType((*TestPushMvpImMsgResp)(nil), "pgc_channel_pk.TestPushMvpImMsgResp")
	proto.RegisterType((*ChoseInteractionReq)(nil), "pgc_channel_pk.ChoseInteractionReq")
	proto.RegisterType((*ChoseInteractionResp)(nil), "pgc_channel_pk.ChoseInteractionResp")
	proto.RegisterType((*SetPgcChannelPKEndReq)(nil), "pgc_channel_pk.SetPgcChannelPKEndReq")
	proto.RegisterType((*SetPgcChannelPKEndResp)(nil), "pgc_channel_pk.SetPgcChannelPKEndResp")
	proto.RegisterEnum("pgc_channel_pk.EUserPrivilegeType", EUserPrivilegeType_name, EUserPrivilegeType_value)
	proto.RegisterEnum("pgc_channel_pk.PgcChannelPKSwitchEnum", PgcChannelPKSwitchEnum_name, PgcChannelPKSwitchEnum_value)
	proto.RegisterEnum("pgc_channel_pk.PgcChannelPKOpponentMicFlag", PgcChannelPKOpponentMicFlag_name, PgcChannelPKOpponentMicFlag_value)
	proto.RegisterEnum("pgc_channel_pk.DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE", DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE_name, DownloadSourceInfo_DOWNLOAD_SOURCE_TYPE_value)
	proto.RegisterEnum("pgc_channel_pk.PgcChannelPKChannelInfo_Status", PgcChannelPKChannelInfo_Status_name, PgcChannelPKChannelInfo_Status_value)
	proto.RegisterEnum("pgc_channel_pk.PgcChannelPKBattle_Status", PgcChannelPKBattle_Status_name, PgcChannelPKBattle_Status_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PgcChannelPKClient is the client API for PgcChannelPK service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PgcChannelPKClient interface {
	// 获取PK入口
	GetPgcChannelPKEntry(ctx context.Context, in *GetPgcChannelPKEntryReq, opts ...grpc.CallOption) (*GetPgcChannelPKEntryResp, error)
	// 获取PK房间列表
	GetPgcChannelPKChannelList(ctx context.Context, in *GetPgcChannelPKChannelListReq, opts ...grpc.CallOption) (*GetPgcChannelPKChannelListResp, error)
	// 切换PK状态
	SetPgcChannelPKSwitch(ctx context.Context, in *SetPgcChannelPKSwitchReq, opts ...grpc.CallOption) (*SetPgcChannelPKSwitchResp, error)
	// 发起PK
	StartPgcChannelPK(ctx context.Context, in *StartPgcChannelPKReq, opts ...grpc.CallOption) (*StartPgcChannelPKResp, error)
	// 处理收到PK邀请
	AcceptPgcChannelPK(ctx context.Context, in *AcceptPgcChannelPKReq, opts ...grpc.CallOption) (*AcceptPgcChannelPKResp, error)
	// 获取房间PK信息
	GetPgcChannelPKInfo(ctx context.Context, in *GetPgcChannelPKInfoReq, opts ...grpc.CallOption) (*GetPgcChannelPKInfoResp, error)
	// 语音流变化上报
	PgcChannelPKReportClientIDChange(ctx context.Context, in *PgcChannelPKReportClientIDChangeReq, opts ...grpc.CallOption) (*PgcChannelPKReportClientIDChangeResp, error)
	// 闭麦
	SetPgcChannelPKOpponentMicFlag(ctx context.Context, in *SetPgcChannelPKOpponentMicFlagReq, opts ...grpc.CallOption) (*SetPgcChannelPKOpponentMicFlagResp, error)
	// 获取自己在当前PK中送给to_uid的送礼值
	GetPgcChannelPKSendGiftScore(ctx context.Context, in *GetPgcChannelPKSendGiftScoreReq, opts ...grpc.CallOption) (*GetPgcChannelPKSendGiftScoreResp, error)
	// 获取PK观众火力榜
	GetPgcChannelPKAudienceRank(ctx context.Context, in *GetPgcChannelPKAudienceRankReq, opts ...grpc.CallOption) (*GetPgcChannelPKAudienceRankResp, error)
	// 测试接口
	TestSetPgcChannelPKStatus(ctx context.Context, in *TestSetPgcChannelPKStatusReq, opts ...grpc.CallOption) (*TestSetPgcChannelPKStatusResp, error)
	// 获取房间PKID PKID>0 正在PK中
	GetPgcChannelPKId(ctx context.Context, in *GetPgcChannelPKIdReq, opts ...grpc.CallOption) (*GetPgcChannelPKIdResp, error)
	TestPushMvpImMsg(ctx context.Context, in *TestPushMvpImMsgReq, opts ...grpc.CallOption) (*TestPushMvpImMsgResp, error)
	// 选择互动玩家
	ChoseInteraction(ctx context.Context, in *ChoseInteractionReq, opts ...grpc.CallOption) (*ChoseInteractionResp, error)
	// 结束PK
	SetPgcChannelPKEnd(ctx context.Context, in *SetPgcChannelPKEndReq, opts ...grpc.CallOption) (*SetPgcChannelPKEndResp, error)
}

type pgcChannelPKClient struct {
	cc *grpc.ClientConn
}

func NewPgcChannelPKClient(cc *grpc.ClientConn) PgcChannelPKClient {
	return &pgcChannelPKClient{cc}
}

func (c *pgcChannelPKClient) GetPgcChannelPKEntry(ctx context.Context, in *GetPgcChannelPKEntryReq, opts ...grpc.CallOption) (*GetPgcChannelPKEntryResp, error) {
	out := new(GetPgcChannelPKEntryResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) GetPgcChannelPKChannelList(ctx context.Context, in *GetPgcChannelPKChannelListReq, opts ...grpc.CallOption) (*GetPgcChannelPKChannelListResp, error) {
	out := new(GetPgcChannelPKChannelListResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) SetPgcChannelPKSwitch(ctx context.Context, in *SetPgcChannelPKSwitchReq, opts ...grpc.CallOption) (*SetPgcChannelPKSwitchResp, error) {
	out := new(SetPgcChannelPKSwitchResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/SetPgcChannelPKSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) StartPgcChannelPK(ctx context.Context, in *StartPgcChannelPKReq, opts ...grpc.CallOption) (*StartPgcChannelPKResp, error) {
	out := new(StartPgcChannelPKResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/StartPgcChannelPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) AcceptPgcChannelPK(ctx context.Context, in *AcceptPgcChannelPKReq, opts ...grpc.CallOption) (*AcceptPgcChannelPKResp, error) {
	out := new(AcceptPgcChannelPKResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/AcceptPgcChannelPK", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) GetPgcChannelPKInfo(ctx context.Context, in *GetPgcChannelPKInfoReq, opts ...grpc.CallOption) (*GetPgcChannelPKInfoResp, error) {
	out := new(GetPgcChannelPKInfoResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) PgcChannelPKReportClientIDChange(ctx context.Context, in *PgcChannelPKReportClientIDChangeReq, opts ...grpc.CallOption) (*PgcChannelPKReportClientIDChangeResp, error) {
	out := new(PgcChannelPKReportClientIDChangeResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/PgcChannelPKReportClientIDChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) SetPgcChannelPKOpponentMicFlag(ctx context.Context, in *SetPgcChannelPKOpponentMicFlagReq, opts ...grpc.CallOption) (*SetPgcChannelPKOpponentMicFlagResp, error) {
	out := new(SetPgcChannelPKOpponentMicFlagResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/SetPgcChannelPKOpponentMicFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) GetPgcChannelPKSendGiftScore(ctx context.Context, in *GetPgcChannelPKSendGiftScoreReq, opts ...grpc.CallOption) (*GetPgcChannelPKSendGiftScoreResp, error) {
	out := new(GetPgcChannelPKSendGiftScoreResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKSendGiftScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) GetPgcChannelPKAudienceRank(ctx context.Context, in *GetPgcChannelPKAudienceRankReq, opts ...grpc.CallOption) (*GetPgcChannelPKAudienceRankResp, error) {
	out := new(GetPgcChannelPKAudienceRankResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKAudienceRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) TestSetPgcChannelPKStatus(ctx context.Context, in *TestSetPgcChannelPKStatusReq, opts ...grpc.CallOption) (*TestSetPgcChannelPKStatusResp, error) {
	out := new(TestSetPgcChannelPKStatusResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/TestSetPgcChannelPKStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) GetPgcChannelPKId(ctx context.Context, in *GetPgcChannelPKIdReq, opts ...grpc.CallOption) (*GetPgcChannelPKIdResp, error) {
	out := new(GetPgcChannelPKIdResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) TestPushMvpImMsg(ctx context.Context, in *TestPushMvpImMsgReq, opts ...grpc.CallOption) (*TestPushMvpImMsgResp, error) {
	out := new(TestPushMvpImMsgResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/TestPushMvpImMsg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) ChoseInteraction(ctx context.Context, in *ChoseInteractionReq, opts ...grpc.CallOption) (*ChoseInteractionResp, error) {
	out := new(ChoseInteractionResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/ChoseInteraction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pgcChannelPKClient) SetPgcChannelPKEnd(ctx context.Context, in *SetPgcChannelPKEndReq, opts ...grpc.CallOption) (*SetPgcChannelPKEndResp, error) {
	out := new(SetPgcChannelPKEndResp)
	err := c.cc.Invoke(ctx, "/pgc_channel_pk.PgcChannelPK/SetPgcChannelPKEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PgcChannelPKServer is the server API for PgcChannelPK service.
type PgcChannelPKServer interface {
	// 获取PK入口
	GetPgcChannelPKEntry(context.Context, *GetPgcChannelPKEntryReq) (*GetPgcChannelPKEntryResp, error)
	// 获取PK房间列表
	GetPgcChannelPKChannelList(context.Context, *GetPgcChannelPKChannelListReq) (*GetPgcChannelPKChannelListResp, error)
	// 切换PK状态
	SetPgcChannelPKSwitch(context.Context, *SetPgcChannelPKSwitchReq) (*SetPgcChannelPKSwitchResp, error)
	// 发起PK
	StartPgcChannelPK(context.Context, *StartPgcChannelPKReq) (*StartPgcChannelPKResp, error)
	// 处理收到PK邀请
	AcceptPgcChannelPK(context.Context, *AcceptPgcChannelPKReq) (*AcceptPgcChannelPKResp, error)
	// 获取房间PK信息
	GetPgcChannelPKInfo(context.Context, *GetPgcChannelPKInfoReq) (*GetPgcChannelPKInfoResp, error)
	// 语音流变化上报
	PgcChannelPKReportClientIDChange(context.Context, *PgcChannelPKReportClientIDChangeReq) (*PgcChannelPKReportClientIDChangeResp, error)
	// 闭麦
	SetPgcChannelPKOpponentMicFlag(context.Context, *SetPgcChannelPKOpponentMicFlagReq) (*SetPgcChannelPKOpponentMicFlagResp, error)
	// 获取自己在当前PK中送给to_uid的送礼值
	GetPgcChannelPKSendGiftScore(context.Context, *GetPgcChannelPKSendGiftScoreReq) (*GetPgcChannelPKSendGiftScoreResp, error)
	// 获取PK观众火力榜
	GetPgcChannelPKAudienceRank(context.Context, *GetPgcChannelPKAudienceRankReq) (*GetPgcChannelPKAudienceRankResp, error)
	// 测试接口
	TestSetPgcChannelPKStatus(context.Context, *TestSetPgcChannelPKStatusReq) (*TestSetPgcChannelPKStatusResp, error)
	// 获取房间PKID PKID>0 正在PK中
	GetPgcChannelPKId(context.Context, *GetPgcChannelPKIdReq) (*GetPgcChannelPKIdResp, error)
	TestPushMvpImMsg(context.Context, *TestPushMvpImMsgReq) (*TestPushMvpImMsgResp, error)
	// 选择互动玩家
	ChoseInteraction(context.Context, *ChoseInteractionReq) (*ChoseInteractionResp, error)
	// 结束PK
	SetPgcChannelPKEnd(context.Context, *SetPgcChannelPKEndReq) (*SetPgcChannelPKEndResp, error)
}

func RegisterPgcChannelPKServer(s *grpc.Server, srv PgcChannelPKServer) {
	s.RegisterService(&_PgcChannelPK_serviceDesc, srv)
}

func _PgcChannelPK_GetPgcChannelPKEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcChannelPKEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).GetPgcChannelPKEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).GetPgcChannelPKEntry(ctx, req.(*GetPgcChannelPKEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_GetPgcChannelPKChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcChannelPKChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).GetPgcChannelPKChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).GetPgcChannelPKChannelList(ctx, req.(*GetPgcChannelPKChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_SetPgcChannelPKSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPgcChannelPKSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).SetPgcChannelPKSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/SetPgcChannelPKSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).SetPgcChannelPKSwitch(ctx, req.(*SetPgcChannelPKSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_StartPgcChannelPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPgcChannelPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).StartPgcChannelPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/StartPgcChannelPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).StartPgcChannelPK(ctx, req.(*StartPgcChannelPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_AcceptPgcChannelPK_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AcceptPgcChannelPKReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).AcceptPgcChannelPK(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/AcceptPgcChannelPK",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).AcceptPgcChannelPK(ctx, req.(*AcceptPgcChannelPKReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_GetPgcChannelPKInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcChannelPKInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).GetPgcChannelPKInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).GetPgcChannelPKInfo(ctx, req.(*GetPgcChannelPKInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_PgcChannelPKReportClientIDChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PgcChannelPKReportClientIDChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).PgcChannelPKReportClientIDChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/PgcChannelPKReportClientIDChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).PgcChannelPKReportClientIDChange(ctx, req.(*PgcChannelPKReportClientIDChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_SetPgcChannelPKOpponentMicFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPgcChannelPKOpponentMicFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).SetPgcChannelPKOpponentMicFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/SetPgcChannelPKOpponentMicFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).SetPgcChannelPKOpponentMicFlag(ctx, req.(*SetPgcChannelPKOpponentMicFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_GetPgcChannelPKSendGiftScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcChannelPKSendGiftScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).GetPgcChannelPKSendGiftScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKSendGiftScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).GetPgcChannelPKSendGiftScore(ctx, req.(*GetPgcChannelPKSendGiftScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_GetPgcChannelPKAudienceRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcChannelPKAudienceRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).GetPgcChannelPKAudienceRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKAudienceRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).GetPgcChannelPKAudienceRank(ctx, req.(*GetPgcChannelPKAudienceRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_TestSetPgcChannelPKStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestSetPgcChannelPKStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).TestSetPgcChannelPKStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/TestSetPgcChannelPKStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).TestSetPgcChannelPKStatus(ctx, req.(*TestSetPgcChannelPKStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_GetPgcChannelPKId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPgcChannelPKIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).GetPgcChannelPKId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/GetPgcChannelPKId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).GetPgcChannelPKId(ctx, req.(*GetPgcChannelPKIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_TestPushMvpImMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestPushMvpImMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).TestPushMvpImMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/TestPushMvpImMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).TestPushMvpImMsg(ctx, req.(*TestPushMvpImMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_ChoseInteraction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChoseInteractionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).ChoseInteraction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/ChoseInteraction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).ChoseInteraction(ctx, req.(*ChoseInteractionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _PgcChannelPK_SetPgcChannelPKEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPgcChannelPKEndReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PgcChannelPKServer).SetPgcChannelPKEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pgc_channel_pk.PgcChannelPK/SetPgcChannelPKEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PgcChannelPKServer).SetPgcChannelPKEnd(ctx, req.(*SetPgcChannelPKEndReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _PgcChannelPK_serviceDesc = grpc.ServiceDesc{
	ServiceName: "pgc_channel_pk.PgcChannelPK",
	HandlerType: (*PgcChannelPKServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPgcChannelPKEntry",
			Handler:    _PgcChannelPK_GetPgcChannelPKEntry_Handler,
		},
		{
			MethodName: "GetPgcChannelPKChannelList",
			Handler:    _PgcChannelPK_GetPgcChannelPKChannelList_Handler,
		},
		{
			MethodName: "SetPgcChannelPKSwitch",
			Handler:    _PgcChannelPK_SetPgcChannelPKSwitch_Handler,
		},
		{
			MethodName: "StartPgcChannelPK",
			Handler:    _PgcChannelPK_StartPgcChannelPK_Handler,
		},
		{
			MethodName: "AcceptPgcChannelPK",
			Handler:    _PgcChannelPK_AcceptPgcChannelPK_Handler,
		},
		{
			MethodName: "GetPgcChannelPKInfo",
			Handler:    _PgcChannelPK_GetPgcChannelPKInfo_Handler,
		},
		{
			MethodName: "PgcChannelPKReportClientIDChange",
			Handler:    _PgcChannelPK_PgcChannelPKReportClientIDChange_Handler,
		},
		{
			MethodName: "SetPgcChannelPKOpponentMicFlag",
			Handler:    _PgcChannelPK_SetPgcChannelPKOpponentMicFlag_Handler,
		},
		{
			MethodName: "GetPgcChannelPKSendGiftScore",
			Handler:    _PgcChannelPK_GetPgcChannelPKSendGiftScore_Handler,
		},
		{
			MethodName: "GetPgcChannelPKAudienceRank",
			Handler:    _PgcChannelPK_GetPgcChannelPKAudienceRank_Handler,
		},
		{
			MethodName: "TestSetPgcChannelPKStatus",
			Handler:    _PgcChannelPK_TestSetPgcChannelPKStatus_Handler,
		},
		{
			MethodName: "GetPgcChannelPKId",
			Handler:    _PgcChannelPK_GetPgcChannelPKId_Handler,
		},
		{
			MethodName: "TestPushMvpImMsg",
			Handler:    _PgcChannelPK_TestPushMvpImMsg_Handler,
		},
		{
			MethodName: "ChoseInteraction",
			Handler:    _PgcChannelPK_ChoseInteraction_Handler,
		},
		{
			MethodName: "SetPgcChannelPKEnd",
			Handler:    _PgcChannelPK_SetPgcChannelPKEnd_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pgc-channel-pk/pgc-channel-pk.proto",
}

func init() {
	proto.RegisterFile("pgc-channel-pk/pgc-channel-pk.proto", fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0)
}

var fileDescriptor_pgc_channel_pk_4521f7f05af0c4b0 = []byte{
	// 2830 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x5a, 0x4f, 0x6f, 0xdb, 0xc8,
	0x15, 0x0f, 0x25, 0xcb, 0x92, 0x9e, 0x2c, 0x5b, 0x99, 0x38, 0x89, 0x2c, 0x6f, 0x36, 0x5e, 0x26,
	0x9b, 0x75, 0xd2, 0x8d, 0xb3, 0xf5, 0x36, 0x68, 0x8b, 0xa2, 0x2d, 0x1c, 0x5b, 0x76, 0xbc, 0xb2,
	0x65, 0x2d, 0x65, 0x27, 0xdd, 0x02, 0xed, 0x94, 0x22, 0xc7, 0xd2, 0x40, 0x14, 0x39, 0x21, 0x29,
	0xaf, 0xdd, 0xa2, 0xc0, 0x62, 0xf7, 0xd2, 0x5e, 0xda, 0x73, 0x7b, 0xe9, 0xb7, 0xe8, 0xa1, 0x68,
	0x4f, 0xfd, 0x03, 0x14, 0x28, 0xd0, 0x5b, 0x3f, 0x4f, 0x31, 0x33, 0xa4, 0x44, 0x91, 0xb4, 0x64,
	0x27, 0xb7, 0x99, 0xc7, 0x37, 0xef, 0xbd, 0x79, 0xf3, 0xfe, 0xfc, 0x66, 0x24, 0x78, 0xc0, 0xba,
	0xc6, 0x53, 0xa3, 0xa7, 0xdb, 0x36, 0xb1, 0x9e, 0xb2, 0xfe, 0xb3, 0xc9, 0xe9, 0x06, 0x73, 0x1d,
	0xdf, 0x41, 0x8b, 0xac, 0x6b, 0xe0, 0x80, 0x8a, 0x59, 0x5f, 0xfd, 0x67, 0x06, 0xd0, 0x8e, 0xf3,
	0xa5, 0x6d, 0x39, 0xba, 0xd9, 0x76, 0x86, 0xae, 0x41, 0xf6, 0xed, 0x53, 0x07, 0xdd, 0x87, 0x92,
	0x27, 0x66, 0xd8, 0xbf, 0x60, 0xa4, 0xaa, 0xac, 0x29, 0xeb, 0x65, 0x0d, 0x24, 0xe9, 0xf8, 0x82,
	0x11, 0xb4, 0x0a, 0xc5, 0x80, 0x81, 0x9a, 0xd5, 0x8c, 0xf8, 0x5c, 0x90, 0x84, 0x7d, 0x13, 0x55,
	0x20, 0x3b, 0x74, 0xad, 0x6a, 0x76, 0x4d, 0x59, 0x2f, 0x6a, 0x7c, 0xc8, 0x29, 0x03, 0xf3, 0x79,
	0x75, 0x4e, 0x52, 0x06, 0xe6, 0x73, 0xf5, 0x3f, 0x0a, 0x2c, 0xef, 0x1c, 0xbd, 0x6e, 0x1e, 0x1c,
	0x6d, 0xed, 0xe0, 0xf6, 0xd1, 0x89, 0xb6, 0x5d, 0xc7, 0xc7, 0x5f, 0xb4, 0xea, 0xa8, 0x9a, 0xa4,
	0x37, 0x8f, 0x9a, 0xf5, 0xca, 0x0d, 0xf4, 0x00, 0xee, 0xc7, 0xbf, 0xec, 0x6c, 0x1d, 0xef, 0x37,
	0xf7, 0xf0, 0xde, 0xd6, 0x61, 0x1d, 0xbf, 0xdc, 0x3a, 0xae, 0x28, 0x68, 0x15, 0xee, 0xc6, 0x99,
	0xea, 0xbb, 0xbb, 0xf5, 0xed, 0xe3, 0x76, 0x25, 0x83, 0x1e, 0xc3, 0x87, 0xf1, 0x8f, 0x2d, 0xad,
	0xde, 0xae, 0x37, 0x8f, 0xb1, 0x76, 0xd2, 0x7c, 0xbd, 0xf5, 0x05, 0xd6, 0x8e, 0xb6, 0x1b, 0xf5,
	0xe3, 0x4a, 0x16, 0x3d, 0x85, 0xc7, 0x09, 0xd6, 0xbd, 0x6d, 0xbc, 0xfd, 0x72, 0xab, 0xd9, 0xac,
	0x1f, 0xe0, 0x56, 0x03, 0x6b, 0xf5, 0xf6, 0xc9, 0xc1, 0x31, 0x3e, 0x7c, 0xd5, 0xaa, 0xcc, 0xa9,
	0xff, 0x56, 0xa0, 0x74, 0xe2, 0x11, 0xb7, 0xe5, 0x3a, 0xa7, 0xd4, 0x22, 0xc2, 0x05, 0xd4, 0x0c,
	0x1c, 0xc7, 0x87, 0xa8, 0x0a, 0x79, 0xdd, 0x30, 0x9c, 0xa1, 0xed, 0x0b, 0x7f, 0x15, 0xb5, 0x70,
	0x8a, 0x6a, 0x50, 0xb0, 0xa9, 0xd1, 0xb7, 0xf5, 0x01, 0x09, 0x7c, 0x36, 0x9a, 0xa3, 0x07, 0x50,
	0x0e, 0xd8, 0xb0, 0x6e, 0x51, 0xdd, 0x0b, 0x5c, 0xb8, 0x10, 0x10, 0xb7, 0x38, 0x8d, 0x2b, 0xf3,
	0xc8, 0x79, 0x35, 0x27, 0x95, 0x79, 0xe4, 0x1c, 0xfd, 0x00, 0x8a, 0xcc, 0xa5, 0x67, 0xd4, 0x22,
	0x5d, 0x52, 0x9d, 0x5f, 0x53, 0xd6, 0x4b, 0x9b, 0xf7, 0x36, 0x26, 0x8f, 0x7e, 0x43, 0x9a, 0x1b,
	0x30, 0x69, 0x63, 0x7e, 0xd5, 0x83, 0xf2, 0xc4, 0xb7, 0xa8, 0xe9, 0xca, 0xe5, 0xa6, 0x67, 0x62,
	0xa6, 0x23, 0x98, 0x13, 0xc1, 0x93, 0x15, 0x66, 0x89, 0x31, 0x97, 0xe4, 0x30, 0x9f, 0x3a, 0xb6,
	0xdc, 0xc8, 0x82, 0x16, 0x4e, 0xd5, 0x9f, 0x48, 0xff, 0x9d, 0x34, 0x5e, 0x8b, 0x00, 0x5c, 0x86,
	0x9c, 0x45, 0xce, 0x88, 0x15, 0x78, 0x50, 0x4e, 0x38, 0x75, 0x40, 0x4c, 0xdd, 0x0a, 0x74, 0xc9,
	0x09, 0xba, 0x07, 0xd0, 0x23, 0xba, 0x89, 0x4f, 0xdd, 0xb1, 0x07, 0x8b, 0x9c, 0xb2, 0xcb, 0x09,
	0x6a, 0x1f, 0x50, 0xab, 0x6b, 0x6c, 0xcb, 0x8d, 0xb7, 0x1a, 0x5b, 0xb6, 0xd1, 0x73, 0x5c, 0xf4,
	0x23, 0x58, 0x18, 0x7a, 0xc4, 0xc5, 0x4c, 0x1e, 0x98, 0xd0, 0x53, 0xda, 0x5c, 0x4d, 0x77, 0x92,
	0x60, 0xd1, 0x4a, 0xc3, 0xc8, 0x01, 0xdf, 0x86, 0x79, 0xea, 0xe1, 0xc1, 0x19, 0x13, 0xb6, 0x14,
	0xb4, 0x1c, 0xf5, 0x0e, 0xcf, 0x98, 0xfa, 0x77, 0x05, 0xee, 0x46, 0xb5, 0x1d, 0x52, 0x43, 0xd3,
	0xed, 0xbe, 0xd8, 0xd3, 0x0f, 0xa1, 0x30, 0xa0, 0x06, 0xe6, 0x52, 0x02, 0x75, 0x6a, 0x5c, 0x5d,
	0xd2, 0x50, 0x2d, 0x3f, 0xa0, 0x06, 0xb7, 0x02, 0xfd, 0x18, 0x8a, 0xa7, 0xae, 0x33, 0x90, 0xeb,
	0x33, 0x57, 0x5e, 0x5f, 0xe0, 0x8b, 0x84, 0x80, 0x65, 0xc8, 0x79, 0x86, 0xe3, 0x86, 0x27, 0x22,
	0x27, 0xfc, 0x98, 0x5c, 0xdd, 0xee, 0x8b, 0xf3, 0x28, 0x6b, 0x62, 0xac, 0xfe, 0x2f, 0x03, 0x95,
	0xa8, 0x28, 0x61, 0xfe, 0x67, 0xb0, 0x10, 0x6a, 0xa2, 0xf6, 0xa9, 0x13, 0x6c, 0xe1, 0xa3, 0x69,
	0x26, 0x04, 0x03, 0xbe, 0x5c, 0x2b, 0x19, 0xe3, 0xc9, 0xd8, 0x94, 0x4c, 0xd4, 0x94, 0xcf, 0x60,
	0xc9, 0x77, 0x18, 0xd6, 0x85, 0xe1, 0xd8, 0xa2, 0x9e, 0x5f, 0xcd, 0xae, 0x65, 0xaf, 0xb8, 0xcf,
	0xb2, 0xef, 0x30, 0x39, 0x3c, 0xa0, 0x9e, 0x8f, 0x5e, 0x40, 0x61, 0x70, 0xc6, 0xa4, 0xa5, 0x73,
	0x42, 0xc8, 0x54, 0x4b, 0x23, 0xe7, 0xa4, 0xe5, 0x07, 0x67, 0x4c, 0x58, 0xf9, 0x04, 0x6e, 0x86,
	0xec, 0x86, 0x45, 0x89, 0xed, 0xf3, 0x62, 0x97, 0x13, 0xf1, 0xb5, 0x14, 0x7c, 0xd8, 0x16, 0xf4,
	0x7d, 0x13, 0x3d, 0x84, 0xc5, 0x01, 0xb5, 0xb1, 0xd8, 0x08, 0xf6, 0xc9, 0xb9, 0x2f, 0xd2, 0xae,
	0xa8, 0x2d, 0x0c, 0xa8, 0xdd, 0xe6, 0xc4, 0x63, 0x72, 0xee, 0xab, 0xff, 0xca, 0xc0, 0x4a, 0x54,
	0xed, 0xe7, 0x43, 0x6a, 0xf4, 0x1b, 0xd4, 0x92, 0x5e, 0xf9, 0x14, 0xee, 0xbc, 0xe1, 0x04, 0xdc,
	0xa7, 0x96, 0x85, 0x4d, 0xe2, 0x19, 0x98, 0xb9, 0xe4, 0x94, 0x9e, 0x07, 0x69, 0x77, 0xeb, 0x4d,
	0xc8, 0xbe, 0x43, 0x3c, 0xa3, 0x25, 0x3e, 0xa1, 0x47, 0xb0, 0x14, 0x5b, 0x14, 0x64, 0x47, 0x79,
	0x82, 0x1b, 0x3d, 0x86, 0x9b, 0x11, 0x3e, 0x62, 0x9b, 0xd8, 0xf7, 0x82, 0x48, 0x58, 0x1c, 0x71,
	0xd6, 0x6d, 0xf3, 0xd8, 0xe3, 0xac, 0xc4, 0xd6, 0x3b, 0x16, 0xc1, 0x7c, 0x4b, 0xac, 0x8f, 0x3d,
	0x62, 0x04, 0xf1, 0xb1, 0x28, 0x3f, 0x1c, 0x52, 0xbb, 0xd5, 0x6f, 0x13, 0x23, 0xca, 0xaa, 0x9f,
	0x87, 0xac, 0xb9, 0x09, 0x56, 0xfd, 0x5c, 0xb2, 0x7e, 0x04, 0x4b, 0x86, 0x63, 0x9b, 0x94, 0xe7,
	0x3b, 0x3e, 0xd3, 0xad, 0xa1, 0xac, 0x4c, 0x65, 0x6d, 0x71, 0x44, 0x7e, 0xc5, 0xa9, 0x48, 0x85,
	0x32, 0xb5, 0xf1, 0xd8, 0xd8, 0x6a, 0x5e, 0x64, 0x58, 0x89, 0xda, 0x23, 0x77, 0xa9, 0x9f, 0xc3,
	0x72, 0xd4, 0x8f, 0x2d, 0xa2, 0xcb, 0x1c, 0x5b, 0x85, 0x22, 0x23, 0x7a, 0x5f, 0xfa, 0x41, 0x7a,
	0xad, 0xc0, 0x09, 0xc2, 0x05, 0xef, 0x01, 0xf0, 0xfd, 0xf0, 0xef, 0xac, 0x1f, 0xe4, 0x6d, 0x81,
	0xda, 0x7c, 0x71, 0xab, 0xaf, 0x7e, 0x0f, 0xee, 0xee, 0x11, 0x3f, 0x2a, 0xb5, 0x6e, 0xfb, 0xee,
	0x85, 0x46, 0xde, 0xf0, 0x0a, 0x33, 0x0a, 0xfd, 0xb0, 0xa8, 0x17, 0xc3, 0x78, 0x36, 0xd5, 0x57,
	0x50, 0x4d, 0x5f, 0xe9, 0xb1, 0x19, 0x4b, 0xb9, 0xbd, 0x3d, 0xdd, 0xc3, 0x84, 0xf3, 0x87, 0x16,
	0xf5, 0x74, 0x4f, 0xac, 0x57, 0xbf, 0x80, 0x7b, 0x31, 0xb9, 0xc1, 0x80, 0x47, 0xf8, 0x6c, 0xbb,
	0xd0, 0x0a, 0x14, 0xba, 0x43, 0x6a, 0x99, 0xe3, 0x1e, 0x9d, 0x17, 0xf3, 0x7d, 0x53, 0xfd, 0x7a,
	0x7e, 0xb2, 0x4e, 0x45, 0x32, 0x75, 0x96, 0xd4, 0x0f, 0xc6, 0x75, 0x40, 0xd4, 0x77, 0x29, 0x39,
	0x4c, 0x6f, 0x81, 0x0e, 0x22, 0x2c, 0xd4, 0x70, 0xec, 0xa0, 0x26, 0x8f, 0x2a, 0x80, 0xe1, 0xd8,
	0x51, 0x16, 0xd1, 0x3d, 0xe6, 0x26, 0x58, 0x9a, 0xbc, 0x81, 0xa8, 0x50, 0xee, 0x50, 0xdb, 0xc4,
	0xa3, 0x3d, 0xc8, 0xb8, 0x2a, 0x71, 0xe2, 0x9e, 0xdc, 0x07, 0x07, 0x2a, 0x86, 0x4b, 0x74, 0xdf,
	0x71, 0x31, 0xef, 0xb7, 0x32, 0xa0, 0x20, 0x20, 0x9d, 0x50, 0x13, 0x35, 0xe0, 0x56, 0x50, 0x4f,
	0x26, 0xca, 0x7d, 0x7e, 0x76, 0xb9, 0xbf, 0x29, 0xd7, 0x45, 0xbb, 0xfa, 0x0a, 0x14, 0x7c, 0xbd,
	0x23, 0x0d, 0x2e, 0xc8, 0x4e, 0xe8, 0xeb, 0x1d, 0x61, 0x6c, 0x05, 0xb2, 0x3d, 0xc7, 0xaf, 0x16,
	0x65, 0x0f, 0xee, 0x39, 0x3e, 0x6a, 0x40, 0x91, 0xe7, 0x83, 0xaf, 0xfb, 0x43, 0xaf, 0x0a, 0x6b,
	0xca, 0xfa, 0xe2, 0xe6, 0xc6, 0x15, 0x8b, 0xe5, 0x46, 0x5b, 0xac, 0xd2, 0x0a, 0xac, 0x2f, 0x47,
	0x68, 0x57, 0xf6, 0x8e, 0x53, 0x4b, 0xef, 0x56, 0x4b, 0x42, 0xd6, 0xb7, 0xa6, 0xc9, 0x3a, 0x62,
	0xcc, 0xb1, 0x89, 0xed, 0x1f, 0x52, 0x63, 0xd7, 0xd2, 0xbb, 0xa2, 0x89, 0xf0, 0x01, 0x8f, 0x37,
	0xbe, 0x03, 0xc3, 0xb1, 0x1c, 0xb7, 0xba, 0xb0, 0x96, 0xe5, 0xf9, 0xe1, 0xeb, 0x9d, 0x6d, 0x3e,
	0x47, 0x3f, 0x83, 0x65, 0x6a, 0xfb, 0xc4, 0xd5, 0x0d, 0x91, 0xa3, 0x23, 0x85, 0xe5, 0xeb, 0x2b,
	0x44, 0x11, 0x41, 0x01, 0x0d, 0x1d, 0x41, 0x25, 0x2a, 0x5e, 0xf4, 0xb1, 0x45, 0x71, 0x0e, 0x0f,
	0x67, 0x94, 0xe6, 0x36, 0xd3, 0x0d, 0xa2, 0x2d, 0x45, 0x56, 0x8b, 0x86, 0x76, 0x1f, 0x4a, 0x2e,
	0xf1, 0x86, 0x96, 0x2f, 0x03, 0x71, 0x49, 0x1e, 0xbe, 0x24, 0xf1, 0x38, 0x54, 0xef, 0xc1, 0x7c,
	0xe0, 0xbf, 0x22, 0xe4, 0x9a, 0x8e, 0xdf, 0x6a, 0x54, 0x6e, 0xa0, 0x02, 0xcc, 0xed, 0xdb, 0xad,
	0x46, 0x45, 0x51, 0xbf, 0xce, 0xc0, 0xfb, 0xd3, 0x12, 0x6c, 0x76, 0xfa, 0x5e, 0x9e, 0x61, 0xd1,
	0x76, 0x19, 0xe9, 0x64, 0xd7, 0x6e, 0x97, 0xa2, 0x99, 0xad, 0xca, 0x50, 0xfa, 0x92, 0xfa, 0x46,
	0x2f, 0x28, 0xc4, 0x3c, 0x34, 0xc4, 0x9c, 0xa7, 0x09, 0xeb, 0x63, 0x8b, 0x0e, 0xa8, 0x2f, 0x1b,
	0x8f, 0xec, 0x50, 0x25, 0xd6, 0x3f, 0xe0, 0x34, 0xde, 0x77, 0xb8, 0x9d, 0x92, 0x81, 0xf5, 0x45,
	0x8e, 0x14, 0xb4, 0xbc, 0x98, 0xb7, 0xfa, 0xbc, 0x78, 0xb5, 0x27, 0x7d, 0x20, 0xe5, 0x5e, 0xa1,
	0xbe, 0x4c, 0x98, 0x95, 0x99, 0x34, 0x4b, 0x7d, 0x0d, 0x2b, 0x97, 0xc8, 0xbd, 0x52, 0x55, 0xbc,
	0x5c, 0xf0, 0xcf, 0x61, 0xb9, 0xed, 0xeb, 0xee, 0x84, 0x68, 0x6e, 0xac, 0x0a, 0xe5, 0xc1, 0x05,
	0x4e, 0x88, 0x2d, 0x0d, 0x2e, 0xb6, 0x47, 0x82, 0x55, 0x28, 0xfb, 0x4e, 0x94, 0x27, 0x28, 0x5e,
	0xbe, 0x33, 0xe2, 0x51, 0xef, 0xc2, 0xed, 0x14, 0xf9, 0x1e, 0x53, 0x7f, 0x05, 0xb7, 0xb7, 0x0c,
	0x83, 0xb0, 0xb7, 0xd2, 0xfc, 0x08, 0x96, 0x04, 0x7a, 0x4b, 0xe8, 0x2e, 0x73, 0xf2, 0x98, 0xef,
	0x0e, 0xcc, 0xeb, 0x42, 0x89, 0x28, 0x9a, 0x05, 0x2d, 0x98, 0xa9, 0x55, 0xb8, 0x93, 0xa6, 0xdc,
	0x63, 0xea, 0x1f, 0x94, 0xc9, 0x5e, 0x18, 0xe6, 0x0b, 0x87, 0xa8, 0x3c, 0x85, 0x47, 0xf6, 0xe4,
	0x06, 0xd4, 0xd8, 0x37, 0x13, 0xc8, 0x37, 0x73, 0x4d, 0xe4, 0x9b, 0x8a, 0x8a, 0xb2, 0xa9, 0xa8,
	0x48, 0xfd, 0x5d, 0x76, 0x12, 0x7c, 0xbf, 0xd0, 0x7d, 0xdf, 0x22, 0xe8, 0xfb, 0x90, 0x17, 0x21,
	0xeb, 0xf1, 0x0b, 0x05, 0x4f, 0x8b, 0xb5, 0x69, 0x69, 0x21, 0xf2, 0x61, 0x9e, 0x87, 0xb3, 0xc7,
	0x6f, 0x1c, 0x3c, 0x34, 0x02, 0xf8, 0x12, 0xa4, 0x1c, 0xeb, 0x4b, 0xdc, 0xc2, 0x2f, 0xa5, 0xc4,
	0x3d, 0x23, 0x2e, 0xb6, 0x25, 0xb4, 0xc9, 0x6a, 0x05, 0x49, 0x68, 0x7a, 0xfc, 0x90, 0xce, 0x74,
	0x8b, 0x9a, 0x1c, 0xa4, 0x08, 0x74, 0x10, 0x74, 0x1c, 0x41, 0x6c, 0x49, 0x80, 0xb0, 0x02, 0x85,
	0x90, 0x47, 0x64, 0x51, 0x41, 0xcb, 0x07, 0x9f, 0x39, 0x62, 0x65, 0xae, 0xee, 0x85, 0x98, 0x45,
	0x4e, 0xd0, 0x2d, 0xc8, 0xb1, 0x3e, 0xdf, 0x7f, 0x5e, 0xa2, 0x67, 0xd6, 0x97, 0x41, 0x26, 0xab,
	0x7e, 0x68, 0x66, 0x41, 0x86, 0x83, 0x24, 0x4a, 0x53, 0xf9, 0x05, 0x5b, 0xf2, 0x08, 0x5b, 0x8a,
	0xc2, 0x16, 0x90, 0x24, 0x6e, 0x8a, 0x7a, 0x92, 0x56, 0xba, 0x16, 0xa0, 0xb0, 0x6f, 0x9f, 0x51,
	0x9f, 0xf0, 0xf2, 0x85, 0x4a, 0x90, 0xe7, 0x85, 0x8c, 0xda, 0xdd, 0x4a, 0x06, 0x2d, 0x43, 0x65,
	0xbb, 0xe7, 0x78, 0x64, 0x7f, 0x5c, 0x23, 0x2b, 0x39, 0xb4, 0x04, 0xa5, 0x28, 0x61, 0x5e, 0xa5,
	0x80, 0x5e, 0x12, 0xdd, 0xe4, 0x2d, 0x58, 0x5e, 0xf7, 0xb7, 0x1d, 0xfb, 0x94, 0x3b, 0x6e, 0x04,
	0x5e, 0x83, 0x60, 0x29, 0x84, 0xb8, 0x55, 0x7c, 0xd4, 0xcf, 0x71, 0x14, 0xaf, 0x17, 0x06, 0xfa,
	0xb9, 0xfc, 0xb8, 0x02, 0x05, 0x46, 0x8d, 0x71, 0x85, 0x2b, 0x6a, 0x79, 0x46, 0x0d, 0x7e, 0x52,
	0xea, 0x77, 0xe1, 0x4e, 0xac, 0xb8, 0x8a, 0x83, 0x9c, 0x0d, 0xa7, 0xfe, 0x92, 0x4d, 0x20, 0x31,
	0xb9, 0xd2, 0x63, 0xe8, 0x25, 0x2c, 0xb2, 0x3e, 0xee, 0x88, 0x30, 0x8a, 0x5e, 0x43, 0xa6, 0xde,
	0x10, 0x64, 0xd4, 0x69, 0x0b, 0xac, 0x2f, 0x47, 0x02, 0xe5, 0x7c, 0x3e, 0x81, 0x9b, 0x85, 0x28,
	0x99, 0x09, 0x8f, 0xa7, 0x89, 0x9a, 0x00, 0xec, 0x11, 0x88, 0x2d, 0x44, 0x6e, 0x05, 0xe0, 0x53,
	0x08, 0xcb, 0xce, 0xee, 0x6c, 0x21, 0x6a, 0x95, 0x10, 0x55, 0x88, 0x68, 0x40, 0xd9, 0xd2, 0x3d,
	0x1f, 0xbf, 0xed, 0xdd, 0xa5, 0xc4, 0x57, 0x1f, 0x06, 0xf7, 0x97, 0x75, 0xa8, 0x8c, 0x84, 0x91,
	0x73, 0x46, 0x5d, 0x12, 0x62, 0xa8, 0xc5, 0x80, 0xad, 0x2e, 0xa9, 0xdc, 0xad, 0x1c, 0xa8, 0x61,
	0xc3, 0xb1, 0x4f, 0xe5, 0x61, 0xce, 0xa7, 0x5f, 0xbc, 0x92, 0xc1, 0xa3, 0x2d, 0xf0, 0x95, 0x7c,
	0x24, 0x4e, 0xfd, 0x4f, 0x0a, 0x3c, 0x98, 0x2c, 0x51, 0xcc, 0x71, 0xfd, 0xa0, 0x20, 0xec, 0x70,
	0x72, 0x97, 0xf0, 0x18, 0x48, 0x3e, 0x90, 0x4c, 0x46, 0x45, 0x26, 0xde, 0x13, 0xc6, 0xd5, 0x2c,
	0x1b, 0xad, 0x66, 0xa3, 0x0c, 0x9c, 0x8b, 0x64, 0xe0, 0x2a, 0x14, 0xe3, 0x17, 0xb6, 0x82, 0x11,
	0xd6, 0xa4, 0x47, 0xf0, 0x70, 0xb6, 0x81, 0x1e, 0x53, 0xff, 0xa6, 0xc0, 0x07, 0xb1, 0x0e, 0x16,
	0x47, 0x39, 0xb3, 0x5b, 0xe4, 0xc8, 0xbc, 0x4c, 0xc4, 0xbc, 0x43, 0x58, 0x70, 0x98, 0x3f, 0xc6,
	0x57, 0xd9, 0xeb, 0xe3, 0x2b, 0x70, 0x58, 0x38, 0x8e, 0x78, 0x66, 0x2e, 0xe2, 0x19, 0xf5, 0x21,
	0xa8, 0xb3, 0xcc, 0xf7, 0x98, 0xfa, 0x0f, 0x05, 0xd4, 0x29, 0x3c, 0xad, 0xa1, 0xd7, 0x3b, 0xf4,
	0xba, 0x09, 0x93, 0x95, 0x77, 0x33, 0x79, 0xc6, 0x59, 0x7f, 0x02, 0xb7, 0x3b, 0x04, 0x77, 0x74,
	0x89, 0x41, 0x23, 0x9c, 0xf2, 0xe8, 0x6f, 0x76, 0xc8, 0x0b, 0x9d, 0xc3, 0xca, 0x71, 0xd3, 0xfe,
	0x46, 0x81, 0xfb, 0xb1, 0x9a, 0xd1, 0x26, 0xb6, 0xb9, 0x47, 0x4f, 0x7d, 0x51, 0xa8, 0xde, 0xf6,
	0xa8, 0xb8, 0x6f, 0x2f, 0xc4, 0xd5, 0x22, 0x8c, 0xba, 0x0b, 0x7e, 0xab, 0xb8, 0x0d, 0xf3, 0xbe,
	0x23, 0xc8, 0x81, 0xcb, 0x7d, 0xe7, 0x84, 0x9a, 0xea, 0x1f, 0x15, 0x58, 0x9b, 0x6e, 0xc5, 0x6c,
	0xec, 0xf3, 0xee, 0x66, 0x8c, 0x5f, 0x57, 0x72, 0x91, 0xd7, 0x15, 0xf5, 0x2b, 0x25, 0x81, 0x76,
	0xb7, 0x86, 0x26, 0x25, 0xb6, 0x41, 0x78, 0xf9, 0x78, 0x5b, 0x0f, 0x2d, 0x43, 0xae, 0x43, 0xba,
	0xd4, 0x0e, 0x2d, 0x13, 0x13, 0xf1, 0x7e, 0xc7, 0x01, 0x66, 0x68, 0x98, 0x98, 0xa8, 0x7f, 0xce,
	0x40, 0x35, 0x4d, 0xbf, 0xa8, 0x56, 0xef, 0xfa, 0x22, 0x97, 0xfe, 0xa6, 0x84, 0x60, 0xce, 0xa5,
	0x46, 0x2f, 0x7c, 0x85, 0xe4, 0x63, 0xce, 0x69, 0xf4, 0x74, 0x77, 0x10, 0x1a, 0x27, 0x26, 0xe8,
	0x43, 0x58, 0xb4, 0x9d, 0x0e, 0xb5, 0xa8, 0x7f, 0x81, 0xe5, 0xdb, 0xa3, 0x74, 0x5f, 0x39, 0xa4,
	0x1e, 0x88, 0x37, 0xc8, 0x75, 0xa8, 0x74, 0x5d, 0x67, 0xc8, 0xf0, 0xa9, 0x6e, 0x7b, 0x01, 0x63,
	0xf0, 0x8e, 0x21, 0xe8, 0xbb, 0xba, 0xed, 0x49, 0xce, 0x08, 0x50, 0x1a, 0x90, 0x41, 0xc0, 0x2a,
	0x81, 0x42, 0x08, 0x94, 0x0e, 0xc9, 0x40, 0xf2, 0xde, 0x03, 0x90, 0x52, 0x23, 0x77, 0xcb, 0xa2,
	0xa0, 0xf0, 0xdb, 0xa5, 0xda, 0x4b, 0x44, 0xf7, 0xe4, 0xd1, 0x79, 0x0c, 0xd5, 0xa1, 0xe8, 0xea,
	0xf6, 0x04, 0xaa, 0x5a, 0x9f, 0xfa, 0x6c, 0x16, 0xf1, 0xbd, 0x56, 0xe0, 0x4b, 0x45, 0xfd, 0xfe,
	0xab, 0x02, 0xef, 0x1d, 0x13, 0xcf, 0x8f, 0x63, 0x77, 0x79, 0x21, 0x25, 0x6f, 0xd2, 0x80, 0xac,
	0x92, 0x06, 0x64, 0xaf, 0x00, 0xb5, 0x39, 0xd8, 0x0d, 0xee, 0xc7, 0xf2, 0x78, 0x82, 0x19, 0xf7,
	0x86, 0xd0, 0x21, 0xcf, 0x53, 0x9e, 0x92, 0x78, 0xfc, 0x1c, 0x81, 0x0e, 0xdf, 0xc1, 0xd1, 0x10,
	0xcf, 0xfb, 0x8e, 0xf8, 0xa4, 0xde, 0x87, 0x7b, 0x53, 0xac, 0xf7, 0x98, 0xfa, 0x1c, 0x96, 0xe3,
	0xd8, 0xc2, 0xbc, 0x02, 0x26, 0x69, 0xc0, 0xed, 0x94, 0x65, 0x6f, 0x97, 0xcd, 0xea, 0x6f, 0x14,
	0xb8, 0xc5, 0xad, 0x14, 0xc5, 0xf5, 0x8c, 0xed, 0x0f, 0x0e, 0x3d, 0xd1, 0x4b, 0x6e, 0x41, 0x6e,
	0x48, 0x4d, 0xac, 0x07, 0x62, 0xe6, 0x86, 0xd4, 0xdc, 0x0a, 0x89, 0x9d, 0x50, 0xc2, 0x90, 0x9a,
	0x2f, 0x62, 0x5a, 0xb3, 0x71, 0xad, 0xa3, 0x54, 0x98, 0x4b, 0x7b, 0xe9, 0xcd, 0x45, 0x5e, 0x7a,
	0xef, 0xc0, 0x72, 0xd2, 0x12, 0x8f, 0xa9, 0xbf, 0x80, 0x5b, 0x71, 0x38, 0xf9, 0x2e, 0x25, 0x34,
	0xd9, 0xb8, 0xd5, 0x8f, 0x61, 0x39, 0xa9, 0xc1, 0x63, 0xdc, 0x76, 0xdf, 0xd1, 0xbd, 0xf0, 0xa7,
	0x06, 0x39, 0xe1, 0xfe, 0x6f, 0xc7, 0x9f, 0xd8, 0xcc, 0xb7, 0xb4, 0x88, 0xdf, 0xa5, 0xd2, 0x84,
	0x79, 0xec, 0x89, 0x06, 0xa8, 0x3e, 0xf1, 0xdb, 0x87, 0x78, 0xce, 0xba, 0x0f, 0xab, 0xf5, 0xe6,
	0xc9, 0x21, 0x3e, 0x69, 0xd7, 0x35, 0xdc, 0xd2, 0xf6, 0x5f, 0xed, 0x1f, 0xd4, 0xf7, 0xea, 0xf8,
	0xa4, 0xd9, 0x68, 0x1e, 0xbd, 0x6e, 0x56, 0x6e, 0xa0, 0x55, 0xb8, 0x9b, 0xca, 0xd0, 0x78, 0x5d,
	0x51, 0x9e, 0x3c, 0x86, 0x3b, 0xc9, 0x5b, 0x70, 0xdd, 0x1e, 0x0e, 0xd0, 0x3c, 0x64, 0x8e, 0xf8,
	0xf2, 0x3c, 0x64, 0x8f, 0x76, 0x77, 0x2b, 0xca, 0x93, 0x1d, 0x58, 0x9d, 0xd2, 0x41, 0xd1, 0x4d,
	0x28, 0xb7, 0xba, 0x86, 0x00, 0x7c, 0xf8, 0xa8, 0x55, 0xe7, 0x4b, 0x11, 0x2c, 0x8e, 0x48, 0xdb,
	0x07, 0x47, 0xed, 0x7a, 0x45, 0xd9, 0xfc, 0x6f, 0x19, 0x16, 0xa2, 0x62, 0x50, 0x3f, 0x11, 0xf3,
	0xe2, 0x7d, 0x11, 0x25, 0x50, 0xe5, 0x25, 0xef, 0x9f, 0xb5, 0xf5, 0xab, 0x31, 0x7a, 0x4c, 0xbd,
	0x81, 0x7e, 0x0d, 0xb5, 0xcb, 0xdf, 0x54, 0xd0, 0xd3, 0x19, 0x92, 0x26, 0x1f, 0x38, 0x6b, 0x1b,
	0xd7, 0x61, 0x17, 0xea, 0xed, 0x44, 0xa0, 0x04, 0xcf, 0x24, 0x89, 0x3d, 0x5c, 0xf6, 0xea, 0x51,
	0x7b, 0x7c, 0x45, 0x4e, 0xa1, 0xaf, 0x03, 0x37, 0x13, 0xaf, 0x05, 0x28, 0x81, 0xfa, 0xd3, 0x1e,
	0x2c, 0x6a, 0x1f, 0x5e, 0x81, 0x4b, 0xe8, 0x20, 0x80, 0x92, 0x77, 0x7f, 0x94, 0x58, 0x9e, 0xfa,
	0x38, 0x51, 0x7b, 0x74, 0x15, 0x36, 0xa1, 0xa6, 0x07, 0xb7, 0x52, 0xae, 0x5d, 0xe8, 0xd1, 0x8c,
	0x33, 0x08, 0x6e, 0x75, 0xb5, 0x8f, 0xae, 0xc4, 0x27, 0x34, 0xfd, 0x5e, 0x81, 0xb5, 0x59, 0x18,
	0x1c, 0x7d, 0x3a, 0xad, 0x7b, 0x5d, 0x72, 0xad, 0xa8, 0x7d, 0xe7, 0xfa, 0x8b, 0x84, 0x45, 0xbf,
	0x55, 0xe0, 0xfd, 0xe9, 0x68, 0x19, 0x7d, 0x7b, 0x46, 0x58, 0x24, 0x2f, 0x07, 0xb5, 0xcd, 0xeb,
	0x2e, 0x11, 0xb6, 0x7c, 0xa3, 0xc0, 0x7b, 0xd3, 0x50, 0x24, 0x7a, 0x36, 0xc3, 0xd3, 0x71, 0xe4,
	0x5b, 0xfb, 0xe4, 0x7a, 0x0b, 0x84, 0x15, 0x5f, 0x29, 0xb0, 0x3a, 0x05, 0x73, 0xa0, 0x59, 0xa9,
	0x19, 0xc3, 0x96, 0xb5, 0x67, 0xd7, 0xe2, 0x17, 0x26, 0xfc, 0x12, 0x56, 0x2e, 0x6d, 0xe6, 0xe8,
	0xe3, 0xb8, 0xbc, 0x69, 0xa8, 0xa5, 0xf6, 0xf4, 0x1a, 0xdc, 0x61, 0x5e, 0x27, 0x1a, 0x7e, 0x32,
	0xaf, 0xd3, 0xa0, 0x44, 0x32, 0xaf, 0x53, 0x91, 0x83, 0x7a, 0x03, 0x61, 0xa8, 0xc4, 0x9b, 0x2f,
	0x7a, 0x90, 0x66, 0x68, 0x0c, 0x28, 0xd4, 0x1e, 0xce, 0x66, 0x0a, 0x15, 0xc4, 0x7b, 0x6c, 0x52,
	0x41, 0x4a, 0x9f, 0x4f, 0x2a, 0x48, 0x6b, 0xd5, 0xb2, 0x32, 0x25, 0x3b, 0x69, 0xb2, 0x32, 0xa5,
	0xb6, 0xee, 0x64, 0x65, 0x4a, 0x6f, 0xca, 0xea, 0x8d, 0x17, 0x9b, 0x3f, 0xfd, 0xa4, 0xeb, 0x58,
	0xba, 0xdd, 0xdd, 0x78, 0xbe, 0xe9, 0xfb, 0x1b, 0x86, 0x33, 0x78, 0x26, 0xfe, 0xce, 0x62, 0x38,
	0xd6, 0x33, 0x8f, 0xb8, 0x67, 0xd4, 0x20, 0x5e, 0xec, 0xff, 0x2e, 0x9d, 0x79, 0xc1, 0xf1, 0xe9,
	0xff, 0x03, 0x00, 0x00, 0xff, 0xff, 0x54, 0x3d, 0x06, 0x0d, 0x17, 0x23, 0x00, 0x00,
}
