// Code generated by protoc-gen-go. DO NOT EDIT.
// source: muse-social-community-achieve/muse-social-community-achieve.proto

package muse_social_community_achieve // import "golang.52tt.com/protocol/services/muse-social-community-achieve"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BrandMemberRole int32

const (
	BrandMemberRole_Brand_Role_None         BrandMemberRole = 0
	BrandMemberRole_Brand_Role_Captain      BrandMemberRole = 1
	BrandMemberRole_Brand_Role_Kernel       BrandMemberRole = 2
	BrandMemberRole_Brand_Role_Vice_Captain BrandMemberRole = 3
	BrandMemberRole_Brand_Producer          BrandMemberRole = 4
	BrandMemberRole_Brand_Fans              BrandMemberRole = 5
)

var BrandMemberRole_name = map[int32]string{
	0: "Brand_Role_None",
	1: "Brand_Role_Captain",
	2: "Brand_Role_Kernel",
	3: "Brand_Role_Vice_Captain",
	4: "Brand_Producer",
	5: "Brand_Fans",
}
var BrandMemberRole_value = map[string]int32{
	"Brand_Role_None":         0,
	"Brand_Role_Captain":      1,
	"Brand_Role_Kernel":       2,
	"Brand_Role_Vice_Captain": 3,
	"Brand_Producer":          4,
	"Brand_Fans":              5,
}

func (x BrandMemberRole) String() string {
	return proto.EnumName(BrandMemberRole_name, int32(x))
}
func (BrandMemberRole) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{0}
}

type SocialCommunityRightStatus int32

const (
	SocialCommunityRightStatus_Status_Lock  SocialCommunityRightStatus = 0
	SocialCommunityRightStatus_Status_Valid SocialCommunityRightStatus = 1
)

var SocialCommunityRightStatus_name = map[int32]string{
	0: "Status_Lock",
	1: "Status_Valid",
}
var SocialCommunityRightStatus_value = map[string]int32{
	"Status_Lock":  0,
	"Status_Valid": 1,
}

func (x SocialCommunityRightStatus) String() string {
	return proto.EnumName(SocialCommunityRightStatus_name, int32(x))
}
func (SocialCommunityRightStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{1}
}

type TaskViewType int32

const (
	TaskViewType_TASK_VIEW_TYPE_NORMAL   TaskViewType = 0
	TaskViewType_TASK_VIEW_TYPE_CHECK_IN TaskViewType = 1
	TaskViewType_TASK_VIEW_TYPE_STEP     TaskViewType = 2
)

var TaskViewType_name = map[int32]string{
	0: "TASK_VIEW_TYPE_NORMAL",
	1: "TASK_VIEW_TYPE_CHECK_IN",
	2: "TASK_VIEW_TYPE_STEP",
}
var TaskViewType_value = map[string]int32{
	"TASK_VIEW_TYPE_NORMAL":   0,
	"TASK_VIEW_TYPE_CHECK_IN": 1,
	"TASK_VIEW_TYPE_STEP":     2,
}

func (x TaskViewType) String() string {
	return proto.EnumName(TaskViewType_name, int32(x))
}
func (TaskViewType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{2}
}

type TaskStatus int32

const (
	TaskStatus_TASK_STATUS_DOING TaskStatus = 0
	TaskStatus_TASK_STATUS_DONE  TaskStatus = 1
)

var TaskStatus_name = map[int32]string{
	0: "TASK_STATUS_DOING",
	1: "TASK_STATUS_DONE",
}
var TaskStatus_value = map[string]int32{
	"TASK_STATUS_DOING": 0,
	"TASK_STATUS_DONE":  1,
}

func (x TaskStatus) String() string {
	return proto.EnumName(TaskStatus_name, int32(x))
}
func (TaskStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{3}
}

type ActionType int32

const (
	ActionType_ActionTypeNone    ActionType = 0
	ActionType_ActionTypeUrl     ActionType = 1
	ActionType_ActionTypeCheckIn ActionType = 2
)

var ActionType_name = map[int32]string{
	0: "ActionTypeNone",
	1: "ActionTypeUrl",
	2: "ActionTypeCheckIn",
}
var ActionType_value = map[string]int32{
	"ActionTypeNone":    0,
	"ActionTypeUrl":     1,
	"ActionTypeCheckIn": 2,
}

func (x ActionType) String() string {
	return proto.EnumName(ActionType_name, int32(x))
}
func (ActionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{4}
}

type GetLevelDetailRequest struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	MarketId             uint32   `protobuf:"varint,3,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLevelDetailRequest) Reset()         { *m = GetLevelDetailRequest{} }
func (m *GetLevelDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetLevelDetailRequest) ProtoMessage()    {}
func (*GetLevelDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{0}
}
func (m *GetLevelDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelDetailRequest.Unmarshal(m, b)
}
func (m *GetLevelDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetLevelDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelDetailRequest.Merge(dst, src)
}
func (m *GetLevelDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetLevelDetailRequest.Size(m)
}
func (m *GetLevelDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelDetailRequest proto.InternalMessageInfo

func (m *GetLevelDetailRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetLevelDetailRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLevelDetailRequest) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

type GetLevelDetailResponse struct {
	SocialCommunityId    string                      `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string                      `protobuf:"bytes,2,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	SocialCommunityLogo  string                      `protobuf:"bytes,3,opt,name=social_community_logo,json=socialCommunityLogo,proto3" json:"social_community_logo,omitempty"`
	LevelCard            *SocialCommunityLevelCard   `protobuf:"bytes,4,opt,name=level_card,json=levelCard,proto3" json:"level_card,omitempty"`
	RightGroup           *SocialCommunityRightGroup  `protobuf:"bytes,5,opt,name=right_group,json=rightGroup,proto3" json:"right_group,omitempty"`
	TaskGroups           []*SocialCommunityTaskGroup `protobuf:"bytes,6,rep,name=task_groups,json=taskGroups,proto3" json:"task_groups,omitempty"`
	Member               *BrandMember                `protobuf:"bytes,7,opt,name=member,proto3" json:"member,omitempty"`
	Professionalism      uint32                      `protobuf:"varint,8,opt,name=professionalism,proto3" json:"professionalism,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetLevelDetailResponse) Reset()         { *m = GetLevelDetailResponse{} }
func (m *GetLevelDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetLevelDetailResponse) ProtoMessage()    {}
func (*GetLevelDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{1}
}
func (m *GetLevelDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLevelDetailResponse.Unmarshal(m, b)
}
func (m *GetLevelDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLevelDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetLevelDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLevelDetailResponse.Merge(dst, src)
}
func (m *GetLevelDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetLevelDetailResponse.Size(m)
}
func (m *GetLevelDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLevelDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLevelDetailResponse proto.InternalMessageInfo

func (m *GetLevelDetailResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetLevelDetailResponse) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *GetLevelDetailResponse) GetSocialCommunityLogo() string {
	if m != nil {
		return m.SocialCommunityLogo
	}
	return ""
}

func (m *GetLevelDetailResponse) GetLevelCard() *SocialCommunityLevelCard {
	if m != nil {
		return m.LevelCard
	}
	return nil
}

func (m *GetLevelDetailResponse) GetRightGroup() *SocialCommunityRightGroup {
	if m != nil {
		return m.RightGroup
	}
	return nil
}

func (m *GetLevelDetailResponse) GetTaskGroups() []*SocialCommunityTaskGroup {
	if m != nil {
		return m.TaskGroups
	}
	return nil
}

func (m *GetLevelDetailResponse) GetMember() *BrandMember {
	if m != nil {
		return m.Member
	}
	return nil
}

func (m *GetLevelDetailResponse) GetProfessionalism() uint32 {
	if m != nil {
		return m.Professionalism
	}
	return 0
}

type BrandMember struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoleText             string          `protobuf:"bytes,2,opt,name=role_text,json=roleText,proto3" json:"role_text,omitempty"`
	Role                 BrandMemberRole `protobuf:"varint,3,opt,name=role,proto3,enum=muse_social_community_achieve.BrandMemberRole" json:"role,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BrandMember) Reset()         { *m = BrandMember{} }
func (m *BrandMember) String() string { return proto.CompactTextString(m) }
func (*BrandMember) ProtoMessage()    {}
func (*BrandMember) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{2}
}
func (m *BrandMember) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BrandMember.Unmarshal(m, b)
}
func (m *BrandMember) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BrandMember.Marshal(b, m, deterministic)
}
func (dst *BrandMember) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BrandMember.Merge(dst, src)
}
func (m *BrandMember) XXX_Size() int {
	return xxx_messageInfo_BrandMember.Size(m)
}
func (m *BrandMember) XXX_DiscardUnknown() {
	xxx_messageInfo_BrandMember.DiscardUnknown(m)
}

var xxx_messageInfo_BrandMember proto.InternalMessageInfo

func (m *BrandMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BrandMember) GetRoleText() string {
	if m != nil {
		return m.RoleText
	}
	return ""
}

func (m *BrandMember) GetRole() BrandMemberRole {
	if m != nil {
		return m.Role
	}
	return BrandMemberRole_Brand_Role_None
}

type SocialCommunityRightGroup struct {
	Title                 string                  `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	SocialCommunityRights []*SocialCommunityRight `protobuf:"bytes,2,rep,name=social_community_rights,json=socialCommunityRights,proto3" json:"social_community_rights,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *SocialCommunityRightGroup) Reset()         { *m = SocialCommunityRightGroup{} }
func (m *SocialCommunityRightGroup) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRightGroup) ProtoMessage()    {}
func (*SocialCommunityRightGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{3}
}
func (m *SocialCommunityRightGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRightGroup.Unmarshal(m, b)
}
func (m *SocialCommunityRightGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRightGroup.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRightGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRightGroup.Merge(dst, src)
}
func (m *SocialCommunityRightGroup) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRightGroup.Size(m)
}
func (m *SocialCommunityRightGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRightGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRightGroup proto.InternalMessageInfo

func (m *SocialCommunityRightGroup) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityRightGroup) GetSocialCommunityRights() []*SocialCommunityRight {
	if m != nil {
		return m.SocialCommunityRights
	}
	return nil
}

type SocialCommunityLevelCard struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	CurExp               int64    `protobuf:"varint,2,opt,name=cur_exp,json=curExp,proto3" json:"cur_exp,omitempty"`
	NextLevelExp         int64    `protobuf:"varint,3,opt,name=next_level_exp,json=nextLevelExp,proto3" json:"next_level_exp,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	UserTodayExp         int64    `protobuf:"varint,5,opt,name=user_today_exp,json=userTodayExp,proto3" json:"user_today_exp,omitempty"`
	UserTotalExp         int64    `protobuf:"varint,6,opt,name=user_total_exp,json=userTotalExp,proto3" json:"user_total_exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityLevelCard) Reset()         { *m = SocialCommunityLevelCard{} }
func (m *SocialCommunityLevelCard) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityLevelCard) ProtoMessage()    {}
func (*SocialCommunityLevelCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{4}
}
func (m *SocialCommunityLevelCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityLevelCard.Unmarshal(m, b)
}
func (m *SocialCommunityLevelCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityLevelCard.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityLevelCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityLevelCard.Merge(dst, src)
}
func (m *SocialCommunityLevelCard) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityLevelCard.Size(m)
}
func (m *SocialCommunityLevelCard) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityLevelCard.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityLevelCard proto.InternalMessageInfo

func (m *SocialCommunityLevelCard) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetCurExp() int64 {
	if m != nil {
		return m.CurExp
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetNextLevelExp() int64 {
	if m != nil {
		return m.NextLevelExp
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SocialCommunityLevelCard) GetUserTodayExp() int64 {
	if m != nil {
		return m.UserTodayExp
	}
	return 0
}

func (m *SocialCommunityLevelCard) GetUserTotalExp() int64 {
	if m != nil {
		return m.UserTotalExp
	}
	return 0
}

type SocialCommunityRight struct {
	Logo                 string                     `protobuf:"bytes,1,opt,name=logo,proto3" json:"logo,omitempty"`
	Title                string                     `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string                     `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	Status               SocialCommunityRightStatus `protobuf:"varint,4,opt,name=status,proto3,enum=muse_social_community_achieve.SocialCommunityRightStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *SocialCommunityRight) Reset()         { *m = SocialCommunityRight{} }
func (m *SocialCommunityRight) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityRight) ProtoMessage()    {}
func (*SocialCommunityRight) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{5}
}
func (m *SocialCommunityRight) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityRight.Unmarshal(m, b)
}
func (m *SocialCommunityRight) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityRight.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityRight) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityRight.Merge(dst, src)
}
func (m *SocialCommunityRight) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityRight.Size(m)
}
func (m *SocialCommunityRight) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityRight.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityRight proto.InternalMessageInfo

func (m *SocialCommunityRight) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *SocialCommunityRight) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityRight) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityRight) GetStatus() SocialCommunityRightStatus {
	if m != nil {
		return m.Status
	}
	return SocialCommunityRightStatus_Status_Lock
}

type SocialCommunityTaskGroup struct {
	Title                string                 `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Tasks                []*SocialCommunityTask `protobuf:"bytes,7,rep,name=tasks,proto3" json:"tasks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SocialCommunityTaskGroup) Reset()         { *m = SocialCommunityTaskGroup{} }
func (m *SocialCommunityTaskGroup) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityTaskGroup) ProtoMessage()    {}
func (*SocialCommunityTaskGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{6}
}
func (m *SocialCommunityTaskGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityTaskGroup.Unmarshal(m, b)
}
func (m *SocialCommunityTaskGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityTaskGroup.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityTaskGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityTaskGroup.Merge(dst, src)
}
func (m *SocialCommunityTaskGroup) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityTaskGroup.Size(m)
}
func (m *SocialCommunityTaskGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityTaskGroup.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityTaskGroup proto.InternalMessageInfo

func (m *SocialCommunityTaskGroup) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityTaskGroup) GetTasks() []*SocialCommunityTask {
	if m != nil {
		return m.Tasks
	}
	return nil
}

type SocialCommunityTask struct {
	ViewType TaskViewType `protobuf:"varint,1,opt,name=view_type,json=viewType,proto3,enum=muse_social_community_achieve.TaskViewType" json:"view_type,omitempty"`
	// Types that are valid to be assigned to SocialCommunityTaskView:
	//	*SocialCommunityTask_NormalTaskView
	//	*SocialCommunityTask_CheckInTaskView
	//	*SocialCommunityTask_StepTaskView
	SocialCommunityTaskView isSocialCommunityTask_SocialCommunityTaskView `protobuf_oneof:"social_community_task_view"`
	Status                  TaskStatus                                    `protobuf:"varint,5,opt,name=status,proto3,enum=muse_social_community_achieve.TaskStatus" json:"status,omitempty"`
	ActionType              ActionType                                    `protobuf:"varint,6,opt,name=action_type,json=actionType,proto3,enum=muse_social_community_achieve.ActionType" json:"action_type,omitempty"`
	ActionUrl               string                                        `protobuf:"bytes,7,opt,name=action_url,json=actionUrl,proto3" json:"action_url,omitempty"`
	BgColors                []string                                      `protobuf:"bytes,8,rep,name=bg_colors,json=bgColors,proto3" json:"bg_colors,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}                                      `json:"-"`
	XXX_unrecognized        []byte                                        `json:"-"`
	XXX_sizecache           int32                                         `json:"-"`
}

func (m *SocialCommunityTask) Reset()         { *m = SocialCommunityTask{} }
func (m *SocialCommunityTask) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityTask) ProtoMessage()    {}
func (*SocialCommunityTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{7}
}
func (m *SocialCommunityTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityTask.Unmarshal(m, b)
}
func (m *SocialCommunityTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityTask.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityTask.Merge(dst, src)
}
func (m *SocialCommunityTask) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityTask.Size(m)
}
func (m *SocialCommunityTask) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityTask.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityTask proto.InternalMessageInfo

func (m *SocialCommunityTask) GetViewType() TaskViewType {
	if m != nil {
		return m.ViewType
	}
	return TaskViewType_TASK_VIEW_TYPE_NORMAL
}

type isSocialCommunityTask_SocialCommunityTaskView interface {
	isSocialCommunityTask_SocialCommunityTaskView()
}

type SocialCommunityTask_NormalTaskView struct {
	NormalTaskView *SocialCommunityNormalTaskView `protobuf:"bytes,2,opt,name=normal_task_view,json=normalTaskView,proto3,oneof"`
}

type SocialCommunityTask_CheckInTaskView struct {
	CheckInTaskView *SocialCommunityCheckInTaskView `protobuf:"bytes,3,opt,name=check_in_task_view,json=checkInTaskView,proto3,oneof"`
}

type SocialCommunityTask_StepTaskView struct {
	StepTaskView *SocialCommunityStepTaskView `protobuf:"bytes,4,opt,name=step_task_view,json=stepTaskView,proto3,oneof"`
}

func (*SocialCommunityTask_NormalTaskView) isSocialCommunityTask_SocialCommunityTaskView() {}

func (*SocialCommunityTask_CheckInTaskView) isSocialCommunityTask_SocialCommunityTaskView() {}

func (*SocialCommunityTask_StepTaskView) isSocialCommunityTask_SocialCommunityTaskView() {}

func (m *SocialCommunityTask) GetSocialCommunityTaskView() isSocialCommunityTask_SocialCommunityTaskView {
	if m != nil {
		return m.SocialCommunityTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetNormalTaskView() *SocialCommunityNormalTaskView {
	if x, ok := m.GetSocialCommunityTaskView().(*SocialCommunityTask_NormalTaskView); ok {
		return x.NormalTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetCheckInTaskView() *SocialCommunityCheckInTaskView {
	if x, ok := m.GetSocialCommunityTaskView().(*SocialCommunityTask_CheckInTaskView); ok {
		return x.CheckInTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetStepTaskView() *SocialCommunityStepTaskView {
	if x, ok := m.GetSocialCommunityTaskView().(*SocialCommunityTask_StepTaskView); ok {
		return x.StepTaskView
	}
	return nil
}

func (m *SocialCommunityTask) GetStatus() TaskStatus {
	if m != nil {
		return m.Status
	}
	return TaskStatus_TASK_STATUS_DOING
}

func (m *SocialCommunityTask) GetActionType() ActionType {
	if m != nil {
		return m.ActionType
	}
	return ActionType_ActionTypeNone
}

func (m *SocialCommunityTask) GetActionUrl() string {
	if m != nil {
		return m.ActionUrl
	}
	return ""
}

func (m *SocialCommunityTask) GetBgColors() []string {
	if m != nil {
		return m.BgColors
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SocialCommunityTask) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SocialCommunityTask_OneofMarshaler, _SocialCommunityTask_OneofUnmarshaler, _SocialCommunityTask_OneofSizer, []interface{}{
		(*SocialCommunityTask_NormalTaskView)(nil),
		(*SocialCommunityTask_CheckInTaskView)(nil),
		(*SocialCommunityTask_StepTaskView)(nil),
	}
}

func _SocialCommunityTask_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SocialCommunityTask)
	// social_community_task_view
	switch x := m.SocialCommunityTaskView.(type) {
	case *SocialCommunityTask_NormalTaskView:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.NormalTaskView); err != nil {
			return err
		}
	case *SocialCommunityTask_CheckInTaskView:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.CheckInTaskView); err != nil {
			return err
		}
	case *SocialCommunityTask_StepTaskView:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.StepTaskView); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SocialCommunityTask.SocialCommunityTaskView has unexpected type %T", x)
	}
	return nil
}

func _SocialCommunityTask_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SocialCommunityTask)
	switch tag {
	case 2: // social_community_task_view.normal_task_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityNormalTaskView)
		err := b.DecodeMessage(msg)
		m.SocialCommunityTaskView = &SocialCommunityTask_NormalTaskView{msg}
		return true, err
	case 3: // social_community_task_view.check_in_task_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityCheckInTaskView)
		err := b.DecodeMessage(msg)
		m.SocialCommunityTaskView = &SocialCommunityTask_CheckInTaskView{msg}
		return true, err
	case 4: // social_community_task_view.step_task_view
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(SocialCommunityStepTaskView)
		err := b.DecodeMessage(msg)
		m.SocialCommunityTaskView = &SocialCommunityTask_StepTaskView{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SocialCommunityTask_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SocialCommunityTask)
	// social_community_task_view
	switch x := m.SocialCommunityTaskView.(type) {
	case *SocialCommunityTask_NormalTaskView:
		s := proto.Size(x.NormalTaskView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SocialCommunityTask_CheckInTaskView:
		s := proto.Size(x.CheckInTaskView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *SocialCommunityTask_StepTaskView:
		s := proto.Size(x.StepTaskView)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type SocialCommunityNormalTaskView struct {
	AwardExp             int64    `protobuf:"varint,1,opt,name=award_exp,json=awardExp,proto3" json:"award_exp,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ActiveDesc           string   `protobuf:"bytes,4,opt,name=active_desc,json=activeDesc,proto3" json:"active_desc,omitempty"`
	ButtonText           string   `protobuf:"bytes,5,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityNormalTaskView) Reset()         { *m = SocialCommunityNormalTaskView{} }
func (m *SocialCommunityNormalTaskView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityNormalTaskView) ProtoMessage()    {}
func (*SocialCommunityNormalTaskView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{8}
}
func (m *SocialCommunityNormalTaskView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityNormalTaskView.Unmarshal(m, b)
}
func (m *SocialCommunityNormalTaskView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityNormalTaskView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityNormalTaskView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityNormalTaskView.Merge(dst, src)
}
func (m *SocialCommunityNormalTaskView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityNormalTaskView.Size(m)
}
func (m *SocialCommunityNormalTaskView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityNormalTaskView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityNormalTaskView proto.InternalMessageInfo

func (m *SocialCommunityNormalTaskView) GetAwardExp() int64 {
	if m != nil {
		return m.AwardExp
	}
	return 0
}

func (m *SocialCommunityNormalTaskView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityNormalTaskView) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityNormalTaskView) GetActiveDesc() string {
	if m != nil {
		return m.ActiveDesc
	}
	return ""
}

func (m *SocialCommunityNormalTaskView) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

type SocialCommunityCheckInTaskView struct {
	AwardExp             int64             `protobuf:"varint,1,opt,name=award_exp,json=awardExp,proto3" json:"award_exp,omitempty"`
	Title                string            `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string            `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	ActiveDesc           string            `protobuf:"bytes,4,opt,name=active_desc,json=activeDesc,proto3" json:"active_desc,omitempty"`
	StatusButtonText     map[uint32]string `protobuf:"bytes,5,rep,name=status_button_text,json=statusButtonText,proto3" json:"status_button_text,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SocialCommunityCheckInTaskView) Reset()         { *m = SocialCommunityCheckInTaskView{} }
func (m *SocialCommunityCheckInTaskView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityCheckInTaskView) ProtoMessage()    {}
func (*SocialCommunityCheckInTaskView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{9}
}
func (m *SocialCommunityCheckInTaskView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityCheckInTaskView.Unmarshal(m, b)
}
func (m *SocialCommunityCheckInTaskView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityCheckInTaskView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityCheckInTaskView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityCheckInTaskView.Merge(dst, src)
}
func (m *SocialCommunityCheckInTaskView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityCheckInTaskView.Size(m)
}
func (m *SocialCommunityCheckInTaskView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityCheckInTaskView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityCheckInTaskView proto.InternalMessageInfo

func (m *SocialCommunityCheckInTaskView) GetAwardExp() int64 {
	if m != nil {
		return m.AwardExp
	}
	return 0
}

func (m *SocialCommunityCheckInTaskView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityCheckInTaskView) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityCheckInTaskView) GetActiveDesc() string {
	if m != nil {
		return m.ActiveDesc
	}
	return ""
}

func (m *SocialCommunityCheckInTaskView) GetStatusButtonText() map[uint32]string {
	if m != nil {
		return m.StatusButtonText
	}
	return nil
}

type SocialCommunityStepTaskView struct {
	Steps                []*SocialCommunityStep `protobuf:"bytes,1,rep,name=steps,proto3" json:"steps,omitempty"`
	Title                string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	ButtonText           string                 `protobuf:"bytes,3,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	Score                int64                  `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	Desc                 string                 `protobuf:"bytes,5,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *SocialCommunityStepTaskView) Reset()         { *m = SocialCommunityStepTaskView{} }
func (m *SocialCommunityStepTaskView) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityStepTaskView) ProtoMessage()    {}
func (*SocialCommunityStepTaskView) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{10}
}
func (m *SocialCommunityStepTaskView) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityStepTaskView.Unmarshal(m, b)
}
func (m *SocialCommunityStepTaskView) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityStepTaskView.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityStepTaskView) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityStepTaskView.Merge(dst, src)
}
func (m *SocialCommunityStepTaskView) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityStepTaskView.Size(m)
}
func (m *SocialCommunityStepTaskView) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityStepTaskView.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityStepTaskView proto.InternalMessageInfo

func (m *SocialCommunityStepTaskView) GetSteps() []*SocialCommunityStep {
	if m != nil {
		return m.Steps
	}
	return nil
}

func (m *SocialCommunityStepTaskView) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SocialCommunityStepTaskView) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *SocialCommunityStepTaskView) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *SocialCommunityStepTaskView) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type SocialCommunityStep struct {
	AwardExp             int64    `protobuf:"varint,1,opt,name=award_exp,json=awardExp,proto3" json:"award_exp,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Score                int64    `protobuf:"varint,3,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SocialCommunityStep) Reset()         { *m = SocialCommunityStep{} }
func (m *SocialCommunityStep) String() string { return proto.CompactTextString(m) }
func (*SocialCommunityStep) ProtoMessage()    {}
func (*SocialCommunityStep) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{11}
}
func (m *SocialCommunityStep) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SocialCommunityStep.Unmarshal(m, b)
}
func (m *SocialCommunityStep) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SocialCommunityStep.Marshal(b, m, deterministic)
}
func (dst *SocialCommunityStep) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SocialCommunityStep.Merge(dst, src)
}
func (m *SocialCommunityStep) XXX_Size() int {
	return xxx_messageInfo_SocialCommunityStep.Size(m)
}
func (m *SocialCommunityStep) XXX_DiscardUnknown() {
	xxx_messageInfo_SocialCommunityStep.DiscardUnknown(m)
}

var xxx_messageInfo_SocialCommunityStep proto.InternalMessageInfo

func (m *SocialCommunityStep) GetAwardExp() int64 {
	if m != nil {
		return m.AwardExp
	}
	return 0
}

func (m *SocialCommunityStep) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SocialCommunityStep) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type CheckInRequest struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInRequest) Reset()         { *m = CheckInRequest{} }
func (m *CheckInRequest) String() string { return proto.CompactTextString(m) }
func (*CheckInRequest) ProtoMessage()    {}
func (*CheckInRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{12}
}
func (m *CheckInRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInRequest.Unmarshal(m, b)
}
func (m *CheckInRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInRequest.Marshal(b, m, deterministic)
}
func (dst *CheckInRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInRequest.Merge(dst, src)
}
func (m *CheckInRequest) XXX_Size() int {
	return xxx_messageInfo_CheckInRequest.Size(m)
}
func (m *CheckInRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInRequest proto.InternalMessageInfo

func (m *CheckInRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type CheckInResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInResponse) Reset()         { *m = CheckInResponse{} }
func (m *CheckInResponse) String() string { return proto.CompactTextString(m) }
func (*CheckInResponse) ProtoMessage()    {}
func (*CheckInResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{13}
}
func (m *CheckInResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInResponse.Unmarshal(m, b)
}
func (m *CheckInResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInResponse.Marshal(b, m, deterministic)
}
func (dst *CheckInResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInResponse.Merge(dst, src)
}
func (m *CheckInResponse) XXX_Size() int {
	return xxx_messageInfo_CheckInResponse.Size(m)
}
func (m *CheckInResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInResponse proto.InternalMessageInfo

type GetCheckInSimpleRequest struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCheckInSimpleRequest) Reset()         { *m = GetCheckInSimpleRequest{} }
func (m *GetCheckInSimpleRequest) String() string { return proto.CompactTextString(m) }
func (*GetCheckInSimpleRequest) ProtoMessage()    {}
func (*GetCheckInSimpleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{14}
}
func (m *GetCheckInSimpleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCheckInSimpleRequest.Unmarshal(m, b)
}
func (m *GetCheckInSimpleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCheckInSimpleRequest.Marshal(b, m, deterministic)
}
func (dst *GetCheckInSimpleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCheckInSimpleRequest.Merge(dst, src)
}
func (m *GetCheckInSimpleRequest) XXX_Size() int {
	return xxx_messageInfo_GetCheckInSimpleRequest.Size(m)
}
func (m *GetCheckInSimpleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCheckInSimpleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCheckInSimpleRequest proto.InternalMessageInfo

func (m *GetCheckInSimpleRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetCheckInSimpleResponse struct {
	CheckInStatus        uint32   `protobuf:"varint,1,opt,name=check_in_status,json=checkInStatus,proto3" json:"check_in_status,omitempty"`
	CheckInExp           uint32   `protobuf:"varint,2,opt,name=check_in_exp,json=checkInExp,proto3" json:"check_in_exp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCheckInSimpleResponse) Reset()         { *m = GetCheckInSimpleResponse{} }
func (m *GetCheckInSimpleResponse) String() string { return proto.CompactTextString(m) }
func (*GetCheckInSimpleResponse) ProtoMessage()    {}
func (*GetCheckInSimpleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{15}
}
func (m *GetCheckInSimpleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCheckInSimpleResponse.Unmarshal(m, b)
}
func (m *GetCheckInSimpleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCheckInSimpleResponse.Marshal(b, m, deterministic)
}
func (dst *GetCheckInSimpleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCheckInSimpleResponse.Merge(dst, src)
}
func (m *GetCheckInSimpleResponse) XXX_Size() int {
	return xxx_messageInfo_GetCheckInSimpleResponse.Size(m)
}
func (m *GetCheckInSimpleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCheckInSimpleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCheckInSimpleResponse proto.InternalMessageInfo

func (m *GetCheckInSimpleResponse) GetCheckInStatus() uint32 {
	if m != nil {
		return m.CheckInStatus
	}
	return 0
}

func (m *GetCheckInSimpleResponse) GetCheckInExp() uint32 {
	if m != nil {
		return m.CheckInExp
	}
	return 0
}

type GetSocialCommunityUpdateLevelTipRequest struct {
	SocialCommunityId    string   `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSocialCommunityUpdateLevelTipRequest) Reset() {
	*m = GetSocialCommunityUpdateLevelTipRequest{}
}
func (m *GetSocialCommunityUpdateLevelTipRequest) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityUpdateLevelTipRequest) ProtoMessage()    {}
func (*GetSocialCommunityUpdateLevelTipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{16}
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Unmarshal(m, b)
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityUpdateLevelTipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Merge(dst, src)
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.Size(m)
}
func (m *GetSocialCommunityUpdateLevelTipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityUpdateLevelTipRequest proto.InternalMessageInfo

func (m *GetSocialCommunityUpdateLevelTipRequest) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

type GetSocialCommunityUpdateLevelTipResponse struct {
	SocialCommunityId    string                     `protobuf:"bytes,1,opt,name=social_community_id,json=socialCommunityId,proto3" json:"social_community_id,omitempty"`
	SocialCommunityName  string                     `protobuf:"bytes,2,opt,name=social_community_name,json=socialCommunityName,proto3" json:"social_community_name,omitempty"`
	Logo                 string                     `protobuf:"bytes,3,opt,name=logo,proto3" json:"logo,omitempty"`
	RightGroup           *SocialCommunityRightGroup `protobuf:"bytes,4,opt,name=right_group,json=rightGroup,proto3" json:"right_group,omitempty"`
	Captain              uint32                     `protobuf:"varint,5,opt,name=captain,proto3" json:"captain,omitempty"`
	Level                uint32                     `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSocialCommunityUpdateLevelTipResponse) Reset() {
	*m = GetSocialCommunityUpdateLevelTipResponse{}
}
func (m *GetSocialCommunityUpdateLevelTipResponse) String() string { return proto.CompactTextString(m) }
func (*GetSocialCommunityUpdateLevelTipResponse) ProtoMessage()    {}
func (*GetSocialCommunityUpdateLevelTipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_muse_social_community_achieve_335d46f8e0203523, []int{17}
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Unmarshal(m, b)
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Marshal(b, m, deterministic)
}
func (dst *GetSocialCommunityUpdateLevelTipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Merge(dst, src)
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_Size() int {
	return xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.Size(m)
}
func (m *GetSocialCommunityUpdateLevelTipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetSocialCommunityUpdateLevelTipResponse proto.InternalMessageInfo

func (m *GetSocialCommunityUpdateLevelTipResponse) GetSocialCommunityId() string {
	if m != nil {
		return m.SocialCommunityId
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetSocialCommunityName() string {
	if m != nil {
		return m.SocialCommunityName
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetLogo() string {
	if m != nil {
		return m.Logo
	}
	return ""
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetRightGroup() *SocialCommunityRightGroup {
	if m != nil {
		return m.RightGroup
	}
	return nil
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetCaptain() uint32 {
	if m != nil {
		return m.Captain
	}
	return 0
}

func (m *GetSocialCommunityUpdateLevelTipResponse) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func init() {
	proto.RegisterType((*GetLevelDetailRequest)(nil), "muse_social_community_achieve.GetLevelDetailRequest")
	proto.RegisterType((*GetLevelDetailResponse)(nil), "muse_social_community_achieve.GetLevelDetailResponse")
	proto.RegisterType((*BrandMember)(nil), "muse_social_community_achieve.BrandMember")
	proto.RegisterType((*SocialCommunityRightGroup)(nil), "muse_social_community_achieve.SocialCommunityRightGroup")
	proto.RegisterType((*SocialCommunityLevelCard)(nil), "muse_social_community_achieve.SocialCommunityLevelCard")
	proto.RegisterType((*SocialCommunityRight)(nil), "muse_social_community_achieve.SocialCommunityRight")
	proto.RegisterType((*SocialCommunityTaskGroup)(nil), "muse_social_community_achieve.SocialCommunityTaskGroup")
	proto.RegisterType((*SocialCommunityTask)(nil), "muse_social_community_achieve.SocialCommunityTask")
	proto.RegisterType((*SocialCommunityNormalTaskView)(nil), "muse_social_community_achieve.SocialCommunityNormalTaskView")
	proto.RegisterType((*SocialCommunityCheckInTaskView)(nil), "muse_social_community_achieve.SocialCommunityCheckInTaskView")
	proto.RegisterMapType((map[uint32]string)(nil), "muse_social_community_achieve.SocialCommunityCheckInTaskView.StatusButtonTextEntry")
	proto.RegisterType((*SocialCommunityStepTaskView)(nil), "muse_social_community_achieve.SocialCommunityStepTaskView")
	proto.RegisterType((*SocialCommunityStep)(nil), "muse_social_community_achieve.SocialCommunityStep")
	proto.RegisterType((*CheckInRequest)(nil), "muse_social_community_achieve.CheckInRequest")
	proto.RegisterType((*CheckInResponse)(nil), "muse_social_community_achieve.CheckInResponse")
	proto.RegisterType((*GetCheckInSimpleRequest)(nil), "muse_social_community_achieve.GetCheckInSimpleRequest")
	proto.RegisterType((*GetCheckInSimpleResponse)(nil), "muse_social_community_achieve.GetCheckInSimpleResponse")
	proto.RegisterType((*GetSocialCommunityUpdateLevelTipRequest)(nil), "muse_social_community_achieve.GetSocialCommunityUpdateLevelTipRequest")
	proto.RegisterType((*GetSocialCommunityUpdateLevelTipResponse)(nil), "muse_social_community_achieve.GetSocialCommunityUpdateLevelTipResponse")
	proto.RegisterEnum("muse_social_community_achieve.BrandMemberRole", BrandMemberRole_name, BrandMemberRole_value)
	proto.RegisterEnum("muse_social_community_achieve.SocialCommunityRightStatus", SocialCommunityRightStatus_name, SocialCommunityRightStatus_value)
	proto.RegisterEnum("muse_social_community_achieve.TaskViewType", TaskViewType_name, TaskViewType_value)
	proto.RegisterEnum("muse_social_community_achieve.TaskStatus", TaskStatus_name, TaskStatus_value)
	proto.RegisterEnum("muse_social_community_achieve.ActionType", ActionType_name, ActionType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MuseSocialCommunityAchieveClient is the client API for MuseSocialCommunityAchieve service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MuseSocialCommunityAchieveClient interface {
	GetLevelDetail(ctx context.Context, in *GetLevelDetailRequest, opts ...grpc.CallOption) (*GetLevelDetailResponse, error)
	CheckIn(ctx context.Context, in *CheckInRequest, opts ...grpc.CallOption) (*CheckInResponse, error)
	GetCheckInSimple(ctx context.Context, in *GetCheckInSimpleRequest, opts ...grpc.CallOption) (*GetCheckInSimpleResponse, error)
	GetSocialCommunityUpdateLevelTip(ctx context.Context, in *GetSocialCommunityUpdateLevelTipRequest, opts ...grpc.CallOption) (*GetSocialCommunityUpdateLevelTipResponse, error)
}

type museSocialCommunityAchieveClient struct {
	cc *grpc.ClientConn
}

func NewMuseSocialCommunityAchieveClient(cc *grpc.ClientConn) MuseSocialCommunityAchieveClient {
	return &museSocialCommunityAchieveClient{cc}
}

func (c *museSocialCommunityAchieveClient) GetLevelDetail(ctx context.Context, in *GetLevelDetailRequest, opts ...grpc.CallOption) (*GetLevelDetailResponse, error) {
	out := new(GetLevelDetailResponse)
	err := c.cc.Invoke(ctx, "/muse_social_community_achieve.muse_social_community_achieve/GetLevelDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialCommunityAchieveClient) CheckIn(ctx context.Context, in *CheckInRequest, opts ...grpc.CallOption) (*CheckInResponse, error) {
	out := new(CheckInResponse)
	err := c.cc.Invoke(ctx, "/muse_social_community_achieve.muse_social_community_achieve/CheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialCommunityAchieveClient) GetCheckInSimple(ctx context.Context, in *GetCheckInSimpleRequest, opts ...grpc.CallOption) (*GetCheckInSimpleResponse, error) {
	out := new(GetCheckInSimpleResponse)
	err := c.cc.Invoke(ctx, "/muse_social_community_achieve.muse_social_community_achieve/GetCheckInSimple", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *museSocialCommunityAchieveClient) GetSocialCommunityUpdateLevelTip(ctx context.Context, in *GetSocialCommunityUpdateLevelTipRequest, opts ...grpc.CallOption) (*GetSocialCommunityUpdateLevelTipResponse, error) {
	out := new(GetSocialCommunityUpdateLevelTipResponse)
	err := c.cc.Invoke(ctx, "/muse_social_community_achieve.muse_social_community_achieve/GetSocialCommunityUpdateLevelTip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MuseSocialCommunityAchieveServer is the server API for MuseSocialCommunityAchieve service.
type MuseSocialCommunityAchieveServer interface {
	GetLevelDetail(context.Context, *GetLevelDetailRequest) (*GetLevelDetailResponse, error)
	CheckIn(context.Context, *CheckInRequest) (*CheckInResponse, error)
	GetCheckInSimple(context.Context, *GetCheckInSimpleRequest) (*GetCheckInSimpleResponse, error)
	GetSocialCommunityUpdateLevelTip(context.Context, *GetSocialCommunityUpdateLevelTipRequest) (*GetSocialCommunityUpdateLevelTipResponse, error)
}

func RegisterMuseSocialCommunityAchieveServer(s *grpc.Server, srv MuseSocialCommunityAchieveServer) {
	s.RegisterService(&_MuseSocialCommunityAchieve_serviceDesc, srv)
}

func _MuseSocialCommunityAchieve_GetLevelDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLevelDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialCommunityAchieveServer).GetLevelDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_community_achieve.muse_social_community_achieve/GetLevelDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialCommunityAchieveServer).GetLevelDetail(ctx, req.(*GetLevelDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialCommunityAchieve_CheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialCommunityAchieveServer).CheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_community_achieve.muse_social_community_achieve/CheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialCommunityAchieveServer).CheckIn(ctx, req.(*CheckInRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialCommunityAchieve_GetCheckInSimple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckInSimpleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialCommunityAchieveServer).GetCheckInSimple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_community_achieve.muse_social_community_achieve/GetCheckInSimple",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialCommunityAchieveServer).GetCheckInSimple(ctx, req.(*GetCheckInSimpleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MuseSocialCommunityAchieve_GetSocialCommunityUpdateLevelTip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSocialCommunityUpdateLevelTipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MuseSocialCommunityAchieveServer).GetSocialCommunityUpdateLevelTip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/muse_social_community_achieve.muse_social_community_achieve/GetSocialCommunityUpdateLevelTip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MuseSocialCommunityAchieveServer).GetSocialCommunityUpdateLevelTip(ctx, req.(*GetSocialCommunityUpdateLevelTipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _MuseSocialCommunityAchieve_serviceDesc = grpc.ServiceDesc{
	ServiceName: "muse_social_community_achieve.muse_social_community_achieve",
	HandlerType: (*MuseSocialCommunityAchieveServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetLevelDetail",
			Handler:    _MuseSocialCommunityAchieve_GetLevelDetail_Handler,
		},
		{
			MethodName: "CheckIn",
			Handler:    _MuseSocialCommunityAchieve_CheckIn_Handler,
		},
		{
			MethodName: "GetCheckInSimple",
			Handler:    _MuseSocialCommunityAchieve_GetCheckInSimple_Handler,
		},
		{
			MethodName: "GetSocialCommunityUpdateLevelTip",
			Handler:    _MuseSocialCommunityAchieve_GetSocialCommunityUpdateLevelTip_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "muse-social-community-achieve/muse-social-community-achieve.proto",
}

func init() {
	proto.RegisterFile("muse-social-community-achieve/muse-social-community-achieve.proto", fileDescriptor_muse_social_community_achieve_335d46f8e0203523)
}

var fileDescriptor_muse_social_community_achieve_335d46f8e0203523 = []byte{
	// 1470 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x58, 0xdd, 0x6e, 0xdb, 0xc6,
	0x12, 0x36, 0xf5, 0x67, 0x6b, 0x6c, 0xcb, 0xf4, 0x3a, 0x8e, 0x19, 0xe7, 0xe4, 0x1c, 0x83, 0x38,
	0x38, 0xc7, 0x75, 0x61, 0x05, 0x50, 0x9a, 0xe6, 0x07, 0x2d, 0x52, 0x5b, 0x71, 0x6c, 0x27, 0x8e,
	0x92, 0x52, 0xb2, 0xdb, 0x14, 0x05, 0x88, 0x35, 0xb9, 0x95, 0x09, 0x51, 0x24, 0xcb, 0x5d, 0x2a,
	0x56, 0x81, 0x02, 0x2d, 0xda, 0x67, 0xe8, 0x65, 0x6f, 0x5b, 0xa0, 0xe8, 0x55, 0x5f, 0xa1, 0x17,
	0xbd, 0xed, 0x45, 0xdf, 0xa7, 0xd8, 0x5d, 0x4a, 0xa2, 0x7e, 0x2c, 0x59, 0x4e, 0x91, 0xbb, 0xdd,
	0x6f, 0x67, 0xbe, 0x19, 0xce, 0xcc, 0xce, 0x8e, 0x04, 0x3b, 0xcd, 0x88, 0x92, 0x6d, 0xea, 0x5b,
	0x0e, 0x76, 0xb7, 0x2d, 0xbf, 0xd9, 0x8c, 0x3c, 0x87, 0xb5, 0xb7, 0xb1, 0x75, 0xe6, 0x90, 0x16,
	0xb9, 0x3d, 0xf6, 0xb4, 0x18, 0x84, 0x3e, 0xf3, 0xd1, 0x2d, 0x2e, 0x64, 0x4a, 0x21, 0xb3, 0x2b,
	0x64, 0xc6, 0x42, 0x7a, 0x0b, 0x56, 0xf7, 0x09, 0x3b, 0x22, 0x2d, 0xe2, 0x3e, 0x26, 0x0c, 0x3b,
	0xae, 0x41, 0xbe, 0x8c, 0x08, 0x65, 0xa8, 0x08, 0x2b, 0x43, 0x4a, 0x8e, 0xad, 0x29, 0x1b, 0xca,
	0x66, 0xde, 0x58, 0x96, 0x47, 0xe5, 0xce, 0xc9, 0xa1, 0x8d, 0x54, 0x48, 0x47, 0x8e, 0xad, 0xa5,
	0x36, 0x94, 0xcd, 0x45, 0x83, 0x2f, 0xd1, 0x4d, 0xc8, 0x37, 0x71, 0xd8, 0x20, 0x8c, 0xeb, 0xa5,
	0x05, 0x3e, 0x27, 0x81, 0x43, 0x5b, 0xff, 0x25, 0x03, 0xd7, 0x07, 0x0d, 0xd3, 0xc0, 0xf7, 0x28,
	0x99, 0xda, 0x72, 0x09, 0x56, 0x87, 0xe4, 0x3d, 0xdc, 0x24, 0xc2, 0x97, 0xbc, 0xb1, 0x32, 0xa0,
	0x51, 0xc1, 0x4d, 0x32, 0x52, 0xc7, 0xf5, 0xeb, 0xbe, 0xf0, 0x73, 0x58, 0xe7, 0xc8, 0xaf, 0xfb,
	0xe8, 0x04, 0xc0, 0xe5, 0xee, 0x9a, 0x16, 0x0e, 0x6d, 0x2d, 0xb3, 0xa1, 0x6c, 0xce, 0x97, 0xee,
	0x15, 0xc7, 0x86, 0xb7, 0x58, 0x1d, 0xe0, 0xe1, 0xfa, 0x65, 0x1c, 0xda, 0x46, 0xde, 0xed, 0x2c,
	0xd1, 0x2b, 0x98, 0x0f, 0x9d, 0xfa, 0x19, 0x33, 0xeb, 0xa1, 0x1f, 0x05, 0x5a, 0x56, 0x10, 0xdf,
	0x9f, 0x8e, 0xd8, 0xe0, 0x04, 0xfb, 0x5c, 0xdf, 0x80, 0xb0, 0xbb, 0x46, 0x9f, 0xc2, 0x3c, 0xc3,
	0xb4, 0x21, 0x99, 0xa9, 0x96, 0xdb, 0x48, 0x4f, 0xef, 0x73, 0x0d, 0xd3, 0x46, 0xcc, 0xcc, 0x3a,
	0x4b, 0x8a, 0x76, 0x21, 0xd7, 0x24, 0xcd, 0x53, 0x12, 0x6a, 0xb3, 0xc2, 0xdf, 0xad, 0x09, 0xa4,
	0xbb, 0x21, 0xf6, 0xec, 0xe7, 0x42, 0xc3, 0x88, 0x35, 0xd1, 0x26, 0x2c, 0x05, 0xa1, 0xff, 0x05,
	0xa1, 0xd4, 0xf1, 0x3d, 0xec, 0x3a, 0xb4, 0xa9, 0xcd, 0x89, 0x32, 0x19, 0x84, 0xf5, 0x6f, 0x14,
	0x98, 0x4f, 0x30, 0x74, 0x8a, 0x4d, 0xe9, 0x2b, 0xb6, 0xd0, 0x77, 0x89, 0xc9, 0xc8, 0x39, 0x8b,
	0x13, 0x3f, 0xc7, 0x81, 0x1a, 0x39, 0x67, 0x68, 0x17, 0x32, 0x7c, 0x2d, 0x92, 0x5b, 0x28, 0x15,
	0xa7, 0x70, 0xd5, 0x77, 0x89, 0x21, 0x74, 0xf5, 0x1f, 0x15, 0xb8, 0x71, 0x61, 0xd0, 0xd1, 0x35,
	0xc8, 0x32, 0x87, 0xb9, 0x24, 0xae, 0x52, 0xb9, 0x41, 0x0d, 0x58, 0x1b, 0xb2, 0x22, 0xb2, 0x43,
	0xb5, 0x94, 0x48, 0xc5, 0x9d, 0x2b, 0x64, 0xd9, 0x58, 0xa5, 0x23, 0x50, 0xaa, 0xff, 0xa5, 0x80,
	0x76, 0x51, 0xb9, 0x71, 0xff, 0x44, 0xc1, 0xc5, 0x21, 0x93, 0x1b, 0xb4, 0x06, 0xb3, 0x56, 0x14,
	0x9a, 0xe4, 0x3c, 0x10, 0x21, 0x4b, 0x1b, 0x39, 0x2b, 0x0a, 0xf7, 0xce, 0x03, 0xf4, 0x5f, 0x28,
	0x78, 0xe4, 0x9c, 0x99, 0xb2, 0xde, 0xf9, 0x79, 0x5a, 0x9c, 0x2f, 0x70, 0x54, 0xb0, 0x72, 0x29,
	0x0d, 0x66, 0xb1, 0x65, 0xf9, 0x91, 0xc7, 0xc4, 0x6d, 0xc8, 0x1b, 0x9d, 0x2d, 0xd7, 0x8f, 0x28,
	0x09, 0x4d, 0xe6, 0xdb, 0xb8, 0x2d, 0xf4, 0xb3, 0x52, 0x9f, 0xa3, 0x35, 0x0e, 0xc6, 0x56, 0x62,
	0x29, 0x86, 0xa5, 0x95, 0x5c, 0x52, 0x8a, 0x61, 0x6e, 0x45, 0xff, 0x49, 0x81, 0x6b, 0xa3, 0xe2,
	0x80, 0x10, 0x64, 0xc4, 0x95, 0x95, 0x21, 0x17, 0xeb, 0x5e, 0x1e, 0x52, 0xc9, 0x3c, 0x20, 0xc8,
	0xd8, 0x84, 0x5a, 0xf1, 0xe5, 0x16, 0x6b, 0xf4, 0x31, 0xe4, 0x28, 0xc3, 0x2c, 0xa2, 0xc2, 0xf7,
	0x42, 0xe9, 0xc1, 0x15, 0x52, 0x51, 0x15, 0x04, 0x46, 0x4c, 0xa4, 0x7f, 0x35, 0x94, 0x80, 0xee,
	0xdd, 0xb9, 0xa0, 0x40, 0x0e, 0x20, 0xcb, 0xef, 0x14, 0xd5, 0x66, 0x45, 0x39, 0x94, 0xa6, 0xbf,
	0x99, 0x86, 0x24, 0xd0, 0xbf, 0xcf, 0xc2, 0xca, 0x88, 0x63, 0x74, 0x00, 0xf9, 0x96, 0x43, 0x5e,
	0x9b, 0xac, 0x1d, 0x48, 0xdb, 0x85, 0xd2, 0xbb, 0x13, 0xac, 0x70, 0xbd, 0x13, 0x87, 0xbc, 0xae,
	0xb5, 0x03, 0x62, 0xcc, 0xb5, 0xe2, 0x15, 0x3a, 0x03, 0xd5, 0xf3, 0xc3, 0x26, 0x76, 0x4d, 0xd1,
	0x52, 0x38, 0x2e, 0xa2, 0x3c, 0x5f, 0xfa, 0x60, 0x3a, 0xb7, 0x2b, 0x82, 0xa5, 0x63, 0xe5, 0x60,
	0xc6, 0x28, 0x78, 0x7d, 0x08, 0x72, 0x01, 0x59, 0x67, 0xc4, 0x6a, 0x98, 0x8e, 0x97, 0xb0, 0x95,
	0x16, 0xb6, 0x3e, 0x9c, 0xce, 0x56, 0x99, 0xf3, 0x1c, 0x7a, 0x09, 0x63, 0x4b, 0x56, 0x3f, 0x84,
	0x4e, 0xa1, 0x40, 0x19, 0x09, 0x12, 0x96, 0x64, 0x6b, 0x7f, 0x38, 0x9d, 0xa5, 0x2a, 0x23, 0x41,
	0xc2, 0xcc, 0x02, 0x4d, 0xec, 0xd1, 0x4e, 0xb7, 0xd8, 0xb2, 0x22, 0x05, 0xef, 0x5c, 0x22, 0x05,
	0xfd, 0xc5, 0x85, 0x9e, 0xc2, 0x3c, 0xb6, 0x98, 0xe3, 0x7b, 0x32, 0x95, 0xb9, 0x4b, 0xf1, 0xec,
	0x08, 0x0d, 0x91, 0x48, 0xc0, 0xdd, 0x35, 0xba, 0x05, 0xf1, 0xce, 0x8c, 0x42, 0x57, 0x34, 0xf0,
	0xbc, 0x91, 0x97, 0xc8, 0x71, 0xe8, 0xf2, 0x5e, 0x7a, 0x5a, 0x37, 0x2d, 0xdf, 0xf5, 0x43, 0xaa,
	0xcd, 0x6d, 0xa4, 0x79, 0x2f, 0x3d, 0xad, 0x97, 0xc5, 0x7e, 0xf7, 0x5f, 0xb0, 0x3e, 0x64, 0xae,
	0x1b, 0x3a, 0xfd, 0x67, 0x05, 0x6e, 0x8d, 0x4d, 0x37, 0x27, 0xc7, 0xaf, 0x71, 0x68, 0x8b, 0xfb,
	0xae, 0x88, 0xfb, 0x3e, 0x27, 0x00, 0xde, 0x11, 0x2e, 0x7f, 0x7d, 0xff, 0x23, 0xc3, 0xd1, 0x22,
	0xa6, 0x38, 0x92, 0xfd, 0x07, 0x24, 0xf4, 0x38, 0x16, 0x38, 0x8d, 0x18, 0xe3, 0xf1, 0xe2, 0x4f,
	0x42, 0x56, 0x0a, 0x48, 0x88, 0x3f, 0x0a, 0xfa, 0x9f, 0x29, 0xf8, 0xf7, 0xf8, 0x6a, 0x79, 0x6b,
	0xbe, 0x7e, 0xab, 0x00, 0x92, 0x69, 0x36, 0xfb, 0x7d, 0xe6, 0x4d, 0xa1, 0xfa, 0x46, 0x15, 0x5f,
	0x94, 0x65, 0xb4, 0xdb, 0xfd, 0xee, 0x3d, 0x8f, 0x85, 0x6d, 0x43, 0xa5, 0x03, 0xf0, 0x7a, 0x19,
	0x56, 0x47, 0x8a, 0xf2, 0xb7, 0xb6, 0x41, 0xda, 0x9d, 0xb7, 0xb6, 0x41, 0xda, 0xfc, 0xcb, 0x5b,
	0xd8, 0x8d, 0xba, 0x5f, 0x2e, 0x36, 0x0f, 0x53, 0xf7, 0x15, 0xfd, 0x0f, 0x05, 0x6e, 0x8e, 0xb9,
	0x17, 0xbc, 0xdf, 0xf1, 0x7b, 0x41, 0x35, 0xe5, 0x2a, 0xfd, 0x8e, 0x53, 0x19, 0x92, 0xe0, 0x82,
	0xe8, 0x0f, 0x24, 0x3d, 0x3d, 0x98, 0x74, 0xae, 0x46, 0x2d, 0x3f, 0x24, 0x22, 0x09, 0x69, 0x43,
	0x6e, 0xba, 0x49, 0xcb, 0xf6, 0x92, 0xa6, 0x7f, 0x3e, 0xd4, 0x4f, 0xb9, 0xf9, 0xf1, 0x25, 0xd1,
	0xe1, 0x49, 0x25, 0x92, 0xdf, 0xb5, 0x98, 0x4e, 0x58, 0xd4, 0x3f, 0x82, 0x42, 0x9c, 0xa8, 0x2b,
	0xce, 0xdb, 0xfa, 0x32, 0x2c, 0x75, 0x19, 0xe4, 0xe0, 0xac, 0x1f, 0xc2, 0xda, 0x3e, 0x61, 0x31,
	0x5a, 0x75, 0x9a, 0x81, 0x4b, 0xae, 0xca, 0x6e, 0x83, 0x36, 0x4c, 0x15, 0xcf, 0xe7, 0xff, 0x83,
	0xa5, 0x6e, 0x7b, 0x8e, 0xbb, 0x9a, 0x2c, 0x8e, 0xc5, 0xb8, 0xb5, 0xca, 0x3a, 0x42, 0x1b, 0xb0,
	0xd0, 0x95, 0xeb, 0x8c, 0x18, 0x8b, 0x06, 0xc4, 0x42, 0xfc, 0x69, 0x7f, 0x05, 0xff, 0xdf, 0x27,
	0x6c, 0x20, 0xcc, 0xc7, 0x81, 0x8d, 0x19, 0x11, 0x43, 0x46, 0xcd, 0x09, 0xae, 0xfa, 0x01, 0xbf,
	0xa6, 0x60, 0x73, 0x32, 0xf7, 0x5b, 0xfc, 0xc5, 0xd1, 0x99, 0x56, 0xd2, 0x89, 0x69, 0x65, 0x60,
	0xf2, 0xcf, 0xfc, 0x83, 0x93, 0xbf, 0x06, 0xb3, 0x16, 0x0e, 0x18, 0x76, 0x3c, 0x51, 0xd5, 0x8b,
	0x46, 0x67, 0xdb, 0x1b, 0x05, 0x73, 0x89, 0x51, 0x70, 0xeb, 0x07, 0x05, 0x96, 0x06, 0x06, 0x5f,
	0xb4, 0x12, 0x43, 0x26, 0xdf, 0x99, 0x15, 0xdf, 0x23, 0xea, 0x0c, 0xba, 0x0e, 0x28, 0x01, 0x96,
	0x25, 0xa9, 0xaa, 0xa0, 0x55, 0x58, 0x4e, 0xe0, 0xcf, 0x48, 0xe8, 0x11, 0x57, 0x4d, 0xa1, 0x9b,
	0xb0, 0x96, 0x80, 0x4f, 0x1c, 0xab, 0xa7, 0x93, 0x46, 0x08, 0x0a, 0xf2, 0xf0, 0x65, 0xe8, 0xdb,
	0x91, 0x45, 0x42, 0x35, 0x83, 0x0a, 0x00, 0x12, 0x7b, 0x82, 0x3d, 0xaa, 0x66, 0xb7, 0x1e, 0xc1,
	0xfa, 0xc5, 0xa3, 0x17, 0x5a, 0x82, 0x79, 0xb9, 0x32, 0x8f, 0x7c, 0xab, 0xa1, 0xce, 0x20, 0x15,
	0x16, 0x62, 0xe0, 0x04, 0xbb, 0x8e, 0xad, 0x2a, 0x5b, 0x26, 0x2c, 0x24, 0x27, 0x1a, 0x74, 0x03,
	0x56, 0x6b, 0x3b, 0xd5, 0x67, 0xe6, 0xc9, 0xe1, 0xde, 0x27, 0x66, 0xed, 0xd5, 0xcb, 0x3d, 0xb3,
	0xf2, 0xc2, 0x78, 0xbe, 0x73, 0xa4, 0xce, 0x70, 0x67, 0x07, 0x8e, 0xca, 0x07, 0x7b, 0xe5, 0x67,
	0xe6, 0x61, 0x45, 0x55, 0xd0, 0x1a, 0xac, 0x0c, 0x1c, 0x56, 0x6b, 0x7b, 0x2f, 0xd5, 0xd4, 0xd6,
	0x03, 0x80, 0xde, 0x7b, 0xcd, 0xe3, 0x20, 0xc4, 0xaa, 0xb5, 0x9d, 0xda, 0x71, 0xd5, 0x7c, 0xfc,
	0xe2, 0xb0, 0xb2, 0xaf, 0xce, 0xa0, 0x6b, 0xa0, 0xf6, 0xc3, 0x95, 0x3d, 0x55, 0xd9, 0x7a, 0x0a,
	0xd0, 0x7b, 0xa2, 0x79, 0x38, 0x7a, 0xbb, 0x38, 0xdc, 0xcb, 0xb0, 0xd8, 0xc3, 0x8e, 0x43, 0x57,
	0x46, 0xba, 0x07, 0xc5, 0x57, 0x54, 0x4d, 0x95, 0x7e, 0xcf, 0xc0, 0xf8, 0xdf, 0xfa, 0xe8, 0x6b,
	0x28, 0xf4, 0xff, 0xe4, 0x46, 0xef, 0x4d, 0xa8, 0xb5, 0x91, 0x7f, 0x0d, 0xac, 0xdf, 0x9d, 0x52,
	0x2b, 0xbe, 0x65, 0x67, 0x30, 0x1b, 0x7b, 0x8b, 0xb6, 0x27, 0x30, 0xf4, 0xf7, 0xc6, 0xf5, 0xe2,
	0x65, 0xc5, 0x63, 0x4b, 0xdf, 0x29, 0xa0, 0x0e, 0xb6, 0x2f, 0xf4, 0xfe, 0x64, 0xaf, 0x47, 0xb5,
	0xce, 0xf5, 0x7b, 0x53, 0xeb, 0xc5, 0x5e, 0xfc, 0xa6, 0xc0, 0xc6, 0xa4, 0x16, 0x84, 0x9e, 0x4c,
	0x66, 0xbf, 0x4c, 0x7f, 0x5c, 0xdf, 0x7f, 0x63, 0x1e, 0xe9, 0xf5, 0xee, 0xce, 0x67, 0x8f, 0xea,
	0xbe, 0x8b, 0xbd, 0x7a, 0xf1, 0x6e, 0x89, 0xb1, 0xa2, 0xe5, 0x37, 0x6f, 0x8b, 0x3f, 0x92, 0x2c,
	0xdf, 0xbd, 0x4d, 0x49, 0xd8, 0x72, 0x2c, 0x42, 0xc7, 0xff, 0xf1, 0x74, 0x9a, 0x13, 0x0a, 0x77,
	0xfe, 0x0e, 0x00, 0x00, 0xff, 0xff, 0x46, 0x20, 0xdb, 0xcc, 0xbe, 0x12, 0x00, 0x00,
}
