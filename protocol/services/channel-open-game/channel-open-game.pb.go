// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-open-game/channel-open-game.proto

package channel_open_game // import "golang.52tt.com/protocol/services/channel-open-game"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ChannelOpenGamePlayer struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Username             string   `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Sex                  uint32   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Openid               string   `protobuf:"bytes,6,opt,name=openid,proto3" json:"openid,omitempty"`
	Seq                  uint32   `protobuf:"varint,7,opt,name=seq,proto3" json:"seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelOpenGamePlayer) Reset()         { *m = ChannelOpenGamePlayer{} }
func (m *ChannelOpenGamePlayer) String() string { return proto.CompactTextString(m) }
func (*ChannelOpenGamePlayer) ProtoMessage()    {}
func (*ChannelOpenGamePlayer) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{0}
}
func (m *ChannelOpenGamePlayer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelOpenGamePlayer.Unmarshal(m, b)
}
func (m *ChannelOpenGamePlayer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelOpenGamePlayer.Marshal(b, m, deterministic)
}
func (dst *ChannelOpenGamePlayer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelOpenGamePlayer.Merge(dst, src)
}
func (m *ChannelOpenGamePlayer) XXX_Size() int {
	return xxx_messageInfo_ChannelOpenGamePlayer.Size(m)
}
func (m *ChannelOpenGamePlayer) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelOpenGamePlayer.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelOpenGamePlayer proto.InternalMessageInfo

func (m *ChannelOpenGamePlayer) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChannelOpenGamePlayer) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ChannelOpenGamePlayer) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *ChannelOpenGamePlayer) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ChannelOpenGamePlayer) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *ChannelOpenGamePlayer) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *ChannelOpenGamePlayer) GetSeq() uint32 {
	if m != nil {
		return m.Seq
	}
	return 0
}

type GameBaseInfo struct {
	CpId                 uint32   `protobuf:"varint,1,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,3,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	GameName             string   `protobuf:"bytes,4,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GamePic              string   `protobuf:"bytes,5,opt,name=game_pic,json=gamePic,proto3" json:"game_pic,omitempty"`
	GamePackage          string   `protobuf:"bytes,6,opt,name=game_package,json=gamePackage,proto3" json:"game_package,omitempty"`
	GameUrl              string   `protobuf:"bytes,7,opt,name=game_url,json=gameUrl,proto3" json:"game_url,omitempty"`
	EngineVer            uint32   `protobuf:"varint,8,opt,name=engine_ver,json=engineVer,proto3" json:"engine_ver,omitempty"`
	GameMemberCntLimit   uint32   `protobuf:"varint,9,opt,name=game_member_cnt_limit,json=gameMemberCntLimit,proto3" json:"game_member_cnt_limit,omitempty"`
	EngineType           uint32   `protobuf:"varint,10,opt,name=engine_type,json=engineType,proto3" json:"engine_type,omitempty"`
	GameAppLimit         []uint32 `protobuf:"varint,11,rep,packed,name=game_app_limit,json=gameAppLimit,proto3" json:"game_app_limit,omitempty"`
	GamePlatformLimit    []uint32 `protobuf:"varint,12,rep,packed,name=game_platform_limit,json=gamePlatformLimit,proto3" json:"game_platform_limit,omitempty"`
	CreateTime           uint32   `protobuf:"varint,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	GameResUrl           string   `protobuf:"bytes,14,opt,name=game_res_url,json=gameResUrl,proto3" json:"game_res_url,omitempty"`
	StartTime            uint32   `protobuf:"varint,15,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	GameExtraProperties  string   `protobuf:"bytes,16,opt,name=game_extra_properties,json=gameExtraProperties,proto3" json:"game_extra_properties,omitempty"`
	GameDigest           string   `protobuf:"bytes,17,opt,name=game_digest,json=gameDigest,proto3" json:"game_digest,omitempty"`
	GameResDigest        string   `protobuf:"bytes,18,opt,name=game_res_digest,json=gameResDigest,proto3" json:"game_res_digest,omitempty"`
	GameMode             string   `protobuf:"bytes,19,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	JsUrl                string   `protobuf:"bytes,20,opt,name=js_url,json=jsUrl,proto3" json:"js_url,omitempty"`
	JsDigest             string   `protobuf:"bytes,21,opt,name=js_digest,json=jsDigest,proto3" json:"js_digest,omitempty"`
	MainPackageUrl       string   `protobuf:"bytes,22,opt,name=main_package_url,json=mainPackageUrl,proto3" json:"main_package_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameBaseInfo) Reset()         { *m = GameBaseInfo{} }
func (m *GameBaseInfo) String() string { return proto.CompactTextString(m) }
func (*GameBaseInfo) ProtoMessage()    {}
func (*GameBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{1}
}
func (m *GameBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameBaseInfo.Unmarshal(m, b)
}
func (m *GameBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameBaseInfo.Marshal(b, m, deterministic)
}
func (dst *GameBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameBaseInfo.Merge(dst, src)
}
func (m *GameBaseInfo) XXX_Size() int {
	return xxx_messageInfo_GameBaseInfo.Size(m)
}
func (m *GameBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameBaseInfo proto.InternalMessageInfo

func (m *GameBaseInfo) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *GameBaseInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameBaseInfo) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *GameBaseInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameBaseInfo) GetGamePic() string {
	if m != nil {
		return m.GamePic
	}
	return ""
}

func (m *GameBaseInfo) GetGamePackage() string {
	if m != nil {
		return m.GamePackage
	}
	return ""
}

func (m *GameBaseInfo) GetGameUrl() string {
	if m != nil {
		return m.GameUrl
	}
	return ""
}

func (m *GameBaseInfo) GetEngineVer() uint32 {
	if m != nil {
		return m.EngineVer
	}
	return 0
}

func (m *GameBaseInfo) GetGameMemberCntLimit() uint32 {
	if m != nil {
		return m.GameMemberCntLimit
	}
	return 0
}

func (m *GameBaseInfo) GetEngineType() uint32 {
	if m != nil {
		return m.EngineType
	}
	return 0
}

func (m *GameBaseInfo) GetGameAppLimit() []uint32 {
	if m != nil {
		return m.GameAppLimit
	}
	return nil
}

func (m *GameBaseInfo) GetGamePlatformLimit() []uint32 {
	if m != nil {
		return m.GamePlatformLimit
	}
	return nil
}

func (m *GameBaseInfo) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GameBaseInfo) GetGameResUrl() string {
	if m != nil {
		return m.GameResUrl
	}
	return ""
}

func (m *GameBaseInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GameBaseInfo) GetGameExtraProperties() string {
	if m != nil {
		return m.GameExtraProperties
	}
	return ""
}

func (m *GameBaseInfo) GetGameDigest() string {
	if m != nil {
		return m.GameDigest
	}
	return ""
}

func (m *GameBaseInfo) GetGameResDigest() string {
	if m != nil {
		return m.GameResDigest
	}
	return ""
}

func (m *GameBaseInfo) GetGameMode() string {
	if m != nil {
		return m.GameMode
	}
	return ""
}

func (m *GameBaseInfo) GetJsUrl() string {
	if m != nil {
		return m.JsUrl
	}
	return ""
}

func (m *GameBaseInfo) GetJsDigest() string {
	if m != nil {
		return m.JsDigest
	}
	return ""
}

func (m *GameBaseInfo) GetMainPackageUrl() string {
	if m != nil {
		return m.MainPackageUrl
	}
	return ""
}

type ChannelGameMaintain struct {
	MaintainBegin        int64    `protobuf:"varint,1,opt,name=maintain_begin,json=maintainBegin,proto3" json:"maintain_begin,omitempty"`
	MaintainEnd          int64    `protobuf:"varint,2,opt,name=maintain_end,json=maintainEnd,proto3" json:"maintain_end,omitempty"`
	MaintainText         string   `protobuf:"bytes,3,opt,name=maintain_text,json=maintainText,proto3" json:"maintain_text,omitempty"`
	MaintainKey          int64    `protobuf:"varint,4,opt,name=maintain_key,json=maintainKey,proto3" json:"maintain_key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGameMaintain) Reset()         { *m = ChannelGameMaintain{} }
func (m *ChannelGameMaintain) String() string { return proto.CompactTextString(m) }
func (*ChannelGameMaintain) ProtoMessage()    {}
func (*ChannelGameMaintain) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{2}
}
func (m *ChannelGameMaintain) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameMaintain.Unmarshal(m, b)
}
func (m *ChannelGameMaintain) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameMaintain.Marshal(b, m, deterministic)
}
func (dst *ChannelGameMaintain) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameMaintain.Merge(dst, src)
}
func (m *ChannelGameMaintain) XXX_Size() int {
	return xxx_messageInfo_ChannelGameMaintain.Size(m)
}
func (m *ChannelGameMaintain) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameMaintain.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameMaintain proto.InternalMessageInfo

func (m *ChannelGameMaintain) GetMaintainBegin() int64 {
	if m != nil {
		return m.MaintainBegin
	}
	return 0
}

func (m *ChannelGameMaintain) GetMaintainEnd() int64 {
	if m != nil {
		return m.MaintainEnd
	}
	return 0
}

func (m *ChannelGameMaintain) GetMaintainText() string {
	if m != nil {
		return m.MaintainText
	}
	return ""
}

func (m *ChannelGameMaintain) GetMaintainKey() int64 {
	if m != nil {
		return m.MaintainKey
	}
	return 0
}

type ChannelGameMode struct {
	ModeKey              string   `protobuf:"bytes,1,opt,name=mode_key,json=modeKey,proto3" json:"mode_key,omitempty"`
	GameParam            string   `protobuf:"bytes,2,opt,name=game_param,json=gameParam,proto3" json:"game_param,omitempty"`
	PlayerLimit          uint32   `protobuf:"varint,3,opt,name=player_limit,json=playerLimit,proto3" json:"player_limit,omitempty"`
	PlayerLimitList      []uint32 `protobuf:"varint,4,rep,packed,name=player_limit_list,json=playerLimitList,proto3" json:"player_limit_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelGameMode) Reset()         { *m = ChannelGameMode{} }
func (m *ChannelGameMode) String() string { return proto.CompactTextString(m) }
func (*ChannelGameMode) ProtoMessage()    {}
func (*ChannelGameMode) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{3}
}
func (m *ChannelGameMode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelGameMode.Unmarshal(m, b)
}
func (m *ChannelGameMode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelGameMode.Marshal(b, m, deterministic)
}
func (dst *ChannelGameMode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelGameMode.Merge(dst, src)
}
func (m *ChannelGameMode) XXX_Size() int {
	return xxx_messageInfo_ChannelGameMode.Size(m)
}
func (m *ChannelGameMode) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelGameMode.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelGameMode proto.InternalMessageInfo

func (m *ChannelGameMode) GetModeKey() string {
	if m != nil {
		return m.ModeKey
	}
	return ""
}

func (m *ChannelGameMode) GetGameParam() string {
	if m != nil {
		return m.GameParam
	}
	return ""
}

func (m *ChannelGameMode) GetPlayerLimit() uint32 {
	if m != nil {
		return m.PlayerLimit
	}
	return 0
}

func (m *ChannelGameMode) GetPlayerLimitList() []uint32 {
	if m != nil {
		return m.PlayerLimitList
	}
	return nil
}

type ChannelOpenGameInfo struct {
	Base                 *GameBaseInfo            `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	GameStatus           uint32                   `protobuf:"varint,2,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	GameMaster           uint32                   `protobuf:"varint,3,opt,name=game_master,json=gameMaster,proto3" json:"game_master,omitempty"`
	GameMemberCnt        uint32                   `protobuf:"varint,4,opt,name=game_member_cnt,json=gameMemberCnt,proto3" json:"game_member_cnt,omitempty"`
	GamePlayers          []*ChannelOpenGamePlayer `protobuf:"bytes,5,rep,name=game_players,json=gamePlayers,proto3" json:"game_players,omitempty"`
	GameInitiator        uint32                   `protobuf:"varint,6,opt,name=game_initiator,json=gameInitiator,proto3" json:"game_initiator,omitempty"`
	GameCurrentRound     string                   `protobuf:"bytes,7,opt,name=game_current_round,json=gameCurrentRound,proto3" json:"game_current_round,omitempty"`
	IsNewPlayer          bool                     `protobuf:"varint,8,opt,name=is_new_player,json=isNewPlayer,proto3" json:"is_new_player,omitempty"`
	GameMode             *ChannelGameMode         `protobuf:"bytes,9,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	GameMaintain         *ChannelGameMaintain     `protobuf:"bytes,10,opt,name=game_maintain,json=gameMaintain,proto3" json:"game_maintain,omitempty"`
	LoadSeq              int64                    `protobuf:"varint,11,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ChannelOpenGameInfo) Reset()         { *m = ChannelOpenGameInfo{} }
func (m *ChannelOpenGameInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelOpenGameInfo) ProtoMessage()    {}
func (*ChannelOpenGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{4}
}
func (m *ChannelOpenGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelOpenGameInfo.Unmarshal(m, b)
}
func (m *ChannelOpenGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelOpenGameInfo.Marshal(b, m, deterministic)
}
func (dst *ChannelOpenGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelOpenGameInfo.Merge(dst, src)
}
func (m *ChannelOpenGameInfo) XXX_Size() int {
	return xxx_messageInfo_ChannelOpenGameInfo.Size(m)
}
func (m *ChannelOpenGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelOpenGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelOpenGameInfo proto.InternalMessageInfo

func (m *ChannelOpenGameInfo) GetBase() *GameBaseInfo {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *ChannelOpenGameInfo) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

func (m *ChannelOpenGameInfo) GetGameMaster() uint32 {
	if m != nil {
		return m.GameMaster
	}
	return 0
}

func (m *ChannelOpenGameInfo) GetGameMemberCnt() uint32 {
	if m != nil {
		return m.GameMemberCnt
	}
	return 0
}

func (m *ChannelOpenGameInfo) GetGamePlayers() []*ChannelOpenGamePlayer {
	if m != nil {
		return m.GamePlayers
	}
	return nil
}

func (m *ChannelOpenGameInfo) GetGameInitiator() uint32 {
	if m != nil {
		return m.GameInitiator
	}
	return 0
}

func (m *ChannelOpenGameInfo) GetGameCurrentRound() string {
	if m != nil {
		return m.GameCurrentRound
	}
	return ""
}

func (m *ChannelOpenGameInfo) GetIsNewPlayer() bool {
	if m != nil {
		return m.IsNewPlayer
	}
	return false
}

func (m *ChannelOpenGameInfo) GetGameMode() *ChannelGameMode {
	if m != nil {
		return m.GameMode
	}
	return nil
}

func (m *ChannelOpenGameInfo) GetGameMaintain() *ChannelGameMaintain {
	if m != nil {
		return m.GameMaintain
	}
	return nil
}

func (m *ChannelOpenGameInfo) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type GetSupportGameListReq struct {
	CpId                 uint32   `protobuf:"varint,1,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	AppId                uint32   `protobuf:"varint,2,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Platform             uint32   `protobuf:"varint,3,opt,name=platform,proto3" json:"platform,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSupportGameListReq) Reset()         { *m = GetSupportGameListReq{} }
func (m *GetSupportGameListReq) String() string { return proto.CompactTextString(m) }
func (*GetSupportGameListReq) ProtoMessage()    {}
func (*GetSupportGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{5}
}
func (m *GetSupportGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupportGameListReq.Unmarshal(m, b)
}
func (m *GetSupportGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupportGameListReq.Marshal(b, m, deterministic)
}
func (dst *GetSupportGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupportGameListReq.Merge(dst, src)
}
func (m *GetSupportGameListReq) XXX_Size() int {
	return xxx_messageInfo_GetSupportGameListReq.Size(m)
}
func (m *GetSupportGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupportGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupportGameListReq proto.InternalMessageInfo

func (m *GetSupportGameListReq) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *GetSupportGameListReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetSupportGameListReq) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

type GetSupportGameListResp struct {
	Base                 []*GameBaseInfo `protobuf:"bytes,1,rep,name=base,proto3" json:"base,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetSupportGameListResp) Reset()         { *m = GetSupportGameListResp{} }
func (m *GetSupportGameListResp) String() string { return proto.CompactTextString(m) }
func (*GetSupportGameListResp) ProtoMessage()    {}
func (*GetSupportGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{6}
}
func (m *GetSupportGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSupportGameListResp.Unmarshal(m, b)
}
func (m *GetSupportGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSupportGameListResp.Marshal(b, m, deterministic)
}
func (dst *GetSupportGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSupportGameListResp.Merge(dst, src)
}
func (m *GetSupportGameListResp) XXX_Size() int {
	return xxx_messageInfo_GetSupportGameListResp.Size(m)
}
func (m *GetSupportGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSupportGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSupportGameListResp proto.InternalMessageInfo

func (m *GetSupportGameListResp) GetBase() []*GameBaseInfo {
	if m != nil {
		return m.Base
	}
	return nil
}

type GetGameMaintainReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,2,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameMaintainReq) Reset()         { *m = GetGameMaintainReq{} }
func (m *GetGameMaintainReq) String() string { return proto.CompactTextString(m) }
func (*GetGameMaintainReq) ProtoMessage()    {}
func (*GetGameMaintainReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{7}
}
func (m *GetGameMaintainReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameMaintainReq.Unmarshal(m, b)
}
func (m *GetGameMaintainReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameMaintainReq.Marshal(b, m, deterministic)
}
func (dst *GetGameMaintainReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameMaintainReq.Merge(dst, src)
}
func (m *GetGameMaintainReq) XXX_Size() int {
	return xxx_messageInfo_GetGameMaintainReq.Size(m)
}
func (m *GetGameMaintainReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameMaintainReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameMaintainReq proto.InternalMessageInfo

func (m *GetGameMaintainReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameMaintainReq) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

type GetGameMaintainResp struct {
	GameMaintain         *ChannelGameMaintain `protobuf:"bytes,1,opt,name=game_maintain,json=gameMaintain,proto3" json:"game_maintain,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetGameMaintainResp) Reset()         { *m = GetGameMaintainResp{} }
func (m *GetGameMaintainResp) String() string { return proto.CompactTextString(m) }
func (*GetGameMaintainResp) ProtoMessage()    {}
func (*GetGameMaintainResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{8}
}
func (m *GetGameMaintainResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameMaintainResp.Unmarshal(m, b)
}
func (m *GetGameMaintainResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameMaintainResp.Marshal(b, m, deterministic)
}
func (dst *GetGameMaintainResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameMaintainResp.Merge(dst, src)
}
func (m *GetGameMaintainResp) XXX_Size() int {
	return xxx_messageInfo_GetGameMaintainResp.Size(m)
}
func (m *GetGameMaintainResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameMaintainResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameMaintainResp proto.InternalMessageInfo

func (m *GetGameMaintainResp) GetGameMaintain() *ChannelGameMaintain {
	if m != nil {
		return m.GameMaintain
	}
	return nil
}

type BatchGetGameInfoReq struct {
	GameId               []uint32 `protobuf:"varint,1,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGameInfoReq) Reset()         { *m = BatchGetGameInfoReq{} }
func (m *BatchGetGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameInfoReq) ProtoMessage()    {}
func (*BatchGetGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{9}
}
func (m *BatchGetGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameInfoReq.Unmarshal(m, b)
}
func (m *BatchGetGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameInfoReq.Merge(dst, src)
}
func (m *BatchGetGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameInfoReq.Size(m)
}
func (m *BatchGetGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameInfoReq proto.InternalMessageInfo

func (m *BatchGetGameInfoReq) GetGameId() []uint32 {
	if m != nil {
		return m.GameId
	}
	return nil
}

type BatchGetGameInfoResp struct {
	Base                 []*GameBaseInfo `protobuf:"bytes,1,rep,name=base,proto3" json:"base,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetGameInfoResp) Reset()         { *m = BatchGetGameInfoResp{} }
func (m *BatchGetGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetGameInfoResp) ProtoMessage()    {}
func (*BatchGetGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{10}
}
func (m *BatchGetGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGameInfoResp.Unmarshal(m, b)
}
func (m *BatchGetGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGameInfoResp.Merge(dst, src)
}
func (m *BatchGetGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetGameInfoResp.Size(m)
}
func (m *BatchGetGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGameInfoResp proto.InternalMessageInfo

func (m *BatchGetGameInfoResp) GetBase() []*GameBaseInfo {
	if m != nil {
		return m.Base
	}
	return nil
}

type SetChannelGameReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,4,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	GameInitiator        uint32   `protobuf:"varint,5,opt,name=game_initiator,json=gameInitiator,proto3" json:"game_initiator,omitempty"`
	GameMode             string   `protobuf:"bytes,6,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameReq) Reset()         { *m = SetChannelGameReq{} }
func (m *SetChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameReq) ProtoMessage()    {}
func (*SetChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{11}
}
func (m *SetChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameReq.Unmarshal(m, b)
}
func (m *SetChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameReq.Merge(dst, src)
}
func (m *SetChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameReq.Size(m)
}
func (m *SetChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameReq proto.InternalMessageInfo

func (m *SetChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameReq) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *SetChannelGameReq) GetGameInitiator() uint32 {
	if m != nil {
		return m.GameInitiator
	}
	return 0
}

func (m *SetChannelGameReq) GetGameMode() string {
	if m != nil {
		return m.GameMode
	}
	return ""
}

type SetChannelGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameResp) Reset()         { *m = SetChannelGameResp{} }
func (m *SetChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameResp) ProtoMessage()    {}
func (*SetChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{12}
}
func (m *SetChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameResp.Unmarshal(m, b)
}
func (m *SetChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameResp.Merge(dst, src)
}
func (m *SetChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameResp.Size(m)
}
func (m *SetChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameResp proto.InternalMessageInfo

type SetChannelGameSyncReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,4,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	GameInitiator        uint32   `protobuf:"varint,5,opt,name=game_initiator,json=gameInitiator,proto3" json:"game_initiator,omitempty"`
	GameMode             string   `protobuf:"bytes,6,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameSyncReq) Reset()         { *m = SetChannelGameSyncReq{} }
func (m *SetChannelGameSyncReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameSyncReq) ProtoMessage()    {}
func (*SetChannelGameSyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{13}
}
func (m *SetChannelGameSyncReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameSyncReq.Unmarshal(m, b)
}
func (m *SetChannelGameSyncReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameSyncReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameSyncReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameSyncReq.Merge(dst, src)
}
func (m *SetChannelGameSyncReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameSyncReq.Size(m)
}
func (m *SetChannelGameSyncReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameSyncReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameSyncReq proto.InternalMessageInfo

func (m *SetChannelGameSyncReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameSyncReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameSyncReq) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *SetChannelGameSyncReq) GetGameInitiator() uint32 {
	if m != nil {
		return m.GameInitiator
	}
	return 0
}

func (m *SetChannelGameSyncReq) GetGameMode() string {
	if m != nil {
		return m.GameMode
	}
	return ""
}

type SetChannelGameSyncResp struct {
	GameVersion          string           `protobuf:"bytes,1,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	Info                 *ChannelGameMode `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	LoadSeq              int64            `protobuf:"varint,3,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *SetChannelGameSyncResp) Reset()         { *m = SetChannelGameSyncResp{} }
func (m *SetChannelGameSyncResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameSyncResp) ProtoMessage()    {}
func (*SetChannelGameSyncResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{14}
}
func (m *SetChannelGameSyncResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameSyncResp.Unmarshal(m, b)
}
func (m *SetChannelGameSyncResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameSyncResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameSyncResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameSyncResp.Merge(dst, src)
}
func (m *SetChannelGameSyncResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameSyncResp.Size(m)
}
func (m *SetChannelGameSyncResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameSyncResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameSyncResp proto.InternalMessageInfo

func (m *SetChannelGameSyncResp) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *SetChannelGameSyncResp) GetInfo() *ChannelGameMode {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *SetChannelGameSyncResp) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type SetChannelGameAsyncReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,4,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	GameInitiator        uint32   `protobuf:"varint,5,opt,name=game_initiator,json=gameInitiator,proto3" json:"game_initiator,omitempty"`
	GameMode             string   `protobuf:"bytes,6,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameAsyncReq) Reset()         { *m = SetChannelGameAsyncReq{} }
func (m *SetChannelGameAsyncReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameAsyncReq) ProtoMessage()    {}
func (*SetChannelGameAsyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{15}
}
func (m *SetChannelGameAsyncReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameAsyncReq.Unmarshal(m, b)
}
func (m *SetChannelGameAsyncReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameAsyncReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameAsyncReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameAsyncReq.Merge(dst, src)
}
func (m *SetChannelGameAsyncReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameAsyncReq.Size(m)
}
func (m *SetChannelGameAsyncReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameAsyncReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameAsyncReq proto.InternalMessageInfo

func (m *SetChannelGameAsyncReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameAsyncReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameAsyncReq) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *SetChannelGameAsyncReq) GetGameInitiator() uint32 {
	if m != nil {
		return m.GameInitiator
	}
	return 0
}

func (m *SetChannelGameAsyncReq) GetGameMode() string {
	if m != nil {
		return m.GameMode
	}
	return ""
}

type SetChannelGameAsyncResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameAsyncResp) Reset()         { *m = SetChannelGameAsyncResp{} }
func (m *SetChannelGameAsyncResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameAsyncResp) ProtoMessage()    {}
func (*SetChannelGameAsyncResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{16}
}
func (m *SetChannelGameAsyncResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameAsyncResp.Unmarshal(m, b)
}
func (m *SetChannelGameAsyncResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameAsyncResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameAsyncResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameAsyncResp.Merge(dst, src)
}
func (m *SetChannelGameAsyncResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameAsyncResp.Size(m)
}
func (m *SetChannelGameAsyncResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameAsyncResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameAsyncResp proto.InternalMessageInfo

type UpdateChannelGameReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelGameReq) Reset()         { *m = UpdateChannelGameReq{} }
func (m *UpdateChannelGameReq) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelGameReq) ProtoMessage()    {}
func (*UpdateChannelGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{17}
}
func (m *UpdateChannelGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelGameReq.Unmarshal(m, b)
}
func (m *UpdateChannelGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelGameReq.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelGameReq.Merge(dst, src)
}
func (m *UpdateChannelGameReq) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelGameReq.Size(m)
}
func (m *UpdateChannelGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelGameReq proto.InternalMessageInfo

func (m *UpdateChannelGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type UpdateChannelGameResp struct {
	LoadSeq              int64    `protobuf:"varint,1,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateChannelGameResp) Reset()         { *m = UpdateChannelGameResp{} }
func (m *UpdateChannelGameResp) String() string { return proto.CompactTextString(m) }
func (*UpdateChannelGameResp) ProtoMessage()    {}
func (*UpdateChannelGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{18}
}
func (m *UpdateChannelGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateChannelGameResp.Unmarshal(m, b)
}
func (m *UpdateChannelGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateChannelGameResp.Marshal(b, m, deterministic)
}
func (dst *UpdateChannelGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateChannelGameResp.Merge(dst, src)
}
func (m *UpdateChannelGameResp) XXX_Size() int {
	return xxx_messageInfo_UpdateChannelGameResp.Size(m)
}
func (m *UpdateChannelGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateChannelGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateChannelGameResp proto.InternalMessageInfo

func (m *UpdateChannelGameResp) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

type SetChannelGameMasterReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameMaster           uint32   `protobuf:"varint,3,opt,name=game_master,json=gameMaster,proto3" json:"game_master,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameMasterReq) Reset()         { *m = SetChannelGameMasterReq{} }
func (m *SetChannelGameMasterReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameMasterReq) ProtoMessage()    {}
func (*SetChannelGameMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{19}
}
func (m *SetChannelGameMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameMasterReq.Unmarshal(m, b)
}
func (m *SetChannelGameMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameMasterReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameMasterReq.Merge(dst, src)
}
func (m *SetChannelGameMasterReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameMasterReq.Size(m)
}
func (m *SetChannelGameMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameMasterReq proto.InternalMessageInfo

func (m *SetChannelGameMasterReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameMasterReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameMasterReq) GetGameMaster() uint32 {
	if m != nil {
		return m.GameMaster
	}
	return 0
}

type SetChannelGameMasterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameMasterResp) Reset()         { *m = SetChannelGameMasterResp{} }
func (m *SetChannelGameMasterResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameMasterResp) ProtoMessage()    {}
func (*SetChannelGameMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{20}
}
func (m *SetChannelGameMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameMasterResp.Unmarshal(m, b)
}
func (m *SetChannelGameMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameMasterResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameMasterResp.Merge(dst, src)
}
func (m *SetChannelGameMasterResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameMasterResp.Size(m)
}
func (m *SetChannelGameMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameMasterResp proto.InternalMessageInfo

type SetChannelGameInfoReq struct {
	ChannelId            uint32               `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Info                 *ChannelOpenGameInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SetChannelGameInfoReq) Reset()         { *m = SetChannelGameInfoReq{} }
func (m *SetChannelGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameInfoReq) ProtoMessage()    {}
func (*SetChannelGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{21}
}
func (m *SetChannelGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameInfoReq.Unmarshal(m, b)
}
func (m *SetChannelGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameInfoReq.Merge(dst, src)
}
func (m *SetChannelGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameInfoReq.Size(m)
}
func (m *SetChannelGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameInfoReq proto.InternalMessageInfo

func (m *SetChannelGameInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameInfoReq) GetInfo() *ChannelOpenGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetChannelGameInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameInfoResp) Reset()         { *m = SetChannelGameInfoResp{} }
func (m *SetChannelGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameInfoResp) ProtoMessage()    {}
func (*SetChannelGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{22}
}
func (m *SetChannelGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameInfoResp.Unmarshal(m, b)
}
func (m *SetChannelGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameInfoResp.Merge(dst, src)
}
func (m *SetChannelGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameInfoResp.Size(m)
}
func (m *SetChannelGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameInfoResp proto.InternalMessageInfo

type GetChannelGameInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelGameInfoReq) Reset()         { *m = GetChannelGameInfoReq{} }
func (m *GetChannelGameInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameInfoReq) ProtoMessage()    {}
func (*GetChannelGameInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{23}
}
func (m *GetChannelGameInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameInfoReq.Unmarshal(m, b)
}
func (m *GetChannelGameInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameInfoReq.Merge(dst, src)
}
func (m *GetChannelGameInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameInfoReq.Size(m)
}
func (m *GetChannelGameInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameInfoReq proto.InternalMessageInfo

func (m *GetChannelGameInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelGameInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelGameInfoResp struct {
	Info                 *ChannelOpenGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetChannelGameInfoResp) Reset()         { *m = GetChannelGameInfoResp{} }
func (m *GetChannelGameInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameInfoResp) ProtoMessage()    {}
func (*GetChannelGameInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{24}
}
func (m *GetChannelGameInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameInfoResp.Unmarshal(m, b)
}
func (m *GetChannelGameInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameInfoResp.Merge(dst, src)
}
func (m *GetChannelGameInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameInfoResp.Size(m)
}
func (m *GetChannelGameInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameInfoResp proto.InternalMessageInfo

func (m *GetChannelGameInfoResp) GetInfo() *ChannelOpenGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetGameModeInfoReq struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,2,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	GameMode             string   `protobuf:"bytes,3,opt,name=game_mode,json=gameMode,proto3" json:"game_mode,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameModeInfoReq) Reset()         { *m = GetGameModeInfoReq{} }
func (m *GetGameModeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGameModeInfoReq) ProtoMessage()    {}
func (*GetGameModeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{25}
}
func (m *GetGameModeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameModeInfoReq.Unmarshal(m, b)
}
func (m *GetGameModeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameModeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGameModeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameModeInfoReq.Merge(dst, src)
}
func (m *GetGameModeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGameModeInfoReq.Size(m)
}
func (m *GetGameModeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameModeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameModeInfoReq proto.InternalMessageInfo

func (m *GetGameModeInfoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetGameModeInfoReq) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *GetGameModeInfoReq) GetGameMode() string {
	if m != nil {
		return m.GameMode
	}
	return ""
}

type GetGameModeInfoResp struct {
	Info                 *ChannelGameMode `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetGameModeInfoResp) Reset()         { *m = GetGameModeInfoResp{} }
func (m *GetGameModeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGameModeInfoResp) ProtoMessage()    {}
func (*GetGameModeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{26}
}
func (m *GetGameModeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameModeInfoResp.Unmarshal(m, b)
}
func (m *GetGameModeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameModeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGameModeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameModeInfoResp.Merge(dst, src)
}
func (m *GetGameModeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGameModeInfoResp.Size(m)
}
func (m *GetGameModeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameModeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameModeInfoResp proto.InternalMessageInfo

func (m *GetGameModeInfoResp) GetInfo() *ChannelGameMode {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetChannelGameStatusReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameStatus           uint32   `protobuf:"varint,3,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameStatusReq) Reset()         { *m = SetChannelGameStatusReq{} }
func (m *SetChannelGameStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameStatusReq) ProtoMessage()    {}
func (*SetChannelGameStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{27}
}
func (m *SetChannelGameStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameStatusReq.Unmarshal(m, b)
}
func (m *SetChannelGameStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameStatusReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameStatusReq.Merge(dst, src)
}
func (m *SetChannelGameStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameStatusReq.Size(m)
}
func (m *SetChannelGameStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameStatusReq proto.InternalMessageInfo

func (m *SetChannelGameStatusReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameStatusReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameStatusReq) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

type SetChannelGameStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameStatusResp) Reset()         { *m = SetChannelGameStatusResp{} }
func (m *SetChannelGameStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameStatusResp) ProtoMessage()    {}
func (*SetChannelGameStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{28}
}
func (m *SetChannelGameStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameStatusResp.Unmarshal(m, b)
}
func (m *SetChannelGameStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameStatusResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameStatusResp.Merge(dst, src)
}
func (m *SetChannelGameStatusResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameStatusResp.Size(m)
}
func (m *SetChannelGameStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameStatusResp proto.InternalMessageInfo

type SetChannelGameMemberReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameMemberCnt        uint32   `protobuf:"varint,3,opt,name=game_member_cnt,json=gameMemberCnt,proto3" json:"game_member_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameMemberReq) Reset()         { *m = SetChannelGameMemberReq{} }
func (m *SetChannelGameMemberReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameMemberReq) ProtoMessage()    {}
func (*SetChannelGameMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{29}
}
func (m *SetChannelGameMemberReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameMemberReq.Unmarshal(m, b)
}
func (m *SetChannelGameMemberReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameMemberReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameMemberReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameMemberReq.Merge(dst, src)
}
func (m *SetChannelGameMemberReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameMemberReq.Size(m)
}
func (m *SetChannelGameMemberReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameMemberReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameMemberReq proto.InternalMessageInfo

func (m *SetChannelGameMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGameMemberReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGameMemberReq) GetGameMemberCnt() uint32 {
	if m != nil {
		return m.GameMemberCnt
	}
	return 0
}

type SetChannelGameMemberResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGameMemberResp) Reset()         { *m = SetChannelGameMemberResp{} }
func (m *SetChannelGameMemberResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGameMemberResp) ProtoMessage()    {}
func (*SetChannelGameMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{30}
}
func (m *SetChannelGameMemberResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGameMemberResp.Unmarshal(m, b)
}
func (m *SetChannelGameMemberResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGameMemberResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGameMemberResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGameMemberResp.Merge(dst, src)
}
func (m *SetChannelGameMemberResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGameMemberResp.Size(m)
}
func (m *SetChannelGameMemberResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGameMemberResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGameMemberResp proto.InternalMessageInfo

type SetChannelGamePlayerOpenIdReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	Openids              []string `protobuf:"bytes,4,rep,name=openids,proto3" json:"openids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGamePlayerOpenIdReq) Reset()         { *m = SetChannelGamePlayerOpenIdReq{} }
func (m *SetChannelGamePlayerOpenIdReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerOpenIdReq) ProtoMessage()    {}
func (*SetChannelGamePlayerOpenIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{31}
}
func (m *SetChannelGamePlayerOpenIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerOpenIdReq.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerOpenIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerOpenIdReq.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerOpenIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerOpenIdReq.Merge(dst, src)
}
func (m *SetChannelGamePlayerOpenIdReq) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerOpenIdReq.Size(m)
}
func (m *SetChannelGamePlayerOpenIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerOpenIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerOpenIdReq proto.InternalMessageInfo

func (m *SetChannelGamePlayerOpenIdReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetChannelGamePlayerOpenIdReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *SetChannelGamePlayerOpenIdReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

func (m *SetChannelGamePlayerOpenIdReq) GetOpenids() []string {
	if m != nil {
		return m.Openids
	}
	return nil
}

type SetChannelGamePlayerOpenIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetChannelGamePlayerOpenIdResp) Reset()         { *m = SetChannelGamePlayerOpenIdResp{} }
func (m *SetChannelGamePlayerOpenIdResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelGamePlayerOpenIdResp) ProtoMessage()    {}
func (*SetChannelGamePlayerOpenIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{32}
}
func (m *SetChannelGamePlayerOpenIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetChannelGamePlayerOpenIdResp.Unmarshal(m, b)
}
func (m *SetChannelGamePlayerOpenIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetChannelGamePlayerOpenIdResp.Marshal(b, m, deterministic)
}
func (dst *SetChannelGamePlayerOpenIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetChannelGamePlayerOpenIdResp.Merge(dst, src)
}
func (m *SetChannelGamePlayerOpenIdResp) XXX_Size() int {
	return xxx_messageInfo_SetChannelGamePlayerOpenIdResp.Size(m)
}
func (m *SetChannelGamePlayerOpenIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetChannelGamePlayerOpenIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetChannelGamePlayerOpenIdResp proto.InternalMessageInfo

type GetChannelGameBaseInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelGameBaseInfoReq) Reset()         { *m = GetChannelGameBaseInfoReq{} }
func (m *GetChannelGameBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameBaseInfoReq) ProtoMessage()    {}
func (*GetChannelGameBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{33}
}
func (m *GetChannelGameBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameBaseInfoReq.Unmarshal(m, b)
}
func (m *GetChannelGameBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameBaseInfoReq.Merge(dst, src)
}
func (m *GetChannelGameBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameBaseInfoReq.Size(m)
}
func (m *GetChannelGameBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameBaseInfoReq proto.InternalMessageInfo

func (m *GetChannelGameBaseInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetChannelGameBaseInfoResp struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GameVersion          string   `protobuf:"bytes,2,opt,name=game_version,json=gameVersion,proto3" json:"game_version,omitempty"`
	GameName             string   `protobuf:"bytes,3,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	GameMemberCntLimit   uint32   `protobuf:"varint,4,opt,name=game_member_cnt_limit,json=gameMemberCntLimit,proto3" json:"game_member_cnt_limit,omitempty"`
	CpId                 uint32   `protobuf:"varint,5,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	GameModeInfo         string   `protobuf:"bytes,6,opt,name=game_mode_info,json=gameModeInfo,proto3" json:"game_mode_info,omitempty"`
	Mode                 string   `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`
	LoadSeq              int64    `protobuf:"varint,8,opt,name=load_seq,json=loadSeq,proto3" json:"load_seq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelGameBaseInfoResp) Reset()         { *m = GetChannelGameBaseInfoResp{} }
func (m *GetChannelGameBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelGameBaseInfoResp) ProtoMessage()    {}
func (*GetChannelGameBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_open_game_8fb924251e7b0ad7, []int{34}
}
func (m *GetChannelGameBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelGameBaseInfoResp.Unmarshal(m, b)
}
func (m *GetChannelGameBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelGameBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelGameBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelGameBaseInfoResp.Merge(dst, src)
}
func (m *GetChannelGameBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelGameBaseInfoResp.Size(m)
}
func (m *GetChannelGameBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelGameBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelGameBaseInfoResp proto.InternalMessageInfo

func (m *GetChannelGameBaseInfoResp) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetChannelGameBaseInfoResp) GetGameVersion() string {
	if m != nil {
		return m.GameVersion
	}
	return ""
}

func (m *GetChannelGameBaseInfoResp) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GetChannelGameBaseInfoResp) GetGameMemberCntLimit() uint32 {
	if m != nil {
		return m.GameMemberCntLimit
	}
	return 0
}

func (m *GetChannelGameBaseInfoResp) GetCpId() uint32 {
	if m != nil {
		return m.CpId
	}
	return 0
}

func (m *GetChannelGameBaseInfoResp) GetGameModeInfo() string {
	if m != nil {
		return m.GameModeInfo
	}
	return ""
}

func (m *GetChannelGameBaseInfoResp) GetMode() string {
	if m != nil {
		return m.Mode
	}
	return ""
}

func (m *GetChannelGameBaseInfoResp) GetLoadSeq() int64 {
	if m != nil {
		return m.LoadSeq
	}
	return 0
}

func init() {
	proto.RegisterType((*ChannelOpenGamePlayer)(nil), "channel_open_game.ChannelOpenGamePlayer")
	proto.RegisterType((*GameBaseInfo)(nil), "channel_open_game.GameBaseInfo")
	proto.RegisterType((*ChannelGameMaintain)(nil), "channel_open_game.ChannelGameMaintain")
	proto.RegisterType((*ChannelGameMode)(nil), "channel_open_game.ChannelGameMode")
	proto.RegisterType((*ChannelOpenGameInfo)(nil), "channel_open_game.ChannelOpenGameInfo")
	proto.RegisterType((*GetSupportGameListReq)(nil), "channel_open_game.GetSupportGameListReq")
	proto.RegisterType((*GetSupportGameListResp)(nil), "channel_open_game.GetSupportGameListResp")
	proto.RegisterType((*GetGameMaintainReq)(nil), "channel_open_game.GetGameMaintainReq")
	proto.RegisterType((*GetGameMaintainResp)(nil), "channel_open_game.GetGameMaintainResp")
	proto.RegisterType((*BatchGetGameInfoReq)(nil), "channel_open_game.BatchGetGameInfoReq")
	proto.RegisterType((*BatchGetGameInfoResp)(nil), "channel_open_game.BatchGetGameInfoResp")
	proto.RegisterType((*SetChannelGameReq)(nil), "channel_open_game.SetChannelGameReq")
	proto.RegisterType((*SetChannelGameResp)(nil), "channel_open_game.SetChannelGameResp")
	proto.RegisterType((*SetChannelGameSyncReq)(nil), "channel_open_game.SetChannelGameSyncReq")
	proto.RegisterType((*SetChannelGameSyncResp)(nil), "channel_open_game.SetChannelGameSyncResp")
	proto.RegisterType((*SetChannelGameAsyncReq)(nil), "channel_open_game.SetChannelGameAsyncReq")
	proto.RegisterType((*SetChannelGameAsyncResp)(nil), "channel_open_game.SetChannelGameAsyncResp")
	proto.RegisterType((*UpdateChannelGameReq)(nil), "channel_open_game.UpdateChannelGameReq")
	proto.RegisterType((*UpdateChannelGameResp)(nil), "channel_open_game.UpdateChannelGameResp")
	proto.RegisterType((*SetChannelGameMasterReq)(nil), "channel_open_game.SetChannelGameMasterReq")
	proto.RegisterType((*SetChannelGameMasterResp)(nil), "channel_open_game.SetChannelGameMasterResp")
	proto.RegisterType((*SetChannelGameInfoReq)(nil), "channel_open_game.SetChannelGameInfoReq")
	proto.RegisterType((*SetChannelGameInfoResp)(nil), "channel_open_game.SetChannelGameInfoResp")
	proto.RegisterType((*GetChannelGameInfoReq)(nil), "channel_open_game.GetChannelGameInfoReq")
	proto.RegisterType((*GetChannelGameInfoResp)(nil), "channel_open_game.GetChannelGameInfoResp")
	proto.RegisterType((*GetGameModeInfoReq)(nil), "channel_open_game.GetGameModeInfoReq")
	proto.RegisterType((*GetGameModeInfoResp)(nil), "channel_open_game.GetGameModeInfoResp")
	proto.RegisterType((*SetChannelGameStatusReq)(nil), "channel_open_game.SetChannelGameStatusReq")
	proto.RegisterType((*SetChannelGameStatusResp)(nil), "channel_open_game.SetChannelGameStatusResp")
	proto.RegisterType((*SetChannelGameMemberReq)(nil), "channel_open_game.SetChannelGameMemberReq")
	proto.RegisterType((*SetChannelGameMemberResp)(nil), "channel_open_game.SetChannelGameMemberResp")
	proto.RegisterType((*SetChannelGamePlayerOpenIdReq)(nil), "channel_open_game.SetChannelGamePlayerOpenIdReq")
	proto.RegisterType((*SetChannelGamePlayerOpenIdResp)(nil), "channel_open_game.SetChannelGamePlayerOpenIdResp")
	proto.RegisterType((*GetChannelGameBaseInfoReq)(nil), "channel_open_game.GetChannelGameBaseInfoReq")
	proto.RegisterType((*GetChannelGameBaseInfoResp)(nil), "channel_open_game.GetChannelGameBaseInfoResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelOpenGameClient is the client API for ChannelOpenGame service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelOpenGameClient interface {
	// 基础接口
	GetSupportGameList(ctx context.Context, in *GetSupportGameListReq, opts ...grpc.CallOption) (*GetSupportGameListResp, error)
	GetGameMaintain(ctx context.Context, in *GetGameMaintainReq, opts ...grpc.CallOption) (*GetGameMaintainResp, error)
	BatchGetGameInfo(ctx context.Context, in *BatchGetGameInfoReq, opts ...grpc.CallOption) (*BatchGetGameInfoResp, error)
	SetChannelGame(ctx context.Context, in *SetChannelGameReq, opts ...grpc.CallOption) (*SetChannelGameResp, error)
	UpdateChannelGame(ctx context.Context, in *UpdateChannelGameReq, opts ...grpc.CallOption) (*UpdateChannelGameResp, error)
	SetChannelGameSync(ctx context.Context, in *SetChannelGameSyncReq, opts ...grpc.CallOption) (*SetChannelGameSyncResp, error)
	SetChannelGameAsync(ctx context.Context, in *SetChannelGameAsyncReq, opts ...grpc.CallOption) (*SetChannelGameAsyncResp, error)
	SetChannelGameMaster(ctx context.Context, in *SetChannelGameMasterReq, opts ...grpc.CallOption) (*SetChannelGameMasterResp, error)
	SetChannelGameInfo(ctx context.Context, in *SetChannelGameInfoReq, opts ...grpc.CallOption) (*SetChannelGameInfoResp, error)
	GetChannelGameInfo(ctx context.Context, in *GetChannelGameInfoReq, opts ...grpc.CallOption) (*GetChannelGameInfoResp, error)
	GetGameModeInfo(ctx context.Context, in *GetGameModeInfoReq, opts ...grpc.CallOption) (*GetGameModeInfoResp, error)
	// 扩展接口
	SetChannelGameStatus(ctx context.Context, in *SetChannelGameStatusReq, opts ...grpc.CallOption) (*SetChannelGameStatusResp, error)
	SetChannelGameMember(ctx context.Context, in *SetChannelGameMemberReq, opts ...grpc.CallOption) (*SetChannelGameMemberResp, error)
	SetChannelGamePlayerOpenId(ctx context.Context, in *SetChannelGamePlayerOpenIdReq, opts ...grpc.CallOption) (*SetChannelGamePlayerOpenIdResp, error)
	GetChannelGameBaseInfo(ctx context.Context, in *GetChannelGameBaseInfoReq, opts ...grpc.CallOption) (*GetChannelGameBaseInfoResp, error)
}

type channelOpenGameClient struct {
	cc *grpc.ClientConn
}

func NewChannelOpenGameClient(cc *grpc.ClientConn) ChannelOpenGameClient {
	return &channelOpenGameClient{cc}
}

func (c *channelOpenGameClient) GetSupportGameList(ctx context.Context, in *GetSupportGameListReq, opts ...grpc.CallOption) (*GetSupportGameListResp, error) {
	out := new(GetSupportGameListResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/GetSupportGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) GetGameMaintain(ctx context.Context, in *GetGameMaintainReq, opts ...grpc.CallOption) (*GetGameMaintainResp, error) {
	out := new(GetGameMaintainResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/GetGameMaintain", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) BatchGetGameInfo(ctx context.Context, in *BatchGetGameInfoReq, opts ...grpc.CallOption) (*BatchGetGameInfoResp, error) {
	out := new(BatchGetGameInfoResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/BatchGetGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGame(ctx context.Context, in *SetChannelGameReq, opts ...grpc.CallOption) (*SetChannelGameResp, error) {
	out := new(SetChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) UpdateChannelGame(ctx context.Context, in *UpdateChannelGameReq, opts ...grpc.CallOption) (*UpdateChannelGameResp, error) {
	out := new(UpdateChannelGameResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/UpdateChannelGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGameSync(ctx context.Context, in *SetChannelGameSyncReq, opts ...grpc.CallOption) (*SetChannelGameSyncResp, error) {
	out := new(SetChannelGameSyncResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGameSync", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGameAsync(ctx context.Context, in *SetChannelGameAsyncReq, opts ...grpc.CallOption) (*SetChannelGameAsyncResp, error) {
	out := new(SetChannelGameAsyncResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGameAsync", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGameMaster(ctx context.Context, in *SetChannelGameMasterReq, opts ...grpc.CallOption) (*SetChannelGameMasterResp, error) {
	out := new(SetChannelGameMasterResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGameMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGameInfo(ctx context.Context, in *SetChannelGameInfoReq, opts ...grpc.CallOption) (*SetChannelGameInfoResp, error) {
	out := new(SetChannelGameInfoResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) GetChannelGameInfo(ctx context.Context, in *GetChannelGameInfoReq, opts ...grpc.CallOption) (*GetChannelGameInfoResp, error) {
	out := new(GetChannelGameInfoResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/GetChannelGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) GetGameModeInfo(ctx context.Context, in *GetGameModeInfoReq, opts ...grpc.CallOption) (*GetGameModeInfoResp, error) {
	out := new(GetGameModeInfoResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/GetGameModeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGameStatus(ctx context.Context, in *SetChannelGameStatusReq, opts ...grpc.CallOption) (*SetChannelGameStatusResp, error) {
	out := new(SetChannelGameStatusResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGameStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGameMember(ctx context.Context, in *SetChannelGameMemberReq, opts ...grpc.CallOption) (*SetChannelGameMemberResp, error) {
	out := new(SetChannelGameMemberResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGameMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) SetChannelGamePlayerOpenId(ctx context.Context, in *SetChannelGamePlayerOpenIdReq, opts ...grpc.CallOption) (*SetChannelGamePlayerOpenIdResp, error) {
	out := new(SetChannelGamePlayerOpenIdResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/SetChannelGamePlayerOpenId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameClient) GetChannelGameBaseInfo(ctx context.Context, in *GetChannelGameBaseInfoReq, opts ...grpc.CallOption) (*GetChannelGameBaseInfoResp, error) {
	out := new(GetChannelGameBaseInfoResp)
	err := c.cc.Invoke(ctx, "/channel_open_game.ChannelOpenGame/GetChannelGameBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelOpenGameServer is the server API for ChannelOpenGame service.
type ChannelOpenGameServer interface {
	// 基础接口
	GetSupportGameList(context.Context, *GetSupportGameListReq) (*GetSupportGameListResp, error)
	GetGameMaintain(context.Context, *GetGameMaintainReq) (*GetGameMaintainResp, error)
	BatchGetGameInfo(context.Context, *BatchGetGameInfoReq) (*BatchGetGameInfoResp, error)
	SetChannelGame(context.Context, *SetChannelGameReq) (*SetChannelGameResp, error)
	UpdateChannelGame(context.Context, *UpdateChannelGameReq) (*UpdateChannelGameResp, error)
	SetChannelGameSync(context.Context, *SetChannelGameSyncReq) (*SetChannelGameSyncResp, error)
	SetChannelGameAsync(context.Context, *SetChannelGameAsyncReq) (*SetChannelGameAsyncResp, error)
	SetChannelGameMaster(context.Context, *SetChannelGameMasterReq) (*SetChannelGameMasterResp, error)
	SetChannelGameInfo(context.Context, *SetChannelGameInfoReq) (*SetChannelGameInfoResp, error)
	GetChannelGameInfo(context.Context, *GetChannelGameInfoReq) (*GetChannelGameInfoResp, error)
	GetGameModeInfo(context.Context, *GetGameModeInfoReq) (*GetGameModeInfoResp, error)
	// 扩展接口
	SetChannelGameStatus(context.Context, *SetChannelGameStatusReq) (*SetChannelGameStatusResp, error)
	SetChannelGameMember(context.Context, *SetChannelGameMemberReq) (*SetChannelGameMemberResp, error)
	SetChannelGamePlayerOpenId(context.Context, *SetChannelGamePlayerOpenIdReq) (*SetChannelGamePlayerOpenIdResp, error)
	GetChannelGameBaseInfo(context.Context, *GetChannelGameBaseInfoReq) (*GetChannelGameBaseInfoResp, error)
}

func RegisterChannelOpenGameServer(s *grpc.Server, srv ChannelOpenGameServer) {
	s.RegisterService(&_ChannelOpenGame_serviceDesc, srv)
}

func _ChannelOpenGame_GetSupportGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).GetSupportGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/GetSupportGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).GetSupportGameList(ctx, req.(*GetSupportGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_GetGameMaintain_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameMaintainReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).GetGameMaintain(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/GetGameMaintain",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).GetGameMaintain(ctx, req.(*GetGameMaintainReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_BatchGetGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).BatchGetGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/BatchGetGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).BatchGetGameInfo(ctx, req.(*BatchGetGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGame(ctx, req.(*SetChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_UpdateChannelGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChannelGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).UpdateChannelGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/UpdateChannelGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).UpdateChannelGame(ctx, req.(*UpdateChannelGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGameSync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameSyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGameSync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGameSync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGameSync(ctx, req.(*SetChannelGameSyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGameAsync_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameAsyncReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGameAsync(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGameAsync",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGameAsync(ctx, req.(*SetChannelGameAsyncReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGameMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGameMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGameMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGameMaster(ctx, req.(*SetChannelGameMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGameInfo(ctx, req.(*SetChannelGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_GetChannelGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelGameInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).GetChannelGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/GetChannelGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).GetChannelGameInfo(ctx, req.(*GetChannelGameInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_GetGameModeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameModeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).GetGameModeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/GetGameModeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).GetGameModeInfo(ctx, req.(*GetGameModeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGameStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGameStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGameStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGameStatus(ctx, req.(*SetChannelGameStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGameMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGameMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGameMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGameMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGameMember(ctx, req.(*SetChannelGameMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_SetChannelGamePlayerOpenId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelGamePlayerOpenIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).SetChannelGamePlayerOpenId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/SetChannelGamePlayerOpenId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).SetChannelGamePlayerOpenId(ctx, req.(*SetChannelGamePlayerOpenIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGame_GetChannelGameBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelGameBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameServer).GetChannelGameBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game.ChannelOpenGame/GetChannelGameBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameServer).GetChannelGameBaseInfo(ctx, req.(*GetChannelGameBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOpenGame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_open_game.ChannelOpenGame",
	HandlerType: (*ChannelOpenGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSupportGameList",
			Handler:    _ChannelOpenGame_GetSupportGameList_Handler,
		},
		{
			MethodName: "GetGameMaintain",
			Handler:    _ChannelOpenGame_GetGameMaintain_Handler,
		},
		{
			MethodName: "BatchGetGameInfo",
			Handler:    _ChannelOpenGame_BatchGetGameInfo_Handler,
		},
		{
			MethodName: "SetChannelGame",
			Handler:    _ChannelOpenGame_SetChannelGame_Handler,
		},
		{
			MethodName: "UpdateChannelGame",
			Handler:    _ChannelOpenGame_UpdateChannelGame_Handler,
		},
		{
			MethodName: "SetChannelGameSync",
			Handler:    _ChannelOpenGame_SetChannelGameSync_Handler,
		},
		{
			MethodName: "SetChannelGameAsync",
			Handler:    _ChannelOpenGame_SetChannelGameAsync_Handler,
		},
		{
			MethodName: "SetChannelGameMaster",
			Handler:    _ChannelOpenGame_SetChannelGameMaster_Handler,
		},
		{
			MethodName: "SetChannelGameInfo",
			Handler:    _ChannelOpenGame_SetChannelGameInfo_Handler,
		},
		{
			MethodName: "GetChannelGameInfo",
			Handler:    _ChannelOpenGame_GetChannelGameInfo_Handler,
		},
		{
			MethodName: "GetGameModeInfo",
			Handler:    _ChannelOpenGame_GetGameModeInfo_Handler,
		},
		{
			MethodName: "SetChannelGameStatus",
			Handler:    _ChannelOpenGame_SetChannelGameStatus_Handler,
		},
		{
			MethodName: "SetChannelGameMember",
			Handler:    _ChannelOpenGame_SetChannelGameMember_Handler,
		},
		{
			MethodName: "SetChannelGamePlayerOpenId",
			Handler:    _ChannelOpenGame_SetChannelGamePlayerOpenId_Handler,
		},
		{
			MethodName: "GetChannelGameBaseInfo",
			Handler:    _ChannelOpenGame_GetChannelGameBaseInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-open-game/channel-open-game.proto",
}

func init() {
	proto.RegisterFile("channel-open-game/channel-open-game.proto", fileDescriptor_channel_open_game_8fb924251e7b0ad7)
}

var fileDescriptor_channel_open_game_8fb924251e7b0ad7 = []byte{
	// 1709 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x59, 0xcd, 0x73, 0xd3, 0x48,
	0x16, 0x47, 0xb1, 0x9d, 0xc4, 0xcf, 0x71, 0x3e, 0x3a, 0x71, 0x50, 0x4c, 0xb1, 0x64, 0xb5, 0x90,
	0x0d, 0x2c, 0x84, 0x25, 0x14, 0x7b, 0xe0, 0xb2, 0x45, 0x58, 0x2a, 0x9b, 0x0a, 0x61, 0x53, 0x4e,
	0xe0, 0xb0, 0x17, 0x95, 0x62, 0x37, 0x46, 0x89, 0x25, 0x75, 0xd4, 0x6d, 0x88, 0xcf, 0x5b, 0x5b,
	0x7b, 0xdb, 0xc3, 0x1c, 0xe7, 0x3a, 0x87, 0xb9, 0x4f, 0xd5, 0xcc, 0xfc, 0x6b, 0x73, 0x9c, 0xea,
	0xd7, 0x2d, 0x59, 0xb2, 0x3a, 0xb1, 0x03, 0x5c, 0xb8, 0x49, 0xaf, 0x5f, 0xbf, 0xf7, 0xeb, 0xa7,
	0xdf, 0xfb, 0x68, 0x1b, 0xee, 0xb7, 0x3f, 0x78, 0x61, 0x48, 0x7b, 0x8f, 0x22, 0x46, 0xc3, 0x47,
	0x5d, 0x2f, 0xa0, 0x8f, 0x0b, 0x92, 0x2d, 0x16, 0x47, 0x22, 0x22, 0x4b, 0x7a, 0xc1, 0x95, 0x0b,
	0xae, 0x5c, 0x70, 0x7e, 0xb6, 0xa0, 0xf1, 0x52, 0x49, 0xff, 0xc5, 0x68, 0xb8, 0xeb, 0x05, 0xf4,
	0xb0, 0xe7, 0x0d, 0x68, 0x4c, 0x16, 0xa1, 0xd4, 0xf7, 0x3b, 0xb6, 0xb5, 0x6e, 0x6d, 0xd6, 0x5b,
	0xf2, 0x91, 0xac, 0xc2, 0x34, 0x17, 0x9e, 0xe8, 0x73, 0x7b, 0x0a, 0x85, 0xfa, 0x8d, 0x34, 0x61,
	0xb6, 0xcf, 0x69, 0x1c, 0x7a, 0x01, 0xb5, 0x4b, 0xeb, 0xd6, 0x66, 0xb5, 0x95, 0xbe, 0xcb, 0xb5,
	0xd0, 0x6f, 0x9f, 0xe1, 0x5a, 0x59, 0xad, 0x25, 0xef, 0xd2, 0x03, 0xa7, 0x17, 0x76, 0x45, 0x79,
	0xe0, 0xf4, 0x42, 0x7a, 0x90, 0xd0, 0xfc, 0x8e, 0x3d, 0x8d, 0xba, 0xfa, 0x4d, 0x69, 0x9e, 0xdb,
	0x33, 0x89, 0xe6, 0xb9, 0xf3, 0x5b, 0x05, 0xe6, 0x24, 0xd8, 0x1d, 0x8f, 0xd3, 0xbd, 0xf0, 0x7d,
	0x44, 0x96, 0xa1, 0xd2, 0x66, 0x6e, 0x0a, 0xb8, 0xdc, 0x66, 0x7b, 0x1d, 0x72, 0x13, 0x66, 0xe4,
	0x29, 0xa5, 0x58, 0x43, 0x96, 0xaf, 0x7b, 0x1d, 0xf2, 0x47, 0x98, 0xc3, 0x85, 0x8f, 0x34, 0xe6,
	0x7e, 0x14, 0x6a, 0xd8, 0x35, 0x29, 0x7b, 0xa7, 0x44, 0xe4, 0x16, 0x54, 0x51, 0x25, 0x0b, 0x5d,
	0x0a, 0xde, 0x48, 0xe8, 0x6b, 0x80, 0xcf, 0x2e, 0xf3, 0xdb, 0x88, 0xbf, 0xda, 0x42, 0x47, 0x87,
	0x7e, 0x3b, 0x35, 0xcd, 0xbc, 0xf6, 0x99, 0xd7, 0xa5, 0xfa, 0x24, 0x68, 0xfa, 0x50, 0x89, 0xd2,
	0xdd, 0xfd, 0xb8, 0x87, 0x67, 0xd2, 0xbb, 0xdf, 0xc6, 0x3d, 0x72, 0x1b, 0x80, 0x86, 0x5d, 0x3f,
	0x44, 0x68, 0xf6, 0x2c, 0x82, 0xae, 0x2a, 0xc9, 0x3b, 0x1a, 0x93, 0x27, 0xd0, 0xc0, 0x9d, 0x01,
	0x0d, 0x4e, 0x68, 0xec, 0xb6, 0x43, 0xe1, 0xf6, 0xfc, 0xc0, 0x17, 0x76, 0x15, 0x35, 0x89, 0x5c,
	0x3c, 0xc0, 0xb5, 0x97, 0xa1, 0x78, 0x2d, 0x57, 0xc8, 0x1d, 0xa8, 0x69, 0x8b, 0x62, 0xc0, 0xa8,
	0x0d, 0xa8, 0xa8, 0x9d, 0x1c, 0x0f, 0x18, 0x25, 0x77, 0x61, 0x1e, 0x6d, 0x7a, 0x8c, 0x69, 0x63,
	0xb5, 0xf5, 0xd2, 0x66, 0xbd, 0x85, 0xc7, 0x78, 0xc1, 0x98, 0x32, 0xb3, 0x05, 0xcb, 0xea, 0x58,
	0x3d, 0x4f, 0xbc, 0x8f, 0xe2, 0x40, 0xab, 0xce, 0xa1, 0xea, 0x52, 0x57, 0xf1, 0x06, 0x57, 0x52,
	0xb7, 0xed, 0x98, 0x7a, 0x82, 0xba, 0xc2, 0x0f, 0xa8, 0x5d, 0x57, 0x6e, 0x95, 0xe8, 0xd8, 0x0f,
	0x28, 0x59, 0xd7, 0x71, 0x8a, 0x29, 0xc7, 0x40, 0xcc, 0x63, 0x20, 0x40, 0xca, 0x5a, 0x94, 0xeb,
	0x58, 0x70, 0xe1, 0xc5, 0x42, 0x59, 0x58, 0x50, 0xb1, 0x40, 0x09, 0x1a, 0xd8, 0xd6, 0xb1, 0xa0,
	0x17, 0x22, 0xf6, 0x5c, 0x16, 0x47, 0x8c, 0xc6, 0xc2, 0xa7, 0xdc, 0x5e, 0x44, 0x4b, 0x08, 0xf7,
	0x95, 0x5c, 0x3b, 0x4c, 0x97, 0x24, 0x2a, 0xdc, 0xd3, 0xf1, 0xbb, 0x94, 0x0b, 0x7b, 0x69, 0xe8,
	0xf3, 0x1f, 0x28, 0x21, 0x1b, 0xb0, 0x90, 0xa2, 0xd2, 0x4a, 0x04, 0x95, 0xea, 0x1a, 0x98, 0xd6,
	0x4b, 0xd8, 0x11, 0x44, 0x1d, 0x6a, 0x2f, 0x0f, 0xd9, 0x71, 0x10, 0x75, 0x28, 0x69, 0xc0, 0xf4,
	0xa9, 0x3a, 0xd4, 0x0a, 0xae, 0x54, 0x4e, 0xf1, 0x3c, 0xb7, 0xa0, 0x7a, 0x9a, 0x5a, 0x6d, 0xa8,
	0x3d, 0xa7, 0x89, 0xc1, 0x4d, 0x58, 0x0c, 0x3c, 0x3f, 0x4c, 0x68, 0x83, 0xbb, 0x57, 0x51, 0x67,
	0x5e, 0xca, 0x35, 0x75, 0xde, 0xc6, 0x3d, 0xe7, 0x47, 0x0b, 0x96, 0x75, 0xca, 0xca, 0x0c, 0x38,
	0xf0, 0xfc, 0x50, 0x78, 0x7e, 0x48, 0xee, 0x01, 0x6a, 0xca, 0x67, 0xf7, 0x84, 0x76, 0xfd, 0x10,
	0x53, 0xa1, 0xd4, 0xaa, 0x27, 0xd2, 0x1d, 0x29, 0x94, 0xfc, 0x4c, 0xd5, 0x68, 0xa8, 0x12, 0xa3,
	0xd4, 0xaa, 0x25, 0xb2, 0x57, 0x61, 0x87, 0xfc, 0x09, 0xd2, 0x3d, 0xae, 0xa0, 0x17, 0x42, 0xa7,
	0x47, 0xba, 0xef, 0x98, 0x5e, 0x88, 0x9c, 0x9d, 0x33, 0x3a, 0xc0, 0x14, 0xc9, 0xd8, 0xd9, 0xa7,
	0x03, 0xe7, 0x7b, 0x0b, 0x16, 0xb2, 0x48, 0x65, 0x6c, 0xd6, 0x60, 0x56, 0xc6, 0x0c, 0xb7, 0x58,
	0x8a, 0xfb, 0xf2, 0x7d, 0x9f, 0x0e, 0xe4, 0xf7, 0xd6, 0x99, 0x13, 0x7b, 0x01, 0xe2, 0xaa, 0xb6,
	0xaa, 0x2a, 0x6f, 0x62, 0x2f, 0x90, 0x0e, 0x19, 0x96, 0x26, 0x4d, 0xbd, 0x12, 0x12, 0xa2, 0xa6,
	0x64, 0x8a, 0x74, 0x0f, 0x60, 0x29, 0xab, 0xe2, 0xf6, 0x7c, 0x2e, 0xec, 0x32, 0x52, 0x74, 0x21,
	0xa3, 0xf7, 0xda, 0xe7, 0xc2, 0xf9, 0xa1, 0x9c, 0x86, 0x31, 0xa9, 0x7c, 0x58, 0x48, 0x9e, 0x42,
	0xf9, 0xc4, 0xe3, 0x14, 0xc1, 0xd5, 0xb6, 0xef, 0x6c, 0x15, 0x6a, 0xe6, 0x56, 0xb6, 0xee, 0xb4,
	0x50, 0x39, 0xe5, 0x55, 0xae, 0x3e, 0xe2, 0x69, 0x8e, 0x54, 0x8d, 0x4c, 0x14, 0x02, 0x8f, 0x0b,
	0x1a, 0x6b, 0xec, 0xa8, 0x70, 0x80, 0x92, 0x94, 0x78, 0xc3, 0xcc, 0xc6, 0x88, 0xd6, 0x15, 0xf1,
	0xd2, 0x9c, 0x26, 0xfb, 0x49, 0x79, 0xc1, 0xe3, 0x70, 0xbb, 0xb2, 0x5e, 0xda, 0xac, 0x6d, 0x6f,
	0x1a, 0x60, 0x1a, 0xcb, 0xba, 0x2e, 0x44, 0x6a, 0xb3, 0xa4, 0x8c, 0xaa, 0x8f, 0xa1, 0x2f, 0x7c,
	0x4f, 0x44, 0x31, 0x56, 0x2b, 0xed, 0x73, 0x2f, 0x11, 0x92, 0x87, 0x80, 0x85, 0xc5, 0x6d, 0xf7,
	0xe3, 0x98, 0x86, 0xc2, 0x8d, 0xa3, 0x7e, 0xd8, 0xd1, 0x95, 0x6b, 0x51, 0xae, 0xbc, 0x54, 0x0b,
	0x2d, 0x29, 0x27, 0x0e, 0xd4, 0x7d, 0xee, 0x86, 0xf4, 0x93, 0xc6, 0x88, 0x55, 0x6c, 0xb6, 0x55,
	0xf3, 0xf9, 0x1b, 0xfa, 0x49, 0x37, 0x97, 0xbf, 0x67, 0xd3, 0xa7, 0x8a, 0x91, 0x76, 0x2e, 0x3f,
	0x42, 0x42, 0x9e, 0x4c, 0x8a, 0xed, 0x43, 0x5d, 0xc7, 0x53, 0xd1, 0x0d, 0xeb, 0x5a, 0x6d, 0x7b,
	0x63, 0x8c, 0x11, 0xad, 0xad, 0x6a, 0x5b, 0x9a, 0x39, 0x6b, 0x30, 0xdb, 0x8b, 0xbc, 0x8e, 0x2b,
	0x7b, 0x4c, 0x0d, 0x69, 0x3c, 0x23, 0xdf, 0x8f, 0xe8, 0xb9, 0xe3, 0x42, 0x63, 0x97, 0x8a, 0xa3,
	0x3e, 0x63, 0x51, 0x2c, 0xa4, 0x09, 0xc9, 0x9d, 0x16, 0x3d, 0x37, 0xf7, 0x9b, 0x06, 0x4c, 0xcb,
	0x2a, 0x9a, 0xb6, 0x9b, 0x8a, 0xc7, 0xa4, 0xb8, 0x09, 0xb3, 0x49, 0xd9, 0xd4, 0x5f, 0x3e, 0x7d,
	0x77, 0x0e, 0x60, 0xd5, 0xe4, 0x80, 0xb3, 0x0c, 0x11, 0x4b, 0x13, 0x13, 0xd1, 0x39, 0x04, 0xb2,
	0x4b, 0x45, 0xee, 0xac, 0xf4, 0x3c, 0xdb, 0x07, 0xad, 0x2b, 0xfb, 0xe0, 0x54, 0xa1, 0x0f, 0x3a,
	0x27, 0xb0, 0x5c, 0xb0, 0xc8, 0x59, 0xf1, 0x03, 0x58, 0x9f, 0xff, 0x01, 0x9c, 0x2d, 0x58, 0xde,
	0xf1, 0x44, 0xfb, 0x83, 0x76, 0x84, 0xe7, 0x19, 0x85, 0x5d, 0x1a, 0xc2, 0x76, 0xf6, 0x61, 0xa5,
	0xa8, 0xff, 0xb9, 0x21, 0xfb, 0xc9, 0x82, 0xa5, 0x23, 0x2a, 0x32, 0x28, 0xa5, 0xef, 0xdb, 0x00,
	0xc9, 0xee, 0x34, 0x6a, 0x55, 0x2d, 0xc9, 0x4f, 0x16, 0xa5, 0x2b, 0x23, 0x5a, 0x2e, 0x4e, 0x16,
	0xc5, 0xac, 0xab, 0x98, 0xb2, 0x2e, 0xd7, 0x62, 0xa6, 0xf3, 0x2d, 0xc6, 0x59, 0x01, 0x32, 0x8a,
	0x99, 0x33, 0xe7, 0x17, 0x0b, 0x1a, 0x79, 0xf1, 0xd1, 0x20, 0x6c, 0x7f, 0x03, 0xc7, 0xf9, 0xbf,
	0x05, 0xab, 0x26, 0xe0, 0x9c, 0x15, 0x10, 0x58, 0x45, 0x04, 0x7f, 0x83, 0xb2, 0x1f, 0xbe, 0x8f,
	0x90, 0xbd, 0x93, 0x15, 0x12, 0xd4, 0xcf, 0xe5, 0x7d, 0x29, 0x9f, 0xf7, 0xbf, 0x16, 0x00, 0xbd,
	0xe0, 0xdf, 0x46, 0x28, 0xd7, 0xe0, 0xa6, 0x11, 0x38, 0x67, 0xce, 0x33, 0x58, 0x79, 0xcb, 0x3a,
	0x9e, 0xa0, 0xd7, 0xe2, 0xba, 0xb3, 0x0d, 0x0d, 0xc3, 0x36, 0xce, 0x72, 0xf1, 0xb3, 0xf2, 0xf1,
	0x8b, 0x47, 0x51, 0xa8, 0x36, 0x77, 0xbd, 0xf8, 0xe5, 0x67, 0xf6, 0x71, 0x2d, 0xd4, 0x69, 0x82,
	0x6d, 0xf6, 0xc9, 0x99, 0x13, 0x8f, 0x26, 0x46, 0x52, 0x63, 0xc6, 0xa0, 0x79, 0x9e, 0xa3, 0xd6,
	0xc6, 0xf8, 0x36, 0xab, 0x0a, 0x8b, 0xdc, 0xe3, 0xd8, 0xa3, 0x14, 0x4a, 0xea, 0x94, 0xf3, 0x4f,
	0xec, 0x2a, 0xd7, 0x47, 0xa3, 0xef, 0x64, 0x53, 0xe9, 0x9d, 0xcc, 0x39, 0xc6, 0xf6, 0x61, 0xf0,
	0x91, 0x22, 0xb7, 0x3e, 0x03, 0x79, 0x30, 0xec, 0x22, 0x51, 0xc7, 0x5c, 0x8e, 0xaf, 0xd7, 0x45,
	0xf2, 0x94, 0x2d, 0x8d, 0x50, 0xf6, 0x60, 0xd8, 0x62, 0x52, 0x77, 0x9c, 0xa5, 0x69, 0x6d, 0x5d,
	0x2f, 0xad, 0x8b, 0xdc, 0x53, 0x33, 0xd8, 0xd7, 0xe0, 0x9e, 0x9e, 0xef, 0x4a, 0xa3, 0xf3, 0x5d,
	0x91, 0x7b, 0x89, 0x4f, 0xce, 0x9c, 0x41, 0x21, 0x17, 0x70, 0x9a, 0xfb, 0x12, 0x3c, 0x86, 0x69,
	0xb1, 0x64, 0x98, 0x16, 0x0d, 0x29, 0xa1, 0x5d, 0x73, 0xe6, 0xfc, 0xd7, 0x82, 0xdb, 0xf9, 0x45,
	0x35, 0x9c, 0x49, 0x3e, 0xec, 0x75, 0xbe, 0x04, 0x1d, 0x81, 0x72, 0xdf, 0xef, 0xc8, 0x30, 0xc9,
	0xa6, 0x8d, 0xcf, 0xc4, 0x86, 0x19, 0x75, 0x99, 0xe7, 0x38, 0x90, 0x57, 0x5b, 0xc9, 0xab, 0xb3,
	0x0e, 0x7f, 0xb8, 0x0a, 0x06, 0x67, 0xce, 0x73, 0x58, 0xcb, 0x93, 0x3c, 0xed, 0xe0, 0xe3, 0x8b,
	0xd7, 0x77, 0x53, 0xd0, 0xbc, 0x6c, 0x33, 0x67, 0x5f, 0x85, 0xd3, 0xd9, 0x1f, 0x3e, 0xd2, 0x5f,
	0x08, 0x2e, 0xbd, 0xa9, 0x97, 0x2f, 0xbd, 0xa9, 0xa7, 0x23, 0x65, 0x25, 0x33, 0x52, 0x26, 0xb7,
	0x73, 0xbc, 0x34, 0x61, 0x3a, 0xa8, 0x82, 0x3f, 0xd7, 0xcd, 0xa4, 0x8b, 0x8c, 0x38, 0x66, 0x96,
	0x9a, 0xc9, 0xf1, 0x39, 0x57, 0x9d, 0x67, 0x73, 0xd5, 0x79, 0xfb, 0x7f, 0xf5, 0xf4, 0x62, 0x96,
	0x64, 0x3f, 0x39, 0xc3, 0x9c, 0x1f, 0x19, 0x44, 0x89, 0xe9, 0x62, 0x61, 0x1c, 0x88, 0x9b, 0xf7,
	0x27, 0xd4, 0xe4, 0xcc, 0xb9, 0x41, 0x4e, 0x60, 0x61, 0x64, 0xa8, 0x24, 0xf7, 0xcc, 0xfb, 0x47,
	0x46, 0xd9, 0xe6, 0xc6, 0x24, 0x6a, 0xe8, 0x83, 0xc2, 0xe2, 0xe8, 0x90, 0x48, 0x4c, 0xbb, 0x0d,
	0x93, 0x67, 0xf3, 0xcf, 0x13, 0xe9, 0xa1, 0x1b, 0x17, 0xe6, 0xf3, 0xf4, 0x25, 0x77, 0x0d, 0x9b,
	0x0b, 0x03, 0x66, 0xf3, 0xde, 0x04, 0x5a, 0xe8, 0xe0, 0x03, 0x2c, 0x15, 0xda, 0x2f, 0x31, 0x01,
	0x34, 0xf5, 0xf6, 0xe6, 0xe6, 0x64, 0x8a, 0xe8, 0xe9, 0x6c, 0x74, 0xa8, 0x94, 0x43, 0x98, 0x91,
	0x02, 0xc6, 0x21, 0xd3, 0x48, 0x01, 0xf3, 0x54, 0xe7, 0xdc, 0x20, 0x21, 0x2c, 0x1b, 0xe6, 0x14,
	0x32, 0xde, 0x46, 0x32, 0x88, 0x35, 0x1f, 0x4c, 0xaa, 0x8a, 0xfe, 0xce, 0x61, 0xc5, 0x34, 0x1d,
	0x90, 0xf1, 0x56, 0xd2, 0xd1, 0xa5, 0xf9, 0x97, 0x89, 0x75, 0xcd, 0xf1, 0x44, 0x0e, 0x8e, 0x8f,
	0x67, 0xc2, 0xc2, 0xfb, 0x13, 0x6a, 0x26, 0xce, 0x76, 0x27, 0x73, 0xb6, 0x3b, 0xb1, 0xb3, 0xdd,
	0xcb, 0x9c, 0x65, 0xf2, 0x37, 0x29, 0x41, 0x57, 0xe5, 0xef, 0x70, 0x88, 0xb8, 0x32, 0x7f, 0x33,
	0xcd, 0xdf, 0xf4, 0xc1, 0xf4, 0x4f, 0x29, 0xe3, 0x3f, 0x58, 0xda, 0xef, 0x27, 0xf8, 0x60, 0x99,
	0x3e, 0x6d, 0xe2, 0x08, 0x56, 0xe8, 0x49, 0x38, 0x92, 0xb4, 0xf4, 0x49, 0x38, 0x32, 0xec, 0xc1,
	0x37, 0xc8, 0x7f, 0x2c, 0x68, 0x5e, 0xde, 0xfe, 0xc8, 0x5f, 0xc7, 0x5a, 0x1b, 0x69, 0xda, 0xcd,
	0x27, 0xd7, 0xdc, 0x81, 0x28, 0x3e, 0x8d, 0x8e, 0x91, 0xe9, 0xef, 0xea, 0x0f, 0xc7, 0xd2, 0x22,
	0xd3, 0x8c, 0x9b, 0x8f, 0xae, 0xa1, 0x2d, 0x1d, 0xef, 0x3c, 0xfb, 0xf7, 0xd3, 0x6e, 0xd4, 0xf3,
	0xc2, 0xee, 0xd6, 0xb3, 0x6d, 0x21, 0xb6, 0xda, 0x51, 0xf0, 0x18, 0xff, 0xab, 0x68, 0x47, 0xbd,
	0xc7, 0x9c, 0xc6, 0x1f, 0xfd, 0x36, 0xe5, 0xc5, 0xff, 0x33, 0x4e, 0xa6, 0x51, 0xe9, 0xe9, 0xef,
	0x01, 0x00, 0x00, 0xff, 0xff, 0xbf, 0x04, 0xf0, 0x1d, 0xfd, 0x18, 0x00, 0x00,
}
