// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-statistics/esport-statistics.proto

package esport_statistics // import "golang.52tt.com/protocol/services/esport-statistics"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 批量获取电竞指导首次接单时间
type BatchGetUserFirstTakeOrdersTimeRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserFirstTakeOrdersTimeRequest) Reset() {
	*m = BatchGetUserFirstTakeOrdersTimeRequest{}
}
func (m *BatchGetUserFirstTakeOrdersTimeRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFirstTakeOrdersTimeRequest) ProtoMessage()    {}
func (*BatchGetUserFirstTakeOrdersTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{0}
}
func (m *BatchGetUserFirstTakeOrdersTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeRequest.Unmarshal(m, b)
}
func (m *BatchGetUserFirstTakeOrdersTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserFirstTakeOrdersTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeRequest.Merge(dst, src)
}
func (m *BatchGetUserFirstTakeOrdersTimeRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeRequest.Size(m)
}
func (m *BatchGetUserFirstTakeOrdersTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeRequest proto.InternalMessageInfo

func (m *BatchGetUserFirstTakeOrdersTimeRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetUserFirstTakeOrdersTimeResponse struct {
	// 首次接单（下单）时间
	TimeMap              map[uint32]int64 `protobuf:"bytes,1,rep,name=time_map,json=timeMap,proto3" json:"time_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatchGetUserFirstTakeOrdersTimeResponse) Reset() {
	*m = BatchGetUserFirstTakeOrdersTimeResponse{}
}
func (m *BatchGetUserFirstTakeOrdersTimeResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserFirstTakeOrdersTimeResponse) ProtoMessage()    {}
func (*BatchGetUserFirstTakeOrdersTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{1}
}
func (m *BatchGetUserFirstTakeOrdersTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeResponse.Unmarshal(m, b)
}
func (m *BatchGetUserFirstTakeOrdersTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserFirstTakeOrdersTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeResponse.Merge(dst, src)
}
func (m *BatchGetUserFirstTakeOrdersTimeResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeResponse.Size(m)
}
func (m *BatchGetUserFirstTakeOrdersTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserFirstTakeOrdersTimeResponse proto.InternalMessageInfo

func (m *BatchGetUserFirstTakeOrdersTimeResponse) GetTimeMap() map[uint32]int64 {
	if m != nil {
		return m.TimeMap
	}
	return nil
}

// 获取用户特定game的统计数据
type GetCoachStatisticsRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachStatisticsRequest) Reset()         { *m = GetCoachStatisticsRequest{} }
func (m *GetCoachStatisticsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachStatisticsRequest) ProtoMessage()    {}
func (*GetCoachStatisticsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{2}
}
func (m *GetCoachStatisticsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachStatisticsRequest.Unmarshal(m, b)
}
func (m *GetCoachStatisticsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachStatisticsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachStatisticsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachStatisticsRequest.Merge(dst, src)
}
func (m *GetCoachStatisticsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachStatisticsRequest.Size(m)
}
func (m *GetCoachStatisticsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachStatisticsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachStatisticsRequest proto.InternalMessageInfo

func (m *GetCoachStatisticsRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachStatisticsRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type StatisticsData struct {
	OrderNum             uint32   `protobuf:"varint,1,opt,name=order_num,json=orderNum,proto3" json:"order_num,omitempty"`
	CustomerNum          uint32   `protobuf:"varint,2,opt,name=customer_num,json=customerNum,proto3" json:"customer_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatisticsData) Reset()         { *m = StatisticsData{} }
func (m *StatisticsData) String() string { return proto.CompactTextString(m) }
func (*StatisticsData) ProtoMessage()    {}
func (*StatisticsData) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{3}
}
func (m *StatisticsData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatisticsData.Unmarshal(m, b)
}
func (m *StatisticsData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatisticsData.Marshal(b, m, deterministic)
}
func (dst *StatisticsData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatisticsData.Merge(dst, src)
}
func (m *StatisticsData) XXX_Size() int {
	return xxx_messageInfo_StatisticsData.Size(m)
}
func (m *StatisticsData) XXX_DiscardUnknown() {
	xxx_messageInfo_StatisticsData.DiscardUnknown(m)
}

var xxx_messageInfo_StatisticsData proto.InternalMessageInfo

func (m *StatisticsData) GetOrderNum() uint32 {
	if m != nil {
		return m.OrderNum
	}
	return 0
}

func (m *StatisticsData) GetCustomerNum() uint32 {
	if m != nil {
		return m.CustomerNum
	}
	return 0
}

type GetCoachStatisticsResponse struct {
	Data                 *StatisticsData `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetCoachStatisticsResponse) Reset()         { *m = GetCoachStatisticsResponse{} }
func (m *GetCoachStatisticsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachStatisticsResponse) ProtoMessage()    {}
func (*GetCoachStatisticsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{4}
}
func (m *GetCoachStatisticsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachStatisticsResponse.Unmarshal(m, b)
}
func (m *GetCoachStatisticsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachStatisticsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachStatisticsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachStatisticsResponse.Merge(dst, src)
}
func (m *GetCoachStatisticsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachStatisticsResponse.Size(m)
}
func (m *GetCoachStatisticsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachStatisticsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachStatisticsResponse proto.InternalMessageInfo

func (m *GetCoachStatisticsResponse) GetData() *StatisticsData {
	if m != nil {
		return m.Data
	}
	return nil
}

// 获取大神全部技能统计
type GetCoachAllStatisticsRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachAllStatisticsRequest) Reset()         { *m = GetCoachAllStatisticsRequest{} }
func (m *GetCoachAllStatisticsRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachAllStatisticsRequest) ProtoMessage()    {}
func (*GetCoachAllStatisticsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{5}
}
func (m *GetCoachAllStatisticsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachAllStatisticsRequest.Unmarshal(m, b)
}
func (m *GetCoachAllStatisticsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachAllStatisticsRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachAllStatisticsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachAllStatisticsRequest.Merge(dst, src)
}
func (m *GetCoachAllStatisticsRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachAllStatisticsRequest.Size(m)
}
func (m *GetCoachAllStatisticsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachAllStatisticsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachAllStatisticsRequest proto.InternalMessageInfo

func (m *GetCoachAllStatisticsRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetCoachAllStatisticsResponse struct {
	GameDataMap          map[uint32]*StatisticsData `protobuf:"bytes,1,rep,name=game_data_map,json=gameDataMap,proto3" json:"game_data_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetCoachAllStatisticsResponse) Reset()         { *m = GetCoachAllStatisticsResponse{} }
func (m *GetCoachAllStatisticsResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachAllStatisticsResponse) ProtoMessage()    {}
func (*GetCoachAllStatisticsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{6}
}
func (m *GetCoachAllStatisticsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachAllStatisticsResponse.Unmarshal(m, b)
}
func (m *GetCoachAllStatisticsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachAllStatisticsResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachAllStatisticsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachAllStatisticsResponse.Merge(dst, src)
}
func (m *GetCoachAllStatisticsResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachAllStatisticsResponse.Size(m)
}
func (m *GetCoachAllStatisticsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachAllStatisticsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachAllStatisticsResponse proto.InternalMessageInfo

func (m *GetCoachAllStatisticsResponse) GetGameDataMap() map[uint32]*StatisticsData {
	if m != nil {
		return m.GameDataMap
	}
	return nil
}

// 获取用户最多下单的游戏
type GetUserMostPayGameRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMostPayGameRequest) Reset()         { *m = GetUserMostPayGameRequest{} }
func (m *GetUserMostPayGameRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserMostPayGameRequest) ProtoMessage()    {}
func (*GetUserMostPayGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{7}
}
func (m *GetUserMostPayGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMostPayGameRequest.Unmarshal(m, b)
}
func (m *GetUserMostPayGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMostPayGameRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserMostPayGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMostPayGameRequest.Merge(dst, src)
}
func (m *GetUserMostPayGameRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserMostPayGameRequest.Size(m)
}
func (m *GetUserMostPayGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMostPayGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMostPayGameRequest proto.InternalMessageInfo

func (m *GetUserMostPayGameRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMostPayGameResponse struct {
	GameId               uint32   `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMostPayGameResponse) Reset()         { *m = GetUserMostPayGameResponse{} }
func (m *GetUserMostPayGameResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserMostPayGameResponse) ProtoMessage()    {}
func (*GetUserMostPayGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{8}
}
func (m *GetUserMostPayGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMostPayGameResponse.Unmarshal(m, b)
}
func (m *GetUserMostPayGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMostPayGameResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserMostPayGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMostPayGameResponse.Merge(dst, src)
}
func (m *GetUserMostPayGameResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserMostPayGameResponse.Size(m)
}
func (m *GetUserMostPayGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMostPayGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMostPayGameResponse proto.InternalMessageInfo

func (m *GetUserMostPayGameResponse) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 处理电竞指导身份回收
type HandleRecallCoachRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleRecallCoachRequest) Reset()         { *m = HandleRecallCoachRequest{} }
func (m *HandleRecallCoachRequest) String() string { return proto.CompactTextString(m) }
func (*HandleRecallCoachRequest) ProtoMessage()    {}
func (*HandleRecallCoachRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{9}
}
func (m *HandleRecallCoachRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleRecallCoachRequest.Unmarshal(m, b)
}
func (m *HandleRecallCoachRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleRecallCoachRequest.Marshal(b, m, deterministic)
}
func (dst *HandleRecallCoachRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleRecallCoachRequest.Merge(dst, src)
}
func (m *HandleRecallCoachRequest) XXX_Size() int {
	return xxx_messageInfo_HandleRecallCoachRequest.Size(m)
}
func (m *HandleRecallCoachRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleRecallCoachRequest.DiscardUnknown(m)
}

var xxx_messageInfo_HandleRecallCoachRequest proto.InternalMessageInfo

func (m *HandleRecallCoachRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type HandleRecallCoachResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HandleRecallCoachResponse) Reset()         { *m = HandleRecallCoachResponse{} }
func (m *HandleRecallCoachResponse) String() string { return proto.CompactTextString(m) }
func (*HandleRecallCoachResponse) ProtoMessage()    {}
func (*HandleRecallCoachResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{10}
}
func (m *HandleRecallCoachResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HandleRecallCoachResponse.Unmarshal(m, b)
}
func (m *HandleRecallCoachResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HandleRecallCoachResponse.Marshal(b, m, deterministic)
}
func (dst *HandleRecallCoachResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HandleRecallCoachResponse.Merge(dst, src)
}
func (m *HandleRecallCoachResponse) XXX_Size() int {
	return xxx_messageInfo_HandleRecallCoachResponse.Size(m)
}
func (m *HandleRecallCoachResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_HandleRecallCoachResponse.DiscardUnknown(m)
}

var xxx_messageInfo_HandleRecallCoachResponse proto.InternalMessageInfo

// 批量获取指导周期内接单量
type BatchGetCoachDurationOrderNumRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	DayCnt               uint32   `protobuf:"varint,3,opt,name=day_cnt,json=dayCnt,proto3" json:"day_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCoachDurationOrderNumRequest) Reset()         { *m = BatchGetCoachDurationOrderNumRequest{} }
func (m *BatchGetCoachDurationOrderNumRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachDurationOrderNumRequest) ProtoMessage()    {}
func (*BatchGetCoachDurationOrderNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{11}
}
func (m *BatchGetCoachDurationOrderNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachDurationOrderNumRequest.Unmarshal(m, b)
}
func (m *BatchGetCoachDurationOrderNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachDurationOrderNumRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachDurationOrderNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachDurationOrderNumRequest.Merge(dst, src)
}
func (m *BatchGetCoachDurationOrderNumRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachDurationOrderNumRequest.Size(m)
}
func (m *BatchGetCoachDurationOrderNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachDurationOrderNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachDurationOrderNumRequest proto.InternalMessageInfo

func (m *BatchGetCoachDurationOrderNumRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetCoachDurationOrderNumRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BatchGetCoachDurationOrderNumRequest) GetDayCnt() uint32 {
	if m != nil {
		return m.DayCnt
	}
	return 0
}

type BatchGetCoachDurationOrderNumResponse struct {
	OrderNumMap          map[uint32]uint32 `protobuf:"bytes,1,rep,name=order_num_map,json=orderNumMap,proto3" json:"order_num_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetCoachDurationOrderNumResponse) Reset()         { *m = BatchGetCoachDurationOrderNumResponse{} }
func (m *BatchGetCoachDurationOrderNumResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachDurationOrderNumResponse) ProtoMessage()    {}
func (*BatchGetCoachDurationOrderNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{12}
}
func (m *BatchGetCoachDurationOrderNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachDurationOrderNumResponse.Unmarshal(m, b)
}
func (m *BatchGetCoachDurationOrderNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachDurationOrderNumResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachDurationOrderNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachDurationOrderNumResponse.Merge(dst, src)
}
func (m *BatchGetCoachDurationOrderNumResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachDurationOrderNumResponse.Size(m)
}
func (m *BatchGetCoachDurationOrderNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachDurationOrderNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachDurationOrderNumResponse proto.InternalMessageInfo

func (m *BatchGetCoachDurationOrderNumResponse) GetOrderNumMap() map[uint32]uint32 {
	if m != nil {
		return m.OrderNumMap
	}
	return nil
}

// 批量获取电竞指导接单数量
type BatchGetOrderCntByGameIdRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetOrderCntByGameIdRequest) Reset()         { *m = BatchGetOrderCntByGameIdRequest{} }
func (m *BatchGetOrderCntByGameIdRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetOrderCntByGameIdRequest) ProtoMessage()    {}
func (*BatchGetOrderCntByGameIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{13}
}
func (m *BatchGetOrderCntByGameIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOrderCntByGameIdRequest.Unmarshal(m, b)
}
func (m *BatchGetOrderCntByGameIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOrderCntByGameIdRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetOrderCntByGameIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOrderCntByGameIdRequest.Merge(dst, src)
}
func (m *BatchGetOrderCntByGameIdRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetOrderCntByGameIdRequest.Size(m)
}
func (m *BatchGetOrderCntByGameIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOrderCntByGameIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOrderCntByGameIdRequest proto.InternalMessageInfo

func (m *BatchGetOrderCntByGameIdRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetOrderCntByGameIdRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type BatchGetOrderCntByGameIdResponse struct {
	OrderNumMap          map[uint32]uint32 `protobuf:"bytes,1,rep,name=order_num_map,json=orderNumMap,proto3" json:"order_num_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetOrderCntByGameIdResponse) Reset()         { *m = BatchGetOrderCntByGameIdResponse{} }
func (m *BatchGetOrderCntByGameIdResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetOrderCntByGameIdResponse) ProtoMessage()    {}
func (*BatchGetOrderCntByGameIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{14}
}
func (m *BatchGetOrderCntByGameIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOrderCntByGameIdResponse.Unmarshal(m, b)
}
func (m *BatchGetOrderCntByGameIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOrderCntByGameIdResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetOrderCntByGameIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOrderCntByGameIdResponse.Merge(dst, src)
}
func (m *BatchGetOrderCntByGameIdResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetOrderCntByGameIdResponse.Size(m)
}
func (m *BatchGetOrderCntByGameIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOrderCntByGameIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOrderCntByGameIdResponse proto.InternalMessageInfo

func (m *BatchGetOrderCntByGameIdResponse) GetOrderNumMap() map[uint32]uint32 {
	if m != nil {
		return m.OrderNumMap
	}
	return nil
}

type GetUserTotalExpenditureAmountMonthlyRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Month                int64    `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTotalExpenditureAmountMonthlyRequest) Reset() {
	*m = GetUserTotalExpenditureAmountMonthlyRequest{}
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserTotalExpenditureAmountMonthlyRequest) ProtoMessage() {}
func (*GetUserTotalExpenditureAmountMonthlyRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{15}
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Unmarshal(m, b)
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Merge(dst, src)
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.Size(m)
}
func (m *GetUserTotalExpenditureAmountMonthlyRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyRequest proto.InternalMessageInfo

func (m *GetUserTotalExpenditureAmountMonthlyRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserTotalExpenditureAmountMonthlyRequest) GetMonth() int64 {
	if m != nil {
		return m.Month
	}
	return 0
}

type GetUserTotalExpenditureAmountMonthlyResponse struct {
	TotalAmount          int64    `protobuf:"varint,1,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTotalExpenditureAmountMonthlyResponse) Reset() {
	*m = GetUserTotalExpenditureAmountMonthlyResponse{}
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserTotalExpenditureAmountMonthlyResponse) ProtoMessage() {}
func (*GetUserTotalExpenditureAmountMonthlyResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{16}
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Unmarshal(m, b)
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Merge(dst, src)
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.Size(m)
}
func (m *GetUserTotalExpenditureAmountMonthlyResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTotalExpenditureAmountMonthlyResponse proto.InternalMessageInfo

func (m *GetUserTotalExpenditureAmountMonthlyResponse) GetTotalAmount() int64 {
	if m != nil {
		return m.TotalAmount
	}
	return 0
}

type GetUserCoachLatestOrderTimeRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCoachLatestOrderTimeRequest) Reset()         { *m = GetUserCoachLatestOrderTimeRequest{} }
func (m *GetUserCoachLatestOrderTimeRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCoachLatestOrderTimeRequest) ProtoMessage()    {}
func (*GetUserCoachLatestOrderTimeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{17}
}
func (m *GetUserCoachLatestOrderTimeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCoachLatestOrderTimeRequest.Unmarshal(m, b)
}
func (m *GetUserCoachLatestOrderTimeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCoachLatestOrderTimeRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCoachLatestOrderTimeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCoachLatestOrderTimeRequest.Merge(dst, src)
}
func (m *GetUserCoachLatestOrderTimeRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCoachLatestOrderTimeRequest.Size(m)
}
func (m *GetUserCoachLatestOrderTimeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCoachLatestOrderTimeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCoachLatestOrderTimeRequest proto.InternalMessageInfo

func (m *GetUserCoachLatestOrderTimeRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCoachLatestOrderTimeRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetUserCoachLatestOrderTimeResponse struct {
	LatestOrderTime      int64    `protobuf:"varint,1,opt,name=latest_order_time,json=latestOrderTime,proto3" json:"latest_order_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCoachLatestOrderTimeResponse) Reset()         { *m = GetUserCoachLatestOrderTimeResponse{} }
func (m *GetUserCoachLatestOrderTimeResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCoachLatestOrderTimeResponse) ProtoMessage()    {}
func (*GetUserCoachLatestOrderTimeResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{18}
}
func (m *GetUserCoachLatestOrderTimeResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCoachLatestOrderTimeResponse.Unmarshal(m, b)
}
func (m *GetUserCoachLatestOrderTimeResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCoachLatestOrderTimeResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCoachLatestOrderTimeResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCoachLatestOrderTimeResponse.Merge(dst, src)
}
func (m *GetUserCoachLatestOrderTimeResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCoachLatestOrderTimeResponse.Size(m)
}
func (m *GetUserCoachLatestOrderTimeResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCoachLatestOrderTimeResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCoachLatestOrderTimeResponse proto.InternalMessageInfo

func (m *GetUserCoachLatestOrderTimeResponse) GetLatestOrderTime() int64 {
	if m != nil {
		return m.LatestOrderTime
	}
	return 0
}

type ReportUserVisitIMPageRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GetOnly              bool     `protobuf:"varint,3,opt,name=get_only,json=getOnly,proto3" json:"get_only,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserVisitIMPageRequest) Reset()         { *m = ReportUserVisitIMPageRequest{} }
func (m *ReportUserVisitIMPageRequest) String() string { return proto.CompactTextString(m) }
func (*ReportUserVisitIMPageRequest) ProtoMessage()    {}
func (*ReportUserVisitIMPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{19}
}
func (m *ReportUserVisitIMPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserVisitIMPageRequest.Unmarshal(m, b)
}
func (m *ReportUserVisitIMPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserVisitIMPageRequest.Marshal(b, m, deterministic)
}
func (dst *ReportUserVisitIMPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserVisitIMPageRequest.Merge(dst, src)
}
func (m *ReportUserVisitIMPageRequest) XXX_Size() int {
	return xxx_messageInfo_ReportUserVisitIMPageRequest.Size(m)
}
func (m *ReportUserVisitIMPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserVisitIMPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserVisitIMPageRequest proto.InternalMessageInfo

func (m *ReportUserVisitIMPageRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportUserVisitIMPageRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *ReportUserVisitIMPageRequest) GetGetOnly() bool {
	if m != nil {
		return m.GetOnly
	}
	return false
}

type ReportUserVisitIMPageResponse struct {
	VisitCnt             uint32   `protobuf:"varint,3,opt,name=visit_cnt,json=visitCnt,proto3" json:"visit_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserVisitIMPageResponse) Reset()         { *m = ReportUserVisitIMPageResponse{} }
func (m *ReportUserVisitIMPageResponse) String() string { return proto.CompactTextString(m) }
func (*ReportUserVisitIMPageResponse) ProtoMessage()    {}
func (*ReportUserVisitIMPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{20}
}
func (m *ReportUserVisitIMPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserVisitIMPageResponse.Unmarshal(m, b)
}
func (m *ReportUserVisitIMPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserVisitIMPageResponse.Marshal(b, m, deterministic)
}
func (dst *ReportUserVisitIMPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserVisitIMPageResponse.Merge(dst, src)
}
func (m *ReportUserVisitIMPageResponse) XXX_Size() int {
	return xxx_messageInfo_ReportUserVisitIMPageResponse.Size(m)
}
func (m *ReportUserVisitIMPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserVisitIMPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserVisitIMPageResponse proto.InternalMessageInfo

func (m *ReportUserVisitIMPageResponse) GetVisitCnt() uint32 {
	if m != nil {
		return m.VisitCnt
	}
	return 0
}

type ReportUserVisitGameCardRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserVisitGameCardRequest) Reset()         { *m = ReportUserVisitGameCardRequest{} }
func (m *ReportUserVisitGameCardRequest) String() string { return proto.CompactTextString(m) }
func (*ReportUserVisitGameCardRequest) ProtoMessage()    {}
func (*ReportUserVisitGameCardRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{21}
}
func (m *ReportUserVisitGameCardRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserVisitGameCardRequest.Unmarshal(m, b)
}
func (m *ReportUserVisitGameCardRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserVisitGameCardRequest.Marshal(b, m, deterministic)
}
func (dst *ReportUserVisitGameCardRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserVisitGameCardRequest.Merge(dst, src)
}
func (m *ReportUserVisitGameCardRequest) XXX_Size() int {
	return xxx_messageInfo_ReportUserVisitGameCardRequest.Size(m)
}
func (m *ReportUserVisitGameCardRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserVisitGameCardRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserVisitGameCardRequest proto.InternalMessageInfo

func (m *ReportUserVisitGameCardRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportUserVisitGameCardRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *ReportUserVisitGameCardRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type ReportUserVisitGameCardResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportUserVisitGameCardResponse) Reset()         { *m = ReportUserVisitGameCardResponse{} }
func (m *ReportUserVisitGameCardResponse) String() string { return proto.CompactTextString(m) }
func (*ReportUserVisitGameCardResponse) ProtoMessage()    {}
func (*ReportUserVisitGameCardResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{22}
}
func (m *ReportUserVisitGameCardResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportUserVisitGameCardResponse.Unmarshal(m, b)
}
func (m *ReportUserVisitGameCardResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportUserVisitGameCardResponse.Marshal(b, m, deterministic)
}
func (dst *ReportUserVisitGameCardResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportUserVisitGameCardResponse.Merge(dst, src)
}
func (m *ReportUserVisitGameCardResponse) XXX_Size() int {
	return xxx_messageInfo_ReportUserVisitGameCardResponse.Size(m)
}
func (m *ReportUserVisitGameCardResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportUserVisitGameCardResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ReportUserVisitGameCardResponse proto.InternalMessageInfo

type GetBeVisitorRecordCountRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordCountRequest) Reset()         { *m = GetBeVisitorRecordCountRequest{} }
func (m *GetBeVisitorRecordCountRequest) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordCountRequest) ProtoMessage()    {}
func (*GetBeVisitorRecordCountRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{23}
}
func (m *GetBeVisitorRecordCountRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordCountRequest.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordCountRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordCountRequest.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordCountRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordCountRequest.Merge(dst, src)
}
func (m *GetBeVisitorRecordCountRequest) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordCountRequest.Size(m)
}
func (m *GetBeVisitorRecordCountRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordCountRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordCountRequest proto.InternalMessageInfo

func (m *GetBeVisitorRecordCountRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetBeVisitorRecordCountResponse struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Growth               uint32   `protobuf:"varint,2,opt,name=growth,proto3" json:"growth,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordCountResponse) Reset()         { *m = GetBeVisitorRecordCountResponse{} }
func (m *GetBeVisitorRecordCountResponse) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordCountResponse) ProtoMessage()    {}
func (*GetBeVisitorRecordCountResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{24}
}
func (m *GetBeVisitorRecordCountResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordCountResponse.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordCountResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordCountResponse.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordCountResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordCountResponse.Merge(dst, src)
}
func (m *GetBeVisitorRecordCountResponse) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordCountResponse.Size(m)
}
func (m *GetBeVisitorRecordCountResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordCountResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordCountResponse proto.InternalMessageInfo

func (m *GetBeVisitorRecordCountResponse) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetBeVisitorRecordCountResponse) GetGrowth() uint32 {
	if m != nil {
		return m.Growth
	}
	return 0
}

type VisitRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	UpdateTime           int64    `protobuf:"varint,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	GameId               uint32   `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	LastOrderTs          int64    `protobuf:"varint,5,opt,name=last_order_ts,json=lastOrderTs,proto3" json:"last_order_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VisitRecord) Reset()         { *m = VisitRecord{} }
func (m *VisitRecord) String() string { return proto.CompactTextString(m) }
func (*VisitRecord) ProtoMessage()    {}
func (*VisitRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{25}
}
func (m *VisitRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VisitRecord.Unmarshal(m, b)
}
func (m *VisitRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VisitRecord.Marshal(b, m, deterministic)
}
func (dst *VisitRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VisitRecord.Merge(dst, src)
}
func (m *VisitRecord) XXX_Size() int {
	return xxx_messageInfo_VisitRecord.Size(m)
}
func (m *VisitRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_VisitRecord.DiscardUnknown(m)
}

var xxx_messageInfo_VisitRecord proto.InternalMessageInfo

func (m *VisitRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VisitRecord) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *VisitRecord) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *VisitRecord) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *VisitRecord) GetLastOrderTs() int64 {
	if m != nil {
		return m.LastOrderTs
	}
	return 0
}

type GetBeVisitorRecordListRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordListRequest) Reset()         { *m = GetBeVisitorRecordListRequest{} }
func (m *GetBeVisitorRecordListRequest) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordListRequest) ProtoMessage()    {}
func (*GetBeVisitorRecordListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{26}
}
func (m *GetBeVisitorRecordListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordListRequest.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordListRequest.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordListRequest.Merge(dst, src)
}
func (m *GetBeVisitorRecordListRequest) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordListRequest.Size(m)
}
func (m *GetBeVisitorRecordListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordListRequest proto.InternalMessageInfo

func (m *GetBeVisitorRecordListRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetBeVisitorRecordListRequest) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBeVisitorRecordListRequest) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBeVisitorRecordListResponse struct {
	RecordList []*VisitRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	NextOffset uint32         `protobuf:"varint,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	// 是否到达底部
	IsEnd                bool     `protobuf:"varint,3,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBeVisitorRecordListResponse) Reset()         { *m = GetBeVisitorRecordListResponse{} }
func (m *GetBeVisitorRecordListResponse) String() string { return proto.CompactTextString(m) }
func (*GetBeVisitorRecordListResponse) ProtoMessage()    {}
func (*GetBeVisitorRecordListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{27}
}
func (m *GetBeVisitorRecordListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBeVisitorRecordListResponse.Unmarshal(m, b)
}
func (m *GetBeVisitorRecordListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBeVisitorRecordListResponse.Marshal(b, m, deterministic)
}
func (dst *GetBeVisitorRecordListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBeVisitorRecordListResponse.Merge(dst, src)
}
func (m *GetBeVisitorRecordListResponse) XXX_Size() int {
	return xxx_messageInfo_GetBeVisitorRecordListResponse.Size(m)
}
func (m *GetBeVisitorRecordListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBeVisitorRecordListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetBeVisitorRecordListResponse proto.InternalMessageInfo

func (m *GetBeVisitorRecordListResponse) GetRecordList() []*VisitRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetBeVisitorRecordListResponse) GetNextOffset() uint32 {
	if m != nil {
		return m.NextOffset
	}
	return 0
}

func (m *GetBeVisitorRecordListResponse) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

type GetUserCoachLeastOrderRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CoachUid             uint32   `protobuf:"varint,2,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCoachLeastOrderRequest) Reset()         { *m = GetUserCoachLeastOrderRequest{} }
func (m *GetUserCoachLeastOrderRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserCoachLeastOrderRequest) ProtoMessage()    {}
func (*GetUserCoachLeastOrderRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{28}
}
func (m *GetUserCoachLeastOrderRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCoachLeastOrderRequest.Unmarshal(m, b)
}
func (m *GetUserCoachLeastOrderRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCoachLeastOrderRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserCoachLeastOrderRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCoachLeastOrderRequest.Merge(dst, src)
}
func (m *GetUserCoachLeastOrderRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserCoachLeastOrderRequest.Size(m)
}
func (m *GetUserCoachLeastOrderRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCoachLeastOrderRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCoachLeastOrderRequest proto.InternalMessageInfo

func (m *GetUserCoachLeastOrderRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCoachLeastOrderRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetUserCoachLeastOrderResponse struct {
	OrderTime            int64    `protobuf:"varint,1,opt,name=order_time,json=orderTime,proto3" json:"order_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserCoachLeastOrderResponse) Reset()         { *m = GetUserCoachLeastOrderResponse{} }
func (m *GetUserCoachLeastOrderResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserCoachLeastOrderResponse) ProtoMessage()    {}
func (*GetUserCoachLeastOrderResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{29}
}
func (m *GetUserCoachLeastOrderResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserCoachLeastOrderResponse.Unmarshal(m, b)
}
func (m *GetUserCoachLeastOrderResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserCoachLeastOrderResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserCoachLeastOrderResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserCoachLeastOrderResponse.Merge(dst, src)
}
func (m *GetUserCoachLeastOrderResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserCoachLeastOrderResponse.Size(m)
}
func (m *GetUserCoachLeastOrderResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserCoachLeastOrderResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserCoachLeastOrderResponse proto.InternalMessageInfo

func (m *GetUserCoachLeastOrderResponse) GetOrderTime() int64 {
	if m != nil {
		return m.OrderTime
	}
	return 0
}

type GetUserOrderCoachListRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StartTime            int64    `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOrderCoachListRequest) Reset()         { *m = GetUserOrderCoachListRequest{} }
func (m *GetUserOrderCoachListRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderCoachListRequest) ProtoMessage()    {}
func (*GetUserOrderCoachListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{30}
}
func (m *GetUserOrderCoachListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderCoachListRequest.Unmarshal(m, b)
}
func (m *GetUserOrderCoachListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderCoachListRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserOrderCoachListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderCoachListRequest.Merge(dst, src)
}
func (m *GetUserOrderCoachListRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderCoachListRequest.Size(m)
}
func (m *GetUserOrderCoachListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderCoachListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderCoachListRequest proto.InternalMessageInfo

func (m *GetUserOrderCoachListRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserOrderCoachListRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetUserOrderCoachListRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetUserOrderCoachListResponse struct {
	CoachOrderTimeMap    map[uint32]int64 `protobuf:"bytes,1,rep,name=coach_order_time_map,json=coachOrderTimeMap,proto3" json:"coach_order_time_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetUserOrderCoachListResponse) Reset()         { *m = GetUserOrderCoachListResponse{} }
func (m *GetUserOrderCoachListResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserOrderCoachListResponse) ProtoMessage()    {}
func (*GetUserOrderCoachListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{31}
}
func (m *GetUserOrderCoachListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOrderCoachListResponse.Unmarshal(m, b)
}
func (m *GetUserOrderCoachListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOrderCoachListResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserOrderCoachListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOrderCoachListResponse.Merge(dst, src)
}
func (m *GetUserOrderCoachListResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserOrderCoachListResponse.Size(m)
}
func (m *GetUserOrderCoachListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOrderCoachListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOrderCoachListResponse proto.InternalMessageInfo

func (m *GetUserOrderCoachListResponse) GetCoachOrderTimeMap() map[uint32]int64 {
	if m != nil {
		return m.CoachOrderTimeMap
	}
	return nil
}

type CoachConversionRate struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	ConversionRate       float64  `protobuf:"fixed64,2,opt,name=conversion_rate,json=conversionRate,proto3" json:"conversion_rate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachConversionRate) Reset()         { *m = CoachConversionRate{} }
func (m *CoachConversionRate) String() string { return proto.CompactTextString(m) }
func (*CoachConversionRate) ProtoMessage()    {}
func (*CoachConversionRate) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{32}
}
func (m *CoachConversionRate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachConversionRate.Unmarshal(m, b)
}
func (m *CoachConversionRate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachConversionRate.Marshal(b, m, deterministic)
}
func (dst *CoachConversionRate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachConversionRate.Merge(dst, src)
}
func (m *CoachConversionRate) XXX_Size() int {
	return xxx_messageInfo_CoachConversionRate.Size(m)
}
func (m *CoachConversionRate) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachConversionRate.DiscardUnknown(m)
}

var xxx_messageInfo_CoachConversionRate proto.InternalMessageInfo

func (m *CoachConversionRate) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *CoachConversionRate) GetConversionRate() float64 {
	if m != nil {
		return m.ConversionRate
	}
	return 0
}

type BatGetCoachConversionRateRequest struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetCoachConversionRateRequest) Reset()         { *m = BatGetCoachConversionRateRequest{} }
func (m *BatGetCoachConversionRateRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetCoachConversionRateRequest) ProtoMessage()    {}
func (*BatGetCoachConversionRateRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{33}
}
func (m *BatGetCoachConversionRateRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachConversionRateRequest.Unmarshal(m, b)
}
func (m *BatGetCoachConversionRateRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachConversionRateRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachConversionRateRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachConversionRateRequest.Merge(dst, src)
}
func (m *BatGetCoachConversionRateRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachConversionRateRequest.Size(m)
}
func (m *BatGetCoachConversionRateRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachConversionRateRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachConversionRateRequest proto.InternalMessageInfo

func (m *BatGetCoachConversionRateRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *BatGetCoachConversionRateRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type BatGetCoachConversionRateResponse struct {
	CoachConversionRateList []*CoachConversionRate `protobuf:"bytes,1,rep,name=coach_conversion_rate_list,json=coachConversionRateList,proto3" json:"coach_conversion_rate_list,omitempty"`
	XXX_NoUnkeyedLiteral    struct{}               `json:"-"`
	XXX_unrecognized        []byte                 `json:"-"`
	XXX_sizecache           int32                  `json:"-"`
}

func (m *BatGetCoachConversionRateResponse) Reset()         { *m = BatGetCoachConversionRateResponse{} }
func (m *BatGetCoachConversionRateResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetCoachConversionRateResponse) ProtoMessage()    {}
func (*BatGetCoachConversionRateResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{34}
}
func (m *BatGetCoachConversionRateResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachConversionRateResponse.Unmarshal(m, b)
}
func (m *BatGetCoachConversionRateResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachConversionRateResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachConversionRateResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachConversionRateResponse.Merge(dst, src)
}
func (m *BatGetCoachConversionRateResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachConversionRateResponse.Size(m)
}
func (m *BatGetCoachConversionRateResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachConversionRateResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachConversionRateResponse proto.InternalMessageInfo

func (m *BatGetCoachConversionRateResponse) GetCoachConversionRateList() []*CoachConversionRate {
	if m != nil {
		return m.CoachConversionRateList
	}
	return nil
}

type CoachOrderSum struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	Days14GameOrderSum   uint32   `protobuf:"varint,2,opt,name=days14_game_order_sum,json=days14GameOrderSum,proto3" json:"days14_game_order_sum,omitempty"`
	Days14AllOrderSum    uint32   `protobuf:"varint,3,opt,name=days14_all_order_sum,json=days14AllOrderSum,proto3" json:"days14_all_order_sum,omitempty"`
	HistoryGameOrderSum  uint32   `protobuf:"varint,4,opt,name=history_game_order_sum,json=historyGameOrderSum,proto3" json:"history_game_order_sum,omitempty"`
	HistoryAllOrderSum   uint32   `protobuf:"varint,5,opt,name=history_all_order_sum,json=historyAllOrderSum,proto3" json:"history_all_order_sum,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CoachOrderSum) Reset()         { *m = CoachOrderSum{} }
func (m *CoachOrderSum) String() string { return proto.CompactTextString(m) }
func (*CoachOrderSum) ProtoMessage()    {}
func (*CoachOrderSum) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{35}
}
func (m *CoachOrderSum) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachOrderSum.Unmarshal(m, b)
}
func (m *CoachOrderSum) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachOrderSum.Marshal(b, m, deterministic)
}
func (dst *CoachOrderSum) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachOrderSum.Merge(dst, src)
}
func (m *CoachOrderSum) XXX_Size() int {
	return xxx_messageInfo_CoachOrderSum.Size(m)
}
func (m *CoachOrderSum) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachOrderSum.DiscardUnknown(m)
}

var xxx_messageInfo_CoachOrderSum proto.InternalMessageInfo

func (m *CoachOrderSum) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *CoachOrderSum) GetDays14GameOrderSum() uint32 {
	if m != nil {
		return m.Days14GameOrderSum
	}
	return 0
}

func (m *CoachOrderSum) GetDays14AllOrderSum() uint32 {
	if m != nil {
		return m.Days14AllOrderSum
	}
	return 0
}

func (m *CoachOrderSum) GetHistoryGameOrderSum() uint32 {
	if m != nil {
		return m.HistoryGameOrderSum
	}
	return 0
}

func (m *CoachOrderSum) GetHistoryAllOrderSum() uint32 {
	if m != nil {
		return m.HistoryAllOrderSum
	}
	return 0
}

type BatGetCoachOrderSumRequest struct {
	CoachUidList         []uint32 `protobuf:"varint,1,rep,packed,name=coach_uid_list,json=coachUidList,proto3" json:"coach_uid_list,omitempty"`
	GameId               uint32   `protobuf:"varint,2,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetCoachOrderSumRequest) Reset()         { *m = BatGetCoachOrderSumRequest{} }
func (m *BatGetCoachOrderSumRequest) String() string { return proto.CompactTextString(m) }
func (*BatGetCoachOrderSumRequest) ProtoMessage()    {}
func (*BatGetCoachOrderSumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{36}
}
func (m *BatGetCoachOrderSumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachOrderSumRequest.Unmarshal(m, b)
}
func (m *BatGetCoachOrderSumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachOrderSumRequest.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachOrderSumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachOrderSumRequest.Merge(dst, src)
}
func (m *BatGetCoachOrderSumRequest) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachOrderSumRequest.Size(m)
}
func (m *BatGetCoachOrderSumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachOrderSumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachOrderSumRequest proto.InternalMessageInfo

func (m *BatGetCoachOrderSumRequest) GetCoachUidList() []uint32 {
	if m != nil {
		return m.CoachUidList
	}
	return nil
}

func (m *BatGetCoachOrderSumRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

type BatGetCoachOrderSumResponse struct {
	CoachOrderSumList    []*CoachOrderSum `protobuf:"bytes,1,rep,name=coach_order_sum_list,json=coachOrderSumList,proto3" json:"coach_order_sum_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *BatGetCoachOrderSumResponse) Reset()         { *m = BatGetCoachOrderSumResponse{} }
func (m *BatGetCoachOrderSumResponse) String() string { return proto.CompactTextString(m) }
func (*BatGetCoachOrderSumResponse) ProtoMessage()    {}
func (*BatGetCoachOrderSumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{37}
}
func (m *BatGetCoachOrderSumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetCoachOrderSumResponse.Unmarshal(m, b)
}
func (m *BatGetCoachOrderSumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetCoachOrderSumResponse.Marshal(b, m, deterministic)
}
func (dst *BatGetCoachOrderSumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetCoachOrderSumResponse.Merge(dst, src)
}
func (m *BatGetCoachOrderSumResponse) XXX_Size() int {
	return xxx_messageInfo_BatGetCoachOrderSumResponse.Size(m)
}
func (m *BatGetCoachOrderSumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetCoachOrderSumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetCoachOrderSumResponse proto.InternalMessageInfo

func (m *BatGetCoachOrderSumResponse) GetCoachOrderSumList() []*CoachOrderSum {
	if m != nil {
		return m.CoachOrderSumList
	}
	return nil
}

type RebuildUserOrderCoachListRequest struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildUserOrderCoachListRequest) Reset()         { *m = RebuildUserOrderCoachListRequest{} }
func (m *RebuildUserOrderCoachListRequest) String() string { return proto.CompactTextString(m) }
func (*RebuildUserOrderCoachListRequest) ProtoMessage()    {}
func (*RebuildUserOrderCoachListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{38}
}
func (m *RebuildUserOrderCoachListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildUserOrderCoachListRequest.Unmarshal(m, b)
}
func (m *RebuildUserOrderCoachListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildUserOrderCoachListRequest.Marshal(b, m, deterministic)
}
func (dst *RebuildUserOrderCoachListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildUserOrderCoachListRequest.Merge(dst, src)
}
func (m *RebuildUserOrderCoachListRequest) XXX_Size() int {
	return xxx_messageInfo_RebuildUserOrderCoachListRequest.Size(m)
}
func (m *RebuildUserOrderCoachListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildUserOrderCoachListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildUserOrderCoachListRequest proto.InternalMessageInfo

type RebuildUserOrderCoachListResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildUserOrderCoachListResponse) Reset()         { *m = RebuildUserOrderCoachListResponse{} }
func (m *RebuildUserOrderCoachListResponse) String() string { return proto.CompactTextString(m) }
func (*RebuildUserOrderCoachListResponse) ProtoMessage()    {}
func (*RebuildUserOrderCoachListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{39}
}
func (m *RebuildUserOrderCoachListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildUserOrderCoachListResponse.Unmarshal(m, b)
}
func (m *RebuildUserOrderCoachListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildUserOrderCoachListResponse.Marshal(b, m, deterministic)
}
func (dst *RebuildUserOrderCoachListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildUserOrderCoachListResponse.Merge(dst, src)
}
func (m *RebuildUserOrderCoachListResponse) XXX_Size() int {
	return xxx_messageInfo_RebuildUserOrderCoachListResponse.Size(m)
}
func (m *RebuildUserOrderCoachListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildUserOrderCoachListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildUserOrderCoachListResponse proto.InternalMessageInfo

type GetCoachOrderRunningValueRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	StartTime            int64    `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachOrderRunningValueRequest) Reset()         { *m = GetCoachOrderRunningValueRequest{} }
func (m *GetCoachOrderRunningValueRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachOrderRunningValueRequest) ProtoMessage()    {}
func (*GetCoachOrderRunningValueRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{40}
}
func (m *GetCoachOrderRunningValueRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachOrderRunningValueRequest.Unmarshal(m, b)
}
func (m *GetCoachOrderRunningValueRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachOrderRunningValueRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachOrderRunningValueRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachOrderRunningValueRequest.Merge(dst, src)
}
func (m *GetCoachOrderRunningValueRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachOrderRunningValueRequest.Size(m)
}
func (m *GetCoachOrderRunningValueRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachOrderRunningValueRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachOrderRunningValueRequest proto.InternalMessageInfo

func (m *GetCoachOrderRunningValueRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetCoachOrderRunningValueRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCoachOrderRunningValueRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetCoachOrderRunningValueResponse struct {
	RunningValue         uint32   `protobuf:"varint,1,opt,name=running_value,json=runningValue,proto3" json:"running_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachOrderRunningValueResponse) Reset()         { *m = GetCoachOrderRunningValueResponse{} }
func (m *GetCoachOrderRunningValueResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachOrderRunningValueResponse) ProtoMessage()    {}
func (*GetCoachOrderRunningValueResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{41}
}
func (m *GetCoachOrderRunningValueResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachOrderRunningValueResponse.Unmarshal(m, b)
}
func (m *GetCoachOrderRunningValueResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachOrderRunningValueResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachOrderRunningValueResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachOrderRunningValueResponse.Merge(dst, src)
}
func (m *GetCoachOrderRunningValueResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachOrderRunningValueResponse.Size(m)
}
func (m *GetCoachOrderRunningValueResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachOrderRunningValueResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachOrderRunningValueResponse proto.InternalMessageInfo

func (m *GetCoachOrderRunningValueResponse) GetRunningValue() uint32 {
	if m != nil {
		return m.RunningValue
	}
	return 0
}

type GetCoachCustomerNumRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	StartTime            int64    `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachCustomerNumRequest) Reset()         { *m = GetCoachCustomerNumRequest{} }
func (m *GetCoachCustomerNumRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachCustomerNumRequest) ProtoMessage()    {}
func (*GetCoachCustomerNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{42}
}
func (m *GetCoachCustomerNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachCustomerNumRequest.Unmarshal(m, b)
}
func (m *GetCoachCustomerNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachCustomerNumRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachCustomerNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachCustomerNumRequest.Merge(dst, src)
}
func (m *GetCoachCustomerNumRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachCustomerNumRequest.Size(m)
}
func (m *GetCoachCustomerNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachCustomerNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachCustomerNumRequest proto.InternalMessageInfo

func (m *GetCoachCustomerNumRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

func (m *GetCoachCustomerNumRequest) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCoachCustomerNumRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetCoachCustomerNumResponse struct {
	CustomerNum          uint32   `protobuf:"varint,1,opt,name=customer_num,json=customerNum,proto3" json:"customer_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachCustomerNumResponse) Reset()         { *m = GetCoachCustomerNumResponse{} }
func (m *GetCoachCustomerNumResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachCustomerNumResponse) ProtoMessage()    {}
func (*GetCoachCustomerNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{43}
}
func (m *GetCoachCustomerNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachCustomerNumResponse.Unmarshal(m, b)
}
func (m *GetCoachCustomerNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachCustomerNumResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachCustomerNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachCustomerNumResponse.Merge(dst, src)
}
func (m *GetCoachCustomerNumResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachCustomerNumResponse.Size(m)
}
func (m *GetCoachCustomerNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachCustomerNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachCustomerNumResponse proto.InternalMessageInfo

func (m *GetCoachCustomerNumResponse) GetCustomerNum() uint32 {
	if m != nil {
		return m.CustomerNum
	}
	return 0
}

// 获取大神今日访问量
type GetCoachTodayUvRequest struct {
	CoachUid             uint32   `protobuf:"varint,1,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachTodayUvRequest) Reset()         { *m = GetCoachTodayUvRequest{} }
func (m *GetCoachTodayUvRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachTodayUvRequest) ProtoMessage()    {}
func (*GetCoachTodayUvRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{44}
}
func (m *GetCoachTodayUvRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachTodayUvRequest.Unmarshal(m, b)
}
func (m *GetCoachTodayUvRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachTodayUvRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachTodayUvRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachTodayUvRequest.Merge(dst, src)
}
func (m *GetCoachTodayUvRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachTodayUvRequest.Size(m)
}
func (m *GetCoachTodayUvRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachTodayUvRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachTodayUvRequest proto.InternalMessageInfo

func (m *GetCoachTodayUvRequest) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

// 获取大神今日访问量
type GetCoachTodayUvResponse struct {
	Uv                   uint32   `protobuf:"varint,1,opt,name=uv,proto3" json:"uv,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachTodayUvResponse) Reset()         { *m = GetCoachTodayUvResponse{} }
func (m *GetCoachTodayUvResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachTodayUvResponse) ProtoMessage()    {}
func (*GetCoachTodayUvResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_statistics_9e077c1b611c835d, []int{45}
}
func (m *GetCoachTodayUvResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachTodayUvResponse.Unmarshal(m, b)
}
func (m *GetCoachTodayUvResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachTodayUvResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachTodayUvResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachTodayUvResponse.Merge(dst, src)
}
func (m *GetCoachTodayUvResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachTodayUvResponse.Size(m)
}
func (m *GetCoachTodayUvResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachTodayUvResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachTodayUvResponse proto.InternalMessageInfo

func (m *GetCoachTodayUvResponse) GetUv() uint32 {
	if m != nil {
		return m.Uv
	}
	return 0
}

func init() {
	proto.RegisterType((*BatchGetUserFirstTakeOrdersTimeRequest)(nil), "esport_statistics.BatchGetUserFirstTakeOrdersTimeRequest")
	proto.RegisterType((*BatchGetUserFirstTakeOrdersTimeResponse)(nil), "esport_statistics.BatchGetUserFirstTakeOrdersTimeResponse")
	proto.RegisterMapType((map[uint32]int64)(nil), "esport_statistics.BatchGetUserFirstTakeOrdersTimeResponse.TimeMapEntry")
	proto.RegisterType((*GetCoachStatisticsRequest)(nil), "esport_statistics.GetCoachStatisticsRequest")
	proto.RegisterType((*StatisticsData)(nil), "esport_statistics.StatisticsData")
	proto.RegisterType((*GetCoachStatisticsResponse)(nil), "esport_statistics.GetCoachStatisticsResponse")
	proto.RegisterType((*GetCoachAllStatisticsRequest)(nil), "esport_statistics.GetCoachAllStatisticsRequest")
	proto.RegisterType((*GetCoachAllStatisticsResponse)(nil), "esport_statistics.GetCoachAllStatisticsResponse")
	proto.RegisterMapType((map[uint32]*StatisticsData)(nil), "esport_statistics.GetCoachAllStatisticsResponse.GameDataMapEntry")
	proto.RegisterType((*GetUserMostPayGameRequest)(nil), "esport_statistics.GetUserMostPayGameRequest")
	proto.RegisterType((*GetUserMostPayGameResponse)(nil), "esport_statistics.GetUserMostPayGameResponse")
	proto.RegisterType((*HandleRecallCoachRequest)(nil), "esport_statistics.HandleRecallCoachRequest")
	proto.RegisterType((*HandleRecallCoachResponse)(nil), "esport_statistics.HandleRecallCoachResponse")
	proto.RegisterType((*BatchGetCoachDurationOrderNumRequest)(nil), "esport_statistics.BatchGetCoachDurationOrderNumRequest")
	proto.RegisterType((*BatchGetCoachDurationOrderNumResponse)(nil), "esport_statistics.BatchGetCoachDurationOrderNumResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_statistics.BatchGetCoachDurationOrderNumResponse.OrderNumMapEntry")
	proto.RegisterType((*BatchGetOrderCntByGameIdRequest)(nil), "esport_statistics.BatchGetOrderCntByGameIdRequest")
	proto.RegisterType((*BatchGetOrderCntByGameIdResponse)(nil), "esport_statistics.BatchGetOrderCntByGameIdResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_statistics.BatchGetOrderCntByGameIdResponse.OrderNumMapEntry")
	proto.RegisterType((*GetUserTotalExpenditureAmountMonthlyRequest)(nil), "esport_statistics.GetUserTotalExpenditureAmountMonthlyRequest")
	proto.RegisterType((*GetUserTotalExpenditureAmountMonthlyResponse)(nil), "esport_statistics.GetUserTotalExpenditureAmountMonthlyResponse")
	proto.RegisterType((*GetUserCoachLatestOrderTimeRequest)(nil), "esport_statistics.GetUserCoachLatestOrderTimeRequest")
	proto.RegisterType((*GetUserCoachLatestOrderTimeResponse)(nil), "esport_statistics.GetUserCoachLatestOrderTimeResponse")
	proto.RegisterType((*ReportUserVisitIMPageRequest)(nil), "esport_statistics.ReportUserVisitIMPageRequest")
	proto.RegisterType((*ReportUserVisitIMPageResponse)(nil), "esport_statistics.ReportUserVisitIMPageResponse")
	proto.RegisterType((*ReportUserVisitGameCardRequest)(nil), "esport_statistics.ReportUserVisitGameCardRequest")
	proto.RegisterType((*ReportUserVisitGameCardResponse)(nil), "esport_statistics.ReportUserVisitGameCardResponse")
	proto.RegisterType((*GetBeVisitorRecordCountRequest)(nil), "esport_statistics.GetBeVisitorRecordCountRequest")
	proto.RegisterType((*GetBeVisitorRecordCountResponse)(nil), "esport_statistics.GetBeVisitorRecordCountResponse")
	proto.RegisterType((*VisitRecord)(nil), "esport_statistics.VisitRecord")
	proto.RegisterType((*GetBeVisitorRecordListRequest)(nil), "esport_statistics.GetBeVisitorRecordListRequest")
	proto.RegisterType((*GetBeVisitorRecordListResponse)(nil), "esport_statistics.GetBeVisitorRecordListResponse")
	proto.RegisterType((*GetUserCoachLeastOrderRequest)(nil), "esport_statistics.GetUserCoachLeastOrderRequest")
	proto.RegisterType((*GetUserCoachLeastOrderResponse)(nil), "esport_statistics.GetUserCoachLeastOrderResponse")
	proto.RegisterType((*GetUserOrderCoachListRequest)(nil), "esport_statistics.GetUserOrderCoachListRequest")
	proto.RegisterType((*GetUserOrderCoachListResponse)(nil), "esport_statistics.GetUserOrderCoachListResponse")
	proto.RegisterMapType((map[uint32]int64)(nil), "esport_statistics.GetUserOrderCoachListResponse.CoachOrderTimeMapEntry")
	proto.RegisterType((*CoachConversionRate)(nil), "esport_statistics.CoachConversionRate")
	proto.RegisterType((*BatGetCoachConversionRateRequest)(nil), "esport_statistics.BatGetCoachConversionRateRequest")
	proto.RegisterType((*BatGetCoachConversionRateResponse)(nil), "esport_statistics.BatGetCoachConversionRateResponse")
	proto.RegisterType((*CoachOrderSum)(nil), "esport_statistics.CoachOrderSum")
	proto.RegisterType((*BatGetCoachOrderSumRequest)(nil), "esport_statistics.BatGetCoachOrderSumRequest")
	proto.RegisterType((*BatGetCoachOrderSumResponse)(nil), "esport_statistics.BatGetCoachOrderSumResponse")
	proto.RegisterType((*RebuildUserOrderCoachListRequest)(nil), "esport_statistics.RebuildUserOrderCoachListRequest")
	proto.RegisterType((*RebuildUserOrderCoachListResponse)(nil), "esport_statistics.RebuildUserOrderCoachListResponse")
	proto.RegisterType((*GetCoachOrderRunningValueRequest)(nil), "esport_statistics.GetCoachOrderRunningValueRequest")
	proto.RegisterType((*GetCoachOrderRunningValueResponse)(nil), "esport_statistics.GetCoachOrderRunningValueResponse")
	proto.RegisterType((*GetCoachCustomerNumRequest)(nil), "esport_statistics.GetCoachCustomerNumRequest")
	proto.RegisterType((*GetCoachCustomerNumResponse)(nil), "esport_statistics.GetCoachCustomerNumResponse")
	proto.RegisterType((*GetCoachTodayUvRequest)(nil), "esport_statistics.GetCoachTodayUvRequest")
	proto.RegisterType((*GetCoachTodayUvResponse)(nil), "esport_statistics.GetCoachTodayUvResponse")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EsportStatisticsClient is the client API for EsportStatistics service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EsportStatisticsClient interface {
	// 批量获取用户首次接单时间
	BatchGetUserFirstTakeOrdersTime(ctx context.Context, in *BatchGetUserFirstTakeOrdersTimeRequest, opts ...grpc.CallOption) (*BatchGetUserFirstTakeOrdersTimeResponse, error)
	// 获取电竞指导接单统计数据（单个game_id）
	GetCoachStatistics(ctx context.Context, in *GetCoachStatisticsRequest, opts ...grpc.CallOption) (*GetCoachStatisticsResponse, error)
	// 获取用户最多下单的游戏
	GetUserMostPayGame(ctx context.Context, in *GetUserMostPayGameRequest, opts ...grpc.CallOption) (*GetUserMostPayGameResponse, error)
	// 获取电竞指导全部游戏统计数据
	GetCoachAllStatistics(ctx context.Context, in *GetCoachAllStatisticsRequest, opts ...grpc.CallOption) (*GetCoachAllStatisticsResponse, error)
	// 处理电竞指导身份回收
	HandleRecallCoach(ctx context.Context, in *HandleRecallCoachRequest, opts ...grpc.CallOption) (*HandleRecallCoachResponse, error)
	// 批量获取电竞指导周期内接单数量
	BatchGetCoachDurationOrderNum(ctx context.Context, in *BatchGetCoachDurationOrderNumRequest, opts ...grpc.CallOption) (*BatchGetCoachDurationOrderNumResponse, error)
	// 批量获取电竞指导接单数量
	BatchGetOrderCntByGameId(ctx context.Context, in *BatchGetOrderCntByGameIdRequest, opts ...grpc.CallOption) (*BatchGetOrderCntByGameIdResponse, error)
	// 获取用户月度总消费金额
	GetUserTotalExpenditureAmountMonthly(ctx context.Context, in *GetUserTotalExpenditureAmountMonthlyRequest, opts ...grpc.CallOption) (*GetUserTotalExpenditureAmountMonthlyResponse, error)
	// esport-visit 相关（大神谁看过我 + 从游戏卡片进入IM页）
	// 上报用户访问大神IM页
	ReportUserVisitIMPage(ctx context.Context, in *ReportUserVisitIMPageRequest, opts ...grpc.CallOption) (*ReportUserVisitIMPageResponse, error)
	// 上报用户访问大神游戏详情卡片
	ReportUserVisitGameCard(ctx context.Context, in *ReportUserVisitGameCardRequest, opts ...grpc.CallOption) (*ReportUserVisitGameCardResponse, error)
	// 获取大神 谁看过我summary数据
	GetBeVisitorRecordCount(ctx context.Context, in *GetBeVisitorRecordCountRequest, opts ...grpc.CallOption) (*GetBeVisitorRecordCountResponse, error)
	// 获取大神 谁看过我记录
	GetBeVisitorRecordList(ctx context.Context, in *GetBeVisitorRecordListRequest, opts ...grpc.CallOption) (*GetBeVisitorRecordListResponse, error)
	// 获取指定用户给指定大神最新一次的下单记录
	GetUserCoachLeastOrder(ctx context.Context, in *GetUserCoachLeastOrderRequest, opts ...grpc.CallOption) (*GetUserCoachLeastOrderResponse, error)
	// 获取用户在指定时间段内下过单的大神
	GetUserOrderCoachList(ctx context.Context, in *GetUserOrderCoachListRequest, opts ...grpc.CallOption) (*GetUserOrderCoachListResponse, error)
	// 批量获取电竞指导转化率
	BatGetCoachConversionRate(ctx context.Context, in *BatGetCoachConversionRateRequest, opts ...grpc.CallOption) (*BatGetCoachConversionRateResponse, error)
	// 批量获取电竞指导订单量统计
	BatGetCoachOrderSum(ctx context.Context, in *BatGetCoachOrderSumRequest, opts ...grpc.CallOption) (*BatGetCoachOrderSumResponse, error)
	// 重建用户下过单的记录
	RebuildUserOrderCoachList(ctx context.Context, in *RebuildUserOrderCoachListRequest, opts ...grpc.CallOption) (*RebuildUserOrderCoachListResponse, error)
	// 获取电竞指导流水值
	GetCoachOrderRunningValue(ctx context.Context, in *GetCoachOrderRunningValueRequest, opts ...grpc.CallOption) (*GetCoachOrderRunningValueResponse, error)
	// 获取电竞指导老板数
	GetCoachCustomerNum(ctx context.Context, in *GetCoachCustomerNumRequest, opts ...grpc.CallOption) (*GetCoachCustomerNumResponse, error)
	// 获取大神今日访问量
	GetCoachTodayUv(ctx context.Context, in *GetCoachTodayUvRequest, opts ...grpc.CallOption) (*GetCoachTodayUvResponse, error)
}

type esportStatisticsClient struct {
	cc *grpc.ClientConn
}

func NewEsportStatisticsClient(cc *grpc.ClientConn) EsportStatisticsClient {
	return &esportStatisticsClient{cc}
}

func (c *esportStatisticsClient) BatchGetUserFirstTakeOrdersTime(ctx context.Context, in *BatchGetUserFirstTakeOrdersTimeRequest, opts ...grpc.CallOption) (*BatchGetUserFirstTakeOrdersTimeResponse, error) {
	out := new(BatchGetUserFirstTakeOrdersTimeResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/BatchGetUserFirstTakeOrdersTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetCoachStatistics(ctx context.Context, in *GetCoachStatisticsRequest, opts ...grpc.CallOption) (*GetCoachStatisticsResponse, error) {
	out := new(GetCoachStatisticsResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetCoachStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetUserMostPayGame(ctx context.Context, in *GetUserMostPayGameRequest, opts ...grpc.CallOption) (*GetUserMostPayGameResponse, error) {
	out := new(GetUserMostPayGameResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetUserMostPayGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetCoachAllStatistics(ctx context.Context, in *GetCoachAllStatisticsRequest, opts ...grpc.CallOption) (*GetCoachAllStatisticsResponse, error) {
	out := new(GetCoachAllStatisticsResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetCoachAllStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) HandleRecallCoach(ctx context.Context, in *HandleRecallCoachRequest, opts ...grpc.CallOption) (*HandleRecallCoachResponse, error) {
	out := new(HandleRecallCoachResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/HandleRecallCoach", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) BatchGetCoachDurationOrderNum(ctx context.Context, in *BatchGetCoachDurationOrderNumRequest, opts ...grpc.CallOption) (*BatchGetCoachDurationOrderNumResponse, error) {
	out := new(BatchGetCoachDurationOrderNumResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/BatchGetCoachDurationOrderNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) BatchGetOrderCntByGameId(ctx context.Context, in *BatchGetOrderCntByGameIdRequest, opts ...grpc.CallOption) (*BatchGetOrderCntByGameIdResponse, error) {
	out := new(BatchGetOrderCntByGameIdResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/BatchGetOrderCntByGameId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetUserTotalExpenditureAmountMonthly(ctx context.Context, in *GetUserTotalExpenditureAmountMonthlyRequest, opts ...grpc.CallOption) (*GetUserTotalExpenditureAmountMonthlyResponse, error) {
	out := new(GetUserTotalExpenditureAmountMonthlyResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetUserTotalExpenditureAmountMonthly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) ReportUserVisitIMPage(ctx context.Context, in *ReportUserVisitIMPageRequest, opts ...grpc.CallOption) (*ReportUserVisitIMPageResponse, error) {
	out := new(ReportUserVisitIMPageResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/ReportUserVisitIMPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) ReportUserVisitGameCard(ctx context.Context, in *ReportUserVisitGameCardRequest, opts ...grpc.CallOption) (*ReportUserVisitGameCardResponse, error) {
	out := new(ReportUserVisitGameCardResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/ReportUserVisitGameCard", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetBeVisitorRecordCount(ctx context.Context, in *GetBeVisitorRecordCountRequest, opts ...grpc.CallOption) (*GetBeVisitorRecordCountResponse, error) {
	out := new(GetBeVisitorRecordCountResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetBeVisitorRecordCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetBeVisitorRecordList(ctx context.Context, in *GetBeVisitorRecordListRequest, opts ...grpc.CallOption) (*GetBeVisitorRecordListResponse, error) {
	out := new(GetBeVisitorRecordListResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetBeVisitorRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetUserCoachLeastOrder(ctx context.Context, in *GetUserCoachLeastOrderRequest, opts ...grpc.CallOption) (*GetUserCoachLeastOrderResponse, error) {
	out := new(GetUserCoachLeastOrderResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetUserCoachLeastOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetUserOrderCoachList(ctx context.Context, in *GetUserOrderCoachListRequest, opts ...grpc.CallOption) (*GetUserOrderCoachListResponse, error) {
	out := new(GetUserOrderCoachListResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetUserOrderCoachList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) BatGetCoachConversionRate(ctx context.Context, in *BatGetCoachConversionRateRequest, opts ...grpc.CallOption) (*BatGetCoachConversionRateResponse, error) {
	out := new(BatGetCoachConversionRateResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/BatGetCoachConversionRate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) BatGetCoachOrderSum(ctx context.Context, in *BatGetCoachOrderSumRequest, opts ...grpc.CallOption) (*BatGetCoachOrderSumResponse, error) {
	out := new(BatGetCoachOrderSumResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/BatGetCoachOrderSum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) RebuildUserOrderCoachList(ctx context.Context, in *RebuildUserOrderCoachListRequest, opts ...grpc.CallOption) (*RebuildUserOrderCoachListResponse, error) {
	out := new(RebuildUserOrderCoachListResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/RebuildUserOrderCoachList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetCoachOrderRunningValue(ctx context.Context, in *GetCoachOrderRunningValueRequest, opts ...grpc.CallOption) (*GetCoachOrderRunningValueResponse, error) {
	out := new(GetCoachOrderRunningValueResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetCoachOrderRunningValue", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetCoachCustomerNum(ctx context.Context, in *GetCoachCustomerNumRequest, opts ...grpc.CallOption) (*GetCoachCustomerNumResponse, error) {
	out := new(GetCoachCustomerNumResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetCoachCustomerNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *esportStatisticsClient) GetCoachTodayUv(ctx context.Context, in *GetCoachTodayUvRequest, opts ...grpc.CallOption) (*GetCoachTodayUvResponse, error) {
	out := new(GetCoachTodayUvResponse)
	err := c.cc.Invoke(ctx, "/esport_statistics.EsportStatistics/GetCoachTodayUv", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EsportStatisticsServer is the server API for EsportStatistics service.
type EsportStatisticsServer interface {
	// 批量获取用户首次接单时间
	BatchGetUserFirstTakeOrdersTime(context.Context, *BatchGetUserFirstTakeOrdersTimeRequest) (*BatchGetUserFirstTakeOrdersTimeResponse, error)
	// 获取电竞指导接单统计数据（单个game_id）
	GetCoachStatistics(context.Context, *GetCoachStatisticsRequest) (*GetCoachStatisticsResponse, error)
	// 获取用户最多下单的游戏
	GetUserMostPayGame(context.Context, *GetUserMostPayGameRequest) (*GetUserMostPayGameResponse, error)
	// 获取电竞指导全部游戏统计数据
	GetCoachAllStatistics(context.Context, *GetCoachAllStatisticsRequest) (*GetCoachAllStatisticsResponse, error)
	// 处理电竞指导身份回收
	HandleRecallCoach(context.Context, *HandleRecallCoachRequest) (*HandleRecallCoachResponse, error)
	// 批量获取电竞指导周期内接单数量
	BatchGetCoachDurationOrderNum(context.Context, *BatchGetCoachDurationOrderNumRequest) (*BatchGetCoachDurationOrderNumResponse, error)
	// 批量获取电竞指导接单数量
	BatchGetOrderCntByGameId(context.Context, *BatchGetOrderCntByGameIdRequest) (*BatchGetOrderCntByGameIdResponse, error)
	// 获取用户月度总消费金额
	GetUserTotalExpenditureAmountMonthly(context.Context, *GetUserTotalExpenditureAmountMonthlyRequest) (*GetUserTotalExpenditureAmountMonthlyResponse, error)
	// esport-visit 相关（大神谁看过我 + 从游戏卡片进入IM页）
	// 上报用户访问大神IM页
	ReportUserVisitIMPage(context.Context, *ReportUserVisitIMPageRequest) (*ReportUserVisitIMPageResponse, error)
	// 上报用户访问大神游戏详情卡片
	ReportUserVisitGameCard(context.Context, *ReportUserVisitGameCardRequest) (*ReportUserVisitGameCardResponse, error)
	// 获取大神 谁看过我summary数据
	GetBeVisitorRecordCount(context.Context, *GetBeVisitorRecordCountRequest) (*GetBeVisitorRecordCountResponse, error)
	// 获取大神 谁看过我记录
	GetBeVisitorRecordList(context.Context, *GetBeVisitorRecordListRequest) (*GetBeVisitorRecordListResponse, error)
	// 获取指定用户给指定大神最新一次的下单记录
	GetUserCoachLeastOrder(context.Context, *GetUserCoachLeastOrderRequest) (*GetUserCoachLeastOrderResponse, error)
	// 获取用户在指定时间段内下过单的大神
	GetUserOrderCoachList(context.Context, *GetUserOrderCoachListRequest) (*GetUserOrderCoachListResponse, error)
	// 批量获取电竞指导转化率
	BatGetCoachConversionRate(context.Context, *BatGetCoachConversionRateRequest) (*BatGetCoachConversionRateResponse, error)
	// 批量获取电竞指导订单量统计
	BatGetCoachOrderSum(context.Context, *BatGetCoachOrderSumRequest) (*BatGetCoachOrderSumResponse, error)
	// 重建用户下过单的记录
	RebuildUserOrderCoachList(context.Context, *RebuildUserOrderCoachListRequest) (*RebuildUserOrderCoachListResponse, error)
	// 获取电竞指导流水值
	GetCoachOrderRunningValue(context.Context, *GetCoachOrderRunningValueRequest) (*GetCoachOrderRunningValueResponse, error)
	// 获取电竞指导老板数
	GetCoachCustomerNum(context.Context, *GetCoachCustomerNumRequest) (*GetCoachCustomerNumResponse, error)
	// 获取大神今日访问量
	GetCoachTodayUv(context.Context, *GetCoachTodayUvRequest) (*GetCoachTodayUvResponse, error)
}

func RegisterEsportStatisticsServer(s *grpc.Server, srv EsportStatisticsServer) {
	s.RegisterService(&_EsportStatistics_serviceDesc, srv)
}

func _EsportStatistics_BatchGetUserFirstTakeOrdersTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserFirstTakeOrdersTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).BatchGetUserFirstTakeOrdersTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/BatchGetUserFirstTakeOrdersTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).BatchGetUserFirstTakeOrdersTime(ctx, req.(*BatchGetUserFirstTakeOrdersTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetCoachStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetCoachStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetCoachStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetCoachStatistics(ctx, req.(*GetCoachStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetUserMostPayGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMostPayGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetUserMostPayGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetUserMostPayGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetUserMostPayGame(ctx, req.(*GetUserMostPayGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetCoachAllStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachAllStatisticsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetCoachAllStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetCoachAllStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetCoachAllStatistics(ctx, req.(*GetCoachAllStatisticsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_HandleRecallCoach_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleRecallCoachRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).HandleRecallCoach(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/HandleRecallCoach",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).HandleRecallCoach(ctx, req.(*HandleRecallCoachRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_BatchGetCoachDurationOrderNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCoachDurationOrderNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).BatchGetCoachDurationOrderNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/BatchGetCoachDurationOrderNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).BatchGetCoachDurationOrderNum(ctx, req.(*BatchGetCoachDurationOrderNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_BatchGetOrderCntByGameId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetOrderCntByGameIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).BatchGetOrderCntByGameId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/BatchGetOrderCntByGameId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).BatchGetOrderCntByGameId(ctx, req.(*BatchGetOrderCntByGameIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetUserTotalExpenditureAmountMonthly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTotalExpenditureAmountMonthlyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetUserTotalExpenditureAmountMonthly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetUserTotalExpenditureAmountMonthly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetUserTotalExpenditureAmountMonthly(ctx, req.(*GetUserTotalExpenditureAmountMonthlyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_ReportUserVisitIMPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserVisitIMPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).ReportUserVisitIMPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/ReportUserVisitIMPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).ReportUserVisitIMPage(ctx, req.(*ReportUserVisitIMPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_ReportUserVisitGameCard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportUserVisitGameCardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).ReportUserVisitGameCard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/ReportUserVisitGameCard",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).ReportUserVisitGameCard(ctx, req.(*ReportUserVisitGameCardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetBeVisitorRecordCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBeVisitorRecordCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetBeVisitorRecordCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetBeVisitorRecordCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetBeVisitorRecordCount(ctx, req.(*GetBeVisitorRecordCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetBeVisitorRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBeVisitorRecordListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetBeVisitorRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetBeVisitorRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetBeVisitorRecordList(ctx, req.(*GetBeVisitorRecordListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetUserCoachLeastOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCoachLeastOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetUserCoachLeastOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetUserCoachLeastOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetUserCoachLeastOrder(ctx, req.(*GetUserCoachLeastOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetUserOrderCoachList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOrderCoachListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetUserOrderCoachList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetUserOrderCoachList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetUserOrderCoachList(ctx, req.(*GetUserOrderCoachListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_BatGetCoachConversionRate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetCoachConversionRateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).BatGetCoachConversionRate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/BatGetCoachConversionRate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).BatGetCoachConversionRate(ctx, req.(*BatGetCoachConversionRateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_BatGetCoachOrderSum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetCoachOrderSumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).BatGetCoachOrderSum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/BatGetCoachOrderSum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).BatGetCoachOrderSum(ctx, req.(*BatGetCoachOrderSumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_RebuildUserOrderCoachList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RebuildUserOrderCoachListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).RebuildUserOrderCoachList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/RebuildUserOrderCoachList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).RebuildUserOrderCoachList(ctx, req.(*RebuildUserOrderCoachListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetCoachOrderRunningValue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachOrderRunningValueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetCoachOrderRunningValue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetCoachOrderRunningValue",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetCoachOrderRunningValue(ctx, req.(*GetCoachOrderRunningValueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetCoachCustomerNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachCustomerNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetCoachCustomerNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetCoachCustomerNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetCoachCustomerNum(ctx, req.(*GetCoachCustomerNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EsportStatistics_GetCoachTodayUv_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachTodayUvRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EsportStatisticsServer).GetCoachTodayUv(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_statistics.EsportStatistics/GetCoachTodayUv",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EsportStatisticsServer).GetCoachTodayUv(ctx, req.(*GetCoachTodayUvRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EsportStatistics_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_statistics.EsportStatistics",
	HandlerType: (*EsportStatisticsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetUserFirstTakeOrdersTime",
			Handler:    _EsportStatistics_BatchGetUserFirstTakeOrdersTime_Handler,
		},
		{
			MethodName: "GetCoachStatistics",
			Handler:    _EsportStatistics_GetCoachStatistics_Handler,
		},
		{
			MethodName: "GetUserMostPayGame",
			Handler:    _EsportStatistics_GetUserMostPayGame_Handler,
		},
		{
			MethodName: "GetCoachAllStatistics",
			Handler:    _EsportStatistics_GetCoachAllStatistics_Handler,
		},
		{
			MethodName: "HandleRecallCoach",
			Handler:    _EsportStatistics_HandleRecallCoach_Handler,
		},
		{
			MethodName: "BatchGetCoachDurationOrderNum",
			Handler:    _EsportStatistics_BatchGetCoachDurationOrderNum_Handler,
		},
		{
			MethodName: "BatchGetOrderCntByGameId",
			Handler:    _EsportStatistics_BatchGetOrderCntByGameId_Handler,
		},
		{
			MethodName: "GetUserTotalExpenditureAmountMonthly",
			Handler:    _EsportStatistics_GetUserTotalExpenditureAmountMonthly_Handler,
		},
		{
			MethodName: "ReportUserVisitIMPage",
			Handler:    _EsportStatistics_ReportUserVisitIMPage_Handler,
		},
		{
			MethodName: "ReportUserVisitGameCard",
			Handler:    _EsportStatistics_ReportUserVisitGameCard_Handler,
		},
		{
			MethodName: "GetBeVisitorRecordCount",
			Handler:    _EsportStatistics_GetBeVisitorRecordCount_Handler,
		},
		{
			MethodName: "GetBeVisitorRecordList",
			Handler:    _EsportStatistics_GetBeVisitorRecordList_Handler,
		},
		{
			MethodName: "GetUserCoachLeastOrder",
			Handler:    _EsportStatistics_GetUserCoachLeastOrder_Handler,
		},
		{
			MethodName: "GetUserOrderCoachList",
			Handler:    _EsportStatistics_GetUserOrderCoachList_Handler,
		},
		{
			MethodName: "BatGetCoachConversionRate",
			Handler:    _EsportStatistics_BatGetCoachConversionRate_Handler,
		},
		{
			MethodName: "BatGetCoachOrderSum",
			Handler:    _EsportStatistics_BatGetCoachOrderSum_Handler,
		},
		{
			MethodName: "RebuildUserOrderCoachList",
			Handler:    _EsportStatistics_RebuildUserOrderCoachList_Handler,
		},
		{
			MethodName: "GetCoachOrderRunningValue",
			Handler:    _EsportStatistics_GetCoachOrderRunningValue_Handler,
		},
		{
			MethodName: "GetCoachCustomerNum",
			Handler:    _EsportStatistics_GetCoachCustomerNum_Handler,
		},
		{
			MethodName: "GetCoachTodayUv",
			Handler:    _EsportStatistics_GetCoachTodayUv_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-statistics/esport-statistics.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-statistics/esport-statistics.proto", fileDescriptor_esport_statistics_9e077c1b611c835d)
}

var fileDescriptor_esport_statistics_9e077c1b611c835d = []byte{
	// 1836 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0x4f, 0x6f, 0xdb, 0xc8,
	0x15, 0x07, 0xad, 0x75, 0x6c, 0x3f, 0x59, 0x89, 0xcd, 0x38, 0x89, 0x4d, 0xaf, 0x13, 0x9b, 0x49,
	0x77, 0xb3, 0xbb, 0x89, 0x1c, 0x3b, 0x6b, 0x24, 0x0d, 0xda, 0x4d, 0x13, 0x25, 0xeb, 0x35, 0xb0,
	0x8e, 0x13, 0xda, 0xde, 0xc3, 0xee, 0x41, 0x98, 0x90, 0xb3, 0x12, 0x37, 0xfc, 0xa3, 0x70, 0x86,
	0x6a, 0x14, 0xa0, 0x40, 0x81, 0x02, 0x45, 0xcf, 0xed, 0xa5, 0xfd, 0x04, 0x3d, 0xf7, 0x63, 0x14,
	0xe8, 0xb9, 0xd7, 0x9e, 0xfb, 0x0d, 0x7a, 0x2c, 0xe6, 0x0f, 0x45, 0x52, 0x1a, 0x52, 0x94, 0x16,
	0xb9, 0x69, 0xde, 0xcc, 0xfc, 0xde, 0x9b, 0xf7, 0x8f, 0xef, 0x3d, 0xc1, 0x43, 0x4a, 0x77, 0xdf,
	0xc6, 0xae, 0xfd, 0x86, 0xb8, 0x5e, 0x1f, 0x47, 0xbb, 0x98, 0xf4, 0xc2, 0x88, 0xde, 0x25, 0x14,
	0x51, 0x97, 0x50, 0xd7, 0x26, 0xe3, 0x94, 0x66, 0x2f, 0x0a, 0x69, 0xa8, 0xaf, 0x8a, 0x8d, 0x76,
	0xba, 0x61, 0xb6, 0xe0, 0x93, 0xa7, 0x88, 0xda, 0xdd, 0x43, 0x4c, 0xcf, 0x09, 0x8e, 0xbe, 0x76,
	0x23, 0x42, 0xcf, 0xd0, 0x1b, 0x7c, 0x12, 0x39, 0x38, 0x22, 0x67, 0xae, 0x8f, 0x2d, 0xfc, 0x36,
	0xc6, 0x84, 0xea, 0x1b, 0xb0, 0x18, 0xbb, 0x4e, 0xdb, 0x73, 0x09, 0x5d, 0xd7, 0xb6, 0x6b, 0xb7,
	0x1b, 0xd6, 0x42, 0xec, 0x3a, 0xdf, 0xba, 0x84, 0x9a, 0xff, 0xd4, 0xe0, 0xd3, 0x89, 0x28, 0xa4,
	0x17, 0x06, 0x04, 0xeb, 0xaf, 0x61, 0x91, 0xba, 0x3e, 0x6e, 0xfb, 0xa8, 0xc7, 0x61, 0xea, 0xfb,
	0x87, 0xcd, 0x31, 0xb1, 0x9a, 0x15, 0xd1, 0x9a, 0x6c, 0x71, 0x8c, 0x7a, 0xcf, 0x03, 0x1a, 0x0d,
	0xac, 0x05, 0x2a, 0x56, 0xc6, 0x23, 0x58, 0xce, 0x6e, 0xe8, 0x2b, 0x50, 0x7b, 0x83, 0x07, 0xeb,
	0xda, 0xb6, 0x76, 0xbb, 0x61, 0xb1, 0x9f, 0xfa, 0x1a, 0xcc, 0xf7, 0x91, 0x17, 0xe3, 0xf5, 0xb9,
	0x6d, 0xed, 0x76, 0xcd, 0x12, 0x8b, 0x47, 0x73, 0x0f, 0x35, 0xf3, 0x6b, 0xd8, 0x38, 0xc4, 0xb4,
	0x15, 0x22, 0xbb, 0x7b, 0x3a, 0x94, 0x27, 0xd1, 0xc1, 0x0a, 0xd4, 0x62, 0xd7, 0x49, 0x80, 0x62,
	0xd7, 0xd1, 0xaf, 0xc1, 0x42, 0x07, 0xf9, 0xb8, 0xed, 0x3a, 0x1c, 0xaa, 0x61, 0x5d, 0x60, 0xcb,
	0x23, 0xc7, 0x7c, 0x09, 0x17, 0xd3, 0xfb, 0xcf, 0x10, 0x45, 0xfa, 0x26, 0x2c, 0x85, 0xec, 0x05,
	0xed, 0x20, 0xf6, 0x25, 0xc4, 0x22, 0x27, 0xbc, 0x88, 0x7d, 0x7d, 0x07, 0x96, 0xed, 0x98, 0xd0,
	0xd0, 0x97, 0xfb, 0x02, 0xac, 0x9e, 0xd0, 0x5e, 0xc4, 0xbe, 0x79, 0x0a, 0x86, 0x4a, 0x32, 0xa9,
	0xd7, 0x03, 0xf8, 0xc8, 0x41, 0x14, 0x71, 0xe0, 0xfa, 0xfe, 0x8e, 0x42, 0xa7, 0x79, 0x71, 0x2c,
	0x7e, 0xdc, 0xbc, 0x07, 0x1f, 0x27, 0xa0, 0x4f, 0x3c, 0xaf, 0xc2, 0x8b, 0xcd, 0xff, 0x6a, 0xb0,
	0x55, 0x70, 0x45, 0x8a, 0x82, 0xa1, 0xc1, 0x75, 0xc2, 0x18, 0x64, 0xec, 0xfc, 0x44, 0x21, 0x53,
	0x29, 0x50, 0xf3, 0x10, 0xf9, 0x98, 0xc9, 0x3a, 0xb4, 0x70, 0xbd, 0x93, 0x52, 0x0c, 0x04, 0x2b,
	0xa3, 0x07, 0x14, 0x96, 0x7e, 0x90, 0xb5, 0x74, 0x25, 0xc5, 0x64, 0x9c, 0xe1, 0x2e, 0x77, 0x06,
	0xe6, 0x84, 0xc7, 0x21, 0xa1, 0x2f, 0xd1, 0x80, 0x31, 0x2c, 0x56, 0xcd, 0x01, 0xb7, 0xd0, 0xd8,
	0x71, 0xa9, 0x96, 0x8c, 0xab, 0x68, 0x39, 0x57, 0x79, 0x00, 0xeb, 0xdf, 0xa0, 0xc0, 0xf1, 0xb0,
	0x85, 0x6d, 0xe4, 0x79, 0x5c, 0x21, 0x09, 0x93, 0x4d, 0x58, 0xb2, 0xd9, 0xba, 0x9d, 0xb2, 0x5a,
	0xe4, 0x84, 0x73, 0xd7, 0x31, 0x37, 0x61, 0x43, 0x71, 0x51, 0xb0, 0x33, 0x09, 0xdc, 0x4a, 0xa2,
	0x88, 0x6f, 0x3c, 0x8b, 0x23, 0x44, 0xdd, 0x30, 0x38, 0x91, 0x2e, 0x37, 0x39, 0xae, 0x0b, 0x9d,
	0x9b, 0x6d, 0x38, 0x68, 0xd0, 0xb6, 0x03, 0xba, 0x5e, 0x13, 0x1b, 0x0e, 0x1a, 0xb4, 0x02, 0x6a,
	0xfe, 0x5b, 0x83, 0x5f, 0x4c, 0xe0, 0x2a, 0xb5, 0xe1, 0x43, 0x63, 0x18, 0x0d, 0x19, 0x27, 0x39,
	0x2a, 0x49, 0x06, 0xa5, 0x80, 0xcd, 0x84, 0x90, 0x3a, 0x4b, 0x98, 0x52, 0x8c, 0xaf, 0x60, 0x65,
	0xf4, 0xc0, 0xa4, 0xb4, 0xd0, 0xc8, 0x7a, 0xc2, 0x39, 0xdc, 0x48, 0xc4, 0xe0, 0x38, 0xad, 0x80,
	0x3e, 0xe5, 0xe6, 0x3d, 0x72, 0x7e, 0x86, 0x22, 0xcd, 0x7f, 0x69, 0xb0, 0x5d, 0x8c, 0x2b, 0x55,
	0xd5, 0x55, 0xab, 0xea, 0x59, 0x89, 0xaa, 0x8a, 0xb0, 0x3e, 0xb8, 0x96, 0xbe, 0x90, 0x01, 0x70,
	0x16, 0x52, 0xe4, 0x3d, 0x7f, 0xd7, 0xc3, 0x81, 0xe3, 0xd2, 0x38, 0xc2, 0x4f, 0xfc, 0x30, 0x0e,
	0xe8, 0x71, 0x18, 0xd0, 0xae, 0x37, 0x28, 0x4e, 0xa7, 0x6b, 0x30, 0xef, 0xb3, 0x33, 0x49, 0x5e,
	0xe6, 0x0b, 0xf3, 0x15, 0xdc, 0xa9, 0x06, 0x2b, 0x15, 0xb6, 0x03, 0xcb, 0x94, 0x1d, 0x6c, 0x23,
	0xbe, 0xcd, 0x19, 0xd4, 0xac, 0x3a, 0xa7, 0x89, 0x1b, 0xe6, 0x29, 0x98, 0x12, 0x92, 0x7b, 0xd5,
	0xb7, 0x88, 0x62, 0x22, 0xb4, 0x96, 0xfd, 0xe6, 0x8d, 0x0b, 0x98, 0x8b, 0xc7, 0xb9, 0x91, 0x78,
	0x7c, 0x05, 0x37, 0x4b, 0x41, 0xa5, 0x78, 0x9f, 0xc3, 0xaa, 0xc7, 0xb7, 0xda, 0xc2, 0xac, 0xec,
	0xb3, 0x25, 0x65, 0xbc, 0xe4, 0xe5, 0xef, 0x98, 0x5d, 0xf8, 0xd8, 0xc2, 0xcc, 0xca, 0x0c, 0xf5,
	0x3b, 0x97, 0xb8, 0xf4, 0xe8, 0xf8, 0x25, 0xea, 0xcc, 0x28, 0x21, 0xf3, 0xd1, 0x0e, 0xa6, 0xed,
	0x30, 0xf0, 0x06, 0x3c, 0x72, 0x17, 0xad, 0x85, 0x0e, 0xa6, 0x27, 0x81, 0x37, 0x30, 0x7f, 0x05,
	0x5b, 0x05, 0x9c, 0xa4, 0xd8, 0x9b, 0xb0, 0xd4, 0x67, 0xe4, 0x4c, 0xd8, 0x2f, 0x72, 0x02, 0x0b,
	0xfc, 0x2e, 0x5c, 0x1f, 0xb9, 0xcd, 0x1c, 0xaf, 0x85, 0x22, 0x67, 0x46, 0x49, 0x33, 0x21, 0x53,
	0xcb, 0x85, 0xcc, 0x0e, 0xdc, 0x28, 0xe4, 0x24, 0x53, 0xdf, 0xaf, 0xe1, 0xfa, 0x21, 0xa6, 0x4f,
	0x31, 0xdf, 0x0d, 0x23, 0x0b, 0xdb, 0x61, 0xe4, 0xb4, 0x98, 0xdd, 0x2b, 0xa5, 0xd5, 0x13, 0xb8,
	0x51, 0x78, 0x5d, 0xea, 0x62, 0x0d, 0xe6, 0xed, 0xa1, 0x6b, 0x35, 0x2c, 0xb1, 0xd0, 0xaf, 0xc2,
	0x85, 0x4e, 0x14, 0xfe, 0x56, 0xba, 0x2f, 0x13, 0x99, 0xaf, 0xcc, 0xbf, 0x68, 0x50, 0xe7, 0x60,
	0x02, 0x4a, 0xed, 0xf7, 0x02, 0x6f, 0x2e, 0x8b, 0x77, 0x03, 0xea, 0x71, 0xcf, 0x41, 0x14, 0x0b,
	0x17, 0xa9, 0x71, 0x17, 0x01, 0x41, 0x62, 0xde, 0x91, 0x55, 0xd2, 0x47, 0xb9, 0x04, 0x6d, 0x42,
	0xc3, 0x43, 0xa9, 0x83, 0x91, 0xf5, 0x79, 0x11, 0x02, 0x8c, 0x28, 0x9c, 0x8b, 0x98, 0x3f, 0xf1,
	0xef, 0xf8, 0xc8, 0x33, 0x59, 0xba, 0xaa, 0xa2, 0x24, 0xf6, 0xd6, 0xf0, 0xc7, 0x1f, 0x09, 0x4e,
	0x44, 0x96, 0x2b, 0xf6, 0x12, 0xcf, 0xf5, 0xdd, 0xc4, 0x43, 0xc4, 0xc2, 0xfc, 0x9b, 0xa6, 0x32,
	0x89, 0x60, 0x26, 0x55, 0xfa, 0x18, 0xea, 0x11, 0xa7, 0xa6, 0x19, 0xb4, 0xbe, 0x7f, 0x5d, 0x91,
	0xe3, 0x32, 0x9a, 0xb4, 0x20, 0x1a, 0x02, 0x31, 0x6d, 0x05, 0xf8, 0x1d, 0x6d, 0xe7, 0xc4, 0x02,
	0x46, 0x3a, 0x11, 0xa2, 0x5d, 0x81, 0x0b, 0x2e, 0x69, 0xe3, 0xc0, 0x91, 0xae, 0x3f, 0xef, 0x92,
	0xe7, 0x81, 0x63, 0xbe, 0xe0, 0x7a, 0x48, 0xa3, 0x16, 0x27, 0x3a, 0x9a, 0x31, 0x0b, 0x3c, 0xe6,
	0x4f, 0x55, 0xe2, 0xc9, 0xa7, 0x6e, 0x01, 0x8c, 0x45, 0xbe, 0xa8, 0x0d, 0x79, 0xcc, 0xff, 0xc4,
	0x6b, 0x32, 0x06, 0x20, 0xd2, 0x38, 0x47, 0xc9, 0xd8, 0x65, 0x5c, 0x9e, 0x2d, 0x00, 0x42, 0x51,
	0x44, 0x05, 0xa0, 0xc8, 0x9d, 0x4b, 0x9c, 0xc2, 0xdd, 0x64, 0x03, 0x16, 0x71, 0xe0, 0x64, 0x9d,
	0x68, 0x01, 0x07, 0x0e, 0xe7, 0xf5, 0x1f, 0x6d, 0xf8, 0xfa, 0x51, 0x66, 0x52, 0xd8, 0x77, 0xb0,
	0x26, 0xde, 0x9a, 0x8a, 0x3c, 0xa1, 0x78, 0x2f, 0xc5, 0x6b, 0x72, 0xca, 0x30, 0xc1, 0x0d, 0xbf,
	0x43, 0xab, 0xf6, 0x28, 0xdd, 0x78, 0x06, 0x57, 0xd5, 0x87, 0xa7, 0x2a, 0xe8, 0x7f, 0x80, 0xcb,
	0x1c, 0xa5, 0x15, 0x06, 0x7d, 0x1c, 0x11, 0x37, 0x0c, 0x2c, 0x44, 0x71, 0xb9, 0x73, 0x7f, 0x0a,
	0x97, 0xec, 0xe1, 0xf1, 0x76, 0x84, 0xa8, 0xc0, 0xd5, 0xac, 0x8b, 0x76, 0x0e, 0xc5, 0xfc, 0x9e,
	0x7f, 0xbe, 0x93, 0xda, 0x24, 0xcf, 0x22, 0x53, 0x17, 0xf4, 0x50, 0x07, 0x67, 0xca, 0xfe, 0x05,
	0xb6, 0x66, 0x55, 0xff, 0x26, 0x2c, 0xf1, 0x2d, 0xe2, 0xbe, 0x4f, 0xbe, 0xa6, 0xfc, 0xec, 0xa9,
	0xfb, 0x1e, 0x9b, 0x7f, 0xd2, 0x60, 0xa7, 0x04, 0x5c, 0x9a, 0xc7, 0x06, 0x43, 0xbc, 0x63, 0x44,
	0xe0, 0x6c, 0x14, 0x7d, 0xa2, 0x30, 0x92, 0x0a, 0xf3, 0x9a, 0x3d, 0x4e, 0xe4, 0x0d, 0xde, 0xff,
	0x34, 0x68, 0xa4, 0xa6, 0x38, 0x15, 0x92, 0x17, 0xab, 0x6f, 0x0f, 0xae, 0x38, 0x68, 0x40, 0xf6,
	0xbe, 0x6c, 0xf3, 0xec, 0x24, 0x1c, 0x87, 0x0c, 0xbb, 0x1a, 0x5d, 0x6c, 0xb2, 0xb4, 0x3d, 0xc4,
	0xdb, 0x85, 0x35, 0x79, 0x05, 0x79, 0x5e, 0xe6, 0x86, 0xc8, 0x22, 0xab, 0x62, 0xef, 0x89, 0xe7,
	0x0d, 0x2f, 0xdc, 0x87, 0xab, 0x5d, 0x97, 0xd0, 0x30, 0x1a, 0x8c, 0x32, 0x11, 0x99, 0xf0, 0xb2,
	0xdc, 0xcd, 0x71, 0xd9, 0x83, 0x2b, 0xc9, 0xa5, 0x3c, 0x9b, 0x79, 0x21, 0x98, 0xdc, 0xcc, 0xf0,
	0x31, 0x7f, 0x00, 0x23, 0x63, 0x84, 0x84, 0x9c, 0xd8, 0xf6, 0x16, 0x5c, 0x1c, 0xaa, 0x21, 0x5b,
	0xf9, 0x2d, 0x27, 0xba, 0x28, 0x2f, 0xff, 0x7a, 0xb0, 0xa9, 0x04, 0x97, 0xb6, 0x7d, 0x95, 0x0f,
	0x3d, 0x12, 0xfb, 0x59, 0xab, 0x6e, 0x17, 0x59, 0x75, 0x88, 0x93, 0x89, 0xa9, 0xd3, 0xd8, 0xe7,
	0x96, 0x34, 0x61, 0xdb, 0xc2, 0xaf, 0x63, 0xd7, 0x73, 0x0a, 0xf3, 0x8b, 0x79, 0x13, 0x76, 0x4a,
	0xce, 0xc8, 0x6f, 0xec, 0x00, 0xb6, 0x73, 0x72, 0x5b, 0x71, 0x10, 0xb8, 0x41, 0xe7, 0x3b, 0x16,
	0x77, 0x95, 0x3e, 0x20, 0xb3, 0xe7, 0xac, 0x6f, 0x60, 0xa7, 0x84, 0xb5, 0xd4, 0xdd, 0x4d, 0x68,
	0x44, 0x82, 0xde, 0x16, 0x89, 0x41, 0xf0, 0x5f, 0x8e, 0x32, 0x87, 0x4d, 0x92, 0xb6, 0xd4, 0xad,
	0xb4, 0xd3, 0xfe, 0xc0, 0xe2, 0xff, 0x06, 0x36, 0x95, 0x4c, 0xd3, 0xe2, 0x35, 0x37, 0x09, 0xd0,
	0xc6, 0x27, 0x01, 0x07, 0x70, 0x35, 0x41, 0x38, 0x0b, 0x1d, 0x34, 0x38, 0xef, 0x57, 0xaa, 0x6b,
	0x3e, 0x83, 0x6b, 0x63, 0xd7, 0x24, 0xd3, 0x8b, 0x30, 0x17, 0xf7, 0xe5, 0x85, 0xb9, 0xb8, 0xbf,
	0xff, 0x8f, 0x35, 0x58, 0x79, 0xce, 0xbd, 0x2b, 0x6d, 0x8e, 0xf5, 0xbf, 0x6a, 0x69, 0x13, 0x54,
	0x30, 0x98, 0xd1, 0x7f, 0x39, 0xcb, 0x30, 0x87, 0xcb, 0x6e, 0x3c, 0x9a, 0x7d, 0x0e, 0xa4, 0xbf,
	0x05, 0x7d, 0x7c, 0x36, 0xa2, 0xdf, 0x29, 0x99, 0x38, 0x8c, 0x8d, 0x3a, 0x8c, 0xbb, 0x15, 0x4f,
	0xe7, 0x58, 0x8e, 0x34, 0xfb, 0x45, 0x2c, 0xd5, 0x23, 0x84, 0x22, 0x96, 0x45, 0x13, 0x84, 0xf7,
	0x70, 0x45, 0x39, 0x30, 0xd1, 0x77, 0xab, 0x8f, 0x56, 0x04, 0xe3, 0x7b, 0xd3, 0xce, 0x62, 0xf4,
	0x00, 0x56, 0xc7, 0x66, 0x0d, 0xfa, 0x17, 0x0a, 0x98, 0xa2, 0x51, 0x86, 0x71, 0xa7, 0xda, 0x61,
	0xc9, 0xef, 0xcf, 0x1a, 0x6c, 0x95, 0x36, 0xfe, 0xfa, 0x83, 0xe9, 0x47, 0x05, 0x42, 0x90, 0x87,
	0xb3, 0xce, 0x18, 0xf4, 0x3f, 0x68, 0xb0, 0x5e, 0xd4, 0x62, 0xeb, 0xfb, 0x53, 0xf5, 0xe3, 0x42,
	0x94, 0xfb, 0x33, 0xf4, 0xf0, 0xfa, 0xdf, 0x35, 0xb8, 0x55, 0xa5, 0x1f, 0xd6, 0xbf, 0x2a, 0x76,
	0xaf, 0x2a, 0xfd, 0xb9, 0xf1, 0x78, 0xe6, 0xfb, 0xa9, 0xc3, 0x2a, 0x7b, 0x4a, 0xa5, 0xc3, 0x96,
	0xf5, 0xb9, 0x4a, 0x87, 0x2d, 0x6f, 0x57, 0x7f, 0xaf, 0xc1, 0xb5, 0x82, 0x46, 0x51, 0xdf, 0x9b,
	0x8c, 0x36, 0xd2, 0xbe, 0x1a, 0xfb, 0xd3, 0x5c, 0xc9, 0x88, 0x50, 0xd0, 0x49, 0x2a, 0x45, 0x28,
	0x6f, 0x5a, 0x95, 0x22, 0x4c, 0x6a, 0x54, 0x7f, 0xc7, 0x3f, 0x15, 0x8a, 0xbe, 0x4b, 0xbf, 0x57,
	0x09, 0x2d, 0x53, 0x17, 0x18, 0x7b, 0x53, 0xdc, 0xc8, 0xb1, 0x57, 0xf4, 0x42, 0x45, 0xec, 0x8b,
	0xdb, 0xb0, 0x22, 0xf6, 0x65, 0x8d, 0x96, 0x48, 0x98, 0xe3, 0x55, 0x4c, 0x51, 0xc2, 0x2c, 0xac,
	0x89, 0x8c, 0x7b, 0xd3, 0xf6, 0x39, 0xfa, 0x1f, 0x35, 0xd8, 0x28, 0x2c, 0xdf, 0xf5, 0x82, 0xc0,
	0x2f, 0xed, 0x24, 0x8c, 0x2f, 0xa7, 0xbb, 0x24, 0x05, 0xa1, 0x70, 0x59, 0x51, 0x64, 0xea, 0x77,
	0xcb, 0xc1, 0x46, 0x2a, 0x5d, 0xa3, 0x59, 0xf5, 0x78, 0xe6, 0xf9, 0x85, 0x55, 0xa4, 0xf2, 0xf9,
	0x93, 0xea, 0x52, 0xe5, 0xf3, 0x27, 0x16, 0xaa, 0x5c, 0x90, 0xc2, 0x72, 0x51, 0x29, 0xc8, 0xa4,
	0xba, 0x56, 0x29, 0xc8, 0xe4, 0x8a, 0x94, 0xc2, 0x65, 0x45, 0xdd, 0xa7, 0x97, 0x95, 0x1d, 0xe3,
	0x45, 0xa9, 0xd2, 0x0e, 0x65, 0xe5, 0x64, 0x17, 0x2e, 0x8d, 0x14, 0x7d, 0xfa, 0x67, 0x25, 0x10,
	0xf9, 0x7a, 0xd2, 0xf8, 0xbc, 0xca, 0x51, 0xc1, 0xe9, 0xe9, 0xc1, 0xf7, 0xf7, 0x3b, 0xa1, 0x87,
	0x82, 0x4e, 0xf3, 0x60, 0x9f, 0xd2, 0xa6, 0x1d, 0xfa, 0xbb, 0xfc, 0x6f, 0x47, 0x3b, 0xf4, 0x76,
	0x09, 0x8e, 0xfa, 0xae, 0x8d, 0x15, 0x7f, 0x4d, 0xbe, 0xbe, 0xc0, 0x0f, 0xdd, 0xff, 0x7f, 0x00,
	0x00, 0x00, 0xff, 0xff, 0xfc, 0x90, 0x85, 0x01, 0xd7, 0x1c, 0x00, 0x00,
}
