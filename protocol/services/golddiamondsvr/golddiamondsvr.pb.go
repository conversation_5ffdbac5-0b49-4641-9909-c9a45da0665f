// Code generated by protoc-gen-gogo.
// source: services/golddiamond/golddiamondsvr.proto
// DO NOT EDIT!

/*
	Package golddiamondsvr is a generated protocol buffer package.

	It is generated from these files:
		services/golddiamond/golddiamondsvr.proto

	It has these top-level messages:
		AwardDiamondReq
		GetIncomeReq
		IncomeAmount
		GetIncomeResp
		GetIncomeSingleReq
		GetIncomeSingleResp
		IncomeDetail
		GetIncomeDetailByTimeReq
		GetIncomeDetailReq
		GetIncomeDetailResp
		SettlementBalanceReq
		SettlementBalanceResp
		ReportDetail
		ReportDetailReq
		ReportDetailResp
		GetCommissionInfoReq
		GetRoomCommissionInfoReq
		GetCommissionInfoResp
		GetCommissionDetailReq
		CommissionInfo
		GetCommissionDetailResp
		GetRoomCommissionDetailReq
		RoomCommissionInfo
		GetRoomCommissionDetailResp
		MemberDetailInfo
		GetMemberDetailResp
		GameDetailInfo
		GetGameDetailResp
		GetRechargeGameReq
		GetRechargeGameResp
		GetConsumeRoomReq
		GetConsumeRoomResp
		RoomDetailInfo
		GetRoomDetailResp
*/
package golddiamondsvr

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import math3 "math"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import math4 "math"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type IncomeType int32

const (
	IncomeType_GAME_RECHARGE_REBATE     IncomeType = 1
	IncomeType_GUILD_SYS_TASK           IncomeType = 2
	IncomeType_GUILD_OPE_TASK           IncomeType = 3
	IncomeType_SETTLEMENT               IncomeType = 4
	IncomeType_GUILD_WELFARE_FIRST_JOIN IncomeType = 5
	IncomeType_CHANNEL_SEND_GIFT        IncomeType = 6
	IncomeType_CHANNEL_SETTLEMENT       IncomeType = 7
)

var IncomeType_name = map[int32]string{
	1: "GAME_RECHARGE_REBATE",
	2: "GUILD_SYS_TASK",
	3: "GUILD_OPE_TASK",
	4: "SETTLEMENT",
	5: "GUILD_WELFARE_FIRST_JOIN",
	6: "CHANNEL_SEND_GIFT",
	7: "CHANNEL_SETTLEMENT",
}
var IncomeType_value = map[string]int32{
	"GAME_RECHARGE_REBATE":     1,
	"GUILD_SYS_TASK":           2,
	"GUILD_OPE_TASK":           3,
	"SETTLEMENT":               4,
	"GUILD_WELFARE_FIRST_JOIN": 5,
	"CHANNEL_SEND_GIFT":        6,
	"CHANNEL_SETTLEMENT":       7,
}

func (x IncomeType) Enum() *IncomeType {
	p := new(IncomeType)
	*p = x
	return p
}
func (x IncomeType) String() string {
	return proto.EnumName(IncomeType_name, int32(x))
}
func (x *IncomeType) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(IncomeType_value, data, "IncomeType")
	if err != nil {
		return err
	}
	*x = IncomeType(value)
	return nil
}
func (IncomeType) EnumDescriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{0} }

type AwardDiamondReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	PaidUid    uint32 `protobuf:"varint,2,req,name=paid_uid,json=paidUid" json:"paid_uid"`
	BoughtTime uint64 `protobuf:"varint,3,req,name=bought_time,json=boughtTime" json:"bought_time"`
	SourceType uint32 `protobuf:"varint,4,req,name=source_type,json=sourceType" json:"source_type"`
	Income     int64  `protobuf:"varint,5,req,name=income" json:"income"`
	OrderId    string `protobuf:"bytes,6,req,name=order_id,json=orderId" json:"order_id"`
	UniqSign   string `protobuf:"bytes,7,req,name=uniq_sign,json=uniqSign" json:"uniq_sign"`
	Desc       string `protobuf:"bytes,8,req,name=desc" json:"desc"`
	Extand     string `protobuf:"bytes,9,req,name=extand" json:"extand"`
}

func (m *AwardDiamondReq) Reset()                    { *m = AwardDiamondReq{} }
func (m *AwardDiamondReq) String() string            { return proto.CompactTextString(m) }
func (*AwardDiamondReq) ProtoMessage()               {}
func (*AwardDiamondReq) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{0} }

func (m *AwardDiamondReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AwardDiamondReq) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *AwardDiamondReq) GetBoughtTime() uint64 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *AwardDiamondReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *AwardDiamondReq) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *AwardDiamondReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AwardDiamondReq) GetUniqSign() string {
	if m != nil {
		return m.UniqSign
	}
	return ""
}

func (m *AwardDiamondReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *AwardDiamondReq) GetExtand() string {
	if m != nil {
		return m.Extand
	}
	return ""
}

type GetIncomeReq struct {
	GuildIdList []uint32 `protobuf:"varint,1,rep,name=guild_id_list,json=guildIdList" json:"guild_id_list,omitempty"`
}

func (m *GetIncomeReq) Reset()                    { *m = GetIncomeReq{} }
func (m *GetIncomeReq) String() string            { return proto.CompactTextString(m) }
func (*GetIncomeReq) ProtoMessage()               {}
func (*GetIncomeReq) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{1} }

func (m *GetIncomeReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

type IncomeAmount struct {
	GuildId     uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	DayIncome   int64  `protobuf:"varint,2,req,name=day_income,json=dayIncome" json:"day_income"`
	MonthIncome int64  `protobuf:"varint,3,req,name=month_income,json=monthIncome" json:"month_income"`
}

func (m *IncomeAmount) Reset()                    { *m = IncomeAmount{} }
func (m *IncomeAmount) String() string            { return proto.CompactTextString(m) }
func (*IncomeAmount) ProtoMessage()               {}
func (*IncomeAmount) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{2} }

func (m *IncomeAmount) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *IncomeAmount) GetDayIncome() int64 {
	if m != nil {
		return m.DayIncome
	}
	return 0
}

func (m *IncomeAmount) GetMonthIncome() int64 {
	if m != nil {
		return m.MonthIncome
	}
	return 0
}

type GetIncomeResp struct {
	IncomeList []*IncomeAmount `protobuf:"bytes,1,rep,name=income_list,json=incomeList" json:"income_list,omitempty"`
}

func (m *GetIncomeResp) Reset()                    { *m = GetIncomeResp{} }
func (m *GetIncomeResp) String() string            { return proto.CompactTextString(m) }
func (*GetIncomeResp) ProtoMessage()               {}
func (*GetIncomeResp) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{3} }

func (m *GetIncomeResp) GetIncomeList() []*IncomeAmount {
	if m != nil {
		return m.IncomeList
	}
	return nil
}

type GetIncomeSingleReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StarLevel  uint32 `protobuf:"varint,2,req,name=star_level,json=starLevel" json:"star_level"`
	SourceType uint32 `protobuf:"varint,3,opt,name=source_type,json=sourceType" json:"source_type"`
}

func (m *GetIncomeSingleReq) Reset()                    { *m = GetIncomeSingleReq{} }
func (m *GetIncomeSingleReq) String() string            { return proto.CompactTextString(m) }
func (*GetIncomeSingleReq) ProtoMessage()               {}
func (*GetIncomeSingleReq) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{4} }

func (m *GetIncomeSingleReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetIncomeSingleReq) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *GetIncomeSingleReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type GetIncomeSingleResp struct {
	DayIncome               int64   `protobuf:"varint,1,req,name=day_income,json=dayIncome" json:"day_income"`
	MonthIncome             int64   `protobuf:"varint,2,req,name=month_income,json=monthIncome" json:"month_income"`
	IncomeEstimate          float64 `protobuf:"fixed64,3,req,name=income_estimate,json=incomeEstimate" json:"income_estimate"`
	Coefficient             float32 `protobuf:"fixed32,4,opt,name=coefficient" json:"coefficient"`
	LastMonthIncome         int64   `protobuf:"varint,5,opt,name=last_month_income,json=lastMonthIncome" json:"last_month_income"`
	LastMonthEstimateIncome float64 `protobuf:"fixed64,6,opt,name=last_month_estimate_income,json=lastMonthEstimateIncome" json:"last_month_estimate_income"`
}

func (m *GetIncomeSingleResp) Reset()         { *m = GetIncomeSingleResp{} }
func (m *GetIncomeSingleResp) String() string { return proto.CompactTextString(m) }
func (*GetIncomeSingleResp) ProtoMessage()    {}
func (*GetIncomeSingleResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{5}
}

func (m *GetIncomeSingleResp) GetDayIncome() int64 {
	if m != nil {
		return m.DayIncome
	}
	return 0
}

func (m *GetIncomeSingleResp) GetMonthIncome() int64 {
	if m != nil {
		return m.MonthIncome
	}
	return 0
}

func (m *GetIncomeSingleResp) GetIncomeEstimate() float64 {
	if m != nil {
		return m.IncomeEstimate
	}
	return 0
}

func (m *GetIncomeSingleResp) GetCoefficient() float32 {
	if m != nil {
		return m.Coefficient
	}
	return 0
}

func (m *GetIncomeSingleResp) GetLastMonthIncome() int64 {
	if m != nil {
		return m.LastMonthIncome
	}
	return 0
}

func (m *GetIncomeSingleResp) GetLastMonthEstimateIncome() float64 {
	if m != nil {
		return m.LastMonthEstimateIncome
	}
	return 0
}

type IncomeDetail struct {
	PaidUid       uint32 `protobuf:"varint,1,req,name=paid_uid,json=paidUid" json:"paid_uid"`
	BoughtTime    uint64 `protobuf:"varint,2,req,name=bought_time,json=boughtTime" json:"bought_time"`
	SourceType    uint32 `protobuf:"varint,3,req,name=source_type,json=sourceType" json:"source_type"`
	Income        int64  `protobuf:"varint,4,req,name=income" json:"income"`
	Desc          string `protobuf:"bytes,5,req,name=desc" json:"desc"`
	OrderId       string `protobuf:"bytes,6,req,name=order_id,json=orderId" json:"order_id"`
	Extand        string `protobuf:"bytes,7,req,name=extand" json:"extand"`
	IncomeBalance int64  `protobuf:"varint,8,req,name=income_balance,json=incomeBalance" json:"income_balance"`
}

func (m *IncomeDetail) Reset()                    { *m = IncomeDetail{} }
func (m *IncomeDetail) String() string            { return proto.CompactTextString(m) }
func (*IncomeDetail) ProtoMessage()               {}
func (*IncomeDetail) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{6} }

func (m *IncomeDetail) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *IncomeDetail) GetBoughtTime() uint64 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *IncomeDetail) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

func (m *IncomeDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *IncomeDetail) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *IncomeDetail) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *IncomeDetail) GetExtand() string {
	if m != nil {
		return m.Extand
	}
	return ""
}

func (m *IncomeDetail) GetIncomeBalance() int64 {
	if m != nil {
		return m.IncomeBalance
	}
	return 0
}

type GetIncomeDetailByTimeReq struct {
	GuildId   uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StartTime uint32 `protobuf:"varint,2,req,name=start_time,json=startTime" json:"start_time"`
	EndTime   uint32 `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
}

func (m *GetIncomeDetailByTimeReq) Reset()         { *m = GetIncomeDetailByTimeReq{} }
func (m *GetIncomeDetailByTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetIncomeDetailByTimeReq) ProtoMessage()    {}
func (*GetIncomeDetailByTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{7}
}

func (m *GetIncomeDetailByTimeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetIncomeDetailByTimeReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetIncomeDetailByTimeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetIncomeDetailReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Index   uint32 `protobuf:"varint,2,req,name=index" json:"index"`
	Count   uint32 `protobuf:"varint,3,req,name=count" json:"count"`
}

func (m *GetIncomeDetailReq) Reset()                    { *m = GetIncomeDetailReq{} }
func (m *GetIncomeDetailReq) String() string            { return proto.CompactTextString(m) }
func (*GetIncomeDetailReq) ProtoMessage()               {}
func (*GetIncomeDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{8} }

func (m *GetIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetIncomeDetailReq) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GetIncomeDetailReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetIncomeDetailResp struct {
	IncomeDetailList []*IncomeDetail `protobuf:"bytes,1,rep,name=income_detail_list,json=incomeDetailList" json:"income_detail_list,omitempty"`
	Total            uint32          `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetIncomeDetailResp) Reset()         { *m = GetIncomeDetailResp{} }
func (m *GetIncomeDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetIncomeDetailResp) ProtoMessage()    {}
func (*GetIncomeDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{9}
}

func (m *GetIncomeDetailResp) GetIncomeDetailList() []*IncomeDetail {
	if m != nil {
		return m.IncomeDetailList
	}
	return nil
}

func (m *GetIncomeDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type SettlementBalanceReq struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SettlementTime uint32 `protobuf:"varint,2,req,name=settlement_time,json=settlementTime" json:"settlement_time"`
	StarLevel      uint32 `protobuf:"varint,3,req,name=star_level,json=starLevel" json:"star_level"`
}

func (m *SettlementBalanceReq) Reset()         { *m = SettlementBalanceReq{} }
func (m *SettlementBalanceReq) String() string { return proto.CompactTextString(m) }
func (*SettlementBalanceReq) ProtoMessage()    {}
func (*SettlementBalanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{10}
}

func (m *SettlementBalanceReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SettlementBalanceReq) GetSettlementTime() uint32 {
	if m != nil {
		return m.SettlementTime
	}
	return 0
}

func (m *SettlementBalanceReq) GetStarLevel() uint32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

type SettlementBalanceResp struct {
	Detail    *GetIncomeDetailResp `protobuf:"bytes,1,req,name=detail" json:"detail,omitempty"`
	Amount    uint64               `protobuf:"varint,2,req,name=amount" json:"amount"`
	RmbIncome float64              `protobuf:"fixed64,3,req,name=rmb_income,json=rmbIncome" json:"rmb_income"`
}

func (m *SettlementBalanceResp) Reset()         { *m = SettlementBalanceResp{} }
func (m *SettlementBalanceResp) String() string { return proto.CompactTextString(m) }
func (*SettlementBalanceResp) ProtoMessage()    {}
func (*SettlementBalanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{11}
}

func (m *SettlementBalanceResp) GetDetail() *GetIncomeDetailResp {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *SettlementBalanceResp) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *SettlementBalanceResp) GetRmbIncome() float64 {
	if m != nil {
		return m.RmbIncome
	}
	return 0
}

type ReportDetail struct {
	PaidUid    uint32 `protobuf:"varint,1,req,name=paid_uid,json=paidUid" json:"paid_uid"`
	BoughtTime uint32 `protobuf:"varint,2,req,name=bought_time,json=boughtTime" json:"bought_time"`
	GuildId    uint32 `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	Income     int64  `protobuf:"varint,4,req,name=income" json:"income"`
	Extand     string `protobuf:"bytes,5,req,name=extand" json:"extand"`
}

func (m *ReportDetail) Reset()                    { *m = ReportDetail{} }
func (m *ReportDetail) String() string            { return proto.CompactTextString(m) }
func (*ReportDetail) ProtoMessage()               {}
func (*ReportDetail) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{12} }

func (m *ReportDetail) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *ReportDetail) GetBoughtTime() uint32 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *ReportDetail) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ReportDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *ReportDetail) GetExtand() string {
	if m != nil {
		return m.Extand
	}
	return ""
}

type ReportDetailReq struct {
	StartTime uint32 `protobuf:"varint,1,req,name=start_time,json=startTime" json:"start_time"`
	EndTime   uint32 `protobuf:"varint,2,req,name=end_time,json=endTime" json:"end_time"`
}

func (m *ReportDetailReq) Reset()                    { *m = ReportDetailReq{} }
func (m *ReportDetailReq) String() string            { return proto.CompactTextString(m) }
func (*ReportDetailReq) ProtoMessage()               {}
func (*ReportDetailReq) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{13} }

func (m *ReportDetailReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *ReportDetailReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ReportDetailResp struct {
	DetailInfo []*ReportDetail `protobuf:"bytes,1,rep,name=detail_info,json=detailInfo" json:"detail_info,omitempty"`
}

func (m *ReportDetailResp) Reset()                    { *m = ReportDetailResp{} }
func (m *ReportDetailResp) String() string            { return proto.CompactTextString(m) }
func (*ReportDetailResp) ProtoMessage()               {}
func (*ReportDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{14} }

func (m *ReportDetailResp) GetDetailInfo() []*ReportDetail {
	if m != nil {
		return m.DetailInfo
	}
	return nil
}

type GetCommissionInfoReq struct {
	GuildId          uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StartTime        uint32 `protobuf:"varint,2,req,name=start_time,json=startTime" json:"start_time"`
	EndTime          uint32 `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
	JustUnsettlement bool   `protobuf:"varint,4,req,name=just_unsettlement,json=justUnsettlement" json:"just_unsettlement"`
	LimitStart       uint32 `protobuf:"varint,5,opt,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd         uint32 `protobuf:"varint,6,opt,name=limit_end,json=limitEnd" json:"limit_end"`
	GameId           uint32 `protobuf:"varint,7,opt,name=game_id,json=gameId" json:"game_id"`
	Uid              uint32 `protobuf:"varint,8,opt,name=uid" json:"uid"`
	SourceType       uint32 `protobuf:"varint,9,opt,name=source_type,json=sourceType" json:"source_type"`
}

func (m *GetCommissionInfoReq) Reset()         { *m = GetCommissionInfoReq{} }
func (m *GetCommissionInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetCommissionInfoReq) ProtoMessage()    {}
func (*GetCommissionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{15}
}

func (m *GetCommissionInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCommissionInfoReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCommissionInfoReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetCommissionInfoReq) GetJustUnsettlement() bool {
	if m != nil {
		return m.JustUnsettlement
	}
	return false
}

func (m *GetCommissionInfoReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetCommissionInfoReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

func (m *GetCommissionInfoReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetCommissionInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCommissionInfoReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type GetRoomCommissionInfoReq struct {
	GuildId          uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StartTime        uint32 `protobuf:"varint,2,req,name=start_time,json=startTime" json:"start_time"`
	EndTime          uint32 `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
	JustUnsettlement bool   `protobuf:"varint,4,req,name=just_unsettlement,json=justUnsettlement" json:"just_unsettlement"`
	LimitStart       uint32 `protobuf:"varint,5,opt,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd         uint32 `protobuf:"varint,6,opt,name=limit_end,json=limitEnd" json:"limit_end"`
	RoomId           uint32 `protobuf:"varint,7,opt,name=room_id,json=roomId" json:"room_id"`
	Uid              uint32 `protobuf:"varint,8,opt,name=uid" json:"uid"`
	SourceType       uint32 `protobuf:"varint,9,opt,name=source_type,json=sourceType" json:"source_type"`
}

func (m *GetRoomCommissionInfoReq) Reset()         { *m = GetRoomCommissionInfoReq{} }
func (m *GetRoomCommissionInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetRoomCommissionInfoReq) ProtoMessage()    {}
func (*GetRoomCommissionInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{16}
}

func (m *GetRoomCommissionInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetJustUnsettlement() bool {
	if m != nil {
		return m.JustUnsettlement
	}
	return false
}

func (m *GetRoomCommissionInfoReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoomCommissionInfoReq) GetSourceType() uint32 {
	if m != nil {
		return m.SourceType
	}
	return 0
}

type GetCommissionInfoResp struct {
	Income     uint32 `protobuf:"varint,1,req,name=income" json:"income"`
	PayerCount uint32 `protobuf:"varint,2,req,name=payer_count,json=payerCount" json:"payer_count"`
	CashAmount uint32 `protobuf:"varint,3,req,name=cash_amount,json=cashAmount" json:"cash_amount"`
}

func (m *GetCommissionInfoResp) Reset()         { *m = GetCommissionInfoResp{} }
func (m *GetCommissionInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetCommissionInfoResp) ProtoMessage()    {}
func (*GetCommissionInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{17}
}

func (m *GetCommissionInfoResp) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GetCommissionInfoResp) GetPayerCount() uint32 {
	if m != nil {
		return m.PayerCount
	}
	return 0
}

func (m *GetCommissionInfoResp) GetCashAmount() uint32 {
	if m != nil {
		return m.CashAmount
	}
	return 0
}

type GetCommissionDetailReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StartTime  uint32 `protobuf:"varint,2,req,name=start_time,json=startTime" json:"start_time"`
	EndTime    uint32 `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
	Uid        uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
	GameId     uint32 `protobuf:"varint,5,opt,name=game_id,json=gameId" json:"game_id"`
	LimitStart uint32 `protobuf:"varint,6,req,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd   uint32 `protobuf:"varint,7,req,name=limit_end,json=limitEnd" json:"limit_end"`
}

func (m *GetCommissionDetailReq) Reset()         { *m = GetCommissionDetailReq{} }
func (m *GetCommissionDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetCommissionDetailReq) ProtoMessage()    {}
func (*GetCommissionDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{18}
}

func (m *GetCommissionDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCommissionDetailReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCommissionDetailReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetCommissionDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCommissionDetailReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetCommissionDetailReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetCommissionDetailReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

type CommissionInfo struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Fee        uint32 `protobuf:"varint,2,req,name=fee" json:"fee"`
	BoughtTime uint32 `protobuf:"varint,3,req,name=bought_time,json=boughtTime" json:"bought_time"`
	GameId     uint32 `protobuf:"varint,4,opt,name=game_id,json=gameId" json:"game_id"`
	Id         uint32 `protobuf:"varint,5,req,name=id" json:"id"`
}

func (m *CommissionInfo) Reset()                    { *m = CommissionInfo{} }
func (m *CommissionInfo) String() string            { return proto.CompactTextString(m) }
func (*CommissionInfo) ProtoMessage()               {}
func (*CommissionInfo) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{19} }

func (m *CommissionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CommissionInfo) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *CommissionInfo) GetBoughtTime() uint32 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *CommissionInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CommissionInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetCommissionDetailResp struct {
	CommissionList []*CommissionInfo `protobuf:"bytes,1,rep,name=commission_list,json=commissionList" json:"commission_list,omitempty"`
	Total          uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetCommissionDetailResp) Reset()         { *m = GetCommissionDetailResp{} }
func (m *GetCommissionDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetCommissionDetailResp) ProtoMessage()    {}
func (*GetCommissionDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{20}
}

func (m *GetCommissionDetailResp) GetCommissionList() []*CommissionInfo {
	if m != nil {
		return m.CommissionList
	}
	return nil
}

func (m *GetCommissionDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetRoomCommissionDetailReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	StartTime  uint32 `protobuf:"varint,2,req,name=start_time,json=startTime" json:"start_time"`
	EndTime    uint32 `protobuf:"varint,3,req,name=end_time,json=endTime" json:"end_time"`
	Uid        uint32 `protobuf:"varint,4,opt,name=uid" json:"uid"`
	RoomId     uint32 `protobuf:"varint,5,opt,name=room_id,json=roomId" json:"room_id"`
	LimitStart uint32 `protobuf:"varint,6,req,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd   uint32 `protobuf:"varint,7,req,name=limit_end,json=limitEnd" json:"limit_end"`
}

func (m *GetRoomCommissionDetailReq) Reset()         { *m = GetRoomCommissionDetailReq{} }
func (m *GetRoomCommissionDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetRoomCommissionDetailReq) ProtoMessage()    {}
func (*GetRoomCommissionDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{21}
}

func (m *GetRoomCommissionDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRoomCommissionDetailReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetRoomCommissionDetailReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetRoomCommissionDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetRoomCommissionDetailReq) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *GetRoomCommissionDetailReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetRoomCommissionDetailReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

type RoomCommissionInfo struct {
	Uid        uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Fee        uint32 `protobuf:"varint,2,req,name=fee" json:"fee"`
	BoughtTime uint32 `protobuf:"varint,3,req,name=bought_time,json=boughtTime" json:"bought_time"`
	Id         uint32 `protobuf:"varint,4,req,name=id" json:"id"`
	RoomId     uint32 `protobuf:"varint,5,opt,name=room_id,json=roomId" json:"room_id"`
}

func (m *RoomCommissionInfo) Reset()         { *m = RoomCommissionInfo{} }
func (m *RoomCommissionInfo) String() string { return proto.CompactTextString(m) }
func (*RoomCommissionInfo) ProtoMessage()    {}
func (*RoomCommissionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{22}
}

func (m *RoomCommissionInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RoomCommissionInfo) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *RoomCommissionInfo) GetBoughtTime() uint32 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *RoomCommissionInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RoomCommissionInfo) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

type GetRoomCommissionDetailResp struct {
	CommissionList []*RoomCommissionInfo `protobuf:"bytes,1,rep,name=commission_list,json=commissionList" json:"commission_list,omitempty"`
	Total          uint32                `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetRoomCommissionDetailResp) Reset()         { *m = GetRoomCommissionDetailResp{} }
func (m *GetRoomCommissionDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetRoomCommissionDetailResp) ProtoMessage()    {}
func (*GetRoomCommissionDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{23}
}

func (m *GetRoomCommissionDetailResp) GetCommissionList() []*RoomCommissionInfo {
	if m != nil {
		return m.CommissionList
	}
	return nil
}

func (m *GetRoomCommissionDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type MemberDetailInfo struct {
	Uid    uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Fee    uint32 `protobuf:"varint,2,req,name=fee" json:"fee"`
	Income uint32 `protobuf:"varint,3,req,name=income" json:"income"`
	Id     uint32 `protobuf:"varint,4,req,name=id" json:"id"`
}

func (m *MemberDetailInfo) Reset()                    { *m = MemberDetailInfo{} }
func (m *MemberDetailInfo) String() string            { return proto.CompactTextString(m) }
func (*MemberDetailInfo) ProtoMessage()               {}
func (*MemberDetailInfo) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{24} }

func (m *MemberDetailInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MemberDetailInfo) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *MemberDetailInfo) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *MemberDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetMemberDetailResp struct {
	MemberDetailList []*MemberDetailInfo `protobuf:"bytes,1,rep,name=member_detail_list,json=memberDetailList" json:"member_detail_list,omitempty"`
	Total            uint32              `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetMemberDetailResp) Reset()         { *m = GetMemberDetailResp{} }
func (m *GetMemberDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetMemberDetailResp) ProtoMessage()    {}
func (*GetMemberDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{25}
}

func (m *GetMemberDetailResp) GetMemberDetailList() []*MemberDetailInfo {
	if m != nil {
		return m.MemberDetailList
	}
	return nil
}

func (m *GetMemberDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GameDetailInfo struct {
	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId" json:"game_id"`
	Fee    uint32 `protobuf:"varint,2,req,name=fee" json:"fee"`
	Income uint32 `protobuf:"varint,3,req,name=income" json:"income"`
	Id     uint32 `protobuf:"varint,4,req,name=id" json:"id"`
}

func (m *GameDetailInfo) Reset()                    { *m = GameDetailInfo{} }
func (m *GameDetailInfo) String() string            { return proto.CompactTextString(m) }
func (*GameDetailInfo) ProtoMessage()               {}
func (*GameDetailInfo) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{26} }

func (m *GameDetailInfo) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GameDetailInfo) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *GameDetailInfo) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GameDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetGameDetailResp struct {
	GameDetailList []*GameDetailInfo `protobuf:"bytes,1,rep,name=game_detail_list,json=gameDetailList" json:"game_detail_list,omitempty"`
	Total          uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetGameDetailResp) Reset()                    { *m = GetGameDetailResp{} }
func (m *GetGameDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetGameDetailResp) ProtoMessage()               {}
func (*GetGameDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{27} }

func (m *GetGameDetailResp) GetGameDetailList() []*GameDetailInfo {
	if m != nil {
		return m.GameDetailList
	}
	return nil
}

func (m *GetGameDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetRechargeGameReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LimitStart uint32 `protobuf:"varint,2,req,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd   uint32 `protobuf:"varint,3,req,name=limit_end,json=limitEnd" json:"limit_end"`
}

func (m *GetRechargeGameReq) Reset()         { *m = GetRechargeGameReq{} }
func (m *GetRechargeGameReq) String() string { return proto.CompactTextString(m) }
func (*GetRechargeGameReq) ProtoMessage()    {}
func (*GetRechargeGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{28}
}

func (m *GetRechargeGameReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRechargeGameReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetRechargeGameReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

type GetRechargeGameResp struct {
	GameIdList []uint32 `protobuf:"varint,1,rep,name=game_id_list,json=gameIdList" json:"game_id_list,omitempty"`
	Total      uint32   `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetRechargeGameResp) Reset()         { *m = GetRechargeGameResp{} }
func (m *GetRechargeGameResp) String() string { return proto.CompactTextString(m) }
func (*GetRechargeGameResp) ProtoMessage()    {}
func (*GetRechargeGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{29}
}

func (m *GetRechargeGameResp) GetGameIdList() []uint32 {
	if m != nil {
		return m.GameIdList
	}
	return nil
}

func (m *GetRechargeGameResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type GetConsumeRoomReq struct {
	GuildId    uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LimitStart uint32 `protobuf:"varint,2,req,name=limit_start,json=limitStart" json:"limit_start"`
	LimitEnd   uint32 `protobuf:"varint,3,req,name=limit_end,json=limitEnd" json:"limit_end"`
}

func (m *GetConsumeRoomReq) Reset()                    { *m = GetConsumeRoomReq{} }
func (m *GetConsumeRoomReq) String() string            { return proto.CompactTextString(m) }
func (*GetConsumeRoomReq) ProtoMessage()               {}
func (*GetConsumeRoomReq) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{30} }

func (m *GetConsumeRoomReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetConsumeRoomReq) GetLimitStart() uint32 {
	if m != nil {
		return m.LimitStart
	}
	return 0
}

func (m *GetConsumeRoomReq) GetLimitEnd() uint32 {
	if m != nil {
		return m.LimitEnd
	}
	return 0
}

type GetConsumeRoomResp struct {
	RoomIdList []uint32 `protobuf:"varint,1,rep,name=room_id_list,json=roomIdList" json:"room_id_list,omitempty"`
	Total      uint32   `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetConsumeRoomResp) Reset()         { *m = GetConsumeRoomResp{} }
func (m *GetConsumeRoomResp) String() string { return proto.CompactTextString(m) }
func (*GetConsumeRoomResp) ProtoMessage()    {}
func (*GetConsumeRoomResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGolddiamondsvr, []int{31}
}

func (m *GetConsumeRoomResp) GetRoomIdList() []uint32 {
	if m != nil {
		return m.RoomIdList
	}
	return nil
}

func (m *GetConsumeRoomResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type RoomDetailInfo struct {
	RoomId uint32 `protobuf:"varint,1,opt,name=room_id,json=roomId" json:"room_id"`
	Fee    uint32 `protobuf:"varint,2,req,name=fee" json:"fee"`
	Income uint32 `protobuf:"varint,3,req,name=income" json:"income"`
	Id     uint32 `protobuf:"varint,4,req,name=id" json:"id"`
}

func (m *RoomDetailInfo) Reset()                    { *m = RoomDetailInfo{} }
func (m *RoomDetailInfo) String() string            { return proto.CompactTextString(m) }
func (*RoomDetailInfo) ProtoMessage()               {}
func (*RoomDetailInfo) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{32} }

func (m *RoomDetailInfo) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *RoomDetailInfo) GetFee() uint32 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *RoomDetailInfo) GetIncome() uint32 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *RoomDetailInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetRoomDetailResp struct {
	RoomDetailList []*RoomDetailInfo `protobuf:"bytes,1,rep,name=room_detail_list,json=roomDetailList" json:"room_detail_list,omitempty"`
	Total          uint32            `protobuf:"varint,2,req,name=total" json:"total"`
}

func (m *GetRoomDetailResp) Reset()                    { *m = GetRoomDetailResp{} }
func (m *GetRoomDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetRoomDetailResp) ProtoMessage()               {}
func (*GetRoomDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorGolddiamondsvr, []int{33} }

func (m *GetRoomDetailResp) GetRoomDetailList() []*RoomDetailInfo {
	if m != nil {
		return m.RoomDetailList
	}
	return nil
}

func (m *GetRoomDetailResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*AwardDiamondReq)(nil), "golddiamondsvr.AwardDiamondReq")
	proto.RegisterType((*GetIncomeReq)(nil), "golddiamondsvr.GetIncomeReq")
	proto.RegisterType((*IncomeAmount)(nil), "golddiamondsvr.IncomeAmount")
	proto.RegisterType((*GetIncomeResp)(nil), "golddiamondsvr.GetIncomeResp")
	proto.RegisterType((*GetIncomeSingleReq)(nil), "golddiamondsvr.GetIncomeSingleReq")
	proto.RegisterType((*GetIncomeSingleResp)(nil), "golddiamondsvr.GetIncomeSingleResp")
	proto.RegisterType((*IncomeDetail)(nil), "golddiamondsvr.IncomeDetail")
	proto.RegisterType((*GetIncomeDetailByTimeReq)(nil), "golddiamondsvr.GetIncomeDetailByTimeReq")
	proto.RegisterType((*GetIncomeDetailReq)(nil), "golddiamondsvr.GetIncomeDetailReq")
	proto.RegisterType((*GetIncomeDetailResp)(nil), "golddiamondsvr.GetIncomeDetailResp")
	proto.RegisterType((*SettlementBalanceReq)(nil), "golddiamondsvr.SettlementBalanceReq")
	proto.RegisterType((*SettlementBalanceResp)(nil), "golddiamondsvr.SettlementBalanceResp")
	proto.RegisterType((*ReportDetail)(nil), "golddiamondsvr.ReportDetail")
	proto.RegisterType((*ReportDetailReq)(nil), "golddiamondsvr.ReportDetailReq")
	proto.RegisterType((*ReportDetailResp)(nil), "golddiamondsvr.ReportDetailResp")
	proto.RegisterType((*GetCommissionInfoReq)(nil), "golddiamondsvr.GetCommissionInfoReq")
	proto.RegisterType((*GetRoomCommissionInfoReq)(nil), "golddiamondsvr.GetRoomCommissionInfoReq")
	proto.RegisterType((*GetCommissionInfoResp)(nil), "golddiamondsvr.GetCommissionInfoResp")
	proto.RegisterType((*GetCommissionDetailReq)(nil), "golddiamondsvr.GetCommissionDetailReq")
	proto.RegisterType((*CommissionInfo)(nil), "golddiamondsvr.CommissionInfo")
	proto.RegisterType((*GetCommissionDetailResp)(nil), "golddiamondsvr.GetCommissionDetailResp")
	proto.RegisterType((*GetRoomCommissionDetailReq)(nil), "golddiamondsvr.GetRoomCommissionDetailReq")
	proto.RegisterType((*RoomCommissionInfo)(nil), "golddiamondsvr.RoomCommissionInfo")
	proto.RegisterType((*GetRoomCommissionDetailResp)(nil), "golddiamondsvr.GetRoomCommissionDetailResp")
	proto.RegisterType((*MemberDetailInfo)(nil), "golddiamondsvr.MemberDetailInfo")
	proto.RegisterType((*GetMemberDetailResp)(nil), "golddiamondsvr.GetMemberDetailResp")
	proto.RegisterType((*GameDetailInfo)(nil), "golddiamondsvr.GameDetailInfo")
	proto.RegisterType((*GetGameDetailResp)(nil), "golddiamondsvr.GetGameDetailResp")
	proto.RegisterType((*GetRechargeGameReq)(nil), "golddiamondsvr.GetRechargeGameReq")
	proto.RegisterType((*GetRechargeGameResp)(nil), "golddiamondsvr.GetRechargeGameResp")
	proto.RegisterType((*GetConsumeRoomReq)(nil), "golddiamondsvr.GetConsumeRoomReq")
	proto.RegisterType((*GetConsumeRoomResp)(nil), "golddiamondsvr.GetConsumeRoomResp")
	proto.RegisterType((*RoomDetailInfo)(nil), "golddiamondsvr.RoomDetailInfo")
	proto.RegisterType((*GetRoomDetailResp)(nil), "golddiamondsvr.GetRoomDetailResp")
	proto.RegisterEnum("golddiamondsvr.IncomeType", IncomeType_name, IncomeType_value)
}
func (m *AwardDiamondReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardDiamondReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.PaidUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.BoughtTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.UniqSign)))
	i += copy(dAtA[i:], m.UniqSign)
	dAtA[i] = 0x42
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.Extand)))
	i += copy(dAtA[i:], m.Extand)
	return i, nil
}

func (m *GetIncomeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, num := range m.GuildIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *IncomeAmount) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncomeAmount) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.DayIncome))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.MonthIncome))
	return i, nil
}

func (m *GetIncomeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.IncomeList) > 0 {
		for _, msg := range m.IncomeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetIncomeSingleReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeSingleReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StarLevel))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.SourceType))
	return i, nil
}

func (m *GetIncomeSingleResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeSingleResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.DayIncome))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.MonthIncome))
	dAtA[i] = 0x19
	i++
	i = encodeFixed64Golddiamondsvr(dAtA, i, uint64(math3.Float64bits(float64(m.IncomeEstimate))))
	dAtA[i] = 0x25
	i++
	i = encodeFixed32Golddiamondsvr(dAtA, i, uint32(math3.Float32bits(float32(m.Coefficient))))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LastMonthIncome))
	dAtA[i] = 0x31
	i++
	i = encodeFixed64Golddiamondsvr(dAtA, i, uint64(math3.Float64bits(float64(m.LastMonthEstimateIncome))))
	return i, nil
}

func (m *IncomeDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *IncomeDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.PaidUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.BoughtTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.SourceType))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x32
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.OrderId)))
	i += copy(dAtA[i:], m.OrderId)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.Extand)))
	i += copy(dAtA[i:], m.Extand)
	dAtA[i] = 0x40
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.IncomeBalance))
	return i, nil
}

func (m *GetIncomeDetailByTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeDetailByTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *GetIncomeDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Index))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Count))
	return i, nil
}

func (m *GetIncomeDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetIncomeDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.IncomeDetailList) > 0 {
		for _, msg := range m.IncomeDetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *SettlementBalanceReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SettlementBalanceReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.SettlementTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StarLevel))
	return i, nil
}

func (m *SettlementBalanceResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SettlementBalanceResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Detail == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("detail")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Detail.Size()))
		n1, err := m.Detail.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Amount))
	dAtA[i] = 0x19
	i++
	i = encodeFixed64Golddiamondsvr(dAtA, i, uint64(math3.Float64bits(float64(m.RmbIncome))))
	return i, nil
}

func (m *ReportDetail) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportDetail) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.PaidUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.BoughtTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(len(m.Extand)))
	i += copy(dAtA[i:], m.Extand)
	return i, nil
}

func (m *ReportDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.EndTime))
	return i, nil
}

func (m *ReportDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReportDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DetailInfo) > 0 {
		for _, msg := range m.DetailInfo {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetCommissionInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommissionInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x20
	i++
	if m.JustUnsettlement {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitEnd))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.SourceType))
	return i, nil
}

func (m *GetRoomCommissionInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRoomCommissionInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x20
	i++
	if m.JustUnsettlement {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitEnd))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.RoomId))
	dAtA[i] = 0x40
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x48
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.SourceType))
	return i, nil
}

func (m *GetCommissionInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommissionInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.PayerCount))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.CashAmount))
	return i, nil
}

func (m *GetCommissionDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommissionDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitEnd))
	return i, nil
}

func (m *CommissionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CommissionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Fee))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.BoughtTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetCommissionDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommissionDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CommissionList) > 0 {
		for _, msg := range m.CommissionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetRoomCommissionDetailReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRoomCommissionDetailReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.StartTime))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.EndTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.RoomId))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitEnd))
	return i, nil
}

func (m *RoomCommissionInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RoomCommissionInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Fee))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.BoughtTime))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.RoomId))
	return i, nil
}

func (m *GetRoomCommissionDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRoomCommissionDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CommissionList) > 0 {
		for _, msg := range m.CommissionList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *MemberDetailInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MemberDetailInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Fee))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetMemberDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetMemberDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MemberDetailList) > 0 {
		for _, msg := range m.MemberDetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GameDetailInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GameDetailInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Fee))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetGameDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGameDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameDetailList) > 0 {
		for _, msg := range m.GameDetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetRechargeGameReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRechargeGameReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitEnd))
	return i, nil
}

func (m *GetRechargeGameResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRechargeGameResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, num := range m.GameIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *GetConsumeRoomReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeRoomReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitStart))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.LimitEnd))
	return i, nil
}

func (m *GetConsumeRoomResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetConsumeRoomResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RoomIdList) > 0 {
		for _, num := range m.RoomIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func (m *RoomDetailInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RoomDetailInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.RoomId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Fee))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Income))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *GetRoomDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetRoomDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.RoomDetailList) > 0 {
		for _, msg := range m.RoomDetailList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGolddiamondsvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintGolddiamondsvr(dAtA, i, uint64(m.Total))
	return i, nil
}

func encodeFixed64Golddiamondsvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Golddiamondsvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGolddiamondsvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *AwardDiamondReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.PaidUid))
	n += 1 + sovGolddiamondsvr(uint64(m.BoughtTime))
	n += 1 + sovGolddiamondsvr(uint64(m.SourceType))
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	l = len(m.OrderId)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	l = len(m.UniqSign)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	l = len(m.Desc)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	l = len(m.Extand)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	return n
}

func (m *GetIncomeReq) Size() (n int) {
	var l int
	_ = l
	if len(m.GuildIdList) > 0 {
		for _, e := range m.GuildIdList {
			n += 1 + sovGolddiamondsvr(uint64(e))
		}
	}
	return n
}

func (m *IncomeAmount) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.DayIncome))
	n += 1 + sovGolddiamondsvr(uint64(m.MonthIncome))
	return n
}

func (m *GetIncomeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.IncomeList) > 0 {
		for _, e := range m.IncomeList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	return n
}

func (m *GetIncomeSingleReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.StarLevel))
	n += 1 + sovGolddiamondsvr(uint64(m.SourceType))
	return n
}

func (m *GetIncomeSingleResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.DayIncome))
	n += 1 + sovGolddiamondsvr(uint64(m.MonthIncome))
	n += 9
	n += 5
	n += 1 + sovGolddiamondsvr(uint64(m.LastMonthIncome))
	n += 9
	return n
}

func (m *IncomeDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.PaidUid))
	n += 1 + sovGolddiamondsvr(uint64(m.BoughtTime))
	n += 1 + sovGolddiamondsvr(uint64(m.SourceType))
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	l = len(m.Desc)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	l = len(m.OrderId)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	l = len(m.Extand)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	n += 1 + sovGolddiamondsvr(uint64(m.IncomeBalance))
	return n
}

func (m *GetIncomeDetailByTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.StartTime))
	n += 1 + sovGolddiamondsvr(uint64(m.EndTime))
	return n
}

func (m *GetIncomeDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.Index))
	n += 1 + sovGolddiamondsvr(uint64(m.Count))
	return n
}

func (m *GetIncomeDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.IncomeDetailList) > 0 {
		for _, e := range m.IncomeDetailList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *SettlementBalanceReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.SettlementTime))
	n += 1 + sovGolddiamondsvr(uint64(m.StarLevel))
	return n
}

func (m *SettlementBalanceResp) Size() (n int) {
	var l int
	_ = l
	if m.Detail != nil {
		l = m.Detail.Size()
		n += 1 + l + sovGolddiamondsvr(uint64(l))
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Amount))
	n += 9
	return n
}

func (m *ReportDetail) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.PaidUid))
	n += 1 + sovGolddiamondsvr(uint64(m.BoughtTime))
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	l = len(m.Extand)
	n += 1 + l + sovGolddiamondsvr(uint64(l))
	return n
}

func (m *ReportDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.StartTime))
	n += 1 + sovGolddiamondsvr(uint64(m.EndTime))
	return n
}

func (m *ReportDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.DetailInfo) > 0 {
		for _, e := range m.DetailInfo {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	return n
}

func (m *GetCommissionInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.StartTime))
	n += 1 + sovGolddiamondsvr(uint64(m.EndTime))
	n += 2
	n += 1 + sovGolddiamondsvr(uint64(m.LimitStart))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitEnd))
	n += 1 + sovGolddiamondsvr(uint64(m.GameId))
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.SourceType))
	return n
}

func (m *GetRoomCommissionInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.StartTime))
	n += 1 + sovGolddiamondsvr(uint64(m.EndTime))
	n += 2
	n += 1 + sovGolddiamondsvr(uint64(m.LimitStart))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitEnd))
	n += 1 + sovGolddiamondsvr(uint64(m.RoomId))
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.SourceType))
	return n
}

func (m *GetCommissionInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	n += 1 + sovGolddiamondsvr(uint64(m.PayerCount))
	n += 1 + sovGolddiamondsvr(uint64(m.CashAmount))
	return n
}

func (m *GetCommissionDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.StartTime))
	n += 1 + sovGolddiamondsvr(uint64(m.EndTime))
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.GameId))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitStart))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitEnd))
	return n
}

func (m *CommissionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.Fee))
	n += 1 + sovGolddiamondsvr(uint64(m.BoughtTime))
	n += 1 + sovGolddiamondsvr(uint64(m.GameId))
	n += 1 + sovGolddiamondsvr(uint64(m.Id))
	return n
}

func (m *GetCommissionDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CommissionList) > 0 {
		for _, e := range m.CommissionList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *GetRoomCommissionDetailReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.StartTime))
	n += 1 + sovGolddiamondsvr(uint64(m.EndTime))
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.RoomId))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitStart))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitEnd))
	return n
}

func (m *RoomCommissionInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.Fee))
	n += 1 + sovGolddiamondsvr(uint64(m.BoughtTime))
	n += 1 + sovGolddiamondsvr(uint64(m.Id))
	n += 1 + sovGolddiamondsvr(uint64(m.RoomId))
	return n
}

func (m *GetRoomCommissionDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CommissionList) > 0 {
		for _, e := range m.CommissionList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *MemberDetailInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.Uid))
	n += 1 + sovGolddiamondsvr(uint64(m.Fee))
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	n += 1 + sovGolddiamondsvr(uint64(m.Id))
	return n
}

func (m *GetMemberDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MemberDetailList) > 0 {
		for _, e := range m.MemberDetailList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *GameDetailInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GameId))
	n += 1 + sovGolddiamondsvr(uint64(m.Fee))
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	n += 1 + sovGolddiamondsvr(uint64(m.Id))
	return n
}

func (m *GetGameDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameDetailList) > 0 {
		for _, e := range m.GameDetailList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *GetRechargeGameReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitStart))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitEnd))
	return n
}

func (m *GetRechargeGameResp) Size() (n int) {
	var l int
	_ = l
	if len(m.GameIdList) > 0 {
		for _, e := range m.GameIdList {
			n += 1 + sovGolddiamondsvr(uint64(e))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *GetConsumeRoomReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.GuildId))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitStart))
	n += 1 + sovGolddiamondsvr(uint64(m.LimitEnd))
	return n
}

func (m *GetConsumeRoomResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RoomIdList) > 0 {
		for _, e := range m.RoomIdList {
			n += 1 + sovGolddiamondsvr(uint64(e))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func (m *RoomDetailInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGolddiamondsvr(uint64(m.RoomId))
	n += 1 + sovGolddiamondsvr(uint64(m.Fee))
	n += 1 + sovGolddiamondsvr(uint64(m.Income))
	n += 1 + sovGolddiamondsvr(uint64(m.Id))
	return n
}

func (m *GetRoomDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.RoomDetailList) > 0 {
		for _, e := range m.RoomDetailList {
			l = e.Size()
			n += 1 + l + sovGolddiamondsvr(uint64(l))
		}
	}
	n += 1 + sovGolddiamondsvr(uint64(m.Total))
	return n
}

func sovGolddiamondsvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGolddiamondsvr(x uint64) (n int) {
	return sovGolddiamondsvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *AwardDiamondReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardDiamondReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardDiamondReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PaidUid", wireType)
			}
			m.PaidUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PaidUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoughtTime", wireType)
			}
			m.BoughtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BoughtTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UniqSign", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.UniqSign = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extand", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extand = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("paid_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bought_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uniq_sign")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("desc")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("extand")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GuildIdList = append(m.GuildIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGolddiamondsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGolddiamondsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GuildIdList = append(m.GuildIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncomeAmount) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncomeAmount: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncomeAmount: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayIncome", wireType)
			}
			m.DayIncome = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayIncome |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MonthIncome", wireType)
			}
			m.MonthIncome = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MonthIncome |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_income")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("month_income")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncomeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IncomeList = append(m.IncomeList, &IncomeAmount{})
			if err := m.IncomeList[len(m.IncomeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeSingleReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeSingleReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeSingleReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("star_level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeSingleResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeSingleResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeSingleResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DayIncome", wireType)
			}
			m.DayIncome = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DayIncome |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MonthIncome", wireType)
			}
			m.MonthIncome = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MonthIncome |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncomeEstimate", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.IncomeEstimate = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 5 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Coefficient", wireType)
			}
			var v uint32
			if (iNdEx + 4) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 4
			v = uint32(dAtA[iNdEx-4])
			v |= uint32(dAtA[iNdEx-3]) << 8
			v |= uint32(dAtA[iNdEx-2]) << 16
			v |= uint32(dAtA[iNdEx-1]) << 24
			m.Coefficient = float32(math4.Float32frombits(v))
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastMonthIncome", wireType)
			}
			m.LastMonthIncome = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastMonthIncome |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastMonthEstimateIncome", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.LastMonthEstimateIncome = float64(math4.Float64frombits(v))
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("day_income")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("month_income")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income_estimate")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *IncomeDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: IncomeDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: IncomeDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PaidUid", wireType)
			}
			m.PaidUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PaidUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoughtTime", wireType)
			}
			m.BoughtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BoughtTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extand", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extand = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncomeBalance", wireType)
			}
			m.IncomeBalance = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IncomeBalance |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("paid_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bought_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("source_type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("desc")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("order_id")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("extand")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income_balance")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeDetailByTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeDetailByTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeDetailByTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Index", wireType)
			}
			m.Index = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Index |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Count", wireType)
			}
			m.Count = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Count |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("index")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetIncomeDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetIncomeDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetIncomeDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IncomeDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IncomeDetailList = append(m.IncomeDetailList, &IncomeDetail{})
			if err := m.IncomeDetailList[len(m.IncomeDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SettlementBalanceReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SettlementBalanceReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SettlementBalanceReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SettlementTime", wireType)
			}
			m.SettlementTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SettlementTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StarLevel", wireType)
			}
			m.StarLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StarLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("settlement_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("star_level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SettlementBalanceResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SettlementBalanceResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SettlementBalanceResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Detail", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Detail == nil {
				m.Detail = &GetIncomeDetailResp{}
			}
			if err := m.Detail.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 1 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RmbIncome", wireType)
			}
			var v uint64
			if (iNdEx + 8) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += 8
			v = uint64(dAtA[iNdEx-8])
			v |= uint64(dAtA[iNdEx-7]) << 8
			v |= uint64(dAtA[iNdEx-6]) << 16
			v |= uint64(dAtA[iNdEx-5]) << 24
			v |= uint64(dAtA[iNdEx-4]) << 32
			v |= uint64(dAtA[iNdEx-3]) << 40
			v |= uint64(dAtA[iNdEx-2]) << 48
			v |= uint64(dAtA[iNdEx-1]) << 56
			m.RmbIncome = float64(math4.Float64frombits(v))
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detail")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("amount")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("rmb_income")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportDetail) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportDetail: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportDetail: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PaidUid", wireType)
			}
			m.PaidUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PaidUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoughtTime", wireType)
			}
			m.BoughtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BoughtTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (int64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extand", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Extand = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("paid_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bought_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("extand")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReportDetailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReportDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReportDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetailInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetailInfo = append(m.DetailInfo, &ReportDetail{})
			if err := m.DetailInfo[len(m.DetailInfo)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommissionInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommissionInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommissionInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JustUnsettlement", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.JustUnsettlement = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("just_unsettlement")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRoomCommissionInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRoomCommissionInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRoomCommissionInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field JustUnsettlement", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.JustUnsettlement = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SourceType", wireType)
			}
			m.SourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("just_unsettlement")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommissionInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommissionInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommissionInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PayerCount", wireType)
			}
			m.PayerCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PayerCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CashAmount", wireType)
			}
			m.CashAmount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CashAmount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("payer_count")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("cash_amount")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommissionDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommissionDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommissionDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CommissionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CommissionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CommissionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Fee", wireType)
			}
			m.Fee = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fee |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoughtTime", wireType)
			}
			m.BoughtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BoughtTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fee")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bought_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommissionDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommissionDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommissionDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommissionList = append(m.CommissionList, &CommissionInfo{})
			if err := m.CommissionList[len(m.CommissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRoomCommissionDetailReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRoomCommissionDetailReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRoomCommissionDetailReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTime", wireType)
			}
			m.StartTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndTime", wireType)
			}
			m.EndTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_time")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("end_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RoomCommissionInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RoomCommissionInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RoomCommissionInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Fee", wireType)
			}
			m.Fee = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fee |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BoughtTime", wireType)
			}
			m.BoughtTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BoughtTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fee")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("bought_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRoomCommissionDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRoomCommissionDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRoomCommissionDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommissionList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommissionList = append(m.CommissionList, &RoomCommissionInfo{})
			if err := m.CommissionList[len(m.CommissionList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MemberDetailInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MemberDetailInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MemberDetailInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Fee", wireType)
			}
			m.Fee = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fee |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fee")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetMemberDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetMemberDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetMemberDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberDetailList = append(m.MemberDetailList, &MemberDetailInfo{})
			if err := m.MemberDetailList[len(m.MemberDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GameDetailInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GameDetailInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GameDetailInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Fee", wireType)
			}
			m.Fee = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fee |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fee")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGameDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGameDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGameDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameDetailList = append(m.GameDetailList, &GameDetailInfo{})
			if err := m.GameDetailList[len(m.GameDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRechargeGameReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRechargeGameReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRechargeGameReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRechargeGameResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRechargeGameResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRechargeGameResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.GameIdList = append(m.GameIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGolddiamondsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGolddiamondsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.GameIdList = append(m.GameIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeRoomReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeRoomReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeRoomReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitStart", wireType)
			}
			m.LimitStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitStart |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LimitEnd", wireType)
			}
			m.LimitEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LimitEnd |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_start")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit_end")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetConsumeRoomResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetConsumeRoomResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetConsumeRoomResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.RoomIdList = append(m.RoomIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGolddiamondsvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGolddiamondsvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.RoomIdList = append(m.RoomIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomIdList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *RoomDetailInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: RoomDetailInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: RoomDetailInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomId", wireType)
			}
			m.RoomId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RoomId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Fee", wireType)
			}
			m.Fee = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Fee |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Income", wireType)
			}
			m.Income = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Income |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("fee")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("income")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetRoomDetailResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetRoomDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetRoomDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RoomDetailList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RoomDetailList = append(m.RoomDetailList, &RoomDetailInfo{})
			if err := m.RoomDetailList[len(m.RoomDetailList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Total", wireType)
			}
			m.Total = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Total |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGolddiamondsvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGolddiamondsvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("total")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGolddiamondsvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGolddiamondsvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGolddiamondsvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGolddiamondsvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGolddiamondsvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGolddiamondsvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGolddiamondsvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGolddiamondsvr   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/golddiamond/golddiamondsvr.proto", fileDescriptorGolddiamondsvr)
}

var fileDescriptorGolddiamondsvr = []byte{
	// 2021 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x59, 0xcd, 0x6f, 0x1b, 0xc7,
	0x15, 0xf7, 0xee, 0x92, 0x94, 0xf4, 0x28, 0x51, 0xd4, 0xc4, 0x76, 0x08, 0x56, 0x96, 0x56, 0x6b,
	0xcb, 0x56, 0x13, 0xc8, 0x6e, 0x7d, 0x74, 0x5c, 0x03, 0x94, 0x44, 0xd3, 0x8c, 0x65, 0x25, 0x25,
	0x69, 0x04, 0x39, 0x2d, 0x56, 0xdc, 0x11, 0xb5, 0x35, 0xf7, 0x43, 0xdc, 0xa5, 0x1c, 0xa2, 0x48,
	0x9a, 0x22, 0x45, 0x51, 0x34, 0x97, 0xa2, 0x97, 0x5e, 0x5a, 0xa0, 0x05, 0x7c, 0xeb, 0xa1, 0x87,
	0x1e, 0x7b, 0xec, 0x25, 0xa7, 0xa2, 0xc7, 0x1e, 0x8a, 0xa2, 0x70, 0x2f, 0xfe, 0x33, 0x8a, 0x99,
	0xd9, 0x8f, 0xd9, 0xe5, 0x48, 0x5c, 0xc1, 0xae, 0x51, 0x20, 0x37, 0xf1, 0xbd, 0x37, 0xfb, 0x7b,
	0xef, 0xcd, 0xef, 0xbd, 0x79, 0x33, 0x82, 0xef, 0xfa, 0x78, 0x74, 0x6a, 0xf5, 0xb1, 0x7f, 0x67,
	0xe0, 0x0e, 0x4d, 0xd3, 0x32, 0x6c, 0xd7, 0x31, 0xf9, 0xbf, 0xfd, 0xd3, 0xd1, 0x6d, 0x6f, 0xe4,
	0x06, 0x2e, 0xaa, 0xa4, 0xa5, 0xf5, 0x1b, 0x7d, 0xd7, 0xb6, 0x5d, 0xe7, 0x4e, 0x30, 0x3c, 0xf5,
	0xac, 0xfe, 0xb3, 0x21, 0xbe, 0xe3, 0x3f, 0x3b, 0x1c, 0x5b, 0xc3, 0xc0, 0x72, 0x82, 0x89, 0x87,
	0xd9, 0x2a, 0xed, 0x2f, 0x32, 0x2c, 0x37, 0x9e, 0x1b, 0x23, 0x73, 0x8f, 0xad, 0xec, 0xe0, 0x13,
	0xb4, 0x0e, 0xf3, 0x83, 0xb1, 0x35, 0x34, 0x75, 0xcb, 0xac, 0x49, 0xaa, 0xbc, 0xb5, 0xb4, 0x53,
	0xf8, 0xe6, 0x5f, 0xeb, 0x97, 0x3a, 0x73, 0x54, 0xda, 0x36, 0x89, 0x81, 0x67, 0x58, 0xa6, 0x3e,
	0xb6, 0xcc, 0x9a, 0xcc, 0x1b, 0x10, 0xe9, 0x53, 0xcb, 0x44, 0x9b, 0x50, 0x3e, 0x74, 0xc7, 0x83,
	0xe3, 0x40, 0x0f, 0x2c, 0x1b, 0xd7, 0x14, 0x55, 0xde, 0x2a, 0x84, 0x36, 0xc0, 0x14, 0x3d, 0xcb,
	0xc6, 0xc4, 0xcc, 0x77, 0xc7, 0xa3, 0x3e, 0xd6, 0x89, 0x47, 0xb5, 0x02, 0xf7, 0x29, 0x60, 0x8a,
	0xde, 0xc4, 0xc3, 0x68, 0x15, 0x4a, 0x96, 0xd3, 0x77, 0x6d, 0x5c, 0x2b, 0xaa, 0xf2, 0x96, 0x12,
	0x5a, 0x84, 0x32, 0xe2, 0x8c, 0x3b, 0x32, 0xf1, 0x88, 0x78, 0x5b, 0x52, 0xe5, 0xad, 0x85, 0xc8,
	0x19, 0x2a, 0x6d, 0x9b, 0x68, 0x03, 0x16, 0xc6, 0x8e, 0x75, 0xa2, 0xfb, 0xd6, 0xc0, 0xa9, 0xcd,
	0x71, 0x16, 0xf3, 0x44, 0xdc, 0xb5, 0x06, 0x0e, 0xaa, 0x41, 0xc1, 0xc4, 0x7e, 0xbf, 0x36, 0xcf,
	0x69, 0xa9, 0x84, 0x60, 0xe3, 0xcf, 0x02, 0xc3, 0x31, 0x6b, 0x0b, 0x9c, 0x2e, 0x94, 0x69, 0x77,
	0x61, 0xb1, 0x85, 0x83, 0x36, 0x75, 0x84, 0x64, 0x4e, 0x83, 0xa5, 0x28, 0x73, 0xfa, 0xd0, 0xf2,
	0x83, 0x9a, 0xa4, 0x2a, 0x5b, 0x4b, 0x9d, 0x72, 0x98, 0xb8, 0x7d, 0xcb, 0x0f, 0xb4, 0xcf, 0x61,
	0x91, 0x2d, 0x68, 0xd8, 0xee, 0xd8, 0x09, 0x66, 0x67, 0xfb, 0x3a, 0x80, 0x69, 0x4c, 0xf4, 0x30,
	0x05, 0x32, 0x97, 0x82, 0x05, 0xd3, 0x98, 0xb0, 0x6f, 0xa1, 0x5b, 0xb0, 0x68, 0xbb, 0x4e, 0x70,
	0x1c, 0x99, 0x29, 0x9c, 0x59, 0x99, 0x6a, 0x98, 0xa1, 0x76, 0x00, 0x4b, 0x9c, 0xcb, 0xbe, 0x87,
	0x7e, 0x00, 0x65, 0xb6, 0x26, 0xf1, 0xb8, 0x7c, 0x77, 0xf5, 0x76, 0x86, 0x63, 0xbc, 0xcb, 0x1d,
	0x60, 0x0b, 0x68, 0x38, 0x3f, 0x95, 0x00, 0xc5, 0x1f, 0xec, 0x5a, 0xce, 0x60, 0x88, 0x73, 0x71,
	0xe8, 0x3a, 0x80, 0x1f, 0x18, 0x23, 0x7d, 0x88, 0x4f, 0xf1, 0x30, 0xc5, 0xa2, 0x05, 0x22, 0xdf,
	0x27, 0xe2, 0x2c, 0x41, 0x14, 0x55, 0x12, 0x11, 0x44, 0xfb, 0x93, 0x0c, 0xef, 0x4c, 0xf9, 0xe0,
	0x7b, 0x99, 0xcc, 0x49, 0xf9, 0x32, 0x27, 0x9f, 0x91, 0x39, 0xb4, 0x0d, 0xcb, 0x61, 0xa2, 0xb0,
	0x1f, 0x58, 0xb6, 0x11, 0xb0, 0x2c, 0x4b, 0xa1, 0x6d, 0x85, 0x29, 0x9b, 0xa1, 0x0e, 0xdd, 0x84,
	0x72, 0xdf, 0xc5, 0x47, 0x47, 0x56, 0xdf, 0xc2, 0x4e, 0x50, 0x2b, 0xa8, 0xd2, 0x96, 0x1c, 0x7d,
	0x96, 0x53, 0xa0, 0xef, 0xc1, 0xca, 0xd0, 0xf0, 0x03, 0x3d, 0xe5, 0x44, 0x51, 0x95, 0x62, 0x27,
	0x96, 0x89, 0xfa, 0x09, 0xe7, 0x48, 0x03, 0xea, 0xdc, 0x8a, 0xc8, 0x99, 0x68, 0x69, 0x49, 0x95,
	0x62, 0x9f, 0xde, 0x8d, 0x97, 0x46, 0x6e, 0x85, 0x2c, 0xf8, 0x83, 0x1c, 0xb1, 0x70, 0x0f, 0x07,
	0x86, 0x35, 0x4c, 0x95, 0xb4, 0x94, 0xa3, 0xa4, 0xe5, 0x7c, 0x25, 0xad, 0xcc, 0x2c, 0xe9, 0x82,
	0xa0, 0xa4, 0xa3, 0x72, 0x2c, 0x4e, 0x95, 0xe3, 0xcc, 0x62, 0x4f, 0xea, 0x75, 0x6e, 0xba, 0x5e,
	0xd1, 0xfb, 0x10, 0xee, 0x92, 0x7e, 0x68, 0x0c, 0x0d, 0xa7, 0x8f, 0x69, 0xc5, 0x47, 0xf0, 0x4b,
	0x4c, 0xb7, 0xc3, 0x54, 0x84, 0xd9, 0xb5, 0x98, 0x55, 0x2c, 0x4d, 0x3b, 0x13, 0x12, 0xe4, 0x45,
	0xf8, 0xcd, 0xa5, 0x2b, 0xc5, 0x6f, 0x96, 0xad, 0x75, 0x98, 0xc7, 0x8e, 0x99, 0x34, 0xc9, 0xf8,
	0x2b, 0xd8, 0x31, 0x89, 0x81, 0x66, 0x73, 0xc5, 0xc5, 0x5c, 0xc8, 0x05, 0x5e, 0x87, 0xa2, 0xe5,
	0x98, 0xf8, 0xb3, 0x14, 0x2e, 0x13, 0x11, 0x5d, 0x9f, 0x54, 0x71, 0x0a, 0x90, 0x89, 0xb4, 0xcf,
	0xb9, 0x3a, 0x8a, 0xe0, 0x7c, 0x0f, 0x7d, 0x08, 0x28, 0x4c, 0x9b, 0x49, 0x85, 0x39, 0x3a, 0x45,
	0xb8, 0xba, 0x6a, 0x71, 0xbf, 0x48, 0xbf, 0x20, 0xf0, 0x81, 0x1b, 0x18, 0xe9, 0x92, 0x67, 0x22,
	0xed, 0x6b, 0x09, 0x2e, 0x77, 0x71, 0x10, 0x0c, 0xb1, 0x8d, 0x9d, 0x20, 0xdc, 0x87, 0x5c, 0x01,
	0x6f, 0xc3, 0xb2, 0x1f, 0x2f, 0x9c, 0x4e, 0x79, 0x25, 0x51, 0xd2, 0xbc, 0xa7, 0x9b, 0x8f, 0x22,
	0x6c, 0x3e, 0xda, 0x6f, 0x24, 0xb8, 0x22, 0xf0, 0xc6, 0xf7, 0xd0, 0x07, 0x50, 0x62, 0x89, 0xa0,
	0xce, 0x94, 0xef, 0x5e, 0xcf, 0xe6, 0x40, 0x90, 0xc4, 0x4e, 0xb8, 0x84, 0x30, 0xd4, 0xa0, 0x6d,
	0x34, 0x55, 0x43, 0xa1, 0x8c, 0x78, 0x36, 0xb2, 0x0f, 0xf9, 0x2e, 0x1e, 0xd5, 0xf2, 0xc2, 0xc8,
	0x3e, 0x0c, 0xab, 0xf7, 0xcf, 0x12, 0x2c, 0x76, 0xb0, 0xe7, 0x8e, 0x82, 0xd7, 0xa8, 0xde, 0x25,
	0x41, 0xf5, 0xf2, 0x79, 0x56, 0x44, 0x79, 0x3e, 0xbf, 0x6e, 0x93, 0xe2, 0x2b, 0x0a, 0x0e, 0xcb,
	0x4f, 0x60, 0x99, 0x77, 0x9a, 0xec, 0x6b, 0xba, 0x48, 0xa4, 0xd9, 0x45, 0x22, 0x8b, 0x8a, 0xe4,
	0x87, 0x50, 0x4d, 0x7f, 0x98, 0x9d, 0x6a, 0x21, 0x57, 0x2d, 0xe7, 0xc8, 0x3d, 0x8b, 0xab, 0xa9,
	0x65, 0xc0, 0x16, 0xb4, 0x9d, 0x23, 0x57, 0xfb, 0x87, 0x0c, 0x97, 0x5b, 0x38, 0xd8, 0x75, 0x6d,
	0xdb, 0xf2, 0x7d, 0xcb, 0x75, 0x88, 0xf4, 0xed, 0xd5, 0x3d, 0xfa, 0x3e, 0xac, 0xfc, 0x68, 0xec,
	0x07, 0xfa, 0xd8, 0x49, 0x98, 0x4b, 0x53, 0x3e, 0x1f, 0x5a, 0x56, 0x89, 0xfa, 0x29, 0xa7, 0x25,
	0x5b, 0x3c, 0xb4, 0x6c, 0x2b, 0xd0, 0x29, 0x0c, 0x3d, 0x41, 0xe2, 0x2d, 0xa6, 0x8a, 0x2e, 0x91,
	0x93, 0x69, 0x88, 0x99, 0x61, 0xc7, 0xa4, 0x67, 0x45, 0x64, 0x34, 0x4f, 0xc5, 0x4d, 0xc7, 0x44,
	0xd7, 0x60, 0x6e, 0x60, 0xd8, 0x98, 0x84, 0x38, 0xc7, 0x19, 0x94, 0x88, 0xb0, 0x6d, 0xa2, 0xab,
	0xa0, 0x10, 0x9e, 0xcd, 0x73, 0x2a, 0x22, 0xc8, 0xb6, 0xfe, 0x85, 0x33, 0x0e, 0xeb, 0x7f, 0xca,
	0xb4, 0xad, 0x76, 0x5c, 0xd7, 0xfe, 0xd6, 0xa6, 0x77, 0xe4, 0xba, 0xf6, 0x54, 0x7a, 0x89, 0xf0,
	0xf5, 0xd3, 0xfb, 0x95, 0x04, 0x57, 0x04, 0xcc, 0xf5, 0x3d, 0xae, 0x76, 0xf9, 0xcc, 0x46, 0xb5,
	0xbb, 0x09, 0x65, 0xcf, 0x98, 0xe0, 0x91, 0xde, 0x8f, 0x7b, 0x53, 0xfc, 0x79, 0xaa, 0xd8, 0xa5,
	0xfd, 0x69, 0x13, 0xca, 0x7d, 0xc3, 0x3f, 0xd6, 0xc3, 0x16, 0x96, 0x3a, 0xdf, 0x89, 0x82, 0x4d,
	0x88, 0xda, 0x97, 0x32, 0x5c, 0x4d, 0x79, 0x71, 0x81, 0xc3, 0xeb, 0xcd, 0x6c, 0x71, 0x98, 0xc6,
	0x42, 0x36, 0x8d, 0x1c, 0xb9, 0x8b, 0x02, 0x72, 0x67, 0xb6, 0xb9, 0xc4, 0xc7, 0x77, 0xd6, 0x36,
	0xcf, 0x71, 0x46, 0xf1, 0x36, 0x6b, 0xbf, 0x95, 0xa0, 0x92, 0xde, 0x85, 0xc8, 0x27, 0x3e, 0x6a,
	0xea, 0xd3, 0x55, 0x50, 0x8e, 0x70, 0x3a, 0x54, 0x22, 0x10, 0x5d, 0xa3, 0x44, 0x5d, 0x9b, 0x0b,
	0xa9, 0x20, 0x08, 0xe9, 0x32, 0xc8, 0x16, 0xeb, 0xc8, 0x91, 0x46, 0xb6, 0x4c, 0xed, 0x0b, 0x78,
	0x57, 0xb8, 0x41, 0xbe, 0x87, 0x5a, 0xb0, 0xdc, 0x8f, 0xe5, 0xfc, 0x59, 0xbf, 0x96, 0xed, 0x9f,
	0x19, 0x96, 0x55, 0x92, 0x65, 0x33, 0xcf, 0xfa, 0x9f, 0xc9, 0x50, 0x9f, 0x6a, 0x03, 0xff, 0x4f,
	0x2c, 0x89, 0x6a, 0xb4, 0x28, 0xa8, 0xd1, 0x37, 0xc7, 0x92, 0xdf, 0x4b, 0x80, 0xa6, 0x5b, 0xe1,
	0xff, 0x8a, 0x29, 0x8c, 0x0a, 0x85, 0x34, 0x15, 0x66, 0x04, 0xab, 0xfd, 0x5c, 0x82, 0xef, 0x9c,
	0xb9, 0x53, 0xbe, 0x87, 0x1e, 0x9f, 0x45, 0x17, 0x6d, 0xea, 0xb8, 0x9d, 0xee, 0xf9, 0x17, 0xa1,
	0xcc, 0x29, 0x54, 0x9f, 0x60, 0xfb, 0x10, 0x8f, 0xf6, 0xe2, 0x83, 0xfa, 0xc2, 0x89, 0x4a, 0x9a,
	0xa0, 0x22, 0x68, 0x82, 0xc2, 0xfc, 0x90, 0x8b, 0x00, 0x19, 0x8b, 0x79, 0x6c, 0x1a, 0xf8, 0x01,
	0x20, 0x9b, 0xca, 0x04, 0x63, 0xb1, 0x9a, 0x8d, 0x3d, 0xeb, 0x79, 0xa7, 0x6a, 0x73, 0x92, 0x99,
	0xb1, 0xff, 0x18, 0x2a, 0x2d, 0x23, 0x9a, 0x27, 0x69, 0xe4, 0x5c, 0xd5, 0x4b, 0xe2, 0x53, 0xfa,
	0x8d, 0x25, 0x60, 0x02, 0x2b, 0x2d, 0x1c, 0x24, 0xf8, 0x34, 0xfa, 0x47, 0x50, 0xa5, 0xf8, 0xd3,
	0xb1, 0x4f, 0xb5, 0x89, 0xb4, 0xe7, 0x9d, 0xca, 0xc0, 0xc8, 0x7d, 0x25, 0xf8, 0x09, 0xbd, 0x00,
	0x75, 0x70, 0xff, 0xd8, 0x18, 0x0d, 0x30, 0xf9, 0x50, 0xae, 0xee, 0x90, 0x29, 0x50, 0x39, 0x4f,
	0x81, 0x2a, 0xc2, 0x02, 0xed, 0xd2, 0xbd, 0x4f, 0x3b, 0xe0, 0x7b, 0x48, 0x85, 0xc5, 0x30, 0xfb,
	0xfc, 0x43, 0x0f, 0xb0, 0xe4, 0xcf, 0x8c, 0xea, 0x0b, 0x9a, 0xd0, 0x5d, 0xd7, 0xf1, 0xc7, 0x36,
	0x26, 0x55, 0xf1, 0x96, 0x83, 0xea, 0xd0, 0xac, 0xa6, 0xf0, 0x59, 0x4c, 0x61, 0x1f, 0x48, 0xc5,
	0xc4, 0xda, 0x40, 0x1e, 0x86, 0x92, 0x2f, 0xa5, 0x19, 0x1a, 0xf5, 0x15, 0x49, 0x3c, 0xe8, 0xbc,
	0x61, 0x86, 0x26, 0xf8, 0x11, 0x43, 0x29, 0x7e, 0x0e, 0x86, 0xa6, 0x3d, 0xef, 0x54, 0x46, 0xf1,
	0xef, 0x59, 0x71, 0xbf, 0xf7, 0x47, 0x09, 0x80, 0xdd, 0xcb, 0xe8, 0xcb, 0x46, 0x0d, 0x2e, 0xb7,
	0x1a, 0x4f, 0x9a, 0x7a, 0xa7, 0xb9, 0xfb, 0xa8, 0xd1, 0x69, 0x91, 0x3f, 0x76, 0x1a, 0xbd, 0x66,
	0x55, 0x42, 0x08, 0x2a, 0xad, 0xa7, 0xed, 0xfd, 0x3d, 0xbd, 0xfb, 0x69, 0x57, 0xef, 0x35, 0xba,
	0x8f, 0xab, 0x72, 0x22, 0xfb, 0xe8, 0xe3, 0x26, 0x93, 0x29, 0xa8, 0x02, 0xd0, 0x6d, 0xf6, 0x7a,
	0xfb, 0xcd, 0x27, 0xcd, 0x83, 0x5e, 0xb5, 0x80, 0x56, 0xa1, 0xc6, 0x6c, 0x3e, 0x69, 0xee, 0x3f,
	0x6c, 0x74, 0x9a, 0xfa, 0xc3, 0x76, 0xa7, 0xdb, 0xd3, 0x3f, 0xfc, 0xa8, 0x7d, 0x50, 0x2d, 0xa2,
	0x2b, 0xb0, 0xb2, 0xfb, 0xa8, 0x71, 0x70, 0xd0, 0xdc, 0xd7, 0xbb, 0xcd, 0x83, 0x3d, 0xbd, 0xd5,
	0x7e, 0xd8, 0xab, 0x96, 0xd0, 0x55, 0x40, 0x89, 0x38, 0xfe, 0xd8, 0xdc, 0xdd, 0xbf, 0x56, 0xa1,
	0xd2, 0x72, 0x87, 0xd1, 0x73, 0x6f, 0xf7, 0x74, 0x84, 0x6c, 0x58, 0xe4, 0x5f, 0x80, 0xd1, 0x7a,
	0x36, 0x39, 0x99, 0xf7, 0xe1, 0xfa, 0xea, 0xed, 0xf8, 0x4d, 0xf9, 0x76, 0xf7, 0xf1, 0x0e, 0x7b,
	0x53, 0x6e, 0xda, 0x5e, 0x30, 0xd1, 0x3f, 0xde, 0xd1, 0xd6, 0xbe, 0x7c, 0xf1, 0x4a, 0x91, 0x7e,
	0xf9, 0xe2, 0x95, 0x22, 0x0f, 0xee, 0xfd, 0xfa, 0xc5, 0x2b, 0x65, 0x69, 0x7b, 0xa0, 0xde, 0x8f,
	0x98, 0xfd, 0x00, 0x7d, 0x0a, 0x0b, 0xf1, 0xf5, 0x18, 0xad, 0x9e, 0x79, 0x73, 0x26, 0x40, 0xd7,
	0xce, 0xd1, 0xfa, 0x9e, 0xb6, 0x4c, 0x90, 0x64, 0x82, 0x74, 0x89, 0xe0, 0x5c, 0x42, 0xcf, 0x60,
	0x39, 0x73, 0xf3, 0x46, 0xda, 0xcc, 0xab, 0xf9, 0x49, 0x3d, 0xcf, 0xf5, 0x9d, 0x81, 0x29, 0x1c,
	0xd8, 0x73, 0x3a, 0x67, 0x4f, 0xbf, 0x0e, 0xa1, 0xad, 0x19, 0x9f, 0x8b, 0x1f, 0x91, 0x2e, 0x00,
	0x5c, 0xe0, 0x80, 0x7f, 0x27, 0xc1, 0xca, 0xd4, 0xbb, 0x04, 0xba, 0x91, 0xfd, 0x96, 0xe8, 0x21,
	0xa5, 0xbe, 0x99, 0xc3, 0xca, 0xf7, 0xb4, 0x0f, 0x08, 0x66, 0x91, 0x60, 0x16, 0x07, 0xf7, 0x82,
	0x7b, 0x43, 0x02, 0x7c, 0x8b, 0xdf, 0x46, 0x75, 0x3b, 0x50, 0xef, 0x93, 0x51, 0xe3, 0x81, 0xba,
	0x3d, 0x54, 0xef, 0x27, 0x8f, 0x29, 0x0f, 0x1e, 0xa0, 0xaf, 0x25, 0x6e, 0x1b, 0xd8, 0x6b, 0xec,
	0x39, 0xdb, 0x10, 0x3f, 0x19, 0x9f, 0x93, 0x8d, 0xe4, 0x49, 0x57, 0xbb, 0x43, 0x3c, 0x2b, 0x11,
	0xcf, 0x94, 0xc1, 0x3d, 0x9f, 0xf8, 0x75, 0x2d, 0x45, 0x2f, 0x75, 0xdb, 0x4f, 0xb9, 0x83, 0xfa,
	0xb0, 0xf2, 0xd4, 0x33, 0x8d, 0x00, 0x87, 0x04, 0xde, 0xa3, 0xcf, 0x88, 0xaf, 0x47, 0x71, 0xba,
	0x25, 0x73, 0xdc, 0x96, 0x1c, 0x66, 0xdf, 0x63, 0xce, 0x7d, 0x68, 0xc0, 0x27, 0x75, 0xf5, 0x7c,
	0x83, 0x68, 0xdb, 0xe7, 0x39, 0x8c, 0x93, 0xf0, 0xcc, 0x48, 0xcd, 0x89, 0x37, 0x04, 0x39, 0x9b,
	0xba, 0x55, 0x4f, 0xef, 0xba, 0xf0, 0x82, 0xc8, 0x20, 0xd7, 0x52, 0x14, 0x7f, 0x47, 0x70, 0x47,
	0x40, 0x37, 0xcf, 0xfd, 0x5c, 0x12, 0xe4, 0xad, 0x5c, 0x76, 0x11, 0xf0, 0x3a, 0x07, 0x6c, 0x53,
	0x06, 0xf1, 0x23, 0x53, 0xce, 0x48, 0x45, 0x1c, 0xca, 0xce, 0x6d, 0x0c, 0x4e, 0xe5, 0xe0, 0x2c,
	0xfa, 0x3f, 0x91, 0x64, 0x4a, 0xc9, 0x09, 0xb6, 0x21, 0xb0, 0x4a, 0x0f, 0x49, 0x0c, 0x6a, 0x63,
	0xaa, 0x45, 0xf1, 0xe3, 0x84, 0xb0, 0x36, 0x32, 0x03, 0x8f, 0x30, 0xae, 0xec, 0x4c, 0xc2, 0xc0,
	0x34, 0x0e, 0x6c, 0x42, 0x5b, 0x94, 0xe0, 0x7a, 0x21, 0x6a, 0x51, 0xc2, 0x07, 0x99, 0x0b, 0x51,
	0xe7, 0x3a, 0x07, 0xfd, 0x95, 0x44, 0xef, 0x97, 0xa2, 0x4b, 0x03, 0x7a, 0x6f, 0x26, 0x7a, 0xc2,
	0xa1, 0xf7, 0x73, 0xdb, 0x46, 0x5e, 0xdc, 0xe0, 0xbc, 0x38, 0x86, 0x4a, 0x7a, 0xce, 0x41, 0x1b,
	0xc2, 0x78, 0xf8, 0x39, 0xac, 0xae, 0xcd, 0x32, 0x89, 0x90, 0x36, 0x39, 0xa4, 0x31, 0x1b, 0x13,
	0x5d, 0xd7, 0x4e, 0xb1, 0x36, 0x7f, 0xa2, 0xf3, 0x33, 0xf7, 0x66, 0xaa, 0x50, 0x96, 0x52, 0x73,
	0xcf, 0x05, 0x00, 0x37, 0xce, 0xb0, 0xcc, 0xc2, 0xdd, 0x4a, 0xe0, 0xea, 0xa5, 0x5f, 0xbc, 0x78,
	0xa5, 0xfc, 0xed, 0xf9, 0x4e, 0xf5, 0x9b, 0x97, 0x6b, 0xd2, 0xdf, 0x5f, 0xae, 0x49, 0xff, 0x7e,
	0xb9, 0x26, 0xfd, 0xea, 0x3f, 0x6b, 0x97, 0xfe, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xea, 0x51, 0x13,
	0x09, 0xa9, 0x1e, 0x00, 0x00,
}
