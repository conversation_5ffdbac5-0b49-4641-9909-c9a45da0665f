// Code generated by protoc-gen-go. DO NOT EDIT.
// source: star-train/star-train.proto

package star_train // import "golang.52tt.com/protocol/services/star-train"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 奖励类型
type AwardType int32

const (
	AwardType_AwardTypeNone        AwardType = 0
	AwardType_AwardTypeGoal        AwardType = 1
	AwardType_AwardTypeHelp        AwardType = 2
	AwardType_AwardTypeConsolation AwardType = 3
)

var AwardType_name = map[int32]string{
	0: "AwardTypeNone",
	1: "AwardTypeGoal",
	2: "AwardTypeHelp",
	3: "AwardTypeConsolation",
}
var AwardType_value = map[string]int32{
	"AwardTypeNone":        0,
	"AwardTypeGoal":        1,
	"AwardTypeHelp":        2,
	"AwardTypeConsolation": 3,
}

func (x AwardType) String() string {
	return proto.EnumName(AwardType_name, int32(x))
}
func (AwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{0}
}

type StationType int32

const (
	StationType_StationTypeNone   StationType = 0
	StationType_StationTypeNormal StationType = 1
	StationType_StationTypeEnd    StationType = 2
)

var StationType_name = map[int32]string{
	0: "StationTypeNone",
	1: "StationTypeNormal",
	2: "StationTypeEnd",
}
var StationType_value = map[string]int32{
	"StationTypeNone":   0,
	"StationTypeNormal": 1,
	"StationTypeEnd":    2,
}

func (x StationType) String() string {
	return proto.EnumName(StationType_name, int32(x))
}
func (StationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{1}
}

type WeekDay int32

const (
	WeekDay_Sunday    WeekDay = 0
	WeekDay_Monday    WeekDay = 1
	WeekDay_Tuesday   WeekDay = 2
	WeekDay_Wednesday WeekDay = 3
	WeekDay_Thursday  WeekDay = 4
	WeekDay_Friday    WeekDay = 5
	WeekDay_Saturday  WeekDay = 6
	WeekDay_UnkownDay WeekDay = 7
)

var WeekDay_name = map[int32]string{
	0: "Sunday",
	1: "Monday",
	2: "Tuesday",
	3: "Wednesday",
	4: "Thursday",
	5: "Friday",
	6: "Saturday",
	7: "UnkownDay",
}
var WeekDay_value = map[string]int32{
	"Sunday":    0,
	"Monday":    1,
	"Tuesday":   2,
	"Wednesday": 3,
	"Thursday":  4,
	"Friday":    5,
	"Saturday":  6,
	"UnkownDay": 7,
}

func (x WeekDay) String() string {
	return proto.EnumName(WeekDay_name, int32(x))
}
func (WeekDay) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{2}
}

// 活动状态
type StarTrainStatus int32

const (
	StarTrainStatus_None     StarTrainStatus = 0
	StarTrainStatus_Using    StarTrainStatus = 1
	StarTrainStatus_Unused   StarTrainStatus = 2
	StarTrainStatus_Finished StarTrainStatus = 3
)

var StarTrainStatus_name = map[int32]string{
	0: "None",
	1: "Using",
	2: "Unused",
	3: "Finished",
}
var StarTrainStatus_value = map[string]int32{
	"None":     0,
	"Using":    1,
	"Unused":   2,
	"Finished": 3,
}

func (x StarTrainStatus) String() string {
	return proto.EnumName(StarTrainStatus_name, int32(x))
}
func (StarTrainStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{3}
}

type PrizeType int32

const (
	PrizeType_PRIZE_TYPE_UNSPECIFIED      PrizeType = 0
	PrizeType_PRIZE_TYPE_STAR_AWARD       PrizeType = 1
	PrizeType_PRIZE_TYPE_BOOST_AWARD      PrizeType = 2
	PrizeType_PRIZE_TYPE_COMFORT_AWARD    PrizeType = 3
	PrizeType_PRIZE_TYPE_HONOR_TOP1_AWARD PrizeType = 4
	PrizeType_PRIZE_TYPE_HONOR_TOP2_AWARD PrizeType = 5
	PrizeType_PRIZE_TYPE_HONOR_TOP3_AWARD PrizeType = 6
)

var PrizeType_name = map[int32]string{
	0: "PRIZE_TYPE_UNSPECIFIED",
	1: "PRIZE_TYPE_STAR_AWARD",
	2: "PRIZE_TYPE_BOOST_AWARD",
	3: "PRIZE_TYPE_COMFORT_AWARD",
	4: "PRIZE_TYPE_HONOR_TOP1_AWARD",
	5: "PRIZE_TYPE_HONOR_TOP2_AWARD",
	6: "PRIZE_TYPE_HONOR_TOP3_AWARD",
}
var PrizeType_value = map[string]int32{
	"PRIZE_TYPE_UNSPECIFIED":      0,
	"PRIZE_TYPE_STAR_AWARD":       1,
	"PRIZE_TYPE_BOOST_AWARD":      2,
	"PRIZE_TYPE_COMFORT_AWARD":    3,
	"PRIZE_TYPE_HONOR_TOP1_AWARD": 4,
	"PRIZE_TYPE_HONOR_TOP2_AWARD": 5,
	"PRIZE_TYPE_HONOR_TOP3_AWARD": 6,
}

func (x PrizeType) String() string {
	return proto.EnumName(PrizeType_name, int32(x))
}
func (PrizeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{4}
}

type StarTrainBroadcast_BroadcastType int32

const (
	StarTrainBroadcast_BROADCAST_TYPE_UNSPECIFIED StarTrainBroadcast_BroadcastType = 0
	StarTrainBroadcast_BROADCAST_TYPE_STAR_AWARD  StarTrainBroadcast_BroadcastType = 1
	StarTrainBroadcast_BROADCAST_TYPE_BOOST_AWARD StarTrainBroadcast_BroadcastType = 2
	StarTrainBroadcast_BROADCAST_TYPE_JOIN        StarTrainBroadcast_BroadcastType = 3
	StarTrainBroadcast_BROADCAST_TYPE_SEAT        StarTrainBroadcast_BroadcastType = 4
)

var StarTrainBroadcast_BroadcastType_name = map[int32]string{
	0: "BROADCAST_TYPE_UNSPECIFIED",
	1: "BROADCAST_TYPE_STAR_AWARD",
	2: "BROADCAST_TYPE_BOOST_AWARD",
	3: "BROADCAST_TYPE_JOIN",
	4: "BROADCAST_TYPE_SEAT",
}
var StarTrainBroadcast_BroadcastType_value = map[string]int32{
	"BROADCAST_TYPE_UNSPECIFIED": 0,
	"BROADCAST_TYPE_STAR_AWARD":  1,
	"BROADCAST_TYPE_BOOST_AWARD": 2,
	"BROADCAST_TYPE_JOIN":        3,
	"BROADCAST_TYPE_SEAT":        4,
}

func (x StarTrainBroadcast_BroadcastType) String() string {
	return proto.EnumName(StarTrainBroadcast_BroadcastType_name, int32(x))
}
func (StarTrainBroadcast_BroadcastType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{31, 0}
}

// 开启时间段配置
type DayTimeRangeInfo struct {
	DayBegin             uint32   `protobuf:"varint,1,opt,name=day_begin,json=dayBegin,proto3" json:"day_begin,omitempty"`
	DayEnd               uint32   `protobuf:"varint,2,opt,name=day_end,json=dayEnd,proto3" json:"day_end,omitempty"`
	WDayList             []uint32 `protobuf:"varint,3,rep,packed,name=w_day_list,json=wDayList,proto3" json:"w_day_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTimeRangeInfo) Reset()         { *m = DayTimeRangeInfo{} }
func (m *DayTimeRangeInfo) String() string { return proto.CompactTextString(m) }
func (*DayTimeRangeInfo) ProtoMessage()    {}
func (*DayTimeRangeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{0}
}
func (m *DayTimeRangeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTimeRangeInfo.Unmarshal(m, b)
}
func (m *DayTimeRangeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTimeRangeInfo.Marshal(b, m, deterministic)
}
func (dst *DayTimeRangeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTimeRangeInfo.Merge(dst, src)
}
func (m *DayTimeRangeInfo) XXX_Size() int {
	return xxx_messageInfo_DayTimeRangeInfo.Size(m)
}
func (m *DayTimeRangeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTimeRangeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayTimeRangeInfo proto.InternalMessageInfo

func (m *DayTimeRangeInfo) GetDayBegin() uint32 {
	if m != nil {
		return m.DayBegin
	}
	return 0
}

func (m *DayTimeRangeInfo) GetDayEnd() uint32 {
	if m != nil {
		return m.DayEnd
	}
	return 0
}

func (m *DayTimeRangeInfo) GetWDayList() []uint32 {
	if m != nil {
		return m.WDayList
	}
	return nil
}

type StarTrainBgInfo struct {
	BgId                 uint32   `protobuf:"varint,1,opt,name=bg_id,json=bgId,proto3" json:"bg_id,omitempty"`
	GiftId               uint32   `protobuf:"varint,2,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,3,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftName             string   `protobuf:"bytes,4,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPic              string   `protobuf:"bytes,5,opt,name=gift_pic,json=giftPic,proto3" json:"gift_pic,omitempty"`
	GiftNum              uint32   `protobuf:"varint,6,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	Stock                uint32   `protobuf:"varint,7,opt,name=stock,proto3" json:"stock,omitempty"`
	AwardType            uint32   `protobuf:"varint,8,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	FinTime              uint32   `protobuf:"varint,9,opt,name=fin_time,json=finTime,proto3" json:"fin_time,omitempty"`
	Months               uint32   `protobuf:"varint,10,opt,name=months,proto3" json:"months,omitempty"`
	DynamicFinTime       uint32   `protobuf:"varint,11,opt,name=dynamic_fin_time,json=dynamicFinTime,proto3" json:"dynamic_fin_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTrainBgInfo) Reset()         { *m = StarTrainBgInfo{} }
func (m *StarTrainBgInfo) String() string { return proto.CompactTextString(m) }
func (*StarTrainBgInfo) ProtoMessage()    {}
func (*StarTrainBgInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{1}
}
func (m *StarTrainBgInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainBgInfo.Unmarshal(m, b)
}
func (m *StarTrainBgInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainBgInfo.Marshal(b, m, deterministic)
}
func (dst *StarTrainBgInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainBgInfo.Merge(dst, src)
}
func (m *StarTrainBgInfo) XXX_Size() int {
	return xxx_messageInfo_StarTrainBgInfo.Size(m)
}
func (m *StarTrainBgInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainBgInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainBgInfo proto.InternalMessageInfo

func (m *StarTrainBgInfo) GetBgId() uint32 {
	if m != nil {
		return m.BgId
	}
	return 0
}

func (m *StarTrainBgInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *StarTrainBgInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *StarTrainBgInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *StarTrainBgInfo) GetGiftPic() string {
	if m != nil {
		return m.GiftPic
	}
	return ""
}

func (m *StarTrainBgInfo) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

func (m *StarTrainBgInfo) GetStock() uint32 {
	if m != nil {
		return m.Stock
	}
	return 0
}

func (m *StarTrainBgInfo) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *StarTrainBgInfo) GetFinTime() uint32 {
	if m != nil {
		return m.FinTime
	}
	return 0
}

func (m *StarTrainBgInfo) GetMonths() uint32 {
	if m != nil {
		return m.Months
	}
	return 0
}

func (m *StarTrainBgInfo) GetDynamicFinTime() uint32 {
	if m != nil {
		return m.DynamicFinTime
	}
	return 0
}

// 站点信息
type StationInfo struct {
	StationId            string             `protobuf:"bytes,1,opt,name=station_id,json=stationId,proto3" json:"station_id,omitempty"`
	Distance             uint32             `protobuf:"varint,2,opt,name=distance,proto3" json:"distance,omitempty"`
	GoalPrize            *StarTrainBgInfo   `protobuf:"bytes,3,opt,name=goal_prize,json=goalPrize,proto3" json:"goal_prize,omitempty"`
	HelpPrize            *StarTrainBgInfo   `protobuf:"bytes,4,opt,name=help_prize,json=helpPrize,proto3" json:"help_prize,omitempty"`
	ConsolationPrizeList []*StarTrainBgInfo `protobuf:"bytes,5,rep,name=consolation_prize_list,json=consolationPrizeList,proto3" json:"consolation_prize_list,omitempty"`
	StationType          uint32             `protobuf:"varint,6,opt,name=station_type,json=stationType,proto3" json:"station_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *StationInfo) Reset()         { *m = StationInfo{} }
func (m *StationInfo) String() string { return proto.CompactTextString(m) }
func (*StationInfo) ProtoMessage()    {}
func (*StationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{2}
}
func (m *StationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StationInfo.Unmarshal(m, b)
}
func (m *StationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StationInfo.Marshal(b, m, deterministic)
}
func (dst *StationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StationInfo.Merge(dst, src)
}
func (m *StationInfo) XXX_Size() int {
	return xxx_messageInfo_StationInfo.Size(m)
}
func (m *StationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StationInfo proto.InternalMessageInfo

func (m *StationInfo) GetStationId() string {
	if m != nil {
		return m.StationId
	}
	return ""
}

func (m *StationInfo) GetDistance() uint32 {
	if m != nil {
		return m.Distance
	}
	return 0
}

func (m *StationInfo) GetGoalPrize() *StarTrainBgInfo {
	if m != nil {
		return m.GoalPrize
	}
	return nil
}

func (m *StationInfo) GetHelpPrize() *StarTrainBgInfo {
	if m != nil {
		return m.HelpPrize
	}
	return nil
}

func (m *StationInfo) GetConsolationPrizeList() []*StarTrainBgInfo {
	if m != nil {
		return m.ConsolationPrizeList
	}
	return nil
}

func (m *StationInfo) GetStationType() uint32 {
	if m != nil {
		return m.StationType
	}
	return 0
}

// 列车路线信息
type TrainRouteInfo struct {
	TrainId              uint32         `protobuf:"varint,1,opt,name=train_id,json=trainId,proto3" json:"train_id,omitempty"`
	Weight               uint32         `protobuf:"varint,2,opt,name=weight,proto3" json:"weight,omitempty"`
	GoalStation          *StationInfo   `protobuf:"bytes,3,opt,name=goal_station,json=goalStation,proto3" json:"goal_station,omitempty"`
	StationList          []*StationInfo `protobuf:"bytes,4,rep,name=station_list,json=stationList,proto3" json:"station_list,omitempty"`
	FixedOrder           uint32         `protobuf:"varint,5,opt,name=fixed_order,json=fixedOrder,proto3" json:"fixed_order,omitempty"`
	TotalReward          uint64         `protobuf:"varint,7,opt,name=total_reward,json=totalReward,proto3" json:"total_reward,omitempty"`
	RewardRatio          float32        `protobuf:"fixed32,8,opt,name=reward_ratio,json=rewardRatio,proto3" json:"reward_ratio,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *TrainRouteInfo) Reset()         { *m = TrainRouteInfo{} }
func (m *TrainRouteInfo) String() string { return proto.CompactTextString(m) }
func (*TrainRouteInfo) ProtoMessage()    {}
func (*TrainRouteInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{3}
}
func (m *TrainRouteInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrainRouteInfo.Unmarshal(m, b)
}
func (m *TrainRouteInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrainRouteInfo.Marshal(b, m, deterministic)
}
func (dst *TrainRouteInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrainRouteInfo.Merge(dst, src)
}
func (m *TrainRouteInfo) XXX_Size() int {
	return xxx_messageInfo_TrainRouteInfo.Size(m)
}
func (m *TrainRouteInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TrainRouteInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TrainRouteInfo proto.InternalMessageInfo

func (m *TrainRouteInfo) GetTrainId() uint32 {
	if m != nil {
		return m.TrainId
	}
	return 0
}

func (m *TrainRouteInfo) GetWeight() uint32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *TrainRouteInfo) GetGoalStation() *StationInfo {
	if m != nil {
		return m.GoalStation
	}
	return nil
}

func (m *TrainRouteInfo) GetStationList() []*StationInfo {
	if m != nil {
		return m.StationList
	}
	return nil
}

func (m *TrainRouteInfo) GetFixedOrder() uint32 {
	if m != nil {
		return m.FixedOrder
	}
	return 0
}

func (m *TrainRouteInfo) GetTotalReward() uint64 {
	if m != nil {
		return m.TotalReward
	}
	return 0
}

func (m *TrainRouteInfo) GetRewardRatio() float32 {
	if m != nil {
		return m.RewardRatio
	}
	return 0
}

// 玩法活动配置
type StarTrainConf struct {
	ConfId               uint32              `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	ActivityName         string              `protobuf:"bytes,2,opt,name=activity_name,json=activityName,proto3" json:"activity_name,omitempty"`
	ActivityBegin        int64               `protobuf:"varint,3,opt,name=activity_begin,json=activityBegin,proto3" json:"activity_begin,omitempty"`
	ActivityEnd          int64               `protobuf:"varint,4,opt,name=activity_end,json=activityEnd,proto3" json:"activity_end,omitempty"`
	TrainList            []*TrainRouteInfo   `protobuf:"bytes,5,rep,name=train_list,json=trainList,proto3" json:"train_list,omitempty"`
	TimeList             []*DayTimeRangeInfo `protobuf:"bytes,6,rep,name=time_list,json=timeList,proto3" json:"time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *StarTrainConf) Reset()         { *m = StarTrainConf{} }
func (m *StarTrainConf) String() string { return proto.CompactTextString(m) }
func (*StarTrainConf) ProtoMessage()    {}
func (*StarTrainConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{4}
}
func (m *StarTrainConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainConf.Unmarshal(m, b)
}
func (m *StarTrainConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainConf.Marshal(b, m, deterministic)
}
func (dst *StarTrainConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainConf.Merge(dst, src)
}
func (m *StarTrainConf) XXX_Size() int {
	return xxx_messageInfo_StarTrainConf.Size(m)
}
func (m *StarTrainConf) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainConf.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainConf proto.InternalMessageInfo

func (m *StarTrainConf) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

func (m *StarTrainConf) GetActivityName() string {
	if m != nil {
		return m.ActivityName
	}
	return ""
}

func (m *StarTrainConf) GetActivityBegin() int64 {
	if m != nil {
		return m.ActivityBegin
	}
	return 0
}

func (m *StarTrainConf) GetActivityEnd() int64 {
	if m != nil {
		return m.ActivityEnd
	}
	return 0
}

func (m *StarTrainConf) GetTrainList() []*TrainRouteInfo {
	if m != nil {
		return m.TrainList
	}
	return nil
}

func (m *StarTrainConf) GetTimeList() []*DayTimeRangeInfo {
	if m != nil {
		return m.TimeList
	}
	return nil
}

// 设置玩法活动配置
type SetStarTrainConfReq struct {
	Conf                 *StarTrainConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetStarTrainConfReq) Reset()         { *m = SetStarTrainConfReq{} }
func (m *SetStarTrainConfReq) String() string { return proto.CompactTextString(m) }
func (*SetStarTrainConfReq) ProtoMessage()    {}
func (*SetStarTrainConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{5}
}
func (m *SetStarTrainConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrainConfReq.Unmarshal(m, b)
}
func (m *SetStarTrainConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrainConfReq.Marshal(b, m, deterministic)
}
func (dst *SetStarTrainConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrainConfReq.Merge(dst, src)
}
func (m *SetStarTrainConfReq) XXX_Size() int {
	return xxx_messageInfo_SetStarTrainConfReq.Size(m)
}
func (m *SetStarTrainConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrainConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrainConfReq proto.InternalMessageInfo

func (m *SetStarTrainConfReq) GetConf() *StarTrainConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type SetStarTrainConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTrainConfResp) Reset()         { *m = SetStarTrainConfResp{} }
func (m *SetStarTrainConfResp) String() string { return proto.CompactTextString(m) }
func (*SetStarTrainConfResp) ProtoMessage()    {}
func (*SetStarTrainConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{6}
}
func (m *SetStarTrainConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrainConfResp.Unmarshal(m, b)
}
func (m *SetStarTrainConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrainConfResp.Marshal(b, m, deterministic)
}
func (dst *SetStarTrainConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrainConfResp.Merge(dst, src)
}
func (m *SetStarTrainConfResp) XXX_Size() int {
	return xxx_messageInfo_SetStarTrainConfResp.Size(m)
}
func (m *SetStarTrainConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrainConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrainConfResp proto.InternalMessageInfo

type GetStarTrainConfReq struct {
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainConfReq) Reset()         { *m = GetStarTrainConfReq{} }
func (m *GetStarTrainConfReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainConfReq) ProtoMessage()    {}
func (*GetStarTrainConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{7}
}
func (m *GetStarTrainConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainConfReq.Unmarshal(m, b)
}
func (m *GetStarTrainConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainConfReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainConfReq.Merge(dst, src)
}
func (m *GetStarTrainConfReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainConfReq.Size(m)
}
func (m *GetStarTrainConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainConfReq proto.InternalMessageInfo

func (m *GetStarTrainConfReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GetStarTrainConfReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetStarTrainConfReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetStarTrainConfResp struct {
	ConfList             []*StarTrainConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	Total                uint32           `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetStarTrainConfResp) Reset()         { *m = GetStarTrainConfResp{} }
func (m *GetStarTrainConfResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainConfResp) ProtoMessage()    {}
func (*GetStarTrainConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{8}
}
func (m *GetStarTrainConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainConfResp.Unmarshal(m, b)
}
func (m *GetStarTrainConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainConfResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainConfResp.Merge(dst, src)
}
func (m *GetStarTrainConfResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainConfResp.Size(m)
}
func (m *GetStarTrainConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainConfResp proto.InternalMessageInfo

func (m *GetStarTrainConfResp) GetConfList() []*StarTrainConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

func (m *GetStarTrainConfResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateStarTrainConfReq struct {
	Conf                 *StarTrainConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UpdateStarTrainConfReq) Reset()         { *m = UpdateStarTrainConfReq{} }
func (m *UpdateStarTrainConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateStarTrainConfReq) ProtoMessage()    {}
func (*UpdateStarTrainConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{9}
}
func (m *UpdateStarTrainConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStarTrainConfReq.Unmarshal(m, b)
}
func (m *UpdateStarTrainConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStarTrainConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateStarTrainConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStarTrainConfReq.Merge(dst, src)
}
func (m *UpdateStarTrainConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateStarTrainConfReq.Size(m)
}
func (m *UpdateStarTrainConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStarTrainConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStarTrainConfReq proto.InternalMessageInfo

func (m *UpdateStarTrainConfReq) GetConf() *StarTrainConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

type UpdateStarTrainConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateStarTrainConfResp) Reset()         { *m = UpdateStarTrainConfResp{} }
func (m *UpdateStarTrainConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateStarTrainConfResp) ProtoMessage()    {}
func (*UpdateStarTrainConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{10}
}
func (m *UpdateStarTrainConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateStarTrainConfResp.Unmarshal(m, b)
}
func (m *UpdateStarTrainConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateStarTrainConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateStarTrainConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateStarTrainConfResp.Merge(dst, src)
}
func (m *UpdateStarTrainConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateStarTrainConfResp.Size(m)
}
func (m *UpdateStarTrainConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateStarTrainConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateStarTrainConfResp proto.InternalMessageInfo

type DelStarTrainConfReq struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrainConfReq) Reset()         { *m = DelStarTrainConfReq{} }
func (m *DelStarTrainConfReq) String() string { return proto.CompactTextString(m) }
func (*DelStarTrainConfReq) ProtoMessage()    {}
func (*DelStarTrainConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{11}
}
func (m *DelStarTrainConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrainConfReq.Unmarshal(m, b)
}
func (m *DelStarTrainConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrainConfReq.Marshal(b, m, deterministic)
}
func (dst *DelStarTrainConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrainConfReq.Merge(dst, src)
}
func (m *DelStarTrainConfReq) XXX_Size() int {
	return xxx_messageInfo_DelStarTrainConfReq.Size(m)
}
func (m *DelStarTrainConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrainConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrainConfReq proto.InternalMessageInfo

func (m *DelStarTrainConfReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

type DelStarTrainConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrainConfResp) Reset()         { *m = DelStarTrainConfResp{} }
func (m *DelStarTrainConfResp) String() string { return proto.CompactTextString(m) }
func (*DelStarTrainConfResp) ProtoMessage()    {}
func (*DelStarTrainConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{12}
}
func (m *DelStarTrainConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrainConfResp.Unmarshal(m, b)
}
func (m *DelStarTrainConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrainConfResp.Marshal(b, m, deterministic)
}
func (dst *DelStarTrainConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrainConfResp.Merge(dst, src)
}
func (m *DelStarTrainConfResp) XXX_Size() int {
	return xxx_messageInfo_DelStarTrainConfResp.Size(m)
}
func (m *DelStarTrainConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrainConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrainConfResp proto.InternalMessageInfo

type GetStarTrainConfByIdReq struct {
	ConfId               uint32   `protobuf:"varint,1,opt,name=conf_id,json=confId,proto3" json:"conf_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainConfByIdReq) Reset()         { *m = GetStarTrainConfByIdReq{} }
func (m *GetStarTrainConfByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainConfByIdReq) ProtoMessage()    {}
func (*GetStarTrainConfByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{13}
}
func (m *GetStarTrainConfByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainConfByIdReq.Unmarshal(m, b)
}
func (m *GetStarTrainConfByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainConfByIdReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainConfByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainConfByIdReq.Merge(dst, src)
}
func (m *GetStarTrainConfByIdReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainConfByIdReq.Size(m)
}
func (m *GetStarTrainConfByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainConfByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainConfByIdReq proto.InternalMessageInfo

func (m *GetStarTrainConfByIdReq) GetConfId() uint32 {
	if m != nil {
		return m.ConfId
	}
	return 0
}

type GetStarTrainConfByIdResp struct {
	Conf                 *StarTrainConf `protobuf:"bytes,1,opt,name=conf,proto3" json:"conf,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetStarTrainConfByIdResp) Reset()         { *m = GetStarTrainConfByIdResp{} }
func (m *GetStarTrainConfByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainConfByIdResp) ProtoMessage()    {}
func (*GetStarTrainConfByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{14}
}
func (m *GetStarTrainConfByIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainConfByIdResp.Unmarshal(m, b)
}
func (m *GetStarTrainConfByIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainConfByIdResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainConfByIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainConfByIdResp.Merge(dst, src)
}
func (m *GetStarTrainConfByIdResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainConfByIdResp.Size(m)
}
func (m *GetStarTrainConfByIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainConfByIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainConfByIdResp proto.InternalMessageInfo

func (m *GetStarTrainConfByIdResp) GetConf() *StarTrainConf {
	if m != nil {
		return m.Conf
	}
	return nil
}

// ========= 活动礼物配置 ===============
type SetStarTrainGiftReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTrainGiftReq) Reset()         { *m = SetStarTrainGiftReq{} }
func (m *SetStarTrainGiftReq) String() string { return proto.CompactTextString(m) }
func (*SetStarTrainGiftReq) ProtoMessage()    {}
func (*SetStarTrainGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{15}
}
func (m *SetStarTrainGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrainGiftReq.Unmarshal(m, b)
}
func (m *SetStarTrainGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrainGiftReq.Marshal(b, m, deterministic)
}
func (dst *SetStarTrainGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrainGiftReq.Merge(dst, src)
}
func (m *SetStarTrainGiftReq) XXX_Size() int {
	return xxx_messageInfo_SetStarTrainGiftReq.Size(m)
}
func (m *SetStarTrainGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrainGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrainGiftReq proto.InternalMessageInfo

func (m *SetStarTrainGiftReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type SetStarTrainGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetStarTrainGiftResp) Reset()         { *m = SetStarTrainGiftResp{} }
func (m *SetStarTrainGiftResp) String() string { return proto.CompactTextString(m) }
func (*SetStarTrainGiftResp) ProtoMessage()    {}
func (*SetStarTrainGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{16}
}
func (m *SetStarTrainGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetStarTrainGiftResp.Unmarshal(m, b)
}
func (m *SetStarTrainGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetStarTrainGiftResp.Marshal(b, m, deterministic)
}
func (dst *SetStarTrainGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetStarTrainGiftResp.Merge(dst, src)
}
func (m *SetStarTrainGiftResp) XXX_Size() int {
	return xxx_messageInfo_SetStarTrainGiftResp.Size(m)
}
func (m *SetStarTrainGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetStarTrainGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetStarTrainGiftResp proto.InternalMessageInfo

// 全量接口
type GetAllStarTrainGiftReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllStarTrainGiftReq) Reset()         { *m = GetAllStarTrainGiftReq{} }
func (m *GetAllStarTrainGiftReq) String() string { return proto.CompactTextString(m) }
func (*GetAllStarTrainGiftReq) ProtoMessage()    {}
func (*GetAllStarTrainGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{17}
}
func (m *GetAllStarTrainGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllStarTrainGiftReq.Unmarshal(m, b)
}
func (m *GetAllStarTrainGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllStarTrainGiftReq.Marshal(b, m, deterministic)
}
func (dst *GetAllStarTrainGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStarTrainGiftReq.Merge(dst, src)
}
func (m *GetAllStarTrainGiftReq) XXX_Size() int {
	return xxx_messageInfo_GetAllStarTrainGiftReq.Size(m)
}
func (m *GetAllStarTrainGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStarTrainGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStarTrainGiftReq proto.InternalMessageInfo

type TicketGiftInfo struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftPrice            uint32   `protobuf:"varint,2,opt,name=gift_price,json=giftPrice,proto3" json:"gift_price,omitempty"`
	GiftName             string   `protobuf:"bytes,3,opt,name=gift_name,json=giftName,proto3" json:"gift_name,omitempty"`
	GiftPic              string   `protobuf:"bytes,4,opt,name=gift_pic,json=giftPic,proto3" json:"gift_pic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TicketGiftInfo) Reset()         { *m = TicketGiftInfo{} }
func (m *TicketGiftInfo) String() string { return proto.CompactTextString(m) }
func (*TicketGiftInfo) ProtoMessage()    {}
func (*TicketGiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{18}
}
func (m *TicketGiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketGiftInfo.Unmarshal(m, b)
}
func (m *TicketGiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketGiftInfo.Marshal(b, m, deterministic)
}
func (dst *TicketGiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketGiftInfo.Merge(dst, src)
}
func (m *TicketGiftInfo) XXX_Size() int {
	return xxx_messageInfo_TicketGiftInfo.Size(m)
}
func (m *TicketGiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketGiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketGiftInfo proto.InternalMessageInfo

func (m *TicketGiftInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *TicketGiftInfo) GetGiftPrice() uint32 {
	if m != nil {
		return m.GiftPrice
	}
	return 0
}

func (m *TicketGiftInfo) GetGiftName() string {
	if m != nil {
		return m.GiftName
	}
	return ""
}

func (m *TicketGiftInfo) GetGiftPic() string {
	if m != nil {
		return m.GiftPic
	}
	return ""
}

type GetAllStarTrainGiftResp struct {
	GiftList             []*TicketGiftInfo `protobuf:"bytes,1,rep,name=gift_list,json=giftList,proto3" json:"gift_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllStarTrainGiftResp) Reset()         { *m = GetAllStarTrainGiftResp{} }
func (m *GetAllStarTrainGiftResp) String() string { return proto.CompactTextString(m) }
func (*GetAllStarTrainGiftResp) ProtoMessage()    {}
func (*GetAllStarTrainGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{19}
}
func (m *GetAllStarTrainGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllStarTrainGiftResp.Unmarshal(m, b)
}
func (m *GetAllStarTrainGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllStarTrainGiftResp.Marshal(b, m, deterministic)
}
func (dst *GetAllStarTrainGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllStarTrainGiftResp.Merge(dst, src)
}
func (m *GetAllStarTrainGiftResp) XXX_Size() int {
	return xxx_messageInfo_GetAllStarTrainGiftResp.Size(m)
}
func (m *GetAllStarTrainGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllStarTrainGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllStarTrainGiftResp proto.InternalMessageInfo

func (m *GetAllStarTrainGiftResp) GetGiftList() []*TicketGiftInfo {
	if m != nil {
		return m.GiftList
	}
	return nil
}

type DelStarTrainGiftReq struct {
	GiftId               uint32   `protobuf:"varint,1,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrainGiftReq) Reset()         { *m = DelStarTrainGiftReq{} }
func (m *DelStarTrainGiftReq) String() string { return proto.CompactTextString(m) }
func (*DelStarTrainGiftReq) ProtoMessage()    {}
func (*DelStarTrainGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{20}
}
func (m *DelStarTrainGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrainGiftReq.Unmarshal(m, b)
}
func (m *DelStarTrainGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrainGiftReq.Marshal(b, m, deterministic)
}
func (dst *DelStarTrainGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrainGiftReq.Merge(dst, src)
}
func (m *DelStarTrainGiftReq) XXX_Size() int {
	return xxx_messageInfo_DelStarTrainGiftReq.Size(m)
}
func (m *DelStarTrainGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrainGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrainGiftReq proto.InternalMessageInfo

func (m *DelStarTrainGiftReq) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

type DelStarTrainGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelStarTrainGiftResp) Reset()         { *m = DelStarTrainGiftResp{} }
func (m *DelStarTrainGiftResp) String() string { return proto.CompactTextString(m) }
func (*DelStarTrainGiftResp) ProtoMessage()    {}
func (*DelStarTrainGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{21}
}
func (m *DelStarTrainGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelStarTrainGiftResp.Unmarshal(m, b)
}
func (m *DelStarTrainGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelStarTrainGiftResp.Marshal(b, m, deterministic)
}
func (dst *DelStarTrainGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelStarTrainGiftResp.Merge(dst, src)
}
func (m *DelStarTrainGiftResp) XXX_Size() int {
	return xxx_messageInfo_DelStarTrainGiftResp.Size(m)
}
func (m *DelStarTrainGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelStarTrainGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelStarTrainGiftResp proto.InternalMessageInfo

// 列车进程信息
type StarTrainProgress struct {
	ActId                uint32              `protobuf:"varint,1,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	RoundId              uint32              `protobuf:"varint,2,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	TotalDistance        uint32              `protobuf:"varint,3,opt,name=total_distance,json=totalDistance,proto3" json:"total_distance,omitempty"`
	Stations             []*StarTrainStation `protobuf:"bytes,4,rep,name=stations,proto3" json:"stations,omitempty"`
	SeatUidList          []uint32            `protobuf:"varint,5,rep,packed,name=seat_uid_list,json=seatUidList,proto3" json:"seat_uid_list,omitempty"`
	CurrentDistance      uint32              `protobuf:"varint,6,opt,name=current_distance,json=currentDistance,proto3" json:"current_distance,omitempty"`
	CurrentSpeed         uint32              `protobuf:"varint,7,opt,name=current_speed,json=currentSpeed,proto3" json:"current_speed,omitempty"`
	LastRoundBingo       *StarTrainBingo     `protobuf:"bytes,8,opt,name=last_round_bingo,json=lastRoundBingo,proto3" json:"last_round_bingo,omitempty"`
	TodayTrainIdx        uint32              `protobuf:"varint,9,opt,name=today_train_idx,json=todayTrainIdx,proto3" json:"today_train_idx,omitempty"`
	StarAward            *StarTrainAward     `protobuf:"bytes,10,opt,name=star_award,json=starAward,proto3" json:"star_award,omitempty"`
	ServerTs             int64               `protobuf:"varint,11,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *StarTrainProgress) Reset()         { *m = StarTrainProgress{} }
func (m *StarTrainProgress) String() string { return proto.CompactTextString(m) }
func (*StarTrainProgress) ProtoMessage()    {}
func (*StarTrainProgress) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{22}
}
func (m *StarTrainProgress) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainProgress.Unmarshal(m, b)
}
func (m *StarTrainProgress) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainProgress.Marshal(b, m, deterministic)
}
func (dst *StarTrainProgress) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainProgress.Merge(dst, src)
}
func (m *StarTrainProgress) XXX_Size() int {
	return xxx_messageInfo_StarTrainProgress.Size(m)
}
func (m *StarTrainProgress) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainProgress.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainProgress proto.InternalMessageInfo

func (m *StarTrainProgress) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *StarTrainProgress) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *StarTrainProgress) GetTotalDistance() uint32 {
	if m != nil {
		return m.TotalDistance
	}
	return 0
}

func (m *StarTrainProgress) GetStations() []*StarTrainStation {
	if m != nil {
		return m.Stations
	}
	return nil
}

func (m *StarTrainProgress) GetSeatUidList() []uint32 {
	if m != nil {
		return m.SeatUidList
	}
	return nil
}

func (m *StarTrainProgress) GetCurrentDistance() uint32 {
	if m != nil {
		return m.CurrentDistance
	}
	return 0
}

func (m *StarTrainProgress) GetCurrentSpeed() uint32 {
	if m != nil {
		return m.CurrentSpeed
	}
	return 0
}

func (m *StarTrainProgress) GetLastRoundBingo() *StarTrainBingo {
	if m != nil {
		return m.LastRoundBingo
	}
	return nil
}

func (m *StarTrainProgress) GetTodayTrainIdx() uint32 {
	if m != nil {
		return m.TodayTrainIdx
	}
	return 0
}

func (m *StarTrainProgress) GetStarAward() *StarTrainAward {
	if m != nil {
		return m.StarAward
	}
	return nil
}

func (m *StarTrainProgress) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

// 中奖信息
type StarTrainBingo struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Award                *StarTrainAward `protobuf:"bytes,2,opt,name=award,proto3" json:"award,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StarTrainBingo) Reset()         { *m = StarTrainBingo{} }
func (m *StarTrainBingo) String() string { return proto.CompactTextString(m) }
func (*StarTrainBingo) ProtoMessage()    {}
func (*StarTrainBingo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{23}
}
func (m *StarTrainBingo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainBingo.Unmarshal(m, b)
}
func (m *StarTrainBingo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainBingo.Marshal(b, m, deterministic)
}
func (dst *StarTrainBingo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainBingo.Merge(dst, src)
}
func (m *StarTrainBingo) XXX_Size() int {
	return xxx_messageInfo_StarTrainBingo.Size(m)
}
func (m *StarTrainBingo) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainBingo.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainBingo proto.InternalMessageInfo

func (m *StarTrainBingo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StarTrainBingo) GetAward() *StarTrainAward {
	if m != nil {
		return m.Award
	}
	return nil
}

// 站点信息
type StarTrainStation struct {
	Position             uint32          `protobuf:"varint,1,opt,name=position,proto3" json:"position,omitempty"`
	AwardInfo            *StarTrainAward `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	AwardUid             uint32          `protobuf:"varint,3,opt,name=award_uid,json=awardUid,proto3" json:"award_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StarTrainStation) Reset()         { *m = StarTrainStation{} }
func (m *StarTrainStation) String() string { return proto.CompactTextString(m) }
func (*StarTrainStation) ProtoMessage()    {}
func (*StarTrainStation) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{24}
}
func (m *StarTrainStation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainStation.Unmarshal(m, b)
}
func (m *StarTrainStation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainStation.Marshal(b, m, deterministic)
}
func (dst *StarTrainStation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainStation.Merge(dst, src)
}
func (m *StarTrainStation) XXX_Size() int {
	return xxx_messageInfo_StarTrainStation.Size(m)
}
func (m *StarTrainStation) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainStation.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainStation proto.InternalMessageInfo

func (m *StarTrainStation) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *StarTrainStation) GetAwardInfo() *StarTrainAward {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *StarTrainStation) GetAwardUid() uint32 {
	if m != nil {
		return m.AwardUid
	}
	return 0
}

// 奖品信息
type StarTrainAward struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	Amount               uint32   `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	GiftType             uint32   `protobuf:"varint,6,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTrainAward) Reset()         { *m = StarTrainAward{} }
func (m *StarTrainAward) String() string { return proto.CompactTextString(m) }
func (*StarTrainAward) ProtoMessage()    {}
func (*StarTrainAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{25}
}
func (m *StarTrainAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainAward.Unmarshal(m, b)
}
func (m *StarTrainAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainAward.Marshal(b, m, deterministic)
}
func (dst *StarTrainAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainAward.Merge(dst, src)
}
func (m *StarTrainAward) XXX_Size() int {
	return xxx_messageInfo_StarTrainAward.Size(m)
}
func (m *StarTrainAward) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainAward.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainAward proto.InternalMessageInfo

func (m *StarTrainAward) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StarTrainAward) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StarTrainAward) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *StarTrainAward) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *StarTrainAward) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *StarTrainAward) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

// 用户参与信息
type StarTrainUserJoin struct {
	ActId                 uint32   `protobuf:"varint,1,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	RoundId               uint32   `protobuf:"varint,2,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	JoinDistance          uint32   `protobuf:"varint,3,opt,name=join_distance,json=joinDistance,proto3" json:"join_distance,omitempty"`
	LastJoinFinalDistance uint32   `protobuf:"varint,4,opt,name=last_join_final_distance,json=lastJoinFinalDistance,proto3" json:"last_join_final_distance,omitempty"`
	SeatAmount            uint32   `protobuf:"varint,5,opt,name=seat_amount,json=seatAmount,proto3" json:"seat_amount,omitempty"`
	ServerTs              int64    `protobuf:"varint,6,opt,name=server_ts,json=serverTs,proto3" json:"server_ts,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *StarTrainUserJoin) Reset()         { *m = StarTrainUserJoin{} }
func (m *StarTrainUserJoin) String() string { return proto.CompactTextString(m) }
func (*StarTrainUserJoin) ProtoMessage()    {}
func (*StarTrainUserJoin) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{26}
}
func (m *StarTrainUserJoin) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainUserJoin.Unmarshal(m, b)
}
func (m *StarTrainUserJoin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainUserJoin.Marshal(b, m, deterministic)
}
func (dst *StarTrainUserJoin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainUserJoin.Merge(dst, src)
}
func (m *StarTrainUserJoin) XXX_Size() int {
	return xxx_messageInfo_StarTrainUserJoin.Size(m)
}
func (m *StarTrainUserJoin) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainUserJoin.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainUserJoin proto.InternalMessageInfo

func (m *StarTrainUserJoin) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *StarTrainUserJoin) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *StarTrainUserJoin) GetJoinDistance() uint32 {
	if m != nil {
		return m.JoinDistance
	}
	return 0
}

func (m *StarTrainUserJoin) GetLastJoinFinalDistance() uint32 {
	if m != nil {
		return m.LastJoinFinalDistance
	}
	return 0
}

func (m *StarTrainUserJoin) GetSeatAmount() uint32 {
	if m != nil {
		return m.SeatAmount
	}
	return 0
}

func (m *StarTrainUserJoin) GetServerTs() int64 {
	if m != nil {
		return m.ServerTs
	}
	return 0
}

// 获取列车信息
type GetStarTrainInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainInfoReq) Reset()         { *m = GetStarTrainInfoReq{} }
func (m *GetStarTrainInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainInfoReq) ProtoMessage()    {}
func (*GetStarTrainInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{27}
}
func (m *GetStarTrainInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainInfoReq.Unmarshal(m, b)
}
func (m *GetStarTrainInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainInfoReq.Merge(dst, src)
}
func (m *GetStarTrainInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainInfoReq.Size(m)
}
func (m *GetStarTrainInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainInfoReq proto.InternalMessageInfo

func (m *GetStarTrainInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStarTrainInfoResp struct {
	Progress             *StarTrainProgress `protobuf:"bytes,1,opt,name=progress,proto3" json:"progress,omitempty"`
	MyJoinInfo           *StarTrainUserJoin `protobuf:"bytes,2,opt,name=my_join_info,json=myJoinInfo,proto3" json:"my_join_info,omitempty"`
	ActEndTime           int64              `protobuf:"varint,3,opt,name=act_end_time,json=actEndTime,proto3" json:"act_end_time,omitempty"`
	NextRoundBegin       int64              `protobuf:"varint,4,opt,name=next_round_begin,json=nextRoundBegin,proto3" json:"next_round_begin,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetStarTrainInfoResp) Reset()         { *m = GetStarTrainInfoResp{} }
func (m *GetStarTrainInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainInfoResp) ProtoMessage()    {}
func (*GetStarTrainInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{28}
}
func (m *GetStarTrainInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainInfoResp.Unmarshal(m, b)
}
func (m *GetStarTrainInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainInfoResp.Merge(dst, src)
}
func (m *GetStarTrainInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainInfoResp.Size(m)
}
func (m *GetStarTrainInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainInfoResp proto.InternalMessageInfo

func (m *GetStarTrainInfoResp) GetProgress() *StarTrainProgress {
	if m != nil {
		return m.Progress
	}
	return nil
}

func (m *GetStarTrainInfoResp) GetMyJoinInfo() *StarTrainUserJoin {
	if m != nil {
		return m.MyJoinInfo
	}
	return nil
}

func (m *GetStarTrainInfoResp) GetActEndTime() int64 {
	if m != nil {
		return m.ActEndTime
	}
	return 0
}

func (m *GetStarTrainInfoResp) GetNextRoundBegin() int64 {
	if m != nil {
		return m.NextRoundBegin
	}
	return 0
}

// 获取列车进程信息
type GetStarTrainProgressReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainProgressReq) Reset()         { *m = GetStarTrainProgressReq{} }
func (m *GetStarTrainProgressReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainProgressReq) ProtoMessage()    {}
func (*GetStarTrainProgressReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{29}
}
func (m *GetStarTrainProgressReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainProgressReq.Unmarshal(m, b)
}
func (m *GetStarTrainProgressReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainProgressReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainProgressReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainProgressReq.Merge(dst, src)
}
func (m *GetStarTrainProgressReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainProgressReq.Size(m)
}
func (m *GetStarTrainProgressReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainProgressReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainProgressReq proto.InternalMessageInfo

func (m *GetStarTrainProgressReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStarTrainProgressResp struct {
	Progress             *StarTrainProgress `protobuf:"bytes,1,opt,name=progress,proto3" json:"progress,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetStarTrainProgressResp) Reset()         { *m = GetStarTrainProgressResp{} }
func (m *GetStarTrainProgressResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainProgressResp) ProtoMessage()    {}
func (*GetStarTrainProgressResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{30}
}
func (m *GetStarTrainProgressResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainProgressResp.Unmarshal(m, b)
}
func (m *GetStarTrainProgressResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainProgressResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainProgressResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainProgressResp.Merge(dst, src)
}
func (m *GetStarTrainProgressResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainProgressResp.Size(m)
}
func (m *GetStarTrainProgressResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainProgressResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainProgressResp proto.InternalMessageInfo

func (m *GetStarTrainProgressResp) GetProgress() *StarTrainProgress {
	if m != nil {
		return m.Progress
	}
	return nil
}

type StarTrainBroadcast struct {
	BroadcastType        uint32          `protobuf:"varint,1,opt,name=broadcast_type,json=broadcastType,proto3" json:"broadcast_type,omitempty"`
	Uid                  uint32          `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Award                *StarTrainAward `protobuf:"bytes,3,opt,name=award,proto3" json:"award,omitempty"`
	IncrDistance         uint32          `protobuf:"varint,4,opt,name=incr_distance,json=incrDistance,proto3" json:"incr_distance,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StarTrainBroadcast) Reset()         { *m = StarTrainBroadcast{} }
func (m *StarTrainBroadcast) String() string { return proto.CompactTextString(m) }
func (*StarTrainBroadcast) ProtoMessage()    {}
func (*StarTrainBroadcast) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{31}
}
func (m *StarTrainBroadcast) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainBroadcast.Unmarshal(m, b)
}
func (m *StarTrainBroadcast) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainBroadcast.Marshal(b, m, deterministic)
}
func (dst *StarTrainBroadcast) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainBroadcast.Merge(dst, src)
}
func (m *StarTrainBroadcast) XXX_Size() int {
	return xxx_messageInfo_StarTrainBroadcast.Size(m)
}
func (m *StarTrainBroadcast) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainBroadcast.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainBroadcast proto.InternalMessageInfo

func (m *StarTrainBroadcast) GetBroadcastType() uint32 {
	if m != nil {
		return m.BroadcastType
	}
	return 0
}

func (m *StarTrainBroadcast) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StarTrainBroadcast) GetAward() *StarTrainAward {
	if m != nil {
		return m.Award
	}
	return nil
}

func (m *StarTrainBroadcast) GetIncrDistance() uint32 {
	if m != nil {
		return m.IncrDistance
	}
	return 0
}

// 获取列车播报数据
type GetStarTrainBroadcastReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainBroadcastReq) Reset()         { *m = GetStarTrainBroadcastReq{} }
func (m *GetStarTrainBroadcastReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainBroadcastReq) ProtoMessage()    {}
func (*GetStarTrainBroadcastReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{32}
}
func (m *GetStarTrainBroadcastReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainBroadcastReq.Unmarshal(m, b)
}
func (m *GetStarTrainBroadcastReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainBroadcastReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainBroadcastReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainBroadcastReq.Merge(dst, src)
}
func (m *GetStarTrainBroadcastReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainBroadcastReq.Size(m)
}
func (m *GetStarTrainBroadcastReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainBroadcastReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainBroadcastReq proto.InternalMessageInfo

func (m *GetStarTrainBroadcastReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStarTrainBroadcastResp struct {
	BroadcastList        []*StarTrainBroadcast `protobuf:"bytes,1,rep,name=broadcast_list,json=broadcastList,proto3" json:"broadcast_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetStarTrainBroadcastResp) Reset()         { *m = GetStarTrainBroadcastResp{} }
func (m *GetStarTrainBroadcastResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainBroadcastResp) ProtoMessage()    {}
func (*GetStarTrainBroadcastResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{33}
}
func (m *GetStarTrainBroadcastResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainBroadcastResp.Unmarshal(m, b)
}
func (m *GetStarTrainBroadcastResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainBroadcastResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainBroadcastResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainBroadcastResp.Merge(dst, src)
}
func (m *GetStarTrainBroadcastResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainBroadcastResp.Size(m)
}
func (m *GetStarTrainBroadcastResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainBroadcastResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainBroadcastResp proto.InternalMessageInfo

func (m *GetStarTrainBroadcastResp) GetBroadcastList() []*StarTrainBroadcast {
	if m != nil {
		return m.BroadcastList
	}
	return nil
}

type StarTrainSeat struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SeatId               uint32   `protobuf:"varint,2,opt,name=seat_id,json=seatId,proto3" json:"seat_id,omitempty"`
	SeatAmount           uint32   `protobuf:"varint,3,opt,name=seat_amount,json=seatAmount,proto3" json:"seat_amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarTrainSeat) Reset()         { *m = StarTrainSeat{} }
func (m *StarTrainSeat) String() string { return proto.CompactTextString(m) }
func (*StarTrainSeat) ProtoMessage()    {}
func (*StarTrainSeat) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{34}
}
func (m *StarTrainSeat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainSeat.Unmarshal(m, b)
}
func (m *StarTrainSeat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainSeat.Marshal(b, m, deterministic)
}
func (dst *StarTrainSeat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainSeat.Merge(dst, src)
}
func (m *StarTrainSeat) XXX_Size() int {
	return xxx_messageInfo_StarTrainSeat.Size(m)
}
func (m *StarTrainSeat) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainSeat.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainSeat proto.InternalMessageInfo

func (m *StarTrainSeat) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StarTrainSeat) GetSeatId() uint32 {
	if m != nil {
		return m.SeatId
	}
	return 0
}

func (m *StarTrainSeat) GetSeatAmount() uint32 {
	if m != nil {
		return m.SeatAmount
	}
	return 0
}

// 获取列车座次表
type GetStarTrainSeatListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RoundId              uint32   `protobuf:"varint,2,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainSeatListReq) Reset()         { *m = GetStarTrainSeatListReq{} }
func (m *GetStarTrainSeatListReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainSeatListReq) ProtoMessage()    {}
func (*GetStarTrainSeatListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{35}
}
func (m *GetStarTrainSeatListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainSeatListReq.Unmarshal(m, b)
}
func (m *GetStarTrainSeatListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainSeatListReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainSeatListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainSeatListReq.Merge(dst, src)
}
func (m *GetStarTrainSeatListReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainSeatListReq.Size(m)
}
func (m *GetStarTrainSeatListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainSeatListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainSeatListReq proto.InternalMessageInfo

func (m *GetStarTrainSeatListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStarTrainSeatListReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type GetStarTrainSeatListResp struct {
	AwardList            []*StarTrainAward `protobuf:"bytes,1,rep,name=award_list,json=awardList,proto3" json:"award_list,omitempty"`
	SeatList             []*StarTrainSeat  `protobuf:"bytes,2,rep,name=seat_list,json=seatList,proto3" json:"seat_list,omitempty"`
	MySeat               *StarTrainSeat    `protobuf:"bytes,3,opt,name=my_seat,json=mySeat,proto3" json:"my_seat,omitempty"`
	HonorAwardDesc       string            `protobuf:"bytes,4,opt,name=honor_award_desc,json=honorAwardDesc,proto3" json:"honor_award_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetStarTrainSeatListResp) Reset()         { *m = GetStarTrainSeatListResp{} }
func (m *GetStarTrainSeatListResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainSeatListResp) ProtoMessage()    {}
func (*GetStarTrainSeatListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{36}
}
func (m *GetStarTrainSeatListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainSeatListResp.Unmarshal(m, b)
}
func (m *GetStarTrainSeatListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainSeatListResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainSeatListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainSeatListResp.Merge(dst, src)
}
func (m *GetStarTrainSeatListResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainSeatListResp.Size(m)
}
func (m *GetStarTrainSeatListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainSeatListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainSeatListResp proto.InternalMessageInfo

func (m *GetStarTrainSeatListResp) GetAwardList() []*StarTrainAward {
	if m != nil {
		return m.AwardList
	}
	return nil
}

func (m *GetStarTrainSeatListResp) GetSeatList() []*StarTrainSeat {
	if m != nil {
		return m.SeatList
	}
	return nil
}

func (m *GetStarTrainSeatListResp) GetMySeat() *StarTrainSeat {
	if m != nil {
		return m.MySeat
	}
	return nil
}

func (m *GetStarTrainSeatListResp) GetHonorAwardDesc() string {
	if m != nil {
		return m.HonorAwardDesc
	}
	return ""
}

type GetSimpleActivityInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSimpleActivityInfoReq) Reset()         { *m = GetSimpleActivityInfoReq{} }
func (m *GetSimpleActivityInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSimpleActivityInfoReq) ProtoMessage()    {}
func (*GetSimpleActivityInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{37}
}
func (m *GetSimpleActivityInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimpleActivityInfoReq.Unmarshal(m, b)
}
func (m *GetSimpleActivityInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimpleActivityInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSimpleActivityInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimpleActivityInfoReq.Merge(dst, src)
}
func (m *GetSimpleActivityInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSimpleActivityInfoReq.Size(m)
}
func (m *GetSimpleActivityInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimpleActivityInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimpleActivityInfoReq proto.InternalMessageInfo

type GetSimpleActivityInfoResp struct {
	ActId                  uint32   `protobuf:"varint,1,opt,name=act_id,json=actId,proto3" json:"act_id,omitempty"`
	ActBeginTime           int64    `protobuf:"varint,2,opt,name=act_begin_time,json=actBeginTime,proto3" json:"act_begin_time,omitempty"`
	ActEndTime             int64    `protobuf:"varint,3,opt,name=act_end_time,json=actEndTime,proto3" json:"act_end_time,omitempty"`
	GiftIdList             []uint32 `protobuf:"varint,4,rep,packed,name=gift_id_list,json=giftIdList,proto3" json:"gift_id_list,omitempty"`
	SeatUnitPrice          uint32   `protobuf:"varint,5,opt,name=seat_unit_price,json=seatUnitPrice,proto3" json:"seat_unit_price,omitempty"`
	ActGiftDesc            string   `protobuf:"bytes,6,opt,name=act_gift_desc,json=actGiftDesc,proto3" json:"act_gift_desc,omitempty"`
	StrategyRuleImg        string   `protobuf:"bytes,7,opt,name=strategy_rule_img,json=strategyRuleImg,proto3" json:"strategy_rule_img,omitempty"`
	NotReachedStopDesc     string   `protobuf:"bytes,8,opt,name=not_reached_stop_desc,json=notReachedStopDesc,proto3" json:"not_reached_stop_desc,omitempty"`
	AlreadyReachedStopDesc string   `protobuf:"bytes,9,opt,name=already_reached_stop_desc,json=alreadyReachedStopDesc,proto3" json:"already_reached_stop_desc,omitempty"`
	LastStarAwardUserDesc  string   `protobuf:"bytes,10,opt,name=last_star_award_user_desc,json=lastStarAwardUserDesc,proto3" json:"last_star_award_user_desc,omitempty"`
	StarAwardStopDesc      string   `protobuf:"bytes,11,opt,name=star_award_stop_desc,json=starAwardStopDesc,proto3" json:"star_award_stop_desc,omitempty"`
	StarAwardStopJoinDesc  string   `protobuf:"bytes,12,opt,name=star_award_stop_join_desc,json=starAwardStopJoinDesc,proto3" json:"star_award_stop_join_desc,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GetSimpleActivityInfoResp) Reset()         { *m = GetSimpleActivityInfoResp{} }
func (m *GetSimpleActivityInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSimpleActivityInfoResp) ProtoMessage()    {}
func (*GetSimpleActivityInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{38}
}
func (m *GetSimpleActivityInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSimpleActivityInfoResp.Unmarshal(m, b)
}
func (m *GetSimpleActivityInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSimpleActivityInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSimpleActivityInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSimpleActivityInfoResp.Merge(dst, src)
}
func (m *GetSimpleActivityInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSimpleActivityInfoResp.Size(m)
}
func (m *GetSimpleActivityInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSimpleActivityInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSimpleActivityInfoResp proto.InternalMessageInfo

func (m *GetSimpleActivityInfoResp) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *GetSimpleActivityInfoResp) GetActBeginTime() int64 {
	if m != nil {
		return m.ActBeginTime
	}
	return 0
}

func (m *GetSimpleActivityInfoResp) GetActEndTime() int64 {
	if m != nil {
		return m.ActEndTime
	}
	return 0
}

func (m *GetSimpleActivityInfoResp) GetGiftIdList() []uint32 {
	if m != nil {
		return m.GiftIdList
	}
	return nil
}

func (m *GetSimpleActivityInfoResp) GetSeatUnitPrice() uint32 {
	if m != nil {
		return m.SeatUnitPrice
	}
	return 0
}

func (m *GetSimpleActivityInfoResp) GetActGiftDesc() string {
	if m != nil {
		return m.ActGiftDesc
	}
	return ""
}

func (m *GetSimpleActivityInfoResp) GetStrategyRuleImg() string {
	if m != nil {
		return m.StrategyRuleImg
	}
	return ""
}

func (m *GetSimpleActivityInfoResp) GetNotReachedStopDesc() string {
	if m != nil {
		return m.NotReachedStopDesc
	}
	return ""
}

func (m *GetSimpleActivityInfoResp) GetAlreadyReachedStopDesc() string {
	if m != nil {
		return m.AlreadyReachedStopDesc
	}
	return ""
}

func (m *GetSimpleActivityInfoResp) GetLastStarAwardUserDesc() string {
	if m != nil {
		return m.LastStarAwardUserDesc
	}
	return ""
}

func (m *GetSimpleActivityInfoResp) GetStarAwardStopDesc() string {
	if m != nil {
		return m.StarAwardStopDesc
	}
	return ""
}

func (m *GetSimpleActivityInfoResp) GetStarAwardStopJoinDesc() string {
	if m != nil {
		return m.StarAwardStopJoinDesc
	}
	return ""
}

type StarTrainUserRecord struct {
	Id                   uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Award                *StarTrainAward `protobuf:"bytes,2,opt,name=award,proto3" json:"award,omitempty"`
	GiftType             uint32          `protobuf:"varint,3,opt,name=gift_type,json=giftType,proto3" json:"gift_type,omitempty"`
	PrizeType            uint32          `protobuf:"varint,4,opt,name=prize_type,json=prizeType,proto3" json:"prize_type,omitempty"`
	AwardTs              int64           `protobuf:"varint,5,opt,name=award_ts,json=awardTs,proto3" json:"award_ts,omitempty"`
	PriceType            uint32          `protobuf:"varint,6,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StarTrainUserRecord) Reset()         { *m = StarTrainUserRecord{} }
func (m *StarTrainUserRecord) String() string { return proto.CompactTextString(m) }
func (*StarTrainUserRecord) ProtoMessage()    {}
func (*StarTrainUserRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{39}
}
func (m *StarTrainUserRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarTrainUserRecord.Unmarshal(m, b)
}
func (m *StarTrainUserRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarTrainUserRecord.Marshal(b, m, deterministic)
}
func (dst *StarTrainUserRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarTrainUserRecord.Merge(dst, src)
}
func (m *StarTrainUserRecord) XXX_Size() int {
	return xxx_messageInfo_StarTrainUserRecord.Size(m)
}
func (m *StarTrainUserRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_StarTrainUserRecord.DiscardUnknown(m)
}

var xxx_messageInfo_StarTrainUserRecord proto.InternalMessageInfo

func (m *StarTrainUserRecord) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *StarTrainUserRecord) GetAward() *StarTrainAward {
	if m != nil {
		return m.Award
	}
	return nil
}

func (m *StarTrainUserRecord) GetGiftType() uint32 {
	if m != nil {
		return m.GiftType
	}
	return 0
}

func (m *StarTrainUserRecord) GetPrizeType() uint32 {
	if m != nil {
		return m.PrizeType
	}
	return 0
}

func (m *StarTrainUserRecord) GetAwardTs() int64 {
	if m != nil {
		return m.AwardTs
	}
	return 0
}

func (m *StarTrainUserRecord) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

// 获取用户记录
type GetStarTrainUserRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Offset               string   `protobuf:"bytes,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarTrainUserRecordReq) Reset()         { *m = GetStarTrainUserRecordReq{} }
func (m *GetStarTrainUserRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainUserRecordReq) ProtoMessage()    {}
func (*GetStarTrainUserRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{40}
}
func (m *GetStarTrainUserRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainUserRecordReq.Unmarshal(m, b)
}
func (m *GetStarTrainUserRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainUserRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainUserRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainUserRecordReq.Merge(dst, src)
}
func (m *GetStarTrainUserRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainUserRecordReq.Size(m)
}
func (m *GetStarTrainUserRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainUserRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainUserRecordReq proto.InternalMessageInfo

func (m *GetStarTrainUserRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStarTrainUserRecordReq) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

func (m *GetStarTrainUserRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetStarTrainUserRecordResp struct {
	RecordList           []*StarTrainUserRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	NextOffset           string                 `protobuf:"bytes,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetStarTrainUserRecordResp) Reset()         { *m = GetStarTrainUserRecordResp{} }
func (m *GetStarTrainUserRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetStarTrainUserRecordResp) ProtoMessage()    {}
func (*GetStarTrainUserRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{41}
}
func (m *GetStarTrainUserRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarTrainUserRecordResp.Unmarshal(m, b)
}
func (m *GetStarTrainUserRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarTrainUserRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetStarTrainUserRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarTrainUserRecordResp.Merge(dst, src)
}
func (m *GetStarTrainUserRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetStarTrainUserRecordResp.Size(m)
}
func (m *GetStarTrainUserRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarTrainUserRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarTrainUserRecordResp proto.InternalMessageInfo

func (m *GetStarTrainUserRecordResp) GetRecordList() []*StarTrainUserRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetStarTrainUserRecordResp) GetNextOffset() string {
	if m != nil {
		return m.NextOffset
	}
	return ""
}

type TrainDepartureRecord struct {
	RoundId              string                              `protobuf:"bytes,1,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	GoalStation          string                              `protobuf:"bytes,2,opt,name=goal_station,json=goalStation,proto3" json:"goal_station,omitempty"`
	EndTime              int64                               `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	UserList             []*TrainDepartureRecord_TopUserInfo `protobuf:"bytes,4,rep,name=user_list,json=userList,proto3" json:"user_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *TrainDepartureRecord) Reset()         { *m = TrainDepartureRecord{} }
func (m *TrainDepartureRecord) String() string { return proto.CompactTextString(m) }
func (*TrainDepartureRecord) ProtoMessage()    {}
func (*TrainDepartureRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{42}
}
func (m *TrainDepartureRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrainDepartureRecord.Unmarshal(m, b)
}
func (m *TrainDepartureRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrainDepartureRecord.Marshal(b, m, deterministic)
}
func (dst *TrainDepartureRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrainDepartureRecord.Merge(dst, src)
}
func (m *TrainDepartureRecord) XXX_Size() int {
	return xxx_messageInfo_TrainDepartureRecord.Size(m)
}
func (m *TrainDepartureRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_TrainDepartureRecord.DiscardUnknown(m)
}

var xxx_messageInfo_TrainDepartureRecord proto.InternalMessageInfo

func (m *TrainDepartureRecord) GetRoundId() string {
	if m != nil {
		return m.RoundId
	}
	return ""
}

func (m *TrainDepartureRecord) GetGoalStation() string {
	if m != nil {
		return m.GoalStation
	}
	return ""
}

func (m *TrainDepartureRecord) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TrainDepartureRecord) GetUserList() []*TrainDepartureRecord_TopUserInfo {
	if m != nil {
		return m.UserList
	}
	return nil
}

type TrainDepartureRecord_TopUserInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsBingo              bool     `protobuf:"varint,2,opt,name=is_bingo,json=isBingo,proto3" json:"is_bingo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrainDepartureRecord_TopUserInfo) Reset()         { *m = TrainDepartureRecord_TopUserInfo{} }
func (m *TrainDepartureRecord_TopUserInfo) String() string { return proto.CompactTextString(m) }
func (*TrainDepartureRecord_TopUserInfo) ProtoMessage()    {}
func (*TrainDepartureRecord_TopUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{42, 0}
}
func (m *TrainDepartureRecord_TopUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrainDepartureRecord_TopUserInfo.Unmarshal(m, b)
}
func (m *TrainDepartureRecord_TopUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrainDepartureRecord_TopUserInfo.Marshal(b, m, deterministic)
}
func (dst *TrainDepartureRecord_TopUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrainDepartureRecord_TopUserInfo.Merge(dst, src)
}
func (m *TrainDepartureRecord_TopUserInfo) XXX_Size() int {
	return xxx_messageInfo_TrainDepartureRecord_TopUserInfo.Size(m)
}
func (m *TrainDepartureRecord_TopUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TrainDepartureRecord_TopUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TrainDepartureRecord_TopUserInfo proto.InternalMessageInfo

func (m *TrainDepartureRecord_TopUserInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TrainDepartureRecord_TopUserInfo) GetIsBingo() bool {
	if m != nil {
		return m.IsBingo
	}
	return false
}

// 获取发车记录
type GetTrainDepartureRecordReq struct {
	Offset               string   `protobuf:"bytes,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTrainDepartureRecordReq) Reset()         { *m = GetTrainDepartureRecordReq{} }
func (m *GetTrainDepartureRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetTrainDepartureRecordReq) ProtoMessage()    {}
func (*GetTrainDepartureRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{43}
}
func (m *GetTrainDepartureRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrainDepartureRecordReq.Unmarshal(m, b)
}
func (m *GetTrainDepartureRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrainDepartureRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetTrainDepartureRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrainDepartureRecordReq.Merge(dst, src)
}
func (m *GetTrainDepartureRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetTrainDepartureRecordReq.Size(m)
}
func (m *GetTrainDepartureRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrainDepartureRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrainDepartureRecordReq proto.InternalMessageInfo

func (m *GetTrainDepartureRecordReq) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

func (m *GetTrainDepartureRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetTrainDepartureRecordResp struct {
	RecordList []*TrainDepartureRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	NextOffset string                  `protobuf:"bytes,2,opt,name=next_offset,json=nextOffset,proto3" json:"next_offset,omitempty"`
	// 荣耀奖励配置
	HonorPrizeList       []*StarTrainAward `protobuf:"bytes,3,rep,name=honor_prize_list,json=honorPrizeList,proto3" json:"honor_prize_list,omitempty"`
	HonorAwardDesc       string            `protobuf:"bytes,4,opt,name=honor_award_desc,json=honorAwardDesc,proto3" json:"honor_award_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetTrainDepartureRecordResp) Reset()         { *m = GetTrainDepartureRecordResp{} }
func (m *GetTrainDepartureRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetTrainDepartureRecordResp) ProtoMessage()    {}
func (*GetTrainDepartureRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{44}
}
func (m *GetTrainDepartureRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrainDepartureRecordResp.Unmarshal(m, b)
}
func (m *GetTrainDepartureRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrainDepartureRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetTrainDepartureRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrainDepartureRecordResp.Merge(dst, src)
}
func (m *GetTrainDepartureRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetTrainDepartureRecordResp.Size(m)
}
func (m *GetTrainDepartureRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrainDepartureRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrainDepartureRecordResp proto.InternalMessageInfo

func (m *GetTrainDepartureRecordResp) GetRecordList() []*TrainDepartureRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetTrainDepartureRecordResp) GetNextOffset() string {
	if m != nil {
		return m.NextOffset
	}
	return ""
}

func (m *GetTrainDepartureRecordResp) GetHonorPrizeList() []*StarTrainAward {
	if m != nil {
		return m.HonorPrizeList
	}
	return nil
}

func (m *GetTrainDepartureRecordResp) GetHonorAwardDesc() string {
	if m != nil {
		return m.HonorAwardDesc
	}
	return ""
}

type TestUserJoinReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Cid                  uint32   `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`
	PresentId            uint32   `protobuf:"varint,3,opt,name=present_id,json=presentId,proto3" json:"present_id,omitempty"`
	Price                uint32   `protobuf:"varint,4,opt,name=price,proto3" json:"price,omitempty"`
	OrderId              string   `protobuf:"bytes,5,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestUserJoinReq) Reset()         { *m = TestUserJoinReq{} }
func (m *TestUserJoinReq) String() string { return proto.CompactTextString(m) }
func (*TestUserJoinReq) ProtoMessage()    {}
func (*TestUserJoinReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{45}
}
func (m *TestUserJoinReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestUserJoinReq.Unmarshal(m, b)
}
func (m *TestUserJoinReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestUserJoinReq.Marshal(b, m, deterministic)
}
func (dst *TestUserJoinReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestUserJoinReq.Merge(dst, src)
}
func (m *TestUserJoinReq) XXX_Size() int {
	return xxx_messageInfo_TestUserJoinReq.Size(m)
}
func (m *TestUserJoinReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestUserJoinReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestUserJoinReq proto.InternalMessageInfo

func (m *TestUserJoinReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TestUserJoinReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TestUserJoinReq) GetPresentId() uint32 {
	if m != nil {
		return m.PresentId
	}
	return 0
}

func (m *TestUserJoinReq) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *TestUserJoinReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type TestUserJoinResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestUserJoinResp) Reset()         { *m = TestUserJoinResp{} }
func (m *TestUserJoinResp) String() string { return proto.CompactTextString(m) }
func (*TestUserJoinResp) ProtoMessage()    {}
func (*TestUserJoinResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{46}
}
func (m *TestUserJoinResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestUserJoinResp.Unmarshal(m, b)
}
func (m *TestUserJoinResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestUserJoinResp.Marshal(b, m, deterministic)
}
func (dst *TestUserJoinResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestUserJoinResp.Merge(dst, src)
}
func (m *TestUserJoinResp) XXX_Size() int {
	return xxx_messageInfo_TestUserJoinResp.Size(m)
}
func (m *TestUserJoinResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestUserJoinResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestUserJoinResp proto.InternalMessageInfo

type TestActivityBeginNotifyReq struct {
	ChTypeList           []uint32 `protobuf:"varint,1,rep,packed,name=ch_type_list,json=chTypeList,proto3" json:"ch_type_list,omitempty"`
	CidList              []uint32 `protobuf:"varint,2,rep,packed,name=cid_list,json=cidList,proto3" json:"cid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestActivityBeginNotifyReq) Reset()         { *m = TestActivityBeginNotifyReq{} }
func (m *TestActivityBeginNotifyReq) String() string { return proto.CompactTextString(m) }
func (*TestActivityBeginNotifyReq) ProtoMessage()    {}
func (*TestActivityBeginNotifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{47}
}
func (m *TestActivityBeginNotifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestActivityBeginNotifyReq.Unmarshal(m, b)
}
func (m *TestActivityBeginNotifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestActivityBeginNotifyReq.Marshal(b, m, deterministic)
}
func (dst *TestActivityBeginNotifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestActivityBeginNotifyReq.Merge(dst, src)
}
func (m *TestActivityBeginNotifyReq) XXX_Size() int {
	return xxx_messageInfo_TestActivityBeginNotifyReq.Size(m)
}
func (m *TestActivityBeginNotifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TestActivityBeginNotifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_TestActivityBeginNotifyReq proto.InternalMessageInfo

func (m *TestActivityBeginNotifyReq) GetChTypeList() []uint32 {
	if m != nil {
		return m.ChTypeList
	}
	return nil
}

func (m *TestActivityBeginNotifyReq) GetCidList() []uint32 {
	if m != nil {
		return m.CidList
	}
	return nil
}

type TestActivityBeginNotifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TestActivityBeginNotifyResp) Reset()         { *m = TestActivityBeginNotifyResp{} }
func (m *TestActivityBeginNotifyResp) String() string { return proto.CompactTextString(m) }
func (*TestActivityBeginNotifyResp) ProtoMessage()    {}
func (*TestActivityBeginNotifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_star_train_03aa28f1592d8825, []int{48}
}
func (m *TestActivityBeginNotifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TestActivityBeginNotifyResp.Unmarshal(m, b)
}
func (m *TestActivityBeginNotifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TestActivityBeginNotifyResp.Marshal(b, m, deterministic)
}
func (dst *TestActivityBeginNotifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TestActivityBeginNotifyResp.Merge(dst, src)
}
func (m *TestActivityBeginNotifyResp) XXX_Size() int {
	return xxx_messageInfo_TestActivityBeginNotifyResp.Size(m)
}
func (m *TestActivityBeginNotifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TestActivityBeginNotifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_TestActivityBeginNotifyResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*DayTimeRangeInfo)(nil), "star_train.DayTimeRangeInfo")
	proto.RegisterType((*StarTrainBgInfo)(nil), "star_train.StarTrainBgInfo")
	proto.RegisterType((*StationInfo)(nil), "star_train.StationInfo")
	proto.RegisterType((*TrainRouteInfo)(nil), "star_train.TrainRouteInfo")
	proto.RegisterType((*StarTrainConf)(nil), "star_train.StarTrainConf")
	proto.RegisterType((*SetStarTrainConfReq)(nil), "star_train.SetStarTrainConfReq")
	proto.RegisterType((*SetStarTrainConfResp)(nil), "star_train.SetStarTrainConfResp")
	proto.RegisterType((*GetStarTrainConfReq)(nil), "star_train.GetStarTrainConfReq")
	proto.RegisterType((*GetStarTrainConfResp)(nil), "star_train.GetStarTrainConfResp")
	proto.RegisterType((*UpdateStarTrainConfReq)(nil), "star_train.UpdateStarTrainConfReq")
	proto.RegisterType((*UpdateStarTrainConfResp)(nil), "star_train.UpdateStarTrainConfResp")
	proto.RegisterType((*DelStarTrainConfReq)(nil), "star_train.DelStarTrainConfReq")
	proto.RegisterType((*DelStarTrainConfResp)(nil), "star_train.DelStarTrainConfResp")
	proto.RegisterType((*GetStarTrainConfByIdReq)(nil), "star_train.GetStarTrainConfByIdReq")
	proto.RegisterType((*GetStarTrainConfByIdResp)(nil), "star_train.GetStarTrainConfByIdResp")
	proto.RegisterType((*SetStarTrainGiftReq)(nil), "star_train.SetStarTrainGiftReq")
	proto.RegisterType((*SetStarTrainGiftResp)(nil), "star_train.SetStarTrainGiftResp")
	proto.RegisterType((*GetAllStarTrainGiftReq)(nil), "star_train.GetAllStarTrainGiftReq")
	proto.RegisterType((*TicketGiftInfo)(nil), "star_train.TicketGiftInfo")
	proto.RegisterType((*GetAllStarTrainGiftResp)(nil), "star_train.GetAllStarTrainGiftResp")
	proto.RegisterType((*DelStarTrainGiftReq)(nil), "star_train.DelStarTrainGiftReq")
	proto.RegisterType((*DelStarTrainGiftResp)(nil), "star_train.DelStarTrainGiftResp")
	proto.RegisterType((*StarTrainProgress)(nil), "star_train.StarTrainProgress")
	proto.RegisterType((*StarTrainBingo)(nil), "star_train.StarTrainBingo")
	proto.RegisterType((*StarTrainStation)(nil), "star_train.StarTrainStation")
	proto.RegisterType((*StarTrainAward)(nil), "star_train.StarTrainAward")
	proto.RegisterType((*StarTrainUserJoin)(nil), "star_train.StarTrainUserJoin")
	proto.RegisterType((*GetStarTrainInfoReq)(nil), "star_train.GetStarTrainInfoReq")
	proto.RegisterType((*GetStarTrainInfoResp)(nil), "star_train.GetStarTrainInfoResp")
	proto.RegisterType((*GetStarTrainProgressReq)(nil), "star_train.GetStarTrainProgressReq")
	proto.RegisterType((*GetStarTrainProgressResp)(nil), "star_train.GetStarTrainProgressResp")
	proto.RegisterType((*StarTrainBroadcast)(nil), "star_train.StarTrainBroadcast")
	proto.RegisterType((*GetStarTrainBroadcastReq)(nil), "star_train.GetStarTrainBroadcastReq")
	proto.RegisterType((*GetStarTrainBroadcastResp)(nil), "star_train.GetStarTrainBroadcastResp")
	proto.RegisterType((*StarTrainSeat)(nil), "star_train.StarTrainSeat")
	proto.RegisterType((*GetStarTrainSeatListReq)(nil), "star_train.GetStarTrainSeatListReq")
	proto.RegisterType((*GetStarTrainSeatListResp)(nil), "star_train.GetStarTrainSeatListResp")
	proto.RegisterType((*GetSimpleActivityInfoReq)(nil), "star_train.GetSimpleActivityInfoReq")
	proto.RegisterType((*GetSimpleActivityInfoResp)(nil), "star_train.GetSimpleActivityInfoResp")
	proto.RegisterType((*StarTrainUserRecord)(nil), "star_train.StarTrainUserRecord")
	proto.RegisterType((*GetStarTrainUserRecordReq)(nil), "star_train.GetStarTrainUserRecordReq")
	proto.RegisterType((*GetStarTrainUserRecordResp)(nil), "star_train.GetStarTrainUserRecordResp")
	proto.RegisterType((*TrainDepartureRecord)(nil), "star_train.TrainDepartureRecord")
	proto.RegisterType((*TrainDepartureRecord_TopUserInfo)(nil), "star_train.TrainDepartureRecord.TopUserInfo")
	proto.RegisterType((*GetTrainDepartureRecordReq)(nil), "star_train.GetTrainDepartureRecordReq")
	proto.RegisterType((*GetTrainDepartureRecordResp)(nil), "star_train.GetTrainDepartureRecordResp")
	proto.RegisterType((*TestUserJoinReq)(nil), "star_train.TestUserJoinReq")
	proto.RegisterType((*TestUserJoinResp)(nil), "star_train.TestUserJoinResp")
	proto.RegisterType((*TestActivityBeginNotifyReq)(nil), "star_train.TestActivityBeginNotifyReq")
	proto.RegisterType((*TestActivityBeginNotifyResp)(nil), "star_train.TestActivityBeginNotifyResp")
	proto.RegisterEnum("star_train.AwardType", AwardType_name, AwardType_value)
	proto.RegisterEnum("star_train.StationType", StationType_name, StationType_value)
	proto.RegisterEnum("star_train.WeekDay", WeekDay_name, WeekDay_value)
	proto.RegisterEnum("star_train.StarTrainStatus", StarTrainStatus_name, StarTrainStatus_value)
	proto.RegisterEnum("star_train.PrizeType", PrizeType_name, PrizeType_value)
	proto.RegisterEnum("star_train.StarTrainBroadcast_BroadcastType", StarTrainBroadcast_BroadcastType_name, StarTrainBroadcast_BroadcastType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// StarTrainClient is the client API for StarTrain service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type StarTrainClient interface {
	// 获取摘星列车玩法信息
	GetStarTrainInfo(ctx context.Context, in *GetStarTrainInfoReq, opts ...grpc.CallOption) (*GetStarTrainInfoResp, error)
	// 获取摘星列车进程信息
	GetStarTrainProgress(ctx context.Context, in *GetStarTrainProgressReq, opts ...grpc.CallOption) (*GetStarTrainProgressResp, error)
	// 获取摘星列车播报数据
	GetStarTrainBroadcast(ctx context.Context, in *GetStarTrainBroadcastReq, opts ...grpc.CallOption) (*GetStarTrainBroadcastResp, error)
	// 获取简单玩法活动信息
	GetSimpleActivityInfo(ctx context.Context, in *GetSimpleActivityInfoReq, opts ...grpc.CallOption) (*GetSimpleActivityInfoResp, error)
	// 获取用户记录
	GetStarTrainUserRecord(ctx context.Context, in *GetStarTrainUserRecordReq, opts ...grpc.CallOption) (*GetStarTrainUserRecordResp, error)
	// 获取发车记录
	GetTrainDepartureRecord(ctx context.Context, in *GetTrainDepartureRecordReq, opts ...grpc.CallOption) (*GetTrainDepartureRecordResp, error)
	// 获取座次表
	GetStarTrainSeatList(ctx context.Context, in *GetStarTrainSeatListReq, opts ...grpc.CallOption) (*GetStarTrainSeatListResp, error)
	// 用户参与测试接口
	TestUserJoin(ctx context.Context, in *TestUserJoinReq, opts ...grpc.CallOption) (*TestUserJoinResp, error)
	// 即将开启活动提醒推送触发(仅测试环境有效)
	TestActivityBeginNotify(ctx context.Context, in *TestActivityBeginNotifyReq, opts ...grpc.CallOption) (*TestActivityBeginNotifyResp, error)
	// 活动配置
	SetStarTrainConf(ctx context.Context, in *SetStarTrainConfReq, opts ...grpc.CallOption) (*SetStarTrainConfResp, error)
	// 获取活动配置
	GetStarTrainConf(ctx context.Context, in *GetStarTrainConfReq, opts ...grpc.CallOption) (*GetStarTrainConfResp, error)
	GetStarTrainConfById(ctx context.Context, in *GetStarTrainConfByIdReq, opts ...grpc.CallOption) (*GetStarTrainConfByIdResp, error)
	// 修改活动配置
	UpdateStarTrainConf(ctx context.Context, in *UpdateStarTrainConfReq, opts ...grpc.CallOption) (*UpdateStarTrainConfResp, error)
	// 删除活动配置
	DelStarTrainConf(ctx context.Context, in *DelStarTrainConfReq, opts ...grpc.CallOption) (*DelStarTrainConfResp, error)
	// 新增活动礼物配置
	SetStarTrainGift(ctx context.Context, in *SetStarTrainGiftReq, opts ...grpc.CallOption) (*SetStarTrainGiftResp, error)
	// 获取活动礼物配置（全量）
	GetAllStarTrainGift(ctx context.Context, in *GetAllStarTrainGiftReq, opts ...grpc.CallOption) (*GetAllStarTrainGiftResp, error)
	// 删除活动礼物配置
	DelStarTrainGift(ctx context.Context, in *DelStarTrainGiftReq, opts ...grpc.CallOption) (*DelStarTrainGiftResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 送礼订单对账
	GetPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	GetPresentOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type starTrainClient struct {
	cc *grpc.ClientConn
}

func NewStarTrainClient(cc *grpc.ClientConn) StarTrainClient {
	return &starTrainClient{cc}
}

func (c *starTrainClient) GetStarTrainInfo(ctx context.Context, in *GetStarTrainInfoReq, opts ...grpc.CallOption) (*GetStarTrainInfoResp, error) {
	out := new(GetStarTrainInfoResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetStarTrainProgress(ctx context.Context, in *GetStarTrainProgressReq, opts ...grpc.CallOption) (*GetStarTrainProgressResp, error) {
	out := new(GetStarTrainProgressResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainProgress", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetStarTrainBroadcast(ctx context.Context, in *GetStarTrainBroadcastReq, opts ...grpc.CallOption) (*GetStarTrainBroadcastResp, error) {
	out := new(GetStarTrainBroadcastResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainBroadcast", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetSimpleActivityInfo(ctx context.Context, in *GetSimpleActivityInfoReq, opts ...grpc.CallOption) (*GetSimpleActivityInfoResp, error) {
	out := new(GetSimpleActivityInfoResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetSimpleActivityInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetStarTrainUserRecord(ctx context.Context, in *GetStarTrainUserRecordReq, opts ...grpc.CallOption) (*GetStarTrainUserRecordResp, error) {
	out := new(GetStarTrainUserRecordResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainUserRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetTrainDepartureRecord(ctx context.Context, in *GetTrainDepartureRecordReq, opts ...grpc.CallOption) (*GetTrainDepartureRecordResp, error) {
	out := new(GetTrainDepartureRecordResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetTrainDepartureRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetStarTrainSeatList(ctx context.Context, in *GetStarTrainSeatListReq, opts ...grpc.CallOption) (*GetStarTrainSeatListResp, error) {
	out := new(GetStarTrainSeatListResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainSeatList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) TestUserJoin(ctx context.Context, in *TestUserJoinReq, opts ...grpc.CallOption) (*TestUserJoinResp, error) {
	out := new(TestUserJoinResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/TestUserJoin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) TestActivityBeginNotify(ctx context.Context, in *TestActivityBeginNotifyReq, opts ...grpc.CallOption) (*TestActivityBeginNotifyResp, error) {
	out := new(TestActivityBeginNotifyResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/TestActivityBeginNotify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) SetStarTrainConf(ctx context.Context, in *SetStarTrainConfReq, opts ...grpc.CallOption) (*SetStarTrainConfResp, error) {
	out := new(SetStarTrainConfResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/SetStarTrainConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetStarTrainConf(ctx context.Context, in *GetStarTrainConfReq, opts ...grpc.CallOption) (*GetStarTrainConfResp, error) {
	out := new(GetStarTrainConfResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetStarTrainConfById(ctx context.Context, in *GetStarTrainConfByIdReq, opts ...grpc.CallOption) (*GetStarTrainConfByIdResp, error) {
	out := new(GetStarTrainConfByIdResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetStarTrainConfById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) UpdateStarTrainConf(ctx context.Context, in *UpdateStarTrainConfReq, opts ...grpc.CallOption) (*UpdateStarTrainConfResp, error) {
	out := new(UpdateStarTrainConfResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/UpdateStarTrainConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) DelStarTrainConf(ctx context.Context, in *DelStarTrainConfReq, opts ...grpc.CallOption) (*DelStarTrainConfResp, error) {
	out := new(DelStarTrainConfResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/DelStarTrainConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) SetStarTrainGift(ctx context.Context, in *SetStarTrainGiftReq, opts ...grpc.CallOption) (*SetStarTrainGiftResp, error) {
	out := new(SetStarTrainGiftResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/SetStarTrainGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetAllStarTrainGift(ctx context.Context, in *GetAllStarTrainGiftReq, opts ...grpc.CallOption) (*GetAllStarTrainGiftResp, error) {
	out := new(GetAllStarTrainGiftResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetAllStarTrainGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) DelStarTrainGift(ctx context.Context, in *DelStarTrainGiftReq, opts ...grpc.CallOption) (*DelStarTrainGiftResp, error) {
	out := new(DelStarTrainGiftResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/DelStarTrainGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetAwardTotalCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetAwardTotalCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetAwardOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetAwardOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetPresentOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetPresentOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) GetPresentOrderIds(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/GetPresentOrderIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *starTrainClient) FixPresentOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/star_train.StarTrain/FixPresentOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StarTrainServer is the server API for StarTrain service.
type StarTrainServer interface {
	// 获取摘星列车玩法信息
	GetStarTrainInfo(context.Context, *GetStarTrainInfoReq) (*GetStarTrainInfoResp, error)
	// 获取摘星列车进程信息
	GetStarTrainProgress(context.Context, *GetStarTrainProgressReq) (*GetStarTrainProgressResp, error)
	// 获取摘星列车播报数据
	GetStarTrainBroadcast(context.Context, *GetStarTrainBroadcastReq) (*GetStarTrainBroadcastResp, error)
	// 获取简单玩法活动信息
	GetSimpleActivityInfo(context.Context, *GetSimpleActivityInfoReq) (*GetSimpleActivityInfoResp, error)
	// 获取用户记录
	GetStarTrainUserRecord(context.Context, *GetStarTrainUserRecordReq) (*GetStarTrainUserRecordResp, error)
	// 获取发车记录
	GetTrainDepartureRecord(context.Context, *GetTrainDepartureRecordReq) (*GetTrainDepartureRecordResp, error)
	// 获取座次表
	GetStarTrainSeatList(context.Context, *GetStarTrainSeatListReq) (*GetStarTrainSeatListResp, error)
	// 用户参与测试接口
	TestUserJoin(context.Context, *TestUserJoinReq) (*TestUserJoinResp, error)
	// 即将开启活动提醒推送触发(仅测试环境有效)
	TestActivityBeginNotify(context.Context, *TestActivityBeginNotifyReq) (*TestActivityBeginNotifyResp, error)
	// 活动配置
	SetStarTrainConf(context.Context, *SetStarTrainConfReq) (*SetStarTrainConfResp, error)
	// 获取活动配置
	GetStarTrainConf(context.Context, *GetStarTrainConfReq) (*GetStarTrainConfResp, error)
	GetStarTrainConfById(context.Context, *GetStarTrainConfByIdReq) (*GetStarTrainConfByIdResp, error)
	// 修改活动配置
	UpdateStarTrainConf(context.Context, *UpdateStarTrainConfReq) (*UpdateStarTrainConfResp, error)
	// 删除活动配置
	DelStarTrainConf(context.Context, *DelStarTrainConfReq) (*DelStarTrainConfResp, error)
	// 新增活动礼物配置
	SetStarTrainGift(context.Context, *SetStarTrainGiftReq) (*SetStarTrainGiftResp, error)
	// 获取活动礼物配置（全量）
	GetAllStarTrainGift(context.Context, *GetAllStarTrainGiftReq) (*GetAllStarTrainGiftResp, error)
	// 删除活动礼物配置
	DelStarTrainGift(context.Context, *DelStarTrainGiftReq) (*DelStarTrainGiftResp, error)
	// 发放包裹数据对账
	GetAwardTotalCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetAwardOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 送礼订单对账
	GetPresentOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	GetPresentOrderIds(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	FixPresentOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterStarTrainServer(s *grpc.Server, srv StarTrainServer) {
	s.RegisterService(&_StarTrain_serviceDesc, srv)
}

func _StarTrain_GetStarTrainInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainInfo(ctx, req.(*GetStarTrainInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetStarTrainProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainProgress",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainProgress(ctx, req.(*GetStarTrainProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetStarTrainBroadcast_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainBroadcastReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainBroadcast(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainBroadcast",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainBroadcast(ctx, req.(*GetStarTrainBroadcastReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetSimpleActivityInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSimpleActivityInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetSimpleActivityInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetSimpleActivityInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetSimpleActivityInfo(ctx, req.(*GetSimpleActivityInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetStarTrainUserRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainUserRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainUserRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainUserRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainUserRecord(ctx, req.(*GetStarTrainUserRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetTrainDepartureRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrainDepartureRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetTrainDepartureRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetTrainDepartureRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetTrainDepartureRecord(ctx, req.(*GetTrainDepartureRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetStarTrainSeatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainSeatListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainSeatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainSeatList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainSeatList(ctx, req.(*GetStarTrainSeatListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_TestUserJoin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestUserJoinReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).TestUserJoin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/TestUserJoin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).TestUserJoin(ctx, req.(*TestUserJoinReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_TestActivityBeginNotify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestActivityBeginNotifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).TestActivityBeginNotify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/TestActivityBeginNotify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).TestActivityBeginNotify(ctx, req.(*TestActivityBeginNotifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_SetStarTrainConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStarTrainConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).SetStarTrainConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/SetStarTrainConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).SetStarTrainConf(ctx, req.(*SetStarTrainConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetStarTrainConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainConf(ctx, req.(*GetStarTrainConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetStarTrainConfById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarTrainConfByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetStarTrainConfById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetStarTrainConfById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetStarTrainConfById(ctx, req.(*GetStarTrainConfByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_UpdateStarTrainConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStarTrainConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).UpdateStarTrainConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/UpdateStarTrainConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).UpdateStarTrainConf(ctx, req.(*UpdateStarTrainConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_DelStarTrainConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStarTrainConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).DelStarTrainConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/DelStarTrainConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).DelStarTrainConf(ctx, req.(*DelStarTrainConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_SetStarTrainGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetStarTrainGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).SetStarTrainGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/SetStarTrainGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).SetStarTrainGift(ctx, req.(*SetStarTrainGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetAllStarTrainGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllStarTrainGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetAllStarTrainGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetAllStarTrainGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetAllStarTrainGift(ctx, req.(*GetAllStarTrainGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_DelStarTrainGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelStarTrainGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).DelStarTrainGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/DelStarTrainGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).DelStarTrainGift(ctx, req.(*DelStarTrainGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetAwardTotalCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetAwardTotalCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetAwardTotalCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetAwardTotalCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetAwardOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetAwardOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetAwardOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetAwardOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetPresentOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetPresentOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetPresentOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetPresentOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_GetPresentOrderIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).GetPresentOrderIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/GetPresentOrderIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).GetPresentOrderIds(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _StarTrain_FixPresentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StarTrainServer).FixPresentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/star_train.StarTrain/FixPresentOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StarTrainServer).FixPresentOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _StarTrain_serviceDesc = grpc.ServiceDesc{
	ServiceName: "star_train.StarTrain",
	HandlerType: (*StarTrainServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetStarTrainInfo",
			Handler:    _StarTrain_GetStarTrainInfo_Handler,
		},
		{
			MethodName: "GetStarTrainProgress",
			Handler:    _StarTrain_GetStarTrainProgress_Handler,
		},
		{
			MethodName: "GetStarTrainBroadcast",
			Handler:    _StarTrain_GetStarTrainBroadcast_Handler,
		},
		{
			MethodName: "GetSimpleActivityInfo",
			Handler:    _StarTrain_GetSimpleActivityInfo_Handler,
		},
		{
			MethodName: "GetStarTrainUserRecord",
			Handler:    _StarTrain_GetStarTrainUserRecord_Handler,
		},
		{
			MethodName: "GetTrainDepartureRecord",
			Handler:    _StarTrain_GetTrainDepartureRecord_Handler,
		},
		{
			MethodName: "GetStarTrainSeatList",
			Handler:    _StarTrain_GetStarTrainSeatList_Handler,
		},
		{
			MethodName: "TestUserJoin",
			Handler:    _StarTrain_TestUserJoin_Handler,
		},
		{
			MethodName: "TestActivityBeginNotify",
			Handler:    _StarTrain_TestActivityBeginNotify_Handler,
		},
		{
			MethodName: "SetStarTrainConf",
			Handler:    _StarTrain_SetStarTrainConf_Handler,
		},
		{
			MethodName: "GetStarTrainConf",
			Handler:    _StarTrain_GetStarTrainConf_Handler,
		},
		{
			MethodName: "GetStarTrainConfById",
			Handler:    _StarTrain_GetStarTrainConfById_Handler,
		},
		{
			MethodName: "UpdateStarTrainConf",
			Handler:    _StarTrain_UpdateStarTrainConf_Handler,
		},
		{
			MethodName: "DelStarTrainConf",
			Handler:    _StarTrain_DelStarTrainConf_Handler,
		},
		{
			MethodName: "SetStarTrainGift",
			Handler:    _StarTrain_SetStarTrainGift_Handler,
		},
		{
			MethodName: "GetAllStarTrainGift",
			Handler:    _StarTrain_GetAllStarTrainGift_Handler,
		},
		{
			MethodName: "DelStarTrainGift",
			Handler:    _StarTrain_DelStarTrainGift_Handler,
		},
		{
			MethodName: "GetAwardTotalCount",
			Handler:    _StarTrain_GetAwardTotalCount_Handler,
		},
		{
			MethodName: "GetAwardOrderIds",
			Handler:    _StarTrain_GetAwardOrderIds_Handler,
		},
		{
			MethodName: "GetPresentOrderCount",
			Handler:    _StarTrain_GetPresentOrderCount_Handler,
		},
		{
			MethodName: "GetPresentOrderIds",
			Handler:    _StarTrain_GetPresentOrderIds_Handler,
		},
		{
			MethodName: "FixPresentOrder",
			Handler:    _StarTrain_FixPresentOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "star-train/star-train.proto",
}

func init() {
	proto.RegisterFile("star-train/star-train.proto", fileDescriptor_star_train_03aa28f1592d8825)
}

var fileDescriptor_star_train_03aa28f1592d8825 = []byte{
	// 3034 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x1a, 0xcb, 0x76, 0xdb, 0xc6,
	0x55, 0x7c, 0x88, 0x22, 0xaf, 0x44, 0x99, 0x1e, 0xc9, 0x32, 0x49, 0xdb, 0xb1, 0x02, 0xe7, 0xe1,
	0xba, 0x89, 0xdc, 0x28, 0xa7, 0x49, 0x9c, 0x4d, 0x23, 0x59, 0x8f, 0xd0, 0x4d, 0x2c, 0x15, 0xa4,
	0xea, 0x93, 0xe4, 0x9c, 0xb0, 0x10, 0x30, 0xa4, 0x50, 0x93, 0x00, 0x82, 0x01, 0x6d, 0x33, 0x9b,
	0x76, 0xd3, 0x9e, 0x6e, 0xba, 0xea, 0x0f, 0xf4, 0x74, 0xd3, 0x3f, 0x68, 0x3f, 0xa0, 0x8b, 0x6e,
	0xda, 0x5f, 0xe8, 0x63, 0xdd, 0x55, 0x37, 0x5d, 0x74, 0xd5, 0x73, 0xef, 0x0c, 0x40, 0x00, 0x04,
	0x24, 0xb9, 0x5e, 0x11, 0x73, 0x5f, 0x33, 0x73, 0xe7, 0x3e, 0x67, 0x08, 0x37, 0x44, 0x60, 0xf8,
	0xef, 0x06, 0xbe, 0x61, 0x3b, 0xf7, 0x67, 0x9f, 0x5b, 0x9e, 0xef, 0x06, 0x2e, 0x03, 0x84, 0xf4,
	0x09, 0xd2, 0xbe, 0xcd, 0x5f, 0x04, 0xdc, 0x11, 0xb6, 0xeb, 0xdc, 0x77, 0xbd, 0xc0, 0x76, 0x1d,
	0x11, 0xfe, 0x4a, 0xe2, 0xf6, 0x6d, 0x9f, 0x9b, 0xae, 0x63, 0xda, 0x23, 0xfe, 0xee, 0xb3, 0xed,
	0xfb, 0xf1, 0x81, 0x24, 0xd0, 0x06, 0xd0, 0xd8, 0x33, 0xa6, 0x3d, 0x7b, 0xcc, 0x75, 0xc3, 0x19,
	0xf2, 0x8e, 0x33, 0x70, 0xd9, 0x0d, 0xa8, 0x59, 0xc6, 0xb4, 0x7f, 0xca, 0x87, 0xb6, 0xd3, 0x2c,
	0x6c, 0x16, 0xee, 0xd6, 0xf5, 0xaa, 0x65, 0x4c, 0x77, 0x71, 0xcc, 0xae, 0xc3, 0x12, 0x22, 0xb9,
	0x63, 0x35, 0x8b, 0x84, 0xaa, 0x58, 0xc6, 0x74, 0xdf, 0xb1, 0xd8, 0x4d, 0x80, 0xe7, 0x7d, 0x44,
	0x8d, 0x6c, 0x11, 0x34, 0x4b, 0x9b, 0x25, 0x64, 0x7b, 0xbe, 0x67, 0x4c, 0x3f, 0xb3, 0x45, 0xa0,
	0xfd, 0xa9, 0x08, 0x57, 0xba, 0x81, 0xe1, 0xf7, 0x70, 0xdd, 0xbb, 0x43, 0x9a, 0x67, 0x0d, 0x16,
	0x4f, 0x87, 0x7d, 0xdb, 0x52, 0x73, 0x94, 0x4f, 0x87, 0x1d, 0x0b, 0xe5, 0x0f, 0xed, 0x41, 0x80,
	0x60, 0x25, 0x1f, 0x87, 0x1d, 0x8b, 0xdd, 0x02, 0x20, 0x84, 0xe7, 0xdb, 0x26, 0x6f, 0x96, 0x08,
	0x57, 0x43, 0xc8, 0x31, 0x02, 0x70, 0xd1, 0x84, 0x76, 0x8c, 0x31, 0x6f, 0x96, 0x37, 0x0b, 0x77,
	0x6b, 0x7a, 0x15, 0x01, 0x8f, 0x8d, 0x31, 0x67, 0x2d, 0xa8, 0x4a, 0x5e, 0xdb, 0x6c, 0x2e, 0x12,
	0x8e, 0x26, 0x39, 0xb6, 0xcd, 0x08, 0xe5, 0x4c, 0xc6, 0xcd, 0x0a, 0x09, 0x25, 0xd4, 0xe3, 0xc9,
	0x98, 0xad, 0xc3, 0xa2, 0x08, 0x5c, 0xf3, 0x69, 0x73, 0x89, 0xe0, 0x72, 0x80, 0xeb, 0x30, 0x9e,
	0x1b, 0xbe, 0xd5, 0x0f, 0xa6, 0x1e, 0x6f, 0x56, 0xe5, 0x3a, 0x08, 0xd2, 0x9b, 0x7a, 0x34, 0xd5,
	0xc0, 0x76, 0xfa, 0x81, 0x3d, 0xe6, 0xcd, 0x9a, 0x94, 0x37, 0xb0, 0x1d, 0x54, 0x30, 0xdb, 0x80,
	0xca, 0xd8, 0x75, 0x82, 0x33, 0xd1, 0x04, 0xb9, 0x33, 0x39, 0x62, 0x77, 0xa1, 0x61, 0x4d, 0x1d,
	0x63, 0x6c, 0x9b, 0xfd, 0x88, 0x75, 0x99, 0x28, 0x56, 0x15, 0xfc, 0x40, 0x4a, 0xd0, 0xfe, 0x50,
	0x84, 0xe5, 0x6e, 0x60, 0xe0, 0x09, 0x93, 0x06, 0x6f, 0x01, 0x5a, 0x03, 0x0e, 0x43, 0x35, 0xd6,
	0xf4, 0x9a, 0x82, 0x74, 0x2c, 0xd6, 0x86, 0xaa, 0x65, 0x8b, 0xc0, 0x70, 0x4c, 0xae, 0x94, 0x19,
	0x8d, 0xd9, 0xc7, 0x00, 0x43, 0xd7, 0x18, 0xa1, 0x3a, 0xbf, 0x95, 0xea, 0x5c, 0xde, 0xbe, 0xb1,
	0x35, 0xb3, 0xad, 0xad, 0xd4, 0x69, 0xe9, 0x35, 0x24, 0x3f, 0x46, 0x6a, 0xe4, 0x3d, 0xe3, 0x23,
	0x4f, 0xf1, 0x96, 0x2f, 0xc1, 0x8b, 0xe4, 0x92, 0xf7, 0x47, 0xb0, 0x61, 0xba, 0x8e, 0x70, 0x47,
	0x72, 0xd9, 0x24, 0x42, 0x9a, 0xcc, 0xe2, 0x66, 0xe9, 0x22, 0x39, 0xeb, 0x31, 0x56, 0x12, 0x87,
	0xb6, 0xc5, 0x5e, 0x87, 0x95, 0x50, 0x0b, 0x74, 0x26, 0xf2, 0x18, 0x97, 0x15, 0x0c, 0x4f, 0x45,
	0xfb, 0x5d, 0x11, 0x56, 0x49, 0x90, 0xee, 0x4e, 0x02, 0x69, 0xe5, 0x2d, 0xa8, 0xd2, 0x24, 0x33,
	0x03, 0x5c, 0xa2, 0x71, 0xc7, 0xc2, 0x83, 0x7a, 0xce, 0xed, 0xe1, 0x59, 0x10, 0x9a, 0xa0, 0x1c,
	0xb1, 0x8f, 0x61, 0x85, 0x74, 0xa6, 0x24, 0x2b, 0xad, 0x5d, 0x4f, 0xad, 0x38, 0x3c, 0x1d, 0x7d,
	0x19, 0x89, 0x15, 0x00, 0x79, 0xc3, 0x45, 0xd2, 0x6e, 0xcb, 0xb4, 0xdb, 0x7c, 0x5e, 0x45, 0x4c,
	0x1b, 0xbc, 0x0d, 0xcb, 0x03, 0xfb, 0x05, 0xb7, 0xfa, 0xae, 0x6f, 0x71, 0x9f, 0x2c, 0xb8, 0xae,
	0x03, 0x81, 0x8e, 0x10, 0x82, 0x1a, 0x08, 0xdc, 0xc0, 0x18, 0xf5, 0x7d, 0x8e, 0x86, 0x48, 0x06,
	0x5b, 0xd6, 0x97, 0x09, 0xa6, 0x13, 0x08, 0x49, 0x24, 0xb2, 0xef, 0xa3, 0x60, 0x32, 0xdc, 0xa2,
	0xbe, 0x2c, 0x61, 0x3a, 0x82, 0xb4, 0x5f, 0x17, 0xa1, 0x1e, 0x69, 0xfc, 0xa1, 0xeb, 0x0c, 0xd0,
	0x19, 0x4d, 0xd7, 0x19, 0xcc, 0x54, 0x54, 0xc1, 0x61, 0xc7, 0x62, 0x77, 0xa0, 0x6e, 0x98, 0x81,
	0xfd, 0xcc, 0x0e, 0xa6, 0xd2, 0xe3, 0x8a, 0x64, 0x7b, 0x2b, 0x21, 0x90, 0xbc, 0xee, 0x4d, 0x58,
	0x8d, 0x88, 0x64, 0x30, 0x41, 0x85, 0x95, 0xf4, 0x88, 0x55, 0x46, 0x94, 0xd7, 0x21, 0x62, 0xa3,
	0xb0, 0x52, 0x26, 0xa2, 0xe5, 0x10, 0x86, 0xb1, 0xe5, 0x01, 0x80, 0x3c, 0xab, 0x98, 0xa1, 0xb4,
	0xe3, 0xaa, 0x4b, 0x9e, 0xad, 0x5e, 0x23, 0x28, 0xe9, 0xee, 0x01, 0xd4, 0xd0, 0xa1, 0x24, 0x67,
	0x85, 0x38, 0x6f, 0xc6, 0x39, 0xd3, 0xd1, 0x4f, 0xaf, 0x22, 0x39, 0xc5, 0xac, 0x3d, 0x58, 0xeb,
	0xf2, 0x20, 0xa1, 0x11, 0x9d, 0x7f, 0xc3, 0xde, 0x85, 0x32, 0x6a, 0x81, 0x34, 0xb2, 0xbc, 0xdd,
	0xca, 0xb4, 0x57, 0xa2, 0x25, 0x32, 0x6d, 0x03, 0xd6, 0xe7, 0xa5, 0x08, 0x4f, 0xfb, 0x1a, 0xd6,
	0x0e, 0x33, 0xa4, 0x6f, 0x40, 0x05, 0x8f, 0x7e, 0x22, 0x42, 0x8d, 0xcb, 0x11, 0x63, 0x50, 0xf6,
	0x8c, 0x61, 0xe8, 0xc7, 0xf4, 0x8d, 0x31, 0x0f, 0x7f, 0xfb, 0x22, 0x74, 0xe1, 0xba, 0x5e, 0x45,
	0x40, 0xd7, 0xfe, 0x96, 0x6b, 0x16, 0xac, 0x1f, 0x66, 0xcc, 0xcb, 0x3e, 0x80, 0x1a, 0x9d, 0x29,
	0x29, 0xa4, 0x40, 0x0a, 0x39, 0x67, 0x0f, 0x55, 0xa4, 0x25, 0x45, 0xae, 0xc3, 0x22, 0xd9, 0x93,
	0x5a, 0x81, 0x1c, 0x68, 0x87, 0xb0, 0x71, 0xe2, 0x59, 0x46, 0xc0, 0x5f, 0x55, 0x4d, 0x2d, 0xb8,
	0x9e, 0x29, 0x48, 0x78, 0xda, 0x16, 0xac, 0xed, 0xf1, 0xd1, 0xdc, 0x04, 0x79, 0xc6, 0x89, 0x1a,
	0x9f, 0xa7, 0x17, 0x9e, 0xb6, 0x0d, 0xd7, 0xd3, 0x1a, 0xd9, 0x9d, 0x76, 0xac, 0x73, 0x65, 0x75,
	0xa0, 0x99, 0xcd, 0x23, 0xbc, 0x97, 0xdd, 0xe1, 0x56, 0xd2, 0x9c, 0x0e, 0xed, 0x41, 0xa0, 0xa6,
	0x0e, 0x13, 0x5e, 0x21, 0x9e, 0xf0, 0xd2, 0x86, 0x23, 0xe9, 0x85, 0xa7, 0x35, 0x61, 0xe3, 0x90,
	0x07, 0x3b, 0xa3, 0x51, 0x5a, 0x94, 0xf6, 0xf3, 0x02, 0xac, 0xf6, 0x6c, 0xf3, 0x29, 0x0f, 0x10,
	0x42, 0x51, 0x2e, 0x4f, 0x7a, 0x2a, 0x9d, 0x16, 0xcf, 0x4d, 0xa7, 0xa5, 0x73, 0xd2, 0x69, 0x39,
	0x91, 0x4e, 0x35, 0x9d, 0x74, 0x3c, 0xbf, 0x38, 0xe1, 0xb1, 0x0f, 0x95, 0xc8, 0x98, 0xe1, 0x25,
	0x7d, 0x38, 0xb1, 0x72, 0x39, 0x1d, 0xf9, 0x61, 0xea, 0xfc, 0x2f, 0xa3, 0xb8, 0x79, 0x7a, 0xe1,
	0x69, 0xff, 0x28, 0xc1, 0xd5, 0x08, 0x7a, 0xec, 0xbb, 0x43, 0x9f, 0x0b, 0xc1, 0xae, 0x41, 0xc5,
	0x30, 0x63, 0x52, 0x16, 0x0d, 0x13, 0xf5, 0xd3, 0x82, 0xaa, 0xef, 0x4e, 0x1c, 0x6b, 0x56, 0x88,
	0x2c, 0xd1, 0xb8, 0x63, 0x61, 0x5c, 0x93, 0xd1, 0x36, 0x4a, 0xae, 0xd2, 0xf7, 0xea, 0x04, 0xdd,
	0x0b, 0x33, 0xec, 0x47, 0x50, 0x55, 0x41, 0x5c, 0xa8, 0x68, 0x7f, 0x33, 0xd3, 0x44, 0x54, 0xd8,
	0xd7, 0x23, 0x6a, 0xa6, 0x41, 0x5d, 0x70, 0x23, 0xe8, 0x4f, 0x6c, 0x6b, 0x16, 0xf1, 0x30, 0xa3,
	0x71, 0x23, 0x38, 0xb1, 0x2d, 0x72, 0xc7, 0xef, 0x40, 0xc3, 0x9c, 0xf8, 0x3e, 0x77, 0x82, 0xd9,
	0x32, 0x64, 0xe2, 0xbb, 0xa2, 0xe0, 0xd1, 0x42, 0xee, 0x40, 0x3d, 0x24, 0x15, 0x1e, 0xe7, 0x96,
	0xaa, 0x67, 0x56, 0x14, 0xb0, 0x8b, 0x30, 0xb6, 0x07, 0x8d, 0x91, 0x21, 0x82, 0xbe, 0xdc, 0xf4,
	0xa9, 0xed, 0x0c, 0x65, 0x8e, 0x48, 0x1d, 0xd2, 0x2c, 0x23, 0x23, 0x85, 0xbe, 0x8a, 0x3c, 0x3a,
	0xb2, 0xd0, 0x98, 0xbd, 0x05, 0x57, 0x02, 0x17, 0x8b, 0xc0, 0x30, 0xb5, 0xbe, 0x50, 0x45, 0x50,
	0x9d, 0xc0, 0x3d, 0x99, 0x60, 0x5f, 0x60, 0x40, 0x27, 0xa1, 0x54, 0x37, 0x51, 0x39, 0x94, 0x37,
	0xcf, 0x0e, 0xe5, 0x27, 0x2c, 0x6a, 0x7c, 0xfa, 0x44, 0xcb, 0x14, 0xdc, 0x7f, 0xc6, 0xfd, 0x7e,
	0x20, 0xa8, 0x4c, 0x2a, 0xe9, 0x55, 0x09, 0xe8, 0x09, 0xad, 0x07, 0xab, 0xc9, 0x15, 0xb2, 0x06,
	0x94, 0x26, 0xd1, 0xd9, 0xe2, 0x27, 0xfb, 0x1e, 0x2c, 0xca, 0x69, 0x8b, 0x17, 0x4e, 0x2b, 0x09,
	0xb5, 0x5f, 0x16, 0xa0, 0x91, 0x3e, 0x2e, 0x2c, 0xae, 0x3c, 0x57, 0xd8, 0x54, 0x08, 0xa8, 0x22,
	0x39, 0x1c, 0xe3, 0xf6, 0x64, 0x8d, 0x68, 0x3b, 0x03, 0xf7, 0x12, 0xf3, 0xc8, 0xfa, 0x31, 0x2c,
	0xbe, 0x25, 0x2b, 0xae, 0x5a, 0xc5, 0x74, 0x02, 0x9c, 0xd8, 0x96, 0xf6, 0x9b, 0x42, 0x6c, 0x7f,
	0x52, 0x1d, 0xab, 0x50, 0x8c, 0xb6, 0x57, 0xb4, 0x2d, 0xcc, 0x13, 0xb1, 0x84, 0x4c, 0xdf, 0x08,
	0xb3, 0x4d, 0x55, 0xaf, 0xd4, 0x74, 0xfa, 0xc6, 0x70, 0x2e, 0x5d, 0xbf, 0x2c, 0xad, 0x9e, 0x06,
	0x98, 0x7d, 0x8c, 0xb1, 0x3b, 0x71, 0x02, 0x55, 0x64, 0xa8, 0x51, 0x14, 0x0e, 0x62, 0xf5, 0x15,
	0xf9, 0x27, 0x15, 0x57, 0xff, 0x2c, 0xc4, 0xfc, 0xea, 0x44, 0x70, 0xff, 0x91, 0x6b, 0x3b, 0xff,
	0x87, 0x5f, 0xdd, 0x81, 0xfa, 0x4f, 0x5d, 0xdb, 0x49, 0xbb, 0xd5, 0x0a, 0x02, 0x23, 0x63, 0xfe,
	0x10, 0x9a, 0x64, 0xa7, 0x44, 0x39, 0xb0, 0x9d, 0xb8, 0x1b, 0xca, 0xad, 0x5c, 0x43, 0x3c, 0x2e,
	0xe1, 0x00, 0xb1, 0x11, 0xe3, 0x6d, 0x20, 0xff, 0xe9, 0x27, 0xf6, 0x07, 0x08, 0xda, 0x89, 0xf6,
	0x38, 0x33, 0xac, 0x4a, 0xca, 0xb0, 0xde, 0x4e, 0x66, 0x6b, 0x8a, 0x50, 0xfc, 0x9b, 0x79, 0xeb,
	0xd2, 0xfe, 0x56, 0x48, 0xe6, 0x5d, 0x49, 0x29, 0x3c, 0xf6, 0x00, 0xaa, 0x9e, 0x8a, 0x39, 0x2a,
	0x63, 0xdc, 0xca, 0xb4, 0x88, 0x30, 0x30, 0xe9, 0x11, 0x39, 0xfb, 0x01, 0xac, 0x8c, 0xa7, 0x72,
	0xc7, 0x31, 0x83, 0xca, 0x66, 0x0f, 0xf5, 0xaf, 0xc3, 0x78, 0x8a, 0xbf, 0x64, 0x54, 0x9b, 0x54,
	0x62, 0x61, 0x75, 0x25, 0xbb, 0x0b, 0x59, 0x87, 0x81, 0x61, 0x06, 0xfb, 0x8e, 0x45, 0xbd, 0xc9,
	0x5d, 0x68, 0x38, 0xfc, 0x45, 0xe4, 0xfe, 0x54, 0xad, 0xc9, 0x42, 0x6c, 0x15, 0xe1, 0xd2, 0xc5,
	0x11, 0xaa, 0x7d, 0x37, 0x99, 0x45, 0xa3, 0xe5, 0x66, 0x6a, 0xe3, 0x24, 0x99, 0x3e, 0x67, 0xc4,
	0xaf, 0xa4, 0x10, 0xed, 0xcf, 0x45, 0x60, 0x33, 0x3f, 0xf7, 0x5d, 0xc3, 0x32, 0x0d, 0x11, 0x60,
	0x60, 0x3e, 0x0d, 0x07, 0xd2, 0x54, 0xe5, 0x52, 0xea, 0x11, 0x94, 0x5a, 0x34, 0xb5, 0xcc, 0x62,
	0x46, 0x48, 0x28, 0x5d, 0x32, 0x24, 0xa0, 0xad, 0xda, 0x8e, 0xe9, 0xa7, 0x6d, 0x6f, 0x05, 0x81,
	0xa1, 0xc9, 0x69, 0xbf, 0x2d, 0x40, 0x7d, 0x37, 0x31, 0xf5, 0x6b, 0xd0, 0xde, 0xd5, 0x8f, 0x76,
	0xf6, 0x1e, 0xee, 0x74, 0x7b, 0xfd, 0xde, 0x17, 0xc7, 0xfb, 0xfd, 0x93, 0xc7, 0xdd, 0xe3, 0xfd,
	0x87, 0x9d, 0x83, 0xce, 0xfe, 0x5e, 0x63, 0x81, 0xdd, 0x82, 0x56, 0x0a, 0xdf, 0xed, 0xed, 0xe8,
	0xfd, 0x9d, 0x27, 0x3b, 0xfa, 0x5e, 0xa3, 0x90, 0xc1, 0xbe, 0x7b, 0x74, 0xd4, 0xed, 0x29, 0x7c,
	0x91, 0x5d, 0x87, 0xb5, 0x14, 0xfe, 0xd1, 0x51, 0xe7, 0x71, 0xa3, 0x94, 0x81, 0xe8, 0xee, 0xef,
	0xf4, 0x1a, 0x65, 0xed, 0x9d, 0xe4, 0x01, 0x45, 0xab, 0xcd, 0x3e, 0xce, 0x53, 0x68, 0xe5, 0x50,
	0x0b, 0x8f, 0xed, 0xc7, 0xb5, 0x1f, 0x4b, 0xf2, 0xaf, 0x65, 0xe7, 0x8f, 0x88, 0x77, 0x76, 0x3a,
	0x94, 0xed, 0xbf, 0x8a, 0x35, 0x21, 0x5d, 0x6e, 0x04, 0x19, 0x11, 0xfc, 0x3a, 0x2c, 0x91, 0x2b,
	0xcf, 0xee, 0x08, 0x70, 0xd8, 0xb1, 0xd2, 0x3e, 0x5e, 0x4a, 0xfb, 0xb8, 0x76, 0x90, 0x34, 0x5e,
	0x94, 0x8f, 0x93, 0x66, 0xee, 0xf6, 0x9c, 0x50, 0xa5, 0xfd, 0xab, 0x90, 0xd4, 0xdb, 0x4c, 0x10,
	0x19, 0xb6, 0x8a, 0xfe, 0x79, 0x95, 0x4e, 0x66, 0xf4, 0xa7, 0xac, 0xfe, 0x01, 0xc6, 0x20, 0x43,
	0xa9, 0xaf, 0x78, 0x4e, 0x71, 0x8e, 0x13, 0x62, 0x78, 0x92, 0xd3, 0xb2, 0x6d, 0x58, 0x1a, 0x4f,
	0xfb, 0x38, 0x54, 0x26, 0x7c, 0x0e, 0x57, 0x65, 0x3c, 0x25, 0xbd, 0xde, 0x85, 0xc6, 0x99, 0xeb,
	0xb8, 0x2a, 0x09, 0xf7, 0x2d, 0x2e, 0xc2, 0x6a, 0x6e, 0x95, 0xe0, 0xb4, 0xb8, 0x3d, 0x2e, 0x4c,
	0xad, 0x2d, 0x37, 0x6b, 0x8f, 0xbd, 0x11, 0xdf, 0x51, 0x6d, 0x99, 0x8a, 0x80, 0xda, 0xef, 0xcb,
	0xd2, 0x26, 0x32, 0x90, 0xc2, 0xcb, 0x4b, 0x02, 0x6f, 0x50, 0x67, 0x28, 0xc3, 0x8c, 0x8c, 0x48,
	0x45, 0x8a, 0x35, 0x18, 0xa5, 0x28, 0xca, 0x50, 0x4c, 0xba, 0x38, 0x6a, 0x6d, 0xc2, 0x8a, 0x2a,
	0x01, 0x67, 0x4d, 0x75, 0x5d, 0x07, 0x59, 0x07, 0x92, 0x62, 0xde, 0x82, 0x2b, 0xb2, 0x94, 0x72,
	0xec, 0xb0, 0xd6, 0x95, 0x91, 0x9f, 0x2a, 0xac, 0x13, 0xc7, 0x56, 0xf5, 0xae, 0x46, 0x0d, 0x6d,
	0x9f, 0xa4, 0x91, 0x26, 0x2a, 0xa4, 0x09, 0xec, 0x42, 0xb1, 0x7e, 0x44, 0x35, 0xb0, 0x7b, 0x70,
	0x55, 0x04, 0xbe, 0x11, 0xf0, 0xe1, 0xb4, 0xef, 0x4f, 0x46, 0xbc, 0x6f, 0x8f, 0x87, 0x54, 0x4b,
	0xd5, 0xf4, 0x2b, 0x21, 0x42, 0x9f, 0x8c, 0x78, 0x67, 0x3c, 0x64, 0xef, 0xc1, 0x35, 0xc7, 0x0d,
	0xfa, 0x3e, 0x37, 0xcc, 0x33, 0x6e, 0xf5, 0x45, 0xe0, 0x7a, 0x52, 0x6e, 0x95, 0xe8, 0x99, 0xe3,
	0x06, 0xba, 0xc4, 0x75, 0x03, 0xd7, 0x23, 0xf1, 0x0f, 0xa0, 0x65, 0x8c, 0x7c, 0x6e, 0x58, 0xd3,
	0x0c, 0xb6, 0x1a, 0xb1, 0x6d, 0x28, 0x82, 0x34, 0xeb, 0x47, 0xd0, 0xa2, 0xa4, 0x38, 0xab, 0xa9,
	0xfa, 0x13, 0xc1, 0x7d, 0xc9, 0x0a, 0xc4, 0x4a, 0x59, 0xb1, 0x1b, 0x56, 0x51, 0x98, 0x25, 0x88,
	0xf3, 0x3e, 0xac, 0xc7, 0x98, 0x66, 0xf3, 0x2d, 0x13, 0xd3, 0xd5, 0xa8, 0xec, 0x8a, 0x4f, 0x95,
	0x66, 0x90, 0x49, 0x1b, 0xb9, 0x56, 0xe4, 0x54, 0x09, 0x2e, 0x4c, 0x42, 0x64, 0x45, 0x7f, 0x2d,
	0xc0, 0x5a, 0x22, 0x4d, 0xe9, 0xdc, 0x74, 0x33, 0x2a, 0x98, 0x97, 0xae, 0xcf, 0x92, 0xd5, 0x49,
	0x29, 0x59, 0x9d, 0x60, 0xa3, 0x23, 0x2f, 0x99, 0x08, 0x2b, 0xc3, 0x74, 0x8d, 0x20, 0xe1, 0x7d,
	0x9d, 0xba, 0xce, 0x13, 0x64, 0x19, 0x25, 0x7d, 0x49, 0x5e, 0xe6, 0x09, 0xc5, 0x69, 0xf2, 0x78,
	0xd5, 0x53, 0x23, 0x08, 0x95, 0x3d, 0x5f, 0x25, 0x83, 0xe1, 0x6c, 0x47, 0xd9, 0xd1, 0x64, 0x03,
	0x2a, 0xee, 0x60, 0x20, 0x78, 0xa0, 0x4a, 0x33, 0x35, 0xc2, 0x42, 0x6c, 0x64, 0x8f, 0xed, 0x30,
	0x5a, 0xc9, 0x81, 0xf6, 0x33, 0x68, 0xe7, 0x09, 0x17, 0x1e, 0xfb, 0x04, 0x96, 0x7d, 0x1a, 0xc5,
	0x43, 0xcc, 0xed, 0xdc, 0x7a, 0x40, 0x71, 0x82, 0xe4, 0x09, 0xaf, 0x94, 0x28, 0xdf, 0x27, 0x96,
	0x04, 0x08, 0x3a, 0x22, 0x88, 0xf6, 0x9f, 0x02, 0xac, 0x93, 0x80, 0x3d, 0xee, 0x19, 0x7e, 0x30,
	0xf1, 0xb9, 0x3a, 0xae, 0x78, 0x54, 0x94, 0x37, 0x8e, 0x51, 0x01, 0xf7, 0x7a, 0xea, 0x7e, 0x4c,
	0x4a, 0x4d, 0x5c, 0x83, 0xb5, 0xa0, 0x9a, 0xf2, 0xe7, 0x25, 0xae, 0x9c, 0xb9, 0x03, 0x35, 0x32,
	0xda, 0xd8, 0xf5, 0xd8, 0x3b, 0x73, 0x77, 0x3c, 0xa9, 0xd5, 0x6c, 0xf5, 0x5c, 0x0f, 0x77, 0x28,
	0x3b, 0x46, 0x64, 0xc7, 0xdd, 0xb5, 0x3f, 0x86, 0xe5, 0x18, 0x22, 0x3b, 0xb4, 0xdb, 0x42, 0x75,
	0x39, 0xb8, 0xca, 0xaa, 0xbe, 0x64, 0x0b, 0x6a, 0x18, 0xb4, 0x47, 0xa4, 0xf9, 0xac, 0xc9, 0xd4,
	0xf5, 0xcc, 0x4b, 0x9c, 0xe2, 0xbf, 0x0b, 0x70, 0x23, 0x57, 0x98, 0xf0, 0xd8, 0x4e, 0xd6, 0x39,
	0x6e, 0x5e, 0xb4, 0xe9, 0x97, 0x3a, 0x48, 0x6c, 0xec, 0x64, 0x98, 0x8f, 0x5d, 0xb5, 0x96, 0x2e,
	0xcc, 0x49, 0x32, 0x05, 0xcc, 0xee, 0x58, 0x2f, 0x9f, 0x2c, 0x7e, 0x51, 0x80, 0x2b, 0x3d, 0x2e,
	0x82, 0xa8, 0x10, 0xcd, 0xf4, 0x86, 0x06, 0x94, 0xcc, 0x59, 0x0d, 0x66, 0xda, 0x96, 0xf4, 0x36,
	0x2e, 0xb0, 0x4b, 0x8d, 0x3a, 0x9f, 0x9a, 0x82, 0x74, 0xac, 0x9c, 0x7e, 0xa5, 0x05, 0x55, 0xba,
	0x13, 0x45, 0x16, 0x75, 0xb1, 0x4f, 0xe3, 0x8e, 0xa5, 0x31, 0x68, 0x24, 0x97, 0x21, 0x3c, 0xed,
	0x0b, 0x68, 0x23, 0x6c, 0x27, 0x7e, 0xff, 0xf8, 0xd8, 0x0d, 0xec, 0xc1, 0x14, 0x57, 0xb9, 0x09,
	0x2b, 0xe6, 0x19, 0x39, 0xfb, 0xec, 0x38, 0xea, 0x3a, 0x98, 0x67, 0xe8, 0xee, 0xa4, 0x85, 0x16,
	0x54, 0xcd, 0x30, 0xd7, 0x14, 0x09, 0xbb, 0x64, 0xca, 0x7e, 0x5c, 0xbb, 0x05, 0x37, 0x72, 0x45,
	0x0b, 0xef, 0xde, 0xd7, 0x50, 0xdb, 0x89, 0xde, 0x08, 0xae, 0x42, 0x3d, 0x1a, 0x3c, 0x76, 0x1d,
	0xde, 0x58, 0x48, 0x80, 0x0e, 0x5d, 0x63, 0xd4, 0x28, 0x24, 0x40, 0x9f, 0xf2, 0x91, 0xd7, 0x28,
	0xb2, 0x26, 0xac, 0x47, 0xa0, 0x87, 0xb3, 0xab, 0xf0, 0x46, 0xe9, 0xde, 0xe7, 0xd1, 0xc3, 0x00,
	0xcd, 0xb0, 0x46, 0xaf, 0x2d, 0xe1, 0x50, 0xcd, 0x71, 0x8d, 0xda, 0xb4, 0x19, 0xd0, 0x1f, 0xd3,
	0x3c, 0x8c, 0x7a, 0xca, 0x10, 0xbc, 0xef, 0x58, 0x8d, 0xe2, 0xbd, 0x00, 0x96, 0x9e, 0x70, 0xfe,
	0x74, 0xcf, 0x98, 0x32, 0x80, 0x4a, 0x77, 0xe2, 0x58, 0xc6, 0xb4, 0xb1, 0x80, 0xdf, 0x9f, 0xbb,
	0xf4, 0x5d, 0x60, 0xcb, 0xb0, 0xd4, 0x9b, 0x70, 0x81, 0x83, 0x22, 0xab, 0x43, 0xed, 0x09, 0xb7,
	0x1c, 0x39, 0x2c, 0xb1, 0x15, 0xa8, 0xf6, 0xce, 0x26, 0x3e, 0x8d, 0xca, 0xc8, 0x75, 0xe0, 0xdb,
	0xf8, 0xbd, 0x88, 0x98, 0xae, 0x11, 0x4c, 0x7c, 0x1c, 0x55, 0x90, 0xed, 0xc4, 0x79, 0xea, 0x3e,
	0x77, 0xf6, 0x8c, 0x69, 0x63, 0xe9, 0xde, 0x27, 0xb1, 0x37, 0xa2, 0xae, 0xbc, 0xf6, 0xac, 0x42,
	0x59, 0xad, 0xbe, 0x06, 0x8b, 0x27, 0xc2, 0x76, 0x86, 0x8d, 0x02, 0x0a, 0x3c, 0x71, 0x26, 0x82,
	0x5b, 0x8d, 0x22, 0x0a, 0x3c, 0xb0, 0x1d, 0x5b, 0x9c, 0x71, 0xab, 0x51, 0xba, 0xf7, 0xf7, 0x02,
	0xd4, 0x8e, 0xa3, 0xd8, 0xde, 0x86, 0x8d, 0x63, 0xbd, 0xf3, 0xe5, 0x7e, 0x56, 0xa5, 0xdd, 0x82,
	0x6b, 0x31, 0x5c, 0xa2, 0xca, 0x4e, 0xb2, 0x25, 0x2b, 0xec, 0x9b, 0xd0, 0x8c, 0xe1, 0x1e, 0x1e,
	0x7d, 0x7e, 0x70, 0xa4, 0x87, 0xd8, 0x12, 0xbb, 0x0d, 0x37, 0x62, 0xd8, 0x4f, 0x8f, 0x1e, 0x1f,
	0xe9, 0xfd, 0xde, 0xd1, 0xf1, 0x7b, 0x8a, 0xa0, 0x9c, 0x47, 0xb0, 0xad, 0x08, 0x16, 0xf3, 0x08,
	0xde, 0x57, 0x04, 0x95, 0xed, 0x5f, 0x35, 0xa0, 0x16, 0x29, 0x89, 0x3d, 0x81, 0x46, 0xba, 0xd9,
	0x64, 0x89, 0x3c, 0x90, 0xd1, 0xb4, 0xb6, 0x37, 0xcf, 0x27, 0x10, 0x9e, 0xb6, 0xc0, 0xcc, 0x64,
	0x17, 0x1b, 0xdd, 0x96, 0xdd, 0xc9, 0xe3, 0x8d, 0xf5, 0x81, 0xed, 0x37, 0x2e, 0x26, 0xa2, 0x49,
	0x06, 0x70, 0x2d, 0xb3, 0x9d, 0x60, 0xb9, 0x02, 0xe2, 0xfd, 0x49, 0xfb, 0xcd, 0x4b, 0x50, 0xc5,
	0xe7, 0x99, 0x2b, 0x51, 0xe7, 0xe7, 0xc9, 0x2a, 0x71, 0xe7, 0xe7, 0xc9, 0xac, 0x75, 0xb5, 0x05,
	0x66, 0xd3, 0xcd, 0x6c, 0x56, 0x8d, 0x93, 0xbb, 0xd4, 0x44, 0xd5, 0xd0, 0x7e, 0xeb, 0x32, 0x64,
	0x34, 0xd5, 0x88, 0x1a, 0x99, 0xcc, 0x04, 0x9d, 0x16, 0x92, 0x93, 0xca, 0xda, 0x6f, 0x5f, 0x8a,
	0x2e, 0xcb, 0x1a, 0xc2, 0x6e, 0x27, 0xdf, 0x1a, 0x62, 0x8d, 0x55, 0xbe, 0x35, 0xc4, 0x9b, 0x26,
	0x6d, 0x81, 0xfd, 0x10, 0x56, 0xe2, 0x01, 0x9b, 0x25, 0x5e, 0x02, 0x53, 0x19, 0xa5, 0x7d, 0x33,
	0x1f, 0x19, 0xea, 0x27, 0x27, 0x1c, 0x27, 0xf5, 0x93, 0x9f, 0x0e, 0x92, 0xfa, 0x39, 0x27, 0xb6,
	0x6b, 0x0b, 0xe8, 0x86, 0xe9, 0x37, 0x9e, 0xa4, 0x1b, 0x66, 0xbc, 0x23, 0x25, 0xdd, 0x30, 0xf3,
	0x89, 0x68, 0x21, 0xed, 0xdf, 0xf3, 0x82, 0x0f, 0x2f, 0x12, 0x7c, 0x98, 0x2d, 0xd8, 0x9c, 0x7f,
	0x1d, 0xda, 0x9d, 0x76, 0xac, 0xfc, 0x13, 0x8d, 0xbd, 0x96, 0xe4, 0x9f, 0x68, 0xfc, 0x79, 0x44,
	0x5b, 0x60, 0x3f, 0x81, 0xb5, 0x8c, 0x37, 0x1d, 0xa6, 0xc5, 0xd9, 0xb3, 0x5f, 0x8f, 0xda, 0x77,
	0x2e, 0xa4, 0x09, 0xf5, 0x93, 0x7e, 0xea, 0x49, 0xea, 0x27, 0xe3, 0xe1, 0x28, 0xa9, 0x9f, 0xcc,
	0x97, 0xa2, 0xb9, 0x13, 0xc5, 0x1e, 0x30, 0xff, 0x44, 0xd5, 0x8b, 0x44, 0xfe, 0x89, 0x46, 0x4f,
	0x10, 0xa4, 0x93, 0x8c, 0x07, 0x92, 0xa4, 0x4e, 0xb2, 0x9f, 0x77, 0xda, 0x77, 0x2e, 0xa4, 0xc9,
	0xd2, 0xc9, 0xfc, 0xd2, 0x33, 0x1e, 0x53, 0xf2, 0x75, 0x12, 0x13, 0xdc, 0x01, 0x86, 0xb3, 0x52,
	0x01, 0xe2, 0x06, 0xc6, 0xe8, 0x21, 0x5d, 0x9b, 0xb6, 0xb6, 0xf4, 0xf0, 0x6f, 0x25, 0x3f, 0xde,
	0xde, 0x8a, 0xde, 0x52, 0x51, 0xe8, 0x46, 0x02, 0x45, 0xe4, 0x4a, 0xd4, 0x23, 0xb2, 0x6b, 0x12,
	0x75, 0x24, 0xeb, 0x35, 0x71, 0x9e, 0xa0, 0x24, 0x2a, 0xe4, 0x88, 0xe2, 0x06, 0x9a, 0xf2, 0xb1,
	0xac, 0x14, 0x09, 0xf7, 0x0a, 0x0b, 0xfb, 0x8c, 0xf6, 0x18, 0x17, 0xf6, 0x2a, 0x4b, 0xeb, 0xc0,
	0x95, 0x03, 0xfb, 0x45, 0x5c, 0x1a, 0xbb, 0x99, 0xa0, 0xd7, 0xb9, 0x37, 0x32, 0x4c, 0x4e, 0xa8,
	0xf9, 0x85, 0xed, 0x8f, 0xbd, 0x40, 0x85, 0x98, 0xf6, 0xd5, 0xff, 0xfe, 0xf1, 0x2f, 0xbd, 0x15,
	0xf9, 0x6c, 0x22, 0xff, 0x0f, 0xb4, 0xbb, 0xf5, 0xe5, 0x3b, 0x43, 0x77, 0x64, 0x38, 0xc3, 0xad,
	0xef, 0x6f, 0x07, 0xc1, 0x96, 0xe9, 0x8e, 0xef, 0xd3, 0x9f, 0x7a, 0x4c, 0x77, 0x74, 0x5f, 0x70,
	0xff, 0x99, 0x6d, 0x72, 0x11, 0xfb, 0xff, 0xd0, 0x69, 0x85, 0xb0, 0xef, 0xff, 0x2f, 0x00, 0x00,
	0xff, 0xff, 0x88, 0xbe, 0x5a, 0x98, 0x5f, 0x24, 0x00, 0x00,
}
