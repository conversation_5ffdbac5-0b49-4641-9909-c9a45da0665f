// Code generated by protoc-gen-go. DO NOT EDIT.
// source: golddiamonn/golddiamonn.proto

package golddiamonn // import "golang.52tt.com/protocol/services/golddiamonn"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import reconcile_v2 "golang.52tt.com/protocol/services/reconcile-v2"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GoldIncomeType int32

const (
	GoldIncomeType_UNKNOW_TYPE     GoldIncomeType = 0
	GoldIncomeType_GAME_INCOME     GoldIncomeType = 1
	GoldIncomeType_GAME_SETTLEMENT GoldIncomeType = 4
	GoldIncomeType_ROOM_INCOME     GoldIncomeType = 6
	GoldIncomeType_ROOM_SETTLEMENT GoldIncomeType = 7
)

var GoldIncomeType_name = map[int32]string{
	0: "UNKNOW_TYPE",
	1: "GAME_INCOME",
	4: "GAME_SETTLEMENT",
	6: "ROOM_INCOME",
	7: "ROOM_SETTLEMENT",
}
var GoldIncomeType_value = map[string]int32{
	"UNKNOW_TYPE":     0,
	"GAME_INCOME":     1,
	"GAME_SETTLEMENT": 4,
	"ROOM_INCOME":     6,
	"ROOM_SETTLEMENT": 7,
}

func (x GoldIncomeType) String() string {
	return proto.EnumName(GoldIncomeType_name, int32(x))
}
func (GoldIncomeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{0}
}

// 请求结算状态 (实际转换为 0 未结算 1 已结算)
type ReqSettleStatus int32

const (
	ReqSettleStatus_ReqSettleStatusALL      ReqSettleStatus = 0
	ReqSettleStatus_ReqSettleStatusWait     ReqSettleStatus = 1
	ReqSettleStatus_ReqSettleStatusFinished ReqSettleStatus = 2
)

var ReqSettleStatus_name = map[int32]string{
	0: "ReqSettleStatusALL",
	1: "ReqSettleStatusWait",
	2: "ReqSettleStatusFinished",
}
var ReqSettleStatus_value = map[string]int32{
	"ReqSettleStatusALL":      0,
	"ReqSettleStatusWait":     1,
	"ReqSettleStatusFinished": 2,
}

func (x ReqSettleStatus) String() string {
	return proto.EnumName(ReqSettleStatus_name, int32(x))
}
func (ReqSettleStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{1}
}

type ApplicationOpeType int32

const (
	ApplicationOpeType_APPLICATION_INIT   ApplicationOpeType = 0
	ApplicationOpeType_APPLICATION_ACCEPT ApplicationOpeType = 1
	ApplicationOpeType_APPLICATION_REJECT ApplicationOpeType = 2
)

var ApplicationOpeType_name = map[int32]string{
	0: "APPLICATION_INIT",
	1: "APPLICATION_ACCEPT",
	2: "APPLICATION_REJECT",
}
var ApplicationOpeType_value = map[string]int32{
	"APPLICATION_INIT":   0,
	"APPLICATION_ACCEPT": 1,
	"APPLICATION_REJECT": 2,
}

func (x ApplicationOpeType) String() string {
	return proto.EnumName(ApplicationOpeType_name, int32(x))
}
func (ApplicationOpeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{2}
}

type TimeFilterUnit int32

const (
	TimeFilterUnit_BY_DAY   TimeFilterUnit = 0
	TimeFilterUnit_BY_WEEK  TimeFilterUnit = 1
	TimeFilterUnit_BY_MONTH TimeFilterUnit = 2
)

var TimeFilterUnit_name = map[int32]string{
	0: "BY_DAY",
	1: "BY_WEEK",
	2: "BY_MONTH",
}
var TimeFilterUnit_value = map[string]int32{
	"BY_DAY":   0,
	"BY_WEEK":  1,
	"BY_MONTH": 2,
}

func (x TimeFilterUnit) String() string {
	return proto.EnumName(TimeFilterUnit_name, int32(x))
}
func (TimeFilterUnit) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{3}
}

type GuildInfoType int32

const (
	GuildInfoType_GUILD_INSERT GuildInfoType = 0
	GuildInfoType_GUILD_DELETE GuildInfoType = 1
)

var GuildInfoType_name = map[int32]string{
	0: "GUILD_INSERT",
	1: "GUILD_DELETE",
}
var GuildInfoType_value = map[string]int32{
	"GUILD_INSERT": 0,
	"GUILD_DELETE": 1,
}

func (x GuildInfoType) String() string {
	return proto.EnumName(GuildInfoType_name, int32(x))
}
func (GuildInfoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{4}
}

type IncomeType int32

const (
	IncomeType_NONE_INDEX               IncomeType = 0
	IncomeType_GAME_RECHARGE_REBATE     IncomeType = 1
	IncomeType_GUILD_SYS_TASK           IncomeType = 2
	IncomeType_GUILD_OPE_TASK           IncomeType = 3
	IncomeType_SETTLEMENT               IncomeType = 4
	IncomeType_GUILD_WELFARE_FIRST_JOIN IncomeType = 5
	IncomeType_CHANNEL_SEND_GIFT        IncomeType = 6
	IncomeType_CHANNEL_SETTLEMENT       IncomeType = 7
)

var IncomeType_name = map[int32]string{
	0: "NONE_INDEX",
	1: "GAME_RECHARGE_REBATE",
	2: "GUILD_SYS_TASK",
	3: "GUILD_OPE_TASK",
	4: "SETTLEMENT",
	5: "GUILD_WELFARE_FIRST_JOIN",
	6: "CHANNEL_SEND_GIFT",
	7: "CHANNEL_SETTLEMENT",
}
var IncomeType_value = map[string]int32{
	"NONE_INDEX":               0,
	"GAME_RECHARGE_REBATE":     1,
	"GUILD_SYS_TASK":           2,
	"GUILD_OPE_TASK":           3,
	"SETTLEMENT":               4,
	"GUILD_WELFARE_FIRST_JOIN": 5,
	"CHANNEL_SEND_GIFT":        6,
	"CHANNEL_SETTLEMENT":       7,
}

func (x IncomeType) String() string {
	return proto.EnumName(IncomeType_name, int32(x))
}
func (IncomeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{5}
}

type RangeType int32

const (
	RangeType_DAY_RANGE_TYPE   RangeType = 0
	RangeType_MONTH_RANGE_TYPE RangeType = 1
)

var RangeType_name = map[int32]string{
	0: "DAY_RANGE_TYPE",
	1: "MONTH_RANGE_TYPE",
}
var RangeType_value = map[string]int32{
	"DAY_RANGE_TYPE":   0,
	"MONTH_RANGE_TYPE": 1,
}

func (x RangeType) String() string {
	return proto.EnumName(RangeType_name, int32(x))
}
func (RangeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{6}
}

type QueryType int32

const (
	QueryType_NONE_TYPE     QueryType = 0
	QueryType_ROOM_ID_TYPE  QueryType = 1
	QueryType_PAID_UID_TYPE QueryType = 2
)

var QueryType_name = map[int32]string{
	0: "NONE_TYPE",
	1: "ROOM_ID_TYPE",
	2: "PAID_UID_TYPE",
}
var QueryType_value = map[string]int32{
	"NONE_TYPE":     0,
	"ROOM_ID_TYPE":  1,
	"PAID_UID_TYPE": 2,
}

func (x QueryType) String() string {
	return proto.EnumName(QueryType_name, int32(x))
}
func (QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{7}
}

type DbOpeType int32

const (
	DbOpeType_Db_INSERT DbOpeType = 0
	DbOpeType_Db_DELETE DbOpeType = 1
)

var DbOpeType_name = map[int32]string{
	0: "Db_INSERT",
	1: "Db_DELETE",
}
var DbOpeType_value = map[string]int32{
	"Db_INSERT": 0,
	"Db_DELETE": 1,
}

func (x DbOpeType) String() string {
	return proto.EnumName(DbOpeType_name, int32(x))
}
func (DbOpeType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{8}
}

// 考核目的
type ExamPurpose int32

const (
	ExamPurpose_FOR_DEFAULT         ExamPurpose = 0
	ExamPurpose_FOR_APPLY_PGC_FIRST ExamPurpose = 1
	ExamPurpose_FOR_MODIFY_TAG      ExamPurpose = 2
	ExamPurpose_FOR_ADD_PGC         ExamPurpose = 3
)

var ExamPurpose_name = map[int32]string{
	0: "FOR_DEFAULT",
	1: "FOR_APPLY_PGC_FIRST",
	2: "FOR_MODIFY_TAG",
	3: "FOR_ADD_PGC",
}
var ExamPurpose_value = map[string]int32{
	"FOR_DEFAULT":         0,
	"FOR_APPLY_PGC_FIRST": 1,
	"FOR_MODIFY_TAG":      2,
	"FOR_ADD_PGC":         3,
}

func (x ExamPurpose) String() string {
	return proto.EnumName(ExamPurpose_name, int32(x))
}
func (ExamPurpose) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{9}
}

// 操作
type ExamStatus int32

const (
	ExamStatus_EXAM_DEFAULT  ExamStatus = 0
	ExamStatus_EXAM_PASS     ExamStatus = 1
	ExamStatus_EXAM_NOT_PASS ExamStatus = 2
)

var ExamStatus_name = map[int32]string{
	0: "EXAM_DEFAULT",
	1: "EXAM_PASS",
	2: "EXAM_NOT_PASS",
}
var ExamStatus_value = map[string]int32{
	"EXAM_DEFAULT":  0,
	"EXAM_PASS":     1,
	"EXAM_NOT_PASS": 2,
}

func (x ExamStatus) String() string {
	return proto.EnumName(ExamStatus_name, int32(x))
}
func (ExamStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{10}
}

// 操作类型
type ExamOperationType int32

const (
	ExamOperationType_OPERATION_DEFAULT     ExamOperationType = 0
	ExamOperationType_OPERATION_APPLICATION ExamOperationType = 1
	ExamOperationType_OPERATION_RECORD      ExamOperationType = 2
)

var ExamOperationType_name = map[int32]string{
	0: "OPERATION_DEFAULT",
	1: "OPERATION_APPLICATION",
	2: "OPERATION_RECORD",
}
var ExamOperationType_value = map[string]int32{
	"OPERATION_DEFAULT":     0,
	"OPERATION_APPLICATION": 1,
	"OPERATION_RECORD":      2,
}

func (x ExamOperationType) String() string {
	return proto.EnumName(ExamOperationType_name, int32(x))
}
func (ExamOperationType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{11}
}

// 获取考核标签
type ExamTagType int32

const (
	ExamTagType_TAG_DEFAULT   ExamTagType = 0
	ExamTagType_TAG_RECOMMEND ExamTagType = 1
	ExamTagType_TAG_YUYIN     ExamTagType = 2
)

var ExamTagType_name = map[int32]string{
	0: "TAG_DEFAULT",
	1: "TAG_RECOMMEND",
	2: "TAG_YUYIN",
}
var ExamTagType_value = map[string]int32{
	"TAG_DEFAULT":   0,
	"TAG_RECOMMEND": 1,
	"TAG_YUYIN":     2,
}

func (x ExamTagType) String() string {
	return proto.EnumName(ExamTagType_name, int32(x))
}
func (ExamTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{12}
}

// 语音直播
type YuyinExamStatus int32

const (
	YuyinExamStatus_YE_DEFAULT   YuyinExamStatus = 0
	YuyinExamStatus_YE_COMMITTED YuyinExamStatus = 1
	YuyinExamStatus_YE_REJECTED  YuyinExamStatus = 2
)

var YuyinExamStatus_name = map[int32]string{
	0: "YE_DEFAULT",
	1: "YE_COMMITTED",
	2: "YE_REJECTED",
}
var YuyinExamStatus_value = map[string]int32{
	"YE_DEFAULT":   0,
	"YE_COMMITTED": 1,
	"YE_REJECTED":  2,
}

func (x YuyinExamStatus) String() string {
	return proto.EnumName(YuyinExamStatus_name, int32(x))
}
func (YuyinExamStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{13}
}

type GuildApplicationInfo struct {
	GuildId               uint32             `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ApplicationType       uint32             `protobuf:"varint,2,opt,name=application_type,json=applicationType,proto3" json:"application_type,omitempty"`
	PlatformName          string             `protobuf:"bytes,3,opt,name=platform_name,json=platformName,proto3" json:"platform_name,omitempty"`
	PlatformContent       string             `protobuf:"bytes,4,opt,name=platform_content,json=platformContent,proto3" json:"platform_content,omitempty"`
	PlatformId            uint32             `protobuf:"varint,5,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	PlatformCertification []string           `protobuf:"bytes,6,rep,name=platform_certification,json=platformCertification,proto3" json:"platform_certification,omitempty"`
	Desc                  string             `protobuf:"bytes,7,opt,name=desc,proto3" json:"desc,omitempty"`
	Operation             ApplicationOpeType `protobuf:"varint,8,opt,name=operation,proto3,enum=golddiamonn.ApplicationOpeType" json:"operation,omitempty"`
	CreateTime            int64              `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	OpePeople             string             `protobuf:"bytes,10,opt,name=ope_people,json=opePeople,proto3" json:"ope_people,omitempty"`
	ReqCnt                uint32             `protobuf:"varint,11,opt,name=req_cnt,json=reqCnt,proto3" json:"req_cnt,omitempty"`
	PlatformIdStr         string             `protobuf:"bytes,12,opt,name=platform_id_str,json=platformIdStr,proto3" json:"platform_id_str,omitempty"`
	LibraryType           uint32             `protobuf:"varint,13,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	Ttguild               uint32             `protobuf:"varint,14,opt,name=ttguild,proto3" json:"ttguild,omitempty"`
	PlatformGuildType     uint32             `protobuf:"varint,15,opt,name=platform_guild_type,json=platformGuildType,proto3" json:"platform_guild_type,omitempty"`
	PlatformGuildid       string             `protobuf:"bytes,16,opt,name=platform_guildid,json=platformGuildid,proto3" json:"platform_guildid,omitempty"`
	PlatformGuildOwnerId  string             `protobuf:"bytes,17,opt,name=platform_guild_ownerId,json=platformGuildOwnerId,proto3" json:"platform_guild_ownerId,omitempty"`
	PlatformGuildAuthimgs []string           `protobuf:"bytes,18,rep,name=platform_guild_authimgs,json=platformGuildAuthimgs,proto3" json:"platform_guild_authimgs,omitempty"`
	EstablishTime         string             `protobuf:"bytes,19,opt,name=establish_time,json=establishTime,proto3" json:"establish_time,omitempty"`
	Operate               *OperateInfoT      `protobuf:"bytes,20,opt,name=operate,proto3" json:"operate,omitempty"`
	Estimates             uint32             `protobuf:"varint,21,opt,name=estimates,proto3" json:"estimates,omitempty"`
	Contact               string             `protobuf:"bytes,22,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}           `json:"-"`
	XXX_unrecognized      []byte             `json:"-"`
	XXX_sizecache         int32              `json:"-"`
}

func (m *GuildApplicationInfo) Reset()         { *m = GuildApplicationInfo{} }
func (m *GuildApplicationInfo) String() string { return proto.CompactTextString(m) }
func (*GuildApplicationInfo) ProtoMessage()    {}
func (*GuildApplicationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{0}
}
func (m *GuildApplicationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildApplicationInfo.Unmarshal(m, b)
}
func (m *GuildApplicationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildApplicationInfo.Marshal(b, m, deterministic)
}
func (dst *GuildApplicationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildApplicationInfo.Merge(dst, src)
}
func (m *GuildApplicationInfo) XXX_Size() int {
	return xxx_messageInfo_GuildApplicationInfo.Size(m)
}
func (m *GuildApplicationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildApplicationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildApplicationInfo proto.InternalMessageInfo

func (m *GuildApplicationInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildApplicationInfo) GetApplicationType() uint32 {
	if m != nil {
		return m.ApplicationType
	}
	return 0
}

func (m *GuildApplicationInfo) GetPlatformName() string {
	if m != nil {
		return m.PlatformName
	}
	return ""
}

func (m *GuildApplicationInfo) GetPlatformContent() string {
	if m != nil {
		return m.PlatformContent
	}
	return ""
}

func (m *GuildApplicationInfo) GetPlatformId() uint32 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

func (m *GuildApplicationInfo) GetPlatformCertification() []string {
	if m != nil {
		return m.PlatformCertification
	}
	return nil
}

func (m *GuildApplicationInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *GuildApplicationInfo) GetOperation() ApplicationOpeType {
	if m != nil {
		return m.Operation
	}
	return ApplicationOpeType_APPLICATION_INIT
}

func (m *GuildApplicationInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *GuildApplicationInfo) GetOpePeople() string {
	if m != nil {
		return m.OpePeople
	}
	return ""
}

func (m *GuildApplicationInfo) GetReqCnt() uint32 {
	if m != nil {
		return m.ReqCnt
	}
	return 0
}

func (m *GuildApplicationInfo) GetPlatformIdStr() string {
	if m != nil {
		return m.PlatformIdStr
	}
	return ""
}

func (m *GuildApplicationInfo) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

func (m *GuildApplicationInfo) GetTtguild() uint32 {
	if m != nil {
		return m.Ttguild
	}
	return 0
}

func (m *GuildApplicationInfo) GetPlatformGuildType() uint32 {
	if m != nil {
		return m.PlatformGuildType
	}
	return 0
}

func (m *GuildApplicationInfo) GetPlatformGuildid() string {
	if m != nil {
		return m.PlatformGuildid
	}
	return ""
}

func (m *GuildApplicationInfo) GetPlatformGuildOwnerId() string {
	if m != nil {
		return m.PlatformGuildOwnerId
	}
	return ""
}

func (m *GuildApplicationInfo) GetPlatformGuildAuthimgs() []string {
	if m != nil {
		return m.PlatformGuildAuthimgs
	}
	return nil
}

func (m *GuildApplicationInfo) GetEstablishTime() string {
	if m != nil {
		return m.EstablishTime
	}
	return ""
}

func (m *GuildApplicationInfo) GetOperate() *OperateInfoT {
	if m != nil {
		return m.Operate
	}
	return nil
}

func (m *GuildApplicationInfo) GetEstimates() uint32 {
	if m != nil {
		return m.Estimates
	}
	return 0
}

func (m *GuildApplicationInfo) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

// 申请加入会长服务号的记录
type GuildApplicationReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IsRecord             bool     `protobuf:"varint,2,opt,name=is_record,json=isRecord,proto3" json:"is_record,omitempty"`
	LibraryType          uint32   `protobuf:"varint,3,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildApplicationReq) Reset()         { *m = GuildApplicationReq{} }
func (m *GuildApplicationReq) String() string { return proto.CompactTextString(m) }
func (*GuildApplicationReq) ProtoMessage()    {}
func (*GuildApplicationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{1}
}
func (m *GuildApplicationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildApplicationReq.Unmarshal(m, b)
}
func (m *GuildApplicationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildApplicationReq.Marshal(b, m, deterministic)
}
func (dst *GuildApplicationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildApplicationReq.Merge(dst, src)
}
func (m *GuildApplicationReq) XXX_Size() int {
	return xxx_messageInfo_GuildApplicationReq.Size(m)
}
func (m *GuildApplicationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildApplicationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildApplicationReq proto.InternalMessageInfo

func (m *GuildApplicationReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildApplicationReq) GetIsRecord() bool {
	if m != nil {
		return m.IsRecord
	}
	return false
}

func (m *GuildApplicationReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type GuildApplicationRsp struct {
	Info                 *GuildApplicationInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GuildApplicationRsp) Reset()         { *m = GuildApplicationRsp{} }
func (m *GuildApplicationRsp) String() string { return proto.CompactTextString(m) }
func (*GuildApplicationRsp) ProtoMessage()    {}
func (*GuildApplicationRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{2}
}
func (m *GuildApplicationRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildApplicationRsp.Unmarshal(m, b)
}
func (m *GuildApplicationRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildApplicationRsp.Marshal(b, m, deterministic)
}
func (dst *GuildApplicationRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildApplicationRsp.Merge(dst, src)
}
func (m *GuildApplicationRsp) XXX_Size() int {
	return xxx_messageInfo_GuildApplicationRsp.Size(m)
}
func (m *GuildApplicationRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildApplicationRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildApplicationRsp proto.InternalMessageInfo

func (m *GuildApplicationRsp) GetInfo() *GuildApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 列表
type ApplicationListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	IsRecord             bool     `protobuf:"varint,3,opt,name=is_record,json=isRecord,proto3" json:"is_record,omitempty"`
	LibraryType          uint32   `protobuf:"varint,4,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationListReq) Reset()         { *m = ApplicationListReq{} }
func (m *ApplicationListReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationListReq) ProtoMessage()    {}
func (*ApplicationListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{3}
}
func (m *ApplicationListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationListReq.Unmarshal(m, b)
}
func (m *ApplicationListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationListReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationListReq.Merge(dst, src)
}
func (m *ApplicationListReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationListReq.Size(m)
}
func (m *ApplicationListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationListReq proto.InternalMessageInfo

func (m *ApplicationListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ApplicationListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ApplicationListReq) GetIsRecord() bool {
	if m != nil {
		return m.IsRecord
	}
	return false
}

func (m *ApplicationListReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type ApplicationListRsp struct {
	Infos                []*GuildApplicationInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	ApplicationCnt       uint32                  `protobuf:"varint,2,opt,name=application_cnt,json=applicationCnt,proto3" json:"application_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ApplicationListRsp) Reset()         { *m = ApplicationListRsp{} }
func (m *ApplicationListRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationListRsp) ProtoMessage()    {}
func (*ApplicationListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{4}
}
func (m *ApplicationListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationListRsp.Unmarshal(m, b)
}
func (m *ApplicationListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationListRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationListRsp.Merge(dst, src)
}
func (m *ApplicationListRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationListRsp.Size(m)
}
func (m *ApplicationListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationListRsp proto.InternalMessageInfo

func (m *ApplicationListRsp) GetInfos() []*GuildApplicationInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *ApplicationListRsp) GetApplicationCnt() uint32 {
	if m != nil {
		return m.ApplicationCnt
	}
	return 0
}

// 同意，拒绝
type ApplicationOpeReq struct {
	Operation            ApplicationOpeType `protobuf:"varint,1,opt,name=operation,proto3,enum=golddiamonn.ApplicationOpeType" json:"operation,omitempty"`
	GuildId              uint32             `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Desc                 string             `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	OpePeople            string             `protobuf:"bytes,4,opt,name=ope_people,json=opePeople,proto3" json:"ope_people,omitempty"`
	LibraryType          uint32             `protobuf:"varint,5,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ApplicationOpeReq) Reset()         { *m = ApplicationOpeReq{} }
func (m *ApplicationOpeReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationOpeReq) ProtoMessage()    {}
func (*ApplicationOpeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{5}
}
func (m *ApplicationOpeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationOpeReq.Unmarshal(m, b)
}
func (m *ApplicationOpeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationOpeReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationOpeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationOpeReq.Merge(dst, src)
}
func (m *ApplicationOpeReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationOpeReq.Size(m)
}
func (m *ApplicationOpeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationOpeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationOpeReq proto.InternalMessageInfo

func (m *ApplicationOpeReq) GetOperation() ApplicationOpeType {
	if m != nil {
		return m.Operation
	}
	return ApplicationOpeType_APPLICATION_INIT
}

func (m *ApplicationOpeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplicationOpeReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ApplicationOpeReq) GetOpePeople() string {
	if m != nil {
		return m.OpePeople
	}
	return ""
}

func (m *ApplicationOpeReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type ApplicationOpeRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationOpeRsp) Reset()         { *m = ApplicationOpeRsp{} }
func (m *ApplicationOpeRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationOpeRsp) ProtoMessage()    {}
func (*ApplicationOpeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{6}
}
func (m *ApplicationOpeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationOpeRsp.Unmarshal(m, b)
}
func (m *ApplicationOpeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationOpeRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationOpeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationOpeRsp.Merge(dst, src)
}
func (m *ApplicationOpeRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationOpeRsp.Size(m)
}
func (m *ApplicationOpeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationOpeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationOpeRsp proto.InternalMessageInfo

func (m *ApplicationOpeRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ApplicationOpeRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type OperateInfoT struct {
	MonthIncome          string   `protobuf:"bytes,1,opt,name=month_income,json=monthIncome,proto3" json:"month_income,omitempty"`
	IncomeImgs           []string `protobuf:"bytes,2,rep,name=income_imgs,json=incomeImgs,proto3" json:"income_imgs,omitempty"`
	Consume              string   `protobuf:"bytes,3,opt,name=consume,proto3" json:"consume,omitempty"`
	ConsumeImgs          []string `protobuf:"bytes,4,rep,name=consume_imgs,json=consumeImgs,proto3" json:"consume_imgs,omitempty"`
	Teamsize             uint32   `protobuf:"varint,5,opt,name=teamsize,proto3" json:"teamsize,omitempty"`
	Accompanys           []string `protobuf:"bytes,6,rep,name=accompanys,proto3" json:"accompanys,omitempty"`
	EstimateTeams        uint32   `protobuf:"varint,7,opt,name=estimate_teams,json=estimateTeams,proto3" json:"estimate_teams,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OperateInfoT) Reset()         { *m = OperateInfoT{} }
func (m *OperateInfoT) String() string { return proto.CompactTextString(m) }
func (*OperateInfoT) ProtoMessage()    {}
func (*OperateInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{7}
}
func (m *OperateInfoT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OperateInfoT.Unmarshal(m, b)
}
func (m *OperateInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OperateInfoT.Marshal(b, m, deterministic)
}
func (dst *OperateInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OperateInfoT.Merge(dst, src)
}
func (m *OperateInfoT) XXX_Size() int {
	return xxx_messageInfo_OperateInfoT.Size(m)
}
func (m *OperateInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_OperateInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_OperateInfoT proto.InternalMessageInfo

func (m *OperateInfoT) GetMonthIncome() string {
	if m != nil {
		return m.MonthIncome
	}
	return ""
}

func (m *OperateInfoT) GetIncomeImgs() []string {
	if m != nil {
		return m.IncomeImgs
	}
	return nil
}

func (m *OperateInfoT) GetConsume() string {
	if m != nil {
		return m.Consume
	}
	return ""
}

func (m *OperateInfoT) GetConsumeImgs() []string {
	if m != nil {
		return m.ConsumeImgs
	}
	return nil
}

func (m *OperateInfoT) GetTeamsize() uint32 {
	if m != nil {
		return m.Teamsize
	}
	return 0
}

func (m *OperateInfoT) GetAccompanys() []string {
	if m != nil {
		return m.Accompanys
	}
	return nil
}

func (m *OperateInfoT) GetEstimateTeams() uint32 {
	if m != nil {
		return m.EstimateTeams
	}
	return 0
}

type ApplicationCreateReq struct {
	GuildId               uint32        `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ApplicationType       uint32        `protobuf:"varint,2,opt,name=application_type,json=applicationType,proto3" json:"application_type,omitempty"`
	PlatformName          string        `protobuf:"bytes,3,opt,name=platform_name,json=platformName,proto3" json:"platform_name,omitempty"`
	PlatformContent       string        `protobuf:"bytes,4,opt,name=platform_content,json=platformContent,proto3" json:"platform_content,omitempty"`
	PlatformId            uint32        `protobuf:"varint,5,opt,name=platform_id,json=platformId,proto3" json:"platform_id,omitempty"`
	PlatformCertification []string      `protobuf:"bytes,6,rep,name=platform_certification,json=platformCertification,proto3" json:"platform_certification,omitempty"`
	PlatformIdStr         string        `protobuf:"bytes,7,opt,name=platform_id_str,json=platformIdStr,proto3" json:"platform_id_str,omitempty"`
	LibraryType           uint32        `protobuf:"varint,8,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	Ttguild               uint32        `protobuf:"varint,9,opt,name=ttguild,proto3" json:"ttguild,omitempty"`
	PlatformGuildType     uint32        `protobuf:"varint,10,opt,name=platform_guild_type,json=platformGuildType,proto3" json:"platform_guild_type,omitempty"`
	PlatformGuildid       string        `protobuf:"bytes,11,opt,name=platform_guildid,json=platformGuildid,proto3" json:"platform_guildid,omitempty"`
	PlatformGuildOwnerId  string        `protobuf:"bytes,12,opt,name=platform_guild_ownerId,json=platformGuildOwnerId,proto3" json:"platform_guild_ownerId,omitempty"`
	PlatformGuildAuthimgs []string      `protobuf:"bytes,13,rep,name=platform_guild_authimgs,json=platformGuildAuthimgs,proto3" json:"platform_guild_authimgs,omitempty"`
	EstablishTime         string        `protobuf:"bytes,14,opt,name=establish_time,json=establishTime,proto3" json:"establish_time,omitempty"`
	Operate               *OperateInfoT `protobuf:"bytes,15,opt,name=operate,proto3" json:"operate,omitempty"`
	Estimates             uint32        `protobuf:"varint,16,opt,name=estimates,proto3" json:"estimates,omitempty"`
	Contact               string        `protobuf:"bytes,17,opt,name=contact,proto3" json:"contact,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}      `json:"-"`
	XXX_unrecognized      []byte        `json:"-"`
	XXX_sizecache         int32         `json:"-"`
}

func (m *ApplicationCreateReq) Reset()         { *m = ApplicationCreateReq{} }
func (m *ApplicationCreateReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationCreateReq) ProtoMessage()    {}
func (*ApplicationCreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{8}
}
func (m *ApplicationCreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationCreateReq.Unmarshal(m, b)
}
func (m *ApplicationCreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationCreateReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationCreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationCreateReq.Merge(dst, src)
}
func (m *ApplicationCreateReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationCreateReq.Size(m)
}
func (m *ApplicationCreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationCreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationCreateReq proto.InternalMessageInfo

func (m *ApplicationCreateReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplicationCreateReq) GetApplicationType() uint32 {
	if m != nil {
		return m.ApplicationType
	}
	return 0
}

func (m *ApplicationCreateReq) GetPlatformName() string {
	if m != nil {
		return m.PlatformName
	}
	return ""
}

func (m *ApplicationCreateReq) GetPlatformContent() string {
	if m != nil {
		return m.PlatformContent
	}
	return ""
}

func (m *ApplicationCreateReq) GetPlatformId() uint32 {
	if m != nil {
		return m.PlatformId
	}
	return 0
}

func (m *ApplicationCreateReq) GetPlatformCertification() []string {
	if m != nil {
		return m.PlatformCertification
	}
	return nil
}

func (m *ApplicationCreateReq) GetPlatformIdStr() string {
	if m != nil {
		return m.PlatformIdStr
	}
	return ""
}

func (m *ApplicationCreateReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

func (m *ApplicationCreateReq) GetTtguild() uint32 {
	if m != nil {
		return m.Ttguild
	}
	return 0
}

func (m *ApplicationCreateReq) GetPlatformGuildType() uint32 {
	if m != nil {
		return m.PlatformGuildType
	}
	return 0
}

func (m *ApplicationCreateReq) GetPlatformGuildid() string {
	if m != nil {
		return m.PlatformGuildid
	}
	return ""
}

func (m *ApplicationCreateReq) GetPlatformGuildOwnerId() string {
	if m != nil {
		return m.PlatformGuildOwnerId
	}
	return ""
}

func (m *ApplicationCreateReq) GetPlatformGuildAuthimgs() []string {
	if m != nil {
		return m.PlatformGuildAuthimgs
	}
	return nil
}

func (m *ApplicationCreateReq) GetEstablishTime() string {
	if m != nil {
		return m.EstablishTime
	}
	return ""
}

func (m *ApplicationCreateReq) GetOperate() *OperateInfoT {
	if m != nil {
		return m.Operate
	}
	return nil
}

func (m *ApplicationCreateReq) GetEstimates() uint32 {
	if m != nil {
		return m.Estimates
	}
	return 0
}

func (m *ApplicationCreateReq) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

type ApplicationCreateRsp struct {
	IsApp                bool     `protobuf:"varint,1,opt,name=is_app,json=isApp,proto3" json:"is_app,omitempty"`
	IsLimit              bool     `protobuf:"varint,2,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	LimitEndTime         int64    `protobuf:"varint,3,opt,name=limit_end_time,json=limitEndTime,proto3" json:"limit_end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationCreateRsp) Reset()         { *m = ApplicationCreateRsp{} }
func (m *ApplicationCreateRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationCreateRsp) ProtoMessage()    {}
func (*ApplicationCreateRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{9}
}
func (m *ApplicationCreateRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationCreateRsp.Unmarshal(m, b)
}
func (m *ApplicationCreateRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationCreateRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationCreateRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationCreateRsp.Merge(dst, src)
}
func (m *ApplicationCreateRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationCreateRsp.Size(m)
}
func (m *ApplicationCreateRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationCreateRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationCreateRsp proto.InternalMessageInfo

func (m *ApplicationCreateRsp) GetIsApp() bool {
	if m != nil {
		return m.IsApp
	}
	return false
}

func (m *ApplicationCreateRsp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *ApplicationCreateRsp) GetLimitEndTime() int64 {
	if m != nil {
		return m.LimitEndTime
	}
	return 0
}

// 查询
type GuildInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	LibraryType          uint32   `protobuf:"varint,2,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInfoReq) Reset()         { *m = GuildInfoReq{} }
func (m *GuildInfoReq) String() string { return proto.CompactTextString(m) }
func (*GuildInfoReq) ProtoMessage()    {}
func (*GuildInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{10}
}
func (m *GuildInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInfoReq.Unmarshal(m, b)
}
func (m *GuildInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInfoReq.Marshal(b, m, deterministic)
}
func (dst *GuildInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInfoReq.Merge(dst, src)
}
func (m *GuildInfoReq) XXX_Size() int {
	return xxx_messageInfo_GuildInfoReq.Size(m)
}
func (m *GuildInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInfoReq proto.InternalMessageInfo

func (m *GuildInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildInfoReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type GuildInfoRsp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildType            uint32   `protobuf:"varint,2,opt,name=guild_type,json=guildType,proto3" json:"guild_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInfoRsp) Reset()         { *m = GuildInfoRsp{} }
func (m *GuildInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GuildInfoRsp) ProtoMessage()    {}
func (*GuildInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{11}
}
func (m *GuildInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInfoRsp.Unmarshal(m, b)
}
func (m *GuildInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GuildInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInfoRsp.Merge(dst, src)
}
func (m *GuildInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GuildInfoRsp.Size(m)
}
func (m *GuildInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInfoRsp proto.InternalMessageInfo

func (m *GuildInfoRsp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildInfoRsp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

type GuildListReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LibraryType          uint32   `protobuf:"varint,3,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildListReq) Reset()         { *m = GuildListReq{} }
func (m *GuildListReq) String() string { return proto.CompactTextString(m) }
func (*GuildListReq) ProtoMessage()    {}
func (*GuildListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{12}
}
func (m *GuildListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildListReq.Unmarshal(m, b)
}
func (m *GuildListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildListReq.Marshal(b, m, deterministic)
}
func (dst *GuildListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildListReq.Merge(dst, src)
}
func (m *GuildListReq) XXX_Size() int {
	return xxx_messageInfo_GuildListReq.Size(m)
}
func (m *GuildListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildListReq proto.InternalMessageInfo

func (m *GuildListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GuildListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GuildListReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type GuildListRsp struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	GuildCnt             uint32   `protobuf:"varint,2,opt,name=guild_cnt,json=guildCnt,proto3" json:"guild_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildListRsp) Reset()         { *m = GuildListRsp{} }
func (m *GuildListRsp) String() string { return proto.CompactTextString(m) }
func (*GuildListRsp) ProtoMessage()    {}
func (*GuildListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{13}
}
func (m *GuildListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildListRsp.Unmarshal(m, b)
}
func (m *GuildListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildListRsp.Marshal(b, m, deterministic)
}
func (dst *GuildListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildListRsp.Merge(dst, src)
}
func (m *GuildListRsp) XXX_Size() int {
	return xxx_messageInfo_GuildListRsp.Size(m)
}
func (m *GuildListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildListRsp proto.InternalMessageInfo

func (m *GuildListRsp) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GuildListRsp) GetGuildCnt() uint32 {
	if m != nil {
		return m.GuildCnt
	}
	return 0
}

// 插入，删除
type GuildInfoOpeReq struct {
	Operation            GuildInfoType `protobuf:"varint,1,opt,name=operation,proto3,enum=golddiamonn.GuildInfoType" json:"operation,omitempty"`
	GuildId              uint32        `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildType            uint32        `protobuf:"varint,3,opt,name=guild_type,json=guildType,proto3" json:"guild_type,omitempty"`
	LibraryType          uint32        `protobuf:"varint,4,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GuildInfoOpeReq) Reset()         { *m = GuildInfoOpeReq{} }
func (m *GuildInfoOpeReq) String() string { return proto.CompactTextString(m) }
func (*GuildInfoOpeReq) ProtoMessage()    {}
func (*GuildInfoOpeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{14}
}
func (m *GuildInfoOpeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInfoOpeReq.Unmarshal(m, b)
}
func (m *GuildInfoOpeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInfoOpeReq.Marshal(b, m, deterministic)
}
func (dst *GuildInfoOpeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInfoOpeReq.Merge(dst, src)
}
func (m *GuildInfoOpeReq) XXX_Size() int {
	return xxx_messageInfo_GuildInfoOpeReq.Size(m)
}
func (m *GuildInfoOpeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInfoOpeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInfoOpeReq proto.InternalMessageInfo

func (m *GuildInfoOpeReq) GetOperation() GuildInfoType {
	if m != nil {
		return m.Operation
	}
	return GuildInfoType_GUILD_INSERT
}

func (m *GuildInfoOpeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildInfoOpeReq) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GuildInfoOpeReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type GuildInfoOpeRsp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInfoOpeRsp) Reset()         { *m = GuildInfoOpeRsp{} }
func (m *GuildInfoOpeRsp) String() string { return proto.CompactTextString(m) }
func (*GuildInfoOpeRsp) ProtoMessage()    {}
func (*GuildInfoOpeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{15}
}
func (m *GuildInfoOpeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInfoOpeRsp.Unmarshal(m, b)
}
func (m *GuildInfoOpeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInfoOpeRsp.Marshal(b, m, deterministic)
}
func (dst *GuildInfoOpeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInfoOpeRsp.Merge(dst, src)
}
func (m *GuildInfoOpeRsp) XXX_Size() int {
	return xxx_messageInfo_GuildInfoOpeRsp.Size(m)
}
func (m *GuildInfoOpeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInfoOpeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInfoOpeRsp proto.InternalMessageInfo

func (m *GuildInfoOpeRsp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type AwardDiamondReq struct {
	GuildId              uint32     `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	PaidUid              uint32     `protobuf:"varint,2,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	BoughtTime           uint64     `protobuf:"varint,3,opt,name=bought_time,json=boughtTime,proto3" json:"bought_time,omitempty"`
	SourceType           IncomeType `protobuf:"varint,4,opt,name=source_type,json=sourceType,proto3,enum=golddiamonn.IncomeType" json:"source_type,omitempty"`
	Income               int64      `protobuf:"varint,5,opt,name=income,proto3" json:"income,omitempty"`
	OrderId              string     `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	UniqSign             string     `protobuf:"bytes,7,opt,name=uniq_sign,json=uniqSign,proto3" json:"uniq_sign,omitempty"`
	Desc                 string     `protobuf:"bytes,8,opt,name=desc,proto3" json:"desc,omitempty"`
	Extand               string     `protobuf:"bytes,9,opt,name=extand,proto3" json:"extand,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AwardDiamondReq) Reset()         { *m = AwardDiamondReq{} }
func (m *AwardDiamondReq) String() string { return proto.CompactTextString(m) }
func (*AwardDiamondReq) ProtoMessage()    {}
func (*AwardDiamondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{16}
}
func (m *AwardDiamondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardDiamondReq.Unmarshal(m, b)
}
func (m *AwardDiamondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardDiamondReq.Marshal(b, m, deterministic)
}
func (dst *AwardDiamondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardDiamondReq.Merge(dst, src)
}
func (m *AwardDiamondReq) XXX_Size() int {
	return xxx_messageInfo_AwardDiamondReq.Size(m)
}
func (m *AwardDiamondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardDiamondReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardDiamondReq proto.InternalMessageInfo

func (m *AwardDiamondReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AwardDiamondReq) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *AwardDiamondReq) GetBoughtTime() uint64 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

func (m *AwardDiamondReq) GetSourceType() IncomeType {
	if m != nil {
		return m.SourceType
	}
	return IncomeType_NONE_INDEX
}

func (m *AwardDiamondReq) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *AwardDiamondReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *AwardDiamondReq) GetUniqSign() string {
	if m != nil {
		return m.UniqSign
	}
	return ""
}

func (m *AwardDiamondReq) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *AwardDiamondReq) GetExtand() string {
	if m != nil {
		return m.Extand
	}
	return ""
}

type AwardDiamondRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardDiamondRsp) Reset()         { *m = AwardDiamondRsp{} }
func (m *AwardDiamondRsp) String() string { return proto.CompactTextString(m) }
func (*AwardDiamondRsp) ProtoMessage()    {}
func (*AwardDiamondRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{17}
}
func (m *AwardDiamondRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardDiamondRsp.Unmarshal(m, b)
}
func (m *AwardDiamondRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardDiamondRsp.Marshal(b, m, deterministic)
}
func (dst *AwardDiamondRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardDiamondRsp.Merge(dst, src)
}
func (m *AwardDiamondRsp) XXX_Size() int {
	return xxx_messageInfo_AwardDiamondRsp.Size(m)
}
func (m *AwardDiamondRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardDiamondRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardDiamondRsp proto.InternalMessageInfo

type GoldRoomsReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoldRoomsReq) Reset()         { *m = GoldRoomsReq{} }
func (m *GoldRoomsReq) String() string { return proto.CompactTextString(m) }
func (*GoldRoomsReq) ProtoMessage()    {}
func (*GoldRoomsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{18}
}
func (m *GoldRoomsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoldRoomsReq.Unmarshal(m, b)
}
func (m *GoldRoomsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoldRoomsReq.Marshal(b, m, deterministic)
}
func (dst *GoldRoomsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoldRoomsReq.Merge(dst, src)
}
func (m *GoldRoomsReq) XXX_Size() int {
	return xxx_messageInfo_GoldRoomsReq.Size(m)
}
func (m *GoldRoomsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GoldRoomsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GoldRoomsReq proto.InternalMessageInfo

func (m *GoldRoomsReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GoldRoomsRsp struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GoldRoomsRsp) Reset()         { *m = GoldRoomsRsp{} }
func (m *GoldRoomsRsp) String() string { return proto.CompactTextString(m) }
func (*GoldRoomsRsp) ProtoMessage()    {}
func (*GoldRoomsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{19}
}
func (m *GoldRoomsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GoldRoomsRsp.Unmarshal(m, b)
}
func (m *GoldRoomsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GoldRoomsRsp.Marshal(b, m, deterministic)
}
func (dst *GoldRoomsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GoldRoomsRsp.Merge(dst, src)
}
func (m *GoldRoomsRsp) XXX_Size() int {
	return xxx_messageInfo_GoldRoomsRsp.Size(m)
}
func (m *GoldRoomsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GoldRoomsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GoldRoomsRsp proto.InternalMessageInfo

func (m *GoldRoomsRsp) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GuildInitInfoReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildInitInfoReq) Reset()         { *m = GuildInitInfoReq{} }
func (m *GuildInitInfoReq) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoReq) ProtoMessage()    {}
func (*GuildInitInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{20}
}
func (m *GuildInitInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoReq.Unmarshal(m, b)
}
func (m *GuildInitInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoReq.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoReq.Merge(dst, src)
}
func (m *GuildInitInfoReq) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoReq.Size(m)
}
func (m *GuildInitInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoReq proto.InternalMessageInfo

func (m *GuildInitInfoReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type DayTrendInfo struct {
	Day                  uint32   `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	Income               int64    `protobuf:"varint,2,opt,name=income,proto3" json:"income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTrendInfo) Reset()         { *m = DayTrendInfo{} }
func (m *DayTrendInfo) String() string { return proto.CompactTextString(m) }
func (*DayTrendInfo) ProtoMessage()    {}
func (*DayTrendInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{21}
}
func (m *DayTrendInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTrendInfo.Unmarshal(m, b)
}
func (m *DayTrendInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTrendInfo.Marshal(b, m, deterministic)
}
func (dst *DayTrendInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTrendInfo.Merge(dst, src)
}
func (m *DayTrendInfo) XXX_Size() int {
	return xxx_messageInfo_DayTrendInfo.Size(m)
}
func (m *DayTrendInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTrendInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayTrendInfo proto.InternalMessageInfo

func (m *DayTrendInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *DayTrendInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

type RoomIdFee struct {
	Roomid               uint32   `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Fee                  int64    `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	MonthTonowQoq        float32  `protobuf:"fixed32,3,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomIdFee) Reset()         { *m = RoomIdFee{} }
func (m *RoomIdFee) String() string { return proto.CompactTextString(m) }
func (*RoomIdFee) ProtoMessage()    {}
func (*RoomIdFee) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{22}
}
func (m *RoomIdFee) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomIdFee.Unmarshal(m, b)
}
func (m *RoomIdFee) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomIdFee.Marshal(b, m, deterministic)
}
func (dst *RoomIdFee) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomIdFee.Merge(dst, src)
}
func (m *RoomIdFee) XXX_Size() int {
	return xxx_messageInfo_RoomIdFee.Size(m)
}
func (m *RoomIdFee) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomIdFee.DiscardUnknown(m)
}

var xxx_messageInfo_RoomIdFee proto.InternalMessageInfo

func (m *RoomIdFee) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *RoomIdFee) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *RoomIdFee) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

type GuildInitInfoRsp struct {
	TodayIncome          int64           `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	YestodayIncome       int64           `protobuf:"varint,2,opt,name=yestoday_income,json=yestodayIncome,proto3" json:"yestoday_income,omitempty"`
	ThismonthIncome      int64           `protobuf:"varint,3,opt,name=thismonth_income,json=thismonthIncome,proto3" json:"thismonth_income,omitempty"`
	LastmonthIncome      int64           `protobuf:"varint,4,opt,name=lastmonth_income,json=lastmonthIncome,proto3" json:"lastmonth_income,omitempty"`
	RoomidFees           []*RoomIdFee    `protobuf:"bytes,5,rep,name=roomid_fees,json=roomidFees,proto3" json:"roomid_fees,omitempty"`
	DayTrendInfo         []*DayTrendInfo `protobuf:"bytes,6,rep,name=day_trend_info,json=dayTrendInfo,proto3" json:"day_trend_info,omitempty"`
	ServerTime           int64           `protobuf:"varint,7,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	GuildType            uint32          `protobuf:"varint,8,opt,name=guild_type,json=guildType,proto3" json:"guild_type,omitempty"`
	DayQoq               float32         `protobuf:"fixed32,9,opt,name=day_qoq,json=dayQoq,proto3" json:"day_qoq,omitempty"`
	MonthQoq             float32         `protobuf:"fixed32,10,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq,omitempty"`
	IsApplication        bool            `protobuf:"varint,11,opt,name=is_application,json=isApplication,proto3" json:"is_application,omitempty"`
	LastdayQoq           float32         `protobuf:"fixed32,12,opt,name=lastday_qoq,json=lastdayQoq,proto3" json:"lastday_qoq,omitempty"`
	MonthTonowQoq        float32         `protobuf:"fixed32,13,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq,omitempty"`
	LastMonthSamePeriod  int64           `protobuf:"varint,14,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period,omitempty"`
	LastMonthDayList     []*DayTrendInfo `protobuf:"bytes,15,rep,name=last_month_day_list,json=lastMonthDayList,proto3" json:"last_month_day_list,omitempty"`
	MonthSixIncome       int64           `protobuf:"varint,16,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GuildInitInfoRsp) Reset()         { *m = GuildInitInfoRsp{} }
func (m *GuildInitInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GuildInitInfoRsp) ProtoMessage()    {}
func (*GuildInitInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{23}
}
func (m *GuildInitInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildInitInfoRsp.Unmarshal(m, b)
}
func (m *GuildInitInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildInitInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GuildInitInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildInitInfoRsp.Merge(dst, src)
}
func (m *GuildInitInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GuildInitInfoRsp.Size(m)
}
func (m *GuildInitInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildInitInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildInitInfoRsp proto.InternalMessageInfo

func (m *GuildInitInfoRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetYestodayIncome() int64 {
	if m != nil {
		return m.YestodayIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetThismonthIncome() int64 {
	if m != nil {
		return m.ThismonthIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastmonthIncome() int64 {
	if m != nil {
		return m.LastmonthIncome
	}
	return 0
}

func (m *GuildInitInfoRsp) GetRoomidFees() []*RoomIdFee {
	if m != nil {
		return m.RoomidFees
	}
	return nil
}

func (m *GuildInitInfoRsp) GetDayTrendInfo() []*DayTrendInfo {
	if m != nil {
		return m.DayTrendInfo
	}
	return nil
}

func (m *GuildInitInfoRsp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *GuildInitInfoRsp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GuildInitInfoRsp) GetDayQoq() float32 {
	if m != nil {
		return m.DayQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetIsApplication() bool {
	if m != nil {
		return m.IsApplication
	}
	return false
}

func (m *GuildInitInfoRsp) GetLastdayQoq() float32 {
	if m != nil {
		return m.LastdayQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastMonthSamePeriod() int64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *GuildInitInfoRsp) GetLastMonthDayList() []*DayTrendInfo {
	if m != nil {
		return m.LastMonthDayList
	}
	return nil
}

func (m *GuildInitInfoRsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

type GuildChannelIncomeReq struct {
	GuildId              uint32    `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	RangeType            RangeType `protobuf:"varint,4,opt,name=range_type,json=rangeType,proto3,enum=golddiamonn.RangeType" json:"range_type,omitempty"`
	BeginTime            int64     `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64     `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Roomid               uint32    `protobuf:"varint,7,opt,name=roomid,proto3" json:"roomid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GuildChannelIncomeReq) Reset()         { *m = GuildChannelIncomeReq{} }
func (m *GuildChannelIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GuildChannelIncomeReq) ProtoMessage()    {}
func (*GuildChannelIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{24}
}
func (m *GuildChannelIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildChannelIncomeReq.Unmarshal(m, b)
}
func (m *GuildChannelIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildChannelIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GuildChannelIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildChannelIncomeReq.Merge(dst, src)
}
func (m *GuildChannelIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GuildChannelIncomeReq.Size(m)
}
func (m *GuildChannelIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildChannelIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildChannelIncomeReq proto.InternalMessageInfo

func (m *GuildChannelIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildChannelIncomeReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GuildChannelIncomeReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GuildChannelIncomeReq) GetRangeType() RangeType {
	if m != nil {
		return m.RangeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *GuildChannelIncomeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GuildChannelIncomeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GuildChannelIncomeReq) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

type StatIncomeInfo struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members,omitempty"`
	Incomes              int64    `protobuf:"varint,3,opt,name=incomes,proto3" json:"incomes,omitempty"`
	Fees                 int64    `protobuf:"varint,4,opt,name=fees,proto3" json:"fees,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatIncomeInfo) Reset()         { *m = StatIncomeInfo{} }
func (m *StatIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*StatIncomeInfo) ProtoMessage()    {}
func (*StatIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{25}
}
func (m *StatIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatIncomeInfo.Unmarshal(m, b)
}
func (m *StatIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *StatIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatIncomeInfo.Merge(dst, src)
}
func (m *StatIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_StatIncomeInfo.Size(m)
}
func (m *StatIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StatIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StatIncomeInfo proto.InternalMessageInfo

func (m *StatIncomeInfo) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *StatIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *StatIncomeInfo) GetIncomes() int64 {
	if m != nil {
		return m.Incomes
	}
	return 0
}

func (m *StatIncomeInfo) GetFees() int64 {
	if m != nil {
		return m.Fees
	}
	return 0
}

type GuildChannelIncomeRsp struct {
	TotalFees            int64             `protobuf:"varint,1,opt,name=total_fees,json=totalFees,proto3" json:"total_fees,omitempty"`
	TotalIncomes         int64             `protobuf:"varint,2,opt,name=total_incomes,json=totalIncomes,proto3" json:"total_incomes,omitempty"`
	NextPage             bool              `protobuf:"varint,3,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	StatIncomeInfo       []*StatIncomeInfo `protobuf:"bytes,4,rep,name=stat_income_info,json=statIncomeInfo,proto3" json:"stat_income_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GuildChannelIncomeRsp) Reset()         { *m = GuildChannelIncomeRsp{} }
func (m *GuildChannelIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GuildChannelIncomeRsp) ProtoMessage()    {}
func (*GuildChannelIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{26}
}
func (m *GuildChannelIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildChannelIncomeRsp.Unmarshal(m, b)
}
func (m *GuildChannelIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildChannelIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GuildChannelIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildChannelIncomeRsp.Merge(dst, src)
}
func (m *GuildChannelIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GuildChannelIncomeRsp.Size(m)
}
func (m *GuildChannelIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildChannelIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildChannelIncomeRsp proto.InternalMessageInfo

func (m *GuildChannelIncomeRsp) GetTotalFees() int64 {
	if m != nil {
		return m.TotalFees
	}
	return 0
}

func (m *GuildChannelIncomeRsp) GetTotalIncomes() int64 {
	if m != nil {
		return m.TotalIncomes
	}
	return 0
}

func (m *GuildChannelIncomeRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

func (m *GuildChannelIncomeRsp) GetStatIncomeInfo() []*StatIncomeInfo {
	if m != nil {
		return m.StatIncomeInfo
	}
	return nil
}

type GuildTodayIncomeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildTodayIncomeReq) Reset()         { *m = GuildTodayIncomeReq{} }
func (m *GuildTodayIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GuildTodayIncomeReq) ProtoMessage()    {}
func (*GuildTodayIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{27}
}
func (m *GuildTodayIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTodayIncomeReq.Unmarshal(m, b)
}
func (m *GuildTodayIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTodayIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GuildTodayIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTodayIncomeReq.Merge(dst, src)
}
func (m *GuildTodayIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GuildTodayIncomeReq.Size(m)
}
func (m *GuildTodayIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTodayIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTodayIncomeReq proto.InternalMessageInfo

func (m *GuildTodayIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type StatTodayRoomIncomeInfo struct {
	Roomid               uint32   `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Members              int64    `protobuf:"varint,2,opt,name=members,proto3" json:"members,omitempty"`
	Incomes              int64    `protobuf:"varint,3,opt,name=incomes,proto3" json:"incomes,omitempty"`
	Fees                 int64    `protobuf:"varint,4,opt,name=fees,proto3" json:"fees,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StatTodayRoomIncomeInfo) Reset()         { *m = StatTodayRoomIncomeInfo{} }
func (m *StatTodayRoomIncomeInfo) String() string { return proto.CompactTextString(m) }
func (*StatTodayRoomIncomeInfo) ProtoMessage()    {}
func (*StatTodayRoomIncomeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{28}
}
func (m *StatTodayRoomIncomeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StatTodayRoomIncomeInfo.Unmarshal(m, b)
}
func (m *StatTodayRoomIncomeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StatTodayRoomIncomeInfo.Marshal(b, m, deterministic)
}
func (dst *StatTodayRoomIncomeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StatTodayRoomIncomeInfo.Merge(dst, src)
}
func (m *StatTodayRoomIncomeInfo) XXX_Size() int {
	return xxx_messageInfo_StatTodayRoomIncomeInfo.Size(m)
}
func (m *StatTodayRoomIncomeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StatTodayRoomIncomeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StatTodayRoomIncomeInfo proto.InternalMessageInfo

func (m *StatTodayRoomIncomeInfo) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *StatTodayRoomIncomeInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

func (m *StatTodayRoomIncomeInfo) GetIncomes() int64 {
	if m != nil {
		return m.Incomes
	}
	return 0
}

func (m *StatTodayRoomIncomeInfo) GetFees() int64 {
	if m != nil {
		return m.Fees
	}
	return 0
}

type GuildTodayIncomeRsp struct {
	StatRoomInfo         []*StatTodayRoomIncomeInfo `protobuf:"bytes,1,rep,name=stat_room_info,json=statRoomInfo,proto3" json:"stat_room_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GuildTodayIncomeRsp) Reset()         { *m = GuildTodayIncomeRsp{} }
func (m *GuildTodayIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GuildTodayIncomeRsp) ProtoMessage()    {}
func (*GuildTodayIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{29}
}
func (m *GuildTodayIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildTodayIncomeRsp.Unmarshal(m, b)
}
func (m *GuildTodayIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildTodayIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GuildTodayIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildTodayIncomeRsp.Merge(dst, src)
}
func (m *GuildTodayIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GuildTodayIncomeRsp.Size(m)
}
func (m *GuildTodayIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildTodayIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildTodayIncomeRsp proto.InternalMessageInfo

func (m *GuildTodayIncomeRsp) GetStatRoomInfo() []*StatTodayRoomIncomeInfo {
	if m != nil {
		return m.StatRoomInfo
	}
	return nil
}

type GuildMonthIncomeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Month                int64    `protobuf:"varint,2,opt,name=month,proto3" json:"month,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildMonthIncomeReq) Reset()         { *m = GuildMonthIncomeReq{} }
func (m *GuildMonthIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GuildMonthIncomeReq) ProtoMessage()    {}
func (*GuildMonthIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{30}
}
func (m *GuildMonthIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildMonthIncomeReq.Unmarshal(m, b)
}
func (m *GuildMonthIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildMonthIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GuildMonthIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildMonthIncomeReq.Merge(dst, src)
}
func (m *GuildMonthIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GuildMonthIncomeReq.Size(m)
}
func (m *GuildMonthIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildMonthIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildMonthIncomeReq proto.InternalMessageInfo

func (m *GuildMonthIncomeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildMonthIncomeReq) GetMonth() int64 {
	if m != nil {
		return m.Month
	}
	return 0
}

type DayTrendMoreInfo struct {
	Day                  int64    `protobuf:"varint,1,opt,name=day,proto3" json:"day,omitempty"`
	Fee                  int64    `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               int64    `protobuf:"varint,3,opt,name=income,proto3" json:"income,omitempty"`
	Members              int64    `protobuf:"varint,4,opt,name=members,proto3" json:"members,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DayTrendMoreInfo) Reset()         { *m = DayTrendMoreInfo{} }
func (m *DayTrendMoreInfo) String() string { return proto.CompactTextString(m) }
func (*DayTrendMoreInfo) ProtoMessage()    {}
func (*DayTrendMoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{31}
}
func (m *DayTrendMoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DayTrendMoreInfo.Unmarshal(m, b)
}
func (m *DayTrendMoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DayTrendMoreInfo.Marshal(b, m, deterministic)
}
func (dst *DayTrendMoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DayTrendMoreInfo.Merge(dst, src)
}
func (m *DayTrendMoreInfo) XXX_Size() int {
	return xxx_messageInfo_DayTrendMoreInfo.Size(m)
}
func (m *DayTrendMoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DayTrendMoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DayTrendMoreInfo proto.InternalMessageInfo

func (m *DayTrendMoreInfo) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *DayTrendMoreInfo) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *DayTrendMoreInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *DayTrendMoreInfo) GetMembers() int64 {
	if m != nil {
		return m.Members
	}
	return 0
}

type GuildMonthIncomeRsp struct {
	StatTrendInfo        []*DayTrendMoreInfo `protobuf:"bytes,1,rep,name=stat_trend_info,json=statTrendInfo,proto3" json:"stat_trend_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GuildMonthIncomeRsp) Reset()         { *m = GuildMonthIncomeRsp{} }
func (m *GuildMonthIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*GuildMonthIncomeRsp) ProtoMessage()    {}
func (*GuildMonthIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{32}
}
func (m *GuildMonthIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildMonthIncomeRsp.Unmarshal(m, b)
}
func (m *GuildMonthIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildMonthIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *GuildMonthIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildMonthIncomeRsp.Merge(dst, src)
}
func (m *GuildMonthIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_GuildMonthIncomeRsp.Size(m)
}
func (m *GuildMonthIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildMonthIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildMonthIncomeRsp proto.InternalMessageInfo

func (m *GuildMonthIncomeRsp) GetStatTrendInfo() []*DayTrendMoreInfo {
	if m != nil {
		return m.StatTrendInfo
	}
	return nil
}

type GuildUnSettlementDetailReq struct {
	GuildId              uint32    `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	QueryType            QueryType `protobuf:"varint,2,opt,name=query_type,json=queryType,proto3,enum=golddiamonn.QueryType" json:"query_type,omitempty"`
	Offset               uint32    `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GuildUnSettlementDetailReq) Reset()         { *m = GuildUnSettlementDetailReq{} }
func (m *GuildUnSettlementDetailReq) String() string { return proto.CompactTextString(m) }
func (*GuildUnSettlementDetailReq) ProtoMessage()    {}
func (*GuildUnSettlementDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{33}
}
func (m *GuildUnSettlementDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildUnSettlementDetailReq.Unmarshal(m, b)
}
func (m *GuildUnSettlementDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildUnSettlementDetailReq.Marshal(b, m, deterministic)
}
func (dst *GuildUnSettlementDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildUnSettlementDetailReq.Merge(dst, src)
}
func (m *GuildUnSettlementDetailReq) XXX_Size() int {
	return xxx_messageInfo_GuildUnSettlementDetailReq.Size(m)
}
func (m *GuildUnSettlementDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildUnSettlementDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildUnSettlementDetailReq proto.InternalMessageInfo

func (m *GuildUnSettlementDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildUnSettlementDetailReq) GetQueryType() QueryType {
	if m != nil {
		return m.QueryType
	}
	return QueryType_NONE_TYPE
}

func (m *GuildUnSettlementDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GuildUnSettlementDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GuildUnSettlementDetail struct {
	Roomid               uint32   `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Fee                  int64    `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               int64    `protobuf:"varint,3,opt,name=income,proto3" json:"income,omitempty"`
	Paiduid              uint32   `protobuf:"varint,4,opt,name=paiduid,proto3" json:"paiduid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildUnSettlementDetail) Reset()         { *m = GuildUnSettlementDetail{} }
func (m *GuildUnSettlementDetail) String() string { return proto.CompactTextString(m) }
func (*GuildUnSettlementDetail) ProtoMessage()    {}
func (*GuildUnSettlementDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{34}
}
func (m *GuildUnSettlementDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildUnSettlementDetail.Unmarshal(m, b)
}
func (m *GuildUnSettlementDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildUnSettlementDetail.Marshal(b, m, deterministic)
}
func (dst *GuildUnSettlementDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildUnSettlementDetail.Merge(dst, src)
}
func (m *GuildUnSettlementDetail) XXX_Size() int {
	return xxx_messageInfo_GuildUnSettlementDetail.Size(m)
}
func (m *GuildUnSettlementDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildUnSettlementDetail.DiscardUnknown(m)
}

var xxx_messageInfo_GuildUnSettlementDetail proto.InternalMessageInfo

func (m *GuildUnSettlementDetail) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GuildUnSettlementDetail) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *GuildUnSettlementDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GuildUnSettlementDetail) GetPaiduid() uint32 {
	if m != nil {
		return m.Paiduid
	}
	return 0
}

type GuildUnSettlementDetailRsp struct {
	Details              []*GuildUnSettlementDetail `protobuf:"bytes,1,rep,name=details,proto3" json:"details,omitempty"`
	NextPage             bool                       `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GuildUnSettlementDetailRsp) Reset()         { *m = GuildUnSettlementDetailRsp{} }
func (m *GuildUnSettlementDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GuildUnSettlementDetailRsp) ProtoMessage()    {}
func (*GuildUnSettlementDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{35}
}
func (m *GuildUnSettlementDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildUnSettlementDetailRsp.Unmarshal(m, b)
}
func (m *GuildUnSettlementDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildUnSettlementDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GuildUnSettlementDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildUnSettlementDetailRsp.Merge(dst, src)
}
func (m *GuildUnSettlementDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GuildUnSettlementDetailRsp.Size(m)
}
func (m *GuildUnSettlementDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildUnSettlementDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildUnSettlementDetailRsp proto.InternalMessageInfo

func (m *GuildUnSettlementDetailRsp) GetDetails() []*GuildUnSettlementDetail {
	if m != nil {
		return m.Details
	}
	return nil
}

func (m *GuildUnSettlementDetailRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

type GetGuildsUnSettlementSummaryReq struct {
	GuildIds             []uint32 `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildsUnSettlementSummaryReq) Reset()         { *m = GetGuildsUnSettlementSummaryReq{} }
func (m *GetGuildsUnSettlementSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildsUnSettlementSummaryReq) ProtoMessage()    {}
func (*GetGuildsUnSettlementSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{36}
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Unmarshal(m, b)
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildsUnSettlementSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Merge(dst, src)
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryReq.Size(m)
}
func (m *GetGuildsUnSettlementSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsUnSettlementSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsUnSettlementSummaryReq proto.InternalMessageInfo

func (m *GetGuildsUnSettlementSummaryReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

type GetGuildsUnSettlementSummaryRsp struct {
	TodayFee             int64    `protobuf:"varint,1,opt,name=today_fee,json=todayFee,proto3" json:"today_fee,omitempty"`
	TodayIncome          int64    `protobuf:"varint,2,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	MonthFee             int64    `protobuf:"varint,3,opt,name=month_fee,json=monthFee,proto3" json:"month_fee,omitempty"`
	MonthIncome          int64    `protobuf:"varint,4,opt,name=month_income,json=monthIncome,proto3" json:"month_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildsUnSettlementSummaryRsp) Reset()         { *m = GetGuildsUnSettlementSummaryRsp{} }
func (m *GetGuildsUnSettlementSummaryRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildsUnSettlementSummaryRsp) ProtoMessage()    {}
func (*GetGuildsUnSettlementSummaryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{37}
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Unmarshal(m, b)
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildsUnSettlementSummaryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Merge(dst, src)
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.Size(m)
}
func (m *GetGuildsUnSettlementSummaryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildsUnSettlementSummaryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildsUnSettlementSummaryRsp proto.InternalMessageInfo

func (m *GetGuildsUnSettlementSummaryRsp) GetTodayFee() int64 {
	if m != nil {
		return m.TodayFee
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetMonthFee() int64 {
	if m != nil {
		return m.MonthFee
	}
	return 0
}

func (m *GetGuildsUnSettlementSummaryRsp) GetMonthIncome() int64 {
	if m != nil {
		return m.MonthIncome
	}
	return 0
}

type GuildIncomeDetailReq struct {
	GuildId              uint32    `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32    `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	BeginTime            int64     `protobuf:"varint,4,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64     `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	QueryType            QueryType `protobuf:"varint,6,opt,name=query_type,json=queryType,proto3,enum=golddiamonn.QueryType" json:"query_type,omitempty"`
	PaidUid              uint32    `protobuf:"varint,7,opt,name=paid_uid,json=paidUid,proto3" json:"paid_uid,omitempty"`
	ChannelId            uint32    `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GuildIncomeDetailReq) Reset()         { *m = GuildIncomeDetailReq{} }
func (m *GuildIncomeDetailReq) String() string { return proto.CompactTextString(m) }
func (*GuildIncomeDetailReq) ProtoMessage()    {}
func (*GuildIncomeDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{38}
}
func (m *GuildIncomeDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildIncomeDetailReq.Unmarshal(m, b)
}
func (m *GuildIncomeDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildIncomeDetailReq.Marshal(b, m, deterministic)
}
func (dst *GuildIncomeDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildIncomeDetailReq.Merge(dst, src)
}
func (m *GuildIncomeDetailReq) XXX_Size() int {
	return xxx_messageInfo_GuildIncomeDetailReq.Size(m)
}
func (m *GuildIncomeDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildIncomeDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildIncomeDetailReq proto.InternalMessageInfo

func (m *GuildIncomeDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildIncomeDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GuildIncomeDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GuildIncomeDetailReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GuildIncomeDetailReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GuildIncomeDetailReq) GetQueryType() QueryType {
	if m != nil {
		return m.QueryType
	}
	return QueryType_NONE_TYPE
}

func (m *GuildIncomeDetailReq) GetPaidUid() uint32 {
	if m != nil {
		return m.PaidUid
	}
	return 0
}

func (m *GuildIncomeDetailReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GuildIncomeDetail struct {
	Roomid               uint32   `protobuf:"varint,1,opt,name=roomid,proto3" json:"roomid,omitempty"`
	Fee                  int64    `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               int64    `protobuf:"varint,3,opt,name=income,proto3" json:"income,omitempty"`
	Paiduid              uint32   `protobuf:"varint,4,opt,name=paiduid,proto3" json:"paiduid,omitempty"`
	BoughtTime           int64    `protobuf:"varint,5,opt,name=bought_time,json=boughtTime,proto3" json:"bought_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildIncomeDetail) Reset()         { *m = GuildIncomeDetail{} }
func (m *GuildIncomeDetail) String() string { return proto.CompactTextString(m) }
func (*GuildIncomeDetail) ProtoMessage()    {}
func (*GuildIncomeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{39}
}
func (m *GuildIncomeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildIncomeDetail.Unmarshal(m, b)
}
func (m *GuildIncomeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildIncomeDetail.Marshal(b, m, deterministic)
}
func (dst *GuildIncomeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildIncomeDetail.Merge(dst, src)
}
func (m *GuildIncomeDetail) XXX_Size() int {
	return xxx_messageInfo_GuildIncomeDetail.Size(m)
}
func (m *GuildIncomeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildIncomeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_GuildIncomeDetail proto.InternalMessageInfo

func (m *GuildIncomeDetail) GetRoomid() uint32 {
	if m != nil {
		return m.Roomid
	}
	return 0
}

func (m *GuildIncomeDetail) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *GuildIncomeDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GuildIncomeDetail) GetPaiduid() uint32 {
	if m != nil {
		return m.Paiduid
	}
	return 0
}

func (m *GuildIncomeDetail) GetBoughtTime() int64 {
	if m != nil {
		return m.BoughtTime
	}
	return 0
}

type GuildRoomIncomeDetail struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	RoomId               uint32   `protobuf:"varint,2,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Fee                  int64    `protobuf:"varint,3,opt,name=fee,proto3" json:"fee,omitempty"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	Date                 uint32   `protobuf:"varint,5,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildRoomIncomeDetail) Reset()         { *m = GuildRoomIncomeDetail{} }
func (m *GuildRoomIncomeDetail) String() string { return proto.CompactTextString(m) }
func (*GuildRoomIncomeDetail) ProtoMessage()    {}
func (*GuildRoomIncomeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{40}
}
func (m *GuildRoomIncomeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildRoomIncomeDetail.Unmarshal(m, b)
}
func (m *GuildRoomIncomeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildRoomIncomeDetail.Marshal(b, m, deterministic)
}
func (dst *GuildRoomIncomeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildRoomIncomeDetail.Merge(dst, src)
}
func (m *GuildRoomIncomeDetail) XXX_Size() int {
	return xxx_messageInfo_GuildRoomIncomeDetail.Size(m)
}
func (m *GuildRoomIncomeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildRoomIncomeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_GuildRoomIncomeDetail proto.InternalMessageInfo

func (m *GuildRoomIncomeDetail) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildRoomIncomeDetail) GetRoomId() uint32 {
	if m != nil {
		return m.RoomId
	}
	return 0
}

func (m *GuildRoomIncomeDetail) GetFee() int64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *GuildRoomIncomeDetail) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *GuildRoomIncomeDetail) GetDate() uint32 {
	if m != nil {
		return m.Date
	}
	return 0
}

type GuildIncomeDetailRsp struct {
	IncomeDetails        []*GuildIncomeDetail `protobuf:"bytes,1,rep,name=income_details,json=incomeDetails,proto3" json:"income_details,omitempty"`
	NextPage             bool                 `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GuildIncomeDetailRsp) Reset()         { *m = GuildIncomeDetailRsp{} }
func (m *GuildIncomeDetailRsp) String() string { return proto.CompactTextString(m) }
func (*GuildIncomeDetailRsp) ProtoMessage()    {}
func (*GuildIncomeDetailRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{41}
}
func (m *GuildIncomeDetailRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildIncomeDetailRsp.Unmarshal(m, b)
}
func (m *GuildIncomeDetailRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildIncomeDetailRsp.Marshal(b, m, deterministic)
}
func (dst *GuildIncomeDetailRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildIncomeDetailRsp.Merge(dst, src)
}
func (m *GuildIncomeDetailRsp) XXX_Size() int {
	return xxx_messageInfo_GuildIncomeDetailRsp.Size(m)
}
func (m *GuildIncomeDetailRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildIncomeDetailRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildIncomeDetailRsp proto.InternalMessageInfo

func (m *GuildIncomeDetailRsp) GetIncomeDetails() []*GuildIncomeDetail {
	if m != nil {
		return m.IncomeDetails
	}
	return nil
}

func (m *GuildIncomeDetailRsp) GetNextPage() bool {
	if m != nil {
		return m.NextPage
	}
	return false
}

type GuildUnSettlementSummaryReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildUnSettlementSummaryReq) Reset()         { *m = GuildUnSettlementSummaryReq{} }
func (m *GuildUnSettlementSummaryReq) String() string { return proto.CompactTextString(m) }
func (*GuildUnSettlementSummaryReq) ProtoMessage()    {}
func (*GuildUnSettlementSummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{42}
}
func (m *GuildUnSettlementSummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildUnSettlementSummaryReq.Unmarshal(m, b)
}
func (m *GuildUnSettlementSummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildUnSettlementSummaryReq.Marshal(b, m, deterministic)
}
func (dst *GuildUnSettlementSummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildUnSettlementSummaryReq.Merge(dst, src)
}
func (m *GuildUnSettlementSummaryReq) XXX_Size() int {
	return xxx_messageInfo_GuildUnSettlementSummaryReq.Size(m)
}
func (m *GuildUnSettlementSummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildUnSettlementSummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildUnSettlementSummaryReq proto.InternalMessageInfo

func (m *GuildUnSettlementSummaryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GuildUnSettlementSummaryRsp struct {
	PaidUidCnt           int64    `protobuf:"varint,1,opt,name=paid_uid_cnt,json=paidUidCnt,proto3" json:"paid_uid_cnt,omitempty"`
	TotalIncome          int64    `protobuf:"varint,2,opt,name=total_income,json=totalIncome,proto3" json:"total_income,omitempty"`
	TotalFee             int64    `protobuf:"varint,3,opt,name=total_fee,json=totalFee,proto3" json:"total_fee,omitempty"`
	IncomeBalance        int64    `protobuf:"varint,4,opt,name=income_balance,json=incomeBalance,proto3" json:"income_balance,omitempty"`
	BeginTime            int64    `protobuf:"varint,5,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildUnSettlementSummaryRsp) Reset()         { *m = GuildUnSettlementSummaryRsp{} }
func (m *GuildUnSettlementSummaryRsp) String() string { return proto.CompactTextString(m) }
func (*GuildUnSettlementSummaryRsp) ProtoMessage()    {}
func (*GuildUnSettlementSummaryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{43}
}
func (m *GuildUnSettlementSummaryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildUnSettlementSummaryRsp.Unmarshal(m, b)
}
func (m *GuildUnSettlementSummaryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildUnSettlementSummaryRsp.Marshal(b, m, deterministic)
}
func (dst *GuildUnSettlementSummaryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildUnSettlementSummaryRsp.Merge(dst, src)
}
func (m *GuildUnSettlementSummaryRsp) XXX_Size() int {
	return xxx_messageInfo_GuildUnSettlementSummaryRsp.Size(m)
}
func (m *GuildUnSettlementSummaryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildUnSettlementSummaryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildUnSettlementSummaryRsp proto.InternalMessageInfo

func (m *GuildUnSettlementSummaryRsp) GetPaidUidCnt() int64 {
	if m != nil {
		return m.PaidUidCnt
	}
	return 0
}

func (m *GuildUnSettlementSummaryRsp) GetTotalIncome() int64 {
	if m != nil {
		return m.TotalIncome
	}
	return 0
}

func (m *GuildUnSettlementSummaryRsp) GetTotalFee() int64 {
	if m != nil {
		return m.TotalFee
	}
	return 0
}

func (m *GuildUnSettlementSummaryRsp) GetIncomeBalance() int64 {
	if m != nil {
		return m.IncomeBalance
	}
	return 0
}

func (m *GuildUnSettlementSummaryRsp) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GuildUnSettlementSummaryRsp) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type ConsumeRankItem struct {
	Alias                string   `protobuf:"bytes,1,opt,name=alias,proto3" json:"alias,omitempty"`
	NickName             string   `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Consume              int64    `protobuf:"varint,4,opt,name=consume,proto3" json:"consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ConsumeRankItem) Reset()         { *m = ConsumeRankItem{} }
func (m *ConsumeRankItem) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankItem) ProtoMessage()    {}
func (*ConsumeRankItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{44}
}
func (m *ConsumeRankItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankItem.Unmarshal(m, b)
}
func (m *ConsumeRankItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankItem.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankItem.Merge(dst, src)
}
func (m *ConsumeRankItem) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankItem.Size(m)
}
func (m *ConsumeRankItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankItem.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankItem proto.InternalMessageInfo

func (m *ConsumeRankItem) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *ConsumeRankItem) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *ConsumeRankItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *ConsumeRankItem) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

type ConsumeRankReq struct {
	ConsumeType          RangeType `protobuf:"varint,1,opt,name=consume_type,json=consumeType,proto3,enum=golddiamonn.RangeType" json:"consume_type,omitempty"`
	GuildId              uint32    `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *ConsumeRankReq) Reset()         { *m = ConsumeRankReq{} }
func (m *ConsumeRankReq) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankReq) ProtoMessage()    {}
func (*ConsumeRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{45}
}
func (m *ConsumeRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankReq.Unmarshal(m, b)
}
func (m *ConsumeRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankReq.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankReq.Merge(dst, src)
}
func (m *ConsumeRankReq) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankReq.Size(m)
}
func (m *ConsumeRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankReq proto.InternalMessageInfo

func (m *ConsumeRankReq) GetConsumeType() RangeType {
	if m != nil {
		return m.ConsumeType
	}
	return RangeType_DAY_RANGE_TYPE
}

func (m *ConsumeRankReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ConsumeRankRsp struct {
	ConsumeRankList      []*ConsumeRankItem `protobuf:"bytes,1,rep,name=consume_rank_list,json=consumeRankList,proto3" json:"consume_rank_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ConsumeRankRsp) Reset()         { *m = ConsumeRankRsp{} }
func (m *ConsumeRankRsp) String() string { return proto.CompactTextString(m) }
func (*ConsumeRankRsp) ProtoMessage()    {}
func (*ConsumeRankRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{46}
}
func (m *ConsumeRankRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ConsumeRankRsp.Unmarshal(m, b)
}
func (m *ConsumeRankRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ConsumeRankRsp.Marshal(b, m, deterministic)
}
func (dst *ConsumeRankRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ConsumeRankRsp.Merge(dst, src)
}
func (m *ConsumeRankRsp) XXX_Size() int {
	return xxx_messageInfo_ConsumeRankRsp.Size(m)
}
func (m *ConsumeRankRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ConsumeRankRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ConsumeRankRsp proto.InternalMessageInfo

func (m *ConsumeRankRsp) GetConsumeRankList() []*ConsumeRankItem {
	if m != nil {
		return m.ConsumeRankList
	}
	return nil
}

// channelConsume  公会推荐房日流水消费数据
type RoomConsumeQoqReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Day                  int64    `protobuf:"varint,3,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomConsumeQoqReq) Reset()         { *m = RoomConsumeQoqReq{} }
func (m *RoomConsumeQoqReq) String() string { return proto.CompactTextString(m) }
func (*RoomConsumeQoqReq) ProtoMessage()    {}
func (*RoomConsumeQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{47}
}
func (m *RoomConsumeQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomConsumeQoqReq.Unmarshal(m, b)
}
func (m *RoomConsumeQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomConsumeQoqReq.Marshal(b, m, deterministic)
}
func (dst *RoomConsumeQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomConsumeQoqReq.Merge(dst, src)
}
func (m *RoomConsumeQoqReq) XXX_Size() int {
	return xxx_messageInfo_RoomConsumeQoqReq.Size(m)
}
func (m *RoomConsumeQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomConsumeQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_RoomConsumeQoqReq proto.InternalMessageInfo

func (m *RoomConsumeQoqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RoomConsumeQoqReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RoomConsumeQoqReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type RoomConsumeQoqRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume,omitempty"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time,omitempty"`
	CompareConsume       int64    `protobuf:"varint,4,opt,name=compare_consume,json=compareConsume,proto3" json:"compare_consume,omitempty"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq,omitempty"`
	Channelid            uint32   `protobuf:"varint,6,opt,name=channelid,proto3" json:"channelid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RoomConsumeQoqRsp) Reset()         { *m = RoomConsumeQoqRsp{} }
func (m *RoomConsumeQoqRsp) String() string { return proto.CompactTextString(m) }
func (*RoomConsumeQoqRsp) ProtoMessage()    {}
func (*RoomConsumeQoqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{48}
}
func (m *RoomConsumeQoqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoomConsumeQoqRsp.Unmarshal(m, b)
}
func (m *RoomConsumeQoqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoomConsumeQoqRsp.Marshal(b, m, deterministic)
}
func (dst *RoomConsumeQoqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoomConsumeQoqRsp.Merge(dst, src)
}
func (m *RoomConsumeQoqRsp) XXX_Size() int {
	return xxx_messageInfo_RoomConsumeQoqRsp.Size(m)
}
func (m *RoomConsumeQoqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RoomConsumeQoqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RoomConsumeQoqRsp proto.InternalMessageInfo

func (m *RoomConsumeQoqRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetCompareConsume() int64 {
	if m != nil {
		return m.CompareConsume
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

func (m *RoomConsumeQoqRsp) GetChannelid() uint32 {
	if m != nil {
		return m.Channelid
	}
	return 0
}

// 请求的是收益
type GuildConsumeDayQoqReq struct {
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Day                  int64    `protobuf:"varint,4,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildConsumeDayQoqReq) Reset()         { *m = GuildConsumeDayQoqReq{} }
func (m *GuildConsumeDayQoqReq) String() string { return proto.CompactTextString(m) }
func (*GuildConsumeDayQoqReq) ProtoMessage()    {}
func (*GuildConsumeDayQoqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{49}
}
func (m *GuildConsumeDayQoqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Unmarshal(m, b)
}
func (m *GuildConsumeDayQoqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Marshal(b, m, deterministic)
}
func (dst *GuildConsumeDayQoqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildConsumeDayQoqReq.Merge(dst, src)
}
func (m *GuildConsumeDayQoqReq) XXX_Size() int {
	return xxx_messageInfo_GuildConsumeDayQoqReq.Size(m)
}
func (m *GuildConsumeDayQoqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildConsumeDayQoqReq.DiscardUnknown(m)
}

var xxx_messageInfo_GuildConsumeDayQoqReq proto.InternalMessageInfo

func (m *GuildConsumeDayQoqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildConsumeDayQoqReq) GetDay() int64 {
	if m != nil {
		return m.Day
	}
	return 0
}

type GuildConsumeDayQoqRsp struct {
	StatTime             int64    `protobuf:"varint,1,opt,name=stat_time,json=statTime,proto3" json:"stat_time,omitempty"`
	Consume              int64    `protobuf:"varint,2,opt,name=consume,proto3" json:"consume,omitempty"`
	CompareStatTime      int64    `protobuf:"varint,3,opt,name=compare_stat_time,json=compareStatTime,proto3" json:"compare_stat_time,omitempty"`
	CompareConsume       int64    `protobuf:"varint,4,opt,name=compare_consume,json=compareConsume,proto3" json:"compare_consume,omitempty"`
	Qoq                  float32  `protobuf:"fixed32,5,opt,name=qoq,proto3" json:"qoq,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildConsumeDayQoqRsp) Reset()         { *m = GuildConsumeDayQoqRsp{} }
func (m *GuildConsumeDayQoqRsp) String() string { return proto.CompactTextString(m) }
func (*GuildConsumeDayQoqRsp) ProtoMessage()    {}
func (*GuildConsumeDayQoqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{50}
}
func (m *GuildConsumeDayQoqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Unmarshal(m, b)
}
func (m *GuildConsumeDayQoqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Marshal(b, m, deterministic)
}
func (dst *GuildConsumeDayQoqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildConsumeDayQoqRsp.Merge(dst, src)
}
func (m *GuildConsumeDayQoqRsp) XXX_Size() int {
	return xxx_messageInfo_GuildConsumeDayQoqRsp.Size(m)
}
func (m *GuildConsumeDayQoqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildConsumeDayQoqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GuildConsumeDayQoqRsp proto.InternalMessageInfo

func (m *GuildConsumeDayQoqRsp) GetStatTime() int64 {
	if m != nil {
		return m.StatTime
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetConsume() int64 {
	if m != nil {
		return m.Consume
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetCompareStatTime() int64 {
	if m != nil {
		return m.CompareStatTime
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetCompareConsume() int64 {
	if m != nil {
		return m.CompareConsume
	}
	return 0
}

func (m *GuildConsumeDayQoqRsp) GetQoq() float32 {
	if m != nil {
		return m.Qoq
	}
	return 0
}

type ApplicationLimitInfo struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	LimitDay             int64    `protobuf:"varint,2,opt,name=limit_day,json=limitDay,proto3" json:"limit_day,omitempty"`
	LimitEndTime         int64    `protobuf:"varint,3,opt,name=limit_end_time,json=limitEndTime,proto3" json:"limit_end_time,omitempty"`
	OpeUser              string   `protobuf:"bytes,4,opt,name=opeUser,proto3" json:"opeUser,omitempty"`
	UpdateTime           int64    `protobuf:"varint,5,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationLimitInfo) Reset()         { *m = ApplicationLimitInfo{} }
func (m *ApplicationLimitInfo) String() string { return proto.CompactTextString(m) }
func (*ApplicationLimitInfo) ProtoMessage()    {}
func (*ApplicationLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{51}
}
func (m *ApplicationLimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationLimitInfo.Unmarshal(m, b)
}
func (m *ApplicationLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationLimitInfo.Marshal(b, m, deterministic)
}
func (dst *ApplicationLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationLimitInfo.Merge(dst, src)
}
func (m *ApplicationLimitInfo) XXX_Size() int {
	return xxx_messageInfo_ApplicationLimitInfo.Size(m)
}
func (m *ApplicationLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationLimitInfo proto.InternalMessageInfo

func (m *ApplicationLimitInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplicationLimitInfo) GetLimitDay() int64 {
	if m != nil {
		return m.LimitDay
	}
	return 0
}

func (m *ApplicationLimitInfo) GetLimitEndTime() int64 {
	if m != nil {
		return m.LimitEndTime
	}
	return 0
}

func (m *ApplicationLimitInfo) GetOpeUser() string {
	if m != nil {
		return m.OpeUser
	}
	return ""
}

func (m *ApplicationLimitInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

// 插入
type ApplicationLimitOpeReq struct {
	Operation            GuildInfoType         `protobuf:"varint,1,opt,name=operation,proto3,enum=golddiamonn.GuildInfoType" json:"operation,omitempty"`
	LimitInfo            *ApplicationLimitInfo `protobuf:"bytes,2,opt,name=limit_info,json=limitInfo,proto3" json:"limit_info,omitempty"`
	LibraryType          uint32                `protobuf:"varint,3,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ApplicationLimitOpeReq) Reset()         { *m = ApplicationLimitOpeReq{} }
func (m *ApplicationLimitOpeReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationLimitOpeReq) ProtoMessage()    {}
func (*ApplicationLimitOpeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{52}
}
func (m *ApplicationLimitOpeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationLimitOpeReq.Unmarshal(m, b)
}
func (m *ApplicationLimitOpeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationLimitOpeReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationLimitOpeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationLimitOpeReq.Merge(dst, src)
}
func (m *ApplicationLimitOpeReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationLimitOpeReq.Size(m)
}
func (m *ApplicationLimitOpeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationLimitOpeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationLimitOpeReq proto.InternalMessageInfo

func (m *ApplicationLimitOpeReq) GetOperation() GuildInfoType {
	if m != nil {
		return m.Operation
	}
	return GuildInfoType_GUILD_INSERT
}

func (m *ApplicationLimitOpeReq) GetLimitInfo() *ApplicationLimitInfo {
	if m != nil {
		return m.LimitInfo
	}
	return nil
}

func (m *ApplicationLimitOpeReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type ApplicationLimitOpeRsp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationLimitOpeRsp) Reset()         { *m = ApplicationLimitOpeRsp{} }
func (m *ApplicationLimitOpeRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationLimitOpeRsp) ProtoMessage()    {}
func (*ApplicationLimitOpeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{53}
}
func (m *ApplicationLimitOpeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationLimitOpeRsp.Unmarshal(m, b)
}
func (m *ApplicationLimitOpeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationLimitOpeRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationLimitOpeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationLimitOpeRsp.Merge(dst, src)
}
func (m *ApplicationLimitOpeRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationLimitOpeRsp.Size(m)
}
func (m *ApplicationLimitOpeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationLimitOpeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationLimitOpeRsp proto.InternalMessageInfo

func (m *ApplicationLimitOpeRsp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type ApplicationLimitQueryReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	LibraryType          uint32   `protobuf:"varint,4,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationLimitQueryReq) Reset()         { *m = ApplicationLimitQueryReq{} }
func (m *ApplicationLimitQueryReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationLimitQueryReq) ProtoMessage()    {}
func (*ApplicationLimitQueryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{54}
}
func (m *ApplicationLimitQueryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationLimitQueryReq.Unmarshal(m, b)
}
func (m *ApplicationLimitQueryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationLimitQueryReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationLimitQueryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationLimitQueryReq.Merge(dst, src)
}
func (m *ApplicationLimitQueryReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationLimitQueryReq.Size(m)
}
func (m *ApplicationLimitQueryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationLimitQueryReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationLimitQueryReq proto.InternalMessageInfo

func (m *ApplicationLimitQueryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplicationLimitQueryReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ApplicationLimitQueryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ApplicationLimitQueryReq) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

type ApplicationLimitQueryRsp struct {
	QueryInfo            []*ApplicationLimitInfo `protobuf:"bytes,1,rep,name=queryInfo,proto3" json:"queryInfo,omitempty"`
	Totalnum             uint32                  `protobuf:"varint,2,opt,name=totalnum,proto3" json:"totalnum,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *ApplicationLimitQueryRsp) Reset()         { *m = ApplicationLimitQueryRsp{} }
func (m *ApplicationLimitQueryRsp) String() string { return proto.CompactTextString(m) }
func (*ApplicationLimitQueryRsp) ProtoMessage()    {}
func (*ApplicationLimitQueryRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{55}
}
func (m *ApplicationLimitQueryRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationLimitQueryRsp.Unmarshal(m, b)
}
func (m *ApplicationLimitQueryRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationLimitQueryRsp.Marshal(b, m, deterministic)
}
func (dst *ApplicationLimitQueryRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationLimitQueryRsp.Merge(dst, src)
}
func (m *ApplicationLimitQueryRsp) XXX_Size() int {
	return xxx_messageInfo_ApplicationLimitQueryRsp.Size(m)
}
func (m *ApplicationLimitQueryRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationLimitQueryRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationLimitQueryRsp proto.InternalMessageInfo

func (m *ApplicationLimitQueryRsp) GetQueryInfo() []*ApplicationLimitInfo {
	if m != nil {
		return m.QueryInfo
	}
	return nil
}

func (m *ApplicationLimitQueryRsp) GetTotalnum() uint32 {
	if m != nil {
		return m.Totalnum
	}
	return 0
}

// 是否限制
type RecommendExamLimitCheckReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamLimitCheckReq) Reset()         { *m = RecommendExamLimitCheckReq{} }
func (m *RecommendExamLimitCheckReq) String() string { return proto.CompactTextString(m) }
func (*RecommendExamLimitCheckReq) ProtoMessage()    {}
func (*RecommendExamLimitCheckReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{56}
}
func (m *RecommendExamLimitCheckReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamLimitCheckReq.Unmarshal(m, b)
}
func (m *RecommendExamLimitCheckReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamLimitCheckReq.Marshal(b, m, deterministic)
}
func (dst *RecommendExamLimitCheckReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamLimitCheckReq.Merge(dst, src)
}
func (m *RecommendExamLimitCheckReq) XXX_Size() int {
	return xxx_messageInfo_RecommendExamLimitCheckReq.Size(m)
}
func (m *RecommendExamLimitCheckReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamLimitCheckReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamLimitCheckReq proto.InternalMessageInfo

func (m *RecommendExamLimitCheckReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecommendExamLimitCheckReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type RecommendExamLimitCheckResp struct {
	IsLimit              bool     `protobuf:"varint,1,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamLimitCheckResp) Reset()         { *m = RecommendExamLimitCheckResp{} }
func (m *RecommendExamLimitCheckResp) String() string { return proto.CompactTextString(m) }
func (*RecommendExamLimitCheckResp) ProtoMessage()    {}
func (*RecommendExamLimitCheckResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{57}
}
func (m *RecommendExamLimitCheckResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Unmarshal(m, b)
}
func (m *RecommendExamLimitCheckResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Marshal(b, m, deterministic)
}
func (dst *RecommendExamLimitCheckResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamLimitCheckResp.Merge(dst, src)
}
func (m *RecommendExamLimitCheckResp) XXX_Size() int {
	return xxx_messageInfo_RecommendExamLimitCheckResp.Size(m)
}
func (m *RecommendExamLimitCheckResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamLimitCheckResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamLimitCheckResp proto.InternalMessageInfo

func (m *RecommendExamLimitCheckResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

// 考核信息
type ExamContent struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ExamTimes            uint32   `protobuf:"varint,3,opt,name=exam_times,json=examTimes,proto3" json:"exam_times,omitempty"`
	SignOutTimes         uint32   `protobuf:"varint,4,opt,name=sign_out_times,json=signOutTimes,proto3" json:"sign_out_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExamContent) Reset()         { *m = ExamContent{} }
func (m *ExamContent) String() string { return proto.CompactTextString(m) }
func (*ExamContent) ProtoMessage()    {}
func (*ExamContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{58}
}
func (m *ExamContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamContent.Unmarshal(m, b)
}
func (m *ExamContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamContent.Marshal(b, m, deterministic)
}
func (dst *ExamContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamContent.Merge(dst, src)
}
func (m *ExamContent) XXX_Size() int {
	return xxx_messageInfo_ExamContent.Size(m)
}
func (m *ExamContent) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamContent.DiscardUnknown(m)
}

var xxx_messageInfo_ExamContent proto.InternalMessageInfo

func (m *ExamContent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExamContent) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ExamContent) GetExamTimes() uint32 {
	if m != nil {
		return m.ExamTimes
	}
	return 0
}

func (m *ExamContent) GetSignOutTimes() uint32 {
	if m != nil {
		return m.SignOutTimes
	}
	return 0
}

// 推荐房考核申请信息
type RecommendExamBaseInfo struct {
	GuildOwnerUid        uint32      `protobuf:"varint,1,opt,name=guild_owner_uid,json=guildOwnerUid,proto3" json:"guild_owner_uid,omitempty"`
	GuildId              uint32      `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Purpose              ExamPurpose `protobuf:"varint,3,opt,name=purpose,proto3,enum=golddiamonn.ExamPurpose" json:"purpose,omitempty"`
	ExamChannelId        uint32      `protobuf:"varint,4,opt,name=exam_channel_id,json=examChannelId,proto3" json:"exam_channel_id,omitempty"`
	CurrentTag           uint32      `protobuf:"varint,5,opt,name=current_tag,json=currentTag,proto3" json:"current_tag,omitempty"`
	ExamTag              uint32      `protobuf:"varint,6,opt,name=exam_tag,json=examTag,proto3" json:"exam_tag,omitempty"`
	ApplicationTime      uint32      `protobuf:"varint,7,opt,name=application_time,json=applicationTime,proto3" json:"application_time,omitempty"`
	ExamUids             []uint32    `protobuf:"varint,8,rep,packed,name=exam_uids,json=examUids,proto3" json:"exam_uids,omitempty"`
	GuildOwnerChannelId  uint32      `protobuf:"varint,9,opt,name=guild_owner_channel_id,json=guildOwnerChannelId,proto3" json:"guild_owner_channel_id,omitempty"`
	Password             string      `protobuf:"bytes,10,opt,name=password,proto3" json:"password,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RecommendExamBaseInfo) Reset()         { *m = RecommendExamBaseInfo{} }
func (m *RecommendExamBaseInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendExamBaseInfo) ProtoMessage()    {}
func (*RecommendExamBaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{59}
}
func (m *RecommendExamBaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamBaseInfo.Unmarshal(m, b)
}
func (m *RecommendExamBaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamBaseInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendExamBaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamBaseInfo.Merge(dst, src)
}
func (m *RecommendExamBaseInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendExamBaseInfo.Size(m)
}
func (m *RecommendExamBaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamBaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamBaseInfo proto.InternalMessageInfo

func (m *RecommendExamBaseInfo) GetGuildOwnerUid() uint32 {
	if m != nil {
		return m.GuildOwnerUid
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetPurpose() ExamPurpose {
	if m != nil {
		return m.Purpose
	}
	return ExamPurpose_FOR_DEFAULT
}

func (m *RecommendExamBaseInfo) GetExamChannelId() uint32 {
	if m != nil {
		return m.ExamChannelId
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetCurrentTag() uint32 {
	if m != nil {
		return m.CurrentTag
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetExamTag() uint32 {
	if m != nil {
		return m.ExamTag
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetApplicationTime() uint32 {
	if m != nil {
		return m.ApplicationTime
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetExamUids() []uint32 {
	if m != nil {
		return m.ExamUids
	}
	return nil
}

func (m *RecommendExamBaseInfo) GetGuildOwnerChannelId() uint32 {
	if m != nil {
		return m.GuildOwnerChannelId
	}
	return 0
}

func (m *RecommendExamBaseInfo) GetPassword() string {
	if m != nil {
		return m.Password
	}
	return ""
}

type RecommendExamTotalInfo struct {
	BaseInfo             *RecommendExamBaseInfo `protobuf:"bytes,1,opt,name=base_info,json=baseInfo,proto3" json:"base_info,omitempty"`
	ExamTime             uint32                 `protobuf:"varint,2,opt,name=exam_time,json=examTime,proto3" json:"exam_time,omitempty"`
	Operation            *ExamOperation         `protobuf:"bytes,3,opt,name=operation,proto3" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RecommendExamTotalInfo) Reset()         { *m = RecommendExamTotalInfo{} }
func (m *RecommendExamTotalInfo) String() string { return proto.CompactTextString(m) }
func (*RecommendExamTotalInfo) ProtoMessage()    {}
func (*RecommendExamTotalInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{60}
}
func (m *RecommendExamTotalInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamTotalInfo.Unmarshal(m, b)
}
func (m *RecommendExamTotalInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamTotalInfo.Marshal(b, m, deterministic)
}
func (dst *RecommendExamTotalInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamTotalInfo.Merge(dst, src)
}
func (m *RecommendExamTotalInfo) XXX_Size() int {
	return xxx_messageInfo_RecommendExamTotalInfo.Size(m)
}
func (m *RecommendExamTotalInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamTotalInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamTotalInfo proto.InternalMessageInfo

func (m *RecommendExamTotalInfo) GetBaseInfo() *RecommendExamBaseInfo {
	if m != nil {
		return m.BaseInfo
	}
	return nil
}

func (m *RecommendExamTotalInfo) GetExamTime() uint32 {
	if m != nil {
		return m.ExamTime
	}
	return 0
}

func (m *RecommendExamTotalInfo) GetOperation() *ExamOperation {
	if m != nil {
		return m.Operation
	}
	return nil
}

// 操作结果
type ExamOperation struct {
	Note                 string     `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
	Status               ExamStatus `protobuf:"varint,2,opt,name=status,proto3,enum=golddiamonn.ExamStatus" json:"status,omitempty"`
	Operator             string     `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	OperationTime        uint32     `protobuf:"varint,4,opt,name=operation_time,json=operationTime,proto3" json:"operation_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ExamOperation) Reset()         { *m = ExamOperation{} }
func (m *ExamOperation) String() string { return proto.CompactTextString(m) }
func (*ExamOperation) ProtoMessage()    {}
func (*ExamOperation) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{61}
}
func (m *ExamOperation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamOperation.Unmarshal(m, b)
}
func (m *ExamOperation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamOperation.Marshal(b, m, deterministic)
}
func (dst *ExamOperation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamOperation.Merge(dst, src)
}
func (m *ExamOperation) XXX_Size() int {
	return xxx_messageInfo_ExamOperation.Size(m)
}
func (m *ExamOperation) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamOperation.DiscardUnknown(m)
}

var xxx_messageInfo_ExamOperation proto.InternalMessageInfo

func (m *ExamOperation) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *ExamOperation) GetStatus() ExamStatus {
	if m != nil {
		return m.Status
	}
	return ExamStatus_EXAM_DEFAULT
}

func (m *ExamOperation) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ExamOperation) GetOperationTime() uint32 {
	if m != nil {
		return m.OperationTime
	}
	return 0
}

type RecommendExamOperationReq struct {
	GuildId              uint32         `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Operation            *ExamOperation `protobuf:"bytes,2,opt,name=operation,proto3" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *RecommendExamOperationReq) Reset()         { *m = RecommendExamOperationReq{} }
func (m *RecommendExamOperationReq) String() string { return proto.CompactTextString(m) }
func (*RecommendExamOperationReq) ProtoMessage()    {}
func (*RecommendExamOperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{62}
}
func (m *RecommendExamOperationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamOperationReq.Unmarshal(m, b)
}
func (m *RecommendExamOperationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamOperationReq.Marshal(b, m, deterministic)
}
func (dst *RecommendExamOperationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamOperationReq.Merge(dst, src)
}
func (m *RecommendExamOperationReq) XXX_Size() int {
	return xxx_messageInfo_RecommendExamOperationReq.Size(m)
}
func (m *RecommendExamOperationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamOperationReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamOperationReq proto.InternalMessageInfo

func (m *RecommendExamOperationReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *RecommendExamOperationReq) GetOperation() *ExamOperation {
	if m != nil {
		return m.Operation
	}
	return nil
}

type RecommendExamOperationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamOperationResp) Reset()         { *m = RecommendExamOperationResp{} }
func (m *RecommendExamOperationResp) String() string { return proto.CompactTextString(m) }
func (*RecommendExamOperationResp) ProtoMessage()    {}
func (*RecommendExamOperationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{63}
}
func (m *RecommendExamOperationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamOperationResp.Unmarshal(m, b)
}
func (m *RecommendExamOperationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamOperationResp.Marshal(b, m, deterministic)
}
func (dst *RecommendExamOperationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamOperationResp.Merge(dst, src)
}
func (m *RecommendExamOperationResp) XXX_Size() int {
	return xxx_messageInfo_RecommendExamOperationResp.Size(m)
}
func (m *RecommendExamOperationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamOperationResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamOperationResp proto.InternalMessageInfo

type RecommendExamReq struct {
	Info                 *RecommendExamBaseInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RecommendExamReq) Reset()         { *m = RecommendExamReq{} }
func (m *RecommendExamReq) String() string { return proto.CompactTextString(m) }
func (*RecommendExamReq) ProtoMessage()    {}
func (*RecommendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{64}
}
func (m *RecommendExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamReq.Unmarshal(m, b)
}
func (m *RecommendExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamReq.Marshal(b, m, deterministic)
}
func (dst *RecommendExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamReq.Merge(dst, src)
}
func (m *RecommendExamReq) XXX_Size() int {
	return xxx_messageInfo_RecommendExamReq.Size(m)
}
func (m *RecommendExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamReq proto.InternalMessageInfo

func (m *RecommendExamReq) GetInfo() *RecommendExamBaseInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type RecommendExamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecommendExamResp) Reset()         { *m = RecommendExamResp{} }
func (m *RecommendExamResp) String() string { return proto.CompactTextString(m) }
func (*RecommendExamResp) ProtoMessage()    {}
func (*RecommendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{65}
}
func (m *RecommendExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecommendExamResp.Unmarshal(m, b)
}
func (m *RecommendExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecommendExamResp.Marshal(b, m, deterministic)
}
func (dst *RecommendExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecommendExamResp.Merge(dst, src)
}
func (m *RecommendExamResp) XXX_Size() int {
	return xxx_messageInfo_RecommendExamResp.Size(m)
}
func (m *RecommendExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecommendExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_RecommendExamResp proto.InternalMessageInfo

type GetRecommendExamReq struct {
	GuildOwnerUid        uint32            `protobuf:"varint,1,opt,name=guild_owner_uid,json=guildOwnerUid,proto3" json:"guild_owner_uid,omitempty"`
	GuildId              uint32            `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Type                 ExamOperationType `protobuf:"varint,3,opt,name=type,proto3,enum=golddiamonn.ExamOperationType" json:"type,omitempty"`
	Offset               uint32            `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32            `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetRecommendExamReq) Reset()         { *m = GetRecommendExamReq{} }
func (m *GetRecommendExamReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendExamReq) ProtoMessage()    {}
func (*GetRecommendExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{66}
}
func (m *GetRecommendExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendExamReq.Unmarshal(m, b)
}
func (m *GetRecommendExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendExamReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendExamReq.Merge(dst, src)
}
func (m *GetRecommendExamReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendExamReq.Size(m)
}
func (m *GetRecommendExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendExamReq proto.InternalMessageInfo

func (m *GetRecommendExamReq) GetGuildOwnerUid() uint32 {
	if m != nil {
		return m.GuildOwnerUid
	}
	return 0
}

func (m *GetRecommendExamReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetRecommendExamReq) GetType() ExamOperationType {
	if m != nil {
		return m.Type
	}
	return ExamOperationType_OPERATION_DEFAULT
}

func (m *GetRecommendExamReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRecommendExamReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRecommendExamResp struct {
	Info                 []*RecommendExamTotalInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Cnt                  uint32                    `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetRecommendExamResp) Reset()         { *m = GetRecommendExamResp{} }
func (m *GetRecommendExamResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendExamResp) ProtoMessage()    {}
func (*GetRecommendExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{67}
}
func (m *GetRecommendExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendExamResp.Unmarshal(m, b)
}
func (m *GetRecommendExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendExamResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendExamResp.Merge(dst, src)
}
func (m *GetRecommendExamResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendExamResp.Size(m)
}
func (m *GetRecommendExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendExamResp proto.InternalMessageInfo

func (m *GetRecommendExamResp) GetInfo() []*RecommendExamTotalInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetRecommendExamResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// 考核时间
type SetRecommendExamTimeReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ExamTime             uint32   `protobuf:"varint,2,opt,name=exam_time,json=examTime,proto3" json:"exam_time,omitempty"`
	ApplicationTime      uint32   `protobuf:"varint,3,opt,name=application_time,json=applicationTime,proto3" json:"application_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRecommendExamTimeReq) Reset()         { *m = SetRecommendExamTimeReq{} }
func (m *SetRecommendExamTimeReq) String() string { return proto.CompactTextString(m) }
func (*SetRecommendExamTimeReq) ProtoMessage()    {}
func (*SetRecommendExamTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{68}
}
func (m *SetRecommendExamTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRecommendExamTimeReq.Unmarshal(m, b)
}
func (m *SetRecommendExamTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRecommendExamTimeReq.Marshal(b, m, deterministic)
}
func (dst *SetRecommendExamTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRecommendExamTimeReq.Merge(dst, src)
}
func (m *SetRecommendExamTimeReq) XXX_Size() int {
	return xxx_messageInfo_SetRecommendExamTimeReq.Size(m)
}
func (m *SetRecommendExamTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRecommendExamTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetRecommendExamTimeReq proto.InternalMessageInfo

func (m *SetRecommendExamTimeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetRecommendExamTimeReq) GetExamTime() uint32 {
	if m != nil {
		return m.ExamTime
	}
	return 0
}

func (m *SetRecommendExamTimeReq) GetApplicationTime() uint32 {
	if m != nil {
		return m.ApplicationTime
	}
	return 0
}

type SetRecommendExamTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRecommendExamTimeResp) Reset()         { *m = SetRecommendExamTimeResp{} }
func (m *SetRecommendExamTimeResp) String() string { return proto.CompactTextString(m) }
func (*SetRecommendExamTimeResp) ProtoMessage()    {}
func (*SetRecommendExamTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{69}
}
func (m *SetRecommendExamTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRecommendExamTimeResp.Unmarshal(m, b)
}
func (m *SetRecommendExamTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRecommendExamTimeResp.Marshal(b, m, deterministic)
}
func (dst *SetRecommendExamTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRecommendExamTimeResp.Merge(dst, src)
}
func (m *SetRecommendExamTimeResp) XXX_Size() int {
	return xxx_messageInfo_SetRecommendExamTimeResp.Size(m)
}
func (m *SetRecommendExamTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRecommendExamTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetRecommendExamTimeResp proto.InternalMessageInfo

// 考核信息
type GetExamContentReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ApplicationTime      uint32   `protobuf:"varint,2,opt,name=application_time,json=applicationTime,proto3" json:"application_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExamContentReq) Reset()         { *m = GetExamContentReq{} }
func (m *GetExamContentReq) String() string { return proto.CompactTextString(m) }
func (*GetExamContentReq) ProtoMessage()    {}
func (*GetExamContentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{70}
}
func (m *GetExamContentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExamContentReq.Unmarshal(m, b)
}
func (m *GetExamContentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExamContentReq.Marshal(b, m, deterministic)
}
func (dst *GetExamContentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExamContentReq.Merge(dst, src)
}
func (m *GetExamContentReq) XXX_Size() int {
	return xxx_messageInfo_GetExamContentReq.Size(m)
}
func (m *GetExamContentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExamContentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExamContentReq proto.InternalMessageInfo

func (m *GetExamContentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetExamContentReq) GetApplicationTime() uint32 {
	if m != nil {
		return m.ApplicationTime
	}
	return 0
}

type GetExamContentResp struct {
	Contents             []*ExamContent `protobuf:"bytes,1,rep,name=contents,proto3" json:"contents,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetExamContentResp) Reset()         { *m = GetExamContentResp{} }
func (m *GetExamContentResp) String() string { return proto.CompactTextString(m) }
func (*GetExamContentResp) ProtoMessage()    {}
func (*GetExamContentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{71}
}
func (m *GetExamContentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExamContentResp.Unmarshal(m, b)
}
func (m *GetExamContentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExamContentResp.Marshal(b, m, deterministic)
}
func (dst *GetExamContentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExamContentResp.Merge(dst, src)
}
func (m *GetExamContentResp) XXX_Size() int {
	return xxx_messageInfo_GetExamContentResp.Size(m)
}
func (m *GetExamContentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExamContentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExamContentResp proto.InternalMessageInfo

func (m *GetExamContentResp) GetContents() []*ExamContent {
	if m != nil {
		return m.Contents
	}
	return nil
}

type ExamTagInfo struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExamTagInfo) Reset()         { *m = ExamTagInfo{} }
func (m *ExamTagInfo) String() string { return proto.CompactTextString(m) }
func (*ExamTagInfo) ProtoMessage()    {}
func (*ExamTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{72}
}
func (m *ExamTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamTagInfo.Unmarshal(m, b)
}
func (m *ExamTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamTagInfo.Marshal(b, m, deterministic)
}
func (dst *ExamTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamTagInfo.Merge(dst, src)
}
func (m *ExamTagInfo) XXX_Size() int {
	return xxx_messageInfo_ExamTagInfo.Size(m)
}
func (m *ExamTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ExamTagInfo proto.InternalMessageInfo

func (m *ExamTagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ExamTagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type GetExamTagListReq struct {
	Type                 ExamTagType `protobuf:"varint,1,opt,name=type,proto3,enum=golddiamonn.ExamTagType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetExamTagListReq) Reset()         { *m = GetExamTagListReq{} }
func (m *GetExamTagListReq) String() string { return proto.CompactTextString(m) }
func (*GetExamTagListReq) ProtoMessage()    {}
func (*GetExamTagListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{73}
}
func (m *GetExamTagListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExamTagListReq.Unmarshal(m, b)
}
func (m *GetExamTagListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExamTagListReq.Marshal(b, m, deterministic)
}
func (dst *GetExamTagListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExamTagListReq.Merge(dst, src)
}
func (m *GetExamTagListReq) XXX_Size() int {
	return xxx_messageInfo_GetExamTagListReq.Size(m)
}
func (m *GetExamTagListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExamTagListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExamTagListReq proto.InternalMessageInfo

func (m *GetExamTagListReq) GetType() ExamTagType {
	if m != nil {
		return m.Type
	}
	return ExamTagType_TAG_DEFAULT
}

type GetExamTagListResp struct {
	Infos                []*ExamTagInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetExamTagListResp) Reset()         { *m = GetExamTagListResp{} }
func (m *GetExamTagListResp) String() string { return proto.CompactTextString(m) }
func (*GetExamTagListResp) ProtoMessage()    {}
func (*GetExamTagListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{74}
}
func (m *GetExamTagListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExamTagListResp.Unmarshal(m, b)
}
func (m *GetExamTagListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExamTagListResp.Marshal(b, m, deterministic)
}
func (dst *GetExamTagListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExamTagListResp.Merge(dst, src)
}
func (m *GetExamTagListResp) XXX_Size() int {
	return xxx_messageInfo_GetExamTagListResp.Size(m)
}
func (m *GetExamTagListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExamTagListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExamTagListResp proto.InternalMessageInfo

func (m *GetExamTagListResp) GetInfos() []*ExamTagInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type YuyinExamApplicationInfo struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32          `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ExamTag              uint32          `protobuf:"varint,3,opt,name=exam_tag,json=examTag,proto3" json:"exam_tag,omitempty"`
	Url                  string          `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	Contact              string          `protobuf:"bytes,5,opt,name=contact,proto3" json:"contact,omitempty"`
	ApplicationTime      uint32          `protobuf:"varint,6,opt,name=application_time,json=applicationTime,proto3" json:"application_time,omitempty"`
	YuyinStatus          YuyinExamStatus `protobuf:"varint,7,opt,name=yuyin_status,json=yuyinStatus,proto3,enum=golddiamonn.YuyinExamStatus" json:"yuyin_status,omitempty"`
	SignTime             uint32          `protobuf:"varint,8,opt,name=sign_time,json=signTime,proto3" json:"sign_time,omitempty"`
	ExpireTime           uint32          `protobuf:"varint,9,opt,name=expire_time,json=expireTime,proto3" json:"expire_time,omitempty"`
	TagName              string          `protobuf:"bytes,10,opt,name=tagName,proto3" json:"tagName,omitempty"`
	ExamStatus           ExamStatus      `protobuf:"varint,11,opt,name=exam_status,json=examStatus,proto3,enum=golddiamonn.ExamStatus" json:"exam_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *YuyinExamApplicationInfo) Reset()         { *m = YuyinExamApplicationInfo{} }
func (m *YuyinExamApplicationInfo) String() string { return proto.CompactTextString(m) }
func (*YuyinExamApplicationInfo) ProtoMessage()    {}
func (*YuyinExamApplicationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{75}
}
func (m *YuyinExamApplicationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamApplicationInfo.Unmarshal(m, b)
}
func (m *YuyinExamApplicationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamApplicationInfo.Marshal(b, m, deterministic)
}
func (dst *YuyinExamApplicationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamApplicationInfo.Merge(dst, src)
}
func (m *YuyinExamApplicationInfo) XXX_Size() int {
	return xxx_messageInfo_YuyinExamApplicationInfo.Size(m)
}
func (m *YuyinExamApplicationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamApplicationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamApplicationInfo proto.InternalMessageInfo

func (m *YuyinExamApplicationInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetExamTag() uint32 {
	if m != nil {
		return m.ExamTag
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetContact() string {
	if m != nil {
		return m.Contact
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetApplicationTime() uint32 {
	if m != nil {
		return m.ApplicationTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetYuyinStatus() YuyinExamStatus {
	if m != nil {
		return m.YuyinStatus
	}
	return YuyinExamStatus_YE_DEFAULT
}

func (m *YuyinExamApplicationInfo) GetSignTime() uint32 {
	if m != nil {
		return m.SignTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetExpireTime() uint32 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

func (m *YuyinExamApplicationInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *YuyinExamApplicationInfo) GetExamStatus() ExamStatus {
	if m != nil {
		return m.ExamStatus
	}
	return ExamStatus_EXAM_DEFAULT
}

// 会长处理主播考核申请
type YuyinExamGuildOwnerOperationReq struct {
	AnchorUids           []uint32        `protobuf:"varint,1,rep,packed,name=anchor_uids,json=anchorUids,proto3" json:"anchor_uids,omitempty"`
	GuildId              uint32          `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	YuyinStatus          YuyinExamStatus `protobuf:"varint,3,opt,name=yuyin_status,json=yuyinStatus,proto3,enum=golddiamonn.YuyinExamStatus" json:"yuyin_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *YuyinExamGuildOwnerOperationReq) Reset()         { *m = YuyinExamGuildOwnerOperationReq{} }
func (m *YuyinExamGuildOwnerOperationReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamGuildOwnerOperationReq) ProtoMessage()    {}
func (*YuyinExamGuildOwnerOperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{76}
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Unmarshal(m, b)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamGuildOwnerOperationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Merge(dst, src)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationReq.Size(m)
}
func (m *YuyinExamGuildOwnerOperationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamGuildOwnerOperationReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamGuildOwnerOperationReq proto.InternalMessageInfo

func (m *YuyinExamGuildOwnerOperationReq) GetAnchorUids() []uint32 {
	if m != nil {
		return m.AnchorUids
	}
	return nil
}

func (m *YuyinExamGuildOwnerOperationReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *YuyinExamGuildOwnerOperationReq) GetYuyinStatus() YuyinExamStatus {
	if m != nil {
		return m.YuyinStatus
	}
	return YuyinExamStatus_YE_DEFAULT
}

type YuyinExamGuildOwnerOperationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamGuildOwnerOperationResp) Reset()         { *m = YuyinExamGuildOwnerOperationResp{} }
func (m *YuyinExamGuildOwnerOperationResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamGuildOwnerOperationResp) ProtoMessage()    {}
func (*YuyinExamGuildOwnerOperationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{77}
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Unmarshal(m, b)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamGuildOwnerOperationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Merge(dst, src)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamGuildOwnerOperationResp.Size(m)
}
func (m *YuyinExamGuildOwnerOperationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamGuildOwnerOperationResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamGuildOwnerOperationResp proto.InternalMessageInfo

// 主播提交考核
type YuyinExamReq struct {
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *YuyinExamReq) Reset()         { *m = YuyinExamReq{} }
func (m *YuyinExamReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamReq) ProtoMessage()    {}
func (*YuyinExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{78}
}
func (m *YuyinExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamReq.Unmarshal(m, b)
}
func (m *YuyinExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamReq.Merge(dst, src)
}
func (m *YuyinExamReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamReq.Size(m)
}
func (m *YuyinExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamReq proto.InternalMessageInfo

func (m *YuyinExamReq) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type YuyinExamResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamResp) Reset()         { *m = YuyinExamResp{} }
func (m *YuyinExamResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamResp) ProtoMessage()    {}
func (*YuyinExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{79}
}
func (m *YuyinExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamResp.Unmarshal(m, b)
}
func (m *YuyinExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamResp.Merge(dst, src)
}
func (m *YuyinExamResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamResp.Size(m)
}
func (m *YuyinExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamResp proto.InternalMessageInfo

// 主播查看提交的考核信息
type GetYuyinExamReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamReq) Reset()         { *m = GetYuyinExamReq{} }
func (m *GetYuyinExamReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamReq) ProtoMessage()    {}
func (*GetYuyinExamReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{80}
}
func (m *GetYuyinExamReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamReq.Unmarshal(m, b)
}
func (m *GetYuyinExamReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamReq.Merge(dst, src)
}
func (m *GetYuyinExamReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamReq.Size(m)
}
func (m *GetYuyinExamReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamReq proto.InternalMessageInfo

func (m *GetYuyinExamReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetYuyinExamResp struct {
	IsLimit              bool                      `protobuf:"varint,1,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,2,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetYuyinExamResp) Reset()         { *m = GetYuyinExamResp{} }
func (m *GetYuyinExamResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamResp) ProtoMessage()    {}
func (*GetYuyinExamResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{81}
}
func (m *GetYuyinExamResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamResp.Unmarshal(m, b)
}
func (m *GetYuyinExamResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamResp.Merge(dst, src)
}
func (m *GetYuyinExamResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamResp.Size(m)
}
func (m *GetYuyinExamResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamResp proto.InternalMessageInfo

func (m *GetYuyinExamResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

func (m *GetYuyinExamResp) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 会长查看主播提交的考核信息
type GetYuyinExamForGuildReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamForGuildReq) Reset()         { *m = GetYuyinExamForGuildReq{} }
func (m *GetYuyinExamForGuildReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamForGuildReq) ProtoMessage()    {}
func (*GetYuyinExamForGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{82}
}
func (m *GetYuyinExamForGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamForGuildReq.Unmarshal(m, b)
}
func (m *GetYuyinExamForGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamForGuildReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamForGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamForGuildReq.Merge(dst, src)
}
func (m *GetYuyinExamForGuildReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamForGuildReq.Size(m)
}
func (m *GetYuyinExamForGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamForGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamForGuildReq proto.InternalMessageInfo

func (m *GetYuyinExamForGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetYuyinExamForGuildReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetYuyinExamForGuildReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetYuyinExamForGuildResp struct {
	Info                 []*YuyinExamApplicationInfo `protobuf:"bytes,1,rep,name=info,proto3" json:"info,omitempty"`
	Total                uint32                      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetYuyinExamForGuildResp) Reset()         { *m = GetYuyinExamForGuildResp{} }
func (m *GetYuyinExamForGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamForGuildResp) ProtoMessage()    {}
func (*GetYuyinExamForGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{83}
}
func (m *GetYuyinExamForGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Unmarshal(m, b)
}
func (m *GetYuyinExamForGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamForGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamForGuildResp.Merge(dst, src)
}
func (m *GetYuyinExamForGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamForGuildResp.Size(m)
}
func (m *GetYuyinExamForGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamForGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamForGuildResp proto.InternalMessageInfo

func (m *GetYuyinExamForGuildResp) GetInfo() []*YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *GetYuyinExamForGuildResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type YuyinExamDetail struct {
	Info                 *YuyinExamApplicationInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Age                  uint32                    `protobuf:"varint,2,opt,name=age,proto3" json:"age,omitempty"`
	Operation            *ExamOperation            `protobuf:"bytes,3,opt,name=operation,proto3" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *YuyinExamDetail) Reset()         { *m = YuyinExamDetail{} }
func (m *YuyinExamDetail) String() string { return proto.CompactTextString(m) }
func (*YuyinExamDetail) ProtoMessage()    {}
func (*YuyinExamDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{84}
}
func (m *YuyinExamDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamDetail.Unmarshal(m, b)
}
func (m *YuyinExamDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamDetail.Marshal(b, m, deterministic)
}
func (dst *YuyinExamDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamDetail.Merge(dst, src)
}
func (m *YuyinExamDetail) XXX_Size() int {
	return xxx_messageInfo_YuyinExamDetail.Size(m)
}
func (m *YuyinExamDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamDetail.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamDetail proto.InternalMessageInfo

func (m *YuyinExamDetail) GetInfo() *YuyinExamApplicationInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

func (m *YuyinExamDetail) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *YuyinExamDetail) GetOperation() *ExamOperation {
	if m != nil {
		return m.Operation
	}
	return nil
}

// 会长查看申请记录
type ApplicationRecordForGuildOwnerReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplicationRecordForGuildOwnerReq) Reset()         { *m = ApplicationRecordForGuildOwnerReq{} }
func (m *ApplicationRecordForGuildOwnerReq) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordForGuildOwnerReq) ProtoMessage()    {}
func (*ApplicationRecordForGuildOwnerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{85}
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Unmarshal(m, b)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordForGuildOwnerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Merge(dst, src)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerReq.Size(m)
}
func (m *ApplicationRecordForGuildOwnerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordForGuildOwnerReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordForGuildOwnerReq proto.InternalMessageInfo

func (m *ApplicationRecordForGuildOwnerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *ApplicationRecordForGuildOwnerReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ApplicationRecordInfo struct {
	SubmitTime           uint32                      `protobuf:"varint,1,opt,name=submit_time,json=submitTime,proto3" json:"submit_time,omitempty"`
	Items                []*YuyinExamApplicationInfo `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *ApplicationRecordInfo) Reset()         { *m = ApplicationRecordInfo{} }
func (m *ApplicationRecordInfo) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordInfo) ProtoMessage()    {}
func (*ApplicationRecordInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{86}
}
func (m *ApplicationRecordInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordInfo.Unmarshal(m, b)
}
func (m *ApplicationRecordInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordInfo.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordInfo.Merge(dst, src)
}
func (m *ApplicationRecordInfo) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordInfo.Size(m)
}
func (m *ApplicationRecordInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordInfo proto.InternalMessageInfo

func (m *ApplicationRecordInfo) GetSubmitTime() uint32 {
	if m != nil {
		return m.SubmitTime
	}
	return 0
}

func (m *ApplicationRecordInfo) GetItems() []*YuyinExamApplicationInfo {
	if m != nil {
		return m.Items
	}
	return nil
}

type ApplicationRecordForGuildOwnerResp struct {
	List                 []*ApplicationRecordInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	HasNextPage          bool                     `protobuf:"varint,2,opt,name=has_next_page,json=hasNextPage,proto3" json:"has_next_page,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *ApplicationRecordForGuildOwnerResp) Reset()         { *m = ApplicationRecordForGuildOwnerResp{} }
func (m *ApplicationRecordForGuildOwnerResp) String() string { return proto.CompactTextString(m) }
func (*ApplicationRecordForGuildOwnerResp) ProtoMessage()    {}
func (*ApplicationRecordForGuildOwnerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{87}
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Unmarshal(m, b)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Marshal(b, m, deterministic)
}
func (dst *ApplicationRecordForGuildOwnerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Merge(dst, src)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_Size() int {
	return xxx_messageInfo_ApplicationRecordForGuildOwnerResp.Size(m)
}
func (m *ApplicationRecordForGuildOwnerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplicationRecordForGuildOwnerResp.DiscardUnknown(m)
}

var xxx_messageInfo_ApplicationRecordForGuildOwnerResp proto.InternalMessageInfo

func (m *ApplicationRecordForGuildOwnerResp) GetList() []*ApplicationRecordInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ApplicationRecordForGuildOwnerResp) GetHasNextPage() bool {
	if m != nil {
		return m.HasNextPage
	}
	return false
}

// 运营后台查看主播考核信息
type GetYuyinExamDetailReq struct {
	Uid                  uint32            `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32            `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Type                 ExamOperationType `protobuf:"varint,3,opt,name=type,proto3,enum=golddiamonn.ExamOperationType" json:"type,omitempty"`
	Offset               uint32            `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32            `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetYuyinExamDetailReq) Reset()         { *m = GetYuyinExamDetailReq{} }
func (m *GetYuyinExamDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamDetailReq) ProtoMessage()    {}
func (*GetYuyinExamDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{88}
}
func (m *GetYuyinExamDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamDetailReq.Unmarshal(m, b)
}
func (m *GetYuyinExamDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamDetailReq.Merge(dst, src)
}
func (m *GetYuyinExamDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamDetailReq.Size(m)
}
func (m *GetYuyinExamDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamDetailReq proto.InternalMessageInfo

func (m *GetYuyinExamDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetYuyinExamDetailReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetYuyinExamDetailReq) GetType() ExamOperationType {
	if m != nil {
		return m.Type
	}
	return ExamOperationType_OPERATION_DEFAULT
}

func (m *GetYuyinExamDetailReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetYuyinExamDetailReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetYuyinExamDetailResp struct {
	Detail               []*YuyinExamDetail `protobuf:"bytes,1,rep,name=detail,proto3" json:"detail,omitempty"`
	Cnt                  uint32             `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetYuyinExamDetailResp) Reset()         { *m = GetYuyinExamDetailResp{} }
func (m *GetYuyinExamDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamDetailResp) ProtoMessage()    {}
func (*GetYuyinExamDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{89}
}
func (m *GetYuyinExamDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamDetailResp.Unmarshal(m, b)
}
func (m *GetYuyinExamDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamDetailResp.Merge(dst, src)
}
func (m *GetYuyinExamDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamDetailResp.Size(m)
}
func (m *GetYuyinExamDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamDetailResp proto.InternalMessageInfo

func (m *GetYuyinExamDetailResp) GetDetail() []*YuyinExamDetail {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *GetYuyinExamDetailResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

type YuyinExamOperationReq struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32         `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Operation            *ExamOperation `protobuf:"bytes,3,opt,name=operation,proto3" json:"operation,omitempty"`
	ApplicationTime      uint32         `protobuf:"varint,4,opt,name=application_time,json=applicationTime,proto3" json:"application_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *YuyinExamOperationReq) Reset()         { *m = YuyinExamOperationReq{} }
func (m *YuyinExamOperationReq) String() string { return proto.CompactTextString(m) }
func (*YuyinExamOperationReq) ProtoMessage()    {}
func (*YuyinExamOperationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{90}
}
func (m *YuyinExamOperationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamOperationReq.Unmarshal(m, b)
}
func (m *YuyinExamOperationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamOperationReq.Marshal(b, m, deterministic)
}
func (dst *YuyinExamOperationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamOperationReq.Merge(dst, src)
}
func (m *YuyinExamOperationReq) XXX_Size() int {
	return xxx_messageInfo_YuyinExamOperationReq.Size(m)
}
func (m *YuyinExamOperationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamOperationReq.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamOperationReq proto.InternalMessageInfo

func (m *YuyinExamOperationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *YuyinExamOperationReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *YuyinExamOperationReq) GetOperation() *ExamOperation {
	if m != nil {
		return m.Operation
	}
	return nil
}

func (m *YuyinExamOperationReq) GetApplicationTime() uint32 {
	if m != nil {
		return m.ApplicationTime
	}
	return 0
}

type YuyinExamOperationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *YuyinExamOperationResp) Reset()         { *m = YuyinExamOperationResp{} }
func (m *YuyinExamOperationResp) String() string { return proto.CompactTextString(m) }
func (*YuyinExamOperationResp) ProtoMessage()    {}
func (*YuyinExamOperationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{91}
}
func (m *YuyinExamOperationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_YuyinExamOperationResp.Unmarshal(m, b)
}
func (m *YuyinExamOperationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_YuyinExamOperationResp.Marshal(b, m, deterministic)
}
func (dst *YuyinExamOperationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_YuyinExamOperationResp.Merge(dst, src)
}
func (m *YuyinExamOperationResp) XXX_Size() int {
	return xxx_messageInfo_YuyinExamOperationResp.Size(m)
}
func (m *YuyinExamOperationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_YuyinExamOperationResp.DiscardUnknown(m)
}

var xxx_messageInfo_YuyinExamOperationResp proto.InternalMessageInfo

// appraisalStatus 考核状态
type GetYuyinExamStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamStatusReq) Reset()         { *m = GetYuyinExamStatusReq{} }
func (m *GetYuyinExamStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamStatusReq) ProtoMessage()    {}
func (*GetYuyinExamStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{92}
}
func (m *GetYuyinExamStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamStatusReq.Unmarshal(m, b)
}
func (m *GetYuyinExamStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamStatusReq.Merge(dst, src)
}
func (m *GetYuyinExamStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamStatusReq.Size(m)
}
func (m *GetYuyinExamStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamStatusReq proto.InternalMessageInfo

func (m *GetYuyinExamStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetYuyinExamStatusResp struct {
	Status               int32    `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetYuyinExamStatusResp) Reset()         { *m = GetYuyinExamStatusResp{} }
func (m *GetYuyinExamStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetYuyinExamStatusResp) ProtoMessage()    {}
func (*GetYuyinExamStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{93}
}
func (m *GetYuyinExamStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetYuyinExamStatusResp.Unmarshal(m, b)
}
func (m *GetYuyinExamStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetYuyinExamStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetYuyinExamStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetYuyinExamStatusResp.Merge(dst, src)
}
func (m *GetYuyinExamStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetYuyinExamStatusResp.Size(m)
}
func (m *GetYuyinExamStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetYuyinExamStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetYuyinExamStatusResp proto.InternalMessageInfo

func (m *GetYuyinExamStatusResp) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 删除语音主播申请的考核 会长提交后不用删
type DelYuyinExamApplicationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelYuyinExamApplicationReq) Reset()         { *m = DelYuyinExamApplicationReq{} }
func (m *DelYuyinExamApplicationReq) String() string { return proto.CompactTextString(m) }
func (*DelYuyinExamApplicationReq) ProtoMessage()    {}
func (*DelYuyinExamApplicationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{94}
}
func (m *DelYuyinExamApplicationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelYuyinExamApplicationReq.Unmarshal(m, b)
}
func (m *DelYuyinExamApplicationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelYuyinExamApplicationReq.Marshal(b, m, deterministic)
}
func (dst *DelYuyinExamApplicationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelYuyinExamApplicationReq.Merge(dst, src)
}
func (m *DelYuyinExamApplicationReq) XXX_Size() int {
	return xxx_messageInfo_DelYuyinExamApplicationReq.Size(m)
}
func (m *DelYuyinExamApplicationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelYuyinExamApplicationReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelYuyinExamApplicationReq proto.InternalMessageInfo

func (m *DelYuyinExamApplicationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DelYuyinExamApplicationReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type DelYuyinExamApplicationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelYuyinExamApplicationResp) Reset()         { *m = DelYuyinExamApplicationResp{} }
func (m *DelYuyinExamApplicationResp) String() string { return proto.CompactTextString(m) }
func (*DelYuyinExamApplicationResp) ProtoMessage()    {}
func (*DelYuyinExamApplicationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{95}
}
func (m *DelYuyinExamApplicationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelYuyinExamApplicationResp.Unmarshal(m, b)
}
func (m *DelYuyinExamApplicationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelYuyinExamApplicationResp.Marshal(b, m, deterministic)
}
func (dst *DelYuyinExamApplicationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelYuyinExamApplicationResp.Merge(dst, src)
}
func (m *DelYuyinExamApplicationResp) XXX_Size() int {
	return xxx_messageInfo_DelYuyinExamApplicationResp.Size(m)
}
func (m *DelYuyinExamApplicationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelYuyinExamApplicationResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelYuyinExamApplicationResp proto.InternalMessageInfo

type GetGuildTypeReq struct {
	Guildid              uint32   `protobuf:"varint,1,opt,name=guildid,proto3" json:"guildid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTypeReq) Reset()         { *m = GetGuildTypeReq{} }
func (m *GetGuildTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeReq) ProtoMessage()    {}
func (*GetGuildTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{96}
}
func (m *GetGuildTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeReq.Unmarshal(m, b)
}
func (m *GetGuildTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeReq.Merge(dst, src)
}
func (m *GetGuildTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeReq.Size(m)
}
func (m *GetGuildTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeReq proto.InternalMessageInfo

func (m *GetGuildTypeReq) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type GetGuildTypeRsp struct {
	MultiGuildType       uint32   `protobuf:"varint,1,opt,name=multi_guild_type,json=multiGuildType,proto3" json:"multi_guild_type,omitempty"`
	YuyinGuildType       uint32   `protobuf:"varint,2,opt,name=yuyin_guild_type,json=yuyinGuildType,proto3" json:"yuyin_guild_type,omitempty"`
	MultiApp             uint32   `protobuf:"varint,3,opt,name=multi_app,json=multiApp,proto3" json:"multi_app,omitempty"`
	YuyinApp             uint32   `protobuf:"varint,4,opt,name=yuyin_app,json=yuyinApp,proto3" json:"yuyin_app,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTypeRsp) Reset()         { *m = GetGuildTypeRsp{} }
func (m *GetGuildTypeRsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeRsp) ProtoMessage()    {}
func (*GetGuildTypeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{97}
}
func (m *GetGuildTypeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeRsp.Unmarshal(m, b)
}
func (m *GetGuildTypeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeRsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeRsp.Merge(dst, src)
}
func (m *GetGuildTypeRsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeRsp.Size(m)
}
func (m *GetGuildTypeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeRsp proto.InternalMessageInfo

func (m *GetGuildTypeRsp) GetMultiGuildType() uint32 {
	if m != nil {
		return m.MultiGuildType
	}
	return 0
}

func (m *GetGuildTypeRsp) GetYuyinGuildType() uint32 {
	if m != nil {
		return m.YuyinGuildType
	}
	return 0
}

func (m *GetGuildTypeRsp) GetMultiApp() uint32 {
	if m != nil {
		return m.MultiApp
	}
	return 0
}

func (m *GetGuildTypeRsp) GetYuyinApp() uint32 {
	if m != nil {
		return m.YuyinApp
	}
	return 0
}

type GetGuildInfoV2Rsp struct {
	ServerTime           int64    `protobuf:"varint,1,opt,name=server_time,json=serverTime,proto3" json:"server_time,omitempty"`
	GuildType            uint32   `protobuf:"varint,2,opt,name=guild_type,json=guildType,proto3" json:"guild_type,omitempty"`
	IsApplication        bool     `protobuf:"varint,3,opt,name=is_application,json=isApplication,proto3" json:"is_application,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildInfoV2Rsp) Reset()         { *m = GetGuildInfoV2Rsp{} }
func (m *GetGuildInfoV2Rsp) String() string { return proto.CompactTextString(m) }
func (*GetGuildInfoV2Rsp) ProtoMessage()    {}
func (*GetGuildInfoV2Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{98}
}
func (m *GetGuildInfoV2Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildInfoV2Rsp.Unmarshal(m, b)
}
func (m *GetGuildInfoV2Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildInfoV2Rsp.Marshal(b, m, deterministic)
}
func (dst *GetGuildInfoV2Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildInfoV2Rsp.Merge(dst, src)
}
func (m *GetGuildInfoV2Rsp) XXX_Size() int {
	return xxx_messageInfo_GetGuildInfoV2Rsp.Size(m)
}
func (m *GetGuildInfoV2Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildInfoV2Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildInfoV2Rsp proto.InternalMessageInfo

func (m *GetGuildInfoV2Rsp) GetServerTime() int64 {
	if m != nil {
		return m.ServerTime
	}
	return 0
}

func (m *GetGuildInfoV2Rsp) GetGuildType() uint32 {
	if m != nil {
		return m.GuildType
	}
	return 0
}

func (m *GetGuildInfoV2Rsp) GetIsApplication() bool {
	if m != nil {
		return m.IsApplication
	}
	return false
}

type AmuseLastIncomeTrendRsp struct {
	LastMonthDayList     []*DayTrendInfo `protobuf:"bytes,1,rep,name=last_month_day_list,json=lastMonthDayList,proto3" json:"last_month_day_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AmuseLastIncomeTrendRsp) Reset()         { *m = AmuseLastIncomeTrendRsp{} }
func (m *AmuseLastIncomeTrendRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseLastIncomeTrendRsp) ProtoMessage()    {}
func (*AmuseLastIncomeTrendRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{99}
}
func (m *AmuseLastIncomeTrendRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseLastIncomeTrendRsp.Unmarshal(m, b)
}
func (m *AmuseLastIncomeTrendRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseLastIncomeTrendRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseLastIncomeTrendRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseLastIncomeTrendRsp.Merge(dst, src)
}
func (m *AmuseLastIncomeTrendRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseLastIncomeTrendRsp.Size(m)
}
func (m *AmuseLastIncomeTrendRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseLastIncomeTrendRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseLastIncomeTrendRsp proto.InternalMessageInfo

func (m *AmuseLastIncomeTrendRsp) GetLastMonthDayList() []*DayTrendInfo {
	if m != nil {
		return m.LastMonthDayList
	}
	return nil
}

type AmuseIncomeTrendRsp struct {
	DayTrendInfo         []*DayTrendInfo `protobuf:"bytes,1,rep,name=day_trend_info,json=dayTrendInfo,proto3" json:"day_trend_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *AmuseIncomeTrendRsp) Reset()         { *m = AmuseIncomeTrendRsp{} }
func (m *AmuseIncomeTrendRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseIncomeTrendRsp) ProtoMessage()    {}
func (*AmuseIncomeTrendRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{100}
}
func (m *AmuseIncomeTrendRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseIncomeTrendRsp.Unmarshal(m, b)
}
func (m *AmuseIncomeTrendRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseIncomeTrendRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseIncomeTrendRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseIncomeTrendRsp.Merge(dst, src)
}
func (m *AmuseIncomeTrendRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseIncomeTrendRsp.Size(m)
}
func (m *AmuseIncomeTrendRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseIncomeTrendRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseIncomeTrendRsp proto.InternalMessageInfo

func (m *AmuseIncomeTrendRsp) GetDayTrendInfo() []*DayTrendInfo {
	if m != nil {
		return m.DayTrendInfo
	}
	return nil
}

type AmuseIncomeRsp struct {
	TodayIncome          int64    `protobuf:"varint,1,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	YesterdayIncome      int64    `protobuf:"varint,2,opt,name=yesterday_income,json=yesterdayIncome,proto3" json:"yesterday_income,omitempty"`
	ThismonthIncome      int64    `protobuf:"varint,3,opt,name=thismonth_income,json=thismonthIncome,proto3" json:"thismonth_income,omitempty"`
	LastmonthIncome      int64    `protobuf:"varint,4,opt,name=lastmonth_income,json=lastmonthIncome,proto3" json:"lastmonth_income,omitempty"`
	DayQoq               float32  `protobuf:"fixed32,5,opt,name=day_qoq,json=dayQoq,proto3" json:"day_qoq,omitempty"`
	MonthQoq             float32  `protobuf:"fixed32,6,opt,name=month_qoq,json=monthQoq,proto3" json:"month_qoq,omitempty"`
	LastdayQoq           float32  `protobuf:"fixed32,7,opt,name=lastday_qoq,json=lastdayQoq,proto3" json:"lastday_qoq,omitempty"`
	MonthTonowQoq        float32  `protobuf:"fixed32,8,opt,name=month_tonow_qoq,json=monthTonowQoq,proto3" json:"month_tonow_qoq,omitempty"`
	LastMonthSamePeriod  int64    `protobuf:"varint,9,opt,name=last_month_same_period,json=lastMonthSamePeriod,proto3" json:"last_month_same_period,omitempty"`
	MonthSixIncome       int64    `protobuf:"varint,10,opt,name=month_six_income,json=monthSixIncome,proto3" json:"month_six_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseIncomeRsp) Reset()         { *m = AmuseIncomeRsp{} }
func (m *AmuseIncomeRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseIncomeRsp) ProtoMessage()    {}
func (*AmuseIncomeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{101}
}
func (m *AmuseIncomeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseIncomeRsp.Unmarshal(m, b)
}
func (m *AmuseIncomeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseIncomeRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseIncomeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseIncomeRsp.Merge(dst, src)
}
func (m *AmuseIncomeRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseIncomeRsp.Size(m)
}
func (m *AmuseIncomeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseIncomeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseIncomeRsp proto.InternalMessageInfo

func (m *AmuseIncomeRsp) GetTodayIncome() int64 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetYesterdayIncome() int64 {
	if m != nil {
		return m.YesterdayIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetThismonthIncome() int64 {
	if m != nil {
		return m.ThismonthIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastmonthIncome() int64 {
	if m != nil {
		return m.LastmonthIncome
	}
	return 0
}

func (m *AmuseIncomeRsp) GetDayQoq() float32 {
	if m != nil {
		return m.DayQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetMonthQoq() float32 {
	if m != nil {
		return m.MonthQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastdayQoq() float32 {
	if m != nil {
		return m.LastdayQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetMonthTonowQoq() float32 {
	if m != nil {
		return m.MonthTonowQoq
	}
	return 0
}

func (m *AmuseIncomeRsp) GetLastMonthSamePeriod() int64 {
	if m != nil {
		return m.LastMonthSamePeriod
	}
	return 0
}

func (m *AmuseIncomeRsp) GetMonthSixIncome() int64 {
	if m != nil {
		return m.MonthSixIncome
	}
	return 0
}

type AmuseChannelRsp struct {
	RoomidFees           []*RoomIdFee `protobuf:"bytes,5,rep,name=roomid_fees,json=roomidFees,proto3" json:"roomid_fees,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *AmuseChannelRsp) Reset()         { *m = AmuseChannelRsp{} }
func (m *AmuseChannelRsp) String() string { return proto.CompactTextString(m) }
func (*AmuseChannelRsp) ProtoMessage()    {}
func (*AmuseChannelRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{102}
}
func (m *AmuseChannelRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseChannelRsp.Unmarshal(m, b)
}
func (m *AmuseChannelRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseChannelRsp.Marshal(b, m, deterministic)
}
func (dst *AmuseChannelRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseChannelRsp.Merge(dst, src)
}
func (m *AmuseChannelRsp) XXX_Size() int {
	return xxx_messageInfo_AmuseChannelRsp.Size(m)
}
func (m *AmuseChannelRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseChannelRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseChannelRsp proto.InternalMessageInfo

func (m *AmuseChannelRsp) GetRoomidFees() []*RoomIdFee {
	if m != nil {
		return m.RoomidFees
	}
	return nil
}

type GetAllHandlePresentCountReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllHandlePresentCountReq) Reset()         { *m = GetAllHandlePresentCountReq{} }
func (m *GetAllHandlePresentCountReq) String() string { return proto.CompactTextString(m) }
func (*GetAllHandlePresentCountReq) ProtoMessage()    {}
func (*GetAllHandlePresentCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{103}
}
func (m *GetAllHandlePresentCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllHandlePresentCountReq.Unmarshal(m, b)
}
func (m *GetAllHandlePresentCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllHandlePresentCountReq.Marshal(b, m, deterministic)
}
func (dst *GetAllHandlePresentCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllHandlePresentCountReq.Merge(dst, src)
}
func (m *GetAllHandlePresentCountReq) XXX_Size() int {
	return xxx_messageInfo_GetAllHandlePresentCountReq.Size(m)
}
func (m *GetAllHandlePresentCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllHandlePresentCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllHandlePresentCountReq proto.InternalMessageInfo

func (m *GetAllHandlePresentCountReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAllHandlePresentCountReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAllHandlePresentCountResp struct {
	TotalCount           uint32   `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllHandlePresentCountResp) Reset()         { *m = GetAllHandlePresentCountResp{} }
func (m *GetAllHandlePresentCountResp) String() string { return proto.CompactTextString(m) }
func (*GetAllHandlePresentCountResp) ProtoMessage()    {}
func (*GetAllHandlePresentCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{104}
}
func (m *GetAllHandlePresentCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllHandlePresentCountResp.Unmarshal(m, b)
}
func (m *GetAllHandlePresentCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllHandlePresentCountResp.Marshal(b, m, deterministic)
}
func (dst *GetAllHandlePresentCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllHandlePresentCountResp.Merge(dst, src)
}
func (m *GetAllHandlePresentCountResp) XXX_Size() int {
	return xxx_messageInfo_GetAllHandlePresentCountResp.Size(m)
}
func (m *GetAllHandlePresentCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllHandlePresentCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllHandlePresentCountResp proto.InternalMessageInfo

func (m *GetAllHandlePresentCountResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetAllHandlePresentOrderListReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllHandlePresentOrderListReq) Reset()         { *m = GetAllHandlePresentOrderListReq{} }
func (m *GetAllHandlePresentOrderListReq) String() string { return proto.CompactTextString(m) }
func (*GetAllHandlePresentOrderListReq) ProtoMessage()    {}
func (*GetAllHandlePresentOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{105}
}
func (m *GetAllHandlePresentOrderListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllHandlePresentOrderListReq.Unmarshal(m, b)
}
func (m *GetAllHandlePresentOrderListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllHandlePresentOrderListReq.Marshal(b, m, deterministic)
}
func (dst *GetAllHandlePresentOrderListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllHandlePresentOrderListReq.Merge(dst, src)
}
func (m *GetAllHandlePresentOrderListReq) XXX_Size() int {
	return xxx_messageInfo_GetAllHandlePresentOrderListReq.Size(m)
}
func (m *GetAllHandlePresentOrderListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllHandlePresentOrderListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllHandlePresentOrderListReq proto.InternalMessageInfo

func (m *GetAllHandlePresentOrderListReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAllHandlePresentOrderListReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAllHandlePresentOrderListResp struct {
	Orders               []string `protobuf:"bytes,1,rep,name=orders,proto3" json:"orders,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllHandlePresentOrderListResp) Reset()         { *m = GetAllHandlePresentOrderListResp{} }
func (m *GetAllHandlePresentOrderListResp) String() string { return proto.CompactTextString(m) }
func (*GetAllHandlePresentOrderListResp) ProtoMessage()    {}
func (*GetAllHandlePresentOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{106}
}
func (m *GetAllHandlePresentOrderListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllHandlePresentOrderListResp.Unmarshal(m, b)
}
func (m *GetAllHandlePresentOrderListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllHandlePresentOrderListResp.Marshal(b, m, deterministic)
}
func (dst *GetAllHandlePresentOrderListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllHandlePresentOrderListResp.Merge(dst, src)
}
func (m *GetAllHandlePresentOrderListResp) XXX_Size() int {
	return xxx_messageInfo_GetAllHandlePresentOrderListResp.Size(m)
}
func (m *GetAllHandlePresentOrderListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllHandlePresentOrderListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllHandlePresentOrderListResp proto.InternalMessageInfo

func (m *GetAllHandlePresentOrderListResp) GetOrders() []string {
	if m != nil {
		return m.Orders
	}
	return nil
}

type OrderInfoT struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	OrderId              string   `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,5,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	GuildId              uint32   `protobuf:"varint,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SendTime             uint32   `protobuf:"varint,7,opt,name=send_time,json=sendTime,proto3" json:"send_time,omitempty"`
	ItemId               uint32   `protobuf:"varint,8,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCount            uint32   `protobuf:"varint,9,opt,name=item_count,json=itemCount,proto3" json:"item_count,omitempty"`
	Price                uint32   `protobuf:"varint,10,opt,name=price,proto3" json:"price,omitempty"`
	PriceType            uint32   `protobuf:"varint,11,opt,name=price_type,json=priceType,proto3" json:"price_type,omitempty"`
	DealToken            string   `protobuf:"bytes,12,opt,name=deal_token,json=dealToken,proto3" json:"deal_token,omitempty"`
	IsUkwPaid            bool     `protobuf:"varint,13,opt,name=is_ukw_paid,json=isUkwPaid,proto3" json:"is_ukw_paid,omitempty"`
	IsUkwTarget          bool     `protobuf:"varint,14,opt,name=is_ukw_target,json=isUkwTarget,proto3" json:"is_ukw_target,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderInfoT) Reset()         { *m = OrderInfoT{} }
func (m *OrderInfoT) String() string { return proto.CompactTextString(m) }
func (*OrderInfoT) ProtoMessage()    {}
func (*OrderInfoT) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{107}
}
func (m *OrderInfoT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderInfoT.Unmarshal(m, b)
}
func (m *OrderInfoT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderInfoT.Marshal(b, m, deterministic)
}
func (dst *OrderInfoT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderInfoT.Merge(dst, src)
}
func (m *OrderInfoT) XXX_Size() int {
	return xxx_messageInfo_OrderInfoT.Size(m)
}
func (m *OrderInfoT) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderInfoT.DiscardUnknown(m)
}

var xxx_messageInfo_OrderInfoT proto.InternalMessageInfo

func (m *OrderInfoT) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OrderInfoT) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *OrderInfoT) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderInfoT) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OrderInfoT) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *OrderInfoT) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *OrderInfoT) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *OrderInfoT) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *OrderInfoT) GetItemCount() uint32 {
	if m != nil {
		return m.ItemCount
	}
	return 0
}

func (m *OrderInfoT) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *OrderInfoT) GetPriceType() uint32 {
	if m != nil {
		return m.PriceType
	}
	return 0
}

func (m *OrderInfoT) GetDealToken() string {
	if m != nil {
		return m.DealToken
	}
	return ""
}

func (m *OrderInfoT) GetIsUkwPaid() bool {
	if m != nil {
		return m.IsUkwPaid
	}
	return false
}

func (m *OrderInfoT) GetIsUkwTarget() bool {
	if m != nil {
		return m.IsUkwTarget
	}
	return false
}

type AddOrderReq struct {
	AddOrderInfo         *OrderInfoT `protobuf:"bytes,1,opt,name=add_order_info,json=addOrderInfo,proto3" json:"add_order_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddOrderReq) Reset()         { *m = AddOrderReq{} }
func (m *AddOrderReq) String() string { return proto.CompactTextString(m) }
func (*AddOrderReq) ProtoMessage()    {}
func (*AddOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{108}
}
func (m *AddOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOrderReq.Unmarshal(m, b)
}
func (m *AddOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOrderReq.Marshal(b, m, deterministic)
}
func (dst *AddOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOrderReq.Merge(dst, src)
}
func (m *AddOrderReq) XXX_Size() int {
	return xxx_messageInfo_AddOrderReq.Size(m)
}
func (m *AddOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddOrderReq proto.InternalMessageInfo

func (m *AddOrderReq) GetAddOrderInfo() *OrderInfoT {
	if m != nil {
		return m.AddOrderInfo
	}
	return nil
}

type AddOrderResp struct {
	Ret                  uint32   `protobuf:"varint,2,opt,name=ret,proto3" json:"ret,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddOrderResp) Reset()         { *m = AddOrderResp{} }
func (m *AddOrderResp) String() string { return proto.CompactTextString(m) }
func (*AddOrderResp) ProtoMessage()    {}
func (*AddOrderResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{109}
}
func (m *AddOrderResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddOrderResp.Unmarshal(m, b)
}
func (m *AddOrderResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddOrderResp.Marshal(b, m, deterministic)
}
func (dst *AddOrderResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddOrderResp.Merge(dst, src)
}
func (m *AddOrderResp) XXX_Size() int {
	return xxx_messageInfo_AddOrderResp.Size(m)
}
func (m *AddOrderResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddOrderResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddOrderResp proto.InternalMessageInfo

func (m *AddOrderResp) GetRet() uint32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

type GetGuildTypeInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SnapshotTime         int64    `protobuf:"varint,2,opt,name=snapshot_time,json=snapshotTime,proto3" json:"snapshot_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTypeInfoReq) Reset()         { *m = GetGuildTypeInfoReq{} }
func (m *GetGuildTypeInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeInfoReq) ProtoMessage()    {}
func (*GetGuildTypeInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{110}
}
func (m *GetGuildTypeInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeInfoReq.Unmarshal(m, b)
}
func (m *GetGuildTypeInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeInfoReq.Merge(dst, src)
}
func (m *GetGuildTypeInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeInfoReq.Size(m)
}
func (m *GetGuildTypeInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeInfoReq proto.InternalMessageInfo

func (m *GetGuildTypeInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildTypeInfoReq) GetSnapshotTime() int64 {
	if m != nil {
		return m.SnapshotTime
	}
	return 0
}

type GetGuildTypeInfoResp struct {
	Guildtype            uint32   `protobuf:"varint,1,opt,name=guildtype,proto3" json:"guildtype,omitempty"`
	LibraryType          uint32   `protobuf:"varint,2,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	IsExist              bool     `protobuf:"varint,3,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTypeInfoResp) Reset()         { *m = GetGuildTypeInfoResp{} }
func (m *GetGuildTypeInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeInfoResp) ProtoMessage()    {}
func (*GetGuildTypeInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{111}
}
func (m *GetGuildTypeInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeInfoResp.Unmarshal(m, b)
}
func (m *GetGuildTypeInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeInfoResp.Merge(dst, src)
}
func (m *GetGuildTypeInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeInfoResp.Size(m)
}
func (m *GetGuildTypeInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeInfoResp proto.InternalMessageInfo

func (m *GetGuildTypeInfoResp) GetGuildtype() uint32 {
	if m != nil {
		return m.Guildtype
	}
	return 0
}

func (m *GetGuildTypeInfoResp) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

func (m *GetGuildTypeInfoResp) GetIsExist() bool {
	if m != nil {
		return m.IsExist
	}
	return false
}

type GetGuildTypeHistoryReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTypeHistoryReq) Reset()         { *m = GetGuildTypeHistoryReq{} }
func (m *GetGuildTypeHistoryReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeHistoryReq) ProtoMessage()    {}
func (*GetGuildTypeHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{112}
}
func (m *GetGuildTypeHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeHistoryReq.Unmarshal(m, b)
}
func (m *GetGuildTypeHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeHistoryReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeHistoryReq.Merge(dst, src)
}
func (m *GetGuildTypeHistoryReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeHistoryReq.Size(m)
}
func (m *GetGuildTypeHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeHistoryReq proto.InternalMessageInfo

func (m *GetGuildTypeHistoryReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildTypeHistoryResp struct {
	// 不在记录中的时间点 都是解约的
	GuildTypeList        []*GetGuildTypeHistoryResp_GuildTypeItem `protobuf:"bytes,1,rep,name=guild_type_list,json=guildTypeList,proto3" json:"guild_type_list,omitempty"`
	SnapshotTimeUnix     int64                                    `protobuf:"varint,2,opt,name=snapshot_time_unix,json=snapshotTimeUnix,proto3" json:"snapshot_time_unix,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetGuildTypeHistoryResp) Reset()         { *m = GetGuildTypeHistoryResp{} }
func (m *GetGuildTypeHistoryResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeHistoryResp) ProtoMessage()    {}
func (*GetGuildTypeHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{113}
}
func (m *GetGuildTypeHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeHistoryResp.Unmarshal(m, b)
}
func (m *GetGuildTypeHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeHistoryResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeHistoryResp.Merge(dst, src)
}
func (m *GetGuildTypeHistoryResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeHistoryResp.Size(m)
}
func (m *GetGuildTypeHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeHistoryResp proto.InternalMessageInfo

func (m *GetGuildTypeHistoryResp) GetGuildTypeList() []*GetGuildTypeHistoryResp_GuildTypeItem {
	if m != nil {
		return m.GuildTypeList
	}
	return nil
}

func (m *GetGuildTypeHistoryResp) GetSnapshotTimeUnix() int64 {
	if m != nil {
		return m.SnapshotTimeUnix
	}
	return 0
}

type GetGuildTypeHistoryResp_GuildTypeItem struct {
	Guildtype            uint32   `protobuf:"varint,1,opt,name=guildtype,proto3" json:"guildtype,omitempty"`
	LibraryType          uint32   `protobuf:"varint,2,opt,name=library_type,json=libraryType,proto3" json:"library_type,omitempty"`
	BeginTimeUnix        int64    `protobuf:"varint,3,opt,name=begin_time_unix,json=beginTimeUnix,proto3" json:"begin_time_unix,omitempty"`
	EndTimeUnix          int64    `protobuf:"varint,4,opt,name=end_time_unix,json=endTimeUnix,proto3" json:"end_time_unix,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildTypeHistoryResp_GuildTypeItem) Reset()         { *m = GetGuildTypeHistoryResp_GuildTypeItem{} }
func (m *GetGuildTypeHistoryResp_GuildTypeItem) String() string { return proto.CompactTextString(m) }
func (*GetGuildTypeHistoryResp_GuildTypeItem) ProtoMessage()    {}
func (*GetGuildTypeHistoryResp_GuildTypeItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{113, 0}
}
func (m *GetGuildTypeHistoryResp_GuildTypeItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildTypeHistoryResp_GuildTypeItem.Unmarshal(m, b)
}
func (m *GetGuildTypeHistoryResp_GuildTypeItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildTypeHistoryResp_GuildTypeItem.Marshal(b, m, deterministic)
}
func (dst *GetGuildTypeHistoryResp_GuildTypeItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildTypeHistoryResp_GuildTypeItem.Merge(dst, src)
}
func (m *GetGuildTypeHistoryResp_GuildTypeItem) XXX_Size() int {
	return xxx_messageInfo_GetGuildTypeHistoryResp_GuildTypeItem.Size(m)
}
func (m *GetGuildTypeHistoryResp_GuildTypeItem) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildTypeHistoryResp_GuildTypeItem.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildTypeHistoryResp_GuildTypeItem proto.InternalMessageInfo

func (m *GetGuildTypeHistoryResp_GuildTypeItem) GetGuildtype() uint32 {
	if m != nil {
		return m.Guildtype
	}
	return 0
}

func (m *GetGuildTypeHistoryResp_GuildTypeItem) GetLibraryType() uint32 {
	if m != nil {
		return m.LibraryType
	}
	return 0
}

func (m *GetGuildTypeHistoryResp_GuildTypeItem) GetBeginTimeUnix() int64 {
	if m != nil {
		return m.BeginTimeUnix
	}
	return 0
}

func (m *GetGuildTypeHistoryResp_GuildTypeItem) GetEndTimeUnix() int64 {
	if m != nil {
		return m.EndTimeUnix
	}
	return 0
}

type GetAmuseChannelsIncomeReq struct {
	GuildIds             []uint32        `protobuf:"varint,1,rep,packed,name=guild_ids,json=guildIds,proto3" json:"guild_ids,omitempty"`
	Offset               uint32          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	Unit                 TimeFilterUnit  `protobuf:"varint,4,opt,name=unit,proto3,enum=golddiamonn.TimeFilterUnit" json:"unit,omitempty"`
	SettleStatus         ReqSettleStatus `protobuf:"varint,5,opt,name=settle_status,json=settleStatus,proto3,enum=golddiamonn.ReqSettleStatus" json:"settle_status,omitempty"`
	BeginTime            uint32          `protobuf:"varint,6,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32          `protobuf:"varint,7,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAmuseChannelsIncomeReq) Reset()         { *m = GetAmuseChannelsIncomeReq{} }
func (m *GetAmuseChannelsIncomeReq) String() string { return proto.CompactTextString(m) }
func (*GetAmuseChannelsIncomeReq) ProtoMessage()    {}
func (*GetAmuseChannelsIncomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{114}
}
func (m *GetAmuseChannelsIncomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseChannelsIncomeReq.Unmarshal(m, b)
}
func (m *GetAmuseChannelsIncomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseChannelsIncomeReq.Marshal(b, m, deterministic)
}
func (dst *GetAmuseChannelsIncomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseChannelsIncomeReq.Merge(dst, src)
}
func (m *GetAmuseChannelsIncomeReq) XXX_Size() int {
	return xxx_messageInfo_GetAmuseChannelsIncomeReq.Size(m)
}
func (m *GetAmuseChannelsIncomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseChannelsIncomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseChannelsIncomeReq proto.InternalMessageInfo

func (m *GetAmuseChannelsIncomeReq) GetGuildIds() []uint32 {
	if m != nil {
		return m.GuildIds
	}
	return nil
}

func (m *GetAmuseChannelsIncomeReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAmuseChannelsIncomeReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAmuseChannelsIncomeReq) GetUnit() TimeFilterUnit {
	if m != nil {
		return m.Unit
	}
	return TimeFilterUnit_BY_DAY
}

func (m *GetAmuseChannelsIncomeReq) GetSettleStatus() ReqSettleStatus {
	if m != nil {
		return m.SettleStatus
	}
	return ReqSettleStatus_ReqSettleStatusALL
}

func (m *GetAmuseChannelsIncomeReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetAmuseChannelsIncomeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetAmuseChannelsIncomeResp struct {
	Rooms                []*GuildRoomIncomeDetail `protobuf:"bytes,1,rep,name=rooms,proto3" json:"rooms,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAmuseChannelsIncomeResp) Reset()         { *m = GetAmuseChannelsIncomeResp{} }
func (m *GetAmuseChannelsIncomeResp) String() string { return proto.CompactTextString(m) }
func (*GetAmuseChannelsIncomeResp) ProtoMessage()    {}
func (*GetAmuseChannelsIncomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{115}
}
func (m *GetAmuseChannelsIncomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAmuseChannelsIncomeResp.Unmarshal(m, b)
}
func (m *GetAmuseChannelsIncomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAmuseChannelsIncomeResp.Marshal(b, m, deterministic)
}
func (dst *GetAmuseChannelsIncomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAmuseChannelsIncomeResp.Merge(dst, src)
}
func (m *GetAmuseChannelsIncomeResp) XXX_Size() int {
	return xxx_messageInfo_GetAmuseChannelsIncomeResp.Size(m)
}
func (m *GetAmuseChannelsIncomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAmuseChannelsIncomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAmuseChannelsIncomeResp proto.InternalMessageInfo

func (m *GetAmuseChannelsIncomeResp) GetRooms() []*GuildRoomIncomeDetail {
	if m != nil {
		return m.Rooms
	}
	return nil
}

type AmuseChannelListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            []uint32 `protobuf:"varint,2,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseChannelListReq) Reset()         { *m = AmuseChannelListReq{} }
func (m *AmuseChannelListReq) String() string { return proto.CompactTextString(m) }
func (*AmuseChannelListReq) ProtoMessage()    {}
func (*AmuseChannelListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{116}
}
func (m *AmuseChannelListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseChannelListReq.Unmarshal(m, b)
}
func (m *AmuseChannelListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseChannelListReq.Marshal(b, m, deterministic)
}
func (dst *AmuseChannelListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseChannelListReq.Merge(dst, src)
}
func (m *AmuseChannelListReq) XXX_Size() int {
	return xxx_messageInfo_AmuseChannelListReq.Size(m)
}
func (m *AmuseChannelListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseChannelListReq.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseChannelListReq proto.InternalMessageInfo

func (m *AmuseChannelListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AmuseChannelListReq) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *AmuseChannelListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *AmuseChannelListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type AmuseChannelItem struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Fee                  uint64   `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	SamePeriodFee        uint64   `protobuf:"varint,3,opt,name=same_period_fee,json=samePeriodFee,proto3" json:"same_period_fee,omitempty"`
	SamePeriodQoq        float32  `protobuf:"fixed32,4,opt,name=same_period_qoq,json=samePeriodQoq,proto3" json:"same_period_qoq,omitempty"`
	LastMonthFee         uint64   `protobuf:"varint,5,opt,name=last_month_fee,json=lastMonthFee,proto3" json:"last_month_fee,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AmuseChannelItem) Reset()         { *m = AmuseChannelItem{} }
func (m *AmuseChannelItem) String() string { return proto.CompactTextString(m) }
func (*AmuseChannelItem) ProtoMessage()    {}
func (*AmuseChannelItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{117}
}
func (m *AmuseChannelItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseChannelItem.Unmarshal(m, b)
}
func (m *AmuseChannelItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseChannelItem.Marshal(b, m, deterministic)
}
func (dst *AmuseChannelItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseChannelItem.Merge(dst, src)
}
func (m *AmuseChannelItem) XXX_Size() int {
	return xxx_messageInfo_AmuseChannelItem.Size(m)
}
func (m *AmuseChannelItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseChannelItem.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseChannelItem proto.InternalMessageInfo

func (m *AmuseChannelItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AmuseChannelItem) GetFee() uint64 {
	if m != nil {
		return m.Fee
	}
	return 0
}

func (m *AmuseChannelItem) GetSamePeriodFee() uint64 {
	if m != nil {
		return m.SamePeriodFee
	}
	return 0
}

func (m *AmuseChannelItem) GetSamePeriodQoq() float32 {
	if m != nil {
		return m.SamePeriodQoq
	}
	return 0
}

func (m *AmuseChannelItem) GetLastMonthFee() uint64 {
	if m != nil {
		return m.LastMonthFee
	}
	return 0
}

type AmuseChannelListResp struct {
	ChannelList          []*AmuseChannelItem `protobuf:"bytes,1,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	Total                uint32              `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AmuseChannelListResp) Reset()         { *m = AmuseChannelListResp{} }
func (m *AmuseChannelListResp) String() string { return proto.CompactTextString(m) }
func (*AmuseChannelListResp) ProtoMessage()    {}
func (*AmuseChannelListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_golddiamonn_16e8414721ab9ede, []int{118}
}
func (m *AmuseChannelListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AmuseChannelListResp.Unmarshal(m, b)
}
func (m *AmuseChannelListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AmuseChannelListResp.Marshal(b, m, deterministic)
}
func (dst *AmuseChannelListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AmuseChannelListResp.Merge(dst, src)
}
func (m *AmuseChannelListResp) XXX_Size() int {
	return xxx_messageInfo_AmuseChannelListResp.Size(m)
}
func (m *AmuseChannelListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AmuseChannelListResp.DiscardUnknown(m)
}

var xxx_messageInfo_AmuseChannelListResp proto.InternalMessageInfo

func (m *AmuseChannelListResp) GetChannelList() []*AmuseChannelItem {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

func (m *AmuseChannelListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func init() {
	proto.RegisterType((*GuildApplicationInfo)(nil), "golddiamonn.GuildApplicationInfo")
	proto.RegisterType((*GuildApplicationReq)(nil), "golddiamonn.GuildApplicationReq")
	proto.RegisterType((*GuildApplicationRsp)(nil), "golddiamonn.GuildApplicationRsp")
	proto.RegisterType((*ApplicationListReq)(nil), "golddiamonn.ApplicationListReq")
	proto.RegisterType((*ApplicationListRsp)(nil), "golddiamonn.ApplicationListRsp")
	proto.RegisterType((*ApplicationOpeReq)(nil), "golddiamonn.ApplicationOpeReq")
	proto.RegisterType((*ApplicationOpeRsp)(nil), "golddiamonn.ApplicationOpeRsp")
	proto.RegisterType((*OperateInfoT)(nil), "golddiamonn.OperateInfoT")
	proto.RegisterType((*ApplicationCreateReq)(nil), "golddiamonn.ApplicationCreateReq")
	proto.RegisterType((*ApplicationCreateRsp)(nil), "golddiamonn.ApplicationCreateRsp")
	proto.RegisterType((*GuildInfoReq)(nil), "golddiamonn.GuildInfoReq")
	proto.RegisterType((*GuildInfoRsp)(nil), "golddiamonn.GuildInfoRsp")
	proto.RegisterType((*GuildListReq)(nil), "golddiamonn.GuildListReq")
	proto.RegisterType((*GuildListRsp)(nil), "golddiamonn.GuildListRsp")
	proto.RegisterType((*GuildInfoOpeReq)(nil), "golddiamonn.GuildInfoOpeReq")
	proto.RegisterType((*GuildInfoOpeRsp)(nil), "golddiamonn.GuildInfoOpeRsp")
	proto.RegisterType((*AwardDiamondReq)(nil), "golddiamonn.AwardDiamondReq")
	proto.RegisterType((*AwardDiamondRsp)(nil), "golddiamonn.AwardDiamondRsp")
	proto.RegisterType((*GoldRoomsReq)(nil), "golddiamonn.GoldRoomsReq")
	proto.RegisterType((*GoldRoomsRsp)(nil), "golddiamonn.GoldRoomsRsp")
	proto.RegisterType((*GuildInitInfoReq)(nil), "golddiamonn.GuildInitInfoReq")
	proto.RegisterType((*DayTrendInfo)(nil), "golddiamonn.DayTrendInfo")
	proto.RegisterType((*RoomIdFee)(nil), "golddiamonn.RoomIdFee")
	proto.RegisterType((*GuildInitInfoRsp)(nil), "golddiamonn.GuildInitInfoRsp")
	proto.RegisterType((*GuildChannelIncomeReq)(nil), "golddiamonn.GuildChannelIncomeReq")
	proto.RegisterType((*StatIncomeInfo)(nil), "golddiamonn.StatIncomeInfo")
	proto.RegisterType((*GuildChannelIncomeRsp)(nil), "golddiamonn.GuildChannelIncomeRsp")
	proto.RegisterType((*GuildTodayIncomeReq)(nil), "golddiamonn.GuildTodayIncomeReq")
	proto.RegisterType((*StatTodayRoomIncomeInfo)(nil), "golddiamonn.StatTodayRoomIncomeInfo")
	proto.RegisterType((*GuildTodayIncomeRsp)(nil), "golddiamonn.GuildTodayIncomeRsp")
	proto.RegisterType((*GuildMonthIncomeReq)(nil), "golddiamonn.GuildMonthIncomeReq")
	proto.RegisterType((*DayTrendMoreInfo)(nil), "golddiamonn.DayTrendMoreInfo")
	proto.RegisterType((*GuildMonthIncomeRsp)(nil), "golddiamonn.GuildMonthIncomeRsp")
	proto.RegisterType((*GuildUnSettlementDetailReq)(nil), "golddiamonn.GuildUnSettlementDetailReq")
	proto.RegisterType((*GuildUnSettlementDetail)(nil), "golddiamonn.GuildUnSettlementDetail")
	proto.RegisterType((*GuildUnSettlementDetailRsp)(nil), "golddiamonn.GuildUnSettlementDetailRsp")
	proto.RegisterType((*GetGuildsUnSettlementSummaryReq)(nil), "golddiamonn.GetGuildsUnSettlementSummaryReq")
	proto.RegisterType((*GetGuildsUnSettlementSummaryRsp)(nil), "golddiamonn.GetGuildsUnSettlementSummaryRsp")
	proto.RegisterType((*GuildIncomeDetailReq)(nil), "golddiamonn.GuildIncomeDetailReq")
	proto.RegisterType((*GuildIncomeDetail)(nil), "golddiamonn.GuildIncomeDetail")
	proto.RegisterType((*GuildRoomIncomeDetail)(nil), "golddiamonn.GuildRoomIncomeDetail")
	proto.RegisterType((*GuildIncomeDetailRsp)(nil), "golddiamonn.GuildIncomeDetailRsp")
	proto.RegisterType((*GuildUnSettlementSummaryReq)(nil), "golddiamonn.GuildUnSettlementSummaryReq")
	proto.RegisterType((*GuildUnSettlementSummaryRsp)(nil), "golddiamonn.GuildUnSettlementSummaryRsp")
	proto.RegisterType((*ConsumeRankItem)(nil), "golddiamonn.ConsumeRankItem")
	proto.RegisterType((*ConsumeRankReq)(nil), "golddiamonn.ConsumeRankReq")
	proto.RegisterType((*ConsumeRankRsp)(nil), "golddiamonn.ConsumeRankRsp")
	proto.RegisterType((*RoomConsumeQoqReq)(nil), "golddiamonn.RoomConsumeQoqReq")
	proto.RegisterType((*RoomConsumeQoqRsp)(nil), "golddiamonn.RoomConsumeQoqRsp")
	proto.RegisterType((*GuildConsumeDayQoqReq)(nil), "golddiamonn.GuildConsumeDayQoqReq")
	proto.RegisterType((*GuildConsumeDayQoqRsp)(nil), "golddiamonn.GuildConsumeDayQoqRsp")
	proto.RegisterType((*ApplicationLimitInfo)(nil), "golddiamonn.ApplicationLimitInfo")
	proto.RegisterType((*ApplicationLimitOpeReq)(nil), "golddiamonn.ApplicationLimitOpeReq")
	proto.RegisterType((*ApplicationLimitOpeRsp)(nil), "golddiamonn.ApplicationLimitOpeRsp")
	proto.RegisterType((*ApplicationLimitQueryReq)(nil), "golddiamonn.ApplicationLimitQueryReq")
	proto.RegisterType((*ApplicationLimitQueryRsp)(nil), "golddiamonn.ApplicationLimitQueryRsp")
	proto.RegisterType((*RecommendExamLimitCheckReq)(nil), "golddiamonn.RecommendExamLimitCheckReq")
	proto.RegisterType((*RecommendExamLimitCheckResp)(nil), "golddiamonn.RecommendExamLimitCheckResp")
	proto.RegisterType((*ExamContent)(nil), "golddiamonn.ExamContent")
	proto.RegisterType((*RecommendExamBaseInfo)(nil), "golddiamonn.RecommendExamBaseInfo")
	proto.RegisterType((*RecommendExamTotalInfo)(nil), "golddiamonn.RecommendExamTotalInfo")
	proto.RegisterType((*ExamOperation)(nil), "golddiamonn.ExamOperation")
	proto.RegisterType((*RecommendExamOperationReq)(nil), "golddiamonn.RecommendExamOperationReq")
	proto.RegisterType((*RecommendExamOperationResp)(nil), "golddiamonn.RecommendExamOperationResp")
	proto.RegisterType((*RecommendExamReq)(nil), "golddiamonn.RecommendExamReq")
	proto.RegisterType((*RecommendExamResp)(nil), "golddiamonn.RecommendExamResp")
	proto.RegisterType((*GetRecommendExamReq)(nil), "golddiamonn.GetRecommendExamReq")
	proto.RegisterType((*GetRecommendExamResp)(nil), "golddiamonn.GetRecommendExamResp")
	proto.RegisterType((*SetRecommendExamTimeReq)(nil), "golddiamonn.SetRecommendExamTimeReq")
	proto.RegisterType((*SetRecommendExamTimeResp)(nil), "golddiamonn.SetRecommendExamTimeResp")
	proto.RegisterType((*GetExamContentReq)(nil), "golddiamonn.GetExamContentReq")
	proto.RegisterType((*GetExamContentResp)(nil), "golddiamonn.GetExamContentResp")
	proto.RegisterType((*ExamTagInfo)(nil), "golddiamonn.ExamTagInfo")
	proto.RegisterType((*GetExamTagListReq)(nil), "golddiamonn.GetExamTagListReq")
	proto.RegisterType((*GetExamTagListResp)(nil), "golddiamonn.GetExamTagListResp")
	proto.RegisterType((*YuyinExamApplicationInfo)(nil), "golddiamonn.YuyinExamApplicationInfo")
	proto.RegisterType((*YuyinExamGuildOwnerOperationReq)(nil), "golddiamonn.YuyinExamGuildOwnerOperationReq")
	proto.RegisterType((*YuyinExamGuildOwnerOperationResp)(nil), "golddiamonn.YuyinExamGuildOwnerOperationResp")
	proto.RegisterType((*YuyinExamReq)(nil), "golddiamonn.YuyinExamReq")
	proto.RegisterType((*YuyinExamResp)(nil), "golddiamonn.YuyinExamResp")
	proto.RegisterType((*GetYuyinExamReq)(nil), "golddiamonn.GetYuyinExamReq")
	proto.RegisterType((*GetYuyinExamResp)(nil), "golddiamonn.GetYuyinExamResp")
	proto.RegisterType((*GetYuyinExamForGuildReq)(nil), "golddiamonn.GetYuyinExamForGuildReq")
	proto.RegisterType((*GetYuyinExamForGuildResp)(nil), "golddiamonn.GetYuyinExamForGuildResp")
	proto.RegisterType((*YuyinExamDetail)(nil), "golddiamonn.YuyinExamDetail")
	proto.RegisterType((*ApplicationRecordForGuildOwnerReq)(nil), "golddiamonn.ApplicationRecordForGuildOwnerReq")
	proto.RegisterType((*ApplicationRecordInfo)(nil), "golddiamonn.ApplicationRecordInfo")
	proto.RegisterType((*ApplicationRecordForGuildOwnerResp)(nil), "golddiamonn.ApplicationRecordForGuildOwnerResp")
	proto.RegisterType((*GetYuyinExamDetailReq)(nil), "golddiamonn.GetYuyinExamDetailReq")
	proto.RegisterType((*GetYuyinExamDetailResp)(nil), "golddiamonn.GetYuyinExamDetailResp")
	proto.RegisterType((*YuyinExamOperationReq)(nil), "golddiamonn.YuyinExamOperationReq")
	proto.RegisterType((*YuyinExamOperationResp)(nil), "golddiamonn.YuyinExamOperationResp")
	proto.RegisterType((*GetYuyinExamStatusReq)(nil), "golddiamonn.GetYuyinExamStatusReq")
	proto.RegisterType((*GetYuyinExamStatusResp)(nil), "golddiamonn.GetYuyinExamStatusResp")
	proto.RegisterType((*DelYuyinExamApplicationReq)(nil), "golddiamonn.DelYuyinExamApplicationReq")
	proto.RegisterType((*DelYuyinExamApplicationResp)(nil), "golddiamonn.DelYuyinExamApplicationResp")
	proto.RegisterType((*GetGuildTypeReq)(nil), "golddiamonn.GetGuildTypeReq")
	proto.RegisterType((*GetGuildTypeRsp)(nil), "golddiamonn.GetGuildTypeRsp")
	proto.RegisterType((*GetGuildInfoV2Rsp)(nil), "golddiamonn.GetGuildInfoV2Rsp")
	proto.RegisterType((*AmuseLastIncomeTrendRsp)(nil), "golddiamonn.AmuseLastIncomeTrendRsp")
	proto.RegisterType((*AmuseIncomeTrendRsp)(nil), "golddiamonn.AmuseIncomeTrendRsp")
	proto.RegisterType((*AmuseIncomeRsp)(nil), "golddiamonn.AmuseIncomeRsp")
	proto.RegisterType((*AmuseChannelRsp)(nil), "golddiamonn.AmuseChannelRsp")
	proto.RegisterType((*GetAllHandlePresentCountReq)(nil), "golddiamonn.GetAllHandlePresentCountReq")
	proto.RegisterType((*GetAllHandlePresentCountResp)(nil), "golddiamonn.GetAllHandlePresentCountResp")
	proto.RegisterType((*GetAllHandlePresentOrderListReq)(nil), "golddiamonn.GetAllHandlePresentOrderListReq")
	proto.RegisterType((*GetAllHandlePresentOrderListResp)(nil), "golddiamonn.GetAllHandlePresentOrderListResp")
	proto.RegisterType((*OrderInfoT)(nil), "golddiamonn.OrderInfoT")
	proto.RegisterType((*AddOrderReq)(nil), "golddiamonn.AddOrderReq")
	proto.RegisterType((*AddOrderResp)(nil), "golddiamonn.AddOrderResp")
	proto.RegisterType((*GetGuildTypeInfoReq)(nil), "golddiamonn.GetGuildTypeInfoReq")
	proto.RegisterType((*GetGuildTypeInfoResp)(nil), "golddiamonn.GetGuildTypeInfoResp")
	proto.RegisterType((*GetGuildTypeHistoryReq)(nil), "golddiamonn.GetGuildTypeHistoryReq")
	proto.RegisterType((*GetGuildTypeHistoryResp)(nil), "golddiamonn.GetGuildTypeHistoryResp")
	proto.RegisterType((*GetGuildTypeHistoryResp_GuildTypeItem)(nil), "golddiamonn.GetGuildTypeHistoryResp.GuildTypeItem")
	proto.RegisterType((*GetAmuseChannelsIncomeReq)(nil), "golddiamonn.GetAmuseChannelsIncomeReq")
	proto.RegisterType((*GetAmuseChannelsIncomeResp)(nil), "golddiamonn.GetAmuseChannelsIncomeResp")
	proto.RegisterType((*AmuseChannelListReq)(nil), "golddiamonn.AmuseChannelListReq")
	proto.RegisterType((*AmuseChannelItem)(nil), "golddiamonn.AmuseChannelItem")
	proto.RegisterType((*AmuseChannelListResp)(nil), "golddiamonn.AmuseChannelListResp")
	proto.RegisterEnum("golddiamonn.GoldIncomeType", GoldIncomeType_name, GoldIncomeType_value)
	proto.RegisterEnum("golddiamonn.ReqSettleStatus", ReqSettleStatus_name, ReqSettleStatus_value)
	proto.RegisterEnum("golddiamonn.ApplicationOpeType", ApplicationOpeType_name, ApplicationOpeType_value)
	proto.RegisterEnum("golddiamonn.TimeFilterUnit", TimeFilterUnit_name, TimeFilterUnit_value)
	proto.RegisterEnum("golddiamonn.GuildInfoType", GuildInfoType_name, GuildInfoType_value)
	proto.RegisterEnum("golddiamonn.IncomeType", IncomeType_name, IncomeType_value)
	proto.RegisterEnum("golddiamonn.RangeType", RangeType_name, RangeType_value)
	proto.RegisterEnum("golddiamonn.QueryType", QueryType_name, QueryType_value)
	proto.RegisterEnum("golddiamonn.DbOpeType", DbOpeType_name, DbOpeType_value)
	proto.RegisterEnum("golddiamonn.ExamPurpose", ExamPurpose_name, ExamPurpose_value)
	proto.RegisterEnum("golddiamonn.ExamStatus", ExamStatus_name, ExamStatus_value)
	proto.RegisterEnum("golddiamonn.ExamOperationType", ExamOperationType_name, ExamOperationType_value)
	proto.RegisterEnum("golddiamonn.ExamTagType", ExamTagType_name, ExamTagType_value)
	proto.RegisterEnum("golddiamonn.YuyinExamStatus", YuyinExamStatus_name, YuyinExamStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GoldDiamondClient is the client API for GoldDiamond service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GoldDiamondClient interface {
	GetGuildApplication(ctx context.Context, in *GuildApplicationReq, opts ...grpc.CallOption) (*GuildApplicationRsp, error)
	GetApplicationList(ctx context.Context, in *ApplicationListReq, opts ...grpc.CallOption) (*ApplicationListRsp, error)
	OpeApplication(ctx context.Context, in *ApplicationOpeReq, opts ...grpc.CallOption) (*ApplicationOpeRsp, error)
	CreateApplication(ctx context.Context, in *ApplicationCreateReq, opts ...grpc.CallOption) (*ApplicationCreateRsp, error)
	GetGuildInfo(ctx context.Context, in *GuildInfoReq, opts ...grpc.CallOption) (*GuildInfoRsp, error)
	GetGuildList(ctx context.Context, in *GuildListReq, opts ...grpc.CallOption) (*GuildListRsp, error)
	OpeGuildInfo(ctx context.Context, in *GuildInfoOpeReq, opts ...grpc.CallOption) (*GuildInfoOpeRsp, error)
	GetGuildRooms(ctx context.Context, in *GoldRoomsReq, opts ...grpc.CallOption) (*GoldRoomsRsp, error)
	GetGuildInitInfo(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*GuildInitInfoRsp, error)
	GetGuildChannelIncomeInfo(ctx context.Context, in *GuildChannelIncomeReq, opts ...grpc.CallOption) (*GuildChannelIncomeRsp, error)
	GetGuildTodayIncomeInfo(ctx context.Context, in *GuildTodayIncomeReq, opts ...grpc.CallOption) (*GuildTodayIncomeRsp, error)
	GetGuildMonthTrendInfo(ctx context.Context, in *GuildMonthIncomeReq, opts ...grpc.CallOption) (*GuildMonthIncomeRsp, error)
	GetUnSettlementDetals(ctx context.Context, in *GuildUnSettlementDetailReq, opts ...grpc.CallOption) (*GuildUnSettlementDetailRsp, error)
	GetGuildsUnSettlementSummary(ctx context.Context, in *GetGuildsUnSettlementSummaryReq, opts ...grpc.CallOption) (*GetGuildsUnSettlementSummaryRsp, error)
	GetGuildIncomeDetails(ctx context.Context, in *GuildIncomeDetailReq, opts ...grpc.CallOption) (*GuildIncomeDetailRsp, error)
	GetGuildUnSettlementSummary(ctx context.Context, in *GuildUnSettlementSummaryReq, opts ...grpc.CallOption) (*GuildUnSettlementSummaryRsp, error)
	AwardGameDiamond(ctx context.Context, in *AwardDiamondReq, opts ...grpc.CallOption) (*AwardDiamondRsp, error)
	GetConsumeRank(ctx context.Context, in *ConsumeRankReq, opts ...grpc.CallOption) (*ConsumeRankRsp, error)
	GuildConsumeDayQoq(ctx context.Context, in *GuildConsumeDayQoqReq, opts ...grpc.CallOption) (*GuildConsumeDayQoqRsp, error)
	RoomConsumeQoq(ctx context.Context, in *RoomConsumeQoqReq, opts ...grpc.CallOption) (*RoomConsumeQoqRsp, error)
	ApplicationLimitOpe(ctx context.Context, in *ApplicationLimitOpeReq, opts ...grpc.CallOption) (*ApplicationLimitOpeRsp, error)
	ApplicationLimitQuery(ctx context.Context, in *ApplicationLimitQueryReq, opts ...grpc.CallOption) (*ApplicationLimitQueryRsp, error)
	GetGuildType(ctx context.Context, in *GetGuildTypeReq, opts ...grpc.CallOption) (*GetGuildTypeRsp, error)
	// v2版本的情况
	// 获取工会信息
	GetGuildInfoV2(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*GetGuildInfoV2Rsp, error)
	// 获取娱乐房上个月趋势
	AmuseLastIncomeTrend(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseLastIncomeTrendRsp, error)
	// 获取娱乐房本个月趋势
	AmuseIncomeTrend(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseIncomeTrendRsp, error)
	// 娱乐房收入情况
	AmuseIncome(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseIncomeRsp, error)
	// 娱乐房房间情况
	AmuseChannel(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseChannelRsp, error)
	// 娱乐房收入情况查询
	AmuseChannelList(ctx context.Context, in *AmuseChannelListReq, opts ...grpc.CallOption) (*AmuseChannelListResp, error)
	// 娱乐房收益详情，通过时间段
	GetAmuseChannelsIncome(ctx context.Context, in *GetAmuseChannelsIncomeReq, opts ...grpc.CallOption) (*GetAmuseChannelsIncomeResp, error)
	// 检查是否限制
	RecommendExamLimitCheck(ctx context.Context, in *RecommendExamLimitCheckReq, opts ...grpc.CallOption) (*RecommendExamLimitCheckResp, error)
	// 推荐房操作
	RecommendExamOperation(ctx context.Context, in *RecommendExamOperationReq, opts ...grpc.CallOption) (*RecommendExamOperationResp, error)
	// 推荐房考核申请信息
	RecommendExam(ctx context.Context, in *RecommendExamReq, opts ...grpc.CallOption) (*RecommendExamResp, error)
	// 运营后台查询推荐房考核申请信息
	GetRecommendExam(ctx context.Context, in *GetRecommendExamReq, opts ...grpc.CallOption) (*GetRecommendExamResp, error)
	// 设置考核时间
	SetRecommendExamTime(ctx context.Context, in *SetRecommendExamTimeReq, opts ...grpc.CallOption) (*SetRecommendExamTimeResp, error)
	// 考核信息
	GetExamContent(ctx context.Context, in *GetExamContentReq, opts ...grpc.CallOption) (*GetExamContentResp, error)
	// 主播提交考核
	YuyinExam(ctx context.Context, in *YuyinExamReq, opts ...grpc.CallOption) (*YuyinExamResp, error)
	// 主播查看提交的考核信息
	GetYuyinExam(ctx context.Context, in *GetYuyinExamReq, opts ...grpc.CallOption) (*GetYuyinExamResp, error)
	// 运营后台查看主播考核信息
	GetYuyinExamDetail(ctx context.Context, in *GetYuyinExamDetailReq, opts ...grpc.CallOption) (*GetYuyinExamDetailResp, error)
	// 语音考核操作
	YuyinExamOperation(ctx context.Context, in *YuyinExamOperationReq, opts ...grpc.CallOption) (*YuyinExamOperationResp, error)
	// 会长处理主播考核申请
	YuyinExamGuildOwnerOperation(ctx context.Context, in *YuyinExamGuildOwnerOperationReq, opts ...grpc.CallOption) (*YuyinExamGuildOwnerOperationResp, error)
	// 会长查看主播提交的考核信息
	GetYuyinExamForGuild(ctx context.Context, in *GetYuyinExamForGuildReq, opts ...grpc.CallOption) (*GetYuyinExamForGuildResp, error)
	// 考核标签列表
	GetExamTagList(ctx context.Context, in *GetExamTagListReq, opts ...grpc.CallOption) (*GetExamTagListResp, error)
	// 语音直播考核状态 -1 未提交(或没通过) 0 审核中 1已通过(包括会长&官方)
	GetYuyinExamStatus(ctx context.Context, in *GetYuyinExamStatusReq, opts ...grpc.CallOption) (*GetYuyinExamStatusResp, error)
	// 删除语音主播申请的考核 会长提交后不用删
	DelYuyinExamApplication(ctx context.Context, in *DelYuyinExamApplicationReq, opts ...grpc.CallOption) (*DelYuyinExamApplicationResp, error)
	// 会长查看申请记录
	ApplicationRecordForGuildOwner(ctx context.Context, in *ApplicationRecordForGuildOwnerReq, opts ...grpc.CallOption) (*ApplicationRecordForGuildOwnerResp, error)
	// 对帐
	GetAllHandlePresentCount(ctx context.Context, in *GetAllHandlePresentCountReq, opts ...grpc.CallOption) (*GetAllHandlePresentCountResp, error)
	GetAllHandlePresentOrderList(ctx context.Context, in *GetAllHandlePresentOrderListReq, opts ...grpc.CallOption) (*GetAllHandlePresentOrderListResp, error)
	AddOrder(ctx context.Context, in *AddOrderReq, opts ...grpc.CallOption) (*AddOrderResp, error)
	// 检查当时的签约工会状态
	GetGuildTypeInfo(ctx context.Context, in *GetGuildTypeInfoReq, opts ...grpc.CallOption) (*GetGuildTypeInfoResp, error)
	// 获取当时签约工会历史记录
	GetGuildTypeHistory(ctx context.Context, in *GetGuildTypeHistoryReq, opts ...grpc.CallOption) (*GetGuildTypeHistoryResp, error)
	// 获取时间范围内的娱乐房金钻订单数量和金额
	GetGoldOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的订单列表
	GetGoldOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error)
}

type goldDiamondClient struct {
	cc *grpc.ClientConn
}

func NewGoldDiamondClient(cc *grpc.ClientConn) GoldDiamondClient {
	return &goldDiamondClient{cc}
}

func (c *goldDiamondClient) GetGuildApplication(ctx context.Context, in *GuildApplicationReq, opts ...grpc.CallOption) (*GuildApplicationRsp, error) {
	out := new(GuildApplicationRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetApplicationList(ctx context.Context, in *ApplicationListReq, opts ...grpc.CallOption) (*ApplicationListRsp, error) {
	out := new(ApplicationListRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetApplicationList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) OpeApplication(ctx context.Context, in *ApplicationOpeReq, opts ...grpc.CallOption) (*ApplicationOpeRsp, error) {
	out := new(ApplicationOpeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/OpeApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) CreateApplication(ctx context.Context, in *ApplicationCreateReq, opts ...grpc.CallOption) (*ApplicationCreateRsp, error) {
	out := new(ApplicationCreateRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/CreateApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildInfo(ctx context.Context, in *GuildInfoReq, opts ...grpc.CallOption) (*GuildInfoRsp, error) {
	out := new(GuildInfoRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildList(ctx context.Context, in *GuildListReq, opts ...grpc.CallOption) (*GuildListRsp, error) {
	out := new(GuildListRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) OpeGuildInfo(ctx context.Context, in *GuildInfoOpeReq, opts ...grpc.CallOption) (*GuildInfoOpeRsp, error) {
	out := new(GuildInfoOpeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/OpeGuildInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildRooms(ctx context.Context, in *GoldRoomsReq, opts ...grpc.CallOption) (*GoldRoomsRsp, error) {
	out := new(GoldRoomsRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildRooms", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildInitInfo(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*GuildInitInfoRsp, error) {
	out := new(GuildInitInfoRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildInitInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildChannelIncomeInfo(ctx context.Context, in *GuildChannelIncomeReq, opts ...grpc.CallOption) (*GuildChannelIncomeRsp, error) {
	out := new(GuildChannelIncomeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildChannelIncomeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildTodayIncomeInfo(ctx context.Context, in *GuildTodayIncomeReq, opts ...grpc.CallOption) (*GuildTodayIncomeRsp, error) {
	out := new(GuildTodayIncomeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildTodayIncomeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildMonthTrendInfo(ctx context.Context, in *GuildMonthIncomeReq, opts ...grpc.CallOption) (*GuildMonthIncomeRsp, error) {
	out := new(GuildMonthIncomeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildMonthTrendInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetUnSettlementDetals(ctx context.Context, in *GuildUnSettlementDetailReq, opts ...grpc.CallOption) (*GuildUnSettlementDetailRsp, error) {
	out := new(GuildUnSettlementDetailRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetUnSettlementDetals", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildsUnSettlementSummary(ctx context.Context, in *GetGuildsUnSettlementSummaryReq, opts ...grpc.CallOption) (*GetGuildsUnSettlementSummaryRsp, error) {
	out := new(GetGuildsUnSettlementSummaryRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildsUnSettlementSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildIncomeDetails(ctx context.Context, in *GuildIncomeDetailReq, opts ...grpc.CallOption) (*GuildIncomeDetailRsp, error) {
	out := new(GuildIncomeDetailRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildIncomeDetails", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildUnSettlementSummary(ctx context.Context, in *GuildUnSettlementSummaryReq, opts ...grpc.CallOption) (*GuildUnSettlementSummaryRsp, error) {
	out := new(GuildUnSettlementSummaryRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildUnSettlementSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AwardGameDiamond(ctx context.Context, in *AwardDiamondReq, opts ...grpc.CallOption) (*AwardDiamondRsp, error) {
	out := new(AwardDiamondRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AwardGameDiamond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetConsumeRank(ctx context.Context, in *ConsumeRankReq, opts ...grpc.CallOption) (*ConsumeRankRsp, error) {
	out := new(ConsumeRankRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetConsumeRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GuildConsumeDayQoq(ctx context.Context, in *GuildConsumeDayQoqReq, opts ...grpc.CallOption) (*GuildConsumeDayQoqRsp, error) {
	out := new(GuildConsumeDayQoqRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GuildConsumeDayQoq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) RoomConsumeQoq(ctx context.Context, in *RoomConsumeQoqReq, opts ...grpc.CallOption) (*RoomConsumeQoqRsp, error) {
	out := new(RoomConsumeQoqRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/RoomConsumeQoq", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) ApplicationLimitOpe(ctx context.Context, in *ApplicationLimitOpeReq, opts ...grpc.CallOption) (*ApplicationLimitOpeRsp, error) {
	out := new(ApplicationLimitOpeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/ApplicationLimitOpe", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) ApplicationLimitQuery(ctx context.Context, in *ApplicationLimitQueryReq, opts ...grpc.CallOption) (*ApplicationLimitQueryRsp, error) {
	out := new(ApplicationLimitQueryRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/ApplicationLimitQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildType(ctx context.Context, in *GetGuildTypeReq, opts ...grpc.CallOption) (*GetGuildTypeRsp, error) {
	out := new(GetGuildTypeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildInfoV2(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*GetGuildInfoV2Rsp, error) {
	out := new(GetGuildInfoV2Rsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildInfoV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AmuseLastIncomeTrend(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseLastIncomeTrendRsp, error) {
	out := new(AmuseLastIncomeTrendRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AmuseLastIncomeTrend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AmuseIncomeTrend(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseIncomeTrendRsp, error) {
	out := new(AmuseIncomeTrendRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AmuseIncomeTrend", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AmuseIncome(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseIncomeRsp, error) {
	out := new(AmuseIncomeRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AmuseIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AmuseChannel(ctx context.Context, in *GuildInitInfoReq, opts ...grpc.CallOption) (*AmuseChannelRsp, error) {
	out := new(AmuseChannelRsp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AmuseChannel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AmuseChannelList(ctx context.Context, in *AmuseChannelListReq, opts ...grpc.CallOption) (*AmuseChannelListResp, error) {
	out := new(AmuseChannelListResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AmuseChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetAmuseChannelsIncome(ctx context.Context, in *GetAmuseChannelsIncomeReq, opts ...grpc.CallOption) (*GetAmuseChannelsIncomeResp, error) {
	out := new(GetAmuseChannelsIncomeResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetAmuseChannelsIncome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) RecommendExamLimitCheck(ctx context.Context, in *RecommendExamLimitCheckReq, opts ...grpc.CallOption) (*RecommendExamLimitCheckResp, error) {
	out := new(RecommendExamLimitCheckResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/RecommendExamLimitCheck", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) RecommendExamOperation(ctx context.Context, in *RecommendExamOperationReq, opts ...grpc.CallOption) (*RecommendExamOperationResp, error) {
	out := new(RecommendExamOperationResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/RecommendExamOperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) RecommendExam(ctx context.Context, in *RecommendExamReq, opts ...grpc.CallOption) (*RecommendExamResp, error) {
	out := new(RecommendExamResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/RecommendExam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetRecommendExam(ctx context.Context, in *GetRecommendExamReq, opts ...grpc.CallOption) (*GetRecommendExamResp, error) {
	out := new(GetRecommendExamResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetRecommendExam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) SetRecommendExamTime(ctx context.Context, in *SetRecommendExamTimeReq, opts ...grpc.CallOption) (*SetRecommendExamTimeResp, error) {
	out := new(SetRecommendExamTimeResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/SetRecommendExamTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetExamContent(ctx context.Context, in *GetExamContentReq, opts ...grpc.CallOption) (*GetExamContentResp, error) {
	out := new(GetExamContentResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetExamContent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) YuyinExam(ctx context.Context, in *YuyinExamReq, opts ...grpc.CallOption) (*YuyinExamResp, error) {
	out := new(YuyinExamResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/YuyinExam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetYuyinExam(ctx context.Context, in *GetYuyinExamReq, opts ...grpc.CallOption) (*GetYuyinExamResp, error) {
	out := new(GetYuyinExamResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetYuyinExam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetYuyinExamDetail(ctx context.Context, in *GetYuyinExamDetailReq, opts ...grpc.CallOption) (*GetYuyinExamDetailResp, error) {
	out := new(GetYuyinExamDetailResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetYuyinExamDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) YuyinExamOperation(ctx context.Context, in *YuyinExamOperationReq, opts ...grpc.CallOption) (*YuyinExamOperationResp, error) {
	out := new(YuyinExamOperationResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/YuyinExamOperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) YuyinExamGuildOwnerOperation(ctx context.Context, in *YuyinExamGuildOwnerOperationReq, opts ...grpc.CallOption) (*YuyinExamGuildOwnerOperationResp, error) {
	out := new(YuyinExamGuildOwnerOperationResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/YuyinExamGuildOwnerOperation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetYuyinExamForGuild(ctx context.Context, in *GetYuyinExamForGuildReq, opts ...grpc.CallOption) (*GetYuyinExamForGuildResp, error) {
	out := new(GetYuyinExamForGuildResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetYuyinExamForGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetExamTagList(ctx context.Context, in *GetExamTagListReq, opts ...grpc.CallOption) (*GetExamTagListResp, error) {
	out := new(GetExamTagListResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetExamTagList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetYuyinExamStatus(ctx context.Context, in *GetYuyinExamStatusReq, opts ...grpc.CallOption) (*GetYuyinExamStatusResp, error) {
	out := new(GetYuyinExamStatusResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetYuyinExamStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) DelYuyinExamApplication(ctx context.Context, in *DelYuyinExamApplicationReq, opts ...grpc.CallOption) (*DelYuyinExamApplicationResp, error) {
	out := new(DelYuyinExamApplicationResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/DelYuyinExamApplication", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) ApplicationRecordForGuildOwner(ctx context.Context, in *ApplicationRecordForGuildOwnerReq, opts ...grpc.CallOption) (*ApplicationRecordForGuildOwnerResp, error) {
	out := new(ApplicationRecordForGuildOwnerResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/ApplicationRecordForGuildOwner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetAllHandlePresentCount(ctx context.Context, in *GetAllHandlePresentCountReq, opts ...grpc.CallOption) (*GetAllHandlePresentCountResp, error) {
	out := new(GetAllHandlePresentCountResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetAllHandlePresentCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetAllHandlePresentOrderList(ctx context.Context, in *GetAllHandlePresentOrderListReq, opts ...grpc.CallOption) (*GetAllHandlePresentOrderListResp, error) {
	out := new(GetAllHandlePresentOrderListResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetAllHandlePresentOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) AddOrder(ctx context.Context, in *AddOrderReq, opts ...grpc.CallOption) (*AddOrderResp, error) {
	out := new(AddOrderResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/AddOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildTypeInfo(ctx context.Context, in *GetGuildTypeInfoReq, opts ...grpc.CallOption) (*GetGuildTypeInfoResp, error) {
	out := new(GetGuildTypeInfoResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildTypeInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGuildTypeHistory(ctx context.Context, in *GetGuildTypeHistoryReq, opts ...grpc.CallOption) (*GetGuildTypeHistoryResp, error) {
	out := new(GetGuildTypeHistoryResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGuildTypeHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGoldOrderCount(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.CountResp, error) {
	out := new(reconcile_v2.CountResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGoldOrderCount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) GetGoldOrderList(ctx context.Context, in *reconcile_v2.TimeRangeReq, opts ...grpc.CallOption) (*reconcile_v2.OrderIdsResp, error) {
	out := new(reconcile_v2.OrderIdsResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/GetGoldOrderList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *goldDiamondClient) ReplaceOrder(ctx context.Context, in *reconcile_v2.ReplaceOrderReq, opts ...grpc.CallOption) (*reconcile_v2.EmptyResp, error) {
	out := new(reconcile_v2.EmptyResp)
	err := c.cc.Invoke(ctx, "/golddiamonn.GoldDiamond/ReplaceOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GoldDiamondServer is the server API for GoldDiamond service.
type GoldDiamondServer interface {
	GetGuildApplication(context.Context, *GuildApplicationReq) (*GuildApplicationRsp, error)
	GetApplicationList(context.Context, *ApplicationListReq) (*ApplicationListRsp, error)
	OpeApplication(context.Context, *ApplicationOpeReq) (*ApplicationOpeRsp, error)
	CreateApplication(context.Context, *ApplicationCreateReq) (*ApplicationCreateRsp, error)
	GetGuildInfo(context.Context, *GuildInfoReq) (*GuildInfoRsp, error)
	GetGuildList(context.Context, *GuildListReq) (*GuildListRsp, error)
	OpeGuildInfo(context.Context, *GuildInfoOpeReq) (*GuildInfoOpeRsp, error)
	GetGuildRooms(context.Context, *GoldRoomsReq) (*GoldRoomsRsp, error)
	GetGuildInitInfo(context.Context, *GuildInitInfoReq) (*GuildInitInfoRsp, error)
	GetGuildChannelIncomeInfo(context.Context, *GuildChannelIncomeReq) (*GuildChannelIncomeRsp, error)
	GetGuildTodayIncomeInfo(context.Context, *GuildTodayIncomeReq) (*GuildTodayIncomeRsp, error)
	GetGuildMonthTrendInfo(context.Context, *GuildMonthIncomeReq) (*GuildMonthIncomeRsp, error)
	GetUnSettlementDetals(context.Context, *GuildUnSettlementDetailReq) (*GuildUnSettlementDetailRsp, error)
	GetGuildsUnSettlementSummary(context.Context, *GetGuildsUnSettlementSummaryReq) (*GetGuildsUnSettlementSummaryRsp, error)
	GetGuildIncomeDetails(context.Context, *GuildIncomeDetailReq) (*GuildIncomeDetailRsp, error)
	GetGuildUnSettlementSummary(context.Context, *GuildUnSettlementSummaryReq) (*GuildUnSettlementSummaryRsp, error)
	AwardGameDiamond(context.Context, *AwardDiamondReq) (*AwardDiamondRsp, error)
	GetConsumeRank(context.Context, *ConsumeRankReq) (*ConsumeRankRsp, error)
	GuildConsumeDayQoq(context.Context, *GuildConsumeDayQoqReq) (*GuildConsumeDayQoqRsp, error)
	RoomConsumeQoq(context.Context, *RoomConsumeQoqReq) (*RoomConsumeQoqRsp, error)
	ApplicationLimitOpe(context.Context, *ApplicationLimitOpeReq) (*ApplicationLimitOpeRsp, error)
	ApplicationLimitQuery(context.Context, *ApplicationLimitQueryReq) (*ApplicationLimitQueryRsp, error)
	GetGuildType(context.Context, *GetGuildTypeReq) (*GetGuildTypeRsp, error)
	// v2版本的情况
	// 获取工会信息
	GetGuildInfoV2(context.Context, *GuildInitInfoReq) (*GetGuildInfoV2Rsp, error)
	// 获取娱乐房上个月趋势
	AmuseLastIncomeTrend(context.Context, *GuildInitInfoReq) (*AmuseLastIncomeTrendRsp, error)
	// 获取娱乐房本个月趋势
	AmuseIncomeTrend(context.Context, *GuildInitInfoReq) (*AmuseIncomeTrendRsp, error)
	// 娱乐房收入情况
	AmuseIncome(context.Context, *GuildInitInfoReq) (*AmuseIncomeRsp, error)
	// 娱乐房房间情况
	AmuseChannel(context.Context, *GuildInitInfoReq) (*AmuseChannelRsp, error)
	// 娱乐房收入情况查询
	AmuseChannelList(context.Context, *AmuseChannelListReq) (*AmuseChannelListResp, error)
	// 娱乐房收益详情，通过时间段
	GetAmuseChannelsIncome(context.Context, *GetAmuseChannelsIncomeReq) (*GetAmuseChannelsIncomeResp, error)
	// 检查是否限制
	RecommendExamLimitCheck(context.Context, *RecommendExamLimitCheckReq) (*RecommendExamLimitCheckResp, error)
	// 推荐房操作
	RecommendExamOperation(context.Context, *RecommendExamOperationReq) (*RecommendExamOperationResp, error)
	// 推荐房考核申请信息
	RecommendExam(context.Context, *RecommendExamReq) (*RecommendExamResp, error)
	// 运营后台查询推荐房考核申请信息
	GetRecommendExam(context.Context, *GetRecommendExamReq) (*GetRecommendExamResp, error)
	// 设置考核时间
	SetRecommendExamTime(context.Context, *SetRecommendExamTimeReq) (*SetRecommendExamTimeResp, error)
	// 考核信息
	GetExamContent(context.Context, *GetExamContentReq) (*GetExamContentResp, error)
	// 主播提交考核
	YuyinExam(context.Context, *YuyinExamReq) (*YuyinExamResp, error)
	// 主播查看提交的考核信息
	GetYuyinExam(context.Context, *GetYuyinExamReq) (*GetYuyinExamResp, error)
	// 运营后台查看主播考核信息
	GetYuyinExamDetail(context.Context, *GetYuyinExamDetailReq) (*GetYuyinExamDetailResp, error)
	// 语音考核操作
	YuyinExamOperation(context.Context, *YuyinExamOperationReq) (*YuyinExamOperationResp, error)
	// 会长处理主播考核申请
	YuyinExamGuildOwnerOperation(context.Context, *YuyinExamGuildOwnerOperationReq) (*YuyinExamGuildOwnerOperationResp, error)
	// 会长查看主播提交的考核信息
	GetYuyinExamForGuild(context.Context, *GetYuyinExamForGuildReq) (*GetYuyinExamForGuildResp, error)
	// 考核标签列表
	GetExamTagList(context.Context, *GetExamTagListReq) (*GetExamTagListResp, error)
	// 语音直播考核状态 -1 未提交(或没通过) 0 审核中 1已通过(包括会长&官方)
	GetYuyinExamStatus(context.Context, *GetYuyinExamStatusReq) (*GetYuyinExamStatusResp, error)
	// 删除语音主播申请的考核 会长提交后不用删
	DelYuyinExamApplication(context.Context, *DelYuyinExamApplicationReq) (*DelYuyinExamApplicationResp, error)
	// 会长查看申请记录
	ApplicationRecordForGuildOwner(context.Context, *ApplicationRecordForGuildOwnerReq) (*ApplicationRecordForGuildOwnerResp, error)
	// 对帐
	GetAllHandlePresentCount(context.Context, *GetAllHandlePresentCountReq) (*GetAllHandlePresentCountResp, error)
	GetAllHandlePresentOrderList(context.Context, *GetAllHandlePresentOrderListReq) (*GetAllHandlePresentOrderListResp, error)
	AddOrder(context.Context, *AddOrderReq) (*AddOrderResp, error)
	// 检查当时的签约工会状态
	GetGuildTypeInfo(context.Context, *GetGuildTypeInfoReq) (*GetGuildTypeInfoResp, error)
	// 获取当时签约工会历史记录
	GetGuildTypeHistory(context.Context, *GetGuildTypeHistoryReq) (*GetGuildTypeHistoryResp, error)
	// 获取时间范围内的娱乐房金钻订单数量和金额
	GetGoldOrderCount(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.CountResp, error)
	// 获取时间范围内的订单列表
	GetGoldOrderList(context.Context, *reconcile_v2.TimeRangeReq) (*reconcile_v2.OrderIdsResp, error)
	// 补单
	ReplaceOrder(context.Context, *reconcile_v2.ReplaceOrderReq) (*reconcile_v2.EmptyResp, error)
}

func RegisterGoldDiamondServer(s *grpc.Server, srv GoldDiamondServer) {
	s.RegisterService(&_GoldDiamond_serviceDesc, srv)
}

func _GoldDiamond_GetGuildApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildApplication(ctx, req.(*GuildApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetApplicationList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicationListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetApplicationList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetApplicationList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetApplicationList(ctx, req.(*ApplicationListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_OpeApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicationOpeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).OpeApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/OpeApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).OpeApplication(ctx, req.(*ApplicationOpeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_CreateApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicationCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).CreateApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/CreateApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).CreateApplication(ctx, req.(*ApplicationCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildInfo(ctx, req.(*GuildInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildList(ctx, req.(*GuildListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_OpeGuildInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInfoOpeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).OpeGuildInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/OpeGuildInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).OpeGuildInfo(ctx, req.(*GuildInfoOpeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildRooms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GoldRoomsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildRooms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildRooms",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildRooms(ctx, req.(*GoldRoomsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildInitInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildInitInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildInitInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildInitInfo(ctx, req.(*GuildInitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildChannelIncomeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildChannelIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildChannelIncomeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildChannelIncomeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildChannelIncomeInfo(ctx, req.(*GuildChannelIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildTodayIncomeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildTodayIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildTodayIncomeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildTodayIncomeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildTodayIncomeInfo(ctx, req.(*GuildTodayIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildMonthTrendInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildMonthIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildMonthTrendInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildMonthTrendInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildMonthTrendInfo(ctx, req.(*GuildMonthIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetUnSettlementDetals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildUnSettlementDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetUnSettlementDetals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetUnSettlementDetals",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetUnSettlementDetals(ctx, req.(*GuildUnSettlementDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildsUnSettlementSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildsUnSettlementSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildsUnSettlementSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildsUnSettlementSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildsUnSettlementSummary(ctx, req.(*GetGuildsUnSettlementSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildIncomeDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildIncomeDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildIncomeDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildIncomeDetails",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildIncomeDetails(ctx, req.(*GuildIncomeDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildUnSettlementSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildUnSettlementSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildUnSettlementSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildUnSettlementSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildUnSettlementSummary(ctx, req.(*GuildUnSettlementSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AwardGameDiamond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardDiamondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AwardGameDiamond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AwardGameDiamond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AwardGameDiamond(ctx, req.(*AwardDiamondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetConsumeRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConsumeRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetConsumeRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetConsumeRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetConsumeRank(ctx, req.(*ConsumeRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GuildConsumeDayQoq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildConsumeDayQoqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GuildConsumeDayQoq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GuildConsumeDayQoq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GuildConsumeDayQoq(ctx, req.(*GuildConsumeDayQoqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_RoomConsumeQoq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RoomConsumeQoqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).RoomConsumeQoq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/RoomConsumeQoq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).RoomConsumeQoq(ctx, req.(*RoomConsumeQoqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_ApplicationLimitOpe_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicationLimitOpeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).ApplicationLimitOpe(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/ApplicationLimitOpe",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).ApplicationLimitOpe(ctx, req.(*ApplicationLimitOpeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_ApplicationLimitQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicationLimitQueryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).ApplicationLimitQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/ApplicationLimitQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).ApplicationLimitQuery(ctx, req.(*ApplicationLimitQueryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildType(ctx, req.(*GetGuildTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildInfoV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildInfoV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildInfoV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildInfoV2(ctx, req.(*GuildInitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AmuseLastIncomeTrend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AmuseLastIncomeTrend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AmuseLastIncomeTrend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AmuseLastIncomeTrend(ctx, req.(*GuildInitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AmuseIncomeTrend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AmuseIncomeTrend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AmuseIncomeTrend",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AmuseIncomeTrend(ctx, req.(*GuildInitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AmuseIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AmuseIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AmuseIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AmuseIncome(ctx, req.(*GuildInitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AmuseChannel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GuildInitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AmuseChannel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AmuseChannel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AmuseChannel(ctx, req.(*GuildInitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AmuseChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AmuseChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AmuseChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AmuseChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AmuseChannelList(ctx, req.(*AmuseChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetAmuseChannelsIncome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmuseChannelsIncomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetAmuseChannelsIncome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetAmuseChannelsIncome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetAmuseChannelsIncome(ctx, req.(*GetAmuseChannelsIncomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_RecommendExamLimitCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendExamLimitCheckReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).RecommendExamLimitCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/RecommendExamLimitCheck",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).RecommendExamLimitCheck(ctx, req.(*RecommendExamLimitCheckReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_RecommendExamOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendExamOperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).RecommendExamOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/RecommendExamOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).RecommendExamOperation(ctx, req.(*RecommendExamOperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_RecommendExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).RecommendExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/RecommendExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).RecommendExam(ctx, req.(*RecommendExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetRecommendExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetRecommendExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetRecommendExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetRecommendExam(ctx, req.(*GetRecommendExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_SetRecommendExamTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRecommendExamTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).SetRecommendExamTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/SetRecommendExamTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).SetRecommendExamTime(ctx, req.(*SetRecommendExamTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetExamContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExamContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetExamContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetExamContent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetExamContent(ctx, req.(*GetExamContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_YuyinExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YuyinExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).YuyinExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/YuyinExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).YuyinExam(ctx, req.(*YuyinExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetYuyinExam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetYuyinExamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetYuyinExam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetYuyinExam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetYuyinExam(ctx, req.(*GetYuyinExamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetYuyinExamDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetYuyinExamDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetYuyinExamDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetYuyinExamDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetYuyinExamDetail(ctx, req.(*GetYuyinExamDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_YuyinExamOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YuyinExamOperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).YuyinExamOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/YuyinExamOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).YuyinExamOperation(ctx, req.(*YuyinExamOperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_YuyinExamGuildOwnerOperation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(YuyinExamGuildOwnerOperationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).YuyinExamGuildOwnerOperation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/YuyinExamGuildOwnerOperation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).YuyinExamGuildOwnerOperation(ctx, req.(*YuyinExamGuildOwnerOperationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetYuyinExamForGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetYuyinExamForGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetYuyinExamForGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetYuyinExamForGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetYuyinExamForGuild(ctx, req.(*GetYuyinExamForGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetExamTagList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExamTagListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetExamTagList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetExamTagList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetExamTagList(ctx, req.(*GetExamTagListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetYuyinExamStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetYuyinExamStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetYuyinExamStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetYuyinExamStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetYuyinExamStatus(ctx, req.(*GetYuyinExamStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_DelYuyinExamApplication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelYuyinExamApplicationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).DelYuyinExamApplication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/DelYuyinExamApplication",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).DelYuyinExamApplication(ctx, req.(*DelYuyinExamApplicationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_ApplicationRecordForGuildOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplicationRecordForGuildOwnerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).ApplicationRecordForGuildOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/ApplicationRecordForGuildOwner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).ApplicationRecordForGuildOwner(ctx, req.(*ApplicationRecordForGuildOwnerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetAllHandlePresentCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllHandlePresentCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetAllHandlePresentCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetAllHandlePresentCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetAllHandlePresentCount(ctx, req.(*GetAllHandlePresentCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetAllHandlePresentOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllHandlePresentOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetAllHandlePresentOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetAllHandlePresentOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetAllHandlePresentOrderList(ctx, req.(*GetAllHandlePresentOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_AddOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).AddOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/AddOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).AddOrder(ctx, req.(*AddOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildTypeInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildTypeInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildTypeInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildTypeInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildTypeInfo(ctx, req.(*GetGuildTypeInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGuildTypeHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildTypeHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGuildTypeHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGuildTypeHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGuildTypeHistory(ctx, req.(*GetGuildTypeHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGoldOrderCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGoldOrderCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGoldOrderCount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGoldOrderCount(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_GetGoldOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).GetGoldOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/GetGoldOrderList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).GetGoldOrderList(ctx, req.(*reconcile_v2.TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GoldDiamond_ReplaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(reconcile_v2.ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GoldDiamondServer).ReplaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/golddiamonn.GoldDiamond/ReplaceOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GoldDiamondServer).ReplaceOrder(ctx, req.(*reconcile_v2.ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GoldDiamond_serviceDesc = grpc.ServiceDesc{
	ServiceName: "golddiamonn.GoldDiamond",
	HandlerType: (*GoldDiamondServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGuildApplication",
			Handler:    _GoldDiamond_GetGuildApplication_Handler,
		},
		{
			MethodName: "GetApplicationList",
			Handler:    _GoldDiamond_GetApplicationList_Handler,
		},
		{
			MethodName: "OpeApplication",
			Handler:    _GoldDiamond_OpeApplication_Handler,
		},
		{
			MethodName: "CreateApplication",
			Handler:    _GoldDiamond_CreateApplication_Handler,
		},
		{
			MethodName: "GetGuildInfo",
			Handler:    _GoldDiamond_GetGuildInfo_Handler,
		},
		{
			MethodName: "GetGuildList",
			Handler:    _GoldDiamond_GetGuildList_Handler,
		},
		{
			MethodName: "OpeGuildInfo",
			Handler:    _GoldDiamond_OpeGuildInfo_Handler,
		},
		{
			MethodName: "GetGuildRooms",
			Handler:    _GoldDiamond_GetGuildRooms_Handler,
		},
		{
			MethodName: "GetGuildInitInfo",
			Handler:    _GoldDiamond_GetGuildInitInfo_Handler,
		},
		{
			MethodName: "GetGuildChannelIncomeInfo",
			Handler:    _GoldDiamond_GetGuildChannelIncomeInfo_Handler,
		},
		{
			MethodName: "GetGuildTodayIncomeInfo",
			Handler:    _GoldDiamond_GetGuildTodayIncomeInfo_Handler,
		},
		{
			MethodName: "GetGuildMonthTrendInfo",
			Handler:    _GoldDiamond_GetGuildMonthTrendInfo_Handler,
		},
		{
			MethodName: "GetUnSettlementDetals",
			Handler:    _GoldDiamond_GetUnSettlementDetals_Handler,
		},
		{
			MethodName: "GetGuildsUnSettlementSummary",
			Handler:    _GoldDiamond_GetGuildsUnSettlementSummary_Handler,
		},
		{
			MethodName: "GetGuildIncomeDetails",
			Handler:    _GoldDiamond_GetGuildIncomeDetails_Handler,
		},
		{
			MethodName: "GetGuildUnSettlementSummary",
			Handler:    _GoldDiamond_GetGuildUnSettlementSummary_Handler,
		},
		{
			MethodName: "AwardGameDiamond",
			Handler:    _GoldDiamond_AwardGameDiamond_Handler,
		},
		{
			MethodName: "GetConsumeRank",
			Handler:    _GoldDiamond_GetConsumeRank_Handler,
		},
		{
			MethodName: "GuildConsumeDayQoq",
			Handler:    _GoldDiamond_GuildConsumeDayQoq_Handler,
		},
		{
			MethodName: "RoomConsumeQoq",
			Handler:    _GoldDiamond_RoomConsumeQoq_Handler,
		},
		{
			MethodName: "ApplicationLimitOpe",
			Handler:    _GoldDiamond_ApplicationLimitOpe_Handler,
		},
		{
			MethodName: "ApplicationLimitQuery",
			Handler:    _GoldDiamond_ApplicationLimitQuery_Handler,
		},
		{
			MethodName: "GetGuildType",
			Handler:    _GoldDiamond_GetGuildType_Handler,
		},
		{
			MethodName: "GetGuildInfoV2",
			Handler:    _GoldDiamond_GetGuildInfoV2_Handler,
		},
		{
			MethodName: "AmuseLastIncomeTrend",
			Handler:    _GoldDiamond_AmuseLastIncomeTrend_Handler,
		},
		{
			MethodName: "AmuseIncomeTrend",
			Handler:    _GoldDiamond_AmuseIncomeTrend_Handler,
		},
		{
			MethodName: "AmuseIncome",
			Handler:    _GoldDiamond_AmuseIncome_Handler,
		},
		{
			MethodName: "AmuseChannel",
			Handler:    _GoldDiamond_AmuseChannel_Handler,
		},
		{
			MethodName: "AmuseChannelList",
			Handler:    _GoldDiamond_AmuseChannelList_Handler,
		},
		{
			MethodName: "GetAmuseChannelsIncome",
			Handler:    _GoldDiamond_GetAmuseChannelsIncome_Handler,
		},
		{
			MethodName: "RecommendExamLimitCheck",
			Handler:    _GoldDiamond_RecommendExamLimitCheck_Handler,
		},
		{
			MethodName: "RecommendExamOperation",
			Handler:    _GoldDiamond_RecommendExamOperation_Handler,
		},
		{
			MethodName: "RecommendExam",
			Handler:    _GoldDiamond_RecommendExam_Handler,
		},
		{
			MethodName: "GetRecommendExam",
			Handler:    _GoldDiamond_GetRecommendExam_Handler,
		},
		{
			MethodName: "SetRecommendExamTime",
			Handler:    _GoldDiamond_SetRecommendExamTime_Handler,
		},
		{
			MethodName: "GetExamContent",
			Handler:    _GoldDiamond_GetExamContent_Handler,
		},
		{
			MethodName: "YuyinExam",
			Handler:    _GoldDiamond_YuyinExam_Handler,
		},
		{
			MethodName: "GetYuyinExam",
			Handler:    _GoldDiamond_GetYuyinExam_Handler,
		},
		{
			MethodName: "GetYuyinExamDetail",
			Handler:    _GoldDiamond_GetYuyinExamDetail_Handler,
		},
		{
			MethodName: "YuyinExamOperation",
			Handler:    _GoldDiamond_YuyinExamOperation_Handler,
		},
		{
			MethodName: "YuyinExamGuildOwnerOperation",
			Handler:    _GoldDiamond_YuyinExamGuildOwnerOperation_Handler,
		},
		{
			MethodName: "GetYuyinExamForGuild",
			Handler:    _GoldDiamond_GetYuyinExamForGuild_Handler,
		},
		{
			MethodName: "GetExamTagList",
			Handler:    _GoldDiamond_GetExamTagList_Handler,
		},
		{
			MethodName: "GetYuyinExamStatus",
			Handler:    _GoldDiamond_GetYuyinExamStatus_Handler,
		},
		{
			MethodName: "DelYuyinExamApplication",
			Handler:    _GoldDiamond_DelYuyinExamApplication_Handler,
		},
		{
			MethodName: "ApplicationRecordForGuildOwner",
			Handler:    _GoldDiamond_ApplicationRecordForGuildOwner_Handler,
		},
		{
			MethodName: "GetAllHandlePresentCount",
			Handler:    _GoldDiamond_GetAllHandlePresentCount_Handler,
		},
		{
			MethodName: "GetAllHandlePresentOrderList",
			Handler:    _GoldDiamond_GetAllHandlePresentOrderList_Handler,
		},
		{
			MethodName: "AddOrder",
			Handler:    _GoldDiamond_AddOrder_Handler,
		},
		{
			MethodName: "GetGuildTypeInfo",
			Handler:    _GoldDiamond_GetGuildTypeInfo_Handler,
		},
		{
			MethodName: "GetGuildTypeHistory",
			Handler:    _GoldDiamond_GetGuildTypeHistory_Handler,
		},
		{
			MethodName: "GetGoldOrderCount",
			Handler:    _GoldDiamond_GetGoldOrderCount_Handler,
		},
		{
			MethodName: "GetGoldOrderList",
			Handler:    _GoldDiamond_GetGoldOrderList_Handler,
		},
		{
			MethodName: "ReplaceOrder",
			Handler:    _GoldDiamond_ReplaceOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "golddiamonn/golddiamonn.proto",
}

func init() {
	proto.RegisterFile("golddiamonn/golddiamonn.proto", fileDescriptor_golddiamonn_16e8414721ab9ede)
}

var fileDescriptor_golddiamonn_16e8414721ab9ede = []byte{
	// 6138 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x7c, 0x5b, 0x6f, 0x1b, 0x49,
	0x76, 0xb0, 0x9a, 0xa4, 0x44, 0xf2, 0xf0, 0x22, 0xaa, 0x6d, 0x4b, 0x34, 0x7d, 0xd3, 0xb4, 0xe7,
	0xe2, 0xf1, 0x37, 0x6b, 0x2f, 0xec, 0x9d, 0x5d, 0xef, 0xf7, 0xe1, 0xdb, 0x19, 0x5a, 0xa4, 0x64,
	0x7a, 0x74, 0x9b, 0x16, 0x35, 0xb3, 0x9a, 0xd9, 0x4d, 0xa7, 0x45, 0xb6, 0xa9, 0x86, 0xc9, 0xee,
	0x16, 0xab, 0x39, 0xb6, 0x16, 0xbb, 0x48, 0x90, 0x00, 0x01, 0x72, 0x79, 0x08, 0x82, 0x00, 0xc9,
	0xc3, 0x66, 0x83, 0xe4, 0x21, 0x48, 0x5e, 0x82, 0xbc, 0x2c, 0x82, 0x24, 0x08, 0x02, 0x24, 0x40,
	0x5e, 0xf3, 0x17, 0xf2, 0x9a, 0xd7, 0xfd, 0x05, 0x41, 0x9d, 0xaa, 0xea, 0xae, 0xbe, 0x90, 0x14,
	0xbd, 0xb3, 0x40, 0x90, 0x37, 0xd6, 0xa9, 0x53, 0xa7, 0x4e, 0x9d, 0x3a, 0xb7, 0xaa, 0x3a, 0x4d,
	0xb8, 0x35, 0x70, 0x87, 0xfd, 0xbe, 0x6d, 0x8e, 0x5c, 0xc7, 0x79, 0x28, 0xfd, 0x7e, 0xe0, 0x8d,
	0x5d, 0xdf, 0x55, 0x4b, 0x12, 0xa8, 0x71, 0x67, 0x6c, 0xf5, 0x5c, 0xa7, 0x67, 0x0f, 0xad, 0x6f,
	0x7c, 0xf5, 0xe8, 0xa1, 0xdc, 0x60, 0xd8, 0xda, 0x2f, 0x56, 0xe0, 0xea, 0xce, 0xc4, 0x1e, 0xf6,
	0x9b, 0x9e, 0x37, 0xb4, 0x7b, 0xa6, 0x6f, 0xbb, 0x4e, 0xc7, 0x79, 0xe1, 0xaa, 0xd7, 0xa1, 0x30,
	0xa0, 0x70, 0xc3, 0xee, 0xd7, 0x95, 0x4d, 0xe5, 0x5e, 0x45, 0xcf, 0x63, 0xbb, 0xd3, 0x57, 0xdf,
	0x87, 0x9a, 0x19, 0x62, 0x1b, 0xfe, 0x85, 0x67, 0xd5, 0x33, 0x88, 0xb2, 0x2a, 0xc1, 0xbb, 0x17,
	0x9e, 0xa5, 0xde, 0x85, 0x8a, 0x37, 0x34, 0xfd, 0x17, 0xee, 0x78, 0x64, 0x38, 0xe6, 0xc8, 0xaa,
	0x67, 0x37, 0x95, 0x7b, 0x45, 0xbd, 0x2c, 0x80, 0xfb, 0xe6, 0xc8, 0xa2, 0xf4, 0x02, 0xa4, 0x9e,
	0xeb, 0xf8, 0x96, 0xe3, 0xd7, 0x73, 0x88, 0xb7, 0x2a, 0xe0, 0x5b, 0x0c, 0xac, 0xde, 0x81, 0x52,
	0x80, 0x6a, 0xf7, 0xeb, 0xcb, 0x38, 0x2b, 0x08, 0x50, 0xa7, 0xaf, 0x7e, 0x08, 0xeb, 0x21, 0x2d,
	0x6b, 0xec, 0xdb, 0x2f, 0x38, 0x3b, 0xf5, 0x95, 0xcd, 0xec, 0xbd, 0xa2, 0x7e, 0x2d, 0xa0, 0x28,
	0x77, 0xaa, 0x2a, 0xe4, 0xfa, 0x16, 0xe9, 0xd5, 0xf3, 0x38, 0x2d, 0xfe, 0x56, 0xff, 0x3f, 0x14,
	0x5d, 0xcf, 0x1a, 0xb3, 0xd1, 0x85, 0x4d, 0xe5, 0x5e, 0xf5, 0xd1, 0x9d, 0x07, 0xb2, 0xbc, 0x25,
	0x91, 0x1d, 0x78, 0x16, 0x5d, 0xaf, 0x1e, 0x8e, 0xa0, 0xac, 0xf6, 0xc6, 0x96, 0xe9, 0x5b, 0x86,
	0x6f, 0x8f, 0xac, 0x7a, 0x71, 0x53, 0xb9, 0x97, 0xd5, 0x81, 0x81, 0xba, 0xf6, 0xc8, 0x52, 0x6f,
	0x01, 0xb8, 0x9e, 0x65, 0x78, 0x96, 0xeb, 0x0d, 0xad, 0x3a, 0xe0, 0xcc, 0x74, 0xfc, 0x21, 0x02,
	0xd4, 0x0d, 0xc8, 0x8f, 0xad, 0x73, 0xa3, 0xe7, 0xf8, 0xf5, 0x12, 0x2e, 0x73, 0x65, 0x6c, 0x9d,
	0x6f, 0x39, 0xbe, 0xfa, 0x2e, 0xac, 0x4a, 0x32, 0x30, 0x88, 0x3f, 0xae, 0x97, 0x71, 0x70, 0x25,
	0x94, 0xc3, 0x91, 0x3f, 0x56, 0xdf, 0x82, 0xf2, 0xd0, 0x3e, 0x1d, 0x9b, 0xe3, 0x0b, 0xb6, 0x45,
	0x15, 0xa4, 0x52, 0xe2, 0x30, 0xdc, 0x9e, 0x3a, 0xe4, 0x7d, 0x1f, 0xb7, 0xb5, 0x5e, 0x65, 0x7b,
	0xcc, 0x9b, 0xea, 0x03, 0xb8, 0x12, 0x4c, 0xc2, 0xf4, 0x00, 0x69, 0xac, 0x22, 0xd6, 0x9a, 0xe8,
	0x42, 0xcd, 0x41, 0x4a, 0xf2, 0x1e, 0x22, 0xbe, 0xdd, 0xaf, 0xd7, 0xa2, 0x7b, 0xb8, 0xc3, 0xc0,
	0xea, 0xb7, 0xa4, 0x2d, 0x62, 0xa4, 0xdd, 0x57, 0x8e, 0x35, 0xee, 0xf4, 0xeb, 0x6b, 0x38, 0xe0,
	0x6a, 0x64, 0xc0, 0x01, 0xeb, 0x53, 0xbf, 0x0d, 0x1b, 0xb1, 0x51, 0xe6, 0xc4, 0x3f, 0xb3, 0x47,
	0x03, 0x52, 0x57, 0xa3, 0x3b, 0xcb, 0xd4, 0x99, 0x77, 0xaa, 0xef, 0x40, 0xd5, 0x22, 0xbe, 0x79,
	0x3a, 0xb4, 0xc9, 0x19, 0xdb, 0x89, 0x2b, 0x4c, 0x58, 0x01, 0x14, 0x37, 0xe3, 0x31, 0xe4, 0xd9,
	0xd6, 0x59, 0xf5, 0xab, 0x9b, 0xca, 0xbd, 0xd2, 0xa3, 0xeb, 0x91, 0xad, 0x3e, 0x60, 0x7d, 0xd4,
	0x32, 0xba, 0xba, 0xc0, 0x54, 0x6f, 0x42, 0xd1, 0x22, 0xbe, 0x3d, 0x32, 0x7d, 0x8b, 0xd4, 0xaf,
	0xa1, 0x68, 0x42, 0x00, 0x15, 0x2e, 0xd5, 0x66, 0xb3, 0xe7, 0xd7, 0xd7, 0x71, 0x4a, 0xd1, 0xd4,
	0x3c, 0xb8, 0x12, 0xb7, 0x39, 0xdd, 0x3a, 0x9f, 0x65, 0x72, 0x37, 0xa0, 0x68, 0x13, 0x83, 0xda,
	0xef, 0xb8, 0x8f, 0xb6, 0x56, 0xd0, 0x0b, 0x36, 0xd1, 0xb1, 0x9d, 0xd8, 0xe8, 0x6c, 0x62, 0xa3,
	0xb5, 0xdd, 0x94, 0x19, 0x89, 0xa7, 0x7e, 0x08, 0x39, 0xdb, 0x79, 0xe1, 0xe2, 0x6c, 0xa5, 0x47,
	0x6f, 0x45, 0x96, 0x9c, 0xe6, 0x15, 0x74, 0x44, 0xd7, 0x7e, 0x4b, 0x01, 0x55, 0xea, 0xd9, 0xb5,
	0x89, 0x4f, 0xf9, 0x5f, 0x87, 0x15, 0xf7, 0xc5, 0x0b, 0x62, 0xf9, 0x9c, 0x7b, 0xde, 0x52, 0xaf,
	0xc2, 0xf2, 0xd0, 0x1e, 0xd9, 0x3e, 0x77, 0x12, 0xac, 0x11, 0x5d, 0x52, 0x76, 0xce, 0x92, 0x72,
	0xc9, 0x25, 0x7d, 0x95, 0xe4, 0x81, 0x78, 0xea, 0x77, 0x60, 0x99, 0xb2, 0x48, 0xea, 0xca, 0x66,
	0xf6, 0x72, 0x4b, 0x62, 0xf8, 0xea, 0x7b, 0x20, 0x3b, 0x2f, 0x34, 0x3b, 0xc6, 0x6e, 0x55, 0x02,
	0x6f, 0x39, 0xbe, 0xf6, 0x6f, 0x0a, 0xac, 0x45, 0x2d, 0x9f, 0xae, 0x3d, 0xe2, 0x2c, 0x94, 0x85,
	0x9d, 0x85, 0xbc, 0xf5, 0x99, 0xe8, 0xd6, 0x0b, 0xd7, 0x94, 0x95, 0x5c, 0x53, 0xd4, 0x75, 0xe4,
	0xe2, 0xae, 0x23, 0x2e, 0xbd, 0xe5, 0xa4, 0xf4, 0xbe, 0x9b, 0x58, 0x04, 0xf1, 0xe8, 0x54, 0x3d,
	0xb7, 0x6f, 0xf1, 0xed, 0xc3, 0xdf, 0x6a, 0x0d, 0xb2, 0x23, 0x32, 0x40, 0xa6, 0x8a, 0x3a, 0xfd,
	0xa9, 0xfd, 0x42, 0x81, 0xb2, 0x6c, 0x0f, 0x74, 0xba, 0x91, 0xeb, 0xf8, 0x67, 0x86, 0xed, 0xf4,
	0xdc, 0x11, 0x1b, 0x5e, 0xd4, 0x4b, 0x08, 0xeb, 0x20, 0x88, 0x3a, 0x43, 0xd6, 0x69, 0xa0, 0xc5,
	0x66, 0xd0, 0x62, 0x81, 0x81, 0x3a, 0xd4, 0x4c, 0x99, 0xb1, 0x90, 0x49, 0x10, 0x22, 0x44, 0x93,
	0x52, 0xe7, 0x3f, 0xd9, 0xd8, 0x1c, 0x8e, 0x2d, 0x71, 0x18, 0x0e, 0x6e, 0x40, 0xc1, 0xb7, 0xcc,
	0x11, 0xb1, 0x7f, 0x24, 0xd6, 0x1a, 0xb4, 0xd5, 0xdb, 0x00, 0x66, 0xaf, 0xe7, 0x8e, 0x3c, 0xd3,
	0xb9, 0x20, 0x3c, 0x08, 0x48, 0x10, 0xee, 0x1f, 0xd0, 0x64, 0x0d, 0x1c, 0x84, 0x31, 0xa0, 0x82,
	0xfe, 0x01, 0xa1, 0x5d, 0x0a, 0xd4, 0xfe, 0x7d, 0x19, 0xae, 0x4a, 0x02, 0xdb, 0x42, 0x37, 0x3e,
	0xc7, 0x68, 0xff, 0x17, 0xc6, 0xc9, 0x94, 0xd8, 0x93, 0xbf, 0x4c, 0xec, 0x29, 0xcc, 0x8c, 0x3d,
	0xc5, 0x4b, 0xc5, 0x1e, 0x58, 0x24, 0xf6, 0x94, 0x16, 0x8d, 0x3d, 0xe5, 0x37, 0x8b, 0x3d, 0x95,
	0xc5, 0x62, 0x4f, 0x75, 0x4e, 0xec, 0x59, 0x7d, 0xb3, 0xd8, 0x53, 0x9b, 0x11, 0x7b, 0xd6, 0xa2,
	0xb1, 0xc7, 0x49, 0xd3, 0x63, 0xe2, 0xa9, 0xd7, 0x60, 0xc5, 0x26, 0x86, 0xe9, 0x79, 0xa8, 0xc5,
	0x05, 0x7d, 0xd9, 0x26, 0x4d, 0xcf, 0xa3, 0xea, 0x6d, 0x13, 0x23, 0x74, 0xdf, 0x05, 0x3d, 0x6f,
	0x93, 0x5d, 0x74, 0xe0, 0x6f, 0x43, 0x15, 0xe1, 0x86, 0xe5, 0xf4, 0xd9, 0xea, 0xb2, 0x98, 0xe3,
	0x94, 0x11, 0xda, 0x76, 0xfa, 0x74, 0x71, 0xda, 0x2e, 0x94, 0x51, 0x28, 0xe8, 0x6b, 0x67, 0xdb,
	0x4b, 0x5c, 0x69, 0x32, 0x49, 0xb7, 0xf5, 0x4c, 0xa6, 0x46, 0xbc, 0x59, 0xd4, 0x6e, 0x01, 0x48,
	0xca, 0xc3, 0x68, 0x15, 0x07, 0x42, 0x69, 0x34, 0x83, 0x53, 0x7a, 0xb3, 0xe0, 0x75, 0x89, 0x90,
	0xfb, 0x4c, 0x9e, 0x80, 0x78, 0x34, 0xde, 0x09, 0x56, 0x59, 0x74, 0xaa, 0xe8, 0x05, 0xce, 0x2b,
	0x09, 0x3b, 0xc3, 0xb8, 0xc3, 0x3a, 0x69, 0xc4, 0xf9, 0x6b, 0x05, 0x56, 0x83, 0x55, 0xf3, 0x78,
	0xf3, 0x24, 0x19, 0x6f, 0x1a, 0xc9, 0x58, 0x87, 0x3a, 0x73, 0xf9, 0x50, 0x13, 0x15, 0x59, 0x36,
	0x26, 0xb2, 0xcb, 0x04, 0xe5, 0x0f, 0x62, 0x9c, 0xce, 0xdc, 0x22, 0xed, 0x67, 0x19, 0x58, 0x6d,
	0xbe, 0x32, 0xc7, 0xfd, 0x16, 0x32, 0xdd, 0x9f, 0xa3, 0x1f, 0xd7, 0xa1, 0xe0, 0x99, 0x76, 0xdf,
	0x98, 0x84, 0x9c, 0xd3, 0xf6, 0xb1, 0xdd, 0xa7, 0xfe, 0xee, 0xd4, 0x9d, 0x0c, 0xce, 0xfc, 0x50,
	0x11, 0x73, 0x3a, 0x30, 0x10, 0xda, 0xd8, 0x13, 0x28, 0x11, 0x77, 0x32, 0xee, 0x59, 0x21, 0xeb,
	0xd5, 0x47, 0x1b, 0x11, 0x89, 0xb1, 0x50, 0x85, 0xe2, 0x02, 0x86, 0x8b, 0xab, 0x5e, 0x87, 0x15,
	0x1e, 0xd7, 0x96, 0x51, 0xbd, 0x79, 0x8b, 0x72, 0xe3, 0x8e, 0xfb, 0xd6, 0x98, 0x32, 0xba, 0xc2,
	0x6c, 0x0c, 0xdb, 0x2c, 0x5b, 0x9b, 0x38, 0xf6, 0xb9, 0x41, 0xec, 0x81, 0xc3, 0xfd, 0x63, 0x81,
	0x02, 0x8e, 0xec, 0x41, 0x78, 0xd4, 0x28, 0x48, 0xf1, 0x7c, 0x1d, 0x56, 0xac, 0xd7, 0xbe, 0xe9,
	0x30, 0x57, 0x58, 0xd4, 0x79, 0x4b, 0x5b, 0x8b, 0xc9, 0x87, 0x78, 0xda, 0xfb, 0x50, 0xde, 0x71,
	0x87, 0x7d, 0xdd, 0x75, 0x47, 0x64, 0xb6, 0xbc, 0xb4, 0x87, 0x32, 0x2a, 0xf1, 0xf0, 0x44, 0x72,
	0x66, 0x3a, 0x8e, 0x35, 0x94, 0x74, 0x10, 0x38, 0xa8, 0xd3, 0x27, 0xda, 0x07, 0x50, 0xe3, 0xbb,
	0x67, 0xfb, 0xc2, 0x5e, 0xeb, 0x90, 0x17, 0x3e, 0x55, 0x26, 0x6f, 0xf7, 0xb5, 0x27, 0x50, 0x6e,
	0x99, 0x17, 0xdd, 0xb1, 0xe5, 0xe0, 0x76, 0xd3, 0x4c, 0xa1, 0x6f, 0x5e, 0x70, 0x2c, 0xfa, 0x53,
	0x12, 0x5d, 0x46, 0x16, 0x9d, 0xf6, 0x43, 0x28, 0x52, 0xa6, 0x3a, 0xfd, 0x6d, 0x0b, 0xe5, 0x3b,
	0x76, 0xdd, 0x51, 0x40, 0x9f, 0xb7, 0x28, 0xb9, 0x17, 0x96, 0x18, 0x49, 0x7f, 0xd2, 0xe0, 0xc3,
	0xf2, 0x0c, 0xdf, 0x75, 0xdc, 0x57, 0xc6, 0xb9, 0x7b, 0x8e, 0x1b, 0x9d, 0xd1, 0x2b, 0x08, 0xee,
	0x52, 0xe8, 0xa7, 0xee, 0xb9, 0xf6, 0xcf, 0xcb, 0xf1, 0x75, 0x10, 0x8f, 0x2a, 0xaf, 0xef, 0xf6,
	0xcd, 0x0b, 0x39, 0x49, 0xc9, 0xea, 0x25, 0x84, 0xf1, 0x24, 0xe5, 0x3d, 0x58, 0xbd, 0xb0, 0x48,
	0x04, 0x8b, 0xcd, 0x5e, 0x15, 0x60, 0x8e, 0xf8, 0x3e, 0xd4, 0xfc, 0x33, 0x9b, 0x44, 0x92, 0x1e,
	0xe6, 0xfb, 0x56, 0x03, 0x78, 0x88, 0x3a, 0x34, 0x89, 0x1f, 0x41, 0xcd, 0x31, 0xd4, 0x00, 0xce,
	0x51, 0xbf, 0x03, 0x25, 0xb6, 0x74, 0xe3, 0x85, 0x65, 0x91, 0xfa, 0x32, 0x26, 0xb0, 0xeb, 0x11,
	0x15, 0x0d, 0xa4, 0xa6, 0x03, 0x43, 0xdd, 0xb6, 0x2c, 0xa2, 0x7e, 0x04, 0x55, 0xca, 0xb2, 0x4f,
	0x77, 0xc2, 0xc0, 0x7c, 0x7e, 0x05, 0xc7, 0x46, 0xc3, 0x88, 0xbc, 0x57, 0x7a, 0xb9, 0x2f, 0xef,
	0xdc, 0x1d, 0x28, 0x11, 0x6b, 0xfc, 0x95, 0x35, 0x66, 0xd6, 0x93, 0x67, 0x47, 0x55, 0x06, 0x12,
	0x47, 0x55, 0xc9, 0x31, 0x14, 0xe2, 0x8e, 0x61, 0x03, 0xf2, 0x94, 0x01, 0xba, 0x21, 0x45, 0xdc,
	0x90, 0x95, 0xbe, 0x79, 0xf1, 0xa9, 0x7b, 0x4e, 0x0d, 0x81, 0xad, 0x9c, 0x76, 0x01, 0x76, 0x15,
	0x10, 0x40, 0x3b, 0xdf, 0x81, 0x2a, 0x8b, 0x38, 0x22, 0x18, 0x61, 0xd0, 0x2e, 0xe8, 0x15, 0x8c,
	0x3c, 0x02, 0x48, 0x99, 0xa3, 0x92, 0x12, 0x13, 0x94, 0x91, 0x0a, 0x70, 0x10, 0xa5, 0x93, 0xa2,
	0x16, 0x95, 0x14, 0xb5, 0x50, 0x1f, 0xc3, 0x3a, 0x1d, 0x65, 0x30, 0x64, 0x62, 0x8e, 0x68, 0x02,
	0x3d, 0xb6, 0x5d, 0x76, 0xf6, 0xcd, 0xea, 0x57, 0x68, 0xef, 0x1e, 0xed, 0x3c, 0x32, 0x47, 0xd6,
	0x21, 0x76, 0xa9, 0xcf, 0xe0, 0x8a, 0x34, 0x88, 0x32, 0x31, 0xb4, 0x89, 0x5f, 0x5f, 0x9d, 0x27,
	0xe0, 0x5a, 0x40, 0xac, 0x65, 0x5e, 0xd0, 0x10, 0xa0, 0xde, 0x83, 0x1a, 0x9f, 0xd9, 0x7e, 0x2d,
	0x34, 0xa1, 0xc6, 0xd4, 0x0b, 0xe1, 0x47, 0xf6, 0x6b, 0xa6, 0x08, 0xda, 0x7f, 0x29, 0x70, 0x0d,
	0xf5, 0x77, 0x8b, 0x9b, 0x26, 0x82, 0xe7, 0x38, 0xc7, 0x30, 0x7e, 0x65, 0xd2, 0xe3, 0x57, 0x56,
	0x8e, 0x5f, 0x1f, 0x02, 0x8c, 0x4d, 0x67, 0x10, 0xf1, 0x86, 0x31, 0x55, 0xa3, 0xdd, 0x2c, 0x76,
	0x8c, 0xc5, 0x4f, 0xaa, 0x07, 0xa7, 0xd6, 0xc0, 0x76, 0x98, 0x9e, 0x30, 0x7f, 0x58, 0x44, 0x08,
	0xaa, 0xc9, 0x75, 0x28, 0x04, 0xb9, 0xc0, 0x0a, 0x76, 0xe6, 0x2d, 0x96, 0x06, 0x48, 0x56, 0x9e,
	0x97, 0xad, 0x5c, 0x9b, 0x40, 0xf5, 0xc8, 0x37, 0x7d, 0xb6, 0x44, 0x54, 0xc6, 0x1b, 0x50, 0x24,
	0xbe, 0xc9, 0x1d, 0x39, 0xb3, 0xd2, 0x02, 0x05, 0x20, 0x99, 0x3a, 0xe4, 0x47, 0xd6, 0xe8, 0xd4,
	0x1a, 0x13, 0x6e, 0x9a, 0xa2, 0x49, 0x7b, 0x98, 0x50, 0x09, 0x37, 0x45, 0xd1, 0xa4, 0x0e, 0x17,
	0x0d, 0x8a, 0x99, 0x1d, 0xfe, 0xd6, 0xfe, 0x25, 0x5d, 0xc4, 0xc4, 0xa3, 0x4b, 0xf4, 0x5d, 0xdf,
	0x1c, 0x32, 0x23, 0x64, 0xf3, 0x17, 0x11, 0x82, 0xb6, 0x76, 0x17, 0x2a, 0xac, 0x5b, 0x4c, 0xc6,
	0xd8, 0x28, 0x23, 0xb0, 0xc3, 0x67, 0xbc, 0x01, 0x45, 0xc7, 0x7a, 0xed, 0x1b, 0x9e, 0x39, 0xb0,
	0xc4, 0xd1, 0x96, 0x02, 0x0e, 0xcd, 0x81, 0xa5, 0xb6, 0xa1, 0x86, 0xeb, 0x13, 0xe7, 0x21, 0x6a,
	0xaf, 0x39, 0x54, 0xa7, 0x1b, 0x91, 0x0d, 0x88, 0x8a, 0x45, 0xaf, 0x92, 0x48, 0x5b, 0xfb, 0x26,
	0x3f, 0xd1, 0x77, 0x43, 0xbf, 0x34, 0x27, 0x1c, 0x5c, 0xc0, 0x06, 0xa5, 0x89, 0x03, 0xd0, 0x91,
	0x84, 0x32, 0x9f, 0xe6, 0x83, 0xbf, 0x2e, 0x71, 0x9b, 0x29, 0xcc, 0x12, 0x4f, 0x7d, 0x0e, 0xb8,
	0x2a, 0x83, 0xce, 0x66, 0xf0, 0x8b, 0x08, 0x2a, 0x88, 0xb7, 0x13, 0x82, 0x48, 0x61, 0x5a, 0x2f,
	0xd3, 0xb1, 0x0c, 0xf6, 0xc2, 0xd5, 0xb6, 0xf9, 0x14, 0x7b, 0xa1, 0x47, 0x9d, 0x63, 0x31, 0x57,
	0x61, 0x19, 0x0d, 0x8f, 0x2f, 0x8d, 0x35, 0xb4, 0x33, 0xa8, 0x09, 0x43, 0xde, 0x73, 0xc7, 0x56,
	0x3c, 0xb2, 0x65, 0x59, 0x64, 0x4b, 0x06, 0xa7, 0x30, 0xd6, 0x65, 0x23, 0x69, 0x82, 0x24, 0xc2,
	0x5c, 0x44, 0x84, 0xda, 0x0f, 0x52, 0x38, 0x26, 0x9e, 0xda, 0x86, 0x55, 0xa6, 0xff, 0xa1, 0x3b,
	0x67, 0x52, 0xb9, 0x95, 0xea, 0x6d, 0x04, 0x93, 0x7a, 0x05, 0x8d, 0x44, 0x38, 0x20, 0xed, 0xa7,
	0x0a, 0x34, 0x90, 0xfc, 0xb1, 0x73, 0x64, 0xf9, 0xfe, 0xd0, 0x1a, 0x59, 0x8e, 0xdf, 0xb2, 0x7c,
	0xd3, 0x1e, 0xce, 0x91, 0xcb, 0x87, 0x00, 0xe7, 0x13, 0x4b, 0x4e, 0xc2, 0xe3, 0xbe, 0xe1, 0x53,
	0xda, 0xcd, 0x7c, 0xc3, 0xb9, 0xf8, 0x29, 0x39, 0xa0, 0x6c, 0xba, 0x03, 0xca, 0x49, 0x0e, 0x48,
	0x9b, 0xc0, 0xc6, 0x14, 0xee, 0x16, 0x48, 0x08, 0x66, 0xc8, 0x9c, 0x26, 0x86, 0x34, 0x4f, 0xcc,
	0x85, 0x79, 0xe2, 0xc4, 0xa6, 0x36, 0x30, 0x55, 0x28, 0xc4, 0x53, 0xbf, 0x07, 0xf9, 0x3e, 0x36,
	0x48, 0xaa, 0x22, 0x4e, 0x1b, 0x29, 0x06, 0x45, 0xed, 0x3e, 0x13, 0xb5, 0x7b, 0xed, 0x7b, 0x70,
	0x67, 0xc7, 0xf2, 0x91, 0x06, 0x91, 0x89, 0x1c, 0x4d, 0x46, 0x23, 0x73, 0x7c, 0x41, 0x37, 0x65,
	0xd6, 0x11, 0x41, 0xfb, 0x0b, 0x65, 0x0e, 0x01, 0x76, 0xc6, 0x60, 0xe9, 0x0b, 0x15, 0x14, 0xf7,
	0x9d, 0x08, 0xa0, 0x89, 0x56, 0x3c, 0x03, 0xca, 0x24, 0x33, 0xa0, 0x20, 0x5e, 0xd3, 0xf1, 0x4c,
	0xa6, 0x2c, 0x5e, 0xf3, 0xf1, 0x29, 0x69, 0x8c, 0x7c, 0xcd, 0xa3, 0xfd, 0x5e, 0x86, 0xbf, 0x26,
	0xb0, 0xf6, 0xa5, 0xd4, 0x6d, 0xb1, 0xc0, 0x15, 0x8d, 0x40, 0xb9, 0x59, 0x11, 0x68, 0x39, 0x1a,
	0x81, 0xa2, 0x6a, 0xbd, 0x72, 0x59, 0xb5, 0x96, 0x0f, 0x1d, 0xf9, 0xe8, 0xa1, 0xe3, 0x16, 0x40,
	0x98, 0x4f, 0x8b, 0xac, 0x28, 0x48, 0xa7, 0xb5, 0x3f, 0x54, 0x60, 0x2d, 0x21, 0x8c, 0x5f, 0xa5,
	0x76, 0xc7, 0x4f, 0x41, 0x4c, 0x00, 0xd2, 0x29, 0x48, 0xfb, 0x5d, 0x11, 0xf6, 0x42, 0x57, 0xca,
	0xd9, 0x9a, 0xb1, 0x41, 0x1b, 0x90, 0x67, 0x0e, 0x5a, 0x9c, 0xba, 0x90, 0xe5, 0x4e, 0xc0, 0x72,
	0x36, 0x8d, 0xe5, 0x5c, 0x84, 0x65, 0x7a, 0xe6, 0x31, 0x7d, 0x71, 0x39, 0x87, 0xbf, 0xb5, 0x1f,
	0xa5, 0xa9, 0x0a, 0xfa, 0xbf, 0x2a, 0x0f, 0x8d, 0x51, 0x5b, 0xbc, 0x9d, 0x76, 0xbc, 0x95, 0x86,
	0x56, 0x6c, 0xa9, 0x35, 0xc7, 0x16, 0x9f, 0xc0, 0x8d, 0x84, 0x31, 0x4b, 0x76, 0x38, 0x23, 0x88,
	0xfe, 0xa7, 0x32, 0x63, 0x28, 0xf1, 0xd4, 0x4d, 0x28, 0x0b, 0x75, 0xc1, 0xb3, 0x3c, 0x33, 0x42,
	0xe0, 0x2a, 0xb3, 0xe5, 0xf8, 0xcc, 0x0c, 0xc3, 0x0c, 0x22, 0x34, 0xc3, 0x20, 0x81, 0x60, 0x66,
	0xcc, 0x73, 0x10, 0x61, 0x86, 0x22, 0x05, 0xc1, 0xb4, 0x99, 0xc9, 0xe7, 0xd4, 0x1c, 0x9a, 0x4e,
	0x4f, 0xc8, 0x9a, 0xaf, 0xff, 0x29, 0x03, 0xbe, 0x79, 0xaa, 0xa6, 0xbd, 0x86, 0xd5, 0x2d, 0x76,
	0xb9, 0xaa, 0x9b, 0xce, 0xcb, 0x8e, 0x6f, 0x8d, 0xa8, 0x2d, 0x9a, 0x43, 0xdb, 0x24, 0xfc, 0x6a,
	0x97, 0x35, 0x50, 0xc4, 0x76, 0xef, 0x25, 0xbb, 0xb0, 0x64, 0x17, 0xc4, 0x05, 0x0a, 0xc0, 0xcb,
	0xca, 0x3a, 0xe4, 0xcd, 0x5e, 0xcf, 0x9d, 0x38, 0xbe, 0xb8, 0xd0, 0xe5, 0x4d, 0xf9, 0xaa, 0x97,
	0x47, 0x44, 0xde, 0xd4, 0x5e, 0x40, 0x55, 0x9a, 0x99, 0xee, 0xc4, 0x77, 0xc3, 0xcb, 0x5f, 0x34,
	0x5b, 0x65, 0x66, 0xa6, 0x2a, 0x2e, 0x85, 0x85, 0xe1, 0x4e, 0xb9, 0xe7, 0xd0, 0xbe, 0x88, 0xce,
	0x43, 0x3c, 0xf5, 0x19, 0xac, 0x89, 0x79, 0xc6, 0xa6, 0xf3, 0x92, 0x25, 0xf9, 0x4c, 0xef, 0x6e,
	0x46, 0x26, 0x8b, 0x49, 0x46, 0x5f, 0xed, 0x85, 0x00, 0x9a, 0xe6, 0x6b, 0x06, 0xac, 0x51, 0xe3,
	0xe2, 0x78, 0x9f, 0xba, 0xe7, 0x73, 0xdc, 0x5f, 0xd4, 0x89, 0x64, 0x62, 0x4e, 0x44, 0xa4, 0x1e,
	0xd9, 0x20, 0xf5, 0xd0, 0xfe, 0x43, 0x49, 0xcc, 0xc0, 0x3c, 0xff, 0xcc, 0xac, 0x59, 0x48, 0x3c,
	0x13, 0x91, 0xb8, 0x7a, 0x9f, 0xae, 0x7b, 0xe4, 0x99, 0x63, 0xcb, 0x08, 0x87, 0xf3, 0xa3, 0x2c,
	0xef, 0x38, 0x12, 0x54, 0xde, 0x03, 0x01, 0x32, 0xa2, 0xfb, 0x57, 0xe5, 0x60, 0xce, 0x11, 0xe5,
	0x99, 0x1e, 0xc2, 0x96, 0xf1, 0x10, 0x46, 0x7f, 0xaa, 0x37, 0x41, 0x2c, 0x89, 0x5f, 0x96, 0x84,
	0x6b, 0xb4, 0xfb, 0x5a, 0x4b, 0xe4, 0xe2, 0x6c, 0x7c, 0x0b, 0x8f, 0x75, 0x71, 0xb1, 0xc5, 0xae,
	0xaa, 0xb8, 0x5c, 0x72, 0xa1, 0x5c, 0x7e, 0xae, 0xa4, 0x92, 0xf9, 0x9f, 0x2e, 0x1b, 0xed, 0x6f,
	0x95, 0xc8, 0x8d, 0x2c, 0xde, 0xad, 0xce, 0x7b, 0x81, 0xbf, 0x01, 0x45, 0x76, 0xf5, 0x4a, 0x65,
	0xc0, 0xd8, 0x2e, 0x20, 0xa0, 0x65, 0x5e, 0x5c, 0xee, 0x5e, 0x96, 0xae, 0xdb, 0xf5, 0xac, 0x63,
	0x62, 0x8d, 0xf9, 0x1b, 0x82, 0x68, 0xaa, 0xb7, 0x01, 0x26, 0x5e, 0x9f, 0xbf, 0x52, 0x8b, 0x20,
	0x12, 0x42, 0xb4, 0xbf, 0x57, 0x60, 0x3d, 0xce, 0xf0, 0x2f, 0x7d, 0x2b, 0xf9, 0x31, 0x00, 0x63,
	0x1a, 0x13, 0xde, 0x4c, 0xca, 0x7b, 0x64, 0x9a, 0x8c, 0x74, 0x26, 0x06, 0x14, 0xd7, 0x25, 0xae,
	0x64, 0x1f, 0xa7, 0x33, 0x3e, 0xfb, 0x92, 0xf2, 0x77, 0x14, 0xa8, 0xc7, 0x47, 0x61, 0xa6, 0xf0,
	0xb5, 0xe6, 0x35, 0x97, 0xb8, 0x5b, 0x7d, 0x35, 0x8d, 0x0f, 0xe2, 0xa9, 0x1f, 0x01, 0x4b, 0x59,
	0x3a, 0xe1, 0x71, 0xe1, 0x32, 0xd2, 0x0b, 0xc6, 0xe0, 0x13, 0x1a, 0x8d, 0x30, 0xce, 0x64, 0x24,
	0xee, 0x9f, 0x45, 0x5b, 0xeb, 0x40, 0x43, 0xb7, 0x7a, 0xee, 0x68, 0x64, 0x39, 0xfd, 0xf6, 0x6b,
	0x73, 0x84, 0x04, 0xb6, 0xce, 0xac, 0x1e, 0xba, 0xe8, 0x1a, 0x64, 0x27, 0xc1, 0xea, 0xe9, 0xcf,
	0x59, 0x9e, 0xf7, 0x09, 0xdc, 0x98, 0x4a, 0x8a, 0x44, 0x5f, 0x1b, 0x94, 0xc8, 0x6b, 0x83, 0xf6,
	0x1b, 0x50, 0xa2, 0x03, 0xc4, 0x03, 0xd7, 0x22, 0xb3, 0x52, 0x1f, 0x6b, 0xbd, 0x36, 0x47, 0x68,
	0x0c, 0x44, 0xdc, 0x6b, 0x53, 0x08, 0xd5, 0x67, 0x42, 0x0d, 0x86, 0xd8, 0x03, 0xc7, 0x70, 0x27,
	0x3e, 0x47, 0x61, 0xd2, 0x2f, 0x53, 0xe8, 0xc1, 0x04, 0x2d, 0x9c, 0x68, 0xbf, 0x9f, 0x85, 0x6b,
	0x11, 0xde, 0x9f, 0x9a, 0x84, 0x1d, 0x0f, 0xdf, 0x85, 0x55, 0xe9, 0x2d, 0xc9, 0x08, 0xf9, 0xaa,
	0x0c, 0x82, 0x57, 0xa4, 0xe3, 0xd9, 0x1c, 0x3e, 0x82, 0xbc, 0x37, 0x19, 0x7b, 0x2e, 0x61, 0x7a,
	0x5b, 0x7d, 0x54, 0x8f, 0xec, 0x1e, 0x9d, 0xee, 0x90, 0xf5, 0xeb, 0x02, 0x91, 0x4e, 0x8b, 0xab,
	0x92, 0xc2, 0x47, 0x8e, 0x3f, 0x5d, 0x52, 0x41, 0x05, 0x21, 0xe4, 0x0e, 0x94, 0x7a, 0x93, 0xf1,
	0xd8, 0x72, 0x7c, 0xc3, 0x37, 0x07, 0xe2, 0x2d, 0x90, 0x83, 0xba, 0xe6, 0x00, 0x73, 0x01, 0x14,
	0x8f, 0x39, 0xe0, 0xce, 0x39, 0x8f, 0xc2, 0x31, 0x07, 0x89, 0x27, 0x4c, 0x71, 0x3d, 0x18, 0x7b,
	0xc2, 0xb4, 0x59, 0xd2, 0x82, 0x54, 0x26, 0xf4, 0xf0, 0x52, 0x60, 0x87, 0x17, 0x0a, 0x38, 0xb6,
	0xfb, 0x44, 0x7d, 0x0c, 0xeb, 0xb2, 0x88, 0x24, 0x96, 0xd9, 0xdb, 0xdf, 0x95, 0x50, 0x52, 0x21,
	0xe3, 0x0d, 0x9a, 0x7a, 0x13, 0xf2, 0xca, 0x1d, 0xf7, 0x79, 0x79, 0x4c, 0xd0, 0xd6, 0xfe, 0x4e,
	0x81, 0xf5, 0xc8, 0x6e, 0x74, 0x59, 0xfe, 0xf4, 0xc2, 0xa5, 0xb6, 0x70, 0x6a, 0x12, 0xcb, 0x90,
	0x2a, 0x1b, 0xb4, 0x68, 0xc2, 0x90, 0xb6, 0x8b, 0x7a, 0xe1, 0x54, 0xec, 0xa7, 0x58, 0x09, 0xae,
	0x96, 0x1b, 0x83, 0xd0, 0x96, 0xa8, 0x8b, 0xcb, 0x22, 0xf5, 0x46, 0x62, 0xaf, 0x0e, 0x04, 0x86,
	0xe4, 0xe2, 0xb4, 0x3f, 0x51, 0xa0, 0x12, 0xe9, 0xa4, 0x69, 0xb1, 0xe3, 0xfa, 0xe2, 0xc1, 0x1c,
	0x7f, 0xab, 0x0f, 0x61, 0x85, 0x46, 0x9b, 0x09, 0xe1, 0x27, 0xef, 0x8d, 0x04, 0xf1, 0x23, 0xec,
	0xd6, 0x39, 0x1a, 0x95, 0x12, 0x9b, 0xc3, 0x1d, 0xf3, 0x4c, 0x2b, 0x68, 0xd3, 0x5c, 0x31, 0x98,
	0x3f, 0x3c, 0x31, 0x55, 0xf4, 0x4a, 0x00, 0x45, 0x8f, 0xee, 0xc1, 0xf5, 0x88, 0x4c, 0x42, 0xf6,
	0x67, 0xbb, 0xb8, 0x88, 0x2c, 0x32, 0x8b, 0xc8, 0xe2, 0x66, 0xcc, 0xa5, 0x48, 0x33, 0x12, 0x4f,
	0x7b, 0x0e, 0xb5, 0x48, 0x2f, 0x65, 0xe3, 0xdb, 0x91, 0x52, 0x95, 0xcb, 0x6c, 0x28, 0xab, 0x55,
	0xb9, 0x02, 0x6b, 0x31, 0x5a, 0xc4, 0xd3, 0xfe, 0x51, 0x81, 0x2b, 0x3b, 0x96, 0x9f, 0x98, 0xe4,
	0x6b, 0xb1, 0xe4, 0x5c, 0x10, 0x7e, 0xaa, 0xb1, 0x43, 0x4b, 0x64, 0xa5, 0x18, 0x01, 0x11, 0x57,
	0x0a, 0x15, 0xb9, 0xf4, 0x50, 0xb1, 0x2c, 0x5f, 0x9d, 0x98, 0x70, 0x35, 0xc9, 0x3b, 0x96, 0xbe,
	0xe4, 0xa4, 0xdb, 0xa2, 0xbb, 0xd3, 0x25, 0x14, 0x98, 0x0a, 0x13, 0x11, 0xf5, 0xa5, 0xe1, 0xb3,
	0x23, 0xfd, 0xa9, 0xfd, 0x18, 0x36, 0x8e, 0x62, 0x53, 0x50, 0x45, 0x99, 0x5f, 0xa4, 0x34, 0xdd,
	0x6e, 0xd2, 0x3c, 0x49, 0x36, 0xd5, 0x93, 0x68, 0x0d, 0xa8, 0xa7, 0xcf, 0x4e, 0x3c, 0xed, 0x04,
	0xd6, 0x76, 0x2c, 0x5f, 0x8a, 0x04, 0x0b, 0xd6, 0x60, 0x84, 0xac, 0x25, 0xa6, 0x7d, 0x0e, 0x6a,
	0x9c, 0x34, 0xf1, 0xd4, 0x6f, 0x41, 0x81, 0xd7, 0x5a, 0x88, 0x83, 0x68, 0xd2, 0x35, 0x0b, 0xfc,
	0x00, 0x53, 0xfb, 0x88, 0x45, 0xab, 0xae, 0x39, 0x40, 0x8f, 0x72, 0x0d, 0x56, 0x7c, 0x73, 0x10,
	0xb2, 0xb7, 0xec, 0x9b, 0x03, 0xf6, 0xa0, 0x49, 0xc1, 0xd2, 0xf9, 0x29, 0xef, 0x9b, 0x03, 0x7a,
	0x7c, 0xd2, 0x9a, 0xc1, 0x3a, 0xbb, 0xe6, 0x40, 0xbc, 0x51, 0x7f, 0xc0, 0x75, 0x4b, 0x99, 0x12,
	0x22, 0xba, 0xe6, 0x20, 0xd4, 0x2a, 0xad, 0x15, 0xac, 0x27, 0x20, 0x41, 0x3c, 0xf5, 0x41, 0xb4,
	0x40, 0x2a, 0x95, 0x88, 0x54, 0x17, 0xa5, 0xfd, 0x41, 0x16, 0xea, 0x27, 0x93, 0x0b, 0xdb, 0xa1,
	0x7d, 0xf1, 0x22, 0xd1, 0x85, 0xa2, 0xb0, 0x1c, 0x66, 0xb2, 0xd1, 0x30, 0x43, 0xe9, 0x8c, 0x87,
	0x3c, 0x11, 0xa5, 0x3f, 0xe5, 0x02, 0x86, 0xe5, 0x48, 0x01, 0x43, 0xea, 0x8e, 0xae, 0xa4, 0x87,
	0xa4, 0x8f, 0xa0, 0x7c, 0x41, 0x59, 0x37, 0xb8, 0x47, 0xcd, 0xa3, 0xdc, 0xa2, 0x07, 0xba, 0x60,
	0x6d, 0xdc, 0xad, 0x96, 0x70, 0x04, 0x6b, 0xe0, 0xc9, 0x81, 0x66, 0x06, 0x38, 0x09, 0xbb, 0xe0,
	0x29, 0x50, 0x00, 0x52, 0xbf, 0x03, 0x25, 0xeb, 0xb5, 0x67, 0x8f, 0xa5, 0x02, 0xcf, 0x8a, 0x0e,
	0x0c, 0x24, 0x52, 0x6c, 0xbe, 0x9d, 0x3c, 0x7c, 0x89, 0xa6, 0xfa, 0x84, 0x0e, 0x35, 0x47, 0x82,
	0xaf, 0xd2, 0x6c, 0x4f, 0x8f, 0xc9, 0x0b, 0xfb, 0xad, 0xfd, 0x4c, 0x81, 0x3b, 0x01, 0xcb, 0x61,
	0x91, 0x4a, 0xc4, 0x63, 0xdf, 0x81, 0x92, 0xe9, 0xf4, 0xce, 0xdc, 0x31, 0x8b, 0xc5, 0xfc, 0x9d,
	0x97, 0x81, 0x30, 0x1a, 0xcf, 0xd8, 0xa4, 0xb8, 0xc8, 0xb2, 0x0b, 0x8a, 0x4c, 0xd3, 0x60, 0x73,
	0x36, 0x7f, 0xc4, 0xd3, 0x3a, 0x50, 0x0e, 0x70, 0xd8, 0x29, 0x5f, 0xf6, 0xed, 0xef, 0xa4, 0x4f,
	0x96, 0x5e, 0x8a, 0xb8, 0x0a, 0x15, 0x89, 0x14, 0xf1, 0xb4, 0xbb, 0xb0, 0xba, 0x63, 0xf9, 0x11,
	0xf2, 0x09, 0x2d, 0xd5, 0xce, 0xa0, 0x16, 0x45, 0x9a, 0x99, 0x7b, 0x06, 0xfc, 0x65, 0x16, 0xe7,
	0xef, 0x14, 0x36, 0xe4, 0x99, 0xb6, 0xdd, 0x31, 0xbb, 0x7f, 0xfb, 0x1a, 0xcf, 0x0e, 0xda, 0x4b,
	0xa8, 0xa7, 0xcf, 0x41, 0x3c, 0x49, 0xb4, 0xd9, 0x05, 0x59, 0xa7, 0x93, 0xe1, 0x11, 0x40, 0x54,
	0xbe, 0x60, 0x43, 0xfb, 0x53, 0x05, 0x56, 0x83, 0x81, 0xfc, 0xf2, 0xf0, 0xcd, 0xf7, 0x8f, 0xee,
	0x8d, 0xb8, 0xa0, 0xab, 0xe8, 0xf4, 0xe7, 0x2f, 0x91, 0x60, 0xfd, 0x99, 0x02, 0x6f, 0x45, 0x4a,
	0x6a, 0x7b, 0xee, 0xb8, 0x2f, 0xa4, 0x81, 0x6a, 0xb8, 0xe8, 0x79, 0x85, 0x9e, 0x1c, 0x42, 0x53,
	0x12, 0x27, 0x87, 0xc0, 0x92, 0x16, 0x0c, 0xdc, 0x13, 0xb8, 0x96, 0x60, 0x2f, 0x78, 0x7f, 0x9f,
	0x9c, 0xd2, 0xd3, 0x6f, 0x70, 0x45, 0x51, 0xd1, 0x81, 0x81, 0xd0, 0x93, 0xfc, 0x3f, 0x58, 0xb6,
	0x7d, 0x6b, 0xc4, 0x0a, 0x27, 0x2f, 0x2d, 0x61, 0x36, 0x46, 0xfb, 0x4d, 0x05, 0xb4, 0x79, 0x62,
	0x21, 0x1e, 0x4d, 0xb0, 0xa4, 0x5b, 0x2f, 0x6d, 0xda, 0xe9, 0x31, 0x64, 0x5b, 0x47, 0x7c, 0x55,
	0x83, 0xca, 0x99, 0x49, 0x8c, 0xf8, 0x65, 0x6b, 0xe9, 0xcc, 0x24, 0xfb, 0xe2, 0xbe, 0xf5, 0xaf,
	0x14, 0xb8, 0x26, 0xab, 0x68, 0xf8, 0x30, 0xb0, 0xd0, 0x6e, 0xfc, 0xea, 0x73, 0xab, 0x5f, 0x87,
	0xf5, 0x34, 0x3e, 0x31, 0x0f, 0x58, 0x61, 0xf7, 0xd1, 0xa9, 0xd7, 0x82, 0xf1, 0x11, 0x1c, 0x37,
	0x25, 0xb5, 0xfa, 0x1b, 0x05, 0xae, 0x05, 0xd8, 0x11, 0xb7, 0xbd, 0x90, 0x28, 0xde, 0xd8, 0x4a,
	0x52, 0xe3, 0x67, 0x2e, 0x3d, 0x23, 0xaa, 0xc3, 0x7a, 0x1a, 0xab, 0x58, 0x85, 0x14, 0xd9, 0x4f,
	0x1e, 0x08, 0x52, 0x7d, 0xed, 0x37, 0xa3, 0x22, 0x15, 0xa8, 0xc4, 0xa3, 0x5b, 0xc3, 0xa3, 0x0c,
	0x45, 0x5f, 0x16, 0x27, 0x1a, 0xad, 0x03, 0x8d, 0x96, 0x35, 0x4c, 0x53, 0xeb, 0x85, 0xef, 0x1b,
	0x6e, 0xc1, 0x8d, 0xa9, 0xa4, 0x88, 0xa7, 0xfd, 0x1f, 0x0c, 0x16, 0x41, 0x25, 0xe9, 0xec, 0x7a,
	0xa7, 0x3f, 0x57, 0x62, 0xd8, 0xc4, 0xc3, 0xa2, 0x8e, 0xc9, 0xd0, 0xb7, 0xe5, 0x3a, 0x55, 0x36,
	0xac, 0x8a, 0xf0, 0xb0, 0x48, 0xf5, 0x1e, 0xd4, 0x58, 0x60, 0x4d, 0x14, 0x25, 0x56, 0x11, 0x1e,
	0x62, 0xde, 0x80, 0x22, 0xa3, 0x69, 0x7a, 0x1e, 0x77, 0x39, 0x05, 0x04, 0x34, 0x3d, 0xbc, 0xcb,
	0x64, 0x64, 0x68, 0x27, 0xdb, 0xb6, 0x02, 0x02, 0x9a, 0x9e, 0xa7, 0xfd, 0x08, 0x93, 0xc6, 0xe0,
	0x8e, 0xed, 0xb3, 0x47, 0xbc, 0xea, 0x4b, 0x2e, 0xee, 0x51, 0xe6, 0x14, 0xf7, 0xc4, 0x0b, 0x25,
	0x53, 0xca, 0x74, 0xb2, 0x29, 0x65, 0x3a, 0x5a, 0x0f, 0x36, 0x9a, 0xa3, 0x09, 0xb1, 0x76, 0x4d,
	0xc2, 0xcb, 0x14, 0xf0, 0x2d, 0x9a, 0x5d, 0xae, 0xa7, 0xd6, 0xd0, 0x28, 0x0b, 0xd7, 0xd0, 0x68,
	0x9f, 0xc1, 0x15, 0x9c, 0x24, 0x36, 0x41, 0xb2, 0x00, 0x4a, 0x59, 0xa8, 0x00, 0x4a, 0xfb, 0xa3,
	0x2c, 0x54, 0x25, 0xc2, 0x97, 0xac, 0x17, 0x7b, 0x1f, 0x6a, 0x17, 0x16, 0xf1, 0xad, 0x71, 0xe2,
	0x51, 0x75, 0x35, 0x80, 0xff, 0x4a, 0x2b, 0xc6, 0xa4, 0xba, 0xab, 0xe5, 0xe9, 0x75, 0x57, 0x2b,
	0xb1, 0xba, 0xab, 0x58, 0x41, 0x55, 0xfe, 0x32, 0x05, 0x55, 0x85, 0xc5, 0x0a, 0xaa, 0x8a, 0xd3,
	0x0b, 0xaa, 0xd2, 0xca, 0xa0, 0x20, 0xb5, 0x0c, 0xea, 0x39, 0xac, 0xe2, 0x9e, 0xf0, 0x0b, 0x21,
	0xf6, 0x75, 0xc7, 0x9b, 0x95, 0xc8, 0x69, 0x9f, 0xc3, 0x8d, 0x1d, 0xcb, 0x6f, 0x0e, 0x87, 0xcf,
	0x4c, 0xa7, 0x3f, 0xb4, 0x0e, 0xc7, 0x16, 0xb1, 0x1c, 0x7f, 0xcb, 0x9d, 0xb0, 0x03, 0x64, 0xf4,
	0xb1, 0x4c, 0x99, 0xf5, 0x58, 0x96, 0x89, 0x3e, 0x96, 0x7d, 0x04, 0x37, 0xa7, 0x13, 0x66, 0xd6,
	0xc7, 0x9e, 0xf2, 0xd8, 0x53, 0x18, 0x0f, 0xed, 0x08, 0x42, 0x24, 0xed, 0x4b, 0x7c, 0xd5, 0x8f,
	0x13, 0x38, 0x18, 0xf7, 0xad, 0xb1, 0x38, 0xf6, 0xbd, 0x39, 0x77, 0xff, 0x17, 0x36, 0x67, 0x13,
	0x67, 0x5e, 0x18, 0xeb, 0x56, 0xd9, 0x41, 0xa1, 0xa8, 0xf3, 0x96, 0xf6, 0x97, 0x59, 0x00, 0xc4,
	0x64, 0x1f, 0x79, 0x24, 0xdd, 0xee, 0x2d, 0x00, 0xdf, 0x1c, 0x0f, 0x2c, 0x5f, 0x2a, 0xc8, 0x2d,
	0x32, 0x08, 0xbf, 0x23, 0x09, 0xea, 0x63, 0xb3, 0xd1, 0xfa, 0xd8, 0xe8, 0x9b, 0x57, 0x2e, 0xfe,
	0xe6, 0xf5, 0x16, 0x94, 0x45, 0xb7, 0xfc, 0xf9, 0x0a, 0x87, 0x25, 0x1e, 0xf7, 0x56, 0x12, 0xb7,
	0x10, 0x24, 0x90, 0x47, 0x9e, 0x9f, 0xd9, 0x44, 0x11, 0xc0, 0x06, 0xe4, 0x69, 0x52, 0x14, 0xbe,
	0xd7, 0xaf, 0xd0, 0x26, 0x63, 0x09, 0x3b, 0xd8, 0x36, 0xb1, 0xb3, 0x5c, 0x91, 0x42, 0x70, 0x97,
	0x68, 0xb6, 0xe0, 0x8d, 0xed, 0x9e, 0xf8, 0x08, 0x81, 0x35, 0xe8, 0x20, 0xfc, 0xc1, 0xd8, 0x64,
	0x5f, 0xe9, 0x15, 0x11, 0x22, 0xaa, 0xe5, 0xfa, 0x96, 0x39, 0x34, 0x7c, 0xf7, 0xa5, 0xe5, 0xf0,
	0x0f, 0x0c, 0x8a, 0x14, 0xd2, 0xa5, 0x00, 0xf5, 0x36, 0x94, 0x6c, 0x62, 0x4c, 0x5e, 0xbe, 0x32,
	0x3c, 0xd3, 0xee, 0x63, 0xcd, 0x62, 0x41, 0x2f, 0xda, 0xe4, 0xf8, 0xe5, 0xab, 0x43, 0xd3, 0xee,
	0xd3, 0xc4, 0x8a, 0xf7, 0x33, 0xa1, 0x62, 0x99, 0x62, 0x41, 0x2f, 0x21, 0x46, 0x17, 0x41, 0xda,
	0x2e, 0x94, 0x9a, 0xfd, 0x3e, 0x6e, 0x13, 0xfb, 0x0a, 0xa9, 0x6a, 0xf6, 0xfb, 0x06, 0x97, 0x7b,
	0x98, 0x92, 0x47, 0x8f, 0x96, 0xe1, 0xae, 0xea, 0x65, 0x93, 0x8f, 0x46, 0x37, 0xb8, 0x09, 0xe5,
	0x90, 0x1a, 0xf1, 0xe8, 0x9e, 0x8f, 0x83, 0x33, 0x08, 0xfd, 0xa9, 0x1d, 0xe3, 0xbd, 0x59, 0x10,
	0xab, 0x2e, 0x51, 0xd4, 0x7f, 0x17, 0x2a, 0xc4, 0x31, 0x3d, 0x72, 0xe6, 0xfa, 0xb2, 0x8a, 0x96,
	0x05, 0x10, 0xf5, 0x74, 0x8c, 0x57, 0x5a, 0x31, 0xb2, 0xc4, 0x53, 0x6f, 0xf2, 0x82, 0x18, 0x29,
	0xae, 0x86, 0x80, 0x4b, 0x7c, 0x2f, 0xc0, 0x0f, 0x75, 0xd6, 0x6b, 0x1a, 0x6f, 0xb2, 0xe2, 0x50,
	0xd7, 0xa6, 0x4d, 0xed, 0x31, 0xe6, 0x25, 0xc1, 0x9c, 0xcf, 0x6c, 0xe2, 0xbb, 0x73, 0x9f, 0xff,
	0xff, 0x35, 0x83, 0xe7, 0xb9, 0xe4, 0x28, 0xe2, 0xa9, 0x5f, 0x88, 0xcb, 0x43, 0xca, 0x8c, 0x1c,
	0xe2, 0x1e, 0x45, 0x9f, 0xc0, 0xd2, 0x87, 0x3f, 0x08, 0x57, 0xef, 0x5b, 0x23, 0x7e, 0xe1, 0x48,
	0x9b, 0x58, 0x3c, 0xfa, 0x01, 0xa8, 0x11, 0x29, 0x1a, 0x13, 0xc7, 0x7e, 0xcd, 0x45, 0x59, 0x93,
	0x45, 0x79, 0xec, 0xd8, 0xaf, 0x1b, 0x3f, 0x55, 0xa0, 0x12, 0x21, 0xf7, 0xcb, 0x0b, 0xf2, 0x5d,
	0x58, 0x0d, 0x7d, 0x10, 0x9b, 0x9d, 0xc5, 0xaf, 0x4a, 0xe0, 0x88, 0xe8, 0xd4, 0x54, 0x69, 0x85,
	0xf1, 0x31, 0x2c, 0x5e, 0x25, 0xc4, 0x0d, 0x90, 0xe2, 0x68, 0x7f, 0x9c, 0x81, 0xeb, 0xd4, 0x2d,
	0x49, 0xce, 0x9d, 0x84, 0x15, 0x7b, 0x33, 0xbf, 0x93, 0x58, 0xec, 0x51, 0xed, 0x21, 0xe4, 0x26,
	0x0e, 0xaf, 0x3c, 0xab, 0xc6, 0xca, 0x2b, 0x29, 0x37, 0xdb, 0xf6, 0xd0, 0xb7, 0xc6, 0xc7, 0x8e,
	0xed, 0xeb, 0x88, 0xa8, 0x36, 0xa1, 0x42, 0xb0, 0xaa, 0x43, 0x5c, 0x7f, 0x2c, 0xa7, 0x5c, 0x7f,
	0xe8, 0xd6, 0x39, 0x2b, 0xfd, 0xe0, 0xa9, 0x6c, 0x99, 0x48, 0xad, 0x98, 0xb3, 0xe6, 0x6f, 0xdd,
	0xe9, 0xce, 0x9a, 0x97, 0x13, 0x09, 0x67, 0xfd, 0x19, 0x34, 0xa6, 0x49, 0x85, 0x78, 0xea, 0x13,
	0x58, 0xa6, 0xf1, 0x8c, 0xa4, 0x9e, 0xcf, 0x52, 0x6b, 0x7a, 0x74, 0x36, 0x40, 0xfb, 0x09, 0x4f,
	0x9a, 0x38, 0x51, 0x11, 0x55, 0x16, 0xa8, 0x49, 0xc8, 0x46, 0xfd, 0xf3, 0x62, 0x95, 0x7e, 0xff,
	0xa0, 0x40, 0x4d, 0x9e, 0x1f, 0xf5, 0x31, 0x3a, 0x83, 0x92, 0x52, 0xf5, 0x20, 0x8a, 0xa1, 0x72,
	0x41, 0xed, 0xbf, 0x94, 0x60, 0x04, 0x85, 0x31, 0x39, 0xbd, 0x42, 0x82, 0xdc, 0x62, 0x3b, 0x89,
	0x47, 0x73, 0x97, 0x1c, 0xcb, 0x5d, 0x42, 0x3c, 0x9a, 0xbb, 0xbc, 0x0d, 0x55, 0x29, 0x77, 0xa1,
	0xe4, 0x96, 0x91, 0x5c, 0x39, 0xc8, 0x59, 0xb6, 0x2d, 0x0b, 0x3f, 0x96, 0x4a, 0x88, 0x8e, 0x78,
	0xea, 0xc7, 0x61, 0x84, 0x92, 0xec, 0x3c, 0x5a, 0xa0, 0x19, 0x5f, 0x73, 0x10, 0xc0, 0xd0, 0xa0,
	0x53, 0x2f, 0x57, 0xee, 0x8f, 0xa0, 0xba, 0xe3, 0x8a, 0xba, 0x26, 0xb4, 0xbb, 0x55, 0x28, 0x1d,
	0xef, 0x7f, 0xb2, 0x7f, 0xf0, 0xb9, 0xd1, 0x3d, 0x39, 0x6c, 0xd7, 0x96, 0x28, 0x60, 0xa7, 0xb9,
	0xd7, 0x36, 0x3a, 0xfb, 0x5b, 0x07, 0x7b, 0xed, 0x9a, 0xa2, 0x5e, 0x81, 0x55, 0x04, 0x1c, 0xb5,
	0xbb, 0xdd, 0xdd, 0xf6, 0x5e, 0x7b, 0xbf, 0x5b, 0xcb, 0x51, 0x2c, 0xfd, 0xe0, 0x60, 0x4f, 0x60,
	0xad, 0x50, 0x2c, 0x04, 0x48, 0x58, 0xf9, 0xfb, 0x06, 0xac, 0xc6, 0x94, 0x59, 0x5d, 0x07, 0x35,
	0x06, 0x6a, 0xee, 0xee, 0xd6, 0x96, 0xd4, 0x0d, 0xb8, 0x12, 0x83, 0x7f, 0x6e, 0xda, 0x7e, 0x4d,
	0x51, 0x6f, 0xc0, 0x46, 0xac, 0x63, 0xdb, 0x76, 0x6c, 0x72, 0x66, 0xf5, 0x6b, 0x99, 0xfb, 0x5f,
	0x44, 0xbe, 0xd1, 0xe5, 0xdf, 0xbd, 0xaa, 0x57, 0xa1, 0xd6, 0x3c, 0x3c, 0xdc, 0xed, 0x6c, 0x35,
	0xbb, 0x9d, 0x83, 0x7d, 0xa3, 0xb3, 0xdf, 0xe9, 0xd6, 0x96, 0xe8, 0xcc, 0x32, 0xb4, 0xb9, 0xb5,
	0xd5, 0x3e, 0xec, 0xd6, 0x94, 0x38, 0x5c, 0x6f, 0x3f, 0x6f, 0x6f, 0x75, 0x6b, 0x99, 0xfb, 0xdf,
	0x81, 0x6a, 0xd4, 0x86, 0x55, 0x80, 0x95, 0xa7, 0x27, 0x46, 0xab, 0x79, 0x52, 0x5b, 0x52, 0x4b,
	0x90, 0x7f, 0x7a, 0x62, 0x7c, 0xde, 0x6e, 0x7f, 0x52, 0x53, 0xd4, 0x32, 0x14, 0x9e, 0x9e, 0x18,
	0x7b, 0x07, 0xfb, 0xdd, 0x67, 0xb5, 0xcc, 0xfd, 0xc7, 0xdc, 0x39, 0x8a, 0x32, 0x04, 0xb5, 0x06,
	0xe5, 0x9d, 0xe3, 0xce, 0x6e, 0xcb, 0xe8, 0xec, 0x1f, 0xb5, 0x75, 0xca, 0x4b, 0x00, 0x69, 0xb5,
	0x77, 0xdb, 0xdd, 0x76, 0x4d, 0xb9, 0xff, 0x4f, 0x0a, 0x80, 0xb4, 0x2d, 0x55, 0x80, 0xfd, 0x83,
	0x7d, 0xba, 0x0b, 0xad, 0xf6, 0xf7, 0x6b, 0x4b, 0x6a, 0x1d, 0xae, 0xe2, 0x26, 0xe8, 0xed, 0xad,
	0x67, 0x4d, 0x7d, 0x87, 0xfe, 0x78, 0xda, 0xa4, 0x03, 0x55, 0x15, 0xaa, 0x8c, 0xd4, 0xd1, 0xc9,
	0x91, 0xd1, 0x6d, 0x1e, 0x7d, 0x52, 0xcb, 0x84, 0xb0, 0x83, 0xc3, 0x36, 0x83, 0x65, 0x29, 0xc5,
	0xc8, 0x0e, 0xde, 0x84, 0x3a, 0xc3, 0xf9, 0xbc, 0xbd, 0xbb, 0xdd, 0xd4, 0xdb, 0xc6, 0x76, 0x47,
	0x3f, 0xea, 0x1a, 0xcf, 0x0f, 0x3a, 0xfb, 0xb5, 0x65, 0xf5, 0x1a, 0xac, 0x6d, 0x3d, 0x6b, 0xee,
	0xef, 0xb7, 0x77, 0x8d, 0xa3, 0xf6, 0x7e, 0xcb, 0xd8, 0xe9, 0x6c, 0x77, 0x6b, 0x2b, 0x54, 0x56,
	0x21, 0x58, 0xda, 0xe8, 0x0f, 0xa1, 0x18, 0x54, 0x49, 0xd1, 0xd9, 0x5b, 0xcd, 0x13, 0x43, 0x6f,
	0xee, 0xef, 0xb4, 0x85, 0x56, 0x5d, 0x85, 0x1a, 0x8a, 0x47, 0x86, 0x2a, 0xf7, 0x3f, 0x82, 0x62,
	0x50, 0x13, 0xa9, 0x56, 0xa0, 0x88, 0x4b, 0xe6, 0x23, 0x6a, 0x50, 0x66, 0x1a, 0xd6, 0xe2, 0xd8,
	0xea, 0x1a, 0x54, 0x0e, 0x9b, 0x9d, 0x96, 0x71, 0x2c, 0x40, 0x99, 0xfb, 0xef, 0x43, 0xb1, 0x75,
	0x2a, 0xb6, 0xbd, 0x42, 0x1b, 0xa1, 0x8c, 0x59, 0x33, 0x10, 0xf0, 0x0f, 0xd8, 0x8b, 0x09, 0x7f,
	0xe5, 0xa6, 0x0a, 0xbc, 0x7d, 0xa0, 0x1b, 0xad, 0xf6, 0x76, 0xf3, 0x78, 0xb7, 0xcb, 0x14, 0x90,
	0x02, 0xa8, 0x2a, 0x9c, 0x18, 0x87, 0x3b, 0x5b, 0x4c, 0x1e, 0x4c, 0xc0, 0xb4, 0x63, 0xef, 0xa0,
	0xd5, 0xd9, 0x3e, 0x31, 0xba, 0xcd, 0x9d, 0x5a, 0x46, 0x8c, 0x6e, 0xb6, 0x5a, 0x14, 0xb5, 0x96,
	0xbd, 0xff, 0x31, 0x40, 0x78, 0xf9, 0x40, 0x79, 0x6f, 0x7f, 0xbf, 0xb9, 0x27, 0x51, 0xaf, 0x40,
	0x11, 0x21, 0x87, 0xcd, 0xa3, 0x23, 0xb6, 0x14, 0x6c, 0xee, 0x1f, 0x74, 0x19, 0x28, 0x73, 0xff,
	0x4b, 0x58, 0x4b, 0x5c, 0x31, 0xd1, 0x6d, 0x38, 0x38, 0x6c, 0xeb, 0x4c, 0x33, 0x43, 0x6a, 0xd7,
	0xe1, 0x5a, 0x08, 0x96, 0x94, 0xb7, 0xa6, 0x50, 0x41, 0x87, 0x5d, 0x7a, 0x7b, 0xeb, 0x40, 0x6f,
	0xd5, 0x32, 0xf7, 0x3f, 0x0e, 0x9e, 0x8b, 0x84, 0xd1, 0x77, 0x9b, 0x3b, 0x12, 0xc1, 0x35, 0xa8,
	0x50, 0x00, 0xc5, 0xdf, 0xdb, 0x6b, 0xef, 0xb7, 0x6a, 0x0a, 0xe5, 0x98, 0x82, 0x4e, 0x8e, 0x4f,
	0x3a, 0xfb, 0xb5, 0xcc, 0xfd, 0x96, 0x74, 0x2b, 0xcb, 0x57, 0x59, 0x05, 0x38, 0x69, 0x4b, 0x44,
	0x6a, 0x50, 0x3e, 0x69, 0x1b, 0x94, 0x42, 0xa7, 0xdb, 0x6d, 0x53, 0x1a, 0xab, 0x50, 0x3a, 0x69,
	0x73, 0x8b, 0x6a, 0xb7, 0x6a, 0x99, 0x47, 0x3f, 0x7f, 0x0f, 0x4a, 0xd4, 0x01, 0xf1, 0xef, 0xcd,
	0xd4, 0x2f, 0xc3, 0x74, 0x4f, 0xfe, 0x24, 0x67, 0x73, 0xe6, 0x57, 0xf5, 0xba, 0x75, 0xde, 0x98,
	0x83, 0x41, 0x3c, 0x6d, 0x49, 0xfd, 0x3e, 0xbe, 0x4f, 0xc5, 0xbe, 0xe1, 0x57, 0xef, 0x4c, 0x2f,
	0x5b, 0xc1, 0xb8, 0xd5, 0x98, 0x8d, 0x80, 0x94, 0x75, 0xa8, 0x1e, 0x78, 0x96, 0xcc, 0xf1, 0xed,
	0x19, 0xdf, 0xe2, 0x53, 0xa2, 0x33, 0xfb, 0x91, 0xe6, 0x97, 0xb0, 0xc6, 0x3e, 0x96, 0x95, 0xc9,
	0x4e, 0xad, 0xb1, 0x09, 0xbe, 0x0f, 0x6f, 0xcc, 0x43, 0x41, 0xe2, 0xdb, 0x50, 0x96, 0x2f, 0x6e,
	0xd4, 0xeb, 0xe9, 0x45, 0x53, 0x94, 0xde, 0xb4, 0xae, 0x38, 0x1d, 0x14, 0x66, 0x0a, 0xb2, 0x10,
	0xe3, 0xb4, 0x2e, 0xa4, 0xb3, 0x8b, 0x5f, 0xf8, 0x87, 0xfc, 0xdc, 0x4c, 0x9f, 0x94, 0x0b, 0x6f,
	0x46, 0x2f, 0x52, 0xdb, 0x81, 0x8a, 0xe0, 0x0a, 0xbf, 0x45, 0x8c, 0xb3, 0x25, 0x7d, 0xce, 0xd8,
	0x98, 0xd6, 0xc5, 0xf7, 0xb5, 0x16, 0x8a, 0x89, 0x97, 0x7d, 0xdd, 0x4a, 0x9b, 0x3c, 0xf8, 0x7c,
	0xb1, 0x31, 0xab, 0x1b, 0x69, 0x5a, 0x98, 0x8b, 0x26, 0xbf, 0x05, 0x42, 0xe2, 0x29, 0x59, 0x56,
	0xfc, 0x9b, 0xac, 0xc6, 0x5c, 0x1c, 0x9c, 0xc6, 0x90, 0xce, 0x0d, 0xe1, 0x15, 0x12, 0x4e, 0x92,
	0x62, 0x2b, 0xd1, 0x8f, 0x7a, 0x1a, 0x73, 0x30, 0x70, 0x82, 0x5f, 0x0b, 0x8f, 0x33, 0x98, 0xbe,
	0x84, 0x5f, 0xf7, 0xa5, 0x8c, 0x8e, 0x7e, 0x24, 0xd3, 0x98, 0x83, 0x81, 0xf4, 0x5f, 0xe2, 0x8d,
	0x6f, 0xfc, 0xeb, 0x87, 0x21, 0x51, 0xdf, 0xbb, 0xd4, 0x37, 0x12, 0xd6, 0x79, 0xe3, 0x72, 0x88,
	0x38, 0xd9, 0x8f, 0xf1, 0x56, 0x65, 0xea, 0xa7, 0x0e, 0xea, 0x07, 0xa9, 0x27, 0xaa, 0x29, 0x9f,
	0x55, 0x34, 0x16, 0xc0, 0xe6, 0x7b, 0x75, 0x2d, 0x54, 0x33, 0xb9, 0xa6, 0xfc, 0xad, 0x39, 0x25,
	0xe8, 0x09, 0x73, 0x4f, 0x2b, 0x70, 0xd7, 0x96, 0x54, 0x82, 0xb7, 0x51, 0xd3, 0xca, 0xc8, 0xd5,
	0x7b, 0xb3, 0x05, 0x25, 0xad, 0xec, 0x92, 0x98, 0x38, 0xe9, 0x21, 0xd4, 0xf0, 0x5b, 0xe2, 0x1d,
	0x73, 0x64, 0x09, 0xff, 0x1e, 0xb5, 0xdc, 0xd8, 0xa7, 0xd8, 0x8d, 0x19, 0xbd, 0xdc, 0x4b, 0x54,
	0x77, 0x2c, 0x5f, 0xaa, 0x88, 0x56, 0x6f, 0x4c, 0xab, 0x95, 0xa6, 0xe4, 0xa6, 0x77, 0x72, 0x05,
	0x56, 0x93, 0xe5, 0xbb, 0xa9, 0x16, 0x18, 0x2b, 0x13, 0x6e, 0xcc, 0xc5, 0x11, 0x41, 0x21, 0x5a,
	0x36, 0x1d, 0x0b, 0x0a, 0x89, 0xaa, 0xed, 0xc6, 0xcc, 0x7e, 0xa4, 0x69, 0xc2, 0x95, 0x94, 0x82,
	0x52, 0xf5, 0xee, 0xcc, 0xd2, 0x4b, 0xee, 0x35, 0xe7, 0x23, 0xe1, 0x14, 0x83, 0xc8, 0xa3, 0x61,
	0x58, 0xf5, 0xa9, 0xbe, 0x33, 0x73, 0xbc, 0xa8, 0x50, 0x6d, 0x5c, 0x06, 0x4d, 0xf8, 0x7c, 0xf9,
	0x6a, 0x22, 0xee, 0xf3, 0xa3, 0xcf, 0x24, 0x8d, 0x19, 0xbd, 0x48, 0xed, 0x53, 0xd4, 0x0d, 0xe9,
	0x29, 0x62, 0x9e, 0xa3, 0xbe, 0x9d, 0x4a, 0x30, 0x78, 0xc6, 0xd0, 0x96, 0xd4, 0x1f, 0xf2, 0xc3,
	0x58, 0xec, 0x85, 0x61, 0x1e, 0xe1, 0xb7, 0x93, 0xa7, 0xb2, 0xe4, 0x1b, 0x85, 0xb6, 0xa4, 0x1e,
	0xf3, 0x63, 0xea, 0x02, 0xa4, 0x37, 0x93, 0xa4, 0x13, 0x64, 0x3f, 0x81, 0x92, 0xd4, 0x31, 0x8f,
	0xe2, 0x8d, 0x69, 0x14, 0x19, 0xb1, 0x3d, 0x28, 0xcb, 0xc7, 0xca, 0x79, 0xd4, 0x6e, 0x4e, 0x3d,
	0x90, 0x32, 0x72, 0x27, 0xd1, 0x93, 0x39, 0xa6, 0x0c, 0x9b, 0x53, 0xc7, 0x88, 0xcc, 0xe1, 0xad,
	0x39, 0x18, 0x3c, 0x5c, 0xac, 0xa7, 0x5f, 0x66, 0xa8, 0xef, 0xc6, 0x37, 0x3a, 0xfd, 0x1e, 0x28,
	0x1e, 0x2e, 0xa6, 0xde, 0x8c, 0x68, 0x4b, 0xaa, 0x43, 0xcf, 0xa0, 0xa9, 0x55, 0xc5, 0xb1, 0xe8,
	0x34, 0xbd, 0x8c, 0x39, 0xe6, 0x4a, 0x67, 0x14, 0x29, 0xb3, 0xc5, 0xa5, 0x57, 0x2f, 0xc6, 0x16,
	0x37, 0xb5, 0xa8, 0xb2, 0xf1, 0xde, 0xa5, 0xf0, 0xb8, 0xdf, 0xae, 0x44, 0xfa, 0x63, 0x9b, 0x1e,
	0xaf, 0x61, 0x8c, 0x7b, 0xad, 0x44, 0xed, 0x23, 0x6e, 0x7b, 0xbc, 0x80, 0x30, 0x9e, 0x24, 0x24,
	0x6b, 0x23, 0xe3, 0x91, 0x2d, 0xa5, 0x02, 0x11, 0xb3, 0xa9, 0xab, 0x69, 0xa5, 0x7b, 0x6a, 0xec,
	0x8b, 0xde, 0xf4, 0xda, 0xc2, 0x98, 0xaf, 0x9a, 0x5a, 0x03, 0xb8, 0xa4, 0x1e, 0xa1, 0x77, 0x91,
	0xeb, 0xc1, 0x13, 0xee, 0x23, 0x5a, 0x22, 0x18, 0x3b, 0x35, 0x24, 0xeb, 0xfc, 0xb4, 0x25, 0xb5,
	0x05, 0xc5, 0xe0, 0x08, 0x15, 0x4b, 0x51, 0xe5, 0x72, 0xa2, 0x46, 0x63, 0x5a, 0x97, 0x30, 0x51,
	0xf9, 0xb9, 0x3b, 0xe9, 0x46, 0x23, 0xb4, 0x6e, 0xcd, 0xe8, 0xe5, 0xb9, 0x88, 0x9a, 0x2c, 0x48,
	0x88, 0x47, 0xc5, 0xb4, 0xca, 0x8a, 0x58, 0x7c, 0x49, 0xaf, 0x6a, 0x60, 0x13, 0x24, 0xdf, 0xf8,
	0x63, 0x13, 0xa4, 0xd6, 0x2b, 0xc4, 0x26, 0x98, 0x52, 0x28, 0xb0, 0xa4, 0xfe, 0x04, 0x6e, 0xce,
	0x2a, 0x08, 0x8b, 0xe5, 0x72, 0x73, 0x6a, 0xdb, 0x1a, 0xdf, 0x58, 0x00, 0x5b, 0x68, 0x64, 0x5a,
	0x71, 0x54, 0x4c, 0x23, 0xa7, 0xd4, 0x68, 0xc5, 0x34, 0x72, 0x5a, 0x95, 0x55, 0x44, 0x23, 0x79,
	0xb1, 0x65, 0xba, 0x46, 0x86, 0xc5, 0x9c, 0xe9, 0x1a, 0x29, 0x55, 0x6a, 0x26, 0x37, 0x9f, 0x9f,
	0xeb, 0xa7, 0x6f, 0x7e, 0x50, 0x86, 0x31, 0x63, 0xf3, 0xc3, 0xfa, 0x0b, 0xe6, 0x38, 0xa7, 0x94,
	0x47, 0xc4, 0x1c, 0xe7, 0xf4, 0x7a, 0x8c, 0x98, 0xe3, 0x9c, 0x55, 0x6d, 0xb1, 0xa4, 0xfe, 0xb6,
	0x02, 0xb7, 0x67, 0x97, 0x22, 0xa9, 0x0f, 0x66, 0x17, 0x1e, 0xc5, 0xcb, 0xb9, 0x1a, 0x0f, 0x17,
	0xc2, 0x47, 0x2e, 0xce, 0xb1, 0x5e, 0x2e, 0xf5, 0xcd, 0x36, 0x9e, 0x7b, 0x4f, 0x7f, 0x33, 0x6e,
	0xbc, 0x7f, 0x49, 0x4c, 0x61, 0x04, 0xb3, 0x1e, 0x62, 0x93, 0x07, 0x9a, 0x59, 0x0f, 0xc2, 0x31,
	0x23, 0x98, 0xf7, 0xc2, 0xab, 0x2d, 0xa9, 0x4d, 0x28, 0x88, 0x87, 0x3d, 0x35, 0x5a, 0xf1, 0x2b,
	0xbd, 0x1e, 0xc6, 0xce, 0xde, 0xf2, 0x4b, 0x60, 0x10, 0x34, 0x22, 0x4f, 0x74, 0xc9, 0xa0, 0x11,
	0x7f, 0x18, 0x4c, 0x06, 0x8d, 0xc4, 0x1b, 0x9f, 0xb6, 0xa4, 0x9e, 0x46, 0x1f, 0x15, 0xf9, 0xa3,
	0x98, 0x7a, 0x77, 0xfe, 0xb3, 0x59, 0x3c, 0xbb, 0x9b, 0xf2, 0xb6, 0xa6, 0x2d, 0xa9, 0xcf, 0x58,
	0x69, 0x8c, 0x3b, 0x64, 0x8b, 0x62, 0x9b, 0x7d, 0x1d, 0xc3, 0x23, 0xfe, 0x23, 0xee, 0x67, 0x8f,
	0xf0, 0x45, 0x08, 0x6f, 0x49, 0x29, 0xdd, 0xf5, 0x48, 0x97, 0xbc, 0x95, 0xcf, 0x99, 0x20, 0x04,
	0x25, 0x7e, 0xcf, 0x32, 0x95, 0x50, 0xb4, 0x8b, 0xbd, 0xb5, 0xf6, 0x85, 0xfd, 0x6d, 0x43, 0x59,
	0xb7, 0xbc, 0xa1, 0xd9, 0xb3, 0xd8, 0xde, 0xdc, 0x8c, 0x20, 0xcb, 0x5d, 0x49, 0x9e, 0xda, 0x23,
	0xcf, 0xe7, 0xab, 0x7b, 0xfa, 0xf0, 0x8b, 0x6f, 0x0c, 0xdc, 0xa1, 0xe9, 0x0c, 0x1e, 0x7c, 0xf8,
	0xc8, 0xf7, 0x1f, 0xf4, 0xdc, 0xd1, 0x43, 0xfc, 0x7b, 0xdf, 0x9e, 0x3b, 0x7c, 0x48, 0xac, 0xf1,
	0x57, 0x76, 0xcf, 0x22, 0xf2, 0x5f, 0x05, 0x9f, 0xae, 0x60, 0xf7, 0xe3, 0xff, 0x0e, 0x00, 0x00,
	0xff, 0xff, 0x94, 0x1b, 0x4a, 0x7e, 0x4c, 0x58, 0x00, 0x00,
}
