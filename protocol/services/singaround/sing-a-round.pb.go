// Code generated by protoc-gen-go. DO NOT EDIT.
// source: sing-a-round/sing-a-round.proto

package singaround // import "golang.52tt.com/protocol/services/singaround"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type SingingGameRoundStage int32

const (
	SingingGameRoundStage_SingingGameRoundStageUndefined       SingingGameRoundStage = 0
	SingingGameRoundStage_SingingGameRoundStageGrabMic         SingingGameRoundStage = 1
	SingingGameRoundStage_SingingGameRoundStageSinging         SingingGameRoundStage = 2
	SingingGameRoundStage_SingingGameRoundStageReqHelp1        SingingGameRoundStage = 3
	SingingGameRoundStage_SingingGameRoundStageReqHelpFailed1  SingingGameRoundStage = 4
	SingingGameRoundStage_SingingGameRoundStageComparing       SingingGameRoundStage = 5
	SingingGameRoundStage_SingingGameRoundStageResult          SingingGameRoundStage = 6
	SingingGameRoundStage_SingingGameRoundStageReqHelp2        SingingGameRoundStage = 7
	SingingGameRoundStage_SingingGameRoundStageReqHelpFailed2  SingingGameRoundStage = 8
	SingingGameRoundStage_SingingGameRoundStageHelperSinging   SingingGameRoundStage = 9
	SingingGameRoundStage_SingingGameRoundStageHelperComparing SingingGameRoundStage = 10
	SingingGameRoundStage_SingingGameRoundStageHelperResult    SingingGameRoundStage = 11
	SingingGameRoundStage_SingingGameRoundStagePartialResult   SingingGameRoundStage = 12
	SingingGameRoundStage_SingingGameRoundStageVote            SingingGameRoundStage = 13
	SingingGameRoundStage_SingingGameRoundStagePreload         SingingGameRoundStage = 14
)

var SingingGameRoundStage_name = map[int32]string{
	0:  "SingingGameRoundStageUndefined",
	1:  "SingingGameRoundStageGrabMic",
	2:  "SingingGameRoundStageSinging",
	3:  "SingingGameRoundStageReqHelp1",
	4:  "SingingGameRoundStageReqHelpFailed1",
	5:  "SingingGameRoundStageComparing",
	6:  "SingingGameRoundStageResult",
	7:  "SingingGameRoundStageReqHelp2",
	8:  "SingingGameRoundStageReqHelpFailed2",
	9:  "SingingGameRoundStageHelperSinging",
	10: "SingingGameRoundStageHelperComparing",
	11: "SingingGameRoundStageHelperResult",
	12: "SingingGameRoundStagePartialResult",
	13: "SingingGameRoundStageVote",
	14: "SingingGameRoundStagePreload",
}
var SingingGameRoundStage_value = map[string]int32{
	"SingingGameRoundStageUndefined":       0,
	"SingingGameRoundStageGrabMic":         1,
	"SingingGameRoundStageSinging":         2,
	"SingingGameRoundStageReqHelp1":        3,
	"SingingGameRoundStageReqHelpFailed1":  4,
	"SingingGameRoundStageComparing":       5,
	"SingingGameRoundStageResult":          6,
	"SingingGameRoundStageReqHelp2":        7,
	"SingingGameRoundStageReqHelpFailed2":  8,
	"SingingGameRoundStageHelperSinging":   9,
	"SingingGameRoundStageHelperComparing": 10,
	"SingingGameRoundStageHelperResult":    11,
	"SingingGameRoundStagePartialResult":   12,
	"SingingGameRoundStageVote":            13,
	"SingingGameRoundStagePreload":         14,
}

func (x SingingGameRoundStage) String() string {
	return proto.EnumName(SingingGameRoundStage_name, int32(x))
}
func (SingingGameRoundStage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{0}
}

type SingingGameUserType int32

const (
	SingingGameUserType_SingingGameUserTypeUndefined SingingGameUserType = 0
	SingingGameUserType_SingingGameUserTypeSinger    SingingGameUserType = 1
	SingingGameUserType_SingingGameUserTypeHelper    SingingGameUserType = 2
)

var SingingGameUserType_name = map[int32]string{
	0: "SingingGameUserTypeUndefined",
	1: "SingingGameUserTypeSinger",
	2: "SingingGameUserTypeHelper",
}
var SingingGameUserType_value = map[string]int32{
	"SingingGameUserTypeUndefined": 0,
	"SingingGameUserTypeSinger":    1,
	"SingingGameUserTypeHelper":    2,
}

func (x SingingGameUserType) String() string {
	return proto.EnumName(SingingGameUserType_name, int32(x))
}
func (SingingGameUserType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{1}
}

type RankListType int32

const (
	RankListType_RankListTypeUndefined RankListType = 0
	RankListType_RankListTypeFriend    RankListType = 1
	RankListType_RankListTypeAll       RankListType = 2
	RankListType_RankListTypeDay       RankListType = 3
)

var RankListType_name = map[int32]string{
	0: "RankListTypeUndefined",
	1: "RankListTypeFriend",
	2: "RankListTypeAll",
	3: "RankListTypeDay",
}
var RankListType_value = map[string]int32{
	"RankListTypeUndefined": 0,
	"RankListTypeFriend":    1,
	"RankListTypeAll":       2,
	"RankListTypeDay":       3,
}

func (x RankListType) String() string {
	return proto.EnumName(RankListType_name, int32(x))
}
func (RankListType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{2}
}

type SingingGameResultType int32

const (
	SingingGameResultType_SingingGameResultTypeUndefined   SingingGameResultType = 0
	SingingGameResultType_SingingGameResultTypeFailed      SingingGameResultType = 1
	SingingGameResultType_SingingGameResultTypeNormal      SingingGameResultType = 2
	SingingGameResultType_SingingGameResultTypeCooperative SingingGameResultType = 3
	SingingGameResultType_SingingGameResultTypePerfect     SingingGameResultType = 4
)

var SingingGameResultType_name = map[int32]string{
	0: "SingingGameResultTypeUndefined",
	1: "SingingGameResultTypeFailed",
	2: "SingingGameResultTypeNormal",
	3: "SingingGameResultTypeCooperative",
	4: "SingingGameResultTypePerfect",
}
var SingingGameResultType_value = map[string]int32{
	"SingingGameResultTypeUndefined":   0,
	"SingingGameResultTypeFailed":      1,
	"SingingGameResultTypeNormal":      2,
	"SingingGameResultTypeCooperative": 3,
	"SingingGameResultTypePerfect":     4,
}

func (x SingingGameResultType) String() string {
	return proto.EnumName(SingingGameResultType_name, int32(x))
}
func (SingingGameResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{3}
}

type SingingGameType int32

const (
	SingingGameType_SingingGameTypeUndefined    SingingGameType = 0
	SingingGameType_SingingGameTypeExpert       SingingGameType = 1
	SingingGameType_SingingGameTypeRookie       SingingGameType = 2
	SingingGameType_SingingGameTypePassThrough  SingingGameType = 999
	SingingGameType_SingingGameTypePassThrough2 SingingGameType = 1000
)

var SingingGameType_name = map[int32]string{
	0:    "SingingGameTypeUndefined",
	1:    "SingingGameTypeExpert",
	2:    "SingingGameTypeRookie",
	999:  "SingingGameTypePassThrough",
	1000: "SingingGameTypePassThrough2",
}
var SingingGameType_value = map[string]int32{
	"SingingGameTypeUndefined":    0,
	"SingingGameTypeExpert":       1,
	"SingingGameTypeRookie":       2,
	"SingingGameTypePassThrough":  999,
	"SingingGameTypePassThrough2": 1000,
}

func (x SingingGameType) String() string {
	return proto.EnumName(SingingGameType_name, int32(x))
}
func (SingingGameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{4}
}

// 形象信息
type UserImageType int32

const (
	UserImageType_NormalImage  UserImageType = 0
	UserImageType_DefaultImage UserImageType = 1
	UserImageType_LockImage    UserImageType = 2
)

var UserImageType_name = map[int32]string{
	0: "NormalImage",
	1: "DefaultImage",
	2: "LockImage",
}
var UserImageType_value = map[string]int32{
	"NormalImage":  0,
	"DefaultImage": 1,
	"LockImage":    2,
}

func (x UserImageType) String() string {
	return proto.EnumName(UserImageType_name, int32(x))
}
func (UserImageType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{5}
}

type AwardType int32

const (
	AwardType_AwardType_Undefined  AwardType = 0
	AwardType_AwardType_Image      AwardType = 1
	AwardType_AwardType_Decoration AwardType = 2
	AwardType_AwardType_Precentor  AwardType = 3
)

var AwardType_name = map[int32]string{
	0: "AwardType_Undefined",
	1: "AwardType_Image",
	2: "AwardType_Decoration",
	3: "AwardType_Precentor",
}
var AwardType_value = map[string]int32{
	"AwardType_Undefined":  0,
	"AwardType_Image":      1,
	"AwardType_Decoration": 2,
	"AwardType_Precentor":  3,
}

func (x AwardType) String() string {
	return proto.EnumName(AwardType_name, int32(x))
}
func (AwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{6}
}

// 专区配置
type SingingZoneType int32

const (
	SingingZoneType_SingingZoneTypeUndefined   SingingZoneType = 0
	SingingZoneType_SingingZoneTypeNormal      SingingZoneType = 1
	SingingZoneType_SingingZoneTypeFestival    SingingZoneType = 2
	SingingZoneType_SingingZoneTypePassThrough SingingZoneType = 3
)

var SingingZoneType_name = map[int32]string{
	0: "SingingZoneTypeUndefined",
	1: "SingingZoneTypeNormal",
	2: "SingingZoneTypeFestival",
	3: "SingingZoneTypePassThrough",
}
var SingingZoneType_value = map[string]int32{
	"SingingZoneTypeUndefined":   0,
	"SingingZoneTypeNormal":      1,
	"SingingZoneTypeFestival":    2,
	"SingingZoneTypePassThrough": 3,
}

func (x SingingZoneType) String() string {
	return proto.EnumName(SingingZoneType_name, int32(x))
}
func (SingingZoneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{7}
}

type GetSongListReq_FileExist int32

const (
	GetSongListReq_NoneSense        GetSongListReq_FileExist = 0
	GetSongListReq_OnlyNotExistFile GetSongListReq_FileExist = 1
	GetSongListReq_OnlyExistFile    GetSongListReq_FileExist = 2
)

var GetSongListReq_FileExist_name = map[int32]string{
	0: "NoneSense",
	1: "OnlyNotExistFile",
	2: "OnlyExistFile",
}
var GetSongListReq_FileExist_value = map[string]int32{
	"NoneSense":        0,
	"OnlyNotExistFile": 1,
	"OnlyExistFile":    2,
}

func (x GetSongListReq_FileExist) String() string {
	return proto.EnumName(GetSongListReq_FileExist_name, int32(x))
}
func (GetSongListReq_FileExist) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{84, 0}
}

type GetLessUserReq_GameType int32

const (
	GetLessUserReq_SingARound GetLessUserReq_GameType = 0
	GetLessUserReq_KTV        GetLessUserReq_GameType = 1
)

var GetLessUserReq_GameType_name = map[int32]string{
	0: "SingARound",
	1: "KTV",
}
var GetLessUserReq_GameType_value = map[string]int32{
	"SingARound": 0,
	"KTV":        1,
}

func (x GetLessUserReq_GameType) String() string {
	return proto.EnumName(GetLessUserReq_GameType_name, int32(x))
}
func (GetLessUserReq_GameType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{113, 0}
}

// 匹配
type StartSingingGameMatchingReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SingingGameType      SingingGameType `protobuf:"varint,2,opt,name=singing_game_type,json=singingGameType,proto3,enum=singaround.SingingGameType" json:"singing_game_type,omitempty"`
	NewSingingGameType   uint32          `protobuf:"varint,3,opt,name=new_singing_game_type,json=newSingingGameType,proto3" json:"new_singing_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *StartSingingGameMatchingReq) Reset()         { *m = StartSingingGameMatchingReq{} }
func (m *StartSingingGameMatchingReq) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameMatchingReq) ProtoMessage()    {}
func (*StartSingingGameMatchingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{0}
}
func (m *StartSingingGameMatchingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameMatchingReq.Unmarshal(m, b)
}
func (m *StartSingingGameMatchingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameMatchingReq.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameMatchingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameMatchingReq.Merge(dst, src)
}
func (m *StartSingingGameMatchingReq) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameMatchingReq.Size(m)
}
func (m *StartSingingGameMatchingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameMatchingReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameMatchingReq proto.InternalMessageInfo

func (m *StartSingingGameMatchingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartSingingGameMatchingReq) GetSingingGameType() SingingGameType {
	if m != nil {
		return m.SingingGameType
	}
	return SingingGameType_SingingGameTypeUndefined
}

func (m *StartSingingGameMatchingReq) GetNewSingingGameType() uint32 {
	if m != nil {
		return m.NewSingingGameType
	}
	return 0
}

type StartSingingGameMatchingResp struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartSingingGameMatchingResp) Reset()         { *m = StartSingingGameMatchingResp{} }
func (m *StartSingingGameMatchingResp) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameMatchingResp) ProtoMessage()    {}
func (*StartSingingGameMatchingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{1}
}
func (m *StartSingingGameMatchingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameMatchingResp.Unmarshal(m, b)
}
func (m *StartSingingGameMatchingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameMatchingResp.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameMatchingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameMatchingResp.Merge(dst, src)
}
func (m *StartSingingGameMatchingResp) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameMatchingResp.Size(m)
}
func (m *StartSingingGameMatchingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameMatchingResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameMatchingResp proto.InternalMessageInfo

func (m *StartSingingGameMatchingResp) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

// 加入游戏（观众加入游戏时使用）
type JoinSingingGameReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinSingingGameReq) Reset()         { *m = JoinSingingGameReq{} }
func (m *JoinSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*JoinSingingGameReq) ProtoMessage()    {}
func (*JoinSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{2}
}
func (m *JoinSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSingingGameReq.Unmarshal(m, b)
}
func (m *JoinSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *JoinSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSingingGameReq.Merge(dst, src)
}
func (m *JoinSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_JoinSingingGameReq.Size(m)
}
func (m *JoinSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSingingGameReq proto.InternalMessageInfo

func (m *JoinSingingGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *JoinSingingGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type JoinSingingGameResp struct {
	IsSuccess            bool     `protobuf:"varint,1,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	IsPlaying            bool     `protobuf:"varint,2,opt,name=is_playing,json=isPlaying,proto3" json:"is_playing,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *JoinSingingGameResp) Reset()         { *m = JoinSingingGameResp{} }
func (m *JoinSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*JoinSingingGameResp) ProtoMessage()    {}
func (*JoinSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{3}
}
func (m *JoinSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinSingingGameResp.Unmarshal(m, b)
}
func (m *JoinSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *JoinSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinSingingGameResp.Merge(dst, src)
}
func (m *JoinSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_JoinSingingGameResp.Size(m)
}
func (m *JoinSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_JoinSingingGameResp proto.InternalMessageInfo

func (m *JoinSingingGameResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

func (m *JoinSingingGameResp) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

func (m *JoinSingingGameResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 获取开始倒计时
type GetSingingGameCountdownReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameCountdownReq) Reset()         { *m = GetSingingGameCountdownReq{} }
func (m *GetSingingGameCountdownReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameCountdownReq) ProtoMessage()    {}
func (*GetSingingGameCountdownReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{4}
}
func (m *GetSingingGameCountdownReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameCountdownReq.Unmarshal(m, b)
}
func (m *GetSingingGameCountdownReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameCountdownReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameCountdownReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameCountdownReq.Merge(dst, src)
}
func (m *GetSingingGameCountdownReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameCountdownReq.Size(m)
}
func (m *GetSingingGameCountdownReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameCountdownReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameCountdownReq proto.InternalMessageInfo

func (m *GetSingingGameCountdownReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSingingGameCountdownReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSingingGameCountdownReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type GetSingingGameCountdownResp struct {
	Countdown            uint32   `protobuf:"varint,1,opt,name=countdown,proto3" json:"countdown,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameCountdownResp) Reset()         { *m = GetSingingGameCountdownResp{} }
func (m *GetSingingGameCountdownResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameCountdownResp) ProtoMessage()    {}
func (*GetSingingGameCountdownResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{5}
}
func (m *GetSingingGameCountdownResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameCountdownResp.Unmarshal(m, b)
}
func (m *GetSingingGameCountdownResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameCountdownResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameCountdownResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameCountdownResp.Merge(dst, src)
}
func (m *GetSingingGameCountdownResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameCountdownResp.Size(m)
}
func (m *GetSingingGameCountdownResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameCountdownResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameCountdownResp proto.InternalMessageInfo

func (m *GetSingingGameCountdownResp) GetCountdown() uint32 {
	if m != nil {
		return m.Countdown
	}
	return 0
}

// 准备（开始游戏）
type StartSingingGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartSingingGameReq) Reset()         { *m = StartSingingGameReq{} }
func (m *StartSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameReq) ProtoMessage()    {}
func (*StartSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{6}
}
func (m *StartSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameReq.Unmarshal(m, b)
}
func (m *StartSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameReq.Merge(dst, src)
}
func (m *StartSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameReq.Size(m)
}
func (m *StartSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameReq proto.InternalMessageInfo

func (m *StartSingingGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartSingingGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartSingingGameReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type StartSingingGameResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *StartSingingGameResp) Reset()         { *m = StartSingingGameResp{} }
func (m *StartSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*StartSingingGameResp) ProtoMessage()    {}
func (*StartSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{7}
}
func (m *StartSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartSingingGameResp.Unmarshal(m, b)
}
func (m *StartSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *StartSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartSingingGameResp.Merge(dst, src)
}
func (m *StartSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_StartSingingGameResp.Size(m)
}
func (m *StartSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartSingingGameResp proto.InternalMessageInfo

func (m *StartSingingGameResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 抢唱
type GrabSingingGameMicReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GrabSingingGameMicReq) Reset()         { *m = GrabSingingGameMicReq{} }
func (m *GrabSingingGameMicReq) String() string { return proto.CompactTextString(m) }
func (*GrabSingingGameMicReq) ProtoMessage()    {}
func (*GrabSingingGameMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{8}
}
func (m *GrabSingingGameMicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabSingingGameMicReq.Unmarshal(m, b)
}
func (m *GrabSingingGameMicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabSingingGameMicReq.Marshal(b, m, deterministic)
}
func (dst *GrabSingingGameMicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabSingingGameMicReq.Merge(dst, src)
}
func (m *GrabSingingGameMicReq) XXX_Size() int {
	return xxx_messageInfo_GrabSingingGameMicReq.Size(m)
}
func (m *GrabSingingGameMicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabSingingGameMicReq.DiscardUnknown(m)
}

var xxx_messageInfo_GrabSingingGameMicReq proto.InternalMessageInfo

func (m *GrabSingingGameMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GrabSingingGameMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GrabSingingGameMicReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *GrabSingingGameMicReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type GrabSingingGameMicResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	IsSuccess            bool                    `protobuf:"varint,2,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GrabSingingGameMicResp) Reset()         { *m = GrabSingingGameMicResp{} }
func (m *GrabSingingGameMicResp) String() string { return proto.CompactTextString(m) }
func (*GrabSingingGameMicResp) ProtoMessage()    {}
func (*GrabSingingGameMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{9}
}
func (m *GrabSingingGameMicResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GrabSingingGameMicResp.Unmarshal(m, b)
}
func (m *GrabSingingGameMicResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GrabSingingGameMicResp.Marshal(b, m, deterministic)
}
func (dst *GrabSingingGameMicResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GrabSingingGameMicResp.Merge(dst, src)
}
func (m *GrabSingingGameMicResp) XXX_Size() int {
	return xxx_messageInfo_GrabSingingGameMicResp.Size(m)
}
func (m *GrabSingingGameMicResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GrabSingingGameMicResp.DiscardUnknown(m)
}

var xxx_messageInfo_GrabSingingGameMicResp proto.InternalMessageInfo

func (m *GrabSingingGameMicResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *GrabSingingGameMicResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

// 请求帮唱
type AskForSingingHelpReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	AutoReq              bool     `protobuf:"varint,5,opt,name=auto_req,json=autoReq,proto3" json:"auto_req,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AskForSingingHelpReq) Reset()         { *m = AskForSingingHelpReq{} }
func (m *AskForSingingHelpReq) String() string { return proto.CompactTextString(m) }
func (*AskForSingingHelpReq) ProtoMessage()    {}
func (*AskForSingingHelpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{10}
}
func (m *AskForSingingHelpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AskForSingingHelpReq.Unmarshal(m, b)
}
func (m *AskForSingingHelpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AskForSingingHelpReq.Marshal(b, m, deterministic)
}
func (dst *AskForSingingHelpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskForSingingHelpReq.Merge(dst, src)
}
func (m *AskForSingingHelpReq) XXX_Size() int {
	return xxx_messageInfo_AskForSingingHelpReq.Size(m)
}
func (m *AskForSingingHelpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AskForSingingHelpReq.DiscardUnknown(m)
}

var xxx_messageInfo_AskForSingingHelpReq proto.InternalMessageInfo

func (m *AskForSingingHelpReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AskForSingingHelpReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AskForSingingHelpReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *AskForSingingHelpReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *AskForSingingHelpReq) GetAutoReq() bool {
	if m != nil {
		return m.AutoReq
	}
	return false
}

type AskForSingingHelpResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AskForSingingHelpResp) Reset()         { *m = AskForSingingHelpResp{} }
func (m *AskForSingingHelpResp) String() string { return proto.CompactTextString(m) }
func (*AskForSingingHelpResp) ProtoMessage()    {}
func (*AskForSingingHelpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{11}
}
func (m *AskForSingingHelpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AskForSingingHelpResp.Unmarshal(m, b)
}
func (m *AskForSingingHelpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AskForSingingHelpResp.Marshal(b, m, deterministic)
}
func (dst *AskForSingingHelpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AskForSingingHelpResp.Merge(dst, src)
}
func (m *AskForSingingHelpResp) XXX_Size() int {
	return xxx_messageInfo_AskForSingingHelpResp.Size(m)
}
func (m *AskForSingingHelpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AskForSingingHelpResp.DiscardUnknown(m)
}

var xxx_messageInfo_AskForSingingHelpResp proto.InternalMessageInfo

func (m *AskForSingingHelpResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 帮唱
type AnswerSingingHelpReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnswerSingingHelpReq) Reset()         { *m = AnswerSingingHelpReq{} }
func (m *AnswerSingingHelpReq) String() string { return proto.CompactTextString(m) }
func (*AnswerSingingHelpReq) ProtoMessage()    {}
func (*AnswerSingingHelpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{12}
}
func (m *AnswerSingingHelpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnswerSingingHelpReq.Unmarshal(m, b)
}
func (m *AnswerSingingHelpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnswerSingingHelpReq.Marshal(b, m, deterministic)
}
func (dst *AnswerSingingHelpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnswerSingingHelpReq.Merge(dst, src)
}
func (m *AnswerSingingHelpReq) XXX_Size() int {
	return xxx_messageInfo_AnswerSingingHelpReq.Size(m)
}
func (m *AnswerSingingHelpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AnswerSingingHelpReq.DiscardUnknown(m)
}

var xxx_messageInfo_AnswerSingingHelpReq proto.InternalMessageInfo

func (m *AnswerSingingHelpReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnswerSingingHelpReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AnswerSingingHelpReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *AnswerSingingHelpReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type AnswerSingingHelpResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	IsSuccess            bool                    `protobuf:"varint,2,opt,name=is_success,json=isSuccess,proto3" json:"is_success,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AnswerSingingHelpResp) Reset()         { *m = AnswerSingingHelpResp{} }
func (m *AnswerSingingHelpResp) String() string { return proto.CompactTextString(m) }
func (*AnswerSingingHelpResp) ProtoMessage()    {}
func (*AnswerSingingHelpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{13}
}
func (m *AnswerSingingHelpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnswerSingingHelpResp.Unmarshal(m, b)
}
func (m *AnswerSingingHelpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnswerSingingHelpResp.Marshal(b, m, deterministic)
}
func (dst *AnswerSingingHelpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnswerSingingHelpResp.Merge(dst, src)
}
func (m *AnswerSingingHelpResp) XXX_Size() int {
	return xxx_messageInfo_AnswerSingingHelpResp.Size(m)
}
func (m *AnswerSingingHelpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AnswerSingingHelpResp.DiscardUnknown(m)
}

var xxx_messageInfo_AnswerSingingHelpResp proto.InternalMessageInfo

func (m *AnswerSingingHelpResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *AnswerSingingHelpResp) GetIsSuccess() bool {
	if m != nil {
		return m.IsSuccess
	}
	return false
}

// 完成演唱
type AccomplishSingingGameSongReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccomplishSingingGameSongReq) Reset()         { *m = AccomplishSingingGameSongReq{} }
func (m *AccomplishSingingGameSongReq) String() string { return proto.CompactTextString(m) }
func (*AccomplishSingingGameSongReq) ProtoMessage()    {}
func (*AccomplishSingingGameSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{14}
}
func (m *AccomplishSingingGameSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishSingingGameSongReq.Unmarshal(m, b)
}
func (m *AccomplishSingingGameSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishSingingGameSongReq.Marshal(b, m, deterministic)
}
func (dst *AccomplishSingingGameSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishSingingGameSongReq.Merge(dst, src)
}
func (m *AccomplishSingingGameSongReq) XXX_Size() int {
	return xxx_messageInfo_AccomplishSingingGameSongReq.Size(m)
}
func (m *AccomplishSingingGameSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishSingingGameSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishSingingGameSongReq proto.InternalMessageInfo

func (m *AccomplishSingingGameSongReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AccomplishSingingGameSongReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AccomplishSingingGameSongReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *AccomplishSingingGameSongReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

type AccomplishSingingGameSongResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AccomplishSingingGameSongResp) Reset()         { *m = AccomplishSingingGameSongResp{} }
func (m *AccomplishSingingGameSongResp) String() string { return proto.CompactTextString(m) }
func (*AccomplishSingingGameSongResp) ProtoMessage()    {}
func (*AccomplishSingingGameSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{15}
}
func (m *AccomplishSingingGameSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishSingingGameSongResp.Unmarshal(m, b)
}
func (m *AccomplishSingingGameSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishSingingGameSongResp.Marshal(b, m, deterministic)
}
func (dst *AccomplishSingingGameSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishSingingGameSongResp.Merge(dst, src)
}
func (m *AccomplishSingingGameSongResp) XXX_Size() int {
	return xxx_messageInfo_AccomplishSingingGameSongResp.Size(m)
}
func (m *AccomplishSingingGameSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishSingingGameSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishSingingGameSongResp proto.InternalMessageInfo

func (m *AccomplishSingingGameSongResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 上报积分
type ReportSingingGameSongScoreReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Score                uint32   `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReportSingingGameSongScoreReq) Reset()         { *m = ReportSingingGameSongScoreReq{} }
func (m *ReportSingingGameSongScoreReq) String() string { return proto.CompactTextString(m) }
func (*ReportSingingGameSongScoreReq) ProtoMessage()    {}
func (*ReportSingingGameSongScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{16}
}
func (m *ReportSingingGameSongScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportSingingGameSongScoreReq.Unmarshal(m, b)
}
func (m *ReportSingingGameSongScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportSingingGameSongScoreReq.Marshal(b, m, deterministic)
}
func (dst *ReportSingingGameSongScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSingingGameSongScoreReq.Merge(dst, src)
}
func (m *ReportSingingGameSongScoreReq) XXX_Size() int {
	return xxx_messageInfo_ReportSingingGameSongScoreReq.Size(m)
}
func (m *ReportSingingGameSongScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSingingGameSongScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSingingGameSongScoreReq proto.InternalMessageInfo

func (m *ReportSingingGameSongScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReportSingingGameSongScoreReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ReportSingingGameSongScoreReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *ReportSingingGameSongScoreReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ReportSingingGameSongScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type ReportSingingGameSongScoreResp struct {
	SongResult           *SingingGameSongResult `protobuf:"bytes,1,opt,name=song_result,json=songResult,proto3" json:"song_result,omitempty"`
	IsReportAudio        bool                   `protobuf:"varint,2,opt,name=is_report_audio,json=isReportAudio,proto3" json:"is_report_audio,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *ReportSingingGameSongScoreResp) Reset()         { *m = ReportSingingGameSongScoreResp{} }
func (m *ReportSingingGameSongScoreResp) String() string { return proto.CompactTextString(m) }
func (*ReportSingingGameSongScoreResp) ProtoMessage()    {}
func (*ReportSingingGameSongScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{17}
}
func (m *ReportSingingGameSongScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReportSingingGameSongScoreResp.Unmarshal(m, b)
}
func (m *ReportSingingGameSongScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReportSingingGameSongScoreResp.Marshal(b, m, deterministic)
}
func (dst *ReportSingingGameSongScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReportSingingGameSongScoreResp.Merge(dst, src)
}
func (m *ReportSingingGameSongScoreResp) XXX_Size() int {
	return xxx_messageInfo_ReportSingingGameSongScoreResp.Size(m)
}
func (m *ReportSingingGameSongScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReportSingingGameSongScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReportSingingGameSongScoreResp proto.InternalMessageInfo

func (m *ReportSingingGameSongScoreResp) GetSongResult() *SingingGameSongResult {
	if m != nil {
		return m.SongResult
	}
	return nil
}

func (m *ReportSingingGameSongScoreResp) GetIsReportAudio() bool {
	if m != nil {
		return m.IsReportAudio
	}
	return false
}

type SingingGameSongResult struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	ScoreDetail          *SingingGameScoreDetail `protobuf:"bytes,2,opt,name=score_detail,json=scoreDetail,proto3" json:"score_detail,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameSongResult) Reset()         { *m = SingingGameSongResult{} }
func (m *SingingGameSongResult) String() string { return proto.CompactTextString(m) }
func (*SingingGameSongResult) ProtoMessage()    {}
func (*SingingGameSongResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{18}
}
func (m *SingingGameSongResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameSongResult.Unmarshal(m, b)
}
func (m *SingingGameSongResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameSongResult.Marshal(b, m, deterministic)
}
func (dst *SingingGameSongResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameSongResult.Merge(dst, src)
}
func (m *SingingGameSongResult) XXX_Size() int {
	return xxx_messageInfo_SingingGameSongResult.Size(m)
}
func (m *SingingGameSongResult) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameSongResult.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameSongResult proto.InternalMessageInfo

func (m *SingingGameSongResult) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGameSongResult) GetScoreDetail() *SingingGameScoreDetail {
	if m != nil {
		return m.ScoreDetail
	}
	return nil
}

type SingingGameScoreDetail struct {
	Success              bool     `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	HelperScore          uint32   `protobuf:"varint,2,opt,name=helper_score,json=helperScore,proto3" json:"helper_score,omitempty"`
	Scores               []string `protobuf:"bytes,3,rep,name=scores,proto3" json:"scores,omitempty"`
	Account              string   `protobuf:"bytes,4,opt,name=account,proto3" json:"account,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameScoreDetail) Reset()         { *m = SingingGameScoreDetail{} }
func (m *SingingGameScoreDetail) String() string { return proto.CompactTextString(m) }
func (*SingingGameScoreDetail) ProtoMessage()    {}
func (*SingingGameScoreDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{19}
}
func (m *SingingGameScoreDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameScoreDetail.Unmarshal(m, b)
}
func (m *SingingGameScoreDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameScoreDetail.Marshal(b, m, deterministic)
}
func (dst *SingingGameScoreDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameScoreDetail.Merge(dst, src)
}
func (m *SingingGameScoreDetail) XXX_Size() int {
	return xxx_messageInfo_SingingGameScoreDetail.Size(m)
}
func (m *SingingGameScoreDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameScoreDetail.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameScoreDetail proto.InternalMessageInfo

func (m *SingingGameScoreDetail) GetSuccess() bool {
	if m != nil {
		return m.Success
	}
	return false
}

func (m *SingingGameScoreDetail) GetHelperScore() uint32 {
	if m != nil {
		return m.HelperScore
	}
	return 0
}

func (m *SingingGameScoreDetail) GetScores() []string {
	if m != nil {
		return m.Scores
	}
	return nil
}

func (m *SingingGameScoreDetail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

// 用户房间状态（客户端重连时使用）
type GetSingingGameChannelInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameChannelInfoReq) Reset()         { *m = GetSingingGameChannelInfoReq{} }
func (m *GetSingingGameChannelInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameChannelInfoReq) ProtoMessage()    {}
func (*GetSingingGameChannelInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{20}
}
func (m *GetSingingGameChannelInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameChannelInfoReq.Unmarshal(m, b)
}
func (m *GetSingingGameChannelInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameChannelInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameChannelInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameChannelInfoReq.Merge(dst, src)
}
func (m *GetSingingGameChannelInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameChannelInfoReq.Size(m)
}
func (m *GetSingingGameChannelInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameChannelInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameChannelInfoReq proto.InternalMessageInfo

func (m *GetSingingGameChannelInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetSingingGameChannelInfoResp struct {
	ChannelInfo           *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Songs                 []*SongInfo             `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	GrabMicBtnHiddenAt    uint32                  `protobuf:"varint,3,opt,name=grab_mic_btn_hidden_at,json=grabMicBtnHiddenAt,proto3" json:"grab_mic_btn_hidden_at,omitempty"`
	GrabMicBtnDisplayTime uint32                  `protobuf:"varint,4,opt,name=grab_mic_btn_display_time,json=grabMicBtnDisplayTime,proto3" json:"grab_mic_btn_display_time,omitempty"`
	HelpBtnHiddenAt       uint32                  `protobuf:"varint,5,opt,name=help_btn_hidden_at,json=helpBtnHiddenAt,proto3" json:"help_btn_hidden_at,omitempty"`
	HelpBtnDisplayTime    uint32                  `protobuf:"varint,6,opt,name=help_btn_display_time,json=helpBtnDisplayTime,proto3" json:"help_btn_display_time,omitempty"`
	Styles                []*SingingGameSongStyle `protobuf:"bytes,7,rep,name=styles,proto3" json:"styles,omitempty"`
	SelectedStyle         *SingingGameSongStyle   `protobuf:"bytes,8,opt,name=selected_style,json=selectedStyle,proto3" json:"selected_style,omitempty"`
	VoteCountdown         uint32                  `protobuf:"varint,9,opt,name=vote_countdown,json=voteCountdown,proto3" json:"vote_countdown,omitempty"`
	NoPrepareBanDuration  uint32                  `protobuf:"varint,11,opt,name=no_prepare_ban_duration,json=noPrepareBanDuration,proto3" json:"no_prepare_ban_duration,omitempty"`
	ExitGameBanDuration   uint32                  `protobuf:"varint,12,opt,name=exit_game_ban_duration,json=exitGameBanDuration,proto3" json:"exit_game_ban_duration,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *GetSingingGameChannelInfoResp) Reset()         { *m = GetSingingGameChannelInfoResp{} }
func (m *GetSingingGameChannelInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameChannelInfoResp) ProtoMessage()    {}
func (*GetSingingGameChannelInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{21}
}
func (m *GetSingingGameChannelInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameChannelInfoResp.Unmarshal(m, b)
}
func (m *GetSingingGameChannelInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameChannelInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameChannelInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameChannelInfoResp.Merge(dst, src)
}
func (m *GetSingingGameChannelInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameChannelInfoResp.Size(m)
}
func (m *GetSingingGameChannelInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameChannelInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameChannelInfoResp proto.InternalMessageInfo

func (m *GetSingingGameChannelInfoResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetSongs() []*SongInfo {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetGrabMicBtnHiddenAt() uint32 {
	if m != nil {
		return m.GrabMicBtnHiddenAt
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetGrabMicBtnDisplayTime() uint32 {
	if m != nil {
		return m.GrabMicBtnDisplayTime
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetHelpBtnHiddenAt() uint32 {
	if m != nil {
		return m.HelpBtnHiddenAt
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetHelpBtnDisplayTime() uint32 {
	if m != nil {
		return m.HelpBtnDisplayTime
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetStyles() []*SingingGameSongStyle {
	if m != nil {
		return m.Styles
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetSelectedStyle() *SingingGameSongStyle {
	if m != nil {
		return m.SelectedStyle
	}
	return nil
}

func (m *GetSingingGameChannelInfoResp) GetVoteCountdown() uint32 {
	if m != nil {
		return m.VoteCountdown
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetNoPrepareBanDuration() uint32 {
	if m != nil {
		return m.NoPrepareBanDuration
	}
	return 0
}

func (m *GetSingingGameChannelInfoResp) GetExitGameBanDuration() uint32 {
	if m != nil {
		return m.ExitGameBanDuration
	}
	return 0
}

// 切换游戏类型
type SwitchSingingGameTypeReq struct {
	Uid                  uint32          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32          `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SingingGameType      SingingGameType `protobuf:"varint,3,opt,name=singing_game_type,json=singingGameType,proto3,enum=singaround.SingingGameType" json:"singing_game_type,omitempty"`
	NewSingingGameType   uint32          `protobuf:"varint,4,opt,name=new_singing_game_type,json=newSingingGameType,proto3" json:"new_singing_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SwitchSingingGameTypeReq) Reset()         { *m = SwitchSingingGameTypeReq{} }
func (m *SwitchSingingGameTypeReq) String() string { return proto.CompactTextString(m) }
func (*SwitchSingingGameTypeReq) ProtoMessage()    {}
func (*SwitchSingingGameTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{22}
}
func (m *SwitchSingingGameTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSingingGameTypeReq.Unmarshal(m, b)
}
func (m *SwitchSingingGameTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSingingGameTypeReq.Marshal(b, m, deterministic)
}
func (dst *SwitchSingingGameTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSingingGameTypeReq.Merge(dst, src)
}
func (m *SwitchSingingGameTypeReq) XXX_Size() int {
	return xxx_messageInfo_SwitchSingingGameTypeReq.Size(m)
}
func (m *SwitchSingingGameTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSingingGameTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSingingGameTypeReq proto.InternalMessageInfo

func (m *SwitchSingingGameTypeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SwitchSingingGameTypeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SwitchSingingGameTypeReq) GetSingingGameType() SingingGameType {
	if m != nil {
		return m.SingingGameType
	}
	return SingingGameType_SingingGameTypeUndefined
}

func (m *SwitchSingingGameTypeReq) GetNewSingingGameType() uint32 {
	if m != nil {
		return m.NewSingingGameType
	}
	return 0
}

type SwitchSingingGameTypeResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Songs                []*SongInfo             `protobuf:"bytes,2,rep,name=songs,proto3" json:"songs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SwitchSingingGameTypeResp) Reset()         { *m = SwitchSingingGameTypeResp{} }
func (m *SwitchSingingGameTypeResp) String() string { return proto.CompactTextString(m) }
func (*SwitchSingingGameTypeResp) ProtoMessage()    {}
func (*SwitchSingingGameTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{23}
}
func (m *SwitchSingingGameTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchSingingGameTypeResp.Unmarshal(m, b)
}
func (m *SwitchSingingGameTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchSingingGameTypeResp.Marshal(b, m, deterministic)
}
func (dst *SwitchSingingGameTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchSingingGameTypeResp.Merge(dst, src)
}
func (m *SwitchSingingGameTypeResp) XXX_Size() int {
	return xxx_messageInfo_SwitchSingingGameTypeResp.Size(m)
}
func (m *SwitchSingingGameTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchSingingGameTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchSingingGameTypeResp proto.InternalMessageInfo

func (m *SwitchSingingGameTypeResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SwitchSingingGameTypeResp) GetSongs() []*SongInfo {
	if m != nil {
		return m.Songs
	}
	return nil
}

// 房间全量状态信息
type SingingGameChannelInfo struct {
	ChannelId          uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	MemberLimit        uint32                 `protobuf:"varint,2,opt,name=member_limit,json=memberLimit,proto3" json:"member_limit,omitempty"`
	SingingGameType    SingingGameType        `protobuf:"varint,3,opt,name=singing_game_type,json=singingGameType,proto3,enum=singaround.SingingGameType" json:"singing_game_type,omitempty"`
	SongCountdown      uint32                 `protobuf:"varint,4,opt,name=song_countdown,json=songCountdown,proto3" json:"song_countdown,omitempty"`
	MaxLifeCount       uint32                 `protobuf:"varint,5,opt,name=max_life_count,json=maxLifeCount,proto3" json:"max_life_count,omitempty"`
	CurRoundId         uint32                 `protobuf:"varint,6,opt,name=cur_round_id,json=curRoundId,proto3" json:"cur_round_id,omitempty"`
	IsPlaying          bool                   `protobuf:"varint,7,opt,name=is_playing,json=isPlaying,proto3" json:"is_playing,omitempty"`
	Stage              SingingGameRoundStage  `protobuf:"varint,8,opt,name=stage,proto3,enum=singaround.SingingGameRoundStage" json:"stage,omitempty"`
	StageUpdatedAt     uint32                 `protobuf:"varint,9,opt,name=stage_updated_at,json=stageUpdatedAt,proto3" json:"stage_updated_at,omitempty"`
	CurSongId          string                 `protobuf:"bytes,10,opt,name=cur_song_id,json=curSongId,proto3" json:"cur_song_id,omitempty"`
	CurSongIndex       uint32                 `protobuf:"varint,11,opt,name=cur_song_index,json=curSongIndex,proto3" json:"cur_song_index,omitempty"`
	Positions          []*SingingGamePosition `protobuf:"bytes,12,rep,name=positions,proto3" json:"positions,omitempty"`
	Version            uint32                 `protobuf:"varint,13,opt,name=version,proto3" json:"version,omitempty"`
	MinimumPlayerNum   uint32                 `protobuf:"varint,14,opt,name=minimum_player_num,json=minimumPlayerNum,proto3" json:"minimum_player_num,omitempty"`
	ControllerId       uint32                 `protobuf:"varint,15,opt,name=controller_id,json=controllerId,proto3" json:"controller_id,omitempty"`
	NewSingingGameType uint32                 `protobuf:"varint,16,opt,name=new_singing_game_type,json=newSingingGameType,proto3" json:"new_singing_game_type,omitempty"`
	OfficialGroupInfo  string                 `protobuf:"bytes,17,opt,name=official_group_info,json=officialGroupInfo,proto3" json:"official_group_info,omitempty"`
	// Types that are valid to be assigned to Extra:
	//	*SingingGameChannelInfo_PassThroughExtra
	Extra                isSingingGameChannelInfo_Extra `protobuf_oneof:"extra"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *SingingGameChannelInfo) Reset()         { *m = SingingGameChannelInfo{} }
func (m *SingingGameChannelInfo) String() string { return proto.CompactTextString(m) }
func (*SingingGameChannelInfo) ProtoMessage()    {}
func (*SingingGameChannelInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{24}
}
func (m *SingingGameChannelInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameChannelInfo.Unmarshal(m, b)
}
func (m *SingingGameChannelInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameChannelInfo.Marshal(b, m, deterministic)
}
func (dst *SingingGameChannelInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameChannelInfo.Merge(dst, src)
}
func (m *SingingGameChannelInfo) XXX_Size() int {
	return xxx_messageInfo_SingingGameChannelInfo.Size(m)
}
func (m *SingingGameChannelInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameChannelInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameChannelInfo proto.InternalMessageInfo

func (m *SingingGameChannelInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameChannelInfo) GetMemberLimit() uint32 {
	if m != nil {
		return m.MemberLimit
	}
	return 0
}

func (m *SingingGameChannelInfo) GetSingingGameType() SingingGameType {
	if m != nil {
		return m.SingingGameType
	}
	return SingingGameType_SingingGameTypeUndefined
}

func (m *SingingGameChannelInfo) GetSongCountdown() uint32 {
	if m != nil {
		return m.SongCountdown
	}
	return 0
}

func (m *SingingGameChannelInfo) GetMaxLifeCount() uint32 {
	if m != nil {
		return m.MaxLifeCount
	}
	return 0
}

func (m *SingingGameChannelInfo) GetCurRoundId() uint32 {
	if m != nil {
		return m.CurRoundId
	}
	return 0
}

func (m *SingingGameChannelInfo) GetIsPlaying() bool {
	if m != nil {
		return m.IsPlaying
	}
	return false
}

func (m *SingingGameChannelInfo) GetStage() SingingGameRoundStage {
	if m != nil {
		return m.Stage
	}
	return SingingGameRoundStage_SingingGameRoundStageUndefined
}

func (m *SingingGameChannelInfo) GetStageUpdatedAt() uint32 {
	if m != nil {
		return m.StageUpdatedAt
	}
	return 0
}

func (m *SingingGameChannelInfo) GetCurSongId() string {
	if m != nil {
		return m.CurSongId
	}
	return ""
}

func (m *SingingGameChannelInfo) GetCurSongIndex() uint32 {
	if m != nil {
		return m.CurSongIndex
	}
	return 0
}

func (m *SingingGameChannelInfo) GetPositions() []*SingingGamePosition {
	if m != nil {
		return m.Positions
	}
	return nil
}

func (m *SingingGameChannelInfo) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *SingingGameChannelInfo) GetMinimumPlayerNum() uint32 {
	if m != nil {
		return m.MinimumPlayerNum
	}
	return 0
}

func (m *SingingGameChannelInfo) GetControllerId() uint32 {
	if m != nil {
		return m.ControllerId
	}
	return 0
}

func (m *SingingGameChannelInfo) GetNewSingingGameType() uint32 {
	if m != nil {
		return m.NewSingingGameType
	}
	return 0
}

func (m *SingingGameChannelInfo) GetOfficialGroupInfo() string {
	if m != nil {
		return m.OfficialGroupInfo
	}
	return ""
}

type isSingingGameChannelInfo_Extra interface {
	isSingingGameChannelInfo_Extra()
}

type SingingGameChannelInfo_PassThroughExtra struct {
	PassThroughExtra *PassThroughExtra `protobuf:"bytes,18,opt,name=pass_through_extra,json=passThroughExtra,proto3,oneof"`
}

func (*SingingGameChannelInfo_PassThroughExtra) isSingingGameChannelInfo_Extra() {}

func (m *SingingGameChannelInfo) GetExtra() isSingingGameChannelInfo_Extra {
	if m != nil {
		return m.Extra
	}
	return nil
}

func (m *SingingGameChannelInfo) GetPassThroughExtra() *PassThroughExtra {
	if x, ok := m.GetExtra().(*SingingGameChannelInfo_PassThroughExtra); ok {
		return x.PassThroughExtra
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*SingingGameChannelInfo) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _SingingGameChannelInfo_OneofMarshaler, _SingingGameChannelInfo_OneofUnmarshaler, _SingingGameChannelInfo_OneofSizer, []interface{}{
		(*SingingGameChannelInfo_PassThroughExtra)(nil),
	}
}

func _SingingGameChannelInfo_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*SingingGameChannelInfo)
	// extra
	switch x := m.Extra.(type) {
	case *SingingGameChannelInfo_PassThroughExtra:
		b.EncodeVarint(18<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PassThroughExtra); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("SingingGameChannelInfo.Extra has unexpected type %T", x)
	}
	return nil
}

func _SingingGameChannelInfo_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*SingingGameChannelInfo)
	switch tag {
	case 18: // extra.pass_through_extra
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PassThroughExtra)
		err := b.DecodeMessage(msg)
		m.Extra = &SingingGameChannelInfo_PassThroughExtra{msg}
		return true, err
	default:
		return false, nil
	}
}

func _SingingGameChannelInfo_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*SingingGameChannelInfo)
	// extra
	switch x := m.Extra.(type) {
	case *SingingGameChannelInfo_PassThroughExtra:
		s := proto.Size(x.PassThroughExtra)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type PassThroughExtra struct {
	CurSkipCount         uint32   `protobuf:"varint,1,opt,name=cur_skip_count,json=curSkipCount,proto3" json:"cur_skip_count,omitempty"`
	MaxSkipCount         uint32   `protobuf:"varint,2,opt,name=max_skip_count,json=maxSkipCount,proto3" json:"max_skip_count,omitempty"`
	CurSongIndex         uint32   `protobuf:"varint,3,opt,name=cur_song_index,json=curSongIndex,proto3" json:"cur_song_index,omitempty"`
	PassThroughCount     uint32   `protobuf:"varint,4,opt,name=pass_through_count,json=passThroughCount,proto3" json:"pass_through_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PassThroughExtra) Reset()         { *m = PassThroughExtra{} }
func (m *PassThroughExtra) String() string { return proto.CompactTextString(m) }
func (*PassThroughExtra) ProtoMessage()    {}
func (*PassThroughExtra) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{25}
}
func (m *PassThroughExtra) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PassThroughExtra.Unmarshal(m, b)
}
func (m *PassThroughExtra) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PassThroughExtra.Marshal(b, m, deterministic)
}
func (dst *PassThroughExtra) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PassThroughExtra.Merge(dst, src)
}
func (m *PassThroughExtra) XXX_Size() int {
	return xxx_messageInfo_PassThroughExtra.Size(m)
}
func (m *PassThroughExtra) XXX_DiscardUnknown() {
	xxx_messageInfo_PassThroughExtra.DiscardUnknown(m)
}

var xxx_messageInfo_PassThroughExtra proto.InternalMessageInfo

func (m *PassThroughExtra) GetCurSkipCount() uint32 {
	if m != nil {
		return m.CurSkipCount
	}
	return 0
}

func (m *PassThroughExtra) GetMaxSkipCount() uint32 {
	if m != nil {
		return m.MaxSkipCount
	}
	return 0
}

func (m *PassThroughExtra) GetCurSongIndex() uint32 {
	if m != nil {
		return m.CurSongIndex
	}
	return 0
}

func (m *PassThroughExtra) GetPassThroughCount() uint32 {
	if m != nil {
		return m.PassThroughCount
	}
	return 0
}

type SingingGamePosition struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string              `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string              `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Index                uint32              `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	Prepared             bool                `protobuf:"varint,5,opt,name=prepared,proto3" json:"prepared,omitempty"`
	LeftLifeCount        uint32              `protobuf:"varint,6,opt,name=left_life_count,json=leftLifeCount,proto3" json:"left_life_count,omitempty"`
	UserType             SingingGameUserType `protobuf:"varint,7,opt,name=user_type,json=userType,proto3,enum=singaround.SingingGameUserType" json:"user_type,omitempty"`
	Score                uint32              `protobuf:"varint,8,opt,name=score,proto3" json:"score,omitempty"`
	UserSetImageId       string              `protobuf:"bytes,9,opt,name=user_set_image_id,json=userSetImageId,proto3" json:"user_set_image_id,omitempty"`
	DecorationId         string              `protobuf:"bytes,10,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	Sex                  uint32              `protobuf:"varint,11,opt,name=sex,proto3" json:"sex,omitempty"`
	HaveRunAway          bool                `protobuf:"varint,12,opt,name=have_run_away,json=haveRunAway,proto3" json:"have_run_away,omitempty"`
	PrecentorTag         string              `protobuf:"bytes,13,opt,name=precentor_tag,json=precentorTag,proto3" json:"precentor_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SingingGamePosition) Reset()         { *m = SingingGamePosition{} }
func (m *SingingGamePosition) String() string { return proto.CompactTextString(m) }
func (*SingingGamePosition) ProtoMessage()    {}
func (*SingingGamePosition) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{26}
}
func (m *SingingGamePosition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGamePosition.Unmarshal(m, b)
}
func (m *SingingGamePosition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGamePosition.Marshal(b, m, deterministic)
}
func (dst *SingingGamePosition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGamePosition.Merge(dst, src)
}
func (m *SingingGamePosition) XXX_Size() int {
	return xxx_messageInfo_SingingGamePosition.Size(m)
}
func (m *SingingGamePosition) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGamePosition.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGamePosition proto.InternalMessageInfo

func (m *SingingGamePosition) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingingGamePosition) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *SingingGamePosition) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SingingGamePosition) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *SingingGamePosition) GetPrepared() bool {
	if m != nil {
		return m.Prepared
	}
	return false
}

func (m *SingingGamePosition) GetLeftLifeCount() uint32 {
	if m != nil {
		return m.LeftLifeCount
	}
	return 0
}

func (m *SingingGamePosition) GetUserType() SingingGameUserType {
	if m != nil {
		return m.UserType
	}
	return SingingGameUserType_SingingGameUserTypeUndefined
}

func (m *SingingGamePosition) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *SingingGamePosition) GetUserSetImageId() string {
	if m != nil {
		return m.UserSetImageId
	}
	return ""
}

func (m *SingingGamePosition) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *SingingGamePosition) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *SingingGamePosition) GetHaveRunAway() bool {
	if m != nil {
		return m.HaveRunAway
	}
	return false
}

func (m *SingingGamePosition) GetPrecentorTag() string {
	if m != nil {
		return m.PrecentorTag
	}
	return ""
}

type SingingGameSongStyle struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	SupportedNum         uint32   `protobuf:"varint,3,opt,name=supported_num,json=supportedNum,proto3" json:"supported_num,omitempty"`
	IsOpen               bool     `protobuf:"varint,4,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameSongStyle) Reset()         { *m = SingingGameSongStyle{} }
func (m *SingingGameSongStyle) String() string { return proto.CompactTextString(m) }
func (*SingingGameSongStyle) ProtoMessage()    {}
func (*SingingGameSongStyle) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{27}
}
func (m *SingingGameSongStyle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameSongStyle.Unmarshal(m, b)
}
func (m *SingingGameSongStyle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameSongStyle.Marshal(b, m, deterministic)
}
func (dst *SingingGameSongStyle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameSongStyle.Merge(dst, src)
}
func (m *SingingGameSongStyle) XXX_Size() int {
	return xxx_messageInfo_SingingGameSongStyle.Size(m)
}
func (m *SingingGameSongStyle) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameSongStyle.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameSongStyle proto.InternalMessageInfo

func (m *SingingGameSongStyle) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SingingGameSongStyle) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *SingingGameSongStyle) GetSupportedNum() uint32 {
	if m != nil {
		return m.SupportedNum
	}
	return 0
}

func (m *SingingGameSongStyle) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

// 投票
type SingingGameVoteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	StyleId              string   `protobuf:"bytes,4,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameVoteReq) Reset()         { *m = SingingGameVoteReq{} }
func (m *SingingGameVoteReq) String() string { return proto.CompactTextString(m) }
func (*SingingGameVoteReq) ProtoMessage()    {}
func (*SingingGameVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{28}
}
func (m *SingingGameVoteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameVoteReq.Unmarshal(m, b)
}
func (m *SingingGameVoteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameVoteReq.Marshal(b, m, deterministic)
}
func (dst *SingingGameVoteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameVoteReq.Merge(dst, src)
}
func (m *SingingGameVoteReq) XXX_Size() int {
	return xxx_messageInfo_SingingGameVoteReq.Size(m)
}
func (m *SingingGameVoteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameVoteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameVoteReq proto.InternalMessageInfo

func (m *SingingGameVoteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingingGameVoteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameVoteReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *SingingGameVoteReq) GetStyleId() string {
	if m != nil {
		return m.StyleId
	}
	return ""
}

type SingingGameVoteResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	Styles               []*SingingGameSongStyle `protobuf:"bytes,2,rep,name=styles,proto3" json:"styles,omitempty"`
	Style                *SingingGameSongStyle   `protobuf:"bytes,3,opt,name=style,proto3" json:"style,omitempty"`
	Songs                []*SongInfo             `protobuf:"bytes,4,rep,name=songs,proto3" json:"songs,omitempty"`
	VoteCountdown        uint32                  `protobuf:"varint,5,opt,name=vote_countdown,json=voteCountdown,proto3" json:"vote_countdown,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SingingGameVoteResp) Reset()         { *m = SingingGameVoteResp{} }
func (m *SingingGameVoteResp) String() string { return proto.CompactTextString(m) }
func (*SingingGameVoteResp) ProtoMessage()    {}
func (*SingingGameVoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{29}
}
func (m *SingingGameVoteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameVoteResp.Unmarshal(m, b)
}
func (m *SingingGameVoteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameVoteResp.Marshal(b, m, deterministic)
}
func (dst *SingingGameVoteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameVoteResp.Merge(dst, src)
}
func (m *SingingGameVoteResp) XXX_Size() int {
	return xxx_messageInfo_SingingGameVoteResp.Size(m)
}
func (m *SingingGameVoteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameVoteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameVoteResp proto.InternalMessageInfo

func (m *SingingGameVoteResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

func (m *SingingGameVoteResp) GetStyles() []*SingingGameSongStyle {
	if m != nil {
		return m.Styles
	}
	return nil
}

func (m *SingingGameVoteResp) GetStyle() *SingingGameSongStyle {
	if m != nil {
		return m.Style
	}
	return nil
}

func (m *SingingGameVoteResp) GetSongs() []*SongInfo {
	if m != nil {
		return m.Songs
	}
	return nil
}

func (m *SingingGameVoteResp) GetVoteCountdown() uint32 {
	if m != nil {
		return m.VoteCountdown
	}
	return 0
}

// 完成资源加载
type AccomplishLoadingResReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccomplishLoadingResReq) Reset()         { *m = AccomplishLoadingResReq{} }
func (m *AccomplishLoadingResReq) String() string { return proto.CompactTextString(m) }
func (*AccomplishLoadingResReq) ProtoMessage()    {}
func (*AccomplishLoadingResReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{30}
}
func (m *AccomplishLoadingResReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishLoadingResReq.Unmarshal(m, b)
}
func (m *AccomplishLoadingResReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishLoadingResReq.Marshal(b, m, deterministic)
}
func (dst *AccomplishLoadingResReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishLoadingResReq.Merge(dst, src)
}
func (m *AccomplishLoadingResReq) XXX_Size() int {
	return xxx_messageInfo_AccomplishLoadingResReq.Size(m)
}
func (m *AccomplishLoadingResReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishLoadingResReq.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishLoadingResReq proto.InternalMessageInfo

func (m *AccomplishLoadingResReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AccomplishLoadingResReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AccomplishLoadingResReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type AccomplishLoadingResResp struct {
	ChannelInfo          *SingingGameChannelInfo `protobuf:"bytes,1,opt,name=channel_info,json=channelInfo,proto3" json:"channel_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *AccomplishLoadingResResp) Reset()         { *m = AccomplishLoadingResResp{} }
func (m *AccomplishLoadingResResp) String() string { return proto.CompactTextString(m) }
func (*AccomplishLoadingResResp) ProtoMessage()    {}
func (*AccomplishLoadingResResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{31}
}
func (m *AccomplishLoadingResResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccomplishLoadingResResp.Unmarshal(m, b)
}
func (m *AccomplishLoadingResResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccomplishLoadingResResp.Marshal(b, m, deterministic)
}
func (dst *AccomplishLoadingResResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccomplishLoadingResResp.Merge(dst, src)
}
func (m *AccomplishLoadingResResp) XXX_Size() int {
	return xxx_messageInfo_AccomplishLoadingResResp.Size(m)
}
func (m *AccomplishLoadingResResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AccomplishLoadingResResp.DiscardUnknown(m)
}

var xxx_messageInfo_AccomplishLoadingResResp proto.InternalMessageInfo

func (m *AccomplishLoadingResResp) GetChannelInfo() *SingingGameChannelInfo {
	if m != nil {
		return m.ChannelInfo
	}
	return nil
}

// 排行榜
type GetSingingGameRankListReq struct {
	Uid                  uint32       `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type                 RankListType `protobuf:"varint,2,opt,name=type,proto3,enum=singaround.RankListType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameRankListReq) Reset()         { *m = GetSingingGameRankListReq{} }
func (m *GetSingingGameRankListReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRankListReq) ProtoMessage()    {}
func (*GetSingingGameRankListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{32}
}
func (m *GetSingingGameRankListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRankListReq.Unmarshal(m, b)
}
func (m *GetSingingGameRankListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRankListReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRankListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRankListReq.Merge(dst, src)
}
func (m *GetSingingGameRankListReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRankListReq.Size(m)
}
func (m *GetSingingGameRankListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRankListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRankListReq proto.InternalMessageInfo

func (m *GetSingingGameRankListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSingingGameRankListReq) GetType() RankListType {
	if m != nil {
		return m.Type
	}
	return RankListType_RankListTypeUndefined
}

type GetSingingGameRankListResp struct {
	List                 []*SingingGameRankListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	PersonalRank         *SingingGameRankListItem   `protobuf:"bytes,2,opt,name=personal_rank,json=personalRank,proto3" json:"personal_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSingingGameRankListResp) Reset()         { *m = GetSingingGameRankListResp{} }
func (m *GetSingingGameRankListResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRankListResp) ProtoMessage()    {}
func (*GetSingingGameRankListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{33}
}
func (m *GetSingingGameRankListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRankListResp.Unmarshal(m, b)
}
func (m *GetSingingGameRankListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRankListResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRankListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRankListResp.Merge(dst, src)
}
func (m *GetSingingGameRankListResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRankListResp.Size(m)
}
func (m *GetSingingGameRankListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRankListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRankListResp proto.InternalMessageInfo

func (m *GetSingingGameRankListResp) GetList() []*SingingGameRankListItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetSingingGameRankListResp) GetPersonalRank() *SingingGameRankListItem {
	if m != nil {
		return m.PersonalRank
	}
	return nil
}

// 通关模式游戏记录
type GetSingingGameRecordListReq struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Count                uint32               `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	LoadMore             *SingingGameLoadMore `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetSingingGameRecordListReq) Reset()         { *m = GetSingingGameRecordListReq{} }
func (m *GetSingingGameRecordListReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRecordListReq) ProtoMessage()    {}
func (*GetSingingGameRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{34}
}
func (m *GetSingingGameRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRecordListReq.Unmarshal(m, b)
}
func (m *GetSingingGameRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRecordListReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRecordListReq.Merge(dst, src)
}
func (m *GetSingingGameRecordListReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRecordListReq.Size(m)
}
func (m *GetSingingGameRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRecordListReq proto.InternalMessageInfo

func (m *GetSingingGameRecordListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetSingingGameRecordListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetSingingGameRecordListReq) GetLoadMore() *SingingGameLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetSingingGameRecordListResp struct {
	List                 []*SingingGameRankListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	LoadMore             *SingingGameLoadMore       `protobuf:"bytes,2,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSingingGameRecordListResp) Reset()         { *m = GetSingingGameRecordListResp{} }
func (m *GetSingingGameRecordListResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRecordListResp) ProtoMessage()    {}
func (*GetSingingGameRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{35}
}
func (m *GetSingingGameRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRecordListResp.Unmarshal(m, b)
}
func (m *GetSingingGameRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRecordListResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRecordListResp.Merge(dst, src)
}
func (m *GetSingingGameRecordListResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRecordListResp.Size(m)
}
func (m *GetSingingGameRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRecordListResp proto.InternalMessageInfo

func (m *GetSingingGameRecordListResp) GetList() []*SingingGameRankListItem {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetSingingGameRecordListResp) GetLoadMore() *SingingGameLoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type SingingGameRankListItem struct {
	Rank                 uint32                `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`
	Accounts             []string              `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
	PassThroughCount     uint32                `protobuf:"varint,3,opt,name=pass_through_count,json=passThroughCount,proto3" json:"pass_through_count,omitempty"`
	UpdatedAt            uint32                `protobuf:"varint,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	ResultType           SingingGameResultType `protobuf:"varint,5,opt,name=result_type,json=resultType,proto3,enum=singaround.SingingGameResultType" json:"result_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SingingGameRankListItem) Reset()         { *m = SingingGameRankListItem{} }
func (m *SingingGameRankListItem) String() string { return proto.CompactTextString(m) }
func (*SingingGameRankListItem) ProtoMessage()    {}
func (*SingingGameRankListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{36}
}
func (m *SingingGameRankListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameRankListItem.Unmarshal(m, b)
}
func (m *SingingGameRankListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameRankListItem.Marshal(b, m, deterministic)
}
func (dst *SingingGameRankListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameRankListItem.Merge(dst, src)
}
func (m *SingingGameRankListItem) XXX_Size() int {
	return xxx_messageInfo_SingingGameRankListItem.Size(m)
}
func (m *SingingGameRankListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameRankListItem.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameRankListItem proto.InternalMessageInfo

func (m *SingingGameRankListItem) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SingingGameRankListItem) GetAccounts() []string {
	if m != nil {
		return m.Accounts
	}
	return nil
}

func (m *SingingGameRankListItem) GetPassThroughCount() uint32 {
	if m != nil {
		return m.PassThroughCount
	}
	return 0
}

func (m *SingingGameRankListItem) GetUpdatedAt() uint32 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

func (m *SingingGameRankListItem) GetResultType() SingingGameResultType {
	if m != nil {
		return m.ResultType
	}
	return SingingGameResultType_SingingGameResultTypeUndefined
}

type SingingGameLoadMore struct {
	LastPage             uint32   `protobuf:"varint,1,opt,name=last_page,json=lastPage,proto3" json:"last_page,omitempty"`
	LastCount            uint32   `protobuf:"varint,2,opt,name=last_count,json=lastCount,proto3" json:"last_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameLoadMore) Reset()         { *m = SingingGameLoadMore{} }
func (m *SingingGameLoadMore) String() string { return proto.CompactTextString(m) }
func (*SingingGameLoadMore) ProtoMessage()    {}
func (*SingingGameLoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{37}
}
func (m *SingingGameLoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameLoadMore.Unmarshal(m, b)
}
func (m *SingingGameLoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameLoadMore.Marshal(b, m, deterministic)
}
func (dst *SingingGameLoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameLoadMore.Merge(dst, src)
}
func (m *SingingGameLoadMore) XXX_Size() int {
	return xxx_messageInfo_SingingGameLoadMore.Size(m)
}
func (m *SingingGameLoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameLoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameLoadMore proto.InternalMessageInfo

func (m *SingingGameLoadMore) GetLastPage() uint32 {
	if m != nil {
		return m.LastPage
	}
	return 0
}

func (m *SingingGameLoadMore) GetLastCount() uint32 {
	if m != nil {
		return m.LastCount
	}
	return 0
}

// 添加曲风
type AddSongStyleReq struct {
	Style                *SingingGameSongStyle `protobuf:"bytes,1,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AddSongStyleReq) Reset()         { *m = AddSongStyleReq{} }
func (m *AddSongStyleReq) String() string { return proto.CompactTextString(m) }
func (*AddSongStyleReq) ProtoMessage()    {}
func (*AddSongStyleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{38}
}
func (m *AddSongStyleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongStyleReq.Unmarshal(m, b)
}
func (m *AddSongStyleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongStyleReq.Marshal(b, m, deterministic)
}
func (dst *AddSongStyleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongStyleReq.Merge(dst, src)
}
func (m *AddSongStyleReq) XXX_Size() int {
	return xxx_messageInfo_AddSongStyleReq.Size(m)
}
func (m *AddSongStyleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongStyleReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongStyleReq proto.InternalMessageInfo

func (m *AddSongStyleReq) GetStyle() *SingingGameSongStyle {
	if m != nil {
		return m.Style
	}
	return nil
}

type AddSongStyleResp struct {
	Style                *SingingGameSongStyle `protobuf:"bytes,1,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *AddSongStyleResp) Reset()         { *m = AddSongStyleResp{} }
func (m *AddSongStyleResp) String() string { return proto.CompactTextString(m) }
func (*AddSongStyleResp) ProtoMessage()    {}
func (*AddSongStyleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{39}
}
func (m *AddSongStyleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSongStyleResp.Unmarshal(m, b)
}
func (m *AddSongStyleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSongStyleResp.Marshal(b, m, deterministic)
}
func (dst *AddSongStyleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSongStyleResp.Merge(dst, src)
}
func (m *AddSongStyleResp) XXX_Size() int {
	return xxx_messageInfo_AddSongStyleResp.Size(m)
}
func (m *AddSongStyleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSongStyleResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSongStyleResp proto.InternalMessageInfo

func (m *AddSongStyleResp) GetStyle() *SingingGameSongStyle {
	if m != nil {
		return m.Style
	}
	return nil
}

// 删除曲风
type DelSongStyleReq struct {
	StyleId              string   `protobuf:"bytes,1,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongStyleReq) Reset()         { *m = DelSongStyleReq{} }
func (m *DelSongStyleReq) String() string { return proto.CompactTextString(m) }
func (*DelSongStyleReq) ProtoMessage()    {}
func (*DelSongStyleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{40}
}
func (m *DelSongStyleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongStyleReq.Unmarshal(m, b)
}
func (m *DelSongStyleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongStyleReq.Marshal(b, m, deterministic)
}
func (dst *DelSongStyleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongStyleReq.Merge(dst, src)
}
func (m *DelSongStyleReq) XXX_Size() int {
	return xxx_messageInfo_DelSongStyleReq.Size(m)
}
func (m *DelSongStyleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongStyleReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongStyleReq proto.InternalMessageInfo

func (m *DelSongStyleReq) GetStyleId() string {
	if m != nil {
		return m.StyleId
	}
	return ""
}

type DelSongStyleResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongStyleResp) Reset()         { *m = DelSongStyleResp{} }
func (m *DelSongStyleResp) String() string { return proto.CompactTextString(m) }
func (*DelSongStyleResp) ProtoMessage()    {}
func (*DelSongStyleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{41}
}
func (m *DelSongStyleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongStyleResp.Unmarshal(m, b)
}
func (m *DelSongStyleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongStyleResp.Marshal(b, m, deterministic)
}
func (dst *DelSongStyleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongStyleResp.Merge(dst, src)
}
func (m *DelSongStyleResp) XXX_Size() int {
	return xxx_messageInfo_DelSongStyleResp.Size(m)
}
func (m *DelSongStyleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongStyleResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongStyleResp proto.InternalMessageInfo

// 获取所有曲风
type GetAllSongStyleReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSongStyleReq) Reset()         { *m = GetAllSongStyleReq{} }
func (m *GetAllSongStyleReq) String() string { return proto.CompactTextString(m) }
func (*GetAllSongStyleReq) ProtoMessage()    {}
func (*GetAllSongStyleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{42}
}
func (m *GetAllSongStyleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSongStyleReq.Unmarshal(m, b)
}
func (m *GetAllSongStyleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSongStyleReq.Marshal(b, m, deterministic)
}
func (dst *GetAllSongStyleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSongStyleReq.Merge(dst, src)
}
func (m *GetAllSongStyleReq) XXX_Size() int {
	return xxx_messageInfo_GetAllSongStyleReq.Size(m)
}
func (m *GetAllSongStyleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSongStyleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSongStyleReq proto.InternalMessageInfo

type GetAllSongStyleResp struct {
	Styles               []*SingingGameSongStyle `protobuf:"bytes,1,rep,name=styles,proto3" json:"styles,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAllSongStyleResp) Reset()         { *m = GetAllSongStyleResp{} }
func (m *GetAllSongStyleResp) String() string { return proto.CompactTextString(m) }
func (*GetAllSongStyleResp) ProtoMessage()    {}
func (*GetAllSongStyleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{43}
}
func (m *GetAllSongStyleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSongStyleResp.Unmarshal(m, b)
}
func (m *GetAllSongStyleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSongStyleResp.Marshal(b, m, deterministic)
}
func (dst *GetAllSongStyleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSongStyleResp.Merge(dst, src)
}
func (m *GetAllSongStyleResp) XXX_Size() int {
	return xxx_messageInfo_GetAllSongStyleResp.Size(m)
}
func (m *GetAllSongStyleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSongStyleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSongStyleResp proto.InternalMessageInfo

func (m *GetAllSongStyleResp) GetStyles() []*SingingGameSongStyle {
	if m != nil {
		return m.Styles
	}
	return nil
}

// 牛啊
type ExpressSingingGameLikeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	SongId               string   `protobuf:"bytes,4,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressSingingGameLikeReq) Reset()         { *m = ExpressSingingGameLikeReq{} }
func (m *ExpressSingingGameLikeReq) String() string { return proto.CompactTextString(m) }
func (*ExpressSingingGameLikeReq) ProtoMessage()    {}
func (*ExpressSingingGameLikeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{44}
}
func (m *ExpressSingingGameLikeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressSingingGameLikeReq.Unmarshal(m, b)
}
func (m *ExpressSingingGameLikeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressSingingGameLikeReq.Marshal(b, m, deterministic)
}
func (dst *ExpressSingingGameLikeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressSingingGameLikeReq.Merge(dst, src)
}
func (m *ExpressSingingGameLikeReq) XXX_Size() int {
	return xxx_messageInfo_ExpressSingingGameLikeReq.Size(m)
}
func (m *ExpressSingingGameLikeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressSingingGameLikeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressSingingGameLikeReq proto.InternalMessageInfo

func (m *ExpressSingingGameLikeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExpressSingingGameLikeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ExpressSingingGameLikeReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

func (m *ExpressSingingGameLikeReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *ExpressSingingGameLikeReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ExpressSingingGameLikeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExpressSingingGameLikeResp) Reset()         { *m = ExpressSingingGameLikeResp{} }
func (m *ExpressSingingGameLikeResp) String() string { return proto.CompactTextString(m) }
func (*ExpressSingingGameLikeResp) ProtoMessage()    {}
func (*ExpressSingingGameLikeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{45}
}
func (m *ExpressSingingGameLikeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExpressSingingGameLikeResp.Unmarshal(m, b)
}
func (m *ExpressSingingGameLikeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExpressSingingGameLikeResp.Marshal(b, m, deterministic)
}
func (dst *ExpressSingingGameLikeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExpressSingingGameLikeResp.Merge(dst, src)
}
func (m *ExpressSingingGameLikeResp) XXX_Size() int {
	return xxx_messageInfo_ExpressSingingGameLikeResp.Size(m)
}
func (m *ExpressSingingGameLikeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExpressSingingGameLikeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExpressSingingGameLikeResp proto.InternalMessageInfo

// 碰拳
type SingingGameFistBumpReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameFistBumpReq) Reset()         { *m = SingingGameFistBumpReq{} }
func (m *SingingGameFistBumpReq) String() string { return proto.CompactTextString(m) }
func (*SingingGameFistBumpReq) ProtoMessage()    {}
func (*SingingGameFistBumpReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{46}
}
func (m *SingingGameFistBumpReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameFistBumpReq.Unmarshal(m, b)
}
func (m *SingingGameFistBumpReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameFistBumpReq.Marshal(b, m, deterministic)
}
func (dst *SingingGameFistBumpReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameFistBumpReq.Merge(dst, src)
}
func (m *SingingGameFistBumpReq) XXX_Size() int {
	return xxx_messageInfo_SingingGameFistBumpReq.Size(m)
}
func (m *SingingGameFistBumpReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameFistBumpReq.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameFistBumpReq proto.InternalMessageInfo

func (m *SingingGameFistBumpReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SingingGameFistBumpReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SingingGameFistBumpReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type SingingGameFistBumpResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameFistBumpResp) Reset()         { *m = SingingGameFistBumpResp{} }
func (m *SingingGameFistBumpResp) String() string { return proto.CompactTextString(m) }
func (*SingingGameFistBumpResp) ProtoMessage()    {}
func (*SingingGameFistBumpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{47}
}
func (m *SingingGameFistBumpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameFistBumpResp.Unmarshal(m, b)
}
func (m *SingingGameFistBumpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameFistBumpResp.Marshal(b, m, deterministic)
}
func (dst *SingingGameFistBumpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameFistBumpResp.Merge(dst, src)
}
func (m *SingingGameFistBumpResp) XXX_Size() int {
	return xxx_messageInfo_SingingGameFistBumpResp.Size(m)
}
func (m *SingingGameFistBumpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameFistBumpResp.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameFistBumpResp proto.InternalMessageInfo

// 开始游戏（UGC房）
type StartUgcChannelSingingGameReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartUgcChannelSingingGameReq) Reset()         { *m = StartUgcChannelSingingGameReq{} }
func (m *StartUgcChannelSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*StartUgcChannelSingingGameReq) ProtoMessage()    {}
func (*StartUgcChannelSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{48}
}
func (m *StartUgcChannelSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartUgcChannelSingingGameReq.Unmarshal(m, b)
}
func (m *StartUgcChannelSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartUgcChannelSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *StartUgcChannelSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartUgcChannelSingingGameReq.Merge(dst, src)
}
func (m *StartUgcChannelSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_StartUgcChannelSingingGameReq.Size(m)
}
func (m *StartUgcChannelSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StartUgcChannelSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_StartUgcChannelSingingGameReq proto.InternalMessageInfo

func (m *StartUgcChannelSingingGameReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StartUgcChannelSingingGameReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *StartUgcChannelSingingGameReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type StartUgcChannelSingingGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StartUgcChannelSingingGameResp) Reset()         { *m = StartUgcChannelSingingGameResp{} }
func (m *StartUgcChannelSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*StartUgcChannelSingingGameResp) ProtoMessage()    {}
func (*StartUgcChannelSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{49}
}
func (m *StartUgcChannelSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StartUgcChannelSingingGameResp.Unmarshal(m, b)
}
func (m *StartUgcChannelSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StartUgcChannelSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *StartUgcChannelSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StartUgcChannelSingingGameResp.Merge(dst, src)
}
func (m *StartUgcChannelSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_StartUgcChannelSingingGameResp.Size(m)
}
func (m *StartUgcChannelSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StartUgcChannelSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_StartUgcChannelSingingGameResp proto.InternalMessageInfo

// 取消准备（UGC房）
type CancelSingingGamePreparationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	RoundId              uint32   `protobuf:"varint,3,opt,name=round_id,json=roundId,proto3" json:"round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelSingingGamePreparationReq) Reset()         { *m = CancelSingingGamePreparationReq{} }
func (m *CancelSingingGamePreparationReq) String() string { return proto.CompactTextString(m) }
func (*CancelSingingGamePreparationReq) ProtoMessage()    {}
func (*CancelSingingGamePreparationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{50}
}
func (m *CancelSingingGamePreparationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelSingingGamePreparationReq.Unmarshal(m, b)
}
func (m *CancelSingingGamePreparationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelSingingGamePreparationReq.Marshal(b, m, deterministic)
}
func (dst *CancelSingingGamePreparationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelSingingGamePreparationReq.Merge(dst, src)
}
func (m *CancelSingingGamePreparationReq) XXX_Size() int {
	return xxx_messageInfo_CancelSingingGamePreparationReq.Size(m)
}
func (m *CancelSingingGamePreparationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelSingingGamePreparationReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelSingingGamePreparationReq proto.InternalMessageInfo

func (m *CancelSingingGamePreparationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CancelSingingGamePreparationReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CancelSingingGamePreparationReq) GetRoundId() uint32 {
	if m != nil {
		return m.RoundId
	}
	return 0
}

type CancelSingingGamePreparationResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelSingingGamePreparationResp) Reset()         { *m = CancelSingingGamePreparationResp{} }
func (m *CancelSingingGamePreparationResp) String() string { return proto.CompactTextString(m) }
func (*CancelSingingGamePreparationResp) ProtoMessage()    {}
func (*CancelSingingGamePreparationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{51}
}
func (m *CancelSingingGamePreparationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelSingingGamePreparationResp.Unmarshal(m, b)
}
func (m *CancelSingingGamePreparationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelSingingGamePreparationResp.Marshal(b, m, deterministic)
}
func (dst *CancelSingingGamePreparationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelSingingGamePreparationResp.Merge(dst, src)
}
func (m *CancelSingingGamePreparationResp) XXX_Size() int {
	return xxx_messageInfo_CancelSingingGamePreparationResp.Size(m)
}
func (m *CancelSingingGamePreparationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelSingingGamePreparationResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelSingingGamePreparationResp proto.InternalMessageInfo

type UserImageConf struct {
	ImageId              string   `protobuf:"bytes,1,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	ImageName            string   `protobuf:"bytes,2,opt,name=image_name,json=imageName,proto3" json:"image_name,omitempty"`
	ImageUrl             string   `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	ImageType            uint32   `protobuf:"varint,5,opt,name=image_type,json=imageType,proto3" json:"image_type,omitempty"`
	Index                uint32   `protobuf:"varint,6,opt,name=index,proto3" json:"index,omitempty"`
	DefaultIconUrl       string   `protobuf:"bytes,7,opt,name=default_icon_url,json=defaultIconUrl,proto3" json:"default_icon_url,omitempty"`
	DefaultSingAvatarUrl string   `protobuf:"bytes,8,opt,name=default_sing_avatar_url,json=defaultSingAvatarUrl,proto3" json:"default_sing_avatar_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserImageConf) Reset()         { *m = UserImageConf{} }
func (m *UserImageConf) String() string { return proto.CompactTextString(m) }
func (*UserImageConf) ProtoMessage()    {}
func (*UserImageConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{52}
}
func (m *UserImageConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserImageConf.Unmarshal(m, b)
}
func (m *UserImageConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserImageConf.Marshal(b, m, deterministic)
}
func (dst *UserImageConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserImageConf.Merge(dst, src)
}
func (m *UserImageConf) XXX_Size() int {
	return xxx_messageInfo_UserImageConf.Size(m)
}
func (m *UserImageConf) XXX_DiscardUnknown() {
	xxx_messageInfo_UserImageConf.DiscardUnknown(m)
}

var xxx_messageInfo_UserImageConf proto.InternalMessageInfo

func (m *UserImageConf) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *UserImageConf) GetImageName() string {
	if m != nil {
		return m.ImageName
	}
	return ""
}

func (m *UserImageConf) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *UserImageConf) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *UserImageConf) GetImageType() uint32 {
	if m != nil {
		return m.ImageType
	}
	return 0
}

func (m *UserImageConf) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *UserImageConf) GetDefaultIconUrl() string {
	if m != nil {
		return m.DefaultIconUrl
	}
	return ""
}

func (m *UserImageConf) GetDefaultSingAvatarUrl() string {
	if m != nil {
		return m.DefaultSingAvatarUrl
	}
	return ""
}

// 唱歌时装饰
type DecorationInfo struct {
	DecorationId         string   `protobuf:"bytes,1,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	DecorationName       string   `protobuf:"bytes,2,opt,name=decoration_name,json=decorationName,proto3" json:"decoration_name,omitempty"`
	DecorationUrl        string   `protobuf:"bytes,3,opt,name=decoration_url,json=decorationUrl,proto3" json:"decoration_url,omitempty"`
	Md5                  string   `protobuf:"bytes,4,opt,name=md5,proto3" json:"md5,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DecorationInfo) Reset()         { *m = DecorationInfo{} }
func (m *DecorationInfo) String() string { return proto.CompactTextString(m) }
func (*DecorationInfo) ProtoMessage()    {}
func (*DecorationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{53}
}
func (m *DecorationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DecorationInfo.Unmarshal(m, b)
}
func (m *DecorationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DecorationInfo.Marshal(b, m, deterministic)
}
func (dst *DecorationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DecorationInfo.Merge(dst, src)
}
func (m *DecorationInfo) XXX_Size() int {
	return xxx_messageInfo_DecorationInfo.Size(m)
}
func (m *DecorationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DecorationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DecorationInfo proto.InternalMessageInfo

func (m *DecorationInfo) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *DecorationInfo) GetDecorationName() string {
	if m != nil {
		return m.DecorationName
	}
	return ""
}

func (m *DecorationInfo) GetDecorationUrl() string {
	if m != nil {
		return m.DecorationUrl
	}
	return ""
}

func (m *DecorationInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

type GetAllSingImageConfReq struct {
	Version              uint32   `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllSingImageConfReq) Reset()         { *m = GetAllSingImageConfReq{} }
func (m *GetAllSingImageConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllSingImageConfReq) ProtoMessage()    {}
func (*GetAllSingImageConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{54}
}
func (m *GetAllSingImageConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSingImageConfReq.Unmarshal(m, b)
}
func (m *GetAllSingImageConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSingImageConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllSingImageConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSingImageConfReq.Merge(dst, src)
}
func (m *GetAllSingImageConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllSingImageConfReq.Size(m)
}
func (m *GetAllSingImageConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSingImageConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSingImageConfReq proto.InternalMessageInfo

func (m *GetAllSingImageConfReq) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetAllSingImageConfResp struct {
	UserImageConfList    []*UserImageConf  `protobuf:"bytes,1,rep,name=user_image_conf_list,json=userImageConfList,proto3" json:"user_image_conf_list,omitempty"`
	DecorationList       []*DecorationInfo `protobuf:"bytes,2,rep,name=decoration_list,json=decorationList,proto3" json:"decoration_list,omitempty"`
	Version              uint32            `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllSingImageConfResp) Reset()         { *m = GetAllSingImageConfResp{} }
func (m *GetAllSingImageConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllSingImageConfResp) ProtoMessage()    {}
func (*GetAllSingImageConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{55}
}
func (m *GetAllSingImageConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllSingImageConfResp.Unmarshal(m, b)
}
func (m *GetAllSingImageConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllSingImageConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllSingImageConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllSingImageConfResp.Merge(dst, src)
}
func (m *GetAllSingImageConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllSingImageConfResp.Size(m)
}
func (m *GetAllSingImageConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllSingImageConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllSingImageConfResp proto.InternalMessageInfo

func (m *GetAllSingImageConfResp) GetUserImageConfList() []*UserImageConf {
	if m != nil {
		return m.UserImageConfList
	}
	return nil
}

func (m *GetAllSingImageConfResp) GetDecorationList() []*DecorationInfo {
	if m != nil {
		return m.DecorationList
	}
	return nil
}

func (m *GetAllSingImageConfResp) GetVersion() uint32 {
	if m != nil {
		return m.Version
	}
	return 0
}

type GetUserSingImageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSingImageReq) Reset()         { *m = GetUserSingImageReq{} }
func (m *GetUserSingImageReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSingImageReq) ProtoMessage()    {}
func (*GetUserSingImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{56}
}
func (m *GetUserSingImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingImageReq.Unmarshal(m, b)
}
func (m *GetUserSingImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingImageReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSingImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingImageReq.Merge(dst, src)
}
func (m *GetUserSingImageReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSingImageReq.Size(m)
}
func (m *GetUserSingImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingImageReq proto.InternalMessageInfo

func (m *GetUserSingImageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSingImageResp struct {
	ImageIdList          []string                          `protobuf:"bytes,1,rep,name=image_id_list,json=imageIdList,proto3" json:"image_id_list,omitempty"`
	UserSetImageId       string                            `protobuf:"bytes,2,opt,name=user_set_image_id,json=userSetImageId,proto3" json:"user_set_image_id,omitempty"`
	DecorationId         string                            `protobuf:"bytes,3,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	DecorationEndTs      uint32                            `protobuf:"varint,4,opt,name=decoration_end_ts,json=decorationEndTs,proto3" json:"decoration_end_ts,omitempty"`
	LockImages           []*GetUserSingImageResp_LockImage `protobuf:"bytes,5,rep,name=lock_images,json=lockImages,proto3" json:"lock_images,omitempty"`
	IsPrecentor          bool                              `protobuf:"varint,6,opt,name=is_precentor,json=isPrecentor,proto3" json:"is_precentor,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetUserSingImageResp) Reset()         { *m = GetUserSingImageResp{} }
func (m *GetUserSingImageResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSingImageResp) ProtoMessage()    {}
func (*GetUserSingImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{57}
}
func (m *GetUserSingImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingImageResp.Unmarshal(m, b)
}
func (m *GetUserSingImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingImageResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSingImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingImageResp.Merge(dst, src)
}
func (m *GetUserSingImageResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSingImageResp.Size(m)
}
func (m *GetUserSingImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingImageResp proto.InternalMessageInfo

func (m *GetUserSingImageResp) GetImageIdList() []string {
	if m != nil {
		return m.ImageIdList
	}
	return nil
}

func (m *GetUserSingImageResp) GetUserSetImageId() string {
	if m != nil {
		return m.UserSetImageId
	}
	return ""
}

func (m *GetUserSingImageResp) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *GetUserSingImageResp) GetDecorationEndTs() uint32 {
	if m != nil {
		return m.DecorationEndTs
	}
	return 0
}

func (m *GetUserSingImageResp) GetLockImages() []*GetUserSingImageResp_LockImage {
	if m != nil {
		return m.LockImages
	}
	return nil
}

func (m *GetUserSingImageResp) GetIsPrecentor() bool {
	if m != nil {
		return m.IsPrecentor
	}
	return false
}

type GetUserSingImageResp_LockImage struct {
	ImageId              string   `protobuf:"bytes,1,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	Tip                  string   `protobuf:"bytes,2,opt,name=tip,proto3" json:"tip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSingImageResp_LockImage) Reset()         { *m = GetUserSingImageResp_LockImage{} }
func (m *GetUserSingImageResp_LockImage) String() string { return proto.CompactTextString(m) }
func (*GetUserSingImageResp_LockImage) ProtoMessage()    {}
func (*GetUserSingImageResp_LockImage) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{57, 0}
}
func (m *GetUserSingImageResp_LockImage) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingImageResp_LockImage.Unmarshal(m, b)
}
func (m *GetUserSingImageResp_LockImage) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingImageResp_LockImage.Marshal(b, m, deterministic)
}
func (dst *GetUserSingImageResp_LockImage) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingImageResp_LockImage.Merge(dst, src)
}
func (m *GetUserSingImageResp_LockImage) XXX_Size() int {
	return xxx_messageInfo_GetUserSingImageResp_LockImage.Size(m)
}
func (m *GetUserSingImageResp_LockImage) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingImageResp_LockImage.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingImageResp_LockImage proto.InternalMessageInfo

func (m *GetUserSingImageResp_LockImage) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *GetUserSingImageResp_LockImage) GetTip() string {
	if m != nil {
		return m.Tip
	}
	return ""
}

// 解锁用户的形象
type AwardUserSingImageReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ImageId              string   `protobuf:"bytes,2,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardUserSingImageReq) Reset()         { *m = AwardUserSingImageReq{} }
func (m *AwardUserSingImageReq) String() string { return proto.CompactTextString(m) }
func (*AwardUserSingImageReq) ProtoMessage()    {}
func (*AwardUserSingImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{58}
}
func (m *AwardUserSingImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardUserSingImageReq.Unmarshal(m, b)
}
func (m *AwardUserSingImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardUserSingImageReq.Marshal(b, m, deterministic)
}
func (dst *AwardUserSingImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardUserSingImageReq.Merge(dst, src)
}
func (m *AwardUserSingImageReq) XXX_Size() int {
	return xxx_messageInfo_AwardUserSingImageReq.Size(m)
}
func (m *AwardUserSingImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardUserSingImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_AwardUserSingImageReq proto.InternalMessageInfo

func (m *AwardUserSingImageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AwardUserSingImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

type AwardUserSingImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardUserSingImageResp) Reset()         { *m = AwardUserSingImageResp{} }
func (m *AwardUserSingImageResp) String() string { return proto.CompactTextString(m) }
func (*AwardUserSingImageResp) ProtoMessage()    {}
func (*AwardUserSingImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{59}
}
func (m *AwardUserSingImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardUserSingImageResp.Unmarshal(m, b)
}
func (m *AwardUserSingImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardUserSingImageResp.Marshal(b, m, deterministic)
}
func (dst *AwardUserSingImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardUserSingImageResp.Merge(dst, src)
}
func (m *AwardUserSingImageResp) XXX_Size() int {
	return xxx_messageInfo_AwardUserSingImageResp.Size(m)
}
func (m *AwardUserSingImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardUserSingImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_AwardUserSingImageResp proto.InternalMessageInfo

type UserDecorationInfo struct {
	DecorationId         string   `protobuf:"bytes,1,opt,name=decoration_id,json=decorationId,proto3" json:"decoration_id,omitempty"`
	StartTs              uint32   `protobuf:"varint,2,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,3,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserDecorationInfo) Reset()         { *m = UserDecorationInfo{} }
func (m *UserDecorationInfo) String() string { return proto.CompactTextString(m) }
func (*UserDecorationInfo) ProtoMessage()    {}
func (*UserDecorationInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{60}
}
func (m *UserDecorationInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDecorationInfo.Unmarshal(m, b)
}
func (m *UserDecorationInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDecorationInfo.Marshal(b, m, deterministic)
}
func (dst *UserDecorationInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDecorationInfo.Merge(dst, src)
}
func (m *UserDecorationInfo) XXX_Size() int {
	return xxx_messageInfo_UserDecorationInfo.Size(m)
}
func (m *UserDecorationInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDecorationInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserDecorationInfo proto.InternalMessageInfo

func (m *UserDecorationInfo) GetDecorationId() string {
	if m != nil {
		return m.DecorationId
	}
	return ""
}

func (m *UserDecorationInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *UserDecorationInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type PrecentorTag struct {
	StartTs              uint32   `protobuf:"varint,1,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PrecentorTag) Reset()         { *m = PrecentorTag{} }
func (m *PrecentorTag) String() string { return proto.CompactTextString(m) }
func (*PrecentorTag) ProtoMessage()    {}
func (*PrecentorTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{61}
}
func (m *PrecentorTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PrecentorTag.Unmarshal(m, b)
}
func (m *PrecentorTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PrecentorTag.Marshal(b, m, deterministic)
}
func (dst *PrecentorTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PrecentorTag.Merge(dst, src)
}
func (m *PrecentorTag) XXX_Size() int {
	return xxx_messageInfo_PrecentorTag.Size(m)
}
func (m *PrecentorTag) XXX_DiscardUnknown() {
	xxx_messageInfo_PrecentorTag.DiscardUnknown(m)
}

var xxx_messageInfo_PrecentorTag proto.InternalMessageInfo

func (m *PrecentorTag) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *PrecentorTag) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type SetUserSingImageReq struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ImageId              string              `protobuf:"bytes,2,opt,name=image_id,json=imageId,proto3" json:"image_id,omitempty"`
	Decoration           *UserDecorationInfo `protobuf:"bytes,3,opt,name=decoration,proto3" json:"decoration,omitempty"`
	ChannelId            uint32              `protobuf:"varint,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PrecentorTag         *PrecentorTag       `protobuf:"bytes,5,opt,name=precentor_tag,json=precentorTag,proto3" json:"precentor_tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *SetUserSingImageReq) Reset()         { *m = SetUserSingImageReq{} }
func (m *SetUserSingImageReq) String() string { return proto.CompactTextString(m) }
func (*SetUserSingImageReq) ProtoMessage()    {}
func (*SetUserSingImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{62}
}
func (m *SetUserSingImageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSingImageReq.Unmarshal(m, b)
}
func (m *SetUserSingImageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSingImageReq.Marshal(b, m, deterministic)
}
func (dst *SetUserSingImageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSingImageReq.Merge(dst, src)
}
func (m *SetUserSingImageReq) XXX_Size() int {
	return xxx_messageInfo_SetUserSingImageReq.Size(m)
}
func (m *SetUserSingImageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSingImageReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSingImageReq proto.InternalMessageInfo

func (m *SetUserSingImageReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserSingImageReq) GetImageId() string {
	if m != nil {
		return m.ImageId
	}
	return ""
}

func (m *SetUserSingImageReq) GetDecoration() *UserDecorationInfo {
	if m != nil {
		return m.Decoration
	}
	return nil
}

func (m *SetUserSingImageReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUserSingImageReq) GetPrecentorTag() *PrecentorTag {
	if m != nil {
		return m.PrecentorTag
	}
	return nil
}

type SetUserSingImageResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSingImageResp) Reset()         { *m = SetUserSingImageResp{} }
func (m *SetUserSingImageResp) String() string { return proto.CompactTextString(m) }
func (*SetUserSingImageResp) ProtoMessage()    {}
func (*SetUserSingImageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{63}
}
func (m *SetUserSingImageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSingImageResp.Unmarshal(m, b)
}
func (m *SetUserSingImageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSingImageResp.Marshal(b, m, deterministic)
}
func (dst *SetUserSingImageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSingImageResp.Merge(dst, src)
}
func (m *SetUserSingImageResp) XXX_Size() int {
	return xxx_messageInfo_SetUserSingImageResp.Size(m)
}
func (m *SetUserSingImageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSingImageResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSingImageResp proto.InternalMessageInfo

type BatchAwardUserReq struct {
	Uids []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	// Types that are valid to be assigned to Award:
	//	*BatchAwardUserReq_ImageId
	//	*BatchAwardUserReq_Decoration
	//	*BatchAwardUserReq_PrecentorTag
	Award                isBatchAwardUserReq_Award `protobuf_oneof:"award"`
	Day                  uint32                    `protobuf:"varint,5,opt,name=day,proto3" json:"day,omitempty"`
	Operator             string                    `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatchAwardUserReq) Reset()         { *m = BatchAwardUserReq{} }
func (m *BatchAwardUserReq) String() string { return proto.CompactTextString(m) }
func (*BatchAwardUserReq) ProtoMessage()    {}
func (*BatchAwardUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{64}
}
func (m *BatchAwardUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAwardUserReq.Unmarshal(m, b)
}
func (m *BatchAwardUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAwardUserReq.Marshal(b, m, deterministic)
}
func (dst *BatchAwardUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAwardUserReq.Merge(dst, src)
}
func (m *BatchAwardUserReq) XXX_Size() int {
	return xxx_messageInfo_BatchAwardUserReq.Size(m)
}
func (m *BatchAwardUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAwardUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAwardUserReq proto.InternalMessageInfo

func (m *BatchAwardUserReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type isBatchAwardUserReq_Award interface {
	isBatchAwardUserReq_Award()
}

type BatchAwardUserReq_ImageId struct {
	ImageId string `protobuf:"bytes,2,opt,name=image_id,json=imageId,proto3,oneof"`
}

type BatchAwardUserReq_Decoration struct {
	Decoration *UserDecorationInfo `protobuf:"bytes,3,opt,name=decoration,proto3,oneof"`
}

type BatchAwardUserReq_PrecentorTag struct {
	PrecentorTag *PrecentorTag `protobuf:"bytes,4,opt,name=precentor_tag,json=precentorTag,proto3,oneof"`
}

func (*BatchAwardUserReq_ImageId) isBatchAwardUserReq_Award() {}

func (*BatchAwardUserReq_Decoration) isBatchAwardUserReq_Award() {}

func (*BatchAwardUserReq_PrecentorTag) isBatchAwardUserReq_Award() {}

func (m *BatchAwardUserReq) GetAward() isBatchAwardUserReq_Award {
	if m != nil {
		return m.Award
	}
	return nil
}

func (m *BatchAwardUserReq) GetImageId() string {
	if x, ok := m.GetAward().(*BatchAwardUserReq_ImageId); ok {
		return x.ImageId
	}
	return ""
}

func (m *BatchAwardUserReq) GetDecoration() *UserDecorationInfo {
	if x, ok := m.GetAward().(*BatchAwardUserReq_Decoration); ok {
		return x.Decoration
	}
	return nil
}

func (m *BatchAwardUserReq) GetPrecentorTag() *PrecentorTag {
	if x, ok := m.GetAward().(*BatchAwardUserReq_PrecentorTag); ok {
		return x.PrecentorTag
	}
	return nil
}

func (m *BatchAwardUserReq) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *BatchAwardUserReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*BatchAwardUserReq) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _BatchAwardUserReq_OneofMarshaler, _BatchAwardUserReq_OneofUnmarshaler, _BatchAwardUserReq_OneofSizer, []interface{}{
		(*BatchAwardUserReq_ImageId)(nil),
		(*BatchAwardUserReq_Decoration)(nil),
		(*BatchAwardUserReq_PrecentorTag)(nil),
	}
}

func _BatchAwardUserReq_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*BatchAwardUserReq)
	// award
	switch x := m.Award.(type) {
	case *BatchAwardUserReq_ImageId:
		b.EncodeVarint(2<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.ImageId)
	case *BatchAwardUserReq_Decoration:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Decoration); err != nil {
			return err
		}
	case *BatchAwardUserReq_PrecentorTag:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PrecentorTag); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("BatchAwardUserReq.Award has unexpected type %T", x)
	}
	return nil
}

func _BatchAwardUserReq_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*BatchAwardUserReq)
	switch tag {
	case 2: // award.image_id
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Award = &BatchAwardUserReq_ImageId{x}
		return true, err
	case 3: // award.decoration
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(UserDecorationInfo)
		err := b.DecodeMessage(msg)
		m.Award = &BatchAwardUserReq_Decoration{msg}
		return true, err
	case 4: // award.precentor_tag
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PrecentorTag)
		err := b.DecodeMessage(msg)
		m.Award = &BatchAwardUserReq_PrecentorTag{msg}
		return true, err
	default:
		return false, nil
	}
}

func _BatchAwardUserReq_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*BatchAwardUserReq)
	// award
	switch x := m.Award.(type) {
	case *BatchAwardUserReq_ImageId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.ImageId)))
		n += len(x.ImageId)
	case *BatchAwardUserReq_Decoration:
		s := proto.Size(x.Decoration)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *BatchAwardUserReq_PrecentorTag:
		s := proto.Size(x.PrecentorTag)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type BatchAwardUserResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAwardUserResp) Reset()         { *m = BatchAwardUserResp{} }
func (m *BatchAwardUserResp) String() string { return proto.CompactTextString(m) }
func (*BatchAwardUserResp) ProtoMessage()    {}
func (*BatchAwardUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{65}
}
func (m *BatchAwardUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAwardUserResp.Unmarshal(m, b)
}
func (m *BatchAwardUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAwardUserResp.Marshal(b, m, deterministic)
}
func (dst *BatchAwardUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAwardUserResp.Merge(dst, src)
}
func (m *BatchAwardUserResp) XXX_Size() int {
	return xxx_messageInfo_BatchAwardUserResp.Size(m)
}
func (m *BatchAwardUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAwardUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAwardUserResp proto.InternalMessageInfo

type GetAwardRecordReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardRecordReq) Reset()         { *m = GetAwardRecordReq{} }
func (m *GetAwardRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardRecordReq) ProtoMessage()    {}
func (*GetAwardRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{66}
}
func (m *GetAwardRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardRecordReq.Unmarshal(m, b)
}
func (m *GetAwardRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardRecordReq.Merge(dst, src)
}
func (m *GetAwardRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardRecordReq.Size(m)
}
func (m *GetAwardRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardRecordReq proto.InternalMessageInfo

func (m *GetAwardRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAwardRecordReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetAwardRecordResp struct {
	TotalCount           uint32         `protobuf:"varint,1,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	Records              []*AwardRecord `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAwardRecordResp) Reset()         { *m = GetAwardRecordResp{} }
func (m *GetAwardRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardRecordResp) ProtoMessage()    {}
func (*GetAwardRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{67}
}
func (m *GetAwardRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardRecordResp.Unmarshal(m, b)
}
func (m *GetAwardRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardRecordResp.Merge(dst, src)
}
func (m *GetAwardRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardRecordResp.Size(m)
}
func (m *GetAwardRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardRecordResp proto.InternalMessageInfo

func (m *GetAwardRecordResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetAwardRecordResp) GetRecords() []*AwardRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type AwardRecord struct {
	Ttid                 string    `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	AwardType            AwardType `protobuf:"varint,2,opt,name=award_type,json=awardType,proto3,enum=singaround.AwardType" json:"award_type,omitempty"`
	Award                string    `protobuf:"bytes,3,opt,name=award,proto3" json:"award,omitempty"`
	AwardedAt            uint32    `protobuf:"varint,4,opt,name=awarded_at,json=awardedAt,proto3" json:"awarded_at,omitempty"`
	ExpiredAt            uint32    `protobuf:"varint,5,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	Operator             string    `protobuf:"bytes,6,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *AwardRecord) Reset()         { *m = AwardRecord{} }
func (m *AwardRecord) String() string { return proto.CompactTextString(m) }
func (*AwardRecord) ProtoMessage()    {}
func (*AwardRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{68}
}
func (m *AwardRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardRecord.Unmarshal(m, b)
}
func (m *AwardRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardRecord.Marshal(b, m, deterministic)
}
func (dst *AwardRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardRecord.Merge(dst, src)
}
func (m *AwardRecord) XXX_Size() int {
	return xxx_messageInfo_AwardRecord.Size(m)
}
func (m *AwardRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardRecord.DiscardUnknown(m)
}

var xxx_messageInfo_AwardRecord proto.InternalMessageInfo

func (m *AwardRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *AwardRecord) GetAwardType() AwardType {
	if m != nil {
		return m.AwardType
	}
	return AwardType_AwardType_Undefined
}

func (m *AwardRecord) GetAward() string {
	if m != nil {
		return m.Award
	}
	return ""
}

func (m *AwardRecord) GetAwardedAt() uint32 {
	if m != nil {
		return m.AwardedAt
	}
	return 0
}

func (m *AwardRecord) GetExpiredAt() uint32 {
	if m != nil {
		return m.ExpiredAt
	}
	return 0
}

func (m *AwardRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type UpdateSingImageConfReq struct {
	UserImageConfList    []*UserImageConf  `protobuf:"bytes,1,rep,name=user_image_conf_list,json=userImageConfList,proto3" json:"user_image_conf_list,omitempty"`
	DecorationList       []*DecorationInfo `protobuf:"bytes,2,rep,name=decoration_list,json=decorationList,proto3" json:"decoration_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateSingImageConfReq) Reset()         { *m = UpdateSingImageConfReq{} }
func (m *UpdateSingImageConfReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSingImageConfReq) ProtoMessage()    {}
func (*UpdateSingImageConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{69}
}
func (m *UpdateSingImageConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSingImageConfReq.Unmarshal(m, b)
}
func (m *UpdateSingImageConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSingImageConfReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSingImageConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSingImageConfReq.Merge(dst, src)
}
func (m *UpdateSingImageConfReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSingImageConfReq.Size(m)
}
func (m *UpdateSingImageConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSingImageConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSingImageConfReq proto.InternalMessageInfo

func (m *UpdateSingImageConfReq) GetUserImageConfList() []*UserImageConf {
	if m != nil {
		return m.UserImageConfList
	}
	return nil
}

func (m *UpdateSingImageConfReq) GetDecorationList() []*DecorationInfo {
	if m != nil {
		return m.DecorationList
	}
	return nil
}

type UpdateSingImageConfResp struct {
	FailedList           []string `protobuf:"bytes,1,rep,name=failed_list,json=failedList,proto3" json:"failed_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSingImageConfResp) Reset()         { *m = UpdateSingImageConfResp{} }
func (m *UpdateSingImageConfResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSingImageConfResp) ProtoMessage()    {}
func (*UpdateSingImageConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{70}
}
func (m *UpdateSingImageConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSingImageConfResp.Unmarshal(m, b)
}
func (m *UpdateSingImageConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSingImageConfResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSingImageConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSingImageConfResp.Merge(dst, src)
}
func (m *UpdateSingImageConfResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSingImageConfResp.Size(m)
}
func (m *UpdateSingImageConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSingImageConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSingImageConfResp proto.InternalMessageInfo

func (m *UpdateSingImageConfResp) GetFailedList() []string {
	if m != nil {
		return m.FailedList
	}
	return nil
}

type DelSingImageConfReq struct {
	ImageIdList          []string `protobuf:"bytes,1,rep,name=image_id_list,json=imageIdList,proto3" json:"image_id_list,omitempty"`
	DecorationIdList     []string `protobuf:"bytes,2,rep,name=decoration_id_list,json=decorationIdList,proto3" json:"decoration_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSingImageConfReq) Reset()         { *m = DelSingImageConfReq{} }
func (m *DelSingImageConfReq) String() string { return proto.CompactTextString(m) }
func (*DelSingImageConfReq) ProtoMessage()    {}
func (*DelSingImageConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{71}
}
func (m *DelSingImageConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSingImageConfReq.Unmarshal(m, b)
}
func (m *DelSingImageConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSingImageConfReq.Marshal(b, m, deterministic)
}
func (dst *DelSingImageConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSingImageConfReq.Merge(dst, src)
}
func (m *DelSingImageConfReq) XXX_Size() int {
	return xxx_messageInfo_DelSingImageConfReq.Size(m)
}
func (m *DelSingImageConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSingImageConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSingImageConfReq proto.InternalMessageInfo

func (m *DelSingImageConfReq) GetImageIdList() []string {
	if m != nil {
		return m.ImageIdList
	}
	return nil
}

func (m *DelSingImageConfReq) GetDecorationIdList() []string {
	if m != nil {
		return m.DecorationIdList
	}
	return nil
}

type DelSingImageConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSingImageConfResp) Reset()         { *m = DelSingImageConfResp{} }
func (m *DelSingImageConfResp) String() string { return proto.CompactTextString(m) }
func (*DelSingImageConfResp) ProtoMessage()    {}
func (*DelSingImageConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{72}
}
func (m *DelSingImageConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSingImageConfResp.Unmarshal(m, b)
}
func (m *DelSingImageConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSingImageConfResp.Marshal(b, m, deterministic)
}
func (dst *DelSingImageConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSingImageConfResp.Merge(dst, src)
}
func (m *DelSingImageConfResp) XXX_Size() int {
	return xxx_messageInfo_DelSingImageConfResp.Size(m)
}
func (m *DelSingImageConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSingImageConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSingImageConfResp proto.InternalMessageInfo

type UpdateConfVersionReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateConfVersionReq) Reset()         { *m = UpdateConfVersionReq{} }
func (m *UpdateConfVersionReq) String() string { return proto.CompactTextString(m) }
func (*UpdateConfVersionReq) ProtoMessage()    {}
func (*UpdateConfVersionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{73}
}
func (m *UpdateConfVersionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConfVersionReq.Unmarshal(m, b)
}
func (m *UpdateConfVersionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConfVersionReq.Marshal(b, m, deterministic)
}
func (dst *UpdateConfVersionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConfVersionReq.Merge(dst, src)
}
func (m *UpdateConfVersionReq) XXX_Size() int {
	return xxx_messageInfo_UpdateConfVersionReq.Size(m)
}
func (m *UpdateConfVersionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConfVersionReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConfVersionReq proto.InternalMessageInfo

type UpdateConfVersionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateConfVersionResp) Reset()         { *m = UpdateConfVersionResp{} }
func (m *UpdateConfVersionResp) String() string { return proto.CompactTextString(m) }
func (*UpdateConfVersionResp) ProtoMessage()    {}
func (*UpdateConfVersionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{74}
}
func (m *UpdateConfVersionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConfVersionResp.Unmarshal(m, b)
}
func (m *UpdateConfVersionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConfVersionResp.Marshal(b, m, deterministic)
}
func (dst *UpdateConfVersionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConfVersionResp.Merge(dst, src)
}
func (m *UpdateConfVersionResp) XXX_Size() int {
	return xxx_messageInfo_UpdateConfVersionResp.Size(m)
}
func (m *UpdateConfVersionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConfVersionResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConfVersionResp proto.InternalMessageInfo

// 歌曲管理
type SongResInfo struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongResInfo) Reset()         { *m = SongResInfo{} }
func (m *SongResInfo) String() string { return proto.CompactTextString(m) }
func (*SongResInfo) ProtoMessage()    {}
func (*SongResInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{75}
}
func (m *SongResInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongResInfo.Unmarshal(m, b)
}
func (m *SongResInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongResInfo.Marshal(b, m, deterministic)
}
func (dst *SongResInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongResInfo.Merge(dst, src)
}
func (m *SongResInfo) XXX_Size() int {
	return xxx_messageInfo_SongResInfo.Size(m)
}
func (m *SongResInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongResInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SongResInfo proto.InternalMessageInfo

type SongInfo struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SongName             string   `protobuf:"bytes,2,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Singer               string   `protobuf:"bytes,3,opt,name=singer,proto3" json:"singer,omitempty"`
	Lyricist             string   `protobuf:"bytes,4,opt,name=lyricist,proto3" json:"lyricist,omitempty"`
	Composer             string   `protobuf:"bytes,5,opt,name=composer,proto3" json:"composer,omitempty"`
	OriginSinger         string   `protobuf:"bytes,6,opt,name=origin_singer,json=originSinger,proto3" json:"origin_singer,omitempty"`
	UploadUser           string   `protobuf:"bytes,7,opt,name=upload_user,json=uploadUser,proto3" json:"upload_user,omitempty"`
	UploadUid            uint32   `protobuf:"varint,12,opt,name=upload_uid,json=uploadUid,proto3" json:"upload_uid,omitempty"`
	UploadTtid           string   `protobuf:"bytes,13,opt,name=upload_ttid,json=uploadTtid,proto3" json:"upload_ttid,omitempty"`
	Gender               uint32   `protobuf:"varint,8,opt,name=gender,proto3" json:"gender,omitempty"`
	LibType              uint32   `protobuf:"varint,9,opt,name=lib_type,json=libType,proto3" json:"lib_type,omitempty"`
	FileUrl              string   `protobuf:"bytes,10,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	Md5                  string   `protobuf:"bytes,11,opt,name=md5,proto3" json:"md5,omitempty"`
	SectionId            uint32   `protobuf:"varint,14,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	StyleId              string   `protobuf:"bytes,15,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongInfo) Reset()         { *m = SongInfo{} }
func (m *SongInfo) String() string { return proto.CompactTextString(m) }
func (*SongInfo) ProtoMessage()    {}
func (*SongInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{76}
}
func (m *SongInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongInfo.Unmarshal(m, b)
}
func (m *SongInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongInfo.Marshal(b, m, deterministic)
}
func (dst *SongInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongInfo.Merge(dst, src)
}
func (m *SongInfo) XXX_Size() int {
	return xxx_messageInfo_SongInfo.Size(m)
}
func (m *SongInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SongInfo proto.InternalMessageInfo

func (m *SongInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *SongInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongInfo) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *SongInfo) GetLyricist() string {
	if m != nil {
		return m.Lyricist
	}
	return ""
}

func (m *SongInfo) GetComposer() string {
	if m != nil {
		return m.Composer
	}
	return ""
}

func (m *SongInfo) GetOriginSinger() string {
	if m != nil {
		return m.OriginSinger
	}
	return ""
}

func (m *SongInfo) GetUploadUser() string {
	if m != nil {
		return m.UploadUser
	}
	return ""
}

func (m *SongInfo) GetUploadUid() uint32 {
	if m != nil {
		return m.UploadUid
	}
	return 0
}

func (m *SongInfo) GetUploadTtid() string {
	if m != nil {
		return m.UploadTtid
	}
	return ""
}

func (m *SongInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *SongInfo) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

func (m *SongInfo) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *SongInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *SongInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

func (m *SongInfo) GetStyleId() string {
	if m != nil {
		return m.StyleId
	}
	return ""
}

type CheckSongExistReq struct {
	SongList             []*SongInfo `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckSongExistReq) Reset()         { *m = CheckSongExistReq{} }
func (m *CheckSongExistReq) String() string { return proto.CompactTextString(m) }
func (*CheckSongExistReq) ProtoMessage()    {}
func (*CheckSongExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{77}
}
func (m *CheckSongExistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSongExistReq.Unmarshal(m, b)
}
func (m *CheckSongExistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSongExistReq.Marshal(b, m, deterministic)
}
func (dst *CheckSongExistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSongExistReq.Merge(dst, src)
}
func (m *CheckSongExistReq) XXX_Size() int {
	return xxx_messageInfo_CheckSongExistReq.Size(m)
}
func (m *CheckSongExistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSongExistReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSongExistReq proto.InternalMessageInfo

func (m *CheckSongExistReq) GetSongList() []*SongInfo {
	if m != nil {
		return m.SongList
	}
	return nil
}

type CheckSongExistResp struct {
	ExistList            []*SongInfo `protobuf:"bytes,1,rep,name=exist_list,json=existList,proto3" json:"exist_list,omitempty"`
	NonExistList         []*SongInfo `protobuf:"bytes,2,rep,name=non_exist_list,json=nonExistList,proto3" json:"non_exist_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *CheckSongExistResp) Reset()         { *m = CheckSongExistResp{} }
func (m *CheckSongExistResp) String() string { return proto.CompactTextString(m) }
func (*CheckSongExistResp) ProtoMessage()    {}
func (*CheckSongExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{78}
}
func (m *CheckSongExistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSongExistResp.Unmarshal(m, b)
}
func (m *CheckSongExistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSongExistResp.Marshal(b, m, deterministic)
}
func (dst *CheckSongExistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSongExistResp.Merge(dst, src)
}
func (m *CheckSongExistResp) XXX_Size() int {
	return xxx_messageInfo_CheckSongExistResp.Size(m)
}
func (m *CheckSongExistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSongExistResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSongExistResp proto.InternalMessageInfo

func (m *CheckSongExistResp) GetExistList() []*SongInfo {
	if m != nil {
		return m.ExistList
	}
	return nil
}

func (m *CheckSongExistResp) GetNonExistList() []*SongInfo {
	if m != nil {
		return m.NonExistList
	}
	return nil
}

type SetSongBaseInfoReq struct {
	Infos                []*SongInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetSongBaseInfoReq) Reset()         { *m = SetSongBaseInfoReq{} }
func (m *SetSongBaseInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetSongBaseInfoReq) ProtoMessage()    {}
func (*SetSongBaseInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{79}
}
func (m *SetSongBaseInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSongBaseInfoReq.Unmarshal(m, b)
}
func (m *SetSongBaseInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSongBaseInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetSongBaseInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSongBaseInfoReq.Merge(dst, src)
}
func (m *SetSongBaseInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetSongBaseInfoReq.Size(m)
}
func (m *SetSongBaseInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSongBaseInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSongBaseInfoReq proto.InternalMessageInfo

func (m *SetSongBaseInfoReq) GetInfos() []*SongInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type SetSongBaseInfoResp struct {
	DuplicativeNameList  []string `protobuf:"bytes,1,rep,name=duplicative_name_list,json=duplicativeNameList,proto3" json:"duplicative_name_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSongBaseInfoResp) Reset()         { *m = SetSongBaseInfoResp{} }
func (m *SetSongBaseInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetSongBaseInfoResp) ProtoMessage()    {}
func (*SetSongBaseInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{80}
}
func (m *SetSongBaseInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSongBaseInfoResp.Unmarshal(m, b)
}
func (m *SetSongBaseInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSongBaseInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetSongBaseInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSongBaseInfoResp.Merge(dst, src)
}
func (m *SetSongBaseInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetSongBaseInfoResp.Size(m)
}
func (m *SetSongBaseInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSongBaseInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSongBaseInfoResp proto.InternalMessageInfo

func (m *SetSongBaseInfoResp) GetDuplicativeNameList() []string {
	if m != nil {
		return m.DuplicativeNameList
	}
	return nil
}

type SongFileInfo struct {
	SongName             string   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Singer               string   `protobuf:"bytes,2,opt,name=singer,proto3" json:"singer,omitempty"`
	LibType              uint32   `protobuf:"varint,3,opt,name=lib_type,json=libType,proto3" json:"lib_type,omitempty"`
	FileUrl              string   `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url,omitempty"`
	Md5                  string   `protobuf:"bytes,5,opt,name=md5,proto3" json:"md5,omitempty"`
	SectionId            uint32   `protobuf:"varint,6,opt,name=section_id,json=sectionId,proto3" json:"section_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SongFileInfo) Reset()         { *m = SongFileInfo{} }
func (m *SongFileInfo) String() string { return proto.CompactTextString(m) }
func (*SongFileInfo) ProtoMessage()    {}
func (*SongFileInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{81}
}
func (m *SongFileInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SongFileInfo.Unmarshal(m, b)
}
func (m *SongFileInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SongFileInfo.Marshal(b, m, deterministic)
}
func (dst *SongFileInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SongFileInfo.Merge(dst, src)
}
func (m *SongFileInfo) XXX_Size() int {
	return xxx_messageInfo_SongFileInfo.Size(m)
}
func (m *SongFileInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SongFileInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SongFileInfo proto.InternalMessageInfo

func (m *SongFileInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *SongFileInfo) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *SongFileInfo) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

func (m *SongFileInfo) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *SongFileInfo) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *SongFileInfo) GetSectionId() uint32 {
	if m != nil {
		return m.SectionId
	}
	return 0
}

type SetSongFileReq struct {
	SongList             []*SongFileInfo `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *SetSongFileReq) Reset()         { *m = SetSongFileReq{} }
func (m *SetSongFileReq) String() string { return proto.CompactTextString(m) }
func (*SetSongFileReq) ProtoMessage()    {}
func (*SetSongFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{82}
}
func (m *SetSongFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSongFileReq.Unmarshal(m, b)
}
func (m *SetSongFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSongFileReq.Marshal(b, m, deterministic)
}
func (dst *SetSongFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSongFileReq.Merge(dst, src)
}
func (m *SetSongFileReq) XXX_Size() int {
	return xxx_messageInfo_SetSongFileReq.Size(m)
}
func (m *SetSongFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSongFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSongFileReq proto.InternalMessageInfo

func (m *SetSongFileReq) GetSongList() []*SongFileInfo {
	if m != nil {
		return m.SongList
	}
	return nil
}

type SetSongFileResp struct {
	DuplicativeNameList  []*SongInfo `protobuf:"bytes,1,rep,name=duplicative_name_list,json=duplicativeNameList,proto3" json:"duplicative_name_list,omitempty"`
	NonExistedNameList   []*SongInfo `protobuf:"bytes,2,rep,name=non_existed_name_list,json=nonExistedNameList,proto3" json:"non_existed_name_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SetSongFileResp) Reset()         { *m = SetSongFileResp{} }
func (m *SetSongFileResp) String() string { return proto.CompactTextString(m) }
func (*SetSongFileResp) ProtoMessage()    {}
func (*SetSongFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{83}
}
func (m *SetSongFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSongFileResp.Unmarshal(m, b)
}
func (m *SetSongFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSongFileResp.Marshal(b, m, deterministic)
}
func (dst *SetSongFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSongFileResp.Merge(dst, src)
}
func (m *SetSongFileResp) XXX_Size() int {
	return xxx_messageInfo_SetSongFileResp.Size(m)
}
func (m *SetSongFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSongFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSongFileResp proto.InternalMessageInfo

func (m *SetSongFileResp) GetDuplicativeNameList() []*SongInfo {
	if m != nil {
		return m.DuplicativeNameList
	}
	return nil
}

func (m *SetSongFileResp) GetNonExistedNameList() []*SongInfo {
	if m != nil {
		return m.NonExistedNameList
	}
	return nil
}

type GetSongListReq struct {
	SongName             string                   `protobuf:"bytes,1,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Singer               string                   `protobuf:"bytes,2,opt,name=singer,proto3" json:"singer,omitempty"`
	LibType              uint32                   `protobuf:"varint,3,opt,name=lib_type,json=libType,proto3" json:"lib_type,omitempty"`
	Offset               uint32                   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32                   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	FileExist            GetSongListReq_FileExist `protobuf:"varint,6,opt,name=file_exist,json=fileExist,proto3,enum=singaround.GetSongListReq_FileExist" json:"file_exist,omitempty"`
	StyleId              string                   `protobuf:"bytes,7,opt,name=style_id,json=styleId,proto3" json:"style_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetSongListReq) Reset()         { *m = GetSongListReq{} }
func (m *GetSongListReq) String() string { return proto.CompactTextString(m) }
func (*GetSongListReq) ProtoMessage()    {}
func (*GetSongListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{84}
}
func (m *GetSongListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongListReq.Unmarshal(m, b)
}
func (m *GetSongListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongListReq.Marshal(b, m, deterministic)
}
func (dst *GetSongListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongListReq.Merge(dst, src)
}
func (m *GetSongListReq) XXX_Size() int {
	return xxx_messageInfo_GetSongListReq.Size(m)
}
func (m *GetSongListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongListReq proto.InternalMessageInfo

func (m *GetSongListReq) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *GetSongListReq) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *GetSongListReq) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

func (m *GetSongListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetSongListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSongListReq) GetFileExist() GetSongListReq_FileExist {
	if m != nil {
		return m.FileExist
	}
	return GetSongListReq_NoneSense
}

func (m *GetSongListReq) GetStyleId() string {
	if m != nil {
		return m.StyleId
	}
	return ""
}

type GetSongListResp struct {
	Infos                []*SongInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	Total                uint32      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetSongListResp) Reset()         { *m = GetSongListResp{} }
func (m *GetSongListResp) String() string { return proto.CompactTextString(m) }
func (*GetSongListResp) ProtoMessage()    {}
func (*GetSongListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{85}
}
func (m *GetSongListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongListResp.Unmarshal(m, b)
}
func (m *GetSongListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongListResp.Marshal(b, m, deterministic)
}
func (dst *GetSongListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongListResp.Merge(dst, src)
}
func (m *GetSongListResp) XXX_Size() int {
	return xxx_messageInfo_GetSongListResp.Size(m)
}
func (m *GetSongListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongListResp proto.InternalMessageInfo

func (m *GetSongListResp) GetInfos() []*SongInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

func (m *GetSongListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type DelSongReq struct {
	SongList             []*SongInfo `protobuf:"bytes,1,rep,name=song_list,json=songList,proto3" json:"song_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *DelSongReq) Reset()         { *m = DelSongReq{} }
func (m *DelSongReq) String() string { return proto.CompactTextString(m) }
func (*DelSongReq) ProtoMessage()    {}
func (*DelSongReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{86}
}
func (m *DelSongReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongReq.Unmarshal(m, b)
}
func (m *DelSongReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongReq.Marshal(b, m, deterministic)
}
func (dst *DelSongReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongReq.Merge(dst, src)
}
func (m *DelSongReq) XXX_Size() int {
	return xxx_messageInfo_DelSongReq.Size(m)
}
func (m *DelSongReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongReq proto.InternalMessageInfo

func (m *DelSongReq) GetSongList() []*SongInfo {
	if m != nil {
		return m.SongList
	}
	return nil
}

type DelSongResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongResp) Reset()         { *m = DelSongResp{} }
func (m *DelSongResp) String() string { return proto.CompactTextString(m) }
func (*DelSongResp) ProtoMessage()    {}
func (*DelSongResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{87}
}
func (m *DelSongResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongResp.Unmarshal(m, b)
}
func (m *DelSongResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongResp.Marshal(b, m, deterministic)
}
func (dst *DelSongResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongResp.Merge(dst, src)
}
func (m *DelSongResp) XXX_Size() int {
	return xxx_messageInfo_DelSongResp.Size(m)
}
func (m *DelSongResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongResp proto.InternalMessageInfo

type GetSongForAppReq struct {
	Tag                  uint32   `protobuf:"varint,1,opt,name=tag,proto3" json:"tag,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSongForAppReq) Reset()         { *m = GetSongForAppReq{} }
func (m *GetSongForAppReq) String() string { return proto.CompactTextString(m) }
func (*GetSongForAppReq) ProtoMessage()    {}
func (*GetSongForAppReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{88}
}
func (m *GetSongForAppReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongForAppReq.Unmarshal(m, b)
}
func (m *GetSongForAppReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongForAppReq.Marshal(b, m, deterministic)
}
func (dst *GetSongForAppReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongForAppReq.Merge(dst, src)
}
func (m *GetSongForAppReq) XXX_Size() int {
	return xxx_messageInfo_GetSongForAppReq.Size(m)
}
func (m *GetSongForAppReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongForAppReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongForAppReq proto.InternalMessageInfo

func (m *GetSongForAppReq) GetTag() uint32 {
	if m != nil {
		return m.Tag
	}
	return 0
}

func (m *GetSongForAppReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type GetSongForAppResp struct {
	SongIdList           []*SongInfo `protobuf:"bytes,1,rep,name=song_id_list,json=songIdList,proto3" json:"song_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetSongForAppResp) Reset()         { *m = GetSongForAppResp{} }
func (m *GetSongForAppResp) String() string { return proto.CompactTextString(m) }
func (*GetSongForAppResp) ProtoMessage()    {}
func (*GetSongForAppResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{89}
}
func (m *GetSongForAppResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSongForAppResp.Unmarshal(m, b)
}
func (m *GetSongForAppResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSongForAppResp.Marshal(b, m, deterministic)
}
func (dst *GetSongForAppResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSongForAppResp.Merge(dst, src)
}
func (m *GetSongForAppResp) XXX_Size() int {
	return xxx_messageInfo_GetSongForAppResp.Size(m)
}
func (m *GetSongForAppResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSongForAppResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSongForAppResp proto.InternalMessageInfo

func (m *GetSongForAppResp) GetSongIdList() []*SongInfo {
	if m != nil {
		return m.SongIdList
	}
	return nil
}

type UpdateSongFileReq struct {
	Infos                []*SongInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateSongFileReq) Reset()         { *m = UpdateSongFileReq{} }
func (m *UpdateSongFileReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSongFileReq) ProtoMessage()    {}
func (*UpdateSongFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{90}
}
func (m *UpdateSongFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongFileReq.Unmarshal(m, b)
}
func (m *UpdateSongFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongFileReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSongFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongFileReq.Merge(dst, src)
}
func (m *UpdateSongFileReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSongFileReq.Size(m)
}
func (m *UpdateSongFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongFileReq proto.InternalMessageInfo

func (m *UpdateSongFileReq) GetInfos() []*SongInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type UpdateSongFileResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSongFileResp) Reset()         { *m = UpdateSongFileResp{} }
func (m *UpdateSongFileResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSongFileResp) ProtoMessage()    {}
func (*UpdateSongFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{91}
}
func (m *UpdateSongFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSongFileResp.Unmarshal(m, b)
}
func (m *UpdateSongFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSongFileResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSongFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSongFileResp.Merge(dst, src)
}
func (m *UpdateSongFileResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSongFileResp.Size(m)
}
func (m *UpdateSongFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSongFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSongFileResp proto.InternalMessageInfo

type SingingGameInfo struct {
	SingingGameType      uint32   `protobuf:"varint,1,opt,name=singing_game_type,json=singingGameType,proto3" json:"singing_game_type,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Descr                string   `protobuf:"bytes,3,opt,name=descr,proto3" json:"descr,omitempty"`
	ZoneType             uint32   `protobuf:"varint,4,opt,name=zone_type,json=zoneType,proto3" json:"zone_type,omitempty"`
	Hp                   uint32   `protobuf:"varint,5,opt,name=hp,proto3" json:"hp,omitempty"`
	SameSexScore         uint32   `protobuf:"varint,6,opt,name=same_sex_score,json=sameSexScore,proto3" json:"same_sex_score,omitempty"`
	OppositeSexScore     uint32   `protobuf:"varint,7,opt,name=opposite_sex_score,json=oppositeSexScore,proto3" json:"opposite_sex_score,omitempty"`
	Rank                 uint32   `protobuf:"varint,8,opt,name=rank,proto3" json:"rank,omitempty"`
	RankTime             uint32   `protobuf:"varint,9,opt,name=rank_time,json=rankTime,proto3" json:"rank_time,omitempty"`
	StartTime            uint32   `protobuf:"varint,10,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,11,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	InletIcon            string   `protobuf:"bytes,12,opt,name=inlet_icon,json=inletIcon,proto3" json:"inlet_icon,omitempty"`
	InletColor           string   `protobuf:"bytes,13,opt,name=inlet_color,json=inletColor,proto3" json:"inlet_color,omitempty"`
	ReadyCover           string   `protobuf:"bytes,14,opt,name=ready_cover,json=readyCover,proto3" json:"ready_cover,omitempty"`
	ZoneButton           string   `protobuf:"bytes,15,opt,name=zone_button,json=zoneButton,proto3" json:"zone_button,omitempty"`
	ZoneReadyBg          string   `protobuf:"bytes,16,opt,name=zone_ready_bg,json=zoneReadyBg,proto3" json:"zone_ready_bg,omitempty"`
	ZoneBg               string   `protobuf:"bytes,17,opt,name=zone_bg,json=zoneBg,proto3" json:"zone_bg,omitempty"`
	IsOpen               bool     `protobuf:"varint,18,opt,name=is_open,json=isOpen,proto3" json:"is_open,omitempty"`
	MemberLimit          uint32   `protobuf:"varint,19,opt,name=member_limit,json=memberLimit,proto3" json:"member_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SingingGameInfo) Reset()         { *m = SingingGameInfo{} }
func (m *SingingGameInfo) String() string { return proto.CompactTextString(m) }
func (*SingingGameInfo) ProtoMessage()    {}
func (*SingingGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{92}
}
func (m *SingingGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameInfo.Unmarshal(m, b)
}
func (m *SingingGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameInfo.Marshal(b, m, deterministic)
}
func (dst *SingingGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameInfo.Merge(dst, src)
}
func (m *SingingGameInfo) XXX_Size() int {
	return xxx_messageInfo_SingingGameInfo.Size(m)
}
func (m *SingingGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameInfo proto.InternalMessageInfo

func (m *SingingGameInfo) GetSingingGameType() uint32 {
	if m != nil {
		return m.SingingGameType
	}
	return 0
}

func (m *SingingGameInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SingingGameInfo) GetDescr() string {
	if m != nil {
		return m.Descr
	}
	return ""
}

func (m *SingingGameInfo) GetZoneType() uint32 {
	if m != nil {
		return m.ZoneType
	}
	return 0
}

func (m *SingingGameInfo) GetHp() uint32 {
	if m != nil {
		return m.Hp
	}
	return 0
}

func (m *SingingGameInfo) GetSameSexScore() uint32 {
	if m != nil {
		return m.SameSexScore
	}
	return 0
}

func (m *SingingGameInfo) GetOppositeSexScore() uint32 {
	if m != nil {
		return m.OppositeSexScore
	}
	return 0
}

func (m *SingingGameInfo) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *SingingGameInfo) GetRankTime() uint32 {
	if m != nil {
		return m.RankTime
	}
	return 0
}

func (m *SingingGameInfo) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *SingingGameInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *SingingGameInfo) GetInletIcon() string {
	if m != nil {
		return m.InletIcon
	}
	return ""
}

func (m *SingingGameInfo) GetInletColor() string {
	if m != nil {
		return m.InletColor
	}
	return ""
}

func (m *SingingGameInfo) GetReadyCover() string {
	if m != nil {
		return m.ReadyCover
	}
	return ""
}

func (m *SingingGameInfo) GetZoneButton() string {
	if m != nil {
		return m.ZoneButton
	}
	return ""
}

func (m *SingingGameInfo) GetZoneReadyBg() string {
	if m != nil {
		return m.ZoneReadyBg
	}
	return ""
}

func (m *SingingGameInfo) GetZoneBg() string {
	if m != nil {
		return m.ZoneBg
	}
	return ""
}

func (m *SingingGameInfo) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *SingingGameInfo) GetMemberLimit() uint32 {
	if m != nil {
		return m.MemberLimit
	}
	return 0
}

type AddSingingGameReq struct {
	SingingGameInfo      *SingingGameInfo `protobuf:"bytes,1,opt,name=singing_game_info,json=singingGameInfo,proto3" json:"singing_game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddSingingGameReq) Reset()         { *m = AddSingingGameReq{} }
func (m *AddSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*AddSingingGameReq) ProtoMessage()    {}
func (*AddSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{93}
}
func (m *AddSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSingingGameReq.Unmarshal(m, b)
}
func (m *AddSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *AddSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSingingGameReq.Merge(dst, src)
}
func (m *AddSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_AddSingingGameReq.Size(m)
}
func (m *AddSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSingingGameReq proto.InternalMessageInfo

func (m *AddSingingGameReq) GetSingingGameInfo() *SingingGameInfo {
	if m != nil {
		return m.SingingGameInfo
	}
	return nil
}

type AddSingingGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddSingingGameResp) Reset()         { *m = AddSingingGameResp{} }
func (m *AddSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*AddSingingGameResp) ProtoMessage()    {}
func (*AddSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{94}
}
func (m *AddSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSingingGameResp.Unmarshal(m, b)
}
func (m *AddSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *AddSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSingingGameResp.Merge(dst, src)
}
func (m *AddSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_AddSingingGameResp.Size(m)
}
func (m *AddSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSingingGameResp proto.InternalMessageInfo

type UpdateSingingGameReq struct {
	SingingGameInfo      *SingingGameInfo `protobuf:"bytes,1,opt,name=singing_game_info,json=singingGameInfo,proto3" json:"singing_game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UpdateSingingGameReq) Reset()         { *m = UpdateSingingGameReq{} }
func (m *UpdateSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSingingGameReq) ProtoMessage()    {}
func (*UpdateSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{95}
}
func (m *UpdateSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSingingGameReq.Unmarshal(m, b)
}
func (m *UpdateSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSingingGameReq.Merge(dst, src)
}
func (m *UpdateSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSingingGameReq.Size(m)
}
func (m *UpdateSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSingingGameReq proto.InternalMessageInfo

func (m *UpdateSingingGameReq) GetSingingGameInfo() *SingingGameInfo {
	if m != nil {
		return m.SingingGameInfo
	}
	return nil
}

type UpdateSingingGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSingingGameResp) Reset()         { *m = UpdateSingingGameResp{} }
func (m *UpdateSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSingingGameResp) ProtoMessage()    {}
func (*UpdateSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{96}
}
func (m *UpdateSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSingingGameResp.Unmarshal(m, b)
}
func (m *UpdateSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSingingGameResp.Merge(dst, src)
}
func (m *UpdateSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSingingGameResp.Size(m)
}
func (m *UpdateSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSingingGameResp proto.InternalMessageInfo

type GetSingingGameListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameListReq) Reset()         { *m = GetSingingGameListReq{} }
func (m *GetSingingGameListReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameListReq) ProtoMessage()    {}
func (*GetSingingGameListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{97}
}
func (m *GetSingingGameListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameListReq.Unmarshal(m, b)
}
func (m *GetSingingGameListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameListReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameListReq.Merge(dst, src)
}
func (m *GetSingingGameListReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameListReq.Size(m)
}
func (m *GetSingingGameListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameListReq proto.InternalMessageInfo

type GetSingingGameListResp struct {
	SingingGameInfos     []*SingingGameInfo `protobuf:"bytes,2,rep,name=singing_game_infos,json=singingGameInfos,proto3" json:"singing_game_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetSingingGameListResp) Reset()         { *m = GetSingingGameListResp{} }
func (m *GetSingingGameListResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameListResp) ProtoMessage()    {}
func (*GetSingingGameListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{98}
}
func (m *GetSingingGameListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameListResp.Unmarshal(m, b)
}
func (m *GetSingingGameListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameListResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameListResp.Merge(dst, src)
}
func (m *GetSingingGameListResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameListResp.Size(m)
}
func (m *GetSingingGameListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameListResp proto.InternalMessageInfo

func (m *GetSingingGameListResp) GetSingingGameInfos() []*SingingGameInfo {
	if m != nil {
		return m.SingingGameInfos
	}
	return nil
}

type SetSingingGameRankReq struct {
	SingingGameTypes     []uint32 `protobuf:"varint,1,rep,packed,name=singing_game_types,json=singingGameTypes,proto3" json:"singing_game_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSingingGameRankReq) Reset()         { *m = SetSingingGameRankReq{} }
func (m *SetSingingGameRankReq) String() string { return proto.CompactTextString(m) }
func (*SetSingingGameRankReq) ProtoMessage()    {}
func (*SetSingingGameRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{99}
}
func (m *SetSingingGameRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSingingGameRankReq.Unmarshal(m, b)
}
func (m *SetSingingGameRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSingingGameRankReq.Marshal(b, m, deterministic)
}
func (dst *SetSingingGameRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSingingGameRankReq.Merge(dst, src)
}
func (m *SetSingingGameRankReq) XXX_Size() int {
	return xxx_messageInfo_SetSingingGameRankReq.Size(m)
}
func (m *SetSingingGameRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSingingGameRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetSingingGameRankReq proto.InternalMessageInfo

func (m *SetSingingGameRankReq) GetSingingGameTypes() []uint32 {
	if m != nil {
		return m.SingingGameTypes
	}
	return nil
}

type SetSingingGameRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetSingingGameRankResp) Reset()         { *m = SetSingingGameRankResp{} }
func (m *SetSingingGameRankResp) String() string { return proto.CompactTextString(m) }
func (*SetSingingGameRankResp) ProtoMessage()    {}
func (*SetSingingGameRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{100}
}
func (m *SetSingingGameRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetSingingGameRankResp.Unmarshal(m, b)
}
func (m *SetSingingGameRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetSingingGameRankResp.Marshal(b, m, deterministic)
}
func (dst *SetSingingGameRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetSingingGameRankResp.Merge(dst, src)
}
func (m *SetSingingGameRankResp) XXX_Size() int {
	return xxx_messageInfo_SetSingingGameRankResp.Size(m)
}
func (m *SetSingingGameRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetSingingGameRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetSingingGameRankResp proto.InternalMessageInfo

type DelSingingGameReq struct {
	SingingGameTypes     []uint32 `protobuf:"varint,1,rep,packed,name=singing_game_types,json=singingGameTypes,proto3" json:"singing_game_types,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSingingGameReq) Reset()         { *m = DelSingingGameReq{} }
func (m *DelSingingGameReq) String() string { return proto.CompactTextString(m) }
func (*DelSingingGameReq) ProtoMessage()    {}
func (*DelSingingGameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{101}
}
func (m *DelSingingGameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSingingGameReq.Unmarshal(m, b)
}
func (m *DelSingingGameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSingingGameReq.Marshal(b, m, deterministic)
}
func (dst *DelSingingGameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSingingGameReq.Merge(dst, src)
}
func (m *DelSingingGameReq) XXX_Size() int {
	return xxx_messageInfo_DelSingingGameReq.Size(m)
}
func (m *DelSingingGameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSingingGameReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSingingGameReq proto.InternalMessageInfo

func (m *DelSingingGameReq) GetSingingGameTypes() []uint32 {
	if m != nil {
		return m.SingingGameTypes
	}
	return nil
}

type DelSingingGameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSingingGameResp) Reset()         { *m = DelSingingGameResp{} }
func (m *DelSingingGameResp) String() string { return proto.CompactTextString(m) }
func (*DelSingingGameResp) ProtoMessage()    {}
func (*DelSingingGameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{102}
}
func (m *DelSingingGameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSingingGameResp.Unmarshal(m, b)
}
func (m *DelSingingGameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSingingGameResp.Marshal(b, m, deterministic)
}
func (dst *DelSingingGameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSingingGameResp.Merge(dst, src)
}
func (m *DelSingingGameResp) XXX_Size() int {
	return xxx_messageInfo_DelSingingGameResp.Size(m)
}
func (m *DelSingingGameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSingingGameResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSingingGameResp proto.InternalMessageInfo

type GetEnableTagsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnableTagsReq) Reset()         { *m = GetEnableTagsReq{} }
func (m *GetEnableTagsReq) String() string { return proto.CompactTextString(m) }
func (*GetEnableTagsReq) ProtoMessage()    {}
func (*GetEnableTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{103}
}
func (m *GetEnableTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnableTagsReq.Unmarshal(m, b)
}
func (m *GetEnableTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnableTagsReq.Marshal(b, m, deterministic)
}
func (dst *GetEnableTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnableTagsReq.Merge(dst, src)
}
func (m *GetEnableTagsReq) XXX_Size() int {
	return xxx_messageInfo_GetEnableTagsReq.Size(m)
}
func (m *GetEnableTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnableTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnableTagsReq proto.InternalMessageInfo

type GetEnableTagsResp struct {
	Tags                 []*Tag   `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEnableTagsResp) Reset()         { *m = GetEnableTagsResp{} }
func (m *GetEnableTagsResp) String() string { return proto.CompactTextString(m) }
func (*GetEnableTagsResp) ProtoMessage()    {}
func (*GetEnableTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{104}
}
func (m *GetEnableTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEnableTagsResp.Unmarshal(m, b)
}
func (m *GetEnableTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEnableTagsResp.Marshal(b, m, deterministic)
}
func (dst *GetEnableTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEnableTagsResp.Merge(dst, src)
}
func (m *GetEnableTagsResp) XXX_Size() int {
	return xxx_messageInfo_GetEnableTagsResp.Size(m)
}
func (m *GetEnableTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEnableTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEnableTagsResp proto.InternalMessageInfo

func (m *GetEnableTagsResp) GetTags() []*Tag {
	if m != nil {
		return m.Tags
	}
	return nil
}

type Tag struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Descr                string   `protobuf:"bytes,3,opt,name=descr,proto3" json:"descr,omitempty"`
	ZoneType             uint32   `protobuf:"varint,4,opt,name=zone_type,json=zoneType,proto3" json:"zone_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Tag) Reset()         { *m = Tag{} }
func (m *Tag) String() string { return proto.CompactTextString(m) }
func (*Tag) ProtoMessage()    {}
func (*Tag) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{105}
}
func (m *Tag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tag.Unmarshal(m, b)
}
func (m *Tag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tag.Marshal(b, m, deterministic)
}
func (dst *Tag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tag.Merge(dst, src)
}
func (m *Tag) XXX_Size() int {
	return xxx_messageInfo_Tag.Size(m)
}
func (m *Tag) XXX_DiscardUnknown() {
	xxx_messageInfo_Tag.DiscardUnknown(m)
}

var xxx_messageInfo_Tag proto.InternalMessageInfo

func (m *Tag) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *Tag) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Tag) GetDescr() string {
	if m != nil {
		return m.Descr
	}
	return ""
}

func (m *Tag) GetZoneType() uint32 {
	if m != nil {
		return m.ZoneType
	}
	return 0
}

type GetSingingGameForAppReq struct {
	CurrentGameType      uint32   `protobuf:"varint,1,opt,name=current_game_type,json=currentGameType,proto3" json:"current_game_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameForAppReq) Reset()         { *m = GetSingingGameForAppReq{} }
func (m *GetSingingGameForAppReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameForAppReq) ProtoMessage()    {}
func (*GetSingingGameForAppReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{106}
}
func (m *GetSingingGameForAppReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameForAppReq.Unmarshal(m, b)
}
func (m *GetSingingGameForAppReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameForAppReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameForAppReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameForAppReq.Merge(dst, src)
}
func (m *GetSingingGameForAppReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameForAppReq.Size(m)
}
func (m *GetSingingGameForAppReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameForAppReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameForAppReq proto.InternalMessageInfo

func (m *GetSingingGameForAppReq) GetCurrentGameType() uint32 {
	if m != nil {
		return m.CurrentGameType
	}
	return 0
}

type GetSingingGameForAppResp struct {
	SingingGameInfos     []*SingingGameInfoForApp `protobuf:"bytes,1,rep,name=singing_game_infos,json=singingGameInfos,proto3" json:"singing_game_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetSingingGameForAppResp) Reset()         { *m = GetSingingGameForAppResp{} }
func (m *GetSingingGameForAppResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameForAppResp) ProtoMessage()    {}
func (*GetSingingGameForAppResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{107}
}
func (m *GetSingingGameForAppResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameForAppResp.Unmarshal(m, b)
}
func (m *GetSingingGameForAppResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameForAppResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameForAppResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameForAppResp.Merge(dst, src)
}
func (m *GetSingingGameForAppResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameForAppResp.Size(m)
}
func (m *GetSingingGameForAppResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameForAppResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameForAppResp proto.InternalMessageInfo

func (m *GetSingingGameForAppResp) GetSingingGameInfos() []*SingingGameInfoForApp {
	if m != nil {
		return m.SingingGameInfos
	}
	return nil
}

type SpecialGameZoneInfo struct {
	Style                uint32   `protobuf:"varint,1,opt,name=style,proto3" json:"style,omitempty"`
	IsUserChannel        bool     `protobuf:"varint,2,opt,name=is_user_channel,json=isUserChannel,proto3" json:"is_user_channel,omitempty"`
	IsNeedShare          bool     `protobuf:"varint,3,opt,name=is_need_share,json=isNeedShare,proto3" json:"is_need_share,omitempty"`
	TabId                uint32   `protobuf:"varint,4,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,5,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SpecialGameZoneInfo) Reset()         { *m = SpecialGameZoneInfo{} }
func (m *SpecialGameZoneInfo) String() string { return proto.CompactTextString(m) }
func (*SpecialGameZoneInfo) ProtoMessage()    {}
func (*SpecialGameZoneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{108}
}
func (m *SpecialGameZoneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SpecialGameZoneInfo.Unmarshal(m, b)
}
func (m *SpecialGameZoneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SpecialGameZoneInfo.Marshal(b, m, deterministic)
}
func (dst *SpecialGameZoneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SpecialGameZoneInfo.Merge(dst, src)
}
func (m *SpecialGameZoneInfo) XXX_Size() int {
	return xxx_messageInfo_SpecialGameZoneInfo.Size(m)
}
func (m *SpecialGameZoneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SpecialGameZoneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SpecialGameZoneInfo proto.InternalMessageInfo

func (m *SpecialGameZoneInfo) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

func (m *SpecialGameZoneInfo) GetIsUserChannel() bool {
	if m != nil {
		return m.IsUserChannel
	}
	return false
}

func (m *SpecialGameZoneInfo) GetIsNeedShare() bool {
	if m != nil {
		return m.IsNeedShare
	}
	return false
}

func (m *SpecialGameZoneInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SpecialGameZoneInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

type SingingGameInfoForApp struct {
	Type                 uint32               `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Name                 string               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Descr                string               `protobuf:"bytes,3,opt,name=descr,proto3" json:"descr,omitempty"`
	InletIcon            string               `protobuf:"bytes,4,opt,name=inlet_icon,json=inletIcon,proto3" json:"inlet_icon,omitempty"`
	InletColor           string               `protobuf:"bytes,5,opt,name=inlet_color,json=inletColor,proto3" json:"inlet_color,omitempty"`
	ReadyCover           string               `protobuf:"bytes,6,opt,name=ready_cover,json=readyCover,proto3" json:"ready_cover,omitempty"`
	ZoneButton           string               `protobuf:"bytes,7,opt,name=zone_button,json=zoneButton,proto3" json:"zone_button,omitempty"`
	ZoneReadyBg          string               `protobuf:"bytes,8,opt,name=zone_ready_bg,json=zoneReadyBg,proto3" json:"zone_ready_bg,omitempty"`
	ZoneBg               string               `protobuf:"bytes,9,opt,name=zone_bg,json=zoneBg,proto3" json:"zone_bg,omitempty"`
	TimeLimitIcon        string               `protobuf:"bytes,10,opt,name=time_limit_icon,json=timeLimitIcon,proto3" json:"time_limit_icon,omitempty"`
	SpecialGameInfo      *SpecialGameZoneInfo `protobuf:"bytes,11,opt,name=special_game_info,json=specialGameInfo,proto3" json:"special_game_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SingingGameInfoForApp) Reset()         { *m = SingingGameInfoForApp{} }
func (m *SingingGameInfoForApp) String() string { return proto.CompactTextString(m) }
func (*SingingGameInfoForApp) ProtoMessage()    {}
func (*SingingGameInfoForApp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{109}
}
func (m *SingingGameInfoForApp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SingingGameInfoForApp.Unmarshal(m, b)
}
func (m *SingingGameInfoForApp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SingingGameInfoForApp.Marshal(b, m, deterministic)
}
func (dst *SingingGameInfoForApp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SingingGameInfoForApp.Merge(dst, src)
}
func (m *SingingGameInfoForApp) XXX_Size() int {
	return xxx_messageInfo_SingingGameInfoForApp.Size(m)
}
func (m *SingingGameInfoForApp) XXX_DiscardUnknown() {
	xxx_messageInfo_SingingGameInfoForApp.DiscardUnknown(m)
}

var xxx_messageInfo_SingingGameInfoForApp proto.InternalMessageInfo

func (m *SingingGameInfoForApp) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *SingingGameInfoForApp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *SingingGameInfoForApp) GetDescr() string {
	if m != nil {
		return m.Descr
	}
	return ""
}

func (m *SingingGameInfoForApp) GetInletIcon() string {
	if m != nil {
		return m.InletIcon
	}
	return ""
}

func (m *SingingGameInfoForApp) GetInletColor() string {
	if m != nil {
		return m.InletColor
	}
	return ""
}

func (m *SingingGameInfoForApp) GetReadyCover() string {
	if m != nil {
		return m.ReadyCover
	}
	return ""
}

func (m *SingingGameInfoForApp) GetZoneButton() string {
	if m != nil {
		return m.ZoneButton
	}
	return ""
}

func (m *SingingGameInfoForApp) GetZoneReadyBg() string {
	if m != nil {
		return m.ZoneReadyBg
	}
	return ""
}

func (m *SingingGameInfoForApp) GetZoneBg() string {
	if m != nil {
		return m.ZoneBg
	}
	return ""
}

func (m *SingingGameInfoForApp) GetTimeLimitIcon() string {
	if m != nil {
		return m.TimeLimitIcon
	}
	return ""
}

func (m *SingingGameInfoForApp) GetSpecialGameInfo() *SpecialGameZoneInfo {
	if m != nil {
		return m.SpecialGameInfo
	}
	return nil
}

type GetSingingGameRoundInfoReq struct {
	ChannelIds           []uint32 `protobuf:"varint,1,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameRoundInfoReq) Reset()         { *m = GetSingingGameRoundInfoReq{} }
func (m *GetSingingGameRoundInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRoundInfoReq) ProtoMessage()    {}
func (*GetSingingGameRoundInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{110}
}
func (m *GetSingingGameRoundInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRoundInfoReq.Unmarshal(m, b)
}
func (m *GetSingingGameRoundInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRoundInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRoundInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRoundInfoReq.Merge(dst, src)
}
func (m *GetSingingGameRoundInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRoundInfoReq.Size(m)
}
func (m *GetSingingGameRoundInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRoundInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRoundInfoReq proto.InternalMessageInfo

func (m *GetSingingGameRoundInfoReq) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

type GetSingingGameRoundInfoResp struct {
	RoundInfos           []*RoundInfo `protobuf:"bytes,1,rep,name=round_infos,json=roundInfos,proto3" json:"round_infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetSingingGameRoundInfoResp) Reset()         { *m = GetSingingGameRoundInfoResp{} }
func (m *GetSingingGameRoundInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameRoundInfoResp) ProtoMessage()    {}
func (*GetSingingGameRoundInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{111}
}
func (m *GetSingingGameRoundInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameRoundInfoResp.Unmarshal(m, b)
}
func (m *GetSingingGameRoundInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameRoundInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameRoundInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameRoundInfoResp.Merge(dst, src)
}
func (m *GetSingingGameRoundInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameRoundInfoResp.Size(m)
}
func (m *GetSingingGameRoundInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameRoundInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameRoundInfoResp proto.InternalMessageInfo

func (m *GetSingingGameRoundInfoResp) GetRoundInfos() []*RoundInfo {
	if m != nil {
		return m.RoundInfos
	}
	return nil
}

type RoundInfo struct {
	ChannelId            uint32                `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	SongIndex            uint32                `protobuf:"varint,2,opt,name=song_index,json=songIndex,proto3" json:"song_index,omitempty"`
	SongTotal            uint32                `protobuf:"varint,3,opt,name=song_total,json=songTotal,proto3" json:"song_total,omitempty"`
	SingingGameName      string                `protobuf:"bytes,4,opt,name=singing_game_name,json=singingGameName,proto3" json:"singing_game_name,omitempty"`
	Status               string                `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	SongName             string                `protobuf:"bytes,6,opt,name=song_name,json=songName,proto3" json:"song_name,omitempty"`
	Singer               string                `protobuf:"bytes,7,opt,name=singer,proto3" json:"singer,omitempty"`
	Stage                SingingGameRoundStage `protobuf:"varint,8,opt,name=stage,proto3,enum=singaround.SingingGameRoundStage" json:"stage,omitempty"`
	TagId                uint32                `protobuf:"varint,9,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *RoundInfo) Reset()         { *m = RoundInfo{} }
func (m *RoundInfo) String() string { return proto.CompactTextString(m) }
func (*RoundInfo) ProtoMessage()    {}
func (*RoundInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{112}
}
func (m *RoundInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RoundInfo.Unmarshal(m, b)
}
func (m *RoundInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RoundInfo.Marshal(b, m, deterministic)
}
func (dst *RoundInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RoundInfo.Merge(dst, src)
}
func (m *RoundInfo) XXX_Size() int {
	return xxx_messageInfo_RoundInfo.Size(m)
}
func (m *RoundInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RoundInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RoundInfo proto.InternalMessageInfo

func (m *RoundInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *RoundInfo) GetSongIndex() uint32 {
	if m != nil {
		return m.SongIndex
	}
	return 0
}

func (m *RoundInfo) GetSongTotal() uint32 {
	if m != nil {
		return m.SongTotal
	}
	return 0
}

func (m *RoundInfo) GetSingingGameName() string {
	if m != nil {
		return m.SingingGameName
	}
	return ""
}

func (m *RoundInfo) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *RoundInfo) GetSongName() string {
	if m != nil {
		return m.SongName
	}
	return ""
}

func (m *RoundInfo) GetSinger() string {
	if m != nil {
		return m.Singer
	}
	return ""
}

func (m *RoundInfo) GetStage() SingingGameRoundStage {
	if m != nil {
		return m.Stage
	}
	return SingingGameRoundStage_SingingGameRoundStageUndefined
}

func (m *RoundInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

// 低质用户行为记录
type GetLessUserReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameType             uint32   `protobuf:"varint,2,opt,name=game_type,json=gameType,proto3" json:"game_type,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLessUserReq) Reset()         { *m = GetLessUserReq{} }
func (m *GetLessUserReq) String() string { return proto.CompactTextString(m) }
func (*GetLessUserReq) ProtoMessage()    {}
func (*GetLessUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{113}
}
func (m *GetLessUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLessUserReq.Unmarshal(m, b)
}
func (m *GetLessUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLessUserReq.Marshal(b, m, deterministic)
}
func (dst *GetLessUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLessUserReq.Merge(dst, src)
}
func (m *GetLessUserReq) XXX_Size() int {
	return xxx_messageInfo_GetLessUserReq.Size(m)
}
func (m *GetLessUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLessUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetLessUserReq proto.InternalMessageInfo

func (m *GetLessUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetLessUserReq) GetGameType() uint32 {
	if m != nil {
		return m.GameType
	}
	return 0
}

func (m *GetLessUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetLessUserResp struct {
	IsLimit              bool     `protobuf:"varint,1,opt,name=is_limit,json=isLimit,proto3" json:"is_limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLessUserResp) Reset()         { *m = GetLessUserResp{} }
func (m *GetLessUserResp) String() string { return proto.CompactTextString(m) }
func (*GetLessUserResp) ProtoMessage()    {}
func (*GetLessUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{114}
}
func (m *GetLessUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLessUserResp.Unmarshal(m, b)
}
func (m *GetLessUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLessUserResp.Marshal(b, m, deterministic)
}
func (dst *GetLessUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLessUserResp.Merge(dst, src)
}
func (m *GetLessUserResp) XXX_Size() int {
	return xxx_messageInfo_GetLessUserResp.Size(m)
}
func (m *GetLessUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLessUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetLessUserResp proto.InternalMessageInfo

func (m *GetLessUserResp) GetIsLimit() bool {
	if m != nil {
		return m.IsLimit
	}
	return false
}

type GetSingingGameUserInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameUserInfoReq) Reset()         { *m = GetSingingGameUserInfoReq{} }
func (m *GetSingingGameUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameUserInfoReq) ProtoMessage()    {}
func (*GetSingingGameUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{115}
}
func (m *GetSingingGameUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameUserInfoReq.Unmarshal(m, b)
}
func (m *GetSingingGameUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameUserInfoReq.Merge(dst, src)
}
func (m *GetSingingGameUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameUserInfoReq.Size(m)
}
func (m *GetSingingGameUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameUserInfoReq proto.InternalMessageInfo

func (m *GetSingingGameUserInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSingingGameUserInfoResp struct {
	GrabMicTime          uint32   `protobuf:"varint,1,opt,name=grab_mic_time,json=grabMicTime,proto3" json:"grab_mic_time,omitempty"`
	GrabMicSuccessTime   uint32   `protobuf:"varint,2,opt,name=grab_mic_success_time,json=grabMicSuccessTime,proto3" json:"grab_mic_success_time,omitempty"`
	LikeCount            uint32   `protobuf:"varint,3,opt,name=like_count,json=likeCount,proto3" json:"like_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingingGameUserInfoResp) Reset()         { *m = GetSingingGameUserInfoResp{} }
func (m *GetSingingGameUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSingingGameUserInfoResp) ProtoMessage()    {}
func (*GetSingingGameUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_sing_a_round_2f5f025d96d12b52, []int{116}
}
func (m *GetSingingGameUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingingGameUserInfoResp.Unmarshal(m, b)
}
func (m *GetSingingGameUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingingGameUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSingingGameUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingingGameUserInfoResp.Merge(dst, src)
}
func (m *GetSingingGameUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSingingGameUserInfoResp.Size(m)
}
func (m *GetSingingGameUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingingGameUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingingGameUserInfoResp proto.InternalMessageInfo

func (m *GetSingingGameUserInfoResp) GetGrabMicTime() uint32 {
	if m != nil {
		return m.GrabMicTime
	}
	return 0
}

func (m *GetSingingGameUserInfoResp) GetGrabMicSuccessTime() uint32 {
	if m != nil {
		return m.GrabMicSuccessTime
	}
	return 0
}

func (m *GetSingingGameUserInfoResp) GetLikeCount() uint32 {
	if m != nil {
		return m.LikeCount
	}
	return 0
}

func init() {
	proto.RegisterType((*StartSingingGameMatchingReq)(nil), "singaround.StartSingingGameMatchingReq")
	proto.RegisterType((*StartSingingGameMatchingResp)(nil), "singaround.StartSingingGameMatchingResp")
	proto.RegisterType((*JoinSingingGameReq)(nil), "singaround.JoinSingingGameReq")
	proto.RegisterType((*JoinSingingGameResp)(nil), "singaround.JoinSingingGameResp")
	proto.RegisterType((*GetSingingGameCountdownReq)(nil), "singaround.GetSingingGameCountdownReq")
	proto.RegisterType((*GetSingingGameCountdownResp)(nil), "singaround.GetSingingGameCountdownResp")
	proto.RegisterType((*StartSingingGameReq)(nil), "singaround.StartSingingGameReq")
	proto.RegisterType((*StartSingingGameResp)(nil), "singaround.StartSingingGameResp")
	proto.RegisterType((*GrabSingingGameMicReq)(nil), "singaround.GrabSingingGameMicReq")
	proto.RegisterType((*GrabSingingGameMicResp)(nil), "singaround.GrabSingingGameMicResp")
	proto.RegisterType((*AskForSingingHelpReq)(nil), "singaround.AskForSingingHelpReq")
	proto.RegisterType((*AskForSingingHelpResp)(nil), "singaround.AskForSingingHelpResp")
	proto.RegisterType((*AnswerSingingHelpReq)(nil), "singaround.AnswerSingingHelpReq")
	proto.RegisterType((*AnswerSingingHelpResp)(nil), "singaround.AnswerSingingHelpResp")
	proto.RegisterType((*AccomplishSingingGameSongReq)(nil), "singaround.AccomplishSingingGameSongReq")
	proto.RegisterType((*AccomplishSingingGameSongResp)(nil), "singaround.AccomplishSingingGameSongResp")
	proto.RegisterType((*ReportSingingGameSongScoreReq)(nil), "singaround.ReportSingingGameSongScoreReq")
	proto.RegisterType((*ReportSingingGameSongScoreResp)(nil), "singaround.ReportSingingGameSongScoreResp")
	proto.RegisterType((*SingingGameSongResult)(nil), "singaround.SingingGameSongResult")
	proto.RegisterType((*SingingGameScoreDetail)(nil), "singaround.SingingGameScoreDetail")
	proto.RegisterType((*GetSingingGameChannelInfoReq)(nil), "singaround.GetSingingGameChannelInfoReq")
	proto.RegisterType((*GetSingingGameChannelInfoResp)(nil), "singaround.GetSingingGameChannelInfoResp")
	proto.RegisterType((*SwitchSingingGameTypeReq)(nil), "singaround.SwitchSingingGameTypeReq")
	proto.RegisterType((*SwitchSingingGameTypeResp)(nil), "singaround.SwitchSingingGameTypeResp")
	proto.RegisterType((*SingingGameChannelInfo)(nil), "singaround.SingingGameChannelInfo")
	proto.RegisterType((*PassThroughExtra)(nil), "singaround.PassThroughExtra")
	proto.RegisterType((*SingingGamePosition)(nil), "singaround.SingingGamePosition")
	proto.RegisterType((*SingingGameSongStyle)(nil), "singaround.SingingGameSongStyle")
	proto.RegisterType((*SingingGameVoteReq)(nil), "singaround.SingingGameVoteReq")
	proto.RegisterType((*SingingGameVoteResp)(nil), "singaround.SingingGameVoteResp")
	proto.RegisterType((*AccomplishLoadingResReq)(nil), "singaround.AccomplishLoadingResReq")
	proto.RegisterType((*AccomplishLoadingResResp)(nil), "singaround.AccomplishLoadingResResp")
	proto.RegisterType((*GetSingingGameRankListReq)(nil), "singaround.GetSingingGameRankListReq")
	proto.RegisterType((*GetSingingGameRankListResp)(nil), "singaround.GetSingingGameRankListResp")
	proto.RegisterType((*GetSingingGameRecordListReq)(nil), "singaround.GetSingingGameRecordListReq")
	proto.RegisterType((*GetSingingGameRecordListResp)(nil), "singaround.GetSingingGameRecordListResp")
	proto.RegisterType((*SingingGameRankListItem)(nil), "singaround.SingingGameRankListItem")
	proto.RegisterType((*SingingGameLoadMore)(nil), "singaround.SingingGameLoadMore")
	proto.RegisterType((*AddSongStyleReq)(nil), "singaround.AddSongStyleReq")
	proto.RegisterType((*AddSongStyleResp)(nil), "singaround.AddSongStyleResp")
	proto.RegisterType((*DelSongStyleReq)(nil), "singaround.DelSongStyleReq")
	proto.RegisterType((*DelSongStyleResp)(nil), "singaround.DelSongStyleResp")
	proto.RegisterType((*GetAllSongStyleReq)(nil), "singaround.GetAllSongStyleReq")
	proto.RegisterType((*GetAllSongStyleResp)(nil), "singaround.GetAllSongStyleResp")
	proto.RegisterType((*ExpressSingingGameLikeReq)(nil), "singaround.ExpressSingingGameLikeReq")
	proto.RegisterType((*ExpressSingingGameLikeResp)(nil), "singaround.ExpressSingingGameLikeResp")
	proto.RegisterType((*SingingGameFistBumpReq)(nil), "singaround.SingingGameFistBumpReq")
	proto.RegisterType((*SingingGameFistBumpResp)(nil), "singaround.SingingGameFistBumpResp")
	proto.RegisterType((*StartUgcChannelSingingGameReq)(nil), "singaround.StartUgcChannelSingingGameReq")
	proto.RegisterType((*StartUgcChannelSingingGameResp)(nil), "singaround.StartUgcChannelSingingGameResp")
	proto.RegisterType((*CancelSingingGamePreparationReq)(nil), "singaround.CancelSingingGamePreparationReq")
	proto.RegisterType((*CancelSingingGamePreparationResp)(nil), "singaround.CancelSingingGamePreparationResp")
	proto.RegisterType((*UserImageConf)(nil), "singaround.UserImageConf")
	proto.RegisterType((*DecorationInfo)(nil), "singaround.DecorationInfo")
	proto.RegisterType((*GetAllSingImageConfReq)(nil), "singaround.GetAllSingImageConfReq")
	proto.RegisterType((*GetAllSingImageConfResp)(nil), "singaround.GetAllSingImageConfResp")
	proto.RegisterType((*GetUserSingImageReq)(nil), "singaround.GetUserSingImageReq")
	proto.RegisterType((*GetUserSingImageResp)(nil), "singaround.GetUserSingImageResp")
	proto.RegisterType((*GetUserSingImageResp_LockImage)(nil), "singaround.GetUserSingImageResp.LockImage")
	proto.RegisterType((*AwardUserSingImageReq)(nil), "singaround.AwardUserSingImageReq")
	proto.RegisterType((*AwardUserSingImageResp)(nil), "singaround.AwardUserSingImageResp")
	proto.RegisterType((*UserDecorationInfo)(nil), "singaround.UserDecorationInfo")
	proto.RegisterType((*PrecentorTag)(nil), "singaround.PrecentorTag")
	proto.RegisterType((*SetUserSingImageReq)(nil), "singaround.SetUserSingImageReq")
	proto.RegisterType((*SetUserSingImageResp)(nil), "singaround.SetUserSingImageResp")
	proto.RegisterType((*BatchAwardUserReq)(nil), "singaround.BatchAwardUserReq")
	proto.RegisterType((*BatchAwardUserResp)(nil), "singaround.BatchAwardUserResp")
	proto.RegisterType((*GetAwardRecordReq)(nil), "singaround.GetAwardRecordReq")
	proto.RegisterType((*GetAwardRecordResp)(nil), "singaround.GetAwardRecordResp")
	proto.RegisterType((*AwardRecord)(nil), "singaround.AwardRecord")
	proto.RegisterType((*UpdateSingImageConfReq)(nil), "singaround.UpdateSingImageConfReq")
	proto.RegisterType((*UpdateSingImageConfResp)(nil), "singaround.UpdateSingImageConfResp")
	proto.RegisterType((*DelSingImageConfReq)(nil), "singaround.DelSingImageConfReq")
	proto.RegisterType((*DelSingImageConfResp)(nil), "singaround.DelSingImageConfResp")
	proto.RegisterType((*UpdateConfVersionReq)(nil), "singaround.UpdateConfVersionReq")
	proto.RegisterType((*UpdateConfVersionResp)(nil), "singaround.UpdateConfVersionResp")
	proto.RegisterType((*SongResInfo)(nil), "singaround.SongResInfo")
	proto.RegisterType((*SongInfo)(nil), "singaround.SongInfo")
	proto.RegisterType((*CheckSongExistReq)(nil), "singaround.CheckSongExistReq")
	proto.RegisterType((*CheckSongExistResp)(nil), "singaround.CheckSongExistResp")
	proto.RegisterType((*SetSongBaseInfoReq)(nil), "singaround.SetSongBaseInfoReq")
	proto.RegisterType((*SetSongBaseInfoResp)(nil), "singaround.SetSongBaseInfoResp")
	proto.RegisterType((*SongFileInfo)(nil), "singaround.SongFileInfo")
	proto.RegisterType((*SetSongFileReq)(nil), "singaround.SetSongFileReq")
	proto.RegisterType((*SetSongFileResp)(nil), "singaround.SetSongFileResp")
	proto.RegisterType((*GetSongListReq)(nil), "singaround.GetSongListReq")
	proto.RegisterType((*GetSongListResp)(nil), "singaround.GetSongListResp")
	proto.RegisterType((*DelSongReq)(nil), "singaround.DelSongReq")
	proto.RegisterType((*DelSongResp)(nil), "singaround.DelSongResp")
	proto.RegisterType((*GetSongForAppReq)(nil), "singaround.GetSongForAppReq")
	proto.RegisterType((*GetSongForAppResp)(nil), "singaround.GetSongForAppResp")
	proto.RegisterType((*UpdateSongFileReq)(nil), "singaround.UpdateSongFileReq")
	proto.RegisterType((*UpdateSongFileResp)(nil), "singaround.UpdateSongFileResp")
	proto.RegisterType((*SingingGameInfo)(nil), "singaround.SingingGameInfo")
	proto.RegisterType((*AddSingingGameReq)(nil), "singaround.AddSingingGameReq")
	proto.RegisterType((*AddSingingGameResp)(nil), "singaround.AddSingingGameResp")
	proto.RegisterType((*UpdateSingingGameReq)(nil), "singaround.UpdateSingingGameReq")
	proto.RegisterType((*UpdateSingingGameResp)(nil), "singaround.UpdateSingingGameResp")
	proto.RegisterType((*GetSingingGameListReq)(nil), "singaround.GetSingingGameListReq")
	proto.RegisterType((*GetSingingGameListResp)(nil), "singaround.GetSingingGameListResp")
	proto.RegisterType((*SetSingingGameRankReq)(nil), "singaround.SetSingingGameRankReq")
	proto.RegisterType((*SetSingingGameRankResp)(nil), "singaround.SetSingingGameRankResp")
	proto.RegisterType((*DelSingingGameReq)(nil), "singaround.DelSingingGameReq")
	proto.RegisterType((*DelSingingGameResp)(nil), "singaround.DelSingingGameResp")
	proto.RegisterType((*GetEnableTagsReq)(nil), "singaround.GetEnableTagsReq")
	proto.RegisterType((*GetEnableTagsResp)(nil), "singaround.GetEnableTagsResp")
	proto.RegisterType((*Tag)(nil), "singaround.Tag")
	proto.RegisterType((*GetSingingGameForAppReq)(nil), "singaround.GetSingingGameForAppReq")
	proto.RegisterType((*GetSingingGameForAppResp)(nil), "singaround.GetSingingGameForAppResp")
	proto.RegisterType((*SpecialGameZoneInfo)(nil), "singaround.SpecialGameZoneInfo")
	proto.RegisterType((*SingingGameInfoForApp)(nil), "singaround.SingingGameInfoForApp")
	proto.RegisterType((*GetSingingGameRoundInfoReq)(nil), "singaround.GetSingingGameRoundInfoReq")
	proto.RegisterType((*GetSingingGameRoundInfoResp)(nil), "singaround.GetSingingGameRoundInfoResp")
	proto.RegisterType((*RoundInfo)(nil), "singaround.RoundInfo")
	proto.RegisterType((*GetLessUserReq)(nil), "singaround.GetLessUserReq")
	proto.RegisterType((*GetLessUserResp)(nil), "singaround.GetLessUserResp")
	proto.RegisterType((*GetSingingGameUserInfoReq)(nil), "singaround.GetSingingGameUserInfoReq")
	proto.RegisterType((*GetSingingGameUserInfoResp)(nil), "singaround.GetSingingGameUserInfoResp")
	proto.RegisterEnum("singaround.SingingGameRoundStage", SingingGameRoundStage_name, SingingGameRoundStage_value)
	proto.RegisterEnum("singaround.SingingGameUserType", SingingGameUserType_name, SingingGameUserType_value)
	proto.RegisterEnum("singaround.RankListType", RankListType_name, RankListType_value)
	proto.RegisterEnum("singaround.SingingGameResultType", SingingGameResultType_name, SingingGameResultType_value)
	proto.RegisterEnum("singaround.SingingGameType", SingingGameType_name, SingingGameType_value)
	proto.RegisterEnum("singaround.UserImageType", UserImageType_name, UserImageType_value)
	proto.RegisterEnum("singaround.AwardType", AwardType_name, AwardType_value)
	proto.RegisterEnum("singaround.SingingZoneType", SingingZoneType_name, SingingZoneType_value)
	proto.RegisterEnum("singaround.GetSongListReq_FileExist", GetSongListReq_FileExist_name, GetSongListReq_FileExist_value)
	proto.RegisterEnum("singaround.GetLessUserReq_GameType", GetLessUserReq_GameType_name, GetLessUserReq_GameType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// SingARoundClient is the client API for SingARound service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type SingARoundClient interface {
	// 匹配
	StartSingingGameMatching(ctx context.Context, in *StartSingingGameMatchingReq, opts ...grpc.CallOption) (*StartSingingGameMatchingResp, error)
	// 加入游戏（观众加入游戏时使用）
	JoinSingingGame(ctx context.Context, in *JoinSingingGameReq, opts ...grpc.CallOption) (*JoinSingingGameResp, error)
	// 获取开始倒计时
	GetSingingGameCountdown(ctx context.Context, in *GetSingingGameCountdownReq, opts ...grpc.CallOption) (*GetSingingGameCountdownResp, error)
	// 准备（开始游戏）
	StartSingingGame(ctx context.Context, in *StartSingingGameReq, opts ...grpc.CallOption) (*StartSingingGameResp, error)
	// 抢唱
	GrabSingingGameMic(ctx context.Context, in *GrabSingingGameMicReq, opts ...grpc.CallOption) (*GrabSingingGameMicResp, error)
	// 请求帮唱
	AskForSingingHelp(ctx context.Context, in *AskForSingingHelpReq, opts ...grpc.CallOption) (*AskForSingingHelpResp, error)
	// 帮唱
	AnswerSingingHelp(ctx context.Context, in *AnswerSingingHelpReq, opts ...grpc.CallOption) (*AnswerSingingHelpResp, error)
	// 完成演唱
	AccomplishSingingGameSong(ctx context.Context, in *AccomplishSingingGameSongReq, opts ...grpc.CallOption) (*AccomplishSingingGameSongResp, error)
	// 上报积分
	ReportSingingGameSongScore(ctx context.Context, in *ReportSingingGameSongScoreReq, opts ...grpc.CallOption) (*ReportSingingGameSongScoreResp, error)
	// 用户房间状态（客户端重连时使用）
	GetSingingGameChannelInfo(ctx context.Context, in *GetSingingGameChannelInfoReq, opts ...grpc.CallOption) (*GetSingingGameChannelInfoResp, error)
	// 切换游戏类型
	SwitchSingingGameType(ctx context.Context, in *SwitchSingingGameTypeReq, opts ...grpc.CallOption) (*SwitchSingingGameTypeResp, error)
	// 牛啊
	ExpressSingingGameLike(ctx context.Context, in *ExpressSingingGameLikeReq, opts ...grpc.CallOption) (*ExpressSingingGameLikeResp, error)
	// 碰拳
	SingingGameFistBump(ctx context.Context, in *SingingGameFistBumpReq, opts ...grpc.CallOption) (*SingingGameFistBumpResp, error)
	// 开始游戏（UGC房）
	StartUgcChannelSingingGame(ctx context.Context, in *StartUgcChannelSingingGameReq, opts ...grpc.CallOption) (*StartUgcChannelSingingGameResp, error)
	// 取消准备（UGC房）
	CancelSingingGamePreparation(ctx context.Context, in *CancelSingingGamePreparationReq, opts ...grpc.CallOption) (*CancelSingingGamePreparationResp, error)
	// 拉全部形象 装饰配置
	GetAllSingImageConf(ctx context.Context, in *GetAllSingImageConfReq, opts ...grpc.CallOption) (*GetAllSingImageConfResp, error)
	// 用户可见形象
	GetUserSingImage(ctx context.Context, in *GetUserSingImageReq, opts ...grpc.CallOption) (*GetUserSingImageResp, error)
	AwardUserSingImage(ctx context.Context, in *AwardUserSingImageReq, opts ...grpc.CallOption) (*AwardUserSingImageResp, error)
	SetUserSingImage(ctx context.Context, in *SetUserSingImageReq, opts ...grpc.CallOption) (*SetUserSingImageResp, error)
	BatchAwardUser(ctx context.Context, in *BatchAwardUserReq, opts ...grpc.CallOption) (*BatchAwardUserResp, error)
	GetAwardRecord(ctx context.Context, in *GetAwardRecordReq, opts ...grpc.CallOption) (*GetAwardRecordResp, error)
	UpdateSingImageConf(ctx context.Context, in *UpdateSingImageConfReq, opts ...grpc.CallOption) (*UpdateSingImageConfResp, error)
	DelSingImageConf(ctx context.Context, in *DelSingImageConfReq, opts ...grpc.CallOption) (*DelSingImageConfResp, error)
	UpdateConfVersion(ctx context.Context, in *UpdateConfVersionReq, opts ...grpc.CallOption) (*UpdateConfVersionResp, error)
	// 歌曲基础信息
	SetSongBaseInfo(ctx context.Context, in *SetSongBaseInfoReq, opts ...grpc.CallOption) (*SetSongBaseInfoResp, error)
	// 歌曲文件
	CheckSongExist(ctx context.Context, in *CheckSongExistReq, opts ...grpc.CallOption) (*CheckSongExistResp, error)
	SetSongFile(ctx context.Context, in *SetSongFileReq, opts ...grpc.CallOption) (*SetSongFileResp, error)
	UpdateSongFile(ctx context.Context, in *UpdateSongFileReq, opts ...grpc.CallOption) (*UpdateSongFileResp, error)
	GetSongList(ctx context.Context, in *GetSongListReq, opts ...grpc.CallOption) (*GetSongListResp, error)
	DelSong(ctx context.Context, in *DelSongReq, opts ...grpc.CallOption) (*DelSongResp, error)
	GetSongForApp(ctx context.Context, in *GetSongForAppReq, opts ...grpc.CallOption) (*GetSongForAppResp, error)
	// 专区配置
	AddSingingGame(ctx context.Context, in *AddSingingGameReq, opts ...grpc.CallOption) (*AddSingingGameResp, error)
	UpdateSingingGame(ctx context.Context, in *UpdateSingingGameReq, opts ...grpc.CallOption) (*UpdateSingingGameResp, error)
	GetSingingGame(ctx context.Context, in *GetSingingGameListReq, opts ...grpc.CallOption) (*GetSingingGameListResp, error)
	SetSingingGameRank(ctx context.Context, in *SetSingingGameRankReq, opts ...grpc.CallOption) (*SetSingingGameRankResp, error)
	DelSingingGame(ctx context.Context, in *DelSingingGameReq, opts ...grpc.CallOption) (*DelSingingGameResp, error)
	// 推荐侧
	GetEnableTags(ctx context.Context, in *GetEnableTagsReq, opts ...grpc.CallOption) (*GetEnableTagsResp, error)
	// 客户端专区列表
	GetSingGameForApp(ctx context.Context, in *GetSingingGameForAppReq, opts ...grpc.CallOption) (*GetSingingGameForAppResp, error)
	// 获取轮次信息
	GetSingingGameRoundInfo(ctx context.Context, in *GetSingingGameRoundInfoReq, opts ...grpc.CallOption) (*GetSingingGameRoundInfoResp, error)
	// 投票
	SingingGameVote(ctx context.Context, in *SingingGameVoteReq, opts ...grpc.CallOption) (*SingingGameVoteResp, error)
	// 完成资源加载
	AccomplishLoadingRes(ctx context.Context, in *AccomplishLoadingResReq, opts ...grpc.CallOption) (*AccomplishLoadingResResp, error)
	// 排行榜
	GetSingingGameRankList(ctx context.Context, in *GetSingingGameRankListReq, opts ...grpc.CallOption) (*GetSingingGameRankListResp, error)
	// 通关模式游戏记录
	GetSingingGameRecordList(ctx context.Context, in *GetSingingGameRecordListReq, opts ...grpc.CallOption) (*GetSingingGameRecordListResp, error)
	// 新增曲风
	AddSongStyle(ctx context.Context, in *AddSongStyleReq, opts ...grpc.CallOption) (*AddSongStyleResp, error)
	// 删除曲风
	DelSongStyle(ctx context.Context, in *DelSongStyleReq, opts ...grpc.CallOption) (*DelSongStyleResp, error)
	// 获取所有曲风
	GetAllSongStyle(ctx context.Context, in *GetAllSongStyleReq, opts ...grpc.CallOption) (*GetAllSongStyleResp, error)
	// 低质用户行为记录
	GetLessUser(ctx context.Context, in *GetLessUserReq, opts ...grpc.CallOption) (*GetLessUserResp, error)
	// 获取个人抢唱信息
	GetSingingGameUserInfo(ctx context.Context, in *GetSingingGameUserInfoReq, opts ...grpc.CallOption) (*GetSingingGameUserInfoResp, error)
}

type singARoundClient struct {
	cc *grpc.ClientConn
}

func NewSingARoundClient(cc *grpc.ClientConn) SingARoundClient {
	return &singARoundClient{cc}
}

func (c *singARoundClient) StartSingingGameMatching(ctx context.Context, in *StartSingingGameMatchingReq, opts ...grpc.CallOption) (*StartSingingGameMatchingResp, error) {
	out := new(StartSingingGameMatchingResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/StartSingingGameMatching", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) JoinSingingGame(ctx context.Context, in *JoinSingingGameReq, opts ...grpc.CallOption) (*JoinSingingGameResp, error) {
	out := new(JoinSingingGameResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/JoinSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGameCountdown(ctx context.Context, in *GetSingingGameCountdownReq, opts ...grpc.CallOption) (*GetSingingGameCountdownResp, error) {
	out := new(GetSingingGameCountdownResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGameCountdown", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) StartSingingGame(ctx context.Context, in *StartSingingGameReq, opts ...grpc.CallOption) (*StartSingingGameResp, error) {
	out := new(StartSingingGameResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/StartSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GrabSingingGameMic(ctx context.Context, in *GrabSingingGameMicReq, opts ...grpc.CallOption) (*GrabSingingGameMicResp, error) {
	out := new(GrabSingingGameMicResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GrabSingingGameMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AskForSingingHelp(ctx context.Context, in *AskForSingingHelpReq, opts ...grpc.CallOption) (*AskForSingingHelpResp, error) {
	out := new(AskForSingingHelpResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AskForSingingHelp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AnswerSingingHelp(ctx context.Context, in *AnswerSingingHelpReq, opts ...grpc.CallOption) (*AnswerSingingHelpResp, error) {
	out := new(AnswerSingingHelpResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AnswerSingingHelp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AccomplishSingingGameSong(ctx context.Context, in *AccomplishSingingGameSongReq, opts ...grpc.CallOption) (*AccomplishSingingGameSongResp, error) {
	out := new(AccomplishSingingGameSongResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AccomplishSingingGameSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) ReportSingingGameSongScore(ctx context.Context, in *ReportSingingGameSongScoreReq, opts ...grpc.CallOption) (*ReportSingingGameSongScoreResp, error) {
	out := new(ReportSingingGameSongScoreResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/ReportSingingGameSongScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGameChannelInfo(ctx context.Context, in *GetSingingGameChannelInfoReq, opts ...grpc.CallOption) (*GetSingingGameChannelInfoResp, error) {
	out := new(GetSingingGameChannelInfoResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGameChannelInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SwitchSingingGameType(ctx context.Context, in *SwitchSingingGameTypeReq, opts ...grpc.CallOption) (*SwitchSingingGameTypeResp, error) {
	out := new(SwitchSingingGameTypeResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SwitchSingingGameType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) ExpressSingingGameLike(ctx context.Context, in *ExpressSingingGameLikeReq, opts ...grpc.CallOption) (*ExpressSingingGameLikeResp, error) {
	out := new(ExpressSingingGameLikeResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/ExpressSingingGameLike", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SingingGameFistBump(ctx context.Context, in *SingingGameFistBumpReq, opts ...grpc.CallOption) (*SingingGameFistBumpResp, error) {
	out := new(SingingGameFistBumpResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SingingGameFistBump", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) StartUgcChannelSingingGame(ctx context.Context, in *StartUgcChannelSingingGameReq, opts ...grpc.CallOption) (*StartUgcChannelSingingGameResp, error) {
	out := new(StartUgcChannelSingingGameResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/StartUgcChannelSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) CancelSingingGamePreparation(ctx context.Context, in *CancelSingingGamePreparationReq, opts ...grpc.CallOption) (*CancelSingingGamePreparationResp, error) {
	out := new(CancelSingingGamePreparationResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/CancelSingingGamePreparation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetAllSingImageConf(ctx context.Context, in *GetAllSingImageConfReq, opts ...grpc.CallOption) (*GetAllSingImageConfResp, error) {
	out := new(GetAllSingImageConfResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetAllSingImageConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetUserSingImage(ctx context.Context, in *GetUserSingImageReq, opts ...grpc.CallOption) (*GetUserSingImageResp, error) {
	out := new(GetUserSingImageResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetUserSingImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AwardUserSingImage(ctx context.Context, in *AwardUserSingImageReq, opts ...grpc.CallOption) (*AwardUserSingImageResp, error) {
	out := new(AwardUserSingImageResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AwardUserSingImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SetUserSingImage(ctx context.Context, in *SetUserSingImageReq, opts ...grpc.CallOption) (*SetUserSingImageResp, error) {
	out := new(SetUserSingImageResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SetUserSingImage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) BatchAwardUser(ctx context.Context, in *BatchAwardUserReq, opts ...grpc.CallOption) (*BatchAwardUserResp, error) {
	out := new(BatchAwardUserResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/BatchAwardUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetAwardRecord(ctx context.Context, in *GetAwardRecordReq, opts ...grpc.CallOption) (*GetAwardRecordResp, error) {
	out := new(GetAwardRecordResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetAwardRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) UpdateSingImageConf(ctx context.Context, in *UpdateSingImageConfReq, opts ...grpc.CallOption) (*UpdateSingImageConfResp, error) {
	out := new(UpdateSingImageConfResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/UpdateSingImageConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) DelSingImageConf(ctx context.Context, in *DelSingImageConfReq, opts ...grpc.CallOption) (*DelSingImageConfResp, error) {
	out := new(DelSingImageConfResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/DelSingImageConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) UpdateConfVersion(ctx context.Context, in *UpdateConfVersionReq, opts ...grpc.CallOption) (*UpdateConfVersionResp, error) {
	out := new(UpdateConfVersionResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/UpdateConfVersion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SetSongBaseInfo(ctx context.Context, in *SetSongBaseInfoReq, opts ...grpc.CallOption) (*SetSongBaseInfoResp, error) {
	out := new(SetSongBaseInfoResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SetSongBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) CheckSongExist(ctx context.Context, in *CheckSongExistReq, opts ...grpc.CallOption) (*CheckSongExistResp, error) {
	out := new(CheckSongExistResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/CheckSongExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SetSongFile(ctx context.Context, in *SetSongFileReq, opts ...grpc.CallOption) (*SetSongFileResp, error) {
	out := new(SetSongFileResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SetSongFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) UpdateSongFile(ctx context.Context, in *UpdateSongFileReq, opts ...grpc.CallOption) (*UpdateSongFileResp, error) {
	out := new(UpdateSongFileResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/UpdateSongFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSongList(ctx context.Context, in *GetSongListReq, opts ...grpc.CallOption) (*GetSongListResp, error) {
	out := new(GetSongListResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) DelSong(ctx context.Context, in *DelSongReq, opts ...grpc.CallOption) (*DelSongResp, error) {
	out := new(DelSongResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/DelSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSongForApp(ctx context.Context, in *GetSongForAppReq, opts ...grpc.CallOption) (*GetSongForAppResp, error) {
	out := new(GetSongForAppResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSongForApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AddSingingGame(ctx context.Context, in *AddSingingGameReq, opts ...grpc.CallOption) (*AddSingingGameResp, error) {
	out := new(AddSingingGameResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AddSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) UpdateSingingGame(ctx context.Context, in *UpdateSingingGameReq, opts ...grpc.CallOption) (*UpdateSingingGameResp, error) {
	out := new(UpdateSingingGameResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/UpdateSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGame(ctx context.Context, in *GetSingingGameListReq, opts ...grpc.CallOption) (*GetSingingGameListResp, error) {
	out := new(GetSingingGameListResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SetSingingGameRank(ctx context.Context, in *SetSingingGameRankReq, opts ...grpc.CallOption) (*SetSingingGameRankResp, error) {
	out := new(SetSingingGameRankResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SetSingingGameRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) DelSingingGame(ctx context.Context, in *DelSingingGameReq, opts ...grpc.CallOption) (*DelSingingGameResp, error) {
	out := new(DelSingingGameResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/DelSingingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetEnableTags(ctx context.Context, in *GetEnableTagsReq, opts ...grpc.CallOption) (*GetEnableTagsResp, error) {
	out := new(GetEnableTagsResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetEnableTags", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingGameForApp(ctx context.Context, in *GetSingingGameForAppReq, opts ...grpc.CallOption) (*GetSingingGameForAppResp, error) {
	out := new(GetSingingGameForAppResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingGameForApp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGameRoundInfo(ctx context.Context, in *GetSingingGameRoundInfoReq, opts ...grpc.CallOption) (*GetSingingGameRoundInfoResp, error) {
	out := new(GetSingingGameRoundInfoResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGameRoundInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) SingingGameVote(ctx context.Context, in *SingingGameVoteReq, opts ...grpc.CallOption) (*SingingGameVoteResp, error) {
	out := new(SingingGameVoteResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/SingingGameVote", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AccomplishLoadingRes(ctx context.Context, in *AccomplishLoadingResReq, opts ...grpc.CallOption) (*AccomplishLoadingResResp, error) {
	out := new(AccomplishLoadingResResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AccomplishLoadingRes", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGameRankList(ctx context.Context, in *GetSingingGameRankListReq, opts ...grpc.CallOption) (*GetSingingGameRankListResp, error) {
	out := new(GetSingingGameRankListResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGameRankList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGameRecordList(ctx context.Context, in *GetSingingGameRecordListReq, opts ...grpc.CallOption) (*GetSingingGameRecordListResp, error) {
	out := new(GetSingingGameRecordListResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGameRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) AddSongStyle(ctx context.Context, in *AddSongStyleReq, opts ...grpc.CallOption) (*AddSongStyleResp, error) {
	out := new(AddSongStyleResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/AddSongStyle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) DelSongStyle(ctx context.Context, in *DelSongStyleReq, opts ...grpc.CallOption) (*DelSongStyleResp, error) {
	out := new(DelSongStyleResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/DelSongStyle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetAllSongStyle(ctx context.Context, in *GetAllSongStyleReq, opts ...grpc.CallOption) (*GetAllSongStyleResp, error) {
	out := new(GetAllSongStyleResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetAllSongStyle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetLessUser(ctx context.Context, in *GetLessUserReq, opts ...grpc.CallOption) (*GetLessUserResp, error) {
	out := new(GetLessUserResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetLessUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *singARoundClient) GetSingingGameUserInfo(ctx context.Context, in *GetSingingGameUserInfoReq, opts ...grpc.CallOption) (*GetSingingGameUserInfoResp, error) {
	out := new(GetSingingGameUserInfoResp)
	err := c.cc.Invoke(ctx, "/singaround.SingARound/GetSingingGameUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SingARoundServer is the server API for SingARound service.
type SingARoundServer interface {
	// 匹配
	StartSingingGameMatching(context.Context, *StartSingingGameMatchingReq) (*StartSingingGameMatchingResp, error)
	// 加入游戏（观众加入游戏时使用）
	JoinSingingGame(context.Context, *JoinSingingGameReq) (*JoinSingingGameResp, error)
	// 获取开始倒计时
	GetSingingGameCountdown(context.Context, *GetSingingGameCountdownReq) (*GetSingingGameCountdownResp, error)
	// 准备（开始游戏）
	StartSingingGame(context.Context, *StartSingingGameReq) (*StartSingingGameResp, error)
	// 抢唱
	GrabSingingGameMic(context.Context, *GrabSingingGameMicReq) (*GrabSingingGameMicResp, error)
	// 请求帮唱
	AskForSingingHelp(context.Context, *AskForSingingHelpReq) (*AskForSingingHelpResp, error)
	// 帮唱
	AnswerSingingHelp(context.Context, *AnswerSingingHelpReq) (*AnswerSingingHelpResp, error)
	// 完成演唱
	AccomplishSingingGameSong(context.Context, *AccomplishSingingGameSongReq) (*AccomplishSingingGameSongResp, error)
	// 上报积分
	ReportSingingGameSongScore(context.Context, *ReportSingingGameSongScoreReq) (*ReportSingingGameSongScoreResp, error)
	// 用户房间状态（客户端重连时使用）
	GetSingingGameChannelInfo(context.Context, *GetSingingGameChannelInfoReq) (*GetSingingGameChannelInfoResp, error)
	// 切换游戏类型
	SwitchSingingGameType(context.Context, *SwitchSingingGameTypeReq) (*SwitchSingingGameTypeResp, error)
	// 牛啊
	ExpressSingingGameLike(context.Context, *ExpressSingingGameLikeReq) (*ExpressSingingGameLikeResp, error)
	// 碰拳
	SingingGameFistBump(context.Context, *SingingGameFistBumpReq) (*SingingGameFistBumpResp, error)
	// 开始游戏（UGC房）
	StartUgcChannelSingingGame(context.Context, *StartUgcChannelSingingGameReq) (*StartUgcChannelSingingGameResp, error)
	// 取消准备（UGC房）
	CancelSingingGamePreparation(context.Context, *CancelSingingGamePreparationReq) (*CancelSingingGamePreparationResp, error)
	// 拉全部形象 装饰配置
	GetAllSingImageConf(context.Context, *GetAllSingImageConfReq) (*GetAllSingImageConfResp, error)
	// 用户可见形象
	GetUserSingImage(context.Context, *GetUserSingImageReq) (*GetUserSingImageResp, error)
	AwardUserSingImage(context.Context, *AwardUserSingImageReq) (*AwardUserSingImageResp, error)
	SetUserSingImage(context.Context, *SetUserSingImageReq) (*SetUserSingImageResp, error)
	BatchAwardUser(context.Context, *BatchAwardUserReq) (*BatchAwardUserResp, error)
	GetAwardRecord(context.Context, *GetAwardRecordReq) (*GetAwardRecordResp, error)
	UpdateSingImageConf(context.Context, *UpdateSingImageConfReq) (*UpdateSingImageConfResp, error)
	DelSingImageConf(context.Context, *DelSingImageConfReq) (*DelSingImageConfResp, error)
	UpdateConfVersion(context.Context, *UpdateConfVersionReq) (*UpdateConfVersionResp, error)
	// 歌曲基础信息
	SetSongBaseInfo(context.Context, *SetSongBaseInfoReq) (*SetSongBaseInfoResp, error)
	// 歌曲文件
	CheckSongExist(context.Context, *CheckSongExistReq) (*CheckSongExistResp, error)
	SetSongFile(context.Context, *SetSongFileReq) (*SetSongFileResp, error)
	UpdateSongFile(context.Context, *UpdateSongFileReq) (*UpdateSongFileResp, error)
	GetSongList(context.Context, *GetSongListReq) (*GetSongListResp, error)
	DelSong(context.Context, *DelSongReq) (*DelSongResp, error)
	GetSongForApp(context.Context, *GetSongForAppReq) (*GetSongForAppResp, error)
	// 专区配置
	AddSingingGame(context.Context, *AddSingingGameReq) (*AddSingingGameResp, error)
	UpdateSingingGame(context.Context, *UpdateSingingGameReq) (*UpdateSingingGameResp, error)
	GetSingingGame(context.Context, *GetSingingGameListReq) (*GetSingingGameListResp, error)
	SetSingingGameRank(context.Context, *SetSingingGameRankReq) (*SetSingingGameRankResp, error)
	DelSingingGame(context.Context, *DelSingingGameReq) (*DelSingingGameResp, error)
	// 推荐侧
	GetEnableTags(context.Context, *GetEnableTagsReq) (*GetEnableTagsResp, error)
	// 客户端专区列表
	GetSingGameForApp(context.Context, *GetSingingGameForAppReq) (*GetSingingGameForAppResp, error)
	// 获取轮次信息
	GetSingingGameRoundInfo(context.Context, *GetSingingGameRoundInfoReq) (*GetSingingGameRoundInfoResp, error)
	// 投票
	SingingGameVote(context.Context, *SingingGameVoteReq) (*SingingGameVoteResp, error)
	// 完成资源加载
	AccomplishLoadingRes(context.Context, *AccomplishLoadingResReq) (*AccomplishLoadingResResp, error)
	// 排行榜
	GetSingingGameRankList(context.Context, *GetSingingGameRankListReq) (*GetSingingGameRankListResp, error)
	// 通关模式游戏记录
	GetSingingGameRecordList(context.Context, *GetSingingGameRecordListReq) (*GetSingingGameRecordListResp, error)
	// 新增曲风
	AddSongStyle(context.Context, *AddSongStyleReq) (*AddSongStyleResp, error)
	// 删除曲风
	DelSongStyle(context.Context, *DelSongStyleReq) (*DelSongStyleResp, error)
	// 获取所有曲风
	GetAllSongStyle(context.Context, *GetAllSongStyleReq) (*GetAllSongStyleResp, error)
	// 低质用户行为记录
	GetLessUser(context.Context, *GetLessUserReq) (*GetLessUserResp, error)
	// 获取个人抢唱信息
	GetSingingGameUserInfo(context.Context, *GetSingingGameUserInfoReq) (*GetSingingGameUserInfoResp, error)
}

func RegisterSingARoundServer(s *grpc.Server, srv SingARoundServer) {
	s.RegisterService(&_SingARound_serviceDesc, srv)
}

func _SingARound_StartSingingGameMatching_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartSingingGameMatchingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).StartSingingGameMatching(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/StartSingingGameMatching",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).StartSingingGameMatching(ctx, req.(*StartSingingGameMatchingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_JoinSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JoinSingingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).JoinSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/JoinSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).JoinSingingGame(ctx, req.(*JoinSingingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGameCountdown_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameCountdownReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGameCountdown(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGameCountdown",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGameCountdown(ctx, req.(*GetSingingGameCountdownReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_StartSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartSingingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).StartSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/StartSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).StartSingingGame(ctx, req.(*StartSingingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GrabSingingGameMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GrabSingingGameMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GrabSingingGameMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GrabSingingGameMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GrabSingingGameMic(ctx, req.(*GrabSingingGameMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AskForSingingHelp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AskForSingingHelpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AskForSingingHelp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AskForSingingHelp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AskForSingingHelp(ctx, req.(*AskForSingingHelpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AnswerSingingHelp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnswerSingingHelpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AnswerSingingHelp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AnswerSingingHelp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AnswerSingingHelp(ctx, req.(*AnswerSingingHelpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AccomplishSingingGameSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccomplishSingingGameSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AccomplishSingingGameSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AccomplishSingingGameSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AccomplishSingingGameSong(ctx, req.(*AccomplishSingingGameSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_ReportSingingGameSongScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportSingingGameSongScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).ReportSingingGameSongScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/ReportSingingGameSongScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).ReportSingingGameSongScore(ctx, req.(*ReportSingingGameSongScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGameChannelInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameChannelInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGameChannelInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGameChannelInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGameChannelInfo(ctx, req.(*GetSingingGameChannelInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SwitchSingingGameType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SwitchSingingGameTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SwitchSingingGameType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SwitchSingingGameType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SwitchSingingGameType(ctx, req.(*SwitchSingingGameTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_ExpressSingingGameLike_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpressSingingGameLikeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).ExpressSingingGameLike(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/ExpressSingingGameLike",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).ExpressSingingGameLike(ctx, req.(*ExpressSingingGameLikeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SingingGameFistBump_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SingingGameFistBumpReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SingingGameFistBump(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SingingGameFistBump",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SingingGameFistBump(ctx, req.(*SingingGameFistBumpReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_StartUgcChannelSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartUgcChannelSingingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).StartUgcChannelSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/StartUgcChannelSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).StartUgcChannelSingingGame(ctx, req.(*StartUgcChannelSingingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_CancelSingingGamePreparation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelSingingGamePreparationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).CancelSingingGamePreparation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/CancelSingingGamePreparation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).CancelSingingGamePreparation(ctx, req.(*CancelSingingGamePreparationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetAllSingImageConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSingImageConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetAllSingImageConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetAllSingImageConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetAllSingImageConf(ctx, req.(*GetAllSingImageConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetUserSingImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSingImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetUserSingImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetUserSingImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetUserSingImage(ctx, req.(*GetUserSingImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AwardUserSingImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AwardUserSingImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AwardUserSingImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AwardUserSingImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AwardUserSingImage(ctx, req.(*AwardUserSingImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SetUserSingImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSingImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SetUserSingImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SetUserSingImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SetUserSingImage(ctx, req.(*SetUserSingImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_BatchAwardUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAwardUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).BatchAwardUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/BatchAwardUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).BatchAwardUser(ctx, req.(*BatchAwardUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetAwardRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetAwardRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetAwardRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetAwardRecord(ctx, req.(*GetAwardRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_UpdateSingImageConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSingImageConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).UpdateSingImageConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/UpdateSingImageConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).UpdateSingImageConf(ctx, req.(*UpdateSingImageConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_DelSingImageConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSingImageConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).DelSingImageConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/DelSingImageConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).DelSingImageConf(ctx, req.(*DelSingImageConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_UpdateConfVersion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConfVersionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).UpdateConfVersion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/UpdateConfVersion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).UpdateConfVersion(ctx, req.(*UpdateConfVersionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SetSongBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSongBaseInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SetSongBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SetSongBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SetSongBaseInfo(ctx, req.(*SetSongBaseInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_CheckSongExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSongExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).CheckSongExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/CheckSongExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).CheckSongExist(ctx, req.(*CheckSongExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SetSongFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSongFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SetSongFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SetSongFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SetSongFile(ctx, req.(*SetSongFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_UpdateSongFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSongFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).UpdateSongFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/UpdateSongFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).UpdateSongFile(ctx, req.(*UpdateSongFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSongList(ctx, req.(*GetSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_DelSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).DelSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/DelSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).DelSong(ctx, req.(*DelSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSongForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSongForAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSongForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSongForApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSongForApp(ctx, req.(*GetSongForAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AddSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSingingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AddSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AddSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AddSingingGame(ctx, req.(*AddSingingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_UpdateSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSingingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).UpdateSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/UpdateSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).UpdateSingingGame(ctx, req.(*UpdateSingingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGame(ctx, req.(*GetSingingGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SetSingingGameRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSingingGameRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SetSingingGameRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SetSingingGameRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SetSingingGameRank(ctx, req.(*SetSingingGameRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_DelSingingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSingingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).DelSingingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/DelSingingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).DelSingingGame(ctx, req.(*DelSingingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetEnableTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnableTagsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetEnableTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetEnableTags",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetEnableTags(ctx, req.(*GetEnableTagsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingGameForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameForAppReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingGameForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingGameForApp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingGameForApp(ctx, req.(*GetSingingGameForAppReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGameRoundInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameRoundInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGameRoundInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGameRoundInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGameRoundInfo(ctx, req.(*GetSingingGameRoundInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_SingingGameVote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SingingGameVoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).SingingGameVote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/SingingGameVote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).SingingGameVote(ctx, req.(*SingingGameVoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AccomplishLoadingRes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AccomplishLoadingResReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AccomplishLoadingRes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AccomplishLoadingRes",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AccomplishLoadingRes(ctx, req.(*AccomplishLoadingResReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGameRankList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGameRankList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGameRankList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGameRankList(ctx, req.(*GetSingingGameRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGameRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGameRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGameRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGameRecordList(ctx, req.(*GetSingingGameRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_AddSongStyle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSongStyleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).AddSongStyle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/AddSongStyle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).AddSongStyle(ctx, req.(*AddSongStyleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_DelSongStyle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSongStyleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).DelSongStyle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/DelSongStyle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).DelSongStyle(ctx, req.(*DelSongStyleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetAllSongStyle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllSongStyleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetAllSongStyle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetAllSongStyle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetAllSongStyle(ctx, req.(*GetAllSongStyleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetLessUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLessUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetLessUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetLessUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetLessUser(ctx, req.(*GetLessUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SingARound_GetSingingGameUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingingGameUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SingARoundServer).GetSingingGameUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/singaround.SingARound/GetSingingGameUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SingARoundServer).GetSingingGameUserInfo(ctx, req.(*GetSingingGameUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _SingARound_serviceDesc = grpc.ServiceDesc{
	ServiceName: "singaround.SingARound",
	HandlerType: (*SingARoundServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "StartSingingGameMatching",
			Handler:    _SingARound_StartSingingGameMatching_Handler,
		},
		{
			MethodName: "JoinSingingGame",
			Handler:    _SingARound_JoinSingingGame_Handler,
		},
		{
			MethodName: "GetSingingGameCountdown",
			Handler:    _SingARound_GetSingingGameCountdown_Handler,
		},
		{
			MethodName: "StartSingingGame",
			Handler:    _SingARound_StartSingingGame_Handler,
		},
		{
			MethodName: "GrabSingingGameMic",
			Handler:    _SingARound_GrabSingingGameMic_Handler,
		},
		{
			MethodName: "AskForSingingHelp",
			Handler:    _SingARound_AskForSingingHelp_Handler,
		},
		{
			MethodName: "AnswerSingingHelp",
			Handler:    _SingARound_AnswerSingingHelp_Handler,
		},
		{
			MethodName: "AccomplishSingingGameSong",
			Handler:    _SingARound_AccomplishSingingGameSong_Handler,
		},
		{
			MethodName: "ReportSingingGameSongScore",
			Handler:    _SingARound_ReportSingingGameSongScore_Handler,
		},
		{
			MethodName: "GetSingingGameChannelInfo",
			Handler:    _SingARound_GetSingingGameChannelInfo_Handler,
		},
		{
			MethodName: "SwitchSingingGameType",
			Handler:    _SingARound_SwitchSingingGameType_Handler,
		},
		{
			MethodName: "ExpressSingingGameLike",
			Handler:    _SingARound_ExpressSingingGameLike_Handler,
		},
		{
			MethodName: "SingingGameFistBump",
			Handler:    _SingARound_SingingGameFistBump_Handler,
		},
		{
			MethodName: "StartUgcChannelSingingGame",
			Handler:    _SingARound_StartUgcChannelSingingGame_Handler,
		},
		{
			MethodName: "CancelSingingGamePreparation",
			Handler:    _SingARound_CancelSingingGamePreparation_Handler,
		},
		{
			MethodName: "GetAllSingImageConf",
			Handler:    _SingARound_GetAllSingImageConf_Handler,
		},
		{
			MethodName: "GetUserSingImage",
			Handler:    _SingARound_GetUserSingImage_Handler,
		},
		{
			MethodName: "AwardUserSingImage",
			Handler:    _SingARound_AwardUserSingImage_Handler,
		},
		{
			MethodName: "SetUserSingImage",
			Handler:    _SingARound_SetUserSingImage_Handler,
		},
		{
			MethodName: "BatchAwardUser",
			Handler:    _SingARound_BatchAwardUser_Handler,
		},
		{
			MethodName: "GetAwardRecord",
			Handler:    _SingARound_GetAwardRecord_Handler,
		},
		{
			MethodName: "UpdateSingImageConf",
			Handler:    _SingARound_UpdateSingImageConf_Handler,
		},
		{
			MethodName: "DelSingImageConf",
			Handler:    _SingARound_DelSingImageConf_Handler,
		},
		{
			MethodName: "UpdateConfVersion",
			Handler:    _SingARound_UpdateConfVersion_Handler,
		},
		{
			MethodName: "SetSongBaseInfo",
			Handler:    _SingARound_SetSongBaseInfo_Handler,
		},
		{
			MethodName: "CheckSongExist",
			Handler:    _SingARound_CheckSongExist_Handler,
		},
		{
			MethodName: "SetSongFile",
			Handler:    _SingARound_SetSongFile_Handler,
		},
		{
			MethodName: "UpdateSongFile",
			Handler:    _SingARound_UpdateSongFile_Handler,
		},
		{
			MethodName: "GetSongList",
			Handler:    _SingARound_GetSongList_Handler,
		},
		{
			MethodName: "DelSong",
			Handler:    _SingARound_DelSong_Handler,
		},
		{
			MethodName: "GetSongForApp",
			Handler:    _SingARound_GetSongForApp_Handler,
		},
		{
			MethodName: "AddSingingGame",
			Handler:    _SingARound_AddSingingGame_Handler,
		},
		{
			MethodName: "UpdateSingingGame",
			Handler:    _SingARound_UpdateSingingGame_Handler,
		},
		{
			MethodName: "GetSingingGame",
			Handler:    _SingARound_GetSingingGame_Handler,
		},
		{
			MethodName: "SetSingingGameRank",
			Handler:    _SingARound_SetSingingGameRank_Handler,
		},
		{
			MethodName: "DelSingingGame",
			Handler:    _SingARound_DelSingingGame_Handler,
		},
		{
			MethodName: "GetEnableTags",
			Handler:    _SingARound_GetEnableTags_Handler,
		},
		{
			MethodName: "GetSingGameForApp",
			Handler:    _SingARound_GetSingGameForApp_Handler,
		},
		{
			MethodName: "GetSingingGameRoundInfo",
			Handler:    _SingARound_GetSingingGameRoundInfo_Handler,
		},
		{
			MethodName: "SingingGameVote",
			Handler:    _SingARound_SingingGameVote_Handler,
		},
		{
			MethodName: "AccomplishLoadingRes",
			Handler:    _SingARound_AccomplishLoadingRes_Handler,
		},
		{
			MethodName: "GetSingingGameRankList",
			Handler:    _SingARound_GetSingingGameRankList_Handler,
		},
		{
			MethodName: "GetSingingGameRecordList",
			Handler:    _SingARound_GetSingingGameRecordList_Handler,
		},
		{
			MethodName: "AddSongStyle",
			Handler:    _SingARound_AddSongStyle_Handler,
		},
		{
			MethodName: "DelSongStyle",
			Handler:    _SingARound_DelSongStyle_Handler,
		},
		{
			MethodName: "GetAllSongStyle",
			Handler:    _SingARound_GetAllSongStyle_Handler,
		},
		{
			MethodName: "GetLessUser",
			Handler:    _SingARound_GetLessUser_Handler,
		},
		{
			MethodName: "GetSingingGameUserInfo",
			Handler:    _SingARound_GetSingingGameUserInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sing-a-round/sing-a-round.proto",
}

func init() {
	proto.RegisterFile("sing-a-round/sing-a-round.proto", fileDescriptor_sing_a_round_2f5f025d96d12b52)
}

var fileDescriptor_sing_a_round_2f5f025d96d12b52 = []byte{
	// 5556 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x7c, 0xcd, 0x6f, 0x1c, 0xc9,
	0x75, 0x38, 0x7b, 0x86, 0xe4, 0x70, 0xde, 0x70, 0xc8, 0x61, 0xf1, 0x7b, 0x28, 0x89, 0x54, 0x4b,
	0xbb, 0x2b, 0xd3, 0xb2, 0x16, 0x92, 0x7f, 0xfb, 0xf3, 0xc2, 0xf1, 0xc6, 0xa6, 0xa8, 0x2f, 0x7a,
	0x65, 0xad, 0x3c, 0x94, 0x36, 0xc8, 0x26, 0x4e, 0xa7, 0xd9, 0x5d, 0x1c, 0x36, 0xd8, 0xd3, 0xdd,
	0xdb, 0xd5, 0xa3, 0x8f, 0x38, 0xc9, 0xc5, 0x08, 0x8c, 0x00, 0x81, 0x0f, 0x01, 0xe2, 0x00, 0xb9,
	0x18, 0x01, 0x12, 0x20, 0x4e, 0x2e, 0xf9, 0x00, 0x72, 0x0c, 0x72, 0x48, 0xe0, 0x5b, 0xfe, 0x80,
	0x9c, 0x7c, 0x4b, 0xfe, 0x82, 0x1c, 0x83, 0xa0, 0x5e, 0x55, 0x77, 0x57, 0xf5, 0xc7, 0x90, 0x52,
	0xe8, 0x75, 0x4e, 0x9c, 0x7e, 0xf5, 0xea, 0x55, 0xd5, 0xab, 0x57, 0xef, 0xab, 0x5e, 0x11, 0xb6,
	0x99, 0x17, 0x0c, 0xbf, 0x62, 0x7f, 0x25, 0x0e, 0xc7, 0x81, 0xfb, 0xbe, 0xfa, 0x71, 0x2b, 0x8a,
	0xc3, 0x24, 0x24, 0xc0, 0x61, 0x36, 0x42, 0xcc, 0xbf, 0x31, 0x60, 0xeb, 0x30, 0xb1, 0xe3, 0xe4,
	0xd0, 0x0b, 0x86, 0x5e, 0x30, 0x7c, 0x68, 0x8f, 0xe8, 0x77, 0xec, 0xc4, 0x39, 0xf1, 0x82, 0xe1,
	0x80, 0x7e, 0x4e, 0x7a, 0xd0, 0x1c, 0x7b, 0xee, 0x86, 0xb1, 0x63, 0xdc, 0xe8, 0x0e, 0xf8, 0x4f,
	0xf2, 0x10, 0x96, 0x98, 0xc0, 0xb5, 0x86, 0xf6, 0x88, 0x5a, 0xc9, 0xeb, 0x88, 0x6e, 0x34, 0x76,
	0x8c, 0x1b, 0x0b, 0x77, 0xb6, 0x6e, 0xe5, 0x94, 0x6f, 0x29, 0x04, 0x9f, 0xbd, 0x8e, 0xe8, 0x60,
	0x91, 0xe9, 0x00, 0x72, 0x1b, 0x56, 0x03, 0xfa, 0xd2, 0x2a, 0x13, 0x6b, 0xe2, 0x60, 0x24, 0xa0,
	0x2f, 0x0b, 0x34, 0xcc, 0x8f, 0xe0, 0x52, 0xfd, 0x64, 0x59, 0x44, 0x2e, 0x03, 0x38, 0x27, 0x76,
	0x10, 0x50, 0xdf, 0xca, 0x26, 0xdd, 0x96, 0x90, 0x03, 0xd7, 0xbc, 0x0f, 0xe4, 0xdb, 0xa1, 0x17,
	0x28, 0xbd, 0xf9, 0x12, 0x27, 0x77, 0x4a, 0x39, 0xd0, 0xc8, 0x38, 0x60, 0x06, 0xb0, 0x5c, 0x22,
	0x23, 0x06, 0xf7, 0x98, 0xc5, 0xc6, 0x8e, 0x43, 0x19, 0x43, 0x3a, 0x73, 0x83, 0xb6, 0xc7, 0x0e,
	0x05, 0x40, 0x36, 0x47, 0xbe, 0xfd, 0xda, 0x0b, 0x86, 0x48, 0x0e, 0x9b, 0x9f, 0x0a, 0x00, 0xd9,
	0x84, 0xb9, 0xb1, 0xe7, 0x5a, 0xbe, 0xc7, 0x92, 0x8d, 0xe6, 0x4e, 0xf3, 0x46, 0x77, 0xd0, 0x1a,
	0x7b, 0xee, 0x63, 0x8f, 0x25, 0xe6, 0x09, 0xf4, 0x1f, 0x52, 0x75, 0xcd, 0xfb, 0xe1, 0x38, 0x48,
	0xdc, 0xf0, 0x65, 0x50, 0xbd, 0x43, 0xfa, 0x82, 0x1a, 0xc5, 0x05, 0x6d, 0xc2, 0x1c, 0xee, 0x10,
	0x6f, 0x14, 0xac, 0x6e, 0xe1, 0xf7, 0x81, 0x6b, 0xfe, 0x0a, 0x6c, 0xd5, 0x8e, 0xc4, 0x22, 0x72,
	0x09, 0xda, 0x4e, 0x0a, 0xc8, 0x18, 0x95, 0x02, 0x4c, 0x1b, 0x96, 0x8b, 0x9b, 0x73, 0xd1, 0xf3,
	0xfb, 0x1e, 0xac, 0x94, 0x87, 0x60, 0x11, 0xb9, 0x0f, 0xf3, 0x19, 0xc5, 0xe0, 0x38, 0xc4, 0xc1,
	0x3a, 0x77, 0xcc, 0x1a, 0x71, 0xdc, 0x97, 0x43, 0x05, 0xc7, 0xe1, 0xa0, 0xe3, 0xe4, 0x1f, 0xe6,
	0xef, 0xc2, 0xea, 0xc3, 0xd8, 0x3e, 0x52, 0xa5, 0xcb, 0x73, 0x2e, 0x78, 0x0d, 0x64, 0x1d, 0x5a,
	0x2c, 0x0c, 0x86, 0xbc, 0x65, 0x7a, 0xc7, 0xb8, 0xd1, 0x1e, 0xcc, 0xf2, 0xcf, 0x03, 0xd7, 0xfc,
	0x7d, 0x58, 0xab, 0x1a, 0xfd, 0xc2, 0x96, 0x57, 0x10, 0xd0, 0x46, 0x41, 0x40, 0xcd, 0x1f, 0x1b,
	0xb0, 0xb2, 0xc7, 0x4e, 0x1f, 0x84, 0xb1, 0x24, 0xf6, 0x88, 0xfa, 0xd1, 0x17, 0xb5, 0x7a, 0xde,
	0xc7, 0x1e, 0x27, 0xa1, 0x15, 0xd3, 0xcf, 0x37, 0x66, 0x70, 0x6a, 0x2d, 0xfe, 0x3d, 0xa0, 0x9f,
	0x9b, 0xbf, 0x05, 0xab, 0x15, 0xf3, 0xba, 0xb8, 0x6d, 0xff, 0x3e, 0xac, 0xec, 0x05, 0xec, 0x25,
	0xfd, 0x65, 0xac, 0xdb, 0xfc, 0x3d, 0x58, 0xad, 0x18, 0xfc, 0x0b, 0xdb, 0xf4, 0x1f, 0x18, 0x70,
	0x69, 0xcf, 0x71, 0xc2, 0x51, 0xe4, 0x7b, 0xec, 0x44, 0x21, 0x78, 0x18, 0xd6, 0x19, 0x80, 0x5f,
	0x00, 0x13, 0x8e, 0xe1, 0xf2, 0x84, 0x49, 0x5c, 0xdc, 0x4e, 0xff, 0x99, 0x01, 0x97, 0x07, 0x34,
	0x0a, 0x35, 0x0d, 0xc2, 0x07, 0x39, 0x74, 0xc2, 0x98, 0x7e, 0x61, 0xb2, 0xbe, 0x02, 0x33, 0x8c,
	0x0f, 0x88, 0x82, 0xde, 0x1d, 0x88, 0x0f, 0xf3, 0x8f, 0x0c, 0xb8, 0x32, 0x69, 0x72, 0x2c, 0x22,
	0x77, 0xa1, 0x83, 0x14, 0x63, 0xca, 0xc6, 0x7e, 0x22, 0xb9, 0x70, 0xb5, 0x86, 0x0b, 0x92, 0x79,
	0x63, 0x3f, 0x19, 0x00, 0xcb, 0x7e, 0x93, 0x77, 0x61, 0xd1, 0x63, 0x56, 0x8c, 0x03, 0x59, 0xf6,
	0xd8, 0xf5, 0x42, 0x29, 0x15, 0x5d, 0x8f, 0x89, 0xe1, 0xf7, 0x38, 0xd0, 0xfc, 0x4b, 0x03, 0x56,
	0x2b, 0xa9, 0x5d, 0x94, 0x64, 0xde, 0x87, 0x79, 0x5c, 0xb8, 0xe5, 0xd2, 0xc4, 0xf6, 0x7c, 0x9c,
	0x45, 0x3d, 0x19, 0x64, 0xc2, 0x3d, 0xc4, 0x1c, 0x74, 0x58, 0xfe, 0x61, 0xfe, 0xd0, 0x80, 0xb5,
	0x6a, 0x3c, 0xb2, 0x01, 0x2d, 0xdd, 0x1c, 0xa7, 0x9f, 0xe4, 0x2a, 0xcc, 0x9f, 0x50, 0x3f, 0xa2,
	0xb1, 0x25, 0x36, 0x42, 0x6c, 0x6b, 0x47, 0xc0, 0x90, 0x04, 0x59, 0x83, 0x59, 0x6c, 0x63, 0x68,
	0x8e, 0xf9, 0xe6, 0xe1, 0x17, 0x27, 0x6a, 0x3b, 0x68, 0xf5, 0xe4, 0xae, 0xa6, 0x9f, 0xdc, 0x3b,
	0x29, 0x58, 0x4f, 0x65, 0xe9, 0x67, 0x3a, 0x1a, 0xe6, 0x7f, 0x4d, 0xc3, 0xe5, 0x09, 0xfd, 0x2f,
	0x4e, 0x25, 0xec, 0xc2, 0x0c, 0x97, 0x07, 0xae, 0x0d, 0x9a, 0x37, 0x3a, 0x77, 0x56, 0xb4, 0xfe,
	0x5c, 0x42, 0x79, 0x0f, 0x81, 0x42, 0xee, 0xc0, 0xda, 0x30, 0xb6, 0x8f, 0xac, 0x91, 0xe7, 0x58,
	0x47, 0x49, 0x60, 0x9d, 0x78, 0xae, 0x4b, 0x03, 0xcb, 0x4e, 0x52, 0x2f, 0x8d, 0xb7, 0x7e, 0xc7,
	0x73, 0xee, 0x26, 0xc1, 0x23, 0x6c, 0xda, 0x4b, 0xc8, 0x87, 0xb0, 0xa9, 0xf5, 0x71, 0x3d, 0xc6,
	0xdd, 0x1e, 0x2b, 0xf1, 0x46, 0x14, 0x79, 0xd6, 0x1d, 0xac, 0xe6, 0xdd, 0xee, 0x89, 0xd6, 0x67,
	0xde, 0x88, 0x92, 0x2f, 0x03, 0xe1, 0x5b, 0x50, 0x18, 0x49, 0x9c, 0x92, 0x45, 0xde, 0xa2, 0x0e,
	0x73, 0x1b, 0x56, 0x33, 0x64, 0x6d, 0x88, 0x59, 0x31, 0x33, 0x89, 0xaf, 0xd2, 0xff, 0x10, 0x66,
	0x59, 0xf2, 0xda, 0xa7, 0x6c, 0xa3, 0x85, 0x4b, 0xdf, 0x99, 0x70, 0x74, 0x0e, 0x39, 0xe2, 0x40,
	0xe2, 0x93, 0x87, 0xb0, 0xc0, 0xa8, 0x4f, 0x9d, 0x84, 0xba, 0x16, 0x82, 0x36, 0xe6, 0x90, 0xf9,
	0x67, 0x53, 0xe8, 0xa6, 0xfd, 0xf0, 0x93, 0xbc, 0x03, 0x0b, 0x2f, 0xc2, 0x84, 0x5a, 0xb9, 0x23,
	0xd5, 0xc6, 0xe9, 0x76, 0x39, 0x34, 0x73, 0xb7, 0xc8, 0x07, 0xb0, 0x1e, 0x84, 0x56, 0x14, 0xd3,
	0xc8, 0x8e, 0xa9, 0x75, 0x64, 0x07, 0x96, 0x3b, 0x8e, 0xed, 0xc4, 0x0b, 0x83, 0x8d, 0x0e, 0xe2,
	0xaf, 0x04, 0xe1, 0x53, 0xd1, 0x7a, 0xd7, 0x0e, 0xee, 0xc9, 0x36, 0xf2, 0x55, 0x58, 0xa3, 0xaf,
	0xbc, 0x44, 0x38, 0xd3, 0x5a, 0xaf, 0x79, 0xec, 0xb5, 0xcc, 0x5b, 0xf9, 0xfc, 0x94, 0x4e, 0xe6,
	0xcf, 0x0c, 0xd8, 0x38, 0x7c, 0xe9, 0x25, 0xce, 0x49, 0xd1, 0x67, 0x7f, 0x1b, 0x85, 0x58, 0x19,
	0x1f, 0x34, 0x2f, 0x32, 0x3e, 0x98, 0xae, 0x8d, 0x0f, 0x7e, 0x64, 0xc0, 0x66, 0xcd, 0x4a, 0x7e,
	0x29, 0xc7, 0xc7, 0xfc, 0xe7, 0x59, 0x4d, 0x39, 0xed, 0xeb, 0x86, 0x79, 0x52, 0xd8, 0x71, 0x15,
	0xe6, 0x47, 0x74, 0x74, 0x44, 0x63, 0xcb, 0xf7, 0x46, 0x5e, 0x92, 0x6a, 0x28, 0x01, 0x7b, 0xcc,
	0x41, 0x17, 0xc7, 0xe9, 0x77, 0x60, 0x01, 0xcd, 0x4a, 0x2e, 0x93, 0x82, 0xc5, 0x5d, 0x0e, 0xcd,
	0x65, 0xf2, 0x3a, 0x2c, 0x8c, 0xec, 0x57, 0x96, 0xef, 0x1d, 0x4b, 0xf1, 0x95, 0x27, 0x73, 0x7e,
	0x64, 0xbf, 0x7a, 0xec, 0x1d, 0x0b, 0xe9, 0x25, 0x3b, 0x30, 0xef, 0x8c, 0x63, 0x2b, 0x33, 0x8a,
	0xe2, 0x34, 0x82, 0x33, 0x8e, 0x07, 0xd2, 0x2e, 0xea, 0x91, 0x50, 0xab, 0x18, 0x09, 0x7d, 0x0d,
	0x66, 0x58, 0x62, 0x0f, 0xc5, 0x09, 0x5b, 0xa8, 0x35, 0x6f, 0x48, 0xed, 0x90, 0x23, 0x0e, 0x04,
	0x3e, 0xb9, 0x01, 0x3d, 0xfc, 0x61, 0x8d, 0x23, 0xd7, 0xe6, 0x07, 0xd5, 0x4e, 0xe4, 0xe1, 0x5a,
	0x40, 0xf8, 0x73, 0x01, 0xde, 0x4b, 0xc8, 0x15, 0xe8, 0xf0, 0x39, 0xa6, 0xd6, 0x19, 0x50, 0x8f,
	0xb7, 0x9d, 0x71, 0x7c, 0x28, 0x0c, 0xf4, 0x75, 0x58, 0xc8, 0xdb, 0x03, 0x97, 0xbe, 0x92, 0x87,
	0x6e, 0x3e, 0x45, 0xe1, 0x30, 0xf2, 0x11, 0xb4, 0xa3, 0x90, 0x79, 0xfc, 0x0c, 0xb1, 0x8d, 0x79,
	0x14, 0x86, 0xed, 0x9a, 0xc9, 0x3e, 0x95, 0x78, 0x83, 0xbc, 0x07, 0x37, 0x24, 0x2f, 0x68, 0xcc,
	0xf8, 0xe1, 0xec, 0x0a, 0xc7, 0x41, 0x7e, 0x92, 0x9b, 0x40, 0x46, 0x5e, 0xe0, 0x8d, 0xc6, 0x23,
	0xe4, 0x12, 0x8d, 0xad, 0x60, 0x3c, 0xda, 0x58, 0x40, 0xa4, 0x9e, 0x6c, 0x79, 0x8a, 0x0d, 0x4f,
	0xc6, 0x23, 0x72, 0x0d, 0xba, 0x4e, 0x18, 0x24, 0x71, 0xe8, 0xfb, 0x34, 0xe6, 0xcb, 0x59, 0x94,
	0x73, 0xcd, 0x80, 0x07, 0x6e, 0xfd, 0x61, 0xea, 0xd5, 0x1d, 0x26, 0x72, 0x0b, 0x96, 0xc3, 0xe3,
	0x63, 0xcf, 0xf1, 0x6c, 0xdf, 0x1a, 0xc6, 0xe1, 0x38, 0x12, 0xa7, 0x66, 0x09, 0x99, 0xb5, 0x94,
	0x36, 0x3d, 0xe4, 0x2d, 0x28, 0xd0, 0x8f, 0x81, 0x44, 0x36, 0x63, 0x56, 0x72, 0x12, 0x87, 0xe3,
	0xe1, 0x89, 0x45, 0x5f, 0x25, 0xb1, 0xbd, 0x41, 0xf0, 0x90, 0x5d, 0x52, 0xf9, 0xf2, 0xd4, 0x66,
	0xec, 0x99, 0x40, 0xba, 0xcf, 0x71, 0x1e, 0x4d, 0x0d, 0x7a, 0x51, 0x01, 0x76, 0xb7, 0x05, 0x33,
	0x48, 0xc0, 0xfc, 0x7b, 0x03, 0x7a, 0xc5, 0x1e, 0xd9, 0x06, 0x9d, 0x7a, 0x91, 0x14, 0x45, 0x23,
	0xdf, 0xa0, 0x53, 0x2f, 0x12, 0xa2, 0x28, 0x05, 0x56, 0xc1, 0x6a, 0x64, 0x02, 0xab, 0x61, 0x15,
	0x36, 0xbb, 0x59, 0xb1, 0xd9, 0x37, 0x0b, 0xab, 0xcb, 0x3d, 0x80, 0xae, 0x36, 0x7b, 0xa4, 0x69,
	0xfe, 0x63, 0x13, 0x96, 0x2b, 0xb6, 0xbf, 0x42, 0x9b, 0xf6, 0x61, 0x2e, 0xf0, 0x9c, 0xd3, 0xc0,
	0x1e, 0x09, 0x2f, 0xa4, 0x3d, 0xc8, 0xbe, 0x55, 0x57, 0xa3, 0xa9, 0xb9, 0x1a, 0xdc, 0x83, 0x14,
	0x53, 0x15, 0x13, 0x10, 0x1f, 0x9c, 0x96, 0xb4, 0x18, 0xae, 0x8c, 0xa1, 0xb2, 0x6f, 0xee, 0xf6,
	0xf9, 0xf4, 0x38, 0x51, 0x4f, 0xaf, 0x38, 0x99, 0x5d, 0x0e, 0xce, 0x8f, 0xef, 0x37, 0xa0, 0x3d,
	0x66, 0x34, 0x16, 0xc2, 0xd1, 0xc2, 0x13, 0x58, 0x27, 0xd4, 0xcf, 0x19, 0x8d, 0x51, 0xa1, 0xcc,
	0x8d, 0xe5, 0xaf, 0xdc, 0xb3, 0x9d, 0x53, 0x3c, 0x5b, 0xf2, 0x25, 0x58, 0x42, 0x9a, 0x8c, 0x26,
	0x96, 0x37, 0xe2, 0x27, 0xd4, 0x73, 0xf1, 0x64, 0xb6, 0x07, 0x0b, 0xbc, 0xe1, 0x90, 0x26, 0x07,
	0x1c, 0x7c, 0xe0, 0x72, 0x61, 0x76, 0xa9, 0x13, 0x0a, 0xcb, 0x94, 0x9f, 0xcd, 0xf9, 0x1c, 0x28,
	0x52, 0x32, 0x2c, 0x3b, 0x93, 0xfc, 0x27, 0x31, 0xa1, 0x7b, 0x62, 0xbf, 0xa0, 0x56, 0x3c, 0x0e,
	0x2c, 0xfb, 0xa5, 0xfd, 0x1a, 0xcd, 0xdd, 0xdc, 0xa0, 0xc3, 0x81, 0x83, 0x71, 0xb0, 0xf7, 0xd2,
	0x7e, 0xcd, 0x49, 0x47, 0x31, 0x75, 0x68, 0x90, 0x84, 0xb1, 0x95, 0xd8, 0x43, 0x3c, 0x75, 0xed,
	0xc1, 0x7c, 0x06, 0x7c, 0x66, 0x0f, 0xcd, 0x57, 0xb0, 0x52, 0x65, 0xc5, 0xc9, 0x02, 0x34, 0xe4,
	0xbe, 0xb5, 0x07, 0x0d, 0xcf, 0x25, 0x04, 0xa6, 0x5d, 0xca, 0x1c, 0xb9, 0x65, 0xf8, 0x9b, 0x0f,
	0xc0, 0xc6, 0x11, 0xf7, 0xa0, 0xa9, 0x8b, 0x27, 0x56, 0xca, 0x51, 0x06, 0xe4, 0xa7, 0x75, 0x1d,
	0x5a, 0x1e, 0xb3, 0xc2, 0x88, 0x0a, 0x25, 0x3b, 0x37, 0x98, 0xf5, 0xd8, 0x27, 0x11, 0x0d, 0xcc,
	0xef, 0x03, 0x51, 0x46, 0xfe, 0x34, 0x4c, 0x2e, 0x3c, 0x1e, 0xd9, 0x84, 0x39, 0x74, 0x5d, 0xf2,
	0x80, 0xa4, 0x85, 0xdf, 0x07, 0xae, 0xf9, 0xe7, 0x0d, 0x4d, 0x5e, 0xc5, 0xe8, 0x17, 0x67, 0x32,
	0x73, 0xbf, 0xab, 0xf1, 0x86, 0x7e, 0xd7, 0xff, 0xe7, 0xc6, 0x80, 0xbb, 0x5b, 0xcd, 0x73, 0xba,
	0x5b, 0x02, 0x3d, 0x37, 0xd2, 0xd3, 0x67, 0xfb, 0xb8, 0x65, 0x97, 0x6c, 0xa6, 0xc2, 0x25, 0x33,
	0x29, 0xac, 0xe7, 0x41, 0xea, 0xe3, 0xd0, 0x76, 0x45, 0xd6, 0xf1, 0xa2, 0x73, 0x5c, 0x36, 0x6c,
	0x54, 0x0f, 0x73, 0x71, 0x61, 0xf0, 0x6f, 0xc0, 0xa6, 0x1e, 0x68, 0x0c, 0xec, 0xe0, 0xf4, 0xb1,
	0xc7, 0x92, 0xea, 0xb5, 0xdc, 0x84, 0x69, 0x25, 0xc9, 0xbb, 0xa1, 0x8e, 0x96, 0x76, 0x44, 0x35,
	0x80, 0x58, 0xe6, 0x4f, 0x8c, 0x62, 0xba, 0x32, 0xa7, 0xce, 0x22, 0xf2, 0x35, 0x98, 0xc6, 0x1c,
	0xa7, 0x81, 0xfb, 0x72, 0xad, 0xce, 0xb8, 0xcb, 0x2e, 0x07, 0x09, 0x1d, 0x0d, 0xb0, 0x03, 0x79,
	0x04, 0xdd, 0x88, 0xc6, 0x2c, 0x0c, 0x6c, 0xdf, 0x8a, 0xed, 0xe0, 0x54, 0xc6, 0x8b, 0xe7, 0xa2,
	0x30, 0x9f, 0xf6, 0xe4, 0x50, 0xf3, 0x07, 0x46, 0x31, 0xcd, 0x39, 0xe0, 0xda, 0xc5, 0xad, 0xe7,
	0xc0, 0x0a, 0xcc, 0xa8, 0xf6, 0x43, 0x7c, 0x70, 0x55, 0xe9, 0x87, 0xb6, 0x6b, 0x8d, 0xb8, 0xc2,
	0x13, 0xf2, 0x59, 0xa7, 0x2a, 0xf9, 0x3e, 0x7e, 0x87, 0x47, 0xf1, 0x73, 0xbe, 0xfc, 0x65, 0xfe,
	0x89, 0x51, 0x0c, 0x17, 0xd5, 0x59, 0xfc, 0x6f, 0x38, 0xa5, 0xcd, 0xab, 0xf1, 0xa6, 0xf3, 0xfa,
	0x77, 0x03, 0xd6, 0x6b, 0xe8, 0x73, 0xad, 0x87, 0xac, 0x17, 0xac, 0xc1, 0xdf, 0xdc, 0xe8, 0x48,
	0xab, 0x24, 0x4e, 0x77, 0x7b, 0x90, 0x7d, 0xd7, 0x18, 0xcd, 0x66, 0xb5, 0xd1, 0xe4, 0x67, 0x46,
	0xf1, 0xdc, 0x84, 0x65, 0x6b, 0x8f, 0x33, 0xa7, 0xed, 0x2e, 0x74, 0x44, 0xde, 0x43, 0xd8, 0xa6,
	0x99, 0xc9, 0xde, 0x21, 0x62, 0xa2, 0x58, 0x42, 0x9c, 0xfd, 0x36, 0xbf, 0xab, 0xa9, 0xb9, 0x74,
	0xf5, 0x64, 0x0b, 0xda, 0xbe, 0xcd, 0x12, 0x2b, 0xe2, 0x6e, 0xa7, 0x58, 0xdc, 0x1c, 0x07, 0x3c,
	0xe5, 0x6e, 0xe5, 0x65, 0x00, 0x6c, 0x54, 0x25, 0x00, 0xd1, 0x85, 0xa9, 0x3f, 0x80, 0xc5, 0x3d,
	0xd7, 0xcd, 0x15, 0x10, 0xfd, 0x3c, 0x57, 0x5a, 0xc6, 0x1b, 0x29, 0x2d, 0xf3, 0xdb, 0xd0, 0xd3,
	0x49, 0xb1, 0xe8, 0xad, 0x69, 0xdd, 0x84, 0xc5, 0x7b, 0xd4, 0xd7, 0xa6, 0xa5, 0xea, 0x7f, 0x43,
	0xd7, 0xff, 0x04, 0x7a, 0x3a, 0x36, 0x8b, 0xcc, 0x15, 0x20, 0x0f, 0x69, 0xb2, 0xe7, 0x6b, 0x44,
	0xcc, 0x4f, 0x60, 0xb9, 0x04, 0x65, 0x91, 0xa2, 0xe1, 0x8d, 0x37, 0xd3, 0xf0, 0xe6, 0x8f, 0x0d,
	0xd8, 0xbc, 0xff, 0x2a, 0x8a, 0x29, 0x63, 0xea, 0xd6, 0x78, 0xa7, 0x5f, 0x68, 0x3e, 0x4e, 0x8d,
	0x67, 0xc4, 0x87, 0x79, 0x09, 0xfa, 0x75, 0xf3, 0x62, 0x91, 0xf9, 0x3d, 0x2d, 0xb0, 0x7b, 0xe0,
	0xb1, 0xe4, 0xee, 0x78, 0xf4, 0x76, 0x69, 0x63, 0x02, 0xd3, 0x63, 0xcf, 0x65, 0xf2, 0xda, 0x07,
	0x7f, 0x9b, 0x9b, 0xda, 0x21, 0xcc, 0xc9, 0xb3, 0xc8, 0x3c, 0x85, 0xcb, 0x78, 0x09, 0xf2, 0x7c,
	0xe8, 0x48, 0x0d, 0xff, 0x0b, 0xbc, 0x71, 0xd9, 0x81, 0x2b, 0x93, 0x06, 0x63, 0x91, 0x39, 0x82,
	0xed, 0x7d, 0x3b, 0x70, 0xb4, 0x06, 0x91, 0x99, 0x40, 0x77, 0xed, 0xa2, 0x27, 0x64, 0xc2, 0xce,
	0xe4, 0xe1, 0x58, 0x64, 0xfe, 0xa8, 0x01, 0x5d, 0xee, 0x9c, 0xa2, 0x53, 0xb9, 0x1f, 0x06, 0xc7,
	0x9c, 0x60, 0xe6, 0x78, 0x4a, 0xd1, 0xf7, 0xa4, 0xc7, 0xc9, 0xa3, 0x51, 0x6c, 0x52, 0x5c, 0xf0,
	0x36, 0x42, 0x9e, 0x70, 0x1f, 0x7c, 0x0b, 0xc4, 0x87, 0x35, 0x8e, 0x7d, 0xe9, 0x85, 0x0b, 0x52,
	0xcf, 0x63, 0x9f, 0x2f, 0x6c, 0xe4, 0x7e, 0x20, 0xa5, 0x89, 0xff, 0xcc, 0xa9, 0x65, 0x3a, 0xaa,
	0x2b, 0xa9, 0xa5, 0xfe, 0xb1, 0xf0, 0xdb, 0x67, 0x55, 0xbf, 0xfd, 0x06, 0xf4, 0x5c, 0x7a, 0x6c,
	0x73, 0xd5, 0xe6, 0x39, 0x61, 0x80, 0x43, 0xb5, 0x84, 0x7b, 0x2c, 0xe1, 0x07, 0x4e, 0x18, 0xf0,
	0x01, 0x3f, 0x80, 0xf5, 0x14, 0x93, 0x9f, 0x2f, 0xcb, 0x7e, 0x61, 0x27, 0x76, 0x8c, 0x1d, 0xe6,
	0xb0, 0xc3, 0x8a, 0x6c, 0xe6, 0xdc, 0xd9, 0xc3, 0xc6, 0xe7, 0xb1, 0x6f, 0xfe, 0xa9, 0x01, 0x0b,
	0xf7, 0x72, 0x0f, 0x9a, 0xbb, 0x64, 0x25, 0x47, 0xdb, 0xa8, 0x70, 0xb4, 0xdf, 0x83, 0x45, 0x05,
	0x49, 0x61, 0xd0, 0x42, 0x0e, 0x46, 0x2e, 0xbd, 0x03, 0x0a, 0x44, 0x61, 0x95, 0x32, 0x46, 0x25,
	0xbf, 0xcc, 0x3b, 0xb0, 0x26, 0xd5, 0x89, 0x17, 0x0c, 0xb3, 0xfd, 0xe2, 0x42, 0xa3, 0x84, 0xc7,
	0x86, 0x16, 0x1e, 0x9b, 0xff, 0x62, 0xc0, 0x7a, 0x65, 0x27, 0x16, 0x91, 0x6f, 0xc3, 0x0a, 0x86,
	0x1a, 0x62, 0x13, 0x9c, 0x30, 0x38, 0xb6, 0x14, 0x23, 0xba, 0xa9, 0x6a, 0x25, 0x4d, 0x42, 0x06,
	0x18, 0xa1, 0x64, 0x9f, 0xdc, 0xe2, 0x91, 0x7d, 0x6d, 0xf5, 0x48, 0x46, 0xb8, 0xaf, 0x7d, 0x95,
	0x8c, 0xce, 0x57, 0x95, 0x33, 0x48, 0x44, 0x59, 0x46, 0x53, 0x5f, 0xc6, 0x7b, 0xa8, 0x49, 0xf9,
	0x2c, 0xb2, 0x65, 0x54, 0x1e, 0x16, 0xf3, 0xe7, 0x0d, 0x58, 0x29, 0x63, 0xb2, 0x88, 0x47, 0x3d,
	0xa9, 0x54, 0xe7, 0xab, 0x6c, 0x0f, 0x3a, 0x52, 0xb4, 0x71, 0xfc, 0xca, 0xd8, 0xab, 0x71, 0xbe,
	0xd8, 0xab, 0x59, 0x21, 0x12, 0xbb, 0xb0, 0xa4, 0x20, 0xd1, 0xc0, 0xb5, 0x12, 0x26, 0x6d, 0xb5,
	0xc2, 0xad, 0xfb, 0x81, 0xfb, 0x8c, 0x91, 0x8f, 0xa1, 0xe3, 0x87, 0xce, 0xa9, 0x18, 0x97, 0x6d,
	0xcc, 0x20, 0xf3, 0x76, 0x55, 0xe6, 0x55, 0x2d, 0xeb, 0xd6, 0xe3, 0xd0, 0x39, 0x15, 0x5f, 0xe0,
	0xa7, 0x3f, 0x31, 0x65, 0xef, 0x31, 0x2b, 0x0b, 0xd6, 0xf0, 0x04, 0xcd, 0x0d, 0x3a, 0x1e, 0x7b,
	0x9a, 0x82, 0xfa, 0x1f, 0x42, 0x3b, 0xeb, 0x3b, 0xe9, 0xc8, 0xf7, 0xa0, 0x99, 0x78, 0x91, 0xe4,
	0x02, 0xff, 0x69, 0xde, 0x83, 0xd5, 0xbd, 0x97, 0x76, 0xec, 0x9e, 0xbd, 0x1b, 0x1a, 0xdd, 0x86,
	0x46, 0xd7, 0xdc, 0x80, 0xb5, 0x2a, 0x2a, 0xa8, 0xb3, 0x09, 0x07, 0xbe, 0xcd, 0x19, 0x44, 0xab,
	0x6d, 0xc7, 0x89, 0x95, 0x30, 0xa9, 0x28, 0x5b, 0xf8, 0xfd, 0x8c, 0x91, 0x55, 0x98, 0x95, 0x1b,
	0x20, 0x44, 0x6b, 0x86, 0x72, 0xb6, 0x9b, 0xdf, 0x82, 0xf9, 0xa7, 0x4a, 0x4c, 0xab, 0x51, 0x30,
	0xea, 0x28, 0x34, 0x54, 0x0a, 0x3f, 0x37, 0x60, 0xf9, 0xf0, 0x3c, 0xb2, 0x39, 0x81, 0x1b, 0xe4,
	0x57, 0x01, 0xf2, 0x85, 0x48, 0xff, 0xf8, 0x4a, 0xf1, 0x00, 0x16, 0x4e, 0x8f, 0xd2, 0xa3, 0x60,
	0x23, 0xa6, 0x8b, 0x36, 0xe2, 0xa3, 0x62, 0x38, 0x3f, 0x83, 0x23, 0x68, 0xe1, 0x89, 0xca, 0x86,
	0x42, 0xa0, 0xbf, 0x06, 0x2b, 0x87, 0x15, 0xc2, 0x67, 0xfe, 0xb7, 0x01, 0x4b, 0x77, 0xed, 0xc4,
	0x39, 0xc9, 0x76, 0x92, 0x2f, 0x3c, 0x35, 0xd1, 0x46, 0x6e, 0xa2, 0xc9, 0x56, 0x71, 0xe9, 0x8f,
	0xa6, 0xf2, 0xc5, 0x7f, 0xeb, 0xcd, 0x17, 0xff, 0x68, 0x4a, 0x5b, 0xfe, 0x37, 0x8b, 0xeb, 0x9b,
	0x9e, 0xbc, 0xbe, 0x47, 0x53, 0xfa, 0x0a, 0xf9, 0x66, 0xb9, 0xf6, 0x6b, 0x69, 0x83, 0xf8, 0x4f,
	0xee, 0xaa, 0x87, 0x11, 0x8d, 0xed, 0xf4, 0xf8, 0xb4, 0x07, 0xd9, 0xf7, 0xdd, 0x16, 0xcc, 0xd8,
	0x7c, 0xc5, 0xdc, 0xed, 0x2b, 0xae, 0x9f, 0x45, 0xe6, 0x47, 0xb0, 0xc4, 0x55, 0x2e, 0x87, 0x89,
	0x30, 0x45, 0x72, 0x45, 0xf1, 0x98, 0xf1, 0x77, 0x75, 0xa8, 0x64, 0x9e, 0x08, 0x5f, 0x52, 0xed,
	0xce, 0x22, 0xb2, 0x0d, 0x9d, 0x24, 0x4c, 0x6c, 0x5f, 0x4b, 0xe1, 0x01, 0x82, 0x44, 0x44, 0x70,
	0x1b, 0x5a, 0x31, 0xa2, 0xa7, 0x89, 0x83, 0x75, 0x75, 0xf5, 0x2a, 0xb9, 0x14, 0xcf, 0xfc, 0x99,
	0x01, 0x1d, 0xa5, 0x81, 0xcf, 0x31, 0x49, 0xb2, 0xa3, 0x85, 0xbf, 0xc9, 0xff, 0x03, 0xc0, 0xb5,
	0xaa, 0xb5, 0x4b, 0xab, 0x25, 0xca, 0x18, 0x3c, 0xb4, 0xed, 0xf4, 0x27, 0x5f, 0x19, 0x7e, 0x48,
	0xb5, 0x28, 0x3e, 0xb8, 0x94, 0xe2, 0x0f, 0x2d, 0x68, 0x91, 0x90, 0x3d, 0x8c, 0x69, 0xe8, 0xab,
	0xc8, 0x8b, 0x45, 0xb3, 0xf4, 0x07, 0x24, 0x64, 0x2f, 0x99, 0xb4, 0x23, 0xe6, 0x4f, 0x0d, 0x58,
	0x13, 0x29, 0xeb, 0x92, 0x6d, 0xfc, 0xbf, 0x66, 0xe5, 0xcc, 0xaf, 0xc3, 0x7a, 0xe5, 0x54, 0xc5,
	0x26, 0x1f, 0xdb, 0x9e, 0x4f, 0x35, 0x13, 0x05, 0x02, 0x84, 0x7d, 0x87, 0xb0, 0x7c, 0x8f, 0x96,
	0xed, 0xff, 0x79, 0x8c, 0xdb, 0x4d, 0x20, 0x9a, 0x02, 0xcd, 0xa7, 0xdf, 0x1e, 0xf4, 0x54, 0x2d,
	0x8a, 0x03, 0xad, 0xc1, 0x4a, 0x79, 0x20, 0x16, 0x71, 0xb8, 0x98, 0x3c, 0x87, 0x7c, 0x2a, 0xac,
	0x33, 0x0f, 0x75, 0xd6, 0x61, 0xb5, 0x02, 0xce, 0x22, 0xb3, 0x0b, 0x1d, 0x79, 0x1d, 0x8e, 0xe9,
	0x94, 0xbf, 0x6d, 0xc2, 0x5c, 0x9a, 0x53, 0x52, 0x83, 0x0c, 0x43, 0x0b, 0x32, 0xb6, 0xa0, 0x8d,
	0x0d, 0x6a, 0xa6, 0x97, 0x03, 0xd0, 0x7f, 0x5a, 0x83, 0x59, 0xce, 0x6c, 0x1a, 0x4b, 0xe1, 0x92,
	0x5f, 0x5c, 0x3e, 0xfc, 0xd7, 0xb1, 0xe7, 0xf0, 0x65, 0x09, 0xaf, 0x29, 0xfb, 0xe6, 0x6d, 0x4e,
	0x38, 0x8a, 0x42, 0x46, 0x63, 0x14, 0xac, 0xf6, 0x20, 0xfb, 0xe6, 0x96, 0x25, 0x8c, 0xbd, 0xa1,
	0x17, 0x58, 0x92, 0xac, 0x10, 0xae, 0x79, 0x01, 0x3c, 0x14, 0xc4, 0xb7, 0xa1, 0x33, 0x8e, 0x30,
	0x53, 0xc0, 0xa5, 0x42, 0x7a, 0x9c, 0x20, 0x40, 0x5c, 0x6c, 0x44, 0x40, 0x2e, 0x10, 0x3c, 0x57,
	0xde, 0x20, 0xb6, 0x65, 0xbb, 0xe7, 0x2a, 0xfd, 0xf1, 0x84, 0x75, 0xd5, 0xfe, 0xcf, 0xf8, 0x39,
	0x5b, 0x83, 0xd9, 0x21, 0x0d, 0x5c, 0x1a, 0xcb, 0x74, 0xb0, 0xfc, 0xe2, 0x46, 0xc3, 0xf7, 0x8e,
	0xc4, 0xe9, 0x13, 0x17, 0x34, 0x2d, 0xdf, 0x3b, 0xc2, 0x43, 0xb6, 0x09, 0x73, 0xc7, 0x9e, 0x2f,
	0xbc, 0x6d, 0x91, 0xfa, 0x6d, 0xf1, 0x6f, 0xc5, 0x79, 0xec, 0x68, 0xce, 0x36, 0xa3, 0x4e, 0x6a,
	0x3c, 0xc5, 0xfd, 0x48, 0x5b, 0x42, 0x0a, 0xf9, 0xce, 0x45, 0x3d, 0xde, 0x7d, 0x00, 0x4b, 0xfb,
	0x27, 0xd4, 0x39, 0xe5, 0xdb, 0x76, 0xff, 0x95, 0xcc, 0xfb, 0xdc, 0x96, 0x3b, 0xa4, 0x1c, 0xa5,
	0xea, 0xbc, 0x21, 0xee, 0x1b, 0x8a, 0xd4, 0x1f, 0x18, 0x40, 0x8a, 0x84, 0x58, 0x44, 0xbe, 0xca,
	0x4f, 0xbd, 0xc7, 0x92, 0xb3, 0x49, 0xb5, 0x11, 0x0f, 0x85, 0xf9, 0xeb, 0xb0, 0x10, 0x70, 0x97,
	0x2a, 0xef, 0x38, 0xe9, 0x82, 0x71, 0x3e, 0x08, 0x83, 0xfb, 0x69, 0x5f, 0xf3, 0x5b, 0x40, 0x0e,
	0x69, 0xc2, 0x1b, 0xef, 0xda, 0x8c, 0xa6, 0x05, 0x07, 0xbb, 0x3c, 0xda, 0x38, 0x0e, 0xd9, 0xc4,
	0x19, 0x08, 0x14, 0xf3, 0x00, 0x2d, 0xbe, 0x4e, 0x81, 0x45, 0xe4, 0x0e, 0xac, 0xba, 0xe3, 0xc8,
	0xf7, 0x1c, 0x3b, 0xf1, 0x5e, 0x88, 0x18, 0x49, 0x3d, 0x8d, 0xcb, 0x4a, 0xe3, 0x13, 0x0c, 0x8f,
	0x59, 0x62, 0xfe, 0xb5, 0x01, 0xf3, 0x9c, 0xd0, 0x03, 0xcf, 0x47, 0x42, 0xba, 0xe8, 0x1b, 0xb5,
	0xa2, 0xdf, 0xd0, 0x44, 0x5f, 0x15, 0x92, 0x66, 0xbd, 0x90, 0x4c, 0x57, 0x0a, 0xc9, 0x4c, 0x9d,
	0x90, 0xcc, 0x16, 0x84, 0xc4, 0x7c, 0x08, 0x0b, 0x72, 0xdd, 0x7c, 0xba, 0x9c, 0x6b, 0x1f, 0x94,
	0xc5, 0x60, 0xa3, 0xc8, 0xb9, 0x74, 0x69, 0x8a, 0x28, 0xfc, 0x85, 0x01, 0x8b, 0x1a, 0x25, 0x16,
	0x91, 0x47, 0x93, 0xb8, 0x57, 0xb7, 0x21, 0x55, 0x3c, 0x25, 0x0f, 0x61, 0x35, 0x13, 0x0e, 0xea,
	0x2a, 0x94, 0x26, 0xc9, 0x08, 0x49, 0x65, 0x84, 0xba, 0xd9, 0xe6, 0xfc, 0x43, 0x03, 0x16, 0x1e,
	0x8a, 0x69, 0xa6, 0xf9, 0xce, 0x8b, 0xde, 0x9e, 0x35, 0x98, 0x0d, 0x8f, 0x8f, 0x19, 0x4d, 0xcd,
	0xa1, 0xfc, 0xe2, 0x06, 0x54, 0xdc, 0x65, 0xcb, 0x34, 0x0b, 0x7e, 0x90, 0x7d, 0x00, 0xdc, 0x4c,
	0x5c, 0x1a, 0xee, 0xcf, 0xc2, 0x9d, 0xeb, 0x85, 0x18, 0x41, 0x99, 0xed, 0x2d, 0xce, 0x5c, 0x71,
	0xd2, 0xda, 0xc7, 0xe9, 0x4f, 0xed, 0xa8, 0xb7, 0xf4, 0xa3, 0xbe, 0x0f, 0xed, 0xac, 0x0b, 0xe9,
	0x42, 0xfb, 0x49, 0x18, 0xd0, 0x43, 0x1a, 0x30, 0xda, 0x9b, 0x22, 0x2b, 0xd0, 0xfb, 0x24, 0xf0,
	0x5f, 0x3f, 0x09, 0x13, 0x6c, 0xe6, 0x78, 0x3d, 0x83, 0x2c, 0x41, 0x97, 0x43, 0x73, 0x50, 0xc3,
	0x3c, 0x84, 0x45, 0x6d, 0x1a, 0x2c, 0x7a, 0x93, 0xc3, 0xc5, 0x57, 0x8e, 0x5e, 0x4d, 0xea, 0x14,
	0xe1, 0x87, 0xf9, 0x4d, 0x00, 0x99, 0x74, 0x7b, 0x4b, 0xed, 0xd3, 0x85, 0x4e, 0x46, 0x80, 0x45,
	0xe6, 0xd7, 0xa1, 0x27, 0x27, 0xf9, 0x20, 0x8c, 0xf7, 0xa2, 0x34, 0x19, 0xc5, 0x7d, 0x47, 0xe9,
	0xb1, 0x27, 0xf6, 0xb0, 0xc6, 0x41, 0xfb, 0x18, 0xfd, 0x3b, 0xb5, 0x2f, 0xe6, 0x1e, 0xe7, 0xa5,
	0x2d, 0x3b, 0x7b, 0x56, 0x20, 0xcc, 0x1c, 0xce, 0xeb, 0x9b, 0xb0, 0x24, 0xbd, 0x01, 0xe5, 0x58,
	0xbd, 0x89, 0x32, 0x5a, 0x01, 0x52, 0x24, 0xc0, 0x22, 0xf3, 0xdf, 0xa6, 0x61, 0x51, 0xc9, 0xfa,
	0xc8, 0x62, 0x8c, 0x8a, 0x1a, 0x08, 0xb1, 0xda, 0x52, 0x99, 0x03, 0x81, 0x69, 0xc5, 0xf8, 0xe2,
	0x6f, 0xce, 0x0d, 0x97, 0x32, 0x27, 0xb5, 0xbb, 0xe2, 0x83, 0x9f, 0x88, 0xdf, 0x09, 0x03, 0xad,
	0xdc, 0x64, 0x8e, 0x03, 0x90, 0xcc, 0x02, 0x34, 0x4e, 0x22, 0x29, 0xc3, 0x8d, 0x93, 0x88, 0x5c,
	0x87, 0x05, 0xc6, 0x87, 0x66, 0xf4, 0x95, 0xac, 0x26, 0x9b, 0x95, 0xf7, 0x7e, 0xf6, 0x88, 0x1e,
	0xd2, 0x57, 0xa2, 0x9c, 0xec, 0x26, 0x90, 0x30, 0xc2, 0xcb, 0x7f, 0x15, 0xb3, 0x25, 0x52, 0xe1,
	0x69, 0x4b, 0x86, 0x9d, 0x26, 0xda, 0xe7, 0x94, 0x44, 0xfb, 0x16, 0xb4, 0xf9, 0x5f, 0x51, 0xe3,
	0x24, 0xcc, 0xe6, 0x1c, 0x07, 0x60, 0x65, 0x13, 0xd7, 0x72, 0x22, 0xc6, 0xe3, 0xad, 0x20, 0xb5,
	0x1c, 0x46, 0x79, 0xbc, 0x79, 0x13, 0xe6, 0x30, 0xce, 0xe3, 0x8d, 0xe2, 0xda, 0xb4, 0xc5, 0x23,
	0x3d, 0xd9, 0xd3, 0x0b, 0x7c, 0x2a, 0x52, 0x4f, 0x68, 0xe4, 0xdb, 0x83, 0x36, 0x42, 0x0e, 0x9c,
	0x30, 0xe0, 0x46, 0x5e, 0x34, 0x3b, 0xa1, 0x1f, 0xc6, 0xa9, 0x91, 0x47, 0xd0, 0x3e, 0x87, 0x70,
	0x84, 0x98, 0xda, 0xee, 0x6b, 0xcb, 0x09, 0x5f, 0xd0, 0x18, 0xad, 0x70, 0x7b, 0x00, 0x08, 0xda,
	0xe7, 0x10, 0x8e, 0x80, 0xcc, 0x3c, 0x1a, 0x27, 0x49, 0x18, 0x48, 0x4b, 0x0c, 0x1c, 0x74, 0x17,
	0x21, 0xdc, 0xd3, 0x43, 0x04, 0x41, 0xe6, 0x68, 0x88, 0x35, 0x09, 0xed, 0x01, 0xf6, 0x1a, 0x70,
	0xd8, 0xdd, 0x21, 0x77, 0xab, 0x04, 0x91, 0xa1, 0x2c, 0x40, 0x98, 0x45, 0x02, 0x43, 0xf5, 0x3e,
	0x95, 0xa8, 0xf7, 0xa9, 0xa5, 0x02, 0x9a, 0xe5, 0x52, 0x01, 0x8d, 0xf9, 0x9b, 0xb0, 0xb4, 0xe7,
	0xba, 0x85, 0xec, 0x69, 0xb1, 0xaa, 0x46, 0xb9, 0x68, 0xab, 0xab, 0xaa, 0x41, 0xd1, 0x55, 0xc5,
	0x0d, 0xdd, 0xc2, 0x15, 0x20, 0x45, 0xea, 0x2c, 0x32, 0xad, 0xd4, 0xd9, 0xfc, 0x45, 0x0d, 0x9b,
	0x79, 0xad, 0xc5, 0x91, 0xd7, 0x61, 0x55, 0xbf, 0x6f, 0x92, 0x1a, 0xd5, 0x74, 0x30, 0x07, 0x57,
	0x6a, 0x60, 0x11, 0x39, 0x00, 0x52, 0x9a, 0x54, 0x1a, 0x8a, 0x4d, 0x9c, 0x55, 0xaf, 0x30, 0x2b,
	0x66, 0xde, 0x87, 0xd5, 0xc3, 0xd2, 0xad, 0x20, 0x5f, 0xf8, 0xcd, 0xc2, 0x18, 0xfc, 0xcc, 0xa5,
	0x81, 0x76, 0xaf, 0x70, 0x84, 0x99, 0xb9, 0x01, 0x6b, 0x55, 0x64, 0x58, 0x64, 0xee, 0xc1, 0xd2,
	0xbd, 0x52, 0x2a, 0xfc, 0xcd, 0x88, 0xaf, 0x00, 0xb9, 0x57, 0x4e, 0x70, 0x13, 0x54, 0xab, 0xf7,
	0x03, 0xfb, 0xc8, 0xa7, 0xcf, 0xec, 0x21, 0xe3, 0x2c, 0xfb, 0x10, 0xd5, 0xa5, 0x0a, 0x63, 0x11,
	0xb9, 0x06, 0xd3, 0x89, 0x3d, 0x4c, 0x15, 0xdc, 0xa2, 0xca, 0x9f, 0x67, 0xf6, 0x70, 0x80, 0x8d,
	0xa6, 0x03, 0x4d, 0x1e, 0x9c, 0xaf, 0xc2, 0x6c, 0x62, 0x0f, 0xf3, 0xca, 0xaf, 0x99, 0xc4, 0x1e,
	0x8a, 0xab, 0x80, 0x0b, 0x50, 0x51, 0xe6, 0x7d, 0x4c, 0x90, 0xaa, 0x17, 0x08, 0x99, 0x41, 0xd8,
	0x85, 0x25, 0x67, 0x1c, 0xc7, 0x34, 0x48, 0xca, 0x0a, 0x53, 0x36, 0x64, 0xe5, 0x74, 0xa7, 0xb0,
	0x51, 0x4d, 0x86, 0x45, 0xe4, 0x93, 0x4a, 0xd1, 0x10, 0x4b, 0xbf, 0x3a, 0x41, 0x34, 0x24, 0x89,
	0xb2, 0x80, 0xfc, 0xd4, 0x80, 0xe5, 0xc3, 0x88, 0x62, 0x4d, 0x91, 0x3d, 0xa2, 0x9f, 0x85, 0x81,
	0xd0, 0xf0, 0x2b, 0xea, 0x05, 0x58, 0x37, 0xbd, 0xdf, 0x17, 0x55, 0xcc, 0x18, 0x04, 0xcb, 0x94,
	0x50, 0x5e, 0xc5, 0xcc, 0x63, 0x17, 0x79, 0x59, 0x81, 0x51, 0x24, 0xb3, 0x02, 0x4a, 0x5d, 0x8b,
	0x9d, 0xd8, 0xf2, 0x9e, 0x16, 0xd3, 0x86, 0x4f, 0x28, 0x75, 0x0f, 0x39, 0x48, 0xec, 0xc5, 0x51,
	0x9e, 0x64, 0x9a, 0x49, 0xec, 0x23, 0x11, 0x3e, 0x70, 0x30, 0xee, 0x87, 0xf0, 0x27, 0x5b, 0x89,
	0x7d, 0xc4, 0x9d, 0x22, 0xf3, 0x0f, 0x9b, 0x5a, 0x6d, 0x74, 0xbe, 0x2e, 0x4c, 0x37, 0xe4, 0x1c,
	0xc5, 0xdf, 0x6f, 0xb0, 0xa9, 0xba, 0x2e, 0x9e, 0x3e, 0x43, 0x17, 0xcf, 0x9c, 0xa5, 0x8b, 0x67,
	0xcf, 0xd2, 0xc5, 0xad, 0xb3, 0x75, 0xf1, 0xdc, 0x44, 0x5d, 0xdc, 0xd6, 0x74, 0xf1, 0xbb, 0xb0,
	0xc8, 0x2d, 0x8c, 0x50, 0xb8, 0x62, 0x0d, 0x22, 0x86, 0xeb, 0x72, 0x30, 0xea, 0x5c, 0x5c, 0xc7,
	0xc7, 0xb0, 0xc4, 0xc4, 0x4e, 0x2b, 0xba, 0xae, 0x53, 0x71, 0x51, 0x5d, 0x16, 0x87, 0xc1, 0x22,
	0xcb, 0x81, 0xa8, 0xef, 0x3e, 0x2a, 0x95, 0x1b, 0xe0, 0x4d, 0x91, 0x0c, 0x81, 0xb6, 0xa1, 0x93,
	0x27, 0x11, 0xd3, 0x93, 0x0f, 0x59, 0x16, 0x91, 0x99, 0xcf, 0x4b, 0xb5, 0x00, 0x79, 0x77, 0x74,
	0x81, 0x3a, 0xf2, 0x26, 0x4a, 0x91, 0x6f, 0x2d, 0x57, 0x94, 0xe3, 0x43, 0x9c, 0xfe, 0x64, 0xe6,
	0xdf, 0x35, 0xa0, 0x9d, 0xb5, 0x9c, 0x55, 0xeb, 0xc9, 0x8d, 0x77, 0x5e, 0x7d, 0x26, 0x6f, 0xc3,
	0x58, 0x56, 0x7a, 0x96, 0x36, 0x0b, 0x17, 0xb2, 0x99, 0x37, 0x3f, 0xe3, 0x80, 0x92, 0x0b, 0x84,
	0xb2, 0x26, 0x64, 0x47, 0x35, 0x0e, 0x99, 0x37, 0x9f, 0xd8, 0xc9, 0x98, 0x49, 0xe1, 0x91, 0x5f,
	0x7a, 0x08, 0x30, 0x5b, 0x1b, 0x02, 0xb4, 0xb4, 0x10, 0xe0, 0xad, 0x0b, 0x35, 0x73, 0xe5, 0xd7,
	0x56, 0x94, 0x9f, 0xf9, 0x03, 0x03, 0x43, 0x93, 0xc7, 0x94, 0xb1, 0x34, 0xef, 0x5a, 0x4e, 0x38,
	0x6f, 0x41, 0x5b, 0x7f, 0x76, 0xd8, 0x1d, 0xcc, 0x0d, 0x53, 0x0f, 0x4f, 0xe7, 0x73, 0xb3, 0x58,
	0x61, 0x7f, 0x0d, 0xe6, 0x32, 0x67, 0x70, 0x01, 0x00, 0xef, 0xc8, 0x70, 0x76, 0xbd, 0x29, 0xd2,
	0x82, 0xe6, 0xc7, 0xcf, 0x3e, 0xed, 0x19, 0xe6, 0x4d, 0x74, 0xf5, 0xf3, 0x49, 0xb0, 0x08, 0x93,
	0xdc, 0x4c, 0xba, 0x11, 0xf2, 0x21, 0x81, 0xc7, 0x84, 0x0b, 0xf1, 0x95, 0x62, 0x29, 0x0d, 0xe6,
	0xdb, 0xa4, 0xf0, 0x95, 0xaf, 0x72, 0xfe, 0xb8, 0x54, 0x1c, 0x93, 0xe3, 0x8b, 0x0b, 0x9d, 0xac,
	0x72, 0x1e, 0x7d, 0x35, 0xd1, 0xb5, 0x23, 0xab, 0xe5, 0xd1, 0x5f, 0xbb, 0x0d, 0xab, 0x19, 0x8e,
	0x7c, 0xce, 0x20, 0x70, 0x1b, 0x5a, 0x41, 0xbe, 0x7c, 0xe0, 0x93, 0xba, 0x78, 0xbe, 0x77, 0x4a,
	0xb5, 0xf2, 0x8b, 0x36, 0x87, 0x60, 0x96, 0x75, 0xf7, 0x9f, 0xa6, 0x35, 0x6d, 0x96, 0xef, 0x17,
	0x31, 0xe1, 0x4a, 0x65, 0xc3, 0xf3, 0xc0, 0xa5, 0xc7, 0x5e, 0x40, 0x39, 0xe3, 0x76, 0xe0, 0x52,
	0x25, 0xce, 0x43, 0x31, 0x8f, 0x9e, 0x51, 0x8b, 0x21, 0x81, 0xbd, 0x06, 0xb9, 0x0a, 0x97, 0xab,
	0x05, 0x86, 0x7e, 0xfe, 0x88, 0xfa, 0xd1, 0xed, 0x5e, 0x93, 0xbc, 0x07, 0xd7, 0x26, 0xa1, 0x3c,
	0xc0, 0x7c, 0xe2, 0xed, 0xde, 0x74, 0xed, 0x9c, 0xf7, 0xc3, 0x51, 0x64, 0xc7, 0x7c, 0xbc, 0x19,
	0xb2, 0x0d, 0x5b, 0x35, 0xc4, 0xd8, 0xd8, 0x4f, 0x7a, 0xb3, 0x67, 0x4d, 0xe8, 0x4e, 0xaf, 0x75,
	0xbe, 0x09, 0xdd, 0xe9, 0xcd, 0x91, 0x77, 0xc1, 0xac, 0x44, 0x7c, 0x24, 0x1e, 0x9b, 0x48, 0x26,
	0xb4, 0xc9, 0x0d, 0xb8, 0x3e, 0x01, 0x2f, 0x9f, 0x3e, 0x90, 0x77, 0xe0, 0xea, 0x04, 0x4c, 0xb9,
	0x88, 0x4e, 0xed, 0xc0, 0x4f, 0xed, 0x38, 0xf1, 0x6c, 0x5f, 0xe2, 0xcd, 0x93, 0xcb, 0xb0, 0x59,
	0x89, 0xf7, 0x69, 0x98, 0xd0, 0x5e, 0xb7, 0x76, 0xfb, 0x9e, 0xc6, 0xd4, 0x0f, 0x6d, 0xb7, 0xb7,
	0xb0, 0x3b, 0xd6, 0xaa, 0x6a, 0xd2, 0xb2, 0xd0, 0x42, 0xc7, 0x14, 0xac, 0xca, 0x8e, 0x3e, 0x72,
	0x8a, 0x21, 0xd2, 0x93, 0x3d, 0xa3, 0xa6, 0x59, 0xac, 0xb2, 0xd7, 0xd8, 0x3d, 0x85, 0x79, 0xb5,
	0xfe, 0x8c, 0x6c, 0xc2, 0xaa, 0xfa, 0xad, 0x0e, 0xb4, 0x06, 0x44, 0x6d, 0x7a, 0x10, 0x7b, 0x34,
	0x70, 0x7b, 0x06, 0x59, 0x86, 0x45, 0x15, 0xbe, 0xe7, 0xfb, 0xbd, 0x46, 0x11, 0x78, 0xcf, 0x7e,
	0xdd, 0x6b, 0xee, 0xfe, 0xab, 0xfe, 0x1c, 0x2a, 0xaf, 0x2f, 0x2a, 0x0a, 0x5c, 0xd6, 0xa0, 0x8e,
	0x5f, 0x10, 0xb8, 0x0c, 0x47, 0x48, 0x49, 0xcf, 0xa8, 0x45, 0x78, 0x12, 0xc6, 0x23, 0x9b, 0x4f,
	0xea, 0x3a, 0xec, 0x54, 0x22, 0xec, 0x87, 0xe2, 0xc2, 0xc0, 0x7b, 0x41, 0x7b, 0xcd, 0xe2, 0x5e,
	0x65, 0x58, 0x4f, 0x69, 0x7c, 0x4c, 0x9d, 0xa4, 0x37, 0xbd, 0xfb, 0x57, 0x86, 0x16, 0x44, 0xe3,
	0x0a, 0x2e, 0xc1, 0x46, 0x01, 0xa4, 0xce, 0x7d, 0x53, 0x5b, 0x38, 0x6f, 0xbd, 0xff, 0x2a, 0xa2,
	0x71, 0xd2, 0x33, 0x2a, 0x9a, 0x06, 0x61, 0x78, 0xea, 0xd1, 0x5e, 0x83, 0x6c, 0x43, 0xbf, 0xd0,
	0xa4, 0x14, 0x71, 0xf7, 0xfe, 0xa3, 0x45, 0x76, 0xb4, 0x15, 0x17, 0x10, 0xee, 0xf4, 0xfe, 0xb3,
	0xb5, 0xbb, 0xa7, 0x54, 0x71, 0xe0, 0x3c, 0x17, 0xa1, 0x23, 0xf8, 0x81, 0xa0, 0xde, 0x14, 0xe9,
	0xc1, 0xfc, 0x3d, 0x59, 0x20, 0x81, 0x10, 0x83, 0x74, 0x95, 0x2b, 0xe0, 0x5e, 0x63, 0xd7, 0x87,
	0x76, 0x76, 0x97, 0x43, 0xd6, 0x61, 0x39, 0xfb, 0xb0, 0xd4, 0x15, 0x2e, 0xc3, 0x62, 0xde, 0x90,
	0x52, 0xda, 0x80, 0x95, 0x1c, 0x98, 0x5f, 0x7f, 0xf4, 0x1a, 0x3a, 0x9d, 0xec, 0x0a, 0xae, 0xd7,
	0xdc, 0xfd, 0x61, 0xce, 0xdb, 0xcf, 0xd2, 0x6c, 0x41, 0xce, 0xdb, 0x14, 0x54, 0xcd, 0xdb, 0xb4,
	0x55, 0x6e, 0xb8, 0x41, 0xb6, 0xb2, 0x0a, 0xa0, 0xb4, 0xe9, 0x01, 0x65, 0x89, 0xf7, 0x02, 0xa5,
	0xe1, 0x4a, 0xc6, 0xdd, 0xb4, 0x51, 0xe5, 0x6e, 0xf3, 0xce, 0x4f, 0xae, 0xa9, 0xe6, 0x8d, 0x84,
	0xb0, 0x51, 0xf7, 0x6e, 0x9e, 0xbc, 0xa7, 0x99, 0xed, 0xfa, 0x7f, 0x05, 0xd0, 0xbf, 0x71, 0x3e,
	0x44, 0x16, 0x99, 0x53, 0xe4, 0x19, 0x2c, 0x16, 0x9e, 0xc8, 0x13, 0xed, 0xf6, 0xb3, 0xfc, 0x0c,
	0xbf, 0xbf, 0x3d, 0xb1, 0x1d, 0xa9, 0xfa, 0xc5, 0xb0, 0x26, 0x7f, 0x9b, 0xf2, 0x6e, 0x31, 0x63,
	0x58, 0xfd, 0x5a, 0xbe, 0xff, 0xde, 0xb9, 0xf0, 0x70, 0xb4, 0x5f, 0x83, 0x5e, 0x71, 0x95, 0x64,
	0x7b, 0x12, 0x0f, 0x38, 0xfd, 0x9d, 0xc9, 0x08, 0x48, 0xf8, 0x7b, 0x40, 0xca, 0x0f, 0xbd, 0x89,
	0xe6, 0x3e, 0x55, 0x3e, 0x43, 0xef, 0x9b, 0x67, 0xa1, 0x20, 0xf9, 0xcf, 0x60, 0xa9, 0xf4, 0x5c,
	0x9a, 0x68, 0xf3, 0xaa, 0x7a, 0xe5, 0xdd, 0xbf, 0x7a, 0x06, 0x46, 0x46, 0xbb, 0xf8, 0x5a, 0xb9,
	0x40, 0xbb, 0xe2, 0x25, 0x75, 0x81, 0x76, 0xd5, 0x73, 0x67, 0x73, 0x8a, 0xc4, 0xb0, 0x59, 0xfb,
	0x08, 0x98, 0x68, 0xc2, 0x37, 0xe9, 0xc1, 0x72, 0xff, 0x4b, 0xe7, 0xc4, 0xc4, 0x31, 0xc7, 0xd0,
	0xaf, 0x7f, 0x72, 0x4b, 0x34, 0x52, 0x13, 0xdf, 0x0d, 0xf7, 0x77, 0xcf, 0x8b, 0x9a, 0x2e, 0xb5,
	0xf6, 0xa5, 0xa7, 0xbe, 0xd4, 0x49, 0x0f, 0x4a, 0xf5, 0xa5, 0x4e, 0x7c, 0x3a, 0x6a, 0x4e, 0x91,
	0x63, 0x58, 0xad, 0x7c, 0x1a, 0x47, 0xb4, 0x64, 0x7b, 0xdd, 0x3b, 0xc0, 0xfe, 0x3b, 0xe7, 0xc0,
	0xc2, 0x71, 0x3c, 0x58, 0xab, 0x2e, 0x9b, 0x24, 0x1a, 0x89, 0xda, 0x92, 0xcf, 0xfe, 0xbb, 0xe7,
	0x41, 0xc3, 0xa1, 0x7e, 0x5b, 0xf3, 0x3b, 0xd2, 0x22, 0x49, 0x52, 0x57, 0x0f, 0xaf, 0x14, 0x69,
	0xf6, 0xaf, 0x9d, 0x89, 0x93, 0xca, 0x47, 0x7d, 0xf9, 0xa3, 0x2e, 0x1f, 0x13, 0x6b, 0x32, 0x75,
	0xf9, 0x38, 0xa3, 0xa2, 0x72, 0x8a, 0x7c, 0x1f, 0x2e, 0x4d, 0x2a, 0x72, 0x24, 0x5f, 0x56, 0xa9,
	0x9d, 0x51, 0x7d, 0xd9, 0xbf, 0x79, 0x7e, 0xe4, 0x94, 0xab, 0x15, 0xd5, 0x75, 0x3a, 0x57, 0xab,
	0x6b, 0xf6, 0x74, 0xae, 0xd6, 0x94, 0xe8, 0x09, 0xcd, 0x5a, 0x2c, 0xfc, 0xd2, 0x35, 0x6b, 0x45,
	0x5d, 0x9c, 0xae, 0x59, 0xab, 0xea, 0xc6, 0x84, 0x66, 0x2d, 0x17, 0x60, 0xe9, 0x9a, 0xb5, 0xb2,
	0xcc, 0x4b, 0xd7, 0xac, 0x35, 0x35, 0x5c, 0xc2, 0x22, 0x4c, 0x9c, 0xf7, 0xe1, 0x59, 0xf3, 0x3e,
	0xac, 0x9e, 0xf7, 0x77, 0x61, 0x41, 0xaf, 0xb9, 0x21, 0x97, 0xd5, 0x5e, 0xa5, 0x7a, 0xa4, 0xfe,
	0x95, 0x49, 0xcd, 0x29, 0x49, 0xbd, 0xe2, 0x46, 0x27, 0x59, 0x2a, 0xe6, 0xd1, 0x49, 0x96, 0x8b,
	0x75, 0x84, 0x60, 0x54, 0x14, 0x79, 0xe8, 0x82, 0x51, 0x5d, 0xb0, 0xa2, 0x0b, 0x46, 0x4d, 0xa5,
	0x88, 0x60, 0x70, 0xb1, 0x42, 0x43, 0x67, 0x70, 0x45, 0xa1, 0x88, 0xce, 0xe0, 0xca, 0x02, 0x0f,
	0xb4, 0x5b, 0xa5, 0x52, 0x0e, 0xdd, 0x6e, 0x55, 0x55, 0x80, 0xe8, 0x76, 0xab, 0xba, 0x16, 0x04,
	0x7d, 0x9d, 0xc2, 0xcd, 0xb9, 0xee, 0xeb, 0x94, 0x2f, 0xe6, 0xfb, 0xdb, 0x13, 0xdb, 0xd3, 0xfd,
	0xd3, 0x0b, 0x0b, 0xf4, 0xfd, 0x2b, 0x55, 0x2f, 0xe8, 0xfb, 0x57, 0xae, 0x49, 0x30, 0xa7, 0xc8,
	0x23, 0xe8, 0x28, 0x17, 0xd4, 0xa4, 0x5f, 0x31, 0x09, 0x79, 0x59, 0xd7, 0xdf, 0xaa, 0x6d, 0x4b,
	0x27, 0xa7, 0xdf, 0xcf, 0xe9, 0x93, 0x2b, 0x5d, 0xfe, 0xe9, 0x93, 0xab, 0xb8, 0xda, 0xc3, 0xc9,
	0x29, 0x37, 0xac, 0xfa, 0xe4, 0xf4, 0x1b, 0x60, 0x7d, 0x72, 0x85, 0x6b, 0x59, 0x73, 0x8a, 0x7c,
	0x03, 0x5a, 0xf2, 0x56, 0x94, 0xac, 0x15, 0x45, 0x43, 0xfa, 0x08, 0xeb, 0x95, 0x70, 0xec, 0xfd,
	0x04, 0xba, 0xda, 0x45, 0x28, 0xb9, 0x54, 0x31, 0x5a, 0x96, 0x4e, 0xef, 0x5f, 0x9e, 0xd0, 0x9a,
	0xb2, 0x4a, 0xbf, 0x05, 0xd2, 0x59, 0x55, 0xba, 0x7f, 0xd2, 0x59, 0x55, 0x71, 0x81, 0xa4, 0x08,
	0xb3, 0x4a, 0x75, 0xa7, 0xfa, 0x84, 0x29, 0x84, 0xaf, 0x9e, 0x81, 0x81, 0xb4, 0x7f, 0x5d, 0x54,
	0x07, 0x28, 0x84, 0xaf, 0xd6, 0x3b, 0x19, 0xe9, 0x86, 0x98, 0x67, 0xa1, 0xa4, 0xca, 0xb9, 0x7c,
	0x75, 0xa3, 0x93, 0xaf, 0xbc, 0x21, 0xd2, 0xc9, 0xd7, 0xdc, 0xfe, 0x20, 0xa3, 0xf5, 0xcb, 0x1b,
	0x9d, 0xd1, 0xa5, 0xbb, 0x21, 0x9d, 0xd1, 0xf7, 0xaa, 0xcc, 0xb0, 0x90, 0x85, 0xfc, 0x96, 0xa7,
	0x24, 0x0b, 0xda, 0xa5, 0x50, 0x49, 0x16, 0xf4, 0xeb, 0x21, 0x54, 0xa0, 0x4b, 0x92, 0x3b, 0xf9,
	0x65, 0x0a, 0xb9, 0x56, 0xcf, 0xbc, 0x5c, 0xcc, 0xae, 0x9f, 0x8d, 0x54, 0x1d, 0x21, 0xe5, 0x39,
	0xe8, 0x09, 0x11, 0x92, 0x9a, 0x31, 0x9f, 0x14, 0x21, 0x69, 0xa9, 0x71, 0xa9, 0xf9, 0xf4, 0x47,
	0xa3, 0x05, 0xcd, 0x57, 0x7a, 0xcf, 0xda, 0xdf, 0x9e, 0xd8, 0x8e, 0x54, 0x1d, 0x58, 0xa9, 0x7a,
	0x00, 0xa9, 0x33, 0xaa, 0xe6, 0x25, 0xa6, 0xce, 0xa8, 0xba, 0x77, 0x94, 0xc2, 0x4b, 0xad, 0x7e,
	0xa4, 0xa8, 0x7b, 0xa9, 0xb5, 0xcf, 0x24, 0xfb, 0xef, 0x9e, 0x07, 0x0d, 0x87, 0x0a, 0x8b, 0xb7,
	0x68, 0xf9, 0x3b, 0x3f, 0x32, 0x89, 0xd9, 0xea, 0x9b, 0xc4, 0xfe, 0x8d, 0xf3, 0x21, 0xe2, 0x80,
	0x1f, 0xc3, 0xbc, 0xfa, 0x8c, 0x8c, 0x6c, 0x15, 0x35, 0x8a, 0xf2, 0x9e, 0xab, 0x7f, 0xa9, 0xbe,
	0x31, 0x25, 0xa6, 0xbe, 0x0c, 0xd3, 0x89, 0x15, 0x5e, 0x98, 0xe9, 0xc4, 0x4a, 0x0f, 0xca, 0x50,
	0x60, 0x0a, 0x8f, 0xc7, 0xc8, 0x95, 0x0a, 0x97, 0x51, 0x25, 0xb9, 0x3d, 0xb1, 0x5d, 0x31, 0x1d,
	0x69, 0xc6, 0xbe, 0x64, 0x3a, 0x94, 0xfb, 0x84, 0x92, 0xe9, 0x50, 0xd3, 0xfc, 0x55, 0x52, 0x91,
	0x66, 0xe7, 0x27, 0x49, 0x85, 0x92, 0xf1, 0x9f, 0x24, 0x15, 0x6a, 0xa2, 0xdf, 0x9c, 0xba, 0x7b,
	0xeb, 0xb3, 0x9b, 0xc3, 0xd0, 0xb7, 0x83, 0xe1, 0xad, 0x0f, 0xee, 0x24, 0xc9, 0x2d, 0x27, 0x1c,
	0xbd, 0x8f, 0xff, 0x9d, 0xd1, 0x09, 0xfd, 0xf7, 0x19, 0x8d, 0x5f, 0x78, 0x0e, 0x65, 0xef, 0xe7,
	0xc4, 0x8e, 0x66, 0xb1, 0xf5, 0xab, 0xff, 0x13, 0x00, 0x00, 0xff, 0xff, 0x45, 0x89, 0x5e, 0x3e,
	0xde, 0x51, 0x00, 0x00,
}
