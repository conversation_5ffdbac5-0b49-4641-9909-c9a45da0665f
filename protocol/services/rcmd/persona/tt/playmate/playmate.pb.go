// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/playmate.proto

package playmate // import "golang.52tt.com/protocol/services/rcmd/persona/tt/playmate"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 下发玩伴功能开关
type PlaymateSettings struct {
	SettingOn            uint32   `protobuf:"varint,1,opt,name=setting_on,json=settingOn,proto3" json:"setting_on,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateSettings) Reset()         { *m = PlaymateSettings{} }
func (m *PlaymateSettings) String() string { return proto.CompactTextString(m) }
func (*PlaymateSettings) ProtoMessage()    {}
func (*PlaymateSettings) Descriptor() ([]byte, []int) {
	return fileDescriptor_playmate_25166ef133c52843, []int{0}
}
func (m *PlaymateSettings) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateSettings.Unmarshal(m, b)
}
func (m *PlaymateSettings) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateSettings.Marshal(b, m, deterministic)
}
func (dst *PlaymateSettings) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateSettings.Merge(dst, src)
}
func (m *PlaymateSettings) XXX_Size() int {
	return xxx_messageInfo_PlaymateSettings.Size(m)
}
func (m *PlaymateSettings) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateSettings.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateSettings proto.InternalMessageInfo

func (m *PlaymateSettings) GetSettingOn() uint32 {
	if m != nil {
		return m.SettingOn
	}
	return 0
}

// 下发玩伴场景的下发次数和上次下发时间
type PlaymateDeliverInfo struct {
	DeliverCount         uint32   `protobuf:"varint,1,opt,name=deliver_count,json=deliverCount,proto3" json:"deliver_count,omitempty"`
	LastDeliverTime      uint32   `protobuf:"varint,2,opt,name=last_deliver_time,json=lastDeliverTime,proto3" json:"last_deliver_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateDeliverInfo) Reset()         { *m = PlaymateDeliverInfo{} }
func (m *PlaymateDeliverInfo) String() string { return proto.CompactTextString(m) }
func (*PlaymateDeliverInfo) ProtoMessage()    {}
func (*PlaymateDeliverInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_playmate_25166ef133c52843, []int{1}
}
func (m *PlaymateDeliverInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateDeliverInfo.Unmarshal(m, b)
}
func (m *PlaymateDeliverInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateDeliverInfo.Marshal(b, m, deterministic)
}
func (dst *PlaymateDeliverInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateDeliverInfo.Merge(dst, src)
}
func (m *PlaymateDeliverInfo) XXX_Size() int {
	return xxx_messageInfo_PlaymateDeliverInfo.Size(m)
}
func (m *PlaymateDeliverInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateDeliverInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateDeliverInfo proto.InternalMessageInfo

func (m *PlaymateDeliverInfo) GetDeliverCount() uint32 {
	if m != nil {
		return m.DeliverCount
	}
	return 0
}

func (m *PlaymateDeliverInfo) GetLastDeliverTime() uint32 {
	if m != nil {
		return m.LastDeliverTime
	}
	return 0
}

func init() {
	proto.RegisterType((*PlaymateSettings)(nil), "rcmd.persona.tt.playmate.PlaymateSettings")
	proto.RegisterType((*PlaymateDeliverInfo)(nil), "rcmd.persona.tt.playmate.PlaymateDeliverInfo")
}

func init() {
	proto.RegisterFile("rcmd/persona/tt/playmate.proto", fileDescriptor_playmate_25166ef133c52843)
}

var fileDescriptor_playmate_25166ef133c52843 = []byte{
	// 244 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x92, 0x2b, 0x4a, 0xce, 0x4d,
	0xd1, 0x2f, 0x48, 0x2d, 0x2a, 0xce, 0xcf, 0x4b, 0xd4, 0x2f, 0x29, 0xd1, 0x2f, 0xc8, 0x49, 0xac,
	0xcc, 0x4d, 0x2c, 0x49, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x92, 0x00, 0xc9, 0xeb, 0x41,
	0xe5, 0xf5, 0x4a, 0x4a, 0xf4, 0x60, 0xf2, 0x52, 0x52, 0x28, 0x3a, 0xf3, 0x0b, 0x4a, 0x32, 0xf3,
	0xf3, 0x8a, 0x21, 0xba, 0x94, 0x6c, 0xb8, 0x04, 0x02, 0xa0, 0xea, 0x82, 0x53, 0x4b, 0x4a, 0x32,
	0xf3, 0xd2, 0x8b, 0x85, 0x64, 0xb9, 0xb8, 0x8a, 0x21, 0xec, 0xf8, 0xfc, 0x3c, 0x09, 0x46, 0x05,
	0x46, 0x0d, 0xde, 0x20, 0x4e, 0xa8, 0x88, 0x7f, 0x9e, 0x15, 0x67, 0xc3, 0x56, 0x89, 0x69, 0x7e,
	0x1d, 0x5b, 0x25, 0x18, 0x95, 0x0a, 0xb9, 0x84, 0x61, 0xba, 0x5d, 0x52, 0x73, 0x32, 0xcb, 0x52,
	0x8b, 0x3c, 0xf3, 0xd2, 0xf2, 0x85, 0x94, 0xb9, 0x78, 0x53, 0x20, 0xdc, 0xf8, 0xe4, 0xfc, 0xd2,
	0xbc, 0x12, 0xa8, 0x19, 0x3c, 0x50, 0x41, 0x67, 0x90, 0x98, 0x90, 0x16, 0x97, 0x60, 0x4e, 0x62,
	0x71, 0x49, 0x3c, 0x4c, 0x65, 0x49, 0x66, 0x6e, 0xaa, 0x04, 0x13, 0x58, 0x21, 0x3f, 0x48, 0x02,
	0x6a, 0x60, 0x48, 0x66, 0x6e, 0x2a, 0xd8, 0xca, 0xe9, 0x60, 0x2b, 0x9d, 0x6c, 0xa2, 0xac, 0xd2,
	0xf3, 0x73, 0x12, 0xf3, 0xd2, 0xf5, 0x4c, 0x8d, 0x4a, 0x4a, 0xf4, 0x92, 0xf3, 0x73, 0xf5, 0xc1,
	0x3e, 0x49, 0xce, 0xcf, 0xd1, 0x2f, 0x4e, 0x2d, 0x2a, 0xcb, 0x4c, 0x4e, 0x2d, 0xd6, 0xc7, 0x15,
	0x54, 0x49, 0x6c, 0x60, 0xb5, 0xc6, 0x80, 0x00, 0x00, 0x00, 0xff, 0xff, 0x90, 0xee, 0xbc, 0xa7,
	0x4d, 0x01, 0x00, 0x00,
}
