// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/user.proto

package user // import "golang.52tt.com/protocol/services/rcmd/persona/tt/user"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "github.com/envoyproxy/protoc-gen-validate/validate"
import common "golang.52tt.com/protocol/services/rcmd/common"
import _ "golang.52tt.com/protocol/services/rcmd/persona"
import channel "golang.52tt.com/protocol/services/rcmd/persona/tt/channel"
import dimension "golang.52tt.com/protocol/services/rcmd/persona/tt/dimension"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type UserOnlineState_OnlineStatus int32

const (
	UserOnlineState_Offline UserOnlineState_OnlineStatus = 0
	UserOnlineState_Online  UserOnlineState_OnlineStatus = 1
)

var UserOnlineState_OnlineStatus_name = map[int32]string{
	0: "Offline",
	1: "Online",
}
var UserOnlineState_OnlineStatus_value = map[string]int32{
	"Offline": 0,
	"Online":  1,
}

func (x UserOnlineState_OnlineStatus) String() string {
	return proto.EnumName(UserOnlineState_OnlineStatus_name, int32(x))
}
func (UserOnlineState_OnlineStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{2, 0}
}

type UserOnMicState_OnMicStatus int32

const (
	UserOnMicState_OffMic UserOnMicState_OnMicStatus = 0
	UserOnMicState_OnMic  UserOnMicState_OnMicStatus = 1
)

var UserOnMicState_OnMicStatus_name = map[int32]string{
	0: "OffMic",
	1: "OnMic",
}
var UserOnMicState_OnMicStatus_value = map[string]int32{
	"OffMic": 0,
	"OnMic":  1,
}

func (x UserOnMicState_OnMicStatus) String() string {
	return proto.EnumName(UserOnMicState_OnMicStatus_name, int32(x))
}
func (UserOnMicState_OnMicStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{4, 0}
}

type UserOffline_MUSICLABEL int32

const (
	UserOffline_Default         UserOffline_MUSICLABEL = 0
	UserOffline_ExcelProducer   UserOffline_MUSICLABEL = 1
	UserOffline_SocialKing      UserOffline_MUSICLABEL = 2
	UserOffline_ContentConsumer UserOffline_MUSICLABEL = 3
	UserOffline_ImpreciseUser   UserOffline_MUSICLABEL = 4
	UserOffline_GoodSingle      UserOffline_MUSICLABEL = 5
	UserOffline_BadChorus       UserOffline_MUSICLABEL = 6
)

var UserOffline_MUSICLABEL_name = map[int32]string{
	0: "Default",
	1: "ExcelProducer",
	2: "SocialKing",
	3: "ContentConsumer",
	4: "ImpreciseUser",
	5: "GoodSingle",
	6: "BadChorus",
}
var UserOffline_MUSICLABEL_value = map[string]int32{
	"Default":         0,
	"ExcelProducer":   1,
	"SocialKing":      2,
	"ContentConsumer": 3,
	"ImpreciseUser":   4,
	"GoodSingle":      5,
	"BadChorus":       6,
}

func (x UserOffline_MUSICLABEL) String() string {
	return proto.EnumName(UserOffline_MUSICLABEL_name, int32(x))
}
func (UserOffline_MUSICLABEL) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{8, 0}
}

type UserInChannel_ChannelType int32

const (
	UserInChannel_Default      UserInChannel_ChannelType = 0
	UserInChannel_UGCChannel   UserInChannel_ChannelType = 1
	UserInChannel_OtherChannel UserInChannel_ChannelType = 2
)

var UserInChannel_ChannelType_name = map[int32]string{
	0: "Default",
	1: "UGCChannel",
	2: "OtherChannel",
}
var UserInChannel_ChannelType_value = map[string]int32{
	"Default":      0,
	"UGCChannel":   1,
	"OtherChannel": 2,
}

func (x UserInChannel_ChannelType) String() string {
	return proto.EnumName(UserInChannel_ChannelType_name, int32(x))
}
func (UserInChannel_ChannelType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{46, 0}
}

type UserAppStatus_AppStatus int32

const (
	UserAppStatus_AppNone       UserAppStatus_AppStatus = 0
	UserAppStatus_AppBackGround UserAppStatus_AppStatus = 1
	UserAppStatus_AppAWait      UserAppStatus_AppStatus = 2
)

var UserAppStatus_AppStatus_name = map[int32]string{
	0: "AppNone",
	1: "AppBackGround",
	2: "AppAWait",
}
var UserAppStatus_AppStatus_value = map[string]int32{
	"AppNone":       0,
	"AppBackGround": 1,
	"AppAWait":      2,
}

func (x UserAppStatus_AppStatus) String() string {
	return proto.EnumName(UserAppStatus_AppStatus_name, int32(x))
}
func (UserAppStatus_AppStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{47, 0}
}

type UserActionStat_Action int32

const (
	UserActionStat_None         UserActionStat_Action = 0
	UserActionStat_EnterChannel UserActionStat_Action = 1
	UserActionStat_LeaveChannel UserActionStat_Action = 2
)

var UserActionStat_Action_name = map[int32]string{
	0: "None",
	1: "EnterChannel",
	2: "LeaveChannel",
}
var UserActionStat_Action_value = map[string]int32{
	"None":         0,
	"EnterChannel": 1,
	"LeaveChannel": 2,
}

func (x UserActionStat_Action) String() string {
	return proto.EnumName(UserActionStat_Action_name, int32(x))
}
func (UserActionStat_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 0}
}

type UserBasic struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegTime               uint32   `protobuf:"varint,2,opt,name=reg_time,json=regTime,proto3" json:"reg_time,omitempty"`
	Sex                   uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Brithday              uint32   `protobuf:"varint,4,opt,name=brithday,proto3" json:"brithday,omitempty"`
	Age                   uint32   `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	AgeGroup              uint32   `protobuf:"varint,6,opt,name=age_group,json=ageGroup,proto3" json:"age_group,omitempty"`
	Tag_2                 uint32   `protobuf:"varint,7,opt,name=tag_2,json=tag2,proto3" json:"tag_2,omitempty"`
	Tag_4                 string   `protobuf:"bytes,8,opt,name=tag_4,json=tag4,proto3" json:"tag_4,omitempty"`
	Tag_4_13Opt1          string   `protobuf:"bytes,9,opt,name=tag_4_13_opt1,json=tag413Opt1,proto3" json:"tag_4_13_opt1,omitempty"`
	Tag_4_16Opt1          string   `protobuf:"bytes,10,opt,name=tag_4_16_opt1,json=tag416Opt1,proto3" json:"tag_4_16_opt1,omitempty"`
	Tag_4_13Opt2          string   `protobuf:"bytes,11,opt,name=tag_4_13_opt2,json=tag413Opt2,proto3" json:"tag_4_13_opt2,omitempty"`
	Tag_4_16Opt2          string   `protobuf:"bytes,12,opt,name=tag_4_16_opt2,json=tag416Opt2,proto3" json:"tag_4_16_opt2,omitempty"`
	Tag_4_13Opt3          string   `protobuf:"bytes,13,opt,name=tag_4_13_opt3,json=tag413Opt3,proto3" json:"tag_4_13_opt3,omitempty"`
	Tag_4_16Opt3          string   `protobuf:"bytes,14,opt,name=tag_4_16_opt3,json=tag416Opt3,proto3" json:"tag_4_16_opt3,omitempty"`
	MaterialTagNames      []string `protobuf:"bytes,16,rep,name=material_tag_names,json=materialTagNames,proto3" json:"material_tag_names,omitempty"`
	BusinessId            int64    `protobuf:"varint,17,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	CompanionChannelEnter uint32   `protobuf:"varint,18,opt,name=companion_channel_enter,json=companionChannelEnter,proto3" json:"companion_channel_enter,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserBasic) Reset()         { *m = UserBasic{} }
func (m *UserBasic) String() string { return proto.CompactTextString(m) }
func (*UserBasic) ProtoMessage()    {}
func (*UserBasic) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{0}
}
func (m *UserBasic) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBasic.Unmarshal(m, b)
}
func (m *UserBasic) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBasic.Marshal(b, m, deterministic)
}
func (dst *UserBasic) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBasic.Merge(dst, src)
}
func (m *UserBasic) XXX_Size() int {
	return xxx_messageInfo_UserBasic.Size(m)
}
func (m *UserBasic) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBasic.DiscardUnknown(m)
}

var xxx_messageInfo_UserBasic proto.InternalMessageInfo

func (m *UserBasic) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBasic) GetRegTime() uint32 {
	if m != nil {
		return m.RegTime
	}
	return 0
}

func (m *UserBasic) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserBasic) GetBrithday() uint32 {
	if m != nil {
		return m.Brithday
	}
	return 0
}

func (m *UserBasic) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *UserBasic) GetAgeGroup() uint32 {
	if m != nil {
		return m.AgeGroup
	}
	return 0
}

func (m *UserBasic) GetTag_2() uint32 {
	if m != nil {
		return m.Tag_2
	}
	return 0
}

func (m *UserBasic) GetTag_4() string {
	if m != nil {
		return m.Tag_4
	}
	return ""
}

func (m *UserBasic) GetTag_4_13Opt1() string {
	if m != nil {
		return m.Tag_4_13Opt1
	}
	return ""
}

func (m *UserBasic) GetTag_4_16Opt1() string {
	if m != nil {
		return m.Tag_4_16Opt1
	}
	return ""
}

func (m *UserBasic) GetTag_4_13Opt2() string {
	if m != nil {
		return m.Tag_4_13Opt2
	}
	return ""
}

func (m *UserBasic) GetTag_4_16Opt2() string {
	if m != nil {
		return m.Tag_4_16Opt2
	}
	return ""
}

func (m *UserBasic) GetTag_4_13Opt3() string {
	if m != nil {
		return m.Tag_4_13Opt3
	}
	return ""
}

func (m *UserBasic) GetTag_4_16Opt3() string {
	if m != nil {
		return m.Tag_4_16Opt3
	}
	return ""
}

func (m *UserBasic) GetMaterialTagNames() []string {
	if m != nil {
		return m.MaterialTagNames
	}
	return nil
}

func (m *UserBasic) GetBusinessId() int64 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *UserBasic) GetCompanionChannelEnter() uint32 {
	if m != nil {
		return m.CompanionChannelEnter
	}
	return 0
}

// 画像v2.0 添加局部更新material_tag_names
type UserBasic_MaterialTagNamesUpdate struct {
	MaterialTagNames     []string `protobuf:"bytes,1,rep,name=material_tag_names,json=materialTagNames,proto3" json:"material_tag_names,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBasic_MaterialTagNamesUpdate) Reset()         { *m = UserBasic_MaterialTagNamesUpdate{} }
func (m *UserBasic_MaterialTagNamesUpdate) String() string { return proto.CompactTextString(m) }
func (*UserBasic_MaterialTagNamesUpdate) ProtoMessage()    {}
func (*UserBasic_MaterialTagNamesUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{0, 0}
}
func (m *UserBasic_MaterialTagNamesUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBasic_MaterialTagNamesUpdate.Unmarshal(m, b)
}
func (m *UserBasic_MaterialTagNamesUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBasic_MaterialTagNamesUpdate.Marshal(b, m, deterministic)
}
func (dst *UserBasic_MaterialTagNamesUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBasic_MaterialTagNamesUpdate.Merge(dst, src)
}
func (m *UserBasic_MaterialTagNamesUpdate) XXX_Size() int {
	return xxx_messageInfo_UserBasic_MaterialTagNamesUpdate.Size(m)
}
func (m *UserBasic_MaterialTagNamesUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBasic_MaterialTagNamesUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserBasic_MaterialTagNamesUpdate proto.InternalMessageInfo

func (m *UserBasic_MaterialTagNamesUpdate) GetMaterialTagNames() []string {
	if m != nil {
		return m.MaterialTagNames
	}
	return nil
}

type UserBasic_BusinessIdUpdate struct {
	BusinessId           int64    `protobuf:"varint,1,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBasic_BusinessIdUpdate) Reset()         { *m = UserBasic_BusinessIdUpdate{} }
func (m *UserBasic_BusinessIdUpdate) String() string { return proto.CompactTextString(m) }
func (*UserBasic_BusinessIdUpdate) ProtoMessage()    {}
func (*UserBasic_BusinessIdUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{0, 1}
}
func (m *UserBasic_BusinessIdUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBasic_BusinessIdUpdate.Unmarshal(m, b)
}
func (m *UserBasic_BusinessIdUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBasic_BusinessIdUpdate.Marshal(b, m, deterministic)
}
func (dst *UserBasic_BusinessIdUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBasic_BusinessIdUpdate.Merge(dst, src)
}
func (m *UserBasic_BusinessIdUpdate) XXX_Size() int {
	return xxx_messageInfo_UserBasic_BusinessIdUpdate.Size(m)
}
func (m *UserBasic_BusinessIdUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBasic_BusinessIdUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserBasic_BusinessIdUpdate proto.InternalMessageInfo

func (m *UserBasic_BusinessIdUpdate) GetBusinessId() int64 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

// 局部更新老的 user basic画像, 防止覆盖其它的新增字段
type UserBasic_UserBasicUpdate struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegTime              uint32   `protobuf:"varint,2,opt,name=reg_time,json=regTime,proto3" json:"reg_time,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Brithday             uint32   `protobuf:"varint,4,opt,name=brithday,proto3" json:"brithday,omitempty"`
	Age                  uint32   `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	AgeGroup             uint32   `protobuf:"varint,6,opt,name=age_group,json=ageGroup,proto3" json:"age_group,omitempty"`
	Tag_2                uint32   `protobuf:"varint,7,opt,name=tag_2,json=tag2,proto3" json:"tag_2,omitempty"`
	Tag_4                string   `protobuf:"bytes,8,opt,name=tag_4,json=tag4,proto3" json:"tag_4,omitempty"`
	Tag_4_13Opt1         string   `protobuf:"bytes,9,opt,name=tag_4_13_opt1,json=tag413Opt1,proto3" json:"tag_4_13_opt1,omitempty"`
	Tag_4_16Opt1         string   `protobuf:"bytes,10,opt,name=tag_4_16_opt1,json=tag416Opt1,proto3" json:"tag_4_16_opt1,omitempty"`
	Tag_4_13Opt2         string   `protobuf:"bytes,11,opt,name=tag_4_13_opt2,json=tag413Opt2,proto3" json:"tag_4_13_opt2,omitempty"`
	Tag_4_16Opt2         string   `protobuf:"bytes,12,opt,name=tag_4_16_opt2,json=tag416Opt2,proto3" json:"tag_4_16_opt2,omitempty"`
	Tag_4_13Opt3         string   `protobuf:"bytes,13,opt,name=tag_4_13_opt3,json=tag413Opt3,proto3" json:"tag_4_13_opt3,omitempty"`
	Tag_4_16Opt3         string   `protobuf:"bytes,14,opt,name=tag_4_16_opt3,json=tag416Opt3,proto3" json:"tag_4_16_opt3,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBasic_UserBasicUpdate) Reset()         { *m = UserBasic_UserBasicUpdate{} }
func (m *UserBasic_UserBasicUpdate) String() string { return proto.CompactTextString(m) }
func (*UserBasic_UserBasicUpdate) ProtoMessage()    {}
func (*UserBasic_UserBasicUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{0, 2}
}
func (m *UserBasic_UserBasicUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBasic_UserBasicUpdate.Unmarshal(m, b)
}
func (m *UserBasic_UserBasicUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBasic_UserBasicUpdate.Marshal(b, m, deterministic)
}
func (dst *UserBasic_UserBasicUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBasic_UserBasicUpdate.Merge(dst, src)
}
func (m *UserBasic_UserBasicUpdate) XXX_Size() int {
	return xxx_messageInfo_UserBasic_UserBasicUpdate.Size(m)
}
func (m *UserBasic_UserBasicUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBasic_UserBasicUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserBasic_UserBasicUpdate proto.InternalMessageInfo

func (m *UserBasic_UserBasicUpdate) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetRegTime() uint32 {
	if m != nil {
		return m.RegTime
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetBrithday() uint32 {
	if m != nil {
		return m.Brithday
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetAgeGroup() uint32 {
	if m != nil {
		return m.AgeGroup
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetTag_2() uint32 {
	if m != nil {
		return m.Tag_2
	}
	return 0
}

func (m *UserBasic_UserBasicUpdate) GetTag_4() string {
	if m != nil {
		return m.Tag_4
	}
	return ""
}

func (m *UserBasic_UserBasicUpdate) GetTag_4_13Opt1() string {
	if m != nil {
		return m.Tag_4_13Opt1
	}
	return ""
}

func (m *UserBasic_UserBasicUpdate) GetTag_4_16Opt1() string {
	if m != nil {
		return m.Tag_4_16Opt1
	}
	return ""
}

func (m *UserBasic_UserBasicUpdate) GetTag_4_13Opt2() string {
	if m != nil {
		return m.Tag_4_13Opt2
	}
	return ""
}

func (m *UserBasic_UserBasicUpdate) GetTag_4_16Opt2() string {
	if m != nil {
		return m.Tag_4_16Opt2
	}
	return ""
}

func (m *UserBasic_UserBasicUpdate) GetTag_4_13Opt3() string {
	if m != nil {
		return m.Tag_4_13Opt3
	}
	return ""
}

func (m *UserBasic_UserBasicUpdate) GetTag_4_16Opt3() string {
	if m != nil {
		return m.Tag_4_16Opt3
	}
	return ""
}

type UserBasic_CompanionChannelEnterIncr struct {
	CompanionChannelEnter uint32   `protobuf:"varint,1,opt,name=companion_channel_enter,json=companionChannelEnter,proto3" json:"companion_channel_enter,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserBasic_CompanionChannelEnterIncr) Reset()         { *m = UserBasic_CompanionChannelEnterIncr{} }
func (m *UserBasic_CompanionChannelEnterIncr) String() string { return proto.CompactTextString(m) }
func (*UserBasic_CompanionChannelEnterIncr) ProtoMessage()    {}
func (*UserBasic_CompanionChannelEnterIncr) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{0, 3}
}
func (m *UserBasic_CompanionChannelEnterIncr) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBasic_CompanionChannelEnterIncr.Unmarshal(m, b)
}
func (m *UserBasic_CompanionChannelEnterIncr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBasic_CompanionChannelEnterIncr.Marshal(b, m, deterministic)
}
func (dst *UserBasic_CompanionChannelEnterIncr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBasic_CompanionChannelEnterIncr.Merge(dst, src)
}
func (m *UserBasic_CompanionChannelEnterIncr) XXX_Size() int {
	return xxx_messageInfo_UserBasic_CompanionChannelEnterIncr.Size(m)
}
func (m *UserBasic_CompanionChannelEnterIncr) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBasic_CompanionChannelEnterIncr.DiscardUnknown(m)
}

var xxx_messageInfo_UserBasic_CompanionChannelEnterIncr proto.InternalMessageInfo

func (m *UserBasic_CompanionChannelEnterIncr) GetCompanionChannelEnter() uint32 {
	if m != nil {
		return m.CompanionChannelEnter
	}
	return 0
}

type UserAuthInfo struct {
	Uid                  uint32               `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ip                   string               `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Ts                   uint64               `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	Phone                string               `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Imei                 string               `protobuf:"bytes,5,opt,name=imei,proto3" json:"imei,omitempty"`
	Idfa                 string               `protobuf:"bytes,6,opt,name=idfa,proto3" json:"idfa,omitempty"`
	ClientType           uint32               `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32               `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceIdHex          string               `protobuf:"bytes,9,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	IsAnti               bool                 `protobuf:"varint,10,opt,name=is_anti,json=isAnti,proto3" json:"is_anti,omitempty"`
	MarketId             uint32               `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	CmdId                uint32               `protobuf:"varint,12,opt,name=cmd_id,json=cmdId,proto3" json:"cmd_id,omitempty"`
	IsAutoLogin          bool                 `protobuf:"varint,13,opt,name=is_auto_login,json=isAutoLogin,proto3" json:"is_auto_login,omitempty"`
	PkgChannel           string               `protobuf:"bytes,14,opt,name=pkg_channel,json=pkgChannel,proto3" json:"pkg_channel,omitempty"`
	SmDeviceId           string               `protobuf:"bytes,15,opt,name=sm_device_id,json=smDeviceId,proto3" json:"sm_device_id,omitempty"`
	DeviceModel          string               `protobuf:"bytes,16,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	SdkGameId            int64                `protobuf:"varint,17,opt,name=sdk_game_id,json=sdkGameId,proto3" json:"sdk_game_id,omitempty"`
	LastLoginTs          uint64               `protobuf:"varint,18,opt,name=last_login_ts,json=lastLoginTs,proto3" json:"last_login_ts,omitempty"`
	DeviceInfo           string               `protobuf:"bytes,19,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
	Oaid                 string               `protobuf:"bytes,20,opt,name=oaid,proto3" json:"oaid,omitempty"`
	AndroidId            string               `protobuf:"bytes,21,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	Loc                  *common.LocationInfo `protobuf:"bytes,22,opt,name=loc,proto3" json:"loc,omitempty"`
	PromotionTagList     []string             `protobuf:"bytes,23,rep,name=promotion_tag_list,json=promotionTagList,proto3" json:"promotion_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserAuthInfo) Reset()         { *m = UserAuthInfo{} }
func (m *UserAuthInfo) String() string { return proto.CompactTextString(m) }
func (*UserAuthInfo) ProtoMessage()    {}
func (*UserAuthInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{1}
}
func (m *UserAuthInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAuthInfo.Unmarshal(m, b)
}
func (m *UserAuthInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAuthInfo.Marshal(b, m, deterministic)
}
func (dst *UserAuthInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAuthInfo.Merge(dst, src)
}
func (m *UserAuthInfo) XXX_Size() int {
	return xxx_messageInfo_UserAuthInfo.Size(m)
}
func (m *UserAuthInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAuthInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserAuthInfo proto.InternalMessageInfo

func (m *UserAuthInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserAuthInfo) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *UserAuthInfo) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *UserAuthInfo) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *UserAuthInfo) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *UserAuthInfo) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *UserAuthInfo) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *UserAuthInfo) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *UserAuthInfo) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

func (m *UserAuthInfo) GetIsAnti() bool {
	if m != nil {
		return m.IsAnti
	}
	return false
}

func (m *UserAuthInfo) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *UserAuthInfo) GetCmdId() uint32 {
	if m != nil {
		return m.CmdId
	}
	return 0
}

func (m *UserAuthInfo) GetIsAutoLogin() bool {
	if m != nil {
		return m.IsAutoLogin
	}
	return false
}

func (m *UserAuthInfo) GetPkgChannel() string {
	if m != nil {
		return m.PkgChannel
	}
	return ""
}

func (m *UserAuthInfo) GetSmDeviceId() string {
	if m != nil {
		return m.SmDeviceId
	}
	return ""
}

func (m *UserAuthInfo) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *UserAuthInfo) GetSdkGameId() int64 {
	if m != nil {
		return m.SdkGameId
	}
	return 0
}

func (m *UserAuthInfo) GetLastLoginTs() uint64 {
	if m != nil {
		return m.LastLoginTs
	}
	return 0
}

func (m *UserAuthInfo) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *UserAuthInfo) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *UserAuthInfo) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

func (m *UserAuthInfo) GetLoc() *common.LocationInfo {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *UserAuthInfo) GetPromotionTagList() []string {
	if m != nil {
		return m.PromotionTagList
	}
	return nil
}

type UserAuthInfo_PromotionTagListUpdate struct {
	PromotionTagList     []string `protobuf:"bytes,1,rep,name=promotion_tag_list,json=promotionTagList,proto3" json:"promotion_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAuthInfo_PromotionTagListUpdate) Reset()         { *m = UserAuthInfo_PromotionTagListUpdate{} }
func (m *UserAuthInfo_PromotionTagListUpdate) String() string { return proto.CompactTextString(m) }
func (*UserAuthInfo_PromotionTagListUpdate) ProtoMessage()    {}
func (*UserAuthInfo_PromotionTagListUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{1, 0}
}
func (m *UserAuthInfo_PromotionTagListUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAuthInfo_PromotionTagListUpdate.Unmarshal(m, b)
}
func (m *UserAuthInfo_PromotionTagListUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAuthInfo_PromotionTagListUpdate.Marshal(b, m, deterministic)
}
func (dst *UserAuthInfo_PromotionTagListUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAuthInfo_PromotionTagListUpdate.Merge(dst, src)
}
func (m *UserAuthInfo_PromotionTagListUpdate) XXX_Size() int {
	return xxx_messageInfo_UserAuthInfo_PromotionTagListUpdate.Size(m)
}
func (m *UserAuthInfo_PromotionTagListUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAuthInfo_PromotionTagListUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserAuthInfo_PromotionTagListUpdate proto.InternalMessageInfo

func (m *UserAuthInfo_PromotionTagListUpdate) GetPromotionTagList() []string {
	if m != nil {
		return m.PromotionTagList
	}
	return nil
}

type UserOnlineState struct {
	IsOnline             uint32   `protobuf:"varint,1,opt,name=is_online,json=isOnline,proto3" json:"is_online,omitempty"`
	LastLoginCity        string   `protobuf:"bytes,2,opt,name=last_login_city,json=lastLoginCity,proto3" json:"last_login_city,omitempty"`
	LastOsType           uint32   `protobuf:"varint,3,opt,name=last_os_type,json=lastOsType,proto3" json:"last_os_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOnlineState) Reset()         { *m = UserOnlineState{} }
func (m *UserOnlineState) String() string { return proto.CompactTextString(m) }
func (*UserOnlineState) ProtoMessage()    {}
func (*UserOnlineState) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{2}
}
func (m *UserOnlineState) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOnlineState.Unmarshal(m, b)
}
func (m *UserOnlineState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOnlineState.Marshal(b, m, deterministic)
}
func (dst *UserOnlineState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOnlineState.Merge(dst, src)
}
func (m *UserOnlineState) XXX_Size() int {
	return xxx_messageInfo_UserOnlineState.Size(m)
}
func (m *UserOnlineState) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOnlineState.DiscardUnknown(m)
}

var xxx_messageInfo_UserOnlineState proto.InternalMessageInfo

func (m *UserOnlineState) GetIsOnline() uint32 {
	if m != nil {
		return m.IsOnline
	}
	return 0
}

func (m *UserOnlineState) GetLastLoginCity() string {
	if m != nil {
		return m.LastLoginCity
	}
	return ""
}

func (m *UserOnlineState) GetLastOsType() uint32 {
	if m != nil {
		return m.LastOsType
	}
	return 0
}

// 2021-12-27 业务没有再使用，为了释放v1 redis空间压力，进行删除
// https://q9jvw0u5f5.feishu.cn/docs/doccnbZmBjGV0AqOB08DliWxZie
type AccountState struct {
	IsUnusual            uint32   `protobuf:"varint,1,opt,name=is_unusual,json=isUnusual,proto3" json:"is_unusual,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AccountState) Reset()         { *m = AccountState{} }
func (m *AccountState) String() string { return proto.CompactTextString(m) }
func (*AccountState) ProtoMessage()    {}
func (*AccountState) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{3}
}
func (m *AccountState) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AccountState.Unmarshal(m, b)
}
func (m *AccountState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AccountState.Marshal(b, m, deterministic)
}
func (dst *AccountState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AccountState.Merge(dst, src)
}
func (m *AccountState) XXX_Size() int {
	return xxx_messageInfo_AccountState.Size(m)
}
func (m *AccountState) XXX_DiscardUnknown() {
	xxx_messageInfo_AccountState.DiscardUnknown(m)
}

var xxx_messageInfo_AccountState proto.InternalMessageInfo

func (m *AccountState) GetIsUnusual() uint32 {
	if m != nil {
		return m.IsUnusual
	}
	return 0
}

type UserOnMicState struct {
	IsOnmic              uint32   `protobuf:"varint,3,opt,name=is_onmic,json=isOnmic,proto3" json:"is_onmic,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOnMicState) Reset()         { *m = UserOnMicState{} }
func (m *UserOnMicState) String() string { return proto.CompactTextString(m) }
func (*UserOnMicState) ProtoMessage()    {}
func (*UserOnMicState) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{4}
}
func (m *UserOnMicState) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOnMicState.Unmarshal(m, b)
}
func (m *UserOnMicState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOnMicState.Marshal(b, m, deterministic)
}
func (dst *UserOnMicState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOnMicState.Merge(dst, src)
}
func (m *UserOnMicState) XXX_Size() int {
	return xxx_messageInfo_UserOnMicState.Size(m)
}
func (m *UserOnMicState) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOnMicState.DiscardUnknown(m)
}

var xxx_messageInfo_UserOnMicState proto.InternalMessageInfo

func (m *UserOnMicState) GetIsOnmic() uint32 {
	if m != nil {
		return m.IsOnmic
	}
	return 0
}

type UserBlackProductionMark struct {
	IsBlackProduction    uint32   `protobuf:"varint,1,opt,name=is_black_production,json=isBlackProduction,proto3" json:"is_black_production,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserBlackProductionMark) Reset()         { *m = UserBlackProductionMark{} }
func (m *UserBlackProductionMark) String() string { return proto.CompactTextString(m) }
func (*UserBlackProductionMark) ProtoMessage()    {}
func (*UserBlackProductionMark) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{5}
}
func (m *UserBlackProductionMark) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserBlackProductionMark.Unmarshal(m, b)
}
func (m *UserBlackProductionMark) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserBlackProductionMark.Marshal(b, m, deterministic)
}
func (dst *UserBlackProductionMark) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserBlackProductionMark.Merge(dst, src)
}
func (m *UserBlackProductionMark) XXX_Size() int {
	return xxx_messageInfo_UserBlackProductionMark.Size(m)
}
func (m *UserBlackProductionMark) XXX_DiscardUnknown() {
	xxx_messageInfo_UserBlackProductionMark.DiscardUnknown(m)
}

var xxx_messageInfo_UserBlackProductionMark proto.InternalMessageInfo

func (m *UserBlackProductionMark) GetIsBlackProduction() uint32 {
	if m != nil {
		return m.IsBlackProduction
	}
	return 0
}

type UserInferiorMark struct {
	IsInferior           uint32   `protobuf:"varint,1,opt,name=is_inferior,json=isInferior,proto3" json:"is_inferior,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInferiorMark) Reset()         { *m = UserInferiorMark{} }
func (m *UserInferiorMark) String() string { return proto.CompactTextString(m) }
func (*UserInferiorMark) ProtoMessage()    {}
func (*UserInferiorMark) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{6}
}
func (m *UserInferiorMark) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInferiorMark.Unmarshal(m, b)
}
func (m *UserInferiorMark) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInferiorMark.Marshal(b, m, deterministic)
}
func (dst *UserInferiorMark) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInferiorMark.Merge(dst, src)
}
func (m *UserInferiorMark) XXX_Size() int {
	return xxx_messageInfo_UserInferiorMark.Size(m)
}
func (m *UserInferiorMark) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInferiorMark.DiscardUnknown(m)
}

var xxx_messageInfo_UserInferiorMark proto.InternalMessageInfo

func (m *UserInferiorMark) GetIsInferior() uint32 {
	if m != nil {
		return m.IsInferior
	}
	return 0
}

type ImInfo struct {
	ImCount              uint32   `protobuf:"varint,1,opt,name=im_count,json=imCount,proto3" json:"im_count,omitempty"`
	ImMembers            []uint32 `protobuf:"varint,2,rep,packed,name=im_members,json=imMembers,proto3" json:"im_members,omitempty"`
	RecordTime           uint32   `protobuf:"varint,3,opt,name=record_time,json=recordTime,proto3" json:"record_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImInfo) Reset()         { *m = ImInfo{} }
func (m *ImInfo) String() string { return proto.CompactTextString(m) }
func (*ImInfo) ProtoMessage()    {}
func (*ImInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{7}
}
func (m *ImInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImInfo.Unmarshal(m, b)
}
func (m *ImInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImInfo.Marshal(b, m, deterministic)
}
func (dst *ImInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImInfo.Merge(dst, src)
}
func (m *ImInfo) XXX_Size() int {
	return xxx_messageInfo_ImInfo.Size(m)
}
func (m *ImInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ImInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ImInfo proto.InternalMessageInfo

func (m *ImInfo) GetImCount() uint32 {
	if m != nil {
		return m.ImCount
	}
	return 0
}

func (m *ImInfo) GetImMembers() []uint32 {
	if m != nil {
		return m.ImMembers
	}
	return nil
}

func (m *ImInfo) GetRecordTime() uint32 {
	if m != nil {
		return m.RecordTime
	}
	return 0
}

type UserOffline struct {
	PrefTagId                uint32             `protobuf:"varint,1,opt,name=pref_tag_id,json=prefTagId,proto3" json:"pref_tag_id,omitempty"`
	PrefTagIdKuolie          uint32             `protobuf:"varint,2,opt,name=pref_tag_id_kuolie,json=prefTagIdKuolie,proto3" json:"pref_tag_id_kuolie,omitempty"`
	FriendNum                uint32             `protobuf:"varint,3,opt,name=friend_num,json=friendNum,proto3" json:"friend_num,omitempty"`
	FriendNum_1              uint32             `protobuf:"varint,4,opt,name=friend_num_1,json=friendNum1,proto3" json:"friend_num_1,omitempty"`
	ImNum                    uint32             `protobuf:"varint,5,opt,name=im_num,json=imNum,proto3" json:"im_num,omitempty"`
	ImNum_1                  uint32             `protobuf:"varint,6,opt,name=im_num_1,json=imNum1,proto3" json:"im_num_1,omitempty"`
	PostNum                  uint32             `protobuf:"varint,7,opt,name=post_num,json=postNum,proto3" json:"post_num,omitempty"`
	LoginDays                uint32             `protobuf:"varint,8,opt,name=login_days,json=loginDays,proto3" json:"login_days,omitempty"`
	DurationUgc              float64            `protobuf:"fixed64,9,opt,name=duration_ugc,json=durationUgc,proto3" json:"duration_ugc,omitempty"`
	DurationAvgUgc           float64            `protobuf:"fixed64,10,opt,name=duration_avg_ugc,json=durationAvgUgc,proto3" json:"duration_avg_ugc,omitempty"`
	DurationKuolie           float64            `protobuf:"fixed64,11,opt,name=duration_kuolie,json=durationKuolie,proto3" json:"duration_kuolie,omitempty"`
	DurationAvgKuolie        float64            `protobuf:"fixed64,12,opt,name=duration_avg_kuolie,json=durationAvgKuolie,proto3" json:"duration_avg_kuolie,omitempty"`
	DurationSmall            float64            `protobuf:"fixed64,13,opt,name=duration_small,json=durationSmall,proto3" json:"duration_small,omitempty"`
	DurationAvgSmall         float64            `protobuf:"fixed64,14,opt,name=duration_avg_small,json=durationAvgSmall,proto3" json:"duration_avg_small,omitempty"`
	FriendRate_1             float64            `protobuf:"fixed64,15,opt,name=friend_rate_1,json=friendRate1,proto3" json:"friend_rate_1,omitempty"`
	ImRate_1                 float64            `protobuf:"fixed64,16,opt,name=im_rate_1,json=imRate1,proto3" json:"im_rate_1,omitempty"`
	TfIdf                    map[uint32]float64 `protobuf:"bytes,17,rep,name=tf_idf,json=tfIdf,proto3" json:"tf_idf,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UserChannelLevel         uint32             `protobuf:"varint,18,opt,name=user_channel_level,json=userChannelLevel,proto3" json:"user_channel_level,omitempty"`
	HasExpLevel_6Channel     uint32             `protobuf:"varint,19,opt,name=has_exp_level_6_channel,json=hasExpLevel6Channel,proto3" json:"has_exp_level_6_channel,omitempty"`
	TagTfIdf                 string             `protobuf:"bytes,20,opt,name=tag_tf_idf,json=tagTfIdf,proto3" json:"tag_tf_idf,omitempty"`
	TagTfIdfV1               string             `protobuf:"bytes,21,opt,name=tag_tf_idf_v1,json=tagTfIdfV1,proto3" json:"tag_tf_idf_v1,omitempty"`
	UiRoomIntentTagTf_28D    map[string]string  `protobuf:"bytes,22,rep,name=ui_room_intent_tag_tf_28d,json=uiRoomIntentTagTf28d,proto3" json:"ui_room_intent_tag_tf_28d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UiRoomIntentTagTfidf_28D map[string]string  `protobuf:"bytes,23,rep,name=ui_room_intent_tag_tfidf_28d,json=uiRoomIntentTagTfidf28d,proto3" json:"ui_room_intent_tag_tfidf_28d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	MusicLabel               []string           `protobuf:"bytes,24,rep,name=music_label,json=musicLabel,proto3" json:"music_label,omitempty"`
	XXX_NoUnkeyedLiteral     struct{}           `json:"-"`
	XXX_unrecognized         []byte             `json:"-"`
	XXX_sizecache            int32              `json:"-"`
}

func (m *UserOffline) Reset()         { *m = UserOffline{} }
func (m *UserOffline) String() string { return proto.CompactTextString(m) }
func (*UserOffline) ProtoMessage()    {}
func (*UserOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{8}
}
func (m *UserOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOffline.Unmarshal(m, b)
}
func (m *UserOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOffline.Marshal(b, m, deterministic)
}
func (dst *UserOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOffline.Merge(dst, src)
}
func (m *UserOffline) XXX_Size() int {
	return xxx_messageInfo_UserOffline.Size(m)
}
func (m *UserOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOffline.DiscardUnknown(m)
}

var xxx_messageInfo_UserOffline proto.InternalMessageInfo

func (m *UserOffline) GetPrefTagId() uint32 {
	if m != nil {
		return m.PrefTagId
	}
	return 0
}

func (m *UserOffline) GetPrefTagIdKuolie() uint32 {
	if m != nil {
		return m.PrefTagIdKuolie
	}
	return 0
}

func (m *UserOffline) GetFriendNum() uint32 {
	if m != nil {
		return m.FriendNum
	}
	return 0
}

func (m *UserOffline) GetFriendNum_1() uint32 {
	if m != nil {
		return m.FriendNum_1
	}
	return 0
}

func (m *UserOffline) GetImNum() uint32 {
	if m != nil {
		return m.ImNum
	}
	return 0
}

func (m *UserOffline) GetImNum_1() uint32 {
	if m != nil {
		return m.ImNum_1
	}
	return 0
}

func (m *UserOffline) GetPostNum() uint32 {
	if m != nil {
		return m.PostNum
	}
	return 0
}

func (m *UserOffline) GetLoginDays() uint32 {
	if m != nil {
		return m.LoginDays
	}
	return 0
}

func (m *UserOffline) GetDurationUgc() float64 {
	if m != nil {
		return m.DurationUgc
	}
	return 0
}

func (m *UserOffline) GetDurationAvgUgc() float64 {
	if m != nil {
		return m.DurationAvgUgc
	}
	return 0
}

func (m *UserOffline) GetDurationKuolie() float64 {
	if m != nil {
		return m.DurationKuolie
	}
	return 0
}

func (m *UserOffline) GetDurationAvgKuolie() float64 {
	if m != nil {
		return m.DurationAvgKuolie
	}
	return 0
}

func (m *UserOffline) GetDurationSmall() float64 {
	if m != nil {
		return m.DurationSmall
	}
	return 0
}

func (m *UserOffline) GetDurationAvgSmall() float64 {
	if m != nil {
		return m.DurationAvgSmall
	}
	return 0
}

func (m *UserOffline) GetFriendRate_1() float64 {
	if m != nil {
		return m.FriendRate_1
	}
	return 0
}

func (m *UserOffline) GetImRate_1() float64 {
	if m != nil {
		return m.ImRate_1
	}
	return 0
}

func (m *UserOffline) GetTfIdf() map[uint32]float64 {
	if m != nil {
		return m.TfIdf
	}
	return nil
}

func (m *UserOffline) GetUserChannelLevel() uint32 {
	if m != nil {
		return m.UserChannelLevel
	}
	return 0
}

func (m *UserOffline) GetHasExpLevel_6Channel() uint32 {
	if m != nil {
		return m.HasExpLevel_6Channel
	}
	return 0
}

func (m *UserOffline) GetTagTfIdf() string {
	if m != nil {
		return m.TagTfIdf
	}
	return ""
}

func (m *UserOffline) GetTagTfIdfV1() string {
	if m != nil {
		return m.TagTfIdfV1
	}
	return ""
}

func (m *UserOffline) GetUiRoomIntentTagTf_28D() map[string]string {
	if m != nil {
		return m.UiRoomIntentTagTf_28D
	}
	return nil
}

func (m *UserOffline) GetUiRoomIntentTagTfidf_28D() map[string]string {
	if m != nil {
		return m.UiRoomIntentTagTfidf_28D
	}
	return nil
}

func (m *UserOffline) GetMusicLabel() []string {
	if m != nil {
		return m.MusicLabel
	}
	return nil
}

type UserOffline_MusicLabelUpdate struct {
	MusicLabel           []string `protobuf:"bytes,1,rep,name=music_label,json=musicLabel,proto3" json:"music_label,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOffline_MusicLabelUpdate) Reset()         { *m = UserOffline_MusicLabelUpdate{} }
func (m *UserOffline_MusicLabelUpdate) String() string { return proto.CompactTextString(m) }
func (*UserOffline_MusicLabelUpdate) ProtoMessage()    {}
func (*UserOffline_MusicLabelUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{8, 3}
}
func (m *UserOffline_MusicLabelUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOffline_MusicLabelUpdate.Unmarshal(m, b)
}
func (m *UserOffline_MusicLabelUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOffline_MusicLabelUpdate.Marshal(b, m, deterministic)
}
func (dst *UserOffline_MusicLabelUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOffline_MusicLabelUpdate.Merge(dst, src)
}
func (m *UserOffline_MusicLabelUpdate) XXX_Size() int {
	return xxx_messageInfo_UserOffline_MusicLabelUpdate.Size(m)
}
func (m *UserOffline_MusicLabelUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOffline_MusicLabelUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserOffline_MusicLabelUpdate proto.InternalMessageInfo

func (m *UserOffline_MusicLabelUpdate) GetMusicLabel() []string {
	if m != nil {
		return m.MusicLabel
	}
	return nil
}

// 下发玩伴离线用户侧画像
type PlaymateUser struct {
	UPRecExposure_7D     uint32   `protobuf:"varint,1,opt,name=u_p_rec_exposure_7d,json=uPRecExposure7d,proto3" json:"u_p_rec_exposure_7d,omitempty"`
	UPRecClick_7D        uint32   `protobuf:"varint,2,opt,name=u_p_rec_click_7d,json=uPRecClick7d,proto3" json:"u_p_rec_click_7d,omitempty"`
	UPRecClickrate_7D    float64  `protobuf:"fixed64,3,opt,name=u_p_rec_clickrate_7d,json=uPRecClickrate7d,proto3" json:"u_p_rec_clickrate_7d,omitempty"`
	UPRecIm_7D           uint32   `protobuf:"varint,4,opt,name=u_p_rec_im_7d,json=uPRecIm7d,proto3" json:"u_p_rec_im_7d,omitempty"`
	UPRecImrate_7D       float64  `protobuf:"fixed64,5,opt,name=u_p_rec_imrate_7d,json=uPRecImrate7d,proto3" json:"u_p_rec_imrate_7d,omitempty"`
	UidFollow7           uint32   `protobuf:"varint,6,opt,name=uid_follow7,json=uidFollow7,proto3" json:"uid_follow7,omitempty"`
	UidFollowed7         uint32   `protobuf:"varint,7,opt,name=uid_followed7,json=uidFollowed7,proto3" json:"uid_followed7,omitempty"`
	UidFollow28          uint32   `protobuf:"varint,8,opt,name=uid_follow28,json=uidFollow28,proto3" json:"uid_follow28,omitempty"`
	UidFollowed28        uint32   `protobuf:"varint,9,opt,name=uid_followed28,json=uidFollowed28,proto3" json:"uid_followed28,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateUser) Reset()         { *m = PlaymateUser{} }
func (m *PlaymateUser) String() string { return proto.CompactTextString(m) }
func (*PlaymateUser) ProtoMessage()    {}
func (*PlaymateUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{9}
}
func (m *PlaymateUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateUser.Unmarshal(m, b)
}
func (m *PlaymateUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateUser.Marshal(b, m, deterministic)
}
func (dst *PlaymateUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateUser.Merge(dst, src)
}
func (m *PlaymateUser) XXX_Size() int {
	return xxx_messageInfo_PlaymateUser.Size(m)
}
func (m *PlaymateUser) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateUser.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateUser proto.InternalMessageInfo

func (m *PlaymateUser) GetUPRecExposure_7D() uint32 {
	if m != nil {
		return m.UPRecExposure_7D
	}
	return 0
}

func (m *PlaymateUser) GetUPRecClick_7D() uint32 {
	if m != nil {
		return m.UPRecClick_7D
	}
	return 0
}

func (m *PlaymateUser) GetUPRecClickrate_7D() float64 {
	if m != nil {
		return m.UPRecClickrate_7D
	}
	return 0
}

func (m *PlaymateUser) GetUPRecIm_7D() uint32 {
	if m != nil {
		return m.UPRecIm_7D
	}
	return 0
}

func (m *PlaymateUser) GetUPRecImrate_7D() float64 {
	if m != nil {
		return m.UPRecImrate_7D
	}
	return 0
}

func (m *PlaymateUser) GetUidFollow7() uint32 {
	if m != nil {
		return m.UidFollow7
	}
	return 0
}

func (m *PlaymateUser) GetUidFollowed7() uint32 {
	if m != nil {
		return m.UidFollowed7
	}
	return 0
}

func (m *PlaymateUser) GetUidFollow28() uint32 {
	if m != nil {
		return m.UidFollow28
	}
	return 0
}

func (m *PlaymateUser) GetUidFollowed28() uint32 {
	if m != nil {
		return m.UidFollowed28
	}
	return 0
}

type ChannelUser struct {
	UBrowseCount                    uint32                                 `protobuf:"varint,1,opt,name=u_browse_count,json=uBrowseCount,proto3" json:"u_browse_count,omitempty"`
	UClickCount                     uint32                                 `protobuf:"varint,2,opt,name=u_click_count,json=uClickCount,proto3" json:"u_click_count,omitempty"`
	UClickRate                      float64                                `protobuf:"fixed64,3,opt,name=u_click_rate,json=uClickRate,proto3" json:"u_click_rate,omitempty"`
	UExpDTagId                      map[uint32]*channel.ChannelExpDetail   `protobuf:"bytes,4,rep,name=u_exp_d_tag_id,json=uExpDTagId,proto3" json:"u_exp_d_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UEnterDTagId                    map[uint32]*channel.ChannelEnterDetail `protobuf:"bytes,5,rep,name=u_enter_d_tag_id,json=uEnterDTagId,proto3" json:"u_enter_d_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UEnterCount                     uint32                                 `protobuf:"varint,6,opt,name=u_enter_count,json=uEnterCount,proto3" json:"u_enter_count,omitempty"`
	UTeamCount                      uint32                                 `protobuf:"varint,7,opt,name=u_team_count,json=uTeamCount,proto3" json:"u_team_count,omitempty"`
	UTeamRate                       float64                                `protobuf:"fixed64,8,opt,name=u_team_rate,json=uTeamRate,proto3" json:"u_team_rate,omitempty"`
	UPRoomPref                      map[uint32]*UserRoomPref               `protobuf:"bytes,9,rep,name=u_p_room_pref,json=uPRoomPref,proto3" json:"u_p_room_pref,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UPSelectPref                    *UserSelectPref                        `protobuf:"bytes,10,opt,name=u_p_select_pref,json=uPSelectPref,proto3" json:"u_p_select_pref,omitempty"`
	UPrefRoomNameVec                string                                 `protobuf:"bytes,11,opt,name=u_pref_room_name_vec,json=uPrefRoomNameVec,proto3" json:"u_pref_room_name_vec,omitempty"`
	UPSelectTagPref                 map[uint32]*UserSelectPref             `protobuf:"bytes,12,rep,name=u_p_select_tag_pref,json=uPSelectTagPref,proto3" json:"u_p_select_tag_pref,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	UPSelectGenderPref              map[uint32]float64                     `protobuf:"bytes,13,rep,name=u_p_select_gender_pref,json=uPSelectGenderPref,proto3" json:"u_p_select_gender_pref,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPGamePrefPredictScore          map[string]float64                     `protobuf:"bytes,14,rep,name=u_p_game_pref_predict_score,json=uPGamePrefPredictScore,proto3" json:"u_p_game_pref_predict_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomNameTagPrefPredictScore   map[string]float64                     `protobuf:"bytes,15,rep,name=u_p_room_name_tag_pref_predict_score,json=uPRoomNameTagPrefPredictScore,proto3" json:"u_p_room_name_tag_pref_predict_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomNameTagPrefPredictScoreV1 map[string]float64                     `protobuf:"bytes,16,rep,name=u_p_room_name_tag_pref_predict_score_v1,json=uPRoomNameTagPrefPredictScoreV1,proto3" json:"u_p_room_name_tag_pref_predict_score_v1,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UEnter_28DTfTagId               map[uint32]float64                     `protobuf:"bytes,17,rep,name=u_enter_28d_tf_tag_id,json=uEnter28dTfTagId,proto3" json:"u_enter_28d_tf_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UGangup_28DTfTagId              map[uint32]float64                     `protobuf:"bytes,18,rep,name=u_gangup_28d_tf_tag_id,json=uGangup28dTfTagId,proto3" json:"u_gangup_28d_tf_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UFilter_28DTfTagId              map[uint32]float64                     `protobuf:"bytes,19,rep,name=u_filter_28d_tf_tag_id,json=uFilter28dTfTagId,proto3" json:"u_filter_28d_tf_tag_id,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral            struct{}                               `json:"-"`
	XXX_unrecognized                []byte                                 `json:"-"`
	XXX_sizecache                   int32                                  `json:"-"`
}

func (m *ChannelUser) Reset()         { *m = ChannelUser{} }
func (m *ChannelUser) String() string { return proto.CompactTextString(m) }
func (*ChannelUser) ProtoMessage()    {}
func (*ChannelUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{10}
}
func (m *ChannelUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelUser.Unmarshal(m, b)
}
func (m *ChannelUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelUser.Marshal(b, m, deterministic)
}
func (dst *ChannelUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelUser.Merge(dst, src)
}
func (m *ChannelUser) XXX_Size() int {
	return xxx_messageInfo_ChannelUser.Size(m)
}
func (m *ChannelUser) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelUser.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelUser proto.InternalMessageInfo

func (m *ChannelUser) GetUBrowseCount() uint32 {
	if m != nil {
		return m.UBrowseCount
	}
	return 0
}

func (m *ChannelUser) GetUClickCount() uint32 {
	if m != nil {
		return m.UClickCount
	}
	return 0
}

func (m *ChannelUser) GetUClickRate() float64 {
	if m != nil {
		return m.UClickRate
	}
	return 0
}

func (m *ChannelUser) GetUExpDTagId() map[uint32]*channel.ChannelExpDetail {
	if m != nil {
		return m.UExpDTagId
	}
	return nil
}

func (m *ChannelUser) GetUEnterDTagId() map[uint32]*channel.ChannelEnterDetail {
	if m != nil {
		return m.UEnterDTagId
	}
	return nil
}

func (m *ChannelUser) GetUEnterCount() uint32 {
	if m != nil {
		return m.UEnterCount
	}
	return 0
}

func (m *ChannelUser) GetUTeamCount() uint32 {
	if m != nil {
		return m.UTeamCount
	}
	return 0
}

func (m *ChannelUser) GetUTeamRate() float64 {
	if m != nil {
		return m.UTeamRate
	}
	return 0
}

func (m *ChannelUser) GetUPRoomPref() map[uint32]*UserRoomPref {
	if m != nil {
		return m.UPRoomPref
	}
	return nil
}

func (m *ChannelUser) GetUPSelectPref() *UserSelectPref {
	if m != nil {
		return m.UPSelectPref
	}
	return nil
}

func (m *ChannelUser) GetUPrefRoomNameVec() string {
	if m != nil {
		return m.UPrefRoomNameVec
	}
	return ""
}

func (m *ChannelUser) GetUPSelectTagPref() map[uint32]*UserSelectPref {
	if m != nil {
		return m.UPSelectTagPref
	}
	return nil
}

func (m *ChannelUser) GetUPSelectGenderPref() map[uint32]float64 {
	if m != nil {
		return m.UPSelectGenderPref
	}
	return nil
}

func (m *ChannelUser) GetUPGamePrefPredictScore() map[string]float64 {
	if m != nil {
		return m.UPGamePrefPredictScore
	}
	return nil
}

func (m *ChannelUser) GetUPRoomNameTagPrefPredictScore() map[string]float64 {
	if m != nil {
		return m.UPRoomNameTagPrefPredictScore
	}
	return nil
}

func (m *ChannelUser) GetUPRoomNameTagPrefPredictScoreV1() map[string]float64 {
	if m != nil {
		return m.UPRoomNameTagPrefPredictScoreV1
	}
	return nil
}

func (m *ChannelUser) GetUEnter_28DTfTagId() map[uint32]float64 {
	if m != nil {
		return m.UEnter_28DTfTagId
	}
	return nil
}

func (m *ChannelUser) GetUGangup_28DTfTagId() map[uint32]float64 {
	if m != nil {
		return m.UGangup_28DTfTagId
	}
	return nil
}

func (m *ChannelUser) GetUFilter_28DTfTagId() map[uint32]float64 {
	if m != nil {
		return m.UFilter_28DTfTagId
	}
	return nil
}

type UserRoomPref struct {
	UPRoomPrefMode       map[uint32]float64 `protobuf:"bytes,1,rep,name=u_p_room_pref_mode,json=uPRoomPrefMode,proto3" json:"u_p_room_pref_mode,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomPrefSocial     map[uint32]float64 `protobuf:"bytes,2,rep,name=u_p_room_pref_social,json=uPRoomPrefSocial,proto3" json:"u_p_room_pref_social,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomPrefNum        map[uint32]float64 `protobuf:"bytes,3,rep,name=u_p_room_pref_num,json=uPRoomPrefNum,proto3" json:"u_p_room_pref_num,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomPrefTheme      map[uint32]float64 `protobuf:"bytes,4,rep,name=u_p_room_pref_theme,json=uPRoomPrefTheme,proto3" json:"u_p_room_pref_theme,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomPrefRoute      map[uint32]float64 `protobuf:"bytes,5,rep,name=u_p_room_pref_route,json=uPRoomPrefRoute,proto3" json:"u_p_room_pref_route,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomPrefMap        map[uint32]float64 `protobuf:"bytes,6,rep,name=u_p_room_pref_map,json=uPRoomPrefMap,proto3" json:"u_p_room_pref_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPRoomPrefLevel      map[uint32]float64 `protobuf:"bytes,7,rep,name=u_p_room_pref_level,json=uPRoomPrefLevel,proto3" json:"u_p_room_pref_level,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UserRoomPref) Reset()         { *m = UserRoomPref{} }
func (m *UserRoomPref) String() string { return proto.CompactTextString(m) }
func (*UserRoomPref) ProtoMessage()    {}
func (*UserRoomPref) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{11}
}
func (m *UserRoomPref) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRoomPref.Unmarshal(m, b)
}
func (m *UserRoomPref) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRoomPref.Marshal(b, m, deterministic)
}
func (dst *UserRoomPref) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRoomPref.Merge(dst, src)
}
func (m *UserRoomPref) XXX_Size() int {
	return xxx_messageInfo_UserRoomPref.Size(m)
}
func (m *UserRoomPref) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRoomPref.DiscardUnknown(m)
}

var xxx_messageInfo_UserRoomPref proto.InternalMessageInfo

func (m *UserRoomPref) GetUPRoomPrefMode() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefMode
	}
	return nil
}

func (m *UserRoomPref) GetUPRoomPrefSocial() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefSocial
	}
	return nil
}

func (m *UserRoomPref) GetUPRoomPrefNum() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefNum
	}
	return nil
}

func (m *UserRoomPref) GetUPRoomPrefTheme() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefTheme
	}
	return nil
}

func (m *UserRoomPref) GetUPRoomPrefRoute() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefRoute
	}
	return nil
}

func (m *UserRoomPref) GetUPRoomPrefMap() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefMap
	}
	return nil
}

func (m *UserRoomPref) GetUPRoomPrefLevel() map[uint32]float64 {
	if m != nil {
		return m.UPRoomPrefLevel
	}
	return nil
}

type UserSelectPref struct {
	UPSelectPrefMode     map[uint32]float64 `protobuf:"bytes,1,rep,name=u_p_select_pref_mode,json=uPSelectPrefMode,proto3" json:"u_p_select_pref_mode,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPSelectPrefSocial   map[uint32]float64 `protobuf:"bytes,2,rep,name=u_p_select_pref_social,json=uPSelectPrefSocial,proto3" json:"u_p_select_pref_social,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPSelectPrefGender   map[uint32]float64 `protobuf:"bytes,3,rep,name=u_p_select_pref_gender,json=uPSelectPrefGender,proto3" json:"u_p_select_pref_gender,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPSelectPrefTheme    map[uint32]float64 `protobuf:"bytes,4,rep,name=u_p_select_pref_theme,json=uPSelectPrefTheme,proto3" json:"u_p_select_pref_theme,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPSelectPrefRoute    map[uint32]float64 `protobuf:"bytes,5,rep,name=u_p_select_pref_route,json=uPSelectPrefRoute,proto3" json:"u_p_select_pref_route,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPSelectPrefMap      map[uint32]float64 `protobuf:"bytes,6,rep,name=u_p_select_pref_map,json=uPSelectPrefMap,proto3" json:"u_p_select_pref_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPSelectPrefLevel    map[uint32]float64 `protobuf:"bytes,7,rep,name=u_p_select_pref_level,json=uPSelectPrefLevel,proto3" json:"u_p_select_pref_level,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UserSelectPref) Reset()         { *m = UserSelectPref{} }
func (m *UserSelectPref) String() string { return proto.CompactTextString(m) }
func (*UserSelectPref) ProtoMessage()    {}
func (*UserSelectPref) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{12}
}
func (m *UserSelectPref) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSelectPref.Unmarshal(m, b)
}
func (m *UserSelectPref) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSelectPref.Marshal(b, m, deterministic)
}
func (dst *UserSelectPref) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSelectPref.Merge(dst, src)
}
func (m *UserSelectPref) XXX_Size() int {
	return xxx_messageInfo_UserSelectPref.Size(m)
}
func (m *UserSelectPref) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSelectPref.DiscardUnknown(m)
}

var xxx_messageInfo_UserSelectPref proto.InternalMessageInfo

func (m *UserSelectPref) GetUPSelectPrefMode() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefMode
	}
	return nil
}

func (m *UserSelectPref) GetUPSelectPrefSocial() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefSocial
	}
	return nil
}

func (m *UserSelectPref) GetUPSelectPrefGender() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefGender
	}
	return nil
}

func (m *UserSelectPref) GetUPSelectPrefTheme() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefTheme
	}
	return nil
}

func (m *UserSelectPref) GetUPSelectPrefRoute() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefRoute
	}
	return nil
}

func (m *UserSelectPref) GetUPSelectPrefMap() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefMap
	}
	return nil
}

func (m *UserSelectPref) GetUPSelectPrefLevel() map[uint32]float64 {
	if m != nil {
		return m.UPSelectPrefLevel
	}
	return nil
}

// 是否核心用户相关的画像
type UserCore struct {
	FollowCount          uint32   `protobuf:"varint,1,opt,name=follow_count,json=followCount,proto3" json:"follow_count,omitempty"`
	PostCount            uint32   `protobuf:"varint,2,opt,name=post_count,json=postCount,proto3" json:"post_count,omitempty"`
	EnterChannelCount    uint32   `protobuf:"varint,3,opt,name=enter_channel_count,json=enterChannelCount,proto3" json:"enter_channel_count,omitempty"`
	ImCount              uint32   `protobuf:"varint,4,opt,name=im_count,json=imCount,proto3" json:"im_count,omitempty"`
	CpId                 string   `protobuf:"bytes,5,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	ChType               string   `protobuf:"bytes,6,opt,name=ch_type,json=chType,proto3" json:"ch_type,omitempty"`
	TgChTypeId           string   `protobuf:"bytes,7,opt,name=tg_ch_type_id,json=tgChTypeId,proto3" json:"tg_ch_type_id,omitempty"`
	MediaId              string   `protobuf:"bytes,8,opt,name=media_id,json=mediaId,proto3" json:"media_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserCore) Reset()         { *m = UserCore{} }
func (m *UserCore) String() string { return proto.CompactTextString(m) }
func (*UserCore) ProtoMessage()    {}
func (*UserCore) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{13}
}
func (m *UserCore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserCore.Unmarshal(m, b)
}
func (m *UserCore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserCore.Marshal(b, m, deterministic)
}
func (dst *UserCore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserCore.Merge(dst, src)
}
func (m *UserCore) XXX_Size() int {
	return xxx_messageInfo_UserCore.Size(m)
}
func (m *UserCore) XXX_DiscardUnknown() {
	xxx_messageInfo_UserCore.DiscardUnknown(m)
}

var xxx_messageInfo_UserCore proto.InternalMessageInfo

func (m *UserCore) GetFollowCount() uint32 {
	if m != nil {
		return m.FollowCount
	}
	return 0
}

func (m *UserCore) GetPostCount() uint32 {
	if m != nil {
		return m.PostCount
	}
	return 0
}

func (m *UserCore) GetEnterChannelCount() uint32 {
	if m != nil {
		return m.EnterChannelCount
	}
	return 0
}

func (m *UserCore) GetImCount() uint32 {
	if m != nil {
		return m.ImCount
	}
	return 0
}

func (m *UserCore) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

func (m *UserCore) GetChType() string {
	if m != nil {
		return m.ChType
	}
	return ""
}

func (m *UserCore) GetTgChTypeId() string {
	if m != nil {
		return m.TgChTypeId
	}
	return ""
}

func (m *UserCore) GetMediaId() string {
	if m != nil {
		return m.MediaId
	}
	return ""
}

// 开黑
type UserGangupRl struct {
	GangupTimeList       []uint32 `protobuf:"varint,1,rep,packed,name=gangup_time_list,json=gangupTimeList,proto3" json:"gangup_time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGangupRl) Reset()         { *m = UserGangupRl{} }
func (m *UserGangupRl) String() string { return proto.CompactTextString(m) }
func (*UserGangupRl) ProtoMessage()    {}
func (*UserGangupRl) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{14}
}
func (m *UserGangupRl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGangupRl.Unmarshal(m, b)
}
func (m *UserGangupRl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGangupRl.Marshal(b, m, deterministic)
}
func (dst *UserGangupRl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGangupRl.Merge(dst, src)
}
func (m *UserGangupRl) XXX_Size() int {
	return xxx_messageInfo_UserGangupRl.Size(m)
}
func (m *UserGangupRl) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGangupRl.DiscardUnknown(m)
}

var xxx_messageInfo_UserGangupRl proto.InternalMessageInfo

func (m *UserGangupRl) GetGangupTimeList() []uint32 {
	if m != nil {
		return m.GangupTimeList
	}
	return nil
}

type UserGangupRl_Append struct {
	GangupTimeList       []uint32 `protobuf:"varint,1,rep,packed,name=gangup_time_list,json=gangupTimeList,proto3" json:"gangup_time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserGangupRl_Append) Reset()         { *m = UserGangupRl_Append{} }
func (m *UserGangupRl_Append) String() string { return proto.CompactTextString(m) }
func (*UserGangupRl_Append) ProtoMessage()    {}
func (*UserGangupRl_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{14, 0}
}
func (m *UserGangupRl_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGangupRl_Append.Unmarshal(m, b)
}
func (m *UserGangupRl_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGangupRl_Append.Marshal(b, m, deterministic)
}
func (dst *UserGangupRl_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGangupRl_Append.Merge(dst, src)
}
func (m *UserGangupRl_Append) XXX_Size() int {
	return xxx_messageInfo_UserGangupRl_Append.Size(m)
}
func (m *UserGangupRl_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGangupRl_Append.DiscardUnknown(m)
}

var xxx_messageInfo_UserGangupRl_Append proto.InternalMessageInfo

func (m *UserGangupRl_Append) GetGangupTimeList() []uint32 {
	if m != nil {
		return m.GangupTimeList
	}
	return nil
}

// 单聊
type User7DayImRl struct {
	ImTimeList           []uint32 `protobuf:"varint,1,rep,packed,name=im_time_list,json=imTimeList,proto3" json:"im_time_list,omitempty"`
	LastImTime           uint32   `protobuf:"varint,2,opt,name=last_im_time,json=lastImTime,proto3" json:"last_im_time,omitempty"`
	ImCount              uint32   `protobuf:"varint,3,opt,name=im_count,json=imCount,proto3" json:"im_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User7DayImRl) Reset()         { *m = User7DayImRl{} }
func (m *User7DayImRl) String() string { return proto.CompactTextString(m) }
func (*User7DayImRl) ProtoMessage()    {}
func (*User7DayImRl) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{15}
}
func (m *User7DayImRl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User7DayImRl.Unmarshal(m, b)
}
func (m *User7DayImRl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User7DayImRl.Marshal(b, m, deterministic)
}
func (dst *User7DayImRl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User7DayImRl.Merge(dst, src)
}
func (m *User7DayImRl) XXX_Size() int {
	return xxx_messageInfo_User7DayImRl.Size(m)
}
func (m *User7DayImRl) XXX_DiscardUnknown() {
	xxx_messageInfo_User7DayImRl.DiscardUnknown(m)
}

var xxx_messageInfo_User7DayImRl proto.InternalMessageInfo

func (m *User7DayImRl) GetImTimeList() []uint32 {
	if m != nil {
		return m.ImTimeList
	}
	return nil
}

func (m *User7DayImRl) GetLastImTime() uint32 {
	if m != nil {
		return m.LastImTime
	}
	return 0
}

func (m *User7DayImRl) GetImCount() uint32 {
	if m != nil {
		return m.ImCount
	}
	return 0
}

type User7DayImRl_Append struct {
	ImTimeList           []uint32 `protobuf:"varint,1,rep,packed,name=im_time_list,json=imTimeList,proto3" json:"im_time_list,omitempty"`
	LastImTime           uint32   `protobuf:"varint,2,opt,name=last_im_time,json=lastImTime,proto3" json:"last_im_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User7DayImRl_Append) Reset()         { *m = User7DayImRl_Append{} }
func (m *User7DayImRl_Append) String() string { return proto.CompactTextString(m) }
func (*User7DayImRl_Append) ProtoMessage()    {}
func (*User7DayImRl_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{15, 0}
}
func (m *User7DayImRl_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User7DayImRl_Append.Unmarshal(m, b)
}
func (m *User7DayImRl_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User7DayImRl_Append.Marshal(b, m, deterministic)
}
func (dst *User7DayImRl_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User7DayImRl_Append.Merge(dst, src)
}
func (m *User7DayImRl_Append) XXX_Size() int {
	return xxx_messageInfo_User7DayImRl_Append.Size(m)
}
func (m *User7DayImRl_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_User7DayImRl_Append.DiscardUnknown(m)
}

var xxx_messageInfo_User7DayImRl_Append proto.InternalMessageInfo

func (m *User7DayImRl_Append) GetImTimeList() []uint32 {
	if m != nil {
		return m.ImTimeList
	}
	return nil
}

func (m *User7DayImRl_Append) GetLastImTime() uint32 {
	if m != nil {
		return m.LastImTime
	}
	return 0
}

type User7DayImRl_User7DayImSimpleRl struct {
	LastImTime           uint32   `protobuf:"varint,1,opt,name=last_im_time,json=lastImTime,proto3" json:"last_im_time,omitempty"`
	ImCount              uint32   `protobuf:"varint,2,opt,name=im_count,json=imCount,proto3" json:"im_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *User7DayImRl_User7DayImSimpleRl) Reset()         { *m = User7DayImRl_User7DayImSimpleRl{} }
func (m *User7DayImRl_User7DayImSimpleRl) String() string { return proto.CompactTextString(m) }
func (*User7DayImRl_User7DayImSimpleRl) ProtoMessage()    {}
func (*User7DayImRl_User7DayImSimpleRl) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{15, 1}
}
func (m *User7DayImRl_User7DayImSimpleRl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_User7DayImRl_User7DayImSimpleRl.Unmarshal(m, b)
}
func (m *User7DayImRl_User7DayImSimpleRl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_User7DayImRl_User7DayImSimpleRl.Marshal(b, m, deterministic)
}
func (dst *User7DayImRl_User7DayImSimpleRl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_User7DayImRl_User7DayImSimpleRl.Merge(dst, src)
}
func (m *User7DayImRl_User7DayImSimpleRl) XXX_Size() int {
	return xxx_messageInfo_User7DayImRl_User7DayImSimpleRl.Size(m)
}
func (m *User7DayImRl_User7DayImSimpleRl) XXX_DiscardUnknown() {
	xxx_messageInfo_User7DayImRl_User7DayImSimpleRl.DiscardUnknown(m)
}

var xxx_messageInfo_User7DayImRl_User7DayImSimpleRl proto.InternalMessageInfo

func (m *User7DayImRl_User7DayImSimpleRl) GetLastImTime() uint32 {
	if m != nil {
		return m.LastImTime
	}
	return 0
}

func (m *User7DayImRl_User7DayImSimpleRl) GetImCount() uint32 {
	if m != nil {
		return m.ImCount
	}
	return 0
}

// 互聊
type UserMutualImRl struct {
	ImTimeList           []uint32 `protobuf:"varint,1,rep,packed,name=im_time_list,json=imTimeList,proto3" json:"im_time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMutualImRl) Reset()         { *m = UserMutualImRl{} }
func (m *UserMutualImRl) String() string { return proto.CompactTextString(m) }
func (*UserMutualImRl) ProtoMessage()    {}
func (*UserMutualImRl) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{16}
}
func (m *UserMutualImRl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMutualImRl.Unmarshal(m, b)
}
func (m *UserMutualImRl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMutualImRl.Marshal(b, m, deterministic)
}
func (dst *UserMutualImRl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMutualImRl.Merge(dst, src)
}
func (m *UserMutualImRl) XXX_Size() int {
	return xxx_messageInfo_UserMutualImRl.Size(m)
}
func (m *UserMutualImRl) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMutualImRl.DiscardUnknown(m)
}

var xxx_messageInfo_UserMutualImRl proto.InternalMessageInfo

func (m *UserMutualImRl) GetImTimeList() []uint32 {
	if m != nil {
		return m.ImTimeList
	}
	return nil
}

type UserMutualImRl_Append struct {
	ImTimeList           []uint32 `protobuf:"varint,1,rep,packed,name=im_time_list,json=imTimeList,proto3" json:"im_time_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMutualImRl_Append) Reset()         { *m = UserMutualImRl_Append{} }
func (m *UserMutualImRl_Append) String() string { return proto.CompactTextString(m) }
func (*UserMutualImRl_Append) ProtoMessage()    {}
func (*UserMutualImRl_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{16, 0}
}
func (m *UserMutualImRl_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMutualImRl_Append.Unmarshal(m, b)
}
func (m *UserMutualImRl_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMutualImRl_Append.Marshal(b, m, deterministic)
}
func (dst *UserMutualImRl_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMutualImRl_Append.Merge(dst, src)
}
func (m *UserMutualImRl_Append) XXX_Size() int {
	return xxx_messageInfo_UserMutualImRl_Append.Size(m)
}
func (m *UserMutualImRl_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMutualImRl_Append.DiscardUnknown(m)
}

var xxx_messageInfo_UserMutualImRl_Append proto.InternalMessageInfo

func (m *UserMutualImRl_Append) GetImTimeList() []uint32 {
	if m != nil {
		return m.ImTimeList
	}
	return nil
}

type UserHistoryAction struct {
	GangupList           []uint32 `protobuf:"varint,1,rep,packed,name=gangup_list,json=gangupList,proto3" json:"gangup_list,omitempty"`
	ImList               []uint32 `protobuf:"varint,2,rep,packed,name=im_list,json=imList,proto3" json:"im_list,omitempty"`
	RoomOwnerList        []uint32 `protobuf:"varint,3,rep,packed,name=room_owner_list,json=roomOwnerList,proto3" json:"room_owner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction) Reset()         { *m = UserHistoryAction{} }
func (m *UserHistoryAction) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction) ProtoMessage()    {}
func (*UserHistoryAction) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17}
}
func (m *UserHistoryAction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction.Unmarshal(m, b)
}
func (m *UserHistoryAction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction.Merge(dst, src)
}
func (m *UserHistoryAction) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction.Size(m)
}
func (m *UserHistoryAction) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction proto.InternalMessageInfo

func (m *UserHistoryAction) GetGangupList() []uint32 {
	if m != nil {
		return m.GangupList
	}
	return nil
}

func (m *UserHistoryAction) GetImList() []uint32 {
	if m != nil {
		return m.ImList
	}
	return nil
}

func (m *UserHistoryAction) GetRoomOwnerList() []uint32 {
	if m != nil {
		return m.RoomOwnerList
	}
	return nil
}

type UserHistoryAction_GangUp struct {
	GangupList           []uint32 `protobuf:"varint,1,rep,packed,name=gangup_list,json=gangupList,proto3" json:"gangup_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_GangUp) Reset()         { *m = UserHistoryAction_GangUp{} }
func (m *UserHistoryAction_GangUp) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_GangUp) ProtoMessage()    {}
func (*UserHistoryAction_GangUp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 0}
}
func (m *UserHistoryAction_GangUp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_GangUp.Unmarshal(m, b)
}
func (m *UserHistoryAction_GangUp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_GangUp.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_GangUp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_GangUp.Merge(dst, src)
}
func (m *UserHistoryAction_GangUp) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_GangUp.Size(m)
}
func (m *UserHistoryAction_GangUp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_GangUp.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_GangUp proto.InternalMessageInfo

func (m *UserHistoryAction_GangUp) GetGangupList() []uint32 {
	if m != nil {
		return m.GangupList
	}
	return nil
}

type UserHistoryAction_Im struct {
	ImList               []uint32 `protobuf:"varint,1,rep,packed,name=im_list,json=imList,proto3" json:"im_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_Im) Reset()         { *m = UserHistoryAction_Im{} }
func (m *UserHistoryAction_Im) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_Im) ProtoMessage()    {}
func (*UserHistoryAction_Im) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 1}
}
func (m *UserHistoryAction_Im) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_Im.Unmarshal(m, b)
}
func (m *UserHistoryAction_Im) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_Im.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_Im) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_Im.Merge(dst, src)
}
func (m *UserHistoryAction_Im) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_Im.Size(m)
}
func (m *UserHistoryAction_Im) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_Im.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_Im proto.InternalMessageInfo

func (m *UserHistoryAction_Im) GetImList() []uint32 {
	if m != nil {
		return m.ImList
	}
	return nil
}

type UserHistoryAction_Room struct {
	RoomOwnerList        []uint32 `protobuf:"varint,1,rep,packed,name=room_owner_list,json=roomOwnerList,proto3" json:"room_owner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_Room) Reset()         { *m = UserHistoryAction_Room{} }
func (m *UserHistoryAction_Room) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_Room) ProtoMessage()    {}
func (*UserHistoryAction_Room) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 2}
}
func (m *UserHistoryAction_Room) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_Room.Unmarshal(m, b)
}
func (m *UserHistoryAction_Room) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_Room.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_Room) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_Room.Merge(dst, src)
}
func (m *UserHistoryAction_Room) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_Room.Size(m)
}
func (m *UserHistoryAction_Room) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_Room.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_Room proto.InternalMessageInfo

func (m *UserHistoryAction_Room) GetRoomOwnerList() []uint32 {
	if m != nil {
		return m.RoomOwnerList
	}
	return nil
}

type UserHistoryAction_AppendGangUp struct {
	GangupList           []uint32 `protobuf:"varint,1,rep,packed,name=gangup_list,json=gangupList,proto3" json:"gangup_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_AppendGangUp) Reset()         { *m = UserHistoryAction_AppendGangUp{} }
func (m *UserHistoryAction_AppendGangUp) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_AppendGangUp) ProtoMessage()    {}
func (*UserHistoryAction_AppendGangUp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 3}
}
func (m *UserHistoryAction_AppendGangUp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_AppendGangUp.Unmarshal(m, b)
}
func (m *UserHistoryAction_AppendGangUp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_AppendGangUp.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_AppendGangUp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_AppendGangUp.Merge(dst, src)
}
func (m *UserHistoryAction_AppendGangUp) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_AppendGangUp.Size(m)
}
func (m *UserHistoryAction_AppendGangUp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_AppendGangUp.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_AppendGangUp proto.InternalMessageInfo

func (m *UserHistoryAction_AppendGangUp) GetGangupList() []uint32 {
	if m != nil {
		return m.GangupList
	}
	return nil
}

type UserHistoryAction_AppendIm struct {
	ImList               []uint32 `protobuf:"varint,1,rep,packed,name=im_list,json=imList,proto3" json:"im_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_AppendIm) Reset()         { *m = UserHistoryAction_AppendIm{} }
func (m *UserHistoryAction_AppendIm) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_AppendIm) ProtoMessage()    {}
func (*UserHistoryAction_AppendIm) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 4}
}
func (m *UserHistoryAction_AppendIm) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_AppendIm.Unmarshal(m, b)
}
func (m *UserHistoryAction_AppendIm) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_AppendIm.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_AppendIm) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_AppendIm.Merge(dst, src)
}
func (m *UserHistoryAction_AppendIm) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_AppendIm.Size(m)
}
func (m *UserHistoryAction_AppendIm) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_AppendIm.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_AppendIm proto.InternalMessageInfo

func (m *UserHistoryAction_AppendIm) GetImList() []uint32 {
	if m != nil {
		return m.ImList
	}
	return nil
}

type UserHistoryAction_AppendRoom struct {
	RoomOwnerList        []uint32 `protobuf:"varint,1,rep,packed,name=room_owner_list,json=roomOwnerList,proto3" json:"room_owner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_AppendRoom) Reset()         { *m = UserHistoryAction_AppendRoom{} }
func (m *UserHistoryAction_AppendRoom) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_AppendRoom) ProtoMessage()    {}
func (*UserHistoryAction_AppendRoom) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 5}
}
func (m *UserHistoryAction_AppendRoom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_AppendRoom.Unmarshal(m, b)
}
func (m *UserHistoryAction_AppendRoom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_AppendRoom.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_AppendRoom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_AppendRoom.Merge(dst, src)
}
func (m *UserHistoryAction_AppendRoom) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_AppendRoom.Size(m)
}
func (m *UserHistoryAction_AppendRoom) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_AppendRoom.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_AppendRoom proto.InternalMessageInfo

func (m *UserHistoryAction_AppendRoom) GetRoomOwnerList() []uint32 {
	if m != nil {
		return m.RoomOwnerList
	}
	return nil
}

type UserHistoryAction_DeleteGangUp struct {
	GangupExpireCnt      uint32   `protobuf:"varint,1,opt,name=gangup_expire_cnt,json=gangupExpireCnt,proto3" json:"gangup_expire_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_DeleteGangUp) Reset()         { *m = UserHistoryAction_DeleteGangUp{} }
func (m *UserHistoryAction_DeleteGangUp) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_DeleteGangUp) ProtoMessage()    {}
func (*UserHistoryAction_DeleteGangUp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 6}
}
func (m *UserHistoryAction_DeleteGangUp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_DeleteGangUp.Unmarshal(m, b)
}
func (m *UserHistoryAction_DeleteGangUp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_DeleteGangUp.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_DeleteGangUp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_DeleteGangUp.Merge(dst, src)
}
func (m *UserHistoryAction_DeleteGangUp) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_DeleteGangUp.Size(m)
}
func (m *UserHistoryAction_DeleteGangUp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_DeleteGangUp.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_DeleteGangUp proto.InternalMessageInfo

func (m *UserHistoryAction_DeleteGangUp) GetGangupExpireCnt() uint32 {
	if m != nil {
		return m.GangupExpireCnt
	}
	return 0
}

type UserHistoryAction_DeleteIm struct {
	ImExpireCnt          uint32   `protobuf:"varint,1,opt,name=im_expire_cnt,json=imExpireCnt,proto3" json:"im_expire_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_DeleteIm) Reset()         { *m = UserHistoryAction_DeleteIm{} }
func (m *UserHistoryAction_DeleteIm) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_DeleteIm) ProtoMessage()    {}
func (*UserHistoryAction_DeleteIm) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 7}
}
func (m *UserHistoryAction_DeleteIm) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_DeleteIm.Unmarshal(m, b)
}
func (m *UserHistoryAction_DeleteIm) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_DeleteIm.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_DeleteIm) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_DeleteIm.Merge(dst, src)
}
func (m *UserHistoryAction_DeleteIm) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_DeleteIm.Size(m)
}
func (m *UserHistoryAction_DeleteIm) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_DeleteIm.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_DeleteIm proto.InternalMessageInfo

func (m *UserHistoryAction_DeleteIm) GetImExpireCnt() uint32 {
	if m != nil {
		return m.ImExpireCnt
	}
	return 0
}

type UserHistoryAction_DeleteRoom struct {
	RoomExpireCnt        uint32   `protobuf:"varint,1,opt,name=room_expire_cnt,json=roomExpireCnt,proto3" json:"room_expire_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserHistoryAction_DeleteRoom) Reset()         { *m = UserHistoryAction_DeleteRoom{} }
func (m *UserHistoryAction_DeleteRoom) String() string { return proto.CompactTextString(m) }
func (*UserHistoryAction_DeleteRoom) ProtoMessage()    {}
func (*UserHistoryAction_DeleteRoom) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{17, 8}
}
func (m *UserHistoryAction_DeleteRoom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserHistoryAction_DeleteRoom.Unmarshal(m, b)
}
func (m *UserHistoryAction_DeleteRoom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserHistoryAction_DeleteRoom.Marshal(b, m, deterministic)
}
func (dst *UserHistoryAction_DeleteRoom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserHistoryAction_DeleteRoom.Merge(dst, src)
}
func (m *UserHistoryAction_DeleteRoom) XXX_Size() int {
	return xxx_messageInfo_UserHistoryAction_DeleteRoom.Size(m)
}
func (m *UserHistoryAction_DeleteRoom) XXX_DiscardUnknown() {
	xxx_messageInfo_UserHistoryAction_DeleteRoom.DiscardUnknown(m)
}

var xxx_messageInfo_UserHistoryAction_DeleteRoom proto.InternalMessageInfo

func (m *UserHistoryAction_DeleteRoom) GetRoomExpireCnt() uint32 {
	if m != nil {
		return m.RoomExpireCnt
	}
	return 0
}

// 用户关注关系
type UserFollowRL struct {
	Follow               bool     `protobuf:"varint,1,opt,name=follow,proto3" json:"follow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFollowRL) Reset()         { *m = UserFollowRL{} }
func (m *UserFollowRL) String() string { return proto.CompactTextString(m) }
func (*UserFollowRL) ProtoMessage()    {}
func (*UserFollowRL) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{18}
}
func (m *UserFollowRL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFollowRL.Unmarshal(m, b)
}
func (m *UserFollowRL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFollowRL.Marshal(b, m, deterministic)
}
func (dst *UserFollowRL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFollowRL.Merge(dst, src)
}
func (m *UserFollowRL) XXX_Size() int {
	return xxx_messageInfo_UserFollowRL.Size(m)
}
func (m *UserFollowRL) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFollowRL.DiscardUnknown(m)
}

var xxx_messageInfo_UserFollowRL proto.InternalMessageInfo

func (m *UserFollowRL) GetFollow() bool {
	if m != nil {
		return m.Follow
	}
	return false
}

type UserFollowRL_Follow struct {
	Follow               bool     `protobuf:"varint,1,opt,name=follow,proto3" json:"follow,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFollowRL_Follow) Reset()         { *m = UserFollowRL_Follow{} }
func (m *UserFollowRL_Follow) String() string { return proto.CompactTextString(m) }
func (*UserFollowRL_Follow) ProtoMessage()    {}
func (*UserFollowRL_Follow) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{18, 0}
}
func (m *UserFollowRL_Follow) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFollowRL_Follow.Unmarshal(m, b)
}
func (m *UserFollowRL_Follow) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFollowRL_Follow.Marshal(b, m, deterministic)
}
func (dst *UserFollowRL_Follow) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFollowRL_Follow.Merge(dst, src)
}
func (m *UserFollowRL_Follow) XXX_Size() int {
	return xxx_messageInfo_UserFollowRL_Follow.Size(m)
}
func (m *UserFollowRL_Follow) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFollowRL_Follow.DiscardUnknown(m)
}

var xxx_messageInfo_UserFollowRL_Follow proto.InternalMessageInfo

func (m *UserFollowRL_Follow) GetFollow() bool {
	if m != nil {
		return m.Follow
	}
	return false
}

// 用户加入的粉丝团关系
type UserFansRL struct {
	// 因为要存储，属性不能为空，至少需要一个属性，尽管这个属性没什么用
	IsValid              bool     `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFansRL) Reset()         { *m = UserFansRL{} }
func (m *UserFansRL) String() string { return proto.CompactTextString(m) }
func (*UserFansRL) ProtoMessage()    {}
func (*UserFansRL) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{19}
}
func (m *UserFansRL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFansRL.Unmarshal(m, b)
}
func (m *UserFansRL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFansRL.Marshal(b, m, deterministic)
}
func (dst *UserFansRL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFansRL.Merge(dst, src)
}
func (m *UserFansRL) XXX_Size() int {
	return xxx_messageInfo_UserFansRL.Size(m)
}
func (m *UserFansRL) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFansRL.DiscardUnknown(m)
}

var xxx_messageInfo_UserFansRL proto.InternalMessageInfo

func (m *UserFansRL) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

type UserFansRL_IsValid struct {
	IsValid              bool     `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFansRL_IsValid) Reset()         { *m = UserFansRL_IsValid{} }
func (m *UserFansRL_IsValid) String() string { return proto.CompactTextString(m) }
func (*UserFansRL_IsValid) ProtoMessage()    {}
func (*UserFansRL_IsValid) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{19, 0}
}
func (m *UserFansRL_IsValid) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFansRL_IsValid.Unmarshal(m, b)
}
func (m *UserFansRL_IsValid) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFansRL_IsValid.Marshal(b, m, deterministic)
}
func (dst *UserFansRL_IsValid) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFansRL_IsValid.Merge(dst, src)
}
func (m *UserFansRL_IsValid) XXX_Size() int {
	return xxx_messageInfo_UserFansRL_IsValid.Size(m)
}
func (m *UserFansRL_IsValid) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFansRL_IsValid.DiscardUnknown(m)
}

var xxx_messageInfo_UserFansRL_IsValid proto.InternalMessageInfo

func (m *UserFansRL_IsValid) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

// 用户拉黑关系
type BlackUserRL struct {
	Black                bool     `protobuf:"varint,1,opt,name=black,proto3" json:"black,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlackUserRL) Reset()         { *m = BlackUserRL{} }
func (m *BlackUserRL) String() string { return proto.CompactTextString(m) }
func (*BlackUserRL) ProtoMessage()    {}
func (*BlackUserRL) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{20}
}
func (m *BlackUserRL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlackUserRL.Unmarshal(m, b)
}
func (m *BlackUserRL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlackUserRL.Marshal(b, m, deterministic)
}
func (dst *BlackUserRL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlackUserRL.Merge(dst, src)
}
func (m *BlackUserRL) XXX_Size() int {
	return xxx_messageInfo_BlackUserRL.Size(m)
}
func (m *BlackUserRL) XXX_DiscardUnknown() {
	xxx_messageInfo_BlackUserRL.DiscardUnknown(m)
}

var xxx_messageInfo_BlackUserRL proto.InternalMessageInfo

func (m *BlackUserRL) GetBlack() bool {
	if m != nil {
		return m.Black
	}
	return false
}

type BlackUserRL_Black struct {
	Black                bool     `protobuf:"varint,1,opt,name=black,proto3" json:"black,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlackUserRL_Black) Reset()         { *m = BlackUserRL_Black{} }
func (m *BlackUserRL_Black) String() string { return proto.CompactTextString(m) }
func (*BlackUserRL_Black) ProtoMessage()    {}
func (*BlackUserRL_Black) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{20, 0}
}
func (m *BlackUserRL_Black) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlackUserRL_Black.Unmarshal(m, b)
}
func (m *BlackUserRL_Black) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlackUserRL_Black.Marshal(b, m, deterministic)
}
func (dst *BlackUserRL_Black) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlackUserRL_Black.Merge(dst, src)
}
func (m *BlackUserRL_Black) XXX_Size() int {
	return xxx_messageInfo_BlackUserRL_Black.Size(m)
}
func (m *BlackUserRL_Black) XXX_DiscardUnknown() {
	xxx_messageInfo_BlackUserRL_Black.DiscardUnknown(m)
}

var xxx_messageInfo_BlackUserRL_Black proto.InternalMessageInfo

func (m *BlackUserRL_Black) GetBlack() bool {
	if m != nil {
		return m.Black
	}
	return false
}

// 用户房间属性
type UserPersonalChannel struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	PublishTime          uint32   `protobuf:"varint,3,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPersonalChannel) Reset()         { *m = UserPersonalChannel{} }
func (m *UserPersonalChannel) String() string { return proto.CompactTextString(m) }
func (*UserPersonalChannel) ProtoMessage()    {}
func (*UserPersonalChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{21}
}
func (m *UserPersonalChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPersonalChannel.Unmarshal(m, b)
}
func (m *UserPersonalChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPersonalChannel.Marshal(b, m, deterministic)
}
func (dst *UserPersonalChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPersonalChannel.Merge(dst, src)
}
func (m *UserPersonalChannel) XXX_Size() int {
	return xxx_messageInfo_UserPersonalChannel.Size(m)
}
func (m *UserPersonalChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPersonalChannel.DiscardUnknown(m)
}

var xxx_messageInfo_UserPersonalChannel proto.InternalMessageInfo

func (m *UserPersonalChannel) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserPersonalChannel) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *UserPersonalChannel) GetPublishTime() uint32 {
	if m != nil {
		return m.PublishTime
	}
	return 0
}

type UserPersonalChannel_UserChannelBasicUpdate struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelType          uint32   `protobuf:"varint,2,opt,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty"`
	PublishTime          uint32   `protobuf:"varint,3,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPersonalChannel_UserChannelBasicUpdate) Reset() {
	*m = UserPersonalChannel_UserChannelBasicUpdate{}
}
func (m *UserPersonalChannel_UserChannelBasicUpdate) String() string {
	return proto.CompactTextString(m)
}
func (*UserPersonalChannel_UserChannelBasicUpdate) ProtoMessage() {}
func (*UserPersonalChannel_UserChannelBasicUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{21, 0}
}
func (m *UserPersonalChannel_UserChannelBasicUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPersonalChannel_UserChannelBasicUpdate.Unmarshal(m, b)
}
func (m *UserPersonalChannel_UserChannelBasicUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPersonalChannel_UserChannelBasicUpdate.Marshal(b, m, deterministic)
}
func (dst *UserPersonalChannel_UserChannelBasicUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPersonalChannel_UserChannelBasicUpdate.Merge(dst, src)
}
func (m *UserPersonalChannel_UserChannelBasicUpdate) XXX_Size() int {
	return xxx_messageInfo_UserPersonalChannel_UserChannelBasicUpdate.Size(m)
}
func (m *UserPersonalChannel_UserChannelBasicUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPersonalChannel_UserChannelBasicUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserPersonalChannel_UserChannelBasicUpdate proto.InternalMessageInfo

func (m *UserPersonalChannel_UserChannelBasicUpdate) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserPersonalChannel_UserChannelBasicUpdate) GetChannelType() uint32 {
	if m != nil {
		return m.ChannelType
	}
	return 0
}

func (m *UserPersonalChannel_UserChannelBasicUpdate) GetPublishTime() uint32 {
	if m != nil {
		return m.PublishTime
	}
	return 0
}

// 用户房间关系
type UserChannelRL struct {
	Own                  bool     `protobuf:"varint,1,opt,name=own,proto3" json:"own,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannelRL) Reset()         { *m = UserChannelRL{} }
func (m *UserChannelRL) String() string { return proto.CompactTextString(m) }
func (*UserChannelRL) ProtoMessage()    {}
func (*UserChannelRL) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{22}
}
func (m *UserChannelRL) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannelRL.Unmarshal(m, b)
}
func (m *UserChannelRL) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannelRL.Marshal(b, m, deterministic)
}
func (dst *UserChannelRL) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannelRL.Merge(dst, src)
}
func (m *UserChannelRL) XXX_Size() int {
	return xxx_messageInfo_UserChannelRL.Size(m)
}
func (m *UserChannelRL) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannelRL.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannelRL proto.InternalMessageInfo

func (m *UserChannelRL) GetOwn() bool {
	if m != nil {
		return m.Own
	}
	return false
}

type UserChannelRL_Own struct {
	Own                  bool     `protobuf:"varint,1,opt,name=own,proto3" json:"own,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannelRL_Own) Reset()         { *m = UserChannelRL_Own{} }
func (m *UserChannelRL_Own) String() string { return proto.CompactTextString(m) }
func (*UserChannelRL_Own) ProtoMessage()    {}
func (*UserChannelRL_Own) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{22, 0}
}
func (m *UserChannelRL_Own) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannelRL_Own.Unmarshal(m, b)
}
func (m *UserChannelRL_Own) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannelRL_Own.Marshal(b, m, deterministic)
}
func (dst *UserChannelRL_Own) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannelRL_Own.Merge(dst, src)
}
func (m *UserChannelRL_Own) XXX_Size() int {
	return xxx_messageInfo_UserChannelRL_Own.Size(m)
}
func (m *UserChannelRL_Own) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannelRL_Own.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannelRL_Own proto.InternalMessageInfo

func (m *UserChannelRL_Own) GetOwn() bool {
	if m != nil {
		return m.Own
	}
	return false
}

// 直播房用户侧画像
type ChannelLiveUserOffline struct {
	UPLiveLoginCnt_3D       uint32             `protobuf:"varint,1,opt,name=u_p_live_login_cnt_3d,json=uPLiveLoginCnt3d,proto3" json:"u_p_live_login_cnt_3d,omitempty"`
	UPLiveEnterCnt_3D       uint32             `protobuf:"varint,2,opt,name=u_p_live_enter_cnt_3d,json=uPLiveEnterCnt3d,proto3" json:"u_p_live_enter_cnt_3d,omitempty"`
	UPLiveEnterRoomCnt_3D   uint32             `protobuf:"varint,3,opt,name=u_p_live_enter_room_cnt_3d,json=uPLiveEnterRoomCnt3d,proto3" json:"u_p_live_enter_room_cnt_3d,omitempty"`
	UPLiveTotalDuration_3D  uint32             `protobuf:"varint,4,opt,name=u_p_live_total_duration_3d,json=uPLiveTotalDuration3d,proto3" json:"u_p_live_total_duration_3d,omitempty"`
	UPLiveFollowRoomCnt_3D  uint32             `protobuf:"varint,5,opt,name=u_p_live_follow_room_cnt_3d,json=uPLiveFollowRoomCnt3d,proto3" json:"u_p_live_follow_room_cnt_3d,omitempty"`
	UPLiveFansRoomCnt_3D    uint32             `protobuf:"varint,6,opt,name=u_p_live_fans_room_cnt_3d,json=uPLiveFansRoomCnt3d,proto3" json:"u_p_live_fans_room_cnt_3d,omitempty"`
	UPLiveGiftRoomCnt_3D    uint32             `protobuf:"varint,7,opt,name=u_p_live_gift_room_cnt_3d,json=uPLiveGiftRoomCnt3d,proto3" json:"u_p_live_gift_room_cnt_3d,omitempty"`
	UPLiveGiftCnt_3D        uint32             `protobuf:"varint,8,opt,name=u_p_live_gift_cnt_3d,json=uPLiveGiftCnt3d,proto3" json:"u_p_live_gift_cnt_3d,omitempty"`
	UPLiveGiftAmt_3D        float64            `protobuf:"fixed64,9,opt,name=u_p_live_gift_amt_3d,json=uPLiveGiftAmt3d,proto3" json:"u_p_live_gift_amt_3d,omitempty"`
	UPLiveGiftMaxAmt_3D     float64            `protobuf:"fixed64,10,opt,name=u_p_live_gift_max_amt_3d,json=uPLiveGiftMaxAmt3d,proto3" json:"u_p_live_gift_max_amt_3d,omitempty"`
	UPLiveLoginCnt_7D       uint32             `protobuf:"varint,11,opt,name=u_p_live_login_cnt_7d,json=uPLiveLoginCnt7d,proto3" json:"u_p_live_login_cnt_7d,omitempty"`
	UPLiveEnterCnt_7D       uint32             `protobuf:"varint,12,opt,name=u_p_live_enter_cnt_7d,json=uPLiveEnterCnt7d,proto3" json:"u_p_live_enter_cnt_7d,omitempty"`
	UPLiveEnterRoomCnt_7D   uint32             `protobuf:"varint,13,opt,name=u_p_live_enter_room_cnt_7d,json=uPLiveEnterRoomCnt7d,proto3" json:"u_p_live_enter_room_cnt_7d,omitempty"`
	UPLiveTotalDuration_7D  uint32             `protobuf:"varint,14,opt,name=u_p_live_total_duration_7d,json=uPLiveTotalDuration7d,proto3" json:"u_p_live_total_duration_7d,omitempty"`
	UPLiveFollowRoomCnt_7D  uint32             `protobuf:"varint,15,opt,name=u_p_live_follow_room_cnt_7d,json=uPLiveFollowRoomCnt7d,proto3" json:"u_p_live_follow_room_cnt_7d,omitempty"`
	UPLiveFansRoomCnt_7D    uint32             `protobuf:"varint,16,opt,name=u_p_live_fans_room_cnt_7d,json=uPLiveFansRoomCnt7d,proto3" json:"u_p_live_fans_room_cnt_7d,omitempty"`
	UPLiveGiftRoomCnt_7D    uint32             `protobuf:"varint,17,opt,name=u_p_live_gift_room_cnt_7d,json=uPLiveGiftRoomCnt7d,proto3" json:"u_p_live_gift_room_cnt_7d,omitempty"`
	UPLiveGiftCnt_7D        uint32             `protobuf:"varint,18,opt,name=u_p_live_gift_cnt_7d,json=uPLiveGiftCnt7d,proto3" json:"u_p_live_gift_cnt_7d,omitempty"`
	UPLiveGiftAmt_7D        float64            `protobuf:"fixed64,19,opt,name=u_p_live_gift_amt_7d,json=uPLiveGiftAmt7d,proto3" json:"u_p_live_gift_amt_7d,omitempty"`
	UPLiveGiftMaxAmt_7D     float64            `protobuf:"fixed64,20,opt,name=u_p_live_gift_max_amt_7d,json=uPLiveGiftMaxAmt7d,proto3" json:"u_p_live_gift_max_amt_7d,omitempty"`
	UPLiveLoginCnt_14D      uint32             `protobuf:"varint,21,opt,name=u_p_live_login_cnt_14d,json=uPLiveLoginCnt14d,proto3" json:"u_p_live_login_cnt_14d,omitempty"`
	UPLiveEnterCnt_14D      uint32             `protobuf:"varint,22,opt,name=u_p_live_enter_cnt_14d,json=uPLiveEnterCnt14d,proto3" json:"u_p_live_enter_cnt_14d,omitempty"`
	UPLiveEnterRoomCnt_14D  uint32             `protobuf:"varint,23,opt,name=u_p_live_enter_room_cnt_14d,json=uPLiveEnterRoomCnt14d,proto3" json:"u_p_live_enter_room_cnt_14d,omitempty"`
	UPLiveTotalDuration_14D uint32             `protobuf:"varint,24,opt,name=u_p_live_total_duration_14d,json=uPLiveTotalDuration14d,proto3" json:"u_p_live_total_duration_14d,omitempty"`
	UPLiveFollowRoomCnt_14D uint32             `protobuf:"varint,25,opt,name=u_p_live_follow_room_cnt_14d,json=uPLiveFollowRoomCnt14d,proto3" json:"u_p_live_follow_room_cnt_14d,omitempty"`
	UPLiveFansRoomCnt_14D   uint32             `protobuf:"varint,26,opt,name=u_p_live_fans_room_cnt_14d,json=uPLiveFansRoomCnt14d,proto3" json:"u_p_live_fans_room_cnt_14d,omitempty"`
	UPLiveGiftRoomCnt_14D   uint32             `protobuf:"varint,27,opt,name=u_p_live_gift_room_cnt_14d,json=uPLiveGiftRoomCnt14d,proto3" json:"u_p_live_gift_room_cnt_14d,omitempty"`
	UPLiveGiftCnt_14D       uint32             `protobuf:"varint,28,opt,name=u_p_live_gift_cnt_14d,json=uPLiveGiftCnt14d,proto3" json:"u_p_live_gift_cnt_14d,omitempty"`
	UPLiveGiftAmt_14D       float64            `protobuf:"fixed64,29,opt,name=u_p_live_gift_amt_14d,json=uPLiveGiftAmt14d,proto3" json:"u_p_live_gift_amt_14d,omitempty"`
	UPLiveGiftMaxAmt_14D    float64            `protobuf:"fixed64,30,opt,name=u_p_live_gift_max_amt_14d,json=uPLiveGiftMaxAmt14d,proto3" json:"u_p_live_gift_max_amt_14d,omitempty"`
	UPLiveTagIdPref_14D     map[uint32]float64 `protobuf:"bytes,31,rep,name=u_p_live_tag_id_pref_14d,json=uPLiveTagIdPref14d,proto3" json:"u_p_live_tag_id_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPLiveGenderPref_14D    map[uint32]float64 `protobuf:"bytes,32,rep,name=u_p_live_gender_pref_14d,json=uPLiveGenderPref14d,proto3" json:"u_p_live_gender_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UPLiveAgeGroupPref_14D  map[uint32]float64 `protobuf:"bytes,33,rep,name=u_p_live_age_group_pref_14d,json=uPLiveAgeGroupPref14d,proto3" json:"u_p_live_age_group_pref_14d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral    struct{}           `json:"-"`
	XXX_unrecognized        []byte             `json:"-"`
	XXX_sizecache           int32              `json:"-"`
}

func (m *ChannelLiveUserOffline) Reset()         { *m = ChannelLiveUserOffline{} }
func (m *ChannelLiveUserOffline) String() string { return proto.CompactTextString(m) }
func (*ChannelLiveUserOffline) ProtoMessage()    {}
func (*ChannelLiveUserOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{23}
}
func (m *ChannelLiveUserOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelLiveUserOffline.Unmarshal(m, b)
}
func (m *ChannelLiveUserOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelLiveUserOffline.Marshal(b, m, deterministic)
}
func (dst *ChannelLiveUserOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelLiveUserOffline.Merge(dst, src)
}
func (m *ChannelLiveUserOffline) XXX_Size() int {
	return xxx_messageInfo_ChannelLiveUserOffline.Size(m)
}
func (m *ChannelLiveUserOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelLiveUserOffline.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelLiveUserOffline proto.InternalMessageInfo

func (m *ChannelLiveUserOffline) GetUPLiveLoginCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveLoginCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveEnterCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveEnterCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveEnterRoomCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveEnterRoomCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveTotalDuration_3D() uint32 {
	if m != nil {
		return m.UPLiveTotalDuration_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveFollowRoomCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveFollowRoomCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveFansRoomCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveFansRoomCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftRoomCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveGiftRoomCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftCnt_3D() uint32 {
	if m != nil {
		return m.UPLiveGiftCnt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftAmt_3D() float64 {
	if m != nil {
		return m.UPLiveGiftAmt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftMaxAmt_3D() float64 {
	if m != nil {
		return m.UPLiveGiftMaxAmt_3D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveLoginCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveLoginCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveEnterCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveEnterCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveEnterRoomCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveEnterRoomCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveTotalDuration_7D() uint32 {
	if m != nil {
		return m.UPLiveTotalDuration_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveFollowRoomCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveFollowRoomCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveFansRoomCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveFansRoomCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftRoomCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveGiftRoomCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftCnt_7D() uint32 {
	if m != nil {
		return m.UPLiveGiftCnt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftAmt_7D() float64 {
	if m != nil {
		return m.UPLiveGiftAmt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftMaxAmt_7D() float64 {
	if m != nil {
		return m.UPLiveGiftMaxAmt_7D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveLoginCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveLoginCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveEnterCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveEnterCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveEnterRoomCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveEnterRoomCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveTotalDuration_14D() uint32 {
	if m != nil {
		return m.UPLiveTotalDuration_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveFollowRoomCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveFollowRoomCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveFansRoomCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveFansRoomCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftRoomCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveGiftRoomCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftCnt_14D() uint32 {
	if m != nil {
		return m.UPLiveGiftCnt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftAmt_14D() float64 {
	if m != nil {
		return m.UPLiveGiftAmt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveGiftMaxAmt_14D() float64 {
	if m != nil {
		return m.UPLiveGiftMaxAmt_14D
	}
	return 0
}

func (m *ChannelLiveUserOffline) GetUPLiveTagIdPref_14D() map[uint32]float64 {
	if m != nil {
		return m.UPLiveTagIdPref_14D
	}
	return nil
}

func (m *ChannelLiveUserOffline) GetUPLiveGenderPref_14D() map[uint32]float64 {
	if m != nil {
		return m.UPLiveGenderPref_14D
	}
	return nil
}

func (m *ChannelLiveUserOffline) GetUPLiveAgeGroupPref_14D() map[uint32]float64 {
	if m != nil {
		return m.UPLiveAgeGroupPref_14D
	}
	return nil
}

type UserSquareOffline struct {
	UClickRecPosterRatio_7D  float64  `protobuf:"fixed64,1,opt,name=u_click_rec_poster_ratio_7d,json=uClickRecPosterRatio7d,proto3" json:"u_click_rec_poster_ratio_7d,omitempty"`
	UClickPosterRatio_7D     float64  `protobuf:"fixed64,2,opt,name=u_click_poster_ratio_7d,json=uClickPosterRatio7d,proto3" json:"u_click_poster_ratio_7d,omitempty"`
	UClickRecPosterRatio_3D  float64  `protobuf:"fixed64,3,opt,name=u_click_rec_poster_ratio_3d,json=uClickRecPosterRatio3d,proto3" json:"u_click_rec_poster_ratio_3d,omitempty"`
	UClickPosterRatio_3D     float64  `protobuf:"fixed64,4,opt,name=u_click_poster_ratio_3d,json=uClickPosterRatio3d,proto3" json:"u_click_poster_ratio_3d,omitempty"`
	UUserLastLoginCityLevel  uint32   `protobuf:"varint,5,opt,name=u_user_last_login_city_level,json=uUserLastLoginCityLevel,proto3" json:"u_user_last_login_city_level,omitempty"`
	UViewPostCnt_7D          uint32   `protobuf:"varint,6,opt,name=u_view_post_cnt_7d,json=uViewPostCnt7d,proto3" json:"u_view_post_cnt_7d,omitempty"`
	UViewRecPostCnt_7D       uint32   `protobuf:"varint,7,opt,name=u_view_rec_post_cnt_7d,json=uViewRecPostCnt7d,proto3" json:"u_view_rec_post_cnt_7d,omitempty"`
	UViewPostCnt_3D          uint32   `protobuf:"varint,8,opt,name=u_view_post_cnt_3d,json=uViewPostCnt3d,proto3" json:"u_view_post_cnt_3d,omitempty"`
	UViewRecPostCnt_3D       uint32   `protobuf:"varint,9,opt,name=u_view_rec_post_cnt_3d,json=uViewRecPostCnt3d,proto3" json:"u_view_rec_post_cnt_3d,omitempty"`
	ULogDaysCnt_7D           uint32   `protobuf:"varint,10,opt,name=u_log_days_cnt_7d,json=uLogDaysCnt7d,proto3" json:"u_log_days_cnt_7d,omitempty"`
	ULogDaysCnt_3D           uint32   `protobuf:"varint,11,opt,name=u_log_days_cnt_3d,json=uLogDaysCnt3d,proto3" json:"u_log_days_cnt_3d,omitempty"`
	URelatedUcnt             uint32   `protobuf:"varint,12,opt,name=u_related_ucnt,json=uRelatedUcnt,proto3" json:"u_related_ucnt,omitempty"`
	UPostTypeClickRatio      string   `protobuf:"bytes,13,opt,name=u_post_type_click_ratio,json=uPostTypeClickRatio,proto3" json:"u_post_type_click_ratio,omitempty"`
	UActiveHour              int32    `protobuf:"varint,14,opt,name=u_active_hour,json=uActiveHour,proto3" json:"u_active_hour,omitempty"`
	UViewPostCnt_14D         uint32   `protobuf:"varint,15,opt,name=u_view_post_cnt_14d,json=uViewPostCnt14d,proto3" json:"u_view_post_cnt_14d,omitempty"`
	UViewRecPostCnt_14D      uint32   `protobuf:"varint,16,opt,name=u_view_rec_post_cnt_14d,json=uViewRecPostCnt14d,proto3" json:"u_view_rec_post_cnt_14d,omitempty"`
	UClickPosterRatio_14D    float64  `protobuf:"fixed64,17,opt,name=u_click_poster_ratio_14d,json=uClickPosterRatio14d,proto3" json:"u_click_poster_ratio_14d,omitempty"`
	UClickRecPosterRatio_14D float64  `protobuf:"fixed64,18,opt,name=u_click_rec_poster_ratio_14d,json=uClickRecPosterRatio14d,proto3" json:"u_click_rec_poster_ratio_14d,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *UserSquareOffline) Reset()         { *m = UserSquareOffline{} }
func (m *UserSquareOffline) String() string { return proto.CompactTextString(m) }
func (*UserSquareOffline) ProtoMessage()    {}
func (*UserSquareOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{24}
}
func (m *UserSquareOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSquareOffline.Unmarshal(m, b)
}
func (m *UserSquareOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSquareOffline.Marshal(b, m, deterministic)
}
func (dst *UserSquareOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSquareOffline.Merge(dst, src)
}
func (m *UserSquareOffline) XXX_Size() int {
	return xxx_messageInfo_UserSquareOffline.Size(m)
}
func (m *UserSquareOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSquareOffline.DiscardUnknown(m)
}

var xxx_messageInfo_UserSquareOffline proto.InternalMessageInfo

func (m *UserSquareOffline) GetUClickRecPosterRatio_7D() float64 {
	if m != nil {
		return m.UClickRecPosterRatio_7D
	}
	return 0
}

func (m *UserSquareOffline) GetUClickPosterRatio_7D() float64 {
	if m != nil {
		return m.UClickPosterRatio_7D
	}
	return 0
}

func (m *UserSquareOffline) GetUClickRecPosterRatio_3D() float64 {
	if m != nil {
		return m.UClickRecPosterRatio_3D
	}
	return 0
}

func (m *UserSquareOffline) GetUClickPosterRatio_3D() float64 {
	if m != nil {
		return m.UClickPosterRatio_3D
	}
	return 0
}

func (m *UserSquareOffline) GetUUserLastLoginCityLevel() uint32 {
	if m != nil {
		return m.UUserLastLoginCityLevel
	}
	return 0
}

func (m *UserSquareOffline) GetUViewPostCnt_7D() uint32 {
	if m != nil {
		return m.UViewPostCnt_7D
	}
	return 0
}

func (m *UserSquareOffline) GetUViewRecPostCnt_7D() uint32 {
	if m != nil {
		return m.UViewRecPostCnt_7D
	}
	return 0
}

func (m *UserSquareOffline) GetUViewPostCnt_3D() uint32 {
	if m != nil {
		return m.UViewPostCnt_3D
	}
	return 0
}

func (m *UserSquareOffline) GetUViewRecPostCnt_3D() uint32 {
	if m != nil {
		return m.UViewRecPostCnt_3D
	}
	return 0
}

func (m *UserSquareOffline) GetULogDaysCnt_7D() uint32 {
	if m != nil {
		return m.ULogDaysCnt_7D
	}
	return 0
}

func (m *UserSquareOffline) GetULogDaysCnt_3D() uint32 {
	if m != nil {
		return m.ULogDaysCnt_3D
	}
	return 0
}

func (m *UserSquareOffline) GetURelatedUcnt() uint32 {
	if m != nil {
		return m.URelatedUcnt
	}
	return 0
}

func (m *UserSquareOffline) GetUPostTypeClickRatio() string {
	if m != nil {
		return m.UPostTypeClickRatio
	}
	return ""
}

func (m *UserSquareOffline) GetUActiveHour() int32 {
	if m != nil {
		return m.UActiveHour
	}
	return 0
}

func (m *UserSquareOffline) GetUViewPostCnt_14D() uint32 {
	if m != nil {
		return m.UViewPostCnt_14D
	}
	return 0
}

func (m *UserSquareOffline) GetUViewRecPostCnt_14D() uint32 {
	if m != nil {
		return m.UViewRecPostCnt_14D
	}
	return 0
}

func (m *UserSquareOffline) GetUClickPosterRatio_14D() float64 {
	if m != nil {
		return m.UClickPosterRatio_14D
	}
	return 0
}

func (m *UserSquareOffline) GetUClickRecPosterRatio_14D() float64 {
	if m != nil {
		return m.UClickRecPosterRatio_14D
	}
	return 0
}

type PublisherSquareOffline struct {
	PAvgViewClickRatio_28D    float64  `protobuf:"fixed64,1,opt,name=p_avg_view_click_ratio_28d,json=pAvgViewClickRatio28d,proto3" json:"p_avg_view_click_ratio_28d,omitempty"`
	PReplyCommentRatio_28D    float64  `protobuf:"fixed64,2,opt,name=p_reply_comment_ratio_28d,json=pReplyCommentRatio28d,proto3" json:"p_reply_comment_ratio_28d,omitempty"`
	PMaxViewClickRatio_28D    float64  `protobuf:"fixed64,3,opt,name=p_max_view_click_ratio_28d,json=pMaxViewClickRatio28d,proto3" json:"p_max_view_click_ratio_28d,omitempty"`
	PUserLastLoginCityLevel   uint32   `protobuf:"varint,4,opt,name=p_user_last_login_city_level,json=pUserLastLoginCityLevel,proto3" json:"p_user_last_login_city_level,omitempty"`
	PPublishPostCnt_28D       uint32   `protobuf:"varint,5,opt,name=p_publish_post_cnt_28d,json=pPublishPostCnt28d,proto3" json:"p_publish_post_cnt_28d,omitempty"`
	PViewedPostCnt_28D        uint32   `protobuf:"varint,6,opt,name=p_viewed_post_cnt_28d,json=pViewedPostCnt28d,proto3" json:"p_viewed_post_cnt_28d,omitempty"`
	PFanCnt                   uint32   `protobuf:"varint,7,opt,name=p_fan_cnt,json=pFanCnt,proto3" json:"p_fan_cnt,omitempty"`
	PMaleFanCnt               uint32   `protobuf:"varint,8,opt,name=p_male_fan_cnt,json=pMaleFanCnt,proto3" json:"p_male_fan_cnt,omitempty"`
	PPersonalPageViewUcnt_14D uint32   `protobuf:"varint,9,opt,name=p_personal_page_view_ucnt_14d,json=pPersonalPageViewUcnt14d,proto3" json:"p_personal_page_view_ucnt_14d,omitempty"`
	PPersonalPageViewUcnt_3D  uint32   `protobuf:"varint,10,opt,name=p_personal_page_view_ucnt_3d,json=pPersonalPageViewUcnt3d,proto3" json:"p_personal_page_view_ucnt_3d,omitempty"`
	PCommentCntSum_28D        uint32   `protobuf:"varint,11,opt,name=p_comment_cnt_sum_28d,json=pCommentCntSum28d,proto3" json:"p_comment_cnt_sum_28d,omitempty"`
	PAvgViewCommentRatio_28D  float64  `protobuf:"fixed64,12,opt,name=p_avg_view_comment_ratio_28d,json=pAvgViewCommentRatio28d,proto3" json:"p_avg_view_comment_ratio_28d,omitempty"`
	PMaxViewCommentRatio_28D  float64  `protobuf:"fixed64,13,opt,name=p_max_view_comment_ratio_28d,json=pMaxViewCommentRatio28d,proto3" json:"p_max_view_comment_ratio_28d,omitempty"`
	XXX_NoUnkeyedLiteral      struct{} `json:"-"`
	XXX_unrecognized          []byte   `json:"-"`
	XXX_sizecache             int32    `json:"-"`
}

func (m *PublisherSquareOffline) Reset()         { *m = PublisherSquareOffline{} }
func (m *PublisherSquareOffline) String() string { return proto.CompactTextString(m) }
func (*PublisherSquareOffline) ProtoMessage()    {}
func (*PublisherSquareOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{25}
}
func (m *PublisherSquareOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PublisherSquareOffline.Unmarshal(m, b)
}
func (m *PublisherSquareOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PublisherSquareOffline.Marshal(b, m, deterministic)
}
func (dst *PublisherSquareOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PublisherSquareOffline.Merge(dst, src)
}
func (m *PublisherSquareOffline) XXX_Size() int {
	return xxx_messageInfo_PublisherSquareOffline.Size(m)
}
func (m *PublisherSquareOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_PublisherSquareOffline.DiscardUnknown(m)
}

var xxx_messageInfo_PublisherSquareOffline proto.InternalMessageInfo

func (m *PublisherSquareOffline) GetPAvgViewClickRatio_28D() float64 {
	if m != nil {
		return m.PAvgViewClickRatio_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPReplyCommentRatio_28D() float64 {
	if m != nil {
		return m.PReplyCommentRatio_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPMaxViewClickRatio_28D() float64 {
	if m != nil {
		return m.PMaxViewClickRatio_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPUserLastLoginCityLevel() uint32 {
	if m != nil {
		return m.PUserLastLoginCityLevel
	}
	return 0
}

func (m *PublisherSquareOffline) GetPPublishPostCnt_28D() uint32 {
	if m != nil {
		return m.PPublishPostCnt_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPViewedPostCnt_28D() uint32 {
	if m != nil {
		return m.PViewedPostCnt_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPFanCnt() uint32 {
	if m != nil {
		return m.PFanCnt
	}
	return 0
}

func (m *PublisherSquareOffline) GetPMaleFanCnt() uint32 {
	if m != nil {
		return m.PMaleFanCnt
	}
	return 0
}

func (m *PublisherSquareOffline) GetPPersonalPageViewUcnt_14D() uint32 {
	if m != nil {
		return m.PPersonalPageViewUcnt_14D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPPersonalPageViewUcnt_3D() uint32 {
	if m != nil {
		return m.PPersonalPageViewUcnt_3D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPCommentCntSum_28D() uint32 {
	if m != nil {
		return m.PCommentCntSum_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPAvgViewCommentRatio_28D() float64 {
	if m != nil {
		return m.PAvgViewCommentRatio_28D
	}
	return 0
}

func (m *PublisherSquareOffline) GetPMaxViewCommentRatio_28D() float64 {
	if m != nil {
		return m.PMaxViewCommentRatio_28D
	}
	return 0
}

type UserSquarePreferOffline struct {
	UCtrPMale_14D        float64  `protobuf:"fixed64,1,opt,name=u_ctr_p_male_14d,json=uCtrPMale14d,proto3" json:"u_ctr_p_male_14d,omitempty"`
	UCtrPFemale_14D      float64  `protobuf:"fixed64,2,opt,name=u_ctr_p_female_14d,json=uCtrPFemale14d,proto3" json:"u_ctr_p_female_14d,omitempty"`
	UCtrOddPFemale_14D   float64  `protobuf:"fixed64,3,opt,name=u_ctr_odd_p_female_14d,json=uCtrOddPFemale14d,proto3" json:"u_ctr_odd_p_female_14d,omitempty"`
	UCtrPMale_7D         float64  `protobuf:"fixed64,4,opt,name=u_ctr_p_male_7d,json=uCtrPMale7d,proto3" json:"u_ctr_p_male_7d,omitempty"`
	UCtrPFemale_7D       float64  `protobuf:"fixed64,5,opt,name=u_ctr_p_female_7d,json=uCtrPFemale7d,proto3" json:"u_ctr_p_female_7d,omitempty"`
	UCtrOddPFemale_7D    float64  `protobuf:"fixed64,6,opt,name=u_ctr_odd_p_female_7d,json=uCtrOddPFemale7d,proto3" json:"u_ctr_odd_p_female_7d,omitempty"`
	UCtrPMale_3D         float64  `protobuf:"fixed64,7,opt,name=u_ctr_p_male_3d,json=uCtrPMale3d,proto3" json:"u_ctr_p_male_3d,omitempty"`
	UCtrPFemale_3D       float64  `protobuf:"fixed64,8,opt,name=u_ctr_p_female_3d,json=uCtrPFemale3d,proto3" json:"u_ctr_p_female_3d,omitempty"`
	UCtrOddPFemale_3D    float64  `protobuf:"fixed64,9,opt,name=u_ctr_odd_p_female_3d,json=uCtrOddPFemale3d,proto3" json:"u_ctr_odd_p_female_3d,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSquarePreferOffline) Reset()         { *m = UserSquarePreferOffline{} }
func (m *UserSquarePreferOffline) String() string { return proto.CompactTextString(m) }
func (*UserSquarePreferOffline) ProtoMessage()    {}
func (*UserSquarePreferOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{26}
}
func (m *UserSquarePreferOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSquarePreferOffline.Unmarshal(m, b)
}
func (m *UserSquarePreferOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSquarePreferOffline.Marshal(b, m, deterministic)
}
func (dst *UserSquarePreferOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSquarePreferOffline.Merge(dst, src)
}
func (m *UserSquarePreferOffline) XXX_Size() int {
	return xxx_messageInfo_UserSquarePreferOffline.Size(m)
}
func (m *UserSquarePreferOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSquarePreferOffline.DiscardUnknown(m)
}

var xxx_messageInfo_UserSquarePreferOffline proto.InternalMessageInfo

func (m *UserSquarePreferOffline) GetUCtrPMale_14D() float64 {
	if m != nil {
		return m.UCtrPMale_14D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrPFemale_14D() float64 {
	if m != nil {
		return m.UCtrPFemale_14D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrOddPFemale_14D() float64 {
	if m != nil {
		return m.UCtrOddPFemale_14D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrPMale_7D() float64 {
	if m != nil {
		return m.UCtrPMale_7D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrPFemale_7D() float64 {
	if m != nil {
		return m.UCtrPFemale_7D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrOddPFemale_7D() float64 {
	if m != nil {
		return m.UCtrOddPFemale_7D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrPMale_3D() float64 {
	if m != nil {
		return m.UCtrPMale_3D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrPFemale_3D() float64 {
	if m != nil {
		return m.UCtrPFemale_3D
	}
	return 0
}

func (m *UserSquarePreferOffline) GetUCtrOddPFemale_3D() float64 {
	if m != nil {
		return m.UCtrOddPFemale_3D
	}
	return 0
}

type UserSquareSequenceOffline struct {
	UThumpUpImageHwTagSeq string   `protobuf:"bytes,1,opt,name=u_thump_up_image_hw_tag_seq,json=uThumpUpImageHwTagSeq,proto3" json:"u_thump_up_image_hw_tag_seq,omitempty"`
	UCommentImageHwTagSeq string   `protobuf:"bytes,2,opt,name=u_comment_image_hw_tag_seq,json=uCommentImageHwTagSeq,proto3" json:"u_comment_image_hw_tag_seq,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserSquareSequenceOffline) Reset()         { *m = UserSquareSequenceOffline{} }
func (m *UserSquareSequenceOffline) String() string { return proto.CompactTextString(m) }
func (*UserSquareSequenceOffline) ProtoMessage()    {}
func (*UserSquareSequenceOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{27}
}
func (m *UserSquareSequenceOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSquareSequenceOffline.Unmarshal(m, b)
}
func (m *UserSquareSequenceOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSquareSequenceOffline.Marshal(b, m, deterministic)
}
func (dst *UserSquareSequenceOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSquareSequenceOffline.Merge(dst, src)
}
func (m *UserSquareSequenceOffline) XXX_Size() int {
	return xxx_messageInfo_UserSquareSequenceOffline.Size(m)
}
func (m *UserSquareSequenceOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSquareSequenceOffline.DiscardUnknown(m)
}

var xxx_messageInfo_UserSquareSequenceOffline proto.InternalMessageInfo

func (m *UserSquareSequenceOffline) GetUThumpUpImageHwTagSeq() string {
	if m != nil {
		return m.UThumpUpImageHwTagSeq
	}
	return ""
}

func (m *UserSquareSequenceOffline) GetUCommentImageHwTagSeq() string {
	if m != nil {
		return m.UCommentImageHwTagSeq
	}
	return ""
}

type UserSquarePublisherOffline struct {
	UPViewPCnt_1D        map[uint32]uint32 `protobuf:"bytes,1,rep,name=u_p_view_p_cnt_1d,json=uPViewPCnt1d,proto3" json:"u_p_view_p_cnt_1d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPClickPCnt_1D       map[uint32]uint32 `protobuf:"bytes,2,rep,name=u_p_click_p_cnt_1d,json=uPClickPCnt1d,proto3" json:"u_p_click_p_cnt_1d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPThumpUpPCnt_1D     map[uint32]uint32 `protobuf:"bytes,3,rep,name=u_p_thump_up_p_cnt_1d,json=uPThumpUpPCnt1d,proto3" json:"u_p_thump_up_p_cnt_1d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPViewTime_1D        map[uint32]uint32 `protobuf:"bytes,4,rep,name=u_p_view_time_1d,json=uPViewTime1d,proto3" json:"u_p_view_time_1d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPViewPCnt_3D        map[uint32]uint32 `protobuf:"bytes,5,rep,name=u_p_view_p_cnt_3d,json=uPViewPCnt3d,proto3" json:"u_p_view_p_cnt_3d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPClickPCnt_3D       map[uint32]uint32 `protobuf:"bytes,6,rep,name=u_p_click_p_cnt_3d,json=uPClickPCnt3d,proto3" json:"u_p_click_p_cnt_3d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPThumpUpPCnt_3D     map[uint32]uint32 `protobuf:"bytes,7,rep,name=u_p_thump_up_p_cnt_3d,json=uPThumpUpPCnt3d,proto3" json:"u_p_thump_up_p_cnt_3d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	UPViewTime_3D        map[uint32]uint32 `protobuf:"bytes,8,rep,name=u_p_view_time_3d,json=uPViewTime3d,proto3" json:"u_p_view_time_3d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserSquarePublisherOffline) Reset()         { *m = UserSquarePublisherOffline{} }
func (m *UserSquarePublisherOffline) String() string { return proto.CompactTextString(m) }
func (*UserSquarePublisherOffline) ProtoMessage()    {}
func (*UserSquarePublisherOffline) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{28}
}
func (m *UserSquarePublisherOffline) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSquarePublisherOffline.Unmarshal(m, b)
}
func (m *UserSquarePublisherOffline) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSquarePublisherOffline.Marshal(b, m, deterministic)
}
func (dst *UserSquarePublisherOffline) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSquarePublisherOffline.Merge(dst, src)
}
func (m *UserSquarePublisherOffline) XXX_Size() int {
	return xxx_messageInfo_UserSquarePublisherOffline.Size(m)
}
func (m *UserSquarePublisherOffline) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSquarePublisherOffline.DiscardUnknown(m)
}

var xxx_messageInfo_UserSquarePublisherOffline proto.InternalMessageInfo

func (m *UserSquarePublisherOffline) GetUPViewPCnt_1D() map[uint32]uint32 {
	if m != nil {
		return m.UPViewPCnt_1D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPClickPCnt_1D() map[uint32]uint32 {
	if m != nil {
		return m.UPClickPCnt_1D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPThumpUpPCnt_1D() map[uint32]uint32 {
	if m != nil {
		return m.UPThumpUpPCnt_1D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPViewTime_1D() map[uint32]uint32 {
	if m != nil {
		return m.UPViewTime_1D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPViewPCnt_3D() map[uint32]uint32 {
	if m != nil {
		return m.UPViewPCnt_3D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPClickPCnt_3D() map[uint32]uint32 {
	if m != nil {
		return m.UPClickPCnt_3D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPThumpUpPCnt_3D() map[uint32]uint32 {
	if m != nil {
		return m.UPThumpUpPCnt_3D
	}
	return nil
}

func (m *UserSquarePublisherOffline) GetUPViewTime_3D() map[uint32]uint32 {
	if m != nil {
		return m.UPViewTime_3D
	}
	return nil
}

type UserChannleAccumAction struct {
	GameBrowseCountAccum map[string]uint32 `protobuf:"bytes,1,rep,name=game_browse_count_accum,json=gameBrowseCountAccum,proto3" json:"game_browse_count_accum,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserChannleAccumAction) Reset()         { *m = UserChannleAccumAction{} }
func (m *UserChannleAccumAction) String() string { return proto.CompactTextString(m) }
func (*UserChannleAccumAction) ProtoMessage()    {}
func (*UserChannleAccumAction) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{29}
}
func (m *UserChannleAccumAction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleAccumAction.Unmarshal(m, b)
}
func (m *UserChannleAccumAction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleAccumAction.Marshal(b, m, deterministic)
}
func (dst *UserChannleAccumAction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleAccumAction.Merge(dst, src)
}
func (m *UserChannleAccumAction) XXX_Size() int {
	return xxx_messageInfo_UserChannleAccumAction.Size(m)
}
func (m *UserChannleAccumAction) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleAccumAction.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleAccumAction proto.InternalMessageInfo

func (m *UserChannleAccumAction) GetGameBrowseCountAccum() map[string]uint32 {
	if m != nil {
		return m.GameBrowseCountAccum
	}
	return nil
}

type UserChannleAccumAction_Append struct {
	GameBrowseCountAccum map[string]uint32 `protobuf:"bytes,1,rep,name=game_browse_count_accum,json=gameBrowseCountAccum,proto3" json:"game_browse_count_accum,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserChannleAccumAction_Append) Reset()         { *m = UserChannleAccumAction_Append{} }
func (m *UserChannleAccumAction_Append) String() string { return proto.CompactTextString(m) }
func (*UserChannleAccumAction_Append) ProtoMessage()    {}
func (*UserChannleAccumAction_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{29, 1}
}
func (m *UserChannleAccumAction_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleAccumAction_Append.Unmarshal(m, b)
}
func (m *UserChannleAccumAction_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleAccumAction_Append.Marshal(b, m, deterministic)
}
func (dst *UserChannleAccumAction_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleAccumAction_Append.Merge(dst, src)
}
func (m *UserChannleAccumAction_Append) XXX_Size() int {
	return xxx_messageInfo_UserChannleAccumAction_Append.Size(m)
}
func (m *UserChannleAccumAction_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleAccumAction_Append.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleAccumAction_Append proto.InternalMessageInfo

func (m *UserChannleAccumAction_Append) GetGameBrowseCountAccum() map[string]uint32 {
	if m != nil {
		return m.GameBrowseCountAccum
	}
	return nil
}

type UserChannleEnterAction struct {
	// 用户近6次进房房间类型及停留时长
	UserEnter []string `protobuf:"bytes,1,rep,name=UserEnter,proto3" json:"UserEnter,omitempty"`
	// 用户近6次开黑房间类型
	UserGangup []string `protobuf:"bytes,2,rep,name=UserGangup,proto3" json:"UserGangup,omitempty"`
	// 用户近6次进房标题序列[{'tag_id':17,'room_name':'name'},{}...]
	UserEnterRoomName    []*UserChannleEnterAction_UserEnterInfo `protobuf:"bytes,3,rep,name=UserEnterRoomName,proto3" json:"UserEnterRoomName,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *UserChannleEnterAction) Reset()         { *m = UserChannleEnterAction{} }
func (m *UserChannleEnterAction) String() string { return proto.CompactTextString(m) }
func (*UserChannleEnterAction) ProtoMessage()    {}
func (*UserChannleEnterAction) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{30}
}
func (m *UserChannleEnterAction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterAction.Unmarshal(m, b)
}
func (m *UserChannleEnterAction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterAction.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterAction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterAction.Merge(dst, src)
}
func (m *UserChannleEnterAction) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterAction.Size(m)
}
func (m *UserChannleEnterAction) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterAction.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterAction proto.InternalMessageInfo

func (m *UserChannleEnterAction) GetUserEnter() []string {
	if m != nil {
		return m.UserEnter
	}
	return nil
}

func (m *UserChannleEnterAction) GetUserGangup() []string {
	if m != nil {
		return m.UserGangup
	}
	return nil
}

func (m *UserChannleEnterAction) GetUserEnterRoomName() []*UserChannleEnterAction_UserEnterInfo {
	if m != nil {
		return m.UserEnterRoomName
	}
	return nil
}

type UserChannleEnterAction_UserEnterInfo struct {
	TagId                string   `protobuf:"bytes,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	RoomName             string   `protobuf:"bytes,2,opt,name=room_name,json=roomName,proto3" json:"room_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannleEnterAction_UserEnterInfo) Reset()         { *m = UserChannleEnterAction_UserEnterInfo{} }
func (m *UserChannleEnterAction_UserEnterInfo) String() string { return proto.CompactTextString(m) }
func (*UserChannleEnterAction_UserEnterInfo) ProtoMessage()    {}
func (*UserChannleEnterAction_UserEnterInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{30, 0}
}
func (m *UserChannleEnterAction_UserEnterInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterAction_UserEnterInfo.Unmarshal(m, b)
}
func (m *UserChannleEnterAction_UserEnterInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterAction_UserEnterInfo.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterAction_UserEnterInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterAction_UserEnterInfo.Merge(dst, src)
}
func (m *UserChannleEnterAction_UserEnterInfo) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterAction_UserEnterInfo.Size(m)
}
func (m *UserChannleEnterAction_UserEnterInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterAction_UserEnterInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterAction_UserEnterInfo proto.InternalMessageInfo

func (m *UserChannleEnterAction_UserEnterInfo) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

func (m *UserChannleEnterAction_UserEnterInfo) GetRoomName() string {
	if m != nil {
		return m.RoomName
	}
	return ""
}

type UserChannleEnterAction_AppendUserEnter struct {
	UserEnter            []string `protobuf:"bytes,1,rep,name=UserEnter,proto3" json:"UserEnter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannleEnterAction_AppendUserEnter) Reset() {
	*m = UserChannleEnterAction_AppendUserEnter{}
}
func (m *UserChannleEnterAction_AppendUserEnter) String() string { return proto.CompactTextString(m) }
func (*UserChannleEnterAction_AppendUserEnter) ProtoMessage()    {}
func (*UserChannleEnterAction_AppendUserEnter) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{30, 1}
}
func (m *UserChannleEnterAction_AppendUserEnter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserEnter.Unmarshal(m, b)
}
func (m *UserChannleEnterAction_AppendUserEnter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserEnter.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterAction_AppendUserEnter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterAction_AppendUserEnter.Merge(dst, src)
}
func (m *UserChannleEnterAction_AppendUserEnter) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserEnter.Size(m)
}
func (m *UserChannleEnterAction_AppendUserEnter) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterAction_AppendUserEnter.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterAction_AppendUserEnter proto.InternalMessageInfo

func (m *UserChannleEnterAction_AppendUserEnter) GetUserEnter() []string {
	if m != nil {
		return m.UserEnter
	}
	return nil
}

type UserChannleEnterAction_AppendUserGangup struct {
	UserGangup           []string `protobuf:"bytes,1,rep,name=UserGangup,proto3" json:"UserGangup,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannleEnterAction_AppendUserGangup) Reset() {
	*m = UserChannleEnterAction_AppendUserGangup{}
}
func (m *UserChannleEnterAction_AppendUserGangup) String() string { return proto.CompactTextString(m) }
func (*UserChannleEnterAction_AppendUserGangup) ProtoMessage()    {}
func (*UserChannleEnterAction_AppendUserGangup) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{30, 2}
}
func (m *UserChannleEnterAction_AppendUserGangup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserGangup.Unmarshal(m, b)
}
func (m *UserChannleEnterAction_AppendUserGangup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserGangup.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterAction_AppendUserGangup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterAction_AppendUserGangup.Merge(dst, src)
}
func (m *UserChannleEnterAction_AppendUserGangup) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserGangup.Size(m)
}
func (m *UserChannleEnterAction_AppendUserGangup) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterAction_AppendUserGangup.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterAction_AppendUserGangup proto.InternalMessageInfo

func (m *UserChannleEnterAction_AppendUserGangup) GetUserGangup() []string {
	if m != nil {
		return m.UserGangup
	}
	return nil
}

type UserChannleEnterAction_AppendUserEnterName struct {
	UserEnterRoomName    []*UserChannleEnterAction_UserEnterInfo `protobuf:"bytes,1,rep,name=UserEnterRoomName,proto3" json:"UserEnterRoomName,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *UserChannleEnterAction_AppendUserEnterName) Reset() {
	*m = UserChannleEnterAction_AppendUserEnterName{}
}
func (m *UserChannleEnterAction_AppendUserEnterName) String() string {
	return proto.CompactTextString(m)
}
func (*UserChannleEnterAction_AppendUserEnterName) ProtoMessage() {}
func (*UserChannleEnterAction_AppendUserEnterName) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{30, 3}
}
func (m *UserChannleEnterAction_AppendUserEnterName) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserEnterName.Unmarshal(m, b)
}
func (m *UserChannleEnterAction_AppendUserEnterName) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserEnterName.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterAction_AppendUserEnterName) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterAction_AppendUserEnterName.Merge(dst, src)
}
func (m *UserChannleEnterAction_AppendUserEnterName) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterAction_AppendUserEnterName.Size(m)
}
func (m *UserChannleEnterAction_AppendUserEnterName) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterAction_AppendUserEnterName.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterAction_AppendUserEnterName proto.InternalMessageInfo

func (m *UserChannleEnterAction_AppendUserEnterName) GetUserEnterRoomName() []*UserChannleEnterAction_UserEnterInfo {
	if m != nil {
		return m.UserEnterRoomName
	}
	return nil
}

type UserChannleEnterActionForGamecard struct {
	// 用户近3次进房房间类型及停留时长
	UserEnter            []string `protobuf:"bytes,1,rep,name=UserEnter,proto3" json:"UserEnter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannleEnterActionForGamecard) Reset()         { *m = UserChannleEnterActionForGamecard{} }
func (m *UserChannleEnterActionForGamecard) String() string { return proto.CompactTextString(m) }
func (*UserChannleEnterActionForGamecard) ProtoMessage()    {}
func (*UserChannleEnterActionForGamecard) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{31}
}
func (m *UserChannleEnterActionForGamecard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterActionForGamecard.Unmarshal(m, b)
}
func (m *UserChannleEnterActionForGamecard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterActionForGamecard.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterActionForGamecard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterActionForGamecard.Merge(dst, src)
}
func (m *UserChannleEnterActionForGamecard) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterActionForGamecard.Size(m)
}
func (m *UserChannleEnterActionForGamecard) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterActionForGamecard.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterActionForGamecard proto.InternalMessageInfo

func (m *UserChannleEnterActionForGamecard) GetUserEnter() []string {
	if m != nil {
		return m.UserEnter
	}
	return nil
}

type UserChannleEnterActionForGamecard_Append struct {
	UserEnter            []string `protobuf:"bytes,1,rep,name=UserEnter,proto3" json:"UserEnter,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserChannleEnterActionForGamecard_Append) Reset() {
	*m = UserChannleEnterActionForGamecard_Append{}
}
func (m *UserChannleEnterActionForGamecard_Append) String() string { return proto.CompactTextString(m) }
func (*UserChannleEnterActionForGamecard_Append) ProtoMessage()    {}
func (*UserChannleEnterActionForGamecard_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{31, 0}
}
func (m *UserChannleEnterActionForGamecard_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChannleEnterActionForGamecard_Append.Unmarshal(m, b)
}
func (m *UserChannleEnterActionForGamecard_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChannleEnterActionForGamecard_Append.Marshal(b, m, deterministic)
}
func (dst *UserChannleEnterActionForGamecard_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChannleEnterActionForGamecard_Append.Merge(dst, src)
}
func (m *UserChannleEnterActionForGamecard_Append) XXX_Size() int {
	return xxx_messageInfo_UserChannleEnterActionForGamecard_Append.Size(m)
}
func (m *UserChannleEnterActionForGamecard_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChannleEnterActionForGamecard_Append.DiscardUnknown(m)
}

var xxx_messageInfo_UserChannleEnterActionForGamecard_Append proto.InternalMessageInfo

func (m *UserChannleEnterActionForGamecard_Append) GetUserEnter() []string {
	if m != nil {
		return m.UserEnter
	}
	return nil
}

// 用户分数
// Deprecated
type UserSingScore struct {
	SongGodScore         uint32   `protobuf:"varint,1,opt,name=song_god_score,json=songGodScore,proto3" json:"song_god_score,omitempty"`
	SongGodCount         uint32   `protobuf:"varint,2,opt,name=song_god_count,json=songGodCount,proto3" json:"song_god_count,omitempty"`
	OutToneScore         uint32   `protobuf:"varint,3,opt,name=out_tone_score,json=outToneScore,proto3" json:"out_tone_score,omitempty"`
	OutToneCount         uint32   `protobuf:"varint,4,opt,name=out_tone_count,json=outToneCount,proto3" json:"out_tone_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSingScore) Reset()         { *m = UserSingScore{} }
func (m *UserSingScore) String() string { return proto.CompactTextString(m) }
func (*UserSingScore) ProtoMessage()    {}
func (*UserSingScore) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{32}
}
func (m *UserSingScore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSingScore.Unmarshal(m, b)
}
func (m *UserSingScore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSingScore.Marshal(b, m, deterministic)
}
func (dst *UserSingScore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSingScore.Merge(dst, src)
}
func (m *UserSingScore) XXX_Size() int {
	return xxx_messageInfo_UserSingScore.Size(m)
}
func (m *UserSingScore) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSingScore.DiscardUnknown(m)
}

var xxx_messageInfo_UserSingScore proto.InternalMessageInfo

func (m *UserSingScore) GetSongGodScore() uint32 {
	if m != nil {
		return m.SongGodScore
	}
	return 0
}

func (m *UserSingScore) GetSongGodCount() uint32 {
	if m != nil {
		return m.SongGodCount
	}
	return 0
}

func (m *UserSingScore) GetOutToneScore() uint32 {
	if m != nil {
		return m.OutToneScore
	}
	return 0
}

func (m *UserSingScore) GetOutToneCount() uint32 {
	if m != nil {
		return m.OutToneCount
	}
	return 0
}

type UserSingScore_SongGodAppend struct {
	SongGodScore         uint32   `protobuf:"varint,1,opt,name=song_god_score,json=songGodScore,proto3" json:"song_god_score,omitempty"`
	SongGodCount         uint32   `protobuf:"varint,2,opt,name=song_god_count,json=songGodCount,proto3" json:"song_god_count,omitempty"`
	OutToneScore         uint32   `protobuf:"varint,3,opt,name=out_tone_score,json=outToneScore,proto3" json:"out_tone_score,omitempty"`
	OutToneCount         uint32   `protobuf:"varint,4,opt,name=out_tone_count,json=outToneCount,proto3" json:"out_tone_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserSingScore_SongGodAppend) Reset()         { *m = UserSingScore_SongGodAppend{} }
func (m *UserSingScore_SongGodAppend) String() string { return proto.CompactTextString(m) }
func (*UserSingScore_SongGodAppend) ProtoMessage()    {}
func (*UserSingScore_SongGodAppend) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{32, 0}
}
func (m *UserSingScore_SongGodAppend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSingScore_SongGodAppend.Unmarshal(m, b)
}
func (m *UserSingScore_SongGodAppend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSingScore_SongGodAppend.Marshal(b, m, deterministic)
}
func (dst *UserSingScore_SongGodAppend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSingScore_SongGodAppend.Merge(dst, src)
}
func (m *UserSingScore_SongGodAppend) XXX_Size() int {
	return xxx_messageInfo_UserSingScore_SongGodAppend.Size(m)
}
func (m *UserSingScore_SongGodAppend) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSingScore_SongGodAppend.DiscardUnknown(m)
}

var xxx_messageInfo_UserSingScore_SongGodAppend proto.InternalMessageInfo

func (m *UserSingScore_SongGodAppend) GetSongGodScore() uint32 {
	if m != nil {
		return m.SongGodScore
	}
	return 0
}

func (m *UserSingScore_SongGodAppend) GetSongGodCount() uint32 {
	if m != nil {
		return m.SongGodCount
	}
	return 0
}

func (m *UserSingScore_SongGodAppend) GetOutToneScore() uint32 {
	if m != nil {
		return m.OutToneScore
	}
	return 0
}

func (m *UserSingScore_SongGodAppend) GetOutToneCount() uint32 {
	if m != nil {
		return m.OutToneCount
	}
	return 0
}

// 用户分数V2
type UserSingScoreV2 struct {
	ToneScore            map[string]uint32 `protobuf:"bytes,1,rep,name=tone_score,json=toneScore,proto3" json:"tone_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ToneCount            map[string]uint32 `protobuf:"bytes,2,rep,name=tone_count,json=toneCount,proto3" json:"tone_count,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserSingScoreV2) Reset()         { *m = UserSingScoreV2{} }
func (m *UserSingScoreV2) String() string { return proto.CompactTextString(m) }
func (*UserSingScoreV2) ProtoMessage()    {}
func (*UserSingScoreV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{33}
}
func (m *UserSingScoreV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSingScoreV2.Unmarshal(m, b)
}
func (m *UserSingScoreV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSingScoreV2.Marshal(b, m, deterministic)
}
func (dst *UserSingScoreV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSingScoreV2.Merge(dst, src)
}
func (m *UserSingScoreV2) XXX_Size() int {
	return xxx_messageInfo_UserSingScoreV2.Size(m)
}
func (m *UserSingScoreV2) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSingScoreV2.DiscardUnknown(m)
}

var xxx_messageInfo_UserSingScoreV2 proto.InternalMessageInfo

func (m *UserSingScoreV2) GetToneScore() map[string]uint32 {
	if m != nil {
		return m.ToneScore
	}
	return nil
}

func (m *UserSingScoreV2) GetToneCount() map[string]uint32 {
	if m != nil {
		return m.ToneCount
	}
	return nil
}

type UserSingScoreV2_Append struct {
	ToneScore            map[string]uint32 `protobuf:"bytes,1,rep,name=tone_score,json=toneScore,proto3" json:"tone_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ToneCount            map[string]uint32 `protobuf:"bytes,2,rep,name=tone_count,json=toneCount,proto3" json:"tone_count,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserSingScoreV2_Append) Reset()         { *m = UserSingScoreV2_Append{} }
func (m *UserSingScoreV2_Append) String() string { return proto.CompactTextString(m) }
func (*UserSingScoreV2_Append) ProtoMessage()    {}
func (*UserSingScoreV2_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{33, 2}
}
func (m *UserSingScoreV2_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSingScoreV2_Append.Unmarshal(m, b)
}
func (m *UserSingScoreV2_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSingScoreV2_Append.Marshal(b, m, deterministic)
}
func (dst *UserSingScoreV2_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSingScoreV2_Append.Merge(dst, src)
}
func (m *UserSingScoreV2_Append) XXX_Size() int {
	return xxx_messageInfo_UserSingScoreV2_Append.Size(m)
}
func (m *UserSingScoreV2_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSingScoreV2_Append.DiscardUnknown(m)
}

var xxx_messageInfo_UserSingScoreV2_Append proto.InternalMessageInfo

func (m *UserSingScoreV2_Append) GetToneScore() map[string]uint32 {
	if m != nil {
		return m.ToneScore
	}
	return nil
}

func (m *UserSingScoreV2_Append) GetToneCount() map[string]uint32 {
	if m != nil {
		return m.ToneCount
	}
	return nil
}

// 用户游戏卡 跟TT的KGameCardUpdateEvent事件对应
//
type UserGameCard struct {
	Cards                []*GameCardInfo `protobuf:"bytes,1,rep,name=cards,proto3" json:"cards,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UserGameCard) Reset()         { *m = UserGameCard{} }
func (m *UserGameCard) String() string { return proto.CompactTextString(m) }
func (*UserGameCard) ProtoMessage()    {}
func (*UserGameCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{34}
}
func (m *UserGameCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameCard.Unmarshal(m, b)
}
func (m *UserGameCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameCard.Marshal(b, m, deterministic)
}
func (dst *UserGameCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameCard.Merge(dst, src)
}
func (m *UserGameCard) XXX_Size() int {
	return xxx_messageInfo_UserGameCard.Size(m)
}
func (m *UserGameCard) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameCard.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameCard proto.InternalMessageInfo

func (m *UserGameCard) GetCards() []*GameCardInfo {
	if m != nil {
		return m.Cards
	}
	return nil
}

type GameCardInfo struct {
	GameCardId           uint32         `protobuf:"varint,1,opt,name=game_card_id,json=gameCardId,proto3" json:"game_card_id,omitempty"`
	GameName             string         `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	UGameId              uint32         `protobuf:"varint,3,opt,name=u_game_id,json=uGameId,proto3" json:"u_game_id,omitempty"`
	CardType             uint32         `protobuf:"varint,4,opt,name=card_type,json=cardType,proto3" json:"card_type,omitempty"`
	OptList              []*GameCardOpt `protobuf:"bytes,5,rep,name=opt_list,json=optList,proto3" json:"opt_list,omitempty"`
	NickName             string         `protobuf:"bytes,6,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	ScreenshotList       []string       `protobuf:"bytes,7,rep,name=screenshot_list,json=screenshotList,proto3" json:"screenshot_list,omitempty"`
	ModifyAt             uint32         `protobuf:"varint,8,opt,name=modify_at,json=modifyAt,proto3" json:"modify_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GameCardInfo) Reset()         { *m = GameCardInfo{} }
func (m *GameCardInfo) String() string { return proto.CompactTextString(m) }
func (*GameCardInfo) ProtoMessage()    {}
func (*GameCardInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{35}
}
func (m *GameCardInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardInfo.Unmarshal(m, b)
}
func (m *GameCardInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardInfo.Marshal(b, m, deterministic)
}
func (dst *GameCardInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardInfo.Merge(dst, src)
}
func (m *GameCardInfo) XXX_Size() int {
	return xxx_messageInfo_GameCardInfo.Size(m)
}
func (m *GameCardInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardInfo proto.InternalMessageInfo

func (m *GameCardInfo) GetGameCardId() uint32 {
	if m != nil {
		return m.GameCardId
	}
	return 0
}

func (m *GameCardInfo) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *GameCardInfo) GetUGameId() uint32 {
	if m != nil {
		return m.UGameId
	}
	return 0
}

func (m *GameCardInfo) GetCardType() uint32 {
	if m != nil {
		return m.CardType
	}
	return 0
}

func (m *GameCardInfo) GetOptList() []*GameCardOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *GameCardInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GameCardInfo) GetScreenshotList() []string {
	if m != nil {
		return m.ScreenshotList
	}
	return nil
}

func (m *GameCardInfo) GetModifyAt() uint32 {
	if m != nil {
		return m.ModifyAt
	}
	return 0
}

type GameCardOpt struct {
	OptName              string   `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	ValueList            []string `protobuf:"bytes,2,rep,name=value_list,json=valueList,proto3" json:"value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameCardOpt) Reset()         { *m = GameCardOpt{} }
func (m *GameCardOpt) String() string { return proto.CompactTextString(m) }
func (*GameCardOpt) ProtoMessage()    {}
func (*GameCardOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{36}
}
func (m *GameCardOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCardOpt.Unmarshal(m, b)
}
func (m *GameCardOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCardOpt.Marshal(b, m, deterministic)
}
func (dst *GameCardOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCardOpt.Merge(dst, src)
}
func (m *GameCardOpt) XXX_Size() int {
	return xxx_messageInfo_GameCardOpt.Size(m)
}
func (m *GameCardOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCardOpt.DiscardUnknown(m)
}

var xxx_messageInfo_GameCardOpt proto.InternalMessageInfo

func (m *GameCardOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *GameCardOpt) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

// UserGameTags 用户游戏卡片列表，包含所有的游戏卡片信息，单独profile结构
type UserGameTags struct {
	Tags                 []*GameTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserGameTags) Reset()         { *m = UserGameTags{} }
func (m *UserGameTags) String() string { return proto.CompactTextString(m) }
func (*UserGameTags) ProtoMessage()    {}
func (*UserGameTags) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{37}
}
func (m *UserGameTags) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTags.Unmarshal(m, b)
}
func (m *UserGameTags) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTags.Marshal(b, m, deterministic)
}
func (dst *UserGameTags) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTags.Merge(dst, src)
}
func (m *UserGameTags) XXX_Size() int {
	return xxx_messageInfo_UserGameTags.Size(m)
}
func (m *UserGameTags) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTags.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTags proto.InternalMessageInfo

func (m *UserGameTags) GetTags() []*GameTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

// GameTagUpdate 标签-游戏卡片信息-更新
type UserGameTags_UserGameTagsUpdate struct {
	Tags                 []*GameTag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserGameTags_UserGameTagsUpdate) Reset()         { *m = UserGameTags_UserGameTagsUpdate{} }
func (m *UserGameTags_UserGameTagsUpdate) String() string { return proto.CompactTextString(m) }
func (*UserGameTags_UserGameTagsUpdate) ProtoMessage()    {}
func (*UserGameTags_UserGameTagsUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{37, 0}
}
func (m *UserGameTags_UserGameTagsUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTags_UserGameTagsUpdate.Unmarshal(m, b)
}
func (m *UserGameTags_UserGameTagsUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTags_UserGameTagsUpdate.Marshal(b, m, deterministic)
}
func (dst *UserGameTags_UserGameTagsUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTags_UserGameTagsUpdate.Merge(dst, src)
}
func (m *UserGameTags_UserGameTagsUpdate) XXX_Size() int {
	return xxx_messageInfo_UserGameTags_UserGameTagsUpdate.Size(m)
}
func (m *UserGameTags_UserGameTagsUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTags_UserGameTagsUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTags_UserGameTagsUpdate proto.InternalMessageInfo

func (m *UserGameTags_UserGameTagsUpdate) GetTags() []*GameTag {
	if m != nil {
		return m.Tags
	}
	return nil
}

// GameTag 标签-游戏卡片信息
type GameTag struct {
	Id                   uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type                 uint32          `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	Attrs                []*GameTag_Attr `protobuf:"bytes,4,rep,name=attrs,proto3" json:"attrs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GameTag) Reset()         { *m = GameTag{} }
func (m *GameTag) String() string { return proto.CompactTextString(m) }
func (*GameTag) ProtoMessage()    {}
func (*GameTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{38}
}
func (m *GameTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTag.Unmarshal(m, b)
}
func (m *GameTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTag.Marshal(b, m, deterministic)
}
func (dst *GameTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTag.Merge(dst, src)
}
func (m *GameTag) XXX_Size() int {
	return xxx_messageInfo_GameTag.Size(m)
}
func (m *GameTag) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTag.DiscardUnknown(m)
}

var xxx_messageInfo_GameTag proto.InternalMessageInfo

func (m *GameTag) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GameTag) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameTag) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GameTag) GetAttrs() []*GameTag_Attr {
	if m != nil {
		return m.Attrs
	}
	return nil
}

// 标签-游戏卡片属性
type GameTag_Attr struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Values               []string `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTag_Attr) Reset()         { *m = GameTag_Attr{} }
func (m *GameTag_Attr) String() string { return proto.CompactTextString(m) }
func (*GameTag_Attr) ProtoMessage()    {}
func (*GameTag_Attr) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{38, 0}
}
func (m *GameTag_Attr) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTag_Attr.Unmarshal(m, b)
}
func (m *GameTag_Attr) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTag_Attr.Marshal(b, m, deterministic)
}
func (dst *GameTag_Attr) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTag_Attr.Merge(dst, src)
}
func (m *GameTag_Attr) XXX_Size() int {
	return xxx_messageInfo_GameTag_Attr.Size(m)
}
func (m *GameTag_Attr) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTag_Attr.DiscardUnknown(m)
}

var xxx_messageInfo_GameTag_Attr proto.InternalMessageInfo

func (m *GameTag_Attr) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GameTag_Attr) GetValues() []string {
	if m != nil {
		return m.Values
	}
	return nil
}

// 用户手机本地游戏列表 - 弃用，下个发版删掉
// Deprecated
type MobileGameList struct {
	// 转换后的tabIds
	TabIds               []int32  `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileGameList) Reset()         { *m = MobileGameList{} }
func (m *MobileGameList) String() string { return proto.CompactTextString(m) }
func (*MobileGameList) ProtoMessage()    {}
func (*MobileGameList) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{39}
}
func (m *MobileGameList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileGameList.Unmarshal(m, b)
}
func (m *MobileGameList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileGameList.Marshal(b, m, deterministic)
}
func (dst *MobileGameList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileGameList.Merge(dst, src)
}
func (m *MobileGameList) XXX_Size() int {
	return xxx_messageInfo_MobileGameList.Size(m)
}
func (m *MobileGameList) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileGameList.DiscardUnknown(m)
}

var xxx_messageInfo_MobileGameList proto.InternalMessageInfo

func (m *MobileGameList) GetTabIds() []int32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

// 用户手机本地游戏列表V2 - 第一个版本写入了脏数据，已废弃
type MobileGameListV2 struct {
	// 转换后的tabIds
	TabIds               []int32  `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileGameListV2) Reset()         { *m = MobileGameListV2{} }
func (m *MobileGameListV2) String() string { return proto.CompactTextString(m) }
func (*MobileGameListV2) ProtoMessage()    {}
func (*MobileGameListV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{40}
}
func (m *MobileGameListV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileGameListV2.Unmarshal(m, b)
}
func (m *MobileGameListV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileGameListV2.Marshal(b, m, deterministic)
}
func (dst *MobileGameListV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileGameListV2.Merge(dst, src)
}
func (m *MobileGameListV2) XXX_Size() int {
	return xxx_messageInfo_MobileGameListV2.Size(m)
}
func (m *MobileGameListV2) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileGameListV2.DiscardUnknown(m)
}

var xxx_messageInfo_MobileGameListV2 proto.InternalMessageInfo

func (m *MobileGameListV2) GetTabIds() []int32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

// MyTagList 关于我的标签
type MyTagList struct {
	Tags                 []string `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MyTagList) Reset()         { *m = MyTagList{} }
func (m *MyTagList) String() string { return proto.CompactTextString(m) }
func (*MyTagList) ProtoMessage()    {}
func (*MyTagList) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{41}
}
func (m *MyTagList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MyTagList.Unmarshal(m, b)
}
func (m *MyTagList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MyTagList.Marshal(b, m, deterministic)
}
func (dst *MyTagList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MyTagList.Merge(dst, src)
}
func (m *MyTagList) XXX_Size() int {
	return xxx_messageInfo_MyTagList.Size(m)
}
func (m *MyTagList) XXX_DiscardUnknown() {
	xxx_messageInfo_MyTagList.DiscardUnknown(m)
}

var xxx_messageInfo_MyTagList proto.InternalMessageInfo

func (m *MyTagList) GetTags() []string {
	if m != nil {
		return m.Tags
	}
	return nil
}

// 游戏卡提权
type GameTabWeight struct {
	CancelWeight         bool     `protobuf:"varint,1,opt,name=cancel_weight,json=cancelWeight,proto3" json:"cancel_weight,omitempty"`
	OldUserWeight        bool     `protobuf:"varint,2,opt,name=old_user_weight,json=oldUserWeight,proto3" json:"old_user_weight,omitempty"`
	HadCanceled          bool     `protobuf:"varint,3,opt,name=had_canceled,json=hadCanceled,proto3" json:"had_canceled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTabWeight) Reset()         { *m = GameTabWeight{} }
func (m *GameTabWeight) String() string { return proto.CompactTextString(m) }
func (*GameTabWeight) ProtoMessage()    {}
func (*GameTabWeight) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{42}
}
func (m *GameTabWeight) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTabWeight.Unmarshal(m, b)
}
func (m *GameTabWeight) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTabWeight.Marshal(b, m, deterministic)
}
func (dst *GameTabWeight) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTabWeight.Merge(dst, src)
}
func (m *GameTabWeight) XXX_Size() int {
	return xxx_messageInfo_GameTabWeight.Size(m)
}
func (m *GameTabWeight) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTabWeight.DiscardUnknown(m)
}

var xxx_messageInfo_GameTabWeight proto.InternalMessageInfo

func (m *GameTabWeight) GetCancelWeight() bool {
	if m != nil {
		return m.CancelWeight
	}
	return false
}

func (m *GameTabWeight) GetOldUserWeight() bool {
	if m != nil {
		return m.OldUserWeight
	}
	return false
}

func (m *GameTabWeight) GetHadCanceled() bool {
	if m != nil {
		return m.HadCanceled
	}
	return false
}

type GameTabWeight_CancelWeightAppend struct {
	CancelWeight         bool     `protobuf:"varint,1,opt,name=cancel_weight,json=cancelWeight,proto3" json:"cancel_weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTabWeight_CancelWeightAppend) Reset()         { *m = GameTabWeight_CancelWeightAppend{} }
func (m *GameTabWeight_CancelWeightAppend) String() string { return proto.CompactTextString(m) }
func (*GameTabWeight_CancelWeightAppend) ProtoMessage()    {}
func (*GameTabWeight_CancelWeightAppend) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{42, 0}
}
func (m *GameTabWeight_CancelWeightAppend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTabWeight_CancelWeightAppend.Unmarshal(m, b)
}
func (m *GameTabWeight_CancelWeightAppend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTabWeight_CancelWeightAppend.Marshal(b, m, deterministic)
}
func (dst *GameTabWeight_CancelWeightAppend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTabWeight_CancelWeightAppend.Merge(dst, src)
}
func (m *GameTabWeight_CancelWeightAppend) XXX_Size() int {
	return xxx_messageInfo_GameTabWeight_CancelWeightAppend.Size(m)
}
func (m *GameTabWeight_CancelWeightAppend) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTabWeight_CancelWeightAppend.DiscardUnknown(m)
}

var xxx_messageInfo_GameTabWeight_CancelWeightAppend proto.InternalMessageInfo

func (m *GameTabWeight_CancelWeightAppend) GetCancelWeight() bool {
	if m != nil {
		return m.CancelWeight
	}
	return false
}

type GameTabWeight_OldUserWeightAppend struct {
	OldUserWeight        bool     `protobuf:"varint,1,opt,name=old_user_weight,json=oldUserWeight,proto3" json:"old_user_weight,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTabWeight_OldUserWeightAppend) Reset()         { *m = GameTabWeight_OldUserWeightAppend{} }
func (m *GameTabWeight_OldUserWeightAppend) String() string { return proto.CompactTextString(m) }
func (*GameTabWeight_OldUserWeightAppend) ProtoMessage()    {}
func (*GameTabWeight_OldUserWeightAppend) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{42, 1}
}
func (m *GameTabWeight_OldUserWeightAppend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTabWeight_OldUserWeightAppend.Unmarshal(m, b)
}
func (m *GameTabWeight_OldUserWeightAppend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTabWeight_OldUserWeightAppend.Marshal(b, m, deterministic)
}
func (dst *GameTabWeight_OldUserWeightAppend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTabWeight_OldUserWeightAppend.Merge(dst, src)
}
func (m *GameTabWeight_OldUserWeightAppend) XXX_Size() int {
	return xxx_messageInfo_GameTabWeight_OldUserWeightAppend.Size(m)
}
func (m *GameTabWeight_OldUserWeightAppend) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTabWeight_OldUserWeightAppend.DiscardUnknown(m)
}

var xxx_messageInfo_GameTabWeight_OldUserWeightAppend proto.InternalMessageInfo

func (m *GameTabWeight_OldUserWeightAppend) GetOldUserWeight() bool {
	if m != nil {
		return m.OldUserWeight
	}
	return false
}

type GameTabWeight_HadCanceledAppend struct {
	HadCanceled          bool     `protobuf:"varint,1,opt,name=had_canceled,json=hadCanceled,proto3" json:"had_canceled,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameTabWeight_HadCanceledAppend) Reset()         { *m = GameTabWeight_HadCanceledAppend{} }
func (m *GameTabWeight_HadCanceledAppend) String() string { return proto.CompactTextString(m) }
func (*GameTabWeight_HadCanceledAppend) ProtoMessage()    {}
func (*GameTabWeight_HadCanceledAppend) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{42, 2}
}
func (m *GameTabWeight_HadCanceledAppend) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameTabWeight_HadCanceledAppend.Unmarshal(m, b)
}
func (m *GameTabWeight_HadCanceledAppend) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameTabWeight_HadCanceledAppend.Marshal(b, m, deterministic)
}
func (dst *GameTabWeight_HadCanceledAppend) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameTabWeight_HadCanceledAppend.Merge(dst, src)
}
func (m *GameTabWeight_HadCanceledAppend) XXX_Size() int {
	return xxx_messageInfo_GameTabWeight_HadCanceledAppend.Size(m)
}
func (m *GameTabWeight_HadCanceledAppend) XXX_DiscardUnknown() {
	xxx_messageInfo_GameTabWeight_HadCanceledAppend.DiscardUnknown(m)
}

var xxx_messageInfo_GameTabWeight_HadCanceledAppend proto.InternalMessageInfo

func (m *GameTabWeight_HadCanceledAppend) GetHadCanceled() bool {
	if m != nil {
		return m.HadCanceled
	}
	return false
}

// 用户音乐偏好
type UserMusicPrefer struct {
	Uid                      uint32                   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UiRoomTabDurationRate_1D map[uint32]float64       `protobuf:"bytes,2,rep,name=ui_room_tab_duration_rate_1d,json=uiRoomTabDurationRate1d,proto3" json:"ui_room_tab_duration_rate_1d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiRoomTabDurationRate_3D map[uint32]float64       `protobuf:"bytes,3,rep,name=ui_room_tab_duration_rate_3d,json=uiRoomTabDurationRate3d,proto3" json:"ui_room_tab_duration_rate_3d,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiRoomTabDurationRate_1W map[uint32]float64       `protobuf:"bytes,4,rep,name=ui_room_tab_duration_rate_1w,json=uiRoomTabDurationRate1w,proto3" json:"ui_room_tab_duration_rate_1w,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiRoomTabDurationRate_2W map[uint32]float64       `protobuf:"bytes,5,rep,name=ui_room_tab_duration_rate_2w,json=uiRoomTabDurationRate2w,proto3" json:"ui_room_tab_duration_rate_2w,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiSongPrefer             map[uint32]*UserSongPref `protobuf:"bytes,6,rep,name=ui_song_prefer,json=uiSongPrefer,proto3" json:"ui_song_prefer,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 你行你唱各专区逗留占比
	UiUcusTagDurationRate_1D []string `protobuf:"bytes,7,rep,name=ui_ucus_tag_duration_rate_1d,json=uiUcusTagDurationRate1d,proto3" json:"ui_ucus_tag_duration_rate_1d,omitempty"`
	UiUcusTagDurationRate_3D []string `protobuf:"bytes,8,rep,name=ui_ucus_tag_duration_rate_3d,json=uiUcusTagDurationRate3d,proto3" json:"ui_ucus_tag_duration_rate_3d,omitempty"`
	UiUcusTagDurationRate_1W []string `protobuf:"bytes,9,rep,name=ui_ucus_tag_duration_rate_1w,json=uiUcusTagDurationRate1w,proto3" json:"ui_ucus_tag_duration_rate_1w,omitempty"`
	// 用户音乐房进房偏好
	UiRoomMusicTabEnterPref_1W map[uint32]float64 `protobuf:"bytes,10,rep,name=ui_room_music_tab_enter_pref_1w,json=uiRoomMusicTabEnterPref1w,proto3" json:"ui_room_music_tab_enter_pref_1w,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiRoomMusicTabEnterPref_3W map[uint32]float64 `protobuf:"bytes,11,rep,name=ui_room_music_tab_enter_pref_3w,json=uiRoomMusicTabEnterPref3w,proto3" json:"ui_room_music_tab_enter_pref_3w,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral       struct{}           `json:"-"`
	XXX_unrecognized           []byte             `json:"-"`
	XXX_sizecache              int32              `json:"-"`
}

func (m *UserMusicPrefer) Reset()         { *m = UserMusicPrefer{} }
func (m *UserMusicPrefer) String() string { return proto.CompactTextString(m) }
func (*UserMusicPrefer) ProtoMessage()    {}
func (*UserMusicPrefer) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{43}
}
func (m *UserMusicPrefer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicPrefer.Unmarshal(m, b)
}
func (m *UserMusicPrefer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicPrefer.Marshal(b, m, deterministic)
}
func (dst *UserMusicPrefer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicPrefer.Merge(dst, src)
}
func (m *UserMusicPrefer) XXX_Size() int {
	return xxx_messageInfo_UserMusicPrefer.Size(m)
}
func (m *UserMusicPrefer) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicPrefer.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicPrefer proto.InternalMessageInfo

func (m *UserMusicPrefer) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserMusicPrefer) GetUiRoomTabDurationRate_1D() map[uint32]float64 {
	if m != nil {
		return m.UiRoomTabDurationRate_1D
	}
	return nil
}

func (m *UserMusicPrefer) GetUiRoomTabDurationRate_3D() map[uint32]float64 {
	if m != nil {
		return m.UiRoomTabDurationRate_3D
	}
	return nil
}

func (m *UserMusicPrefer) GetUiRoomTabDurationRate_1W() map[uint32]float64 {
	if m != nil {
		return m.UiRoomTabDurationRate_1W
	}
	return nil
}

func (m *UserMusicPrefer) GetUiRoomTabDurationRate_2W() map[uint32]float64 {
	if m != nil {
		return m.UiRoomTabDurationRate_2W
	}
	return nil
}

func (m *UserMusicPrefer) GetUiSongPrefer() map[uint32]*UserSongPref {
	if m != nil {
		return m.UiSongPrefer
	}
	return nil
}

func (m *UserMusicPrefer) GetUiUcusTagDurationRate_1D() []string {
	if m != nil {
		return m.UiUcusTagDurationRate_1D
	}
	return nil
}

func (m *UserMusicPrefer) GetUiUcusTagDurationRate_3D() []string {
	if m != nil {
		return m.UiUcusTagDurationRate_3D
	}
	return nil
}

func (m *UserMusicPrefer) GetUiUcusTagDurationRate_1W() []string {
	if m != nil {
		return m.UiUcusTagDurationRate_1W
	}
	return nil
}

func (m *UserMusicPrefer) GetUiRoomMusicTabEnterPref_1W() map[uint32]float64 {
	if m != nil {
		return m.UiRoomMusicTabEnterPref_1W
	}
	return nil
}

func (m *UserMusicPrefer) GetUiRoomMusicTabEnterPref_3W() map[uint32]float64 {
	if m != nil {
		return m.UiRoomMusicTabEnterPref_3W
	}
	return nil
}

type UserMusicPrefer_UcusTagDurationUpdate struct {
	UiUcusTagDurationRate_1D []string `protobuf:"bytes,1,rep,name=ui_ucus_tag_duration_rate_1d,json=uiUcusTagDurationRate1d,proto3" json:"ui_ucus_tag_duration_rate_1d,omitempty"`
	UiUcusTagDurationRate_3D []string `protobuf:"bytes,2,rep,name=ui_ucus_tag_duration_rate_3d,json=uiUcusTagDurationRate3d,proto3" json:"ui_ucus_tag_duration_rate_3d,omitempty"`
	UiUcusTagDurationRate_1W []string `protobuf:"bytes,3,rep,name=ui_ucus_tag_duration_rate_1w,json=uiUcusTagDurationRate1w,proto3" json:"ui_ucus_tag_duration_rate_1w,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *UserMusicPrefer_UcusTagDurationUpdate) Reset()         { *m = UserMusicPrefer_UcusTagDurationUpdate{} }
func (m *UserMusicPrefer_UcusTagDurationUpdate) String() string { return proto.CompactTextString(m) }
func (*UserMusicPrefer_UcusTagDurationUpdate) ProtoMessage()    {}
func (*UserMusicPrefer_UcusTagDurationUpdate) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{43, 7}
}
func (m *UserMusicPrefer_UcusTagDurationUpdate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicPrefer_UcusTagDurationUpdate.Unmarshal(m, b)
}
func (m *UserMusicPrefer_UcusTagDurationUpdate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicPrefer_UcusTagDurationUpdate.Marshal(b, m, deterministic)
}
func (dst *UserMusicPrefer_UcusTagDurationUpdate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicPrefer_UcusTagDurationUpdate.Merge(dst, src)
}
func (m *UserMusicPrefer_UcusTagDurationUpdate) XXX_Size() int {
	return xxx_messageInfo_UserMusicPrefer_UcusTagDurationUpdate.Size(m)
}
func (m *UserMusicPrefer_UcusTagDurationUpdate) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicPrefer_UcusTagDurationUpdate.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicPrefer_UcusTagDurationUpdate proto.InternalMessageInfo

func (m *UserMusicPrefer_UcusTagDurationUpdate) GetUiUcusTagDurationRate_1D() []string {
	if m != nil {
		return m.UiUcusTagDurationRate_1D
	}
	return nil
}

func (m *UserMusicPrefer_UcusTagDurationUpdate) GetUiUcusTagDurationRate_3D() []string {
	if m != nil {
		return m.UiUcusTagDurationRate_3D
	}
	return nil
}

func (m *UserMusicPrefer_UcusTagDurationUpdate) GetUiUcusTagDurationRate_1W() []string {
	if m != nil {
		return m.UiUcusTagDurationRate_1W
	}
	return nil
}

type UserSongPref struct {
	UiSongClickRate_1D          map[string]float64 `protobuf:"bytes,1,rep,name=ui_song_click_rate_1d,json=uiSongClickRate1d,proto3" json:"ui_song_click_rate_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiSongClickRate_3D          map[string]float64 `protobuf:"bytes,2,rep,name=ui_song_click_rate_3d,json=uiSongClickRate3d,proto3" json:"ui_song_click_rate_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiSongClickRate_1W          map[string]float64 `protobuf:"bytes,3,rep,name=ui_song_click_rate_1w,json=uiSongClickRate1w,proto3" json:"ui_song_click_rate_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
	UiSongListenRate_1D         map[string]float32 `protobuf:"bytes,4,rep,name=ui_song_listen_rate_1d,json=uiSongListenRate1d,proto3" json:"ui_song_listen_rate_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongListenRate_3D         map[string]float32 `protobuf:"bytes,5,rep,name=ui_song_listen_rate_3d,json=uiSongListenRate3d,proto3" json:"ui_song_listen_rate_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongListenRate_1W         map[string]float32 `protobuf:"bytes,6,rep,name=ui_song_listen_rate_1w,json=uiSongListenRate1w,proto3" json:"ui_song_listen_rate_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongSingRate_1D           map[string]float32 `protobuf:"bytes,8,rep,name=ui_song_sing_rate_1d,json=uiSongSingRate1d,proto3" json:"ui_song_sing_rate_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongSingRate_3D           map[string]float32 `protobuf:"bytes,9,rep,name=ui_song_sing_rate_3d,json=uiSongSingRate3d,proto3" json:"ui_song_sing_rate_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongSingRate_1W           map[string]float32 `protobuf:"bytes,10,rep,name=ui_song_sing_rate_1w,json=uiSongSingRate1w,proto3" json:"ui_song_sing_rate_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongListenCompleteRate_1D map[string]float32 `protobuf:"bytes,11,rep,name=ui_song_listen_complete_rate_1d,json=uiSongListenCompleteRate1d,proto3" json:"ui_song_listen_complete_rate_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongListenCompleteRate_3D map[string]float32 `protobuf:"bytes,12,rep,name=ui_song_listen_complete_rate_3d,json=uiSongListenCompleteRate3d,proto3" json:"ui_song_listen_complete_rate_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongListenCompleteRate_1W map[string]float32 `protobuf:"bytes,13,rep,name=ui_song_listen_complete_rate_1w,json=uiSongListenCompleteRate1w,proto3" json:"ui_song_listen_complete_rate_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongScore                 map[string]float32 `protobuf:"bytes,14,rep,name=ui_song_score,json=uiSongScore,proto3" json:"ui_song_score,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongSingSum_1D            map[string]float32 `protobuf:"bytes,15,rep,name=ui_song_sing_sum_1d,json=uiSongSingSum1d,proto3" json:"ui_song_sing_sum_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongSingSum_3D            map[string]float32 `protobuf:"bytes,16,rep,name=ui_song_sing_sum_3d,json=uiSongSingSum3d,proto3" json:"ui_song_sing_sum_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSongSingSum_1W            map[string]float32 `protobuf:"bytes,17,rep,name=ui_song_sing_sum_1w,json=uiSongSingSum1w,proto3" json:"ui_song_sing_sum_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	// https://q9jvw0u5f5.feishu.cn/docs/doccnI4cEpLWrVPUIltcAHOZNZY
	// 唱维度 和 听维度 偏好系数
	UiSingSongPreferenceScore_1D  map[string]float32 `protobuf:"bytes,18,rep,name=ui_sing_song_preference_score_1d,json=uiSingSongPreferenceScore1d,proto3" json:"ui_sing_song_preference_score_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSingSongPreferenceScore_3D  map[string]float32 `protobuf:"bytes,19,rep,name=ui_sing_song_preference_score_3d,json=uiSingSongPreferenceScore3d,proto3" json:"ui_sing_song_preference_score_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiSingSongPreferenceScore_1W  map[string]float32 `protobuf:"bytes,20,rep,name=ui_sing_song_preference_score_1w,json=uiSingSongPreferenceScore1w,proto3" json:"ui_sing_song_preference_score_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiListenSongPreferenceSong_1D map[string]float32 `protobuf:"bytes,21,rep,name=ui_listen_song_preference_song_1d,json=uiListenSongPreferenceSong1d,proto3" json:"ui_listen_song_preference_song_1d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiListenSongPreferenceSong_3D map[string]float32 `protobuf:"bytes,22,rep,name=ui_listen_song_preference_song_3d,json=uiListenSongPreferenceSong3d,proto3" json:"ui_listen_song_preference_song_3d,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	UiListenSongPreferenceSong_1W map[string]float32 `protobuf:"bytes,23,rep,name=ui_listen_song_preference_song_1w,json=uiListenSongPreferenceSong1w,proto3" json:"ui_listen_song_preference_song_1w,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed32,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral          struct{}           `json:"-"`
	XXX_unrecognized              []byte             `json:"-"`
	XXX_sizecache                 int32              `json:"-"`
}

func (m *UserSongPref) Reset()         { *m = UserSongPref{} }
func (m *UserSongPref) String() string { return proto.CompactTextString(m) }
func (*UserSongPref) ProtoMessage()    {}
func (*UserSongPref) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{44}
}
func (m *UserSongPref) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserSongPref.Unmarshal(m, b)
}
func (m *UserSongPref) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserSongPref.Marshal(b, m, deterministic)
}
func (dst *UserSongPref) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserSongPref.Merge(dst, src)
}
func (m *UserSongPref) XXX_Size() int {
	return xxx_messageInfo_UserSongPref.Size(m)
}
func (m *UserSongPref) XXX_DiscardUnknown() {
	xxx_messageInfo_UserSongPref.DiscardUnknown(m)
}

var xxx_messageInfo_UserSongPref proto.InternalMessageInfo

func (m *UserSongPref) GetUiSongClickRate_1D() map[string]float64 {
	if m != nil {
		return m.UiSongClickRate_1D
	}
	return nil
}

func (m *UserSongPref) GetUiSongClickRate_3D() map[string]float64 {
	if m != nil {
		return m.UiSongClickRate_3D
	}
	return nil
}

func (m *UserSongPref) GetUiSongClickRate_1W() map[string]float64 {
	if m != nil {
		return m.UiSongClickRate_1W
	}
	return nil
}

func (m *UserSongPref) GetUiSongListenRate_1D() map[string]float32 {
	if m != nil {
		return m.UiSongListenRate_1D
	}
	return nil
}

func (m *UserSongPref) GetUiSongListenRate_3D() map[string]float32 {
	if m != nil {
		return m.UiSongListenRate_3D
	}
	return nil
}

func (m *UserSongPref) GetUiSongListenRate_1W() map[string]float32 {
	if m != nil {
		return m.UiSongListenRate_1W
	}
	return nil
}

func (m *UserSongPref) GetUiSongSingRate_1D() map[string]float32 {
	if m != nil {
		return m.UiSongSingRate_1D
	}
	return nil
}

func (m *UserSongPref) GetUiSongSingRate_3D() map[string]float32 {
	if m != nil {
		return m.UiSongSingRate_3D
	}
	return nil
}

func (m *UserSongPref) GetUiSongSingRate_1W() map[string]float32 {
	if m != nil {
		return m.UiSongSingRate_1W
	}
	return nil
}

func (m *UserSongPref) GetUiSongListenCompleteRate_1D() map[string]float32 {
	if m != nil {
		return m.UiSongListenCompleteRate_1D
	}
	return nil
}

func (m *UserSongPref) GetUiSongListenCompleteRate_3D() map[string]float32 {
	if m != nil {
		return m.UiSongListenCompleteRate_3D
	}
	return nil
}

func (m *UserSongPref) GetUiSongListenCompleteRate_1W() map[string]float32 {
	if m != nil {
		return m.UiSongListenCompleteRate_1W
	}
	return nil
}

func (m *UserSongPref) GetUiSongScore() map[string]float32 {
	if m != nil {
		return m.UiSongScore
	}
	return nil
}

func (m *UserSongPref) GetUiSongSingSum_1D() map[string]float32 {
	if m != nil {
		return m.UiSongSingSum_1D
	}
	return nil
}

func (m *UserSongPref) GetUiSongSingSum_3D() map[string]float32 {
	if m != nil {
		return m.UiSongSingSum_3D
	}
	return nil
}

func (m *UserSongPref) GetUiSongSingSum_1W() map[string]float32 {
	if m != nil {
		return m.UiSongSingSum_1W
	}
	return nil
}

func (m *UserSongPref) GetUiSingSongPreferenceScore_1D() map[string]float32 {
	if m != nil {
		return m.UiSingSongPreferenceScore_1D
	}
	return nil
}

func (m *UserSongPref) GetUiSingSongPreferenceScore_3D() map[string]float32 {
	if m != nil {
		return m.UiSingSongPreferenceScore_3D
	}
	return nil
}

func (m *UserSongPref) GetUiSingSongPreferenceScore_1W() map[string]float32 {
	if m != nil {
		return m.UiSingSongPreferenceScore_1W
	}
	return nil
}

func (m *UserSongPref) GetUiListenSongPreferenceSong_1D() map[string]float32 {
	if m != nil {
		return m.UiListenSongPreferenceSong_1D
	}
	return nil
}

func (m *UserSongPref) GetUiListenSongPreferenceSong_3D() map[string]float32 {
	if m != nil {
		return m.UiListenSongPreferenceSong_3D
	}
	return nil
}

func (m *UserSongPref) GetUiListenSongPreferenceSong_1W() map[string]float32 {
	if m != nil {
		return m.UiListenSongPreferenceSong_1W
	}
	return nil
}

type UserMusicChannelEnterAction struct {
	// 用户近5次进房房间类型和逗留时常
	UserMusicChannel     []string `protobuf:"bytes,1,rep,name=UserMusicChannel,proto3" json:"UserMusicChannel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicChannelEnterAction) Reset()         { *m = UserMusicChannelEnterAction{} }
func (m *UserMusicChannelEnterAction) String() string { return proto.CompactTextString(m) }
func (*UserMusicChannelEnterAction) ProtoMessage()    {}
func (*UserMusicChannelEnterAction) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{45}
}
func (m *UserMusicChannelEnterAction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicChannelEnterAction.Unmarshal(m, b)
}
func (m *UserMusicChannelEnterAction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicChannelEnterAction.Marshal(b, m, deterministic)
}
func (dst *UserMusicChannelEnterAction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicChannelEnterAction.Merge(dst, src)
}
func (m *UserMusicChannelEnterAction) XXX_Size() int {
	return xxx_messageInfo_UserMusicChannelEnterAction.Size(m)
}
func (m *UserMusicChannelEnterAction) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicChannelEnterAction.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicChannelEnterAction proto.InternalMessageInfo

func (m *UserMusicChannelEnterAction) GetUserMusicChannel() []string {
	if m != nil {
		return m.UserMusicChannel
	}
	return nil
}

type UserMusicChannelEnterAction_AppendMusicChannel struct {
	UserMusicChannel     []string `protobuf:"bytes,1,rep,name=UserMusicChannel,proto3" json:"UserMusicChannel,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicChannelEnterAction_AppendMusicChannel) Reset() {
	*m = UserMusicChannelEnterAction_AppendMusicChannel{}
}
func (m *UserMusicChannelEnterAction_AppendMusicChannel) String() string {
	return proto.CompactTextString(m)
}
func (*UserMusicChannelEnterAction_AppendMusicChannel) ProtoMessage() {}
func (*UserMusicChannelEnterAction_AppendMusicChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{45, 0}
}
func (m *UserMusicChannelEnterAction_AppendMusicChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicChannelEnterAction_AppendMusicChannel.Unmarshal(m, b)
}
func (m *UserMusicChannelEnterAction_AppendMusicChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicChannelEnterAction_AppendMusicChannel.Marshal(b, m, deterministic)
}
func (dst *UserMusicChannelEnterAction_AppendMusicChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicChannelEnterAction_AppendMusicChannel.Merge(dst, src)
}
func (m *UserMusicChannelEnterAction_AppendMusicChannel) XXX_Size() int {
	return xxx_messageInfo_UserMusicChannelEnterAction_AppendMusicChannel.Size(m)
}
func (m *UserMusicChannelEnterAction_AppendMusicChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicChannelEnterAction_AppendMusicChannel.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicChannelEnterAction_AppendMusicChannel proto.InternalMessageInfo

func (m *UserMusicChannelEnterAction_AppendMusicChannel) GetUserMusicChannel() []string {
	if m != nil {
		return m.UserMusicChannel
	}
	return nil
}

// 用户当前所在房间类型
type UserInChannel struct {
	InChannelType        uint32   `protobuf:"varint,1,opt,name=in_channel_type,json=inChannelType,proto3" json:"in_channel_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInChannel) Reset()         { *m = UserInChannel{} }
func (m *UserInChannel) String() string { return proto.CompactTextString(m) }
func (*UserInChannel) ProtoMessage()    {}
func (*UserInChannel) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{46}
}
func (m *UserInChannel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInChannel.Unmarshal(m, b)
}
func (m *UserInChannel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInChannel.Marshal(b, m, deterministic)
}
func (dst *UserInChannel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInChannel.Merge(dst, src)
}
func (m *UserInChannel) XXX_Size() int {
	return xxx_messageInfo_UserInChannel.Size(m)
}
func (m *UserInChannel) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInChannel.DiscardUnknown(m)
}

var xxx_messageInfo_UserInChannel proto.InternalMessageInfo

func (m *UserInChannel) GetInChannelType() uint32 {
	if m != nil {
		return m.InChannelType
	}
	return 0
}

// 只有开黑预约用户才会有该画像
type UserAppStatus struct {
	AppStatus            uint32   `protobuf:"varint,1,opt,name=app_status,json=appStatus,proto3" json:"app_status,omitempty"`
	AppChangeTime        int64    `protobuf:"varint,2,opt,name=app_change_time,json=appChangeTime,proto3" json:"app_change_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAppStatus) Reset()         { *m = UserAppStatus{} }
func (m *UserAppStatus) String() string { return proto.CompactTextString(m) }
func (*UserAppStatus) ProtoMessage()    {}
func (*UserAppStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{47}
}
func (m *UserAppStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAppStatus.Unmarshal(m, b)
}
func (m *UserAppStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAppStatus.Marshal(b, m, deterministic)
}
func (dst *UserAppStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAppStatus.Merge(dst, src)
}
func (m *UserAppStatus) XXX_Size() int {
	return xxx_messageInfo_UserAppStatus.Size(m)
}
func (m *UserAppStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAppStatus.DiscardUnknown(m)
}

var xxx_messageInfo_UserAppStatus proto.InternalMessageInfo

func (m *UserAppStatus) GetAppStatus() uint32 {
	if m != nil {
		return m.AppStatus
	}
	return 0
}

func (m *UserAppStatus) GetAppChangeTime() int64 {
	if m != nil {
		return m.AppChangeTime
	}
	return 0
}

// 用户行为动作的基数统计画像
type UserActionStat struct {
	TcDeliverTimes  uint32   `protobuf:"varint,1,opt,name=tc_deliver_times,json=tcDeliverTimes,proto3" json:"tc_deliver_times,omitempty"`
	EnterChannelSet []uint32 `protobuf:"varint,2,rep,packed,name=enter_channel_set,json=enterChannelSet,proto3" json:"enter_channel_set,omitempty"`
	// 用户近10次进出的房间id 以及时间戳 channel_id + _ + timestamp + _ + action + _ + tab_id
	ChannelTraces          []string `protobuf:"bytes,4,rep,name=channel_traces,json=channelTraces,proto3" json:"channel_traces,omitempty"`
	PreferDeliverList      []uint64 `protobuf:"varint,5,rep,packed,name=prefer_deliver_list,json=preferDeliverList,proto3" json:"prefer_deliver_list,omitempty"`
	LastPreferTime         uint64   `protobuf:"varint,6,opt,name=last_prefer_time,json=lastPreferTime,proto3" json:"last_prefer_time,omitempty"`
	LastPreferTimeTtl      int64    `protobuf:"varint,7,opt,name=last_prefer_time_ttl,json=lastPreferTimeTtl,proto3" json:"last_prefer_time_ttl,omitempty"`
	CancelPreferGameReRank bool     `protobuf:"varint,8,opt,name=cancel_prefer_game_re_rank,json=cancelPreferGameReRank,proto3" json:"cancel_prefer_game_re_rank,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *UserActionStat) Reset()         { *m = UserActionStat{} }
func (m *UserActionStat) String() string { return proto.CompactTextString(m) }
func (*UserActionStat) ProtoMessage()    {}
func (*UserActionStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48}
}
func (m *UserActionStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat.Unmarshal(m, b)
}
func (m *UserActionStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat.Marshal(b, m, deterministic)
}
func (dst *UserActionStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat.Merge(dst, src)
}
func (m *UserActionStat) XXX_Size() int {
	return xxx_messageInfo_UserActionStat.Size(m)
}
func (m *UserActionStat) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat proto.InternalMessageInfo

func (m *UserActionStat) GetTcDeliverTimes() uint32 {
	if m != nil {
		return m.TcDeliverTimes
	}
	return 0
}

func (m *UserActionStat) GetEnterChannelSet() []uint32 {
	if m != nil {
		return m.EnterChannelSet
	}
	return nil
}

func (m *UserActionStat) GetChannelTraces() []string {
	if m != nil {
		return m.ChannelTraces
	}
	return nil
}

func (m *UserActionStat) GetPreferDeliverList() []uint64 {
	if m != nil {
		return m.PreferDeliverList
	}
	return nil
}

func (m *UserActionStat) GetLastPreferTime() uint64 {
	if m != nil {
		return m.LastPreferTime
	}
	return 0
}

func (m *UserActionStat) GetLastPreferTimeTtl() int64 {
	if m != nil {
		return m.LastPreferTimeTtl
	}
	return 0
}

func (m *UserActionStat) GetCancelPreferGameReRank() bool {
	if m != nil {
		return m.CancelPreferGameReRank
	}
	return false
}

type UserActionStat_AddEnterChannelSet struct {
	EnterChannelSet      []uint32 `protobuf:"varint,1,rep,packed,name=enter_channel_set,json=enterChannelSet,proto3" json:"enter_channel_set,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserActionStat_AddEnterChannelSet) Reset()         { *m = UserActionStat_AddEnterChannelSet{} }
func (m *UserActionStat_AddEnterChannelSet) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_AddEnterChannelSet) ProtoMessage()    {}
func (*UserActionStat_AddEnterChannelSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 0}
}
func (m *UserActionStat_AddEnterChannelSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_AddEnterChannelSet.Unmarshal(m, b)
}
func (m *UserActionStat_AddEnterChannelSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_AddEnterChannelSet.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_AddEnterChannelSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_AddEnterChannelSet.Merge(dst, src)
}
func (m *UserActionStat_AddEnterChannelSet) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_AddEnterChannelSet.Size(m)
}
func (m *UserActionStat_AddEnterChannelSet) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_AddEnterChannelSet.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_AddEnterChannelSet proto.InternalMessageInfo

func (m *UserActionStat_AddEnterChannelSet) GetEnterChannelSet() []uint32 {
	if m != nil {
		return m.EnterChannelSet
	}
	return nil
}

type UserActionStat_AddChannelTraces struct {
	ChannelTraces        []string `protobuf:"bytes,1,rep,name=channel_traces,json=channelTraces,proto3" json:"channel_traces,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserActionStat_AddChannelTraces) Reset()         { *m = UserActionStat_AddChannelTraces{} }
func (m *UserActionStat_AddChannelTraces) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_AddChannelTraces) ProtoMessage()    {}
func (*UserActionStat_AddChannelTraces) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 1}
}
func (m *UserActionStat_AddChannelTraces) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_AddChannelTraces.Unmarshal(m, b)
}
func (m *UserActionStat_AddChannelTraces) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_AddChannelTraces.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_AddChannelTraces) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_AddChannelTraces.Merge(dst, src)
}
func (m *UserActionStat_AddChannelTraces) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_AddChannelTraces.Size(m)
}
func (m *UserActionStat_AddChannelTraces) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_AddChannelTraces.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_AddChannelTraces proto.InternalMessageInfo

func (m *UserActionStat_AddChannelTraces) GetChannelTraces() []string {
	if m != nil {
		return m.ChannelTraces
	}
	return nil
}

type UserActionStat_SetLastPreferDeliverTime struct {
	LastPreferTime       uint64   `protobuf:"varint,1,opt,name=last_prefer_time,json=lastPreferTime,proto3" json:"last_prefer_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserActionStat_SetLastPreferDeliverTime) Reset() {
	*m = UserActionStat_SetLastPreferDeliverTime{}
}
func (m *UserActionStat_SetLastPreferDeliverTime) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_SetLastPreferDeliverTime) ProtoMessage()    {}
func (*UserActionStat_SetLastPreferDeliverTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 2}
}
func (m *UserActionStat_SetLastPreferDeliverTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_SetLastPreferDeliverTime.Unmarshal(m, b)
}
func (m *UserActionStat_SetLastPreferDeliverTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_SetLastPreferDeliverTime.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_SetLastPreferDeliverTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_SetLastPreferDeliverTime.Merge(dst, src)
}
func (m *UserActionStat_SetLastPreferDeliverTime) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_SetLastPreferDeliverTime.Size(m)
}
func (m *UserActionStat_SetLastPreferDeliverTime) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_SetLastPreferDeliverTime.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_SetLastPreferDeliverTime proto.InternalMessageInfo

func (m *UserActionStat_SetLastPreferDeliverTime) GetLastPreferTime() uint64 {
	if m != nil {
		return m.LastPreferTime
	}
	return 0
}

type UserActionStat_AddPreferDeliverTime struct {
	PreferDeliverList    []uint64 `protobuf:"varint,1,rep,packed,name=prefer_deliver_list,json=preferDeliverList,proto3" json:"prefer_deliver_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserActionStat_AddPreferDeliverTime) Reset()         { *m = UserActionStat_AddPreferDeliverTime{} }
func (m *UserActionStat_AddPreferDeliverTime) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_AddPreferDeliverTime) ProtoMessage()    {}
func (*UserActionStat_AddPreferDeliverTime) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 3}
}
func (m *UserActionStat_AddPreferDeliverTime) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_AddPreferDeliverTime.Unmarshal(m, b)
}
func (m *UserActionStat_AddPreferDeliverTime) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_AddPreferDeliverTime.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_AddPreferDeliverTime) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_AddPreferDeliverTime.Merge(dst, src)
}
func (m *UserActionStat_AddPreferDeliverTime) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_AddPreferDeliverTime.Size(m)
}
func (m *UserActionStat_AddPreferDeliverTime) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_AddPreferDeliverTime.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_AddPreferDeliverTime proto.InternalMessageInfo

func (m *UserActionStat_AddPreferDeliverTime) GetPreferDeliverList() []uint64 {
	if m != nil {
		return m.PreferDeliverList
	}
	return nil
}

type UserActionStat_RecentEnterTrace struct {
	ChannelTraces          []string `protobuf:"bytes,1,rep,name=channel_traces,json=channelTraces,proto3" json:"channel_traces,omitempty"`
	LastPreferTime         uint64   `protobuf:"varint,2,opt,name=last_prefer_time,json=lastPreferTime,proto3" json:"last_prefer_time,omitempty"`
	LastPreferTimeTtl      int64    `protobuf:"varint,3,opt,name=last_prefer_time_ttl,json=lastPreferTimeTtl,proto3" json:"last_prefer_time_ttl,omitempty"`
	CancelPreferGameReRank bool     `protobuf:"varint,4,opt,name=cancel_prefer_game_re_rank,json=cancelPreferGameReRank,proto3" json:"cancel_prefer_game_re_rank,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *UserActionStat_RecentEnterTrace) Reset()         { *m = UserActionStat_RecentEnterTrace{} }
func (m *UserActionStat_RecentEnterTrace) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_RecentEnterTrace) ProtoMessage()    {}
func (*UserActionStat_RecentEnterTrace) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 4}
}
func (m *UserActionStat_RecentEnterTrace) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_RecentEnterTrace.Unmarshal(m, b)
}
func (m *UserActionStat_RecentEnterTrace) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_RecentEnterTrace.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_RecentEnterTrace) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_RecentEnterTrace.Merge(dst, src)
}
func (m *UserActionStat_RecentEnterTrace) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_RecentEnterTrace.Size(m)
}
func (m *UserActionStat_RecentEnterTrace) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_RecentEnterTrace.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_RecentEnterTrace proto.InternalMessageInfo

func (m *UserActionStat_RecentEnterTrace) GetChannelTraces() []string {
	if m != nil {
		return m.ChannelTraces
	}
	return nil
}

func (m *UserActionStat_RecentEnterTrace) GetLastPreferTime() uint64 {
	if m != nil {
		return m.LastPreferTime
	}
	return 0
}

func (m *UserActionStat_RecentEnterTrace) GetLastPreferTimeTtl() int64 {
	if m != nil {
		return m.LastPreferTimeTtl
	}
	return 0
}

func (m *UserActionStat_RecentEnterTrace) GetCancelPreferGameReRank() bool {
	if m != nil {
		return m.CancelPreferGameReRank
	}
	return false
}

type UserActionStat_SetCancelPreferGameReRank struct {
	CancelPreferGameReRank bool     `protobuf:"varint,1,opt,name=cancel_prefer_game_re_rank,json=cancelPreferGameReRank,proto3" json:"cancel_prefer_game_re_rank,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *UserActionStat_SetCancelPreferGameReRank) Reset() {
	*m = UserActionStat_SetCancelPreferGameReRank{}
}
func (m *UserActionStat_SetCancelPreferGameReRank) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_SetCancelPreferGameReRank) ProtoMessage()    {}
func (*UserActionStat_SetCancelPreferGameReRank) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 5}
}
func (m *UserActionStat_SetCancelPreferGameReRank) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_SetCancelPreferGameReRank.Unmarshal(m, b)
}
func (m *UserActionStat_SetCancelPreferGameReRank) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_SetCancelPreferGameReRank.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_SetCancelPreferGameReRank) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_SetCancelPreferGameReRank.Merge(dst, src)
}
func (m *UserActionStat_SetCancelPreferGameReRank) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_SetCancelPreferGameReRank.Size(m)
}
func (m *UserActionStat_SetCancelPreferGameReRank) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_SetCancelPreferGameReRank.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_SetCancelPreferGameReRank proto.InternalMessageInfo

func (m *UserActionStat_SetCancelPreferGameReRank) GetCancelPreferGameReRank() bool {
	if m != nil {
		return m.CancelPreferGameReRank
	}
	return false
}

type UserActionStat_IncTCDeliverTimes struct {
	TcDeliverTimes       uint32   `protobuf:"varint,1,opt,name=tc_deliver_times,json=tcDeliverTimes,proto3" json:"tc_deliver_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserActionStat_IncTCDeliverTimes) Reset()         { *m = UserActionStat_IncTCDeliverTimes{} }
func (m *UserActionStat_IncTCDeliverTimes) String() string { return proto.CompactTextString(m) }
func (*UserActionStat_IncTCDeliverTimes) ProtoMessage()    {}
func (*UserActionStat_IncTCDeliverTimes) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{48, 6}
}
func (m *UserActionStat_IncTCDeliverTimes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserActionStat_IncTCDeliverTimes.Unmarshal(m, b)
}
func (m *UserActionStat_IncTCDeliverTimes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserActionStat_IncTCDeliverTimes.Marshal(b, m, deterministic)
}
func (dst *UserActionStat_IncTCDeliverTimes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserActionStat_IncTCDeliverTimes.Merge(dst, src)
}
func (m *UserActionStat_IncTCDeliverTimes) XXX_Size() int {
	return xxx_messageInfo_UserActionStat_IncTCDeliverTimes.Size(m)
}
func (m *UserActionStat_IncTCDeliverTimes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserActionStat_IncTCDeliverTimes.DiscardUnknown(m)
}

var xxx_messageInfo_UserActionStat_IncTCDeliverTimes proto.InternalMessageInfo

func (m *UserActionStat_IncTCDeliverTimes) GetTcDeliverTimes() uint32 {
	if m != nil {
		return m.TcDeliverTimes
	}
	return 0
}

type UserDeliverPrefGames struct {
	Games                []*UserDeliverPrefGames_Game `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	ConfirmTime          uint64                       `protobuf:"varint,2,opt,name=confirm_time,json=confirmTime,proto3" json:"confirm_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UserDeliverPrefGames) Reset()         { *m = UserDeliverPrefGames{} }
func (m *UserDeliverPrefGames) String() string { return proto.CompactTextString(m) }
func (*UserDeliverPrefGames) ProtoMessage()    {}
func (*UserDeliverPrefGames) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{49}
}
func (m *UserDeliverPrefGames) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDeliverPrefGames.Unmarshal(m, b)
}
func (m *UserDeliverPrefGames) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDeliverPrefGames.Marshal(b, m, deterministic)
}
func (dst *UserDeliverPrefGames) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDeliverPrefGames.Merge(dst, src)
}
func (m *UserDeliverPrefGames) XXX_Size() int {
	return xxx_messageInfo_UserDeliverPrefGames.Size(m)
}
func (m *UserDeliverPrefGames) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDeliverPrefGames.DiscardUnknown(m)
}

var xxx_messageInfo_UserDeliverPrefGames proto.InternalMessageInfo

func (m *UserDeliverPrefGames) GetGames() []*UserDeliverPrefGames_Game {
	if m != nil {
		return m.Games
	}
	return nil
}

func (m *UserDeliverPrefGames) GetConfirmTime() uint64 {
	if m != nil {
		return m.ConfirmTime
	}
	return 0
}

type UserDeliverPrefGames_Game struct {
	TabId                uint32                       `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Labels               []*dimension.PreferGameLabel `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UserDeliverPrefGames_Game) Reset()         { *m = UserDeliverPrefGames_Game{} }
func (m *UserDeliverPrefGames_Game) String() string { return proto.CompactTextString(m) }
func (*UserDeliverPrefGames_Game) ProtoMessage()    {}
func (*UserDeliverPrefGames_Game) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{49, 0}
}
func (m *UserDeliverPrefGames_Game) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDeliverPrefGames_Game.Unmarshal(m, b)
}
func (m *UserDeliverPrefGames_Game) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDeliverPrefGames_Game.Marshal(b, m, deterministic)
}
func (dst *UserDeliverPrefGames_Game) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDeliverPrefGames_Game.Merge(dst, src)
}
func (m *UserDeliverPrefGames_Game) XXX_Size() int {
	return xxx_messageInfo_UserDeliverPrefGames_Game.Size(m)
}
func (m *UserDeliverPrefGames_Game) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDeliverPrefGames_Game.DiscardUnknown(m)
}

var xxx_messageInfo_UserDeliverPrefGames_Game proto.InternalMessageInfo

func (m *UserDeliverPrefGames_Game) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *UserDeliverPrefGames_Game) GetLabels() []*dimension.PreferGameLabel {
	if m != nil {
		return m.Labels
	}
	return nil
}

type UserDeliverPrefGames_SetUserDeliverPrefGames struct {
	Games                []*UserDeliverPrefGames_Game `protobuf:"bytes,1,rep,name=games,proto3" json:"games,omitempty"`
	ConfirmTime          uint64                       `protobuf:"varint,2,opt,name=confirm_time,json=confirmTime,proto3" json:"confirm_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) Reset() {
	*m = UserDeliverPrefGames_SetUserDeliverPrefGames{}
}
func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) String() string {
	return proto.CompactTextString(m)
}
func (*UserDeliverPrefGames_SetUserDeliverPrefGames) ProtoMessage() {}
func (*UserDeliverPrefGames_SetUserDeliverPrefGames) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{49, 1}
}
func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserDeliverPrefGames_SetUserDeliverPrefGames.Unmarshal(m, b)
}
func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserDeliverPrefGames_SetUserDeliverPrefGames.Marshal(b, m, deterministic)
}
func (dst *UserDeliverPrefGames_SetUserDeliverPrefGames) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserDeliverPrefGames_SetUserDeliverPrefGames.Merge(dst, src)
}
func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) XXX_Size() int {
	return xxx_messageInfo_UserDeliverPrefGames_SetUserDeliverPrefGames.Size(m)
}
func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) XXX_DiscardUnknown() {
	xxx_messageInfo_UserDeliverPrefGames_SetUserDeliverPrefGames.DiscardUnknown(m)
}

var xxx_messageInfo_UserDeliverPrefGames_SetUserDeliverPrefGames proto.InternalMessageInfo

func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) GetGames() []*UserDeliverPrefGames_Game {
	if m != nil {
		return m.Games
	}
	return nil
}

func (m *UserDeliverPrefGames_SetUserDeliverPrefGames) GetConfirmTime() uint64 {
	if m != nil {
		return m.ConfirmTime
	}
	return 0
}

// 千人千面投放素材标签 leftKey为设备ID
type UserPromotionTags struct {
	DeviceId             string   `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ActivateTime         uint32   `protobuf:"varint,2,opt,name=activate_time,json=activateTime,proto3" json:"activate_time,omitempty"`
	PromotionTagList     []string `protobuf:"bytes,3,rep,name=promotion_tag_list,json=promotionTagList,proto3" json:"promotion_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPromotionTags) Reset()         { *m = UserPromotionTags{} }
func (m *UserPromotionTags) String() string { return proto.CompactTextString(m) }
func (*UserPromotionTags) ProtoMessage()    {}
func (*UserPromotionTags) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_dc5629dccb383aea, []int{50}
}
func (m *UserPromotionTags) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPromotionTags.Unmarshal(m, b)
}
func (m *UserPromotionTags) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPromotionTags.Marshal(b, m, deterministic)
}
func (dst *UserPromotionTags) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPromotionTags.Merge(dst, src)
}
func (m *UserPromotionTags) XXX_Size() int {
	return xxx_messageInfo_UserPromotionTags.Size(m)
}
func (m *UserPromotionTags) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPromotionTags.DiscardUnknown(m)
}

var xxx_messageInfo_UserPromotionTags proto.InternalMessageInfo

func (m *UserPromotionTags) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserPromotionTags) GetActivateTime() uint32 {
	if m != nil {
		return m.ActivateTime
	}
	return 0
}

func (m *UserPromotionTags) GetPromotionTagList() []string {
	if m != nil {
		return m.PromotionTagList
	}
	return nil
}

func init() {
	proto.RegisterType((*UserBasic)(nil), "rcmd.persona.tt.user.UserBasic")
	proto.RegisterType((*UserBasic_MaterialTagNamesUpdate)(nil), "rcmd.persona.tt.user.UserBasic.MaterialTagNamesUpdate")
	proto.RegisterType((*UserBasic_BusinessIdUpdate)(nil), "rcmd.persona.tt.user.UserBasic.BusinessIdUpdate")
	proto.RegisterType((*UserBasic_UserBasicUpdate)(nil), "rcmd.persona.tt.user.UserBasic.UserBasicUpdate")
	proto.RegisterType((*UserBasic_CompanionChannelEnterIncr)(nil), "rcmd.persona.tt.user.UserBasic.CompanionChannelEnterIncr")
	proto.RegisterType((*UserAuthInfo)(nil), "rcmd.persona.tt.user.UserAuthInfo")
	proto.RegisterType((*UserAuthInfo_PromotionTagListUpdate)(nil), "rcmd.persona.tt.user.UserAuthInfo.PromotionTagListUpdate")
	proto.RegisterType((*UserOnlineState)(nil), "rcmd.persona.tt.user.UserOnlineState")
	proto.RegisterType((*AccountState)(nil), "rcmd.persona.tt.user.AccountState")
	proto.RegisterType((*UserOnMicState)(nil), "rcmd.persona.tt.user.UserOnMicState")
	proto.RegisterType((*UserBlackProductionMark)(nil), "rcmd.persona.tt.user.UserBlackProductionMark")
	proto.RegisterType((*UserInferiorMark)(nil), "rcmd.persona.tt.user.UserInferiorMark")
	proto.RegisterType((*ImInfo)(nil), "rcmd.persona.tt.user.ImInfo")
	proto.RegisterType((*UserOffline)(nil), "rcmd.persona.tt.user.UserOffline")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserOffline.TfIdfEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.persona.tt.user.UserOffline.UiRoomIntentTagTf28dEntry")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.persona.tt.user.UserOffline.UiRoomIntentTagTfidf28dEntry")
	proto.RegisterType((*UserOffline_MusicLabelUpdate)(nil), "rcmd.persona.tt.user.UserOffline.MusicLabelUpdate")
	proto.RegisterType((*PlaymateUser)(nil), "rcmd.persona.tt.user.PlaymateUser")
	proto.RegisterType((*ChannelUser)(nil), "rcmd.persona.tt.user.ChannelUser")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UEnter28dTfTagIdEntry")
	proto.RegisterMapType((map[uint32]*channel.ChannelEnterDetail)(nil), "rcmd.persona.tt.user.ChannelUser.UEnterDTagIdEntry")
	proto.RegisterMapType((map[uint32]*channel.ChannelExpDetail)(nil), "rcmd.persona.tt.user.ChannelUser.UExpDTagIdEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UFilter28dTfTagIdEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UGangup28dTfTagIdEntry")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UPGamePrefPredictScoreEntry")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UPRoomNameTagPrefPredictScoreEntry")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UPRoomNameTagPrefPredictScoreV1Entry")
	proto.RegisterMapType((map[uint32]*UserRoomPref)(nil), "rcmd.persona.tt.user.ChannelUser.UPRoomPrefEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelUser.UPSelectGenderPrefEntry")
	proto.RegisterMapType((map[uint32]*UserSelectPref)(nil), "rcmd.persona.tt.user.ChannelUser.UPSelectTagPrefEntry")
	proto.RegisterType((*UserRoomPref)(nil), "rcmd.persona.tt.user.UserRoomPref")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefLevelEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefMapEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefModeEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefNumEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefRouteEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefSocialEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserRoomPref.UPRoomPrefThemeEntry")
	proto.RegisterType((*UserSelectPref)(nil), "rcmd.persona.tt.user.UserSelectPref")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefGenderEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefLevelEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefMapEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefModeEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefRouteEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefSocialEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserSelectPref.UPSelectPrefThemeEntry")
	proto.RegisterType((*UserCore)(nil), "rcmd.persona.tt.user.UserCore")
	proto.RegisterType((*UserGangupRl)(nil), "rcmd.persona.tt.user.UserGangupRl")
	proto.RegisterType((*UserGangupRl_Append)(nil), "rcmd.persona.tt.user.UserGangupRl.Append")
	proto.RegisterType((*User7DayImRl)(nil), "rcmd.persona.tt.user.User7DayImRl")
	proto.RegisterType((*User7DayImRl_Append)(nil), "rcmd.persona.tt.user.User7DayImRl.Append")
	proto.RegisterType((*User7DayImRl_User7DayImSimpleRl)(nil), "rcmd.persona.tt.user.User7DayImRl.User7DayImSimpleRl")
	proto.RegisterType((*UserMutualImRl)(nil), "rcmd.persona.tt.user.UserMutualImRl")
	proto.RegisterType((*UserMutualImRl_Append)(nil), "rcmd.persona.tt.user.UserMutualImRl.Append")
	proto.RegisterType((*UserHistoryAction)(nil), "rcmd.persona.tt.user.UserHistoryAction")
	proto.RegisterType((*UserHistoryAction_GangUp)(nil), "rcmd.persona.tt.user.UserHistoryAction.GangUp")
	proto.RegisterType((*UserHistoryAction_Im)(nil), "rcmd.persona.tt.user.UserHistoryAction.Im")
	proto.RegisterType((*UserHistoryAction_Room)(nil), "rcmd.persona.tt.user.UserHistoryAction.Room")
	proto.RegisterType((*UserHistoryAction_AppendGangUp)(nil), "rcmd.persona.tt.user.UserHistoryAction.AppendGangUp")
	proto.RegisterType((*UserHistoryAction_AppendIm)(nil), "rcmd.persona.tt.user.UserHistoryAction.AppendIm")
	proto.RegisterType((*UserHistoryAction_AppendRoom)(nil), "rcmd.persona.tt.user.UserHistoryAction.AppendRoom")
	proto.RegisterType((*UserHistoryAction_DeleteGangUp)(nil), "rcmd.persona.tt.user.UserHistoryAction.DeleteGangUp")
	proto.RegisterType((*UserHistoryAction_DeleteIm)(nil), "rcmd.persona.tt.user.UserHistoryAction.DeleteIm")
	proto.RegisterType((*UserHistoryAction_DeleteRoom)(nil), "rcmd.persona.tt.user.UserHistoryAction.DeleteRoom")
	proto.RegisterType((*UserFollowRL)(nil), "rcmd.persona.tt.user.UserFollowRL")
	proto.RegisterType((*UserFollowRL_Follow)(nil), "rcmd.persona.tt.user.UserFollowRL.Follow")
	proto.RegisterType((*UserFansRL)(nil), "rcmd.persona.tt.user.UserFansRL")
	proto.RegisterType((*UserFansRL_IsValid)(nil), "rcmd.persona.tt.user.UserFansRL.IsValid")
	proto.RegisterType((*BlackUserRL)(nil), "rcmd.persona.tt.user.BlackUserRL")
	proto.RegisterType((*BlackUserRL_Black)(nil), "rcmd.persona.tt.user.BlackUserRL.Black")
	proto.RegisterType((*UserPersonalChannel)(nil), "rcmd.persona.tt.user.UserPersonalChannel")
	proto.RegisterType((*UserPersonalChannel_UserChannelBasicUpdate)(nil), "rcmd.persona.tt.user.UserPersonalChannel.UserChannelBasicUpdate")
	proto.RegisterType((*UserChannelRL)(nil), "rcmd.persona.tt.user.UserChannelRL")
	proto.RegisterType((*UserChannelRL_Own)(nil), "rcmd.persona.tt.user.UserChannelRL.Own")
	proto.RegisterType((*ChannelLiveUserOffline)(nil), "rcmd.persona.tt.user.ChannelLiveUserOffline")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelLiveUserOffline.UPLiveAgeGroupPref14dEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelLiveUserOffline.UPLiveGenderPref14dEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.ChannelLiveUserOffline.UPLiveTagIdPref14dEntry")
	proto.RegisterType((*UserSquareOffline)(nil), "rcmd.persona.tt.user.UserSquareOffline")
	proto.RegisterType((*PublisherSquareOffline)(nil), "rcmd.persona.tt.user.PublisherSquareOffline")
	proto.RegisterType((*UserSquarePreferOffline)(nil), "rcmd.persona.tt.user.UserSquarePreferOffline")
	proto.RegisterType((*UserSquareSequenceOffline)(nil), "rcmd.persona.tt.user.UserSquareSequenceOffline")
	proto.RegisterType((*UserSquarePublisherOffline)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPClickPCnt1dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPClickPCnt3dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPThumpUpPCnt1dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPThumpUpPCnt3dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPViewPCnt1dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPViewPCnt3dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPViewTime1dEntry")
	proto.RegisterMapType((map[uint32]uint32)(nil), "rcmd.persona.tt.user.UserSquarePublisherOffline.UPViewTime3dEntry")
	proto.RegisterType((*UserChannleAccumAction)(nil), "rcmd.persona.tt.user.UserChannleAccumAction")
	proto.RegisterMapType((map[string]uint32)(nil), "rcmd.persona.tt.user.UserChannleAccumAction.GameBrowseCountAccumEntry")
	proto.RegisterType((*UserChannleAccumAction_Append)(nil), "rcmd.persona.tt.user.UserChannleAccumAction.Append")
	proto.RegisterMapType((map[string]uint32)(nil), "rcmd.persona.tt.user.UserChannleAccumAction.Append.GameBrowseCountAccumEntry")
	proto.RegisterType((*UserChannleEnterAction)(nil), "rcmd.persona.tt.user.UserChannleEnterAction")
	proto.RegisterType((*UserChannleEnterAction_UserEnterInfo)(nil), "rcmd.persona.tt.user.UserChannleEnterAction.UserEnterInfo")
	proto.RegisterType((*UserChannleEnterAction_AppendUserEnter)(nil), "rcmd.persona.tt.user.UserChannleEnterAction.AppendUserEnter")
	proto.RegisterType((*UserChannleEnterAction_AppendUserGangup)(nil), "rcmd.persona.tt.user.UserChannleEnterAction.AppendUserGangup")
	proto.RegisterType((*UserChannleEnterAction_AppendUserEnterName)(nil), "rcmd.persona.tt.user.UserChannleEnterAction.AppendUserEnterName")
	proto.RegisterType((*UserChannleEnterActionForGamecard)(nil), "rcmd.persona.tt.user.UserChannleEnterActionForGamecard")
	proto.RegisterType((*UserChannleEnterActionForGamecard_Append)(nil), "rcmd.persona.tt.user.UserChannleEnterActionForGamecard.Append")
	proto.RegisterType((*UserSingScore)(nil), "rcmd.persona.tt.user.UserSingScore")
	proto.RegisterType((*UserSingScore_SongGodAppend)(nil), "rcmd.persona.tt.user.UserSingScore.SongGodAppend")
	proto.RegisterType((*UserSingScoreV2)(nil), "rcmd.persona.tt.user.UserSingScoreV2")
	proto.RegisterMapType((map[string]uint32)(nil), "rcmd.persona.tt.user.UserSingScoreV2.ToneCountEntry")
	proto.RegisterMapType((map[string]uint32)(nil), "rcmd.persona.tt.user.UserSingScoreV2.ToneScoreEntry")
	proto.RegisterType((*UserSingScoreV2_Append)(nil), "rcmd.persona.tt.user.UserSingScoreV2.Append")
	proto.RegisterMapType((map[string]uint32)(nil), "rcmd.persona.tt.user.UserSingScoreV2.Append.ToneCountEntry")
	proto.RegisterMapType((map[string]uint32)(nil), "rcmd.persona.tt.user.UserSingScoreV2.Append.ToneScoreEntry")
	proto.RegisterType((*UserGameCard)(nil), "rcmd.persona.tt.user.UserGameCard")
	proto.RegisterType((*GameCardInfo)(nil), "rcmd.persona.tt.user.GameCardInfo")
	proto.RegisterType((*GameCardOpt)(nil), "rcmd.persona.tt.user.GameCardOpt")
	proto.RegisterType((*UserGameTags)(nil), "rcmd.persona.tt.user.UserGameTags")
	proto.RegisterType((*UserGameTags_UserGameTagsUpdate)(nil), "rcmd.persona.tt.user.UserGameTags.UserGameTagsUpdate")
	proto.RegisterType((*GameTag)(nil), "rcmd.persona.tt.user.GameTag")
	proto.RegisterType((*GameTag_Attr)(nil), "rcmd.persona.tt.user.GameTag.Attr")
	proto.RegisterType((*MobileGameList)(nil), "rcmd.persona.tt.user.MobileGameList")
	proto.RegisterType((*MobileGameListV2)(nil), "rcmd.persona.tt.user.MobileGameListV2")
	proto.RegisterType((*MyTagList)(nil), "rcmd.persona.tt.user.MyTagList")
	proto.RegisterType((*GameTabWeight)(nil), "rcmd.persona.tt.user.GameTabWeight")
	proto.RegisterType((*GameTabWeight_CancelWeightAppend)(nil), "rcmd.persona.tt.user.GameTabWeight.CancelWeightAppend")
	proto.RegisterType((*GameTabWeight_OldUserWeightAppend)(nil), "rcmd.persona.tt.user.GameTabWeight.OldUserWeightAppend")
	proto.RegisterType((*GameTabWeight_HadCanceledAppend)(nil), "rcmd.persona.tt.user.GameTabWeight.HadCanceledAppend")
	proto.RegisterType((*UserMusicPrefer)(nil), "rcmd.persona.tt.user.UserMusicPrefer")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiRoomMusicTabEnterPref1wEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiRoomMusicTabEnterPref3wEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiRoomTabDurationRate1dEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiRoomTabDurationRate1wEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiRoomTabDurationRate2wEntry")
	proto.RegisterMapType((map[uint32]float64)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiRoomTabDurationRate3dEntry")
	proto.RegisterMapType((map[uint32]*UserSongPref)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UiSongPreferEntry")
	proto.RegisterType((*UserMusicPrefer_UcusTagDurationUpdate)(nil), "rcmd.persona.tt.user.UserMusicPrefer.UcusTagDurationUpdate")
	proto.RegisterType((*UserSongPref)(nil), "rcmd.persona.tt.user.UserSongPref")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiListenSongPreferenceSong1dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiListenSongPreferenceSong1wEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiListenSongPreferenceSong3dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSingSongPreferenceScore1dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSingSongPreferenceScore1wEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSingSongPreferenceScore3dEntry")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongClickRate1dEntry")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongClickRate1wEntry")
	proto.RegisterMapType((map[string]float64)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongClickRate3dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongListenCompleteRate1dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongListenCompleteRate1wEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongListenCompleteRate3dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongListenRate1dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongListenRate1wEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongListenRate3dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongScoreEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongSingRate1dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongSingRate1wEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongSingRate3dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongSingSum1dEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongSingSum1wEntry")
	proto.RegisterMapType((map[string]float32)(nil), "rcmd.persona.tt.user.UserSongPref.UiSongSingSum3dEntry")
	proto.RegisterType((*UserMusicChannelEnterAction)(nil), "rcmd.persona.tt.user.UserMusicChannelEnterAction")
	proto.RegisterType((*UserMusicChannelEnterAction_AppendMusicChannel)(nil), "rcmd.persona.tt.user.UserMusicChannelEnterAction.AppendMusicChannel")
	proto.RegisterType((*UserInChannel)(nil), "rcmd.persona.tt.user.UserInChannel")
	proto.RegisterType((*UserAppStatus)(nil), "rcmd.persona.tt.user.UserAppStatus")
	proto.RegisterType((*UserActionStat)(nil), "rcmd.persona.tt.user.UserActionStat")
	proto.RegisterType((*UserActionStat_AddEnterChannelSet)(nil), "rcmd.persona.tt.user.UserActionStat.AddEnterChannelSet")
	proto.RegisterType((*UserActionStat_AddChannelTraces)(nil), "rcmd.persona.tt.user.UserActionStat.AddChannelTraces")
	proto.RegisterType((*UserActionStat_SetLastPreferDeliverTime)(nil), "rcmd.persona.tt.user.UserActionStat.SetLastPreferDeliverTime")
	proto.RegisterType((*UserActionStat_AddPreferDeliverTime)(nil), "rcmd.persona.tt.user.UserActionStat.AddPreferDeliverTime")
	proto.RegisterType((*UserActionStat_RecentEnterTrace)(nil), "rcmd.persona.tt.user.UserActionStat.RecentEnterTrace")
	proto.RegisterType((*UserActionStat_SetCancelPreferGameReRank)(nil), "rcmd.persona.tt.user.UserActionStat.SetCancelPreferGameReRank")
	proto.RegisterType((*UserActionStat_IncTCDeliverTimes)(nil), "rcmd.persona.tt.user.UserActionStat.IncTCDeliverTimes")
	proto.RegisterType((*UserDeliverPrefGames)(nil), "rcmd.persona.tt.user.UserDeliverPrefGames")
	proto.RegisterType((*UserDeliverPrefGames_Game)(nil), "rcmd.persona.tt.user.UserDeliverPrefGames.Game")
	proto.RegisterType((*UserDeliverPrefGames_SetUserDeliverPrefGames)(nil), "rcmd.persona.tt.user.UserDeliverPrefGames.SetUserDeliverPrefGames")
	proto.RegisterType((*UserPromotionTags)(nil), "rcmd.persona.tt.user.UserPromotionTags")
	proto.RegisterEnum("rcmd.persona.tt.user.UserOnlineState_OnlineStatus", UserOnlineState_OnlineStatus_name, UserOnlineState_OnlineStatus_value)
	proto.RegisterEnum("rcmd.persona.tt.user.UserOnMicState_OnMicStatus", UserOnMicState_OnMicStatus_name, UserOnMicState_OnMicStatus_value)
	proto.RegisterEnum("rcmd.persona.tt.user.UserOffline_MUSICLABEL", UserOffline_MUSICLABEL_name, UserOffline_MUSICLABEL_value)
	proto.RegisterEnum("rcmd.persona.tt.user.UserInChannel_ChannelType", UserInChannel_ChannelType_name, UserInChannel_ChannelType_value)
	proto.RegisterEnum("rcmd.persona.tt.user.UserAppStatus_AppStatus", UserAppStatus_AppStatus_name, UserAppStatus_AppStatus_value)
	proto.RegisterEnum("rcmd.persona.tt.user.UserActionStat_Action", UserActionStat_Action_name, UserActionStat_Action_value)
}

func init() { proto.RegisterFile("rcmd/persona/tt/user.proto", fileDescriptor_user_dc5629dccb383aea) }

var fileDescriptor_user_dc5629dccb383aea = []byte{
	// 8569 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x7d, 0x6d, 0x6c, 0x24, 0xc9,
	0x75, 0x98, 0x86, 0xdf, 0x53, 0xc3, 0x21, 0x87, 0xcd, 0x5d, 0xb2, 0x77, 0x76, 0x79, 0xbb, 0x3b,
	0xb7, 0x77, 0xb7, 0x3a, 0x9d, 0xb9, 0x47, 0x36, 0xb5, 0xc3, 0xe3, 0xdd, 0x49, 0x21, 0x67, 0x3f,
	0x8e, 0xd6, 0x72, 0x77, 0x33, 0x24, 0x57, 0x8a, 0x03, 0xa3, 0xd3, 0xec, 0x2e, 0x0e, 0xdb, 0x9c,
	0xfe, 0xb8, 0xfe, 0xe0, 0x2c, 0x0d, 0x19, 0x61, 0x64, 0x08, 0x91, 0xef, 0xa4, 0x24, 0x3a, 0xe5,
	0x12, 0xd9, 0x89, 0x90, 0xe4, 0x12, 0xd0, 0xd2, 0xd9, 0x8e, 0x6c, 0x38, 0x04, 0x8c, 0xe4, 0x47,
	0x36, 0x40, 0x70, 0x3f, 0xac, 0x38, 0x39, 0x9d, 0x23, 0x01, 0xf9, 0x10, 0xe0, 0x00, 0xc9, 0xbf,
	0x53, 0x90, 0xc4, 0x48, 0x1c, 0x0b, 0x09, 0x12, 0xd4, 0xab, 0xaa, 0xee, 0xea, 0x9e, 0x1e, 0x0e,
	0x87, 0xdc, 0x7c, 0xfc, 0xf0, 0xaf, 0x5d, 0x56, 0xbd, 0xaf, 0x7a, 0xef, 0xd5, 0xab, 0x57, 0x55,
	0xaf, 0x6b, 0x50, 0xd9, 0xd3, 0x2d, 0xe3, 0x86, 0x8b, 0x3d, 0xdf, 0xb1, 0xb5, 0x1b, 0x41, 0x70,
	0x23, 0xf4, 0xb1, 0x37, 0xeb, 0x7a, 0x4e, 0xe0, 0x48, 0xe7, 0x48, 0xdf, 0x2c, 0xeb, 0x9b, 0x0d,
	0x82, 0x59, 0xd2, 0x57, 0x96, 0x01, 0x43, 0x77, 0x2c, 0xcb, 0xb1, 0xd9, 0x3f, 0x14, 0xbe, 0x5c,
	0x16, 0x7b, 0xf6, 0xb4, 0xa6, 0x69, 0x68, 0x01, 0x4e, 0xf4, 0x71, 0x3e, 0x8e, 0x1b, 0x98, 0x8e,
	0xed, 0xb3, 0xbe, 0x99, 0xb4, 0x0c, 0xfa, 0x8e, 0x66, 0xdb, 0xb8, 0xc9, 0xba, 0x2f, 0xa7, 0xbb,
	0x0d, 0xd3, 0xc2, 0xb6, 0x6f, 0x72, 0xbe, 0x95, 0x8f, 0xf3, 0x28, 0xbf, 0xe9, 0x63, 0x6f, 0x45,
	0xf3, 0x4d, 0x5d, 0x2a, 0xa1, 0xfe, 0xd0, 0x34, 0xe4, 0xdc, 0x95, 0xdc, 0xf5, 0x62, 0x9d, 0xfc,
	0x57, 0xba, 0x80, 0x46, 0x3c, 0xdc, 0x50, 0x03, 0xd3, 0xc2, 0x72, 0x1f, 0x34, 0x0f, 0x7b, 0xb8,
	0xb1, 0x61, 0x5a, 0x98, 0x00, 0xfb, 0xf8, 0xb1, 0xdc, 0x4f, 0x81, 0x7d, 0xfc, 0x58, 0x2a, 0xa3,
	0x91, 0x2d, 0xcf, 0x0c, 0x76, 0x0c, 0x6d, 0x5f, 0x1e, 0x80, 0xe6, 0xe8, 0x6f, 0x02, 0xad, 0x35,
	0xb0, 0x3c, 0x48, 0xa1, 0xb5, 0x06, 0x96, 0x2e, 0xa2, 0xbc, 0xd6, 0xc0, 0x6a, 0xc3, 0x73, 0x42,
	0x57, 0x1e, 0xa2, 0xe0, 0x5a, 0x03, 0xdf, 0x25, 0x7f, 0x4b, 0x93, 0x68, 0x30, 0xd0, 0x1a, 0xea,
	0xbc, 0x3c, 0x0c, 0x1d, 0x03, 0x81, 0xd6, 0x98, 0xe7, 0x8d, 0x0b, 0xf2, 0xc8, 0x95, 0xdc, 0xf5,
	0x3c, 0x34, 0x2e, 0x48, 0x57, 0x51, 0x11, 0x1a, 0xd5, 0x39, 0x45, 0x75, 0xdc, 0x60, 0x4e, 0xce,
	0x43, 0x27, 0x22, 0x9d, 0x73, 0xca, 0x03, 0x37, 0x98, 0x13, 0x40, 0x6e, 0x52, 0x10, 0x24, 0x80,
	0xdc, 0x4c, 0x81, 0x00, 0x95, 0x79, 0xb9, 0x90, 0xa2, 0x32, 0x9f, 0xa6, 0x32, 0x2f, 0x8f, 0xa6,
	0xa8, 0xcc, 0xa7, 0xa9, 0x28, 0x72, 0x31, 0x45, 0x45, 0x49, 0x53, 0x51, 0xe4, 0xb1, 0x14, 0x15,
	0x45, 0xba, 0x89, 0x24, 0x4b, 0x0b, 0xb0, 0x67, 0x6a, 0x4d, 0x95, 0xc0, 0xda, 0x9a, 0x85, 0x7d,
	0xb9, 0x74, 0xa5, 0xff, 0x7a, 0x7e, 0x65, 0xe4, 0x57, 0x0e, 0x67, 0x06, 0x0c, 0x53, 0x0f, 0xea,
	0x25, 0x0e, 0xb3, 0xa1, 0x35, 0xee, 0x13, 0x08, 0xe9, 0x32, 0x2a, 0x6c, 0x85, 0xbe, 0x69, 0x63,
	0xdf, 0x57, 0x4d, 0x43, 0x9e, 0xb8, 0x92, 0xbb, 0xde, 0x5f, 0x47, 0xbc, 0x69, 0xd5, 0x90, 0x6e,
	0xa2, 0x69, 0xdd, 0xb1, 0x5c, 0xcd, 0x36, 0x1d, 0x5b, 0x65, 0x8e, 0xa2, 0x62, 0x3b, 0xc0, 0x9e,
	0x2c, 0x81, 0x9a, 0xcf, 0x47, 0xdd, 0x35, 0xda, 0x7b, 0x9b, 0x74, 0x96, 0x37, 0xd1, 0xd4, 0x5a,
	0x8a, 0xd9, 0xa6, 0x4b, 0x1c, 0x54, 0xfa, 0x74, 0xa6, 0xa8, 0x39, 0x10, 0x75, 0xf8, 0xad, 0xc3,
	0x99, 0x7e, 0x1f, 0x67, 0x48, 0xba, 0x34, 0xf0, 0xe4, 0x0f, 0xe5, 0x5c, 0x79, 0x05, 0x95, 0x56,
	0x22, 0xe1, 0x18, 0xc1, 0xeb, 0xc9, 0x31, 0x10, 0x4f, 0xec, 0x8f, 0x29, 0x09, 0x83, 0x01, 0x1a,
	0x7d, 0xe5, 0x6f, 0xf5, 0xa3, 0xf1, 0xc8, 0x7f, 0x19, 0x8d, 0x3f, 0xf1, 0xe2, 0xff, 0x0f, 0xbc,
	0x18, 0xec, 0xd3, 0x5f, 0x6e, 0xa0, 0x0b, 0xb5, 0x2c, 0x9f, 0x5a, 0xb5, 0x75, 0x4f, 0x5a, 0xe9,
	0xec, 0x8f, 0x60, 0xbc, 0x15, 0xf4, 0xd6, 0xe1, 0xcc, 0x90, 0x69, 0xeb, 0xde, 0xd6, 0x7e, 0x07,
	0xdf, 0x5c, 0x1a, 0x3a, 0x38, 0x92, 0xbf, 0xfd, 0xe5, 0xd7, 0x97, 0xae, 0x1d, 0x1c, 0xc9, 0x5f,
	0xbf, 0xff, 0x95, 0x23, 0x39, 0xf7, 0xce, 0x91, 0x8c, 0x48, 0xb4, 0x55, 0xb7, 0x88, 0x4f, 0xbc,
	0x77, 0x24, 0xe7, 0x77, 0x34, 0xcf, 0x50, 0xf7, 0x4c, 0xdc, 0xaa, 0x7c, 0x77, 0x08, 0x8d, 0x12,
	0x77, 0x59, 0x0e, 0x83, 0x9d, 0x55, 0x7b, 0xdb, 0xc9, 0xf0, 0x95, 0x31, 0xd4, 0x67, 0xba, 0xe0,
	0x25, 0xf9, 0x7a, 0x9f, 0xe9, 0x92, 0xbf, 0x03, 0x1f, 0xfc, 0x63, 0xa0, 0xde, 0x17, 0xf8, 0xd2,
	0x39, 0x34, 0xe8, 0xee, 0x38, 0x36, 0x06, 0xdf, 0xc8, 0xd7, 0xe9, 0x1f, 0x92, 0x84, 0x06, 0x4c,
	0x0b, 0x9b, 0xe0, 0x19, 0xf9, 0x3a, 0xfc, 0x1f, 0xda, 0x8c, 0x6d, 0x0d, 0xbc, 0x82, 0xb4, 0x19,
	0xdb, 0x1a, 0x99, 0xa3, 0x7a, 0xd3, 0xc4, 0x76, 0xa0, 0x06, 0xfb, 0x2e, 0x66, 0x7e, 0x81, 0x68,
	0xd3, 0xc6, 0xbe, 0x8b, 0xa5, 0xe7, 0xd0, 0x18, 0x03, 0xd8, 0xc3, 0x1e, 0x09, 0xd4, 0xe0, 0x26,
	0xc5, 0x7a, 0x91, 0xb6, 0x3e, 0xa2, 0x8d, 0x52, 0x05, 0x15, 0x0d, 0xbc, 0x67, 0xea, 0x58, 0x35,
	0x0d, 0x75, 0x07, 0x3f, 0x66, 0xfe, 0x52, 0xa0, 0x8d, 0xab, 0xc6, 0x1b, 0xf8, 0xb1, 0x34, 0x8d,
	0x86, 0x4d, 0x5f, 0xd5, 0xec, 0xc0, 0x04, 0x57, 0x19, 0xa9, 0x0f, 0x99, 0xfe, 0xb2, 0x1d, 0x98,
	0xc4, 0x67, 0x2d, 0xcd, 0xdb, 0xc5, 0x01, 0x99, 0x62, 0x05, 0xea, 0xb3, 0xb4, 0x61, 0xd5, 0x90,
	0xce, 0xa3, 0x21, 0xdd, 0x32, 0x48, 0xcf, 0x28, 0xf4, 0x0c, 0xea, 0x96, 0xb1, 0x6a, 0x10, 0x86,
	0x84, 0x58, 0x18, 0x38, 0x6a, 0xd3, 0x69, 0x98, 0x36, 0x38, 0xc5, 0x48, 0xbd, 0x60, 0xfa, 0xcb,
	0x61, 0xe0, 0xdc, 0x23, 0x4d, 0x64, 0x70, 0xee, 0x6e, 0x83, 0x5b, 0x92, 0xfb, 0x84, 0xbb, 0xdb,
	0x60, 0x16, 0x93, 0xae, 0xa0, 0x51, 0xdf, 0x52, 0x23, 0xc1, 0xe5, 0x71, 0x0a, 0xe1, 0x5b, 0xb7,
	0x98, 0xd8, 0xd2, 0x55, 0x34, 0xca, 0xba, 0x2d, 0xc7, 0xc0, 0x4d, 0xb9, 0x24, 0x0e, 0x6b, 0x8d,
	0x34, 0x49, 0xcf, 0xa0, 0x82, 0x6f, 0xec, 0xaa, 0x0d, 0xcd, 0xc2, 0x71, 0x98, 0xcb, 0xfb, 0xc6,
	0xee, 0x5d, 0xcd, 0xc2, 0x54, 0xd2, 0xa6, 0xe6, 0x07, 0x54, 0x4c, 0x35, 0xf0, 0x21, 0xb6, 0x0d,
	0xd4, 0x0b, 0xa4, 0x11, 0xe4, 0xdc, 0x80, 0x50, 0xc9, 0xa5, 0xb0, 0xb7, 0x1d, 0x79, 0x92, 0xca,
	0xc1, 0x94, 0x47, 0xfc, 0x42, 0x42, 0x03, 0x8e, 0x66, 0x1a, 0xf2, 0x39, 0x6a, 0x3b, 0xf2, 0x7f,
	0x69, 0x06, 0x21, 0xcd, 0x36, 0x3c, 0xc7, 0x04, 0xed, 0x9c, 0x87, 0x9e, 0x3c, 0x6b, 0x59, 0x35,
	0xa4, 0x4f, 0xa1, 0xfe, 0xa6, 0xa3, 0xcb, 0x53, 0x57, 0x72, 0xd7, 0x0b, 0xf3, 0x17, 0x66, 0x21,
	0x01, 0x60, 0x6b, 0xfc, 0x3d, 0x47, 0xd7, 0xc8, 0xaa, 0x4d, 0x48, 0xd7, 0x09, 0x14, 0x89, 0xf1,
	0xae, 0xe7, 0x58, 0x0e, 0x69, 0x85, 0xc8, 0xd9, 0x34, 0xfd, 0x40, 0x9e, 0x4e, 0xc7, 0xf8, 0x08,
	0x66, 0x43, 0x6b, 0xdc, 0x33, 0xfd, 0xa0, 0xfc, 0x79, 0x34, 0xf5, 0x30, 0xd5, 0x16, 0x87, 0xe2,
	0x0c, 0x8a, 0xe9, 0x50, 0x9c, 0x26, 0x08, 0xf3, 0xe7, 0x3f, 0x7c, 0xfd, 0xf5, 0xa5, 0xa9, 0x83,
	0x23, 0xf9, 0xef, 0xf0, 0xf9, 0x93, 0x87, 0xf9, 0xa3, 0x85, 0xc1, 0x4e, 0xe5, 0x9f, 0xe4, 0x68,
	0x80, 0x7d, 0x60, 0x37, 0x4d, 0x1b, 0xaf, 0x07, 0x84, 0xd5, 0x45, 0x94, 0x37, 0x7d, 0xd5, 0x81,
	0x16, 0x36, 0x75, 0x46, 0x4c, 0x9f, 0x42, 0x48, 0xcf, 0xa3, 0x71, 0x41, 0xfd, 0xba, 0x19, 0xec,
	0xb3, 0xc9, 0x54, 0x8c, 0x0c, 0x50, 0x33, 0x83, 0x7d, 0xe2, 0x0b, 0x00, 0xe7, 0xf8, 0x74, 0x2a,
	0xd0, 0x08, 0x8c, 0x48, 0xdb, 0x03, 0x9f, 0x4c, 0x85, 0xca, 0x0b, 0x68, 0x34, 0xe6, 0x1a, 0xfa,
	0x52, 0x01, 0x0d, 0x3f, 0xd8, 0xde, 0x26, 0x0d, 0xa5, 0x4f, 0x48, 0x08, 0x0d, 0xd1, 0xce, 0x52,
	0x6e, 0xe9, 0x99, 0x83, 0x23, 0xf9, 0x1d, 0x2e, 0xfb, 0x04, 0xc8, 0x4e, 0x85, 0x53, 0x7d, 0x22,
	0x6f, 0x65, 0x15, 0x8d, 0x2e, 0xeb, 0xba, 0x13, 0xda, 0x01, 0x95, 0x7f, 0x06, 0x21, 0xd3, 0x57,
	0x43, 0x3b, 0xf4, 0x43, 0xad, 0xc9, 0x06, 0x90, 0x37, 0xfd, 0x4d, 0xda, 0xb0, 0x54, 0x3e, 0x38,
	0x92, 0xbf, 0xc1, 0xc9, 0x15, 0x35, 0x8a, 0xc9, 0x48, 0xbd, 0x89, 0xc6, 0xa8, 0x36, 0xd6, 0x4c,
	0x9d, 0x12, 0xbb, 0x80, 0x46, 0x40, 0x19, 0x96, 0xa9, 0xb3, 0x31, 0x0c, 0x13, 0x5d, 0x58, 0xa6,
	0x5e, 0xb9, 0x86, 0x0a, 0x11, 0x60, 0xe8, 0x83, 0xc8, 0xdb, 0xdb, 0x6b, 0xa6, 0x5e, 0xfa, 0x84,
	0x94, 0x47, 0x83, 0xd0, 0xc5, 0xa4, 0xff, 0xab, 0x69, 0xe9, 0x55, 0xcb, 0xd4, 0x19, 0xcb, 0x3f,
	0x8f, 0xa6, 0x61, 0x85, 0x6b, 0x6a, 0xfa, 0xee, 0x43, 0xcf, 0x31, 0x42, 0x9d, 0xd8, 0x6f, 0x4d,
	0xf3, 0x76, 0xa5, 0x57, 0xd0, 0xa4, 0xe9, 0xab, 0x5b, 0xa4, 0x47, 0x75, 0xa3, 0x2e, 0x16, 0x3c,
	0xf3, 0x3f, 0x59, 0x19, 0x7a, 0x71, 0xe0, 0xe5, 0x4f, 0xbc, 0x9c, 0xab, 0x4f, 0x98, 0x7e, 0x0a,
	0x7d, 0xe9, 0x93, 0x07, 0x47, 0xf2, 0x93, 0xdf, 0x7b, 0xb2, 0xc0, 0xf8, 0x96, 0x69, 0xc4, 0x4c,
	0x51, 0x52, 0x49, 0x54, 0xa8, 0x68, 0xa8, 0x44, 0x04, 0x58, 0xb5, 0xb7, 0xb1, 0x67, 0x3a, 0x1e,
	0x70, 0x7e, 0x11, 0x15, 0x4c, 0x9f, 0x4c, 0x1e, 0x68, 0x6a, 0xe7, 0x88, 0x4c, 0x9f, 0xc3, 0x2f,
	0x5d, 0x3d, 0x38, 0x92, 0xbf, 0xf9, 0x51, 0xc4, 0x4a, 0x02, 0x56, 0x1c, 0x95, 0xb2, 0xd8, 0x47,
	0x43, 0xab, 0x16, 0x4c, 0x3c, 0xa2, 0x4e, 0x4b, 0x05, 0x95, 0x33, 0xcb, 0x0c, 0x9b, 0x56, 0x8d,
	0xfc, 0x09, 0x66, 0xb3, 0x54, 0x0b, 0x5b, 0x5b, 0xd8, 0xf3, 0xe5, 0xbe, 0x2b, 0xfd, 0x60, 0x36,
	0x6b, 0x8d, 0x36, 0x90, 0x39, 0xed, 0x61, 0xdd, 0xf1, 0x0c, 0xba, 0xce, 0x33, 0x7f, 0xa2, 0x4d,
	0x64, 0xa9, 0x5f, 0x3a, 0x77, 0x70, 0x24, 0xbf, 0xcb, 0x15, 0x3d, 0x6c, 0x5a, 0x30, 0xf7, 0x2b,
	0x7f, 0x54, 0x40, 0x05, 0x30, 0x29, 0x75, 0x2d, 0x12, 0x5e, 0x5c, 0x0f, 0x6f, 0xc3, 0x14, 0x8a,
	0x56, 0x86, 0x3c, 0x69, 0xda, 0xd0, 0x1a, 0x30, 0xcd, 0x25, 0xa1, 0x5f, 0xdd, 0x0d, 0x9d, 0xa6,
	0xc9, 0xb3, 0x8a, 0xf1, 0x08, 0xec, 0x73, 0xd0, 0x4c, 0x44, 0xde, 0xf6, 0x4c, 0x6c, 0x1b, 0xaa,
	0x1d, 0x5a, 0x4c, 0xa4, 0x3c, 0x6d, 0xb9, 0x1f, 0x5a, 0x64, 0x0e, 0xc4, 0xdd, 0xea, 0x1c, 0x4b,
	0x37, 0x50, 0x04, 0x30, 0x47, 0xa2, 0xb1, 0x69, 0x01, 0x32, 0xcd, 0x39, 0x06, 0x4d, 0x8b, 0x20,
	0xca, 0xa0, 0x25, 0x8a, 0x44, 0x93, 0x8e, 0x21, 0xe8, 0x98, 0x23, 0xfa, 0x73, 0x1d, 0x3f, 0x00,
	0x14, 0xba, 0xba, 0x0c, 0x93, 0xbf, 0x09, 0xd2, 0x0c, 0x42, 0x74, 0x52, 0x1a, 0xda, 0xbe, 0xcf,
	0x96, 0x95, 0x3c, 0xb4, 0xdc, 0xd2, 0xf6, 0x7d, 0x08, 0xbd, 0xa1, 0x07, 0x71, 0x4a, 0x0d, 0x1b,
	0x3a, 0xac, 0x28, 0xb9, 0x7a, 0x81, 0xb7, 0x6d, 0x36, 0x74, 0xe9, 0x3a, 0x2a, 0x45, 0x20, 0xda,
	0x5e, 0x03, 0xc0, 0x10, 0x80, 0x8d, 0xf1, 0xf6, 0xe5, 0xbd, 0x06, 0x81, 0x7c, 0x01, 0x8d, 0x47,
	0x90, 0x4c, 0x45, 0x85, 0x24, 0x20, 0xd3, 0xd0, 0x2c, 0x9a, 0x4c, 0x90, 0x64, 0xc0, 0xa3, 0x00,
	0x3c, 0x21, 0x50, 0x65, 0xf0, 0xcf, 0xa1, 0x88, 0x82, 0xea, 0x5b, 0x5a, 0xb3, 0x09, 0x0b, 0x51,
	0xae, 0x5e, 0xe4, 0xad, 0xeb, 0xa4, 0x51, 0x7a, 0x09, 0x49, 0x09, 0xb2, 0x14, 0x74, 0x0c, 0x40,
	0x4b, 0x02, 0x55, 0x0a, 0x5d, 0x41, 0x45, 0x66, 0x07, 0x4f, 0x0b, 0xb0, 0x3a, 0x07, 0x0b, 0x53,
	0xae, 0x5e, 0xa0, 0x8d, 0x75, 0x2d, 0xc0, 0x73, 0x52, 0x19, 0xe5, 0x4d, 0x8b, 0xf7, 0x97, 0xa0,
	0x7f, 0xd8, 0xb4, 0x68, 0x5f, 0x0d, 0x0d, 0x05, 0xdb, 0xaa, 0x69, 0x6c, 0xcb, 0x13, 0x57, 0xfa,
	0xaf, 0x17, 0xe6, 0x5f, 0x9a, 0xcd, 0xda, 0xfe, 0xcd, 0x0a, 0x6e, 0x36, 0xbb, 0xb1, 0xbd, 0x6a,
	0x6c, 0xdf, 0xb6, 0x03, 0x6f, 0xbf, 0x3e, 0x18, 0x90, 0xff, 0x13, 0x91, 0x61, 0x66, 0xf0, 0x44,
	0xa8, 0x89, 0xf7, 0x70, 0x93, 0x25, 0xe6, 0x25, 0xd2, 0xc3, 0x56, 0xd1, 0x7b, 0xa4, 0x5d, 0x5a,
	0x40, 0xd3, 0x3b, 0x9a, 0xaf, 0xe2, 0xc7, 0x2e, 0x05, 0x54, 0x6f, 0x46, 0xeb, 0xee, 0x24, 0xa0,
	0x4c, 0xee, 0x68, 0xfe, 0xed, 0xc7, 0x2e, 0x40, 0xdf, 0xe4, 0x0b, 0xf0, 0x25, 0x44, 0x52, 0x34,
	0x95, 0x09, 0x4b, 0x17, 0xb7, 0x91, 0x40, 0x6b, 0x80, 0x34, 0x3c, 0xab, 0xa3, 0xbd, 0xea, 0xde,
	0x1c, 0x5b, 0xe3, 0x10, 0x07, 0x78, 0x34, 0x27, 0x05, 0xe8, 0x42, 0x68, 0xaa, 0x9e, 0xe3, 0x90,
	0xd9, 0x13, 0x40, 0x1e, 0x43, 0x31, 0xe6, 0x17, 0x0d, 0x79, 0x0a, 0x06, 0xff, 0x6a, 0xf7, 0xc1,
	0x6f, 0x9a, 0x75, 0xc7, 0xb1, 0x56, 0x81, 0xc0, 0x06, 0x21, 0x3e, 0xbf, 0x68, 0x50, 0x5d, 0x9c,
	0x0b, 0x33, 0xba, 0xa4, 0x5f, 0x40, 0x97, 0x32, 0xb9, 0x12, 0x31, 0x09, 0xe3, 0x69, 0x60, 0xfc,
	0x99, 0x53, 0x30, 0x36, 0x8d, 0x98, 0xf7, 0x74, 0x98, 0xdd, 0x4b, 0x22, 0x8b, 0x15, 0xfa, 0xa6,
	0xae, 0x36, 0xb5, 0x2d, 0xdc, 0x94, 0x65, 0xb2, 0xa6, 0xd6, 0x11, 0x34, 0xdd, 0x23, 0x2d, 0xe5,
	0x45, 0x84, 0x62, 0x7b, 0x92, 0x9c, 0x72, 0x17, 0xef, 0xf3, 0x9c, 0x72, 0x17, 0xef, 0x93, 0x9c,
	0x71, 0x4f, 0x6b, 0x86, 0x34, 0x4c, 0xe4, 0xea, 0xf4, 0x8f, 0xa5, 0xbe, 0xc5, 0x5c, 0xf9, 0x2e,
	0xba, 0xd0, 0x51, 0x19, 0x22, 0xa1, 0x7c, 0x06, 0xa1, 0xbc, 0x48, 0xe8, 0xa7, 0xd1, 0xa5, 0xe3,
	0x06, 0xd7, 0x13, 0xad, 0x5b, 0xa8, 0xb4, 0x16, 0x0d, 0x2e, 0xde, 0x98, 0x89, 0x3a, 0x48, 0xe5,
	0x15, 0x82, 0x32, 0x20, 0xa3, 0xf8, 0x77, 0x6f, 0xbd, 0x5e, 0xf9, 0xc5, 0x1c, 0x42, 0x6b, 0x9b,
	0xeb, 0xab, 0xb5, 0x7b, 0xcb, 0x2b, 0xb7, 0xef, 0x91, 0xd5, 0xfb, 0x16, 0xde, 0xd6, 0xc2, 0x66,
	0x50, 0xfa, 0x84, 0x34, 0x81, 0x8a, 0xb7, 0x1f, 0xeb, 0xb8, 0x49, 0x17, 0x24, 0xec, 0x95, 0x72,
	0xd2, 0x18, 0x42, 0xeb, 0x8e, 0x6e, 0x6a, 0xcd, 0xcf, 0x99, 0x76, 0xa3, 0xd4, 0x27, 0x4d, 0xa2,
	0xf1, 0x9a, 0x03, 0x63, 0xa9, 0x39, 0xb6, 0x1f, 0x5a, 0xd8, 0x2b, 0xf5, 0x13, 0xbc, 0x55, 0xcb,
	0xf5, 0xb0, 0x6e, 0xfa, 0x98, 0xd8, 0xb5, 0x34, 0x40, 0xf0, 0xee, 0x3a, 0x8e, 0xb1, 0x6e, 0xda,
	0x8d, 0x26, 0x2e, 0x0d, 0x4a, 0x45, 0x94, 0x5f, 0xd1, 0x8c, 0xda, 0x8e, 0xe3, 0x85, 0x7e, 0x69,
	0x68, 0xe9, 0x02, 0x59, 0x7c, 0x78, 0xd0, 0x1f, 0xa5, 0xab, 0x2b, 0x75, 0x86, 0xca, 0x57, 0xfb,
	0xd1, 0xe8, 0xc3, 0xa6, 0xb6, 0x4f, 0xb6, 0xa7, 0x84, 0x98, 0xf4, 0x12, 0x9a, 0x0c, 0x55, 0x57,
	0xf5, 0xb0, 0x4e, 0xe6, 0x95, 0xe3, 0x87, 0x1e, 0x56, 0xab, 0x7c, 0x09, 0x18, 0x0f, 0x1f, 0xd6,
	0xb1, 0x7e, 0x9b, 0xb5, 0x57, 0x0d, 0xe9, 0x79, 0x54, 0xe2, 0xd0, 0x7a, 0xd3, 0xd4, 0x77, 0x09,
	0x28, 0x5d, 0x06, 0x46, 0x01, 0xb4, 0x46, 0x1a, 0xab, 0x86, 0x34, 0x8b, 0xce, 0x25, 0xe0, 0x20,
	0x84, 0x54, 0x0d, 0x58, 0x0d, 0x72, 0xf5, 0x52, 0x0c, 0x4b, 0x3a, 0xaa, 0x86, 0x74, 0x05, 0x15,
	0x39, 0xbc, 0x69, 0x11, 0x40, 0xba, 0x2a, 0xe4, 0x01, 0x70, 0xd5, 0xaa, 0x1a, 0xd2, 0x75, 0x34,
	0x11, 0x43, 0x70, 0x72, 0x83, 0x34, 0x0c, 0x32, 0x28, 0x46, 0xeb, 0x32, 0x2a, 0x84, 0xa6, 0xa1,
	0x6e, 0x3b, 0xcd, 0xa6, 0xd3, 0xaa, 0xb2, 0xa5, 0x02, 0x85, 0xa6, 0x71, 0x87, 0xb6, 0x48, 0xcf,
	0xa2, 0x62, 0x0c, 0x80, 0x8d, 0x2a, 0x5b, 0x33, 0x46, 0x23, 0x10, 0x6c, 0x54, 0xc9, 0xca, 0x10,
	0x03, 0xcd, 0x2f, 0xb2, 0xa5, 0xa3, 0x10, 0xc1, 0xcc, 0x2f, 0x92, 0xb0, 0x2c, 0xd2, 0x99, 0x5f,
	0x84, 0xe5, 0xa3, 0x58, 0x2f, 0x0a, 0x84, 0xe6, 0x17, 0x21, 0xb5, 0xfa, 0xe5, 0x28, 0xb5, 0x72,
	0x99, 0xf2, 0x55, 0x62, 0x96, 0xca, 0x1f, 0x4e, 0xa2, 0x02, 0x8b, 0x53, 0x60, 0x8d, 0x6b, 0x68,
	0x2c, 0x54, 0xb7, 0x3c, 0xa7, 0xe5, 0xe3, 0x44, 0x3e, 0x30, 0x1a, 0xae, 0x40, 0x23, 0x4d, 0x0a,
	0x2a, 0x44, 0x5b, 0x54, 0xff, 0x14, 0xa8, 0x8f, 0x09, 0x07, 0x2a, 0xa5, 0x30, 0x57, 0xd0, 0x28,
	0x87, 0x21, 0x7a, 0x61, 0x9a, 0x47, 0x14, 0x84, 0x44, 0x70, 0x69, 0x93, 0xf0, 0x22, 0xb1, 0xd4,
	0xe0, 0xeb, 0xfe, 0x00, 0x84, 0x94, 0xb9, 0xec, 0x90, 0x22, 0x88, 0x39, 0xbb, 0x79, 0xfb, 0xb1,
	0x7b, 0x0b, 0xd6, 0x7c, 0x1a, 0x45, 0x50, 0x18, 0x35, 0x48, 0x7f, 0x96, 0xb8, 0x08, 0x6c, 0x68,
	0x63, 0xc2, 0x83, 0x40, 0x58, 0x39, 0x09, 0x61, 0x82, 0x28, 0x92, 0x1e, 0x0d, 0x85, 0x26, 0x3a,
	0x72, 0x4a, 0x9c, 0x8e, 0x7c, 0x88, 0x8d, 0x1c, 0x80, 0x84, 0x91, 0x07, 0x58, 0xe3, 0x19, 0x15,
	0xdb, 0x6f, 0x86, 0x1b, 0x58, 0x63, 0x49, 0xd5, 0x33, 0xa8, 0xc0, 0x20, 0x40, 0x35, 0x23, 0xa0,
	0x9a, 0x3c, 0x00, 0x80, 0x66, 0x36, 0x98, 0x37, 0x92, 0xd8, 0x4b, 0xb2, 0x1b, 0x39, 0x7f, 0x62,
	0xc5, 0x3c, 0x24, 0xe1, 0xe8, 0xa1, 0x87, 0xb7, 0xb9, 0x62, 0xa2, 0x06, 0xe9, 0x73, 0x68, 0x9c,
	0x50, 0xf5, 0x71, 0x13, 0xeb, 0x01, 0xa5, 0x8b, 0x60, 0xdf, 0x74, 0xad, 0x73, 0x0c, 0x5f, 0x07,
	0x60, 0x82, 0x4e, 0x26, 0x58, 0xfc, 0x17, 0x9b, 0x60, 0x24, 0x27, 0x03, 0x29, 0x6d, 0xb2, 0x31,
	0xdc, 0xc3, 0x3a, 0x3b, 0xfc, 0x28, 0x85, 0x80, 0xe2, 0x38, 0xd6, 0x7d, 0xcd, 0xc2, 0x8f, 0xb0,
	0x2e, 0x19, 0x74, 0x9a, 0x33, 0xe6, 0xc4, 0x2c, 0x20, 0xc0, 0x28, 0x0c, 0xec, 0xe6, 0x49, 0x06,
	0x46, 0x99, 0x6f, 0x68, 0x8d, 0x78, 0x74, 0xe3, 0x61, 0xb2, 0x55, 0xb2, 0xd0, 0x94, 0xc0, 0xa5,
	0x81, 0x6d, 0x03, 0x7b, 0x94, 0x51, 0x11, 0x18, 0xbd, 0x72, 0x72, 0x46, 0x77, 0x01, 0x39, 0xe6,
	0x25, 0x85, 0x6d, 0x1d, 0xd2, 0xcf, 0xa3, 0x8b, 0x84, 0x1d, 0xec, 0x8a, 0x41, 0x17, 0xae, 0x87,
	0xc9, 0x1e, 0x52, 0xf5, 0x75, 0xc7, 0xc3, 0xf2, 0x18, 0xf0, 0x7c, 0xfd, 0x24, 0x3c, 0xc9, 0x36,
	0x9a, 0x90, 0x7c, 0x48, 0x09, 0xac, 0x13, 0x7c, 0xca, 0x77, 0x2a, 0xcc, 0xec, 0x94, 0xbe, 0x9e,
	0x43, 0xd7, 0x22, 0x27, 0x01, 0xf5, 0x73, 0xa5, 0xa6, 0xa4, 0x18, 0x07, 0x29, 0x6e, 0x9d, 0xd4,
	0x77, 0x88, 0xc1, 0x98, 0x3a, 0xdb, 0x85, 0x99, 0x09, 0x8f, 0x83, 0x91, 0xbe, 0x99, 0x43, 0x2f,
	0x9c, 0x44, 0x26, 0x92, 0xe6, 0x94, 0x40, 0xac, 0x3b, 0x67, 0x14, 0xeb, 0xd1, 0x1c, 0x15, 0xec,
	0x72, 0x78, 0x3c, 0x94, 0xb4, 0x83, 0xce, 0xf3, 0x89, 0x3b, 0xbf, 0x68, 0x90, 0xe4, 0x89, 0x85,
	0x06, 0x9a, 0x3c, 0x56, 0x4f, 0x1a, 0x1a, 0xe6, 0x17, 0x8d, 0x8d, 0x6d, 0x21, 0x3c, 0x94, 0xc2,
	0x54, 0xb3, 0xb4, 0x4b, 0x7c, 0xb0, 0xa1, 0xd9, 0x8d, 0xd0, 0x4d, 0xb1, 0x92, 0x80, 0xd5, 0xe2,
	0x09, 0x58, 0xdd, 0x05, 0xf4, 0x34, 0xaf, 0x89, 0x30, 0xdd, 0x4e, 0x99, 0x6d, 0x9b, 0xcd, 0xf6,
	0x71, 0x4d, 0x9e, 0x98, 0xd9, 0x1d, 0x40, 0x6f, 0x67, 0x96, 0x6e, 0x2f, 0xef, 0xa0, 0xf1, 0x54,
	0xe0, 0xcd, 0x48, 0xbb, 0x3e, 0x2b, 0x66, 0x38, 0x85, 0xf9, 0x4f, 0xb6, 0x09, 0xc0, 0x2f, 0x4b,
	0xf8, 0x89, 0xe2, 0x63, 0xf7, 0x16, 0x0e, 0x34, 0xb3, 0x29, 0x26, 0x43, 0x4d, 0x34, 0xd1, 0x16,
	0x89, 0x33, 0x78, 0x2d, 0x27, 0x79, 0x7d, 0xaa, 0x2b, 0x2f, 0x20, 0xd9, 0xc6, 0x4d, 0x43, 0xe3,
	0xa9, 0xb8, 0x99, 0xc1, 0x6b, 0x31, 0xc9, 0xab, 0xd2, 0x39, 0x66, 0x72, 0x4a, 0x22, 0x8b, 0x1d,
	0x74, 0x2e, 0x2b, 0x82, 0x65, 0xf0, 0x59, 0x4a, 0xf2, 0x39, 0x59, 0x6c, 0x16, 0x38, 0xdd, 0x46,
	0xd3, 0x1d, 0x42, 0x58, 0x4f, 0x39, 0xf2, 0x2a, 0xba, 0x78, 0x4c, 0x54, 0xea, 0x96, 0xd9, 0x26,
	0x48, 0x3d, 0x44, 0x95, 0xee, 0xa1, 0xa5, 0x27, 0x8a, 0x75, 0x74, 0xed, 0x24, 0x51, 0xa1, 0x27,
	0x9a, 0x35, 0x74, 0x3e, 0x73, 0x86, 0xf7, 0xa4, 0xb5, 0x5b, 0x68, 0x2a, 0x7b, 0xee, 0xf6, 0x4c,
	0x25, 0x73, 0x52, 0xf6, 0x42, 0x05, 0x92, 0xf0, 0x3f, 0x8a, 0x92, 0x70, 0xbe, 0xc1, 0x85, 0xac,
	0xef, 0xd7, 0xf2, 0xf4, 0x44, 0x3e, 0x4a, 0x0d, 0xb6, 0x90, 0x94, 0x48, 0x38, 0xe0, 0x20, 0x18,
	0xf6, 0x1b, 0x1d, 0x17, 0x67, 0x11, 0x5f, 0x48, 0x3b, 0xd6, 0x1c, 0x83, 0xad, 0x15, 0x63, 0x61,
	0xa2, 0x51, 0xda, 0x61, 0x29, 0x79, 0xc4, 0xc3, 0x87, 0x9d, 0x07, 0x9c, 0x29, 0x75, 0x0c, 0x54,
	0x1d, 0xb8, 0xd0, 0x4d, 0x0b, 0x8f, 0xc0, 0xa9, 0x66, 0x49, 0x65, 0xa9, 0x7a, 0xc4, 0x89, 0x9e,
	0x03, 0x11, 0x36, 0x9f, 0xee, 0x89, 0xcd, 0xfd, 0xd0, 0xa2, 0x3c, 0x8a, 0xa1, 0xd8, 0x26, 0x61,
	0xb6, 0x67, 0x89, 0x18, 0x04, 0x3b, 0xd8, 0xc2, 0x2c, 0x7d, 0xad, 0xf6, 0xc4, 0x62, 0x83, 0x60,
	0x46, 0xd9, 0x4c, 0xa2, 0xb5, 0x9d, 0x8d, 0xe7, 0x84, 0x01, 0x66, 0xc9, 0x6c, 0x6f, 0x6c, 0xea,
	0x04, 0xb3, 0x8d, 0x0d, 0xb4, 0xb6, 0xab, 0xcb, 0xd2, 0x5c, 0x79, 0xe8, 0x14, 0xea, 0x5a, 0xd3,
	0xdc, 0x36, 0x75, 0xad, 0x69, 0x6e, 0xfb, 0x38, 0xe8, 0x29, 0xcb, 0xf0, 0x29, 0xc6, 0x01, 0x27,
	0x2b, 0x6d, 0xe3, 0x80, 0xd6, 0xf2, 0x32, 0x9a, 0xcc, 0xf0, 0xc3, 0x9e, 0x66, 0x1e, 0x09, 0x02,
	0x59, 0x4e, 0xd6, 0x13, 0x91, 0x3f, 0x85, 0xa4, 0x76, 0x17, 0xea, 0x89, 0xc2, 0x0a, 0x59, 0x2d,
	0xda, 0x3d, 0xe4, 0xf4, 0x34, 0x62, 0xf3, 0x9f, 0x7e, 0x24, 0xdc, 0xba, 0xa7, 0x97, 0x22, 0x36,
	0x5e, 0x2f, 0x34, 0x2a, 0xff, 0x2b, 0x4f, 0xcf, 0xff, 0x85, 0xdd, 0xc7, 0xcf, 0xd1, 0x58, 0x22,
	0x6c, 0x65, 0xc4, 0x88, 0xb5, 0x74, 0x92, 0x35, 0x33, 0x4a, 0xf4, 0x93, 0x51, 0xab, 0x14, 0xa6,
	0x9a, 0x25, 0x37, 0xb1, 0xa7, 0x68, 0x8f, 0x5c, 0xaf, 0xf5, 0xcc, 0x4d, 0x8c, 0x5e, 0x52, 0xd8,
	0xd6, 0x91, 0xc5, 0x91, 0x6e, 0x65, 0x58, 0x10, 0xeb, 0x9d, 0x23, 0xcd, 0x04, 0x32, 0x38, 0xd2,
	0x0e, 0xc9, 0x22, 0xd9, 0x71, 0x92, 0xa3, 0x18, 0xd2, 0x5e, 0xed, 0x99, 0xa1, 0x10, 0xd6, 0x26,
	0xc2, 0x74, 0x7b, 0x16, 0x3b, 0x31, 0xb4, 0xf5, 0xce, 0x4e, 0x08, 0x6f, 0x09, 0x76, 0x34, 0xc0,
	0x35, 0x12, 0x7b, 0xcf, 0x54, 0x88, 0x7b, 0xa5, 0x77, 0x67, 0xe1, 0x61, 0x6e, 0x3c, 0x4c, 0xb6,
	0x66, 0x8d, 0x4b, 0x0c, 0x75, 0xbd, 0x8f, 0x4b, 0x08, 0x77, 0x89, 0x71, 0xd1, 0x80, 0x07, 0xd1,
	0x2a, 0xc3, 0x89, 0x7b, 0x9a, 0xa1, 0x42, 0xbe, 0x78, 0x96, 0xa0, 0x97, 0x22, 0x23, 0x38, 0x5c,
	0xcf, 0xa9, 0x4f, 0xa6, 0x1b, 0x9d, 0x85, 0xca, 0x29, 0xa3, 0xdf, 0x4a, 0x9c, 0xb3, 0x9f, 0x3a,
	0xfe, 0xa5, 0x24, 0x39, 0x65, 0x04, 0xfc, 0x1b, 0x7d, 0x68, 0x84, 0x78, 0x49, 0x8d, 0x6c, 0xb2,
	0xaf, 0xa2, 0x51, 0x7a, 0xe2, 0x97, 0x38, 0xa0, 0x2b, 0xd0, 0xb6, 0xe8, 0xd2, 0x0e, 0xee, 0xa3,
	0xc4, 0xc3, 0xb9, 0x3c, 0x69, 0xa1, 0xdd, 0xb3, 0x68, 0x92, 0x1d, 0x61, 0xb1, 0xa4, 0x90, 0xc2,
	0xd1, 0x9b, 0xb2, 0x09, 0xe8, 0x62, 0x1b, 0x26, 0x0a, 0x2f, 0x5e, 0x0f, 0x0e, 0x24, 0xaf, 0x07,
	0x27, 0xd1, 0xa0, 0xee, 0xd2, 0x13, 0x36, 0xb8, 0xb3, 0xd7, 0xdd, 0x55, 0x43, 0x9a, 0x46, 0xc3,
	0xfa, 0x0e, 0xbd, 0x60, 0xa6, 0x65, 0x18, 0x43, 0xfa, 0x0e, 0xd4, 0x59, 0x5c, 0x45, 0xc5, 0xa0,
	0xa1, 0xb2, 0x3e, 0x82, 0x35, 0xcc, 0xee, 0x3a, 0x1a, 0x35, 0x00, 0x58, 0x85, 0xaa, 0x21, 0x0b,
	0x1b, 0xa6, 0x46, 0x7a, 0x69, 0xad, 0xce, 0x30, 0xfc, 0xbd, 0x6a, 0xc0, 0x6d, 0xf9, 0xb7, 0xa2,
	0xdb, 0x72, 0x38, 0x47, 0x80, 0x6c, 0xf6, 0xb7, 0x73, 0x34, 0x9b, 0xa5, 0x89, 0x77, 0xbd, 0x29,
	0x2d, 0xa2, 0x12, 0xdb, 0x7f, 0x07, 0xa6, 0x85, 0xe3, 0x3b, 0xf9, 0xe2, 0xca, 0xd8, 0x3b, 0x87,
	0x33, 0x03, 0xe4, 0xef, 0xdf, 0x3c, 0x9c, 0x39, 0xf8, 0xda, 0x83, 0xfa, 0x18, 0x85, 0xdb, 0x30,
	0x2d, 0x0c, 0x37, 0xfd, 0x35, 0x34, 0xb4, 0xec, 0xba, 0xd8, 0x36, 0xa4, 0xb9, 0x8e, 0x34, 0xe0,
	0xfc, 0x5d, 0x33, 0x8c, 0x34, 0x32, 0x9c, 0xc1, 0xbf, 0xfb, 0x5e, 0x0d, 0xaa, 0x62, 0xbe, 0xf6,
	0x88, 0xc9, 0x39, 0x06, 0xa7, 0xdf, 0x8c, 0x94, 0xd7, 0xfc, 0x95, 0x23, 0x79, 0x70, 0x5b, 0xd3,
	0x71, 0x50, 0x79, 0xbb, 0x9f, 0x4a, 0x5d, 0xbd, 0xa5, 0xed, 0xaf, 0x5a, 0xf5, 0xa6, 0xf4, 0x32,
	0x1a, 0x35, 0xad, 0xe3, 0x25, 0xfe, 0xaf, 0xd7, 0xea, 0xc8, 0xb4, 0x38, 0x43, 0xa9, 0xca, 0x6e,
	0xf3, 0x19, 0x1a, 0x35, 0xf4, 0xca, 0xf9, 0xf7, 0x0f, 0x67, 0x26, 0x44, 0x4a, 0x5f, 0x24, 0x40,
	0xf4, 0x92, 0x7f, 0x15, 0x90, 0xa5, 0x1b, 0x82, 0x41, 0xc1, 0xea, 0x2b, 0xe7, 0xde, 0x3f, 0x9c,
	0x29, 0x25, 0x91, 0xb0, 0x1d, 0x99, 0xb9, 0xfc, 0xb3, 0x91, 0x5e, 0x3e, 0x99, 0x29, 0x65, 0xa4,
	0x13, 0x51, 0xbc, 0x2b, 0x59, 0xe2, 0x89, 0x72, 0x80, 0xc6, 0xfe, 0xf8, 0xbd, 0x5a, 0xf9, 0xcf,
	0x20, 0x29, 0x56, 0xc5, 0xba, 0x69, 0xb9, 0x4d, 0x5c, 0x6f, 0xb6, 0xe1, 0xe7, 0xd2, 0xf8, 0x09,
	0xc7, 0xec, 0x4b, 0x38, 0x26, 0x90, 0xfe, 0xc9, 0x7b, 0xb5, 0xa5, 0xe7, 0x0e, 0x8e, 0xe4, 0xbf,
	0xc4, 0x8d, 0x31, 0x0e, 0xc6, 0xa8, 0x1a, 0xda, 0x3e, 0x21, 0x2a, 0x5a, 0xe3, 0xdb, 0x39, 0x9a,
	0x63, 0xac, 0x85, 0x41, 0xa8, 0x35, 0x4f, 0x67, 0x8f, 0xf2, 0xab, 0xa7, 0xd0, 0x12, 0x08, 0xfa,
	0xa3, 0xbf, 0x5b, 0x5b, 0x7a, 0xe1, 0xe0, 0x48, 0xfe, 0xcb, 0x8f, 0x12, 0x15, 0x09, 0x16, 0x08,
	0x93, 0x16, 0xf5, 0xb7, 0x87, 0xd0, 0x04, 0x11, 0xf5, 0x0d, 0xd3, 0x0f, 0x1c, 0x6f, 0x7f, 0x19,
	0x8a, 0x06, 0xa4, 0x1b, 0xa8, 0xc0, 0x9c, 0x4c, 0x14, 0xf6, 0xbd, 0xc3, 0x19, 0x04, 0x61, 0x04,
	0x64, 0xa9, 0x23, 0x0a, 0x02, 0xd6, 0x79, 0x01, 0x0d, 0x9b, 0x16, 0x05, 0xee, 0xcb, 0x04, 0x1e,
	0x32, 0x2d, 0x00, 0xbc, 0x89, 0xc6, 0x21, 0x73, 0x77, 0x5a, 0x36, 0xf6, 0x28, 0x42, 0x7f, 0x26,
	0x42, 0x91, 0x80, 0x3d, 0x20, 0x50, 0xa0, 0x8d, 0x45, 0x34, 0x44, 0x66, 0xe4, 0xa6, 0x2b, 0x5d,
	0xcf, 0x92, 0x2d, 0xbe, 0xc6, 0x8a, 0x85, 0x62, 0x35, 0x8a, 0x2f, 0xa1, 0xbe, 0x55, 0x4b, 0xba,
	0x12, 0x0b, 0x98, 0xc2, 0x60, 0x92, 0xb1, 0x6a, 0xc4, 0xd7, 0xd1, 0x00, 0x49, 0x30, 0xa5, 0x1b,
	0xed, 0x72, 0xa6, 0xf0, 0x92, 0x02, 0xb2, 0x62, 0xb9, 0xcf, 0xa0, 0x51, 0x6a, 0xb4, 0xae, 0xc2,
	0x82, 0xe5, 0x52, 0xc2, 0x0e, 0x94, 0xe7, 0xd1, 0x08, 0xc5, 0xef, 0x28, 0x32, 0xc1, 0x13, 0x45,
	0x1e, 0x2c, 0xd7, 0x10, 0xa2, 0x38, 0x27, 0x10, 0x9c, 0x60, 0x67, 0x08, 0x3e, 0x54, 0xfe, 0x19,
	0x34, 0x7a, 0x0b, 0x37, 0x71, 0x80, 0x99, 0xe0, 0xb7, 0xd0, 0x04, 0x13, 0x1c, 0x3f, 0x76, 0x4d,
	0x0f, 0xab, 0x3a, 0x5f, 0x1c, 0x56, 0xe4, 0x2f, 0x1d, 0xce, 0x88, 0xa3, 0x7a, 0xeb, 0x70, 0x66,
	0xb0, 0x19, 0x78, 0xa6, 0x55, 0x1f, 0xa7, 0xad, 0xb7, 0x01, 0xa3, 0x66, 0x53, 0xda, 0xc3, 0xe5,
	0x9f, 0x46, 0x23, 0x94, 0xf6, 0xaa, 0x25, 0x2d, 0xa2, 0xa2, 0x69, 0xb5, 0xd3, 0x3c, 0xf7, 0xa5,
	0xc3, 0x19, 0x3e, 0xda, 0x98, 0x5e, 0xc1, 0xb4, 0x92, 0xb4, 0x46, 0xca, 0x5f, 0x40, 0x88, 0xd2,
	0x82, 0xc1, 0xde, 0x62, 0x83, 0x6d, 0xa3, 0x77, 0xe9, 0x4b, 0x87, 0x33, 0x69, 0x3d, 0xc4, 0x74,
	0x41, 0x03, 0x49, 0xca, 0xf9, 0xa5, 0x17, 0x0f, 0x8e, 0xe4, 0xb7, 0xf8, 0x94, 0x39, 0x07, 0x53,
	0x86, 0x66, 0xe6, 0xea, 0x16, 0xde, 0xd1, 0xf6, 0x4c, 0xc7, 0x8b, 0x67, 0xcd, 0x1e, 0x8d, 0xb6,
	0xf4, 0x5a, 0xac, 0x7e, 0x4f, 0x9a, 0x42, 0x43, 0x74, 0xc5, 0x04, 0xf6, 0x23, 0x75, 0xf6, 0x57,
	0xf9, 0x3a, 0x1a, 0xa2, 0x30, 0x9d, 0x20, 0x60, 0xc2, 0xfe, 0xd2, 0x21, 0x0d, 0xf3, 0xef, 0x24,
	0xc3, 0x3c, 0x5b, 0x97, 0xc5, 0xd9, 0xba, 0x8f, 0x10, 0xf0, 0xd5, 0x6c, 0xbf, 0x7e, 0x8f, 0xd5,
	0x2d, 0x41, 0xa9, 0x39, 0xa3, 0x3a, 0x6c, 0xfa, 0x8f, 0xc8, 0x9f, 0xe5, 0x97, 0xd0, 0xf0, 0x2a,
	0xfd, 0xef, 0x31, 0x50, 0xc0, 0xfc, 0x3f, 0xff, 0x46, 0x6d, 0xa9, 0x72, 0x70, 0x24, 0xff, 0xcd,
	0x47, 0x89, 0x1b, 0xd6, 0x6d, 0xcd, 0xf6, 0x13, 0xac, 0x1d, 0x54, 0x80, 0x02, 0x24, 0xd8, 0x4a,
	0xdf, 0x23, 0xe9, 0x05, 0x94, 0x1a, 0x31, 0x92, 0xf4, 0x8f, 0xf2, 0x73, 0x68, 0x10, 0x80, 0xb2,
	0xbb, 0x81, 0xdf, 0x8f, 0x0f, 0x6b, 0x4b, 0xcf, 0x1e, 0x1c, 0xc9, 0xdf, 0xe0, 0xfc, 0x8a, 0xb4,
	0x64, 0x09, 0xb8, 0x8a, 0x0c, 0xdf, 0xeb, 0x43, 0x93, 0x84, 0xd9, 0x43, 0x9a, 0xe0, 0x36, 0x79,
	0x01, 0xc4, 0x0c, 0x42, 0x3c, 0xd3, 0x88, 0x8b, 0x7b, 0x58, 0x0b, 0x2d, 0x3f, 0xe4, 0xdd, 0x90,
	0x33, 0xb0, 0xcb, 0x44, 0xd6, 0xc6, 0x12, 0x87, 0x51, 0x37, 0xdc, 0x6a, 0x9a, 0xfe, 0x8e, 0x58,
	0x67, 0x54, 0x60, 0x6d, 0x24, 0x96, 0x96, 0xbf, 0x9c, 0x43, 0x53, 0x9b, 0x71, 0xc1, 0x86, 0x58,
	0x9b, 0xfc, 0x7f, 0x83, 0x3f, 0x68, 0xea, 0x97, 0xbf, 0x53, 0x83, 0xca, 0xb2, 0xbf, 0x96, 0x8c,
	0xe3, 0x11, 0x73, 0x7b, 0xdb, 0xa9, 0x98, 0xa8, 0x28, 0x88, 0x59, 0xbf, 0x47, 0x12, 0x41, 0xa7,
	0x65, 0x33, 0xb5, 0x93, 0xff, 0x96, 0x2f, 0xa3, 0xfe, 0x07, 0x2d, 0xbb, 0xbd, 0x03, 0x78, 0xfc,
	0xcf, 0xef, 0xd0, 0x45, 0xed, 0xaf, 0x27, 0x17, 0x35, 0xce, 0x43, 0xb4, 0xc7, 0x7f, 0x99, 0x40,
	0x53, 0xbc, 0x7e, 0xc5, 0xdc, 0xc3, 0x62, 0xc1, 0xd5, 0x0d, 0xba, 0x53, 0x69, 0x9a, 0x7b, 0x98,
	0x17, 0x0d, 0xda, 0x81, 0xaa, 0x70, 0xed, 0x94, 0xc2, 0x87, 0x04, 0x83, 0x16, 0x0e, 0xda, 0x81,
	0x62, 0x24, 0x10, 0x58, 0xf2, 0x48, 0x11, 0xfa, 0x44, 0x04, 0x7a, 0x0b, 0x0a, 0x08, 0x8b, 0xa8,
	0x9c, 0x42, 0x80, 0xc9, 0xcd, 0xb0, 0xa8, 0x02, 0xcf, 0x09, 0x58, 0x24, 0x3a, 0x50, 0xcc, 0x57,
	0x04, 0xcc, 0xc0, 0x09, 0xb4, 0xa6, 0x1a, 0x55, 0x15, 0x29, 0xfc, 0x62, 0xfe, 0x3c, 0xc5, 0xdc,
	0x20, 0xdd, 0xb7, 0x58, 0xaf, 0x62, 0x48, 0x4b, 0xf4, 0x42, 0x0e, 0x50, 0xf9, 0x64, 0x14, 0xb8,
	0x0e, 0x8a, 0xb8, 0x2c, 0x14, 0x44, 0x6c, 0x6f, 0xa2, 0x0b, 0x31, 0x2e, 0xcc, 0x25, 0x01, 0x93,
	0x5e, 0xf3, 0x4e, 0x32, 0x4c, 0x32, 0x99, 0x33, 0xf1, 0x1a, 0xe6, 0x76, 0x90, 0xc0, 0x1b, 0x16,
	0xf1, 0xee, 0x9a, 0xdb, 0x41, 0x8c, 0xf7, 0x53, 0xf4, 0x0c, 0x23, 0xc6, 0x63, 0x28, 0x23, 0xbc,
	0xf2, 0x81, 0xa3, 0x74, 0x00, 0xd7, 0x2c, 0x00, 0xa7, 0x15, 0x63, 0x02, 0xf8, 0xb2, 0x45, 0xc0,
	0x17, 0x90, 0x9c, 0x04, 0xb7, 0xb4, 0xc7, 0x1c, 0x85, 0x56, 0x8f, 0x49, 0x31, 0xca, 0x9a, 0xf6,
	0x98, 0x62, 0x65, 0xbb, 0x45, 0x95, 0x17, 0x2c, 0xa7, 0xdc, 0xa2, 0xda, 0xc9, 0x2d, 0xaa, 0xbc,
	0x8e, 0x39, 0xe5, 0x16, 0xd5, 0x63, 0xdd, 0xa2, 0x6a, 0x40, 0x59, 0x59, 0xa6, 0x5b, 0x54, 0x8f,
	0x75, 0x8b, 0xaa, 0x01, 0x55, 0x66, 0xd9, 0x6e, 0x51, 0x3d, 0xde, 0x2d, 0xaa, 0xb4, 0x22, 0x3a,
	0xdb, 0x2d, 0xaa, 0xc7, 0xb9, 0x45, 0xd5, 0x80, 0x92, 0xb4, 0x2c, 0xb7, 0xa8, 0x1e, 0xe7, 0x16,
	0x55, 0x5a, 0x3f, 0x9d, 0xe5, 0x16, 0xd5, 0x0e, 0x6e, 0x51, 0x35, 0x58, 0x4d, 0x5a, 0xd2, 0x2d,
	0xaa, 0x1d, 0xdc, 0xa2, 0x6a, 0x40, 0x3d, 0x5a, 0xda, 0x2d, 0xaa, 0xc7, 0xb8, 0x45, 0x95, 0x96,
	0x5d, 0x67, 0xb8, 0x45, 0x95, 0x6c, 0x86, 0xa6, 0x32, 0xdc, 0x62, 0x6e, 0x81, 0x16, 0x64, 0x17,
	0xeb, 0x13, 0x49, 0xbf, 0x98, 0x5b, 0x48, 0xa2, 0xc4, 0x8e, 0x41, 0x50, 0xa6, 0x44, 0x14, 0xee,
	0x19, 0x04, 0x45, 0xb4, 0x52, 0xca, 0x35, 0x08, 0xde, 0xb4, 0x68, 0x25, 0xd1, 0x37, 0x08, 0xee,
	0xab, 0x02, 0x6e, 0xca, 0x39, 0x08, 0xae, 0x0c, 0xb8, 0x53, 0x19, 0xde, 0x41, 0x90, 0x5f, 0x43,
	0x97, 0x3a, 0xba, 0x07, 0xc1, 0xbe, 0x20, 0x62, 0x27, 0xfc, 0x83, 0x60, 0x8b, 0x1e, 0x9d, 0x74,
	0x10, 0x82, 0x5b, 0x16, 0x3d, 0x5a, 0xf0, 0x90, 0x34, 0x66, 0xd2, 0x45, 0x08, 0xe6, 0x45, 0x11,
	0x53, 0xf0, 0x11, 0x82, 0x29, 0x4e, 0xbb, 0xc8, 0x49, 0x08, 0xd2, 0x25, 0x71, 0xda, 0x31, 0x2f,
	0xc9, 0x44, 0x20, 0x36, 0x27, 0x08, 0x33, 0xbc, 0x20, 0x4a, 0xf0, 0x13, 0x82, 0xd0, 0xe6, 0xbe,
	0xdc, 0x51, 0x08, 0xd2, 0x33, 0x80, 0x34, 0x99, 0xf6, 0x14, 0x82, 0xf7, 0xf3, 0x82, 0x83, 0xb1,
	0x6a, 0x5d, 0x38, 0x07, 0x23, 0x68, 0x97, 0x4f, 0x50, 0x89, 0x90, 0x5a, 0xa8, 0x66, 0x37, 0xa9,
	0xc9, 0xb4, 0xc6, 0xaa, 0xf1, 0xd0, 0xc3, 0xdb, 0x73, 0x0b, 0x46, 0x74, 0x8a, 0x99, 0xee, 0x90,
	0xbe, 0x28, 0x3a, 0x77, 0x5c, 0xfb, 0x01, 0xbc, 0xaf, 0x00, 0xef, 0xdb, 0xa7, 0xe0, 0x1d, 0xdf,
	0xa2, 0x46, 0xcc, 0xf9, 0xc8, 0xc5, 0x1e, 0xe9, 0x17, 0x73, 0x82, 0x0f, 0x46, 0x9f, 0x27, 0xc5,
	0x12, 0x5c, 0x05, 0x09, 0xee, 0x9e, 0x42, 0x82, 0x65, 0xf6, 0x69, 0x53, 0x42, 0x06, 0x36, 0x11,
	0x52, 0x7d, 0xf4, 0x1c, 0x2e, 0x53, 0x65, 0x3d, 0x9d, 0x5b, 0xdd, 0x41, 0x72, 0xa7, 0xd1, 0xf7,
	0x44, 0xe7, 0x0d, 0x54, 0xee, 0x3c, 0x86, 0x9e, 0xae, 0x33, 0x9f, 0x3f, 0x38, 0x92, 0xbf, 0xc5,
	0x73, 0x9e, 0x0b, 0x51, 0xbd, 0x2e, 0x51, 0x76, 0xa2, 0xc0, 0xf0, 0x83, 0x61, 0xba, 0x3d, 0x5e,
	0x7f, 0x33, 0xd4, 0x3c, 0xcc, 0xf3, 0x1d, 0x88, 0x0f, 0xac, 0x1a, 0x0d, 0xeb, 0xaa, 0xeb, 0xf8,
	0x10, 0x5f, 0x48, 0x08, 0xe0, 0xd5, 0x86, 0xb9, 0xfa, 0x14, 0x2b, 0x4e, 0xc3, 0xfa, 0x43, 0xe8,
	0xaf, 0x93, 0x6e, 0x08, 0x9a, 0xd3, 0x1c, 0x39, 0x8d, 0xd8, 0xc7, 0x66, 0x02, 0x20, 0x26, 0xb1,
	0x8e, 0x63, 0xa9, 0xf0, 0x4a, 0xc4, 0x4c, 0x96, 0x4a, 0x67, 0x96, 0x2c, 0x01, 0xca, 0x62, 0xa9,
	0x18, 0xd2, 0xeb, 0x24, 0x90, 0x81, 0x36, 0x52, 0x5f, 0x83, 0xb0, 0x63, 0x68, 0x9a, 0xff, 0x4c,
	0x87, 0x44, 0x3f, 0xf7, 0xc4, 0x0f, 0x43, 0x68, 0x79, 0xf3, 0x8b, 0x48, 0x0a, 0xe1, 0x93, 0x2d,
	0x95, 0x9e, 0x1e, 0xd2, 0x85, 0x87, 0xa6, 0x3e, 0x63, 0xe1, 0x23, 0x13, 0xb7, 0x08, 0x3b, 0xba,
	0xee, 0x40, 0x7c, 0x07, 0x58, 0x3e, 0x38, 0x0e, 0x3f, 0xcc, 0xe2, 0x3b, 0x81, 0x67, 0xe3, 0xa2,
	0x28, 0x19, 0xe4, 0xa3, 0x74, 0x27, 0x41, 0x5e, 0xe9, 0x48, 0x9e, 0xe5, 0x3b, 0xed, 0xe4, 0x15,
	0x56, 0xa0, 0xd9, 0x74, 0x1a, 0x50, 0x69, 0xcf, 0x85, 0x41, 0xac, 0x20, 0xf2, 0x9e, 0xd3, 0xb8,
	0xa5, 0xed, 0xfb, 0x54, 0x90, 0x76, 0x48, 0x85, 0x67, 0x38, 0x22, 0xa4, 0x62, 0xd0, 0x72, 0x48,
	0x0f, 0x37, 0xb5, 0x00, 0x1b, 0x6a, 0x48, 0x36, 0xab, 0xa3, 0xac, 0x1c, 0xb2, 0x4e, 0x1b, 0x37,
	0x75, 0x3b, 0xa0, 0xc6, 0x02, 0x19, 0xe1, 0x5c, 0x33, 0x2a, 0x7a, 0x34, 0x1d, 0xf6, 0x15, 0xdf,
	0x64, 0x48, 0xc4, 0x24, 0x3b, 0x09, 0x5e, 0xfd, 0x68, 0x3a, 0xb4, 0x94, 0x50, 0xd3, 0x03, 0xe2,
	0xc1, 0x3b, 0x4e, 0xe8, 0x41, 0x0a, 0x33, 0x58, 0x2f, 0x84, 0xcb, 0xd0, 0xf6, 0x86, 0x13, 0xb2,
	0xe2, 0xd8, 0xa4, 0xca, 0x48, 0x28, 0x19, 0x67, 0xb9, 0x80, 0xa0, 0x33, 0x12, 0x81, 0x14, 0x22,
	0x47, 0xbb, 0xd2, 0x08, 0x06, 0x4d, 0x54, 0xa4, 0x94, 0xd6, 0x68, 0xa0, 0x97, 0x33, 0x3d, 0x8d,
	0x60, 0x4d, 0x80, 0xab, 0x9d, 0x6b, 0x73, 0x35, 0x82, 0x07, 0xbe, 0xd6, 0xc1, 0xbd, 0x09, 0xae,
	0x04, 0xb8, 0xd3, 0x59, 0xfe, 0x3d, 0xb7, 0x60, 0x2c, 0x5d, 0x39, 0x38, 0x92, 0xff, 0x16, 0x9f,
	0xce, 0x93, 0x74, 0xef, 0x0e, 0x33, 0x36, 0x9a, 0xc8, 0x5f, 0x1e, 0x42, 0x53, 0x0f, 0xe9, 0x06,
	0x2b, 0x3d, 0x9b, 0x5f, 0x41, 0x65, 0x17, 0xbe, 0x30, 0x80, 0xc1, 0x0a, 0xfa, 0x86, 0xc2, 0x74,
	0x3a, 0x99, 0xcf, 0xbb, 0xcb, 0x7b, 0x0d, 0x32, 0xdc, 0x58, 0xe5, 0xf3, 0x8b, 0x64, 0xcd, 0xbd,
	0xe0, 0xaa, 0x1e, 0x76, 0x9b, 0xfb, 0xaa, 0xee, 0x58, 0x16, 0xb6, 0x03, 0x01, 0xb3, 0x8f, 0x61,
	0xd6, 0x49, 0x7f, 0x8d, 0x76, 0x47, 0x98, 0xc0, 0x94, 0xac, 0x82, 0x99, 0x4c, 0xfb, 0x19, 0xea,
	0x9a, 0xf6, 0xb8, 0x9d, 0xe9, 0xeb, 0xe8, 0x92, 0x7b, 0xdc, 0xbc, 0xa4, 0x7b, 0x9a, 0x69, 0xb7,
	0xc3, 0xbc, 0x9c, 0x47, 0x53, 0xae, 0xca, 0xf7, 0x9f, 0x91, 0x59, 0x09, 0x57, 0x3a, 0xa1, 0x25,
	0x97, 0xe9, 0x89, 0x99, 0x95, 0xb0, 0x7c, 0x19, 0x9d, 0x77, 0x41, 0x52, 0x6c, 0x24, 0x51, 0xe8,
	0x74, 0x9e, 0x70, 0x1f, 0x41, 0x9f, 0x80, 0x51, 0x46, 0x79, 0x97, 0x24, 0x30, 0x70, 0x26, 0xc3,
	0xbf, 0x62, 0xb9, 0xa3, 0x91, 0x84, 0x4e, 0x7a, 0x16, 0x8d, 0x91, 0xb1, 0x37, 0x71, 0x04, 0xc0,
	0xca, 0x91, 0xdd, 0x35, 0xad, 0x89, 0x19, 0xd0, 0x67, 0xd1, 0x8c, 0xab, 0xb2, 0x85, 0xad, 0xa9,
	0xba, 0x64, 0x09, 0x04, 0x55, 0x85, 0xdc, 0x09, 0xe9, 0xd4, 0x95, 0x5d, 0x7e, 0x3e, 0xf0, 0x50,
	0x6b, 0x60, 0x22, 0x06, 0x99, 0x44, 0xcc, 0xa5, 0x3a, 0x13, 0x50, 0xf8, 0x64, 0x9e, 0xce, 0xc4,
	0x57, 0xd8, 0x90, 0xb9, 0x51, 0x09, 0x8a, 0x1f, 0x5a, 0x30, 0xe4, 0x02, 0x1b, 0x32, 0xb3, 0x68,
	0xcd, 0x0e, 0xd6, 0x43, 0x2b, 0xb2, 0x4b, 0xec, 0x47, 0x6d, 0xfe, 0x40, 0x3f, 0x88, 0x99, 0x8e,
	0x3c, 0x29, 0xe5, 0x11, 0x80, 0x1e, 0x7b, 0x44, 0x1b, 0x7a, 0x91, 0xa1, 0x73, 0x9f, 0x48, 0xa2,
	0xc3, 0x2e, 0xfe, 0x6f, 0xf3, 0x29, 0x20, 0xbb, 0xdc, 0xd5, 0xd3, 0xf3, 0xe0, 0xf7, 0xfa, 0xe9,
	0xb7, 0x68, 0x74, 0x0a, 0x90, 0xf5, 0x33, 0xde, 0xc6, 0x43, 0x39, 0xbc, 0x1e, 0x78, 0x2a, 0xb3,
	0x0e, 0xd1, 0x32, 0x75, 0xff, 0xd1, 0xb0, 0x16, 0x78, 0x0f, 0x89, 0x75, 0x88, 0x66, 0x21, 0xf4,
	0x52, 0xb8, 0x6d, 0x1c, 0x41, 0x52, 0x77, 0x1f, 0x03, 0xc8, 0x3b, 0xd0, 0x1c, 0x65, 0xee, 0x04,
	0xd6, 0x31, 0x8c, 0x24, 0x3c, 0xf5, 0xf1, 0x09, 0x02, 0xff, 0xc0, 0x30, 0x04, 0x94, 0x6b, 0x68,
	0x3c, 0x21, 0x46, 0x95, 0xaf, 0x52, 0x85, 0x48, 0x0a, 0x1e, 0x76, 0x13, 0x42, 0x08, 0x15, 0xf4,
	0xb1, 0x0c, 0x7c, 0x57, 0xd9, 0x26, 0x02, 0x5b, 0x8b, 0x48, 0xb6, 0x9a, 0x90, 0xa0, 0xda, 0x2e,
	0x00, 0xdb, 0x79, 0x8b, 0x02, 0x28, 0x59, 0x02, 0xb0, 0xf5, 0x27, 0x29, 0x80, 0xd2, 0x49, 0x80,
	0x68, 0xb7, 0x9d, 0x12, 0x40, 0x31, 0xe0, 0xcb, 0xbe, 0xaf, 0xfc, 0x8b, 0xd4, 0x97, 0x7d, 0xcc,
	0x90, 0x2e, 0x98, 0x2c, 0xb2, 0xe7, 0x3f, 0xce, 0xa1, 0x0b, 0xb1, 0x3d, 0xd7, 0xf1, 0x9b, 0x21,
	0xb6, 0xf5, 0x28, 0xb4, 0xc1, 0x26, 0x28, 0xd8, 0x09, 0x2d, 0x57, 0x0d, 0x5d, 0xd5, 0xb4, 0xc8,
	0x24, 0xd8, 0x69, 0x41, 0x2a, 0xed, 0xe3, 0x37, 0x59, 0x29, 0xdb, 0xf9, 0x70, 0x83, 0x40, 0x6c,
	0xba, 0xab, 0xa4, 0xff, 0x8d, 0xd6, 0x86, 0xd6, 0x58, 0xc7, 0x6f, 0xd2, 0x1d, 0x32, 0x77, 0xc3,
	0x36, 0xd4, 0x3e, 0x86, 0xca, 0xdc, 0x30, 0x81, 0xba, 0xf4, 0xa9, 0x83, 0x23, 0xf9, 0x9f, 0xfe,
	0xcb, 0x48, 0xfe, 0x4b, 0xa2, 0xfc, 0x3e, 0x13, 0x31, 0x1a, 0xc1, 0x87, 0x05, 0x54, 0x16, 0x3c,
	0x92, 0x3b, 0x2e, 0x1f, 0xc2, 0x2e, 0xad, 0x27, 0xa2, 0xcb, 0x16, 0x5d, 0x81, 0x0c, 0x56, 0x99,
	0xb1, 0x72, 0xcc, 0x0d, 0x78, 0x26, 0xb1, 0xd9, 0xcd, 0x87, 0xb0, 0xc8, 0x91, 0xc5, 0x2a, 0x2a,
	0xc8, 0x17, 0x9a, 0x24, 0x9b, 0x56, 0xae, 0xb1, 0x05, 0x8c, 0x73, 0xa3, 0x95, 0x19, 0xb5, 0x53,
	0x70, 0xa3, 0x4b, 0x9d, 0xc0, 0xae, 0x18, 0x8a, 0x6d, 0x52, 0x48, 0x37, 0x52, 0x91, 0x85, 0x22,
	0x96, 0xfd, 0xc7, 0x6d, 0x30, 0x8e, 0x65, 0xc9, 0x6c, 0x29, 0x32, 0x1d, 0x0f, 0x93, 0xad, 0xd2,
	0xcf, 0xd1, 0xef, 0x5e, 0x40, 0xa7, 0x70, 0xad, 0x34, 0xc7, 0xbf, 0x96, 0x38, 0xad, 0x4a, 0x37,
	0x4c, 0x0b, 0xa7, 0x54, 0x4a, 0x9b, 0x32, 0xec, 0xa7, 0xf0, 0x2f, 0x28, 0xce, 0x62, 0x3f, 0xa5,
	0xdd, 0x7e, 0x4a, 0xa6, 0xfd, 0xe0, 0xb8, 0xed, 0xcc, 0xf6, 0x53, 0x32, 0xec, 0xa7, 0x74, 0xb2,
	0x1f, 0xc4, 0x8b, 0xa7, 0x60, 0x3f, 0x25, 0xd3, 0x7e, 0x4a, 0x86, 0xfd, 0x20, 0xf2, 0x9c, 0xd5,
	0x7e, 0x4a, 0xbb, 0xfd, 0x14, 0xa3, 0xfc, 0x59, 0x34, 0xd1, 0x36, 0x6b, 0xba, 0x6d, 0xb5, 0x8a,
	0x6d, 0x65, 0x5f, 0xe9, 0x89, 0xd0, 0x13, 0x05, 0x28, 0x9d, 0x68, 0xf7, 0xeb, 0x9e, 0x68, 0x44,
	0xc3, 0x10, 0x3c, 0xf5, 0x74, 0x04, 0x04, 0xcb, 0x9c, 0x41, 0x0f, 0xca, 0x53, 0xd0, 0x83, 0x72,
	0x56, 0x3d, 0x9c, 0x82, 0x00, 0xdc, 0x15, 0x1d, 0xfc, 0xeb, 0x28, 0xb6, 0xd3, 0x64, 0xdb, 0x0d,
	0xb7, 0xcc, 0x26, 0x4d, 0x37, 0x02, 0x2d, 0xa8, 0xfc, 0xa4, 0x5f, 0xb8, 0x3d, 0x69, 0xe2, 0x65,
	0x5d, 0x0f, 0x2d, 0x76, 0xb3, 0xfc, 0x0b, 0x68, 0x1a, 0x3e, 0x70, 0x11, 0xbf, 0x0a, 0x53, 0x35,
	0x02, 0xc0, 0x82, 0xfa, 0x9d, 0xce, 0x1e, 0xdc, 0x4e, 0x6e, 0xf6, 0xae, 0x66, 0x61, 0xe1, 0x5b,
	0x32, 0xe8, 0x62, 0x9f, 0xa1, 0x36, 0x32, 0xba, 0xca, 0x77, 0xd1, 0x85, 0x8e, 0x28, 0xdd, 0x0a,
	0xbc, 0x13, 0x7a, 0xfc, 0x71, 0x2e, 0xba, 0x9e, 0xff, 0x2b, 0xb9, 0x6e, 0x63, 0x5a, 0xeb, 0x69,
	0x4c, 0x94, 0x6c, 0xe7, 0xa1, 0x25, 0xde, 0x54, 0xf9, 0x3f, 0x3b, 0xcc, 0xa5, 0xe1, 0x83, 0x23,
	0xf9, 0xb7, 0x7e, 0xf7, 0xc9, 0xc2, 0xd2, 0xf5, 0x83, 0x23, 0xf9, 0x37, 0x7f, 0x37, 0x32, 0xfb,
	0x85, 0xc4, 0x35, 0x11, 0x8c, 0x15, 0x76, 0x9f, 0x8e, 0x5d, 0xf9, 0x6f, 0x03, 0x09, 0xe3, 0xc3,
	0xd9, 0x2a, 0x33, 0xfe, 0x8b, 0xf4, 0xa5, 0xaa, 0xdb, 0xec, 0x7d, 0x98, 0xfe, 0xeb, 0xf9, 0x95,
	0x51, 0x5e, 0x01, 0xf1, 0x3b, 0x87, 0x33, 0x43, 0xf5, 0xb8, 0x5b, 0x7a, 0x89, 0x5e, 0x75, 0xd2,
	0x32, 0x1c, 0x58, 0x82, 0xd3, 0xc0, 0x42, 0xbf, 0xb4, 0x47, 0x8f, 0x69, 0xa2, 0x83, 0xdc, 0xfb,
	0x1a, 0xdc, 0xab, 0x75, 0xa9, 0xdf, 0x6c, 0x17, 0x71, 0x36, 0xa2, 0xb2, 0x6a, 0x6f, 0x3b, 0x29,
	0x86, 0xed, 0x2c, 0xca, 0x35, 0x7a, 0xff, 0x16, 0x61, 0x48, 0xe7, 0xd1, 0x90, 0xf0, 0xec, 0x40,
	0xbe, 0x3e, 0x18, 0xc0, 0x97, 0x35, 0x17, 0x51, 0x3e, 0xfa, 0x8c, 0x89, 0xe5, 0x4e, 0x23, 0x1e,
	0x27, 0xb2, 0x8c, 0xc6, 0xa9, 0xcd, 0xe3, 0xd1, 0x3f, 0xd7, 0xae, 0xa9, 0xe8, 0xfe, 0x3e, 0xee,
	0x01, 0xf3, 0xfc, 0xd2, 0xdb, 0x1f, 0x2c, 0x94, 0x6f, 0xa1, 0x52, 0x4c, 0x82, 0xe9, 0xe4, 0x85,
	0x84, 0x06, 0x53, 0x44, 0x84, 0x2e, 0xa0, 0xf2, 0x16, 0xa1, 0xf2, 0xf5, 0x1c, 0x9a, 0x4c, 0x49,
	0x42, 0x04, 0x94, 0xde, 0xcc, 0xd2, 0x6e, 0xee, 0xcc, 0xda, 0x8d, 0x84, 0x69, 0xa7, 0x0e, 0x32,
	0xbd, 0xfd, 0xf6, 0x07, 0xd4, 0xf1, 0xbe, 0xf2, 0xf6, 0x07, 0xd9, 0x8e, 0x47, 0x0f, 0xf6, 0x99,
	0xe3, 0x3d, 0xc9, 0xa1, 0xab, 0xd9, 0x7c, 0xef, 0x38, 0x1e, 0x99, 0x0e, 0xba, 0xe6, 0x19, 0xdd,
	0x7c, 0xb0, 0x4f, 0x50, 0x6f, 0x79, 0x31, 0x9a, 0xe3, 0x3d, 0xd8, 0xe3, 0xc7, 0x44, 0xea, 0x4f,
	0x1f, 0x1c, 0xc9, 0x1f, 0xc7, 0x52, 0x3f, 0xdf, 0x51, 0x6a, 0x75, 0xdb, 0xf1, 0xe0, 0x93, 0x3f,
	0x22, 0x5c, 0xe5, 0xfd, 0x7e, 0xea, 0x4f, 0xeb, 0xa6, 0xdd, 0xa0, 0x1f, 0xc1, 0x5d, 0x43, 0x63,
	0xbe, 0x63, 0x37, 0xd4, 0x86, 0x63, 0xb0, 0x2f, 0xf0, 0xd8, 0x27, 0xb4, 0xa4, 0xf5, 0xae, 0x63,
	0xb4, 0x43, 0x89, 0x05, 0x4c, 0x1c, 0x8a, 0x96, 0xd7, 0x5d, 0x43, 0x63, 0x4e, 0x18, 0xa8, 0x81,
	0x63, 0x63, 0x46, 0x8b, 0x5e, 0x9c, 0x8e, 0x3a, 0x61, 0xb0, 0xe1, 0xd8, 0x38, 0xa2, 0x15, 0x41,
	0x89, 0x55, 0x7a, 0x1c, 0x8a, 0xd6, 0x70, 0xfd, 0x41, 0x0e, 0x15, 0xd7, 0x29, 0x71, 0xa6, 0xa2,
	0x97, 0xb3, 0x25, 0x4d, 0x44, 0xab, 0xa4, 0xd4, 0x2f, 0x67, 0x4b, 0x9d, 0x89, 0x41, 0x47, 0xf0,
	0x72, 0xf6, 0x08, 0x92, 0x18, 0x89, 0xd1, 0xbc, 0x9c, 0x3d, 0x9a, 0x4c, 0x0c, 0x5a, 0xeb, 0x45,
	0x6c, 0xf8, 0xee, 0xf7, 0x9e, 0x2c, 0xd0, 0x57, 0x5d, 0xbe, 0x17, 0x85, 0x3c, 0x7a, 0x33, 0xee,
	0x9b, 0x76, 0x83, 0xb2, 0xae, 0xfc, 0xc3, 0x41, 0xfa, 0xae, 0x4e, 0x64, 0xac, 0x47, 0xf3, 0xd2,
	0x3a, 0x42, 0x82, 0x70, 0x74, 0x8a, 0x2c, 0x1c, 0x93, 0x93, 0xc5, 0xa8, 0xb3, 0x91, 0xcc, 0x74,
	0xfd, 0xca, 0x07, 0xd1, 0x18, 0x38, 0x51, 0xae, 0xa3, 0x1e, 0x89, 0xc2, 0xb0, 0x04, 0xa2, 0xd4,
	0x80, 0xaf, 0xa1, 0xb1, 0x24, 0xc7, 0x9e, 0x96, 0x3f, 0x86, 0x1d, 0x93, 0xee, 0x09, 0xfb, 0x47,
	0x7d, 0xd1, 0xc4, 0xda, 0xce, 0x50, 0xd8, 0xab, 0x27, 0x1b, 0x1b, 0x5b, 0x27, 0x93, 0xa3, 0x48,
	0x18, 0x56, 0xd0, 0xe1, 0x76, 0x86, 0x0e, 0x7b, 0xe7, 0x13, 0x8f, 0xb7, 0x9d, 0xcf, 0xff, 0x73,
	0xb5, 0x82, 0xe7, 0x1e, 0x7d, 0xff, 0xc9, 0x02, 0x3c, 0xd7, 0xf3, 0x0f, 0xbe, 0x9f, 0x7a, 0xae,
	0x27, 0xf6, 0x5c, 0x75, 0x6f, 0xbe, 0xe2, 0xf2, 0x2a, 0x57, 0x0b, 0xd7, 0x48, 0x58, 0x5c, 0x46,
	0x83, 0x24, 0x02, 0xf9, 0xcc, 0x04, 0x1d, 0x3e, 0x48, 0xe4, 0xe0, 0x34, 0x7c, 0xbf, 0xc3, 0x2a,
	0xe1, 0x28, 0xe6, 0xd2, 0xcc, 0xc1, 0x91, 0xfc, 0xef, 0xe3, 0xcc, 0x90, 0xd7, 0xaa, 0x5a, 0x58,
	0x85, 0xd8, 0xf6, 0xab, 0x7d, 0x68, 0x54, 0xc4, 0x97, 0xae, 0xa0, 0xd1, 0xa8, 0x37, 0x2e, 0xa5,
	0x41, 0x0d, 0x0e, 0x03, 0xab, 0x26, 0x40, 0x88, 0xab, 0x26, 0x69, 0x80, 0x45, 0xa9, 0x8c, 0xf2,
	0x61, 0xf4, 0x84, 0x18, 0x7b, 0xb6, 0x29, 0x64, 0x0f, 0x88, 0x5d, 0x44, 0x79, 0xa0, 0x0a, 0x15,
	0x38, 0xec, 0x05, 0x40, 0xd2, 0x00, 0xe5, 0x37, 0xaf, 0xa1, 0x11, 0xc7, 0x0d, 0x68, 0x69, 0x1c,
	0xdd, 0x88, 0x5e, 0x3d, 0x7e, 0xb4, 0x0f, 0xdc, 0xa0, 0x3e, 0xec, 0xb8, 0x01, 0x14, 0x30, 0x5e,
	0x44, 0x79, 0x9b, 0xec, 0x2e, 0x41, 0x26, 0x5a, 0x90, 0x3c, 0x42, 0x1a, 0x40, 0xa6, 0x17, 0xd0,
	0xb8, 0xaf, 0x7b, 0x18, 0xdb, 0xfe, 0x8e, 0xc3, 0x38, 0x0c, 0xc3, 0x53, 0x23, 0x63, 0x71, 0x33,
	0xa7, 0x62, 0x39, 0x86, 0xb9, 0xbd, 0xaf, 0x6a, 0xfc, 0xf4, 0x73, 0x84, 0x36, 0x2c, 0x07, 0x95,
	0xbb, 0xa8, 0x20, 0xb0, 0x96, 0x2e, 0x50, 0x79, 0x6d, 0x8d, 0x55, 0xad, 0xe6, 0x41, 0x18, 0xe0,
	0x37, 0x83, 0x58, 0xc9, 0x64, 0x54, 0x79, 0x99, 0xaf, 0xe7, 0xa1, 0x85, 0x70, 0xa9, 0xfc, 0xa3,
	0x5c, 0x6c, 0xe5, 0x0d, 0xad, 0xe1, 0x4b, 0x4b, 0x68, 0x20, 0xd0, 0x1a, 0xdc, 0xc8, 0x33, 0x9d,
	0x87, 0xbd, 0xa1, 0x35, 0x62, 0xfb, 0x02, 0x4e, 0xf9, 0x21, 0x2d, 0xab, 0xe5, 0xb4, 0x58, 0x35,
	0xd4, 0x5c, 0x0f, 0x14, 0x29, 0x21, 0x70, 0xd3, 0x3f, 0x26, 0x01, 0x96, 0x38, 0xcc, 0x7f, 0xff,
	0x5e, 0x96, 0xc3, 0x10, 0xb8, 0xca, 0x77, 0x73, 0x68, 0x98, 0x61, 0xc2, 0x93, 0x7e, 0xdc, 0x43,
	0xfa, 0x4c, 0x43, 0x92, 0xd0, 0x80, 0xe0, 0x14, 0xf0, 0x7f, 0xd2, 0x26, 0x3c, 0x43, 0x06, 0xff,
	0x97, 0x16, 0xd1, 0xa0, 0x16, 0x04, 0x9e, 0xcf, 0x8e, 0x37, 0x2a, 0xc7, 0xca, 0x37, 0xbb, 0x1c,
	0x04, 0x5e, 0x9d, 0x22, 0x94, 0xe7, 0xd1, 0x00, 0xf9, 0x33, 0xe2, 0x94, 0x13, 0x38, 0x4d, 0xa1,
	0x21, 0x50, 0xb2, 0xcf, 0x54, 0xce, 0xfe, 0xaa, 0xac, 0xa3, 0xb1, 0x35, 0x67, 0xcb, 0x6c, 0x62,
	0x42, 0x90, 0x55, 0x2d, 0x0f, 0x07, 0xda, 0x96, 0x6a, 0xb2, 0x89, 0x35, 0x18, 0x2b, 0x75, 0x28,
	0xd0, 0xb6, 0x56, 0x0d, 0x7f, 0xe9, 0xf2, 0xc1, 0x91, 0xfc, 0x6f, 0xff, 0x59, 0xa4, 0x84, 0x92,
	0x05, 0x04, 0xa8, 0x1a, 0x88, 0x55, 0x2b, 0x5f, 0x40, 0xa5, 0x24, 0xd1, 0x47, 0xf3, 0x27, 0x20,
	0x4b, 0xb6, 0x69, 0x7f, 0xef, 0x9f, 0xc7, 0xdb, 0xb4, 0x34, 0x59, 0x12, 0x03, 0x6e, 0xa1, 0xfc,
	0xda, 0x3e, 0x7b, 0x45, 0x4e, 0xba, 0x28, 0x18, 0x32, 0x9f, 0xb2, 0x3d, 0x3c, 0xfa, 0xf1, 0x71,
	0x7c, 0x20, 0x59, 0xb0, 0xf6, 0xa3, 0xf7, 0xe9, 0x2a, 0x1f, 0xf7, 0xa1, 0x22, 0x55, 0xe0, 0xd6,
	0xe7, 0xb1, 0xd9, 0xd8, 0x09, 0xa4, 0x67, 0x51, 0x51, 0xd7, 0x6c, 0x1d, 0x37, 0xd5, 0x16, 0x34,
	0xb0, 0xa2, 0xb3, 0x51, 0xda, 0xc8, 0x80, 0x9e, 0x47, 0xe3, 0x4e, 0xd3, 0xa0, 0xf7, 0x18, 0x0c,
	0xac, 0x0f, 0xc0, 0x8a, 0x4e, 0x13, 0xd2, 0x4f, 0x06, 0x77, 0x15, 0x8d, 0xee, 0x68, 0x86, 0x4a,
	0x71, 0x31, 0x9d, 0xe9, 0x23, 0xf5, 0xc2, 0x8e, 0x66, 0xd4, 0x58, 0x53, 0x79, 0x05, 0x49, 0x35,
	0x81, 0x34, 0x5b, 0x59, 0x4e, 0x22, 0x05, 0xf8, 0xe2, 0xf7, 0x3f, 0x7c, 0xb2, 0x50, 0xbe, 0x83,
	0x26, 0x1f, 0x88, 0x7c, 0x19, 0x91, 0x0c, 0x29, 0x73, 0x19, 0x52, 0x02, 0x9d, 0x8f, 0x08, 0x9d,
	0xcf, 0xa2, 0x89, 0x37, 0x62, 0xd1, 0x18, 0x95, 0xf4, 0x18, 0x72, 0x6d, 0x63, 0x00, 0x02, 0xbf,
	0xff, 0x21, 0xcb, 0x3a, 0x3e, 0xfc, 0x30, 0xce, 0x3a, 0xd8, 0x7c, 0xd8, 0x62, 0xec, 0x2b, 0xdf,
	0x1c, 0xa7, 0x59, 0x07, 0x3c, 0xef, 0x43, 0xcf, 0xef, 0x33, 0x9e, 0xc0, 0xfc, 0x0b, 0xb9, 0xf8,
	0xbd, 0x25, 0x82, 0x1c, 0xd5, 0xaf, 0xd0, 0xd7, 0xaf, 0xf8, 0x99, 0xe6, 0x31, 0xc7, 0x45, 0x02,
	0x7d, 0xf6, 0xe6, 0xd2, 0x86, 0xb6, 0xc5, 0xeb, 0x5c, 0xe0, 0xcd, 0xac, 0xe4, 0x9b, 0x4b, 0x6d,
	0xbd, 0x5d, 0x64, 0x50, 0xf8, 0x21, 0xe7, 0x59, 0x64, 0x50, 0x8e, 0x95, 0x41, 0xe9, 0xaa, 0x87,
	0x56, 0xf7, 0x63, 0xcf, 0xee, 0x7a, 0x68, 0x1d, 0xab, 0x87, 0x56, 0x17, 0x19, 0xe6, 0x5b, 0xdd,
	0x4f, 0x43, 0xbb, 0xca, 0x30, 0x7f, 0xac, 0x0c, 0xf3, 0x2d, 0xe9, 0x67, 0xd1, 0x58, 0x68, 0xaa,
	0x90, 0x6d, 0xd3, 0x0b, 0x04, 0x76, 0x28, 0x5a, 0x3d, 0x29, 0x53, 0x92, 0xeb, 0xd3, 0x3f, 0xf8,
	0x21, 0xa1, 0xd0, 0x04, 0xd7, 0xb7, 0xa6, 0x1a, 0xea, 0xa1, 0x0f, 0x71, 0xa1, 0xcd, 0xdb, 0xe8,
	0x22, 0x38, 0x1d, 0x9a, 0x9b, 0x7a, 0xe8, 0x6f, 0x68, 0x8d, 0x94, 0xa7, 0x1c, 0x8b, 0xce, 0xce,
	0x36, 0x3b, 0xa1, 0x2b, 0x5d, 0xd0, 0xe7, 0x5a, 0xf0, 0xde, 0x4d, 0x47, 0xee, 0x2d, 0xe9, 0x2b,
	0x39, 0x74, 0x99, 0xdb, 0x87, 0x3e, 0x90, 0x45, 0xac, 0x44, 0x37, 0x69, 0xb4, 0xde, 0xa6, 0x25,
	0xa3, 0xe3, 0x8a, 0x8d, 0xb2, 0x4d, 0x04, 0x2d, 0x1b, 0xda, 0x16, 0xec, 0x0c, 0xa1, 0x50, 0x85,
	0x19, 0xe9, 0x42, 0xd8, 0xa9, 0xbf, 0xbb, 0x28, 0x4a, 0x4b, 0x2e, 0x3c, 0x05, 0x51, 0x94, 0x2e,
	0xa2, 0x28, 0xad, 0xf8, 0x35, 0xb2, 0xec, 0x69, 0xdf, 0x53, 0xdd, 0x4e, 0x27, 0x5a, 0xca, 0x53,
	0xa4, 0xc5, 0xb4, 0xfb, 0x54, 0x68, 0xcd, 0x9f, 0x82, 0x96, 0x8e, 0x26, 0xda, 0x66, 0xc9, 0x99,
	0x1e, 0xfe, 0xe0, 0xb4, 0x44, 0x26, 0xf7, 0xd0, 0x33, 0xc7, 0x3b, 0x57, 0x4f, 0x22, 0x77, 0xa6,
	0xa6, 0x9c, 0x82, 0xda, 0x8f, 0x72, 0xe8, 0x7c, 0x6a, 0x82, 0xb1, 0x1c, 0xb1, 0x5b, 0x74, 0xc8,
	0x9d, 0x2d, 0x3a, 0xf4, 0x9d, 0x2d, 0x3a, 0xf4, 0x1f, 0x1b, 0x1d, 0x60, 0x61, 0xfe, 0x8d, 0xc3,
	0x8f, 0x16, 0xa0, 0xc6, 0xe4, 0xd7, 0x0f, 0x3f, 0x5a, 0x48, 0x7d, 0x54, 0x45, 0x26, 0x29, 0x0d,
	0xa9, 0x95, 0xdf, 0xb9, 0x4a, 0xd3, 0x6d, 0x6e, 0x39, 0x69, 0x17, 0x9d, 0xe7, 0x51, 0x37, 0x7e,
	0xbb, 0x2c, 0xbe, 0xbf, 0x7c, 0xa5, 0xbb, 0xf1, 0x59, 0xe4, 0x8d, 0x9e, 0x39, 0x9b, 0x8b, 0xde,
	0xd3, 0x49, 0xb7, 0x77, 0x60, 0xa6, 0xf0, 0xa5, 0xfe, 0x14, 0xcc, 0x94, 0x0e, 0xcc, 0x94, 0x4e,
	0xcc, 0x98, 0x36, 0x4f, 0x37, 0xb2, 0x56, 0x87, 0x91, 0xb5, 0x24, 0x1b, 0x4d, 0x71, 0x66, 0x24,
	0xe3, 0xc4, 0xb1, 0xe7, 0x0c, 0x74, 0xfd, 0x42, 0x3f, 0xc9, 0xed, 0x1e, 0xa0, 0x8b, 0x8a, 0x94,
	0xc2, 0xb6, 0x8e, 0x4e, 0xfc, 0xa2, 0x7b, 0xcb, 0xd3, 0xf0, 0x53, 0x3a, 0xf1, 0x53, 0x3a, 0xf2,
	0x9b, 0x6b, 0xb1, 0x45, 0xfa, 0x54, 0xe3, 0x6b, 0x75, 0x1a, 0x5f, 0x0b, 0xde, 0x4e, 0x61, 0xfc,
	0xe0, 0x50, 0x80, 0x6b, 0x73, 0xa4, 0xdb, 0xdb, 0x29, 0x29, 0x6e, 0xeb, 0xa6, 0xdd, 0x10, 0x75,
	0x59, 0x0a, 0x53, 0xcd, 0xd9, 0x9c, 0xa0, 0xf0, 0xe1, 0x74, 0x9c, 0x94, 0x6c, 0x4e, 0x4a, 0x07,
	0x4e, 0xd1, 0xc2, 0x7d, 0x8a, 0x31, 0xb5, 0xb2, 0xc7, 0xd4, 0x92, 0xfe, 0x22, 0x5d, 0xa3, 0x45,
	0x73, 0xe9, 0x8e, 0xe5, 0x36, 0x71, 0x80, 0x23, 0x4d, 0x16, 0xba, 0x5e, 0xc6, 0x66, 0xd9, 0xad,
	0xc6, 0xc8, 0x88, 0x3a, 0x2d, 0x87, 0x1d, 0x01, 0xba, 0x4b, 0xa2, 0x18, 0xec, 0x49, 0xbc, 0xb3,
	0x48, 0xa2, 0x74, 0x93, 0x44, 0x39, 0x81, 0x24, 0x73, 0x2d, 0xf6, 0x66, 0xde, 0x99, 0x74, 0xd2,
	0xea, 0xa6, 0x93, 0x96, 0xf4, 0x79, 0x54, 0x8c, 0xfc, 0x40, 0x78, 0x36, 0x4f, 0x39, 0xb9, 0x03,
	0xc4, 0x47, 0xb0, 0x85, 0x30, 0x6e, 0x81, 0x67, 0x67, 0x44, 0x07, 0xf3, 0x43, 0x8b, 0x58, 0x7a,
	0xbc, 0x5b, 0x1a, 0x9d, 0xe1, 0x5f, 0xeb, 0xa1, 0x15, 0x97, 0x66, 0x24, 0x5b, 0x33, 0xd9, 0x28,
	0x06, 0x7b, 0xdf, 0xae, 0x67, 0x36, 0x4a, 0x26, 0x1b, 0x25, 0x9b, 0xcd, 0x5c, 0xeb, 0xf8, 0xe7,
	0xeb, 0x8e, 0x19, 0x4d, 0x2b, 0x73, 0x34, 0x2d, 0xe9, 0xad, 0x1c, 0xba, 0x42, 0xf8, 0x00, 0x8b,
	0x78, 0xf3, 0x01, 0xf5, 0x3f, 0xf4, 0x28, 0x72, 0x8e, 0x3f, 0x64, 0x57, 0x3b, 0x19, 0x53, 0x42,
	0x3a, 0x4a, 0xb2, 0x08, 0x1d, 0xb0, 0x0e, 0x57, 0xe7, 0xc5, 0xb0, 0x33, 0xc4, 0x09, 0x84, 0x51,
	0xf8, 0x43, 0x77, 0x67, 0x12, 0x46, 0xe9, 0x2a, 0x8c, 0x72, 0x12, 0x61, 0xe6, 0x5a, 0xf2, 0xb9,
	0xa7, 0xa0, 0x99, 0x56, 0x57, 0xcd, 0xb4, 0xa4, 0xaf, 0xe5, 0xd0, 0xd5, 0xd0, 0xe4, 0x33, 0xb7,
	0x4d, 0x1c, 0xf2, 0xf7, 0x9c, 0x21, 0x9f, 0xef, 0xb6, 0xf1, 0x10, 0xa4, 0xa1, 0x73, 0x33, 0xc5,
	0xcd, 0xb1, 0x1b, 0xdc, 0x50, 0x97, 0xc2, 0x63, 0x40, 0x4e, 0x22, 0x8f, 0xc2, 0xdf, 0xaa, 0x3e,
	0x9b, 0x3c, 0x4a, 0x77, 0x79, 0x94, 0x13, 0xc9, 0x33, 0xd7, 0x62, 0x4f, 0x58, 0x9f, 0x51, 0x3f,
	0xad, 0xee, 0xfa, 0x69, 0xc1, 0x3b, 0x20, 0x99, 0x79, 0x61, 0x4f, 0x6f, 0xd4, 0xb5, 0x53, 0x51,
	0x9e, 0x0a, 0x95, 0x8c, 0xad, 0x48, 0xfe, 0x24, 0x0f, 0xbe, 0x64, 0x67, 0x68, 0xdd, 0xc8, 0xf4,
	0x75, 0x21, 0xa3, 0x3c, 0x1d, 0x32, 0x27, 0x1c, 0x54, 0x5f, 0xfa, 0xfd, 0xaf, 0xac, 0x44, 0xe9,
	0x6c, 0x44, 0x94, 0xa7, 0x41, 0xe4, 0x34, 0xc3, 0x59, 0x43, 0x97, 0xbb, 0x64, 0x2b, 0x4f, 0x8b,
	0x9c, 0xf2, 0x74, 0xc9, 0x9d, 0x66, 0xb0, 0x9f, 0x41, 0xa5, 0x74, 0x3e, 0xd0, 0x13, 0xfe, 0x0a,
	0x3a, 0x97, 0xb5, 0xe0, 0x9f, 0x89, 0x86, 0xf2, 0x14, 0x68, 0x9c, 0x46, 0x17, 0xf7, 0xd1, 0x95,
	0x6e, 0x2b, 0xef, 0x53, 0xa3, 0xa7, 0x3c, 0x65, 0x7a, 0xa7, 0x19, 0xef, 0x03, 0x74, 0xb5, 0xeb,
	0x0a, 0xf6, 0xf4, 0x08, 0x2a, 0x4f, 0x9b, 0xe0, 0x29, 0x86, 0x5c, 0xf9, 0x37, 0x39, 0x74, 0x31,
	0x3a, 0x3b, 0x14, 0xdf, 0xb7, 0x65, 0x95, 0x5b, 0x8b, 0xf4, 0x07, 0x64, 0xc4, 0xee, 0x8c, 0xe2,
	0x99, 0xc1, 0x7a, 0x1b, 0x54, 0xb9, 0x8e, 0x24, 0x7a, 0x0b, 0x22, 0xb6, 0x4a, 0x4a, 0x47, 0x7a,
	0x51, 0x59, 0x4d, 0x1b, 0x00, 0xbd, 0xac, 0xf9, 0xea, 0x07, 0x0b, 0x4b, 0xb3, 0x07, 0x47, 0xf2,
	0x87, 0x5f, 0x8d, 0xaa, 0x6b, 0x2e, 0x0b, 0x47, 0x31, 0x99, 0x95, 0x41, 0xef, 0xe6, 0x68, 0x59,
	0xcd, 0x2a, 0xff, 0xe1, 0x31, 0xe9, 0x79, 0x34, 0x6e, 0xc6, 0x3f, 0x58, 0x06, 0xd7, 0x86, 0xf4,
	0x90, 0xab, 0x68, 0x72, 0x18, 0xf8, 0x01, 0xa3, 0xd7, 0xa2, 0x07, 0xed, 0xe1, 0xea, 0x38, 0xf1,
	0x0b, 0x08, 0x63, 0x08, 0x6d, 0xde, 0xad, 0xb1, 0xee, 0x52, 0x4e, 0x2a, 0xa1, 0xd1, 0x07, 0xc1,
	0x4e, 0xf4, 0x18, 0x43, 0xa9, 0x0f, 0xee, 0x72, 0xfe, 0xc7, 0x47, 0xa9, 0x0a, 0x92, 0x98, 0x6f,
	0xe5, 0xb7, 0x98, 0x5c, 0xcb, 0xae, 0xcb, 0x7e, 0x60, 0x68, 0x06, 0x21, 0xcd, 0x75, 0xa1, 0x8a,
	0x32, 0xf4, 0xf9, 0xe3, 0x12, 0x5a, 0xd4, 0xfd, 0x3c, 0x1a, 0x27, 0xdd, 0x04, 0xbf, 0x81, 0xe3,
	0x77, 0x90, 0xfa, 0xeb, 0x45, 0xcd, 0x75, 0x6b, 0xd0, 0xba, 0x61, 0x5a, 0xb8, 0xf2, 0x0a, 0xca,
	0xc7, 0x34, 0x0b, 0x68, 0x78, 0xd9, 0x75, 0xef, 0x3b, 0xf0, 0xa3, 0x4b, 0x13, 0xa8, 0xb8, 0xec,
	0xba, 0x2b, 0x9a, 0xbe, 0x7b, 0xd7, 0x73, 0x42, 0xdb, 0x28, 0xe5, 0xa4, 0x51, 0x78, 0x73, 0x66,
	0xf9, 0xf3, 0x9a, 0x19, 0x30, 0x99, 0xff, 0xe0, 0xf7, 0x53, 0x32, 0xc7, 0x32, 0x55, 0xfe, 0x23,
	0x7b, 0x3f, 0x91, 0x3a, 0x06, 0x61, 0x21, 0x7d, 0x06, 0x95, 0x02, 0x5d, 0x35, 0x70, 0xd3, 0xdc,
	0xc3, 0x1e, 0x48, 0xe5, 0xf3, 0x67, 0x5d, 0x3e, 0x3a, 0x9c, 0x29, 0xc1, 0x57, 0x4a, 0xec, 0xb3,
	0x05, 0x78, 0xda, 0x67, 0x2c, 0xd0, 0x6f, 0x51, 0x60, 0x22, 0xac, 0x2f, 0xdd, 0x45, 0x13, 0xc9,
	0x17, 0xc4, 0x7c, 0xcc, 0x9f, 0x11, 0xba, 0xc8, 0xee, 0x21, 0x33, 0xe9, 0x8c, 0x8b, 0x8f, 0x8b,
	0xad, 0xe3, 0x40, 0x52, 0xd0, 0x58, 0x64, 0x52, 0x4f, 0xd3, 0x31, 0xbd, 0xf6, 0x4d, 0xfa, 0x68,
	0xa1, 0x5e, 0xe4, 0x6f, 0x71, 0x00, 0x88, 0xb4, 0x88, 0x26, 0x19, 0x51, 0x3e, 0x82, 0xa8, 0x32,
	0x60, 0x60, 0x65, 0x84, 0x63, 0xd6, 0x27, 0x28, 0x10, 0x13, 0x1c, 0xae, 0x4c, 0xaf, 0xa3, 0x36,
	0x99, 0xa0, 0x22, 0x60, 0xa0, 0x3e, 0x46, 0xda, 0xe9, 0xac, 0x84, 0xa7, 0xa5, 0x56, 0xd1, 0xb9,
	0x34, 0xa4, 0x1a, 0x04, 0x4d, 0xf8, 0xac, 0xa4, 0x7f, 0x45, 0x7e, 0xff, 0x70, 0xa6, 0xad, 0xff,
	0x8b, 0x41, 0xd0, 0xac, 0x4f, 0x24, 0xe9, 0x6c, 0x04, 0x4d, 0x69, 0x09, 0x95, 0xd9, 0xb5, 0x26,
	0x03, 0x86, 0x0b, 0x42, 0x8f, 0xec, 0xb3, 0xed, 0x5d, 0x28, 0x25, 0x18, 0xa9, 0x4f, 0x51, 0x08,
	0x8a, 0x78, 0x57, 0xb3, 0x70, 0x1d, 0xd7, 0x35, 0x7b, 0x17, 0xe6, 0xa2, 0x61, 0xdc, 0x6e, 0xd3,
	0x5a, 0x86, 0xfa, 0x53, 0x6f, 0x06, 0xa5, 0x55, 0x0d, 0x73, 0xf1, 0x3b, 0x3f, 0x78, 0xb2, 0x50,
	0xfe, 0x1c, 0x2a, 0x2d, 0x1b, 0x46, 0x2d, 0xa1, 0xd2, 0xd9, 0x36, 0x3b, 0xa4, 0xe6, 0x76, 0xd2,
	0x04, 0x40, 0xec, 0x7d, 0x42, 0x6c, 0x0d, 0xc9, 0xeb, 0x38, 0xb8, 0x17, 0x0d, 0x5a, 0x70, 0x93,
	0x4c, 0x6d, 0xe7, 0xb2, 0xb4, 0x0d, 0xe4, 0x7e, 0x8d, 0x90, 0xfb, 0x02, 0x3a, 0xb7, 0x6c, 0x18,
	0xed, 0xa4, 0xaa, 0xd9, 0x26, 0xcf, 0x81, 0xc9, 0xe3, 0xaa, 0xc4, 0x36, 0x8b, 0x03, 0xe5, 0x5f,
	0x27, 0x94, 0xff, 0x55, 0x0e, 0x95, 0xea, 0x58, 0xc7, 0x50, 0xa6, 0x83, 0x3d, 0x18, 0x07, 0xfc,
	0xf0, 0x5f, 0xc6, 0xb0, 0xd3, 0x0e, 0x97, 0x35, 0x90, 0xbe, 0x4c, 0xb7, 0xb9, 0xd1, 0xc1, 0x6d,
	0xfa, 0x61, 0xce, 0xf7, 0xec, 0x1c, 0x03, 0xc7, 0x39, 0x07, 0x3d, 0xe8, 0x26, 0x63, 0xfb, 0x73,
	0xe8, 0xc2, 0x3a, 0x0e, 0x6a, 0x99, 0x50, 0x5d, 0x38, 0xe4, 0xba, 0x72, 0xf8, 0xfb, 0x84, 0x43,
	0x1d, 0x4d, 0xac, 0xda, 0xfa, 0x46, 0x2d, 0x11, 0x05, 0x16, 0x3a, 0x46, 0x11, 0xb1, 0xd4, 0x2a,
	0x15, 0x3b, 0x80, 0xe6, 0x77, 0x7f, 0xf0, 0x64, 0xa1, 0x72, 0x13, 0x0d, 0xb1, 0xb5, 0x6a, 0x04,
	0x0d, 0xb0, 0x60, 0x57, 0x42, 0xa3, 0xa2, 0xb3, 0xd3, 0x18, 0x7d, 0x0f, 0x6b, 0x7b, 0x38, 0x8e,
	0xd1, 0x97, 0x0f, 0x8e, 0xe4, 0x6f, 0xff, 0x20, 0xae, 0xbf, 0xa0, 0xf1, 0x8e, 0xd6, 0x66, 0x42,
	0x31, 0xfb, 0x37, 0xfa, 0xd1, 0x39, 0x12, 0xf0, 0x18, 0x5b, 0x78, 0x8f, 0x12, 0x7e, 0xac, 0xf5,
	0x36, 0x1a, 0x6c, 0x44, 0x3f, 0x96, 0x5a, 0x98, 0xbf, 0xd1, 0x79, 0xe3, 0x97, 0x46, 0x85, 0x82,
	0x93, 0x3a, 0xc5, 0x86, 0x07, 0x83, 0x1c, 0x7b, 0xdb, 0xf4, 0x2c, 0xd1, 0x15, 0x0a, 0xac, 0x0d,
	0x5e, 0x23, 0xfa, 0xd3, 0x68, 0x80, 0x60, 0xd0, 0xe2, 0xe2, 0xad, 0xb8, 0x56, 0x6a, 0x10, 0x8a,
	0x3d, 0xa4, 0x79, 0x34, 0x04, 0x3f, 0xe9, 0xe3, 0xb3, 0xa3, 0xfe, 0xf2, 0x6c, 0xfc, 0x1b, 0xc1,
	0xb1, 0x11, 0xe0, 0xa7, 0x7d, 0xea, 0x0c, 0xb2, 0xfc, 0x6e, 0x0e, 0x4d, 0xaf, 0xe3, 0x20, 0x73,
	0x60, 0xf7, 0xcf, 0x36, 0xb0, 0xf8, 0x89, 0xb4, 0x13, 0x8f, 0x10, 0xcc, 0xf8, 0xb5, 0x1f, 0x3e,
	0x59, 0x80, 0x0f, 0xf3, 0xbf, 0xfa, 0xc3, 0xc8, 0x1c, 0xd3, 0x60, 0x0e, 0xee, 0x14, 0xf4, 0xf1,
	0x5a, 0x42, 0xb3, 0xf2, 0xab, 0x39, 0x5a, 0x93, 0x2c, 0xfe, 0x94, 0xa2, 0x2f, 0x5d, 0x44, 0xf9,
	0xf8, 0xa7, 0x29, 0x69, 0xe2, 0x33, 0xc2, 0x7f, 0x4f, 0x53, 0x7a, 0x16, 0x15, 0xe1, 0x03, 0x69,
	0x2d, 0xc0, 0xe2, 0x13, 0x82, 0xa3, 0xbc, 0x11, 0xa6, 0xdc, 0x4b, 0x99, 0xbf, 0xc1, 0x48, 0xef,
	0x88, 0xda, 0x7f, 0x7a, 0xb1, 0x72, 0x70, 0x24, 0xff, 0xa7, 0x1f, 0xa6, 0x3f, 0x86, 0x10, 0xf1,
	0xfd, 0x15, 0x0f, 0xfd, 0x94, 0xee, 0x58, 0x44, 0x6d, 0xf0, 0x7b, 0xcd, 0xba, 0xd3, 0x9c, 0xf5,
	0xb1, 0x47, 0x64, 0xf2, 0x33, 0x15, 0xbb, 0x92, 0x67, 0xe3, 0x0a, 0x9c, 0x9f, 0xb9, 0xd9, 0x70,
	0x9a, 0x9a, 0xdd, 0x98, 0xfd, 0xf4, 0x7c, 0x10, 0xcc, 0xea, 0x8e, 0x75, 0x83, 0x93, 0xb8, 0xc1,
	0x49, 0xdc, 0xc8, 0xfa, 0x05, 0xeb, 0xad, 0x21, 0x80, 0x53, 0xfe, 0x77, 0x00, 0x00, 0x00, 0xff,
	0xff, 0x06, 0x39, 0xd9, 0x06, 0xe0, 0x7a, 0x00, 0x00,
}
