// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/tt/online_recall.proto

package online_recall // import "golang.52tt.com/protocol/services/rcmd/persona/tt/online_recall"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type AlgoItem struct {
	Ts                   string   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	ItemId               string   `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemOwnerUid         string   `protobuf:"bytes,3,opt,name=item_owner_uid,json=itemOwnerUid,proto3" json:"item_owner_uid,omitempty"`
	Score                string   `protobuf:"bytes,4,opt,name=score,proto3" json:"score,omitempty"`
	ActionType           string   `protobuf:"bytes,5,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	RecommendType        []string `protobuf:"bytes,6,rep,name=recommend_type,json=recommendType,proto3" json:"recommend_type,omitempty"`
	Fscore               float32  `protobuf:"fixed32,7,opt,name=fscore,proto3" json:"fscore,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AlgoItem) Reset()         { *m = AlgoItem{} }
func (m *AlgoItem) String() string { return proto.CompactTextString(m) }
func (*AlgoItem) ProtoMessage()    {}
func (*AlgoItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{0}
}
func (m *AlgoItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlgoItem.Unmarshal(m, b)
}
func (m *AlgoItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlgoItem.Marshal(b, m, deterministic)
}
func (dst *AlgoItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlgoItem.Merge(dst, src)
}
func (m *AlgoItem) XXX_Size() int {
	return xxx_messageInfo_AlgoItem.Size(m)
}
func (m *AlgoItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AlgoItem.DiscardUnknown(m)
}

var xxx_messageInfo_AlgoItem proto.InternalMessageInfo

func (m *AlgoItem) GetTs() string {
	if m != nil {
		return m.Ts
	}
	return ""
}

func (m *AlgoItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *AlgoItem) GetItemOwnerUid() string {
	if m != nil {
		return m.ItemOwnerUid
	}
	return ""
}

func (m *AlgoItem) GetScore() string {
	if m != nil {
		return m.Score
	}
	return ""
}

func (m *AlgoItem) GetActionType() string {
	if m != nil {
		return m.ActionType
	}
	return ""
}

func (m *AlgoItem) GetRecommendType() []string {
	if m != nil {
		return m.RecommendType
	}
	return nil
}

func (m *AlgoItem) GetFscore() float32 {
	if m != nil {
		return m.Fscore
	}
	return 0
}

type AlgoList struct {
	Items                []*AlgoItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Id                   string      `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AlgoList) Reset()         { *m = AlgoList{} }
func (m *AlgoList) String() string { return proto.CompactTextString(m) }
func (*AlgoList) ProtoMessage()    {}
func (*AlgoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{1}
}
func (m *AlgoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlgoList.Unmarshal(m, b)
}
func (m *AlgoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlgoList.Marshal(b, m, deterministic)
}
func (dst *AlgoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlgoList.Merge(dst, src)
}
func (m *AlgoList) XXX_Size() int {
	return xxx_messageInfo_AlgoList.Size(m)
}
func (m *AlgoList) XXX_DiscardUnknown() {
	xxx_messageInfo_AlgoList.DiscardUnknown(m)
}

var xxx_messageInfo_AlgoList proto.InternalMessageInfo

func (m *AlgoList) GetItems() []*AlgoItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *AlgoList) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

// 在线有效序列Item
type AlgoOnlineItem struct {
	Ts                   string   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	ItemId               string   `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemOwnerUid         string   `protobuf:"bytes,3,opt,name=item_owner_uid,json=itemOwnerUid,proto3" json:"item_owner_uid,omitempty"`
	Score                string   `protobuf:"bytes,4,opt,name=score,proto3" json:"score,omitempty"`
	ActionType           string   `protobuf:"bytes,5,opt,name=action_type,json=actionType,proto3" json:"action_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AlgoOnlineItem) Reset()         { *m = AlgoOnlineItem{} }
func (m *AlgoOnlineItem) String() string { return proto.CompactTextString(m) }
func (*AlgoOnlineItem) ProtoMessage()    {}
func (*AlgoOnlineItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{2}
}
func (m *AlgoOnlineItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlgoOnlineItem.Unmarshal(m, b)
}
func (m *AlgoOnlineItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlgoOnlineItem.Marshal(b, m, deterministic)
}
func (dst *AlgoOnlineItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlgoOnlineItem.Merge(dst, src)
}
func (m *AlgoOnlineItem) XXX_Size() int {
	return xxx_messageInfo_AlgoOnlineItem.Size(m)
}
func (m *AlgoOnlineItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AlgoOnlineItem.DiscardUnknown(m)
}

var xxx_messageInfo_AlgoOnlineItem proto.InternalMessageInfo

func (m *AlgoOnlineItem) GetTs() string {
	if m != nil {
		return m.Ts
	}
	return ""
}

func (m *AlgoOnlineItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *AlgoOnlineItem) GetItemOwnerUid() string {
	if m != nil {
		return m.ItemOwnerUid
	}
	return ""
}

func (m *AlgoOnlineItem) GetScore() string {
	if m != nil {
		return m.Score
	}
	return ""
}

func (m *AlgoOnlineItem) GetActionType() string {
	if m != nil {
		return m.ActionType
	}
	return ""
}

// 广场有效序列
type SquarePostOnlineSequence struct {
	Items                []*AlgoOnlineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SquarePostOnlineSequence) Reset()         { *m = SquarePostOnlineSequence{} }
func (m *SquarePostOnlineSequence) String() string { return proto.CompactTextString(m) }
func (*SquarePostOnlineSequence) ProtoMessage()    {}
func (*SquarePostOnlineSequence) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{3}
}
func (m *SquarePostOnlineSequence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOnlineSequence.Unmarshal(m, b)
}
func (m *SquarePostOnlineSequence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOnlineSequence.Marshal(b, m, deterministic)
}
func (dst *SquarePostOnlineSequence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOnlineSequence.Merge(dst, src)
}
func (m *SquarePostOnlineSequence) XXX_Size() int {
	return xxx_messageInfo_SquarePostOnlineSequence.Size(m)
}
func (m *SquarePostOnlineSequence) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOnlineSequence.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOnlineSequence proto.InternalMessageInfo

func (m *SquarePostOnlineSequence) GetItems() []*AlgoOnlineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type SquarePostOnlineSequence_Append struct {
	Items                []*AlgoOnlineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SquarePostOnlineSequence_Append) Reset()         { *m = SquarePostOnlineSequence_Append{} }
func (m *SquarePostOnlineSequence_Append) String() string { return proto.CompactTextString(m) }
func (*SquarePostOnlineSequence_Append) ProtoMessage()    {}
func (*SquarePostOnlineSequence_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{3, 0}
}
func (m *SquarePostOnlineSequence_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOnlineSequence_Append.Unmarshal(m, b)
}
func (m *SquarePostOnlineSequence_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOnlineSequence_Append.Marshal(b, m, deterministic)
}
func (dst *SquarePostOnlineSequence_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOnlineSequence_Append.Merge(dst, src)
}
func (m *SquarePostOnlineSequence_Append) XXX_Size() int {
	return xxx_messageInfo_SquarePostOnlineSequence_Append.Size(m)
}
func (m *SquarePostOnlineSequence_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOnlineSequence_Append.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOnlineSequence_Append proto.InternalMessageInfo

func (m *SquarePostOnlineSequence_Append) GetItems() []*AlgoOnlineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场uid2pid有效序列
type SquarePostUid2PidOnlineSequence struct {
	Items                []*AlgoOnlineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SquarePostUid2PidOnlineSequence) Reset()         { *m = SquarePostUid2PidOnlineSequence{} }
func (m *SquarePostUid2PidOnlineSequence) String() string { return proto.CompactTextString(m) }
func (*SquarePostUid2PidOnlineSequence) ProtoMessage()    {}
func (*SquarePostUid2PidOnlineSequence) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{4}
}
func (m *SquarePostUid2PidOnlineSequence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostUid2PidOnlineSequence.Unmarshal(m, b)
}
func (m *SquarePostUid2PidOnlineSequence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostUid2PidOnlineSequence.Marshal(b, m, deterministic)
}
func (dst *SquarePostUid2PidOnlineSequence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostUid2PidOnlineSequence.Merge(dst, src)
}
func (m *SquarePostUid2PidOnlineSequence) XXX_Size() int {
	return xxx_messageInfo_SquarePostUid2PidOnlineSequence.Size(m)
}
func (m *SquarePostUid2PidOnlineSequence) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostUid2PidOnlineSequence.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostUid2PidOnlineSequence proto.InternalMessageInfo

func (m *SquarePostUid2PidOnlineSequence) GetItems() []*AlgoOnlineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type SquarePostUid2PidOnlineSequence_Append struct {
	Items                []*AlgoOnlineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SquarePostUid2PidOnlineSequence_Append) Reset() {
	*m = SquarePostUid2PidOnlineSequence_Append{}
}
func (m *SquarePostUid2PidOnlineSequence_Append) String() string { return proto.CompactTextString(m) }
func (*SquarePostUid2PidOnlineSequence_Append) ProtoMessage()    {}
func (*SquarePostUid2PidOnlineSequence_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{4, 0}
}
func (m *SquarePostUid2PidOnlineSequence_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostUid2PidOnlineSequence_Append.Unmarshal(m, b)
}
func (m *SquarePostUid2PidOnlineSequence_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostUid2PidOnlineSequence_Append.Marshal(b, m, deterministic)
}
func (dst *SquarePostUid2PidOnlineSequence_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostUid2PidOnlineSequence_Append.Merge(dst, src)
}
func (m *SquarePostUid2PidOnlineSequence_Append) XXX_Size() int {
	return xxx_messageInfo_SquarePostUid2PidOnlineSequence_Append.Size(m)
}
func (m *SquarePostUid2PidOnlineSequence_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostUid2PidOnlineSequence_Append.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostUid2PidOnlineSequence_Append proto.InternalMessageInfo

func (m *SquarePostUid2PidOnlineSequence_Append) GetItems() []*AlgoOnlineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场pid2post有效序列
type SquarePostPid2PostOnlineSequence struct {
	Items                []*AlgoOnlineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SquarePostPid2PostOnlineSequence) Reset()         { *m = SquarePostPid2PostOnlineSequence{} }
func (m *SquarePostPid2PostOnlineSequence) String() string { return proto.CompactTextString(m) }
func (*SquarePostPid2PostOnlineSequence) ProtoMessage()    {}
func (*SquarePostPid2PostOnlineSequence) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{5}
}
func (m *SquarePostPid2PostOnlineSequence) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostPid2PostOnlineSequence.Unmarshal(m, b)
}
func (m *SquarePostPid2PostOnlineSequence) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostPid2PostOnlineSequence.Marshal(b, m, deterministic)
}
func (dst *SquarePostPid2PostOnlineSequence) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostPid2PostOnlineSequence.Merge(dst, src)
}
func (m *SquarePostPid2PostOnlineSequence) XXX_Size() int {
	return xxx_messageInfo_SquarePostPid2PostOnlineSequence.Size(m)
}
func (m *SquarePostPid2PostOnlineSequence) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostPid2PostOnlineSequence.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostPid2PostOnlineSequence proto.InternalMessageInfo

func (m *SquarePostPid2PostOnlineSequence) GetItems() []*AlgoOnlineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type SquarePostPid2PostOnlineSequence_Append struct {
	Items                []*AlgoOnlineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SquarePostPid2PostOnlineSequence_Append) Reset() {
	*m = SquarePostPid2PostOnlineSequence_Append{}
}
func (m *SquarePostPid2PostOnlineSequence_Append) String() string { return proto.CompactTextString(m) }
func (*SquarePostPid2PostOnlineSequence_Append) ProtoMessage()    {}
func (*SquarePostPid2PostOnlineSequence_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{5, 0}
}
func (m *SquarePostPid2PostOnlineSequence_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostPid2PostOnlineSequence_Append.Unmarshal(m, b)
}
func (m *SquarePostPid2PostOnlineSequence_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostPid2PostOnlineSequence_Append.Marshal(b, m, deterministic)
}
func (dst *SquarePostPid2PostOnlineSequence_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostPid2PostOnlineSequence_Append.Merge(dst, src)
}
func (m *SquarePostPid2PostOnlineSequence_Append) XXX_Size() int {
	return xxx_messageInfo_SquarePostPid2PostOnlineSequence_Append.Size(m)
}
func (m *SquarePostPid2PostOnlineSequence_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostPid2PostOnlineSequence_Append.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostPid2PostOnlineSequence_Append proto.InternalMessageInfo

func (m *SquarePostPid2PostOnlineSequence_Append) GetItems() []*AlgoOnlineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 离线算法序列Item
type AlgoOfflineItem struct {
	ItemId               string   `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemOwnerUid         string   `protobuf:"bytes,3,opt,name=item_owner_uid,json=itemOwnerUid,proto3" json:"item_owner_uid,omitempty"`
	Score                string   `protobuf:"bytes,4,opt,name=score,proto3" json:"score,omitempty"`
	RecommendType        []string `protobuf:"bytes,6,rep,name=recommend_type,json=recommendType,proto3" json:"recommend_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AlgoOfflineItem) Reset()         { *m = AlgoOfflineItem{} }
func (m *AlgoOfflineItem) String() string { return proto.CompactTextString(m) }
func (*AlgoOfflineItem) ProtoMessage()    {}
func (*AlgoOfflineItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{6}
}
func (m *AlgoOfflineItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlgoOfflineItem.Unmarshal(m, b)
}
func (m *AlgoOfflineItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlgoOfflineItem.Marshal(b, m, deterministic)
}
func (dst *AlgoOfflineItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlgoOfflineItem.Merge(dst, src)
}
func (m *AlgoOfflineItem) XXX_Size() int {
	return xxx_messageInfo_AlgoOfflineItem.Size(m)
}
func (m *AlgoOfflineItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AlgoOfflineItem.DiscardUnknown(m)
}

var xxx_messageInfo_AlgoOfflineItem proto.InternalMessageInfo

func (m *AlgoOfflineItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *AlgoOfflineItem) GetItemOwnerUid() string {
	if m != nil {
		return m.ItemOwnerUid
	}
	return ""
}

func (m *AlgoOfflineItem) GetScore() string {
	if m != nil {
		return m.Score
	}
	return ""
}

func (m *AlgoOfflineItem) GetRecommendType() []string {
	if m != nil {
		return m.RecommendType
	}
	return nil
}

// 广场召回：离线item2vec
type SquarePostOfflineItem2Vec struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineItem2Vec) Reset()         { *m = SquarePostOfflineItem2Vec{} }
func (m *SquarePostOfflineItem2Vec) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineItem2Vec) ProtoMessage()    {}
func (*SquarePostOfflineItem2Vec) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{7}
}
func (m *SquarePostOfflineItem2Vec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineItem2Vec.Unmarshal(m, b)
}
func (m *SquarePostOfflineItem2Vec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineItem2Vec.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineItem2Vec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineItem2Vec.Merge(dst, src)
}
func (m *SquarePostOfflineItem2Vec) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineItem2Vec.Size(m)
}
func (m *SquarePostOfflineItem2Vec) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineItem2Vec.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineItem2Vec proto.InternalMessageInfo

func (m *SquarePostOfflineItem2Vec) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线itemcf（经典）
type SquarePostOfflineItemCf struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineItemCf) Reset()         { *m = SquarePostOfflineItemCf{} }
func (m *SquarePostOfflineItemCf) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineItemCf) ProtoMessage()    {}
func (*SquarePostOfflineItemCf) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{8}
}
func (m *SquarePostOfflineItemCf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineItemCf.Unmarshal(m, b)
}
func (m *SquarePostOfflineItemCf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineItemCf.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineItemCf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineItemCf.Merge(dst, src)
}
func (m *SquarePostOfflineItemCf) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineItemCf.Size(m)
}
func (m *SquarePostOfflineItemCf) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineItemCf.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineItemCf proto.InternalMessageInfo

func (m *SquarePostOfflineItemCf) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线usercf（经典）
type SquarePostOfflineUserCf struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineUserCf) Reset()         { *m = SquarePostOfflineUserCf{} }
func (m *SquarePostOfflineUserCf) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineUserCf) ProtoMessage()    {}
func (*SquarePostOfflineUserCf) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{9}
}
func (m *SquarePostOfflineUserCf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineUserCf.Unmarshal(m, b)
}
func (m *SquarePostOfflineUserCf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineUserCf.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineUserCf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineUserCf.Merge(dst, src)
}
func (m *SquarePostOfflineUserCf) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineUserCf.Size(m)
}
func (m *SquarePostOfflineUserCf) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineUserCf.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineUserCf proto.InternalMessageInfo

func (m *SquarePostOfflineUserCf) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线Als
type SquarePostOfflineAls struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineAls) Reset()         { *m = SquarePostOfflineAls{} }
func (m *SquarePostOfflineAls) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineAls) ProtoMessage()    {}
func (*SquarePostOfflineAls) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{10}
}
func (m *SquarePostOfflineAls) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineAls.Unmarshal(m, b)
}
func (m *SquarePostOfflineAls) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineAls.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineAls) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineAls.Merge(dst, src)
}
func (m *SquarePostOfflineAls) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineAls.Size(m)
}
func (m *SquarePostOfflineAls) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineAls.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineAls proto.InternalMessageInfo

func (m *SquarePostOfflineAls) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线Bert
type SquarePostOfflineBert struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineBert) Reset()         { *m = SquarePostOfflineBert{} }
func (m *SquarePostOfflineBert) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineBert) ProtoMessage()    {}
func (*SquarePostOfflineBert) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{11}
}
func (m *SquarePostOfflineBert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineBert.Unmarshal(m, b)
}
func (m *SquarePostOfflineBert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineBert.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineBert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineBert.Merge(dst, src)
}
func (m *SquarePostOfflineBert) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineBert.Size(m)
}
func (m *SquarePostOfflineBert) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineBert.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineBert proto.InternalMessageInfo

func (m *SquarePostOfflineBert) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：在线itemcf（经典）
type SquarePostOnlineItemCf struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOnlineItemCf) Reset()         { *m = SquarePostOnlineItemCf{} }
func (m *SquarePostOnlineItemCf) String() string { return proto.CompactTextString(m) }
func (*SquarePostOnlineItemCf) ProtoMessage()    {}
func (*SquarePostOnlineItemCf) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{12}
}
func (m *SquarePostOnlineItemCf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOnlineItemCf.Unmarshal(m, b)
}
func (m *SquarePostOnlineItemCf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOnlineItemCf.Marshal(b, m, deterministic)
}
func (dst *SquarePostOnlineItemCf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOnlineItemCf.Merge(dst, src)
}
func (m *SquarePostOnlineItemCf) XXX_Size() int {
	return xxx_messageInfo_SquarePostOnlineItemCf.Size(m)
}
func (m *SquarePostOnlineItemCf) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOnlineItemCf.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOnlineItemCf proto.InternalMessageInfo

func (m *SquarePostOnlineItemCf) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：小时级item2vec召回
type SquarePostHourItem2Vec struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostHourItem2Vec) Reset()         { *m = SquarePostHourItem2Vec{} }
func (m *SquarePostHourItem2Vec) String() string { return proto.CompactTextString(m) }
func (*SquarePostHourItem2Vec) ProtoMessage()    {}
func (*SquarePostHourItem2Vec) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{13}
}
func (m *SquarePostHourItem2Vec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostHourItem2Vec.Unmarshal(m, b)
}
func (m *SquarePostHourItem2Vec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostHourItem2Vec.Marshal(b, m, deterministic)
}
func (dst *SquarePostHourItem2Vec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostHourItem2Vec.Merge(dst, src)
}
func (m *SquarePostHourItem2Vec) XXX_Size() int {
	return xxx_messageInfo_SquarePostHourItem2Vec.Size(m)
}
func (m *SquarePostHourItem2Vec) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostHourItem2Vec.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostHourItem2Vec proto.InternalMessageInfo

func (m *SquarePostHourItem2Vec) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：小时级bert召回
type SquarePostHourBert struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostHourBert) Reset()         { *m = SquarePostHourBert{} }
func (m *SquarePostHourBert) String() string { return proto.CompactTextString(m) }
func (*SquarePostHourBert) ProtoMessage()    {}
func (*SquarePostHourBert) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{14}
}
func (m *SquarePostHourBert) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostHourBert.Unmarshal(m, b)
}
func (m *SquarePostHourBert) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostHourBert.Marshal(b, m, deterministic)
}
func (dst *SquarePostHourBert) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostHourBert.Merge(dst, src)
}
func (m *SquarePostHourBert) XXX_Size() int {
	return xxx_messageInfo_SquarePostHourBert.Size(m)
}
func (m *SquarePostHourBert) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostHourBert.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostHourBert proto.InternalMessageInfo

func (m *SquarePostHourBert) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：小时级user2vec召回
type SquarePostHourUser2Vec struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostHourUser2Vec) Reset()         { *m = SquarePostHourUser2Vec{} }
func (m *SquarePostHourUser2Vec) String() string { return proto.CompactTextString(m) }
func (*SquarePostHourUser2Vec) ProtoMessage()    {}
func (*SquarePostHourUser2Vec) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{15}
}
func (m *SquarePostHourUser2Vec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostHourUser2Vec.Unmarshal(m, b)
}
func (m *SquarePostHourUser2Vec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostHourUser2Vec.Marshal(b, m, deterministic)
}
func (dst *SquarePostHourUser2Vec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostHourUser2Vec.Merge(dst, src)
}
func (m *SquarePostHourUser2Vec) XXX_Size() int {
	return xxx_messageInfo_SquarePostHourUser2Vec.Size(m)
}
func (m *SquarePostHourUser2Vec) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostHourUser2Vec.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostHourUser2Vec proto.InternalMessageInfo

func (m *SquarePostHourUser2Vec) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线关系链
type SquarePostOfflineRelation struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineRelation) Reset()         { *m = SquarePostOfflineRelation{} }
func (m *SquarePostOfflineRelation) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineRelation) ProtoMessage()    {}
func (*SquarePostOfflineRelation) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{16}
}
func (m *SquarePostOfflineRelation) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineRelation.Unmarshal(m, b)
}
func (m *SquarePostOfflineRelation) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineRelation.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineRelation) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineRelation.Merge(dst, src)
}
func (m *SquarePostOfflineRelation) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineRelation.Size(m)
}
func (m *SquarePostOfflineRelation) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineRelation.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineRelation proto.InternalMessageInfo

func (m *SquarePostOfflineRelation) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：近线user2vec召回
type SquarePostStreamUser2Vec struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostStreamUser2Vec) Reset()         { *m = SquarePostStreamUser2Vec{} }
func (m *SquarePostStreamUser2Vec) String() string { return proto.CompactTextString(m) }
func (*SquarePostStreamUser2Vec) ProtoMessage()    {}
func (*SquarePostStreamUser2Vec) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{17}
}
func (m *SquarePostStreamUser2Vec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostStreamUser2Vec.Unmarshal(m, b)
}
func (m *SquarePostStreamUser2Vec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostStreamUser2Vec.Marshal(b, m, deterministic)
}
func (dst *SquarePostStreamUser2Vec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostStreamUser2Vec.Merge(dst, src)
}
func (m *SquarePostStreamUser2Vec) XXX_Size() int {
	return xxx_messageInfo_SquarePostStreamUser2Vec.Size(m)
}
func (m *SquarePostStreamUser2Vec) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostStreamUser2Vec.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostStreamUser2Vec proto.InternalMessageInfo

func (m *SquarePostStreamUser2Vec) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：近线user2pvec召回
type SquarePostStreamUser2Pvec struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostStreamUser2Pvec) Reset()         { *m = SquarePostStreamUser2Pvec{} }
func (m *SquarePostStreamUser2Pvec) String() string { return proto.CompactTextString(m) }
func (*SquarePostStreamUser2Pvec) ProtoMessage()    {}
func (*SquarePostStreamUser2Pvec) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{18}
}
func (m *SquarePostStreamUser2Pvec) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostStreamUser2Pvec.Unmarshal(m, b)
}
func (m *SquarePostStreamUser2Pvec) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostStreamUser2Pvec.Marshal(b, m, deterministic)
}
func (dst *SquarePostStreamUser2Pvec) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostStreamUser2Pvec.Merge(dst, src)
}
func (m *SquarePostStreamUser2Pvec) XXX_Size() int {
	return xxx_messageInfo_SquarePostStreamUser2Pvec.Size(m)
}
func (m *SquarePostStreamUser2Pvec) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostStreamUser2Pvec.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostStreamUser2Pvec proto.InternalMessageInfo

func (m *SquarePostStreamUser2Pvec) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：近线user2vecpub召回
type SquarePostStreamUser2Vecpub struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostStreamUser2Vecpub) Reset()         { *m = SquarePostStreamUser2Vecpub{} }
func (m *SquarePostStreamUser2Vecpub) String() string { return proto.CompactTextString(m) }
func (*SquarePostStreamUser2Vecpub) ProtoMessage()    {}
func (*SquarePostStreamUser2Vecpub) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{19}
}
func (m *SquarePostStreamUser2Vecpub) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostStreamUser2Vecpub.Unmarshal(m, b)
}
func (m *SquarePostStreamUser2Vecpub) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostStreamUser2Vecpub.Marshal(b, m, deterministic)
}
func (dst *SquarePostStreamUser2Vecpub) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostStreamUser2Vecpub.Merge(dst, src)
}
func (m *SquarePostStreamUser2Vecpub) XXX_Size() int {
	return xxx_messageInfo_SquarePostStreamUser2Vecpub.Size(m)
}
func (m *SquarePostStreamUser2Vecpub) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostStreamUser2Vecpub.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostStreamUser2Vecpub proto.InternalMessageInfo

func (m *SquarePostStreamUser2Vecpub) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：小时级CDTOM召回
type SquarePostHourCDTOM struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostHourCDTOM) Reset()         { *m = SquarePostHourCDTOM{} }
func (m *SquarePostHourCDTOM) String() string { return proto.CompactTextString(m) }
func (*SquarePostHourCDTOM) ProtoMessage()    {}
func (*SquarePostHourCDTOM) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{20}
}
func (m *SquarePostHourCDTOM) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostHourCDTOM.Unmarshal(m, b)
}
func (m *SquarePostHourCDTOM) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostHourCDTOM.Marshal(b, m, deterministic)
}
func (dst *SquarePostHourCDTOM) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostHourCDTOM.Merge(dst, src)
}
func (m *SquarePostHourCDTOM) XXX_Size() int {
	return xxx_messageInfo_SquarePostHourCDTOM.Size(m)
}
func (m *SquarePostHourCDTOM) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostHourCDTOM.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostHourCDTOM proto.InternalMessageInfo

func (m *SquarePostHourCDTOM) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线publisher召回（usercf-u2p）
type SquarePostPublisherUserCfU2P struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostPublisherUserCfU2P) Reset()         { *m = SquarePostPublisherUserCfU2P{} }
func (m *SquarePostPublisherUserCfU2P) String() string { return proto.CompactTextString(m) }
func (*SquarePostPublisherUserCfU2P) ProtoMessage()    {}
func (*SquarePostPublisherUserCfU2P) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{21}
}
func (m *SquarePostPublisherUserCfU2P) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostPublisherUserCfU2P.Unmarshal(m, b)
}
func (m *SquarePostPublisherUserCfU2P) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostPublisherUserCfU2P.Marshal(b, m, deterministic)
}
func (dst *SquarePostPublisherUserCfU2P) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostPublisherUserCfU2P.Merge(dst, src)
}
func (m *SquarePostPublisherUserCfU2P) XXX_Size() int {
	return xxx_messageInfo_SquarePostPublisherUserCfU2P.Size(m)
}
func (m *SquarePostPublisherUserCfU2P) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostPublisherUserCfU2P.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostPublisherUserCfU2P proto.InternalMessageInfo

func (m *SquarePostPublisherUserCfU2P) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线高质池
type SquarePostOfflineHighQuality struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineHighQuality) Reset()         { *m = SquarePostOfflineHighQuality{} }
func (m *SquarePostOfflineHighQuality) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineHighQuality) ProtoMessage()    {}
func (*SquarePostOfflineHighQuality) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{22}
}
func (m *SquarePostOfflineHighQuality) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineHighQuality.Unmarshal(m, b)
}
func (m *SquarePostOfflineHighQuality) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineHighQuality.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineHighQuality) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineHighQuality.Merge(dst, src)
}
func (m *SquarePostOfflineHighQuality) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineHighQuality.Size(m)
}
func (m *SquarePostOfflineHighQuality) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineHighQuality.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineHighQuality proto.InternalMessageInfo

func (m *SquarePostOfflineHighQuality) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：在线热点（分用户属性）
type SquarePostOnlineHot struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOnlineHot) Reset()         { *m = SquarePostOnlineHot{} }
func (m *SquarePostOnlineHot) String() string { return proto.CompactTextString(m) }
func (*SquarePostOnlineHot) ProtoMessage()    {}
func (*SquarePostOnlineHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{23}
}
func (m *SquarePostOnlineHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOnlineHot.Unmarshal(m, b)
}
func (m *SquarePostOnlineHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOnlineHot.Marshal(b, m, deterministic)
}
func (dst *SquarePostOnlineHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOnlineHot.Merge(dst, src)
}
func (m *SquarePostOnlineHot) XXX_Size() int {
	return xxx_messageInfo_SquarePostOnlineHot.Size(m)
}
func (m *SquarePostOnlineHot) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOnlineHot.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOnlineHot proto.InternalMessageInfo

func (m *SquarePostOnlineHot) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：实时热门（全局）
type SquarePostRealTimeHot struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostRealTimeHot) Reset()         { *m = SquarePostRealTimeHot{} }
func (m *SquarePostRealTimeHot) String() string { return proto.CompactTextString(m) }
func (*SquarePostRealTimeHot) ProtoMessage()    {}
func (*SquarePostRealTimeHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{24}
}
func (m *SquarePostRealTimeHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostRealTimeHot.Unmarshal(m, b)
}
func (m *SquarePostRealTimeHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostRealTimeHot.Marshal(b, m, deterministic)
}
func (dst *SquarePostRealTimeHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostRealTimeHot.Merge(dst, src)
}
func (m *SquarePostRealTimeHot) XXX_Size() int {
	return xxx_messageInfo_SquarePostRealTimeHot.Size(m)
}
func (m *SquarePostRealTimeHot) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostRealTimeHot.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostRealTimeHot proto.InternalMessageInfo

func (m *SquarePostRealTimeHot) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type SquarePostRealTimeHot_Append struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostRealTimeHot_Append) Reset()         { *m = SquarePostRealTimeHot_Append{} }
func (m *SquarePostRealTimeHot_Append) String() string { return proto.CompactTextString(m) }
func (*SquarePostRealTimeHot_Append) ProtoMessage()    {}
func (*SquarePostRealTimeHot_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{24, 0}
}
func (m *SquarePostRealTimeHot_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostRealTimeHot_Append.Unmarshal(m, b)
}
func (m *SquarePostRealTimeHot_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostRealTimeHot_Append.Marshal(b, m, deterministic)
}
func (dst *SquarePostRealTimeHot_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostRealTimeHot_Append.Merge(dst, src)
}
func (m *SquarePostRealTimeHot_Append) XXX_Size() int {
	return xxx_messageInfo_SquarePostRealTimeHot_Append.Size(m)
}
func (m *SquarePostRealTimeHot_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostRealTimeHot_Append.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostRealTimeHot_Append proto.InternalMessageInfo

func (m *SquarePostRealTimeHot_Append) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：离线热门
type SquarePostOfflineHot struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostOfflineHot) Reset()         { *m = SquarePostOfflineHot{} }
func (m *SquarePostOfflineHot) String() string { return proto.CompactTextString(m) }
func (*SquarePostOfflineHot) ProtoMessage()    {}
func (*SquarePostOfflineHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{25}
}
func (m *SquarePostOfflineHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostOfflineHot.Unmarshal(m, b)
}
func (m *SquarePostOfflineHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostOfflineHot.Marshal(b, m, deterministic)
}
func (dst *SquarePostOfflineHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostOfflineHot.Merge(dst, src)
}
func (m *SquarePostOfflineHot) XXX_Size() int {
	return xxx_messageInfo_SquarePostOfflineHot.Size(m)
}
func (m *SquarePostOfflineHot) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostOfflineHot.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostOfflineHot proto.InternalMessageInfo

func (m *SquarePostOfflineHot) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

// 广场召回：近线热点
type SquarePostNearlineHot struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostNearlineHot) Reset()         { *m = SquarePostNearlineHot{} }
func (m *SquarePostNearlineHot) String() string { return proto.CompactTextString(m) }
func (*SquarePostNearlineHot) ProtoMessage()    {}
func (*SquarePostNearlineHot) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{26}
}
func (m *SquarePostNearlineHot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostNearlineHot.Unmarshal(m, b)
}
func (m *SquarePostNearlineHot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostNearlineHot.Marshal(b, m, deterministic)
}
func (dst *SquarePostNearlineHot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostNearlineHot.Merge(dst, src)
}
func (m *SquarePostNearlineHot) XXX_Size() int {
	return xxx_messageInfo_SquarePostNearlineHot.Size(m)
}
func (m *SquarePostNearlineHot) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostNearlineHot.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostNearlineHot proto.InternalMessageInfo

func (m *SquarePostNearlineHot) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type SquarePostNearlineHot_Append struct {
	Items                []*AlgoOfflineItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *SquarePostNearlineHot_Append) Reset()         { *m = SquarePostNearlineHot_Append{} }
func (m *SquarePostNearlineHot_Append) String() string { return proto.CompactTextString(m) }
func (*SquarePostNearlineHot_Append) ProtoMessage()    {}
func (*SquarePostNearlineHot_Append) Descriptor() ([]byte, []int) {
	return fileDescriptor_online_recall_9d9b38ce73b306ba, []int{26, 0}
}
func (m *SquarePostNearlineHot_Append) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SquarePostNearlineHot_Append.Unmarshal(m, b)
}
func (m *SquarePostNearlineHot_Append) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SquarePostNearlineHot_Append.Marshal(b, m, deterministic)
}
func (dst *SquarePostNearlineHot_Append) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SquarePostNearlineHot_Append.Merge(dst, src)
}
func (m *SquarePostNearlineHot_Append) XXX_Size() int {
	return xxx_messageInfo_SquarePostNearlineHot_Append.Size(m)
}
func (m *SquarePostNearlineHot_Append) XXX_DiscardUnknown() {
	xxx_messageInfo_SquarePostNearlineHot_Append.DiscardUnknown(m)
}

var xxx_messageInfo_SquarePostNearlineHot_Append proto.InternalMessageInfo

func (m *SquarePostNearlineHot_Append) GetItems() []*AlgoOfflineItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func init() {
	proto.RegisterType((*AlgoItem)(nil), "rcmd.persona.tt.online_recall.AlgoItem")
	proto.RegisterType((*AlgoList)(nil), "rcmd.persona.tt.online_recall.AlgoList")
	proto.RegisterType((*AlgoOnlineItem)(nil), "rcmd.persona.tt.online_recall.AlgoOnlineItem")
	proto.RegisterType((*SquarePostOnlineSequence)(nil), "rcmd.persona.tt.online_recall.SquarePostOnlineSequence")
	proto.RegisterType((*SquarePostOnlineSequence_Append)(nil), "rcmd.persona.tt.online_recall.SquarePostOnlineSequence.Append")
	proto.RegisterType((*SquarePostUid2PidOnlineSequence)(nil), "rcmd.persona.tt.online_recall.SquarePostUid2PidOnlineSequence")
	proto.RegisterType((*SquarePostUid2PidOnlineSequence_Append)(nil), "rcmd.persona.tt.online_recall.SquarePostUid2PidOnlineSequence.Append")
	proto.RegisterType((*SquarePostPid2PostOnlineSequence)(nil), "rcmd.persona.tt.online_recall.SquarePostPid2PostOnlineSequence")
	proto.RegisterType((*SquarePostPid2PostOnlineSequence_Append)(nil), "rcmd.persona.tt.online_recall.SquarePostPid2PostOnlineSequence.Append")
	proto.RegisterType((*AlgoOfflineItem)(nil), "rcmd.persona.tt.online_recall.AlgoOfflineItem")
	proto.RegisterType((*SquarePostOfflineItem2Vec)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineItem2Vec")
	proto.RegisterType((*SquarePostOfflineItemCf)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineItemCf")
	proto.RegisterType((*SquarePostOfflineUserCf)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineUserCf")
	proto.RegisterType((*SquarePostOfflineAls)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineAls")
	proto.RegisterType((*SquarePostOfflineBert)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineBert")
	proto.RegisterType((*SquarePostOnlineItemCf)(nil), "rcmd.persona.tt.online_recall.SquarePostOnlineItemCf")
	proto.RegisterType((*SquarePostHourItem2Vec)(nil), "rcmd.persona.tt.online_recall.SquarePostHourItem2Vec")
	proto.RegisterType((*SquarePostHourBert)(nil), "rcmd.persona.tt.online_recall.SquarePostHourBert")
	proto.RegisterType((*SquarePostHourUser2Vec)(nil), "rcmd.persona.tt.online_recall.SquarePostHourUser2Vec")
	proto.RegisterType((*SquarePostOfflineRelation)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineRelation")
	proto.RegisterType((*SquarePostStreamUser2Vec)(nil), "rcmd.persona.tt.online_recall.SquarePostStreamUser2Vec")
	proto.RegisterType((*SquarePostStreamUser2Pvec)(nil), "rcmd.persona.tt.online_recall.SquarePostStreamUser2Pvec")
	proto.RegisterType((*SquarePostStreamUser2Vecpub)(nil), "rcmd.persona.tt.online_recall.SquarePostStreamUser2Vecpub")
	proto.RegisterType((*SquarePostHourCDTOM)(nil), "rcmd.persona.tt.online_recall.SquarePostHourCDTOM")
	proto.RegisterType((*SquarePostPublisherUserCfU2P)(nil), "rcmd.persona.tt.online_recall.SquarePostPublisherUserCfU2p")
	proto.RegisterType((*SquarePostOfflineHighQuality)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineHighQuality")
	proto.RegisterType((*SquarePostOnlineHot)(nil), "rcmd.persona.tt.online_recall.SquarePostOnlineHot")
	proto.RegisterType((*SquarePostRealTimeHot)(nil), "rcmd.persona.tt.online_recall.SquarePostRealTimeHot")
	proto.RegisterType((*SquarePostRealTimeHot_Append)(nil), "rcmd.persona.tt.online_recall.SquarePostRealTimeHot.Append")
	proto.RegisterType((*SquarePostOfflineHot)(nil), "rcmd.persona.tt.online_recall.SquarePostOfflineHot")
	proto.RegisterType((*SquarePostNearlineHot)(nil), "rcmd.persona.tt.online_recall.SquarePostNearlineHot")
	proto.RegisterType((*SquarePostNearlineHot_Append)(nil), "rcmd.persona.tt.online_recall.SquarePostNearlineHot.Append")
}

func init() {
	proto.RegisterFile("rcmd/persona/tt/online_recall.proto", fileDescriptor_online_recall_9d9b38ce73b306ba)
}

var fileDescriptor_online_recall_9d9b38ce73b306ba = []byte{
	// 1107 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x98, 0xcd, 0x6f, 0xdc, 0xc4,
	0x1b, 0xc7, 0xe5, 0x4d, 0x93, 0xfc, 0x3a, 0x6d, 0xf3, 0x13, 0xa6, 0x4d, 0x26, 0x69, 0x43, 0x22,
	0x37, 0xa5, 0x01, 0x54, 0xaf, 0xb4, 0x15, 0x48, 0x89, 0x44, 0x50, 0x52, 0x0e, 0xa9, 0xd4, 0x92,
	0x74, 0x93, 0x20, 0xc1, 0xc5, 0x78, 0xed, 0xd9, 0xdd, 0x91, 0x6c, 0x8f, 0x33, 0x1e, 0x07, 0xe5,
	0xe6, 0x23, 0x2f, 0xa9, 0x28, 0x45, 0x2c, 0x42, 0xb4, 0x07, 0x04, 0x8a, 0x40, 0x42, 0x11, 0xb4,
	0xb2, 0x94, 0x03, 0xc7, 0xa4, 0xe2, 0xc6, 0x85, 0x03, 0x7f, 0x00, 0xdc, 0x79, 0x09, 0xa4, 0x14,
	0x0e, 0x68, 0x6c, 0xaf, 0x77, 0xdd, 0x75, 0x56, 0x91, 0xaa, 0x91, 0xe8, 0xd1, 0xf3, 0xf2, 0xcc,
	0xe7, 0xf9, 0x3e, 0xf3, 0xcc, 0xf3, 0xc8, 0xe0, 0x2c, 0x35, 0x6c, 0xb3, 0xe8, 0x22, 0xea, 0x11,
	0x47, 0x2f, 0x32, 0x56, 0x24, 0x8e, 0x85, 0x1d, 0xa4, 0x51, 0x64, 0xe8, 0x96, 0xa5, 0xba, 0x94,
	0x30, 0x22, 0x8f, 0xf2, 0x45, 0x6a, 0xb2, 0x48, 0x65, 0x4c, 0xcd, 0x2c, 0x1a, 0x19, 0xc9, 0xd8,
	0x20, 0x2e, 0xc3, 0xc4, 0xf1, 0xe2, 0xad, 0xca, 0xf7, 0x12, 0xf8, 0xdf, 0xac, 0x55, 0x23, 0x97,
	0x19, 0xb2, 0xe5, 0x01, 0x50, 0x60, 0x1e, 0x94, 0xc6, 0xa5, 0xc9, 0xa3, 0xe5, 0x02, 0xf3, 0xe4,
	0x21, 0xd0, 0x8f, 0x19, 0xb2, 0x35, 0x6c, 0xc2, 0x42, 0x34, 0xd8, 0xc7, 0x3f, 0x2f, 0x9b, 0xf2,
	0x04, 0x18, 0x88, 0x26, 0xc8, 0x9b, 0x0e, 0xa2, 0x9a, 0x8f, 0x4d, 0xd8, 0x13, 0xcd, 0x1f, 0xe7,
	0xa3, 0x0b, 0x7c, 0x70, 0x05, 0x9b, 0xf2, 0x49, 0xd0, 0xeb, 0x19, 0x84, 0x22, 0x78, 0x24, 0x9a,
	0x8c, 0x3f, 0xe4, 0x31, 0x70, 0x4c, 0x37, 0x38, 0x82, 0xc6, 0xd6, 0x5d, 0x04, 0x7b, 0xa3, 0x39,
	0x10, 0x0f, 0x2d, 0xaf, 0xbb, 0x48, 0x3e, 0x07, 0x06, 0x28, 0x32, 0x88, 0x6d, 0x23, 0xc7, 0x8c,
	0xd7, 0xf4, 0x8d, 0xf7, 0x4c, 0x1e, 0x2d, 0x9f, 0x48, 0x47, 0xa3, 0x65, 0x83, 0xa0, 0xaf, 0x1a,
	0x9b, 0xef, 0x1f, 0x97, 0x26, 0x0b, 0xe5, 0xe4, 0x4b, 0x79, 0x2d, 0x76, 0xe8, 0x0a, 0xf6, 0x98,
	0xfc, 0x22, 0xe8, 0xe5, 0x44, 0xdc, 0xa7, 0x9e, 0xc9, 0x63, 0xa5, 0xf3, 0x6a, 0x57, 0xa1, 0xd4,
	0xa6, 0x10, 0xe5, 0x78, 0x17, 0xd7, 0x23, 0x75, 0xbd, 0x80, 0x4d, 0xa5, 0x21, 0x81, 0x01, 0xbe,
	0x66, 0x21, 0xda, 0xf5, 0xdf, 0x91, 0x4c, 0x79, 0x20, 0x01, 0xb8, 0xb4, 0xea, 0xeb, 0x14, 0x2d,
	0x12, 0x8f, 0xc5, 0x78, 0x4b, 0x68, 0xd5, 0x47, 0x8e, 0x81, 0xe4, 0x6b, 0x59, 0x11, 0x2e, 0x1c,
	0x42, 0x84, 0x96, 0x83, 0x73, 0xc7, 0x6f, 0x6e, 0x8e, 0x1e, 0xb1, 0xb0, 0xc7, 0xb6, 0x37, 0x47,
	0x87, 0x13, 0x61, 0x46, 0x34, 0xd0, 0x37, 0xeb, 0xba, 0xc8, 0x31, 0xe5, 0x2b, 0x8f, 0x64, 0xbc,
	0xff, 0x9d, 0xcd, 0xd1, 0x1e, 0xdd, 0x34, 0x13, 0xbb, 0xd3, 0xfd, 0x41, 0x08, 0x77, 0x77, 0xb6,
	0xa6, 0xa6, 0x9f, 0x0d, 0x42, 0xb8, 0xb3, 0xb3, 0x35, 0xf5, 0x56, 0x08, 0xa5, 0x9b, 0x21, 0x3c,
	0xed, 0x45, 0xce, 0x69, 0x2e, 0xf1, 0x98, 0x96, 0x58, 0xf4, 0x12, 0xff, 0x94, 0xb7, 0x0b, 0x60,
	0xac, 0xe5, 0xfc, 0x0a, 0x36, 0x4b, 0x8b, 0xd8, 0x7c, 0xbc, 0x35, 0xf8, 0x72, 0x77, 0x6b, 0x6a,
	0xba, 0x14, 0x84, 0xf0, 0x8b, 0xdd, 0x54, 0x83, 0xb3, 0xed, 0x1a, 0xf8, 0xd8, 0x2c, 0xb9, 0xd8,
	0xec, 0xd0, 0xe2, 0xdd, 0x02, 0x18, 0x6f, 0x69, 0xb1, 0xc8, 0xb5, 0x78, 0xec, 0x2f, 0xc4, 0xf5,
	0x7b, 0x5b, 0x53, 0xd3, 0x17, 0x83, 0x10, 0x6e, 0xdc, 0x4b, 0xc5, 0x98, 0x68, 0x17, 0xc3, 0xe5,
	0x62, 0xe4, 0xdd, 0x8c, 0xeb, 0x12, 0xf8, 0x7f, 0x74, 0x40, 0xb5, 0x9a, 0x26, 0xac, 0x90, 0x04,
	0x3d, 0xdc, 0x93, 0xa5, 0xdc, 0x96, 0xc0, 0x70, 0x5b, 0x9a, 0xb6, 0xa8, 0x4a, 0xaf, 0x22, 0x43,
	0x5e, 0xca, 0x2a, 0xa7, 0x1e, 0x46, 0xb9, 0x96, 0x89, 0xb9, 0x13, 0x6d, 0x71, 0xd9, 0xeb, 0x69,
	0x0a, 0xf8, 0x1c, 0x4f, 0xa4, 0x5f, 0x6f, 0xcc, 0x24, 0xba, 0x9d, 0xc9, 0x24, 0x52, 0xbc, 0x5b,
	0xe3, 0x2b, 0x4b, 0x6b, 0xc8, 0x50, 0x3e, 0x96, 0xc0, 0x50, 0x2e, 0xdf, 0xa5, 0xaa, 0x18, 0xba,
	0x67, 0xf8, 0x15, 0xff, 0x2d, 0xa5, 0x1b, 0x39, 0x88, 0xce, 0xa8, 0xe6, 0xb3, 0xad, 0x78, 0x88,
	0x8a, 0x64, 0xdb, 0xf8, 0xbd, 0x3b, 0x9b, 0xef, 0x21, 0x6a, 0x54, 0x95, 0x0f, 0x24, 0x70, 0xb2,
	0x83, 0x6d, 0xd6, 0xf2, 0xc4, 0x80, 0x3d, 0x1d, 0x84, 0xf0, 0x97, 0x16, 0xd8, 0x50, 0x1e, 0x98,
	0x6e, 0x79, 0xca, 0x87, 0x12, 0x38, 0xd5, 0x41, 0x35, 0x87, 0x28, 0x13, 0x83, 0x75, 0x3e, 0x08,
	0xe1, 0x0f, 0x7b, 0x29, 0x16, 0xcc, 0xc3, 0xaa, 0x20, 0xca, 0x94, 0x8f, 0x24, 0x30, 0xf8, 0x70,
	0xb1, 0x12, 0x79, 0xc9, 0x26, 0x83, 0x10, 0x6e, 0xff, 0x91, 0x82, 0x0d, 0xe7, 0xd4, 0x92, 0xe4,
	0x8e, 0x65, 0xc9, 0xe6, 0x89, 0x4f, 0xc5, 0x26, 0x27, 0x27, 0xbb, 0xf5, 0x67, 0x3e, 0x59, 0x9d,
	0xf8, 0xb4, 0x95, 0x99, 0xef, 0x49, 0x40, 0xce, 0x92, 0x89, 0x0b, 0xe4, 0x44, 0x10, 0xc2, 0x60,
	0x3f, 0xa5, 0x3a, 0xd5, 0x41, 0x95, 0x13, 0x45, 0x4e, 0xc4, 0x93, 0x51, 0xac, 0x56, 0x3f, 0xed,
	0x77, 0xd1, 0x8a, 0xe7, 0x62, 0xa4, 0x55, 0xee, 0x2b, 0x5b, 0x46, 0x96, 0xce, 0xdb, 0x25, 0x71,
	0xaf, 0xec, 0x77, 0xf7, 0xbb, 0xbf, 0xb2, 0x34, 0x21, 0x50, 0x3e, 0xc9, 0x34, 0x6b, 0x4b, 0x8c,
	0x22, 0xdd, 0x16, 0xab, 0x1d, 0xef, 0xa6, 0xbe, 0xfa, 0x2b, 0xc5, 0xcb, 0x74, 0x53, 0x5e, 0x74,
	0xfe, 0x41, 0xea, 0xb5, 0xd1, 0x2d, 0xae, 0x89, 0xac, 0x51, 0x37, 0x1e, 0xe4, 0xab, 0xd7, 0x8e,
	0xe7, 0x72, 0xbe, 0x4f, 0x25, 0x70, 0xfa, 0x20, 0xf5, 0x5c, 0xbf, 0x22, 0x86, 0xf0, 0x42, 0x10,
	0xc2, 0xbd, 0x16, 0xe1, 0x53, 0x5d, 0x04, 0x74, 0xfd, 0x8a, 0xf2, 0xbe, 0x04, 0x9e, 0xcc, 0xe6,
	0xc6, 0xa5, 0x97, 0x97, 0x17, 0xae, 0x8a, 0x61, 0x3b, 0x17, 0x84, 0xf0, 0xc7, 0xbf, 0x53, 0xb6,
	0xc1, 0x8e, 0xc4, 0x30, 0x4c, 0x46, 0x6c, 0xe5, 0x73, 0x09, 0x9c, 0x69, 0xeb, 0x0c, 0xfd, 0x8a,
	0x85, 0xbd, 0x3a, 0xa2, 0x71, 0x05, 0x5d, 0x29, 0xb9, 0x62, 0xe0, 0x8a, 0x41, 0x08, 0xbf, 0xfd,
	0x27, 0x85, 0x1b, 0xcf, 0xb4, 0x6d, 0x4d, 0x86, 0xa4, 0x8c, 0x6a, 0x7e, 0xc9, 0x55, 0x3e, 0xcb,
	0x60, 0x26, 0x07, 0xcc, 0xe3, 0x5a, 0xfd, 0x9a, 0xaf, 0x5b, 0x98, 0xad, 0x8b, 0xc1, 0x54, 0x79,
	0xed, 0xfa, 0xba, 0xd1, 0xc4, 0x1c, 0xcb, 0xcb, 0xdf, 0x3a, 0xae, 0xd5, 0x57, 0x63, 0x88, 0x87,
	0x02, 0x1c, 0x97, 0xb0, 0x79, 0xc2, 0xc4, 0x05, 0x78, 0xfb, 0x9b, 0x46, 0x6e, 0x80, 0x13, 0x7b,
	0x75, 0xc2, 0x94, 0xfd, 0x4c, 0xb9, 0x2f, 0x23, 0xdd, 0x5a, 0xc6, 0xb6, 0x30, 0xaa, 0x91, 0x37,
	0xd2, 0x8e, 0xff, 0xea, 0xa3, 0x99, 0xcf, 0x6b, 0xf9, 0x6f, 0xdf, 0x69, 0xcc, 0x44, 0x0d, 0xc5,
	0xad, 0x3b, 0x8d, 0xdc, 0x86, 0x82, 0x22, 0xdd, 0x62, 0xd8, 0x8e, 0x3d, 0xcf, 0x6d, 0xbf, 0x84,
	0x85, 0x83, 0xb7, 0x5f, 0xc1, 0xdd, 0x46, 0xd7, 0xf6, 0x8b, 0x53, 0xdd, 0xcf, 0xc4, 0xe3, 0x15,
	0xa4, 0x53, 0x91, 0x58, 0x62, 0xe3, 0xf1, 0xf3, 0xdd, 0xc6, 0x4c, 0x5c, 0x81, 0x5b, 0x8e, 0x67,
	0x2a, 0xb0, 0x93, 0xb8, 0xa7, 0xd5, 0x91, 0xce, 0xe6, 0x36, 0x24, 0xf0, 0x82, 0x41, 0x6c, 0x7e,
	0x56, 0xf4, 0x97, 0xc9, 0x20, 0x96, 0xea, 0x21, 0xba, 0x86, 0x0d, 0xe4, 0x75, 0xa7, 0x99, 0x7b,
	0x22, 0x4e, 0xa6, 0x72, 0xf4, 0xb5, 0xc8, 0x37, 0xbf, 0xfe, 0x52, 0x8d, 0x58, 0xba, 0x53, 0x53,
	0x9f, 0x2f, 0x31, 0xa6, 0x1a, 0xc4, 0x2e, 0x36, 0x6d, 0x16, 0x9b, 0x36, 0x8b, 0x5d, 0x7f, 0x92,
	0x55, 0xfa, 0xa2, 0x0d, 0x17, 0xff, 0x0d, 0x00, 0x00, 0xff, 0xff, 0xda, 0x03, 0x86, 0xe7, 0x4c,
	0x13, 0x00, 0x00,
}
