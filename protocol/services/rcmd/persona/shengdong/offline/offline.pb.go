// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/persona/shengdong/offline.proto

package offline // import "golang.52tt.com/protocol/services/rcmd/persona/shengdong/offline"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/rcmd/persona"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// user static features
type UserOfflineStFeatures struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UStSex                uint32   `protobuf:"varint,2,opt,name=u_st_sex,json=uStSex,proto3" json:"u_st_sex,omitempty"`
	UStAge                uint32   `protobuf:"varint,3,opt,name=u_st_age,json=uStAge,proto3" json:"u_st_age,omitempty"`
	UStAgeGroup           uint32   `protobuf:"varint,4,opt,name=u_st_age_group,json=uStAgeGroup,proto3" json:"u_st_age_group,omitempty"`
	UStZodiac             uint32   `protobuf:"varint,5,opt,name=u_st_zodiac,json=uStZodiac,proto3" json:"u_st_zodiac,omitempty"`
	UStRegisterFrom       uint32   `protobuf:"varint,6,opt,name=u_st_register_from,json=uStRegisterFrom,proto3" json:"u_st_register_from,omitempty"`
	UStFirstLoginDiffdays uint32   `protobuf:"varint,7,opt,name=u_st_first_login_diffdays,json=uStFirstLoginDiffdays,proto3" json:"u_st_first_login_diffdays,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserOfflineStFeatures) Reset()         { *m = UserOfflineStFeatures{} }
func (m *UserOfflineStFeatures) String() string { return proto.CompactTextString(m) }
func (*UserOfflineStFeatures) ProtoMessage()    {}
func (*UserOfflineStFeatures) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{0}
}
func (m *UserOfflineStFeatures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOfflineStFeatures.Unmarshal(m, b)
}
func (m *UserOfflineStFeatures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOfflineStFeatures.Marshal(b, m, deterministic)
}
func (dst *UserOfflineStFeatures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOfflineStFeatures.Merge(dst, src)
}
func (m *UserOfflineStFeatures) XXX_Size() int {
	return xxx_messageInfo_UserOfflineStFeatures.Size(m)
}
func (m *UserOfflineStFeatures) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOfflineStFeatures.DiscardUnknown(m)
}

var xxx_messageInfo_UserOfflineStFeatures proto.InternalMessageInfo

func (m *UserOfflineStFeatures) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserOfflineStFeatures) GetUStSex() uint32 {
	if m != nil {
		return m.UStSex
	}
	return 0
}

func (m *UserOfflineStFeatures) GetUStAge() uint32 {
	if m != nil {
		return m.UStAge
	}
	return 0
}

func (m *UserOfflineStFeatures) GetUStAgeGroup() uint32 {
	if m != nil {
		return m.UStAgeGroup
	}
	return 0
}

func (m *UserOfflineStFeatures) GetUStZodiac() uint32 {
	if m != nil {
		return m.UStZodiac
	}
	return 0
}

func (m *UserOfflineStFeatures) GetUStRegisterFrom() uint32 {
	if m != nil {
		return m.UStRegisterFrom
	}
	return 0
}

func (m *UserOfflineStFeatures) GetUStFirstLoginDiffdays() uint32 {
	if m != nil {
		return m.UStFirstLoginDiffdays
	}
	return 0
}

// user dynamic features
type UserOfflineDyFeatures struct {
	Uid                   uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UDySRecFinishRate_28D float64  `protobuf:"fixed64,2,opt,name=u_dy_s_rec_finish_rate_28d,json=uDySRecFinishRate28d,proto3" json:"u_dy_s_rec_finish_rate_28d,omitempty"`
	UDySRecLikeRate_28D   float64  `protobuf:"fixed64,3,opt,name=u_dy_s_rec_like_rate_28d,json=uDySRecLikeRate28d,proto3" json:"u_dy_s_rec_like_rate_28d,omitempty"`
	UDySRecValidPlay_28D  uint64   `protobuf:"varint,4,opt,name=u_dy_s_rec_valid_play_28d,json=uDySRecValidPlay28d,proto3" json:"u_dy_s_rec_valid_play_28d,omitempty"`
	UDySAllFinishRate_28D float64  `protobuf:"fixed64,5,opt,name=u_dy_s_all_finish_rate_28d,json=uDySAllFinishRate28d,proto3" json:"u_dy_s_all_finish_rate_28d,omitempty"`
	UDySRecPlay_28D       uint64   `protobuf:"varint,6,opt,name=u_dy_s_rec_play_28d,json=uDySRecPlay28d,proto3" json:"u_dy_s_rec_play_28d,omitempty"`
	UDySRecFinishRate_14D float64  `protobuf:"fixed64,7,opt,name=u_dy_s_rec_finish_rate_14d,json=uDySRecFinishRate14d,proto3" json:"u_dy_s_rec_finish_rate_14d,omitempty"`
	UDySRecLikeRate_14D   float64  `protobuf:"fixed64,8,opt,name=u_dy_s_rec_like_rate_14d,json=uDySRecLikeRate14d,proto3" json:"u_dy_s_rec_like_rate_14d,omitempty"`
	UDySRecValidPlay_14D  uint64   `protobuf:"varint,9,opt,name=u_dy_s_rec_valid_play_14d,json=uDySRecValidPlay14d,proto3" json:"u_dy_s_rec_valid_play_14d,omitempty"`
	UDySRecLike_28D       uint32   `protobuf:"varint,10,opt,name=u_dy_s_rec_like_28d,json=uDySRecLike28d,proto3" json:"u_dy_s_rec_like_28d,omitempty"`
	UDySAllValidPlay_28D  uint32   `protobuf:"varint,11,opt,name=u_dy_s_all_valid_play_28d,json=uDySAllValidPlay28d,proto3" json:"u_dy_s_all_valid_play_28d,omitempty"`
	UDySRecFinishRate_7D  float64  `protobuf:"fixed64,12,opt,name=u_dy_s_rec_finish_rate_7d,json=uDySRecFinishRate7d,proto3" json:"u_dy_s_rec_finish_rate_7d,omitempty"`
	UDySRecPlay_14D       uint64   `protobuf:"varint,13,opt,name=u_dy_s_rec_play_14d,json=uDySRecPlay14d,proto3" json:"u_dy_s_rec_play_14d,omitempty"`
	UDySRecPlay_7D        uint64   `protobuf:"varint,14,opt,name=u_dy_s_rec_play_7d,json=uDySRecPlay7d,proto3" json:"u_dy_s_rec_play_7d,omitempty"`
	UDySAllPlay_3D        uint64   `protobuf:"varint,15,opt,name=u_dy_s_all_play_3d,json=uDySAllPlay3d,proto3" json:"u_dy_s_all_play_3d,omitempty"`
	UDySRecFinish_14D     uint32   `protobuf:"varint,16,opt,name=u_dy_s_rec_finish_14d,json=uDySRecFinish14d,proto3" json:"u_dy_s_rec_finish_14d,omitempty"`
	UDySRecFinish_28D     uint32   `protobuf:"varint,17,opt,name=u_dy_s_rec_finish_28d,json=uDySRecFinish28d,proto3" json:"u_dy_s_rec_finish_28d,omitempty"`
	UDySAllLikeRate_28D   float64  `protobuf:"fixed64,18,opt,name=u_dy_s_all_like_rate_28d,json=uDySAllLikeRate28d,proto3" json:"u_dy_s_all_like_rate_28d,omitempty"`
	UDySAllPlay_28D       uint64   `protobuf:"varint,19,opt,name=u_dy_s_all_play_28d,json=uDySAllPlay28d,proto3" json:"u_dy_s_all_play_28d,omitempty"`
	UDySRecValidPlay_7D   uint64   `protobuf:"varint,20,opt,name=u_dy_s_rec_valid_play_7d,json=uDySRecValidPlay7d,proto3" json:"u_dy_s_rec_valid_play_7d,omitempty"`
	UDySRecLikeRate_7D    float64  `protobuf:"fixed64,21,opt,name=u_dy_s_rec_like_rate_7d,json=uDySRecLikeRate7d,proto3" json:"u_dy_s_rec_like_rate_7d,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *UserOfflineDyFeatures) Reset()         { *m = UserOfflineDyFeatures{} }
func (m *UserOfflineDyFeatures) String() string { return proto.CompactTextString(m) }
func (*UserOfflineDyFeatures) ProtoMessage()    {}
func (*UserOfflineDyFeatures) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{1}
}
func (m *UserOfflineDyFeatures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOfflineDyFeatures.Unmarshal(m, b)
}
func (m *UserOfflineDyFeatures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOfflineDyFeatures.Marshal(b, m, deterministic)
}
func (dst *UserOfflineDyFeatures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOfflineDyFeatures.Merge(dst, src)
}
func (m *UserOfflineDyFeatures) XXX_Size() int {
	return xxx_messageInfo_UserOfflineDyFeatures.Size(m)
}
func (m *UserOfflineDyFeatures) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOfflineDyFeatures.DiscardUnknown(m)
}

var xxx_messageInfo_UserOfflineDyFeatures proto.InternalMessageInfo

func (m *UserOfflineDyFeatures) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecFinishRate_28D() float64 {
	if m != nil {
		return m.UDySRecFinishRate_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecLikeRate_28D() float64 {
	if m != nil {
		return m.UDySRecLikeRate_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecValidPlay_28D() uint64 {
	if m != nil {
		return m.UDySRecValidPlay_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySAllFinishRate_28D() float64 {
	if m != nil {
		return m.UDySAllFinishRate_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecPlay_28D() uint64 {
	if m != nil {
		return m.UDySRecPlay_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecFinishRate_14D() float64 {
	if m != nil {
		return m.UDySRecFinishRate_14D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecLikeRate_14D() float64 {
	if m != nil {
		return m.UDySRecLikeRate_14D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecValidPlay_14D() uint64 {
	if m != nil {
		return m.UDySRecValidPlay_14D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecLike_28D() uint32 {
	if m != nil {
		return m.UDySRecLike_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySAllValidPlay_28D() uint32 {
	if m != nil {
		return m.UDySAllValidPlay_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecFinishRate_7D() float64 {
	if m != nil {
		return m.UDySRecFinishRate_7D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecPlay_14D() uint64 {
	if m != nil {
		return m.UDySRecPlay_14D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecPlay_7D() uint64 {
	if m != nil {
		return m.UDySRecPlay_7D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySAllPlay_3D() uint64 {
	if m != nil {
		return m.UDySAllPlay_3D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecFinish_14D() uint32 {
	if m != nil {
		return m.UDySRecFinish_14D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecFinish_28D() uint32 {
	if m != nil {
		return m.UDySRecFinish_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySAllLikeRate_28D() float64 {
	if m != nil {
		return m.UDySAllLikeRate_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySAllPlay_28D() uint64 {
	if m != nil {
		return m.UDySAllPlay_28D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecValidPlay_7D() uint64 {
	if m != nil {
		return m.UDySRecValidPlay_7D
	}
	return 0
}

func (m *UserOfflineDyFeatures) GetUDySRecLikeRate_7D() float64 {
	if m != nil {
		return m.UDySRecLikeRate_7D
	}
	return 0
}

// slog all features
type ItemOfflineFeatures struct {
	SlogId                uint32   `protobuf:"varint,1,opt,name=slog_id,json=slogId,proto3" json:"slog_id,omitempty"`
	IStTotalTime          uint64   `protobuf:"varint,2,opt,name=i_st_total_time,json=iStTotalTime,proto3" json:"i_st_total_time,omitempty"`
	IStDiffdays           uint32   `protobuf:"varint,3,opt,name=i_st_diffdays,json=iStDiffdays,proto3" json:"i_st_diffdays,omitempty"`
	IStScore              uint32   `protobuf:"varint,4,opt,name=i_st_score,json=iStScore,proto3" json:"i_st_score,omitempty"`
	IStCircleId           uint32   `protobuf:"varint,5,opt,name=i_st_circle_id,json=iStCircleId,proto3" json:"i_st_circle_id,omitempty"`
	IStTopicId            uint32   `protobuf:"varint,6,opt,name=i_st_topic_id,json=iStTopicId,proto3" json:"i_st_topic_id,omitempty"`
	IStType               uint32   `protobuf:"varint,7,opt,name=i_st_type,json=iStType,proto3" json:"i_st_type,omitempty"`
	IStVoiceType          uint32   `protobuf:"varint,8,opt,name=i_st_voice_type,json=iStVoiceType,proto3" json:"i_st_voice_type,omitempty"`
	PStSex                uint32   `protobuf:"varint,9,opt,name=p_st_sex,json=pStSex,proto3" json:"p_st_sex,omitempty"`
	PStAge                uint32   `protobuf:"varint,10,opt,name=p_st_age,json=pStAge,proto3" json:"p_st_age,omitempty"`
	PStAgeGroup           uint32   `protobuf:"varint,11,opt,name=p_st_age_group,json=pStAgeGroup,proto3" json:"p_st_age_group,omitempty"`
	PStZodiac             uint32   `protobuf:"varint,12,opt,name=p_st_zodiac,json=pStZodiac,proto3" json:"p_st_zodiac,omitempty"`
	PStRegisterFrom       uint32   `protobuf:"varint,13,opt,name=p_st_register_from,json=pStRegisterFrom,proto3" json:"p_st_register_from,omitempty"`
	PStFirstLoginDiffdays uint32   `protobuf:"varint,14,opt,name=p_st_first_login_diffdays,json=pStFirstLoginDiffdays,proto3" json:"p_st_first_login_diffdays,omitempty"`
	IDySRecFinishRate_28D float64  `protobuf:"fixed64,20,opt,name=i_dy_s_rec_finish_rate_28d,json=iDySRecFinishRate28d,proto3" json:"i_dy_s_rec_finish_rate_28d,omitempty"`
	IDySRecFinishRate_14D float64  `protobuf:"fixed64,21,opt,name=i_dy_s_rec_finish_rate_14d,json=iDySRecFinishRate14d,proto3" json:"i_dy_s_rec_finish_rate_14d,omitempty"`
	IDySRecPlay_28D       uint64   `protobuf:"varint,22,opt,name=i_dy_s_rec_play_28d,json=iDySRecPlay28d,proto3" json:"i_dy_s_rec_play_28d,omitempty"`
	IDySRecLikeRate_28D   float64  `protobuf:"fixed64,23,opt,name=i_dy_s_rec_like_rate_28d,json=iDySRecLikeRate28d,proto3" json:"i_dy_s_rec_like_rate_28d,omitempty"`
	IDySAllShare_14D      uint32   `protobuf:"varint,24,opt,name=i_dy_s_all_share_14d,json=iDySAllShare14d,proto3" json:"i_dy_s_all_share_14d,omitempty"`
	IDySAllShare_28D      uint32   `protobuf:"varint,25,opt,name=i_dy_s_all_share_28d,json=iDySAllShare28d,proto3" json:"i_dy_s_all_share_28d,omitempty"`
	IDySRecValidPlay_28D  uint64   `protobuf:"varint,26,opt,name=i_dy_s_rec_valid_play_28d,json=iDySRecValidPlay28d,proto3" json:"i_dy_s_rec_valid_play_28d,omitempty"`
	IDySRecLikeRate_14D   float64  `protobuf:"fixed64,27,opt,name=i_dy_s_rec_like_rate_14d,json=iDySRecLikeRate14d,proto3" json:"i_dy_s_rec_like_rate_14d,omitempty"`
	IDySRecComment_28D    uint32   `protobuf:"varint,28,opt,name=i_dy_s_rec_comment_28d,json=iDySRecComment28d,proto3" json:"i_dy_s_rec_comment_28d,omitempty"`
	IDySRecFinishRate_7D  float64  `protobuf:"fixed64,29,opt,name=i_dy_s_rec_finish_rate_7d,json=iDySRecFinishRate7d,proto3" json:"i_dy_s_rec_finish_rate_7d,omitempty"`
	IDySAllValidPlay_14D  uint64   `protobuf:"varint,30,opt,name=i_dy_s_all_valid_play_14d,json=iDySAllValidPlay14d,proto3" json:"i_dy_s_all_valid_play_14d,omitempty"`
	IDySRecFinish_3D      uint32   `protobuf:"varint,31,opt,name=i_dy_s_rec_finish_3d,json=iDySRecFinish3d,proto3" json:"i_dy_s_rec_finish_3d,omitempty"`
	IDySRecComment_14D    uint32   `protobuf:"varint,32,opt,name=i_dy_s_rec_comment_14d,json=iDySRecComment14d,proto3" json:"i_dy_s_rec_comment_14d,omitempty"`
	IDySRecLikeRate_3D    float64  `protobuf:"fixed64,33,opt,name=i_dy_s_rec_like_rate_3d,json=iDySRecLikeRate3d,proto3" json:"i_dy_s_rec_like_rate_3d,omitempty"`
	IDySRecLike_28D       uint32   `protobuf:"varint,34,opt,name=i_dy_s_rec_like_28d,json=iDySRecLike28d,proto3" json:"i_dy_s_rec_like_28d,omitempty"`
	IDySAllLikeRate_1D    float64  `protobuf:"fixed64,35,opt,name=i_dy_s_all_like_rate_1d,json=iDySAllLikeRate1d,proto3" json:"i_dy_s_all_like_rate_1d,omitempty"`
	IDySAllFinishRate_14D float64  `protobuf:"fixed64,36,opt,name=i_dy_s_all_finish_rate_14d,json=iDySAllFinishRate14d,proto3" json:"i_dy_s_all_finish_rate_14d,omitempty"`
	IDySRecPlay_14D       uint64   `protobuf:"varint,37,opt,name=i_dy_s_rec_play_14d,json=iDySRecPlay14d,proto3" json:"i_dy_s_rec_play_14d,omitempty"`
	IDySRecValidPlay_1D   uint64   `protobuf:"varint,38,opt,name=i_dy_s_rec_valid_play_1d,json=iDySRecValidPlay1d,proto3" json:"i_dy_s_rec_valid_play_1d,omitempty"`
	IDySRecLikeRate_7D    float64  `protobuf:"fixed64,39,opt,name=i_dy_s_rec_like_rate_7d,json=iDySRecLikeRate7d,proto3" json:"i_dy_s_rec_like_rate_7d,omitempty"`
	IDySAllLikeRate_28D   float64  `protobuf:"fixed64,40,opt,name=i_dy_s_all_like_rate_28d,json=iDySAllLikeRate28d,proto3" json:"i_dy_s_all_like_rate_28d,omitempty"`
	IDySAllFinish_28D     uint32   `protobuf:"varint,41,opt,name=i_dy_s_all_finish_28d,json=iDySAllFinish28d,proto3" json:"i_dy_s_all_finish_28d,omitempty"`
	IDySAllFinishRate_28D float64  `protobuf:"fixed64,42,opt,name=i_dy_s_all_finish_rate_28d,json=iDySAllFinishRate28d,proto3" json:"i_dy_s_all_finish_rate_28d,omitempty"`
	PDySRecShareRate_1D   float64  `protobuf:"fixed64,43,opt,name=p_dy_s_rec_share_rate_1d,json=pDySRecShareRate1d,proto3" json:"p_dy_s_rec_share_rate_1d,omitempty"`
	PDySAllFinishRate_28D float64  `protobuf:"fixed64,44,opt,name=p_dy_s_all_finish_rate_28d,json=pDySAllFinishRate28d,proto3" json:"p_dy_s_all_finish_rate_28d,omitempty"`
	IDySRecLikeRate_1D    float64  `protobuf:"fixed64,45,opt,name=i_dy_s_rec_like_rate_1d,json=iDySRecLikeRate1d,proto3" json:"i_dy_s_rec_like_rate_1d,omitempty"`
	IDySAllFinishRate_7D  float64  `protobuf:"fixed64,46,opt,name=i_dy_s_all_finish_rate_7d,json=iDySAllFinishRate7d,proto3" json:"i_dy_s_all_finish_rate_7d,omitempty"`
	IDySAllFinish_14D     uint32   `protobuf:"varint,47,opt,name=i_dy_s_all_finish_14d,json=iDySAllFinish14d,proto3" json:"i_dy_s_all_finish_14d,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *ItemOfflineFeatures) Reset()         { *m = ItemOfflineFeatures{} }
func (m *ItemOfflineFeatures) String() string { return proto.CompactTextString(m) }
func (*ItemOfflineFeatures) ProtoMessage()    {}
func (*ItemOfflineFeatures) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{2}
}
func (m *ItemOfflineFeatures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemOfflineFeatures.Unmarshal(m, b)
}
func (m *ItemOfflineFeatures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemOfflineFeatures.Marshal(b, m, deterministic)
}
func (dst *ItemOfflineFeatures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemOfflineFeatures.Merge(dst, src)
}
func (m *ItemOfflineFeatures) XXX_Size() int {
	return xxx_messageInfo_ItemOfflineFeatures.Size(m)
}
func (m *ItemOfflineFeatures) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemOfflineFeatures.DiscardUnknown(m)
}

var xxx_messageInfo_ItemOfflineFeatures proto.InternalMessageInfo

func (m *ItemOfflineFeatures) GetSlogId() uint32 {
	if m != nil {
		return m.SlogId
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStTotalTime() uint64 {
	if m != nil {
		return m.IStTotalTime
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStDiffdays() uint32 {
	if m != nil {
		return m.IStDiffdays
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStScore() uint32 {
	if m != nil {
		return m.IStScore
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStCircleId() uint32 {
	if m != nil {
		return m.IStCircleId
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStTopicId() uint32 {
	if m != nil {
		return m.IStTopicId
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStType() uint32 {
	if m != nil {
		return m.IStType
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIStVoiceType() uint32 {
	if m != nil {
		return m.IStVoiceType
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPStSex() uint32 {
	if m != nil {
		return m.PStSex
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPStAge() uint32 {
	if m != nil {
		return m.PStAge
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPStAgeGroup() uint32 {
	if m != nil {
		return m.PStAgeGroup
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPStZodiac() uint32 {
	if m != nil {
		return m.PStZodiac
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPStRegisterFrom() uint32 {
	if m != nil {
		return m.PStRegisterFrom
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPStFirstLoginDiffdays() uint32 {
	if m != nil {
		return m.PStFirstLoginDiffdays
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecFinishRate_28D() float64 {
	if m != nil {
		return m.IDySRecFinishRate_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecFinishRate_14D() float64 {
	if m != nil {
		return m.IDySRecFinishRate_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecPlay_28D() uint64 {
	if m != nil {
		return m.IDySRecPlay_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecLikeRate_28D() float64 {
	if m != nil {
		return m.IDySRecLikeRate_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllShare_14D() uint32 {
	if m != nil {
		return m.IDySAllShare_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllShare_28D() uint32 {
	if m != nil {
		return m.IDySAllShare_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecValidPlay_28D() uint64 {
	if m != nil {
		return m.IDySRecValidPlay_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecLikeRate_14D() float64 {
	if m != nil {
		return m.IDySRecLikeRate_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecComment_28D() uint32 {
	if m != nil {
		return m.IDySRecComment_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecFinishRate_7D() float64 {
	if m != nil {
		return m.IDySRecFinishRate_7D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllValidPlay_14D() uint64 {
	if m != nil {
		return m.IDySAllValidPlay_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecFinish_3D() uint32 {
	if m != nil {
		return m.IDySRecFinish_3D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecComment_14D() uint32 {
	if m != nil {
		return m.IDySRecComment_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecLikeRate_3D() float64 {
	if m != nil {
		return m.IDySRecLikeRate_3D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecLike_28D() uint32 {
	if m != nil {
		return m.IDySRecLike_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllLikeRate_1D() float64 {
	if m != nil {
		return m.IDySAllLikeRate_1D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllFinishRate_14D() float64 {
	if m != nil {
		return m.IDySAllFinishRate_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecPlay_14D() uint64 {
	if m != nil {
		return m.IDySRecPlay_14D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecValidPlay_1D() uint64 {
	if m != nil {
		return m.IDySRecValidPlay_1D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecLikeRate_7D() float64 {
	if m != nil {
		return m.IDySRecLikeRate_7D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllLikeRate_28D() float64 {
	if m != nil {
		return m.IDySAllLikeRate_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllFinish_28D() uint32 {
	if m != nil {
		return m.IDySAllFinish_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllFinishRate_28D() float64 {
	if m != nil {
		return m.IDySAllFinishRate_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPDySRecShareRate_1D() float64 {
	if m != nil {
		return m.PDySRecShareRate_1D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetPDySAllFinishRate_28D() float64 {
	if m != nil {
		return m.PDySAllFinishRate_28D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySRecLikeRate_1D() float64 {
	if m != nil {
		return m.IDySRecLikeRate_1D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllFinishRate_7D() float64 {
	if m != nil {
		return m.IDySAllFinishRate_7D
	}
	return 0
}

func (m *ItemOfflineFeatures) GetIDySAllFinish_14D() uint32 {
	if m != nil {
		return m.IDySAllFinish_14D
	}
	return 0
}

// user features v2
type UserOfflineV2DyFeatures struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	USex                 uint64   `protobuf:"varint,2,opt,name=u_sex,json=uSex,proto3" json:"u_sex,omitempty"`
	UUdiff               uint64   `protobuf:"varint,3,opt,name=u_udiff,json=uUdiff,proto3" json:"u_udiff,omitempty"`
	UAge                 uint64   `protobuf:"varint,4,opt,name=u_age,json=uAge,proto3" json:"u_age,omitempty"`
	UZodiac              uint64   `protobuf:"varint,5,opt,name=u_zodiac,json=uZodiac,proto3" json:"u_zodiac,omitempty"`
	URegisterSource      uint64   `protobuf:"varint,6,opt,name=u_register_source,json=uRegisterSource,proto3" json:"u_register_source,omitempty"`
	UBbsRecAvgtime_14D   float64  `protobuf:"fixed64,7,opt,name=u_bbs_rec_avgtime_14d,json=uBbsRecAvgtime14d,proto3" json:"u_bbs_rec_avgtime_14d,omitempty"`
	UBbsAllView_28D      uint64   `protobuf:"varint,8,opt,name=u_bbs_all_view_28d,json=uBbsAllView28d,proto3" json:"u_bbs_all_view_28d,omitempty"`
	UBbsRecLike_14D      uint64   `protobuf:"varint,9,opt,name=u_bbs_rec_like_14d,json=uBbsRecLike14d,proto3" json:"u_bbs_rec_like_14d,omitempty"`
	UBbsRecAvgtime_7D    float64  `protobuf:"fixed64,10,opt,name=u_bbs_rec_avgtime_7d,json=uBbsRecAvgtime7d,proto3" json:"u_bbs_rec_avgtime_7d,omitempty"`
	UBbsAllCComment_28D  uint64   `protobuf:"varint,11,opt,name=u_bbs_all_c_comment_28d,json=uBbsAllCComment28d,proto3" json:"u_bbs_all_c_comment_28d,omitempty"`
	UBbsRecCLike_28D     uint64   `protobuf:"varint,12,opt,name=u_bbs_rec_c_like_28d,json=uBbsRecCLike28d,proto3" json:"u_bbs_rec_c_like_28d,omitempty"`
	UBbsAllAvgtime_3D    float64  `protobuf:"fixed64,13,opt,name=u_bbs_all_avgtime_3d,json=uBbsAllAvgtime3d,proto3" json:"u_bbs_all_avgtime_3d,omitempty"`
	UBbsAllAvgtime_28D   float64  `protobuf:"fixed64,14,opt,name=u_bbs_all_avgtime_28d,json=uBbsAllAvgtime28d,proto3" json:"u_bbs_all_avgtime_28d,omitempty"`
	UBbsRecView_7D       uint64   `protobuf:"varint,15,opt,name=u_bbs_rec_view_7d,json=uBbsRecView7d,proto3" json:"u_bbs_rec_view_7d,omitempty"`
	UBbsRecAvgtime_3D    float64  `protobuf:"fixed64,16,opt,name=u_bbs_rec_avgtime_3d,json=uBbsRecAvgtime3d,proto3" json:"u_bbs_rec_avgtime_3d,omitempty"`
	UBbsAllComment_28D   uint64   `protobuf:"varint,17,opt,name=u_bbs_all_comment_28d,json=uBbsAllComment28d,proto3" json:"u_bbs_all_comment_28d,omitempty"`
	UBbsAllLike_28D      uint64   `protobuf:"varint,18,opt,name=u_bbs_all_like_28d,json=uBbsAllLike28d,proto3" json:"u_bbs_all_like_28d,omitempty"`
	UBbsAllView_3D       uint64   `protobuf:"varint,19,opt,name=u_bbs_all_view_3d,json=uBbsAllView3d,proto3" json:"u_bbs_all_view_3d,omitempty"`
	UBbsRecValidview_14D uint64   `protobuf:"varint,20,opt,name=u_bbs_rec_validview_14d,json=uBbsRecValidview14d,proto3" json:"u_bbs_rec_validview_14d,omitempty"`
	UBbsAllComment_14D   uint64   `protobuf:"varint,21,opt,name=u_bbs_all_comment_14d,json=uBbsAllComment14d,proto3" json:"u_bbs_all_comment_14d,omitempty"`
	UBbsRecLike_1D       uint64   `protobuf:"varint,22,opt,name=u_bbs_rec_like_1d,json=uBbsRecLike1d,proto3" json:"u_bbs_rec_like_1d,omitempty"`
	UBbsRecView_3D       uint64   `protobuf:"varint,23,opt,name=u_bbs_rec_view_3d,json=uBbsRecView3d,proto3" json:"u_bbs_rec_view_3d,omitempty"`
	UBbsRecCLike_14D     uint64   `protobuf:"varint,24,opt,name=u_bbs_rec_c_like_14d,json=uBbsRecCLike14d,proto3" json:"u_bbs_rec_c_like_14d,omitempty"`
	UBbsRecValidview_7D  uint64   `protobuf:"varint,25,opt,name=u_bbs_rec_validview_7d,json=uBbsRecValidview7d,proto3" json:"u_bbs_rec_validview_7d,omitempty"`
	UBbsProduce_28D      uint64   `protobuf:"varint,26,opt,name=u_bbs_produce_28d,json=uBbsProduce28d,proto3" json:"u_bbs_produce_28d,omitempty"`
	UBbsRecAvgtime_28D   float64  `protobuf:"fixed64,27,opt,name=u_bbs_rec_avgtime_28d,json=uBbsRecAvgtime28d,proto3" json:"u_bbs_rec_avgtime_28d,omitempty"`
	UBbsRecView_28D      uint64   `protobuf:"varint,28,opt,name=u_bbs_rec_view_28d,json=uBbsRecView28d,proto3" json:"u_bbs_rec_view_28d,omitempty"`
	UBbsRecLike_28D      uint64   `protobuf:"varint,29,opt,name=u_bbs_rec_like_28d,json=uBbsRecLike28d,proto3" json:"u_bbs_rec_like_28d,omitempty"`
	UBbsRecValidview_28D uint64   `protobuf:"varint,30,opt,name=u_bbs_rec_validview_28d,json=uBbsRecValidview28d,proto3" json:"u_bbs_rec_validview_28d,omitempty"`
	UBbsRecView_14D      uint64   `protobuf:"varint,31,opt,name=u_bbs_rec_view_14d,json=uBbsRecView14d,proto3" json:"u_bbs_rec_view_14d,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOfflineV2DyFeatures) Reset()         { *m = UserOfflineV2DyFeatures{} }
func (m *UserOfflineV2DyFeatures) String() string { return proto.CompactTextString(m) }
func (*UserOfflineV2DyFeatures) ProtoMessage()    {}
func (*UserOfflineV2DyFeatures) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{3}
}
func (m *UserOfflineV2DyFeatures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOfflineV2DyFeatures.Unmarshal(m, b)
}
func (m *UserOfflineV2DyFeatures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOfflineV2DyFeatures.Marshal(b, m, deterministic)
}
func (dst *UserOfflineV2DyFeatures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOfflineV2DyFeatures.Merge(dst, src)
}
func (m *UserOfflineV2DyFeatures) XXX_Size() int {
	return xxx_messageInfo_UserOfflineV2DyFeatures.Size(m)
}
func (m *UserOfflineV2DyFeatures) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOfflineV2DyFeatures.DiscardUnknown(m)
}

var xxx_messageInfo_UserOfflineV2DyFeatures proto.InternalMessageInfo

func (m *UserOfflineV2DyFeatures) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUSex() uint64 {
	if m != nil {
		return m.USex
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUUdiff() uint64 {
	if m != nil {
		return m.UUdiff
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUAge() uint64 {
	if m != nil {
		return m.UAge
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUZodiac() uint64 {
	if m != nil {
		return m.UZodiac
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetURegisterSource() uint64 {
	if m != nil {
		return m.URegisterSource
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecAvgtime_14D() float64 {
	if m != nil {
		return m.UBbsRecAvgtime_14D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllView_28D() uint64 {
	if m != nil {
		return m.UBbsAllView_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecLike_14D() uint64 {
	if m != nil {
		return m.UBbsRecLike_14D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecAvgtime_7D() float64 {
	if m != nil {
		return m.UBbsRecAvgtime_7D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllCComment_28D() uint64 {
	if m != nil {
		return m.UBbsAllCComment_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecCLike_28D() uint64 {
	if m != nil {
		return m.UBbsRecCLike_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllAvgtime_3D() float64 {
	if m != nil {
		return m.UBbsAllAvgtime_3D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllAvgtime_28D() float64 {
	if m != nil {
		return m.UBbsAllAvgtime_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecView_7D() uint64 {
	if m != nil {
		return m.UBbsRecView_7D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecAvgtime_3D() float64 {
	if m != nil {
		return m.UBbsRecAvgtime_3D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllComment_28D() uint64 {
	if m != nil {
		return m.UBbsAllComment_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllLike_28D() uint64 {
	if m != nil {
		return m.UBbsAllLike_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllView_3D() uint64 {
	if m != nil {
		return m.UBbsAllView_3D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecValidview_14D() uint64 {
	if m != nil {
		return m.UBbsRecValidview_14D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsAllComment_14D() uint64 {
	if m != nil {
		return m.UBbsAllComment_14D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecLike_1D() uint64 {
	if m != nil {
		return m.UBbsRecLike_1D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecView_3D() uint64 {
	if m != nil {
		return m.UBbsRecView_3D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecCLike_14D() uint64 {
	if m != nil {
		return m.UBbsRecCLike_14D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecValidview_7D() uint64 {
	if m != nil {
		return m.UBbsRecValidview_7D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsProduce_28D() uint64 {
	if m != nil {
		return m.UBbsProduce_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecAvgtime_28D() float64 {
	if m != nil {
		return m.UBbsRecAvgtime_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecView_28D() uint64 {
	if m != nil {
		return m.UBbsRecView_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecLike_28D() uint64 {
	if m != nil {
		return m.UBbsRecLike_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecValidview_28D() uint64 {
	if m != nil {
		return m.UBbsRecValidview_28D
	}
	return 0
}

func (m *UserOfflineV2DyFeatures) GetUBbsRecView_14D() uint64 {
	if m != nil {
		return m.UBbsRecView_14D
	}
	return 0
}

// slog all features v2
type ItemOfflineV2Features struct {
	BbsId                uint64   `protobuf:"varint,1,opt,name=bbs_id,json=bbsId,proto3" json:"bbs_id,omitempty"`
	IBbsRecAvgtime_28D   float64  `protobuf:"fixed64,2,opt,name=i_bbs_rec_avgtime_28d,json=iBbsRecAvgtime28d,proto3" json:"i_bbs_rec_avgtime_28d,omitempty"`
	IBbsRecView_28D      uint64   `protobuf:"varint,3,opt,name=i_bbs_rec_view_28d,json=iBbsRecView28d,proto3" json:"i_bbs_rec_view_28d,omitempty"`
	IBbsRecAvgtime_14D   float64  `protobuf:"fixed64,4,opt,name=i_bbs_rec_avgtime_14d,json=iBbsRecAvgtime14d,proto3" json:"i_bbs_rec_avgtime_14d,omitempty"`
	IIdiff               uint64   `protobuf:"varint,5,opt,name=i_idiff,json=iIdiff,proto3" json:"i_idiff,omitempty"`
	IBbsRecLike_28D      uint64   `protobuf:"varint,6,opt,name=i_bbs_rec_like_28d,json=iBbsRecLike28d,proto3" json:"i_bbs_rec_like_28d,omitempty"`
	IBbsRecValidView_28D uint64   `protobuf:"varint,7,opt,name=i_bbs_rec_valid_view_28d,json=iBbsRecValidView28d,proto3" json:"i_bbs_rec_valid_view_28d,omitempty"`
	IBbsRecView_14D      uint64   `protobuf:"varint,8,opt,name=i_bbs_rec_view_14d,json=iBbsRecView14d,proto3" json:"i_bbs_rec_view_14d,omitempty"`
	IBbsType             uint64   `protobuf:"varint,9,opt,name=i_bbs_type,json=iBbsType,proto3" json:"i_bbs_type,omitempty"`
	IScore               uint64   `protobuf:"varint,10,opt,name=i_score,json=iScore,proto3" json:"i_score,omitempty"`
	IBbsRecAvgtime_7D    float64  `protobuf:"fixed64,11,opt,name=i_bbs_rec_avgtime_7d,json=iBbsRecAvgtime7d,proto3" json:"i_bbs_rec_avgtime_7d,omitempty"`
	IBbsAllLike_3D       uint64   `protobuf:"varint,12,opt,name=i_bbs_all_like_3d,json=iBbsAllLike3d,proto3" json:"i_bbs_all_like_3d,omitempty"`
	IBbsAllView_3D       uint64   `protobuf:"varint,13,opt,name=i_bbs_all_view_3d,json=iBbsAllView3d,proto3" json:"i_bbs_all_view_3d,omitempty"`
	IBbsRecAvgtime_3D    float64  `protobuf:"fixed64,14,opt,name=i_bbs_rec_avgtime_3d,json=iBbsRecAvgtime3d,proto3" json:"i_bbs_rec_avgtime_3d,omitempty"`
	IBbsAllLike_28D      uint64   `protobuf:"varint,15,opt,name=i_bbs_all_like_28d,json=iBbsAllLike28d,proto3" json:"i_bbs_all_like_28d,omitempty"`
	IBbsRecLike_7D       uint64   `protobuf:"varint,16,opt,name=i_bbs_rec_like_7d,json=iBbsRecLike7d,proto3" json:"i_bbs_rec_like_7d,omitempty"`
	IBbsAllAvgtime_1D    float64  `protobuf:"fixed64,17,opt,name=i_bbs_all_avgtime_1d,json=iBbsAllAvgtime1d,proto3" json:"i_bbs_all_avgtime_1d,omitempty"`
	IBbsAllComment_7D    uint64   `protobuf:"varint,18,opt,name=i_bbs_all_comment_7d,json=iBbsAllComment7d,proto3" json:"i_bbs_all_comment_7d,omitempty"`
	IBbsAllView_7D       uint64   `protobuf:"varint,19,opt,name=i_bbs_all_view_7d,json=iBbsAllView7d,proto3" json:"i_bbs_all_view_7d,omitempty"`
	IBbsAllAvgtime_3D    float64  `protobuf:"fixed64,20,opt,name=i_bbs_all_avgtime_3d,json=iBbsAllAvgtime3d,proto3" json:"i_bbs_all_avgtime_3d,omitempty"`
	IBbsRecValidView_14D uint64   `protobuf:"varint,21,opt,name=i_bbs_rec_valid_view_14d,json=iBbsRecValidView14d,proto3" json:"i_bbs_rec_valid_view_14d,omitempty"`
	IBbsRecView_3D       uint64   `protobuf:"varint,22,opt,name=i_bbs_rec_view_3d,json=iBbsRecView3d,proto3" json:"i_bbs_rec_view_3d,omitempty"`
	IBbsRecView_1D       uint64   `protobuf:"varint,23,opt,name=i_bbs_rec_view_1d,json=iBbsRecView1d,proto3" json:"i_bbs_rec_view_1d,omitempty"`
	IBbsRecValidView_7D  uint64   `protobuf:"varint,24,opt,name=i_bbs_rec_valid_view_7d,json=iBbsRecValidView7d,proto3" json:"i_bbs_rec_valid_view_7d,omitempty"`
	IBbsAllAvgtime_28D   float64  `protobuf:"fixed64,25,opt,name=i_bbs_all_avgtime_28d,json=iBbsAllAvgtime28d,proto3" json:"i_bbs_all_avgtime_28d,omitempty"`
	PUdiff               uint64   `protobuf:"varint,26,opt,name=p_udiff,json=pUdiff,proto3" json:"p_udiff,omitempty"`
	IBbsAllLike_7D       uint64   `protobuf:"varint,27,opt,name=i_bbs_all_like_7d,json=iBbsAllLike7d,proto3" json:"i_bbs_all_like_7d,omitempty"`
	PSex                 uint64   `protobuf:"varint,28,opt,name=p_sex,json=pSex,proto3" json:"p_sex,omitempty"`
	ICircleId            uint64   `protobuf:"varint,29,opt,name=i_circle_id,json=iCircleId,proto3" json:"i_circle_id,omitempty"`
	PZodiac              uint64   `protobuf:"varint,30,opt,name=p_zodiac,json=pZodiac,proto3" json:"p_zodiac,omitempty"`
	PAge                 uint64   `protobuf:"varint,31,opt,name=p_age,json=pAge,proto3" json:"p_age,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ItemOfflineV2Features) Reset()         { *m = ItemOfflineV2Features{} }
func (m *ItemOfflineV2Features) String() string { return proto.CompactTextString(m) }
func (*ItemOfflineV2Features) ProtoMessage()    {}
func (*ItemOfflineV2Features) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{4}
}
func (m *ItemOfflineV2Features) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemOfflineV2Features.Unmarshal(m, b)
}
func (m *ItemOfflineV2Features) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemOfflineV2Features.Marshal(b, m, deterministic)
}
func (dst *ItemOfflineV2Features) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemOfflineV2Features.Merge(dst, src)
}
func (m *ItemOfflineV2Features) XXX_Size() int {
	return xxx_messageInfo_ItemOfflineV2Features.Size(m)
}
func (m *ItemOfflineV2Features) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemOfflineV2Features.DiscardUnknown(m)
}

var xxx_messageInfo_ItemOfflineV2Features proto.InternalMessageInfo

func (m *ItemOfflineV2Features) GetBbsId() uint64 {
	if m != nil {
		return m.BbsId
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecAvgtime_28D() float64 {
	if m != nil {
		return m.IBbsRecAvgtime_28D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecView_28D() uint64 {
	if m != nil {
		return m.IBbsRecView_28D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecAvgtime_14D() float64 {
	if m != nil {
		return m.IBbsRecAvgtime_14D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIIdiff() uint64 {
	if m != nil {
		return m.IIdiff
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecLike_28D() uint64 {
	if m != nil {
		return m.IBbsRecLike_28D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecValidView_28D() uint64 {
	if m != nil {
		return m.IBbsRecValidView_28D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecView_14D() uint64 {
	if m != nil {
		return m.IBbsRecView_14D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsType() uint64 {
	if m != nil {
		return m.IBbsType
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIScore() uint64 {
	if m != nil {
		return m.IScore
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecAvgtime_7D() float64 {
	if m != nil {
		return m.IBbsRecAvgtime_7D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllLike_3D() uint64 {
	if m != nil {
		return m.IBbsAllLike_3D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllView_3D() uint64 {
	if m != nil {
		return m.IBbsAllView_3D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecAvgtime_3D() float64 {
	if m != nil {
		return m.IBbsRecAvgtime_3D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllLike_28D() uint64 {
	if m != nil {
		return m.IBbsAllLike_28D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecLike_7D() uint64 {
	if m != nil {
		return m.IBbsRecLike_7D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllAvgtime_1D() float64 {
	if m != nil {
		return m.IBbsAllAvgtime_1D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllComment_7D() uint64 {
	if m != nil {
		return m.IBbsAllComment_7D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllView_7D() uint64 {
	if m != nil {
		return m.IBbsAllView_7D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllAvgtime_3D() float64 {
	if m != nil {
		return m.IBbsAllAvgtime_3D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecValidView_14D() uint64 {
	if m != nil {
		return m.IBbsRecValidView_14D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecView_3D() uint64 {
	if m != nil {
		return m.IBbsRecView_3D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecView_1D() uint64 {
	if m != nil {
		return m.IBbsRecView_1D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsRecValidView_7D() uint64 {
	if m != nil {
		return m.IBbsRecValidView_7D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllAvgtime_28D() float64 {
	if m != nil {
		return m.IBbsAllAvgtime_28D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetPUdiff() uint64 {
	if m != nil {
		return m.PUdiff
	}
	return 0
}

func (m *ItemOfflineV2Features) GetIBbsAllLike_7D() uint64 {
	if m != nil {
		return m.IBbsAllLike_7D
	}
	return 0
}

func (m *ItemOfflineV2Features) GetPSex() uint64 {
	if m != nil {
		return m.PSex
	}
	return 0
}

func (m *ItemOfflineV2Features) GetICircleId() uint64 {
	if m != nil {
		return m.ICircleId
	}
	return 0
}

func (m *ItemOfflineV2Features) GetPZodiac() uint64 {
	if m != nil {
		return m.PZodiac
	}
	return 0
}

func (m *ItemOfflineV2Features) GetPAge() uint64 {
	if m != nil {
		return m.PAge
	}
	return 0
}

// slog circle user features
type CircleUserOfflineFeatures struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CityHash             uint64   `protobuf:"varint,2,opt,name=city_hash,json=cityHash,proto3" json:"city_hash,omitempty"`
	AgeGroup             uint64   `protobuf:"varint,3,opt,name=age_group,json=ageGroup,proto3" json:"age_group,omitempty"`
	Zodiac               uint64   `protobuf:"varint,4,opt,name=zodiac,proto3" json:"zodiac,omitempty"`
	Sex                  uint64   `protobuf:"varint,5,opt,name=sex,proto3" json:"sex,omitempty"`
	Diffdates            uint64   `protobuf:"varint,6,opt,name=diffdates,proto3" json:"diffdates,omitempty"`
	CharmExp             uint64   `protobuf:"varint,7,opt,name=charm_exp,json=charmExp,proto3" json:"charm_exp,omitempty"`
	PrestigeExp          uint64   `protobuf:"varint,8,opt,name=prestige_exp,json=prestigeExp,proto3" json:"prestige_exp,omitempty"`
	FriendCnt            uint64   `protobuf:"varint,9,opt,name=friend_cnt,json=friendCnt,proto3" json:"friend_cnt,omitempty"`
	LikeCnt              uint64   `protobuf:"varint,10,opt,name=like_cnt,json=likeCnt,proto3" json:"like_cnt,omitempty"`
	FansCnt              uint64   `protobuf:"varint,11,opt,name=fans_cnt,json=fansCnt,proto3" json:"fans_cnt,omitempty"`
	CircleCnt            uint64   `protobuf:"varint,12,opt,name=circle_cnt,json=circleCnt,proto3" json:"circle_cnt,omitempty"`
	UCircleStayTime_3D   float64  `protobuf:"fixed64,50,opt,name=u_circle_stay_time_3d,json=uCircleStayTime3d,proto3" json:"u_circle_stay_time_3d,omitempty"`
	UCircleStayTime_7D   float64  `protobuf:"fixed64,51,opt,name=u_circle_stay_time_7d,json=uCircleStayTime7d,proto3" json:"u_circle_stay_time_7d,omitempty"`
	UCircleStayTime_14D  float64  `protobuf:"fixed64,52,opt,name=u_circle_stay_time_14d,json=uCircleStayTime14d,proto3" json:"u_circle_stay_time_14d,omitempty"`
	UCircleJoin_3D       uint64   `protobuf:"varint,53,opt,name=u_circle_join_3d,json=uCircleJoin3d,proto3" json:"u_circle_join_3d,omitempty"`
	UCircleJoin_7D       uint64   `protobuf:"varint,54,opt,name=u_circle_join_7d,json=uCircleJoin7d,proto3" json:"u_circle_join_7d,omitempty"`
	UCircleJoin_14D      uint64   `protobuf:"varint,55,opt,name=u_circle_join_14d,json=uCircleJoin14d,proto3" json:"u_circle_join_14d,omitempty"`
	CircleTypes_3D       uint64   `protobuf:"varint,56,opt,name=circle_types_3d,json=circleTypes3d,proto3" json:"circle_types_3d,omitempty"`
	CircleTypes_7D       uint64   `protobuf:"varint,57,opt,name=circle_types_7d,json=circleTypes7d,proto3" json:"circle_types_7d,omitempty"`
	CircleTypes_14D      uint64   `protobuf:"varint,58,opt,name=circle_types_14d,json=circleTypes14d,proto3" json:"circle_types_14d,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CircleUserOfflineFeatures) Reset()         { *m = CircleUserOfflineFeatures{} }
func (m *CircleUserOfflineFeatures) String() string { return proto.CompactTextString(m) }
func (*CircleUserOfflineFeatures) ProtoMessage()    {}
func (*CircleUserOfflineFeatures) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{5}
}
func (m *CircleUserOfflineFeatures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CircleUserOfflineFeatures.Unmarshal(m, b)
}
func (m *CircleUserOfflineFeatures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CircleUserOfflineFeatures.Marshal(b, m, deterministic)
}
func (dst *CircleUserOfflineFeatures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CircleUserOfflineFeatures.Merge(dst, src)
}
func (m *CircleUserOfflineFeatures) XXX_Size() int {
	return xxx_messageInfo_CircleUserOfflineFeatures.Size(m)
}
func (m *CircleUserOfflineFeatures) XXX_DiscardUnknown() {
	xxx_messageInfo_CircleUserOfflineFeatures.DiscardUnknown(m)
}

var xxx_messageInfo_CircleUserOfflineFeatures proto.InternalMessageInfo

func (m *CircleUserOfflineFeatures) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetCityHash() uint64 {
	if m != nil {
		return m.CityHash
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetAgeGroup() uint64 {
	if m != nil {
		return m.AgeGroup
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetZodiac() uint64 {
	if m != nil {
		return m.Zodiac
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetSex() uint64 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetDiffdates() uint64 {
	if m != nil {
		return m.Diffdates
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetCharmExp() uint64 {
	if m != nil {
		return m.CharmExp
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetPrestigeExp() uint64 {
	if m != nil {
		return m.PrestigeExp
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetFriendCnt() uint64 {
	if m != nil {
		return m.FriendCnt
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetLikeCnt() uint64 {
	if m != nil {
		return m.LikeCnt
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetFansCnt() uint64 {
	if m != nil {
		return m.FansCnt
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetCircleCnt() uint64 {
	if m != nil {
		return m.CircleCnt
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetUCircleStayTime_3D() float64 {
	if m != nil {
		return m.UCircleStayTime_3D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetUCircleStayTime_7D() float64 {
	if m != nil {
		return m.UCircleStayTime_7D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetUCircleStayTime_14D() float64 {
	if m != nil {
		return m.UCircleStayTime_14D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetUCircleJoin_3D() uint64 {
	if m != nil {
		return m.UCircleJoin_3D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetUCircleJoin_7D() uint64 {
	if m != nil {
		return m.UCircleJoin_7D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetUCircleJoin_14D() uint64 {
	if m != nil {
		return m.UCircleJoin_14D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetCircleTypes_3D() uint64 {
	if m != nil {
		return m.CircleTypes_3D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetCircleTypes_7D() uint64 {
	if m != nil {
		return m.CircleTypes_7D
	}
	return 0
}

func (m *CircleUserOfflineFeatures) GetCircleTypes_14D() uint64 {
	if m != nil {
		return m.CircleTypes_14D
	}
	return 0
}

// slog circle item features
type CircleItemOfflineFeatures struct {
	CircleId             uint64   `protobuf:"varint,1,opt,name=circle_id,json=circleId,proto3" json:"circle_id,omitempty"`
	IMemberCnt           uint64   `protobuf:"varint,2,opt,name=i_member_cnt,json=iMemberCnt,proto3" json:"i_member_cnt,omitempty"`
	IBbsCnt              uint64   `protobuf:"varint,3,opt,name=i_bbs_cnt,json=iBbsCnt,proto3" json:"i_bbs_cnt,omitempty"`
	IIsHot               uint64   `protobuf:"varint,4,opt,name=i_is_hot,json=iIsHot,proto3" json:"i_is_hot,omitempty"`
	IScore               uint64   `protobuf:"varint,5,opt,name=i_score,json=iScore,proto3" json:"i_score,omitempty"`
	IAmount              uint64   `protobuf:"varint,6,opt,name=i_amount,json=iAmount,proto3" json:"i_amount,omitempty"`
	IDiffdays            uint64   `protobuf:"varint,7,opt,name=i_diffdays,json=iDiffdays,proto3" json:"i_diffdays,omitempty"`
	IType                uint64   `protobuf:"varint,8,opt,name=i_type,json=iType,proto3" json:"i_type,omitempty"`
	ICircleAvgTime_3D    float64  `protobuf:"fixed64,50,opt,name=i_circle_avg_time_3d,json=iCircleAvgTime3d,proto3" json:"i_circle_avg_time_3d,omitempty"`
	ICircleAvgTime_7D    float64  `protobuf:"fixed64,51,opt,name=i_circle_avg_time_7d,json=iCircleAvgTime7d,proto3" json:"i_circle_avg_time_7d,omitempty"`
	ICircleAvgTime_14D   float64  `protobuf:"fixed64,52,opt,name=i_circle_avg_time_14d,json=iCircleAvgTime14d,proto3" json:"i_circle_avg_time_14d,omitempty"`
	IUserJoin_3D         uint64   `protobuf:"varint,53,opt,name=i_user_join_3d,json=iUserJoin3d,proto3" json:"i_user_join_3d,omitempty"`
	IUserJoin_7D         uint64   `protobuf:"varint,54,opt,name=i_user_join_7d,json=iUserJoin7d,proto3" json:"i_user_join_7d,omitempty"`
	IUserJoin_14D        uint64   `protobuf:"varint,55,opt,name=i_user_join_14d,json=iUserJoin14d,proto3" json:"i_user_join_14d,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CircleItemOfflineFeatures) Reset()         { *m = CircleItemOfflineFeatures{} }
func (m *CircleItemOfflineFeatures) String() string { return proto.CompactTextString(m) }
func (*CircleItemOfflineFeatures) ProtoMessage()    {}
func (*CircleItemOfflineFeatures) Descriptor() ([]byte, []int) {
	return fileDescriptor_offline_936dd885307bb04e, []int{6}
}
func (m *CircleItemOfflineFeatures) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CircleItemOfflineFeatures.Unmarshal(m, b)
}
func (m *CircleItemOfflineFeatures) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CircleItemOfflineFeatures.Marshal(b, m, deterministic)
}
func (dst *CircleItemOfflineFeatures) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CircleItemOfflineFeatures.Merge(dst, src)
}
func (m *CircleItemOfflineFeatures) XXX_Size() int {
	return xxx_messageInfo_CircleItemOfflineFeatures.Size(m)
}
func (m *CircleItemOfflineFeatures) XXX_DiscardUnknown() {
	xxx_messageInfo_CircleItemOfflineFeatures.DiscardUnknown(m)
}

var xxx_messageInfo_CircleItemOfflineFeatures proto.InternalMessageInfo

func (m *CircleItemOfflineFeatures) GetCircleId() uint64 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIMemberCnt() uint64 {
	if m != nil {
		return m.IMemberCnt
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIBbsCnt() uint64 {
	if m != nil {
		return m.IBbsCnt
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIIsHot() uint64 {
	if m != nil {
		return m.IIsHot
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIScore() uint64 {
	if m != nil {
		return m.IScore
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIAmount() uint64 {
	if m != nil {
		return m.IAmount
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIDiffdays() uint64 {
	if m != nil {
		return m.IDiffdays
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIType() uint64 {
	if m != nil {
		return m.IType
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetICircleAvgTime_3D() float64 {
	if m != nil {
		return m.ICircleAvgTime_3D
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetICircleAvgTime_7D() float64 {
	if m != nil {
		return m.ICircleAvgTime_7D
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetICircleAvgTime_14D() float64 {
	if m != nil {
		return m.ICircleAvgTime_14D
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIUserJoin_3D() uint64 {
	if m != nil {
		return m.IUserJoin_3D
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIUserJoin_7D() uint64 {
	if m != nil {
		return m.IUserJoin_7D
	}
	return 0
}

func (m *CircleItemOfflineFeatures) GetIUserJoin_14D() uint64 {
	if m != nil {
		return m.IUserJoin_14D
	}
	return 0
}

func init() {
	proto.RegisterType((*UserOfflineStFeatures)(nil), "rcmd.persona.shengdong.offline.UserOfflineStFeatures")
	proto.RegisterType((*UserOfflineDyFeatures)(nil), "rcmd.persona.shengdong.offline.UserOfflineDyFeatures")
	proto.RegisterType((*ItemOfflineFeatures)(nil), "rcmd.persona.shengdong.offline.ItemOfflineFeatures")
	proto.RegisterType((*UserOfflineV2DyFeatures)(nil), "rcmd.persona.shengdong.offline.UserOfflineV2DyFeatures")
	proto.RegisterType((*ItemOfflineV2Features)(nil), "rcmd.persona.shengdong.offline.ItemOfflineV2Features")
	proto.RegisterType((*CircleUserOfflineFeatures)(nil), "rcmd.persona.shengdong.offline.CircleUserOfflineFeatures")
	proto.RegisterType((*CircleItemOfflineFeatures)(nil), "rcmd.persona.shengdong.offline.CircleItemOfflineFeatures")
}

func init() {
	proto.RegisterFile("rcmd/persona/shengdong/offline.proto", fileDescriptor_offline_936dd885307bb04e)
}

var fileDescriptor_offline_936dd885307bb04e = []byte{
	// 2447 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x59, 0x4d, 0x73, 0xdc, 0xc6,
	0xd1, 0x2e, 0x4a, 0xcb, 0xdd, 0xe5, 0x2c, 0x3f, 0xc1, 0x2f, 0x90, 0x12, 0x29, 0x89, 0x96, 0x6d,
	0x59, 0x7a, 0x45, 0xbe, 0x4b, 0x50, 0x06, 0xe3, 0x53, 0x48, 0x2a, 0x8a, 0x99, 0x72, 0x2a, 0xae,
	0x5d, 0x59, 0x07, 0x5f, 0x50, 0xe0, 0x62, 0xb8, 0x9c, 0x18, 0xbb, 0x40, 0xe1, 0x83, 0xd6, 0xe6,
	0xe4, 0xf2, 0x21, 0xf1, 0x35, 0xc9, 0x39, 0xc9, 0x4d, 0xf7, 0xa4, 0x4a, 0xff, 0x22, 0xc9, 0x5f,
	0xc8, 0x21, 0xb7, 0xfc, 0x8a, 0x54, 0x4f, 0xcf, 0x00, 0x33, 0xc0, 0xc0, 0x95, 0x8b, 0x4a, 0x3b,
	0xdd, 0x8d, 0xee, 0x7e, 0xa6, 0xfb, 0xe9, 0x06, 0x48, 0x1e, 0x27, 0xa3, 0x49, 0x70, 0x14, 0xd3,
	0x24, 0x8d, 0xa6, 0xfe, 0x51, 0x7a, 0x43, 0xa7, 0xe3, 0x20, 0x9a, 0x8e, 0x8f, 0xa2, 0xeb, 0xeb,
	0x90, 0x4d, 0xe9, 0x61, 0x9c, 0x44, 0x59, 0x64, 0xed, 0x83, 0xd6, 0xa1, 0xd0, 0x3a, 0x2c, 0xb4,
	0x0e, 0x85, 0xd6, 0xee, 0xae, 0xf6, 0x94, 0x28, 0xce, 0x58, 0x34, 0x4d, 0xd1, 0xf6, 0xe0, 0xdd,
	0x1d, 0xb2, 0xf9, 0x55, 0x4a, 0x93, 0x5f, 0xa1, 0xee, 0x30, 0x7b, 0x45, 0xfd, 0x2c, 0x4f, 0x68,
	0x6a, 0xed, 0x90, 0xbb, 0x39, 0x0b, 0xec, 0xb9, 0x87, 0x73, 0x4f, 0x96, 0xce, 0x3b, 0xdf, 0xbf,
	0xdb, 0x83, 0x9f, 0x03, 0xf8, 0xc7, 0xb2, 0x49, 0x37, 0xf7, 0xd2, 0xcc, 0x4b, 0xe9, 0x5b, 0xfb,
	0x0e, 0xc8, 0x07, 0xed, 0x7c, 0x98, 0x0d, 0xe9, 0xdb, 0x42, 0xe2, 0x8f, 0xa9, 0x7d, 0xb7, 0x90,
	0x9c, 0x8d, 0xa9, 0xf5, 0x01, 0x59, 0x96, 0x12, 0x6f, 0x9c, 0x44, 0x79, 0x6c, 0xb7, 0xb8, 0xbc,
	0x87, 0xf2, 0x9f, 0xc3, 0x91, 0xb5, 0x4f, 0x7a, 0x5c, 0xe9, 0x37, 0x51, 0xc0, 0xfc, 0x91, 0x3d,
	0xcf, 0x35, 0x16, 0xf2, 0x61, 0xf6, 0x35, 0x3f, 0xb0, 0x9e, 0x11, 0x8b, 0xcb, 0x13, 0x3a, 0x66,
	0x69, 0x46, 0x13, 0xef, 0x3a, 0x89, 0x26, 0x76, 0x9b, 0xab, 0xad, 0xe4, 0xc3, 0x6c, 0x20, 0xce,
	0x5f, 0x25, 0xd1, 0xc4, 0x3a, 0x25, 0x3b, 0x5c, 0xf9, 0x9a, 0x25, 0x69, 0xe6, 0x85, 0xd1, 0x98,
	0x4d, 0xbd, 0x80, 0x5d, 0x5f, 0x07, 0xfe, 0x2c, 0xb5, 0x3b, 0xdc, 0x66, 0x33, 0x1f, 0x66, 0xaf,
	0x40, 0xfc, 0x05, 0x48, 0x5f, 0x0a, 0xe1, 0x67, 0xd6, 0x77, 0xef, 0xed, 0xdf, 0xfe, 0x69, 0xee,
	0x87, 0xf7, 0xf6, 0xdc, 0x1f, 0xde, 0xdb, 0xad, 0x34, 0x8c, 0xc6, 0x07, 0x7f, 0xef, 0x6a, 0x40,
	0xbd, 0x9c, 0xfd, 0x2f, 0x40, 0x9d, 0x92, 0xdd, 0xdc, 0x0b, 0x66, 0x5e, 0xea, 0x25, 0x74, 0xe4,
	0x5d, 0xb3, 0x29, 0x4b, 0x6f, 0xbc, 0xc4, 0xcf, 0xa8, 0x77, 0x7c, 0x1a, 0x70, 0xe8, 0xe6, 0x06,
	0x1b, 0xf9, 0xcb, 0xd9, 0x70, 0x40, 0x47, 0xaf, 0xb8, 0x74, 0xe0, 0x67, 0xf4, 0xf8, 0x34, 0xb0,
	0x4e, 0x88, 0xad, 0x58, 0x86, 0xec, 0x1b, 0x5a, 0xda, 0xdd, 0xe5, 0x76, 0x96, 0xb0, 0xfb, 0x82,
	0x7d, 0x43, 0xa5, 0xd5, 0xa7, 0x90, 0x72, 0x61, 0x75, 0xeb, 0x87, 0x2c, 0xf0, 0xe2, 0xd0, 0x9f,
	0x71, 0x33, 0xc0, 0xbb, 0x35, 0x58, 0x17, 0x66, 0x6f, 0x40, 0xf8, 0x65, 0xe8, 0xcf, 0xc0, 0xae,
	0x8c, 0xd3, 0x0f, 0xc3, 0x5a, 0x9c, 0xf3, 0x65, 0x9c, 0x67, 0x61, 0xa8, 0xc7, 0xf9, 0x8c, 0xac,
	0x2b, 0x1e, 0x0b, 0x5f, 0x6d, 0xee, 0x6b, 0x59, 0xf8, 0xaa, 0xbb, 0xa9, 0xc2, 0xd1, 0x3f, 0x09,
	0xf8, 0x95, 0x98, 0xe0, 0xe8, 0x9f, 0x34, 0xc3, 0x01, 0x76, 0x5d, 0x23, 0x1c, 0x60, 0xd5, 0x08,
	0x07, 0x98, 0x2d, 0x98, 0xe1, 0x00, 0x3b, 0x3d, 0x29, 0xee, 0x0d, 0x92, 0x22, 0xbc, 0x66, 0x96,
	0x15, 0x47, 0x3a, 0xe6, 0x80, 0x5d, 0x05, 0xf3, 0x1e, 0x37, 0x59, 0x17, 0xd0, 0x69, 0x98, 0xeb,
	0xc1, 0xa9, 0x60, 0xb8, 0x81, 0xbd, 0xc8, 0x73, 0x5a, 0xaf, 0x61, 0xe1, 0x1a, 0x11, 0x87, 0x74,
	0x96, 0x6a, 0x88, 0x43, 0x26, 0x9f, 0x40, 0xc3, 0xe8, 0xca, 0x6e, 0x60, 0x2f, 0x73, 0xdd, 0x25,
	0x45, 0xd7, 0x55, 0x55, 0x21, 0x0f, 0xae, 0xea, 0x04, 0xf6, 0x4a, 0xa9, 0x7a, 0x16, 0x86, 0xa0,
	0xea, 0x04, 0xd6, 0x11, 0xd9, 0xac, 0x87, 0x0e, 0x41, 0xac, 0xf2, 0x74, 0x57, 0xb5, 0xb0, 0x21,
	0x0c, 0xa3, 0x01, 0xe0, 0xb3, 0x66, 0x30, 0xd0, 0xcb, 0x1f, 0x82, 0xd1, 0xcb, 0xdf, 0x2a, 0xef,
	0xfb, 0x2c, 0x0c, 0xd5, 0xf2, 0x2f, 0xa1, 0x29, 0x52, 0x00, 0x83, 0xf5, 0x12, 0x1a, 0x91, 0x43,
	0xbd, 0xc3, 0x94, 0x7b, 0x73, 0x03, 0x7b, 0x83, 0x5b, 0x58, 0xd5, 0xda, 0x70, 0x03, 0xeb, 0x98,
	0x6c, 0x1b, 0x0b, 0xd1, 0x0d, 0xec, 0x4d, 0x1e, 0xd7, 0x5a, 0xa5, 0x0e, 0xdd, 0x80, 0xd3, 0xc9,
	0xef, 0x2a, 0x74, 0xf2, 0xaf, 0x15, 0xb2, 0x7e, 0x99, 0xd1, 0x89, 0xa0, 0x93, 0x82, 0x4c, 0x1e,
	0x93, 0x0e, 0xc8, 0xbd, 0x82, 0x50, 0x7a, 0xdf, 0xbf, 0xdb, 0x93, 0x47, 0x83, 0x36, 0xfc, 0xe7,
	0x32, 0xb0, 0x3e, 0x24, 0x2b, 0x0c, 0xa8, 0x2d, 0x8b, 0x32, 0x3f, 0xf4, 0x32, 0x36, 0xa1, 0x9c,
	0x4c, 0x5a, 0x83, 0x45, 0x36, 0xcc, 0x5e, 0xc3, 0xe1, 0x6b, 0x36, 0xa1, 0xd6, 0x01, 0x59, 0xe2,
	0x6a, 0x05, 0xeb, 0x21, 0x25, 0xf7, 0xd8, 0x30, 0x93, 0x5c, 0x67, 0xdd, 0x27, 0x84, 0xeb, 0xa4,
	0xa3, 0x28, 0xa1, 0x82, 0x93, 0xbb, 0x6c, 0x98, 0x0d, 0xe1, 0x37, 0xb0, 0x36, 0x97, 0x8e, 0x58,
	0x32, 0x0a, 0x29, 0x44, 0x35, 0x5f, 0x3c, 0xe2, 0x82, 0x9f, 0x5d, 0x06, 0xd6, 0x23, 0xe1, 0x26,
	0x8b, 0x62, 0x36, 0x02, 0x1d, 0x24, 0x64, 0xc2, 0x63, 0x89, 0xd9, 0xe8, 0x32, 0xb0, 0x76, 0xc9,
	0x02, 0xaa, 0xcc, 0x62, 0x2a, 0xb8, 0xb7, 0x03, 0xe2, 0x59, 0x4c, 0x8b, 0x64, 0x6e, 0x23, 0x36,
	0xa2, 0xa8, 0xd1, 0xe5, 0x1a, 0x90, 0xcc, 0x1b, 0x38, 0xe4, 0x6a, 0x36, 0xe9, 0xc6, 0x72, 0xe8,
	0x2c, 0xe0, 0x68, 0x89, 0x8b, 0xa1, 0x13, 0xcb, 0xa1, 0x43, 0x0a, 0x89, 0x18, 0x3a, 0xb1, 0x3e,
	0x74, 0xb0, 0x21, 0x7b, 0xb1, 0x3e, 0x74, 0x62, 0x65, 0xe8, 0x2c, 0xe2, 0xd0, 0x89, 0xd5, 0xa1,
	0x13, 0xd7, 0x87, 0xce, 0x12, 0x0e, 0x9d, 0xb8, 0x3e, 0x74, 0xe2, 0xc6, 0xa1, 0xb3, 0x8c, 0x43,
	0x27, 0x36, 0x0d, 0x1d, 0x20, 0x47, 0xd6, 0x3c, 0x2b, 0x36, 0x90, 0x1c, 0x99, 0x69, 0x56, 0x34,
	0x5b, 0x42, 0x4f, 0x6e, 0x36, 0x58, 0x0a, 0xa2, 0x63, 0x06, 0xf6, 0xde, 0xc2, 0x86, 0x61, 0x3a,
	0x7b, 0x9f, 0x10, 0x9b, 0x35, 0x8d, 0xa4, 0x6d, 0xec, 0x49, 0x56, 0x1f, 0x49, 0xcf, 0xc9, 0x06,
	0x2b, 0x7b, 0x32, 0xbd, 0xf1, 0x13, 0x0c, 0xcb, 0x46, 0xfc, 0x18, 0x36, 0xe5, 0x10, 0xce, 0x21,
	0x22, 0x93, 0x3a, 0x38, 0xd8, 0xa9, 0xab, 0x0b, 0x12, 0x65, 0x8d, 0x03, 0x6f, 0x17, 0x19, 0x9e,
	0x19, 0x06, 0x5e, 0x53, 0x2e, 0x10, 0xd9, 0x3d, 0x63, 0x2e, 0x10, 0x5c, 0x9f, 0x6c, 0x29, 0x56,
	0xa3, 0x68, 0x32, 0xa1, 0xd3, 0x8c, 0xbb, 0xba, 0xcf, 0xc3, 0x5b, 0x13, 0x36, 0x17, 0x28, 0xa9,
	0x07, 0x58, 0x61, 0xf9, 0x3d, 0x64, 0x79, 0x66, 0x60, 0xf9, 0xd2, 0xae, 0x32, 0x55, 0x20, 0xc2,
	0xfd, 0x32, 0x31, 0x75, 0xaa, 0xe8, 0xf8, 0x29, 0xfe, 0x9c, 0xc0, 0x7e, 0x50, 0xe2, 0x57, 0xb8,
	0x72, 0x9a, 0x32, 0x02, 0x1f, 0x0f, 0x4d, 0x19, 0x81, 0x87, 0x63, 0xb2, 0x6d, 0x84, 0xce, 0x09,
	0xec, 0x47, 0xc8, 0x80, 0x15, 0xe4, 0x9c, 0x6a, 0x9d, 0x15, 0x03, 0xf5, 0x00, 0x07, 0x2a, 0xd3,
	0x07, 0x6a, 0xe9, 0x40, 0xe7, 0xfe, 0x7e, 0x60, 0x7f, 0x50, 0x3a, 0x50, 0xa8, 0xbf, 0xaf, 0xb6,
	0x40, 0x75, 0x81, 0x81, 0x5c, 0x1e, 0x97, 0x2d, 0xa0, 0x2d, 0x30, 0x0d, 0x2d, 0x00, 0x26, 0x1f,
	0xd6, 0x5a, 0x40, 0xac, 0x21, 0xe6, 0x72, 0xeb, 0x07, 0xf6, 0x47, 0x38, 0x33, 0xaa, 0xd5, 0xd6,
	0x6f, 0x46, 0xcc, 0x0d, 0xec, 0x8f, 0x8d, 0x88, 0xb9, 0xaa, 0xa7, 0xfa, 0x00, 0x7c, 0x52, 0x16,
	0x68, 0x65, 0x00, 0x1e, 0x91, 0xcd, 0x3a, 0x0c, 0x60, 0xf2, 0x09, 0xce, 0x59, 0x0d, 0x01, 0x9d,
	0x3a, 0x4c, 0x8b, 0xdf, 0xd3, 0x06, 0xdc, 0x44, 0x07, 0xc5, 0x65, 0x52, 0xd8, 0xa8, 0xf2, 0x9a,
	0x9e, 0x61, 0x80, 0x31, 0x66, 0xc5, 0x9b, 0xb5, 0xbc, 0xa7, 0xb8, 0xd9, 0xdf, 0xff, 0xa1, 0xbf,
	0xd8, 0xe4, 0xaf, 0x09, 0xc4, 0x7e, 0x60, 0x3f, 0x37, 0x82, 0xd8, 0xaf, 0x36, 0x51, 0xa5, 0xf9,
	0x0e, 0xcb, 0xe6, 0xd3, 0x9c, 0xb9, 0x0d, 0x30, 0x42, 0x55, 0x1c, 0x19, 0x60, 0xec, 0x9f, 0xe0,
	0x84, 0xff, 0x4f, 0x65, 0xc2, 0xff, 0x9b, 0x90, 0x6d, 0xe5, 0x85, 0xe1, 0xcd, 0xb1, 0xf2, 0xca,
	0xb0, 0xaa, 0xbc, 0x32, 0xe0, 0x9b, 0xc2, 0x3a, 0x99, 0xcf, 0x8b, 0xf7, 0xa9, 0xd6, 0xa0, 0x95,
	0xc3, 0x60, 0xdb, 0x26, 0x9d, 0xdc, 0xcb, 0x61, 0x7c, 0xf0, 0xc9, 0xdd, 0x1a, 0xb4, 0xf3, 0xaf,
	0xe0, 0x17, 0x6a, 0xc3, 0xb8, 0x6b, 0x09, 0x6d, 0x18, 0x76, 0x3b, 0xf0, 0xee, 0xa5, 0xbc, 0x39,
	0xb5, 0x06, 0x9d, 0x5c, 0x8c, 0xb0, 0xa7, 0x64, 0x2d, 0x2f, 0xe7, 0x57, 0x1a, 0xe5, 0xc9, 0x88,
	0x8a, 0x1d, 0x7d, 0x25, 0x97, 0xf3, 0x6b, 0xc8, 0x8f, 0xad, 0xff, 0x87, 0x5d, 0xed, 0xea, 0x0a,
	0x71, 0xf6, 0x6f, 0xc7, 0xb0, 0x5d, 0x28, 0xfb, 0xf9, 0x5a, 0x7e, 0x7e, 0x95, 0x0e, 0xe8, 0xe8,
	0x0c, 0x25, 0xd0, 0x15, 0x4f, 0x61, 0x73, 0x04, 0x0b, 0x4e, 0x55, 0x8c, 0x7e, 0xcb, 0x2f, 0xb3,
	0x2b, 0xb6, 0xae, 0xf3, 0xab, 0x14, 0x48, 0x8a, 0xd1, 0x6f, 0xe1, 0x1a, 0x0b, 0xdd, 0xe2, 0x16,
	0xcb, 0x5d, 0x7c, 0x59, 0x3c, 0x1a, 0x6e, 0x10, 0x9e, 0x7b, 0x48, 0x36, 0xea, 0x91, 0xb8, 0xb8,
	0x87, 0xcf, 0x0d, 0x56, 0xf5, 0x40, 0xdc, 0xc0, 0x72, 0x60, 0x37, 0x93, 0x71, 0xe8, 0xfc, 0xdc,
	0x13, 0x0b, 0x1d, 0x06, 0x73, 0xa1, 0x10, 0xf4, 0x73, 0xd5, 0x89, 0xc2, 0x4d, 0x8b, 0x02, 0x1d,
	0x74, 0x72, 0x21, 0xc9, 0xa9, 0x88, 0x09, 0x7c, 0xc8, 0x98, 0x1c, 0x5c, 0xbf, 0x45, 0x4c, 0x67,
	0x61, 0x28, 0x62, 0x72, 0x82, 0x12, 0x4d, 0x55, 0x1f, 0x9e, 0xbf, 0x5c, 0xa2, 0x59, 0x1a, 0x80,
	0x87, 0x27, 0x70, 0x57, 0x32, 0x20, 0x8e, 0xa6, 0x5b, 0xae, 0xe1, 0x18, 0x0d, 0x80, 0xe9, 0x36,
	0xe0, 0xe3, 0xe0, 0x16, 0x5e, 0xc3, 0xa7, 0x1a, 0x8b, 0x8a, 0xce, 0x1a, 0x7f, 0xba, 0x8c, 0x45,
	0x01, 0x47, 0xbb, 0xd9, 0x02, 0x1a, 0x4b, 0xbb, 0x59, 0x89, 0x4c, 0x11, 0x77, 0x51, 0x05, 0x8e,
	0x5c, 0xbd, 0x97, 0x94, 0x22, 0x70, 0x80, 0x3a, 0xb6, 0x95, 0x0c, 0x81, 0x28, 0xb9, 0x3a, 0x14,
	0xc2, 0x86, 0x78, 0x29, 0x13, 0x79, 0x4a, 0x19, 0x54, 0x83, 0x31, 0x7a, 0xb9, 0xe0, 0xd4, 0xa2,
	0x07, 0x0b, 0x0d, 0x49, 0xac, 0x35, 0xb9, 0xdb, 0x2c, 0xa9, 0xa5, 0x66, 0xc2, 0xdc, 0xc1, 0x9d,
	0x46, 0xc7, 0xdc, 0x31, 0x97, 0x8b, 0x5c, 0x67, 0x2a, 0xe5, 0x82, 0xc3, 0x72, 0xcb, 0x94, 0xaa,
	0x8b, 0x0b, 0x8d, 0xa8, 0x48, 0x35, 0x53, 0xfe, 0x22, 0x26, 0x82, 0x89, 0x93, 0x28, 0xc8, 0x47,
	0x54, 0xd9, 0x65, 0x38, 0xe6, 0x5f, 0xe2, 0x31, 0x60, 0x6e, 0xec, 0x55, 0x50, 0xbf, 0x67, 0xea,
	0xd5, 0x5a, 0xff, 0x15, 0xbd, 0x7a, 0x5f, 0xeb, 0xbf, 0xe6, 0x5e, 0x05, 0xdd, 0xbd, 0x5a, 0xaf,
	0xe2, 0x38, 0x30, 0xde, 0x29, 0x18, 0xec, 0x9b, 0xef, 0xd4, 0x1c, 0x0d, 0x60, 0xf9, 0xa0, 0x16,
	0x0d, 0x70, 0xec, 0xc6, 0x77, 0xef, 0xed, 0x7f, 0xfc, 0x59, 0x72, 0x2c, 0x7f, 0x13, 0xba, 0x3d,
	0x3e, 0xf8, 0x3d, 0x21, 0x9b, 0xca, 0x7b, 0xd4, 0x9b, 0xe3, 0x82, 0x63, 0x37, 0x49, 0x1b, 0x9e,
	0x2c, 0x68, 0xb6, 0x35, 0x98, 0xbf, 0xba, 0x4a, 0x2f, 0x39, 0x64, 0xcc, 0x08, 0xd9, 0x1d, 0x31,
	0x45, 0x4c, 0x90, 0xb1, 0x3a, 0x64, 0x77, 0xc5, 0x82, 0xa0, 0x43, 0x66, 0x7c, 0x3a, 0xe4, 0xd4,
	0x32, 0x3d, 0x1d, 0x2a, 0x64, 0x9b, 0x74, 0x98, 0xc7, 0x38, 0xc7, 0x23, 0x69, 0xb7, 0xd9, 0x25,
	0xe7, 0x78, 0xcd, 0x6d, 0x81, 0x7e, 0x5b, 0x73, 0x2b, 0xd1, 0x7f, 0x01, 0xdb, 0x82, 0x86, 0x7e,
	0x19, 0x68, 0x47, 0x2c, 0x8b, 0x0a, 0xfc, 0xca, 0x05, 0xb3, 0x3a, 0xfc, 0xdd, 0x5a, 0x66, 0x10,
	0x27, 0x7f, 0x4f, 0x04, 0x5d, 0xfe, 0x82, 0x86, 0x84, 0xdd, 0x05, 0x1d, 0xfe, 0x72, 0xc6, 0xb3,
	0xc0, 0x57, 0x48, 0x22, 0xb2, 0xc0, 0x17, 0xc8, 0x43, 0xd8, 0x47, 0x0d, 0x1c, 0xde, 0x43, 0x8e,
	0x62, 0x55, 0x0e, 0x7f, 0x42, 0xd6, 0x98, 0xce, 0x38, 0x8e, 0xe4, 0xe2, 0x25, 0x56, 0x12, 0x8e,
	0x53, 0xd1, 0x94, 0x3d, 0xbb, 0xa4, 0x69, 0x8a, 0x9e, 0x35, 0xc6, 0xe0, 0x48, 0x0a, 0xae, 0xc4,
	0xe0, 0x28, 0xb0, 0x68, 0xac, 0xb7, 0x52, 0xc2, 0xa2, 0xb3, 0x5e, 0xe5, 0x96, 0x5c, 0x24, 0x60,
	0x11, 0x85, 0xb8, 0x24, 0x57, 0x89, 0x42, 0x9d, 0x04, 0x7d, 0x24, 0x5f, 0x11, 0x45, 0x39, 0x08,
	0xfa, 0x15, 0x7d, 0xc9, 0x77, 0xae, 0x64, 0x5f, 0xa9, 0x2f, 0xe8, 0xce, 0x35, 0xe1, 0xe1, 0x16,
	0xfc, 0xab, 0xe0, 0xd1, 0x14, 0x89, 0x23, 0xdf, 0x31, 0x2b, 0x91, 0x38, 0xcd, 0xd5, 0x55, 0x92,
	0x6f, 0xad, 0xba, 0x04, 0xfd, 0xb2, 0x1a, 0xa9, 0x6e, 0x69, 0xd0, 0x88, 0x0b, 0xaa, 0x6b, 0xf6,
	0x0b, 0xfa, 0x55, 0xcb, 0x90, 0x8f, 0x78, 0x63, 0x28, 0xae, 0x64, 0x60, 0xab, 0x1a, 0x89, 0xab,
	0x34, 0x65, 0x75, 0x06, 0xef, 0x94, 0x4d, 0xa9, 0xcf, 0xe0, 0x6d, 0xd2, 0x89, 0xc5, 0xe2, 0x85,
	0xc4, 0xdb, 0x8e, 0x71, 0xf1, 0xaa, 0x97, 0xa7, 0x8b, 0x64, 0xab, 0x97, 0xa7, 0xcb, 0x17, 0xba,
	0x98, 0x2f, 0x74, 0xc8, 0xad, 0xad, 0x18, 0x16, 0xba, 0x7d, 0xd2, 0x63, 0xca, 0xb7, 0x14, 0xa4,
	0xd2, 0x05, 0x56, 0x7c, 0x49, 0xd9, 0x21, 0xdd, 0x58, 0xae, 0x70, 0x48, 0x9b, 0x9d, 0x58, 0xac,
	0x70, 0xfc, 0x79, 0xb0, 0xf2, 0x3d, 0x10, 0xcf, 0x3b, 0x1b, 0x53, 0xce, 0x89, 0xff, 0xac, 0x71,
	0xe2, 0x0f, 0x6d, 0xb2, 0x83, 0x8f, 0x54, 0xf6, 0xcf, 0x1f, 0xd9, 0x3d, 0xef, 0x91, 0x85, 0x11,
	0xcb, 0x66, 0xde, 0x8d, 0x9f, 0xde, 0x88, 0xfd, 0xb3, 0x0b, 0x07, 0x9f, 0xfb, 0xe9, 0x0d, 0x08,
	0xcb, 0xaf, 0x27, 0x48, 0x7a, 0x5d, 0x5f, 0x7e, 0x3a, 0xd9, 0x22, 0x6d, 0x11, 0x2d, 0x2e, 0xa2,
	0xe2, 0x17, 0xf8, 0x80, 0xd4, 0x91, 0xd0, 0xe0, 0xbf, 0xd6, 0x7d, 0xb2, 0x80, 0x9f, 0x41, 0x32,
	0x9a, 0x0a, 0x12, 0x2b, 0x0f, 0x78, 0x04, 0x37, 0x7e, 0x32, 0xf1, 0xe8, 0xdb, 0x58, 0x10, 0x56,
	0x97, 0x1f, 0xfc, 0xec, 0x6d, 0x6c, 0x3d, 0x22, 0x8b, 0x71, 0x42, 0xd3, 0x8c, 0x8d, 0x29, 0x97,
	0x23, 0x3f, 0xf5, 0xe4, 0x19, 0xa8, 0xec, 0x11, 0x72, 0x9d, 0x30, 0x3a, 0x0d, 0xbc, 0xd1, 0x34,
	0x13, 0xe4, 0xb4, 0x80, 0x27, 0x17, 0xd3, 0x0c, 0x60, 0xe5, 0x77, 0x05, 0x42, 0xa4, 0xa7, 0x0e,
	0xfc, 0x16, 0xa2, 0x6b, 0x7f, 0x9a, 0x72, 0x11, 0x2e, 0x89, 0x1d, 0xf8, 0x0d, 0xa2, 0x3d, 0x42,
	0xc4, 0x55, 0x81, 0x10, 0x39, 0x68, 0x01, 0x4f, 0x40, 0xcc, 0x67, 0xaf, 0x50, 0x48, 0x33, 0x7f,
	0xe6, 0xc9, 0x36, 0x3a, 0x16, 0xb3, 0x17, 0xaf, 0x60, 0x98, 0xf9, 0xb3, 0xd7, 0xca, 0xfe, 0x55,
	0xb3, 0x70, 0x03, 0xdb, 0x31, 0x5a, 0xb8, 0x62, 0x7d, 0xa8, 0x59, 0x40, 0xdf, 0x9d, 0x88, 0x8f,
	0xa0, 0xba, 0x09, 0xb4, 0xdd, 0xc7, 0x64, 0xb5, 0xb0, 0xf9, 0x75, 0xc4, 0xa6, 0x10, 0xd2, 0x0b,
	0xb1, 0xca, 0xa0, 0xf6, 0x2f, 0x22, 0x36, 0x75, 0x0c, 0x8a, 0x6e, 0x60, 0x7f, 0x5a, 0x53, 0x94,
	0x0b, 0x89, 0xaa, 0x08, 0x01, 0xb8, 0x62, 0x48, 0x97, 0x9a, 0xe0, 0xfc, 0x23, 0xb2, 0x22, 0x14,
	0x61, 0x4c, 0xa4, 0xe0, 0xfb, 0x14, 0x1f, 0x89, 0xc7, 0x30, 0x2c, 0x52, 0xa7, 0xae, 0xe7, 0x06,
	0xf6, 0x4f, 0x6a, 0x7a, 0x9c, 0xd4, 0x56, 0x35, 0x3d, 0xf0, 0xfc, 0x19, 0x7a, 0x56, 0x14, 0xe5,
	0x7a, 0xf0, 0xd7, 0xbf, 0x14, 0xad, 0x80, 0xd2, 0x83, 0x3f, 0xb6, 0x64, 0x2b, 0x98, 0x3e, 0xb6,
	0xf2, 0xc2, 0x97, 0xcd, 0x38, 0x27, 0x0b, 0x5f, 0xf4, 0xe2, 0x43, 0xb2, 0xc8, 0xbc, 0x09, 0x9d,
	0x5c, 0xd1, 0x84, 0x17, 0x00, 0x36, 0x06, 0x61, 0xbf, 0xe4, 0x47, 0x50, 0x01, 0xfc, 0xa3, 0x26,
	0x90, 0x01, 0x88, 0xb1, 0x35, 0x3a, 0x40, 0x02, 0x20, 0xb3, 0x49, 0x97, 0x79, 0x2c, 0xf5, 0x6e,
	0xa2, 0x4c, 0xf6, 0x06, 0xbb, 0x4c, 0x3f, 0x8f, 0x32, 0x75, 0x54, 0xce, 0x6b, 0xa3, 0x72, 0x07,
	0x4c, 0xfc, 0x49, 0x94, 0x4f, 0x33, 0xd1, 0x21, 0x1d, 0x76, 0xc6, 0x7f, 0x42, 0x29, 0x32, 0xfd,
	0x6f, 0x57, 0x40, 0x1b, 0xc5, 0xa7, 0xc3, 0x4d, 0xd2, 0x66, 0xe5, 0x87, 0xd3, 0xd6, 0x60, 0x9e,
	0xf1, 0xa1, 0xcc, 0x79, 0x5e, 0x24, 0xe8, 0xdf, 0x8e, 0x2b, 0x05, 0xba, 0x2a, 0x68, 0xe7, 0xec,
	0x76, 0x2c, 0xea, 0xd3, 0xa8, 0x5f, 0x94, 0x67, 0x45, 0x5f, 0xf2, 0x6a, 0x55, 0xbf, 0x2c, 0xce,
	0x35, 0xdd, 0x00, 0xca, 0x83, 0x7f, 0x4e, 0xce, 0x53, 0x9a, 0x54, 0x2a, 0xb3, 0xc7, 0x80, 0xa9,
	0x44, 0x5d, 0x56, 0x94, 0x8a, 0xaa, 0x2c, 0x95, 0x5c, 0xf1, 0x05, 0xbc, 0x54, 0x2a, 0x2b, 0x72,
	0xb1, 0xd0, 0x92, 0x55, 0xf1, 0xb7, 0x6a, 0x55, 0x9c, 0x9f, 0x7f, 0xfd, 0xd3, 0x71, 0x14, 0xfa,
	0xd3, 0xf1, 0xe1, 0x8b, 0xe3, 0x2c, 0x3b, 0x1c, 0x45, 0x93, 0x23, 0xfe, 0xd7, 0xd0, 0x51, 0x14,
	0x1e, 0xa5, 0x34, 0xb9, 0x65, 0x23, 0x9a, 0x1e, 0xfd, 0xf8, 0x9f, 0x5e, 0xaf, 0xda, 0xdc, 0xc2,
	0xf9, 0x6f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x5e, 0x10, 0x56, 0xd3, 0xa3, 0x1d, 0x00, 0x00,
}
