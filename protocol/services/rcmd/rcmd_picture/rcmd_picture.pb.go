// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd-picture/rcmd_picture.proto

package rcmd_picture // import "golang.52tt.com/protocol/services/rcmd/rcmd_picture"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PictureSelector_Gender int32

const (
	PictureSelector_Gender_Whatever PictureSelector_Gender = 0
	PictureSelector_Gender_Female   PictureSelector_Gender = 1
	PictureSelector_Gender_Male     PictureSelector_Gender = 2
)

var PictureSelector_Gender_name = map[int32]string{
	0: "Gender_Whatever",
	1: "Gender_Female",
	2: "Gender_Male",
}
var PictureSelector_Gender_value = map[string]int32{
	"Gender_Whatever": 0,
	"Gender_Female":   1,
	"Gender_Male":     2,
}

func (x PictureSelector_Gender) String() string {
	return proto.EnumName(PictureSelector_Gender_name, int32(x))
}
func (PictureSelector_Gender) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{4, 0}
}

type CreatePicturesReq struct {
	Pictures             []*CreatePicture `protobuf:"bytes,1,rep,name=pictures,proto3" json:"pictures,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *CreatePicturesReq) Reset()         { *m = CreatePicturesReq{} }
func (m *CreatePicturesReq) String() string { return proto.CompactTextString(m) }
func (*CreatePicturesReq) ProtoMessage()    {}
func (*CreatePicturesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{0}
}
func (m *CreatePicturesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePicturesReq.Unmarshal(m, b)
}
func (m *CreatePicturesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePicturesReq.Marshal(b, m, deterministic)
}
func (dst *CreatePicturesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePicturesReq.Merge(dst, src)
}
func (m *CreatePicturesReq) XXX_Size() int {
	return xxx_messageInfo_CreatePicturesReq.Size(m)
}
func (m *CreatePicturesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePicturesReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePicturesReq proto.InternalMessageInfo

func (m *CreatePicturesReq) GetPictures() []*CreatePicture {
	if m != nil {
		return m.Pictures
	}
	return nil
}

type CreatePicturesResp struct {
	Success              uint64   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Duplicate            uint64   `protobuf:"varint,2,opt,name=duplicate,proto3" json:"duplicate,omitempty"`
	CreateFailedCount    uint64   `protobuf:"varint,3,opt,name=create_failed_count,json=createFailedCount,proto3" json:"create_failed_count,omitempty"`
	InvalidCount         uint64   `protobuf:"varint,4,opt,name=invalid_count,json=invalidCount,proto3" json:"invalid_count,omitempty"`
	InvalidInfo          []string `protobuf:"bytes,5,rep,name=invalid_info,json=invalidInfo,proto3" json:"invalid_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePicturesResp) Reset()         { *m = CreatePicturesResp{} }
func (m *CreatePicturesResp) String() string { return proto.CompactTextString(m) }
func (*CreatePicturesResp) ProtoMessage()    {}
func (*CreatePicturesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{1}
}
func (m *CreatePicturesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePicturesResp.Unmarshal(m, b)
}
func (m *CreatePicturesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePicturesResp.Marshal(b, m, deterministic)
}
func (dst *CreatePicturesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePicturesResp.Merge(dst, src)
}
func (m *CreatePicturesResp) XXX_Size() int {
	return xxx_messageInfo_CreatePicturesResp.Size(m)
}
func (m *CreatePicturesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePicturesResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePicturesResp proto.InternalMessageInfo

func (m *CreatePicturesResp) GetSuccess() uint64 {
	if m != nil {
		return m.Success
	}
	return 0
}

func (m *CreatePicturesResp) GetDuplicate() uint64 {
	if m != nil {
		return m.Duplicate
	}
	return 0
}

func (m *CreatePicturesResp) GetCreateFailedCount() uint64 {
	if m != nil {
		return m.CreateFailedCount
	}
	return 0
}

func (m *CreatePicturesResp) GetInvalidCount() uint64 {
	if m != nil {
		return m.InvalidCount
	}
	return 0
}

func (m *CreatePicturesResp) GetInvalidInfo() []string {
	if m != nil {
		return m.InvalidInfo
	}
	return nil
}

type CreatePicture struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Gender               string   `protobuf:"bytes,4,opt,name=gender,proto3" json:"gender,omitempty"`
	Style                string   `protobuf:"bytes,5,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreatePicture) Reset()         { *m = CreatePicture{} }
func (m *CreatePicture) String() string { return proto.CompactTextString(m) }
func (*CreatePicture) ProtoMessage()    {}
func (*CreatePicture) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{2}
}
func (m *CreatePicture) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreatePicture.Unmarshal(m, b)
}
func (m *CreatePicture) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreatePicture.Marshal(b, m, deterministic)
}
func (dst *CreatePicture) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreatePicture.Merge(dst, src)
}
func (m *CreatePicture) XXX_Size() int {
	return xxx_messageInfo_CreatePicture.Size(m)
}
func (m *CreatePicture) XXX_DiscardUnknown() {
	xxx_messageInfo_CreatePicture.DiscardUnknown(m)
}

var xxx_messageInfo_CreatePicture proto.InternalMessageInfo

func (m *CreatePicture) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CreatePicture) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *CreatePicture) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *CreatePicture) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *CreatePicture) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

type GetPicturesReq struct {
	Uid uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 图片选择条件，每个PictureSelector, 代表一张图片，如果要3张图片，就需要传3个PictureSelector
	Selectors            []*PictureSelector `protobuf:"bytes,2,rep,name=selectors,proto3" json:"selectors,omitempty"`
	AppId                uint32             `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetPicturesReq) Reset()         { *m = GetPicturesReq{} }
func (m *GetPicturesReq) String() string { return proto.CompactTextString(m) }
func (*GetPicturesReq) ProtoMessage()    {}
func (*GetPicturesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{3}
}
func (m *GetPicturesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPicturesReq.Unmarshal(m, b)
}
func (m *GetPicturesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPicturesReq.Marshal(b, m, deterministic)
}
func (dst *GetPicturesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPicturesReq.Merge(dst, src)
}
func (m *GetPicturesReq) XXX_Size() int {
	return xxx_messageInfo_GetPicturesReq.Size(m)
}
func (m *GetPicturesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPicturesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPicturesReq proto.InternalMessageInfo

func (m *GetPicturesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetPicturesReq) GetSelectors() []*PictureSelector {
	if m != nil {
		return m.Selectors
	}
	return nil
}

func (m *GetPicturesReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

type PictureSelector struct {
	Title                string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Gender               PictureSelector_Gender `protobuf:"varint,3,opt,name=gender,proto3,enum=rcmd.rcmd_picture.PictureSelector_Gender" json:"gender,omitempty"`
	Style                uint32                 `protobuf:"varint,4,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *PictureSelector) Reset()         { *m = PictureSelector{} }
func (m *PictureSelector) String() string { return proto.CompactTextString(m) }
func (*PictureSelector) ProtoMessage()    {}
func (*PictureSelector) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{4}
}
func (m *PictureSelector) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PictureSelector.Unmarshal(m, b)
}
func (m *PictureSelector) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PictureSelector.Marshal(b, m, deterministic)
}
func (dst *PictureSelector) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PictureSelector.Merge(dst, src)
}
func (m *PictureSelector) XXX_Size() int {
	return xxx_messageInfo_PictureSelector.Size(m)
}
func (m *PictureSelector) XXX_DiscardUnknown() {
	xxx_messageInfo_PictureSelector.DiscardUnknown(m)
}

var xxx_messageInfo_PictureSelector proto.InternalMessageInfo

func (m *PictureSelector) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *PictureSelector) GetGender() PictureSelector_Gender {
	if m != nil {
		return m.Gender
	}
	return PictureSelector_Gender_Whatever
}

func (m *PictureSelector) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

type GetPicturesResp struct {
	Pictures             []*Picture `protobuf:"bytes,1,rep,name=pictures,proto3" json:"pictures,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetPicturesResp) Reset()         { *m = GetPicturesResp{} }
func (m *GetPicturesResp) String() string { return proto.CompactTextString(m) }
func (*GetPicturesResp) ProtoMessage()    {}
func (*GetPicturesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{5}
}
func (m *GetPicturesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPicturesResp.Unmarshal(m, b)
}
func (m *GetPicturesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPicturesResp.Marshal(b, m, deterministic)
}
func (dst *GetPicturesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPicturesResp.Merge(dst, src)
}
func (m *GetPicturesResp) XXX_Size() int {
	return xxx_messageInfo_GetPicturesResp.Size(m)
}
func (m *GetPicturesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPicturesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPicturesResp proto.InternalMessageInfo

func (m *GetPicturesResp) GetPictures() []*Picture {
	if m != nil {
		return m.Pictures
	}
	return nil
}

type Picture struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Style                uint32   `protobuf:"varint,3,opt,name=style,proto3" json:"style,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Picture) Reset()         { *m = Picture{} }
func (m *Picture) String() string { return proto.CompactTextString(m) }
func (*Picture) ProtoMessage()    {}
func (*Picture) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_picture_32c7df4e29c05709, []int{6}
}
func (m *Picture) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Picture.Unmarshal(m, b)
}
func (m *Picture) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Picture.Marshal(b, m, deterministic)
}
func (dst *Picture) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Picture.Merge(dst, src)
}
func (m *Picture) XXX_Size() int {
	return xxx_messageInfo_Picture.Size(m)
}
func (m *Picture) XXX_DiscardUnknown() {
	xxx_messageInfo_Picture.DiscardUnknown(m)
}

var xxx_messageInfo_Picture proto.InternalMessageInfo

func (m *Picture) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *Picture) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *Picture) GetStyle() uint32 {
	if m != nil {
		return m.Style
	}
	return 0
}

func init() {
	proto.RegisterType((*CreatePicturesReq)(nil), "rcmd.rcmd_picture.CreatePicturesReq")
	proto.RegisterType((*CreatePicturesResp)(nil), "rcmd.rcmd_picture.CreatePicturesResp")
	proto.RegisterType((*CreatePicture)(nil), "rcmd.rcmd_picture.CreatePicture")
	proto.RegisterType((*GetPicturesReq)(nil), "rcmd.rcmd_picture.GetPicturesReq")
	proto.RegisterType((*PictureSelector)(nil), "rcmd.rcmd_picture.PictureSelector")
	proto.RegisterType((*GetPicturesResp)(nil), "rcmd.rcmd_picture.GetPicturesResp")
	proto.RegisterType((*Picture)(nil), "rcmd.rcmd_picture.Picture")
	proto.RegisterEnum("rcmd.rcmd_picture.PictureSelector_Gender", PictureSelector_Gender_name, PictureSelector_Gender_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RCMDPictureClient is the client API for RCMDPicture service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RCMDPictureClient interface {
	GetPictures(ctx context.Context, in *GetPicturesReq, opts ...grpc.CallOption) (*GetPicturesResp, error)
	CreatePictures(ctx context.Context, in *CreatePicturesReq, opts ...grpc.CallOption) (*CreatePicturesResp, error)
}

type rCMDPictureClient struct {
	cc *grpc.ClientConn
}

func NewRCMDPictureClient(cc *grpc.ClientConn) RCMDPictureClient {
	return &rCMDPictureClient{cc}
}

func (c *rCMDPictureClient) GetPictures(ctx context.Context, in *GetPicturesReq, opts ...grpc.CallOption) (*GetPicturesResp, error) {
	out := new(GetPicturesResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_picture.RCMDPicture/GetPictures", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rCMDPictureClient) CreatePictures(ctx context.Context, in *CreatePicturesReq, opts ...grpc.CallOption) (*CreatePicturesResp, error) {
	out := new(CreatePicturesResp)
	err := c.cc.Invoke(ctx, "/rcmd.rcmd_picture.RCMDPicture/CreatePictures", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RCMDPictureServer is the server API for RCMDPicture service.
type RCMDPictureServer interface {
	GetPictures(context.Context, *GetPicturesReq) (*GetPicturesResp, error)
	CreatePictures(context.Context, *CreatePicturesReq) (*CreatePicturesResp, error)
}

func RegisterRCMDPictureServer(s *grpc.Server, srv RCMDPictureServer) {
	s.RegisterService(&_RCMDPicture_serviceDesc, srv)
}

func _RCMDPicture_GetPictures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPicturesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDPictureServer).GetPictures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_picture.RCMDPicture/GetPictures",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDPictureServer).GetPictures(ctx, req.(*GetPicturesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RCMDPicture_CreatePictures_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePicturesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RCMDPictureServer).CreatePictures(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.rcmd_picture.RCMDPicture/CreatePictures",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RCMDPictureServer).CreatePictures(ctx, req.(*CreatePicturesReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RCMDPicture_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.rcmd_picture.RCMDPicture",
	HandlerType: (*RCMDPictureServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPictures",
			Handler:    _RCMDPicture_GetPictures_Handler,
		},
		{
			MethodName: "CreatePictures",
			Handler:    _RCMDPicture_CreatePictures_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd-picture/rcmd_picture.proto",
}

func init() {
	proto.RegisterFile("rcmd-picture/rcmd_picture.proto", fileDescriptor_rcmd_picture_32c7df4e29c05709)
}

var fileDescriptor_rcmd_picture_32c7df4e29c05709 = []byte{
	// 523 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0x5d, 0x8b, 0xd3, 0x40,
	0x14, 0x35, 0xe9, 0xc7, 0x9a, 0x5b, 0xfb, 0x35, 0xab, 0x12, 0x16, 0xc1, 0x6e, 0x54, 0xa8, 0x0f,
	0xa6, 0xd0, 0x65, 0x7d, 0xf2, 0xc1, 0x5a, 0xd9, 0xa5, 0x0f, 0x0b, 0x3a, 0x0a, 0x82, 0x20, 0x21,
	0x4e, 0x6e, 0xeb, 0x40, 0x9a, 0xc4, 0xcc, 0xa4, 0x20, 0xfe, 0x3b, 0xf5, 0x87, 0xc9, 0x4c, 0xa6,
	0x1f, 0xd9, 0xae, 0xd4, 0x7d, 0x9b, 0x7b, 0xee, 0xb9, 0x77, 0xce, 0xe1, 0xde, 0x19, 0x78, 0x9c,
	0xb3, 0x65, 0xf4, 0x22, 0xe3, 0x4c, 0x16, 0x39, 0x8e, 0x54, 0x10, 0x98, 0xc0, 0xcf, 0xf2, 0x54,
	0xa6, 0xa4, 0xaf, 0x30, 0x7f, 0x37, 0xe1, 0xbd, 0x87, 0xfe, 0x34, 0xc7, 0x50, 0xe2, 0xbb, 0x12,
	0x10, 0x14, 0xbf, 0x93, 0x57, 0x70, 0xd7, 0xe4, 0x85, 0x6b, 0x0d, 0x6a, 0xc3, 0xd6, 0x78, 0xe0,
	0xef, 0x95, 0xfa, 0x95, 0x3a, 0xba, 0xa9, 0xf0, 0xfe, 0x58, 0x40, 0xae, 0xf7, 0x14, 0x19, 0x71,
	0xe1, 0x48, 0x14, 0x8c, 0xa1, 0x50, 0x3d, 0xad, 0x61, 0x9d, 0xae, 0x43, 0xf2, 0x08, 0x9c, 0xa8,
	0xc8, 0x62, 0xce, 0x42, 0x89, 0xae, 0xad, 0x73, 0x5b, 0x80, 0xf8, 0x70, 0xcc, 0x74, 0xb7, 0x60,
	0x1e, 0xf2, 0x18, 0xa3, 0x80, 0xa5, 0x45, 0x22, 0xdd, 0x9a, 0xe6, 0xf5, 0xcb, 0xd4, 0x85, 0xce,
	0x4c, 0x55, 0x82, 0x3c, 0x81, 0x36, 0x4f, 0x56, 0x61, 0xcc, 0xd7, 0xcc, 0xba, 0x66, 0xde, 0x33,
	0x60, 0x49, 0x3a, 0x85, 0x75, 0x1c, 0xf0, 0x64, 0x9e, 0xba, 0x8d, 0x41, 0x6d, 0xe8, 0xd0, 0x96,
	0xc1, 0x66, 0xc9, 0x3c, 0xf5, 0x0a, 0x68, 0x57, 0x5c, 0x90, 0x0e, 0xd8, 0x3c, 0xd2, 0xda, 0x1d,
	0x6a, 0xf3, 0x88, 0xf4, 0xa0, 0x56, 0xe4, 0xb1, 0x16, 0xec, 0x50, 0x75, 0x24, 0xf7, 0xa1, 0x21,
	0xb9, 0x8c, 0x51, 0x8b, 0x73, 0x68, 0x19, 0x90, 0x87, 0xd0, 0x5c, 0x60, 0x12, 0x61, 0xae, 0x95,
	0x38, 0xd4, 0x44, 0x8a, 0x2d, 0xe4, 0x8f, 0x18, 0xdd, 0x46, 0xc9, 0xd6, 0x81, 0xf7, 0x13, 0x3a,
	0x97, 0x28, 0x77, 0xa7, 0xa1, 0xee, 0x31, 0x17, 0xb7, 0xa9, 0x3a, 0x92, 0xd7, 0xe0, 0x08, 0x8c,
	0x91, 0xc9, 0x34, 0x17, 0xae, 0xad, 0x07, 0xe4, 0xdd, 0x30, 0x20, 0xd3, 0xe4, 0x83, 0xa1, 0xd2,
	0x6d, 0x11, 0x79, 0x00, 0xcd, 0x30, 0xcb, 0x02, 0x1e, 0x69, 0xa9, 0x6d, 0xda, 0x08, 0xb3, 0x6c,
	0x16, 0x79, 0xbf, 0x2d, 0xe8, 0x5e, 0xab, 0xda, 0x9a, 0xb2, 0x77, 0x4d, 0x4d, 0x36, 0xa6, 0x54,
	0x83, 0xce, 0xf8, 0xf9, 0xe1, 0xfb, 0xfd, 0x4b, 0x5d, 0xb0, 0xef, 0xbf, 0x5e, 0x4a, 0x28, 0xfd,
	0x4f, 0xa0, 0x59, 0xf2, 0xc8, 0x31, 0x74, 0xcb, 0x53, 0xf0, 0xe9, 0x5b, 0x28, 0x71, 0x85, 0x79,
	0xef, 0x0e, 0xe9, 0x43, 0xdb, 0x80, 0x17, 0xb8, 0x0c, 0x63, 0xec, 0x59, 0xa4, 0x0b, 0x2d, 0x03,
	0x5d, 0x29, 0xc0, 0xf6, 0x66, 0xaa, 0x50, 0x56, 0x96, 0xef, 0xe5, 0xde, 0x46, 0x9f, 0xfc, 0x5b,
	0xf0, 0xce, 0x2e, 0x4f, 0xe0, 0xe8, 0x56, 0xe3, 0x2f, 0x0d, 0xd5, 0x76, 0x0c, 0x8d, 0x7f, 0x59,
	0xd0, 0xa2, 0xd3, 0xab, 0xb7, 0xeb, 0x3e, 0x1f, 0x95, 0xdc, 0x8d, 0x3a, 0x72, 0x7a, 0x83, 0x8e,
	0xea, 0x02, 0x9c, 0x78, 0x87, 0x28, 0x22, 0x23, 0x5f, 0xa0, 0x53, 0x7d, 0x73, 0xe4, 0xe9, 0xa1,
	0x27, 0xab, 0x7b, 0x3f, 0xfb, 0x0f, 0x96, 0xc8, 0xde, 0x9c, 0x7f, 0x3e, 0x5b, 0xa4, 0x71, 0x98,
	0x2c, 0xfc, 0xf3, 0xb1, 0x94, 0x3e, 0x4b, 0x97, 0x23, 0xfd, 0xa5, 0xb0, 0x34, 0x1e, 0x09, 0xcc,
	0x57, 0x9c, 0xa1, 0xd0, 0x3f, 0x4e, 0xe5, 0xdb, 0xf9, 0xda, 0xd4, 0xa4, 0xb3, 0xbf, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xdf, 0x01, 0xb1, 0x35, 0x9a, 0x04, 0x00, 0x00,
}
