// Code generated by protoc-gen-go. DO NOT EDIT.
// source: operating-platform/rcmd_operating_platform.proto

package operating_platform // import "golang.52tt.com/protocol/services/rcmd/operating_platform"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel "golang.52tt.com/protocol/services/rcmd/persona/tt/channel"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TopicStreamType int32

const (
	TopicStreamType_TopicStreamType_Invalid TopicStreamType = 0
	TopicStreamType_New                     TopicStreamType = 1
	TopicStreamType_Hot                     TopicStreamType = 2
)

var TopicStreamType_name = map[int32]string{
	0: "TopicStreamType_Invalid",
	1: "New",
	2: "Hot",
}
var TopicStreamType_value = map[string]int32{
	"TopicStreamType_Invalid": 0,
	"New": 1,
	"Hot": 2,
}

func (x TopicStreamType) String() string {
	return proto.EnumName(TopicStreamType_name, int32(x))
}
func (TopicStreamType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{0}
}

type WeightScene int32

const (
	WeightScene_Invalid                      WeightScene = 0
	WeightScene_TtUgcTopic                   WeightScene = 1
	WeightScene_TtUgcPost                    WeightScene = 2
	WeightScene_TtUgcTopicStreamNew          WeightScene = 3
	WeightScene_TtUgcTopicStreamHot          WeightScene = 4
	WeightScene_TtUgcTopicStreamNewWithTopic WeightScene = 5
	WeightScene_TtUgcTopicStreamHotWithTopic WeightScene = 6
	WeightScene_TtUgcMood                    WeightScene = 7
)

var WeightScene_name = map[int32]string{
	0: "Invalid",
	1: "TtUgcTopic",
	2: "TtUgcPost",
	3: "TtUgcTopicStreamNew",
	4: "TtUgcTopicStreamHot",
	5: "TtUgcTopicStreamNewWithTopic",
	6: "TtUgcTopicStreamHotWithTopic",
	7: "TtUgcMood",
}
var WeightScene_value = map[string]int32{
	"Invalid":                      0,
	"TtUgcTopic":                   1,
	"TtUgcPost":                    2,
	"TtUgcTopicStreamNew":          3,
	"TtUgcTopicStreamHot":          4,
	"TtUgcTopicStreamNewWithTopic": 5,
	"TtUgcTopicStreamHotWithTopic": 6,
	"TtUgcMood":                    7,
}

func (x WeightScene) String() string {
	return proto.EnumName(WeightScene_name, int32(x))
}
func (WeightScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{1}
}

type WeightStatus int32

const (
	WeightStatus_DefaultWeightStatus WeightStatus = 0
	WeightStatus_Valid               WeightStatus = 1
	WeightStatus_Expired             WeightStatus = 2
	WeightStatus_Future              WeightStatus = 3
)

var WeightStatus_name = map[int32]string{
	0: "DefaultWeightStatus",
	1: "Valid",
	2: "Expired",
	3: "Future",
}
var WeightStatus_value = map[string]int32{
	"DefaultWeightStatus": 0,
	"Valid":               1,
	"Expired":             2,
	"Future":              3,
}

func (x WeightStatus) String() string {
	return proto.EnumName(WeightStatus_name, int32(x))
}
func (WeightStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{2}
}

type BlackWhiteListUsage int32

const (
	BlackWhiteListUsage_UnknownBlackWhiteListUsage BlackWhiteListUsage = 0
	BlackWhiteListUsage_PostStreamPostWhiteList    BlackWhiteListUsage = 1
	BlackWhiteListUsage_PostStreamUserWhiteList    BlackWhiteListUsage = 2
)

var BlackWhiteListUsage_name = map[int32]string{
	0: "UnknownBlackWhiteListUsage",
	1: "PostStreamPostWhiteList",
	2: "PostStreamUserWhiteList",
}
var BlackWhiteListUsage_value = map[string]int32{
	"UnknownBlackWhiteListUsage": 0,
	"PostStreamPostWhiteList":    1,
	"PostStreamUserWhiteList":    2,
}

func (x BlackWhiteListUsage) String() string {
	return proto.EnumName(BlackWhiteListUsage_name, int32(x))
}
func (BlackWhiteListUsage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{3}
}

type ModifyBlackWhiteOperation int32

const (
	ModifyBlackWhiteOperation_UnknownSetBlackWhiteOperation ModifyBlackWhiteOperation = 0
	ModifyBlackWhiteOperation_AddBlackWhiteOperation        ModifyBlackWhiteOperation = 1
	ModifyBlackWhiteOperation_DelBlackWhiteOperation        ModifyBlackWhiteOperation = 2
)

var ModifyBlackWhiteOperation_name = map[int32]string{
	0: "UnknownSetBlackWhiteOperation",
	1: "AddBlackWhiteOperation",
	2: "DelBlackWhiteOperation",
}
var ModifyBlackWhiteOperation_value = map[string]int32{
	"UnknownSetBlackWhiteOperation": 0,
	"AddBlackWhiteOperation":        1,
	"DelBlackWhiteOperation":        2,
}

func (x ModifyBlackWhiteOperation) String() string {
	return proto.EnumName(ModifyBlackWhiteOperation_name, int32(x))
}
func (ModifyBlackWhiteOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{4}
}

type ModifyVCOperation int32

const (
	ModifyVCOperation_Unknown ModifyVCOperation = 0
	ModifyVCOperation_Add     ModifyVCOperation = 1
	ModifyVCOperation_Update  ModifyVCOperation = 2
	ModifyVCOperation_Delete  ModifyVCOperation = 3
)

var ModifyVCOperation_name = map[int32]string{
	0: "Unknown",
	1: "Add",
	2: "Update",
	3: "Delete",
}
var ModifyVCOperation_value = map[string]int32{
	"Unknown": 0,
	"Add":     1,
	"Update":  2,
	"Delete":  3,
}

func (x ModifyVCOperation) String() string {
	return proto.EnumName(ModifyVCOperation_name, int32(x))
}
func (ModifyVCOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{5}
}

type BatchGetWeightInfoWithStatusReq struct {
	Scene                WeightScene  `protobuf:"varint,1,opt,name=scene,proto3,enum=rcmd.operating_platform.WeightScene" json:"scene,omitempty"`
	IdList               []string     `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	PostItemList         []*PostItem  `protobuf:"bytes,3,rep,name=post_item_list,json=postItemList,proto3" json:"post_item_list,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,4,opt,name=weight_status,json=weightStatus,proto3,enum=rcmd.operating_platform.WeightStatus" json:"weight_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *BatchGetWeightInfoWithStatusReq) Reset()         { *m = BatchGetWeightInfoWithStatusReq{} }
func (m *BatchGetWeightInfoWithStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeightInfoWithStatusReq) ProtoMessage()    {}
func (*BatchGetWeightInfoWithStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{0}
}
func (m *BatchGetWeightInfoWithStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeightInfoWithStatusReq.Unmarshal(m, b)
}
func (m *BatchGetWeightInfoWithStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeightInfoWithStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeightInfoWithStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeightInfoWithStatusReq.Merge(dst, src)
}
func (m *BatchGetWeightInfoWithStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeightInfoWithStatusReq.Size(m)
}
func (m *BatchGetWeightInfoWithStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeightInfoWithStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeightInfoWithStatusReq proto.InternalMessageInfo

func (m *BatchGetWeightInfoWithStatusReq) GetScene() WeightScene {
	if m != nil {
		return m.Scene
	}
	return WeightScene_Invalid
}

func (m *BatchGetWeightInfoWithStatusReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatchGetWeightInfoWithStatusReq) GetPostItemList() []*PostItem {
	if m != nil {
		return m.PostItemList
	}
	return nil
}

func (m *BatchGetWeightInfoWithStatusReq) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_DefaultWeightStatus
}

type BatchGetWeightInfoWithStatusRsp struct {
	ErrCode              uint32        `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string        `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	WeightInfoList       []*WeightInfo `protobuf:"bytes,3,rep,name=weight_info_list,json=weightInfoList,proto3" json:"weight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetWeightInfoWithStatusRsp) Reset()         { *m = BatchGetWeightInfoWithStatusRsp{} }
func (m *BatchGetWeightInfoWithStatusRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeightInfoWithStatusRsp) ProtoMessage()    {}
func (*BatchGetWeightInfoWithStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{1}
}
func (m *BatchGetWeightInfoWithStatusRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeightInfoWithStatusRsp.Unmarshal(m, b)
}
func (m *BatchGetWeightInfoWithStatusRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeightInfoWithStatusRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeightInfoWithStatusRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeightInfoWithStatusRsp.Merge(dst, src)
}
func (m *BatchGetWeightInfoWithStatusRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeightInfoWithStatusRsp.Size(m)
}
func (m *BatchGetWeightInfoWithStatusRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeightInfoWithStatusRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeightInfoWithStatusRsp proto.InternalMessageInfo

func (m *BatchGetWeightInfoWithStatusRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BatchGetWeightInfoWithStatusRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *BatchGetWeightInfoWithStatusRsp) GetWeightInfoList() []*WeightInfo {
	if m != nil {
		return m.WeightInfoList
	}
	return nil
}

type GetAllWeightInfoWithStatusReq struct {
	Scene                WeightScene  `protobuf:"varint,1,opt,name=scene,proto3,enum=rcmd.operating_platform.WeightScene" json:"scene,omitempty"`
	Offset               uint32       `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32       `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	TopicId              string       `protobuf:"bytes,4,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,5,opt,name=weight_status,json=weightStatus,proto3,enum=rcmd.operating_platform.WeightStatus" json:"weight_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetAllWeightInfoWithStatusReq) Reset()         { *m = GetAllWeightInfoWithStatusReq{} }
func (m *GetAllWeightInfoWithStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetAllWeightInfoWithStatusReq) ProtoMessage()    {}
func (*GetAllWeightInfoWithStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{2}
}
func (m *GetAllWeightInfoWithStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeightInfoWithStatusReq.Unmarshal(m, b)
}
func (m *GetAllWeightInfoWithStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeightInfoWithStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetAllWeightInfoWithStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeightInfoWithStatusReq.Merge(dst, src)
}
func (m *GetAllWeightInfoWithStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetAllWeightInfoWithStatusReq.Size(m)
}
func (m *GetAllWeightInfoWithStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeightInfoWithStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeightInfoWithStatusReq proto.InternalMessageInfo

func (m *GetAllWeightInfoWithStatusReq) GetScene() WeightScene {
	if m != nil {
		return m.Scene
	}
	return WeightScene_Invalid
}

func (m *GetAllWeightInfoWithStatusReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllWeightInfoWithStatusReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAllWeightInfoWithStatusReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *GetAllWeightInfoWithStatusReq) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_DefaultWeightStatus
}

type GetAllWeightInfoWithStatusRsp struct {
	ErrCode              uint32              `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string              `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	TotalCount           uint32              `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	WeightInfoList       []*StreamWeightInfo `protobuf:"bytes,4,rep,name=weight_info_list,json=weightInfoList,proto3" json:"weight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetAllWeightInfoWithStatusRsp) Reset()         { *m = GetAllWeightInfoWithStatusRsp{} }
func (m *GetAllWeightInfoWithStatusRsp) String() string { return proto.CompactTextString(m) }
func (*GetAllWeightInfoWithStatusRsp) ProtoMessage()    {}
func (*GetAllWeightInfoWithStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{3}
}
func (m *GetAllWeightInfoWithStatusRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeightInfoWithStatusRsp.Unmarshal(m, b)
}
func (m *GetAllWeightInfoWithStatusRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeightInfoWithStatusRsp.Marshal(b, m, deterministic)
}
func (dst *GetAllWeightInfoWithStatusRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeightInfoWithStatusRsp.Merge(dst, src)
}
func (m *GetAllWeightInfoWithStatusRsp) XXX_Size() int {
	return xxx_messageInfo_GetAllWeightInfoWithStatusRsp.Size(m)
}
func (m *GetAllWeightInfoWithStatusRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeightInfoWithStatusRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeightInfoWithStatusRsp proto.InternalMessageInfo

func (m *GetAllWeightInfoWithStatusRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetAllWeightInfoWithStatusRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetAllWeightInfoWithStatusRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetAllWeightInfoWithStatusRsp) GetWeightInfoList() []*StreamWeightInfo {
	if m != nil {
		return m.WeightInfoList
	}
	return nil
}

type StreamWeightInfo struct {
	TopicId              string       `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostId               string       `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Valid                bool         `protobuf:"varint,3,opt,name=valid,proto3" json:"valid,omitempty"`
	Weight               float64      `protobuf:"fixed64,4,opt,name=weight,proto3" json:"weight,omitempty"`
	StartTs              uint32       `protobuf:"varint,5,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32       `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,7,opt,name=weight_status,json=weightStatus,proto3,enum=rcmd.operating_platform.WeightStatus" json:"weight_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *StreamWeightInfo) Reset()         { *m = StreamWeightInfo{} }
func (m *StreamWeightInfo) String() string { return proto.CompactTextString(m) }
func (*StreamWeightInfo) ProtoMessage()    {}
func (*StreamWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{4}
}
func (m *StreamWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StreamWeightInfo.Unmarshal(m, b)
}
func (m *StreamWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StreamWeightInfo.Marshal(b, m, deterministic)
}
func (dst *StreamWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StreamWeightInfo.Merge(dst, src)
}
func (m *StreamWeightInfo) XXX_Size() int {
	return xxx_messageInfo_StreamWeightInfo.Size(m)
}
func (m *StreamWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_StreamWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_StreamWeightInfo proto.InternalMessageInfo

func (m *StreamWeightInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *StreamWeightInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *StreamWeightInfo) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

func (m *StreamWeightInfo) GetWeight() float64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *StreamWeightInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *StreamWeightInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *StreamWeightInfo) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_DefaultWeightStatus
}

type BatchGetTopicStreamByPostIdReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	PostIdList           []string `protobuf:"bytes,3,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetTopicStreamByPostIdReq) Reset()         { *m = BatchGetTopicStreamByPostIdReq{} }
func (m *BatchGetTopicStreamByPostIdReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicStreamByPostIdReq) ProtoMessage()    {}
func (*BatchGetTopicStreamByPostIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{5}
}
func (m *BatchGetTopicStreamByPostIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdReq.Unmarshal(m, b)
}
func (m *BatchGetTopicStreamByPostIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicStreamByPostIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicStreamByPostIdReq.Merge(dst, src)
}
func (m *BatchGetTopicStreamByPostIdReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdReq.Size(m)
}
func (m *BatchGetTopicStreamByPostIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicStreamByPostIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicStreamByPostIdReq proto.InternalMessageInfo

func (m *BatchGetTopicStreamByPostIdReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *BatchGetTopicStreamByPostIdReq) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *BatchGetTopicStreamByPostIdReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type BatchGetTopicStreamByPostIdRsp struct {
	PostList             []*BatchGetTopicStreamByPostIdRsp_TopicStreamPost `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalCount           uint32                                            `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-"`
	XXX_unrecognized     []byte                                            `json:"-"`
	XXX_sizecache        int32                                             `json:"-"`
}

func (m *BatchGetTopicStreamByPostIdRsp) Reset()         { *m = BatchGetTopicStreamByPostIdRsp{} }
func (m *BatchGetTopicStreamByPostIdRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicStreamByPostIdRsp) ProtoMessage()    {}
func (*BatchGetTopicStreamByPostIdRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{6}
}
func (m *BatchGetTopicStreamByPostIdRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdRsp.Unmarshal(m, b)
}
func (m *BatchGetTopicStreamByPostIdRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicStreamByPostIdRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicStreamByPostIdRsp.Merge(dst, src)
}
func (m *BatchGetTopicStreamByPostIdRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdRsp.Size(m)
}
func (m *BatchGetTopicStreamByPostIdRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicStreamByPostIdRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicStreamByPostIdRsp proto.InternalMessageInfo

func (m *BatchGetTopicStreamByPostIdRsp) GetPostList() []*BatchGetTopicStreamByPostIdRsp_TopicStreamPost {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *BatchGetTopicStreamByPostIdRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type BatchGetTopicStreamByPostIdRsp_TopicStreamPost struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	OriginScore          float32  `protobuf:"fixed32,2,opt,name=origin_score,json=originScore,proto3" json:"origin_score,omitempty"`
	WeightScore          float32  `protobuf:"fixed32,3,opt,name=weight_score,json=weightScore,proto3" json:"weight_score,omitempty"`
	WeightScorePosition  float32  `protobuf:"fixed32,4,opt,name=weight_score_position,json=weightScorePosition,proto3" json:"weight_score_position,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) Reset() {
	*m = BatchGetTopicStreamByPostIdRsp_TopicStreamPost{}
}
func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetTopicStreamByPostIdRsp_TopicStreamPost) ProtoMessage() {}
func (*BatchGetTopicStreamByPostIdRsp_TopicStreamPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{6, 0}
}
func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdRsp_TopicStreamPost.Unmarshal(m, b)
}
func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdRsp_TopicStreamPost.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicStreamByPostIdRsp_TopicStreamPost.Merge(dst, src)
}
func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicStreamByPostIdRsp_TopicStreamPost.Size(m)
}
func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicStreamByPostIdRsp_TopicStreamPost.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicStreamByPostIdRsp_TopicStreamPost proto.InternalMessageInfo

func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) GetOriginScore() float32 {
	if m != nil {
		return m.OriginScore
	}
	return 0
}

func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) GetWeightScore() float32 {
	if m != nil {
		return m.WeightScore
	}
	return 0
}

func (m *BatchGetTopicStreamByPostIdRsp_TopicStreamPost) GetWeightScorePosition() float32 {
	if m != nil {
		return m.WeightScorePosition
	}
	return 0
}

type GetTopicStreamListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Topic                string   `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	PostIdList           []string `protobuf:"bytes,5,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	SortByAlgo           bool     `protobuf:"varint,6,opt,name=sort_by_algo,json=sortByAlgo,proto3" json:"sort_by_algo,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicStreamListReq) Reset()         { *m = GetTopicStreamListReq{} }
func (m *GetTopicStreamListReq) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamListReq) ProtoMessage()    {}
func (*GetTopicStreamListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{7}
}
func (m *GetTopicStreamListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamListReq.Unmarshal(m, b)
}
func (m *GetTopicStreamListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamListReq.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamListReq.Merge(dst, src)
}
func (m *GetTopicStreamListReq) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamListReq.Size(m)
}
func (m *GetTopicStreamListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamListReq proto.InternalMessageInfo

func (m *GetTopicStreamListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetTopicStreamListReq) GetTopic() string {
	if m != nil {
		return m.Topic
	}
	return ""
}

func (m *GetTopicStreamListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetTopicStreamListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetTopicStreamListReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *GetTopicStreamListReq) GetSortByAlgo() bool {
	if m != nil {
		return m.SortByAlgo
	}
	return false
}

type GetTopicStreamListRsp struct {
	PostList             []*GetTopicStreamListRsp_TopicStreamPost `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalCount           uint32                                   `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-"`
	XXX_unrecognized     []byte                                   `json:"-"`
	XXX_sizecache        int32                                    `json:"-"`
}

func (m *GetTopicStreamListRsp) Reset()         { *m = GetTopicStreamListRsp{} }
func (m *GetTopicStreamListRsp) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamListRsp) ProtoMessage()    {}
func (*GetTopicStreamListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{8}
}
func (m *GetTopicStreamListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamListRsp.Unmarshal(m, b)
}
func (m *GetTopicStreamListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamListRsp.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamListRsp.Merge(dst, src)
}
func (m *GetTopicStreamListRsp) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamListRsp.Size(m)
}
func (m *GetTopicStreamListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamListRsp proto.InternalMessageInfo

func (m *GetTopicStreamListRsp) GetPostList() []*GetTopicStreamListRsp_TopicStreamPost {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *GetTopicStreamListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetTopicStreamListRsp_TopicStreamPost struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	OriginScore          float32  `protobuf:"fixed32,2,opt,name=origin_score,json=originScore,proto3" json:"origin_score,omitempty"`
	WeightScore          float32  `protobuf:"fixed32,3,opt,name=weight_score,json=weightScore,proto3" json:"weight_score,omitempty"`
	WeightScorePosition  float32  `protobuf:"fixed32,4,opt,name=weight_score_position,json=weightScorePosition,proto3" json:"weight_score_position,omitempty"`
	ScoreFactor          float32  `protobuf:"fixed32,5,opt,name=score_factor,json=scoreFactor,proto3" json:"score_factor,omitempty"`
	TopicAlgoScore       float32  `protobuf:"fixed32,6,opt,name=topic_algo_score,json=topicAlgoScore,proto3" json:"topic_algo_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTopicStreamListRsp_TopicStreamPost) Reset()         { *m = GetTopicStreamListRsp_TopicStreamPost{} }
func (m *GetTopicStreamListRsp_TopicStreamPost) String() string { return proto.CompactTextString(m) }
func (*GetTopicStreamListRsp_TopicStreamPost) ProtoMessage()    {}
func (*GetTopicStreamListRsp_TopicStreamPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{8, 0}
}
func (m *GetTopicStreamListRsp_TopicStreamPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTopicStreamListRsp_TopicStreamPost.Unmarshal(m, b)
}
func (m *GetTopicStreamListRsp_TopicStreamPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTopicStreamListRsp_TopicStreamPost.Marshal(b, m, deterministic)
}
func (dst *GetTopicStreamListRsp_TopicStreamPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTopicStreamListRsp_TopicStreamPost.Merge(dst, src)
}
func (m *GetTopicStreamListRsp_TopicStreamPost) XXX_Size() int {
	return xxx_messageInfo_GetTopicStreamListRsp_TopicStreamPost.Size(m)
}
func (m *GetTopicStreamListRsp_TopicStreamPost) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTopicStreamListRsp_TopicStreamPost.DiscardUnknown(m)
}

var xxx_messageInfo_GetTopicStreamListRsp_TopicStreamPost proto.InternalMessageInfo

func (m *GetTopicStreamListRsp_TopicStreamPost) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetTopicStreamListRsp_TopicStreamPost) GetOriginScore() float32 {
	if m != nil {
		return m.OriginScore
	}
	return 0
}

func (m *GetTopicStreamListRsp_TopicStreamPost) GetWeightScore() float32 {
	if m != nil {
		return m.WeightScore
	}
	return 0
}

func (m *GetTopicStreamListRsp_TopicStreamPost) GetWeightScorePosition() float32 {
	if m != nil {
		return m.WeightScorePosition
	}
	return 0
}

func (m *GetTopicStreamListRsp_TopicStreamPost) GetScoreFactor() float32 {
	if m != nil {
		return m.ScoreFactor
	}
	return 0
}

func (m *GetTopicStreamListRsp_TopicStreamPost) GetTopicAlgoScore() float32 {
	if m != nil {
		return m.TopicAlgoScore
	}
	return 0
}

// 两种查询模式:
// 1. 根据post_id，查询这些postid是否在推荐池和话题流推荐池中, 如果在，则其推荐分和话题流推荐分及占比
// 2. 批量获取当前推荐池, 及其得分情况
type GetHighInteractPostListReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetHighInteractPostListReq) Reset()         { *m = GetHighInteractPostListReq{} }
func (m *GetHighInteractPostListReq) String() string { return proto.CompactTextString(m) }
func (*GetHighInteractPostListReq) ProtoMessage()    {}
func (*GetHighInteractPostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{9}
}
func (m *GetHighInteractPostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHighInteractPostListReq.Unmarshal(m, b)
}
func (m *GetHighInteractPostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHighInteractPostListReq.Marshal(b, m, deterministic)
}
func (dst *GetHighInteractPostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHighInteractPostListReq.Merge(dst, src)
}
func (m *GetHighInteractPostListReq) XXX_Size() int {
	return xxx_messageInfo_GetHighInteractPostListReq.Size(m)
}
func (m *GetHighInteractPostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHighInteractPostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetHighInteractPostListReq proto.InternalMessageInfo

func (m *GetHighInteractPostListReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *GetHighInteractPostListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetHighInteractPostListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetHighInteractPostListRsp struct {
	PostList             []*GetHighInteractPostListRsp_HighInteractPost `protobuf:"bytes,1,rep,name=post_list,json=postList,proto3" json:"post_list,omitempty"`
	TotalCount           uint32                                         `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                       `json:"-"`
	XXX_unrecognized     []byte                                         `json:"-"`
	XXX_sizecache        int32                                          `json:"-"`
}

func (m *GetHighInteractPostListRsp) Reset()         { *m = GetHighInteractPostListRsp{} }
func (m *GetHighInteractPostListRsp) String() string { return proto.CompactTextString(m) }
func (*GetHighInteractPostListRsp) ProtoMessage()    {}
func (*GetHighInteractPostListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{10}
}
func (m *GetHighInteractPostListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHighInteractPostListRsp.Unmarshal(m, b)
}
func (m *GetHighInteractPostListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHighInteractPostListRsp.Marshal(b, m, deterministic)
}
func (dst *GetHighInteractPostListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHighInteractPostListRsp.Merge(dst, src)
}
func (m *GetHighInteractPostListRsp) XXX_Size() int {
	return xxx_messageInfo_GetHighInteractPostListRsp.Size(m)
}
func (m *GetHighInteractPostListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHighInteractPostListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetHighInteractPostListRsp proto.InternalMessageInfo

func (m *GetHighInteractPostListRsp) GetPostList() []*GetHighInteractPostListRsp_HighInteractPost {
	if m != nil {
		return m.PostList
	}
	return nil
}

func (m *GetHighInteractPostListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type GetHighInteractPostListRsp_HighInteractPost struct {
	PostId                string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	StdScore              float32  `protobuf:"fixed32,2,opt,name=std_score,json=stdScore,proto3" json:"std_score,omitempty"`
	StdScorePosition      float32  `protobuf:"fixed32,3,opt,name=std_score_position,json=stdScorePosition,proto3" json:"std_score_position,omitempty"`
	InteractScore         float32  `protobuf:"fixed32,4,opt,name=interact_score,json=interactScore,proto3" json:"interact_score,omitempty"`
	InteractScorePosition float32  `protobuf:"fixed32,5,opt,name=interact_score_position,json=interactScorePosition,proto3" json:"interact_score_position,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *GetHighInteractPostListRsp_HighInteractPost) Reset() {
	*m = GetHighInteractPostListRsp_HighInteractPost{}
}
func (m *GetHighInteractPostListRsp_HighInteractPost) String() string {
	return proto.CompactTextString(m)
}
func (*GetHighInteractPostListRsp_HighInteractPost) ProtoMessage() {}
func (*GetHighInteractPostListRsp_HighInteractPost) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{10, 0}
}
func (m *GetHighInteractPostListRsp_HighInteractPost) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHighInteractPostListRsp_HighInteractPost.Unmarshal(m, b)
}
func (m *GetHighInteractPostListRsp_HighInteractPost) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHighInteractPostListRsp_HighInteractPost.Marshal(b, m, deterministic)
}
func (dst *GetHighInteractPostListRsp_HighInteractPost) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHighInteractPostListRsp_HighInteractPost.Merge(dst, src)
}
func (m *GetHighInteractPostListRsp_HighInteractPost) XXX_Size() int {
	return xxx_messageInfo_GetHighInteractPostListRsp_HighInteractPost.Size(m)
}
func (m *GetHighInteractPostListRsp_HighInteractPost) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHighInteractPostListRsp_HighInteractPost.DiscardUnknown(m)
}

var xxx_messageInfo_GetHighInteractPostListRsp_HighInteractPost proto.InternalMessageInfo

func (m *GetHighInteractPostListRsp_HighInteractPost) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *GetHighInteractPostListRsp_HighInteractPost) GetStdScore() float32 {
	if m != nil {
		return m.StdScore
	}
	return 0
}

func (m *GetHighInteractPostListRsp_HighInteractPost) GetStdScorePosition() float32 {
	if m != nil {
		return m.StdScorePosition
	}
	return 0
}

func (m *GetHighInteractPostListRsp_HighInteractPost) GetInteractScore() float32 {
	if m != nil {
		return m.InteractScore
	}
	return 0
}

func (m *GetHighInteractPostListRsp_HighInteractPost) GetInteractScorePosition() float32 {
	if m != nil {
		return m.InteractScorePosition
	}
	return 0
}

// 雷达白名单管理
type GetRadarWhiteListReq struct {
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRadarWhiteListReq) Reset()         { *m = GetRadarWhiteListReq{} }
func (m *GetRadarWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetRadarWhiteListReq) ProtoMessage()    {}
func (*GetRadarWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{11}
}
func (m *GetRadarWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarWhiteListReq.Unmarshal(m, b)
}
func (m *GetRadarWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetRadarWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarWhiteListReq.Merge(dst, src)
}
func (m *GetRadarWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetRadarWhiteListReq.Size(m)
}
func (m *GetRadarWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarWhiteListReq proto.InternalMessageInfo

func (m *GetRadarWhiteListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRadarWhiteListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRadarWhiteListRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	Uids                 []uint32 `protobuf:"varint,3,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRadarWhiteListRsp) Reset()         { *m = GetRadarWhiteListRsp{} }
func (m *GetRadarWhiteListRsp) String() string { return proto.CompactTextString(m) }
func (*GetRadarWhiteListRsp) ProtoMessage()    {}
func (*GetRadarWhiteListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{12}
}
func (m *GetRadarWhiteListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRadarWhiteListRsp.Unmarshal(m, b)
}
func (m *GetRadarWhiteListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRadarWhiteListRsp.Marshal(b, m, deterministic)
}
func (dst *GetRadarWhiteListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRadarWhiteListRsp.Merge(dst, src)
}
func (m *GetRadarWhiteListRsp) XXX_Size() int {
	return xxx_messageInfo_GetRadarWhiteListRsp.Size(m)
}
func (m *GetRadarWhiteListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRadarWhiteListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRadarWhiteListRsp proto.InternalMessageInfo

func (m *GetRadarWhiteListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetRadarWhiteListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetRadarWhiteListRsp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type AddRadarWhiteReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRadarWhiteReq) Reset()         { *m = AddRadarWhiteReq{} }
func (m *AddRadarWhiteReq) String() string { return proto.CompactTextString(m) }
func (*AddRadarWhiteReq) ProtoMessage()    {}
func (*AddRadarWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{13}
}
func (m *AddRadarWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRadarWhiteReq.Unmarshal(m, b)
}
func (m *AddRadarWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRadarWhiteReq.Marshal(b, m, deterministic)
}
func (dst *AddRadarWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRadarWhiteReq.Merge(dst, src)
}
func (m *AddRadarWhiteReq) XXX_Size() int {
	return xxx_messageInfo_AddRadarWhiteReq.Size(m)
}
func (m *AddRadarWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRadarWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRadarWhiteReq proto.InternalMessageInfo

func (m *AddRadarWhiteReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AddRadarWhiteReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type AddRadarWhiteRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRadarWhiteRsp) Reset()         { *m = AddRadarWhiteRsp{} }
func (m *AddRadarWhiteRsp) String() string { return proto.CompactTextString(m) }
func (*AddRadarWhiteRsp) ProtoMessage()    {}
func (*AddRadarWhiteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{14}
}
func (m *AddRadarWhiteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRadarWhiteRsp.Unmarshal(m, b)
}
func (m *AddRadarWhiteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRadarWhiteRsp.Marshal(b, m, deterministic)
}
func (dst *AddRadarWhiteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRadarWhiteRsp.Merge(dst, src)
}
func (m *AddRadarWhiteRsp) XXX_Size() int {
	return xxx_messageInfo_AddRadarWhiteRsp.Size(m)
}
func (m *AddRadarWhiteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRadarWhiteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRadarWhiteRsp proto.InternalMessageInfo

func (m *AddRadarWhiteRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *AddRadarWhiteRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type RemoveRadarWhiteReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveRadarWhiteReq) Reset()         { *m = RemoveRadarWhiteReq{} }
func (m *RemoveRadarWhiteReq) String() string { return proto.CompactTextString(m) }
func (*RemoveRadarWhiteReq) ProtoMessage()    {}
func (*RemoveRadarWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{15}
}
func (m *RemoveRadarWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRadarWhiteReq.Unmarshal(m, b)
}
func (m *RemoveRadarWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRadarWhiteReq.Marshal(b, m, deterministic)
}
func (dst *RemoveRadarWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRadarWhiteReq.Merge(dst, src)
}
func (m *RemoveRadarWhiteReq) XXX_Size() int {
	return xxx_messageInfo_RemoveRadarWhiteReq.Size(m)
}
func (m *RemoveRadarWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRadarWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRadarWhiteReq proto.InternalMessageInfo

func (m *RemoveRadarWhiteReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveRadarWhiteReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type RemoveRadarWhiteRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveRadarWhiteRsp) Reset()         { *m = RemoveRadarWhiteRsp{} }
func (m *RemoveRadarWhiteRsp) String() string { return proto.CompactTextString(m) }
func (*RemoveRadarWhiteRsp) ProtoMessage()    {}
func (*RemoveRadarWhiteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{16}
}
func (m *RemoveRadarWhiteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRadarWhiteRsp.Unmarshal(m, b)
}
func (m *RemoveRadarWhiteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRadarWhiteRsp.Marshal(b, m, deterministic)
}
func (dst *RemoveRadarWhiteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRadarWhiteRsp.Merge(dst, src)
}
func (m *RemoveRadarWhiteRsp) XXX_Size() int {
	return xxx_messageInfo_RemoveRadarWhiteRsp.Size(m)
}
func (m *RemoveRadarWhiteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRadarWhiteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRadarWhiteRsp proto.InternalMessageInfo

func (m *RemoveRadarWhiteRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *RemoveRadarWhiteRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// 敏感词管理
type GetRiskyWordListReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRiskyWordListReq) Reset()         { *m = GetRiskyWordListReq{} }
func (m *GetRiskyWordListReq) String() string { return proto.CompactTextString(m) }
func (*GetRiskyWordListReq) ProtoMessage()    {}
func (*GetRiskyWordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{17}
}
func (m *GetRiskyWordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRiskyWordListReq.Unmarshal(m, b)
}
func (m *GetRiskyWordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRiskyWordListReq.Marshal(b, m, deterministic)
}
func (dst *GetRiskyWordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRiskyWordListReq.Merge(dst, src)
}
func (m *GetRiskyWordListReq) XXX_Size() int {
	return xxx_messageInfo_GetRiskyWordListReq.Size(m)
}
func (m *GetRiskyWordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRiskyWordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRiskyWordListReq proto.InternalMessageInfo

func (m *GetRiskyWordListReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *GetRiskyWordListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetRiskyWordListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetRiskyWordListRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	WordList             []string `protobuf:"bytes,3,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRiskyWordListRsp) Reset()         { *m = GetRiskyWordListRsp{} }
func (m *GetRiskyWordListRsp) String() string { return proto.CompactTextString(m) }
func (*GetRiskyWordListRsp) ProtoMessage()    {}
func (*GetRiskyWordListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{18}
}
func (m *GetRiskyWordListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRiskyWordListRsp.Unmarshal(m, b)
}
func (m *GetRiskyWordListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRiskyWordListRsp.Marshal(b, m, deterministic)
}
func (dst *GetRiskyWordListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRiskyWordListRsp.Merge(dst, src)
}
func (m *GetRiskyWordListRsp) XXX_Size() int {
	return xxx_messageInfo_GetRiskyWordListRsp.Size(m)
}
func (m *GetRiskyWordListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRiskyWordListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRiskyWordListRsp proto.InternalMessageInfo

func (m *GetRiskyWordListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetRiskyWordListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetRiskyWordListRsp) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

type AddRiskyWordReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	WordList             []string `protobuf:"bytes,2,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRiskyWordReq) Reset()         { *m = AddRiskyWordReq{} }
func (m *AddRiskyWordReq) String() string { return proto.CompactTextString(m) }
func (*AddRiskyWordReq) ProtoMessage()    {}
func (*AddRiskyWordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{19}
}
func (m *AddRiskyWordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRiskyWordReq.Unmarshal(m, b)
}
func (m *AddRiskyWordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRiskyWordReq.Marshal(b, m, deterministic)
}
func (dst *AddRiskyWordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRiskyWordReq.Merge(dst, src)
}
func (m *AddRiskyWordReq) XXX_Size() int {
	return xxx_messageInfo_AddRiskyWordReq.Size(m)
}
func (m *AddRiskyWordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRiskyWordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRiskyWordReq proto.InternalMessageInfo

func (m *AddRiskyWordReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *AddRiskyWordReq) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

type AddRiskyWordRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRiskyWordRsp) Reset()         { *m = AddRiskyWordRsp{} }
func (m *AddRiskyWordRsp) String() string { return proto.CompactTextString(m) }
func (*AddRiskyWordRsp) ProtoMessage()    {}
func (*AddRiskyWordRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{20}
}
func (m *AddRiskyWordRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRiskyWordRsp.Unmarshal(m, b)
}
func (m *AddRiskyWordRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRiskyWordRsp.Marshal(b, m, deterministic)
}
func (dst *AddRiskyWordRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRiskyWordRsp.Merge(dst, src)
}
func (m *AddRiskyWordRsp) XXX_Size() int {
	return xxx_messageInfo_AddRiskyWordRsp.Size(m)
}
func (m *AddRiskyWordRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRiskyWordRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRiskyWordRsp proto.InternalMessageInfo

func (m *AddRiskyWordRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *AddRiskyWordRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type RemoveRiskyWordReq struct {
	AppId                uint32   `protobuf:"varint,1,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	WordList             []string `protobuf:"bytes,2,rep,name=word_list,json=wordList,proto3" json:"word_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveRiskyWordReq) Reset()         { *m = RemoveRiskyWordReq{} }
func (m *RemoveRiskyWordReq) String() string { return proto.CompactTextString(m) }
func (*RemoveRiskyWordReq) ProtoMessage()    {}
func (*RemoveRiskyWordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{21}
}
func (m *RemoveRiskyWordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRiskyWordReq.Unmarshal(m, b)
}
func (m *RemoveRiskyWordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRiskyWordReq.Marshal(b, m, deterministic)
}
func (dst *RemoveRiskyWordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRiskyWordReq.Merge(dst, src)
}
func (m *RemoveRiskyWordReq) XXX_Size() int {
	return xxx_messageInfo_RemoveRiskyWordReq.Size(m)
}
func (m *RemoveRiskyWordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRiskyWordReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRiskyWordReq proto.InternalMessageInfo

func (m *RemoveRiskyWordReq) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *RemoveRiskyWordReq) GetWordList() []string {
	if m != nil {
		return m.WordList
	}
	return nil
}

type RemoveRiskyWordRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveRiskyWordRsp) Reset()         { *m = RemoveRiskyWordRsp{} }
func (m *RemoveRiskyWordRsp) String() string { return proto.CompactTextString(m) }
func (*RemoveRiskyWordRsp) ProtoMessage()    {}
func (*RemoveRiskyWordRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{22}
}
func (m *RemoveRiskyWordRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveRiskyWordRsp.Unmarshal(m, b)
}
func (m *RemoveRiskyWordRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveRiskyWordRsp.Marshal(b, m, deterministic)
}
func (dst *RemoveRiskyWordRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveRiskyWordRsp.Merge(dst, src)
}
func (m *RemoveRiskyWordRsp) XXX_Size() int {
	return xxx_messageInfo_RemoveRiskyWordRsp.Size(m)
}
func (m *RemoveRiskyWordRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveRiskyWordRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveRiskyWordRsp proto.InternalMessageInfo

func (m *RemoveRiskyWordRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *RemoveRiskyWordRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// 房间索引信息查看
type GetAllChannelInIndexReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllChannelInIndexReq) Reset()         { *m = GetAllChannelInIndexReq{} }
func (m *GetAllChannelInIndexReq) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelInIndexReq) ProtoMessage()    {}
func (*GetAllChannelInIndexReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{23}
}
func (m *GetAllChannelInIndexReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelInIndexReq.Unmarshal(m, b)
}
func (m *GetAllChannelInIndexReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelInIndexReq.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelInIndexReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelInIndexReq.Merge(dst, src)
}
func (m *GetAllChannelInIndexReq) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelInIndexReq.Size(m)
}
func (m *GetAllChannelInIndexReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelInIndexReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelInIndexReq proto.InternalMessageInfo

type GetAllChannelInIndexRsp struct {
	ErrCode              uint32                  `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string                  `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	ChannelList          []*channel.ChannelBasic `protobuf:"bytes,3,rep,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *GetAllChannelInIndexRsp) Reset()         { *m = GetAllChannelInIndexRsp{} }
func (m *GetAllChannelInIndexRsp) String() string { return proto.CompactTextString(m) }
func (*GetAllChannelInIndexRsp) ProtoMessage()    {}
func (*GetAllChannelInIndexRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{24}
}
func (m *GetAllChannelInIndexRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllChannelInIndexRsp.Unmarshal(m, b)
}
func (m *GetAllChannelInIndexRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllChannelInIndexRsp.Marshal(b, m, deterministic)
}
func (dst *GetAllChannelInIndexRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllChannelInIndexRsp.Merge(dst, src)
}
func (m *GetAllChannelInIndexRsp) XXX_Size() int {
	return xxx_messageInfo_GetAllChannelInIndexRsp.Size(m)
}
func (m *GetAllChannelInIndexRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllChannelInIndexRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllChannelInIndexRsp proto.InternalMessageInfo

func (m *GetAllChannelInIndexRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetAllChannelInIndexRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetAllChannelInIndexRsp) GetChannelList() []*channel.ChannelBasic {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type WeightInfo struct {
	Id                   string       `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	HasWeight            bool         `protobuf:"varint,2,opt,name=has_weight,json=hasWeight,proto3" json:"has_weight,omitempty"`
	Weight               float64      `protobuf:"fixed64,3,opt,name=weight,proto3" json:"weight,omitempty"`
	StartTs              uint32       `protobuf:"varint,4,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32       `protobuf:"varint,5,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	TopicId              string       `protobuf:"bytes,6,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	WeightStatus         WeightStatus `protobuf:"varint,7,opt,name=weight_status,json=weightStatus,proto3,enum=rcmd.operating_platform.WeightStatus" json:"weight_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *WeightInfo) Reset()         { *m = WeightInfo{} }
func (m *WeightInfo) String() string { return proto.CompactTextString(m) }
func (*WeightInfo) ProtoMessage()    {}
func (*WeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{25}
}
func (m *WeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeightInfo.Unmarshal(m, b)
}
func (m *WeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeightInfo.Marshal(b, m, deterministic)
}
func (dst *WeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeightInfo.Merge(dst, src)
}
func (m *WeightInfo) XXX_Size() int {
	return xxx_messageInfo_WeightInfo.Size(m)
}
func (m *WeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeightInfo proto.InternalMessageInfo

func (m *WeightInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *WeightInfo) GetHasWeight() bool {
	if m != nil {
		return m.HasWeight
	}
	return false
}

func (m *WeightInfo) GetWeight() float64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *WeightInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *WeightInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *WeightInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *WeightInfo) GetWeightStatus() WeightStatus {
	if m != nil {
		return m.WeightStatus
	}
	return WeightStatus_DefaultWeightStatus
}

type PostItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	TopicIdList          []string `protobuf:"bytes,2,rep,name=topic_id_list,json=topicIdList,proto3" json:"topic_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PostItem) Reset()         { *m = PostItem{} }
func (m *PostItem) String() string { return proto.CompactTextString(m) }
func (*PostItem) ProtoMessage()    {}
func (*PostItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{26}
}
func (m *PostItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PostItem.Unmarshal(m, b)
}
func (m *PostItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PostItem.Marshal(b, m, deterministic)
}
func (dst *PostItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PostItem.Merge(dst, src)
}
func (m *PostItem) XXX_Size() int {
	return xxx_messageInfo_PostItem.Size(m)
}
func (m *PostItem) XXX_DiscardUnknown() {
	xxx_messageInfo_PostItem.DiscardUnknown(m)
}

var xxx_messageInfo_PostItem proto.InternalMessageInfo

func (m *PostItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *PostItem) GetTopicIdList() []string {
	if m != nil {
		return m.TopicIdList
	}
	return nil
}

type BatchSetWeightInfoReq struct {
	Scene                WeightScene `protobuf:"varint,1,opt,name=scene,proto3,enum=rcmd.operating_platform.WeightScene" json:"scene,omitempty"`
	IdList               []string    `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	WeightInfo           *WeightInfo `protobuf:"bytes,3,opt,name=weight_info,json=weightInfo,proto3" json:"weight_info,omitempty"`
	PostItemList         []*PostItem `protobuf:"bytes,4,rep,name=post_item_list,json=postItemList,proto3" json:"post_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchSetWeightInfoReq) Reset()         { *m = BatchSetWeightInfoReq{} }
func (m *BatchSetWeightInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetWeightInfoReq) ProtoMessage()    {}
func (*BatchSetWeightInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{27}
}
func (m *BatchSetWeightInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetWeightInfoReq.Unmarshal(m, b)
}
func (m *BatchSetWeightInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetWeightInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetWeightInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetWeightInfoReq.Merge(dst, src)
}
func (m *BatchSetWeightInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetWeightInfoReq.Size(m)
}
func (m *BatchSetWeightInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetWeightInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetWeightInfoReq proto.InternalMessageInfo

func (m *BatchSetWeightInfoReq) GetScene() WeightScene {
	if m != nil {
		return m.Scene
	}
	return WeightScene_Invalid
}

func (m *BatchSetWeightInfoReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatchSetWeightInfoReq) GetWeightInfo() *WeightInfo {
	if m != nil {
		return m.WeightInfo
	}
	return nil
}

func (m *BatchSetWeightInfoReq) GetPostItemList() []*PostItem {
	if m != nil {
		return m.PostItemList
	}
	return nil
}

type BatchSetWeightInfoRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetWeightInfoRsp) Reset()         { *m = BatchSetWeightInfoRsp{} }
func (m *BatchSetWeightInfoRsp) String() string { return proto.CompactTextString(m) }
func (*BatchSetWeightInfoRsp) ProtoMessage()    {}
func (*BatchSetWeightInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{28}
}
func (m *BatchSetWeightInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetWeightInfoRsp.Unmarshal(m, b)
}
func (m *BatchSetWeightInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetWeightInfoRsp.Marshal(b, m, deterministic)
}
func (dst *BatchSetWeightInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetWeightInfoRsp.Merge(dst, src)
}
func (m *BatchSetWeightInfoRsp) XXX_Size() int {
	return xxx_messageInfo_BatchSetWeightInfoRsp.Size(m)
}
func (m *BatchSetWeightInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetWeightInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetWeightInfoRsp proto.InternalMessageInfo

func (m *BatchSetWeightInfoRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BatchSetWeightInfoRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type BatchGetWeightInfoReq struct {
	Scene                WeightScene `protobuf:"varint,1,opt,name=scene,proto3,enum=rcmd.operating_platform.WeightScene" json:"scene,omitempty"`
	IdList               []string    `protobuf:"bytes,2,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	PostItemList         []*PostItem `protobuf:"bytes,3,rep,name=post_item_list,json=postItemList,proto3" json:"post_item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGetWeightInfoReq) Reset()         { *m = BatchGetWeightInfoReq{} }
func (m *BatchGetWeightInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeightInfoReq) ProtoMessage()    {}
func (*BatchGetWeightInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{29}
}
func (m *BatchGetWeightInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeightInfoReq.Unmarshal(m, b)
}
func (m *BatchGetWeightInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeightInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeightInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeightInfoReq.Merge(dst, src)
}
func (m *BatchGetWeightInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeightInfoReq.Size(m)
}
func (m *BatchGetWeightInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeightInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeightInfoReq proto.InternalMessageInfo

func (m *BatchGetWeightInfoReq) GetScene() WeightScene {
	if m != nil {
		return m.Scene
	}
	return WeightScene_Invalid
}

func (m *BatchGetWeightInfoReq) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

func (m *BatchGetWeightInfoReq) GetPostItemList() []*PostItem {
	if m != nil {
		return m.PostItemList
	}
	return nil
}

type BatchGetWeightInfoRsp struct {
	ErrCode              uint32        `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string        `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	WeightInfoList       []*WeightInfo `protobuf:"bytes,3,rep,name=weight_info_list,json=weightInfoList,proto3" json:"weight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *BatchGetWeightInfoRsp) Reset()         { *m = BatchGetWeightInfoRsp{} }
func (m *BatchGetWeightInfoRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeightInfoRsp) ProtoMessage()    {}
func (*BatchGetWeightInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{30}
}
func (m *BatchGetWeightInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeightInfoRsp.Unmarshal(m, b)
}
func (m *BatchGetWeightInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeightInfoRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeightInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeightInfoRsp.Merge(dst, src)
}
func (m *BatchGetWeightInfoRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeightInfoRsp.Size(m)
}
func (m *BatchGetWeightInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeightInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeightInfoRsp proto.InternalMessageInfo

func (m *BatchGetWeightInfoRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BatchGetWeightInfoRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *BatchGetWeightInfoRsp) GetWeightInfoList() []*WeightInfo {
	if m != nil {
		return m.WeightInfoList
	}
	return nil
}

type GetAllWeightInfoReq struct {
	Scene                WeightScene `protobuf:"varint,1,opt,name=scene,proto3,enum=rcmd.operating_platform.WeightScene" json:"scene,omitempty"`
	Offset               uint32      `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32      `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	TopicId              string      `protobuf:"bytes,4,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAllWeightInfoReq) Reset()         { *m = GetAllWeightInfoReq{} }
func (m *GetAllWeightInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetAllWeightInfoReq) ProtoMessage()    {}
func (*GetAllWeightInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{31}
}
func (m *GetAllWeightInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeightInfoReq.Unmarshal(m, b)
}
func (m *GetAllWeightInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeightInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetAllWeightInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeightInfoReq.Merge(dst, src)
}
func (m *GetAllWeightInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetAllWeightInfoReq.Size(m)
}
func (m *GetAllWeightInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeightInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeightInfoReq proto.InternalMessageInfo

func (m *GetAllWeightInfoReq) GetScene() WeightScene {
	if m != nil {
		return m.Scene
	}
	return WeightScene_Invalid
}

func (m *GetAllWeightInfoReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllWeightInfoReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetAllWeightInfoReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

type GetAllWeightInfoRsp struct {
	ErrCode              uint32        `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string        `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	WeightInfoList       []*WeightInfo `protobuf:"bytes,3,rep,name=weight_info_list,json=weightInfoList,proto3" json:"weight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetAllWeightInfoRsp) Reset()         { *m = GetAllWeightInfoRsp{} }
func (m *GetAllWeightInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetAllWeightInfoRsp) ProtoMessage()    {}
func (*GetAllWeightInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{32}
}
func (m *GetAllWeightInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllWeightInfoRsp.Unmarshal(m, b)
}
func (m *GetAllWeightInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllWeightInfoRsp.Marshal(b, m, deterministic)
}
func (dst *GetAllWeightInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllWeightInfoRsp.Merge(dst, src)
}
func (m *GetAllWeightInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetAllWeightInfoRsp.Size(m)
}
func (m *GetAllWeightInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllWeightInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllWeightInfoRsp proto.InternalMessageInfo

func (m *GetAllWeightInfoRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetAllWeightInfoRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetAllWeightInfoRsp) GetWeightInfoList() []*WeightInfo {
	if m != nil {
		return m.WeightInfoList
	}
	return nil
}

type TopicStreamWeightInfo struct {
	TopicId              string   `protobuf:"bytes,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostId               string   `protobuf:"bytes,2,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Valid                bool     `protobuf:"varint,3,opt,name=valid,proto3" json:"valid,omitempty"`
	Weight               float64  `protobuf:"fixed64,4,opt,name=weight,proto3" json:"weight,omitempty"`
	StartTs              uint32   `protobuf:"varint,5,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,6,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Show                 bool     `protobuf:"varint,7,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopicStreamWeightInfo) Reset()         { *m = TopicStreamWeightInfo{} }
func (m *TopicStreamWeightInfo) String() string { return proto.CompactTextString(m) }
func (*TopicStreamWeightInfo) ProtoMessage()    {}
func (*TopicStreamWeightInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{33}
}
func (m *TopicStreamWeightInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopicStreamWeightInfo.Unmarshal(m, b)
}
func (m *TopicStreamWeightInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopicStreamWeightInfo.Marshal(b, m, deterministic)
}
func (dst *TopicStreamWeightInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopicStreamWeightInfo.Merge(dst, src)
}
func (m *TopicStreamWeightInfo) XXX_Size() int {
	return xxx_messageInfo_TopicStreamWeightInfo.Size(m)
}
func (m *TopicStreamWeightInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TopicStreamWeightInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TopicStreamWeightInfo proto.InternalMessageInfo

func (m *TopicStreamWeightInfo) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *TopicStreamWeightInfo) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *TopicStreamWeightInfo) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

func (m *TopicStreamWeightInfo) GetWeight() float64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *TopicStreamWeightInfo) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *TopicStreamWeightInfo) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *TopicStreamWeightInfo) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

type GetAllTopicStreamWeightReq struct {
	PostType             TopicStreamType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=rcmd.operating_platform.TopicStreamType" json:"post_type,omitempty"`
	Offset               uint32          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAllTopicStreamWeightReq) Reset()         { *m = GetAllTopicStreamWeightReq{} }
func (m *GetAllTopicStreamWeightReq) String() string { return proto.CompactTextString(m) }
func (*GetAllTopicStreamWeightReq) ProtoMessage()    {}
func (*GetAllTopicStreamWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{34}
}
func (m *GetAllTopicStreamWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTopicStreamWeightReq.Unmarshal(m, b)
}
func (m *GetAllTopicStreamWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTopicStreamWeightReq.Marshal(b, m, deterministic)
}
func (dst *GetAllTopicStreamWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTopicStreamWeightReq.Merge(dst, src)
}
func (m *GetAllTopicStreamWeightReq) XXX_Size() int {
	return xxx_messageInfo_GetAllTopicStreamWeightReq.Size(m)
}
func (m *GetAllTopicStreamWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTopicStreamWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTopicStreamWeightReq proto.InternalMessageInfo

func (m *GetAllTopicStreamWeightReq) GetPostType() TopicStreamType {
	if m != nil {
		return m.PostType
	}
	return TopicStreamType_TopicStreamType_Invalid
}

func (m *GetAllTopicStreamWeightReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllTopicStreamWeightReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAllTopicStreamWeightRsp struct {
	ErrCode              uint32                   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string                   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	WeightInfoList       []*TopicStreamWeightInfo `protobuf:"bytes,3,rep,name=weight_info_list,json=weightInfoList,proto3" json:"weight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetAllTopicStreamWeightRsp) Reset()         { *m = GetAllTopicStreamWeightRsp{} }
func (m *GetAllTopicStreamWeightRsp) String() string { return proto.CompactTextString(m) }
func (*GetAllTopicStreamWeightRsp) ProtoMessage()    {}
func (*GetAllTopicStreamWeightRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{35}
}
func (m *GetAllTopicStreamWeightRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllTopicStreamWeightRsp.Unmarshal(m, b)
}
func (m *GetAllTopicStreamWeightRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllTopicStreamWeightRsp.Marshal(b, m, deterministic)
}
func (dst *GetAllTopicStreamWeightRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllTopicStreamWeightRsp.Merge(dst, src)
}
func (m *GetAllTopicStreamWeightRsp) XXX_Size() int {
	return xxx_messageInfo_GetAllTopicStreamWeightRsp.Size(m)
}
func (m *GetAllTopicStreamWeightRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllTopicStreamWeightRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllTopicStreamWeightRsp proto.InternalMessageInfo

func (m *GetAllTopicStreamWeightRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetAllTopicStreamWeightRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetAllTopicStreamWeightRsp) GetWeightInfoList() []*TopicStreamWeightInfo {
	if m != nil {
		return m.WeightInfoList
	}
	return nil
}

type BatchSetTopicStreamWeightReq struct {
	PostType             TopicStreamType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=rcmd.operating_platform.TopicStreamType" json:"post_type,omitempty"`
	TopicId              string          `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostIdList           []string        `protobuf:"bytes,3,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	Valid                bool            `protobuf:"varint,4,opt,name=valid,proto3" json:"valid,omitempty"`
	Weight               float64         `protobuf:"fixed64,5,opt,name=weight,proto3" json:"weight,omitempty"`
	StartTs              uint32          `protobuf:"varint,6,opt,name=start_ts,json=startTs,proto3" json:"start_ts,omitempty"`
	EndTs                uint32          `protobuf:"varint,7,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchSetTopicStreamWeightReq) Reset()         { *m = BatchSetTopicStreamWeightReq{} }
func (m *BatchSetTopicStreamWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetTopicStreamWeightReq) ProtoMessage()    {}
func (*BatchSetTopicStreamWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{36}
}
func (m *BatchSetTopicStreamWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetTopicStreamWeightReq.Unmarshal(m, b)
}
func (m *BatchSetTopicStreamWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetTopicStreamWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetTopicStreamWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetTopicStreamWeightReq.Merge(dst, src)
}
func (m *BatchSetTopicStreamWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetTopicStreamWeightReq.Size(m)
}
func (m *BatchSetTopicStreamWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetTopicStreamWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetTopicStreamWeightReq proto.InternalMessageInfo

func (m *BatchSetTopicStreamWeightReq) GetPostType() TopicStreamType {
	if m != nil {
		return m.PostType
	}
	return TopicStreamType_TopicStreamType_Invalid
}

func (m *BatchSetTopicStreamWeightReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BatchSetTopicStreamWeightReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *BatchSetTopicStreamWeightReq) GetValid() bool {
	if m != nil {
		return m.Valid
	}
	return false
}

func (m *BatchSetTopicStreamWeightReq) GetWeight() float64 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *BatchSetTopicStreamWeightReq) GetStartTs() uint32 {
	if m != nil {
		return m.StartTs
	}
	return 0
}

func (m *BatchSetTopicStreamWeightReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type BatchSetTopicStreamWeightRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetTopicStreamWeightRsp) Reset()         { *m = BatchSetTopicStreamWeightRsp{} }
func (m *BatchSetTopicStreamWeightRsp) String() string { return proto.CompactTextString(m) }
func (*BatchSetTopicStreamWeightRsp) ProtoMessage()    {}
func (*BatchSetTopicStreamWeightRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{37}
}
func (m *BatchSetTopicStreamWeightRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetTopicStreamWeightRsp.Unmarshal(m, b)
}
func (m *BatchSetTopicStreamWeightRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetTopicStreamWeightRsp.Marshal(b, m, deterministic)
}
func (dst *BatchSetTopicStreamWeightRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetTopicStreamWeightRsp.Merge(dst, src)
}
func (m *BatchSetTopicStreamWeightRsp) XXX_Size() int {
	return xxx_messageInfo_BatchSetTopicStreamWeightRsp.Size(m)
}
func (m *BatchSetTopicStreamWeightRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetTopicStreamWeightRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetTopicStreamWeightRsp proto.InternalMessageInfo

func (m *BatchSetTopicStreamWeightRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BatchSetTopicStreamWeightRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type BatchGetTopicStreamWeightReq struct {
	PostType             TopicStreamType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=rcmd.operating_platform.TopicStreamType" json:"post_type,omitempty"`
	TopicId              string          `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostIdList           []string        `protobuf:"bytes,3,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchGetTopicStreamWeightReq) Reset()         { *m = BatchGetTopicStreamWeightReq{} }
func (m *BatchGetTopicStreamWeightReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicStreamWeightReq) ProtoMessage()    {}
func (*BatchGetTopicStreamWeightReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{38}
}
func (m *BatchGetTopicStreamWeightReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicStreamWeightReq.Unmarshal(m, b)
}
func (m *BatchGetTopicStreamWeightReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicStreamWeightReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicStreamWeightReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicStreamWeightReq.Merge(dst, src)
}
func (m *BatchGetTopicStreamWeightReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicStreamWeightReq.Size(m)
}
func (m *BatchGetTopicStreamWeightReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicStreamWeightReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicStreamWeightReq proto.InternalMessageInfo

func (m *BatchGetTopicStreamWeightReq) GetPostType() TopicStreamType {
	if m != nil {
		return m.PostType
	}
	return TopicStreamType_TopicStreamType_Invalid
}

func (m *BatchGetTopicStreamWeightReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BatchGetTopicStreamWeightReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

type BatchGetTopicStreamWeightRsp struct {
	ErrCode              uint32                   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string                   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	WeightInfoList       []*TopicStreamWeightInfo `protobuf:"bytes,3,rep,name=weight_info_list,json=weightInfoList,proto3" json:"weight_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *BatchGetTopicStreamWeightRsp) Reset()         { *m = BatchGetTopicStreamWeightRsp{} }
func (m *BatchGetTopicStreamWeightRsp) String() string { return proto.CompactTextString(m) }
func (*BatchGetTopicStreamWeightRsp) ProtoMessage()    {}
func (*BatchGetTopicStreamWeightRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{39}
}
func (m *BatchGetTopicStreamWeightRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetTopicStreamWeightRsp.Unmarshal(m, b)
}
func (m *BatchGetTopicStreamWeightRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetTopicStreamWeightRsp.Marshal(b, m, deterministic)
}
func (dst *BatchGetTopicStreamWeightRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetTopicStreamWeightRsp.Merge(dst, src)
}
func (m *BatchGetTopicStreamWeightRsp) XXX_Size() int {
	return xxx_messageInfo_BatchGetTopicStreamWeightRsp.Size(m)
}
func (m *BatchGetTopicStreamWeightRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetTopicStreamWeightRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetTopicStreamWeightRsp proto.InternalMessageInfo

func (m *BatchGetTopicStreamWeightRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BatchGetTopicStreamWeightRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *BatchGetTopicStreamWeightRsp) GetWeightInfoList() []*TopicStreamWeightInfo {
	if m != nil {
		return m.WeightInfoList
	}
	return nil
}

type GetBlackWhiteListReq struct {
	Usage                BlackWhiteListUsage `protobuf:"varint,1,opt,name=usage,proto3,enum=rcmd.operating_platform.BlackWhiteListUsage" json:"usage,omitempty"`
	Limit                uint32              `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32              `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetBlackWhiteListReq) Reset()         { *m = GetBlackWhiteListReq{} }
func (m *GetBlackWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetBlackWhiteListReq) ProtoMessage()    {}
func (*GetBlackWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{40}
}
func (m *GetBlackWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlackWhiteListReq.Unmarshal(m, b)
}
func (m *GetBlackWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlackWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetBlackWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlackWhiteListReq.Merge(dst, src)
}
func (m *GetBlackWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetBlackWhiteListReq.Size(m)
}
func (m *GetBlackWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlackWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlackWhiteListReq proto.InternalMessageInfo

func (m *GetBlackWhiteListReq) GetUsage() BlackWhiteListUsage {
	if m != nil {
		return m.Usage
	}
	return BlackWhiteListUsage_UnknownBlackWhiteListUsage
}

func (m *GetBlackWhiteListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetBlackWhiteListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetBlackWhiteListRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	Ids                  []string `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	TotalNum             uint32   `protobuf:"varint,4,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBlackWhiteListRsp) Reset()         { *m = GetBlackWhiteListRsp{} }
func (m *GetBlackWhiteListRsp) String() string { return proto.CompactTextString(m) }
func (*GetBlackWhiteListRsp) ProtoMessage()    {}
func (*GetBlackWhiteListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{41}
}
func (m *GetBlackWhiteListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlackWhiteListRsp.Unmarshal(m, b)
}
func (m *GetBlackWhiteListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlackWhiteListRsp.Marshal(b, m, deterministic)
}
func (dst *GetBlackWhiteListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlackWhiteListRsp.Merge(dst, src)
}
func (m *GetBlackWhiteListRsp) XXX_Size() int {
	return xxx_messageInfo_GetBlackWhiteListRsp.Size(m)
}
func (m *GetBlackWhiteListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlackWhiteListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlackWhiteListRsp proto.InternalMessageInfo

func (m *GetBlackWhiteListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetBlackWhiteListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetBlackWhiteListRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *GetBlackWhiteListRsp) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

type ModifyBlackWhiteReq struct {
	Usage                BlackWhiteListUsage       `protobuf:"varint,1,opt,name=usage,proto3,enum=rcmd.operating_platform.BlackWhiteListUsage" json:"usage,omitempty"`
	Opeartion            ModifyBlackWhiteOperation `protobuf:"varint,2,opt,name=opeartion,proto3,enum=rcmd.operating_platform.ModifyBlackWhiteOperation" json:"opeartion,omitempty"`
	Ids                  []string                  `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ModifyBlackWhiteReq) Reset()         { *m = ModifyBlackWhiteReq{} }
func (m *ModifyBlackWhiteReq) String() string { return proto.CompactTextString(m) }
func (*ModifyBlackWhiteReq) ProtoMessage()    {}
func (*ModifyBlackWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{42}
}
func (m *ModifyBlackWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyBlackWhiteReq.Unmarshal(m, b)
}
func (m *ModifyBlackWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyBlackWhiteReq.Marshal(b, m, deterministic)
}
func (dst *ModifyBlackWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyBlackWhiteReq.Merge(dst, src)
}
func (m *ModifyBlackWhiteReq) XXX_Size() int {
	return xxx_messageInfo_ModifyBlackWhiteReq.Size(m)
}
func (m *ModifyBlackWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyBlackWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyBlackWhiteReq proto.InternalMessageInfo

func (m *ModifyBlackWhiteReq) GetUsage() BlackWhiteListUsage {
	if m != nil {
		return m.Usage
	}
	return BlackWhiteListUsage_UnknownBlackWhiteListUsage
}

func (m *ModifyBlackWhiteReq) GetOpeartion() ModifyBlackWhiteOperation {
	if m != nil {
		return m.Opeartion
	}
	return ModifyBlackWhiteOperation_UnknownSetBlackWhiteOperation
}

func (m *ModifyBlackWhiteReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type ModifyBlackWhiteRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyBlackWhiteRsp) Reset()         { *m = ModifyBlackWhiteRsp{} }
func (m *ModifyBlackWhiteRsp) String() string { return proto.CompactTextString(m) }
func (*ModifyBlackWhiteRsp) ProtoMessage()    {}
func (*ModifyBlackWhiteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{43}
}
func (m *ModifyBlackWhiteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyBlackWhiteRsp.Unmarshal(m, b)
}
func (m *ModifyBlackWhiteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyBlackWhiteRsp.Marshal(b, m, deterministic)
}
func (dst *ModifyBlackWhiteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyBlackWhiteRsp.Merge(dst, src)
}
func (m *ModifyBlackWhiteRsp) XXX_Size() int {
	return xxx_messageInfo_ModifyBlackWhiteRsp.Size(m)
}
func (m *ModifyBlackWhiteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyBlackWhiteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyBlackWhiteRsp proto.InternalMessageInfo

func (m *ModifyBlackWhiteRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *ModifyBlackWhiteRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type CheckInBlackWhiteReq struct {
	Usage                BlackWhiteListUsage `protobuf:"varint,1,opt,name=usage,proto3,enum=rcmd.operating_platform.BlackWhiteListUsage" json:"usage,omitempty"`
	Ids                  []string            `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CheckInBlackWhiteReq) Reset()         { *m = CheckInBlackWhiteReq{} }
func (m *CheckInBlackWhiteReq) String() string { return proto.CompactTextString(m) }
func (*CheckInBlackWhiteReq) ProtoMessage()    {}
func (*CheckInBlackWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{44}
}
func (m *CheckInBlackWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInBlackWhiteReq.Unmarshal(m, b)
}
func (m *CheckInBlackWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInBlackWhiteReq.Marshal(b, m, deterministic)
}
func (dst *CheckInBlackWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInBlackWhiteReq.Merge(dst, src)
}
func (m *CheckInBlackWhiteReq) XXX_Size() int {
	return xxx_messageInfo_CheckInBlackWhiteReq.Size(m)
}
func (m *CheckInBlackWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInBlackWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInBlackWhiteReq proto.InternalMessageInfo

func (m *CheckInBlackWhiteReq) GetUsage() BlackWhiteListUsage {
	if m != nil {
		return m.Usage
	}
	return BlackWhiteListUsage_UnknownBlackWhiteListUsage
}

func (m *CheckInBlackWhiteReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type CheckInBlackWhiteRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	Ids                  []string `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInBlackWhiteRsp) Reset()         { *m = CheckInBlackWhiteRsp{} }
func (m *CheckInBlackWhiteRsp) String() string { return proto.CompactTextString(m) }
func (*CheckInBlackWhiteRsp) ProtoMessage()    {}
func (*CheckInBlackWhiteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{45}
}
func (m *CheckInBlackWhiteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInBlackWhiteRsp.Unmarshal(m, b)
}
func (m *CheckInBlackWhiteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInBlackWhiteRsp.Marshal(b, m, deterministic)
}
func (dst *CheckInBlackWhiteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInBlackWhiteRsp.Merge(dst, src)
}
func (m *CheckInBlackWhiteRsp) XXX_Size() int {
	return xxx_messageInfo_CheckInBlackWhiteRsp.Size(m)
}
func (m *CheckInBlackWhiteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInBlackWhiteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInBlackWhiteRsp proto.InternalMessageInfo

func (m *CheckInBlackWhiteRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *CheckInBlackWhiteRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *CheckInBlackWhiteRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type BatchSetShowStatusReq struct {
	PostType             TopicStreamType `protobuf:"varint,1,opt,name=post_type,json=postType,proto3,enum=rcmd.operating_platform.TopicStreamType" json:"post_type,omitempty"`
	TopicId              string          `protobuf:"bytes,2,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	PostIdList           []string        `protobuf:"bytes,3,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	Show                 bool            `protobuf:"varint,4,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *BatchSetShowStatusReq) Reset()         { *m = BatchSetShowStatusReq{} }
func (m *BatchSetShowStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetShowStatusReq) ProtoMessage()    {}
func (*BatchSetShowStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{46}
}
func (m *BatchSetShowStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetShowStatusReq.Unmarshal(m, b)
}
func (m *BatchSetShowStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetShowStatusReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetShowStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetShowStatusReq.Merge(dst, src)
}
func (m *BatchSetShowStatusReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetShowStatusReq.Size(m)
}
func (m *BatchSetShowStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetShowStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetShowStatusReq proto.InternalMessageInfo

func (m *BatchSetShowStatusReq) GetPostType() TopicStreamType {
	if m != nil {
		return m.PostType
	}
	return TopicStreamType_TopicStreamType_Invalid
}

func (m *BatchSetShowStatusReq) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *BatchSetShowStatusReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *BatchSetShowStatusReq) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

type BatchSetShowStatusRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetShowStatusRsp) Reset()         { *m = BatchSetShowStatusRsp{} }
func (m *BatchSetShowStatusRsp) String() string { return proto.CompactTextString(m) }
func (*BatchSetShowStatusRsp) ProtoMessage()    {}
func (*BatchSetShowStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{47}
}
func (m *BatchSetShowStatusRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetShowStatusRsp.Unmarshal(m, b)
}
func (m *BatchSetShowStatusRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetShowStatusRsp.Marshal(b, m, deterministic)
}
func (dst *BatchSetShowStatusRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetShowStatusRsp.Merge(dst, src)
}
func (m *BatchSetShowStatusRsp) XXX_Size() int {
	return xxx_messageInfo_BatchSetShowStatusRsp.Size(m)
}
func (m *BatchSetShowStatusRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetShowStatusRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetShowStatusRsp proto.InternalMessageInfo

func (m *BatchSetShowStatusRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *BatchSetShowStatusRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetVCUserWhiteListReq struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	WhiteLevel           string   `protobuf:"bytes,4,opt,name=white_level,json=whiteLevel,proto3" json:"white_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVCUserWhiteListReq) Reset()         { *m = GetVCUserWhiteListReq{} }
func (m *GetVCUserWhiteListReq) String() string { return proto.CompactTextString(m) }
func (*GetVCUserWhiteListReq) ProtoMessage()    {}
func (*GetVCUserWhiteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{48}
}
func (m *GetVCUserWhiteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCUserWhiteListReq.Unmarshal(m, b)
}
func (m *GetVCUserWhiteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCUserWhiteListReq.Marshal(b, m, deterministic)
}
func (dst *GetVCUserWhiteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCUserWhiteListReq.Merge(dst, src)
}
func (m *GetVCUserWhiteListReq) XXX_Size() int {
	return xxx_messageInfo_GetVCUserWhiteListReq.Size(m)
}
func (m *GetVCUserWhiteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCUserWhiteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCUserWhiteListReq proto.InternalMessageInfo

func (m *GetVCUserWhiteListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetVCUserWhiteListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetVCUserWhiteListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetVCUserWhiteListReq) GetWhiteLevel() string {
	if m != nil {
		return m.WhiteLevel
	}
	return ""
}

type VCUserWhiteItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	WhiteLevel           string   `protobuf:"bytes,2,opt,name=white_level,json=whiteLevel,proto3" json:"white_level,omitempty"`
	CreateTime           string   `protobuf:"bytes,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VCUserWhiteItem) Reset()         { *m = VCUserWhiteItem{} }
func (m *VCUserWhiteItem) String() string { return proto.CompactTextString(m) }
func (*VCUserWhiteItem) ProtoMessage()    {}
func (*VCUserWhiteItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{49}
}
func (m *VCUserWhiteItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VCUserWhiteItem.Unmarshal(m, b)
}
func (m *VCUserWhiteItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VCUserWhiteItem.Marshal(b, m, deterministic)
}
func (dst *VCUserWhiteItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VCUserWhiteItem.Merge(dst, src)
}
func (m *VCUserWhiteItem) XXX_Size() int {
	return xxx_messageInfo_VCUserWhiteItem.Size(m)
}
func (m *VCUserWhiteItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VCUserWhiteItem.DiscardUnknown(m)
}

var xxx_messageInfo_VCUserWhiteItem proto.InternalMessageInfo

func (m *VCUserWhiteItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *VCUserWhiteItem) GetWhiteLevel() string {
	if m != nil {
		return m.WhiteLevel
	}
	return ""
}

func (m *VCUserWhiteItem) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *VCUserWhiteItem) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type GetVCUserWhiteListRsp struct {
	ErrCode              uint32             `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string             `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	TotalCount           uint32             `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ItemList             []*VCUserWhiteItem `protobuf:"bytes,4,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetVCUserWhiteListRsp) Reset()         { *m = GetVCUserWhiteListRsp{} }
func (m *GetVCUserWhiteListRsp) String() string { return proto.CompactTextString(m) }
func (*GetVCUserWhiteListRsp) ProtoMessage()    {}
func (*GetVCUserWhiteListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{50}
}
func (m *GetVCUserWhiteListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCUserWhiteListRsp.Unmarshal(m, b)
}
func (m *GetVCUserWhiteListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCUserWhiteListRsp.Marshal(b, m, deterministic)
}
func (dst *GetVCUserWhiteListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCUserWhiteListRsp.Merge(dst, src)
}
func (m *GetVCUserWhiteListRsp) XXX_Size() int {
	return xxx_messageInfo_GetVCUserWhiteListRsp.Size(m)
}
func (m *GetVCUserWhiteListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCUserWhiteListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCUserWhiteListRsp proto.InternalMessageInfo

func (m *GetVCUserWhiteListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetVCUserWhiteListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetVCUserWhiteListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetVCUserWhiteListRsp) GetItemList() []*VCUserWhiteItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ModifyVCUserWhiteReq struct {
	Operation            ModifyVCOperation  `protobuf:"varint,1,opt,name=operation,proto3,enum=rcmd.operating_platform.ModifyVCOperation" json:"operation,omitempty"`
	ItemList             []*VCUserWhiteItem `protobuf:"bytes,2,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ModifyVCUserWhiteReq) Reset()         { *m = ModifyVCUserWhiteReq{} }
func (m *ModifyVCUserWhiteReq) String() string { return proto.CompactTextString(m) }
func (*ModifyVCUserWhiteReq) ProtoMessage()    {}
func (*ModifyVCUserWhiteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{51}
}
func (m *ModifyVCUserWhiteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCUserWhiteReq.Unmarshal(m, b)
}
func (m *ModifyVCUserWhiteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCUserWhiteReq.Marshal(b, m, deterministic)
}
func (dst *ModifyVCUserWhiteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCUserWhiteReq.Merge(dst, src)
}
func (m *ModifyVCUserWhiteReq) XXX_Size() int {
	return xxx_messageInfo_ModifyVCUserWhiteReq.Size(m)
}
func (m *ModifyVCUserWhiteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCUserWhiteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCUserWhiteReq proto.InternalMessageInfo

func (m *ModifyVCUserWhiteReq) GetOperation() ModifyVCOperation {
	if m != nil {
		return m.Operation
	}
	return ModifyVCOperation_Unknown
}

func (m *ModifyVCUserWhiteReq) GetItemList() []*VCUserWhiteItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ModifyVCUserWhiteRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyVCUserWhiteRsp) Reset()         { *m = ModifyVCUserWhiteRsp{} }
func (m *ModifyVCUserWhiteRsp) String() string { return proto.CompactTextString(m) }
func (*ModifyVCUserWhiteRsp) ProtoMessage()    {}
func (*ModifyVCUserWhiteRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{52}
}
func (m *ModifyVCUserWhiteRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCUserWhiteRsp.Unmarshal(m, b)
}
func (m *ModifyVCUserWhiteRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCUserWhiteRsp.Marshal(b, m, deterministic)
}
func (dst *ModifyVCUserWhiteRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCUserWhiteRsp.Merge(dst, src)
}
func (m *ModifyVCUserWhiteRsp) XXX_Size() int {
	return xxx_messageInfo_ModifyVCUserWhiteRsp.Size(m)
}
func (m *ModifyVCUserWhiteRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCUserWhiteRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCUserWhiteRsp proto.InternalMessageInfo

func (m *ModifyVCUserWhiteRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *ModifyVCUserWhiteRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetVCPostListReq struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	BeginDate            uint32   `protobuf:"varint,3,opt,name=begin_date,json=beginDate,proto3" json:"begin_date,omitempty"`
	EndDate              uint32   `protobuf:"varint,4,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	Show                 string   `protobuf:"bytes,5,opt,name=show,proto3" json:"show,omitempty"`
	PostIdList           []string `protobuf:"bytes,6,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	UidList              []uint32 `protobuf:"varint,7,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVCPostListReq) Reset()         { *m = GetVCPostListReq{} }
func (m *GetVCPostListReq) String() string { return proto.CompactTextString(m) }
func (*GetVCPostListReq) ProtoMessage()    {}
func (*GetVCPostListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{53}
}
func (m *GetVCPostListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCPostListReq.Unmarshal(m, b)
}
func (m *GetVCPostListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCPostListReq.Marshal(b, m, deterministic)
}
func (dst *GetVCPostListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCPostListReq.Merge(dst, src)
}
func (m *GetVCPostListReq) XXX_Size() int {
	return xxx_messageInfo_GetVCPostListReq.Size(m)
}
func (m *GetVCPostListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCPostListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCPostListReq proto.InternalMessageInfo

func (m *GetVCPostListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetVCPostListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetVCPostListReq) GetBeginDate() uint32 {
	if m != nil {
		return m.BeginDate
	}
	return 0
}

func (m *GetVCPostListReq) GetEndDate() uint32 {
	if m != nil {
		return m.EndDate
	}
	return 0
}

func (m *GetVCPostListReq) GetShow() string {
	if m != nil {
		return m.Show
	}
	return ""
}

func (m *GetVCPostListReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *GetVCPostListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type VCPostItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Show                 bool     `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VCPostItem) Reset()         { *m = VCPostItem{} }
func (m *VCPostItem) String() string { return proto.CompactTextString(m) }
func (*VCPostItem) ProtoMessage()    {}
func (*VCPostItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{54}
}
func (m *VCPostItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VCPostItem.Unmarshal(m, b)
}
func (m *VCPostItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VCPostItem.Marshal(b, m, deterministic)
}
func (dst *VCPostItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VCPostItem.Merge(dst, src)
}
func (m *VCPostItem) XXX_Size() int {
	return xxx_messageInfo_VCPostItem.Size(m)
}
func (m *VCPostItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VCPostItem.DiscardUnknown(m)
}

var xxx_messageInfo_VCPostItem proto.InternalMessageInfo

func (m *VCPostItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *VCPostItem) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

type GetVCPostListRsp struct {
	ErrCode              uint32        `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string        `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	TotalCount           uint32        `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ItemList             []*VCPostItem `protobuf:"bytes,4,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetVCPostListRsp) Reset()         { *m = GetVCPostListRsp{} }
func (m *GetVCPostListRsp) String() string { return proto.CompactTextString(m) }
func (*GetVCPostListRsp) ProtoMessage()    {}
func (*GetVCPostListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{55}
}
func (m *GetVCPostListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCPostListRsp.Unmarshal(m, b)
}
func (m *GetVCPostListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCPostListRsp.Marshal(b, m, deterministic)
}
func (dst *GetVCPostListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCPostListRsp.Merge(dst, src)
}
func (m *GetVCPostListRsp) XXX_Size() int {
	return xxx_messageInfo_GetVCPostListRsp.Size(m)
}
func (m *GetVCPostListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCPostListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCPostListRsp proto.InternalMessageInfo

func (m *GetVCPostListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetVCPostListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetVCPostListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetVCPostListRsp) GetItemList() []*VCPostItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ModifyVCPostStatusReq struct {
	PostIdList           []string `protobuf:"bytes,1,rep,name=post_id_list,json=postIdList,proto3" json:"post_id_list,omitempty"`
	Show                 bool     `protobuf:"varint,2,opt,name=show,proto3" json:"show,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyVCPostStatusReq) Reset()         { *m = ModifyVCPostStatusReq{} }
func (m *ModifyVCPostStatusReq) String() string { return proto.CompactTextString(m) }
func (*ModifyVCPostStatusReq) ProtoMessage()    {}
func (*ModifyVCPostStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{56}
}
func (m *ModifyVCPostStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCPostStatusReq.Unmarshal(m, b)
}
func (m *ModifyVCPostStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCPostStatusReq.Marshal(b, m, deterministic)
}
func (dst *ModifyVCPostStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCPostStatusReq.Merge(dst, src)
}
func (m *ModifyVCPostStatusReq) XXX_Size() int {
	return xxx_messageInfo_ModifyVCPostStatusReq.Size(m)
}
func (m *ModifyVCPostStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCPostStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCPostStatusReq proto.InternalMessageInfo

func (m *ModifyVCPostStatusReq) GetPostIdList() []string {
	if m != nil {
		return m.PostIdList
	}
	return nil
}

func (m *ModifyVCPostStatusReq) GetShow() bool {
	if m != nil {
		return m.Show
	}
	return false
}

type VCPostStatusItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	IsFirst              bool     `protobuf:"varint,2,opt,name=is_first,json=isFirst,proto3" json:"is_first,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VCPostStatusItem) Reset()         { *m = VCPostStatusItem{} }
func (m *VCPostStatusItem) String() string { return proto.CompactTextString(m) }
func (*VCPostStatusItem) ProtoMessage()    {}
func (*VCPostStatusItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{57}
}
func (m *VCPostStatusItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VCPostStatusItem.Unmarshal(m, b)
}
func (m *VCPostStatusItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VCPostStatusItem.Marshal(b, m, deterministic)
}
func (dst *VCPostStatusItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VCPostStatusItem.Merge(dst, src)
}
func (m *VCPostStatusItem) XXX_Size() int {
	return xxx_messageInfo_VCPostStatusItem.Size(m)
}
func (m *VCPostStatusItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VCPostStatusItem.DiscardUnknown(m)
}

var xxx_messageInfo_VCPostStatusItem proto.InternalMessageInfo

func (m *VCPostStatusItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *VCPostStatusItem) GetIsFirst() bool {
	if m != nil {
		return m.IsFirst
	}
	return false
}

type ModifyVCPostStatusRsp struct {
	ErrCode              uint32              `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string              `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	ItemList             []*VCPostStatusItem `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ModifyVCPostStatusRsp) Reset()         { *m = ModifyVCPostStatusRsp{} }
func (m *ModifyVCPostStatusRsp) String() string { return proto.CompactTextString(m) }
func (*ModifyVCPostStatusRsp) ProtoMessage()    {}
func (*ModifyVCPostStatusRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{58}
}
func (m *ModifyVCPostStatusRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCPostStatusRsp.Unmarshal(m, b)
}
func (m *ModifyVCPostStatusRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCPostStatusRsp.Marshal(b, m, deterministic)
}
func (dst *ModifyVCPostStatusRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCPostStatusRsp.Merge(dst, src)
}
func (m *ModifyVCPostStatusRsp) XXX_Size() int {
	return xxx_messageInfo_ModifyVCPostStatusRsp.Size(m)
}
func (m *ModifyVCPostStatusRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCPostStatusRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCPostStatusRsp proto.InternalMessageInfo

func (m *ModifyVCPostStatusRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *ModifyVCPostStatusRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *ModifyVCPostStatusRsp) GetItemList() []*VCPostStatusItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type GetVCForceInsertListReq struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVCForceInsertListReq) Reset()         { *m = GetVCForceInsertListReq{} }
func (m *GetVCForceInsertListReq) String() string { return proto.CompactTextString(m) }
func (*GetVCForceInsertListReq) ProtoMessage()    {}
func (*GetVCForceInsertListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{59}
}
func (m *GetVCForceInsertListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCForceInsertListReq.Unmarshal(m, b)
}
func (m *GetVCForceInsertListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCForceInsertListReq.Marshal(b, m, deterministic)
}
func (dst *GetVCForceInsertListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCForceInsertListReq.Merge(dst, src)
}
func (m *GetVCForceInsertListReq) XXX_Size() int {
	return xxx_messageInfo_GetVCForceInsertListReq.Size(m)
}
func (m *GetVCForceInsertListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCForceInsertListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCForceInsertListReq proto.InternalMessageInfo

func (m *GetVCForceInsertListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetVCForceInsertListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type VCForceInsertItem struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	CreateTime           string   `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VCForceInsertItem) Reset()         { *m = VCForceInsertItem{} }
func (m *VCForceInsertItem) String() string { return proto.CompactTextString(m) }
func (*VCForceInsertItem) ProtoMessage()    {}
func (*VCForceInsertItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{60}
}
func (m *VCForceInsertItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VCForceInsertItem.Unmarshal(m, b)
}
func (m *VCForceInsertItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VCForceInsertItem.Marshal(b, m, deterministic)
}
func (dst *VCForceInsertItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VCForceInsertItem.Merge(dst, src)
}
func (m *VCForceInsertItem) XXX_Size() int {
	return xxx_messageInfo_VCForceInsertItem.Size(m)
}
func (m *VCForceInsertItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VCForceInsertItem.DiscardUnknown(m)
}

var xxx_messageInfo_VCForceInsertItem proto.InternalMessageInfo

func (m *VCForceInsertItem) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *VCForceInsertItem) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *VCForceInsertItem) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type GetVCForceInsertListRsp struct {
	ErrCode              uint32               `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string               `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	TotalCount           uint32               `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ItemList             []*VCForceInsertItem `protobuf:"bytes,4,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetVCForceInsertListRsp) Reset()         { *m = GetVCForceInsertListRsp{} }
func (m *GetVCForceInsertListRsp) String() string { return proto.CompactTextString(m) }
func (*GetVCForceInsertListRsp) ProtoMessage()    {}
func (*GetVCForceInsertListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{61}
}
func (m *GetVCForceInsertListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCForceInsertListRsp.Unmarshal(m, b)
}
func (m *GetVCForceInsertListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCForceInsertListRsp.Marshal(b, m, deterministic)
}
func (dst *GetVCForceInsertListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCForceInsertListRsp.Merge(dst, src)
}
func (m *GetVCForceInsertListRsp) XXX_Size() int {
	return xxx_messageInfo_GetVCForceInsertListRsp.Size(m)
}
func (m *GetVCForceInsertListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCForceInsertListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCForceInsertListRsp proto.InternalMessageInfo

func (m *GetVCForceInsertListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetVCForceInsertListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetVCForceInsertListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetVCForceInsertListRsp) GetItemList() []*VCForceInsertItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ModifyVCForceInsertReq struct {
	Operation            ModifyVCOperation  `protobuf:"varint,1,opt,name=operation,proto3,enum=rcmd.operating_platform.ModifyVCOperation" json:"operation,omitempty"`
	Item                 *VCForceInsertItem `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ModifyVCForceInsertReq) Reset()         { *m = ModifyVCForceInsertReq{} }
func (m *ModifyVCForceInsertReq) String() string { return proto.CompactTextString(m) }
func (*ModifyVCForceInsertReq) ProtoMessage()    {}
func (*ModifyVCForceInsertReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{62}
}
func (m *ModifyVCForceInsertReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCForceInsertReq.Unmarshal(m, b)
}
func (m *ModifyVCForceInsertReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCForceInsertReq.Marshal(b, m, deterministic)
}
func (dst *ModifyVCForceInsertReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCForceInsertReq.Merge(dst, src)
}
func (m *ModifyVCForceInsertReq) XXX_Size() int {
	return xxx_messageInfo_ModifyVCForceInsertReq.Size(m)
}
func (m *ModifyVCForceInsertReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCForceInsertReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCForceInsertReq proto.InternalMessageInfo

func (m *ModifyVCForceInsertReq) GetOperation() ModifyVCOperation {
	if m != nil {
		return m.Operation
	}
	return ModifyVCOperation_Unknown
}

func (m *ModifyVCForceInsertReq) GetItem() *VCForceInsertItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type ModifyVCForceInsertRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyVCForceInsertRsp) Reset()         { *m = ModifyVCForceInsertRsp{} }
func (m *ModifyVCForceInsertRsp) String() string { return proto.CompactTextString(m) }
func (*ModifyVCForceInsertRsp) ProtoMessage()    {}
func (*ModifyVCForceInsertRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{63}
}
func (m *ModifyVCForceInsertRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCForceInsertRsp.Unmarshal(m, b)
}
func (m *ModifyVCForceInsertRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCForceInsertRsp.Marshal(b, m, deterministic)
}
func (dst *ModifyVCForceInsertRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCForceInsertRsp.Merge(dst, src)
}
func (m *ModifyVCForceInsertRsp) XXX_Size() int {
	return xxx_messageInfo_ModifyVCForceInsertRsp.Size(m)
}
func (m *ModifyVCForceInsertRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCForceInsertRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCForceInsertRsp proto.InternalMessageInfo

func (m *ModifyVCForceInsertRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *ModifyVCForceInsertRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetVCExposeLimitListReq struct {
	PageNum              uint32   `protobuf:"varint,1,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetVCExposeLimitListReq) Reset()         { *m = GetVCExposeLimitListReq{} }
func (m *GetVCExposeLimitListReq) String() string { return proto.CompactTextString(m) }
func (*GetVCExposeLimitListReq) ProtoMessage()    {}
func (*GetVCExposeLimitListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{64}
}
func (m *GetVCExposeLimitListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCExposeLimitListReq.Unmarshal(m, b)
}
func (m *GetVCExposeLimitListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCExposeLimitListReq.Marshal(b, m, deterministic)
}
func (dst *GetVCExposeLimitListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCExposeLimitListReq.Merge(dst, src)
}
func (m *GetVCExposeLimitListReq) XXX_Size() int {
	return xxx_messageInfo_GetVCExposeLimitListReq.Size(m)
}
func (m *GetVCExposeLimitListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCExposeLimitListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCExposeLimitListReq proto.InternalMessageInfo

func (m *GetVCExposeLimitListReq) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetVCExposeLimitListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type VCExposeLimitItem struct {
	WhiteLevel           string   `protobuf:"bytes,1,opt,name=white_level,json=whiteLevel,proto3" json:"white_level,omitempty"`
	ExposeLimit          uint32   `protobuf:"varint,2,opt,name=expose_limit,json=exposeLimit,proto3" json:"expose_limit,omitempty"`
	UpdateTime           string   `protobuf:"bytes,3,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Operator             string   `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *VCExposeLimitItem) Reset()         { *m = VCExposeLimitItem{} }
func (m *VCExposeLimitItem) String() string { return proto.CompactTextString(m) }
func (*VCExposeLimitItem) ProtoMessage()    {}
func (*VCExposeLimitItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{65}
}
func (m *VCExposeLimitItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_VCExposeLimitItem.Unmarshal(m, b)
}
func (m *VCExposeLimitItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_VCExposeLimitItem.Marshal(b, m, deterministic)
}
func (dst *VCExposeLimitItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_VCExposeLimitItem.Merge(dst, src)
}
func (m *VCExposeLimitItem) XXX_Size() int {
	return xxx_messageInfo_VCExposeLimitItem.Size(m)
}
func (m *VCExposeLimitItem) XXX_DiscardUnknown() {
	xxx_messageInfo_VCExposeLimitItem.DiscardUnknown(m)
}

var xxx_messageInfo_VCExposeLimitItem proto.InternalMessageInfo

func (m *VCExposeLimitItem) GetWhiteLevel() string {
	if m != nil {
		return m.WhiteLevel
	}
	return ""
}

func (m *VCExposeLimitItem) GetExposeLimit() uint32 {
	if m != nil {
		return m.ExposeLimit
	}
	return 0
}

func (m *VCExposeLimitItem) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *VCExposeLimitItem) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type GetVCExposeLimitListRsp struct {
	ErrCode              uint32               `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string               `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	TotalCount           uint32               `protobuf:"varint,3,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	ItemList             []*VCExposeLimitItem `protobuf:"bytes,4,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetVCExposeLimitListRsp) Reset()         { *m = GetVCExposeLimitListRsp{} }
func (m *GetVCExposeLimitListRsp) String() string { return proto.CompactTextString(m) }
func (*GetVCExposeLimitListRsp) ProtoMessage()    {}
func (*GetVCExposeLimitListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{66}
}
func (m *GetVCExposeLimitListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetVCExposeLimitListRsp.Unmarshal(m, b)
}
func (m *GetVCExposeLimitListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetVCExposeLimitListRsp.Marshal(b, m, deterministic)
}
func (dst *GetVCExposeLimitListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetVCExposeLimitListRsp.Merge(dst, src)
}
func (m *GetVCExposeLimitListRsp) XXX_Size() int {
	return xxx_messageInfo_GetVCExposeLimitListRsp.Size(m)
}
func (m *GetVCExposeLimitListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetVCExposeLimitListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetVCExposeLimitListRsp proto.InternalMessageInfo

func (m *GetVCExposeLimitListRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *GetVCExposeLimitListRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetVCExposeLimitListRsp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

func (m *GetVCExposeLimitListRsp) GetItemList() []*VCExposeLimitItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type ModifyVCExposeLimitReq struct {
	Operation            ModifyVCOperation  `protobuf:"varint,1,opt,name=operation,proto3,enum=rcmd.operating_platform.ModifyVCOperation" json:"operation,omitempty"`
	Item                 *VCExposeLimitItem `protobuf:"bytes,2,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ModifyVCExposeLimitReq) Reset()         { *m = ModifyVCExposeLimitReq{} }
func (m *ModifyVCExposeLimitReq) String() string { return proto.CompactTextString(m) }
func (*ModifyVCExposeLimitReq) ProtoMessage()    {}
func (*ModifyVCExposeLimitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{67}
}
func (m *ModifyVCExposeLimitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCExposeLimitReq.Unmarshal(m, b)
}
func (m *ModifyVCExposeLimitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCExposeLimitReq.Marshal(b, m, deterministic)
}
func (dst *ModifyVCExposeLimitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCExposeLimitReq.Merge(dst, src)
}
func (m *ModifyVCExposeLimitReq) XXX_Size() int {
	return xxx_messageInfo_ModifyVCExposeLimitReq.Size(m)
}
func (m *ModifyVCExposeLimitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCExposeLimitReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCExposeLimitReq proto.InternalMessageInfo

func (m *ModifyVCExposeLimitReq) GetOperation() ModifyVCOperation {
	if m != nil {
		return m.Operation
	}
	return ModifyVCOperation_Unknown
}

func (m *ModifyVCExposeLimitReq) GetItem() *VCExposeLimitItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type ModifyVCExposeLimitRsp struct {
	ErrCode              uint32   `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3" json:"err_code,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyVCExposeLimitRsp) Reset()         { *m = ModifyVCExposeLimitRsp{} }
func (m *ModifyVCExposeLimitRsp) String() string { return proto.CompactTextString(m) }
func (*ModifyVCExposeLimitRsp) ProtoMessage()    {}
func (*ModifyVCExposeLimitRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_operating_platform_62d4472b742aae2e, []int{68}
}
func (m *ModifyVCExposeLimitRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyVCExposeLimitRsp.Unmarshal(m, b)
}
func (m *ModifyVCExposeLimitRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyVCExposeLimitRsp.Marshal(b, m, deterministic)
}
func (dst *ModifyVCExposeLimitRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyVCExposeLimitRsp.Merge(dst, src)
}
func (m *ModifyVCExposeLimitRsp) XXX_Size() int {
	return xxx_messageInfo_ModifyVCExposeLimitRsp.Size(m)
}
func (m *ModifyVCExposeLimitRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyVCExposeLimitRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyVCExposeLimitRsp proto.InternalMessageInfo

func (m *ModifyVCExposeLimitRsp) GetErrCode() uint32 {
	if m != nil {
		return m.ErrCode
	}
	return 0
}

func (m *ModifyVCExposeLimitRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func init() {
	proto.RegisterType((*BatchGetWeightInfoWithStatusReq)(nil), "rcmd.operating_platform.BatchGetWeightInfoWithStatusReq")
	proto.RegisterType((*BatchGetWeightInfoWithStatusRsp)(nil), "rcmd.operating_platform.BatchGetWeightInfoWithStatusRsp")
	proto.RegisterType((*GetAllWeightInfoWithStatusReq)(nil), "rcmd.operating_platform.GetAllWeightInfoWithStatusReq")
	proto.RegisterType((*GetAllWeightInfoWithStatusRsp)(nil), "rcmd.operating_platform.GetAllWeightInfoWithStatusRsp")
	proto.RegisterType((*StreamWeightInfo)(nil), "rcmd.operating_platform.StreamWeightInfo")
	proto.RegisterType((*BatchGetTopicStreamByPostIdReq)(nil), "rcmd.operating_platform.BatchGetTopicStreamByPostIdReq")
	proto.RegisterType((*BatchGetTopicStreamByPostIdRsp)(nil), "rcmd.operating_platform.BatchGetTopicStreamByPostIdRsp")
	proto.RegisterType((*BatchGetTopicStreamByPostIdRsp_TopicStreamPost)(nil), "rcmd.operating_platform.BatchGetTopicStreamByPostIdRsp.TopicStreamPost")
	proto.RegisterType((*GetTopicStreamListReq)(nil), "rcmd.operating_platform.GetTopicStreamListReq")
	proto.RegisterType((*GetTopicStreamListRsp)(nil), "rcmd.operating_platform.GetTopicStreamListRsp")
	proto.RegisterType((*GetTopicStreamListRsp_TopicStreamPost)(nil), "rcmd.operating_platform.GetTopicStreamListRsp.TopicStreamPost")
	proto.RegisterType((*GetHighInteractPostListReq)(nil), "rcmd.operating_platform.GetHighInteractPostListReq")
	proto.RegisterType((*GetHighInteractPostListRsp)(nil), "rcmd.operating_platform.GetHighInteractPostListRsp")
	proto.RegisterType((*GetHighInteractPostListRsp_HighInteractPost)(nil), "rcmd.operating_platform.GetHighInteractPostListRsp.HighInteractPost")
	proto.RegisterType((*GetRadarWhiteListReq)(nil), "rcmd.operating_platform.GetRadarWhiteListReq")
	proto.RegisterType((*GetRadarWhiteListRsp)(nil), "rcmd.operating_platform.GetRadarWhiteListRsp")
	proto.RegisterType((*AddRadarWhiteReq)(nil), "rcmd.operating_platform.AddRadarWhiteReq")
	proto.RegisterType((*AddRadarWhiteRsp)(nil), "rcmd.operating_platform.AddRadarWhiteRsp")
	proto.RegisterType((*RemoveRadarWhiteReq)(nil), "rcmd.operating_platform.RemoveRadarWhiteReq")
	proto.RegisterType((*RemoveRadarWhiteRsp)(nil), "rcmd.operating_platform.RemoveRadarWhiteRsp")
	proto.RegisterType((*GetRiskyWordListReq)(nil), "rcmd.operating_platform.GetRiskyWordListReq")
	proto.RegisterType((*GetRiskyWordListRsp)(nil), "rcmd.operating_platform.GetRiskyWordListRsp")
	proto.RegisterType((*AddRiskyWordReq)(nil), "rcmd.operating_platform.AddRiskyWordReq")
	proto.RegisterType((*AddRiskyWordRsp)(nil), "rcmd.operating_platform.AddRiskyWordRsp")
	proto.RegisterType((*RemoveRiskyWordReq)(nil), "rcmd.operating_platform.RemoveRiskyWordReq")
	proto.RegisterType((*RemoveRiskyWordRsp)(nil), "rcmd.operating_platform.RemoveRiskyWordRsp")
	proto.RegisterType((*GetAllChannelInIndexReq)(nil), "rcmd.operating_platform.GetAllChannelInIndexReq")
	proto.RegisterType((*GetAllChannelInIndexRsp)(nil), "rcmd.operating_platform.GetAllChannelInIndexRsp")
	proto.RegisterType((*WeightInfo)(nil), "rcmd.operating_platform.WeightInfo")
	proto.RegisterType((*PostItem)(nil), "rcmd.operating_platform.PostItem")
	proto.RegisterType((*BatchSetWeightInfoReq)(nil), "rcmd.operating_platform.BatchSetWeightInfoReq")
	proto.RegisterType((*BatchSetWeightInfoRsp)(nil), "rcmd.operating_platform.BatchSetWeightInfoRsp")
	proto.RegisterType((*BatchGetWeightInfoReq)(nil), "rcmd.operating_platform.BatchGetWeightInfoReq")
	proto.RegisterType((*BatchGetWeightInfoRsp)(nil), "rcmd.operating_platform.BatchGetWeightInfoRsp")
	proto.RegisterType((*GetAllWeightInfoReq)(nil), "rcmd.operating_platform.GetAllWeightInfoReq")
	proto.RegisterType((*GetAllWeightInfoRsp)(nil), "rcmd.operating_platform.GetAllWeightInfoRsp")
	proto.RegisterType((*TopicStreamWeightInfo)(nil), "rcmd.operating_platform.TopicStreamWeightInfo")
	proto.RegisterType((*GetAllTopicStreamWeightReq)(nil), "rcmd.operating_platform.GetAllTopicStreamWeightReq")
	proto.RegisterType((*GetAllTopicStreamWeightRsp)(nil), "rcmd.operating_platform.GetAllTopicStreamWeightRsp")
	proto.RegisterType((*BatchSetTopicStreamWeightReq)(nil), "rcmd.operating_platform.BatchSetTopicStreamWeightReq")
	proto.RegisterType((*BatchSetTopicStreamWeightRsp)(nil), "rcmd.operating_platform.BatchSetTopicStreamWeightRsp")
	proto.RegisterType((*BatchGetTopicStreamWeightReq)(nil), "rcmd.operating_platform.BatchGetTopicStreamWeightReq")
	proto.RegisterType((*BatchGetTopicStreamWeightRsp)(nil), "rcmd.operating_platform.BatchGetTopicStreamWeightRsp")
	proto.RegisterType((*GetBlackWhiteListReq)(nil), "rcmd.operating_platform.GetBlackWhiteListReq")
	proto.RegisterType((*GetBlackWhiteListRsp)(nil), "rcmd.operating_platform.GetBlackWhiteListRsp")
	proto.RegisterType((*ModifyBlackWhiteReq)(nil), "rcmd.operating_platform.ModifyBlackWhiteReq")
	proto.RegisterType((*ModifyBlackWhiteRsp)(nil), "rcmd.operating_platform.ModifyBlackWhiteRsp")
	proto.RegisterType((*CheckInBlackWhiteReq)(nil), "rcmd.operating_platform.CheckInBlackWhiteReq")
	proto.RegisterType((*CheckInBlackWhiteRsp)(nil), "rcmd.operating_platform.CheckInBlackWhiteRsp")
	proto.RegisterType((*BatchSetShowStatusReq)(nil), "rcmd.operating_platform.BatchSetShowStatusReq")
	proto.RegisterType((*BatchSetShowStatusRsp)(nil), "rcmd.operating_platform.BatchSetShowStatusRsp")
	proto.RegisterType((*GetVCUserWhiteListReq)(nil), "rcmd.operating_platform.GetVCUserWhiteListReq")
	proto.RegisterType((*VCUserWhiteItem)(nil), "rcmd.operating_platform.VCUserWhiteItem")
	proto.RegisterType((*GetVCUserWhiteListRsp)(nil), "rcmd.operating_platform.GetVCUserWhiteListRsp")
	proto.RegisterType((*ModifyVCUserWhiteReq)(nil), "rcmd.operating_platform.ModifyVCUserWhiteReq")
	proto.RegisterType((*ModifyVCUserWhiteRsp)(nil), "rcmd.operating_platform.ModifyVCUserWhiteRsp")
	proto.RegisterType((*GetVCPostListReq)(nil), "rcmd.operating_platform.GetVCPostListReq")
	proto.RegisterType((*VCPostItem)(nil), "rcmd.operating_platform.VCPostItem")
	proto.RegisterType((*GetVCPostListRsp)(nil), "rcmd.operating_platform.GetVCPostListRsp")
	proto.RegisterType((*ModifyVCPostStatusReq)(nil), "rcmd.operating_platform.ModifyVCPostStatusReq")
	proto.RegisterType((*VCPostStatusItem)(nil), "rcmd.operating_platform.VCPostStatusItem")
	proto.RegisterType((*ModifyVCPostStatusRsp)(nil), "rcmd.operating_platform.ModifyVCPostStatusRsp")
	proto.RegisterType((*GetVCForceInsertListReq)(nil), "rcmd.operating_platform.GetVCForceInsertListReq")
	proto.RegisterType((*VCForceInsertItem)(nil), "rcmd.operating_platform.VCForceInsertItem")
	proto.RegisterType((*GetVCForceInsertListRsp)(nil), "rcmd.operating_platform.GetVCForceInsertListRsp")
	proto.RegisterType((*ModifyVCForceInsertReq)(nil), "rcmd.operating_platform.ModifyVCForceInsertReq")
	proto.RegisterType((*ModifyVCForceInsertRsp)(nil), "rcmd.operating_platform.ModifyVCForceInsertRsp")
	proto.RegisterType((*GetVCExposeLimitListReq)(nil), "rcmd.operating_platform.GetVCExposeLimitListReq")
	proto.RegisterType((*VCExposeLimitItem)(nil), "rcmd.operating_platform.VCExposeLimitItem")
	proto.RegisterType((*GetVCExposeLimitListRsp)(nil), "rcmd.operating_platform.GetVCExposeLimitListRsp")
	proto.RegisterType((*ModifyVCExposeLimitReq)(nil), "rcmd.operating_platform.ModifyVCExposeLimitReq")
	proto.RegisterType((*ModifyVCExposeLimitRsp)(nil), "rcmd.operating_platform.ModifyVCExposeLimitRsp")
	proto.RegisterEnum("rcmd.operating_platform.TopicStreamType", TopicStreamType_name, TopicStreamType_value)
	proto.RegisterEnum("rcmd.operating_platform.WeightScene", WeightScene_name, WeightScene_value)
	proto.RegisterEnum("rcmd.operating_platform.WeightStatus", WeightStatus_name, WeightStatus_value)
	proto.RegisterEnum("rcmd.operating_platform.BlackWhiteListUsage", BlackWhiteListUsage_name, BlackWhiteListUsage_value)
	proto.RegisterEnum("rcmd.operating_platform.ModifyBlackWhiteOperation", ModifyBlackWhiteOperation_name, ModifyBlackWhiteOperation_value)
	proto.RegisterEnum("rcmd.operating_platform.ModifyVCOperation", ModifyVCOperation_name, ModifyVCOperation_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RcmdOperatingPlatformClient is the client API for RcmdOperatingPlatform service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RcmdOperatingPlatformClient interface {
	GetHighInteractPostList(ctx context.Context, in *GetHighInteractPostListReq, opts ...grpc.CallOption) (*GetHighInteractPostListRsp, error)
	// 获取话题流的信息，给运营后台用的接口
	GetTopicStreamList(ctx context.Context, in *GetTopicStreamListReq, opts ...grpc.CallOption) (*GetTopicStreamListRsp, error)
	// 批量获取话题流信息（一开始准备给运营后台用的接口，后面不用用到）
	BatchGetTopicStreamByPostId(ctx context.Context, in *BatchGetTopicStreamByPostIdReq, opts ...grpc.CallOption) (*BatchGetTopicStreamByPostIdRsp, error)
	GetRiskyWordList(ctx context.Context, in *GetRiskyWordListReq, opts ...grpc.CallOption) (*GetRiskyWordListRsp, error)
	AddRiskyWord(ctx context.Context, in *AddRiskyWordReq, opts ...grpc.CallOption) (*AddRiskyWordRsp, error)
	RemoveRiskyWord(ctx context.Context, in *RemoveRiskyWordReq, opts ...grpc.CallOption) (*RemoveRiskyWordRsp, error)
	GetRadarWhiteList(ctx context.Context, in *GetRadarWhiteListReq, opts ...grpc.CallOption) (*GetRadarWhiteListRsp, error)
	AddRadarWhite(ctx context.Context, in *AddRadarWhiteReq, opts ...grpc.CallOption) (*AddRadarWhiteRsp, error)
	RemoveRadarWhite(ctx context.Context, in *RemoveRadarWhiteReq, opts ...grpc.CallOption) (*RemoveRadarWhiteRsp, error)
	GetAllChannelInIndex(ctx context.Context, in *GetAllChannelInIndexReq, opts ...grpc.CallOption) (*GetAllChannelInIndexRsp, error)
	// 通用权重接口(maybe)
	// 批量设置提权信息
	BatchSetWeightInfo(ctx context.Context, in *BatchSetWeightInfoReq, opts ...grpc.CallOption) (*BatchSetWeightInfoRsp, error)
	// 根据ID批量获取提权信息
	BatchGetWeightInfo(ctx context.Context, in *BatchGetWeightInfoReq, opts ...grpc.CallOption) (*BatchGetWeightInfoRsp, error)
	// 获取当前生效的所有提权信息
	GetAllWeightInfo(ctx context.Context, in *GetAllWeightInfoReq, opts ...grpc.CallOption) (*GetAllWeightInfoRsp, error)
	// 话题流专用。这是因为话题流要按话题提权 //2022.1.6加上返回展示状态
	GetAllTopicStreamWeight(ctx context.Context, in *GetAllTopicStreamWeightReq, opts ...grpc.CallOption) (*GetAllTopicStreamWeightRsp, error)
	BatchSetTopicStreamWeight(ctx context.Context, in *BatchSetTopicStreamWeightReq, opts ...grpc.CallOption) (*BatchSetTopicStreamWeightRsp, error)
	BatchGetTopicStreamWeight(ctx context.Context, in *BatchGetTopicStreamWeightReq, opts ...grpc.CallOption) (*BatchGetTopicStreamWeightRsp, error)
	// 通用白名单/黑名单
	GetBlackWhiteList(ctx context.Context, in *GetBlackWhiteListReq, opts ...grpc.CallOption) (*GetBlackWhiteListRsp, error)
	ModifyBlackWhite(ctx context.Context, in *ModifyBlackWhiteReq, opts ...grpc.CallOption) (*ModifyBlackWhiteRsp, error)
	CheckInBlackWhite(ctx context.Context, in *CheckInBlackWhiteReq, opts ...grpc.CallOption) (*CheckInBlackWhiteRsp, error)
	// 查询接口（加筛选状态）
	GetAllWeightInfoWithStatus(ctx context.Context, in *GetAllWeightInfoWithStatusReq, opts ...grpc.CallOption) (*GetAllWeightInfoWithStatusRsp, error)
	// 批量设置是否展示话题下的部分帖子
	BatchSetShowStatus(ctx context.Context, in *BatchSetShowStatusReq, opts ...grpc.CallOption) (*BatchSetShowStatusRsp, error)
	// 用户白名单
	GetVCUserWhiteList(ctx context.Context, in *GetVCUserWhiteListReq, opts ...grpc.CallOption) (*GetVCUserWhiteListRsp, error)
	ModifyVCUserWhite(ctx context.Context, in *ModifyVCUserWhiteReq, opts ...grpc.CallOption) (*ModifyVCUserWhiteRsp, error)
	// 白名单用户相关帖子
	GetVCPostList(ctx context.Context, in *GetVCPostListReq, opts ...grpc.CallOption) (*GetVCPostListRsp, error)
	ModifyVCPostStatus(ctx context.Context, in *ModifyVCPostStatusReq, opts ...grpc.CallOption) (*ModifyVCPostStatusRsp, error)
	// 强插
	GetVCForceInsertList(ctx context.Context, in *GetVCForceInsertListReq, opts ...grpc.CallOption) (*GetVCForceInsertListRsp, error)
	ModifyVCForceInsert(ctx context.Context, in *ModifyVCForceInsertReq, opts ...grpc.CallOption) (*ModifyVCForceInsertRsp, error)
	// 曝光上限
	GetVCExposeLimitList(ctx context.Context, in *GetVCExposeLimitListReq, opts ...grpc.CallOption) (*GetVCExposeLimitListRsp, error)
	ModifyVCExposeLimit(ctx context.Context, in *ModifyVCExposeLimitReq, opts ...grpc.CallOption) (*ModifyVCExposeLimitRsp, error)
}

type rcmdOperatingPlatformClient struct {
	cc *grpc.ClientConn
}

func NewRcmdOperatingPlatformClient(cc *grpc.ClientConn) RcmdOperatingPlatformClient {
	return &rcmdOperatingPlatformClient{cc}
}

func (c *rcmdOperatingPlatformClient) GetHighInteractPostList(ctx context.Context, in *GetHighInteractPostListReq, opts ...grpc.CallOption) (*GetHighInteractPostListRsp, error) {
	out := new(GetHighInteractPostListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetHighInteractPostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetTopicStreamList(ctx context.Context, in *GetTopicStreamListReq, opts ...grpc.CallOption) (*GetTopicStreamListRsp, error) {
	out := new(GetTopicStreamListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetTopicStreamList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) BatchGetTopicStreamByPostId(ctx context.Context, in *BatchGetTopicStreamByPostIdReq, opts ...grpc.CallOption) (*BatchGetTopicStreamByPostIdRsp, error) {
	out := new(BatchGetTopicStreamByPostIdRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/BatchGetTopicStreamByPostId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetRiskyWordList(ctx context.Context, in *GetRiskyWordListReq, opts ...grpc.CallOption) (*GetRiskyWordListRsp, error) {
	out := new(GetRiskyWordListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetRiskyWordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) AddRiskyWord(ctx context.Context, in *AddRiskyWordReq, opts ...grpc.CallOption) (*AddRiskyWordRsp, error) {
	out := new(AddRiskyWordRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/AddRiskyWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) RemoveRiskyWord(ctx context.Context, in *RemoveRiskyWordReq, opts ...grpc.CallOption) (*RemoveRiskyWordRsp, error) {
	out := new(RemoveRiskyWordRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/RemoveRiskyWord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetRadarWhiteList(ctx context.Context, in *GetRadarWhiteListReq, opts ...grpc.CallOption) (*GetRadarWhiteListRsp, error) {
	out := new(GetRadarWhiteListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetRadarWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) AddRadarWhite(ctx context.Context, in *AddRadarWhiteReq, opts ...grpc.CallOption) (*AddRadarWhiteRsp, error) {
	out := new(AddRadarWhiteRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/AddRadarWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) RemoveRadarWhite(ctx context.Context, in *RemoveRadarWhiteReq, opts ...grpc.CallOption) (*RemoveRadarWhiteRsp, error) {
	out := new(RemoveRadarWhiteRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/RemoveRadarWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetAllChannelInIndex(ctx context.Context, in *GetAllChannelInIndexReq, opts ...grpc.CallOption) (*GetAllChannelInIndexRsp, error) {
	out := new(GetAllChannelInIndexRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllChannelInIndex", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) BatchSetWeightInfo(ctx context.Context, in *BatchSetWeightInfoReq, opts ...grpc.CallOption) (*BatchSetWeightInfoRsp, error) {
	out := new(BatchSetWeightInfoRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/BatchSetWeightInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) BatchGetWeightInfo(ctx context.Context, in *BatchGetWeightInfoReq, opts ...grpc.CallOption) (*BatchGetWeightInfoRsp, error) {
	out := new(BatchGetWeightInfoRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/BatchGetWeightInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetAllWeightInfo(ctx context.Context, in *GetAllWeightInfoReq, opts ...grpc.CallOption) (*GetAllWeightInfoRsp, error) {
	out := new(GetAllWeightInfoRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllWeightInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetAllTopicStreamWeight(ctx context.Context, in *GetAllTopicStreamWeightReq, opts ...grpc.CallOption) (*GetAllTopicStreamWeightRsp, error) {
	out := new(GetAllTopicStreamWeightRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllTopicStreamWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) BatchSetTopicStreamWeight(ctx context.Context, in *BatchSetTopicStreamWeightReq, opts ...grpc.CallOption) (*BatchSetTopicStreamWeightRsp, error) {
	out := new(BatchSetTopicStreamWeightRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/BatchSetTopicStreamWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) BatchGetTopicStreamWeight(ctx context.Context, in *BatchGetTopicStreamWeightReq, opts ...grpc.CallOption) (*BatchGetTopicStreamWeightRsp, error) {
	out := new(BatchGetTopicStreamWeightRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/BatchGetTopicStreamWeight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetBlackWhiteList(ctx context.Context, in *GetBlackWhiteListReq, opts ...grpc.CallOption) (*GetBlackWhiteListRsp, error) {
	out := new(GetBlackWhiteListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetBlackWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) ModifyBlackWhite(ctx context.Context, in *ModifyBlackWhiteReq, opts ...grpc.CallOption) (*ModifyBlackWhiteRsp, error) {
	out := new(ModifyBlackWhiteRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyBlackWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) CheckInBlackWhite(ctx context.Context, in *CheckInBlackWhiteReq, opts ...grpc.CallOption) (*CheckInBlackWhiteRsp, error) {
	out := new(CheckInBlackWhiteRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/CheckInBlackWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetAllWeightInfoWithStatus(ctx context.Context, in *GetAllWeightInfoWithStatusReq, opts ...grpc.CallOption) (*GetAllWeightInfoWithStatusRsp, error) {
	out := new(GetAllWeightInfoWithStatusRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllWeightInfoWithStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) BatchSetShowStatus(ctx context.Context, in *BatchSetShowStatusReq, opts ...grpc.CallOption) (*BatchSetShowStatusRsp, error) {
	out := new(BatchSetShowStatusRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/BatchSetShowStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetVCUserWhiteList(ctx context.Context, in *GetVCUserWhiteListReq, opts ...grpc.CallOption) (*GetVCUserWhiteListRsp, error) {
	out := new(GetVCUserWhiteListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCUserWhiteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) ModifyVCUserWhite(ctx context.Context, in *ModifyVCUserWhiteReq, opts ...grpc.CallOption) (*ModifyVCUserWhiteRsp, error) {
	out := new(ModifyVCUserWhiteRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCUserWhite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetVCPostList(ctx context.Context, in *GetVCPostListReq, opts ...grpc.CallOption) (*GetVCPostListRsp, error) {
	out := new(GetVCPostListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCPostList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) ModifyVCPostStatus(ctx context.Context, in *ModifyVCPostStatusReq, opts ...grpc.CallOption) (*ModifyVCPostStatusRsp, error) {
	out := new(ModifyVCPostStatusRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCPostStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetVCForceInsertList(ctx context.Context, in *GetVCForceInsertListReq, opts ...grpc.CallOption) (*GetVCForceInsertListRsp, error) {
	out := new(GetVCForceInsertListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCForceInsertList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) ModifyVCForceInsert(ctx context.Context, in *ModifyVCForceInsertReq, opts ...grpc.CallOption) (*ModifyVCForceInsertRsp, error) {
	out := new(ModifyVCForceInsertRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCForceInsert", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) GetVCExposeLimitList(ctx context.Context, in *GetVCExposeLimitListReq, opts ...grpc.CallOption) (*GetVCExposeLimitListRsp, error) {
	out := new(GetVCExposeLimitListRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCExposeLimitList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rcmdOperatingPlatformClient) ModifyVCExposeLimit(ctx context.Context, in *ModifyVCExposeLimitReq, opts ...grpc.CallOption) (*ModifyVCExposeLimitRsp, error) {
	out := new(ModifyVCExposeLimitRsp)
	err := c.cc.Invoke(ctx, "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCExposeLimit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RcmdOperatingPlatformServer is the server API for RcmdOperatingPlatform service.
type RcmdOperatingPlatformServer interface {
	GetHighInteractPostList(context.Context, *GetHighInteractPostListReq) (*GetHighInteractPostListRsp, error)
	// 获取话题流的信息，给运营后台用的接口
	GetTopicStreamList(context.Context, *GetTopicStreamListReq) (*GetTopicStreamListRsp, error)
	// 批量获取话题流信息（一开始准备给运营后台用的接口，后面不用用到）
	BatchGetTopicStreamByPostId(context.Context, *BatchGetTopicStreamByPostIdReq) (*BatchGetTopicStreamByPostIdRsp, error)
	GetRiskyWordList(context.Context, *GetRiskyWordListReq) (*GetRiskyWordListRsp, error)
	AddRiskyWord(context.Context, *AddRiskyWordReq) (*AddRiskyWordRsp, error)
	RemoveRiskyWord(context.Context, *RemoveRiskyWordReq) (*RemoveRiskyWordRsp, error)
	GetRadarWhiteList(context.Context, *GetRadarWhiteListReq) (*GetRadarWhiteListRsp, error)
	AddRadarWhite(context.Context, *AddRadarWhiteReq) (*AddRadarWhiteRsp, error)
	RemoveRadarWhite(context.Context, *RemoveRadarWhiteReq) (*RemoveRadarWhiteRsp, error)
	GetAllChannelInIndex(context.Context, *GetAllChannelInIndexReq) (*GetAllChannelInIndexRsp, error)
	// 通用权重接口(maybe)
	// 批量设置提权信息
	BatchSetWeightInfo(context.Context, *BatchSetWeightInfoReq) (*BatchSetWeightInfoRsp, error)
	// 根据ID批量获取提权信息
	BatchGetWeightInfo(context.Context, *BatchGetWeightInfoReq) (*BatchGetWeightInfoRsp, error)
	// 获取当前生效的所有提权信息
	GetAllWeightInfo(context.Context, *GetAllWeightInfoReq) (*GetAllWeightInfoRsp, error)
	// 话题流专用。这是因为话题流要按话题提权 //2022.1.6加上返回展示状态
	GetAllTopicStreamWeight(context.Context, *GetAllTopicStreamWeightReq) (*GetAllTopicStreamWeightRsp, error)
	BatchSetTopicStreamWeight(context.Context, *BatchSetTopicStreamWeightReq) (*BatchSetTopicStreamWeightRsp, error)
	BatchGetTopicStreamWeight(context.Context, *BatchGetTopicStreamWeightReq) (*BatchGetTopicStreamWeightRsp, error)
	// 通用白名单/黑名单
	GetBlackWhiteList(context.Context, *GetBlackWhiteListReq) (*GetBlackWhiteListRsp, error)
	ModifyBlackWhite(context.Context, *ModifyBlackWhiteReq) (*ModifyBlackWhiteRsp, error)
	CheckInBlackWhite(context.Context, *CheckInBlackWhiteReq) (*CheckInBlackWhiteRsp, error)
	// 查询接口（加筛选状态）
	GetAllWeightInfoWithStatus(context.Context, *GetAllWeightInfoWithStatusReq) (*GetAllWeightInfoWithStatusRsp, error)
	// 批量设置是否展示话题下的部分帖子
	BatchSetShowStatus(context.Context, *BatchSetShowStatusReq) (*BatchSetShowStatusRsp, error)
	// 用户白名单
	GetVCUserWhiteList(context.Context, *GetVCUserWhiteListReq) (*GetVCUserWhiteListRsp, error)
	ModifyVCUserWhite(context.Context, *ModifyVCUserWhiteReq) (*ModifyVCUserWhiteRsp, error)
	// 白名单用户相关帖子
	GetVCPostList(context.Context, *GetVCPostListReq) (*GetVCPostListRsp, error)
	ModifyVCPostStatus(context.Context, *ModifyVCPostStatusReq) (*ModifyVCPostStatusRsp, error)
	// 强插
	GetVCForceInsertList(context.Context, *GetVCForceInsertListReq) (*GetVCForceInsertListRsp, error)
	ModifyVCForceInsert(context.Context, *ModifyVCForceInsertReq) (*ModifyVCForceInsertRsp, error)
	// 曝光上限
	GetVCExposeLimitList(context.Context, *GetVCExposeLimitListReq) (*GetVCExposeLimitListRsp, error)
	ModifyVCExposeLimit(context.Context, *ModifyVCExposeLimitReq) (*ModifyVCExposeLimitRsp, error)
}

func RegisterRcmdOperatingPlatformServer(s *grpc.Server, srv RcmdOperatingPlatformServer) {
	s.RegisterService(&_RcmdOperatingPlatform_serviceDesc, srv)
}

func _RcmdOperatingPlatform_GetHighInteractPostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHighInteractPostListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetHighInteractPostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetHighInteractPostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetHighInteractPostList(ctx, req.(*GetHighInteractPostListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetTopicStreamList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTopicStreamListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetTopicStreamList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetTopicStreamList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetTopicStreamList(ctx, req.(*GetTopicStreamListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_BatchGetTopicStreamByPostId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTopicStreamByPostIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).BatchGetTopicStreamByPostId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/BatchGetTopicStreamByPostId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).BatchGetTopicStreamByPostId(ctx, req.(*BatchGetTopicStreamByPostIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetRiskyWordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRiskyWordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetRiskyWordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetRiskyWordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetRiskyWordList(ctx, req.(*GetRiskyWordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_AddRiskyWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRiskyWordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).AddRiskyWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/AddRiskyWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).AddRiskyWord(ctx, req.(*AddRiskyWordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_RemoveRiskyWord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRiskyWordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).RemoveRiskyWord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/RemoveRiskyWord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).RemoveRiskyWord(ctx, req.(*RemoveRiskyWordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetRadarWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRadarWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetRadarWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetRadarWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetRadarWhiteList(ctx, req.(*GetRadarWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_AddRadarWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRadarWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).AddRadarWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/AddRadarWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).AddRadarWhite(ctx, req.(*AddRadarWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_RemoveRadarWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRadarWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).RemoveRadarWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/RemoveRadarWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).RemoveRadarWhite(ctx, req.(*RemoveRadarWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetAllChannelInIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllChannelInIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetAllChannelInIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllChannelInIndex",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetAllChannelInIndex(ctx, req.(*GetAllChannelInIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_BatchSetWeightInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetWeightInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).BatchSetWeightInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/BatchSetWeightInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).BatchSetWeightInfo(ctx, req.(*BatchSetWeightInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_BatchGetWeightInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWeightInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).BatchGetWeightInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/BatchGetWeightInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).BatchGetWeightInfo(ctx, req.(*BatchGetWeightInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetAllWeightInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWeightInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetAllWeightInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllWeightInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetAllWeightInfo(ctx, req.(*GetAllWeightInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetAllTopicStreamWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllTopicStreamWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetAllTopicStreamWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllTopicStreamWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetAllTopicStreamWeight(ctx, req.(*GetAllTopicStreamWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_BatchSetTopicStreamWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetTopicStreamWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).BatchSetTopicStreamWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/BatchSetTopicStreamWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).BatchSetTopicStreamWeight(ctx, req.(*BatchSetTopicStreamWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_BatchGetTopicStreamWeight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetTopicStreamWeightReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).BatchGetTopicStreamWeight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/BatchGetTopicStreamWeight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).BatchGetTopicStreamWeight(ctx, req.(*BatchGetTopicStreamWeightReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetBlackWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetBlackWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetBlackWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetBlackWhiteList(ctx, req.(*GetBlackWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_ModifyBlackWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyBlackWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).ModifyBlackWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyBlackWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).ModifyBlackWhite(ctx, req.(*ModifyBlackWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_CheckInBlackWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInBlackWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).CheckInBlackWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/CheckInBlackWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).CheckInBlackWhite(ctx, req.(*CheckInBlackWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetAllWeightInfoWithStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllWeightInfoWithStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetAllWeightInfoWithStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetAllWeightInfoWithStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetAllWeightInfoWithStatus(ctx, req.(*GetAllWeightInfoWithStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_BatchSetShowStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetShowStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).BatchSetShowStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/BatchSetShowStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).BatchSetShowStatus(ctx, req.(*BatchSetShowStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetVCUserWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVCUserWhiteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetVCUserWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCUserWhiteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetVCUserWhiteList(ctx, req.(*GetVCUserWhiteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_ModifyVCUserWhite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyVCUserWhiteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).ModifyVCUserWhite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCUserWhite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).ModifyVCUserWhite(ctx, req.(*ModifyVCUserWhiteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetVCPostList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVCPostListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetVCPostList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCPostList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetVCPostList(ctx, req.(*GetVCPostListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_ModifyVCPostStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyVCPostStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).ModifyVCPostStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCPostStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).ModifyVCPostStatus(ctx, req.(*ModifyVCPostStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetVCForceInsertList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVCForceInsertListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetVCForceInsertList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCForceInsertList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetVCForceInsertList(ctx, req.(*GetVCForceInsertListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_ModifyVCForceInsert_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyVCForceInsertReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).ModifyVCForceInsert(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCForceInsert",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).ModifyVCForceInsert(ctx, req.(*ModifyVCForceInsertReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_GetVCExposeLimitList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVCExposeLimitListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).GetVCExposeLimitList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/GetVCExposeLimitList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).GetVCExposeLimitList(ctx, req.(*GetVCExposeLimitListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RcmdOperatingPlatform_ModifyVCExposeLimit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyVCExposeLimitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RcmdOperatingPlatformServer).ModifyVCExposeLimit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.operating_platform.RcmdOperatingPlatform/ModifyVCExposeLimit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RcmdOperatingPlatformServer).ModifyVCExposeLimit(ctx, req.(*ModifyVCExposeLimitReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RcmdOperatingPlatform_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.operating_platform.RcmdOperatingPlatform",
	HandlerType: (*RcmdOperatingPlatformServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetHighInteractPostList",
			Handler:    _RcmdOperatingPlatform_GetHighInteractPostList_Handler,
		},
		{
			MethodName: "GetTopicStreamList",
			Handler:    _RcmdOperatingPlatform_GetTopicStreamList_Handler,
		},
		{
			MethodName: "BatchGetTopicStreamByPostId",
			Handler:    _RcmdOperatingPlatform_BatchGetTopicStreamByPostId_Handler,
		},
		{
			MethodName: "GetRiskyWordList",
			Handler:    _RcmdOperatingPlatform_GetRiskyWordList_Handler,
		},
		{
			MethodName: "AddRiskyWord",
			Handler:    _RcmdOperatingPlatform_AddRiskyWord_Handler,
		},
		{
			MethodName: "RemoveRiskyWord",
			Handler:    _RcmdOperatingPlatform_RemoveRiskyWord_Handler,
		},
		{
			MethodName: "GetRadarWhiteList",
			Handler:    _RcmdOperatingPlatform_GetRadarWhiteList_Handler,
		},
		{
			MethodName: "AddRadarWhite",
			Handler:    _RcmdOperatingPlatform_AddRadarWhite_Handler,
		},
		{
			MethodName: "RemoveRadarWhite",
			Handler:    _RcmdOperatingPlatform_RemoveRadarWhite_Handler,
		},
		{
			MethodName: "GetAllChannelInIndex",
			Handler:    _RcmdOperatingPlatform_GetAllChannelInIndex_Handler,
		},
		{
			MethodName: "BatchSetWeightInfo",
			Handler:    _RcmdOperatingPlatform_BatchSetWeightInfo_Handler,
		},
		{
			MethodName: "BatchGetWeightInfo",
			Handler:    _RcmdOperatingPlatform_BatchGetWeightInfo_Handler,
		},
		{
			MethodName: "GetAllWeightInfo",
			Handler:    _RcmdOperatingPlatform_GetAllWeightInfo_Handler,
		},
		{
			MethodName: "GetAllTopicStreamWeight",
			Handler:    _RcmdOperatingPlatform_GetAllTopicStreamWeight_Handler,
		},
		{
			MethodName: "BatchSetTopicStreamWeight",
			Handler:    _RcmdOperatingPlatform_BatchSetTopicStreamWeight_Handler,
		},
		{
			MethodName: "BatchGetTopicStreamWeight",
			Handler:    _RcmdOperatingPlatform_BatchGetTopicStreamWeight_Handler,
		},
		{
			MethodName: "GetBlackWhiteList",
			Handler:    _RcmdOperatingPlatform_GetBlackWhiteList_Handler,
		},
		{
			MethodName: "ModifyBlackWhite",
			Handler:    _RcmdOperatingPlatform_ModifyBlackWhite_Handler,
		},
		{
			MethodName: "CheckInBlackWhite",
			Handler:    _RcmdOperatingPlatform_CheckInBlackWhite_Handler,
		},
		{
			MethodName: "GetAllWeightInfoWithStatus",
			Handler:    _RcmdOperatingPlatform_GetAllWeightInfoWithStatus_Handler,
		},
		{
			MethodName: "BatchSetShowStatus",
			Handler:    _RcmdOperatingPlatform_BatchSetShowStatus_Handler,
		},
		{
			MethodName: "GetVCUserWhiteList",
			Handler:    _RcmdOperatingPlatform_GetVCUserWhiteList_Handler,
		},
		{
			MethodName: "ModifyVCUserWhite",
			Handler:    _RcmdOperatingPlatform_ModifyVCUserWhite_Handler,
		},
		{
			MethodName: "GetVCPostList",
			Handler:    _RcmdOperatingPlatform_GetVCPostList_Handler,
		},
		{
			MethodName: "ModifyVCPostStatus",
			Handler:    _RcmdOperatingPlatform_ModifyVCPostStatus_Handler,
		},
		{
			MethodName: "GetVCForceInsertList",
			Handler:    _RcmdOperatingPlatform_GetVCForceInsertList_Handler,
		},
		{
			MethodName: "ModifyVCForceInsert",
			Handler:    _RcmdOperatingPlatform_ModifyVCForceInsert_Handler,
		},
		{
			MethodName: "GetVCExposeLimitList",
			Handler:    _RcmdOperatingPlatform_GetVCExposeLimitList_Handler,
		},
		{
			MethodName: "ModifyVCExposeLimit",
			Handler:    _RcmdOperatingPlatform_ModifyVCExposeLimit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "operating-platform/rcmd_operating_platform.proto",
}

func init() {
	proto.RegisterFile("operating-platform/rcmd_operating_platform.proto", fileDescriptor_rcmd_operating_platform_62d4472b742aae2e)
}

var fileDescriptor_rcmd_operating_platform_62d4472b742aae2e = []byte{
	// 2800 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3b, 0xcb, 0x8f, 0x1c, 0x47,
	0xf9, 0xee, 0x9e, 0xdd, 0x79, 0x7c, 0xfb, 0x70, 0xa7, 0xec, 0xcd, 0xda, 0xed, 0x38, 0xb1, 0x3b,
	0xbf, 0x48, 0xce, 0xfe, 0x92, 0xd9, 0x68, 0xa3, 0x24, 0x0a, 0x88, 0x28, 0xfb, 0xf0, 0x8e, 0x37,
	0xb1, 0x13, 0xd3, 0xeb, 0x07, 0x0a, 0x88, 0x51, 0x7b, 0xba, 0x76, 0xa6, 0xe5, 0x99, 0xee, 0x76,
	0x57, 0x8d, 0xd7, 0x9b, 0x1b, 0x70, 0x21, 0x0a, 0x12, 0x42, 0x20, 0x81, 0x72, 0x8c, 0x22, 0x21,
	0x10, 0x12, 0x42, 0x08, 0x71, 0x02, 0xce, 0x5c, 0xb8, 0xe5, 0xbf, 0xe0, 0x00, 0x9c, 0x90, 0xb8,
	0xa0, 0xaa, 0xea, 0x9e, 0xa9, 0x7e, 0xce, 0x76, 0xdb, 0x86, 0xe5, 0x36, 0x5d, 0xf5, 0xd5, 0x57,
	0xdf, 0xab, 0xbe, 0xfa, 0x1e, 0x35, 0xf0, 0x9a, 0xe7, 0xe3, 0xc0, 0xa2, 0x8e, 0xdb, 0x7f, 0xd5,
	0x1f, 0x5a, 0xf4, 0xc0, 0x0b, 0x46, 0xeb, 0x41, 0x6f, 0x64, 0x77, 0x27, 0xe3, 0xdd, 0x68, 0xbc,
	0xed, 0x07, 0x1e, 0xf5, 0xd0, 0x2a, 0x9b, 0x6e, 0xa7, 0xa7, 0xf5, 0x73, 0x3e, 0x0e, 0x88, 0xe7,
	0x5a, 0xeb, 0x94, 0xae, 0xf7, 0x06, 0x96, 0xeb, 0xe2, 0xa1, 0x58, 0x62, 0x7c, 0xaa, 0xc2, 0x0b,
	0x5b, 0x16, 0xed, 0x0d, 0x3a, 0x98, 0xde, 0xc5, 0x4e, 0x7f, 0x40, 0xf7, 0xdc, 0x03, 0xef, 0xae,
	0x43, 0x07, 0xfb, 0xd4, 0xa2, 0x63, 0x62, 0xe2, 0x07, 0xe8, 0x2b, 0x30, 0x4f, 0x7a, 0xd8, 0xc5,
	0xe7, 0x94, 0x4b, 0xca, 0x95, 0xe5, 0x8d, 0xff, 0x6b, 0xe7, 0x6c, 0xd3, 0x16, 0x08, 0xf6, 0x19,
	0xac, 0x29, 0x96, 0xa0, 0x55, 0x68, 0x38, 0x76, 0x77, 0xe8, 0x10, 0x7a, 0x4e, 0xbd, 0x54, 0xbb,
	0xd2, 0x32, 0xeb, 0x8e, 0x7d, 0xdd, 0x21, 0x14, 0x75, 0x60, 0xd9, 0xf7, 0x08, 0xed, 0x3a, 0x14,
	0x8f, 0xc4, 0x7c, 0xed, 0x52, 0xed, 0xca, 0xc2, 0xc6, 0xe5, 0x5c, 0xec, 0x37, 0x3d, 0x42, 0xf7,
	0x28, 0x1e, 0x99, 0x8b, 0x7e, 0xf8, 0x8b, 0x23, 0x7a, 0x0f, 0x96, 0x0e, 0xf9, 0xbe, 0x5d, 0xc2,
	0x29, 0x3e, 0x37, 0xc7, 0xa9, 0x7c, 0x69, 0x16, 0x95, 0x82, 0xbd, 0xc5, 0x43, 0xe9, 0xcb, 0xf8,
	0x42, 0x99, 0x21, 0x0d, 0xe2, 0xa3, 0xf3, 0xd0, 0xc4, 0x41, 0xd0, 0xed, 0x79, 0xb6, 0x10, 0xc8,
	0x92, 0xd9, 0xc0, 0x41, 0xb0, 0xed, 0xd9, 0x9c, 0x59, 0x36, 0x35, 0x22, 0xfd, 0x73, 0xea, 0x25,
	0x85, 0x31, 0x8b, 0x83, 0xe0, 0x06, 0xe9, 0xa3, 0x1b, 0xa0, 0x85, 0x34, 0x3a, 0xee, 0x81, 0x27,
	0xb3, 0xfb, 0xe2, 0x0c, 0x32, 0xd9, 0xfe, 0xe6, 0xf2, 0xe1, 0xe4, 0x37, 0x63, 0xd9, 0xf8, 0x9b,
	0x02, 0x17, 0x3b, 0x98, 0x6e, 0x0e, 0x87, 0x4f, 0x43, 0x65, 0xcf, 0x42, 0xdd, 0x3b, 0x38, 0x20,
	0x98, 0x72, 0x26, 0x96, 0xcc, 0xf0, 0x0b, 0x9d, 0x85, 0xf9, 0xa1, 0x33, 0x72, 0x18, 0xe5, 0x6c,
	0x58, 0x7c, 0x30, 0x71, 0x50, 0xcf, 0x77, 0x7a, 0x5d, 0xc7, 0xe6, 0x92, 0x6f, 0x99, 0x0d, 0xfe,
	0xbd, 0x67, 0xa7, 0x35, 0x33, 0x5f, 0x5d, 0x33, 0x7f, 0x2e, 0x66, 0xb9, 0xa2, 0x5e, 0x5e, 0x80,
	0x05, 0xea, 0x51, 0x6b, 0xd8, 0xed, 0x79, 0x63, 0x37, 0x62, 0x0c, 0xf8, 0xd0, 0x36, 0x1b, 0x41,
	0xfb, 0x19, 0x8a, 0x9b, 0xe3, 0x8a, 0x7b, 0x39, 0x97, 0x8b, 0x7d, 0x1a, 0x60, 0x6b, 0x54, 0xa0,
	0xbe, 0x7f, 0x28, 0xa0, 0x25, 0x81, 0x62, 0x72, 0x54, 0xe2, 0x72, 0x5c, 0x85, 0x86, 0x38, 0x2a,
	0x76, 0x44, 0x3e, 0x3f, 0x00, 0x36, 0xd3, 0xc8, 0x43, 0x6b, 0xe8, 0xd8, 0x9c, 0xf0, 0xa6, 0x29,
	0x3e, 0x98, 0xfe, 0xc4, 0x86, 0x5c, 0x1f, 0x8a, 0x19, 0x7e, 0xb1, 0x1d, 0x08, 0xb5, 0x02, 0xda,
	0xa5, 0x42, 0x13, 0x4b, 0x66, 0x83, 0x7f, 0xdf, 0x22, 0x68, 0x05, 0xea, 0xd8, 0xb5, 0xd9, 0x44,
	0x5d, 0xe8, 0x16, 0xbb, 0xf6, 0x2d, 0x92, 0x56, 0x60, 0xa3, 0xba, 0x02, 0x87, 0xf0, 0x7c, 0x74,
	0xb2, 0x6e, 0x31, 0xbe, 0x84, 0x00, 0xb6, 0x8e, 0xf8, 0xa9, 0xb6, 0x99, 0xcd, 0x22, 0x98, 0xa3,
	0x47, 0x7e, 0xa4, 0x3c, 0xfe, 0x9b, 0x71, 0xc8, 0xa5, 0x10, 0x32, 0x2e, 0x3e, 0xd0, 0x25, 0x58,
	0x0c, 0x05, 0x32, 0x3d, 0x4a, 0x2d, 0x13, 0x84, 0x54, 0xb8, 0x88, 0xff, 0xa2, 0x16, 0x6f, 0x47,
	0x7c, 0x64, 0x43, 0x8b, 0x23, 0xe1, 0x18, 0x14, 0xae, 0xd3, 0x4e, 0x2e, 0x63, 0xc5, 0xb8, 0xda,
	0xd2, 0x30, 0x1b, 0x34, 0x9b, 0x0c, 0x33, 0xf7, 0x4e, 0x09, 0x0b, 0x53, 0x93, 0x16, 0xa6, 0x7f,
	0xa1, 0xc0, 0xe9, 0xc4, 0x72, 0x59, 0xe1, 0x4a, 0x4c, 0xe1, 0x97, 0x61, 0xd1, 0x0b, 0x9c, 0xbe,
	0xe3, 0x76, 0x49, 0xcf, 0x0b, 0x30, 0x47, 0xa7, 0x9a, 0x0b, 0x62, 0x6c, 0x9f, 0x0d, 0x31, 0x90,
	0x48, 0x67, 0x1c, 0xa4, 0x26, 0x40, 0x42, 0x5d, 0x70, 0x90, 0x0d, 0x58, 0x91, 0x41, 0xba, 0xbe,
	0x47, 0x1c, 0xea, 0x78, 0x2e, 0xb7, 0x17, 0xd5, 0x3c, 0x23, 0xc1, 0xde, 0x0c, 0xa7, 0x8c, 0xdf,
	0x2a, 0xb0, 0x12, 0xe7, 0x9f, 0xb1, 0x57, 0x4e, 0x6d, 0x53, 0xc7, 0x52, 0xcb, 0x76, 0x2c, 0x73,
	0xb2, 0x63, 0x49, 0x2a, 0x79, 0x3e, 0xa9, 0x64, 0x06, 0x41, 0xbc, 0x80, 0x76, 0xef, 0x1d, 0x75,
	0xad, 0x61, 0xdf, 0xe3, 0xb6, 0xdb, 0x34, 0x81, 0x8d, 0x6d, 0x1d, 0x6d, 0x0e, 0xfb, 0x9e, 0xf1,
	0x49, 0x2d, 0x93, 0x6a, 0xe2, 0xa3, 0x6f, 0xa6, 0xb5, 0xff, 0x4e, 0xae, 0xf6, 0x33, 0x51, 0x3c,
	0x8e, 0xd2, 0xff, 0xfe, 0x3f, 0xa1, 0x74, 0x86, 0x56, 0x00, 0x1f, 0x58, 0x3d, 0xea, 0x05, 0xdc,
	0x6b, 0xa8, 0xe6, 0x02, 0x1f, 0xdb, 0xe5, 0x43, 0xe8, 0x0a, 0x68, 0xc2, 0x6d, 0x31, 0x0d, 0x84,
	0xbb, 0xd7, 0x39, 0xd8, 0x32, 0x1f, 0x67, 0x6a, 0xe0, 0x48, 0x8d, 0x21, 0xe8, 0x1d, 0x4c, 0xaf,
	0x39, 0xfd, 0xc1, 0x9e, 0x4b, 0x71, 0x60, 0xf5, 0xe8, 0xcd, 0x50, 0x5e, 0xcc, 0x8a, 0x92, 0xda,
	0x56, 0x52, 0xda, 0x2e, 0x75, 0x2d, 0x19, 0xff, 0x54, 0xf3, 0xb7, 0x23, 0x3e, 0xb2, 0xd2, 0xea,
	0xdf, 0x29, 0x52, 0x7f, 0x0e, 0x9e, 0x76, 0x72, 0xbc, 0x8c, 0x11, 0x7c, 0xa9, 0x80, 0x96, 0x5c,
	0x9f, 0x6f, 0x05, 0x17, 0xa0, 0x45, 0xa8, 0x1d, 0x33, 0x81, 0x26, 0xa1, 0xb6, 0x50, 0xee, 0x2b,
	0x80, 0x26, 0x93, 0x53, 0xcd, 0x0a, 0x2b, 0xd0, 0x22, 0xa8, 0x89, 0x5a, 0x5f, 0x82, 0x65, 0x27,
	0xdc, 0x33, 0xc4, 0x27, 0x6c, 0x60, 0x29, 0x1a, 0x15, 0x48, 0xdf, 0x84, 0xd5, 0x38, 0xd8, 0x14,
	0xb3, 0x30, 0x84, 0x95, 0x18, 0xfc, 0xc4, 0x55, 0xec, 0xc0, 0xd9, 0x0e, 0xa6, 0xa6, 0x65, 0x5b,
	0xc1, 0xdd, 0x81, 0x43, 0x71, 0xa4, 0xe2, 0x72, 0x0a, 0xfc, 0x76, 0x16, 0x96, 0x8a, 0xd7, 0x3c,
	0x82, 0xb9, 0xb1, 0x63, 0x13, 0x7e, 0x4f, 0x2c, 0x99, 0xfc, 0xb7, 0xf1, 0x35, 0xd0, 0x36, 0x6d,
	0x7b, 0x8a, 0x9f, 0x51, 0xb8, 0x02, 0x75, 0xcb, 0xf7, 0x23, 0xd9, 0x2f, 0x99, 0xf3, 0x96, 0xef,
	0xef, 0xd9, 0x93, 0xe5, 0xaa, 0xb4, 0x7c, 0x37, 0xb9, 0xbc, 0x1a, 0x69, 0xc6, 0xbb, 0x70, 0xc6,
	0xc4, 0x23, 0xef, 0x21, 0xae, 0x4c, 0xc9, 0x5e, 0x06, 0x86, 0x8a, 0xc4, 0x7c, 0x04, 0x67, 0x98,
	0xcc, 0x1d, 0x72, 0xff, 0xe8, 0xae, 0x17, 0xd8, 0x91, 0xe2, 0x72, 0x88, 0x29, 0xa7, 0xcf, 0x83,
	0x0c, 0xdc, 0x15, 0xd5, 0x79, 0x01, 0x5a, 0x87, 0x5e, 0x10, 0xbb, 0xfb, 0x9b, 0x87, 0x21, 0x4e,
	0xe3, 0x2a, 0x9c, 0x66, 0x8a, 0x89, 0xf6, 0x29, 0xa0, 0x3f, 0x86, 0x46, 0x9d, 0x81, 0xa6, 0xa2,
	0x44, 0xaf, 0x01, 0x0a, 0x95, 0xf3, 0xb8, 0x04, 0x65, 0x60, 0xaa, 0x48, 0xd3, 0x79, 0x58, 0x15,
	0x91, 0xf4, 0xb6, 0xc8, 0x04, 0xf7, 0xdc, 0x3d, 0xd7, 0xc6, 0x8f, 0x4c, 0xfc, 0xc0, 0xf8, 0xa9,
	0x92, 0x33, 0x57, 0x51, 0x53, 0xd7, 0x60, 0x31, 0x4c, 0x37, 0xe5, 0x9c, 0x27, 0x8c, 0x1f, 0xc3,
	0x9c, 0xb4, 0x4d, 0x69, 0x3b, 0xca, 0x49, 0xc3, 0x5d, 0xb7, 0x2c, 0xe2, 0xf4, 0xcc, 0x85, 0x70,
	0x94, 0xb3, 0xff, 0x57, 0x05, 0x40, 0x8a, 0x96, 0x97, 0x41, 0x9d, 0x78, 0x48, 0xd5, 0xb1, 0xd1,
	0x45, 0x80, 0x81, 0x45, 0xba, 0x61, 0xdc, 0xab, 0xf2, 0x40, 0xa0, 0x35, 0xb0, 0x88, 0x58, 0x22,
	0x85, 0xc4, 0xb5, 0xdc, 0x90, 0x78, 0x2e, 0x2f, 0x24, 0x9e, 0x97, 0x43, 0x62, 0x39, 0x4c, 0xaf,
	0xcf, 0x48, 0x77, 0x1e, 0x23, 0x5a, 0xee, 0x40, 0x33, 0x4a, 0x77, 0xf3, 0xaf, 0x04, 0x03, 0x96,
	0x22, 0x5a, 0x64, 0x9b, 0x59, 0x08, 0x09, 0xe2, 0x72, 0xfb, 0x97, 0x02, 0x2b, 0x3c, 0x78, 0xdd,
	0x97, 0x33, 0xda, 0xa7, 0x96, 0xd5, 0xef, 0xc0, 0x82, 0x94, 0x2f, 0x71, 0x69, 0x1f, 0x33, 0xc7,
	0x85, 0x69, 0x92, 0x94, 0x51, 0x1b, 0x98, 0xab, 0x54, 0x1b, 0x30, 0xde, 0xcf, 0x64, 0xbe, 0xe2,
	0xb9, 0xf9, 0x5d, 0x24, 0xca, 0xce, 0x7f, 0x44, 0x94, 0x4f, 0xaa, 0x40, 0x62, 0x7c, 0x96, 0x4d,
	0xf7, 0xc9, 0x28, 0x65, 0x7c, 0xa6, 0xf0, 0x7b, 0x21, 0x96, 0xd7, 0x9f, 0x94, 0x02, 0x86, 0xf1,
	0xb3, 0x2c, 0xe2, 0x4e, 0x86, 0xdc, 0xfe, 0xa4, 0xc0, 0x8a, 0x94, 0x41, 0x9c, 0xf0, 0x42, 0x02,
	0x82, 0x39, 0x32, 0xf0, 0x0e, 0xb9, 0x47, 0x6c, 0x9a, 0xfc, 0xb7, 0xf1, 0x23, 0x85, 0x47, 0xe8,
	0x9b, 0xc3, 0x61, 0x8a, 0x0f, 0x66, 0x00, 0x57, 0xc3, 0x08, 0x7d, 0x92, 0x5b, 0x2e, 0x6f, 0x5c,
	0xc9, 0x15, 0x94, 0x84, 0xe1, 0xd6, 0x91, 0x8f, 0x45, 0x14, 0xce, 0x7e, 0x95, 0x0c, 0x52, 0x7e,
	0x59, 0x40, 0x53, 0x45, 0xbd, 0x7f, 0x23, 0x57, 0xef, 0xed, 0xe3, 0xb0, 0x53, 0x60, 0x02, 0xdf,
	0x55, 0xe1, 0xb9, 0xc8, 0xbb, 0x3d, 0x4d, 0x11, 0xca, 0x06, 0xa5, 0xc6, 0x0d, 0x6a, 0x66, 0x21,
	0x66, 0x6a, 0x59, 0x73, 0xd9, 0x96, 0x35, 0x9f, 0x6b, 0x59, 0xf5, 0x3c, 0xcb, 0x6a, 0x48, 0x96,
	0x65, 0x98, 0x45, 0x32, 0xa8, 0xe8, 0xe8, 0x3f, 0x57, 0x42, 0xa4, 0x9d, 0x93, 0x2b, 0x58, 0xe3,
	0x57, 0x85, 0x44, 0x9e, 0x38, 0x63, 0xfd, 0xbe, 0xc2, 0xf3, 0xb9, 0xad, 0xa1, 0xd5, 0xbb, 0x1f,
	0xcb, 0x0a, 0xb7, 0x60, 0x7e, 0x4c, 0xac, 0x7e, 0x24, 0xc7, 0x57, 0xf2, 0x4b, 0x70, 0xb1, 0xa5,
	0xb7, 0xd9, 0x1a, 0x53, 0x2c, 0x9d, 0x1e, 0x66, 0x55, 0x76, 0xec, 0x39, 0xe5, 0x26, 0xe3, 0x28,
	0x8b, 0x92, 0x8a, 0x02, 0xd3, 0xa0, 0x16, 0x25, 0x96, 0x2d, 0x93, 0xfd, 0x64, 0x41, 0xbc, 0x48,
	0xfb, 0xdd, 0xf1, 0x28, 0x8c, 0x29, 0x9b, 0x7c, 0xe0, 0x83, 0xf1, 0xc8, 0xf8, 0x83, 0x02, 0x67,
	0x6e, 0x78, 0xb6, 0x73, 0x70, 0x34, 0xdd, 0xfe, 0x49, 0x09, 0xe1, 0x26, 0xb4, 0x3c, 0x1f, 0x5b,
	0x01, 0x4f, 0xd0, 0x55, 0x8e, 0x67, 0x23, 0x17, 0x4f, 0x92, 0x88, 0x0f, 0x05, 0x88, 0xe7, 0x9a,
	0x53, 0x24, 0x69, 0xe6, 0x58, 0xae, 0x99, 0x22, 0xbf, 0xe2, 0x21, 0x1b, 0xc2, 0xd9, 0xed, 0x01,
	0xee, 0xdd, 0xdf, 0x73, 0x9f, 0xbc, 0x28, 0x42, 0xc2, 0xd5, 0x29, 0xe1, 0xdf, 0xca, 0xda, 0xed,
	0x49, 0xe9, 0xdc, 0xf8, 0xb5, 0x14, 0x64, 0xef, 0x0f, 0xbc, 0xc3, 0x69, 0x1f, 0xe6, 0x24, 0xb8,
	0xe0, 0xe8, 0xf2, 0x9d, 0x93, 0x2e, 0xdf, 0xf7, 0x33, 0x09, 0xae, 0xa8, 0xca, 0x4f, 0x45, 0x6d,
	0xf8, 0xce, 0xf6, 0x6d, 0x82, 0xe3, 0x25, 0x9f, 0xf3, 0xd0, 0xf4, 0xad, 0x3e, 0xe6, 0x67, 0x21,
	0xc4, 0xc6, 0xbe, 0x3f, 0x18, 0x8f, 0xd8, 0x39, 0xe1, 0x53, 0xc4, 0xf9, 0x18, 0x87, 0xe7, 0x96,
	0xc3, 0xee, 0x3b, 0x1f, 0x73, 0x7e, 0xc7, 0x32, 0x43, 0x4b, 0x66, 0x63, 0x1c, 0x86, 0xc5, 0x2f,
	0xc0, 0xc2, 0x21, 0xdb, 0xa2, 0x3b, 0xc4, 0x0f, 0xf1, 0x30, 0x8c, 0xd8, 0x80, 0x0f, 0x5d, 0x67,
	0x23, 0xc6, 0x77, 0x14, 0x38, 0x2d, 0x91, 0xc2, 0x53, 0x28, 0x0d, 0x6a, 0xe3, 0x49, 0xb6, 0xcd,
	0x7e, 0x26, 0xd1, 0xa8, 0x49, 0x34, 0x0c, 0xa0, 0x17, 0x60, 0x8b, 0xe2, 0x2e, 0x75, 0x46, 0xa2,
	0xa2, 0xda, 0x32, 0x41, 0x0c, 0xdd, 0x72, 0x46, 0x18, 0xe9, 0xd0, 0x14, 0x3a, 0xf4, 0x82, 0x90,
	0x8a, 0xc9, 0x77, 0x54, 0x2d, 0x4f, 0x4a, 0xe4, 0x69, 0x75, 0xa9, 0xae, 0x42, 0x2b, 0x99, 0x2a,
	0xe5, 0x5b, 0x59, 0x42, 0x36, 0x66, 0xd3, 0x89, 0x12, 0x85, 0x9f, 0x2b, 0x70, 0x56, 0x1c, 0x6f,
	0x09, 0x86, 0xa9, 0xf1, 0x1a, 0x77, 0x2d, 0xc2, 0x41, 0x84, 0x56, 0xbc, 0x36, 0xc3, 0xb5, 0xdc,
	0xd9, 0x8e, 0xbb, 0x14, 0xf1, 0x33, 0x4e, 0xa9, 0x5a, 0x99, 0xd2, 0xf7, 0xb2, 0x08, 0xad, 0x68,
	0xbd, 0x5f, 0x2a, 0xa0, 0x71, 0x5d, 0xc9, 0xe5, 0xe8, 0xaa, 0x86, 0x7b, 0x11, 0xe0, 0x1e, 0xee,
	0x3b, 0x6e, 0xd7, 0xb6, 0x28, 0x0e, 0x35, 0xd5, 0xe2, 0x23, 0x3b, 0x16, 0xe5, 0x76, 0xcd, 0x82,
	0x18, 0x3e, 0x19, 0xd6, 0x1b, 0xb0, 0x6b, 0xf3, 0xa9, 0xe8, 0x94, 0xce, 0x73, 0xe2, 0xf8, 0xef,
	0xd4, 0xd9, 0xae, 0xa7, 0xce, 0xb6, 0x7c, 0x50, 0x1a, 0xb1, 0x83, 0x62, 0xbc, 0x0d, 0x20, 0x78,
	0x2a, 0x2e, 0x22, 0x44, 0xfb, 0xaa, 0x92, 0x77, 0xf8, 0x45, 0x4a, 0x24, 0x4f, 0xcb, 0x72, 0xdf,
	0x4d, 0x5b, 0xee, 0x8b, 0x05, 0xf6, 0x30, 0xc9, 0x70, 0xa7, 0xa6, 0x70, 0x03, 0x56, 0x22, 0x53,
	0x60, 0xb3, 0x53, 0xd7, 0x3b, 0xbb, 0xa3, 0x90, 0xc5, 0xfa, 0x2e, 0x68, 0x32, 0xa2, 0x62, 0xd9,
	0x9d, 0x87, 0xa6, 0x43, 0xba, 0x07, 0x4e, 0x40, 0xa2, 0x9a, 0x53, 0xc3, 0x21, 0xbb, 0xec, 0xd3,
	0xf8, 0xb1, 0x92, 0x49, 0x57, 0x45, 0x39, 0xee, 0xca, 0x62, 0xaa, 0xcd, 0xe8, 0x3f, 0x27, 0xe9,
	0x97, 0x84, 0xf5, 0x75, 0x5e, 0xde, 0xbb, 0xb3, 0xbd, 0xeb, 0x05, 0x3d, 0xbc, 0xe7, 0x12, 0x1c,
	0x3c, 0xae, 0xc5, 0x1b, 0x07, 0xf0, 0x4c, 0x0c, 0x1f, 0x97, 0xd8, 0x59, 0x98, 0x77, 0x5c, 0x1b,
	0x3f, 0x8a, 0xea, 0x9b, 0xfc, 0x23, 0xe9, 0x52, 0xd5, 0x42, 0x97, 0x5a, 0x4b, 0xb8, 0xd4, 0xdf,
	0x2b, 0x39, 0xb4, 0x3f, 0x2d, 0xd3, 0xec, 0xa4, 0x4d, 0x73, 0xad, 0x40, 0xe6, 0x09, 0x11, 0x48,
	0x42, 0xff, 0x5c, 0x81, 0x67, 0x23, 0x53, 0x90, 0xa0, 0x9e, 0xac, 0x63, 0x7d, 0x07, 0xe6, 0xd8,
	0x86, 0x9c, 0xc9, 0x72, 0x84, 0xf2, 0x75, 0xc6, 0xf5, 0x6c, 0x1a, 0x2b, 0xfa, 0xd4, 0xc8, 0xce,
	0xae, 0x3e, 0xf2, 0x3d, 0x82, 0xaf, 0xb3, 0x70, 0xfc, 0x71, 0xed, 0xec, 0x27, 0x0a, 0x33, 0x34,
	0x09, 0x21, 0x37, 0xb4, 0xc4, 0x35, 0xae, 0xa4, 0xae, 0xf1, 0xcb, 0xb0, 0x88, 0xf9, 0x9a, 0xae,
	0x9c, 0x21, 0x2c, 0xe0, 0x29, 0x1e, 0x86, 0x63, 0xec, 0xdb, 0xc9, 0x9b, 0x5e, 0x0c, 0xcd, 0xbc,
	0xe9, 0x27, 0x66, 0x99, 0x64, 0xf5, 0x84, 0x98, 0x65, 0x42, 0x60, 0x39, 0x66, 0x29, 0x41, 0xfd,
	0xb7, 0xcc, 0x32, 0x49, 0x68, 0xca, 0x2c, 0x65, 0x1a, 0xab, 0x09, 0x77, 0xed, 0xdd, 0x58, 0xd7,
	0x9d, 0x47, 0xd6, 0x17, 0x60, 0x35, 0x31, 0xd4, 0xdd, 0x73, 0x79, 0x91, 0x42, 0x3b, 0x85, 0x1a,
	0x50, 0xfb, 0x00, 0x1f, 0x6a, 0x0a, 0xfb, 0x71, 0xcd, 0xa3, 0x9a, 0xba, 0xf6, 0x47, 0x05, 0x16,
	0xa4, 0x8a, 0x23, 0x5a, 0x80, 0xc6, 0x14, 0x7c, 0x19, 0xe0, 0x16, 0xbd, 0xdd, 0xef, 0x71, 0x84,
	0x9a, 0x82, 0x96, 0xa0, 0xc5, 0xbf, 0x99, 0x3b, 0xd6, 0x54, 0xb4, 0x0a, 0x67, 0xa6, 0xd3, 0x62,
	0x3f, 0x86, 0xbd, 0x96, 0x35, 0xc1, 0x76, 0x9b, 0x43, 0x97, 0xe0, 0xb9, 0x8c, 0x15, 0x77, 0x1d,
	0x3a, 0x10, 0x5b, 0xcc, 0x67, 0x41, 0x5c, 0xf3, 0xe8, 0x14, 0xa2, 0x3e, 0x21, 0xe2, 0x86, 0xe7,
	0xd9, 0x5a, 0x63, 0xed, 0x7d, 0x58, 0x94, 0xdb, 0x0e, 0x6c, 0xef, 0x1d, 0x7c, 0x60, 0x8d, 0x87,
	0x54, 0x1e, 0xd6, 0x4e, 0xa1, 0x16, 0xcc, 0xdf, 0xe1, 0x7c, 0x29, 0x8c, 0xc9, 0xab, 0x8f, 0x7c,
	0x27, 0xc0, 0xb6, 0xa6, 0x22, 0x80, 0xfa, 0xee, 0x98, 0x8e, 0x03, 0xac, 0xd5, 0xd6, 0x3c, 0x38,
	0x93, 0x91, 0x85, 0xa1, 0xe7, 0x41, 0xbf, 0xed, 0xde, 0x77, 0xbd, 0x43, 0x37, 0x63, 0x56, 0x3b,
	0xc5, 0x64, 0x2e, 0x6e, 0xa8, 0xe8, 0xed, 0xc3, 0x04, 0x42, 0x53, 0xe2, 0x93, 0xb1, 0xf0, 0x59,
	0x53, 0xd7, 0x1e, 0xc2, 0xf9, 0xdc, 0xcc, 0x15, 0x5d, 0x86, 0x8b, 0xe1, 0xb6, 0xfb, 0x72, 0x7a,
	0x3f, 0x01, 0xd0, 0x4e, 0x21, 0x1d, 0x9e, 0xdd, 0xb4, 0xed, 0xac, 0x39, 0x85, 0xcd, 0xed, 0xe0,
	0x61, 0xd6, 0x9c, 0xba, 0xb6, 0x09, 0xcf, 0xa4, 0xcc, 0x9c, 0x89, 0x25, 0xdc, 0x4f, 0x98, 0xca,
	0xa6, 0xcd, 0x84, 0x05, 0x50, 0xbf, 0xcd, 0x5d, 0x87, 0x90, 0xd5, 0x0e, 0x1e, 0x62, 0x8a, 0xb5,
	0xda, 0xc6, 0x6f, 0x2e, 0xc0, 0x8a, 0xd9, 0x1b, 0xd9, 0x1f, 0x46, 0xc6, 0x7f, 0x33, 0xb4, 0x7d,
	0xf4, 0x3d, 0xe1, 0x42, 0xb2, 0x9e, 0x18, 0xa0, 0xd7, 0x4b, 0x3f, 0x4a, 0xc0, 0x0f, 0xf4, 0xd7,
	0x2b, 0xbc, 0x64, 0x40, 0x14, 0x50, 0xfa, 0x99, 0x0b, 0x6a, 0x97, 0x79, 0x13, 0x83, 0x1f, 0xe8,
	0xed, 0x72, 0x6f, 0x68, 0xd0, 0x0f, 0x15, 0xb8, 0x50, 0xf0, 0xb6, 0x0a, 0xbd, 0x55, 0xe9, 0x45,
	0x16, 0x7e, 0xa0, 0xbf, 0x55, 0xf1, 0x29, 0x17, 0x72, 0x79, 0xe8, 0x1b, 0xeb, 0x53, 0xa3, 0x57,
	0x8a, 0xb8, 0x4a, 0xb6, 0xcb, 0xf5, 0x12, 0xd0, 0xc4, 0x47, 0xf7, 0x60, 0x51, 0x6e, 0x34, 0xa3,
	0xfc, 0x74, 0x28, 0xd1, 0xd6, 0xd6, 0x8f, 0x09, 0x49, 0x7c, 0x74, 0x1f, 0x4e, 0x27, 0x7a, 0xc7,
	0xe8, 0xff, 0x73, 0x17, 0xa7, 0xfb, 0xd5, 0xfa, 0xf1, 0x81, 0x89, 0x8f, 0x1e, 0xc0, 0x33, 0xa9,
	0x87, 0x1b, 0xe8, 0xd5, 0x42, 0x99, 0x24, 0x9f, 0x8a, 0xe8, 0x65, 0xc0, 0x89, 0x8f, 0x30, 0x2c,
	0xc5, 0x1e, 0x63, 0xa0, 0x97, 0x0b, 0x45, 0x23, 0xbf, 0xb4, 0xd0, 0x8f, 0x0b, 0x2a, 0x4c, 0x23,
	0xf9, 0xd2, 0xa2, 0xc0, 0x34, 0x32, 0x9e, 0x75, 0xe8, 0x25, 0xa0, 0x89, 0x8f, 0x3e, 0xe6, 0x85,
	0xca, 0x54, 0x33, 0x1e, 0xbd, 0x56, 0x24, 0x9d, 0xac, 0xbe, 0xbe, 0x5e, 0x72, 0x85, 0x70, 0x07,
	0xe9, 0xce, 0x69, 0x81, 0x3b, 0xc8, 0xec, 0x31, 0xeb, 0xa5, 0xe0, 0xa5, 0x5d, 0x3b, 0x65, 0x76,
	0xed, 0x94, 0xdc, 0x35, 0xd5, 0x06, 0x15, 0x47, 0x3e, 0xd6, 0xe5, 0x2b, 0x3e, 0xf2, 0xc9, 0x6e,
	0xa5, 0x5e, 0x02, 0x9a, 0xf8, 0x91, 0xc3, 0xcf, 0xea, 0x32, 0x15, 0x3b, 0xfc, 0x9c, 0x5e, 0x99,
	0x5e, 0x7e, 0x11, 0xf1, 0xd1, 0x27, 0x0a, 0x9c, 0xcf, 0x6d, 0x9d, 0xa0, 0x37, 0x66, 0x6a, 0x2e,
	0x93, 0x92, 0x2a, 0xcb, 0x64, 0x5a, 0x3a, 0x15, 0x68, 0xe9, 0x54, 0xa3, 0x25, 0xaf, 0x6f, 0x22,
	0xfc, 0x57, 0x3c, 0x72, 0x29, 0xf6, 0x5f, 0xa9, 0xa6, 0x86, 0x5e, 0x06, 0x5c, 0x18, 0x60, 0x32,
	0xac, 0x29, 0x30, 0xc0, 0x8c, 0x06, 0x82, 0x5e, 0x02, 0x5a, 0xb0, 0x98, 0xaa, 0x86, 0x17, 0xb0,
	0x98, 0x55, 0xa7, 0xd7, 0xcb, 0x80, 0x13, 0x1f, 0xfd, 0x60, 0xd2, 0x59, 0xcd, 0x7a, 0xbf, 0x8f,
	0xde, 0x3c, 0xf6, 0x01, 0x8a, 0xfd, 0xcf, 0x41, 0xaf, 0xb4, 0x2e, 0xee, 0xde, 0xa6, 0xf5, 0xef,
	0x63, 0xb8, 0xb7, 0x58, 0x75, 0x5f, 0x2f, 0x05, 0x3f, 0x89, 0xb1, 0x12, 0x55, 0xe1, 0xe2, 0x18,
	0x2b, 0x5d, 0x54, 0xd7, 0x4b, 0xc1, 0x0b, 0x6d, 0xa7, 0x8a, 0xa5, 0x05, 0xda, 0xce, 0xaa, 0x00,
	0xeb, 0x65, 0xc0, 0xc5, 0x85, 0x1c, 0xab, 0x1f, 0x16, 0x5c, 0xc8, 0xc9, 0xd2, 0xab, 0x7e, 0x5c,
	0x50, 0x21, 0xcf, 0x74, 0x8d, 0xad, 0x40, 0x9e, 0x99, 0x85, 0x42, 0xbd, 0x14, 0xfc, 0xe4, 0x5a,
	0x4e, 0x15, 0xa2, 0x8a, 0xaf, 0xe5, 0xac, 0x9a, 0x9b, 0x5e, 0x72, 0x05, 0xf1, 0xd1, 0x61, 0xd4,
	0x80, 0x8b, 0xcd, 0xa2, 0xf5, 0x99, 0x2c, 0xc4, 0x0b, 0x4f, 0x7a, 0xb9, 0x05, 0x12, 0xd3, 0x89,
	0x32, 0xc7, 0x2c, 0xa6, 0xd3, 0x05, 0x20, 0xbd, 0xe4, 0x8a, 0x38, 0xd3, 0xd2, 0xec, 0x31, 0x98,
	0x8e, 0x97, 0x35, 0xf4, 0x72, 0x0b, 0x88, 0xbf, 0xf5, 0xd5, 0x8f, 0xde, 0xee, 0x7b, 0x43, 0xcb,
	0xed, 0xb7, 0xdf, 0xd8, 0xa0, 0xb4, 0xdd, 0xf3, 0x46, 0xeb, 0xfc, 0x5f, 0x73, 0x3d, 0x6f, 0xb8,
	0x4e, 0x70, 0xf0, 0xd0, 0xe9, 0x61, 0xc2, 0xff, 0x92, 0xb7, 0x9e, 0xc6, 0x79, 0xaf, 0xce, 0x41,
	0x5f, 0xff, 0x77, 0x00, 0x00, 0x00, 0xff, 0xff, 0x9e, 0x27, 0xe0, 0xd4, 0xc7, 0x37, 0x00, 0x00,
}
