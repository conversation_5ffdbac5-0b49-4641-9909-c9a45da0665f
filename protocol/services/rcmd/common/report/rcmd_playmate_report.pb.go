// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/common/report/rcmd_playmate_report.proto

package report // import "golang.52tt.com/protocol/services/rcmd/common/report"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import playmate "golang.52tt.com/protocol/services/rcmd/persona/tt/playmate"
import user "golang.52tt.com/protocol/services/rcmd/persona/tt/user"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 发放玩伴召回过程数据上报
type PlaymateRecallReportMsg struct {
	// 召回过程数据
	TraceId            string `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ReqUid             uint32 `protobuf:"varint,2,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	Time               uint64 `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	RecallType         uint32 `protobuf:"varint,4,opt,name=recall_type,json=recallType,proto3" json:"recall_type,omitempty"`
	SmallPoolRecallNum uint32 `protobuf:"varint,5,opt,name=small_pool_recall_num,json=smallPoolRecallNum,proto3" json:"small_pool_recall_num,omitempty"`
	BigPoolRecallNum   uint32 `protobuf:"varint,6,opt,name=big_pool_recall_num,json=bigPoolRecallNum,proto3" json:"big_pool_recall_num,omitempty"`
	RecallFilterNum    uint32 `protobuf:"varint,7,opt,name=recall_filter_num,json=recallFilterNum,proto3" json:"recall_filter_num,omitempty"`
	BucketName         string `protobuf:"bytes,20,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	// 过滤过程数据（过滤跟召回放一起）
	NotSameAgeGroupFilterNum uint32   `protobuf:"varint,108,opt,name=not_same_age_group_filter_num,json=notSameAgeGroupFilterNum,proto3" json:"not_same_age_group_filter_num,omitempty"`
	DeliverFilterNum         uint32   `protobuf:"varint,109,opt,name=deliver_filter_num,json=deliverFilterNum,proto3" json:"deliver_filter_num,omitempty"`
	BeyondLimitFilterNum     uint32   `protobuf:"varint,110,opt,name=beyond_limit_filter_num,json=beyondLimitFilterNum,proto3" json:"beyond_limit_filter_num,omitempty"`
	NotDiffSexFilterNum      uint32   `protobuf:"varint,111,opt,name=not_diff_sex_filter_num,json=notDiffSexFilterNum,proto3" json:"not_diff_sex_filter_num,omitempty"`
	AbnormalUserFilterNum    uint32   `protobuf:"varint,112,opt,name=abnormal_user_filter_num,json=abnormalUserFilterNum,proto3" json:"abnormal_user_filter_num,omitempty"`
	SayHiFrequentlyFilterNum uint32   `protobuf:"varint,113,opt,name=say_hi_frequently_filter_num,json=sayHiFrequentlyFilterNum,proto3" json:"say_hi_frequently_filter_num,omitempty"`
	CloseFindPlayerFilterNum uint32   `protobuf:"varint,114,opt,name=close_find_player_filter_num,json=closeFindPlayerFilterNum,proto3" json:"close_find_player_filter_num,omitempty"`
	FollowFilterNum          uint32   `protobuf:"varint,115,opt,name=follow_filter_num,json=followFilterNum,proto3" json:"follow_filter_num,omitempty"`
	OnMicFilterNum           uint32   `protobuf:"varint,116,opt,name=on_mic_filter_num,json=onMicFilterNum,proto3" json:"on_mic_filter_num,omitempty"`
	MyselfFilterNum          uint32   `protobuf:"varint,117,opt,name=myself_filter_num,json=myselfFilterNum,proto3" json:"myself_filter_num,omitempty"`
	NilFilterNum             uint32   `protobuf:"varint,118,opt,name=nil_filter_num,json=nilFilterNum,proto3" json:"nil_filter_num,omitempty"`
	CoolDownFilterNum        uint32   `protobuf:"varint,119,opt,name=cool_down_filter_num,json=coolDownFilterNum,proto3" json:"cool_down_filter_num,omitempty"`
	OutDateFilterNum         uint32   `protobuf:"varint,120,opt,name=out_date_filter_num,json=outDateFilterNum,proto3" json:"out_date_filter_num,omitempty"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-"`
	XXX_unrecognized         []byte   `json:"-"`
	XXX_sizecache            int32    `json:"-"`
}

func (m *PlaymateRecallReportMsg) Reset()         { *m = PlaymateRecallReportMsg{} }
func (m *PlaymateRecallReportMsg) String() string { return proto.CompactTextString(m) }
func (*PlaymateRecallReportMsg) ProtoMessage()    {}
func (*PlaymateRecallReportMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{0}
}
func (m *PlaymateRecallReportMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateRecallReportMsg.Unmarshal(m, b)
}
func (m *PlaymateRecallReportMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateRecallReportMsg.Marshal(b, m, deterministic)
}
func (dst *PlaymateRecallReportMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateRecallReportMsg.Merge(dst, src)
}
func (m *PlaymateRecallReportMsg) XXX_Size() int {
	return xxx_messageInfo_PlaymateRecallReportMsg.Size(m)
}
func (m *PlaymateRecallReportMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateRecallReportMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateRecallReportMsg proto.InternalMessageInfo

func (m *PlaymateRecallReportMsg) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *PlaymateRecallReportMsg) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetRecallType() uint32 {
	if m != nil {
		return m.RecallType
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetSmallPoolRecallNum() uint32 {
	if m != nil {
		return m.SmallPoolRecallNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetBigPoolRecallNum() uint32 {
	if m != nil {
		return m.BigPoolRecallNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetRecallFilterNum() uint32 {
	if m != nil {
		return m.RecallFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetBucketName() string {
	if m != nil {
		return m.BucketName
	}
	return ""
}

func (m *PlaymateRecallReportMsg) GetNotSameAgeGroupFilterNum() uint32 {
	if m != nil {
		return m.NotSameAgeGroupFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetDeliverFilterNum() uint32 {
	if m != nil {
		return m.DeliverFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetBeyondLimitFilterNum() uint32 {
	if m != nil {
		return m.BeyondLimitFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetNotDiffSexFilterNum() uint32 {
	if m != nil {
		return m.NotDiffSexFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetAbnormalUserFilterNum() uint32 {
	if m != nil {
		return m.AbnormalUserFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetSayHiFrequentlyFilterNum() uint32 {
	if m != nil {
		return m.SayHiFrequentlyFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetCloseFindPlayerFilterNum() uint32 {
	if m != nil {
		return m.CloseFindPlayerFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetFollowFilterNum() uint32 {
	if m != nil {
		return m.FollowFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetOnMicFilterNum() uint32 {
	if m != nil {
		return m.OnMicFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetMyselfFilterNum() uint32 {
	if m != nil {
		return m.MyselfFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetNilFilterNum() uint32 {
	if m != nil {
		return m.NilFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetCoolDownFilterNum() uint32 {
	if m != nil {
		return m.CoolDownFilterNum
	}
	return 0
}

func (m *PlaymateRecallReportMsg) GetOutDateFilterNum() uint32 {
	if m != nil {
		return m.OutDateFilterNum
	}
	return 0
}

type FineRankUserInfo struct {
	UserBasic            *user.UserBasic               `protobuf:"bytes,1,opt,name=user_basic,json=userBasic,proto3" json:"user_basic,omitempty"`
	OnlineState          *user.UserOnlineState         `protobuf:"bytes,2,opt,name=online_state,json=onlineState,proto3" json:"online_state,omitempty"`
	UserFeatureVector    string                        `protobuf:"bytes,3,opt,name=user_feature_vector,json=userFeatureVector,proto3" json:"user_feature_vector,omitempty"`
	Score                float32                       `protobuf:"fixed32,4,opt,name=score,proto3" json:"score,omitempty"`
	PlaymateUser         *user.PlaymateUser            `protobuf:"bytes,5,opt,name=playmate_user,json=playmateUser,proto3" json:"playmate_user,omitempty"`
	UserOffline          *user.UserOffline             `protobuf:"bytes,6,opt,name=user_offline,json=userOffline,proto3" json:"user_offline,omitempty"`
	DeliverInfo          *playmate.PlaymateDeliverInfo `protobuf:"bytes,7,opt,name=deliver_info,json=deliverInfo,proto3" json:"deliver_info,omitempty"`
	UserCore             *user.UserCore                `protobuf:"bytes,8,opt,name=user_core,json=userCore,proto3" json:"user_core,omitempty"`
	ChannelUser          *user.ChannelUser             `protobuf:"bytes,9,opt,name=channel_user,json=channelUser,proto3" json:"channel_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *FineRankUserInfo) Reset()         { *m = FineRankUserInfo{} }
func (m *FineRankUserInfo) String() string { return proto.CompactTextString(m) }
func (*FineRankUserInfo) ProtoMessage()    {}
func (*FineRankUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{1}
}
func (m *FineRankUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FineRankUserInfo.Unmarshal(m, b)
}
func (m *FineRankUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FineRankUserInfo.Marshal(b, m, deterministic)
}
func (dst *FineRankUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FineRankUserInfo.Merge(dst, src)
}
func (m *FineRankUserInfo) XXX_Size() int {
	return xxx_messageInfo_FineRankUserInfo.Size(m)
}
func (m *FineRankUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FineRankUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FineRankUserInfo proto.InternalMessageInfo

func (m *FineRankUserInfo) GetUserBasic() *user.UserBasic {
	if m != nil {
		return m.UserBasic
	}
	return nil
}

func (m *FineRankUserInfo) GetOnlineState() *user.UserOnlineState {
	if m != nil {
		return m.OnlineState
	}
	return nil
}

func (m *FineRankUserInfo) GetUserFeatureVector() string {
	if m != nil {
		return m.UserFeatureVector
	}
	return ""
}

func (m *FineRankUserInfo) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *FineRankUserInfo) GetPlaymateUser() *user.PlaymateUser {
	if m != nil {
		return m.PlaymateUser
	}
	return nil
}

func (m *FineRankUserInfo) GetUserOffline() *user.UserOffline {
	if m != nil {
		return m.UserOffline
	}
	return nil
}

func (m *FineRankUserInfo) GetDeliverInfo() *playmate.PlaymateDeliverInfo {
	if m != nil {
		return m.DeliverInfo
	}
	return nil
}

func (m *FineRankUserInfo) GetUserCore() *user.UserCore {
	if m != nil {
		return m.UserCore
	}
	return nil
}

func (m *FineRankUserInfo) GetChannelUser() *user.ChannelUser {
	if m != nil {
		return m.ChannelUser
	}
	return nil
}

type FineRankItemUserInfoList struct {
	FineRankUserInfoList []*FineRankUserInfo `protobuf:"bytes,1,rep,name=fine_rank_user_info_list,json=fineRankUserInfoList,proto3" json:"fine_rank_user_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *FineRankItemUserInfoList) Reset()         { *m = FineRankItemUserInfoList{} }
func (m *FineRankItemUserInfoList) String() string { return proto.CompactTextString(m) }
func (*FineRankItemUserInfoList) ProtoMessage()    {}
func (*FineRankItemUserInfoList) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{2}
}
func (m *FineRankItemUserInfoList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FineRankItemUserInfoList.Unmarshal(m, b)
}
func (m *FineRankItemUserInfoList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FineRankItemUserInfoList.Marshal(b, m, deterministic)
}
func (dst *FineRankItemUserInfoList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FineRankItemUserInfoList.Merge(dst, src)
}
func (m *FineRankItemUserInfoList) XXX_Size() int {
	return xxx_messageInfo_FineRankItemUserInfoList.Size(m)
}
func (m *FineRankItemUserInfoList) XXX_DiscardUnknown() {
	xxx_messageInfo_FineRankItemUserInfoList.DiscardUnknown(m)
}

var xxx_messageInfo_FineRankItemUserInfoList proto.InternalMessageInfo

func (m *FineRankItemUserInfoList) GetFineRankUserInfoList() []*FineRankUserInfo {
	if m != nil {
		return m.FineRankUserInfoList
	}
	return nil
}

// 发放玩伴精排过程数据上报
type PlaymateFineRankReportMsg struct {
	TraceId string `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ReqUid  uint32 `protobuf:"varint,2,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	Time    uint64 `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	// 排序过程数据
	SortType             uint32   `protobuf:"varint,104,opt,name=sort_type,json=sortType,proto3" json:"sort_type,omitempty"`
	ReqUserFeature       string   `protobuf:"bytes,105,opt,name=req_user_feature,json=reqUserFeature,proto3" json:"req_user_feature,omitempty"`
	ItemUserFeatures     string   `protobuf:"bytes,106,opt,name=item_user_features,json=itemUserFeatures,proto3" json:"item_user_features,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateFineRankReportMsg) Reset()         { *m = PlaymateFineRankReportMsg{} }
func (m *PlaymateFineRankReportMsg) String() string { return proto.CompactTextString(m) }
func (*PlaymateFineRankReportMsg) ProtoMessage()    {}
func (*PlaymateFineRankReportMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{3}
}
func (m *PlaymateFineRankReportMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateFineRankReportMsg.Unmarshal(m, b)
}
func (m *PlaymateFineRankReportMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateFineRankReportMsg.Marshal(b, m, deterministic)
}
func (dst *PlaymateFineRankReportMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateFineRankReportMsg.Merge(dst, src)
}
func (m *PlaymateFineRankReportMsg) XXX_Size() int {
	return xxx_messageInfo_PlaymateFineRankReportMsg.Size(m)
}
func (m *PlaymateFineRankReportMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateFineRankReportMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateFineRankReportMsg proto.InternalMessageInfo

func (m *PlaymateFineRankReportMsg) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *PlaymateFineRankReportMsg) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *PlaymateFineRankReportMsg) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *PlaymateFineRankReportMsg) GetSortType() uint32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

func (m *PlaymateFineRankReportMsg) GetReqUserFeature() string {
	if m != nil {
		return m.ReqUserFeature
	}
	return ""
}

func (m *PlaymateFineRankReportMsg) GetItemUserFeatures() string {
	if m != nil {
		return m.ItemUserFeatures
	}
	return ""
}

type PlaymateReRankReportMsg struct {
	TraceId string `protobuf:"bytes,1,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ReqUid  uint32 `protobuf:"varint,2,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	Time    uint64 `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	// 重排过程数据
	SameScoreUserNum     uint32   `protobuf:"varint,208,opt,name=same_score_user_num,json=sameScoreUserNum,proto3" json:"same_score_user_num,omitempty"`
	SuperiorReason       uint32   `protobuf:"varint,209,opt,name=superior_reason,json=superiorReason,proto3" json:"superior_reason,omitempty"`
	BestUserDeliverTimes uint32   `protobuf:"varint,210,opt,name=best_user_deliver_times,json=bestUserDeliverTimes,proto3" json:"best_user_deliver_times,omitempty"`
	BestUserRegTime      uint32   `protobuf:"varint,211,opt,name=best_user_reg_time,json=bestUserRegTime,proto3" json:"best_user_reg_time,omitempty"`
	ItemUid              uint32   `protobuf:"varint,212,opt,name=item_uid,json=itemUid,proto3" json:"item_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateReRankReportMsg) Reset()         { *m = PlaymateReRankReportMsg{} }
func (m *PlaymateReRankReportMsg) String() string { return proto.CompactTextString(m) }
func (*PlaymateReRankReportMsg) ProtoMessage()    {}
func (*PlaymateReRankReportMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{4}
}
func (m *PlaymateReRankReportMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateReRankReportMsg.Unmarshal(m, b)
}
func (m *PlaymateReRankReportMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateReRankReportMsg.Marshal(b, m, deterministic)
}
func (dst *PlaymateReRankReportMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateReRankReportMsg.Merge(dst, src)
}
func (m *PlaymateReRankReportMsg) XXX_Size() int {
	return xxx_messageInfo_PlaymateReRankReportMsg.Size(m)
}
func (m *PlaymateReRankReportMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateReRankReportMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateReRankReportMsg proto.InternalMessageInfo

func (m *PlaymateReRankReportMsg) GetTraceId() string {
	if m != nil {
		return m.TraceId
	}
	return ""
}

func (m *PlaymateReRankReportMsg) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

func (m *PlaymateReRankReportMsg) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *PlaymateReRankReportMsg) GetSameScoreUserNum() uint32 {
	if m != nil {
		return m.SameScoreUserNum
	}
	return 0
}

func (m *PlaymateReRankReportMsg) GetSuperiorReason() uint32 {
	if m != nil {
		return m.SuperiorReason
	}
	return 0
}

func (m *PlaymateReRankReportMsg) GetBestUserDeliverTimes() uint32 {
	if m != nil {
		return m.BestUserDeliverTimes
	}
	return 0
}

func (m *PlaymateReRankReportMsg) GetBestUserRegTime() uint32 {
	if m != nil {
		return m.BestUserRegTime
	}
	return 0
}

func (m *PlaymateReRankReportMsg) GetItemUid() uint32 {
	if m != nil {
		return m.ItemUid
	}
	return 0
}

type PlaymateRuleReportFlatMsg struct {
	// 召回过程数据
	RecallType         uint32 `protobuf:"varint,1,opt,name=recall_type,json=recallType,proto3" json:"recall_type,omitempty"`
	SmallPoolRecallNum uint32 `protobuf:"varint,2,opt,name=small_pool_recall_num,json=smallPoolRecallNum,proto3" json:"small_pool_recall_num,omitempty"`
	BigPoolRecallNum   uint32 `protobuf:"varint,3,opt,name=big_pool_recall_num,json=bigPoolRecallNum,proto3" json:"big_pool_recall_num,omitempty"`
	// 过滤过程数据
	NotSameAgeGroupFilterNum uint32 `protobuf:"varint,4,opt,name=not_same_age_group_filter_num,json=notSameAgeGroupFilterNum,proto3" json:"not_same_age_group_filter_num,omitempty"`
	DeliverFilterNum         uint32 `protobuf:"varint,5,opt,name=deliver_filter_num,json=deliverFilterNum,proto3" json:"deliver_filter_num,omitempty"`
	BeyondLimitFilterNum     uint32 `protobuf:"varint,6,opt,name=beyond_limit_filter_num,json=beyondLimitFilterNum,proto3" json:"beyond_limit_filter_num,omitempty"`
	NotDiffSexFilterNum      uint32 `protobuf:"varint,7,opt,name=not_diff_sex_filter_num,json=notDiffSexFilterNum,proto3" json:"not_diff_sex_filter_num,omitempty"`
	AbnormalUserFilterNum    uint32 `protobuf:"varint,8,opt,name=abnormal_user_filter_num,json=abnormalUserFilterNum,proto3" json:"abnormal_user_filter_num,omitempty"`
	SayHiFrequentlyFilterNum uint32 `protobuf:"varint,9,opt,name=say_hi_frequently_filter_num,json=sayHiFrequentlyFilterNum,proto3" json:"say_hi_frequently_filter_num,omitempty"`
	CloseFindPlayerFilterNum uint32 `protobuf:"varint,10,opt,name=close_find_player_filter_num,json=closeFindPlayerFilterNum,proto3" json:"close_find_player_filter_num,omitempty"`
	FollowFilterNum          uint32 `protobuf:"varint,11,opt,name=follow_filter_num,json=followFilterNum,proto3" json:"follow_filter_num,omitempty"`
	OnMicFilterNum           uint32 `protobuf:"varint,12,opt,name=on_mic_filter_num,json=onMicFilterNum,proto3" json:"on_mic_filter_num,omitempty"`
	MyselfFilterNum          uint32 `protobuf:"varint,13,opt,name=myself_filter_num,json=myselfFilterNum,proto3" json:"myself_filter_num,omitempty"`
	// 排序过程数据
	SuperiorReason uint32 `protobuf:"varint,14,opt,name=superior_reason,json=superiorReason,proto3" json:"superior_reason,omitempty"`
	// 请求用户数据
	ReqUserUid       uint32 `protobuf:"varint,15,opt,name=req_user_uid,json=reqUserUid,proto3" json:"req_user_uid,omitempty"`
	ReqUserSex       uint32 `protobuf:"varint,16,opt,name=req_user_sex,json=reqUserSex,proto3" json:"req_user_sex,omitempty"`
	ReqUserAge       uint32 `protobuf:"varint,17,opt,name=req_user_age,json=reqUserAge,proto3" json:"req_user_age,omitempty"`
	ReqUserCity      string `protobuf:"bytes,18,opt,name=req_user_city,json=reqUserCity,proto3" json:"req_user_city,omitempty"`
	ReqUserTagList   string `protobuf:"bytes,19,opt,name=req_user_tag_list,json=reqUserTagList,proto3" json:"req_user_tag_list,omitempty"`
	ReqUserWzryLevel string `protobuf:"bytes,20,opt,name=req_user_wzry_level,json=reqUserWzryLevel,proto3" json:"req_user_wzry_level,omitempty"`
	ReqUserHpjyLevel string `protobuf:"bytes,21,opt,name=req_user_hpjy_level,json=reqUserHpjyLevel,proto3" json:"req_user_hpjy_level,omitempty"`
	// 下发用户数据
	ItemUserUid          uint32   `protobuf:"varint,22,opt,name=item_user_uid,json=itemUserUid,proto3" json:"item_user_uid,omitempty"`
	ItemUserSex          uint32   `protobuf:"varint,23,opt,name=item_user_sex,json=itemUserSex,proto3" json:"item_user_sex,omitempty"`
	ItemUserAge          uint32   `protobuf:"varint,24,opt,name=item_user_age,json=itemUserAge,proto3" json:"item_user_age,omitempty"`
	ItemUserCity         string   `protobuf:"bytes,25,opt,name=item_user_city,json=itemUserCity,proto3" json:"item_user_city,omitempty"`
	ItemUserTagList      string   `protobuf:"bytes,26,opt,name=item_user_tag_list,json=itemUserTagList,proto3" json:"item_user_tag_list,omitempty"`
	ItemUserWzryLevel    string   `protobuf:"bytes,27,opt,name=item_user_wzry_level,json=itemUserWzryLevel,proto3" json:"item_user_wzry_level,omitempty"`
	ItemUserHpjyLevel    string   `protobuf:"bytes,28,opt,name=item_user_hpjy_level,json=itemUserHpjyLevel,proto3" json:"item_user_hpjy_level,omitempty"`
	Score                float32  `protobuf:"fixed32,29,opt,name=score,proto3" json:"score,omitempty"`
	DeliverTime          uint64   `protobuf:"varint,30,opt,name=deliver_time,json=deliverTime,proto3" json:"deliver_time,omitempty"`
	SameScoreUserNum     uint32   `protobuf:"varint,31,opt,name=same_score_user_num,json=sameScoreUserNum,proto3" json:"same_score_user_num,omitempty"`
	BestUserDeliverTimes uint32   `protobuf:"varint,32,opt,name=best_user_deliver_times,json=bestUserDeliverTimes,proto3" json:"best_user_deliver_times,omitempty"`
	BestUserRegTime      uint32   `protobuf:"varint,33,opt,name=best_user_reg_time,json=bestUserRegTime,proto3" json:"best_user_reg_time,omitempty"`
	SortType             uint32   `protobuf:"varint,34,opt,name=sort_type,json=sortType,proto3" json:"sort_type,omitempty"`
	NilFilterNum         uint32   `protobuf:"varint,35,opt,name=nil_filter_num,json=nilFilterNum,proto3" json:"nil_filter_num,omitempty"`
	CoolDownFilterNum    uint32   `protobuf:"varint,36,opt,name=cool_down_filter_num,json=coolDownFilterNum,proto3" json:"cool_down_filter_num,omitempty"`
	OutDateFilterNum     uint32   `protobuf:"varint,37,opt,name=out_date_filter_num,json=outDateFilterNum,proto3" json:"out_date_filter_num,omitempty"`
	RecallFilterNum      uint32   `protobuf:"varint,38,opt,name=recall_filter_num,json=recallFilterNum,proto3" json:"recall_filter_num,omitempty"`
	UserRawFeature       string   `protobuf:"bytes,39,opt,name=user_raw_feature,json=userRawFeature,proto3" json:"user_raw_feature,omitempty"`
	UserFeatureVector    string   `protobuf:"bytes,40,opt,name=user_feature_vector,json=userFeatureVector,proto3" json:"user_feature_vector,omitempty"`
	BucketName           string   `protobuf:"bytes,41,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PlaymateRuleReportFlatMsg) Reset()         { *m = PlaymateRuleReportFlatMsg{} }
func (m *PlaymateRuleReportFlatMsg) String() string { return proto.CompactTextString(m) }
func (*PlaymateRuleReportFlatMsg) ProtoMessage()    {}
func (*PlaymateRuleReportFlatMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{5}
}
func (m *PlaymateRuleReportFlatMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PlaymateRuleReportFlatMsg.Unmarshal(m, b)
}
func (m *PlaymateRuleReportFlatMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PlaymateRuleReportFlatMsg.Marshal(b, m, deterministic)
}
func (dst *PlaymateRuleReportFlatMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PlaymateRuleReportFlatMsg.Merge(dst, src)
}
func (m *PlaymateRuleReportFlatMsg) XXX_Size() int {
	return xxx_messageInfo_PlaymateRuleReportFlatMsg.Size(m)
}
func (m *PlaymateRuleReportFlatMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_PlaymateRuleReportFlatMsg.DiscardUnknown(m)
}

var xxx_messageInfo_PlaymateRuleReportFlatMsg proto.InternalMessageInfo

func (m *PlaymateRuleReportFlatMsg) GetRecallType() uint32 {
	if m != nil {
		return m.RecallType
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetSmallPoolRecallNum() uint32 {
	if m != nil {
		return m.SmallPoolRecallNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetBigPoolRecallNum() uint32 {
	if m != nil {
		return m.BigPoolRecallNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetNotSameAgeGroupFilterNum() uint32 {
	if m != nil {
		return m.NotSameAgeGroupFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetDeliverFilterNum() uint32 {
	if m != nil {
		return m.DeliverFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetBeyondLimitFilterNum() uint32 {
	if m != nil {
		return m.BeyondLimitFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetNotDiffSexFilterNum() uint32 {
	if m != nil {
		return m.NotDiffSexFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetAbnormalUserFilterNum() uint32 {
	if m != nil {
		return m.AbnormalUserFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetSayHiFrequentlyFilterNum() uint32 {
	if m != nil {
		return m.SayHiFrequentlyFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetCloseFindPlayerFilterNum() uint32 {
	if m != nil {
		return m.CloseFindPlayerFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetFollowFilterNum() uint32 {
	if m != nil {
		return m.FollowFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetOnMicFilterNum() uint32 {
	if m != nil {
		return m.OnMicFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetMyselfFilterNum() uint32 {
	if m != nil {
		return m.MyselfFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetSuperiorReason() uint32 {
	if m != nil {
		return m.SuperiorReason
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserUid() uint32 {
	if m != nil {
		return m.ReqUserUid
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserSex() uint32 {
	if m != nil {
		return m.ReqUserSex
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserAge() uint32 {
	if m != nil {
		return m.ReqUserAge
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserCity() string {
	if m != nil {
		return m.ReqUserCity
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserTagList() string {
	if m != nil {
		return m.ReqUserTagList
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserWzryLevel() string {
	if m != nil {
		return m.ReqUserWzryLevel
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetReqUserHpjyLevel() string {
	if m != nil {
		return m.ReqUserHpjyLevel
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserUid() uint32 {
	if m != nil {
		return m.ItemUserUid
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserSex() uint32 {
	if m != nil {
		return m.ItemUserSex
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserAge() uint32 {
	if m != nil {
		return m.ItemUserAge
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserCity() string {
	if m != nil {
		return m.ItemUserCity
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserTagList() string {
	if m != nil {
		return m.ItemUserTagList
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserWzryLevel() string {
	if m != nil {
		return m.ItemUserWzryLevel
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetItemUserHpjyLevel() string {
	if m != nil {
		return m.ItemUserHpjyLevel
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetDeliverTime() uint64 {
	if m != nil {
		return m.DeliverTime
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetSameScoreUserNum() uint32 {
	if m != nil {
		return m.SameScoreUserNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetBestUserDeliverTimes() uint32 {
	if m != nil {
		return m.BestUserDeliverTimes
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetBestUserRegTime() uint32 {
	if m != nil {
		return m.BestUserRegTime
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetSortType() uint32 {
	if m != nil {
		return m.SortType
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetNilFilterNum() uint32 {
	if m != nil {
		return m.NilFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetCoolDownFilterNum() uint32 {
	if m != nil {
		return m.CoolDownFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetOutDateFilterNum() uint32 {
	if m != nil {
		return m.OutDateFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetRecallFilterNum() uint32 {
	if m != nil {
		return m.RecallFilterNum
	}
	return 0
}

func (m *PlaymateRuleReportFlatMsg) GetUserRawFeature() string {
	if m != nil {
		return m.UserRawFeature
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetUserFeatureVector() string {
	if m != nil {
		return m.UserFeatureVector
	}
	return ""
}

func (m *PlaymateRuleReportFlatMsg) GetBucketName() string {
	if m != nil {
		return m.BucketName
	}
	return ""
}

type UserRawFeature struct {
	ReqUser              *UserAllProfile `protobuf:"bytes,1,opt,name=req_user,json=reqUser,proto3" json:"req_user,omitempty"`
	ItemUser             *UserAllProfile `protobuf:"bytes,2,opt,name=item_user,json=itemUser,proto3" json:"item_user,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *UserRawFeature) Reset()         { *m = UserRawFeature{} }
func (m *UserRawFeature) String() string { return proto.CompactTextString(m) }
func (*UserRawFeature) ProtoMessage()    {}
func (*UserRawFeature) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{6}
}
func (m *UserRawFeature) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRawFeature.Unmarshal(m, b)
}
func (m *UserRawFeature) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRawFeature.Marshal(b, m, deterministic)
}
func (dst *UserRawFeature) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRawFeature.Merge(dst, src)
}
func (m *UserRawFeature) XXX_Size() int {
	return xxx_messageInfo_UserRawFeature.Size(m)
}
func (m *UserRawFeature) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRawFeature.DiscardUnknown(m)
}

var xxx_messageInfo_UserRawFeature proto.InternalMessageInfo

func (m *UserRawFeature) GetReqUser() *UserAllProfile {
	if m != nil {
		return m.ReqUser
	}
	return nil
}

func (m *UserRawFeature) GetItemUser() *UserAllProfile {
	if m != nil {
		return m.ItemUser
	}
	return nil
}

type UserAllProfile struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RegTime              uint64   `protobuf:"varint,2,opt,name=reg_time,json=regTime,proto3" json:"reg_time,omitempty"`
	Sex                  uint32   `protobuf:"varint,3,opt,name=sex,proto3" json:"sex,omitempty"`
	Birthday             string   `protobuf:"bytes,4,opt,name=birthday,proto3" json:"birthday,omitempty"`
	Age                  uint32   `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	AgeGroup             uint32   `protobuf:"varint,6,opt,name=age_group,json=ageGroup,proto3" json:"age_group,omitempty"`
	Tag_2                string   `protobuf:"bytes,7,opt,name=tag_2,json=tag2,proto3" json:"tag_2,omitempty"`
	Tag_4                string   `protobuf:"bytes,8,opt,name=tag_4,json=tag4,proto3" json:"tag_4,omitempty"`
	Tag_4_13Opt1         string   `protobuf:"bytes,9,opt,name=tag_4_13_opt1,json=tag413Opt1,proto3" json:"tag_4_13_opt1,omitempty"`
	Tag_4_16Opt1         string   `protobuf:"bytes,10,opt,name=tag_4_16_opt1,json=tag416Opt1,proto3" json:"tag_4_16_opt1,omitempty"`
	Tag_4_13Opt2         string   `protobuf:"bytes,11,opt,name=tag_4_13_opt2,json=tag413Opt2,proto3" json:"tag_4_13_opt2,omitempty"`
	Tag_4_16Opt2         string   `protobuf:"bytes,12,opt,name=tag_4_16_opt2,json=tag416Opt2,proto3" json:"tag_4_16_opt2,omitempty"`
	Platform             uint32   `protobuf:"varint,13,opt,name=platform,proto3" json:"platform,omitempty"`
	LastLoginCity        string   `protobuf:"bytes,14,opt,name=last_login_city,json=lastLoginCity,proto3" json:"last_login_city,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAllProfile) Reset()         { *m = UserAllProfile{} }
func (m *UserAllProfile) String() string { return proto.CompactTextString(m) }
func (*UserAllProfile) ProtoMessage()    {}
func (*UserAllProfile) Descriptor() ([]byte, []int) {
	return fileDescriptor_rcmd_playmate_report_4bee524f79f84818, []int{7}
}
func (m *UserAllProfile) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAllProfile.Unmarshal(m, b)
}
func (m *UserAllProfile) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAllProfile.Marshal(b, m, deterministic)
}
func (dst *UserAllProfile) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAllProfile.Merge(dst, src)
}
func (m *UserAllProfile) XXX_Size() int {
	return xxx_messageInfo_UserAllProfile.Size(m)
}
func (m *UserAllProfile) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAllProfile.DiscardUnknown(m)
}

var xxx_messageInfo_UserAllProfile proto.InternalMessageInfo

func (m *UserAllProfile) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserAllProfile) GetRegTime() uint64 {
	if m != nil {
		return m.RegTime
	}
	return 0
}

func (m *UserAllProfile) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *UserAllProfile) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *UserAllProfile) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *UserAllProfile) GetAgeGroup() uint32 {
	if m != nil {
		return m.AgeGroup
	}
	return 0
}

func (m *UserAllProfile) GetTag_2() string {
	if m != nil {
		return m.Tag_2
	}
	return ""
}

func (m *UserAllProfile) GetTag_4() string {
	if m != nil {
		return m.Tag_4
	}
	return ""
}

func (m *UserAllProfile) GetTag_4_13Opt1() string {
	if m != nil {
		return m.Tag_4_13Opt1
	}
	return ""
}

func (m *UserAllProfile) GetTag_4_16Opt1() string {
	if m != nil {
		return m.Tag_4_16Opt1
	}
	return ""
}

func (m *UserAllProfile) GetTag_4_13Opt2() string {
	if m != nil {
		return m.Tag_4_13Opt2
	}
	return ""
}

func (m *UserAllProfile) GetTag_4_16Opt2() string {
	if m != nil {
		return m.Tag_4_16Opt2
	}
	return ""
}

func (m *UserAllProfile) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *UserAllProfile) GetLastLoginCity() string {
	if m != nil {
		return m.LastLoginCity
	}
	return ""
}

func init() {
	proto.RegisterType((*PlaymateRecallReportMsg)(nil), "rcmd.common.report.PlaymateRecallReportMsg")
	proto.RegisterType((*FineRankUserInfo)(nil), "rcmd.common.report.FineRankUserInfo")
	proto.RegisterType((*FineRankItemUserInfoList)(nil), "rcmd.common.report.FineRankItemUserInfoList")
	proto.RegisterType((*PlaymateFineRankReportMsg)(nil), "rcmd.common.report.PlaymateFineRankReportMsg")
	proto.RegisterType((*PlaymateReRankReportMsg)(nil), "rcmd.common.report.PlaymateReRankReportMsg")
	proto.RegisterType((*PlaymateRuleReportFlatMsg)(nil), "rcmd.common.report.PlaymateRuleReportFlatMsg")
	proto.RegisterType((*UserRawFeature)(nil), "rcmd.common.report.UserRawFeature")
	proto.RegisterType((*UserAllProfile)(nil), "rcmd.common.report.UserAllProfile")
}

func init() {
	proto.RegisterFile("rcmd/common/report/rcmd_playmate_report.proto", fileDescriptor_rcmd_playmate_report_4bee524f79f84818)
}

var fileDescriptor_rcmd_playmate_report_4bee524f79f84818 = []byte{
	// 1705 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xdd, 0x6e, 0xdc, 0xc6,
	0x15, 0xc6, 0x4a, 0xb2, 0xb5, 0x3b, 0x2b, 0xad, 0x56, 0x94, 0x5c, 0xd1, 0x72, 0x62, 0xcb, 0x5b,
	0x27, 0xd9, 0x24, 0xd5, 0x2e, 0xb4, 0xfe, 0xe9, 0x45, 0xd1, 0x04, 0xb1, 0x05, 0xc5, 0x06, 0x9c,
	0x44, 0xa0, 0xec, 0x16, 0x28, 0x0a, 0x10, 0x23, 0x72, 0x48, 0x8d, 0x4d, 0xce, 0xd0, 0xc3, 0xa1,
	0xa4, 0x0d, 0xd0, 0x77, 0xe8, 0x45, 0x5f, 0xa2, 0x0f, 0xd1, 0xdb, 0xde, 0xf6, 0xf7, 0xa2, 0x8f,
	0x53, 0x9c, 0x33, 0xfc, 0x5d, 0xad, 0x5c, 0xcb, 0x6e, 0xee, 0x38, 0xe7, 0x7c, 0xe7, 0xec, 0xcc,
	0xe1, 0x99, 0xef, 0x7c, 0x5c, 0xb2, 0xab, 0xbc, 0xd8, 0x1f, 0x7b, 0x32, 0x8e, 0xa5, 0x18, 0x2b,
	0x96, 0x48, 0xa5, 0xc7, 0x60, 0x72, 0x93, 0x88, 0x4e, 0x63, 0xaa, 0x99, 0x6b, 0x8c, 0xa3, 0x44,
	0x49, 0x2d, 0x2d, 0x0b, 0x7c, 0x23, 0x03, 0x1f, 0x19, 0xcf, 0xf6, 0x36, 0xa6, 0x48, 0x98, 0x4a,
	0xa5, 0xa0, 0x63, 0xad, 0xc7, 0x59, 0xca, 0x94, 0xc1, 0x6f, 0xdf, 0x9e, 0xf5, 0x15, 0x69, 0x8d,
	0x7f, 0xf0, 0xb7, 0x65, 0xb2, 0x75, 0x98, 0x9b, 0x1c, 0xe6, 0xd1, 0x28, 0x72, 0x30, 0xe9, 0x77,
	0x69, 0x68, 0xdd, 0x24, 0x6d, 0xad, 0xa8, 0xc7, 0x5c, 0xee, 0xdb, 0xad, 0x9d, 0xd6, 0xb0, 0xe3,
	0x2c, 0xe3, 0xfa, 0x99, 0x6f, 0x6d, 0x91, 0x65, 0xc5, 0xde, 0xb8, 0x19, 0xf7, 0xed, 0x85, 0x9d,
	0xd6, 0x70, 0xd5, 0xb9, 0xae, 0xd8, 0x9b, 0x97, 0xdc, 0xb7, 0x2c, 0xb2, 0xa4, 0x79, 0xcc, 0xec,
	0xc5, 0x9d, 0xd6, 0x70, 0xc9, 0xc1, 0x67, 0xeb, 0x0e, 0xe9, 0x2a, 0x4c, 0xed, 0xea, 0x69, 0xc2,
	0xec, 0x25, 0x0c, 0x20, 0xc6, 0xf4, 0x62, 0x9a, 0x30, 0x6b, 0x8f, 0xdc, 0x48, 0x63, 0xf0, 0x27,
	0x52, 0x46, 0x6e, 0x8e, 0x15, 0x59, 0x6c, 0x5f, 0x43, 0xa8, 0x85, 0xce, 0x43, 0x29, 0x23, 0xb3,
	0xc3, 0xef, 0xb3, 0xd8, 0xda, 0x25, 0x1b, 0xc7, 0x3c, 0xbc, 0x10, 0x70, 0x1d, 0x03, 0xfa, 0xc7,
	0x3c, 0x6c, 0xc2, 0xbf, 0x20, 0xeb, 0x39, 0x2a, 0xe0, 0x91, 0x66, 0x0a, 0xc1, 0xcb, 0x08, 0x5e,
	0x33, 0x8e, 0x03, 0xb4, 0x03, 0xf6, 0x0e, 0xe9, 0x1e, 0x67, 0xde, 0x6b, 0xa6, 0x5d, 0x41, 0x63,
	0x66, 0x6f, 0xe2, 0xc9, 0x89, 0x31, 0x7d, 0x4f, 0x63, 0x66, 0x7d, 0x4d, 0x3e, 0x16, 0x52, 0xbb,
	0x29, 0x8d, 0x99, 0x4b, 0x43, 0xe6, 0x86, 0x4a, 0x66, 0x49, 0x3d, 0x71, 0x84, 0x89, 0x6d, 0x21,
	0xf5, 0x11, 0x8d, 0xd9, 0x37, 0x21, 0xfb, 0x16, 0x10, 0xd5, 0x2f, 0xfc, 0x82, 0x58, 0x3e, 0x8b,
	0xf8, 0x29, 0x53, 0xf5, 0xa8, 0xd8, 0xec, 0x3d, 0xf7, 0x54, 0xe8, 0x87, 0x64, 0xeb, 0x98, 0x4d,
	0xa5, 0xf0, 0xdd, 0x88, 0xc7, 0x5c, 0xd7, 0x43, 0x04, 0x86, 0x6c, 0x1a, 0xf7, 0x73, 0xf0, 0x56,
	0x61, 0x0f, 0xc8, 0x16, 0xec, 0xd2, 0xe7, 0x41, 0xe0, 0xa6, 0xec, 0xbc, 0x1e, 0x26, 0x31, 0x6c,
	0x43, 0x48, 0xbd, 0xcf, 0x83, 0xe0, 0x88, 0x9d, 0x57, 0x51, 0xbf, 0x24, 0x36, 0x3d, 0x16, 0x52,
	0xc5, 0x34, 0x72, 0xa1, 0x8d, 0xea, 0x61, 0x09, 0x86, 0xdd, 0x28, 0xfc, 0x2f, 0xd3, 0xfa, 0x2e,
	0xbf, 0x22, 0x1f, 0xa5, 0x74, 0xea, 0x9e, 0x70, 0x37, 0x50, 0xec, 0x4d, 0xc6, 0x84, 0x8e, 0xa6,
	0xf5, 0xe0, 0x37, 0xa6, 0x26, 0x29, 0x9d, 0x3e, 0xe5, 0x07, 0x25, 0xa2, 0x11, 0xef, 0x45, 0x32,
	0x65, 0x6e, 0xc0, 0x85, 0x69, 0xfe, 0xe6, 0x8f, 0x2b, 0x13, 0x8f, 0x98, 0x03, 0x2e, 0xfc, 0x43,
	0x44, 0x54, 0xf1, 0x5f, 0x90, 0xf5, 0x40, 0x46, 0x91, 0x3c, 0xab, 0x07, 0xa5, 0xe6, 0x0d, 0x1b,
	0x47, 0x85, 0xfd, 0x9c, 0xac, 0x4b, 0xe1, 0xc6, 0xdc, 0xab, 0x63, 0x35, 0x62, 0x7b, 0x52, 0x7c,
	0xc7, 0xbd, 0x46, 0xda, 0x78, 0x9a, 0xb2, 0x28, 0xa8, 0x43, 0x33, 0x93, 0xd6, 0x38, 0x2a, 0xec,
	0x3d, 0xd2, 0x13, 0xbc, 0xd1, 0x61, 0xa7, 0x08, 0x5c, 0x11, 0xbc, 0xd6, 0x5e, 0x63, 0xb2, 0xe9,
	0x41, 0xd7, 0xfa, 0xf2, 0x4c, 0xd4, 0xb1, 0x67, 0x88, 0x5d, 0x07, 0xdf, 0xbe, 0x3c, 0x13, 0x55,
	0xc0, 0x2e, 0xd9, 0x90, 0x99, 0x76, 0x7d, 0xe0, 0x82, 0x1a, 0xfe, 0xdc, 0xb4, 0x8b, 0xcc, 0xf4,
	0x3e, 0xd5, 0xac, 0x84, 0x0f, 0xfe, 0xba, 0x44, 0xfa, 0x07, 0x5c, 0x30, 0x87, 0x8a, 0xd7, 0xf0,
	0x8a, 0x9e, 0x89, 0x40, 0x5a, 0x5f, 0x11, 0x82, 0x6f, 0xf3, 0x98, 0xa6, 0xdc, 0xc3, 0xcb, 0xdc,
	0x9d, 0xdc, 0x19, 0x21, 0x97, 0xe4, 0xdc, 0x30, 0xd2, 0x7a, 0x84, 0xbc, 0x01, 0x31, 0x8f, 0x01,
	0xe6, 0x74, 0xb2, 0xe2, 0xd1, 0x7a, 0x4a, 0x56, 0xa4, 0x88, 0xb8, 0x60, 0x6e, 0xaa, 0xa9, 0x66,
	0x78, 0xe9, 0xbb, 0x93, 0x4f, 0x2e, 0xcf, 0xf0, 0x03, 0xa2, 0x8f, 0x00, 0xec, 0x74, 0x65, 0xb5,
	0xb0, 0x46, 0x64, 0xc3, 0xf4, 0x15, 0xa3, 0x3a, 0x53, 0xcc, 0x3d, 0x65, 0x9e, 0x96, 0x0a, 0xf9,
	0xa2, 0xe3, 0xac, 0x83, 0xeb, 0xc0, 0x78, 0x7e, 0x83, 0x0e, 0x6b, 0x93, 0x5c, 0x4b, 0x3d, 0xa9,
	0x0c, 0x6d, 0x2c, 0x38, 0x66, 0x61, 0x7d, 0x4b, 0x56, 0x4b, 0x7e, 0x84, 0x18, 0x64, 0x8a, 0xee,
	0x64, 0x30, 0x7f, 0x43, 0x05, 0xc1, 0xc1, 0xc6, 0x9c, 0x95, 0xa4, 0xb6, 0xb2, 0xf6, 0xc9, 0x0a,
	0x6e, 0x47, 0x06, 0x01, 0xec, 0x11, 0x09, 0xa4, 0x3b, 0xb9, 0xfb, 0x96, 0x83, 0x19, 0xa0, 0xd3,
	0xcd, 0xaa, 0x85, 0x75, 0x48, 0x56, 0x8a, 0x0b, 0xcd, 0x45, 0x20, 0x91, 0x59, 0xba, 0x93, 0xdd,
	0x0b, 0x59, 0x4a, 0xf2, 0x2d, 0x76, 0xb4, 0x6f, 0xa2, 0xe0, 0x1d, 0x39, 0x5d, 0xbf, 0x5a, 0x58,
	0xbf, 0x22, 0x58, 0x7d, 0x17, 0x8f, 0xde, 0xc6, 0x74, 0xb7, 0x2f, 0xdf, 0xd4, 0x13, 0xa9, 0x98,
	0xd3, 0xce, 0xf2, 0x27, 0x38, 0x94, 0x77, 0x42, 0x85, 0x60, 0xe6, 0x0e, 0xdb, 0x9d, 0xb7, 0x1d,
	0xea, 0x89, 0x41, 0x62, 0x6d, 0xba, 0x5e, 0xb5, 0x18, 0x9c, 0x13, 0xbb, 0xe8, 0xa3, 0x67, 0x9a,
	0xc5, 0x45, 0x2f, 0x3d, 0xe7, 0xa9, 0xb6, 0x7e, 0x4f, 0xec, 0x00, 0xba, 0x41, 0x51, 0xf1, 0xda,
	0xf0, 0x04, 0x9c, 0xdb, 0x8d, 0x78, 0xaa, 0xed, 0xd6, 0xce, 0xe2, 0xb0, 0x3b, 0xb9, 0x37, 0xba,
	0x38, 0xa9, 0x46, 0xb3, 0x7d, 0xe9, 0x6c, 0x06, 0x33, 0x16, 0xc8, 0x3e, 0xf8, 0x4f, 0x8b, 0xdc,
	0x2c, 0x2a, 0x54, 0x84, 0xfc, 0xff, 0xc7, 0xd2, 0x2d, 0xd2, 0x49, 0xa5, 0xd2, 0x66, 0x28, 0x9d,
	0x20, 0xbc, 0x0d, 0x06, 0x1c, 0x49, 0x43, 0xd2, 0xc7, 0x4c, 0xb5, 0x56, 0xb5, 0x39, 0xfe, 0x58,
	0x0f, 0x52, 0x56, 0x6d, 0x0a, 0x64, 0xce, 0x35, 0x8b, 0x1b, 0xd0, 0xd4, 0x7e, 0x85, 0xd8, 0x3e,
	0xcf, 0x0b, 0x97, 0x83, 0xd3, 0xc1, 0x5f, 0x16, 0xea, 0xf3, 0xf6, 0xa7, 0x39, 0xd8, 0x88, 0x6c,
	0xe0, 0x6c, 0xc2, 0xab, 0x62, 0xf6, 0x05, 0x84, 0xf1, 0xf7, 0x96, 0x61, 0x0c, 0xf0, 0x1d, 0x81,
	0x0b, 0x36, 0x06, 0x04, 0x33, 0x24, 0x6b, 0x69, 0x96, 0x30, 0xc5, 0xa5, 0x72, 0x15, 0xa3, 0xa9,
	0x14, 0xf6, 0x3f, 0x0c, 0xb6, 0x57, 0xd8, 0x1d, 0x34, 0x5b, 0x8f, 0x60, 0x14, 0xa5, 0xda, 0xe4,
	0x2c, 0x3a, 0x1e, 0x7e, 0x33, 0xb5, 0xff, 0xd9, 0x2a, 0x66, 0x51, 0xaa, 0x21, 0x71, 0xde, 0xd9,
	0x2f, 0xc0, 0x09, 0x35, 0xaa, 0xe2, 0x14, 0x0b, 0x31, 0xc6, 0xfe, 0x97, 0x09, 0x59, 0x2b, 0x42,
	0x1c, 0x16, 0x02, 0xdc, 0xda, 0x26, 0x6d, 0x53, 0x51, 0xee, 0xdb, 0xff, 0x36, 0x98, 0x65, 0x2c,
	0x24, 0xf7, 0x07, 0x7f, 0xee, 0x55, 0xad, 0xe1, 0x64, 0x11, 0x33, 0xd5, 0x3b, 0x88, 0x28, 0x56,
	0x70, 0x46, 0x69, 0xb4, 0xde, 0x5d, 0x69, 0x2c, 0x5c, 0x55, 0x69, 0x2c, 0x5e, 0xa2, 0x34, 0xfe,
	0xa7, 0x38, 0x58, 0x7a, 0x2f, 0x71, 0x70, 0xed, 0xea, 0xe2, 0xe0, 0xfa, 0xfb, 0x89, 0x83, 0xe5,
	0xf7, 0x13, 0x07, 0xed, 0x0f, 0x11, 0x07, 0x9d, 0x0f, 0x14, 0x07, 0xe4, 0x7d, 0xc4, 0x41, 0xf7,
	0x0a, 0xe2, 0x60, 0xe5, 0xdd, 0xc5, 0xc1, 0xea, 0x7c, 0x71, 0xf0, 0xd9, 0xc5, 0x4b, 0xd6, 0x9b,
	0x7b, 0xc7, 0x76, 0xc8, 0x4a, 0xc9, 0x3c, 0x70, 0x03, 0xd6, 0x8a, 0x26, 0x46, 0xd6, 0x81, 0x3b,
	0x5f, 0x47, 0xa4, 0xec, 0xdc, 0xee, 0x37, 0x10, 0x47, 0xec, 0xbc, 0x81, 0xa0, 0x21, 0xb3, 0xd7,
	0x1b, 0x88, 0x6f, 0x42, 0x66, 0x0d, 0xc8, 0x6a, 0x89, 0xf0, 0xb8, 0x9e, 0xda, 0x16, 0x12, 0x4e,
	0x37, 0x87, 0x3c, 0xe1, 0x7a, 0x0a, 0x95, 0x28, 0x31, 0x9a, 0x86, 0x86, 0xdd, 0x37, 0x1a, 0x24,
	0xf8, 0x82, 0x86, 0x38, 0x0f, 0x76, 0xc9, 0x46, 0x09, 0x3d, 0xfb, 0x51, 0x4d, 0xdd, 0x88, 0x9d,
	0xb2, 0x28, 0xd7, 0xce, 0xfd, 0x1c, 0xfc, 0xdb, 0x1f, 0xd5, 0xf4, 0x39, 0xd8, 0x1b, 0xf0, 0x93,
	0xe4, 0x55, 0x01, 0xbf, 0xd1, 0x80, 0x3f, 0x4d, 0x5e, 0xe5, 0xf0, 0x01, 0x59, 0xad, 0x28, 0x16,
	0x6a, 0xf2, 0x33, 0x3c, 0x4f, 0xb7, 0x60, 0x57, 0x28, 0x4a, 0x03, 0x03, 0x55, 0xd9, 0x6a, 0x62,
	0xa0, 0x2c, 0x0d, 0x0c, 0xd4, 0xc5, 0x6e, 0x62, 0xa0, 0x30, 0xf7, 0x48, 0xaf, 0xc2, 0x60, 0x65,
	0x6e, 0xe2, 0xae, 0x56, 0x0a, 0x10, 0x96, 0xe6, 0xcb, 0x3a, 0xe9, 0x97, 0xb5, 0xd9, 0x46, 0xe4,
	0x5a, 0x81, 0x2c, 0x8a, 0x33, 0x26, 0x9b, 0x15, 0xb8, 0x56, 0x9d, 0x5b, 0x46, 0xf3, 0x14, 0xf0,
	0xaa, 0x3c, 0x8d, 0x80, 0x5a, 0x7d, 0x3e, 0x6a, 0x06, 0x54, 0x05, 0x2a, 0x45, 0xd2, 0xc7, 0x75,
	0x91, 0x74, 0xb7, 0x52, 0x25, 0xc8, 0xb7, 0xb7, 0x71, 0x46, 0x14, 0x32, 0x03, 0xa9, 0x76, 0x77,
	0xfe, 0xa8, 0xb8, 0x73, 0xc9, 0xa4, 0x78, 0x78, 0x39, 0xff, 0xef, 0xbc, 0x85, 0xfe, 0xbf, 0x9c,
	0x4b, 0xff, 0x77, 0xe7, 0xb3, 0x7f, 0x63, 0x2c, 0x0f, 0x66, 0xc6, 0xf2, 0x45, 0x89, 0xfd, 0xf3,
	0x2b, 0x48, 0xec, 0x7b, 0x57, 0x94, 0xd8, 0x9f, 0xcc, 0x97, 0xd8, 0xf3, 0xbf, 0x26, 0x3f, 0x9d,
	0xff, 0x35, 0x39, 0x24, 0x7d, 0x73, 0x6c, 0x7a, 0x56, 0x0a, 0x89, 0xcf, 0xcc, 0x1d, 0x02, 0xbb,
	0x43, 0xcf, 0x0a, 0x21, 0x71, 0x89, 0x32, 0x1e, 0x5e, 0xa6, 0x8c, 0x67, 0xbe, 0x53, 0x3f, 0x9f,
	0xfd, 0x4e, 0x1d, 0xfc, 0xb1, 0x45, 0x7a, 0x2f, 0x9b, 0xbf, 0xf1, 0x6b, 0xd2, 0x2e, 0x2e, 0x5e,
	0xfe, 0x15, 0x30, 0x98, 0xa7, 0xd3, 0xf0, 0x32, 0x44, 0xd1, 0xa1, 0x92, 0x01, 0x8f, 0x98, 0xb3,
	0x9c, 0xdf, 0x48, 0xeb, 0x6b, 0xd2, 0x29, 0x1b, 0x33, 0xff, 0x06, 0x78, 0x97, 0xf8, 0x76, 0xd1,
	0xb1, 0x83, 0x3f, 0x2d, 0x9a, 0x2d, 0x55, 0x4e, 0xab, 0x4f, 0x16, 0xb3, 0x5c, 0xf0, 0xac, 0x3a,
	0xf0, 0x08, 0x3a, 0xa8, 0x6c, 0x92, 0x05, 0xec, 0xd9, 0x65, 0x95, 0x37, 0x47, 0x9f, 0x2c, 0xc2,
	0xdd, 0x36, 0xc3, 0x17, 0x1e, 0x41, 0x2c, 0x1c, 0x73, 0xa5, 0x4f, 0x7c, 0x3a, 0xc5, 0xd1, 0xda,
	0x71, 0xca, 0x35, 0xa0, 0xe1, 0x96, 0x9b, 0xd9, 0x09, 0x8f, 0xd0, 0x5c, 0xe5, 0x50, 0xce, 0x07,
	0x64, 0x9b, 0xe6, 0x23, 0xd8, 0xda, 0x20, 0xd7, 0xe0, 0x2a, 0x4f, 0x70, 0x04, 0x76, 0x9c, 0x25,
	0x4d, 0xc3, 0x49, 0x61, 0x7c, 0x80, 0x03, 0xce, 0x18, 0x1f, 0x58, 0x77, 0xc9, 0x2a, 0x1a, 0xdd,
	0xbd, 0xfb, 0xae, 0x4c, 0xf4, 0x1e, 0x0e, 0xb0, 0x8e, 0x43, 0xc0, 0xb9, 0x77, 0xff, 0x87, 0x44,
	0xef, 0xd5, 0x20, 0x8f, 0x0c, 0x84, 0xd4, 0x20, 0x8f, 0x66, 0x20, 0x98, 0x65, 0x82, 0x13, 0xa9,
	0x9e, 0x65, 0x32, 0x9b, 0x65, 0x82, 0x83, 0xa8, 0x9e, 0x65, 0x02, 0x05, 0x48, 0x22, 0xaa, 0x03,
	0xa9, 0x8a, 0xd9, 0x53, 0xae, 0xad, 0x4f, 0xc9, 0x5a, 0x44, 0x53, 0xed, 0x46, 0x32, 0xe4, 0xc2,
	0xb0, 0x59, 0x0f, 0x13, 0xac, 0x82, 0xf9, 0x39, 0x58, 0x81, 0xce, 0x1e, 0xff, 0x81, 0x0c, 0x3d,
	0x19, 0x8f, 0xb4, 0x9e, 0x66, 0x53, 0x2e, 0x1a, 0x6f, 0xd4, 0xa7, 0x9a, 0x8e, 0x62, 0xe9, 0xb3,
	0xc8, 0xfc, 0x63, 0xf4, 0xf8, 0xd6, 0x25, 0xf2, 0x0b, 0x20, 0xbf, 0x7b, 0x10, 0xca, 0x88, 0x8a,
	0x70, 0xf4, 0x70, 0xa2, 0x35, 0x64, 0x18, 0x63, 0x8c, 0x27, 0xa3, 0x71, 0xca, 0xd4, 0x29, 0xf7,
	0x58, 0x3a, 0xbe, 0xf8, 0x7f, 0xd7, 0xf1, 0x75, 0x44, 0xdd, 0xff, 0x6f, 0x00, 0x00, 0x00, 0xff,
	0xff, 0xcd, 0x60, 0x3a, 0xd5, 0x0c, 0x13, 0x00, 0x00,
}
