// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/recall/recall_index_es.proto

package recall_index_es // import "golang.52tt.com/protocol/services/rcmd/recall_index_es"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import persona "golang.52tt.com/protocol/services/rcmd/persona"
import recall_common "golang.52tt.com/protocol/services/rcmd/recall_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RecallStrategy int32

const (
	RecallStrategy_UnknownRecallStrategy RecallStrategy = 0
	RecallStrategy_RecallQueueStrategy   RecallStrategy = 1
)

var RecallStrategy_name = map[int32]string{
	0: "UnknownRecallStrategy",
	1: "RecallQueueStrategy",
}
var RecallStrategy_value = map[string]int32{
	"UnknownRecallStrategy": 0,
	"RecallQueueStrategy":   1,
}

func (x RecallStrategy) String() string {
	return proto.EnumName(RecallStrategy_name, int32(x))
}
func (RecallStrategy) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{0}
}

type FieldValueType int32

const (
	FieldValueType_StringFieldValue FieldValueType = 0
	FieldValueType_StructFieldValue FieldValueType = 1
	FieldValueType_ArrayFieldValue  FieldValueType = 2
)

var FieldValueType_name = map[int32]string{
	0: "StringFieldValue",
	1: "StructFieldValue",
	2: "ArrayFieldValue",
}
var FieldValueType_value = map[string]int32{
	"StringFieldValue": 0,
	"StructFieldValue": 1,
	"ArrayFieldValue":  2,
}

func (x FieldValueType) String() string {
	return proto.EnumName(FieldValueType_name, int32(x))
}
func (FieldValueType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{1}
}

type ItemIdType int32

const (
	ItemIdType_Uint32ItemId ItemIdType = 0
	ItemIdType_StringItemId ItemIdType = 1
)

var ItemIdType_name = map[int32]string{
	0: "Uint32ItemId",
	1: "StringItemId",
}
var ItemIdType_value = map[string]int32{
	"Uint32ItemId": 0,
	"StringItemId": 1,
}

func (x ItemIdType) String() string {
	return proto.EnumName(ItemIdType_name, int32(x))
}
func (ItemIdType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{2}
}

type ItemsStorage int32

const (
	ItemsStorage_RedisSet  ItemsStorage = 0
	ItemsStorage_RedisList ItemsStorage = 1
)

var ItemsStorage_name = map[int32]string{
	0: "RedisSet",
	1: "RedisList",
}
var ItemsStorage_value = map[string]int32{
	"RedisSet":  0,
	"RedisList": 1,
}

func (x ItemsStorage) String() string {
	return proto.EnumName(ItemsStorage_name, int32(x))
}
func (ItemsStorage) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{3}
}

// 这里的枚举目前只是用来做监控
type EventId int32

const (
	EventId_Invalid             EventId = 0
	EventId_PostCreate          EventId = 1
	EventId_PostDelete          EventId = 2
	EventId_PostPrivate         EventId = 3
	EventId_PostExpose          EventId = 4
	EventId_PostAttitude        EventId = 5
	EventId_PostComment         EventId = 6
	EventId_PostOwnerReply      EventId = 7
	EventId_PoolChange          EventId = 8
	EventId_PostBadScoreChange  EventId = 9
	EventId_userChatCardPublish EventId = 10
	EventId_userChatCardClose   EventId = 11
	EventId_LiveChannelChange   EventId = 12
	EventId_SingChannelChannel  EventId = 13
	EventId_PostBadLevelChange  EventId = 14
)

var EventId_name = map[int32]string{
	0:  "Invalid",
	1:  "PostCreate",
	2:  "PostDelete",
	3:  "PostPrivate",
	4:  "PostExpose",
	5:  "PostAttitude",
	6:  "PostComment",
	7:  "PostOwnerReply",
	8:  "PoolChange",
	9:  "PostBadScoreChange",
	10: "userChatCardPublish",
	11: "userChatCardClose",
	12: "LiveChannelChange",
	13: "SingChannelChannel",
	14: "PostBadLevelChange",
}
var EventId_value = map[string]int32{
	"Invalid":             0,
	"PostCreate":          1,
	"PostDelete":          2,
	"PostPrivate":         3,
	"PostExpose":          4,
	"PostAttitude":        5,
	"PostComment":         6,
	"PostOwnerReply":      7,
	"PoolChange":          8,
	"PostBadScoreChange":  9,
	"userChatCardPublish": 10,
	"userChatCardClose":   11,
	"LiveChannelChange":   12,
	"SingChannelChannel":  13,
	"PostBadLevelChange":  14,
}

func (x EventId) String() string {
	return proto.EnumName(EventId_name, int32(x))
}
func (EventId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{4}
}

type SetKey int32

const (
	SetKey_SetKeyInvalid  SetKey = 0
	SetKey_ExposeWhite    SetKey = 1
	SetKey_ExposeUidWhite SetKey = 2
	SetKey_BlackPost      SetKey = 3
	SetKey_PrivatePost    SetKey = 4
	SetKey_IsMan          SetKey = 5
	SetKey_IsWoman        SetKey = 6
)

var SetKey_name = map[int32]string{
	0: "SetKeyInvalid",
	1: "ExposeWhite",
	2: "ExposeUidWhite",
	3: "BlackPost",
	4: "PrivatePost",
	5: "IsMan",
	6: "IsWoman",
}
var SetKey_value = map[string]int32{
	"SetKeyInvalid":  0,
	"ExposeWhite":    1,
	"ExposeUidWhite": 2,
	"BlackPost":      3,
	"PrivatePost":    4,
	"IsMan":          5,
	"IsWoman":        6,
}

func (x SetKey) String() string {
	return proto.EnumName(SetKey_name, int32(x))
}
func (SetKey) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{5}
}

type RangeSetKey int32

const (
	RangeSetKey_RangeSetKeyInvalid   RangeSetKey = 0
	RangeSetKey_RcmdPostStreamExpose RangeSetKey = 1
)

var RangeSetKey_name = map[int32]string{
	0: "RangeSetKeyInvalid",
	1: "RcmdPostStreamExpose",
}
var RangeSetKey_value = map[string]int32{
	"RangeSetKeyInvalid":   0,
	"RcmdPostStreamExpose": 1,
}

func (x RangeSetKey) String() string {
	return proto.EnumName(RangeSetKey_name, int32(x))
}
func (RangeSetKey) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{6}
}

type Op int32

const (
	Op_Op_Invalid Op = 0
	Op_Op_Add     Op = 1
	Op_Op_Del     Op = 2
	Op_Op_Incr    Op = 3
)

var Op_name = map[int32]string{
	0: "Op_Invalid",
	1: "Op_Add",
	2: "Op_Del",
	3: "Op_Incr",
}
var Op_value = map[string]int32{
	"Op_Invalid": 0,
	"Op_Add":     1,
	"Op_Del":     2,
	"Op_Incr":    3,
}

func (x Op) String() string {
	return proto.EnumName(Op_name, int32(x))
}
func (Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{7}
}

type BloomKey int32

const (
	BloomKey_BloomKeyInvalid    BloomKey = 0
	BloomKey_UgcRecommendPost   BloomKey = 1
	BloomKey_ChatCardDeliver    BloomKey = 2
	BloomKey_LiveChannelDeliver BloomKey = 3
	BloomKey_SingChannelDeliver BloomKey = 4
)

var BloomKey_name = map[int32]string{
	0: "BloomKeyInvalid",
	1: "UgcRecommendPost",
	2: "ChatCardDeliver",
	3: "LiveChannelDeliver",
	4: "SingChannelDeliver",
}
var BloomKey_value = map[string]int32{
	"BloomKeyInvalid":    0,
	"UgcRecommendPost":   1,
	"ChatCardDeliver":    2,
	"LiveChannelDeliver": 3,
	"SingChannelDeliver": 4,
}

func (x BloomKey) String() string {
	return proto.EnumName(BloomKey_name, int32(x))
}
func (BloomKey) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{8}
}

type Relation int32

const (
	Relation_Equal       Relation = 0
	Relation_LowerThan   Relation = 1
	Relation_GreaterThan Relation = 2
)

var Relation_name = map[int32]string{
	0: "Equal",
	1: "LowerThan",
	2: "GreaterThan",
}
var Relation_value = map[string]int32{
	"Equal":       0,
	"LowerThan":   1,
	"GreaterThan": 2,
}

func (x Relation) String() string {
	return proto.EnumName(Relation_name, int32(x))
}
func (Relation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{9}
}

type ParamType int32

const (
	ParamType_UnknownParamType ParamType = 0
	ParamType_StringParam      ParamType = 1
	ParamType_IntParam         ParamType = 2
)

var ParamType_name = map[int32]string{
	0: "UnknownParamType",
	1: "StringParam",
	2: "IntParam",
}
var ParamType_value = map[string]int32{
	"UnknownParamType": 0,
	"StringParam":      1,
	"IntParam":         2,
}

func (x ParamType) String() string {
	return proto.EnumName(ParamType_name, int32(x))
}
func (ParamType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{10}
}

type ReduceType int32

const (
	ReduceType_UnknownReduceType ReduceType = 0
	ReduceType_ReduceBySort      ReduceType = 1
	ReduceType_ReduceByRandom    ReduceType = 2
)

var ReduceType_name = map[int32]string{
	0: "UnknownReduceType",
	1: "ReduceBySort",
	2: "ReduceByRandom",
}
var ReduceType_value = map[string]int32{
	"UnknownReduceType": 0,
	"ReduceBySort":      1,
	"ReduceByRandom":    2,
}

func (x ReduceType) String() string {
	return proto.EnumName(ReduceType_name, int32(x))
}
func (ReduceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{11}
}

type RecallByActionReq struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               string           `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ProfileIds           []*ReqActionItem `protobuf:"bytes,3,rep,name=profile_ids,json=profileIds,proto3" json:"profile_ids,omitempty"`
	Limit                uint32           `protobuf:"varint,4,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *RecallByActionReq) Reset()         { *m = RecallByActionReq{} }
func (m *RecallByActionReq) String() string { return proto.CompactTextString(m) }
func (*RecallByActionReq) ProtoMessage()    {}
func (*RecallByActionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{0}
}
func (m *RecallByActionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallByActionReq.Unmarshal(m, b)
}
func (m *RecallByActionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallByActionReq.Marshal(b, m, deterministic)
}
func (dst *RecallByActionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallByActionReq.Merge(dst, src)
}
func (m *RecallByActionReq) XXX_Size() int {
	return xxx_messageInfo_RecallByActionReq.Size(m)
}
func (m *RecallByActionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallByActionReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecallByActionReq proto.InternalMessageInfo

func (m *RecallByActionReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecallByActionReq) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *RecallByActionReq) GetProfileIds() []*ReqActionItem {
	if m != nil {
		return m.ProfileIds
	}
	return nil
}

func (m *RecallByActionReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type ReqActionItem struct {
	ProfileId            uint32                     `protobuf:"varint,1,opt,name=profile_id,json=profileId,proto3" json:"profile_id,omitempty"`
	FilterChain          *FilterBoxChain            `protobuf:"bytes,2,opt,name=filter_chain,json=filterChain,proto3" json:"filter_chain,omitempty"`
	BloomKey             BloomKey                   `protobuf:"varint,3,opt,name=bloom_key,json=bloomKey,proto3,enum=rcmd.recall_index_es.BloomKey" json:"bloom_key,omitempty"`
	MetaData             *common.RecallMetaData     `protobuf:"bytes,4,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	Formula              common.RecallActionFormula `protobuf:"varint,5,opt,name=formula,proto3,enum=rcmd.common.RecallActionFormula" json:"formula,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *ReqActionItem) Reset()         { *m = ReqActionItem{} }
func (m *ReqActionItem) String() string { return proto.CompactTextString(m) }
func (*ReqActionItem) ProtoMessage()    {}
func (*ReqActionItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{1}
}
func (m *ReqActionItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReqActionItem.Unmarshal(m, b)
}
func (m *ReqActionItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReqActionItem.Marshal(b, m, deterministic)
}
func (dst *ReqActionItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReqActionItem.Merge(dst, src)
}
func (m *ReqActionItem) XXX_Size() int {
	return xxx_messageInfo_ReqActionItem.Size(m)
}
func (m *ReqActionItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ReqActionItem.DiscardUnknown(m)
}

var xxx_messageInfo_ReqActionItem proto.InternalMessageInfo

func (m *ReqActionItem) GetProfileId() uint32 {
	if m != nil {
		return m.ProfileId
	}
	return 0
}

func (m *ReqActionItem) GetFilterChain() *FilterBoxChain {
	if m != nil {
		return m.FilterChain
	}
	return nil
}

func (m *ReqActionItem) GetBloomKey() BloomKey {
	if m != nil {
		return m.BloomKey
	}
	return BloomKey_BloomKeyInvalid
}

func (m *ReqActionItem) GetMetaData() *common.RecallMetaData {
	if m != nil {
		return m.MetaData
	}
	return nil
}

func (m *ReqActionItem) GetFormula() common.RecallActionFormula {
	if m != nil {
		return m.Formula
	}
	return common.RecallActionFormula_UnknownFormula
}

type RecallByActionRsp struct {
	Items                []*AlgoRecallItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *RecallByActionRsp) Reset()         { *m = RecallByActionRsp{} }
func (m *RecallByActionRsp) String() string { return proto.CompactTextString(m) }
func (*RecallByActionRsp) ProtoMessage()    {}
func (*RecallByActionRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{2}
}
func (m *RecallByActionRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallByActionRsp.Unmarshal(m, b)
}
func (m *RecallByActionRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallByActionRsp.Marshal(b, m, deterministic)
}
func (dst *RecallByActionRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallByActionRsp.Merge(dst, src)
}
func (m *RecallByActionRsp) XXX_Size() int {
	return xxx_messageInfo_RecallByActionRsp.Size(m)
}
func (m *RecallByActionRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallByActionRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RecallByActionRsp proto.InternalMessageInfo

func (m *RecallByActionRsp) GetItems() []*AlgoRecallItem {
	if m != nil {
		return m.Items
	}
	return nil
}

type AlgoRecallItem struct {
	ItemId               string   `protobuf:"bytes,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Score                float32  `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	RecommendType        []string `protobuf:"bytes,3,rep,name=recommend_type,json=recommendType,proto3" json:"recommend_type,omitempty"`
	ItemOwnerUid         uint32   `protobuf:"varint,4,opt,name=item_owner_uid,json=itemOwnerUid,proto3" json:"item_owner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AlgoRecallItem) Reset()         { *m = AlgoRecallItem{} }
func (m *AlgoRecallItem) String() string { return proto.CompactTextString(m) }
func (*AlgoRecallItem) ProtoMessage()    {}
func (*AlgoRecallItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{3}
}
func (m *AlgoRecallItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AlgoRecallItem.Unmarshal(m, b)
}
func (m *AlgoRecallItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AlgoRecallItem.Marshal(b, m, deterministic)
}
func (dst *AlgoRecallItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AlgoRecallItem.Merge(dst, src)
}
func (m *AlgoRecallItem) XXX_Size() int {
	return xxx_messageInfo_AlgoRecallItem.Size(m)
}
func (m *AlgoRecallItem) XXX_DiscardUnknown() {
	xxx_messageInfo_AlgoRecallItem.DiscardUnknown(m)
}

var xxx_messageInfo_AlgoRecallItem proto.InternalMessageInfo

func (m *AlgoRecallItem) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *AlgoRecallItem) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *AlgoRecallItem) GetRecommendType() []string {
	if m != nil {
		return m.RecommendType
	}
	return nil
}

func (m *AlgoRecallItem) GetItemOwnerUid() uint32 {
	if m != nil {
		return m.ItemOwnerUid
	}
	return 0
}

type RecallReq struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SearchReq            *SearchReq             `protobuf:"bytes,2,opt,name=search_req,json=searchReq,proto3" json:"search_req,omitempty"`
	JoinFilter           *JoinFilter            `protobuf:"bytes,3,opt,name=join_filter,json=joinFilter,proto3" json:"join_filter,omitempty"`
	BloomFilter          BloomKey               `protobuf:"varint,4,opt,name=bloom_filter,json=bloomFilter,proto3,enum=rcmd.recall_index_es.BloomKey" json:"bloom_filter,omitempty"`
	RecallStrategy       RecallStrategy         `protobuf:"varint,5,opt,name=recall_strategy,json=recallStrategy,proto3,enum=rcmd.recall_index_es.RecallStrategy" json:"recall_strategy,omitempty"`
	MetaData             *common.RecallMetaData `protobuf:"bytes,6,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RecallReq) Reset()         { *m = RecallReq{} }
func (m *RecallReq) String() string { return proto.CompactTextString(m) }
func (*RecallReq) ProtoMessage()    {}
func (*RecallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{4}
}
func (m *RecallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallReq.Unmarshal(m, b)
}
func (m *RecallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallReq.Marshal(b, m, deterministic)
}
func (dst *RecallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallReq.Merge(dst, src)
}
func (m *RecallReq) XXX_Size() int {
	return xxx_messageInfo_RecallReq.Size(m)
}
func (m *RecallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecallReq proto.InternalMessageInfo

func (m *RecallReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecallReq) GetSearchReq() *SearchReq {
	if m != nil {
		return m.SearchReq
	}
	return nil
}

func (m *RecallReq) GetJoinFilter() *JoinFilter {
	if m != nil {
		return m.JoinFilter
	}
	return nil
}

func (m *RecallReq) GetBloomFilter() BloomKey {
	if m != nil {
		return m.BloomFilter
	}
	return BloomKey_BloomKeyInvalid
}

func (m *RecallReq) GetRecallStrategy() RecallStrategy {
	if m != nil {
		return m.RecallStrategy
	}
	return RecallStrategy_UnknownRecallStrategy
}

func (m *RecallReq) GetMetaData() *common.RecallMetaData {
	if m != nil {
		return m.MetaData
	}
	return nil
}

type JoinFilter struct {
	FilterChain          *FilterBoxChain `protobuf:"bytes,1,opt,name=filter_chain,json=filterChain,proto3" json:"filter_chain,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *JoinFilter) Reset()         { *m = JoinFilter{} }
func (m *JoinFilter) String() string { return proto.CompactTextString(m) }
func (*JoinFilter) ProtoMessage()    {}
func (*JoinFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{5}
}
func (m *JoinFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_JoinFilter.Unmarshal(m, b)
}
func (m *JoinFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_JoinFilter.Marshal(b, m, deterministic)
}
func (dst *JoinFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_JoinFilter.Merge(dst, src)
}
func (m *JoinFilter) XXX_Size() int {
	return xxx_messageInfo_JoinFilter.Size(m)
}
func (m *JoinFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_JoinFilter.DiscardUnknown(m)
}

var xxx_messageInfo_JoinFilter proto.InternalMessageInfo

func (m *JoinFilter) GetFilterChain() *FilterBoxChain {
	if m != nil {
		return m.FilterChain
	}
	return nil
}

type Rsp struct {
	Json string `protobuf:"bytes,1,opt,name=json,proto3" json:"json,omitempty"`
	// es的返回条数，仅Recall接口
	EsCount uint32 `protobuf:"varint,2,opt,name=es_count,json=esCount,proto3" json:"es_count,omitempty"`
	// es的search after字段， 会返回es最后一个item的sort[0]字段，仅Recall接口
	SearchAfter          string   `protobuf:"bytes,3,opt,name=search_after,json=searchAfter,proto3" json:"search_after,omitempty"`
	IdList               []string `protobuf:"bytes,4,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Rsp) Reset()         { *m = Rsp{} }
func (m *Rsp) String() string { return proto.CompactTextString(m) }
func (*Rsp) ProtoMessage()    {}
func (*Rsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{6}
}
func (m *Rsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Rsp.Unmarshal(m, b)
}
func (m *Rsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Rsp.Marshal(b, m, deterministic)
}
func (dst *Rsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Rsp.Merge(dst, src)
}
func (m *Rsp) XXX_Size() int {
	return xxx_messageInfo_Rsp.Size(m)
}
func (m *Rsp) XXX_DiscardUnknown() {
	xxx_messageInfo_Rsp.DiscardUnknown(m)
}

var xxx_messageInfo_Rsp proto.InternalMessageInfo

func (m *Rsp) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *Rsp) GetEsCount() uint32 {
	if m != nil {
		return m.EsCount
	}
	return 0
}

func (m *Rsp) GetSearchAfter() string {
	if m != nil {
		return m.SearchAfter
	}
	return ""
}

func (m *Rsp) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type Label struct {
	Key                  recall_common.Key `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_common.Key" json:"key,omitempty"`
	Value                string            `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Label) Reset()         { *m = Label{} }
func (m *Label) String() string { return proto.CompactTextString(m) }
func (*Label) ProtoMessage()    {}
func (*Label) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{7}
}
func (m *Label) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Label.Unmarshal(m, b)
}
func (m *Label) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Label.Marshal(b, m, deterministic)
}
func (dst *Label) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Label.Merge(dst, src)
}
func (m *Label) XXX_Size() int {
	return xxx_messageInfo_Label.Size(m)
}
func (m *Label) XXX_DiscardUnknown() {
	xxx_messageInfo_Label.DiscardUnknown(m)
}

var xxx_messageInfo_Label proto.InternalMessageInfo

func (m *Label) GetKey() recall_common.Key {
	if m != nil {
		return m.Key
	}
	return recall_common.Key_Invalid_Key
}

func (m *Label) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type RecallByKeyReq struct {
	App                  recall_common.App      `protobuf:"varint,1,opt,name=app,proto3,enum=rcmd.recall_common.App" json:"app,omitempty"`
	Item                 recall_common.Item     `protobuf:"varint,2,opt,name=item,proto3,enum=rcmd.recall_common.Item" json:"item,omitempty"`
	Labels               []*Label               `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty"`
	ItemIdType           ItemIdType             `protobuf:"varint,4,opt,name=item_id_type,json=itemIdType,proto3,enum=rcmd.recall_index_es.ItemIdType" json:"item_id_type,omitempty"`
	Storage              ItemsStorage           `protobuf:"varint,5,opt,name=storage,proto3,enum=rcmd.recall_index_es.ItemsStorage" json:"storage,omitempty"`
	FilterChain          *FilterBoxChain        `protobuf:"bytes,6,opt,name=filter_chain,json=filterChain,proto3" json:"filter_chain,omitempty"`
	Uid                  uint32                 `protobuf:"varint,7,opt,name=uid,proto3" json:"uid,omitempty"`
	BloomKey             BloomKey               `protobuf:"varint,8,opt,name=bloom_key,json=bloomKey,proto3,enum=rcmd.recall_index_es.BloomKey" json:"bloom_key,omitempty"`
	MetaData             *common.RecallMetaData `protobuf:"bytes,9,opt,name=meta_data,json=metaData,proto3" json:"meta_data,omitempty"`
	Fields               []string               `protobuf:"bytes,10,rep,name=fields,proto3" json:"fields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RecallByKeyReq) Reset()         { *m = RecallByKeyReq{} }
func (m *RecallByKeyReq) String() string { return proto.CompactTextString(m) }
func (*RecallByKeyReq) ProtoMessage()    {}
func (*RecallByKeyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{8}
}
func (m *RecallByKeyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallByKeyReq.Unmarshal(m, b)
}
func (m *RecallByKeyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallByKeyReq.Marshal(b, m, deterministic)
}
func (dst *RecallByKeyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallByKeyReq.Merge(dst, src)
}
func (m *RecallByKeyReq) XXX_Size() int {
	return xxx_messageInfo_RecallByKeyReq.Size(m)
}
func (m *RecallByKeyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallByKeyReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecallByKeyReq proto.InternalMessageInfo

func (m *RecallByKeyReq) GetApp() recall_common.App {
	if m != nil {
		return m.App
	}
	return recall_common.App_Invalid_App
}

func (m *RecallByKeyReq) GetItem() recall_common.Item {
	if m != nil {
		return m.Item
	}
	return recall_common.Item_Invalid_Item
}

func (m *RecallByKeyReq) GetLabels() []*Label {
	if m != nil {
		return m.Labels
	}
	return nil
}

func (m *RecallByKeyReq) GetItemIdType() ItemIdType {
	if m != nil {
		return m.ItemIdType
	}
	return ItemIdType_Uint32ItemId
}

func (m *RecallByKeyReq) GetStorage() ItemsStorage {
	if m != nil {
		return m.Storage
	}
	return ItemsStorage_RedisSet
}

func (m *RecallByKeyReq) GetFilterChain() *FilterBoxChain {
	if m != nil {
		return m.FilterChain
	}
	return nil
}

func (m *RecallByKeyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RecallByKeyReq) GetBloomKey() BloomKey {
	if m != nil {
		return m.BloomKey
	}
	return BloomKey_BloomKeyInvalid
}

func (m *RecallByKeyReq) GetMetaData() *common.RecallMetaData {
	if m != nil {
		return m.MetaData
	}
	return nil
}

func (m *RecallByKeyReq) GetFields() []string {
	if m != nil {
		return m.Fields
	}
	return nil
}

type FieldValue struct {
	Type                 FieldValueType `protobuf:"varint,1,opt,name=type,proto3,enum=rcmd.recall_index_es.FieldValueType" json:"type,omitempty"`
	StringValue          string         `protobuf:"bytes,2,opt,name=string_value,json=stringValue,proto3" json:"string_value,omitempty"`
	StructValue          *FieldMap      `protobuf:"bytes,3,opt,name=struct_value,json=structValue,proto3" json:"struct_value,omitempty"`
	ArrayValue           []*FieldValue  `protobuf:"bytes,4,rep,name=array_value,json=arrayValue,proto3" json:"array_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *FieldValue) Reset()         { *m = FieldValue{} }
func (m *FieldValue) String() string { return proto.CompactTextString(m) }
func (*FieldValue) ProtoMessage()    {}
func (*FieldValue) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{9}
}
func (m *FieldValue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldValue.Unmarshal(m, b)
}
func (m *FieldValue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldValue.Marshal(b, m, deterministic)
}
func (dst *FieldValue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldValue.Merge(dst, src)
}
func (m *FieldValue) XXX_Size() int {
	return xxx_messageInfo_FieldValue.Size(m)
}
func (m *FieldValue) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldValue.DiscardUnknown(m)
}

var xxx_messageInfo_FieldValue proto.InternalMessageInfo

func (m *FieldValue) GetType() FieldValueType {
	if m != nil {
		return m.Type
	}
	return FieldValueType_StringFieldValue
}

func (m *FieldValue) GetStringValue() string {
	if m != nil {
		return m.StringValue
	}
	return ""
}

func (m *FieldValue) GetStructValue() *FieldMap {
	if m != nil {
		return m.StructValue
	}
	return nil
}

func (m *FieldValue) GetArrayValue() []*FieldValue {
	if m != nil {
		return m.ArrayValue
	}
	return nil
}

type FieldMap struct {
	FieldMap             map[string]string `protobuf:"bytes,1,rep,name=field_map,json=fieldMap,proto3" json:"field_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *FieldMap) Reset()         { *m = FieldMap{} }
func (m *FieldMap) String() string { return proto.CompactTextString(m) }
func (*FieldMap) ProtoMessage()    {}
func (*FieldMap) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{10}
}
func (m *FieldMap) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FieldMap.Unmarshal(m, b)
}
func (m *FieldMap) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FieldMap.Marshal(b, m, deterministic)
}
func (dst *FieldMap) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FieldMap.Merge(dst, src)
}
func (m *FieldMap) XXX_Size() int {
	return xxx_messageInfo_FieldMap.Size(m)
}
func (m *FieldMap) XXX_DiscardUnknown() {
	xxx_messageInfo_FieldMap.DiscardUnknown(m)
}

var xxx_messageInfo_FieldMap proto.InternalMessageInfo

func (m *FieldMap) GetFieldMap() map[string]string {
	if m != nil {
		return m.FieldMap
	}
	return nil
}

type RecallByKeyRsp struct {
	// repeated RecallItem items = 1;
	Ids                  []string    `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
	Fields               []*FieldMap `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RecallByKeyRsp) Reset()         { *m = RecallByKeyRsp{} }
func (m *RecallByKeyRsp) String() string { return proto.CompactTextString(m) }
func (*RecallByKeyRsp) ProtoMessage()    {}
func (*RecallByKeyRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{11}
}
func (m *RecallByKeyRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallByKeyRsp.Unmarshal(m, b)
}
func (m *RecallByKeyRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallByKeyRsp.Marshal(b, m, deterministic)
}
func (dst *RecallByKeyRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallByKeyRsp.Merge(dst, src)
}
func (m *RecallByKeyRsp) XXX_Size() int {
	return xxx_messageInfo_RecallByKeyRsp.Size(m)
}
func (m *RecallByKeyRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallByKeyRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RecallByKeyRsp proto.InternalMessageInfo

func (m *RecallByKeyRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *RecallByKeyRsp) GetFields() []*FieldMap {
	if m != nil {
		return m.Fields
	}
	return nil
}

type RecallItem struct {
	Id                   uint32     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	IdType               ItemIdType `protobuf:"varint,2,opt,name=id_type,json=idType,proto3,enum=rcmd.recall_index_es.ItemIdType" json:"id_type,omitempty"`
	IdString             string     `protobuf:"bytes,3,opt,name=id_string,json=idString,proto3" json:"id_string,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *RecallItem) Reset()         { *m = RecallItem{} }
func (m *RecallItem) String() string { return proto.CompactTextString(m) }
func (*RecallItem) ProtoMessage()    {}
func (*RecallItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{12}
}
func (m *RecallItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallItem.Unmarshal(m, b)
}
func (m *RecallItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallItem.Marshal(b, m, deterministic)
}
func (dst *RecallItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallItem.Merge(dst, src)
}
func (m *RecallItem) XXX_Size() int {
	return xxx_messageInfo_RecallItem.Size(m)
}
func (m *RecallItem) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallItem.DiscardUnknown(m)
}

var xxx_messageInfo_RecallItem proto.InternalMessageInfo

func (m *RecallItem) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *RecallItem) GetIdType() ItemIdType {
	if m != nil {
		return m.IdType
	}
	return ItemIdType_Uint32ItemId
}

func (m *RecallItem) GetIdString() string {
	if m != nil {
		return m.IdString
	}
	return ""
}

type CreateReq struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Json                 string   `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateReq) Reset()         { *m = CreateReq{} }
func (m *CreateReq) String() string { return proto.CompactTextString(m) }
func (*CreateReq) ProtoMessage()    {}
func (*CreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{13}
}
func (m *CreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateReq.Unmarshal(m, b)
}
func (m *CreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateReq.Marshal(b, m, deterministic)
}
func (dst *CreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateReq.Merge(dst, src)
}
func (m *CreateReq) XXX_Size() int {
	return xxx_messageInfo_CreateReq.Size(m)
}
func (m *CreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateReq proto.InternalMessageInfo

func (m *CreateReq) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *CreateReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CreateReq) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

type SearchReq struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Json                 string   `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	Refresh              bool     `protobuf:"varint,3,opt,name=refresh,proto3" json:"refresh,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchReq) Reset()         { *m = SearchReq{} }
func (m *SearchReq) String() string { return proto.CompactTextString(m) }
func (*SearchReq) ProtoMessage()    {}
func (*SearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{14}
}
func (m *SearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchReq.Unmarshal(m, b)
}
func (m *SearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchReq.Marshal(b, m, deterministic)
}
func (dst *SearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchReq.Merge(dst, src)
}
func (m *SearchReq) XXX_Size() int {
	return xxx_messageInfo_SearchReq.Size(m)
}
func (m *SearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchReq proto.InternalMessageInfo

func (m *SearchReq) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *SearchReq) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *SearchReq) GetRefresh() bool {
	if m != nil {
		return m.Refresh
	}
	return false
}

// 这个是rpc接口，更新用kafka
type UpdateReq struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Json                 string   `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateReq) Reset()         { *m = UpdateReq{} }
func (m *UpdateReq) String() string { return proto.CompactTextString(m) }
func (*UpdateReq) ProtoMessage()    {}
func (*UpdateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{15}
}
func (m *UpdateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateReq.Unmarshal(m, b)
}
func (m *UpdateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateReq.Marshal(b, m, deterministic)
}
func (dst *UpdateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateReq.Merge(dst, src)
}
func (m *UpdateReq) XXX_Size() int {
	return xxx_messageInfo_UpdateReq.Size(m)
}
func (m *UpdateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateReq proto.InternalMessageInfo

func (m *UpdateReq) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *UpdateReq) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

// 这个是rpc接口，更新用kafka
type UpdateByIdReq struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Json                 string   `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateByIdReq) Reset()         { *m = UpdateByIdReq{} }
func (m *UpdateByIdReq) String() string { return proto.CompactTextString(m) }
func (*UpdateByIdReq) ProtoMessage()    {}
func (*UpdateByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{16}
}
func (m *UpdateByIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateByIdReq.Unmarshal(m, b)
}
func (m *UpdateByIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateByIdReq.Marshal(b, m, deterministic)
}
func (dst *UpdateByIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateByIdReq.Merge(dst, src)
}
func (m *UpdateByIdReq) XXX_Size() int {
	return xxx_messageInfo_UpdateByIdReq.Size(m)
}
func (m *UpdateByIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateByIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateByIdReq proto.InternalMessageInfo

func (m *UpdateByIdReq) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *UpdateByIdReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateByIdReq) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

type DelReq struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Json                 string   `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelReq) Reset()         { *m = DelReq{} }
func (m *DelReq) String() string { return proto.CompactTextString(m) }
func (*DelReq) ProtoMessage()    {}
func (*DelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{17}
}
func (m *DelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelReq.Unmarshal(m, b)
}
func (m *DelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelReq.Marshal(b, m, deterministic)
}
func (dst *DelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelReq.Merge(dst, src)
}
func (m *DelReq) XXX_Size() int {
	return xxx_messageInfo_DelReq.Size(m)
}
func (m *DelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelReq proto.InternalMessageInfo

func (m *DelReq) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *DelReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DelReq) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

// kafka的格式
type UpdateEvent struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Json                 string   `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	EventId              EventId  `protobuf:"varint,4,opt,name=event_id,json=eventId,proto3,enum=rcmd.recall_index_es.EventId" json:"event_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateEvent) Reset()         { *m = UpdateEvent{} }
func (m *UpdateEvent) String() string { return proto.CompactTextString(m) }
func (*UpdateEvent) ProtoMessage()    {}
func (*UpdateEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{18}
}
func (m *UpdateEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateEvent.Unmarshal(m, b)
}
func (m *UpdateEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateEvent.Marshal(b, m, deterministic)
}
func (dst *UpdateEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateEvent.Merge(dst, src)
}
func (m *UpdateEvent) XXX_Size() int {
	return xxx_messageInfo_UpdateEvent.Size(m)
}
func (m *UpdateEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateEvent.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateEvent proto.InternalMessageInfo

func (m *UpdateEvent) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *UpdateEvent) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateEvent) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *UpdateEvent) GetEventId() EventId {
	if m != nil {
		return m.EventId
	}
	return EventId_Invalid
}

// kafka的格式
type CreateEvent struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Json                 string   `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	EventId              EventId  `protobuf:"varint,4,opt,name=event_id,json=eventId,proto3,enum=rcmd.recall_index_es.EventId" json:"event_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateEvent) Reset()         { *m = CreateEvent{} }
func (m *CreateEvent) String() string { return proto.CompactTextString(m) }
func (*CreateEvent) ProtoMessage()    {}
func (*CreateEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{19}
}
func (m *CreateEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateEvent.Unmarshal(m, b)
}
func (m *CreateEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateEvent.Marshal(b, m, deterministic)
}
func (dst *CreateEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateEvent.Merge(dst, src)
}
func (m *CreateEvent) XXX_Size() int {
	return xxx_messageInfo_CreateEvent.Size(m)
}
func (m *CreateEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateEvent.DiscardUnknown(m)
}

var xxx_messageInfo_CreateEvent proto.InternalMessageInfo

func (m *CreateEvent) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *CreateEvent) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *CreateEvent) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *CreateEvent) GetEventId() EventId {
	if m != nil {
		return m.EventId
	}
	return EventId_Invalid
}

// kafka的格式
type DeleteEvent struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Id                   string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	Json                 string   `protobuf:"bytes,3,opt,name=json,proto3" json:"json,omitempty"`
	EventId              EventId  `protobuf:"varint,4,opt,name=event_id,json=eventId,proto3,enum=rcmd.recall_index_es.EventId" json:"event_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteEvent) Reset()         { *m = DeleteEvent{} }
func (m *DeleteEvent) String() string { return proto.CompactTextString(m) }
func (*DeleteEvent) ProtoMessage()    {}
func (*DeleteEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{20}
}
func (m *DeleteEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteEvent.Unmarshal(m, b)
}
func (m *DeleteEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteEvent.Marshal(b, m, deterministic)
}
func (dst *DeleteEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteEvent.Merge(dst, src)
}
func (m *DeleteEvent) XXX_Size() int {
	return xxx_messageInfo_DeleteEvent.Size(m)
}
func (m *DeleteEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteEvent.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteEvent proto.InternalMessageInfo

func (m *DeleteEvent) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *DeleteEvent) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DeleteEvent) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *DeleteEvent) GetEventId() EventId {
	if m != nil {
		return m.EventId
	}
	return EventId_Invalid
}

type FilterBoxChain struct {
	Chain                []*FilterBox `protobuf:"bytes,1,rep,name=chain,proto3" json:"chain,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *FilterBoxChain) Reset()         { *m = FilterBoxChain{} }
func (m *FilterBoxChain) String() string { return proto.CompactTextString(m) }
func (*FilterBoxChain) ProtoMessage()    {}
func (*FilterBoxChain) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{21}
}
func (m *FilterBoxChain) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterBoxChain.Unmarshal(m, b)
}
func (m *FilterBoxChain) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterBoxChain.Marshal(b, m, deterministic)
}
func (dst *FilterBoxChain) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterBoxChain.Merge(dst, src)
}
func (m *FilterBoxChain) XXX_Size() int {
	return xxx_messageInfo_FilterBoxChain.Size(m)
}
func (m *FilterBoxChain) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterBoxChain.DiscardUnknown(m)
}

var xxx_messageInfo_FilterBoxChain proto.InternalMessageInfo

func (m *FilterBoxChain) GetChain() []*FilterBox {
	if m != nil {
		return m.Chain
	}
	return nil
}

type FilterBox struct {
	ApartFrom            *ApartFrom `protobuf:"bytes,1,opt,name=apart_from,json=apartFrom,proto3" json:"apart_from,omitempty"`
	MustNot              *MustNot   `protobuf:"bytes,2,opt,name=must_not,json=mustNot,proto3" json:"must_not,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *FilterBox) Reset()         { *m = FilterBox{} }
func (m *FilterBox) String() string { return proto.CompactTextString(m) }
func (*FilterBox) ProtoMessage()    {}
func (*FilterBox) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{22}
}
func (m *FilterBox) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterBox.Unmarshal(m, b)
}
func (m *FilterBox) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterBox.Marshal(b, m, deterministic)
}
func (dst *FilterBox) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterBox.Merge(dst, src)
}
func (m *FilterBox) XXX_Size() int {
	return xxx_messageInfo_FilterBox.Size(m)
}
func (m *FilterBox) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterBox.DiscardUnknown(m)
}

var xxx_messageInfo_FilterBox proto.InternalMessageInfo

func (m *FilterBox) GetApartFrom() *ApartFrom {
	if m != nil {
		return m.ApartFrom
	}
	return nil
}

func (m *FilterBox) GetMustNot() *MustNot {
	if m != nil {
		return m.MustNot
	}
	return nil
}

type ApartFrom struct {
	Bool                 []*Bool  `protobuf:"bytes,1,rep,name=bool,proto3" json:"bool,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApartFrom) Reset()         { *m = ApartFrom{} }
func (m *ApartFrom) String() string { return proto.CompactTextString(m) }
func (*ApartFrom) ProtoMessage()    {}
func (*ApartFrom) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{23}
}
func (m *ApartFrom) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApartFrom.Unmarshal(m, b)
}
func (m *ApartFrom) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApartFrom.Marshal(b, m, deterministic)
}
func (dst *ApartFrom) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApartFrom.Merge(dst, src)
}
func (m *ApartFrom) XXX_Size() int {
	return xxx_messageInfo_ApartFrom.Size(m)
}
func (m *ApartFrom) XXX_DiscardUnknown() {
	xxx_messageInfo_ApartFrom.DiscardUnknown(m)
}

var xxx_messageInfo_ApartFrom proto.InternalMessageInfo

func (m *ApartFrom) GetBool() []*Bool {
	if m != nil {
		return m.Bool
	}
	return nil
}

type MustNot struct {
	Bool                 []*Bool  `protobuf:"bytes,1,rep,name=bool,proto3" json:"bool,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MustNot) Reset()         { *m = MustNot{} }
func (m *MustNot) String() string { return proto.CompactTextString(m) }
func (*MustNot) ProtoMessage()    {}
func (*MustNot) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{24}
}
func (m *MustNot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MustNot.Unmarshal(m, b)
}
func (m *MustNot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MustNot.Marshal(b, m, deterministic)
}
func (dst *MustNot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MustNot.Merge(dst, src)
}
func (m *MustNot) XXX_Size() int {
	return xxx_messageInfo_MustNot.Size(m)
}
func (m *MustNot) XXX_DiscardUnknown() {
	xxx_messageInfo_MustNot.DiscardUnknown(m)
}

var xxx_messageInfo_MustNot proto.InternalMessageInfo

func (m *MustNot) GetBool() []*Bool {
	if m != nil {
		return m.Bool
	}
	return nil
}

type Bool struct {
	Field                string      `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	InSet                *InSet      `protobuf:"bytes,2,opt,name=in_set,json=inSet,proto3" json:"in_set,omitempty"`
	InRangeSet           *InRangeSet `protobuf:"bytes,3,opt,name=in_range_set,json=inRangeSet,proto3" json:"in_range_set,omitempty"`
	Pid                  persona.Id  `protobuf:"varint,4,opt,name=pid,proto3,enum=rcmd.persona.Id" json:"pid,omitempty"`
	Name                 string      `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *Bool) Reset()         { *m = Bool{} }
func (m *Bool) String() string { return proto.CompactTextString(m) }
func (*Bool) ProtoMessage()    {}
func (*Bool) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{25}
}
func (m *Bool) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Bool.Unmarshal(m, b)
}
func (m *Bool) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Bool.Marshal(b, m, deterministic)
}
func (dst *Bool) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Bool.Merge(dst, src)
}
func (m *Bool) XXX_Size() int {
	return xxx_messageInfo_Bool.Size(m)
}
func (m *Bool) XXX_DiscardUnknown() {
	xxx_messageInfo_Bool.DiscardUnknown(m)
}

var xxx_messageInfo_Bool proto.InternalMessageInfo

func (m *Bool) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *Bool) GetInSet() *InSet {
	if m != nil {
		return m.InSet
	}
	return nil
}

func (m *Bool) GetInRangeSet() *InRangeSet {
	if m != nil {
		return m.InRangeSet
	}
	return nil
}

func (m *Bool) GetPid() persona.Id {
	if m != nil {
		return m.Pid
	}
	return persona.Id_Default
}

func (m *Bool) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type InSet struct {
	Key                  SetKey   `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.SetKey" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InSet) Reset()         { *m = InSet{} }
func (m *InSet) String() string { return proto.CompactTextString(m) }
func (*InSet) ProtoMessage()    {}
func (*InSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{26}
}
func (m *InSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InSet.Unmarshal(m, b)
}
func (m *InSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InSet.Marshal(b, m, deterministic)
}
func (dst *InSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InSet.Merge(dst, src)
}
func (m *InSet) XXX_Size() int {
	return xxx_messageInfo_InSet.Size(m)
}
func (m *InSet) XXX_DiscardUnknown() {
	xxx_messageInfo_InSet.DiscardUnknown(m)
}

var xxx_messageInfo_InSet proto.InternalMessageInfo

func (m *InSet) GetKey() SetKey {
	if m != nil {
		return m.Key
	}
	return SetKey_SetKeyInvalid
}

type InRangeSet struct {
	Key                  RangeSetKey `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.RangeSetKey" json:"key,omitempty"`
	Min                  string      `protobuf:"bytes,2,opt,name=min,proto3" json:"min,omitempty"`
	Max                  string      `protobuf:"bytes,3,opt,name=max,proto3" json:"max,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *InRangeSet) Reset()         { *m = InRangeSet{} }
func (m *InRangeSet) String() string { return proto.CompactTextString(m) }
func (*InRangeSet) ProtoMessage()    {}
func (*InRangeSet) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{27}
}
func (m *InRangeSet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InRangeSet.Unmarshal(m, b)
}
func (m *InRangeSet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InRangeSet.Marshal(b, m, deterministic)
}
func (dst *InRangeSet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InRangeSet.Merge(dst, src)
}
func (m *InRangeSet) XXX_Size() int {
	return xxx_messageInfo_InRangeSet.Size(m)
}
func (m *InRangeSet) XXX_DiscardUnknown() {
	xxx_messageInfo_InRangeSet.DiscardUnknown(m)
}

var xxx_messageInfo_InRangeSet proto.InternalMessageInfo

func (m *InRangeSet) GetKey() RangeSetKey {
	if m != nil {
		return m.Key
	}
	return RangeSetKey_RangeSetKeyInvalid
}

func (m *InRangeSet) GetMin() string {
	if m != nil {
		return m.Min
	}
	return ""
}

func (m *InRangeSet) GetMax() string {
	if m != nil {
		return m.Max
	}
	return ""
}

type UpdateSetReq struct {
	Key                  SetKey   `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.SetKey" json:"key,omitempty"`
	Op                   Op       `protobuf:"varint,2,opt,name=op,proto3,enum=rcmd.recall_index_es.Op" json:"op,omitempty"`
	Val                  string   `protobuf:"bytes,3,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSetReq) Reset()         { *m = UpdateSetReq{} }
func (m *UpdateSetReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSetReq) ProtoMessage()    {}
func (*UpdateSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{28}
}
func (m *UpdateSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSetReq.Unmarshal(m, b)
}
func (m *UpdateSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSetReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSetReq.Merge(dst, src)
}
func (m *UpdateSetReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSetReq.Size(m)
}
func (m *UpdateSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSetReq proto.InternalMessageInfo

func (m *UpdateSetReq) GetKey() SetKey {
	if m != nil {
		return m.Key
	}
	return SetKey_SetKeyInvalid
}

func (m *UpdateSetReq) GetOp() Op {
	if m != nil {
		return m.Op
	}
	return Op_Op_Invalid
}

func (m *UpdateSetReq) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

type UpdateSetRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSetRsp) Reset()         { *m = UpdateSetRsp{} }
func (m *UpdateSetRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateSetRsp) ProtoMessage()    {}
func (*UpdateSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{29}
}
func (m *UpdateSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSetRsp.Unmarshal(m, b)
}
func (m *UpdateSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSetRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSetRsp.Merge(dst, src)
}
func (m *UpdateSetRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateSetRsp.Size(m)
}
func (m *UpdateSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSetRsp proto.InternalMessageInfo

func (m *UpdateSetRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateSetRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type BatchUpdateSetReq struct {
	Key                  SetKey   `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.SetKey" json:"key,omitempty"`
	Op                   Op       `protobuf:"varint,2,opt,name=op,proto3,enum=rcmd.recall_index_es.Op" json:"op,omitempty"`
	Ids                  []string `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateSetReq) Reset()         { *m = BatchUpdateSetReq{} }
func (m *BatchUpdateSetReq) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateSetReq) ProtoMessage()    {}
func (*BatchUpdateSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{30}
}
func (m *BatchUpdateSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateSetReq.Unmarshal(m, b)
}
func (m *BatchUpdateSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateSetReq.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateSetReq.Merge(dst, src)
}
func (m *BatchUpdateSetReq) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateSetReq.Size(m)
}
func (m *BatchUpdateSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateSetReq proto.InternalMessageInfo

func (m *BatchUpdateSetReq) GetKey() SetKey {
	if m != nil {
		return m.Key
	}
	return SetKey_SetKeyInvalid
}

func (m *BatchUpdateSetReq) GetOp() Op {
	if m != nil {
		return m.Op
	}
	return Op_Op_Invalid
}

func (m *BatchUpdateSetReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type BatchUpdateSetRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUpdateSetRsp) Reset()         { *m = BatchUpdateSetRsp{} }
func (m *BatchUpdateSetRsp) String() string { return proto.CompactTextString(m) }
func (*BatchUpdateSetRsp) ProtoMessage()    {}
func (*BatchUpdateSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{31}
}
func (m *BatchUpdateSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUpdateSetRsp.Unmarshal(m, b)
}
func (m *BatchUpdateSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUpdateSetRsp.Marshal(b, m, deterministic)
}
func (dst *BatchUpdateSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUpdateSetRsp.Merge(dst, src)
}
func (m *BatchUpdateSetRsp) XXX_Size() int {
	return xxx_messageInfo_BatchUpdateSetRsp.Size(m)
}
func (m *BatchUpdateSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUpdateSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUpdateSetRsp proto.InternalMessageInfo

func (m *BatchUpdateSetRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BatchUpdateSetRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// TODO: 增加消费监控
type UpdateRangeSetEvent struct {
	Key                  RangeSetKey `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.RangeSetKey" json:"key,omitempty"`
	Op                   Op          `protobuf:"varint,2,opt,name=op,proto3,enum=rcmd.recall_index_es.Op" json:"op,omitempty"`
	Val                  string      `protobuf:"bytes,3,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateRangeSetEvent) Reset()         { *m = UpdateRangeSetEvent{} }
func (m *UpdateRangeSetEvent) String() string { return proto.CompactTextString(m) }
func (*UpdateRangeSetEvent) ProtoMessage()    {}
func (*UpdateRangeSetEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{32}
}
func (m *UpdateRangeSetEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRangeSetEvent.Unmarshal(m, b)
}
func (m *UpdateRangeSetEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRangeSetEvent.Marshal(b, m, deterministic)
}
func (dst *UpdateRangeSetEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRangeSetEvent.Merge(dst, src)
}
func (m *UpdateRangeSetEvent) XXX_Size() int {
	return xxx_messageInfo_UpdateRangeSetEvent.Size(m)
}
func (m *UpdateRangeSetEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRangeSetEvent.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRangeSetEvent proto.InternalMessageInfo

func (m *UpdateRangeSetEvent) GetKey() RangeSetKey {
	if m != nil {
		return m.Key
	}
	return RangeSetKey_RangeSetKeyInvalid
}

func (m *UpdateRangeSetEvent) GetOp() Op {
	if m != nil {
		return m.Op
	}
	return Op_Op_Invalid
}

func (m *UpdateRangeSetEvent) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

type UpdateRangeSetReq struct {
	Key                  RangeSetKey `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.RangeSetKey" json:"key,omitempty"`
	Op                   Op          `protobuf:"varint,2,opt,name=op,proto3,enum=rcmd.recall_index_es.Op" json:"op,omitempty"`
	Val                  string      `protobuf:"bytes,3,opt,name=val,proto3" json:"val,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateRangeSetReq) Reset()         { *m = UpdateRangeSetReq{} }
func (m *UpdateRangeSetReq) String() string { return proto.CompactTextString(m) }
func (*UpdateRangeSetReq) ProtoMessage()    {}
func (*UpdateRangeSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{33}
}
func (m *UpdateRangeSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRangeSetReq.Unmarshal(m, b)
}
func (m *UpdateRangeSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRangeSetReq.Marshal(b, m, deterministic)
}
func (dst *UpdateRangeSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRangeSetReq.Merge(dst, src)
}
func (m *UpdateRangeSetReq) XXX_Size() int {
	return xxx_messageInfo_UpdateRangeSetReq.Size(m)
}
func (m *UpdateRangeSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRangeSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRangeSetReq proto.InternalMessageInfo

func (m *UpdateRangeSetReq) GetKey() RangeSetKey {
	if m != nil {
		return m.Key
	}
	return RangeSetKey_RangeSetKeyInvalid
}

func (m *UpdateRangeSetReq) GetOp() Op {
	if m != nil {
		return m.Op
	}
	return Op_Op_Invalid
}

func (m *UpdateRangeSetReq) GetVal() string {
	if m != nil {
		return m.Val
	}
	return ""
}

type UpdateRangeSetRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateRangeSetRsp) Reset()         { *m = UpdateRangeSetRsp{} }
func (m *UpdateRangeSetRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateRangeSetRsp) ProtoMessage()    {}
func (*UpdateRangeSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{34}
}
func (m *UpdateRangeSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateRangeSetRsp.Unmarshal(m, b)
}
func (m *UpdateRangeSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateRangeSetRsp.Marshal(b, m, deterministic)
}
func (dst *UpdateRangeSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateRangeSetRsp.Merge(dst, src)
}
func (m *UpdateRangeSetRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateRangeSetRsp.Size(m)
}
func (m *UpdateRangeSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateRangeSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateRangeSetRsp proto.InternalMessageInfo

func (m *UpdateRangeSetRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *UpdateRangeSetRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type GetSetReq struct {
	Key                  SetKey   `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.SetKey" json:"key,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               uint32   `protobuf:"varint,3,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSetReq) Reset()         { *m = GetSetReq{} }
func (m *GetSetReq) String() string { return proto.CompactTextString(m) }
func (*GetSetReq) ProtoMessage()    {}
func (*GetSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{35}
}
func (m *GetSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSetReq.Unmarshal(m, b)
}
func (m *GetSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSetReq.Marshal(b, m, deterministic)
}
func (dst *GetSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSetReq.Merge(dst, src)
}
func (m *GetSetReq) XXX_Size() int {
	return xxx_messageInfo_GetSetReq.Size(m)
}
func (m *GetSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSetReq proto.InternalMessageInfo

func (m *GetSetReq) GetKey() SetKey {
	if m != nil {
		return m.Key
	}
	return SetKey_SetKeyInvalid
}

func (m *GetSetReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetSetReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

type GetSetRsp struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	TotalNum             uint32   `protobuf:"varint,2,opt,name=total_num,json=totalNum,proto3" json:"total_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSetRsp) Reset()         { *m = GetSetRsp{} }
func (m *GetSetRsp) String() string { return proto.CompactTextString(m) }
func (*GetSetRsp) ProtoMessage()    {}
func (*GetSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{36}
}
func (m *GetSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSetRsp.Unmarshal(m, b)
}
func (m *GetSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSetRsp.Marshal(b, m, deterministic)
}
func (dst *GetSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSetRsp.Merge(dst, src)
}
func (m *GetSetRsp) XXX_Size() int {
	return xxx_messageInfo_GetSetRsp.Size(m)
}
func (m *GetSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSetRsp proto.InternalMessageInfo

func (m *GetSetRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *GetSetRsp) GetTotalNum() uint32 {
	if m != nil {
		return m.TotalNum
	}
	return 0
}

type CheckInSetReq struct {
	Key                  SetKey   `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.SetKey" json:"key,omitempty"`
	Ids                  []string `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInSetReq) Reset()         { *m = CheckInSetReq{} }
func (m *CheckInSetReq) String() string { return proto.CompactTextString(m) }
func (*CheckInSetReq) ProtoMessage()    {}
func (*CheckInSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{37}
}
func (m *CheckInSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInSetReq.Unmarshal(m, b)
}
func (m *CheckInSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInSetReq.Marshal(b, m, deterministic)
}
func (dst *CheckInSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInSetReq.Merge(dst, src)
}
func (m *CheckInSetReq) XXX_Size() int {
	return xxx_messageInfo_CheckInSetReq.Size(m)
}
func (m *CheckInSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInSetReq proto.InternalMessageInfo

func (m *CheckInSetReq) GetKey() SetKey {
	if m != nil {
		return m.Key
	}
	return SetKey_SetKeyInvalid
}

func (m *CheckInSetReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type CheckInSetRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Ids                  []string `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInSetRsp) Reset()         { *m = CheckInSetRsp{} }
func (m *CheckInSetRsp) String() string { return proto.CompactTextString(m) }
func (*CheckInSetRsp) ProtoMessage()    {}
func (*CheckInSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{38}
}
func (m *CheckInSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInSetRsp.Unmarshal(m, b)
}
func (m *CheckInSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInSetRsp.Marshal(b, m, deterministic)
}
func (dst *CheckInSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInSetRsp.Merge(dst, src)
}
func (m *CheckInSetRsp) XXX_Size() int {
	return xxx_messageInfo_CheckInSetRsp.Size(m)
}
func (m *CheckInSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInSetRsp proto.InternalMessageInfo

func (m *CheckInSetRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CheckInSetRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *CheckInSetRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

// TODO 要考虑有些场景需要重置过滤器
type AddBloomReq struct {
	Key                  BloomKey `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.BloomKey" json:"key,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemIdList           []string `protobuf:"bytes,3,rep,name=item_id_list,json=itemIdList,proto3" json:"item_id_list,omitempty"`
	Expire               uint64   `protobuf:"varint,4,opt,name=expire,proto3" json:"expire,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBloomReq) Reset()         { *m = AddBloomReq{} }
func (m *AddBloomReq) String() string { return proto.CompactTextString(m) }
func (*AddBloomReq) ProtoMessage()    {}
func (*AddBloomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{39}
}
func (m *AddBloomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBloomReq.Unmarshal(m, b)
}
func (m *AddBloomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBloomReq.Marshal(b, m, deterministic)
}
func (dst *AddBloomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBloomReq.Merge(dst, src)
}
func (m *AddBloomReq) XXX_Size() int {
	return xxx_messageInfo_AddBloomReq.Size(m)
}
func (m *AddBloomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBloomReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBloomReq proto.InternalMessageInfo

func (m *AddBloomReq) GetKey() BloomKey {
	if m != nil {
		return m.Key
	}
	return BloomKey_BloomKeyInvalid
}

func (m *AddBloomReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddBloomReq) GetItemIdList() []string {
	if m != nil {
		return m.ItemIdList
	}
	return nil
}

func (m *AddBloomReq) GetExpire() uint64 {
	if m != nil {
		return m.Expire
	}
	return 0
}

type AddBloomRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBloomRsp) Reset()         { *m = AddBloomRsp{} }
func (m *AddBloomRsp) String() string { return proto.CompactTextString(m) }
func (*AddBloomRsp) ProtoMessage()    {}
func (*AddBloomRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{40}
}
func (m *AddBloomRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBloomRsp.Unmarshal(m, b)
}
func (m *AddBloomRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBloomRsp.Marshal(b, m, deterministic)
}
func (dst *AddBloomRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBloomRsp.Merge(dst, src)
}
func (m *AddBloomRsp) XXX_Size() int {
	return xxx_messageInfo_AddBloomRsp.Size(m)
}
func (m *AddBloomRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBloomRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBloomRsp proto.InternalMessageInfo

func (m *AddBloomRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *AddBloomRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type ResetBloomReq struct {
	Key                  BloomKey `protobuf:"varint,1,opt,name=key,proto3,enum=rcmd.recall_index_es.BloomKey" json:"key,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetBloomReq) Reset()         { *m = ResetBloomReq{} }
func (m *ResetBloomReq) String() string { return proto.CompactTextString(m) }
func (*ResetBloomReq) ProtoMessage()    {}
func (*ResetBloomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{41}
}
func (m *ResetBloomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetBloomReq.Unmarshal(m, b)
}
func (m *ResetBloomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetBloomReq.Marshal(b, m, deterministic)
}
func (dst *ResetBloomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetBloomReq.Merge(dst, src)
}
func (m *ResetBloomReq) XXX_Size() int {
	return xxx_messageInfo_ResetBloomReq.Size(m)
}
func (m *ResetBloomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetBloomReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResetBloomReq proto.InternalMessageInfo

func (m *ResetBloomReq) GetKey() BloomKey {
	if m != nil {
		return m.Key
	}
	return BloomKey_BloomKeyInvalid
}

func (m *ResetBloomReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ResetBloomRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResetBloomRsp) Reset()         { *m = ResetBloomRsp{} }
func (m *ResetBloomRsp) String() string { return proto.CompactTextString(m) }
func (*ResetBloomRsp) ProtoMessage()    {}
func (*ResetBloomRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{42}
}
func (m *ResetBloomRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetBloomRsp.Unmarshal(m, b)
}
func (m *ResetBloomRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetBloomRsp.Marshal(b, m, deterministic)
}
func (dst *ResetBloomRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetBloomRsp.Merge(dst, src)
}
func (m *ResetBloomRsp) XXX_Size() int {
	return xxx_messageInfo_ResetBloomRsp.Size(m)
}
func (m *ResetBloomRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetBloomRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ResetBloomRsp proto.InternalMessageInfo

func (m *ResetBloomRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ResetBloomRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

// 查看符合某个查询的item数量
type CountReq struct {
	IndexName            string   `protobuf:"bytes,1,opt,name=index_name,json=indexName,proto3" json:"index_name,omitempty"`
	Json                 string   `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountReq) Reset()         { *m = CountReq{} }
func (m *CountReq) String() string { return proto.CompactTextString(m) }
func (*CountReq) ProtoMessage()    {}
func (*CountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{43}
}
func (m *CountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountReq.Unmarshal(m, b)
}
func (m *CountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountReq.Marshal(b, m, deterministic)
}
func (dst *CountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountReq.Merge(dst, src)
}
func (m *CountReq) XXX_Size() int {
	return xxx_messageInfo_CountReq.Size(m)
}
func (m *CountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CountReq.DiscardUnknown(m)
}

var xxx_messageInfo_CountReq proto.InternalMessageInfo

func (m *CountReq) GetIndexName() string {
	if m != nil {
		return m.IndexName
	}
	return ""
}

func (m *CountReq) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

type CountRsp struct {
	Cnt                  uint32   `protobuf:"varint,1,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountRsp) Reset()         { *m = CountRsp{} }
func (m *CountRsp) String() string { return proto.CompactTextString(m) }
func (*CountRsp) ProtoMessage()    {}
func (*CountRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{44}
}
func (m *CountRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountRsp.Unmarshal(m, b)
}
func (m *CountRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountRsp.Marshal(b, m, deterministic)
}
func (dst *CountRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountRsp.Merge(dst, src)
}
func (m *CountRsp) XXX_Size() int {
	return xxx_messageInfo_CountRsp.Size(m)
}
func (m *CountRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CountRsp proto.InternalMessageInfo

func (m *CountRsp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// KV index
type IndexEvent struct {
	Op        recall_common.IndexOp `protobuf:"varint,1,opt,name=op,proto3,enum=rcmd.recall_common.IndexOp" json:"op,omitempty"`
	IndexDesc *IndexDesc            `protobuf:"bytes,2,opt,name=index_desc,json=indexDesc,proto3" json:"index_desc,omitempty"`
	// repeated uint32 id_list = 3;//兼容老版本
	// repeated RecallItem item_list = 4;//新item实现，id_list和item_list内的内容都会被索引进去
	Storage              ItemsStorage `protobuf:"varint,5,opt,name=storage,proto3,enum=rcmd.recall_index_es.ItemsStorage" json:"storage,omitempty"`
	Ids                  []string     `protobuf:"bytes,6,rep,name=ids,proto3" json:"ids,omitempty"`
	Fields               []*FieldMap  `protobuf:"bytes,7,rep,name=fields,proto3" json:"fields,omitempty"`
	Ttl                  uint32       `protobuf:"varint,8,opt,name=ttl,proto3" json:"ttl,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *IndexEvent) Reset()         { *m = IndexEvent{} }
func (m *IndexEvent) String() string { return proto.CompactTextString(m) }
func (*IndexEvent) ProtoMessage()    {}
func (*IndexEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{45}
}
func (m *IndexEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexEvent.Unmarshal(m, b)
}
func (m *IndexEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexEvent.Marshal(b, m, deterministic)
}
func (dst *IndexEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexEvent.Merge(dst, src)
}
func (m *IndexEvent) XXX_Size() int {
	return xxx_messageInfo_IndexEvent.Size(m)
}
func (m *IndexEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexEvent.DiscardUnknown(m)
}

var xxx_messageInfo_IndexEvent proto.InternalMessageInfo

func (m *IndexEvent) GetOp() recall_common.IndexOp {
	if m != nil {
		return m.Op
	}
	return recall_common.IndexOp_Invalid_Op
}

func (m *IndexEvent) GetIndexDesc() *IndexDesc {
	if m != nil {
		return m.IndexDesc
	}
	return nil
}

func (m *IndexEvent) GetStorage() ItemsStorage {
	if m != nil {
		return m.Storage
	}
	return ItemsStorage_RedisSet
}

func (m *IndexEvent) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *IndexEvent) GetFields() []*FieldMap {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *IndexEvent) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type IndexDesc struct {
	App                  recall_common.App  `protobuf:"varint,1,opt,name=app,proto3,enum=rcmd.recall_common.App" json:"app,omitempty"`
	Item                 recall_common.Item `protobuf:"varint,2,opt,name=item,proto3,enum=rcmd.recall_common.Item" json:"item,omitempty"`
	LabelList            []*Label           `protobuf:"bytes,3,rep,name=label_list,json=labelList,proto3" json:"label_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *IndexDesc) Reset()         { *m = IndexDesc{} }
func (m *IndexDesc) String() string { return proto.CompactTextString(m) }
func (*IndexDesc) ProtoMessage()    {}
func (*IndexDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{46}
}
func (m *IndexDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndexDesc.Unmarshal(m, b)
}
func (m *IndexDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndexDesc.Marshal(b, m, deterministic)
}
func (dst *IndexDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndexDesc.Merge(dst, src)
}
func (m *IndexDesc) XXX_Size() int {
	return xxx_messageInfo_IndexDesc.Size(m)
}
func (m *IndexDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_IndexDesc.DiscardUnknown(m)
}

var xxx_messageInfo_IndexDesc proto.InternalMessageInfo

func (m *IndexDesc) GetApp() recall_common.App {
	if m != nil {
		return m.App
	}
	return recall_common.App_Invalid_App
}

func (m *IndexDesc) GetItem() recall_common.Item {
	if m != nil {
		return m.Item
	}
	return recall_common.Item_Invalid_Item
}

func (m *IndexDesc) GetLabelList() []*Label {
	if m != nil {
		return m.LabelList
	}
	return nil
}

// 暴露过滤接口
type FilterReq struct {
	Ids                  []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Chain                *FilterBoxChain        `protobuf:"bytes,2,opt,name=chain,proto3" json:"chain,omitempty"`
	Uid                  uint32                 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	BloomKey             BloomKey               `protobuf:"varint,4,opt,name=bloom_key,json=bloomKey,proto3,enum=rcmd.recall_index_es.BloomKey" json:"bloom_key,omitempty"`
	DocMap               string                 `protobuf:"bytes,5,opt,name=doc_map,json=docMap,proto3" json:"doc_map,omitempty"`
	Meta                 *common.RecallMetaData `protobuf:"bytes,6,opt,name=meta,proto3" json:"meta,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *FilterReq) Reset()         { *m = FilterReq{} }
func (m *FilterReq) String() string { return proto.CompactTextString(m) }
func (*FilterReq) ProtoMessage()    {}
func (*FilterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{47}
}
func (m *FilterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterReq.Unmarshal(m, b)
}
func (m *FilterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterReq.Marshal(b, m, deterministic)
}
func (dst *FilterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterReq.Merge(dst, src)
}
func (m *FilterReq) XXX_Size() int {
	return xxx_messageInfo_FilterReq.Size(m)
}
func (m *FilterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterReq.DiscardUnknown(m)
}

var xxx_messageInfo_FilterReq proto.InternalMessageInfo

func (m *FilterReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *FilterReq) GetChain() *FilterBoxChain {
	if m != nil {
		return m.Chain
	}
	return nil
}

func (m *FilterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FilterReq) GetBloomKey() BloomKey {
	if m != nil {
		return m.BloomKey
	}
	return BloomKey_BloomKeyInvalid
}

func (m *FilterReq) GetDocMap() string {
	if m != nil {
		return m.DocMap
	}
	return ""
}

func (m *FilterReq) GetMeta() *common.RecallMetaData {
	if m != nil {
		return m.Meta
	}
	return nil
}

type FilterRsp struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FilterRsp) Reset()         { *m = FilterRsp{} }
func (m *FilterRsp) String() string { return proto.CompactTextString(m) }
func (*FilterRsp) ProtoMessage()    {}
func (*FilterRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{48}
}
func (m *FilterRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FilterRsp.Unmarshal(m, b)
}
func (m *FilterRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FilterRsp.Marshal(b, m, deterministic)
}
func (dst *FilterRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FilterRsp.Merge(dst, src)
}
func (m *FilterRsp) XXX_Size() int {
	return xxx_messageInfo_FilterRsp.Size(m)
}
func (m *FilterRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_FilterRsp.DiscardUnknown(m)
}

var xxx_messageInfo_FilterRsp proto.InternalMessageInfo

func (m *FilterRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type OnPersonaChangeReq struct {
	Pid                  persona.Id `protobuf:"varint,1,opt,name=pid,proto3,enum=rcmd.persona.Id" json:"pid,omitempty"`
	Key                  string     `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *OnPersonaChangeReq) Reset()         { *m = OnPersonaChangeReq{} }
func (m *OnPersonaChangeReq) String() string { return proto.CompactTextString(m) }
func (*OnPersonaChangeReq) ProtoMessage()    {}
func (*OnPersonaChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{49}
}
func (m *OnPersonaChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnPersonaChangeReq.Unmarshal(m, b)
}
func (m *OnPersonaChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnPersonaChangeReq.Marshal(b, m, deterministic)
}
func (dst *OnPersonaChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnPersonaChangeReq.Merge(dst, src)
}
func (m *OnPersonaChangeReq) XXX_Size() int {
	return xxx_messageInfo_OnPersonaChangeReq.Size(m)
}
func (m *OnPersonaChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OnPersonaChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_OnPersonaChangeReq proto.InternalMessageInfo

func (m *OnPersonaChangeReq) GetPid() persona.Id {
	if m != nil {
		return m.Pid
	}
	return persona.Id_Default
}

func (m *OnPersonaChangeReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type OnPersonaChangeRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OnPersonaChangeRsp) Reset()         { *m = OnPersonaChangeRsp{} }
func (m *OnPersonaChangeRsp) String() string { return proto.CompactTextString(m) }
func (*OnPersonaChangeRsp) ProtoMessage()    {}
func (*OnPersonaChangeRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{50}
}
func (m *OnPersonaChangeRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnPersonaChangeRsp.Unmarshal(m, b)
}
func (m *OnPersonaChangeRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnPersonaChangeRsp.Marshal(b, m, deterministic)
}
func (dst *OnPersonaChangeRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnPersonaChangeRsp.Merge(dst, src)
}
func (m *OnPersonaChangeRsp) XXX_Size() int {
	return xxx_messageInfo_OnPersonaChangeRsp.Size(m)
}
func (m *OnPersonaChangeRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_OnPersonaChangeRsp.DiscardUnknown(m)
}

var xxx_messageInfo_OnPersonaChangeRsp proto.InternalMessageInfo

func (m *OnPersonaChangeRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *OnPersonaChangeRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type Condition struct {
	Field                string   `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	Relation             Relation `protobuf:"varint,2,opt,name=relation,proto3,enum=rcmd.recall_index_es.Relation" json:"relation,omitempty"`
	Value                string   `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Condition) Reset()         { *m = Condition{} }
func (m *Condition) String() string { return proto.CompactTextString(m) }
func (*Condition) ProtoMessage()    {}
func (*Condition) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{51}
}
func (m *Condition) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Condition.Unmarshal(m, b)
}
func (m *Condition) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Condition.Marshal(b, m, deterministic)
}
func (dst *Condition) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Condition.Merge(dst, src)
}
func (m *Condition) XXX_Size() int {
	return xxx_messageInfo_Condition.Size(m)
}
func (m *Condition) XXX_DiscardUnknown() {
	xxx_messageInfo_Condition.DiscardUnknown(m)
}

var xxx_messageInfo_Condition proto.InternalMessageInfo

func (m *Condition) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *Condition) GetRelation() Relation {
	if m != nil {
		return m.Relation
	}
	return Relation_Equal
}

func (m *Condition) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type Param struct {
	Name                 string    `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value                string    `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Type                 ParamType `protobuf:"varint,3,opt,name=type,proto3,enum=rcmd.recall_index_es.ParamType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *Param) Reset()         { *m = Param{} }
func (m *Param) String() string { return proto.CompactTextString(m) }
func (*Param) ProtoMessage()    {}
func (*Param) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{52}
}
func (m *Param) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Param.Unmarshal(m, b)
}
func (m *Param) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Param.Marshal(b, m, deterministic)
}
func (dst *Param) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Param.Merge(dst, src)
}
func (m *Param) XXX_Size() int {
	return xxx_messageInfo_Param.Size(m)
}
func (m *Param) XXX_DiscardUnknown() {
	xxx_messageInfo_Param.DiscardUnknown(m)
}

var xxx_messageInfo_Param proto.InternalMessageInfo

func (m *Param) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *Param) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *Param) GetType() ParamType {
	if m != nil {
		return m.Type
	}
	return ParamType_UnknownParamType
}

type SortDesc struct {
	Expr                 string   `protobuf:"bytes,1,opt,name=expr,proto3" json:"expr,omitempty"`
	Params               []*Param `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortDesc) Reset()         { *m = SortDesc{} }
func (m *SortDesc) String() string { return proto.CompactTextString(m) }
func (*SortDesc) ProtoMessage()    {}
func (*SortDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{53}
}
func (m *SortDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortDesc.Unmarshal(m, b)
}
func (m *SortDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortDesc.Marshal(b, m, deterministic)
}
func (dst *SortDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortDesc.Merge(dst, src)
}
func (m *SortDesc) XXX_Size() int {
	return xxx_messageInfo_SortDesc.Size(m)
}
func (m *SortDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_SortDesc.DiscardUnknown(m)
}

var xxx_messageInfo_SortDesc proto.InternalMessageInfo

func (m *SortDesc) GetExpr() string {
	if m != nil {
		return m.Expr
	}
	return ""
}

func (m *SortDesc) GetParams() []*Param {
	if m != nil {
		return m.Params
	}
	return nil
}

type ReduceDesc struct {
	Type                 ReduceType `protobuf:"varint,1,opt,name=type,proto3,enum=rcmd.recall_index_es.ReduceType" json:"type,omitempty"`
	SortDesc             *SortDesc  `protobuf:"bytes,2,opt,name=sort_desc,json=sortDesc,proto3" json:"sort_desc,omitempty"`
	TargetSize           uint32     `protobuf:"varint,3,opt,name=target_size,json=targetSize,proto3" json:"target_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *ReduceDesc) Reset()         { *m = ReduceDesc{} }
func (m *ReduceDesc) String() string { return proto.CompactTextString(m) }
func (*ReduceDesc) ProtoMessage()    {}
func (*ReduceDesc) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{54}
}
func (m *ReduceDesc) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReduceDesc.Unmarshal(m, b)
}
func (m *ReduceDesc) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReduceDesc.Marshal(b, m, deterministic)
}
func (dst *ReduceDesc) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReduceDesc.Merge(dst, src)
}
func (m *ReduceDesc) XXX_Size() int {
	return xxx_messageInfo_ReduceDesc.Size(m)
}
func (m *ReduceDesc) XXX_DiscardUnknown() {
	xxx_messageInfo_ReduceDesc.DiscardUnknown(m)
}

var xxx_messageInfo_ReduceDesc proto.InternalMessageInfo

func (m *ReduceDesc) GetType() ReduceType {
	if m != nil {
		return m.Type
	}
	return ReduceType_UnknownReduceType
}

func (m *ReduceDesc) GetSortDesc() *SortDesc {
	if m != nil {
		return m.SortDesc
	}
	return nil
}

func (m *ReduceDesc) GetTargetSize() uint32 {
	if m != nil {
		return m.TargetSize
	}
	return 0
}

type RecallByCondReq struct {
	Index   string          `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	Conds   []*Condition    `protobuf:"bytes,2,rep,name=conds,proto3" json:"conds,omitempty"`
	Reduces []*ReduceDesc   `protobuf:"bytes,3,rep,name=reduces,proto3" json:"reduces,omitempty"`
	Chain   *FilterBoxChain `protobuf:"bytes,4,opt,name=chain,proto3" json:"chain,omitempty"`
	Fields  []string        `protobuf:"bytes,5,rep,name=fields,proto3" json:"fields,omitempty"`
	// TODO:将bloom_key和uid整合到filter chain
	BloomKey             BloomKey `protobuf:"varint,100,opt,name=bloom_key,json=bloomKey,proto3,enum=rcmd.recall_index_es.BloomKey" json:"bloom_key,omitempty"`
	Uid                  uint32   `protobuf:"varint,101,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallByCondReq) Reset()         { *m = RecallByCondReq{} }
func (m *RecallByCondReq) String() string { return proto.CompactTextString(m) }
func (*RecallByCondReq) ProtoMessage()    {}
func (*RecallByCondReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{55}
}
func (m *RecallByCondReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallByCondReq.Unmarshal(m, b)
}
func (m *RecallByCondReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallByCondReq.Marshal(b, m, deterministic)
}
func (dst *RecallByCondReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallByCondReq.Merge(dst, src)
}
func (m *RecallByCondReq) XXX_Size() int {
	return xxx_messageInfo_RecallByCondReq.Size(m)
}
func (m *RecallByCondReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallByCondReq.DiscardUnknown(m)
}

var xxx_messageInfo_RecallByCondReq proto.InternalMessageInfo

func (m *RecallByCondReq) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *RecallByCondReq) GetConds() []*Condition {
	if m != nil {
		return m.Conds
	}
	return nil
}

func (m *RecallByCondReq) GetReduces() []*ReduceDesc {
	if m != nil {
		return m.Reduces
	}
	return nil
}

func (m *RecallByCondReq) GetChain() *FilterBoxChain {
	if m != nil {
		return m.Chain
	}
	return nil
}

func (m *RecallByCondReq) GetFields() []string {
	if m != nil {
		return m.Fields
	}
	return nil
}

func (m *RecallByCondReq) GetBloomKey() BloomKey {
	if m != nil {
		return m.BloomKey
	}
	return BloomKey_BloomKeyInvalid
}

func (m *RecallByCondReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type RecallByCondRsp struct {
	Ids                  []string    `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Fields               []*FieldMap `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *RecallByCondRsp) Reset()         { *m = RecallByCondRsp{} }
func (m *RecallByCondRsp) String() string { return proto.CompactTextString(m) }
func (*RecallByCondRsp) ProtoMessage()    {}
func (*RecallByCondRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{56}
}
func (m *RecallByCondRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallByCondRsp.Unmarshal(m, b)
}
func (m *RecallByCondRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallByCondRsp.Marshal(b, m, deterministic)
}
func (dst *RecallByCondRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallByCondRsp.Merge(dst, src)
}
func (m *RecallByCondRsp) XXX_Size() int {
	return xxx_messageInfo_RecallByCondRsp.Size(m)
}
func (m *RecallByCondRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallByCondRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RecallByCondRsp proto.InternalMessageInfo

func (m *RecallByCondRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *RecallByCondRsp) GetFields() []*FieldMap {
	if m != nil {
		return m.Fields
	}
	return nil
}

type ItemList struct {
	Ids                  []string    `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	Fields               []*FieldMap `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *ItemList) Reset()         { *m = ItemList{} }
func (m *ItemList) String() string { return proto.CompactTextString(m) }
func (*ItemList) ProtoMessage()    {}
func (*ItemList) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{57}
}
func (m *ItemList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ItemList.Unmarshal(m, b)
}
func (m *ItemList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ItemList.Marshal(b, m, deterministic)
}
func (dst *ItemList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ItemList.Merge(dst, src)
}
func (m *ItemList) XXX_Size() int {
	return xxx_messageInfo_ItemList.Size(m)
}
func (m *ItemList) XXX_DiscardUnknown() {
	xxx_messageInfo_ItemList.DiscardUnknown(m)
}

var xxx_messageInfo_ItemList proto.InternalMessageInfo

func (m *ItemList) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *ItemList) GetFields() []*FieldMap {
	if m != nil {
		return m.Fields
	}
	return nil
}

// 按时间滚动的cuckoo filter存储在redis内的格式
type RollingCuckooFilterData struct {
	Segments             []*RollingCuckooFilterSegment `protobuf:"bytes,1,rep,name=segments,proto3" json:"segments,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *RollingCuckooFilterData) Reset()         { *m = RollingCuckooFilterData{} }
func (m *RollingCuckooFilterData) String() string { return proto.CompactTextString(m) }
func (*RollingCuckooFilterData) ProtoMessage()    {}
func (*RollingCuckooFilterData) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{58}
}
func (m *RollingCuckooFilterData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollingCuckooFilterData.Unmarshal(m, b)
}
func (m *RollingCuckooFilterData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollingCuckooFilterData.Marshal(b, m, deterministic)
}
func (dst *RollingCuckooFilterData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollingCuckooFilterData.Merge(dst, src)
}
func (m *RollingCuckooFilterData) XXX_Size() int {
	return xxx_messageInfo_RollingCuckooFilterData.Size(m)
}
func (m *RollingCuckooFilterData) XXX_DiscardUnknown() {
	xxx_messageInfo_RollingCuckooFilterData.DiscardUnknown(m)
}

var xxx_messageInfo_RollingCuckooFilterData proto.InternalMessageInfo

func (m *RollingCuckooFilterData) GetSegments() []*RollingCuckooFilterSegment {
	if m != nil {
		return m.Segments
	}
	return nil
}

type RollingCuckooFilterSegment struct {
	Ctime                uint32   `protobuf:"varint,1,opt,name=ctime,proto3" json:"ctime,omitempty"`
	Mtime                uint32   `protobuf:"varint,2,opt,name=mtime,proto3" json:"mtime,omitempty"`
	Count                uint32   `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Data                 []byte   `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RollingCuckooFilterSegment) Reset()         { *m = RollingCuckooFilterSegment{} }
func (m *RollingCuckooFilterSegment) String() string { return proto.CompactTextString(m) }
func (*RollingCuckooFilterSegment) ProtoMessage()    {}
func (*RollingCuckooFilterSegment) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_index_es_6f09b4163b40626d, []int{59}
}
func (m *RollingCuckooFilterSegment) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RollingCuckooFilterSegment.Unmarshal(m, b)
}
func (m *RollingCuckooFilterSegment) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RollingCuckooFilterSegment.Marshal(b, m, deterministic)
}
func (dst *RollingCuckooFilterSegment) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RollingCuckooFilterSegment.Merge(dst, src)
}
func (m *RollingCuckooFilterSegment) XXX_Size() int {
	return xxx_messageInfo_RollingCuckooFilterSegment.Size(m)
}
func (m *RollingCuckooFilterSegment) XXX_DiscardUnknown() {
	xxx_messageInfo_RollingCuckooFilterSegment.DiscardUnknown(m)
}

var xxx_messageInfo_RollingCuckooFilterSegment proto.InternalMessageInfo

func (m *RollingCuckooFilterSegment) GetCtime() uint32 {
	if m != nil {
		return m.Ctime
	}
	return 0
}

func (m *RollingCuckooFilterSegment) GetMtime() uint32 {
	if m != nil {
		return m.Mtime
	}
	return 0
}

func (m *RollingCuckooFilterSegment) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *RollingCuckooFilterSegment) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

func init() {
	proto.RegisterType((*RecallByActionReq)(nil), "rcmd.recall_index_es.RecallByActionReq")
	proto.RegisterType((*ReqActionItem)(nil), "rcmd.recall_index_es.ReqActionItem")
	proto.RegisterType((*RecallByActionRsp)(nil), "rcmd.recall_index_es.RecallByActionRsp")
	proto.RegisterType((*AlgoRecallItem)(nil), "rcmd.recall_index_es.AlgoRecallItem")
	proto.RegisterType((*RecallReq)(nil), "rcmd.recall_index_es.RecallReq")
	proto.RegisterType((*JoinFilter)(nil), "rcmd.recall_index_es.JoinFilter")
	proto.RegisterType((*Rsp)(nil), "rcmd.recall_index_es.Rsp")
	proto.RegisterType((*Label)(nil), "rcmd.recall_index_es.Label")
	proto.RegisterType((*RecallByKeyReq)(nil), "rcmd.recall_index_es.RecallByKeyReq")
	proto.RegisterType((*FieldValue)(nil), "rcmd.recall_index_es.FieldValue")
	proto.RegisterType((*FieldMap)(nil), "rcmd.recall_index_es.FieldMap")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.recall_index_es.FieldMap.FieldMapEntry")
	proto.RegisterType((*RecallByKeyRsp)(nil), "rcmd.recall_index_es.RecallByKeyRsp")
	proto.RegisterType((*RecallItem)(nil), "rcmd.recall_index_es.RecallItem")
	proto.RegisterType((*CreateReq)(nil), "rcmd.recall_index_es.CreateReq")
	proto.RegisterType((*SearchReq)(nil), "rcmd.recall_index_es.SearchReq")
	proto.RegisterType((*UpdateReq)(nil), "rcmd.recall_index_es.UpdateReq")
	proto.RegisterType((*UpdateByIdReq)(nil), "rcmd.recall_index_es.UpdateByIdReq")
	proto.RegisterType((*DelReq)(nil), "rcmd.recall_index_es.DelReq")
	proto.RegisterType((*UpdateEvent)(nil), "rcmd.recall_index_es.UpdateEvent")
	proto.RegisterType((*CreateEvent)(nil), "rcmd.recall_index_es.CreateEvent")
	proto.RegisterType((*DeleteEvent)(nil), "rcmd.recall_index_es.DeleteEvent")
	proto.RegisterType((*FilterBoxChain)(nil), "rcmd.recall_index_es.FilterBoxChain")
	proto.RegisterType((*FilterBox)(nil), "rcmd.recall_index_es.FilterBox")
	proto.RegisterType((*ApartFrom)(nil), "rcmd.recall_index_es.ApartFrom")
	proto.RegisterType((*MustNot)(nil), "rcmd.recall_index_es.MustNot")
	proto.RegisterType((*Bool)(nil), "rcmd.recall_index_es.Bool")
	proto.RegisterType((*InSet)(nil), "rcmd.recall_index_es.InSet")
	proto.RegisterType((*InRangeSet)(nil), "rcmd.recall_index_es.InRangeSet")
	proto.RegisterType((*UpdateSetReq)(nil), "rcmd.recall_index_es.UpdateSetReq")
	proto.RegisterType((*UpdateSetRsp)(nil), "rcmd.recall_index_es.UpdateSetRsp")
	proto.RegisterType((*BatchUpdateSetReq)(nil), "rcmd.recall_index_es.BatchUpdateSetReq")
	proto.RegisterType((*BatchUpdateSetRsp)(nil), "rcmd.recall_index_es.BatchUpdateSetRsp")
	proto.RegisterType((*UpdateRangeSetEvent)(nil), "rcmd.recall_index_es.UpdateRangeSetEvent")
	proto.RegisterType((*UpdateRangeSetReq)(nil), "rcmd.recall_index_es.UpdateRangeSetReq")
	proto.RegisterType((*UpdateRangeSetRsp)(nil), "rcmd.recall_index_es.UpdateRangeSetRsp")
	proto.RegisterType((*GetSetReq)(nil), "rcmd.recall_index_es.GetSetReq")
	proto.RegisterType((*GetSetRsp)(nil), "rcmd.recall_index_es.GetSetRsp")
	proto.RegisterType((*CheckInSetReq)(nil), "rcmd.recall_index_es.CheckInSetReq")
	proto.RegisterType((*CheckInSetRsp)(nil), "rcmd.recall_index_es.CheckInSetRsp")
	proto.RegisterType((*AddBloomReq)(nil), "rcmd.recall_index_es.AddBloomReq")
	proto.RegisterType((*AddBloomRsp)(nil), "rcmd.recall_index_es.AddBloomRsp")
	proto.RegisterType((*ResetBloomReq)(nil), "rcmd.recall_index_es.ResetBloomReq")
	proto.RegisterType((*ResetBloomRsp)(nil), "rcmd.recall_index_es.ResetBloomRsp")
	proto.RegisterType((*CountReq)(nil), "rcmd.recall_index_es.CountReq")
	proto.RegisterType((*CountRsp)(nil), "rcmd.recall_index_es.CountRsp")
	proto.RegisterType((*IndexEvent)(nil), "rcmd.recall_index_es.IndexEvent")
	proto.RegisterType((*IndexDesc)(nil), "rcmd.recall_index_es.IndexDesc")
	proto.RegisterType((*FilterReq)(nil), "rcmd.recall_index_es.FilterReq")
	proto.RegisterType((*FilterRsp)(nil), "rcmd.recall_index_es.FilterRsp")
	proto.RegisterType((*OnPersonaChangeReq)(nil), "rcmd.recall_index_es.OnPersonaChangeReq")
	proto.RegisterType((*OnPersonaChangeRsp)(nil), "rcmd.recall_index_es.OnPersonaChangeRsp")
	proto.RegisterType((*Condition)(nil), "rcmd.recall_index_es.Condition")
	proto.RegisterType((*Param)(nil), "rcmd.recall_index_es.Param")
	proto.RegisterType((*SortDesc)(nil), "rcmd.recall_index_es.SortDesc")
	proto.RegisterType((*ReduceDesc)(nil), "rcmd.recall_index_es.ReduceDesc")
	proto.RegisterType((*RecallByCondReq)(nil), "rcmd.recall_index_es.RecallByCondReq")
	proto.RegisterType((*RecallByCondRsp)(nil), "rcmd.recall_index_es.RecallByCondRsp")
	proto.RegisterType((*ItemList)(nil), "rcmd.recall_index_es.ItemList")
	proto.RegisterType((*RollingCuckooFilterData)(nil), "rcmd.recall_index_es.RollingCuckooFilterData")
	proto.RegisterType((*RollingCuckooFilterSegment)(nil), "rcmd.recall_index_es.RollingCuckooFilterSegment")
	proto.RegisterEnum("rcmd.recall_index_es.RecallStrategy", RecallStrategy_name, RecallStrategy_value)
	proto.RegisterEnum("rcmd.recall_index_es.FieldValueType", FieldValueType_name, FieldValueType_value)
	proto.RegisterEnum("rcmd.recall_index_es.ItemIdType", ItemIdType_name, ItemIdType_value)
	proto.RegisterEnum("rcmd.recall_index_es.ItemsStorage", ItemsStorage_name, ItemsStorage_value)
	proto.RegisterEnum("rcmd.recall_index_es.EventId", EventId_name, EventId_value)
	proto.RegisterEnum("rcmd.recall_index_es.SetKey", SetKey_name, SetKey_value)
	proto.RegisterEnum("rcmd.recall_index_es.RangeSetKey", RangeSetKey_name, RangeSetKey_value)
	proto.RegisterEnum("rcmd.recall_index_es.Op", Op_name, Op_value)
	proto.RegisterEnum("rcmd.recall_index_es.BloomKey", BloomKey_name, BloomKey_value)
	proto.RegisterEnum("rcmd.recall_index_es.Relation", Relation_name, Relation_value)
	proto.RegisterEnum("rcmd.recall_index_es.ParamType", ParamType_name, ParamType_value)
	proto.RegisterEnum("rcmd.recall_index_es.ReduceType", ReduceType_name, ReduceType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RecallIndexEsClient is the client API for RecallIndexEs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecallIndexEsClient interface {
	Recall(ctx context.Context, in *RecallReq, opts ...grpc.CallOption) (*Rsp, error)
	RecallByKey(ctx context.Context, in *RecallByKeyReq, opts ...grpc.CallOption) (*RecallByKeyRsp, error)
	RecallByCond(ctx context.Context, in *RecallByCondReq, opts ...grpc.CallOption) (*RecallByCondRsp, error)
	RecallByAction(ctx context.Context, in *RecallByActionReq, opts ...grpc.CallOption) (*RecallByActionRsp, error)
	UpdateSet(ctx context.Context, in *UpdateSetReq, opts ...grpc.CallOption) (*UpdateSetRsp, error)
	UpdateRangeSet(ctx context.Context, in *UpdateRangeSetReq, opts ...grpc.CallOption) (*UpdateRangeSetRsp, error)
	// 运营后台用。考虑这些数据是否由operating-platform同步到这边
	GetSet(ctx context.Context, in *GetSetReq, opts ...grpc.CallOption) (*GetSetRsp, error)
	CheckInSet(ctx context.Context, in *CheckInSetReq, opts ...grpc.CallOption) (*CheckInSetRsp, error)
	AddBloom(ctx context.Context, in *AddBloomReq, opts ...grpc.CallOption) (*AddBloomRsp, error)
	ResetBloom(ctx context.Context, in *ResetBloomReq, opts ...grpc.CallOption) (*ResetBloomRsp, error)
	Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*Rsp, error)
	Create(ctx context.Context, in *CreateReq, opts ...grpc.CallOption) (*Rsp, error)
	Update(ctx context.Context, in *UpdateReq, opts ...grpc.CallOption) (*Rsp, error)
	UpdateById(ctx context.Context, in *UpdateByIdReq, opts ...grpc.CallOption) (*Rsp, error)
	Del(ctx context.Context, in *DelReq, opts ...grpc.CallOption) (*Rsp, error)
	// 统计用接口
	Count(ctx context.Context, in *CountReq, opts ...grpc.CallOption) (*CountRsp, error)
	Filter(ctx context.Context, in *FilterReq, opts ...grpc.CallOption) (*FilterRsp, error)
	OnPersonaChange(ctx context.Context, in *OnPersonaChangeReq, opts ...grpc.CallOption) (*OnPersonaChangeRsp, error)
}

type recallIndexEsClient struct {
	cc *grpc.ClientConn
}

func NewRecallIndexEsClient(cc *grpc.ClientConn) RecallIndexEsClient {
	return &recallIndexEsClient{cc}
}

func (c *recallIndexEsClient) Recall(ctx context.Context, in *RecallReq, opts ...grpc.CallOption) (*Rsp, error) {
	out := new(Rsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Recall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) RecallByKey(ctx context.Context, in *RecallByKeyReq, opts ...grpc.CallOption) (*RecallByKeyRsp, error) {
	out := new(RecallByKeyRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/RecallByKey", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) RecallByCond(ctx context.Context, in *RecallByCondReq, opts ...grpc.CallOption) (*RecallByCondRsp, error) {
	out := new(RecallByCondRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/RecallByCond", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) RecallByAction(ctx context.Context, in *RecallByActionReq, opts ...grpc.CallOption) (*RecallByActionRsp, error) {
	out := new(RecallByActionRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/RecallByAction", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) UpdateSet(ctx context.Context, in *UpdateSetReq, opts ...grpc.CallOption) (*UpdateSetRsp, error) {
	out := new(UpdateSetRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/UpdateSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) UpdateRangeSet(ctx context.Context, in *UpdateRangeSetReq, opts ...grpc.CallOption) (*UpdateRangeSetRsp, error) {
	out := new(UpdateRangeSetRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/UpdateRangeSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) GetSet(ctx context.Context, in *GetSetReq, opts ...grpc.CallOption) (*GetSetRsp, error) {
	out := new(GetSetRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/GetSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) CheckInSet(ctx context.Context, in *CheckInSetReq, opts ...grpc.CallOption) (*CheckInSetRsp, error) {
	out := new(CheckInSetRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/CheckInSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) AddBloom(ctx context.Context, in *AddBloomReq, opts ...grpc.CallOption) (*AddBloomRsp, error) {
	out := new(AddBloomRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/AddBloom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) ResetBloom(ctx context.Context, in *ResetBloomReq, opts ...grpc.CallOption) (*ResetBloomRsp, error) {
	out := new(ResetBloomRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/ResetBloom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*Rsp, error) {
	out := new(Rsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Search", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) Create(ctx context.Context, in *CreateReq, opts ...grpc.CallOption) (*Rsp, error) {
	out := new(Rsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Create", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) Update(ctx context.Context, in *UpdateReq, opts ...grpc.CallOption) (*Rsp, error) {
	out := new(Rsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Update", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) UpdateById(ctx context.Context, in *UpdateByIdReq, opts ...grpc.CallOption) (*Rsp, error) {
	out := new(Rsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/UpdateById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) Del(ctx context.Context, in *DelReq, opts ...grpc.CallOption) (*Rsp, error) {
	out := new(Rsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Del", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) Count(ctx context.Context, in *CountReq, opts ...grpc.CallOption) (*CountRsp, error) {
	out := new(CountRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Count", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) Filter(ctx context.Context, in *FilterReq, opts ...grpc.CallOption) (*FilterRsp, error) {
	out := new(FilterRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/Filter", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallIndexEsClient) OnPersonaChange(ctx context.Context, in *OnPersonaChangeReq, opts ...grpc.CallOption) (*OnPersonaChangeRsp, error) {
	out := new(OnPersonaChangeRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_index_es.RecallIndexEs/OnPersonaChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecallIndexEsServer is the server API for RecallIndexEs service.
type RecallIndexEsServer interface {
	Recall(context.Context, *RecallReq) (*Rsp, error)
	RecallByKey(context.Context, *RecallByKeyReq) (*RecallByKeyRsp, error)
	RecallByCond(context.Context, *RecallByCondReq) (*RecallByCondRsp, error)
	RecallByAction(context.Context, *RecallByActionReq) (*RecallByActionRsp, error)
	UpdateSet(context.Context, *UpdateSetReq) (*UpdateSetRsp, error)
	UpdateRangeSet(context.Context, *UpdateRangeSetReq) (*UpdateRangeSetRsp, error)
	// 运营后台用。考虑这些数据是否由operating-platform同步到这边
	GetSet(context.Context, *GetSetReq) (*GetSetRsp, error)
	CheckInSet(context.Context, *CheckInSetReq) (*CheckInSetRsp, error)
	AddBloom(context.Context, *AddBloomReq) (*AddBloomRsp, error)
	ResetBloom(context.Context, *ResetBloomReq) (*ResetBloomRsp, error)
	Search(context.Context, *SearchReq) (*Rsp, error)
	Create(context.Context, *CreateReq) (*Rsp, error)
	Update(context.Context, *UpdateReq) (*Rsp, error)
	UpdateById(context.Context, *UpdateByIdReq) (*Rsp, error)
	Del(context.Context, *DelReq) (*Rsp, error)
	// 统计用接口
	Count(context.Context, *CountReq) (*CountRsp, error)
	Filter(context.Context, *FilterReq) (*FilterRsp, error)
	OnPersonaChange(context.Context, *OnPersonaChangeReq) (*OnPersonaChangeRsp, error)
}

func RegisterRecallIndexEsServer(s *grpc.Server, srv RecallIndexEsServer) {
	s.RegisterService(&_RecallIndexEs_serviceDesc, srv)
}

func _RecallIndexEs_Recall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Recall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Recall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Recall(ctx, req.(*RecallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_RecallByKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecallByKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).RecallByKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/RecallByKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).RecallByKey(ctx, req.(*RecallByKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_RecallByCond_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecallByCondReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).RecallByCond(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/RecallByCond",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).RecallByCond(ctx, req.(*RecallByCondReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_RecallByAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecallByActionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).RecallByAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/RecallByAction",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).RecallByAction(ctx, req.(*RecallByActionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_UpdateSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).UpdateSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/UpdateSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).UpdateSet(ctx, req.(*UpdateSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_UpdateRangeSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRangeSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).UpdateRangeSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/UpdateRangeSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).UpdateRangeSet(ctx, req.(*UpdateRangeSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_GetSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).GetSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/GetSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).GetSet(ctx, req.(*GetSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_CheckInSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).CheckInSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/CheckInSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).CheckInSet(ctx, req.(*CheckInSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_AddBloom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBloomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).AddBloom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/AddBloom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).AddBloom(ctx, req.(*AddBloomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_ResetBloom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetBloomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).ResetBloom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/ResetBloom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).ResetBloom(ctx, req.(*ResetBloomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Search",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Search(ctx, req.(*SearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_Create_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Create(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Create",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Create(ctx, req.(*CreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_Update_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Update(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Update",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Update(ctx, req.(*UpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_UpdateById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).UpdateById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/UpdateById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).UpdateById(ctx, req.(*UpdateByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_Del_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Del(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Del",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Del(ctx, req.(*DelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_Count_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Count(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Count",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Count(ctx, req.(*CountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_Filter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).Filter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/Filter",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).Filter(ctx, req.(*FilterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallIndexEs_OnPersonaChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnPersonaChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallIndexEsServer).OnPersonaChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_index_es.RecallIndexEs/OnPersonaChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallIndexEsServer).OnPersonaChange(ctx, req.(*OnPersonaChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RecallIndexEs_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.recall_index_es.RecallIndexEs",
	HandlerType: (*RecallIndexEsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Recall",
			Handler:    _RecallIndexEs_Recall_Handler,
		},
		{
			MethodName: "RecallByKey",
			Handler:    _RecallIndexEs_RecallByKey_Handler,
		},
		{
			MethodName: "RecallByCond",
			Handler:    _RecallIndexEs_RecallByCond_Handler,
		},
		{
			MethodName: "RecallByAction",
			Handler:    _RecallIndexEs_RecallByAction_Handler,
		},
		{
			MethodName: "UpdateSet",
			Handler:    _RecallIndexEs_UpdateSet_Handler,
		},
		{
			MethodName: "UpdateRangeSet",
			Handler:    _RecallIndexEs_UpdateRangeSet_Handler,
		},
		{
			MethodName: "GetSet",
			Handler:    _RecallIndexEs_GetSet_Handler,
		},
		{
			MethodName: "CheckInSet",
			Handler:    _RecallIndexEs_CheckInSet_Handler,
		},
		{
			MethodName: "AddBloom",
			Handler:    _RecallIndexEs_AddBloom_Handler,
		},
		{
			MethodName: "ResetBloom",
			Handler:    _RecallIndexEs_ResetBloom_Handler,
		},
		{
			MethodName: "Search",
			Handler:    _RecallIndexEs_Search_Handler,
		},
		{
			MethodName: "Create",
			Handler:    _RecallIndexEs_Create_Handler,
		},
		{
			MethodName: "Update",
			Handler:    _RecallIndexEs_Update_Handler,
		},
		{
			MethodName: "UpdateById",
			Handler:    _RecallIndexEs_UpdateById_Handler,
		},
		{
			MethodName: "Del",
			Handler:    _RecallIndexEs_Del_Handler,
		},
		{
			MethodName: "Count",
			Handler:    _RecallIndexEs_Count_Handler,
		},
		{
			MethodName: "Filter",
			Handler:    _RecallIndexEs_Filter_Handler,
		},
		{
			MethodName: "OnPersonaChange",
			Handler:    _RecallIndexEs_OnPersonaChange_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/recall/recall_index_es.proto",
}

func init() {
	proto.RegisterFile("rcmd/recall/recall_index_es.proto", fileDescriptor_recall_index_es_6f09b4163b40626d)
}

var fileDescriptor_recall_index_es_6f09b4163b40626d = []byte{
	// 3002 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x3a, 0x4b, 0x73, 0xdc, 0xc6,
	0xd1, 0x02, 0xf6, 0x89, 0x5e, 0xee, 0x0a, 0x1a, 0xd3, 0x16, 0xbd, 0xb2, 0x24, 0x1a, 0x9f, 0x5d,
	0x9f, 0x42, 0xbb, 0x28, 0x17, 0x69, 0x39, 0x12, 0x95, 0x28, 0xe1, 0x43, 0x52, 0xd6, 0xa6, 0x44,
	0x79, 0xd6, 0xb4, 0x13, 0x27, 0x55, 0x5b, 0x20, 0x30, 0x5c, 0xc2, 0xc2, 0x8b, 0xc0, 0x2c, 0x4d,
	0xea, 0x92, 0x4b, 0x92, 0x83, 0x93, 0x53, 0x72, 0xcb, 0x29, 0xe7, 0x54, 0xe5, 0x27, 0xe4, 0x1f,
	0xe4, 0x9a, 0x7f, 0x91, 0xaa, 0x54, 0xe5, 0x17, 0xa4, 0xe6, 0x05, 0x60, 0xc9, 0xc5, 0x72, 0x29,
	0x2b, 0x2e, 0x9f, 0x76, 0xa6, 0xd1, 0xdd, 0xd3, 0xd3, 0xaf, 0xe9, 0xe9, 0x59, 0x78, 0x3b, 0x71,
	0x02, 0xf7, 0x76, 0x42, 0x1c, 0xdb, 0xf7, 0xe5, 0xcf, 0xc0, 0x0b, 0x5d, 0x72, 0x3c, 0x20, 0xe9,
	0x72, 0x9c, 0x44, 0x34, 0x42, 0xf3, 0x0c, 0x65, 0xf9, 0xd4, 0xb7, 0xee, 0xcd, 0x09, 0x84, 0x4e,
	0x14, 0x04, 0x51, 0x28, 0xc8, 0xba, 0x5d, 0x8e, 0x10, 0x93, 0x24, 0x8d, 0x42, 0x5b, 0xfd, 0xca,
	0x6f, 0x0b, 0xfc, 0x9b, 0x40, 0xbf, 0x5d, 0xa4, 0xb2, 0xfe, 0xac, 0xc1, 0x15, 0xcc, 0xb9, 0x6d,
	0x9c, 0xac, 0x3b, 0xd4, 0x8b, 0x42, 0x4c, 0x0e, 0x91, 0x09, 0x95, 0x91, 0xe7, 0x2e, 0x68, 0x8b,
	0xda, 0xad, 0x36, 0x66, 0x43, 0x74, 0x15, 0x1a, 0x1e, 0x25, 0xc1, 0xc0, 0x73, 0x17, 0xf4, 0x45,
	0xed, 0x96, 0x81, 0xeb, 0x6c, 0xda, 0x73, 0xd1, 0x16, 0xb4, 0xe2, 0x24, 0xda, 0xf7, 0x7c, 0x32,
	0xf0, 0xdc, 0x74, 0xa1, 0xb2, 0x58, 0xb9, 0xd5, 0x5a, 0xf9, 0xbf, 0xe5, 0x49, 0x7b, 0x58, 0xc6,
	0xe4, 0x50, 0xac, 0xd1, 0xa3, 0x24, 0xc0, 0x20, 0xe9, 0x7a, 0x6e, 0x8a, 0xe6, 0xa1, 0xe6, 0x7b,
	0x81, 0x47, 0x17, 0xaa, 0x7c, 0x49, 0x31, 0xb1, 0xfe, 0xaa, 0x43, 0x7b, 0x8c, 0x06, 0x5d, 0x07,
	0xc8, 0x57, 0x93, 0xf2, 0x19, 0x19, 0x1f, 0xf4, 0x18, 0xe6, 0xf6, 0x3d, 0x9f, 0x92, 0x64, 0xe0,
	0x1c, 0xd8, 0x5e, 0xc8, 0x45, 0x6d, 0xad, 0xbc, 0x33, 0x59, 0x9a, 0x47, 0x1c, 0x73, 0x23, 0x3a,
	0xde, 0x64, 0xb8, 0xb8, 0x25, 0x28, 0xf9, 0x04, 0xdd, 0x07, 0x63, 0xcf, 0x8f, 0xa2, 0x60, 0xf0,
	0x9c, 0x9c, 0x2c, 0x54, 0x16, 0xb5, 0x5b, 0x9d, 0x95, 0x1b, 0x93, 0xb9, 0x6c, 0x30, 0xb4, 0x4f,
	0xc8, 0x09, 0x6e, 0xee, 0xc9, 0x11, 0xba, 0x0b, 0x46, 0x40, 0xa8, 0x3d, 0x70, 0x6d, 0x6a, 0xf3,
	0x0d, 0xb5, 0x56, 0xae, 0x09, 0x62, 0xa9, 0x7a, 0xa1, 0xf0, 0x27, 0x84, 0xda, 0x5b, 0x36, 0xb5,
	0x71, 0x33, 0x90, 0x23, 0xb4, 0x06, 0x8d, 0xfd, 0x28, 0x09, 0x46, 0xbe, 0xbd, 0x50, 0xe3, 0x8b,
	0x2e, 0x4e, 0xa0, 0x13, 0xea, 0x78, 0x24, 0xf0, 0xb0, 0x22, 0xb0, 0x76, 0xce, 0x18, 0x32, 0x8d,
	0xd1, 0x1a, 0xd4, 0x98, 0x9d, 0xd2, 0x05, 0x8d, 0xdb, 0xa5, 0x44, 0x13, 0xeb, 0xfe, 0x30, 0x12,
	0xb4, 0xdc, 0x30, 0x82, 0xc4, 0xfa, 0x83, 0x06, 0x9d, 0xf1, 0x2f, 0x45, 0x2f, 0xd0, 0xc6, 0xbc,
	0x60, 0x1e, 0x6a, 0xa9, 0x13, 0x25, 0x84, 0x6b, 0x5c, 0xc7, 0x62, 0x82, 0xde, 0x85, 0x4e, 0x42,
	0x98, 0xec, 0x24, 0x74, 0x07, 0xf4, 0x24, 0x26, 0xdc, 0x3d, 0x0c, 0xdc, 0xce, 0xa0, 0x9f, 0x9d,
	0xc4, 0x04, 0xbd, 0x03, 0x1d, 0xce, 0x35, 0xfa, 0x3a, 0x24, 0xc9, 0x80, 0x39, 0x9e, 0xf0, 0x82,
	0x39, 0x06, 0xdd, 0x61, 0xc0, 0x5d, 0xcf, 0xb5, 0xfe, 0xa5, 0x83, 0x21, 0x44, 0x99, 0xec, 0xa1,
	0x0f, 0x00, 0x52, 0x62, 0x27, 0xce, 0xc1, 0x20, 0x21, 0x87, 0xd2, 0xf2, 0x37, 0x27, 0xef, 0xb7,
	0xcf, 0xf1, 0x30, 0x39, 0xc4, 0x46, 0xaa, 0x86, 0x68, 0x1d, 0x5a, 0x5f, 0x45, 0x5e, 0x38, 0x10,
	0x6e, 0xc0, 0x8d, 0xde, 0x52, 0xfa, 0x3f, 0xcd, 0xe0, 0xe3, 0xc8, 0x0b, 0x85, 0xfb, 0x60, 0xf8,
	0x2a, 0x1b, 0xa3, 0x75, 0x98, 0x13, 0x5e, 0x23, 0x79, 0x54, 0x67, 0x72, 0x9c, 0x16, 0xa7, 0x91,
	0x2c, 0x9e, 0xc0, 0x65, 0x89, 0x98, 0xd2, 0xc4, 0xa6, 0x64, 0x78, 0x22, 0x3d, 0xe1, 0x9d, 0xb2,
	0x90, 0x62, 0xf3, 0xbe, 0xc4, 0xc5, 0x9d, 0x64, 0x6c, 0x3e, 0xee, 0x8a, 0xf5, 0x0b, 0xb8, 0xa2,
	0xb5, 0x0b, 0x90, 0xef, 0xf2, 0x4c, 0x60, 0x69, 0x2f, 0x19, 0x58, 0x56, 0x0c, 0x15, 0xe6, 0x97,
	0x08, 0xaa, 0x5f, 0xa5, 0x51, 0x28, 0xbd, 0x88, 0x8f, 0xd1, 0x9b, 0xd0, 0x24, 0xe9, 0xc0, 0x89,
	0x46, 0x21, 0xe5, 0xe6, 0x6b, 0xe3, 0x06, 0x49, 0x37, 0xd9, 0x14, 0xbd, 0x0d, 0x73, 0xd2, 0xb6,
	0xf6, 0xbe, 0x32, 0x8e, 0x81, 0x5b, 0x02, 0xb6, 0xce, 0x40, 0xdc, 0x35, 0xdd, 0x81, 0xef, 0xa5,
	0x2c, 0x87, 0x54, 0xb8, 0x6b, 0xba, 0xdb, 0x5e, 0x4a, 0xad, 0x9f, 0x41, 0x6d, 0xdb, 0xde, 0x23,
	0x3e, 0xfa, 0x01, 0x54, 0x58, 0x34, 0x6b, 0x5c, 0x9d, 0x57, 0xc7, 0x44, 0x97, 0xca, 0x60, 0xd6,
	0x60, 0x38, 0xcc, 0x9d, 0x8f, 0x6c, 0x7f, 0x44, 0x64, 0xae, 0x13, 0x13, 0xeb, 0x9b, 0x2a, 0x74,
	0x54, 0x88, 0x31, 0x54, 0x72, 0xc8, 0x78, 0xda, 0x71, 0x3c, 0x8d, 0xe7, 0x7a, 0x1c, 0x63, 0x86,
	0x83, 0xde, 0x87, 0x2a, 0xf3, 0x67, 0xce, 0xb2, 0xb3, 0xb2, 0x30, 0x09, 0x97, 0x47, 0x1f, 0xc7,
	0x42, 0xab, 0x50, 0xf7, 0x99, 0xd4, 0x2a, 0xa3, 0x5e, 0x9b, 0xac, 0x6a, 0xbe, 0x33, 0x2c, 0x51,
	0xd1, 0x06, 0xcc, 0xc9, 0xf0, 0x14, 0xd1, 0x56, 0x2d, 0xe6, 0x90, 0xd3, 0xa4, 0x3d, 0x1e, 0xb9,
	0x2c, 0x00, 0x31, 0x78, 0xd9, 0x18, 0xfd, 0x08, 0x1a, 0x29, 0x8d, 0x12, 0x7b, 0x48, 0xa4, 0xe3,
	0x59, 0xe5, 0xe4, 0x69, 0x5f, 0x60, 0x62, 0x45, 0x72, 0xc6, 0x4f, 0xea, 0x2f, 0x9b, 0x80, 0x65,
	0x7c, 0x37, 0xf2, 0xf8, 0x1e, 0x4b, 0xc9, 0xcd, 0x6f, 0x93, 0x92, 0x8d, 0x8b, 0xa4, 0xe4, 0x37,
	0xa0, 0xbe, 0xef, 0x11, 0xdf, 0x4d, 0x17, 0x40, 0xb8, 0x95, 0x98, 0x59, 0xff, 0xd6, 0x00, 0x1e,
	0xb1, 0xe1, 0xe7, 0xcc, 0x37, 0xd0, 0x5d, 0xa8, 0x72, 0x95, 0x6b, 0xd3, 0x82, 0x35, 0xc7, 0xe7,
	0x6a, 0xe7, 0x14, 0xdc, 0xb7, 0x69, 0xe2, 0x85, 0xc3, 0x41, 0xd1, 0xe5, 0x5a, 0x02, 0x26, 0x98,
	0xaf, 0x73, 0x94, 0x91, 0x43, 0x25, 0x8a, 0xc8, 0x4d, 0x37, 0xa6, 0x2c, 0xf2, 0xc4, 0x8e, 0x39,
	0x8b, 0x91, 0x43, 0x15, 0x8b, 0x96, 0x9d, 0x24, 0xf6, 0x89, 0xe4, 0x50, 0xe5, 0x4e, 0xb5, 0x78,
	0x9e, 0x98, 0x18, 0x38, 0x11, 0x1f, 0x5b, 0x7f, 0xd4, 0xa0, 0xa9, 0x98, 0xa3, 0x1e, 0x18, 0x5c,
	0x11, 0x83, 0xc0, 0x8e, 0xe5, 0xe1, 0xf2, 0xfe, 0x74, 0x79, 0xb2, 0xc1, 0xc3, 0x90, 0x26, 0x27,
	0xb8, 0xb9, 0x2f, 0xa7, 0xdd, 0xfb, 0xd0, 0x1e, 0xfb, 0xc4, 0x6c, 0xaf, 0x02, 0xd5, 0x98, 0x12,
	0x8f, 0x6b, 0xfa, 0x5d, 0xcd, 0xfa, 0x72, 0x3c, 0x24, 0xd3, 0x98, 0x51, 0xb3, 0x42, 0x44, 0xe7,
	0xd6, 0x62, 0x43, 0xf4, 0x51, 0x66, 0x42, 0x11, 0x4b, 0xe7, 0x29, 0x4e, 0x99, 0x98, 0x02, 0x14,
	0xce, 0xbe, 0x0e, 0xe8, 0xd9, 0x81, 0xa3, 0x7b, 0x2e, 0xba, 0xc7, 0x13, 0x0e, 0x37, 0xba, 0x3e,
	0x63, 0x9c, 0xd5, 0x3d, 0x11, 0x63, 0xd7, 0xc0, 0xf0, 0xdc, 0x81, 0xb0, 0xb0, 0xcc, 0x65, 0x4d,
	0xcf, 0xed, 0xf3, 0xb9, 0xf5, 0x14, 0x8c, 0xcd, 0x84, 0xd8, 0x94, 0xb0, 0xfc, 0x72, 0x1d, 0x40,
	0x30, 0x0a, 0xed, 0x80, 0x48, 0x8d, 0x18, 0x1c, 0xf2, 0xd4, 0x0e, 0x88, 0x94, 0x49, 0x28, 0x85,
	0xc9, 0xa4, 0xd2, 0x6a, 0x25, 0x4f, 0xab, 0xd6, 0xcf, 0xc1, 0xc8, 0xce, 0xbb, 0xf3, 0xf8, 0x29,
	0x7a, 0xbd, 0x90, 0x96, 0x17, 0xa0, 0x91, 0x90, 0xfd, 0x84, 0xa4, 0x07, 0x9c, 0x6d, 0x13, 0xab,
	0xa9, 0xf5, 0x00, 0x8c, 0xdd, 0xd8, 0x9d, 0x4d, 0xd2, 0x09, 0x9c, 0x2d, 0x0c, 0x6d, 0x41, 0xbf,
	0x71, 0xd2, 0x73, 0x5f, 0xd1, 0x6e, 0x3f, 0x81, 0xfa, 0x16, 0xf1, 0x5f, 0x11, 0xb3, 0x6f, 0x34,
	0x68, 0x09, 0x09, 0x1f, 0x1e, 0x91, 0x90, 0xbe, 0x02, 0x96, 0xe8, 0x2e, 0x34, 0x09, 0xe3, 0x35,
	0x90, 0x55, 0x4e, 0x67, 0xe5, 0xfa, 0x64, 0xb7, 0xe1, 0x2b, 0xf6, 0x5c, 0xdc, 0x20, 0x62, 0xc0,
	0x85, 0x11, 0x8e, 0xf1, 0x3d, 0x11, 0x66, 0x8b, 0xf8, 0xe4, 0x7b, 0x21, 0xcc, 0x63, 0xe8, 0x8c,
	0x1f, 0x25, 0xe8, 0x0e, 0xd4, 0x54, 0x9d, 0x52, 0x29, 0x2f, 0x03, 0x33, 0x22, 0x2c, 0xb0, 0xad,
	0xdf, 0x6a, 0x60, 0x64, 0x40, 0x56, 0x50, 0xda, 0xb1, 0x9d, 0xd0, 0xc1, 0x7e, 0x12, 0x05, 0xb2,
	0xe2, 0x29, 0xe1, 0xb4, 0xce, 0xf0, 0x1e, 0x25, 0x51, 0x80, 0x0d, 0x5b, 0x0d, 0xd9, 0x86, 0x82,
	0x51, 0x4a, 0x07, 0x61, 0x44, 0x65, 0x39, 0x5a, 0xb2, 0xa1, 0x27, 0xa3, 0x94, 0x3e, 0x8d, 0x28,
	0x6e, 0x04, 0x62, 0x60, 0xdd, 0x07, 0x23, 0xe3, 0x88, 0x96, 0xa1, 0xba, 0x17, 0x45, 0xbe, 0xdc,
	0x4a, 0xb7, 0xe4, 0xc8, 0x8b, 0x22, 0x1f, 0x73, 0x3c, 0xeb, 0x1e, 0x34, 0x24, 0xc3, 0x0b, 0x93,
	0xfe, 0x43, 0x83, 0x2a, 0x9b, 0xb2, 0x7c, 0xcb, 0x73, 0xa0, 0xb4, 0xa4, 0x98, 0xa0, 0x15, 0xa8,
	0x7b, 0xe1, 0x20, 0x25, 0x6a, 0x3b, 0x25, 0x35, 0x49, 0x2f, 0xec, 0x13, 0x8a, 0x6b, 0x1e, 0xfb,
	0xe1, 0x25, 0x49, 0x38, 0x48, 0xec, 0x70, 0x48, 0x38, 0xe5, 0xd4, 0xb2, 0xba, 0x17, 0x62, 0x86,
	0xc8, 0xc8, 0xc1, 0xcb, 0xc6, 0xc8, 0x82, 0x4a, 0x9c, 0x39, 0x85, 0x29, 0x48, 0xd5, 0xfd, 0xb6,
	0xe7, 0x62, 0xf6, 0x91, 0x79, 0x14, 0x77, 0xbd, 0x9a, 0xf0, 0x28, 0x36, 0xb6, 0x7e, 0x08, 0x35,
	0x2e, 0x0b, 0x5a, 0x2e, 0x56, 0x7e, 0x6f, 0x95, 0xdd, 0x09, 0xa8, 0x2a, 0xff, 0x2c, 0x02, 0x90,
	0x8b, 0x82, 0x56, 0x8b, 0xd4, 0x6f, 0x97, 0x94, 0xe1, 0x12, 0x39, 0xab, 0x20, 0x4d, 0xa8, 0x04,
	0x9e, 0x4a, 0x77, 0x6c, 0xc8, 0x21, 0xf6, 0xb1, 0x74, 0x79, 0x36, 0xb4, 0x5e, 0xc0, 0x9c, 0xc8,
	0x2e, 0x6c, 0xc3, 0xe4, 0xf0, 0xa2, 0x62, 0xa2, 0x5b, 0xa0, 0x47, 0xf1, 0xc4, 0x7a, 0x32, 0x43,
	0xdf, 0x89, 0xb1, 0x1e, 0xf1, 0x33, 0xf1, 0xc8, 0xf6, 0xd5, 0xda, 0x47, 0xb6, 0x6f, 0x7d, 0x58,
	0x5c, 0x5b, 0x14, 0xe4, 0x4e, 0xe4, 0x12, 0x79, 0xbe, 0xf1, 0x31, 0x97, 0x38, 0x1d, 0x66, 0x7b,
	0x48, 0x87, 0xd6, 0xaf, 0xe1, 0xca, 0x86, 0x4d, 0x9d, 0x83, 0xef, 0x4e, 0x6c, 0xd5, 0x53, 0x10,
	0x47, 0xb9, 0x75, 0xef, 0x8c, 0x00, 0x33, 0xcb, 0xfe, 0x3b, 0x0d, 0x5e, 0x93, 0xc7, 0x95, 0x34,
	0x96, 0x48, 0x5d, 0x2f, 0x65, 0xde, 0x6f, 0xa3, 0xfa, 0xdf, 0x68, 0x70, 0x65, 0x5c, 0x10, 0xa6,
	0xc5, 0xef, 0x5c, 0x8c, 0x7b, 0x67, 0xa4, 0x98, 0x59, 0x95, 0x1e, 0x18, 0x8f, 0x09, 0x7d, 0x49,
	0xf3, 0x67, 0xad, 0x1e, 0xbd, 0xd0, 0xea, 0x61, 0x65, 0x76, 0xb4, 0xbf, 0xaf, 0x32, 0x44, 0x1b,
	0xcb, 0x99, 0xb5, 0x96, 0x2d, 0x95, 0x97, 0x76, 0x5a, 0x5e, 0xda, 0x5d, 0x03, 0x83, 0x46, 0xd4,
	0xf6, 0x07, 0xe1, 0x28, 0x90, 0x0c, 0x9b, 0x1c, 0xf0, 0x74, 0x14, 0x58, 0x9f, 0x42, 0x7b, 0xf3,
	0x80, 0x38, 0xcf, 0x45, 0x42, 0x7a, 0x09, 0x51, 0xcf, 0x94, 0x92, 0xd6, 0xe3, 0x31, 0x96, 0xb3,
	0x2a, 0x6c, 0x82, 0x23, 0xff, 0x5e, 0x83, 0xd6, 0xba, 0xeb, 0xf2, 0xab, 0x0a, 0x13, 0xed, 0x83,
	0xa2, 0x68, 0xe7, 0xdd, 0x6b, 0x94, 0x70, 0x23, 0x79, 0xa8, 0xca, 0x1b, 0xd2, 0x62, 0x7e, 0xfd,
	0xe3, 0xf7, 0x60, 0xb1, 0x9c, 0xbc, 0xdc, 0xb1, 0xbb, 0x30, 0xd3, 0x32, 0x39, 0x8e, 0xbd, 0x44,
	0x5c, 0x0d, 0xab, 0x58, 0xce, 0xac, 0xd5, 0x82, 0x30, 0x33, 0x7b, 0x41, 0x1f, 0xda, 0x98, 0xa4,
	0x84, 0xbe, 0xca, 0x3d, 0x58, 0x77, 0xc6, 0x98, 0xce, 0x2c, 0xcb, 0x8f, 0xa1, 0xc9, 0x3b, 0x05,
	0x2f, 0x59, 0x89, 0xbe, 0xa5, 0xc8, 0x85, 0x93, 0x39, 0x21, 0x55, 0x9d, 0x25, 0x27, 0xa4, 0xd6,
	0x9f, 0x74, 0x76, 0x1e, 0xb8, 0xe4, 0x58, 0x24, 0x8c, 0xf7, 0x78, 0xd0, 0x89, 0x5d, 0x5e, 0x9b,
	0x78, 0x8d, 0x67, 0xb8, 0x32, 0xee, 0x1e, 0x28, 0x61, 0x5c, 0x92, 0x3a, 0xd3, 0xbb, 0x52, 0x9c,
	0x6c, 0x8b, 0xa4, 0x8e, 0x94, 0x96, 0x0d, 0xbf, 0xe5, 0x75, 0x5c, 0xfa, 0x5d, 0x7d, 0xd2, 0x5d,
	0xa8, 0x71, 0x91, 0xbb, 0x10, 0xe3, 0x44, 0xa9, 0xcf, 0xef, 0xdd, 0x6d, 0xcc, 0x86, 0xd6, 0x5f,
	0x34, 0x30, 0x32, 0x91, 0xff, 0x77, 0x8d, 0x90, 0x35, 0x00, 0xde, 0xdd, 0xc8, 0x5d, 0xfa, 0x9c,
	0x66, 0x88, 0xc1, 0xd1, 0x79, 0xeb, 0xe7, 0x3f, 0x59, 0x3d, 0x27, 0x5b, 0x86, 0xa7, 0xb2, 0xc7,
	0x9a, 0x2a, 0x13, 0x2f, 0xd2, 0x27, 0x16, 0x24, 0xca, 0x75, 0x2b, 0x25, 0x0d, 0x8a, 0xea, 0x05,
	0x1b, 0x14, 0x57, 0xa1, 0xe1, 0x46, 0x0e, 0xbf, 0x4d, 0x8b, 0x12, 0xa6, 0xee, 0x46, 0x0e, 0xbb,
	0x68, 0xdf, 0x86, 0x6a, 0x40, 0x66, 0x6b, 0xde, 0x71, 0x44, 0xeb, 0x7a, 0xb6, 0xe7, 0x49, 0x19,
	0xd3, 0xfa, 0x18, 0xd0, 0x4e, 0xf8, 0x4c, 0x54, 0x4f, 0x9b, 0x07, 0x2c, 0xf5, 0x33, 0xdd, 0xc8,
	0x12, 0x4b, 0x9b, 0x56, 0x62, 0xc9, 0x6b, 0xb9, 0x9e, 0x5d, 0xcb, 0xad, 0xb5, 0xb3, 0xbc, 0x66,
	0x8e, 0xd8, 0x14, 0x8c, 0xcd, 0x28, 0x74, 0x3d, 0xea, 0x45, 0x61, 0x49, 0xbd, 0xb9, 0x06, 0xcd,
	0x84, 0xf8, 0x36, 0xc3, 0x90, 0xce, 0x72, 0xa3, 0xac, 0x09, 0x2a, 0xb0, 0x70, 0x86, 0x9f, 0x77,
	0x0c, 0x2a, 0xc5, 0x0e, 0xde, 0x3e, 0xd4, 0x9e, 0xd9, 0x89, 0x1d, 0x64, 0xe5, 0xa2, 0x96, 0x97,
	0x8b, 0x93, 0x9b, 0x0c, 0x68, 0x55, 0x36, 0x76, 0xc4, 0x23, 0x40, 0x49, 0xe8, 0x72, 0xa6, 0x79,
	0x4f, 0xc7, 0xea, 0x43, 0xb3, 0x1f, 0x25, 0x94, 0x47, 0x06, 0x82, 0x2a, 0x39, 0x8e, 0x13, 0xb5,
	0x14, 0x1b, 0xa3, 0x55, 0xa8, 0xc7, 0x8c, 0x44, 0x9c, 0x2d, 0xa5, 0x0e, 0xcd, 0xd9, 0x62, 0x89,
	0xca, 0x02, 0x0e, 0x30, 0x71, 0x47, 0x0e, 0xe1, 0x7c, 0x3f, 0x1c, 0xeb, 0x38, 0x2d, 0x96, 0x69,
	0x86, 0xe1, 0x17, 0xba, 0x4d, 0xf7, 0xc1, 0x48, 0xa3, 0x84, 0x16, 0xd3, 0x51, 0x89, 0x52, 0xd5,
	0x06, 0x70, 0x33, 0x55, 0x5b, 0xb9, 0x09, 0x2d, 0x6a, 0x27, 0x43, 0x42, 0x07, 0xa9, 0xf7, 0x82,
	0x48, 0xdf, 0x07, 0x01, 0xea, 0x7b, 0x2f, 0x88, 0xf5, 0x77, 0x1d, 0x2e, 0xab, 0x76, 0x0c, 0xb3,
	0x2e, 0x73, 0xad, 0x79, 0xa8, 0x71, 0x9e, 0xca, 0xb6, 0x7c, 0xc2, 0x6f, 0x68, 0x51, 0xe8, 0x2a,
	0x05, 0x94, 0xe8, 0x35, 0xf3, 0x10, 0x2c, 0xb0, 0xd1, 0x1a, 0x34, 0x12, 0xbe, 0x25, 0xd5, 0xcb,
	0x99, 0xba, 0x6f, 0x2e, 0xbe, 0x22, 0xc8, 0xa3, 0xbd, 0x7a, 0xf1, 0x68, 0xcf, 0xbb, 0x80, 0xb5,
	0x62, 0x17, 0x70, 0x3c, 0xe6, 0xdd, 0x0b, 0xc6, 0xbc, 0x4c, 0x21, 0x24, 0x3f, 0xfd, 0x7e, 0x79,
	0x4a, 0x7d, 0x13, 0x6b, 0x9e, 0x3c, 0x85, 0xeb, 0x17, 0x6a, 0x67, 0x7d, 0x06, 0x4d, 0x96, 0x57,
	0x79, 0x21, 0xf0, 0xea, 0xb8, 0x0e, 0xe1, 0x2a, 0x8e, 0x7c, 0xdf, 0x0b, 0x87, 0x9b, 0x23, 0xe7,
	0x79, 0x14, 0x09, 0xfd, 0xf1, 0xd6, 0xe9, 0x36, 0x34, 0x53, 0x32, 0x0c, 0x48, 0x48, 0xd5, 0xfb,
	0xd3, 0x07, 0x25, 0xd6, 0x3a, 0xcb, 0xa0, 0x2f, 0x08, 0x71, 0xc6, 0xc1, 0x4a, 0xa0, 0x5b, 0x8e,
	0xc7, 0xbc, 0xcc, 0xa1, 0x5e, 0xa0, 0xb2, 0x8e, 0x98, 0x30, 0x68, 0xc0, 0xa1, 0xb2, 0xd6, 0x0c,
	0x14, 0x54, 0xbc, 0x32, 0x54, 0x24, 0x2e, 0x7f, 0x63, 0x40, 0x50, 0xcd, 0x1e, 0xec, 0xe6, 0x30,
	0x1f, 0x2f, 0x6d, 0xa9, 0xee, 0x62, 0xf6, 0xa0, 0xf2, 0x26, 0xbc, 0xbe, 0x1b, 0x3e, 0x0f, 0xa3,
	0xaf, 0xc3, 0xf1, 0x0f, 0xe6, 0x25, 0x74, 0x15, 0x5e, 0x13, 0xb0, 0x4f, 0x47, 0x64, 0x44, 0xb2,
	0x0f, 0xda, 0xd2, 0xa7, 0xd0, 0x19, 0xef, 0xfc, 0xa2, 0x79, 0x30, 0x45, 0xb7, 0x2f, 0x87, 0x9b,
	0x97, 0x24, 0x74, 0xe4, 0xd0, 0x02, 0x54, 0x43, 0xaf, 0xc1, 0xe5, 0xf5, 0x24, 0xb1, 0x4f, 0x0a,
	0x40, 0x7d, 0xe9, 0x03, 0x80, 0xbc, 0xaf, 0x88, 0x4c, 0x98, 0xdb, 0xf5, 0x42, 0xba, 0xba, 0x22,
	0x60, 0xe6, 0x25, 0x06, 0x11, 0x0b, 0x48, 0x88, 0xb6, 0xf4, 0x1e, 0xcc, 0x15, 0x6b, 0x04, 0x34,
	0x07, 0x4d, 0x4c, 0x5c, 0x2f, 0xed, 0x13, 0x6a, 0x5e, 0x42, 0x6d, 0x30, 0xf8, 0x8c, 0x39, 0x87,
	0xa9, 0x2d, 0xfd, 0x4d, 0x87, 0x86, 0x6c, 0xb3, 0xa0, 0x16, 0x34, 0x7a, 0xe1, 0x91, 0xed, 0x7b,
	0x8c, 0x6f, 0x07, 0xe0, 0x59, 0x94, 0x52, 0xd1, 0x87, 0x32, 0x35, 0x35, 0x17, 0xad, 0x20, 0x53,
	0x47, 0x97, 0xa1, 0xc5, 0xe6, 0xcf, 0x12, 0xef, 0x88, 0x21, 0x54, 0x14, 0xc2, 0xc3, 0xe3, 0x38,
	0x4a, 0x89, 0x59, 0x65, 0x82, 0xb1, 0xf9, 0x3a, 0xa5, 0x1e, 0x1d, 0xb9, 0xc4, 0xac, 0x29, 0x92,
	0x4d, 0xfe, 0x20, 0x48, 0xcd, 0x3a, 0x42, 0xd0, 0x61, 0x00, 0xfe, 0xf0, 0x87, 0x49, 0xec, 0x9f,
	0x98, 0x0d, 0xc1, 0x26, 0xf2, 0xc5, 0x21, 0x63, 0x36, 0xd1, 0x1b, 0x80, 0x18, 0xce, 0x86, 0xed,
	0xf6, 0x9d, 0x28, 0x21, 0x12, 0x6e, 0x30, 0x1b, 0x8c, 0x52, 0xfe, 0x86, 0x40, 0x37, 0xed, 0xc4,
	0x7d, 0x36, 0xda, 0xf3, 0xbd, 0xf4, 0xc0, 0x04, 0xf4, 0x3a, 0x5c, 0x29, 0x7e, 0xd8, 0xf4, 0x99,
	0x38, 0x2d, 0x06, 0xde, 0xf6, 0x8e, 0x38, 0x7d, 0x48, 0x14, 0xfb, 0x39, 0xc6, 0xbe, 0xcf, 0x1c,
	0x2d, 0x07, 0x87, 0xc4, 0x37, 0xdb, 0x85, 0x65, 0xb7, 0xc9, 0x51, 0x86, 0xdf, 0x59, 0x3a, 0x86,
	0xba, 0xb8, 0x37, 0xa0, 0x2b, 0xd0, 0x16, 0xa3, 0x5c, 0x67, 0x97, 0xa1, 0x25, 0xb6, 0xff, 0xc5,
	0x81, 0xc7, 0x95, 0x86, 0xa0, 0x23, 0x00, 0xbb, 0x9e, 0x2b, 0x60, 0x3a, 0x33, 0xc0, 0x86, 0x6f,
	0x3b, 0xcf, 0x19, 0x7b, 0xb3, 0xc2, 0x95, 0x22, 0x74, 0xc8, 0x01, 0x55, 0x64, 0x40, 0xad, 0x97,
	0x3e, 0xb1, 0x43, 0xb3, 0xc6, 0x0d, 0x92, 0x7e, 0x11, 0x05, 0x76, 0x68, 0xd6, 0x97, 0x7e, 0x02,
	0xad, 0xc2, 0xad, 0x90, 0x09, 0x58, 0x98, 0xe6, 0x32, 0x2c, 0xc0, 0x3c, 0x76, 0x02, 0x97, 0x31,
	0xeb, 0xd3, 0x84, 0xd8, 0x81, 0x34, 0x88, 0xb6, 0x74, 0x0f, 0xf4, 0x9d, 0x98, 0xe9, 0x77, 0x27,
	0x1e, 0xe4, 0xf8, 0x00, 0xf5, 0x9d, 0x78, 0xb0, 0xee, 0xba, 0xa6, 0x26, 0xc7, 0x5b, 0xc4, 0x37,
	0x75, 0xb6, 0x36, 0xc7, 0x73, 0x12, 0xb3, 0xb2, 0xf4, 0x02, 0x9a, 0x2a, 0xab, 0x31, 0x2f, 0x55,
	0xe3, 0x9c, 0xcb, 0x3c, 0x98, 0xbb, 0x43, 0x07, 0xab, 0xc7, 0x5e, 0xbe, 0x15, 0xee, 0xd0, 0xca,
	0x0c, 0x5b, 0xc4, 0xf7, 0x8e, 0x48, 0x62, 0xea, 0x4c, 0xf0, 0x82, 0x21, 0x14, 0xbc, 0x72, 0xca,
	0x12, 0x0a, 0x5e, 0x5d, 0xba, 0xc3, 0xdc, 0x57, 0x9e, 0xf5, 0x06, 0xd4, 0x1e, 0x1e, 0x8e, 0x6c,
	0x5f, 0xf8, 0xf1, 0x76, 0xf4, 0x35, 0x49, 0x3e, 0x3b, 0xb0, 0x43, 0x53, 0x63, 0x6a, 0x7c, 0xcc,
	0x5d, 0x55, 0x00, 0xf4, 0xa5, 0x9f, 0x82, 0x91, 0x9d, 0xd5, 0x5c, 0x3c, 0x11, 0xcb, 0x19, 0x4c,
	0x98, 0x4b, 0x84, 0x0e, 0x07, 0x9a, 0x1a, 0x8b, 0x94, 0x5e, 0x48, 0xc5, 0x4c, 0x5f, 0xea, 0xa9,
	0x43, 0x98, 0xb3, 0x78, 0x1d, 0xae, 0x64, 0xe9, 0x40, 0x01, 0x45, 0xf8, 0x89, 0xf9, 0xc6, 0x09,
	0x3b, 0x46, 0x85, 0xcd, 0x15, 0x04, 0xdb, 0xa1, 0x1b, 0x05, 0xa6, 0xbe, 0xf2, 0xcf, 0x16, 0xbb,
	0xec, 0xf0, 0x07, 0x06, 0x7e, 0xbb, 0x48, 0xd1, 0x16, 0xd4, 0x05, 0x00, 0xdd, 0x9c, 0xf6, 0xdc,
	0x8b, 0xc9, 0x61, 0xf7, 0xcd, 0x12, 0x84, 0x34, 0x46, 0xbf, 0x80, 0x56, 0xe1, 0x4d, 0x04, 0x4d,
	0x7d, 0x39, 0x56, 0x2f, 0x99, 0xdd, 0x19, 0xb0, 0xd2, 0x18, 0xfd, 0x8a, 0x6d, 0x2c, 0x3f, 0xa0,
	0xd0, 0xbb, 0xd3, 0xa9, 0x64, 0x0d, 0xd0, 0x9d, 0x05, 0x2d, 0x8d, 0xd1, 0x5e, 0xfe, 0x98, 0x23,
	0xfe, 0xc2, 0x80, 0xfe, 0x7f, 0x3a, 0x61, 0xf6, 0x8f, 0x95, 0xee, 0x6c, 0x88, 0x69, 0x8c, 0xfa,
	0xea, 0xd1, 0x82, 0x77, 0x16, 0x27, 0x53, 0x15, 0xdb, 0x5b, 0xdd, 0x73, 0x71, 0x84, 0xe0, 0xe3,
	0xbd, 0x94, 0x32, 0xc1, 0xcf, 0xf4, 0x7d, 0xba, 0xb3, 0x21, 0xa6, 0x31, 0xfa, 0x18, 0xea, 0xa2,
	0x13, 0x52, 0xe6, 0x1b, 0x59, 0x4b, 0xa6, 0x3b, 0x1d, 0x21, 0x8d, 0xd1, 0xe7, 0x00, 0x79, 0x1b,
	0x03, 0x95, 0xfc, 0x5b, 0x67, 0xac, 0x77, 0xd2, 0x3d, 0x1f, 0x29, 0x8d, 0xd1, 0x33, 0x68, 0xaa,
	0x3e, 0x02, 0x2a, 0xe9, 0x61, 0x15, 0x9a, 0x1e, 0xdd, 0xf3, 0x50, 0x84, 0xa4, 0x79, 0x3f, 0x00,
	0x95, 0xfe, 0xaf, 0xa8, 0xd0, 0x86, 0xe8, 0x9e, 0x8f, 0x94, 0xc6, 0x2c, 0xd2, 0xc4, 0xab, 0x18,
	0x3a, 0xef, 0x3f, 0x22, 0xd3, 0x22, 0x6d, 0x0b, 0xea, 0xe2, 0x28, 0x2c, 0xe3, 0x92, 0xbd, 0xe4,
	0x9d, 0xc3, 0x45, 0x98, 0xbb, 0x8c, 0x4b, 0xf6, 0xca, 0x36, 0x8d, 0xcb, 0x36, 0x40, 0xfe, 0x9a,
	0x56, 0xa6, 0xa9, 0xb1, 0xf7, 0xb6, 0x69, 0xdc, 0x1e, 0x40, 0x65, 0x8b, 0xf8, 0xa8, 0xa4, 0x49,
	0x26, 0x9e, 0xd8, 0xa6, 0xd1, 0x3f, 0x86, 0x9a, 0xf8, 0xeb, 0xc6, 0x8d, 0xb2, 0xca, 0x5e, 0x74,
	0x6b, 0xba, 0x53, 0xbf, 0x0b, 0xb7, 0x97, 0xff, 0x41, 0x99, 0xfa, 0x8a, 0x33, 0xc5, 0xed, 0xf3,
	0xdb, 0x30, 0x81, 0xcb, 0xa7, 0xee, 0xab, 0xe8, 0x56, 0x49, 0xd7, 0xf4, 0xcc, 0x15, 0xb9, 0x3b,
	0x23, 0x66, 0x1a, 0x6f, 0xdc, 0xfd, 0xf2, 0xa3, 0x61, 0xe4, 0xdb, 0xe1, 0x70, 0xf9, 0xce, 0x0a,
	0xa5, 0xec, 0xb2, 0x7e, 0x9b, 0xff, 0xd9, 0xce, 0x89, 0xfc, 0xdb, 0x29, 0x49, 0x8e, 0x3c, 0x87,
	0xa4, 0xb7, 0x0b, 0x7f, 0xe7, 0xcb, 0x98, 0xed, 0xd5, 0x39, 0xde, 0xea, 0x7f, 0x03, 0x00, 0x00,
	0xff, 0xff, 0xd0, 0x25, 0x88, 0xc5, 0x26, 0x28, 0x00, 0x00,
}
