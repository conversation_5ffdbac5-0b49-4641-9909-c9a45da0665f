// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/recall/recall_etl.proto

package recall_etl // import "golang.52tt.com/protocol/services/rcmd/recall_etl"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_game_tab "golang.52tt.com/protocol/services/rcmd/persona/tt/channel_game_tab"
import chatcard "golang.52tt.com/protocol/services/rcmd/persona/tt/chatcard"
import radar "golang.52tt.com/protocol/services/rcmd/persona/tt/radar"
import user "golang.52tt.com/protocol/services/rcmd/persona/tt/user"
import recall_common "golang.52tt.com/protocol/services/rcmd/recall_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type OuterType int32

const (
	OuterType_Invalid     OuterType = 0
	OuterType_Active_User OuterType = 1
)

var OuterType_name = map[int32]string{
	0: "Invalid",
	1: "Active_User",
}
var OuterType_value = map[string]int32{
	"Invalid":     0,
	"Active_User": 1,
}

func (x OuterType) String() string {
	return proto.EnumName(OuterType_name, int32(x))
}
func (OuterType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{0}
}

type Operator int32

const (
	Operator_Operator_Invalid Operator = 0
	Operator_Enter_Pool       Operator = 1
	Operator_Exit_Pool        Operator = 2
)

var Operator_name = map[int32]string{
	0: "Operator_Invalid",
	1: "Enter_Pool",
	2: "Exit_Pool",
}
var Operator_value = map[string]int32{
	"Operator_Invalid": 0,
	"Enter_Pool":       1,
	"Exit_Pool":        2,
}

func (x Operator) String() string {
	return proto.EnumName(Operator_name, int32(x))
}
func (Operator) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{1}
}

type MicEventType int32

const (
	MicEventType_MicInvalid      MicEventType = 0
	MicEventType_MicEventHold    MicEventType = 1
	MicEventType_MicEventRelease MicEventType = 2
	MicEventType_MicEventChange  MicEventType = 3
	MicEventType_MicEventModSet  MicEventType = 4
)

var MicEventType_name = map[int32]string{
	0: "MicInvalid",
	1: "MicEventHold",
	2: "MicEventRelease",
	3: "MicEventChange",
	4: "MicEventModSet",
}
var MicEventType_value = map[string]int32{
	"MicInvalid":      0,
	"MicEventHold":    1,
	"MicEventRelease": 2,
	"MicEventChange":  3,
	"MicEventModSet":  4,
}

func (x MicEventType) String() string {
	return proto.EnumName(MicEventType_name, int32(x))
}
func (MicEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{2}
}

type ChannelClass int32

const (
	ChannelClass_Exp_Class ChannelClass = 0
)

var ChannelClass_name = map[int32]string{
	0: "Exp_Class",
}
var ChannelClass_value = map[string]int32{
	"Exp_Class": 0,
}

func (x ChannelClass) String() string {
	return proto.EnumName(ChannelClass_name, int32(x))
}
func (ChannelClass) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{3}
}

type ChannelTypeId int32

const (
	ChannelTypeId_ChannelType_Exp_Kuolie       ChannelTypeId = 0
	ChannelTypeId_ChannelType_Exp_Kaihei       ChannelTypeId = 1
	ChannelTypeId_ChannelType_Exp_MinGame      ChannelTypeId = 2
	ChannelTypeId_ChannelType_Exp_City         ChannelTypeId = 3
	ChannelTypeId_ChannelType_Exp_Province     ChannelTypeId = 4
	ChannelTypeId_ChannelType_Exp_Relationship ChannelTypeId = 5
	ChannelTypeId_ChannelType_Exp_Music        ChannelTypeId = 6
	ChannelTypeId_ChannelType_Exp_None         ChannelTypeId = 1000
)

var ChannelTypeId_name = map[int32]string{
	0:    "ChannelType_Exp_Kuolie",
	1:    "ChannelType_Exp_Kaihei",
	2:    "ChannelType_Exp_MinGame",
	3:    "ChannelType_Exp_City",
	4:    "ChannelType_Exp_Province",
	5:    "ChannelType_Exp_Relationship",
	6:    "ChannelType_Exp_Music",
	1000: "ChannelType_Exp_None",
}
var ChannelTypeId_value = map[string]int32{
	"ChannelType_Exp_Kuolie":       0,
	"ChannelType_Exp_Kaihei":       1,
	"ChannelType_Exp_MinGame":      2,
	"ChannelType_Exp_City":         3,
	"ChannelType_Exp_Province":     4,
	"ChannelType_Exp_Relationship": 5,
	"ChannelType_Exp_Music":        6,
	"ChannelType_Exp_None":         1000,
}

func (x ChannelTypeId) String() string {
	return proto.EnumName(ChannelTypeId_name, int32(x))
}
func (ChannelTypeId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{4}
}

type ChannelDisplayType int32

const (
	ChannelDisplayType_DISPLAY_AT_MAIN_PAGE   ChannelDisplayType = 0
	ChannelDisplayType_DISPLAY_AT_FIND_FRIEND ChannelDisplayType = 2
	ChannelDisplayType_TEMPORARY              ChannelDisplayType = 3
)

var ChannelDisplayType_name = map[int32]string{
	0: "DISPLAY_AT_MAIN_PAGE",
	2: "DISPLAY_AT_FIND_FRIEND",
	3: "TEMPORARY",
}
var ChannelDisplayType_value = map[string]int32{
	"DISPLAY_AT_MAIN_PAGE":   0,
	"DISPLAY_AT_FIND_FRIEND": 2,
	"TEMPORARY":              3,
}

func (x ChannelDisplayType) String() string {
	return proto.EnumName(ChannelDisplayType_name, int32(x))
}
func (ChannelDisplayType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{5}
}

type OfflineUid2ItemWriteType int32

const (
	OfflineUid2ItemWriteType_UnknownOfflineUid2ItemWriteType OfflineUid2ItemWriteType = 0
	OfflineUid2ItemWriteType_OfflineRecallRank10             OfflineUid2ItemWriteType = 1
	OfflineUid2ItemWriteType_OfflineRecallHighQuality        OfflineUid2ItemWriteType = 2
	OfflineUid2ItemWriteType_OfflineRecall11                 OfflineUid2ItemWriteType = 3
)

var OfflineUid2ItemWriteType_name = map[int32]string{
	0: "UnknownOfflineUid2ItemWriteType",
	1: "OfflineRecallRank10",
	2: "OfflineRecallHighQuality",
	3: "OfflineRecall11",
}
var OfflineUid2ItemWriteType_value = map[string]int32{
	"UnknownOfflineUid2ItemWriteType": 0,
	"OfflineRecallRank10":             1,
	"OfflineRecallHighQuality":        2,
	"OfflineRecall11":                 3,
}

func (x OfflineUid2ItemWriteType) String() string {
	return proto.EnumName(OfflineUid2ItemWriteType_name, int32(x))
}
func (OfflineUid2ItemWriteType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{6}
}

type PostStatType int32

const (
	PostStatType_StatType_Unknow   PostStatType = 0
	PostStatType_StatType_Exposure PostStatType = 1
	PostStatType_StatType_Comment  PostStatType = 2
	PostStatType_StatType_Like     PostStatType = 3
)

var PostStatType_name = map[int32]string{
	0: "StatType_Unknow",
	1: "StatType_Exposure",
	2: "StatType_Comment",
	3: "StatType_Like",
}
var PostStatType_value = map[string]int32{
	"StatType_Unknow":   0,
	"StatType_Exposure": 1,
	"StatType_Comment":  2,
	"StatType_Like":     3,
}

func (x PostStatType) String() string {
	return proto.EnumName(PostStatType_name, int32(x))
}
func (PostStatType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{7}
}

type CompanionType int32

const (
	CompanionType_Normal CompanionType = 0
	CompanionType_LOLWR  CompanionType = 1
)

var CompanionType_name = map[int32]string{
	0: "Normal",
	1: "LOLWR",
}
var CompanionType_value = map[string]int32{
	"Normal": 0,
	"LOLWR":  1,
}

func (x CompanionType) String() string {
	return proto.EnumName(CompanionType_name, int32(x))
}
func (CompanionType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{8}
}

type MP_TopicChannelEvent_ACTION int32

const (
	MP_TopicChannelEvent_INVALID MP_TopicChannelEvent_ACTION = 0
	MP_TopicChannelEvent_CREATE  MP_TopicChannelEvent_ACTION = 1
	MP_TopicChannelEvent_DISMISS MP_TopicChannelEvent_ACTION = 2
)

var MP_TopicChannelEvent_ACTION_name = map[int32]string{
	0: "INVALID",
	1: "CREATE",
	2: "DISMISS",
}
var MP_TopicChannelEvent_ACTION_value = map[string]int32{
	"INVALID": 0,
	"CREATE":  1,
	"DISMISS": 2,
}

func (x MP_TopicChannelEvent_ACTION) String() string {
	return proto.EnumName(MP_TopicChannelEvent_ACTION_name, int32(x))
}
func (MP_TopicChannelEvent_ACTION) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{19, 0}
}

type MP_TopicChannelQuality_TopicChannelQuality int32

const (
	MP_TopicChannelQuality_DEFAULT MP_TopicChannelQuality_TopicChannelQuality = 0
	MP_TopicChannelQuality_LOW     MP_TopicChannelQuality_TopicChannelQuality = 1
)

var MP_TopicChannelQuality_TopicChannelQuality_name = map[int32]string{
	0: "DEFAULT",
	1: "LOW",
}
var MP_TopicChannelQuality_TopicChannelQuality_value = map[string]int32{
	"DEFAULT": 0,
	"LOW":     1,
}

func (x MP_TopicChannelQuality_TopicChannelQuality) String() string {
	return proto.EnumName(MP_TopicChannelQuality_TopicChannelQuality_name, int32(x))
}
func (MP_TopicChannelQuality_TopicChannelQuality) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{22, 0}
}

type MP_ChannelEvent_Action int32

const (
	MP_ChannelEvent_Action_Invalid      MP_ChannelEvent_Action = 0
	MP_ChannelEvent_Action_Enter        MP_ChannelEvent_Action = 1
	MP_ChannelEvent_Action_Leave        MP_ChannelEvent_Action = 2
	MP_ChannelEvent_Action_Admin_Update MP_ChannelEvent_Action = 5
)

var MP_ChannelEvent_Action_name = map[int32]string{
	0: "Action_Invalid",
	1: "Action_Enter",
	2: "Action_Leave",
	5: "Action_Admin_Update",
}
var MP_ChannelEvent_Action_value = map[string]int32{
	"Action_Invalid":      0,
	"Action_Enter":        1,
	"Action_Leave":        2,
	"Action_Admin_Update": 5,
}

func (x MP_ChannelEvent_Action) String() string {
	return proto.EnumName(MP_ChannelEvent_Action_name, int32(x))
}
func (MP_ChannelEvent_Action) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{23, 0}
}

type MP_PostEvent_PostEventType int32

const (
	MP_PostEvent_UnknownEvent   MP_PostEvent_PostEventType = 0
	MP_PostEvent_Publish        MP_PostEvent_PostEventType = 1
	MP_PostEvent_Delete         MP_PostEvent_PostEventType = 2
	MP_PostEvent_PrivacyChange  MP_PostEvent_PostEventType = 3
	MP_PostEvent_Attitude       MP_PostEvent_PostEventType = 4
	MP_PostEvent_Comment        MP_PostEvent_PostEventType = 5
	MP_PostEvent_AppBlock       MP_PostEvent_PostEventType = 6
	MP_PostEvent_PoolChange     MP_PostEvent_PostEventType = 7
	MP_PostEvent_OwnerReply     MP_PostEvent_PostEventType = 8
	MP_PostEvent_BadScoreChange MP_PostEvent_PostEventType = 9
	MP_PostEvent_BadLevelChange MP_PostEvent_PostEventType = 10
)

var MP_PostEvent_PostEventType_name = map[int32]string{
	0:  "UnknownEvent",
	1:  "Publish",
	2:  "Delete",
	3:  "PrivacyChange",
	4:  "Attitude",
	5:  "Comment",
	6:  "AppBlock",
	7:  "PoolChange",
	8:  "OwnerReply",
	9:  "BadScoreChange",
	10: "BadLevelChange",
}
var MP_PostEvent_PostEventType_value = map[string]int32{
	"UnknownEvent":   0,
	"Publish":        1,
	"Delete":         2,
	"PrivacyChange":  3,
	"Attitude":       4,
	"Comment":        5,
	"AppBlock":       6,
	"PoolChange":     7,
	"OwnerReply":     8,
	"BadScoreChange": 9,
	"BadLevelChange": 10,
}

func (x MP_PostEvent_PostEventType) String() string {
	return proto.EnumName(MP_PostEvent_PostEventType_name, int32(x))
}
func (MP_PostEvent_PostEventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{30, 0}
}

type MP_PostEvent_PostType int32

const (
	MP_PostEvent_InvalidPostType MP_PostEvent_PostType = 0
	MP_PostEvent_TextPost        MP_PostEvent_PostType = 1
	MP_PostEvent_ImagePost       MP_PostEvent_PostType = 2
	MP_PostEvent_VideoPost       MP_PostEvent_PostType = 3
	MP_PostEvent_CmsPost         MP_PostEvent_PostType = 4
	MP_PostEvent_AudioPost       MP_PostEvent_PostType = 5
)

var MP_PostEvent_PostType_name = map[int32]string{
	0: "InvalidPostType",
	1: "TextPost",
	2: "ImagePost",
	3: "VideoPost",
	4: "CmsPost",
	5: "AudioPost",
}
var MP_PostEvent_PostType_value = map[string]int32{
	"InvalidPostType": 0,
	"TextPost":        1,
	"ImagePost":       2,
	"VideoPost":       3,
	"CmsPost":         4,
	"AudioPost":       5,
}

func (x MP_PostEvent_PostType) String() string {
	return proto.EnumName(MP_PostEvent_PostType_name, int32(x))
}
func (MP_PostEvent_PostType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{30, 1}
}

type MP_RiskyWord_Oper int32

const (
	MP_RiskyWord_Oper_Invalid MP_RiskyWord_Oper = 0
	MP_RiskyWord_Oper_Add     MP_RiskyWord_Oper = 1
	MP_RiskyWord_Oper_Remove  MP_RiskyWord_Oper = 2
)

var MP_RiskyWord_Oper_name = map[int32]string{
	0: "Oper_Invalid",
	1: "Oper_Add",
	2: "Oper_Remove",
}
var MP_RiskyWord_Oper_value = map[string]int32{
	"Oper_Invalid": 0,
	"Oper_Add":     1,
	"Oper_Remove":  2,
}

func (x MP_RiskyWord_Oper) String() string {
	return proto.EnumName(MP_RiskyWord_Oper_name, int32(x))
}
func (MP_RiskyWord_Oper) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{31, 0}
}

type MP_ChatCard_Op int32

const (
	MP_ChatCard_Invalid MP_ChatCard_Op = 0
	MP_ChatCard_Publish MP_ChatCard_Op = 1
	MP_ChatCard_Close   MP_ChatCard_Op = 2
)

var MP_ChatCard_Op_name = map[int32]string{
	0: "Invalid",
	1: "Publish",
	2: "Close",
}
var MP_ChatCard_Op_value = map[string]int32{
	"Invalid": 0,
	"Publish": 1,
	"Close":   2,
}

func (x MP_ChatCard_Op) String() string {
	return proto.EnumName(MP_ChatCard_Op_name, int32(x))
}
func (MP_ChatCard_Op) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{32, 0}
}

type MP_ChatCardHitOn_Typ int32

const (
	MP_ChatCardHitOn_Invalid MP_ChatCardHitOn_Typ = 0
	MP_ChatCardHitOn_HitOn   MP_ChatCardHitOn_Typ = 1
	MP_ChatCardHitOn_Reply   MP_ChatCardHitOn_Typ = 2
)

var MP_ChatCardHitOn_Typ_name = map[int32]string{
	0: "Invalid",
	1: "HitOn",
	2: "Reply",
}
var MP_ChatCardHitOn_Typ_value = map[string]int32{
	"Invalid": 0,
	"HitOn":   1,
	"Reply":   2,
}

func (x MP_ChatCardHitOn_Typ) String() string {
	return proto.EnumName(MP_ChatCardHitOn_Typ_name, int32(x))
}
func (MP_ChatCardHitOn_Typ) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{33, 0}
}

type MP_SingChannelEvent_Status int32

const (
	MP_SingChannelEvent_StatusInvalid MP_SingChannelEvent_Status = 0
	MP_SingChannelEvent_StatusNormal  MP_SingChannelEvent_Status = 1
	MP_SingChannelEvent_StatusDismiss MP_SingChannelEvent_Status = 2
)

var MP_SingChannelEvent_Status_name = map[int32]string{
	0: "StatusInvalid",
	1: "StatusNormal",
	2: "StatusDismiss",
}
var MP_SingChannelEvent_Status_value = map[string]int32{
	"StatusInvalid": 0,
	"StatusNormal":  1,
	"StatusDismiss": 2,
}

func (x MP_SingChannelEvent_Status) String() string {
	return proto.EnumName(MP_SingChannelEvent_Status_name, int32(x))
}
func (MP_SingChannelEvent_Status) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{47, 0}
}

type MP_SingChannelEvent_OpType int32

const (
	MP_SingChannelEvent_OpTypeInvalid MP_SingChannelEvent_OpType = 0
	MP_SingChannelEvent_OpTypeCreate  MP_SingChannelEvent_OpType = 1
	MP_SingChannelEvent_OpTypeUpdate  MP_SingChannelEvent_OpType = 2
	MP_SingChannelEvent_OpTypeDismiss MP_SingChannelEvent_OpType = 3
)

var MP_SingChannelEvent_OpType_name = map[int32]string{
	0: "OpTypeInvalid",
	1: "OpTypeCreate",
	2: "OpTypeUpdate",
	3: "OpTypeDismiss",
}
var MP_SingChannelEvent_OpType_value = map[string]int32{
	"OpTypeInvalid": 0,
	"OpTypeCreate":  1,
	"OpTypeUpdate":  2,
	"OpTypeDismiss": 3,
}

func (x MP_SingChannelEvent_OpType) String() string {
	return proto.EnumName(MP_SingChannelEvent_OpType_name, int32(x))
}
func (MP_SingChannelEvent_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{47, 1}
}

type MP_SingChannelEvent_GameStatus int32

const (
	MP_SingChannelEvent_GameStatusNotStart MP_SingChannelEvent_GameStatus = 0
	MP_SingChannelEvent_GameStatusStart    MP_SingChannelEvent_GameStatus = 1
)

var MP_SingChannelEvent_GameStatus_name = map[int32]string{
	0: "GameStatusNotStart",
	1: "GameStatusStart",
}
var MP_SingChannelEvent_GameStatus_value = map[string]int32{
	"GameStatusNotStart": 0,
	"GameStatusStart":    1,
}

func (x MP_SingChannelEvent_GameStatus) String() string {
	return proto.EnumName(MP_SingChannelEvent_GameStatus_name, int32(x))
}
func (MP_SingChannelEvent_GameStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{47, 2}
}

type SetLoginDurationReq struct {
	LoginDuration        uint32   `protobuf:"varint,1,opt,name=login_duration,json=loginDuration,proto3" json:"login_duration,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLoginDurationReq) Reset()         { *m = SetLoginDurationReq{} }
func (m *SetLoginDurationReq) String() string { return proto.CompactTextString(m) }
func (*SetLoginDurationReq) ProtoMessage()    {}
func (*SetLoginDurationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{0}
}
func (m *SetLoginDurationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLoginDurationReq.Unmarshal(m, b)
}
func (m *SetLoginDurationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLoginDurationReq.Marshal(b, m, deterministic)
}
func (dst *SetLoginDurationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLoginDurationReq.Merge(dst, src)
}
func (m *SetLoginDurationReq) XXX_Size() int {
	return xxx_messageInfo_SetLoginDurationReq.Size(m)
}
func (m *SetLoginDurationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLoginDurationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetLoginDurationReq proto.InternalMessageInfo

func (m *SetLoginDurationReq) GetLoginDuration() uint32 {
	if m != nil {
		return m.LoginDuration
	}
	return 0
}

type SetLoginDurationRsp struct {
	Msg                  string   `protobuf:"bytes,1,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetLoginDurationRsp) Reset()         { *m = SetLoginDurationRsp{} }
func (m *SetLoginDurationRsp) String() string { return proto.CompactTextString(m) }
func (*SetLoginDurationRsp) ProtoMessage()    {}
func (*SetLoginDurationRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{1}
}
func (m *SetLoginDurationRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetLoginDurationRsp.Unmarshal(m, b)
}
func (m *SetLoginDurationRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetLoginDurationRsp.Marshal(b, m, deterministic)
}
func (dst *SetLoginDurationRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetLoginDurationRsp.Merge(dst, src)
}
func (m *SetLoginDurationRsp) XXX_Size() int {
	return xxx_messageInfo_SetLoginDurationRsp.Size(m)
}
func (m *SetLoginDurationRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetLoginDurationRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetLoginDurationRsp proto.InternalMessageInfo

func (m *SetLoginDurationRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type MockDeliverReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MockDeliverReq) Reset()         { *m = MockDeliverReq{} }
func (m *MockDeliverReq) String() string { return proto.CompactTextString(m) }
func (*MockDeliverReq) ProtoMessage()    {}
func (*MockDeliverReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{2}
}
func (m *MockDeliverReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MockDeliverReq.Unmarshal(m, b)
}
func (m *MockDeliverReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MockDeliverReq.Marshal(b, m, deterministic)
}
func (dst *MockDeliverReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MockDeliverReq.Merge(dst, src)
}
func (m *MockDeliverReq) XXX_Size() int {
	return xxx_messageInfo_MockDeliverReq.Size(m)
}
func (m *MockDeliverReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MockDeliverReq.DiscardUnknown(m)
}

var xxx_messageInfo_MockDeliverReq proto.InternalMessageInfo

func (m *MockDeliverReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MockDeliverReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

type MockDeliverRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MockDeliverRsp) Reset()         { *m = MockDeliverRsp{} }
func (m *MockDeliverRsp) String() string { return proto.CompactTextString(m) }
func (*MockDeliverRsp) ProtoMessage()    {}
func (*MockDeliverRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{3}
}
func (m *MockDeliverRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MockDeliverRsp.Unmarshal(m, b)
}
func (m *MockDeliverRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MockDeliverRsp.Marshal(b, m, deterministic)
}
func (dst *MockDeliverRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MockDeliverRsp.Merge(dst, src)
}
func (m *MockDeliverRsp) XXX_Size() int {
	return xxx_messageInfo_MockDeliverRsp.Size(m)
}
func (m *MockDeliverRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_MockDeliverRsp.DiscardUnknown(m)
}

var xxx_messageInfo_MockDeliverRsp proto.InternalMessageInfo

type MockLoginReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Oper                 uint32   `protobuf:"varint,2,opt,name=oper,proto3" json:"oper,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MockLoginReq) Reset()         { *m = MockLoginReq{} }
func (m *MockLoginReq) String() string { return proto.CompactTextString(m) }
func (*MockLoginReq) ProtoMessage()    {}
func (*MockLoginReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{4}
}
func (m *MockLoginReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MockLoginReq.Unmarshal(m, b)
}
func (m *MockLoginReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MockLoginReq.Marshal(b, m, deterministic)
}
func (dst *MockLoginReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MockLoginReq.Merge(dst, src)
}
func (m *MockLoginReq) XXX_Size() int {
	return xxx_messageInfo_MockLoginReq.Size(m)
}
func (m *MockLoginReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MockLoginReq.DiscardUnknown(m)
}

var xxx_messageInfo_MockLoginReq proto.InternalMessageInfo

func (m *MockLoginReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MockLoginReq) GetOper() uint32 {
	if m != nil {
		return m.Oper
	}
	return 0
}

type MockLoginRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MockLoginRsp) Reset()         { *m = MockLoginRsp{} }
func (m *MockLoginRsp) String() string { return proto.CompactTextString(m) }
func (*MockLoginRsp) ProtoMessage()    {}
func (*MockLoginRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{5}
}
func (m *MockLoginRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MockLoginRsp.Unmarshal(m, b)
}
func (m *MockLoginRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MockLoginRsp.Marshal(b, m, deterministic)
}
func (dst *MockLoginRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MockLoginRsp.Merge(dst, src)
}
func (m *MockLoginRsp) XXX_Size() int {
	return xxx_messageInfo_MockLoginRsp.Size(m)
}
func (m *MockLoginRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_MockLoginRsp.DiscardUnknown(m)
}

var xxx_messageInfo_MockLoginRsp proto.InternalMessageInfo

type ImSinkData struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	EventTime            uint64   `protobuf:"varint,2,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ImSinkData) Reset()         { *m = ImSinkData{} }
func (m *ImSinkData) String() string { return proto.CompactTextString(m) }
func (*ImSinkData) ProtoMessage()    {}
func (*ImSinkData) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{6}
}
func (m *ImSinkData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ImSinkData.Unmarshal(m, b)
}
func (m *ImSinkData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ImSinkData.Marshal(b, m, deterministic)
}
func (dst *ImSinkData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ImSinkData.Merge(dst, src)
}
func (m *ImSinkData) XXX_Size() int {
	return xxx_messageInfo_ImSinkData.Size(m)
}
func (m *ImSinkData) XXX_DiscardUnknown() {
	xxx_messageInfo_ImSinkData.DiscardUnknown(m)
}

var xxx_messageInfo_ImSinkData proto.InternalMessageInfo

func (m *ImSinkData) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *ImSinkData) GetEventTime() uint64 {
	if m != nil {
		return m.EventTime
	}
	return 0
}

type ActiveUserSinkData struct {
	Operator             Operator `protobuf:"varint,1,opt,name=operator,proto3,enum=rcmd.recall_etl.Operator" json:"operator,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	EventTime            uint64   `protobuf:"varint,3,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActiveUserSinkData) Reset()         { *m = ActiveUserSinkData{} }
func (m *ActiveUserSinkData) String() string { return proto.CompactTextString(m) }
func (*ActiveUserSinkData) ProtoMessage()    {}
func (*ActiveUserSinkData) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{7}
}
func (m *ActiveUserSinkData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActiveUserSinkData.Unmarshal(m, b)
}
func (m *ActiveUserSinkData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActiveUserSinkData.Marshal(b, m, deterministic)
}
func (dst *ActiveUserSinkData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActiveUserSinkData.Merge(dst, src)
}
func (m *ActiveUserSinkData) XXX_Size() int {
	return xxx_messageInfo_ActiveUserSinkData.Size(m)
}
func (m *ActiveUserSinkData) XXX_DiscardUnknown() {
	xxx_messageInfo_ActiveUserSinkData.DiscardUnknown(m)
}

var xxx_messageInfo_ActiveUserSinkData proto.InternalMessageInfo

func (m *ActiveUserSinkData) GetOperator() Operator {
	if m != nil {
		return m.Operator
	}
	return Operator_Operator_Invalid
}

func (m *ActiveUserSinkData) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *ActiveUserSinkData) GetEventTime() uint64 {
	if m != nil {
		return m.EventTime
	}
	return 0
}

type GetOuterDataReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOuterDataReq) Reset()         { *m = GetOuterDataReq{} }
func (m *GetOuterDataReq) String() string { return proto.CompactTextString(m) }
func (*GetOuterDataReq) ProtoMessage()    {}
func (*GetOuterDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{8}
}
func (m *GetOuterDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOuterDataReq.Unmarshal(m, b)
}
func (m *GetOuterDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOuterDataReq.Marshal(b, m, deterministic)
}
func (dst *GetOuterDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOuterDataReq.Merge(dst, src)
}
func (m *GetOuterDataReq) XXX_Size() int {
	return xxx_messageInfo_GetOuterDataReq.Size(m)
}
func (m *GetOuterDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOuterDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOuterDataReq proto.InternalMessageInfo

type GetOuterDataRsp struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOuterDataRsp) Reset()         { *m = GetOuterDataRsp{} }
func (m *GetOuterDataRsp) String() string { return proto.CompactTextString(m) }
func (*GetOuterDataRsp) ProtoMessage()    {}
func (*GetOuterDataRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{9}
}
func (m *GetOuterDataRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOuterDataRsp.Unmarshal(m, b)
}
func (m *GetOuterDataRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOuterDataRsp.Marshal(b, m, deterministic)
}
func (dst *GetOuterDataRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOuterDataRsp.Merge(dst, src)
}
func (m *GetOuterDataRsp) XXX_Size() int {
	return xxx_messageInfo_GetOuterDataRsp.Size(m)
}
func (m *GetOuterDataRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOuterDataRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOuterDataRsp proto.InternalMessageInfo

func (m *GetOuterDataRsp) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

// common
type MP_Base struct {
	App                  recall_common.App `protobuf:"varint,1,opt,name=app,proto3,enum=rcmd.recall_common.App" json:"app,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *MP_Base) Reset()         { *m = MP_Base{} }
func (m *MP_Base) String() string { return proto.CompactTextString(m) }
func (*MP_Base) ProtoMessage()    {}
func (*MP_Base) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{10}
}
func (m *MP_Base) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_Base.Unmarshal(m, b)
}
func (m *MP_Base) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_Base.Marshal(b, m, deterministic)
}
func (dst *MP_Base) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_Base.Merge(dst, src)
}
func (m *MP_Base) XXX_Size() int {
	return xxx_messageInfo_MP_Base.Size(m)
}
func (m *MP_Base) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_Base.DiscardUnknown(m)
}

var xxx_messageInfo_MP_Base proto.InternalMessageInfo

func (m *MP_Base) GetApp() recall_common.App {
	if m != nil {
		return m.App
	}
	return recall_common.App_Invalid_App
}

func (m *MP_Base) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type MP_UserBase struct {
	RegisterTime         uint32   `protobuf:"varint,1,opt,name=register_time,json=registerTime,proto3" json:"register_time,omitempty"`
	Sex                  uint32   `protobuf:"varint,2,opt,name=sex,proto3" json:"sex,omitempty"`
	Birthday             uint32   `protobuf:"varint,3,opt,name=birthday,proto3" json:"birthday,omitempty"`
	AgeGroup             uint32   `protobuf:"varint,4,opt,name=age_group,json=ageGroup,proto3" json:"age_group,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_UserBase) Reset()         { *m = MP_UserBase{} }
func (m *MP_UserBase) String() string { return proto.CompactTextString(m) }
func (*MP_UserBase) ProtoMessage()    {}
func (*MP_UserBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{11}
}
func (m *MP_UserBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_UserBase.Unmarshal(m, b)
}
func (m *MP_UserBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_UserBase.Marshal(b, m, deterministic)
}
func (dst *MP_UserBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_UserBase.Merge(dst, src)
}
func (m *MP_UserBase) XXX_Size() int {
	return xxx_messageInfo_MP_UserBase.Size(m)
}
func (m *MP_UserBase) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_UserBase.DiscardUnknown(m)
}

var xxx_messageInfo_MP_UserBase proto.InternalMessageInfo

func (m *MP_UserBase) GetRegisterTime() uint32 {
	if m != nil {
		return m.RegisterTime
	}
	return 0
}

func (m *MP_UserBase) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MP_UserBase) GetBirthday() uint32 {
	if m != nil {
		return m.Birthday
	}
	return 0
}

func (m *MP_UserBase) GetAgeGroup() uint32 {
	if m != nil {
		return m.AgeGroup
	}
	return 0
}

type MP_UserTagChange struct {
	Base                 *MP_Base           `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32             `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Time                 uint64             `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	User                 *user.UserBasic    `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`
	Register             bool               `protobuf:"varint,5,opt,name=register,proto3" json:"register,omitempty"`
	UserGameTags         *user.UserGameTags `protobuf:"bytes,6,opt,name=userGameTags,proto3" json:"userGameTags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MP_UserTagChange) Reset()         { *m = MP_UserTagChange{} }
func (m *MP_UserTagChange) String() string { return proto.CompactTextString(m) }
func (*MP_UserTagChange) ProtoMessage()    {}
func (*MP_UserTagChange) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{12}
}
func (m *MP_UserTagChange) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_UserTagChange.Unmarshal(m, b)
}
func (m *MP_UserTagChange) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_UserTagChange.Marshal(b, m, deterministic)
}
func (dst *MP_UserTagChange) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_UserTagChange.Merge(dst, src)
}
func (m *MP_UserTagChange) XXX_Size() int {
	return xxx_messageInfo_MP_UserTagChange.Size(m)
}
func (m *MP_UserTagChange) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_UserTagChange.DiscardUnknown(m)
}

var xxx_messageInfo_MP_UserTagChange proto.InternalMessageInfo

func (m *MP_UserTagChange) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_UserTagChange) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_UserTagChange) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *MP_UserTagChange) GetUser() *user.UserBasic {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *MP_UserTagChange) GetRegister() bool {
	if m != nil {
		return m.Register
	}
	return false
}

func (m *MP_UserTagChange) GetUserGameTags() *user.UserGameTags {
	if m != nil {
		return m.UserGameTags
	}
	return nil
}

type MP_UserLogin struct {
	Base                 *MP_Base              `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32                `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Time                 uint64                `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	User                 *MP_UserBase          `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`
	IsLogin              bool                  `protobuf:"varint,5,opt,name=is_login,json=isLogin,proto3" json:"is_login,omitempty"`
	UserBasic            *user.UserBasic       `protobuf:"bytes,6,opt,name=user_basic,json=userBasic,proto3" json:"user_basic,omitempty"`
	OnlineState          *user.UserOnlineState `protobuf:"bytes,7,opt,name=online_state,json=onlineState,proto3" json:"online_state,omitempty"`
	UserGameTags         *user.UserGameTags    `protobuf:"bytes,8,opt,name=userGameTags,proto3" json:"userGameTags,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *MP_UserLogin) Reset()         { *m = MP_UserLogin{} }
func (m *MP_UserLogin) String() string { return proto.CompactTextString(m) }
func (*MP_UserLogin) ProtoMessage()    {}
func (*MP_UserLogin) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{13}
}
func (m *MP_UserLogin) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_UserLogin.Unmarshal(m, b)
}
func (m *MP_UserLogin) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_UserLogin.Marshal(b, m, deterministic)
}
func (dst *MP_UserLogin) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_UserLogin.Merge(dst, src)
}
func (m *MP_UserLogin) XXX_Size() int {
	return xxx_messageInfo_MP_UserLogin.Size(m)
}
func (m *MP_UserLogin) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_UserLogin.DiscardUnknown(m)
}

var xxx_messageInfo_MP_UserLogin proto.InternalMessageInfo

func (m *MP_UserLogin) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_UserLogin) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_UserLogin) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *MP_UserLogin) GetUser() *MP_UserBase {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *MP_UserLogin) GetIsLogin() bool {
	if m != nil {
		return m.IsLogin
	}
	return false
}

func (m *MP_UserLogin) GetUserBasic() *user.UserBasic {
	if m != nil {
		return m.UserBasic
	}
	return nil
}

func (m *MP_UserLogin) GetOnlineState() *user.UserOnlineState {
	if m != nil {
		return m.OnlineState
	}
	return nil
}

func (m *MP_UserLogin) GetUserGameTags() *user.UserGameTags {
	if m != nil {
		return m.UserGameTags
	}
	return nil
}

type MP_IM struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Time                 uint64   `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	MsgSourceType        uint32   `protobuf:"varint,17,opt,name=msg_source_type,json=msgSourceType,proto3" json:"msg_source_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_IM) Reset()         { *m = MP_IM{} }
func (m *MP_IM) String() string { return proto.CompactTextString(m) }
func (*MP_IM) ProtoMessage()    {}
func (*MP_IM) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{14}
}
func (m *MP_IM) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_IM.Unmarshal(m, b)
}
func (m *MP_IM) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_IM.Marshal(b, m, deterministic)
}
func (dst *MP_IM) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_IM.Merge(dst, src)
}
func (m *MP_IM) XXX_Size() int {
	return xxx_messageInfo_MP_IM.Size(m)
}
func (m *MP_IM) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_IM.DiscardUnknown(m)
}

var xxx_messageInfo_MP_IM proto.InternalMessageInfo

func (m *MP_IM) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_IM) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_IM) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *MP_IM) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *MP_IM) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

type MP_MicEvent struct {
	Base                 *MP_Base     `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32       `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32       `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	FromMicId            uint32       `protobuf:"varint,4,opt,name=from_mic_id,json=fromMicId,proto3" json:"from_mic_id,omitempty"`
	ToMicId              uint32       `protobuf:"varint,5,opt,name=to_mic_id,json=toMicId,proto3" json:"to_mic_id,omitempty"`
	IsUpMic              bool         `protobuf:"varint,6,opt,name=is_up_mic,json=isUpMic,proto3" json:"is_up_mic,omitempty"`
	Time                 uint64       `protobuf:"varint,7,opt,name=time,proto3" json:"time,omitempty"`
	EventType            MicEventType `protobuf:"varint,8,opt,name=event_type,json=eventType,proto3,enum=rcmd.recall_etl.MicEventType" json:"event_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MP_MicEvent) Reset()         { *m = MP_MicEvent{} }
func (m *MP_MicEvent) String() string { return proto.CompactTextString(m) }
func (*MP_MicEvent) ProtoMessage()    {}
func (*MP_MicEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{15}
}
func (m *MP_MicEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_MicEvent.Unmarshal(m, b)
}
func (m *MP_MicEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_MicEvent.Marshal(b, m, deterministic)
}
func (dst *MP_MicEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_MicEvent.Merge(dst, src)
}
func (m *MP_MicEvent) XXX_Size() int {
	return xxx_messageInfo_MP_MicEvent.Size(m)
}
func (m *MP_MicEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_MicEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_MicEvent proto.InternalMessageInfo

func (m *MP_MicEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_MicEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_MicEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_MicEvent) GetFromMicId() uint32 {
	if m != nil {
		return m.FromMicId
	}
	return 0
}

func (m *MP_MicEvent) GetToMicId() uint32 {
	if m != nil {
		return m.ToMicId
	}
	return 0
}

func (m *MP_MicEvent) GetIsUpMic() bool {
	if m != nil {
		return m.IsUpMic
	}
	return false
}

func (m *MP_MicEvent) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

func (m *MP_MicEvent) GetEventType() MicEventType {
	if m != nil {
		return m.EventType
	}
	return MicEventType_MicInvalid
}

type MP_PlayerSetting struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Setting              uint32   `protobuf:"varint,3,opt,name=setting,proto3" json:"setting,omitempty"`
	Time                 uint64   `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_PlayerSetting) Reset()         { *m = MP_PlayerSetting{} }
func (m *MP_PlayerSetting) String() string { return proto.CompactTextString(m) }
func (*MP_PlayerSetting) ProtoMessage()    {}
func (*MP_PlayerSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{16}
}
func (m *MP_PlayerSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_PlayerSetting.Unmarshal(m, b)
}
func (m *MP_PlayerSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_PlayerSetting.Marshal(b, m, deterministic)
}
func (dst *MP_PlayerSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_PlayerSetting.Merge(dst, src)
}
func (m *MP_PlayerSetting) XXX_Size() int {
	return xxx_messageInfo_MP_PlayerSetting.Size(m)
}
func (m *MP_PlayerSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_PlayerSetting.DiscardUnknown(m)
}

var xxx_messageInfo_MP_PlayerSetting proto.InternalMessageInfo

func (m *MP_PlayerSetting) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_PlayerSetting) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_PlayerSetting) GetSetting() uint32 {
	if m != nil {
		return m.Setting
	}
	return 0
}

func (m *MP_PlayerSetting) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type MP_BlackUser struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Time                 uint64   `protobuf:"varint,3,opt,name=time,proto3" json:"time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_BlackUser) Reset()         { *m = MP_BlackUser{} }
func (m *MP_BlackUser) String() string { return proto.CompactTextString(m) }
func (*MP_BlackUser) ProtoMessage()    {}
func (*MP_BlackUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{17}
}
func (m *MP_BlackUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_BlackUser.Unmarshal(m, b)
}
func (m *MP_BlackUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_BlackUser.Marshal(b, m, deterministic)
}
func (dst *MP_BlackUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_BlackUser.Merge(dst, src)
}
func (m *MP_BlackUser) XXX_Size() int {
	return xxx_messageInfo_MP_BlackUser.Size(m)
}
func (m *MP_BlackUser) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_BlackUser.DiscardUnknown(m)
}

var xxx_messageInfo_MP_BlackUser proto.InternalMessageInfo

func (m *MP_BlackUser) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_BlackUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_BlackUser) GetTime() uint64 {
	if m != nil {
		return m.Time
	}
	return 0
}

type MP_DeliverFilter struct {
	Base                 *MP_Base           `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Item                 recall_common.Item `protobuf:"varint,2,opt,name=item,proto3,enum=rcmd.recall_common.Item" json:"item,omitempty"`
	Key                  recall_common.Key  `protobuf:"varint,3,opt,name=key,proto3,enum=rcmd.recall_common.Key" json:"key,omitempty"`
	Uid                  uint32             `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	IdList               []uint32           `protobuf:"varint,5,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MP_DeliverFilter) Reset()         { *m = MP_DeliverFilter{} }
func (m *MP_DeliverFilter) String() string { return proto.CompactTextString(m) }
func (*MP_DeliverFilter) ProtoMessage()    {}
func (*MP_DeliverFilter) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{18}
}
func (m *MP_DeliverFilter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_DeliverFilter.Unmarshal(m, b)
}
func (m *MP_DeliverFilter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_DeliverFilter.Marshal(b, m, deterministic)
}
func (dst *MP_DeliverFilter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_DeliverFilter.Merge(dst, src)
}
func (m *MP_DeliverFilter) XXX_Size() int {
	return xxx_messageInfo_MP_DeliverFilter.Size(m)
}
func (m *MP_DeliverFilter) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_DeliverFilter.DiscardUnknown(m)
}

var xxx_messageInfo_MP_DeliverFilter proto.InternalMessageInfo

func (m *MP_DeliverFilter) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_DeliverFilter) GetItem() recall_common.Item {
	if m != nil {
		return m.Item
	}
	return recall_common.Item_Invalid_Item
}

func (m *MP_DeliverFilter) GetKey() recall_common.Key {
	if m != nil {
		return m.Key
	}
	return recall_common.Key_Invalid_Key
}

func (m *MP_DeliverFilter) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_DeliverFilter) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type MP_TopicChannelEvent struct {
	Base                 *MP_Base                    `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelId            uint32                      `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TabId                uint32                      `protobuf:"varint,3,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	Creator              uint32                      `protobuf:"varint,4,opt,name=creator,proto3" json:"creator,omitempty"`
	BlockOptions         []*BlockOption              `protobuf:"bytes,5,rep,name=block_options,json=blockOptions,proto3" json:"block_options,omitempty"`
	IsPrivate            bool                        `protobuf:"varint,6,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Action               MP_TopicChannelEvent_ACTION `protobuf:"varint,7,opt,name=action,proto3,enum=rcmd.recall_etl.MP_TopicChannelEvent_ACTION" json:"action,omitempty"`
	Name                 string                      `protobuf:"bytes,8,opt,name=name,proto3" json:"name,omitempty"`
	DisplayType          []ChannelDisplayType        `protobuf:"varint,9,rep,packed,name=display_type,json=displayType,proto3,enum=rcmd.recall_etl.ChannelDisplayType" json:"display_type,omitempty"`
	WantFresh            bool                        `protobuf:"varint,10,opt,name=want_fresh,json=wantFresh,proto3" json:"want_fresh,omitempty"`
	ChannelType          map[uint32]ChannelTypeId    `protobuf:"bytes,11,rep,name=channel_type,json=channelType,proto3" json:"channel_type,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3,enum=rcmd.recall_etl.ChannelTypeId"`
	Sex                  uint32                      `protobuf:"varint,12,opt,name=sex,proto3" json:"sex,omitempty"`
	IsChanged            bool                        `protobuf:"varint,13,opt,name=is_changed,json=isChanged,proto3" json:"is_changed,omitempty"`
	ReleaseTime          uint64                      `protobuf:"varint,14,opt,name=release_time,json=releaseTime,proto3" json:"release_time,omitempty"`
	ReleaseIp            string                      `protobuf:"bytes,15,opt,name=release_ip,json=releaseIp,proto3" json:"release_ip,omitempty"`
	ShowGeoInfo          bool                        `protobuf:"varint,16,opt,name=show_geo_info,json=showGeoInfo,proto3" json:"show_geo_info,omitempty"`
	IsMinorityGame       bool                        `protobuf:"varint,17,opt,name=isMinorityGame,proto3" json:"isMinorityGame,omitempty"`
	ParentMinorityTabId  uint32                      `protobuf:"varint,18,opt,name=parentMinorityTabId,proto3" json:"parentMinorityTabId,omitempty"`
	CategoryId           uint32                      `protobuf:"varint,19,opt,name=categoryId,proto3" json:"categoryId,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *MP_TopicChannelEvent) Reset()         { *m = MP_TopicChannelEvent{} }
func (m *MP_TopicChannelEvent) String() string { return proto.CompactTextString(m) }
func (*MP_TopicChannelEvent) ProtoMessage()    {}
func (*MP_TopicChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{19}
}
func (m *MP_TopicChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_TopicChannelEvent.Unmarshal(m, b)
}
func (m *MP_TopicChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_TopicChannelEvent.Marshal(b, m, deterministic)
}
func (dst *MP_TopicChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_TopicChannelEvent.Merge(dst, src)
}
func (m *MP_TopicChannelEvent) XXX_Size() int {
	return xxx_messageInfo_MP_TopicChannelEvent.Size(m)
}
func (m *MP_TopicChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_TopicChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_TopicChannelEvent proto.InternalMessageInfo

func (m *MP_TopicChannelEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_TopicChannelEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_TopicChannelEvent) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MP_TopicChannelEvent) GetCreator() uint32 {
	if m != nil {
		return m.Creator
	}
	return 0
}

func (m *MP_TopicChannelEvent) GetBlockOptions() []*BlockOption {
	if m != nil {
		return m.BlockOptions
	}
	return nil
}

func (m *MP_TopicChannelEvent) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *MP_TopicChannelEvent) GetAction() MP_TopicChannelEvent_ACTION {
	if m != nil {
		return m.Action
	}
	return MP_TopicChannelEvent_INVALID
}

func (m *MP_TopicChannelEvent) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *MP_TopicChannelEvent) GetDisplayType() []ChannelDisplayType {
	if m != nil {
		return m.DisplayType
	}
	return nil
}

func (m *MP_TopicChannelEvent) GetWantFresh() bool {
	if m != nil {
		return m.WantFresh
	}
	return false
}

func (m *MP_TopicChannelEvent) GetChannelType() map[uint32]ChannelTypeId {
	if m != nil {
		return m.ChannelType
	}
	return nil
}

func (m *MP_TopicChannelEvent) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

func (m *MP_TopicChannelEvent) GetIsChanged() bool {
	if m != nil {
		return m.IsChanged
	}
	return false
}

func (m *MP_TopicChannelEvent) GetReleaseTime() uint64 {
	if m != nil {
		return m.ReleaseTime
	}
	return 0
}

func (m *MP_TopicChannelEvent) GetReleaseIp() string {
	if m != nil {
		return m.ReleaseIp
	}
	return ""
}

func (m *MP_TopicChannelEvent) GetShowGeoInfo() bool {
	if m != nil {
		return m.ShowGeoInfo
	}
	return false
}

func (m *MP_TopicChannelEvent) GetIsMinorityGame() bool {
	if m != nil {
		return m.IsMinorityGame
	}
	return false
}

func (m *MP_TopicChannelEvent) GetParentMinorityTabId() uint32 {
	if m != nil {
		return m.ParentMinorityTabId
	}
	return 0
}

func (m *MP_TopicChannelEvent) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

type BlockOption struct {
	BlockId              uint32   `protobuf:"varint,1,opt,name=block_id,json=blockId,proto3" json:"block_id,omitempty"`
	ElemId               uint32   `protobuf:"varint,2,opt,name=elem_id,json=elemId,proto3" json:"elem_id,omitempty"`
	BlockTitle           string   `protobuf:"bytes,3,opt,name=block_title,json=blockTitle,proto3" json:"block_title,omitempty"`
	ElementTitle         string   `protobuf:"bytes,4,opt,name=element_title,json=elementTitle,proto3" json:"element_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlockOption) Reset()         { *m = BlockOption{} }
func (m *BlockOption) String() string { return proto.CompactTextString(m) }
func (*BlockOption) ProtoMessage()    {}
func (*BlockOption) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{20}
}
func (m *BlockOption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlockOption.Unmarshal(m, b)
}
func (m *BlockOption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlockOption.Marshal(b, m, deterministic)
}
func (dst *BlockOption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlockOption.Merge(dst, src)
}
func (m *BlockOption) XXX_Size() int {
	return xxx_messageInfo_BlockOption.Size(m)
}
func (m *BlockOption) XXX_DiscardUnknown() {
	xxx_messageInfo_BlockOption.DiscardUnknown(m)
}

var xxx_messageInfo_BlockOption proto.InternalMessageInfo

func (m *BlockOption) GetBlockId() uint32 {
	if m != nil {
		return m.BlockId
	}
	return 0
}

func (m *BlockOption) GetElemId() uint32 {
	if m != nil {
		return m.ElemId
	}
	return 0
}

func (m *BlockOption) GetBlockTitle() string {
	if m != nil {
		return m.BlockTitle
	}
	return ""
}

func (m *BlockOption) GetElementTitle() string {
	if m != nil {
		return m.ElementTitle
	}
	return ""
}

type ChannelType struct {
	ClassMode            uint32   `protobuf:"varint,1,opt,name=class_mode,json=classMode,proto3" json:"class_mode,omitempty"`
	TypeId               uint32   `protobuf:"varint,2,opt,name=type_id,json=typeId,proto3" json:"type_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChannelType) Reset()         { *m = ChannelType{} }
func (m *ChannelType) String() string { return proto.CompactTextString(m) }
func (*ChannelType) ProtoMessage()    {}
func (*ChannelType) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{21}
}
func (m *ChannelType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelType.Unmarshal(m, b)
}
func (m *ChannelType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelType.Marshal(b, m, deterministic)
}
func (dst *ChannelType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelType.Merge(dst, src)
}
func (m *ChannelType) XXX_Size() int {
	return xxx_messageInfo_ChannelType.Size(m)
}
func (m *ChannelType) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelType.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelType proto.InternalMessageInfo

func (m *ChannelType) GetClassMode() uint32 {
	if m != nil {
		return m.ClassMode
	}
	return 0
}

func (m *ChannelType) GetTypeId() uint32 {
	if m != nil {
		return m.TypeId
	}
	return 0
}

type MP_TopicChannelQuality struct {
	Base                 *MP_Base                                   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelId            []uint32                                   `protobuf:"varint,2,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Quality              MP_TopicChannelQuality_TopicChannelQuality `protobuf:"varint,3,opt,name=quality,proto3,enum=rcmd.recall_etl.MP_TopicChannelQuality_TopicChannelQuality" json:"quality,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *MP_TopicChannelQuality) Reset()         { *m = MP_TopicChannelQuality{} }
func (m *MP_TopicChannelQuality) String() string { return proto.CompactTextString(m) }
func (*MP_TopicChannelQuality) ProtoMessage()    {}
func (*MP_TopicChannelQuality) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{22}
}
func (m *MP_TopicChannelQuality) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_TopicChannelQuality.Unmarshal(m, b)
}
func (m *MP_TopicChannelQuality) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_TopicChannelQuality.Marshal(b, m, deterministic)
}
func (dst *MP_TopicChannelQuality) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_TopicChannelQuality.Merge(dst, src)
}
func (m *MP_TopicChannelQuality) XXX_Size() int {
	return xxx_messageInfo_MP_TopicChannelQuality.Size(m)
}
func (m *MP_TopicChannelQuality) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_TopicChannelQuality.DiscardUnknown(m)
}

var xxx_messageInfo_MP_TopicChannelQuality proto.InternalMessageInfo

func (m *MP_TopicChannelQuality) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_TopicChannelQuality) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *MP_TopicChannelQuality) GetQuality() MP_TopicChannelQuality_TopicChannelQuality {
	if m != nil {
		return m.Quality
	}
	return MP_TopicChannelQuality_DEFAULT
}

type MP_ChannelEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Action               uint32   `protobuf:"varint,4,opt,name=action,proto3" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ChannelEvent) Reset()         { *m = MP_ChannelEvent{} }
func (m *MP_ChannelEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelEvent) ProtoMessage()    {}
func (*MP_ChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{23}
}
func (m *MP_ChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelEvent.Unmarshal(m, b)
}
func (m *MP_ChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelEvent.Merge(dst, src)
}
func (m *MP_ChannelEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelEvent.Size(m)
}
func (m *MP_ChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelEvent proto.InternalMessageInfo

func (m *MP_ChannelEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_ChannelEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_ChannelEvent) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

type MP_RadarEvent struct {
	Base                 *MP_Base     `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Radar                *radar.Radar `protobuf:"bytes,2,opt,name=radar,proto3" json:"radar,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MP_RadarEvent) Reset()         { *m = MP_RadarEvent{} }
func (m *MP_RadarEvent) String() string { return proto.CompactTextString(m) }
func (*MP_RadarEvent) ProtoMessage()    {}
func (*MP_RadarEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{24}
}
func (m *MP_RadarEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_RadarEvent.Unmarshal(m, b)
}
func (m *MP_RadarEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_RadarEvent.Marshal(b, m, deterministic)
}
func (dst *MP_RadarEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_RadarEvent.Merge(dst, src)
}
func (m *MP_RadarEvent) XXX_Size() int {
	return xxx_messageInfo_MP_RadarEvent.Size(m)
}
func (m *MP_RadarEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_RadarEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_RadarEvent proto.InternalMessageInfo

func (m *MP_RadarEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_RadarEvent) GetRadar() *radar.Radar {
	if m != nil {
		return m.Radar
	}
	return nil
}

type MP_RadarDatingEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,3,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	Timestamp            uint32   `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_RadarDatingEvent) Reset()         { *m = MP_RadarDatingEvent{} }
func (m *MP_RadarDatingEvent) String() string { return proto.CompactTextString(m) }
func (*MP_RadarDatingEvent) ProtoMessage()    {}
func (*MP_RadarDatingEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{25}
}
func (m *MP_RadarDatingEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_RadarDatingEvent.Unmarshal(m, b)
}
func (m *MP_RadarDatingEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_RadarDatingEvent.Marshal(b, m, deterministic)
}
func (dst *MP_RadarDatingEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_RadarDatingEvent.Merge(dst, src)
}
func (m *MP_RadarDatingEvent) XXX_Size() int {
	return xxx_messageInfo_MP_RadarDatingEvent.Size(m)
}
func (m *MP_RadarDatingEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_RadarDatingEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_RadarDatingEvent proto.InternalMessageInfo

func (m *MP_RadarDatingEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_RadarDatingEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_RadarDatingEvent) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *MP_RadarDatingEvent) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

type MP_UserBackgroundEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Timestamp            uint32   `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Action               uint32   `protobuf:"varint,4,opt,name=action,proto3" json:"action,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_UserBackgroundEvent) Reset()         { *m = MP_UserBackgroundEvent{} }
func (m *MP_UserBackgroundEvent) String() string { return proto.CompactTextString(m) }
func (*MP_UserBackgroundEvent) ProtoMessage()    {}
func (*MP_UserBackgroundEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{26}
}
func (m *MP_UserBackgroundEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_UserBackgroundEvent.Unmarshal(m, b)
}
func (m *MP_UserBackgroundEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_UserBackgroundEvent.Marshal(b, m, deterministic)
}
func (dst *MP_UserBackgroundEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_UserBackgroundEvent.Merge(dst, src)
}
func (m *MP_UserBackgroundEvent) XXX_Size() int {
	return xxx_messageInfo_MP_UserBackgroundEvent.Size(m)
}
func (m *MP_UserBackgroundEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_UserBackgroundEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_UserBackgroundEvent proto.InternalMessageInfo

func (m *MP_UserBackgroundEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_UserBackgroundEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_UserBackgroundEvent) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *MP_UserBackgroundEvent) GetAction() uint32 {
	if m != nil {
		return m.Action
	}
	return 0
}

type MP_BindCpIdEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CpId                 string   `protobuf:"bytes,3,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_BindCpIdEvent) Reset()         { *m = MP_BindCpIdEvent{} }
func (m *MP_BindCpIdEvent) String() string { return proto.CompactTextString(m) }
func (*MP_BindCpIdEvent) ProtoMessage()    {}
func (*MP_BindCpIdEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{27}
}
func (m *MP_BindCpIdEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_BindCpIdEvent.Unmarshal(m, b)
}
func (m *MP_BindCpIdEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_BindCpIdEvent.Marshal(b, m, deterministic)
}
func (dst *MP_BindCpIdEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_BindCpIdEvent.Merge(dst, src)
}
func (m *MP_BindCpIdEvent) XXX_Size() int {
	return xxx_messageInfo_MP_BindCpIdEvent.Size(m)
}
func (m *MP_BindCpIdEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_BindCpIdEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_BindCpIdEvent proto.InternalMessageInfo

func (m *MP_BindCpIdEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_BindCpIdEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_BindCpIdEvent) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

// 提权 或者 降权 事件
type MP_FactorChangeEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Type                 string   `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Id                   string   `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Factor               float32  `protobuf:"fixed32,4,opt,name=factor,proto3" json:"factor,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_FactorChangeEvent) Reset()         { *m = MP_FactorChangeEvent{} }
func (m *MP_FactorChangeEvent) String() string { return proto.CompactTextString(m) }
func (*MP_FactorChangeEvent) ProtoMessage()    {}
func (*MP_FactorChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{28}
}
func (m *MP_FactorChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_FactorChangeEvent.Unmarshal(m, b)
}
func (m *MP_FactorChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_FactorChangeEvent.Marshal(b, m, deterministic)
}
func (dst *MP_FactorChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_FactorChangeEvent.Merge(dst, src)
}
func (m *MP_FactorChangeEvent) XXX_Size() int {
	return xxx_messageInfo_MP_FactorChangeEvent.Size(m)
}
func (m *MP_FactorChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_FactorChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_FactorChangeEvent proto.InternalMessageInfo

func (m *MP_FactorChangeEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_FactorChangeEvent) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *MP_FactorChangeEvent) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MP_FactorChangeEvent) GetFactor() float32 {
	if m != nil {
		return m.Factor
	}
	return 0
}

// 人审标签修改
type MP_LevelChangeEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Type                 string   `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	Id                   string   `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	Level                string   `protobuf:"bytes,4,opt,name=level,proto3" json:"level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_LevelChangeEvent) Reset()         { *m = MP_LevelChangeEvent{} }
func (m *MP_LevelChangeEvent) String() string { return proto.CompactTextString(m) }
func (*MP_LevelChangeEvent) ProtoMessage()    {}
func (*MP_LevelChangeEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{29}
}
func (m *MP_LevelChangeEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_LevelChangeEvent.Unmarshal(m, b)
}
func (m *MP_LevelChangeEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_LevelChangeEvent.Marshal(b, m, deterministic)
}
func (dst *MP_LevelChangeEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_LevelChangeEvent.Merge(dst, src)
}
func (m *MP_LevelChangeEvent) XXX_Size() int {
	return xxx_messageInfo_MP_LevelChangeEvent.Size(m)
}
func (m *MP_LevelChangeEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_LevelChangeEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_LevelChangeEvent proto.InternalMessageInfo

func (m *MP_LevelChangeEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_LevelChangeEvent) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *MP_LevelChangeEvent) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MP_LevelChangeEvent) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

type MP_PostEvent struct {
	Base                 *MP_Base                   `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	PostEventType        MP_PostEvent_PostEventType `protobuf:"varint,2,opt,name=post_event_type,json=postEventType,proto3,enum=rcmd.recall_etl.MP_PostEvent_PostEventType" json:"post_event_type,omitempty"`
	PostId               string                     `protobuf:"bytes,3,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	AuthorUid            uint32                     `protobuf:"varint,4,opt,name=author_uid,json=authorUid,proto3" json:"author_uid,omitempty"`
	CreateAt             uint64                     `protobuf:"varint,5,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	Content              string                     `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	IsSystemPost         bool                       `protobuf:"varint,7,opt,name=is_system_post,json=isSystemPost,proto3" json:"is_system_post,omitempty"`
	TopicList            []string                   `protobuf:"bytes,8,rep,name=topic_list,json=topicList,proto3" json:"topic_list,omitempty"`
	Pool                 uint32                     `protobuf:"varint,9,opt,name=pool,proto3" json:"pool,omitempty"`
	PostType             MP_PostEvent_PostType      `protobuf:"varint,10,opt,name=post_type,json=postType,proto3,enum=rcmd.recall_etl.MP_PostEvent_PostType" json:"post_type,omitempty"`
	IsPrivate            bool                       `protobuf:"varint,11,opt,name=is_private,json=isPrivate,proto3" json:"is_private,omitempty"`
	Ip                   string                     `protobuf:"bytes,12,opt,name=ip,proto3" json:"ip,omitempty"`
	IsBlack              bool                       `protobuf:"varint,13,opt,name=is_black,json=isBlack,proto3" json:"is_black,omitempty"`
	CommentUid           uint32                     `protobuf:"varint,14,opt,name=comment_uid,json=commentUid,proto3" json:"comment_uid,omitempty"`
	ShowLocation         bool                       `protobuf:"varint,15,opt,name=show_location,json=showLocation,proto3" json:"show_location,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *MP_PostEvent) Reset()         { *m = MP_PostEvent{} }
func (m *MP_PostEvent) String() string { return proto.CompactTextString(m) }
func (*MP_PostEvent) ProtoMessage()    {}
func (*MP_PostEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{30}
}
func (m *MP_PostEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_PostEvent.Unmarshal(m, b)
}
func (m *MP_PostEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_PostEvent.Marshal(b, m, deterministic)
}
func (dst *MP_PostEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_PostEvent.Merge(dst, src)
}
func (m *MP_PostEvent) XXX_Size() int {
	return xxx_messageInfo_MP_PostEvent.Size(m)
}
func (m *MP_PostEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_PostEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_PostEvent proto.InternalMessageInfo

func (m *MP_PostEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_PostEvent) GetPostEventType() MP_PostEvent_PostEventType {
	if m != nil {
		return m.PostEventType
	}
	return MP_PostEvent_UnknownEvent
}

func (m *MP_PostEvent) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MP_PostEvent) GetAuthorUid() uint32 {
	if m != nil {
		return m.AuthorUid
	}
	return 0
}

func (m *MP_PostEvent) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *MP_PostEvent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *MP_PostEvent) GetIsSystemPost() bool {
	if m != nil {
		return m.IsSystemPost
	}
	return false
}

func (m *MP_PostEvent) GetTopicList() []string {
	if m != nil {
		return m.TopicList
	}
	return nil
}

func (m *MP_PostEvent) GetPool() uint32 {
	if m != nil {
		return m.Pool
	}
	return 0
}

func (m *MP_PostEvent) GetPostType() MP_PostEvent_PostType {
	if m != nil {
		return m.PostType
	}
	return MP_PostEvent_InvalidPostType
}

func (m *MP_PostEvent) GetIsPrivate() bool {
	if m != nil {
		return m.IsPrivate
	}
	return false
}

func (m *MP_PostEvent) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *MP_PostEvent) GetIsBlack() bool {
	if m != nil {
		return m.IsBlack
	}
	return false
}

func (m *MP_PostEvent) GetCommentUid() uint32 {
	if m != nil {
		return m.CommentUid
	}
	return 0
}

func (m *MP_PostEvent) GetShowLocation() bool {
	if m != nil {
		return m.ShowLocation
	}
	return false
}

type MP_RiskyWord struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Oper                 uint32   `protobuf:"varint,2,opt,name=oper,proto3" json:"oper,omitempty"`
	Words                []string `protobuf:"bytes,3,rep,name=words,proto3" json:"words,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_RiskyWord) Reset()         { *m = MP_RiskyWord{} }
func (m *MP_RiskyWord) String() string { return proto.CompactTextString(m) }
func (*MP_RiskyWord) ProtoMessage()    {}
func (*MP_RiskyWord) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{31}
}
func (m *MP_RiskyWord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_RiskyWord.Unmarshal(m, b)
}
func (m *MP_RiskyWord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_RiskyWord.Marshal(b, m, deterministic)
}
func (dst *MP_RiskyWord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_RiskyWord.Merge(dst, src)
}
func (m *MP_RiskyWord) XXX_Size() int {
	return xxx_messageInfo_MP_RiskyWord.Size(m)
}
func (m *MP_RiskyWord) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_RiskyWord.DiscardUnknown(m)
}

var xxx_messageInfo_MP_RiskyWord proto.InternalMessageInfo

func (m *MP_RiskyWord) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_RiskyWord) GetOper() uint32 {
	if m != nil {
		return m.Oper
	}
	return 0
}

func (m *MP_RiskyWord) GetWords() []string {
	if m != nil {
		return m.Words
	}
	return nil
}

type MP_ChatCard struct {
	Base                 *MP_Base           `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Op                   MP_ChatCard_Op     `protobuf:"varint,2,opt,name=op,proto3,enum=rcmd.recall_etl.MP_ChatCard_Op" json:"op,omitempty"`
	Card                 *chatcard.ChatCard `protobuf:"bytes,3,opt,name=card,proto3" json:"card,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *MP_ChatCard) Reset()         { *m = MP_ChatCard{} }
func (m *MP_ChatCard) String() string { return proto.CompactTextString(m) }
func (*MP_ChatCard) ProtoMessage()    {}
func (*MP_ChatCard) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{32}
}
func (m *MP_ChatCard) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChatCard.Unmarshal(m, b)
}
func (m *MP_ChatCard) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChatCard.Marshal(b, m, deterministic)
}
func (dst *MP_ChatCard) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChatCard.Merge(dst, src)
}
func (m *MP_ChatCard) XXX_Size() int {
	return xxx_messageInfo_MP_ChatCard.Size(m)
}
func (m *MP_ChatCard) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChatCard.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChatCard proto.InternalMessageInfo

func (m *MP_ChatCard) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChatCard) GetOp() MP_ChatCard_Op {
	if m != nil {
		return m.Op
	}
	return MP_ChatCard_Invalid
}

func (m *MP_ChatCard) GetCard() *chatcard.ChatCard {
	if m != nil {
		return m.Card
	}
	return nil
}

type MP_ChatCardHitOn struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Target               uint32   `protobuf:"varint,2,opt,name=target,proto3" json:"target,omitempty"`
	Timestamp            uint32   `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	HitOnType            uint32   `protobuf:"varint,4,opt,name=hit_on_type,json=hitOnType,proto3" json:"hit_on_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ChatCardHitOn) Reset()         { *m = MP_ChatCardHitOn{} }
func (m *MP_ChatCardHitOn) String() string { return proto.CompactTextString(m) }
func (*MP_ChatCardHitOn) ProtoMessage()    {}
func (*MP_ChatCardHitOn) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{33}
}
func (m *MP_ChatCardHitOn) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChatCardHitOn.Unmarshal(m, b)
}
func (m *MP_ChatCardHitOn) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChatCardHitOn.Marshal(b, m, deterministic)
}
func (dst *MP_ChatCardHitOn) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChatCardHitOn.Merge(dst, src)
}
func (m *MP_ChatCardHitOn) XXX_Size() int {
	return xxx_messageInfo_MP_ChatCardHitOn.Size(m)
}
func (m *MP_ChatCardHitOn) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChatCardHitOn.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChatCardHitOn proto.InternalMessageInfo

func (m *MP_ChatCardHitOn) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChatCardHitOn) GetTarget() uint32 {
	if m != nil {
		return m.Target
	}
	return 0
}

func (m *MP_ChatCardHitOn) GetTimestamp() uint32 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *MP_ChatCardHitOn) GetHitOnType() uint32 {
	if m != nil {
		return m.HitOnType
	}
	return 0
}

// ref: https://gitlab.ttyuyin.com/avengers/quicksilver/blob/develop/protocol/services/minToolkit/kafka/pb/kfk_tt_auth_ev.pb.go
type MP_TTAuthEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Ip                   string   `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	Ts                   uint64   `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	Phone                string   `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	Imei                 string   `protobuf:"bytes,5,opt,name=imei,proto3" json:"imei,omitempty"`
	Idfa                 string   `protobuf:"bytes,6,opt,name=idfa,proto3" json:"idfa,omitempty"`
	ClientType           uint32   `protobuf:"varint,7,opt,name=client_type,json=clientType,proto3" json:"client_type,omitempty"`
	ClientVersion        uint32   `protobuf:"varint,8,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	DeviceIdHex          string   `protobuf:"bytes,9,opt,name=device_id_hex,json=deviceIdHex,proto3" json:"device_id_hex,omitempty"`
	IsAnti               bool     `protobuf:"varint,10,opt,name=is_anti,json=isAnti,proto3" json:"is_anti,omitempty"`
	MarketId             uint32   `protobuf:"varint,11,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	CmdId                uint32   `protobuf:"varint,12,opt,name=cmd_id,json=cmdId,proto3" json:"cmd_id,omitempty"`
	IsAutoLogin          bool     `protobuf:"varint,13,opt,name=is_auto_login,json=isAutoLogin,proto3" json:"is_auto_login,omitempty"`
	PkgChannel           string   `protobuf:"bytes,14,opt,name=pkg_channel,json=pkgChannel,proto3" json:"pkg_channel,omitempty"`
	SmDeviceId           string   `protobuf:"bytes,15,opt,name=sm_device_id,json=smDeviceId,proto3" json:"sm_device_id,omitempty"`
	DeviceModel          string   `protobuf:"bytes,16,opt,name=device_model,json=deviceModel,proto3" json:"device_model,omitempty"`
	SdkGameId            int64    `protobuf:"varint,17,opt,name=sdk_game_id,json=sdkGameId,proto3" json:"sdk_game_id,omitempty"`
	LastLoginTs          uint64   `protobuf:"varint,18,opt,name=last_login_ts,json=lastLoginTs,proto3" json:"last_login_ts,omitempty"`
	DeviceInfo           string   `protobuf:"bytes,19,opt,name=device_info,json=deviceInfo,proto3" json:"device_info,omitempty"`
	Oaid                 string   `protobuf:"bytes,20,opt,name=oaid,proto3" json:"oaid,omitempty"`
	AndroidId            string   `protobuf:"bytes,21,opt,name=android_id,json=androidId,proto3" json:"android_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_TTAuthEvent) Reset()         { *m = MP_TTAuthEvent{} }
func (m *MP_TTAuthEvent) String() string { return proto.CompactTextString(m) }
func (*MP_TTAuthEvent) ProtoMessage()    {}
func (*MP_TTAuthEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{34}
}
func (m *MP_TTAuthEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_TTAuthEvent.Unmarshal(m, b)
}
func (m *MP_TTAuthEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_TTAuthEvent.Marshal(b, m, deterministic)
}
func (dst *MP_TTAuthEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_TTAuthEvent.Merge(dst, src)
}
func (m *MP_TTAuthEvent) XXX_Size() int {
	return xxx_messageInfo_MP_TTAuthEvent.Size(m)
}
func (m *MP_TTAuthEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_TTAuthEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_TTAuthEvent proto.InternalMessageInfo

func (m *MP_TTAuthEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_TTAuthEvent) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *MP_TTAuthEvent) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *MP_TTAuthEvent) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *MP_TTAuthEvent) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *MP_TTAuthEvent) GetIdfa() string {
	if m != nil {
		return m.Idfa
	}
	return ""
}

func (m *MP_TTAuthEvent) GetClientType() uint32 {
	if m != nil {
		return m.ClientType
	}
	return 0
}

func (m *MP_TTAuthEvent) GetClientVersion() uint32 {
	if m != nil {
		return m.ClientVersion
	}
	return 0
}

func (m *MP_TTAuthEvent) GetDeviceIdHex() string {
	if m != nil {
		return m.DeviceIdHex
	}
	return ""
}

func (m *MP_TTAuthEvent) GetIsAnti() bool {
	if m != nil {
		return m.IsAnti
	}
	return false
}

func (m *MP_TTAuthEvent) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *MP_TTAuthEvent) GetCmdId() uint32 {
	if m != nil {
		return m.CmdId
	}
	return 0
}

func (m *MP_TTAuthEvent) GetIsAutoLogin() bool {
	if m != nil {
		return m.IsAutoLogin
	}
	return false
}

func (m *MP_TTAuthEvent) GetPkgChannel() string {
	if m != nil {
		return m.PkgChannel
	}
	return ""
}

func (m *MP_TTAuthEvent) GetSmDeviceId() string {
	if m != nil {
		return m.SmDeviceId
	}
	return ""
}

func (m *MP_TTAuthEvent) GetDeviceModel() string {
	if m != nil {
		return m.DeviceModel
	}
	return ""
}

func (m *MP_TTAuthEvent) GetSdkGameId() int64 {
	if m != nil {
		return m.SdkGameId
	}
	return 0
}

func (m *MP_TTAuthEvent) GetLastLoginTs() uint64 {
	if m != nil {
		return m.LastLoginTs
	}
	return 0
}

func (m *MP_TTAuthEvent) GetDeviceInfo() string {
	if m != nil {
		return m.DeviceInfo
	}
	return ""
}

func (m *MP_TTAuthEvent) GetOaid() string {
	if m != nil {
		return m.Oaid
	}
	return ""
}

func (m *MP_TTAuthEvent) GetAndroidId() string {
	if m != nil {
		return m.AndroidId
	}
	return ""
}

type MP_AttributionTT struct {
	Base                    *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Id                      string   `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	MaterialTagName         string   `protobuf:"bytes,3,opt,name=material_tag_name,json=materialTagName,proto3" json:"material_tag_name,omitempty"`
	IsExistsMaterialTagName bool     `protobuf:"varint,4,opt,name=is_exists_material_tag_name,json=isExistsMaterialTagName,proto3" json:"is_exists_material_tag_name,omitempty"`
	BusinessId              int64    `protobuf:"varint,5,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	IsExistsBusinessId      bool     `protobuf:"varint,6,opt,name=is_exists_business_id,json=isExistsBusinessId,proto3" json:"is_exists_business_id,omitempty"`
	CpId                    string   `protobuf:"bytes,7,opt,name=cp_id,json=cpId,proto3" json:"cp_id,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *MP_AttributionTT) Reset()         { *m = MP_AttributionTT{} }
func (m *MP_AttributionTT) String() string { return proto.CompactTextString(m) }
func (*MP_AttributionTT) ProtoMessage()    {}
func (*MP_AttributionTT) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{35}
}
func (m *MP_AttributionTT) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_AttributionTT.Unmarshal(m, b)
}
func (m *MP_AttributionTT) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_AttributionTT.Marshal(b, m, deterministic)
}
func (dst *MP_AttributionTT) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_AttributionTT.Merge(dst, src)
}
func (m *MP_AttributionTT) XXX_Size() int {
	return xxx_messageInfo_MP_AttributionTT.Size(m)
}
func (m *MP_AttributionTT) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_AttributionTT.DiscardUnknown(m)
}

var xxx_messageInfo_MP_AttributionTT proto.InternalMessageInfo

func (m *MP_AttributionTT) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_AttributionTT) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MP_AttributionTT) GetMaterialTagName() string {
	if m != nil {
		return m.MaterialTagName
	}
	return ""
}

func (m *MP_AttributionTT) GetIsExistsMaterialTagName() bool {
	if m != nil {
		return m.IsExistsMaterialTagName
	}
	return false
}

func (m *MP_AttributionTT) GetBusinessId() int64 {
	if m != nil {
		return m.BusinessId
	}
	return 0
}

func (m *MP_AttributionTT) GetIsExistsBusinessId() bool {
	if m != nil {
		return m.IsExistsBusinessId
	}
	return false
}

func (m *MP_AttributionTT) GetCpId() string {
	if m != nil {
		return m.CpId
	}
	return ""
}

type MP_OfflinePostItem struct {
	PostId               string   `protobuf:"bytes,1,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	Score                float32  `protobuf:"fixed32,2,opt,name=score,proto3" json:"score,omitempty"`
	Ranking              uint32   `protobuf:"varint,3,opt,name=ranking,proto3" json:"ranking,omitempty"`
	Strategies           string   `protobuf:"bytes,4,opt,name=strategies,proto3" json:"strategies,omitempty"`
	ItemOwnerUid         string   `protobuf:"bytes,5,opt,name=item_owner_uid,json=itemOwnerUid,proto3" json:"item_owner_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_OfflinePostItem) Reset()         { *m = MP_OfflinePostItem{} }
func (m *MP_OfflinePostItem) String() string { return proto.CompactTextString(m) }
func (*MP_OfflinePostItem) ProtoMessage()    {}
func (*MP_OfflinePostItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{36}
}
func (m *MP_OfflinePostItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_OfflinePostItem.Unmarshal(m, b)
}
func (m *MP_OfflinePostItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_OfflinePostItem.Marshal(b, m, deterministic)
}
func (dst *MP_OfflinePostItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_OfflinePostItem.Merge(dst, src)
}
func (m *MP_OfflinePostItem) XXX_Size() int {
	return xxx_messageInfo_MP_OfflinePostItem.Size(m)
}
func (m *MP_OfflinePostItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_OfflinePostItem.DiscardUnknown(m)
}

var xxx_messageInfo_MP_OfflinePostItem proto.InternalMessageInfo

func (m *MP_OfflinePostItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MP_OfflinePostItem) GetScore() float32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *MP_OfflinePostItem) GetRanking() uint32 {
	if m != nil {
		return m.Ranking
	}
	return 0
}

func (m *MP_OfflinePostItem) GetStrategies() string {
	if m != nil {
		return m.Strategies
	}
	return ""
}

func (m *MP_OfflinePostItem) GetItemOwnerUid() string {
	if m != nil {
		return m.ItemOwnerUid
	}
	return ""
}

type MP_PostOfflineUid2Item struct {
	Base                 *MP_Base                 `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	WriteType            OfflineUid2ItemWriteType `protobuf:"varint,2,opt,name=write_type,json=writeType,proto3,enum=rcmd.recall_etl.OfflineUid2ItemWriteType" json:"write_type,omitempty"`
	ItemList             []*MP_OfflinePostItem    `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	UpdateTime           uint32                   `protobuf:"varint,4,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *MP_PostOfflineUid2Item) Reset()         { *m = MP_PostOfflineUid2Item{} }
func (m *MP_PostOfflineUid2Item) String() string { return proto.CompactTextString(m) }
func (*MP_PostOfflineUid2Item) ProtoMessage()    {}
func (*MP_PostOfflineUid2Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{37}
}
func (m *MP_PostOfflineUid2Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_PostOfflineUid2Item.Unmarshal(m, b)
}
func (m *MP_PostOfflineUid2Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_PostOfflineUid2Item.Marshal(b, m, deterministic)
}
func (dst *MP_PostOfflineUid2Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_PostOfflineUid2Item.Merge(dst, src)
}
func (m *MP_PostOfflineUid2Item) XXX_Size() int {
	return xxx_messageInfo_MP_PostOfflineUid2Item.Size(m)
}
func (m *MP_PostOfflineUid2Item) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_PostOfflineUid2Item.DiscardUnknown(m)
}

var xxx_messageInfo_MP_PostOfflineUid2Item proto.InternalMessageInfo

func (m *MP_PostOfflineUid2Item) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_PostOfflineUid2Item) GetWriteType() OfflineUid2ItemWriteType {
	if m != nil {
		return m.WriteType
	}
	return OfflineUid2ItemWriteType_UnknownOfflineUid2ItemWriteType
}

func (m *MP_PostOfflineUid2Item) GetItemList() []*MP_OfflinePostItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *MP_PostOfflineUid2Item) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type MP_PostStatistcsItem struct {
	SrcIp                string       `protobuf:"bytes,1,opt,name=src_ip,json=srcIp,proto3" json:"src_ip,omitempty"`
	ServerTimestamp      int32        `protobuf:"varint,2,opt,name=server_timestamp,json=serverTimestamp,proto3" json:"server_timestamp,omitempty"`
	BizType              string       `protobuf:"bytes,3,opt,name=biz_type,json=bizType,proto3" json:"biz_type,omitempty"`
	Uid                  int32        `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	PostUid              string       `protobuf:"bytes,5,opt,name=post_uid,json=postUid,proto3" json:"post_uid,omitempty"`
	PostId               string       `protobuf:"bytes,6,opt,name=post_id,json=postId,proto3" json:"post_id,omitempty"`
	PublishTime          string       `protobuf:"bytes,7,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty"`
	PostType             string       `protobuf:"bytes,8,opt,name=post_type,json=postType,proto3" json:"post_type,omitempty"`
	TopicId              string       `protobuf:"bytes,9,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty"`
	City                 string       `protobuf:"bytes,10,opt,name=city,proto3" json:"city,omitempty"`
	Source               string       `protobuf:"bytes,11,opt,name=source,proto3" json:"source,omitempty"`
	TotalDate            int32        `protobuf:"varint,12,opt,name=total_date,json=totalDate,proto3" json:"total_date,omitempty"`
	StatType             PostStatType `protobuf:"varint,13,opt,name=stat_type,json=statType,proto3,enum=rcmd.recall_etl.PostStatType" json:"stat_type,omitempty"`
	StatCount            int32        `protobuf:"varint,14,opt,name=stat_count,json=statCount,proto3" json:"stat_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *MP_PostStatistcsItem) Reset()         { *m = MP_PostStatistcsItem{} }
func (m *MP_PostStatistcsItem) String() string { return proto.CompactTextString(m) }
func (*MP_PostStatistcsItem) ProtoMessage()    {}
func (*MP_PostStatistcsItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{38}
}
func (m *MP_PostStatistcsItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_PostStatistcsItem.Unmarshal(m, b)
}
func (m *MP_PostStatistcsItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_PostStatistcsItem.Marshal(b, m, deterministic)
}
func (dst *MP_PostStatistcsItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_PostStatistcsItem.Merge(dst, src)
}
func (m *MP_PostStatistcsItem) XXX_Size() int {
	return xxx_messageInfo_MP_PostStatistcsItem.Size(m)
}
func (m *MP_PostStatistcsItem) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_PostStatistcsItem.DiscardUnknown(m)
}

var xxx_messageInfo_MP_PostStatistcsItem proto.InternalMessageInfo

func (m *MP_PostStatistcsItem) GetSrcIp() string {
	if m != nil {
		return m.SrcIp
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetServerTimestamp() int32 {
	if m != nil {
		return m.ServerTimestamp
	}
	return 0
}

func (m *MP_PostStatistcsItem) GetBizType() string {
	if m != nil {
		return m.BizType
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetUid() int32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_PostStatistcsItem) GetPostUid() string {
	if m != nil {
		return m.PostUid
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetPostId() string {
	if m != nil {
		return m.PostId
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetPublishTime() string {
	if m != nil {
		return m.PublishTime
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetPostType() string {
	if m != nil {
		return m.PostType
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetTopicId() string {
	if m != nil {
		return m.TopicId
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *MP_PostStatistcsItem) GetTotalDate() int32 {
	if m != nil {
		return m.TotalDate
	}
	return 0
}

func (m *MP_PostStatistcsItem) GetStatType() PostStatType {
	if m != nil {
		return m.StatType
	}
	return PostStatType_StatType_Unknow
}

func (m *MP_PostStatistcsItem) GetStatCount() int32 {
	if m != nil {
		return m.StatCount
	}
	return 0
}

// 房间创建/变更事件
type MP_ChannelInfoEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelName          string   `protobuf:"bytes,4,opt,name=channel_name,json=channelName,proto3" json:"channel_name,omitempty"`
	BindType             string   `protobuf:"bytes,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	MicMode              string   `protobuf:"bytes,6,opt,name=mic_mode,json=micMode,proto3" json:"mic_mode,omitempty"`
	TagId                string   `protobuf:"bytes,7,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagName              string   `protobuf:"bytes,8,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,9,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	AppId                string   `protobuf:"bytes,10,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	ChannelPara          string   `protobuf:"bytes,11,opt,name=channel_para,json=channelPara,proto3" json:"channel_para,omitempty"`
	LocationList         string   `protobuf:"bytes,12,opt,name=location_list,json=locationList,proto3" json:"location_list,omitempty"`
	CreateTime           string   `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           string   `protobuf:"bytes,14,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ChannelInfoEvent) Reset()         { *m = MP_ChannelInfoEvent{} }
func (m *MP_ChannelInfoEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelInfoEvent) ProtoMessage()    {}
func (*MP_ChannelInfoEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{39}
}
func (m *MP_ChannelInfoEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelInfoEvent.Unmarshal(m, b)
}
func (m *MP_ChannelInfoEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelInfoEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelInfoEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelInfoEvent.Merge(dst, src)
}
func (m *MP_ChannelInfoEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelInfoEvent.Size(m)
}
func (m *MP_ChannelInfoEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelInfoEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelInfoEvent proto.InternalMessageInfo

func (m *MP_ChannelInfoEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelInfoEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_ChannelInfoEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_ChannelInfoEvent) GetChannelName() string {
	if m != nil {
		return m.ChannelName
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetBindType() string {
	if m != nil {
		return m.BindType
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetMicMode() string {
	if m != nil {
		return m.MicMode
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *MP_ChannelInfoEvent) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetChannelPara() string {
	if m != nil {
		return m.ChannelPara
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetLocationList() string {
	if m != nil {
		return m.LocationList
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *MP_ChannelInfoEvent) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

// 直播房间等级
type MP_ChannelLevelEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Level                string   `protobuf:"bytes,3,opt,name=level,proto3" json:"level,omitempty"`
	LevelType            string   `protobuf:"bytes,4,opt,name=level_type,json=levelType,proto3" json:"level_type,omitempty"`
	BindType             string   `protobuf:"bytes,5,opt,name=bind_type,json=bindType,proto3" json:"bind_type,omitempty"`
	EnterType            string   `protobuf:"bytes,6,opt,name=enter_type,json=enterType,proto3" json:"enter_type,omitempty"`
	Score                string   `protobuf:"bytes,7,opt,name=score,proto3" json:"score,omitempty"`
	CreateTime           string   `protobuf:"bytes,8,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           string   `protobuf:"bytes,9,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ChannelLevelEvent) Reset()         { *m = MP_ChannelLevelEvent{} }
func (m *MP_ChannelLevelEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelLevelEvent) ProtoMessage()    {}
func (*MP_ChannelLevelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{40}
}
func (m *MP_ChannelLevelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelLevelEvent.Unmarshal(m, b)
}
func (m *MP_ChannelLevelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelLevelEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelLevelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelLevelEvent.Merge(dst, src)
}
func (m *MP_ChannelLevelEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelLevelEvent.Size(m)
}
func (m *MP_ChannelLevelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelLevelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelLevelEvent proto.InternalMessageInfo

func (m *MP_ChannelLevelEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelLevelEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_ChannelLevelEvent) GetLevel() string {
	if m != nil {
		return m.Level
	}
	return ""
}

func (m *MP_ChannelLevelEvent) GetLevelType() string {
	if m != nil {
		return m.LevelType
	}
	return ""
}

func (m *MP_ChannelLevelEvent) GetBindType() string {
	if m != nil {
		return m.BindType
	}
	return ""
}

func (m *MP_ChannelLevelEvent) GetEnterType() string {
	if m != nil {
		return m.EnterType
	}
	return ""
}

func (m *MP_ChannelLevelEvent) GetScore() string {
	if m != nil {
		return m.Score
	}
	return ""
}

func (m *MP_ChannelLevelEvent) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

func (m *MP_ChannelLevelEvent) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

// 直播开播状态变更
//    CLOSE = 0;
//    OPEN  = 1;
//    PAUSE = 2;
//    CONTINUE = 3; //从暂停状态->继续状态
type MP_ChannelLiveStatusEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ChannelLiveStatusEvent) Reset()         { *m = MP_ChannelLiveStatusEvent{} }
func (m *MP_ChannelLiveStatusEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelLiveStatusEvent) ProtoMessage()    {}
func (*MP_ChannelLiveStatusEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{41}
}
func (m *MP_ChannelLiveStatusEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelLiveStatusEvent.Unmarshal(m, b)
}
func (m *MP_ChannelLiveStatusEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelLiveStatusEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelLiveStatusEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelLiveStatusEvent.Merge(dst, src)
}
func (m *MP_ChannelLiveStatusEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelLiveStatusEvent.Size(m)
}
func (m *MP_ChannelLiveStatusEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelLiveStatusEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelLiveStatusEvent proto.InternalMessageInfo

func (m *MP_ChannelLiveStatusEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelLiveStatusEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_ChannelLiveStatusEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_ChannelLiveStatusEvent) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 加入粉丝团
type MP_AnchorFansEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	FansUid              uint32   `protobuf:"varint,2,opt,name=fans_uid,json=fansUid,proto3" json:"fans_uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,3,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	EventType            uint32   `protobuf:"varint,4,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_AnchorFansEvent) Reset()         { *m = MP_AnchorFansEvent{} }
func (m *MP_AnchorFansEvent) String() string { return proto.CompactTextString(m) }
func (*MP_AnchorFansEvent) ProtoMessage()    {}
func (*MP_AnchorFansEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{42}
}
func (m *MP_AnchorFansEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_AnchorFansEvent.Unmarshal(m, b)
}
func (m *MP_AnchorFansEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_AnchorFansEvent.Marshal(b, m, deterministic)
}
func (dst *MP_AnchorFansEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_AnchorFansEvent.Merge(dst, src)
}
func (m *MP_AnchorFansEvent) XXX_Size() int {
	return xxx_messageInfo_MP_AnchorFansEvent.Size(m)
}
func (m *MP_AnchorFansEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_AnchorFansEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_AnchorFansEvent proto.InternalMessageInfo

func (m *MP_AnchorFansEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_AnchorFansEvent) GetFansUid() uint32 {
	if m != nil {
		return m.FansUid
	}
	return 0
}

func (m *MP_AnchorFansEvent) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *MP_AnchorFansEvent) GetEventType() uint32 {
	if m != nil {
		return m.EventType
	}
	return 0
}

type MP_ChannelOpenGameStatusEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameStatus           uint32   `protobuf:"varint,3,opt,name=game_status,json=gameStatus,proto3" json:"game_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_ChannelOpenGameStatusEvent) Reset()         { *m = MP_ChannelOpenGameStatusEvent{} }
func (m *MP_ChannelOpenGameStatusEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelOpenGameStatusEvent) ProtoMessage()    {}
func (*MP_ChannelOpenGameStatusEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{43}
}
func (m *MP_ChannelOpenGameStatusEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelOpenGameStatusEvent.Unmarshal(m, b)
}
func (m *MP_ChannelOpenGameStatusEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelOpenGameStatusEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelOpenGameStatusEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelOpenGameStatusEvent.Merge(dst, src)
}
func (m *MP_ChannelOpenGameStatusEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelOpenGameStatusEvent.Size(m)
}
func (m *MP_ChannelOpenGameStatusEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelOpenGameStatusEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelOpenGameStatusEvent proto.InternalMessageInfo

func (m *MP_ChannelOpenGameStatusEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelOpenGameStatusEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_ChannelOpenGameStatusEvent) GetGameStatus() uint32 {
	if m != nil {
		return m.GameStatus
	}
	return 0
}

type MP_ChannelGameTabsEvent struct {
	Base                 *MP_Base                          `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelGameTabs      *channel_game_tab.ChannelGameTabs `protobuf:"bytes,2,opt,name=channel_game_tabs,json=channelGameTabs,proto3" json:"channel_game_tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *MP_ChannelGameTabsEvent) Reset()         { *m = MP_ChannelGameTabsEvent{} }
func (m *MP_ChannelGameTabsEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelGameTabsEvent) ProtoMessage()    {}
func (*MP_ChannelGameTabsEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{44}
}
func (m *MP_ChannelGameTabsEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelGameTabsEvent.Unmarshal(m, b)
}
func (m *MP_ChannelGameTabsEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelGameTabsEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelGameTabsEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelGameTabsEvent.Merge(dst, src)
}
func (m *MP_ChannelGameTabsEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelGameTabsEvent.Size(m)
}
func (m *MP_ChannelGameTabsEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelGameTabsEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelGameTabsEvent proto.InternalMessageInfo

func (m *MP_ChannelGameTabsEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelGameTabsEvent) GetChannelGameTabs() *channel_game_tab.ChannelGameTabs {
	if m != nil {
		return m.ChannelGameTabs
	}
	return nil
}

type MP_ChannelCompanionEvent struct {
	Base                 *MP_Base      `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	ChannelIds           []uint32      `protobuf:"varint,2,rep,packed,name=channel_ids,json=channelIds,proto3" json:"channel_ids,omitempty"`
	CompanionType        CompanionType `protobuf:"varint,3,opt,name=companion_type,json=companionType,proto3,enum=rcmd.recall_etl.CompanionType" json:"companion_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *MP_ChannelCompanionEvent) Reset()         { *m = MP_ChannelCompanionEvent{} }
func (m *MP_ChannelCompanionEvent) String() string { return proto.CompactTextString(m) }
func (*MP_ChannelCompanionEvent) ProtoMessage()    {}
func (*MP_ChannelCompanionEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{45}
}
func (m *MP_ChannelCompanionEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_ChannelCompanionEvent.Unmarshal(m, b)
}
func (m *MP_ChannelCompanionEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_ChannelCompanionEvent.Marshal(b, m, deterministic)
}
func (dst *MP_ChannelCompanionEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_ChannelCompanionEvent.Merge(dst, src)
}
func (m *MP_ChannelCompanionEvent) XXX_Size() int {
	return xxx_messageInfo_MP_ChannelCompanionEvent.Size(m)
}
func (m *MP_ChannelCompanionEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_ChannelCompanionEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_ChannelCompanionEvent proto.InternalMessageInfo

func (m *MP_ChannelCompanionEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_ChannelCompanionEvent) GetChannelIds() []uint32 {
	if m != nil {
		return m.ChannelIds
	}
	return nil
}

func (m *MP_ChannelCompanionEvent) GetCompanionType() CompanionType {
	if m != nil {
		return m.CompanionType
	}
	return CompanionType_Normal
}

type MP_UserChannelCompanionEnterEvent struct {
	Base                 *MP_Base `protobuf:"bytes,1,opt,name=base,proto3" json:"base,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ChannelId            uint32   `protobuf:"varint,3,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MP_UserChannelCompanionEnterEvent) Reset()         { *m = MP_UserChannelCompanionEnterEvent{} }
func (m *MP_UserChannelCompanionEnterEvent) String() string { return proto.CompactTextString(m) }
func (*MP_UserChannelCompanionEnterEvent) ProtoMessage()    {}
func (*MP_UserChannelCompanionEnterEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{46}
}
func (m *MP_UserChannelCompanionEnterEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_UserChannelCompanionEnterEvent.Unmarshal(m, b)
}
func (m *MP_UserChannelCompanionEnterEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_UserChannelCompanionEnterEvent.Marshal(b, m, deterministic)
}
func (dst *MP_UserChannelCompanionEnterEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_UserChannelCompanionEnterEvent.Merge(dst, src)
}
func (m *MP_UserChannelCompanionEnterEvent) XXX_Size() int {
	return xxx_messageInfo_MP_UserChannelCompanionEnterEvent.Size(m)
}
func (m *MP_UserChannelCompanionEnterEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_UserChannelCompanionEnterEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_UserChannelCompanionEnterEvent proto.InternalMessageInfo

func (m *MP_UserChannelCompanionEnterEvent) GetBase() *MP_Base {
	if m != nil {
		return m.Base
	}
	return nil
}

func (m *MP_UserChannelCompanionEnterEvent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MP_UserChannelCompanionEnterEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type MP_SingChannelEvent struct {
	ChannelId            uint32                         `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	TagId                uint32                         `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Status               MP_SingChannelEvent_Status     `protobuf:"varint,3,opt,name=status,proto3,enum=rcmd.recall_etl.MP_SingChannelEvent_Status" json:"status,omitempty"`
	OpType               MP_SingChannelEvent_OpType     `protobuf:"varint,4,opt,name=op_type,json=opType,proto3,enum=rcmd.recall_etl.MP_SingChannelEvent_OpType" json:"op_type,omitempty"`
	GameStatus           MP_SingChannelEvent_GameStatus `protobuf:"varint,5,opt,name=game_status,json=gameStatus,proto3,enum=rcmd.recall_etl.MP_SingChannelEvent_GameStatus" json:"game_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *MP_SingChannelEvent) Reset()         { *m = MP_SingChannelEvent{} }
func (m *MP_SingChannelEvent) String() string { return proto.CompactTextString(m) }
func (*MP_SingChannelEvent) ProtoMessage()    {}
func (*MP_SingChannelEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_etl_a68ba07e88d0e62b, []int{47}
}
func (m *MP_SingChannelEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MP_SingChannelEvent.Unmarshal(m, b)
}
func (m *MP_SingChannelEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MP_SingChannelEvent.Marshal(b, m, deterministic)
}
func (dst *MP_SingChannelEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MP_SingChannelEvent.Merge(dst, src)
}
func (m *MP_SingChannelEvent) XXX_Size() int {
	return xxx_messageInfo_MP_SingChannelEvent.Size(m)
}
func (m *MP_SingChannelEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_MP_SingChannelEvent.DiscardUnknown(m)
}

var xxx_messageInfo_MP_SingChannelEvent proto.InternalMessageInfo

func (m *MP_SingChannelEvent) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MP_SingChannelEvent) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *MP_SingChannelEvent) GetStatus() MP_SingChannelEvent_Status {
	if m != nil {
		return m.Status
	}
	return MP_SingChannelEvent_StatusInvalid
}

func (m *MP_SingChannelEvent) GetOpType() MP_SingChannelEvent_OpType {
	if m != nil {
		return m.OpType
	}
	return MP_SingChannelEvent_OpTypeInvalid
}

func (m *MP_SingChannelEvent) GetGameStatus() MP_SingChannelEvent_GameStatus {
	if m != nil {
		return m.GameStatus
	}
	return MP_SingChannelEvent_GameStatusNotStart
}

func init() {
	proto.RegisterType((*SetLoginDurationReq)(nil), "rcmd.recall_etl.SetLoginDurationReq")
	proto.RegisterType((*SetLoginDurationRsp)(nil), "rcmd.recall_etl.SetLoginDurationRsp")
	proto.RegisterType((*MockDeliverReq)(nil), "rcmd.recall_etl.MockDeliverReq")
	proto.RegisterType((*MockDeliverRsp)(nil), "rcmd.recall_etl.MockDeliverRsp")
	proto.RegisterType((*MockLoginReq)(nil), "rcmd.recall_etl.MockLoginReq")
	proto.RegisterType((*MockLoginRsp)(nil), "rcmd.recall_etl.MockLoginRsp")
	proto.RegisterType((*ImSinkData)(nil), "rcmd.recall_etl.ImSinkData")
	proto.RegisterType((*ActiveUserSinkData)(nil), "rcmd.recall_etl.ActiveUserSinkData")
	proto.RegisterType((*GetOuterDataReq)(nil), "rcmd.recall_etl.GetOuterDataReq")
	proto.RegisterType((*GetOuterDataRsp)(nil), "rcmd.recall_etl.GetOuterDataRsp")
	proto.RegisterType((*MP_Base)(nil), "rcmd.recall_etl.MP_Base")
	proto.RegisterType((*MP_UserBase)(nil), "rcmd.recall_etl.MP_UserBase")
	proto.RegisterType((*MP_UserTagChange)(nil), "rcmd.recall_etl.MP_UserTagChange")
	proto.RegisterType((*MP_UserLogin)(nil), "rcmd.recall_etl.MP_UserLogin")
	proto.RegisterType((*MP_IM)(nil), "rcmd.recall_etl.MP_IM")
	proto.RegisterType((*MP_MicEvent)(nil), "rcmd.recall_etl.MP_MicEvent")
	proto.RegisterType((*MP_PlayerSetting)(nil), "rcmd.recall_etl.MP_PlayerSetting")
	proto.RegisterType((*MP_BlackUser)(nil), "rcmd.recall_etl.MP_BlackUser")
	proto.RegisterType((*MP_DeliverFilter)(nil), "rcmd.recall_etl.MP_DeliverFilter")
	proto.RegisterType((*MP_TopicChannelEvent)(nil), "rcmd.recall_etl.MP_TopicChannelEvent")
	proto.RegisterMapType((map[uint32]ChannelTypeId)(nil), "rcmd.recall_etl.MP_TopicChannelEvent.ChannelTypeEntry")
	proto.RegisterType((*BlockOption)(nil), "rcmd.recall_etl.BlockOption")
	proto.RegisterType((*ChannelType)(nil), "rcmd.recall_etl.ChannelType")
	proto.RegisterType((*MP_TopicChannelQuality)(nil), "rcmd.recall_etl.MP_TopicChannelQuality")
	proto.RegisterType((*MP_ChannelEvent)(nil), "rcmd.recall_etl.MP_ChannelEvent")
	proto.RegisterType((*MP_RadarEvent)(nil), "rcmd.recall_etl.MP_RadarEvent")
	proto.RegisterType((*MP_RadarDatingEvent)(nil), "rcmd.recall_etl.MP_RadarDatingEvent")
	proto.RegisterType((*MP_UserBackgroundEvent)(nil), "rcmd.recall_etl.MP_UserBackgroundEvent")
	proto.RegisterType((*MP_BindCpIdEvent)(nil), "rcmd.recall_etl.MP_BindCpIdEvent")
	proto.RegisterType((*MP_FactorChangeEvent)(nil), "rcmd.recall_etl.MP_FactorChangeEvent")
	proto.RegisterType((*MP_LevelChangeEvent)(nil), "rcmd.recall_etl.MP_LevelChangeEvent")
	proto.RegisterType((*MP_PostEvent)(nil), "rcmd.recall_etl.MP_PostEvent")
	proto.RegisterType((*MP_RiskyWord)(nil), "rcmd.recall_etl.MP_RiskyWord")
	proto.RegisterType((*MP_ChatCard)(nil), "rcmd.recall_etl.MP_ChatCard")
	proto.RegisterType((*MP_ChatCardHitOn)(nil), "rcmd.recall_etl.MP_ChatCardHitOn")
	proto.RegisterType((*MP_TTAuthEvent)(nil), "rcmd.recall_etl.MP_TTAuthEvent")
	proto.RegisterType((*MP_AttributionTT)(nil), "rcmd.recall_etl.MP_AttributionTT")
	proto.RegisterType((*MP_OfflinePostItem)(nil), "rcmd.recall_etl.MP_OfflinePostItem")
	proto.RegisterType((*MP_PostOfflineUid2Item)(nil), "rcmd.recall_etl.MP_PostOfflineUid2Item")
	proto.RegisterType((*MP_PostStatistcsItem)(nil), "rcmd.recall_etl.MP_PostStatistcsItem")
	proto.RegisterType((*MP_ChannelInfoEvent)(nil), "rcmd.recall_etl.MP_ChannelInfoEvent")
	proto.RegisterType((*MP_ChannelLevelEvent)(nil), "rcmd.recall_etl.MP_ChannelLevelEvent")
	proto.RegisterType((*MP_ChannelLiveStatusEvent)(nil), "rcmd.recall_etl.MP_ChannelLiveStatusEvent")
	proto.RegisterType((*MP_AnchorFansEvent)(nil), "rcmd.recall_etl.MP_AnchorFansEvent")
	proto.RegisterType((*MP_ChannelOpenGameStatusEvent)(nil), "rcmd.recall_etl.MP_ChannelOpenGameStatusEvent")
	proto.RegisterType((*MP_ChannelGameTabsEvent)(nil), "rcmd.recall_etl.MP_ChannelGameTabsEvent")
	proto.RegisterType((*MP_ChannelCompanionEvent)(nil), "rcmd.recall_etl.MP_ChannelCompanionEvent")
	proto.RegisterType((*MP_UserChannelCompanionEnterEvent)(nil), "rcmd.recall_etl.MP_UserChannelCompanionEnterEvent")
	proto.RegisterType((*MP_SingChannelEvent)(nil), "rcmd.recall_etl.MP_SingChannelEvent")
	proto.RegisterEnum("rcmd.recall_etl.OuterType", OuterType_name, OuterType_value)
	proto.RegisterEnum("rcmd.recall_etl.Operator", Operator_name, Operator_value)
	proto.RegisterEnum("rcmd.recall_etl.MicEventType", MicEventType_name, MicEventType_value)
	proto.RegisterEnum("rcmd.recall_etl.ChannelClass", ChannelClass_name, ChannelClass_value)
	proto.RegisterEnum("rcmd.recall_etl.ChannelTypeId", ChannelTypeId_name, ChannelTypeId_value)
	proto.RegisterEnum("rcmd.recall_etl.ChannelDisplayType", ChannelDisplayType_name, ChannelDisplayType_value)
	proto.RegisterEnum("rcmd.recall_etl.OfflineUid2ItemWriteType", OfflineUid2ItemWriteType_name, OfflineUid2ItemWriteType_value)
	proto.RegisterEnum("rcmd.recall_etl.PostStatType", PostStatType_name, PostStatType_value)
	proto.RegisterEnum("rcmd.recall_etl.CompanionType", CompanionType_name, CompanionType_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_TopicChannelEvent_ACTION", MP_TopicChannelEvent_ACTION_name, MP_TopicChannelEvent_ACTION_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_TopicChannelQuality_TopicChannelQuality", MP_TopicChannelQuality_TopicChannelQuality_name, MP_TopicChannelQuality_TopicChannelQuality_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_ChannelEvent_Action", MP_ChannelEvent_Action_name, MP_ChannelEvent_Action_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_PostEvent_PostEventType", MP_PostEvent_PostEventType_name, MP_PostEvent_PostEventType_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_PostEvent_PostType", MP_PostEvent_PostType_name, MP_PostEvent_PostType_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_RiskyWord_Oper", MP_RiskyWord_Oper_name, MP_RiskyWord_Oper_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_ChatCard_Op", MP_ChatCard_Op_name, MP_ChatCard_Op_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_ChatCardHitOn_Typ", MP_ChatCardHitOn_Typ_name, MP_ChatCardHitOn_Typ_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_SingChannelEvent_Status", MP_SingChannelEvent_Status_name, MP_SingChannelEvent_Status_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_SingChannelEvent_OpType", MP_SingChannelEvent_OpType_name, MP_SingChannelEvent_OpType_value)
	proto.RegisterEnum("rcmd.recall_etl.MP_SingChannelEvent_GameStatus", MP_SingChannelEvent_GameStatus_name, MP_SingChannelEvent_GameStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RecallEtlClient is the client API for RecallEtl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecallEtlClient interface {
	// 校准用接口
	GetOuterData(ctx context.Context, in *GetOuterDataReq, opts ...grpc.CallOption) (*GetOuterDataRsp, error)
	MockLogin(ctx context.Context, in *MockLoginReq, opts ...grpc.CallOption) (*MockLoginRsp, error)
	MockDeliver(ctx context.Context, in *MockDeliverReq, opts ...grpc.CallOption) (*MockDeliverRsp, error)
	SetLoginDuration(ctx context.Context, in *SetLoginDurationReq, opts ...grpc.CallOption) (*SetLoginDurationRsp, error)
}

type recallEtlClient struct {
	cc *grpc.ClientConn
}

func NewRecallEtlClient(cc *grpc.ClientConn) RecallEtlClient {
	return &recallEtlClient{cc}
}

func (c *recallEtlClient) GetOuterData(ctx context.Context, in *GetOuterDataReq, opts ...grpc.CallOption) (*GetOuterDataRsp, error) {
	out := new(GetOuterDataRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_etl.RecallEtl/GetOuterData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallEtlClient) MockLogin(ctx context.Context, in *MockLoginReq, opts ...grpc.CallOption) (*MockLoginRsp, error) {
	out := new(MockLoginRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_etl.RecallEtl/MockLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallEtlClient) MockDeliver(ctx context.Context, in *MockDeliverReq, opts ...grpc.CallOption) (*MockDeliverRsp, error) {
	out := new(MockDeliverRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_etl.RecallEtl/MockDeliver", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallEtlClient) SetLoginDuration(ctx context.Context, in *SetLoginDurationReq, opts ...grpc.CallOption) (*SetLoginDurationRsp, error) {
	out := new(SetLoginDurationRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_etl.RecallEtl/SetLoginDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecallEtlServer is the server API for RecallEtl service.
type RecallEtlServer interface {
	// 校准用接口
	GetOuterData(context.Context, *GetOuterDataReq) (*GetOuterDataRsp, error)
	MockLogin(context.Context, *MockLoginReq) (*MockLoginRsp, error)
	MockDeliver(context.Context, *MockDeliverReq) (*MockDeliverRsp, error)
	SetLoginDuration(context.Context, *SetLoginDurationReq) (*SetLoginDurationRsp, error)
}

func RegisterRecallEtlServer(s *grpc.Server, srv RecallEtlServer) {
	s.RegisterService(&_RecallEtl_serviceDesc, srv)
}

func _RecallEtl_GetOuterData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOuterDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallEtlServer).GetOuterData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_etl.RecallEtl/GetOuterData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallEtlServer).GetOuterData(ctx, req.(*GetOuterDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallEtl_MockLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MockLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallEtlServer).MockLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_etl.RecallEtl/MockLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallEtlServer).MockLogin(ctx, req.(*MockLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallEtl_MockDeliver_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MockDeliverReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallEtlServer).MockDeliver(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_etl.RecallEtl/MockDeliver",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallEtlServer).MockDeliver(ctx, req.(*MockDeliverReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallEtl_SetLoginDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetLoginDurationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallEtlServer).SetLoginDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_etl.RecallEtl/SetLoginDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallEtlServer).SetLoginDuration(ctx, req.(*SetLoginDurationReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RecallEtl_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.recall_etl.RecallEtl",
	HandlerType: (*RecallEtlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOuterData",
			Handler:    _RecallEtl_GetOuterData_Handler,
		},
		{
			MethodName: "MockLogin",
			Handler:    _RecallEtl_MockLogin_Handler,
		},
		{
			MethodName: "MockDeliver",
			Handler:    _RecallEtl_MockDeliver_Handler,
		},
		{
			MethodName: "SetLoginDuration",
			Handler:    _RecallEtl_SetLoginDuration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/recall/recall_etl.proto",
}

func init() {
	proto.RegisterFile("rcmd/recall/recall_etl.proto", fileDescriptor_recall_etl_a68ba07e88d0e62b)
}

var fileDescriptor_recall_etl_a68ba07e88d0e62b = []byte{
	// 4099 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x7a, 0x4d, 0x6c, 0x1c, 0x47,
	0x76, 0xbf, 0xe6, 0x7b, 0xfa, 0xcd, 0x90, 0x6c, 0x35, 0xf5, 0x31, 0xa2, 0x2c, 0x4b, 0x6e, 0xaf,
	0xfd, 0xb7, 0xb9, 0x06, 0x65, 0xc9, 0xbb, 0xfb, 0x4f, 0x36, 0x8b, 0x64, 0x47, 0x24, 0x65, 0x0d,
	0x96, 0x23, 0x32, 0xcd, 0xa1, 0x0d, 0x07, 0xc8, 0x36, 0x8a, 0xdd, 0xa5, 0x61, 0x81, 0x3d, 0xdd,
	0xed, 0xae, 0x1a, 0x4a, 0x5c, 0x60, 0x81, 0x24, 0x40, 0x90, 0x43, 0x90, 0x1c, 0xf6, 0x90, 0x4b,
	0x0e, 0x01, 0xb2, 0x40, 0x4e, 0x39, 0x04, 0xc8, 0x2d, 0x87, 0x20, 0x87, 0xdc, 0x93, 0x63, 0x80,
	0x1c, 0x73, 0x49, 0xce, 0x09, 0x10, 0xe4, 0x94, 0xe0, 0xbd, 0xaa, 0xee, 0xe9, 0x19, 0x8e, 0x2c,
	0x8b, 0xb6, 0xf6, 0x34, 0x55, 0xaf, 0x5e, 0x55, 0xbd, 0x7a, 0xf5, 0xde, 0xab, 0xdf, 0x7b, 0xd3,
	0xf0, 0x56, 0x16, 0x4c, 0xc2, 0xfb, 0x19, 0x0f, 0x58, 0x14, 0x99, 0x1f, 0x9f, 0xab, 0x68, 0x2b,
	0xcd, 0x12, 0x95, 0x38, 0x6b, 0x38, 0xba, 0x35, 0x23, 0x6f, 0xdc, 0x5d, 0xc2, 0x1e, 0x24, 0x93,
	0x49, 0x12, 0xeb, 0x19, 0x1b, 0xb7, 0x89, 0x21, 0xe5, 0x99, 0x4c, 0x62, 0x76, 0x5f, 0xa9, 0xfb,
	0x19, 0x0b, 0x59, 0x66, 0x06, 0x37, 0x16, 0x07, 0xa7, 0x92, 0xe7, 0x63, 0x6f, 0x2f, 0x8e, 0x05,
	0x27, 0x4c, 0x05, 0x2c, 0x0b, 0xcd, 0xf8, 0xfb, 0x4b, 0xc6, 0xe3, 0x98, 0x47, 0xfe, 0x98, 0x4d,
	0xb8, 0xaf, 0xd8, 0xb1, 0xe6, 0x73, 0x7f, 0x04, 0xeb, 0x87, 0x5c, 0xed, 0x25, 0x63, 0x11, 0xef,
	0x4c, 0x33, 0xa6, 0x44, 0x12, 0x7b, 0xfc, 0x4b, 0xe7, 0x3d, 0x58, 0x8d, 0x90, 0xe6, 0x87, 0x86,
	0xd8, 0xab, 0xdc, 0xab, 0x7c, 0xb0, 0xe2, 0xad, 0x44, 0x65, 0x4e, 0xf7, 0xff, 0x2d, 0x99, 0x2d,
	0x53, 0xc7, 0x86, 0xda, 0x44, 0x8e, 0x69, 0x8a, 0xe5, 0x61, 0xd3, 0xed, 0xc3, 0xea, 0x30, 0x09,
	0x4e, 0x77, 0x78, 0x24, 0xce, 0x78, 0x86, 0x3b, 0xd8, 0x50, 0x9b, 0x8a, 0xd0, 0x2c, 0x8b, 0x4d,
	0xe7, 0x0e, 0x80, 0x62, 0xd9, 0x98, 0x2b, 0x1f, 0x07, 0xaa, 0x34, 0x60, 0x69, 0xca, 0x91, 0x08,
	0x5d, 0x7b, 0x7e, 0x09, 0x99, 0xba, 0xdf, 0x83, 0x2e, 0x52, 0x68, 0xfb, 0xe5, 0x4b, 0x3a, 0x50,
	0x4f, 0x52, 0x9e, 0x99, 0xc5, 0xa8, 0xed, 0xae, 0x96, 0x67, 0xc9, 0xd4, 0x7d, 0x0c, 0x30, 0x98,
	0x1c, 0x8a, 0xf8, 0x74, 0x87, 0x29, 0xe6, 0xdc, 0x82, 0xf6, 0x54, 0x84, 0x7e, 0x24, 0xa4, 0xea,
	0x55, 0xee, 0xd5, 0x3e, 0x58, 0xf1, 0x5a, 0x53, 0x11, 0xee, 0x09, 0xa9, 0x50, 0x3e, 0x7e, 0xc6,
	0x63, 0xe5, 0x2b, 0x31, 0xe1, 0xb4, 0x64, 0xdd, 0xb3, 0x88, 0x32, 0x12, 0x13, 0xee, 0xfe, 0x61,
	0x05, 0x9c, 0x7e, 0xa0, 0xc4, 0x19, 0x3f, 0x92, 0x3c, 0x2b, 0x16, 0xfc, 0x3e, 0xb4, 0x71, 0x5b,
	0xa6, 0x92, 0x8c, 0x24, 0x5b, 0x7d, 0x78, 0x6b, 0x6b, 0xc1, 0x4c, 0xb6, 0xf6, 0x0d, 0x83, 0x57,
	0xb0, 0xce, 0xc9, 0x51, 0xfd, 0x2a, 0x39, 0x6a, 0x8b, 0x72, 0x5c, 0x85, 0xb5, 0x4f, 0xb9, 0xda,
	0x9f, 0x2a, 0x9e, 0xa1, 0x00, 0x1e, 0xff, 0xd2, 0xdd, 0x5c, 0x20, 0xc9, 0xd4, 0xb9, 0x09, 0xad,
	0xf9, 0x63, 0x36, 0xf5, 0xea, 0xee, 0x63, 0x68, 0x0d, 0x0f, 0xfc, 0x47, 0x4c, 0x72, 0xe7, 0x43,
	0xa8, 0xb1, 0x34, 0x35, 0x52, 0xdf, 0x9c, 0x93, 0xda, 0x18, 0x71, 0x3f, 0x4d, 0x3d, 0xe4, 0xc9,
	0x55, 0x5f, 0x2d, 0x54, 0xef, 0xfe, 0x1c, 0x3a, 0xc3, 0x03, 0x1f, 0x55, 0x41, 0x6b, 0xbd, 0x0b,
	0x2b, 0x19, 0x1f, 0x0b, 0xa9, 0x78, 0xa6, 0xe5, 0xd6, 0xb7, 0xd4, 0xcd, 0x89, 0x28, 0x3a, 0xae,
	0x22, 0xf9, 0x8b, 0x7c, 0x15, 0xc9, 0x5f, 0x38, 0x1b, 0xd0, 0x3e, 0x16, 0x99, 0x3a, 0x09, 0xd9,
	0x39, 0x9d, 0x74, 0xc5, 0x2b, 0xfa, 0xce, 0x6d, 0xb0, 0xd8, 0x98, 0xfb, 0xe3, 0x2c, 0x99, 0xa6,
	0xbd, 0xba, 0x1e, 0x64, 0x63, 0xfe, 0x29, 0xf6, 0xdd, 0xff, 0xad, 0x80, 0x6d, 0xf6, 0x1f, 0xb1,
	0xf1, 0xf6, 0x09, 0x8b, 0xc7, 0xdc, 0xf9, 0x08, 0xea, 0xc7, 0x4c, 0xea, 0xbd, 0x3b, 0x0f, 0x7b,
	0x17, 0xee, 0xc1, 0x1c, 0xdc, 0x23, 0xae, 0x8b, 0x67, 0x42, 0x73, 0x2a, 0xe9, 0x9c, 0xda, 0xce,
	0x27, 0x50, 0x47, 0xb7, 0x24, 0x01, 0x3a, 0x0f, 0xef, 0xea, 0x35, 0x8d, 0xdf, 0x6d, 0x29, 0xb5,
	0x45, 0x3e, 0x6b, 0xd4, 0x20, 0x02, 0x8f, 0x98, 0xf1, 0x58, 0xf9, 0xc1, 0x7b, 0x8d, 0x7b, 0x95,
	0x0f, 0xda, 0x5e, 0xd1, 0x77, 0x1e, 0x43, 0x17, 0x79, 0x3e, 0x65, 0x13, 0x3e, 0x62, 0x63, 0xd9,
	0x6b, 0xd2, 0xc2, 0xee, 0xcb, 0x17, 0xce, 0x39, 0xbd, 0xb9, 0x79, 0xee, 0xef, 0xd7, 0xa0, 0x6b,
	0x34, 0x40, 0xb6, 0xfe, 0x46, 0x4e, 0xff, 0xf1, 0xdc, 0xe9, 0xdf, 0x5a, 0xb6, 0x66, 0x6e, 0x02,
	0xe6, 0xe8, 0xb7, 0xa0, 0x2d, 0xa4, 0x4f, 0x61, 0xc4, 0x1c, 0xbd, 0x25, 0xa4, 0x16, 0xf0, 0x37,
	0x01, 0x90, 0xc5, 0x3f, 0x46, 0x4d, 0x99, 0x73, 0xbf, 0x52, 0xa1, 0xd6, 0x34, 0x6f, 0x3a, 0x4f,
	0xa0, 0x9b, 0xc4, 0x91, 0x88, 0xb9, 0x2f, 0x15, 0x53, 0xbc, 0xd7, 0xa2, 0x15, 0xde, 0x7b, 0xf9,
	0x0a, 0xfb, 0xc4, 0x7d, 0x88, 0xcc, 0x5e, 0x27, 0x99, 0x75, 0x2e, 0xdc, 0x41, 0xfb, 0x92, 0x77,
	0xf0, 0x97, 0x15, 0x68, 0x0c, 0x0f, 0xfc, 0xc1, 0xf0, 0x1b, 0x2b, 0x7f, 0x3e, 0x38, 0xd6, 0x16,
	0x82, 0x63, 0x71, 0x37, 0xf5, 0xd2, 0xdd, 0xbc, 0x0f, 0x6b, 0x13, 0x39, 0xf6, 0x65, 0x32, 0xcd,
	0x02, 0xee, 0xab, 0xf3, 0x94, 0xf7, 0xae, 0xea, 0x20, 0x3e, 0x91, 0xe3, 0x43, 0xa2, 0x8e, 0xce,
	0x53, 0xee, 0xfe, 0x59, 0x95, 0x5c, 0x75, 0x28, 0x82, 0x5d, 0x0c, 0x22, 0xdf, 0x86, 0xa8, 0xf9,
	0x63, 0x33, 0x13, 0xd5, 0x50, 0x06, 0xa1, 0xf3, 0x36, 0x74, 0x9e, 0x65, 0xc9, 0xc4, 0x9f, 0x88,
	0x00, 0xc7, 0xb5, 0xe3, 0x5a, 0x48, 0x1a, 0x8a, 0x60, 0x10, 0x3a, 0x1b, 0x60, 0xa9, 0x24, 0x1f,
	0x6d, 0xd0, 0x68, 0x4b, 0x25, 0xc5, 0x98, 0x90, 0xfe, 0x34, 0xc5, 0x61, 0x32, 0x10, 0xb2, 0x9e,
	0xa3, 0x74, 0x28, 0x82, 0x42, 0x05, 0xad, 0x92, 0x0a, 0x7e, 0x54, 0x84, 0x4a, 0x3c, 0x7d, 0x9b,
	0x02, 0xd9, 0x9d, 0x8b, 0x07, 0x32, 0x27, 0x47, 0x6d, 0xe4, 0x91, 0x14, 0x15, 0xf3, 0x7b, 0x3a,
	0x86, 0x1c, 0x44, 0xec, 0x9c, 0x67, 0x87, 0x5c, 0x29, 0x11, 0x8f, 0xbf, 0xb1, 0x76, 0x7a, 0xd0,
	0x92, 0x7a, 0x29, 0xa3, 0x9a, 0xbc, 0xbb, 0xec, 0x0e, 0xdd, 0x63, 0xf2, 0xe1, 0x47, 0x11, 0x0b,
	0x4e, 0xd1, 0xcc, 0xde, 0x84, 0x0f, 0xbb, 0xff, 0xa8, 0x8f, 0x69, 0x1e, 0xd6, 0xc7, 0x22, 0x52,
	0xaf, 0xbd, 0xd1, 0x47, 0x50, 0x17, 0x8a, 0x4f, 0x68, 0xa7, 0xd5, 0x05, 0x6e, 0xf3, 0x54, 0x0c,
	0x14, 0x9f, 0x78, 0xc4, 0x85, 0xef, 0xca, 0x29, 0xd7, 0xf1, 0xfc, 0x25, 0xef, 0xca, 0x4f, 0xf8,
	0xb9, 0x87, 0x3c, 0xf9, 0x09, 0xea, 0xb3, 0x13, 0x94, 0x1e, 0xae, 0xc6, 0xdc, 0xc3, 0xf5, 0xb7,
	0x2d, 0xb8, 0x36, 0x3c, 0xf0, 0x47, 0x49, 0x2a, 0x82, 0x6d, 0x6d, 0x6d, 0x97, 0xb1, 0xe7, 0x79,
	0xeb, 0xad, 0x2e, 0x5a, 0xef, 0x75, 0x68, 0x2a, 0x76, 0x3c, 0x33, 0xec, 0x86, 0x62, 0xc7, 0x03,
	0xba, 0xd5, 0x20, 0xe3, 0xf4, 0xc8, 0x6b, 0x59, 0xf3, 0xae, 0xd3, 0x87, 0x95, 0xe3, 0x28, 0x09,
	0x4e, 0xfd, 0x24, 0x45, 0x78, 0x24, 0x49, 0xea, 0x65, 0xa1, 0xf2, 0x11, 0x72, 0xed, 0x13, 0x93,
	0xd7, 0x3d, 0x9e, 0x75, 0x24, 0x8a, 0x24, 0xa4, 0x9f, 0x66, 0xe2, 0x0c, 0xa3, 0x9a, 0x36, 0x7b,
	0x4b, 0xc8, 0x03, 0x4d, 0x70, 0x76, 0xa0, 0xc9, 0x02, 0xc2, 0x68, 0x2d, 0xd2, 0xe8, 0x47, 0xcb,
	0x4e, 0x78, 0x41, 0x2d, 0x5b, 0xfd, 0xed, 0xd1, 0x60, 0xff, 0xa9, 0x67, 0xe6, 0xa2, 0x65, 0xc4,
	0x6c, 0xa2, 0x9d, 0xc4, 0xf2, 0xa8, 0x8d, 0x61, 0x30, 0x14, 0x32, 0x8d, 0xd8, 0xb9, 0x76, 0x20,
	0xeb, 0x5e, 0xed, 0x83, 0xd5, 0x87, 0xef, 0x5e, 0x58, 0xdf, 0xac, 0xbb, 0xa3, 0x79, 0xc9, 0x8d,
	0x3a, 0xe1, 0xac, 0x83, 0x07, 0x78, 0xce, 0x62, 0xe5, 0x3f, 0xcb, 0xb8, 0x3c, 0xe9, 0x81, 0x3e,
	0x00, 0x52, 0x1e, 0x23, 0xc1, 0xf9, 0x02, 0xba, 0xb9, 0xca, 0x69, 0x9b, 0x0e, 0x69, 0xe8, 0x07,
	0x5f, 0xef, 0x18, 0xa6, 0x83, 0xfb, 0xec, 0xc6, 0x2a, 0x3b, 0xf7, 0x3a, 0xc1, 0x8c, 0x92, 0x23,
	0x8a, 0xee, 0x0c, 0x51, 0x68, 0x65, 0x06, 0x04, 0x08, 0xc2, 0xde, 0x4a, 0xae, 0x4c, 0x8d, 0x10,
	0x42, 0xe7, 0x1d, 0xe8, 0x66, 0x3c, 0xe2, 0x4c, 0x72, 0x0d, 0x53, 0x56, 0xc9, 0x51, 0x3a, 0x86,
	0x46, 0x28, 0xe5, 0x0e, 0x40, 0xce, 0x22, 0xd2, 0xde, 0x1a, 0xe9, 0xcb, 0x32, 0x94, 0x41, 0xea,
	0xb8, 0xb0, 0x22, 0x4f, 0x92, 0xe7, 0xfe, 0x98, 0x27, 0xbe, 0x88, 0x9f, 0x25, 0x3d, 0x9b, 0xf6,
	0xe8, 0x20, 0xf1, 0x53, 0x9e, 0x0c, 0xe2, 0x67, 0x89, 0xf3, 0x3e, 0xac, 0x0a, 0x39, 0x14, 0x71,
	0x92, 0x09, 0x75, 0x8e, 0xaf, 0x05, 0x45, 0xe6, 0xb6, 0xb7, 0x40, 0x75, 0x3e, 0x86, 0xf5, 0x94,
	0x65, 0x3c, 0x56, 0x39, 0x75, 0x84, 0xd6, 0xd6, 0x73, 0xe8, 0x38, 0xcb, 0x86, 0x9c, 0xb7, 0x01,
	0x02, 0xa6, 0xf8, 0x38, 0xc9, 0xce, 0x07, 0x61, 0x6f, 0x9d, 0x18, 0x4b, 0x94, 0x8d, 0x9f, 0x82,
	0xbd, 0xa8, 0x31, 0x54, 0x12, 0xfa, 0xa3, 0xc1, 0xcd, 0xe8, 0x76, 0xdf, 0x83, 0xc6, 0x19, 0x8b,
	0xa6, 0xdc, 0x38, 0xf4, 0xdb, 0x2f, 0xbb, 0x71, 0x5c, 0x63, 0x10, 0x7a, 0x9a, 0xf9, 0x87, 0xd5,
	0x5f, 0xab, 0xb8, 0x5b, 0xd0, 0xd4, 0x86, 0xe5, 0x74, 0xa0, 0x35, 0x78, 0xfa, 0x59, 0x7f, 0x6f,
	0xb0, 0x63, 0x5f, 0x71, 0x00, 0x9a, 0xdb, 0xde, 0x6e, 0x7f, 0xb4, 0x6b, 0x57, 0x70, 0x60, 0x67,
	0x70, 0x38, 0x1c, 0x1c, 0x1e, 0xda, 0x55, 0xf7, 0x8f, 0x2a, 0xd0, 0x29, 0x59, 0x3e, 0xc2, 0x03,
	0xed, 0x2e, 0x05, 0x90, 0x6f, 0x51, 0x7f, 0x40, 0x9e, 0xcf, 0x23, 0x3e, 0x99, 0xb9, 0x65, 0x13,
	0xbb, 0x83, 0xd0, 0xb9, 0x0b, 0x1d, 0x3d, 0x47, 0x09, 0x15, 0xe9, 0xd8, 0x66, 0x79, 0x40, 0xa4,
	0x11, 0x52, 0x10, 0x7c, 0x22, 0xab, 0xc6, 0xcc, 0xc8, 0x52, 0x27, 0x96, 0xae, 0x21, 0x12, 0x93,
	0xbb, 0x0b, 0x9d, 0xd2, 0xa9, 0x28, 0x0e, 0x44, 0x4c, 0x4a, 0x7f, 0x92, 0x84, 0x39, 0x5a, 0xb5,
	0x88, 0x32, 0x4c, 0x42, 0x8e, 0xc2, 0xa0, 0xad, 0x96, 0x84, 0x51, 0xa4, 0x0b, 0xf7, 0x3f, 0x2a,
	0x70, 0x63, 0xc1, 0x50, 0x7f, 0x7b, 0xca, 0x22, 0xa1, 0xce, 0xbf, 0x61, 0x20, 0xaa, 0xcd, 0x07,
	0xa2, 0x23, 0x68, 0x7d, 0xa9, 0xd7, 0x35, 0x81, 0xf4, 0x37, 0x5e, 0xe5, 0x2f, 0x46, 0x8c, 0xad,
	0x25, 0x34, 0x2f, 0x5f, 0xcb, 0xfd, 0x2e, 0xac, 0x2f, 0x13, 0x1d, 0xef, 0x6c, 0xf7, 0x71, 0xff,
	0x68, 0x6f, 0x64, 0x5f, 0x71, 0x5a, 0x50, 0xdb, 0xdb, 0xff, 0xdc, 0xae, 0xb8, 0xff, 0x56, 0x81,
	0xb5, 0xe1, 0x81, 0xff, 0x0d, 0xa2, 0xed, 0x6b, 0xa3, 0x87, 0x1b, 0x45, 0xb0, 0xd3, 0x71, 0xd6,
	0xf4, 0xdc, 0x2f, 0xa0, 0xd9, 0xcf, 0x03, 0xd9, 0xaa, 0x6e, 0xf9, 0x83, 0xf8, 0x8c, 0x45, 0x22,
	0xb4, 0xaf, 0x38, 0x36, 0x74, 0x0d, 0x6d, 0x37, 0x56, 0x3c, 0xb3, 0x2b, 0x25, 0xca, 0x1e, 0x67,
	0x67, 0xdc, 0xae, 0x3a, 0x37, 0x61, 0xdd, 0x50, 0xfa, 0xe1, 0x44, 0xc4, 0xfe, 0x51, 0x1a, 0x32,
	0xc5, 0xed, 0x86, 0xfb, 0x25, 0xac, 0x0c, 0x0f, 0x7c, 0x0f, 0x13, 0xf3, 0xcb, 0x1c, 0xf1, 0x21,
	0x34, 0x28, 0xa9, 0xa7, 0x43, 0x16, 0x81, 0xbf, 0x04, 0x22, 0x75, 0xca, 0x4f, 0xeb, 0x7b, 0x9a,
	0xd5, 0xfd, 0x45, 0x05, 0xd6, 0xf3, 0x3d, 0x77, 0x18, 0xa2, 0x83, 0x6f, 0x4d, 0xb9, 0x5f, 0x85,
	0x22, 0xdf, 0x02, 0x0b, 0x83, 0x9e, 0x54, 0x6c, 0x92, 0x67, 0x54, 0x33, 0x82, 0xfb, 0xa7, 0xda,
	0xb2, 0x35, 0xf4, 0x0e, 0x4e, 0x31, 0xef, 0x8a, 0xc3, 0x6f, 0x47, 0xae, 0xb9, 0x8d, 0x6b, 0x0b,
	0x1b, 0xbf, 0xf4, 0xce, 0xc7, 0x84, 0x5b, 0x1e, 0x89, 0x38, 0xdc, 0x4e, 0x07, 0xdf, 0x92, 0x24,
	0xeb, 0xd0, 0x08, 0xd2, 0xdc, 0xf2, 0x2c, 0xaf, 0x1e, 0xa4, 0x83, 0x10, 0x81, 0x20, 0x42, 0x8b,
	0xc7, 0x2c, 0x50, 0x49, 0xa6, 0x5f, 0x8a, 0xcb, 0xec, 0x86, 0xe0, 0x0b, 0xdf, 0xb7, 0xaa, 0x5e,
	0x1a, 0xdb, 0xce, 0x2a, 0x54, 0x8b, 0xcd, 0xaa, 0x82, 0xec, 0xfb, 0x19, 0x6d, 0x43, 0x67, 0xad,
	0x7a, 0xa6, 0xe7, 0xfe, 0x9c, 0x0c, 0x62, 0x8f, 0x9f, 0xf1, 0xe8, 0xcd, 0x0a, 0x70, 0x0d, 0x1a,
	0x11, 0xee, 0x62, 0x62, 0xa4, 0xee, 0xb8, 0xff, 0xd2, 0x24, 0x20, 0x7a, 0x90, 0x48, 0x75, 0x99,
	0x8d, 0x0f, 0x61, 0x2d, 0x4d, 0xa4, 0xf2, 0x4b, 0x60, 0x5c, 0xbf, 0x2c, 0xdf, 0x5d, 0x36, 0xb1,
	0xd8, 0x65, 0xab, 0x68, 0x11, 0xa6, 0x58, 0x49, 0xcb, 0x5d, 0x0c, 0xc1, 0xb4, 0x68, 0x21, 0x7e,
	0x13, 0xbb, 0x03, 0xb2, 0x72, 0x36, 0x55, 0x27, 0x49, 0xe6, 0xcf, 0xb0, 0xa3, 0xa5, 0x29, 0x68,
	0xe5, 0xb7, 0xc1, 0x22, 0x70, 0xc6, 0x7d, 0xa6, 0x28, 0xc1, 0xa8, 0x7b, 0x6d, 0x4d, 0xe8, 0x2b,
	0x02, 0x72, 0x49, 0xac, 0x78, 0xac, 0x08, 0x68, 0x59, 0x5e, 0xde, 0x75, 0xbe, 0x83, 0x6f, 0xb6,
	0x2f, 0xcf, 0xa5, 0xe2, 0x13, 0x1f, 0x77, 0x22, 0xb8, 0xd5, 0xf6, 0xba, 0x42, 0x1e, 0x12, 0x11,
	0x85, 0x25, 0x0f, 0xc3, 0xf8, 0xa9, 0x11, 0x6a, 0xfb, 0x5e, 0x0d, 0xc1, 0x01, 0x51, 0xa8, 0x76,
	0xe3, 0x40, 0x3d, 0x4d, 0x92, 0xa8, 0x67, 0xe9, 0x82, 0x14, 0xb6, 0x9d, 0x6d, 0xb0, 0xe8, 0x1c,
	0xa4, 0x16, 0x20, 0xb5, 0xbc, 0xff, 0x6a, 0xb5, 0x90, 0x46, 0xda, 0xa9, 0x69, 0x2d, 0x60, 0xc4,
	0xce, 0x22, 0x46, 0xc4, 0x5b, 0x4e, 0x09, 0x06, 0xe1, 0x2d, 0xa7, 0x26, 0x0b, 0x3f, 0xc6, 0xbc,
	0xc2, 0x60, 0xa0, 0x96, 0x90, 0x94, 0x66, 0xe0, 0x6b, 0x8a, 0x28, 0x1c, 0x2f, 0x0a, 0xd5, 0xb7,
	0x6a, 0x20, 0x84, 0x26, 0xa1, 0xfe, 0xde, 0x35, 0x00, 0x27, 0x4a, 0x02, 0x5d, 0x1a, 0x5c, 0xd3,
	0x7a, 0x40, 0xe2, 0x9e, 0xa1, 0xb9, 0x7f, 0x5f, 0x81, 0x95, 0xb9, 0xdb, 0xc3, 0x88, 0x7b, 0x14,
	0x9f, 0xc6, 0xc9, 0xf3, 0x98, 0x68, 0xf6, 0x15, 0x7c, 0x54, 0x0e, 0xa6, 0xc7, 0x91, 0x90, 0x27,
	0x76, 0x05, 0x11, 0xc2, 0x0e, 0x8f, 0xb8, 0xc2, 0x50, 0x7c, 0x15, 0x56, 0x48, 0xf0, 0xe0, 0x5c,
	0xdb, 0xba, 0x5d, 0x73, 0xba, 0xd0, 0xee, 0x2b, 0x25, 0xd4, 0x34, 0xe4, 0x76, 0x1d, 0x67, 0x6e,
	0x6b, 0x81, 0xec, 0x06, 0x0d, 0xa5, 0x29, 0x81, 0x08, 0xbb, 0xe9, 0xac, 0x02, 0x1c, 0x24, 0x89,
	0x71, 0x12, 0xbb, 0x85, 0xfd, 0xfd, 0xe7, 0x31, 0xcf, 0x3c, 0x9e, 0x46, 0xe7, 0x76, 0x1b, 0x9f,
	0x87, 0x47, 0x2c, 0x3c, 0x0c, 0x92, 0x8c, 0x1b, 0x1e, 0xcb, 0xd0, 0x4a, 0xce, 0x65, 0x83, 0x3b,
	0x86, 0x76, 0xae, 0x66, 0x67, 0x1d, 0xd6, 0xcc, 0x5b, 0x92, 0x93, 0xec, 0x2b, 0xb8, 0xed, 0x88,
	0xbf, 0x50, 0x48, 0xb1, 0x2b, 0xce, 0x0a, 0x58, 0x83, 0x09, 0x1b, 0x73, 0xea, 0x56, 0xb1, 0xfb,
	0x99, 0x08, 0x79, 0x42, 0xdd, 0x1a, 0xc9, 0x3b, 0x91, 0xd4, 0xa9, 0xe3, 0x58, 0x7f, 0x1a, 0x0a,
	0x3d, 0xd6, 0x70, 0xff, 0xa2, 0x42, 0xae, 0xe5, 0x09, 0x79, 0x7a, 0xfe, 0x79, 0x92, 0x85, 0xaf,
	0xef, 0xd3, 0x8b, 0x25, 0x4e, 0xf4, 0xe1, 0xe7, 0x49, 0x16, 0xca, 0x5e, 0x8d, 0xec, 0x4f, 0x77,
	0xdc, 0xff, 0x0f, 0xf5, 0x7d, 0x1c, 0xb5, 0xa1, 0x8b, 0xbf, 0xa5, 0xe7, 0xb1, 0x0b, 0x6d, 0xa2,
	0xf4, 0xc3, 0xd0, 0xae, 0x38, 0x6b, 0xd0, 0xa1, 0x9e, 0xc7, 0x27, 0x09, 0xbe, 0x8c, 0xee, 0x3f,
	0x55, 0xa8, 0x40, 0xb0, 0x7d, 0xc2, 0xd4, 0x36, 0x7b, 0x6d, 0x01, 0xef, 0x43, 0x35, 0x49, 0x8d,
	0xbb, 0xdf, 0x5d, 0xc6, 0x9b, 0xaf, 0xbb, 0xb5, 0x9f, 0x7a, 0xd5, 0x24, 0x75, 0x7e, 0x00, 0xf5,
	0x80, 0x65, 0xda, 0xa9, 0x97, 0x15, 0x5d, 0x8a, 0x4a, 0x77, 0x3e, 0xd1, 0x23, 0x7e, 0xf7, 0x43,
	0xa8, 0xee, 0xa7, 0x04, 0x3b, 0x8b, 0x83, 0xcd, 0x59, 0x98, 0x05, 0x8d, 0xed, 0x28, 0x91, 0x78,
	0xa2, 0x7f, 0xd0, 0x29, 0x6f, 0xbe, 0xc0, 0x13, 0xa1, 0xf6, 0x5f, 0xb7, 0x3e, 0x76, 0x03, 0x13,
	0x41, 0x7c, 0x38, 0x0b, 0xfc, 0x47, 0xbd, 0x57, 0x3c, 0x65, 0x6f, 0x43, 0xe7, 0x44, 0x28, 0x3f,
	0x89, 0xb5, 0xb7, 0x9b, 0xd8, 0x74, 0x82, 0xfb, 0x53, 0xc9, 0xe1, 0x03, 0xa8, 0x8d, 0xce, 0x17,
	0x0e, 0x61, 0x41, 0x83, 0x04, 0xd4, 0x47, 0xd0, 0x76, 0x5c, 0x75, 0xff, 0xbb, 0x0e, 0xab, 0x08,
	0xf0, 0x46, 0xfd, 0xa9, 0x3a, 0xb9, 0x4c, 0x4c, 0xd6, 0x21, 0xa1, 0x5a, 0x84, 0x84, 0x55, 0xa8,
	0x2a, 0x69, 0x0a, 0x03, 0x55, 0x25, 0xd1, 0x88, 0xd2, 0x93, 0x24, 0xce, 0xc1, 0xb2, 0xee, 0xa0,
	0xb9, 0x89, 0x09, 0x17, 0x14, 0x37, 0x2d, 0x8f, 0xda, 0x44, 0x0b, 0x9f, 0x31, 0x13, 0x30, 0xa9,
	0x4d, 0x51, 0x24, 0x12, 0x45, 0xb4, 0x6f, 0x99, 0x28, 0x42, 0x24, 0xf2, 0xa9, 0xf7, 0x60, 0xd5,
	0x30, 0x9c, 0xf1, 0x4c, 0x62, 0x18, 0x69, 0xeb, 0xe2, 0x94, 0xa6, 0x7e, 0xa6, 0x89, 0x98, 0x4d,
	0x85, 0xfc, 0x4c, 0x04, 0x88, 0xb4, 0xfd, 0x13, 0xfe, 0x82, 0x22, 0xa7, 0xe5, 0x75, 0x34, 0x71,
	0x10, 0x3e, 0xe1, 0x2f, 0xa8, 0x24, 0x20, 0x7d, 0x16, 0x2b, 0x61, 0x72, 0xcb, 0xa6, 0x90, 0xfd,
	0x58, 0x09, 0x8c, 0xf4, 0x13, 0x96, 0x9d, 0x72, 0x7a, 0x23, 0x3a, 0xba, 0x42, 0xac, 0x09, 0x3a,
	0x93, 0x0f, 0x26, 0x21, 0x8e, 0xe8, 0xec, 0xb0, 0x11, 0x4c, 0xc2, 0x41, 0x88, 0x1b, 0xe2, 0x62,
	0x53, 0x95, 0x98, 0x22, 0xa5, 0x0e, 0x8f, 0x1d, 0x21, 0xfb, 0x53, 0x95, 0xe8, 0x42, 0xe5, 0x5d,
	0xe8, 0xa4, 0xa7, 0x63, 0xdf, 0xa0, 0x52, 0x0a, 0x91, 0x96, 0x07, 0xe9, 0xe9, 0xd8, 0x20, 0x61,
	0xe7, 0x1e, 0x74, 0xe5, 0xc4, 0x2f, 0x04, 0x37, 0x49, 0x22, 0xc8, 0xc9, 0x8e, 0x11, 0x1b, 0xf3,
	0x4c, 0x33, 0x8c, 0xf9, 0x45, 0x44, 0x49, 0x62, 0x71, 0x2c, 0xcc, 0x30, 0x22, 0xb4, 0x15, 0x19,
	0x9e, 0xea, 0x3f, 0x6c, 0x44, 0x48, 0x19, 0x62, 0xcd, 0xb3, 0x64, 0x78, 0x8a, 0xa9, 0xa1, 0x96,
	0x34, 0x62, 0x52, 0x69, 0x31, 0x7d, 0x25, 0x29, 0x2d, 0xac, 0x7b, 0x1d, 0x24, 0x92, 0x9c, 0x23,
	0x89, 0x92, 0xe6, 0x52, 0x60, 0x2a, 0xba, 0xae, 0xe5, 0x30, 0xca, 0xc3, 0x4c, 0x14, 0xc3, 0x07,
	0x13, 0x61, 0xef, 0x9a, 0xbe, 0x3b, 0x6c, 0xd3, 0xfb, 0x19, 0x87, 0x59, 0x22, 0x48, 0x3b, 0xd7,
	0x75, 0x82, 0x6b, 0x28, 0x83, 0xd0, 0xfd, 0x65, 0x95, 0x9c, 0xa7, 0xaf, 0x54, 0x26, 0x8e, 0xa7,
	0x18, 0xed, 0x47, 0xa3, 0x4b, 0xd8, 0x5e, 0x58, 0xd8, 0x5e, 0xe8, 0x6c, 0xc2, 0xd5, 0x09, 0x53,
	0x3c, 0x13, 0x2c, 0xf2, 0x15, 0x1b, 0xfb, 0x54, 0x89, 0xd0, 0x8f, 0xfa, 0x5a, 0x3e, 0x30, 0x62,
	0xe3, 0xa7, 0x8c, 0x6a, 0x7a, 0xb7, 0x85, 0xf4, 0xf9, 0x0b, 0x21, 0x95, 0xf4, 0x2f, 0xce, 0xaa,
	0xd3, 0x75, 0xdd, 0x14, 0x72, 0x97, 0x38, 0x86, 0x0b, 0xb3, 0x31, 0x57, 0x9c, 0x4a, 0x11, 0x73,
	0x29, 0xf3, 0xfa, 0x62, 0xcd, 0x83, 0x9c, 0x34, 0x08, 0x9d, 0x07, 0x70, 0x7d, 0xb6, 0x7c, 0x99,
	0x55, 0xd7, 0x5d, 0x9c, 0x7c, 0xe1, 0x47, 0xb3, 0x29, 0x05, 0x66, 0x6c, 0x95, 0x30, 0xe3, 0x2f,
	0x2b, 0xe0, 0x0c, 0x0f, 0xfc, 0xfd, 0x67, 0xcf, 0x22, 0x11, 0xd3, 0xb3, 0x30, 0x50, 0x7c, 0x52,
	0x06, 0x2d, 0x95, 0x39, 0xd0, 0x72, 0x0d, 0x1a, 0x12, 0x1f, 0x25, 0xd2, 0x4a, 0xd5, 0xd3, 0x1d,
	0x84, 0x23, 0x19, 0x8b, 0x4f, 0x4b, 0xd5, 0x42, 0xd3, 0xc5, 0x44, 0x5f, 0xaa, 0x0c, 0x13, 0x7b,
	0xc1, 0xa5, 0xf1, 0xd1, 0x12, 0x85, 0xe0, 0x0a, 0x22, 0x95, 0x04, 0x1f, 0x3f, 0x7a, 0xc9, 0xb5,
	0xcb, 0x76, 0x91, 0x4a, 0x2f, 0xe2, 0x91, 0x08, 0xdd, 0xff, 0xd4, 0x98, 0x1e, 0xc5, 0x33, 0x92,
	0x1e, 0x89, 0xf0, 0x21, 0x49, 0xfa, 0x7a, 0x37, 0xfa, 0x04, 0xe0, 0x79, 0x26, 0x14, 0x2f, 0x83,
	0xbb, 0x0f, 0x2f, 0xfe, 0xd1, 0x35, 0xbf, 0xc7, 0xe7, 0x38, 0x43, 0x57, 0x5d, 0x9f, 0xe7, 0x4d,
	0xe7, 0xc7, 0x60, 0x91, 0xe0, 0x04, 0xa0, 0x6a, 0x54, 0x0a, 0x7a, 0x77, 0xd9, 0xe6, 0x0b, 0x9a,
	0xf5, 0xda, 0x38, 0x8b, 0x40, 0xd6, 0x5d, 0xe8, 0x4c, 0x29, 0x79, 0xf3, 0x8b, 0x7a, 0xea, 0x8a,
	0x07, 0x9a, 0x44, 0x7f, 0x91, 0xfd, 0x5d, 0x8d, 0xf0, 0x3c, 0x4e, 0x3d, 0x54, 0x4c, 0x09, 0xa9,
	0x02, 0x49, 0x67, 0xbe, 0x0e, 0x4d, 0x99, 0x05, 0xbe, 0x48, 0xcd, 0xe5, 0x34, 0x64, 0x16, 0x0c,
	0x52, 0xe7, 0x43, 0xb0, 0x25, 0xcf, 0xce, 0xcc, 0x5f, 0x57, 0x3a, 0xb4, 0xe3, 0x11, 0x1b, 0xde,
	0x9a, 0xa6, 0x8f, 0x8a, 0x00, 0x7f, 0x0b, 0xda, 0xc7, 0xe2, 0x67, 0x5a, 0x0b, 0xda, 0x80, 0x5b,
	0xc7, 0xe2, 0x67, 0x79, 0x2d, 0x2a, 0xc7, 0xa3, 0x0d, 0x9d, 0x6c, 0xdc, 0x02, 0x02, 0x70, 0xa5,
	0xdb, 0x21, 0xe3, 0x38, 0xd2, 0x65, 0xce, 0xdc, 0x4e, 0x9a, 0x73, 0x76, 0xf2, 0x0e, 0x74, 0x53,
	0xfd, 0xa4, 0xf9, 0x45, 0xb9, 0xdb, 0xf2, 0x3a, 0x86, 0x46, 0x05, 0xaa, 0xdb, 0x65, 0x40, 0xa9,
	0xeb, 0x79, 0x33, 0xa0, 0x78, 0x0b, 0xda, 0x1a, 0xa0, 0x8a, 0xd0, 0xc4, 0xd2, 0x16, 0xf5, 0x07,
	0x54, 0x1c, 0x0e, 0x84, 0x3a, 0xa7, 0x20, 0x8a, 0x66, 0x8c, 0x89, 0xff, 0x0d, 0x68, 0xea, 0x3f,
	0x10, 0x28, 0x7e, 0x5a, 0x9e, 0xe9, 0x69, 0x9c, 0xab, 0x58, 0xe4, 0xa3, 0x52, 0x29, 0x82, 0x36,
	0x10, 0xe7, 0x2a, 0x16, 0xed, 0x20, 0xde, 0xfc, 0x21, 0x58, 0x52, 0x31, 0x23, 0xc2, 0xca, 0x4b,
	0xea, 0xee, 0xb9, 0xfe, 0x35, 0x94, 0x95, 0xa6, 0x85, 0x4b, 0xd3, 0xdc, 0x20, 0x99, 0xc6, 0x8a,
	0x82, 0x6b, 0xc3, 0xa3, 0xd5, 0xb6, 0x91, 0xe0, 0xfe, 0x73, 0x8d, 0x52, 0x21, 0x13, 0x6a, 0x31,
	0x88, 0xbd, 0x81, 0x32, 0xaf, 0xb9, 0xab, 0xda, 0x2c, 0x31, 0x7c, 0x67, 0x56, 0xa4, 0x2c, 0xe2,
	0x8c, 0x55, 0x14, 0x1b, 0x29, 0xb6, 0xdc, 0x06, 0xeb, 0x58, 0xc4, 0xa1, 0x3e, 0xb4, 0xbe, 0xcf,
	0x36, 0x12, 0x72, 0xbd, 0x4f, 0x44, 0xa0, 0xab, 0x49, 0x26, 0xb3, 0x98, 0x88, 0x80, 0x6a, 0x49,
	0x54, 0x53, 0x1e, 0xcf, 0x02, 0x48, 0x43, 0xb1, 0xf1, 0x80, 0xac, 0xa3, 0x88, 0x6a, 0x6d, 0x73,
	0x53, 0x26, 0x8a, 0x7d, 0x04, 0x4e, 0x2e, 0x4c, 0x5e, 0xa0, 0x35, 0xd7, 0xb9, 0xe2, 0xd9, 0xc1,
	0x5c, 0x35, 0x56, 0xbf, 0x74, 0x2c, 0xa5, 0x00, 0xa5, 0x6f, 0xb6, 0xc1, 0xd2, 0x74, 0x30, 0x77,
	0xa2, 0x94, 0x65, 0xcc, 0x5c, 0x70, 0x7e, 0xa2, 0x03, 0x96, 0x31, 0x84, 0xfa, 0x39, 0xca, 0xd7,
	0xfe, 0xa8, 0x33, 0x88, 0x6e, 0x4e, 0xcc, 0xdd, 0xcd, 0xe4, 0x53, 0x64, 0x90, 0x2b, 0x3a, 0x14,
	0x69, 0x12, 0xd9, 0xe3, 0x82, 0x3f, 0x9a, 0xe7, 0xb2, 0xe4, 0x8f, 0x7f, 0x5d, 0x25, 0x7f, 0x34,
	0x57, 0x4a, 0x38, 0xfc, 0x0d, 0xdc, 0x69, 0x91, 0xd9, 0xd6, 0x4a, 0x99, 0x2d, 0x4e, 0xa2, 0xc6,
	0x0c, 0x90, 0x59, 0x9e, 0x45, 0x14, 0xba, 0xb6, 0xaf, 0xbc, 0xd3, 0x3b, 0x00, 0x3c, 0xa6, 0x7f,
	0xb4, 0x71, 0x54, 0xdf, 0xaa, 0x45, 0x14, 0x1a, 0x2e, 0x42, 0xba, 0xb9, 0x56, 0x1d, 0xd2, 0x17,
	0xd4, 0xd5, 0x7e, 0x95, 0xba, 0xac, 0x0b, 0xea, 0xfa, 0x45, 0x05, 0x6e, 0x95, 0xd4, 0x25, 0xce,
	0xe8, 0x5f, 0xcb, 0xa9, 0xfc, 0x95, 0xf8, 0x01, 0x06, 0x04, 0xda, 0x2d, 0x2f, 0xc6, 0xe8, 0x9e,
	0xfb, 0xe7, 0xfa, 0xbd, 0xeb, 0xc7, 0xc1, 0x49, 0x92, 0x3d, 0x66, 0xf1, 0xa5, 0xa4, 0xb9, 0x05,
	0xed, 0x67, 0x2c, 0x96, 0xa5, 0x0f, 0x40, 0x5a, 0xd8, 0x3f, 0xca, 0x41, 0x49, 0x90, 0x27, 0xf5,
	0x06, 0x58, 0x6b, 0x8a, 0x19, 0x2e, 0x15, 0x17, 0x0c, 0xae, 0x9e, 0xfd, 0x95, 0xf7, 0x27, 0x15,
	0xb8, 0x33, 0x53, 0xd9, 0x7e, 0xca, 0x63, 0x04, 0x51, 0x6f, 0x4c, 0x6d, 0x77, 0xa1, 0x43, 0xb0,
	0xcd, 0x68, 0x4a, 0x4b, 0x0b, 0xe3, 0x62, 0x4b, 0xf7, 0xaf, 0x2a, 0x70, 0x73, 0x26, 0x8f, 0xfe,
	0xbf, 0xf8, 0xf8, 0x52, 0x92, 0xfc, 0x2e, 0x5c, 0x5d, 0xfc, 0xb4, 0x47, 0x9a, 0x52, 0xe3, 0x83,
	0x65, 0xa9, 0xd3, 0xfc, 0x47, 0x40, 0x0b, 0x02, 0x78, 0x6b, 0xc1, 0x3c, 0xc1, 0xfd, 0x9b, 0x0a,
	0xf4, 0x66, 0x82, 0x6e, 0x27, 0x93, 0x94, 0xc5, 0x22, 0x89, 0x2f, 0x23, 0x29, 0x1a, 0x7e, 0xa1,
	0x33, 0x69, 0x2a, 0xda, 0x50, 0x28, 0x4d, 0x3a, 0xbb, 0xb0, 0x1a, 0xe4, 0x1b, 0xcc, 0x5e, 0xd0,
	0xa5, 0x7f, 0x3f, 0xe4, 0x6c, 0xba, 0x2e, 0x14, 0x94, 0xbb, 0xee, 0x1f, 0x54, 0xe0, 0x1d, 0x53,
	0xa7, 0xbc, 0x20, 0x36, 0xfa, 0xe6, 0xaf, 0xa2, 0x4e, 0xed, 0xfe, 0x8f, 0x7e, 0xa5, 0x0e, 0x45,
	0x3c, 0x9e, 0x2b, 0x8f, 0xcf, 0x4f, 0xab, 0x2c, 0xfd, 0x7b, 0x71, 0x3c, 0xb3, 0x29, 0xf3, 0x14,
	0x6c, 0x17, 0x4e, 0x57, 0x7b, 0x79, 0xd9, 0x6c, 0x71, 0xaf, 0x2d, 0x6d, 0x6b, 0xb9, 0x87, 0x3a,
	0x3b, 0xd0, 0x4a, 0xd2, 0x99, 0x7f, 0x7c, 0xdd, 0x55, 0xf6, 0x53, 0x52, 0x72, 0x33, 0xa1, 0x5f,
	0xe7, 0x60, 0xde, 0xb4, 0x1b, 0xb4, 0xd2, 0xfd, 0xaf, 0xb5, 0xd2, 0xcc, 0xe5, 0xe6, 0x7c, 0xe1,
	0xc7, 0xd0, 0xd4, 0x2d, 0xe7, 0x2a, 0xac, 0xe8, 0xd6, 0x5c, 0xe5, 0x5e, 0x93, 0x9e, 0x26, 0xd9,
	0x84, 0x45, 0x76, 0x65, 0xc6, 0xb4, 0x23, 0xe4, 0x44, 0x48, 0x69, 0x57, 0x5d, 0x0f, 0x9a, 0x5a,
	0x4a, 0x1c, 0xd4, 0xad, 0xb9, 0x15, 0x34, 0x69, 0x9b, 0x42, 0xac, 0xae, 0xfd, 0x6b, 0x8a, 0x29,
	0xf1, 0x57, 0x67, 0xd3, 0xf2, 0x35, 0x6b, 0xee, 0xaf, 0x03, 0xcc, 0xe4, 0x75, 0x6e, 0x80, 0x33,
	0xeb, 0x3d, 0x4d, 0x10, 0xb6, 0x64, 0xca, 0xbe, 0xe2, 0xac, 0xc3, 0xda, 0x8c, 0xae, 0x89, 0x95,
	0xcd, 0x0f, 0xc1, 0xa2, 0x6f, 0xad, 0x48, 0xa2, 0xb9, 0x54, 0x7e, 0x0d, 0x3a, 0xfa, 0x13, 0x31,
	0xb2, 0x4e, 0xbb, 0xb2, 0xf9, 0x5b, 0xba, 0xf2, 0x42, 0xff, 0x14, 0x5f, 0x03, 0x3b, 0x6f, 0x97,
	0x6a, 0x33, 0xab, 0x00, 0x64, 0xb5, 0xfe, 0x41, 0x92, 0x44, 0xba, 0xd0, 0xb4, 0xfb, 0x42, 0x28,
	0xdd, 0xad, 0x6e, 0x4e, 0xa0, 0x5b, 0xfe, 0x7c, 0x01, 0xd9, 0x87, 0x22, 0x98, 0x3b, 0x7d, 0x3e,
	0xfe, 0x24, 0x89, 0x42, 0xbb, 0x82, 0x22, 0xe7, 0x14, 0x4f, 0xff, 0x69, 0x69, 0x57, 0x1d, 0x07,
	0x56, 0x73, 0x62, 0x51, 0x72, 0x2b, 0xd1, 0x86, 0x49, 0x78, 0xc8, 0x95, 0x5d, 0xdf, 0xbc, 0x03,
	0xdd, 0xdc, 0xa7, 0x22, 0x26, 0xa5, 0x96, 0x26, 0xf5, 0xa9, 0x63, 0x5f, 0xd9, 0xfc, 0xaf, 0x0a,
	0xac, 0xcc, 0xfd, 0x35, 0xe8, 0x6c, 0xc0, 0x8d, 0x12, 0xc1, 0x47, 0xe6, 0x9f, 0x4c, 0x93, 0x48,
	0x70, 0xfb, 0xca, 0xd2, 0x31, 0x26, 0x4e, 0xb8, 0xb0, 0x2b, 0xce, 0x6d, 0xb8, 0xb9, 0x38, 0x36,
	0x14, 0x14, 0xb4, 0xed, 0xaa, 0xd3, 0x83, 0x6b, 0x8b, 0x83, 0xdb, 0x42, 0x9d, 0xdb, 0x35, 0xe7,
	0x2d, 0xe8, 0x2d, 0x8e, 0x1c, 0x64, 0xc9, 0x99, 0x88, 0x03, 0x6e, 0xd7, 0x9d, 0x7b, 0xf0, 0xd6,
	0xe2, 0xa8, 0xc7, 0x23, 0x42, 0x32, 0xf2, 0x44, 0xa4, 0x76, 0xc3, 0xb9, 0x05, 0xd7, 0x2f, 0x6c,
	0x3b, 0x95, 0x22, 0xb0, 0x9b, 0xce, 0xad, 0x8b, 0x9b, 0x3e, 0x4d, 0x62, 0x6e, 0xff, 0x7b, 0x6b,
	0xf3, 0x0b, 0x70, 0x2e, 0xfe, 0x05, 0x8e, 0x52, 0xee, 0x0c, 0x0e, 0x0f, 0xf6, 0xfa, 0x5f, 0xf8,
	0xfd, 0x91, 0x3f, 0xec, 0x0f, 0x9e, 0xfa, 0x07, 0xfd, 0x4f, 0x77, 0xf5, 0xc1, 0x4b, 0x23, 0x8f,
	0x07, 0x4f, 0x77, 0xfc, 0xc7, 0xde, 0x60, 0xf7, 0xe9, 0x8e, 0xae, 0x1c, 0x8e, 0x76, 0x87, 0x07,
	0xfb, 0x5e, 0xdf, 0xfb, 0xc2, 0xae, 0x6d, 0xfe, 0x71, 0x05, 0x7a, 0x2f, 0xcb, 0x9a, 0x9c, 0x77,
	0xe1, 0xae, 0x29, 0xa9, 0xbe, 0x8c, 0xc5, 0xbe, 0xe2, 0xdc, 0x84, 0x75, 0x33, 0xea, 0x91, 0x7b,
	0x7a, 0x2c, 0x3e, 0x7d, 0xf0, 0xb1, 0x5d, 0x41, 0x5d, 0xcd, 0x0d, 0x3c, 0x11, 0xe3, 0x13, 0xf3,
	0x7f, 0x9f, 0x5d, 0x45, 0x33, 0x99, 0x1b, 0x7d, 0xf0, 0xc0, 0xae, 0x6d, 0x06, 0xd0, 0x2d, 0x83,
	0x76, 0x64, 0xca, 0xdb, 0xbe, 0x96, 0xc4, 0xbe, 0xe2, 0x5c, 0x87, 0xab, 0x05, 0x71, 0xf7, 0x45,
	0x9a, 0xc8, 0x69, 0x86, 0x5e, 0x77, 0x0d, 0xec, 0x82, 0x9c, 0x17, 0x6f, 0xab, 0xb9, 0x37, 0x13,
	0x75, 0x4f, 0x9c, 0x72, 0xbb, 0xb6, 0xf9, 0x3e, 0xac, 0xcc, 0xc5, 0x77, 0x07, 0xa0, 0x69, 0xbc,
	0x9f, 0x8a, 0x61, 0x7b, 0xfb, 0x7b, 0x9f, 0x7b, 0x76, 0xe5, 0xe1, 0xbf, 0x56, 0xc1, 0xd2, 0xb2,
	0xed, 0xaa, 0xc8, 0xf1, 0xa0, 0x5b, 0xfe, 0xc6, 0xd1, 0xb9, 0x77, 0x21, 0x24, 0x2d, 0x7c, 0x15,
	0xb9, 0xf1, 0x0a, 0x0e, 0x99, 0x3a, 0x03, 0xb0, 0x8a, 0x4f, 0x45, 0x9d, 0x25, 0xdf, 0x0d, 0x95,
	0x3e, 0x3e, 0xdd, 0xf8, 0xaa, 0x61, 0x99, 0x3a, 0xfb, 0xd0, 0x29, 0x7d, 0xbd, 0xea, 0xdc, 0x5d,
	0xca, 0x3d, 0xfb, 0x3c, 0x76, 0xe3, 0xab, 0x19, 0x64, 0xea, 0xfc, 0x14, 0xec, 0xc5, 0x4f, 0x6f,
	0x9d, 0xef, 0x5c, 0x98, 0xb4, 0xe4, 0xdb, 0xde, 0x8d, 0xaf, 0xc1, 0x25, 0xd3, 0x47, 0x9f, 0xfc,
	0xce, 0x83, 0x71, 0x12, 0xb1, 0x78, 0xbc, 0xf5, 0xfd, 0x87, 0x88, 0x1c, 0x92, 0xc9, 0x7d, 0xfa,
	0x62, 0x38, 0x48, 0xa2, 0xfb, 0x98, 0xf4, 0x8a, 0x80, 0xcb, 0xfb, 0xa5, 0xcf, 0x9b, 0x71, 0xa1,
	0xe3, 0x26, 0xb1, 0x7c, 0xf2, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x50, 0x81, 0xed, 0x06, 0x27,
	0x2d, 0x00, 0x00,
}
