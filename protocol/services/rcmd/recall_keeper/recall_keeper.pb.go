// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/recall/recall_keeper.proto

package recall_keeper // import "golang.52tt.com/protocol/services/rcmd/recall_keeper"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import common "golang.52tt.com/protocol/services/rcmd/common"
import recall_common "golang.52tt.com/protocol/services/rcmd/recall_common"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type IndexAction int32

const (
	IndexAction_UnknownIndexAction IndexAction = 0
	IndexAction_Index              IndexAction = 1
	IndexAction_Delete             IndexAction = 2
)

var IndexAction_name = map[int32]string{
	0: "UnknownIndexAction",
	1: "Index",
	2: "Delete",
}
var IndexAction_value = map[string]int32{
	"UnknownIndexAction": 0,
	"Index":              1,
	"Delete":             2,
}

func (x IndexAction) String() string {
	return proto.EnumName(IndexAction_name, int32(x))
}
func (IndexAction) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{0}
}

// 将画像索引到ES
type PersonaIndexEvent struct {
	Action               IndexAction `protobuf:"varint,1,opt,name=action,proto3,enum=rcmd.recall_keeper.IndexAction" json:"action,omitempty"`
	Index                string      `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	Key                  string      `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	Json                 string      `protobuf:"bytes,4,opt,name=json,proto3" json:"json,omitempty"`
	TimeMs               uint64      `protobuf:"varint,5,opt,name=time_ms,json=timeMs,proto3" json:"time_ms,omitempty"`
	ExpireAt             uint64      `protobuf:"varint,6,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PersonaIndexEvent) Reset()         { *m = PersonaIndexEvent{} }
func (m *PersonaIndexEvent) String() string { return proto.CompactTextString(m) }
func (*PersonaIndexEvent) ProtoMessage()    {}
func (*PersonaIndexEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{0}
}
func (m *PersonaIndexEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PersonaIndexEvent.Unmarshal(m, b)
}
func (m *PersonaIndexEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PersonaIndexEvent.Marshal(b, m, deterministic)
}
func (dst *PersonaIndexEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PersonaIndexEvent.Merge(dst, src)
}
func (m *PersonaIndexEvent) XXX_Size() int {
	return xxx_messageInfo_PersonaIndexEvent.Size(m)
}
func (m *PersonaIndexEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_PersonaIndexEvent.DiscardUnknown(m)
}

var xxx_messageInfo_PersonaIndexEvent proto.InternalMessageInfo

func (m *PersonaIndexEvent) GetAction() IndexAction {
	if m != nil {
		return m.Action
	}
	return IndexAction_UnknownIndexAction
}

func (m *PersonaIndexEvent) GetIndex() string {
	if m != nil {
		return m.Index
	}
	return ""
}

func (m *PersonaIndexEvent) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *PersonaIndexEvent) GetJson() string {
	if m != nil {
		return m.Json
	}
	return ""
}

func (m *PersonaIndexEvent) GetTimeMs() uint64 {
	if m != nil {
		return m.TimeMs
	}
	return 0
}

func (m *PersonaIndexEvent) GetExpireAt() uint64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

type AddBloomReq struct {
	BloomLikeDesc        *recall_common.BloomLikeDesc `protobuf:"bytes,1,opt,name=bloom_like_desc,json=bloomLikeDesc,proto3" json:"bloom_like_desc,omitempty"`
	Ids                  []string                     `protobuf:"bytes,2,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *AddBloomReq) Reset()         { *m = AddBloomReq{} }
func (m *AddBloomReq) String() string { return proto.CompactTextString(m) }
func (*AddBloomReq) ProtoMessage()    {}
func (*AddBloomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{1}
}
func (m *AddBloomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBloomReq.Unmarshal(m, b)
}
func (m *AddBloomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBloomReq.Marshal(b, m, deterministic)
}
func (dst *AddBloomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBloomReq.Merge(dst, src)
}
func (m *AddBloomReq) XXX_Size() int {
	return xxx_messageInfo_AddBloomReq.Size(m)
}
func (m *AddBloomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBloomReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBloomReq proto.InternalMessageInfo

func (m *AddBloomReq) GetBloomLikeDesc() *recall_common.BloomLikeDesc {
	if m != nil {
		return m.BloomLikeDesc
	}
	return nil
}

func (m *AddBloomReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type AddBloomRsp struct {
	ErrCode              common.ErrorCode `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3,enum=rcmd.common.ErrorCode" json:"err_code,omitempty"`
	ErrMsg               string           `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddBloomRsp) Reset()         { *m = AddBloomRsp{} }
func (m *AddBloomRsp) String() string { return proto.CompactTextString(m) }
func (*AddBloomRsp) ProtoMessage()    {}
func (*AddBloomRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{2}
}
func (m *AddBloomRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBloomRsp.Unmarshal(m, b)
}
func (m *AddBloomRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBloomRsp.Marshal(b, m, deterministic)
}
func (dst *AddBloomRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBloomRsp.Merge(dst, src)
}
func (m *AddBloomRsp) XXX_Size() int {
	return xxx_messageInfo_AddBloomRsp.Size(m)
}
func (m *AddBloomRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBloomRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBloomRsp proto.InternalMessageInfo

func (m *AddBloomRsp) GetErrCode() common.ErrorCode {
	if m != nil {
		return m.ErrCode
	}
	return common.ErrorCode_Ok
}

func (m *AddBloomRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type ResetBloomReq struct {
	BloomLikeDesc        *recall_common.BloomLikeDesc `protobuf:"bytes,1,opt,name=bloom_like_desc,json=bloomLikeDesc,proto3" json:"bloom_like_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *ResetBloomReq) Reset()         { *m = ResetBloomReq{} }
func (m *ResetBloomReq) String() string { return proto.CompactTextString(m) }
func (*ResetBloomReq) ProtoMessage()    {}
func (*ResetBloomReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{3}
}
func (m *ResetBloomReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetBloomReq.Unmarshal(m, b)
}
func (m *ResetBloomReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetBloomReq.Marshal(b, m, deterministic)
}
func (dst *ResetBloomReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetBloomReq.Merge(dst, src)
}
func (m *ResetBloomReq) XXX_Size() int {
	return xxx_messageInfo_ResetBloomReq.Size(m)
}
func (m *ResetBloomReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetBloomReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResetBloomReq proto.InternalMessageInfo

func (m *ResetBloomReq) GetBloomLikeDesc() *recall_common.BloomLikeDesc {
	if m != nil {
		return m.BloomLikeDesc
	}
	return nil
}

type ResetBloomRsp struct {
	ErrCode              common.ErrorCode `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3,enum=rcmd.common.ErrorCode" json:"err_code,omitempty"`
	ErrMsg               string           `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ResetBloomRsp) Reset()         { *m = ResetBloomRsp{} }
func (m *ResetBloomRsp) String() string { return proto.CompactTextString(m) }
func (*ResetBloomRsp) ProtoMessage()    {}
func (*ResetBloomRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{4}
}
func (m *ResetBloomRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResetBloomRsp.Unmarshal(m, b)
}
func (m *ResetBloomRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResetBloomRsp.Marshal(b, m, deterministic)
}
func (dst *ResetBloomRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResetBloomRsp.Merge(dst, src)
}
func (m *ResetBloomRsp) XXX_Size() int {
	return xxx_messageInfo_ResetBloomRsp.Size(m)
}
func (m *ResetBloomRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResetBloomRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ResetBloomRsp proto.InternalMessageInfo

func (m *ResetBloomRsp) GetErrCode() common.ErrorCode {
	if m != nil {
		return m.ErrCode
	}
	return common.ErrorCode_Ok
}

func (m *ResetBloomRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type GetSetMembersReq struct {
	SetDesc              *recall_common.SetDesc `protobuf:"bytes,1,opt,name=set_desc,json=setDesc,proto3" json:"set_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetSetMembersReq) Reset()         { *m = GetSetMembersReq{} }
func (m *GetSetMembersReq) String() string { return proto.CompactTextString(m) }
func (*GetSetMembersReq) ProtoMessage()    {}
func (*GetSetMembersReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{5}
}
func (m *GetSetMembersReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSetMembersReq.Unmarshal(m, b)
}
func (m *GetSetMembersReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSetMembersReq.Marshal(b, m, deterministic)
}
func (dst *GetSetMembersReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSetMembersReq.Merge(dst, src)
}
func (m *GetSetMembersReq) XXX_Size() int {
	return xxx_messageInfo_GetSetMembersReq.Size(m)
}
func (m *GetSetMembersReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSetMembersReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSetMembersReq proto.InternalMessageInfo

func (m *GetSetMembersReq) GetSetDesc() *recall_common.SetDesc {
	if m != nil {
		return m.SetDesc
	}
	return nil
}

type GetSetMembersRsp struct {
	ErrCode              common.ErrorCode `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3,enum=rcmd.common.ErrorCode" json:"err_code,omitempty"`
	ErrMsg               string           `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	Ids                  []string         `protobuf:"bytes,3,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetSetMembersRsp) Reset()         { *m = GetSetMembersRsp{} }
func (m *GetSetMembersRsp) String() string { return proto.CompactTextString(m) }
func (*GetSetMembersRsp) ProtoMessage()    {}
func (*GetSetMembersRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{6}
}
func (m *GetSetMembersRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSetMembersRsp.Unmarshal(m, b)
}
func (m *GetSetMembersRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSetMembersRsp.Marshal(b, m, deterministic)
}
func (dst *GetSetMembersRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSetMembersRsp.Merge(dst, src)
}
func (m *GetSetMembersRsp) XXX_Size() int {
	return xxx_messageInfo_GetSetMembersRsp.Size(m)
}
func (m *GetSetMembersRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSetMembersRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSetMembersRsp proto.InternalMessageInfo

func (m *GetSetMembersRsp) GetErrCode() common.ErrorCode {
	if m != nil {
		return m.ErrCode
	}
	return common.ErrorCode_Ok
}

func (m *GetSetMembersRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func (m *GetSetMembersRsp) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type AddSetReq struct {
	SetDesc              *recall_common.SetDesc `protobuf:"bytes,1,opt,name=set_desc,json=setDesc,proto3" json:"set_desc,omitempty"`
	Ids                  []string               `protobuf:"bytes,4,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddSetReq) Reset()         { *m = AddSetReq{} }
func (m *AddSetReq) String() string { return proto.CompactTextString(m) }
func (*AddSetReq) ProtoMessage()    {}
func (*AddSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{7}
}
func (m *AddSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSetReq.Unmarshal(m, b)
}
func (m *AddSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSetReq.Marshal(b, m, deterministic)
}
func (dst *AddSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSetReq.Merge(dst, src)
}
func (m *AddSetReq) XXX_Size() int {
	return xxx_messageInfo_AddSetReq.Size(m)
}
func (m *AddSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSetReq proto.InternalMessageInfo

func (m *AddSetReq) GetSetDesc() *recall_common.SetDesc {
	if m != nil {
		return m.SetDesc
	}
	return nil
}

func (m *AddSetReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type AddSetRsp struct {
	ErrCode              common.ErrorCode `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3,enum=rcmd.common.ErrorCode" json:"err_code,omitempty"`
	ErrMsg               string           `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *AddSetRsp) Reset()         { *m = AddSetRsp{} }
func (m *AddSetRsp) String() string { return proto.CompactTextString(m) }
func (*AddSetRsp) ProtoMessage()    {}
func (*AddSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{8}
}
func (m *AddSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSetRsp.Unmarshal(m, b)
}
func (m *AddSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSetRsp.Marshal(b, m, deterministic)
}
func (dst *AddSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSetRsp.Merge(dst, src)
}
func (m *AddSetRsp) XXX_Size() int {
	return xxx_messageInfo_AddSetRsp.Size(m)
}
func (m *AddSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSetRsp proto.InternalMessageInfo

func (m *AddSetRsp) GetErrCode() common.ErrorCode {
	if m != nil {
		return m.ErrCode
	}
	return common.ErrorCode_Ok
}

func (m *AddSetRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

type RmFromSetReq struct {
	SetDesc              *recall_common.SetDesc `protobuf:"bytes,1,opt,name=set_desc,json=setDesc,proto3" json:"set_desc,omitempty"`
	Ids                  []string               `protobuf:"bytes,4,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *RmFromSetReq) Reset()         { *m = RmFromSetReq{} }
func (m *RmFromSetReq) String() string { return proto.CompactTextString(m) }
func (*RmFromSetReq) ProtoMessage()    {}
func (*RmFromSetReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{9}
}
func (m *RmFromSetReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RmFromSetReq.Unmarshal(m, b)
}
func (m *RmFromSetReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RmFromSetReq.Marshal(b, m, deterministic)
}
func (dst *RmFromSetReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RmFromSetReq.Merge(dst, src)
}
func (m *RmFromSetReq) XXX_Size() int {
	return xxx_messageInfo_RmFromSetReq.Size(m)
}
func (m *RmFromSetReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RmFromSetReq.DiscardUnknown(m)
}

var xxx_messageInfo_RmFromSetReq proto.InternalMessageInfo

func (m *RmFromSetReq) GetSetDesc() *recall_common.SetDesc {
	if m != nil {
		return m.SetDesc
	}
	return nil
}

func (m *RmFromSetReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type RmFromSetRsp struct {
	ErrCode              common.ErrorCode `protobuf:"varint,1,opt,name=err_code,json=errCode,proto3,enum=rcmd.common.ErrorCode" json:"err_code,omitempty"`
	ErrMsg               string           `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *RmFromSetRsp) Reset()         { *m = RmFromSetRsp{} }
func (m *RmFromSetRsp) String() string { return proto.CompactTextString(m) }
func (*RmFromSetRsp) ProtoMessage()    {}
func (*RmFromSetRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_recall_keeper_fa2277476dea7ae1, []int{10}
}
func (m *RmFromSetRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RmFromSetRsp.Unmarshal(m, b)
}
func (m *RmFromSetRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RmFromSetRsp.Marshal(b, m, deterministic)
}
func (dst *RmFromSetRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RmFromSetRsp.Merge(dst, src)
}
func (m *RmFromSetRsp) XXX_Size() int {
	return xxx_messageInfo_RmFromSetRsp.Size(m)
}
func (m *RmFromSetRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RmFromSetRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RmFromSetRsp proto.InternalMessageInfo

func (m *RmFromSetRsp) GetErrCode() common.ErrorCode {
	if m != nil {
		return m.ErrCode
	}
	return common.ErrorCode_Ok
}

func (m *RmFromSetRsp) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

func init() {
	proto.RegisterType((*PersonaIndexEvent)(nil), "rcmd.recall_keeper.PersonaIndexEvent")
	proto.RegisterType((*AddBloomReq)(nil), "rcmd.recall_keeper.AddBloomReq")
	proto.RegisterType((*AddBloomRsp)(nil), "rcmd.recall_keeper.AddBloomRsp")
	proto.RegisterType((*ResetBloomReq)(nil), "rcmd.recall_keeper.ResetBloomReq")
	proto.RegisterType((*ResetBloomRsp)(nil), "rcmd.recall_keeper.ResetBloomRsp")
	proto.RegisterType((*GetSetMembersReq)(nil), "rcmd.recall_keeper.GetSetMembersReq")
	proto.RegisterType((*GetSetMembersRsp)(nil), "rcmd.recall_keeper.GetSetMembersRsp")
	proto.RegisterType((*AddSetReq)(nil), "rcmd.recall_keeper.AddSetReq")
	proto.RegisterType((*AddSetRsp)(nil), "rcmd.recall_keeper.AddSetRsp")
	proto.RegisterType((*RmFromSetReq)(nil), "rcmd.recall_keeper.RmFromSetReq")
	proto.RegisterType((*RmFromSetRsp)(nil), "rcmd.recall_keeper.RmFromSetRsp")
	proto.RegisterEnum("rcmd.recall_keeper.IndexAction", IndexAction_name, IndexAction_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// RecallKeeperClient is the client API for RecallKeeper service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type RecallKeeperClient interface {
	// bloom like
	AddBloom(ctx context.Context, in *AddBloomReq, opts ...grpc.CallOption) (*AddBloomRsp, error)
	ResetBloom(ctx context.Context, in *ResetBloomReq, opts ...grpc.CallOption) (*ResetBloomRsp, error)
	// set
	GetSetMembers(ctx context.Context, in *GetSetMembersReq, opts ...grpc.CallOption) (*GetSetMembersRsp, error)
	AddSet(ctx context.Context, in *AddSetReq, opts ...grpc.CallOption) (*AddSetRsp, error)
	RmFromSet(ctx context.Context, in *RmFromSetReq, opts ...grpc.CallOption) (*RmFromSetRsp, error)
}

type recallKeeperClient struct {
	cc *grpc.ClientConn
}

func NewRecallKeeperClient(cc *grpc.ClientConn) RecallKeeperClient {
	return &recallKeeperClient{cc}
}

func (c *recallKeeperClient) AddBloom(ctx context.Context, in *AddBloomReq, opts ...grpc.CallOption) (*AddBloomRsp, error) {
	out := new(AddBloomRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_keeper.RecallKeeper/AddBloom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallKeeperClient) ResetBloom(ctx context.Context, in *ResetBloomReq, opts ...grpc.CallOption) (*ResetBloomRsp, error) {
	out := new(ResetBloomRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_keeper.RecallKeeper/ResetBloom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallKeeperClient) GetSetMembers(ctx context.Context, in *GetSetMembersReq, opts ...grpc.CallOption) (*GetSetMembersRsp, error) {
	out := new(GetSetMembersRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_keeper.RecallKeeper/GetSetMembers", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallKeeperClient) AddSet(ctx context.Context, in *AddSetReq, opts ...grpc.CallOption) (*AddSetRsp, error) {
	out := new(AddSetRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_keeper.RecallKeeper/AddSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *recallKeeperClient) RmFromSet(ctx context.Context, in *RmFromSetReq, opts ...grpc.CallOption) (*RmFromSetRsp, error) {
	out := new(RmFromSetRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall_keeper.RecallKeeper/RmFromSet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RecallKeeperServer is the server API for RecallKeeper service.
type RecallKeeperServer interface {
	// bloom like
	AddBloom(context.Context, *AddBloomReq) (*AddBloomRsp, error)
	ResetBloom(context.Context, *ResetBloomReq) (*ResetBloomRsp, error)
	// set
	GetSetMembers(context.Context, *GetSetMembersReq) (*GetSetMembersRsp, error)
	AddSet(context.Context, *AddSetReq) (*AddSetRsp, error)
	RmFromSet(context.Context, *RmFromSetReq) (*RmFromSetRsp, error)
}

func RegisterRecallKeeperServer(s *grpc.Server, srv RecallKeeperServer) {
	s.RegisterService(&_RecallKeeper_serviceDesc, srv)
}

func _RecallKeeper_AddBloom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBloomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallKeeperServer).AddBloom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_keeper.RecallKeeper/AddBloom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallKeeperServer).AddBloom(ctx, req.(*AddBloomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallKeeper_ResetBloom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetBloomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallKeeperServer).ResetBloom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_keeper.RecallKeeper/ResetBloom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallKeeperServer).ResetBloom(ctx, req.(*ResetBloomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallKeeper_GetSetMembers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSetMembersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallKeeperServer).GetSetMembers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_keeper.RecallKeeper/GetSetMembers",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallKeeperServer).GetSetMembers(ctx, req.(*GetSetMembersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallKeeper_AddSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallKeeperServer).AddSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_keeper.RecallKeeper/AddSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallKeeperServer).AddSet(ctx, req.(*AddSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RecallKeeper_RmFromSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RmFromSetReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RecallKeeperServer).RmFromSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall_keeper.RecallKeeper/RmFromSet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RecallKeeperServer).RmFromSet(ctx, req.(*RmFromSetReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _RecallKeeper_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.recall_keeper.RecallKeeper",
	HandlerType: (*RecallKeeperServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddBloom",
			Handler:    _RecallKeeper_AddBloom_Handler,
		},
		{
			MethodName: "ResetBloom",
			Handler:    _RecallKeeper_ResetBloom_Handler,
		},
		{
			MethodName: "GetSetMembers",
			Handler:    _RecallKeeper_GetSetMembers_Handler,
		},
		{
			MethodName: "AddSet",
			Handler:    _RecallKeeper_AddSet_Handler,
		},
		{
			MethodName: "RmFromSet",
			Handler:    _RecallKeeper_RmFromSet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/recall/recall_keeper.proto",
}

func init() {
	proto.RegisterFile("rcmd/recall/recall_keeper.proto", fileDescriptor_recall_keeper_fa2277476dea7ae1)
}

var fileDescriptor_recall_keeper_fa2277476dea7ae1 = []byte{
	// 596 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x55, 0xcd, 0x6e, 0xd3, 0x40,
	0x10, 0xc6, 0x4d, 0xea, 0x26, 0xd3, 0x16, 0xc2, 0x08, 0xb5, 0x96, 0x2b, 0xd4, 0xd4, 0xe2, 0x10,
	0x71, 0x48, 0x44, 0x80, 0x72, 0xe1, 0x92, 0xd2, 0x16, 0x15, 0x1a, 0x81, 0x1c, 0x55, 0x40, 0x7a,
	0xb0, 0x12, 0x7b, 0x14, 0xb9, 0xb1, 0xbd, 0x66, 0x77, 0x55, 0xca, 0x0b, 0xf1, 0x18, 0x3c, 0x1b,
	0xda, 0xb5, 0xd3, 0x38, 0xc5, 0xb4, 0x48, 0x84, 0x93, 0x67, 0x67, 0xbf, 0xfd, 0xf6, 0x9b, 0x9d,
	0x1f, 0xc3, 0x2e, 0xf7, 0xe3, 0xa0, 0xc3, 0xc9, 0x1f, 0x45, 0x51, 0xfe, 0xf1, 0xa6, 0x44, 0x29,
	0xf1, 0x76, 0xca, 0x99, 0x64, 0x88, 0x0a, 0xd0, 0x5e, 0xd8, 0xb1, 0xb7, 0xf5, 0x21, 0x9f, 0xc5,
	0x31, 0x4b, 0x3a, 0xc4, 0x39, 0xcb, 0xc1, 0x76, 0x19, 0x5b, 0x06, 0xcb, 0x00, 0xce, 0x4f, 0x03,
	0x1e, 0x7e, 0x24, 0x2e, 0x58, 0x32, 0x3a, 0x49, 0x02, 0xba, 0x3a, 0xba, 0xa4, 0x44, 0xe2, 0x2b,
	0x30, 0x47, 0xbe, 0x0c, 0x59, 0x62, 0x19, 0x4d, 0xa3, 0x75, 0xbf, 0xbb, 0xdb, 0xfe, 0xfd, 0xd2,
	0xb6, 0xc6, 0xf7, 0x34, 0xcc, 0xcd, 0xe1, 0xf8, 0x08, 0x56, 0x43, 0xe5, 0xb6, 0x56, 0x9a, 0x46,
	0xab, 0xee, 0x66, 0x0b, 0x6c, 0x40, 0x65, 0x4a, 0xdf, 0xad, 0x8a, 0xf6, 0x29, 0x13, 0x11, 0xaa,
	0x17, 0x82, 0x25, 0x56, 0x55, 0xbb, 0xb4, 0x8d, 0xdb, 0xb0, 0x26, 0xc3, 0x98, 0xbc, 0x58, 0x58,
	0xab, 0x4d, 0xa3, 0x55, 0x75, 0x4d, 0xb5, 0xec, 0x0b, 0xdc, 0x81, 0x3a, 0x5d, 0xa5, 0x21, 0x27,
	0x6f, 0x24, 0x2d, 0x53, 0x6f, 0xd5, 0x32, 0x47, 0x4f, 0x3a, 0x17, 0xb0, 0xde, 0x0b, 0x82, 0x83,
	0x88, 0xb1, 0xd8, 0xa5, 0xaf, 0x78, 0x02, 0x0f, 0xc6, 0xca, 0xf6, 0xa2, 0x70, 0x4a, 0x5e, 0x40,
	0xc2, 0xd7, 0x21, 0xac, 0x77, 0xf7, 0x16, 0x42, 0xc8, 0xdf, 0x40, 0x1f, 0x3b, 0x0d, 0xa7, 0x74,
	0x48, 0xc2, 0x77, 0x37, 0xc7, 0xc5, 0xa5, 0x52, 0x1d, 0x06, 0xc2, 0x5a, 0x69, 0x56, 0x94, 0xea,
	0x30, 0x10, 0xce, 0x97, 0xc2, 0x5d, 0x22, 0xc5, 0x67, 0x50, 0x23, 0xce, 0x3d, 0x9f, 0x05, 0x94,
	0xbf, 0xd3, 0x56, 0x76, 0x49, 0xce, 0x7e, 0xa4, 0x12, 0xf1, 0x86, 0x05, 0xe4, 0xae, 0x11, 0xd7,
	0x86, 0x8a, 0x51, 0x1d, 0x89, 0xc5, 0x24, 0x7f, 0x21, 0x93, 0x38, 0xef, 0x8b, 0x89, 0x33, 0x84,
	0x4d, 0x97, 0x04, 0xc9, 0xff, 0x10, 0x88, 0x73, 0xbe, 0xc0, 0xbd, 0x64, 0xe1, 0xef, 0xa0, 0xf1,
	0x96, 0xe4, 0x80, 0x64, 0x9f, 0xe2, 0x31, 0x71, 0xa1, 0xb4, 0xef, 0x43, 0x4d, 0x90, 0x2c, 0x8a,
	0xde, 0x29, 0x13, 0x3d, 0x20, 0xa9, 0xe5, 0xae, 0x89, 0xcc, 0x70, 0xd2, 0x9b, 0x5c, 0xcb, 0xd5,
	0x3a, 0xcb, 0x68, 0x65, 0x9e, 0xd1, 0x33, 0xa8, 0xf7, 0x82, 0x60, 0x40, 0xf2, 0x1f, 0x64, 0xcf,
	0x68, 0xab, 0x73, 0xda, 0x4f, 0xd7, 0xb4, 0x4b, 0x7e, 0xed, 0xcf, 0xb0, 0xe1, 0xc6, 0xc7, 0x9c,
	0xc5, 0x4b, 0x97, 0x3c, 0x2c, 0x32, 0x2f, 0x57, 0xf5, 0xd3, 0xd7, 0xb0, 0x5e, 0x18, 0x16, 0xb8,
	0x05, 0x78, 0x96, 0x4c, 0x13, 0xf6, 0x2d, 0x29, 0x78, 0x1b, 0xf7, 0xb0, 0x0e, 0xab, 0xda, 0xd1,
	0x30, 0x10, 0xc0, 0x3c, 0xa4, 0x88, 0x24, 0x35, 0x56, 0xba, 0x3f, 0x2a, 0xb0, 0xe1, 0xea, 0x70,
	0xde, 0xeb, 0xc1, 0x83, 0xa7, 0x50, 0x9b, 0xb5, 0x21, 0x96, 0x4e, 0xa6, 0xc2, 0x40, 0xb0, 0x6f,
	0x07, 0x88, 0x14, 0x5d, 0x80, 0x79, 0x77, 0xe0, 0x5e, 0x19, 0x7c, 0xa1, 0x33, 0xed, 0xbb, 0x20,
	0x22, 0xc5, 0x73, 0xd8, 0x5c, 0x28, 0x64, 0x7c, 0x52, 0x76, 0xe6, 0x66, 0xdf, 0xd8, 0x7f, 0x81,
	0x12, 0x29, 0x1e, 0x83, 0x99, 0x15, 0x17, 0x3e, 0xfe, 0x43, 0x6c, 0x59, 0x71, 0xd8, 0xb7, 0x6d,
	0x8b, 0x14, 0x3f, 0x40, 0xfd, 0x3a, 0xe3, 0xd8, 0x2c, 0x0d, 0xaa, 0x50, 0x6a, 0xf6, 0x1d, 0x08,
	0x91, 0x1e, 0xec, 0x0f, 0x5f, 0x4c, 0x58, 0x34, 0x4a, 0x26, 0xed, 0x97, 0x5d, 0x29, 0x55, 0xa1,
	0x74, 0xf4, 0x4f, 0xc6, 0x67, 0x51, 0x47, 0x10, 0xbf, 0x0c, 0x7d, 0x12, 0x9d, 0xc2, 0x0f, 0x29,
	0x27, 0x19, 0x9b, 0x1a, 0xf5, 0xfc, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x31, 0x52, 0xaf, 0x62,
	0xfb, 0x06, 0x00, 0x00,
}
