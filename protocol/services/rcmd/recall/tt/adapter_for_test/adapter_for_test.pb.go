// Code generated by protoc-gen-go. DO NOT EDIT.
// source: rcmd/recall/tt/adapter_for_test.proto

package adapter_for_test // import "golang.52tt.com/protocol/services/rcmd/recall/tt/adapter_for_test"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type EventId int32

const (
	EventId_UnKnow        EventId = 0
	EventId_Register      EventId = 1
	EventId_Login         EventId = 2
	EventId_FollowUser    EventId = 3
	EventId_IM            EventId = 4
	EventId_Post          EventId = 5
	EventId_Enter_Channel EventId = 6
)

var EventId_name = map[int32]string{
	0: "UnKnow",
	1: "Register",
	2: "Login",
	3: "FollowUser",
	4: "IM",
	5: "Post",
	6: "Enter_Channel",
}
var EventId_value = map[string]int32{
	"UnKnow":        0,
	"Register":      1,
	"Login":         2,
	"FollowUser":    3,
	"IM":            4,
	"Post":          5,
	"Enter_Channel": 6,
}

func (x EventId) String() string {
	return proto.EnumName(EventId_name, int32(x))
}
func (EventId) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{0}
}

type BatchSetRecallReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetRecallReq) Reset()         { *m = BatchSetRecallReq{} }
func (m *BatchSetRecallReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetRecallReq) ProtoMessage()    {}
func (*BatchSetRecallReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{0}
}
func (m *BatchSetRecallReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetRecallReq.Unmarshal(m, b)
}
func (m *BatchSetRecallReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetRecallReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetRecallReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetRecallReq.Merge(dst, src)
}
func (m *BatchSetRecallReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetRecallReq.Size(m)
}
func (m *BatchSetRecallReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetRecallReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetRecallReq proto.InternalMessageInfo

func (m *BatchSetRecallReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchSetRecallRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetRecallRsp) Reset()         { *m = BatchSetRecallRsp{} }
func (m *BatchSetRecallRsp) String() string { return proto.CompactTextString(m) }
func (*BatchSetRecallRsp) ProtoMessage()    {}
func (*BatchSetRecallRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{1}
}
func (m *BatchSetRecallRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetRecallRsp.Unmarshal(m, b)
}
func (m *BatchSetRecallRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetRecallRsp.Marshal(b, m, deterministic)
}
func (dst *BatchSetRecallRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetRecallRsp.Merge(dst, src)
}
func (m *BatchSetRecallRsp) XXX_Size() int {
	return xxx_messageInfo_BatchSetRecallRsp.Size(m)
}
func (m *BatchSetRecallRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetRecallRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetRecallRsp proto.InternalMessageInfo

func (m *BatchSetRecallRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BatchSetRecallRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type TmpPersonaItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	DeliverCount         uint32   `protobuf:"varint,2,opt,name=deliver_count,json=deliverCount,proto3" json:"deliver_count,omitempty"`
	EnterCount           uint32   `protobuf:"varint,3,opt,name=enter_count,json=enterCount,proto3" json:"enter_count,omitempty"`
	FollowCount          uint32   `protobuf:"varint,4,opt,name=follow_count,json=followCount,proto3" json:"follow_count,omitempty"`
	ImCount              uint32   `protobuf:"varint,5,opt,name=im_count,json=imCount,proto3" json:"im_count,omitempty"`
	PostCount            uint32   `protobuf:"varint,6,opt,name=post_count,json=postCount,proto3" json:"post_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TmpPersonaItem) Reset()         { *m = TmpPersonaItem{} }
func (m *TmpPersonaItem) String() string { return proto.CompactTextString(m) }
func (*TmpPersonaItem) ProtoMessage()    {}
func (*TmpPersonaItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{2}
}
func (m *TmpPersonaItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TmpPersonaItem.Unmarshal(m, b)
}
func (m *TmpPersonaItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TmpPersonaItem.Marshal(b, m, deterministic)
}
func (dst *TmpPersonaItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TmpPersonaItem.Merge(dst, src)
}
func (m *TmpPersonaItem) XXX_Size() int {
	return xxx_messageInfo_TmpPersonaItem.Size(m)
}
func (m *TmpPersonaItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TmpPersonaItem.DiscardUnknown(m)
}

var xxx_messageInfo_TmpPersonaItem proto.InternalMessageInfo

func (m *TmpPersonaItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TmpPersonaItem) GetDeliverCount() uint32 {
	if m != nil {
		return m.DeliverCount
	}
	return 0
}

func (m *TmpPersonaItem) GetEnterCount() uint32 {
	if m != nil {
		return m.EnterCount
	}
	return 0
}

func (m *TmpPersonaItem) GetFollowCount() uint32 {
	if m != nil {
		return m.FollowCount
	}
	return 0
}

func (m *TmpPersonaItem) GetImCount() uint32 {
	if m != nil {
		return m.ImCount
	}
	return 0
}

func (m *TmpPersonaItem) GetPostCount() uint32 {
	if m != nil {
		return m.PostCount
	}
	return 0
}

type BatchSetPersonaReq struct {
	PersonaList          []*TmpPersonaItem `protobuf:"bytes,1,rep,name=persona_list,json=personaList,proto3" json:"persona_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchSetPersonaReq) Reset()         { *m = BatchSetPersonaReq{} }
func (m *BatchSetPersonaReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetPersonaReq) ProtoMessage()    {}
func (*BatchSetPersonaReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{3}
}
func (m *BatchSetPersonaReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetPersonaReq.Unmarshal(m, b)
}
func (m *BatchSetPersonaReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetPersonaReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetPersonaReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetPersonaReq.Merge(dst, src)
}
func (m *BatchSetPersonaReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetPersonaReq.Size(m)
}
func (m *BatchSetPersonaReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetPersonaReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetPersonaReq proto.InternalMessageInfo

func (m *BatchSetPersonaReq) GetPersonaList() []*TmpPersonaItem {
	if m != nil {
		return m.PersonaList
	}
	return nil
}

type BatchSetPersonaRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetPersonaRsp) Reset()         { *m = BatchSetPersonaRsp{} }
func (m *BatchSetPersonaRsp) String() string { return proto.CompactTextString(m) }
func (*BatchSetPersonaRsp) ProtoMessage()    {}
func (*BatchSetPersonaRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{4}
}
func (m *BatchSetPersonaRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetPersonaRsp.Unmarshal(m, b)
}
func (m *BatchSetPersonaRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetPersonaRsp.Marshal(b, m, deterministic)
}
func (dst *BatchSetPersonaRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetPersonaRsp.Merge(dst, src)
}
func (m *BatchSetPersonaRsp) XXX_Size() int {
	return xxx_messageInfo_BatchSetPersonaRsp.Size(m)
}
func (m *BatchSetPersonaRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetPersonaRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetPersonaRsp proto.InternalMessageInfo

func (m *BatchSetPersonaRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BatchSetPersonaRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type CallPlaymateEntranceReq struct {
	ReqUid               uint32   `protobuf:"varint,1,opt,name=req_uid,json=reqUid,proto3" json:"req_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CallPlaymateEntranceReq) Reset()         { *m = CallPlaymateEntranceReq{} }
func (m *CallPlaymateEntranceReq) String() string { return proto.CompactTextString(m) }
func (*CallPlaymateEntranceReq) ProtoMessage()    {}
func (*CallPlaymateEntranceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{5}
}
func (m *CallPlaymateEntranceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CallPlaymateEntranceReq.Unmarshal(m, b)
}
func (m *CallPlaymateEntranceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CallPlaymateEntranceReq.Marshal(b, m, deterministic)
}
func (dst *CallPlaymateEntranceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CallPlaymateEntranceReq.Merge(dst, src)
}
func (m *CallPlaymateEntranceReq) XXX_Size() int {
	return xxx_messageInfo_CallPlaymateEntranceReq.Size(m)
}
func (m *CallPlaymateEntranceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CallPlaymateEntranceReq.DiscardUnknown(m)
}

var xxx_messageInfo_CallPlaymateEntranceReq proto.InternalMessageInfo

func (m *CallPlaymateEntranceReq) GetReqUid() uint32 {
	if m != nil {
		return m.ReqUid
	}
	return 0
}

type CallPlaymateEntranceRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Final                []*Item  `protobuf:"bytes,3,rep,name=final,proto3" json:"final,omitempty"`
	BeforeRerank         []*Item  `protobuf:"bytes,4,rep,name=before_rerank,json=beforeRerank,proto3" json:"before_rerank,omitempty"`
	AfterRerank          []*Item  `protobuf:"bytes,5,rep,name=after_rerank,json=afterRerank,proto3" json:"after_rerank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CallPlaymateEntranceRsp) Reset()         { *m = CallPlaymateEntranceRsp{} }
func (m *CallPlaymateEntranceRsp) String() string { return proto.CompactTextString(m) }
func (*CallPlaymateEntranceRsp) ProtoMessage()    {}
func (*CallPlaymateEntranceRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{6}
}
func (m *CallPlaymateEntranceRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CallPlaymateEntranceRsp.Unmarshal(m, b)
}
func (m *CallPlaymateEntranceRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CallPlaymateEntranceRsp.Marshal(b, m, deterministic)
}
func (dst *CallPlaymateEntranceRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CallPlaymateEntranceRsp.Merge(dst, src)
}
func (m *CallPlaymateEntranceRsp) XXX_Size() int {
	return xxx_messageInfo_CallPlaymateEntranceRsp.Size(m)
}
func (m *CallPlaymateEntranceRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CallPlaymateEntranceRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CallPlaymateEntranceRsp proto.InternalMessageInfo

func (m *CallPlaymateEntranceRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *CallPlaymateEntranceRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *CallPlaymateEntranceRsp) GetFinal() []*Item {
	if m != nil {
		return m.Final
	}
	return nil
}

func (m *CallPlaymateEntranceRsp) GetBeforeRerank() []*Item {
	if m != nil {
		return m.BeforeRerank
	}
	return nil
}

func (m *CallPlaymateEntranceRsp) GetAfterRerank() []*Item {
	if m != nil {
		return m.AfterRerank
	}
	return nil
}

type ProfileReq struct {
	Scene                string   `protobuf:"bytes,1,opt,name=scene,proto3" json:"scene,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProfileReq) Reset()         { *m = ProfileReq{} }
func (m *ProfileReq) String() string { return proto.CompactTextString(m) }
func (*ProfileReq) ProtoMessage()    {}
func (*ProfileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{7}
}
func (m *ProfileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProfileReq.Unmarshal(m, b)
}
func (m *ProfileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProfileReq.Marshal(b, m, deterministic)
}
func (dst *ProfileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProfileReq.Merge(dst, src)
}
func (m *ProfileReq) XXX_Size() int {
	return xxx_messageInfo_ProfileReq.Size(m)
}
func (m *ProfileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProfileReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProfileReq proto.InternalMessageInfo

func (m *ProfileReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

type Item struct {
	// 调用entrance返回的列表
	ItemId uint32 `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	// 除了id，如果需要更详细的信息，可以recall/rank/model写入redis（trace_id作为key)
	// 然后adapter-for-test读取redis拼装在下面这个opt里面返回
	// 拼装的参数跟测试约定,比如 "关注数","注册时间","进池时间"
	Opt                  map[string]string `protobuf:"bytes,2,rep,name=opt,proto3" json:"opt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Item) Reset()         { *m = Item{} }
func (m *Item) String() string { return proto.CompactTextString(m) }
func (*Item) ProtoMessage()    {}
func (*Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{8}
}
func (m *Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Item.Unmarshal(m, b)
}
func (m *Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Item.Marshal(b, m, deterministic)
}
func (dst *Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Item.Merge(dst, src)
}
func (m *Item) XXX_Size() int {
	return xxx_messageInfo_Item.Size(m)
}
func (m *Item) XXX_DiscardUnknown() {
	xxx_messageInfo_Item.DiscardUnknown(m)
}

var xxx_messageInfo_Item proto.InternalMessageInfo

func (m *Item) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *Item) GetOpt() map[string]string {
	if m != nil {
		return m.Opt
	}
	return nil
}

type ProfileRsp struct {
	Code                 uint32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string            `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	ItemList             []*Item           `protobuf:"bytes,3,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	GlobalOpt            map[string]string `protobuf:"bytes,4,rep,name=global_opt,json=globalOpt,proto3" json:"global_opt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *ProfileRsp) Reset()         { *m = ProfileRsp{} }
func (m *ProfileRsp) String() string { return proto.CompactTextString(m) }
func (*ProfileRsp) ProtoMessage()    {}
func (*ProfileRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{9}
}
func (m *ProfileRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProfileRsp.Unmarshal(m, b)
}
func (m *ProfileRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProfileRsp.Marshal(b, m, deterministic)
}
func (dst *ProfileRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProfileRsp.Merge(dst, src)
}
func (m *ProfileRsp) XXX_Size() int {
	return xxx_messageInfo_ProfileRsp.Size(m)
}
func (m *ProfileRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProfileRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ProfileRsp proto.InternalMessageInfo

func (m *ProfileRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *ProfileRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *ProfileRsp) GetItemList() []*Item {
	if m != nil {
		return m.ItemList
	}
	return nil
}

func (m *ProfileRsp) GetGlobalOpt() map[string]string {
	if m != nil {
		return m.GlobalOpt
	}
	return nil
}

type BatchMakeEventsReq struct {
	Events               []*Event `protobuf:"bytes,1,rep,name=events,proto3" json:"events,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchMakeEventsReq) Reset()         { *m = BatchMakeEventsReq{} }
func (m *BatchMakeEventsReq) String() string { return proto.CompactTextString(m) }
func (*BatchMakeEventsReq) ProtoMessage()    {}
func (*BatchMakeEventsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{10}
}
func (m *BatchMakeEventsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMakeEventsReq.Unmarshal(m, b)
}
func (m *BatchMakeEventsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMakeEventsReq.Marshal(b, m, deterministic)
}
func (dst *BatchMakeEventsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMakeEventsReq.Merge(dst, src)
}
func (m *BatchMakeEventsReq) XXX_Size() int {
	return xxx_messageInfo_BatchMakeEventsReq.Size(m)
}
func (m *BatchMakeEventsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMakeEventsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMakeEventsReq proto.InternalMessageInfo

func (m *BatchMakeEventsReq) GetEvents() []*Event {
	if m != nil {
		return m.Events
	}
	return nil
}

type BatchMakeEventsRsp struct {
	Code                 uint32   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg                  string   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchMakeEventsRsp) Reset()         { *m = BatchMakeEventsRsp{} }
func (m *BatchMakeEventsRsp) String() string { return proto.CompactTextString(m) }
func (*BatchMakeEventsRsp) ProtoMessage()    {}
func (*BatchMakeEventsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{11}
}
func (m *BatchMakeEventsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchMakeEventsRsp.Unmarshal(m, b)
}
func (m *BatchMakeEventsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchMakeEventsRsp.Marshal(b, m, deterministic)
}
func (dst *BatchMakeEventsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchMakeEventsRsp.Merge(dst, src)
}
func (m *BatchMakeEventsRsp) XXX_Size() int {
	return xxx_messageInfo_BatchMakeEventsRsp.Size(m)
}
func (m *BatchMakeEventsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchMakeEventsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchMakeEventsRsp proto.InternalMessageInfo

func (m *BatchMakeEventsRsp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *BatchMakeEventsRsp) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

type Event struct {
	EventId              EventId           `protobuf:"varint,1,opt,name=event_id,json=eventId,proto3,enum=rcmd.recall.tt.adapter_for_test.EventId" json:"event_id,omitempty"`
	Uid                  uint32            `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	ItemId               uint32            `protobuf:"varint,3,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	Opt                  map[string]string `protobuf:"bytes,4,rep,name=opt,proto3" json:"opt,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *Event) Reset()         { *m = Event{} }
func (m *Event) String() string { return proto.CompactTextString(m) }
func (*Event) ProtoMessage()    {}
func (*Event) Descriptor() ([]byte, []int) {
	return fileDescriptor_adapter_for_test_89b72e4f7cb8f074, []int{12}
}
func (m *Event) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Event.Unmarshal(m, b)
}
func (m *Event) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Event.Marshal(b, m, deterministic)
}
func (dst *Event) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Event.Merge(dst, src)
}
func (m *Event) XXX_Size() int {
	return xxx_messageInfo_Event.Size(m)
}
func (m *Event) XXX_DiscardUnknown() {
	xxx_messageInfo_Event.DiscardUnknown(m)
}

var xxx_messageInfo_Event proto.InternalMessageInfo

func (m *Event) GetEventId() EventId {
	if m != nil {
		return m.EventId
	}
	return EventId_UnKnow
}

func (m *Event) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *Event) GetItemId() uint32 {
	if m != nil {
		return m.ItemId
	}
	return 0
}

func (m *Event) GetOpt() map[string]string {
	if m != nil {
		return m.Opt
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchSetRecallReq)(nil), "rcmd.recall.tt.adapter_for_test.BatchSetRecallReq")
	proto.RegisterType((*BatchSetRecallRsp)(nil), "rcmd.recall.tt.adapter_for_test.BatchSetRecallRsp")
	proto.RegisterType((*TmpPersonaItem)(nil), "rcmd.recall.tt.adapter_for_test.TmpPersonaItem")
	proto.RegisterType((*BatchSetPersonaReq)(nil), "rcmd.recall.tt.adapter_for_test.BatchSetPersonaReq")
	proto.RegisterType((*BatchSetPersonaRsp)(nil), "rcmd.recall.tt.adapter_for_test.BatchSetPersonaRsp")
	proto.RegisterType((*CallPlaymateEntranceReq)(nil), "rcmd.recall.tt.adapter_for_test.CallPlaymateEntranceReq")
	proto.RegisterType((*CallPlaymateEntranceRsp)(nil), "rcmd.recall.tt.adapter_for_test.CallPlaymateEntranceRsp")
	proto.RegisterType((*ProfileReq)(nil), "rcmd.recall.tt.adapter_for_test.ProfileReq")
	proto.RegisterType((*Item)(nil), "rcmd.recall.tt.adapter_for_test.Item")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.recall.tt.adapter_for_test.Item.OptEntry")
	proto.RegisterType((*ProfileRsp)(nil), "rcmd.recall.tt.adapter_for_test.ProfileRsp")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.recall.tt.adapter_for_test.ProfileRsp.GlobalOptEntry")
	proto.RegisterType((*BatchMakeEventsReq)(nil), "rcmd.recall.tt.adapter_for_test.BatchMakeEventsReq")
	proto.RegisterType((*BatchMakeEventsRsp)(nil), "rcmd.recall.tt.adapter_for_test.BatchMakeEventsRsp")
	proto.RegisterType((*Event)(nil), "rcmd.recall.tt.adapter_for_test.Event")
	proto.RegisterMapType((map[string]string)(nil), "rcmd.recall.tt.adapter_for_test.Event.OptEntry")
	proto.RegisterEnum("rcmd.recall.tt.adapter_for_test.EventId", EventId_name, EventId_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AdapterForTestServerClient is the client API for AdapterForTestServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AdapterForTestServerClient interface {
	BatchMakeEvents(ctx context.Context, in *BatchMakeEventsReq, opts ...grpc.CallOption) (*BatchMakeEventsRsp, error)
	// 设置召回
	BatchSetRecall(ctx context.Context, in *BatchSetRecallReq, opts ...grpc.CallOption) (*BatchSetRecallRsp, error)
	// 批量设置画像
	BatchSetPersona(ctx context.Context, in *BatchSetPersonaReq, opts ...grpc.CallOption) (*BatchSetPersonaRsp, error)
	// 查看设置的数据
	Profile(ctx context.Context, in *ProfileReq, opts ...grpc.CallOption) (*ProfileRsp, error)
	// 访问接口获取traceId,然后获取拼接数据返回
	CallPlaymateEntrance(ctx context.Context, in *CallPlaymateEntranceReq, opts ...grpc.CallOption) (*CallPlaymateEntranceRsp, error)
}

type adapterForTestServerClient struct {
	cc *grpc.ClientConn
}

func NewAdapterForTestServerClient(cc *grpc.ClientConn) AdapterForTestServerClient {
	return &adapterForTestServerClient{cc}
}

func (c *adapterForTestServerClient) BatchMakeEvents(ctx context.Context, in *BatchMakeEventsReq, opts ...grpc.CallOption) (*BatchMakeEventsRsp, error) {
	out := new(BatchMakeEventsRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/BatchMakeEvents", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterForTestServerClient) BatchSetRecall(ctx context.Context, in *BatchSetRecallReq, opts ...grpc.CallOption) (*BatchSetRecallRsp, error) {
	out := new(BatchSetRecallRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/BatchSetRecall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterForTestServerClient) BatchSetPersona(ctx context.Context, in *BatchSetPersonaReq, opts ...grpc.CallOption) (*BatchSetPersonaRsp, error) {
	out := new(BatchSetPersonaRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/BatchSetPersona", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterForTestServerClient) Profile(ctx context.Context, in *ProfileReq, opts ...grpc.CallOption) (*ProfileRsp, error) {
	out := new(ProfileRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/Profile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adapterForTestServerClient) CallPlaymateEntrance(ctx context.Context, in *CallPlaymateEntranceReq, opts ...grpc.CallOption) (*CallPlaymateEntranceRsp, error) {
	out := new(CallPlaymateEntranceRsp)
	err := c.cc.Invoke(ctx, "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/CallPlaymateEntrance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdapterForTestServerServer is the server API for AdapterForTestServer service.
type AdapterForTestServerServer interface {
	BatchMakeEvents(context.Context, *BatchMakeEventsReq) (*BatchMakeEventsRsp, error)
	// 设置召回
	BatchSetRecall(context.Context, *BatchSetRecallReq) (*BatchSetRecallRsp, error)
	// 批量设置画像
	BatchSetPersona(context.Context, *BatchSetPersonaReq) (*BatchSetPersonaRsp, error)
	// 查看设置的数据
	Profile(context.Context, *ProfileReq) (*ProfileRsp, error)
	// 访问接口获取traceId,然后获取拼接数据返回
	CallPlaymateEntrance(context.Context, *CallPlaymateEntranceReq) (*CallPlaymateEntranceRsp, error)
}

func RegisterAdapterForTestServerServer(s *grpc.Server, srv AdapterForTestServerServer) {
	s.RegisterService(&_AdapterForTestServer_serviceDesc, srv)
}

func _AdapterForTestServer_BatchMakeEvents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchMakeEventsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterForTestServerServer).BatchMakeEvents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/BatchMakeEvents",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterForTestServerServer).BatchMakeEvents(ctx, req.(*BatchMakeEventsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterForTestServer_BatchSetRecall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetRecallReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterForTestServerServer).BatchSetRecall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/BatchSetRecall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterForTestServerServer).BatchSetRecall(ctx, req.(*BatchSetRecallReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterForTestServer_BatchSetPersona_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetPersonaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterForTestServerServer).BatchSetPersona(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/BatchSetPersona",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterForTestServerServer).BatchSetPersona(ctx, req.(*BatchSetPersonaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterForTestServer_Profile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProfileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterForTestServerServer).Profile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/Profile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterForTestServerServer).Profile(ctx, req.(*ProfileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdapterForTestServer_CallPlaymateEntrance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CallPlaymateEntranceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdapterForTestServerServer).CallPlaymateEntrance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/rcmd.recall.tt.adapter_for_test.AdapterForTestServer/CallPlaymateEntrance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdapterForTestServerServer).CallPlaymateEntrance(ctx, req.(*CallPlaymateEntranceReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AdapterForTestServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "rcmd.recall.tt.adapter_for_test.AdapterForTestServer",
	HandlerType: (*AdapterForTestServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchMakeEvents",
			Handler:    _AdapterForTestServer_BatchMakeEvents_Handler,
		},
		{
			MethodName: "BatchSetRecall",
			Handler:    _AdapterForTestServer_BatchSetRecall_Handler,
		},
		{
			MethodName: "BatchSetPersona",
			Handler:    _AdapterForTestServer_BatchSetPersona_Handler,
		},
		{
			MethodName: "Profile",
			Handler:    _AdapterForTestServer_Profile_Handler,
		},
		{
			MethodName: "CallPlaymateEntrance",
			Handler:    _AdapterForTestServer_CallPlaymateEntrance_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rcmd/recall/tt/adapter_for_test.proto",
}

func init() {
	proto.RegisterFile("rcmd/recall/tt/adapter_for_test.proto", fileDescriptor_adapter_for_test_89b72e4f7cb8f074)
}

var fileDescriptor_adapter_for_test_89b72e4f7cb8f074 = []byte{
	// 858 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x56, 0x5f, 0x6f, 0xe3, 0x44,
	0x10, 0xc7, 0xf9, 0x9f, 0xc9, 0x1f, 0x72, 0xab, 0x4a, 0xcd, 0x45, 0x42, 0x57, 0x8c, 0x0e, 0x55,
	0x9c, 0xe4, 0x48, 0x39, 0x81, 0x8e, 0x82, 0x10, 0x6d, 0xd4, 0x83, 0xc2, 0x9d, 0xa8, 0x7c, 0xed,
	0x03, 0xbc, 0x58, 0x5b, 0x7b, 0x92, 0x5a, 0x5d, 0x7b, 0xdd, 0xdd, 0x4d, 0x8e, 0x8a, 0x6f, 0xc0,
	0x0b, 0x12, 0x6f, 0x7c, 0x20, 0xbe, 0x10, 0xcf, 0x3c, 0xa0, 0xdd, 0x75, 0x9a, 0xe6, 0x4a, 0x15,
	0x1b, 0xf1, 0xb6, 0xbb, 0xf3, 0xfb, 0x8d, 0x67, 0x7e, 0x33, 0xbb, 0x63, 0x78, 0x2a, 0xc2, 0x24,
	0x1a, 0x0b, 0x0c, 0x29, 0x63, 0x63, 0xa5, 0xc6, 0x34, 0xa2, 0x99, 0x42, 0x11, 0xcc, 0xb8, 0x08,
	0x14, 0x4a, 0xe5, 0x65, 0x82, 0x2b, 0x4e, 0x9e, 0x68, 0x98, 0x67, 0x61, 0x9e, 0x52, 0xde, 0xbb,
	0x30, 0xd7, 0x83, 0x47, 0x47, 0x54, 0x85, 0x97, 0x6f, 0x50, 0xf9, 0x06, 0xe5, 0xe3, 0x35, 0x79,
	0x0c, 0xad, 0x45, 0x1c, 0x05, 0x2c, 0x96, 0x6a, 0xe8, 0xec, 0x55, 0xf7, 0x7b, 0x7e, 0x73, 0x11,
	0x47, 0xaf, 0x62, 0xa9, 0xdc, 0xcf, 0xef, 0xe1, 0x65, 0x46, 0x08, 0xd4, 0x42, 0x1e, 0xe1, 0xd0,
	0xd9, 0x73, 0xf6, 0x7b, 0xbe, 0x59, 0x93, 0x01, 0x54, 0x13, 0x39, 0x1f, 0x56, 0xf6, 0x9c, 0xfd,
	0xb6, 0xaf, 0x97, 0xee, 0x9f, 0x0e, 0xf4, 0xcf, 0x92, 0xec, 0x14, 0x85, 0xe4, 0x29, 0x3d, 0x51,
	0x98, 0x68, 0xd0, 0x22, 0x8e, 0x72, 0x9e, 0x5e, 0x92, 0x8f, 0xa0, 0x17, 0x21, 0x8b, 0x97, 0x28,
	0x82, 0x90, 0x2f, 0x52, 0x65, 0x1c, 0xf4, 0xfc, 0x6e, 0x7e, 0x38, 0xd5, 0x67, 0xe4, 0x09, 0x74,
	0x30, 0x55, 0xb7, 0x90, 0xaa, 0x81, 0x80, 0x39, 0xb2, 0x80, 0x0f, 0xa1, 0x3b, 0xe3, 0x8c, 0xf1,
	0xb7, 0x39, 0xa2, 0x66, 0x10, 0x1d, 0x7b, 0x66, 0x21, 0x8f, 0xa1, 0x15, 0x27, 0xb9, 0xb9, 0x6e,
	0xcc, 0xcd, 0x38, 0xb1, 0xa6, 0x0f, 0x00, 0x32, 0x2e, 0x55, 0x6e, 0x6c, 0x18, 0x63, 0x5b, 0x9f,
	0x18, 0xb3, 0x7b, 0x09, 0x64, 0x25, 0x41, 0x9e, 0x8b, 0xd6, 0xcc, 0x87, 0x6e, 0x66, 0x77, 0x6b,
	0xdd, 0x3a, 0x93, 0xb1, 0xb7, 0xa5, 0x00, 0xde, 0xa6, 0x22, 0x7e, 0x27, 0x77, 0x62, 0xc4, 0x3e,
	0xb8, 0xff, 0xa5, 0xc2, 0x6a, 0x4f, 0x60, 0x77, 0x4a, 0x19, 0x3b, 0x65, 0xf4, 0x26, 0xa1, 0x0a,
	0x8f, 0x53, 0x25, 0x68, 0x1a, 0xa2, 0x0e, 0x75, 0x17, 0x9a, 0x02, 0xaf, 0x83, 0xb5, 0xf2, 0x0d,
	0x81, 0xd7, 0xe7, 0x71, 0xe4, 0xfe, 0x5e, 0x79, 0x80, 0x54, 0xf4, 0xab, 0xe4, 0x0b, 0xa8, 0xcf,
	0xe2, 0x94, 0xb2, 0x61, 0xd5, 0xa4, 0xff, 0x74, 0x6b, 0xfa, 0x26, 0x69, 0xcb, 0x21, 0xdf, 0x41,
	0xef, 0x02, 0x67, 0x5c, 0x60, 0x20, 0x50, 0xd0, 0xf4, 0x6a, 0x58, 0x2b, 0xe3, 0xa4, 0x6b, 0xb9,
	0xbe, 0xa1, 0x92, 0x6f, 0xa1, 0x4b, 0x67, 0x1a, 0x94, 0xbb, 0xaa, 0x97, 0x71, 0xd5, 0x31, 0x54,
	0xeb, 0xc9, 0x75, 0x01, 0x4e, 0x05, 0x9f, 0xc5, 0xcc, 0x68, 0xb7, 0x03, 0x75, 0x19, 0x62, 0x6a,
	0x75, 0x68, 0xfb, 0x76, 0xe3, 0xfe, 0xe1, 0x40, 0xcd, 0x34, 0xf4, 0x2e, 0x34, 0x63, 0x85, 0x49,
	0xb0, 0x96, 0x56, 0x6f, 0x4f, 0x22, 0xf2, 0x35, 0x54, 0x79, 0xa6, 0xbb, 0x59, 0x87, 0xe1, 0x15,
	0x0a, 0xc3, 0xfb, 0x21, 0x53, 0xba, 0x02, 0x37, 0xbe, 0xa6, 0x8e, 0x3e, 0x83, 0xd6, 0xea, 0x40,
	0x0b, 0x7f, 0x85, 0x37, 0x79, 0x0c, 0x7a, 0xa9, 0xe3, 0x5a, 0x52, 0xb6, 0xc0, 0xbc, 0x18, 0x76,
	0x73, 0x50, 0x79, 0xe1, 0xb8, 0xbf, 0x55, 0xd6, 0x09, 0x14, 0xae, 0xe3, 0x11, 0xb4, 0x4d, 0x1e,
	0xa6, 0x95, 0x4b, 0xd5, 0xb2, 0xa5, 0x79, 0xba, 0x7b, 0xc9, 0x8f, 0x00, 0x73, 0xc6, 0x2f, 0x28,
	0x0b, 0x74, 0xe6, 0xb6, 0x96, 0x07, 0x5b, 0x9d, 0xac, 0x43, 0xf5, 0xbe, 0x31, 0xec, 0x5b, 0x15,
	0xda, 0xf3, 0xd5, 0x7e, 0xf4, 0x25, 0xf4, 0x37, 0x8d, 0xa5, 0x14, 0x39, 0xcb, 0xaf, 0xd5, 0x6b,
	0x7a, 0x85, 0xc7, 0x4b, 0x4c, 0x95, 0xd4, 0x95, 0xfd, 0x0a, 0x1a, 0x68, 0x36, 0xf9, 0xd5, 0xfd,
	0x78, 0x6b, 0xa8, 0x86, 0xeb, 0xe7, 0xac, 0xdb, 0xcb, 0x7a, 0xc7, 0x6b, 0xe1, 0xcb, 0xfa, 0x97,
	0x03, 0x75, 0xc3, 0x21, 0x53, 0x68, 0x19, 0x7f, 0xab, 0x0e, 0xea, 0x4f, 0xf6, 0x8b, 0xc5, 0x71,
	0x12, 0xf9, 0x4d, 0xb4, 0x8b, 0xd5, 0xb3, 0x5a, 0x59, 0x3f, 0xab, 0x77, 0xfa, 0xb2, 0xba, 0xd1,
	0x97, 0x87, 0xb6, 0x2f, 0x6b, 0x05, 0x5f, 0x2b, 0xf3, 0xa9, 0xff, 0xa7, 0x31, 0x3f, 0x09, 0xa1,
	0x99, 0x47, 0x4e, 0x00, 0x1a, 0xe7, 0xe9, 0xf7, 0x29, 0x7f, 0x3b, 0x78, 0x8f, 0x74, 0xa1, 0xe5,
	0xe3, 0x3c, 0x96, 0x0a, 0xc5, 0xc0, 0x21, 0x6d, 0xa8, 0xbf, 0xe2, 0xf3, 0x38, 0x1d, 0x54, 0x48,
	0x1f, 0xe0, 0xa5, 0x79, 0xc0, 0xcf, 0x25, 0x8a, 0x41, 0x95, 0x34, 0xa0, 0x72, 0xf2, 0x7a, 0x50,
	0x23, 0x2d, 0xa8, 0x9d, 0x72, 0xa9, 0x06, 0x75, 0xf2, 0x08, 0x7a, 0xc7, 0x66, 0x2e, 0x4c, 0x2f,
	0x69, 0x9a, 0x22, 0x1b, 0x34, 0x26, 0x7f, 0xd7, 0x60, 0xe7, 0xd0, 0x66, 0xf1, 0x92, 0x8b, 0x33,
	0x94, 0xea, 0x0d, 0x8a, 0x25, 0x0a, 0xf2, 0x0b, 0xbc, 0xff, 0x4e, 0xb9, 0xc8, 0xf3, 0xad, 0xe9,
	0xdf, 0x6f, 0x9b, 0x51, 0x79, 0x92, 0xcc, 0xc8, 0xcf, 0xd0, 0xdf, 0x9c, 0xa2, 0x64, 0x52, 0xcc,
	0xcd, 0xdd, 0x31, 0x3d, 0x2a, 0xcd, 0x91, 0xd9, 0x6d, 0xda, 0xeb, 0x91, 0x52, 0x34, 0xed, 0x8d,
	0x71, 0x37, 0x2a, 0x4f, 0x92, 0x19, 0x09, 0xa1, 0x99, 0x5f, 0x6f, 0xf2, 0xac, 0xf0, 0x43, 0x80,
	0xd7, 0xa3, 0x67, 0x25, 0x5e, 0x0d, 0xf2, 0xab, 0x03, 0x3b, 0xff, 0x36, 0xc4, 0xc8, 0x8b, 0xad,
	0x5e, 0x1e, 0x18, 0x98, 0xa3, 0xff, 0xc8, 0x94, 0xd9, 0xd1, 0xf4, 0xa7, 0xc3, 0x39, 0x67, 0x34,
	0x9d, 0x7b, 0x9f, 0x4e, 0x94, 0xf2, 0x42, 0x9e, 0x8c, 0xcd, 0x8f, 0x59, 0xc8, 0xd9, 0x58, 0xa2,
	0x58, 0xc6, 0x21, 0xca, 0xf1, 0x96, 0x5f, 0xb9, 0x8b, 0x86, 0xa1, 0x3c, 0xff, 0x27, 0x00, 0x00,
	0xff, 0xff, 0xb4, 0x89, 0x9f, 0x89, 0xf4, 0x09, 0x00, 0x00,
}
