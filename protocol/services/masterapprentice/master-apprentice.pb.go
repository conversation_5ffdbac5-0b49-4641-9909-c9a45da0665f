// Code generated by protoc-gen-go. DO NOT EDIT.
// source: master-apprentice/master-apprentice.proto

package masterapprentice // import "golang.52tt.com/protocol/services/masterapprentice"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 收徒状态
type ApprenticeStatus int32

const (
	ApprenticeStatus_INVITING    ApprenticeStatus = 0
	ApprenticeStatus_IN_PROCESS  ApprenticeStatus = 1
	ApprenticeStatus_COMPLETED   ApprenticeStatus = 2
	ApprenticeStatus_TERMINATION ApprenticeStatus = 3
)

var ApprenticeStatus_name = map[int32]string{
	0: "INVITING",
	1: "IN_PROCESS",
	2: "COMPLETED",
	3: "TERMINATION",
}
var ApprenticeStatus_value = map[string]int32{
	"INVITING":    0,
	"IN_PROCESS":  1,
	"COMPLETED":   2,
	"TERMINATION": 3,
}

func (x ApprenticeStatus) String() string {
	return proto.EnumName(ApprenticeStatus_name, int32(x))
}
func (ApprenticeStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{0}
}

type HistoryTaskType int32

const (
	HistoryTaskType_MissionFinish  HistoryTaskType = 0
	HistoryTaskType_MissionUnvalid HistoryTaskType = 1
)

var HistoryTaskType_name = map[int32]string{
	0: "MissionFinish",
	1: "MissionUnvalid",
}
var HistoryTaskType_value = map[string]int32{
	"MissionFinish":  0,
	"MissionUnvalid": 1,
}

func (x HistoryTaskType) String() string {
	return proto.EnumName(HistoryTaskType_name, int32(x))
}
func (HistoryTaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{1}
}

// 返回注册时间大于等于begin且生日小于等于最小生日的徒弟
type BatchGetOnlineReserveApprenticeReq struct {
	Begin                uint32   `protobuf:"varint,1,opt,name=begin,proto3" json:"begin,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	MinBirthday          uint32   `protobuf:"varint,3,opt,name=min_birthday,json=minBirthday,proto3" json:"min_birthday,omitempty"`
	Uid                  uint32   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetOnlineReserveApprenticeReq) Reset()         { *m = BatchGetOnlineReserveApprenticeReq{} }
func (m *BatchGetOnlineReserveApprenticeReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetOnlineReserveApprenticeReq) ProtoMessage()    {}
func (*BatchGetOnlineReserveApprenticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{0}
}
func (m *BatchGetOnlineReserveApprenticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOnlineReserveApprenticeReq.Unmarshal(m, b)
}
func (m *BatchGetOnlineReserveApprenticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOnlineReserveApprenticeReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetOnlineReserveApprenticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOnlineReserveApprenticeReq.Merge(dst, src)
}
func (m *BatchGetOnlineReserveApprenticeReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetOnlineReserveApprenticeReq.Size(m)
}
func (m *BatchGetOnlineReserveApprenticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOnlineReserveApprenticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOnlineReserveApprenticeReq proto.InternalMessageInfo

func (m *BatchGetOnlineReserveApprenticeReq) GetBegin() uint32 {
	if m != nil {
		return m.Begin
	}
	return 0
}

func (m *BatchGetOnlineReserveApprenticeReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *BatchGetOnlineReserveApprenticeReq) GetMinBirthday() uint32 {
	if m != nil {
		return m.MinBirthday
	}
	return 0
}

func (m *BatchGetOnlineReserveApprenticeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type BatchGetOnlineReserveApprenticeResp struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetOnlineReserveApprenticeResp) Reset()         { *m = BatchGetOnlineReserveApprenticeResp{} }
func (m *BatchGetOnlineReserveApprenticeResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetOnlineReserveApprenticeResp) ProtoMessage()    {}
func (*BatchGetOnlineReserveApprenticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{1}
}
func (m *BatchGetOnlineReserveApprenticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetOnlineReserveApprenticeResp.Unmarshal(m, b)
}
func (m *BatchGetOnlineReserveApprenticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetOnlineReserveApprenticeResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetOnlineReserveApprenticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetOnlineReserveApprenticeResp.Merge(dst, src)
}
func (m *BatchGetOnlineReserveApprenticeResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetOnlineReserveApprenticeResp.Size(m)
}
func (m *BatchGetOnlineReserveApprenticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetOnlineReserveApprenticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetOnlineReserveApprenticeResp proto.InternalMessageInfo

func (m *BatchGetOnlineReserveApprenticeResp) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchDelReserveApprenticeReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelReserveApprenticeReq) Reset()         { *m = BatchDelReserveApprenticeReq{} }
func (m *BatchDelReserveApprenticeReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelReserveApprenticeReq) ProtoMessage()    {}
func (*BatchDelReserveApprenticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{2}
}
func (m *BatchDelReserveApprenticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelReserveApprenticeReq.Unmarshal(m, b)
}
func (m *BatchDelReserveApprenticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelReserveApprenticeReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelReserveApprenticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelReserveApprenticeReq.Merge(dst, src)
}
func (m *BatchDelReserveApprenticeReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelReserveApprenticeReq.Size(m)
}
func (m *BatchDelReserveApprenticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelReserveApprenticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelReserveApprenticeReq proto.InternalMessageInfo

func (m *BatchDelReserveApprenticeReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchDelReserveApprenticeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelReserveApprenticeResp) Reset()         { *m = BatchDelReserveApprenticeResp{} }
func (m *BatchDelReserveApprenticeResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelReserveApprenticeResp) ProtoMessage()    {}
func (*BatchDelReserveApprenticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{3}
}
func (m *BatchDelReserveApprenticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelReserveApprenticeResp.Unmarshal(m, b)
}
func (m *BatchDelReserveApprenticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelReserveApprenticeResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelReserveApprenticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelReserveApprenticeResp.Merge(dst, src)
}
func (m *BatchDelReserveApprenticeResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelReserveApprenticeResp.Size(m)
}
func (m *BatchDelReserveApprenticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelReserveApprenticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelReserveApprenticeResp proto.InternalMessageInfo

type BatchAddMasterReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddMasterReq) Reset()         { *m = BatchAddMasterReq{} }
func (m *BatchAddMasterReq) String() string { return proto.CompactTextString(m) }
func (*BatchAddMasterReq) ProtoMessage()    {}
func (*BatchAddMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{4}
}
func (m *BatchAddMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddMasterReq.Unmarshal(m, b)
}
func (m *BatchAddMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddMasterReq.Marshal(b, m, deterministic)
}
func (dst *BatchAddMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddMasterReq.Merge(dst, src)
}
func (m *BatchAddMasterReq) XXX_Size() int {
	return xxx_messageInfo_BatchAddMasterReq.Size(m)
}
func (m *BatchAddMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddMasterReq proto.InternalMessageInfo

func (m *BatchAddMasterReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchAddMasterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddMasterResp) Reset()         { *m = BatchAddMasterResp{} }
func (m *BatchAddMasterResp) String() string { return proto.CompactTextString(m) }
func (*BatchAddMasterResp) ProtoMessage()    {}
func (*BatchAddMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{5}
}
func (m *BatchAddMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddMasterResp.Unmarshal(m, b)
}
func (m *BatchAddMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddMasterResp.Marshal(b, m, deterministic)
}
func (dst *BatchAddMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddMasterResp.Merge(dst, src)
}
func (m *BatchAddMasterResp) XXX_Size() int {
	return xxx_messageInfo_BatchAddMasterResp.Size(m)
}
func (m *BatchAddMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddMasterResp proto.InternalMessageInfo

type BatchDelMasterReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelMasterReq) Reset()         { *m = BatchDelMasterReq{} }
func (m *BatchDelMasterReq) String() string { return proto.CompactTextString(m) }
func (*BatchDelMasterReq) ProtoMessage()    {}
func (*BatchDelMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{6}
}
func (m *BatchDelMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMasterReq.Unmarshal(m, b)
}
func (m *BatchDelMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMasterReq.Marshal(b, m, deterministic)
}
func (dst *BatchDelMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMasterReq.Merge(dst, src)
}
func (m *BatchDelMasterReq) XXX_Size() int {
	return xxx_messageInfo_BatchDelMasterReq.Size(m)
}
func (m *BatchDelMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMasterReq proto.InternalMessageInfo

func (m *BatchDelMasterReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchDelMasterResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelMasterResp) Reset()         { *m = BatchDelMasterResp{} }
func (m *BatchDelMasterResp) String() string { return proto.CompactTextString(m) }
func (*BatchDelMasterResp) ProtoMessage()    {}
func (*BatchDelMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{7}
}
func (m *BatchDelMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelMasterResp.Unmarshal(m, b)
}
func (m *BatchDelMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelMasterResp.Marshal(b, m, deterministic)
}
func (dst *BatchDelMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelMasterResp.Merge(dst, src)
}
func (m *BatchDelMasterResp) XXX_Size() int {
	return xxx_messageInfo_BatchDelMasterResp.Size(m)
}
func (m *BatchDelMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelMasterResp proto.InternalMessageInfo

type IsApprenticeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsApprenticeReq) Reset()         { *m = IsApprenticeReq{} }
func (m *IsApprenticeReq) String() string { return proto.CompactTextString(m) }
func (*IsApprenticeReq) ProtoMessage()    {}
func (*IsApprenticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{8}
}
func (m *IsApprenticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsApprenticeReq.Unmarshal(m, b)
}
func (m *IsApprenticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsApprenticeReq.Marshal(b, m, deterministic)
}
func (dst *IsApprenticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsApprenticeReq.Merge(dst, src)
}
func (m *IsApprenticeReq) XXX_Size() int {
	return xxx_messageInfo_IsApprenticeReq.Size(m)
}
func (m *IsApprenticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsApprenticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsApprenticeReq proto.InternalMessageInfo

func (m *IsApprenticeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsApprenticeResp struct {
	IsApprentice         bool     `protobuf:"varint,1,opt,name=is_apprentice,json=isApprentice,proto3" json:"is_apprentice,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsApprenticeResp) Reset()         { *m = IsApprenticeResp{} }
func (m *IsApprenticeResp) String() string { return proto.CompactTextString(m) }
func (*IsApprenticeResp) ProtoMessage()    {}
func (*IsApprenticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{9}
}
func (m *IsApprenticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsApprenticeResp.Unmarshal(m, b)
}
func (m *IsApprenticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsApprenticeResp.Marshal(b, m, deterministic)
}
func (dst *IsApprenticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsApprenticeResp.Merge(dst, src)
}
func (m *IsApprenticeResp) XXX_Size() int {
	return xxx_messageInfo_IsApprenticeResp.Size(m)
}
func (m *IsApprenticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsApprenticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsApprenticeResp proto.InternalMessageInfo

func (m *IsApprenticeResp) GetIsApprentice() bool {
	if m != nil {
		return m.IsApprentice
	}
	return false
}

type IsMasterReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterReq) Reset()         { *m = IsMasterReq{} }
func (m *IsMasterReq) String() string { return proto.CompactTextString(m) }
func (*IsMasterReq) ProtoMessage()    {}
func (*IsMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{10}
}
func (m *IsMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterReq.Unmarshal(m, b)
}
func (m *IsMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterReq.Marshal(b, m, deterministic)
}
func (dst *IsMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterReq.Merge(dst, src)
}
func (m *IsMasterReq) XXX_Size() int {
	return xxx_messageInfo_IsMasterReq.Size(m)
}
func (m *IsMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterReq proto.InternalMessageInfo

func (m *IsMasterReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsMasterResp struct {
	IsMaster             bool     `protobuf:"varint,1,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterResp) Reset()         { *m = IsMasterResp{} }
func (m *IsMasterResp) String() string { return proto.CompactTextString(m) }
func (*IsMasterResp) ProtoMessage()    {}
func (*IsMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{11}
}
func (m *IsMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterResp.Unmarshal(m, b)
}
func (m *IsMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterResp.Marshal(b, m, deterministic)
}
func (dst *IsMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterResp.Merge(dst, src)
}
func (m *IsMasterResp) XXX_Size() int {
	return xxx_messageInfo_IsMasterResp.Size(m)
}
func (m *IsMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterResp proto.InternalMessageInfo

func (m *IsMasterResp) GetIsMaster() bool {
	if m != nil {
		return m.IsMaster
	}
	return false
}

// 是师父&在收徒时间内
type IsMasterInValidTimeReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterInValidTimeReq) Reset()         { *m = IsMasterInValidTimeReq{} }
func (m *IsMasterInValidTimeReq) String() string { return proto.CompactTextString(m) }
func (*IsMasterInValidTimeReq) ProtoMessage()    {}
func (*IsMasterInValidTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{12}
}
func (m *IsMasterInValidTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterInValidTimeReq.Unmarshal(m, b)
}
func (m *IsMasterInValidTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterInValidTimeReq.Marshal(b, m, deterministic)
}
func (dst *IsMasterInValidTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterInValidTimeReq.Merge(dst, src)
}
func (m *IsMasterInValidTimeReq) XXX_Size() int {
	return xxx_messageInfo_IsMasterInValidTimeReq.Size(m)
}
func (m *IsMasterInValidTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterInValidTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterInValidTimeReq proto.InternalMessageInfo

func (m *IsMasterInValidTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type IsMasterInValidTimeResp struct {
	IsMaster             bool     `protobuf:"varint,1,opt,name=is_master,json=isMaster,proto3" json:"is_master,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsMasterInValidTimeResp) Reset()         { *m = IsMasterInValidTimeResp{} }
func (m *IsMasterInValidTimeResp) String() string { return proto.CompactTextString(m) }
func (*IsMasterInValidTimeResp) ProtoMessage()    {}
func (*IsMasterInValidTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{13}
}
func (m *IsMasterInValidTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsMasterInValidTimeResp.Unmarshal(m, b)
}
func (m *IsMasterInValidTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsMasterInValidTimeResp.Marshal(b, m, deterministic)
}
func (dst *IsMasterInValidTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsMasterInValidTimeResp.Merge(dst, src)
}
func (m *IsMasterInValidTimeResp) XXX_Size() int {
	return xxx_messageInfo_IsMasterInValidTimeResp.Size(m)
}
func (m *IsMasterInValidTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsMasterInValidTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsMasterInValidTimeResp proto.InternalMessageInfo

func (m *IsMasterInValidTimeResp) GetIsMaster() bool {
	if m != nil {
		return m.IsMaster
	}
	return false
}

// 获取师父的徒弟列表 进行中的
type GetApprenticesListReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApprenticesListReq) Reset()         { *m = GetApprenticesListReq{} }
func (m *GetApprenticesListReq) String() string { return proto.CompactTextString(m) }
func (*GetApprenticesListReq) ProtoMessage()    {}
func (*GetApprenticesListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{14}
}
func (m *GetApprenticesListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApprenticesListReq.Unmarshal(m, b)
}
func (m *GetApprenticesListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApprenticesListReq.Marshal(b, m, deterministic)
}
func (dst *GetApprenticesListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApprenticesListReq.Merge(dst, src)
}
func (m *GetApprenticesListReq) XXX_Size() int {
	return xxx_messageInfo_GetApprenticesListReq.Size(m)
}
func (m *GetApprenticesListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApprenticesListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApprenticesListReq proto.InternalMessageInfo

func (m *GetApprenticesListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetApprenticesListResp struct {
	ApprenticesUidList   []uint32 `protobuf:"varint,4,rep,packed,name=apprentices_uid_list,json=apprenticesUidList,proto3" json:"apprentices_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApprenticesListResp) Reset()         { *m = GetApprenticesListResp{} }
func (m *GetApprenticesListResp) String() string { return proto.CompactTextString(m) }
func (*GetApprenticesListResp) ProtoMessage()    {}
func (*GetApprenticesListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{15}
}
func (m *GetApprenticesListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApprenticesListResp.Unmarshal(m, b)
}
func (m *GetApprenticesListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApprenticesListResp.Marshal(b, m, deterministic)
}
func (dst *GetApprenticesListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApprenticesListResp.Merge(dst, src)
}
func (m *GetApprenticesListResp) XXX_Size() int {
	return xxx_messageInfo_GetApprenticesListResp.Size(m)
}
func (m *GetApprenticesListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApprenticesListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApprenticesListResp proto.InternalMessageInfo

func (m *GetApprenticesListResp) GetApprenticesUidList() []uint32 {
	if m != nil {
		return m.ApprenticesUidList
	}
	return nil
}

// 获取徒弟状态
type GetApprenticeStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ApprenticeUid        uint32   `protobuf:"varint,2,opt,name=apprentice_uid,json=apprenticeUid,proto3" json:"apprentice_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApprenticeStatusReq) Reset()         { *m = GetApprenticeStatusReq{} }
func (m *GetApprenticeStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetApprenticeStatusReq) ProtoMessage()    {}
func (*GetApprenticeStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{16}
}
func (m *GetApprenticeStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApprenticeStatusReq.Unmarshal(m, b)
}
func (m *GetApprenticeStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApprenticeStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetApprenticeStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApprenticeStatusReq.Merge(dst, src)
}
func (m *GetApprenticeStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetApprenticeStatusReq.Size(m)
}
func (m *GetApprenticeStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApprenticeStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApprenticeStatusReq proto.InternalMessageInfo

func (m *GetApprenticeStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetApprenticeStatusReq) GetApprenticeUid() uint32 {
	if m != nil {
		return m.ApprenticeUid
	}
	return 0
}

type GetApprenticeStatusResp struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ApprenticeUid        uint32           `protobuf:"varint,2,opt,name=apprentice_uid,json=apprenticeUid,proto3" json:"apprentice_uid,omitempty"`
	Status               ApprenticeStatus `protobuf:"varint,3,opt,name=status,proto3,enum=masterapprentice.ApprenticeStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetApprenticeStatusResp) Reset()         { *m = GetApprenticeStatusResp{} }
func (m *GetApprenticeStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetApprenticeStatusResp) ProtoMessage()    {}
func (*GetApprenticeStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{17}
}
func (m *GetApprenticeStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApprenticeStatusResp.Unmarshal(m, b)
}
func (m *GetApprenticeStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApprenticeStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetApprenticeStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApprenticeStatusResp.Merge(dst, src)
}
func (m *GetApprenticeStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetApprenticeStatusResp.Size(m)
}
func (m *GetApprenticeStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApprenticeStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApprenticeStatusResp proto.InternalMessageInfo

func (m *GetApprenticeStatusResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetApprenticeStatusResp) GetApprenticeUid() uint32 {
	if m != nil {
		return m.ApprenticeUid
	}
	return 0
}

func (m *GetApprenticeStatusResp) GetStatus() ApprenticeStatus {
	if m != nil {
		return m.Status
	}
	return ApprenticeStatus_INVITING
}

// 获取非邀请中的师父信息
type GetUserMasterInfoReq struct {
	ApprenticeUid        uint32   `protobuf:"varint,1,opt,name=apprentice_uid,json=apprenticeUid,proto3" json:"apprentice_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMasterInfoReq) Reset()         { *m = GetUserMasterInfoReq{} }
func (m *GetUserMasterInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMasterInfoReq) ProtoMessage()    {}
func (*GetUserMasterInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{18}
}
func (m *GetUserMasterInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMasterInfoReq.Unmarshal(m, b)
}
func (m *GetUserMasterInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMasterInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserMasterInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMasterInfoReq.Merge(dst, src)
}
func (m *GetUserMasterInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserMasterInfoReq.Size(m)
}
func (m *GetUserMasterInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMasterInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMasterInfoReq proto.InternalMessageInfo

func (m *GetUserMasterInfoReq) GetApprenticeUid() uint32 {
	if m != nil {
		return m.ApprenticeUid
	}
	return 0
}

type GetUserMasterInfoResp struct {
	MasterUid            uint32   `protobuf:"varint,2,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMasterInfoResp) Reset()         { *m = GetUserMasterInfoResp{} }
func (m *GetUserMasterInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMasterInfoResp) ProtoMessage()    {}
func (*GetUserMasterInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{19}
}
func (m *GetUserMasterInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMasterInfoResp.Unmarshal(m, b)
}
func (m *GetUserMasterInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMasterInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserMasterInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMasterInfoResp.Merge(dst, src)
}
func (m *GetUserMasterInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserMasterInfoResp.Size(m)
}
func (m *GetUserMasterInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMasterInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMasterInfoResp proto.InternalMessageInfo

func (m *GetUserMasterInfoResp) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

// 批量获取非邀请中的师父信息
type BatchGetUserMasterInfoReq struct {
	ApprenticeUids       []uint32 `protobuf:"varint,1,rep,packed,name=apprentice_uids,json=apprenticeUids,proto3" json:"apprentice_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserMasterInfoReq) Reset()         { *m = BatchGetUserMasterInfoReq{} }
func (m *BatchGetUserMasterInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserMasterInfoReq) ProtoMessage()    {}
func (*BatchGetUserMasterInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{20}
}
func (m *BatchGetUserMasterInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserMasterInfoReq.Unmarshal(m, b)
}
func (m *BatchGetUserMasterInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserMasterInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserMasterInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserMasterInfoReq.Merge(dst, src)
}
func (m *BatchGetUserMasterInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserMasterInfoReq.Size(m)
}
func (m *BatchGetUserMasterInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserMasterInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserMasterInfoReq proto.InternalMessageInfo

func (m *BatchGetUserMasterInfoReq) GetApprenticeUids() []uint32 {
	if m != nil {
		return m.ApprenticeUids
	}
	return nil
}

type BatchGetUserMasterInfoResp struct {
	ApprenticeToMaster   map[uint32]uint32 `protobuf:"bytes,2,rep,name=apprentice_to_master,json=apprenticeToMaster,proto3" json:"apprentice_to_master,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetUserMasterInfoResp) Reset()         { *m = BatchGetUserMasterInfoResp{} }
func (m *BatchGetUserMasterInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserMasterInfoResp) ProtoMessage()    {}
func (*BatchGetUserMasterInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{21}
}
func (m *BatchGetUserMasterInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserMasterInfoResp.Unmarshal(m, b)
}
func (m *BatchGetUserMasterInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserMasterInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserMasterInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserMasterInfoResp.Merge(dst, src)
}
func (m *BatchGetUserMasterInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserMasterInfoResp.Size(m)
}
func (m *BatchGetUserMasterInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserMasterInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserMasterInfoResp proto.InternalMessageInfo

func (m *BatchGetUserMasterInfoResp) GetApprenticeToMaster() map[uint32]uint32 {
	if m != nil {
		return m.ApprenticeToMaster
	}
	return nil
}

// 发出邀请
type MasterInviteReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	ApprenticeUid        uint32   `protobuf:"varint,2,opt,name=apprentice_uid,json=apprenticeUid,proto3" json:"apprentice_uid,omitempty"`
	ApprenticeNickname   string   `protobuf:"bytes,3,opt,name=apprentice_nickname,json=apprenticeNickname,proto3" json:"apprentice_nickname,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterInviteReq) Reset()         { *m = MasterInviteReq{} }
func (m *MasterInviteReq) String() string { return proto.CompactTextString(m) }
func (*MasterInviteReq) ProtoMessage()    {}
func (*MasterInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{22}
}
func (m *MasterInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterInviteReq.Unmarshal(m, b)
}
func (m *MasterInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterInviteReq.Marshal(b, m, deterministic)
}
func (dst *MasterInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterInviteReq.Merge(dst, src)
}
func (m *MasterInviteReq) XXX_Size() int {
	return xxx_messageInfo_MasterInviteReq.Size(m)
}
func (m *MasterInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_MasterInviteReq proto.InternalMessageInfo

func (m *MasterInviteReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *MasterInviteReq) GetApprenticeUid() uint32 {
	if m != nil {
		return m.ApprenticeUid
	}
	return 0
}

func (m *MasterInviteReq) GetApprenticeNickname() string {
	if m != nil {
		return m.ApprenticeNickname
	}
	return ""
}

type MasterInviteResp struct {
	InviteCnt            uint32   `protobuf:"varint,1,opt,name=invite_cnt,json=inviteCnt,proto3" json:"invite_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterInviteResp) Reset()         { *m = MasterInviteResp{} }
func (m *MasterInviteResp) String() string { return proto.CompactTextString(m) }
func (*MasterInviteResp) ProtoMessage()    {}
func (*MasterInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{23}
}
func (m *MasterInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterInviteResp.Unmarshal(m, b)
}
func (m *MasterInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterInviteResp.Marshal(b, m, deterministic)
}
func (dst *MasterInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterInviteResp.Merge(dst, src)
}
func (m *MasterInviteResp) XXX_Size() int {
	return xxx_messageInfo_MasterInviteResp.Size(m)
}
func (m *MasterInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_MasterInviteResp proto.InternalMessageInfo

func (m *MasterInviteResp) GetInviteCnt() uint32 {
	if m != nil {
		return m.InviteCnt
	}
	return 0
}

// 建立师徒关系 -- 徒弟点击接受邀请
type EstablishShipReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	ApprenticeUid        uint32   `protobuf:"varint,2,opt,name=apprentice_uid,json=apprenticeUid,proto3" json:"apprentice_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EstablishShipReq) Reset()         { *m = EstablishShipReq{} }
func (m *EstablishShipReq) String() string { return proto.CompactTextString(m) }
func (*EstablishShipReq) ProtoMessage()    {}
func (*EstablishShipReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{24}
}
func (m *EstablishShipReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EstablishShipReq.Unmarshal(m, b)
}
func (m *EstablishShipReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EstablishShipReq.Marshal(b, m, deterministic)
}
func (dst *EstablishShipReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstablishShipReq.Merge(dst, src)
}
func (m *EstablishShipReq) XXX_Size() int {
	return xxx_messageInfo_EstablishShipReq.Size(m)
}
func (m *EstablishShipReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EstablishShipReq.DiscardUnknown(m)
}

var xxx_messageInfo_EstablishShipReq proto.InternalMessageInfo

func (m *EstablishShipReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *EstablishShipReq) GetApprenticeUid() uint32 {
	if m != nil {
		return m.ApprenticeUid
	}
	return 0
}

type EstablishShipResp struct {
	Result               bool     `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EstablishShipResp) Reset()         { *m = EstablishShipResp{} }
func (m *EstablishShipResp) String() string { return proto.CompactTextString(m) }
func (*EstablishShipResp) ProtoMessage()    {}
func (*EstablishShipResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{25}
}
func (m *EstablishShipResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EstablishShipResp.Unmarshal(m, b)
}
func (m *EstablishShipResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EstablishShipResp.Marshal(b, m, deterministic)
}
func (dst *EstablishShipResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EstablishShipResp.Merge(dst, src)
}
func (m *EstablishShipResp) XXX_Size() int {
	return xxx_messageInfo_EstablishShipResp.Size(m)
}
func (m *EstablishShipResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EstablishShipResp.DiscardUnknown(m)
}

var xxx_messageInfo_EstablishShipResp proto.InternalMessageInfo

func (m *EstablishShipResp) GetResult() bool {
	if m != nil {
		return m.Result
	}
	return false
}

// 获取师父的绑定的徒弟数(进行中)
type GetApprenticeNumByMasterReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApprenticeNumByMasterReq) Reset()         { *m = GetApprenticeNumByMasterReq{} }
func (m *GetApprenticeNumByMasterReq) String() string { return proto.CompactTextString(m) }
func (*GetApprenticeNumByMasterReq) ProtoMessage()    {}
func (*GetApprenticeNumByMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{26}
}
func (m *GetApprenticeNumByMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApprenticeNumByMasterReq.Unmarshal(m, b)
}
func (m *GetApprenticeNumByMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApprenticeNumByMasterReq.Marshal(b, m, deterministic)
}
func (dst *GetApprenticeNumByMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApprenticeNumByMasterReq.Merge(dst, src)
}
func (m *GetApprenticeNumByMasterReq) XXX_Size() int {
	return xxx_messageInfo_GetApprenticeNumByMasterReq.Size(m)
}
func (m *GetApprenticeNumByMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApprenticeNumByMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApprenticeNumByMasterReq proto.InternalMessageInfo

func (m *GetApprenticeNumByMasterReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type GetApprenticeNumByMasterResp struct {
	Cnt                  uint32   `protobuf:"varint,2,opt,name=cnt,proto3" json:"cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApprenticeNumByMasterResp) Reset()         { *m = GetApprenticeNumByMasterResp{} }
func (m *GetApprenticeNumByMasterResp) String() string { return proto.CompactTextString(m) }
func (*GetApprenticeNumByMasterResp) ProtoMessage()    {}
func (*GetApprenticeNumByMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{27}
}
func (m *GetApprenticeNumByMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApprenticeNumByMasterResp.Unmarshal(m, b)
}
func (m *GetApprenticeNumByMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApprenticeNumByMasterResp.Marshal(b, m, deterministic)
}
func (dst *GetApprenticeNumByMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApprenticeNumByMasterResp.Merge(dst, src)
}
func (m *GetApprenticeNumByMasterResp) XXX_Size() int {
	return xxx_messageInfo_GetApprenticeNumByMasterResp.Size(m)
}
func (m *GetApprenticeNumByMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApprenticeNumByMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApprenticeNumByMasterResp proto.InternalMessageInfo

func (m *GetApprenticeNumByMasterResp) GetCnt() uint32 {
	if m != nil {
		return m.Cnt
	}
	return 0
}

// 今日已完成
type GetTodayCompletedReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTodayCompletedReq) Reset()         { *m = GetTodayCompletedReq{} }
func (m *GetTodayCompletedReq) String() string { return proto.CompactTextString(m) }
func (*GetTodayCompletedReq) ProtoMessage()    {}
func (*GetTodayCompletedReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{28}
}
func (m *GetTodayCompletedReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayCompletedReq.Unmarshal(m, b)
}
func (m *GetTodayCompletedReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayCompletedReq.Marshal(b, m, deterministic)
}
func (dst *GetTodayCompletedReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayCompletedReq.Merge(dst, src)
}
func (m *GetTodayCompletedReq) XXX_Size() int {
	return xxx_messageInfo_GetTodayCompletedReq.Size(m)
}
func (m *GetTodayCompletedReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayCompletedReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayCompletedReq proto.InternalMessageInfo

func (m *GetTodayCompletedReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type GetTodayCompletedResp struct {
	ApprenticeUidList    []uint32 `protobuf:"varint,1,rep,packed,name=apprentice_uid_list,json=apprenticeUidList,proto3" json:"apprentice_uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTodayCompletedResp) Reset()         { *m = GetTodayCompletedResp{} }
func (m *GetTodayCompletedResp) String() string { return proto.CompactTextString(m) }
func (*GetTodayCompletedResp) ProtoMessage()    {}
func (*GetTodayCompletedResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{29}
}
func (m *GetTodayCompletedResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTodayCompletedResp.Unmarshal(m, b)
}
func (m *GetTodayCompletedResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTodayCompletedResp.Marshal(b, m, deterministic)
}
func (dst *GetTodayCompletedResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTodayCompletedResp.Merge(dst, src)
}
func (m *GetTodayCompletedResp) XXX_Size() int {
	return xxx_messageInfo_GetTodayCompletedResp.Size(m)
}
func (m *GetTodayCompletedResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTodayCompletedResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTodayCompletedResp proto.InternalMessageInfo

func (m *GetTodayCompletedResp) GetApprenticeUidList() []uint32 {
	if m != nil {
		return m.ApprenticeUidList
	}
	return nil
}

// 当天的新鲜师父:现在是领取成师礼包当天
type IsNewMasterReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNewMasterReq) Reset()         { *m = IsNewMasterReq{} }
func (m *IsNewMasterReq) String() string { return proto.CompactTextString(m) }
func (*IsNewMasterReq) ProtoMessage()    {}
func (*IsNewMasterReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{30}
}
func (m *IsNewMasterReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNewMasterReq.Unmarshal(m, b)
}
func (m *IsNewMasterReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNewMasterReq.Marshal(b, m, deterministic)
}
func (dst *IsNewMasterReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNewMasterReq.Merge(dst, src)
}
func (m *IsNewMasterReq) XXX_Size() int {
	return xxx_messageInfo_IsNewMasterReq.Size(m)
}
func (m *IsNewMasterReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNewMasterReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsNewMasterReq proto.InternalMessageInfo

func (m *IsNewMasterReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type IsNewMasterResp struct {
	IsNew                bool     `protobuf:"varint,1,opt,name=isNew,proto3" json:"isNew,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsNewMasterResp) Reset()         { *m = IsNewMasterResp{} }
func (m *IsNewMasterResp) String() string { return proto.CompactTextString(m) }
func (*IsNewMasterResp) ProtoMessage()    {}
func (*IsNewMasterResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{31}
}
func (m *IsNewMasterResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsNewMasterResp.Unmarshal(m, b)
}
func (m *IsNewMasterResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsNewMasterResp.Marshal(b, m, deterministic)
}
func (dst *IsNewMasterResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsNewMasterResp.Merge(dst, src)
}
func (m *IsNewMasterResp) XXX_Size() int {
	return xxx_messageInfo_IsNewMasterResp.Size(m)
}
func (m *IsNewMasterResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsNewMasterResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsNewMasterResp proto.InternalMessageInfo

func (m *IsNewMasterResp) GetIsNew() bool {
	if m != nil {
		return m.IsNew
	}
	return false
}

type DrawBalanceReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	DrawBalance          int64    `protobuf:"varint,2,opt,name=draw_balance,json=drawBalance,proto3" json:"draw_balance,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DrawBalanceReq) Reset()         { *m = DrawBalanceReq{} }
func (m *DrawBalanceReq) String() string { return proto.CompactTextString(m) }
func (*DrawBalanceReq) ProtoMessage()    {}
func (*DrawBalanceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{32}
}
func (m *DrawBalanceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawBalanceReq.Unmarshal(m, b)
}
func (m *DrawBalanceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawBalanceReq.Marshal(b, m, deterministic)
}
func (dst *DrawBalanceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawBalanceReq.Merge(dst, src)
}
func (m *DrawBalanceReq) XXX_Size() int {
	return xxx_messageInfo_DrawBalanceReq.Size(m)
}
func (m *DrawBalanceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawBalanceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DrawBalanceReq proto.InternalMessageInfo

func (m *DrawBalanceReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *DrawBalanceReq) GetDrawBalance() int64 {
	if m != nil {
		return m.DrawBalance
	}
	return 0
}

type DrawBalanceResp struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DrawBalanceResp) Reset()         { *m = DrawBalanceResp{} }
func (m *DrawBalanceResp) String() string { return proto.CompactTextString(m) }
func (*DrawBalanceResp) ProtoMessage()    {}
func (*DrawBalanceResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{33}
}
func (m *DrawBalanceResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DrawBalanceResp.Unmarshal(m, b)
}
func (m *DrawBalanceResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DrawBalanceResp.Marshal(b, m, deterministic)
}
func (dst *DrawBalanceResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DrawBalanceResp.Merge(dst, src)
}
func (m *DrawBalanceResp) XXX_Size() int {
	return xxx_messageInfo_DrawBalanceResp.Size(m)
}
func (m *DrawBalanceResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DrawBalanceResp.DiscardUnknown(m)
}

var xxx_messageInfo_DrawBalanceResp proto.InternalMessageInfo

func (m *DrawBalanceResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *DrawBalanceResp) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetUserBalanceByUserIDReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBalanceByUserIDReq) Reset()         { *m = GetUserBalanceByUserIDReq{} }
func (m *GetUserBalanceByUserIDReq) String() string { return proto.CompactTextString(m) }
func (*GetUserBalanceByUserIDReq) ProtoMessage()    {}
func (*GetUserBalanceByUserIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{34}
}
func (m *GetUserBalanceByUserIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBalanceByUserIDReq.Unmarshal(m, b)
}
func (m *GetUserBalanceByUserIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBalanceByUserIDReq.Marshal(b, m, deterministic)
}
func (dst *GetUserBalanceByUserIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBalanceByUserIDReq.Merge(dst, src)
}
func (m *GetUserBalanceByUserIDReq) XXX_Size() int {
	return xxx_messageInfo_GetUserBalanceByUserIDReq.Size(m)
}
func (m *GetUserBalanceByUserIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBalanceByUserIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBalanceByUserIDReq proto.InternalMessageInfo

func (m *GetUserBalanceByUserIDReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetUserBalanceByUserIDResp struct {
	TotalBalance         int64    `protobuf:"varint,1,opt,name=total_balance,json=totalBalance,proto3" json:"total_balance,omitempty"`
	AvailableBalance     int64    `protobuf:"varint,2,opt,name=available_balance,json=availableBalance,proto3" json:"available_balance,omitempty"`
	UnavailableBalance   int64    `protobuf:"varint,3,opt,name=unavailable_balance,json=unavailableBalance,proto3" json:"unavailable_balance,omitempty"`
	Status               int64    `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserBalanceByUserIDResp) Reset()         { *m = GetUserBalanceByUserIDResp{} }
func (m *GetUserBalanceByUserIDResp) String() string { return proto.CompactTextString(m) }
func (*GetUserBalanceByUserIDResp) ProtoMessage()    {}
func (*GetUserBalanceByUserIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{35}
}
func (m *GetUserBalanceByUserIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserBalanceByUserIDResp.Unmarshal(m, b)
}
func (m *GetUserBalanceByUserIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserBalanceByUserIDResp.Marshal(b, m, deterministic)
}
func (dst *GetUserBalanceByUserIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserBalanceByUserIDResp.Merge(dst, src)
}
func (m *GetUserBalanceByUserIDResp) XXX_Size() int {
	return xxx_messageInfo_GetUserBalanceByUserIDResp.Size(m)
}
func (m *GetUserBalanceByUserIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserBalanceByUserIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserBalanceByUserIDResp proto.InternalMessageInfo

func (m *GetUserBalanceByUserIDResp) GetTotalBalance() int64 {
	if m != nil {
		return m.TotalBalance
	}
	return 0
}

func (m *GetUserBalanceByUserIDResp) GetAvailableBalance() int64 {
	if m != nil {
		return m.AvailableBalance
	}
	return 0
}

func (m *GetUserBalanceByUserIDResp) GetUnavailableBalance() int64 {
	if m != nil {
		return m.UnavailableBalance
	}
	return 0
}

func (m *GetUserBalanceByUserIDResp) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

type OrderInfo struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               int64    `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	NowBalance           int64    `protobuf:"varint,4,opt,name=now_balance,json=nowBalance,proto3" json:"now_balance,omitempty"`
	Amount               int64    `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Type                 int64    `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`
	Describe             string   `protobuf:"bytes,7,opt,name=describe,proto3" json:"describe,omitempty"`
	Reason               string   `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	CreateTime           int64    `protobuf:"varint,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	UpdateTime           int64    `protobuf:"varint,10,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderInfo) Reset()         { *m = OrderInfo{} }
func (m *OrderInfo) String() string { return proto.CompactTextString(m) }
func (*OrderInfo) ProtoMessage()    {}
func (*OrderInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{36}
}
func (m *OrderInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderInfo.Unmarshal(m, b)
}
func (m *OrderInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderInfo.Marshal(b, m, deterministic)
}
func (dst *OrderInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderInfo.Merge(dst, src)
}
func (m *OrderInfo) XXX_Size() int {
	return xxx_messageInfo_OrderInfo.Size(m)
}
func (m *OrderInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OrderInfo proto.InternalMessageInfo

func (m *OrderInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *OrderInfo) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *OrderInfo) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *OrderInfo) GetNowBalance() int64 {
	if m != nil {
		return m.NowBalance
	}
	return 0
}

func (m *OrderInfo) GetAmount() int64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *OrderInfo) GetType() int64 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *OrderInfo) GetDescribe() string {
	if m != nil {
		return m.Describe
	}
	return ""
}

func (m *OrderInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *OrderInfo) GetCreateTime() int64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *OrderInfo) GetUpdateTime() int64 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

type GetOrderListByUserIDReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Limit                int64    `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	LastId               int64    `protobuf:"varint,3,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderListByUserIDReq) Reset()         { *m = GetOrderListByUserIDReq{} }
func (m *GetOrderListByUserIDReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByUserIDReq) ProtoMessage()    {}
func (*GetOrderListByUserIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{37}
}
func (m *GetOrderListByUserIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListByUserIDReq.Unmarshal(m, b)
}
func (m *GetOrderListByUserIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListByUserIDReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderListByUserIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListByUserIDReq.Merge(dst, src)
}
func (m *GetOrderListByUserIDReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderListByUserIDReq.Size(m)
}
func (m *GetOrderListByUserIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListByUserIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListByUserIDReq proto.InternalMessageInfo

func (m *GetOrderListByUserIDReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetOrderListByUserIDReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *GetOrderListByUserIDReq) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

type LoadMore struct {
	HasNext              bool     `protobuf:"varint,1,opt,name=has_next,json=hasNext,proto3" json:"has_next,omitempty"`
	LastId               int64    `protobuf:"varint,2,opt,name=last_id,json=lastId,proto3" json:"last_id,omitempty"`
	Limit                int64    `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *LoadMore) Reset()         { *m = LoadMore{} }
func (m *LoadMore) String() string { return proto.CompactTextString(m) }
func (*LoadMore) ProtoMessage()    {}
func (*LoadMore) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{38}
}
func (m *LoadMore) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoadMore.Unmarshal(m, b)
}
func (m *LoadMore) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoadMore.Marshal(b, m, deterministic)
}
func (dst *LoadMore) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoadMore.Merge(dst, src)
}
func (m *LoadMore) XXX_Size() int {
	return xxx_messageInfo_LoadMore.Size(m)
}
func (m *LoadMore) XXX_DiscardUnknown() {
	xxx_messageInfo_LoadMore.DiscardUnknown(m)
}

var xxx_messageInfo_LoadMore proto.InternalMessageInfo

func (m *LoadMore) GetHasNext() bool {
	if m != nil {
		return m.HasNext
	}
	return false
}

func (m *LoadMore) GetLastId() int64 {
	if m != nil {
		return m.LastId
	}
	return 0
}

func (m *LoadMore) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetOrderListByUserIDResp struct {
	UserId               uint32       `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderList            []*OrderInfo `protobuf:"bytes,2,rep,name=order_list,json=orderList,proto3" json:"order_list,omitempty"`
	LoadMore             *LoadMore    `protobuf:"bytes,3,opt,name=load_more,json=loadMore,proto3" json:"load_more,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetOrderListByUserIDResp) Reset()         { *m = GetOrderListByUserIDResp{} }
func (m *GetOrderListByUserIDResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderListByUserIDResp) ProtoMessage()    {}
func (*GetOrderListByUserIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{39}
}
func (m *GetOrderListByUserIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderListByUserIDResp.Unmarshal(m, b)
}
func (m *GetOrderListByUserIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderListByUserIDResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderListByUserIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderListByUserIDResp.Merge(dst, src)
}
func (m *GetOrderListByUserIDResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderListByUserIDResp.Size(m)
}
func (m *GetOrderListByUserIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderListByUserIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderListByUserIDResp proto.InternalMessageInfo

func (m *GetOrderListByUserIDResp) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetOrderListByUserIDResp) GetOrderList() []*OrderInfo {
	if m != nil {
		return m.OrderList
	}
	return nil
}

func (m *GetOrderListByUserIDResp) GetLoadMore() *LoadMore {
	if m != nil {
		return m.LoadMore
	}
	return nil
}

type GetOrderInfoByUserIDOrderIDReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderId              string   `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderInfoByUserIDOrderIDReq) Reset()         { *m = GetOrderInfoByUserIDOrderIDReq{} }
func (m *GetOrderInfoByUserIDOrderIDReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByUserIDOrderIDReq) ProtoMessage()    {}
func (*GetOrderInfoByUserIDOrderIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{40}
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Unmarshal(m, b)
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByUserIDOrderIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Merge(dst, src)
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.Size(m)
}
func (m *GetOrderInfoByUserIDOrderIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByUserIDOrderIDReq proto.InternalMessageInfo

func (m *GetOrderInfoByUserIDOrderIDReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetOrderInfoByUserIDOrderIDReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderInfoByUserIDOrderIDResp struct {
	OrderInfo            *OrderInfo `protobuf:"bytes,1,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetOrderInfoByUserIDOrderIDResp) Reset()         { *m = GetOrderInfoByUserIDOrderIDResp{} }
func (m *GetOrderInfoByUserIDOrderIDResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByUserIDOrderIDResp) ProtoMessage()    {}
func (*GetOrderInfoByUserIDOrderIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{41}
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Unmarshal(m, b)
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByUserIDOrderIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Merge(dst, src)
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.Size(m)
}
func (m *GetOrderInfoByUserIDOrderIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByUserIDOrderIDResp proto.InternalMessageInfo

func (m *GetOrderInfoByUserIDOrderIDResp) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

type GetOrderInfoByOrderIDReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOrderInfoByOrderIDReq) Reset()         { *m = GetOrderInfoByOrderIDReq{} }
func (m *GetOrderInfoByOrderIDReq) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByOrderIDReq) ProtoMessage()    {}
func (*GetOrderInfoByOrderIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{42}
}
func (m *GetOrderInfoByOrderIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByOrderIDReq.Unmarshal(m, b)
}
func (m *GetOrderInfoByOrderIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByOrderIDReq.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByOrderIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByOrderIDReq.Merge(dst, src)
}
func (m *GetOrderInfoByOrderIDReq) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByOrderIDReq.Size(m)
}
func (m *GetOrderInfoByOrderIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByOrderIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByOrderIDReq proto.InternalMessageInfo

func (m *GetOrderInfoByOrderIDReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

type GetOrderInfoByOrderIDResp struct {
	OrderInfo            *OrderInfo `protobuf:"bytes,1,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetOrderInfoByOrderIDResp) Reset()         { *m = GetOrderInfoByOrderIDResp{} }
func (m *GetOrderInfoByOrderIDResp) String() string { return proto.CompactTextString(m) }
func (*GetOrderInfoByOrderIDResp) ProtoMessage()    {}
func (*GetOrderInfoByOrderIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{43}
}
func (m *GetOrderInfoByOrderIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOrderInfoByOrderIDResp.Unmarshal(m, b)
}
func (m *GetOrderInfoByOrderIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOrderInfoByOrderIDResp.Marshal(b, m, deterministic)
}
func (dst *GetOrderInfoByOrderIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOrderInfoByOrderIDResp.Merge(dst, src)
}
func (m *GetOrderInfoByOrderIDResp) XXX_Size() int {
	return xxx_messageInfo_GetOrderInfoByOrderIDResp.Size(m)
}
func (m *GetOrderInfoByOrderIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOrderInfoByOrderIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOrderInfoByOrderIDResp proto.InternalMessageInfo

func (m *GetOrderInfoByOrderIDResp) GetOrderInfo() *OrderInfo {
	if m != nil {
		return m.OrderInfo
	}
	return nil
}

type UpdateOrderInfoByOrderIDReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Status               int64    `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	Reason               string   `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOrderInfoByOrderIDReq) Reset()         { *m = UpdateOrderInfoByOrderIDReq{} }
func (m *UpdateOrderInfoByOrderIDReq) String() string { return proto.CompactTextString(m) }
func (*UpdateOrderInfoByOrderIDReq) ProtoMessage()    {}
func (*UpdateOrderInfoByOrderIDReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{44}
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Unmarshal(m, b)
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Marshal(b, m, deterministic)
}
func (dst *UpdateOrderInfoByOrderIDReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Merge(dst, src)
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_Size() int {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDReq.Size(m)
}
func (m *UpdateOrderInfoByOrderIDReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrderInfoByOrderIDReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrderInfoByOrderIDReq proto.InternalMessageInfo

func (m *UpdateOrderInfoByOrderIDReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *UpdateOrderInfoByOrderIDReq) GetStatus() int64 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UpdateOrderInfoByOrderIDReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type UpdateOrderInfoByOrderIDResp struct {
	Message              string   `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateOrderInfoByOrderIDResp) Reset()         { *m = UpdateOrderInfoByOrderIDResp{} }
func (m *UpdateOrderInfoByOrderIDResp) String() string { return proto.CompactTextString(m) }
func (*UpdateOrderInfoByOrderIDResp) ProtoMessage()    {}
func (*UpdateOrderInfoByOrderIDResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{45}
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Unmarshal(m, b)
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Marshal(b, m, deterministic)
}
func (dst *UpdateOrderInfoByOrderIDResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Merge(dst, src)
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_Size() int {
	return xxx_messageInfo_UpdateOrderInfoByOrderIDResp.Size(m)
}
func (m *UpdateOrderInfoByOrderIDResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateOrderInfoByOrderIDResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateOrderInfoByOrderIDResp proto.InternalMessageInfo

func (m *UpdateOrderInfoByOrderIDResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type BindWXUserPayInfoReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	State                string   `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindWXUserPayInfoReq) Reset()         { *m = BindWXUserPayInfoReq{} }
func (m *BindWXUserPayInfoReq) String() string { return proto.CompactTextString(m) }
func (*BindWXUserPayInfoReq) ProtoMessage()    {}
func (*BindWXUserPayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{46}
}
func (m *BindWXUserPayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindWXUserPayInfoReq.Unmarshal(m, b)
}
func (m *BindWXUserPayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindWXUserPayInfoReq.Marshal(b, m, deterministic)
}
func (dst *BindWXUserPayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindWXUserPayInfoReq.Merge(dst, src)
}
func (m *BindWXUserPayInfoReq) XXX_Size() int {
	return xxx_messageInfo_BindWXUserPayInfoReq.Size(m)
}
func (m *BindWXUserPayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindWXUserPayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindWXUserPayInfoReq proto.InternalMessageInfo

func (m *BindWXUserPayInfoReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *BindWXUserPayInfoReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *BindWXUserPayInfoReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type BindWXUserPayInfoResp struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OpenId               string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Message              string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindWXUserPayInfoResp) Reset()         { *m = BindWXUserPayInfoResp{} }
func (m *BindWXUserPayInfoResp) String() string { return proto.CompactTextString(m) }
func (*BindWXUserPayInfoResp) ProtoMessage()    {}
func (*BindWXUserPayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{47}
}
func (m *BindWXUserPayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindWXUserPayInfoResp.Unmarshal(m, b)
}
func (m *BindWXUserPayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindWXUserPayInfoResp.Marshal(b, m, deterministic)
}
func (dst *BindWXUserPayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindWXUserPayInfoResp.Merge(dst, src)
}
func (m *BindWXUserPayInfoResp) XXX_Size() int {
	return xxx_messageInfo_BindWXUserPayInfoResp.Size(m)
}
func (m *BindWXUserPayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BindWXUserPayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BindWXUserPayInfoResp proto.InternalMessageInfo

func (m *BindWXUserPayInfoResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *BindWXUserPayInfoResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *BindWXUserPayInfoResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *BindWXUserPayInfoResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type GetBindingInfoReq struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Code                 string   `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	State                string   `protobuf:"bytes,3,opt,name=state,proto3" json:"state,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindingInfoReq) Reset()         { *m = GetBindingInfoReq{} }
func (m *GetBindingInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBindingInfoReq) ProtoMessage()    {}
func (*GetBindingInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{48}
}
func (m *GetBindingInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindingInfoReq.Unmarshal(m, b)
}
func (m *GetBindingInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindingInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBindingInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindingInfoReq.Merge(dst, src)
}
func (m *GetBindingInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBindingInfoReq.Size(m)
}
func (m *GetBindingInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindingInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindingInfoReq proto.InternalMessageInfo

func (m *GetBindingInfoReq) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

func (m *GetBindingInfoReq) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *GetBindingInfoReq) GetState() string {
	if m != nil {
		return m.State
	}
	return ""
}

type GetBindingInfoResp struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OpenId               string   `protobuf:"bytes,2,opt,name=openId,proto3" json:"openId,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	Message              string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBindingInfoResp) Reset()         { *m = GetBindingInfoResp{} }
func (m *GetBindingInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBindingInfoResp) ProtoMessage()    {}
func (*GetBindingInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{49}
}
func (m *GetBindingInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBindingInfoResp.Unmarshal(m, b)
}
func (m *GetBindingInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBindingInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBindingInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBindingInfoResp.Merge(dst, src)
}
func (m *GetBindingInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBindingInfoResp.Size(m)
}
func (m *GetBindingInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBindingInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBindingInfoResp proto.InternalMessageInfo

func (m *GetBindingInfoResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *GetBindingInfoResp) GetOpenId() string {
	if m != nil {
		return m.OpenId
	}
	return ""
}

func (m *GetBindingInfoResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *GetBindingInfoResp) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

type HistoryTaskReq struct {
	TaskType             HistoryTaskType `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3,enum=masterapprentice.HistoryTaskType" json:"task_type,omitempty"`
	Offset               uint32          `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32          `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	MasterUid            uint32          `protobuf:"varint,4,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *HistoryTaskReq) Reset()         { *m = HistoryTaskReq{} }
func (m *HistoryTaskReq) String() string { return proto.CompactTextString(m) }
func (*HistoryTaskReq) ProtoMessage()    {}
func (*HistoryTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{50}
}
func (m *HistoryTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HistoryTaskReq.Unmarshal(m, b)
}
func (m *HistoryTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HistoryTaskReq.Marshal(b, m, deterministic)
}
func (dst *HistoryTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistoryTaskReq.Merge(dst, src)
}
func (m *HistoryTaskReq) XXX_Size() int {
	return xxx_messageInfo_HistoryTaskReq.Size(m)
}
func (m *HistoryTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_HistoryTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_HistoryTaskReq proto.InternalMessageInfo

func (m *HistoryTaskReq) GetTaskType() HistoryTaskType {
	if m != nil {
		return m.TaskType
	}
	return HistoryTaskType_MissionFinish
}

func (m *HistoryTaskReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *HistoryTaskReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *HistoryTaskReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type HistoryTaskInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	Gender               uint32   `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	Day                  uint32   `protobuf:"varint,6,opt,name=day,proto3" json:"day,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HistoryTaskInfo) Reset()         { *m = HistoryTaskInfo{} }
func (m *HistoryTaskInfo) String() string { return proto.CompactTextString(m) }
func (*HistoryTaskInfo) ProtoMessage()    {}
func (*HistoryTaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{51}
}
func (m *HistoryTaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HistoryTaskInfo.Unmarshal(m, b)
}
func (m *HistoryTaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HistoryTaskInfo.Marshal(b, m, deterministic)
}
func (dst *HistoryTaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistoryTaskInfo.Merge(dst, src)
}
func (m *HistoryTaskInfo) XXX_Size() int {
	return xxx_messageInfo_HistoryTaskInfo.Size(m)
}
func (m *HistoryTaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HistoryTaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HistoryTaskInfo proto.InternalMessageInfo

func (m *HistoryTaskInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HistoryTaskInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *HistoryTaskInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *HistoryTaskInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *HistoryTaskInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *HistoryTaskInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

type HistoryTaskRsp struct {
	TaskInfo             []*HistoryTaskInfo `protobuf:"bytes,1,rep,name=task_info,json=taskInfo,proto3" json:"task_info,omitempty"`
	CompleteCnt          uint32             `protobuf:"varint,2,opt,name=complete_cnt,json=completeCnt,proto3" json:"complete_cnt,omitempty"`
	UnvalidCnt           uint32             `protobuf:"varint,3,opt,name=unvalid_cnt,json=unvalidCnt,proto3" json:"unvalid_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *HistoryTaskRsp) Reset()         { *m = HistoryTaskRsp{} }
func (m *HistoryTaskRsp) String() string { return proto.CompactTextString(m) }
func (*HistoryTaskRsp) ProtoMessage()    {}
func (*HistoryTaskRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{52}
}
func (m *HistoryTaskRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HistoryTaskRsp.Unmarshal(m, b)
}
func (m *HistoryTaskRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HistoryTaskRsp.Marshal(b, m, deterministic)
}
func (dst *HistoryTaskRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistoryTaskRsp.Merge(dst, src)
}
func (m *HistoryTaskRsp) XXX_Size() int {
	return xxx_messageInfo_HistoryTaskRsp.Size(m)
}
func (m *HistoryTaskRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_HistoryTaskRsp.DiscardUnknown(m)
}

var xxx_messageInfo_HistoryTaskRsp proto.InternalMessageInfo

func (m *HistoryTaskRsp) GetTaskInfo() []*HistoryTaskInfo {
	if m != nil {
		return m.TaskInfo
	}
	return nil
}

func (m *HistoryTaskRsp) GetCompleteCnt() uint32 {
	if m != nil {
		return m.CompleteCnt
	}
	return 0
}

func (m *HistoryTaskRsp) GetUnvalidCnt() uint32 {
	if m != nil {
		return m.UnvalidCnt
	}
	return 0
}

type CurrentTaskReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	MasterUid            uint32   `protobuf:"varint,3,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CurrentTaskReq) Reset()         { *m = CurrentTaskReq{} }
func (m *CurrentTaskReq) String() string { return proto.CompactTextString(m) }
func (*CurrentTaskReq) ProtoMessage()    {}
func (*CurrentTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{53}
}
func (m *CurrentTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CurrentTaskReq.Unmarshal(m, b)
}
func (m *CurrentTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CurrentTaskReq.Marshal(b, m, deterministic)
}
func (dst *CurrentTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CurrentTaskReq.Merge(dst, src)
}
func (m *CurrentTaskReq) XXX_Size() int {
	return xxx_messageInfo_CurrentTaskReq.Size(m)
}
func (m *CurrentTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CurrentTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_CurrentTaskReq proto.InternalMessageInfo

func (m *CurrentTaskReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *CurrentTaskReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *CurrentTaskReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type CurrentTaskInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Income               int64    `protobuf:"varint,4,opt,name=income,proto3" json:"income,omitempty"`
	Day                  uint32   `protobuf:"varint,5,opt,name=day,proto3" json:"day,omitempty"`
	Status               uint32   `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	Gender               uint32   `protobuf:"varint,7,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CurrentTaskInfo) Reset()         { *m = CurrentTaskInfo{} }
func (m *CurrentTaskInfo) String() string { return proto.CompactTextString(m) }
func (*CurrentTaskInfo) ProtoMessage()    {}
func (*CurrentTaskInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{54}
}
func (m *CurrentTaskInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CurrentTaskInfo.Unmarshal(m, b)
}
func (m *CurrentTaskInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CurrentTaskInfo.Marshal(b, m, deterministic)
}
func (dst *CurrentTaskInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CurrentTaskInfo.Merge(dst, src)
}
func (m *CurrentTaskInfo) XXX_Size() int {
	return xxx_messageInfo_CurrentTaskInfo.Size(m)
}
func (m *CurrentTaskInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CurrentTaskInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CurrentTaskInfo proto.InternalMessageInfo

func (m *CurrentTaskInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CurrentTaskInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *CurrentTaskInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *CurrentTaskInfo) GetIncome() int64 {
	if m != nil {
		return m.Income
	}
	return 0
}

func (m *CurrentTaskInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

func (m *CurrentTaskInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *CurrentTaskInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

type CurrentTaskRsp struct {
	TaskInfo             []*CurrentTaskInfo `protobuf:"bytes,1,rep,name=task_info,json=taskInfo,proto3" json:"task_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *CurrentTaskRsp) Reset()         { *m = CurrentTaskRsp{} }
func (m *CurrentTaskRsp) String() string { return proto.CompactTextString(m) }
func (*CurrentTaskRsp) ProtoMessage()    {}
func (*CurrentTaskRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{55}
}
func (m *CurrentTaskRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CurrentTaskRsp.Unmarshal(m, b)
}
func (m *CurrentTaskRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CurrentTaskRsp.Marshal(b, m, deterministic)
}
func (dst *CurrentTaskRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CurrentTaskRsp.Merge(dst, src)
}
func (m *CurrentTaskRsp) XXX_Size() int {
	return xxx_messageInfo_CurrentTaskRsp.Size(m)
}
func (m *CurrentTaskRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_CurrentTaskRsp.DiscardUnknown(m)
}

var xxx_messageInfo_CurrentTaskRsp proto.InternalMessageInfo

func (m *CurrentTaskRsp) GetTaskInfo() []*CurrentTaskInfo {
	if m != nil {
		return m.TaskInfo
	}
	return nil
}

// 获取师父是否第一次进师徒关系首页
type MasterFirstHomeReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterFirstHomeReq) Reset()         { *m = MasterFirstHomeReq{} }
func (m *MasterFirstHomeReq) String() string { return proto.CompactTextString(m) }
func (*MasterFirstHomeReq) ProtoMessage()    {}
func (*MasterFirstHomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{56}
}
func (m *MasterFirstHomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterFirstHomeReq.Unmarshal(m, b)
}
func (m *MasterFirstHomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterFirstHomeReq.Marshal(b, m, deterministic)
}
func (dst *MasterFirstHomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterFirstHomeReq.Merge(dst, src)
}
func (m *MasterFirstHomeReq) XXX_Size() int {
	return xxx_messageInfo_MasterFirstHomeReq.Size(m)
}
func (m *MasterFirstHomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterFirstHomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_MasterFirstHomeReq proto.InternalMessageInfo

func (m *MasterFirstHomeReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type MasterFirstHomeResp struct {
	IsFirst              bool     `protobuf:"varint,1,opt,name=isFirst,proto3" json:"isFirst,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterFirstHomeResp) Reset()         { *m = MasterFirstHomeResp{} }
func (m *MasterFirstHomeResp) String() string { return proto.CompactTextString(m) }
func (*MasterFirstHomeResp) ProtoMessage()    {}
func (*MasterFirstHomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{57}
}
func (m *MasterFirstHomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterFirstHomeResp.Unmarshal(m, b)
}
func (m *MasterFirstHomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterFirstHomeResp.Marshal(b, m, deterministic)
}
func (dst *MasterFirstHomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterFirstHomeResp.Merge(dst, src)
}
func (m *MasterFirstHomeResp) XXX_Size() int {
	return xxx_messageInfo_MasterFirstHomeResp.Size(m)
}
func (m *MasterFirstHomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterFirstHomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_MasterFirstHomeResp proto.InternalMessageInfo

func (m *MasterFirstHomeResp) GetIsFirst() bool {
	if m != nil {
		return m.IsFirst
	}
	return false
}

// 设置师父第一次进师徒关系首页
type InsertMasterFirstHomeReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertMasterFirstHomeReq) Reset()         { *m = InsertMasterFirstHomeReq{} }
func (m *InsertMasterFirstHomeReq) String() string { return proto.CompactTextString(m) }
func (*InsertMasterFirstHomeReq) ProtoMessage()    {}
func (*InsertMasterFirstHomeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{58}
}
func (m *InsertMasterFirstHomeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertMasterFirstHomeReq.Unmarshal(m, b)
}
func (m *InsertMasterFirstHomeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertMasterFirstHomeReq.Marshal(b, m, deterministic)
}
func (dst *InsertMasterFirstHomeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertMasterFirstHomeReq.Merge(dst, src)
}
func (m *InsertMasterFirstHomeReq) XXX_Size() int {
	return xxx_messageInfo_InsertMasterFirstHomeReq.Size(m)
}
func (m *InsertMasterFirstHomeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertMasterFirstHomeReq.DiscardUnknown(m)
}

var xxx_messageInfo_InsertMasterFirstHomeReq proto.InternalMessageInfo

func (m *InsertMasterFirstHomeReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type InsertMasterFirstHomeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InsertMasterFirstHomeResp) Reset()         { *m = InsertMasterFirstHomeResp{} }
func (m *InsertMasterFirstHomeResp) String() string { return proto.CompactTextString(m) }
func (*InsertMasterFirstHomeResp) ProtoMessage()    {}
func (*InsertMasterFirstHomeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{59}
}
func (m *InsertMasterFirstHomeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InsertMasterFirstHomeResp.Unmarshal(m, b)
}
func (m *InsertMasterFirstHomeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InsertMasterFirstHomeResp.Marshal(b, m, deterministic)
}
func (dst *InsertMasterFirstHomeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InsertMasterFirstHomeResp.Merge(dst, src)
}
func (m *InsertMasterFirstHomeResp) XXX_Size() int {
	return xxx_messageInfo_InsertMasterFirstHomeResp.Size(m)
}
func (m *InsertMasterFirstHomeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InsertMasterFirstHomeResp.DiscardUnknown(m)
}

var xxx_messageInfo_InsertMasterFirstHomeResp proto.InternalMessageInfo

type GetActConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActConfigReq) Reset()         { *m = GetActConfigReq{} }
func (m *GetActConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetActConfigReq) ProtoMessage()    {}
func (*GetActConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{60}
}
func (m *GetActConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActConfigReq.Unmarshal(m, b)
}
func (m *GetActConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetActConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActConfigReq.Merge(dst, src)
}
func (m *GetActConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetActConfigReq.Size(m)
}
func (m *GetActConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActConfigReq proto.InternalMessageInfo

type GetActConfigResp struct {
	BeginTime            string   `protobuf:"bytes,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              string   `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	EndGetApprenticeTime string   `protobuf:"bytes,3,opt,name=EndGetApprenticeTime,proto3" json:"EndGetApprenticeTime,omitempty"`
	EndTaskTime          string   `protobuf:"bytes,4,opt,name=EndTaskTime,proto3" json:"EndTaskTime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActConfigResp) Reset()         { *m = GetActConfigResp{} }
func (m *GetActConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetActConfigResp) ProtoMessage()    {}
func (*GetActConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{61}
}
func (m *GetActConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActConfigResp.Unmarshal(m, b)
}
func (m *GetActConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetActConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActConfigResp.Merge(dst, src)
}
func (m *GetActConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetActConfigResp.Size(m)
}
func (m *GetActConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActConfigResp proto.InternalMessageInfo

func (m *GetActConfigResp) GetBeginTime() string {
	if m != nil {
		return m.BeginTime
	}
	return ""
}

func (m *GetActConfigResp) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetActConfigResp) GetEndGetApprenticeTime() string {
	if m != nil {
		return m.EndGetApprenticeTime
	}
	return ""
}

func (m *GetActConfigResp) GetEndTaskTime() string {
	if m != nil {
		return m.EndTaskTime
	}
	return ""
}

// web 首页数据
type MasterInitForWebReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterInitForWebReq) Reset()         { *m = MasterInitForWebReq{} }
func (m *MasterInitForWebReq) String() string { return proto.CompactTextString(m) }
func (*MasterInitForWebReq) ProtoMessage()    {}
func (*MasterInitForWebReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{62}
}
func (m *MasterInitForWebReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterInitForWebReq.Unmarshal(m, b)
}
func (m *MasterInitForWebReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterInitForWebReq.Marshal(b, m, deterministic)
}
func (dst *MasterInitForWebReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterInitForWebReq.Merge(dst, src)
}
func (m *MasterInitForWebReq) XXX_Size() int {
	return xxx_messageInfo_MasterInitForWebReq.Size(m)
}
func (m *MasterInitForWebReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterInitForWebReq.DiscardUnknown(m)
}

var xxx_messageInfo_MasterInitForWebReq proto.InternalMessageInfo

func (m *MasterInitForWebReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type MasterInitForWebResp struct {
	FirstEntry           uint32   `protobuf:"varint,1,opt,name=first_entry,json=firstEntry,proto3" json:"first_entry,omitempty"`
	RecruitTime          uint32   `protobuf:"varint,2,opt,name=recruit_time,json=recruitTime,proto3" json:"recruit_time,omitempty"`
	WithdrawableIncome   uint32   `protobuf:"varint,3,opt,name=withdrawable_income,json=withdrawableIncome,proto3" json:"withdrawable_income,omitempty"`
	TotalIncome          uint32   `protobuf:"varint,4,opt,name=total_income,json=totalIncome,proto3" json:"total_income,omitempty"`
	AvailableNum         uint32   `protobuf:"varint,5,opt,name=available_num,json=availableNum,proto3" json:"available_num,omitempty"`
	AdditionalIncome     uint32   `protobuf:"varint,6,opt,name=additional_income,json=additionalIncome,proto3" json:"additional_income,omitempty"`
	TodayIncome          uint32   `protobuf:"varint,7,opt,name=today_income,json=todayIncome,proto3" json:"today_income,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterInitForWebResp) Reset()         { *m = MasterInitForWebResp{} }
func (m *MasterInitForWebResp) String() string { return proto.CompactTextString(m) }
func (*MasterInitForWebResp) ProtoMessage()    {}
func (*MasterInitForWebResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{63}
}
func (m *MasterInitForWebResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterInitForWebResp.Unmarshal(m, b)
}
func (m *MasterInitForWebResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterInitForWebResp.Marshal(b, m, deterministic)
}
func (dst *MasterInitForWebResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterInitForWebResp.Merge(dst, src)
}
func (m *MasterInitForWebResp) XXX_Size() int {
	return xxx_messageInfo_MasterInitForWebResp.Size(m)
}
func (m *MasterInitForWebResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterInitForWebResp.DiscardUnknown(m)
}

var xxx_messageInfo_MasterInitForWebResp proto.InternalMessageInfo

func (m *MasterInitForWebResp) GetFirstEntry() uint32 {
	if m != nil {
		return m.FirstEntry
	}
	return 0
}

func (m *MasterInitForWebResp) GetRecruitTime() uint32 {
	if m != nil {
		return m.RecruitTime
	}
	return 0
}

func (m *MasterInitForWebResp) GetWithdrawableIncome() uint32 {
	if m != nil {
		return m.WithdrawableIncome
	}
	return 0
}

func (m *MasterInitForWebResp) GetTotalIncome() uint32 {
	if m != nil {
		return m.TotalIncome
	}
	return 0
}

func (m *MasterInitForWebResp) GetAvailableNum() uint32 {
	if m != nil {
		return m.AvailableNum
	}
	return 0
}

func (m *MasterInitForWebResp) GetAdditionalIncome() uint32 {
	if m != nil {
		return m.AdditionalIncome
	}
	return 0
}

func (m *MasterInitForWebResp) GetTodayIncome() uint32 {
	if m != nil {
		return m.TodayIncome
	}
	return 0
}

// 获取进行中的师傅的所有徒弟信息
type GetAllApprenticeInfosInProcessReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllApprenticeInfosInProcessReq) Reset()         { *m = GetAllApprenticeInfosInProcessReq{} }
func (m *GetAllApprenticeInfosInProcessReq) String() string { return proto.CompactTextString(m) }
func (*GetAllApprenticeInfosInProcessReq) ProtoMessage()    {}
func (*GetAllApprenticeInfosInProcessReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{64}
}
func (m *GetAllApprenticeInfosInProcessReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllApprenticeInfosInProcessReq.Unmarshal(m, b)
}
func (m *GetAllApprenticeInfosInProcessReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllApprenticeInfosInProcessReq.Marshal(b, m, deterministic)
}
func (dst *GetAllApprenticeInfosInProcessReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllApprenticeInfosInProcessReq.Merge(dst, src)
}
func (m *GetAllApprenticeInfosInProcessReq) XXX_Size() int {
	return xxx_messageInfo_GetAllApprenticeInfosInProcessReq.Size(m)
}
func (m *GetAllApprenticeInfosInProcessReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllApprenticeInfosInProcessReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllApprenticeInfosInProcessReq proto.InternalMessageInfo

func (m *GetAllApprenticeInfosInProcessReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type ApprenticeInfo struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TotalStatus          ApprenticeStatus `protobuf:"varint,2,opt,name=total_status,json=totalStatus,proto3,enum=masterapprentice.ApprenticeStatus" json:"total_status,omitempty"`
	Day                  uint32           `protobuf:"varint,3,opt,name=Day,proto3" json:"Day,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ApprenticeInfo) Reset()         { *m = ApprenticeInfo{} }
func (m *ApprenticeInfo) String() string { return proto.CompactTextString(m) }
func (*ApprenticeInfo) ProtoMessage()    {}
func (*ApprenticeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{65}
}
func (m *ApprenticeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApprenticeInfo.Unmarshal(m, b)
}
func (m *ApprenticeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApprenticeInfo.Marshal(b, m, deterministic)
}
func (dst *ApprenticeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApprenticeInfo.Merge(dst, src)
}
func (m *ApprenticeInfo) XXX_Size() int {
	return xxx_messageInfo_ApprenticeInfo.Size(m)
}
func (m *ApprenticeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApprenticeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApprenticeInfo proto.InternalMessageInfo

func (m *ApprenticeInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApprenticeInfo) GetTotalStatus() ApprenticeStatus {
	if m != nil {
		return m.TotalStatus
	}
	return ApprenticeStatus_INVITING
}

func (m *ApprenticeInfo) GetDay() uint32 {
	if m != nil {
		return m.Day
	}
	return 0
}

type GetAllApprenticeInfosInProcessResp struct {
	InfoList             []*ApprenticeInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllApprenticeInfosInProcessResp) Reset()         { *m = GetAllApprenticeInfosInProcessResp{} }
func (m *GetAllApprenticeInfosInProcessResp) String() string { return proto.CompactTextString(m) }
func (*GetAllApprenticeInfosInProcessResp) ProtoMessage()    {}
func (*GetAllApprenticeInfosInProcessResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{66}
}
func (m *GetAllApprenticeInfosInProcessResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllApprenticeInfosInProcessResp.Unmarshal(m, b)
}
func (m *GetAllApprenticeInfosInProcessResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllApprenticeInfosInProcessResp.Marshal(b, m, deterministic)
}
func (dst *GetAllApprenticeInfosInProcessResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllApprenticeInfosInProcessResp.Merge(dst, src)
}
func (m *GetAllApprenticeInfosInProcessResp) XXX_Size() int {
	return xxx_messageInfo_GetAllApprenticeInfosInProcessResp.Size(m)
}
func (m *GetAllApprenticeInfosInProcessResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllApprenticeInfosInProcessResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllApprenticeInfosInProcessResp proto.InternalMessageInfo

func (m *GetAllApprenticeInfosInProcessResp) GetInfoList() []*ApprenticeInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

// 成师礼包
type ReceiveGiftReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveGiftReq) Reset()         { *m = ReceiveGiftReq{} }
func (m *ReceiveGiftReq) String() string { return proto.CompactTextString(m) }
func (*ReceiveGiftReq) ProtoMessage()    {}
func (*ReceiveGiftReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{67}
}
func (m *ReceiveGiftReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveGiftReq.Unmarshal(m, b)
}
func (m *ReceiveGiftReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveGiftReq.Marshal(b, m, deterministic)
}
func (dst *ReceiveGiftReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveGiftReq.Merge(dst, src)
}
func (m *ReceiveGiftReq) XXX_Size() int {
	return xxx_messageInfo_ReceiveGiftReq.Size(m)
}
func (m *ReceiveGiftReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveGiftReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveGiftReq proto.InternalMessageInfo

func (m *ReceiveGiftReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ReceiveGiftResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReceiveGiftResp) Reset()         { *m = ReceiveGiftResp{} }
func (m *ReceiveGiftResp) String() string { return proto.CompactTextString(m) }
func (*ReceiveGiftResp) ProtoMessage()    {}
func (*ReceiveGiftResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{68}
}
func (m *ReceiveGiftResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReceiveGiftResp.Unmarshal(m, b)
}
func (m *ReceiveGiftResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReceiveGiftResp.Marshal(b, m, deterministic)
}
func (dst *ReceiveGiftResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReceiveGiftResp.Merge(dst, src)
}
func (m *ReceiveGiftResp) XXX_Size() int {
	return xxx_messageInfo_ReceiveGiftResp.Size(m)
}
func (m *ReceiveGiftResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReceiveGiftResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReceiveGiftResp proto.InternalMessageInfo

// 监控 师父的收益和提现
type MasterMonitorInTodayReq struct {
	LimitIncome          uint32   `protobuf:"varint,1,opt,name=limit_income,json=limitIncome,proto3" json:"limit_income,omitempty"`
	LimitWithdraw        uint32   `protobuf:"varint,2,opt,name=limit_withdraw,json=limitWithdraw,proto3" json:"limit_withdraw,omitempty"`
	CheckTs              int64    `protobuf:"varint,3,opt,name=check_ts,json=checkTs,proto3" json:"check_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterMonitorInTodayReq) Reset()         { *m = MasterMonitorInTodayReq{} }
func (m *MasterMonitorInTodayReq) String() string { return proto.CompactTextString(m) }
func (*MasterMonitorInTodayReq) ProtoMessage()    {}
func (*MasterMonitorInTodayReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{69}
}
func (m *MasterMonitorInTodayReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterMonitorInTodayReq.Unmarshal(m, b)
}
func (m *MasterMonitorInTodayReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterMonitorInTodayReq.Marshal(b, m, deterministic)
}
func (dst *MasterMonitorInTodayReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterMonitorInTodayReq.Merge(dst, src)
}
func (m *MasterMonitorInTodayReq) XXX_Size() int {
	return xxx_messageInfo_MasterMonitorInTodayReq.Size(m)
}
func (m *MasterMonitorInTodayReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterMonitorInTodayReq.DiscardUnknown(m)
}

var xxx_messageInfo_MasterMonitorInTodayReq proto.InternalMessageInfo

func (m *MasterMonitorInTodayReq) GetLimitIncome() uint32 {
	if m != nil {
		return m.LimitIncome
	}
	return 0
}

func (m *MasterMonitorInTodayReq) GetLimitWithdraw() uint32 {
	if m != nil {
		return m.LimitWithdraw
	}
	return 0
}

func (m *MasterMonitorInTodayReq) GetCheckTs() int64 {
	if m != nil {
		return m.CheckTs
	}
	return 0
}

type MasterLimitInfo struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	Amount               uint32   `protobuf:"varint,2,opt,name=amount,proto3" json:"amount,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MasterLimitInfo) Reset()         { *m = MasterLimitInfo{} }
func (m *MasterLimitInfo) String() string { return proto.CompactTextString(m) }
func (*MasterLimitInfo) ProtoMessage()    {}
func (*MasterLimitInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{70}
}
func (m *MasterLimitInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterLimitInfo.Unmarshal(m, b)
}
func (m *MasterLimitInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterLimitInfo.Marshal(b, m, deterministic)
}
func (dst *MasterLimitInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterLimitInfo.Merge(dst, src)
}
func (m *MasterLimitInfo) XXX_Size() int {
	return xxx_messageInfo_MasterLimitInfo.Size(m)
}
func (m *MasterLimitInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterLimitInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MasterLimitInfo proto.InternalMessageInfo

func (m *MasterLimitInfo) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *MasterLimitInfo) GetAmount() uint32 {
	if m != nil {
		return m.Amount
	}
	return 0
}

type MasterMonitorInTodayResp struct {
	ExceedIncomeMasters   []*MasterLimitInfo `protobuf:"bytes,1,rep,name=exceed_income_masters,json=exceedIncomeMasters,proto3" json:"exceed_income_masters,omitempty"`
	ExceedWithdrawMasters []*MasterLimitInfo `protobuf:"bytes,2,rep,name=exceed_withdraw_masters,json=exceedWithdrawMasters,proto3" json:"exceed_withdraw_masters,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}           `json:"-"`
	XXX_unrecognized      []byte             `json:"-"`
	XXX_sizecache         int32              `json:"-"`
}

func (m *MasterMonitorInTodayResp) Reset()         { *m = MasterMonitorInTodayResp{} }
func (m *MasterMonitorInTodayResp) String() string { return proto.CompactTextString(m) }
func (*MasterMonitorInTodayResp) ProtoMessage()    {}
func (*MasterMonitorInTodayResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{71}
}
func (m *MasterMonitorInTodayResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MasterMonitorInTodayResp.Unmarshal(m, b)
}
func (m *MasterMonitorInTodayResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MasterMonitorInTodayResp.Marshal(b, m, deterministic)
}
func (dst *MasterMonitorInTodayResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MasterMonitorInTodayResp.Merge(dst, src)
}
func (m *MasterMonitorInTodayResp) XXX_Size() int {
	return xxx_messageInfo_MasterMonitorInTodayResp.Size(m)
}
func (m *MasterMonitorInTodayResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MasterMonitorInTodayResp.DiscardUnknown(m)
}

var xxx_messageInfo_MasterMonitorInTodayResp proto.InternalMessageInfo

func (m *MasterMonitorInTodayResp) GetExceedIncomeMasters() []*MasterLimitInfo {
	if m != nil {
		return m.ExceedIncomeMasters
	}
	return nil
}

func (m *MasterMonitorInTodayResp) GetExceedWithdrawMasters() []*MasterLimitInfo {
	if m != nil {
		return m.ExceedWithdrawMasters
	}
	return nil
}

// 活动数据统计（活动成本和师父收益）
type GetActivityStatisticsReq struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetActivityStatisticsReq) Reset()         { *m = GetActivityStatisticsReq{} }
func (m *GetActivityStatisticsReq) String() string { return proto.CompactTextString(m) }
func (*GetActivityStatisticsReq) ProtoMessage()    {}
func (*GetActivityStatisticsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{72}
}
func (m *GetActivityStatisticsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityStatisticsReq.Unmarshal(m, b)
}
func (m *GetActivityStatisticsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityStatisticsReq.Marshal(b, m, deterministic)
}
func (dst *GetActivityStatisticsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityStatisticsReq.Merge(dst, src)
}
func (m *GetActivityStatisticsReq) XXX_Size() int {
	return xxx_messageInfo_GetActivityStatisticsReq.Size(m)
}
func (m *GetActivityStatisticsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityStatisticsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityStatisticsReq proto.InternalMessageInfo

func (m *GetActivityStatisticsReq) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

type ActivityStatistics struct {
	TotalBalance         int64    `protobuf:"varint,1,opt,name=TotalBalance,proto3" json:"TotalBalance,omitempty"`
	TotalWithdraw        int64    `protobuf:"varint,2,opt,name=TotalWithdraw,proto3" json:"TotalWithdraw,omitempty"`
	AvailableBalance     int64    `protobuf:"varint,3,opt,name=AvailableBalance,proto3" json:"AvailableBalance,omitempty"`
	AwardInDate          int64    `protobuf:"varint,4,opt,name=AwardInDate,proto3" json:"AwardInDate,omitempty"`
	WithdrawInDate       int64    `protobuf:"varint,5,opt,name=WithdrawInDate,proto3" json:"WithdrawInDate,omitempty"`
	AvgAward             int64    `protobuf:"varint,6,opt,name=AvgAward,proto3" json:"AvgAward,omitempty"`
	AvgAwardInDate       int64    `protobuf:"varint,7,opt,name=AvgAwardInDate,proto3" json:"AvgAwardInDate,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ActivityStatistics) Reset()         { *m = ActivityStatistics{} }
func (m *ActivityStatistics) String() string { return proto.CompactTextString(m) }
func (*ActivityStatistics) ProtoMessage()    {}
func (*ActivityStatistics) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{73}
}
func (m *ActivityStatistics) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ActivityStatistics.Unmarshal(m, b)
}
func (m *ActivityStatistics) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ActivityStatistics.Marshal(b, m, deterministic)
}
func (dst *ActivityStatistics) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ActivityStatistics.Merge(dst, src)
}
func (m *ActivityStatistics) XXX_Size() int {
	return xxx_messageInfo_ActivityStatistics.Size(m)
}
func (m *ActivityStatistics) XXX_DiscardUnknown() {
	xxx_messageInfo_ActivityStatistics.DiscardUnknown(m)
}

var xxx_messageInfo_ActivityStatistics proto.InternalMessageInfo

func (m *ActivityStatistics) GetTotalBalance() int64 {
	if m != nil {
		return m.TotalBalance
	}
	return 0
}

func (m *ActivityStatistics) GetTotalWithdraw() int64 {
	if m != nil {
		return m.TotalWithdraw
	}
	return 0
}

func (m *ActivityStatistics) GetAvailableBalance() int64 {
	if m != nil {
		return m.AvailableBalance
	}
	return 0
}

func (m *ActivityStatistics) GetAwardInDate() int64 {
	if m != nil {
		return m.AwardInDate
	}
	return 0
}

func (m *ActivityStatistics) GetWithdrawInDate() int64 {
	if m != nil {
		return m.WithdrawInDate
	}
	return 0
}

func (m *ActivityStatistics) GetAvgAward() int64 {
	if m != nil {
		return m.AvgAward
	}
	return 0
}

func (m *ActivityStatistics) GetAvgAwardInDate() int64 {
	if m != nil {
		return m.AvgAwardInDate
	}
	return 0
}

type GetActivityStatisticsResp struct {
	Statistics           *ActivityStatistics `protobuf:"bytes,1,opt,name=statistics,proto3" json:"statistics,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetActivityStatisticsResp) Reset()         { *m = GetActivityStatisticsResp{} }
func (m *GetActivityStatisticsResp) String() string { return proto.CompactTextString(m) }
func (*GetActivityStatisticsResp) ProtoMessage()    {}
func (*GetActivityStatisticsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{74}
}
func (m *GetActivityStatisticsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetActivityStatisticsResp.Unmarshal(m, b)
}
func (m *GetActivityStatisticsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetActivityStatisticsResp.Marshal(b, m, deterministic)
}
func (dst *GetActivityStatisticsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetActivityStatisticsResp.Merge(dst, src)
}
func (m *GetActivityStatisticsResp) XXX_Size() int {
	return xxx_messageInfo_GetActivityStatisticsResp.Size(m)
}
func (m *GetActivityStatisticsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetActivityStatisticsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetActivityStatisticsResp proto.InternalMessageInfo

func (m *GetActivityStatisticsResp) GetStatistics() *ActivityStatistics {
	if m != nil {
		return m.Statistics
	}
	return nil
}

// 针对同一个用户两次邀请发送时间<5min
type SameApprenticeReq struct {
	MasterUid            uint32   `protobuf:"varint,1,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	ApprenticeUid        uint32   `protobuf:"varint,2,opt,name=apprentice_uid,json=apprenticeUid,proto3" json:"apprentice_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SameApprenticeReq) Reset()         { *m = SameApprenticeReq{} }
func (m *SameApprenticeReq) String() string { return proto.CompactTextString(m) }
func (*SameApprenticeReq) ProtoMessage()    {}
func (*SameApprenticeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{75}
}
func (m *SameApprenticeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SameApprenticeReq.Unmarshal(m, b)
}
func (m *SameApprenticeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SameApprenticeReq.Marshal(b, m, deterministic)
}
func (dst *SameApprenticeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SameApprenticeReq.Merge(dst, src)
}
func (m *SameApprenticeReq) XXX_Size() int {
	return xxx_messageInfo_SameApprenticeReq.Size(m)
}
func (m *SameApprenticeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SameApprenticeReq.DiscardUnknown(m)
}

var xxx_messageInfo_SameApprenticeReq proto.InternalMessageInfo

func (m *SameApprenticeReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

func (m *SameApprenticeReq) GetApprenticeUid() uint32 {
	if m != nil {
		return m.ApprenticeUid
	}
	return 0
}

type SameApprenticeResp struct {
	IsSame               bool     `protobuf:"varint,1,opt,name=isSame,proto3" json:"isSame,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SameApprenticeResp) Reset()         { *m = SameApprenticeResp{} }
func (m *SameApprenticeResp) String() string { return proto.CompactTextString(m) }
func (*SameApprenticeResp) ProtoMessage()    {}
func (*SameApprenticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{76}
}
func (m *SameApprenticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SameApprenticeResp.Unmarshal(m, b)
}
func (m *SameApprenticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SameApprenticeResp.Marshal(b, m, deterministic)
}
func (dst *SameApprenticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SameApprenticeResp.Merge(dst, src)
}
func (m *SameApprenticeResp) XXX_Size() int {
	return xxx_messageInfo_SameApprenticeResp.Size(m)
}
func (m *SameApprenticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SameApprenticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_SameApprenticeResp proto.InternalMessageInfo

func (m *SameApprenticeResp) GetIsSame() bool {
	if m != nil {
		return m.IsSame
	}
	return false
}

// 完成首日师徒任务
type FinishFirstDayTaskReq struct {
	MasterId             uint32   `protobuf:"varint,1,opt,name=master_id,json=masterId,proto3" json:"master_id,omitempty"`
	ApprenticeId         uint32   `protobuf:"varint,2,opt,name=apprentice_id,json=apprenticeId,proto3" json:"apprentice_id,omitempty"`
	Datetime             int64    `protobuf:"varint,3,opt,name=datetime,proto3" json:"datetime,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishFirstDayTaskReq) Reset()         { *m = FinishFirstDayTaskReq{} }
func (m *FinishFirstDayTaskReq) String() string { return proto.CompactTextString(m) }
func (*FinishFirstDayTaskReq) ProtoMessage()    {}
func (*FinishFirstDayTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{77}
}
func (m *FinishFirstDayTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishFirstDayTaskReq.Unmarshal(m, b)
}
func (m *FinishFirstDayTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishFirstDayTaskReq.Marshal(b, m, deterministic)
}
func (dst *FinishFirstDayTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishFirstDayTaskReq.Merge(dst, src)
}
func (m *FinishFirstDayTaskReq) XXX_Size() int {
	return xxx_messageInfo_FinishFirstDayTaskReq.Size(m)
}
func (m *FinishFirstDayTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishFirstDayTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_FinishFirstDayTaskReq proto.InternalMessageInfo

func (m *FinishFirstDayTaskReq) GetMasterId() uint32 {
	if m != nil {
		return m.MasterId
	}
	return 0
}

func (m *FinishFirstDayTaskReq) GetApprenticeId() uint32 {
	if m != nil {
		return m.ApprenticeId
	}
	return 0
}

func (m *FinishFirstDayTaskReq) GetDatetime() int64 {
	if m != nil {
		return m.Datetime
	}
	return 0
}

type FinishFirstDayTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishFirstDayTaskResp) Reset()         { *m = FinishFirstDayTaskResp{} }
func (m *FinishFirstDayTaskResp) String() string { return proto.CompactTextString(m) }
func (*FinishFirstDayTaskResp) ProtoMessage()    {}
func (*FinishFirstDayTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_master_apprentice_f861af74279aa3b9, []int{78}
}
func (m *FinishFirstDayTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishFirstDayTaskResp.Unmarshal(m, b)
}
func (m *FinishFirstDayTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishFirstDayTaskResp.Marshal(b, m, deterministic)
}
func (dst *FinishFirstDayTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishFirstDayTaskResp.Merge(dst, src)
}
func (m *FinishFirstDayTaskResp) XXX_Size() int {
	return xxx_messageInfo_FinishFirstDayTaskResp.Size(m)
}
func (m *FinishFirstDayTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishFirstDayTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_FinishFirstDayTaskResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BatchGetOnlineReserveApprenticeReq)(nil), "masterapprentice.BatchGetOnlineReserveApprenticeReq")
	proto.RegisterType((*BatchGetOnlineReserveApprenticeResp)(nil), "masterapprentice.BatchGetOnlineReserveApprenticeResp")
	proto.RegisterType((*BatchDelReserveApprenticeReq)(nil), "masterapprentice.BatchDelReserveApprenticeReq")
	proto.RegisterType((*BatchDelReserveApprenticeResp)(nil), "masterapprentice.BatchDelReserveApprenticeResp")
	proto.RegisterType((*BatchAddMasterReq)(nil), "masterapprentice.BatchAddMasterReq")
	proto.RegisterType((*BatchAddMasterResp)(nil), "masterapprentice.BatchAddMasterResp")
	proto.RegisterType((*BatchDelMasterReq)(nil), "masterapprentice.BatchDelMasterReq")
	proto.RegisterType((*BatchDelMasterResp)(nil), "masterapprentice.BatchDelMasterResp")
	proto.RegisterType((*IsApprenticeReq)(nil), "masterapprentice.IsApprenticeReq")
	proto.RegisterType((*IsApprenticeResp)(nil), "masterapprentice.IsApprenticeResp")
	proto.RegisterType((*IsMasterReq)(nil), "masterapprentice.IsMasterReq")
	proto.RegisterType((*IsMasterResp)(nil), "masterapprentice.IsMasterResp")
	proto.RegisterType((*IsMasterInValidTimeReq)(nil), "masterapprentice.IsMasterInValidTimeReq")
	proto.RegisterType((*IsMasterInValidTimeResp)(nil), "masterapprentice.IsMasterInValidTimeResp")
	proto.RegisterType((*GetApprenticesListReq)(nil), "masterapprentice.GetApprenticesListReq")
	proto.RegisterType((*GetApprenticesListResp)(nil), "masterapprentice.GetApprenticesListResp")
	proto.RegisterType((*GetApprenticeStatusReq)(nil), "masterapprentice.GetApprenticeStatusReq")
	proto.RegisterType((*GetApprenticeStatusResp)(nil), "masterapprentice.GetApprenticeStatusResp")
	proto.RegisterType((*GetUserMasterInfoReq)(nil), "masterapprentice.GetUserMasterInfoReq")
	proto.RegisterType((*GetUserMasterInfoResp)(nil), "masterapprentice.GetUserMasterInfoResp")
	proto.RegisterType((*BatchGetUserMasterInfoReq)(nil), "masterapprentice.BatchGetUserMasterInfoReq")
	proto.RegisterType((*BatchGetUserMasterInfoResp)(nil), "masterapprentice.BatchGetUserMasterInfoResp")
	proto.RegisterMapType((map[uint32]uint32)(nil), "masterapprentice.BatchGetUserMasterInfoResp.ApprenticeToMasterEntry")
	proto.RegisterType((*MasterInviteReq)(nil), "masterapprentice.MasterInviteReq")
	proto.RegisterType((*MasterInviteResp)(nil), "masterapprentice.MasterInviteResp")
	proto.RegisterType((*EstablishShipReq)(nil), "masterapprentice.EstablishShipReq")
	proto.RegisterType((*EstablishShipResp)(nil), "masterapprentice.EstablishShipResp")
	proto.RegisterType((*GetApprenticeNumByMasterReq)(nil), "masterapprentice.GetApprenticeNumByMasterReq")
	proto.RegisterType((*GetApprenticeNumByMasterResp)(nil), "masterapprentice.GetApprenticeNumByMasterResp")
	proto.RegisterType((*GetTodayCompletedReq)(nil), "masterapprentice.GetTodayCompletedReq")
	proto.RegisterType((*GetTodayCompletedResp)(nil), "masterapprentice.GetTodayCompletedResp")
	proto.RegisterType((*IsNewMasterReq)(nil), "masterapprentice.IsNewMasterReq")
	proto.RegisterType((*IsNewMasterResp)(nil), "masterapprentice.IsNewMasterResp")
	proto.RegisterType((*DrawBalanceReq)(nil), "masterapprentice.DrawBalanceReq")
	proto.RegisterType((*DrawBalanceResp)(nil), "masterapprentice.DrawBalanceResp")
	proto.RegisterType((*GetUserBalanceByUserIDReq)(nil), "masterapprentice.GetUserBalanceByUserIDReq")
	proto.RegisterType((*GetUserBalanceByUserIDResp)(nil), "masterapprentice.GetUserBalanceByUserIDResp")
	proto.RegisterType((*OrderInfo)(nil), "masterapprentice.OrderInfo")
	proto.RegisterType((*GetOrderListByUserIDReq)(nil), "masterapprentice.GetOrderListByUserIDReq")
	proto.RegisterType((*LoadMore)(nil), "masterapprentice.LoadMore")
	proto.RegisterType((*GetOrderListByUserIDResp)(nil), "masterapprentice.GetOrderListByUserIDResp")
	proto.RegisterType((*GetOrderInfoByUserIDOrderIDReq)(nil), "masterapprentice.GetOrderInfoByUserIDOrderIDReq")
	proto.RegisterType((*GetOrderInfoByUserIDOrderIDResp)(nil), "masterapprentice.GetOrderInfoByUserIDOrderIDResp")
	proto.RegisterType((*GetOrderInfoByOrderIDReq)(nil), "masterapprentice.GetOrderInfoByOrderIDReq")
	proto.RegisterType((*GetOrderInfoByOrderIDResp)(nil), "masterapprentice.GetOrderInfoByOrderIDResp")
	proto.RegisterType((*UpdateOrderInfoByOrderIDReq)(nil), "masterapprentice.UpdateOrderInfoByOrderIDReq")
	proto.RegisterType((*UpdateOrderInfoByOrderIDResp)(nil), "masterapprentice.UpdateOrderInfoByOrderIDResp")
	proto.RegisterType((*BindWXUserPayInfoReq)(nil), "masterapprentice.BindWXUserPayInfoReq")
	proto.RegisterType((*BindWXUserPayInfoResp)(nil), "masterapprentice.BindWXUserPayInfoResp")
	proto.RegisterType((*GetBindingInfoReq)(nil), "masterapprentice.GetBindingInfoReq")
	proto.RegisterType((*GetBindingInfoResp)(nil), "masterapprentice.GetBindingInfoResp")
	proto.RegisterType((*HistoryTaskReq)(nil), "masterapprentice.HistoryTaskReq")
	proto.RegisterType((*HistoryTaskInfo)(nil), "masterapprentice.HistoryTaskInfo")
	proto.RegisterType((*HistoryTaskRsp)(nil), "masterapprentice.HistoryTaskRsp")
	proto.RegisterType((*CurrentTaskReq)(nil), "masterapprentice.CurrentTaskReq")
	proto.RegisterType((*CurrentTaskInfo)(nil), "masterapprentice.CurrentTaskInfo")
	proto.RegisterType((*CurrentTaskRsp)(nil), "masterapprentice.CurrentTaskRsp")
	proto.RegisterType((*MasterFirstHomeReq)(nil), "masterapprentice.MasterFirstHomeReq")
	proto.RegisterType((*MasterFirstHomeResp)(nil), "masterapprentice.MasterFirstHomeResp")
	proto.RegisterType((*InsertMasterFirstHomeReq)(nil), "masterapprentice.InsertMasterFirstHomeReq")
	proto.RegisterType((*InsertMasterFirstHomeResp)(nil), "masterapprentice.InsertMasterFirstHomeResp")
	proto.RegisterType((*GetActConfigReq)(nil), "masterapprentice.GetActConfigReq")
	proto.RegisterType((*GetActConfigResp)(nil), "masterapprentice.GetActConfigResp")
	proto.RegisterType((*MasterInitForWebReq)(nil), "masterapprentice.MasterInitForWebReq")
	proto.RegisterType((*MasterInitForWebResp)(nil), "masterapprentice.MasterInitForWebResp")
	proto.RegisterType((*GetAllApprenticeInfosInProcessReq)(nil), "masterapprentice.GetAllApprenticeInfosInProcessReq")
	proto.RegisterType((*ApprenticeInfo)(nil), "masterapprentice.ApprenticeInfo")
	proto.RegisterType((*GetAllApprenticeInfosInProcessResp)(nil), "masterapprentice.GetAllApprenticeInfosInProcessResp")
	proto.RegisterType((*ReceiveGiftReq)(nil), "masterapprentice.ReceiveGiftReq")
	proto.RegisterType((*ReceiveGiftResp)(nil), "masterapprentice.ReceiveGiftResp")
	proto.RegisterType((*MasterMonitorInTodayReq)(nil), "masterapprentice.MasterMonitorInTodayReq")
	proto.RegisterType((*MasterLimitInfo)(nil), "masterapprentice.MasterLimitInfo")
	proto.RegisterType((*MasterMonitorInTodayResp)(nil), "masterapprentice.MasterMonitorInTodayResp")
	proto.RegisterType((*GetActivityStatisticsReq)(nil), "masterapprentice.GetActivityStatisticsReq")
	proto.RegisterType((*ActivityStatistics)(nil), "masterapprentice.ActivityStatistics")
	proto.RegisterType((*GetActivityStatisticsResp)(nil), "masterapprentice.GetActivityStatisticsResp")
	proto.RegisterType((*SameApprenticeReq)(nil), "masterapprentice.SameApprenticeReq")
	proto.RegisterType((*SameApprenticeResp)(nil), "masterapprentice.SameApprenticeResp")
	proto.RegisterType((*FinishFirstDayTaskReq)(nil), "masterapprentice.FinishFirstDayTaskReq")
	proto.RegisterType((*FinishFirstDayTaskResp)(nil), "masterapprentice.FinishFirstDayTaskResp")
	proto.RegisterEnum("masterapprentice.ApprenticeStatus", ApprenticeStatus_name, ApprenticeStatus_value)
	proto.RegisterEnum("masterapprentice.HistoryTaskType", HistoryTaskType_name, HistoryTaskType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MasterApprenticeClient is the client API for MasterApprentice service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MasterApprenticeClient interface {
	// 根据注册时间返回在线徒弟
	BatchGetOnlineReserveApprentice(ctx context.Context, in *BatchGetOnlineReserveApprenticeReq, opts ...grpc.CallOption) (*BatchGetOnlineReserveApprenticeResp, error)
	// 根据注册时间返回在线徒弟 年龄分层 召回
	BatchGetOnlineReserveApprenticeV2(ctx context.Context, in *BatchGetOnlineReserveApprenticeReq, opts ...grpc.CallOption) (*BatchGetOnlineReserveApprenticeResp, error)
	// 批量删除徒弟
	BatchDelReserveApprentice(ctx context.Context, in *BatchDelReserveApprenticeReq, opts ...grpc.CallOption) (*BatchDelReserveApprenticeResp, error)
	// 批量添加师傅
	BatchAddMaster(ctx context.Context, in *BatchAddMasterReq, opts ...grpc.CallOption) (*BatchAddMasterResp, error)
	// 批量删师傅
	BatchDelMaster(ctx context.Context, in *BatchDelMasterReq, opts ...grpc.CallOption) (*BatchDelMasterResp, error)
	// 是否在徒弟大厅中
	IsApprentice(ctx context.Context, in *IsApprenticeReq, opts ...grpc.CallOption) (*IsApprenticeResp, error)
	// 是否有收徒资格
	IsMaster(ctx context.Context, in *IsMasterReq, opts ...grpc.CallOption) (*IsMasterResp, error)
	// 是师父&在收徒时间内
	IsMasterInValidTime(ctx context.Context, in *IsMasterInValidTimeReq, opts ...grpc.CallOption) (*IsMasterInValidTimeResp, error)
	// 获取师父的徒弟列表 进行中的
	GetApprenticesList(ctx context.Context, in *GetApprenticesListReq, opts ...grpc.CallOption) (*GetApprenticesListResp, error)
	// 获取徒弟状态
	GetApprenticeStatus(ctx context.Context, in *GetApprenticeStatusReq, opts ...grpc.CallOption) (*GetApprenticeStatusResp, error)
	// 获取师父信息
	GetUserMasterInfo(ctx context.Context, in *GetUserMasterInfoReq, opts ...grpc.CallOption) (*GetUserMasterInfoResp, error)
	// 批量获取师父信息
	BatchGetUserMasterInfo(ctx context.Context, in *BatchGetUserMasterInfoReq, opts ...grpc.CallOption) (*BatchGetUserMasterInfoResp, error)
	// 发出邀请
	MasterInvite(ctx context.Context, in *MasterInviteReq, opts ...grpc.CallOption) (*MasterInviteResp, error)
	// 建立师徒关系 -- 徒弟点击接受邀请
	EstablishShip(ctx context.Context, in *EstablishShipReq, opts ...grpc.CallOption) (*EstablishShipResp, error)
	// 获取师父的绑定的徒弟数(进行中)
	GetApprenticeNumByMaster(ctx context.Context, in *GetApprenticeNumByMasterReq, opts ...grpc.CallOption) (*GetApprenticeNumByMasterResp, error)
	// 今日已完成
	GetTodayCompleted(ctx context.Context, in *GetTodayCompletedReq, opts ...grpc.CallOption) (*GetTodayCompletedResp, error)
	// 当天的新鲜师父:现在是领取成师礼包当天
	IsNewMaster(ctx context.Context, in *IsNewMasterReq, opts ...grpc.CallOption) (*IsNewMasterResp, error)
	// 针对同一个用户两次邀请发送时间<5min
	SameApprentice(ctx context.Context, in *SameApprenticeReq, opts ...grpc.CallOption) (*SameApprenticeResp, error)
	// 用户提现
	DrawBalance(ctx context.Context, in *DrawBalanceReq, opts ...grpc.CallOption) (*DrawBalanceResp, error)
	// 获取余额
	GetUserBalanceByUserID(ctx context.Context, in *GetUserBalanceByUserIDReq, opts ...grpc.CallOption) (*GetUserBalanceByUserIDResp, error)
	// 获取其订单列表
	GetOrderListByUserID(ctx context.Context, in *GetOrderListByUserIDReq, opts ...grpc.CallOption) (*GetOrderListByUserIDResp, error)
	// 获取订单信息
	GetOrderInfoByUserIDOrderID(ctx context.Context, in *GetOrderInfoByUserIDOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByUserIDOrderIDResp, error)
	// 获取订单信息 供佣金平台反查
	GetOrderInfoByOrderID(ctx context.Context, in *GetOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByOrderIDResp, error)
	// 佣金平台更新佣金信息 将订单状态标记
	UpdateOrderInfoByOrderID(ctx context.Context, in *UpdateOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*UpdateOrderInfoByOrderIDResp, error)
	// 绑定微信信息
	BindWXUserPayInfo(ctx context.Context, in *BindWXUserPayInfoReq, opts ...grpc.CallOption) (*BindWXUserPayInfoResp, error)
	// 查询微信绑定信息
	GetBindingInfo(ctx context.Context, in *GetBindingInfoReq, opts ...grpc.CallOption) (*GetBindingInfoResp, error)
	GetCurrentTask(ctx context.Context, in *CurrentTaskReq, opts ...grpc.CallOption) (*CurrentTaskRsp, error)
	GetHistoryTask(ctx context.Context, in *HistoryTaskReq, opts ...grpc.CallOption) (*HistoryTaskRsp, error)
	// 获取师父是否第一次进师徒关系首页
	MasterFirstHome(ctx context.Context, in *MasterFirstHomeReq, opts ...grpc.CallOption) (*MasterFirstHomeResp, error)
	// 设置师父第一次进师徒关系首页
	InsertMasterFirstHome(ctx context.Context, in *InsertMasterFirstHomeReq, opts ...grpc.CallOption) (*InsertMasterFirstHomeResp, error)
	GetActConfig(ctx context.Context, in *GetActConfigReq, opts ...grpc.CallOption) (*GetActConfigResp, error)
	// web 首页数据
	MasterInitForWeb(ctx context.Context, in *MasterInitForWebReq, opts ...grpc.CallOption) (*MasterInitForWebResp, error)
	// 获取进行中的师傅的所有徒弟信息
	GetAllApprenticeInfosInProcess(ctx context.Context, in *GetAllApprenticeInfosInProcessReq, opts ...grpc.CallOption) (*GetAllApprenticeInfosInProcessResp, error)
	// 成师礼包
	ReceiveGift(ctx context.Context, in *ReceiveGiftReq, opts ...grpc.CallOption) (*ReceiveGiftResp, error)
	// 监控 师父的收益和提现
	MasterMonitorInToday(ctx context.Context, in *MasterMonitorInTodayReq, opts ...grpc.CallOption) (*MasterMonitorInTodayResp, error)
	// 活动数据统计（活动成本和师父收益）
	GetActivityStatistics(ctx context.Context, in *GetActivityStatisticsReq, opts ...grpc.CallOption) (*GetActivityStatisticsResp, error)
	// 完成首日师徒任务
	FinishFirstDayTask(ctx context.Context, in *FinishFirstDayTaskReq, opts ...grpc.CallOption) (*FinishFirstDayTaskResp, error)
}

type masterApprenticeClient struct {
	cc *grpc.ClientConn
}

func NewMasterApprenticeClient(cc *grpc.ClientConn) MasterApprenticeClient {
	return &masterApprenticeClient{cc}
}

func (c *masterApprenticeClient) BatchGetOnlineReserveApprentice(ctx context.Context, in *BatchGetOnlineReserveApprenticeReq, opts ...grpc.CallOption) (*BatchGetOnlineReserveApprenticeResp, error) {
	out := new(BatchGetOnlineReserveApprenticeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BatchGetOnlineReserveApprentice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) BatchGetOnlineReserveApprenticeV2(ctx context.Context, in *BatchGetOnlineReserveApprenticeReq, opts ...grpc.CallOption) (*BatchGetOnlineReserveApprenticeResp, error) {
	out := new(BatchGetOnlineReserveApprenticeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BatchGetOnlineReserveApprenticeV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) BatchDelReserveApprentice(ctx context.Context, in *BatchDelReserveApprenticeReq, opts ...grpc.CallOption) (*BatchDelReserveApprenticeResp, error) {
	out := new(BatchDelReserveApprenticeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BatchDelReserveApprentice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) BatchAddMaster(ctx context.Context, in *BatchAddMasterReq, opts ...grpc.CallOption) (*BatchAddMasterResp, error) {
	out := new(BatchAddMasterResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BatchAddMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) BatchDelMaster(ctx context.Context, in *BatchDelMasterReq, opts ...grpc.CallOption) (*BatchDelMasterResp, error) {
	out := new(BatchDelMasterResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BatchDelMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) IsApprentice(ctx context.Context, in *IsApprenticeReq, opts ...grpc.CallOption) (*IsApprenticeResp, error) {
	out := new(IsApprenticeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/IsApprentice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) IsMaster(ctx context.Context, in *IsMasterReq, opts ...grpc.CallOption) (*IsMasterResp, error) {
	out := new(IsMasterResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/IsMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) IsMasterInValidTime(ctx context.Context, in *IsMasterInValidTimeReq, opts ...grpc.CallOption) (*IsMasterInValidTimeResp, error) {
	out := new(IsMasterInValidTimeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/IsMasterInValidTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetApprenticesList(ctx context.Context, in *GetApprenticesListReq, opts ...grpc.CallOption) (*GetApprenticesListResp, error) {
	out := new(GetApprenticesListResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetApprenticesList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetApprenticeStatus(ctx context.Context, in *GetApprenticeStatusReq, opts ...grpc.CallOption) (*GetApprenticeStatusResp, error) {
	out := new(GetApprenticeStatusResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetApprenticeStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetUserMasterInfo(ctx context.Context, in *GetUserMasterInfoReq, opts ...grpc.CallOption) (*GetUserMasterInfoResp, error) {
	out := new(GetUserMasterInfoResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetUserMasterInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) BatchGetUserMasterInfo(ctx context.Context, in *BatchGetUserMasterInfoReq, opts ...grpc.CallOption) (*BatchGetUserMasterInfoResp, error) {
	out := new(BatchGetUserMasterInfoResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BatchGetUserMasterInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) MasterInvite(ctx context.Context, in *MasterInviteReq, opts ...grpc.CallOption) (*MasterInviteResp, error) {
	out := new(MasterInviteResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/MasterInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) EstablishShip(ctx context.Context, in *EstablishShipReq, opts ...grpc.CallOption) (*EstablishShipResp, error) {
	out := new(EstablishShipResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/EstablishShip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetApprenticeNumByMaster(ctx context.Context, in *GetApprenticeNumByMasterReq, opts ...grpc.CallOption) (*GetApprenticeNumByMasterResp, error) {
	out := new(GetApprenticeNumByMasterResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetApprenticeNumByMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetTodayCompleted(ctx context.Context, in *GetTodayCompletedReq, opts ...grpc.CallOption) (*GetTodayCompletedResp, error) {
	out := new(GetTodayCompletedResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetTodayCompleted", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) IsNewMaster(ctx context.Context, in *IsNewMasterReq, opts ...grpc.CallOption) (*IsNewMasterResp, error) {
	out := new(IsNewMasterResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/IsNewMaster", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) SameApprentice(ctx context.Context, in *SameApprenticeReq, opts ...grpc.CallOption) (*SameApprenticeResp, error) {
	out := new(SameApprenticeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/SameApprentice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) DrawBalance(ctx context.Context, in *DrawBalanceReq, opts ...grpc.CallOption) (*DrawBalanceResp, error) {
	out := new(DrawBalanceResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/DrawBalance", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetUserBalanceByUserID(ctx context.Context, in *GetUserBalanceByUserIDReq, opts ...grpc.CallOption) (*GetUserBalanceByUserIDResp, error) {
	out := new(GetUserBalanceByUserIDResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetUserBalanceByUserID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetOrderListByUserID(ctx context.Context, in *GetOrderListByUserIDReq, opts ...grpc.CallOption) (*GetOrderListByUserIDResp, error) {
	out := new(GetOrderListByUserIDResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetOrderListByUserID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetOrderInfoByUserIDOrderID(ctx context.Context, in *GetOrderInfoByUserIDOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByUserIDOrderIDResp, error) {
	out := new(GetOrderInfoByUserIDOrderIDResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetOrderInfoByUserIDOrderID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetOrderInfoByOrderID(ctx context.Context, in *GetOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*GetOrderInfoByOrderIDResp, error) {
	out := new(GetOrderInfoByOrderIDResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetOrderInfoByOrderID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) UpdateOrderInfoByOrderID(ctx context.Context, in *UpdateOrderInfoByOrderIDReq, opts ...grpc.CallOption) (*UpdateOrderInfoByOrderIDResp, error) {
	out := new(UpdateOrderInfoByOrderIDResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/UpdateOrderInfoByOrderID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) BindWXUserPayInfo(ctx context.Context, in *BindWXUserPayInfoReq, opts ...grpc.CallOption) (*BindWXUserPayInfoResp, error) {
	out := new(BindWXUserPayInfoResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/BindWXUserPayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetBindingInfo(ctx context.Context, in *GetBindingInfoReq, opts ...grpc.CallOption) (*GetBindingInfoResp, error) {
	out := new(GetBindingInfoResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetBindingInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetCurrentTask(ctx context.Context, in *CurrentTaskReq, opts ...grpc.CallOption) (*CurrentTaskRsp, error) {
	out := new(CurrentTaskRsp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetCurrentTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetHistoryTask(ctx context.Context, in *HistoryTaskReq, opts ...grpc.CallOption) (*HistoryTaskRsp, error) {
	out := new(HistoryTaskRsp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetHistoryTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) MasterFirstHome(ctx context.Context, in *MasterFirstHomeReq, opts ...grpc.CallOption) (*MasterFirstHomeResp, error) {
	out := new(MasterFirstHomeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/MasterFirstHome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) InsertMasterFirstHome(ctx context.Context, in *InsertMasterFirstHomeReq, opts ...grpc.CallOption) (*InsertMasterFirstHomeResp, error) {
	out := new(InsertMasterFirstHomeResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/InsertMasterFirstHome", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetActConfig(ctx context.Context, in *GetActConfigReq, opts ...grpc.CallOption) (*GetActConfigResp, error) {
	out := new(GetActConfigResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetActConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) MasterInitForWeb(ctx context.Context, in *MasterInitForWebReq, opts ...grpc.CallOption) (*MasterInitForWebResp, error) {
	out := new(MasterInitForWebResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/MasterInitForWeb", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetAllApprenticeInfosInProcess(ctx context.Context, in *GetAllApprenticeInfosInProcessReq, opts ...grpc.CallOption) (*GetAllApprenticeInfosInProcessResp, error) {
	out := new(GetAllApprenticeInfosInProcessResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetAllApprenticeInfosInProcess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) ReceiveGift(ctx context.Context, in *ReceiveGiftReq, opts ...grpc.CallOption) (*ReceiveGiftResp, error) {
	out := new(ReceiveGiftResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/ReceiveGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) MasterMonitorInToday(ctx context.Context, in *MasterMonitorInTodayReq, opts ...grpc.CallOption) (*MasterMonitorInTodayResp, error) {
	out := new(MasterMonitorInTodayResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/MasterMonitorInToday", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) GetActivityStatistics(ctx context.Context, in *GetActivityStatisticsReq, opts ...grpc.CallOption) (*GetActivityStatisticsResp, error) {
	out := new(GetActivityStatisticsResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/GetActivityStatistics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *masterApprenticeClient) FinishFirstDayTask(ctx context.Context, in *FinishFirstDayTaskReq, opts ...grpc.CallOption) (*FinishFirstDayTaskResp, error) {
	out := new(FinishFirstDayTaskResp)
	err := c.cc.Invoke(ctx, "/masterapprentice.MasterApprentice/FinishFirstDayTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MasterApprenticeServer is the server API for MasterApprentice service.
type MasterApprenticeServer interface {
	// 根据注册时间返回在线徒弟
	BatchGetOnlineReserveApprentice(context.Context, *BatchGetOnlineReserveApprenticeReq) (*BatchGetOnlineReserveApprenticeResp, error)
	// 根据注册时间返回在线徒弟 年龄分层 召回
	BatchGetOnlineReserveApprenticeV2(context.Context, *BatchGetOnlineReserveApprenticeReq) (*BatchGetOnlineReserveApprenticeResp, error)
	// 批量删除徒弟
	BatchDelReserveApprentice(context.Context, *BatchDelReserveApprenticeReq) (*BatchDelReserveApprenticeResp, error)
	// 批量添加师傅
	BatchAddMaster(context.Context, *BatchAddMasterReq) (*BatchAddMasterResp, error)
	// 批量删师傅
	BatchDelMaster(context.Context, *BatchDelMasterReq) (*BatchDelMasterResp, error)
	// 是否在徒弟大厅中
	IsApprentice(context.Context, *IsApprenticeReq) (*IsApprenticeResp, error)
	// 是否有收徒资格
	IsMaster(context.Context, *IsMasterReq) (*IsMasterResp, error)
	// 是师父&在收徒时间内
	IsMasterInValidTime(context.Context, *IsMasterInValidTimeReq) (*IsMasterInValidTimeResp, error)
	// 获取师父的徒弟列表 进行中的
	GetApprenticesList(context.Context, *GetApprenticesListReq) (*GetApprenticesListResp, error)
	// 获取徒弟状态
	GetApprenticeStatus(context.Context, *GetApprenticeStatusReq) (*GetApprenticeStatusResp, error)
	// 获取师父信息
	GetUserMasterInfo(context.Context, *GetUserMasterInfoReq) (*GetUserMasterInfoResp, error)
	// 批量获取师父信息
	BatchGetUserMasterInfo(context.Context, *BatchGetUserMasterInfoReq) (*BatchGetUserMasterInfoResp, error)
	// 发出邀请
	MasterInvite(context.Context, *MasterInviteReq) (*MasterInviteResp, error)
	// 建立师徒关系 -- 徒弟点击接受邀请
	EstablishShip(context.Context, *EstablishShipReq) (*EstablishShipResp, error)
	// 获取师父的绑定的徒弟数(进行中)
	GetApprenticeNumByMaster(context.Context, *GetApprenticeNumByMasterReq) (*GetApprenticeNumByMasterResp, error)
	// 今日已完成
	GetTodayCompleted(context.Context, *GetTodayCompletedReq) (*GetTodayCompletedResp, error)
	// 当天的新鲜师父:现在是领取成师礼包当天
	IsNewMaster(context.Context, *IsNewMasterReq) (*IsNewMasterResp, error)
	// 针对同一个用户两次邀请发送时间<5min
	SameApprentice(context.Context, *SameApprenticeReq) (*SameApprenticeResp, error)
	// 用户提现
	DrawBalance(context.Context, *DrawBalanceReq) (*DrawBalanceResp, error)
	// 获取余额
	GetUserBalanceByUserID(context.Context, *GetUserBalanceByUserIDReq) (*GetUserBalanceByUserIDResp, error)
	// 获取其订单列表
	GetOrderListByUserID(context.Context, *GetOrderListByUserIDReq) (*GetOrderListByUserIDResp, error)
	// 获取订单信息
	GetOrderInfoByUserIDOrderID(context.Context, *GetOrderInfoByUserIDOrderIDReq) (*GetOrderInfoByUserIDOrderIDResp, error)
	// 获取订单信息 供佣金平台反查
	GetOrderInfoByOrderID(context.Context, *GetOrderInfoByOrderIDReq) (*GetOrderInfoByOrderIDResp, error)
	// 佣金平台更新佣金信息 将订单状态标记
	UpdateOrderInfoByOrderID(context.Context, *UpdateOrderInfoByOrderIDReq) (*UpdateOrderInfoByOrderIDResp, error)
	// 绑定微信信息
	BindWXUserPayInfo(context.Context, *BindWXUserPayInfoReq) (*BindWXUserPayInfoResp, error)
	// 查询微信绑定信息
	GetBindingInfo(context.Context, *GetBindingInfoReq) (*GetBindingInfoResp, error)
	GetCurrentTask(context.Context, *CurrentTaskReq) (*CurrentTaskRsp, error)
	GetHistoryTask(context.Context, *HistoryTaskReq) (*HistoryTaskRsp, error)
	// 获取师父是否第一次进师徒关系首页
	MasterFirstHome(context.Context, *MasterFirstHomeReq) (*MasterFirstHomeResp, error)
	// 设置师父第一次进师徒关系首页
	InsertMasterFirstHome(context.Context, *InsertMasterFirstHomeReq) (*InsertMasterFirstHomeResp, error)
	GetActConfig(context.Context, *GetActConfigReq) (*GetActConfigResp, error)
	// web 首页数据
	MasterInitForWeb(context.Context, *MasterInitForWebReq) (*MasterInitForWebResp, error)
	// 获取进行中的师傅的所有徒弟信息
	GetAllApprenticeInfosInProcess(context.Context, *GetAllApprenticeInfosInProcessReq) (*GetAllApprenticeInfosInProcessResp, error)
	// 成师礼包
	ReceiveGift(context.Context, *ReceiveGiftReq) (*ReceiveGiftResp, error)
	// 监控 师父的收益和提现
	MasterMonitorInToday(context.Context, *MasterMonitorInTodayReq) (*MasterMonitorInTodayResp, error)
	// 活动数据统计（活动成本和师父收益）
	GetActivityStatistics(context.Context, *GetActivityStatisticsReq) (*GetActivityStatisticsResp, error)
	// 完成首日师徒任务
	FinishFirstDayTask(context.Context, *FinishFirstDayTaskReq) (*FinishFirstDayTaskResp, error)
}

func RegisterMasterApprenticeServer(s *grpc.Server, srv MasterApprenticeServer) {
	s.RegisterService(&_MasterApprentice_serviceDesc, srv)
}

func _MasterApprentice_BatchGetOnlineReserveApprentice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetOnlineReserveApprenticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BatchGetOnlineReserveApprentice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BatchGetOnlineReserveApprentice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BatchGetOnlineReserveApprentice(ctx, req.(*BatchGetOnlineReserveApprenticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_BatchGetOnlineReserveApprenticeV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetOnlineReserveApprenticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BatchGetOnlineReserveApprenticeV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BatchGetOnlineReserveApprenticeV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BatchGetOnlineReserveApprenticeV2(ctx, req.(*BatchGetOnlineReserveApprenticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_BatchDelReserveApprentice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelReserveApprenticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BatchDelReserveApprentice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BatchDelReserveApprentice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BatchDelReserveApprentice(ctx, req.(*BatchDelReserveApprenticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_BatchAddMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BatchAddMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BatchAddMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BatchAddMaster(ctx, req.(*BatchAddMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_BatchDelMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BatchDelMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BatchDelMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BatchDelMaster(ctx, req.(*BatchDelMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_IsApprentice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsApprenticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).IsApprentice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/IsApprentice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).IsApprentice(ctx, req.(*IsApprenticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_IsMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).IsMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/IsMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).IsMaster(ctx, req.(*IsMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_IsMasterInValidTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsMasterInValidTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).IsMasterInValidTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/IsMasterInValidTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).IsMasterInValidTime(ctx, req.(*IsMasterInValidTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetApprenticesList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApprenticesListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetApprenticesList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetApprenticesList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetApprenticesList(ctx, req.(*GetApprenticesListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetApprenticeStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApprenticeStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetApprenticeStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetApprenticeStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetApprenticeStatus(ctx, req.(*GetApprenticeStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetUserMasterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMasterInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetUserMasterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetUserMasterInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetUserMasterInfo(ctx, req.(*GetUserMasterInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_BatchGetUserMasterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserMasterInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BatchGetUserMasterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BatchGetUserMasterInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BatchGetUserMasterInfo(ctx, req.(*BatchGetUserMasterInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_MasterInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MasterInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).MasterInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/MasterInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).MasterInvite(ctx, req.(*MasterInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_EstablishShip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EstablishShipReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).EstablishShip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/EstablishShip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).EstablishShip(ctx, req.(*EstablishShipReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetApprenticeNumByMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApprenticeNumByMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetApprenticeNumByMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetApprenticeNumByMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetApprenticeNumByMaster(ctx, req.(*GetApprenticeNumByMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetTodayCompleted_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTodayCompletedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetTodayCompleted(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetTodayCompleted",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetTodayCompleted(ctx, req.(*GetTodayCompletedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_IsNewMaster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsNewMasterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).IsNewMaster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/IsNewMaster",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).IsNewMaster(ctx, req.(*IsNewMasterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_SameApprentice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SameApprenticeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).SameApprentice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/SameApprentice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).SameApprentice(ctx, req.(*SameApprenticeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_DrawBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DrawBalanceReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).DrawBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/DrawBalance",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).DrawBalance(ctx, req.(*DrawBalanceReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetUserBalanceByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserBalanceByUserIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetUserBalanceByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetUserBalanceByUserID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetUserBalanceByUserID(ctx, req.(*GetUserBalanceByUserIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetOrderListByUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderListByUserIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetOrderListByUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetOrderListByUserID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetOrderListByUserID(ctx, req.(*GetOrderListByUserIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetOrderInfoByUserIDOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderInfoByUserIDOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetOrderInfoByUserIDOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetOrderInfoByUserIDOrderID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetOrderInfoByUserIDOrderID(ctx, req.(*GetOrderInfoByUserIDOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetOrderInfoByOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderInfoByOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetOrderInfoByOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetOrderInfoByOrderID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetOrderInfoByOrderID(ctx, req.(*GetOrderInfoByOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_UpdateOrderInfoByOrderID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateOrderInfoByOrderIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).UpdateOrderInfoByOrderID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/UpdateOrderInfoByOrderID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).UpdateOrderInfoByOrderID(ctx, req.(*UpdateOrderInfoByOrderIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_BindWXUserPayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindWXUserPayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).BindWXUserPayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/BindWXUserPayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).BindWXUserPayInfo(ctx, req.(*BindWXUserPayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetBindingInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindingInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetBindingInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetBindingInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetBindingInfo(ctx, req.(*GetBindingInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetCurrentTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CurrentTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetCurrentTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetCurrentTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetCurrentTask(ctx, req.(*CurrentTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetHistoryTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HistoryTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetHistoryTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetHistoryTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetHistoryTask(ctx, req.(*HistoryTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_MasterFirstHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MasterFirstHomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).MasterFirstHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/MasterFirstHome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).MasterFirstHome(ctx, req.(*MasterFirstHomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_InsertMasterFirstHome_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InsertMasterFirstHomeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).InsertMasterFirstHome(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/InsertMasterFirstHome",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).InsertMasterFirstHome(ctx, req.(*InsertMasterFirstHomeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetActConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetActConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetActConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetActConfig(ctx, req.(*GetActConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_MasterInitForWeb_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MasterInitForWebReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).MasterInitForWeb(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/MasterInitForWeb",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).MasterInitForWeb(ctx, req.(*MasterInitForWebReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetAllApprenticeInfosInProcess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllApprenticeInfosInProcessReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetAllApprenticeInfosInProcess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetAllApprenticeInfosInProcess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetAllApprenticeInfosInProcess(ctx, req.(*GetAllApprenticeInfosInProcessReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_ReceiveGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReceiveGiftReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).ReceiveGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/ReceiveGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).ReceiveGift(ctx, req.(*ReceiveGiftReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_MasterMonitorInToday_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MasterMonitorInTodayReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).MasterMonitorInToday(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/MasterMonitorInToday",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).MasterMonitorInToday(ctx, req.(*MasterMonitorInTodayReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_GetActivityStatistics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActivityStatisticsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).GetActivityStatistics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/GetActivityStatistics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).GetActivityStatistics(ctx, req.(*GetActivityStatisticsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MasterApprentice_FinishFirstDayTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishFirstDayTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MasterApprenticeServer).FinishFirstDayTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/masterapprentice.MasterApprentice/FinishFirstDayTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MasterApprenticeServer).FinishFirstDayTask(ctx, req.(*FinishFirstDayTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MasterApprentice_serviceDesc = grpc.ServiceDesc{
	ServiceName: "masterapprentice.MasterApprentice",
	HandlerType: (*MasterApprenticeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetOnlineReserveApprentice",
			Handler:    _MasterApprentice_BatchGetOnlineReserveApprentice_Handler,
		},
		{
			MethodName: "BatchGetOnlineReserveApprenticeV2",
			Handler:    _MasterApprentice_BatchGetOnlineReserveApprenticeV2_Handler,
		},
		{
			MethodName: "BatchDelReserveApprentice",
			Handler:    _MasterApprentice_BatchDelReserveApprentice_Handler,
		},
		{
			MethodName: "BatchAddMaster",
			Handler:    _MasterApprentice_BatchAddMaster_Handler,
		},
		{
			MethodName: "BatchDelMaster",
			Handler:    _MasterApprentice_BatchDelMaster_Handler,
		},
		{
			MethodName: "IsApprentice",
			Handler:    _MasterApprentice_IsApprentice_Handler,
		},
		{
			MethodName: "IsMaster",
			Handler:    _MasterApprentice_IsMaster_Handler,
		},
		{
			MethodName: "IsMasterInValidTime",
			Handler:    _MasterApprentice_IsMasterInValidTime_Handler,
		},
		{
			MethodName: "GetApprenticesList",
			Handler:    _MasterApprentice_GetApprenticesList_Handler,
		},
		{
			MethodName: "GetApprenticeStatus",
			Handler:    _MasterApprentice_GetApprenticeStatus_Handler,
		},
		{
			MethodName: "GetUserMasterInfo",
			Handler:    _MasterApprentice_GetUserMasterInfo_Handler,
		},
		{
			MethodName: "BatchGetUserMasterInfo",
			Handler:    _MasterApprentice_BatchGetUserMasterInfo_Handler,
		},
		{
			MethodName: "MasterInvite",
			Handler:    _MasterApprentice_MasterInvite_Handler,
		},
		{
			MethodName: "EstablishShip",
			Handler:    _MasterApprentice_EstablishShip_Handler,
		},
		{
			MethodName: "GetApprenticeNumByMaster",
			Handler:    _MasterApprentice_GetApprenticeNumByMaster_Handler,
		},
		{
			MethodName: "GetTodayCompleted",
			Handler:    _MasterApprentice_GetTodayCompleted_Handler,
		},
		{
			MethodName: "IsNewMaster",
			Handler:    _MasterApprentice_IsNewMaster_Handler,
		},
		{
			MethodName: "SameApprentice",
			Handler:    _MasterApprentice_SameApprentice_Handler,
		},
		{
			MethodName: "DrawBalance",
			Handler:    _MasterApprentice_DrawBalance_Handler,
		},
		{
			MethodName: "GetUserBalanceByUserID",
			Handler:    _MasterApprentice_GetUserBalanceByUserID_Handler,
		},
		{
			MethodName: "GetOrderListByUserID",
			Handler:    _MasterApprentice_GetOrderListByUserID_Handler,
		},
		{
			MethodName: "GetOrderInfoByUserIDOrderID",
			Handler:    _MasterApprentice_GetOrderInfoByUserIDOrderID_Handler,
		},
		{
			MethodName: "GetOrderInfoByOrderID",
			Handler:    _MasterApprentice_GetOrderInfoByOrderID_Handler,
		},
		{
			MethodName: "UpdateOrderInfoByOrderID",
			Handler:    _MasterApprentice_UpdateOrderInfoByOrderID_Handler,
		},
		{
			MethodName: "BindWXUserPayInfo",
			Handler:    _MasterApprentice_BindWXUserPayInfo_Handler,
		},
		{
			MethodName: "GetBindingInfo",
			Handler:    _MasterApprentice_GetBindingInfo_Handler,
		},
		{
			MethodName: "GetCurrentTask",
			Handler:    _MasterApprentice_GetCurrentTask_Handler,
		},
		{
			MethodName: "GetHistoryTask",
			Handler:    _MasterApprentice_GetHistoryTask_Handler,
		},
		{
			MethodName: "MasterFirstHome",
			Handler:    _MasterApprentice_MasterFirstHome_Handler,
		},
		{
			MethodName: "InsertMasterFirstHome",
			Handler:    _MasterApprentice_InsertMasterFirstHome_Handler,
		},
		{
			MethodName: "GetActConfig",
			Handler:    _MasterApprentice_GetActConfig_Handler,
		},
		{
			MethodName: "MasterInitForWeb",
			Handler:    _MasterApprentice_MasterInitForWeb_Handler,
		},
		{
			MethodName: "GetAllApprenticeInfosInProcess",
			Handler:    _MasterApprentice_GetAllApprenticeInfosInProcess_Handler,
		},
		{
			MethodName: "ReceiveGift",
			Handler:    _MasterApprentice_ReceiveGift_Handler,
		},
		{
			MethodName: "MasterMonitorInToday",
			Handler:    _MasterApprentice_MasterMonitorInToday_Handler,
		},
		{
			MethodName: "GetActivityStatistics",
			Handler:    _MasterApprentice_GetActivityStatistics_Handler,
		},
		{
			MethodName: "FinishFirstDayTask",
			Handler:    _MasterApprentice_FinishFirstDayTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "master-apprentice/master-apprentice.proto",
}

func init() {
	proto.RegisterFile("master-apprentice/master-apprentice.proto", fileDescriptor_master_apprentice_f861af74279aa3b9)
}

var fileDescriptor_master_apprentice_f861af74279aa3b9 = []byte{
	// 3008 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x3a, 0x5b, 0x6f, 0x1b, 0xc7,
	0xd5, 0x5a, 0xd1, 0xb1, 0xa8, 0x43, 0x89, 0xa2, 0xc6, 0x17, 0xd1, 0x74, 0x12, 0x5b, 0x63, 0x3b,
	0x76, 0xe4, 0x44, 0x4a, 0x14, 0x3b, 0x37, 0x7c, 0xf9, 0x00, 0xdd, 0xac, 0xf0, 0xfb, 0x6c, 0xd9,
	0xa5, 0x69, 0x3b, 0x09, 0x10, 0xb0, 0x2b, 0xee, 0x48, 0x1a, 0x98, 0xdc, 0xdd, 0xec, 0x2c, 0xe5,
	0x10, 0x01, 0xf2, 0xd4, 0x02, 0x45, 0x81, 0x16, 0x45, 0xd1, 0x87, 0xa2, 0x45, 0x5f, 0xda, 0x97,
	0xbe, 0xf7, 0xa1, 0x7f, 0xa2, 0xff, 0xa0, 0x7f, 0xa6, 0x98, 0x1b, 0x77, 0x76, 0x77, 0x96, 0xa4,
	0xd2, 0x20, 0x6f, 0x9c, 0xb3, 0xe7, 0x9c, 0x39, 0x97, 0x39, 0x67, 0xce, 0x9c, 0x43, 0x78, 0xbb,
	0xef, 0xb2, 0x98, 0x44, 0xef, 0xba, 0x61, 0x18, 0x11, 0x3f, 0xa6, 0x5d, 0xb2, 0x91, 0x83, 0xac,
	0x87, 0x51, 0x10, 0x07, 0xa8, 0x26, 0x3f, 0x24, 0x70, 0xfc, 0x4b, 0x07, 0xf0, 0xb6, 0x1b, 0x77,
	0x4f, 0xf6, 0x49, 0xfc, 0xd8, 0xef, 0x51, 0x9f, 0xb4, 0x08, 0x23, 0xd1, 0x29, 0xd9, 0x1a, 0xe1,
	0xb4, 0xc8, 0x37, 0xe8, 0x22, 0xbc, 0x76, 0x48, 0x8e, 0xa9, 0x5f, 0x77, 0xae, 0x3b, 0x77, 0x16,
	0x5b, 0x72, 0xc1, 0xa1, 0x3d, 0xda, 0xa7, 0x71, 0x7d, 0x56, 0x42, 0xc5, 0x02, 0xad, 0xc2, 0x42,
	0x9f, 0xfa, 0x9d, 0x43, 0x1a, 0xc5, 0x27, 0x9e, 0x3b, 0xac, 0x97, 0xc4, 0xc7, 0x4a, 0x9f, 0xfa,
	0xdb, 0x0a, 0x84, 0x6a, 0x50, 0x1a, 0x50, 0xaf, 0x7e, 0x4e, 0x7c, 0xe1, 0x3f, 0xf1, 0x27, 0x70,
	0x63, 0xa2, 0x18, 0x2c, 0x44, 0x08, 0xce, 0x0d, 0xa8, 0xc7, 0xea, 0xce, 0xf5, 0xd2, 0x9d, 0xc5,
	0x96, 0xf8, 0x8d, 0x37, 0xe1, 0x75, 0x41, 0xba, 0x4b, 0x7a, 0x56, 0xd9, 0x6d, 0x34, 0xd7, 0xe0,
	0x8d, 0x31, 0x34, 0x2c, 0xc4, 0xb7, 0x61, 0x59, 0x20, 0x6c, 0x79, 0xde, 0x23, 0x61, 0xb3, 0x22,
	0x4e, 0x17, 0x01, 0x65, 0x11, 0x0d, 0xf2, 0x5d, 0xd2, 0x9b, 0x8e, 0xdc, 0x40, 0x64, 0x21, 0xbe,
	0x01, 0x4b, 0x4d, 0x96, 0xd6, 0x42, 0x99, 0xcc, 0x49, 0x4c, 0xf6, 0x11, 0xd4, 0xd2, 0x48, 0x2c,
	0x44, 0x37, 0x60, 0x91, 0xb2, 0x4e, 0xe2, 0x5f, 0x81, 0x5f, 0x6e, 0x2d, 0x50, 0x03, 0x11, 0x5f,
	0x83, 0x4a, 0x93, 0x25, 0x62, 0xe5, 0x39, 0xdf, 0x85, 0x85, 0x04, 0x81, 0x85, 0xe8, 0x2a, 0xcc,
	0x53, 0xd6, 0x91, 0x67, 0x47, 0x71, 0x2c, 0x53, 0x85, 0x80, 0xd7, 0xe0, 0xb2, 0x46, 0x6e, 0xfa,
	0xcf, 0xdd, 0x1e, 0xf5, 0xda, 0xb4, 0x5f, 0x20, 0xf2, 0x87, 0xb0, 0x62, 0xc5, 0x9d, 0xb4, 0xc7,
	0xdb, 0x70, 0x69, 0x9f, 0xc4, 0x89, 0x0a, 0xec, 0x21, 0x65, 0xb1, 0x7d, 0x8b, 0xff, 0x83, 0xcb,
	0x36, 0x54, 0x16, 0xa2, 0xf7, 0xe0, 0x62, 0x62, 0x18, 0xd6, 0x19, 0x50, 0xaf, 0xd3, 0xa3, 0x2c,
	0xae, 0x9f, 0x13, 0xee, 0x40, 0xc6, 0xb7, 0x67, 0xd4, 0xe3, 0x54, 0xf8, 0x67, 0x19, 0x5e, 0x4f,
	0x63, 0x37, 0x1e, 0x30, 0xeb, 0xbe, 0xe8, 0x16, 0x54, 0x13, 0x0e, 0x9c, 0xb9, 0x0a, 0x8a, 0xc5,
	0x04, 0xfa, 0x8c, 0x7a, 0xf8, 0xb7, 0x0e, 0xac, 0x58, 0x79, 0xb2, 0xf0, 0x07, 0x33, 0x45, 0x9f,
	0xc2, 0x79, 0x26, 0xd8, 0x88, 0x58, 0xab, 0x6e, 0xe2, 0xf5, 0x6c, 0x9c, 0xaf, 0xe7, 0x36, 0x54,
	0x14, 0xf8, 0x33, 0xb8, 0xb8, 0x4f, 0xe2, 0x67, 0x8c, 0x44, 0xda, 0x2f, 0x47, 0x01, 0xd7, 0x30,
	0xbf, 0xb5, 0x63, 0xd3, 0xe7, 0x43, 0xe1, 0x99, 0x2c, 0x39, 0x0b, 0xd1, 0x1b, 0x00, 0x52, 0x08,
	0x43, 0xec, 0x79, 0x09, 0xe1, 0x74, 0xbb, 0x70, 0x45, 0xc7, 0x7b, 0x7e, 0xef, 0xdb, 0xb0, 0x94,
	0xde, 0x5b, 0xc7, 0x4c, 0x35, 0xb5, 0x39, 0xc3, 0xff, 0x76, 0xa0, 0x51, 0xc4, 0x86, 0x85, 0xe8,
	0xd4, 0xf4, 0x78, 0x27, 0x0e, 0xf4, 0xf1, 0x9a, 0xbd, 0x5e, 0xba, 0x53, 0xd9, 0xdc, 0xcd, 0x5b,
	0xa9, 0x98, 0x97, 0x61, 0xc0, 0x76, 0x20, 0x3f, 0xed, 0xf9, 0x71, 0x34, 0x34, 0xcf, 0x8d, 0xfe,
	0xd0, 0xd8, 0x83, 0x95, 0x02, 0x74, 0xee, 0xe3, 0x97, 0x64, 0xa8, 0x7d, 0xfc, 0x92, 0x0c, 0x79,
	0x12, 0x3d, 0x75, 0x7b, 0x03, 0xa2, 0x93, 0xa8, 0x58, 0x7c, 0x3a, 0xfb, 0xb1, 0x83, 0x7f, 0xe5,
	0xc0, 0x92, 0x96, 0xe2, 0x94, 0xc6, 0x22, 0xa6, 0xd2, 0x66, 0x75, 0x32, 0x66, 0x9d, 0xf6, 0xc0,
	0x6c, 0xc0, 0x05, 0x03, 0xcd, 0xa7, 0xdd, 0x97, 0xbe, 0xdb, 0x27, 0xe2, 0xf4, 0xcc, 0x9b, 0x1a,
	0x1d, 0xa8, 0x2f, 0xf8, 0x7d, 0xa8, 0xa5, 0x25, 0x91, 0x1e, 0xa6, 0x62, 0xd5, 0xe9, 0xfa, 0xb1,
	0x16, 0x45, 0x42, 0x76, 0xfc, 0x18, 0x7f, 0x01, 0xb5, 0x3d, 0x16, 0xbb, 0x87, 0x3d, 0xca, 0x4e,
	0x9e, 0x9e, 0xd0, 0xf0, 0x47, 0x93, 0x1e, 0xdf, 0x85, 0xe5, 0x0c, 0x67, 0x16, 0xa2, 0xcb, 0x70,
	0x3e, 0x22, 0x6c, 0xd0, 0x8b, 0x55, 0xf2, 0x50, 0x2b, 0xfc, 0x3f, 0x70, 0x35, 0x15, 0x6f, 0x07,
	0x83, 0xfe, 0xf6, 0x30, 0x49, 0x7e, 0xe3, 0x25, 0xc2, 0xef, 0xc1, 0xeb, 0xc5, 0xd4, 0x32, 0x64,
	0xb9, 0xf2, 0x52, 0x4c, 0xfe, 0x13, 0xdf, 0x17, 0xf1, 0xd4, 0x0e, 0x3c, 0x77, 0xb8, 0x13, 0xf4,
	0xc3, 0x1e, 0x89, 0x89, 0x37, 0xc5, 0x46, 0xfb, 0x22, 0x8e, 0xb2, 0x64, 0x2c, 0x44, 0xeb, 0x29,
	0x57, 0x8d, 0x92, 0x96, 0x8c, 0x87, 0xe5, 0x94, 0x61, 0x44, 0xce, 0xda, 0x80, 0x6a, 0x93, 0x1d,
	0x90, 0x57, 0x53, 0xab, 0x78, 0x9b, 0xdf, 0x35, 0x06, 0x01, 0x0b, 0xf9, 0x91, 0xa4, 0x1c, 0xa4,
	0x4c, 0x29, 0x17, 0xf8, 0x21, 0x54, 0x77, 0x23, 0xf7, 0xd5, 0xb6, 0xdb, 0x73, 0x7d, 0x79, 0x27,
	0xad, 0xc0, 0xdc, 0x80, 0x91, 0xa8, 0x33, 0x62, 0x7b, 0x9e, 0x2f, 0x9b, 0x1e, 0x2f, 0x01, 0xbc,
	0xc8, 0x7d, 0xd5, 0x39, 0x94, 0xb8, 0xc2, 0x3e, 0xa5, 0x56, 0xc5, 0x4b, 0xc8, 0xf1, 0x03, 0x58,
	0x4a, 0x71, 0x63, 0x21, 0xaa, 0xc3, 0x5c, 0x9f, 0x30, 0xe6, 0x1e, 0xcb, 0x6b, 0x6b, 0xbe, 0xa5,
	0x97, 0xe8, 0x0a, 0x94, 0x83, 0xc8, 0x93, 0x3b, 0xcd, 0xca, 0x4f, 0x62, 0xdd, 0xf4, 0xf0, 0x3d,
	0xb8, 0xa2, 0x02, 0x56, 0xb1, 0xda, 0x1e, 0xf2, 0x45, 0x73, 0x77, 0x9c, 0x80, 0xf8, 0x9f, 0x0e,
	0x34, 0x8a, 0xc8, 0xe4, 0x35, 0x1a, 0x07, 0xb1, 0xdb, 0x1b, 0x29, 0xe0, 0x08, 0x05, 0x16, 0x04,
	0x50, 0x11, 0xa0, 0xbb, 0xb0, 0xec, 0x9e, 0xba, 0xb4, 0xe7, 0x1e, 0xf6, 0x48, 0x46, 0xd3, 0xda,
	0xe8, 0x83, 0x46, 0xde, 0x80, 0x0b, 0x03, 0x3f, 0x8f, 0x5e, 0x12, 0xe8, 0xc8, 0xf8, 0xa4, 0x09,
	0x2e, 0x8f, 0x72, 0xfa, 0x39, 0x81, 0xa3, 0xf3, 0xf5, 0x9f, 0x66, 0x61, 0xfe, 0xb1, 0xd0, 0xdd,
	0x3f, 0x0a, 0x8a, 0x3d, 0x50, 0x6c, 0x31, 0x83, 0x73, 0xc9, 0xe4, 0x8c, 0xae, 0x41, 0xc5, 0x0f,
	0x12, 0x9f, 0xc9, 0x6d, 0xc1, 0x0f, 0x5e, 0x19, 0x22, 0xb9, 0xfd, 0x60, 0xe0, 0xc7, 0xf5, 0xd7,
	0x24, 0xa1, 0x5c, 0xf1, 0xba, 0x26, 0x1e, 0x86, 0xa4, 0x7e, 0x5e, 0x40, 0xc5, 0x6f, 0xd4, 0x80,
	0xb2, 0x47, 0x58, 0x37, 0xa2, 0x87, 0xa4, 0x3e, 0x27, 0xf6, 0x1f, 0xad, 0x65, 0xa8, 0xba, 0x2c,
	0xf0, 0xeb, 0x65, 0xf1, 0x45, 0xad, 0xb8, 0x00, 0xdd, 0x88, 0xb8, 0x31, 0xe9, 0xc4, 0xb4, 0x4f,
	0xea, 0xf3, 0x52, 0x00, 0x09, 0xe2, 0x75, 0x02, 0x47, 0x18, 0x84, 0xde, 0x08, 0x01, 0x24, 0x82,
	0x04, 0x71, 0x04, 0xec, 0x8a, 0xcb, 0x55, 0x98, 0x87, 0x07, 0xc3, 0x34, 0x47, 0x21, 0x5d, 0xc4,
	0x96, 0x74, 0x11, 0xbb, 0x02, 0x73, 0x3d, 0x97, 0xc5, 0x1c, 0x5d, 0x59, 0x89, 0x2f, 0x9b, 0x1e,
	0x6e, 0x43, 0xf9, 0x61, 0xe0, 0x7a, 0x8f, 0x82, 0x48, 0x1c, 0xcb, 0x13, 0x97, 0x75, 0x7c, 0xf2,
	0xad, 0xce, 0x3a, 0x73, 0x27, 0x2e, 0x3b, 0x20, 0xdf, 0xa6, 0xe8, 0x67, 0x4d, 0xfa, 0x64, 0xbb,
	0x92, 0xb1, 0x1d, 0xfe, 0xbb, 0x03, 0x75, 0xbb, 0xe4, 0x2c, 0x2c, 0x16, 0xfd, 0x53, 0x00, 0xe9,
	0x64, 0x91, 0x12, 0xe4, 0xad, 0x76, 0x35, 0x7f, 0xab, 0x8d, 0x8e, 0x4b, 0x6b, 0x3e, 0xd0, 0x1b,
	0xa0, 0x8f, 0x60, 0xbe, 0x17, 0xb8, 0x5e, 0xa7, 0x1f, 0x44, 0xf2, 0x18, 0x56, 0x36, 0x1b, 0x79,
	0x52, 0xad, 0x6a, 0xab, 0xdc, 0x53, 0xbf, 0x70, 0x1b, 0xde, 0xd4, 0x92, 0x72, 0x9e, 0x5a, 0x52,
	0x09, 0x18, 0x6f, 0xea, 0x31, 0x61, 0xfc, 0x35, 0x5c, 0x1b, 0xcb, 0x95, 0x85, 0x89, 0xb6, 0xd4,
	0x3f, 0x0a, 0x04, 0xe7, 0xa9, 0xb4, 0xe5, 0x3f, 0xf1, 0xfd, 0xc4, 0xbc, 0x92, 0xbd, 0x21, 0xae,
	0x29, 0x95, 0x93, 0x96, 0xea, 0x85, 0x48, 0x2e, 0x36, 0xb2, 0xff, 0x52, 0x9e, 0x13, 0xb8, 0xfa,
	0x4c, 0x1c, 0xdb, 0xb3, 0x8a, 0x64, 0x44, 0xef, 0x6c, 0x2a, 0x7a, 0x93, 0xa0, 0x2a, 0x99, 0x41,
	0x85, 0x3f, 0x86, 0xd7, 0x8b, 0x77, 0x1a, 0x97, 0x74, 0xf1, 0x97, 0x70, 0x71, 0x9b, 0xfa, 0xde,
	0x8b, 0x2f, 0xb8, 0x2b, 0x9e, 0xb8, 0x43, 0x5d, 0x9d, 0x15, 0xba, 0x17, 0xc1, 0xb9, 0x6e, 0xe0,
	0x11, 0xe5, 0x5a, 0xf1, 0x9b, 0x1f, 0x77, 0x2e, 0xa0, 0xae, 0x2d, 0xe4, 0x02, 0x7f, 0x0f, 0x97,
	0x2c, 0xac, 0xe5, 0x2d, 0xae, 0xb4, 0x93, 0xc2, 0x18, 0xda, 0x05, 0x21, 0xf1, 0x9b, 0xfa, 0xdc,
	0xa8, 0x15, 0x7f, 0x35, 0xf0, 0xea, 0xa5, 0x63, 0x94, 0x2f, 0x65, 0x0e, 0x38, 0x70, 0xfb, 0xc4,
	0x54, 0xed, 0x5c, 0x5a, 0xb5, 0xe7, 0xb0, 0xbc, 0x4f, 0x62, 0x2e, 0x02, 0xf5, 0x8f, 0x7f, 0x44,
	0xbd, 0xbe, 0x03, 0x94, 0xe5, 0xfb, 0xd3, 0x29, 0xf5, 0x17, 0x07, 0xaa, 0x9f, 0x53, 0x16, 0x07,
	0xd1, 0xb0, 0xed, 0xb2, 0x97, 0x5c, 0xa5, 0xff, 0x85, 0xf9, 0xd8, 0x65, 0x2f, 0x3b, 0x22, 0x3d,
	0x3b, 0xe2, 0x6d, 0xb0, 0x9a, 0x3f, 0xa1, 0x06, 0x51, 0x7b, 0x18, 0x92, 0x56, 0x39, 0x56, 0xbf,
	0x84, 0x84, 0x47, 0x47, 0x8c, 0xe8, 0x0a, 0x47, 0xad, 0xd2, 0x49, 0x6c, 0xf4, 0xf0, 0x4f, 0x17,
	0x1a, 0xe7, 0xb2, 0x85, 0xc6, 0x9f, 0x1d, 0x58, 0x32, 0xb6, 0x12, 0xf7, 0x57, 0xfe, 0xc9, 0xd3,
	0x80, 0xf2, 0xa8, 0x1e, 0x9d, 0x4d, 0x74, 0xf7, 0x95, 0xee, 0x6e, 0xb7, 0x2b, 0x6e, 0x20, 0x69,
	0x16, 0xbd, 0xe4, 0x82, 0x52, 0xbf, 0x1b, 0xf4, 0xf5, 0xb5, 0xa5, 0x56, 0x1c, 0x7e, 0x4c, 0x7c,
	0x8f, 0x44, 0xe2, 0xca, 0x5a, 0x6c, 0xa9, 0x15, 0xdf, 0xd7, 0x73, 0x87, 0xe2, 0xc6, 0x5a, 0x6c,
	0xf1, 0x9f, 0xf8, 0x0f, 0x19, 0xeb, 0xb1, 0x70, 0x64, 0x3d, 0x15, 0xdf, 0x3c, 0xbb, 0x8e, 0xb7,
	0x9e, 0xf0, 0xb8, 0xb0, 0x9e, 0x50, 0x6e, 0x15, 0x16, 0xba, 0xaa, 0x96, 0xeb, 0x24, 0x55, 0x62,
	0x45, 0xc3, 0x76, 0xfc, 0x58, 0xdc, 0x68, 0xfe, 0x29, 0x7f, 0x08, 0x0b, 0x0c, 0x69, 0x4e, 0x50,
	0x20, 0x5e, 0x45, 0x7f, 0x0d, 0xd5, 0x9d, 0x41, 0xc4, 0xf7, 0xd2, 0x3e, 0x4d, 0x7c, 0xe2, 0xd8,
	0x7d, 0x32, 0x5b, 0xec, 0x93, 0x52, 0xd6, 0x27, 0xff, 0x70, 0x60, 0xc9, 0xe0, 0xff, 0x93, 0xf8,
	0x44, 0xd9, 0xfe, 0xb5, 0x91, 0xed, 0x8d, 0x00, 0x91, 0x0e, 0x31, 0x02, 0x44, 0x79, 0x6f, 0xce,
	0xf4, 0x1e, 0x7e, 0x92, 0x36, 0xca, 0xd4, 0xae, 0xca, 0x68, 0x9a, 0xb8, 0x0a, 0x7f, 0x00, 0x48,
	0xd6, 0xbf, 0x0f, 0x68, 0xc4, 0xe2, 0xcf, 0x83, 0xfe, 0x14, 0x8f, 0x2d, 0xbc, 0x01, 0x17, 0x72,
	0x44, 0x32, 0xa3, 0x52, 0x26, 0x40, 0xba, 0x28, 0x50, 0x4b, 0xfc, 0x09, 0xd4, 0x9b, 0x3e, 0x23,
	0x51, 0x7c, 0xf6, 0xbd, 0xae, 0xc2, 0x95, 0x02, 0x52, 0x16, 0xe2, 0x65, 0x58, 0xe2, 0xaf, 0x94,
	0x6e, 0xbc, 0x13, 0xf8, 0x47, 0xf4, 0xb8, 0x45, 0xbe, 0xc1, 0x7f, 0x75, 0xa0, 0x96, 0x86, 0xc9,
	0x17, 0x9b, 0x68, 0xdc, 0xc9, 0xf2, 0x49, 0x26, 0xa3, 0x79, 0x01, 0x11, 0xe5, 0xd5, 0x15, 0x28,
	0x13, 0xdf, 0x93, 0x1f, 0xd5, 0xf5, 0x4c, 0x7c, 0xd1, 0xa1, 0x41, 0x9b, 0x70, 0x71, 0xcf, 0xf7,
	0x52, 0x4f, 0x21, 0x0e, 0x57, 0x2e, 0xb7, 0x7e, 0x43, 0xd7, 0xa1, 0xb2, 0xe7, 0x7b, 0x22, 0xab,
	0xd0, 0xbe, 0xce, 0x56, 0x26, 0x08, 0xdf, 0xd3, 0x06, 0x6c, 0xfa, 0x34, 0x7e, 0x10, 0x44, 0x2f,
	0xc8, 0xe1, 0x14, 0xa6, 0xf8, 0xdb, 0x2c, 0x5c, 0xcc, 0x93, 0xb1, 0x90, 0x07, 0xd3, 0x11, 0xb7,
	0x4b, 0x87, 0xf0, 0xa7, 0xb6, 0x22, 0x04, 0x01, 0x92, 0x8f, 0xef, 0x55, 0x58, 0x88, 0x48, 0x37,
	0x1a, 0xd0, 0x38, 0x51, 0x72, 0xb1, 0x55, 0x51, 0x30, 0x21, 0xf4, 0x06, 0x5c, 0x78, 0x45, 0xe3,
	0x13, 0xfe, 0x52, 0x11, 0x85, 0xba, 0x3a, 0xc1, 0x32, 0x70, 0x90, 0xf9, 0xa9, 0x29, 0x4f, 0xf3,
	0x2a, 0xc8, 0x57, 0x41, 0xc7, 0x38, 0xeb, 0x8b, 0xad, 0x8a, 0x80, 0x29, 0x94, 0x1b, 0xb0, 0x98,
	0x54, 0xfe, 0xfe, 0xa0, 0xaf, 0x8e, 0xfe, 0xc2, 0x08, 0x78, 0x30, 0xe8, 0x8b, 0xd7, 0x84, 0xe7,
	0xd1, 0x98, 0x06, 0x7e, 0xc2, 0x4c, 0x86, 0x43, 0x2d, 0xf9, 0x60, 0x6e, 0xea, 0xb9, 0x43, 0x8d,
	0x37, 0xa7, 0x37, 0xf5, 0xf8, 0x5d, 0xca, 0x41, 0x78, 0x1b, 0x56, 0xb9, 0x4b, 0x7a, 0xbd, 0xc4,
	0x2b, 0xfc, 0xa4, 0xb3, 0xa6, 0xff, 0x24, 0x0a, 0xba, 0x84, 0xb1, 0x29, 0x2c, 0xfd, 0x1d, 0x54,
	0xd3, 0xd4, 0x96, 0xdc, 0xb0, 0xa7, 0xf5, 0x37, 0xaa, 0x92, 0xe9, 0x3a, 0x50, 0xd2, 0x46, 0x72,
	0xc1, 0x19, 0xef, 0x8e, 0x7a, 0xc5, 0xfc, 0x27, 0xee, 0x02, 0x9e, 0xa4, 0x00, 0x0b, 0xd1, 0x67,
	0x30, 0xcf, 0x63, 0x3e, 0x79, 0x14, 0x57, 0x36, 0xaf, 0x8f, 0xdb, 0x5b, 0xc6, 0x3d, 0x27, 0x11,
	0xaf, 0x65, 0x0c, 0xd5, 0x16, 0xe9, 0x12, 0x7a, 0x4a, 0xf6, 0xe9, 0x51, 0x41, 0x47, 0x71, 0x19,
	0x96, 0x52, 0x38, 0x2c, 0xc4, 0xdf, 0xc3, 0x8a, 0x3c, 0x81, 0x8f, 0x02, 0x9f, 0xc6, 0x41, 0xd4,
	0xf4, 0xc5, 0xcb, 0x9d, 0xd3, 0xaf, 0xc2, 0x82, 0xc8, 0xbc, 0xda, 0x35, 0x92, 0x51, 0x45, 0xc0,
	0x94, 0xf7, 0x6e, 0x41, 0x55, 0xa2, 0xe8, 0xe3, 0xa4, 0xdb, 0x1c, 0x02, 0xfa, 0x42, 0x01, 0x79,
	0x38, 0x76, 0x4f, 0x48, 0xf7, 0x65, 0x27, 0xd6, 0x2f, 0xb5, 0x39, 0xb1, 0x6e, 0x33, 0xfc, 0xb9,
	0x6e, 0x0c, 0x3d, 0x94, 0x6c, 0x8f, 0x82, 0x49, 0xad, 0x95, 0xe4, 0xed, 0xa6, 0x6e, 0x72, 0xb9,
	0xc2, 0xff, 0x72, 0xa0, 0x6e, 0x57, 0x85, 0x85, 0xe8, 0x19, 0x5c, 0x22, 0xdf, 0x76, 0x09, 0xf1,
	0x94, 0x32, 0xaa, 0x7f, 0xc6, 0x8a, 0x33, 0x6c, 0x46, 0xaa, 0xd6, 0x05, 0x49, 0x2f, 0x15, 0x97,
	0x1f, 0x19, 0xfa, 0x12, 0x56, 0x14, 0x5b, 0x6d, 0x80, 0x11, 0xe3, 0xd9, 0x69, 0x19, 0x2b, 0xc1,
	0xb4, 0xb1, 0x14, 0x6b, 0xbc, 0x2e, 0xea, 0xfc, 0xad, 0x6e, 0x4c, 0x4f, 0x69, 0x3c, 0xe4, 0x67,
	0x8b, 0xb2, 0x98, 0x76, 0x99, 0x6a, 0xbf, 0xf3, 0x3a, 0x58, 0xe5, 0x3d, 0xf1, 0x1b, 0xff, 0x71,
	0x16, 0x50, 0x1e, 0x1b, 0x61, 0x58, 0x68, 0x1b, 0x4f, 0x7d, 0xfd, 0xfc, 0x37, 0x61, 0xe8, 0x26,
	0x2c, 0x8a, 0xf5, 0x0b, 0xd3, 0x89, 0xa5, 0x56, 0x1a, 0x88, 0xd6, 0xa0, 0xb6, 0x95, 0x79, 0xda,
	0x2b, 0x67, 0xe6, 0xe0, 0x3c, 0x61, 0x6e, 0xbd, 0x72, 0x23, 0xaf, 0xe9, 0xef, 0x72, 0x39, 0xe5,
	0xad, 0x69, 0x82, 0xd0, 0x5b, 0x50, 0xd5, 0x9c, 0x15, 0x92, 0x7c, 0x89, 0x67, 0xa0, 0xfc, 0xc2,
	0xde, 0x3a, 0x3d, 0x16, 0x94, 0xea, 0x55, 0x3e, 0x5a, 0x73, 0x1e, 0xfa, 0xb7, 0xe2, 0x31, 0x27,
	0x79, 0xa4, 0xa1, 0xd8, 0x15, 0x6f, 0x1f, 0x9b, 0x29, 0x59, 0x88, 0x76, 0x01, 0xd8, 0x08, 0xa2,
	0xde, 0x3e, 0x37, 0x2d, 0x71, 0x97, 0xa7, 0x36, 0xe8, 0xf0, 0x97, 0xb0, 0xfc, 0xd4, 0xed, 0x67,
	0xc6, 0x35, 0x3f, 0x4e, 0x8f, 0xf0, 0x1d, 0x40, 0x59, 0xd6, 0xb2, 0x12, 0xa7, 0x8c, 0xc3, 0x75,
	0x93, 0x50, 0xae, 0xf0, 0x00, 0x2e, 0x3d, 0xa0, 0x3e, 0x65, 0x27, 0xe2, 0x5e, 0xdd, 0x75, 0x47,
	0x05, 0xf4, 0x55, 0x50, 0x5b, 0x27, 0xaf, 0x82, 0xb2, 0x04, 0x34, 0x3d, 0x91, 0xd7, 0x13, 0x51,
	0x46, 0x92, 0x2c, 0x24, 0xc0, 0xa6, 0xa8, 0x9d, 0xf8, 0x49, 0x8b, 0xf5, 0x6d, 0x59, 0x6a, 0x8d,
	0xd6, 0xb8, 0x0e, 0x97, 0x6d, 0xdb, 0xb2, 0x70, 0xed, 0x09, 0xd4, 0xb2, 0xf9, 0x12, 0x2d, 0x40,
	0xb9, 0x79, 0xf0, 0xbc, 0xd9, 0x6e, 0x1e, 0xec, 0xd7, 0x66, 0x50, 0x15, 0xa0, 0x79, 0xd0, 0x79,
	0xd2, 0x7a, 0xbc, 0xb3, 0xf7, 0xf4, 0x69, 0xcd, 0x41, 0x8b, 0x30, 0xbf, 0xf3, 0xf8, 0xd1, 0x93,
	0x87, 0x7b, 0xed, 0xbd, 0xdd, 0xda, 0x2c, 0x5a, 0x82, 0x4a, 0x7b, 0xaf, 0xf5, 0xa8, 0x79, 0xb0,
	0xd5, 0x6e, 0x3e, 0x3e, 0xa8, 0x95, 0xd6, 0x3e, 0x4e, 0x15, 0xdf, 0xa2, 0xba, 0x5f, 0x86, 0xc5,
	0x47, 0x94, 0x31, 0x1a, 0xf8, 0x52, 0x8a, 0xda, 0x0c, 0x42, 0x50, 0x55, 0xa0, 0x67, 0xb2, 0x06,
	0xad, 0x39, 0x9b, 0xbf, 0xbe, 0xa6, 0x9b, 0xbf, 0x89, 0x48, 0xe8, 0x77, 0x0e, 0x5c, 0x9b, 0x30,
	0xb0, 0x43, 0xf7, 0x8a, 0x1b, 0xec, 0xc5, 0xa3, 0xc6, 0xc6, 0xfd, 0x1f, 0x40, 0xc5, 0x42, 0x3c,
	0x83, 0x7e, 0xef, 0xc0, 0xea, 0x04, 0xcc, 0xe7, 0x9b, 0x3f, 0xb5, 0x50, 0xdf, 0xab, 0x39, 0x87,
	0x6d, 0xd0, 0x88, 0xd6, 0x0b, 0xb8, 0x16, 0x4c, 0x32, 0x1b, 0x1b, 0x67, 0xc2, 0x17, 0xfb, 0x7f,
	0x0d, 0xd5, 0xf4, 0x78, 0x12, 0xdd, 0x28, 0x60, 0x62, 0x4e, 0x3a, 0x1b, 0x37, 0x27, 0x23, 0xa5,
	0xd8, 0x8f, 0xc6, 0x97, 0x85, 0xec, 0xcd, 0x49, 0x68, 0x21, 0xfb, 0xf4, 0x14, 0x74, 0x06, 0xbd,
	0x80, 0x05, 0x73, 0xc4, 0x89, 0x2c, 0x17, 0x43, 0x66, 0x4e, 0xda, 0xc0, 0x93, 0x50, 0x04, 0xe3,
	0xff, 0x87, 0xb2, 0x1e, 0x44, 0xa2, 0x37, 0x6c, 0x14, 0x89, 0xac, 0x6f, 0x8e, 0xfb, 0x2c, 0x98,
	0xf5, 0xe0, 0x82, 0x65, 0xaa, 0x89, 0xee, 0x14, 0x13, 0xa6, 0x07, 0xa5, 0x8d, 0xb7, 0xa7, 0xc4,
	0x14, 0xbb, 0x51, 0xd1, 0x63, 0xc8, 0x0c, 0x38, 0xd1, 0xed, 0x3c, 0x0b, 0xeb, 0xc4, 0xb4, 0x71,
	0x67, 0x3a, 0x44, 0xad, 0x98, 0x65, 0x56, 0x89, 0x26, 0xb1, 0x18, 0x8d, 0x49, 0x6d, 0x8a, 0x15,
	0x0c, 0x3f, 0xf1, 0x0c, 0x3a, 0x12, 0x4d, 0x99, 0xf4, 0xe8, 0x0d, 0xbd, 0x65, 0xe5, 0x90, 0x1b,
	0x19, 0x36, 0x6e, 0x4f, 0x85, 0x27, 0xf6, 0x19, 0xc0, 0x65, 0xfb, 0x9c, 0x0f, 0xdd, 0x9d, 0x7e,
	0x22, 0xf8, 0x4d, 0xe3, 0x9d, 0xb3, 0x8c, 0x0f, 0xe5, 0x59, 0x36, 0x47, 0x68, 0xa8, 0xb0, 0xc8,
	0x19, 0x0d, 0xfb, 0x6c, 0x67, 0x39, 0x3b, 0x85, 0xc3, 0x33, 0xe8, 0x2b, 0x58, 0x4c, 0x8d, 0xc3,
	0x90, 0x85, 0x2c, 0x3b, 0x89, 0x6b, 0xdc, 0x98, 0x88, 0x23, 0x78, 0x7f, 0x27, 0xeb, 0x29, 0xdb,
	0xfc, 0x0b, 0xbd, 0x3b, 0xc1, 0xb9, 0xe9, 0x49, 0x5b, 0x63, 0xfd, 0x2c, 0xe8, 0xc6, 0x81, 0x48,
	0xcf, 0xc4, 0x0a, 0x0e, 0x44, 0x6e, 0xde, 0x56, 0x70, 0x20, 0xf2, 0x03, 0x36, 0x3c, 0x83, 0xda,
	0x50, 0x31, 0x26, 0x60, 0xe8, 0xba, 0x2d, 0x1a, 0xcd, 0x89, 0x5a, 0x63, 0x75, 0x02, 0x86, 0x4e,
	0x8d, 0xe9, 0x0a, 0xc4, 0x96, 0x1a, 0x73, 0xe5, 0x8f, 0x2d, 0x35, 0xe6, 0x0b, 0x19, 0x29, 0xb4,
	0x31, 0x3f, 0xb3, 0x09, 0x9d, 0x1e, 0xd6, 0xd9, 0x84, 0xce, 0x0c, 0xe0, 0x64, 0x6c, 0xd8, 0xc7,
	0x62, 0xb6, 0xd8, 0x28, 0x9c, 0xbb, 0xd9, 0x62, 0xa3, 0x78, 0xda, 0x86, 0x67, 0x50, 0x20, 0x86,
	0xa6, 0xb9, 0xe9, 0x07, 0xb2, 0xe7, 0x0f, 0xdb, 0x7c, 0xa7, 0xb1, 0x36, 0x2d, 0xaa, 0xd8, 0xf0,
	0x17, 0x8e, 0x18, 0x0b, 0x17, 0xcd, 0x1b, 0xd0, 0x7b, 0xc5, 0xdc, 0xec, 0x43, 0x8f, 0xc6, 0xfb,
	0x67, 0xa4, 0x10, 0x62, 0x44, 0x62, 0xea, 0x9b, 0xef, 0xcc, 0xa3, 0xb5, 0x49, 0xdc, 0x8c, 0x9d,
	0xef, 0x4e, 0x8d, 0xab, 0x43, 0xba, 0x68, 0x20, 0x60, 0x0b, 0xe9, 0x31, 0x63, 0x0a, 0x5b, 0x48,
	0x8f, 0x9b, 0x35, 0xc8, 0x90, 0xce, 0x35, 0xfe, 0x6d, 0x21, 0x6d, 0x1b, 0x3c, 0xd8, 0x42, 0xda,
	0x3a, 0x45, 0x90, 0xc1, 0x97, 0x6e, 0xc4, 0xdb, 0x82, 0x2f, 0x37, 0x02, 0xb0, 0x05, 0x5f, 0xbe,
	0x9f, 0x8f, 0x67, 0xd0, 0x73, 0xc1, 0xde, 0x68, 0x27, 0xda, 0xe2, 0x2f, 0xdd, 0xb7, 0x6d, 0x4c,
	0xc0, 0x30, 0xf8, 0x1a, 0x75, 0xba, 0x8d, 0x6f, 0xba, 0xc7, 0xdf, 0x98, 0x80, 0x21, 0xf8, 0xfe,
	0x5c, 0xf7, 0x0b, 0x46, 0x7d, 0x43, 0x74, 0xb3, 0xe8, 0x6e, 0x31, 0xbb, 0x92, 0x8d, 0x5b, 0x53,
	0x60, 0xe9, 0x93, 0x6c, 0xed, 0x4f, 0xda, 0x4e, 0x72, 0x51, 0x0f, 0xd4, 0x76, 0x92, 0x8b, 0x9b,
	0x9e, 0xe2, 0x46, 0x35, 0x5b, 0x9c, 0xb6, 0x1b, 0x35, 0xd3, 0x16, 0xb5, 0xdd, 0xa8, 0xd9, 0x2e,
	0x29, 0x9e, 0x41, 0xdd, 0xe4, 0xdf, 0x2e, 0xba, 0xc1, 0x88, 0x6e, 0x15, 0xdf, 0xc5, 0x46, 0xef,
	0xb2, 0xf1, 0xd6, 0x34, 0x68, 0x62, 0x93, 0xdf, 0x38, 0x62, 0x90, 0x3a, 0xa6, 0xc1, 0x85, 0x3e,
	0xb0, 0x4b, 0x3b, 0xb6, 0xa7, 0xd7, 0xb8, 0x77, 0x76, 0x22, 0x7d, 0xa1, 0x18, 0x6d, 0x2e, 0xdb,
	0xc1, 0x4b, 0x77, 0xca, 0x6c, 0x17, 0x4a, 0xb6, 0x4f, 0x26, 0x32, 0xbb, 0xad, 0xbd, 0x64, 0xcb,
	0xec, 0x05, 0x1d, 0x35, 0x5b, 0x66, 0x2f, 0xea, 0x58, 0x8d, 0x52, 0xaa, 0xa5, 0xa7, 0xb3, 0x56,
	0xe4, 0xfa, 0x7c, 0xab, 0xa8, 0x20, 0xa5, 0xda, 0x7b, 0x21, 0xb2, 0x24, 0xcf, 0xbf, 0xe3, 0x6d,
	0x25, 0xb9, 0xb5, 0xc9, 0x60, 0x2b, 0xc9, 0xed, 0x6d, 0x01, 0x3c, 0xb3, 0x7d, 0xef, 0xab, 0xcd,
	0xe3, 0xa0, 0xe7, 0xfa, 0xc7, 0xeb, 0xf7, 0x37, 0xe3, 0x78, 0xbd, 0x1b, 0xf4, 0x37, 0xc4, 0x5f,
	0x7b, 0xbb, 0x41, 0x6f, 0x83, 0xbf, 0x00, 0x79, 0x05, 0xbf, 0x91, 0x65, 0x77, 0x78, 0x5e, 0xe0,
	0x7c, 0xf0, 0x9f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x39, 0x85, 0xa5, 0x96, 0x2b, 0x2c, 0x00, 0x00,
}
