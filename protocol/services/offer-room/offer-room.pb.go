// Code generated by protoc-gen-go. DO NOT EDIT.
// source: offer-room/offer-room.proto

package offer_room // import "golang.52tt.com/protocol/services/offer-room"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 游戏阶段
type GamePhase int32

const (
	GamePhase_GAME_PHASE_UNSPECIFIED    GamePhase = 0
	GamePhase_GAME_PHASE_IN_PREPARATION GamePhase = 1
	GamePhase_GAME_PHASE_OFFERING       GamePhase = 2
	GamePhase_GAME_PHASE_SETTLE         GamePhase = 3
)

var GamePhase_name = map[int32]string{
	0: "GAME_PHASE_UNSPECIFIED",
	1: "GAME_PHASE_IN_PREPARATION",
	2: "GAME_PHASE_OFFERING",
	3: "GAME_PHASE_SETTLE",
}
var GamePhase_value = map[string]int32{
	"GAME_PHASE_UNSPECIFIED":    0,
	"GAME_PHASE_IN_PREPARATION": 1,
	"GAME_PHASE_OFFERING":       2,
	"GAME_PHASE_SETTLE":         3,
}

func (x GamePhase) String() string {
	return proto.EnumName(GamePhase_name, int32(x))
}
func (GamePhase) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{0}
}

type NamePriceOnceOperation int32

const (
	NamePriceOnceOperation_NAME_PRICE_ONCE_OPERATION_UNSPECIFIED NamePriceOnceOperation = 0
	NamePriceOnceOperation_NAME_PRICE_ONCE_OPERATION_ONCE        NamePriceOnceOperation = 1
	NamePriceOnceOperation_NAME_PRICE_ONCE_OPERATION_MAX         NamePriceOnceOperation = 2
)

var NamePriceOnceOperation_name = map[int32]string{
	0: "NAME_PRICE_ONCE_OPERATION_UNSPECIFIED",
	1: "NAME_PRICE_ONCE_OPERATION_ONCE",
	2: "NAME_PRICE_ONCE_OPERATION_MAX",
}
var NamePriceOnceOperation_value = map[string]int32{
	"NAME_PRICE_ONCE_OPERATION_UNSPECIFIED": 0,
	"NAME_PRICE_ONCE_OPERATION_ONCE":        1,
	"NAME_PRICE_ONCE_OPERATION_MAX":         2,
}

func (x NamePriceOnceOperation) String() string {
	return proto.EnumName(NamePriceOnceOperation_name, int32(x))
}
func (NamePriceOnceOperation) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{1}
}

// 嘉宾报名列表
type ApplyList struct {
	List                 []*ApplyList_UserInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Version              int64                 `protobuf:"varint,2,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *ApplyList) Reset()         { *m = ApplyList{} }
func (m *ApplyList) String() string { return proto.CompactTextString(m) }
func (*ApplyList) ProtoMessage()    {}
func (*ApplyList) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{0}
}
func (m *ApplyList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyList.Unmarshal(m, b)
}
func (m *ApplyList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyList.Marshal(b, m, deterministic)
}
func (dst *ApplyList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyList.Merge(dst, src)
}
func (m *ApplyList) XXX_Size() int {
	return xxx_messageInfo_ApplyList.Size(m)
}
func (m *ApplyList) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyList.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyList proto.InternalMessageInfo

func (m *ApplyList) GetList() []*ApplyList_UserInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *ApplyList) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type ApplyList_UserInfo struct {
	UserId               uint32   `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyList_UserInfo) Reset()         { *m = ApplyList_UserInfo{} }
func (m *ApplyList_UserInfo) String() string { return proto.CompactTextString(m) }
func (*ApplyList_UserInfo) ProtoMessage()    {}
func (*ApplyList_UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{0, 0}
}
func (m *ApplyList_UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyList_UserInfo.Unmarshal(m, b)
}
func (m *ApplyList_UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyList_UserInfo.Marshal(b, m, deterministic)
}
func (dst *ApplyList_UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyList_UserInfo.Merge(dst, src)
}
func (m *ApplyList_UserInfo) XXX_Size() int {
	return xxx_messageInfo_ApplyList_UserInfo.Size(m)
}
func (m *ApplyList_UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyList_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyList_UserInfo proto.InternalMessageInfo

func (m *ApplyList_UserInfo) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type GetApplyListRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyListRequest) Reset()         { *m = GetApplyListRequest{} }
func (m *GetApplyListRequest) String() string { return proto.CompactTextString(m) }
func (*GetApplyListRequest) ProtoMessage()    {}
func (*GetApplyListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{1}
}
func (m *GetApplyListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyListRequest.Unmarshal(m, b)
}
func (m *GetApplyListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyListRequest.Marshal(b, m, deterministic)
}
func (dst *GetApplyListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyListRequest.Merge(dst, src)
}
func (m *GetApplyListRequest) XXX_Size() int {
	return xxx_messageInfo_GetApplyListRequest.Size(m)
}
func (m *GetApplyListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyListRequest proto.InternalMessageInfo

func (m *GetApplyListRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetApplyListResponse struct {
	ApplyList            *ApplyList `protobuf:"bytes,1,opt,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetApplyListResponse) Reset()         { *m = GetApplyListResponse{} }
func (m *GetApplyListResponse) String() string { return proto.CompactTextString(m) }
func (*GetApplyListResponse) ProtoMessage()    {}
func (*GetApplyListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{2}
}
func (m *GetApplyListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyListResponse.Unmarshal(m, b)
}
func (m *GetApplyListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyListResponse.Marshal(b, m, deterministic)
}
func (dst *GetApplyListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyListResponse.Merge(dst, src)
}
func (m *GetApplyListResponse) XXX_Size() int {
	return xxx_messageInfo_GetApplyListResponse.Size(m)
}
func (m *GetApplyListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyListResponse proto.InternalMessageInfo

func (m *GetApplyListResponse) GetApplyList() *ApplyList {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type AddAppliedUserRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserId               uint32   `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAppliedUserRequest) Reset()         { *m = AddAppliedUserRequest{} }
func (m *AddAppliedUserRequest) String() string { return proto.CompactTextString(m) }
func (*AddAppliedUserRequest) ProtoMessage()    {}
func (*AddAppliedUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{3}
}
func (m *AddAppliedUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAppliedUserRequest.Unmarshal(m, b)
}
func (m *AddAppliedUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAppliedUserRequest.Marshal(b, m, deterministic)
}
func (dst *AddAppliedUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAppliedUserRequest.Merge(dst, src)
}
func (m *AddAppliedUserRequest) XXX_Size() int {
	return xxx_messageInfo_AddAppliedUserRequest.Size(m)
}
func (m *AddAppliedUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAppliedUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AddAppliedUserRequest proto.InternalMessageInfo

func (m *AddAppliedUserRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AddAppliedUserRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type AddAppliedUserResponse struct {
	ApplyList            *ApplyList `protobuf:"bytes,1,opt,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *AddAppliedUserResponse) Reset()         { *m = AddAppliedUserResponse{} }
func (m *AddAppliedUserResponse) String() string { return proto.CompactTextString(m) }
func (*AddAppliedUserResponse) ProtoMessage()    {}
func (*AddAppliedUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{4}
}
func (m *AddAppliedUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAppliedUserResponse.Unmarshal(m, b)
}
func (m *AddAppliedUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAppliedUserResponse.Marshal(b, m, deterministic)
}
func (dst *AddAppliedUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAppliedUserResponse.Merge(dst, src)
}
func (m *AddAppliedUserResponse) XXX_Size() int {
	return xxx_messageInfo_AddAppliedUserResponse.Size(m)
}
func (m *AddAppliedUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAppliedUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AddAppliedUserResponse proto.InternalMessageInfo

func (m *AddAppliedUserResponse) GetApplyList() *ApplyList {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type DelAppliedUserRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	UserId               uint32   `protobuf:"varint,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAppliedUserRequest) Reset()         { *m = DelAppliedUserRequest{} }
func (m *DelAppliedUserRequest) String() string { return proto.CompactTextString(m) }
func (*DelAppliedUserRequest) ProtoMessage()    {}
func (*DelAppliedUserRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{5}
}
func (m *DelAppliedUserRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAppliedUserRequest.Unmarshal(m, b)
}
func (m *DelAppliedUserRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAppliedUserRequest.Marshal(b, m, deterministic)
}
func (dst *DelAppliedUserRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAppliedUserRequest.Merge(dst, src)
}
func (m *DelAppliedUserRequest) XXX_Size() int {
	return xxx_messageInfo_DelAppliedUserRequest.Size(m)
}
func (m *DelAppliedUserRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAppliedUserRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DelAppliedUserRequest proto.InternalMessageInfo

func (m *DelAppliedUserRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DelAppliedUserRequest) GetUserId() uint32 {
	if m != nil {
		return m.UserId
	}
	return 0
}

type DelAppliedUserResponse struct {
	ApplyList            *ApplyList `protobuf:"bytes,1,opt,name=apply_list,json=applyList,proto3" json:"apply_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *DelAppliedUserResponse) Reset()         { *m = DelAppliedUserResponse{} }
func (m *DelAppliedUserResponse) String() string { return proto.CompactTextString(m) }
func (*DelAppliedUserResponse) ProtoMessage()    {}
func (*DelAppliedUserResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{6}
}
func (m *DelAppliedUserResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAppliedUserResponse.Unmarshal(m, b)
}
func (m *DelAppliedUserResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAppliedUserResponse.Marshal(b, m, deterministic)
}
func (dst *DelAppliedUserResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAppliedUserResponse.Merge(dst, src)
}
func (m *DelAppliedUserResponse) XXX_Size() int {
	return xxx_messageInfo_DelAppliedUserResponse.Size(m)
}
func (m *DelAppliedUserResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAppliedUserResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DelAppliedUserResponse proto.InternalMessageInfo

func (m *DelAppliedUserResponse) GetApplyList() *ApplyList {
	if m != nil {
		return m.ApplyList
	}
	return nil
}

type OfferingRelationshipsRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TargetUid            uint32   `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	PageToken            string   `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferingRelationshipsRequest) Reset()         { *m = OfferingRelationshipsRequest{} }
func (m *OfferingRelationshipsRequest) String() string { return proto.CompactTextString(m) }
func (*OfferingRelationshipsRequest) ProtoMessage()    {}
func (*OfferingRelationshipsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{7}
}
func (m *OfferingRelationshipsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferingRelationshipsRequest.Unmarshal(m, b)
}
func (m *OfferingRelationshipsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferingRelationshipsRequest.Marshal(b, m, deterministic)
}
func (dst *OfferingRelationshipsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferingRelationshipsRequest.Merge(dst, src)
}
func (m *OfferingRelationshipsRequest) XXX_Size() int {
	return xxx_messageInfo_OfferingRelationshipsRequest.Size(m)
}
func (m *OfferingRelationshipsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferingRelationshipsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferingRelationshipsRequest proto.InternalMessageInfo

func (m *OfferingRelationshipsRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *OfferingRelationshipsRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *OfferingRelationshipsRequest) GetPageToken() string {
	if m != nil {
		return m.PageToken
	}
	return ""
}

func (m *OfferingRelationshipsRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 关系信息
type OfferingRelationshipInfo struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	PartnerUid           uint32   `protobuf:"varint,2,opt,name=partner_uid,json=partnerUid,proto3" json:"partner_uid,omitempty"`
	RelationshipName     string   `protobuf:"bytes,3,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	ExpireTs             int64    `protobuf:"varint,4,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	GiftId               uint32   `protobuf:"varint,5,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftCnt              uint32   `protobuf:"varint,6,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`
	IsBuyer              bool     `protobuf:"varint,7,opt,name=is_buyer,json=isBuyer,proto3" json:"is_buyer,omitempty"`
	GiftIcon             string   `protobuf:"bytes,8,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	IsPriceMax           bool     `protobuf:"varint,9,opt,name=is_price_max,json=isPriceMax,proto3" json:"is_price_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferingRelationshipInfo) Reset()         { *m = OfferingRelationshipInfo{} }
func (m *OfferingRelationshipInfo) String() string { return proto.CompactTextString(m) }
func (*OfferingRelationshipInfo) ProtoMessage()    {}
func (*OfferingRelationshipInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{8}
}
func (m *OfferingRelationshipInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferingRelationshipInfo.Unmarshal(m, b)
}
func (m *OfferingRelationshipInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferingRelationshipInfo.Marshal(b, m, deterministic)
}
func (dst *OfferingRelationshipInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferingRelationshipInfo.Merge(dst, src)
}
func (m *OfferingRelationshipInfo) XXX_Size() int {
	return xxx_messageInfo_OfferingRelationshipInfo.Size(m)
}
func (m *OfferingRelationshipInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferingRelationshipInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfferingRelationshipInfo proto.InternalMessageInfo

func (m *OfferingRelationshipInfo) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *OfferingRelationshipInfo) GetPartnerUid() uint32 {
	if m != nil {
		return m.PartnerUid
	}
	return 0
}

func (m *OfferingRelationshipInfo) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferingRelationshipInfo) GetExpireTs() int64 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

func (m *OfferingRelationshipInfo) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *OfferingRelationshipInfo) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *OfferingRelationshipInfo) GetIsBuyer() bool {
	if m != nil {
		return m.IsBuyer
	}
	return false
}

func (m *OfferingRelationshipInfo) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *OfferingRelationshipInfo) GetIsPriceMax() bool {
	if m != nil {
		return m.IsPriceMax
	}
	return false
}

type OfferingRelationshipsResponse struct {
	RelationshipList     []*OfferingRelationshipInfo `protobuf:"bytes,1,rep,name=relationship_list,json=relationshipList,proto3" json:"relationship_list,omitempty"`
	RelationshipName     string                      `protobuf:"bytes,2,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	NextPageToken        string                      `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	RelationshipCnt      uint32                      `protobuf:"varint,4,opt,name=relationship_cnt,json=relationshipCnt,proto3" json:"relationship_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *OfferingRelationshipsResponse) Reset()         { *m = OfferingRelationshipsResponse{} }
func (m *OfferingRelationshipsResponse) String() string { return proto.CompactTextString(m) }
func (*OfferingRelationshipsResponse) ProtoMessage()    {}
func (*OfferingRelationshipsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{9}
}
func (m *OfferingRelationshipsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferingRelationshipsResponse.Unmarshal(m, b)
}
func (m *OfferingRelationshipsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferingRelationshipsResponse.Marshal(b, m, deterministic)
}
func (dst *OfferingRelationshipsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferingRelationshipsResponse.Merge(dst, src)
}
func (m *OfferingRelationshipsResponse) XXX_Size() int {
	return xxx_messageInfo_OfferingRelationshipsResponse.Size(m)
}
func (m *OfferingRelationshipsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferingRelationshipsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferingRelationshipsResponse proto.InternalMessageInfo

func (m *OfferingRelationshipsResponse) GetRelationshipList() []*OfferingRelationshipInfo {
	if m != nil {
		return m.RelationshipList
	}
	return nil
}

func (m *OfferingRelationshipsResponse) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferingRelationshipsResponse) GetNextPageToken() string {
	if m != nil {
		return m.NextPageToken
	}
	return ""
}

func (m *OfferingRelationshipsResponse) GetRelationshipCnt() uint32 {
	if m != nil {
		return m.RelationshipCnt
	}
	return 0
}

type DeleteRelationshipRequest struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RecordIdList         []uint32 `protobuf:"varint,2,rep,packed,name=record_id_list,json=recordIdList,proto3" json:"record_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRelationshipRequest) Reset()         { *m = DeleteRelationshipRequest{} }
func (m *DeleteRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteRelationshipRequest) ProtoMessage()    {}
func (*DeleteRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{10}
}
func (m *DeleteRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRelationshipRequest.Unmarshal(m, b)
}
func (m *DeleteRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRelationshipRequest.Merge(dst, src)
}
func (m *DeleteRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteRelationshipRequest.Size(m)
}
func (m *DeleteRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRelationshipRequest proto.InternalMessageInfo

func (m *DeleteRelationshipRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DeleteRelationshipRequest) GetRecordIdList() []uint32 {
	if m != nil {
		return m.RecordIdList
	}
	return nil
}

type DeleteRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRelationshipResponse) Reset()         { *m = DeleteRelationshipResponse{} }
func (m *DeleteRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteRelationshipResponse) ProtoMessage()    {}
func (*DeleteRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{11}
}
func (m *DeleteRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRelationshipResponse.Unmarshal(m, b)
}
func (m *DeleteRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRelationshipResponse.Merge(dst, src)
}
func (m *DeleteRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteRelationshipResponse.Size(m)
}
func (m *DeleteRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRelationshipResponse proto.InternalMessageInfo

type SetOfferGeneralConfigRequest struct {
	GiftIdLsit           []uint32          `protobuf:"varint,1,rep,packed,name=gift_id_lsit,json=giftIdLsit,proto3" json:"gift_id_lsit,omitempty"`
	CntToDays            map[uint32]uint32 `protobuf:"bytes,2,rep,name=cnt_to_days,json=cntToDays,proto3" json:"cnt_to_days,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	RelationshipNameList []string          `protobuf:"bytes,3,rep,name=relationship_name_list,json=relationshipNameList,proto3" json:"relationship_name_list,omitempty"`
	MaxWordsCnt          uint32            `protobuf:"varint,4,opt,name=max_words_cnt,json=maxWordsCnt,proto3" json:"max_words_cnt,omitempty"`
	ReturnGiftId         uint32            `protobuf:"varint,5,opt,name=return_gift_id,json=returnGiftId,proto3" json:"return_gift_id,omitempty"`
	MaxOfferValue        uint32            `protobuf:"varint,6,opt,name=max_offer_value,json=maxOfferValue,proto3" json:"max_offer_value,omitempty"`
	MaxReturnValue       uint32            `protobuf:"varint,7,opt,name=max_return_value,json=maxReturnValue,proto3" json:"max_return_value,omitempty"`
	ReturnGiftIdList     []uint32          `protobuf:"varint,8,rep,packed,name=return_gift_id_list,json=returnGiftIdList,proto3" json:"return_gift_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *SetOfferGeneralConfigRequest) Reset()         { *m = SetOfferGeneralConfigRequest{} }
func (m *SetOfferGeneralConfigRequest) String() string { return proto.CompactTextString(m) }
func (*SetOfferGeneralConfigRequest) ProtoMessage()    {}
func (*SetOfferGeneralConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{12}
}
func (m *SetOfferGeneralConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetOfferGeneralConfigRequest.Unmarshal(m, b)
}
func (m *SetOfferGeneralConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetOfferGeneralConfigRequest.Marshal(b, m, deterministic)
}
func (dst *SetOfferGeneralConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOfferGeneralConfigRequest.Merge(dst, src)
}
func (m *SetOfferGeneralConfigRequest) XXX_Size() int {
	return xxx_messageInfo_SetOfferGeneralConfigRequest.Size(m)
}
func (m *SetOfferGeneralConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOfferGeneralConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SetOfferGeneralConfigRequest proto.InternalMessageInfo

func (m *SetOfferGeneralConfigRequest) GetGiftIdLsit() []uint32 {
	if m != nil {
		return m.GiftIdLsit
	}
	return nil
}

func (m *SetOfferGeneralConfigRequest) GetCntToDays() map[uint32]uint32 {
	if m != nil {
		return m.CntToDays
	}
	return nil
}

func (m *SetOfferGeneralConfigRequest) GetRelationshipNameList() []string {
	if m != nil {
		return m.RelationshipNameList
	}
	return nil
}

func (m *SetOfferGeneralConfigRequest) GetMaxWordsCnt() uint32 {
	if m != nil {
		return m.MaxWordsCnt
	}
	return 0
}

func (m *SetOfferGeneralConfigRequest) GetReturnGiftId() uint32 {
	if m != nil {
		return m.ReturnGiftId
	}
	return 0
}

func (m *SetOfferGeneralConfigRequest) GetMaxOfferValue() uint32 {
	if m != nil {
		return m.MaxOfferValue
	}
	return 0
}

func (m *SetOfferGeneralConfigRequest) GetMaxReturnValue() uint32 {
	if m != nil {
		return m.MaxReturnValue
	}
	return 0
}

func (m *SetOfferGeneralConfigRequest) GetReturnGiftIdList() []uint32 {
	if m != nil {
		return m.ReturnGiftIdList
	}
	return nil
}

type SetOfferGeneralConfigResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetOfferGeneralConfigResponse) Reset()         { *m = SetOfferGeneralConfigResponse{} }
func (m *SetOfferGeneralConfigResponse) String() string { return proto.CompactTextString(m) }
func (*SetOfferGeneralConfigResponse) ProtoMessage()    {}
func (*SetOfferGeneralConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{13}
}
func (m *SetOfferGeneralConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetOfferGeneralConfigResponse.Unmarshal(m, b)
}
func (m *SetOfferGeneralConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetOfferGeneralConfigResponse.Marshal(b, m, deterministic)
}
func (dst *SetOfferGeneralConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOfferGeneralConfigResponse.Merge(dst, src)
}
func (m *SetOfferGeneralConfigResponse) XXX_Size() int {
	return xxx_messageInfo_SetOfferGeneralConfigResponse.Size(m)
}
func (m *SetOfferGeneralConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOfferGeneralConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SetOfferGeneralConfigResponse proto.InternalMessageInfo

type GetOfferGeneralConfigRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOfferGeneralConfigRequest) Reset()         { *m = GetOfferGeneralConfigRequest{} }
func (m *GetOfferGeneralConfigRequest) String() string { return proto.CompactTextString(m) }
func (*GetOfferGeneralConfigRequest) ProtoMessage()    {}
func (*GetOfferGeneralConfigRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{14}
}
func (m *GetOfferGeneralConfigRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfferGeneralConfigRequest.Unmarshal(m, b)
}
func (m *GetOfferGeneralConfigRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfferGeneralConfigRequest.Marshal(b, m, deterministic)
}
func (dst *GetOfferGeneralConfigRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfferGeneralConfigRequest.Merge(dst, src)
}
func (m *GetOfferGeneralConfigRequest) XXX_Size() int {
	return xxx_messageInfo_GetOfferGeneralConfigRequest.Size(m)
}
func (m *GetOfferGeneralConfigRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfferGeneralConfigRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfferGeneralConfigRequest proto.InternalMessageInfo

func (m *GetOfferGeneralConfigRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 嘉宾拉起礼物设置面板
type GetOfferGeneralConfigResponse struct {
	Id                   uint32            `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GiftInfoList         []*GiftInfo       `protobuf:"bytes,2,rep,name=gift_info_list,json=giftInfoList,proto3" json:"gift_info_list,omitempty"`
	RelationshipNameList []string          `protobuf:"bytes,3,rep,name=relationship_name_list,json=relationshipNameList,proto3" json:"relationship_name_list,omitempty"`
	MaxWordsCnt          uint32            `protobuf:"varint,4,opt,name=max_words_cnt,json=maxWordsCnt,proto3" json:"max_words_cnt,omitempty"`
	ReturnGiftInfo       *GiftInfo         `protobuf:"bytes,5,opt,name=return_gift_info,json=returnGiftInfo,proto3" json:"return_gift_info,omitempty"`
	MaxReturnGiftCnt     uint32            `protobuf:"varint,6,opt,name=max_return_gift_cnt,json=maxReturnGiftCnt,proto3" json:"max_return_gift_cnt,omitempty"`
	CntToDays            map[uint32]uint32 `protobuf:"bytes,7,rep,name=cnt_to_days,json=cntToDays,proto3" json:"cnt_to_days,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ReturnGiftInfoList   []*GiftInfo       `protobuf:"bytes,8,rep,name=return_gift_info_list,json=returnGiftInfoList,proto3" json:"return_gift_info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetOfferGeneralConfigResponse) Reset()         { *m = GetOfferGeneralConfigResponse{} }
func (m *GetOfferGeneralConfigResponse) String() string { return proto.CompactTextString(m) }
func (*GetOfferGeneralConfigResponse) ProtoMessage()    {}
func (*GetOfferGeneralConfigResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{15}
}
func (m *GetOfferGeneralConfigResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOfferGeneralConfigResponse.Unmarshal(m, b)
}
func (m *GetOfferGeneralConfigResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOfferGeneralConfigResponse.Marshal(b, m, deterministic)
}
func (dst *GetOfferGeneralConfigResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOfferGeneralConfigResponse.Merge(dst, src)
}
func (m *GetOfferGeneralConfigResponse) XXX_Size() int {
	return xxx_messageInfo_GetOfferGeneralConfigResponse.Size(m)
}
func (m *GetOfferGeneralConfigResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOfferGeneralConfigResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetOfferGeneralConfigResponse proto.InternalMessageInfo

func (m *GetOfferGeneralConfigResponse) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetOfferGeneralConfigResponse) GetGiftInfoList() []*GiftInfo {
	if m != nil {
		return m.GiftInfoList
	}
	return nil
}

func (m *GetOfferGeneralConfigResponse) GetRelationshipNameList() []string {
	if m != nil {
		return m.RelationshipNameList
	}
	return nil
}

func (m *GetOfferGeneralConfigResponse) GetMaxWordsCnt() uint32 {
	if m != nil {
		return m.MaxWordsCnt
	}
	return 0
}

func (m *GetOfferGeneralConfigResponse) GetReturnGiftInfo() *GiftInfo {
	if m != nil {
		return m.ReturnGiftInfo
	}
	return nil
}

func (m *GetOfferGeneralConfigResponse) GetMaxReturnGiftCnt() uint32 {
	if m != nil {
		return m.MaxReturnGiftCnt
	}
	return 0
}

func (m *GetOfferGeneralConfigResponse) GetCntToDays() map[uint32]uint32 {
	if m != nil {
		return m.CntToDays
	}
	return nil
}

func (m *GetOfferGeneralConfigResponse) GetReturnGiftInfoList() []*GiftInfo {
	if m != nil {
		return m.ReturnGiftInfoList
	}
	return nil
}

// 榜单列表
type TopList struct {
	List                 []*TopList_TopListItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *TopList) Reset()         { *m = TopList{} }
func (m *TopList) String() string { return proto.CompactTextString(m) }
func (*TopList) ProtoMessage()    {}
func (*TopList) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{16}
}
func (m *TopList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopList.Unmarshal(m, b)
}
func (m *TopList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopList.Marshal(b, m, deterministic)
}
func (dst *TopList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopList.Merge(dst, src)
}
func (m *TopList) XXX_Size() int {
	return xxx_messageInfo_TopList.Size(m)
}
func (m *TopList) XXX_DiscardUnknown() {
	xxx_messageInfo_TopList.DiscardUnknown(m)
}

var xxx_messageInfo_TopList proto.InternalMessageInfo

func (m *TopList) GetList() []*TopList_TopListItem {
	if m != nil {
		return m.List
	}
	return nil
}

type TopList_TopListItem struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	UserFakeId           uint32   `protobuf:"varint,2,opt,name=user_fake_id,json=userFakeId,proto3" json:"user_fake_id,omitempty"`
	Price                uint32   `protobuf:"varint,3,opt,name=price,proto3" json:"price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TopList_TopListItem) Reset()         { *m = TopList_TopListItem{} }
func (m *TopList_TopListItem) String() string { return proto.CompactTextString(m) }
func (*TopList_TopListItem) ProtoMessage()    {}
func (*TopList_TopListItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{16, 0}
}
func (m *TopList_TopListItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TopList_TopListItem.Unmarshal(m, b)
}
func (m *TopList_TopListItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TopList_TopListItem.Marshal(b, m, deterministic)
}
func (dst *TopList_TopListItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TopList_TopListItem.Merge(dst, src)
}
func (m *TopList_TopListItem) XXX_Size() int {
	return xxx_messageInfo_TopList_TopListItem.Size(m)
}
func (m *TopList_TopListItem) XXX_DiscardUnknown() {
	xxx_messageInfo_TopList_TopListItem.DiscardUnknown(m)
}

var xxx_messageInfo_TopList_TopListItem proto.InternalMessageInfo

func (m *TopList_TopListItem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TopList_TopListItem) GetUserFakeId() uint32 {
	if m != nil {
		return m.UserFakeId
	}
	return 0
}

func (m *TopList_TopListItem) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

// 游戏设置
type GameSetting struct {
	ConfigId             uint32            `protobuf:"varint,1,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	RelationshipName     string            `protobuf:"bytes,2,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	Gift                 *GiftInfo         `protobuf:"bytes,3,opt,name=gift,proto3" json:"gift,omitempty"`
	MaxOfferingPrice     uint32            `protobuf:"varint,4,opt,name=max_offering_price,json=maxOfferingPrice,proto3" json:"max_offering_price,omitempty"`
	ReturnGift           *GiftInfo         `protobuf:"bytes,5,opt,name=return_gift,json=returnGift,proto3" json:"return_gift,omitempty"`
	RelationshipDayMap   map[uint32]uint32 `protobuf:"bytes,6,rep,name=relationship_day_map,json=relationshipDayMap,proto3" json:"relationship_day_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ReturnGiftNum        uint32            `protobuf:"varint,7,opt,name=return_gift_num,json=returnGiftNum,proto3" json:"return_gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GameSetting) Reset()         { *m = GameSetting{} }
func (m *GameSetting) String() string { return proto.CompactTextString(m) }
func (*GameSetting) ProtoMessage()    {}
func (*GameSetting) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{17}
}
func (m *GameSetting) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameSetting.Unmarshal(m, b)
}
func (m *GameSetting) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameSetting.Marshal(b, m, deterministic)
}
func (dst *GameSetting) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameSetting.Merge(dst, src)
}
func (m *GameSetting) XXX_Size() int {
	return xxx_messageInfo_GameSetting.Size(m)
}
func (m *GameSetting) XXX_DiscardUnknown() {
	xxx_messageInfo_GameSetting.DiscardUnknown(m)
}

var xxx_messageInfo_GameSetting proto.InternalMessageInfo

func (m *GameSetting) GetConfigId() uint32 {
	if m != nil {
		return m.ConfigId
	}
	return 0
}

func (m *GameSetting) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *GameSetting) GetGift() *GiftInfo {
	if m != nil {
		return m.Gift
	}
	return nil
}

func (m *GameSetting) GetMaxOfferingPrice() uint32 {
	if m != nil {
		return m.MaxOfferingPrice
	}
	return 0
}

func (m *GameSetting) GetReturnGift() *GiftInfo {
	if m != nil {
		return m.ReturnGift
	}
	return nil
}

func (m *GameSetting) GetRelationshipDayMap() map[uint32]uint32 {
	if m != nil {
		return m.RelationshipDayMap
	}
	return nil
}

func (m *GameSetting) GetReturnGiftNum() uint32 {
	if m != nil {
		return m.ReturnGiftNum
	}
	return 0
}

type SettleRelationship struct {
	OfferUser            uint32   `protobuf:"varint,1,opt,name=offer_user,json=offerUser,proto3" json:"offer_user,omitempty"`
	SaleUser             uint32   `protobuf:"varint,2,opt,name=sale_user,json=saleUser,proto3" json:"sale_user,omitempty"`
	Duration             uint32   `protobuf:"varint,3,opt,name=duration,proto3" json:"duration,omitempty"`
	GiftNum              uint32   `protobuf:"varint,4,opt,name=gift_num,json=giftNum,proto3" json:"gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleRelationship) Reset()         { *m = SettleRelationship{} }
func (m *SettleRelationship) String() string { return proto.CompactTextString(m) }
func (*SettleRelationship) ProtoMessage()    {}
func (*SettleRelationship) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{18}
}
func (m *SettleRelationship) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleRelationship.Unmarshal(m, b)
}
func (m *SettleRelationship) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleRelationship.Marshal(b, m, deterministic)
}
func (dst *SettleRelationship) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleRelationship.Merge(dst, src)
}
func (m *SettleRelationship) XXX_Size() int {
	return xxx_messageInfo_SettleRelationship.Size(m)
}
func (m *SettleRelationship) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleRelationship.DiscardUnknown(m)
}

var xxx_messageInfo_SettleRelationship proto.InternalMessageInfo

func (m *SettleRelationship) GetOfferUser() uint32 {
	if m != nil {
		return m.OfferUser
	}
	return 0
}

func (m *SettleRelationship) GetSaleUser() uint32 {
	if m != nil {
		return m.SaleUser
	}
	return 0
}

func (m *SettleRelationship) GetDuration() uint32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *SettleRelationship) GetGiftNum() uint32 {
	if m != nil {
		return m.GiftNum
	}
	return 0
}

type CurOfferingGameInfo struct {
	ChannelId            uint32              `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64               `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	Version              int64               `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	TopList              *TopList            `protobuf:"bytes,4,opt,name=top_list,json=topList,proto3" json:"top_list,omitempty"`
	Phase                GamePhase           `protobuf:"varint,5,opt,name=phase,proto3,enum=offer_room.GamePhase" json:"phase,omitempty"`
	IsLocked             bool                `protobuf:"varint,6,opt,name=is_locked,json=isLocked,proto3" json:"is_locked,omitempty"`
	LockedUid            uint32              `protobuf:"varint,7,opt,name=locked_uid,json=lockedUid,proto3" json:"locked_uid,omitempty"`
	GameSetting          *GameSetting        `protobuf:"bytes,8,opt,name=game_setting,json=gameSetting,proto3" json:"game_setting,omitempty"`
	Relationship         *SettleRelationship `protobuf:"bytes,9,opt,name=relationship,proto3" json:"relationship,omitempty"`
	HonoredGuest         uint32              `protobuf:"varint,10,opt,name=honored_guest,json=honoredGuest,proto3" json:"honored_guest,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *CurOfferingGameInfo) Reset()         { *m = CurOfferingGameInfo{} }
func (m *CurOfferingGameInfo) String() string { return proto.CompactTextString(m) }
func (*CurOfferingGameInfo) ProtoMessage()    {}
func (*CurOfferingGameInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{19}
}
func (m *CurOfferingGameInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CurOfferingGameInfo.Unmarshal(m, b)
}
func (m *CurOfferingGameInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CurOfferingGameInfo.Marshal(b, m, deterministic)
}
func (dst *CurOfferingGameInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CurOfferingGameInfo.Merge(dst, src)
}
func (m *CurOfferingGameInfo) XXX_Size() int {
	return xxx_messageInfo_CurOfferingGameInfo.Size(m)
}
func (m *CurOfferingGameInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_CurOfferingGameInfo.DiscardUnknown(m)
}

var xxx_messageInfo_CurOfferingGameInfo proto.InternalMessageInfo

func (m *CurOfferingGameInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *CurOfferingGameInfo) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *CurOfferingGameInfo) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *CurOfferingGameInfo) GetTopList() *TopList {
	if m != nil {
		return m.TopList
	}
	return nil
}

func (m *CurOfferingGameInfo) GetPhase() GamePhase {
	if m != nil {
		return m.Phase
	}
	return GamePhase_GAME_PHASE_UNSPECIFIED
}

func (m *CurOfferingGameInfo) GetIsLocked() bool {
	if m != nil {
		return m.IsLocked
	}
	return false
}

func (m *CurOfferingGameInfo) GetLockedUid() uint32 {
	if m != nil {
		return m.LockedUid
	}
	return 0
}

func (m *CurOfferingGameInfo) GetGameSetting() *GameSetting {
	if m != nil {
		return m.GameSetting
	}
	return nil
}

func (m *CurOfferingGameInfo) GetRelationship() *SettleRelationship {
	if m != nil {
		return m.Relationship
	}
	return nil
}

func (m *CurOfferingGameInfo) GetHonoredGuest() uint32 {
	if m != nil {
		return m.HonoredGuest
	}
	return 0
}

type InitOfferingGameRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	HonoredGuestId       uint32   `protobuf:"varint,2,opt,name=honored_guest_id,json=honoredGuestId,proto3" json:"honored_guest_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *InitOfferingGameRequest) Reset()         { *m = InitOfferingGameRequest{} }
func (m *InitOfferingGameRequest) String() string { return proto.CompactTextString(m) }
func (*InitOfferingGameRequest) ProtoMessage()    {}
func (*InitOfferingGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{20}
}
func (m *InitOfferingGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitOfferingGameRequest.Unmarshal(m, b)
}
func (m *InitOfferingGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitOfferingGameRequest.Marshal(b, m, deterministic)
}
func (dst *InitOfferingGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitOfferingGameRequest.Merge(dst, src)
}
func (m *InitOfferingGameRequest) XXX_Size() int {
	return xxx_messageInfo_InitOfferingGameRequest.Size(m)
}
func (m *InitOfferingGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InitOfferingGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InitOfferingGameRequest proto.InternalMessageInfo

func (m *InitOfferingGameRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *InitOfferingGameRequest) GetHonoredGuestId() uint32 {
	if m != nil {
		return m.HonoredGuestId
	}
	return 0
}

type InitOfferingGameResponse struct {
	Info                 *CurOfferingGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *InitOfferingGameResponse) Reset()         { *m = InitOfferingGameResponse{} }
func (m *InitOfferingGameResponse) String() string { return proto.CompactTextString(m) }
func (*InitOfferingGameResponse) ProtoMessage()    {}
func (*InitOfferingGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{21}
}
func (m *InitOfferingGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InitOfferingGameResponse.Unmarshal(m, b)
}
func (m *InitOfferingGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InitOfferingGameResponse.Marshal(b, m, deterministic)
}
func (dst *InitOfferingGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InitOfferingGameResponse.Merge(dst, src)
}
func (m *InitOfferingGameResponse) XXX_Size() int {
	return xxx_messageInfo_InitOfferingGameResponse.Size(m)
}
func (m *InitOfferingGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_InitOfferingGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_InitOfferingGameResponse proto.InternalMessageInfo

func (m *InitOfferingGameResponse) GetInfo() *CurOfferingGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SubmitOfferingSettingRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64    `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	HonoredGuestId       uint32   `protobuf:"varint,3,opt,name=honored_guest_id,json=honoredGuestId,proto3" json:"honored_guest_id,omitempty"`
	ConfigId             uint32   `protobuf:"varint,4,opt,name=config_id,json=configId,proto3" json:"config_id,omitempty"`
	RelationshipName     string   `protobuf:"bytes,5,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	GiftId               uint32   `protobuf:"varint,6,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ReturnGiftId         uint32   `protobuf:"varint,8,opt,name=return_gift_id,json=returnGiftId,proto3" json:"return_gift_id,omitempty"`
	ReturnGiftNum        uint32   `protobuf:"varint,9,opt,name=return_gift_num,json=returnGiftNum,proto3" json:"return_gift_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitOfferingSettingRequest) Reset()         { *m = SubmitOfferingSettingRequest{} }
func (m *SubmitOfferingSettingRequest) String() string { return proto.CompactTextString(m) }
func (*SubmitOfferingSettingRequest) ProtoMessage()    {}
func (*SubmitOfferingSettingRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{22}
}
func (m *SubmitOfferingSettingRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitOfferingSettingRequest.Unmarshal(m, b)
}
func (m *SubmitOfferingSettingRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitOfferingSettingRequest.Marshal(b, m, deterministic)
}
func (dst *SubmitOfferingSettingRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitOfferingSettingRequest.Merge(dst, src)
}
func (m *SubmitOfferingSettingRequest) XXX_Size() int {
	return xxx_messageInfo_SubmitOfferingSettingRequest.Size(m)
}
func (m *SubmitOfferingSettingRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitOfferingSettingRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitOfferingSettingRequest proto.InternalMessageInfo

func (m *SubmitOfferingSettingRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SubmitOfferingSettingRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *SubmitOfferingSettingRequest) GetHonoredGuestId() uint32 {
	if m != nil {
		return m.HonoredGuestId
	}
	return 0
}

func (m *SubmitOfferingSettingRequest) GetConfigId() uint32 {
	if m != nil {
		return m.ConfigId
	}
	return 0
}

func (m *SubmitOfferingSettingRequest) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *SubmitOfferingSettingRequest) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *SubmitOfferingSettingRequest) GetReturnGiftId() uint32 {
	if m != nil {
		return m.ReturnGiftId
	}
	return 0
}

func (m *SubmitOfferingSettingRequest) GetReturnGiftNum() uint32 {
	if m != nil {
		return m.ReturnGiftNum
	}
	return 0
}

type SubmitOfferingSettingResponse struct {
	Info                 *CurOfferingGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SubmitOfferingSettingResponse) Reset()         { *m = SubmitOfferingSettingResponse{} }
func (m *SubmitOfferingSettingResponse) String() string { return proto.CompactTextString(m) }
func (*SubmitOfferingSettingResponse) ProtoMessage()    {}
func (*SubmitOfferingSettingResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{23}
}
func (m *SubmitOfferingSettingResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitOfferingSettingResponse.Unmarshal(m, b)
}
func (m *SubmitOfferingSettingResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitOfferingSettingResponse.Marshal(b, m, deterministic)
}
func (dst *SubmitOfferingSettingResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitOfferingSettingResponse.Merge(dst, src)
}
func (m *SubmitOfferingSettingResponse) XXX_Size() int {
	return xxx_messageInfo_SubmitOfferingSettingResponse.Size(m)
}
func (m *SubmitOfferingSettingResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitOfferingSettingResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitOfferingSettingResponse proto.InternalMessageInfo

func (m *SubmitOfferingSettingResponse) GetInfo() *CurOfferingGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SettleOfferingGameRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64    `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	AppId                uint32   `protobuf:"varint,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	TopUniId             string   `protobuf:"bytes,4,opt,name=top_uni_id,json=topUniId,proto3" json:"top_uni_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SettleOfferingGameRequest) Reset()         { *m = SettleOfferingGameRequest{} }
func (m *SettleOfferingGameRequest) String() string { return proto.CompactTextString(m) }
func (*SettleOfferingGameRequest) ProtoMessage()    {}
func (*SettleOfferingGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{24}
}
func (m *SettleOfferingGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleOfferingGameRequest.Unmarshal(m, b)
}
func (m *SettleOfferingGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleOfferingGameRequest.Marshal(b, m, deterministic)
}
func (dst *SettleOfferingGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleOfferingGameRequest.Merge(dst, src)
}
func (m *SettleOfferingGameRequest) XXX_Size() int {
	return xxx_messageInfo_SettleOfferingGameRequest.Size(m)
}
func (m *SettleOfferingGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleOfferingGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_SettleOfferingGameRequest proto.InternalMessageInfo

func (m *SettleOfferingGameRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SettleOfferingGameRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *SettleOfferingGameRequest) GetAppId() uint32 {
	if m != nil {
		return m.AppId
	}
	return 0
}

func (m *SettleOfferingGameRequest) GetTopUniId() string {
	if m != nil {
		return m.TopUniId
	}
	return ""
}

type SettleOfferingGameResponse struct {
	Info                 *CurOfferingGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *SettleOfferingGameResponse) Reset()         { *m = SettleOfferingGameResponse{} }
func (m *SettleOfferingGameResponse) String() string { return proto.CompactTextString(m) }
func (*SettleOfferingGameResponse) ProtoMessage()    {}
func (*SettleOfferingGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{25}
}
func (m *SettleOfferingGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SettleOfferingGameResponse.Unmarshal(m, b)
}
func (m *SettleOfferingGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SettleOfferingGameResponse.Marshal(b, m, deterministic)
}
func (dst *SettleOfferingGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SettleOfferingGameResponse.Merge(dst, src)
}
func (m *SettleOfferingGameResponse) XXX_Size() int {
	return xxx_messageInfo_SettleOfferingGameResponse.Size(m)
}
func (m *SettleOfferingGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_SettleOfferingGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_SettleOfferingGameResponse proto.InternalMessageInfo

func (m *SettleOfferingGameResponse) GetInfo() *CurOfferingGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AbortOfferingGameRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64    `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AbortOfferingGameRequest) Reset()         { *m = AbortOfferingGameRequest{} }
func (m *AbortOfferingGameRequest) String() string { return proto.CompactTextString(m) }
func (*AbortOfferingGameRequest) ProtoMessage()    {}
func (*AbortOfferingGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{26}
}
func (m *AbortOfferingGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AbortOfferingGameRequest.Unmarshal(m, b)
}
func (m *AbortOfferingGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AbortOfferingGameRequest.Marshal(b, m, deterministic)
}
func (dst *AbortOfferingGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AbortOfferingGameRequest.Merge(dst, src)
}
func (m *AbortOfferingGameRequest) XXX_Size() int {
	return xxx_messageInfo_AbortOfferingGameRequest.Size(m)
}
func (m *AbortOfferingGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_AbortOfferingGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_AbortOfferingGameRequest proto.InternalMessageInfo

func (m *AbortOfferingGameRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *AbortOfferingGameRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type AbortOfferingGameResponse struct {
	Info                 *CurOfferingGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *AbortOfferingGameResponse) Reset()         { *m = AbortOfferingGameResponse{} }
func (m *AbortOfferingGameResponse) String() string { return proto.CompactTextString(m) }
func (*AbortOfferingGameResponse) ProtoMessage()    {}
func (*AbortOfferingGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{27}
}
func (m *AbortOfferingGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AbortOfferingGameResponse.Unmarshal(m, b)
}
func (m *AbortOfferingGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AbortOfferingGameResponse.Marshal(b, m, deterministic)
}
func (dst *AbortOfferingGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AbortOfferingGameResponse.Merge(dst, src)
}
func (m *AbortOfferingGameResponse) XXX_Size() int {
	return xxx_messageInfo_AbortOfferingGameResponse.Size(m)
}
func (m *AbortOfferingGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_AbortOfferingGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_AbortOfferingGameResponse proto.InternalMessageInfo

func (m *AbortOfferingGameResponse) GetInfo() *CurOfferingGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type EndOfferingGameRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64    `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EndOfferingGameRequest) Reset()         { *m = EndOfferingGameRequest{} }
func (m *EndOfferingGameRequest) String() string { return proto.CompactTextString(m) }
func (*EndOfferingGameRequest) ProtoMessage()    {}
func (*EndOfferingGameRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{28}
}
func (m *EndOfferingGameRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndOfferingGameRequest.Unmarshal(m, b)
}
func (m *EndOfferingGameRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndOfferingGameRequest.Marshal(b, m, deterministic)
}
func (dst *EndOfferingGameRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndOfferingGameRequest.Merge(dst, src)
}
func (m *EndOfferingGameRequest) XXX_Size() int {
	return xxx_messageInfo_EndOfferingGameRequest.Size(m)
}
func (m *EndOfferingGameRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_EndOfferingGameRequest.DiscardUnknown(m)
}

var xxx_messageInfo_EndOfferingGameRequest proto.InternalMessageInfo

func (m *EndOfferingGameRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *EndOfferingGameRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type EndOfferingGameResponse struct {
	Info                 *CurOfferingGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *EndOfferingGameResponse) Reset()         { *m = EndOfferingGameResponse{} }
func (m *EndOfferingGameResponse) String() string { return proto.CompactTextString(m) }
func (*EndOfferingGameResponse) ProtoMessage()    {}
func (*EndOfferingGameResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{29}
}
func (m *EndOfferingGameResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EndOfferingGameResponse.Unmarshal(m, b)
}
func (m *EndOfferingGameResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EndOfferingGameResponse.Marshal(b, m, deterministic)
}
func (dst *EndOfferingGameResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EndOfferingGameResponse.Merge(dst, src)
}
func (m *EndOfferingGameResponse) XXX_Size() int {
	return xxx_messageInfo_EndOfferingGameResponse.Size(m)
}
func (m *EndOfferingGameResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_EndOfferingGameResponse.DiscardUnknown(m)
}

var xxx_messageInfo_EndOfferingGameResponse proto.InternalMessageInfo

func (m *EndOfferingGameResponse) GetInfo() *CurOfferingGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type GetCurOfferingGameInfoRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCurOfferingGameInfoRequest) Reset()         { *m = GetCurOfferingGameInfoRequest{} }
func (m *GetCurOfferingGameInfoRequest) String() string { return proto.CompactTextString(m) }
func (*GetCurOfferingGameInfoRequest) ProtoMessage()    {}
func (*GetCurOfferingGameInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{30}
}
func (m *GetCurOfferingGameInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurOfferingGameInfoRequest.Unmarshal(m, b)
}
func (m *GetCurOfferingGameInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurOfferingGameInfoRequest.Marshal(b, m, deterministic)
}
func (dst *GetCurOfferingGameInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurOfferingGameInfoRequest.Merge(dst, src)
}
func (m *GetCurOfferingGameInfoRequest) XXX_Size() int {
	return xxx_messageInfo_GetCurOfferingGameInfoRequest.Size(m)
}
func (m *GetCurOfferingGameInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurOfferingGameInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurOfferingGameInfoRequest proto.InternalMessageInfo

func (m *GetCurOfferingGameInfoRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetCurOfferingGameInfoResponse struct {
	Info                 *CurOfferingGameInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetCurOfferingGameInfoResponse) Reset()         { *m = GetCurOfferingGameInfoResponse{} }
func (m *GetCurOfferingGameInfoResponse) String() string { return proto.CompactTextString(m) }
func (*GetCurOfferingGameInfoResponse) ProtoMessage()    {}
func (*GetCurOfferingGameInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{31}
}
func (m *GetCurOfferingGameInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCurOfferingGameInfoResponse.Unmarshal(m, b)
}
func (m *GetCurOfferingGameInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCurOfferingGameInfoResponse.Marshal(b, m, deterministic)
}
func (dst *GetCurOfferingGameInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCurOfferingGameInfoResponse.Merge(dst, src)
}
func (m *GetCurOfferingGameInfoResponse) XXX_Size() int {
	return xxx_messageInfo_GetCurOfferingGameInfoResponse.Size(m)
}
func (m *GetCurOfferingGameInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCurOfferingGameInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCurOfferingGameInfoResponse proto.InternalMessageInfo

func (m *GetCurOfferingGameInfoResponse) GetInfo() *CurOfferingGameInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type NamePriceRequest struct {
	ChannelId            uint32                 `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64                  `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	Uid                  uint32                 `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	UserFakeId           uint32                 `protobuf:"varint,4,opt,name=user_fake_id,json=userFakeId,proto3" json:"user_fake_id,omitempty"`
	Operation            NamePriceOnceOperation `protobuf:"varint,5,opt,name=operation,proto3,enum=offer_room.NamePriceOnceOperation" json:"operation,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *NamePriceRequest) Reset()         { *m = NamePriceRequest{} }
func (m *NamePriceRequest) String() string { return proto.CompactTextString(m) }
func (*NamePriceRequest) ProtoMessage()    {}
func (*NamePriceRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{32}
}
func (m *NamePriceRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NamePriceRequest.Unmarshal(m, b)
}
func (m *NamePriceRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NamePriceRequest.Marshal(b, m, deterministic)
}
func (dst *NamePriceRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NamePriceRequest.Merge(dst, src)
}
func (m *NamePriceRequest) XXX_Size() int {
	return xxx_messageInfo_NamePriceRequest.Size(m)
}
func (m *NamePriceRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_NamePriceRequest.DiscardUnknown(m)
}

var xxx_messageInfo_NamePriceRequest proto.InternalMessageInfo

func (m *NamePriceRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NamePriceRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *NamePriceRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NamePriceRequest) GetUserFakeId() uint32 {
	if m != nil {
		return m.UserFakeId
	}
	return 0
}

func (m *NamePriceRequest) GetOperation() NamePriceOnceOperation {
	if m != nil {
		return m.Operation
	}
	return NamePriceOnceOperation_NAME_PRICE_ONCE_OPERATION_UNSPECIFIED
}

type NamePriceResponse struct {
	Version              int64    `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	TopList              *TopList `protobuf:"bytes,3,opt,name=top_list,json=topList,proto3" json:"top_list,omitempty"`
	IdLocked             bool     `protobuf:"varint,4,opt,name=id_locked,json=idLocked,proto3" json:"id_locked,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NamePriceResponse) Reset()         { *m = NamePriceResponse{} }
func (m *NamePriceResponse) String() string { return proto.CompactTextString(m) }
func (*NamePriceResponse) ProtoMessage()    {}
func (*NamePriceResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{33}
}
func (m *NamePriceResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NamePriceResponse.Unmarshal(m, b)
}
func (m *NamePriceResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NamePriceResponse.Marshal(b, m, deterministic)
}
func (dst *NamePriceResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NamePriceResponse.Merge(dst, src)
}
func (m *NamePriceResponse) XXX_Size() int {
	return xxx_messageInfo_NamePriceResponse.Size(m)
}
func (m *NamePriceResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_NamePriceResponse.DiscardUnknown(m)
}

var xxx_messageInfo_NamePriceResponse proto.InternalMessageInfo

func (m *NamePriceResponse) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

func (m *NamePriceResponse) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *NamePriceResponse) GetTopList() *TopList {
	if m != nil {
		return m.TopList
	}
	return nil
}

func (m *NamePriceResponse) GetIdLocked() bool {
	if m != nil {
		return m.IdLocked
	}
	return false
}

type OfferRoomCardInfoRequest struct {
	FromUid   uint32 `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	TargetUid uint32 `protobuf:"varint,2,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	// 个人主页需要前三条数据
	NeedTop3Relationships bool     `protobuf:"varint,3,opt,name=need_top3_relationships,json=needTop3Relationships,proto3" json:"need_top3_relationships,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *OfferRoomCardInfoRequest) Reset()         { *m = OfferRoomCardInfoRequest{} }
func (m *OfferRoomCardInfoRequest) String() string { return proto.CompactTextString(m) }
func (*OfferRoomCardInfoRequest) ProtoMessage()    {}
func (*OfferRoomCardInfoRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{34}
}
func (m *OfferRoomCardInfoRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomCardInfoRequest.Unmarshal(m, b)
}
func (m *OfferRoomCardInfoRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomCardInfoRequest.Marshal(b, m, deterministic)
}
func (dst *OfferRoomCardInfoRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomCardInfoRequest.Merge(dst, src)
}
func (m *OfferRoomCardInfoRequest) XXX_Size() int {
	return xxx_messageInfo_OfferRoomCardInfoRequest.Size(m)
}
func (m *OfferRoomCardInfoRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomCardInfoRequest.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomCardInfoRequest proto.InternalMessageInfo

func (m *OfferRoomCardInfoRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *OfferRoomCardInfoRequest) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *OfferRoomCardInfoRequest) GetNeedTop3Relationships() bool {
	if m != nil {
		return m.NeedTop3Relationships
	}
	return false
}

type OfferRoomCardInfoResponse struct {
	TotalCnt         uint32 `protobuf:"varint,1,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	RelationshipName string `protobuf:"bytes,2,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	// =============== 个人资料卡 铭牌 =============
	Rank    uint32 `protobuf:"varint,3,opt,name=rank,proto3" json:"rank,omitempty"`
	StarCnt uint32 `protobuf:"varint,4,opt,name=star_cnt,json=starCnt,proto3" json:"star_cnt,omitempty"`
	// 个人主页需要前三条关系
	RelationshipList     []*OfferingRelationshipInfo `protobuf:"bytes,5,rep,name=relationship_list,json=relationshipList,proto3" json:"relationship_list,omitempty"`
	NameplateCfgVersion  uint32                      `protobuf:"varint,6,opt,name=nameplate_cfg_version,json=nameplateCfgVersion,proto3" json:"nameplate_cfg_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *OfferRoomCardInfoResponse) Reset()         { *m = OfferRoomCardInfoResponse{} }
func (m *OfferRoomCardInfoResponse) String() string { return proto.CompactTextString(m) }
func (*OfferRoomCardInfoResponse) ProtoMessage()    {}
func (*OfferRoomCardInfoResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{35}
}
func (m *OfferRoomCardInfoResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomCardInfoResponse.Unmarshal(m, b)
}
func (m *OfferRoomCardInfoResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomCardInfoResponse.Marshal(b, m, deterministic)
}
func (dst *OfferRoomCardInfoResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomCardInfoResponse.Merge(dst, src)
}
func (m *OfferRoomCardInfoResponse) XXX_Size() int {
	return xxx_messageInfo_OfferRoomCardInfoResponse.Size(m)
}
func (m *OfferRoomCardInfoResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomCardInfoResponse.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomCardInfoResponse proto.InternalMessageInfo

func (m *OfferRoomCardInfoResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

func (m *OfferRoomCardInfoResponse) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *OfferRoomCardInfoResponse) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *OfferRoomCardInfoResponse) GetStarCnt() uint32 {
	if m != nil {
		return m.StarCnt
	}
	return 0
}

func (m *OfferRoomCardInfoResponse) GetRelationshipList() []*OfferingRelationshipInfo {
	if m != nil {
		return m.RelationshipList
	}
	return nil
}

func (m *OfferRoomCardInfoResponse) GetNameplateCfgVersion() uint32 {
	if m != nil {
		return m.NameplateCfgVersion
	}
	return 0
}

type GetUserOfferNumRequest struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	GameRoundId          int64    `protobuf:"varint,2,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	FakeId               uint32   `protobuf:"varint,4,opt,name=fake_id,json=fakeId,proto3" json:"fake_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOfferNumRequest) Reset()         { *m = GetUserOfferNumRequest{} }
func (m *GetUserOfferNumRequest) String() string { return proto.CompactTextString(m) }
func (*GetUserOfferNumRequest) ProtoMessage()    {}
func (*GetUserOfferNumRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{36}
}
func (m *GetUserOfferNumRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOfferNumRequest.Unmarshal(m, b)
}
func (m *GetUserOfferNumRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOfferNumRequest.Marshal(b, m, deterministic)
}
func (dst *GetUserOfferNumRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOfferNumRequest.Merge(dst, src)
}
func (m *GetUserOfferNumRequest) XXX_Size() int {
	return xxx_messageInfo_GetUserOfferNumRequest.Size(m)
}
func (m *GetUserOfferNumRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOfferNumRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOfferNumRequest proto.InternalMessageInfo

func (m *GetUserOfferNumRequest) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserOfferNumRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

func (m *GetUserOfferNumRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserOfferNumRequest) GetFakeId() uint32 {
	if m != nil {
		return m.FakeId
	}
	return 0
}

type GetUserOfferNumResponse struct {
	Num                  uint32   `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserOfferNumResponse) Reset()         { *m = GetUserOfferNumResponse{} }
func (m *GetUserOfferNumResponse) String() string { return proto.CompactTextString(m) }
func (*GetUserOfferNumResponse) ProtoMessage()    {}
func (*GetUserOfferNumResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{37}
}
func (m *GetUserOfferNumResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserOfferNumResponse.Unmarshal(m, b)
}
func (m *GetUserOfferNumResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserOfferNumResponse.Marshal(b, m, deterministic)
}
func (dst *GetUserOfferNumResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserOfferNumResponse.Merge(dst, src)
}
func (m *GetUserOfferNumResponse) XXX_Size() int {
	return xxx_messageInfo_GetUserOfferNumResponse.Size(m)
}
func (m *GetUserOfferNumResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserOfferNumResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserOfferNumResponse proto.InternalMessageInfo

func (m *GetUserOfferNumResponse) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

type GiftInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Price                uint32   `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Icon                 string   `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GiftInfo) Reset()         { *m = GiftInfo{} }
func (m *GiftInfo) String() string { return proto.CompactTextString(m) }
func (*GiftInfo) ProtoMessage()    {}
func (*GiftInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{38}
}
func (m *GiftInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GiftInfo.Unmarshal(m, b)
}
func (m *GiftInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GiftInfo.Marshal(b, m, deterministic)
}
func (dst *GiftInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GiftInfo.Merge(dst, src)
}
func (m *GiftInfo) XXX_Size() int {
	return xxx_messageInfo_GiftInfo.Size(m)
}
func (m *GiftInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GiftInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GiftInfo proto.InternalMessageInfo

func (m *GiftInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GiftInfo) GetPrice() uint32 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *GiftInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GiftInfo) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

// 发放关系
type CreateRelationshipRequest struct {
	FromUid              uint32   `protobuf:"varint,1,opt,name=from_uid,json=fromUid,proto3" json:"from_uid,omitempty"`
	FromIsFake           bool     `protobuf:"varint,2,opt,name=from_is_fake,json=fromIsFake,proto3" json:"from_is_fake,omitempty"`
	ToUid                uint32   `protobuf:"varint,3,opt,name=to_uid,json=toUid,proto3" json:"to_uid,omitempty"`
	ToIsFake             bool     `protobuf:"varint,4,opt,name=to_is_fake,json=toIsFake,proto3" json:"to_is_fake,omitempty"`
	RelationshipName     string   `protobuf:"bytes,5,opt,name=relationship_name,json=relationshipName,proto3" json:"relationship_name,omitempty"`
	RelationshipDuration int64    `protobuf:"varint,6,opt,name=relationship_duration,json=relationshipDuration,proto3" json:"relationship_duration,omitempty"`
	GiftId               uint32   `protobuf:"varint,7,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	GiftCnt              uint32   `protobuf:"varint,8,opt,name=gift_cnt,json=giftCnt,proto3" json:"gift_cnt,omitempty"`
	GameId               int64    `protobuf:"varint,9,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	GiftIcon             string   `protobuf:"bytes,10,opt,name=gift_icon,json=giftIcon,proto3" json:"gift_icon,omitempty"`
	IsMaxPrice           bool     `protobuf:"varint,11,opt,name=is_max_price,json=isMaxPrice,proto3" json:"is_max_price,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateRelationshipRequest) Reset()         { *m = CreateRelationshipRequest{} }
func (m *CreateRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*CreateRelationshipRequest) ProtoMessage()    {}
func (*CreateRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{39}
}
func (m *CreateRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRelationshipRequest.Unmarshal(m, b)
}
func (m *CreateRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *CreateRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRelationshipRequest.Merge(dst, src)
}
func (m *CreateRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_CreateRelationshipRequest.Size(m)
}
func (m *CreateRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRelationshipRequest proto.InternalMessageInfo

func (m *CreateRelationshipRequest) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *CreateRelationshipRequest) GetFromIsFake() bool {
	if m != nil {
		return m.FromIsFake
	}
	return false
}

func (m *CreateRelationshipRequest) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

func (m *CreateRelationshipRequest) GetToIsFake() bool {
	if m != nil {
		return m.ToIsFake
	}
	return false
}

func (m *CreateRelationshipRequest) GetRelationshipName() string {
	if m != nil {
		return m.RelationshipName
	}
	return ""
}

func (m *CreateRelationshipRequest) GetRelationshipDuration() int64 {
	if m != nil {
		return m.RelationshipDuration
	}
	return 0
}

func (m *CreateRelationshipRequest) GetGiftId() uint32 {
	if m != nil {
		return m.GiftId
	}
	return 0
}

func (m *CreateRelationshipRequest) GetGiftCnt() uint32 {
	if m != nil {
		return m.GiftCnt
	}
	return 0
}

func (m *CreateRelationshipRequest) GetGameId() int64 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CreateRelationshipRequest) GetGiftIcon() string {
	if m != nil {
		return m.GiftIcon
	}
	return ""
}

func (m *CreateRelationshipRequest) GetIsMaxPrice() bool {
	if m != nil {
		return m.IsMaxPrice
	}
	return false
}

type CreateRelationshipResponse struct {
	// <uid, newRank>
	LevelUpMap           map[uint32]uint32 `protobuf:"bytes,1,rep,name=level_up_map,json=levelUpMap,proto3" json:"level_up_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *CreateRelationshipResponse) Reset()         { *m = CreateRelationshipResponse{} }
func (m *CreateRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*CreateRelationshipResponse) ProtoMessage()    {}
func (*CreateRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{40}
}
func (m *CreateRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateRelationshipResponse.Unmarshal(m, b)
}
func (m *CreateRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *CreateRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateRelationshipResponse.Merge(dst, src)
}
func (m *CreateRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_CreateRelationshipResponse.Size(m)
}
func (m *CreateRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CreateRelationshipResponse proto.InternalMessageInfo

func (m *CreateRelationshipResponse) GetLevelUpMap() map[uint32]uint32 {
	if m != nil {
		return m.LevelUpMap
	}
	return nil
}

// 请求时间范围
type TimeRangeReq struct {
	BeginTime            int64    `protobuf:"varint,1,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Params               string   `protobuf:"bytes,3,opt,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeRangeReq) Reset()         { *m = TimeRangeReq{} }
func (m *TimeRangeReq) String() string { return proto.CompactTextString(m) }
func (*TimeRangeReq) ProtoMessage()    {}
func (*TimeRangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{41}
}
func (m *TimeRangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeRangeReq.Unmarshal(m, b)
}
func (m *TimeRangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeRangeReq.Marshal(b, m, deterministic)
}
func (dst *TimeRangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeRangeReq.Merge(dst, src)
}
func (m *TimeRangeReq) XXX_Size() int {
	return xxx_messageInfo_TimeRangeReq.Size(m)
}
func (m *TimeRangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeRangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TimeRangeReq proto.InternalMessageInfo

func (m *TimeRangeReq) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *TimeRangeReq) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *TimeRangeReq) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

// 响应order_id个数
type CountResp struct {
	Count                uint32   `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Value                uint32   `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CountResp) Reset()         { *m = CountResp{} }
func (m *CountResp) String() string { return proto.CompactTextString(m) }
func (*CountResp) ProtoMessage()    {}
func (*CountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{42}
}
func (m *CountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CountResp.Unmarshal(m, b)
}
func (m *CountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CountResp.Marshal(b, m, deterministic)
}
func (dst *CountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CountResp.Merge(dst, src)
}
func (m *CountResp) XXX_Size() int {
	return xxx_messageInfo_CountResp.Size(m)
}
func (m *CountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CountResp.DiscardUnknown(m)
}

var xxx_messageInfo_CountResp proto.InternalMessageInfo

func (m *CountResp) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *CountResp) GetValue() uint32 {
	if m != nil {
		return m.Value
	}
	return 0
}

// 响应orderId详情
type OrderIdsResp struct {
	OrderIds             []string `protobuf:"bytes,1,rep,name=order_ids,json=orderIds,proto3" json:"order_ids,omitempty"`
	Values               []uint32 `protobuf:"varint,2,rep,packed,name=values,proto3" json:"values,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OrderIdsResp) Reset()         { *m = OrderIdsResp{} }
func (m *OrderIdsResp) String() string { return proto.CompactTextString(m) }
func (*OrderIdsResp) ProtoMessage()    {}
func (*OrderIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{43}
}
func (m *OrderIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OrderIdsResp.Unmarshal(m, b)
}
func (m *OrderIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OrderIdsResp.Marshal(b, m, deterministic)
}
func (dst *OrderIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OrderIdsResp.Merge(dst, src)
}
func (m *OrderIdsResp) XXX_Size() int {
	return xxx_messageInfo_OrderIdsResp.Size(m)
}
func (m *OrderIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OrderIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_OrderIdsResp proto.InternalMessageInfo

func (m *OrderIdsResp) GetOrderIds() []string {
	if m != nil {
		return m.OrderIds
	}
	return nil
}

func (m *OrderIdsResp) GetValues() []uint32 {
	if m != nil {
		return m.Values
	}
	return nil
}

type ReplaceOrderReq struct {
	OrderId              string   `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	Params               string   `protobuf:"bytes,2,opt,name=params,proto3" json:"params,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReplaceOrderReq) Reset()         { *m = ReplaceOrderReq{} }
func (m *ReplaceOrderReq) String() string { return proto.CompactTextString(m) }
func (*ReplaceOrderReq) ProtoMessage()    {}
func (*ReplaceOrderReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{44}
}
func (m *ReplaceOrderReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplaceOrderReq.Unmarshal(m, b)
}
func (m *ReplaceOrderReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplaceOrderReq.Marshal(b, m, deterministic)
}
func (dst *ReplaceOrderReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplaceOrderReq.Merge(dst, src)
}
func (m *ReplaceOrderReq) XXX_Size() int {
	return xxx_messageInfo_ReplaceOrderReq.Size(m)
}
func (m *ReplaceOrderReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplaceOrderReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplaceOrderReq proto.InternalMessageInfo

func (m *ReplaceOrderReq) GetOrderId() string {
	if m != nil {
		return m.OrderId
	}
	return ""
}

func (m *ReplaceOrderReq) GetParams() string {
	if m != nil {
		return m.Params
	}
	return ""
}

type EmptyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyResp) Reset()         { *m = EmptyResp{} }
func (m *EmptyResp) String() string { return proto.CompactTextString(m) }
func (*EmptyResp) ProtoMessage()    {}
func (*EmptyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{45}
}
func (m *EmptyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyResp.Unmarshal(m, b)
}
func (m *EmptyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyResp.Marshal(b, m, deterministic)
}
func (dst *EmptyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyResp.Merge(dst, src)
}
func (m *EmptyResp) XXX_Size() int {
	return xxx_messageInfo_EmptyResp.Size(m)
}
func (m *EmptyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyResp.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyResp proto.InternalMessageInfo

type BatchGetUserConsumptionByGameRoundIdRequest struct {
	GameRoundId          int64    `protobuf:"varint,1,opt,name=game_round_id,json=gameRoundId,proto3" json:"game_round_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserConsumptionByGameRoundIdRequest) Reset() {
	*m = BatchGetUserConsumptionByGameRoundIdRequest{}
}
func (m *BatchGetUserConsumptionByGameRoundIdRequest) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserConsumptionByGameRoundIdRequest) ProtoMessage() {}
func (*BatchGetUserConsumptionByGameRoundIdRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{46}
}
func (m *BatchGetUserConsumptionByGameRoundIdRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdRequest.Unmarshal(m, b)
}
func (m *BatchGetUserConsumptionByGameRoundIdRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserConsumptionByGameRoundIdRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdRequest.Merge(dst, src)
}
func (m *BatchGetUserConsumptionByGameRoundIdRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdRequest.Size(m)
}
func (m *BatchGetUserConsumptionByGameRoundIdRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdRequest proto.InternalMessageInfo

func (m *BatchGetUserConsumptionByGameRoundIdRequest) GetGameRoundId() int64 {
	if m != nil {
		return m.GameRoundId
	}
	return 0
}

type BatchGetUserConsumptionByGameRoundIdResponse struct {
	List                 []*BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                                        `json:"-"`
	XXX_unrecognized     []byte                                                          `json:"-"`
	XXX_sizecache        int32                                                           `json:"-"`
}

func (m *BatchGetUserConsumptionByGameRoundIdResponse) Reset() {
	*m = BatchGetUserConsumptionByGameRoundIdResponse{}
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserConsumptionByGameRoundIdResponse) ProtoMessage() {}
func (*BatchGetUserConsumptionByGameRoundIdResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{47}
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse.Unmarshal(m, b)
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserConsumptionByGameRoundIdResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse.Merge(dst, src)
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse.Size(m)
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse proto.InternalMessageInfo

func (m *BatchGetUserConsumptionByGameRoundIdResponse) GetList() []*BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption {
	if m != nil {
		return m.List
	}
	return nil
}

type BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Num                  uint32   `protobuf:"varint,2,opt,name=num,proto3" json:"num,omitempty"`
	IsConsume            bool     `protobuf:"varint,3,opt,name=is_consume,json=isConsume,proto3" json:"is_consume,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) Reset() {
	*m = BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption{}
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) ProtoMessage() {}
func (*BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{47, 0}
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption.Unmarshal(m, b)
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption.Merge(dst, src)
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption.Size(m)
}
func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption proto.InternalMessageInfo

func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) GetNum() uint32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption) GetIsConsume() bool {
	if m != nil {
		return m.IsConsume
	}
	return false
}

type DelUserRelationshipNotExistFlagReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelUserRelationshipNotExistFlagReq) Reset()         { *m = DelUserRelationshipNotExistFlagReq{} }
func (m *DelUserRelationshipNotExistFlagReq) String() string { return proto.CompactTextString(m) }
func (*DelUserRelationshipNotExistFlagReq) ProtoMessage()    {}
func (*DelUserRelationshipNotExistFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{48}
}
func (m *DelUserRelationshipNotExistFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelUserRelationshipNotExistFlagReq.Unmarshal(m, b)
}
func (m *DelUserRelationshipNotExistFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelUserRelationshipNotExistFlagReq.Marshal(b, m, deterministic)
}
func (dst *DelUserRelationshipNotExistFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelUserRelationshipNotExistFlagReq.Merge(dst, src)
}
func (m *DelUserRelationshipNotExistFlagReq) XXX_Size() int {
	return xxx_messageInfo_DelUserRelationshipNotExistFlagReq.Size(m)
}
func (m *DelUserRelationshipNotExistFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelUserRelationshipNotExistFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelUserRelationshipNotExistFlagReq proto.InternalMessageInfo

func (m *DelUserRelationshipNotExistFlagReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type RebuildRelationshipCacheReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildRelationshipCacheReq) Reset()         { *m = RebuildRelationshipCacheReq{} }
func (m *RebuildRelationshipCacheReq) String() string { return proto.CompactTextString(m) }
func (*RebuildRelationshipCacheReq) ProtoMessage()    {}
func (*RebuildRelationshipCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{49}
}
func (m *RebuildRelationshipCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildRelationshipCacheReq.Unmarshal(m, b)
}
func (m *RebuildRelationshipCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildRelationshipCacheReq.Marshal(b, m, deterministic)
}
func (dst *RebuildRelationshipCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildRelationshipCacheReq.Merge(dst, src)
}
func (m *RebuildRelationshipCacheReq) XXX_Size() int {
	return xxx_messageInfo_RebuildRelationshipCacheReq.Size(m)
}
func (m *RebuildRelationshipCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildRelationshipCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildRelationshipCacheReq proto.InternalMessageInfo

type RebuildRelationshipCacheResp struct {
	UserNum              int32    `protobuf:"varint,1,opt,name=user_num,json=userNum,proto3" json:"user_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildRelationshipCacheResp) Reset()         { *m = RebuildRelationshipCacheResp{} }
func (m *RebuildRelationshipCacheResp) String() string { return proto.CompactTextString(m) }
func (*RebuildRelationshipCacheResp) ProtoMessage()    {}
func (*RebuildRelationshipCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{50}
}
func (m *RebuildRelationshipCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildRelationshipCacheResp.Unmarshal(m, b)
}
func (m *RebuildRelationshipCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildRelationshipCacheResp.Marshal(b, m, deterministic)
}
func (dst *RebuildRelationshipCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildRelationshipCacheResp.Merge(dst, src)
}
func (m *RebuildRelationshipCacheResp) XXX_Size() int {
	return xxx_messageInfo_RebuildRelationshipCacheResp.Size(m)
}
func (m *RebuildRelationshipCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildRelationshipCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildRelationshipCacheResp proto.InternalMessageInfo

func (m *RebuildRelationshipCacheResp) GetUserNum() int32 {
	if m != nil {
		return m.UserNum
	}
	return 0
}

// 获取用户拍拍铭牌信息
type GetUserNameplateInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	LastCnt              uint32   `protobuf:"varint,2,opt,name=last_cnt,json=lastCnt,proto3" json:"last_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserNameplateInfoReq) Reset()         { *m = GetUserNameplateInfoReq{} }
func (m *GetUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserNameplateInfoReq) ProtoMessage()    {}
func (*GetUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{51}
}
func (m *GetUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *GetUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNameplateInfoReq.Merge(dst, src)
}
func (m *GetUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserNameplateInfoReq.Size(m)
}
func (m *GetUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNameplateInfoReq proto.InternalMessageInfo

func (m *GetUserNameplateInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserNameplateInfoReq) GetLastCnt() uint32 {
	if m != nil {
		return m.LastCnt
	}
	return 0
}

type GetUserNameplateInfoResp struct {
	Rank                 uint32   `protobuf:"varint,1,opt,name=rank,proto3" json:"rank,omitempty"`
	StarCnt              uint32   `protobuf:"varint,2,opt,name=star_cnt,json=starCnt,proto3" json:"star_cnt,omitempty"`
	StarChangeCnt        int32    `protobuf:"varint,3,opt,name=star_change_cnt,json=starChangeCnt,proto3" json:"star_change_cnt,omitempty"`
	TotalStarCnt         uint32   `protobuf:"varint,4,opt,name=total_star_cnt,json=totalStarCnt,proto3" json:"total_star_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserNameplateInfoResp) Reset()         { *m = GetUserNameplateInfoResp{} }
func (m *GetUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserNameplateInfoResp) ProtoMessage()    {}
func (*GetUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{52}
}
func (m *GetUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *GetUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserNameplateInfoResp.Merge(dst, src)
}
func (m *GetUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserNameplateInfoResp.Size(m)
}
func (m *GetUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserNameplateInfoResp proto.InternalMessageInfo

func (m *GetUserNameplateInfoResp) GetRank() uint32 {
	if m != nil {
		return m.Rank
	}
	return 0
}

func (m *GetUserNameplateInfoResp) GetStarCnt() uint32 {
	if m != nil {
		return m.StarCnt
	}
	return 0
}

func (m *GetUserNameplateInfoResp) GetStarChangeCnt() int32 {
	if m != nil {
		return m.StarChangeCnt
	}
	return 0
}

func (m *GetUserNameplateInfoResp) GetTotalStarCnt() uint32 {
	if m != nil {
		return m.TotalStarCnt
	}
	return 0
}

// 更新用户拍拍铭牌信息（星星数量）
type UpdateUserNameplateInfoReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	StarCntChange        int32    `protobuf:"varint,2,opt,name=star_cnt_change,json=starCntChange,proto3" json:"star_cnt_change,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserNameplateInfoReq) Reset()         { *m = UpdateUserNameplateInfoReq{} }
func (m *UpdateUserNameplateInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserNameplateInfoReq) ProtoMessage()    {}
func (*UpdateUserNameplateInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{53}
}
func (m *UpdateUserNameplateInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserNameplateInfoReq.Unmarshal(m, b)
}
func (m *UpdateUserNameplateInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserNameplateInfoReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserNameplateInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserNameplateInfoReq.Merge(dst, src)
}
func (m *UpdateUserNameplateInfoReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserNameplateInfoReq.Size(m)
}
func (m *UpdateUserNameplateInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserNameplateInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserNameplateInfoReq proto.InternalMessageInfo

func (m *UpdateUserNameplateInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *UpdateUserNameplateInfoReq) GetStarCntChange() int32 {
	if m != nil {
		return m.StarCntChange
	}
	return 0
}

type UpdateUserNameplateInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserNameplateInfoResp) Reset()         { *m = UpdateUserNameplateInfoResp{} }
func (m *UpdateUserNameplateInfoResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserNameplateInfoResp) ProtoMessage()    {}
func (*UpdateUserNameplateInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{54}
}
func (m *UpdateUserNameplateInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserNameplateInfoResp.Unmarshal(m, b)
}
func (m *UpdateUserNameplateInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserNameplateInfoResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserNameplateInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserNameplateInfoResp.Merge(dst, src)
}
func (m *UpdateUserNameplateInfoResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserNameplateInfoResp.Size(m)
}
func (m *UpdateUserNameplateInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserNameplateInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserNameplateInfoResp proto.InternalMessageInfo

type GetNameplateConfigReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNameplateConfigReq) Reset()         { *m = GetNameplateConfigReq{} }
func (m *GetNameplateConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetNameplateConfigReq) ProtoMessage()    {}
func (*GetNameplateConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{55}
}
func (m *GetNameplateConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNameplateConfigReq.Unmarshal(m, b)
}
func (m *GetNameplateConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNameplateConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetNameplateConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNameplateConfigReq.Merge(dst, src)
}
func (m *GetNameplateConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetNameplateConfigReq.Size(m)
}
func (m *GetNameplateConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNameplateConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNameplateConfigReq proto.InternalMessageInfo

type GetNameplateConfigResp struct {
	NameplateList        []*GetNameplateConfigResp_Nameplate `protobuf:"bytes,1,rep,name=nameplate_list,json=nameplateList,proto3" json:"nameplate_list,omitempty"`
	NameplateCfgVersion  uint32                              `protobuf:"varint,2,opt,name=nameplate_cfg_version,json=nameplateCfgVersion,proto3" json:"nameplate_cfg_version,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetNameplateConfigResp) Reset()         { *m = GetNameplateConfigResp{} }
func (m *GetNameplateConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetNameplateConfigResp) ProtoMessage()    {}
func (*GetNameplateConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{56}
}
func (m *GetNameplateConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNameplateConfigResp.Unmarshal(m, b)
}
func (m *GetNameplateConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNameplateConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetNameplateConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNameplateConfigResp.Merge(dst, src)
}
func (m *GetNameplateConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetNameplateConfigResp.Size(m)
}
func (m *GetNameplateConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNameplateConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNameplateConfigResp proto.InternalMessageInfo

func (m *GetNameplateConfigResp) GetNameplateList() []*GetNameplateConfigResp_Nameplate {
	if m != nil {
		return m.NameplateList
	}
	return nil
}

func (m *GetNameplateConfigResp) GetNameplateCfgVersion() uint32 {
	if m != nil {
		return m.NameplateCfgVersion
	}
	return 0
}

// 铭牌资源
type GetNameplateConfigResp_Nameplate struct {
	Name                 string   `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Url                  string   `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	Md5                  string   `protobuf:"bytes,3,opt,name=md5,proto3" json:"md5,omitempty"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
	LevelThreshold       uint32   `protobuf:"varint,5,opt,name=level_threshold,json=levelThreshold,proto3" json:"level_threshold,omitempty"`
	LevelUpCnt           uint32   `protobuf:"varint,6,opt,name=level_up_cnt,json=levelUpCnt,proto3" json:"level_up_cnt,omitempty"`
	LevelName            string   `protobuf:"bytes,7,opt,name=level_name,json=levelName,proto3" json:"level_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNameplateConfigResp_Nameplate) Reset()         { *m = GetNameplateConfigResp_Nameplate{} }
func (m *GetNameplateConfigResp_Nameplate) String() string { return proto.CompactTextString(m) }
func (*GetNameplateConfigResp_Nameplate) ProtoMessage()    {}
func (*GetNameplateConfigResp_Nameplate) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{56, 0}
}
func (m *GetNameplateConfigResp_Nameplate) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNameplateConfigResp_Nameplate.Unmarshal(m, b)
}
func (m *GetNameplateConfigResp_Nameplate) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNameplateConfigResp_Nameplate.Marshal(b, m, deterministic)
}
func (dst *GetNameplateConfigResp_Nameplate) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNameplateConfigResp_Nameplate.Merge(dst, src)
}
func (m *GetNameplateConfigResp_Nameplate) XXX_Size() int {
	return xxx_messageInfo_GetNameplateConfigResp_Nameplate.Size(m)
}
func (m *GetNameplateConfigResp_Nameplate) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNameplateConfigResp_Nameplate.DiscardUnknown(m)
}

var xxx_messageInfo_GetNameplateConfigResp_Nameplate proto.InternalMessageInfo

func (m *GetNameplateConfigResp_Nameplate) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetNameplateConfigResp_Nameplate) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *GetNameplateConfigResp_Nameplate) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *GetNameplateConfigResp_Nameplate) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *GetNameplateConfigResp_Nameplate) GetLevelThreshold() uint32 {
	if m != nil {
		return m.LevelThreshold
	}
	return 0
}

func (m *GetNameplateConfigResp_Nameplate) GetLevelUpCnt() uint32 {
	if m != nil {
		return m.LevelUpCnt
	}
	return 0
}

func (m *GetNameplateConfigResp_Nameplate) GetLevelName() string {
	if m != nil {
		return m.LevelName
	}
	return ""
}

type LimitedRelationshipsConfig struct {
	Id                           uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                         string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	StartTimestamp               int64    `protobuf:"varint,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	EndTimestamp                 int64    `protobuf:"varint,4,opt,name=end_timestamp,json=endTimestamp,proto3" json:"end_timestamp,omitempty"`
	IsAvailableForRegisteredUser bool     `protobuf:"varint,5,opt,name=is_available_for_registered_user,json=isAvailableForRegisteredUser,proto3" json:"is_available_for_registered_user,omitempty"`
	UpdateTimestamp              int64    `protobuf:"varint,6,opt,name=update_timestamp,json=updateTimestamp,proto3" json:"update_timestamp,omitempty"`
	XXX_NoUnkeyedLiteral         struct{} `json:"-"`
	XXX_unrecognized             []byte   `json:"-"`
	XXX_sizecache                int32    `json:"-"`
}

func (m *LimitedRelationshipsConfig) Reset()         { *m = LimitedRelationshipsConfig{} }
func (m *LimitedRelationshipsConfig) String() string { return proto.CompactTextString(m) }
func (*LimitedRelationshipsConfig) ProtoMessage()    {}
func (*LimitedRelationshipsConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{57}
}
func (m *LimitedRelationshipsConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LimitedRelationshipsConfig.Unmarshal(m, b)
}
func (m *LimitedRelationshipsConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LimitedRelationshipsConfig.Marshal(b, m, deterministic)
}
func (dst *LimitedRelationshipsConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LimitedRelationshipsConfig.Merge(dst, src)
}
func (m *LimitedRelationshipsConfig) XXX_Size() int {
	return xxx_messageInfo_LimitedRelationshipsConfig.Size(m)
}
func (m *LimitedRelationshipsConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_LimitedRelationshipsConfig.DiscardUnknown(m)
}

var xxx_messageInfo_LimitedRelationshipsConfig proto.InternalMessageInfo

func (m *LimitedRelationshipsConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LimitedRelationshipsConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *LimitedRelationshipsConfig) GetStartTimestamp() int64 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *LimitedRelationshipsConfig) GetEndTimestamp() int64 {
	if m != nil {
		return m.EndTimestamp
	}
	return 0
}

func (m *LimitedRelationshipsConfig) GetIsAvailableForRegisteredUser() bool {
	if m != nil {
		return m.IsAvailableForRegisteredUser
	}
	return false
}

func (m *LimitedRelationshipsConfig) GetUpdateTimestamp() int64 {
	if m != nil {
		return m.UpdateTimestamp
	}
	return 0
}

type GetLimitedRelationshipsByPageRequest struct {
	PageIndex            uint32   `protobuf:"varint,1,opt,name=page_index,json=pageIndex,proto3" json:"page_index,omitempty"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	SearchName           string   `protobuf:"bytes,3,opt,name=search_name,json=searchName,proto3" json:"search_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLimitedRelationshipsByPageRequest) Reset()         { *m = GetLimitedRelationshipsByPageRequest{} }
func (m *GetLimitedRelationshipsByPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetLimitedRelationshipsByPageRequest) ProtoMessage()    {}
func (*GetLimitedRelationshipsByPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{58}
}
func (m *GetLimitedRelationshipsByPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLimitedRelationshipsByPageRequest.Unmarshal(m, b)
}
func (m *GetLimitedRelationshipsByPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLimitedRelationshipsByPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetLimitedRelationshipsByPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLimitedRelationshipsByPageRequest.Merge(dst, src)
}
func (m *GetLimitedRelationshipsByPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetLimitedRelationshipsByPageRequest.Size(m)
}
func (m *GetLimitedRelationshipsByPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLimitedRelationshipsByPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLimitedRelationshipsByPageRequest proto.InternalMessageInfo

func (m *GetLimitedRelationshipsByPageRequest) GetPageIndex() uint32 {
	if m != nil {
		return m.PageIndex
	}
	return 0
}

func (m *GetLimitedRelationshipsByPageRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetLimitedRelationshipsByPageRequest) GetSearchName() string {
	if m != nil {
		return m.SearchName
	}
	return ""
}

type GetLimitedRelationshipsByPageResponse struct {
	Data                 []*LimitedRelationshipsConfig `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	TotalCnt             uint32                        `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetLimitedRelationshipsByPageResponse) Reset()         { *m = GetLimitedRelationshipsByPageResponse{} }
func (m *GetLimitedRelationshipsByPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetLimitedRelationshipsByPageResponse) ProtoMessage()    {}
func (*GetLimitedRelationshipsByPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{59}
}
func (m *GetLimitedRelationshipsByPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLimitedRelationshipsByPageResponse.Unmarshal(m, b)
}
func (m *GetLimitedRelationshipsByPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLimitedRelationshipsByPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetLimitedRelationshipsByPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLimitedRelationshipsByPageResponse.Merge(dst, src)
}
func (m *GetLimitedRelationshipsByPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetLimitedRelationshipsByPageResponse.Size(m)
}
func (m *GetLimitedRelationshipsByPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLimitedRelationshipsByPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLimitedRelationshipsByPageResponse proto.InternalMessageInfo

func (m *GetLimitedRelationshipsByPageResponse) GetData() []*LimitedRelationshipsConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *GetLimitedRelationshipsByPageResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type BatchAddLimitedRelationshipRequest struct {
	Data                 []*LimitedRelationshipsConfig `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchAddLimitedRelationshipRequest) Reset()         { *m = BatchAddLimitedRelationshipRequest{} }
func (m *BatchAddLimitedRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*BatchAddLimitedRelationshipRequest) ProtoMessage()    {}
func (*BatchAddLimitedRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{60}
}
func (m *BatchAddLimitedRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddLimitedRelationshipRequest.Unmarshal(m, b)
}
func (m *BatchAddLimitedRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddLimitedRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *BatchAddLimitedRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddLimitedRelationshipRequest.Merge(dst, src)
}
func (m *BatchAddLimitedRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_BatchAddLimitedRelationshipRequest.Size(m)
}
func (m *BatchAddLimitedRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddLimitedRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddLimitedRelationshipRequest proto.InternalMessageInfo

func (m *BatchAddLimitedRelationshipRequest) GetData() []*LimitedRelationshipsConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type BatchAddLimitedRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchAddLimitedRelationshipResponse) Reset()         { *m = BatchAddLimitedRelationshipResponse{} }
func (m *BatchAddLimitedRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*BatchAddLimitedRelationshipResponse) ProtoMessage()    {}
func (*BatchAddLimitedRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{61}
}
func (m *BatchAddLimitedRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAddLimitedRelationshipResponse.Unmarshal(m, b)
}
func (m *BatchAddLimitedRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAddLimitedRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *BatchAddLimitedRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAddLimitedRelationshipResponse.Merge(dst, src)
}
func (m *BatchAddLimitedRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_BatchAddLimitedRelationshipResponse.Size(m)
}
func (m *BatchAddLimitedRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAddLimitedRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAddLimitedRelationshipResponse proto.InternalMessageInfo

type BatchDelLimitedRelationshipRequest struct {
	IdList               []uint32 `protobuf:"varint,1,rep,packed,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelLimitedRelationshipRequest) Reset()         { *m = BatchDelLimitedRelationshipRequest{} }
func (m *BatchDelLimitedRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*BatchDelLimitedRelationshipRequest) ProtoMessage()    {}
func (*BatchDelLimitedRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{62}
}
func (m *BatchDelLimitedRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelLimitedRelationshipRequest.Unmarshal(m, b)
}
func (m *BatchDelLimitedRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelLimitedRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *BatchDelLimitedRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelLimitedRelationshipRequest.Merge(dst, src)
}
func (m *BatchDelLimitedRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_BatchDelLimitedRelationshipRequest.Size(m)
}
func (m *BatchDelLimitedRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelLimitedRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelLimitedRelationshipRequest proto.InternalMessageInfo

func (m *BatchDelLimitedRelationshipRequest) GetIdList() []uint32 {
	if m != nil {
		return m.IdList
	}
	return nil
}

type BatchDelLimitedRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchDelLimitedRelationshipResponse) Reset()         { *m = BatchDelLimitedRelationshipResponse{} }
func (m *BatchDelLimitedRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*BatchDelLimitedRelationshipResponse) ProtoMessage()    {}
func (*BatchDelLimitedRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{63}
}
func (m *BatchDelLimitedRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchDelLimitedRelationshipResponse.Unmarshal(m, b)
}
func (m *BatchDelLimitedRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchDelLimitedRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *BatchDelLimitedRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchDelLimitedRelationshipResponse.Merge(dst, src)
}
func (m *BatchDelLimitedRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_BatchDelLimitedRelationshipResponse.Size(m)
}
func (m *BatchDelLimitedRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchDelLimitedRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchDelLimitedRelationshipResponse proto.InternalMessageInfo

type GetLimitedRelationshipDetailRequest struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLimitedRelationshipDetailRequest) Reset()         { *m = GetLimitedRelationshipDetailRequest{} }
func (m *GetLimitedRelationshipDetailRequest) String() string { return proto.CompactTextString(m) }
func (*GetLimitedRelationshipDetailRequest) ProtoMessage()    {}
func (*GetLimitedRelationshipDetailRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{64}
}
func (m *GetLimitedRelationshipDetailRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLimitedRelationshipDetailRequest.Unmarshal(m, b)
}
func (m *GetLimitedRelationshipDetailRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLimitedRelationshipDetailRequest.Marshal(b, m, deterministic)
}
func (dst *GetLimitedRelationshipDetailRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLimitedRelationshipDetailRequest.Merge(dst, src)
}
func (m *GetLimitedRelationshipDetailRequest) XXX_Size() int {
	return xxx_messageInfo_GetLimitedRelationshipDetailRequest.Size(m)
}
func (m *GetLimitedRelationshipDetailRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLimitedRelationshipDetailRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLimitedRelationshipDetailRequest proto.InternalMessageInfo

func (m *GetLimitedRelationshipDetailRequest) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type GetLimitedRelationshipDetailResponse struct {
	Data                 *LimitedRelationshipsConfig `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetLimitedRelationshipDetailResponse) Reset()         { *m = GetLimitedRelationshipDetailResponse{} }
func (m *GetLimitedRelationshipDetailResponse) String() string { return proto.CompactTextString(m) }
func (*GetLimitedRelationshipDetailResponse) ProtoMessage()    {}
func (*GetLimitedRelationshipDetailResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{65}
}
func (m *GetLimitedRelationshipDetailResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLimitedRelationshipDetailResponse.Unmarshal(m, b)
}
func (m *GetLimitedRelationshipDetailResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLimitedRelationshipDetailResponse.Marshal(b, m, deterministic)
}
func (dst *GetLimitedRelationshipDetailResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLimitedRelationshipDetailResponse.Merge(dst, src)
}
func (m *GetLimitedRelationshipDetailResponse) XXX_Size() int {
	return xxx_messageInfo_GetLimitedRelationshipDetailResponse.Size(m)
}
func (m *GetLimitedRelationshipDetailResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLimitedRelationshipDetailResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLimitedRelationshipDetailResponse proto.InternalMessageInfo

func (m *GetLimitedRelationshipDetailResponse) GetData() *LimitedRelationshipsConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type UpdateLimitedRelationshipRequest struct {
	Data                 *LimitedRelationshipsConfig `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *UpdateLimitedRelationshipRequest) Reset()         { *m = UpdateLimitedRelationshipRequest{} }
func (m *UpdateLimitedRelationshipRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateLimitedRelationshipRequest) ProtoMessage()    {}
func (*UpdateLimitedRelationshipRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{66}
}
func (m *UpdateLimitedRelationshipRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLimitedRelationshipRequest.Unmarshal(m, b)
}
func (m *UpdateLimitedRelationshipRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLimitedRelationshipRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateLimitedRelationshipRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLimitedRelationshipRequest.Merge(dst, src)
}
func (m *UpdateLimitedRelationshipRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateLimitedRelationshipRequest.Size(m)
}
func (m *UpdateLimitedRelationshipRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLimitedRelationshipRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLimitedRelationshipRequest proto.InternalMessageInfo

func (m *UpdateLimitedRelationshipRequest) GetData() *LimitedRelationshipsConfig {
	if m != nil {
		return m.Data
	}
	return nil
}

type UpdateLimitedRelationshipResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateLimitedRelationshipResponse) Reset()         { *m = UpdateLimitedRelationshipResponse{} }
func (m *UpdateLimitedRelationshipResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateLimitedRelationshipResponse) ProtoMessage()    {}
func (*UpdateLimitedRelationshipResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{67}
}
func (m *UpdateLimitedRelationshipResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateLimitedRelationshipResponse.Unmarshal(m, b)
}
func (m *UpdateLimitedRelationshipResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateLimitedRelationshipResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateLimitedRelationshipResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateLimitedRelationshipResponse.Merge(dst, src)
}
func (m *UpdateLimitedRelationshipResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateLimitedRelationshipResponse.Size(m)
}
func (m *UpdateLimitedRelationshipResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateLimitedRelationshipResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateLimitedRelationshipResponse proto.InternalMessageInfo

type CheckRelationNameAvailabilityRequest struct {
	RelationName         string   `protobuf:"bytes,1,opt,name=relation_name,json=relationName,proto3" json:"relation_name,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRelationNameAvailabilityRequest) Reset()         { *m = CheckRelationNameAvailabilityRequest{} }
func (m *CheckRelationNameAvailabilityRequest) String() string { return proto.CompactTextString(m) }
func (*CheckRelationNameAvailabilityRequest) ProtoMessage()    {}
func (*CheckRelationNameAvailabilityRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{68}
}
func (m *CheckRelationNameAvailabilityRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRelationNameAvailabilityRequest.Unmarshal(m, b)
}
func (m *CheckRelationNameAvailabilityRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRelationNameAvailabilityRequest.Marshal(b, m, deterministic)
}
func (dst *CheckRelationNameAvailabilityRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRelationNameAvailabilityRequest.Merge(dst, src)
}
func (m *CheckRelationNameAvailabilityRequest) XXX_Size() int {
	return xxx_messageInfo_CheckRelationNameAvailabilityRequest.Size(m)
}
func (m *CheckRelationNameAvailabilityRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRelationNameAvailabilityRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRelationNameAvailabilityRequest proto.InternalMessageInfo

func (m *CheckRelationNameAvailabilityRequest) GetRelationName() string {
	if m != nil {
		return m.RelationName
	}
	return ""
}

func (m *CheckRelationNameAvailabilityRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckRelationNameAvailabilityResponse struct {
	IsAvailable          bool     `protobuf:"varint,1,opt,name=is_available,json=isAvailable,proto3" json:"is_available,omitempty"`
	Reason               string   `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckRelationNameAvailabilityResponse) Reset()         { *m = CheckRelationNameAvailabilityResponse{} }
func (m *CheckRelationNameAvailabilityResponse) String() string { return proto.CompactTextString(m) }
func (*CheckRelationNameAvailabilityResponse) ProtoMessage()    {}
func (*CheckRelationNameAvailabilityResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{69}
}
func (m *CheckRelationNameAvailabilityResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckRelationNameAvailabilityResponse.Unmarshal(m, b)
}
func (m *CheckRelationNameAvailabilityResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckRelationNameAvailabilityResponse.Marshal(b, m, deterministic)
}
func (dst *CheckRelationNameAvailabilityResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckRelationNameAvailabilityResponse.Merge(dst, src)
}
func (m *CheckRelationNameAvailabilityResponse) XXX_Size() int {
	return xxx_messageInfo_CheckRelationNameAvailabilityResponse.Size(m)
}
func (m *CheckRelationNameAvailabilityResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckRelationNameAvailabilityResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckRelationNameAvailabilityResponse proto.InternalMessageInfo

func (m *CheckRelationNameAvailabilityResponse) GetIsAvailable() bool {
	if m != nil {
		return m.IsAvailable
	}
	return false
}

func (m *CheckRelationNameAvailabilityResponse) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

type BatchGetChannelOfferPhaseRequest struct {
	ChannelId            []uint32 `protobuf:"varint,1,rep,packed,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetChannelOfferPhaseRequest) Reset()         { *m = BatchGetChannelOfferPhaseRequest{} }
func (m *BatchGetChannelOfferPhaseRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelOfferPhaseRequest) ProtoMessage()    {}
func (*BatchGetChannelOfferPhaseRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{70}
}
func (m *BatchGetChannelOfferPhaseRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelOfferPhaseRequest.Unmarshal(m, b)
}
func (m *BatchGetChannelOfferPhaseRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelOfferPhaseRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelOfferPhaseRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelOfferPhaseRequest.Merge(dst, src)
}
func (m *BatchGetChannelOfferPhaseRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelOfferPhaseRequest.Size(m)
}
func (m *BatchGetChannelOfferPhaseRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelOfferPhaseRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelOfferPhaseRequest proto.InternalMessageInfo

func (m *BatchGetChannelOfferPhaseRequest) GetChannelId() []uint32 {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

type OfferRoomPhaseInfo struct {
	Phase                uint32   `protobuf:"varint,1,opt,name=phase,proto3" json:"phase,omitempty"`
	RelationName         string   `protobuf:"bytes,2,opt,name=relation_name,json=relationName,proto3" json:"relation_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfferRoomPhaseInfo) Reset()         { *m = OfferRoomPhaseInfo{} }
func (m *OfferRoomPhaseInfo) String() string { return proto.CompactTextString(m) }
func (*OfferRoomPhaseInfo) ProtoMessage()    {}
func (*OfferRoomPhaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{71}
}
func (m *OfferRoomPhaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfferRoomPhaseInfo.Unmarshal(m, b)
}
func (m *OfferRoomPhaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfferRoomPhaseInfo.Marshal(b, m, deterministic)
}
func (dst *OfferRoomPhaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfferRoomPhaseInfo.Merge(dst, src)
}
func (m *OfferRoomPhaseInfo) XXX_Size() int {
	return xxx_messageInfo_OfferRoomPhaseInfo.Size(m)
}
func (m *OfferRoomPhaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OfferRoomPhaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OfferRoomPhaseInfo proto.InternalMessageInfo

func (m *OfferRoomPhaseInfo) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *OfferRoomPhaseInfo) GetRelationName() string {
	if m != nil {
		return m.RelationName
	}
	return ""
}

type BatchGetChannelOfferPhaseResponse struct {
	PhaseMap             map[uint32]*OfferRoomPhaseInfo `protobuf:"bytes,1,rep,name=phase_map,json=phaseMap,proto3" json:"phase_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatchGetChannelOfferPhaseResponse) Reset()         { *m = BatchGetChannelOfferPhaseResponse{} }
func (m *BatchGetChannelOfferPhaseResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetChannelOfferPhaseResponse) ProtoMessage()    {}
func (*BatchGetChannelOfferPhaseResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_offer_room_1aace2553c976c85, []int{72}
}
func (m *BatchGetChannelOfferPhaseResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetChannelOfferPhaseResponse.Unmarshal(m, b)
}
func (m *BatchGetChannelOfferPhaseResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetChannelOfferPhaseResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetChannelOfferPhaseResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetChannelOfferPhaseResponse.Merge(dst, src)
}
func (m *BatchGetChannelOfferPhaseResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetChannelOfferPhaseResponse.Size(m)
}
func (m *BatchGetChannelOfferPhaseResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetChannelOfferPhaseResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetChannelOfferPhaseResponse proto.InternalMessageInfo

func (m *BatchGetChannelOfferPhaseResponse) GetPhaseMap() map[uint32]*OfferRoomPhaseInfo {
	if m != nil {
		return m.PhaseMap
	}
	return nil
}

func init() {
	proto.RegisterType((*ApplyList)(nil), "offer_room.ApplyList")
	proto.RegisterType((*ApplyList_UserInfo)(nil), "offer_room.ApplyList.UserInfo")
	proto.RegisterType((*GetApplyListRequest)(nil), "offer_room.GetApplyListRequest")
	proto.RegisterType((*GetApplyListResponse)(nil), "offer_room.GetApplyListResponse")
	proto.RegisterType((*AddAppliedUserRequest)(nil), "offer_room.AddAppliedUserRequest")
	proto.RegisterType((*AddAppliedUserResponse)(nil), "offer_room.AddAppliedUserResponse")
	proto.RegisterType((*DelAppliedUserRequest)(nil), "offer_room.DelAppliedUserRequest")
	proto.RegisterType((*DelAppliedUserResponse)(nil), "offer_room.DelAppliedUserResponse")
	proto.RegisterType((*OfferingRelationshipsRequest)(nil), "offer_room.OfferingRelationshipsRequest")
	proto.RegisterType((*OfferingRelationshipInfo)(nil), "offer_room.OfferingRelationshipInfo")
	proto.RegisterType((*OfferingRelationshipsResponse)(nil), "offer_room.OfferingRelationshipsResponse")
	proto.RegisterType((*DeleteRelationshipRequest)(nil), "offer_room.DeleteRelationshipRequest")
	proto.RegisterType((*DeleteRelationshipResponse)(nil), "offer_room.DeleteRelationshipResponse")
	proto.RegisterType((*SetOfferGeneralConfigRequest)(nil), "offer_room.SetOfferGeneralConfigRequest")
	proto.RegisterMapType((map[uint32]uint32)(nil), "offer_room.SetOfferGeneralConfigRequest.CntToDaysEntry")
	proto.RegisterType((*SetOfferGeneralConfigResponse)(nil), "offer_room.SetOfferGeneralConfigResponse")
	proto.RegisterType((*GetOfferGeneralConfigRequest)(nil), "offer_room.GetOfferGeneralConfigRequest")
	proto.RegisterType((*GetOfferGeneralConfigResponse)(nil), "offer_room.GetOfferGeneralConfigResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "offer_room.GetOfferGeneralConfigResponse.CntToDaysEntry")
	proto.RegisterType((*TopList)(nil), "offer_room.TopList")
	proto.RegisterType((*TopList_TopListItem)(nil), "offer_room.TopList.TopListItem")
	proto.RegisterType((*GameSetting)(nil), "offer_room.GameSetting")
	proto.RegisterMapType((map[uint32]uint32)(nil), "offer_room.GameSetting.RelationshipDayMapEntry")
	proto.RegisterType((*SettleRelationship)(nil), "offer_room.SettleRelationship")
	proto.RegisterType((*CurOfferingGameInfo)(nil), "offer_room.CurOfferingGameInfo")
	proto.RegisterType((*InitOfferingGameRequest)(nil), "offer_room.InitOfferingGameRequest")
	proto.RegisterType((*InitOfferingGameResponse)(nil), "offer_room.InitOfferingGameResponse")
	proto.RegisterType((*SubmitOfferingSettingRequest)(nil), "offer_room.SubmitOfferingSettingRequest")
	proto.RegisterType((*SubmitOfferingSettingResponse)(nil), "offer_room.SubmitOfferingSettingResponse")
	proto.RegisterType((*SettleOfferingGameRequest)(nil), "offer_room.SettleOfferingGameRequest")
	proto.RegisterType((*SettleOfferingGameResponse)(nil), "offer_room.SettleOfferingGameResponse")
	proto.RegisterType((*AbortOfferingGameRequest)(nil), "offer_room.AbortOfferingGameRequest")
	proto.RegisterType((*AbortOfferingGameResponse)(nil), "offer_room.AbortOfferingGameResponse")
	proto.RegisterType((*EndOfferingGameRequest)(nil), "offer_room.EndOfferingGameRequest")
	proto.RegisterType((*EndOfferingGameResponse)(nil), "offer_room.EndOfferingGameResponse")
	proto.RegisterType((*GetCurOfferingGameInfoRequest)(nil), "offer_room.GetCurOfferingGameInfoRequest")
	proto.RegisterType((*GetCurOfferingGameInfoResponse)(nil), "offer_room.GetCurOfferingGameInfoResponse")
	proto.RegisterType((*NamePriceRequest)(nil), "offer_room.NamePriceRequest")
	proto.RegisterType((*NamePriceResponse)(nil), "offer_room.NamePriceResponse")
	proto.RegisterType((*OfferRoomCardInfoRequest)(nil), "offer_room.OfferRoomCardInfoRequest")
	proto.RegisterType((*OfferRoomCardInfoResponse)(nil), "offer_room.OfferRoomCardInfoResponse")
	proto.RegisterType((*GetUserOfferNumRequest)(nil), "offer_room.GetUserOfferNumRequest")
	proto.RegisterType((*GetUserOfferNumResponse)(nil), "offer_room.GetUserOfferNumResponse")
	proto.RegisterType((*GiftInfo)(nil), "offer_room.GiftInfo")
	proto.RegisterType((*CreateRelationshipRequest)(nil), "offer_room.CreateRelationshipRequest")
	proto.RegisterType((*CreateRelationshipResponse)(nil), "offer_room.CreateRelationshipResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "offer_room.CreateRelationshipResponse.LevelUpMapEntry")
	proto.RegisterType((*TimeRangeReq)(nil), "offer_room.TimeRangeReq")
	proto.RegisterType((*CountResp)(nil), "offer_room.CountResp")
	proto.RegisterType((*OrderIdsResp)(nil), "offer_room.OrderIdsResp")
	proto.RegisterType((*ReplaceOrderReq)(nil), "offer_room.ReplaceOrderReq")
	proto.RegisterType((*EmptyResp)(nil), "offer_room.EmptyResp")
	proto.RegisterType((*BatchGetUserConsumptionByGameRoundIdRequest)(nil), "offer_room.BatchGetUserConsumptionByGameRoundIdRequest")
	proto.RegisterType((*BatchGetUserConsumptionByGameRoundIdResponse)(nil), "offer_room.BatchGetUserConsumptionByGameRoundIdResponse")
	proto.RegisterType((*BatchGetUserConsumptionByGameRoundIdResponse_UserConsumption)(nil), "offer_room.BatchGetUserConsumptionByGameRoundIdResponse.UserConsumption")
	proto.RegisterType((*DelUserRelationshipNotExistFlagReq)(nil), "offer_room.DelUserRelationshipNotExistFlagReq")
	proto.RegisterType((*RebuildRelationshipCacheReq)(nil), "offer_room.RebuildRelationshipCacheReq")
	proto.RegisterType((*RebuildRelationshipCacheResp)(nil), "offer_room.RebuildRelationshipCacheResp")
	proto.RegisterType((*GetUserNameplateInfoReq)(nil), "offer_room.GetUserNameplateInfoReq")
	proto.RegisterType((*GetUserNameplateInfoResp)(nil), "offer_room.GetUserNameplateInfoResp")
	proto.RegisterType((*UpdateUserNameplateInfoReq)(nil), "offer_room.UpdateUserNameplateInfoReq")
	proto.RegisterType((*UpdateUserNameplateInfoResp)(nil), "offer_room.UpdateUserNameplateInfoResp")
	proto.RegisterType((*GetNameplateConfigReq)(nil), "offer_room.GetNameplateConfigReq")
	proto.RegisterType((*GetNameplateConfigResp)(nil), "offer_room.GetNameplateConfigResp")
	proto.RegisterType((*GetNameplateConfigResp_Nameplate)(nil), "offer_room.GetNameplateConfigResp.Nameplate")
	proto.RegisterType((*LimitedRelationshipsConfig)(nil), "offer_room.LimitedRelationshipsConfig")
	proto.RegisterType((*GetLimitedRelationshipsByPageRequest)(nil), "offer_room.GetLimitedRelationshipsByPageRequest")
	proto.RegisterType((*GetLimitedRelationshipsByPageResponse)(nil), "offer_room.GetLimitedRelationshipsByPageResponse")
	proto.RegisterType((*BatchAddLimitedRelationshipRequest)(nil), "offer_room.BatchAddLimitedRelationshipRequest")
	proto.RegisterType((*BatchAddLimitedRelationshipResponse)(nil), "offer_room.BatchAddLimitedRelationshipResponse")
	proto.RegisterType((*BatchDelLimitedRelationshipRequest)(nil), "offer_room.BatchDelLimitedRelationshipRequest")
	proto.RegisterType((*BatchDelLimitedRelationshipResponse)(nil), "offer_room.BatchDelLimitedRelationshipResponse")
	proto.RegisterType((*GetLimitedRelationshipDetailRequest)(nil), "offer_room.GetLimitedRelationshipDetailRequest")
	proto.RegisterType((*GetLimitedRelationshipDetailResponse)(nil), "offer_room.GetLimitedRelationshipDetailResponse")
	proto.RegisterType((*UpdateLimitedRelationshipRequest)(nil), "offer_room.UpdateLimitedRelationshipRequest")
	proto.RegisterType((*UpdateLimitedRelationshipResponse)(nil), "offer_room.UpdateLimitedRelationshipResponse")
	proto.RegisterType((*CheckRelationNameAvailabilityRequest)(nil), "offer_room.CheckRelationNameAvailabilityRequest")
	proto.RegisterType((*CheckRelationNameAvailabilityResponse)(nil), "offer_room.CheckRelationNameAvailabilityResponse")
	proto.RegisterType((*BatchGetChannelOfferPhaseRequest)(nil), "offer_room.BatchGetChannelOfferPhaseRequest")
	proto.RegisterType((*OfferRoomPhaseInfo)(nil), "offer_room.OfferRoomPhaseInfo")
	proto.RegisterType((*BatchGetChannelOfferPhaseResponse)(nil), "offer_room.BatchGetChannelOfferPhaseResponse")
	proto.RegisterMapType((map[uint32]*OfferRoomPhaseInfo)(nil), "offer_room.BatchGetChannelOfferPhaseResponse.PhaseMapEntry")
	proto.RegisterEnum("offer_room.GamePhase", GamePhase_name, GamePhase_value)
	proto.RegisterEnum("offer_room.NamePriceOnceOperation", NamePriceOnceOperation_name, NamePriceOnceOperation_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// OfferingRoomClient is the client API for OfferingRoom service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type OfferingRoomClient interface {
	// 获取报名队列
	GetApplyList(ctx context.Context, in *GetApplyListRequest, opts ...grpc.CallOption) (*GetApplyListResponse, error)
	// 用户报名
	AddAppliedUser(ctx context.Context, in *AddAppliedUserRequest, opts ...grpc.CallOption) (*AddAppliedUserResponse, error)
	// 用户取消报名
	DelAppliedUser(ctx context.Context, in *DelAppliedUserRequest, opts ...grpc.CallOption) (*DelAppliedUserResponse, error)
	// 获取拍拍关系列表
	OfferingRelationships(ctx context.Context, in *OfferingRelationshipsRequest, opts ...grpc.CallOption) (*OfferingRelationshipsResponse, error)
	// 删除我的拍拍关系
	DeleteRelationship(ctx context.Context, in *DeleteRelationshipRequest, opts ...grpc.CallOption) (*DeleteRelationshipResponse, error)
	// 拍卖通用配置设置
	SetOfferGeneralConfig(ctx context.Context, in *SetOfferGeneralConfigRequest, opts ...grpc.CallOption) (*SetOfferGeneralConfigResponse, error)
	// 拍卖嘉宾获取拍卖配置获取
	GetOfferGeneralConfig(ctx context.Context, in *GetOfferGeneralConfigRequest, opts ...grpc.CallOption) (*GetOfferGeneralConfigResponse, error)
	// 初始化游戏
	InitOfferingGame(ctx context.Context, in *InitOfferingGameRequest, opts ...grpc.CallOption) (*InitOfferingGameResponse, error)
	// 提交拍卖设置，开始拍卖
	SubmitOfferingSetting(ctx context.Context, in *SubmitOfferingSettingRequest, opts ...grpc.CallOption) (*SubmitOfferingSettingResponse, error)
	// 出价
	NamePrice(ctx context.Context, in *NamePriceRequest, opts ...grpc.CallOption) (*NamePriceResponse, error)
	// 定拍
	SettleOfferingGame(ctx context.Context, in *SettleOfferingGameRequest, opts ...grpc.CallOption) (*SettleOfferingGameResponse, error)
	// 流拍
	AbortOfferingGame(ctx context.Context, in *AbortOfferingGameRequest, opts ...grpc.CallOption) (*AbortOfferingGameResponse, error)
	// 结束拍卖
	EndOfferingGame(ctx context.Context, in *EndOfferingGameRequest, opts ...grpc.CallOption) (*EndOfferingGameResponse, error)
	// 获取当前拍卖信息
	GetCurOfferingGameInfo(ctx context.Context, in *GetCurOfferingGameInfoRequest, opts ...grpc.CallOption) (*GetCurOfferingGameInfoResponse, error)
	// 个人资料卡 关系信息
	OfferRoomCardInfo(ctx context.Context, in *OfferRoomCardInfoRequest, opts ...grpc.CallOption) (*OfferRoomCardInfoResponse, error)
	// 获取用户的出价
	GetUserOfferNum(ctx context.Context, in *GetUserOfferNumRequest, opts ...grpc.CallOption) (*GetUserOfferNumResponse, error)
	// 创建关系
	CreateRelationship(ctx context.Context, in *CreateRelationshipRequest, opts ...grpc.CallOption) (*CreateRelationshipResponse, error)
	// 合并出价，独立用于神秘人现身
	MergeNamePrice(ctx context.Context, in *NamePriceRequest, opts ...grpc.CallOption) (*NamePriceResponse, error)
	// 获取用户的消费记录
	BatchGetUserConsumptionByGameRoundId(ctx context.Context, in *BatchGetUserConsumptionByGameRoundIdRequest, opts ...grpc.CallOption) (*BatchGetUserConsumptionByGameRoundIdResponse, error)
	// 查询时间范围内的订单总数和价值总数
	CountOrderNumAndValueByTime(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error)
	// 查询时间范围内的OrderIds
	GetOrderIdsByTime(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error)
	// 补单接口
	ReSendPresent(ctx context.Context, in *ReplaceOrderReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// ==================================内部接口=================================
	// 删除用户的关系记录不存在的标记缓存
	DelUserRelationshipNotExistFlag(ctx context.Context, in *DelUserRelationshipNotExistFlagReq, opts ...grpc.CallOption) (*EmptyResp, error)
	// 重建用户的关系缓存
	RebuildRelationshipCache(ctx context.Context, in *RebuildRelationshipCacheReq, opts ...grpc.CallOption) (*RebuildRelationshipCacheResp, error)
	// ================================= 关系铭牌 ===============================
	// 获取用户拍拍铭牌信息
	GetUserNameplateInfo(ctx context.Context, in *GetUserNameplateInfoReq, opts ...grpc.CallOption) (*GetUserNameplateInfoResp, error)
	// 更新用户拍拍铭牌信息
	UpdateUserNameplateInfo(ctx context.Context, in *UpdateUserNameplateInfoReq, opts ...grpc.CallOption) (*UpdateUserNameplateInfoResp, error)
	// 获取铭牌配置
	GetNameplateConfig(ctx context.Context, in *GetNameplateConfigReq, opts ...grpc.CallOption) (*GetNameplateConfigResp, error)
	// ================================== 限时关系 ================================
	// ================================== 运营后台 ================================
	// 分页获取限时关系列表
	GetLimitedRelationshipsByPage(ctx context.Context, in *GetLimitedRelationshipsByPageRequest, opts ...grpc.CallOption) (*GetLimitedRelationshipsByPageResponse, error)
	// 添加限时关系
	BatchAddLimitedRelationship(ctx context.Context, in *BatchAddLimitedRelationshipRequest, opts ...grpc.CallOption) (*BatchAddLimitedRelationshipResponse, error)
	// 删除限时关系
	BatchDelLimitedRelationship(ctx context.Context, in *BatchDelLimitedRelationshipRequest, opts ...grpc.CallOption) (*BatchDelLimitedRelationshipResponse, error)
	// 获取限时关系详情
	GetLimitedRelationshipDetail(ctx context.Context, in *GetLimitedRelationshipDetailRequest, opts ...grpc.CallOption) (*GetLimitedRelationshipDetailResponse, error)
	// 更新限时关系
	UpdateLimitedRelationship(ctx context.Context, in *UpdateLimitedRelationshipRequest, opts ...grpc.CallOption) (*UpdateLimitedRelationshipResponse, error)
	// 判断指定关系名称是否可用
	// 1. 如果是限时关系名称，且不在限时时间段内，则不可用
	CheckRelationNameAvailability(ctx context.Context, in *CheckRelationNameAvailabilityRequest, opts ...grpc.CallOption) (*CheckRelationNameAvailabilityResponse, error)
	// 批量获取房间相亲当前阶段
	BatchGetChannelOfferPhase(ctx context.Context, in *BatchGetChannelOfferPhaseRequest, opts ...grpc.CallOption) (*BatchGetChannelOfferPhaseResponse, error)
}

type offeringRoomClient struct {
	cc *grpc.ClientConn
}

func NewOfferingRoomClient(cc *grpc.ClientConn) OfferingRoomClient {
	return &offeringRoomClient{cc}
}

func (c *offeringRoomClient) GetApplyList(ctx context.Context, in *GetApplyListRequest, opts ...grpc.CallOption) (*GetApplyListResponse, error) {
	out := new(GetApplyListResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) AddAppliedUser(ctx context.Context, in *AddAppliedUserRequest, opts ...grpc.CallOption) (*AddAppliedUserResponse, error) {
	out := new(AddAppliedUserResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/AddAppliedUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) DelAppliedUser(ctx context.Context, in *DelAppliedUserRequest, opts ...grpc.CallOption) (*DelAppliedUserResponse, error) {
	out := new(DelAppliedUserResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/DelAppliedUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) OfferingRelationships(ctx context.Context, in *OfferingRelationshipsRequest, opts ...grpc.CallOption) (*OfferingRelationshipsResponse, error) {
	out := new(OfferingRelationshipsResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/OfferingRelationships", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) DeleteRelationship(ctx context.Context, in *DeleteRelationshipRequest, opts ...grpc.CallOption) (*DeleteRelationshipResponse, error) {
	out := new(DeleteRelationshipResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/DeleteRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) SetOfferGeneralConfig(ctx context.Context, in *SetOfferGeneralConfigRequest, opts ...grpc.CallOption) (*SetOfferGeneralConfigResponse, error) {
	out := new(SetOfferGeneralConfigResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/SetOfferGeneralConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetOfferGeneralConfig(ctx context.Context, in *GetOfferGeneralConfigRequest, opts ...grpc.CallOption) (*GetOfferGeneralConfigResponse, error) {
	out := new(GetOfferGeneralConfigResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetOfferGeneralConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) InitOfferingGame(ctx context.Context, in *InitOfferingGameRequest, opts ...grpc.CallOption) (*InitOfferingGameResponse, error) {
	out := new(InitOfferingGameResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/InitOfferingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) SubmitOfferingSetting(ctx context.Context, in *SubmitOfferingSettingRequest, opts ...grpc.CallOption) (*SubmitOfferingSettingResponse, error) {
	out := new(SubmitOfferingSettingResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/SubmitOfferingSetting", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) NamePrice(ctx context.Context, in *NamePriceRequest, opts ...grpc.CallOption) (*NamePriceResponse, error) {
	out := new(NamePriceResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/NamePrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) SettleOfferingGame(ctx context.Context, in *SettleOfferingGameRequest, opts ...grpc.CallOption) (*SettleOfferingGameResponse, error) {
	out := new(SettleOfferingGameResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/SettleOfferingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) AbortOfferingGame(ctx context.Context, in *AbortOfferingGameRequest, opts ...grpc.CallOption) (*AbortOfferingGameResponse, error) {
	out := new(AbortOfferingGameResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/AbortOfferingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) EndOfferingGame(ctx context.Context, in *EndOfferingGameRequest, opts ...grpc.CallOption) (*EndOfferingGameResponse, error) {
	out := new(EndOfferingGameResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/EndOfferingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetCurOfferingGameInfo(ctx context.Context, in *GetCurOfferingGameInfoRequest, opts ...grpc.CallOption) (*GetCurOfferingGameInfoResponse, error) {
	out := new(GetCurOfferingGameInfoResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetCurOfferingGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) OfferRoomCardInfo(ctx context.Context, in *OfferRoomCardInfoRequest, opts ...grpc.CallOption) (*OfferRoomCardInfoResponse, error) {
	out := new(OfferRoomCardInfoResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/OfferRoomCardInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetUserOfferNum(ctx context.Context, in *GetUserOfferNumRequest, opts ...grpc.CallOption) (*GetUserOfferNumResponse, error) {
	out := new(GetUserOfferNumResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetUserOfferNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) CreateRelationship(ctx context.Context, in *CreateRelationshipRequest, opts ...grpc.CallOption) (*CreateRelationshipResponse, error) {
	out := new(CreateRelationshipResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/CreateRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) MergeNamePrice(ctx context.Context, in *NamePriceRequest, opts ...grpc.CallOption) (*NamePriceResponse, error) {
	out := new(NamePriceResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/MergeNamePrice", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) BatchGetUserConsumptionByGameRoundId(ctx context.Context, in *BatchGetUserConsumptionByGameRoundIdRequest, opts ...grpc.CallOption) (*BatchGetUserConsumptionByGameRoundIdResponse, error) {
	out := new(BatchGetUserConsumptionByGameRoundIdResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/BatchGetUserConsumptionByGameRoundId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) CountOrderNumAndValueByTime(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*CountResp, error) {
	out := new(CountResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/CountOrderNumAndValueByTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetOrderIdsByTime(ctx context.Context, in *TimeRangeReq, opts ...grpc.CallOption) (*OrderIdsResp, error) {
	out := new(OrderIdsResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetOrderIdsByTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) ReSendPresent(ctx context.Context, in *ReplaceOrderReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/ReSendPresent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) DelUserRelationshipNotExistFlag(ctx context.Context, in *DelUserRelationshipNotExistFlagReq, opts ...grpc.CallOption) (*EmptyResp, error) {
	out := new(EmptyResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/DelUserRelationshipNotExistFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) RebuildRelationshipCache(ctx context.Context, in *RebuildRelationshipCacheReq, opts ...grpc.CallOption) (*RebuildRelationshipCacheResp, error) {
	out := new(RebuildRelationshipCacheResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/RebuildRelationshipCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetUserNameplateInfo(ctx context.Context, in *GetUserNameplateInfoReq, opts ...grpc.CallOption) (*GetUserNameplateInfoResp, error) {
	out := new(GetUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) UpdateUserNameplateInfo(ctx context.Context, in *UpdateUserNameplateInfoReq, opts ...grpc.CallOption) (*UpdateUserNameplateInfoResp, error) {
	out := new(UpdateUserNameplateInfoResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/UpdateUserNameplateInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetNameplateConfig(ctx context.Context, in *GetNameplateConfigReq, opts ...grpc.CallOption) (*GetNameplateConfigResp, error) {
	out := new(GetNameplateConfigResp)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetNameplateConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetLimitedRelationshipsByPage(ctx context.Context, in *GetLimitedRelationshipsByPageRequest, opts ...grpc.CallOption) (*GetLimitedRelationshipsByPageResponse, error) {
	out := new(GetLimitedRelationshipsByPageResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetLimitedRelationshipsByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) BatchAddLimitedRelationship(ctx context.Context, in *BatchAddLimitedRelationshipRequest, opts ...grpc.CallOption) (*BatchAddLimitedRelationshipResponse, error) {
	out := new(BatchAddLimitedRelationshipResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/BatchAddLimitedRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) BatchDelLimitedRelationship(ctx context.Context, in *BatchDelLimitedRelationshipRequest, opts ...grpc.CallOption) (*BatchDelLimitedRelationshipResponse, error) {
	out := new(BatchDelLimitedRelationshipResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/BatchDelLimitedRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) GetLimitedRelationshipDetail(ctx context.Context, in *GetLimitedRelationshipDetailRequest, opts ...grpc.CallOption) (*GetLimitedRelationshipDetailResponse, error) {
	out := new(GetLimitedRelationshipDetailResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/GetLimitedRelationshipDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) UpdateLimitedRelationship(ctx context.Context, in *UpdateLimitedRelationshipRequest, opts ...grpc.CallOption) (*UpdateLimitedRelationshipResponse, error) {
	out := new(UpdateLimitedRelationshipResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/UpdateLimitedRelationship", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) CheckRelationNameAvailability(ctx context.Context, in *CheckRelationNameAvailabilityRequest, opts ...grpc.CallOption) (*CheckRelationNameAvailabilityResponse, error) {
	out := new(CheckRelationNameAvailabilityResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/CheckRelationNameAvailability", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *offeringRoomClient) BatchGetChannelOfferPhase(ctx context.Context, in *BatchGetChannelOfferPhaseRequest, opts ...grpc.CallOption) (*BatchGetChannelOfferPhaseResponse, error) {
	out := new(BatchGetChannelOfferPhaseResponse)
	err := c.cc.Invoke(ctx, "/offer_room.OfferingRoom/BatchGetChannelOfferPhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OfferingRoomServer is the server API for OfferingRoom service.
type OfferingRoomServer interface {
	// 获取报名队列
	GetApplyList(context.Context, *GetApplyListRequest) (*GetApplyListResponse, error)
	// 用户报名
	AddAppliedUser(context.Context, *AddAppliedUserRequest) (*AddAppliedUserResponse, error)
	// 用户取消报名
	DelAppliedUser(context.Context, *DelAppliedUserRequest) (*DelAppliedUserResponse, error)
	// 获取拍拍关系列表
	OfferingRelationships(context.Context, *OfferingRelationshipsRequest) (*OfferingRelationshipsResponse, error)
	// 删除我的拍拍关系
	DeleteRelationship(context.Context, *DeleteRelationshipRequest) (*DeleteRelationshipResponse, error)
	// 拍卖通用配置设置
	SetOfferGeneralConfig(context.Context, *SetOfferGeneralConfigRequest) (*SetOfferGeneralConfigResponse, error)
	// 拍卖嘉宾获取拍卖配置获取
	GetOfferGeneralConfig(context.Context, *GetOfferGeneralConfigRequest) (*GetOfferGeneralConfigResponse, error)
	// 初始化游戏
	InitOfferingGame(context.Context, *InitOfferingGameRequest) (*InitOfferingGameResponse, error)
	// 提交拍卖设置，开始拍卖
	SubmitOfferingSetting(context.Context, *SubmitOfferingSettingRequest) (*SubmitOfferingSettingResponse, error)
	// 出价
	NamePrice(context.Context, *NamePriceRequest) (*NamePriceResponse, error)
	// 定拍
	SettleOfferingGame(context.Context, *SettleOfferingGameRequest) (*SettleOfferingGameResponse, error)
	// 流拍
	AbortOfferingGame(context.Context, *AbortOfferingGameRequest) (*AbortOfferingGameResponse, error)
	// 结束拍卖
	EndOfferingGame(context.Context, *EndOfferingGameRequest) (*EndOfferingGameResponse, error)
	// 获取当前拍卖信息
	GetCurOfferingGameInfo(context.Context, *GetCurOfferingGameInfoRequest) (*GetCurOfferingGameInfoResponse, error)
	// 个人资料卡 关系信息
	OfferRoomCardInfo(context.Context, *OfferRoomCardInfoRequest) (*OfferRoomCardInfoResponse, error)
	// 获取用户的出价
	GetUserOfferNum(context.Context, *GetUserOfferNumRequest) (*GetUserOfferNumResponse, error)
	// 创建关系
	CreateRelationship(context.Context, *CreateRelationshipRequest) (*CreateRelationshipResponse, error)
	// 合并出价，独立用于神秘人现身
	MergeNamePrice(context.Context, *NamePriceRequest) (*NamePriceResponse, error)
	// 获取用户的消费记录
	BatchGetUserConsumptionByGameRoundId(context.Context, *BatchGetUserConsumptionByGameRoundIdRequest) (*BatchGetUserConsumptionByGameRoundIdResponse, error)
	// 查询时间范围内的订单总数和价值总数
	CountOrderNumAndValueByTime(context.Context, *TimeRangeReq) (*CountResp, error)
	// 查询时间范围内的OrderIds
	GetOrderIdsByTime(context.Context, *TimeRangeReq) (*OrderIdsResp, error)
	// 补单接口
	ReSendPresent(context.Context, *ReplaceOrderReq) (*EmptyResp, error)
	// ==================================内部接口=================================
	// 删除用户的关系记录不存在的标记缓存
	DelUserRelationshipNotExistFlag(context.Context, *DelUserRelationshipNotExistFlagReq) (*EmptyResp, error)
	// 重建用户的关系缓存
	RebuildRelationshipCache(context.Context, *RebuildRelationshipCacheReq) (*RebuildRelationshipCacheResp, error)
	// ================================= 关系铭牌 ===============================
	// 获取用户拍拍铭牌信息
	GetUserNameplateInfo(context.Context, *GetUserNameplateInfoReq) (*GetUserNameplateInfoResp, error)
	// 更新用户拍拍铭牌信息
	UpdateUserNameplateInfo(context.Context, *UpdateUserNameplateInfoReq) (*UpdateUserNameplateInfoResp, error)
	// 获取铭牌配置
	GetNameplateConfig(context.Context, *GetNameplateConfigReq) (*GetNameplateConfigResp, error)
	// ================================== 限时关系 ================================
	// ================================== 运营后台 ================================
	// 分页获取限时关系列表
	GetLimitedRelationshipsByPage(context.Context, *GetLimitedRelationshipsByPageRequest) (*GetLimitedRelationshipsByPageResponse, error)
	// 添加限时关系
	BatchAddLimitedRelationship(context.Context, *BatchAddLimitedRelationshipRequest) (*BatchAddLimitedRelationshipResponse, error)
	// 删除限时关系
	BatchDelLimitedRelationship(context.Context, *BatchDelLimitedRelationshipRequest) (*BatchDelLimitedRelationshipResponse, error)
	// 获取限时关系详情
	GetLimitedRelationshipDetail(context.Context, *GetLimitedRelationshipDetailRequest) (*GetLimitedRelationshipDetailResponse, error)
	// 更新限时关系
	UpdateLimitedRelationship(context.Context, *UpdateLimitedRelationshipRequest) (*UpdateLimitedRelationshipResponse, error)
	// 判断指定关系名称是否可用
	// 1. 如果是限时关系名称，且不在限时时间段内，则不可用
	CheckRelationNameAvailability(context.Context, *CheckRelationNameAvailabilityRequest) (*CheckRelationNameAvailabilityResponse, error)
	// 批量获取房间相亲当前阶段
	BatchGetChannelOfferPhase(context.Context, *BatchGetChannelOfferPhaseRequest) (*BatchGetChannelOfferPhaseResponse, error)
}

func RegisterOfferingRoomServer(s *grpc.Server, srv OfferingRoomServer) {
	s.RegisterService(&_OfferingRoom_serviceDesc, srv)
}

func _OfferingRoom_GetApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetApplyList(ctx, req.(*GetApplyListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_AddAppliedUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAppliedUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).AddAppliedUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/AddAppliedUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).AddAppliedUser(ctx, req.(*AddAppliedUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_DelAppliedUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAppliedUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).DelAppliedUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/DelAppliedUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).DelAppliedUser(ctx, req.(*DelAppliedUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_OfferingRelationships_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfferingRelationshipsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).OfferingRelationships(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/OfferingRelationships",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).OfferingRelationships(ctx, req.(*OfferingRelationshipsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_DeleteRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).DeleteRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/DeleteRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).DeleteRelationship(ctx, req.(*DeleteRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_SetOfferGeneralConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetOfferGeneralConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).SetOfferGeneralConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/SetOfferGeneralConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).SetOfferGeneralConfig(ctx, req.(*SetOfferGeneralConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetOfferGeneralConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOfferGeneralConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetOfferGeneralConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetOfferGeneralConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetOfferGeneralConfig(ctx, req.(*GetOfferGeneralConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_InitOfferingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitOfferingGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).InitOfferingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/InitOfferingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).InitOfferingGame(ctx, req.(*InitOfferingGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_SubmitOfferingSetting_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitOfferingSettingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).SubmitOfferingSetting(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/SubmitOfferingSetting",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).SubmitOfferingSetting(ctx, req.(*SubmitOfferingSettingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_NamePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).NamePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/NamePrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).NamePrice(ctx, req.(*NamePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_SettleOfferingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SettleOfferingGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).SettleOfferingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/SettleOfferingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).SettleOfferingGame(ctx, req.(*SettleOfferingGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_AbortOfferingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AbortOfferingGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).AbortOfferingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/AbortOfferingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).AbortOfferingGame(ctx, req.(*AbortOfferingGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_EndOfferingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EndOfferingGameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).EndOfferingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/EndOfferingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).EndOfferingGame(ctx, req.(*EndOfferingGameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetCurOfferingGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCurOfferingGameInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetCurOfferingGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetCurOfferingGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetCurOfferingGameInfo(ctx, req.(*GetCurOfferingGameInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_OfferRoomCardInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfferRoomCardInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).OfferRoomCardInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/OfferRoomCardInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).OfferRoomCardInfo(ctx, req.(*OfferRoomCardInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetUserOfferNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserOfferNumRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetUserOfferNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetUserOfferNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetUserOfferNum(ctx, req.(*GetUserOfferNumRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_CreateRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).CreateRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/CreateRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).CreateRelationship(ctx, req.(*CreateRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_MergeNamePrice_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamePriceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).MergeNamePrice(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/MergeNamePrice",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).MergeNamePrice(ctx, req.(*NamePriceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_BatchGetUserConsumptionByGameRoundId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserConsumptionByGameRoundIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).BatchGetUserConsumptionByGameRoundId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/BatchGetUserConsumptionByGameRoundId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).BatchGetUserConsumptionByGameRoundId(ctx, req.(*BatchGetUserConsumptionByGameRoundIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_CountOrderNumAndValueByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).CountOrderNumAndValueByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/CountOrderNumAndValueByTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).CountOrderNumAndValueByTime(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetOrderIdsByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeRangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetOrderIdsByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetOrderIdsByTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetOrderIdsByTime(ctx, req.(*TimeRangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_ReSendPresent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReplaceOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).ReSendPresent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/ReSendPresent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).ReSendPresent(ctx, req.(*ReplaceOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_DelUserRelationshipNotExistFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelUserRelationshipNotExistFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).DelUserRelationshipNotExistFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/DelUserRelationshipNotExistFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).DelUserRelationshipNotExistFlag(ctx, req.(*DelUserRelationshipNotExistFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_RebuildRelationshipCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RebuildRelationshipCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).RebuildRelationshipCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/RebuildRelationshipCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).RebuildRelationshipCache(ctx, req.(*RebuildRelationshipCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetUserNameplateInfo(ctx, req.(*GetUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_UpdateUserNameplateInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserNameplateInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).UpdateUserNameplateInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/UpdateUserNameplateInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).UpdateUserNameplateInfo(ctx, req.(*UpdateUserNameplateInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetNameplateConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNameplateConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetNameplateConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetNameplateConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetNameplateConfig(ctx, req.(*GetNameplateConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetLimitedRelationshipsByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLimitedRelationshipsByPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetLimitedRelationshipsByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetLimitedRelationshipsByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetLimitedRelationshipsByPage(ctx, req.(*GetLimitedRelationshipsByPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_BatchAddLimitedRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchAddLimitedRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).BatchAddLimitedRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/BatchAddLimitedRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).BatchAddLimitedRelationship(ctx, req.(*BatchAddLimitedRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_BatchDelLimitedRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDelLimitedRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).BatchDelLimitedRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/BatchDelLimitedRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).BatchDelLimitedRelationship(ctx, req.(*BatchDelLimitedRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_GetLimitedRelationshipDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLimitedRelationshipDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).GetLimitedRelationshipDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/GetLimitedRelationshipDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).GetLimitedRelationshipDetail(ctx, req.(*GetLimitedRelationshipDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_UpdateLimitedRelationship_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLimitedRelationshipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).UpdateLimitedRelationship(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/UpdateLimitedRelationship",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).UpdateLimitedRelationship(ctx, req.(*UpdateLimitedRelationshipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_CheckRelationNameAvailability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckRelationNameAvailabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).CheckRelationNameAvailability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/CheckRelationNameAvailability",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).CheckRelationNameAvailability(ctx, req.(*CheckRelationNameAvailabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _OfferingRoom_BatchGetChannelOfferPhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetChannelOfferPhaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OfferingRoomServer).BatchGetChannelOfferPhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/offer_room.OfferingRoom/BatchGetChannelOfferPhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OfferingRoomServer).BatchGetChannelOfferPhase(ctx, req.(*BatchGetChannelOfferPhaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _OfferingRoom_serviceDesc = grpc.ServiceDesc{
	ServiceName: "offer_room.OfferingRoom",
	HandlerType: (*OfferingRoomServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetApplyList",
			Handler:    _OfferingRoom_GetApplyList_Handler,
		},
		{
			MethodName: "AddAppliedUser",
			Handler:    _OfferingRoom_AddAppliedUser_Handler,
		},
		{
			MethodName: "DelAppliedUser",
			Handler:    _OfferingRoom_DelAppliedUser_Handler,
		},
		{
			MethodName: "OfferingRelationships",
			Handler:    _OfferingRoom_OfferingRelationships_Handler,
		},
		{
			MethodName: "DeleteRelationship",
			Handler:    _OfferingRoom_DeleteRelationship_Handler,
		},
		{
			MethodName: "SetOfferGeneralConfig",
			Handler:    _OfferingRoom_SetOfferGeneralConfig_Handler,
		},
		{
			MethodName: "GetOfferGeneralConfig",
			Handler:    _OfferingRoom_GetOfferGeneralConfig_Handler,
		},
		{
			MethodName: "InitOfferingGame",
			Handler:    _OfferingRoom_InitOfferingGame_Handler,
		},
		{
			MethodName: "SubmitOfferingSetting",
			Handler:    _OfferingRoom_SubmitOfferingSetting_Handler,
		},
		{
			MethodName: "NamePrice",
			Handler:    _OfferingRoom_NamePrice_Handler,
		},
		{
			MethodName: "SettleOfferingGame",
			Handler:    _OfferingRoom_SettleOfferingGame_Handler,
		},
		{
			MethodName: "AbortOfferingGame",
			Handler:    _OfferingRoom_AbortOfferingGame_Handler,
		},
		{
			MethodName: "EndOfferingGame",
			Handler:    _OfferingRoom_EndOfferingGame_Handler,
		},
		{
			MethodName: "GetCurOfferingGameInfo",
			Handler:    _OfferingRoom_GetCurOfferingGameInfo_Handler,
		},
		{
			MethodName: "OfferRoomCardInfo",
			Handler:    _OfferingRoom_OfferRoomCardInfo_Handler,
		},
		{
			MethodName: "GetUserOfferNum",
			Handler:    _OfferingRoom_GetUserOfferNum_Handler,
		},
		{
			MethodName: "CreateRelationship",
			Handler:    _OfferingRoom_CreateRelationship_Handler,
		},
		{
			MethodName: "MergeNamePrice",
			Handler:    _OfferingRoom_MergeNamePrice_Handler,
		},
		{
			MethodName: "BatchGetUserConsumptionByGameRoundId",
			Handler:    _OfferingRoom_BatchGetUserConsumptionByGameRoundId_Handler,
		},
		{
			MethodName: "CountOrderNumAndValueByTime",
			Handler:    _OfferingRoom_CountOrderNumAndValueByTime_Handler,
		},
		{
			MethodName: "GetOrderIdsByTime",
			Handler:    _OfferingRoom_GetOrderIdsByTime_Handler,
		},
		{
			MethodName: "ReSendPresent",
			Handler:    _OfferingRoom_ReSendPresent_Handler,
		},
		{
			MethodName: "DelUserRelationshipNotExistFlag",
			Handler:    _OfferingRoom_DelUserRelationshipNotExistFlag_Handler,
		},
		{
			MethodName: "RebuildRelationshipCache",
			Handler:    _OfferingRoom_RebuildRelationshipCache_Handler,
		},
		{
			MethodName: "GetUserNameplateInfo",
			Handler:    _OfferingRoom_GetUserNameplateInfo_Handler,
		},
		{
			MethodName: "UpdateUserNameplateInfo",
			Handler:    _OfferingRoom_UpdateUserNameplateInfo_Handler,
		},
		{
			MethodName: "GetNameplateConfig",
			Handler:    _OfferingRoom_GetNameplateConfig_Handler,
		},
		{
			MethodName: "GetLimitedRelationshipsByPage",
			Handler:    _OfferingRoom_GetLimitedRelationshipsByPage_Handler,
		},
		{
			MethodName: "BatchAddLimitedRelationship",
			Handler:    _OfferingRoom_BatchAddLimitedRelationship_Handler,
		},
		{
			MethodName: "BatchDelLimitedRelationship",
			Handler:    _OfferingRoom_BatchDelLimitedRelationship_Handler,
		},
		{
			MethodName: "GetLimitedRelationshipDetail",
			Handler:    _OfferingRoom_GetLimitedRelationshipDetail_Handler,
		},
		{
			MethodName: "UpdateLimitedRelationship",
			Handler:    _OfferingRoom_UpdateLimitedRelationship_Handler,
		},
		{
			MethodName: "CheckRelationNameAvailability",
			Handler:    _OfferingRoom_CheckRelationNameAvailability_Handler,
		},
		{
			MethodName: "BatchGetChannelOfferPhase",
			Handler:    _OfferingRoom_BatchGetChannelOfferPhase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "offer-room/offer-room.proto",
}

func init() {
	proto.RegisterFile("offer-room/offer-room.proto", fileDescriptor_offer_room_1aace2553c976c85)
}

var fileDescriptor_offer_room_1aace2553c976c85 = []byte{
	// 3747 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x3b, 0xcb, 0x73, 0xdb, 0xc6,
	0xf9, 0x01, 0x29, 0x89, 0xe4, 0x47, 0x3d, 0xe8, 0xb5, 0x65, 0x51, 0x94, 0x14, 0xcb, 0x90, 0x1f,
	0x72, 0x62, 0xd3, 0xf9, 0xc9, 0x76, 0x92, 0x5f, 0xda, 0x64, 0x2a, 0x53, 0x14, 0xc3, 0x19, 0x9b,
	0x92, 0x21, 0x29, 0xf6, 0x24, 0x4d, 0x11, 0x88, 0x58, 0x51, 0xa8, 0x40, 0x00, 0x05, 0x40, 0x57,
	0xca, 0x21, 0xd3, 0x4c, 0x3b, 0xed, 0x4c, 0xdb, 0x5b, 0xa7, 0x93, 0xe9, 0xf4, 0x9f, 0xc8, 0xa5,
	0x33, 0x3d, 0xf4, 0xd6, 0x63, 0xff, 0x81, 0x4e, 0x8f, 0x3d, 0xb4, 0xf7, 0x5e, 0x7b, 0xe9, 0xec,
	0x03, 0x20, 0x16, 0x04, 0x48, 0x2a, 0xb2, 0x2f, 0x36, 0xf7, 0xdb, 0xdd, 0x6f, 0xbf, 0xd7, 0x7e,
	0xaf, 0x85, 0x60, 0xc9, 0x3e, 0x3a, 0xc2, 0xee, 0x3d, 0xd7, 0xb6, 0xbb, 0xf7, 0xfb, 0x3f, 0xab,
	0x8e, 0x6b, 0xfb, 0x36, 0x02, 0x0a, 0x51, 0x09, 0x44, 0xfe, 0x0a, 0x0a, 0x9b, 0x8e, 0x63, 0x9e,
	0x3d, 0x31, 0x3c, 0x1f, 0x6d, 0xc0, 0x84, 0x69, 0x78, 0x7e, 0x59, 0x5a, 0xcd, 0xae, 0x17, 0x37,
	0xde, 0xac, 0xf6, 0xd7, 0x55, 0xc3, 0x45, 0xd5, 0x03, 0x0f, 0xbb, 0x4d, 0xeb, 0xc8, 0x56, 0xe8,
	0x5a, 0x54, 0x86, 0xdc, 0x4b, 0xec, 0x7a, 0x86, 0x6d, 0x95, 0x33, 0xab, 0xd2, 0x7a, 0x56, 0x09,
	0x86, 0x95, 0x35, 0xc8, 0x07, 0x6b, 0xd1, 0x02, 0xe4, 0x7a, 0x1e, 0x76, 0x55, 0x43, 0x2f, 0x4b,
	0xab, 0xd2, 0xfa, 0x8c, 0x32, 0x45, 0x86, 0x4d, 0x5d, 0x7e, 0x08, 0x97, 0x1b, 0xd8, 0x0f, 0xb1,
	0x2b, 0xf8, 0x27, 0x3d, 0xec, 0xf9, 0x68, 0x05, 0xa0, 0x7d, 0xac, 0x59, 0x16, 0x36, 0xfb, 0x5b,
	0x0a, 0x1c, 0xd2, 0xd4, 0xe5, 0x27, 0x70, 0x45, 0xdc, 0xe5, 0x39, 0xb6, 0xe5, 0x61, 0xf4, 0x10,
	0x40, 0x23, 0x40, 0x95, 0xb3, 0x21, 0xad, 0x17, 0x37, 0xe6, 0x13, 0xd9, 0x50, 0x0a, 0x5a, 0xf0,
	0x53, 0xde, 0x81, 0xf9, 0x4d, 0x5d, 0x27, 0x53, 0x06, 0xd6, 0x09, 0xc9, 0xe3, 0x51, 0x11, 0x65,
	0x2a, 0x23, 0x30, 0xd5, 0x82, 0xab, 0x71, 0x84, 0x17, 0x25, 0x70, 0x0b, 0x9b, 0xaf, 0x96, 0xc0,
	0x38, 0xc2, 0x0b, 0x11, 0xf8, 0x1b, 0x09, 0x96, 0x77, 0xc8, 0x1a, 0xc3, 0xea, 0x28, 0xd8, 0xd4,
	0x7c, 0xc3, 0xb6, 0xbc, 0x63, 0xc3, 0xf1, 0x02, 0x42, 0x4b, 0x90, 0xed, 0x85, 0x14, 0x92, 0x9f,
	0x84, 0x74, 0x5f, 0x73, 0x3b, 0xd8, 0x57, 0x7b, 0x21, 0x79, 0x05, 0x06, 0x39, 0x60, 0xd3, 0x8e,
	0xd6, 0xc1, 0xaa, 0x6f, 0x9f, 0x60, 0xab, 0x9c, 0x5d, 0x95, 0xd6, 0x0b, 0x4a, 0x81, 0x40, 0xf6,
	0x09, 0x00, 0x2d, 0x01, 0x1d, 0xa8, 0x9e, 0xf1, 0x25, 0x2e, 0x4f, 0xd0, 0xcd, 0x79, 0x02, 0xd8,
	0x33, 0xbe, 0xc4, 0xf2, 0xb7, 0x19, 0x28, 0x27, 0x51, 0x43, 0x2d, 0x71, 0x09, 0x0a, 0x2e, 0x6e,
	0xdb, 0xae, 0xde, 0x97, 0x58, 0x9e, 0x01, 0x9a, 0x3a, 0xba, 0x06, 0x45, 0x47, 0x73, 0x7d, 0x0b,
	0xbb, 0x11, 0xaa, 0x80, 0x83, 0x08, 0x59, 0x6f, 0xc3, 0x25, 0x37, 0x82, 0x51, 0xb5, 0xb4, 0x2e,
	0xe6, 0xd4, 0x95, 0xa2, 0x13, 0x2d, 0xad, 0x8b, 0xc9, 0x51, 0xf8, 0xd4, 0x31, 0x5c, 0xac, 0xfa,
	0x1e, 0x25, 0x32, 0xab, 0xe4, 0x19, 0x60, 0xdf, 0x23, 0xba, 0xe9, 0x18, 0x47, 0x3e, 0xa1, 0x62,
	0x92, 0xe9, 0x86, 0x0c, 0x9b, 0x3a, 0x5a, 0x84, 0x3c, 0x9d, 0x68, 0x5b, 0x7e, 0x79, 0x8a, 0xce,
	0xd0, 0x85, 0x35, 0xcb, 0x27, 0x53, 0x86, 0xa7, 0x1e, 0xf6, 0xce, 0xb0, 0x5b, 0xce, 0xad, 0x4a,
	0xeb, 0x79, 0x25, 0x67, 0x78, 0x8f, 0xc9, 0x90, 0x9c, 0xc5, 0xd0, 0xb5, 0x6d, 0xab, 0x9c, 0xa7,
	0x04, 0x51, 0x34, 0xcd, 0xb6, 0x6d, 0xa1, 0x55, 0x98, 0x36, 0x3c, 0xd5, 0x71, 0x8d, 0x36, 0x56,
	0xbb, 0xda, 0x69, 0xb9, 0x40, 0xf7, 0x82, 0xe1, 0xed, 0x12, 0xd0, 0x53, 0xed, 0x54, 0xfe, 0x8f,
	0x04, 0x2b, 0x29, 0x0a, 0xe4, 0x86, 0xf1, 0x2c, 0xc6, 0x79, 0xc4, 0x51, 0xdc, 0x88, 0xda, 0x47,
	0x9a, 0xe0, 0x45, 0xf9, 0x50, 0x77, 0x93, 0x28, 0xcc, 0x4c, 0x8a, 0x30, 0x6f, 0xc1, 0x9c, 0x85,
	0x4f, 0x7d, 0x75, 0xc0, 0x2a, 0x66, 0x08, 0x78, 0x37, 0xb4, 0x8c, 0x3b, 0x20, 0xec, 0xa5, 0x62,
	0x64, 0x06, 0x32, 0x17, 0x85, 0xd7, 0x2c, 0x5f, 0xde, 0x83, 0xc5, 0x2d, 0x6c, 0x62, 0x1f, 0x47,
	0x69, 0x4d, 0xb7, 0xd8, 0x1b, 0x30, 0x1b, 0x5a, 0x0e, 0x63, 0x3f, 0xb3, 0x9a, 0x5d, 0x9f, 0x51,
	0xa6, 0x03, 0xf3, 0xa1, 0x57, 0x61, 0x19, 0x2a, 0x49, 0x48, 0x99, 0x14, 0xe5, 0xbf, 0x67, 0x61,
	0x79, 0x0f, 0xfb, 0x54, 0x48, 0x0d, 0x6c, 0x61, 0x57, 0x33, 0x6b, 0xb6, 0x75, 0x64, 0x74, 0x82,
	0x63, 0x57, 0x61, 0x9a, 0x9b, 0x85, 0x6a, 0x7a, 0x06, 0x93, 0xf0, 0x8c, 0x02, 0xcc, 0x36, 0x9e,
	0x78, 0x86, 0x8f, 0x9e, 0x43, 0xb1, 0x6d, 0xf9, 0xaa, 0x6f, 0xab, 0xba, 0x76, 0xe6, 0x51, 0x1a,
	0x8a, 0x1b, 0xef, 0x45, 0x55, 0x30, 0xec, 0x80, 0x6a, 0xcd, 0xf2, 0xf7, 0xed, 0x2d, 0xed, 0xcc,
	0xab, 0x5b, 0xbe, 0x7b, 0xa6, 0x14, 0xda, 0xc1, 0x18, 0x3d, 0x84, 0xab, 0x03, 0xea, 0x60, 0x7c,
	0x66, 0x57, 0xb3, 0xeb, 0x05, 0xe5, 0x4a, 0x5c, 0x27, 0x54, 0x89, 0x32, 0xcc, 0x74, 0xb5, 0x53,
	0xf5, 0xa7, 0xb6, 0xab, 0x7b, 0x11, 0x61, 0x17, 0xbb, 0xda, 0xe9, 0x73, 0x02, 0x23, 0x76, 0x4b,
	0x25, 0xe7, 0xf7, 0x5c, 0x4b, 0x15, 0x4d, 0x7e, 0x9a, 0x41, 0x1b, 0xcc, 0xf0, 0x6f, 0xc1, 0x1c,
	0xc1, 0xc4, 0x18, 0x79, 0xa9, 0x99, 0x3d, 0xcc, 0xed, 0x9f, 0x1c, 0x40, 0x19, 0xfa, 0x84, 0x00,
	0xd1, 0x3a, 0x94, 0xc8, 0x3a, 0x8e, 0x91, 0x2d, 0xcc, 0xd1, 0x85, 0xb3, 0x5d, 0xed, 0x54, 0xa1,
	0x60, 0xb6, 0xf2, 0x1e, 0x5c, 0x16, 0xcf, 0x65, 0xec, 0xe4, 0xa9, 0x4c, 0x4b, 0xd1, 0xc3, 0x09,
	0x2b, 0x95, 0xef, 0xc3, 0xac, 0x28, 0x1d, 0x62, 0x04, 0x27, 0xf8, 0x2c, 0x30, 0x82, 0x13, 0x7c,
	0x86, 0xae, 0xc0, 0x24, 0x3b, 0x91, 0xf9, 0x06, 0x36, 0xf8, 0x20, 0xf3, 0xbe, 0x24, 0x5f, 0x83,
	0x95, 0x14, 0xc1, 0x73, 0xdd, 0x57, 0x61, 0xb9, 0x31, 0x4c, 0xf5, 0xb3, 0x90, 0x09, 0x0d, 0x2e,
	0x63, 0xe8, 0xf2, 0xef, 0x27, 0x60, 0xa5, 0x31, 0x0c, 0x63, 0x7c, 0x07, 0xfa, 0x00, 0x66, 0x19,
	0xa3, 0xd6, 0x91, 0xdd, 0xb7, 0xd0, 0xe2, 0xc6, 0x95, 0xa8, 0x75, 0x50, 0x86, 0xc9, 0x85, 0xa4,
	0x86, 0x46, 0x7e, 0x51, 0x3d, 0xbe, 0x3e, 0xed, 0x7f, 0x04, 0x25, 0x41, 0x0b, 0xd6, 0x91, 0x4d,
	0xf5, 0x9f, 0x46, 0xd7, 0x6c, 0x44, 0x31, 0xc4, 0x63, 0xdf, 0x83, 0xcb, 0x11, 0x7d, 0xc7, 0x7c,
	0x63, 0x29, 0x54, 0x79, 0x83, 0x3b, 0xc9, 0x17, 0xe2, 0xfd, 0xc8, 0x51, 0x09, 0xbc, 0x2f, 0x9c,
	0x34, 0x4c, 0xa8, 0x43, 0x2e, 0x48, 0x03, 0xe6, 0xe3, 0x8c, 0xf4, 0x0d, 0x2a, 0x8d, 0x1b, 0x24,
	0x72, 0xf3, 0x0a, 0x0c, 0xed, 0x1b, 0x09, 0x72, 0xfb, 0x36, 0x73, 0xa1, 0x0f, 0x84, 0x8c, 0xed,
	0x5a, 0x94, 0x02, 0xbe, 0x24, 0xf8, 0xbf, 0xe9, 0xe3, 0x2e, 0x4b, 0xd9, 0x2a, 0xcf, 0xa1, 0x18,
	0x01, 0x26, 0x78, 0xba, 0x55, 0x98, 0xa6, 0x79, 0xc3, 0x91, 0x76, 0x82, 0xfb, 0xc9, 0x03, 0x10,
	0xd8, 0xb6, 0x76, 0x82, 0x9b, 0x3a, 0xa1, 0x8e, 0x86, 0x13, 0xea, 0x83, 0x67, 0x14, 0x36, 0x90,
	0xff, 0x9c, 0x85, 0x62, 0x43, 0xeb, 0xe2, 0x3d, 0xec, 0xfb, 0x86, 0xd5, 0x21, 0x41, 0xa9, 0x4d,
	0x85, 0x1b, 0x89, 0xb5, 0x0c, 0xd0, 0xd4, 0xcf, 0xe7, 0xfd, 0xd7, 0x61, 0x82, 0xc8, 0x9c, 0x1e,
	0x97, 0x26, 0x69, 0xba, 0x02, 0xdd, 0x05, 0x14, 0x7a, 0x11, 0xc3, 0xea, 0xb0, 0xa8, 0xc7, 0xcd,
	0xb2, 0x14, 0x38, 0x12, 0xc3, 0xea, 0xd0, 0xd0, 0x87, 0x1e, 0x41, 0x31, 0xa2, 0xd2, 0xa1, 0x66,
	0x09, 0x7d, 0x45, 0x22, 0x0d, 0x84, 0xeb, 0x40, 0x2c, 0x4d, 0xed, 0x6a, 0x4e, 0x79, 0x8a, 0xaa,
	0xe1, 0xbe, 0xb0, 0xbf, 0x2f, 0x8f, 0x6a, 0x34, 0x24, 0x6c, 0x69, 0x67, 0x4f, 0x35, 0x87, 0xd9,
	0x18, 0x72, 0x07, 0x26, 0x88, 0x37, 0x8c, 0x1a, 0x9b, 0xd5, 0xeb, 0x72, 0x27, 0x37, 0xd3, 0xa7,
	0xa3, 0xd5, 0xeb, 0x56, 0xea, 0xb0, 0x90, 0x82, 0xf6, 0x5c, 0x46, 0xf5, 0x2b, 0x09, 0x10, 0x21,
	0xd3, 0x14, 0xe2, 0x16, 0x49, 0xc3, 0x18, 0x2f, 0x44, 0xf7, 0x41, 0x82, 0x49, 0x21, 0x24, 0x6b,
	0x24, 0x0a, 0xf6, 0x34, 0x13, 0xb3, 0x59, 0x86, 0x33, 0x4f, 0x00, 0x74, 0xb2, 0x02, 0x79, 0xbd,
	0xe7, 0x52, 0x5c, 0xdc, 0x4c, 0xc2, 0x71, 0x98, 0xe4, 0x10, 0xb6, 0x26, 0xfa, 0x49, 0x4e, 0xab,
	0xd7, 0x95, 0xbf, 0xcd, 0xc2, 0xe5, 0x5a, 0xcf, 0x0d, 0xf4, 0x44, 0xe4, 0x47, 0xdd, 0xc0, 0x88,
	0x5c, 0x57, 0x86, 0x99, 0x0e, 0x71, 0x59, 0xae, 0xdd, 0xb3, 0xf4, 0xc0, 0x68, 0xb3, 0x4a, 0x91,
	0x00, 0x15, 0x02, 0x6b, 0xea, 0xd1, 0x5a, 0x25, 0x2b, 0xd4, 0x2a, 0xa8, 0x0a, 0x79, 0xdf, 0xe6,
	0x49, 0xcd, 0x04, 0x35, 0x82, 0xcb, 0x09, 0x77, 0x49, 0xc9, 0xf9, 0x76, 0x90, 0xba, 0x4c, 0x3a,
	0xc7, 0x9a, 0x87, 0xa9, 0xc5, 0xcc, 0x8a, 0x19, 0x32, 0xa1, 0x78, 0x97, 0x4c, 0x2a, 0x6c, 0x0d,
	0x91, 0x92, 0xe1, 0xa9, 0xa6, 0xdd, 0x3e, 0xc1, 0x3a, 0x75, 0x5b, 0x79, 0x25, 0x6f, 0x78, 0x4f,
	0xe8, 0x98, 0xb0, 0xc5, 0x66, 0x68, 0xc6, 0xc9, 0x54, 0x5c, 0x60, 0x90, 0x03, 0xea, 0xd2, 0xa7,
	0x29, 0x5b, 0x1e, 0x33, 0x21, 0x9a, 0xda, 0x15, 0x37, 0x16, 0x52, 0x2c, 0x8c, 0xb1, 0x1b, 0x5c,
	0xbf, 0xc7, 0x30, 0x1d, 0x35, 0x2c, 0x9a, 0xf6, 0xc5, 0xca, 0xba, 0x41, 0x95, 0x2b, 0xc2, 0x1e,
	0xb4, 0x06, 0x33, 0xc7, 0xb6, 0x65, 0xbb, 0x58, 0x57, 0x3b, 0x24, 0x4a, 0x95, 0x81, 0x45, 0x6e,
	0x0e, 0x6c, 0x10, 0x98, 0x7c, 0x08, 0x0b, 0x4d, 0xcb, 0xf0, 0xa3, 0x2a, 0x1b, 0xb3, 0x42, 0x59,
	0x87, 0x92, 0x80, 0xbe, 0xef, 0x6d, 0x66, 0xa3, 0x27, 0x34, 0x75, 0x79, 0x07, 0xca, 0x83, 0x67,
	0xf0, 0x38, 0xf8, 0x00, 0x26, 0x68, 0x54, 0x61, 0xe5, 0x8a, 0xe0, 0x05, 0x13, 0x2c, 0x49, 0xa1,
	0x8b, 0xe5, 0xbf, 0x64, 0x60, 0x79, 0xaf, 0x77, 0xd8, 0xed, 0xe3, 0x0c, 0x84, 0x38, 0x1e, 0xe9,
	0xe3, 0x18, 0x5c, 0x12, 0x7b, 0xd9, 0x24, 0xf6, 0x44, 0x57, 0x39, 0x31, 0x8e, 0xab, 0x9c, 0x4c,
	0x71, 0x95, 0x91, 0xc2, 0x62, 0x4a, 0x28, 0x2c, 0x06, 0xb3, 0xb0, 0x7c, 0x72, 0x16, 0x16, 0xf7,
	0x3b, 0x85, 0x04, 0xbf, 0x23, 0xef, 0xc3, 0x4a, 0x8a, 0xf4, 0x2e, 0xa2, 0x94, 0xdf, 0x49, 0xb0,
	0xc8, 0x6c, 0xf2, 0x3b, 0x18, 0xd3, 0x38, 0x1a, 0x99, 0x87, 0x29, 0xcd, 0x71, 0xfa, 0x7a, 0x98,
	0xd4, 0x1c, 0xa7, 0xa9, 0xa3, 0x65, 0x00, 0x72, 0xff, 0x7b, 0x96, 0x11, 0xc8, 0xbf, 0xa0, 0x10,
	0x8f, 0x70, 0x60, 0x19, 0x4d, 0x5d, 0x7e, 0x06, 0x95, 0x24, 0xa2, 0x2e, 0xc2, 0xe8, 0xe7, 0x50,
	0xde, 0x3c, 0xb4, 0x5d, 0xff, 0xf5, 0xb0, 0x29, 0xef, 0xc2, 0x62, 0x02, 0xfa, 0x8b, 0x10, 0xfc,
	0x19, 0x5c, 0xad, 0x5b, 0xfa, 0x6b, 0x22, 0xb7, 0x05, 0x0b, 0x03, 0xc8, 0x2f, 0x42, 0xec, 0x47,
	0x34, 0x73, 0x4e, 0x9a, 0x1f, 0xaf, 0xbf, 0x74, 0x00, 0x6f, 0xa6, 0xed, 0xbf, 0x08, 0x59, 0x7f,
	0x93, 0xa0, 0x44, 0xee, 0x28, 0xcd, 0x3d, 0x5e, 0xa1, 0x51, 0xf3, 0x0c, 0x2e, 0x9b, 0x9e, 0xc1,
	0x4d, 0x0c, 0x64, 0x70, 0x3f, 0x80, 0x82, 0xed, 0x60, 0x1e, 0x9e, 0x59, 0x14, 0x93, 0xa3, 0x5c,
	0x84, 0x74, 0xee, 0x58, 0x6d, 0xbc, 0x13, 0xac, 0x54, 0xfa, 0x9b, 0xe4, 0xdf, 0x4a, 0x70, 0x29,
	0xc2, 0x0d, 0x17, 0x4c, 0x24, 0xc6, 0x4a, 0x62, 0x8c, 0x2d, 0x41, 0x96, 0x78, 0x13, 0xe6, 0xde,
	0xc9, 0x4f, 0x21, 0xea, 0x66, 0xc7, 0x88, 0xba, 0x24, 0x90, 0xea, 0x41, 0x20, 0x9d, 0xe0, 0x81,
	0x54, 0x67, 0x81, 0x94, 0x90, 0xc3, 0xba, 0x3e, 0x8a, 0x6d, 0x77, 0x6b, 0x9a, 0xab, 0x47, 0xf5,
	0xbd, 0x08, 0xf9, 0x23, 0xd7, 0xee, 0xaa, 0xfd, 0x44, 0x37, 0x47, 0xc6, 0x07, 0xa3, 0x1b, 0x51,
	0xef, 0xc2, 0x82, 0x85, 0xb1, 0xae, 0xfa, 0xb6, 0xf3, 0x40, 0x8d, 0x3a, 0x5b, 0x8f, 0x92, 0x9c,
	0x57, 0xe6, 0xc9, 0xf4, 0xbe, 0xed, 0x3c, 0x10, 0xfa, 0x26, 0xf2, 0x1f, 0x33, 0xb0, 0x98, 0x40,
	0x0e, 0x97, 0xd2, 0x12, 0x14, 0x7c, 0xdb, 0xd7, 0x4c, 0x5a, 0xc9, 0xf0, 0xcc, 0x98, 0x02, 0x48,
	0x05, 0x73, 0xae, 0xcc, 0x18, 0xc1, 0x84, 0xab, 0x59, 0x27, 0x5c, 0xf9, 0xf4, 0x37, 0xe1, 0xd6,
	0xf3, 0x35, 0x37, 0x52, 0x90, 0xe5, 0xc8, 0x98, 0xe0, 0x4e, 0x6c, 0xe3, 0x4c, 0x5e, 0xa8, 0x8d,
	0xb3, 0x01, 0xf3, 0x84, 0x42, 0xc7, 0xd4, 0x7c, 0xac, 0xb6, 0x8f, 0x3a, 0x6a, 0xa0, 0x7f, 0x16,
	0x7e, 0x2e, 0x87, 0x93, 0xb5, 0xa3, 0xce, 0x27, 0x6c, 0x4a, 0xfe, 0xa5, 0x04, 0x57, 0x1b, 0xd8,
	0x27, 0x79, 0x22, 0x3d, 0xa9, 0xd5, 0xeb, 0xbe, 0xd6, 0xfb, 0xb0, 0x00, 0x39, 0xf1, 0x2a, 0x4c,
	0x1d, 0xd1, 0x6b, 0x20, 0xbf, 0x0d, 0x0b, 0x03, 0x74, 0x70, 0x1d, 0x71, 0x7b, 0x95, 0x42, 0x7b,
	0x95, 0x5f, 0x40, 0x3e, 0xac, 0x4a, 0xe3, 0xb5, 0x77, 0x58, 0x11, 0x65, 0x22, 0x15, 0x11, 0xd1,
	0x4e, 0xa4, 0x45, 0x48, 0x7f, 0x13, 0x18, 0xed, 0xd2, 0xb1, 0x28, 0x43, 0x7f, 0xcb, 0xff, 0xcd,
	0xc0, 0x62, 0xcd, 0xc5, 0x5a, 0x72, 0x2f, 0x6a, 0x88, 0xf5, 0xae, 0xc2, 0x34, 0x9d, 0x32, 0x3c,
	0x7a, 0xd7, 0xe9, 0xe9, 0x79, 0x05, 0x08, 0xac, 0xe9, 0x91, 0xab, 0x4e, 0x22, 0x9e, 0x6f, 0xab,
	0x7d, 0x79, 0x4c, 0xfa, 0x36, 0xd9, 0x48, 0x23, 0x5e, 0xb8, 0x8d, 0x5f, 0x26, 0xdf, 0xe6, 0x9b,
	0xce, 0x95, 0x71, 0x3c, 0x20, 0x75, 0x71, 0xb4, 0x1a, 0x0a, 0xb2, 0xfe, 0x29, 0xaa, 0x1a, 0xa1,
	0x54, 0xda, 0x0a, 0x2a, 0x80, 0x48, 0x9a, 0x92, 0x4b, 0xed, 0x7f, 0xe6, 0xc5, 0xfe, 0x27, 0xd9,
	0x43, 0x74, 0x6f, 0xe8, 0x34, 0x27, 0xc9, 0x2a, 0x53, 0x64, 0xc8, 0xb2, 0xa7, 0x7e, 0xf7, 0x13,
	0x12, 0xbb, 0x9f, 0xa4, 0x28, 0x64, 0x0a, 0x2a, 0x06, 0xdd, 0xcf, 0xa7, 0xda, 0x29, 0xf5, 0x5d,
	0xf2, 0x9f, 0x24, 0xa8, 0x24, 0x49, 0x9f, 0x1b, 0xc2, 0x0b, 0x98, 0x36, 0xf1, 0x4b, 0x6c, 0xaa,
	0x3d, 0x87, 0x56, 0x79, 0xac, 0xd8, 0x7e, 0x57, 0xf0, 0xf9, 0xa9, 0xbb, 0xab, 0x4f, 0xc8, 0xd6,
	0x03, 0x27, 0x2c, 0xf6, 0xc0, 0x0c, 0x01, 0x95, 0x0f, 0x61, 0x2e, 0x36, 0x7d, 0xae, 0xa2, 0xed,
	0x0b, 0x98, 0xde, 0x37, 0xba, 0x58, 0xd1, 0xac, 0x0e, 0x09, 0x27, 0xe4, 0xea, 0x1c, 0xe2, 0x8e,
	0x61, 0xa9, 0xbe, 0xd1, 0xc5, 0xdc, 0xfd, 0x16, 0x28, 0x84, 0x2c, 0x23, 0x92, 0xc5, 0x96, 0xce,
	0x26, 0xf9, 0x5b, 0x0d, 0xb6, 0x74, 0x3a, 0x75, 0x15, 0xa6, 0x1c, 0xcd, 0xd5, 0xba, 0x1e, 0xb7,
	0x54, 0x3e, 0x92, 0xdf, 0x83, 0x42, 0xcd, 0xee, 0x59, 0xf4, 0x85, 0x85, 0x10, 0xd2, 0x26, 0x03,
	0x4e, 0x1c, 0x1b, 0x24, 0x93, 0x27, 0xd7, 0x60, 0x7a, 0xc7, 0xd5, 0xb1, 0xdb, 0xd4, 0x69, 0x0b,
	0x99, 0x68, 0xc8, 0x26, 0x63, 0xd5, 0xd0, 0x3d, 0x2a, 0xc0, 0x82, 0x92, 0xb7, 0xf9, 0x02, 0x72,
	0x3a, 0xdd, 0xe5, 0xf1, 0x8e, 0x2a, 0x1f, 0xc9, 0x5b, 0x30, 0xa7, 0x10, 0xd7, 0xd1, 0xc6, 0x14,
	0x17, 0x61, 0x71, 0x11, 0xf2, 0x01, 0x1e, 0x4a, 0x46, 0x41, 0xc9, 0x71, 0x34, 0x11, 0x1e, 0x32,
	0x02, 0x0f, 0x45, 0x28, 0xd4, 0xbb, 0x8e, 0x7f, 0x46, 0xe8, 0x90, 0x9f, 0xc1, 0xdb, 0x8f, 0x35,
	0xbf, 0x7d, 0xcc, 0x2f, 0x7d, 0xcd, 0xb6, 0xbc, 0x5e, 0xd7, 0x21, 0x4a, 0x7b, 0x7c, 0xd6, 0xe8,
	0xbb, 0x90, 0xe0, 0xe6, 0x0d, 0x78, 0x1b, 0x69, 0x30, 0x79, 0xf9, 0xa7, 0x04, 0x77, 0xc7, 0xc3,
	0xc9, 0xed, 0xe9, 0x87, 0x42, 0xd3, 0xe6, 0xe3, 0xa8, 0x1d, 0x9d, 0x07, 0x4f, 0x35, 0xb6, 0x88,
	0x77, 0x77, 0xf6, 0x61, 0x2e, 0x36, 0x91, 0xd0, 0xe1, 0x19, 0x8c, 0xc5, 0x2b, 0x00, 0x86, 0xa7,
	0xb6, 0xe9, 0x2e, 0xcc, 0x43, 0x5b, 0xc1, 0xf0, 0x18, 0x1a, 0x2c, 0xbf, 0x0b, 0xf2, 0x16, 0x36,
	0xd9, 0x53, 0x51, 0xe4, 0xfa, 0xdb, 0x7e, 0xfd, 0xd4, 0xf0, 0xfc, 0x6d, 0x53, 0x23, 0x65, 0x53,
	0xff, 0xa0, 0x2c, 0x3f, 0x48, 0x5e, 0x81, 0x25, 0x05, 0x1f, 0xf6, 0x0c, 0x53, 0x8f, 0xee, 0xab,
	0x69, 0xed, 0x63, 0x62, 0xb1, 0xf2, 0xff, 0xc3, 0x72, 0xfa, 0xb4, 0xe7, 0x10, 0x75, 0xd3, 0x3c,
	0x26, 0x70, 0xc4, 0x93, 0x0a, 0x7d, 0xd1, 0x22, 0x05, 0xc8, 0x76, 0xe8, 0xb9, 0x5b, 0x41, 0x80,
	0xe1, 0x11, 0x3f, 0x81, 0xdf, 0x45, 0xc8, 0x9b, 0x9a, 0xc7, 0x9c, 0x0a, 0x63, 0x3a, 0x47, 0xc6,
	0x35, 0xcb, 0x97, 0xbf, 0x91, 0xa0, 0x9c, 0x8c, 0xc8, 0x73, 0xc2, 0xe8, 0x2a, 0xa5, 0x44, 0xd7,
	0x8c, 0x18, 0x5d, 0x6f, 0xc1, 0x1c, 0x9b, 0x3a, 0x26, 0x57, 0x92, 0xae, 0xc8, 0x52, 0xaa, 0x67,
	0xe8, 0x0a, 0x0a, 0xe5, 0x0d, 0x71, 0x16, 0xfe, 0x63, 0x61, 0x7a, 0x9a, 0x42, 0xf7, 0x18, 0x36,
	0x59, 0x85, 0xca, 0x81, 0xa3, 0x6b, 0x3e, 0x4e, 0x64, 0x92, 0x88, 0x26, 0xe8, 0x68, 0x33, 0x81,
	0xe7, 0x7a, 0x06, 0x6d, 0x64, 0xf7, 0xc9, 0xb0, 0x7c, 0x4e, 0x0a, 0x25, 0x34, 0x20, 0xc3, 0xf2,
	0x19, 0x25, 0x44, 0x39, 0xa9, 0x07, 0x78, 0x8e, 0xbc, 0x00, 0xf3, 0x0d, 0xec, 0x87, 0xf0, 0xb0,
	0x5b, 0x2d, 0xff, 0x3b, 0x43, 0xa3, 0xf7, 0xc0, 0x8c, 0xe7, 0xa0, 0x3d, 0x98, 0xed, 0x27, 0x03,
	0x11, 0x2b, 0xbf, 0x1b, 0x6b, 0xc0, 0x26, 0xec, 0xad, 0x86, 0x30, 0x65, 0x26, 0xc4, 0x31, 0x3c,
	0xc3, 0xc8, 0xa4, 0x66, 0x18, 0x95, 0xbf, 0x4a, 0x50, 0x08, 0x11, 0x86, 0x71, 0x58, 0x8a, 0xc4,
	0x61, 0x62, 0x25, 0xae, 0xc9, 0x9d, 0x05, 0xf9, 0x49, 0x20, 0x5d, 0xfd, 0x11, 0x77, 0x81, 0xe4,
	0x27, 0x71, 0x6e, 0xd4, 0x5d, 0x73, 0xfd, 0xb0, 0x01, 0xba, 0x0d, 0x73, 0x2c, 0x20, 0xf8, 0xc7,
	0x2e, 0xf6, 0x8e, 0x6d, 0x33, 0x78, 0xd0, 0x98, 0xa5, 0xe0, 0xfd, 0x00, 0x4a, 0x42, 0x4f, 0x18,
	0x39, 0xfa, 0x3d, 0xeb, 0x20, 0x02, 0x10, 0x4b, 0x58, 0x01, 0x36, 0x62, 0x11, 0x36, 0xc7, 0xde,
	0x39, 0x29, 0x84, 0x10, 0x2f, 0x7f, 0x9d, 0x81, 0xca, 0x13, 0xa3, 0x6b, 0xf8, 0x58, 0xb8, 0x20,
	0x1e, 0x93, 0xda, 0x40, 0x12, 0x12, 0xb0, 0x99, 0x89, 0xb0, 0x79, 0x9b, 0x19, 0x83, 0x4f, 0xfd,
	0xbe, 0xe7, 0x6b, 0x5d, 0x87, 0x37, 0xbf, 0x66, 0x29, 0x78, 0x3f, 0x80, 0xa2, 0x35, 0x98, 0x09,
	0xc2, 0x03, 0x5b, 0xc6, 0x9e, 0x2c, 0xa7, 0x79, 0x8c, 0x60, 0x8b, 0xb6, 0x61, 0xd5, 0xf0, 0x54,
	0xed, 0xa5, 0x66, 0x98, 0xda, 0xa1, 0x89, 0xd5, 0x23, 0xdb, 0x55, 0x5d, 0xdc, 0x31, 0x3c, 0x1f,
	0xbb, 0x58, 0x67, 0x8d, 0xc0, 0x49, 0xea, 0x3c, 0x96, 0x0d, 0x6f, 0x33, 0x58, 0xb6, 0x6d, 0xbb,
	0x4a, 0xb8, 0x88, 0x36, 0x07, 0xef, 0x40, 0xa9, 0x47, 0x4d, 0x2f, 0x72, 0x1e, 0x4b, 0x17, 0xe6,
	0x18, 0x3c, 0x3c, 0x52, 0xfe, 0xb9, 0x04, 0x37, 0x1a, 0xd8, 0x4f, 0x12, 0xc3, 0xe3, 0xb3, 0x5d,
	0xad, 0x13, 0xad, 0xa4, 0xe8, 0xeb, 0xa0, 0x61, 0xe9, 0xf8, 0x34, 0xc8, 0x1c, 0x09, 0xa4, 0x49,
	0x00, 0xe2, 0x9b, 0x71, 0x46, 0x7c, 0x33, 0x46, 0xd7, 0xa0, 0xe8, 0x61, 0xcd, 0x6d, 0x1f, 0x47,
	0x9f, 0x74, 0x81, 0x81, 0xa8, 0x26, 0x7e, 0x26, 0xc1, 0xcd, 0x11, 0x54, 0x70, 0xf7, 0xfe, 0x01,
	0x4c, 0xe8, 0x9a, 0xaf, 0x71, 0xc3, 0xbf, 0x15, 0x35, 0xfc, 0x74, 0x55, 0x2a, 0x74, 0x8f, 0x58,
	0x17, 0x64, 0xc4, 0xba, 0x40, 0xfe, 0x02, 0x64, 0x1a, 0x1f, 0x36, 0x75, 0x3d, 0x01, 0x51, 0x20,
	0x85, 0x0b, 0x1c, 0x2f, 0xdf, 0x84, 0xb5, 0xa1, 0x27, 0xf0, 0x97, 0xac, 0x0f, 0x39, 0x21, 0x5b,
	0xd8, 0x1c, 0x42, 0xc8, 0x02, 0xe4, 0x44, 0xff, 0x34, 0xc5, 0xdc, 0x53, 0x78, 0x4a, 0xda, 0x76,
	0x7e, 0xca, 0x23, 0x58, 0x4b, 0x16, 0xf8, 0x16, 0xf6, 0x35, 0xc3, 0x4c, 0x7b, 0x36, 0x3b, 0x4c,
	0xb3, 0x96, 0x60, 0xdb, 0x80, 0x9a, 0xa4, 0x73, 0xcb, 0xe9, 0x47, 0xb0, 0xca, 0x1c, 0xe7, 0x58,
	0x7a, 0x38, 0x3f, 0xfe, 0x35, 0xb8, 0x3e, 0x04, 0x3f, 0x97, 0xcf, 0xe7, 0x70, 0xa3, 0x76, 0x8c,
	0xdb, 0x27, 0xc1, 0x24, 0x31, 0x53, 0x7e, 0xe3, 0x0c, 0xd3, 0x20, 0xf9, 0x0e, 0x23, 0x64, 0x0d,
	0x66, 0x82, 0x0c, 0x5c, 0x8d, 0x38, 0xc1, 0xb0, 0xcf, 0xdb, 0x0a, 0x9c, 0x61, 0x58, 0xfe, 0xd2,
	0xc8, 0x7d, 0x08, 0x37, 0x47, 0xa0, 0xe7, 0x82, 0xbc, 0x4e, 0xf3, 0xeb, 0xd0, 0x25, 0x50, 0xf4,
	0x79, 0xa5, 0x18, 0xb9, 0xfe, 0x24, 0x35, 0x73, 0xb1, 0xe6, 0x71, 0x8f, 0x5d, 0x50, 0xf8, 0x48,
	0xde, 0x84, 0xd5, 0x20, 0xe3, 0xa9, 0xb1, 0x0a, 0x8f, 0x56, 0x61, 0xac, 0x7b, 0x9e, 0x52, 0x0f,
	0x66, 0xc5, 0x56, 0xcd, 0x0e, 0xa0, 0xb0, 0xcc, 0xa6, 0xfb, 0x68, 0x75, 0x76, 0x25, 0xe8, 0xcf,
	0xf3, 0x54, 0x95, 0x35, 0xe2, 0x07, 0x24, 0x91, 0x19, 0x94, 0x84, 0xfc, 0x0f, 0x09, 0xae, 0x0f,
	0x21, 0x2a, 0xac, 0x09, 0x0a, 0x14, 0x67, 0xa4, 0x20, 0xf8, 0x5e, 0x52, 0x22, 0x97, 0x8a, 0xa1,
	0x4a, 0x47, 0x61, 0x55, 0x90, 0x77, 0xf8, 0xb0, 0xf2, 0x19, 0xcc, 0x08, 0x53, 0x09, 0x15, 0xc1,
	0xc3, 0x68, 0xca, 0x1d, 0xeb, 0xe8, 0x0f, 0x0a, 0x23, 0x52, 0x31, 0xbc, 0xf5, 0x12, 0x0a, 0xe1,
	0xf3, 0x04, 0xaa, 0xc0, 0xd5, 0xc6, 0xe6, 0xd3, 0xba, 0xba, 0xfb, 0xf1, 0xe6, 0x5e, 0x5d, 0x3d,
	0x68, 0xed, 0xed, 0xd6, 0x6b, 0xcd, 0xed, 0x66, 0x7d, 0xab, 0xf4, 0x06, 0x5a, 0x81, 0xc5, 0xc8,
	0x5c, 0xb3, 0xa5, 0xee, 0x2a, 0xf5, 0xdd, 0x4d, 0x65, 0x73, 0xbf, 0xb9, 0xd3, 0x2a, 0x49, 0x68,
	0x01, 0x2e, 0x47, 0xa6, 0x77, 0xb6, 0xb7, 0xeb, 0x4a, 0xb3, 0xd5, 0x28, 0x65, 0xd0, 0x3c, 0x5c,
	0x8a, 0x4c, 0xec, 0xd5, 0xf7, 0xf7, 0x9f, 0xd4, 0x4b, 0xd9, 0xb7, 0x7e, 0x2d, 0xc1, 0xd5, 0xe4,
	0x8e, 0x12, 0xba, 0x03, 0x37, 0x5b, 0x74, 0x87, 0xd2, 0xac, 0xd5, 0xd5, 0x9d, 0x16, 0xf9, 0x67,
	0xb7, 0xce, 0x4e, 0x8a, 0x11, 0x25, 0xc3, 0x9b, 0xe9, 0x4b, 0xc9, 0xb0, 0x24, 0xa1, 0xeb, 0xb0,
	0x92, 0xbe, 0xe6, 0xe9, 0xe6, 0x8b, 0x52, 0x66, 0xe3, 0x5f, 0x4b, 0x30, 0x1d, 0xf6, 0x37, 0x6c,
	0xbb, 0x8b, 0x9e, 0xc1, 0x74, 0xf4, 0x73, 0x32, 0x74, 0x2d, 0x96, 0xac, 0xc4, 0x3f, 0x4f, 0xab,
	0xac, 0xa6, 0x2f, 0xe0, 0xf6, 0xf1, 0x1c, 0x66, 0xc5, 0x4f, 0xc0, 0xd0, 0x75, 0xe1, 0x2b, 0xaa,
	0xa4, 0xef, 0xcd, 0x2a, 0xf2, 0xb0, 0x25, 0x7d, 0xc4, 0xe2, 0xa7, 0x5b, 0x22, 0xe2, 0xc4, 0xef,
	0xc4, 0x44, 0xc4, 0x29, 0x5f, 0x7e, 0x99, 0x30, 0x9f, 0xf8, 0x05, 0x10, 0x5a, 0x1f, 0xd5, 0x17,
	0x0a, 0xbe, 0xf2, 0xaa, 0xdc, 0x19, 0x63, 0x25, 0x3f, 0xad, 0x0d, 0x68, 0xf0, 0x33, 0x19, 0x74,
	0x33, 0x46, 0x67, 0xf2, 0xb7, 0x39, 0x95, 0x5b, 0xa3, 0x96, 0xf5, 0x59, 0x4a, 0xfc, 0x24, 0x43,
	0x64, 0x69, 0xd8, 0xe7, 0x32, 0x22, 0x4b, 0x43, 0xbf, 0xef, 0x20, 0xa7, 0x35, 0x46, 0x9f, 0xd6,
	0x18, 0xfb, 0xb4, 0xe1, 0xdf, 0x7e, 0x7c, 0x0e, 0xa5, 0xf8, 0x7b, 0x18, 0x5a, 0x8b, 0x6e, 0x4f,
	0x79, 0x91, 0xab, 0xdc, 0x18, 0xbe, 0x28, 0x22, 0xba, 0xa4, 0xe7, 0x9d, 0x98, 0xe8, 0x86, 0xbc,
	0x9f, 0xc5, 0x44, 0x37, 0xf4, 0xad, 0xe8, 0x63, 0x96, 0xab, 0xb3, 0x37, 0xf9, 0xe5, 0xc4, 0x36,
	0x74, 0x80, 0x75, 0x25, 0x65, 0xb6, 0x6f, 0x57, 0x83, 0x4f, 0x35, 0xa2, 0x5d, 0xa5, 0xbe, 0x2f,
	0x89, 0x76, 0x35, 0xe4, 0xc5, 0xe7, 0x0b, 0xb8, 0x34, 0xf0, 0xba, 0x82, 0x04, 0xb9, 0xa6, 0xbd,
	0xed, 0x54, 0x6e, 0x8e, 0x58, 0xc5, 0x4f, 0xf8, 0x14, 0xe6, 0x62, 0x0f, 0x22, 0x48, 0xb8, 0xc3,
	0xc9, 0x4f, 0x31, 0x95, 0xb5, 0xa1, 0x6b, 0x38, 0x6e, 0x9b, 0x16, 0x6f, 0x49, 0x4f, 0xec, 0x71,
	0xf3, 0x4b, 0x7f, 0x40, 0xa9, 0xbc, 0x35, 0xce, 0xd2, 0xbe, 0xb8, 0x06, 0x3a, 0xe1, 0xe8, 0x46,
	0x62, 0xd0, 0x8a, 0xf5, 0xed, 0x45, 0x71, 0xa5, 0xb7, 0xd3, 0x3f, 0x85, 0xb9, 0x58, 0x17, 0x57,
	0x14, 0x57, 0x72, 0xab, 0x59, 0x14, 0x57, 0x5a, 0x1b, 0xb8, 0x0d, 0x68, 0xb0, 0xbb, 0x27, 0x5a,
	0x54, 0x6a, 0xe7, 0x56, 0xb4, 0xa8, 0x21, 0x2d, 0xc6, 0xa7, 0x30, 0xfb, 0x14, 0xbb, 0x1d, 0xfc,
	0x8a, 0x6e, 0xc1, 0x1f, 0x24, 0xb8, 0x31, 0x4e, 0x2b, 0x09, 0xbd, 0x77, 0xfe, 0xe6, 0x13, 0x23,
	0xe0, 0xfd, 0xef, 0xda, 0xb5, 0x42, 0x2d, 0x58, 0xa2, 0x2d, 0x45, 0xda, 0xd2, 0x6b, 0xf5, 0xba,
	0x9b, 0x96, 0x4e, 0xbf, 0xd6, 0x7b, 0x7c, 0x46, 0x3b, 0x91, 0x65, 0xe1, 0x05, 0x28, 0xd2, 0xdd,
	0xac, 0x08, 0x1f, 0x59, 0xf4, 0xbb, 0x92, 0x0d, 0xb8, 0x44, 0x3c, 0x25, 0xef, 0x25, 0x8e, 0xc4,
	0x22, 0xcc, 0x08, 0x2d, 0xca, 0x1a, 0xcc, 0x28, 0x78, 0x0f, 0x5b, 0xfa, 0xae, 0x8b, 0x3d, 0x6c,
	0xf9, 0x68, 0x29, 0xba, 0x34, 0xd6, 0x88, 0x14, 0xa9, 0x09, 0xfb, 0x8b, 0xe8, 0x18, 0xae, 0x8d,
	0xe8, 0x93, 0xa1, 0x6a, 0x2c, 0x7a, 0x8d, 0x68, 0xaa, 0xa5, 0x9d, 0xd4, 0x85, 0x72, 0x5a, 0xeb,
	0x0c, 0xdd, 0x16, 0x29, 0x4f, 0xed, 0xbf, 0x55, 0xd6, 0xc7, 0x5b, 0xe8, 0x39, 0x48, 0xa3, 0x9f,
	0xdc, 0x0f, 0x34, 0x8a, 0x50, 0xd2, 0x1d, 0x8a, 0xf7, 0xaa, 0xc4, 0x98, 0x93, 0xda, 0x6c, 0xfb,
	0x31, 0x2c, 0xa4, 0xb4, 0xa3, 0x90, 0x70, 0x8f, 0xd2, 0x9b, 0x62, 0x95, 0xdb, 0x63, 0xad, 0xf3,
	0x1c, 0xf4, 0x19, 0xa0, 0xc1, 0x2e, 0x94, 0x98, 0x4a, 0x25, 0xf6, 0xbe, 0x2a, 0xf2, 0xe8, 0x46,
	0x16, 0xfa, 0x85, 0x44, 0xdf, 0x9f, 0xd3, 0x7b, 0x05, 0xe8, 0x9d, 0x18, 0x96, 0x91, 0xcd, 0x8d,
	0xca, 0xff, 0x9d, 0x63, 0x07, 0xbf, 0x69, 0x5f, 0xc1, 0xd2, 0x90, 0x6a, 0x5e, 0xb4, 0xc3, 0xd1,
	0x8d, 0x85, 0xca, 0xfd, 0xb1, 0xd7, 0xc7, 0xce, 0x4f, 0xae, 0xf3, 0x13, 0xce, 0x1f, 0xda, 0x4f,
	0x48, 0x38, 0x7f, 0x78, 0x03, 0x01, 0x7d, 0x2d, 0xd1, 0x2f, 0x6e, 0x53, 0x5b, 0x01, 0xe8, 0xfe,
	0x68, 0x99, 0x0a, 0xbd, 0x86, 0xca, 0x3b, 0xe3, 0x6f, 0xe0, 0x34, 0x9c, 0xc2, 0x62, 0x6a, 0x25,
	0x8f, 0xee, 0x0e, 0x5a, 0xeb, 0x10, 0xfe, 0xef, 0x8d, 0xb9, 0x9a, 0x9f, 0x4c, 0x8c, 0x70, 0x68,
	0x01, 0x2f, 0x1a, 0xe1, 0x38, 0xad, 0x04, 0xd1, 0x08, 0xc7, 0xeb, 0x0e, 0x7c, 0x09, 0x8b, 0xa9,
	0xb5, 0xb0, 0x28, 0x80, 0x51, 0x9d, 0x00, 0x51, 0x00, 0x23, 0x0b, 0x6c, 0xf9, 0x8d, 0xc7, 0xd5,
	0x4f, 0xef, 0x76, 0x6c, 0x53, 0xb3, 0x3a, 0xd5, 0x47, 0x1b, 0xbe, 0x5f, 0x6d, 0xdb, 0xdd, 0xfb,
	0xf4, 0x2f, 0xa0, 0xda, 0xb6, 0x79, 0xdf, 0xc3, 0xee, 0x4b, 0xa3, 0x8d, 0xbd, 0xc8, 0x9f, 0x47,
	0x1d, 0x4e, 0xd1, 0xd9, 0x07, 0xff, 0x0b, 0x00, 0x00, 0xff, 0xff, 0x66, 0xbe, 0x4d, 0xc7, 0x3e,
	0x35, 0x00, 0x00,
}
