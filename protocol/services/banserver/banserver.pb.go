// Code generated by protoc-gen-go. DO NOT EDIT.
// source: banserver/banserver.proto

package banserver // import "golang.52tt.com/protocol/services/banserver"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type BanOpType int32

const (
	BanOpType_BAN_OP_UNSPECIFIED BanOpType = 0
	// 封 操作
	BanOpType_BAN_OP_BAN_USER      BanOpType = 1
	BanOpType_BAN_OP_BAN_DEVICE    BanOpType = 2
	BanOpType_BAN_OP_BAN_CLIENT_IP BanOpType = 4
	BanOpType_BAN_OP_BAN_PHONE     BanOpType = 8
	// 解封 操作
	BanOpType_BAN_OP_UNBAN_USER      BanOpType = 256
	BanOpType_BAN_OP_UNBAN_DEVICE    BanOpType = 512
	BanOpType_BAN_OP_UNBAN_CLIENT_IP BanOpType = 1024
	BanOpType_BAN_OP_UNBAN_PHONE     BanOpType = 2048
)

var BanOpType_name = map[int32]string{
	0:    "BAN_OP_UNSPECIFIED",
	1:    "BAN_OP_BAN_USER",
	2:    "BAN_OP_BAN_DEVICE",
	4:    "BAN_OP_BAN_CLIENT_IP",
	8:    "BAN_OP_BAN_PHONE",
	256:  "BAN_OP_UNBAN_USER",
	512:  "BAN_OP_UNBAN_DEVICE",
	1024: "BAN_OP_UNBAN_CLIENT_IP",
	2048: "BAN_OP_UNBAN_PHONE",
}
var BanOpType_value = map[string]int32{
	"BAN_OP_UNSPECIFIED":     0,
	"BAN_OP_BAN_USER":        1,
	"BAN_OP_BAN_DEVICE":      2,
	"BAN_OP_BAN_CLIENT_IP":   4,
	"BAN_OP_BAN_PHONE":       8,
	"BAN_OP_UNBAN_USER":      256,
	"BAN_OP_UNBAN_DEVICE":    512,
	"BAN_OP_UNBAN_CLIENT_IP": 1024,
	"BAN_OP_UNBAN_PHONE":     2048,
}

func (x BanOpType) String() string {
	return proto.EnumName(BanOpType_name, int32(x))
}
func (BanOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{0}
}

// 黑名单复核用户标签
type BannedCheckUserTag int32

const (
	BannedCheckUserTag_BANNED_CHECK_USER_TAG_UNSPECIFIED BannedCheckUserTag = 0
	BannedCheckUserTag_BANNED_CHECK_USER_TAG_HIGH_PAY    BannedCheckUserTag = 1
	BannedCheckUserTag_BANNED_CHECK_USER_TAG_PROFESSION  BannedCheckUserTag = 2
)

var BannedCheckUserTag_name = map[int32]string{
	0: "BANNED_CHECK_USER_TAG_UNSPECIFIED",
	1: "BANNED_CHECK_USER_TAG_HIGH_PAY",
	2: "BANNED_CHECK_USER_TAG_PROFESSION",
}
var BannedCheckUserTag_value = map[string]int32{
	"BANNED_CHECK_USER_TAG_UNSPECIFIED": 0,
	"BANNED_CHECK_USER_TAG_HIGH_PAY":    1,
	"BANNED_CHECK_USER_TAG_PROFESSION":  2,
}

func (x BannedCheckUserTag) String() string {
	return proto.EnumName(BannedCheckUserTag_name, int32(x))
}
func (BannedCheckUserTag) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{1}
}

// 黑名单复核封禁来源
type BannedCheckSource int32

const (
	BannedCheckSource_BANNED_CHECK_SOURCE_UNSPECIFIED BannedCheckSource = 0
	BannedCheckSource_BANNED_CHECK_SOURCE_AUDIT       BannedCheckSource = 1
	BannedCheckSource_BANNED_CHECK_SOURCE_STRATEGY    BannedCheckSource = 2
)

var BannedCheckSource_name = map[int32]string{
	0: "BANNED_CHECK_SOURCE_UNSPECIFIED",
	1: "BANNED_CHECK_SOURCE_AUDIT",
	2: "BANNED_CHECK_SOURCE_STRATEGY",
}
var BannedCheckSource_value = map[string]int32{
	"BANNED_CHECK_SOURCE_UNSPECIFIED": 0,
	"BANNED_CHECK_SOURCE_AUDIT":       1,
	"BANNED_CHECK_SOURCE_STRATEGY":    2,
}

func (x BannedCheckSource) String() string {
	return proto.EnumName(BannedCheckSource_name, int32(x))
}
func (BannedCheckSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{2}
}

type BannedCheckRecordType int32

const (
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_UNSPECIFIED BannedCheckRecordType = 0
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_TO_CHECK    BannedCheckRecordType = 1
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_CHECKED     BannedCheckRecordType = 2
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_CHECKING    BannedCheckRecordType = 3
	BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_DELETED     BannedCheckRecordType = 4
)

var BannedCheckRecordType_name = map[int32]string{
	0: "BANNED_CHECK_RECORD_TYPE_UNSPECIFIED",
	1: "BANNED_CHECK_RECORD_TYPE_TO_CHECK",
	2: "BANNED_CHECK_RECORD_TYPE_CHECKED",
	3: "BANNED_CHECK_RECORD_TYPE_CHECKING",
	4: "BANNED_CHECK_RECORD_TYPE_DELETED",
}
var BannedCheckRecordType_value = map[string]int32{
	"BANNED_CHECK_RECORD_TYPE_UNSPECIFIED": 0,
	"BANNED_CHECK_RECORD_TYPE_TO_CHECK":    1,
	"BANNED_CHECK_RECORD_TYPE_CHECKED":     2,
	"BANNED_CHECK_RECORD_TYPE_CHECKING":    3,
	"BANNED_CHECK_RECORD_TYPE_DELETED":     4,
}

func (x BannedCheckRecordType) String() string {
	return proto.EnumName(BannedCheckRecordType_name, int32(x))
}
func (BannedCheckRecordType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{3}
}

type BannedCheckType int32

const (
	BannedCheckType_BANNED_CHECK_TYPE_UNSPECIFIED       BannedCheckType = 0
	BannedCheckType_BANNED_CHECK_TYPE_KEEP_BANNED       BannedCheckType = 1
	BannedCheckType_BANNED_CHECK_TYPE_UNBANNED_BY_CHECK BannedCheckType = 2
	BannedCheckType_BANNED_CHECK_TYPE_UNBANNED_BY_OTHER BannedCheckType = 3
	BannedCheckType_BANNED_CHECK_TYPE_REJECT_UNBAN      BannedCheckType = 4
)

var BannedCheckType_name = map[int32]string{
	0: "BANNED_CHECK_TYPE_UNSPECIFIED",
	1: "BANNED_CHECK_TYPE_KEEP_BANNED",
	2: "BANNED_CHECK_TYPE_UNBANNED_BY_CHECK",
	3: "BANNED_CHECK_TYPE_UNBANNED_BY_OTHER",
	4: "BANNED_CHECK_TYPE_REJECT_UNBAN",
}
var BannedCheckType_value = map[string]int32{
	"BANNED_CHECK_TYPE_UNSPECIFIED":       0,
	"BANNED_CHECK_TYPE_KEEP_BANNED":       1,
	"BANNED_CHECK_TYPE_UNBANNED_BY_CHECK": 2,
	"BANNED_CHECK_TYPE_UNBANNED_BY_OTHER": 3,
	"BANNED_CHECK_TYPE_REJECT_UNBAN":      4,
}

func (x BannedCheckType) String() string {
	return proto.EnumName(BannedCheckType_name, int32(x))
}
func (BannedCheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{4}
}

type BannedCheckResultType int32

const (
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_UNSPECIFIED          BannedCheckResultType = 0
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_UNBANNED             BannedCheckResultType = 1
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_OBJECTION            BannedCheckResultType = 2
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_KEEP_BANNED          BannedCheckResultType = 3
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_UNBANNED_BY_OTHER    BannedCheckResultType = 4
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_OPERATOR_UNBANNED    BannedCheckResultType = 5
	BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_OPERATOR_KEEP_BANNED BannedCheckResultType = 6
)

var BannedCheckResultType_name = map[int32]string{
	0: "BANNED_CHECK_RESULT_TYPE_UNSPECIFIED",
	1: "BANNED_CHECK_RESULT_TYPE_UNBANNED",
	2: "BANNED_CHECK_RESULT_TYPE_OBJECTION",
	3: "BANNED_CHECK_RESULT_TYPE_KEEP_BANNED",
	4: "BANNED_CHECK_RESULT_TYPE_UNBANNED_BY_OTHER",
	5: "BANNED_CHECK_RESULT_TYPE_OPERATOR_UNBANNED",
	6: "BANNED_CHECK_RESULT_TYPE_OPERATOR_KEEP_BANNED",
}
var BannedCheckResultType_value = map[string]int32{
	"BANNED_CHECK_RESULT_TYPE_UNSPECIFIED":          0,
	"BANNED_CHECK_RESULT_TYPE_UNBANNED":             1,
	"BANNED_CHECK_RESULT_TYPE_OBJECTION":            2,
	"BANNED_CHECK_RESULT_TYPE_KEEP_BANNED":          3,
	"BANNED_CHECK_RESULT_TYPE_UNBANNED_BY_OTHER":    4,
	"BANNED_CHECK_RESULT_TYPE_OPERATOR_UNBANNED":    5,
	"BANNED_CHECK_RESULT_TYPE_OPERATOR_KEEP_BANNED": 6,
}

func (x BannedCheckResultType) String() string {
	return proto.EnumName(BannedCheckResultType_name, int32(x))
}
func (BannedCheckResultType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{5}
}

type BannedOperator struct {
	OperatorName         string   `protobuf:"bytes,1,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	LastOpAt             int64    `protobuf:"varint,2,opt,name=last_op_at,json=lastOpAt,proto3" json:"last_op_at,omitempty"`
	LastOpType           uint32   `protobuf:"varint,3,opt,name=last_op_type,json=lastOpType,proto3" json:"last_op_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannedOperator) Reset()         { *m = BannedOperator{} }
func (m *BannedOperator) String() string { return proto.CompactTextString(m) }
func (*BannedOperator) ProtoMessage()    {}
func (*BannedOperator) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{0}
}
func (m *BannedOperator) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedOperator.Unmarshal(m, b)
}
func (m *BannedOperator) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedOperator.Marshal(b, m, deterministic)
}
func (dst *BannedOperator) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedOperator.Merge(dst, src)
}
func (m *BannedOperator) XXX_Size() int {
	return xxx_messageInfo_BannedOperator.Size(m)
}
func (m *BannedOperator) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedOperator.DiscardUnknown(m)
}

var xxx_messageInfo_BannedOperator proto.InternalMessageInfo

func (m *BannedOperator) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BannedOperator) GetLastOpAt() int64 {
	if m != nil {
		return m.LastOpAt
	}
	return 0
}

func (m *BannedOperator) GetLastOpType() uint32 {
	if m != nil {
		return m.LastOpType
	}
	return 0
}

type GetBannedCheckRecordReq struct {
	TaskIdList            []uint32                `protobuf:"varint,1,rep,packed,name=task_id_list,json=taskIdList,proto3" json:"task_id_list,omitempty"`
	UidList               []uint32                `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	RecordTypeList        []BannedCheckRecordType `protobuf:"varint,3,rep,packed,name=record_type_list,json=recordTypeList,proto3,enum=banserver.BannedCheckRecordType" json:"record_type_list,omitempty"`
	UserTagList           []BannedCheckUserTag    `protobuf:"varint,4,rep,packed,name=user_tag_list,json=userTagList,proto3,enum=banserver.BannedCheckUserTag" json:"user_tag_list,omitempty"`
	SourceList            []BannedCheckSource     `protobuf:"varint,5,rep,packed,name=source_list,json=sourceList,proto3,enum=banserver.BannedCheckSource" json:"source_list,omitempty"`
	ReasonList            []string                `protobuf:"bytes,6,rep,name=reason_list,json=reasonList,proto3" json:"reason_list,omitempty"`
	OperatorNameList      []string                `protobuf:"bytes,7,rep,name=operator_name_list,json=operatorNameList,proto3" json:"operator_name_list,omitempty"`
	BannedBeginAt         int64                   `protobuf:"varint,8,opt,name=banned_begin_at,json=bannedBeginAt,proto3" json:"banned_begin_at,omitempty"`
	BannedEndAt           int64                   `protobuf:"varint,9,opt,name=banned_end_at,json=bannedEndAt,proto3" json:"banned_end_at,omitempty"`
	CheckOperatorNameList []string                `protobuf:"bytes,10,rep,name=check_operator_name_list,json=checkOperatorNameList,proto3" json:"check_operator_name_list,omitempty"`
	CheckBeginAt          int64                   `protobuf:"varint,11,opt,name=check_begin_at,json=checkBeginAt,proto3" json:"check_begin_at,omitempty"`
	CheckEndAt            int64                   `protobuf:"varint,12,opt,name=check_end_at,json=checkEndAt,proto3" json:"check_end_at,omitempty"`
	CheckResultTypeList   []BannedCheckResultType `protobuf:"varint,13,rep,packed,name=check_result_type_list,json=checkResultTypeList,proto3,enum=banserver.BannedCheckResultType" json:"check_result_type_list,omitempty"`
	Offset                uint32                  `protobuf:"varint,14,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                 uint32                  `protobuf:"varint,15,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                `json:"-"`
	XXX_unrecognized      []byte                  `json:"-"`
	XXX_sizecache         int32                   `json:"-"`
}

func (m *GetBannedCheckRecordReq) Reset()         { *m = GetBannedCheckRecordReq{} }
func (m *GetBannedCheckRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckRecordReq) ProtoMessage()    {}
func (*GetBannedCheckRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{1}
}
func (m *GetBannedCheckRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckRecordReq.Unmarshal(m, b)
}
func (m *GetBannedCheckRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckRecordReq.Merge(dst, src)
}
func (m *GetBannedCheckRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckRecordReq.Size(m)
}
func (m *GetBannedCheckRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckRecordReq proto.InternalMessageInfo

func (m *GetBannedCheckRecordReq) GetTaskIdList() []uint32 {
	if m != nil {
		return m.TaskIdList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetRecordTypeList() []BannedCheckRecordType {
	if m != nil {
		return m.RecordTypeList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetUserTagList() []BannedCheckUserTag {
	if m != nil {
		return m.UserTagList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetSourceList() []BannedCheckSource {
	if m != nil {
		return m.SourceList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetReasonList() []string {
	if m != nil {
		return m.ReasonList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetOperatorNameList() []string {
	if m != nil {
		return m.OperatorNameList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetBannedBeginAt() int64 {
	if m != nil {
		return m.BannedBeginAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetBannedEndAt() int64 {
	if m != nil {
		return m.BannedEndAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetCheckOperatorNameList() []string {
	if m != nil {
		return m.CheckOperatorNameList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetCheckBeginAt() int64 {
	if m != nil {
		return m.CheckBeginAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetCheckEndAt() int64 {
	if m != nil {
		return m.CheckEndAt
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetCheckResultTypeList() []BannedCheckResultType {
	if m != nil {
		return m.CheckResultTypeList
	}
	return nil
}

func (m *GetBannedCheckRecordReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetBannedCheckRecordReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type BannedCheckRecord struct {
	TaskId               uint32                `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	RecordType           BannedCheckRecordType `protobuf:"varint,2,opt,name=record_type,json=recordType,proto3,enum=banserver.BannedCheckRecordType" json:"record_type,omitempty"`
	Uid                  uint32                `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	Nickname             string                `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Phone                string                `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
	IdentityNum          string                `protobuf:"bytes,6,opt,name=identity_num,json=identityNum,proto3" json:"identity_num,omitempty"`
	UserTag              BannedCheckUserTag    `protobuf:"varint,7,opt,name=user_tag,json=userTag,proto3,enum=banserver.BannedCheckUserTag" json:"user_tag,omitempty"`
	Reason               string                `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	ReasonDetail         string                `protobuf:"bytes,9,opt,name=reason_detail,json=reasonDetail,proto3" json:"reason_detail,omitempty"`
	ProofPic             string                `protobuf:"bytes,10,opt,name=proof_pic,json=proofPic,proto3" json:"proof_pic,omitempty"`
	Source               BannedCheckSource     `protobuf:"varint,11,opt,name=source,proto3,enum=banserver.BannedCheckSource" json:"source,omitempty"`
	BannedAt             int64                 `protobuf:"varint,12,opt,name=banned_at,json=bannedAt,proto3" json:"banned_at,omitempty"`
	RecoveryAt           int64                 `protobuf:"varint,13,opt,name=recovery_at,json=recoveryAt,proto3" json:"recovery_at,omitempty"`
	OperatorName         string                `protobuf:"bytes,14,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	OpType               BanOpType             `protobuf:"varint,15,opt,name=op_type,json=opType,proto3,enum=banserver.BanOpType" json:"op_type,omitempty"`
	CheckAt              int64                 `protobuf:"varint,16,opt,name=check_at,json=checkAt,proto3" json:"check_at,omitempty"`
	CheckOperatorName    string                `protobuf:"bytes,17,opt,name=check_operator_name,json=checkOperatorName,proto3" json:"check_operator_name,omitempty"`
	RecoverReason        string                `protobuf:"bytes,18,opt,name=recover_reason,json=recoverReason,proto3" json:"recover_reason,omitempty"`
	CheckType            BannedCheckType       `protobuf:"varint,19,opt,name=check_type,json=checkType,proto3,enum=banserver.BannedCheckType" json:"check_type,omitempty"`
	CheckResultType      BannedCheckResultType `protobuf:"varint,20,opt,name=check_result_type,json=checkResultType,proto3,enum=banserver.BannedCheckResultType" json:"check_result_type,omitempty"`
	CheckResultDesc      string                `protobuf:"bytes,21,opt,name=check_result_desc,json=checkResultDesc,proto3" json:"check_result_desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BannedCheckRecord) Reset()         { *m = BannedCheckRecord{} }
func (m *BannedCheckRecord) String() string { return proto.CompactTextString(m) }
func (*BannedCheckRecord) ProtoMessage()    {}
func (*BannedCheckRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{2}
}
func (m *BannedCheckRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannedCheckRecord.Unmarshal(m, b)
}
func (m *BannedCheckRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannedCheckRecord.Marshal(b, m, deterministic)
}
func (dst *BannedCheckRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannedCheckRecord.Merge(dst, src)
}
func (m *BannedCheckRecord) XXX_Size() int {
	return xxx_messageInfo_BannedCheckRecord.Size(m)
}
func (m *BannedCheckRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_BannedCheckRecord.DiscardUnknown(m)
}

var xxx_messageInfo_BannedCheckRecord proto.InternalMessageInfo

func (m *BannedCheckRecord) GetTaskId() uint32 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *BannedCheckRecord) GetRecordType() BannedCheckRecordType {
	if m != nil {
		return m.RecordType
	}
	return BannedCheckRecordType_BANNED_CHECK_RECORD_TYPE_UNSPECIFIED
}

func (m *BannedCheckRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BannedCheckRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BannedCheckRecord) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *BannedCheckRecord) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *BannedCheckRecord) GetUserTag() BannedCheckUserTag {
	if m != nil {
		return m.UserTag
	}
	return BannedCheckUserTag_BANNED_CHECK_USER_TAG_UNSPECIFIED
}

func (m *BannedCheckRecord) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *BannedCheckRecord) GetReasonDetail() string {
	if m != nil {
		return m.ReasonDetail
	}
	return ""
}

func (m *BannedCheckRecord) GetProofPic() string {
	if m != nil {
		return m.ProofPic
	}
	return ""
}

func (m *BannedCheckRecord) GetSource() BannedCheckSource {
	if m != nil {
		return m.Source
	}
	return BannedCheckSource_BANNED_CHECK_SOURCE_UNSPECIFIED
}

func (m *BannedCheckRecord) GetBannedAt() int64 {
	if m != nil {
		return m.BannedAt
	}
	return 0
}

func (m *BannedCheckRecord) GetRecoveryAt() int64 {
	if m != nil {
		return m.RecoveryAt
	}
	return 0
}

func (m *BannedCheckRecord) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *BannedCheckRecord) GetOpType() BanOpType {
	if m != nil {
		return m.OpType
	}
	return BanOpType_BAN_OP_UNSPECIFIED
}

func (m *BannedCheckRecord) GetCheckAt() int64 {
	if m != nil {
		return m.CheckAt
	}
	return 0
}

func (m *BannedCheckRecord) GetCheckOperatorName() string {
	if m != nil {
		return m.CheckOperatorName
	}
	return ""
}

func (m *BannedCheckRecord) GetRecoverReason() string {
	if m != nil {
		return m.RecoverReason
	}
	return ""
}

func (m *BannedCheckRecord) GetCheckType() BannedCheckType {
	if m != nil {
		return m.CheckType
	}
	return BannedCheckType_BANNED_CHECK_TYPE_UNSPECIFIED
}

func (m *BannedCheckRecord) GetCheckResultType() BannedCheckResultType {
	if m != nil {
		return m.CheckResultType
	}
	return BannedCheckResultType_BANNED_CHECK_RESULT_TYPE_UNSPECIFIED
}

func (m *BannedCheckRecord) GetCheckResultDesc() string {
	if m != nil {
		return m.CheckResultDesc
	}
	return ""
}

type GetBannedCheckRecordResp struct {
	RecordList           []*BannedCheckRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetBannedCheckRecordResp) Reset()         { *m = GetBannedCheckRecordResp{} }
func (m *GetBannedCheckRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckRecordResp) ProtoMessage()    {}
func (*GetBannedCheckRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{3}
}
func (m *GetBannedCheckRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckRecordResp.Unmarshal(m, b)
}
func (m *GetBannedCheckRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckRecordResp.Merge(dst, src)
}
func (m *GetBannedCheckRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckRecordResp.Size(m)
}
func (m *GetBannedCheckRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckRecordResp proto.InternalMessageInfo

func (m *GetBannedCheckRecordResp) GetRecordList() []*BannedCheckRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetBannedCheckRecordResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type AddBannedCheckRecordReq struct {
	Record               *BannedCheckRecord `protobuf:"bytes,1,opt,name=record,proto3" json:"record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *AddBannedCheckRecordReq) Reset()         { *m = AddBannedCheckRecordReq{} }
func (m *AddBannedCheckRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddBannedCheckRecordReq) ProtoMessage()    {}
func (*AddBannedCheckRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{4}
}
func (m *AddBannedCheckRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBannedCheckRecordReq.Unmarshal(m, b)
}
func (m *AddBannedCheckRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBannedCheckRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddBannedCheckRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBannedCheckRecordReq.Merge(dst, src)
}
func (m *AddBannedCheckRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddBannedCheckRecordReq.Size(m)
}
func (m *AddBannedCheckRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBannedCheckRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBannedCheckRecordReq proto.InternalMessageInfo

func (m *AddBannedCheckRecordReq) GetRecord() *BannedCheckRecord {
	if m != nil {
		return m.Record
	}
	return nil
}

type AddBannedCheckRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBannedCheckRecordResp) Reset()         { *m = AddBannedCheckRecordResp{} }
func (m *AddBannedCheckRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddBannedCheckRecordResp) ProtoMessage()    {}
func (*AddBannedCheckRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{5}
}
func (m *AddBannedCheckRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBannedCheckRecordResp.Unmarshal(m, b)
}
func (m *AddBannedCheckRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBannedCheckRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddBannedCheckRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBannedCheckRecordResp.Merge(dst, src)
}
func (m *AddBannedCheckRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddBannedCheckRecordResp.Size(m)
}
func (m *AddBannedCheckRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBannedCheckRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBannedCheckRecordResp proto.InternalMessageInfo

type UpdateBannedCheckRecordReq struct {
	Uid                  uint32                  `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskIdList           []uint32                `protobuf:"varint,2,rep,packed,name=task_id_list,json=taskIdList,proto3" json:"task_id_list,omitempty"`
	RecordTypeList       []BannedCheckRecordType `protobuf:"varint,3,rep,packed,name=record_type_list,json=recordTypeList,proto3,enum=banserver.BannedCheckRecordType" json:"record_type_list,omitempty"`
	UpdateRecord         *BannedCheckRecord      `protobuf:"bytes,4,opt,name=update_record,json=updateRecord,proto3" json:"update_record,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpdateBannedCheckRecordReq) Reset()         { *m = UpdateBannedCheckRecordReq{} }
func (m *UpdateBannedCheckRecordReq) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedCheckRecordReq) ProtoMessage()    {}
func (*UpdateBannedCheckRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{6}
}
func (m *UpdateBannedCheckRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Unmarshal(m, b)
}
func (m *UpdateBannedCheckRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedCheckRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedCheckRecordReq.Merge(dst, src)
}
func (m *UpdateBannedCheckRecordReq) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedCheckRecordReq.Size(m)
}
func (m *UpdateBannedCheckRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedCheckRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedCheckRecordReq proto.InternalMessageInfo

func (m *UpdateBannedCheckRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateBannedCheckRecordReq) GetTaskIdList() []uint32 {
	if m != nil {
		return m.TaskIdList
	}
	return nil
}

func (m *UpdateBannedCheckRecordReq) GetRecordTypeList() []BannedCheckRecordType {
	if m != nil {
		return m.RecordTypeList
	}
	return nil
}

func (m *UpdateBannedCheckRecordReq) GetUpdateRecord() *BannedCheckRecord {
	if m != nil {
		return m.UpdateRecord
	}
	return nil
}

type UpdateBannedCheckRecordResp struct {
	Affected             int64    `protobuf:"varint,1,opt,name=affected,proto3" json:"affected,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateBannedCheckRecordResp) Reset()         { *m = UpdateBannedCheckRecordResp{} }
func (m *UpdateBannedCheckRecordResp) String() string { return proto.CompactTextString(m) }
func (*UpdateBannedCheckRecordResp) ProtoMessage()    {}
func (*UpdateBannedCheckRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{7}
}
func (m *UpdateBannedCheckRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Unmarshal(m, b)
}
func (m *UpdateBannedCheckRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Marshal(b, m, deterministic)
}
func (dst *UpdateBannedCheckRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateBannedCheckRecordResp.Merge(dst, src)
}
func (m *UpdateBannedCheckRecordResp) XXX_Size() int {
	return xxx_messageInfo_UpdateBannedCheckRecordResp.Size(m)
}
func (m *UpdateBannedCheckRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateBannedCheckRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateBannedCheckRecordResp proto.InternalMessageInfo

func (m *UpdateBannedCheckRecordResp) GetAffected() int64 {
	if m != nil {
		return m.Affected
	}
	return 0
}

type GetBannedCheckOperatorReq struct {
	OperatorName         string   `protobuf:"bytes,1,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannedCheckOperatorReq) Reset()         { *m = GetBannedCheckOperatorReq{} }
func (m *GetBannedCheckOperatorReq) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckOperatorReq) ProtoMessage()    {}
func (*GetBannedCheckOperatorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{8}
}
func (m *GetBannedCheckOperatorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckOperatorReq.Unmarshal(m, b)
}
func (m *GetBannedCheckOperatorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckOperatorReq.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckOperatorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckOperatorReq.Merge(dst, src)
}
func (m *GetBannedCheckOperatorReq) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckOperatorReq.Size(m)
}
func (m *GetBannedCheckOperatorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckOperatorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckOperatorReq proto.InternalMessageInfo

func (m *GetBannedCheckOperatorReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *GetBannedCheckOperatorReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetBannedCheckOperatorResp struct {
	OperatorList         []*BannedOperator `protobuf:"bytes,1,rep,name=operator_list,json=operatorList,proto3" json:"operator_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetBannedCheckOperatorResp) Reset()         { *m = GetBannedCheckOperatorResp{} }
func (m *GetBannedCheckOperatorResp) String() string { return proto.CompactTextString(m) }
func (*GetBannedCheckOperatorResp) ProtoMessage()    {}
func (*GetBannedCheckOperatorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{9}
}
func (m *GetBannedCheckOperatorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannedCheckOperatorResp.Unmarshal(m, b)
}
func (m *GetBannedCheckOperatorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannedCheckOperatorResp.Marshal(b, m, deterministic)
}
func (dst *GetBannedCheckOperatorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannedCheckOperatorResp.Merge(dst, src)
}
func (m *GetBannedCheckOperatorResp) XXX_Size() int {
	return xxx_messageInfo_GetBannedCheckOperatorResp.Size(m)
}
func (m *GetBannedCheckOperatorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannedCheckOperatorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannedCheckOperatorResp proto.InternalMessageInfo

func (m *GetBannedCheckOperatorResp) GetOperatorList() []*BannedOperator {
	if m != nil {
		return m.OperatorList
	}
	return nil
}

type SetBannedCheckOperatorReq struct {
	OperatorName         string   `protobuf:"bytes,1,opt,name=operator_name,json=operatorName,proto3" json:"operator_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBannedCheckOperatorReq) Reset()         { *m = SetBannedCheckOperatorReq{} }
func (m *SetBannedCheckOperatorReq) String() string { return proto.CompactTextString(m) }
func (*SetBannedCheckOperatorReq) ProtoMessage()    {}
func (*SetBannedCheckOperatorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{10}
}
func (m *SetBannedCheckOperatorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBannedCheckOperatorReq.Unmarshal(m, b)
}
func (m *SetBannedCheckOperatorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBannedCheckOperatorReq.Marshal(b, m, deterministic)
}
func (dst *SetBannedCheckOperatorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBannedCheckOperatorReq.Merge(dst, src)
}
func (m *SetBannedCheckOperatorReq) XXX_Size() int {
	return xxx_messageInfo_SetBannedCheckOperatorReq.Size(m)
}
func (m *SetBannedCheckOperatorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBannedCheckOperatorReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetBannedCheckOperatorReq proto.InternalMessageInfo

func (m *SetBannedCheckOperatorReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

type SetBannedCheckOperatorResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetBannedCheckOperatorResp) Reset()         { *m = SetBannedCheckOperatorResp{} }
func (m *SetBannedCheckOperatorResp) String() string { return proto.CompactTextString(m) }
func (*SetBannedCheckOperatorResp) ProtoMessage()    {}
func (*SetBannedCheckOperatorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{11}
}
func (m *SetBannedCheckOperatorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetBannedCheckOperatorResp.Unmarshal(m, b)
}
func (m *SetBannedCheckOperatorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetBannedCheckOperatorResp.Marshal(b, m, deterministic)
}
func (dst *SetBannedCheckOperatorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetBannedCheckOperatorResp.Merge(dst, src)
}
func (m *SetBannedCheckOperatorResp) XXX_Size() int {
	return xxx_messageInfo_SetBannedCheckOperatorResp.Size(m)
}
func (m *SetBannedCheckOperatorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetBannedCheckOperatorResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetBannedCheckOperatorResp proto.InternalMessageInfo

type SubmitBannedAppealReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReasonCode           uint32   `protobuf:"varint,2,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitBannedAppealReq) Reset()         { *m = SubmitBannedAppealReq{} }
func (m *SubmitBannedAppealReq) String() string { return proto.CompactTextString(m) }
func (*SubmitBannedAppealReq) ProtoMessage()    {}
func (*SubmitBannedAppealReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{12}
}
func (m *SubmitBannedAppealReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitBannedAppealReq.Unmarshal(m, b)
}
func (m *SubmitBannedAppealReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitBannedAppealReq.Marshal(b, m, deterministic)
}
func (dst *SubmitBannedAppealReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitBannedAppealReq.Merge(dst, src)
}
func (m *SubmitBannedAppealReq) XXX_Size() int {
	return xxx_messageInfo_SubmitBannedAppealReq.Size(m)
}
func (m *SubmitBannedAppealReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitBannedAppealReq.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitBannedAppealReq proto.InternalMessageInfo

func (m *SubmitBannedAppealReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SubmitBannedAppealReq) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

type SubmitBannedAppealResp struct {
	// 申诉已存在
	Existed              bool     `protobuf:"varint,1,opt,name=existed,proto3" json:"existed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SubmitBannedAppealResp) Reset()         { *m = SubmitBannedAppealResp{} }
func (m *SubmitBannedAppealResp) String() string { return proto.CompactTextString(m) }
func (*SubmitBannedAppealResp) ProtoMessage()    {}
func (*SubmitBannedAppealResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{13}
}
func (m *SubmitBannedAppealResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SubmitBannedAppealResp.Unmarshal(m, b)
}
func (m *SubmitBannedAppealResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SubmitBannedAppealResp.Marshal(b, m, deterministic)
}
func (dst *SubmitBannedAppealResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SubmitBannedAppealResp.Merge(dst, src)
}
func (m *SubmitBannedAppealResp) XXX_Size() int {
	return xxx_messageInfo_SubmitBannedAppealResp.Size(m)
}
func (m *SubmitBannedAppealResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SubmitBannedAppealResp.DiscardUnknown(m)
}

var xxx_messageInfo_SubmitBannedAppealResp proto.InternalMessageInfo

func (m *SubmitBannedAppealResp) GetExisted() bool {
	if m != nil {
		return m.Existed
	}
	return false
}

type RemoveBannedAppealReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReasonCode           uint32   `protobuf:"varint,2,opt,name=reason_code,json=reasonCode,proto3" json:"reason_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveBannedAppealReq) Reset()         { *m = RemoveBannedAppealReq{} }
func (m *RemoveBannedAppealReq) String() string { return proto.CompactTextString(m) }
func (*RemoveBannedAppealReq) ProtoMessage()    {}
func (*RemoveBannedAppealReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{14}
}
func (m *RemoveBannedAppealReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveBannedAppealReq.Unmarshal(m, b)
}
func (m *RemoveBannedAppealReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveBannedAppealReq.Marshal(b, m, deterministic)
}
func (dst *RemoveBannedAppealReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveBannedAppealReq.Merge(dst, src)
}
func (m *RemoveBannedAppealReq) XXX_Size() int {
	return xxx_messageInfo_RemoveBannedAppealReq.Size(m)
}
func (m *RemoveBannedAppealReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveBannedAppealReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveBannedAppealReq proto.InternalMessageInfo

func (m *RemoveBannedAppealReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *RemoveBannedAppealReq) GetReasonCode() uint32 {
	if m != nil {
		return m.ReasonCode
	}
	return 0
}

type RemoveBannedAppealResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveBannedAppealResp) Reset()         { *m = RemoveBannedAppealResp{} }
func (m *RemoveBannedAppealResp) String() string { return proto.CompactTextString(m) }
func (*RemoveBannedAppealResp) ProtoMessage()    {}
func (*RemoveBannedAppealResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_banserver_3de5a7d220373f5b, []int{15}
}
func (m *RemoveBannedAppealResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveBannedAppealResp.Unmarshal(m, b)
}
func (m *RemoveBannedAppealResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveBannedAppealResp.Marshal(b, m, deterministic)
}
func (dst *RemoveBannedAppealResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveBannedAppealResp.Merge(dst, src)
}
func (m *RemoveBannedAppealResp) XXX_Size() int {
	return xxx_messageInfo_RemoveBannedAppealResp.Size(m)
}
func (m *RemoveBannedAppealResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveBannedAppealResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveBannedAppealResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*BannedOperator)(nil), "banserver.BannedOperator")
	proto.RegisterType((*GetBannedCheckRecordReq)(nil), "banserver.GetBannedCheckRecordReq")
	proto.RegisterType((*BannedCheckRecord)(nil), "banserver.BannedCheckRecord")
	proto.RegisterType((*GetBannedCheckRecordResp)(nil), "banserver.GetBannedCheckRecordResp")
	proto.RegisterType((*AddBannedCheckRecordReq)(nil), "banserver.AddBannedCheckRecordReq")
	proto.RegisterType((*AddBannedCheckRecordResp)(nil), "banserver.AddBannedCheckRecordResp")
	proto.RegisterType((*UpdateBannedCheckRecordReq)(nil), "banserver.UpdateBannedCheckRecordReq")
	proto.RegisterType((*UpdateBannedCheckRecordResp)(nil), "banserver.UpdateBannedCheckRecordResp")
	proto.RegisterType((*GetBannedCheckOperatorReq)(nil), "banserver.GetBannedCheckOperatorReq")
	proto.RegisterType((*GetBannedCheckOperatorResp)(nil), "banserver.GetBannedCheckOperatorResp")
	proto.RegisterType((*SetBannedCheckOperatorReq)(nil), "banserver.SetBannedCheckOperatorReq")
	proto.RegisterType((*SetBannedCheckOperatorResp)(nil), "banserver.SetBannedCheckOperatorResp")
	proto.RegisterType((*SubmitBannedAppealReq)(nil), "banserver.SubmitBannedAppealReq")
	proto.RegisterType((*SubmitBannedAppealResp)(nil), "banserver.SubmitBannedAppealResp")
	proto.RegisterType((*RemoveBannedAppealReq)(nil), "banserver.RemoveBannedAppealReq")
	proto.RegisterType((*RemoveBannedAppealResp)(nil), "banserver.RemoveBannedAppealResp")
	proto.RegisterEnum("banserver.BanOpType", BanOpType_name, BanOpType_value)
	proto.RegisterEnum("banserver.BannedCheckUserTag", BannedCheckUserTag_name, BannedCheckUserTag_value)
	proto.RegisterEnum("banserver.BannedCheckSource", BannedCheckSource_name, BannedCheckSource_value)
	proto.RegisterEnum("banserver.BannedCheckRecordType", BannedCheckRecordType_name, BannedCheckRecordType_value)
	proto.RegisterEnum("banserver.BannedCheckType", BannedCheckType_name, BannedCheckType_value)
	proto.RegisterEnum("banserver.BannedCheckResultType", BannedCheckResultType_name, BannedCheckResultType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// BanServerClient is the client API for BanServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type BanServerClient interface {
	// 获取黑名单复核记录
	GetBannedCheckRecord(ctx context.Context, in *GetBannedCheckRecordReq, opts ...grpc.CallOption) (*GetBannedCheckRecordResp, error)
	// 更新黑名单复核记录
	UpdateBannedCheckRecord(ctx context.Context, in *UpdateBannedCheckRecordReq, opts ...grpc.CallOption) (*UpdateBannedCheckRecordResp, error)
	// 新增黑名单复核记录
	AddBannedCheckRecord(ctx context.Context, in *AddBannedCheckRecordReq, opts ...grpc.CallOption) (*AddBannedCheckRecordResp, error)
	// 获取黑名单复核操作人
	GetBannedCheckOperator(ctx context.Context, in *GetBannedCheckOperatorReq, opts ...grpc.CallOption) (*GetBannedCheckOperatorResp, error)
	// 写入黑名单复核操作人
	SetBannedCheckOperator(ctx context.Context, in *SetBannedCheckOperatorReq, opts ...grpc.CallOption) (*SetBannedCheckOperatorResp, error)
	SubmitBannedAppeal(ctx context.Context, in *SubmitBannedAppealReq, opts ...grpc.CallOption) (*SubmitBannedAppealResp, error)
	RemoveBannedAppeal(ctx context.Context, in *RemoveBannedAppealReq, opts ...grpc.CallOption) (*RemoveBannedAppealResp, error)
}

type banServerClient struct {
	cc *grpc.ClientConn
}

func NewBanServerClient(cc *grpc.ClientConn) BanServerClient {
	return &banServerClient{cc}
}

func (c *banServerClient) GetBannedCheckRecord(ctx context.Context, in *GetBannedCheckRecordReq, opts ...grpc.CallOption) (*GetBannedCheckRecordResp, error) {
	out := new(GetBannedCheckRecordResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/GetBannedCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banServerClient) UpdateBannedCheckRecord(ctx context.Context, in *UpdateBannedCheckRecordReq, opts ...grpc.CallOption) (*UpdateBannedCheckRecordResp, error) {
	out := new(UpdateBannedCheckRecordResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/UpdateBannedCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banServerClient) AddBannedCheckRecord(ctx context.Context, in *AddBannedCheckRecordReq, opts ...grpc.CallOption) (*AddBannedCheckRecordResp, error) {
	out := new(AddBannedCheckRecordResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/AddBannedCheckRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banServerClient) GetBannedCheckOperator(ctx context.Context, in *GetBannedCheckOperatorReq, opts ...grpc.CallOption) (*GetBannedCheckOperatorResp, error) {
	out := new(GetBannedCheckOperatorResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/GetBannedCheckOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banServerClient) SetBannedCheckOperator(ctx context.Context, in *SetBannedCheckOperatorReq, opts ...grpc.CallOption) (*SetBannedCheckOperatorResp, error) {
	out := new(SetBannedCheckOperatorResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/SetBannedCheckOperator", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banServerClient) SubmitBannedAppeal(ctx context.Context, in *SubmitBannedAppealReq, opts ...grpc.CallOption) (*SubmitBannedAppealResp, error) {
	out := new(SubmitBannedAppealResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/SubmitBannedAppeal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *banServerClient) RemoveBannedAppeal(ctx context.Context, in *RemoveBannedAppealReq, opts ...grpc.CallOption) (*RemoveBannedAppealResp, error) {
	out := new(RemoveBannedAppealResp)
	err := c.cc.Invoke(ctx, "/banserver.BanServer/RemoveBannedAppeal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BanServerServer is the server API for BanServer service.
type BanServerServer interface {
	// 获取黑名单复核记录
	GetBannedCheckRecord(context.Context, *GetBannedCheckRecordReq) (*GetBannedCheckRecordResp, error)
	// 更新黑名单复核记录
	UpdateBannedCheckRecord(context.Context, *UpdateBannedCheckRecordReq) (*UpdateBannedCheckRecordResp, error)
	// 新增黑名单复核记录
	AddBannedCheckRecord(context.Context, *AddBannedCheckRecordReq) (*AddBannedCheckRecordResp, error)
	// 获取黑名单复核操作人
	GetBannedCheckOperator(context.Context, *GetBannedCheckOperatorReq) (*GetBannedCheckOperatorResp, error)
	// 写入黑名单复核操作人
	SetBannedCheckOperator(context.Context, *SetBannedCheckOperatorReq) (*SetBannedCheckOperatorResp, error)
	SubmitBannedAppeal(context.Context, *SubmitBannedAppealReq) (*SubmitBannedAppealResp, error)
	RemoveBannedAppeal(context.Context, *RemoveBannedAppealReq) (*RemoveBannedAppealResp, error)
}

func RegisterBanServerServer(s *grpc.Server, srv BanServerServer) {
	s.RegisterService(&_BanServer_serviceDesc, srv)
}

func _BanServer_GetBannedCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedCheckRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).GetBannedCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/GetBannedCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).GetBannedCheckRecord(ctx, req.(*GetBannedCheckRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanServer_UpdateBannedCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBannedCheckRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).UpdateBannedCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/UpdateBannedCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).UpdateBannedCheckRecord(ctx, req.(*UpdateBannedCheckRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanServer_AddBannedCheckRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBannedCheckRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).AddBannedCheckRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/AddBannedCheckRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).AddBannedCheckRecord(ctx, req.(*AddBannedCheckRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanServer_GetBannedCheckOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannedCheckOperatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).GetBannedCheckOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/GetBannedCheckOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).GetBannedCheckOperator(ctx, req.(*GetBannedCheckOperatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanServer_SetBannedCheckOperator_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetBannedCheckOperatorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).SetBannedCheckOperator(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/SetBannedCheckOperator",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).SetBannedCheckOperator(ctx, req.(*SetBannedCheckOperatorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanServer_SubmitBannedAppeal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SubmitBannedAppealReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).SubmitBannedAppeal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/SubmitBannedAppeal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).SubmitBannedAppeal(ctx, req.(*SubmitBannedAppealReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _BanServer_RemoveBannedAppeal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveBannedAppealReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BanServerServer).RemoveBannedAppeal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/banserver.BanServer/RemoveBannedAppeal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BanServerServer).RemoveBannedAppeal(ctx, req.(*RemoveBannedAppealReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _BanServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "banserver.BanServer",
	HandlerType: (*BanServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBannedCheckRecord",
			Handler:    _BanServer_GetBannedCheckRecord_Handler,
		},
		{
			MethodName: "UpdateBannedCheckRecord",
			Handler:    _BanServer_UpdateBannedCheckRecord_Handler,
		},
		{
			MethodName: "AddBannedCheckRecord",
			Handler:    _BanServer_AddBannedCheckRecord_Handler,
		},
		{
			MethodName: "GetBannedCheckOperator",
			Handler:    _BanServer_GetBannedCheckOperator_Handler,
		},
		{
			MethodName: "SetBannedCheckOperator",
			Handler:    _BanServer_SetBannedCheckOperator_Handler,
		},
		{
			MethodName: "SubmitBannedAppeal",
			Handler:    _BanServer_SubmitBannedAppeal_Handler,
		},
		{
			MethodName: "RemoveBannedAppeal",
			Handler:    _BanServer_RemoveBannedAppeal_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "banserver/banserver.proto",
}

func init() {
	proto.RegisterFile("banserver/banserver.proto", fileDescriptor_banserver_3de5a7d220373f5b)
}

var fileDescriptor_banserver_3de5a7d220373f5b = []byte{
	// 1570 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0x4f, 0x4f, 0xe3, 0x46,
	0x14, 0x27, 0x09, 0xe4, 0xcf, 0x83, 0x80, 0x19, 0x20, 0x98, 0xc0, 0x76, 0x83, 0x81, 0x2d, 0xa2,
	0x5d, 0x56, 0xa5, 0x5b, 0xb5, 0x7b, 0x68, 0x55, 0x93, 0x78, 0x21, 0xbb, 0x28, 0x89, 0x9c, 0x64,
	0x25, 0xda, 0x4a, 0x96, 0x71, 0x06, 0xd6, 0xdd, 0x24, 0x76, 0x6d, 0x07, 0x95, 0x1b, 0x97, 0x7e,
	0xb4, 0x1e, 0x7a, 0xe8, 0x07, 0xa8, 0x7a, 0xe8, 0xb1, 0x5f, 0xa3, 0x9a, 0x37, 0xb6, 0xe3, 0x24,
	0x0e, 0x41, 0xea, 0x9e, 0xf0, 0xfc, 0xe6, 0x37, 0xef, 0xbd, 0x79, 0xf3, 0x7b, 0xf3, 0x86, 0xc0,
	0xd6, 0x95, 0xde, 0x77, 0xa9, 0x73, 0x4b, 0x9d, 0x17, 0xe1, 0xd7, 0xb1, 0xed, 0x58, 0x9e, 0x45,
	0x72, 0x21, 0x20, 0x0d, 0x60, 0xf9, 0x54, 0xef, 0xf7, 0x69, 0xa7, 0x6e, 0x53, 0x47, 0xf7, 0x2c,
	0x87, 0xec, 0x41, 0xde, 0xf2, 0xbf, 0xb5, 0xbe, 0xde, 0xa3, 0x62, 0xa2, 0x94, 0x38, 0xcc, 0xa9,
	0x4b, 0x01, 0x58, 0xd3, 0x7b, 0x94, 0xec, 0x00, 0x74, 0x75, 0xd7, 0xd3, 0x2c, 0x5b, 0xd3, 0x3d,
	0x31, 0x59, 0x4a, 0x1c, 0xa6, 0xd4, 0x2c, 0x43, 0xea, 0xb6, 0xec, 0x91, 0x12, 0x2c, 0x05, 0xb3,
	0xde, 0x9d, 0x4d, 0xc5, 0x54, 0x29, 0x71, 0x98, 0x57, 0x81, 0xcf, 0xb7, 0xee, 0x6c, 0x2a, 0xfd,
	0xbe, 0x00, 0x9b, 0x67, 0xd4, 0xe3, 0xae, 0xcb, 0xef, 0xa9, 0xf1, 0x41, 0xa5, 0x86, 0xe5, 0x74,
	0x54, 0xfa, 0x0b, 0x5b, 0xed, 0xe9, 0xee, 0x07, 0xcd, 0xec, 0x68, 0x5d, 0xd3, 0xf5, 0xc4, 0x44,
	0x29, 0xc5, 0x56, 0x33, 0xac, 0xda, 0xb9, 0x30, 0x5d, 0x8f, 0x6c, 0x41, 0x76, 0x10, 0xcc, 0x26,
	0x71, 0x36, 0x33, 0x30, 0xf9, 0xd4, 0x1b, 0x10, 0x1c, 0xb4, 0x84, 0x9e, 0x39, 0x25, 0x55, 0x4a,
	0x1d, 0x2e, 0x9f, 0x94, 0x8e, 0x87, 0x69, 0x98, 0xf0, 0xcb, 0x82, 0x52, 0x97, 0x9d, 0xf0, 0x1b,
	0x6d, 0xc9, 0x90, 0x1f, 0xb8, 0xd4, 0xd1, 0x3c, 0xfd, 0x86, 0x1b, 0x9a, 0x47, 0x43, 0x4f, 0xe2,
	0x0d, 0xb5, 0x5d, 0xea, 0xb4, 0xf4, 0x1b, 0x75, 0x71, 0xc0, 0x3f, 0xd0, 0xc4, 0xb7, 0xb0, 0xe8,
	0x5a, 0x03, 0xc7, 0xf0, 0x23, 0x59, 0x40, 0x03, 0x3b, 0xf1, 0x06, 0x9a, 0x48, 0x54, 0x81, 0x2f,
	0xc0, 0xe5, 0x4f, 0x61, 0xd1, 0xa1, 0xba, 0x6b, 0xf5, 0xf9, 0xf2, 0x74, 0x29, 0x75, 0x98, 0x53,
	0x81, 0x43, 0x48, 0xf8, 0x1c, 0xc8, 0xc8, 0x61, 0x71, 0x5e, 0x06, 0x79, 0x42, 0xf4, 0xc4, 0x90,
	0xfd, 0x0c, 0x56, 0xae, 0xd0, 0x9f, 0x76, 0x45, 0x6f, 0xcc, 0x3e, 0x3b, 0xba, 0x2c, 0x1e, 0x5d,
	0x9e, 0xc3, 0xa7, 0x0c, 0x95, 0x3d, 0x22, 0x81, 0x0f, 0x68, 0xb4, 0xdf, 0x61, 0xac, 0x1c, 0xb2,
	0x16, 0x39, 0xa8, 0xf4, 0x3b, 0xb2, 0x47, 0xbe, 0x06, 0xd1, 0x60, 0x51, 0x6b, 0x31, 0xfe, 0x01,
	0xfd, 0x6f, 0xe0, 0x7c, 0x7d, 0x3c, 0x88, 0x7d, 0x58, 0xe6, 0x0b, 0xc3, 0x18, 0x16, 0xd1, 0xfa,
	0x12, 0xa2, 0x41, 0x08, 0x25, 0xe0, 0xe3, 0x20, 0x82, 0x25, 0xe4, 0x00, 0x62, 0x3c, 0x80, 0x36,
	0x14, 0x38, 0xc3, 0xa1, 0xee, 0xa0, 0xeb, 0x45, 0xce, 0x3b, 0xff, 0xf0, 0x79, 0x33, 0x36, 0x9e,
	0xf7, 0x9a, 0x31, 0x0a, 0x60, 0x78, 0x05, 0x48, 0x5b, 0xd7, 0xd7, 0x2e, 0xf5, 0xc4, 0x65, 0x54,
	0xad, 0x3f, 0x22, 0xeb, 0xb0, 0xd0, 0x35, 0x7b, 0xa6, 0x27, 0xae, 0x20, 0xcc, 0x07, 0xd2, 0x5f,
	0x69, 0x58, 0x9d, 0x10, 0x13, 0xd9, 0x84, 0x8c, 0xaf, 0x60, 0x2c, 0x9e, 0xbc, 0x9a, 0xe6, 0xe2,
	0x25, 0x32, 0x3b, 0xcf, 0x50, 0x9d, 0x58, 0x37, 0x8f, 0x11, 0x26, 0x0c, 0x85, 0x49, 0x04, 0x48,
	0x0d, 0xcc, 0x8e, 0x5f, 0x52, 0xec, 0x93, 0x14, 0x21, 0xdb, 0x37, 0x8d, 0x0f, 0x58, 0xab, 0xf3,
	0x58, 0xab, 0xe1, 0x98, 0x45, 0x6d, 0xbf, 0xb7, 0xfa, 0x54, 0x5c, 0xc0, 0x09, 0x3e, 0x20, 0xbb,
	0xb0, 0x64, 0x76, 0x68, 0xdf, 0x33, 0xbd, 0x3b, 0xad, 0x3f, 0xe8, 0x89, 0x69, 0x9c, 0x5c, 0x0c,
	0xb0, 0xda, 0xa0, 0x47, 0xbe, 0x81, 0x6c, 0xa0, 0x7d, 0x31, 0x83, 0x61, 0xce, 0x90, 0x7d, 0xc6,
	0x97, 0x3d, 0x4b, 0x20, 0x17, 0x28, 0x6a, 0x2b, 0xa7, 0xfa, 0x23, 0x76, 0xaf, 0xf8, 0x5a, 0xee,
	0x50, 0x4f, 0x37, 0xbb, 0x28, 0xaa, 0x9c, 0xba, 0xc4, 0xc1, 0x0a, 0x62, 0x64, 0x1b, 0x72, 0xb6,
	0x63, 0x59, 0xd7, 0x9a, 0x6d, 0x1a, 0x22, 0xf0, 0xcd, 0x20, 0xd0, 0x30, 0x0d, 0xf2, 0x12, 0xd2,
	0xbc, 0x36, 0x50, 0x31, 0xb3, 0xea, 0xc8, 0xe7, 0x32, 0x93, 0xbe, 0x98, 0x43, 0x19, 0x65, 0x39,
	0x20, 0xfb, 0x05, 0x66, 0x58, 0xb7, 0xd4, 0xb9, 0x63, 0xd3, 0x79, 0xae, 0xb2, 0x00, 0x92, 0xbd,
	0xc9, 0xdb, 0x70, 0x39, 0xe6, 0x36, 0x7c, 0x0e, 0x99, 0xe0, 0xaa, 0x5b, 0xc1, 0xc8, 0xd6, 0x47,
	0x23, 0xe3, 0x97, 0x9e, 0x9a, 0xb6, 0xf0, 0x2f, 0xbb, 0xbe, 0xb8, 0x72, 0x75, 0x4f, 0x14, 0xd0,
	0x63, 0x06, 0xc7, 0xb2, 0x47, 0x8e, 0x61, 0x2d, 0xa6, 0xaa, 0xc4, 0x55, 0x74, 0xba, 0x3a, 0x51,
	0x50, 0xe4, 0x00, 0x96, 0xfd, 0x60, 0x35, 0x3f, 0xe9, 0x04, 0xa9, 0x79, 0x1f, 0x55, 0x79, 0xee,
	0x5f, 0x01, 0xaf, 0x1c, 0x1e, 0xe3, 0x1a, 0xc6, 0x58, 0x8c, 0xcf, 0x1e, 0x46, 0x9a, 0x33, 0x82,
	0x4f, 0x72, 0x01, 0xab, 0x13, 0x65, 0x26, 0xae, 0x3f, 0x2c, 0xdc, 0xb0, 0xc2, 0x56, 0xc6, 0x2a,
	0x8c, 0x1c, 0x8d, 0x59, 0xeb, 0x50, 0xd7, 0x10, 0x37, 0x30, 0xe4, 0x28, 0xb7, 0x42, 0x5d, 0x43,
	0xba, 0x05, 0x31, 0xbe, 0x45, 0xb8, 0x36, 0xbb, 0x57, 0xfd, 0x42, 0x0a, 0x5b, 0xc4, 0xe2, 0x34,
	0x3d, 0xf8, 0xcb, 0xfc, 0x22, 0xc2, 0x22, 0xdf, 0x86, 0x9c, 0x67, 0x79, 0x7a, 0x57, 0x33, 0xfa,
	0xbc, 0x7b, 0xe5, 0xd5, 0x2c, 0x02, 0xe5, 0xbe, 0x27, 0xd5, 0x61, 0x53, 0xee, 0x74, 0x62, 0x5b,
	0xd3, 0x4b, 0xa6, 0x6d, 0x36, 0xc0, 0xba, 0x9e, 0xe5, 0xd1, 0xe7, 0x4a, 0x45, 0x10, 0xe3, 0x0d,
	0xba, 0xb6, 0xf4, 0x4f, 0x02, 0x8a, 0x6d, 0xbb, 0xa3, 0x7b, 0x34, 0xd6, 0xa1, 0x5f, 0xed, 0x89,
	0x61, 0xb5, 0x8f, 0x77, 0xc7, 0xe4, 0x44, 0x77, 0xfc, 0xd8, 0x2d, 0x10, 0xa3, 0xd3, 0xfc, 0x7d,
	0xcf, 0x3f, 0x62, 0xdf, 0x4b, 0x7c, 0x09, 0x1f, 0x49, 0xaf, 0x60, 0x7b, 0xea, 0x06, 0x5d, 0x9b,
	0xdd, 0x5e, 0xfa, 0xf5, 0x35, 0x35, 0x3c, 0xca, 0xb7, 0x99, 0x52, 0xc3, 0xb1, 0xf4, 0x0e, 0xb6,
	0x46, 0x15, 0x10, 0x68, 0x9f, 0xa5, 0xe6, 0x51, 0xef, 0x94, 0xf0, 0xd6, 0x4e, 0x46, 0x6f, 0xed,
	0x9f, 0xa0, 0x38, 0xcd, 0xae, 0x6b, 0x93, 0xef, 0x22, 0x86, 0x23, 0xea, 0xda, 0x9a, 0xd8, 0x73,
	0xb8, 0x2a, 0xf4, 0xc9, 0x72, 0x26, 0x7d, 0x0f, 0x5b, 0xcd, 0xff, 0x15, 0xb5, 0xb4, 0x03, 0xc5,
	0xe6, 0xd4, 0xf8, 0xa4, 0x37, 0xb0, 0xd1, 0x1c, 0x5c, 0xf5, 0x4c, 0x9f, 0x20, 0xdb, 0x36, 0xd5,
	0xbb, 0xf1, 0x62, 0x19, 0xbe, 0x1f, 0x0c, 0xab, 0x43, 0xfd, 0x24, 0xf8, 0xef, 0x87, 0xb2, 0xd5,
	0xa1, 0xd2, 0x09, 0x14, 0xe2, 0x6c, 0xb9, 0x36, 0x11, 0x21, 0x43, 0x7f, 0x35, 0xdd, 0xe0, 0x58,
	0xb2, 0x6a, 0x30, 0x64, 0xfe, 0x55, 0xda, 0xb3, 0x6e, 0xe9, 0x47, 0xf0, 0x2f, 0x42, 0x21, 0xce,
	0x96, 0x6b, 0x1f, 0xfd, 0x9b, 0x80, 0x5c, 0x78, 0x75, 0x92, 0x02, 0x90, 0x53, 0xb9, 0xa6, 0xd5,
	0x1b, 0x5a, 0xbb, 0xd6, 0x6c, 0x28, 0xe5, 0xea, 0xeb, 0xaa, 0x52, 0x11, 0xe6, 0xc8, 0x1a, 0xac,
	0xf8, 0x38, 0xfb, 0xd3, 0x6e, 0x2a, 0xaa, 0x90, 0x20, 0x1b, 0xb0, 0x1a, 0x01, 0x2b, 0xca, 0xbb,
	0x6a, 0x59, 0x11, 0x92, 0x44, 0x84, 0xf5, 0x08, 0x5c, 0xbe, 0xa8, 0x2a, 0xb5, 0x96, 0x56, 0x6d,
	0x08, 0xf3, 0x64, 0x1d, 0x84, 0xc8, 0x4c, 0xe3, 0xbc, 0x5e, 0x53, 0x84, 0x2c, 0x29, 0x84, 0x66,
	0xda, 0xb5, 0xd0, 0xfa, 0x3d, 0xb3, 0xb3, 0x36, 0x82, 0xfb, 0x0e, 0xee, 0xe7, 0xc9, 0x36, 0x14,
	0x46, 0x66, 0x86, 0x3e, 0xee, 0xb3, 0x64, 0x33, 0xb2, 0x85, 0xa1, 0x9b, 0x7b, 0xe1, 0xe8, 0xb7,
	0x04, 0x90, 0xc9, 0x86, 0x4a, 0x0e, 0x60, 0xf7, 0x54, 0xae, 0xd5, 0x94, 0x8a, 0x56, 0x3e, 0x57,
	0xca, 0x6f, 0xd1, 0xbd, 0xd6, 0x92, 0xcf, 0xc6, 0x32, 0x20, 0xc1, 0x27, 0xf1, 0xb4, 0xf3, 0xea,
	0xd9, 0xb9, 0xd6, 0x90, 0x2f, 0x85, 0x04, 0xd9, 0x87, 0x52, 0x3c, 0xa7, 0xa1, 0xd6, 0x5f, 0x2b,
	0xcd, 0x66, 0xb5, 0x5e, 0x13, 0x92, 0x47, 0x77, 0x23, 0x4f, 0x19, 0xde, 0x45, 0xc9, 0x1e, 0x3c,
	0x1d, 0x59, 0xda, 0xac, 0xb7, 0xd5, 0xb2, 0x32, 0x16, 0xc3, 0x13, 0xd8, 0x8a, 0x23, 0xc9, 0xed,
	0x4a, 0xb5, 0x25, 0x24, 0x48, 0x09, 0x76, 0xe2, 0xa6, 0x9b, 0x2d, 0x55, 0x6e, 0x29, 0x67, 0x97,
	0x42, 0xf2, 0xe8, 0xef, 0x04, 0x6c, 0xc4, 0x5e, 0x48, 0xe4, 0x10, 0xf6, 0x47, 0xd6, 0xaa, 0x4a,
	0xb9, 0xae, 0x56, 0xb4, 0xd6, 0x65, 0x63, 0x3c, 0x88, 0xf1, 0x7c, 0x45, 0x99, 0xad, 0x3a, 0x07,
	0x63, 0x72, 0x11, 0xa5, 0x21, 0xa2, 0x54, 0x84, 0xe4, 0x83, 0xc6, 0x10, 0xa9, 0xd6, 0xce, 0x84,
	0xd4, 0x83, 0xc6, 0x2a, 0xca, 0x85, 0xd2, 0x52, 0x2a, 0xc2, 0xfc, 0xd1, 0x9f, 0x09, 0x58, 0x19,
	0xeb, 0xb0, 0x64, 0x17, 0x9e, 0x8c, 0xac, 0x8c, 0xd9, 0x50, 0x2c, 0xe5, 0xad, 0xa2, 0xa0, 0x4a,
	0x6b, 0x4a, 0x45, 0x48, 0x90, 0x4f, 0x61, 0x2f, 0xce, 0x8a, 0x8f, 0x9d, 0x5e, 0xfa, 0xbb, 0x4e,
	0xce, 0x26, 0xd6, 0x5b, 0xe7, 0x8a, 0x2a, 0xa4, 0x26, 0xe4, 0x84, 0x44, 0x55, 0x79, 0xa3, 0x94,
	0x5b, 0x9c, 0xcf, 0xf6, 0x93, 0x1c, 0x3b, 0xad, 0xb0, 0xbd, 0x4f, 0x9e, 0x56, 0xb3, 0x7d, 0xd1,
	0x7a, 0xdc, 0x69, 0x45, 0x99, 0xe1, 0x06, 0x9f, 0x81, 0x34, 0x95, 0x56, 0x3f, 0x65, 0x51, 0xa1,
	0x76, 0x1f, 0x74, 0x1c, 0x4d, 0x59, 0x8a, 0x1c, 0xc3, 0xd1, 0x4c, 0xc7, 0xc3, 0x84, 0xcc, 0x3f,
	0xc8, 0xaf, 0x37, 0x14, 0x55, 0x6e, 0xd5, 0xd5, 0x61, 0xc4, 0x0b, 0xe4, 0x0b, 0x78, 0x3e, 0x9b,
	0x1f, 0x0d, 0x29, 0x7d, 0xf2, 0xc7, 0x02, 0x5e, 0x75, 0x4d, 0xec, 0x2d, 0x44, 0x87, 0xf5, 0xb8,
	0x67, 0x0f, 0x91, 0x22, 0xfd, 0x67, 0xca, 0xbf, 0xce, 0xc5, 0xbd, 0x99, 0x1c, 0xd7, 0x96, 0xe6,
	0xc8, 0xcf, 0xb0, 0x39, 0xa5, 0x25, 0x93, 0x83, 0x88, 0x85, 0xe9, 0xef, 0x92, 0xe2, 0xb3, 0xc7,
	0xd0, 0xd0, 0x97, 0x0e, 0xeb, 0x71, 0x8f, 0x9f, 0x91, 0xed, 0x4c, 0x79, 0x6e, 0x8d, 0x6c, 0x67,
	0xea, 0x0b, 0x6a, 0x8e, 0xdc, 0x40, 0x21, 0xbe, 0x9d, 0x93, 0xfd, 0xa9, 0xf9, 0x88, 0xf4, 0xe4,
	0xe2, 0xc1, 0x23, 0x58, 0x81, 0xa3, 0xe6, 0x6c, 0x47, 0xcd, 0x47, 0x39, 0x6a, 0x3e, 0xe4, 0xe8,
	0x47, 0x20, 0x93, 0x6d, 0x99, 0x44, 0x9f, 0x6f, 0xb1, 0x2f, 0x80, 0xe2, 0xee, 0x0c, 0x46, 0x60,
	0x7c, 0xb2, 0xe7, 0x8e, 0x18, 0x8f, 0x6d, 0xef, 0x23, 0xc6, 0xe3, 0x9b, 0xb6, 0x34, 0x77, 0xfa,
	0xfc, 0x87, 0xcf, 0x6e, 0xac, 0xae, 0xde, 0xbf, 0x39, 0xfe, 0xea, 0xc4, 0xf3, 0x8e, 0x0d, 0xab,
	0xf7, 0x02, 0x7f, 0x73, 0x32, 0xac, 0xee, 0x0b, 0xb6, 0xd6, 0x34, 0xa8, 0x3b, 0xfc, 0x3d, 0xea,
	0x2a, 0x8d, 0x93, 0x5f, 0xfe, 0x17, 0x00, 0x00, 0xff, 0xff, 0x5c, 0x3d, 0x8e, 0x8e, 0xad, 0x12,
	0x00, 0x00,
}
