// Code generated by protoc-gen-gogo.
// source: src/checkinsvr/checkin.proto
// DO NOT EDIT!

/*
	Package checkin is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/checkinsvr/checkin.proto

	It has these top-level messages:
		CheckInCommonAward
		CheckInBriefConfig
		CheckInCommonConfig
		CheckInDetailInfo
		DoCheckInReq
		GetUserCheckInTimeReq
		GetUserCheckInTimeResp
		GetUserCheckInDetailResp
		FetchAwardReq
		UserAwardInfo
		UserDelayAwardInfo
		GetAwardHistoryReq
		GetAwardHistoryResp
		GetUserAwardByUidReq
		GetUserAwardByUidResp
		GetUserAwardByDevIdReq
		GetUserAwardByDevIdResp
		AddDelayAwardReq
		UserDailyCheckInInfo
		LoginCheckInMissionConfig
		GetDailyCheckInReq
		GetDailyCheckInResp
		DoDailyCheckInReq
		DoDailyCheckInResp
		GetCheckInCommonCfgListReq
		GetCheckInCommonCfgListResp
		GetCheckInCommonCfgByIdReq
		GetCheckInCommonCfgByIdResp
		UpdateCheckInCommonAwardReq
		UpdateCheckInCommonAwardResp
		UpdateCheckInBriefCfgReq
		UpdateCheckInBriefCfgResp
		FirstRechargeAwardReq
		FirstRechargeAwardResp
*/
package checkin

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CheckInType int32

const (
	CheckInType_checkin_none            CheckInType = 0
	CheckInType_game_conglin            CheckInType = 1
	CheckInType_cf_match                CheckInType = 2
	CheckInType_game_zhongchao          CheckInType = 3
	CheckInType_cf_giftpkg              CheckInType = 4
	CheckInType_game_id5                CheckInType = 5
	CheckInType_aliveness_check         CheckInType = 6
	CheckInType_tbean_first_recharge    CheckInType = 7
	CheckInType_chat_anniversary        CheckInType = 8
	CheckInType_cf_ten_anniversary      CheckInType = 9
	CheckInType_mid_autumn_recharge     CheckInType = 10
	CheckInType_mid_autumn_recharge_new CheckInType = 11
	// 1000000以上的是每日签到(DailyCheckInType)，用于读取奖励配置
	CheckInType_daily_checkin_begin CheckInType = 1000000
	// 2000000以上的是虚拟的签到类型，仅用于读取奖励配置
	CheckInType_act_daily_checkin_login CheckInType = 2000001
)

var CheckInType_name = map[int32]string{
	0:       "checkin_none",
	1:       "game_conglin",
	2:       "cf_match",
	3:       "game_zhongchao",
	4:       "cf_giftpkg",
	5:       "game_id5",
	6:       "aliveness_check",
	7:       "tbean_first_recharge",
	8:       "chat_anniversary",
	9:       "cf_ten_anniversary",
	10:      "mid_autumn_recharge",
	11:      "mid_autumn_recharge_new",
	1000000: "daily_checkin_begin",
	2000001: "act_daily_checkin_login",
}
var CheckInType_value = map[string]int32{
	"checkin_none":            0,
	"game_conglin":            1,
	"cf_match":                2,
	"game_zhongchao":          3,
	"cf_giftpkg":              4,
	"game_id5":                5,
	"aliveness_check":         6,
	"tbean_first_recharge":    7,
	"chat_anniversary":        8,
	"cf_ten_anniversary":      9,
	"mid_autumn_recharge":     10,
	"mid_autumn_recharge_new": 11,
	"daily_checkin_begin":     1000000,
	"act_daily_checkin_login": 2000001,
}

func (x CheckInType) String() string {
	return proto.EnumName(CheckInType_name, int32(x))
}
func (CheckInType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{0} }

type AwardIdType int32

const (
	AwardIdType_award_none                 AwardIdType = 0
	AwardIdType_sign                       AwardIdType = 1
	AwardIdType_grab                       AwardIdType = 2
	AwardIdType_channel                    AwardIdType = 3
	AwardIdType_live                       AwardIdType = 4
	AwardIdType_cf_new                     AwardIdType = 5
	AwardIdType_cf_sign                    AwardIdType = 6
	AwardIdType_cf_share                   AwardIdType = 7
	AwardIdType_id5_channel_online_invitee AwardIdType = 8
	AwardIdType_id5_channel_online_inviter AwardIdType = 9
	AwardIdType_id5_quickmatch             AwardIdType = 10
	AwardIdType_id5_new_invite             AwardIdType = 11
	AwardIdType_tbean_recharge_low_gift    AwardIdType = 12
	AwardIdType_tbean_recharge_high_gift   AwardIdType = 13
)

var AwardIdType_name = map[int32]string{
	0:  "award_none",
	1:  "sign",
	2:  "grab",
	3:  "channel",
	4:  "live",
	5:  "cf_new",
	6:  "cf_sign",
	7:  "cf_share",
	8:  "id5_channel_online_invitee",
	9:  "id5_channel_online_inviter",
	10: "id5_quickmatch",
	11: "id5_new_invite",
	12: "tbean_recharge_low_gift",
	13: "tbean_recharge_high_gift",
}
var AwardIdType_value = map[string]int32{
	"award_none":                 0,
	"sign":                       1,
	"grab":                       2,
	"channel":                    3,
	"live":                       4,
	"cf_new":                     5,
	"cf_sign":                    6,
	"cf_share":                   7,
	"id5_channel_online_invitee": 8,
	"id5_channel_online_inviter": 9,
	"id5_quickmatch":             10,
	"id5_new_invite":             11,
	"tbean_recharge_low_gift":    12,
	"tbean_recharge_high_gift":   13,
}

func (x AwardIdType) String() string {
	return proto.EnumName(AwardIdType_name, int32(x))
}
func (AwardIdType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{1} }

// 《第五人格》
type GameId5CheckInFlag int32

const (
	GameId5CheckInFlag_id5_flag_none           GameId5CheckInFlag = 0
	GameId5CheckInFlag_id5_flag_hold_mic       GameId5CheckInFlag = 1
	GameId5CheckInFlag_id5_flag_channel_online GameId5CheckInFlag = 2
)

var GameId5CheckInFlag_name = map[int32]string{
	0: "id5_flag_none",
	1: "id5_flag_hold_mic",
	2: "id5_flag_channel_online",
}
var GameId5CheckInFlag_value = map[string]int32{
	"id5_flag_none":           0,
	"id5_flag_hold_mic":       1,
	"id5_flag_channel_online": 2,
}

func (x GameId5CheckInFlag) String() string {
	return proto.EnumName(GameId5CheckInFlag_name, int32(x))
}
func (GameId5CheckInFlag) EnumDescriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{2} }

// 每日签到
type DailyCheckInType int32

const (
	DailyCheckInType_daily_checkin_none  DailyCheckInType = 0
	DailyCheckInType_daily_checkin_login DailyCheckInType = 1
)

var DailyCheckInType_name = map[int32]string{
	0: "daily_checkin_none",
	1: "daily_checkin_login",
}
var DailyCheckInType_value = map[string]int32{
	"daily_checkin_none":  0,
	"daily_checkin_login": 1,
}

func (x DailyCheckInType) String() string {
	return proto.EnumName(DailyCheckInType_name, int32(x))
}
func (DailyCheckInType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{3} }

// 签到奖励类型
type CheckInCommonAwardType int32

const (
	CheckInCommonAwardType_Common_Award_None          CheckInCommonAwardType = 0
	CheckInCommonAwardType_Common_Award_Backpack_Item CheckInCommonAwardType = 1
	CheckInCommonAwardType_Common_Award_Headwear      CheckInCommonAwardType = 2
	CheckInCommonAwardType_Common_Award_Decoration    CheckInCommonAwardType = 3
	CheckInCommonAwardType_Common_Award_Red_Diamond   CheckInCommonAwardType = 4
	CheckInCommonAwardType_Common_Award_Exp           CheckInCommonAwardType = 5
	CheckInCommonAwardType_Common_Award_Medal         CheckInCommonAwardType = 6
)

var CheckInCommonAwardType_name = map[int32]string{
	0: "Common_Award_None",
	1: "Common_Award_Backpack_Item",
	2: "Common_Award_Headwear",
	3: "Common_Award_Decoration",
	4: "Common_Award_Red_Diamond",
	5: "Common_Award_Exp",
	6: "Common_Award_Medal",
}
var CheckInCommonAwardType_value = map[string]int32{
	"Common_Award_None":          0,
	"Common_Award_Backpack_Item": 1,
	"Common_Award_Headwear":      2,
	"Common_Award_Decoration":    3,
	"Common_Award_Red_Diamond":   4,
	"Common_Award_Exp":           5,
	"Common_Award_Medal":         6,
}

func (x CheckInCommonAwardType) String() string {
	return proto.EnumName(CheckInCommonAwardType_name, int32(x))
}
func (CheckInCommonAwardType) EnumDescriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{4} }

type LoginCheckInMissionConfig_LoginExtraAwardType int32

const (
	LoginCheckInMissionConfig_Extra_Award_None          LoginCheckInMissionConfig_LoginExtraAwardType = 0
	LoginCheckInMissionConfig_Extra_Award_Backpack_Item LoginCheckInMissionConfig_LoginExtraAwardType = 1
)

var LoginCheckInMissionConfig_LoginExtraAwardType_name = map[int32]string{
	0: "Extra_Award_None",
	1: "Extra_Award_Backpack_Item",
}
var LoginCheckInMissionConfig_LoginExtraAwardType_value = map[string]int32{
	"Extra_Award_None":          0,
	"Extra_Award_Backpack_Item": 1,
}

func (x LoginCheckInMissionConfig_LoginExtraAwardType) String() string {
	return proto.EnumName(LoginCheckInMissionConfig_LoginExtraAwardType_name, int32(x))
}
func (LoginCheckInMissionConfig_LoginExtraAwardType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{19, 0}
}

// 通用签到奖励
type CheckInCommonAward struct {
	CheckinType uint32 `protobuf:"varint,1,opt,name=checkin_type,json=checkinType,proto3" json:"checkin_type,omitempty"`
	SubId       uint32 `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`
	AwardType   uint32 `protobuf:"varint,3,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	AwardId     uint32 `protobuf:"varint,4,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	StrAwardId  string `protobuf:"bytes,5,opt,name=str_award_id,json=strAwardId,proto3" json:"str_award_id,omitempty"`
	AwardNum    uint32 `protobuf:"varint,6,opt,name=award_num,json=awardNum,proto3" json:"award_num,omitempty"`
	AwardTime   uint32 `protobuf:"varint,7,opt,name=award_time,json=awardTime,proto3" json:"award_time,omitempty"`
	UniqId      uint32 `protobuf:"varint,8,opt,name=uniq_id,json=uniqId,proto3" json:"uniq_id,omitempty"`
}

func (m *CheckInCommonAward) Reset()                    { *m = CheckInCommonAward{} }
func (m *CheckInCommonAward) String() string            { return proto.CompactTextString(m) }
func (*CheckInCommonAward) ProtoMessage()               {}
func (*CheckInCommonAward) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{0} }

func (m *CheckInCommonAward) GetCheckinType() uint32 {
	if m != nil {
		return m.CheckinType
	}
	return 0
}

func (m *CheckInCommonAward) GetSubId() uint32 {
	if m != nil {
		return m.SubId
	}
	return 0
}

func (m *CheckInCommonAward) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *CheckInCommonAward) GetAwardId() uint32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *CheckInCommonAward) GetStrAwardId() string {
	if m != nil {
		return m.StrAwardId
	}
	return ""
}

func (m *CheckInCommonAward) GetAwardNum() uint32 {
	if m != nil {
		return m.AwardNum
	}
	return 0
}

func (m *CheckInCommonAward) GetAwardTime() uint32 {
	if m != nil {
		return m.AwardTime
	}
	return 0
}

func (m *CheckInCommonAward) GetUniqId() uint32 {
	if m != nil {
		return m.UniqId
	}
	return 0
}

// 签到配置
type CheckInBriefConfig struct {
	CheckinType    uint32 `protobuf:"varint,1,opt,name=checkin_type,json=checkinType,proto3" json:"checkin_type,omitempty"`
	SubId          uint32 `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`
	IconUrl        string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	AwardIconUrl   string `protobuf:"bytes,4,opt,name=award_icon_url,json=awardIconUrl,proto3" json:"award_icon_url,omitempty"`
	AwardMsg       string `protobuf:"bytes,5,opt,name=award_msg,json=awardMsg,proto3" json:"award_msg,omitempty"`
	BackgroundUrl  string `protobuf:"bytes,6,opt,name=background_url,json=backgroundUrl,proto3" json:"background_url,omitempty"`
	AwardButtonUrl string `protobuf:"bytes,7,opt,name=award_button_url,json=awardButtonUrl,proto3" json:"award_button_url,omitempty"`
	MsgColor       string `protobuf:"bytes,8,opt,name=msg_color,json=msgColor,proto3" json:"msg_color,omitempty"`
}

func (m *CheckInBriefConfig) Reset()                    { *m = CheckInBriefConfig{} }
func (m *CheckInBriefConfig) String() string            { return proto.CompactTextString(m) }
func (*CheckInBriefConfig) ProtoMessage()               {}
func (*CheckInBriefConfig) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{1} }

func (m *CheckInBriefConfig) GetCheckinType() uint32 {
	if m != nil {
		return m.CheckinType
	}
	return 0
}

func (m *CheckInBriefConfig) GetSubId() uint32 {
	if m != nil {
		return m.SubId
	}
	return 0
}

func (m *CheckInBriefConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

func (m *CheckInBriefConfig) GetAwardIconUrl() string {
	if m != nil {
		return m.AwardIconUrl
	}
	return ""
}

func (m *CheckInBriefConfig) GetAwardMsg() string {
	if m != nil {
		return m.AwardMsg
	}
	return ""
}

func (m *CheckInBriefConfig) GetBackgroundUrl() string {
	if m != nil {
		return m.BackgroundUrl
	}
	return ""
}

func (m *CheckInBriefConfig) GetAwardButtonUrl() string {
	if m != nil {
		return m.AwardButtonUrl
	}
	return ""
}

func (m *CheckInBriefConfig) GetMsgColor() string {
	if m != nil {
		return m.MsgColor
	}
	return ""
}

type CheckInCommonConfig struct {
	BriefCfg  *CheckInBriefConfig   `protobuf:"bytes,1,opt,name=brief_cfg,json=briefCfg" json:"brief_cfg,omitempty"`
	AwardList []*CheckInCommonAward `protobuf:"bytes,2,rep,name=award_list,json=awardList" json:"award_list,omitempty"`
}

func (m *CheckInCommonConfig) Reset()                    { *m = CheckInCommonConfig{} }
func (m *CheckInCommonConfig) String() string            { return proto.CompactTextString(m) }
func (*CheckInCommonConfig) ProtoMessage()               {}
func (*CheckInCommonConfig) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{2} }

func (m *CheckInCommonConfig) GetBriefCfg() *CheckInBriefConfig {
	if m != nil {
		return m.BriefCfg
	}
	return nil
}

func (m *CheckInCommonConfig) GetAwardList() []*CheckInCommonAward {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type CheckInDetailInfo struct {
	CheckInTime  uint32 `protobuf:"varint,1,opt,name=check_in_time,json=checkInTime,proto3" json:"check_in_time,omitempty"`
	CheckInValue uint32 `protobuf:"varint,2,opt,name=check_in_value,json=checkInValue,proto3" json:"check_in_value,omitempty"`
}

func (m *CheckInDetailInfo) Reset()                    { *m = CheckInDetailInfo{} }
func (m *CheckInDetailInfo) String() string            { return proto.CompactTextString(m) }
func (*CheckInDetailInfo) ProtoMessage()               {}
func (*CheckInDetailInfo) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{3} }

func (m *CheckInDetailInfo) GetCheckInTime() uint32 {
	if m != nil {
		return m.CheckInTime
	}
	return 0
}

func (m *CheckInDetailInfo) GetCheckInValue() uint32 {
	if m != nil {
		return m.CheckInValue
	}
	return 0
}

type DoCheckInReq struct {
	Uid          uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType  uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	CheckInValue uint32 `protobuf:"varint,3,opt,name=check_in_value,json=checkInValue,proto3" json:"check_in_value,omitempty"`
	DeviceId     string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
}

func (m *DoCheckInReq) Reset()                    { *m = DoCheckInReq{} }
func (m *DoCheckInReq) String() string            { return proto.CompactTextString(m) }
func (*DoCheckInReq) ProtoMessage()               {}
func (*DoCheckInReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{4} }

func (m *DoCheckInReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoCheckInReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *DoCheckInReq) GetCheckInValue() uint32 {
	if m != nil {
		return m.CheckInValue
	}
	return 0
}

func (m *DoCheckInReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

type GetUserCheckInTimeReq struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
}

func (m *GetUserCheckInTimeReq) Reset()                    { *m = GetUserCheckInTimeReq{} }
func (m *GetUserCheckInTimeReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserCheckInTimeReq) ProtoMessage()               {}
func (*GetUserCheckInTimeReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{5} }

func (m *GetUserCheckInTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserCheckInTimeReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

type GetUserCheckInTimeResp struct {
	TimeList []uint32 `protobuf:"varint,1,rep,packed,name=time_list,json=timeList" json:"time_list,omitempty"`
}

func (m *GetUserCheckInTimeResp) Reset()                    { *m = GetUserCheckInTimeResp{} }
func (m *GetUserCheckInTimeResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCheckInTimeResp) ProtoMessage()               {}
func (*GetUserCheckInTimeResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{6} }

func (m *GetUserCheckInTimeResp) GetTimeList() []uint32 {
	if m != nil {
		return m.TimeList
	}
	return nil
}

type GetUserCheckInDetailResp struct {
	TimeList []*CheckInDetailInfo `protobuf:"bytes,1,rep,name=time_list,json=timeList" json:"time_list,omitempty"`
}

func (m *GetUserCheckInDetailResp) Reset()                    { *m = GetUserCheckInDetailResp{} }
func (m *GetUserCheckInDetailResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserCheckInDetailResp) ProtoMessage()               {}
func (*GetUserCheckInDetailResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{7} }

func (m *GetUserCheckInDetailResp) GetTimeList() []*CheckInDetailInfo {
	if m != nil {
		return m.TimeList
	}
	return nil
}

type FetchAwardReq struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	AwardId     uint32 `protobuf:"varint,3,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	DeviceId    string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	Status      uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
}

func (m *FetchAwardReq) Reset()                    { *m = FetchAwardReq{} }
func (m *FetchAwardReq) String() string            { return proto.CompactTextString(m) }
func (*FetchAwardReq) ProtoMessage()               {}
func (*FetchAwardReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{8} }

func (m *FetchAwardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FetchAwardReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *FetchAwardReq) GetAwardId() uint32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *FetchAwardReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *FetchAwardReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type UserAwardInfo struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	AwardId     uint32 `protobuf:"varint,3,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	FetchTime   uint32 `protobuf:"varint,4,opt,name=fetch_time,json=fetchTime,proto3" json:"fetch_time,omitempty"`
}

func (m *UserAwardInfo) Reset()                    { *m = UserAwardInfo{} }
func (m *UserAwardInfo) String() string            { return proto.CompactTextString(m) }
func (*UserAwardInfo) ProtoMessage()               {}
func (*UserAwardInfo) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{9} }

func (m *UserAwardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserAwardInfo) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *UserAwardInfo) GetAwardId() uint32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *UserAwardInfo) GetFetchTime() uint32 {
	if m != nil {
		return m.FetchTime
	}
	return 0
}

type UserDelayAwardInfo struct {
	Uid            uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType    uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	AwardId        uint32 `protobuf:"varint,3,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	DeviceId       string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	UniqId         uint32 `protobuf:"varint,5,opt,name=uniq_id,json=uniqId,proto3" json:"uniq_id,omitempty"`
	DelayFetchTime uint32 `protobuf:"varint,6,opt,name=delay_fetch_time,json=delayFetchTime,proto3" json:"delay_fetch_time,omitempty"`
	TbeanNum       uint32 `protobuf:"varint,7,opt,name=tbean_num,json=tbeanNum,proto3" json:"tbean_num,omitempty"`
}

func (m *UserDelayAwardInfo) Reset()                    { *m = UserDelayAwardInfo{} }
func (m *UserDelayAwardInfo) String() string            { return proto.CompactTextString(m) }
func (*UserDelayAwardInfo) ProtoMessage()               {}
func (*UserDelayAwardInfo) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{10} }

func (m *UserDelayAwardInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDelayAwardInfo) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *UserDelayAwardInfo) GetAwardId() uint32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *UserDelayAwardInfo) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *UserDelayAwardInfo) GetUniqId() uint32 {
	if m != nil {
		return m.UniqId
	}
	return 0
}

func (m *UserDelayAwardInfo) GetDelayFetchTime() uint32 {
	if m != nil {
		return m.DelayFetchTime
	}
	return 0
}

func (m *UserDelayAwardInfo) GetTbeanNum() uint32 {
	if m != nil {
		return m.TbeanNum
	}
	return 0
}

type GetAwardHistoryReq struct {
	CheckInType uint32 `protobuf:"varint,1,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	Limit       uint32 `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
}

func (m *GetAwardHistoryReq) Reset()                    { *m = GetAwardHistoryReq{} }
func (m *GetAwardHistoryReq) String() string            { return proto.CompactTextString(m) }
func (*GetAwardHistoryReq) ProtoMessage()               {}
func (*GetAwardHistoryReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{11} }

func (m *GetAwardHistoryReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *GetAwardHistoryReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAwardHistoryResp struct {
	HistoryList []*UserAwardInfo `protobuf:"bytes,1,rep,name=history_list,json=historyList" json:"history_list,omitempty"`
}

func (m *GetAwardHistoryResp) Reset()                    { *m = GetAwardHistoryResp{} }
func (m *GetAwardHistoryResp) String() string            { return proto.CompactTextString(m) }
func (*GetAwardHistoryResp) ProtoMessage()               {}
func (*GetAwardHistoryResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{12} }

func (m *GetAwardHistoryResp) GetHistoryList() []*UserAwardInfo {
	if m != nil {
		return m.HistoryList
	}
	return nil
}

type GetUserAwardByUidReq struct {
	Uid         uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
}

func (m *GetUserAwardByUidReq) Reset()                    { *m = GetUserAwardByUidReq{} }
func (m *GetUserAwardByUidReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserAwardByUidReq) ProtoMessage()               {}
func (*GetUserAwardByUidReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{13} }

func (m *GetUserAwardByUidReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserAwardByUidReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

type GetUserAwardByUidResp struct {
	AwardList []*UserAwardInfo `protobuf:"bytes,1,rep,name=award_list,json=awardList" json:"award_list,omitempty"`
}

func (m *GetUserAwardByUidResp) Reset()                    { *m = GetUserAwardByUidResp{} }
func (m *GetUserAwardByUidResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserAwardByUidResp) ProtoMessage()               {}
func (*GetUserAwardByUidResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{14} }

func (m *GetUserAwardByUidResp) GetAwardList() []*UserAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type GetUserAwardByDevIdReq struct {
	DeviceId    string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	CheckInType uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
}

func (m *GetUserAwardByDevIdReq) Reset()                    { *m = GetUserAwardByDevIdReq{} }
func (m *GetUserAwardByDevIdReq) String() string            { return proto.CompactTextString(m) }
func (*GetUserAwardByDevIdReq) ProtoMessage()               {}
func (*GetUserAwardByDevIdReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{15} }

func (m *GetUserAwardByDevIdReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *GetUserAwardByDevIdReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

type GetUserAwardByDevIdResp struct {
	AwardList []*UserAwardInfo `protobuf:"bytes,1,rep,name=award_list,json=awardList" json:"award_list,omitempty"`
}

func (m *GetUserAwardByDevIdResp) Reset()                    { *m = GetUserAwardByDevIdResp{} }
func (m *GetUserAwardByDevIdResp) String() string            { return proto.CompactTextString(m) }
func (*GetUserAwardByDevIdResp) ProtoMessage()               {}
func (*GetUserAwardByDevIdResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{16} }

func (m *GetUserAwardByDevIdResp) GetAwardList() []*UserAwardInfo {
	if m != nil {
		return m.AwardList
	}
	return nil
}

type AddDelayAwardReq struct {
	Uid              uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CheckInType      uint32 `protobuf:"varint,2,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	AwardId          uint32 `protobuf:"varint,3,opt,name=award_id,json=awardId,proto3" json:"award_id,omitempty"`
	DeviceId         string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	DelayFetchSecond uint32 `protobuf:"varint,5,opt,name=delay_fetch_second,json=delayFetchSecond,proto3" json:"delay_fetch_second,omitempty"`
}

func (m *AddDelayAwardReq) Reset()                    { *m = AddDelayAwardReq{} }
func (m *AddDelayAwardReq) String() string            { return proto.CompactTextString(m) }
func (*AddDelayAwardReq) ProtoMessage()               {}
func (*AddDelayAwardReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{17} }

func (m *AddDelayAwardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddDelayAwardReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *AddDelayAwardReq) GetAwardId() uint32 {
	if m != nil {
		return m.AwardId
	}
	return 0
}

func (m *AddDelayAwardReq) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *AddDelayAwardReq) GetDelayFetchSecond() uint32 {
	if m != nil {
		return m.DelayFetchSecond
	}
	return 0
}

type UserDailyCheckInInfo struct {
	Uid             uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type            uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
	LastCheckinAt   uint32 `protobuf:"varint,3,opt,name=last_checkin_at,json=lastCheckinAt,proto3" json:"last_checkin_at,omitempty"`
	ContCheckinDays uint32 `protobuf:"varint,4,opt,name=cont_checkin_days,json=contCheckinDays,proto3" json:"cont_checkin_days,omitempty"`
}

func (m *UserDailyCheckInInfo) Reset()                    { *m = UserDailyCheckInInfo{} }
func (m *UserDailyCheckInInfo) String() string            { return proto.CompactTextString(m) }
func (*UserDailyCheckInInfo) ProtoMessage()               {}
func (*UserDailyCheckInInfo) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{18} }

func (m *UserDailyCheckInInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserDailyCheckInInfo) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *UserDailyCheckInInfo) GetLastCheckinAt() uint32 {
	if m != nil {
		return m.LastCheckinAt
	}
	return 0
}

func (m *UserDailyCheckInInfo) GetContCheckinDays() uint32 {
	if m != nil {
		return m.ContCheckinDays
	}
	return 0
}

// tt登录签到
type LoginCheckInMissionConfig struct {
	Type                uint32 `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	Exp                 uint32 `protobuf:"varint,2,opt,name=exp,proto3" json:"exp,omitempty"`
	RedDiamond          uint32 `protobuf:"varint,3,opt,name=red_diamond,json=redDiamond,proto3" json:"red_diamond,omitempty"`
	ContDays            uint32 `protobuf:"varint,4,opt,name=cont_days,json=contDays,proto3" json:"cont_days,omitempty"`
	ExtraAwardType      uint32 `protobuf:"varint,5,opt,name=extra_award_type,json=extraAwardType,proto3" json:"extra_award_type,omitempty"`
	ExtraAwardItemId    uint32 `protobuf:"varint,6,opt,name=extra_award_item_id,json=extraAwardItemId,proto3" json:"extra_award_item_id,omitempty"`
	ExtraAwardItemCount uint32 `protobuf:"varint,7,opt,name=extra_award_item_count,json=extraAwardItemCount,proto3" json:"extra_award_item_count,omitempty"`
	ExtraAwardItemUrl   string `protobuf:"bytes,8,opt,name=extra_award_item_url,json=extraAwardItemUrl,proto3" json:"extra_award_item_url,omitempty"`
	IconUrl             string `protobuf:"bytes,9,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
}

func (m *LoginCheckInMissionConfig) Reset()         { *m = LoginCheckInMissionConfig{} }
func (m *LoginCheckInMissionConfig) String() string { return proto.CompactTextString(m) }
func (*LoginCheckInMissionConfig) ProtoMessage()    {}
func (*LoginCheckInMissionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{19}
}

func (m *LoginCheckInMissionConfig) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetExp() uint32 {
	if m != nil {
		return m.Exp
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetRedDiamond() uint32 {
	if m != nil {
		return m.RedDiamond
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetContDays() uint32 {
	if m != nil {
		return m.ContDays
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetExtraAwardType() uint32 {
	if m != nil {
		return m.ExtraAwardType
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetExtraAwardItemId() uint32 {
	if m != nil {
		return m.ExtraAwardItemId
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetExtraAwardItemCount() uint32 {
	if m != nil {
		return m.ExtraAwardItemCount
	}
	return 0
}

func (m *LoginCheckInMissionConfig) GetExtraAwardItemUrl() string {
	if m != nil {
		return m.ExtraAwardItemUrl
	}
	return ""
}

func (m *LoginCheckInMissionConfig) GetIconUrl() string {
	if m != nil {
		return m.IconUrl
	}
	return ""
}

// 每日签到
type GetDailyCheckInReq struct {
	Uid  uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (m *GetDailyCheckInReq) Reset()                    { *m = GetDailyCheckInReq{} }
func (m *GetDailyCheckInReq) String() string            { return proto.CompactTextString(m) }
func (*GetDailyCheckInReq) ProtoMessage()               {}
func (*GetDailyCheckInReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{20} }

func (m *GetDailyCheckInReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetDailyCheckInReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetDailyCheckInResp struct {
	CheckinInfo *UserDailyCheckInInfo `protobuf:"bytes,1,opt,name=checkin_info,json=checkinInfo" json:"checkin_info,omitempty"`
}

func (m *GetDailyCheckInResp) Reset()                    { *m = GetDailyCheckInResp{} }
func (m *GetDailyCheckInResp) String() string            { return proto.CompactTextString(m) }
func (*GetDailyCheckInResp) ProtoMessage()               {}
func (*GetDailyCheckInResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{21} }

func (m *GetDailyCheckInResp) GetCheckinInfo() *UserDailyCheckInInfo {
	if m != nil {
		return m.CheckinInfo
	}
	return nil
}

type DoDailyCheckInReq struct {
	Uid  uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Type uint32 `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"`
}

func (m *DoDailyCheckInReq) Reset()                    { *m = DoDailyCheckInReq{} }
func (m *DoDailyCheckInReq) String() string            { return proto.CompactTextString(m) }
func (*DoDailyCheckInReq) ProtoMessage()               {}
func (*DoDailyCheckInReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{22} }

func (m *DoDailyCheckInReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DoDailyCheckInReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type DoDailyCheckInResp struct {
	CheckinInfo   *UserDailyCheckInInfo `protobuf:"bytes,1,opt,name=checkin_info,json=checkinInfo" json:"checkin_info,omitempty"`
	AwardInfo     []byte                `protobuf:"bytes,2,opt,name=award_info,json=awardInfo,proto3" json:"award_info,omitempty"`
	AlreadyNotify bool                  `protobuf:"varint,3,opt,name=already_notify,json=alreadyNotify,proto3" json:"already_notify,omitempty"`
}

func (m *DoDailyCheckInResp) Reset()                    { *m = DoDailyCheckInResp{} }
func (m *DoDailyCheckInResp) String() string            { return proto.CompactTextString(m) }
func (*DoDailyCheckInResp) ProtoMessage()               {}
func (*DoDailyCheckInResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{23} }

func (m *DoDailyCheckInResp) GetCheckinInfo() *UserDailyCheckInInfo {
	if m != nil {
		return m.CheckinInfo
	}
	return nil
}

func (m *DoDailyCheckInResp) GetAwardInfo() []byte {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

func (m *DoDailyCheckInResp) GetAlreadyNotify() bool {
	if m != nil {
		return m.AlreadyNotify
	}
	return false
}

// 获取签到配置列表
type GetCheckInCommonCfgListReq struct {
	CheckInType uint32 `protobuf:"varint,1,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
}

func (m *GetCheckInCommonCfgListReq) Reset()         { *m = GetCheckInCommonCfgListReq{} }
func (m *GetCheckInCommonCfgListReq) String() string { return proto.CompactTextString(m) }
func (*GetCheckInCommonCfgListReq) ProtoMessage()    {}
func (*GetCheckInCommonCfgListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{24}
}

func (m *GetCheckInCommonCfgListReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

type GetCheckInCommonCfgListResp struct {
	CfgList []*CheckInCommonConfig `protobuf:"bytes,1,rep,name=cfg_list,json=cfgList" json:"cfg_list,omitempty"`
}

func (m *GetCheckInCommonCfgListResp) Reset()         { *m = GetCheckInCommonCfgListResp{} }
func (m *GetCheckInCommonCfgListResp) String() string { return proto.CompactTextString(m) }
func (*GetCheckInCommonCfgListResp) ProtoMessage()    {}
func (*GetCheckInCommonCfgListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{25}
}

func (m *GetCheckInCommonCfgListResp) GetCfgList() []*CheckInCommonConfig {
	if m != nil {
		return m.CfgList
	}
	return nil
}

type GetCheckInCommonCfgByIdReq struct {
	CheckInType uint32 `protobuf:"varint,1,opt,name=check_in_type,json=checkInType,proto3" json:"check_in_type,omitempty"`
	SubId       uint32 `protobuf:"varint,2,opt,name=sub_id,json=subId,proto3" json:"sub_id,omitempty"`
}

func (m *GetCheckInCommonCfgByIdReq) Reset()         { *m = GetCheckInCommonCfgByIdReq{} }
func (m *GetCheckInCommonCfgByIdReq) String() string { return proto.CompactTextString(m) }
func (*GetCheckInCommonCfgByIdReq) ProtoMessage()    {}
func (*GetCheckInCommonCfgByIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{26}
}

func (m *GetCheckInCommonCfgByIdReq) GetCheckInType() uint32 {
	if m != nil {
		return m.CheckInType
	}
	return 0
}

func (m *GetCheckInCommonCfgByIdReq) GetSubId() uint32 {
	if m != nil {
		return m.SubId
	}
	return 0
}

type GetCheckInCommonCfgByIdResp struct {
	CfgInfo *CheckInCommonConfig `protobuf:"bytes,1,opt,name=cfg_info,json=cfgInfo" json:"cfg_info,omitempty"`
}

func (m *GetCheckInCommonCfgByIdResp) Reset()         { *m = GetCheckInCommonCfgByIdResp{} }
func (m *GetCheckInCommonCfgByIdResp) String() string { return proto.CompactTextString(m) }
func (*GetCheckInCommonCfgByIdResp) ProtoMessage()    {}
func (*GetCheckInCommonCfgByIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{27}
}

func (m *GetCheckInCommonCfgByIdResp) GetCfgInfo() *CheckInCommonConfig {
	if m != nil {
		return m.CfgInfo
	}
	return nil
}

// 更新签到奖励
type UpdateCheckInCommonAwardReq struct {
	AwardInfo *CheckInCommonAward `protobuf:"bytes,1,opt,name=award_info,json=awardInfo" json:"award_info,omitempty"`
}

func (m *UpdateCheckInCommonAwardReq) Reset()         { *m = UpdateCheckInCommonAwardReq{} }
func (m *UpdateCheckInCommonAwardReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCheckInCommonAwardReq) ProtoMessage()    {}
func (*UpdateCheckInCommonAwardReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{28}
}

func (m *UpdateCheckInCommonAwardReq) GetAwardInfo() *CheckInCommonAward {
	if m != nil {
		return m.AwardInfo
	}
	return nil
}

type UpdateCheckInCommonAwardResp struct {
}

func (m *UpdateCheckInCommonAwardResp) Reset()         { *m = UpdateCheckInCommonAwardResp{} }
func (m *UpdateCheckInCommonAwardResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCheckInCommonAwardResp) ProtoMessage()    {}
func (*UpdateCheckInCommonAwardResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{29}
}

// 更新签到配置
type UpdateCheckInBriefCfgReq struct {
	CfgInfo *CheckInBriefConfig `protobuf:"bytes,1,opt,name=cfg_info,json=cfgInfo" json:"cfg_info,omitempty"`
}

func (m *UpdateCheckInBriefCfgReq) Reset()                    { *m = UpdateCheckInBriefCfgReq{} }
func (m *UpdateCheckInBriefCfgReq) String() string            { return proto.CompactTextString(m) }
func (*UpdateCheckInBriefCfgReq) ProtoMessage()               {}
func (*UpdateCheckInBriefCfgReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{30} }

func (m *UpdateCheckInBriefCfgReq) GetCfgInfo() *CheckInBriefConfig {
	if m != nil {
		return m.CfgInfo
	}
	return nil
}

type UpdateCheckInBriefCfgResp struct {
}

func (m *UpdateCheckInBriefCfgResp) Reset()         { *m = UpdateCheckInBriefCfgResp{} }
func (m *UpdateCheckInBriefCfgResp) String() string { return proto.CompactTextString(m) }
func (*UpdateCheckInBriefCfgResp) ProtoMessage()    {}
func (*UpdateCheckInBriefCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCheckin, []int{31}
}

type FirstRechargeAwardReq struct {
	Uid             uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RechargeNum     uint32 `protobuf:"varint,2,opt,name=recharge_num,json=rechargeNum,proto3" json:"recharge_num,omitempty"`
	RechargeType    string `protobuf:"bytes,3,opt,name=recharge_type,json=rechargeType,proto3" json:"recharge_type,omitempty"`
	RechargeOrderId string `protobuf:"bytes,4,opt,name=recharge_order_id,json=rechargeOrderId,proto3" json:"recharge_order_id,omitempty"`
}

func (m *FirstRechargeAwardReq) Reset()                    { *m = FirstRechargeAwardReq{} }
func (m *FirstRechargeAwardReq) String() string            { return proto.CompactTextString(m) }
func (*FirstRechargeAwardReq) ProtoMessage()               {}
func (*FirstRechargeAwardReq) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{32} }

func (m *FirstRechargeAwardReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FirstRechargeAwardReq) GetRechargeNum() uint32 {
	if m != nil {
		return m.RechargeNum
	}
	return 0
}

func (m *FirstRechargeAwardReq) GetRechargeType() string {
	if m != nil {
		return m.RechargeType
	}
	return ""
}

func (m *FirstRechargeAwardReq) GetRechargeOrderId() string {
	if m != nil {
		return m.RechargeOrderId
	}
	return ""
}

type FirstRechargeAwardResp struct {
	AwardPkgId uint32 `protobuf:"varint,1,opt,name=award_pkg_id,json=awardPkgId,proto3" json:"award_pkg_id,omitempty"`
}

func (m *FirstRechargeAwardResp) Reset()                    { *m = FirstRechargeAwardResp{} }
func (m *FirstRechargeAwardResp) String() string            { return proto.CompactTextString(m) }
func (*FirstRechargeAwardResp) ProtoMessage()               {}
func (*FirstRechargeAwardResp) Descriptor() ([]byte, []int) { return fileDescriptorCheckin, []int{33} }

func (m *FirstRechargeAwardResp) GetAwardPkgId() uint32 {
	if m != nil {
		return m.AwardPkgId
	}
	return 0
}

func init() {
	proto.RegisterType((*CheckInCommonAward)(nil), "checkin.CheckInCommonAward")
	proto.RegisterType((*CheckInBriefConfig)(nil), "checkin.CheckInBriefConfig")
	proto.RegisterType((*CheckInCommonConfig)(nil), "checkin.CheckInCommonConfig")
	proto.RegisterType((*CheckInDetailInfo)(nil), "checkin.CheckInDetailInfo")
	proto.RegisterType((*DoCheckInReq)(nil), "checkin.DoCheckInReq")
	proto.RegisterType((*GetUserCheckInTimeReq)(nil), "checkin.GetUserCheckInTimeReq")
	proto.RegisterType((*GetUserCheckInTimeResp)(nil), "checkin.GetUserCheckInTimeResp")
	proto.RegisterType((*GetUserCheckInDetailResp)(nil), "checkin.GetUserCheckInDetailResp")
	proto.RegisterType((*FetchAwardReq)(nil), "checkin.FetchAwardReq")
	proto.RegisterType((*UserAwardInfo)(nil), "checkin.UserAwardInfo")
	proto.RegisterType((*UserDelayAwardInfo)(nil), "checkin.UserDelayAwardInfo")
	proto.RegisterType((*GetAwardHistoryReq)(nil), "checkin.GetAwardHistoryReq")
	proto.RegisterType((*GetAwardHistoryResp)(nil), "checkin.GetAwardHistoryResp")
	proto.RegisterType((*GetUserAwardByUidReq)(nil), "checkin.GetUserAwardByUidReq")
	proto.RegisterType((*GetUserAwardByUidResp)(nil), "checkin.GetUserAwardByUidResp")
	proto.RegisterType((*GetUserAwardByDevIdReq)(nil), "checkin.GetUserAwardByDevIdReq")
	proto.RegisterType((*GetUserAwardByDevIdResp)(nil), "checkin.GetUserAwardByDevIdResp")
	proto.RegisterType((*AddDelayAwardReq)(nil), "checkin.AddDelayAwardReq")
	proto.RegisterType((*UserDailyCheckInInfo)(nil), "checkin.UserDailyCheckInInfo")
	proto.RegisterType((*LoginCheckInMissionConfig)(nil), "checkin.LoginCheckInMissionConfig")
	proto.RegisterType((*GetDailyCheckInReq)(nil), "checkin.GetDailyCheckInReq")
	proto.RegisterType((*GetDailyCheckInResp)(nil), "checkin.GetDailyCheckInResp")
	proto.RegisterType((*DoDailyCheckInReq)(nil), "checkin.DoDailyCheckInReq")
	proto.RegisterType((*DoDailyCheckInResp)(nil), "checkin.DoDailyCheckInResp")
	proto.RegisterType((*GetCheckInCommonCfgListReq)(nil), "checkin.GetCheckInCommonCfgListReq")
	proto.RegisterType((*GetCheckInCommonCfgListResp)(nil), "checkin.GetCheckInCommonCfgListResp")
	proto.RegisterType((*GetCheckInCommonCfgByIdReq)(nil), "checkin.GetCheckInCommonCfgByIdReq")
	proto.RegisterType((*GetCheckInCommonCfgByIdResp)(nil), "checkin.GetCheckInCommonCfgByIdResp")
	proto.RegisterType((*UpdateCheckInCommonAwardReq)(nil), "checkin.UpdateCheckInCommonAwardReq")
	proto.RegisterType((*UpdateCheckInCommonAwardResp)(nil), "checkin.UpdateCheckInCommonAwardResp")
	proto.RegisterType((*UpdateCheckInBriefCfgReq)(nil), "checkin.UpdateCheckInBriefCfgReq")
	proto.RegisterType((*UpdateCheckInBriefCfgResp)(nil), "checkin.UpdateCheckInBriefCfgResp")
	proto.RegisterType((*FirstRechargeAwardReq)(nil), "checkin.FirstRechargeAwardReq")
	proto.RegisterType((*FirstRechargeAwardResp)(nil), "checkin.FirstRechargeAwardResp")
	proto.RegisterEnum("checkin.CheckInType", CheckInType_name, CheckInType_value)
	proto.RegisterEnum("checkin.AwardIdType", AwardIdType_name, AwardIdType_value)
	proto.RegisterEnum("checkin.GameId5CheckInFlag", GameId5CheckInFlag_name, GameId5CheckInFlag_value)
	proto.RegisterEnum("checkin.DailyCheckInType", DailyCheckInType_name, DailyCheckInType_value)
	proto.RegisterEnum("checkin.CheckInCommonAwardType", CheckInCommonAwardType_name, CheckInCommonAwardType_value)
	proto.RegisterEnum("checkin.LoginCheckInMissionConfig_LoginExtraAwardType", LoginCheckInMissionConfig_LoginExtraAwardType_name, LoginCheckInMissionConfig_LoginExtraAwardType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Checkin service

type CheckinClient interface {
	DoCheckIn(ctx context.Context, in *DoCheckInReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserCheckInTime(ctx context.Context, in *GetUserCheckInTimeReq, opts ...grpc.CallOption) (*GetUserCheckInTimeResp, error)
	FetchAward(ctx context.Context, in *FetchAwardReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAwardHistory(ctx context.Context, in *GetAwardHistoryReq, opts ...grpc.CallOption) (*GetAwardHistoryResp, error)
	GetUserAwardByUid(ctx context.Context, in *GetUserAwardByUidReq, opts ...grpc.CallOption) (*GetUserAwardByUidResp, error)
	GetUserAwardByDevId(ctx context.Context, in *GetUserAwardByDevIdReq, opts ...grpc.CallOption) (*GetUserAwardByDevIdResp, error)
	AddDelayAward(ctx context.Context, in *AddDelayAwardReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetDailyCheckIn(ctx context.Context, in *GetDailyCheckInReq, opts ...grpc.CallOption) (*GetDailyCheckInResp, error)
	DoDailyCheckIn(ctx context.Context, in *DoDailyCheckInReq, opts ...grpc.CallOption) (*DoDailyCheckInResp, error)
	GetUserCheckInDetail(ctx context.Context, in *GetUserCheckInTimeReq, opts ...grpc.CallOption) (*GetUserCheckInDetailResp, error)
	GetCheckInCommonCfgList(ctx context.Context, in *GetCheckInCommonCfgListReq, opts ...grpc.CallOption) (*GetCheckInCommonCfgListResp, error)
	GetCheckInCommonCfgById(ctx context.Context, in *GetCheckInCommonCfgByIdReq, opts ...grpc.CallOption) (*GetCheckInCommonCfgByIdResp, error)
	UpdateCheckInCommonAward(ctx context.Context, in *UpdateCheckInCommonAwardReq, opts ...grpc.CallOption) (*UpdateCheckInCommonAwardResp, error)
	UpdateCheckInBriefCfg(ctx context.Context, in *UpdateCheckInBriefCfgReq, opts ...grpc.CallOption) (*UpdateCheckInBriefCfgResp, error)
	FirstRechargeAward(ctx context.Context, in *FirstRechargeAwardReq, opts ...grpc.CallOption) (*FirstRechargeAwardResp, error)
}

type checkinClient struct {
	cc *grpc.ClientConn
}

func NewCheckinClient(cc *grpc.ClientConn) CheckinClient {
	return &checkinClient{cc}
}

func (c *checkinClient) DoCheckIn(ctx context.Context, in *DoCheckInReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/checkin.checkin/DoCheckIn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetUserCheckInTime(ctx context.Context, in *GetUserCheckInTimeReq, opts ...grpc.CallOption) (*GetUserCheckInTimeResp, error) {
	out := new(GetUserCheckInTimeResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetUserCheckInTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) FetchAward(ctx context.Context, in *FetchAwardReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/checkin.checkin/FetchAward", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetAwardHistory(ctx context.Context, in *GetAwardHistoryReq, opts ...grpc.CallOption) (*GetAwardHistoryResp, error) {
	out := new(GetAwardHistoryResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetAwardHistory", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetUserAwardByUid(ctx context.Context, in *GetUserAwardByUidReq, opts ...grpc.CallOption) (*GetUserAwardByUidResp, error) {
	out := new(GetUserAwardByUidResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetUserAwardByUid", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetUserAwardByDevId(ctx context.Context, in *GetUserAwardByDevIdReq, opts ...grpc.CallOption) (*GetUserAwardByDevIdResp, error) {
	out := new(GetUserAwardByDevIdResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetUserAwardByDevId", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) AddDelayAward(ctx context.Context, in *AddDelayAwardReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/checkin.checkin/AddDelayAward", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetDailyCheckIn(ctx context.Context, in *GetDailyCheckInReq, opts ...grpc.CallOption) (*GetDailyCheckInResp, error) {
	out := new(GetDailyCheckInResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetDailyCheckIn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) DoDailyCheckIn(ctx context.Context, in *DoDailyCheckInReq, opts ...grpc.CallOption) (*DoDailyCheckInResp, error) {
	out := new(DoDailyCheckInResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/DoDailyCheckIn", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetUserCheckInDetail(ctx context.Context, in *GetUserCheckInTimeReq, opts ...grpc.CallOption) (*GetUserCheckInDetailResp, error) {
	out := new(GetUserCheckInDetailResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetUserCheckInDetail", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetCheckInCommonCfgList(ctx context.Context, in *GetCheckInCommonCfgListReq, opts ...grpc.CallOption) (*GetCheckInCommonCfgListResp, error) {
	out := new(GetCheckInCommonCfgListResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetCheckInCommonCfgList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) GetCheckInCommonCfgById(ctx context.Context, in *GetCheckInCommonCfgByIdReq, opts ...grpc.CallOption) (*GetCheckInCommonCfgByIdResp, error) {
	out := new(GetCheckInCommonCfgByIdResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/GetCheckInCommonCfgById", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateCheckInCommonAward(ctx context.Context, in *UpdateCheckInCommonAwardReq, opts ...grpc.CallOption) (*UpdateCheckInCommonAwardResp, error) {
	out := new(UpdateCheckInCommonAwardResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/UpdateCheckInCommonAward", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) UpdateCheckInBriefCfg(ctx context.Context, in *UpdateCheckInBriefCfgReq, opts ...grpc.CallOption) (*UpdateCheckInBriefCfgResp, error) {
	out := new(UpdateCheckInBriefCfgResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/UpdateCheckInBriefCfg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *checkinClient) FirstRechargeAward(ctx context.Context, in *FirstRechargeAwardReq, opts ...grpc.CallOption) (*FirstRechargeAwardResp, error) {
	out := new(FirstRechargeAwardResp)
	err := grpc.Invoke(ctx, "/checkin.checkin/FirstRechargeAward", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Checkin service

type CheckinServer interface {
	DoCheckIn(context.Context, *DoCheckInReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetUserCheckInTime(context.Context, *GetUserCheckInTimeReq) (*GetUserCheckInTimeResp, error)
	FetchAward(context.Context, *FetchAwardReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetAwardHistory(context.Context, *GetAwardHistoryReq) (*GetAwardHistoryResp, error)
	GetUserAwardByUid(context.Context, *GetUserAwardByUidReq) (*GetUserAwardByUidResp, error)
	GetUserAwardByDevId(context.Context, *GetUserAwardByDevIdReq) (*GetUserAwardByDevIdResp, error)
	AddDelayAward(context.Context, *AddDelayAwardReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetDailyCheckIn(context.Context, *GetDailyCheckInReq) (*GetDailyCheckInResp, error)
	DoDailyCheckIn(context.Context, *DoDailyCheckInReq) (*DoDailyCheckInResp, error)
	GetUserCheckInDetail(context.Context, *GetUserCheckInTimeReq) (*GetUserCheckInDetailResp, error)
	GetCheckInCommonCfgList(context.Context, *GetCheckInCommonCfgListReq) (*GetCheckInCommonCfgListResp, error)
	GetCheckInCommonCfgById(context.Context, *GetCheckInCommonCfgByIdReq) (*GetCheckInCommonCfgByIdResp, error)
	UpdateCheckInCommonAward(context.Context, *UpdateCheckInCommonAwardReq) (*UpdateCheckInCommonAwardResp, error)
	UpdateCheckInBriefCfg(context.Context, *UpdateCheckInBriefCfgReq) (*UpdateCheckInBriefCfgResp, error)
	FirstRechargeAward(context.Context, *FirstRechargeAwardReq) (*FirstRechargeAwardResp, error)
}

func RegisterCheckinServer(s *grpc.Server, srv CheckinServer) {
	s.RegisterService(&_Checkin_serviceDesc, srv)
}

func _Checkin_DoCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).DoCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/DoCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).DoCheckIn(ctx, req.(*DoCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetUserCheckInTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCheckInTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetUserCheckInTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetUserCheckInTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetUserCheckInTime(ctx, req.(*GetUserCheckInTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_FetchAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).FetchAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/FetchAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).FetchAward(ctx, req.(*FetchAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetAwardHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAwardHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetAwardHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetAwardHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetAwardHistory(ctx, req.(*GetAwardHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetUserAwardByUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAwardByUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetUserAwardByUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetUserAwardByUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetUserAwardByUid(ctx, req.(*GetUserAwardByUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetUserAwardByDevId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAwardByDevIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetUserAwardByDevId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetUserAwardByDevId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetUserAwardByDevId(ctx, req.(*GetUserAwardByDevIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_AddDelayAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddDelayAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).AddDelayAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/AddDelayAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).AddDelayAward(ctx, req.(*AddDelayAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetDailyCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDailyCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetDailyCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetDailyCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetDailyCheckIn(ctx, req.(*GetDailyCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_DoDailyCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoDailyCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).DoDailyCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/DoDailyCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).DoDailyCheckIn(ctx, req.(*DoDailyCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetUserCheckInDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserCheckInTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetUserCheckInDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetUserCheckInDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetUserCheckInDetail(ctx, req.(*GetUserCheckInTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetCheckInCommonCfgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckInCommonCfgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetCheckInCommonCfgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetCheckInCommonCfgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetCheckInCommonCfgList(ctx, req.(*GetCheckInCommonCfgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_GetCheckInCommonCfgById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCheckInCommonCfgByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).GetCheckInCommonCfgById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/GetCheckInCommonCfgById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).GetCheckInCommonCfgById(ctx, req.(*GetCheckInCommonCfgByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateCheckInCommonAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCheckInCommonAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateCheckInCommonAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/UpdateCheckInCommonAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateCheckInCommonAward(ctx, req.(*UpdateCheckInCommonAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_UpdateCheckInBriefCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCheckInBriefCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).UpdateCheckInBriefCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/UpdateCheckInBriefCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).UpdateCheckInBriefCfg(ctx, req.(*UpdateCheckInBriefCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Checkin_FirstRechargeAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FirstRechargeAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CheckinServer).FirstRechargeAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/checkin.checkin/FirstRechargeAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CheckinServer).FirstRechargeAward(ctx, req.(*FirstRechargeAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Checkin_serviceDesc = grpc.ServiceDesc{
	ServiceName: "checkin.checkin",
	HandlerType: (*CheckinServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoCheckIn",
			Handler:    _Checkin_DoCheckIn_Handler,
		},
		{
			MethodName: "GetUserCheckInTime",
			Handler:    _Checkin_GetUserCheckInTime_Handler,
		},
		{
			MethodName: "FetchAward",
			Handler:    _Checkin_FetchAward_Handler,
		},
		{
			MethodName: "GetAwardHistory",
			Handler:    _Checkin_GetAwardHistory_Handler,
		},
		{
			MethodName: "GetUserAwardByUid",
			Handler:    _Checkin_GetUserAwardByUid_Handler,
		},
		{
			MethodName: "GetUserAwardByDevId",
			Handler:    _Checkin_GetUserAwardByDevId_Handler,
		},
		{
			MethodName: "AddDelayAward",
			Handler:    _Checkin_AddDelayAward_Handler,
		},
		{
			MethodName: "GetDailyCheckIn",
			Handler:    _Checkin_GetDailyCheckIn_Handler,
		},
		{
			MethodName: "DoDailyCheckIn",
			Handler:    _Checkin_DoDailyCheckIn_Handler,
		},
		{
			MethodName: "GetUserCheckInDetail",
			Handler:    _Checkin_GetUserCheckInDetail_Handler,
		},
		{
			MethodName: "GetCheckInCommonCfgList",
			Handler:    _Checkin_GetCheckInCommonCfgList_Handler,
		},
		{
			MethodName: "GetCheckInCommonCfgById",
			Handler:    _Checkin_GetCheckInCommonCfgById_Handler,
		},
		{
			MethodName: "UpdateCheckInCommonAward",
			Handler:    _Checkin_UpdateCheckInCommonAward_Handler,
		},
		{
			MethodName: "UpdateCheckInBriefCfg",
			Handler:    _Checkin_UpdateCheckInBriefCfg_Handler,
		},
		{
			MethodName: "FirstRechargeAward",
			Handler:    _Checkin_FirstRechargeAward_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/checkinsvr/checkin.proto",
}

func (m *CheckInCommonAward) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInCommonAward) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckinType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckinType))
	}
	if m.SubId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.SubId))
	}
	if m.AwardType != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardType))
	}
	if m.AwardId != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardId))
	}
	if len(m.StrAwardId) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.StrAwardId)))
		i += copy(dAtA[i:], m.StrAwardId)
	}
	if m.AwardNum != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardNum))
	}
	if m.AwardTime != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardTime))
	}
	if m.UniqId != 0 {
		dAtA[i] = 0x40
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.UniqId))
	}
	return i, nil
}

func (m *CheckInBriefConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInBriefConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckinType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckinType))
	}
	if m.SubId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.SubId))
	}
	if len(m.IconUrl) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.IconUrl)))
		i += copy(dAtA[i:], m.IconUrl)
	}
	if len(m.AwardIconUrl) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.AwardIconUrl)))
		i += copy(dAtA[i:], m.AwardIconUrl)
	}
	if len(m.AwardMsg) > 0 {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.AwardMsg)))
		i += copy(dAtA[i:], m.AwardMsg)
	}
	if len(m.BackgroundUrl) > 0 {
		dAtA[i] = 0x32
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.BackgroundUrl)))
		i += copy(dAtA[i:], m.BackgroundUrl)
	}
	if len(m.AwardButtonUrl) > 0 {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.AwardButtonUrl)))
		i += copy(dAtA[i:], m.AwardButtonUrl)
	}
	if len(m.MsgColor) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.MsgColor)))
		i += copy(dAtA[i:], m.MsgColor)
	}
	return i, nil
}

func (m *CheckInCommonConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInCommonConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.BriefCfg != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.BriefCfg.Size()))
		n1, err := m.BriefCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	if len(m.AwardList) > 0 {
		for _, msg := range m.AwardList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintCheckin(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckInDetailInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInDetailInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckInTime != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInTime))
	}
	if m.CheckInValue != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInValue))
	}
	return i, nil
}

func (m *DoCheckInReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoCheckInReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.CheckInValue != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInValue))
	}
	if len(m.DeviceId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	return i, nil
}

func (m *GetUserCheckInTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCheckInTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	return i, nil
}

func (m *GetUserCheckInTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCheckInTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TimeList) > 0 {
		dAtA3 := make([]byte, len(m.TimeList)*10)
		var j2 int
		for _, num := range m.TimeList {
			for num >= 1<<7 {
				dAtA3[j2] = uint8(uint64(num)&0x7f | 0x80)
				num >>= 7
				j2++
			}
			dAtA3[j2] = uint8(num)
			j2++
		}
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(j2))
		i += copy(dAtA[i:], dAtA3[:j2])
	}
	return i, nil
}

func (m *GetUserCheckInDetailResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserCheckInDetailResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TimeList) > 0 {
		for _, msg := range m.TimeList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCheckin(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *FetchAwardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FetchAwardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardId))
	}
	if len(m.DeviceId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	if m.Status != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Status))
	}
	return i, nil
}

func (m *UserAwardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserAwardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardId))
	}
	if m.FetchTime != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.FetchTime))
	}
	return i, nil
}

func (m *UserDelayAwardInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserDelayAwardInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardId))
	}
	if len(m.DeviceId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	if m.UniqId != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.UniqId))
	}
	if m.DelayFetchTime != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.DelayFetchTime))
	}
	if m.TbeanNum != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.TbeanNum))
	}
	return i, nil
}

func (m *GetAwardHistoryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAwardHistoryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckInType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.Limit != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Limit))
	}
	return i, nil
}

func (m *GetAwardHistoryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAwardHistoryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.HistoryList) > 0 {
		for _, msg := range m.HistoryList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCheckin(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserAwardByUidReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAwardByUidReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	return i, nil
}

func (m *GetUserAwardByUidResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAwardByUidResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AwardList) > 0 {
		for _, msg := range m.AwardList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCheckin(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserAwardByDevIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAwardByDevIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.DeviceId) > 0 {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	return i, nil
}

func (m *GetUserAwardByDevIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAwardByDevIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.AwardList) > 0 {
		for _, msg := range m.AwardList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCheckin(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *AddDelayAwardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AddDelayAwardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardId))
	}
	if len(m.DeviceId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.DeviceId)))
		i += copy(dAtA[i:], m.DeviceId)
	}
	if m.DelayFetchSecond != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.DelayFetchSecond))
	}
	return i, nil
}

func (m *UserDailyCheckInInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserDailyCheckInInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Type))
	}
	if m.LastCheckinAt != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.LastCheckinAt))
	}
	if m.ContCheckinDays != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.ContCheckinDays))
	}
	return i, nil
}

func (m *LoginCheckInMissionConfig) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *LoginCheckInMissionConfig) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Type != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Type))
	}
	if m.Exp != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Exp))
	}
	if m.RedDiamond != 0 {
		dAtA[i] = 0x18
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.RedDiamond))
	}
	if m.ContDays != 0 {
		dAtA[i] = 0x20
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.ContDays))
	}
	if m.ExtraAwardType != 0 {
		dAtA[i] = 0x28
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.ExtraAwardType))
	}
	if m.ExtraAwardItemId != 0 {
		dAtA[i] = 0x30
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.ExtraAwardItemId))
	}
	if m.ExtraAwardItemCount != 0 {
		dAtA[i] = 0x38
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.ExtraAwardItemCount))
	}
	if len(m.ExtraAwardItemUrl) > 0 {
		dAtA[i] = 0x42
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.ExtraAwardItemUrl)))
		i += copy(dAtA[i:], m.ExtraAwardItemUrl)
	}
	if len(m.IconUrl) > 0 {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.IconUrl)))
		i += copy(dAtA[i:], m.IconUrl)
	}
	return i, nil
}

func (m *GetDailyCheckInReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDailyCheckInReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Type))
	}
	return i, nil
}

func (m *GetDailyCheckInResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDailyCheckInResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckinInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckinInfo.Size()))
		n4, err := m.CheckinInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *DoDailyCheckInReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoDailyCheckInReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.Type != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Type))
	}
	return i, nil
}

func (m *DoDailyCheckInResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DoDailyCheckInResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckinInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckinInfo.Size()))
		n5, err := m.CheckinInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	if len(m.AwardInfo) > 0 {
		dAtA[i] = 0x12
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.AwardInfo)))
		i += copy(dAtA[i:], m.AwardInfo)
	}
	if m.AlreadyNotify {
		dAtA[i] = 0x18
		i++
		if m.AlreadyNotify {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i++
	}
	return i, nil
}

func (m *GetCheckInCommonCfgListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCheckInCommonCfgListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckInType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	return i, nil
}

func (m *GetCheckInCommonCfgListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCheckInCommonCfgListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, msg := range m.CfgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCheckin(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetCheckInCommonCfgByIdReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCheckInCommonCfgByIdReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CheckInType != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CheckInType))
	}
	if m.SubId != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.SubId))
	}
	return i, nil
}

func (m *GetCheckInCommonCfgByIdResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCheckInCommonCfgByIdResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CfgInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CfgInfo.Size()))
		n6, err := m.CfgInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *UpdateCheckInCommonAwardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateCheckInCommonAwardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AwardInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardInfo.Size()))
		n7, err := m.AwardInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *UpdateCheckInCommonAwardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateCheckInCommonAwardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *UpdateCheckInBriefCfgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateCheckInBriefCfgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.CfgInfo != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.CfgInfo.Size()))
		n8, err := m.CfgInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *UpdateCheckInBriefCfgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateCheckInBriefCfgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *FirstRechargeAwardReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstRechargeAwardReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Uid != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.Uid))
	}
	if m.RechargeNum != 0 {
		dAtA[i] = 0x10
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.RechargeNum))
	}
	if len(m.RechargeType) > 0 {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.RechargeType)))
		i += copy(dAtA[i:], m.RechargeType)
	}
	if len(m.RechargeOrderId) > 0 {
		dAtA[i] = 0x22
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(len(m.RechargeOrderId)))
		i += copy(dAtA[i:], m.RechargeOrderId)
	}
	return i, nil
}

func (m *FirstRechargeAwardResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FirstRechargeAwardResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.AwardPkgId != 0 {
		dAtA[i] = 0x8
		i++
		i = encodeVarintCheckin(dAtA, i, uint64(m.AwardPkgId))
	}
	return i, nil
}

func encodeFixed64Checkin(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Checkin(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintCheckin(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CheckInCommonAward) Size() (n int) {
	var l int
	_ = l
	if m.CheckinType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckinType))
	}
	if m.SubId != 0 {
		n += 1 + sovCheckin(uint64(m.SubId))
	}
	if m.AwardType != 0 {
		n += 1 + sovCheckin(uint64(m.AwardType))
	}
	if m.AwardId != 0 {
		n += 1 + sovCheckin(uint64(m.AwardId))
	}
	l = len(m.StrAwardId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	if m.AwardNum != 0 {
		n += 1 + sovCheckin(uint64(m.AwardNum))
	}
	if m.AwardTime != 0 {
		n += 1 + sovCheckin(uint64(m.AwardTime))
	}
	if m.UniqId != 0 {
		n += 1 + sovCheckin(uint64(m.UniqId))
	}
	return n
}

func (m *CheckInBriefConfig) Size() (n int) {
	var l int
	_ = l
	if m.CheckinType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckinType))
	}
	if m.SubId != 0 {
		n += 1 + sovCheckin(uint64(m.SubId))
	}
	l = len(m.IconUrl)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.AwardIconUrl)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.AwardMsg)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.BackgroundUrl)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.AwardButtonUrl)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.MsgColor)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *CheckInCommonConfig) Size() (n int) {
	var l int
	_ = l
	if m.BriefCfg != nil {
		l = m.BriefCfg.Size()
		n += 1 + l + sovCheckin(uint64(l))
	}
	if len(m.AwardList) > 0 {
		for _, e := range m.AwardList {
			l = e.Size()
			n += 1 + l + sovCheckin(uint64(l))
		}
	}
	return n
}

func (m *CheckInDetailInfo) Size() (n int) {
	var l int
	_ = l
	if m.CheckInTime != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInTime))
	}
	if m.CheckInValue != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInValue))
	}
	return n
}

func (m *DoCheckInReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.CheckInValue != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInValue))
	}
	l = len(m.DeviceId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *GetUserCheckInTimeReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	return n
}

func (m *GetUserCheckInTimeResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TimeList) > 0 {
		l = 0
		for _, e := range m.TimeList {
			l += sovCheckin(uint64(e))
		}
		n += 1 + sovCheckin(uint64(l)) + l
	}
	return n
}

func (m *GetUserCheckInDetailResp) Size() (n int) {
	var l int
	_ = l
	if len(m.TimeList) > 0 {
		for _, e := range m.TimeList {
			l = e.Size()
			n += 1 + l + sovCheckin(uint64(l))
		}
	}
	return n
}

func (m *FetchAwardReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		n += 1 + sovCheckin(uint64(m.AwardId))
	}
	l = len(m.DeviceId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	if m.Status != 0 {
		n += 1 + sovCheckin(uint64(m.Status))
	}
	return n
}

func (m *UserAwardInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		n += 1 + sovCheckin(uint64(m.AwardId))
	}
	if m.FetchTime != 0 {
		n += 1 + sovCheckin(uint64(m.FetchTime))
	}
	return n
}

func (m *UserDelayAwardInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		n += 1 + sovCheckin(uint64(m.AwardId))
	}
	l = len(m.DeviceId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	if m.UniqId != 0 {
		n += 1 + sovCheckin(uint64(m.UniqId))
	}
	if m.DelayFetchTime != 0 {
		n += 1 + sovCheckin(uint64(m.DelayFetchTime))
	}
	if m.TbeanNum != 0 {
		n += 1 + sovCheckin(uint64(m.TbeanNum))
	}
	return n
}

func (m *GetAwardHistoryReq) Size() (n int) {
	var l int
	_ = l
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.Limit != 0 {
		n += 1 + sovCheckin(uint64(m.Limit))
	}
	return n
}

func (m *GetAwardHistoryResp) Size() (n int) {
	var l int
	_ = l
	if len(m.HistoryList) > 0 {
		for _, e := range m.HistoryList {
			l = e.Size()
			n += 1 + l + sovCheckin(uint64(l))
		}
	}
	return n
}

func (m *GetUserAwardByUidReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	return n
}

func (m *GetUserAwardByUidResp) Size() (n int) {
	var l int
	_ = l
	if len(m.AwardList) > 0 {
		for _, e := range m.AwardList {
			l = e.Size()
			n += 1 + l + sovCheckin(uint64(l))
		}
	}
	return n
}

func (m *GetUserAwardByDevIdReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.DeviceId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	return n
}

func (m *GetUserAwardByDevIdResp) Size() (n int) {
	var l int
	_ = l
	if len(m.AwardList) > 0 {
		for _, e := range m.AwardList {
			l = e.Size()
			n += 1 + l + sovCheckin(uint64(l))
		}
	}
	return n
}

func (m *AddDelayAwardReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.AwardId != 0 {
		n += 1 + sovCheckin(uint64(m.AwardId))
	}
	l = len(m.DeviceId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	if m.DelayFetchSecond != 0 {
		n += 1 + sovCheckin(uint64(m.DelayFetchSecond))
	}
	return n
}

func (m *UserDailyCheckInInfo) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovCheckin(uint64(m.Type))
	}
	if m.LastCheckinAt != 0 {
		n += 1 + sovCheckin(uint64(m.LastCheckinAt))
	}
	if m.ContCheckinDays != 0 {
		n += 1 + sovCheckin(uint64(m.ContCheckinDays))
	}
	return n
}

func (m *LoginCheckInMissionConfig) Size() (n int) {
	var l int
	_ = l
	if m.Type != 0 {
		n += 1 + sovCheckin(uint64(m.Type))
	}
	if m.Exp != 0 {
		n += 1 + sovCheckin(uint64(m.Exp))
	}
	if m.RedDiamond != 0 {
		n += 1 + sovCheckin(uint64(m.RedDiamond))
	}
	if m.ContDays != 0 {
		n += 1 + sovCheckin(uint64(m.ContDays))
	}
	if m.ExtraAwardType != 0 {
		n += 1 + sovCheckin(uint64(m.ExtraAwardType))
	}
	if m.ExtraAwardItemId != 0 {
		n += 1 + sovCheckin(uint64(m.ExtraAwardItemId))
	}
	if m.ExtraAwardItemCount != 0 {
		n += 1 + sovCheckin(uint64(m.ExtraAwardItemCount))
	}
	l = len(m.ExtraAwardItemUrl)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.IconUrl)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *GetDailyCheckInReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovCheckin(uint64(m.Type))
	}
	return n
}

func (m *GetDailyCheckInResp) Size() (n int) {
	var l int
	_ = l
	if m.CheckinInfo != nil {
		l = m.CheckinInfo.Size()
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *DoDailyCheckInReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.Type != 0 {
		n += 1 + sovCheckin(uint64(m.Type))
	}
	return n
}

func (m *DoDailyCheckInResp) Size() (n int) {
	var l int
	_ = l
	if m.CheckinInfo != nil {
		l = m.CheckinInfo.Size()
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.AwardInfo)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	if m.AlreadyNotify {
		n += 2
	}
	return n
}

func (m *GetCheckInCommonCfgListReq) Size() (n int) {
	var l int
	_ = l
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	return n
}

func (m *GetCheckInCommonCfgListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.CfgList) > 0 {
		for _, e := range m.CfgList {
			l = e.Size()
			n += 1 + l + sovCheckin(uint64(l))
		}
	}
	return n
}

func (m *GetCheckInCommonCfgByIdReq) Size() (n int) {
	var l int
	_ = l
	if m.CheckInType != 0 {
		n += 1 + sovCheckin(uint64(m.CheckInType))
	}
	if m.SubId != 0 {
		n += 1 + sovCheckin(uint64(m.SubId))
	}
	return n
}

func (m *GetCheckInCommonCfgByIdResp) Size() (n int) {
	var l int
	_ = l
	if m.CfgInfo != nil {
		l = m.CfgInfo.Size()
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *UpdateCheckInCommonAwardReq) Size() (n int) {
	var l int
	_ = l
	if m.AwardInfo != nil {
		l = m.AwardInfo.Size()
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *UpdateCheckInCommonAwardResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *UpdateCheckInBriefCfgReq) Size() (n int) {
	var l int
	_ = l
	if m.CfgInfo != nil {
		l = m.CfgInfo.Size()
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *UpdateCheckInBriefCfgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *FirstRechargeAwardReq) Size() (n int) {
	var l int
	_ = l
	if m.Uid != 0 {
		n += 1 + sovCheckin(uint64(m.Uid))
	}
	if m.RechargeNum != 0 {
		n += 1 + sovCheckin(uint64(m.RechargeNum))
	}
	l = len(m.RechargeType)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	l = len(m.RechargeOrderId)
	if l > 0 {
		n += 1 + l + sovCheckin(uint64(l))
	}
	return n
}

func (m *FirstRechargeAwardResp) Size() (n int) {
	var l int
	_ = l
	if m.AwardPkgId != 0 {
		n += 1 + sovCheckin(uint64(m.AwardPkgId))
	}
	return n
}

func sovCheckin(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozCheckin(x uint64) (n int) {
	return sovCheckin(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CheckInCommonAward) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInCommonAward: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInCommonAward: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinType", wireType)
			}
			m.CheckinType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckinType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubId", wireType)
			}
			m.SubId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardType", wireType)
			}
			m.AwardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardId", wireType)
			}
			m.AwardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StrAwardId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StrAwardId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardNum", wireType)
			}
			m.AwardNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardTime", wireType)
			}
			m.AwardTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UniqId", wireType)
			}
			m.UniqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UniqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckInBriefConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInBriefConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInBriefConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinType", wireType)
			}
			m.CheckinType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckinType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubId", wireType)
			}
			m.SubId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardIconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardIconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BackgroundUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BackgroundUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardButtonUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardButtonUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgColor", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgColor = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckInCommonConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInCommonConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInCommonConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BriefCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.BriefCfg == nil {
				m.BriefCfg = &CheckInBriefConfig{}
			}
			if err := m.BriefCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardList = append(m.AwardList, &CheckInCommonAward{})
			if err := m.AwardList[len(m.AwardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckInDetailInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInDetailInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInDetailInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInTime", wireType)
			}
			m.CheckInTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInValue", wireType)
			}
			m.CheckInValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoCheckInReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoCheckInReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoCheckInReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInValue", wireType)
			}
			m.CheckInValue = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInValue |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCheckInTimeReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCheckInTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCheckInTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCheckInTimeResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCheckInTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCheckInTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCheckin
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TimeList = append(m.TimeList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCheckin
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCheckin
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCheckin
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TimeList = append(m.TimeList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserCheckInDetailResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserCheckInDetailResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserCheckInDetailResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TimeList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TimeList = append(m.TimeList, &CheckInDetailInfo{})
			if err := m.TimeList[len(m.TimeList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FetchAwardReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FetchAwardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FetchAwardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardId", wireType)
			}
			m.AwardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserAwardInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserAwardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserAwardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardId", wireType)
			}
			m.AwardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FetchTime", wireType)
			}
			m.FetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserDelayAwardInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserDelayAwardInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserDelayAwardInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardId", wireType)
			}
			m.AwardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UniqId", wireType)
			}
			m.UniqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UniqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DelayFetchTime", wireType)
			}
			m.DelayFetchTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DelayFetchTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TbeanNum", wireType)
			}
			m.TbeanNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TbeanNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAwardHistoryReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAwardHistoryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAwardHistoryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAwardHistoryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAwardHistoryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAwardHistoryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HistoryList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HistoryList = append(m.HistoryList, &UserAwardInfo{})
			if err := m.HistoryList[len(m.HistoryList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAwardByUidReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAwardByUidReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAwardByUidReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAwardByUidResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAwardByUidResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAwardByUidResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardList = append(m.AwardList, &UserAwardInfo{})
			if err := m.AwardList[len(m.AwardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAwardByDevIdReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAwardByDevIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAwardByDevIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAwardByDevIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAwardByDevIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAwardByDevIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardList = append(m.AwardList, &UserAwardInfo{})
			if err := m.AwardList[len(m.AwardList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AddDelayAwardReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AddDelayAwardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AddDelayAwardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardId", wireType)
			}
			m.AwardId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DeviceId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DeviceId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DelayFetchSecond", wireType)
			}
			m.DelayFetchSecond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DelayFetchSecond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserDailyCheckInInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserDailyCheckInInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserDailyCheckInInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastCheckinAt", wireType)
			}
			m.LastCheckinAt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastCheckinAt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContCheckinDays", wireType)
			}
			m.ContCheckinDays = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContCheckinDays |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *LoginCheckInMissionConfig) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: LoginCheckInMissionConfig: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: LoginCheckInMissionConfig: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Exp", wireType)
			}
			m.Exp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Exp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RedDiamond", wireType)
			}
			m.RedDiamond = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RedDiamond |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContDays", wireType)
			}
			m.ContDays = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ContDays |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraAwardType", wireType)
			}
			m.ExtraAwardType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraAwardType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraAwardItemId", wireType)
			}
			m.ExtraAwardItemId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraAwardItemId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraAwardItemCount", wireType)
			}
			m.ExtraAwardItemCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExtraAwardItemCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExtraAwardItemUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ExtraAwardItemUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconUrl", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconUrl = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDailyCheckInReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDailyCheckInReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDailyCheckInReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDailyCheckInResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDailyCheckInResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDailyCheckInResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckinInfo == nil {
				m.CheckinInfo = &UserDailyCheckInInfo{}
			}
			if err := m.CheckinInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoDailyCheckInReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoDailyCheckInReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoDailyCheckInReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DoDailyCheckInResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DoDailyCheckInResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DoDailyCheckInResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckinInfo == nil {
				m.CheckinInfo = &UserDailyCheckInInfo{}
			}
			if err := m.CheckinInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardInfo", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AwardInfo = append(m.AwardInfo[:0], dAtA[iNdEx:postIndex]...)
			if m.AwardInfo == nil {
				m.AwardInfo = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AlreadyNotify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AlreadyNotify = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCheckInCommonCfgListReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCheckInCommonCfgListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CfgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CfgList = append(m.CfgList, &CheckInCommonConfig{})
			if err := m.CfgList[len(m.CfgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCheckInCommonCfgByIdReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgByIdReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgByIdReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckInType", wireType)
			}
			m.CheckInType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckInType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SubId", wireType)
			}
			m.SubId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SubId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCheckInCommonCfgByIdResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgByIdResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCheckInCommonCfgByIdResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CfgInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CfgInfo == nil {
				m.CfgInfo = &CheckInCommonConfig{}
			}
			if err := m.CfgInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateCheckInCommonAwardReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateCheckInCommonAwardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateCheckInCommonAwardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.AwardInfo == nil {
				m.AwardInfo = &CheckInCommonAward{}
			}
			if err := m.AwardInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateCheckInCommonAwardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateCheckInCommonAwardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateCheckInCommonAwardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateCheckInBriefCfgReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateCheckInBriefCfgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateCheckInBriefCfgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CfgInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CfgInfo == nil {
				m.CfgInfo = &CheckInBriefConfig{}
			}
			if err := m.CfgInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateCheckInBriefCfgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateCheckInBriefCfgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateCheckInBriefCfgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstRechargeAwardReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstRechargeAwardReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstRechargeAwardReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeNum", wireType)
			}
			m.RechargeNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RechargeNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeType", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RechargeType = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RechargeOrderId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCheckin
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RechargeOrderId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *FirstRechargeAwardResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: FirstRechargeAwardResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: FirstRechargeAwardResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AwardPkgId", wireType)
			}
			m.AwardPkgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AwardPkgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCheckin(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCheckin
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipCheckin(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCheckin
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCheckin
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthCheckin
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCheckin
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCheckin(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCheckin = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCheckin   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/checkinsvr/checkin.proto", fileDescriptorCheckin) }

var fileDescriptorCheckin = []byte{
	// 2487 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x59, 0x4b, 0x8f, 0x1b, 0x59,
	0x15, 0x9e, 0xb2, 0xbb, 0xdd, 0xf6, 0x69, 0xdb, 0x5d, 0x7d, 0xfb, 0xed, 0x7e, 0xc4, 0xa9, 0x49,
	0x50, 0xd4, 0x83, 0x13, 0xc8, 0x28, 0xc0, 0x58, 0xc6, 0x4a, 0xba, 0x3b, 0xc9, 0x98, 0x49, 0x9a,
	0xe0, 0x90, 0x19, 0x8d, 0x46, 0xa3, 0x52, 0xb9, 0xea, 0xba, 0x7c, 0xd5, 0xf5, 0x4a, 0x55, 0xb9,
	0x13, 0x0f, 0x48, 0x03, 0x08, 0x98, 0x81, 0xd9, 0x8c, 0x46, 0x9a, 0x0d, 0x8f, 0x5d, 0x90, 0x58,
	0xf1, 0x13, 0x10, 0x0b, 0x16, 0x2c, 0x91, 0xd8, 0xb0, 0x42, 0x28, 0x6c, 0xb2, 0xe3, 0x27, 0x80,
	0xee, 0xa3, 0xca, 0x55, 0x7e, 0x75, 0x13, 0x06, 0x76, 0xae, 0x73, 0xce, 0xbd, 0xe7, 0x3b, 0x8f,
	0x7b, 0xee, 0x39, 0xd7, 0xb0, 0x13, 0xf8, 0xfa, 0x35, 0xbd, 0x87, 0xf5, 0x13, 0xe2, 0x04, 0xa7,
	0x7e, 0xf4, 0xf3, 0xaa, 0xe7, 0xbb, 0xa1, 0x8b, 0x16, 0xc4, 0x67, 0xe5, 0x92, 0xee, 0xda, 0xb6,
	0xeb, 0x5c, 0x0b, 0xad, 0x53, 0x8f, 0xe8, 0x27, 0x16, 0xbe, 0x16, 0x9c, 0x74, 0xfa, 0xc4, 0x0a,
	0x89, 0x13, 0x0e, 0x3c, 0xcc, 0xc5, 0x95, 0x7f, 0x49, 0x80, 0x0e, 0xe9, 0x8a, 0x96, 0x73, 0xc8,
	0xe4, 0x6f, 0x3d, 0xd1, 0x7c, 0x03, 0x5d, 0x84, 0xa2, 0xd8, 0x47, 0xa5, 0xc2, 0x9b, 0x52, 0x55,
	0xba, 0x52, 0x6a, 0x2f, 0x0a, 0xda, 0x77, 0x07, 0x1e, 0x46, 0x6b, 0x90, 0x0b, 0xfa, 0x1d, 0x95,
	0x18, 0x9b, 0x19, 0xc6, 0x9c, 0x0f, 0xfa, 0x9d, 0x96, 0x81, 0x76, 0x01, 0x34, 0xba, 0x05, 0x5f,
	0x97, 0x65, 0xac, 0x02, 0xa3, 0xb0, 0x55, 0x5b, 0x90, 0xe7, 0x6c, 0x62, 0x6c, 0xce, 0x31, 0xe6,
	0x02, 0xfb, 0x6e, 0x19, 0xa8, 0x0a, 0xc5, 0x20, 0xf4, 0xd5, 0x98, 0x3d, 0x5f, 0x95, 0xae, 0x14,
	0xda, 0x10, 0x84, 0xfe, 0x2d, 0x21, 0xb1, 0x0d, 0x7c, 0x27, 0xd5, 0xe9, 0xdb, 0x9b, 0x39, 0xb6,
	0x9a, 0xef, 0x76, 0xdc, 0xb7, 0x13, 0x8a, 0x89, 0x8d, 0x37, 0x17, 0x92, 0x8a, 0x89, 0x8d, 0xd1,
	0x06, 0x2c, 0xf4, 0x1d, 0xf2, 0x98, 0x6e, 0x9c, 0x67, 0xbc, 0x1c, 0xfd, 0x6c, 0x19, 0xca, 0x2f,
	0x32, 0xb1, 0x07, 0x0e, 0x7c, 0x82, 0xbb, 0x87, 0xae, 0xd3, 0x25, 0xe6, 0x7f, 0xe1, 0x81, 0x2d,
	0xc8, 0x13, 0xdd, 0x75, 0xd4, 0xbe, 0x6f, 0x31, 0xfb, 0x0b, 0xed, 0x05, 0xfa, 0xfd, 0xc8, 0xb7,
	0xd0, 0x25, 0x28, 0x0b, 0xf3, 0x22, 0x81, 0x39, 0x26, 0x50, 0xe4, 0x3e, 0x10, 0x52, 0xb1, 0x99,
	0x76, 0x60, 0x0a, 0x2f, 0x70, 0x33, 0xef, 0x07, 0x26, 0xba, 0x0c, 0xe5, 0x8e, 0xa6, 0x9f, 0x98,
	0xbe, 0xdb, 0x77, 0x0c, 0xb6, 0x45, 0x8e, 0x49, 0x94, 0x86, 0x54, 0xba, 0xc7, 0x15, 0x90, 0xf9,
	0x1e, 0x9d, 0x7e, 0x18, 0x0a, 0x5d, 0x0b, 0x4c, 0x90, 0x23, 0x38, 0x60, 0x64, 0xa1, 0xcd, 0x0e,
	0x4c, 0x55, 0x77, 0x2d, 0xd7, 0x67, 0xae, 0x29, 0xb4, 0xf3, 0x76, 0x60, 0x1e, 0xd2, 0x6f, 0xe5,
	0x13, 0x09, 0x56, 0x52, 0xe9, 0x21, 0xbc, 0xf3, 0x0d, 0x28, 0x74, 0xa8, 0xb3, 0x54, 0xbd, 0x6b,
	0x32, 0xd7, 0x2c, 0x5e, 0xdf, 0xbe, 0x1a, 0x25, 0xe2, 0xb8, 0x37, 0xdb, 0x79, 0x26, 0x7d, 0xd8,
	0x35, 0x51, 0x3d, 0x0a, 0x93, 0x45, 0x82, 0x70, 0x33, 0x53, 0xcd, 0x4e, 0x5a, 0x9a, 0x48, 0x45,
	0x11, 0xc3, 0x7b, 0x24, 0x08, 0x95, 0xf7, 0x61, 0x59, 0x08, 0x1c, 0xe1, 0x50, 0x23, 0x56, 0xcb,
	0xe9, 0xba, 0x48, 0x81, 0x12, 0x5b, 0xad, 0xd2, 0x48, 0xd1, 0xd0, 0x27, 0x23, 0xd5, 0x72, 0x58,
	0xf0, 0x2f, 0x41, 0x39, 0x96, 0x39, 0xd5, 0xac, 0x3e, 0x16, 0x11, 0x2b, 0x0a, 0xa1, 0xb7, 0x29,
	0x4d, 0xf9, 0x48, 0x82, 0xe2, 0x91, 0x2b, 0x34, 0xb4, 0xf1, 0x63, 0x24, 0x43, 0xb6, 0x4f, 0x0c,
	0xb1, 0x21, 0xfd, 0x99, 0x56, 0x46, 0xd3, 0x22, 0x93, 0x56, 0x46, 0xd3, 0x62, 0x5c, 0x59, 0x76,
	0x5c, 0x19, 0x75, 0xbb, 0x81, 0x4f, 0x89, 0x8e, 0xa3, 0x93, 0x50, 0x68, 0xe7, 0x39, 0xa1, 0x65,
	0x28, 0xf7, 0x61, 0xed, 0x2e, 0x0e, 0x1f, 0x05, 0xd8, 0x3f, 0x1c, 0x5a, 0xf1, 0xd2, 0x88, 0x94,
	0x1b, 0xb0, 0x3e, 0x69, 0xbb, 0xc0, 0xa3, 0x28, 0xa8, 0xcf, 0x78, 0x30, 0xa4, 0x6a, 0x96, 0x9e,
	0x28, 0x4a, 0x60, 0xee, 0x7e, 0x08, 0x9b, 0xe9, 0x65, 0xdc, 0xeb, 0x6c, 0xe1, 0xd7, 0x47, 0x17,
	0x2e, 0x5e, 0xaf, 0x8c, 0x46, 0x71, 0x18, 0xa4, 0xc4, 0xa6, 0x9f, 0x4b, 0x50, 0xba, 0x83, 0x43,
	0xbd, 0xc7, 0xa3, 0xfb, 0xd2, 0x5e, 0x4e, 0x16, 0x92, 0x6c, 0xba, 0x90, 0xcc, 0x72, 0x2d, 0x5a,
	0x87, 0x5c, 0x10, 0x6a, 0x61, 0x3f, 0x60, 0x27, 0xab, 0xd4, 0x16, 0x5f, 0xca, 0x87, 0x50, 0xa2,
	0x96, 0xf2, 0x52, 0x43, 0xf3, 0xea, 0x0b, 0x87, 0xb5, 0x0b, 0xd0, 0xa5, 0x86, 0xf3, 0x2c, 0xe5,
	0xc5, 0xaf, 0xc0, 0x28, 0x34, 0x1c, 0xca, 0x73, 0x09, 0x10, 0x45, 0x70, 0x84, 0x2d, 0x6d, 0xf0,
	0x3f, 0x84, 0x31, 0xd3, 0x3b, 0x89, 0x2a, 0x39, 0x9f, 0xac, 0x92, 0xb4, 0x9e, 0x18, 0x14, 0x98,
	0x9a, 0x30, 0x81, 0x57, 0xe0, 0x32, 0xa3, 0xdf, 0x89, 0xec, 0x60, 0x29, 0xd5, 0xc1, 0x9a, 0xc3,
	0x8a, 0x34, 0x2f, 0xc3, 0x79, 0x46, 0x38, 0xee, 0xdb, 0xca, 0x31, 0xa0, 0xbb, 0x38, 0x64, 0xd6,
	0xbd, 0x49, 0x82, 0xd0, 0xf5, 0x07, 0x34, 0x03, 0xc6, 0x2c, 0x92, 0xc6, 0x2d, 0x5a, 0x85, 0x79,
	0x8b, 0xd8, 0x24, 0x8c, 0x6a, 0x2d, 0xfb, 0x50, 0x1e, 0xc0, 0xca, 0xd8, 0x7e, 0x81, 0x87, 0xde,
	0x80, 0x62, 0x8f, 0x7f, 0x26, 0x13, 0x74, 0x3d, 0x4e, 0xd0, 0x54, 0xa4, 0xdb, 0x8b, 0x42, 0x96,
	0xe5, 0xe7, 0x3d, 0x58, 0x15, 0x49, 0xcf, 0x04, 0x0e, 0x06, 0x8f, 0xc8, 0xcb, 0x67, 0xa9, 0x72,
	0x1c, 0x1f, 0xe4, 0xe4, 0x6e, 0x81, 0x87, 0x6e, 0xa4, 0xca, 0xe0, 0x6c, 0x7c, 0x89, 0x0a, 0xf8,
	0x6e, 0x7c, 0x92, 0xc5, 0x7e, 0x47, 0xf8, 0xb4, 0xc5, 0xf0, 0xa5, 0xc2, 0x2a, 0x8d, 0x84, 0xf5,
	0x3c, 0x50, 0x1f, 0xc0, 0xc6, 0xc4, 0xad, 0x5f, 0x1e, 0xec, 0xef, 0x24, 0x90, 0x6f, 0x19, 0xc6,
	0x30, 0xa1, 0xff, 0xff, 0xa7, 0xfd, 0xcb, 0x80, 0x92, 0x69, 0x1b, 0x60, 0xdd, 0x75, 0xa2, 0xd4,
	0x96, 0x87, 0x89, 0xfb, 0x90, 0xd1, 0x95, 0x4f, 0x25, 0x58, 0x65, 0x47, 0x50, 0x23, 0xd6, 0x40,
	0x14, 0xb1, 0x29, 0x87, 0x10, 0xc1, 0x5c, 0x02, 0x2b, 0xfb, 0x8d, 0xbe, 0x04, 0x4b, 0x96, 0x16,
	0x84, 0x6a, 0xd4, 0x37, 0x68, 0xa1, 0xc0, 0x5a, 0xa2, 0xe4, 0x43, 0x4e, 0xbd, 0x15, 0xa2, 0x7d,
	0x58, 0xd6, 0x5d, 0x67, 0x28, 0x67, 0x68, 0x83, 0x40, 0xd4, 0x83, 0x25, 0xca, 0x10, 0x92, 0x47,
	0xda, 0x20, 0x50, 0x7e, 0x93, 0x85, 0xad, 0x7b, 0xae, 0x49, 0x1c, 0x01, 0xe7, 0x3e, 0x09, 0x02,
	0x12, 0x5f, 0xc3, 0x11, 0x0a, 0x29, 0x81, 0x42, 0x86, 0x2c, 0x7e, 0xea, 0x09, 0x60, 0xf4, 0x27,
	0xba, 0x00, 0x8b, 0x3e, 0x36, 0x54, 0x83, 0x68, 0x36, 0xb5, 0x9e, 0x63, 0x02, 0x1f, 0x1b, 0x47,
	0x9c, 0x42, 0x5d, 0xc8, 0x00, 0x25, 0x80, 0xe4, 0x29, 0x81, 0x22, 0xa0, 0x27, 0x1f, 0x3f, 0x0d,
	0x7d, 0x4d, 0x4d, 0xb4, 0x75, 0xdc, 0x81, 0x65, 0x46, 0xbf, 0x15, 0xf7, 0x76, 0x35, 0x58, 0x49,
	0x4a, 0x92, 0x10, 0xdb, 0x34, 0x26, 0xbc, 0x4c, 0xc8, 0x43, 0xe1, 0x56, 0x88, 0xed, 0x96, 0x81,
	0x5e, 0x87, 0xf5, 0x31, 0x71, 0xdd, 0xed, 0x3b, 0xa1, 0xa8, 0x1a, 0x2b, 0xe9, 0x15, 0x87, 0x94,
	0x85, 0xae, 0xc1, 0xea, 0xd8, 0x22, 0xda, 0xdb, 0xf0, 0xc6, 0x65, 0x39, 0xbd, 0x84, 0xb6, 0x37,
	0xc9, 0x6e, 0xac, 0x90, 0xea, 0xc6, 0x94, 0x6f, 0xc1, 0x0a, 0x73, 0xed, 0xed, 0xb4, 0x19, 0xab,
	0x20, 0x33, 0x8a, 0xca, 0x48, 0xea, 0xb1, 0xeb, 0x60, 0xf9, 0x15, 0xb4, 0x0b, 0x5b, 0x49, 0xea,
	0x81, 0xa6, 0x9f, 0x78, 0x9a, 0x7e, 0xa2, 0x52, 0x3d, 0xb2, 0xa4, 0xd4, 0x59, 0x61, 0x4b, 0x26,
	0xce, 0xe4, 0x64, 0x9f, 0x90, 0x37, 0xca, 0x3b, 0xac, 0x88, 0xa5, 0xd7, 0x06, 0x1e, 0xba, 0x39,
	0xec, 0x40, 0x89, 0xd3, 0x75, 0x45, 0x9b, 0xb5, 0x9b, 0x3a, 0x77, 0xa3, 0x99, 0x1a, 0x37, 0xa8,
	0xf4, 0x43, 0x79, 0x03, 0x96, 0x8f, 0xdc, 0x97, 0xc3, 0xf4, 0x2b, 0x09, 0xd0, 0xe8, 0xda, 0x2f,
	0x02, 0xd3, 0xb0, 0x4d, 0x67, 0xeb, 0xa9, 0xca, 0xa2, 0xa8, 0x19, 0x8c, 0x7d, 0x19, 0xca, 0x9a,
	0xe5, 0x63, 0xcd, 0x18, 0xa8, 0x8e, 0x1b, 0x92, 0xee, 0x80, 0xa5, 0x6b, 0xbe, 0x5d, 0x12, 0xd4,
	0x63, 0x46, 0x54, 0x6e, 0x42, 0xe5, 0x2e, 0x0e, 0xd3, 0x9d, 0x69, 0xd7, 0xa4, 0x55, 0xe7, 0x9c,
	0xf7, 0x89, 0xf2, 0x36, 0x6c, 0x4f, 0xdd, 0x81, 0xf5, 0x37, 0x79, 0xbd, 0x6b, 0x26, 0x0b, 0xde,
	0xce, 0xe4, 0x26, 0x55, 0x34, 0xb8, 0x0b, 0x3a, 0x5f, 0xac, 0xbc, 0x33, 0x11, 0xd9, 0xc1, 0x80,
	0x57, 0xe9, 0xf3, 0xdc, 0x74, 0x93, 0xc7, 0x8a, 0x29, 0x80, 0xf9, 0xc6, 0x43, 0xc0, 0x89, 0xa8,
	0x9c, 0x0d, 0x98, 0x25, 0xc9, 0xbb, 0xb0, 0xfd, 0xc8, 0x33, 0xb4, 0x10, 0x4f, 0xe8, 0xbd, 0xf1,
	0xe3, 0x61, 0xbf, 0x9e, 0xd8, 0xf9, 0x1c, 0xfd, 0x3a, 0xdb, 0x7a, 0x0f, 0x76, 0xa6, 0x6f, 0x1d,
	0x78, 0x4a, 0x1b, 0x36, 0x53, 0xfc, 0x03, 0x31, 0x24, 0x50, 0xbd, 0x5f, 0x1b, 0xb3, 0x67, 0xe6,
	0x80, 0x11, 0x9b, 0xb3, 0x0d, 0x5b, 0x53, 0xf6, 0x0c, 0x3c, 0xe5, 0xd7, 0x12, 0xac, 0xdd, 0x21,
	0x3e, 0x8d, 0xb1, 0xde, 0xd3, 0x7c, 0x13, 0xcf, 0xb8, 0x96, 0x2e, 0x42, 0xd1, 0x17, 0x52, 0xac,
	0x95, 0x11, 0xb7, 0x52, 0x44, 0xa3, 0x23, 0xe7, 0xab, 0x50, 0x8a, 0x45, 0xe2, 0x71, 0xb7, 0xd0,
	0x8e, 0xd7, 0xb1, 0x70, 0xee, 0xc3, 0x72, 0x2c, 0xe4, 0xfa, 0x06, 0xf6, 0x87, 0xf7, 0xd4, 0x52,
	0xc4, 0xf8, 0x36, 0xa5, 0xb7, 0x0c, 0xa5, 0x0e, 0xeb, 0x93, 0xe0, 0x05, 0x1e, 0x1d, 0x8e, 0x79,
	0x18, 0xbc, 0x13, 0x53, 0x8d, 0x81, 0xf2, 0xd0, 0x3c, 0x38, 0x31, 0x5b, 0xc6, 0xfe, 0xef, 0x33,
	0xb0, 0x78, 0x98, 0x48, 0x23, 0x79, 0x78, 0x54, 0x1d, 0x5e, 0xc2, 0x64, 0x28, 0x9a, 0x9a, 0x8d,
	0x55, 0xdd, 0x75, 0x4c, 0x8b, 0x38, 0xb2, 0x84, 0x8a, 0xd4, 0xc9, 0xaa, 0xad, 0x85, 0x7a, 0x4f,
	0xce, 0x20, 0x04, 0x65, 0xc6, 0xff, 0xa0, 0xe7, 0x3a, 0xa6, 0xde, 0xd3, 0x5c, 0x39, 0x8b, 0xca,
	0x00, 0x7a, 0x57, 0x35, 0x49, 0x37, 0xf4, 0x4e, 0x4c, 0x79, 0x8e, 0xae, 0x60, 0x32, 0xc4, 0xb8,
	0x21, 0xcf, 0xa3, 0x15, 0x58, 0xd2, 0x2c, 0x72, 0x8a, 0x1d, 0x1c, 0x04, 0xfc, 0x3a, 0x93, 0x73,
	0x68, 0x13, 0x56, 0x79, 0x03, 0xd8, 0xa5, 0xa6, 0xa8, 0x91, 0x8d, 0xf2, 0x02, 0xad, 0xac, 0x7a,
	0x4f, 0x0b, 0x55, 0xcd, 0x71, 0xc8, 0x29, 0xf6, 0x03, 0xcd, 0x1f, 0xc8, 0x79, 0xb4, 0x0e, 0x48,
	0xef, 0xaa, 0x21, 0x76, 0x52, 0xf4, 0x02, 0xda, 0x80, 0x15, 0x9b, 0x18, 0xaa, 0xd6, 0x0f, 0xfb,
	0xb6, 0x33, 0xdc, 0x06, 0xd0, 0x36, 0x6c, 0x4c, 0x60, 0xa8, 0x0e, 0x7e, 0x22, 0x2f, 0xa2, 0x2d,
	0x58, 0x31, 0x68, 0x01, 0x8a, 0x6f, 0xd7, 0x0e, 0x36, 0x89, 0x23, 0xff, 0xe1, 0xc7, 0xdf, 0x44,
	0xbb, 0xb0, 0xa1, 0xe9, 0xf4, 0x96, 0x4b, 0xb2, 0x2d, 0x7a, 0x03, 0xc8, 0x3f, 0xfc, 0xd9, 0x07,
	0xfb, 0x9f, 0x65, 0x60, 0x51, 0xbc, 0x34, 0x30, 0x07, 0x96, 0xa3, 0xcc, 0x17, 0xee, 0xcb, 0xc3,
	0x5c, 0x40, 0x4c, 0xea, 0xb6, 0x3c, 0xcc, 0x99, 0xbe, 0xd6, 0x91, 0x33, 0x68, 0x11, 0x16, 0xf4,
	0x9e, 0xe6, 0x38, 0xd8, 0x92, 0xb3, 0x94, 0x4c, 0x9d, 0x21, 0xcf, 0x21, 0x80, 0x9c, 0xde, 0x65,
	0x80, 0xe6, 0x99, 0x48, 0x57, 0x65, 0x2b, 0x73, 0xc2, 0xe1, 0x41, 0x4f, 0xf3, 0xa9, 0x3f, 0xf6,
	0xa0, 0x42, 0x8c, 0x1b, 0xaa, 0xd8, 0x41, 0x75, 0x1d, 0x8b, 0x38, 0x58, 0x25, 0xce, 0x29, 0x09,
	0x31, 0x96, 0xf3, 0x33, 0xf9, 0xbe, 0x5c, 0xa0, 0x01, 0xa3, 0xfc, 0xc7, 0x7d, 0xa2, 0x9f, 0xf0,
	0x20, 0x42, 0x44, 0x73, 0xf0, 0x13, 0x21, 0x28, 0x2f, 0x52, 0x87, 0xf1, 0x88, 0xc4, 0xbe, 0xb2,
	0xdc, 0x27, 0x2c, 0xa8, 0x72, 0x11, 0xed, 0xc0, 0xe6, 0x08, 0xb3, 0x47, 0xcc, 0x1e, 0xe7, 0x96,
	0xf6, 0xdf, 0x03, 0x74, 0x57, 0xb3, 0x71, 0xcb, 0xb8, 0x21, 0x72, 0xeb, 0x8e, 0xa5, 0x99, 0x68,
	0x19, 0x4a, 0x54, 0x49, 0xd7, 0xd2, 0xcc, 0xc8, 0x3b, 0x6b, 0xb0, 0x1c, 0x93, 0x7a, 0xae, 0x65,
	0xa8, 0x36, 0xd1, 0x65, 0x89, 0xaa, 0x8e, 0xc9, 0x69, 0x3b, 0xe4, 0xcc, 0xfe, 0x21, 0xc8, 0xc9,
	0xcb, 0x82, 0x79, 0x7d, 0x1d, 0x50, 0x3a, 0x40, 0x62, 0xff, 0x8d, 0xd1, 0xb8, 0xf2, 0xc0, 0x49,
	0xfb, 0x7f, 0x91, 0x60, 0x7d, 0xbc, 0xbe, 0x88, 0x4a, 0xba, 0xcc, 0x49, 0xe9, 0xab, 0x7c, 0x0f,
	0x2a, 0x29, 0xf2, 0xc8, 0x5d, 0x8e, 0xb6, 0x60, 0x2d, 0xc5, 0x7f, 0x13, 0x6b, 0xc6, 0x13, 0xac,
	0xf9, 0x72, 0x86, 0x9a, 0x93, 0x62, 0x1d, 0x61, 0xdd, 0xf5, 0xb5, 0x90, 0xb8, 0x8e, 0x9c, 0xa5,
	0x9e, 0x4c, 0x31, 0xdb, 0xd8, 0x50, 0x45, 0x8b, 0x25, 0xcf, 0xd1, 0xe4, 0x4f, 0x71, 0x6f, 0x3f,
	0xf5, 0xe4, 0x79, 0x6a, 0x6e, 0x8a, 0x7a, 0x1f, 0x1b, 0x9a, 0x25, 0xe7, 0xae, 0xff, 0x6d, 0x19,
	0xa2, 0x97, 0x3c, 0xf4, 0x23, 0x09, 0x0a, 0xf1, 0xbb, 0x04, 0x5a, 0x8b, 0xcb, 0x60, 0xf2, 0xad,
	0xa2, 0xb2, 0x73, 0x35, 0x7e, 0xe8, 0xbb, 0xfa, 0xf0, 0xad, 0x03, 0xfe, 0xd0, 0x77, 0xdb, 0xf6,
	0xc2, 0x81, 0xfa, 0xe0, 0x40, 0xb9, 0xf9, 0x83, 0x67, 0x2f, 0xb2, 0xd2, 0xcf, 0x9f, 0xbd, 0xc8,
	0xe6, 0xfa, 0xf5, 0xb0, 0xee, 0xd4, 0x3f, 0x7b, 0xf6, 0x22, 0xfb, 0x5a, 0xad, 0xdf, 0xe8, 0x13,
	0xa3, 0x59, 0xad, 0x85, 0x8d, 0xe4, 0x5b, 0x57, 0xb3, 0xfa, 0x5e, 0xcd, 0xa9, 0x46, 0x14, 0xf6,
	0x84, 0xf1, 0x3e, 0x05, 0x81, 0xc6, 0x1f, 0x11, 0xd0, 0x5e, 0x8c, 0x66, 0xe2, 0x83, 0x45, 0xe5,
	0xc2, 0x4c, 0x7e, 0xe0, 0x29, 0xaf, 0x51, 0x64, 0x19, 0x8a, 0x6c, 0x8e, 0x22, 0xa3, 0xb8, 0x36,
	0xa7, 0xe1, 0x42, 0x9f, 0x48, 0x00, 0xc3, 0xc7, 0x03, 0x34, 0x9c, 0x41, 0x52, 0x2f, 0x0a, 0x67,
	0xf8, 0xe2, 0x2e, 0xd5, 0x98, 0xa5, 0x1a, 0xf3, 0x54, 0xa3, 0x56, 0x0f, 0x98, 0xd6, 0xaf, 0x24,
	0xb5, 0x9e, 0x24, 0xbd, 0x51, 0xd3, 0x1a, 0xd1, 0xf0, 0xd1, 0xac, 0xd6, 0x82, 0x06, 0x7f, 0x31,
	0x68, 0xa2, 0xef, 0xc1, 0xd2, 0xc8, 0xf0, 0x89, 0xb6, 0x93, 0xe6, 0x8e, 0x8c, 0xb9, 0x95, 0x9d,
	0xe9, 0xcc, 0xc0, 0x53, 0x6a, 0x14, 0xd6, 0x1c, 0x73, 0x44, 0x58, 0xb7, 0x18, 0xa4, 0xca, 0x38,
	0x14, 0xab, 0xc1, 0x06, 0xdf, 0x26, 0xfa, 0x10, 0x96, 0xc7, 0x26, 0x4b, 0xb4, 0x3b, 0xea, 0xed,
	0xd4, 0x0c, 0x5b, 0xd9, 0x9b, 0xc5, 0x8e, 0x62, 0x31, 0x7f, 0xce, 0x58, 0x7c, 0x2c, 0xb1, 0xb6,
	0x75, 0x74, 0x60, 0x44, 0x17, 0xa6, 0x28, 0x89, 0x26, 0xd5, 0x4a, 0x75, 0xb6, 0x40, 0xe0, 0x29,
	0x57, 0x29, 0x8e, 0x1c, 0xc3, 0x61, 0x08, 0x1c, 0xdb, 0x35, 0xa3, 0x61, 0xe0, 0x53, 0x75, 0x22,
	0x94, 0x5f, 0x4a, 0x50, 0x4a, 0x0d, 0x9a, 0x68, 0x2b, 0xd6, 0x31, 0x3a, 0x80, 0x9e, 0x91, 0x1c,
	0xdf, 0xa1, 0xaa, 0x17, 0x12, 0xc9, 0xc1, 0x8f, 0x4a, 0x7d, 0xea, 0x51, 0x49, 0x27, 0x87, 0xd3,
	0x18, 0x7d, 0x25, 0xa1, 0x91, 0x5a, 0x1a, 0x69, 0xef, 0xd3, 0x69, 0x32, 0xd2, 0xa0, 0xa7, 0xd3,
	0x64, 0xb4, 0x03, 0x57, 0xbe, 0x4a, 0x01, 0xe6, 0x53, 0x31, 0xda, 0x4b, 0x80, 0x4b, 0xd7, 0x48,
	0xee, 0x9e, 0xef, 0x43, 0x39, 0xdd, 0xca, 0xa3, 0x4a, 0xa2, 0x86, 0x8c, 0xaa, 0xdf, 0x9e, 0xca,
	0x8b, 0xb4, 0x17, 0xfe, 0x23, 0xed, 0x3f, 0x95, 0xe2, 0x17, 0x95, 0xd4, 0xbb, 0xe0, 0x99, 0xa5,
	0xe3, 0xe2, 0x14, 0xfe, 0xf0, 0x15, 0x92, 0x27, 0x2c, 0x9c, 0x33, 0x61, 0x3f, 0x92, 0xd8, 0x0b,
	0xc7, 0xa4, 0x96, 0x1f, 0xbd, 0x9a, 0xd4, 0x35, 0x65, 0xac, 0xa8, 0x5c, 0x3a, 0x5b, 0x28, 0xf0,
	0x94, 0x8b, 0x14, 0xd3, 0x22, 0xc5, 0x94, 0xe1, 0x88, 0xe4, 0x31, 0x24, 0x9f, 0x4f, 0x46, 0x42,
	0x7b, 0xf9, 0xd9, 0x48, 0xc4, 0x18, 0x31, 0x1b, 0x49, 0x34, 0x12, 0x28, 0xd7, 0x28, 0x92, 0xa2,
	0xa8, 0x28, 0xbc, 0xc8, 0xed, 0x8c, 0xe7, 0x6f, 0x50, 0x6d, 0xf0, 0x71, 0xa3, 0x89, 0xfe, 0x29,
	0x8d, 0x34, 0xe4, 0xc9, 0xbf, 0x84, 0x86, 0x3a, 0x67, 0x8c, 0x0b, 0x95, 0xcb, 0xe7, 0x90, 0x0a,
	0x3c, 0xe5, 0x27, 0x12, 0xc5, 0x56, 0xa2, 0xd8, 0xca, 0x14, 0x9b, 0x56, 0xef, 0xd4, 0xbd, 0xba,
	0x53, 0xb7, 0x19, 0xca, 0xde, 0x2c, 0x94, 0x23, 0x27, 0xae, 0x53, 0x6d, 0x24, 0xff, 0x35, 0x6a,
	0x56, 0x6b, 0x5e, 0x63, 0xf8, 0x52, 0x41, 0x8f, 0x64, 0xb5, 0x11, 0xff, 0x69, 0xd4, 0xac, 0xd6,
	0xec, 0xc6, 0xf0, 0x5f, 0xa2, 0x26, 0xfa, 0xa3, 0x04, 0x6b, 0x13, 0xc7, 0x05, 0x74, 0x71, 0xb2,
	0x21, 0x89, 0x11, 0xa5, 0xa2, 0x9c, 0x25, 0x12, 0x78, 0x8a, 0x41, 0xed, 0x2c, 0x53, 0x3b, 0x21,
	0xb2, 0x93, 0xdb, 0xf8, 0xd6, 0x39, 0x6d, 0x14, 0x6f, 0x17, 0xdc, 0xd2, 0xc4, 0x57, 0x64, 0x87,
	0x1d, 0x98, 0x4d, 0xf4, 0x5b, 0x09, 0xd0, 0xf8, 0xe0, 0x90, 0x38, 0x61, 0x13, 0x87, 0x9e, 0xc4,
	0xe5, 0x3c, 0x79, 0xea, 0xe0, 0xd5, 0x70, 0x4f, 0x54, 0x43, 0xa7, 0xee, 0x8a, 0x33, 0x56, 0xaf,
	0xf5, 0xab, 0xe2, 0x90, 0x39, 0x8d, 0xe4, 0x90, 0xd4, 0xac, 0xd6, 0xdc, 0x6a, 0x23, 0x9a, 0x72,
	0xe8, 0x19, 0xac, 0x36, 0x52, 0x13, 0x52, 0xb3, 0x92, 0xfb, 0xf8, 0xd9, 0x8b, 0xec, 0x5f, 0x07,
	0x07, 0xf2, 0x9f, 0x9e, 0xef, 0x49, 0x7f, 0x7e, 0xbe, 0x27, 0xfd, 0xfd, 0xf9, 0x9e, 0xf4, 0xe9,
	0x3f, 0xf6, 0x5e, 0xe9, 0xe4, 0xd8, 0x3f, 0x92, 0xaf, 0xff, 0x3b, 0x00, 0x00, 0xff, 0xff, 0x87,
	0x23, 0xad, 0x85, 0xe0, 0x1c, 0x00, 0x00,
}
