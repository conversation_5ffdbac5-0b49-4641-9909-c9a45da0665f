// Code generated by protoc-gen-gogo.
// source: services/circletimeline/circletimeline.proto
// DO NOT EDIT!

/*
	Package circletimeline is a generated protocol buffer package.

	namespace

	It is generated from these files:
		services/circletimeline/circletimeline.proto

	It has these top-level messages:
		CircleTimelineMsg
		NewMsgComment
		MyTopic
		DeleteNewMsgComment
		DeleteNewMsgTopic
		ActBegin
		ActEnd
		WriteTimelineMsgReq
		WriteTimelineMsgResp
		BatchDeleteTimelineReq
		BatchDeleteTimelineResp
		PullTimelineMsgReq
		PullTimelineMsgResp
		MarkReadedReq
		MarkReadedResp
		GetReadedReq
		GetReadedResp
		GetActMessagesReq
		GetActMessagesResp
*/
package circletimeline

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type CircleTimelineMsg_TYPE int32

const (
	CircleTimelineMsg_NEW_MSG_COMMENT                   CircleTimelineMsg_TYPE = 1
	CircleTimelineMsg_MY_TOPIC                          CircleTimelineMsg_TYPE = 2
	CircleTimelineMsg_DELETE_NEW_MSG_COMMENT            CircleTimelineMsg_TYPE = 3
	CircleTimelineMsg_DELETE_NEW_MSG_TOPIC              CircleTimelineMsg_TYPE = 4
	CircleTimelineMsg_ACT_BEGIN                         CircleTimelineMsg_TYPE = 5
	CircleTimelineMsg_ACT_END                           CircleTimelineMsg_TYPE = 6
	CircleTimelineMsg_GUILD_CIRCLE_NEW_MSG_COMMENT      CircleTimelineMsg_TYPE = 7
	CircleTimelineMsg_GUILD_CIRCLE_DELETE_NEW_MSG_TOPIC CircleTimelineMsg_TYPE = 8
	CircleTimelineMsg_GUILD_CIRCLE_MY_TOPIC             CircleTimelineMsg_TYPE = 9
)

var CircleTimelineMsg_TYPE_name = map[int32]string{
	1: "NEW_MSG_COMMENT",
	2: "MY_TOPIC",
	3: "DELETE_NEW_MSG_COMMENT",
	4: "DELETE_NEW_MSG_TOPIC",
	5: "ACT_BEGIN",
	6: "ACT_END",
	7: "GUILD_CIRCLE_NEW_MSG_COMMENT",
	8: "GUILD_CIRCLE_DELETE_NEW_MSG_TOPIC",
	9: "GUILD_CIRCLE_MY_TOPIC",
}
var CircleTimelineMsg_TYPE_value = map[string]int32{
	"NEW_MSG_COMMENT":                   1,
	"MY_TOPIC":                          2,
	"DELETE_NEW_MSG_COMMENT":            3,
	"DELETE_NEW_MSG_TOPIC":              4,
	"ACT_BEGIN":                         5,
	"ACT_END":                           6,
	"GUILD_CIRCLE_NEW_MSG_COMMENT":      7,
	"GUILD_CIRCLE_DELETE_NEW_MSG_TOPIC": 8,
	"GUILD_CIRCLE_MY_TOPIC":             9,
}

func (x CircleTimelineMsg_TYPE) Enum() *CircleTimelineMsg_TYPE {
	p := new(CircleTimelineMsg_TYPE)
	*p = x
	return p
}
func (x CircleTimelineMsg_TYPE) String() string {
	return proto.EnumName(CircleTimelineMsg_TYPE_name, int32(x))
}
func (x *CircleTimelineMsg_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(CircleTimelineMsg_TYPE_value, data, "CircleTimelineMsg_TYPE")
	if err != nil {
		return err
	}
	*x = CircleTimelineMsg_TYPE(value)
	return nil
}
func (CircleTimelineMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{0, 0}
}

type NewMsgComment_MSG_TYPE int32

const (
	NewMsgComment_COMMENT NewMsgComment_MSG_TYPE = 1
	NewMsgComment_LIKE    NewMsgComment_MSG_TYPE = 2
)

var NewMsgComment_MSG_TYPE_name = map[int32]string{
	1: "COMMENT",
	2: "LIKE",
}
var NewMsgComment_MSG_TYPE_value = map[string]int32{
	"COMMENT": 1,
	"LIKE":    2,
}

func (x NewMsgComment_MSG_TYPE) Enum() *NewMsgComment_MSG_TYPE {
	p := new(NewMsgComment_MSG_TYPE)
	*p = x
	return p
}
func (x NewMsgComment_MSG_TYPE) String() string {
	return proto.EnumName(NewMsgComment_MSG_TYPE_name, int32(x))
}
func (x *NewMsgComment_MSG_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NewMsgComment_MSG_TYPE_value, data, "NewMsgComment_MSG_TYPE")
	if err != nil {
		return err
	}
	*x = NewMsgComment_MSG_TYPE(value)
	return nil
}
func (NewMsgComment_MSG_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{1, 0}
}

type NewMsgComment_SYSTEM_MSG_TYPE int32

const (
	NewMsgComment_NONE       NewMsgComment_SYSTEM_MSG_TYPE = 0
	NewMsgComment_SYSTEM_MSG NewMsgComment_SYSTEM_MSG_TYPE = 1
)

var NewMsgComment_SYSTEM_MSG_TYPE_name = map[int32]string{
	0: "NONE",
	1: "SYSTEM_MSG",
}
var NewMsgComment_SYSTEM_MSG_TYPE_value = map[string]int32{
	"NONE":       0,
	"SYSTEM_MSG": 1,
}

func (x NewMsgComment_SYSTEM_MSG_TYPE) Enum() *NewMsgComment_SYSTEM_MSG_TYPE {
	p := new(NewMsgComment_SYSTEM_MSG_TYPE)
	*p = x
	return p
}
func (x NewMsgComment_SYSTEM_MSG_TYPE) String() string {
	return proto.EnumName(NewMsgComment_SYSTEM_MSG_TYPE_name, int32(x))
}
func (x *NewMsgComment_SYSTEM_MSG_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(NewMsgComment_SYSTEM_MSG_TYPE_value, data, "NewMsgComment_SYSTEM_MSG_TYPE")
	if err != nil {
		return err
	}
	*x = NewMsgComment_SYSTEM_MSG_TYPE(value)
	return nil
}
func (NewMsgComment_SYSTEM_MSG_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{1, 1}
}

type MyTopic_TOPIC_TYPE int32

const (
	MyTopic_NORMAL MyTopic_TOPIC_TYPE = 0
	MyTopic_TOP    MyTopic_TOPIC_TYPE = 1
)

var MyTopic_TOPIC_TYPE_name = map[int32]string{
	0: "NORMAL",
	1: "TOP",
}
var MyTopic_TOPIC_TYPE_value = map[string]int32{
	"NORMAL": 0,
	"TOP":    1,
}

func (x MyTopic_TOPIC_TYPE) Enum() *MyTopic_TOPIC_TYPE {
	p := new(MyTopic_TOPIC_TYPE)
	*p = x
	return p
}
func (x MyTopic_TOPIC_TYPE) String() string {
	return proto.EnumName(MyTopic_TOPIC_TYPE_name, int32(x))
}
func (x *MyTopic_TOPIC_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(MyTopic_TOPIC_TYPE_value, data, "MyTopic_TOPIC_TYPE")
	if err != nil {
		return err
	}
	*x = MyTopic_TOPIC_TYPE(value)
	return nil
}
func (MyTopic_TOPIC_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{2, 0}
}

// ------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
// ------------------------------------------
type CircleTimelineMsg struct {
	Type   uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Seqid  uint32 `protobuf:"varint,2,req,name=seqid" json:"seqid"`
	MsgBin []byte `protobuf:"bytes,3,req,name=msg_bin,json=msgBin" json:"msg_bin"`
}

func (m *CircleTimelineMsg) Reset()                    { *m = CircleTimelineMsg{} }
func (m *CircleTimelineMsg) String() string            { return proto.CompactTextString(m) }
func (*CircleTimelineMsg) ProtoMessage()               {}
func (*CircleTimelineMsg) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{0} }

func (m *CircleTimelineMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *CircleTimelineMsg) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *CircleTimelineMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

// 1、新消息  评论
type NewMsgComment struct {
	CommentId         uint32   `protobuf:"varint,1,req,name=comment_id,json=commentId" json:"comment_id"`
	CircleId          uint32   `protobuf:"varint,2,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId           uint32   `protobuf:"varint,3,req,name=topic_id,json=topicId" json:"topic_id"`
	Type              uint32   `protobuf:"varint,4,req,name=type" json:"type"`
	Content           string   `protobuf:"bytes,5,req,name=content" json:"content"`
	SenderAccount     string   `protobuf:"bytes,6,req,name=sender_account,json=senderAccount" json:"sender_account"`
	SenderUid         uint32   `protobuf:"varint,7,req,name=sender_uid,json=senderUid" json:"sender_uid"`
	SendTime          uint32   `protobuf:"varint,8,req,name=send_time,json=sendTime" json:"send_time"`
	RepliedCommentId  uint32   `protobuf:"varint,9,req,name=replied_comment_id,json=repliedCommentId" json:"replied_comment_id"`
	RepliedAccount    string   `protobuf:"bytes,10,req,name=replied_account,json=repliedAccount" json:"replied_account"`
	RepliedUid        uint32   `protobuf:"varint,11,req,name=replied_uid,json=repliedUid" json:"replied_uid"`
	RepliedContent    string   `protobuf:"bytes,12,req,name=replied_content,json=repliedContent" json:"replied_content"`
	MsgId             uint32   `protobuf:"varint,13,req,name=msg_id,json=msgId" json:"msg_id"`
	SystemMsgType     uint32   `protobuf:"varint,14,opt,name=system_msg_type,json=systemMsgType" json:"system_msg_type"`
	CommentImgKeyList []string `protobuf:"bytes,15,rep,name=comment_img_key_list,json=commentImgKeyList" json:"comment_img_key_list,omitempty"`
	RepliedImgKeyList []string `protobuf:"bytes,16,rep,name=replied_img_key_list,json=repliedImgKeyList" json:"replied_img_key_list,omitempty"`
	ParentCommentId   uint32   `protobuf:"varint,17,opt,name=parent_comment_id,json=parentCommentId" json:"parent_comment_id"`
}

func (m *NewMsgComment) Reset()                    { *m = NewMsgComment{} }
func (m *NewMsgComment) String() string            { return proto.CompactTextString(m) }
func (*NewMsgComment) ProtoMessage()               {}
func (*NewMsgComment) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{1} }

func (m *NewMsgComment) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *NewMsgComment) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *NewMsgComment) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *NewMsgComment) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *NewMsgComment) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *NewMsgComment) GetSenderAccount() string {
	if m != nil {
		return m.SenderAccount
	}
	return ""
}

func (m *NewMsgComment) GetSenderUid() uint32 {
	if m != nil {
		return m.SenderUid
	}
	return 0
}

func (m *NewMsgComment) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *NewMsgComment) GetRepliedCommentId() uint32 {
	if m != nil {
		return m.RepliedCommentId
	}
	return 0
}

func (m *NewMsgComment) GetRepliedAccount() string {
	if m != nil {
		return m.RepliedAccount
	}
	return ""
}

func (m *NewMsgComment) GetRepliedUid() uint32 {
	if m != nil {
		return m.RepliedUid
	}
	return 0
}

func (m *NewMsgComment) GetRepliedContent() string {
	if m != nil {
		return m.RepliedContent
	}
	return ""
}

func (m *NewMsgComment) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *NewMsgComment) GetSystemMsgType() uint32 {
	if m != nil {
		return m.SystemMsgType
	}
	return 0
}

func (m *NewMsgComment) GetCommentImgKeyList() []string {
	if m != nil {
		return m.CommentImgKeyList
	}
	return nil
}

func (m *NewMsgComment) GetRepliedImgKeyList() []string {
	if m != nil {
		return m.RepliedImgKeyList
	}
	return nil
}

func (m *NewMsgComment) GetParentCommentId() uint32 {
	if m != nil {
		return m.ParentCommentId
	}
	return 0
}

// 2、我发表的主题
type MyTopic struct {
	CircleId      uint32   `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId       uint32   `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	Type          uint32   `protobuf:"varint,3,req,name=type" json:"type"`
	SenderAccount string   `protobuf:"bytes,4,req,name=sender_account,json=senderAccount" json:"sender_account"`
	SenderUid     uint32   `protobuf:"varint,5,req,name=sender_uid,json=senderUid" json:"sender_uid"`
	SendTime      uint32   `protobuf:"varint,6,req,name=send_time,json=sendTime" json:"send_time"`
	Title         string   `protobuf:"bytes,7,req,name=title" json:"title"`
	Content       string   `protobuf:"bytes,8,req,name=content" json:"content"`
	ImgKeyList    []string `protobuf:"bytes,9,rep,name=img_key_list,json=imgKeyList" json:"img_key_list,omitempty"`
}

func (m *MyTopic) Reset()                    { *m = MyTopic{} }
func (m *MyTopic) String() string            { return proto.CompactTextString(m) }
func (*MyTopic) ProtoMessage()               {}
func (*MyTopic) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{2} }

func (m *MyTopic) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *MyTopic) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *MyTopic) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *MyTopic) GetSenderAccount() string {
	if m != nil {
		return m.SenderAccount
	}
	return ""
}

func (m *MyTopic) GetSenderUid() uint32 {
	if m != nil {
		return m.SenderUid
	}
	return 0
}

func (m *MyTopic) GetSendTime() uint32 {
	if m != nil {
		return m.SendTime
	}
	return 0
}

func (m *MyTopic) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *MyTopic) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *MyTopic) GetImgKeyList() []string {
	if m != nil {
		return m.ImgKeyList
	}
	return nil
}

// 3、删除评论/赞
type DeleteNewMsgComment struct {
	CircleId  uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId   uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	CommentId uint32 `protobuf:"varint,3,req,name=comment_id,json=commentId" json:"comment_id"`
	Type      uint32 `protobuf:"varint,4,req,name=type" json:"type"`
	SenderUid uint32 `protobuf:"varint,5,req,name=sender_uid,json=senderUid" json:"sender_uid"`
}

func (m *DeleteNewMsgComment) Reset()         { *m = DeleteNewMsgComment{} }
func (m *DeleteNewMsgComment) String() string { return proto.CompactTextString(m) }
func (*DeleteNewMsgComment) ProtoMessage()    {}
func (*DeleteNewMsgComment) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{3}
}

func (m *DeleteNewMsgComment) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *DeleteNewMsgComment) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *DeleteNewMsgComment) GetCommentId() uint32 {
	if m != nil {
		return m.CommentId
	}
	return 0
}

func (m *DeleteNewMsgComment) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *DeleteNewMsgComment) GetSenderUid() uint32 {
	if m != nil {
		return m.SenderUid
	}
	return 0
}

// 4、删除主题
type DeleteNewMsgTopic struct {
	CircleId uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *DeleteNewMsgTopic) Reset()                    { *m = DeleteNewMsgTopic{} }
func (m *DeleteNewMsgTopic) String() string            { return proto.CompactTextString(m) }
func (*DeleteNewMsgTopic) ProtoMessage()               {}
func (*DeleteNewMsgTopic) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{4} }

func (m *DeleteNewMsgTopic) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *DeleteNewMsgTopic) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// 5、活动上线
type ActBegin struct {
	CircleId   uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId    uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
	ActDesc    string `protobuf:"bytes,3,req,name=act_desc,json=actDesc" json:"act_desc"`
	ExpireTime uint64 `protobuf:"varint,4,req,name=expire_time,json=expireTime" json:"expire_time"`
}

func (m *ActBegin) Reset()                    { *m = ActBegin{} }
func (m *ActBegin) String() string            { return proto.CompactTextString(m) }
func (*ActBegin) ProtoMessage()               {}
func (*ActBegin) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{5} }

func (m *ActBegin) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *ActBegin) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

func (m *ActBegin) GetActDesc() string {
	if m != nil {
		return m.ActDesc
	}
	return ""
}

func (m *ActBegin) GetExpireTime() uint64 {
	if m != nil {
		return m.ExpireTime
	}
	return 0
}

// 6、活动下线
type ActEnd struct {
	CircleId uint32 `protobuf:"varint,1,req,name=circle_id,json=circleId" json:"circle_id"`
	TopicId  uint32 `protobuf:"varint,2,req,name=topic_id,json=topicId" json:"topic_id"`
}

func (m *ActEnd) Reset()                    { *m = ActEnd{} }
func (m *ActEnd) String() string            { return proto.CompactTextString(m) }
func (*ActEnd) ProtoMessage()               {}
func (*ActEnd) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{6} }

func (m *ActEnd) GetCircleId() uint32 {
	if m != nil {
		return m.CircleId
	}
	return 0
}

func (m *ActEnd) GetTopicId() uint32 {
	if m != nil {
		return m.TopicId
	}
	return 0
}

// ------------------------------------------
// 读写协议
// ------------------------------------------
type WriteTimelineMsgReq struct {
	Id     uint32             `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string             `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	Msg    *CircleTimelineMsg `protobuf:"bytes,3,req,name=msg" json:"msg,omitempty"`
}

func (m *WriteTimelineMsgReq) Reset()         { *m = WriteTimelineMsgReq{} }
func (m *WriteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgReq) ProtoMessage()    {}
func (*WriteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{7}
}

func (m *WriteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WriteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *WriteTimelineMsgReq) GetMsg() *CircleTimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type WriteTimelineMsgResp struct {
}

func (m *WriteTimelineMsgResp) Reset()         { *m = WriteTimelineMsgResp{} }
func (m *WriteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*WriteTimelineMsgResp) ProtoMessage()    {}
func (*WriteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{8}
}

// ------------------------------------------
// 根据seq删除消息
// ------------------------------------------
type BatchDeleteTimelineReq struct {
	Id        uint32   `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix    string   `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	SeqIdList []uint32 `protobuf:"varint,3,rep,name=seq_id_list,json=seqIdList" json:"seq_id_list,omitempty"`
}

func (m *BatchDeleteTimelineReq) Reset()         { *m = BatchDeleteTimelineReq{} }
func (m *BatchDeleteTimelineReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteTimelineReq) ProtoMessage()    {}
func (*BatchDeleteTimelineReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{9}
}

func (m *BatchDeleteTimelineReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BatchDeleteTimelineReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *BatchDeleteTimelineReq) GetSeqIdList() []uint32 {
	if m != nil {
		return m.SeqIdList
	}
	return nil
}

type BatchDeleteTimelineResp struct {
}

func (m *BatchDeleteTimelineResp) Reset()         { *m = BatchDeleteTimelineResp{} }
func (m *BatchDeleteTimelineResp) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteTimelineResp) ProtoMessage()    {}
func (*BatchDeleteTimelineResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{10}
}

// ------------------------------------------
// 拉取timeline
// ------------------------------------------
type PullTimelineMsgReq struct {
	Id         uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix     string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	StartSeqid uint32 `protobuf:"varint,3,req,name=start_seqid,json=startSeqid" json:"start_seqid"`
	Limit      uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
	IsRev      uint32 `protobuf:"varint,5,opt,name=is_rev,json=isRev" json:"is_rev"`
	EndSeqid   uint32 `protobuf:"varint,6,opt,name=end_seqid,json=endSeqid" json:"end_seqid"`
}

func (m *PullTimelineMsgReq) Reset()         { *m = PullTimelineMsgReq{} }
func (m *PullTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgReq) ProtoMessage()    {}
func (*PullTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{11}
}

func (m *PullTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PullTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *PullTimelineMsgReq) GetStartSeqid() uint32 {
	if m != nil {
		return m.StartSeqid
	}
	return 0
}

func (m *PullTimelineMsgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullTimelineMsgReq) GetIsRev() uint32 {
	if m != nil {
		return m.IsRev
	}
	return 0
}

func (m *PullTimelineMsgReq) GetEndSeqid() uint32 {
	if m != nil {
		return m.EndSeqid
	}
	return 0
}

type PullTimelineMsgResp struct {
	MsgList []*CircleTimelineMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *PullTimelineMsgResp) Reset()         { *m = PullTimelineMsgResp{} }
func (m *PullTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*PullTimelineMsgResp) ProtoMessage()    {}
func (*PullTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{12}
}

func (m *PullTimelineMsgResp) GetMsgList() []*CircleTimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

// ------------------------------------------
// 标记已读
// ------------------------------------------
type MarkReadedReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	Seqid  uint32 `protobuf:"varint,3,req,name=seqid" json:"seqid"`
}

func (m *MarkReadedReq) Reset()                    { *m = MarkReadedReq{} }
func (m *MarkReadedReq) String() string            { return proto.CompactTextString(m) }
func (*MarkReadedReq) ProtoMessage()               {}
func (*MarkReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{13} }

func (m *MarkReadedReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MarkReadedReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *MarkReadedReq) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

type MarkReadedResp struct {
}

func (m *MarkReadedResp) Reset()                    { *m = MarkReadedResp{} }
func (m *MarkReadedResp) String() string            { return proto.CompactTextString(m) }
func (*MarkReadedResp) ProtoMessage()               {}
func (*MarkReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{14} }

// ------------------------------------------
// 查已读
// ------------------------------------------
type GetReadedReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
}

func (m *GetReadedReq) Reset()                    { *m = GetReadedReq{} }
func (m *GetReadedReq) String() string            { return proto.CompactTextString(m) }
func (*GetReadedReq) ProtoMessage()               {}
func (*GetReadedReq) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{15} }

func (m *GetReadedReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetReadedReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type GetReadedResp struct {
	Seqid uint32 `protobuf:"varint,1,req,name=seqid" json:"seqid"`
}

func (m *GetReadedResp) Reset()                    { *m = GetReadedResp{} }
func (m *GetReadedResp) String() string            { return proto.CompactTextString(m) }
func (*GetReadedResp) ProtoMessage()               {}
func (*GetReadedResp) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{16} }

func (m *GetReadedResp) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

// ------------------------------------------
// 活动消息
// ------------------------------------------
type GetActMessagesReq struct {
	StartSeqId  uint32 `protobuf:"varint,1,req,name=start_seq_id,json=startSeqId" json:"start_seq_id"`
	MaxMsgCount uint32 `protobuf:"varint,2,opt,name=max_msg_count,json=maxMsgCount" json:"max_msg_count"`
}

func (m *GetActMessagesReq) Reset()                    { *m = GetActMessagesReq{} }
func (m *GetActMessagesReq) String() string            { return proto.CompactTextString(m) }
func (*GetActMessagesReq) ProtoMessage()               {}
func (*GetActMessagesReq) Descriptor() ([]byte, []int) { return fileDescriptorCircletimeline, []int{17} }

func (m *GetActMessagesReq) GetStartSeqId() uint32 {
	if m != nil {
		return m.StartSeqId
	}
	return 0
}

func (m *GetActMessagesReq) GetMaxMsgCount() uint32 {
	if m != nil {
		return m.MaxMsgCount
	}
	return 0
}

type GetActMessagesResp struct {
	MsgList []*CircleTimelineMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
	MaxSeq  uint32               `protobuf:"varint,2,req,name=max_seq,json=maxSeq" json:"max_seq"`
}

func (m *GetActMessagesResp) Reset()         { *m = GetActMessagesResp{} }
func (m *GetActMessagesResp) String() string { return proto.CompactTextString(m) }
func (*GetActMessagesResp) ProtoMessage()    {}
func (*GetActMessagesResp) Descriptor() ([]byte, []int) {
	return fileDescriptorCircletimeline, []int{18}
}

func (m *GetActMessagesResp) GetMsgList() []*CircleTimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

func (m *GetActMessagesResp) GetMaxSeq() uint32 {
	if m != nil {
		return m.MaxSeq
	}
	return 0
}

func init() {
	proto.RegisterType((*CircleTimelineMsg)(nil), "circletimeline.CircleTimelineMsg")
	proto.RegisterType((*NewMsgComment)(nil), "circletimeline.NewMsgComment")
	proto.RegisterType((*MyTopic)(nil), "circletimeline.MyTopic")
	proto.RegisterType((*DeleteNewMsgComment)(nil), "circletimeline.DeleteNewMsgComment")
	proto.RegisterType((*DeleteNewMsgTopic)(nil), "circletimeline.DeleteNewMsgTopic")
	proto.RegisterType((*ActBegin)(nil), "circletimeline.ActBegin")
	proto.RegisterType((*ActEnd)(nil), "circletimeline.ActEnd")
	proto.RegisterType((*WriteTimelineMsgReq)(nil), "circletimeline.WriteTimelineMsgReq")
	proto.RegisterType((*WriteTimelineMsgResp)(nil), "circletimeline.WriteTimelineMsgResp")
	proto.RegisterType((*BatchDeleteTimelineReq)(nil), "circletimeline.BatchDeleteTimelineReq")
	proto.RegisterType((*BatchDeleteTimelineResp)(nil), "circletimeline.BatchDeleteTimelineResp")
	proto.RegisterType((*PullTimelineMsgReq)(nil), "circletimeline.PullTimelineMsgReq")
	proto.RegisterType((*PullTimelineMsgResp)(nil), "circletimeline.PullTimelineMsgResp")
	proto.RegisterType((*MarkReadedReq)(nil), "circletimeline.MarkReadedReq")
	proto.RegisterType((*MarkReadedResp)(nil), "circletimeline.MarkReadedResp")
	proto.RegisterType((*GetReadedReq)(nil), "circletimeline.GetReadedReq")
	proto.RegisterType((*GetReadedResp)(nil), "circletimeline.GetReadedResp")
	proto.RegisterType((*GetActMessagesReq)(nil), "circletimeline.GetActMessagesReq")
	proto.RegisterType((*GetActMessagesResp)(nil), "circletimeline.GetActMessagesResp")
	proto.RegisterEnum("circletimeline.CircleTimelineMsg_TYPE", CircleTimelineMsg_TYPE_name, CircleTimelineMsg_TYPE_value)
	proto.RegisterEnum("circletimeline.NewMsgComment_MSG_TYPE", NewMsgComment_MSG_TYPE_name, NewMsgComment_MSG_TYPE_value)
	proto.RegisterEnum("circletimeline.NewMsgComment_SYSTEM_MSG_TYPE", NewMsgComment_SYSTEM_MSG_TYPE_name, NewMsgComment_SYSTEM_MSG_TYPE_value)
	proto.RegisterEnum("circletimeline.MyTopic_TOPIC_TYPE", MyTopic_TOPIC_TYPE_name, MyTopic_TOPIC_TYPE_value)
}
func (m *CircleTimelineMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CircleTimelineMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Seqid))
	if m.MsgBin != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	return i, nil
}

func (m *NewMsgComment) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *NewMsgComment) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x32
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.SenderAccount)))
	i += copy(dAtA[i:], m.SenderAccount)
	dAtA[i] = 0x38
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.SenderUid))
	dAtA[i] = 0x40
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x48
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.RepliedCommentId))
	dAtA[i] = 0x52
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.RepliedAccount)))
	i += copy(dAtA[i:], m.RepliedAccount)
	dAtA[i] = 0x58
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.RepliedUid))
	dAtA[i] = 0x62
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.RepliedContent)))
	i += copy(dAtA[i:], m.RepliedContent)
	dAtA[i] = 0x68
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.MsgId))
	dAtA[i] = 0x70
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.SystemMsgType))
	if len(m.CommentImgKeyList) > 0 {
		for _, s := range m.CommentImgKeyList {
			dAtA[i] = 0x7a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.RepliedImgKeyList) > 0 {
		for _, s := range m.RepliedImgKeyList {
			dAtA[i] = 0x82
			i++
			dAtA[i] = 0x1
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.ParentCommentId))
	return i, nil
}

func (m *MyTopic) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MyTopic) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x22
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.SenderAccount)))
	i += copy(dAtA[i:], m.SenderAccount)
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.SenderUid))
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.SendTime))
	dAtA[i] = 0x3a
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Title)))
	i += copy(dAtA[i:], m.Title)
	dAtA[i] = 0x42
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			dAtA[i] = 0x4a
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *DeleteNewMsgComment) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteNewMsgComment) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CommentId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.SenderUid))
	return i, nil
}

func (m *DeleteNewMsgTopic) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DeleteNewMsgTopic) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *ActBegin) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActBegin) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.TopicId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.ActDesc)))
	i += copy(dAtA[i:], m.ActDesc)
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.ExpireTime))
	return i, nil
}

func (m *ActEnd) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ActEnd) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.CircleId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.TopicId))
	return i, nil
}

func (m *WriteTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintCircletimeline(dAtA, i, uint64(m.Msg.Size()))
		n1, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *WriteTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchDeleteTimelineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeleteTimelineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	if len(m.SeqIdList) > 0 {
		for _, num := range m.SeqIdList {
			dAtA[i] = 0x18
			i++
			i = encodeVarintCircletimeline(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *BatchDeleteTimelineResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeleteTimelineResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PullTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.StartSeqid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x28
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.IsRev))
	dAtA[i] = 0x30
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.EndSeqid))
	return i, nil
}

func (m *PullTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCircletimeline(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *MarkReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x18
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func (m *MarkReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MarkReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetReadedReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReadedReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *GetReadedResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetReadedResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.Seqid))
	return i, nil
}

func (m *GetActMessagesReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActMessagesReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.StartSeqId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.MaxMsgCount))
	return i, nil
}

func (m *GetActMessagesResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetActMessagesResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintCircletimeline(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintCircletimeline(dAtA, i, uint64(m.MaxSeq))
	return i, nil
}

func encodeFixed64Circletimeline(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Circletimeline(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintCircletimeline(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *CircleTimelineMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Type))
	n += 1 + sovCircletimeline(uint64(m.Seqid))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovCircletimeline(uint64(l))
	}
	return n
}

func (m *NewMsgComment) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.CommentId))
	n += 1 + sovCircletimeline(uint64(m.CircleId))
	n += 1 + sovCircletimeline(uint64(m.TopicId))
	n += 1 + sovCircletimeline(uint64(m.Type))
	l = len(m.Content)
	n += 1 + l + sovCircletimeline(uint64(l))
	l = len(m.SenderAccount)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.SenderUid))
	n += 1 + sovCircletimeline(uint64(m.SendTime))
	n += 1 + sovCircletimeline(uint64(m.RepliedCommentId))
	l = len(m.RepliedAccount)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.RepliedUid))
	l = len(m.RepliedContent)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.MsgId))
	n += 1 + sovCircletimeline(uint64(m.SystemMsgType))
	if len(m.CommentImgKeyList) > 0 {
		for _, s := range m.CommentImgKeyList {
			l = len(s)
			n += 1 + l + sovCircletimeline(uint64(l))
		}
	}
	if len(m.RepliedImgKeyList) > 0 {
		for _, s := range m.RepliedImgKeyList {
			l = len(s)
			n += 2 + l + sovCircletimeline(uint64(l))
		}
	}
	n += 2 + sovCircletimeline(uint64(m.ParentCommentId))
	return n
}

func (m *MyTopic) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.CircleId))
	n += 1 + sovCircletimeline(uint64(m.TopicId))
	n += 1 + sovCircletimeline(uint64(m.Type))
	l = len(m.SenderAccount)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.SenderUid))
	n += 1 + sovCircletimeline(uint64(m.SendTime))
	l = len(m.Title)
	n += 1 + l + sovCircletimeline(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovCircletimeline(uint64(l))
	if len(m.ImgKeyList) > 0 {
		for _, s := range m.ImgKeyList {
			l = len(s)
			n += 1 + l + sovCircletimeline(uint64(l))
		}
	}
	return n
}

func (m *DeleteNewMsgComment) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.CircleId))
	n += 1 + sovCircletimeline(uint64(m.TopicId))
	n += 1 + sovCircletimeline(uint64(m.CommentId))
	n += 1 + sovCircletimeline(uint64(m.Type))
	n += 1 + sovCircletimeline(uint64(m.SenderUid))
	return n
}

func (m *DeleteNewMsgTopic) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.CircleId))
	n += 1 + sovCircletimeline(uint64(m.TopicId))
	return n
}

func (m *ActBegin) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.CircleId))
	n += 1 + sovCircletimeline(uint64(m.TopicId))
	l = len(m.ActDesc)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.ExpireTime))
	return n
}

func (m *ActEnd) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.CircleId))
	n += 1 + sovCircletimeline(uint64(m.TopicId))
	return n
}

func (m *WriteTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovCircletimeline(uint64(l))
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovCircletimeline(uint64(l))
	}
	return n
}

func (m *WriteTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchDeleteTimelineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovCircletimeline(uint64(l))
	if len(m.SeqIdList) > 0 {
		for _, e := range m.SeqIdList {
			n += 1 + sovCircletimeline(uint64(e))
		}
	}
	return n
}

func (m *BatchDeleteTimelineResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PullTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.StartSeqid))
	n += 1 + sovCircletimeline(uint64(m.Limit))
	n += 1 + sovCircletimeline(uint64(m.IsRev))
	n += 1 + sovCircletimeline(uint64(m.EndSeqid))
	return n
}

func (m *PullTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovCircletimeline(uint64(l))
		}
	}
	return n
}

func (m *MarkReadedReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovCircletimeline(uint64(l))
	n += 1 + sovCircletimeline(uint64(m.Seqid))
	return n
}

func (m *MarkReadedResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetReadedReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovCircletimeline(uint64(l))
	return n
}

func (m *GetReadedResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.Seqid))
	return n
}

func (m *GetActMessagesReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovCircletimeline(uint64(m.StartSeqId))
	n += 1 + sovCircletimeline(uint64(m.MaxMsgCount))
	return n
}

func (m *GetActMessagesResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovCircletimeline(uint64(l))
		}
	}
	n += 1 + sovCircletimeline(uint64(m.MaxSeq))
	return n
}

func sovCircletimeline(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozCircletimeline(x uint64) (n int) {
	return sovCircletimeline(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *CircleTimelineMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CircleTimelineMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CircleTimelineMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *NewMsgComment) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: NewMsgComment: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: NewMsgComment: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SenderAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderUid", wireType)
			}
			m.SenderUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SenderUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedCommentId", wireType)
			}
			m.RepliedCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RepliedCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RepliedAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedUid", wireType)
			}
			m.RepliedUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.RepliedUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RepliedContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00001000)
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SystemMsgType", wireType)
			}
			m.SystemMsgType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SystemMsgType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CommentImgKeyList = append(m.CommentImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field RepliedImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.RepliedImgKeyList = append(m.RepliedImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ParentCommentId", wireType)
			}
			m.ParentCommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ParentCommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender_account")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender_uid")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("send_time")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("replied_comment_id")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("replied_account")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("replied_uid")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("replied_content")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MyTopic) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MyTopic: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MyTopic: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SenderAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderUid", wireType)
			}
			m.SenderUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SenderUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SendTime", wireType)
			}
			m.SendTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SendTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Title", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Title = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgKeyList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ImgKeyList = append(m.ImgKeyList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender_account")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender_uid")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("send_time")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("title")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteNewMsgComment) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteNewMsgComment: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteNewMsgComment: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CommentId", wireType)
			}
			m.CommentId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CommentId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderUid", wireType)
			}
			m.SenderUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SenderUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("comment_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("sender_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DeleteNewMsgTopic) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DeleteNewMsgTopic: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DeleteNewMsgTopic: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActBegin) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActBegin: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActBegin: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActDesc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ActDesc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExpireTime", wireType)
			}
			m.ExpireTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExpireTime |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("act_desc")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("expire_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ActEnd) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ActEnd: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ActEnd: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CircleId", wireType)
			}
			m.CircleId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CircleId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopicId", wireType)
			}
			m.TopicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TopicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("circle_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("topic_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &CircleTimelineMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeleteTimelineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeleteTimelineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeleteTimelineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircletimeline
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SeqIdList = append(m.SeqIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowCircletimeline
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthCircletimeline
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowCircletimeline
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SeqIdList = append(m.SeqIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeleteTimelineResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeleteTimelineResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeleteTimelineResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartSeqid", wireType)
			}
			m.StartSeqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSeqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsRev", wireType)
			}
			m.IsRev = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.IsRev |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field EndSeqid", wireType)
			}
			m.EndSeqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.EndSeqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_seqid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &CircleTimelineMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MarkReadedResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MarkReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MarkReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReadedReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReadedReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReadedReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetReadedResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetReadedResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetReadedResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActMessagesReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActMessagesReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActMessagesReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartSeqId", wireType)
			}
			m.StartSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxMsgCount", wireType)
			}
			m.MaxMsgCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxMsgCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetActMessagesResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetActMessagesResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetActMessagesResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthCircletimeline
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &CircleTimelineMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MaxSeq", wireType)
			}
			m.MaxSeq = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MaxSeq |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipCircletimeline(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthCircletimeline
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("max_seq")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipCircletimeline(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowCircletimeline
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowCircletimeline
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthCircletimeline
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowCircletimeline
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipCircletimeline(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthCircletimeline = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowCircletimeline   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/circletimeline/circletimeline.proto", fileDescriptorCircletimeline)
}

var fileDescriptorCircletimeline = []byte{
	// 1499 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x57, 0x4b, 0x4f, 0x23, 0xc7,
	0x16, 0xa6, 0xdd, 0x7e, 0xb4, 0x8f, 0xb1, 0x31, 0x05, 0xc3, 0xf5, 0xf8, 0x32, 0x9e, 0xa6, 0xe7,
	0x71, 0x91, 0x18, 0xe0, 0x5e, 0x66, 0x37, 0x42, 0xe8, 0x62, 0xd3, 0x42, 0xd6, 0x60, 0x83, 0x8c,
	0x47, 0xa3, 0x59, 0xb5, 0x4c, 0x77, 0x8d, 0x53, 0xa2, 0xbb, 0xdd, 0xb8, 0x0a, 0x02, 0x8a, 0x94,
	0x4c, 0x16, 0x91, 0xa2, 0x2c, 0x92, 0x28, 0x8b, 0xac, 0xb3, 0x60, 0x91, 0x3f, 0x91, 0xfd, 0x28,
	0xd9, 0xe4, 0x0f, 0xe4, 0xa1, 0x89, 0x22, 0xf1, 0x33, 0xa2, 0xea, 0x17, 0xdd, 0x6d, 0x67, 0x20,
	0x33, 0xec, 0xdc, 0xe7, 0x9c, 0xaa, 0xf3, 0xd5, 0x77, 0xbe, 0x73, 0xaa, 0x0c, 0x8f, 0x28, 0x1e,
	0x9e, 0x10, 0x1d, 0xd3, 0x55, 0x9d, 0x0c, 0x75, 0x13, 0x33, 0x62, 0x61, 0x93, 0xd8, 0x38, 0xf1,
	0xb9, 0xe2, 0x0c, 0x07, 0x6c, 0x80, 0x4a, 0x71, 0x6b, 0xf5, 0xbe, 0x3e, 0xb0, 0xac, 0x81, 0xbd,
	0xca, 0xcc, 0x13, 0x87, 0xe8, 0x87, 0x26, 0x5e, 0xa5, 0x87, 0x07, 0xc7, 0xc4, 0x64, 0xc4, 0x66,
	0x67, 0x8e, 0xbf, 0x4a, 0xf9, 0x31, 0x05, 0xd3, 0x0d, 0x77, 0x61, 0xd7, 0x5f, 0xd8, 0xa2, 0x7d,
	0x54, 0x81, 0x34, 0x8f, 0xa9, 0x08, 0x72, 0x6a, 0xb1, 0x58, 0x4f, 0xbf, 0xfe, 0xf5, 0xee, 0x44,
	0xc7, 0xb5, 0xa0, 0x2a, 0x64, 0x28, 0x3e, 0x22, 0x46, 0x25, 0x15, 0x71, 0x79, 0x26, 0x74, 0x07,
	0x72, 0x16, 0xed, 0x6b, 0x07, 0xc4, 0xae, 0x88, 0x72, 0x6a, 0x71, 0xd2, 0xf7, 0x66, 0x2d, 0xda,
	0xaf, 0x13, 0x5b, 0xf9, 0x4d, 0x80, 0x74, 0xf7, 0xc5, 0x9e, 0x8a, 0x66, 0x60, 0xaa, 0xad, 0x3e,
	0xd7, 0x5a, 0xfb, 0xdb, 0x5a, 0x63, 0xb7, 0xd5, 0x52, 0xdb, 0xdd, 0xb2, 0x80, 0x26, 0x41, 0x6a,
	0xbd, 0xd0, 0xba, 0xbb, 0x7b, 0xcd, 0x46, 0x39, 0x85, 0xaa, 0x30, 0xb7, 0xa5, 0xee, 0xa8, 0x5d,
	0x55, 0x4b, 0x46, 0x8a, 0xa8, 0x02, 0xb3, 0x09, 0x9f, 0xb7, 0x2a, 0x8d, 0x8a, 0x90, 0xdf, 0x6c,
	0x74, 0xb5, 0xba, 0xba, 0xdd, 0x6c, 0x97, 0x33, 0xa8, 0x00, 0x39, 0xfe, 0xa9, 0xb6, 0xb7, 0xca,
	0x59, 0x24, 0xc3, 0xfc, 0xf6, 0xb3, 0xe6, 0xce, 0x96, 0xd6, 0x68, 0x76, 0x1a, 0x3b, 0xa3, 0xfb,
	0xe6, 0xd0, 0x03, 0x58, 0x88, 0x45, 0x8c, 0x4d, 0x22, 0xa1, 0xdb, 0x70, 0x2b, 0x16, 0x16, 0xa2,
	0xce, 0x2b, 0x5f, 0x65, 0xa1, 0xd8, 0xc6, 0x1f, 0xb6, 0x68, 0xbf, 0x31, 0xb0, 0x2c, 0x6c, 0x33,
	0x74, 0x0f, 0x40, 0xf7, 0x7e, 0x6a, 0xc4, 0x88, 0xd1, 0x99, 0xf7, 0xed, 0x4d, 0x03, 0x2d, 0x40,
	0xde, 0xab, 0x9d, 0x96, 0xe0, 0x55, 0xf2, 0xcc, 0x4d, 0x03, 0xdd, 0x05, 0x89, 0x0d, 0x1c, 0xa2,
	0xf3, 0x08, 0x31, 0x12, 0x91, 0x73, 0xad, 0x4d, 0x23, 0xac, 0x58, 0x7a, 0xa4, 0x62, 0x35, 0xc8,
	0xe9, 0x03, 0x9b, 0x61, 0x9b, 0x55, 0x32, 0x72, 0x6a, 0x31, 0x1f, 0xac, 0xf4, 0x8d, 0x68, 0x09,
	0x4a, 0x14, 0xdb, 0x06, 0x1e, 0x6a, 0x3d, 0x5d, 0x1f, 0x1c, 0xdb, 0xac, 0x92, 0x8d, 0x84, 0x15,
	0x3d, 0xdf, 0xa6, 0xe7, 0xe2, 0xe7, 0xf1, 0x83, 0x8f, 0x89, 0x51, 0xc9, 0x45, 0xcf, 0xe3, 0xd9,
	0x9f, 0x11, 0xf7, 0x3c, 0xfc, 0x43, 0xe3, 0x52, 0xac, 0x48, 0xd1, 0xf3, 0x70, 0x33, 0xd7, 0x19,
	0x5a, 0x03, 0x34, 0xc4, 0x8e, 0x49, 0xb0, 0xa1, 0x45, 0xf8, 0xc9, 0x47, 0x62, 0xcb, 0xbe, 0xbf,
	0x11, 0xd2, 0xb4, 0x0c, 0x53, 0xc1, 0x9a, 0x00, 0x29, 0x44, 0x90, 0x96, 0x7c, 0x67, 0x00, 0xf5,
	0x01, 0x14, 0x82, 0x70, 0x8e, 0xb5, 0x10, 0xd9, 0x1b, 0x7c, 0x07, 0x07, 0x1b, 0xd9, 0x35, 0xa0,
	0x69, 0x72, 0xcc, 0xae, 0x0d, 0x9f, 0xad, 0x7f, 0x03, 0x97, 0x33, 0x07, 0x5b, 0x8c, 0x36, 0x80,
	0x45, 0xfb, 0x4d, 0x03, 0x3d, 0x82, 0x29, 0x7a, 0x46, 0x19, 0xb6, 0x34, 0x1e, 0xe3, 0xd6, 0xa3,
	0x24, 0x0b, 0x61, 0x54, 0xd1, 0x73, 0xb6, 0x68, 0xbf, 0xcb, 0x0b, 0xb3, 0x0a, 0xb3, 0xe1, 0xd9,
	0xad, 0xbe, 0x76, 0x88, 0xcf, 0x34, 0x93, 0x50, 0x56, 0x99, 0x92, 0xc5, 0xc5, 0x7c, 0x67, 0x3a,
	0xd0, 0x87, 0xd5, 0x7f, 0x8a, 0xcf, 0x76, 0x08, 0x65, 0x7c, 0x41, 0x00, 0x35, 0xb6, 0xa0, 0xec,
	0x2d, 0xf0, 0x7d, 0x91, 0x05, 0xff, 0x85, 0x69, 0xa7, 0x37, 0xe4, 0x09, 0x22, 0x24, 0x4f, 0x47,
	0x10, 0x4d, 0x79, 0xee, 0x90, 0x63, 0x65, 0x01, 0x24, 0x57, 0xeb, 0xbc, 0x4d, 0x0b, 0x90, 0xbb,
	0x6c, 0x4f, 0x09, 0xd2, 0x3b, 0xcd, 0xa7, 0x6a, 0x39, 0xa5, 0x2c, 0xc1, 0xd4, 0xfe, 0x8b, 0xfd,
	0xae, 0xda, 0xd2, 0xc2, 0x48, 0x09, 0xd2, 0xed, 0xdd, 0xb6, 0x5a, 0x9e, 0x40, 0x25, 0x80, 0x4b,
	0x67, 0x59, 0x50, 0x7e, 0x49, 0x41, 0xae, 0x75, 0xd6, 0xe5, 0x22, 0x8d, 0xcb, 0x5c, 0xb8, 0x52,
	0xe6, 0xa9, 0xb7, 0xc9, 0x5c, 0x1c, 0x91, 0xf9, 0xa8, 0x8c, 0xd3, 0xd7, 0x95, 0x71, 0xe6, 0x1a,
	0x32, 0xce, 0x8e, 0x95, 0x71, 0x15, 0x32, 0x8c, 0x30, 0x13, 0xbb, 0x9d, 0x10, 0xe4, 0xf2, 0x4c,
	0xd1, 0xbe, 0x93, 0xc6, 0xf5, 0x9d, 0x0c, 0x93, 0xb1, 0x2a, 0xe6, 0xdd, 0x2a, 0x02, 0x09, 0xcb,
	0xa7, 0x2c, 0x00, 0xb8, 0x93, 0xc5, 0x23, 0x19, 0x20, 0xdb, 0xde, 0xed, 0xb4, 0x36, 0x77, 0xca,
	0x13, 0x28, 0x07, 0x62, 0x77, 0x77, 0xaf, 0x2c, 0x28, 0x3f, 0x08, 0x30, 0xb3, 0x85, 0x4d, 0xcc,
	0x70, 0x7c, 0xee, 0xdc, 0x04, 0xd7, 0xf1, 0xd9, 0x25, 0x8e, 0x9f, 0x5d, 0x7f, 0x3f, 0x77, 0xae,
	0xc3, 0xb1, 0xf2, 0x1c, 0xa6, 0xa3, 0xf0, 0x6f, 0x4c, 0x28, 0xca, 0xb7, 0x02, 0x48, 0x9b, 0x3a,
	0xab, 0xe3, 0x3e, 0xb1, 0x6f, 0x84, 0x8d, 0xbb, 0x20, 0xf5, 0x74, 0xa6, 0x19, 0x98, 0xea, 0x2e,
	0x17, 0x61, 0x3d, 0x7b, 0x3a, 0xdb, 0xc2, 0x54, 0xe7, 0xf3, 0x06, 0x9f, 0x3a, 0x64, 0x88, 0x3d,
	0xc1, 0x70, 0x42, 0xd2, 0xc1, 0xbc, 0xf1, 0x1c, 0x5c, 0x32, 0xca, 0x0e, 0x64, 0x37, 0x75, 0xa6,
	0xda, 0xc6, 0x8d, 0x1c, 0xf3, 0x63, 0x98, 0x79, 0x3e, 0x24, 0x2c, 0x7a, 0x79, 0x77, 0xf0, 0x11,
	0x9a, 0x85, 0x54, 0x62, 0xcf, 0x14, 0x31, 0xd0, 0x3c, 0x64, 0xe9, 0xf1, 0xcb, 0x97, 0xe4, 0xd4,
	0xdd, 0x2b, 0x38, 0x80, 0x6f, 0x43, 0x8f, 0x41, 0xb4, 0x68, 0xdf, 0x3d, 0x5b, 0x61, 0x6d, 0x61,
	0x25, 0xf1, 0xc6, 0x18, 0x79, 0x23, 0x74, 0x78, 0xb4, 0x32, 0x07, 0xb3, 0xa3, 0xf9, 0xa9, 0xa3,
	0x98, 0x30, 0x57, 0xef, 0x31, 0xfd, 0x03, 0xaf, 0xb8, 0x81, 0xf7, 0x5d, 0xa1, 0xd5, 0xa0, 0x40,
	0xf1, 0x91, 0x46, 0x0c, 0xaf, 0x53, 0x44, 0x59, 0x5c, 0x2c, 0x72, 0x15, 0x1d, 0x35, 0x0d, 0xb7,
	0x51, 0x6e, 0xc3, 0xbf, 0xc6, 0x66, 0xa3, 0x8e, 0xf2, 0x93, 0x00, 0x68, 0xef, 0xd8, 0x34, 0x6f,
	0x80, 0xa0, 0x07, 0x50, 0xa0, 0xac, 0x37, 0x64, 0x9a, 0xf7, 0x00, 0x8a, 0x36, 0x04, 0xb8, 0x8e,
	0x7d, 0xf7, 0x15, 0x54, 0x85, 0x8c, 0x49, 0x2c, 0xc2, 0x62, 0x2d, 0xe1, 0x99, 0xf8, 0xed, 0x41,
	0xa8, 0x36, 0xc4, 0x27, 0x95, 0x4c, 0x64, 0x0a, 0x67, 0x08, 0xed, 0xe0, 0x13, 0xae, 0x07, 0x3e,
	0x6e, 0xbc, 0xdd, 0xb3, 0x11, 0xbf, 0x84, 0x6d, 0xc3, 0xdd, 0x5b, 0xd9, 0x87, 0x99, 0x91, 0xc3,
	0x50, 0x07, 0xad, 0x83, 0xc4, 0x2f, 0x1c, 0x97, 0x1c, 0x41, 0x16, 0xaf, 0x57, 0x3f, 0xfe, 0x56,
	0x73, 0xd9, 0xd3, 0xa0, 0xd8, 0xea, 0x0d, 0x0f, 0x3b, 0xb8, 0x67, 0x60, 0xe3, 0x5d, 0xc9, 0x09,
	0xdf, 0x85, 0xe2, 0xc8, 0xbb, 0x50, 0x29, 0x43, 0x29, 0x9a, 0x80, 0x3a, 0x4a, 0x1d, 0x26, 0xb7,
	0x31, 0x7b, 0xaf, 0x8c, 0xca, 0x12, 0x14, 0x23, 0x7b, 0x50, 0xe7, 0x12, 0x82, 0x30, 0x0a, 0x01,
	0xc3, 0xf4, 0x36, 0x66, 0x9b, 0x3a, 0x6b, 0x61, 0x4a, 0x7b, 0x7d, 0x4c, 0x79, 0xd6, 0x87, 0x30,
	0x19, 0x16, 0x34, 0xd9, 0x83, 0x61, 0x45, 0x9b, 0x06, 0x5a, 0x84, 0xa2, 0xd5, 0x3b, 0x75, 0xef,
	0x74, 0xef, 0x66, 0x49, 0x45, 0x8a, 0x53, 0xb0, 0x7a, 0xa7, 0xee, 0xe0, 0x3d, 0xb6, 0x99, 0x72,
	0x04, 0x28, 0x99, 0xe6, 0x7d, 0xcb, 0xe3, 0xbe, 0xaa, 0x7b, 0xa7, 0x1c, 0x63, 0x6c, 0x04, 0x64,
	0xad, 0xde, 0xe9, 0x3e, 0x3e, 0x5a, 0xfb, 0x53, 0x82, 0x52, 0x7c, 0x35, 0xfa, 0x4c, 0x80, 0x72,
	0xb2, 0x2b, 0xd1, 0xbd, 0x64, 0xca, 0x31, 0x73, 0xa3, 0x7a, 0xff, 0xea, 0x20, 0xea, 0x28, 0x0f,
	0x5f, 0x9d, 0x5f, 0x88, 0xc2, 0x17, 0xe7, 0x17, 0x62, 0x9a, 0x3c, 0xa1, 0x4f, 0xbe, 0x39, 0xbf,
	0x10, 0x67, 0x96, 0x89, 0xbc, 0x4e, 0x8c, 0x0d, 0x79, 0x99, 0xca, 0xeb, 0x5e, 0x81, 0x36, 0xd0,
	0xf7, 0x02, 0xcc, 0x8c, 0xe9, 0x4b, 0xf4, 0x30, 0x99, 0x65, 0xfc, 0xa8, 0xa8, 0xfe, 0xe7, 0x5a,
	0x71, 0xd4, 0x51, 0xfe, 0xcf, 0x01, 0xa5, 0x38, 0xa0, 0x2c, 0x07, 0x64, 0xba, 0x90, 0x96, 0xc6,
	0x40, 0x92, 0x97, 0x4d, 0x79, 0xdd, 0x1f, 0x25, 0xff, 0x7b, 0x24, 0xfb, 0xbf, 0xd6, 0x36, 0xd0,
	0x77, 0x02, 0x4c, 0x25, 0x3a, 0x0b, 0x29, 0xc9, 0xf4, 0xa3, 0x73, 0xa4, 0x7a, 0xef, 0xca, 0x18,
	0xea, 0x28, 0x5b, 0x1c, 0x9e, 0xc8, 0xe1, 0x49, 0x1c, 0xde, 0x81, 0x0f, 0x70, 0x79, 0x2c, 0xc0,
	0x03, 0x79, 0xfd, 0x80, 0x5f, 0x5a, 0xbc, 0xe0, 0x1e, 0x5e, 0x77, 0x74, 0x6c, 0xa0, 0x57, 0x02,
	0xc0, 0x65, 0x1b, 0xa1, 0x3b, 0xc9, 0xcc, 0xb1, 0x1e, 0xae, 0xd6, 0xde, 0xe6, 0xa6, 0x8e, 0xf2,
	0x98, 0x63, 0x4a, 0x87, 0x94, 0x19, 0x2e, 0xa2, 0xda, 0x58, 0x44, 0x86, 0x4b, 0x19, 0x31, 0x36,
	0x90, 0x03, 0xf9, 0xb0, 0xe5, 0xd0, 0x7c, 0x32, 0x43, 0xb4, 0xa3, 0xab, 0x77, 0xde, 0xe2, 0x0d,
	0x24, 0x94, 0xb9, 0x5a, 0x42, 0x9f, 0xf8, 0xf7, 0x4b, 0x70, 0x95, 0xfb, 0x7d, 0x85, 0x2a, 0xc9,
	0xed, 0x83, 0x80, 0xea, 0xfc, 0x4a, 0xf8, 0x87, 0x77, 0x65, 0xff, 0x69, 0xdd, 0xfb, 0xc3, 0xab,
	0x5a, 0x0e, 0x3b, 0xd3, 0xf6, 0xea, 0xca, 0x2a, 0xcf, 0x9b, 0xf5, 0xf3, 0x32, 0x37, 0xef, 0x3c,
	0xcf, 0x1b, 0x5e, 0xc4, 0x1b, 0xf2, 0x32, 0x93, 0xd7, 0x83, 0x4b, 0x77, 0x03, 0x7d, 0x04, 0x28,
	0x00, 0xa0, 0xda, 0x46, 0x90, 0x7e, 0x6e, 0x4c, 0x7a, 0xd5, 0x36, 0xae, 0x93, 0x3c, 0xf7, 0x0f,
	0x92, 0x7f, 0x2a, 0x40, 0x29, 0x3e, 0x4f, 0xd0, 0xc2, 0x18, 0x5e, 0xe3, 0x63, 0xad, 0xaa, 0x5c,
	0x15, 0x42, 0x1d, 0x65, 0x91, 0x43, 0x91, 0x5c, 0x28, 0x41, 0xbf, 0xdc, 0x72, 0x79, 0xe7, 0x53,
	0x2f, 0x2a, 0xbb, 0x6a, 0xf6, 0xf3, 0xf3, 0x0b, 0xf1, 0xcb, 0xe3, 0x7a, 0xf9, 0xf5, 0x9b, 0x9a,
	0xf0, 0xf3, 0x9b, 0x9a, 0xf0, 0xfb, 0x9b, 0x9a, 0xf0, 0xf5, 0x1f, 0xb5, 0x89, 0xbf, 0x02, 0x00,
	0x00, 0xff, 0xff, 0x3f, 0x62, 0xd8, 0xb2, 0x9f, 0x10, 0x00, 0x00,
}
