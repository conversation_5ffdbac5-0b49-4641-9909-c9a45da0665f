// Code generated by protoc-gen-go. DO NOT EDIT.
// source: cpl-search/cpl-search.proto

package cplsearch // import "golang.52tt.com/protocol/services/cplsearch"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type PlatformType int32

const (
	PlatformType_UNKNOWN PlatformType = 0
	PlatformType_ALL     PlatformType = 1
	PlatformType_ANDROID PlatformType = 2
	PlatformType_IOS     PlatformType = 4
)

var PlatformType_name = map[int32]string{
	0: "UNKNOWN",
	1: "ALL",
	2: "ANDROID",
	4: "IOS",
}
var PlatformType_value = map[string]int32{
	"UNKNOWN": 0,
	"ALL":     1,
	"ANDROID": 2,
	"IOS":     4,
}

func (x PlatformType) String() string {
	return proto.EnumName(PlatformType_name, int32(x))
}
func (PlatformType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{0}
}

type AddSearchItemReq struct {
	Item                 *SearchItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddSearchItemReq) Reset()         { *m = AddSearchItemReq{} }
func (m *AddSearchItemReq) String() string { return proto.CompactTextString(m) }
func (*AddSearchItemReq) ProtoMessage()    {}
func (*AddSearchItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{0}
}
func (m *AddSearchItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSearchItemReq.Unmarshal(m, b)
}
func (m *AddSearchItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSearchItemReq.Marshal(b, m, deterministic)
}
func (dst *AddSearchItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSearchItemReq.Merge(dst, src)
}
func (m *AddSearchItemReq) XXX_Size() int {
	return xxx_messageInfo_AddSearchItemReq.Size(m)
}
func (m *AddSearchItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSearchItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddSearchItemReq proto.InternalMessageInfo

func (m *AddSearchItemReq) GetItem() *SearchItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type AddSearchItemResp struct {
	Item                 *SearchItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *AddSearchItemResp) Reset()         { *m = AddSearchItemResp{} }
func (m *AddSearchItemResp) String() string { return proto.CompactTextString(m) }
func (*AddSearchItemResp) ProtoMessage()    {}
func (*AddSearchItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{1}
}
func (m *AddSearchItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddSearchItemResp.Unmarshal(m, b)
}
func (m *AddSearchItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddSearchItemResp.Marshal(b, m, deterministic)
}
func (dst *AddSearchItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddSearchItemResp.Merge(dst, src)
}
func (m *AddSearchItemResp) XXX_Size() int {
	return xxx_messageInfo_AddSearchItemResp.Size(m)
}
func (m *AddSearchItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddSearchItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddSearchItemResp proto.InternalMessageInfo

func (m *AddSearchItemResp) GetItem() *SearchItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type DeleteSearchItemReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSearchItemReq) Reset()         { *m = DeleteSearchItemReq{} }
func (m *DeleteSearchItemReq) String() string { return proto.CompactTextString(m) }
func (*DeleteSearchItemReq) ProtoMessage()    {}
func (*DeleteSearchItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{2}
}
func (m *DeleteSearchItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSearchItemReq.Unmarshal(m, b)
}
func (m *DeleteSearchItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSearchItemReq.Marshal(b, m, deterministic)
}
func (dst *DeleteSearchItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSearchItemReq.Merge(dst, src)
}
func (m *DeleteSearchItemReq) XXX_Size() int {
	return xxx_messageInfo_DeleteSearchItemReq.Size(m)
}
func (m *DeleteSearchItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSearchItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSearchItemReq proto.InternalMessageInfo

func (m *DeleteSearchItemReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteSearchItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteSearchItemResp) Reset()         { *m = DeleteSearchItemResp{} }
func (m *DeleteSearchItemResp) String() string { return proto.CompactTextString(m) }
func (*DeleteSearchItemResp) ProtoMessage()    {}
func (*DeleteSearchItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{3}
}
func (m *DeleteSearchItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteSearchItemResp.Unmarshal(m, b)
}
func (m *DeleteSearchItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteSearchItemResp.Marshal(b, m, deterministic)
}
func (dst *DeleteSearchItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteSearchItemResp.Merge(dst, src)
}
func (m *DeleteSearchItemResp) XXX_Size() int {
	return xxx_messageInfo_DeleteSearchItemResp.Size(m)
}
func (m *DeleteSearchItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteSearchItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteSearchItemResp proto.InternalMessageInfo

type UpdateSearchItemReq struct {
	Item                 *SearchItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateSearchItemReq) Reset()         { *m = UpdateSearchItemReq{} }
func (m *UpdateSearchItemReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSearchItemReq) ProtoMessage()    {}
func (*UpdateSearchItemReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{4}
}
func (m *UpdateSearchItemReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSearchItemReq.Unmarshal(m, b)
}
func (m *UpdateSearchItemReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSearchItemReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSearchItemReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSearchItemReq.Merge(dst, src)
}
func (m *UpdateSearchItemReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSearchItemReq.Size(m)
}
func (m *UpdateSearchItemReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSearchItemReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSearchItemReq proto.InternalMessageInfo

func (m *UpdateSearchItemReq) GetItem() *SearchItem {
	if m != nil {
		return m.Item
	}
	return nil
}

type UpdateSearchItemResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSearchItemResp) Reset()         { *m = UpdateSearchItemResp{} }
func (m *UpdateSearchItemResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSearchItemResp) ProtoMessage()    {}
func (*UpdateSearchItemResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{5}
}
func (m *UpdateSearchItemResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSearchItemResp.Unmarshal(m, b)
}
func (m *UpdateSearchItemResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSearchItemResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSearchItemResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSearchItemResp.Merge(dst, src)
}
func (m *UpdateSearchItemResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSearchItemResp.Size(m)
}
func (m *UpdateSearchItemResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSearchItemResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSearchItemResp proto.InternalMessageInfo

type SearchItem struct {
	Id                   string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                string        `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Bg                   string        `protobuf:"bytes,3,opt,name=bg,proto3" json:"bg,omitempty"`
	Keywords             []string      `protobuf:"bytes,4,rep,name=keywords,proto3" json:"keywords,omitempty"`
	BeginAt              uint32        `protobuf:"varint,5,opt,name=begin_at,json=beginAt,proto3" json:"begin_at,omitempty"`
	EndAt                uint32        `protobuf:"varint,6,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	Account              string        `protobuf:"bytes,7,opt,name=account,proto3" json:"account,omitempty"`
	ChannelId            uint32        `protobuf:"varint,8,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Activity             *Activity     `protobuf:"bytes,9,opt,name=activity,proto3" json:"activity,omitempty"`
	ResultItems          []*ResultItem `protobuf:"bytes,10,rep,name=result_items,json=resultItems,proto3" json:"result_items,omitempty"`
	PlatformType         PlatformType  `protobuf:"varint,11,opt,name=platform_type,json=platformType,proto3,enum=cplsearch.PlatformType" json:"platform_type,omitempty"`
	MarketIds            []uint32      `protobuf:"varint,12,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	ChannelViewId        string        `protobuf:"bytes,13,opt,name=channel_view_id,json=channelViewId,proto3" json:"channel_view_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *SearchItem) Reset()         { *m = SearchItem{} }
func (m *SearchItem) String() string { return proto.CompactTextString(m) }
func (*SearchItem) ProtoMessage()    {}
func (*SearchItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{6}
}
func (m *SearchItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchItem.Unmarshal(m, b)
}
func (m *SearchItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchItem.Marshal(b, m, deterministic)
}
func (dst *SearchItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchItem.Merge(dst, src)
}
func (m *SearchItem) XXX_Size() int {
	return xxx_messageInfo_SearchItem.Size(m)
}
func (m *SearchItem) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchItem.DiscardUnknown(m)
}

var xxx_messageInfo_SearchItem proto.InternalMessageInfo

func (m *SearchItem) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SearchItem) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SearchItem) GetBg() string {
	if m != nil {
		return m.Bg
	}
	return ""
}

func (m *SearchItem) GetKeywords() []string {
	if m != nil {
		return m.Keywords
	}
	return nil
}

func (m *SearchItem) GetBeginAt() uint32 {
	if m != nil {
		return m.BeginAt
	}
	return 0
}

func (m *SearchItem) GetEndAt() uint32 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *SearchItem) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SearchItem) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SearchItem) GetActivity() *Activity {
	if m != nil {
		return m.Activity
	}
	return nil
}

func (m *SearchItem) GetResultItems() []*ResultItem {
	if m != nil {
		return m.ResultItems
	}
	return nil
}

func (m *SearchItem) GetPlatformType() PlatformType {
	if m != nil {
		return m.PlatformType
	}
	return PlatformType_UNKNOWN
}

func (m *SearchItem) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

func (m *SearchItem) GetChannelViewId() string {
	if m != nil {
		return m.ChannelViewId
	}
	return ""
}

type ResultItem struct {
	// Types that are valid to be assigned to Value:
	//	*ResultItem_Account
	//	*ResultItem_ChannelId
	//	*ResultItem_Activity
	//	*ResultItem_ChannelViewId
	Value                isResultItem_Value `protobuf_oneof:"value"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *ResultItem) Reset()         { *m = ResultItem{} }
func (m *ResultItem) String() string { return proto.CompactTextString(m) }
func (*ResultItem) ProtoMessage()    {}
func (*ResultItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{7}
}
func (m *ResultItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResultItem.Unmarshal(m, b)
}
func (m *ResultItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResultItem.Marshal(b, m, deterministic)
}
func (dst *ResultItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResultItem.Merge(dst, src)
}
func (m *ResultItem) XXX_Size() int {
	return xxx_messageInfo_ResultItem.Size(m)
}
func (m *ResultItem) XXX_DiscardUnknown() {
	xxx_messageInfo_ResultItem.DiscardUnknown(m)
}

var xxx_messageInfo_ResultItem proto.InternalMessageInfo

type isResultItem_Value interface {
	isResultItem_Value()
}

type ResultItem_Account struct {
	Account string `protobuf:"bytes,1,opt,name=account,proto3,oneof"`
}

type ResultItem_ChannelId struct {
	ChannelId uint32 `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3,oneof"`
}

type ResultItem_Activity struct {
	Activity *Activity `protobuf:"bytes,3,opt,name=activity,proto3,oneof"`
}

type ResultItem_ChannelViewId struct {
	ChannelViewId string `protobuf:"bytes,4,opt,name=channel_view_id,json=channelViewId,proto3,oneof"`
}

func (*ResultItem_Account) isResultItem_Value() {}

func (*ResultItem_ChannelId) isResultItem_Value() {}

func (*ResultItem_Activity) isResultItem_Value() {}

func (*ResultItem_ChannelViewId) isResultItem_Value() {}

func (m *ResultItem) GetValue() isResultItem_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *ResultItem) GetAccount() string {
	if x, ok := m.GetValue().(*ResultItem_Account); ok {
		return x.Account
	}
	return ""
}

func (m *ResultItem) GetChannelId() uint32 {
	if x, ok := m.GetValue().(*ResultItem_ChannelId); ok {
		return x.ChannelId
	}
	return 0
}

func (m *ResultItem) GetActivity() *Activity {
	if x, ok := m.GetValue().(*ResultItem_Activity); ok {
		return x.Activity
	}
	return nil
}

func (m *ResultItem) GetChannelViewId() string {
	if x, ok := m.GetValue().(*ResultItem_ChannelViewId); ok {
		return x.ChannelViewId
	}
	return ""
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*ResultItem) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _ResultItem_OneofMarshaler, _ResultItem_OneofUnmarshaler, _ResultItem_OneofSizer, []interface{}{
		(*ResultItem_Account)(nil),
		(*ResultItem_ChannelId)(nil),
		(*ResultItem_Activity)(nil),
		(*ResultItem_ChannelViewId)(nil),
	}
}

func _ResultItem_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*ResultItem)
	// value
	switch x := m.Value.(type) {
	case *ResultItem_Account:
		b.EncodeVarint(1<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.Account)
	case *ResultItem_ChannelId:
		b.EncodeVarint(2<<3 | proto.WireVarint)
		b.EncodeVarint(uint64(x.ChannelId))
	case *ResultItem_Activity:
		b.EncodeVarint(3<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.Activity); err != nil {
			return err
		}
	case *ResultItem_ChannelViewId:
		b.EncodeVarint(4<<3 | proto.WireBytes)
		b.EncodeStringBytes(x.ChannelViewId)
	case nil:
	default:
		return fmt.Errorf("ResultItem.Value has unexpected type %T", x)
	}
	return nil
}

func _ResultItem_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*ResultItem)
	switch tag {
	case 1: // value.account
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Value = &ResultItem_Account{x}
		return true, err
	case 2: // value.channel_id
		if wire != proto.WireVarint {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeVarint()
		m.Value = &ResultItem_ChannelId{uint32(x)}
		return true, err
	case 3: // value.activity
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(Activity)
		err := b.DecodeMessage(msg)
		m.Value = &ResultItem_Activity{msg}
		return true, err
	case 4: // value.channel_view_id
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		x, err := b.DecodeStringBytes()
		m.Value = &ResultItem_ChannelViewId{x}
		return true, err
	default:
		return false, nil
	}
}

func _ResultItem_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*ResultItem)
	// value
	switch x := m.Value.(type) {
	case *ResultItem_Account:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.Account)))
		n += len(x.Account)
	case *ResultItem_ChannelId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(x.ChannelId))
	case *ResultItem_Activity:
		s := proto.Size(x.Activity)
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case *ResultItem_ChannelViewId:
		n += 1 // tag and wire
		n += proto.SizeVarint(uint64(len(x.ChannelViewId)))
		n += len(x.ChannelViewId)
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

type Activity struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Desc                 string   `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	Icon                 string   `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	JumpUrl              string   `protobuf:"bytes,4,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Activity) Reset()         { *m = Activity{} }
func (m *Activity) String() string { return proto.CompactTextString(m) }
func (*Activity) ProtoMessage()    {}
func (*Activity) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{8}
}
func (m *Activity) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Activity.Unmarshal(m, b)
}
func (m *Activity) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Activity.Marshal(b, m, deterministic)
}
func (dst *Activity) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Activity.Merge(dst, src)
}
func (m *Activity) XXX_Size() int {
	return xxx_messageInfo_Activity.Size(m)
}
func (m *Activity) XXX_DiscardUnknown() {
	xxx_messageInfo_Activity.DiscardUnknown(m)
}

var xxx_messageInfo_Activity proto.InternalMessageInfo

func (m *Activity) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *Activity) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *Activity) GetIcon() string {
	if m != nil {
		return m.Icon
	}
	return ""
}

func (m *Activity) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

type GetSearchItemListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	MarketIds            []uint32 `protobuf:"varint,3,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSearchItemListReq) Reset()         { *m = GetSearchItemListReq{} }
func (m *GetSearchItemListReq) String() string { return proto.CompactTextString(m) }
func (*GetSearchItemListReq) ProtoMessage()    {}
func (*GetSearchItemListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{9}
}
func (m *GetSearchItemListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchItemListReq.Unmarshal(m, b)
}
func (m *GetSearchItemListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchItemListReq.Marshal(b, m, deterministic)
}
func (dst *GetSearchItemListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchItemListReq.Merge(dst, src)
}
func (m *GetSearchItemListReq) XXX_Size() int {
	return xxx_messageInfo_GetSearchItemListReq.Size(m)
}
func (m *GetSearchItemListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchItemListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchItemListReq proto.InternalMessageInfo

func (m *GetSearchItemListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetSearchItemListReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *GetSearchItemListReq) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

type GetSearchItemListResp struct {
	Items                []*SearchItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	TotalCount           uint32        `protobuf:"varint,2,opt,name=total_count,json=totalCount,proto3" json:"total_count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetSearchItemListResp) Reset()         { *m = GetSearchItemListResp{} }
func (m *GetSearchItemListResp) String() string { return proto.CompactTextString(m) }
func (*GetSearchItemListResp) ProtoMessage()    {}
func (*GetSearchItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{10}
}
func (m *GetSearchItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSearchItemListResp.Unmarshal(m, b)
}
func (m *GetSearchItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSearchItemListResp.Marshal(b, m, deterministic)
}
func (dst *GetSearchItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSearchItemListResp.Merge(dst, src)
}
func (m *GetSearchItemListResp) XXX_Size() int {
	return xxx_messageInfo_GetSearchItemListResp.Size(m)
}
func (m *GetSearchItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSearchItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSearchItemListResp proto.InternalMessageInfo

func (m *GetSearchItemListResp) GetItems() []*SearchItem {
	if m != nil {
		return m.Items
	}
	return nil
}

func (m *GetSearchItemListResp) GetTotalCount() uint32 {
	if m != nil {
		return m.TotalCount
	}
	return 0
}

type SearchReq struct {
	Keyword              string   `protobuf:"bytes,1,opt,name=keyword,proto3" json:"keyword,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SearchReq) Reset()         { *m = SearchReq{} }
func (m *SearchReq) String() string { return proto.CompactTextString(m) }
func (*SearchReq) ProtoMessage()    {}
func (*SearchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{11}
}
func (m *SearchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchReq.Unmarshal(m, b)
}
func (m *SearchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchReq.Marshal(b, m, deterministic)
}
func (dst *SearchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchReq.Merge(dst, src)
}
func (m *SearchReq) XXX_Size() int {
	return xxx_messageInfo_SearchReq.Size(m)
}
func (m *SearchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SearchReq proto.InternalMessageInfo

func (m *SearchReq) GetKeyword() string {
	if m != nil {
		return m.Keyword
	}
	return ""
}

type SearchResp struct {
	Item                 *SearchItem `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *SearchResp) Reset()         { *m = SearchResp{} }
func (m *SearchResp) String() string { return proto.CompactTextString(m) }
func (*SearchResp) ProtoMessage()    {}
func (*SearchResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_cpl_search_8e8ae12171831c55, []int{12}
}
func (m *SearchResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SearchResp.Unmarshal(m, b)
}
func (m *SearchResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SearchResp.Marshal(b, m, deterministic)
}
func (dst *SearchResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SearchResp.Merge(dst, src)
}
func (m *SearchResp) XXX_Size() int {
	return xxx_messageInfo_SearchResp.Size(m)
}
func (m *SearchResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SearchResp.DiscardUnknown(m)
}

var xxx_messageInfo_SearchResp proto.InternalMessageInfo

func (m *SearchResp) GetItem() *SearchItem {
	if m != nil {
		return m.Item
	}
	return nil
}

func init() {
	proto.RegisterType((*AddSearchItemReq)(nil), "cplsearch.AddSearchItemReq")
	proto.RegisterType((*AddSearchItemResp)(nil), "cplsearch.AddSearchItemResp")
	proto.RegisterType((*DeleteSearchItemReq)(nil), "cplsearch.DeleteSearchItemReq")
	proto.RegisterType((*DeleteSearchItemResp)(nil), "cplsearch.DeleteSearchItemResp")
	proto.RegisterType((*UpdateSearchItemReq)(nil), "cplsearch.UpdateSearchItemReq")
	proto.RegisterType((*UpdateSearchItemResp)(nil), "cplsearch.UpdateSearchItemResp")
	proto.RegisterType((*SearchItem)(nil), "cplsearch.SearchItem")
	proto.RegisterType((*ResultItem)(nil), "cplsearch.ResultItem")
	proto.RegisterType((*Activity)(nil), "cplsearch.Activity")
	proto.RegisterType((*GetSearchItemListReq)(nil), "cplsearch.GetSearchItemListReq")
	proto.RegisterType((*GetSearchItemListResp)(nil), "cplsearch.GetSearchItemListResp")
	proto.RegisterType((*SearchReq)(nil), "cplsearch.SearchReq")
	proto.RegisterType((*SearchResp)(nil), "cplsearch.SearchResp")
	proto.RegisterEnum("cplsearch.PlatformType", PlatformType_name, PlatformType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// CplSearchClient is the client API for CplSearch service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type CplSearchClient interface {
	AddSearchItem(ctx context.Context, in *AddSearchItemReq, opts ...grpc.CallOption) (*AddSearchItemResp, error)
	UpdateSearchItem(ctx context.Context, in *UpdateSearchItemReq, opts ...grpc.CallOption) (*UpdateSearchItemResp, error)
	GetSearchItemList(ctx context.Context, in *GetSearchItemListReq, opts ...grpc.CallOption) (*GetSearchItemListResp, error)
	DeleteSearchItem(ctx context.Context, in *DeleteSearchItemReq, opts ...grpc.CallOption) (*DeleteSearchItemResp, error)
	Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*SearchResp, error)
}

type cplSearchClient struct {
	cc *grpc.ClientConn
}

func NewCplSearchClient(cc *grpc.ClientConn) CplSearchClient {
	return &cplSearchClient{cc}
}

func (c *cplSearchClient) AddSearchItem(ctx context.Context, in *AddSearchItemReq, opts ...grpc.CallOption) (*AddSearchItemResp, error) {
	out := new(AddSearchItemResp)
	err := c.cc.Invoke(ctx, "/cplsearch.CplSearch/AddSearchItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cplSearchClient) UpdateSearchItem(ctx context.Context, in *UpdateSearchItemReq, opts ...grpc.CallOption) (*UpdateSearchItemResp, error) {
	out := new(UpdateSearchItemResp)
	err := c.cc.Invoke(ctx, "/cplsearch.CplSearch/UpdateSearchItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cplSearchClient) GetSearchItemList(ctx context.Context, in *GetSearchItemListReq, opts ...grpc.CallOption) (*GetSearchItemListResp, error) {
	out := new(GetSearchItemListResp)
	err := c.cc.Invoke(ctx, "/cplsearch.CplSearch/GetSearchItemList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cplSearchClient) DeleteSearchItem(ctx context.Context, in *DeleteSearchItemReq, opts ...grpc.CallOption) (*DeleteSearchItemResp, error) {
	out := new(DeleteSearchItemResp)
	err := c.cc.Invoke(ctx, "/cplsearch.CplSearch/DeleteSearchItem", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cplSearchClient) Search(ctx context.Context, in *SearchReq, opts ...grpc.CallOption) (*SearchResp, error) {
	out := new(SearchResp)
	err := c.cc.Invoke(ctx, "/cplsearch.CplSearch/Search", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CplSearchServer is the server API for CplSearch service.
type CplSearchServer interface {
	AddSearchItem(context.Context, *AddSearchItemReq) (*AddSearchItemResp, error)
	UpdateSearchItem(context.Context, *UpdateSearchItemReq) (*UpdateSearchItemResp, error)
	GetSearchItemList(context.Context, *GetSearchItemListReq) (*GetSearchItemListResp, error)
	DeleteSearchItem(context.Context, *DeleteSearchItemReq) (*DeleteSearchItemResp, error)
	Search(context.Context, *SearchReq) (*SearchResp, error)
}

func RegisterCplSearchServer(s *grpc.Server, srv CplSearchServer) {
	s.RegisterService(&_CplSearch_serviceDesc, srv)
}

func _CplSearch_AddSearchItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddSearchItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CplSearchServer).AddSearchItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cplsearch.CplSearch/AddSearchItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CplSearchServer).AddSearchItem(ctx, req.(*AddSearchItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CplSearch_UpdateSearchItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSearchItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CplSearchServer).UpdateSearchItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cplsearch.CplSearch/UpdateSearchItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CplSearchServer).UpdateSearchItem(ctx, req.(*UpdateSearchItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CplSearch_GetSearchItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSearchItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CplSearchServer).GetSearchItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cplsearch.CplSearch/GetSearchItemList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CplSearchServer).GetSearchItemList(ctx, req.(*GetSearchItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CplSearch_DeleteSearchItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSearchItemReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CplSearchServer).DeleteSearchItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cplsearch.CplSearch/DeleteSearchItem",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CplSearchServer).DeleteSearchItem(ctx, req.(*DeleteSearchItemReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _CplSearch_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CplSearchServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/cplsearch.CplSearch/Search",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CplSearchServer).Search(ctx, req.(*SearchReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _CplSearch_serviceDesc = grpc.ServiceDesc{
	ServiceName: "cplsearch.CplSearch",
	HandlerType: (*CplSearchServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddSearchItem",
			Handler:    _CplSearch_AddSearchItem_Handler,
		},
		{
			MethodName: "UpdateSearchItem",
			Handler:    _CplSearch_UpdateSearchItem_Handler,
		},
		{
			MethodName: "GetSearchItemList",
			Handler:    _CplSearch_GetSearchItemList_Handler,
		},
		{
			MethodName: "DeleteSearchItem",
			Handler:    _CplSearch_DeleteSearchItem_Handler,
		},
		{
			MethodName: "Search",
			Handler:    _CplSearch_Search_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cpl-search/cpl-search.proto",
}

func init() {
	proto.RegisterFile("cpl-search/cpl-search.proto", fileDescriptor_cpl_search_8e8ae12171831c55)
}

var fileDescriptor_cpl_search_8e8ae12171831c55 = []byte{
	// 774 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0x61, 0x6f, 0xda, 0x48,
	0x10, 0xc5, 0x18, 0x30, 0x0c, 0x90, 0x23, 0x1b, 0xc8, 0xf9, 0xc8, 0xdd, 0x05, 0x59, 0xca, 0x89,
	0xbb, 0x28, 0xa0, 0xa3, 0xaa, 0x52, 0x55, 0x6d, 0x55, 0x92, 0x48, 0x0d, 0x2a, 0x22, 0x95, 0x53,
	0xda, 0xaa, 0x5f, 0x2c, 0x63, 0x6f, 0x89, 0x1b, 0x63, 0x6f, 0xbd, 0x0b, 0x11, 0x7f, 0xa5, 0x7f,
	0xa2, 0x9f, 0xfb, 0xef, 0xaa, 0x5d, 0x1b, 0x30, 0x86, 0x44, 0xca, 0xb7, 0x9d, 0x37, 0x33, 0xbb,
	0xcf, 0x6f, 0xdf, 0x8e, 0xe1, 0xc0, 0x22, 0xee, 0x09, 0xc5, 0x66, 0x60, 0xdd, 0xb4, 0x57, 0xcb,
	0x16, 0x09, 0x7c, 0xe6, 0xa3, 0x82, 0x45, 0xdc, 0x10, 0xd0, 0x5e, 0x42, 0xa5, 0x6b, 0xdb, 0xd7,
	0x22, 0xe8, 0x31, 0x3c, 0xd1, 0xf1, 0x37, 0xf4, 0x2f, 0x64, 0x1c, 0x86, 0x27, 0xaa, 0xd4, 0x90,
	0x9a, 0xc5, 0x4e, 0xad, 0xb5, 0xac, 0x6e, 0xc5, 0xea, 0x44, 0x89, 0xf6, 0x0a, 0x76, 0x13, 0xed,
	0x94, 0x3c, 0xa6, 0xff, 0x08, 0xf6, 0x2e, 0xb0, 0x8b, 0x19, 0x5e, 0x67, 0xb0, 0x03, 0x69, 0xc7,
	0x16, 0xfd, 0x05, 0x3d, 0xed, 0xd8, 0xda, 0x3e, 0x54, 0x37, 0xcb, 0x28, 0xd1, 0x5e, 0xc3, 0xde,
	0x90, 0xd8, 0x66, 0xb2, 0xfd, 0x11, 0x04, 0xf6, 0xa1, 0xba, 0xb9, 0x03, 0x25, 0xda, 0x4f, 0x19,
	0x60, 0x05, 0x25, 0x09, 0xa1, 0x2a, 0x64, 0x99, 0xc3, 0x5c, 0xac, 0xa6, 0x05, 0x14, 0x06, 0xbc,
	0x6a, 0x34, 0x56, 0xe5, 0xb0, 0x6a, 0x34, 0x46, 0x75, 0xc8, 0xdf, 0xe2, 0xf9, 0x9d, 0x1f, 0xd8,
	0x54, 0xcd, 0x34, 0xe4, 0x66, 0x41, 0x5f, 0xc6, 0xe8, 0x0f, 0xc8, 0x8f, 0xf0, 0xd8, 0xf1, 0x0c,
	0x93, 0xa9, 0xd9, 0x86, 0xd4, 0x2c, 0xeb, 0x8a, 0x88, 0xbb, 0x0c, 0xd5, 0x20, 0x87, 0x3d, 0x9b,
	0x27, 0x72, 0x22, 0x91, 0xc5, 0x9e, 0xdd, 0x65, 0x48, 0x05, 0xc5, 0xb4, 0x2c, 0x7f, 0xea, 0x31,
	0x55, 0x11, 0x47, 0x2c, 0x42, 0xf4, 0x17, 0x80, 0x75, 0x63, 0x7a, 0x1e, 0x76, 0x0d, 0xc7, 0x56,
	0xf3, 0xa2, 0xa9, 0x10, 0x21, 0x3d, 0x1b, 0xb5, 0x21, 0x6f, 0x5a, 0xcc, 0x99, 0x39, 0x6c, 0xae,
	0x16, 0x84, 0x24, 0x7b, 0x31, 0x49, 0xba, 0x51, 0x4a, 0x5f, 0x16, 0xa1, 0x67, 0x50, 0x0a, 0x30,
	0x9d, 0xba, 0xcc, 0xe0, 0x1a, 0x51, 0x15, 0x1a, 0x72, 0x42, 0x47, 0x5d, 0xa4, 0x85, 0x5a, 0xc5,
	0x60, 0xb9, 0xa6, 0xe8, 0x05, 0x94, 0x89, 0x6b, 0xb2, 0x2f, 0x7e, 0x30, 0x31, 0xd8, 0x9c, 0x60,
	0xb5, 0xd8, 0x90, 0x9a, 0x3b, 0x9d, 0xdf, 0x63, 0xad, 0xef, 0xa2, 0xfc, 0xfb, 0x39, 0xc1, 0x7a,
	0x89, 0xc4, 0x22, 0xfe, 0x1d, 0x13, 0x33, 0xb8, 0xc5, 0xcc, 0x70, 0x6c, 0xaa, 0x96, 0x1a, 0x32,
	0xff, 0x8e, 0x10, 0xe9, 0xd9, 0x14, 0xfd, 0x03, 0xbf, 0x2d, 0x3e, 0x73, 0xe6, 0xe0, 0x3b, 0xfe,
	0xad, 0x65, 0x21, 0x44, 0x39, 0x82, 0x3f, 0x38, 0xf8, 0xae, 0x67, 0x6b, 0x3f, 0x24, 0x80, 0x15,
	0x41, 0x54, 0x5f, 0xe9, 0x26, 0x2e, 0xf0, 0x32, 0xb5, 0x52, 0xee, 0x70, 0x4d, 0x39, 0x7e, 0x99,
	0xe5, 0xcb, 0x54, 0x5c, 0xbb, 0xff, 0x63, 0xda, 0xc9, 0xf7, 0x6a, 0x77, 0x99, 0x8a, 0xa9, 0xd7,
	0xdc, 0xa4, 0x99, 0x89, 0xce, 0x5d, 0x27, 0x7a, 0xa6, 0x40, 0x76, 0x66, 0xba, 0x53, 0xac, 0x59,
	0x90, 0x5f, 0x6c, 0xb5, 0xb2, 0x96, 0x14, 0xb7, 0x16, 0x82, 0x8c, 0x8d, 0xa9, 0x15, 0xf9, 0x4d,
	0xac, 0x39, 0xe6, 0x58, 0xbe, 0x17, 0x19, 0x4e, 0xac, 0xb9, 0xad, 0xbe, 0x4e, 0x27, 0xc4, 0x98,
	0x06, 0x6e, 0x78, 0xaa, 0xae, 0xf0, 0x78, 0x18, 0xb8, 0x9a, 0x01, 0xd5, 0x37, 0x98, 0xad, 0x4c,
	0xdd, 0x77, 0x28, 0xe3, 0xaf, 0x05, 0x41, 0x86, 0x98, 0xe3, 0xf0, 0xbc, 0xb2, 0x2e, 0xd6, 0x9c,
	0x44, 0xa8, 0x58, 0x3a, 0x74, 0xe0, 0xd2, 0x67, 0xb1, 0xfb, 0x91, 0x13, 0xf7, 0xa3, 0x61, 0xa8,
	0x6d, 0x39, 0x80, 0x12, 0x74, 0x0c, 0xd9, 0xd0, 0x48, 0xd2, 0x86, 0x91, 0x62, 0xcf, 0x2e, 0xac,
	0x41, 0x87, 0x50, 0x64, 0x3e, 0x33, 0x5d, 0x23, 0x4e, 0x00, 0x04, 0x74, 0xce, 0x11, 0xed, 0x08,
	0x0a, 0x61, 0x17, 0x27, 0xaf, 0x82, 0x12, 0x3d, 0xa9, 0x48, 0xaf, 0x45, 0xa8, 0x9d, 0x2e, 0x1e,
	0xf0, 0x23, 0x67, 0xd2, 0x7f, 0xcf, 0xa1, 0x14, 0xf7, 0x28, 0x2a, 0x82, 0x32, 0x1c, 0xbc, 0x1d,
	0x5c, 0x7d, 0x1c, 0x54, 0x52, 0x48, 0x01, 0xb9, 0xdb, 0xef, 0x57, 0x24, 0x8e, 0x76, 0x07, 0x17,
	0xfa, 0x55, 0xef, 0xa2, 0x92, 0xe6, 0x68, 0xef, 0xea, 0xba, 0x92, 0xe9, 0x7c, 0x97, 0xa1, 0x70,
	0x4e, 0xdc, 0x70, 0x4f, 0xd4, 0x87, 0xf2, 0xda, 0x74, 0x44, 0x07, 0x71, 0xef, 0x24, 0xc6, 0x6e,
	0xfd, 0xcf, 0xfb, 0x93, 0x94, 0x68, 0x29, 0x34, 0x84, 0x4a, 0x72, 0x54, 0xa1, 0xbf, 0x63, 0x3d,
	0x5b, 0x26, 0x61, 0xfd, 0xf0, 0xc1, 0xbc, 0xd8, 0xf6, 0x13, 0xec, 0x6e, 0xdc, 0x1a, 0x8a, 0xf7,
	0x6d, 0x33, 0x4d, 0xbd, 0xf1, 0x70, 0xc1, 0x82, 0x70, 0x72, 0x6a, 0xaf, 0x11, 0xde, 0x32, 0xf9,
	0xd7, 0x08, 0x6f, 0x1d, 0xf9, 0x29, 0x74, 0x0a, 0xb9, 0x48, 0xdf, 0xea, 0xc6, 0x35, 0xf2, 0x2d,
	0x6a, 0x5b, 0x50, 0xde, 0x78, 0x76, 0xf2, 0xf9, 0x78, 0xec, 0xbb, 0xa6, 0x37, 0x6e, 0x3d, 0xed,
	0x30, 0xd6, 0xb2, 0xfc, 0x49, 0x5b, 0xfc, 0x0f, 0x2d, 0xdf, 0x6d, 0x53, 0x1c, 0xcc, 0x1c, 0x0b,
	0xd3, 0xf6, 0xb2, 0x77, 0x94, 0x13, 0xc9, 0x27, 0xbf, 0x02, 0x00, 0x00, 0xff, 0xff, 0x96, 0x74,
	0x8b, 0xa4, 0x4b, 0x07, 0x00, 0x00,
}
