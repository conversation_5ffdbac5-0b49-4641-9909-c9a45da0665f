// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-listening-logic/channel-listening-logic.proto

package channellisteninglogic // import "golang.52tt.com/protocol/services/logicsvr-go/channellisteninglogic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channellisteninglogic "golang.52tt.com/protocol/app/channellisteninglogic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelListeningLogicClient is the client API for ChannelListeningLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelListeningLogicClient interface {
	CheckListeningChannelTab(ctx context.Context, in *channellisteninglogic.CheckListeningChannelTabReq, opts ...grpc.CallOption) (*channellisteninglogic.CheckListeningChannelTabResp, error)
	ListeningCheckInV3(ctx context.Context, in *channellisteninglogic.ListeningCheckInV3Req, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInV3Resp, error)
	ListeningCheckInSharedV3(ctx context.Context, in *channellisteninglogic.ListeningCheckInSharedReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInSharedResp, error)
	ListeningDropCardV3(ctx context.Context, in *channellisteninglogic.ListeningDropCardV3Req, opts ...grpc.CallOption) (*channellisteninglogic.ListeningDropCardV3Resp, error)
	Constellation(ctx context.Context, in *channellisteninglogic.ConstellationReq, opts ...grpc.CallOption) (*channellisteninglogic.ConstellationResp, error)
	GetChannelListeningSimpleInfo(ctx context.Context, in *channellisteninglogic.GetChannelListeningSimpleInfoReq, opts ...grpc.CallOption) (*channellisteninglogic.GetChannelListeningSimpleInfoResp, error)
	LikeSong(ctx context.Context, in *channellisteninglogic.LikeSongReq, opts ...grpc.CallOption) (*channellisteninglogic.LikeSongResp, error)
	GetBeLikedSongList(ctx context.Context, in *channellisteninglogic.GetBeLikedSongListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetBeLikedSongListResp, error)
	GetLikedSongList(ctx context.Context, in *channellisteninglogic.GetLikedSongListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetLikedSongListResp, error)
	GetUserFlowerList(ctx context.Context, in *channellisteninglogic.GetUserFlowerListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetUserFlowerListResp, error)
	GetFlowerDetail(ctx context.Context, in *channellisteninglogic.GetFlowerDetailReq, opts ...grpc.CallOption) (*channellisteninglogic.GetFlowerDetailResp, error)
	SetListeningCheckInTopic(ctx context.Context, in *channellisteninglogic.SetListeningCheckInTopicReq, opts ...grpc.CallOption) (*channellisteninglogic.SetListeningCheckInTopicResp, error)
	ListeningCheckIn(ctx context.Context, in *channellisteninglogic.ListeningCheckInReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInResp, error)
	GetListeningUserCheckInInfoList(ctx context.Context, in *channellisteninglogic.GetListeningUserCheckInInfoListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetListeningUserCheckInInfoListResp, error)
	GetRcmdSongMenu(ctx context.Context, in *channellisteninglogic.GetRcmdSongMenuReq, opts ...grpc.CallOption) (*channellisteninglogic.GetRcmdSongMenuResp, error)
	GetSongByMenuId(ctx context.Context, in *channellisteninglogic.GetSongByMenuIdReq, opts ...grpc.CallOption) (*channellisteninglogic.GetSongByMenuIdResp, error)
	GetAllMoodCfg(ctx context.Context, in *channellisteninglogic.GetAllMoodCfgReq, opts ...grpc.CallOption) (*channellisteninglogic.GetAllMoodCfgResp, error)
	SetListeningUserMood(ctx context.Context, in *channellisteninglogic.SetListeningUserMoodReq, opts ...grpc.CallOption) (*channellisteninglogic.SetListeningUserMoodResp, error)
	GetAllListeningOnMicUserMood(ctx context.Context, in *channellisteninglogic.GetAllListeningOnMicUserMoodReq, opts ...grpc.CallOption) (*channellisteninglogic.GetAllListeningOnMicUserMoodResp, error)
	ListeningCheckInV2(ctx context.Context, in *channellisteninglogic.ListeningCheckInReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInResp, error)
	ListeningCheckInShared(ctx context.Context, in *channellisteninglogic.ListeningCheckInSharedReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInSharedResp, error)
}

type channelListeningLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelListeningLogicClient(cc *grpc.ClientConn) ChannelListeningLogicClient {
	return &channelListeningLogicClient{cc}
}

func (c *channelListeningLogicClient) CheckListeningChannelTab(ctx context.Context, in *channellisteninglogic.CheckListeningChannelTabReq, opts ...grpc.CallOption) (*channellisteninglogic.CheckListeningChannelTabResp, error) {
	out := new(channellisteninglogic.CheckListeningChannelTabResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/CheckListeningChannelTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) ListeningCheckInV3(ctx context.Context, in *channellisteninglogic.ListeningCheckInV3Req, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInV3Resp, error) {
	out := new(channellisteninglogic.ListeningCheckInV3Resp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/ListeningCheckInV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) ListeningCheckInSharedV3(ctx context.Context, in *channellisteninglogic.ListeningCheckInSharedReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInSharedResp, error) {
	out := new(channellisteninglogic.ListeningCheckInSharedResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/ListeningCheckInSharedV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) ListeningDropCardV3(ctx context.Context, in *channellisteninglogic.ListeningDropCardV3Req, opts ...grpc.CallOption) (*channellisteninglogic.ListeningDropCardV3Resp, error) {
	out := new(channellisteninglogic.ListeningDropCardV3Resp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/ListeningDropCardV3", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) Constellation(ctx context.Context, in *channellisteninglogic.ConstellationReq, opts ...grpc.CallOption) (*channellisteninglogic.ConstellationResp, error) {
	out := new(channellisteninglogic.ConstellationResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/Constellation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetChannelListeningSimpleInfo(ctx context.Context, in *channellisteninglogic.GetChannelListeningSimpleInfoReq, opts ...grpc.CallOption) (*channellisteninglogic.GetChannelListeningSimpleInfoResp, error) {
	out := new(channellisteninglogic.GetChannelListeningSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetChannelListeningSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) LikeSong(ctx context.Context, in *channellisteninglogic.LikeSongReq, opts ...grpc.CallOption) (*channellisteninglogic.LikeSongResp, error) {
	out := new(channellisteninglogic.LikeSongResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/LikeSong", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetBeLikedSongList(ctx context.Context, in *channellisteninglogic.GetBeLikedSongListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetBeLikedSongListResp, error) {
	out := new(channellisteninglogic.GetBeLikedSongListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetBeLikedSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetLikedSongList(ctx context.Context, in *channellisteninglogic.GetLikedSongListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetLikedSongListResp, error) {
	out := new(channellisteninglogic.GetLikedSongListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetLikedSongList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetUserFlowerList(ctx context.Context, in *channellisteninglogic.GetUserFlowerListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetUserFlowerListResp, error) {
	out := new(channellisteninglogic.GetUserFlowerListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetUserFlowerList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetFlowerDetail(ctx context.Context, in *channellisteninglogic.GetFlowerDetailReq, opts ...grpc.CallOption) (*channellisteninglogic.GetFlowerDetailResp, error) {
	out := new(channellisteninglogic.GetFlowerDetailResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetFlowerDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) SetListeningCheckInTopic(ctx context.Context, in *channellisteninglogic.SetListeningCheckInTopicReq, opts ...grpc.CallOption) (*channellisteninglogic.SetListeningCheckInTopicResp, error) {
	out := new(channellisteninglogic.SetListeningCheckInTopicResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/SetListeningCheckInTopic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) ListeningCheckIn(ctx context.Context, in *channellisteninglogic.ListeningCheckInReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInResp, error) {
	out := new(channellisteninglogic.ListeningCheckInResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/ListeningCheckIn", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetListeningUserCheckInInfoList(ctx context.Context, in *channellisteninglogic.GetListeningUserCheckInInfoListReq, opts ...grpc.CallOption) (*channellisteninglogic.GetListeningUserCheckInInfoListResp, error) {
	out := new(channellisteninglogic.GetListeningUserCheckInInfoListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetListeningUserCheckInInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetRcmdSongMenu(ctx context.Context, in *channellisteninglogic.GetRcmdSongMenuReq, opts ...grpc.CallOption) (*channellisteninglogic.GetRcmdSongMenuResp, error) {
	out := new(channellisteninglogic.GetRcmdSongMenuResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetRcmdSongMenu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetSongByMenuId(ctx context.Context, in *channellisteninglogic.GetSongByMenuIdReq, opts ...grpc.CallOption) (*channellisteninglogic.GetSongByMenuIdResp, error) {
	out := new(channellisteninglogic.GetSongByMenuIdResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetSongByMenuId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetAllMoodCfg(ctx context.Context, in *channellisteninglogic.GetAllMoodCfgReq, opts ...grpc.CallOption) (*channellisteninglogic.GetAllMoodCfgResp, error) {
	out := new(channellisteninglogic.GetAllMoodCfgResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetAllMoodCfg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) SetListeningUserMood(ctx context.Context, in *channellisteninglogic.SetListeningUserMoodReq, opts ...grpc.CallOption) (*channellisteninglogic.SetListeningUserMoodResp, error) {
	out := new(channellisteninglogic.SetListeningUserMoodResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/SetListeningUserMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) GetAllListeningOnMicUserMood(ctx context.Context, in *channellisteninglogic.GetAllListeningOnMicUserMoodReq, opts ...grpc.CallOption) (*channellisteninglogic.GetAllListeningOnMicUserMoodResp, error) {
	out := new(channellisteninglogic.GetAllListeningOnMicUserMoodResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/GetAllListeningOnMicUserMood", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) ListeningCheckInV2(ctx context.Context, in *channellisteninglogic.ListeningCheckInReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInResp, error) {
	out := new(channellisteninglogic.ListeningCheckInResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/ListeningCheckInV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelListeningLogicClient) ListeningCheckInShared(ctx context.Context, in *channellisteninglogic.ListeningCheckInSharedReq, opts ...grpc.CallOption) (*channellisteninglogic.ListeningCheckInSharedResp, error) {
	out := new(channellisteninglogic.ListeningCheckInSharedResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelListeningLogic/ListeningCheckInShared", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelListeningLogicServer is the server API for ChannelListeningLogic service.
type ChannelListeningLogicServer interface {
	CheckListeningChannelTab(context.Context, *channellisteninglogic.CheckListeningChannelTabReq) (*channellisteninglogic.CheckListeningChannelTabResp, error)
	ListeningCheckInV3(context.Context, *channellisteninglogic.ListeningCheckInV3Req) (*channellisteninglogic.ListeningCheckInV3Resp, error)
	ListeningCheckInSharedV3(context.Context, *channellisteninglogic.ListeningCheckInSharedReq) (*channellisteninglogic.ListeningCheckInSharedResp, error)
	ListeningDropCardV3(context.Context, *channellisteninglogic.ListeningDropCardV3Req) (*channellisteninglogic.ListeningDropCardV3Resp, error)
	Constellation(context.Context, *channellisteninglogic.ConstellationReq) (*channellisteninglogic.ConstellationResp, error)
	GetChannelListeningSimpleInfo(context.Context, *channellisteninglogic.GetChannelListeningSimpleInfoReq) (*channellisteninglogic.GetChannelListeningSimpleInfoResp, error)
	LikeSong(context.Context, *channellisteninglogic.LikeSongReq) (*channellisteninglogic.LikeSongResp, error)
	GetBeLikedSongList(context.Context, *channellisteninglogic.GetBeLikedSongListReq) (*channellisteninglogic.GetBeLikedSongListResp, error)
	GetLikedSongList(context.Context, *channellisteninglogic.GetLikedSongListReq) (*channellisteninglogic.GetLikedSongListResp, error)
	GetUserFlowerList(context.Context, *channellisteninglogic.GetUserFlowerListReq) (*channellisteninglogic.GetUserFlowerListResp, error)
	GetFlowerDetail(context.Context, *channellisteninglogic.GetFlowerDetailReq) (*channellisteninglogic.GetFlowerDetailResp, error)
	SetListeningCheckInTopic(context.Context, *channellisteninglogic.SetListeningCheckInTopicReq) (*channellisteninglogic.SetListeningCheckInTopicResp, error)
	ListeningCheckIn(context.Context, *channellisteninglogic.ListeningCheckInReq) (*channellisteninglogic.ListeningCheckInResp, error)
	GetListeningUserCheckInInfoList(context.Context, *channellisteninglogic.GetListeningUserCheckInInfoListReq) (*channellisteninglogic.GetListeningUserCheckInInfoListResp, error)
	GetRcmdSongMenu(context.Context, *channellisteninglogic.GetRcmdSongMenuReq) (*channellisteninglogic.GetRcmdSongMenuResp, error)
	GetSongByMenuId(context.Context, *channellisteninglogic.GetSongByMenuIdReq) (*channellisteninglogic.GetSongByMenuIdResp, error)
	GetAllMoodCfg(context.Context, *channellisteninglogic.GetAllMoodCfgReq) (*channellisteninglogic.GetAllMoodCfgResp, error)
	SetListeningUserMood(context.Context, *channellisteninglogic.SetListeningUserMoodReq) (*channellisteninglogic.SetListeningUserMoodResp, error)
	GetAllListeningOnMicUserMood(context.Context, *channellisteninglogic.GetAllListeningOnMicUserMoodReq) (*channellisteninglogic.GetAllListeningOnMicUserMoodResp, error)
	ListeningCheckInV2(context.Context, *channellisteninglogic.ListeningCheckInReq) (*channellisteninglogic.ListeningCheckInResp, error)
	ListeningCheckInShared(context.Context, *channellisteninglogic.ListeningCheckInSharedReq) (*channellisteninglogic.ListeningCheckInSharedResp, error)
}

func RegisterChannelListeningLogicServer(s *grpc.Server, srv ChannelListeningLogicServer) {
	s.RegisterService(&_ChannelListeningLogic_serviceDesc, srv)
}

func _ChannelListeningLogic_CheckListeningChannelTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.CheckListeningChannelTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).CheckListeningChannelTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/CheckListeningChannelTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).CheckListeningChannelTab(ctx, req.(*channellisteninglogic.CheckListeningChannelTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_ListeningCheckInV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ListeningCheckInV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).ListeningCheckInV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/ListeningCheckInV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).ListeningCheckInV3(ctx, req.(*channellisteninglogic.ListeningCheckInV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_ListeningCheckInSharedV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ListeningCheckInSharedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).ListeningCheckInSharedV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/ListeningCheckInSharedV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).ListeningCheckInSharedV3(ctx, req.(*channellisteninglogic.ListeningCheckInSharedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_ListeningDropCardV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ListeningDropCardV3Req)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).ListeningDropCardV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/ListeningDropCardV3",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).ListeningDropCardV3(ctx, req.(*channellisteninglogic.ListeningDropCardV3Req))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_Constellation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ConstellationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).Constellation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/Constellation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).Constellation(ctx, req.(*channellisteninglogic.ConstellationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetChannelListeningSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetChannelListeningSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetChannelListeningSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetChannelListeningSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetChannelListeningSimpleInfo(ctx, req.(*channellisteninglogic.GetChannelListeningSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_LikeSong_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.LikeSongReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).LikeSong(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/LikeSong",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).LikeSong(ctx, req.(*channellisteninglogic.LikeSongReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetBeLikedSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetBeLikedSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetBeLikedSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetBeLikedSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetBeLikedSongList(ctx, req.(*channellisteninglogic.GetBeLikedSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetLikedSongList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetLikedSongListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetLikedSongList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetLikedSongList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetLikedSongList(ctx, req.(*channellisteninglogic.GetLikedSongListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetUserFlowerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetUserFlowerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetUserFlowerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetUserFlowerList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetUserFlowerList(ctx, req.(*channellisteninglogic.GetUserFlowerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetFlowerDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetFlowerDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetFlowerDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetFlowerDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetFlowerDetail(ctx, req.(*channellisteninglogic.GetFlowerDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_SetListeningCheckInTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.SetListeningCheckInTopicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).SetListeningCheckInTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/SetListeningCheckInTopic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).SetListeningCheckInTopic(ctx, req.(*channellisteninglogic.SetListeningCheckInTopicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_ListeningCheckIn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ListeningCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).ListeningCheckIn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/ListeningCheckIn",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).ListeningCheckIn(ctx, req.(*channellisteninglogic.ListeningCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetListeningUserCheckInInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetListeningUserCheckInInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetListeningUserCheckInInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetListeningUserCheckInInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetListeningUserCheckInInfoList(ctx, req.(*channellisteninglogic.GetListeningUserCheckInInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetRcmdSongMenu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetRcmdSongMenuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetRcmdSongMenu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetRcmdSongMenu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetRcmdSongMenu(ctx, req.(*channellisteninglogic.GetRcmdSongMenuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetSongByMenuId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetSongByMenuIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetSongByMenuId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetSongByMenuId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetSongByMenuId(ctx, req.(*channellisteninglogic.GetSongByMenuIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetAllMoodCfg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetAllMoodCfgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetAllMoodCfg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetAllMoodCfg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetAllMoodCfg(ctx, req.(*channellisteninglogic.GetAllMoodCfgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_SetListeningUserMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.SetListeningUserMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).SetListeningUserMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/SetListeningUserMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).SetListeningUserMood(ctx, req.(*channellisteninglogic.SetListeningUserMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_GetAllListeningOnMicUserMood_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.GetAllListeningOnMicUserMoodReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).GetAllListeningOnMicUserMood(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/GetAllListeningOnMicUserMood",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).GetAllListeningOnMicUserMood(ctx, req.(*channellisteninglogic.GetAllListeningOnMicUserMoodReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_ListeningCheckInV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ListeningCheckInReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).ListeningCheckInV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/ListeningCheckInV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).ListeningCheckInV2(ctx, req.(*channellisteninglogic.ListeningCheckInReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelListeningLogic_ListeningCheckInShared_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channellisteninglogic.ListeningCheckInSharedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelListeningLogicServer).ListeningCheckInShared(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelListeningLogic/ListeningCheckInShared",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelListeningLogicServer).ListeningCheckInShared(ctx, req.(*channellisteninglogic.ListeningCheckInSharedReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelListeningLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelListeningLogic",
	HandlerType: (*ChannelListeningLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckListeningChannelTab",
			Handler:    _ChannelListeningLogic_CheckListeningChannelTab_Handler,
		},
		{
			MethodName: "ListeningCheckInV3",
			Handler:    _ChannelListeningLogic_ListeningCheckInV3_Handler,
		},
		{
			MethodName: "ListeningCheckInSharedV3",
			Handler:    _ChannelListeningLogic_ListeningCheckInSharedV3_Handler,
		},
		{
			MethodName: "ListeningDropCardV3",
			Handler:    _ChannelListeningLogic_ListeningDropCardV3_Handler,
		},
		{
			MethodName: "Constellation",
			Handler:    _ChannelListeningLogic_Constellation_Handler,
		},
		{
			MethodName: "GetChannelListeningSimpleInfo",
			Handler:    _ChannelListeningLogic_GetChannelListeningSimpleInfo_Handler,
		},
		{
			MethodName: "LikeSong",
			Handler:    _ChannelListeningLogic_LikeSong_Handler,
		},
		{
			MethodName: "GetBeLikedSongList",
			Handler:    _ChannelListeningLogic_GetBeLikedSongList_Handler,
		},
		{
			MethodName: "GetLikedSongList",
			Handler:    _ChannelListeningLogic_GetLikedSongList_Handler,
		},
		{
			MethodName: "GetUserFlowerList",
			Handler:    _ChannelListeningLogic_GetUserFlowerList_Handler,
		},
		{
			MethodName: "GetFlowerDetail",
			Handler:    _ChannelListeningLogic_GetFlowerDetail_Handler,
		},
		{
			MethodName: "SetListeningCheckInTopic",
			Handler:    _ChannelListeningLogic_SetListeningCheckInTopic_Handler,
		},
		{
			MethodName: "ListeningCheckIn",
			Handler:    _ChannelListeningLogic_ListeningCheckIn_Handler,
		},
		{
			MethodName: "GetListeningUserCheckInInfoList",
			Handler:    _ChannelListeningLogic_GetListeningUserCheckInInfoList_Handler,
		},
		{
			MethodName: "GetRcmdSongMenu",
			Handler:    _ChannelListeningLogic_GetRcmdSongMenu_Handler,
		},
		{
			MethodName: "GetSongByMenuId",
			Handler:    _ChannelListeningLogic_GetSongByMenuId_Handler,
		},
		{
			MethodName: "GetAllMoodCfg",
			Handler:    _ChannelListeningLogic_GetAllMoodCfg_Handler,
		},
		{
			MethodName: "SetListeningUserMood",
			Handler:    _ChannelListeningLogic_SetListeningUserMood_Handler,
		},
		{
			MethodName: "GetAllListeningOnMicUserMood",
			Handler:    _ChannelListeningLogic_GetAllListeningOnMicUserMood_Handler,
		},
		{
			MethodName: "ListeningCheckInV2",
			Handler:    _ChannelListeningLogic_ListeningCheckInV2_Handler,
		},
		{
			MethodName: "ListeningCheckInShared",
			Handler:    _ChannelListeningLogic_ListeningCheckInShared_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-listening-logic/channel-listening-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-listening-logic/channel-listening-logic.proto", fileDescriptor_channel_listening_logic_6b1464eb9f186d9f)
}

var fileDescriptor_channel_listening_logic_6b1464eb9f186d9f = []byte{
	// 737 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x96, 0x4d, 0x6b, 0xd4, 0x40,
	0x18, 0xc7, 0x19, 0x50, 0xa9, 0x03, 0xa2, 0x3e, 0x6a, 0x29, 0x45, 0x11, 0x0a, 0x0a, 0xbe, 0x6c,
	0x62, 0x77, 0xad, 0x60, 0xd5, 0x43, 0xbb, 0xd5, 0x50, 0x68, 0x11, 0xba, 0xb5, 0x07, 0x2f, 0x92,
	0x66, 0xa7, 0x69, 0x68, 0x3a, 0x13, 0x93, 0xb1, 0xa5, 0x20, 0x0a, 0x82, 0xe0, 0x49, 0x3c, 0xf8,
	0x01, 0x04, 0x4f, 0x7e, 0x84, 0x7e, 0x07, 0xb5, 0xbe, 0xbf, 0xe2, 0xdb, 0x57, 0xf0, 0x38, 0x27,
	0x99, 0x64, 0x9b, 0x49, 0x76, 0x37, 0xc9, 0x66, 0x05, 0x3d, 0x2d, 0x9b, 0xf9, 0xfd, 0x9f, 0xff,
	0x3f, 0x99, 0x67, 0x5e, 0xf0, 0x84, 0xcb, 0x6c, 0xc7, 0x0a, 0xd6, 0xfc, 0x8a, 0xcd, 0x74, 0x6b,
	0xd9, 0xa4, 0x94, 0xb8, 0x15, 0xd7, 0x09, 0x38, 0xa1, 0x0e, 0xb5, 0x2b, 0xe1, 0x68, 0xd6, 0x73,
	0xcd, 0xf3, 0x19, 0x67, 0xb0, 0x33, 0xfc, 0x33, 0x7c, 0x22, 0x59, 0xc9, 0x36, 0x39, 0x59, 0x37,
	0x37, 0x74, 0xe6, 0x71, 0x87, 0xd1, 0x60, 0xfb, 0x37, 0x52, 0x0c, 0x8f, 0x98, 0x9e, 0x97, 0x55,
	0xf4, 0x46, 0xc4, 0x54, 0x37, 0x07, 0xf1, 0xa1, 0x7a, 0x84, 0xcc, 0x6c, 0x13, 0x33, 0x12, 0x80,
	0xc7, 0x08, 0x0f, 0xd5, 0x97, 0x89, 0xb5, 0x12, 0x3f, 0x6f, 0x71, 0xf3, 0xe6, 0x22, 0x8c, 0x69,
	0xb6, 0xa9, 0xb5, 0x4a, 0xc7, 0x95, 0xa3, 0xb4, 0x59, 0x9a, 0x39, 0x72, 0x73, 0xf8, 0x5c, 0x3f,
	0xb2, 0xc0, 0x1b, 0xd9, 0xfd, 0x7b, 0x6b, 0x53, 0xdb, 0x31, 0xf0, 0x5c, 0x20, 0xb8, 0x87, 0x30,
	0x24, 0x30, 0x62, 0xad, 0x4c, 0xd3, 0x85, 0x1a, 0xe8, 0xd9, 0x95, 0x3b, 0x69, 0x19, 0xe5, 0x4c,
	0x39, 0x81, 0x0a, 0xf1, 0x42, 0x20, 0x78, 0x84, 0xf0, 0x50, 0x3b, 0xd5, 0x58, 0x36, 0x7d, 0xd2,
	0x5c, 0xa8, 0x41, 0xad, 0xf7, 0xca, 0x91, 0x46, 0xc6, 0x39, 0x5b, 0x5e, 0xa4, 0x22, 0xbd, 0x14,
	0x08, 0xee, 0x23, 0x7c, 0x20, 0x26, 0xa7, 0x7c, 0xe6, 0xd5, 0x4d, 0x5f, 0xa6, 0xe9, 0xe5, 0x3d,
	0x15, 0x2e, 0xa3, 0x8c, 0x96, 0x54, 0xa8, 0x1c, 0x5b, 0x02, 0x41, 0x80, 0xf7, 0xd4, 0x19, 0x0d,
	0x38, 0x71, 0x5d, 0x53, 0x36, 0x23, 0x9c, 0xcc, 0x99, 0xf3, 0x24, 0x28, 0xad, 0x4f, 0xf5, 0xcc,
	0x2a, 0xd3, 0x57, 0x02, 0xc1, 0x53, 0x84, 0x8f, 0x18, 0x84, 0xb7, 0x37, 0x72, 0xc3, 0x59, 0xf5,
	0x5c, 0x32, 0x4d, 0x97, 0x18, 0x8c, 0x67, 0x57, 0xce, 0x15, 0xca, 0x54, 0x17, 0xfa, 0xd6, 0xaa,
	0x94, 0xaf, 0x05, 0x82, 0x26, 0x1e, 0x98, 0x71, 0x56, 0x48, 0x83, 0x51, 0x1b, 0x8e, 0xe5, 0x7d,
	0xe4, 0x88, 0x91, 0xd6, 0xc7, 0x7b, 0xc1, 0x94, 0xcb, 0x9b, 0xd6, 0x02, 0x31, 0x08, 0x9f, 0x24,
	0x12, 0x68, 0x4a, 0x42, 0x46, 0xcb, 0x5b, 0x20, 0x9d, 0x74, 0xc1, 0x02, 0xe9, 0x26, 0x50, 0x21,
	0xde, 0x0a, 0x04, 0xb7, 0xf1, 0x3e, 0x83, 0xf0, 0x74, 0x82, 0x4a, 0x6e, 0xc1, 0x0e, 0x7f, 0xad,
	0x0c, 0xae, 0xdc, 0xdf, 0x09, 0x04, 0x77, 0xf1, 0x7e, 0x83, 0xf0, 0x6b, 0x01, 0xf1, 0xaf, 0xb8,
	0x6c, 0x9d, 0xf8, 0xa1, 0x7d, 0x7e, 0xbd, 0x34, 0x2c, 0xfd, 0xf5, 0x52, 0xbc, 0x0a, 0xf0, 0x5e,
	0x20, 0xd8, 0xc0, 0x7b, 0x0d, 0xc2, 0xa3, 0xf1, 0x29, 0xc2, 0x4d, 0xc7, 0x85, 0xd3, 0xb9, 0xe5,
	0x92, 0xa8, 0x34, 0xaf, 0x94, 0xa0, 0x95, 0xf5, 0x07, 0x81, 0xc2, 0x6d, 0xbb, 0x21, 0xbf, 0x4f,
	0x7a, 0xd3, 0x98, 0x67, 0x9e, 0x63, 0xe5, 0x6d, 0xdb, 0x59, 0x9a, 0x82, 0x6d, 0x3b, 0x5b, 0xa6,
	0x62, 0x7d, 0x8c, 0x1a, 0xa2, 0x9d, 0xcb, 0x6b, 0x88, 0x76, 0xb6, 0xa0, 0x21, 0x3a, 0x71, 0xe5,
	0xfe, 0x49, 0x20, 0x78, 0x86, 0xf0, 0x51, 0x23, 0x91, 0x54, 0xce, 0x5e, 0x0b, 0x95, 0x0b, 0x35,
	0xec, 0x8f, 0x8b, 0x05, 0xfd, 0x96, 0x2d, 0x95, 0xe1, 0x2e, 0xfd, 0x85, 0x5a, 0x65, 0xfd, 0x1c,
	0xf7, 0xce, 0x9c, 0xb5, 0x1a, 0xb6, 0xf7, 0x2c, 0xa1, 0xb7, 0x0a, 0x7a, 0x27, 0x89, 0x16, 0xf7,
	0x4e, 0x9a, 0x56, 0xd6, 0x5f, 0x62, 0x6b, 0x39, 0x3a, 0xb9, 0x21, 0xc7, 0xa7, 0x9b, 0x05, 0xd6,
	0x49, 0xb4, 0xd8, 0x3a, 0x4d, 0x2b, 0xeb, 0xaf, 0xd1, 0xb1, 0x61, 0x10, 0x3e, 0xe1, 0xba, 0xb3,
	0x8c, 0x35, 0xeb, 0x4b, 0x76, 0xde, 0xb1, 0x91, 0x02, 0x0b, 0x8e, 0x8d, 0x36, 0x56, 0x99, 0x7e,
	0x13, 0x08, 0x1e, 0x20, 0x7c, 0xb0, 0xd1, 0x36, 0x3b, 0x12, 0x85, 0xd1, 0xde, 0x1a, 0x7e, 0x9b,
	0x97, 0x19, 0xaa, 0x65, 0x25, 0x2a, 0xca, 0x77, 0x81, 0xe0, 0x09, 0xc2, 0x87, 0xa3, 0xac, 0x31,
	0x7a, 0x95, 0xce, 0x3a, 0x56, 0x1c, 0xe9, 0x7c, 0xd1, 0x3b, 0x76, 0xd7, 0xc9, 0x68, 0xe3, 0xfd,
	0x4a, 0x55, 0xc4, 0x1f, 0x02, 0xc1, 0x9d, 0x2e, 0x17, 0xaf, 0xea, 0x3f, 0x5a, 0xc4, 0x3f, 0x05,
	0x82, 0x87, 0x08, 0x0f, 0x76, 0xbf, 0x0b, 0xfd, 0x87, 0x2b, 0xd7, 0x2f, 0x81, 0x26, 0x2f, 0x5f,
	0xaf, 0xdb, 0xcc, 0x35, 0xa9, 0xad, 0x8d, 0x55, 0x39, 0xd7, 0x2c, 0xb6, 0xaa, 0x87, 0x97, 0x6a,
	0x8b, 0xb9, 0x7a, 0x40, 0xfc, 0x35, 0xc7, 0x22, 0x81, 0xde, 0xe5, 0xe2, 0x9f, 0x36, 0x5b, 0xdc,
	0x15, 0x8a, 0x6a, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x82, 0xf9, 0x06, 0x79, 0x24, 0x0c, 0x00,
	0x00,
}
