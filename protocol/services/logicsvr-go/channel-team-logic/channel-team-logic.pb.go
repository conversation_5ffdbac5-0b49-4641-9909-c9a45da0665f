// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-team-logic/channel-team-logic.proto

package channel_team_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-team-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_team "golang.52tt.com/protocol/app/channel-team"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelTeamLogicClient is the client API for ChannelTeamLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelTeamLogicClient interface {
	// 用户进入小队
	JoinChannelTeam(ctx context.Context, in *channel_team.JoinChannelTeamReq, opts ...grpc.CallOption) (*channel_team.JoinChannelTeamResp, error)
	// 房主获取申请列表
	GetChannelTeamApplyList(ctx context.Context, in *channel_team.GetChannelTeamApplyListReq, opts ...grpc.CallOption) (*channel_team.GetChannelTeamApplyListResp, error)
	// 房主同意用户申请入队
	AgreeChannelTeamApply(ctx context.Context, in *channel_team.AgreeChannelTeamApplyReq, opts ...grpc.CallOption) (*channel_team.AgreeChannelTeamApplyResp, error)
	// 用户退出小队或者房主踢用户退出小队
	TickChannelTeamMember(ctx context.Context, in *channel_team.TickChannelTeamMemberReq, opts ...grpc.CallOption) (*channel_team.TickChannelTeamMemberResp, error)
	// 获取小队成员信息
	GetChannelTeamMemberList(ctx context.Context, in *channel_team.GetChannelTeamMemberListReq, opts ...grpc.CallOption) (*channel_team.GetChannelTeamMemberListResp, error)
	// 获取开黑过的用户列表
	GetGangUpHistory(ctx context.Context, in *channel_team.GetGangUpHistoryReq, opts ...grpc.CallOption) (*channel_team.GetGangUpHistoryResp, error)
	// 获取所有的房间用户
	GetAllChannelMember(ctx context.Context, in *channel_team.GetAllChannelMemberReq, opts ...grpc.CallOption) (*channel_team.GetAllChannelMemberResp, error)
	// 设置房间小队昵称
	SetGameNickname(ctx context.Context, in *channel_team.SetGameNicknameReq, opts ...grpc.CallOption) (*channel_team.SetGameNicknameResp, error)
}

type channelTeamLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelTeamLogicClient(cc *grpc.ClientConn) ChannelTeamLogicClient {
	return &channelTeamLogicClient{cc}
}

func (c *channelTeamLogicClient) JoinChannelTeam(ctx context.Context, in *channel_team.JoinChannelTeamReq, opts ...grpc.CallOption) (*channel_team.JoinChannelTeamResp, error) {
	out := new(channel_team.JoinChannelTeamResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/JoinChannelTeam", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) GetChannelTeamApplyList(ctx context.Context, in *channel_team.GetChannelTeamApplyListReq, opts ...grpc.CallOption) (*channel_team.GetChannelTeamApplyListResp, error) {
	out := new(channel_team.GetChannelTeamApplyListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/GetChannelTeamApplyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) AgreeChannelTeamApply(ctx context.Context, in *channel_team.AgreeChannelTeamApplyReq, opts ...grpc.CallOption) (*channel_team.AgreeChannelTeamApplyResp, error) {
	out := new(channel_team.AgreeChannelTeamApplyResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/AgreeChannelTeamApply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) TickChannelTeamMember(ctx context.Context, in *channel_team.TickChannelTeamMemberReq, opts ...grpc.CallOption) (*channel_team.TickChannelTeamMemberResp, error) {
	out := new(channel_team.TickChannelTeamMemberResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/TickChannelTeamMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) GetChannelTeamMemberList(ctx context.Context, in *channel_team.GetChannelTeamMemberListReq, opts ...grpc.CallOption) (*channel_team.GetChannelTeamMemberListResp, error) {
	out := new(channel_team.GetChannelTeamMemberListResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/GetChannelTeamMemberList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) GetGangUpHistory(ctx context.Context, in *channel_team.GetGangUpHistoryReq, opts ...grpc.CallOption) (*channel_team.GetGangUpHistoryResp, error) {
	out := new(channel_team.GetGangUpHistoryResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/GetGangUpHistory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) GetAllChannelMember(ctx context.Context, in *channel_team.GetAllChannelMemberReq, opts ...grpc.CallOption) (*channel_team.GetAllChannelMemberResp, error) {
	out := new(channel_team.GetAllChannelMemberResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/GetAllChannelMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelTeamLogicClient) SetGameNickname(ctx context.Context, in *channel_team.SetGameNicknameReq, opts ...grpc.CallOption) (*channel_team.SetGameNicknameResp, error) {
	out := new(channel_team.SetGameNicknameResp)
	err := c.cc.Invoke(ctx, "/logic.ChannelTeamLogic/SetGameNickname", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelTeamLogicServer is the server API for ChannelTeamLogic service.
type ChannelTeamLogicServer interface {
	// 用户进入小队
	JoinChannelTeam(context.Context, *channel_team.JoinChannelTeamReq) (*channel_team.JoinChannelTeamResp, error)
	// 房主获取申请列表
	GetChannelTeamApplyList(context.Context, *channel_team.GetChannelTeamApplyListReq) (*channel_team.GetChannelTeamApplyListResp, error)
	// 房主同意用户申请入队
	AgreeChannelTeamApply(context.Context, *channel_team.AgreeChannelTeamApplyReq) (*channel_team.AgreeChannelTeamApplyResp, error)
	// 用户退出小队或者房主踢用户退出小队
	TickChannelTeamMember(context.Context, *channel_team.TickChannelTeamMemberReq) (*channel_team.TickChannelTeamMemberResp, error)
	// 获取小队成员信息
	GetChannelTeamMemberList(context.Context, *channel_team.GetChannelTeamMemberListReq) (*channel_team.GetChannelTeamMemberListResp, error)
	// 获取开黑过的用户列表
	GetGangUpHistory(context.Context, *channel_team.GetGangUpHistoryReq) (*channel_team.GetGangUpHistoryResp, error)
	// 获取所有的房间用户
	GetAllChannelMember(context.Context, *channel_team.GetAllChannelMemberReq) (*channel_team.GetAllChannelMemberResp, error)
	// 设置房间小队昵称
	SetGameNickname(context.Context, *channel_team.SetGameNicknameReq) (*channel_team.SetGameNicknameResp, error)
}

func RegisterChannelTeamLogicServer(s *grpc.Server, srv ChannelTeamLogicServer) {
	s.RegisterService(&_ChannelTeamLogic_serviceDesc, srv)
}

func _ChannelTeamLogic_JoinChannelTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.JoinChannelTeamReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).JoinChannelTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/JoinChannelTeam",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).JoinChannelTeam(ctx, req.(*channel_team.JoinChannelTeamReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_GetChannelTeamApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.GetChannelTeamApplyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).GetChannelTeamApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/GetChannelTeamApplyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).GetChannelTeamApplyList(ctx, req.(*channel_team.GetChannelTeamApplyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_AgreeChannelTeamApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.AgreeChannelTeamApplyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).AgreeChannelTeamApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/AgreeChannelTeamApply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).AgreeChannelTeamApply(ctx, req.(*channel_team.AgreeChannelTeamApplyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_TickChannelTeamMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.TickChannelTeamMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).TickChannelTeamMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/TickChannelTeamMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).TickChannelTeamMember(ctx, req.(*channel_team.TickChannelTeamMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_GetChannelTeamMemberList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.GetChannelTeamMemberListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).GetChannelTeamMemberList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/GetChannelTeamMemberList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).GetChannelTeamMemberList(ctx, req.(*channel_team.GetChannelTeamMemberListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_GetGangUpHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.GetGangUpHistoryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).GetGangUpHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/GetGangUpHistory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).GetGangUpHistory(ctx, req.(*channel_team.GetGangUpHistoryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_GetAllChannelMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.GetAllChannelMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).GetAllChannelMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/GetAllChannelMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).GetAllChannelMember(ctx, req.(*channel_team.GetAllChannelMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelTeamLogic_SetGameNickname_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_team.SetGameNicknameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelTeamLogicServer).SetGameNickname(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.ChannelTeamLogic/SetGameNickname",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelTeamLogicServer).SetGameNickname(ctx, req.(*channel_team.SetGameNicknameReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelTeamLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.ChannelTeamLogic",
	HandlerType: (*ChannelTeamLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "JoinChannelTeam",
			Handler:    _ChannelTeamLogic_JoinChannelTeam_Handler,
		},
		{
			MethodName: "GetChannelTeamApplyList",
			Handler:    _ChannelTeamLogic_GetChannelTeamApplyList_Handler,
		},
		{
			MethodName: "AgreeChannelTeamApply",
			Handler:    _ChannelTeamLogic_AgreeChannelTeamApply_Handler,
		},
		{
			MethodName: "TickChannelTeamMember",
			Handler:    _ChannelTeamLogic_TickChannelTeamMember_Handler,
		},
		{
			MethodName: "GetChannelTeamMemberList",
			Handler:    _ChannelTeamLogic_GetChannelTeamMemberList_Handler,
		},
		{
			MethodName: "GetGangUpHistory",
			Handler:    _ChannelTeamLogic_GetGangUpHistory_Handler,
		},
		{
			MethodName: "GetAllChannelMember",
			Handler:    _ChannelTeamLogic_GetAllChannelMember_Handler,
		},
		{
			MethodName: "SetGameNickname",
			Handler:    _ChannelTeamLogic_SetGameNickname_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-team-logic/channel-team-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-team-logic/channel-team-logic.proto", fileDescriptor_channel_team_logic_69d2d40cadac0167)
}

var fileDescriptor_channel_team_logic_69d2d40cadac0167 = []byte{
	// 420 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xbd, 0xae, 0xd3, 0x30,
	0x18, 0x86, 0x65, 0x89, 0x5f, 0x2f, 0x1c, 0x19, 0x10, 0x10, 0x89, 0x05, 0x0e, 0x82, 0x03, 0xc4,
	0x91, 0x0e, 0x42, 0xac, 0xb4, 0x0c, 0x41, 0xa8, 0x30, 0x40, 0x59, 0x58, 0x2a, 0x37, 0xfa, 0x64,
	0x4c, 0x93, 0xd8, 0xc4, 0x56, 0x51, 0x26, 0x24, 0xae, 0x81, 0x7f, 0xae, 0xa4, 0x57, 0xd1, 0x1b,
	0xe0, 0x16, 0xb2, 0x30, 0x32, 0x21, 0x27, 0x45, 0x4e, 0x13, 0x97, 0x86, 0xa9, 0xaa, 0xf3, 0xf8,
	0x7d, 0x5e, 0x7d, 0xd2, 0x67, 0xfc, 0x20, 0x95, 0x5c, 0x24, 0x7a, 0x59, 0x84, 0x5c, 0x46, 0xc9,
	0x6b, 0x96, 0xe7, 0x90, 0x86, 0x06, 0x58, 0x16, 0xd6, 0x1f, 0x3c, 0x47, 0x54, 0x15, 0xd2, 0x48,
	0x72, 0xb2, 0xfe, 0x13, 0x5c, 0x65, 0x4a, 0x79, 0xa0, 0x59, 0x43, 0x05, 0x47, 0xed, 0x78, 0xce,
	0x0c, 0xbc, 0x63, 0x65, 0x24, 0x95, 0x11, 0x32, 0xd7, 0x7f, 0x7f, 0x1b, 0xf4, 0xf8, 0xe7, 0x69,
	0x7c, 0xf0, 0xa8, 0x09, 0x9a, 0x02, 0xcb, 0x26, 0xf6, 0x22, 0xe1, 0xf8, 0xdc, 0x13, 0x29, 0xf2,
	0xd6, 0x39, 0xb9, 0x4e, 0x39, 0xa3, 0x1b, 0xe3, 0xcc, 0x1a, 0x69, 0x87, 0x78, 0x0e, 0x6f, 0x83,
	0xc3, 0xfd, 0x90, 0x56, 0xd7, 0xce, 0xfe, 0x5a, 0xaf, 0xe8, 0x89, 0x33, 0x1f, 0x2b, 0x44, 0xde,
	0xe3, 0x4b, 0x31, 0x98, 0x16, 0x30, 0x52, 0x2a, 0x2d, 0x27, 0x42, 0x1b, 0x72, 0xa7, 0x97, 0xb5,
	0x83, 0xb4, 0xe2, 0xbb, 0xc3, 0x61, 0x57, 0xe0, 0x53, 0x85, 0x48, 0x89, 0x2f, 0x8e, 0x78, 0x01,
	0xd0, 0x65, 0xc9, 0x51, 0x2f, 0xd1, 0xcb, 0x59, 0xf9, 0xed, 0xa1, 0xa8, 0x53, 0x7f, 0x6e, 0xd4,
	0x53, 0x91, 0x2c, 0x5a, 0xd8, 0x53, 0xc8, 0xe6, 0x50, 0x78, 0xd4, 0x5e, 0xce, 0xaf, 0xde, 0x81,
	0x3a, 0xf5, 0x97, 0x0a, 0x91, 0x0f, 0x08, 0x5f, 0xde, 0x1e, 0x50, 0xc3, 0xd5, 0x83, 0xdf, 0x37,
	0x4b, 0x87, 0xda, 0x06, 0xe1, 0x7f, 0xd0, 0xae, 0xc4, 0xd7, 0x0a, 0x91, 0x37, 0xf8, 0x20, 0x06,
	0x13, 0xb3, 0x9c, 0xbf, 0x54, 0x8f, 0x85, 0x36, 0xb2, 0x28, 0xc9, 0xa1, 0x2f, 0x6d, 0x0b, 0xb1,
	0xce, 0x1b, 0x03, 0x28, 0xe7, 0xfa, 0x56, 0x21, 0xa2, 0xf1, 0xf9, 0x18, 0xcc, 0x28, 0x4d, 0x37,
	0xcd, 0x36, 0x93, 0xbe, 0xe9, 0x0b, 0xea, 0x52, 0xd6, 0x78, 0x6b, 0x18, 0xe8, 0xa4, 0xdf, 0x2b,
	0x64, 0xb7, 0xe8, 0x85, 0xed, 0x95, 0xc1, 0x33, 0x91, 0x2c, 0x72, 0x96, 0x81, 0x67, 0x8b, 0x3a,
	0x84, 0x7f, 0x8b, 0x7a, 0x90, 0x13, 0xfd, 0xa8, 0x50, 0x70, 0xe5, 0xf7, 0x7a, 0x45, 0x2f, 0x60,
	0xd2, 0x7f, 0x10, 0xc6, 0xe3, 0x57, 0x0f, 0xb9, 0x4c, 0x59, 0xce, 0xe9, 0xfd, 0x63, 0x63, 0x68,
	0x22, 0xb3, 0xa8, 0xde, 0xfb, 0x44, 0xa6, 0x91, 0x86, 0x62, 0x29, 0x12, 0xd0, 0xd1, 0xbf, 0x1f,
	0xa3, 0xf9, 0xa9, 0xfa, 0xc6, 0xbd, 0x3f, 0x01, 0x00, 0x00, 0xff, 0xff, 0xec, 0xbb, 0xe0, 0xc6,
	0xb5, 0x04, 0x00, 0x00,
}
