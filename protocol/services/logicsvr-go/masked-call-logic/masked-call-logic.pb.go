// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/masked-call-logic/masked-call-logic.proto

package masked_call_logic // import "golang.52tt.com/protocol/services/logicsvr-go/masked-call-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import masked_call "golang.52tt.com/protocol/app/masked-call"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MaskedCallClient is the client API for MaskedCall service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MaskedCallClient interface {
	// 查询匹配信息
	QueryMatchInfo(ctx context.Context, in *masked_call.QueryMatchInfoReq, opts ...grpc.CallOption) (*masked_call.QueryMatchInfoResp, error)
	// 开始匹配
	StartMatch(ctx context.Context, in *masked_call.StartMatchReq, opts ...grpc.CallOption) (*masked_call.StartMatchResp, error)
	// 取消匹配
	CancelMatch(ctx context.Context, in *masked_call.CancelMatchReq, opts ...grpc.CallOption) (*masked_call.CancelMatchResp, error)
	// --------- 通话控制 ----------
	// 获取通话信息
	QueryCallInfo(ctx context.Context, in *masked_call.QueryCallInfoReq, opts ...grpc.CallOption) (*masked_call.QueryCallInfoResp, error)
	// 设置连接状态
	SetConnectStatus(ctx context.Context, in *masked_call.SetConnectStatusReq, opts ...grpc.CallOption) (*masked_call.SetConnectStatusResp, error)
	// 公开身份
	Unmask(ctx context.Context, in *masked_call.UnmaskReq, opts ...grpc.CallOption) (*masked_call.UnmaskResp, error)
	// 邀请公开身份
	InviteUnmask(ctx context.Context, in *masked_call.InviteUnmaskReq, opts ...grpc.CallOption) (*masked_call.InviteUnmaskResp, error)
	// 评价
	Comment(ctx context.Context, in *masked_call.CommentReq, opts ...grpc.CallOption) (*masked_call.CommentResp, error)
	// roll点
	Roll(ctx context.Context, in *masked_call.RollReq, opts ...grpc.CallOption) (*masked_call.RollResp, error)
	ReportAudio(ctx context.Context, in *masked_call.ReportAudioReq, opts ...grpc.CallOption) (*masked_call.ReportAudioResp, error)
	GetAudioUploadToken(ctx context.Context, in *masked_call.GetAudioUploadTokenReq, opts ...grpc.CallOption) (*masked_call.GetAudioUploadTokenResp, error)
}

type maskedCallClient struct {
	cc *grpc.ClientConn
}

func NewMaskedCallClient(cc *grpc.ClientConn) MaskedCallClient {
	return &maskedCallClient{cc}
}

func (c *maskedCallClient) QueryMatchInfo(ctx context.Context, in *masked_call.QueryMatchInfoReq, opts ...grpc.CallOption) (*masked_call.QueryMatchInfoResp, error) {
	out := new(masked_call.QueryMatchInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/QueryMatchInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) StartMatch(ctx context.Context, in *masked_call.StartMatchReq, opts ...grpc.CallOption) (*masked_call.StartMatchResp, error) {
	out := new(masked_call.StartMatchResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/StartMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) CancelMatch(ctx context.Context, in *masked_call.CancelMatchReq, opts ...grpc.CallOption) (*masked_call.CancelMatchResp, error) {
	out := new(masked_call.CancelMatchResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/CancelMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) QueryCallInfo(ctx context.Context, in *masked_call.QueryCallInfoReq, opts ...grpc.CallOption) (*masked_call.QueryCallInfoResp, error) {
	out := new(masked_call.QueryCallInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/QueryCallInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) SetConnectStatus(ctx context.Context, in *masked_call.SetConnectStatusReq, opts ...grpc.CallOption) (*masked_call.SetConnectStatusResp, error) {
	out := new(masked_call.SetConnectStatusResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/SetConnectStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) Unmask(ctx context.Context, in *masked_call.UnmaskReq, opts ...grpc.CallOption) (*masked_call.UnmaskResp, error) {
	out := new(masked_call.UnmaskResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/Unmask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) InviteUnmask(ctx context.Context, in *masked_call.InviteUnmaskReq, opts ...grpc.CallOption) (*masked_call.InviteUnmaskResp, error) {
	out := new(masked_call.InviteUnmaskResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/InviteUnmask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) Comment(ctx context.Context, in *masked_call.CommentReq, opts ...grpc.CallOption) (*masked_call.CommentResp, error) {
	out := new(masked_call.CommentResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/Comment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) Roll(ctx context.Context, in *masked_call.RollReq, opts ...grpc.CallOption) (*masked_call.RollResp, error) {
	out := new(masked_call.RollResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/Roll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) ReportAudio(ctx context.Context, in *masked_call.ReportAudioReq, opts ...grpc.CallOption) (*masked_call.ReportAudioResp, error) {
	out := new(masked_call.ReportAudioResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/ReportAudio", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *maskedCallClient) GetAudioUploadToken(ctx context.Context, in *masked_call.GetAudioUploadTokenReq, opts ...grpc.CallOption) (*masked_call.GetAudioUploadTokenResp, error) {
	out := new(masked_call.GetAudioUploadTokenResp)
	err := c.cc.Invoke(ctx, "/logic.MaskedCall/GetAudioUploadToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MaskedCallServer is the server API for MaskedCall service.
type MaskedCallServer interface {
	// 查询匹配信息
	QueryMatchInfo(context.Context, *masked_call.QueryMatchInfoReq) (*masked_call.QueryMatchInfoResp, error)
	// 开始匹配
	StartMatch(context.Context, *masked_call.StartMatchReq) (*masked_call.StartMatchResp, error)
	// 取消匹配
	CancelMatch(context.Context, *masked_call.CancelMatchReq) (*masked_call.CancelMatchResp, error)
	// --------- 通话控制 ----------
	// 获取通话信息
	QueryCallInfo(context.Context, *masked_call.QueryCallInfoReq) (*masked_call.QueryCallInfoResp, error)
	// 设置连接状态
	SetConnectStatus(context.Context, *masked_call.SetConnectStatusReq) (*masked_call.SetConnectStatusResp, error)
	// 公开身份
	Unmask(context.Context, *masked_call.UnmaskReq) (*masked_call.UnmaskResp, error)
	// 邀请公开身份
	InviteUnmask(context.Context, *masked_call.InviteUnmaskReq) (*masked_call.InviteUnmaskResp, error)
	// 评价
	Comment(context.Context, *masked_call.CommentReq) (*masked_call.CommentResp, error)
	// roll点
	Roll(context.Context, *masked_call.RollReq) (*masked_call.RollResp, error)
	ReportAudio(context.Context, *masked_call.ReportAudioReq) (*masked_call.ReportAudioResp, error)
	GetAudioUploadToken(context.Context, *masked_call.GetAudioUploadTokenReq) (*masked_call.GetAudioUploadTokenResp, error)
}

func RegisterMaskedCallServer(s *grpc.Server, srv MaskedCallServer) {
	s.RegisterService(&_MaskedCall_serviceDesc, srv)
}

func _MaskedCall_QueryMatchInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.QueryMatchInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).QueryMatchInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/QueryMatchInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).QueryMatchInfo(ctx, req.(*masked_call.QueryMatchInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_StartMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.StartMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).StartMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/StartMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).StartMatch(ctx, req.(*masked_call.StartMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_CancelMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.CancelMatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).CancelMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/CancelMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).CancelMatch(ctx, req.(*masked_call.CancelMatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_QueryCallInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.QueryCallInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).QueryCallInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/QueryCallInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).QueryCallInfo(ctx, req.(*masked_call.QueryCallInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_SetConnectStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.SetConnectStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).SetConnectStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/SetConnectStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).SetConnectStatus(ctx, req.(*masked_call.SetConnectStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_Unmask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.UnmaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).Unmask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/Unmask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).Unmask(ctx, req.(*masked_call.UnmaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_InviteUnmask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.InviteUnmaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).InviteUnmask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/InviteUnmask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).InviteUnmask(ctx, req.(*masked_call.InviteUnmaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_Comment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.CommentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).Comment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/Comment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).Comment(ctx, req.(*masked_call.CommentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_Roll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.RollReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).Roll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/Roll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).Roll(ctx, req.(*masked_call.RollReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_ReportAudio_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.ReportAudioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).ReportAudio(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/ReportAudio",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).ReportAudio(ctx, req.(*masked_call.ReportAudioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MaskedCall_GetAudioUploadToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(masked_call.GetAudioUploadTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MaskedCallServer).GetAudioUploadToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MaskedCall/GetAudioUploadToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MaskedCallServer).GetAudioUploadToken(ctx, req.(*masked_call.GetAudioUploadTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MaskedCall_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.MaskedCall",
	HandlerType: (*MaskedCallServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QueryMatchInfo",
			Handler:    _MaskedCall_QueryMatchInfo_Handler,
		},
		{
			MethodName: "StartMatch",
			Handler:    _MaskedCall_StartMatch_Handler,
		},
		{
			MethodName: "CancelMatch",
			Handler:    _MaskedCall_CancelMatch_Handler,
		},
		{
			MethodName: "QueryCallInfo",
			Handler:    _MaskedCall_QueryCallInfo_Handler,
		},
		{
			MethodName: "SetConnectStatus",
			Handler:    _MaskedCall_SetConnectStatus_Handler,
		},
		{
			MethodName: "Unmask",
			Handler:    _MaskedCall_Unmask_Handler,
		},
		{
			MethodName: "InviteUnmask",
			Handler:    _MaskedCall_InviteUnmask_Handler,
		},
		{
			MethodName: "Comment",
			Handler:    _MaskedCall_Comment_Handler,
		},
		{
			MethodName: "Roll",
			Handler:    _MaskedCall_Roll_Handler,
		},
		{
			MethodName: "ReportAudio",
			Handler:    _MaskedCall_ReportAudio_Handler,
		},
		{
			MethodName: "GetAudioUploadToken",
			Handler:    _MaskedCall_GetAudioUploadToken_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/masked-call-logic/masked-call-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/masked-call-logic/masked-call-logic.proto", fileDescriptor_masked_call_logic_e6ab1ab7cca0b4a5)
}

var fileDescriptor_masked_call_logic_e6ab1ab7cca0b4a5 = []byte{
	// 478 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x94, 0xdd, 0x6a, 0x14, 0x31,
	0x18, 0x86, 0x09, 0xd4, 0x5a, 0xe3, 0x0f, 0xfa, 0x15, 0xb4, 0x8e, 0xa8, 0x6d, 0x15, 0xc5, 0x83,
	0xcd, 0x40, 0xa5, 0xc7, 0x52, 0x57, 0x90, 0x0a, 0x7b, 0xe0, 0xae, 0x15, 0x14, 0x61, 0x49, 0xb3,
	0x71, 0x3a, 0x34, 0x9b, 0x6f, 0x3a, 0x93, 0x5d, 0xe9, 0x85, 0x78, 0x31, 0xbd, 0x8a, 0xde, 0x84,
	0xff, 0x51, 0x0f, 0x3c, 0xf4, 0x48, 0x92, 0x69, 0xc9, 0x74, 0x66, 0x7f, 0x3c, 0x5a, 0xf6, 0x7b,
	0xde, 0xf7, 0xc9, 0x24, 0x84, 0xd0, 0x4d, 0x85, 0x49, 0x2a, 0x8a, 0x71, 0xde, 0x4a, 0x30, 0x1e,
	0xf2, 0x62, 0x5f, 0x0e, 0x5a, 0x82, 0x2b, 0xd5, 0xf2, 0xf3, 0xe6, 0x84, 0x65, 0x39, 0x1a, 0x84,
	0x73, 0xfe, 0x4f, 0xf4, 0xa8, 0xda, 0x4e, 0xb8, 0x91, 0x1f, 0xf8, 0x61, 0x8c, 0x99, 0x49, 0x51,
	0x17, 0xa7, 0xbf, 0x65, 0x23, 0xba, 0xce, 0xb3, 0xac, 0xaa, 0xeb, 0x97, 0xf3, 0x8d, 0x8f, 0x4b,
	0x94, 0x76, 0xfc, 0xb8, 0xcd, 0x95, 0x82, 0x5d, 0x7a, 0xe5, 0xe5, 0x48, 0xe6, 0x87, 0x1d, 0x6e,
	0xc4, 0xde, 0xb6, 0x7e, 0x8f, 0xb0, 0xc6, 0x12, 0xce, 0xca, 0x62, 0xdf, 0x15, 0xd9, 0x59, 0xde,
	0x95, 0x07, 0xd1, 0xfa, 0xbc, 0x48, 0x91, 0xad, 0x5f, 0xf8, 0x73, 0x7c, 0xc4, 0x16, 0x96, 0x3e,
	0x59, 0x02, 0xaf, 0x29, 0xed, 0x19, 0x9e, 0x1b, 0x1f, 0x80, 0xdb, 0xf5, 0x72, 0x60, 0xce, 0x7d,
	0x67, 0x16, 0x0e, 0xde, 0xcf, 0x96, 0xc0, 0x1b, 0x7a, 0xb1, 0xcd, 0xb5, 0x90, 0xaa, 0x14, 0x37,
	0x9a, 0x15, 0xe8, 0xcc, 0x77, 0x67, 0xf2, 0xa0, 0xfe, 0x62, 0x09, 0xf4, 0xe9, 0x65, 0xbf, 0x27,
	0x77, 0x46, 0xfe, 0x54, 0x56, 0x27, 0x6e, 0xf9, 0x14, 0x3b, 0xfd, 0xda, 0x9c, 0x44, 0x58, 0xe0,
	0xab, 0x25, 0xb0, 0x47, 0xaf, 0xf6, 0xa4, 0x69, 0xa3, 0xd6, 0x52, 0x98, 0x9e, 0xe1, 0x66, 0x54,
	0xc0, 0xbd, 0xc6, 0xd6, 0x6b, 0x09, 0xb7, 0xcc, 0xfd, 0xf9, 0xa1, 0xb0, 0xd2, 0x37, 0x4b, 0xe0,
	0x05, 0x5d, 0xdc, 0xd1, 0xae, 0x00, 0x37, 0xeb, 0xd5, 0x72, 0xee, 0xac, 0xd1, 0x34, 0x14, 0x5c,
	0xdf, 0x2d, 0x81, 0x77, 0xf4, 0xd2, 0xb6, 0x1e, 0xa7, 0x46, 0x9e, 0x18, 0x1b, 0x47, 0x5a, 0xa5,
	0xce, 0xbb, 0x3a, 0x3b, 0x10, 0xec, 0x3f, 0x2c, 0x81, 0x0e, 0x3d, 0xdf, 0xc6, 0xe1, 0x50, 0x6a,
	0x03, 0x8d, 0xef, 0x39, 0x01, 0xce, 0x79, 0x6b, 0x2a, 0x0b, 0x3a, 0x6b, 0x09, 0x3c, 0xa3, 0x0b,
	0x5d, 0x54, 0x0a, 0x6e, 0xd4, 0xf3, 0x6e, 0xea, 0x44, 0x2b, 0x93, 0x41, 0xb0, 0xfc, 0x2c, 0x2f,
	0x59, 0x57, 0x66, 0x98, 0x9b, 0xad, 0xd1, 0x20, 0xc5, 0xe6, 0x25, 0xab, 0xc0, 0x89, 0x97, 0xec,
	0x0c, 0x0f, 0xea, 0x5f, 0x96, 0xc0, 0x01, 0x5d, 0x7e, 0x2e, 0x4b, 0xb4, 0x93, 0x29, 0xe4, 0x83,
	0x57, 0xb8, 0x2f, 0x35, 0x3c, 0xa8, 0x2b, 0x26, 0x84, 0xdc, 0x52, 0x0f, 0xff, 0x2b, 0x17, 0x96,
	0xfc, 0x6d, 0x49, 0xb4, 0xf2, 0xf7, 0xf8, 0x88, 0x2d, 0xd3, 0x6b, 0x8d, 0x77, 0xe6, 0xe9, 0xd6,
	0xdb, 0x27, 0x09, 0x2a, 0xae, 0x13, 0xb6, 0xb9, 0x61, 0x0c, 0x13, 0x38, 0x8c, 0xfd, 0x83, 0x21,
	0x50, 0xc5, 0x85, 0xcc, 0xc7, 0xa9, 0x90, 0x45, 0x3c, 0xf3, 0xf1, 0xda, 0x5d, 0xf4, 0x85, 0xc7,
	0xff, 0x02, 0x00, 0x00, 0xff, 0xff, 0x14, 0xf6, 0x0d, 0x22, 0xe4, 0x04, 0x00, 0x00,
}
