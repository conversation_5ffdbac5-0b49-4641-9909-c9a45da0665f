// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/perfect-couple-match-logic/perfect-couple-match-logic.proto

package perfect_couple_match_logic // import "golang.52tt.com/protocol/services/logicsvr-go/perfect-couple-match-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import perfect_couple_match_logic "golang.52tt.com/protocol/app/perfect-couple-match-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// PerfectCoupleMatchLogicClient is the client API for PerfectCoupleMatchLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type PerfectCoupleMatchLogicClient interface {
	// 获取当前游戏信息
	GetPrefectCpGameInfo(ctx context.Context, in *perfect_couple_match_logic.GetPrefectCpGameInfoRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetPrefectCpGameInfoResponse, error)
	// 设置游戏阶段
	SetPrefectCpGamePhase(ctx context.Context, in *perfect_couple_match_logic.SetPrefectCpGamePhaseRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.SetPrefectCpGamePhaseResponse, error)
	// 申请上玩家麦
	ApplyToHoldMic(ctx context.Context, in *perfect_couple_match_logic.ApplyToHoldMicRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.ApplyToHoldMicResponse, error)
	// 为指定玩家爆灯
	BlowLight(ctx context.Context, in *perfect_couple_match_logic.BlowLightRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.BlowLightResponse, error)
	// 选择心动对象
	ChooseTheOne(ctx context.Context, in *perfect_couple_match_logic.ChooseTheOneRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.ChooseTheOneResponse, error)
	// 查询已有线索
	GetCoupleClues(ctx context.Context, in *perfect_couple_match_logic.GetCoupleCluesRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetCoupleCluesResponse, error)
	// 查询我的问卷
	GetMyQuestionnaire(ctx context.Context, in *perfect_couple_match_logic.GetMyQuestionnaireRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetMyQuestionnaireResponse, error)
	// 查询我的道具
	GetMyCluesProp(ctx context.Context, in *perfect_couple_match_logic.GetMyCluesPropRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetMyCluesPropResponse, error)
	// 使用道具获得线索
	UseCluesProp(ctx context.Context, in *perfect_couple_match_logic.UseCluesPropRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.UseCluesPropResponse, error)
	// 主持人发放线索
	PublishClues(ctx context.Context, in *perfect_couple_match_logic.PublishCluesRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.PublishCluesResponse, error)
	// ============================== 匹配阶段 =============================
	// 获取天配匹配入口
	GetPerfectMatchEntry(ctx context.Context, in *perfect_couple_match_logic.GetPerfectMatchEntryRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetPerfectMatchEntryResponse, error)
	// 匹配报名
	EnrollPerfectMatch(ctx context.Context, in *perfect_couple_match_logic.EnrollPerfectMatchRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.EnrollPerfectMatchResponse, error)
	// 获取题目
	GetPerfectMatchQuestions(ctx context.Context, in *perfect_couple_match_logic.GetPerfectMatchQuestionsRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetPerfectMatchQuestionsResponse, error)
	// 匹配心跳
	SendPerfectMatchHeartbeat(ctx context.Context, in *perfect_couple_match_logic.SendPerfectMatchHeartbeatRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.SendPerfectMatchHeartbeatResponse, error)
	// 发送答案
	SendPerfectMatchAnswer(ctx context.Context, in *perfect_couple_match_logic.SendPerfectMatchAnswerRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.SendPerfectMatchAnswerResponse, error)
	// 取消匹配
	CancelPerfectMatch(ctx context.Context, in *perfect_couple_match_logic.CancelPerfectMatchRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.CancelPerfectMatchResponse, error)
}

type perfectCoupleMatchLogicClient struct {
	cc *grpc.ClientConn
}

func NewPerfectCoupleMatchLogicClient(cc *grpc.ClientConn) PerfectCoupleMatchLogicClient {
	return &perfectCoupleMatchLogicClient{cc}
}

func (c *perfectCoupleMatchLogicClient) GetPrefectCpGameInfo(ctx context.Context, in *perfect_couple_match_logic.GetPrefectCpGameInfoRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetPrefectCpGameInfoResponse, error) {
	out := new(perfect_couple_match_logic.GetPrefectCpGameInfoResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/GetPrefectCpGameInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) SetPrefectCpGamePhase(ctx context.Context, in *perfect_couple_match_logic.SetPrefectCpGamePhaseRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.SetPrefectCpGamePhaseResponse, error) {
	out := new(perfect_couple_match_logic.SetPrefectCpGamePhaseResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/SetPrefectCpGamePhase", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) ApplyToHoldMic(ctx context.Context, in *perfect_couple_match_logic.ApplyToHoldMicRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.ApplyToHoldMicResponse, error) {
	out := new(perfect_couple_match_logic.ApplyToHoldMicResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/ApplyToHoldMic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) BlowLight(ctx context.Context, in *perfect_couple_match_logic.BlowLightRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.BlowLightResponse, error) {
	out := new(perfect_couple_match_logic.BlowLightResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/BlowLight", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) ChooseTheOne(ctx context.Context, in *perfect_couple_match_logic.ChooseTheOneRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.ChooseTheOneResponse, error) {
	out := new(perfect_couple_match_logic.ChooseTheOneResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/ChooseTheOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) GetCoupleClues(ctx context.Context, in *perfect_couple_match_logic.GetCoupleCluesRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetCoupleCluesResponse, error) {
	out := new(perfect_couple_match_logic.GetCoupleCluesResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/GetCoupleClues", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) GetMyQuestionnaire(ctx context.Context, in *perfect_couple_match_logic.GetMyQuestionnaireRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetMyQuestionnaireResponse, error) {
	out := new(perfect_couple_match_logic.GetMyQuestionnaireResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/GetMyQuestionnaire", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) GetMyCluesProp(ctx context.Context, in *perfect_couple_match_logic.GetMyCluesPropRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetMyCluesPropResponse, error) {
	out := new(perfect_couple_match_logic.GetMyCluesPropResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/GetMyCluesProp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) UseCluesProp(ctx context.Context, in *perfect_couple_match_logic.UseCluesPropRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.UseCluesPropResponse, error) {
	out := new(perfect_couple_match_logic.UseCluesPropResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/UseCluesProp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) PublishClues(ctx context.Context, in *perfect_couple_match_logic.PublishCluesRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.PublishCluesResponse, error) {
	out := new(perfect_couple_match_logic.PublishCluesResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/PublishClues", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) GetPerfectMatchEntry(ctx context.Context, in *perfect_couple_match_logic.GetPerfectMatchEntryRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetPerfectMatchEntryResponse, error) {
	out := new(perfect_couple_match_logic.GetPerfectMatchEntryResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/GetPerfectMatchEntry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) EnrollPerfectMatch(ctx context.Context, in *perfect_couple_match_logic.EnrollPerfectMatchRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.EnrollPerfectMatchResponse, error) {
	out := new(perfect_couple_match_logic.EnrollPerfectMatchResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/EnrollPerfectMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) GetPerfectMatchQuestions(ctx context.Context, in *perfect_couple_match_logic.GetPerfectMatchQuestionsRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.GetPerfectMatchQuestionsResponse, error) {
	out := new(perfect_couple_match_logic.GetPerfectMatchQuestionsResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/GetPerfectMatchQuestions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) SendPerfectMatchHeartbeat(ctx context.Context, in *perfect_couple_match_logic.SendPerfectMatchHeartbeatRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.SendPerfectMatchHeartbeatResponse, error) {
	out := new(perfect_couple_match_logic.SendPerfectMatchHeartbeatResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/SendPerfectMatchHeartbeat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) SendPerfectMatchAnswer(ctx context.Context, in *perfect_couple_match_logic.SendPerfectMatchAnswerRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.SendPerfectMatchAnswerResponse, error) {
	out := new(perfect_couple_match_logic.SendPerfectMatchAnswerResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/SendPerfectMatchAnswer", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *perfectCoupleMatchLogicClient) CancelPerfectMatch(ctx context.Context, in *perfect_couple_match_logic.CancelPerfectMatchRequest, opts ...grpc.CallOption) (*perfect_couple_match_logic.CancelPerfectMatchResponse, error) {
	out := new(perfect_couple_match_logic.CancelPerfectMatchResponse)
	err := c.cc.Invoke(ctx, "/logic.PerfectCoupleMatchLogic/CancelPerfectMatch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PerfectCoupleMatchLogicServer is the server API for PerfectCoupleMatchLogic service.
type PerfectCoupleMatchLogicServer interface {
	// 获取当前游戏信息
	GetPrefectCpGameInfo(context.Context, *perfect_couple_match_logic.GetPrefectCpGameInfoRequest) (*perfect_couple_match_logic.GetPrefectCpGameInfoResponse, error)
	// 设置游戏阶段
	SetPrefectCpGamePhase(context.Context, *perfect_couple_match_logic.SetPrefectCpGamePhaseRequest) (*perfect_couple_match_logic.SetPrefectCpGamePhaseResponse, error)
	// 申请上玩家麦
	ApplyToHoldMic(context.Context, *perfect_couple_match_logic.ApplyToHoldMicRequest) (*perfect_couple_match_logic.ApplyToHoldMicResponse, error)
	// 为指定玩家爆灯
	BlowLight(context.Context, *perfect_couple_match_logic.BlowLightRequest) (*perfect_couple_match_logic.BlowLightResponse, error)
	// 选择心动对象
	ChooseTheOne(context.Context, *perfect_couple_match_logic.ChooseTheOneRequest) (*perfect_couple_match_logic.ChooseTheOneResponse, error)
	// 查询已有线索
	GetCoupleClues(context.Context, *perfect_couple_match_logic.GetCoupleCluesRequest) (*perfect_couple_match_logic.GetCoupleCluesResponse, error)
	// 查询我的问卷
	GetMyQuestionnaire(context.Context, *perfect_couple_match_logic.GetMyQuestionnaireRequest) (*perfect_couple_match_logic.GetMyQuestionnaireResponse, error)
	// 查询我的道具
	GetMyCluesProp(context.Context, *perfect_couple_match_logic.GetMyCluesPropRequest) (*perfect_couple_match_logic.GetMyCluesPropResponse, error)
	// 使用道具获得线索
	UseCluesProp(context.Context, *perfect_couple_match_logic.UseCluesPropRequest) (*perfect_couple_match_logic.UseCluesPropResponse, error)
	// 主持人发放线索
	PublishClues(context.Context, *perfect_couple_match_logic.PublishCluesRequest) (*perfect_couple_match_logic.PublishCluesResponse, error)
	// ============================== 匹配阶段 =============================
	// 获取天配匹配入口
	GetPerfectMatchEntry(context.Context, *perfect_couple_match_logic.GetPerfectMatchEntryRequest) (*perfect_couple_match_logic.GetPerfectMatchEntryResponse, error)
	// 匹配报名
	EnrollPerfectMatch(context.Context, *perfect_couple_match_logic.EnrollPerfectMatchRequest) (*perfect_couple_match_logic.EnrollPerfectMatchResponse, error)
	// 获取题目
	GetPerfectMatchQuestions(context.Context, *perfect_couple_match_logic.GetPerfectMatchQuestionsRequest) (*perfect_couple_match_logic.GetPerfectMatchQuestionsResponse, error)
	// 匹配心跳
	SendPerfectMatchHeartbeat(context.Context, *perfect_couple_match_logic.SendPerfectMatchHeartbeatRequest) (*perfect_couple_match_logic.SendPerfectMatchHeartbeatResponse, error)
	// 发送答案
	SendPerfectMatchAnswer(context.Context, *perfect_couple_match_logic.SendPerfectMatchAnswerRequest) (*perfect_couple_match_logic.SendPerfectMatchAnswerResponse, error)
	// 取消匹配
	CancelPerfectMatch(context.Context, *perfect_couple_match_logic.CancelPerfectMatchRequest) (*perfect_couple_match_logic.CancelPerfectMatchResponse, error)
}

func RegisterPerfectCoupleMatchLogicServer(s *grpc.Server, srv PerfectCoupleMatchLogicServer) {
	s.RegisterService(&_PerfectCoupleMatchLogic_serviceDesc, srv)
}

func _PerfectCoupleMatchLogic_GetPrefectCpGameInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.GetPrefectCpGameInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).GetPrefectCpGameInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/GetPrefectCpGameInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).GetPrefectCpGameInfo(ctx, req.(*perfect_couple_match_logic.GetPrefectCpGameInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_SetPrefectCpGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.SetPrefectCpGamePhaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).SetPrefectCpGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/SetPrefectCpGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).SetPrefectCpGamePhase(ctx, req.(*perfect_couple_match_logic.SetPrefectCpGamePhaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_ApplyToHoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.ApplyToHoldMicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).ApplyToHoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/ApplyToHoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).ApplyToHoldMic(ctx, req.(*perfect_couple_match_logic.ApplyToHoldMicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_BlowLight_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.BlowLightRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).BlowLight(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/BlowLight",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).BlowLight(ctx, req.(*perfect_couple_match_logic.BlowLightRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_ChooseTheOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.ChooseTheOneRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).ChooseTheOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/ChooseTheOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).ChooseTheOne(ctx, req.(*perfect_couple_match_logic.ChooseTheOneRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_GetCoupleClues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.GetCoupleCluesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).GetCoupleClues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/GetCoupleClues",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).GetCoupleClues(ctx, req.(*perfect_couple_match_logic.GetCoupleCluesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_GetMyQuestionnaire_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.GetMyQuestionnaireRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).GetMyQuestionnaire(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/GetMyQuestionnaire",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).GetMyQuestionnaire(ctx, req.(*perfect_couple_match_logic.GetMyQuestionnaireRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_GetMyCluesProp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.GetMyCluesPropRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).GetMyCluesProp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/GetMyCluesProp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).GetMyCluesProp(ctx, req.(*perfect_couple_match_logic.GetMyCluesPropRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_UseCluesProp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.UseCluesPropRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).UseCluesProp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/UseCluesProp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).UseCluesProp(ctx, req.(*perfect_couple_match_logic.UseCluesPropRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_PublishClues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.PublishCluesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).PublishClues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/PublishClues",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).PublishClues(ctx, req.(*perfect_couple_match_logic.PublishCluesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_GetPerfectMatchEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.GetPerfectMatchEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).GetPerfectMatchEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/GetPerfectMatchEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).GetPerfectMatchEntry(ctx, req.(*perfect_couple_match_logic.GetPerfectMatchEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_EnrollPerfectMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.EnrollPerfectMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).EnrollPerfectMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/EnrollPerfectMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).EnrollPerfectMatch(ctx, req.(*perfect_couple_match_logic.EnrollPerfectMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_GetPerfectMatchQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.GetPerfectMatchQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).GetPerfectMatchQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/GetPerfectMatchQuestions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).GetPerfectMatchQuestions(ctx, req.(*perfect_couple_match_logic.GetPerfectMatchQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_SendPerfectMatchHeartbeat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.SendPerfectMatchHeartbeatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).SendPerfectMatchHeartbeat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/SendPerfectMatchHeartbeat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).SendPerfectMatchHeartbeat(ctx, req.(*perfect_couple_match_logic.SendPerfectMatchHeartbeatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_SendPerfectMatchAnswer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.SendPerfectMatchAnswerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).SendPerfectMatchAnswer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/SendPerfectMatchAnswer",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).SendPerfectMatchAnswer(ctx, req.(*perfect_couple_match_logic.SendPerfectMatchAnswerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PerfectCoupleMatchLogic_CancelPerfectMatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(perfect_couple_match_logic.CancelPerfectMatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PerfectCoupleMatchLogicServer).CancelPerfectMatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.PerfectCoupleMatchLogic/CancelPerfectMatch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PerfectCoupleMatchLogicServer).CancelPerfectMatch(ctx, req.(*perfect_couple_match_logic.CancelPerfectMatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PerfectCoupleMatchLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.PerfectCoupleMatchLogic",
	HandlerType: (*PerfectCoupleMatchLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPrefectCpGameInfo",
			Handler:    _PerfectCoupleMatchLogic_GetPrefectCpGameInfo_Handler,
		},
		{
			MethodName: "SetPrefectCpGamePhase",
			Handler:    _PerfectCoupleMatchLogic_SetPrefectCpGamePhase_Handler,
		},
		{
			MethodName: "ApplyToHoldMic",
			Handler:    _PerfectCoupleMatchLogic_ApplyToHoldMic_Handler,
		},
		{
			MethodName: "BlowLight",
			Handler:    _PerfectCoupleMatchLogic_BlowLight_Handler,
		},
		{
			MethodName: "ChooseTheOne",
			Handler:    _PerfectCoupleMatchLogic_ChooseTheOne_Handler,
		},
		{
			MethodName: "GetCoupleClues",
			Handler:    _PerfectCoupleMatchLogic_GetCoupleClues_Handler,
		},
		{
			MethodName: "GetMyQuestionnaire",
			Handler:    _PerfectCoupleMatchLogic_GetMyQuestionnaire_Handler,
		},
		{
			MethodName: "GetMyCluesProp",
			Handler:    _PerfectCoupleMatchLogic_GetMyCluesProp_Handler,
		},
		{
			MethodName: "UseCluesProp",
			Handler:    _PerfectCoupleMatchLogic_UseCluesProp_Handler,
		},
		{
			MethodName: "PublishClues",
			Handler:    _PerfectCoupleMatchLogic_PublishClues_Handler,
		},
		{
			MethodName: "GetPerfectMatchEntry",
			Handler:    _PerfectCoupleMatchLogic_GetPerfectMatchEntry_Handler,
		},
		{
			MethodName: "EnrollPerfectMatch",
			Handler:    _PerfectCoupleMatchLogic_EnrollPerfectMatch_Handler,
		},
		{
			MethodName: "GetPerfectMatchQuestions",
			Handler:    _PerfectCoupleMatchLogic_GetPerfectMatchQuestions_Handler,
		},
		{
			MethodName: "SendPerfectMatchHeartbeat",
			Handler:    _PerfectCoupleMatchLogic_SendPerfectMatchHeartbeat_Handler,
		},
		{
			MethodName: "SendPerfectMatchAnswer",
			Handler:    _PerfectCoupleMatchLogic_SendPerfectMatchAnswer_Handler,
		},
		{
			MethodName: "CancelPerfectMatch",
			Handler:    _PerfectCoupleMatchLogic_CancelPerfectMatch_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/perfect-couple-match-logic/perfect-couple-match-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/perfect-couple-match-logic/perfect-couple-match-logic.proto", fileDescriptor_perfect_couple_match_logic_a883acc636b16d7b)
}

var fileDescriptor_perfect_couple_match_logic_a883acc636b16d7b = []byte{
	// 649 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x96, 0x49, 0x6f, 0xd3, 0x4c,
	0x18, 0xc7, 0x65, 0xeb, 0x7d, 0x11, 0x1d, 0x21, 0x0e, 0x23, 0xd6, 0x88, 0x0b, 0xdc, 0x38, 0xc4,
	0x46, 0x29, 0x95, 0x58, 0x0a, 0xa1, 0x8d, 0xaa, 0x04, 0xd4, 0x88, 0x40, 0xcb, 0x85, 0x4b, 0x34,
	0x71, 0x9f, 0xda, 0x96, 0x9c, 0x19, 0xe3, 0x99, 0x34, 0xf2, 0x95, 0x13, 0x1f, 0x80, 0x13, 0x92,
	0x25, 0x4e, 0x48, 0x9c, 0x59, 0x03, 0xdc, 0x59, 0xa5, 0x7e, 0x17, 0x8e, 0x1c, 0x10, 0x8a, 0x97,
	0xd4, 0xe3, 0xa4, 0xf5, 0x72, 0xaa, 0x2a, 0xff, 0x7f, 0xff, 0xf9, 0xd9, 0xf3, 0x68, 0x32, 0xa8,
	0xed, 0x30, 0xd3, 0x36, 0xf8, 0x9e, 0x57, 0x37, 0x99, 0xee, 0x82, 0xb7, 0x0b, 0x86, 0xa8, 0x1b,
	0x6c, 0xe4, 0x3a, 0x50, 0x1f, 0x12, 0x61, 0x58, 0xf5, 0x30, 0x70, 0xc4, 0x23, 0xcd, 0xf5, 0x98,
	0x60, 0xf8, 0xff, 0xf0, 0x9f, 0xda, 0xe5, 0x74, 0x9f, 0x49, 0x04, 0x8c, 0x89, 0xaf, 0x33, 0x57,
	0xd8, 0x8c, 0xf2, 0xe4, 0x6f, 0x44, 0xd4, 0x9a, 0xc4, 0x75, 0x93, 0xde, 0x7e, 0xd4, 0xdb, 0x0f,
	0x7b, 0xfb, 0xd2, 0x92, 0x0b, 0x1e, 0x45, 0x05, 0x8d, 0xbf, 0x18, 0x9d, 0xed, 0x45, 0xa1, 0x56,
	0x98, 0xe9, 0x4e, 0x23, 0x9b, 0xd3, 0x04, 0x7e, 0xa1, 0xa0, 0x53, 0x6d, 0x10, 0x3d, 0x0f, 0xc2,
	0xc7, 0x6e, 0x9b, 0x0c, 0xe1, 0x2e, 0xdd, 0x65, 0xf8, 0x86, 0x66, 0x12, 0xed, 0x88, 0xea, 0x45,
	0xd0, 0x43, 0x78, 0x32, 0x02, 0x2e, 0x6a, 0x37, 0x2b, 0xb1, 0xdc, 0x65, 0x94, 0xc3, 0xa5, 0xa5,
	0xdf, 0xfb, 0x13, 0xed, 0xbf, 0xe3, 0x6f, 0x02, 0x15, 0x07, 0x0a, 0x3a, 0xbd, 0x95, 0xc9, 0xf6,
	0x2c, 0xc2, 0x01, 0xe7, 0xad, 0xb0, 0x90, 0x4a, 0xf4, 0x56, 0xab, 0xc1, 0xb2, 0xdf, 0xdb, 0x40,
	0xc5, 0xcf, 0x14, 0x74, 0x72, 0xcd, 0x75, 0x1d, 0x7f, 0x9b, 0x75, 0x98, 0xb3, 0xd3, 0xb5, 0x0d,
	0x7c, 0x35, 0xa7, 0x5b, 0x8e, 0x27, 0x46, 0x2b, 0x25, 0x29, 0x59, 0xe5, 0x5d, 0xa0, 0x62, 0x1f,
	0x2d, 0xad, 0x3b, 0x6c, 0xbc, 0x69, 0x9b, 0x96, 0xc0, 0x7a, 0x4e, 0xdd, 0x2c, 0x99, 0xac, 0x7f,
	0xa5, 0x38, 0x20, 0x2f, 0xfd, 0x3e, 0x50, 0xf1, 0x53, 0x05, 0x9d, 0x68, 0x59, 0x8c, 0x71, 0xd8,
	0xb6, 0xe0, 0x3e, 0x05, 0xdc, 0xc8, 0x69, 0x4b, 0x87, 0x13, 0x83, 0xe5, 0x52, 0x8c, 0x2c, 0xf1,
	0x21, 0xde, 0x8a, 0x36, 0xc4, 0xf3, 0xdd, 0x72, 0x46, 0xc0, 0x73, 0xb7, 0x42, 0x8e, 0x17, 0xdd,
	0x8a, 0x2c, 0x25, 0xab, 0x7c, 0x0c, 0x54, 0xfc, 0x5c, 0x41, 0xb8, 0x0d, 0xa2, 0xeb, 0x3f, 0x98,
	0x16, 0xda, 0x8c, 0x52, 0x62, 0x7b, 0x80, 0xaf, 0xe5, 0x17, 0x67, 0x90, 0x44, 0xe9, 0x7a, 0x05,
	0x52, 0xd6, 0x9a, 0x1c, 0x7c, 0xa1, 0xae, 0x1f, 0x8a, 0xf7, 0x3c, 0xe6, 0x16, 0xf9, 0x42, 0xa9,
	0x78, 0x89, 0x2f, 0x24, 0x51, 0xb2, 0xca, 0xa7, 0x78, 0x62, 0x1e, 0x71, 0x38, 0x10, 0xc9, 0x9b,
	0x98, 0x74, 0xb8, 0xe8, 0xc4, 0xc8, 0x8c, 0x2c, 0xf1, 0x39, 0x96, 0xe8, 0x8d, 0x06, 0x8e, 0xcd,
	0xad, 0x68, 0x5e, 0xf2, 0x24, 0xd2, 0xe1, 0xa2, 0x12, 0x32, 0x23, 0x4b, 0x7c, 0x09, 0xd4, 0xd9,
	0xf1, 0x1b, 0x35, 0x84, 0xe7, 0xf2, 0x06, 0x15, 0x9e, 0x5f, 0xe8, 0xf8, 0xcd, 0x42, 0x65, 0x8e,
	0xdf, 0x79, 0x56, 0x96, 0xfb, 0x1a, 0x0f, 0xf2, 0x06, 0xf5, 0x98, 0xe3, 0xa4, 0xe3, 0xb9, 0x83,
	0x3c, 0x8f, 0x14, 0x1d, 0xe4, 0x45, 0xa4, 0xac, 0xf5, 0x2d, 0x50, 0xf1, 0x2b, 0x05, 0x9d, 0xcb,
	0xbc, 0x42, 0x32, 0xfc, 0x1c, 0xdf, 0x2e, 0xf7, 0xee, 0x33, 0x30, 0x51, 0x6c, 0x56, 0xe6, 0x65,
	0xd1, 0xef, 0x81, 0x8a, 0x5f, 0x2b, 0xe8, 0xfc, 0x16, 0xd0, 0x9d, 0x34, 0xd0, 0x01, 0xe2, 0x89,
	0x01, 0x10, 0x81, 0x9b, 0xb9, 0xbf, 0x42, 0x87, 0x90, 0x89, 0xea, 0x9d, 0xea, 0x05, 0xb2, 0xeb,
	0x8f, 0x40, 0xc5, 0x2f, 0x15, 0x74, 0x26, 0x0b, 0xac, 0x51, 0x3e, 0x06, 0x0f, 0xaf, 0x96, 0x5c,
	0x27, 0xc2, 0x12, 0xcb, 0x5b, 0x15, 0x69, 0x59, 0xf1, 0x67, 0x3c, 0x8e, 0x2d, 0x42, 0x0d, 0x28,
	0x37, 0x8e, 0xf3, 0x48, 0xd1, 0x71, 0x5c, 0x44, 0xca, 0x5a, 0xbf, 0x02, 0xb5, 0x76, 0xf1, 0xcf,
	0xfe, 0x44, 0xbb, 0x80, 0x6a, 0x87, 0xdf, 0xfc, 0xd6, 0xef, 0x3d, 0xee, 0x98, 0xcc, 0x21, 0xd4,
	0xd4, 0x56, 0x1a, 0x42, 0x68, 0x06, 0x1b, 0xea, 0xe1, 0xcd, 0xcc, 0x60, 0x8e, 0xce, 0xc1, 0xdb,
	0xb3, 0x0d, 0xe0, 0x7a, 0xb1, 0x0b, 0xe6, 0xe0, 0x58, 0x48, 0x2e, 0xff, 0x0b, 0x00, 0x00, 0xff,
	0xff, 0x98, 0x35, 0xcf, 0x6f, 0x91, 0x0a, 0x00, 0x00,
}
