// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/missiongo-logic/missiongo-logic.proto

package missiongologicsr // import "golang.52tt.com/protocol/services/logicsvr-go/missiongologicsr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import missiongologic "golang.52tt.com/protocol/app/missiongologic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MissionGoLogicClient is the client API for MissionGoLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MissionGoLogicClient interface {
	OpenNotificationPermission(ctx context.Context, in *missiongologic.OpenNotificationPermissionReq, opts ...grpc.CallOption) (*missiongologic.OpenNotificationPermissionResp, error)
}

type missionGoLogicClient struct {
	cc *grpc.ClientConn
}

func NewMissionGoLogicClient(cc *grpc.ClientConn) MissionGoLogicClient {
	return &missionGoLogicClient{cc}
}

func (c *missionGoLogicClient) OpenNotificationPermission(ctx context.Context, in *missiongologic.OpenNotificationPermissionReq, opts ...grpc.CallOption) (*missiongologic.OpenNotificationPermissionResp, error) {
	out := new(missiongologic.OpenNotificationPermissionResp)
	err := c.cc.Invoke(ctx, "/logic.MissionGoLogic/OpenNotificationPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MissionGoLogicServer is the server API for MissionGoLogic service.
type MissionGoLogicServer interface {
	OpenNotificationPermission(context.Context, *missiongologic.OpenNotificationPermissionReq) (*missiongologic.OpenNotificationPermissionResp, error)
}

func RegisterMissionGoLogicServer(s *grpc.Server, srv MissionGoLogicServer) {
	s.RegisterService(&_MissionGoLogic_serviceDesc, srv)
}

func _MissionGoLogic_OpenNotificationPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(missiongologic.OpenNotificationPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MissionGoLogicServer).OpenNotificationPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MissionGoLogic/OpenNotificationPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MissionGoLogicServer).OpenNotificationPermission(ctx, req.(*missiongologic.OpenNotificationPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MissionGoLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.MissionGoLogic",
	HandlerType: (*MissionGoLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OpenNotificationPermission",
			Handler:    _MissionGoLogic_OpenNotificationPermission_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/missiongo-logic/missiongo-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/missiongo-logic/missiongo-logic.proto", fileDescriptor_missiongo_logic_9c2df077e41d2a01)
}

var fileDescriptor_missiongo_logic_9c2df077e41d2a01 = []byte{
	// 223 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x32, 0xcc, 0xc9, 0x4f, 0xcf,
	0x4c, 0x2e, 0x2e, 0x2b, 0xd2, 0x4d, 0xcf, 0xd7, 0xcf, 0xcd, 0x2c, 0x2e, 0xce, 0xcc, 0xcf, 0x4b,
	0xcf, 0xd7, 0x05, 0x8b, 0xa2, 0xf3, 0xf5, 0x0a, 0x8a, 0xf2, 0x4b, 0xf2, 0x85, 0x58, 0xc1, 0x1c,
	0x29, 0x4d, 0x64, 0x9d, 0xe9, 0x89, 0x25, 0xa9, 0xe5, 0x89, 0x95, 0xfa, 0xf9, 0x05, 0x25, 0x99,
	0xf9, 0x79, 0xc5, 0x30, 0x1a, 0xa2, 0x43, 0x4a, 0x2a, 0xb1, 0xa0, 0x00, 0xdd, 0xb0, 0x78, 0x88,
	0x9c, 0xd1, 0x76, 0x46, 0x2e, 0x3e, 0x5f, 0x88, 0x94, 0x7b, 0xbe, 0x0f, 0x48, 0x46, 0xa8, 0x87,
	0x91, 0x4b, 0xca, 0xbf, 0x20, 0x35, 0xcf, 0x2f, 0xbf, 0x24, 0x33, 0x2d, 0x33, 0x39, 0x11, 0x64,
	0x54, 0x40, 0x6a, 0x11, 0xd4, 0x00, 0x21, 0x03, 0xbd, 0xf4, 0x44, 0x3d, 0xb8, 0x69, 0x10, 0x97,
	0xe1, 0x56, 0x1e, 0x94, 0x5a, 0x28, 0x65, 0x48, 0xa2, 0x8e, 0xe2, 0x02, 0x25, 0xce, 0x4f, 0xe7,
	0x77, 0xea, 0xb1, 0x70, 0x4c, 0x7d, 0xcd, 0x28, 0x25, 0xf6, 0xeb, 0xfc, 0x4e, 0x3d, 0x41, 0x2e,
	0x7e, 0x34, 0x0f, 0x38, 0x39, 0x44, 0xd9, 0xa5, 0xe7, 0xe7, 0x24, 0xe6, 0xa5, 0xeb, 0x99, 0x1a,
	0x95, 0x94, 0xe8, 0x25, 0xe7, 0xe7, 0xea, 0x83, 0xbd, 0x94, 0x9c, 0x9f, 0xa3, 0x5f, 0x9c, 0x5a,
	0x54, 0x96, 0x99, 0x9c, 0x5a, 0xac, 0x8f, 0x35, 0x78, 0x21, 0x82, 0x45, 0x49, 0x6c, 0x60, 0xf5,
	0xc6, 0x80, 0x00, 0x00, 0x00, 0xff, 0xff, 0xc7, 0xbd, 0x19, 0x62, 0x85, 0x01, 0x00, 0x00,
}
