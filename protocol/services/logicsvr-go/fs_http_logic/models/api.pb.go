// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/fs_http_logic/models/api.proto

package fsapi

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TokenData struct {
	Uptoken              string   `protobuf:"bytes,1,opt,name=uptoken,proto3" json:"uptoken,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenData) Reset()         { *m = TokenData{} }
func (m *TokenData) String() string { return proto.CompactTextString(m) }
func (*TokenData) ProtoMessage()    {}
func (*TokenData) Descriptor() ([]byte, []int) {
	return fileDescriptor_api_000a9a4a1f7a4fac, []int{0}
}
func (m *TokenData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenData.Unmarshal(m, b)
}
func (m *TokenData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenData.Marshal(b, m, deterministic)
}
func (dst *TokenData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenData.Merge(dst, src)
}
func (m *TokenData) XXX_Size() int {
	return xxx_messageInfo_TokenData.Size(m)
}
func (m *TokenData) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenData.DiscardUnknown(m)
}

var xxx_messageInfo_TokenData proto.InternalMessageInfo

func (m *TokenData) GetUptoken() string {
	if m != nil {
		return m.Uptoken
	}
	return ""
}

func init() {
	proto.RegisterType((*TokenData)(nil), "fsapi.TokenData")
}

func init() {
	proto.RegisterFile("logicsvr-go/fs_http_logic/models/api.proto", fileDescriptor_api_000a9a4a1f7a4fac)
}

var fileDescriptor_api_000a9a4a1f7a4fac = []byte{
	// 107 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0xca, 0xc9, 0x4f, 0xcf,
	0x4c, 0x2e, 0x2e, 0x2b, 0xd2, 0x4d, 0xcf, 0xd7, 0x4f, 0x2b, 0x8e, 0xcf, 0x28, 0x29, 0x29, 0x88,
	0x07, 0x8b, 0xe9, 0xe7, 0xe6, 0xa7, 0xa4, 0xe6, 0x14, 0xeb, 0x27, 0x16, 0x64, 0xea, 0x15, 0x14,
	0xe5, 0x97, 0xe4, 0x0b, 0xb1, 0xa6, 0x15, 0x27, 0x16, 0x64, 0x2a, 0xa9, 0x72, 0x71, 0x86, 0xe4,
	0x67, 0xa7, 0xe6, 0xb9, 0x24, 0x96, 0x24, 0x0a, 0x49, 0x70, 0xb1, 0x97, 0x16, 0x94, 0x80, 0xb8,
	0x12, 0x8c, 0x0a, 0x8c, 0x1a, 0x9c, 0x41, 0x30, 0x6e, 0x12, 0x1b, 0x58, 0x93, 0x31, 0x20, 0x00,
	0x00, 0xff, 0xff, 0x23, 0x05, 0xc1, 0x78, 0x62, 0x00, 0x00, 0x00,
}
