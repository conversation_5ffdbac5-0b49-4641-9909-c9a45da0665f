// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/mystery-place-logic/mystery-place-logic.proto

package mystery_place_logic // import "golang.52tt.com/protocol/services/logicsvr-go/mystery-place-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import mystery_place_logic "golang.52tt.com/protocol/app/mystery-place-logic"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MysteryPlaceLogicClient is the client API for MysteryPlaceLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MysteryPlaceLogicClient interface {
	ListScenarioInfo(ctx context.Context, in *mystery_place_logic.ListScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListScenarioInfoResp, error)
	GetScenarioInfo(ctx context.Context, in *mystery_place_logic.GetScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioInfoResp, error)
	GetLoginTask(ctx context.Context, in *mystery_place_logic.GetLoginTaskReq, opts ...grpc.CallOption) (*mystery_place_logic.GetLoginTaskResp, error)
	ReceiveLoginTaskAward(ctx context.Context, in *mystery_place_logic.ReceiveLoginTaskAwardReq, opts ...grpc.CallOption) (*mystery_place_logic.ReceiveLoginTaskAwardResp, error)
	GetBalanceInfo(ctx context.Context, in *mystery_place_logic.GetBalanceInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetBalanceInfoResp, error)
	CheckScenarioRoom(ctx context.Context, in *mystery_place_logic.CheckScenarioRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.CheckScenarioRoomResp, error)
	Invite2MyRoom(ctx context.Context, in *mystery_place_logic.Invite2MyRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.Invite2MyRoomResp, error)
	GetInvite2MyRoomResult(ctx context.Context, in *mystery_place_logic.GetInvite2MyRoomResultReq, opts ...grpc.CallOption) (*mystery_place_logic.GetInvite2MyRoomResultResp, error)
	ListRecommendedScenarioSimpleInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioSimpleInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioSimpleInfoResp, error)
	GetRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.GetRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRecommendedScenarioDetailInfoResp, error)
	GetRoomShareLinkByTabId(ctx context.Context, in *mystery_place_logic.GetRoomShareLinkByTabIdReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRoomShareLinkByTabIdResp, error)
	MysteryPlaceChannelList(ctx context.Context, in *mystery_place_logic.MysteryPlaceChannelListReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceChannelListResp, error)
	GetCommentPageParams(ctx context.Context, in *mystery_place_logic.GetCommentPageParamsReq, opts ...grpc.CallOption) (*mystery_place_logic.GetCommentPageParamsResp, error)
	CommentToScenario(ctx context.Context, in *mystery_place_logic.CommentToScenarioReq, opts ...grpc.CallOption) (*mystery_place_logic.CommentToScenarioResp, error)
	GetNewScenarioTip(ctx context.Context, in *mystery_place_logic.GetNewScenarioTipReq, opts ...grpc.CallOption) (*mystery_place_logic.GetNewScenarioTipResp, error)
	MarkNewScenarioTipRead(ctx context.Context, in *mystery_place_logic.MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*mystery_place_logic.MarkNewScenarioTipReadResp, error)
	MysteryPlaceClientConfig(ctx context.Context, in *mystery_place_logic.MysteryPlaceClientConfigReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceClientConfigResp, error)
	GetPlayedScenarioRecordList(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordListResp, error)
	GetScenarioChapterSummary(ctx context.Context, in *mystery_place_logic.GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioChapterSummaryResp, error)
	SetPlayedScenarioRecordVisibility(ctx context.Context, in *mystery_place_logic.SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*mystery_place_logic.SetPlayedScenarioRecordVisibilityResp, error)
	GetPlayedScenarioRecordDetail(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordDetailResp, error)
	GetRcmdMiJingTab(ctx context.Context, in *mystery_place_logic.GetRcmdMiJingTabReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRcmdMiJingTabResp, error)
	ListRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioDetailInfoResp, error)
	// 用户是否有剧本限免券
	IsUserHasScenarioFreeCoupons(ctx context.Context, in *mystery_place_logic.IsUserHasScenarioFreeCouponsReq, opts ...grpc.CallOption) (*mystery_place_logic.IsUserHasScenarioFreeCouponsResp, error)
	HomePageBigTofu(ctx context.Context, in *mystery_place_logic.HomePageBigTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageBigTofuResp, error)
	HomePageRightTofu(ctx context.Context, in *mystery_place_logic.HomePageRightTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageRightTofuResp, error)
	// 获取房间剧本信息
	GetChannelScenarioInfo(ctx context.Context, in *mystery_place_logic.GetChannelScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetChannelScenarioInfoResp, error)
}

type mysteryPlaceLogicClient struct {
	cc *grpc.ClientConn
}

func NewMysteryPlaceLogicClient(cc *grpc.ClientConn) MysteryPlaceLogicClient {
	return &mysteryPlaceLogicClient{cc}
}

func (c *mysteryPlaceLogicClient) ListScenarioInfo(ctx context.Context, in *mystery_place_logic.ListScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListScenarioInfoResp, error) {
	out := new(mystery_place_logic.ListScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/ListScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetScenarioInfo(ctx context.Context, in *mystery_place_logic.GetScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioInfoResp, error) {
	out := new(mystery_place_logic.GetScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetLoginTask(ctx context.Context, in *mystery_place_logic.GetLoginTaskReq, opts ...grpc.CallOption) (*mystery_place_logic.GetLoginTaskResp, error) {
	out := new(mystery_place_logic.GetLoginTaskResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetLoginTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) ReceiveLoginTaskAward(ctx context.Context, in *mystery_place_logic.ReceiveLoginTaskAwardReq, opts ...grpc.CallOption) (*mystery_place_logic.ReceiveLoginTaskAwardResp, error) {
	out := new(mystery_place_logic.ReceiveLoginTaskAwardResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/ReceiveLoginTaskAward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetBalanceInfo(ctx context.Context, in *mystery_place_logic.GetBalanceInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetBalanceInfoResp, error) {
	out := new(mystery_place_logic.GetBalanceInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetBalanceInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) CheckScenarioRoom(ctx context.Context, in *mystery_place_logic.CheckScenarioRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.CheckScenarioRoomResp, error) {
	out := new(mystery_place_logic.CheckScenarioRoomResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/CheckScenarioRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) Invite2MyRoom(ctx context.Context, in *mystery_place_logic.Invite2MyRoomReq, opts ...grpc.CallOption) (*mystery_place_logic.Invite2MyRoomResp, error) {
	out := new(mystery_place_logic.Invite2MyRoomResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/Invite2MyRoom", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetInvite2MyRoomResult(ctx context.Context, in *mystery_place_logic.GetInvite2MyRoomResultReq, opts ...grpc.CallOption) (*mystery_place_logic.GetInvite2MyRoomResultResp, error) {
	out := new(mystery_place_logic.GetInvite2MyRoomResultResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetInvite2MyRoomResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) ListRecommendedScenarioSimpleInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioSimpleInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioSimpleInfoResp, error) {
	out := new(mystery_place_logic.ListRecommendedScenarioSimpleInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/ListRecommendedScenarioSimpleInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.GetRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRecommendedScenarioDetailInfoResp, error) {
	out := new(mystery_place_logic.GetRecommendedScenarioDetailInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetRecommendedScenarioDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetRoomShareLinkByTabId(ctx context.Context, in *mystery_place_logic.GetRoomShareLinkByTabIdReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRoomShareLinkByTabIdResp, error) {
	out := new(mystery_place_logic.GetRoomShareLinkByTabIdResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetRoomShareLinkByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) MysteryPlaceChannelList(ctx context.Context, in *mystery_place_logic.MysteryPlaceChannelListReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceChannelListResp, error) {
	out := new(mystery_place_logic.MysteryPlaceChannelListResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/MysteryPlaceChannelList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetCommentPageParams(ctx context.Context, in *mystery_place_logic.GetCommentPageParamsReq, opts ...grpc.CallOption) (*mystery_place_logic.GetCommentPageParamsResp, error) {
	out := new(mystery_place_logic.GetCommentPageParamsResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetCommentPageParams", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) CommentToScenario(ctx context.Context, in *mystery_place_logic.CommentToScenarioReq, opts ...grpc.CallOption) (*mystery_place_logic.CommentToScenarioResp, error) {
	out := new(mystery_place_logic.CommentToScenarioResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/CommentToScenario", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetNewScenarioTip(ctx context.Context, in *mystery_place_logic.GetNewScenarioTipReq, opts ...grpc.CallOption) (*mystery_place_logic.GetNewScenarioTipResp, error) {
	out := new(mystery_place_logic.GetNewScenarioTipResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetNewScenarioTip", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) MarkNewScenarioTipRead(ctx context.Context, in *mystery_place_logic.MarkNewScenarioTipReadReq, opts ...grpc.CallOption) (*mystery_place_logic.MarkNewScenarioTipReadResp, error) {
	out := new(mystery_place_logic.MarkNewScenarioTipReadResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/MarkNewScenarioTipRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) MysteryPlaceClientConfig(ctx context.Context, in *mystery_place_logic.MysteryPlaceClientConfigReq, opts ...grpc.CallOption) (*mystery_place_logic.MysteryPlaceClientConfigResp, error) {
	out := new(mystery_place_logic.MysteryPlaceClientConfigResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/MysteryPlaceClientConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetPlayedScenarioRecordList(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordListReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordListResp, error) {
	out := new(mystery_place_logic.GetPlayedScenarioRecordListResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetPlayedScenarioRecordList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetScenarioChapterSummary(ctx context.Context, in *mystery_place_logic.GetScenarioChapterSummaryReq, opts ...grpc.CallOption) (*mystery_place_logic.GetScenarioChapterSummaryResp, error) {
	out := new(mystery_place_logic.GetScenarioChapterSummaryResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetScenarioChapterSummary", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) SetPlayedScenarioRecordVisibility(ctx context.Context, in *mystery_place_logic.SetPlayedScenarioRecordVisibilityReq, opts ...grpc.CallOption) (*mystery_place_logic.SetPlayedScenarioRecordVisibilityResp, error) {
	out := new(mystery_place_logic.SetPlayedScenarioRecordVisibilityResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/SetPlayedScenarioRecordVisibility", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetPlayedScenarioRecordDetail(ctx context.Context, in *mystery_place_logic.GetPlayedScenarioRecordDetailReq, opts ...grpc.CallOption) (*mystery_place_logic.GetPlayedScenarioRecordDetailResp, error) {
	out := new(mystery_place_logic.GetPlayedScenarioRecordDetailResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetPlayedScenarioRecordDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetRcmdMiJingTab(ctx context.Context, in *mystery_place_logic.GetRcmdMiJingTabReq, opts ...grpc.CallOption) (*mystery_place_logic.GetRcmdMiJingTabResp, error) {
	out := new(mystery_place_logic.GetRcmdMiJingTabResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetRcmdMiJingTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) ListRecommendedScenarioDetailInfo(ctx context.Context, in *mystery_place_logic.ListRecommendedScenarioDetailInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.ListRecommendedScenarioDetailInfoResp, error) {
	out := new(mystery_place_logic.ListRecommendedScenarioDetailInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/ListRecommendedScenarioDetailInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) IsUserHasScenarioFreeCoupons(ctx context.Context, in *mystery_place_logic.IsUserHasScenarioFreeCouponsReq, opts ...grpc.CallOption) (*mystery_place_logic.IsUserHasScenarioFreeCouponsResp, error) {
	out := new(mystery_place_logic.IsUserHasScenarioFreeCouponsResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/IsUserHasScenarioFreeCoupons", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) HomePageBigTofu(ctx context.Context, in *mystery_place_logic.HomePageBigTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageBigTofuResp, error) {
	out := new(mystery_place_logic.HomePageBigTofuResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/HomePageBigTofu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) HomePageRightTofu(ctx context.Context, in *mystery_place_logic.HomePageRightTofuReq, opts ...grpc.CallOption) (*mystery_place_logic.HomePageRightTofuResp, error) {
	out := new(mystery_place_logic.HomePageRightTofuResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/HomePageRightTofu", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mysteryPlaceLogicClient) GetChannelScenarioInfo(ctx context.Context, in *mystery_place_logic.GetChannelScenarioInfoReq, opts ...grpc.CallOption) (*mystery_place_logic.GetChannelScenarioInfoResp, error) {
	out := new(mystery_place_logic.GetChannelScenarioInfoResp)
	err := c.cc.Invoke(ctx, "/logic.MysteryPlaceLogic/GetChannelScenarioInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MysteryPlaceLogicServer is the server API for MysteryPlaceLogic service.
type MysteryPlaceLogicServer interface {
	ListScenarioInfo(context.Context, *mystery_place_logic.ListScenarioInfoReq) (*mystery_place_logic.ListScenarioInfoResp, error)
	GetScenarioInfo(context.Context, *mystery_place_logic.GetScenarioInfoReq) (*mystery_place_logic.GetScenarioInfoResp, error)
	GetLoginTask(context.Context, *mystery_place_logic.GetLoginTaskReq) (*mystery_place_logic.GetLoginTaskResp, error)
	ReceiveLoginTaskAward(context.Context, *mystery_place_logic.ReceiveLoginTaskAwardReq) (*mystery_place_logic.ReceiveLoginTaskAwardResp, error)
	GetBalanceInfo(context.Context, *mystery_place_logic.GetBalanceInfoReq) (*mystery_place_logic.GetBalanceInfoResp, error)
	CheckScenarioRoom(context.Context, *mystery_place_logic.CheckScenarioRoomReq) (*mystery_place_logic.CheckScenarioRoomResp, error)
	Invite2MyRoom(context.Context, *mystery_place_logic.Invite2MyRoomReq) (*mystery_place_logic.Invite2MyRoomResp, error)
	GetInvite2MyRoomResult(context.Context, *mystery_place_logic.GetInvite2MyRoomResultReq) (*mystery_place_logic.GetInvite2MyRoomResultResp, error)
	ListRecommendedScenarioSimpleInfo(context.Context, *mystery_place_logic.ListRecommendedScenarioSimpleInfoReq) (*mystery_place_logic.ListRecommendedScenarioSimpleInfoResp, error)
	GetRecommendedScenarioDetailInfo(context.Context, *mystery_place_logic.GetRecommendedScenarioDetailInfoReq) (*mystery_place_logic.GetRecommendedScenarioDetailInfoResp, error)
	GetRoomShareLinkByTabId(context.Context, *mystery_place_logic.GetRoomShareLinkByTabIdReq) (*mystery_place_logic.GetRoomShareLinkByTabIdResp, error)
	MysteryPlaceChannelList(context.Context, *mystery_place_logic.MysteryPlaceChannelListReq) (*mystery_place_logic.MysteryPlaceChannelListResp, error)
	GetCommentPageParams(context.Context, *mystery_place_logic.GetCommentPageParamsReq) (*mystery_place_logic.GetCommentPageParamsResp, error)
	CommentToScenario(context.Context, *mystery_place_logic.CommentToScenarioReq) (*mystery_place_logic.CommentToScenarioResp, error)
	GetNewScenarioTip(context.Context, *mystery_place_logic.GetNewScenarioTipReq) (*mystery_place_logic.GetNewScenarioTipResp, error)
	MarkNewScenarioTipRead(context.Context, *mystery_place_logic.MarkNewScenarioTipReadReq) (*mystery_place_logic.MarkNewScenarioTipReadResp, error)
	MysteryPlaceClientConfig(context.Context, *mystery_place_logic.MysteryPlaceClientConfigReq) (*mystery_place_logic.MysteryPlaceClientConfigResp, error)
	GetPlayedScenarioRecordList(context.Context, *mystery_place_logic.GetPlayedScenarioRecordListReq) (*mystery_place_logic.GetPlayedScenarioRecordListResp, error)
	GetScenarioChapterSummary(context.Context, *mystery_place_logic.GetScenarioChapterSummaryReq) (*mystery_place_logic.GetScenarioChapterSummaryResp, error)
	SetPlayedScenarioRecordVisibility(context.Context, *mystery_place_logic.SetPlayedScenarioRecordVisibilityReq) (*mystery_place_logic.SetPlayedScenarioRecordVisibilityResp, error)
	GetPlayedScenarioRecordDetail(context.Context, *mystery_place_logic.GetPlayedScenarioRecordDetailReq) (*mystery_place_logic.GetPlayedScenarioRecordDetailResp, error)
	GetRcmdMiJingTab(context.Context, *mystery_place_logic.GetRcmdMiJingTabReq) (*mystery_place_logic.GetRcmdMiJingTabResp, error)
	ListRecommendedScenarioDetailInfo(context.Context, *mystery_place_logic.ListRecommendedScenarioDetailInfoReq) (*mystery_place_logic.ListRecommendedScenarioDetailInfoResp, error)
	// 用户是否有剧本限免券
	IsUserHasScenarioFreeCoupons(context.Context, *mystery_place_logic.IsUserHasScenarioFreeCouponsReq) (*mystery_place_logic.IsUserHasScenarioFreeCouponsResp, error)
	HomePageBigTofu(context.Context, *mystery_place_logic.HomePageBigTofuReq) (*mystery_place_logic.HomePageBigTofuResp, error)
	HomePageRightTofu(context.Context, *mystery_place_logic.HomePageRightTofuReq) (*mystery_place_logic.HomePageRightTofuResp, error)
	// 获取房间剧本信息
	GetChannelScenarioInfo(context.Context, *mystery_place_logic.GetChannelScenarioInfoReq) (*mystery_place_logic.GetChannelScenarioInfoResp, error)
}

func RegisterMysteryPlaceLogicServer(s *grpc.Server, srv MysteryPlaceLogicServer) {
	s.RegisterService(&_MysteryPlaceLogic_serviceDesc, srv)
}

func _MysteryPlaceLogic_ListScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ListScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ListScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/ListScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ListScenarioInfo(ctx, req.(*mystery_place_logic.ListScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetScenarioInfo(ctx, req.(*mystery_place_logic.GetScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetLoginTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetLoginTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetLoginTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetLoginTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetLoginTask(ctx, req.(*mystery_place_logic.GetLoginTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_ReceiveLoginTaskAward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ReceiveLoginTaskAwardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ReceiveLoginTaskAward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/ReceiveLoginTaskAward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ReceiveLoginTaskAward(ctx, req.(*mystery_place_logic.ReceiveLoginTaskAwardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetBalanceInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetBalanceInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetBalanceInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetBalanceInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetBalanceInfo(ctx, req.(*mystery_place_logic.GetBalanceInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_CheckScenarioRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.CheckScenarioRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).CheckScenarioRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/CheckScenarioRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).CheckScenarioRoom(ctx, req.(*mystery_place_logic.CheckScenarioRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_Invite2MyRoom_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.Invite2MyRoomReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).Invite2MyRoom(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/Invite2MyRoom",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).Invite2MyRoom(ctx, req.(*mystery_place_logic.Invite2MyRoomReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetInvite2MyRoomResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetInvite2MyRoomResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetInvite2MyRoomResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetInvite2MyRoomResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetInvite2MyRoomResult(ctx, req.(*mystery_place_logic.GetInvite2MyRoomResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_ListRecommendedScenarioSimpleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ListRecommendedScenarioSimpleInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioSimpleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/ListRecommendedScenarioSimpleInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioSimpleInfo(ctx, req.(*mystery_place_logic.ListRecommendedScenarioSimpleInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetRecommendedScenarioDetailInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetRecommendedScenarioDetailInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetRecommendedScenarioDetailInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetRecommendedScenarioDetailInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetRecommendedScenarioDetailInfo(ctx, req.(*mystery_place_logic.GetRecommendedScenarioDetailInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetRoomShareLinkByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetRoomShareLinkByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetRoomShareLinkByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetRoomShareLinkByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetRoomShareLinkByTabId(ctx, req.(*mystery_place_logic.GetRoomShareLinkByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_MysteryPlaceChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.MysteryPlaceChannelListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/MysteryPlaceChannelList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceChannelList(ctx, req.(*mystery_place_logic.MysteryPlaceChannelListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetCommentPageParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetCommentPageParamsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetCommentPageParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetCommentPageParams",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetCommentPageParams(ctx, req.(*mystery_place_logic.GetCommentPageParamsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_CommentToScenario_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.CommentToScenarioReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).CommentToScenario(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/CommentToScenario",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).CommentToScenario(ctx, req.(*mystery_place_logic.CommentToScenarioReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetNewScenarioTip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetNewScenarioTipReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetNewScenarioTip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetNewScenarioTip",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetNewScenarioTip(ctx, req.(*mystery_place_logic.GetNewScenarioTipReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_MarkNewScenarioTipRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.MarkNewScenarioTipReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).MarkNewScenarioTipRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/MarkNewScenarioTipRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).MarkNewScenarioTipRead(ctx, req.(*mystery_place_logic.MarkNewScenarioTipReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_MysteryPlaceClientConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.MysteryPlaceClientConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceClientConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/MysteryPlaceClientConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).MysteryPlaceClientConfig(ctx, req.(*mystery_place_logic.MysteryPlaceClientConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetPlayedScenarioRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetPlayedScenarioRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetPlayedScenarioRecordList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordList(ctx, req.(*mystery_place_logic.GetPlayedScenarioRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetScenarioChapterSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetScenarioChapterSummaryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetScenarioChapterSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetScenarioChapterSummary",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetScenarioChapterSummary(ctx, req.(*mystery_place_logic.GetScenarioChapterSummaryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_SetPlayedScenarioRecordVisibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.SetPlayedScenarioRecordVisibilityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).SetPlayedScenarioRecordVisibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/SetPlayedScenarioRecordVisibility",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).SetPlayedScenarioRecordVisibility(ctx, req.(*mystery_place_logic.SetPlayedScenarioRecordVisibilityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetPlayedScenarioRecordDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetPlayedScenarioRecordDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetPlayedScenarioRecordDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetPlayedScenarioRecordDetail(ctx, req.(*mystery_place_logic.GetPlayedScenarioRecordDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetRcmdMiJingTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetRcmdMiJingTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetRcmdMiJingTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetRcmdMiJingTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetRcmdMiJingTab(ctx, req.(*mystery_place_logic.GetRcmdMiJingTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_ListRecommendedScenarioDetailInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.ListRecommendedScenarioDetailInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioDetailInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/ListRecommendedScenarioDetailInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).ListRecommendedScenarioDetailInfo(ctx, req.(*mystery_place_logic.ListRecommendedScenarioDetailInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_IsUserHasScenarioFreeCoupons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.IsUserHasScenarioFreeCouponsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).IsUserHasScenarioFreeCoupons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/IsUserHasScenarioFreeCoupons",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).IsUserHasScenarioFreeCoupons(ctx, req.(*mystery_place_logic.IsUserHasScenarioFreeCouponsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_HomePageBigTofu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.HomePageBigTofuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).HomePageBigTofu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/HomePageBigTofu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).HomePageBigTofu(ctx, req.(*mystery_place_logic.HomePageBigTofuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_HomePageRightTofu_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.HomePageRightTofuReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).HomePageRightTofu(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/HomePageRightTofu",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).HomePageRightTofu(ctx, req.(*mystery_place_logic.HomePageRightTofuReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MysteryPlaceLogic_GetChannelScenarioInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(mystery_place_logic.GetChannelScenarioInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MysteryPlaceLogicServer).GetChannelScenarioInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.MysteryPlaceLogic/GetChannelScenarioInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MysteryPlaceLogicServer).GetChannelScenarioInfo(ctx, req.(*mystery_place_logic.GetChannelScenarioInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MysteryPlaceLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.MysteryPlaceLogic",
	HandlerType: (*MysteryPlaceLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListScenarioInfo",
			Handler:    _MysteryPlaceLogic_ListScenarioInfo_Handler,
		},
		{
			MethodName: "GetScenarioInfo",
			Handler:    _MysteryPlaceLogic_GetScenarioInfo_Handler,
		},
		{
			MethodName: "GetLoginTask",
			Handler:    _MysteryPlaceLogic_GetLoginTask_Handler,
		},
		{
			MethodName: "ReceiveLoginTaskAward",
			Handler:    _MysteryPlaceLogic_ReceiveLoginTaskAward_Handler,
		},
		{
			MethodName: "GetBalanceInfo",
			Handler:    _MysteryPlaceLogic_GetBalanceInfo_Handler,
		},
		{
			MethodName: "CheckScenarioRoom",
			Handler:    _MysteryPlaceLogic_CheckScenarioRoom_Handler,
		},
		{
			MethodName: "Invite2MyRoom",
			Handler:    _MysteryPlaceLogic_Invite2MyRoom_Handler,
		},
		{
			MethodName: "GetInvite2MyRoomResult",
			Handler:    _MysteryPlaceLogic_GetInvite2MyRoomResult_Handler,
		},
		{
			MethodName: "ListRecommendedScenarioSimpleInfo",
			Handler:    _MysteryPlaceLogic_ListRecommendedScenarioSimpleInfo_Handler,
		},
		{
			MethodName: "GetRecommendedScenarioDetailInfo",
			Handler:    _MysteryPlaceLogic_GetRecommendedScenarioDetailInfo_Handler,
		},
		{
			MethodName: "GetRoomShareLinkByTabId",
			Handler:    _MysteryPlaceLogic_GetRoomShareLinkByTabId_Handler,
		},
		{
			MethodName: "MysteryPlaceChannelList",
			Handler:    _MysteryPlaceLogic_MysteryPlaceChannelList_Handler,
		},
		{
			MethodName: "GetCommentPageParams",
			Handler:    _MysteryPlaceLogic_GetCommentPageParams_Handler,
		},
		{
			MethodName: "CommentToScenario",
			Handler:    _MysteryPlaceLogic_CommentToScenario_Handler,
		},
		{
			MethodName: "GetNewScenarioTip",
			Handler:    _MysteryPlaceLogic_GetNewScenarioTip_Handler,
		},
		{
			MethodName: "MarkNewScenarioTipRead",
			Handler:    _MysteryPlaceLogic_MarkNewScenarioTipRead_Handler,
		},
		{
			MethodName: "MysteryPlaceClientConfig",
			Handler:    _MysteryPlaceLogic_MysteryPlaceClientConfig_Handler,
		},
		{
			MethodName: "GetPlayedScenarioRecordList",
			Handler:    _MysteryPlaceLogic_GetPlayedScenarioRecordList_Handler,
		},
		{
			MethodName: "GetScenarioChapterSummary",
			Handler:    _MysteryPlaceLogic_GetScenarioChapterSummary_Handler,
		},
		{
			MethodName: "SetPlayedScenarioRecordVisibility",
			Handler:    _MysteryPlaceLogic_SetPlayedScenarioRecordVisibility_Handler,
		},
		{
			MethodName: "GetPlayedScenarioRecordDetail",
			Handler:    _MysteryPlaceLogic_GetPlayedScenarioRecordDetail_Handler,
		},
		{
			MethodName: "GetRcmdMiJingTab",
			Handler:    _MysteryPlaceLogic_GetRcmdMiJingTab_Handler,
		},
		{
			MethodName: "ListRecommendedScenarioDetailInfo",
			Handler:    _MysteryPlaceLogic_ListRecommendedScenarioDetailInfo_Handler,
		},
		{
			MethodName: "IsUserHasScenarioFreeCoupons",
			Handler:    _MysteryPlaceLogic_IsUserHasScenarioFreeCoupons_Handler,
		},
		{
			MethodName: "HomePageBigTofu",
			Handler:    _MysteryPlaceLogic_HomePageBigTofu_Handler,
		},
		{
			MethodName: "HomePageRightTofu",
			Handler:    _MysteryPlaceLogic_HomePageRightTofu_Handler,
		},
		{
			MethodName: "GetChannelScenarioInfo",
			Handler:    _MysteryPlaceLogic_GetChannelScenarioInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/mystery-place-logic/mystery-place-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/mystery-place-logic/mystery-place-logic.proto", fileDescriptor_mystery_place_logic_531ccc87bb820e84)
}

var fileDescriptor_mystery_place_logic_531ccc87bb820e84 = []byte{
	// 984 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x97, 0xcd, 0x8f, 0xdb, 0x44,
	0x18, 0xc6, 0xe5, 0x11, 0x20, 0x18, 0xf1, 0xd5, 0x81, 0xf2, 0x11, 0x28, 0x02, 0x2e, 0xb0, 0x6d,
	0x37, 0x69, 0xb7, 0x2d, 0xb4, 0xa2, 0x3d, 0x74, 0x83, 0x70, 0x17, 0xed, 0xa2, 0x55, 0x12, 0x38,
	0x70, 0x59, 0x4d, 0x9c, 0x77, 0x9d, 0x51, 0x6c, 0x8f, 0xb1, 0x27, 0x59, 0x2c, 0xae, 0x48, 0x48,
	0x48, 0x48, 0x48, 0x9c, 0x46, 0x42, 0xe2, 0x1b, 0x0a, 0x85, 0x1b, 0x5f, 0x0b, 0x07, 0xc4, 0x0d,
	0x24, 0xa4, 0xfe, 0x21, 0xdc, 0x38, 0x72, 0xaa, 0xc6, 0x4e, 0x76, 0xf2, 0x61, 0x4f, 0xec, 0xf4,
	0xb4, 0xda, 0xf8, 0xf7, 0xcc, 0xf3, 0x78, 0xc6, 0xf3, 0xce, 0x3b, 0xf8, 0x92, 0xc7, 0x5d, 0xe6,
	0xc4, 0xa3, 0x68, 0xdd, 0xe5, 0x0d, 0x3f, 0x89, 0x05, 0x44, 0xc9, 0x7a, 0xe8, 0x51, 0x07, 0xd6,
	0xd3, 0x27, 0x79, 0xbf, 0xd5, 0xc3, 0x88, 0x0b, 0x4e, 0xee, 0x4c, 0xff, 0xa9, 0xad, 0x4d, 0x8f,
	0xe0, 0x52, 0x01, 0x07, 0x34, 0x69, 0xf0, 0x50, 0x30, 0x1e, 0xc4, 0x93, 0xbf, 0x99, 0xa2, 0xf6,
	0x14, 0x0d, 0xc3, 0xbc, 0x01, 0xf7, 0xb2, 0xe7, 0x1b, 0xff, 0x9e, 0xc0, 0xc7, 0x76, 0xb2, 0xc7,
	0xbb, 0xea, 0xe9, 0xb6, 0x7a, 0x48, 0xde, 0xc6, 0x0f, 0x6e, 0xb3, 0x58, 0xb4, 0x1d, 0x08, 0x68,
	0xc4, 0xf8, 0x56, 0xb0, 0xcf, 0xc9, 0xa9, 0xba, 0x4b, 0xeb, 0xe3, 0x91, 0xf6, 0xd2, 0x91, 0xf6,
	0xb2, 0x68, 0xf3, 0x64, 0x0b, 0xde, 0xaa, 0x9d, 0x2e, 0x0f, 0xc7, 0xe1, 0xb3, 0xf7, 0xfc, 0x77,
	0xf3, 0xb0, 0x7e, 0xc7, 0xdd, 0x9f, 0x4a, 0x44, 0x86, 0xf8, 0x01, 0x1b, 0x66, 0x8d, 0x4f, 0x16,
	0x8d, 0x35, 0x07, 0x2a, 0xdf, 0x53, 0xa5, 0x59, 0x6d, 0xfb, 0x99, 0x44, 0xc4, 0xc3, 0xf7, 0xda,
	0x20, 0xd4, 0xcb, 0x07, 0x1d, 0x1a, 0x0f, 0xc8, 0x73, 0x86, 0x71, 0x8e, 0x28, 0x65, 0xf8, 0x7c,
	0x39, 0x50, 0xbb, 0x7d, 0x2e, 0x11, 0x79, 0xcf, 0xc2, 0xc7, 0x5b, 0xe0, 0x00, 0x1b, 0xc1, 0x11,
	0x73, 0xf5, 0x80, 0x46, 0x3d, 0x72, 0xa6, 0x68, 0xb8, 0x5c, 0x5c, 0x05, 0x38, 0x5b, 0x51, 0xa1,
	0x93, 0x7c, 0x21, 0x11, 0x89, 0xf0, 0xfd, 0x36, 0x88, 0x4d, 0xea, 0xd1, 0xc0, 0x81, 0x74, 0xb6,
	0xd7, 0x0c, 0x2f, 0x34, 0xc5, 0x29, 0xeb, 0x93, 0x65, 0x51, 0xed, 0xf9, 0xa5, 0x44, 0xe4, 0x1d,
	0x7c, 0xac, 0xd9, 0x07, 0x67, 0x30, 0x59, 0x8f, 0x16, 0xe7, 0x3e, 0x29, 0xfc, 0x60, 0x16, 0x50,
	0xe5, 0xbc, 0x5e, 0x81, 0xd6, 0xe6, 0x5f, 0x49, 0x44, 0x38, 0xbe, 0x6f, 0x2b, 0x18, 0x31, 0x01,
	0x1b, 0x3b, 0x49, 0x6a, 0x5c, 0xb8, 0x80, 0x33, 0x98, 0x32, 0x5d, 0x2b, 0x49, 0x6a, 0xc3, 0xaf,
	0x25, 0x22, 0xef, 0x5b, 0xf8, 0x11, 0x1b, 0xc4, 0x3c, 0x33, 0xf4, 0x04, 0x39, 0x6b, 0x98, 0xbf,
	0x1c, 0x5e, 0x65, 0xd8, 0xa8, 0x2a, 0xd1, 0x61, 0xbe, 0x91, 0x88, 0xdc, 0xb0, 0xf0, 0x33, 0x6a,
	0x07, 0xb6, 0xc0, 0xe1, 0xbe, 0x0f, 0x41, 0x0f, 0x7a, 0x93, 0xc9, 0x6a, 0x33, 0x3f, 0xf4, 0xb2,
	0x4f, 0xe0, 0xb2, 0x69, 0xf3, 0x1a, 0xa5, 0x2a, 0xe2, 0x95, 0xdb, 0x50, 0xeb, 0xb4, 0xd7, 0x25,
	0x22, 0xd7, 0x2d, 0xfc, 0xb4, 0x0d, 0x79, 0x9a, 0x97, 0x41, 0x50, 0xe6, 0xa5, 0x61, 0x5f, 0x32,
	0xcc, 0x88, 0x51, 0xa9, 0xb2, 0x5e, 0x5e, 0x5d, 0xac, 0xa3, 0x7e, 0x2b, 0x11, 0xf9, 0xc0, 0xc2,
	0x8f, 0x2a, 0x0d, 0xe7, 0x7e, 0xbb, 0x4f, 0x23, 0xd8, 0x66, 0xc1, 0x60, 0x33, 0xe9, 0xd0, 0xee,
	0x56, 0x8f, 0x98, 0xd6, 0x2c, 0x4f, 0xa0, 0x82, 0x9d, 0xab, 0xac, 0xd1, 0x79, 0xbe, 0x1b, 0xe7,
	0x99, 0x2e, 0xeb, 0xcd, 0x3e, 0x0d, 0x02, 0xf0, 0xd4, 0x12, 0x14, 0xe7, 0x29, 0x10, 0x18, 0xf3,
	0x14, 0x6a, 0x74, 0x9e, 0x1b, 0x12, 0x91, 0x77, 0x2d, 0xfc, 0xb0, 0x0d, 0xa2, 0x99, 0xce, 0xa8,
	0xd8, 0xa5, 0x2e, 0xec, 0xd2, 0x88, 0xfa, 0x31, 0x69, 0x18, 0x5e, 0x74, 0x81, 0x56, 0x49, 0xce,
	0x54, 0x13, 0xe8, 0x18, 0xdf, 0x8f, 0x4b, 0x4f, 0xc6, 0x74, 0xf8, 0x64, 0x51, 0x0d, 0xa5, 0x67,
	0x1e, 0x35, 0x97, 0x9e, 0x45, 0x5a, 0x9b, 0xff, 0x90, 0x99, 0xdb, 0x20, 0x5e, 0x83, 0x83, 0x09,
	0xd0, 0x61, 0x61, 0xb1, 0xf9, 0x02, 0x6a, 0x34, 0xcf, 0xa1, 0xb5, 0xf9, 0x8f, 0xe3, 0x32, 0xb4,
	0x43, 0xa3, 0xc1, 0x3c, 0x45, 0x7b, 0xc5, 0x65, 0x28, 0x9f, 0x37, 0x96, 0xa1, 0x22, 0x89, 0x0e,
	0xf3, 0x93, 0x44, 0xe4, 0x43, 0x0b, 0x3f, 0x36, 0xf3, 0xe1, 0x78, 0x0c, 0x02, 0xd1, 0xe4, 0xc1,
	0x3e, 0x73, 0x49, 0xb9, 0x4f, 0x6d, 0x4a, 0xa1, 0x02, 0x9d, 0xaf, 0x2e, 0xd2, 0x91, 0x7e, 0x96,
	0x88, 0x48, 0x0b, 0x3f, 0x61, 0x83, 0xd8, 0xf5, 0x68, 0xa2, 0xf7, 0xbb, 0x2a, 0x01, 0x51, 0x2f,
	0xdd, 0x34, 0x2f, 0x18, 0x66, 0xbe, 0x48, 0xa4, 0x82, 0xbd, 0xb8, 0x92, 0x4e, 0x67, 0xfb, 0x45,
	0x22, 0xf2, 0x91, 0x85, 0x1f, 0x9f, 0xea, 0x5f, 0x9a, 0x7d, 0x1a, 0x0a, 0x88, 0xda, 0x43, 0xdf,
	0xa7, 0x51, 0x42, 0xce, 0x97, 0x68, 0x79, 0x66, 0x25, 0x2a, 0xd7, 0x85, 0x15, 0x54, 0x3a, 0xd5,
	0xaf, 0xe3, 0xb3, 0xa4, 0x9d, 0xff, 0x12, 0x6f, 0xb0, 0x98, 0x75, 0x99, 0xc7, 0x44, 0x52, 0x7c,
	0x96, 0x2c, 0x95, 0x1a, 0xcf, 0x92, 0x12, 0x6a, 0x9d, 0xf6, 0x50, 0x22, 0xf2, 0x89, 0x85, 0x4f,
	0x14, 0x4c, 0x79, 0x56, 0xd5, 0xc9, 0xc5, 0x8a, 0x2b, 0x95, 0xc9, 0x54, 0xca, 0x4b, 0x2b, 0x2a,
	0x75, 0xc2, 0xdf, 0x24, 0x52, 0x3d, 0xb7, 0x2a, 0xee, 0x8e, 0xdf, 0xdb, 0x61, 0xaf, 0xb2, 0xc0,
	0xed, 0xd0, 0x2e, 0x31, 0xb5, 0xb3, 0x33, 0xa4, 0xb1, 0xe7, 0x5e, 0x84, 0xb5, 0xf3, 0xef, 0xe6,
	0xae, 0x60, 0xea, 0xa0, 0xad, 0xda, 0x15, 0xcc, 0x9e, 0xb4, 0x57, 0x6e, 0x43, 0xad, 0xd3, 0xfe,
	0x21, 0x11, 0xf9, 0xd8, 0xc2, 0x4f, 0x6e, 0xc5, 0xaf, 0xc7, 0x10, 0x5d, 0xa3, 0xf1, 0x04, 0x7f,
	0x25, 0x02, 0x68, 0xf2, 0x61, 0xc8, 0x83, 0x98, 0x14, 0x6e, 0x39, 0x93, 0x4a, 0x65, 0xbc, 0xb8,
	0x9a, 0x50, 0xc7, 0xfb, 0x33, 0xbb, 0xc0, 0x5c, 0xe3, 0x3e, 0xa8, 0x33, 0x68, 0x93, 0xb9, 0x1d,
	0xbe, 0x3f, 0x2c, 0xbe, 0xc0, 0xcc, 0x81, 0xc6, 0x0b, 0xcc, 0x02, 0xab, 0x6d, 0xff, 0xca, 0x0e,
	0x97, 0x09, 0xd1, 0x62, 0x6e, 0x5f, 0xa4, 0xc6, 0xa7, 0x97, 0x0d, 0x76, 0x84, 0x1a, 0x0f, 0x97,
	0x1c, 0x5a, 0x9b, 0xff, 0xad, 0x7b, 0xdc, 0xf1, 0xf9, 0x3f, 0x73, 0x79, 0x33, 0xf5, 0xb8, 0x39,
	0xfc, 0xb2, 0x1e, 0x37, 0x57, 0xa2, 0xc3, 0xfc, 0x23, 0x51, 0xad, 0xf6, 0xff, 0xcd, 0xc3, 0xfa,
	0x71, 0xfc, 0x50, 0xce, 0xa5, 0x77, 0xb3, 0xf9, 0xe6, 0x55, 0x97, 0x7b, 0x34, 0x70, 0xeb, 0x17,
	0x36, 0x84, 0xa8, 0x3b, 0xdc, 0x6f, 0xa4, 0xd7, 0x60, 0x87, 0x7b, 0x8d, 0x18, 0xa2, 0x11, 0x73,
	0x20, 0x6e, 0x2c, 0xb9, 0x9e, 0x77, 0xef, 0x4a, 0x25, 0xe7, 0x6e, 0x05, 0x00, 0x00, 0xff, 0xff,
	0x21, 0xaa, 0xcc, 0xe8, 0xc8, 0x0f, 0x00, 0x00,
}
