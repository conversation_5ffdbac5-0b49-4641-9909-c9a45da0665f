// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/emoji_logic/emoji_logic.proto

package logic

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import emoji "golang.52tt.com/protocol/app/emoji"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EmojiLogicClient is the client API for EmojiLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EmojiLogicClient interface {
	SaveEmoji(ctx context.Context, in *emoji.SaveEmojiReq, opts ...grpc.CallOption) (*emoji.SaveEmojiResp, error)
	DeleteEmoji(ctx context.Context, in *emoji.DeleteEmojiReq, opts ...grpc.CallOption) (*emoji.DeleteEmojiResp, error)
	GetEmojiPkgList(ctx context.Context, in *emoji.GetEmojiPkgListReq, opts ...grpc.CallOption) (*emoji.GetEmojiPkgListResp, error)
	GetEmojiListByPkg(ctx context.Context, in *emoji.GetEmojiListByPkgReq, opts ...grpc.CallOption) (*emoji.GetEmojiListByPkgResp, error)
	TestEmojiA(ctx context.Context, in *emoji.GetEmojiPkgListReq, opts ...grpc.CallOption) (*emoji.GetEmojiPkgListResp, error)
}

type emojiLogicClient struct {
	cc *grpc.ClientConn
}

func NewEmojiLogicClient(cc *grpc.ClientConn) EmojiLogicClient {
	return &emojiLogicClient{cc}
}

func (c *emojiLogicClient) SaveEmoji(ctx context.Context, in *emoji.SaveEmojiReq, opts ...grpc.CallOption) (*emoji.SaveEmojiResp, error) {
	out := new(emoji.SaveEmojiResp)
	err := c.cc.Invoke(ctx, "/logic.EmojiLogic/SaveEmoji", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emojiLogicClient) DeleteEmoji(ctx context.Context, in *emoji.DeleteEmojiReq, opts ...grpc.CallOption) (*emoji.DeleteEmojiResp, error) {
	out := new(emoji.DeleteEmojiResp)
	err := c.cc.Invoke(ctx, "/logic.EmojiLogic/DeleteEmoji", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emojiLogicClient) GetEmojiPkgList(ctx context.Context, in *emoji.GetEmojiPkgListReq, opts ...grpc.CallOption) (*emoji.GetEmojiPkgListResp, error) {
	out := new(emoji.GetEmojiPkgListResp)
	err := c.cc.Invoke(ctx, "/logic.EmojiLogic/GetEmojiPkgList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emojiLogicClient) GetEmojiListByPkg(ctx context.Context, in *emoji.GetEmojiListByPkgReq, opts ...grpc.CallOption) (*emoji.GetEmojiListByPkgResp, error) {
	out := new(emoji.GetEmojiListByPkgResp)
	err := c.cc.Invoke(ctx, "/logic.EmojiLogic/GetEmojiListByPkg", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emojiLogicClient) TestEmojiA(ctx context.Context, in *emoji.GetEmojiPkgListReq, opts ...grpc.CallOption) (*emoji.GetEmojiPkgListResp, error) {
	out := new(emoji.GetEmojiPkgListResp)
	err := c.cc.Invoke(ctx, "/logic.EmojiLogic/TestEmojiA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmojiLogicServer is the server API for EmojiLogic service.
type EmojiLogicServer interface {
	SaveEmoji(context.Context, *emoji.SaveEmojiReq) (*emoji.SaveEmojiResp, error)
	DeleteEmoji(context.Context, *emoji.DeleteEmojiReq) (*emoji.DeleteEmojiResp, error)
	GetEmojiPkgList(context.Context, *emoji.GetEmojiPkgListReq) (*emoji.GetEmojiPkgListResp, error)
	GetEmojiListByPkg(context.Context, *emoji.GetEmojiListByPkgReq) (*emoji.GetEmojiListByPkgResp, error)
	TestEmojiA(context.Context, *emoji.GetEmojiPkgListReq) (*emoji.GetEmojiPkgListResp, error)
}

func RegisterEmojiLogicServer(s *grpc.Server, srv EmojiLogicServer) {
	s.RegisterService(&_EmojiLogic_serviceDesc, srv)
}

func _EmojiLogic_SaveEmoji_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emoji.SaveEmojiReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmojiLogicServer).SaveEmoji(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.EmojiLogic/SaveEmoji",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmojiLogicServer).SaveEmoji(ctx, req.(*emoji.SaveEmojiReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmojiLogic_DeleteEmoji_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emoji.DeleteEmojiReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmojiLogicServer).DeleteEmoji(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.EmojiLogic/DeleteEmoji",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmojiLogicServer).DeleteEmoji(ctx, req.(*emoji.DeleteEmojiReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmojiLogic_GetEmojiPkgList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emoji.GetEmojiPkgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmojiLogicServer).GetEmojiPkgList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.EmojiLogic/GetEmojiPkgList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmojiLogicServer).GetEmojiPkgList(ctx, req.(*emoji.GetEmojiPkgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmojiLogic_GetEmojiListByPkg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emoji.GetEmojiListByPkgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmojiLogicServer).GetEmojiListByPkg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.EmojiLogic/GetEmojiListByPkg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmojiLogicServer).GetEmojiListByPkg(ctx, req.(*emoji.GetEmojiListByPkgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmojiLogic_TestEmojiA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emoji.GetEmojiPkgListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmojiLogicServer).TestEmojiA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/logic.EmojiLogic/TestEmojiA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmojiLogicServer).TestEmojiA(ctx, req.(*emoji.GetEmojiPkgListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _EmojiLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.EmojiLogic",
	HandlerType: (*EmojiLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SaveEmoji",
			Handler:    _EmojiLogic_SaveEmoji_Handler,
		},
		{
			MethodName: "DeleteEmoji",
			Handler:    _EmojiLogic_DeleteEmoji_Handler,
		},
		{
			MethodName: "GetEmojiPkgList",
			Handler:    _EmojiLogic_GetEmojiPkgList_Handler,
		},
		{
			MethodName: "GetEmojiListByPkg",
			Handler:    _EmojiLogic_GetEmojiListByPkg_Handler,
		},
		{
			MethodName: "TestEmojiA",
			Handler:    _EmojiLogic_TestEmojiA_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/emoji_logic/emoji_logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/emoji_logic/emoji_logic.proto", fileDescriptor_emoji_logic_a04c31d85c71c1b5)
}

var fileDescriptor_emoji_logic_a04c31d85c71c1b5 = []byte{
	// 257 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xd2, 0xcc, 0xc9, 0x4f, 0xcf,
	0x4c, 0x2e, 0x2e, 0x2b, 0xd2, 0x4d, 0xcf, 0xd7, 0x4f, 0xcd, 0xcd, 0xcf, 0xca, 0x8c, 0x07, 0x8b,
	0x20, 0xb3, 0xf5, 0x0a, 0x8a, 0xf2, 0x4b, 0xf2, 0x85, 0x58, 0xc1, 0x1c, 0x29, 0x14, 0x1d, 0xe9,
	0x89, 0x25, 0xa9, 0xe5, 0x89, 0x95, 0xfa, 0xf9, 0x05, 0x25, 0x99, 0xf9, 0x79, 0xc5, 0x30, 0x1a,
	0xa2, 0x43, 0x4a, 0x20, 0xb1, 0xa0, 0x00, 0x6a, 0x10, 0x44, 0xc4, 0xa8, 0x93, 0x99, 0x8b, 0xcb,
	0x15, 0x24, 0xe0, 0x03, 0x32, 0x44, 0xc8, 0x8a, 0x8b, 0x33, 0x38, 0xb1, 0x2c, 0x15, 0x2c, 0x22,
	0x24, 0xa0, 0x97, 0x9e, 0xa8, 0x07, 0xe7, 0x06, 0xa5, 0x16, 0x4a, 0x09, 0xa2, 0x89, 0x14, 0x17,
	0x28, 0x71, 0x7c, 0x3a, 0xbf, 0x53, 0x8f, 0x99, 0xe3, 0x88, 0xb0, 0x90, 0x03, 0x17, 0xb7, 0x4b,
	0x6a, 0x4e, 0x6a, 0x09, 0x54, 0xb7, 0x10, 0x48, 0x2d, 0x92, 0x00, 0x48, 0xbf, 0x30, 0x86, 0x18,
	0xc2, 0x84, 0xa3, 0xc2, 0x42, 0x3e, 0x5c, 0xfc, 0xee, 0xa9, 0x25, 0x60, 0x99, 0x80, 0xec, 0x74,
	0x9f, 0xcc, 0xe2, 0x12, 0x21, 0x31, 0x90, 0x0e, 0x34, 0x41, 0x90, 0x49, 0xe2, 0x58, 0xc5, 0x11,
	0xa6, 0x1d, 0x13, 0x16, 0x0a, 0xe2, 0x12, 0x84, 0x29, 0x00, 0xc9, 0x3a, 0x55, 0x06, 0x64, 0xa7,
	0x0b, 0x49, 0x20, 0xeb, 0x83, 0x0b, 0x83, 0x4c, 0x94, 0xc4, 0x21, 0x83, 0x30, 0xf3, 0xb8, 0xb0,
	0x90, 0x3b, 0x17, 0x57, 0x48, 0x6a, 0x31, 0x44, 0x8d, 0x23, 0xd9, 0x8e, 0xbb, 0x27, 0x2e, 0x25,
	0xf4, 0xeb, 0xfc, 0x4e, 0x3d, 0x5e, 0x2e, 0x6e, 0x70, 0x64, 0xe8, 0x82, 0x63, 0x30, 0x89, 0x0d,
	0x1c, 0x25, 0xc6, 0x80, 0x00, 0x00, 0x00, 0xff, 0xff, 0x45, 0x73, 0xbb, 0x60, 0x03, 0x02, 0x00,
	0x00,
}
