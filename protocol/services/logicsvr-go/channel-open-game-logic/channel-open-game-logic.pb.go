// Code generated by protoc-gen-go. DO NOT EDIT.
// source: logicsvr-go/channel-open-game-logic/channel-open-game-logic.proto

package channel_open_game_logic // import "golang.52tt.com/protocol/services/logicsvr-go/channel-open-game-logic"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import channel_open_game "golang.52tt.com/protocol/app/channel-open-game"
import _ "golang.52tt.com/protocol/services/logicsvr-go/gateway/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelOpenGameLogicClient is the client API for ChannelOpenGameLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelOpenGameLogicClient interface {
	// 获取房间支持游戏列表
	GetChannelSupportGameList(ctx context.Context, in *channel_open_game.GetChannelSupportGameListReq, opts ...grpc.CallOption) (*channel_open_game.GetChannelSupportGameListResp, error)
	// 加载房间玩法
	SetChannelLoadingGame(ctx context.Context, in *channel_open_game.SetChannelLoadingGameReq, opts ...grpc.CallOption) (*channel_open_game.SetChannelLoadingGameResp, error)
	// 获取房间玩法
	GetChannelLoadingGame(ctx context.Context, in *channel_open_game.GetChannelLoadingGameReq, opts ...grpc.CallOption) (*channel_open_game.GetChannelLoadingGameResp, error)
	// 设置游戏玩家角色
	SubmitGameCenterCmd(ctx context.Context, in *channel_open_game.SubmitGameCenterCmdReq, opts ...grpc.CallOption) (*channel_open_game.SubmitGameCenterCmdResp, error)
	// 获取活动奖励
	GetGameActivityReward(ctx context.Context, in *channel_open_game.GetGameActivityRewardReq, opts ...grpc.CallOption) (*channel_open_game.GetGameActivityRewardResp, error)
	// 活动分享上报
	ReportGameActivityShared(ctx context.Context, in *channel_open_game.ReportGameActivitySharedReq, opts ...grpc.CallOption) (*channel_open_game.ReportGameActivitySharedResp, error)
	// 上报加入游戏
	JoinChannelGameBegin(ctx context.Context, in *channel_open_game.JoinChannelGameBeginReq, opts ...grpc.CallOption) (*channel_open_game.JoinChannelGameBeginResp, error)
	// 上报可以开始游戏
	JoinChannelGameEnd(ctx context.Context, in *channel_open_game.JoinChannelGameEndReq, opts ...grpc.CallOption) (*channel_open_game.JoinChannelGameEndResp, error)
	QueryChannelGameTick(ctx context.Context, in *channel_open_game.QueryChannelGameTickReq, opts ...grpc.CallOption) (*channel_open_game.QueryChannelGameTickResp, error)
	GetChannelGameBaseInfo(ctx context.Context, in *channel_open_game.GetChannelGameBaseInfoRequest, opts ...grpc.CallOption) (*channel_open_game.GetChannelGameBaseInfoResponse, error)
}

type channelOpenGameLogicClient struct {
	cc *grpc.ClientConn
}

func NewChannelOpenGameLogicClient(cc *grpc.ClientConn) ChannelOpenGameLogicClient {
	return &channelOpenGameLogicClient{cc}
}

func (c *channelOpenGameLogicClient) GetChannelSupportGameList(ctx context.Context, in *channel_open_game.GetChannelSupportGameListReq, opts ...grpc.CallOption) (*channel_open_game.GetChannelSupportGameListResp, error) {
	out := new(channel_open_game.GetChannelSupportGameListResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/GetChannelSupportGameList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) SetChannelLoadingGame(ctx context.Context, in *channel_open_game.SetChannelLoadingGameReq, opts ...grpc.CallOption) (*channel_open_game.SetChannelLoadingGameResp, error) {
	out := new(channel_open_game.SetChannelLoadingGameResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/SetChannelLoadingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) GetChannelLoadingGame(ctx context.Context, in *channel_open_game.GetChannelLoadingGameReq, opts ...grpc.CallOption) (*channel_open_game.GetChannelLoadingGameResp, error) {
	out := new(channel_open_game.GetChannelLoadingGameResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/GetChannelLoadingGame", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) SubmitGameCenterCmd(ctx context.Context, in *channel_open_game.SubmitGameCenterCmdReq, opts ...grpc.CallOption) (*channel_open_game.SubmitGameCenterCmdResp, error) {
	out := new(channel_open_game.SubmitGameCenterCmdResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/SubmitGameCenterCmd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) GetGameActivityReward(ctx context.Context, in *channel_open_game.GetGameActivityRewardReq, opts ...grpc.CallOption) (*channel_open_game.GetGameActivityRewardResp, error) {
	out := new(channel_open_game.GetGameActivityRewardResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/GetGameActivityReward", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) ReportGameActivityShared(ctx context.Context, in *channel_open_game.ReportGameActivitySharedReq, opts ...grpc.CallOption) (*channel_open_game.ReportGameActivitySharedResp, error) {
	out := new(channel_open_game.ReportGameActivitySharedResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/ReportGameActivityShared", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) JoinChannelGameBegin(ctx context.Context, in *channel_open_game.JoinChannelGameBeginReq, opts ...grpc.CallOption) (*channel_open_game.JoinChannelGameBeginResp, error) {
	out := new(channel_open_game.JoinChannelGameBeginResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/JoinChannelGameBegin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) JoinChannelGameEnd(ctx context.Context, in *channel_open_game.JoinChannelGameEndReq, opts ...grpc.CallOption) (*channel_open_game.JoinChannelGameEndResp, error) {
	out := new(channel_open_game.JoinChannelGameEndResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/JoinChannelGameEnd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) QueryChannelGameTick(ctx context.Context, in *channel_open_game.QueryChannelGameTickReq, opts ...grpc.CallOption) (*channel_open_game.QueryChannelGameTickResp, error) {
	out := new(channel_open_game.QueryChannelGameTickResp)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/QueryChannelGameTick", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelOpenGameLogicClient) GetChannelGameBaseInfo(ctx context.Context, in *channel_open_game.GetChannelGameBaseInfoRequest, opts ...grpc.CallOption) (*channel_open_game.GetChannelGameBaseInfoResponse, error) {
	out := new(channel_open_game.GetChannelGameBaseInfoResponse)
	err := c.cc.Invoke(ctx, "/channel_open_game_logic.ChannelOpenGameLogic/GetChannelGameBaseInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelOpenGameLogicServer is the server API for ChannelOpenGameLogic service.
type ChannelOpenGameLogicServer interface {
	// 获取房间支持游戏列表
	GetChannelSupportGameList(context.Context, *channel_open_game.GetChannelSupportGameListReq) (*channel_open_game.GetChannelSupportGameListResp, error)
	// 加载房间玩法
	SetChannelLoadingGame(context.Context, *channel_open_game.SetChannelLoadingGameReq) (*channel_open_game.SetChannelLoadingGameResp, error)
	// 获取房间玩法
	GetChannelLoadingGame(context.Context, *channel_open_game.GetChannelLoadingGameReq) (*channel_open_game.GetChannelLoadingGameResp, error)
	// 设置游戏玩家角色
	SubmitGameCenterCmd(context.Context, *channel_open_game.SubmitGameCenterCmdReq) (*channel_open_game.SubmitGameCenterCmdResp, error)
	// 获取活动奖励
	GetGameActivityReward(context.Context, *channel_open_game.GetGameActivityRewardReq) (*channel_open_game.GetGameActivityRewardResp, error)
	// 活动分享上报
	ReportGameActivityShared(context.Context, *channel_open_game.ReportGameActivitySharedReq) (*channel_open_game.ReportGameActivitySharedResp, error)
	// 上报加入游戏
	JoinChannelGameBegin(context.Context, *channel_open_game.JoinChannelGameBeginReq) (*channel_open_game.JoinChannelGameBeginResp, error)
	// 上报可以开始游戏
	JoinChannelGameEnd(context.Context, *channel_open_game.JoinChannelGameEndReq) (*channel_open_game.JoinChannelGameEndResp, error)
	QueryChannelGameTick(context.Context, *channel_open_game.QueryChannelGameTickReq) (*channel_open_game.QueryChannelGameTickResp, error)
	GetChannelGameBaseInfo(context.Context, *channel_open_game.GetChannelGameBaseInfoRequest) (*channel_open_game.GetChannelGameBaseInfoResponse, error)
}

func RegisterChannelOpenGameLogicServer(s *grpc.Server, srv ChannelOpenGameLogicServer) {
	s.RegisterService(&_ChannelOpenGameLogic_serviceDesc, srv)
}

func _ChannelOpenGameLogic_GetChannelSupportGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.GetChannelSupportGameListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).GetChannelSupportGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/GetChannelSupportGameList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).GetChannelSupportGameList(ctx, req.(*channel_open_game.GetChannelSupportGameListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_SetChannelLoadingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.SetChannelLoadingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).SetChannelLoadingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/SetChannelLoadingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).SetChannelLoadingGame(ctx, req.(*channel_open_game.SetChannelLoadingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_GetChannelLoadingGame_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.GetChannelLoadingGameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).GetChannelLoadingGame(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/GetChannelLoadingGame",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).GetChannelLoadingGame(ctx, req.(*channel_open_game.GetChannelLoadingGameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_SubmitGameCenterCmd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.SubmitGameCenterCmdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).SubmitGameCenterCmd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/SubmitGameCenterCmd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).SubmitGameCenterCmd(ctx, req.(*channel_open_game.SubmitGameCenterCmdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_GetGameActivityReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.GetGameActivityRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).GetGameActivityReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/GetGameActivityReward",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).GetGameActivityReward(ctx, req.(*channel_open_game.GetGameActivityRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_ReportGameActivityShared_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.ReportGameActivitySharedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).ReportGameActivityShared(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/ReportGameActivityShared",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).ReportGameActivityShared(ctx, req.(*channel_open_game.ReportGameActivitySharedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_JoinChannelGameBegin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.JoinChannelGameBeginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).JoinChannelGameBegin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/JoinChannelGameBegin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).JoinChannelGameBegin(ctx, req.(*channel_open_game.JoinChannelGameBeginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_JoinChannelGameEnd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.JoinChannelGameEndReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).JoinChannelGameEnd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/JoinChannelGameEnd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).JoinChannelGameEnd(ctx, req.(*channel_open_game.JoinChannelGameEndReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_QueryChannelGameTick_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.QueryChannelGameTickReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).QueryChannelGameTick(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/QueryChannelGameTick",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).QueryChannelGameTick(ctx, req.(*channel_open_game.QueryChannelGameTickReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelOpenGameLogic_GetChannelGameBaseInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(channel_open_game.GetChannelGameBaseInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelOpenGameLogicServer).GetChannelGameBaseInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_open_game_logic.ChannelOpenGameLogic/GetChannelGameBaseInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelOpenGameLogicServer).GetChannelGameBaseInfo(ctx, req.(*channel_open_game.GetChannelGameBaseInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelOpenGameLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_open_game_logic.ChannelOpenGameLogic",
	HandlerType: (*ChannelOpenGameLogicServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelSupportGameList",
			Handler:    _ChannelOpenGameLogic_GetChannelSupportGameList_Handler,
		},
		{
			MethodName: "SetChannelLoadingGame",
			Handler:    _ChannelOpenGameLogic_SetChannelLoadingGame_Handler,
		},
		{
			MethodName: "GetChannelLoadingGame",
			Handler:    _ChannelOpenGameLogic_GetChannelLoadingGame_Handler,
		},
		{
			MethodName: "SubmitGameCenterCmd",
			Handler:    _ChannelOpenGameLogic_SubmitGameCenterCmd_Handler,
		},
		{
			MethodName: "GetGameActivityReward",
			Handler:    _ChannelOpenGameLogic_GetGameActivityReward_Handler,
		},
		{
			MethodName: "ReportGameActivityShared",
			Handler:    _ChannelOpenGameLogic_ReportGameActivityShared_Handler,
		},
		{
			MethodName: "JoinChannelGameBegin",
			Handler:    _ChannelOpenGameLogic_JoinChannelGameBegin_Handler,
		},
		{
			MethodName: "JoinChannelGameEnd",
			Handler:    _ChannelOpenGameLogic_JoinChannelGameEnd_Handler,
		},
		{
			MethodName: "QueryChannelGameTick",
			Handler:    _ChannelOpenGameLogic_QueryChannelGameTick_Handler,
		},
		{
			MethodName: "GetChannelGameBaseInfo",
			Handler:    _ChannelOpenGameLogic_GetChannelGameBaseInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "logicsvr-go/channel-open-game-logic/channel-open-game-logic.proto",
}

func init() {
	proto.RegisterFile("logicsvr-go/channel-open-game-logic/channel-open-game-logic.proto", fileDescriptor_channel_open_game_logic_7953b8257cba95ae)
}

var fileDescriptor_channel_open_game_logic_7953b8257cba95ae = []byte{
	// 503 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x95, 0x4d, 0x6f, 0xd3, 0x30,
	0x18, 0xc7, 0x65, 0x81, 0x10, 0xf8, 0x68, 0x06, 0x63, 0x39, 0x70, 0xd8, 0x0d, 0xb1, 0x38, 0xa2,
	0x83, 0x0f, 0xb0, 0x55, 0x55, 0x04, 0x9a, 0x84, 0x68, 0x39, 0x71, 0xa9, 0xbc, 0xe4, 0xc1, 0xb3,
	0x68, 0x6c, 0x2f, 0x76, 0x3b, 0x15, 0x09, 0x09, 0x89, 0x2b, 0x2f, 0x5f, 0x84, 0x2f, 0xb0, 0x4f,
	0xb1, 0xef, 0x91, 0x23, 0x07, 0x40, 0x1b, 0x6f, 0x72, 0x9a, 0xe2, 0x6a, 0x4d, 0xba, 0xe6, 0x54,
	0xa9, 0xff, 0xdf, 0xf3, 0xf8, 0xf7, 0x3c, 0x6d, 0x1c, 0xbc, 0x37, 0x52, 0x5c, 0x24, 0x66, 0x92,
	0x87, 0x5c, 0x45, 0xc9, 0x11, 0x93, 0x12, 0x46, 0xa1, 0xd2, 0x20, 0x43, 0xce, 0x32, 0x08, 0xcb,
	0xb4, 0xe9, 0x7b, 0xaa, 0x73, 0x65, 0x15, 0xd9, 0xac, 0xe2, 0xa1, 0x8b, 0x87, 0x2e, 0x1e, 0x96,
	0x71, 0xf0, 0x60, 0xb1, 0x37, 0x67, 0x16, 0x4e, 0xd8, 0x34, 0x52, 0xda, 0x0a, 0x25, 0xcd, 0xfc,
	0x73, 0xd6, 0x23, 0xd8, 0x66, 0x5a, 0x37, 0x1d, 0x33, 0x9c, 0x31, 0x9d, 0x0b, 0x8c, 0x37, 0xba,
	0x33, 0xe4, 0xb9, 0x06, 0x19, 0xb3, 0x0c, 0x0e, 0x5c, 0x4e, 0xbe, 0x20, 0xbc, 0x15, 0x83, 0xad,
	0xb2, 0xc1, 0x58, 0x6b, 0x95, 0xdb, 0x32, 0x16, 0xc6, 0x92, 0x0e, 0xe5, 0x8c, 0x2e, 0x29, 0xd2,
	0xc6, 0x82, 0x3e, 0x1c, 0x07, 0xbb, 0xad, 0x6b, 0x8c, 0xde, 0xbe, 0xf5, 0xed, 0xec, 0x94, 0x5e,
	0xbf, 0xf9, 0xbd, 0x40, 0xe4, 0x03, 0xc2, 0x77, 0x06, 0xff, 0xe1, 0x03, 0xc5, 0x52, 0x21, 0xb9,
	0x83, 0x09, 0xad, 0xef, 0x5c, 0x0b, 0x3b, 0x93, 0xa8, 0x15, 0xef, 0x2d, 0x7e, 0x54, 0x16, 0x71,
	0x1b, 0x8b, 0xb8, 0xa5, 0x45, 0x7c, 0x95, 0xc5, 0xcf, 0x02, 0x91, 0x77, 0xf8, 0xf6, 0x60, 0x7c,
	0x98, 0x89, 0x72, 0x59, 0x5d, 0x90, 0x16, 0xf2, 0x6e, 0x96, 0x92, 0x9d, 0x86, 0xc1, 0x96, 0x51,
	0x27, 0x10, 0xb6, 0xa0, 0xfd, 0xf1, 0xe7, 0x7e, 0x09, 0x8e, 0xd9, 0x4b, 0xac, 0x98, 0x08, 0x3b,
	0xed, 0xc3, 0x09, 0xcb, 0xd3, 0x15, 0x4b, 0x58, 0x86, 0x57, 0x2f, 0xa1, 0x8e, 0xf7, 0x16, 0x17,
	0x05, 0x22, 0x1f, 0x11, 0xbe, 0xd7, 0x87, 0xf9, 0x5f, 0x66, 0xce, 0x0e, 0x8e, 0x58, 0x0e, 0x29,
	0x79, 0x54, 0xdf, 0xb8, 0x89, 0x77, 0x2e, 0x9d, 0xb6, 0x25, 0x5e, 0xe7, 0x57, 0x81, 0xc8, 0x7b,
	0x84, 0x37, 0x9e, 0x29, 0x21, 0xab, 0x5f, 0xcf, 0x15, 0xec, 0x03, 0x17, 0x92, 0x34, 0xec, 0xb9,
	0x8e, 0x75, 0x1a, 0xb4, 0x0d, 0xee, 0x15, 0x7e, 0x17, 0x88, 0xbc, 0xc5, 0xe4, 0x12, 0xd6, 0x93,
	0x29, 0x79, 0xb8, 0x56, 0xc3, 0x9e, 0x2c, 0x97, 0xb0, 0xb3, 0x3e, 0xec, 0xcf, 0xfe, 0x53, 0x8d,
	0xff, 0x62, 0x0c, 0xf9, 0x74, 0x01, 0x7b, 0x29, 0x92, 0x37, 0x4d, 0xe3, 0xd7, 0xb1, 0x2b, 0xc6,
	0xaf, 0xc7, 0xbd, 0xc2, 0xdf, 0x02, 0x91, 0xcf, 0x08, 0xdf, 0xf5, 0x8f, 0x4f, 0xb9, 0x25, 0x66,
	0xe0, 0xa9, 0x7c, 0xad, 0xc8, 0x95, 0x97, 0xcf, 0x22, 0xdd, 0x87, 0xe3, 0x31, 0x18, 0x1b, 0x3c,
	0x6e, 0x57, 0x64, 0xb4, 0x92, 0x06, 0xe6, 0x42, 0x5f, 0x3f, 0x5d, 0x0b, 0xee, 0x9f, 0x9f, 0x9d,
	0xd2, 0x2d, 0xbc, 0xd9, 0x70, 0x09, 0xef, 0xc7, 0xaf, 0x7a, 0x5c, 0x8d, 0x98, 0xe4, 0xf4, 0x49,
	0xc7, 0x5a, 0x9a, 0xa8, 0x2c, 0x2a, 0xaf, 0xe5, 0x44, 0x8d, 0x22, 0x03, 0xf9, 0x44, 0x24, 0x60,
	0xa2, 0x35, 0x5e, 0x26, 0x87, 0x37, 0xca, 0xb2, 0xdd, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x35,
	0xda, 0x76, 0x25, 0x7a, 0x06, 0x00, 0x00,
}
