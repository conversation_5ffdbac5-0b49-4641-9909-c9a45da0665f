// Code generated by protoc-gen-go. DO NOT EDIT.
// source: missoingo-logic.proto

package missoingologic

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("missoingo-logic.proto", fileDescriptor_2d7c521781bbf85a) }

var fileDescriptor_2d7c521781bbf85a = []byte{
	// 118 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0xcd, 0xcd, 0x2c, 0x2e,
	0xce, 0xcf, 0xcc, 0x4b, 0xcf, 0xd7, 0xcd, 0xc9, 0x4f, 0xcf, 0x4c, 0xd6, 0x2b, 0x28, 0xca, 0x2f,
	0xc9, 0x17, 0x62, 0x05, 0x73, 0x8c, 0xf8, 0xb8, 0x78, 0x7c, 0x33, 0x8b, 0x8b, 0x33, 0xf3, 0xf3,
	0x7c, 0x40, 0x7c, 0x27, 0xbb, 0x28, 0x9b, 0xf4, 0xfc, 0x9c, 0xc4, 0xbc, 0x74, 0x3d, 0x53, 0xa3,
	0x92, 0x12, 0xbd, 0xe4, 0xfc, 0x5c, 0x7d, 0xb0, 0xfa, 0xe4, 0xfc, 0x1c, 0xfd, 0xe2, 0xd4, 0xa2,
	0xb2, 0xcc, 0xe4, 0xd4, 0x62, 0x7d, 0xb0, 0xd6, 0xe2, 0xb2, 0x22, 0xdd, 0xf4, 0x7c, 0x7d, 0xb8,
	0xe9, 0x60, 0xc1, 0x24, 0x36, 0xb0, 0x6a, 0x63, 0x40, 0x00, 0x00, 0x00, 0xff, 0xff, 0x48, 0x01,
	0xa9, 0x42, 0x76, 0x00, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MissionLogicClient is the client API for MissionLogic service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MissionLogicClient interface {
}

type missionLogicClient struct {
	cc *grpc.ClientConn
}

func NewMissionLogicClient(cc *grpc.ClientConn) MissionLogicClient {
	return &missionLogicClient{cc}
}

// MissionLogicServer is the server API for MissionLogic service.
type MissionLogicServer interface {
}

// UnimplementedMissionLogicServer can be embedded to have forward compatible implementations.
type UnimplementedMissionLogicServer struct {
}

func RegisterMissionLogicServer(s *grpc.Server, srv MissionLogicServer) {
	s.RegisterService(&_MissionLogic_serviceDesc, srv)
}

var _MissionLogic_serviceDesc = grpc.ServiceDesc{
	ServiceName: "logic.MissionLogic",
	HandlerType: (*MissionLogicServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams:     []grpc.StreamDesc{},
	Metadata:    "missoingo-logic.proto",
}
