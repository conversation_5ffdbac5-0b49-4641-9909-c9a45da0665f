// Code generated by protoc-gen-go. DO NOT EDIT.
// source: async-export-task/async-export-task.proto

package async_export_task // import "golang.52tt.com/protocol/services/async-export-task"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import _ "golang.52tt.com/protocol/services/extension/options"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TaskType int32

const (
	TaskType_Unknown                          TaskType = 0
	TaskType_CreateAmuseExtraIncomeSettleList TaskType = 1
	TaskType_QueryAnchorInfo                  TaskType = 2
	TaskType_QueryPrepareList                 TaskType = 3
	TaskType_QueryAnchorScore                 TaskType = 4
)

var TaskType_name = map[int32]string{
	0: "Unknown",
	1: "CreateAmuseExtraIncomeSettleList",
	2: "QueryAnchorInfo",
	3: "QueryPrepareList",
	4: "QueryAnchorScore",
}
var TaskType_value = map[string]int32{
	"Unknown":                          0,
	"CreateAmuseExtraIncomeSettleList": 1,
	"QueryAnchorInfo":                  2,
	"QueryPrepareList":                 3,
	"QueryAnchorScore":                 4,
}

func (x TaskType) String() string {
	return proto.EnumName(TaskType_name, int32(x))
}
func (TaskType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{0}
}

type BanStatusType int32

const (
	BanStatusType_BanStatusNoValid BanStatusType = 0
	BanStatusType_BanStatusNo      BanStatusType = 1
	BanStatusType_BanStatusYes     BanStatusType = 2
)

var BanStatusType_name = map[int32]string{
	0: "BanStatusNoValid",
	1: "BanStatusNo",
	2: "BanStatusYes",
}
var BanStatusType_value = map[string]int32{
	"BanStatusNoValid": 0,
	"BanStatusNo":      1,
	"BanStatusYes":     2,
}

func (x BanStatusType) String() string {
	return proto.EnumName(BanStatusType_name, int32(x))
}
func (BanStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{1}
}

type IdReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IdReq) Reset()         { *m = IdReq{} }
func (m *IdReq) String() string { return proto.CompactTextString(m) }
func (*IdReq) ProtoMessage()    {}
func (*IdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{0}
}
func (m *IdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IdReq.Unmarshal(m, b)
}
func (m *IdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IdReq.Marshal(b, m, deterministic)
}
func (dst *IdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IdReq.Merge(dst, src)
}
func (m *IdReq) XXX_Size() int {
	return xxx_messageInfo_IdReq.Size(m)
}
func (m *IdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IdReq.DiscardUnknown(m)
}

var xxx_messageInfo_IdReq proto.InternalMessageInfo

func (m *IdReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type TempUrlResp struct {
	TempUrl              string   `protobuf:"bytes,1,opt,name=temp_url,json=tempUrl,proto3" json:"temp_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TempUrlResp) Reset()         { *m = TempUrlResp{} }
func (m *TempUrlResp) String() string { return proto.CompactTextString(m) }
func (*TempUrlResp) ProtoMessage()    {}
func (*TempUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{1}
}
func (m *TempUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TempUrlResp.Unmarshal(m, b)
}
func (m *TempUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TempUrlResp.Marshal(b, m, deterministic)
}
func (dst *TempUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TempUrlResp.Merge(dst, src)
}
func (m *TempUrlResp) XXX_Size() int {
	return xxx_messageInfo_TempUrlResp.Size(m)
}
func (m *TempUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TempUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_TempUrlResp proto.InternalMessageInfo

func (m *TempUrlResp) GetTempUrl() string {
	if m != nil {
		return m.TempUrl
	}
	return ""
}

type GetExportAllTaskListReq struct {
	TaskType             uint32   `protobuf:"varint,1,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	Offset               uint32   `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetExportAllTaskListReq) Reset()         { *m = GetExportAllTaskListReq{} }
func (m *GetExportAllTaskListReq) String() string { return proto.CompactTextString(m) }
func (*GetExportAllTaskListReq) ProtoMessage()    {}
func (*GetExportAllTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{2}
}
func (m *GetExportAllTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExportAllTaskListReq.Unmarshal(m, b)
}
func (m *GetExportAllTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExportAllTaskListReq.Marshal(b, m, deterministic)
}
func (dst *GetExportAllTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExportAllTaskListReq.Merge(dst, src)
}
func (m *GetExportAllTaskListReq) XXX_Size() int {
	return xxx_messageInfo_GetExportAllTaskListReq.Size(m)
}
func (m *GetExportAllTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExportAllTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetExportAllTaskListReq proto.InternalMessageInfo

func (m *GetExportAllTaskListReq) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

func (m *GetExportAllTaskListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetExportAllTaskListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetExportAllTaskListResp struct {
	List                 []*ExportTaskData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total                uint32            `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetExportAllTaskListResp) Reset()         { *m = GetExportAllTaskListResp{} }
func (m *GetExportAllTaskListResp) String() string { return proto.CompactTextString(m) }
func (*GetExportAllTaskListResp) ProtoMessage()    {}
func (*GetExportAllTaskListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{3}
}
func (m *GetExportAllTaskListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetExportAllTaskListResp.Unmarshal(m, b)
}
func (m *GetExportAllTaskListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetExportAllTaskListResp.Marshal(b, m, deterministic)
}
func (dst *GetExportAllTaskListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetExportAllTaskListResp.Merge(dst, src)
}
func (m *GetExportAllTaskListResp) XXX_Size() int {
	return xxx_messageInfo_GetExportAllTaskListResp.Size(m)
}
func (m *GetExportAllTaskListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetExportAllTaskListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetExportAllTaskListResp proto.InternalMessageInfo

func (m *GetExportAllTaskListResp) GetList() []*ExportTaskData {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetExportAllTaskListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type ExportTaskData struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	TaskDesc             string   `protobuf:"bytes,2,opt,name=task_desc,json=taskDesc,proto3" json:"task_desc,omitempty"`
	TaskType             uint32   `protobuf:"varint,3,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	Percent              uint32   `protobuf:"varint,4,opt,name=percent,proto3" json:"percent,omitempty"`
	CreateTime           uint32   `protobuf:"varint,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	ErrData              string   `protobuf:"bytes,6,opt,name=err_data,json=errData,proto3" json:"err_data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportTaskData) Reset()         { *m = ExportTaskData{} }
func (m *ExportTaskData) String() string { return proto.CompactTextString(m) }
func (*ExportTaskData) ProtoMessage()    {}
func (*ExportTaskData) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{4}
}
func (m *ExportTaskData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportTaskData.Unmarshal(m, b)
}
func (m *ExportTaskData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportTaskData.Marshal(b, m, deterministic)
}
func (dst *ExportTaskData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportTaskData.Merge(dst, src)
}
func (m *ExportTaskData) XXX_Size() int {
	return xxx_messageInfo_ExportTaskData.Size(m)
}
func (m *ExportTaskData) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportTaskData.DiscardUnknown(m)
}

var xxx_messageInfo_ExportTaskData proto.InternalMessageInfo

func (m *ExportTaskData) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExportTaskData) GetTaskDesc() string {
	if m != nil {
		return m.TaskDesc
	}
	return ""
}

func (m *ExportTaskData) GetTaskType() uint32 {
	if m != nil {
		return m.TaskType
	}
	return 0
}

func (m *ExportTaskData) GetPercent() uint32 {
	if m != nil {
		return m.Percent
	}
	return 0
}

func (m *ExportTaskData) GetCreateTime() uint32 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *ExportTaskData) GetErrData() string {
	if m != nil {
		return m.ErrData
	}
	return ""
}

type CreateAmuseExtraIncomeSettleListExportTaskReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	StartTime            uint32   `protobuf:"varint,2,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	CompareStartTime     uint32   `protobuf:"varint,4,opt,name=compare_start_time,json=compareStartTime,proto3" json:"compare_start_time,omitempty"`
	CompareEndTime       uint32   `protobuf:"varint,5,opt,name=compare_end_time,json=compareEndTime,proto3" json:"compare_end_time,omitempty"`
	MasterUid            uint32   `protobuf:"varint,6,opt,name=master_uid,json=masterUid,proto3" json:"master_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) Reset() {
	*m = CreateAmuseExtraIncomeSettleListExportTaskReq{}
}
func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) String() string {
	return proto.CompactTextString(m)
}
func (*CreateAmuseExtraIncomeSettleListExportTaskReq) ProtoMessage() {}
func (*CreateAmuseExtraIncomeSettleListExportTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{5}
}
func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateAmuseExtraIncomeSettleListExportTaskReq.Unmarshal(m, b)
}
func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateAmuseExtraIncomeSettleListExportTaskReq.Marshal(b, m, deterministic)
}
func (dst *CreateAmuseExtraIncomeSettleListExportTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateAmuseExtraIncomeSettleListExportTaskReq.Merge(dst, src)
}
func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) XXX_Size() int {
	return xxx_messageInfo_CreateAmuseExtraIncomeSettleListExportTaskReq.Size(m)
}
func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateAmuseExtraIncomeSettleListExportTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateAmuseExtraIncomeSettleListExportTaskReq proto.InternalMessageInfo

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) GetCompareStartTime() uint32 {
	if m != nil {
		return m.CompareStartTime
	}
	return 0
}

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) GetCompareEndTime() uint32 {
	if m != nil {
		return m.CompareEndTime
	}
	return 0
}

func (m *CreateAmuseExtraIncomeSettleListExportTaskReq) GetMasterUid() uint32 {
	if m != nil {
		return m.MasterUid
	}
	return 0
}

type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{6}
}
func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (dst *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(dst, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

// 查询主播信息
type QueryAnchorInfoTaskReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TtidList             []string `protobuf:"bytes,2,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	BanStatus            uint32   `protobuf:"varint,3,opt,name=ban_status,json=banStatus,proto3" json:"ban_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryAnchorInfoTaskReq) Reset()         { *m = QueryAnchorInfoTaskReq{} }
func (m *QueryAnchorInfoTaskReq) String() string { return proto.CompactTextString(m) }
func (*QueryAnchorInfoTaskReq) ProtoMessage()    {}
func (*QueryAnchorInfoTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{7}
}
func (m *QueryAnchorInfoTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryAnchorInfoTaskReq.Unmarshal(m, b)
}
func (m *QueryAnchorInfoTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryAnchorInfoTaskReq.Marshal(b, m, deterministic)
}
func (dst *QueryAnchorInfoTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryAnchorInfoTaskReq.Merge(dst, src)
}
func (m *QueryAnchorInfoTaskReq) XXX_Size() int {
	return xxx_messageInfo_QueryAnchorInfoTaskReq.Size(m)
}
func (m *QueryAnchorInfoTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryAnchorInfoTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryAnchorInfoTaskReq proto.InternalMessageInfo

func (m *QueryAnchorInfoTaskReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *QueryAnchorInfoTaskReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

func (m *QueryAnchorInfoTaskReq) GetBanStatus() uint32 {
	if m != nil {
		return m.BanStatus
	}
	return 0
}

type QueryAnchorInfoTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryAnchorInfoTaskResp) Reset()         { *m = QueryAnchorInfoTaskResp{} }
func (m *QueryAnchorInfoTaskResp) String() string { return proto.CompactTextString(m) }
func (*QueryAnchorInfoTaskResp) ProtoMessage()    {}
func (*QueryAnchorInfoTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{8}
}
func (m *QueryAnchorInfoTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryAnchorInfoTaskResp.Unmarshal(m, b)
}
func (m *QueryAnchorInfoTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryAnchorInfoTaskResp.Marshal(b, m, deterministic)
}
func (dst *QueryAnchorInfoTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryAnchorInfoTaskResp.Merge(dst, src)
}
func (m *QueryAnchorInfoTaskResp) XXX_Size() int {
	return xxx_messageInfo_QueryAnchorInfoTaskResp.Size(m)
}
func (m *QueryAnchorInfoTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryAnchorInfoTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryAnchorInfoTaskResp proto.InternalMessageInfo

// 查询pgc推荐库的历史备份信息
type QueryPgcPrepareBackupInfoReq struct {
	Version              int64    `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPgcPrepareBackupInfoReq) Reset()         { *m = QueryPgcPrepareBackupInfoReq{} }
func (m *QueryPgcPrepareBackupInfoReq) String() string { return proto.CompactTextString(m) }
func (*QueryPgcPrepareBackupInfoReq) ProtoMessage()    {}
func (*QueryPgcPrepareBackupInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{9}
}
func (m *QueryPgcPrepareBackupInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPgcPrepareBackupInfoReq.Unmarshal(m, b)
}
func (m *QueryPgcPrepareBackupInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPgcPrepareBackupInfoReq.Marshal(b, m, deterministic)
}
func (dst *QueryPgcPrepareBackupInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPgcPrepareBackupInfoReq.Merge(dst, src)
}
func (m *QueryPgcPrepareBackupInfoReq) XXX_Size() int {
	return xxx_messageInfo_QueryPgcPrepareBackupInfoReq.Size(m)
}
func (m *QueryPgcPrepareBackupInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPgcPrepareBackupInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPgcPrepareBackupInfoReq proto.InternalMessageInfo

func (m *QueryPgcPrepareBackupInfoReq) GetVersion() int64 {
	if m != nil {
		return m.Version
	}
	return 0
}

type QueryPgcPrepareBackupInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryPgcPrepareBackupInfoResp) Reset()         { *m = QueryPgcPrepareBackupInfoResp{} }
func (m *QueryPgcPrepareBackupInfoResp) String() string { return proto.CompactTextString(m) }
func (*QueryPgcPrepareBackupInfoResp) ProtoMessage()    {}
func (*QueryPgcPrepareBackupInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{10}
}
func (m *QueryPgcPrepareBackupInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryPgcPrepareBackupInfoResp.Unmarshal(m, b)
}
func (m *QueryPgcPrepareBackupInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryPgcPrepareBackupInfoResp.Marshal(b, m, deterministic)
}
func (dst *QueryPgcPrepareBackupInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryPgcPrepareBackupInfoResp.Merge(dst, src)
}
func (m *QueryPgcPrepareBackupInfoResp) XXX_Size() int {
	return xxx_messageInfo_QueryPgcPrepareBackupInfoResp.Size(m)
}
func (m *QueryPgcPrepareBackupInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryPgcPrepareBackupInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryPgcPrepareBackupInfoResp proto.InternalMessageInfo

// 查询奖励积分信息
type QueryAnchorScoreListReq struct {
	BeginTs              uint32   `protobuf:"varint,1,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,2,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TtidList             []string `protobuf:"bytes,4,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	IsHasExchage         bool     `protobuf:"varint,5,opt,name=is_has_exchage,json=isHasExchage,proto3" json:"is_has_exchage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryAnchorScoreListReq) Reset()         { *m = QueryAnchorScoreListReq{} }
func (m *QueryAnchorScoreListReq) String() string { return proto.CompactTextString(m) }
func (*QueryAnchorScoreListReq) ProtoMessage()    {}
func (*QueryAnchorScoreListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{11}
}
func (m *QueryAnchorScoreListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryAnchorScoreListReq.Unmarshal(m, b)
}
func (m *QueryAnchorScoreListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryAnchorScoreListReq.Marshal(b, m, deterministic)
}
func (dst *QueryAnchorScoreListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryAnchorScoreListReq.Merge(dst, src)
}
func (m *QueryAnchorScoreListReq) XXX_Size() int {
	return xxx_messageInfo_QueryAnchorScoreListReq.Size(m)
}
func (m *QueryAnchorScoreListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryAnchorScoreListReq.DiscardUnknown(m)
}

var xxx_messageInfo_QueryAnchorScoreListReq proto.InternalMessageInfo

func (m *QueryAnchorScoreListReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *QueryAnchorScoreListReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *QueryAnchorScoreListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *QueryAnchorScoreListReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

func (m *QueryAnchorScoreListReq) GetIsHasExchage() bool {
	if m != nil {
		return m.IsHasExchage
	}
	return false
}

type QueryAnchorScoreListResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *QueryAnchorScoreListResp) Reset()         { *m = QueryAnchorScoreListResp{} }
func (m *QueryAnchorScoreListResp) String() string { return proto.CompactTextString(m) }
func (*QueryAnchorScoreListResp) ProtoMessage()    {}
func (*QueryAnchorScoreListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_async_export_task_5dd7691a37618d5a, []int{12}
}
func (m *QueryAnchorScoreListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QueryAnchorScoreListResp.Unmarshal(m, b)
}
func (m *QueryAnchorScoreListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QueryAnchorScoreListResp.Marshal(b, m, deterministic)
}
func (dst *QueryAnchorScoreListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QueryAnchorScoreListResp.Merge(dst, src)
}
func (m *QueryAnchorScoreListResp) XXX_Size() int {
	return xxx_messageInfo_QueryAnchorScoreListResp.Size(m)
}
func (m *QueryAnchorScoreListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QueryAnchorScoreListResp.DiscardUnknown(m)
}

var xxx_messageInfo_QueryAnchorScoreListResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*IdReq)(nil), "async_export_task.IdReq")
	proto.RegisterType((*TempUrlResp)(nil), "async_export_task.TempUrlResp")
	proto.RegisterType((*GetExportAllTaskListReq)(nil), "async_export_task.GetExportAllTaskListReq")
	proto.RegisterType((*GetExportAllTaskListResp)(nil), "async_export_task.GetExportAllTaskListResp")
	proto.RegisterType((*ExportTaskData)(nil), "async_export_task.ExportTaskData")
	proto.RegisterType((*CreateAmuseExtraIncomeSettleListExportTaskReq)(nil), "async_export_task.CreateAmuseExtraIncomeSettleListExportTaskReq")
	proto.RegisterType((*Empty)(nil), "async_export_task.Empty")
	proto.RegisterType((*QueryAnchorInfoTaskReq)(nil), "async_export_task.QueryAnchorInfoTaskReq")
	proto.RegisterType((*QueryAnchorInfoTaskResp)(nil), "async_export_task.QueryAnchorInfoTaskResp")
	proto.RegisterType((*QueryPgcPrepareBackupInfoReq)(nil), "async_export_task.QueryPgcPrepareBackupInfoReq")
	proto.RegisterType((*QueryPgcPrepareBackupInfoResp)(nil), "async_export_task.QueryPgcPrepareBackupInfoResp")
	proto.RegisterType((*QueryAnchorScoreListReq)(nil), "async_export_task.QueryAnchorScoreListReq")
	proto.RegisterType((*QueryAnchorScoreListResp)(nil), "async_export_task.QueryAnchorScoreListResp")
	proto.RegisterEnum("async_export_task.TaskType", TaskType_name, TaskType_value)
	proto.RegisterEnum("async_export_task.BanStatusType", BanStatusType_name, BanStatusType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// AsyncExportTaskClient is the client API for AsyncExportTask service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type AsyncExportTaskClient interface {
	// 通过类型翻页获取异步下载任务列表
	GetExportAllTaskList(ctx context.Context, in *GetExportAllTaskListReq, opts ...grpc.CallOption) (*GetExportAllTaskListResp, error)
	// 获取临时下载地址
	GetTempUrl(ctx context.Context, in *IdReq, opts ...grpc.CallOption) (*TempUrlResp, error)
	// 多人互动额外奖励结算报表异步导出
	CreateAmuseExtraIncomeSettleListExportTask(ctx context.Context, in *CreateAmuseExtraIncomeSettleListExportTaskReq, opts ...grpc.CallOption) (*Empty, error)
	// 主播查询
	QueryAnchorInfoTask(ctx context.Context, in *QueryAnchorInfoTaskReq, opts ...grpc.CallOption) (*QueryAnchorInfoTaskResp, error)
	// 查询pgc推荐库的历史备份信息
	QueryPgcPrepareBackupInfo(ctx context.Context, in *QueryPgcPrepareBackupInfoReq, opts ...grpc.CallOption) (*QueryPgcPrepareBackupInfoResp, error)
	// 查询奖励积分信息
	QueryAnchorScoreList(ctx context.Context, in *QueryAnchorScoreListReq, opts ...grpc.CallOption) (*QueryAnchorScoreListResp, error)
}

type asyncExportTaskClient struct {
	cc *grpc.ClientConn
}

func NewAsyncExportTaskClient(cc *grpc.ClientConn) AsyncExportTaskClient {
	return &asyncExportTaskClient{cc}
}

func (c *asyncExportTaskClient) GetExportAllTaskList(ctx context.Context, in *GetExportAllTaskListReq, opts ...grpc.CallOption) (*GetExportAllTaskListResp, error) {
	out := new(GetExportAllTaskListResp)
	err := c.cc.Invoke(ctx, "/async_export_task.AsyncExportTask/GetExportAllTaskList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *asyncExportTaskClient) GetTempUrl(ctx context.Context, in *IdReq, opts ...grpc.CallOption) (*TempUrlResp, error) {
	out := new(TempUrlResp)
	err := c.cc.Invoke(ctx, "/async_export_task.AsyncExportTask/GetTempUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *asyncExportTaskClient) CreateAmuseExtraIncomeSettleListExportTask(ctx context.Context, in *CreateAmuseExtraIncomeSettleListExportTaskReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, "/async_export_task.AsyncExportTask/CreateAmuseExtraIncomeSettleListExportTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *asyncExportTaskClient) QueryAnchorInfoTask(ctx context.Context, in *QueryAnchorInfoTaskReq, opts ...grpc.CallOption) (*QueryAnchorInfoTaskResp, error) {
	out := new(QueryAnchorInfoTaskResp)
	err := c.cc.Invoke(ctx, "/async_export_task.AsyncExportTask/QueryAnchorInfoTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *asyncExportTaskClient) QueryPgcPrepareBackupInfo(ctx context.Context, in *QueryPgcPrepareBackupInfoReq, opts ...grpc.CallOption) (*QueryPgcPrepareBackupInfoResp, error) {
	out := new(QueryPgcPrepareBackupInfoResp)
	err := c.cc.Invoke(ctx, "/async_export_task.AsyncExportTask/QueryPgcPrepareBackupInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *asyncExportTaskClient) QueryAnchorScoreList(ctx context.Context, in *QueryAnchorScoreListReq, opts ...grpc.CallOption) (*QueryAnchorScoreListResp, error) {
	out := new(QueryAnchorScoreListResp)
	err := c.cc.Invoke(ctx, "/async_export_task.AsyncExportTask/QueryAnchorScoreList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AsyncExportTaskServer is the server API for AsyncExportTask service.
type AsyncExportTaskServer interface {
	// 通过类型翻页获取异步下载任务列表
	GetExportAllTaskList(context.Context, *GetExportAllTaskListReq) (*GetExportAllTaskListResp, error)
	// 获取临时下载地址
	GetTempUrl(context.Context, *IdReq) (*TempUrlResp, error)
	// 多人互动额外奖励结算报表异步导出
	CreateAmuseExtraIncomeSettleListExportTask(context.Context, *CreateAmuseExtraIncomeSettleListExportTaskReq) (*Empty, error)
	// 主播查询
	QueryAnchorInfoTask(context.Context, *QueryAnchorInfoTaskReq) (*QueryAnchorInfoTaskResp, error)
	// 查询pgc推荐库的历史备份信息
	QueryPgcPrepareBackupInfo(context.Context, *QueryPgcPrepareBackupInfoReq) (*QueryPgcPrepareBackupInfoResp, error)
	// 查询奖励积分信息
	QueryAnchorScoreList(context.Context, *QueryAnchorScoreListReq) (*QueryAnchorScoreListResp, error)
}

func RegisterAsyncExportTaskServer(s *grpc.Server, srv AsyncExportTaskServer) {
	s.RegisterService(&_AsyncExportTask_serviceDesc, srv)
}

func _AsyncExportTask_GetExportAllTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExportAllTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AsyncExportTaskServer).GetExportAllTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/async_export_task.AsyncExportTask/GetExportAllTaskList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AsyncExportTaskServer).GetExportAllTaskList(ctx, req.(*GetExportAllTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AsyncExportTask_GetTempUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AsyncExportTaskServer).GetTempUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/async_export_task.AsyncExportTask/GetTempUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AsyncExportTaskServer).GetTempUrl(ctx, req.(*IdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AsyncExportTask_CreateAmuseExtraIncomeSettleListExportTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAmuseExtraIncomeSettleListExportTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AsyncExportTaskServer).CreateAmuseExtraIncomeSettleListExportTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/async_export_task.AsyncExportTask/CreateAmuseExtraIncomeSettleListExportTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AsyncExportTaskServer).CreateAmuseExtraIncomeSettleListExportTask(ctx, req.(*CreateAmuseExtraIncomeSettleListExportTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AsyncExportTask_QueryAnchorInfoTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAnchorInfoTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AsyncExportTaskServer).QueryAnchorInfoTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/async_export_task.AsyncExportTask/QueryAnchorInfoTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AsyncExportTaskServer).QueryAnchorInfoTask(ctx, req.(*QueryAnchorInfoTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AsyncExportTask_QueryPgcPrepareBackupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryPgcPrepareBackupInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AsyncExportTaskServer).QueryPgcPrepareBackupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/async_export_task.AsyncExportTask/QueryPgcPrepareBackupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AsyncExportTaskServer).QueryPgcPrepareBackupInfo(ctx, req.(*QueryPgcPrepareBackupInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AsyncExportTask_QueryAnchorScoreList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryAnchorScoreListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AsyncExportTaskServer).QueryAnchorScoreList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/async_export_task.AsyncExportTask/QueryAnchorScoreList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AsyncExportTaskServer).QueryAnchorScoreList(ctx, req.(*QueryAnchorScoreListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _AsyncExportTask_serviceDesc = grpc.ServiceDesc{
	ServiceName: "async_export_task.AsyncExportTask",
	HandlerType: (*AsyncExportTaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetExportAllTaskList",
			Handler:    _AsyncExportTask_GetExportAllTaskList_Handler,
		},
		{
			MethodName: "GetTempUrl",
			Handler:    _AsyncExportTask_GetTempUrl_Handler,
		},
		{
			MethodName: "CreateAmuseExtraIncomeSettleListExportTask",
			Handler:    _AsyncExportTask_CreateAmuseExtraIncomeSettleListExportTask_Handler,
		},
		{
			MethodName: "QueryAnchorInfoTask",
			Handler:    _AsyncExportTask_QueryAnchorInfoTask_Handler,
		},
		{
			MethodName: "QueryPgcPrepareBackupInfo",
			Handler:    _AsyncExportTask_QueryPgcPrepareBackupInfo_Handler,
		},
		{
			MethodName: "QueryAnchorScoreList",
			Handler:    _AsyncExportTask_QueryAnchorScoreList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "async-export-task/async-export-task.proto",
}

func init() {
	proto.RegisterFile("async-export-task/async-export-task.proto", fileDescriptor_async_export_task_5dd7691a37618d5a)
}

var fileDescriptor_async_export_task_5dd7691a37618d5a = []byte{
	// 916 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0x51, 0x6f, 0x1b, 0x45,
	0x10, 0xf6, 0xd9, 0x49, 0x6c, 0x8f, 0x9b, 0xf4, 0xd8, 0x9a, 0xe6, 0x6c, 0x08, 0x0e, 0x47, 0x1f,
	0x52, 0x43, 0x6c, 0x94, 0x2a, 0x12, 0x8f, 0x24, 0xd4, 0x6a, 0x22, 0x21, 0x54, 0x1c, 0x07, 0x09,
	0x5e, 0x4e, 0x9b, 0xbb, 0x89, 0xb3, 0xca, 0xdd, 0xed, 0x65, 0x77, 0x5d, 0xe2, 0x17, 0xc4, 0x33,
	0xff, 0x84, 0x37, 0x7e, 0x0b, 0x3f, 0x82, 0x3f, 0xc1, 0x03, 0x68, 0x77, 0x7d, 0xae, 0xe3, 0x9c,
	0xdb, 0xf4, 0xc9, 0x9a, 0xf9, 0x66, 0x66, 0xe7, 0xfb, 0xe6, 0x66, 0x64, 0x78, 0x4e, 0xe5, 0x34,
	0x0d, 0xf7, 0xf1, 0x36, 0xe3, 0x42, 0xed, 0x2b, 0x2a, 0xaf, 0xfb, 0xf7, 0x3c, 0xbd, 0x4c, 0x70,
	0xc5, 0xc9, 0x47, 0x06, 0x08, 0x2c, 0x10, 0x68, 0xa0, 0xdd, 0xc1, 0x5b, 0x85, 0xa9, 0x64, 0x3c,
	0xed, 0xf3, 0x4c, 0x31, 0x9e, 0xca, 0xfc, 0xd7, 0xe6, 0xf8, 0xdb, 0xb0, 0x7e, 0x1a, 0x0d, 0xf1,
	0x86, 0x6c, 0x41, 0x99, 0x45, 0x9e, 0xb3, 0xeb, 0xec, 0x6d, 0x0e, 0xcb, 0x2c, 0xf2, 0xf7, 0xa0,
	0x31, 0xc2, 0x24, 0x3b, 0x17, 0xf1, 0x10, 0x65, 0x46, 0x5a, 0x50, 0x53, 0x98, 0x64, 0xc1, 0x44,
	0xc4, 0x26, 0xa8, 0x3e, 0xac, 0x2a, 0x0b, 0xfb, 0x11, 0x6c, 0xbf, 0x42, 0x35, 0x30, 0xaf, 0x1e,
	0xc5, 0xf1, 0x88, 0xca, 0xeb, 0xef, 0x99, 0x54, 0xba, 0xe8, 0x27, 0x50, 0xd7, 0x6d, 0x04, 0x6a,
	0x9a, 0xe1, 0xac, 0x76, 0x4d, 0x3b, 0x46, 0xd3, 0x0c, 0xc9, 0x53, 0xd8, 0xe0, 0x97, 0x97, 0x12,
	0x95, 0x57, 0x36, 0xc8, 0xcc, 0x22, 0x4d, 0x58, 0x8f, 0x59, 0xc2, 0x94, 0x57, 0x31, 0x6e, 0x6b,
	0xf8, 0x63, 0xf0, 0x8a, 0x5f, 0x91, 0x19, 0x39, 0x84, 0xb5, 0x98, 0x49, 0xe5, 0x39, 0xbb, 0x95,
	0xbd, 0xc6, 0xc1, 0xe7, 0xbd, 0x7b, 0x3a, 0xf4, 0x6c, 0x9e, 0x4e, 0x7a, 0x49, 0x15, 0x1d, 0x9a,
	0x70, 0xfd, 0x90, 0xe2, 0x8a, 0xc6, 0xb3, 0xf7, 0xad, 0xe1, 0xff, 0xe5, 0xc0, 0xd6, 0xdd, 0xf0,
	0x65, 0x6d, 0xe6, 0xb4, 0x22, 0x94, 0xa1, 0x49, 0xae, 0x5b, 0x5a, 0x2f, 0x51, 0x86, 0x77, 0x39,
	0x57, 0x96, 0x38, 0x7b, 0x50, 0xcd, 0x50, 0x84, 0x98, 0x2a, 0x6f, 0xcd, 0x40, 0xb9, 0x49, 0x3a,
	0xd0, 0x08, 0x05, 0x52, 0x85, 0x81, 0x62, 0x09, 0x7a, 0xeb, 0x06, 0x05, 0xeb, 0x1a, 0xb1, 0x04,
	0xf5, 0x04, 0x50, 0x88, 0x20, 0xa2, 0x8a, 0x7a, 0x1b, 0x76, 0x02, 0x28, 0x84, 0xee, 0xcf, 0xff,
	0xd7, 0x81, 0xfd, 0xef, 0x4c, 0xe4, 0x51, 0x32, 0x91, 0x38, 0xb8, 0x55, 0x82, 0x9e, 0xa6, 0x21,
	0x4f, 0xf0, 0x0c, 0x95, 0x8a, 0x51, 0x0b, 0xf5, 0x96, 0x92, 0x1e, 0x4c, 0x0b, 0x6a, 0xe3, 0x09,
	0x8b, 0xa3, 0x60, 0xce, 0xab, 0x6a, 0xec, 0xd3, 0x88, 0xec, 0x00, 0x48, 0x45, 0xb5, 0x70, 0xba,
	0x0f, 0x2b, 0x4d, 0xdd, 0x78, 0xe6, 0x6d, 0xa4, 0x91, 0x05, 0x2d, 0xbb, 0x2a, 0xa6, 0x91, 0x81,
	0xbe, 0x02, 0x12, 0xf2, 0x24, 0xa3, 0x02, 0x83, 0x85, 0x0a, 0x96, 0xa7, 0x3b, 0x43, 0xce, 0xe6,
	0x85, 0xf6, 0x20, 0xf7, 0x05, 0xf3, 0x82, 0x96, 0xf5, 0xd6, 0xcc, 0x3f, 0x98, 0xd5, 0xdd, 0x01,
	0x48, 0xa8, 0x54, 0x28, 0x82, 0x09, 0x8b, 0x0c, 0xf7, 0xcd, 0x61, 0xdd, 0x7a, 0xce, 0x59, 0xe4,
	0x57, 0x61, 0x7d, 0x90, 0x64, 0x6a, 0xea, 0x73, 0x78, 0xfa, 0xe3, 0x04, 0xc5, 0xf4, 0x28, 0x0d,
	0xaf, 0xb8, 0x38, 0x4d, 0x2f, 0xf9, 0x02, 0xdd, 0x09, 0x8b, 0x82, 0xf9, 0x47, 0xb2, 0x39, 0xac,
	0x4e, 0x58, 0xa4, 0x65, 0x31, 0xe3, 0x52, 0x39, 0x56, 0xde, 0xad, 0x98, 0x59, 0xaa, 0x19, 0xb8,
	0x03, 0x70, 0x41, 0x53, 0xcd, 0x46, 0x4d, 0xe4, 0x8c, 0x6e, 0xfd, 0x82, 0xa6, 0x67, 0xc6, 0xe1,
	0xb7, 0x60, 0xbb, 0xf0, 0x41, 0x99, 0xf9, 0xdf, 0xc0, 0xa7, 0x06, 0x7a, 0x3d, 0x0e, 0x5f, 0x0b,
	0xd4, 0x6c, 0x8e, 0x69, 0x78, 0x3d, 0xc9, 0x74, 0x90, 0xee, 0xc8, 0x83, 0xea, 0x1b, 0x14, 0x7a,
	0x31, 0x8d, 0xfe, 0x95, 0x61, 0x6e, 0xfa, 0x1d, 0xd8, 0x79, 0x47, 0xa6, 0xcc, 0xfc, 0x3f, 0x9d,
	0x3b, 0xcf, 0x9e, 0x85, 0x5c, 0x60, 0xbe, 0x70, 0x2d, 0xa8, 0x5d, 0xe0, 0x98, 0xa5, 0x81, 0x92,
	0xf9, 0x5c, 0x8d, 0x3d, 0x92, 0xe4, 0x63, 0xd8, 0x30, 0x3a, 0xcb, 0xfc, 0x73, 0xd7, 0x63, 0x93,
	0x77, 0xa4, 0xa9, 0xbc, 0x43, 0x9a, 0xb5, 0x25, 0x69, 0x9e, 0xc1, 0x16, 0x93, 0xc1, 0x15, 0x95,
	0x01, 0xde, 0x86, 0x57, 0x74, 0x6c, 0x87, 0x57, 0x1b, 0x3e, 0x62, 0xf2, 0x84, 0xca, 0x81, 0xf5,
	0xf9, 0x6d, 0xf0, 0x8a, 0x5b, 0x95, 0x59, 0xf7, 0x37, 0xa8, 0x8d, 0xf2, 0xbd, 0x68, 0x40, 0xf5,
	0x3c, 0xbd, 0x4e, 0xf9, 0xaf, 0xa9, 0x5b, 0x22, 0xcf, 0x60, 0xf7, 0x7d, 0x5f, 0xb3, 0xeb, 0x90,
	0x27, 0xf0, 0x78, 0x49, 0x7c, 0xb7, 0x4c, 0x9a, 0xe0, 0x5a, 0xf1, 0xac, 0x72, 0x26, 0xb4, 0x32,
	0xf7, 0x2e, 0x74, 0xe1, 0xae, 0x75, 0x4f, 0x60, 0xf3, 0x38, 0x1f, 0xa5, 0x69, 0xa2, 0x09, 0xee,
	0xdc, 0xf1, 0x03, 0xff, 0x89, 0xc6, 0x2c, 0x72, 0x4b, 0xe4, 0x31, 0x34, 0x16, 0xbc, 0xae, 0x43,
	0x5c, 0x78, 0x34, 0x77, 0xfc, 0x8c, 0xd2, 0x2d, 0x1f, 0xfc, 0xb3, 0x0e, 0x8f, 0x8f, 0xf4, 0xcd,
	0x79, 0xbb, 0x64, 0xe4, 0x06, 0x9a, 0x45, 0xf7, 0x8a, 0x74, 0x0b, 0xae, 0xd3, 0x8a, 0xf3, 0xd9,
	0xfe, 0xf2, 0xc1, 0xb1, 0x32, 0xf3, 0x4b, 0xe4, 0x04, 0xe0, 0x15, 0xaa, 0xd9, 0xd5, 0x26, 0x5e,
	0x41, 0xb2, 0x39, 0xf5, 0xed, 0xcf, 0x0a, 0x90, 0x85, 0x5b, 0xef, 0x97, 0xc8, 0x1f, 0x0e, 0x74,
	0x1f, 0x7e, 0x50, 0xc8, 0xb7, 0x05, 0x05, 0x3f, 0xe8, 0x1e, 0xb5, 0x8b, 0x9a, 0xb5, 0x4b, 0x5d,
	0x22, 0x29, 0x3c, 0x29, 0xd8, 0x32, 0xf2, 0xbc, 0x20, 0xa5, 0x78, 0xfd, 0xdb, 0xdd, 0x87, 0x86,
	0x1a, 0xf2, 0xbf, 0x3b, 0xd0, 0x5a, 0xb9, 0x81, 0xa4, 0xbf, 0xaa, 0xd6, 0x8a, 0x4d, 0x6f, 0x7f,
	0xfd, 0x61, 0x09, 0xa6, 0x85, 0x1b, 0x68, 0x16, 0xad, 0x0d, 0x79, 0x0f, 0x91, 0xc5, 0x53, 0x50,
	0xf8, 0xf1, 0xac, 0xda, 0x45, 0xbf, 0xd4, 0xfe, 0xe2, 0xef, 0xff, 0x3a, 0x9d, 0xa5, 0x8f, 0xb8,
	0xb7, 0x64, 0x1f, 0x1f, 0xfe, 0xf2, 0x62, 0xcc, 0x63, 0x9a, 0x8e, 0x7b, 0x87, 0x07, 0x4a, 0xf5,
	0x42, 0x9e, 0xf4, 0xcd, 0xdf, 0x88, 0x90, 0xc7, 0x7d, 0x89, 0xe2, 0x0d, 0x0b, 0x51, 0xde, 0xff,
	0x7b, 0x72, 0xb1, 0x61, 0x82, 0x5e, 0xfc, 0x1f, 0x00, 0x00, 0xff, 0xff, 0x70, 0xdd, 0x78, 0x19,
	0xcc, 0x08, 0x00, 0x00,
}
