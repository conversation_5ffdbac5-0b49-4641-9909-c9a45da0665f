// Code generated by protoc-gen-gogo.
// source: src/guildtimeline2/guildtimeline2.proto
// DO NOT EDIT!

/*
	Package GuildTimeline2 is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/guildtimeline2/guildtimeline2.proto

	It has these top-level messages:
		StGuildGame
		CheckInMem
		DonateMem
		StGuildMember
		StGuildCheckinNumSyncInfo
		StGuildCheckinTopUserSyncInfo
		StGuildDonateNumSyncInfo
		StGuildDonateTopUserSyncInfo
		StGuildBaseSyncInfo
		StGuildExtraSyncInfo
		StGuildNumberDataSyncInfo
		StGuildGroupSyncInfo
		StGuildBatchGroupSyncInfo
		StGuildSpecialFlagSync
		StGuildGameSyncInfo
		StTimelineDataGiftpkg
		UpdateGuildCheckinNumSeqReq
		UpdateGuildCheckinTopUserSeqReq
		UpdateGuildDonateNumSeqReq
		UpdateGuildDonateTopUserSeqReq
		UpdateGuildBaseInfoSeqReq
		UpdateGuildExtraInfoSeqReq
		UpdateGuildNumberInfoSeqReq
		UpdateGuildMemberInfoReq
		StCommonIncrTimelineData
		UpdateCommonIncrTimelineSeqReq
		GetCommonTimelineBySeqReq
		GetCommonTimelineBySeqResp
		BatchDeleteCommonTimelineReq
		GetSyncUpdateInfoBySeqReq
		GetSyncUpdateInfoBySeqResp
		CheckGuildDataIsExistReq
		CheckGuildDataIsExistResp
		CleanGuildAllTimelineReq
		UpdateGuildGroupInfoReq
		GetGuildCheckInLastTimeReq
		GetGuildCheckInLastTimeResp
		GetGuildDonateLastTimeReq
		GetGuildDonateLastTimeResp
*/
package GuildTimeline2

import proto "github.com/gogo/protobuf/proto"
import fmt "fmt"
import math "math"
import tlvpickle "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 公会游戏
type StGuildGame struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
	Name   string `protobuf:"bytes,2,req,name=name" json:"name"`
	IconId string `protobuf:"bytes,3,req,name=icon_id,json=iconId" json:"icon_id"`
}

func (m *StGuildGame) Reset()                    { *m = StGuildGame{} }
func (m *StGuildGame) String() string            { return proto.CompactTextString(m) }
func (*StGuildGame) ProtoMessage()               {}
func (*StGuildGame) Descriptor() ([]byte, []int) { return fileDescriptorGuildtimeline2, []int{0} }

func (m *StGuildGame) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *StGuildGame) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StGuildGame) GetIconId() string {
	if m != nil {
		return m.IconId
	}
	return ""
}

type CheckInMem struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Days uint32 `protobuf:"varint,2,req,name=days" json:"days"`
}

func (m *CheckInMem) Reset()                    { *m = CheckInMem{} }
func (m *CheckInMem) String() string            { return proto.CompactTextString(m) }
func (*CheckInMem) ProtoMessage()               {}
func (*CheckInMem) Descriptor() ([]byte, []int) { return fileDescriptorGuildtimeline2, []int{1} }

func (m *CheckInMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckInMem) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

type DonateMem struct {
	Uid  uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Days uint32 `protobuf:"varint,2,req,name=days" json:"days"`
}

func (m *DonateMem) Reset()                    { *m = DonateMem{} }
func (m *DonateMem) String() string            { return proto.CompactTextString(m) }
func (*DonateMem) ProtoMessage()               {}
func (*DonateMem) Descriptor() ([]byte, []int) { return fileDescriptorGuildtimeline2, []int{2} }

func (m *DonateMem) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DonateMem) GetDays() uint32 {
	if m != nil {
		return m.Days
	}
	return 0
}

type StGuildMember struct {
	Uid     uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	Name    string `protobuf:"bytes,2,req,name=name" json:"name"`
	Role    uint32 `protobuf:"varint,3,req,name=role" json:"role"`
	Remark  string `protobuf:"bytes,4,req,name=remark" json:"remark"`
	SeqId   uint32 `protobuf:"varint,5,req,name=seq_id,json=seqId" json:"seq_id"`
	Account string `protobuf:"bytes,6,req,name=account" json:"account"`
}

func (m *StGuildMember) Reset()                    { *m = StGuildMember{} }
func (m *StGuildMember) String() string            { return proto.CompactTextString(m) }
func (*StGuildMember) ProtoMessage()               {}
func (*StGuildMember) Descriptor() ([]byte, []int) { return fileDescriptorGuildtimeline2, []int{3} }

func (m *StGuildMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *StGuildMember) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StGuildMember) GetRole() uint32 {
	if m != nil {
		return m.Role
	}
	return 0
}

func (m *StGuildMember) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *StGuildMember) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *StGuildMember) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

type StGuildCheckinNumSyncInfo struct {
	LastUpdateTime uint32 `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32 `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	CheckinNum     uint32 `protobuf:"varint,3,req,name=checkin_num,json=checkinNum" json:"checkin_num"`
}

func (m *StGuildCheckinNumSyncInfo) Reset()         { *m = StGuildCheckinNumSyncInfo{} }
func (m *StGuildCheckinNumSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildCheckinNumSyncInfo) ProtoMessage()    {}
func (*StGuildCheckinNumSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{4}
}

func (m *StGuildCheckinNumSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildCheckinNumSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildCheckinNumSyncInfo) GetCheckinNum() uint32 {
	if m != nil {
		return m.CheckinNum
	}
	return 0
}

type StGuildCheckinTopUserSyncInfo struct {
	LastUpdateTime  uint32        `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId       uint32        `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	CheckinTopNList []*CheckInMem `protobuf:"bytes,3,rep,name=checkin_top_n_list,json=checkinTopNList" json:"checkin_top_n_list,omitempty"`
}

func (m *StGuildCheckinTopUserSyncInfo) Reset()         { *m = StGuildCheckinTopUserSyncInfo{} }
func (m *StGuildCheckinTopUserSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildCheckinTopUserSyncInfo) ProtoMessage()    {}
func (*StGuildCheckinTopUserSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{5}
}

func (m *StGuildCheckinTopUserSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildCheckinTopUserSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildCheckinTopUserSyncInfo) GetCheckinTopNList() []*CheckInMem {
	if m != nil {
		return m.CheckinTopNList
	}
	return nil
}

type StGuildDonateNumSyncInfo struct {
	LastUpdateTime uint32 `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32 `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	DonateNum      uint32 `protobuf:"varint,3,req,name=donate_num,json=donateNum" json:"donate_num"`
}

func (m *StGuildDonateNumSyncInfo) Reset()         { *m = StGuildDonateNumSyncInfo{} }
func (m *StGuildDonateNumSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildDonateNumSyncInfo) ProtoMessage()    {}
func (*StGuildDonateNumSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{6}
}

func (m *StGuildDonateNumSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildDonateNumSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildDonateNumSyncInfo) GetDonateNum() uint32 {
	if m != nil {
		return m.DonateNum
	}
	return 0
}

type StGuildDonateTopUserSyncInfo struct {
	LastUpdateTime uint32       `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32       `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	DonateTopNList []*DonateMem `protobuf:"bytes,3,rep,name=donate_top_n_list,json=donateTopNList" json:"donate_top_n_list,omitempty"`
}

func (m *StGuildDonateTopUserSyncInfo) Reset()         { *m = StGuildDonateTopUserSyncInfo{} }
func (m *StGuildDonateTopUserSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildDonateTopUserSyncInfo) ProtoMessage()    {}
func (*StGuildDonateTopUserSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{7}
}

func (m *StGuildDonateTopUserSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildDonateTopUserSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildDonateTopUserSyncInfo) GetDonateTopNList() []*DonateMem {
	if m != nil {
		return m.DonateTopNList
	}
	return nil
}

type StGuildBaseSyncInfo struct {
	LastUpdateTime uint32 `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32 `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	GuildId        uint32 `protobuf:"varint,3,req,name=guild_id,json=guildId" json:"guild_id"`
	GuildDisplayId uint32 `protobuf:"varint,4,req,name=guild_display_id,json=guildDisplayId" json:"guild_display_id"`
	GuildGroupID   uint32 `protobuf:"varint,5,req,name=guild_groupID,json=guildGroupID" json:"guild_groupID"`
	GameSizelimit  uint32 `protobuf:"varint,6,req,name=game_sizelimit,json=gameSizelimit" json:"game_sizelimit"`
	CreateDate     uint32 `protobuf:"varint,7,req,name=create_date,json=createDate" json:"create_date"`
	GuildPrefix    string `protobuf:"bytes,8,req,name=guild_prefix,json=guildPrefix" json:"guild_prefix"`
	Name           string `protobuf:"bytes,9,req,name=name" json:"name"`
	Desc           string `protobuf:"bytes,10,req,name=desc" json:"desc"`
	Manifesto      string `protobuf:"bytes,11,req,name=manifesto" json:"manifesto"`
	IsNeedverify   bool   `protobuf:"varint,12,req,name=is_needverify,json=isNeedverify" json:"is_needverify"`
}

func (m *StGuildBaseSyncInfo) Reset()         { *m = StGuildBaseSyncInfo{} }
func (m *StGuildBaseSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildBaseSyncInfo) ProtoMessage()    {}
func (*StGuildBaseSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{8}
}

func (m *StGuildBaseSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetGuildDisplayId() uint32 {
	if m != nil {
		return m.GuildDisplayId
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetGuildGroupID() uint32 {
	if m != nil {
		return m.GuildGroupID
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetGameSizelimit() uint32 {
	if m != nil {
		return m.GameSizelimit
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetCreateDate() uint32 {
	if m != nil {
		return m.CreateDate
	}
	return 0
}

func (m *StGuildBaseSyncInfo) GetGuildPrefix() string {
	if m != nil {
		return m.GuildPrefix
	}
	return ""
}

func (m *StGuildBaseSyncInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *StGuildBaseSyncInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *StGuildBaseSyncInfo) GetManifesto() string {
	if m != nil {
		return m.Manifesto
	}
	return ""
}

func (m *StGuildBaseSyncInfo) GetIsNeedverify() bool {
	if m != nil {
		return m.IsNeedverify
	}
	return false
}

type StGuildExtraSyncInfo struct {
	LastUpdateTime uint32 `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32 `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	IconId         string `protobuf:"bytes,3,req,name=icon_id,json=iconId" json:"icon_id"`
}

func (m *StGuildExtraSyncInfo) Reset()         { *m = StGuildExtraSyncInfo{} }
func (m *StGuildExtraSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildExtraSyncInfo) ProtoMessage()    {}
func (*StGuildExtraSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{9}
}

func (m *StGuildExtraSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildExtraSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildExtraSyncInfo) GetIconId() string {
	if m != nil {
		return m.IconId
	}
	return ""
}

type StGuildNumberDataSyncInfo struct {
	LastUpdateTime uint32 `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32 `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	MemberCount    uint32 `protobuf:"varint,3,req,name=member_count,json=memberCount" json:"member_count"`
}

func (m *StGuildNumberDataSyncInfo) Reset()         { *m = StGuildNumberDataSyncInfo{} }
func (m *StGuildNumberDataSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildNumberDataSyncInfo) ProtoMessage()    {}
func (*StGuildNumberDataSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{10}
}

func (m *StGuildNumberDataSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildNumberDataSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildNumberDataSyncInfo) GetMemberCount() uint32 {
	if m != nil {
		return m.MemberCount
	}
	return 0
}

// 新版的保存群组变化的timeline sync结构
// 不再使用旧的通用增量timeline结构存储
type StGuildGroupSyncInfo struct {
	GroupId        uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	LastSeqId      uint32 `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	LastUpdateTime uint32 `protobuf:"varint,3,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	IsDel          bool   `protobuf:"varint,4,req,name=isDel" json:"isDel"`
}

func (m *StGuildGroupSyncInfo) Reset()         { *m = StGuildGroupSyncInfo{} }
func (m *StGuildGroupSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildGroupSyncInfo) ProtoMessage()    {}
func (*StGuildGroupSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{11}
}

func (m *StGuildGroupSyncInfo) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *StGuildGroupSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildGroupSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildGroupSyncInfo) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type StGuildBatchGroupSyncInfo struct {
	GroupInfoList []*StGuildGroupSyncInfo `protobuf:"bytes,1,rep,name=groupInfo_list,json=groupInfoList" json:"groupInfo_list,omitempty"`
}

func (m *StGuildBatchGroupSyncInfo) Reset()         { *m = StGuildBatchGroupSyncInfo{} }
func (m *StGuildBatchGroupSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildBatchGroupSyncInfo) ProtoMessage()    {}
func (*StGuildBatchGroupSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{12}
}

func (m *StGuildBatchGroupSyncInfo) GetGroupInfoList() []*StGuildGroupSyncInfo {
	if m != nil {
		return m.GroupInfoList
	}
	return nil
}

type StGuildSpecialFlagSync struct {
	Guildid uint32 `protobuf:"varint,1,req,name=guildid" json:"guildid"`
}

func (m *StGuildSpecialFlagSync) Reset()         { *m = StGuildSpecialFlagSync{} }
func (m *StGuildSpecialFlagSync) String() string { return proto.CompactTextString(m) }
func (*StGuildSpecialFlagSync) ProtoMessage()    {}
func (*StGuildSpecialFlagSync) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{13}
}

func (m *StGuildSpecialFlagSync) GetGuildid() uint32 {
	if m != nil {
		return m.Guildid
	}
	return 0
}

type StGuildGameSyncInfo struct {
	LastUpdateTime uint32         `protobuf:"varint,1,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
	LastSeqId      uint32         `protobuf:"varint,2,req,name=last_seq_id,json=lastSeqId" json:"last_seq_id"`
	GameList       []*StGuildGame `protobuf:"bytes,3,rep,name=game_list,json=gameList" json:"game_list,omitempty"`
}

func (m *StGuildGameSyncInfo) Reset()         { *m = StGuildGameSyncInfo{} }
func (m *StGuildGameSyncInfo) String() string { return proto.CompactTextString(m) }
func (*StGuildGameSyncInfo) ProtoMessage()    {}
func (*StGuildGameSyncInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{14}
}

func (m *StGuildGameSyncInfo) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func (m *StGuildGameSyncInfo) GetLastSeqId() uint32 {
	if m != nil {
		return m.LastSeqId
	}
	return 0
}

func (m *StGuildGameSyncInfo) GetGameList() []*StGuildGame {
	if m != nil {
		return m.GameList
	}
	return nil
}

type StTimelineDataGiftpkg struct {
	GameId uint32 `protobuf:"varint,1,req,name=game_id,json=gameId" json:"game_id"`
}

func (m *StTimelineDataGiftpkg) Reset()         { *m = StTimelineDataGiftpkg{} }
func (m *StTimelineDataGiftpkg) String() string { return proto.CompactTextString(m) }
func (*StTimelineDataGiftpkg) ProtoMessage()    {}
func (*StTimelineDataGiftpkg) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{15}
}

func (m *StTimelineDataGiftpkg) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

// 更新工会签到人数变化的Seq
type UpdateGuildCheckinNumSeqReq struct {
	GuildId uint32                     `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	NumInfo *StGuildCheckinNumSyncInfo `protobuf:"bytes,2,req,name=num_info,json=numInfo" json:"num_info,omitempty"`
}

func (m *UpdateGuildCheckinNumSeqReq) Reset()         { *m = UpdateGuildCheckinNumSeqReq{} }
func (m *UpdateGuildCheckinNumSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildCheckinNumSeqReq) ProtoMessage()    {}
func (*UpdateGuildCheckinNumSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{16}
}

func (m *UpdateGuildCheckinNumSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildCheckinNumSeqReq) GetNumInfo() *StGuildCheckinNumSyncInfo {
	if m != nil {
		return m.NumInfo
	}
	return nil
}

// 更新工会签到TopN用户列表变化的Seq
type UpdateGuildCheckinTopUserSeqReq struct {
	GuildId uint32                         `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopInfo *StGuildCheckinTopUserSyncInfo `protobuf:"bytes,2,req,name=top_info,json=topInfo" json:"top_info,omitempty"`
}

func (m *UpdateGuildCheckinTopUserSeqReq) Reset()         { *m = UpdateGuildCheckinTopUserSeqReq{} }
func (m *UpdateGuildCheckinTopUserSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildCheckinTopUserSeqReq) ProtoMessage()    {}
func (*UpdateGuildCheckinTopUserSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{17}
}

func (m *UpdateGuildCheckinTopUserSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildCheckinTopUserSeqReq) GetTopInfo() *StGuildCheckinTopUserSyncInfo {
	if m != nil {
		return m.TopInfo
	}
	return nil
}

// 更新工会捐献人数变化的Seq
type UpdateGuildDonateNumSeqReq struct {
	GuildId uint32                    `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	NumInfo *StGuildDonateNumSyncInfo `protobuf:"bytes,2,req,name=num_info,json=numInfo" json:"num_info,omitempty"`
}

func (m *UpdateGuildDonateNumSeqReq) Reset()         { *m = UpdateGuildDonateNumSeqReq{} }
func (m *UpdateGuildDonateNumSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildDonateNumSeqReq) ProtoMessage()    {}
func (*UpdateGuildDonateNumSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{18}
}

func (m *UpdateGuildDonateNumSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildDonateNumSeqReq) GetNumInfo() *StGuildDonateNumSyncInfo {
	if m != nil {
		return m.NumInfo
	}
	return nil
}

// 更新工会捐献TopN用户列表变化的Seq
type UpdateGuildDonateTopUserSeqReq struct {
	GuildId uint32                        `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	TopInfo *StGuildDonateTopUserSyncInfo `protobuf:"bytes,2,req,name=top_info,json=topInfo" json:"top_info,omitempty"`
}

func (m *UpdateGuildDonateTopUserSeqReq) Reset()         { *m = UpdateGuildDonateTopUserSeqReq{} }
func (m *UpdateGuildDonateTopUserSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildDonateTopUserSeqReq) ProtoMessage()    {}
func (*UpdateGuildDonateTopUserSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{19}
}

func (m *UpdateGuildDonateTopUserSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildDonateTopUserSeqReq) GetTopInfo() *StGuildDonateTopUserSyncInfo {
	if m != nil {
		return m.TopInfo
	}
	return nil
}

// 公会基本信息变化
type UpdateGuildBaseInfoSeqReq struct {
	GuildId  uint32               `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Baseinfo *StGuildBaseSyncInfo `protobuf:"bytes,2,req,name=baseinfo" json:"baseinfo,omitempty"`
}

func (m *UpdateGuildBaseInfoSeqReq) Reset()         { *m = UpdateGuildBaseInfoSeqReq{} }
func (m *UpdateGuildBaseInfoSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildBaseInfoSeqReq) ProtoMessage()    {}
func (*UpdateGuildBaseInfoSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{20}
}

func (m *UpdateGuildBaseInfoSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildBaseInfoSeqReq) GetBaseinfo() *StGuildBaseSyncInfo {
	if m != nil {
		return m.Baseinfo
	}
	return nil
}

// 公会附加信息变化
type UpdateGuildExtraInfoSeqReq struct {
	GuildId   uint32                `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Extrainfo *StGuildExtraSyncInfo `protobuf:"bytes,2,req,name=extrainfo" json:"extrainfo,omitempty"`
}

func (m *UpdateGuildExtraInfoSeqReq) Reset()         { *m = UpdateGuildExtraInfoSeqReq{} }
func (m *UpdateGuildExtraInfoSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildExtraInfoSeqReq) ProtoMessage()    {}
func (*UpdateGuildExtraInfoSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{21}
}

func (m *UpdateGuildExtraInfoSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildExtraInfoSeqReq) GetExtrainfo() *StGuildExtraSyncInfo {
	if m != nil {
		return m.Extrainfo
	}
	return nil
}

// 公会数字类信息变化(包括公会成员数量)
type UpdateGuildNumberInfoSeqReq struct {
	GuildId    uint32                     `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	NumberInfo *StGuildNumberDataSyncInfo `protobuf:"bytes,2,req,name=numberInfo" json:"numberInfo,omitempty"`
}

func (m *UpdateGuildNumberInfoSeqReq) Reset()         { *m = UpdateGuildNumberInfoSeqReq{} }
func (m *UpdateGuildNumberInfoSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildNumberInfoSeqReq) ProtoMessage()    {}
func (*UpdateGuildNumberInfoSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{22}
}

func (m *UpdateGuildNumberInfoSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildNumberInfoSeqReq) GetNumberInfo() *StGuildNumberDataSyncInfo {
	if m != nil {
		return m.NumberInfo
	}
	return nil
}

// 公会成员信息变化(目前只对管理员)
type UpdateGuildMemberInfoReq struct {
	GuildId uint32         `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Uid     uint32         `protobuf:"varint,2,req,name=uid" json:"uid"`
	MemInfo *StGuildMember `protobuf:"bytes,3,req,name=mem_info,json=memInfo" json:"mem_info,omitempty"`
}

func (m *UpdateGuildMemberInfoReq) Reset()         { *m = UpdateGuildMemberInfoReq{} }
func (m *UpdateGuildMemberInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildMemberInfoReq) ProtoMessage()    {}
func (*UpdateGuildMemberInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{23}
}

func (m *UpdateGuildMemberInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildMemberInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateGuildMemberInfoReq) GetMemInfo() *StGuildMember {
	if m != nil {
		return m.MemInfo
	}
	return nil
}

type StCommonIncrTimelineData struct {
	KeyPrefix string `protobuf:"bytes,1,req,name=key_prefix,json=keyPrefix" json:"key_prefix"`
	KeyId     uint32 `protobuf:"varint,2,req,name=key_id,json=keyId" json:"key_id"`
	IsDeleted bool   `protobuf:"varint,3,req,name=is_deleted,json=isDeleted" json:"is_deleted"`
	BinValue  []byte `protobuf:"bytes,4,opt,name=bin_value,json=binValue" json:"bin_value"`
}

func (m *StCommonIncrTimelineData) Reset()         { *m = StCommonIncrTimelineData{} }
func (m *StCommonIncrTimelineData) String() string { return proto.CompactTextString(m) }
func (*StCommonIncrTimelineData) ProtoMessage()    {}
func (*StCommonIncrTimelineData) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{24}
}

func (m *StCommonIncrTimelineData) GetKeyPrefix() string {
	if m != nil {
		return m.KeyPrefix
	}
	return ""
}

func (m *StCommonIncrTimelineData) GetKeyId() uint32 {
	if m != nil {
		return m.KeyId
	}
	return 0
}

func (m *StCommonIncrTimelineData) GetIsDeleted() bool {
	if m != nil {
		return m.IsDeleted
	}
	return false
}

func (m *StCommonIncrTimelineData) GetBinValue() []byte {
	if m != nil {
		return m.BinValue
	}
	return nil
}

type UpdateCommonIncrTimelineSeqReq struct {
	GuildId uint32                    `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SeqId   uint32                    `protobuf:"varint,2,req,name=seq_id,json=seqId" json:"seq_id"`
	Data    *StCommonIncrTimelineData `protobuf:"bytes,3,req,name=data" json:"data,omitempty"`
}

func (m *UpdateCommonIncrTimelineSeqReq) Reset()         { *m = UpdateCommonIncrTimelineSeqReq{} }
func (m *UpdateCommonIncrTimelineSeqReq) String() string { return proto.CompactTextString(m) }
func (*UpdateCommonIncrTimelineSeqReq) ProtoMessage()    {}
func (*UpdateCommonIncrTimelineSeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{25}
}

func (m *UpdateCommonIncrTimelineSeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateCommonIncrTimelineSeqReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

func (m *UpdateCommonIncrTimelineSeqReq) GetData() *StCommonIncrTimelineData {
	if m != nil {
		return m.Data
	}
	return nil
}

type GetCommonTimelineBySeqReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SeqId   uint32 `protobuf:"varint,2,req,name=seq_id,json=seqId" json:"seq_id"`
}

func (m *GetCommonTimelineBySeqReq) Reset()         { *m = GetCommonTimelineBySeqReq{} }
func (m *GetCommonTimelineBySeqReq) String() string { return proto.CompactTextString(m) }
func (*GetCommonTimelineBySeqReq) ProtoMessage()    {}
func (*GetCommonTimelineBySeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{26}
}

func (m *GetCommonTimelineBySeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCommonTimelineBySeqReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type GetCommonTimelineBySeqResp struct {
	SeqIdList []uint32                    `protobuf:"varint,1,rep,name=seq_id_list,json=seqIdList" json:"seq_id_list,omitempty"`
	DataList  []*StCommonIncrTimelineData `protobuf:"bytes,2,rep,name=data_list,json=dataList" json:"data_list,omitempty"`
}

func (m *GetCommonTimelineBySeqResp) Reset()         { *m = GetCommonTimelineBySeqResp{} }
func (m *GetCommonTimelineBySeqResp) String() string { return proto.CompactTextString(m) }
func (*GetCommonTimelineBySeqResp) ProtoMessage()    {}
func (*GetCommonTimelineBySeqResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{27}
}

func (m *GetCommonTimelineBySeqResp) GetSeqIdList() []uint32 {
	if m != nil {
		return m.SeqIdList
	}
	return nil
}

func (m *GetCommonTimelineBySeqResp) GetDataList() []*StCommonIncrTimelineData {
	if m != nil {
		return m.DataList
	}
	return nil
}

type BatchDeleteCommonTimelineReq struct {
	GuildId   uint32   `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SeqIdList []uint32 `protobuf:"varint,2,rep,name=seq_id_list,json=seqIdList" json:"seq_id_list,omitempty"`
}

func (m *BatchDeleteCommonTimelineReq) Reset()         { *m = BatchDeleteCommonTimelineReq{} }
func (m *BatchDeleteCommonTimelineReq) String() string { return proto.CompactTextString(m) }
func (*BatchDeleteCommonTimelineReq) ProtoMessage()    {}
func (*BatchDeleteCommonTimelineReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{28}
}

func (m *BatchDeleteCommonTimelineReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchDeleteCommonTimelineReq) GetSeqIdList() []uint32 {
	if m != nil {
		return m.SeqIdList
	}
	return nil
}

type GetSyncUpdateInfoBySeqReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	SeqId   uint32 `protobuf:"varint,2,req,name=seq_id,json=seqId" json:"seq_id"`
}

func (m *GetSyncUpdateInfoBySeqReq) Reset()         { *m = GetSyncUpdateInfoBySeqReq{} }
func (m *GetSyncUpdateInfoBySeqReq) String() string { return proto.CompactTextString(m) }
func (*GetSyncUpdateInfoBySeqReq) ProtoMessage()    {}
func (*GetSyncUpdateInfoBySeqReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{29}
}

func (m *GetSyncUpdateInfoBySeqReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetSyncUpdateInfoBySeqReq) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type GetSyncUpdateInfoBySeqResp struct {
	GuildBaseinf *StGuildBaseSyncInfo           `protobuf:"bytes,1,opt,name=guild_baseinf,json=guildBaseinf" json:"guild_baseinf,omitempty"`
	GuildExtrinf *StGuildExtraSyncInfo          `protobuf:"bytes,2,opt,name=guild_extrinf,json=guildExtrinf" json:"guild_extrinf,omitempty"`
	CheckinNum   *StGuildCheckinNumSyncInfo     `protobuf:"bytes,3,opt,name=checkin_num,json=checkinNum" json:"checkin_num,omitempty"`
	CheckinTopn  *StGuildCheckinTopUserSyncInfo `protobuf:"bytes,4,opt,name=checkin_topn,json=checkinTopn" json:"checkin_topn,omitempty"`
	GuildNumdata *StGuildNumberDataSyncInfo     `protobuf:"bytes,5,opt,name=guild_numdata,json=guildNumdata" json:"guild_numdata,omitempty"`
	GroupInfo    *StGuildBatchGroupSyncInfo     `protobuf:"bytes,6,opt,name=group_info,json=groupInfo" json:"group_info,omitempty"`
	FlagSync     *StGuildSpecialFlagSync        `protobuf:"bytes,7,opt,name=flagSync" json:"flagSync,omitempty"`
	DonateNum    *StGuildDonateNumSyncInfo      `protobuf:"bytes,8,opt,name=donate_num,json=donateNum" json:"donate_num,omitempty"`
	DonateTopn   *StGuildDonateTopUserSyncInfo  `protobuf:"bytes,9,opt,name=donate_topn,json=donateTopn" json:"donate_topn,omitempty"`
}

func (m *GetSyncUpdateInfoBySeqResp) Reset()         { *m = GetSyncUpdateInfoBySeqResp{} }
func (m *GetSyncUpdateInfoBySeqResp) String() string { return proto.CompactTextString(m) }
func (*GetSyncUpdateInfoBySeqResp) ProtoMessage()    {}
func (*GetSyncUpdateInfoBySeqResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{30}
}

func (m *GetSyncUpdateInfoBySeqResp) GetGuildBaseinf() *StGuildBaseSyncInfo {
	if m != nil {
		return m.GuildBaseinf
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetGuildExtrinf() *StGuildExtraSyncInfo {
	if m != nil {
		return m.GuildExtrinf
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetCheckinNum() *StGuildCheckinNumSyncInfo {
	if m != nil {
		return m.CheckinNum
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetCheckinTopn() *StGuildCheckinTopUserSyncInfo {
	if m != nil {
		return m.CheckinTopn
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetGuildNumdata() *StGuildNumberDataSyncInfo {
	if m != nil {
		return m.GuildNumdata
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetGroupInfo() *StGuildBatchGroupSyncInfo {
	if m != nil {
		return m.GroupInfo
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetFlagSync() *StGuildSpecialFlagSync {
	if m != nil {
		return m.FlagSync
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetDonateNum() *StGuildDonateNumSyncInfo {
	if m != nil {
		return m.DonateNum
	}
	return nil
}

func (m *GetSyncUpdateInfoBySeqResp) GetDonateTopn() *StGuildDonateTopUserSyncInfo {
	if m != nil {
		return m.DonateTopn
	}
	return nil
}

type CheckGuildDataIsExistReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *CheckGuildDataIsExistReq) Reset()         { *m = CheckGuildDataIsExistReq{} }
func (m *CheckGuildDataIsExistReq) String() string { return proto.CompactTextString(m) }
func (*CheckGuildDataIsExistReq) ProtoMessage()    {}
func (*CheckGuildDataIsExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{31}
}

func (m *CheckGuildDataIsExistReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type CheckGuildDataIsExistResp struct {
	GuildId  uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	Is_Exist bool   `protobuf:"varint,2,req,name=is_Exist,json=isExist" json:"is_Exist"`
}

func (m *CheckGuildDataIsExistResp) Reset()         { *m = CheckGuildDataIsExistResp{} }
func (m *CheckGuildDataIsExistResp) String() string { return proto.CompactTextString(m) }
func (*CheckGuildDataIsExistResp) ProtoMessage()    {}
func (*CheckGuildDataIsExistResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{32}
}

func (m *CheckGuildDataIsExistResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *CheckGuildDataIsExistResp) GetIs_Exist() bool {
	if m != nil {
		return m.Is_Exist
	}
	return false
}

type CleanGuildAllTimelineReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *CleanGuildAllTimelineReq) Reset()         { *m = CleanGuildAllTimelineReq{} }
func (m *CleanGuildAllTimelineReq) String() string { return proto.CompactTextString(m) }
func (*CleanGuildAllTimelineReq) ProtoMessage()    {}
func (*CleanGuildAllTimelineReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{33}
}

func (m *CleanGuildAllTimelineReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 该接口暂时不启用
type UpdateGuildGroupInfoReq struct {
	GuildId uint32                `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GroupId uint32                `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	Info    *StGuildGroupSyncInfo `protobuf:"bytes,3,req,name=info" json:"info,omitempty"`
}

func (m *UpdateGuildGroupInfoReq) Reset()         { *m = UpdateGuildGroupInfoReq{} }
func (m *UpdateGuildGroupInfoReq) String() string { return proto.CompactTextString(m) }
func (*UpdateGuildGroupInfoReq) ProtoMessage()    {}
func (*UpdateGuildGroupInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{34}
}

func (m *UpdateGuildGroupInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateGuildGroupInfoReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *UpdateGuildGroupInfoReq) GetInfo() *StGuildGroupSyncInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取签到最后更新时间
type GetGuildCheckInLastTimeReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildCheckInLastTimeReq) Reset()         { *m = GetGuildCheckInLastTimeReq{} }
func (m *GetGuildCheckInLastTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInLastTimeReq) ProtoMessage()    {}
func (*GetGuildCheckInLastTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{35}
}

func (m *GetGuildCheckInLastTimeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildCheckInLastTimeResp struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LastUpdateTime uint32 `protobuf:"varint,2,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
}

func (m *GetGuildCheckInLastTimeResp) Reset()         { *m = GetGuildCheckInLastTimeResp{} }
func (m *GetGuildCheckInLastTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildCheckInLastTimeResp) ProtoMessage()    {}
func (*GetGuildCheckInLastTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{36}
}

func (m *GetGuildCheckInLastTimeResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildCheckInLastTimeResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

// 获取捐献最后更新时间
type GetGuildDonateLastTimeReq struct {
	GuildId uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
}

func (m *GetGuildDonateLastTimeReq) Reset()         { *m = GetGuildDonateLastTimeReq{} }
func (m *GetGuildDonateLastTimeReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateLastTimeReq) ProtoMessage()    {}
func (*GetGuildDonateLastTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{37}
}

func (m *GetGuildDonateLastTimeReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetGuildDonateLastTimeResp struct {
	GuildId        uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	LastUpdateTime uint32 `protobuf:"varint,2,req,name=last_update_time,json=lastUpdateTime" json:"last_update_time"`
}

func (m *GetGuildDonateLastTimeResp) Reset()         { *m = GetGuildDonateLastTimeResp{} }
func (m *GetGuildDonateLastTimeResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildDonateLastTimeResp) ProtoMessage()    {}
func (*GetGuildDonateLastTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptorGuildtimeline2, []int{38}
}

func (m *GetGuildDonateLastTimeResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildDonateLastTimeResp) GetLastUpdateTime() uint32 {
	if m != nil {
		return m.LastUpdateTime
	}
	return 0
}

func init() {
	proto.RegisterType((*StGuildGame)(nil), "GuildTimeline2.stGuildGame")
	proto.RegisterType((*CheckInMem)(nil), "GuildTimeline2.CheckInMem")
	proto.RegisterType((*DonateMem)(nil), "GuildTimeline2.DonateMem")
	proto.RegisterType((*StGuildMember)(nil), "GuildTimeline2.stGuildMember")
	proto.RegisterType((*StGuildCheckinNumSyncInfo)(nil), "GuildTimeline2.stGuildCheckinNumSyncInfo")
	proto.RegisterType((*StGuildCheckinTopUserSyncInfo)(nil), "GuildTimeline2.stGuildCheckinTopUserSyncInfo")
	proto.RegisterType((*StGuildDonateNumSyncInfo)(nil), "GuildTimeline2.stGuildDonateNumSyncInfo")
	proto.RegisterType((*StGuildDonateTopUserSyncInfo)(nil), "GuildTimeline2.stGuildDonateTopUserSyncInfo")
	proto.RegisterType((*StGuildBaseSyncInfo)(nil), "GuildTimeline2.stGuildBaseSyncInfo")
	proto.RegisterType((*StGuildExtraSyncInfo)(nil), "GuildTimeline2.stGuildExtraSyncInfo")
	proto.RegisterType((*StGuildNumberDataSyncInfo)(nil), "GuildTimeline2.stGuildNumberDataSyncInfo")
	proto.RegisterType((*StGuildGroupSyncInfo)(nil), "GuildTimeline2.stGuildGroupSyncInfo")
	proto.RegisterType((*StGuildBatchGroupSyncInfo)(nil), "GuildTimeline2.stGuildBatchGroupSyncInfo")
	proto.RegisterType((*StGuildSpecialFlagSync)(nil), "GuildTimeline2.stGuildSpecialFlagSync")
	proto.RegisterType((*StGuildGameSyncInfo)(nil), "GuildTimeline2.stGuildGameSyncInfo")
	proto.RegisterType((*StTimelineDataGiftpkg)(nil), "GuildTimeline2.stTimelineDataGiftpkg")
	proto.RegisterType((*UpdateGuildCheckinNumSeqReq)(nil), "GuildTimeline2.UpdateGuildCheckinNumSeqReq")
	proto.RegisterType((*UpdateGuildCheckinTopUserSeqReq)(nil), "GuildTimeline2.UpdateGuildCheckinTopUserSeqReq")
	proto.RegisterType((*UpdateGuildDonateNumSeqReq)(nil), "GuildTimeline2.UpdateGuildDonateNumSeqReq")
	proto.RegisterType((*UpdateGuildDonateTopUserSeqReq)(nil), "GuildTimeline2.UpdateGuildDonateTopUserSeqReq")
	proto.RegisterType((*UpdateGuildBaseInfoSeqReq)(nil), "GuildTimeline2.UpdateGuildBaseInfoSeqReq")
	proto.RegisterType((*UpdateGuildExtraInfoSeqReq)(nil), "GuildTimeline2.UpdateGuildExtraInfoSeqReq")
	proto.RegisterType((*UpdateGuildNumberInfoSeqReq)(nil), "GuildTimeline2.UpdateGuildNumberInfoSeqReq")
	proto.RegisterType((*UpdateGuildMemberInfoReq)(nil), "GuildTimeline2.UpdateGuildMemberInfoReq")
	proto.RegisterType((*StCommonIncrTimelineData)(nil), "GuildTimeline2.stCommonIncrTimelineData")
	proto.RegisterType((*UpdateCommonIncrTimelineSeqReq)(nil), "GuildTimeline2.UpdateCommonIncrTimelineSeqReq")
	proto.RegisterType((*GetCommonTimelineBySeqReq)(nil), "GuildTimeline2.GetCommonTimelineBySeqReq")
	proto.RegisterType((*GetCommonTimelineBySeqResp)(nil), "GuildTimeline2.GetCommonTimelineBySeqResp")
	proto.RegisterType((*BatchDeleteCommonTimelineReq)(nil), "GuildTimeline2.BatchDeleteCommonTimelineReq")
	proto.RegisterType((*GetSyncUpdateInfoBySeqReq)(nil), "GuildTimeline2.GetSyncUpdateInfoBySeqReq")
	proto.RegisterType((*GetSyncUpdateInfoBySeqResp)(nil), "GuildTimeline2.GetSyncUpdateInfoBySeqResp")
	proto.RegisterType((*CheckGuildDataIsExistReq)(nil), "GuildTimeline2.CheckGuildDataIsExistReq")
	proto.RegisterType((*CheckGuildDataIsExistResp)(nil), "GuildTimeline2.CheckGuildDataIsExistResp")
	proto.RegisterType((*CleanGuildAllTimelineReq)(nil), "GuildTimeline2.CleanGuildAllTimelineReq")
	proto.RegisterType((*UpdateGuildGroupInfoReq)(nil), "GuildTimeline2.UpdateGuildGroupInfoReq")
	proto.RegisterType((*GetGuildCheckInLastTimeReq)(nil), "GuildTimeline2.GetGuildCheckInLastTimeReq")
	proto.RegisterType((*GetGuildCheckInLastTimeResp)(nil), "GuildTimeline2.GetGuildCheckInLastTimeResp")
	proto.RegisterType((*GetGuildDonateLastTimeReq)(nil), "GuildTimeline2.GetGuildDonateLastTimeReq")
	proto.RegisterType((*GetGuildDonateLastTimeResp)(nil), "GuildTimeline2.GetGuildDonateLastTimeResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for GuildTimeline2 service

type GuildTimeline2Client interface {
	// 签到总数的变化
	UpdateGuildCheckinNumSeq(ctx context.Context, in *UpdateGuildCheckinNumSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 签到TOPN的列表的变化
	UpdateGuildCheckinTopUserSeq(ctx context.Context, in *UpdateGuildCheckinTopUserSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 公会基本信息变化
	UpdateGuildBaseInfoSeq(ctx context.Context, in *UpdateGuildBaseInfoSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 公会附加信息变化
	UpdateGuildExtraInfoSeq(ctx context.Context, in *UpdateGuildExtraInfoSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 公会数字类信息变化
	UpdateGuildNumberInfoSeq(ctx context.Context, in *UpdateGuildNumberInfoSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	//
	// 公会成员信息变化(目前只针对有权限的管理员)
	// 注意该接口没有使用！！！！
	UpdateGuildMemberInfo(ctx context.Context, in *UpdateGuildMemberInfoReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 通用的增量时间线变化
	UpdateCommonIncrTimelineSeq(ctx context.Context, in *UpdateCommonIncrTimelineSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCommonTimelineBySeq(ctx context.Context, in *GetCommonTimelineBySeqReq, opts ...grpc.CallOption) (*GetCommonTimelineBySeqResp, error)
	BatchDeleteCommonTimeline(ctx context.Context, in *BatchDeleteCommonTimelineReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 根据SEQ序号获取最新SYNC数据
	GetSyncUpdateInfoBySeq(ctx context.Context, in *GetSyncUpdateInfoBySeqReq, opts ...grpc.CallOption) (*GetSyncUpdateInfoBySeqResp, error)
	// 检测公会信息是否存在
	CheckGuildDataIsExist(ctx context.Context, in *CheckGuildDataIsExistReq, opts ...grpc.CallOption) (*CheckGuildDataIsExistResp, error)
	// 清除公会全部timeline数据 只在公会解散时被调用
	CleanGuildAllTimeline(ctx context.Context, in *CleanGuildAllTimelineReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 该接口暂时不启用！！！
	// 公会群更新（新版不使用通用timeline增量结构存储）
	UpdateGuildGroupInfo(ctx context.Context, in *UpdateGuildGroupInfoReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取公会最后签到更新时间
	GetGuildCheckInLastTime(ctx context.Context, in *GetGuildCheckInLastTimeReq, opts ...grpc.CallOption) (*GetGuildCheckInLastTimeResp, error)
	// 捐献总数的变化
	UpdateGuildDonateNumSeq(ctx context.Context, in *UpdateGuildDonateNumSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 捐献TOPN的列表的变化
	UpdateGuildDonateTopUserSeq(ctx context.Context, in *UpdateGuildDonateTopUserSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取公会最后捐献更新时间
	GetGuildDonateLastTime(ctx context.Context, in *GetGuildDonateLastTimeReq, opts ...grpc.CallOption) (*GetGuildDonateLastTimeResp, error)
}

type guildTimeline2Client struct {
	cc *grpc.ClientConn
}

func NewGuildTimeline2Client(cc *grpc.ClientConn) GuildTimeline2Client {
	return &guildTimeline2Client{cc}
}

func (c *guildTimeline2Client) UpdateGuildCheckinNumSeq(ctx context.Context, in *UpdateGuildCheckinNumSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildCheckinNumSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildCheckinTopUserSeq(ctx context.Context, in *UpdateGuildCheckinTopUserSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildCheckinTopUserSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildBaseInfoSeq(ctx context.Context, in *UpdateGuildBaseInfoSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildBaseInfoSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildExtraInfoSeq(ctx context.Context, in *UpdateGuildExtraInfoSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildExtraInfoSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildNumberInfoSeq(ctx context.Context, in *UpdateGuildNumberInfoSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildNumberInfoSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildMemberInfo(ctx context.Context, in *UpdateGuildMemberInfoReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildMemberInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateCommonIncrTimelineSeq(ctx context.Context, in *UpdateCommonIncrTimelineSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateCommonIncrTimelineSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) GetCommonTimelineBySeq(ctx context.Context, in *GetCommonTimelineBySeqReq, opts ...grpc.CallOption) (*GetCommonTimelineBySeqResp, error) {
	out := new(GetCommonTimelineBySeqResp)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/GetCommonTimelineBySeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) BatchDeleteCommonTimeline(ctx context.Context, in *BatchDeleteCommonTimelineReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/BatchDeleteCommonTimeline", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) GetSyncUpdateInfoBySeq(ctx context.Context, in *GetSyncUpdateInfoBySeqReq, opts ...grpc.CallOption) (*GetSyncUpdateInfoBySeqResp, error) {
	out := new(GetSyncUpdateInfoBySeqResp)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/GetSyncUpdateInfoBySeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) CheckGuildDataIsExist(ctx context.Context, in *CheckGuildDataIsExistReq, opts ...grpc.CallOption) (*CheckGuildDataIsExistResp, error) {
	out := new(CheckGuildDataIsExistResp)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/CheckGuildDataIsExist", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) CleanGuildAllTimeline(ctx context.Context, in *CleanGuildAllTimelineReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/CleanGuildAllTimeline", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildGroupInfo(ctx context.Context, in *UpdateGuildGroupInfoReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildGroupInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) GetGuildCheckInLastTime(ctx context.Context, in *GetGuildCheckInLastTimeReq, opts ...grpc.CallOption) (*GetGuildCheckInLastTimeResp, error) {
	out := new(GetGuildCheckInLastTimeResp)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/GetGuildCheckInLastTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildDonateNumSeq(ctx context.Context, in *UpdateGuildDonateNumSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildDonateNumSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) UpdateGuildDonateTopUserSeq(ctx context.Context, in *UpdateGuildDonateTopUserSeqReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/UpdateGuildDonateTopUserSeq", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildTimeline2Client) GetGuildDonateLastTime(ctx context.Context, in *GetGuildDonateLastTimeReq, opts ...grpc.CallOption) (*GetGuildDonateLastTimeResp, error) {
	out := new(GetGuildDonateLastTimeResp)
	err := grpc.Invoke(ctx, "/GuildTimeline2.GuildTimeline2/GetGuildDonateLastTime", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for GuildTimeline2 service

type GuildTimeline2Server interface {
	// 签到总数的变化
	UpdateGuildCheckinNumSeq(context.Context, *UpdateGuildCheckinNumSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 签到TOPN的列表的变化
	UpdateGuildCheckinTopUserSeq(context.Context, *UpdateGuildCheckinTopUserSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 公会基本信息变化
	UpdateGuildBaseInfoSeq(context.Context, *UpdateGuildBaseInfoSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 公会附加信息变化
	UpdateGuildExtraInfoSeq(context.Context, *UpdateGuildExtraInfoSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 公会数字类信息变化
	UpdateGuildNumberInfoSeq(context.Context, *UpdateGuildNumberInfoSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	//
	// 公会成员信息变化(目前只针对有权限的管理员)
	// 注意该接口没有使用！！！！
	UpdateGuildMemberInfo(context.Context, *UpdateGuildMemberInfoReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 通用的增量时间线变化
	UpdateCommonIncrTimelineSeq(context.Context, *UpdateCommonIncrTimelineSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	GetCommonTimelineBySeq(context.Context, *GetCommonTimelineBySeqReq) (*GetCommonTimelineBySeqResp, error)
	BatchDeleteCommonTimeline(context.Context, *BatchDeleteCommonTimelineReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 根据SEQ序号获取最新SYNC数据
	GetSyncUpdateInfoBySeq(context.Context, *GetSyncUpdateInfoBySeqReq) (*GetSyncUpdateInfoBySeqResp, error)
	// 检测公会信息是否存在
	CheckGuildDataIsExist(context.Context, *CheckGuildDataIsExistReq) (*CheckGuildDataIsExistResp, error)
	// 清除公会全部timeline数据 只在公会解散时被调用
	CleanGuildAllTimeline(context.Context, *CleanGuildAllTimelineReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 该接口暂时不启用！！！
	// 公会群更新（新版不使用通用timeline增量结构存储）
	UpdateGuildGroupInfo(context.Context, *UpdateGuildGroupInfoReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取公会最后签到更新时间
	GetGuildCheckInLastTime(context.Context, *GetGuildCheckInLastTimeReq) (*GetGuildCheckInLastTimeResp, error)
	// 捐献总数的变化
	UpdateGuildDonateNumSeq(context.Context, *UpdateGuildDonateNumSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 捐献TOPN的列表的变化
	UpdateGuildDonateTopUserSeq(context.Context, *UpdateGuildDonateTopUserSeqReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
	// 获取公会最后捐献更新时间
	GetGuildDonateLastTime(context.Context, *GetGuildDonateLastTimeReq) (*GetGuildDonateLastTimeResp, error)
}

func RegisterGuildTimeline2Server(s *grpc.Server, srv GuildTimeline2Server) {
	s.RegisterService(&_GuildTimeline2_serviceDesc, srv)
}

func _GuildTimeline2_UpdateGuildCheckinNumSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildCheckinNumSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildCheckinNumSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildCheckinNumSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildCheckinNumSeq(ctx, req.(*UpdateGuildCheckinNumSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildCheckinTopUserSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildCheckinTopUserSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildCheckinTopUserSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildCheckinTopUserSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildCheckinTopUserSeq(ctx, req.(*UpdateGuildCheckinTopUserSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildBaseInfoSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildBaseInfoSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildBaseInfoSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildBaseInfoSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildBaseInfoSeq(ctx, req.(*UpdateGuildBaseInfoSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildExtraInfoSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildExtraInfoSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildExtraInfoSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildExtraInfoSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildExtraInfoSeq(ctx, req.(*UpdateGuildExtraInfoSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildNumberInfoSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildNumberInfoSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildNumberInfoSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildNumberInfoSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildNumberInfoSeq(ctx, req.(*UpdateGuildNumberInfoSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildMemberInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildMemberInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildMemberInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildMemberInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildMemberInfo(ctx, req.(*UpdateGuildMemberInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateCommonIncrTimelineSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCommonIncrTimelineSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateCommonIncrTimelineSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateCommonIncrTimelineSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateCommonIncrTimelineSeq(ctx, req.(*UpdateCommonIncrTimelineSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_GetCommonTimelineBySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCommonTimelineBySeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).GetCommonTimelineBySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/GetCommonTimelineBySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).GetCommonTimelineBySeq(ctx, req.(*GetCommonTimelineBySeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_BatchDeleteCommonTimeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchDeleteCommonTimelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).BatchDeleteCommonTimeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/BatchDeleteCommonTimeline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).BatchDeleteCommonTimeline(ctx, req.(*BatchDeleteCommonTimelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_GetSyncUpdateInfoBySeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSyncUpdateInfoBySeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).GetSyncUpdateInfoBySeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/GetSyncUpdateInfoBySeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).GetSyncUpdateInfoBySeq(ctx, req.(*GetSyncUpdateInfoBySeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_CheckGuildDataIsExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckGuildDataIsExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).CheckGuildDataIsExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/CheckGuildDataIsExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).CheckGuildDataIsExist(ctx, req.(*CheckGuildDataIsExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_CleanGuildAllTimeline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanGuildAllTimelineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).CleanGuildAllTimeline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/CleanGuildAllTimeline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).CleanGuildAllTimeline(ctx, req.(*CleanGuildAllTimelineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildGroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildGroupInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildGroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildGroupInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildGroupInfo(ctx, req.(*UpdateGuildGroupInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_GetGuildCheckInLastTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildCheckInLastTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).GetGuildCheckInLastTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/GetGuildCheckInLastTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).GetGuildCheckInLastTime(ctx, req.(*GetGuildCheckInLastTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildDonateNumSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildDonateNumSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildDonateNumSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildDonateNumSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildDonateNumSeq(ctx, req.(*UpdateGuildDonateNumSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_UpdateGuildDonateTopUserSeq_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGuildDonateTopUserSeqReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).UpdateGuildDonateTopUserSeq(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/UpdateGuildDonateTopUserSeq",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).UpdateGuildDonateTopUserSeq(ctx, req.(*UpdateGuildDonateTopUserSeqReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildTimeline2_GetGuildDonateLastTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildDonateLastTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildTimeline2Server).GetGuildDonateLastTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/GuildTimeline2.GuildTimeline2/GetGuildDonateLastTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildTimeline2Server).GetGuildDonateLastTime(ctx, req.(*GetGuildDonateLastTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildTimeline2_serviceDesc = grpc.ServiceDesc{
	ServiceName: "GuildTimeline2.GuildTimeline2",
	HandlerType: (*GuildTimeline2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateGuildCheckinNumSeq",
			Handler:    _GuildTimeline2_UpdateGuildCheckinNumSeq_Handler,
		},
		{
			MethodName: "UpdateGuildCheckinTopUserSeq",
			Handler:    _GuildTimeline2_UpdateGuildCheckinTopUserSeq_Handler,
		},
		{
			MethodName: "UpdateGuildBaseInfoSeq",
			Handler:    _GuildTimeline2_UpdateGuildBaseInfoSeq_Handler,
		},
		{
			MethodName: "UpdateGuildExtraInfoSeq",
			Handler:    _GuildTimeline2_UpdateGuildExtraInfoSeq_Handler,
		},
		{
			MethodName: "UpdateGuildNumberInfoSeq",
			Handler:    _GuildTimeline2_UpdateGuildNumberInfoSeq_Handler,
		},
		{
			MethodName: "UpdateGuildMemberInfo",
			Handler:    _GuildTimeline2_UpdateGuildMemberInfo_Handler,
		},
		{
			MethodName: "UpdateCommonIncrTimelineSeq",
			Handler:    _GuildTimeline2_UpdateCommonIncrTimelineSeq_Handler,
		},
		{
			MethodName: "GetCommonTimelineBySeq",
			Handler:    _GuildTimeline2_GetCommonTimelineBySeq_Handler,
		},
		{
			MethodName: "BatchDeleteCommonTimeline",
			Handler:    _GuildTimeline2_BatchDeleteCommonTimeline_Handler,
		},
		{
			MethodName: "GetSyncUpdateInfoBySeq",
			Handler:    _GuildTimeline2_GetSyncUpdateInfoBySeq_Handler,
		},
		{
			MethodName: "CheckGuildDataIsExist",
			Handler:    _GuildTimeline2_CheckGuildDataIsExist_Handler,
		},
		{
			MethodName: "CleanGuildAllTimeline",
			Handler:    _GuildTimeline2_CleanGuildAllTimeline_Handler,
		},
		{
			MethodName: "UpdateGuildGroupInfo",
			Handler:    _GuildTimeline2_UpdateGuildGroupInfo_Handler,
		},
		{
			MethodName: "GetGuildCheckInLastTime",
			Handler:    _GuildTimeline2_GetGuildCheckInLastTime_Handler,
		},
		{
			MethodName: "UpdateGuildDonateNumSeq",
			Handler:    _GuildTimeline2_UpdateGuildDonateNumSeq_Handler,
		},
		{
			MethodName: "UpdateGuildDonateTopUserSeq",
			Handler:    _GuildTimeline2_UpdateGuildDonateTopUserSeq_Handler,
		},
		{
			MethodName: "GetGuildDonateLastTime",
			Handler:    _GuildTimeline2_GetGuildDonateLastTime_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/guildtimeline2/guildtimeline2.proto",
}

func (m *StGuildGame) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGame) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GameId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.IconId)))
	i += copy(dAtA[i:], m.IconId)
	return i, nil
}

func (m *CheckInMem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckInMem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Days))
	return i, nil
}

func (m *DonateMem) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DonateMem) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Days))
	return i, nil
}

func (m *StGuildMember) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildMember) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Role))
	dAtA[i] = 0x22
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Remark)))
	i += copy(dAtA[i:], m.Remark)
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.SeqId))
	dAtA[i] = 0x32
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Account)))
	i += copy(dAtA[i:], m.Account)
	return i, nil
}

func (m *StGuildCheckinNumSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildCheckinNumSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.CheckinNum))
	return i, nil
}

func (m *StGuildCheckinTopUserSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildCheckinTopUserSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	if len(m.CheckinTopNList) > 0 {
		for _, msg := range m.CheckinTopNList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StGuildDonateNumSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildDonateNumSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.DonateNum))
	return i, nil
}

func (m *StGuildDonateTopUserSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildDonateTopUserSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	if len(m.DonateTopNList) > 0 {
		for _, msg := range m.DonateTopNList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StGuildBaseSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildBaseSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x20
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildDisplayId))
	dAtA[i] = 0x28
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildGroupID))
	dAtA[i] = 0x30
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GameSizelimit))
	dAtA[i] = 0x38
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.CreateDate))
	dAtA[i] = 0x42
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.GuildPrefix)))
	i += copy(dAtA[i:], m.GuildPrefix)
	dAtA[i] = 0x4a
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Name)))
	i += copy(dAtA[i:], m.Name)
	dAtA[i] = 0x52
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Desc)))
	i += copy(dAtA[i:], m.Desc)
	dAtA[i] = 0x5a
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.Manifesto)))
	i += copy(dAtA[i:], m.Manifesto)
	dAtA[i] = 0x60
	i++
	if m.IsNeedverify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *StGuildExtraSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildExtraSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.IconId)))
	i += copy(dAtA[i:], m.IconId)
	return i, nil
}

func (m *StGuildNumberDataSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildNumberDataSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.MemberCount))
	return i, nil
}

func (m *StGuildGroupSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGroupSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x20
	i++
	if m.IsDel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *StGuildBatchGroupSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildBatchGroupSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.GroupInfoList) > 0 {
		for _, msg := range m.GroupInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StGuildSpecialFlagSync) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildSpecialFlagSync) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Guildid))
	return i, nil
}

func (m *StGuildGameSyncInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StGuildGameSyncInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastSeqId))
	if len(m.GameList) > 0 {
		for _, msg := range m.GameList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *StTimelineDataGiftpkg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StTimelineDataGiftpkg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GameId))
	return i, nil
}

func (m *UpdateGuildCheckinNumSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildCheckinNumSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.NumInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("num_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.NumInfo.Size()))
		n1, err := m.NumInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *UpdateGuildCheckinTopUserSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildCheckinTopUserSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.TopInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("top_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.TopInfo.Size()))
		n2, err := m.TopInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *UpdateGuildDonateNumSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildDonateNumSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.NumInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("num_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.NumInfo.Size()))
		n3, err := m.NumInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	return i, nil
}

func (m *UpdateGuildDonateTopUserSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildDonateTopUserSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.TopInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("top_info")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.TopInfo.Size()))
		n4, err := m.TopInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n4
	}
	return i, nil
}

func (m *UpdateGuildBaseInfoSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildBaseInfoSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.Baseinfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("baseinfo")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Baseinfo.Size()))
		n5, err := m.Baseinfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n5
	}
	return i, nil
}

func (m *UpdateGuildExtraInfoSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildExtraInfoSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.Extrainfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("extrainfo")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Extrainfo.Size()))
		n6, err := m.Extrainfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n6
	}
	return i, nil
}

func (m *UpdateGuildNumberInfoSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildNumberInfoSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if m.NumberInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("numberInfo")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.NumberInfo.Size()))
		n7, err := m.NumberInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n7
	}
	return i, nil
}

func (m *UpdateGuildMemberInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildMemberInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Uid))
	if m.MemInfo == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("mem_info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.MemInfo.Size()))
		n8, err := m.MemInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n8
	}
	return i, nil
}

func (m *StCommonIncrTimelineData) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *StCommonIncrTimelineData) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.KeyPrefix)))
	i += copy(dAtA[i:], m.KeyPrefix)
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.KeyId))
	dAtA[i] = 0x18
	i++
	if m.IsDeleted {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.BinValue != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(len(m.BinValue)))
		i += copy(dAtA[i:], m.BinValue)
	}
	return i, nil
}

func (m *UpdateCommonIncrTimelineSeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateCommonIncrTimelineSeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.SeqId))
	if m.Data == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("data")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Data.Size()))
		n9, err := m.Data.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n9
	}
	return i, nil
}

func (m *GetCommonTimelineBySeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommonTimelineBySeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.SeqId))
	return i, nil
}

func (m *GetCommonTimelineBySeqResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetCommonTimelineBySeqResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SeqIdList) > 0 {
		for _, num := range m.SeqIdList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(num))
		}
	}
	if len(m.DataList) > 0 {
		for _, msg := range m.DataList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchDeleteCommonTimelineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchDeleteCommonTimelineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	if len(m.SeqIdList) > 0 {
		for _, num := range m.SeqIdList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintGuildtimeline2(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetSyncUpdateInfoBySeqReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSyncUpdateInfoBySeqReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.SeqId))
	return i, nil
}

func (m *GetSyncUpdateInfoBySeqResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSyncUpdateInfoBySeqResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.GuildBaseinf != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildBaseinf.Size()))
		n10, err := m.GuildBaseinf.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n10
	}
	if m.GuildExtrinf != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildExtrinf.Size()))
		n11, err := m.GuildExtrinf.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n11
	}
	if m.CheckinNum != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.CheckinNum.Size()))
		n12, err := m.CheckinNum.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n12
	}
	if m.CheckinTopn != nil {
		dAtA[i] = 0x22
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.CheckinTopn.Size()))
		n13, err := m.CheckinTopn.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n13
	}
	if m.GuildNumdata != nil {
		dAtA[i] = 0x2a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildNumdata.Size()))
		n14, err := m.GuildNumdata.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n14
	}
	if m.GroupInfo != nil {
		dAtA[i] = 0x32
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GroupInfo.Size()))
		n15, err := m.GroupInfo.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n15
	}
	if m.FlagSync != nil {
		dAtA[i] = 0x3a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.FlagSync.Size()))
		n16, err := m.FlagSync.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n16
	}
	if m.DonateNum != nil {
		dAtA[i] = 0x42
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.DonateNum.Size()))
		n17, err := m.DonateNum.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n17
	}
	if m.DonateTopn != nil {
		dAtA[i] = 0x4a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.DonateTopn.Size()))
		n18, err := m.DonateTopn.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n18
	}
	return i, nil
}

func (m *CheckGuildDataIsExistReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckGuildDataIsExistReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *CheckGuildDataIsExistResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckGuildDataIsExistResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	if m.Is_Exist {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *CleanGuildAllTimelineReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CleanGuildAllTimelineReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *UpdateGuildGroupInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UpdateGuildGroupInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GroupId))
	if m.Info == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("info")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.Info.Size()))
		n19, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n19
	}
	return i, nil
}

func (m *GetGuildCheckInLastTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildCheckInLastTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildCheckInLastTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildCheckInLastTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	return i, nil
}

func (m *GetGuildDonateLastTimeReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildDonateLastTimeReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	return i, nil
}

func (m *GetGuildDonateLastTimeResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGuildDonateLastTimeResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintGuildtimeline2(dAtA, i, uint64(m.LastUpdateTime))
	return i, nil
}

func encodeFixed64Guildtimeline2(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Guildtimeline2(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintGuildtimeline2(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *StGuildGame) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GameId))
	l = len(m.Name)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	l = len(m.IconId)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	return n
}

func (m *CheckInMem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.Uid))
	n += 1 + sovGuildtimeline2(uint64(m.Days))
	return n
}

func (m *DonateMem) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.Uid))
	n += 1 + sovGuildtimeline2(uint64(m.Days))
	return n
}

func (m *StGuildMember) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.Uid))
	l = len(m.Name)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	n += 1 + sovGuildtimeline2(uint64(m.Role))
	l = len(m.Remark)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	n += 1 + sovGuildtimeline2(uint64(m.SeqId))
	l = len(m.Account)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	return n
}

func (m *StGuildCheckinNumSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	n += 1 + sovGuildtimeline2(uint64(m.CheckinNum))
	return n
}

func (m *StGuildCheckinTopUserSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	if len(m.CheckinTopNList) > 0 {
		for _, e := range m.CheckinTopNList {
			l = e.Size()
			n += 1 + l + sovGuildtimeline2(uint64(l))
		}
	}
	return n
}

func (m *StGuildDonateNumSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	n += 1 + sovGuildtimeline2(uint64(m.DonateNum))
	return n
}

func (m *StGuildDonateTopUserSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	if len(m.DonateTopNList) > 0 {
		for _, e := range m.DonateTopNList {
			l = e.Size()
			n += 1 + l + sovGuildtimeline2(uint64(l))
		}
	}
	return n
}

func (m *StGuildBaseSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.GuildDisplayId))
	n += 1 + sovGuildtimeline2(uint64(m.GuildGroupID))
	n += 1 + sovGuildtimeline2(uint64(m.GameSizelimit))
	n += 1 + sovGuildtimeline2(uint64(m.CreateDate))
	l = len(m.GuildPrefix)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	l = len(m.Name)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	l = len(m.Desc)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	l = len(m.Manifesto)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	n += 2
	return n
}

func (m *StGuildExtraSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	l = len(m.IconId)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	return n
}

func (m *StGuildNumberDataSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	n += 1 + sovGuildtimeline2(uint64(m.MemberCount))
	return n
}

func (m *StGuildGroupSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GroupId))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 2
	return n
}

func (m *StGuildBatchGroupSyncInfo) Size() (n int) {
	var l int
	_ = l
	if len(m.GroupInfoList) > 0 {
		for _, e := range m.GroupInfoList {
			l = e.Size()
			n += 1 + l + sovGuildtimeline2(uint64(l))
		}
	}
	return n
}

func (m *StGuildSpecialFlagSync) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.Guildid))
	return n
}

func (m *StGuildGameSyncInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	n += 1 + sovGuildtimeline2(uint64(m.LastSeqId))
	if len(m.GameList) > 0 {
		for _, e := range m.GameList {
			l = e.Size()
			n += 1 + l + sovGuildtimeline2(uint64(l))
		}
	}
	return n
}

func (m *StTimelineDataGiftpkg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GameId))
	return n
}

func (m *UpdateGuildCheckinNumSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.NumInfo != nil {
		l = m.NumInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildCheckinTopUserSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.TopInfo != nil {
		l = m.TopInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildDonateNumSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.NumInfo != nil {
		l = m.NumInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildDonateTopUserSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.TopInfo != nil {
		l = m.TopInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildBaseInfoSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.Baseinfo != nil {
		l = m.Baseinfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildExtraInfoSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.Extrainfo != nil {
		l = m.Extrainfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildNumberInfoSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if m.NumberInfo != nil {
		l = m.NumberInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateGuildMemberInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.Uid))
	if m.MemInfo != nil {
		l = m.MemInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *StCommonIncrTimelineData) Size() (n int) {
	var l int
	_ = l
	l = len(m.KeyPrefix)
	n += 1 + l + sovGuildtimeline2(uint64(l))
	n += 1 + sovGuildtimeline2(uint64(m.KeyId))
	n += 2
	if m.BinValue != nil {
		l = len(m.BinValue)
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *UpdateCommonIncrTimelineSeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.SeqId))
	if m.Data != nil {
		l = m.Data.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *GetCommonTimelineBySeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.SeqId))
	return n
}

func (m *GetCommonTimelineBySeqResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SeqIdList) > 0 {
		for _, e := range m.SeqIdList {
			n += 1 + sovGuildtimeline2(uint64(e))
		}
	}
	if len(m.DataList) > 0 {
		for _, e := range m.DataList {
			l = e.Size()
			n += 1 + l + sovGuildtimeline2(uint64(l))
		}
	}
	return n
}

func (m *BatchDeleteCommonTimelineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	if len(m.SeqIdList) > 0 {
		for _, e := range m.SeqIdList {
			n += 1 + sovGuildtimeline2(uint64(e))
		}
	}
	return n
}

func (m *GetSyncUpdateInfoBySeqReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.SeqId))
	return n
}

func (m *GetSyncUpdateInfoBySeqResp) Size() (n int) {
	var l int
	_ = l
	if m.GuildBaseinf != nil {
		l = m.GuildBaseinf.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.GuildExtrinf != nil {
		l = m.GuildExtrinf.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.CheckinNum != nil {
		l = m.CheckinNum.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.CheckinTopn != nil {
		l = m.CheckinTopn.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.GuildNumdata != nil {
		l = m.GuildNumdata.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.GroupInfo != nil {
		l = m.GroupInfo.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.FlagSync != nil {
		l = m.FlagSync.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.DonateNum != nil {
		l = m.DonateNum.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	if m.DonateTopn != nil {
		l = m.DonateTopn.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *CheckGuildDataIsExistReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	return n
}

func (m *CheckGuildDataIsExistResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 2
	return n
}

func (m *CleanGuildAllTimelineReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	return n
}

func (m *UpdateGuildGroupInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.GroupId))
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovGuildtimeline2(uint64(l))
	}
	return n
}

func (m *GetGuildCheckInLastTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	return n
}

func (m *GetGuildCheckInLastTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	return n
}

func (m *GetGuildDonateLastTimeReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	return n
}

func (m *GetGuildDonateLastTimeResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovGuildtimeline2(uint64(m.GuildId))
	n += 1 + sovGuildtimeline2(uint64(m.LastUpdateTime))
	return n
}

func sovGuildtimeline2(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozGuildtimeline2(x uint64) (n int) {
	return sovGuildtimeline2(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *StGuildGame) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGame: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGame: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckInMem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckInMem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckInMem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Days", wireType)
			}
			m.Days = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Days |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("days")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DonateMem) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DonateMem: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DonateMem: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Days", wireType)
			}
			m.Days = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Days |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("days")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildMember) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildMember: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildMember: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Role", wireType)
			}
			m.Role = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Role |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Remark", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Remark = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Account", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Account = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("role")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("remark")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildCheckinNumSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildCheckinNumSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildCheckinNumSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinNum", wireType)
			}
			m.CheckinNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CheckinNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("checkin_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildCheckinTopUserSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildCheckinTopUserSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildCheckinTopUserSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinTopNList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CheckinTopNList = append(m.CheckinTopNList, &CheckInMem{})
			if err := m.CheckinTopNList[len(m.CheckinTopNList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildDonateNumSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildDonateNumSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildDonateNumSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DonateNum", wireType)
			}
			m.DonateNum = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DonateNum |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("donate_num")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildDonateTopUserSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildDonateTopUserSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildDonateTopUserSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DonateTopNList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DonateTopNList = append(m.DonateTopNList, &DonateMem{})
			if err := m.DonateTopNList[len(m.DonateTopNList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildBaseSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildBaseSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildBaseSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildDisplayId", wireType)
			}
			m.GuildDisplayId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildDisplayId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildGroupID", wireType)
			}
			m.GuildGroupID = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildGroupID |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameSizelimit", wireType)
			}
			m.GameSizelimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameSizelimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CreateDate", wireType)
			}
			m.CreateDate = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateDate |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildPrefix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildPrefix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Name", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Name = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Desc", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Desc = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Manifesto", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Manifesto = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsNeedverify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsNeedverify = bool(v != 0)
			hasFields[0] |= uint64(0x00000800)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_display_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_groupID")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_sizelimit")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("create_date")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_prefix")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("name")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("desc")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("manifesto")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_needverify")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildExtraSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildExtraSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildExtraSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IconId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.IconId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("icon_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildNumberDataSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildNumberDataSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildNumberDataSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberCount", wireType)
			}
			m.MemberCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemberCount |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("member_count")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildGroupSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGroupSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGroupSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDel = bool(v != 0)
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("isDel")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildBatchGroupSyncInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildBatchGroupSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildBatchGroupSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupInfoList = append(m.GroupInfoList, &StGuildGroupSyncInfo{})
			if err := m.GroupInfoList[len(m.GroupInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildSpecialFlagSync) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildSpecialFlagSync: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildSpecialFlagSync: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Guildid", wireType)
			}
			m.Guildid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Guildid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guildid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StGuildGameSyncInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stGuildGameSyncInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stGuildGameSyncInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastSeqId", wireType)
			}
			m.LastSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GameList = append(m.GameList, &StGuildGame{})
			if err := m.GameList[len(m.GameList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StTimelineDataGiftpkg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stTimelineDataGiftpkg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stTimelineDataGiftpkg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GameId", wireType)
			}
			m.GameId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GameId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("game_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildCheckinNumSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildCheckinNumSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildCheckinNumSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NumInfo == nil {
				m.NumInfo = &StGuildCheckinNumSyncInfo{}
			}
			if err := m.NumInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("num_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildCheckinTopUserSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildCheckinTopUserSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildCheckinTopUserSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TopInfo == nil {
				m.TopInfo = &StGuildCheckinTopUserSyncInfo{}
			}
			if err := m.TopInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("top_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildDonateNumSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildDonateNumSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildDonateNumSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NumInfo == nil {
				m.NumInfo = &StGuildDonateNumSyncInfo{}
			}
			if err := m.NumInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("num_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildDonateTopUserSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildDonateTopUserSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildDonateTopUserSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TopInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.TopInfo == nil {
				m.TopInfo = &StGuildDonateTopUserSyncInfo{}
			}
			if err := m.TopInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("top_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildBaseInfoSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildBaseInfoSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildBaseInfoSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Baseinfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Baseinfo == nil {
				m.Baseinfo = &StGuildBaseSyncInfo{}
			}
			if err := m.Baseinfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("baseinfo")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildExtraInfoSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildExtraInfoSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildExtraInfoSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Extrainfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Extrainfo == nil {
				m.Extrainfo = &StGuildExtraSyncInfo{}
			}
			if err := m.Extrainfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("extrainfo")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildNumberInfoSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildNumberInfoSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildNumberInfoSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field NumberInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.NumberInfo == nil {
				m.NumberInfo = &StGuildNumberDataSyncInfo{}
			}
			if err := m.NumberInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("numberInfo")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildMemberInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildMemberInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildMemberInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.MemInfo == nil {
				m.MemInfo = &StGuildMember{}
			}
			if err := m.MemInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mem_info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *StCommonIncrTimelineData) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: stCommonIncrTimelineData: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: stCommonIncrTimelineData: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeyPrefix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KeyPrefix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeyId", wireType)
			}
			m.KeyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.KeyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsDeleted", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsDeleted = bool(v != 0)
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BinValue", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.BinValue = append(m.BinValue[:0], dAtA[iNdEx:postIndex]...)
			if m.BinValue == nil {
				m.BinValue = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key_prefix")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_deleted")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateCommonIncrTimelineSeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateCommonIncrTimelineSeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateCommonIncrTimelineSeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Data", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Data == nil {
				m.Data = &StCommonIncrTimelineData{}
			}
			if err := m.Data.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("data")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommonTimelineBySeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommonTimelineBySeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommonTimelineBySeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetCommonTimelineBySeqResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetCommonTimelineBySeqResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetCommonTimelineBySeqResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildtimeline2
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SeqIdList = append(m.SeqIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildtimeline2
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildtimeline2
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildtimeline2
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SeqIdList = append(m.SeqIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqIdList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DataList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DataList = append(m.DataList, &StCommonIncrTimelineData{})
			if err := m.DataList[len(m.DataList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchDeleteCommonTimelineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchDeleteCommonTimelineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchDeleteCommonTimelineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildtimeline2
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SeqIdList = append(m.SeqIdList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowGuildtimeline2
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthGuildtimeline2
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowGuildtimeline2
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SeqIdList = append(m.SeqIdList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqIdList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSyncUpdateInfoBySeqReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSyncUpdateInfoBySeqReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSyncUpdateInfoBySeqReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSyncUpdateInfoBySeqResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSyncUpdateInfoBySeqResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSyncUpdateInfoBySeqResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildBaseinf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GuildBaseinf == nil {
				m.GuildBaseinf = &StGuildBaseSyncInfo{}
			}
			if err := m.GuildBaseinf.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildExtrinf", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GuildExtrinf == nil {
				m.GuildExtrinf = &StGuildExtraSyncInfo{}
			}
			if err := m.GuildExtrinf.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinNum", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckinNum == nil {
				m.CheckinNum = &StGuildCheckinNumSyncInfo{}
			}
			if err := m.CheckinNum.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CheckinTopn", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.CheckinTopn == nil {
				m.CheckinTopn = &StGuildCheckinTopUserSyncInfo{}
			}
			if err := m.CheckinTopn.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildNumdata", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GuildNumdata == nil {
				m.GuildNumdata = &StGuildNumberDataSyncInfo{}
			}
			if err := m.GuildNumdata.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupInfo", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.GroupInfo == nil {
				m.GroupInfo = &StGuildBatchGroupSyncInfo{}
			}
			if err := m.GroupInfo.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FlagSync", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.FlagSync == nil {
				m.FlagSync = &StGuildSpecialFlagSync{}
			}
			if err := m.FlagSync.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 8:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DonateNum", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DonateNum == nil {
				m.DonateNum = &StGuildDonateNumSyncInfo{}
			}
			if err := m.DonateNum.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DonateTopn", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.DonateTopn == nil {
				m.DonateTopn = &StGuildDonateTopUserSyncInfo{}
			}
			if err := m.DonateTopn.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckGuildDataIsExistReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckGuildDataIsExistReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckGuildDataIsExistReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckGuildDataIsExistResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckGuildDataIsExistResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckGuildDataIsExistResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Is_Exist", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.Is_Exist = bool(v != 0)
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("is_Exist")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CleanGuildAllTimelineReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CleanGuildAllTimelineReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CleanGuildAllTimelineReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UpdateGuildGroupInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UpdateGuildGroupInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UpdateGuildGroupInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &StGuildGroupSyncInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildCheckInLastTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildCheckInLastTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildCheckInLastTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildCheckInLastTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildCheckInLastTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildCheckInLastTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildDonateLastTimeReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildDonateLastTimeReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildDonateLastTimeReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGuildDonateLastTimeResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGuildDonateLastTimeResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGuildDonateLastTimeResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LastUpdateTime", wireType)
			}
			m.LastUpdateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LastUpdateTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipGuildtimeline2(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthGuildtimeline2
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("last_update_time")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipGuildtimeline2(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowGuildtimeline2
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowGuildtimeline2
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthGuildtimeline2
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start int = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowGuildtimeline2
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipGuildtimeline2(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthGuildtimeline2 = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowGuildtimeline2   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/guildtimeline2/guildtimeline2.proto", fileDescriptorGuildtimeline2)
}

var fileDescriptorGuildtimeline2 = []byte{
	// 2133 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x98, 0xdd, 0x6f, 0xdb, 0xd6,
	0x15, 0xc0, 0x7b, 0x25, 0x59, 0x1f, 0x47, 0xb6, 0x9b, 0xde, 0xd5, 0x29, 0x2d, 0x3b, 0xb6, 0xc6,
	0x66, 0x8d, 0x93, 0x34, 0x0e, 0x96, 0x87, 0x2e, 0x70, 0x15, 0x0f, 0x53, 0xe4, 0x29, 0x5a, 0x1b,
	0x23, 0x90, 0x9b, 0x01, 0x7b, 0x18, 0x04, 0x5a, 0xbc, 0x56, 0x08, 0x91, 0x14, 0xa3, 0x4b, 0x06,
	0xd6, 0x80, 0x61, 0x9f, 0x0f, 0x5b, 0xd7, 0x61, 0xc5, 0x86, 0x00, 0xc3, 0xd0, 0x0d, 0x03, 0xe6,
	0x6e, 0x18, 0xf6, 0xbe, 0x87, 0x3d, 0xec, 0x61, 0xc0, 0x80, 0x3e, 0xee, 0x2f, 0x18, 0x86, 0xec,
	0x61, 0xfe, 0x33, 0x86, 0x7b, 0x2f, 0x49, 0x91, 0x14, 0x49, 0x51, 0x45, 0xdd, 0x37, 0xfb, 0x9e,
	0x7b, 0xae, 0x7e, 0xe7, 0xdc, 0x7b, 0xbe, 0x08, 0xd7, 0xe8, 0xb8, 0x7f, 0x7b, 0xe0, 0x68, 0xba,
	0x6a, 0x6b, 0x06, 0xd1, 0x35, 0x93, 0xdc, 0x89, 0xfc, 0xbb, 0x6b, 0x8d, 0x47, 0xf6, 0x08, 0xaf,
	0xb6, 0xd9, 0xea, 0x7b, 0xde, 0x6a, 0xed, 0x6a, 0x7f, 0x64, 0x18, 0x23, 0xf3, 0xb6, 0xad, 0x3f,
	0xb3, 0xb4, 0xfe, 0x50, 0x27, 0xb7, 0xe9, 0xf0, 0xd8, 0xd1, 0x74, 0x5b, 0x33, 0xed, 0x89, 0x45,
	0x84, 0x96, 0x4c, 0xa0, 0x4a, 0x6d, 0xae, 0xd9, 0x56, 0x0c, 0x82, 0xaf, 0x40, 0x69, 0xa0, 0x18,
	0xa4, 0xa7, 0xa9, 0x12, 0xaa, 0xe7, 0x76, 0x56, 0x9a, 0x85, 0x4f, 0xfe, 0xbd, 0xfd, 0x52, 0xb7,
	0xc8, 0x16, 0x3b, 0x2a, 0x96, 0xa0, 0x60, 0x2a, 0x06, 0x91, 0x72, 0xf5, 0xdc, 0x4e, 0xc5, 0x95,
	0xf1, 0x15, 0xa6, 0xa8, 0xf5, 0x47, 0x26, 0x53, 0xcc, 0x07, 0x84, 0x45, 0xb6, 0xd8, 0x51, 0xe5,
	0x7d, 0x80, 0xfb, 0x4f, 0x48, 0x7f, 0xd8, 0x31, 0x1f, 0x12, 0x03, 0x5f, 0x86, 0xbc, 0x13, 0xf9,
	0x05, 0xb6, 0xc0, 0x8e, 0x57, 0x95, 0x09, 0xe5, 0xc7, 0x7b, 0x02, 0xbe, 0x22, 0xdf, 0x83, 0x4a,
	0x6b, 0x64, 0x2a, 0x36, 0xf9, 0x74, 0xea, 0x7f, 0x45, 0xb0, 0xe2, 0x9a, 0xf9, 0x90, 0x18, 0xc7,
	0x64, 0x9c, 0x76, 0x46, 0x82, 0x85, 0x12, 0x14, 0xc6, 0x23, 0x9d, 0x70, 0xf3, 0xfc, 0xd3, 0xd9,
	0x0a, 0xde, 0x84, 0xe2, 0x98, 0x18, 0xca, 0x78, 0x28, 0x15, 0x82, 0xa6, 0x8b, 0x35, 0xbc, 0x01,
	0x45, 0x4a, 0x9e, 0x32, 0xc7, 0x2c, 0x05, 0x34, 0x97, 0x28, 0x79, 0xda, 0x51, 0xf1, 0x16, 0x94,
	0x94, 0x7e, 0x7f, 0xe4, 0x98, 0xb6, 0x54, 0x0c, 0xe8, 0x7a, 0x8b, 0xf2, 0xaf, 0x11, 0xac, 0xbb,
	0xe0, 0xdc, 0x7f, 0x9a, 0x79, 0xe8, 0x18, 0x47, 0x13, 0xb3, 0xdf, 0x31, 0x4f, 0x46, 0x78, 0x17,
	0x2e, 0xe9, 0x0a, 0xb5, 0x7b, 0x8e, 0xa5, 0x2a, 0x36, 0xe9, 0xb1, 0x17, 0x11, 0xb2, 0x68, 0x95,
	0x49, 0x1f, 0x73, 0x21, 0x7b, 0x17, 0xf8, 0x2a, 0x54, 0xf9, 0x7e, 0x97, 0x27, 0xe8, 0xa7, 0x0a,
	0x13, 0x1c, 0x71, 0xa6, 0x2f, 0x41, 0xb5, 0x2f, 0x7e, 0xab, 0x67, 0x3a, 0x46, 0xc8, 0x5e, 0xe8,
	0xfb, 0x10, 0xf2, 0xdf, 0x11, 0x5c, 0x09, 0xa3, 0xbd, 0x37, 0xb2, 0x1e, 0x53, 0x32, 0xbe, 0x60,
	0xbc, 0x36, 0x60, 0x0f, 0xcf, 0x1e, 0x59, 0x3d, 0xb3, 0xa7, 0x6b, 0xd4, 0x96, 0xf2, 0xf5, 0xfc,
	0x4e, 0xf5, 0x4e, 0x6d, 0x37, 0x1c, 0x04, 0xbb, 0xd3, 0x47, 0xd7, 0x7d, 0xb9, 0xef, 0x53, 0x1e,
	0xbe, 0xab, 0x51, 0x5b, 0x7e, 0x8e, 0x40, 0x72, 0x0d, 0x10, 0x6f, 0xeb, 0xe2, 0x5d, 0xfb, 0x3a,
	0x80, 0xca, 0x7f, 0x6a, 0xc6, 0xb3, 0x15, 0xd5, 0x43, 0x90, 0xff, 0x86, 0x60, 0x33, 0xc4, 0xf5,
	0xf9, 0xf8, 0xb5, 0x05, 0xaf, 0xb8, 0x6c, 0x33, 0x6e, 0x5d, 0x8f, 0xba, 0xd5, 0x8f, 0xc5, 0xee,
	0xaa, 0xea, 0x21, 0x0a, 0xa7, 0xfe, 0x2f, 0x0f, 0x5f, 0x70, 0xe1, 0x9b, 0x0a, 0x25, 0x17, 0xcc,
	0xbc, 0x0d, 0x65, 0x9e, 0x0b, 0xbd, 0xb4, 0xe3, 0x6d, 0x29, 0xf1, 0xd5, 0x8e, 0xca, 0x7e, 0x56,
	0x6c, 0x50, 0x35, 0x6a, 0xe9, 0xca, 0x84, 0x6d, 0x2c, 0x04, 0x7f, 0x96, 0x4b, 0x5b, 0x42, 0xd8,
	0x51, 0xf1, 0x75, 0x58, 0x11, 0xfb, 0x07, 0xe3, 0x91, 0x63, 0x75, 0x5a, 0xa1, 0x98, 0x5d, 0xe6,
	0xa2, 0xb6, 0x90, 0xe0, 0x9b, 0xb0, 0xca, 0x53, 0x25, 0xd5, 0xbe, 0x43, 0x74, 0xcd, 0xd0, 0x44,
	0x04, 0x7b, 0x7b, 0x57, 0x98, 0xec, 0xc8, 0x13, 0xf1, 0x98, 0x1a, 0x13, 0x66, 0x39, 0xb3, 0x50,
	0x2a, 0x85, 0x62, 0x8a, 0x0b, 0x5a, 0x8a, 0x4d, 0xf0, 0x35, 0x10, 0xbf, 0xd1, 0xb3, 0xc6, 0xe4,
	0x44, 0x3b, 0x95, 0xca, 0x81, 0x9c, 0x50, 0xe5, 0x92, 0x47, 0x5c, 0xe0, 0xa7, 0xa9, 0x4a, 0x5c,
	0x9a, 0x52, 0x09, 0xed, 0x4b, 0x10, 0x94, 0xb0, 0x15, 0x2c, 0x43, 0xc5, 0x50, 0x4c, 0xed, 0x84,
	0x50, 0x7b, 0x24, 0x55, 0x03, 0xe2, 0xe9, 0x32, 0xb3, 0x5f, 0xa3, 0x3d, 0x93, 0x10, 0xf5, 0x19,
	0x19, 0x6b, 0x27, 0x13, 0x69, 0xb9, 0x9e, 0xdb, 0x29, 0x7b, 0xf6, 0x6b, 0xf4, 0xd0, 0x97, 0xc8,
	0x3f, 0x43, 0xf0, 0xaa, 0x7b, 0xd3, 0x07, 0xa7, 0xf6, 0x58, 0xb9, 0xe0, 0xab, 0x9e, 0x53, 0x60,
	0x7e, 0x33, 0x4d, 0x94, 0x87, 0x0e, 0xcb, 0xf0, 0x2d, 0xc5, 0xbe, 0x68, 0xa4, 0x6b, 0xb0, 0x6c,
	0xf0, 0x6a, 0xd2, 0x13, 0x19, 0x3c, 0xf8, 0x02, 0xab, 0x42, 0x72, 0x9f, 0x67, 0xf1, 0x3f, 0x4f,
	0x5d, 0xc5, 0x5f, 0x8f, 0xcf, 0xc5, 0xde, 0x2f, 0x5b, 0x88, 0xd6, 0xdb, 0x92, 0x78, 0x7e, 0x6a,
	0x46, 0x90, 0x38, 0xf3, 0xf2, 0x29, 0xe6, 0xd5, 0x60, 0x49, 0xa3, 0x2d, 0xa2, 0xf3, 0x50, 0xf0,
	0x6e, 0x57, 0x2c, 0xc9, 0x4f, 0x7c, 0x3f, 0x36, 0x15, 0xbb, 0xff, 0x24, 0xcc, 0xfb, 0x0e, 0xac,
	0x0a, 0x32, 0xf3, 0x64, 0x24, 0x12, 0x04, 0xe2, 0x09, 0xe2, 0x6a, 0x34, 0x41, 0xc4, 0x59, 0xdb,
	0x5d, 0xf1, 0x75, 0x79, 0xaa, 0xb8, 0x0b, 0x97, 0xdd, 0x6d, 0x47, 0x16, 0xe9, 0x6b, 0x8a, 0xfe,
	0x75, 0x5d, 0x19, 0xb0, 0xcd, 0xac, 0x2a, 0xf2, 0xc7, 0x3e, 0xe3, 0x15, 0xb1, 0x28, 0x7f, 0x8c,
	0xfc, 0x24, 0xc3, 0xba, 0x96, 0x0b, 0xbe, 0xe6, 0xbb, 0x50, 0xe1, 0x81, 0x1e, 0x48, 0x88, 0x1b,
	0x49, 0xf6, 0x2a, 0x06, 0xe9, 0x96, 0xd9, 0x6e, 0x6e, 0xe1, 0x5b, 0xb0, 0x46, 0x6d, 0x6f, 0x13,
	0x7b, 0x90, 0x6d, 0xed, 0xc4, 0xb6, 0x86, 0x83, 0x39, 0x6d, 0x96, 0xfc, 0x63, 0x04, 0x1b, 0x02,
	0x33, 0x5a, 0xf9, 0xc9, 0xd3, 0x2e, 0x79, 0x1a, 0x4a, 0x7b, 0x28, 0x2e, 0xed, 0xb5, 0xa0, 0x6c,
	0x3a, 0x46, 0x4f, 0x33, 0x4f, 0x46, 0xdc, 0xaa, 0xea, 0x9d, 0xeb, 0x09, 0xc4, 0xb3, 0x5d, 0x45,
	0xb7, 0x64, 0x3a, 0x06, 0xfb, 0x43, 0xfe, 0x00, 0xc1, 0xf6, 0x2c, 0x86, 0x57, 0x8d, 0x32, 0xa2,
	0x3c, 0x80, 0x32, 0xab, 0x27, 0x01, 0x94, 0x5b, 0xe9, 0x28, 0x91, 0x6a, 0xd7, 0x2d, 0xd9, 0x23,
	0xfe, 0x66, 0xe4, 0x1f, 0x22, 0xa8, 0x05, 0x70, 0xa6, 0x35, 0x3b, 0x23, 0xc9, 0xfd, 0x19, 0xa7,
	0xec, 0x24, 0x90, 0xcc, 0xb4, 0x03, 0x53, 0x9f, 0xbc, 0x8f, 0x60, 0x6b, 0x06, 0x62, 0x41, 0x97,
	0xb4, 0x67, 0x5c, 0xf2, 0x66, 0x2a, 0x48, 0xa2, 0x47, 0xbe, 0x0b, 0xeb, 0x01, 0x16, 0x56, 0x6f,
	0xd9, 0x72, 0x56, 0x8c, 0xaf, 0x42, 0xf9, 0x58, 0xa1, 0x24, 0x80, 0xf1, 0x7a, 0x02, 0x46, 0xb0,
	0x92, 0x77, 0x7d, 0x25, 0xf9, 0x07, 0xe1, 0x0b, 0xe1, 0x55, 0x60, 0x11, 0x80, 0x26, 0x54, 0x08,
	0xd3, 0x09, 0x10, 0x24, 0x25, 0x92, 0x50, 0x85, 0xe9, 0x4e, 0xd5, 0xe4, 0x9f, 0x86, 0x43, 0x45,
	0xe4, 0xfe, 0x45, 0x20, 0x3a, 0x00, 0xa6, 0xaf, 0x34, 0x27, 0x58, 0x66, 0x2b, 0x4b, 0x37, 0xa0,
	0x2c, 0xff, 0x1c, 0x81, 0x14, 0x60, 0x11, 0x93, 0x06, 0xdf, 0x95, 0x05, 0xc4, 0x9d, 0x48, 0x72,
	0xd1, 0x89, 0xe4, 0x2e, 0x94, 0x0d, 0xe2, 0x3e, 0xdb, 0x3c, 0xc7, 0xbb, 0x92, 0x80, 0x27, 0x7e,
	0xb0, 0x5b, 0x32, 0x88, 0x78, 0xab, 0x67, 0xbc, 0xc1, 0xbd, 0xcf, 0xc7, 0xc0, 0x8e, 0xd9, 0x1f,
	0x07, 0x33, 0x11, 0x6b, 0x45, 0x87, 0x64, 0xe2, 0x35, 0x1a, 0x28, 0xd8, 0x0e, 0x0c, 0xc9, 0xc4,
	0x6d, 0x33, 0x36, 0xa0, 0xc8, 0x36, 0x45, 0xb0, 0x96, 0x86, 0x64, 0x22, 0x9a, 0x59, 0x8d, 0xf6,
	0x54, 0xa2, 0x13, 0x9b, 0x88, 0xa2, 0xec, 0x95, 0x92, 0x0a, 0x2f, 0x25, 0x6c, 0x19, 0x7f, 0x11,
	0x2a, 0xc7, 0x9a, 0xd9, 0x7b, 0xa6, 0xe8, 0x0e, 0x91, 0x0a, 0x75, 0xb4, 0xb3, 0xec, 0xee, 0x29,
	0x1f, 0x6b, 0xe6, 0x37, 0xd9, 0xaa, 0xfc, 0x91, 0x1f, 0x52, 0xb3, 0xa8, 0x59, 0x6f, 0x71, 0x3a,
	0x64, 0xe5, 0x66, 0x87, 0xac, 0x06, 0x9b, 0x0b, 0x6d, 0xc5, 0xf5, 0x5e, 0x4c, 0xd0, 0xc7, 0xbb,
	0xa8, 0xcb, 0xb5, 0xe4, 0x6f, 0xc1, 0x7a, 0x9b, 0xb8, 0x5b, 0x3c, 0x71, 0x73, 0xf2, 0x59, 0x80,
	0xc9, 0x3f, 0x42, 0x50, 0x4b, 0x3a, 0x9b, 0x5a, 0x78, 0x0b, 0xaa, 0x42, 0x77, 0x5a, 0x6a, 0x57,
	0xba, 0x15, 0xae, 0xca, 0xca, 0x0b, 0x3e, 0x80, 0x0a, 0x23, 0x14, 0xd2, 0x1c, 0x2f, 0x4c, 0xd9,
	0x8d, 0x2b, 0x33, 0x55, 0x5e, 0xa5, 0x7a, 0xb0, 0xc9, 0x4b, 0xbd, 0xb8, 0xb2, 0x30, 0x4c, 0x26,
	0x1b, 0x23, 0x9c, 0xb9, 0x08, 0xa7, 0xeb, 0x41, 0x16, 0x32, 0xe2, 0x9a, 0xd9, 0xe3, 0xfc, 0x8c,
	0x3c, 0xf8, 0xcf, 0x25, 0xee, 0xc1, 0xd8, 0xb3, 0xa9, 0x85, 0x1f, 0x78, 0xed, 0xbc, 0x9b, 0xb3,
	0x24, 0x54, 0x47, 0x59, 0xf3, 0x9c, 0xe8, 0xc4, 0x9b, 0x42, 0x11, 0x77, 0xbc, 0x93, 0x58, 0xea,
	0x61, 0x27, 0xe5, 0xf8, 0x49, 0xd9, 0xf2, 0x95, 0x38, 0xea, 0x40, 0x68, 0xe2, 0x6f, 0x44, 0xe7,
	0x6b, 0xb4, 0x58, 0x7d, 0x0e, 0x0c, 0xe1, 0xf8, 0x11, 0x2c, 0x07, 0x86, 0x61, 0x93, 0x47, 0xd8,
	0xc2, 0x15, 0xb6, 0x3a, 0x9d, 0x8c, 0x4d, 0x7c, 0xe8, 0x19, 0x6a, 0x3a, 0x06, 0x8f, 0x9a, 0xa5,
	0x54, 0xbe, 0x98, 0x94, 0x28, 0xac, 0x3d, 0x14, 0xea, 0xf8, 0x01, 0x80, 0xdb, 0xe2, 0xb2, 0x04,
	0x56, 0x4c, 0x3d, 0x6c, 0xb6, 0xe3, 0xec, 0x56, 0xfc, 0x9e, 0x11, 0x37, 0xa1, 0x7c, 0xe2, 0x76,
	0x88, 0x52, 0x89, 0x9f, 0xf3, 0x46, 0xc2, 0x39, 0x91, 0x7e, 0xb2, 0xeb, 0xeb, 0xe1, 0x76, 0x68,
	0x00, 0x2f, 0xf3, 0x53, 0xb2, 0x77, 0x01, 0xd3, 0x21, 0x1d, 0x3f, 0x84, 0xea, 0x74, 0x5a, 0x36,
	0xa5, 0x0a, 0x3f, 0x69, 0xb1, 0x32, 0x0e, 0xfe, 0xe8, 0x6c, 0xca, 0x6f, 0x83, 0xc4, 0x2f, 0x47,
	0x6c, 0x57, 0x6c, 0xa5, 0x43, 0x0f, 0x4e, 0x35, 0x6a, 0x67, 0x89, 0x10, 0xf9, 0xdb, 0xb0, 0x9e,
	0xa0, 0x4c, 0xad, 0xf9, 0xf1, 0xb5, 0x0d, 0x65, 0x8d, 0xf6, 0xb8, 0x02, 0x8f, 0x30, 0x2f, 0x89,
	0x97, 0x34, 0x71, 0x0a, 0x67, 0xd3, 0x89, 0x62, 0xf2, 0xe3, 0xbf, 0xa6, 0xeb, 0x8b, 0xe4, 0x06,
	0xf9, 0x39, 0x82, 0xd7, 0x02, 0x35, 0xb1, 0xed, 0xdd, 0x66, 0xa6, 0xd0, 0x0f, 0x8e, 0x47, 0xb9,
	0xb8, 0xf1, 0xe8, 0x2e, 0x14, 0x02, 0x75, 0x31, 0xdb, 0x14, 0xc2, 0x35, 0xe4, 0x7b, 0x3c, 0x6f,
	0x4c, 0xa3, 0xa2, 0x63, 0xbe, 0xab, 0x88, 0x5e, 0x3d, 0x93, 0x59, 0x26, 0x6c, 0x24, 0xaa, 0x67,
	0x71, 0x7a, 0xdc, 0xa4, 0x92, 0x4b, 0x9e, 0x54, 0xe4, 0x06, 0x4f, 0xa1, 0x81, 0xc7, 0xb4, 0x10,
	0xad, 0x31, 0x35, 0x36, 0xaa, 0x7d, 0x01, 0xb0, 0x77, 0xfe, 0xb2, 0x06, 0x91, 0x8f, 0xd1, 0xf8,
	0x77, 0xe1, 0xd6, 0x28, 0x34, 0xd1, 0xe0, 0x9b, 0xd1, 0x7b, 0x4b, 0x99, 0x7d, 0x6a, 0x9b, 0xbb,
	0xfe, 0x07, 0xed, 0xdd, 0xa3, 0x77, 0x9a, 0xe2, 0x83, 0xf6, 0x81, 0x61, 0xd9, 0x93, 0xde, 0xa3,
	0xa6, 0xfc, 0x95, 0xef, 0x9f, 0x9d, 0xe7, 0xd1, 0xfb, 0x67, 0xe7, 0xf9, 0xe2, 0x60, 0x8f, 0xee,
	0x9d, 0xee, 0xfd, 0xf2, 0xec, 0x3c, 0x2f, 0xdf, 0x1a, 0xd4, 0x1b, 0x9e, 0x89, 0xfb, 0xf5, 0x5b,
	0xb4, 0xde, 0x10, 0x15, 0x65, 0xbf, 0x7e, 0xeb, 0xb4, 0xde, 0x30, 0x1d, 0x63, 0x1f, 0x7f, 0x8c,
	0x60, 0x33, 0x6d, 0xd6, 0xc1, 0xb7, 0xe7, 0x43, 0x86, 0xc6, 0x80, 0x39, 0xa0, 0x6f, 0x31, 0xd0,
	0x1c, 0x03, 0x5d, 0x62, 0xa0, 0xce, 0x3c, 0x4e, 0xa7, 0xde, 0x70, 0x34, 0x75, 0x1f, 0xff, 0x02,
	0xc1, 0xe5, 0xf8, 0x9e, 0x1f, 0x5f, 0x4f, 0x21, 0x0c, 0xcf, 0x06, 0x73, 0xd8, 0xde, 0x64, 0x6c,
	0x79, 0xc6, 0x56, 0x60, 0x6c, 0x0c, 0x6d, 0x3d, 0x11, 0x0d, 0xff, 0x3e, 0x1c, 0xe1, 0xc1, 0x29,
	0x00, 0xdf, 0x48, 0x41, 0x8a, 0x8c, 0x0b, 0x73, 0x98, 0xde, 0x66, 0x4c, 0x85, 0xc8, 0xc5, 0xbe,
	0x91, 0x7e, 0xb1, 0xee, 0xf7, 0xa2, 0x7d, 0x7c, 0x16, 0x7e, 0x7d, 0xa1, 0x21, 0x21, 0xf5, 0xf5,
	0x45, 0xc7, 0x89, 0x39, 0x90, 0xf7, 0x18, 0xe4, 0x52, 0x04, 0x72, 0x27, 0x1d, 0x52, 0x7c, 0x27,
	0x3a, 0x64, 0x6f, 0xf0, 0x8f, 0x08, 0xd6, 0x62, 0xe7, 0x07, 0xbc, 0x93, 0xc2, 0x18, 0x1a, 0x33,
	0xe6, 0x00, 0x1e, 0x30, 0xc0, 0x22, 0x03, 0x2c, 0x0f, 0xf6, 0x9c, 0x3d, 0xba, 0xa7, 0x72, 0xc4,
	0xdd, 0x08, 0xa2, 0xfb, 0xd8, 0xc2, 0xac, 0x6a, 0xbd, 0xa1, 0xd1, 0x5e, 0x8b, 0xe8, 0xfb, 0xf8,
	0x1f, 0xfe, 0xd0, 0x15, 0xdb, 0xb1, 0xe3, 0xdd, 0x78, 0xdc, 0xa4, 0xf6, 0x7e, 0x0e, 0xf4, 0x63,
	0x06, 0x5d, 0x62, 0xd0, 0xc0, 0xbc, 0x6a, 0xed, 0x39, 0x2e, 0x76, 0x23, 0xc5, 0xb3, 0x56, 0xbd,
	0x21, 0x46, 0x1b, 0x61, 0x4e, 0xc0, 0x02, 0x31, 0xb0, 0xec, 0xe3, 0xdf, 0x22, 0xb8, 0x1c, 0xdf,
	0x7c, 0xcf, 0x46, 0x52, 0xe2, 0x00, 0x50, 0xbb, 0x91, 0x75, 0x2b, 0xb5, 0x44, 0x5c, 0x95, 0xb3,
	0xc6, 0xd5, 0x73, 0x04, 0xeb, 0x89, 0x7d, 0x39, 0x9e, 0x69, 0x35, 0xd2, 0x5a, 0xf8, 0x2c, 0xf1,
	0x5e, 0xc9, 0xca, 0xe5, 0xfa, 0x2d, 0xa6, 0xe5, 0x8e, 0xf5, 0x5b, 0x7c, 0xdb, 0x1f, 0xeb, 0xb7,
	0x84, 0x2e, 0x5e, 0xf0, 0x41, 0x56, 0xbe, 0x0f, 0x10, 0xac, 0xc5, 0xb6, 0x43, 0xb3, 0x51, 0x94,
	0xd4, 0x72, 0xd5, 0xae, 0x67, 0xdc, 0x49, 0x2d, 0x79, 0x8b, 0xc1, 0x55, 0x19, 0x5c, 0x6e, 0xc0,
	0xd1, 0x56, 0x42, 0x68, 0xf8, 0x7b, 0xb0, 0x16, 0xdb, 0x3d, 0xc5, 0xd0, 0x24, 0x34, 0x59, 0x73,
	0x6e, 0x8f, 0x03, 0x2c, 0x27, 0x03, 0xfc, 0x09, 0xc1, 0xab, 0x71, 0x1d, 0x18, 0xbe, 0x96, 0x92,
	0x54, 0x82, 0x7d, 0xda, 0x9c, 0xdf, 0x7f, 0xc0, 0x7e, 0x7f, 0xc5, 0xcd, 0x29, 0x96, 0x9f, 0xf6,
	0xbe, 0x1c, 0xb9, 0x21, 0xab, 0xde, 0xf0, 0x7a, 0x39, 0xff, 0xbe, 0xfc, 0x44, 0x4d, 0x55, 0x96,
	0x56, 0x7e, 0x85, 0xe0, 0xb5, 0x84, 0xae, 0x0a, 0xc7, 0xbd, 0x97, 0x84, 0xee, 0xad, 0x76, 0x33,
	0xf3, 0x5e, 0x6a, 0xc9, 0xdb, 0x0c, 0x7f, 0x35, 0xe0, 0xbe, 0xd5, 0x30, 0x38, 0xfe, 0x28, 0x5c,
	0xdf, 0x82, 0x9f, 0x1d, 0x53, 0xeb, 0x5b, 0xe4, 0xfb, 0x64, 0x96, 0xc6, 0xe5, 0xe5, 0x4f, 0xd1,
	0xb8, 0xfc, 0x21, 0xfc, 0x01, 0x2c, 0xfa, 0x41, 0x32, 0x29, 0x17, 0x27, 0x7d, 0xbd, 0xcc, 0xd2,
	0xb6, 0x5c, 0x5a, 0xbc, 0x6d, 0xf9, 0x50, 0x24, 0x8d, 0x98, 0x16, 0x34, 0x36, 0x69, 0xc4, 0x37,
	0xba, 0xb5, 0x1b, 0x59, 0xb7, 0x7a, 0xf7, 0xfa, 0x4a, 0xf2, 0xbd, 0xd6, 0x8a, 0x3f, 0x39, 0x3b,
	0xcf, 0x9f, 0x3d, 0x6b, 0x5e, 0xfa, 0xe4, 0xc5, 0x16, 0xfa, 0xd7, 0x8b, 0x2d, 0xf4, 0x9f, 0x17,
	0x5b, 0xe8, 0xc3, 0xff, 0x6e, 0xbd, 0xf4, 0xff, 0x00, 0x00, 0x00, 0xff, 0xff, 0xca, 0x3a, 0x9b,
	0x35, 0x73, 0x21, 0x00, 0x00,
}
