// Code generated by protoc-gen-go. DO NOT EDIT.
// source: mijing-trifle/mijing-trifle.proto

package mijing_trifle // import "golang.52tt.com/protocol/services/mijing-trifle"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type FinishMode int32

const (
	FinishMode_FINISH_MODE_UNKNOWN           FinishMode = 0
	FinishMode_FINISH_MODE_ONLY_ONCE_FOREVER FinishMode = 1
	FinishMode_FINISH_MODE_ONLY_ONCE_DAILY   FinishMode = 2
	FinishMode_FINISH_MODE_MULTI_TIME_DAILY  FinishMode = 3
)

var FinishMode_name = map[int32]string{
	0: "FINISH_MODE_UNKNOWN",
	1: "FINISH_MODE_ONLY_ONCE_FOREVER",
	2: "FINISH_MODE_ONLY_ONCE_DAILY",
	3: "FINISH_MODE_MULTI_TIME_DAILY",
}
var FinishMode_value = map[string]int32{
	"FINISH_MODE_UNKNOWN":           0,
	"FINISH_MODE_ONLY_ONCE_FOREVER": 1,
	"FINISH_MODE_ONLY_ONCE_DAILY":   2,
	"FINISH_MODE_MULTI_TIME_DAILY":  3,
}

func (x FinishMode) String() string {
	return proto.EnumName(FinishMode_name, int32(x))
}
func (FinishMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{0}
}

type DisplayMode int32

const (
	DisplayMode_DISPLAY_MODE_UNKNOWN       DisplayMode = 0
	DisplayMode_DISPLAY_MODE_ALWAYS        DisplayMode = 1
	DisplayMode_DISPLAY_MODE_WHEN_FINISHED DisplayMode = 2
	DisplayMode_DISPLAY_MODE_NEVER_SHOW    DisplayMode = 3
)

var DisplayMode_name = map[int32]string{
	0: "DISPLAY_MODE_UNKNOWN",
	1: "DISPLAY_MODE_ALWAYS",
	2: "DISPLAY_MODE_WHEN_FINISHED",
	3: "DISPLAY_MODE_NEVER_SHOW",
}
var DisplayMode_value = map[string]int32{
	"DISPLAY_MODE_UNKNOWN":       0,
	"DISPLAY_MODE_ALWAYS":        1,
	"DISPLAY_MODE_WHEN_FINISHED": 2,
	"DISPLAY_MODE_NEVER_SHOW":    3,
}

func (x DisplayMode) String() string {
	return proto.EnumName(DisplayMode_name, int32(x))
}
func (DisplayMode) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{1}
}

// 触发任务的事件类型
type EventType int32

const (
	EventType_EVENT_TYPE_COMMON    EventType = 0
	EventType_EVENT_TYPE_REGISTER  EventType = 1
	EventType_EVENT_TYPE_PASS_TASK EventType = 2
)

var EventType_name = map[int32]string{
	0: "EVENT_TYPE_COMMON",
	1: "EVENT_TYPE_REGISTER",
	2: "EVENT_TYPE_PASS_TASK",
}
var EventType_value = map[string]int32{
	"EVENT_TYPE_COMMON":    0,
	"EVENT_TYPE_REGISTER":  1,
	"EVENT_TYPE_PASS_TASK": 2,
}

func (x EventType) String() string {
	return proto.EnumName(EventType_name, int32(x))
}
func (EventType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{2}
}

type TrifleStatus int32

const (
	TrifleStatus_TRIFLE_STATUS_UNSPECIFIED    TrifleStatus = 0
	TrifleStatus_TRIFLE_STATUS_LOCKED         TrifleStatus = 1
	TrifleStatus_TRIFLE_STATUS_WAITING_ACCEPT TrifleStatus = 2
	TrifleStatus_TRIFLE_STATUS_LIGHTED        TrifleStatus = 3
)

var TrifleStatus_name = map[int32]string{
	0: "TRIFLE_STATUS_UNSPECIFIED",
	1: "TRIFLE_STATUS_LOCKED",
	2: "TRIFLE_STATUS_WAITING_ACCEPT",
	3: "TRIFLE_STATUS_LIGHTED",
}
var TrifleStatus_value = map[string]int32{
	"TRIFLE_STATUS_UNSPECIFIED":    0,
	"TRIFLE_STATUS_LOCKED":         1,
	"TRIFLE_STATUS_WAITING_ACCEPT": 2,
	"TRIFLE_STATUS_LIGHTED":        3,
}

func (x TrifleStatus) String() string {
	return proto.EnumName(TrifleStatus_name, int32(x))
}
func (TrifleStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{3}
}

type TrifleVisualStyle_Platform int32

const (
	TrifleVisualStyle_PLATFORM_UNSPECIFIED TrifleVisualStyle_Platform = 0
	TrifleVisualStyle_PLATFORM_IOS         TrifleVisualStyle_Platform = 1
	TrifleVisualStyle_PLATFORM_ANDROID     TrifleVisualStyle_Platform = 2
)

var TrifleVisualStyle_Platform_name = map[int32]string{
	0: "PLATFORM_UNSPECIFIED",
	1: "PLATFORM_IOS",
	2: "PLATFORM_ANDROID",
}
var TrifleVisualStyle_Platform_value = map[string]int32{
	"PLATFORM_UNSPECIFIED": 0,
	"PLATFORM_IOS":         1,
	"PLATFORM_ANDROID":     2,
}

func (x TrifleVisualStyle_Platform) String() string {
	return proto.EnumName(TrifleVisualStyle_Platform_name, int32(x))
}
func (TrifleVisualStyle_Platform) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{22, 0}
}

// 数据源
type GetTrifleVisualStyleListRequest_DataSource int32

const (
	GetTrifleVisualStyleListRequest_DATA_SOURCE_UNSPECIFIED GetTrifleVisualStyleListRequest_DataSource = 0
	GetTrifleVisualStyleListRequest_DATA_SOURCE_LOCAL_CACHE GetTrifleVisualStyleListRequest_DataSource = 1
	GetTrifleVisualStyleListRequest_DATA_SOURCE_MONGODB     GetTrifleVisualStyleListRequest_DataSource = 2
)

var GetTrifleVisualStyleListRequest_DataSource_name = map[int32]string{
	0: "DATA_SOURCE_UNSPECIFIED",
	1: "DATA_SOURCE_LOCAL_CACHE",
	2: "DATA_SOURCE_MONGODB",
}
var GetTrifleVisualStyleListRequest_DataSource_value = map[string]int32{
	"DATA_SOURCE_UNSPECIFIED": 0,
	"DATA_SOURCE_LOCAL_CACHE": 1,
	"DATA_SOURCE_MONGODB":     2,
}

func (x GetTrifleVisualStyleListRequest_DataSource) String() string {
	return proto.EnumName(GetTrifleVisualStyleListRequest_DataSource_name, int32(x))
}
func (GetTrifleVisualStyleListRequest_DataSource) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{27, 0}
}

// 生效状态
type GetTrifleVisualStyleListRequest_ActiveStatus int32

const (
	GetTrifleVisualStyleListRequest_ACTIVE_STATUS_UNSPECIFIED GetTrifleVisualStyleListRequest_ActiveStatus = 0
	GetTrifleVisualStyleListRequest_ACTIVE_STATUS_ACTIVE      GetTrifleVisualStyleListRequest_ActiveStatus = 1
)

var GetTrifleVisualStyleListRequest_ActiveStatus_name = map[int32]string{
	0: "ACTIVE_STATUS_UNSPECIFIED",
	1: "ACTIVE_STATUS_ACTIVE",
}
var GetTrifleVisualStyleListRequest_ActiveStatus_value = map[string]int32{
	"ACTIVE_STATUS_UNSPECIFIED": 0,
	"ACTIVE_STATUS_ACTIVE":      1,
}

func (x GetTrifleVisualStyleListRequest_ActiveStatus) String() string {
	return proto.EnumName(GetTrifleVisualStyleListRequest_ActiveStatus_name, int32(x))
}
func (GetTrifleVisualStyleListRequest_ActiveStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{27, 1}
}

type TrifleTask struct {
	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	//  string desc = 3;                        // 小事介绍
	DisplayMode          DisplayMode    `protobuf:"varint,4,opt,name=display_mode,json=displayMode,proto3,enum=mijing_trifle.DisplayMode" json:"display_mode,omitempty"`
	FinishMode           FinishMode     `protobuf:"varint,5,opt,name=finish_mode,json=finishMode,proto3,enum=mijing_trifle.FinishMode" json:"finish_mode,omitempty"`
	Sequence             float32        `protobuf:"fixed32,6,opt,name=sequence,proto3" json:"sequence,omitempty"`
	Awards               []*TrifleAward `protobuf:"bytes,8,rep,name=awards,proto3" json:"awards,omitempty"`
	HomepageText         string         `protobuf:"bytes,9,opt,name=homepage_text,json=homepageText,proto3" json:"homepage_text,omitempty"`
	TaskContent          string         `protobuf:"bytes,10,opt,name=task_content,json=taskContent,proto3" json:"task_content,omitempty"`
	TaskRule             string         `protobuf:"bytes,11,opt,name=task_rule,json=taskRule,proto3" json:"task_rule,omitempty"`
	ImageUri             string         `protobuf:"bytes,13,opt,name=image_uri,json=imageUri,proto3" json:"image_uri,omitempty"`
	LastModifyAt         int64          `protobuf:"varint,16,opt,name=last_modify_at,json=lastModifyAt,proto3" json:"last_modify_at,omitempty"`
	Status               int32          `protobuf:"varint,17,opt,name=status,proto3" json:"status,omitempty"`
	ScenarioIds          []uint32       `protobuf:"varint,18,rep,packed,name=scenario_ids,json=scenarioIds,proto3" json:"scenario_ids,omitempty"`
	UnfinishedImageUri   string         `protobuf:"bytes,19,opt,name=unfinished_image_uri,json=unfinishedImageUri,proto3" json:"unfinished_image_uri,omitempty"`
	ChannelShareImageUri string         `protobuf:"bytes,21,opt,name=channel_share_image_uri,json=channelShareImageUri,proto3" json:"channel_share_image_uri,omitempty"`
	EventType            EventType      `protobuf:"varint,22,opt,name=event_type,json=eventType,proto3,enum=mijing_trifle.EventType" json:"event_type,omitempty"`
	// Types that are valid to be assigned to EventCfg:
	//	*TrifleTask_PassTaskEventCfg
	EventCfg             isTrifleTask_EventCfg `protobuf_oneof:"event_cfg"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *TrifleTask) Reset()         { *m = TrifleTask{} }
func (m *TrifleTask) String() string { return proto.CompactTextString(m) }
func (*TrifleTask) ProtoMessage()    {}
func (*TrifleTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{0}
}
func (m *TrifleTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrifleTask.Unmarshal(m, b)
}
func (m *TrifleTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrifleTask.Marshal(b, m, deterministic)
}
func (dst *TrifleTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrifleTask.Merge(dst, src)
}
func (m *TrifleTask) XXX_Size() int {
	return xxx_messageInfo_TrifleTask.Size(m)
}
func (m *TrifleTask) XXX_DiscardUnknown() {
	xxx_messageInfo_TrifleTask.DiscardUnknown(m)
}

var xxx_messageInfo_TrifleTask proto.InternalMessageInfo

func (m *TrifleTask) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TrifleTask) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *TrifleTask) GetDisplayMode() DisplayMode {
	if m != nil {
		return m.DisplayMode
	}
	return DisplayMode_DISPLAY_MODE_UNKNOWN
}

func (m *TrifleTask) GetFinishMode() FinishMode {
	if m != nil {
		return m.FinishMode
	}
	return FinishMode_FINISH_MODE_UNKNOWN
}

func (m *TrifleTask) GetSequence() float32 {
	if m != nil {
		return m.Sequence
	}
	return 0
}

func (m *TrifleTask) GetAwards() []*TrifleAward {
	if m != nil {
		return m.Awards
	}
	return nil
}

func (m *TrifleTask) GetHomepageText() string {
	if m != nil {
		return m.HomepageText
	}
	return ""
}

func (m *TrifleTask) GetTaskContent() string {
	if m != nil {
		return m.TaskContent
	}
	return ""
}

func (m *TrifleTask) GetTaskRule() string {
	if m != nil {
		return m.TaskRule
	}
	return ""
}

func (m *TrifleTask) GetImageUri() string {
	if m != nil {
		return m.ImageUri
	}
	return ""
}

func (m *TrifleTask) GetLastModifyAt() int64 {
	if m != nil {
		return m.LastModifyAt
	}
	return 0
}

func (m *TrifleTask) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TrifleTask) GetScenarioIds() []uint32 {
	if m != nil {
		return m.ScenarioIds
	}
	return nil
}

func (m *TrifleTask) GetUnfinishedImageUri() string {
	if m != nil {
		return m.UnfinishedImageUri
	}
	return ""
}

func (m *TrifleTask) GetChannelShareImageUri() string {
	if m != nil {
		return m.ChannelShareImageUri
	}
	return ""
}

func (m *TrifleTask) GetEventType() EventType {
	if m != nil {
		return m.EventType
	}
	return EventType_EVENT_TYPE_COMMON
}

type isTrifleTask_EventCfg interface {
	isTrifleTask_EventCfg()
}

type TrifleTask_PassTaskEventCfg struct {
	PassTaskEventCfg *PassTaskEventCfg `protobuf:"bytes,23,opt,name=pass_task_event_cfg,json=passTaskEventCfg,proto3,oneof"`
}

func (*TrifleTask_PassTaskEventCfg) isTrifleTask_EventCfg() {}

func (m *TrifleTask) GetEventCfg() isTrifleTask_EventCfg {
	if m != nil {
		return m.EventCfg
	}
	return nil
}

func (m *TrifleTask) GetPassTaskEventCfg() *PassTaskEventCfg {
	if x, ok := m.GetEventCfg().(*TrifleTask_PassTaskEventCfg); ok {
		return x.PassTaskEventCfg
	}
	return nil
}

// XXX_OneofFuncs is for the internal use of the proto package.
func (*TrifleTask) XXX_OneofFuncs() (func(msg proto.Message, b *proto.Buffer) error, func(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error), func(msg proto.Message) (n int), []interface{}) {
	return _TrifleTask_OneofMarshaler, _TrifleTask_OneofUnmarshaler, _TrifleTask_OneofSizer, []interface{}{
		(*TrifleTask_PassTaskEventCfg)(nil),
	}
}

func _TrifleTask_OneofMarshaler(msg proto.Message, b *proto.Buffer) error {
	m := msg.(*TrifleTask)
	// event_cfg
	switch x := m.EventCfg.(type) {
	case *TrifleTask_PassTaskEventCfg:
		b.EncodeVarint(23<<3 | proto.WireBytes)
		if err := b.EncodeMessage(x.PassTaskEventCfg); err != nil {
			return err
		}
	case nil:
	default:
		return fmt.Errorf("TrifleTask.EventCfg has unexpected type %T", x)
	}
	return nil
}

func _TrifleTask_OneofUnmarshaler(msg proto.Message, tag, wire int, b *proto.Buffer) (bool, error) {
	m := msg.(*TrifleTask)
	switch tag {
	case 23: // event_cfg.pass_task_event_cfg
		if wire != proto.WireBytes {
			return true, proto.ErrInternalBadWireType
		}
		msg := new(PassTaskEventCfg)
		err := b.DecodeMessage(msg)
		m.EventCfg = &TrifleTask_PassTaskEventCfg{msg}
		return true, err
	default:
		return false, nil
	}
}

func _TrifleTask_OneofSizer(msg proto.Message) (n int) {
	m := msg.(*TrifleTask)
	// event_cfg
	switch x := m.EventCfg.(type) {
	case *TrifleTask_PassTaskEventCfg:
		s := proto.Size(x.PassTaskEventCfg)
		n += 2 // tag and wire
		n += proto.SizeVarint(uint64(s))
		n += s
	case nil:
	default:
		panic(fmt.Sprintf("proto: unexpected type %T in oneof", x))
	}
	return n
}

// 完成剧本任务事件的配置
type PassTaskEventCfg struct {
	SharedTaskIds        []string `protobuf:"bytes,1,rep,name=shared_task_ids,json=sharedTaskIds,proto3" json:"shared_task_ids,omitempty"`
	SingleTaskIds        []string `protobuf:"bytes,2,rep,name=single_task_ids,json=singleTaskIds,proto3" json:"single_task_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PassTaskEventCfg) Reset()         { *m = PassTaskEventCfg{} }
func (m *PassTaskEventCfg) String() string { return proto.CompactTextString(m) }
func (*PassTaskEventCfg) ProtoMessage()    {}
func (*PassTaskEventCfg) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{1}
}
func (m *PassTaskEventCfg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PassTaskEventCfg.Unmarshal(m, b)
}
func (m *PassTaskEventCfg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PassTaskEventCfg.Marshal(b, m, deterministic)
}
func (dst *PassTaskEventCfg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PassTaskEventCfg.Merge(dst, src)
}
func (m *PassTaskEventCfg) XXX_Size() int {
	return xxx_messageInfo_PassTaskEventCfg.Size(m)
}
func (m *PassTaskEventCfg) XXX_DiscardUnknown() {
	xxx_messageInfo_PassTaskEventCfg.DiscardUnknown(m)
}

var xxx_messageInfo_PassTaskEventCfg proto.InternalMessageInfo

func (m *PassTaskEventCfg) GetSharedTaskIds() []string {
	if m != nil {
		return m.SharedTaskIds
	}
	return nil
}

func (m *PassTaskEventCfg) GetSingleTaskIds() []string {
	if m != nil {
		return m.SingleTaskIds
	}
	return nil
}

type TrifleAward struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrifleAward) Reset()         { *m = TrifleAward{} }
func (m *TrifleAward) String() string { return proto.CompactTextString(m) }
func (*TrifleAward) ProtoMessage()    {}
func (*TrifleAward) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{2}
}
func (m *TrifleAward) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrifleAward.Unmarshal(m, b)
}
func (m *TrifleAward) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrifleAward.Marshal(b, m, deterministic)
}
func (dst *TrifleAward) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrifleAward.Merge(dst, src)
}
func (m *TrifleAward) XXX_Size() int {
	return xxx_messageInfo_TrifleAward.Size(m)
}
func (m *TrifleAward) XXX_DiscardUnknown() {
	xxx_messageInfo_TrifleAward.DiscardUnknown(m)
}

var xxx_messageInfo_TrifleAward proto.InternalMessageInfo

func (m *TrifleAward) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TrifleAward) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type AwardConfig struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Desc                 string   `protobuf:"bytes,3,opt,name=desc,proto3" json:"desc,omitempty"`
	AwardType            uint32   `protobuf:"varint,4,opt,name=award_type,json=awardType,proto3" json:"award_type,omitempty"`
	Count                uint32   `protobuf:"varint,5,opt,name=count,proto3" json:"count,omitempty"`
	Duration             int64    `protobuf:"varint,6,opt,name=duration,proto3" json:"duration,omitempty"`
	Status               int32    `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AwardConfig) Reset()         { *m = AwardConfig{} }
func (m *AwardConfig) String() string { return proto.CompactTextString(m) }
func (*AwardConfig) ProtoMessage()    {}
func (*AwardConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{3}
}
func (m *AwardConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AwardConfig.Unmarshal(m, b)
}
func (m *AwardConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AwardConfig.Marshal(b, m, deterministic)
}
func (dst *AwardConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AwardConfig.Merge(dst, src)
}
func (m *AwardConfig) XXX_Size() int {
	return xxx_messageInfo_AwardConfig.Size(m)
}
func (m *AwardConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_AwardConfig.DiscardUnknown(m)
}

var xxx_messageInfo_AwardConfig proto.InternalMessageInfo

func (m *AwardConfig) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *AwardConfig) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *AwardConfig) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *AwardConfig) GetAwardType() uint32 {
	if m != nil {
		return m.AwardType
	}
	return 0
}

func (m *AwardConfig) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *AwardConfig) GetDuration() int64 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *AwardConfig) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type TrifleRecord struct {
	Task                 *TrifleTask  `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	RecordId             string       `protobuf:"bytes,2,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	TrifleStatus         TrifleStatus `protobuf:"varint,3,opt,name=trifle_status,json=trifleStatus,proto3,enum=mijing_trifle.TrifleStatus" json:"trifle_status,omitempty"`
	AlreadyRead          bool         `protobuf:"varint,4,opt,name=already_read,json=alreadyRead,proto3" json:"already_read,omitempty"`
	LastFinishAt         int64        `protobuf:"varint,5,opt,name=last_finish_at,json=lastFinishAt,proto3" json:"last_finish_at,omitempty"`
	FirstFinishAt        int64        `protobuf:"varint,6,opt,name=first_finish_at,json=firstFinishAt,proto3" json:"first_finish_at,omitempty"`
	RelatedUids          []uint32     `protobuf:"varint,7,rep,packed,name=related_uids,json=relatedUids,proto3" json:"related_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *TrifleRecord) Reset()         { *m = TrifleRecord{} }
func (m *TrifleRecord) String() string { return proto.CompactTextString(m) }
func (*TrifleRecord) ProtoMessage()    {}
func (*TrifleRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{4}
}
func (m *TrifleRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrifleRecord.Unmarshal(m, b)
}
func (m *TrifleRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrifleRecord.Marshal(b, m, deterministic)
}
func (dst *TrifleRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrifleRecord.Merge(dst, src)
}
func (m *TrifleRecord) XXX_Size() int {
	return xxx_messageInfo_TrifleRecord.Size(m)
}
func (m *TrifleRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_TrifleRecord.DiscardUnknown(m)
}

var xxx_messageInfo_TrifleRecord proto.InternalMessageInfo

func (m *TrifleRecord) GetTask() *TrifleTask {
	if m != nil {
		return m.Task
	}
	return nil
}

func (m *TrifleRecord) GetRecordId() string {
	if m != nil {
		return m.RecordId
	}
	return ""
}

func (m *TrifleRecord) GetTrifleStatus() TrifleStatus {
	if m != nil {
		return m.TrifleStatus
	}
	return TrifleStatus_TRIFLE_STATUS_UNSPECIFIED
}

func (m *TrifleRecord) GetAlreadyRead() bool {
	if m != nil {
		return m.AlreadyRead
	}
	return false
}

func (m *TrifleRecord) GetLastFinishAt() int64 {
	if m != nil {
		return m.LastFinishAt
	}
	return 0
}

func (m *TrifleRecord) GetFirstFinishAt() int64 {
	if m != nil {
		return m.FirstFinishAt
	}
	return 0
}

func (m *TrifleRecord) GetRelatedUids() []uint32 {
	if m != nil {
		return m.RelatedUids
	}
	return nil
}

type FinishTrifleTaskReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId               string   `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	UniqueKey            string   `protobuf:"bytes,3,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`
	FinishAt             int64    `protobuf:"varint,4,opt,name=finish_at,json=finishAt,proto3" json:"finish_at,omitempty"`
	ExpectSendPush       bool     `protobuf:"varint,5,opt,name=expect_send_push,json=expectSendPush,proto3" json:"expect_send_push,omitempty"`
	RelatedUids          []uint32 `protobuf:"varint,10,rep,packed,name=related_uids,json=relatedUids,proto3" json:"related_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishTrifleTaskReq) Reset()         { *m = FinishTrifleTaskReq{} }
func (m *FinishTrifleTaskReq) String() string { return proto.CompactTextString(m) }
func (*FinishTrifleTaskReq) ProtoMessage()    {}
func (*FinishTrifleTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{5}
}
func (m *FinishTrifleTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishTrifleTaskReq.Unmarshal(m, b)
}
func (m *FinishTrifleTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishTrifleTaskReq.Marshal(b, m, deterministic)
}
func (dst *FinishTrifleTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishTrifleTaskReq.Merge(dst, src)
}
func (m *FinishTrifleTaskReq) XXX_Size() int {
	return xxx_messageInfo_FinishTrifleTaskReq.Size(m)
}
func (m *FinishTrifleTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishTrifleTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_FinishTrifleTaskReq proto.InternalMessageInfo

func (m *FinishTrifleTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *FinishTrifleTaskReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *FinishTrifleTaskReq) GetUniqueKey() string {
	if m != nil {
		return m.UniqueKey
	}
	return ""
}

func (m *FinishTrifleTaskReq) GetFinishAt() int64 {
	if m != nil {
		return m.FinishAt
	}
	return 0
}

func (m *FinishTrifleTaskReq) GetExpectSendPush() bool {
	if m != nil {
		return m.ExpectSendPush
	}
	return false
}

func (m *FinishTrifleTaskReq) GetRelatedUids() []uint32 {
	if m != nil {
		return m.RelatedUids
	}
	return nil
}

type FinishTrifleTaskResp struct {
	Finished             bool     `protobuf:"varint,1,opt,name=finished,proto3" json:"finished,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *FinishTrifleTaskResp) Reset()         { *m = FinishTrifleTaskResp{} }
func (m *FinishTrifleTaskResp) String() string { return proto.CompactTextString(m) }
func (*FinishTrifleTaskResp) ProtoMessage()    {}
func (*FinishTrifleTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{6}
}
func (m *FinishTrifleTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FinishTrifleTaskResp.Unmarshal(m, b)
}
func (m *FinishTrifleTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FinishTrifleTaskResp.Marshal(b, m, deterministic)
}
func (dst *FinishTrifleTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FinishTrifleTaskResp.Merge(dst, src)
}
func (m *FinishTrifleTaskResp) XXX_Size() int {
	return xxx_messageInfo_FinishTrifleTaskResp.Size(m)
}
func (m *FinishTrifleTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_FinishTrifleTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_FinishTrifleTaskResp proto.InternalMessageInfo

func (m *FinishTrifleTaskResp) GetFinished() bool {
	if m != nil {
		return m.Finished
	}
	return false
}

type GetTrifleTaskReq struct {
	WithoutCache         bool     `protobuf:"varint,11,opt,name=without_cache,json=withoutCache,proto3" json:"without_cache,omitempty"`
	ReturnAllTask        bool     `protobuf:"varint,12,opt,name=return_all_task,json=returnAllTask,proto3" json:"return_all_task,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTrifleTaskReq) Reset()         { *m = GetTrifleTaskReq{} }
func (m *GetTrifleTaskReq) String() string { return proto.CompactTextString(m) }
func (*GetTrifleTaskReq) ProtoMessage()    {}
func (*GetTrifleTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{7}
}
func (m *GetTrifleTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrifleTaskReq.Unmarshal(m, b)
}
func (m *GetTrifleTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrifleTaskReq.Marshal(b, m, deterministic)
}
func (dst *GetTrifleTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrifleTaskReq.Merge(dst, src)
}
func (m *GetTrifleTaskReq) XXX_Size() int {
	return xxx_messageInfo_GetTrifleTaskReq.Size(m)
}
func (m *GetTrifleTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrifleTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrifleTaskReq proto.InternalMessageInfo

func (m *GetTrifleTaskReq) GetWithoutCache() bool {
	if m != nil {
		return m.WithoutCache
	}
	return false
}

func (m *GetTrifleTaskReq) GetReturnAllTask() bool {
	if m != nil {
		return m.ReturnAllTask
	}
	return false
}

type GetTrifleTaskResp struct {
	Tasks                []*TrifleTask `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetTrifleTaskResp) Reset()         { *m = GetTrifleTaskResp{} }
func (m *GetTrifleTaskResp) String() string { return proto.CompactTextString(m) }
func (*GetTrifleTaskResp) ProtoMessage()    {}
func (*GetTrifleTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{8}
}
func (m *GetTrifleTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrifleTaskResp.Unmarshal(m, b)
}
func (m *GetTrifleTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrifleTaskResp.Marshal(b, m, deterministic)
}
func (dst *GetTrifleTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrifleTaskResp.Merge(dst, src)
}
func (m *GetTrifleTaskResp) XXX_Size() int {
	return xxx_messageInfo_GetTrifleTaskResp.Size(m)
}
func (m *GetTrifleTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrifleTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrifleTaskResp proto.InternalMessageInfo

func (m *GetTrifleTaskResp) GetTasks() []*TrifleTask {
	if m != nil {
		return m.Tasks
	}
	return nil
}

type GetTrifleRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IncludeUnfinished    bool     `protobuf:"varint,11,opt,name=include_unfinished,json=includeUnfinished,proto3" json:"include_unfinished,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTrifleRecordReq) Reset()         { *m = GetTrifleRecordReq{} }
func (m *GetTrifleRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetTrifleRecordReq) ProtoMessage()    {}
func (*GetTrifleRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{9}
}
func (m *GetTrifleRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrifleRecordReq.Unmarshal(m, b)
}
func (m *GetTrifleRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrifleRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetTrifleRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrifleRecordReq.Merge(dst, src)
}
func (m *GetTrifleRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetTrifleRecordReq.Size(m)
}
func (m *GetTrifleRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrifleRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrifleRecordReq proto.InternalMessageInfo

func (m *GetTrifleRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetTrifleRecordReq) GetIncludeUnfinished() bool {
	if m != nil {
		return m.IncludeUnfinished
	}
	return false
}

type GetTrifleRecordResp struct {
	Records              []*TrifleRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetTrifleRecordResp) Reset()         { *m = GetTrifleRecordResp{} }
func (m *GetTrifleRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetTrifleRecordResp) ProtoMessage()    {}
func (*GetTrifleRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{10}
}
func (m *GetTrifleRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrifleRecordResp.Unmarshal(m, b)
}
func (m *GetTrifleRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrifleRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetTrifleRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrifleRecordResp.Merge(dst, src)
}
func (m *GetTrifleRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetTrifleRecordResp.Size(m)
}
func (m *GetTrifleRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrifleRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrifleRecordResp proto.InternalMessageInfo

func (m *GetTrifleRecordResp) GetRecords() []*TrifleRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type UpdateTrifleTaskReq struct {
	Task                 *TrifleTask `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateTrifleTaskReq) Reset()         { *m = UpdateTrifleTaskReq{} }
func (m *UpdateTrifleTaskReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTrifleTaskReq) ProtoMessage()    {}
func (*UpdateTrifleTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{11}
}
func (m *UpdateTrifleTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTrifleTaskReq.Unmarshal(m, b)
}
func (m *UpdateTrifleTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTrifleTaskReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTrifleTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTrifleTaskReq.Merge(dst, src)
}
func (m *UpdateTrifleTaskReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTrifleTaskReq.Size(m)
}
func (m *UpdateTrifleTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTrifleTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTrifleTaskReq proto.InternalMessageInfo

func (m *UpdateTrifleTaskReq) GetTask() *TrifleTask {
	if m != nil {
		return m.Task
	}
	return nil
}

type UpdateTrifleTaskResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTrifleTaskResp) Reset()         { *m = UpdateTrifleTaskResp{} }
func (m *UpdateTrifleTaskResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTrifleTaskResp) ProtoMessage()    {}
func (*UpdateTrifleTaskResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{12}
}
func (m *UpdateTrifleTaskResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTrifleTaskResp.Unmarshal(m, b)
}
func (m *UpdateTrifleTaskResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTrifleTaskResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTrifleTaskResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTrifleTaskResp.Merge(dst, src)
}
func (m *UpdateTrifleTaskResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTrifleTaskResp.Size(m)
}
func (m *UpdateTrifleTaskResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTrifleTaskResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTrifleTaskResp proto.InternalMessageInfo

type UpdateAwardConfigReq struct {
	AwardConfig          *AwardConfig `protobuf:"bytes,1,opt,name=award_config,json=awardConfig,proto3" json:"award_config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *UpdateAwardConfigReq) Reset()         { *m = UpdateAwardConfigReq{} }
func (m *UpdateAwardConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAwardConfigReq) ProtoMessage()    {}
func (*UpdateAwardConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{13}
}
func (m *UpdateAwardConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAwardConfigReq.Unmarshal(m, b)
}
func (m *UpdateAwardConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAwardConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAwardConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAwardConfigReq.Merge(dst, src)
}
func (m *UpdateAwardConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAwardConfigReq.Size(m)
}
func (m *UpdateAwardConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAwardConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAwardConfigReq proto.InternalMessageInfo

func (m *UpdateAwardConfigReq) GetAwardConfig() *AwardConfig {
	if m != nil {
		return m.AwardConfig
	}
	return nil
}

type UpdateAwardConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAwardConfigResp) Reset()         { *m = UpdateAwardConfigResp{} }
func (m *UpdateAwardConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAwardConfigResp) ProtoMessage()    {}
func (*UpdateAwardConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{14}
}
func (m *UpdateAwardConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAwardConfigResp.Unmarshal(m, b)
}
func (m *UpdateAwardConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAwardConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAwardConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAwardConfigResp.Merge(dst, src)
}
func (m *UpdateAwardConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAwardConfigResp.Size(m)
}
func (m *UpdateAwardConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAwardConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAwardConfigResp proto.InternalMessageInfo

type GetAwardConfigReq struct {
	WithoutCache         bool     `protobuf:"varint,11,opt,name=without_cache,json=withoutCache,proto3" json:"without_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAwardConfigReq) Reset()         { *m = GetAwardConfigReq{} }
func (m *GetAwardConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetAwardConfigReq) ProtoMessage()    {}
func (*GetAwardConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{15}
}
func (m *GetAwardConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardConfigReq.Unmarshal(m, b)
}
func (m *GetAwardConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetAwardConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardConfigReq.Merge(dst, src)
}
func (m *GetAwardConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetAwardConfigReq.Size(m)
}
func (m *GetAwardConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardConfigReq proto.InternalMessageInfo

func (m *GetAwardConfigReq) GetWithoutCache() bool {
	if m != nil {
		return m.WithoutCache
	}
	return false
}

type GetAwardConfigResp struct {
	AwardConfigs         []*AwardConfig `protobuf:"bytes,1,rep,name=award_configs,json=awardConfigs,proto3" json:"award_configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetAwardConfigResp) Reset()         { *m = GetAwardConfigResp{} }
func (m *GetAwardConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetAwardConfigResp) ProtoMessage()    {}
func (*GetAwardConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{16}
}
func (m *GetAwardConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAwardConfigResp.Unmarshal(m, b)
}
func (m *GetAwardConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAwardConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetAwardConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAwardConfigResp.Merge(dst, src)
}
func (m *GetAwardConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetAwardConfigResp.Size(m)
}
func (m *GetAwardConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAwardConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAwardConfigResp proto.InternalMessageInfo

func (m *GetAwardConfigResp) GetAwardConfigs() []*AwardConfig {
	if m != nil {
		return m.AwardConfigs
	}
	return nil
}

type ManualAcceptRecordReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RecordId             string   `protobuf:"bytes,2,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	DetailIds            []uint64 `protobuf:"varint,3,rep,packed,name=detail_ids,json=detailIds,proto3" json:"detail_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualAcceptRecordReq) Reset()         { *m = ManualAcceptRecordReq{} }
func (m *ManualAcceptRecordReq) String() string { return proto.CompactTextString(m) }
func (*ManualAcceptRecordReq) ProtoMessage()    {}
func (*ManualAcceptRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{17}
}
func (m *ManualAcceptRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualAcceptRecordReq.Unmarshal(m, b)
}
func (m *ManualAcceptRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualAcceptRecordReq.Marshal(b, m, deterministic)
}
func (dst *ManualAcceptRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualAcceptRecordReq.Merge(dst, src)
}
func (m *ManualAcceptRecordReq) XXX_Size() int {
	return xxx_messageInfo_ManualAcceptRecordReq.Size(m)
}
func (m *ManualAcceptRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualAcceptRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_ManualAcceptRecordReq proto.InternalMessageInfo

func (m *ManualAcceptRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ManualAcceptRecordReq) GetRecordId() string {
	if m != nil {
		return m.RecordId
	}
	return ""
}

func (m *ManualAcceptRecordReq) GetDetailIds() []uint64 {
	if m != nil {
		return m.DetailIds
	}
	return nil
}

type ManualAcceptRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualAcceptRecordResp) Reset()         { *m = ManualAcceptRecordResp{} }
func (m *ManualAcceptRecordResp) String() string { return proto.CompactTextString(m) }
func (*ManualAcceptRecordResp) ProtoMessage()    {}
func (*ManualAcceptRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{18}
}
func (m *ManualAcceptRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualAcceptRecordResp.Unmarshal(m, b)
}
func (m *ManualAcceptRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualAcceptRecordResp.Marshal(b, m, deterministic)
}
func (dst *ManualAcceptRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualAcceptRecordResp.Merge(dst, src)
}
func (m *ManualAcceptRecordResp) XXX_Size() int {
	return xxx_messageInfo_ManualAcceptRecordResp.Size(m)
}
func (m *ManualAcceptRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualAcceptRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_ManualAcceptRecordResp proto.InternalMessageInfo

type MarkRecordReadReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RecordIds            []string `protobuf:"bytes,2,rep,name=record_ids,json=recordIds,proto3" json:"record_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRecordReadReq) Reset()         { *m = MarkRecordReadReq{} }
func (m *MarkRecordReadReq) String() string { return proto.CompactTextString(m) }
func (*MarkRecordReadReq) ProtoMessage()    {}
func (*MarkRecordReadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{19}
}
func (m *MarkRecordReadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRecordReadReq.Unmarshal(m, b)
}
func (m *MarkRecordReadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRecordReadReq.Marshal(b, m, deterministic)
}
func (dst *MarkRecordReadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRecordReadReq.Merge(dst, src)
}
func (m *MarkRecordReadReq) XXX_Size() int {
	return xxx_messageInfo_MarkRecordReadReq.Size(m)
}
func (m *MarkRecordReadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRecordReadReq.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRecordReadReq proto.InternalMessageInfo

func (m *MarkRecordReadReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MarkRecordReadReq) GetRecordIds() []string {
	if m != nil {
		return m.RecordIds
	}
	return nil
}

type MarkRecordReadResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MarkRecordReadResp) Reset()         { *m = MarkRecordReadResp{} }
func (m *MarkRecordReadResp) String() string { return proto.CompactTextString(m) }
func (*MarkRecordReadResp) ProtoMessage()    {}
func (*MarkRecordReadResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{20}
}
func (m *MarkRecordReadResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MarkRecordReadResp.Unmarshal(m, b)
}
func (m *MarkRecordReadResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MarkRecordReadResp.Marshal(b, m, deterministic)
}
func (dst *MarkRecordReadResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MarkRecordReadResp.Merge(dst, src)
}
func (m *MarkRecordReadResp) XXX_Size() int {
	return xxx_messageInfo_MarkRecordReadResp.Size(m)
}
func (m *MarkRecordReadResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MarkRecordReadResp.DiscardUnknown(m)
}

var xxx_messageInfo_MarkRecordReadResp proto.InternalMessageInfo

type DebugFinishTrifleTaskReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TaskId               string   `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	UniqueKey            string   `protobuf:"bytes,3,opt,name=unique_key,json=uniqueKey,proto3" json:"unique_key,omitempty"`
	RelatedUids          []uint32 `protobuf:"varint,10,rep,packed,name=related_uids,json=relatedUids,proto3" json:"related_uids,omitempty"`
	RandomFinishCount    uint32   `protobuf:"varint,11,opt,name=random_finish_count,json=randomFinishCount,proto3" json:"random_finish_count,omitempty"`
	DeleteAll            bool     `protobuf:"varint,12,opt,name=delete_all,json=deleteAll,proto3" json:"delete_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DebugFinishTrifleTaskReq) Reset()         { *m = DebugFinishTrifleTaskReq{} }
func (m *DebugFinishTrifleTaskReq) String() string { return proto.CompactTextString(m) }
func (*DebugFinishTrifleTaskReq) ProtoMessage()    {}
func (*DebugFinishTrifleTaskReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{21}
}
func (m *DebugFinishTrifleTaskReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DebugFinishTrifleTaskReq.Unmarshal(m, b)
}
func (m *DebugFinishTrifleTaskReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DebugFinishTrifleTaskReq.Marshal(b, m, deterministic)
}
func (dst *DebugFinishTrifleTaskReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DebugFinishTrifleTaskReq.Merge(dst, src)
}
func (m *DebugFinishTrifleTaskReq) XXX_Size() int {
	return xxx_messageInfo_DebugFinishTrifleTaskReq.Size(m)
}
func (m *DebugFinishTrifleTaskReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DebugFinishTrifleTaskReq.DiscardUnknown(m)
}

var xxx_messageInfo_DebugFinishTrifleTaskReq proto.InternalMessageInfo

func (m *DebugFinishTrifleTaskReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DebugFinishTrifleTaskReq) GetTaskId() string {
	if m != nil {
		return m.TaskId
	}
	return ""
}

func (m *DebugFinishTrifleTaskReq) GetUniqueKey() string {
	if m != nil {
		return m.UniqueKey
	}
	return ""
}

func (m *DebugFinishTrifleTaskReq) GetRelatedUids() []uint32 {
	if m != nil {
		return m.RelatedUids
	}
	return nil
}

func (m *DebugFinishTrifleTaskReq) GetRandomFinishCount() uint32 {
	if m != nil {
		return m.RandomFinishCount
	}
	return 0
}

func (m *DebugFinishTrifleTaskReq) GetDeleteAll() bool {
	if m != nil {
		return m.DeleteAll
	}
	return false
}

// 谜之小事视觉风格配置
type TrifleVisualStyle struct {
	// p.s 格式不会进行校验，只是用来做文档说明
	Id                     string                                  `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                   string                                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Platform               TrifleVisualStyle_Platform              `protobuf:"varint,3,opt,name=platform,proto3,enum=mijing_trifle.TrifleVisualStyle_Platform" json:"platform,omitempty"`
	BackgroundPicUrl       string                                  `protobuf:"bytes,4,opt,name=background_pic_url,json=backgroundPicUrl,proto3" json:"background_pic_url,omitempty"`
	EntrancePicUrl         string                                  `protobuf:"bytes,5,opt,name=entrance_pic_url,json=entrancePicUrl,proto3" json:"entrance_pic_url,omitempty"`
	SeatPicUrl             string                                  `protobuf:"bytes,6,opt,name=seat_pic_url,json=seatPicUrl,proto3" json:"seat_pic_url,omitempty"`
	InvitePicUrl           string                                  `protobuf:"bytes,7,opt,name=invite_pic_url,json=invitePicUrl,proto3" json:"invite_pic_url,omitempty"`
	EntranceNoFriendsUrl   string                                  `protobuf:"bytes,8,opt,name=entrance_no_friends_url,json=entranceNoFriendsUrl,proto3" json:"entrance_no_friends_url,omitempty"`
	EntranceHasFriendsList []*TrifleVisualStyle_EntranceHasFriends `protobuf:"bytes,9,rep,name=entrance_has_friends_list,json=entranceHasFriendsList,proto3" json:"entrance_has_friends_list,omitempty"`
	StartAt                int64                                   `protobuf:"varint,10,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	EndAt                  int64                                   `protobuf:"varint,11,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	CreatedAt              int64                                   `protobuf:"varint,101,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              int64                                   `protobuf:"varint,102,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral   struct{}                                `json:"-"`
	XXX_unrecognized       []byte                                  `json:"-"`
	XXX_sizecache          int32                                   `json:"-"`
}

func (m *TrifleVisualStyle) Reset()         { *m = TrifleVisualStyle{} }
func (m *TrifleVisualStyle) String() string { return proto.CompactTextString(m) }
func (*TrifleVisualStyle) ProtoMessage()    {}
func (*TrifleVisualStyle) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{22}
}
func (m *TrifleVisualStyle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrifleVisualStyle.Unmarshal(m, b)
}
func (m *TrifleVisualStyle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrifleVisualStyle.Marshal(b, m, deterministic)
}
func (dst *TrifleVisualStyle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrifleVisualStyle.Merge(dst, src)
}
func (m *TrifleVisualStyle) XXX_Size() int {
	return xxx_messageInfo_TrifleVisualStyle.Size(m)
}
func (m *TrifleVisualStyle) XXX_DiscardUnknown() {
	xxx_messageInfo_TrifleVisualStyle.DiscardUnknown(m)
}

var xxx_messageInfo_TrifleVisualStyle proto.InternalMessageInfo

func (m *TrifleVisualStyle) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TrifleVisualStyle) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TrifleVisualStyle) GetPlatform() TrifleVisualStyle_Platform {
	if m != nil {
		return m.Platform
	}
	return TrifleVisualStyle_PLATFORM_UNSPECIFIED
}

func (m *TrifleVisualStyle) GetBackgroundPicUrl() string {
	if m != nil {
		return m.BackgroundPicUrl
	}
	return ""
}

func (m *TrifleVisualStyle) GetEntrancePicUrl() string {
	if m != nil {
		return m.EntrancePicUrl
	}
	return ""
}

func (m *TrifleVisualStyle) GetSeatPicUrl() string {
	if m != nil {
		return m.SeatPicUrl
	}
	return ""
}

func (m *TrifleVisualStyle) GetInvitePicUrl() string {
	if m != nil {
		return m.InvitePicUrl
	}
	return ""
}

func (m *TrifleVisualStyle) GetEntranceNoFriendsUrl() string {
	if m != nil {
		return m.EntranceNoFriendsUrl
	}
	return ""
}

func (m *TrifleVisualStyle) GetEntranceHasFriendsList() []*TrifleVisualStyle_EntranceHasFriends {
	if m != nil {
		return m.EntranceHasFriendsList
	}
	return nil
}

func (m *TrifleVisualStyle) GetStartAt() int64 {
	if m != nil {
		return m.StartAt
	}
	return 0
}

func (m *TrifleVisualStyle) GetEndAt() int64 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *TrifleVisualStyle) GetCreatedAt() int64 {
	if m != nil {
		return m.CreatedAt
	}
	return 0
}

func (m *TrifleVisualStyle) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

// 入口动画（有好友）
type TrifleVisualStyle_EntranceHasFriends struct {
	BubbleBodyUrl        string   `protobuf:"bytes,1,opt,name=bubble_body_url,json=bubbleBodyUrl,proto3" json:"bubble_body_url,omitempty"`
	BubbleBorderUrl      string   `protobuf:"bytes,2,opt,name=bubble_border_url,json=bubbleBorderUrl,proto3" json:"bubble_border_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TrifleVisualStyle_EntranceHasFriends) Reset()         { *m = TrifleVisualStyle_EntranceHasFriends{} }
func (m *TrifleVisualStyle_EntranceHasFriends) String() string { return proto.CompactTextString(m) }
func (*TrifleVisualStyle_EntranceHasFriends) ProtoMessage()    {}
func (*TrifleVisualStyle_EntranceHasFriends) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{22, 0}
}
func (m *TrifleVisualStyle_EntranceHasFriends) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TrifleVisualStyle_EntranceHasFriends.Unmarshal(m, b)
}
func (m *TrifleVisualStyle_EntranceHasFriends) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TrifleVisualStyle_EntranceHasFriends.Marshal(b, m, deterministic)
}
func (dst *TrifleVisualStyle_EntranceHasFriends) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TrifleVisualStyle_EntranceHasFriends.Merge(dst, src)
}
func (m *TrifleVisualStyle_EntranceHasFriends) XXX_Size() int {
	return xxx_messageInfo_TrifleVisualStyle_EntranceHasFriends.Size(m)
}
func (m *TrifleVisualStyle_EntranceHasFriends) XXX_DiscardUnknown() {
	xxx_messageInfo_TrifleVisualStyle_EntranceHasFriends.DiscardUnknown(m)
}

var xxx_messageInfo_TrifleVisualStyle_EntranceHasFriends proto.InternalMessageInfo

func (m *TrifleVisualStyle_EntranceHasFriends) GetBubbleBodyUrl() string {
	if m != nil {
		return m.BubbleBodyUrl
	}
	return ""
}

func (m *TrifleVisualStyle_EntranceHasFriends) GetBubbleBorderUrl() string {
	if m != nil {
		return m.BubbleBorderUrl
	}
	return ""
}

// 新增 or 更新谜之小事视觉风格配置
type UpdateTrifleVisualStyleRequest struct {
	TrifleVisualStyle    *TrifleVisualStyle `protobuf:"bytes,1,opt,name=trifle_visual_style,json=trifleVisualStyle,proto3" json:"trifle_visual_style,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *UpdateTrifleVisualStyleRequest) Reset()         { *m = UpdateTrifleVisualStyleRequest{} }
func (m *UpdateTrifleVisualStyleRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateTrifleVisualStyleRequest) ProtoMessage()    {}
func (*UpdateTrifleVisualStyleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{23}
}
func (m *UpdateTrifleVisualStyleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTrifleVisualStyleRequest.Unmarshal(m, b)
}
func (m *UpdateTrifleVisualStyleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTrifleVisualStyleRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateTrifleVisualStyleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTrifleVisualStyleRequest.Merge(dst, src)
}
func (m *UpdateTrifleVisualStyleRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateTrifleVisualStyleRequest.Size(m)
}
func (m *UpdateTrifleVisualStyleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTrifleVisualStyleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTrifleVisualStyleRequest proto.InternalMessageInfo

func (m *UpdateTrifleVisualStyleRequest) GetTrifleVisualStyle() *TrifleVisualStyle {
	if m != nil {
		return m.TrifleVisualStyle
	}
	return nil
}

type UpdateTrifleVisualStyleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTrifleVisualStyleResponse) Reset()         { *m = UpdateTrifleVisualStyleResponse{} }
func (m *UpdateTrifleVisualStyleResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateTrifleVisualStyleResponse) ProtoMessage()    {}
func (*UpdateTrifleVisualStyleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{24}
}
func (m *UpdateTrifleVisualStyleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTrifleVisualStyleResponse.Unmarshal(m, b)
}
func (m *UpdateTrifleVisualStyleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTrifleVisualStyleResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateTrifleVisualStyleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTrifleVisualStyleResponse.Merge(dst, src)
}
func (m *UpdateTrifleVisualStyleResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateTrifleVisualStyleResponse.Size(m)
}
func (m *UpdateTrifleVisualStyleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTrifleVisualStyleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTrifleVisualStyleResponse proto.InternalMessageInfo

// 删除谜之小事视觉风格配置
type DeleteTrifleVisualStyleRequest struct {
	IdList               []string `protobuf:"bytes,1,rep,name=id_list,json=idList,proto3" json:"id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTrifleVisualStyleRequest) Reset()         { *m = DeleteTrifleVisualStyleRequest{} }
func (m *DeleteTrifleVisualStyleRequest) String() string { return proto.CompactTextString(m) }
func (*DeleteTrifleVisualStyleRequest) ProtoMessage()    {}
func (*DeleteTrifleVisualStyleRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{25}
}
func (m *DeleteTrifleVisualStyleRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTrifleVisualStyleRequest.Unmarshal(m, b)
}
func (m *DeleteTrifleVisualStyleRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTrifleVisualStyleRequest.Marshal(b, m, deterministic)
}
func (dst *DeleteTrifleVisualStyleRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTrifleVisualStyleRequest.Merge(dst, src)
}
func (m *DeleteTrifleVisualStyleRequest) XXX_Size() int {
	return xxx_messageInfo_DeleteTrifleVisualStyleRequest.Size(m)
}
func (m *DeleteTrifleVisualStyleRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTrifleVisualStyleRequest.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTrifleVisualStyleRequest proto.InternalMessageInfo

func (m *DeleteTrifleVisualStyleRequest) GetIdList() []string {
	if m != nil {
		return m.IdList
	}
	return nil
}

type DeleteTrifleVisualStyleResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTrifleVisualStyleResponse) Reset()         { *m = DeleteTrifleVisualStyleResponse{} }
func (m *DeleteTrifleVisualStyleResponse) String() string { return proto.CompactTextString(m) }
func (*DeleteTrifleVisualStyleResponse) ProtoMessage()    {}
func (*DeleteTrifleVisualStyleResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{26}
}
func (m *DeleteTrifleVisualStyleResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTrifleVisualStyleResponse.Unmarshal(m, b)
}
func (m *DeleteTrifleVisualStyleResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTrifleVisualStyleResponse.Marshal(b, m, deterministic)
}
func (dst *DeleteTrifleVisualStyleResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTrifleVisualStyleResponse.Merge(dst, src)
}
func (m *DeleteTrifleVisualStyleResponse) XXX_Size() int {
	return xxx_messageInfo_DeleteTrifleVisualStyleResponse.Size(m)
}
func (m *DeleteTrifleVisualStyleResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTrifleVisualStyleResponse.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTrifleVisualStyleResponse proto.InternalMessageInfo

// 获取谜之小事视觉风格配置列表
type GetTrifleVisualStyleListRequest struct {
	DataSource           GetTrifleVisualStyleListRequest_DataSource   `protobuf:"varint,1,opt,name=data_source,json=dataSource,proto3,enum=mijing_trifle.GetTrifleVisualStyleListRequest_DataSource" json:"data_source,omitempty"`
	ActiveStatus         GetTrifleVisualStyleListRequest_ActiveStatus `protobuf:"varint,2,opt,name=active_status,json=activeStatus,proto3,enum=mijing_trifle.GetTrifleVisualStyleListRequest_ActiveStatus" json:"active_status,omitempty"`
	Platform             TrifleVisualStyle_Platform                   `protobuf:"varint,3,opt,name=platform,proto3,enum=mijing_trifle.TrifleVisualStyle_Platform" json:"platform,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-"`
	XXX_unrecognized     []byte                                       `json:"-"`
	XXX_sizecache        int32                                        `json:"-"`
}

func (m *GetTrifleVisualStyleListRequest) Reset()         { *m = GetTrifleVisualStyleListRequest{} }
func (m *GetTrifleVisualStyleListRequest) String() string { return proto.CompactTextString(m) }
func (*GetTrifleVisualStyleListRequest) ProtoMessage()    {}
func (*GetTrifleVisualStyleListRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{27}
}
func (m *GetTrifleVisualStyleListRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrifleVisualStyleListRequest.Unmarshal(m, b)
}
func (m *GetTrifleVisualStyleListRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrifleVisualStyleListRequest.Marshal(b, m, deterministic)
}
func (dst *GetTrifleVisualStyleListRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrifleVisualStyleListRequest.Merge(dst, src)
}
func (m *GetTrifleVisualStyleListRequest) XXX_Size() int {
	return xxx_messageInfo_GetTrifleVisualStyleListRequest.Size(m)
}
func (m *GetTrifleVisualStyleListRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrifleVisualStyleListRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrifleVisualStyleListRequest proto.InternalMessageInfo

func (m *GetTrifleVisualStyleListRequest) GetDataSource() GetTrifleVisualStyleListRequest_DataSource {
	if m != nil {
		return m.DataSource
	}
	return GetTrifleVisualStyleListRequest_DATA_SOURCE_UNSPECIFIED
}

func (m *GetTrifleVisualStyleListRequest) GetActiveStatus() GetTrifleVisualStyleListRequest_ActiveStatus {
	if m != nil {
		return m.ActiveStatus
	}
	return GetTrifleVisualStyleListRequest_ACTIVE_STATUS_UNSPECIFIED
}

func (m *GetTrifleVisualStyleListRequest) GetPlatform() TrifleVisualStyle_Platform {
	if m != nil {
		return m.Platform
	}
	return TrifleVisualStyle_PLATFORM_UNSPECIFIED
}

type GetTrifleVisualStyleListResponse struct {
	TrifleVisualStyleList []*TrifleVisualStyle `protobuf:"bytes,1,rep,name=trifle_visual_style_list,json=trifleVisualStyleList,proto3" json:"trifle_visual_style_list,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}             `json:"-"`
	XXX_unrecognized      []byte               `json:"-"`
	XXX_sizecache         int32                `json:"-"`
}

func (m *GetTrifleVisualStyleListResponse) Reset()         { *m = GetTrifleVisualStyleListResponse{} }
func (m *GetTrifleVisualStyleListResponse) String() string { return proto.CompactTextString(m) }
func (*GetTrifleVisualStyleListResponse) ProtoMessage()    {}
func (*GetTrifleVisualStyleListResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_mijing_trifle_10c2b312cd2a4ef3, []int{28}
}
func (m *GetTrifleVisualStyleListResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTrifleVisualStyleListResponse.Unmarshal(m, b)
}
func (m *GetTrifleVisualStyleListResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTrifleVisualStyleListResponse.Marshal(b, m, deterministic)
}
func (dst *GetTrifleVisualStyleListResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTrifleVisualStyleListResponse.Merge(dst, src)
}
func (m *GetTrifleVisualStyleListResponse) XXX_Size() int {
	return xxx_messageInfo_GetTrifleVisualStyleListResponse.Size(m)
}
func (m *GetTrifleVisualStyleListResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTrifleVisualStyleListResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetTrifleVisualStyleListResponse proto.InternalMessageInfo

func (m *GetTrifleVisualStyleListResponse) GetTrifleVisualStyleList() []*TrifleVisualStyle {
	if m != nil {
		return m.TrifleVisualStyleList
	}
	return nil
}

func init() {
	proto.RegisterType((*TrifleTask)(nil), "mijing_trifle.TrifleTask")
	proto.RegisterType((*PassTaskEventCfg)(nil), "mijing_trifle.PassTaskEventCfg")
	proto.RegisterType((*TrifleAward)(nil), "mijing_trifle.TrifleAward")
	proto.RegisterType((*AwardConfig)(nil), "mijing_trifle.AwardConfig")
	proto.RegisterType((*TrifleRecord)(nil), "mijing_trifle.TrifleRecord")
	proto.RegisterType((*FinishTrifleTaskReq)(nil), "mijing_trifle.FinishTrifleTaskReq")
	proto.RegisterType((*FinishTrifleTaskResp)(nil), "mijing_trifle.FinishTrifleTaskResp")
	proto.RegisterType((*GetTrifleTaskReq)(nil), "mijing_trifle.GetTrifleTaskReq")
	proto.RegisterType((*GetTrifleTaskResp)(nil), "mijing_trifle.GetTrifleTaskResp")
	proto.RegisterType((*GetTrifleRecordReq)(nil), "mijing_trifle.GetTrifleRecordReq")
	proto.RegisterType((*GetTrifleRecordResp)(nil), "mijing_trifle.GetTrifleRecordResp")
	proto.RegisterType((*UpdateTrifleTaskReq)(nil), "mijing_trifle.UpdateTrifleTaskReq")
	proto.RegisterType((*UpdateTrifleTaskResp)(nil), "mijing_trifle.UpdateTrifleTaskResp")
	proto.RegisterType((*UpdateAwardConfigReq)(nil), "mijing_trifle.UpdateAwardConfigReq")
	proto.RegisterType((*UpdateAwardConfigResp)(nil), "mijing_trifle.UpdateAwardConfigResp")
	proto.RegisterType((*GetAwardConfigReq)(nil), "mijing_trifle.GetAwardConfigReq")
	proto.RegisterType((*GetAwardConfigResp)(nil), "mijing_trifle.GetAwardConfigResp")
	proto.RegisterType((*ManualAcceptRecordReq)(nil), "mijing_trifle.ManualAcceptRecordReq")
	proto.RegisterType((*ManualAcceptRecordResp)(nil), "mijing_trifle.ManualAcceptRecordResp")
	proto.RegisterType((*MarkRecordReadReq)(nil), "mijing_trifle.MarkRecordReadReq")
	proto.RegisterType((*MarkRecordReadResp)(nil), "mijing_trifle.MarkRecordReadResp")
	proto.RegisterType((*DebugFinishTrifleTaskReq)(nil), "mijing_trifle.DebugFinishTrifleTaskReq")
	proto.RegisterType((*TrifleVisualStyle)(nil), "mijing_trifle.TrifleVisualStyle")
	proto.RegisterType((*TrifleVisualStyle_EntranceHasFriends)(nil), "mijing_trifle.TrifleVisualStyle.EntranceHasFriends")
	proto.RegisterType((*UpdateTrifleVisualStyleRequest)(nil), "mijing_trifle.UpdateTrifleVisualStyleRequest")
	proto.RegisterType((*UpdateTrifleVisualStyleResponse)(nil), "mijing_trifle.UpdateTrifleVisualStyleResponse")
	proto.RegisterType((*DeleteTrifleVisualStyleRequest)(nil), "mijing_trifle.DeleteTrifleVisualStyleRequest")
	proto.RegisterType((*DeleteTrifleVisualStyleResponse)(nil), "mijing_trifle.DeleteTrifleVisualStyleResponse")
	proto.RegisterType((*GetTrifleVisualStyleListRequest)(nil), "mijing_trifle.GetTrifleVisualStyleListRequest")
	proto.RegisterType((*GetTrifleVisualStyleListResponse)(nil), "mijing_trifle.GetTrifleVisualStyleListResponse")
	proto.RegisterEnum("mijing_trifle.FinishMode", FinishMode_name, FinishMode_value)
	proto.RegisterEnum("mijing_trifle.DisplayMode", DisplayMode_name, DisplayMode_value)
	proto.RegisterEnum("mijing_trifle.EventType", EventType_name, EventType_value)
	proto.RegisterEnum("mijing_trifle.TrifleStatus", TrifleStatus_name, TrifleStatus_value)
	proto.RegisterEnum("mijing_trifle.TrifleVisualStyle_Platform", TrifleVisualStyle_Platform_name, TrifleVisualStyle_Platform_value)
	proto.RegisterEnum("mijing_trifle.GetTrifleVisualStyleListRequest_DataSource", GetTrifleVisualStyleListRequest_DataSource_name, GetTrifleVisualStyleListRequest_DataSource_value)
	proto.RegisterEnum("mijing_trifle.GetTrifleVisualStyleListRequest_ActiveStatus", GetTrifleVisualStyleListRequest_ActiveStatus_name, GetTrifleVisualStyleListRequest_ActiveStatus_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// MijingTrifleClient is the client API for MijingTrifle service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type MijingTrifleClient interface {
	// 获取谜之小事任务列表
	GetTrifleTask(ctx context.Context, in *GetTrifleTaskReq, opts ...grpc.CallOption) (*GetTrifleTaskResp, error)
	// 更新谜之小事任务
	UpdateTrifleTask(ctx context.Context, in *UpdateTrifleTaskReq, opts ...grpc.CallOption) (*UpdateTrifleTaskResp, error)
	// 获取谜之小事任务记录
	GetTrifleRecord(ctx context.Context, in *GetTrifleRecordReq, opts ...grpc.CallOption) (*GetTrifleRecordResp, error)
	// 修改已读状态
	MarkRecordRead(ctx context.Context, in *MarkRecordReadReq, opts ...grpc.CallOption) (*MarkRecordReadResp, error)
	// 标记完成任务
	FinishTrifleTask(ctx context.Context, in *FinishTrifleTaskReq, opts ...grpc.CallOption) (*FinishTrifleTaskResp, error)
	// 新增 or 更新谜之小事视觉风格配置
	UpdateTrifleVisualStyle(ctx context.Context, in *UpdateTrifleVisualStyleRequest, opts ...grpc.CallOption) (*UpdateTrifleVisualStyleResponse, error)
	// 删除谜之小事视觉风格配置
	DeleteTrifleVisualStyle(ctx context.Context, in *DeleteTrifleVisualStyleRequest, opts ...grpc.CallOption) (*DeleteTrifleVisualStyleResponse, error)
	// 获取谜之小事视觉风格配置列表
	GetTrifleVisualStyleList(ctx context.Context, in *GetTrifleVisualStyleListRequest, opts ...grpc.CallOption) (*GetTrifleVisualStyleListResponse, error)
	// ---------------------------------------------------------------------------------------
	// 手动领取谜之小事奖励
	//  rpc ManualAcceptRecord (ManualAcceptRecordReq) returns (ManualAcceptRecordResp) {}
	// 更新谜之小事奖励配置
	//  rpc UpdateAwardConfig (UpdateAwardConfigReq) returns (UpdateAwardConfigResp) {}
	// 获取谜之小事奖励配置列表
	//  rpc GetAwardConfig (GetAwardConfigReq) returns (GetAwardConfigResp) {}
	// 完成谜之小事任务上报
	DebugFinishTrifleTask(ctx context.Context, in *DebugFinishTrifleTaskReq, opts ...grpc.CallOption) (*FinishTrifleTaskResp, error)
}

type mijingTrifleClient struct {
	cc *grpc.ClientConn
}

func NewMijingTrifleClient(cc *grpc.ClientConn) MijingTrifleClient {
	return &mijingTrifleClient{cc}
}

func (c *mijingTrifleClient) GetTrifleTask(ctx context.Context, in *GetTrifleTaskReq, opts ...grpc.CallOption) (*GetTrifleTaskResp, error) {
	out := new(GetTrifleTaskResp)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/GetTrifleTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) UpdateTrifleTask(ctx context.Context, in *UpdateTrifleTaskReq, opts ...grpc.CallOption) (*UpdateTrifleTaskResp, error) {
	out := new(UpdateTrifleTaskResp)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/UpdateTrifleTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) GetTrifleRecord(ctx context.Context, in *GetTrifleRecordReq, opts ...grpc.CallOption) (*GetTrifleRecordResp, error) {
	out := new(GetTrifleRecordResp)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/GetTrifleRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) MarkRecordRead(ctx context.Context, in *MarkRecordReadReq, opts ...grpc.CallOption) (*MarkRecordReadResp, error) {
	out := new(MarkRecordReadResp)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/MarkRecordRead", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) FinishTrifleTask(ctx context.Context, in *FinishTrifleTaskReq, opts ...grpc.CallOption) (*FinishTrifleTaskResp, error) {
	out := new(FinishTrifleTaskResp)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/FinishTrifleTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) UpdateTrifleVisualStyle(ctx context.Context, in *UpdateTrifleVisualStyleRequest, opts ...grpc.CallOption) (*UpdateTrifleVisualStyleResponse, error) {
	out := new(UpdateTrifleVisualStyleResponse)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/UpdateTrifleVisualStyle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) DeleteTrifleVisualStyle(ctx context.Context, in *DeleteTrifleVisualStyleRequest, opts ...grpc.CallOption) (*DeleteTrifleVisualStyleResponse, error) {
	out := new(DeleteTrifleVisualStyleResponse)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/DeleteTrifleVisualStyle", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) GetTrifleVisualStyleList(ctx context.Context, in *GetTrifleVisualStyleListRequest, opts ...grpc.CallOption) (*GetTrifleVisualStyleListResponse, error) {
	out := new(GetTrifleVisualStyleListResponse)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/GetTrifleVisualStyleList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *mijingTrifleClient) DebugFinishTrifleTask(ctx context.Context, in *DebugFinishTrifleTaskReq, opts ...grpc.CallOption) (*FinishTrifleTaskResp, error) {
	out := new(FinishTrifleTaskResp)
	err := c.cc.Invoke(ctx, "/mijing_trifle.MijingTrifle/DebugFinishTrifleTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MijingTrifleServer is the server API for MijingTrifle service.
type MijingTrifleServer interface {
	// 获取谜之小事任务列表
	GetTrifleTask(context.Context, *GetTrifleTaskReq) (*GetTrifleTaskResp, error)
	// 更新谜之小事任务
	UpdateTrifleTask(context.Context, *UpdateTrifleTaskReq) (*UpdateTrifleTaskResp, error)
	// 获取谜之小事任务记录
	GetTrifleRecord(context.Context, *GetTrifleRecordReq) (*GetTrifleRecordResp, error)
	// 修改已读状态
	MarkRecordRead(context.Context, *MarkRecordReadReq) (*MarkRecordReadResp, error)
	// 标记完成任务
	FinishTrifleTask(context.Context, *FinishTrifleTaskReq) (*FinishTrifleTaskResp, error)
	// 新增 or 更新谜之小事视觉风格配置
	UpdateTrifleVisualStyle(context.Context, *UpdateTrifleVisualStyleRequest) (*UpdateTrifleVisualStyleResponse, error)
	// 删除谜之小事视觉风格配置
	DeleteTrifleVisualStyle(context.Context, *DeleteTrifleVisualStyleRequest) (*DeleteTrifleVisualStyleResponse, error)
	// 获取谜之小事视觉风格配置列表
	GetTrifleVisualStyleList(context.Context, *GetTrifleVisualStyleListRequest) (*GetTrifleVisualStyleListResponse, error)
	// ---------------------------------------------------------------------------------------
	// 手动领取谜之小事奖励
	//  rpc ManualAcceptRecord (ManualAcceptRecordReq) returns (ManualAcceptRecordResp) {}
	// 更新谜之小事奖励配置
	//  rpc UpdateAwardConfig (UpdateAwardConfigReq) returns (UpdateAwardConfigResp) {}
	// 获取谜之小事奖励配置列表
	//  rpc GetAwardConfig (GetAwardConfigReq) returns (GetAwardConfigResp) {}
	// 完成谜之小事任务上报
	DebugFinishTrifleTask(context.Context, *DebugFinishTrifleTaskReq) (*FinishTrifleTaskResp, error)
}

func RegisterMijingTrifleServer(s *grpc.Server, srv MijingTrifleServer) {
	s.RegisterService(&_MijingTrifle_serviceDesc, srv)
}

func _MijingTrifle_GetTrifleTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrifleTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).GetTrifleTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/GetTrifleTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).GetTrifleTask(ctx, req.(*GetTrifleTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_UpdateTrifleTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTrifleTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).UpdateTrifleTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/UpdateTrifleTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).UpdateTrifleTask(ctx, req.(*UpdateTrifleTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_GetTrifleRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrifleRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).GetTrifleRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/GetTrifleRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).GetTrifleRecord(ctx, req.(*GetTrifleRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_MarkRecordRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkRecordReadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).MarkRecordRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/MarkRecordRead",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).MarkRecordRead(ctx, req.(*MarkRecordReadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_FinishTrifleTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FinishTrifleTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).FinishTrifleTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/FinishTrifleTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).FinishTrifleTask(ctx, req.(*FinishTrifleTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_UpdateTrifleVisualStyle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTrifleVisualStyleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).UpdateTrifleVisualStyle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/UpdateTrifleVisualStyle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).UpdateTrifleVisualStyle(ctx, req.(*UpdateTrifleVisualStyleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_DeleteTrifleVisualStyle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTrifleVisualStyleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).DeleteTrifleVisualStyle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/DeleteTrifleVisualStyle",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).DeleteTrifleVisualStyle(ctx, req.(*DeleteTrifleVisualStyleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_GetTrifleVisualStyleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTrifleVisualStyleListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).GetTrifleVisualStyleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/GetTrifleVisualStyleList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).GetTrifleVisualStyleList(ctx, req.(*GetTrifleVisualStyleListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MijingTrifle_DebugFinishTrifleTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DebugFinishTrifleTaskReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MijingTrifleServer).DebugFinishTrifleTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/mijing_trifle.MijingTrifle/DebugFinishTrifleTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MijingTrifleServer).DebugFinishTrifleTask(ctx, req.(*DebugFinishTrifleTaskReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _MijingTrifle_serviceDesc = grpc.ServiceDesc{
	ServiceName: "mijing_trifle.MijingTrifle",
	HandlerType: (*MijingTrifleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTrifleTask",
			Handler:    _MijingTrifle_GetTrifleTask_Handler,
		},
		{
			MethodName: "UpdateTrifleTask",
			Handler:    _MijingTrifle_UpdateTrifleTask_Handler,
		},
		{
			MethodName: "GetTrifleRecord",
			Handler:    _MijingTrifle_GetTrifleRecord_Handler,
		},
		{
			MethodName: "MarkRecordRead",
			Handler:    _MijingTrifle_MarkRecordRead_Handler,
		},
		{
			MethodName: "FinishTrifleTask",
			Handler:    _MijingTrifle_FinishTrifleTask_Handler,
		},
		{
			MethodName: "UpdateTrifleVisualStyle",
			Handler:    _MijingTrifle_UpdateTrifleVisualStyle_Handler,
		},
		{
			MethodName: "DeleteTrifleVisualStyle",
			Handler:    _MijingTrifle_DeleteTrifleVisualStyle_Handler,
		},
		{
			MethodName: "GetTrifleVisualStyleList",
			Handler:    _MijingTrifle_GetTrifleVisualStyleList_Handler,
		},
		{
			MethodName: "DebugFinishTrifleTask",
			Handler:    _MijingTrifle_DebugFinishTrifleTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "mijing-trifle/mijing-trifle.proto",
}

func init() {
	proto.RegisterFile("mijing-trifle/mijing-trifle.proto", fileDescriptor_mijing_trifle_10c2b312cd2a4ef3)
}

var fileDescriptor_mijing_trifle_10c2b312cd2a4ef3 = []byte{
	// 2175 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x58, 0xdd, 0x6e, 0xdb, 0xca,
	0x11, 0xb6, 0x24, 0xff, 0x48, 0x23, 0xc9, 0x87, 0x5e, 0xdb, 0x31, 0xe3, 0x34, 0x89, 0xcc, 0x1c,
	0xb4, 0x6e, 0xd0, 0xd8, 0xad, 0x83, 0xa0, 0x3d, 0x2d, 0x0e, 0x5a, 0x46, 0x92, 0x6d, 0x21, 0xfa,
	0x31, 0x28, 0x29, 0x86, 0x03, 0x14, 0xec, 0x9a, 0x5c, 0xd9, 0x6c, 0x68, 0x52, 0xe1, 0x2e, 0x73,
	0xe2, 0x02, 0xb9, 0xeb, 0x5d, 0xdf, 0xa5, 0xe8, 0x5d, 0x9f, 0xa1, 0x77, 0xbd, 0xea, 0x7d, 0x1f,
	0xa0, 0xef, 0x50, 0xec, 0x2e, 0x49, 0x51, 0x94, 0x64, 0x27, 0x40, 0x7b, 0x63, 0x68, 0xbf, 0xf9,
	0x66, 0x66, 0x67, 0x76, 0x76, 0x66, 0x69, 0xd8, 0xbb, 0x71, 0xfe, 0xe8, 0x78, 0x57, 0x2f, 0x58,
	0xe0, 0x8c, 0x5c, 0x72, 0x38, 0xb5, 0x3a, 0x18, 0x07, 0x3e, 0xf3, 0x51, 0x55, 0x82, 0xa6, 0x04,
	0xb5, 0x7f, 0xaf, 0x00, 0x0c, 0xc4, 0xcf, 0x01, 0xa6, 0xef, 0xd1, 0x3a, 0xe4, 0x1d, 0x5b, 0xcd,
	0xd5, 0x72, 0xfb, 0x25, 0x23, 0xef, 0xd8, 0x68, 0x0b, 0x56, 0x98, 0xc3, 0x5c, 0xa2, 0xe6, 0x05,
	0x24, 0x17, 0xe8, 0x7b, 0xa8, 0xd8, 0x0e, 0x1d, 0xbb, 0xf8, 0xd6, 0xbc, 0xf1, 0x6d, 0xa2, 0x2e,
	0xd7, 0x72, 0xfb, 0xeb, 0x47, 0xbb, 0x07, 0x53, 0xa6, 0x0f, 0x1a, 0x92, 0xd2, 0xf1, 0x6d, 0x62,
	0x94, 0xed, 0xc9, 0x02, 0xfd, 0x1a, 0xca, 0x23, 0xc7, 0x73, 0xe8, 0xb5, 0xd4, 0x5e, 0x11, 0xda,
	0x0f, 0x33, 0xda, 0xc7, 0x82, 0x21, 0x94, 0x61, 0x94, 0xfc, 0x46, 0xbb, 0x50, 0xa4, 0xe4, 0x43,
	0x48, 0x3c, 0x8b, 0xa8, 0xab, 0xb5, 0xdc, 0x7e, 0xde, 0x48, 0xd6, 0xe8, 0x08, 0x56, 0xf1, 0x0f,
	0x38, 0xb0, 0xa9, 0x5a, 0xac, 0x15, 0xf6, 0xcb, 0x33, 0x1b, 0x92, 0x71, 0xea, 0x9c, 0x62, 0x44,
	0x4c, 0xf4, 0x0c, 0xaa, 0xd7, 0xfe, 0x0d, 0x19, 0xe3, 0x2b, 0x62, 0x32, 0xf2, 0x89, 0xa9, 0x25,
	0x11, 0x68, 0x25, 0x06, 0x07, 0xe4, 0x13, 0x43, 0x7b, 0x50, 0x61, 0x98, 0xbe, 0x37, 0x2d, 0xdf,
	0x63, 0xc4, 0x63, 0x2a, 0x08, 0x4e, 0x99, 0x63, 0x75, 0x09, 0xa1, 0x47, 0x50, 0x12, 0x94, 0x20,
	0x74, 0x89, 0x5a, 0x16, 0xf2, 0x22, 0x07, 0x8c, 0xd0, 0x25, 0x5c, 0xe8, 0xdc, 0x70, 0x0f, 0x61,
	0xe0, 0xa8, 0x55, 0x29, 0x14, 0xc0, 0x30, 0x70, 0xd0, 0xb7, 0xb0, 0xee, 0x62, 0xca, 0x78, 0x2e,
	0x9c, 0xd1, 0xad, 0x89, 0x99, 0xaa, 0xd4, 0x72, 0xfb, 0x05, 0xa3, 0xc2, 0xd1, 0x8e, 0x00, 0x75,
	0x86, 0x1e, 0xc0, 0x2a, 0x65, 0x98, 0x85, 0x54, 0xdd, 0xa8, 0xe5, 0xf6, 0x57, 0x8c, 0x68, 0xc5,
	0xb7, 0x46, 0x2d, 0xe2, 0xe1, 0xc0, 0xf1, 0x4d, 0xc7, 0xa6, 0x2a, 0xaa, 0x15, 0xf6, 0xab, 0x46,
	0x39, 0xc6, 0x5a, 0x36, 0x45, 0x3f, 0x87, 0xad, 0xd0, 0x93, 0x29, 0x24, 0xb6, 0x39, 0xd9, 0xc8,
	0xa6, 0xd8, 0x08, 0x9a, 0xc8, 0x5a, 0xf1, 0x96, 0x5e, 0xc1, 0x8e, 0x75, 0x8d, 0x3d, 0x8f, 0xb8,
	0x26, 0xbd, 0xc6, 0x01, 0x49, 0x29, 0x6d, 0x0b, 0xa5, 0xad, 0x48, 0xdc, 0xe7, 0xd2, 0x44, 0xed,
	0x97, 0x00, 0xe4, 0x23, 0xf1, 0x98, 0xc9, 0x6e, 0xc7, 0x44, 0x7d, 0x20, 0x8e, 0x55, 0xcd, 0x9c,
	0x41, 0x93, 0x13, 0x06, 0xb7, 0x63, 0x62, 0x94, 0x48, 0xfc, 0x13, 0x9d, 0xc1, 0xe6, 0x18, 0x53,
	0x6a, 0x8a, 0x0c, 0x4a, 0x13, 0xd6, 0xe8, 0x4a, 0xdd, 0xa9, 0xe5, 0xf6, 0xcb, 0x47, 0x4f, 0x33,
	0x16, 0xce, 0x30, 0xa5, 0xbc, 0x56, 0x85, 0xa5, 0xfa, 0xe8, 0xea, 0x74, 0xc9, 0x50, 0xc6, 0x19,
	0xec, 0x75, 0x19, 0x4a, 0x89, 0x1d, 0xed, 0x12, 0x94, 0xac, 0x12, 0xfa, 0x31, 0x7c, 0x23, 0x42,
	0xb3, 0xa5, 0x53, 0x9e, 0xba, 0x5c, 0xad, 0xb0, 0x5f, 0x32, 0xaa, 0x12, 0xe6, 0x64, 0x9e, 0x3c,
	0xce, 0x73, 0xbc, 0x2b, 0x97, 0x4c, 0x78, 0xf9, 0x88, 0x27, 0xe0, 0x88, 0xa7, 0xbd, 0x84, 0x72,
	0xaa, 0xbc, 0x52, 0xf7, 0xa8, 0x1a, 0xdf, 0x23, 0xcb, 0x0f, 0x3d, 0x26, 0xee, 0x51, 0xd5, 0x90,
	0x0b, 0xed, 0xaf, 0x39, 0x28, 0x0b, 0x7e, 0xdd, 0xf7, 0x46, 0xce, 0xd5, 0x8c, 0x16, 0x82, 0x65,
	0x0f, 0xdf, 0xc4, 0x97, 0x4f, 0xfc, 0xe6, 0x98, 0x4d, 0xa8, 0xa5, 0x16, 0x24, 0xc6, 0x7f, 0xa3,
	0xc7, 0x00, 0xa2, 0x9c, 0x65, 0xe2, 0x97, 0x85, 0x7e, 0x49, 0x20, 0x22, 0xbd, 0x89, 0xf3, 0x95,
	0x94, 0x73, 0x7e, 0x93, 0xec, 0x30, 0xc0, 0xcc, 0xf1, 0x3d, 0x71, 0x93, 0x0a, 0x46, 0xb2, 0x4e,
	0x55, 0xdb, 0x5a, 0xba, 0xda, 0xb4, 0xbf, 0xe5, 0xa1, 0x22, 0xc3, 0x34, 0x88, 0xe5, 0x07, 0x36,
	0x7a, 0x01, 0xcb, 0x3c, 0x2f, 0x62, 0xcf, 0xe5, 0x99, 0x3b, 0x3c, 0x69, 0x2c, 0x86, 0xa0, 0xf1,
	0x8b, 0x10, 0x08, 0x45, 0xd3, 0xb1, 0xa3, 0xa8, 0x8a, 0x12, 0x68, 0xd9, 0xe8, 0x77, 0x50, 0x95,
	0x7a, 0x66, 0xe4, 0xbb, 0x20, 0x2a, 0xe8, 0xd1, 0x5c, 0xa3, 0x7d, 0x41, 0x31, 0x2a, 0x2c, 0xb5,
	0xe2, 0x97, 0x01, 0xbb, 0x01, 0xc1, 0xf6, 0xad, 0xc9, 0xff, 0x8a, 0x4c, 0x14, 0x8d, 0x72, 0x84,
	0x19, 0x04, 0xdb, 0xc9, 0x6d, 0x8b, 0x1a, 0x10, 0x96, 0x49, 0x89, 0x6e, 0x9b, 0xec, 0x39, 0x3a,
	0xe3, 0xa7, 0x3e, 0x72, 0x82, 0x29, 0x9a, 0x4c, 0x51, 0x55, 0xc0, 0x09, 0x6f, 0x0f, 0x2a, 0x01,
	0x71, 0x31, 0x23, 0xb6, 0x19, 0xf2, 0xd2, 0x58, 0x93, 0xb7, 0x2f, 0xc2, 0x86, 0x8e, 0x4d, 0xb5,
	0x7f, 0xe4, 0x60, 0x53, 0xf2, 0x53, 0xd9, 0x20, 0x1f, 0x90, 0x02, 0x85, 0x30, 0x39, 0x6c, 0xfe,
	0x13, 0xed, 0xc0, 0x5a, 0x54, 0x63, 0x51, 0x6a, 0x56, 0x99, 0x28, 0x2e, 0x7e, 0xbc, 0xa1, 0xe7,
	0x7c, 0x08, 0x89, 0xf9, 0x9e, 0xdc, 0x46, 0x07, 0x5f, 0x92, 0xc8, 0x1b, 0x72, 0xcb, 0x93, 0x3a,
	0xd9, 0xe6, 0xb2, 0x3c, 0xc9, 0x51, 0xbc, 0xc3, 0x7d, 0x50, 0xc8, 0xa7, 0x31, 0xb1, 0x98, 0x49,
	0x89, 0x67, 0x9b, 0xe3, 0x90, 0x5e, 0x8b, 0x88, 0x8b, 0xc6, 0xba, 0xc4, 0xfb, 0xc4, 0xb3, 0xcf,
	0x42, 0x7a, 0x3d, 0x13, 0x0b, 0xcc, 0xc6, 0x72, 0x04, 0x5b, 0xb3, 0xa1, 0xd0, 0x31, 0x2f, 0xa5,
	0xb8, 0x87, 0x88, 0x80, 0x8a, 0x46, 0xb2, 0xd6, 0x4c, 0x50, 0x4e, 0x08, 0x9b, 0x8e, 0xfd, 0x19,
	0x54, 0x7f, 0x70, 0xd8, 0xb5, 0x1f, 0x32, 0xd3, 0xc2, 0xd6, 0xb5, 0x6c, 0x98, 0x45, 0xa3, 0x12,
	0x81, 0x75, 0x8e, 0xf1, 0x33, 0x08, 0x08, 0x0b, 0x03, 0xcf, 0xc4, 0xae, 0x2b, 0x6e, 0x9f, 0x5a,
	0x11, 0xb4, 0xaa, 0x84, 0x75, 0xd7, 0xe5, 0xf6, 0xb4, 0x06, 0x6c, 0x64, 0x1c, 0xd0, 0x31, 0x3a,
	0x84, 0x15, 0xae, 0x21, 0x2f, 0xf5, 0x9d, 0x85, 0x29, 0x79, 0xda, 0x10, 0x50, 0x62, 0x45, 0xd6,
	0xf6, 0xfc, 0x43, 0x7a, 0x01, 0xc8, 0xf1, 0x2c, 0x37, 0xb4, 0x89, 0x39, 0x69, 0x9c, 0xd1, 0xfe,
	0x37, 0x22, 0xc9, 0x30, 0x11, 0x68, 0x6d, 0xd8, 0x9c, 0x31, 0x4b, 0xc7, 0xe8, 0x15, 0xac, 0xc9,
	0xb2, 0x8f, 0x37, 0x38, 0xbf, 0xc8, 0x23, 0x8d, 0x98, 0xab, 0x35, 0x60, 0x73, 0x38, 0xb6, 0x31,
	0x23, 0xd3, 0xe9, 0xfc, 0xba, 0x4b, 0xa8, 0x3d, 0x80, 0xad, 0x59, 0x2b, 0x74, 0xac, 0x0d, 0x63,
	0x3c, 0xd5, 0x92, 0xb8, 0xf9, 0xef, 0xa1, 0x22, 0xbb, 0x8b, 0x25, 0xa0, 0xc8, 0x4d, 0x76, 0xb8,
	0xa6, 0x95, 0xca, 0x78, 0xb2, 0xd0, 0x76, 0x60, 0x7b, 0x8e, 0x59, 0x3a, 0xd6, 0x7e, 0x25, 0x0e,
	0x2e, 0xe3, 0xec, 0x4b, 0x4a, 0x23, 0x3a, 0xac, 0x8c, 0x3d, 0xf4, 0x5b, 0xa8, 0xa6, 0xf7, 0x19,
	0xa7, 0xf6, 0xae, 0x8d, 0x56, 0x52, 0x1b, 0xa5, 0x1a, 0x81, 0xed, 0x0e, 0xf6, 0x42, 0xec, 0xea,
	0x96, 0x45, 0xc6, 0xec, 0xae, 0x32, 0xb8, 0xb3, 0x91, 0x3d, 0x06, 0xb0, 0x09, 0xc3, 0x8e, 0x2b,
	0xc6, 0x45, 0xa1, 0x56, 0xd8, 0x5f, 0x36, 0x4a, 0x12, 0xe1, 0xa3, 0x42, 0x85, 0x07, 0xf3, 0xdc,
	0xd0, 0x31, 0x2f, 0xe5, 0x0e, 0x0e, 0xde, 0xc7, 0x08, 0x5e, 0xe0, 0xfc, 0x31, 0x40, 0xe2, 0x3c,
	0x1e, 0x47, 0xa5, 0xd8, 0x3b, 0xd5, 0xb6, 0x00, 0x65, 0xad, 0xd0, 0xb1, 0xf6, 0xaf, 0x1c, 0xa8,
	0x0d, 0x72, 0x19, 0x5e, 0xfd, 0x7f, 0x9b, 0xd1, 0xfd, 0x5d, 0x04, 0x1d, 0xc0, 0x66, 0x80, 0x3d,
	0xdb, 0xbf, 0x89, 0xbb, 0xab, 0x1c, 0x4e, 0x65, 0xe1, 0x7c, 0x43, 0x8a, 0xe4, 0x26, 0xeb, 0x62,
	0x50, 0x89, 0x74, 0xba, 0x84, 0x11, 0xde, 0x08, 0xa2, 0x1e, 0x50, 0x92, 0x88, 0xee, 0xba, 0xda,
	0x7f, 0x56, 0x60, 0x43, 0x46, 0xf3, 0xd6, 0xa1, 0x21, 0x76, 0xfb, 0xec, 0xd6, 0x25, 0x33, 0x0f,
	0xd9, 0x79, 0xa3, 0xb4, 0x09, 0xc5, 0xb1, 0x8b, 0xd9, 0xc8, 0x0f, 0x6e, 0xa2, 0x59, 0xf3, 0xd3,
	0xb9, 0x77, 0x27, 0x65, 0xf7, 0xe0, 0x2c, 0x52, 0x30, 0x12, 0x55, 0xf4, 0x33, 0x40, 0x97, 0xd8,
	0x7a, 0x7f, 0x15, 0xf8, 0x21, 0xef, 0xb0, 0x8e, 0x65, 0x86, 0x81, 0x2b, 0x1a, 0x71, 0xc9, 0x50,
	0x26, 0x92, 0x33, 0xc7, 0x1a, 0x06, 0xae, 0x68, 0xc8, 0x1e, 0x0b, 0xb0, 0x67, 0x91, 0x84, 0xbb,
	0x22, 0xb8, 0xeb, 0x31, 0x1e, 0x31, 0x6b, 0x50, 0xa1, 0x04, 0xb3, 0x84, 0xb5, 0x2a, 0x58, 0xc0,
	0xb1, 0x88, 0xf1, 0x2d, 0xac, 0x3b, 0xde, 0x47, 0x87, 0x4d, 0x2c, 0xad, 0xc9, 0xd7, 0xab, 0x44,
	0x23, 0xd6, 0x2b, 0xd8, 0x49, 0x3c, 0x7a, 0xbe, 0x39, 0x0a, 0x1c, 0xe2, 0xd9, 0x54, 0xd0, 0x8b,
	0xf2, 0x35, 0x17, 0x8b, 0xbb, 0xfe, 0xb1, 0x14, 0x72, 0x35, 0x0f, 0x1e, 0x26, 0x6a, 0xd7, 0x98,
	0x26, 0x7a, 0xae, 0x43, 0xf9, 0x2b, 0x99, 0x5f, 0xad, 0x97, 0xf7, 0xa6, 0xab, 0x19, 0x59, 0x38,
	0xc5, 0x34, 0x32, 0x6d, 0x3c, 0x20, 0x33, 0x58, 0xdb, 0xa1, 0x0c, 0x3d, 0x84, 0x22, 0x65, 0x38,
	0x60, 0x7c, 0x8a, 0x81, 0x98, 0x62, 0x6b, 0x62, 0xad, 0x33, 0xb4, 0x0d, 0xab, 0x7c, 0x78, 0x61,
	0x59, 0x24, 0x05, 0x63, 0x85, 0x78, 0xb6, 0x2e, 0x0a, 0xc3, 0x0a, 0x88, 0xa8, 0x35, 0xcc, 0x54,
	0x22, 0x44, 0xa5, 0x08, 0x91, 0xe2, 0x50, 0x34, 0x1e, 0x21, 0x1e, 0x49, 0x71, 0x84, 0xe8, 0x6c,
	0xf7, 0x1a, 0xd0, 0xec, 0xee, 0xf8, 0xd4, 0xb9, 0x0c, 0x2f, 0x2f, 0x5d, 0x62, 0x5e, 0xfa, 0xf6,
	0xad, 0x48, 0x92, 0x2c, 0xa2, 0xaa, 0x84, 0x5f, 0xfb, 0xf6, 0x2d, 0xcf, 0xce, 0x73, 0xd8, 0x48,
	0x78, 0x81, 0x4d, 0x02, 0xc1, 0x94, 0xc5, 0xf5, 0x4d, 0xcc, 0xe4, 0xf8, 0x30, 0x70, 0xb5, 0x36,
	0x14, 0xe3, 0xb2, 0x41, 0x2a, 0x6c, 0x9d, 0xb5, 0xf5, 0xc1, 0x71, 0xcf, 0xe8, 0x98, 0xc3, 0x6e,
	0xff, 0xac, 0x59, 0x6f, 0x1d, 0xb7, 0x9a, 0x0d, 0x65, 0x09, 0x29, 0x50, 0x49, 0x24, 0xad, 0x5e,
	0x5f, 0xc9, 0xa1, 0x2d, 0x50, 0x12, 0x44, 0xef, 0x36, 0x8c, 0x5e, 0xab, 0xa1, 0xe4, 0xb5, 0x00,
	0x9e, 0xa4, 0xdb, 0x77, 0x2a, 0xdb, 0x06, 0xff, 0x10, 0xa2, 0x8c, 0x3f, 0xa7, 0xa3, 0x87, 0xd4,
	0x47, 0x21, 0x34, 0x29, 0x97, 0x46, 0x7d, 0xbb, 0x76, 0xdf, 0x99, 0x19, 0x1b, 0x2c, 0x0b, 0x69,
	0x7b, 0xf0, 0x74, 0xa1, 0x4f, 0x3a, 0xf6, 0x3d, 0x4a, 0xb4, 0xef, 0xe0, 0x49, 0x43, 0xdc, 0xc9,
	0x85, 0xdb, 0xda, 0x81, 0x35, 0xc7, 0x96, 0xe5, 0x23, 0x9f, 0xda, 0xab, 0x8e, 0xcd, 0x4f, 0x9e,
	0x5b, 0x5f, 0xa8, 0x1a, 0x59, 0xff, 0x67, 0x01, 0x9e, 0x26, 0x83, 0x34, 0x45, 0xe0, 0xfa, 0xb1,
	0xfd, 0x77, 0x50, 0xb6, 0x31, 0xc3, 0x26, 0xf5, 0xc3, 0xc0, 0x92, 0xe1, 0xae, 0x1f, 0x7d, 0x97,
	0x09, 0xf7, 0x1e, 0x23, 0x07, 0x0d, 0xcc, 0x70, 0x5f, 0x18, 0x30, 0xc0, 0x4e, 0x7e, 0xa3, 0x3f,
	0x40, 0x15, 0x5b, 0xcc, 0xf9, 0x98, 0xbc, 0x4d, 0xf3, 0xc2, 0xfa, 0x6f, 0xbe, 0xd2, 0xba, 0x2e,
	0x6c, 0xc4, 0x6f, 0x57, 0x9c, 0x5a, 0xfd, 0x8f, 0x9a, 0x91, 0x66, 0x02, 0x4c, 0x42, 0x40, 0x8f,
	0x60, 0xa7, 0xa1, 0x0f, 0x74, 0xb3, 0xdf, 0x1b, 0x1a, 0xf5, 0x66, 0xa6, 0xe0, 0x32, 0xc2, 0x76,
	0xaf, 0xae, 0xb7, 0xcd, 0xba, 0x5e, 0x3f, 0x6d, 0x2a, 0x39, 0xb4, 0x03, 0x9b, 0x69, 0x61, 0xa7,
	0xd7, 0x3d, 0xe9, 0x35, 0x5e, 0x2b, 0x79, 0xed, 0x04, 0x2a, 0xe9, 0x28, 0xd0, 0x63, 0x78, 0xa8,
	0xd7, 0x07, 0xad, 0xb7, 0x4d, 0xb3, 0x3f, 0xd0, 0x07, 0xc3, 0x7e, 0xc6, 0x89, 0x0a, 0x5b, 0xd3,
	0x62, 0xb9, 0x52, 0x72, 0xda, 0x67, 0xa8, 0x2d, 0x4e, 0x97, 0x3c, 0x76, 0x74, 0x01, 0xea, 0x9c,
	0x4a, 0x9e, 0xd4, 0xd0, 0x97, 0x94, 0xf3, 0x36, 0x9b, 0xe7, 0xe2, 0xf9, 0x5f, 0x72, 0x00, 0x93,
	0xff, 0x31, 0xf0, 0x78, 0x8f, 0x5b, 0xdd, 0x56, 0xff, 0xd4, 0xec, 0xf4, 0x1a, 0x3c, 0x53, 0x6f,
	0xba, 0xbd, 0xf3, 0xae, 0xb2, 0x84, 0xf6, 0xe0, 0x71, 0x5a, 0xd0, 0xeb, 0xb6, 0x2f, 0xcc, 0x5e,
	0xb7, 0xde, 0x34, 0x8f, 0x7b, 0x46, 0xf3, 0x6d, 0xd3, 0x50, 0x72, 0xe8, 0x29, 0x3c, 0x9a, 0x4f,
	0x69, 0xe8, 0xad, 0xf6, 0x85, 0x92, 0x47, 0x35, 0xf8, 0x51, 0x9a, 0xd0, 0x19, 0xb6, 0x07, 0x2d,
	0x73, 0xd0, 0xea, 0xc4, 0x8c, 0xc2, 0xf3, 0xcf, 0x50, 0x4e, 0xfd, 0xbb, 0x84, 0x67, 0xad, 0xd1,
	0xea, 0x9f, 0xb5, 0xf5, 0x8b, 0xec, 0x76, 0xf8, 0xb9, 0xa4, 0x25, 0x7a, 0xfb, 0x5c, 0xbf, 0xe0,
	0xcd, 0xe2, 0x09, 0xec, 0x4e, 0x09, 0xce, 0x4f, 0x9b, 0x5d, 0x53, 0x7a, 0x6d, 0x36, 0x94, 0xbc,
	0x38, 0xed, 0xb4, 0xbc, 0xcb, 0x37, 0x6f, 0xf6, 0x4f, 0x7b, 0xe7, 0x4a, 0xe1, 0xf9, 0x10, 0x4a,
	0xc9, 0x87, 0x39, 0xda, 0x86, 0x8d, 0xe6, 0xdb, 0x66, 0x77, 0x60, 0x0e, 0x2e, 0xce, 0x9a, 0x66,
	0xbd, 0xd7, 0xe9, 0xf4, 0x22, 0xcf, 0x29, 0xd8, 0x68, 0x9e, 0xb4, 0xfa, 0x03, 0x11, 0xbe, 0x0a,
	0x5b, 0x29, 0xc1, 0x99, 0xde, 0xef, 0x9b, 0x03, 0xbd, 0xff, 0x46, 0xc9, 0x3f, 0xff, 0x73, 0x2e,
	0xfe, 0x5c, 0x9c, 0x14, 0xcb, 0xc0, 0x68, 0x1d, 0xb7, 0x17, 0x17, 0xcb, 0xb4, 0xb8, 0xdd, 0xab,
	0xbf, 0x69, 0x36, 0x94, 0x1c, 0xcf, 0xe0, 0xb4, 0xe4, 0x5c, 0x6f, 0x0d, 0x5a, 0xdd, 0x13, 0x53,
	0xaf, 0xd7, 0x9b, 0x67, 0x03, 0x25, 0x8f, 0x1e, 0xc2, 0x76, 0x46, 0xb7, 0x75, 0x72, 0x3a, 0x68,
	0x36, 0x94, 0xc2, 0xd1, 0xdf, 0xd7, 0xa0, 0xd2, 0x11, 0x55, 0x22, 0x37, 0x83, 0x06, 0x50, 0x9d,
	0xfa, 0x64, 0x40, 0x4f, 0x17, 0xdd, 0xe3, 0xe8, 0x81, 0xb4, 0x5b, 0xbb, 0x9b, 0x40, 0xc7, 0xda,
	0x12, 0xfa, 0x3d, 0x28, 0xd9, 0x77, 0x35, 0xd2, 0x32, 0x7a, 0x73, 0x9e, 0xef, 0xbb, 0xcf, 0xee,
	0xe5, 0x08, 0xf3, 0xef, 0xe0, 0x9b, 0xcc, 0xa7, 0x04, 0xda, 0x5b, 0xb4, 0xab, 0xe4, 0xe9, 0xba,
	0xab, 0xdd, 0x47, 0x11, 0xb6, 0xcf, 0x61, 0x7d, 0xfa, 0xc9, 0x88, 0xb2, 0x01, 0xcf, 0xbc, 0x4b,
	0x77, 0xf7, 0xee, 0x61, 0xc4, 0x39, 0xc9, 0xbe, 0x37, 0x67, 0x72, 0x32, 0xe7, 0x41, 0x3a, 0x93,
	0x93, 0x79, 0x9f, 0x9d, 0xda, 0x12, 0xfa, 0x13, 0xec, 0x2c, 0x98, 0x4b, 0xe8, 0xc5, 0x1d, 0x59,
	0x9d, 0x1d, 0x4e, 0xbb, 0x07, 0x5f, 0x4a, 0x8f, 0x06, 0x92, 0xf0, 0xbd, 0x60, 0x6a, 0xcd, 0xf8,
	0xbe, 0x7b, 0x30, 0xce, 0xf8, 0xbe, 0x6f, 0x18, 0x2e, 0xa1, 0xcf, 0xa0, 0x2e, 0xea, 0x9d, 0xe8,
	0xe0, 0xeb, 0x66, 0xd2, 0xee, 0xe1, 0x17, 0xf3, 0x13, 0xf7, 0x57, 0xb0, 0x3d, 0xf7, 0x53, 0x02,
	0xfd, 0x64, 0x26, 0x92, 0xf9, 0x1f, 0x1c, 0x5f, 0x78, 0xbe, 0xaf, 0x7f, 0xf1, 0xee, 0xf0, 0xca,
	0x77, 0xb1, 0x77, 0x75, 0xf0, 0xea, 0x88, 0xb1, 0x03, 0xcb, 0xbf, 0x39, 0x14, 0xff, 0xc5, 0xb6,
	0x7c, 0xf7, 0x90, 0x92, 0xe0, 0xa3, 0x63, 0x11, 0x3a, 0xfd, 0x5f, 0xee, 0xcb, 0x55, 0x41, 0x78,
	0xf9, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x78, 0x33, 0x57, 0x8f, 0x0b, 0x17, 0x00, 0x00,
}
