// Code generated by protoc-gen-gogo.
// source: src/channelvotepkcommonsvr/channelcommonvotepk.proto
// DO NOT EDIT!

/*
	Package channelcommonvotepk is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelvotepkcommonsvr/channelcommonvotepk.proto

	It has these top-level messages:
		ChannelCommonPkInfo
		ChannelCommonPkCompetitor
		SetChannelCommonPkInfoReq
		SetChannelCommonPkInfoResp
		GetUserLeftVoteCntReq
		GetUserLeftVoteCntResp
		ChannelCommonPkVoteReq
		ChannelCommonPkVoteResp
*/
package channelcommonvotepk

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 人气投票的PK信息
type ChannelCommonPkInfo struct {
	ChannelId      uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StartTimestamp uint32 `protobuf:"varint,2,req,name=start_timestamp,json=startTimestamp" json:"start_timestamp"`
	DurationMin    uint32 `protobuf:"varint,3,req,name=duration_min,json=durationMin" json:"duration_min"`
	VoteCntLimit   uint32 `protobuf:"varint,4,req,name=vote_cnt_limit,json=voteCntLimit" json:"vote_cnt_limit"`
}

func (m *ChannelCommonPkInfo) Reset()         { *m = ChannelCommonPkInfo{} }
func (m *ChannelCommonPkInfo) String() string { return proto.CompactTextString(m) }
func (*ChannelCommonPkInfo) ProtoMessage()    {}
func (*ChannelCommonPkInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{0}
}

func (m *ChannelCommonPkInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelCommonPkInfo) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *ChannelCommonPkInfo) GetDurationMin() uint32 {
	if m != nil {
		return m.DurationMin
	}
	return 0
}

func (m *ChannelCommonPkInfo) GetVoteCntLimit() uint32 {
	if m != nil {
		return m.VoteCntLimit
	}
	return 0
}

type ChannelCommonPkCompetitor struct {
	ChannelId      uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	StartTimestamp uint32 `protobuf:"varint,2,req,name=start_timestamp,json=startTimestamp" json:"start_timestamp"`
	Uid            uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *ChannelCommonPkCompetitor) Reset()         { *m = ChannelCommonPkCompetitor{} }
func (m *ChannelCommonPkCompetitor) String() string { return proto.CompactTextString(m) }
func (*ChannelCommonPkCompetitor) ProtoMessage()    {}
func (*ChannelCommonPkCompetitor) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{1}
}

func (m *ChannelCommonPkCompetitor) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelCommonPkCompetitor) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *ChannelCommonPkCompetitor) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetChannelCommonPkInfoReq struct {
	Info *ChannelCommonPkInfo `protobuf:"bytes,1,req,name=info" json:"info,omitempty"`
}

func (m *SetChannelCommonPkInfoReq) Reset()         { *m = SetChannelCommonPkInfoReq{} }
func (m *SetChannelCommonPkInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetChannelCommonPkInfoReq) ProtoMessage()    {}
func (*SetChannelCommonPkInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{2}
}

func (m *SetChannelCommonPkInfoReq) GetInfo() *ChannelCommonPkInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type SetChannelCommonPkInfoResp struct {
	Code uint32 `protobuf:"varint,1,req,name=code" json:"code"`
}

func (m *SetChannelCommonPkInfoResp) Reset()         { *m = SetChannelCommonPkInfoResp{} }
func (m *SetChannelCommonPkInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetChannelCommonPkInfoResp) ProtoMessage()    {}
func (*SetChannelCommonPkInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{3}
}

func (m *SetChannelCommonPkInfoResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type GetUserLeftVoteCntReq struct {
	Uid            uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId      uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	StartTimestamp uint32 `protobuf:"varint,3,req,name=start_timestamp,json=startTimestamp" json:"start_timestamp"`
}

func (m *GetUserLeftVoteCntReq) Reset()         { *m = GetUserLeftVoteCntReq{} }
func (m *GetUserLeftVoteCntReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLeftVoteCntReq) ProtoMessage()    {}
func (*GetUserLeftVoteCntReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{4}
}

func (m *GetUserLeftVoteCntReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLeftVoteCntReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserLeftVoteCntReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

type GetUserLeftVoteCntResp struct {
	LeftVoteCnt uint32 `protobuf:"varint,1,req,name=left_vote_cnt,json=leftVoteCnt" json:"left_vote_cnt"`
}

func (m *GetUserLeftVoteCntResp) Reset()         { *m = GetUserLeftVoteCntResp{} }
func (m *GetUserLeftVoteCntResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLeftVoteCntResp) ProtoMessage()    {}
func (*GetUserLeftVoteCntResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{5}
}

func (m *GetUserLeftVoteCntResp) GetLeftVoteCnt() uint32 {
	if m != nil {
		return m.LeftVoteCnt
	}
	return 0
}

type ChannelCommonPkVoteReq struct {
	FromUid        uint32 `protobuf:"varint,1,req,name=from_uid,json=fromUid" json:"from_uid"`
	ChannelId      uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
	StartTimestamp uint32 `protobuf:"varint,3,req,name=start_timestamp,json=startTimestamp" json:"start_timestamp"`
	ToUid          uint32 `protobuf:"varint,4,req,name=to_uid,json=toUid" json:"to_uid"`
}

func (m *ChannelCommonPkVoteReq) Reset()         { *m = ChannelCommonPkVoteReq{} }
func (m *ChannelCommonPkVoteReq) String() string { return proto.CompactTextString(m) }
func (*ChannelCommonPkVoteReq) ProtoMessage()    {}
func (*ChannelCommonPkVoteReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{6}
}

func (m *ChannelCommonPkVoteReq) GetFromUid() uint32 {
	if m != nil {
		return m.FromUid
	}
	return 0
}

func (m *ChannelCommonPkVoteReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *ChannelCommonPkVoteReq) GetStartTimestamp() uint32 {
	if m != nil {
		return m.StartTimestamp
	}
	return 0
}

func (m *ChannelCommonPkVoteReq) GetToUid() uint32 {
	if m != nil {
		return m.ToUid
	}
	return 0
}

type ChannelCommonPkVoteResp struct {
	Code uint32 `protobuf:"varint,1,req,name=code" json:"code"`
}

func (m *ChannelCommonPkVoteResp) Reset()         { *m = ChannelCommonPkVoteResp{} }
func (m *ChannelCommonPkVoteResp) String() string { return proto.CompactTextString(m) }
func (*ChannelCommonPkVoteResp) ProtoMessage()    {}
func (*ChannelCommonPkVoteResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChannelcommonvotepk, []int{7}
}

func (m *ChannelCommonPkVoteResp) GetCode() uint32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func init() {
	proto.RegisterType((*ChannelCommonPkInfo)(nil), "channelcommonvotepk.ChannelCommonPkInfo")
	proto.RegisterType((*ChannelCommonPkCompetitor)(nil), "channelcommonvotepk.ChannelCommonPkCompetitor")
	proto.RegisterType((*SetChannelCommonPkInfoReq)(nil), "channelcommonvotepk.SetChannelCommonPkInfoReq")
	proto.RegisterType((*SetChannelCommonPkInfoResp)(nil), "channelcommonvotepk.SetChannelCommonPkInfoResp")
	proto.RegisterType((*GetUserLeftVoteCntReq)(nil), "channelcommonvotepk.GetUserLeftVoteCntReq")
	proto.RegisterType((*GetUserLeftVoteCntResp)(nil), "channelcommonvotepk.GetUserLeftVoteCntResp")
	proto.RegisterType((*ChannelCommonPkVoteReq)(nil), "channelcommonvotepk.ChannelCommonPkVoteReq")
	proto.RegisterType((*ChannelCommonPkVoteResp)(nil), "channelcommonvotepk.ChannelCommonPkVoteResp")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for Channelcommonvotepk service

type ChannelcommonvotepkClient interface {
	// 设置人气投票的相关信息
	SetChannelCommonPkInfo(ctx context.Context, in *SetChannelCommonPkInfoReq, opts ...grpc.CallOption) (*SetChannelCommonPkInfoResp, error)
	// 获得观众的剩余票数
	GetUserLeftVoteCnt(ctx context.Context, in *GetUserLeftVoteCntReq, opts ...grpc.CallOption) (*GetUserLeftVoteCntResp, error)
	// 投票
	ChannelCommonPkVote(ctx context.Context, in *ChannelCommonPkVoteReq, opts ...grpc.CallOption) (*ChannelCommonPkVoteResp, error)
}

type channelcommonvotepkClient struct {
	cc *grpc.ClientConn
}

func NewChannelcommonvotepkClient(cc *grpc.ClientConn) ChannelcommonvotepkClient {
	return &channelcommonvotepkClient{cc}
}

func (c *channelcommonvotepkClient) SetChannelCommonPkInfo(ctx context.Context, in *SetChannelCommonPkInfoReq, opts ...grpc.CallOption) (*SetChannelCommonPkInfoResp, error) {
	out := new(SetChannelCommonPkInfoResp)
	err := grpc.Invoke(ctx, "/channelcommonvotepk.channelcommonvotepk/SetChannelCommonPkInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelcommonvotepkClient) GetUserLeftVoteCnt(ctx context.Context, in *GetUserLeftVoteCntReq, opts ...grpc.CallOption) (*GetUserLeftVoteCntResp, error) {
	out := new(GetUserLeftVoteCntResp)
	err := grpc.Invoke(ctx, "/channelcommonvotepk.channelcommonvotepk/GetUserLeftVoteCnt", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelcommonvotepkClient) ChannelCommonPkVote(ctx context.Context, in *ChannelCommonPkVoteReq, opts ...grpc.CallOption) (*ChannelCommonPkVoteResp, error) {
	out := new(ChannelCommonPkVoteResp)
	err := grpc.Invoke(ctx, "/channelcommonvotepk.channelcommonvotepk/ChannelCommonPkVote", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for Channelcommonvotepk service

type ChannelcommonvotepkServer interface {
	// 设置人气投票的相关信息
	SetChannelCommonPkInfo(context.Context, *SetChannelCommonPkInfoReq) (*SetChannelCommonPkInfoResp, error)
	// 获得观众的剩余票数
	GetUserLeftVoteCnt(context.Context, *GetUserLeftVoteCntReq) (*GetUserLeftVoteCntResp, error)
	// 投票
	ChannelCommonPkVote(context.Context, *ChannelCommonPkVoteReq) (*ChannelCommonPkVoteResp, error)
}

func RegisterChannelcommonvotepkServer(s *grpc.Server, srv ChannelcommonvotepkServer) {
	s.RegisterService(&_Channelcommonvotepk_serviceDesc, srv)
}

func _Channelcommonvotepk_SetChannelCommonPkInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetChannelCommonPkInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelcommonvotepkServer).SetChannelCommonPkInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelcommonvotepk.channelcommonvotepk/SetChannelCommonPkInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelcommonvotepkServer).SetChannelCommonPkInfo(ctx, req.(*SetChannelCommonPkInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channelcommonvotepk_GetUserLeftVoteCnt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLeftVoteCntReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelcommonvotepkServer).GetUserLeftVoteCnt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelcommonvotepk.channelcommonvotepk/GetUserLeftVoteCnt",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelcommonvotepkServer).GetUserLeftVoteCnt(ctx, req.(*GetUserLeftVoteCntReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Channelcommonvotepk_ChannelCommonPkVote_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelCommonPkVoteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelcommonvotepkServer).ChannelCommonPkVote(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channelcommonvotepk.channelcommonvotepk/ChannelCommonPkVote",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelcommonvotepkServer).ChannelCommonPkVote(ctx, req.(*ChannelCommonPkVoteReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _Channelcommonvotepk_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channelcommonvotepk.channelcommonvotepk",
	HandlerType: (*ChannelcommonvotepkServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetChannelCommonPkInfo",
			Handler:    _Channelcommonvotepk_SetChannelCommonPkInfo_Handler,
		},
		{
			MethodName: "GetUserLeftVoteCnt",
			Handler:    _Channelcommonvotepk_GetUserLeftVoteCnt_Handler,
		},
		{
			MethodName: "ChannelCommonPkVote",
			Handler:    _Channelcommonvotepk_ChannelCommonPkVote_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelvotepkcommonsvr/channelcommonvotepk.proto",
}

func (m *ChannelCommonPkInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelCommonPkInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.StartTimestamp))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.DurationMin))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.VoteCntLimit))
	return i, nil
}

func (m *ChannelCommonPkCompetitor) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelCommonPkCompetitor) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.StartTimestamp))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *SetChannelCommonPkInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelCommonPkInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Info == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("info")
	} else {
		dAtA[i] = 0xa
		i++
		i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.Info.Size()))
		n1, err := m.Info.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *SetChannelCommonPkInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetChannelCommonPkInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.Code))
	return i, nil
}

func (m *GetUserLeftVoteCntReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLeftVoteCntReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.StartTimestamp))
	return i, nil
}

func (m *GetUserLeftVoteCntResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLeftVoteCntResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.LeftVoteCnt))
	return i, nil
}

func (m *ChannelCommonPkVoteReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelCommonPkVoteReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.FromUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.StartTimestamp))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.ToUid))
	return i, nil
}

func (m *ChannelCommonPkVoteResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelCommonPkVoteResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChannelcommonvotepk(dAtA, i, uint64(m.Code))
	return i, nil
}

func encodeFixed64Channelcommonvotepk(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channelcommonvotepk(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChannelcommonvotepk(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ChannelCommonPkInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.ChannelId))
	n += 1 + sovChannelcommonvotepk(uint64(m.StartTimestamp))
	n += 1 + sovChannelcommonvotepk(uint64(m.DurationMin))
	n += 1 + sovChannelcommonvotepk(uint64(m.VoteCntLimit))
	return n
}

func (m *ChannelCommonPkCompetitor) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.ChannelId))
	n += 1 + sovChannelcommonvotepk(uint64(m.StartTimestamp))
	n += 1 + sovChannelcommonvotepk(uint64(m.Uid))
	return n
}

func (m *SetChannelCommonPkInfoReq) Size() (n int) {
	var l int
	_ = l
	if m.Info != nil {
		l = m.Info.Size()
		n += 1 + l + sovChannelcommonvotepk(uint64(l))
	}
	return n
}

func (m *SetChannelCommonPkInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.Code))
	return n
}

func (m *GetUserLeftVoteCntReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.Uid))
	n += 1 + sovChannelcommonvotepk(uint64(m.ChannelId))
	n += 1 + sovChannelcommonvotepk(uint64(m.StartTimestamp))
	return n
}

func (m *GetUserLeftVoteCntResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.LeftVoteCnt))
	return n
}

func (m *ChannelCommonPkVoteReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.FromUid))
	n += 1 + sovChannelcommonvotepk(uint64(m.ChannelId))
	n += 1 + sovChannelcommonvotepk(uint64(m.StartTimestamp))
	n += 1 + sovChannelcommonvotepk(uint64(m.ToUid))
	return n
}

func (m *ChannelCommonPkVoteResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChannelcommonvotepk(uint64(m.Code))
	return n
}

func sovChannelcommonvotepk(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChannelcommonvotepk(x uint64) (n int) {
	return sovChannelcommonvotepk(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ChannelCommonPkInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelCommonPkInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelCommonPkInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DurationMin", wireType)
			}
			m.DurationMin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.DurationMin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VoteCntLimit", wireType)
			}
			m.VoteCntLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VoteCntLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_timestamp")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("duration_min")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("vote_cnt_limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelCommonPkCompetitor) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelCommonPkCompetitor: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelCommonPkCompetitor: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_timestamp")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelCommonPkInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelCommonPkInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelCommonPkInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Info == nil {
				m.Info = &ChannelCommonPkInfo{}
			}
			if err := m.Info.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("info")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetChannelCommonPkInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetChannelCommonPkInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetChannelCommonPkInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLeftVoteCntReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLeftVoteCntReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLeftVoteCntReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_timestamp")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLeftVoteCntResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLeftVoteCntResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLeftVoteCntResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LeftVoteCnt", wireType)
			}
			m.LeftVoteCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LeftVoteCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("left_vote_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelCommonPkVoteReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelCommonPkVoteReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelCommonPkVoteReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromUid", wireType)
			}
			m.FromUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartTimestamp", wireType)
			}
			m.StartTimestamp = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartTimestamp |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToUid", wireType)
			}
			m.ToUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_timestamp")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelCommonPkVoteResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ChannelCommonPkVoteResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ChannelCommonPkVoteResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Code", wireType)
			}
			m.Code = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Code |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChannelcommonvotepk(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChannelcommonvotepk
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("code")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChannelcommonvotepk(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChannelcommonvotepk
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChannelcommonvotepk
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChannelcommonvotepk
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChannelcommonvotepk
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChannelcommonvotepk(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChannelcommonvotepk = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChannelcommonvotepk   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channelvotepkcommonsvr/channelcommonvotepk.proto", fileDescriptorChannelcommonvotepk)
}

var fileDescriptorChannelcommonvotepk = []byte{
	// 619 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x94, 0x4d, 0x6b, 0xd4, 0x40,
	0x18, 0xc7, 0x3b, 0x9b, 0x6d, 0xad, 0x4f, 0x5f, 0x94, 0x29, 0xae, 0xdb, 0x08, 0xdb, 0x21, 0x0a,
	0x96, 0xd6, 0x74, 0xa1, 0x8a, 0x87, 0x25, 0x44, 0xd8, 0x3d, 0x48, 0xa1, 0x82, 0xb4, 0xb6, 0xe0,
	0x29, 0xac, 0xc9, 0x2c, 0x1d, 0x36, 0xc9, 0xc4, 0xcc, 0xa4, 0xa0, 0x27, 0x41, 0x50, 0xf1, 0x24,
	0x7e, 0x86, 0x3d, 0xe9, 0xc9, 0x2f, 0xe0, 0xb9, 0x47, 0x3f, 0x81, 0x48, 0x3d, 0xb8, 0x1f, 0x43,
	0x26, 0xd9, 0xa5, 0xbb, 0x69, 0x2a, 0xf1, 0xd0, 0xe3, 0x3e, 0xcf, 0xff, 0xc9, 0xfc, 0x7f, 0xcf,
	0xcb, 0xc2, 0x03, 0x11, 0xbb, 0x4d, 0xf7, 0xa8, 0x1b, 0x86, 0xd4, 0x3f, 0xe6, 0x92, 0x46, 0x7d,
	0x97, 0x07, 0x01, 0x0f, 0xc5, 0x71, 0x3c, 0x0e, 0x67, 0x81, 0x2c, 0xb9, 0x15, 0xc5, 0x5c, 0x72,
	0xbc, 0x52, 0x90, 0xd2, 0xef, 0x64, 0xbf, 0x9a, 0xd2, 0x3f, 0x8e, 0x98, 0xdb, 0xf7, 0x69, 0x53,
	0xf4, 0x5f, 0x24, 0xcc, 0x97, 0x2c, 0x94, 0xaf, 0x22, 0x9a, 0x95, 0x1a, 0xdf, 0x11, 0xac, 0x74,
	0xb2, 0xea, 0x4e, 0xaa, 0x7f, 0xda, 0xdf, 0x09, 0x7b, 0x1c, 0xdf, 0x06, 0x18, 0x7d, 0xd4, 0x61,
	0x5e, 0x1d, 0x91, 0xca, 0xfa, 0x52, 0xbb, 0x7a, 0xf2, 0x73, 0x6d, 0x66, 0xef, 0xea, 0x28, 0xbe,
	0xe3, 0x61, 0x13, 0xae, 0x09, 0xd9, 0x8d, 0xa5, 0x23, 0x59, 0x40, 0x85, 0xec, 0x06, 0x51, 0xbd,
	0x32, 0xa1, 0x5c, 0x4e, 0x93, 0xcf, 0xc6, 0x39, 0x7c, 0x17, 0x16, 0xbd, 0x24, 0xee, 0x4a, 0xc6,
	0x43, 0x27, 0x60, 0x61, 0x5d, 0x9b, 0xd0, 0x2e, 0x8c, 0x33, 0x4f, 0x58, 0x88, 0x37, 0x60, 0x59,
	0x41, 0x38, 0x6e, 0x28, 0x1d, 0x9f, 0x05, 0x4c, 0xd6, 0xab, 0x13, 0xd2, 0x45, 0x95, 0xeb, 0x84,
	0x72, 0x57, 0x65, 0x8c, 0xf7, 0x08, 0x56, 0x73, 0x00, 0x1d, 0x1e, 0x44, 0x54, 0x32, 0xc9, 0xe3,
	0x4b, 0xc1, 0xa8, 0x81, 0x96, 0x30, 0x6f, 0xca, 0xbd, 0x0a, 0x18, 0xcf, 0x61, 0x75, 0x9f, 0xca,
	0x82, 0x66, 0xee, 0xd1, 0x97, 0xd8, 0x82, 0x2a, 0x0b, 0x7b, 0x3c, 0xb5, 0xb0, 0xb0, 0xbd, 0xbe,
	0x55, 0x34, 0xcc, 0xa2, 0xd2, 0xb4, 0xca, 0x78, 0x08, 0xfa, 0x45, 0x9f, 0x16, 0x11, 0xae, 0x43,
	0xd5, 0xe5, 0x1e, 0x9d, 0xc2, 0x4b, 0x23, 0xc6, 0x5b, 0x04, 0x37, 0x1e, 0x53, 0x79, 0x20, 0x68,
	0xbc, 0x4b, 0x7b, 0xf2, 0x30, 0x6b, 0x9c, 0xf2, 0x33, 0x82, 0x40, 0x39, 0x88, 0x5c, 0xc3, 0x2a,
	0xa5, 0x1b, 0xa6, 0x5d, 0xdc, 0x30, 0xa3, 0x0d, 0xb5, 0x22, 0x13, 0x22, 0xc2, 0xeb, 0xb0, 0xe4,
	0xd3, 0x9e, 0x74, 0xc6, 0xd3, 0x9e, 0xf2, 0xb3, 0xe0, 0x9f, 0xa9, 0x8d, 0x2f, 0x08, 0x6a, 0x39,
	0x7e, 0x95, 0x52, 0x28, 0x6b, 0x30, 0xdf, 0x8b, 0x79, 0xe0, 0xe4, 0x79, 0xae, 0xa8, 0xe8, 0xc1,
	0xe5, 0x30, 0xe1, 0x5b, 0x30, 0x27, 0x79, 0xfa, 0xe4, 0xe4, 0x6a, 0xce, 0x4a, 0x7e, 0xc0, 0x3c,
	0xe3, 0x3e, 0xdc, 0x2c, 0xf4, 0xfa, 0xaf, 0x59, 0x6d, 0xbf, 0x9b, 0x85, 0xa2, 0x3b, 0xc6, 0x7f,
	0x10, 0xd4, 0x8a, 0x87, 0x8f, 0xb7, 0x0a, 0xd7, 0xe8, 0xc2, 0x25, 0xd4, 0x9b, 0xff, 0xa5, 0x17,
	0x91, 0xe1, 0xbf, 0x19, 0x0c, 0x35, 0xf4, 0x71, 0x30, 0xd4, 0x20, 0x69, 0x1d, 0xb5, 0x44, 0xcb,
	0x6b, 0xf9, 0xad, 0xcf, 0x83, 0xa1, 0xb6, 0x6f, 0x26, 0xc4, 0x4a, 0x98, 0x67, 0x13, 0xf3, 0x88,
	0x58, 0x67, 0xdd, 0xb5, 0x89, 0x29, 0x88, 0x95, 0x6b, 0xa4, 0x4d, 0x4c, 0x8f, 0x58, 0x93, 0xa7,
	0x6f, 0x13, 0xd3, 0x27, 0xd6, 0xf4, 0x8d, 0xdb, 0xf8, 0x1b, 0x02, 0x7c, 0x7e, 0x51, 0xf0, 0x46,
	0xa1, 0xeb, 0xc2, 0xb5, 0xd6, 0x37, 0x4b, 0x6b, 0x45, 0x64, 0xb4, 0x15, 0x5d, 0x45, 0xd1, 0xcd,
	0x29, 0xb6, 0x24, 0x25, 0x33, 0xcb, 0x01, 0x8d, 0xf0, 0xf1, 0xc9, 0xf9, 0xff, 0x4f, 0xf5, 0x04,
	0xde, 0x2c, 0x73, 0xe1, 0xa3, 0x0d, 0xd6, 0xef, 0x95, 0x17, 0x8b, 0xc8, 0x38, 0x54, 0xb6, 0x35,
	0x65, 0x7b, 0x3e, 0x1b, 0x8a, 0x4c, 0x8d, 0x3f, 0x52, 0x9e, 0xc6, 0x87, 0x50, 0x76, 0x2e, 0x92,
	0x58, 0xd9, 0x1a, 0xdb, 0x44, 0x9f, 0xfb, 0x30, 0x18, 0x6a, 0x5f, 0x5f, 0xb7, 0xaf, 0x9f, 0x9c,
	0x36, 0xd0, 0x8f, 0xd3, 0x06, 0xfa, 0x75, 0xda, 0x40, 0x9f, 0x7e, 0x37, 0x66, 0xfe, 0x06, 0x00,
	0x00, 0xff, 0xff, 0xf8, 0xb5, 0x3c, 0x8b, 0x96, 0x06, 0x00, 0x00,
}
