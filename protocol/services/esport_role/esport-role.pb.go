// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/esport-role/esport-role.proto

package esport_role // import "golang.52tt.com/protocol/services/esport_role"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"
import esport_skill "golang.52tt.com/protocol/services/esport-skill"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 电竞指导身份类型
type ESportErType int32

const (
	ESportErType_ESPORT_TYPE_UNSPECIFIED ESportErType = 0
	ESportErType_ESPORT_TYPE_PERSONAL    ESportErType = 1
	ESportErType_ESPORT_TYPE_GUILD       ESportErType = 2
)

var ESportErType_name = map[int32]string{
	0: "ESPORT_TYPE_UNSPECIFIED",
	1: "ESPORT_TYPE_PERSONAL",
	2: "ESPORT_TYPE_GUILD",
}
var ESportErType_value = map[string]int32{
	"ESPORT_TYPE_UNSPECIFIED": 0,
	"ESPORT_TYPE_PERSONAL":    1,
	"ESPORT_TYPE_GUILD":       2,
}

func (x ESportErType) String() string {
	return proto.EnumName(ESportErType_name, int32(x))
}
func (ESportErType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{0}
}

type ApplyESportAuditType int32

const (
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_INVALID            ApplyESportAuditType = 0
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_INIT               ApplyESportAuditType = 1
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_RISK_REJECT        ApplyESportAuditType = 2
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_RISK_PASS          ApplyESportAuditType = 3
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_WAIT_FOR_GUILD     ApplyESportAuditType = 4
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_GUILD_PASS         ApplyESportAuditType = 5
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_GUILD_REJECT       ApplyESportAuditType = 6
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_WAIT_FOR_PLATFORM  ApplyESportAuditType = 7
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_PASS               ApplyESportAuditType = 8
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_REJECT             ApplyESportAuditType = 9
	ApplyESportAuditType_ESPORT_AUDIT_TYPE_GUILD_APPLY_CANCEL ApplyESportAuditType = 10
)

var ApplyESportAuditType_name = map[int32]string{
	0:  "ESPORT_AUDIT_TYPE_INVALID",
	1:  "ESPORT_AUDIT_TYPE_INIT",
	2:  "ESPORT_AUDIT_TYPE_RISK_REJECT",
	3:  "ESPORT_AUDIT_TYPE_RISK_PASS",
	4:  "ESPORT_AUDIT_TYPE_WAIT_FOR_GUILD",
	5:  "ESPORT_AUDIT_TYPE_GUILD_PASS",
	6:  "ESPORT_AUDIT_TYPE_GUILD_REJECT",
	7:  "ESPORT_AUDIT_TYPE_WAIT_FOR_PLATFORM",
	8:  "ESPORT_AUDIT_TYPE_PASS",
	9:  "ESPORT_AUDIT_TYPE_REJECT",
	10: "ESPORT_AUDIT_TYPE_GUILD_APPLY_CANCEL",
}
var ApplyESportAuditType_value = map[string]int32{
	"ESPORT_AUDIT_TYPE_INVALID":            0,
	"ESPORT_AUDIT_TYPE_INIT":               1,
	"ESPORT_AUDIT_TYPE_RISK_REJECT":        2,
	"ESPORT_AUDIT_TYPE_RISK_PASS":          3,
	"ESPORT_AUDIT_TYPE_WAIT_FOR_GUILD":     4,
	"ESPORT_AUDIT_TYPE_GUILD_PASS":         5,
	"ESPORT_AUDIT_TYPE_GUILD_REJECT":       6,
	"ESPORT_AUDIT_TYPE_WAIT_FOR_PLATFORM":  7,
	"ESPORT_AUDIT_TYPE_PASS":               8,
	"ESPORT_AUDIT_TYPE_REJECT":             9,
	"ESPORT_AUDIT_TYPE_GUILD_APPLY_CANCEL": 10,
}

func (x ApplyESportAuditType) String() string {
	return proto.EnumName(ApplyESportAuditType_name, int32(x))
}
func (ApplyESportAuditType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{1}
}

type QueryType int32

const (
	QueryType_QUERY_BY_UID     QueryType = 0
	QueryType_QUERE_BY_GUILDID QueryType = 1
	QueryType_QUERY_BY_ALL     QueryType = 2
)

var QueryType_name = map[int32]string{
	0: "QUERY_BY_UID",
	1: "QUERE_BY_GUILDID",
	2: "QUERY_BY_ALL",
}
var QueryType_value = map[string]int32{
	"QUERY_BY_UID":     0,
	"QUERE_BY_GUILDID": 1,
	"QUERY_BY_ALL":     2,
}

func (x QueryType) String() string {
	return proto.EnumName(QueryType_name, int32(x))
}
func (QueryType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{2}
}

// 资源类型
type LabelSourceType int32

const (
	LabelSourceType_LABEL_SOURCE_TYPE_UNSPECIFIED LabelSourceType = 0
	LabelSourceType_LABEL_SOURCE_TYPE_PNG         LabelSourceType = 1
	LabelSourceType_LABEL_SOURCE_TYPE_WEBP        LabelSourceType = 2
)

var LabelSourceType_name = map[int32]string{
	0: "LABEL_SOURCE_TYPE_UNSPECIFIED",
	1: "LABEL_SOURCE_TYPE_PNG",
	2: "LABEL_SOURCE_TYPE_WEBP",
}
var LabelSourceType_value = map[string]int32{
	"LABEL_SOURCE_TYPE_UNSPECIFIED": 0,
	"LABEL_SOURCE_TYPE_PNG":         1,
	"LABEL_SOURCE_TYPE_WEBP":        2,
}

func (x LabelSourceType) String() string {
	return proto.EnumName(LabelSourceType_name, int32(x))
}
func (LabelSourceType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{3}
}

// 展示状态
type CoachLabelStatus int32

const (
	CoachLabelStatus_COACH_LABEL_STATUS_UNSPECIFIED CoachLabelStatus = 0
	CoachLabelStatus_COACH_LABEL_STATUS_ALL         CoachLabelStatus = 1
	CoachLabelStatus_COACH_LABEL_STATUS_WAITING     CoachLabelStatus = 2
	CoachLabelStatus_COACH_LABEL_STATUS_SHOWING     CoachLabelStatus = 3
	CoachLabelStatus_COACH_LABEL_STATUS_EXPIRED     CoachLabelStatus = 4
)

var CoachLabelStatus_name = map[int32]string{
	0: "COACH_LABEL_STATUS_UNSPECIFIED",
	1: "COACH_LABEL_STATUS_ALL",
	2: "COACH_LABEL_STATUS_WAITING",
	3: "COACH_LABEL_STATUS_SHOWING",
	4: "COACH_LABEL_STATUS_EXPIRED",
}
var CoachLabelStatus_value = map[string]int32{
	"COACH_LABEL_STATUS_UNSPECIFIED": 0,
	"COACH_LABEL_STATUS_ALL":         1,
	"COACH_LABEL_STATUS_WAITING":     2,
	"COACH_LABEL_STATUS_SHOWING":     3,
	"COACH_LABEL_STATUS_EXPIRED":     4,
}

func (x CoachLabelStatus) String() string {
	return proto.EnumName(CoachLabelStatus_name, int32(x))
}
func (CoachLabelStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{4}
}

type ApplyESportRequset_ESportApplyType int32

const (
	ApplyESportRequset_ESPORT_APPLY_TYPE_INVALID           ApplyESportRequset_ESportApplyType = 0
	ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL          ApplyESportRequset_ESportApplyType = 1
	ApplyESportRequset_ESPORT_APPLY_TYPE_GUILD             ApplyESportRequset_ESportApplyType = 2
	ApplyESportRequset_ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD ApplyESportRequset_ESportApplyType = 3
)

var ApplyESportRequset_ESportApplyType_name = map[int32]string{
	0: "ESPORT_APPLY_TYPE_INVALID",
	1: "ESPORT_APPLY_TYPE_PERSONAL",
	2: "ESPORT_APPLY_TYPE_GUILD",
	3: "ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD",
}
var ApplyESportRequset_ESportApplyType_value = map[string]int32{
	"ESPORT_APPLY_TYPE_INVALID":           0,
	"ESPORT_APPLY_TYPE_PERSONAL":          1,
	"ESPORT_APPLY_TYPE_GUILD":             2,
	"ESPORT_APPLY_TYPE_PERSONAL_TO_GUILD": 3,
}

func (x ApplyESportRequset_ESportApplyType) String() string {
	return proto.EnumName(ApplyESportRequset_ESportApplyType_name, int32(x))
}
func (ApplyESportRequset_ESportApplyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{4, 0}
}

type UpdateApplyBlacklistFreezeTimeReq_OpType int32

const (
	UpdateApplyBlacklistFreezeTimeReq_OP_TYPE_INVAILD       UpdateApplyBlacklistFreezeTimeReq_OpType = 0
	UpdateApplyBlacklistFreezeTimeReq_OP_TYPE_SET_PERMANENT UpdateApplyBlacklistFreezeTimeReq_OpType = 1
	UpdateApplyBlacklistFreezeTimeReq_OP_TYPE_SET_UNFORZEN  UpdateApplyBlacklistFreezeTimeReq_OpType = 2
	UpdateApplyBlacklistFreezeTimeReq_OP_TYPE_SET_TIME      UpdateApplyBlacklistFreezeTimeReq_OpType = 3
)

var UpdateApplyBlacklistFreezeTimeReq_OpType_name = map[int32]string{
	0: "OP_TYPE_INVAILD",
	1: "OP_TYPE_SET_PERMANENT",
	2: "OP_TYPE_SET_UNFORZEN",
	3: "OP_TYPE_SET_TIME",
}
var UpdateApplyBlacklistFreezeTimeReq_OpType_value = map[string]int32{
	"OP_TYPE_INVAILD":       0,
	"OP_TYPE_SET_PERMANENT": 1,
	"OP_TYPE_SET_UNFORZEN":  2,
	"OP_TYPE_SET_TIME":      3,
}

func (x UpdateApplyBlacklistFreezeTimeReq_OpType) String() string {
	return proto.EnumName(UpdateApplyBlacklistFreezeTimeReq_OpType_name, int32(x))
}
func (UpdateApplyBlacklistFreezeTimeReq_OpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{21, 0}
}

// 工会审核结果通知回调
type ESportGuildAuditResultReq struct {
	AuditToken           string   `protobuf:"bytes,1,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AuditType            uint32   `protobuf:"varint,4,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	Reason               string   `protobuf:"bytes,5,opt,name=reason,proto3" json:"reason,omitempty"`
	SignTime             uint32   `protobuf:"varint,6,opt,name=sign_time,json=signTime,proto3" json:"sign_time,omitempty"`
	SignExpireTime       uint32   `protobuf:"varint,7,opt,name=sign_expire_time,json=signExpireTime,proto3" json:"sign_expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportGuildAuditResultReq) Reset()         { *m = ESportGuildAuditResultReq{} }
func (m *ESportGuildAuditResultReq) String() string { return proto.CompactTextString(m) }
func (*ESportGuildAuditResultReq) ProtoMessage()    {}
func (*ESportGuildAuditResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{0}
}
func (m *ESportGuildAuditResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportGuildAuditResultReq.Unmarshal(m, b)
}
func (m *ESportGuildAuditResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportGuildAuditResultReq.Marshal(b, m, deterministic)
}
func (dst *ESportGuildAuditResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportGuildAuditResultReq.Merge(dst, src)
}
func (m *ESportGuildAuditResultReq) XXX_Size() int {
	return xxx_messageInfo_ESportGuildAuditResultReq.Size(m)
}
func (m *ESportGuildAuditResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportGuildAuditResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_ESportGuildAuditResultReq proto.InternalMessageInfo

func (m *ESportGuildAuditResultReq) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *ESportGuildAuditResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ESportGuildAuditResultReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ESportGuildAuditResultReq) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *ESportGuildAuditResultReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ESportGuildAuditResultReq) GetSignTime() uint32 {
	if m != nil {
		return m.SignTime
	}
	return 0
}

func (m *ESportGuildAuditResultReq) GetSignExpireTime() uint32 {
	if m != nil {
		return m.SignExpireTime
	}
	return 0
}

type ESportGuildAuditResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportGuildAuditResultResp) Reset()         { *m = ESportGuildAuditResultResp{} }
func (m *ESportGuildAuditResultResp) String() string { return proto.CompactTextString(m) }
func (*ESportGuildAuditResultResp) ProtoMessage()    {}
func (*ESportGuildAuditResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{1}
}
func (m *ESportGuildAuditResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportGuildAuditResultResp.Unmarshal(m, b)
}
func (m *ESportGuildAuditResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportGuildAuditResultResp.Marshal(b, m, deterministic)
}
func (dst *ESportGuildAuditResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportGuildAuditResultResp.Merge(dst, src)
}
func (m *ESportGuildAuditResultResp) XXX_Size() int {
	return xxx_messageInfo_ESportGuildAuditResultResp.Size(m)
}
func (m *ESportGuildAuditResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportGuildAuditResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_ESportGuildAuditResultResp proto.InternalMessageInfo

// T盾风控结果通知
type ESportRiskAuditResultReq struct {
	AuditToken           string   `protobuf:"bytes,1,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	AuditType            uint32   `protobuf:"varint,3,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	Reason               string   `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`
	SceneCode            string   `protobuf:"bytes,5,opt,name=scene_code,json=sceneCode,proto3" json:"scene_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportRiskAuditResultReq) Reset()         { *m = ESportRiskAuditResultReq{} }
func (m *ESportRiskAuditResultReq) String() string { return proto.CompactTextString(m) }
func (*ESportRiskAuditResultReq) ProtoMessage()    {}
func (*ESportRiskAuditResultReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{2}
}
func (m *ESportRiskAuditResultReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportRiskAuditResultReq.Unmarshal(m, b)
}
func (m *ESportRiskAuditResultReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportRiskAuditResultReq.Marshal(b, m, deterministic)
}
func (dst *ESportRiskAuditResultReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportRiskAuditResultReq.Merge(dst, src)
}
func (m *ESportRiskAuditResultReq) XXX_Size() int {
	return xxx_messageInfo_ESportRiskAuditResultReq.Size(m)
}
func (m *ESportRiskAuditResultReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportRiskAuditResultReq.DiscardUnknown(m)
}

var xxx_messageInfo_ESportRiskAuditResultReq proto.InternalMessageInfo

func (m *ESportRiskAuditResultReq) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *ESportRiskAuditResultReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ESportRiskAuditResultReq) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *ESportRiskAuditResultReq) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ESportRiskAuditResultReq) GetSceneCode() string {
	if m != nil {
		return m.SceneCode
	}
	return ""
}

type ESportRiskAuditResultResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportRiskAuditResultResp) Reset()         { *m = ESportRiskAuditResultResp{} }
func (m *ESportRiskAuditResultResp) String() string { return proto.CompactTextString(m) }
func (*ESportRiskAuditResultResp) ProtoMessage()    {}
func (*ESportRiskAuditResultResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{3}
}
func (m *ESportRiskAuditResultResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportRiskAuditResultResp.Unmarshal(m, b)
}
func (m *ESportRiskAuditResultResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportRiskAuditResultResp.Marshal(b, m, deterministic)
}
func (dst *ESportRiskAuditResultResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportRiskAuditResultResp.Merge(dst, src)
}
func (m *ESportRiskAuditResultResp) XXX_Size() int {
	return xxx_messageInfo_ESportRiskAuditResultResp.Size(m)
}
func (m *ESportRiskAuditResultResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportRiskAuditResultResp.DiscardUnknown(m)
}

var xxx_messageInfo_ESportRiskAuditResultResp proto.InternalMessageInfo

// 提交审核申请
type ApplyESportRequset struct {
	AuditToken string `protobuf:"bytes,1,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	Uid        uint32 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	CurRole    uint32 `protobuf:"varint,3,opt,name=cur_role,json=curRole,proto3" json:"cur_role,omitempty"`
	ApplyType  uint32 `protobuf:"varint,4,opt,name=apply_type,json=applyType,proto3" json:"apply_type,omitempty"`
	IdentifyId string `protobuf:"bytes,5,opt,name=identify_id,json=identifyId,proto3" json:"identify_id,omitempty"`
	ApplyTime  uint32 `protobuf:"varint,6,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	// 签约工会需要填写相关信息
	GuildId      uint32 `protobuf:"varint,7,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	SignDuration uint32 `protobuf:"varint,8,opt,name=sign_duration,json=signDuration,proto3" json:"sign_duration,omitempty"`
	// 如果用户已签约该工会，使用该时间
	SignTime             uint32   `protobuf:"varint,9,opt,name=sign_time,json=signTime,proto3" json:"sign_time,omitempty"`
	SignExpireTime       uint32   `protobuf:"varint,10,opt,name=sign_expire_time,json=signExpireTime,proto3" json:"sign_expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyESportRequset) Reset()         { *m = ApplyESportRequset{} }
func (m *ApplyESportRequset) String() string { return proto.CompactTextString(m) }
func (*ApplyESportRequset) ProtoMessage()    {}
func (*ApplyESportRequset) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{4}
}
func (m *ApplyESportRequset) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyESportRequset.Unmarshal(m, b)
}
func (m *ApplyESportRequset) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyESportRequset.Marshal(b, m, deterministic)
}
func (dst *ApplyESportRequset) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyESportRequset.Merge(dst, src)
}
func (m *ApplyESportRequset) XXX_Size() int {
	return xxx_messageInfo_ApplyESportRequset.Size(m)
}
func (m *ApplyESportRequset) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyESportRequset.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyESportRequset proto.InternalMessageInfo

func (m *ApplyESportRequset) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *ApplyESportRequset) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyESportRequset) GetCurRole() uint32 {
	if m != nil {
		return m.CurRole
	}
	return 0
}

func (m *ApplyESportRequset) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *ApplyESportRequset) GetIdentifyId() string {
	if m != nil {
		return m.IdentifyId
	}
	return ""
}

func (m *ApplyESportRequset) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *ApplyESportRequset) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyESportRequset) GetSignDuration() uint32 {
	if m != nil {
		return m.SignDuration
	}
	return 0
}

func (m *ApplyESportRequset) GetSignTime() uint32 {
	if m != nil {
		return m.SignTime
	}
	return 0
}

func (m *ApplyESportRequset) GetSignExpireTime() uint32 {
	if m != nil {
		return m.SignExpireTime
	}
	return 0
}

type ApplyESportResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyESportResponse) Reset()         { *m = ApplyESportResponse{} }
func (m *ApplyESportResponse) String() string { return proto.CompactTextString(m) }
func (*ApplyESportResponse) ProtoMessage()    {}
func (*ApplyESportResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{5}
}
func (m *ApplyESportResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyESportResponse.Unmarshal(m, b)
}
func (m *ApplyESportResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyESportResponse.Marshal(b, m, deterministic)
}
func (dst *ApplyESportResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyESportResponse.Merge(dst, src)
}
func (m *ApplyESportResponse) XXX_Size() int {
	return xxx_messageInfo_ApplyESportResponse.Size(m)
}
func (m *ApplyESportResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyESportResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyESportResponse proto.InternalMessageInfo

// 判断用户是否是什么身份
type GetUserESportRoleReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserESportRoleReq) Reset()         { *m = GetUserESportRoleReq{} }
func (m *GetUserESportRoleReq) String() string { return proto.CompactTextString(m) }
func (*GetUserESportRoleReq) ProtoMessage()    {}
func (*GetUserESportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{6}
}
func (m *GetUserESportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserESportRoleReq.Unmarshal(m, b)
}
func (m *GetUserESportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserESportRoleReq.Marshal(b, m, deterministic)
}
func (dst *GetUserESportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserESportRoleReq.Merge(dst, src)
}
func (m *GetUserESportRoleReq) XXX_Size() int {
	return xxx_messageInfo_GetUserESportRoleReq.Size(m)
}
func (m *GetUserESportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserESportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserESportRoleReq proto.InternalMessageInfo

func (m *GetUserESportRoleReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserESportRoleResp struct {
	EsportRole           uint32   `protobuf:"varint,1,opt,name=esport_role,json=esportRole,proto3" json:"esport_role,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserESportRoleResp) Reset()         { *m = GetUserESportRoleResp{} }
func (m *GetUserESportRoleResp) String() string { return proto.CompactTextString(m) }
func (*GetUserESportRoleResp) ProtoMessage()    {}
func (*GetUserESportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{7}
}
func (m *GetUserESportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserESportRoleResp.Unmarshal(m, b)
}
func (m *GetUserESportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserESportRoleResp.Marshal(b, m, deterministic)
}
func (dst *GetUserESportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserESportRoleResp.Merge(dst, src)
}
func (m *GetUserESportRoleResp) XXX_Size() int {
	return xxx_messageInfo_GetUserESportRoleResp.Size(m)
}
func (m *GetUserESportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserESportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserESportRoleResp proto.InternalMessageInfo

func (m *GetUserESportRoleResp) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

func (m *GetUserESportRoleResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

// 批量判断用户电竞指导身份
type BatchGetUserESportRoleReq struct {
	Uid                  []uint32 `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUserESportRoleReq) Reset()         { *m = BatchGetUserESportRoleReq{} }
func (m *BatchGetUserESportRoleReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserESportRoleReq) ProtoMessage()    {}
func (*BatchGetUserESportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{8}
}
func (m *BatchGetUserESportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserESportRoleReq.Unmarshal(m, b)
}
func (m *BatchGetUserESportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserESportRoleReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserESportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserESportRoleReq.Merge(dst, src)
}
func (m *BatchGetUserESportRoleReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserESportRoleReq.Size(m)
}
func (m *BatchGetUserESportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserESportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserESportRoleReq proto.InternalMessageInfo

func (m *BatchGetUserESportRoleReq) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type UserESportRole struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EsportRole           uint32   `protobuf:"varint,2,opt,name=esport_role,json=esportRole,proto3" json:"esport_role,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserESportRole) Reset()         { *m = UserESportRole{} }
func (m *UserESportRole) String() string { return proto.CompactTextString(m) }
func (*UserESportRole) ProtoMessage()    {}
func (*UserESportRole) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{9}
}
func (m *UserESportRole) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserESportRole.Unmarshal(m, b)
}
func (m *UserESportRole) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserESportRole.Marshal(b, m, deterministic)
}
func (dst *UserESportRole) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserESportRole.Merge(dst, src)
}
func (m *UserESportRole) XXX_Size() int {
	return xxx_messageInfo_UserESportRole.Size(m)
}
func (m *UserESportRole) XXX_DiscardUnknown() {
	xxx_messageInfo_UserESportRole.DiscardUnknown(m)
}

var xxx_messageInfo_UserESportRole proto.InternalMessageInfo

func (m *UserESportRole) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserESportRole) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

func (m *UserESportRole) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type BatchGetUserESportRoleResp struct {
	RoleList             []*UserESportRole `protobuf:"bytes,1,rep,name=role_list,json=roleList,proto3" json:"role_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetUserESportRoleResp) Reset()         { *m = BatchGetUserESportRoleResp{} }
func (m *BatchGetUserESportRoleResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUserESportRoleResp) ProtoMessage()    {}
func (*BatchGetUserESportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{10}
}
func (m *BatchGetUserESportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUserESportRoleResp.Unmarshal(m, b)
}
func (m *BatchGetUserESportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUserESportRoleResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUserESportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUserESportRoleResp.Merge(dst, src)
}
func (m *BatchGetUserESportRoleResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUserESportRoleResp.Size(m)
}
func (m *BatchGetUserESportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUserESportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUserESportRoleResp proto.InternalMessageInfo

func (m *BatchGetUserESportRoleResp) GetRoleList() []*UserESportRole {
	if m != nil {
		return m.RoleList
	}
	return nil
}

// 手动新增用户身份 （谨慎使用，仅辅助测试使用）
type ManualAddUserESportReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	EsportRole           uint32   `protobuf:"varint,2,opt,name=esport_role,json=esportRole,proto3" json:"esport_role,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildSignTime        uint32   `protobuf:"varint,4,opt,name=guild_sign_time,json=guildSignTime,proto3" json:"guild_sign_time,omitempty"`
	GuildSignExpireTime  uint32   `protobuf:"varint,5,opt,name=guild_sign_expire_time,json=guildSignExpireTime,proto3" json:"guild_sign_expire_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualAddUserESportReq) Reset()         { *m = ManualAddUserESportReq{} }
func (m *ManualAddUserESportReq) String() string { return proto.CompactTextString(m) }
func (*ManualAddUserESportReq) ProtoMessage()    {}
func (*ManualAddUserESportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{11}
}
func (m *ManualAddUserESportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualAddUserESportReq.Unmarshal(m, b)
}
func (m *ManualAddUserESportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualAddUserESportReq.Marshal(b, m, deterministic)
}
func (dst *ManualAddUserESportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualAddUserESportReq.Merge(dst, src)
}
func (m *ManualAddUserESportReq) XXX_Size() int {
	return xxx_messageInfo_ManualAddUserESportReq.Size(m)
}
func (m *ManualAddUserESportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualAddUserESportReq.DiscardUnknown(m)
}

var xxx_messageInfo_ManualAddUserESportReq proto.InternalMessageInfo

func (m *ManualAddUserESportReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ManualAddUserESportReq) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

func (m *ManualAddUserESportReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ManualAddUserESportReq) GetGuildSignTime() uint32 {
	if m != nil {
		return m.GuildSignTime
	}
	return 0
}

func (m *ManualAddUserESportReq) GetGuildSignExpireTime() uint32 {
	if m != nil {
		return m.GuildSignExpireTime
	}
	return 0
}

type ManualAddUserESportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ManualAddUserESportResp) Reset()         { *m = ManualAddUserESportResp{} }
func (m *ManualAddUserESportResp) String() string { return proto.CompactTextString(m) }
func (*ManualAddUserESportResp) ProtoMessage()    {}
func (*ManualAddUserESportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{12}
}
func (m *ManualAddUserESportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ManualAddUserESportResp.Unmarshal(m, b)
}
func (m *ManualAddUserESportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ManualAddUserESportResp.Marshal(b, m, deterministic)
}
func (dst *ManualAddUserESportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ManualAddUserESportResp.Merge(dst, src)
}
func (m *ManualAddUserESportResp) XXX_Size() int {
	return xxx_messageInfo_ManualAddUserESportResp.Size(m)
}
func (m *ManualAddUserESportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ManualAddUserESportResp.DiscardUnknown(m)
}

var xxx_messageInfo_ManualAddUserESportResp proto.InternalMessageInfo

// 电竞指导申请记录
type ApplyESportRecord struct {
	ApplyId              uint32   `protobuf:"varint,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Gender               uint32   `protobuf:"varint,5,opt,name=gender,proto3" json:"gender,omitempty"`
	Age                  uint32   `protobuf:"varint,6,opt,name=age,proto3" json:"age,omitempty"`
	GuildId              uint32   `protobuf:"varint,7,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ApplyTime            uint32   `protobuf:"varint,8,opt,name=apply_time,json=applyTime,proto3" json:"apply_time,omitempty"`
	ApplyType            uint32   `protobuf:"varint,9,opt,name=apply_type,json=applyType,proto3" json:"apply_type,omitempty"`
	ContractDuration     uint32   `protobuf:"varint,10,opt,name=contract_duration,json=contractDuration,proto3" json:"contract_duration,omitempty"`
	LoginOtherUids       []uint32 `protobuf:"varint,11,rep,packed,name=login_other_uids,json=loginOtherUids,proto3" json:"login_other_uids,omitempty"`
	RechargeNum          uint32   `protobuf:"varint,12,opt,name=recharge_num,json=rechargeNum,proto3" json:"recharge_num,omitempty"`
	Handler              string   `protobuf:"bytes,13,opt,name=handler,proto3" json:"handler,omitempty"`
	Remarks              string   `protobuf:"bytes,14,opt,name=remarks,proto3" json:"remarks,omitempty"`
	ApplyStatus          uint32   `protobuf:"varint,15,opt,name=apply_status,json=applyStatus,proto3" json:"apply_status,omitempty"`
	AuditToken           string   `protobuf:"bytes,16,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	AuditTime            uint32   `protobuf:"varint,17,opt,name=audit_time,json=auditTime,proto3" json:"audit_time,omitempty"`
	IdentityNum          string   `protobuf:"bytes,18,opt,name=identity_num,json=identityNum,proto3" json:"identity_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyESportRecord) Reset()         { *m = ApplyESportRecord{} }
func (m *ApplyESportRecord) String() string { return proto.CompactTextString(m) }
func (*ApplyESportRecord) ProtoMessage()    {}
func (*ApplyESportRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{13}
}
func (m *ApplyESportRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyESportRecord.Unmarshal(m, b)
}
func (m *ApplyESportRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyESportRecord.Marshal(b, m, deterministic)
}
func (dst *ApplyESportRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyESportRecord.Merge(dst, src)
}
func (m *ApplyESportRecord) XXX_Size() int {
	return xxx_messageInfo_ApplyESportRecord.Size(m)
}
func (m *ApplyESportRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyESportRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyESportRecord proto.InternalMessageInfo

func (m *ApplyESportRecord) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *ApplyESportRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyESportRecord) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ApplyESportRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApplyESportRecord) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *ApplyESportRecord) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *ApplyESportRecord) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ApplyESportRecord) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *ApplyESportRecord) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *ApplyESportRecord) GetContractDuration() uint32 {
	if m != nil {
		return m.ContractDuration
	}
	return 0
}

func (m *ApplyESportRecord) GetLoginOtherUids() []uint32 {
	if m != nil {
		return m.LoginOtherUids
	}
	return nil
}

func (m *ApplyESportRecord) GetRechargeNum() uint32 {
	if m != nil {
		return m.RechargeNum
	}
	return 0
}

func (m *ApplyESportRecord) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

func (m *ApplyESportRecord) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *ApplyESportRecord) GetApplyStatus() uint32 {
	if m != nil {
		return m.ApplyStatus
	}
	return 0
}

func (m *ApplyESportRecord) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *ApplyESportRecord) GetAuditTime() uint32 {
	if m != nil {
		return m.AuditTime
	}
	return 0
}

func (m *ApplyESportRecord) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

// 获取电竞指导身份申请记录
type GetApplyESportRecordReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	EsportType           uint32   `protobuf:"varint,2,opt,name=esport_type,json=esportType,proto3" json:"esport_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	StatusList           []uint32 `protobuf:"varint,7,rep,packed,name=status_list,json=statusList,proto3" json:"status_list,omitempty"`
	BeginTime            uint32   `protobuf:"varint,8,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyESportRecordReq) Reset()         { *m = GetApplyESportRecordReq{} }
func (m *GetApplyESportRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyESportRecordReq) ProtoMessage()    {}
func (*GetApplyESportRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{14}
}
func (m *GetApplyESportRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyESportRecordReq.Unmarshal(m, b)
}
func (m *GetApplyESportRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyESportRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyESportRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyESportRecordReq.Merge(dst, src)
}
func (m *GetApplyESportRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyESportRecordReq.Size(m)
}
func (m *GetApplyESportRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyESportRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyESportRecordReq proto.InternalMessageInfo

func (m *GetApplyESportRecordReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetApplyESportRecordReq) GetEsportType() uint32 {
	if m != nil {
		return m.EsportType
	}
	return 0
}

func (m *GetApplyESportRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetApplyESportRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetApplyESportRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetApplyESportRecordReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetApplyESportRecordReq) GetStatusList() []uint32 {
	if m != nil {
		return m.StatusList
	}
	return nil
}

func (m *GetApplyESportRecordReq) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *GetApplyESportRecordReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type GetApplyESportRecordResp struct {
	RecordList           []*ApplyESportRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32               `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetApplyESportRecordResp) Reset()         { *m = GetApplyESportRecordResp{} }
func (m *GetApplyESportRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyESportRecordResp) ProtoMessage()    {}
func (*GetApplyESportRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{15}
}
func (m *GetApplyESportRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyESportRecordResp.Unmarshal(m, b)
}
func (m *GetApplyESportRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyESportRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyESportRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyESportRecordResp.Merge(dst, src)
}
func (m *GetApplyESportRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyESportRecordResp.Size(m)
}
func (m *GetApplyESportRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyESportRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyESportRecordResp proto.InternalMessageInfo

func (m *GetApplyESportRecordResp) GetRecordList() []*ApplyESportRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetApplyESportRecordResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 批量处理电竞指导身份申请
type OfficialHandleApplyESportReq struct {
	ApplyId              []uint32 `protobuf:"varint,1,rep,packed,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	AuditType            uint32   `protobuf:"varint,2,opt,name=audit_type,json=auditType,proto3" json:"audit_type,omitempty"`
	Operator             string   `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator,omitempty"`
	Remarks              string   `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialHandleApplyESportReq) Reset()         { *m = OfficialHandleApplyESportReq{} }
func (m *OfficialHandleApplyESportReq) String() string { return proto.CompactTextString(m) }
func (*OfficialHandleApplyESportReq) ProtoMessage()    {}
func (*OfficialHandleApplyESportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{16}
}
func (m *OfficialHandleApplyESportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandleApplyESportReq.Unmarshal(m, b)
}
func (m *OfficialHandleApplyESportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandleApplyESportReq.Marshal(b, m, deterministic)
}
func (dst *OfficialHandleApplyESportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandleApplyESportReq.Merge(dst, src)
}
func (m *OfficialHandleApplyESportReq) XXX_Size() int {
	return xxx_messageInfo_OfficialHandleApplyESportReq.Size(m)
}
func (m *OfficialHandleApplyESportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandleApplyESportReq.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandleApplyESportReq proto.InternalMessageInfo

func (m *OfficialHandleApplyESportReq) GetApplyId() []uint32 {
	if m != nil {
		return m.ApplyId
	}
	return nil
}

func (m *OfficialHandleApplyESportReq) GetAuditType() uint32 {
	if m != nil {
		return m.AuditType
	}
	return 0
}

func (m *OfficialHandleApplyESportReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *OfficialHandleApplyESportReq) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

type OfficialHandleApplyESportResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OfficialHandleApplyESportResp) Reset()         { *m = OfficialHandleApplyESportResp{} }
func (m *OfficialHandleApplyESportResp) String() string { return proto.CompactTextString(m) }
func (*OfficialHandleApplyESportResp) ProtoMessage()    {}
func (*OfficialHandleApplyESportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{17}
}
func (m *OfficialHandleApplyESportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OfficialHandleApplyESportResp.Unmarshal(m, b)
}
func (m *OfficialHandleApplyESportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OfficialHandleApplyESportResp.Marshal(b, m, deterministic)
}
func (dst *OfficialHandleApplyESportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfficialHandleApplyESportResp.Merge(dst, src)
}
func (m *OfficialHandleApplyESportResp) XXX_Size() int {
	return xxx_messageInfo_OfficialHandleApplyESportResp.Size(m)
}
func (m *OfficialHandleApplyESportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OfficialHandleApplyESportResp.DiscardUnknown(m)
}

var xxx_messageInfo_OfficialHandleApplyESportResp proto.InternalMessageInfo

type ApplyBlackList struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTime            uint32   `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OpUser               string   `protobuf:"bytes,4,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	UpdateTime           uint32   `protobuf:"varint,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	Remarks              string   `protobuf:"bytes,6,opt,name=remarks,proto3" json:"remarks,omitempty"`
	Nickname             string   `protobuf:"bytes,7,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Ttid                 string   `protobuf:"bytes,8,opt,name=ttid,proto3" json:"ttid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ApplyBlackList) Reset()         { *m = ApplyBlackList{} }
func (m *ApplyBlackList) String() string { return proto.CompactTextString(m) }
func (*ApplyBlackList) ProtoMessage()    {}
func (*ApplyBlackList) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{18}
}
func (m *ApplyBlackList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyBlackList.Unmarshal(m, b)
}
func (m *ApplyBlackList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyBlackList.Marshal(b, m, deterministic)
}
func (dst *ApplyBlackList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyBlackList.Merge(dst, src)
}
func (m *ApplyBlackList) XXX_Size() int {
	return xxx_messageInfo_ApplyBlackList.Size(m)
}
func (m *ApplyBlackList) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyBlackList.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyBlackList proto.InternalMessageInfo

func (m *ApplyBlackList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ApplyBlackList) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *ApplyBlackList) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *ApplyBlackList) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *ApplyBlackList) GetUpdateTime() uint32 {
	if m != nil {
		return m.UpdateTime
	}
	return 0
}

func (m *ApplyBlackList) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *ApplyBlackList) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ApplyBlackList) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

// 获取黑名单管理记录
type GetApplyBlackListReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetApplyBlackListReq) Reset()         { *m = GetApplyBlackListReq{} }
func (m *GetApplyBlackListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyBlackListReq) ProtoMessage()    {}
func (*GetApplyBlackListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{19}
}
func (m *GetApplyBlackListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyBlackListReq.Unmarshal(m, b)
}
func (m *GetApplyBlackListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyBlackListReq.Marshal(b, m, deterministic)
}
func (dst *GetApplyBlackListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyBlackListReq.Merge(dst, src)
}
func (m *GetApplyBlackListReq) XXX_Size() int {
	return xxx_messageInfo_GetApplyBlackListReq.Size(m)
}
func (m *GetApplyBlackListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyBlackListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyBlackListReq proto.InternalMessageInfo

func (m *GetApplyBlackListReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetApplyBlackListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetApplyBlackListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetApplyBlackListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetApplyBlackListResp struct {
	RecordList           []*ApplyBlackList `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32            `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetApplyBlackListResp) Reset()         { *m = GetApplyBlackListResp{} }
func (m *GetApplyBlackListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyBlackListResp) ProtoMessage()    {}
func (*GetApplyBlackListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{20}
}
func (m *GetApplyBlackListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetApplyBlackListResp.Unmarshal(m, b)
}
func (m *GetApplyBlackListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetApplyBlackListResp.Marshal(b, m, deterministic)
}
func (dst *GetApplyBlackListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetApplyBlackListResp.Merge(dst, src)
}
func (m *GetApplyBlackListResp) XXX_Size() int {
	return xxx_messageInfo_GetApplyBlackListResp.Size(m)
}
func (m *GetApplyBlackListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetApplyBlackListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetApplyBlackListResp proto.InternalMessageInfo

func (m *GetApplyBlackListResp) GetRecordList() []*ApplyBlackList {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetApplyBlackListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 修改身份申请黑名单冻结时间
type UpdateApplyBlacklistFreezeTimeReq struct {
	Uid     uint32 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Remarks string `protobuf:"bytes,2,opt,name=remarks,proto3" json:"remarks,omitempty"`
	OpUser  string `protobuf:"bytes,3,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpType  uint32 `protobuf:"varint,4,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	// 自定义冻结时间
	EndTime              uint32   `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateApplyBlacklistFreezeTimeReq) Reset()         { *m = UpdateApplyBlacklistFreezeTimeReq{} }
func (m *UpdateApplyBlacklistFreezeTimeReq) String() string { return proto.CompactTextString(m) }
func (*UpdateApplyBlacklistFreezeTimeReq) ProtoMessage()    {}
func (*UpdateApplyBlacklistFreezeTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{21}
}
func (m *UpdateApplyBlacklistFreezeTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateApplyBlacklistFreezeTimeReq.Unmarshal(m, b)
}
func (m *UpdateApplyBlacklistFreezeTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateApplyBlacklistFreezeTimeReq.Marshal(b, m, deterministic)
}
func (dst *UpdateApplyBlacklistFreezeTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateApplyBlacklistFreezeTimeReq.Merge(dst, src)
}
func (m *UpdateApplyBlacklistFreezeTimeReq) XXX_Size() int {
	return xxx_messageInfo_UpdateApplyBlacklistFreezeTimeReq.Size(m)
}
func (m *UpdateApplyBlacklistFreezeTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateApplyBlacklistFreezeTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateApplyBlacklistFreezeTimeReq proto.InternalMessageInfo

func (m *UpdateApplyBlacklistFreezeTimeReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateApplyBlacklistFreezeTimeReq) GetRemarks() string {
	if m != nil {
		return m.Remarks
	}
	return ""
}

func (m *UpdateApplyBlacklistFreezeTimeReq) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *UpdateApplyBlacklistFreezeTimeReq) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *UpdateApplyBlacklistFreezeTimeReq) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type UpdateApplyBlacklistFreezeTimeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateApplyBlacklistFreezeTimeResp) Reset()         { *m = UpdateApplyBlacklistFreezeTimeResp{} }
func (m *UpdateApplyBlacklistFreezeTimeResp) String() string { return proto.CompactTextString(m) }
func (*UpdateApplyBlacklistFreezeTimeResp) ProtoMessage()    {}
func (*UpdateApplyBlacklistFreezeTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{22}
}
func (m *UpdateApplyBlacklistFreezeTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateApplyBlacklistFreezeTimeResp.Unmarshal(m, b)
}
func (m *UpdateApplyBlacklistFreezeTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateApplyBlacklistFreezeTimeResp.Marshal(b, m, deterministic)
}
func (dst *UpdateApplyBlacklistFreezeTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateApplyBlacklistFreezeTimeResp.Merge(dst, src)
}
func (m *UpdateApplyBlacklistFreezeTimeResp) XXX_Size() int {
	return xxx_messageInfo_UpdateApplyBlacklistFreezeTimeResp.Size(m)
}
func (m *UpdateApplyBlacklistFreezeTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateApplyBlacklistFreezeTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateApplyBlacklistFreezeTimeResp proto.InternalMessageInfo

// 签约时间详情
type SignTimeInfo struct {
	ObtainTime           uint32   `protobuf:"varint,1,opt,name=obtain_time,json=obtainTime,proto3" json:"obtain_time,omitempty"`
	CancelContractTime   uint32   `protobuf:"varint,2,opt,name=cancel_contract_time,json=cancelContractTime,proto3" json:"cancel_contract_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SignTimeInfo) Reset()         { *m = SignTimeInfo{} }
func (m *SignTimeInfo) String() string { return proto.CompactTextString(m) }
func (*SignTimeInfo) ProtoMessage()    {}
func (*SignTimeInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{23}
}
func (m *SignTimeInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SignTimeInfo.Unmarshal(m, b)
}
func (m *SignTimeInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SignTimeInfo.Marshal(b, m, deterministic)
}
func (dst *SignTimeInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SignTimeInfo.Merge(dst, src)
}
func (m *SignTimeInfo) XXX_Size() int {
	return xxx_messageInfo_SignTimeInfo.Size(m)
}
func (m *SignTimeInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SignTimeInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SignTimeInfo proto.InternalMessageInfo

func (m *SignTimeInfo) GetObtainTime() uint32 {
	if m != nil {
		return m.ObtainTime
	}
	return 0
}

func (m *SignTimeInfo) GetCancelContractTime() uint32 {
	if m != nil {
		return m.CancelContractTime
	}
	return 0
}

// 电竞指导库相关
type ESportErInfo struct {
	Uid              uint32                        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid             string                        `protobuf:"bytes,3,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname         string                        `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Age              uint32                        `protobuf:"varint,5,opt,name=age,proto3" json:"age,omitempty"`
	Gender           uint32                        `protobuf:"varint,6,opt,name=gender,proto3" json:"gender,omitempty"`
	EsportErType     uint32                        `protobuf:"varint,7,opt,name=esport_er_type,json=esportErType,proto3" json:"esport_er_type,omitempty"`
	SignGuildId      uint32                        `protobuf:"varint,8,opt,name=sign_guild_id,json=signGuildId,proto3" json:"sign_guild_id,omitempty"`
	SignTs           uint32                        `protobuf:"varint,9,opt,name=sign_ts,json=signTs,proto3" json:"sign_ts,omitempty"`
	SignExpTs        uint32                        `protobuf:"varint,10,opt,name=sign_exp_ts,json=signExpTs,proto3" json:"sign_exp_ts,omitempty"`
	ObtainRoleTs     uint32                        `protobuf:"varint,11,opt,name=obtain_role_ts,json=obtainRoleTs,proto3" json:"obtain_role_ts,omitempty"`
	FirstTakeOrderTs uint32                        `protobuf:"varint,12,opt,name=first_take_order_ts,json=firstTakeOrderTs,proto3" json:"first_take_order_ts,omitempty"`
	GameInfoList     []*esport_skill.UserSkillInfo `protobuf:"bytes,13,rep,name=game_info_list,json=gameInfoList,proto3" json:"game_info_list,omitempty"`
	// 操作信息
	OpUser               string          `protobuf:"bytes,14,opt,name=op_user,json=opUser,proto3" json:"op_user,omitempty"`
	OpTs                 uint32          `protobuf:"varint,15,opt,name=op_ts,json=opTs,proto3" json:"op_ts,omitempty"`
	IdentityNum          string          `protobuf:"bytes,16,opt,name=identity_num,json=identityNum,proto3" json:"identity_num,omitempty"`
	SignList             []*SignTimeInfo `protobuf:"bytes,17,rep,name=sign_list,json=signList,proto3" json:"sign_list,omitempty"`
	FreezeTime           uint32          `protobuf:"varint,18,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	Reason               string          `protobuf:"bytes,19,opt,name=reason,proto3" json:"reason,omitempty"`
	SignGuildName        string          `protobuf:"bytes,20,opt,name=sign_guild_name,json=signGuildName,proto3" json:"sign_guild_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *ESportErInfo) Reset()         { *m = ESportErInfo{} }
func (m *ESportErInfo) String() string { return proto.CompactTextString(m) }
func (*ESportErInfo) ProtoMessage()    {}
func (*ESportErInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{24}
}
func (m *ESportErInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportErInfo.Unmarshal(m, b)
}
func (m *ESportErInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportErInfo.Marshal(b, m, deterministic)
}
func (dst *ESportErInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportErInfo.Merge(dst, src)
}
func (m *ESportErInfo) XXX_Size() int {
	return xxx_messageInfo_ESportErInfo.Size(m)
}
func (m *ESportErInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportErInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ESportErInfo proto.InternalMessageInfo

func (m *ESportErInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ESportErInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *ESportErInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *ESportErInfo) GetAge() uint32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *ESportErInfo) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *ESportErInfo) GetEsportErType() uint32 {
	if m != nil {
		return m.EsportErType
	}
	return 0
}

func (m *ESportErInfo) GetSignGuildId() uint32 {
	if m != nil {
		return m.SignGuildId
	}
	return 0
}

func (m *ESportErInfo) GetSignTs() uint32 {
	if m != nil {
		return m.SignTs
	}
	return 0
}

func (m *ESportErInfo) GetSignExpTs() uint32 {
	if m != nil {
		return m.SignExpTs
	}
	return 0
}

func (m *ESportErInfo) GetObtainRoleTs() uint32 {
	if m != nil {
		return m.ObtainRoleTs
	}
	return 0
}

func (m *ESportErInfo) GetFirstTakeOrderTs() uint32 {
	if m != nil {
		return m.FirstTakeOrderTs
	}
	return 0
}

func (m *ESportErInfo) GetGameInfoList() []*esport_skill.UserSkillInfo {
	if m != nil {
		return m.GameInfoList
	}
	return nil
}

func (m *ESportErInfo) GetOpUser() string {
	if m != nil {
		return m.OpUser
	}
	return ""
}

func (m *ESportErInfo) GetOpTs() uint32 {
	if m != nil {
		return m.OpTs
	}
	return 0
}

func (m *ESportErInfo) GetIdentityNum() string {
	if m != nil {
		return m.IdentityNum
	}
	return ""
}

func (m *ESportErInfo) GetSignList() []*SignTimeInfo {
	if m != nil {
		return m.SignList
	}
	return nil
}

func (m *ESportErInfo) GetFreezeTime() uint32 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

func (m *ESportErInfo) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

func (m *ESportErInfo) GetSignGuildName() string {
	if m != nil {
		return m.SignGuildName
	}
	return ""
}

// 分页获取电竞指导信息
type GetESportErInfoListReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	EsportType           uint32   `protobuf:"varint,2,opt,name=esport_type,json=esportType,proto3" json:"esport_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetESportErInfoListReq) Reset()         { *m = GetESportErInfoListReq{} }
func (m *GetESportErInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetESportErInfoListReq) ProtoMessage()    {}
func (*GetESportErInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{25}
}
func (m *GetESportErInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportErInfoListReq.Unmarshal(m, b)
}
func (m *GetESportErInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportErInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetESportErInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportErInfoListReq.Merge(dst, src)
}
func (m *GetESportErInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetESportErInfoListReq.Size(m)
}
func (m *GetESportErInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportErInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportErInfoListReq proto.InternalMessageInfo

func (m *GetESportErInfoListReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetESportErInfoListReq) GetEsportType() uint32 {
	if m != nil {
		return m.EsportType
	}
	return 0
}

func (m *GetESportErInfoListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetESportErInfoListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetESportErInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetESportErInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetESportErInfoListResp struct {
	InfoList             []*ESportErInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetESportErInfoListResp) Reset()         { *m = GetESportErInfoListResp{} }
func (m *GetESportErInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetESportErInfoListResp) ProtoMessage()    {}
func (*GetESportErInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{26}
}
func (m *GetESportErInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetESportErInfoListResp.Unmarshal(m, b)
}
func (m *GetESportErInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetESportErInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetESportErInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetESportErInfoListResp.Merge(dst, src)
}
func (m *GetESportErInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetESportErInfoListResp.Size(m)
}
func (m *GetESportErInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetESportErInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetESportErInfoListResp proto.InternalMessageInfo

func (m *GetESportErInfoListResp) GetInfoList() []*ESportErInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetESportErInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type ESportRoleInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	EsportRole           uint32   `protobuf:"varint,3,opt,name=esport_role,json=esportRole,proto3" json:"esport_role,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ESportRoleInfo) Reset()         { *m = ESportRoleInfo{} }
func (m *ESportRoleInfo) String() string { return proto.CompactTextString(m) }
func (*ESportRoleInfo) ProtoMessage()    {}
func (*ESportRoleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{27}
}
func (m *ESportRoleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ESportRoleInfo.Unmarshal(m, b)
}
func (m *ESportRoleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ESportRoleInfo.Marshal(b, m, deterministic)
}
func (dst *ESportRoleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ESportRoleInfo.Merge(dst, src)
}
func (m *ESportRoleInfo) XXX_Size() int {
	return xxx_messageInfo_ESportRoleInfo.Size(m)
}
func (m *ESportRoleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ESportRoleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ESportRoleInfo proto.InternalMessageInfo

func (m *ESportRoleInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ESportRoleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ESportRoleInfo) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

type GetAllESportRoleReq struct {
	Offset               uint32   `protobuf:"varint,1,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllESportRoleReq) Reset()         { *m = GetAllESportRoleReq{} }
func (m *GetAllESportRoleReq) String() string { return proto.CompactTextString(m) }
func (*GetAllESportRoleReq) ProtoMessage()    {}
func (*GetAllESportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{28}
}
func (m *GetAllESportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllESportRoleReq.Unmarshal(m, b)
}
func (m *GetAllESportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllESportRoleReq.Marshal(b, m, deterministic)
}
func (dst *GetAllESportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllESportRoleReq.Merge(dst, src)
}
func (m *GetAllESportRoleReq) XXX_Size() int {
	return xxx_messageInfo_GetAllESportRoleReq.Size(m)
}
func (m *GetAllESportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllESportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllESportRoleReq proto.InternalMessageInfo

func (m *GetAllESportRoleReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetAllESportRoleReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAllESportRoleResp struct {
	InfoList             []*ESportRoleInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetAllESportRoleResp) Reset()         { *m = GetAllESportRoleResp{} }
func (m *GetAllESportRoleResp) String() string { return proto.CompactTextString(m) }
func (*GetAllESportRoleResp) ProtoMessage()    {}
func (*GetAllESportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{29}
}
func (m *GetAllESportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllESportRoleResp.Unmarshal(m, b)
}
func (m *GetAllESportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllESportRoleResp.Marshal(b, m, deterministic)
}
func (dst *GetAllESportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllESportRoleResp.Merge(dst, src)
}
func (m *GetAllESportRoleResp) XXX_Size() int {
	return xxx_messageInfo_GetAllESportRoleResp.Size(m)
}
func (m *GetAllESportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllESportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllESportRoleResp proto.InternalMessageInfo

func (m *GetAllESportRoleResp) GetInfoList() []*ESportRoleInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

type ReclaimInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Remark               string   `protobuf:"bytes,2,opt,name=remark,proto3" json:"remark,omitempty"`
	FreezeTime           int32    `protobuf:"varint,3,opt,name=freeze_time,json=freezeTime,proto3" json:"freeze_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReclaimInfo) Reset()         { *m = ReclaimInfo{} }
func (m *ReclaimInfo) String() string { return proto.CompactTextString(m) }
func (*ReclaimInfo) ProtoMessage()    {}
func (*ReclaimInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{30}
}
func (m *ReclaimInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimInfo.Unmarshal(m, b)
}
func (m *ReclaimInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimInfo.Marshal(b, m, deterministic)
}
func (dst *ReclaimInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimInfo.Merge(dst, src)
}
func (m *ReclaimInfo) XXX_Size() int {
	return xxx_messageInfo_ReclaimInfo.Size(m)
}
func (m *ReclaimInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimInfo proto.InternalMessageInfo

func (m *ReclaimInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ReclaimInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *ReclaimInfo) GetFreezeTime() int32 {
	if m != nil {
		return m.FreezeTime
	}
	return 0
}

// 批量回收用户电竞指导身份
type ReclaimESportErIdentityReq struct {
	InfoList             []*ReclaimInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Handler              string         `protobuf:"bytes,2,opt,name=handler,proto3" json:"handler,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ReclaimESportErIdentityReq) Reset()         { *m = ReclaimESportErIdentityReq{} }
func (m *ReclaimESportErIdentityReq) String() string { return proto.CompactTextString(m) }
func (*ReclaimESportErIdentityReq) ProtoMessage()    {}
func (*ReclaimESportErIdentityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{31}
}
func (m *ReclaimESportErIdentityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimESportErIdentityReq.Unmarshal(m, b)
}
func (m *ReclaimESportErIdentityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimESportErIdentityReq.Marshal(b, m, deterministic)
}
func (dst *ReclaimESportErIdentityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimESportErIdentityReq.Merge(dst, src)
}
func (m *ReclaimESportErIdentityReq) XXX_Size() int {
	return xxx_messageInfo_ReclaimESportErIdentityReq.Size(m)
}
func (m *ReclaimESportErIdentityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimESportErIdentityReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimESportErIdentityReq proto.InternalMessageInfo

func (m *ReclaimESportErIdentityReq) GetInfoList() []*ReclaimInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *ReclaimESportErIdentityReq) GetHandler() string {
	if m != nil {
		return m.Handler
	}
	return ""
}

type ReclaimESportErIdentityResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReclaimESportErIdentityResp) Reset()         { *m = ReclaimESportErIdentityResp{} }
func (m *ReclaimESportErIdentityResp) String() string { return proto.CompactTextString(m) }
func (*ReclaimESportErIdentityResp) ProtoMessage()    {}
func (*ReclaimESportErIdentityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{32}
}
func (m *ReclaimESportErIdentityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReclaimESportErIdentityResp.Unmarshal(m, b)
}
func (m *ReclaimESportErIdentityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReclaimESportErIdentityResp.Marshal(b, m, deterministic)
}
func (dst *ReclaimESportErIdentityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReclaimESportErIdentityResp.Merge(dst, src)
}
func (m *ReclaimESportErIdentityResp) XXX_Size() int {
	return xxx_messageInfo_ReclaimESportErIdentityResp.Size(m)
}
func (m *ReclaimESportErIdentityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReclaimESportErIdentityResp.DiscardUnknown(m)
}

var xxx_messageInfo_ReclaimESportErIdentityResp proto.InternalMessageInfo

type GetReclaimInfoListReq struct {
	QueryType            uint32   `protobuf:"varint,1,opt,name=query_type,json=queryType,proto3" json:"query_type,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetReclaimInfoListReq) Reset()         { *m = GetReclaimInfoListReq{} }
func (m *GetReclaimInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetReclaimInfoListReq) ProtoMessage()    {}
func (*GetReclaimInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{33}
}
func (m *GetReclaimInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReclaimInfoListReq.Unmarshal(m, b)
}
func (m *GetReclaimInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReclaimInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetReclaimInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReclaimInfoListReq.Merge(dst, src)
}
func (m *GetReclaimInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetReclaimInfoListReq.Size(m)
}
func (m *GetReclaimInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReclaimInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetReclaimInfoListReq proto.InternalMessageInfo

func (m *GetReclaimInfoListReq) GetQueryType() uint32 {
	if m != nil {
		return m.QueryType
	}
	return 0
}

func (m *GetReclaimInfoListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetReclaimInfoListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetReclaimInfoListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetReclaimInfoListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetReclaimInfoListResp struct {
	InfoList             []*ESportErInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	TotalCnt             uint32          `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetReclaimInfoListResp) Reset()         { *m = GetReclaimInfoListResp{} }
func (m *GetReclaimInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetReclaimInfoListResp) ProtoMessage()    {}
func (*GetReclaimInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{34}
}
func (m *GetReclaimInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetReclaimInfoListResp.Unmarshal(m, b)
}
func (m *GetReclaimInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetReclaimInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetReclaimInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetReclaimInfoListResp.Merge(dst, src)
}
func (m *GetReclaimInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetReclaimInfoListResp.Size(m)
}
func (m *GetReclaimInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetReclaimInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetReclaimInfoListResp proto.InternalMessageInfo

func (m *GetReclaimInfoListResp) GetInfoList() []*ESportErInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetReclaimInfoListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type BatCheckUserESportRoleReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatCheckUserESportRoleReq) Reset()         { *m = BatCheckUserESportRoleReq{} }
func (m *BatCheckUserESportRoleReq) String() string { return proto.CompactTextString(m) }
func (*BatCheckUserESportRoleReq) ProtoMessage()    {}
func (*BatCheckUserESportRoleReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{35}
}
func (m *BatCheckUserESportRoleReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatCheckUserESportRoleReq.Unmarshal(m, b)
}
func (m *BatCheckUserESportRoleReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatCheckUserESportRoleReq.Marshal(b, m, deterministic)
}
func (dst *BatCheckUserESportRoleReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatCheckUserESportRoleReq.Merge(dst, src)
}
func (m *BatCheckUserESportRoleReq) XXX_Size() int {
	return xxx_messageInfo_BatCheckUserESportRoleReq.Size(m)
}
func (m *BatCheckUserESportRoleReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatCheckUserESportRoleReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatCheckUserESportRoleReq proto.InternalMessageInfo

func (m *BatCheckUserESportRoleReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatCheckUserESportRoleResp struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatCheckUserESportRoleResp) Reset()         { *m = BatCheckUserESportRoleResp{} }
func (m *BatCheckUserESportRoleResp) String() string { return proto.CompactTextString(m) }
func (*BatCheckUserESportRoleResp) ProtoMessage()    {}
func (*BatCheckUserESportRoleResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{36}
}
func (m *BatCheckUserESportRoleResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatCheckUserESportRoleResp.Unmarshal(m, b)
}
func (m *BatCheckUserESportRoleResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatCheckUserESportRoleResp.Marshal(b, m, deterministic)
}
func (dst *BatCheckUserESportRoleResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatCheckUserESportRoleResp.Merge(dst, src)
}
func (m *BatCheckUserESportRoleResp) XXX_Size() int {
	return xxx_messageInfo_BatCheckUserESportRoleResp.Size(m)
}
func (m *BatCheckUserESportRoleResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatCheckUserESportRoleResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatCheckUserESportRoleResp proto.InternalMessageInfo

func (m *BatCheckUserESportRoleResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CheckUserApplyESportReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IdentifyNum          string   `protobuf:"bytes,2,opt,name=identify_num,json=identifyNum,proto3" json:"identify_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserApplyESportReq) Reset()         { *m = CheckUserApplyESportReq{} }
func (m *CheckUserApplyESportReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserApplyESportReq) ProtoMessage()    {}
func (*CheckUserApplyESportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{37}
}
func (m *CheckUserApplyESportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserApplyESportReq.Unmarshal(m, b)
}
func (m *CheckUserApplyESportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserApplyESportReq.Marshal(b, m, deterministic)
}
func (dst *CheckUserApplyESportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserApplyESportReq.Merge(dst, src)
}
func (m *CheckUserApplyESportReq) XXX_Size() int {
	return xxx_messageInfo_CheckUserApplyESportReq.Size(m)
}
func (m *CheckUserApplyESportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserApplyESportReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserApplyESportReq proto.InternalMessageInfo

func (m *CheckUserApplyESportReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckUserApplyESportReq) GetIdentifyNum() string {
	if m != nil {
		return m.IdentifyNum
	}
	return ""
}

type CheckUserApplyESportResp struct {
	ApplyId              uint32   `protobuf:"varint,1,opt,name=apply_id,json=applyId,proto3" json:"apply_id,omitempty"`
	ApplyType            uint32   `protobuf:"varint,2,opt,name=apply_type,json=applyType,proto3" json:"apply_type,omitempty"`
	ApplyStatus          uint32   `protobuf:"varint,3,opt,name=apply_status,json=applyStatus,proto3" json:"apply_status,omitempty"`
	AuditToken           string   `protobuf:"bytes,4,opt,name=audit_token,json=auditToken,proto3" json:"audit_token,omitempty"`
	GuildId              uint32   `protobuf:"varint,5,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckUserApplyESportResp) Reset()         { *m = CheckUserApplyESportResp{} }
func (m *CheckUserApplyESportResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserApplyESportResp) ProtoMessage()    {}
func (*CheckUserApplyESportResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{38}
}
func (m *CheckUserApplyESportResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckUserApplyESportResp.Unmarshal(m, b)
}
func (m *CheckUserApplyESportResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckUserApplyESportResp.Marshal(b, m, deterministic)
}
func (dst *CheckUserApplyESportResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckUserApplyESportResp.Merge(dst, src)
}
func (m *CheckUserApplyESportResp) XXX_Size() int {
	return xxx_messageInfo_CheckUserApplyESportResp.Size(m)
}
func (m *CheckUserApplyESportResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckUserApplyESportResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckUserApplyESportResp proto.InternalMessageInfo

func (m *CheckUserApplyESportResp) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *CheckUserApplyESportResp) GetApplyType() uint32 {
	if m != nil {
		return m.ApplyType
	}
	return 0
}

func (m *CheckUserApplyESportResp) GetApplyStatus() uint32 {
	if m != nil {
		return m.ApplyStatus
	}
	return 0
}

func (m *CheckUserApplyESportResp) GetAuditToken() string {
	if m != nil {
		return m.AuditToken
	}
	return ""
}

func (m *CheckUserApplyESportResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetBlackListHandleRecordReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBlackListHandleRecordReq) Reset()         { *m = GetBlackListHandleRecordReq{} }
func (m *GetBlackListHandleRecordReq) String() string { return proto.CompactTextString(m) }
func (*GetBlackListHandleRecordReq) ProtoMessage()    {}
func (*GetBlackListHandleRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{39}
}
func (m *GetBlackListHandleRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlackListHandleRecordReq.Unmarshal(m, b)
}
func (m *GetBlackListHandleRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlackListHandleRecordReq.Marshal(b, m, deterministic)
}
func (dst *GetBlackListHandleRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlackListHandleRecordReq.Merge(dst, src)
}
func (m *GetBlackListHandleRecordReq) XXX_Size() int {
	return xxx_messageInfo_GetBlackListHandleRecordReq.Size(m)
}
func (m *GetBlackListHandleRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlackListHandleRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlackListHandleRecordReq proto.InternalMessageInfo

func (m *GetBlackListHandleRecordReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetBlackListHandleRecordReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetBlackListHandleRecordReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 黑名单操作记录
type BlackListHandleInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ttid                 string   `protobuf:"bytes,2,opt,name=ttid,proto3" json:"ttid,omitempty"`
	Nickname             string   `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`
	OpType               uint32   `protobuf:"varint,4,opt,name=op_type,json=opType,proto3" json:"op_type,omitempty"`
	OpBlackListUser      string   `protobuf:"bytes,5,opt,name=op_black_list_user,json=opBlackListUser,proto3" json:"op_black_list_user,omitempty"`
	OpBlackListTime      uint32   `protobuf:"varint,6,opt,name=op_black_list_time,json=opBlackListTime,proto3" json:"op_black_list_time,omitempty"`
	OpBlackListReason    string   `protobuf:"bytes,7,opt,name=op_black_list_reason,json=opBlackListReason,proto3" json:"op_black_list_reason,omitempty"`
	BeginTime            uint32   `protobuf:"varint,8,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              uint32   `protobuf:"varint,9,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	DefriendUser         string   `protobuf:"bytes,10,opt,name=defriend_user,json=defriendUser,proto3" json:"defriend_user,omitempty"`
	DefriendTime         uint32   `protobuf:"varint,11,opt,name=defriend_time,json=defriendTime,proto3" json:"defriend_time,omitempty"`
	DefriendReason       string   `protobuf:"bytes,12,opt,name=defriend_reason,json=defriendReason,proto3" json:"defriend_reason,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BlackListHandleInfo) Reset()         { *m = BlackListHandleInfo{} }
func (m *BlackListHandleInfo) String() string { return proto.CompactTextString(m) }
func (*BlackListHandleInfo) ProtoMessage()    {}
func (*BlackListHandleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{40}
}
func (m *BlackListHandleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BlackListHandleInfo.Unmarshal(m, b)
}
func (m *BlackListHandleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BlackListHandleInfo.Marshal(b, m, deterministic)
}
func (dst *BlackListHandleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BlackListHandleInfo.Merge(dst, src)
}
func (m *BlackListHandleInfo) XXX_Size() int {
	return xxx_messageInfo_BlackListHandleInfo.Size(m)
}
func (m *BlackListHandleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BlackListHandleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BlackListHandleInfo proto.InternalMessageInfo

func (m *BlackListHandleInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BlackListHandleInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *BlackListHandleInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BlackListHandleInfo) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *BlackListHandleInfo) GetOpBlackListUser() string {
	if m != nil {
		return m.OpBlackListUser
	}
	return ""
}

func (m *BlackListHandleInfo) GetOpBlackListTime() uint32 {
	if m != nil {
		return m.OpBlackListTime
	}
	return 0
}

func (m *BlackListHandleInfo) GetOpBlackListReason() string {
	if m != nil {
		return m.OpBlackListReason
	}
	return ""
}

func (m *BlackListHandleInfo) GetBeginTime() uint32 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BlackListHandleInfo) GetEndTime() uint32 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *BlackListHandleInfo) GetDefriendUser() string {
	if m != nil {
		return m.DefriendUser
	}
	return ""
}

func (m *BlackListHandleInfo) GetDefriendTime() uint32 {
	if m != nil {
		return m.DefriendTime
	}
	return 0
}

func (m *BlackListHandleInfo) GetDefriendReason() string {
	if m != nil {
		return m.DefriendReason
	}
	return ""
}

type GetBlackListHandleRecordResp struct {
	List                 []*BlackListHandleInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	TotalCnt             uint32                 `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GetBlackListHandleRecordResp) Reset()         { *m = GetBlackListHandleRecordResp{} }
func (m *GetBlackListHandleRecordResp) String() string { return proto.CompactTextString(m) }
func (*GetBlackListHandleRecordResp) ProtoMessage()    {}
func (*GetBlackListHandleRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{41}
}
func (m *GetBlackListHandleRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBlackListHandleRecordResp.Unmarshal(m, b)
}
func (m *GetBlackListHandleRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBlackListHandleRecordResp.Marshal(b, m, deterministic)
}
func (dst *GetBlackListHandleRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBlackListHandleRecordResp.Merge(dst, src)
}
func (m *GetBlackListHandleRecordResp) XXX_Size() int {
	return xxx_messageInfo_GetBlackListHandleRecordResp.Size(m)
}
func (m *GetBlackListHandleRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBlackListHandleRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBlackListHandleRecordResp proto.InternalMessageInfo

func (m *GetBlackListHandleRecordResp) GetList() []*BlackListHandleInfo {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetBlackListHandleRecordResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type CheckSignGuildRiskAuditingReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSignGuildRiskAuditingReq) Reset()         { *m = CheckSignGuildRiskAuditingReq{} }
func (m *CheckSignGuildRiskAuditingReq) String() string { return proto.CompactTextString(m) }
func (*CheckSignGuildRiskAuditingReq) ProtoMessage()    {}
func (*CheckSignGuildRiskAuditingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{42}
}
func (m *CheckSignGuildRiskAuditingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSignGuildRiskAuditingReq.Unmarshal(m, b)
}
func (m *CheckSignGuildRiskAuditingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSignGuildRiskAuditingReq.Marshal(b, m, deterministic)
}
func (dst *CheckSignGuildRiskAuditingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSignGuildRiskAuditingReq.Merge(dst, src)
}
func (m *CheckSignGuildRiskAuditingReq) XXX_Size() int {
	return xxx_messageInfo_CheckSignGuildRiskAuditingReq.Size(m)
}
func (m *CheckSignGuildRiskAuditingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSignGuildRiskAuditingReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSignGuildRiskAuditingReq proto.InternalMessageInfo

func (m *CheckSignGuildRiskAuditingReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckSignGuildRiskAuditingResp struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckSignGuildRiskAuditingResp) Reset()         { *m = CheckSignGuildRiskAuditingResp{} }
func (m *CheckSignGuildRiskAuditingResp) String() string { return proto.CompactTextString(m) }
func (*CheckSignGuildRiskAuditingResp) ProtoMessage()    {}
func (*CheckSignGuildRiskAuditingResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{43}
}
func (m *CheckSignGuildRiskAuditingResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckSignGuildRiskAuditingResp.Unmarshal(m, b)
}
func (m *CheckSignGuildRiskAuditingResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckSignGuildRiskAuditingResp.Marshal(b, m, deterministic)
}
func (dst *CheckSignGuildRiskAuditingResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckSignGuildRiskAuditingResp.Merge(dst, src)
}
func (m *CheckSignGuildRiskAuditingResp) XXX_Size() int {
	return xxx_messageInfo_CheckSignGuildRiskAuditingResp.Size(m)
}
func (m *CheckSignGuildRiskAuditingResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckSignGuildRiskAuditingResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckSignGuildRiskAuditingResp proto.InternalMessageInfo

func (m *CheckSignGuildRiskAuditingResp) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type CoachLabel struct {
	Type                 LabelSourceType `protobuf:"varint,1,opt,name=type,proto3,enum=esport_role.LabelSourceType" json:"type,omitempty"`
	SourceUrl            string          `protobuf:"bytes,2,opt,name=source_url,json=sourceUrl,proto3" json:"source_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CoachLabel) Reset()         { *m = CoachLabel{} }
func (m *CoachLabel) String() string { return proto.CompactTextString(m) }
func (*CoachLabel) ProtoMessage()    {}
func (*CoachLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{44}
}
func (m *CoachLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CoachLabel.Unmarshal(m, b)
}
func (m *CoachLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CoachLabel.Marshal(b, m, deterministic)
}
func (dst *CoachLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CoachLabel.Merge(dst, src)
}
func (m *CoachLabel) XXX_Size() int {
	return xxx_messageInfo_CoachLabel.Size(m)
}
func (m *CoachLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_CoachLabel.DiscardUnknown(m)
}

var xxx_messageInfo_CoachLabel proto.InternalMessageInfo

func (m *CoachLabel) GetType() LabelSourceType {
	if m != nil {
		return m.Type
	}
	return LabelSourceType_LABEL_SOURCE_TYPE_UNSPECIFIED
}

func (m *CoachLabel) GetSourceUrl() string {
	if m != nil {
		return m.SourceUrl
	}
	return ""
}

// 批量下发电竞标识
type BatchGrantCoachLabelRequest struct {
	Uid                  []uint32    `protobuf:"varint,1,rep,packed,name=uid,proto3" json:"uid,omitempty"`
	Label                *CoachLabel `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	BeginTime            int64       `protobuf:"varint,3,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64       `protobuf:"varint,4,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *BatchGrantCoachLabelRequest) Reset()         { *m = BatchGrantCoachLabelRequest{} }
func (m *BatchGrantCoachLabelRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGrantCoachLabelRequest) ProtoMessage()    {}
func (*BatchGrantCoachLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{45}
}
func (m *BatchGrantCoachLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGrantCoachLabelRequest.Unmarshal(m, b)
}
func (m *BatchGrantCoachLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGrantCoachLabelRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGrantCoachLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGrantCoachLabelRequest.Merge(dst, src)
}
func (m *BatchGrantCoachLabelRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGrantCoachLabelRequest.Size(m)
}
func (m *BatchGrantCoachLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGrantCoachLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGrantCoachLabelRequest proto.InternalMessageInfo

func (m *BatchGrantCoachLabelRequest) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *BatchGrantCoachLabelRequest) GetLabel() *CoachLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *BatchGrantCoachLabelRequest) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *BatchGrantCoachLabelRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type BatchGrantCoachLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGrantCoachLabelResponse) Reset()         { *m = BatchGrantCoachLabelResponse{} }
func (m *BatchGrantCoachLabelResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGrantCoachLabelResponse) ProtoMessage()    {}
func (*BatchGrantCoachLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{46}
}
func (m *BatchGrantCoachLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGrantCoachLabelResponse.Unmarshal(m, b)
}
func (m *BatchGrantCoachLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGrantCoachLabelResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGrantCoachLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGrantCoachLabelResponse.Merge(dst, src)
}
func (m *BatchGrantCoachLabelResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGrantCoachLabelResponse.Size(m)
}
func (m *BatchGrantCoachLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGrantCoachLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGrantCoachLabelResponse proto.InternalMessageInfo

type GetCoachLabelRecordByPageRequest struct {
	Uid                  uint32           `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               CoachLabelStatus `protobuf:"varint,2,opt,name=status,proto3,enum=esport_role.CoachLabelStatus" json:"status,omitempty"`
	PageNum              uint32           `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	PageSize             uint32           `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCoachLabelRecordByPageRequest) Reset()         { *m = GetCoachLabelRecordByPageRequest{} }
func (m *GetCoachLabelRecordByPageRequest) String() string { return proto.CompactTextString(m) }
func (*GetCoachLabelRecordByPageRequest) ProtoMessage()    {}
func (*GetCoachLabelRecordByPageRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{47}
}
func (m *GetCoachLabelRecordByPageRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachLabelRecordByPageRequest.Unmarshal(m, b)
}
func (m *GetCoachLabelRecordByPageRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachLabelRecordByPageRequest.Marshal(b, m, deterministic)
}
func (dst *GetCoachLabelRecordByPageRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachLabelRecordByPageRequest.Merge(dst, src)
}
func (m *GetCoachLabelRecordByPageRequest) XXX_Size() int {
	return xxx_messageInfo_GetCoachLabelRecordByPageRequest.Size(m)
}
func (m *GetCoachLabelRecordByPageRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachLabelRecordByPageRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachLabelRecordByPageRequest proto.InternalMessageInfo

func (m *GetCoachLabelRecordByPageRequest) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachLabelRecordByPageRequest) GetStatus() CoachLabelStatus {
	if m != nil {
		return m.Status
	}
	return CoachLabelStatus_COACH_LABEL_STATUS_UNSPECIFIED
}

func (m *GetCoachLabelRecordByPageRequest) GetPageNum() uint32 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetCoachLabelRecordByPageRequest) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetCoachLabelRecordByPageResponse struct {
	RecordList           []*GetCoachLabelRecordByPageResponse_CoachLabelRecord `protobuf:"bytes,1,rep,name=record_list,json=recordList,proto3" json:"record_list,omitempty"`
	TotalCnt             uint32                                                `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                              `json:"-"`
	XXX_unrecognized     []byte                                                `json:"-"`
	XXX_sizecache        int32                                                 `json:"-"`
}

func (m *GetCoachLabelRecordByPageResponse) Reset()         { *m = GetCoachLabelRecordByPageResponse{} }
func (m *GetCoachLabelRecordByPageResponse) String() string { return proto.CompactTextString(m) }
func (*GetCoachLabelRecordByPageResponse) ProtoMessage()    {}
func (*GetCoachLabelRecordByPageResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{48}
}
func (m *GetCoachLabelRecordByPageResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachLabelRecordByPageResponse.Unmarshal(m, b)
}
func (m *GetCoachLabelRecordByPageResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachLabelRecordByPageResponse.Marshal(b, m, deterministic)
}
func (dst *GetCoachLabelRecordByPageResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachLabelRecordByPageResponse.Merge(dst, src)
}
func (m *GetCoachLabelRecordByPageResponse) XXX_Size() int {
	return xxx_messageInfo_GetCoachLabelRecordByPageResponse.Size(m)
}
func (m *GetCoachLabelRecordByPageResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachLabelRecordByPageResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachLabelRecordByPageResponse proto.InternalMessageInfo

func (m *GetCoachLabelRecordByPageResponse) GetRecordList() []*GetCoachLabelRecordByPageResponse_CoachLabelRecord {
	if m != nil {
		return m.RecordList
	}
	return nil
}

func (m *GetCoachLabelRecordByPageResponse) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

type GetCoachLabelRecordByPageResponse_CoachLabelRecord struct {
	RecordId             uint32           `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Uid                  uint32           `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Username             string           `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname             string           `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	StartTime            int64            `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime              int64            `protobuf:"varint,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	Label                *CoachLabel      `protobuf:"bytes,7,opt,name=label,proto3" json:"label,omitempty"`
	Status               CoachLabelStatus `protobuf:"varint,8,opt,name=status,proto3,enum=esport_role.CoachLabelStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) Reset() {
	*m = GetCoachLabelRecordByPageResponse_CoachLabelRecord{}
}
func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) String() string {
	return proto.CompactTextString(m)
}
func (*GetCoachLabelRecordByPageResponse_CoachLabelRecord) ProtoMessage() {}
func (*GetCoachLabelRecordByPageResponse_CoachLabelRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{48, 0}
}
func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachLabelRecordByPageResponse_CoachLabelRecord.Unmarshal(m, b)
}
func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachLabelRecordByPageResponse_CoachLabelRecord.Marshal(b, m, deterministic)
}
func (dst *GetCoachLabelRecordByPageResponse_CoachLabelRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachLabelRecordByPageResponse_CoachLabelRecord.Merge(dst, src)
}
func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) XXX_Size() int {
	return xxx_messageInfo_GetCoachLabelRecordByPageResponse_CoachLabelRecord.Size(m)
}
func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachLabelRecordByPageResponse_CoachLabelRecord.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachLabelRecordByPageResponse_CoachLabelRecord proto.InternalMessageInfo

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetStartTime() int64 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetLabel() *CoachLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *GetCoachLabelRecordByPageResponse_CoachLabelRecord) GetStatus() CoachLabelStatus {
	if m != nil {
		return m.Status
	}
	return CoachLabelStatus_COACH_LABEL_STATUS_UNSPECIFIED
}

// 收回
type RecallCoachLabelRequest struct {
	RecordId             uint32   `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallCoachLabelRequest) Reset()         { *m = RecallCoachLabelRequest{} }
func (m *RecallCoachLabelRequest) String() string { return proto.CompactTextString(m) }
func (*RecallCoachLabelRequest) ProtoMessage()    {}
func (*RecallCoachLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{49}
}
func (m *RecallCoachLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallCoachLabelRequest.Unmarshal(m, b)
}
func (m *RecallCoachLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallCoachLabelRequest.Marshal(b, m, deterministic)
}
func (dst *RecallCoachLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallCoachLabelRequest.Merge(dst, src)
}
func (m *RecallCoachLabelRequest) XXX_Size() int {
	return xxx_messageInfo_RecallCoachLabelRequest.Size(m)
}
func (m *RecallCoachLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallCoachLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_RecallCoachLabelRequest proto.InternalMessageInfo

func (m *RecallCoachLabelRequest) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

type RecallCoachLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RecallCoachLabelResponse) Reset()         { *m = RecallCoachLabelResponse{} }
func (m *RecallCoachLabelResponse) String() string { return proto.CompactTextString(m) }
func (*RecallCoachLabelResponse) ProtoMessage()    {}
func (*RecallCoachLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{50}
}
func (m *RecallCoachLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RecallCoachLabelResponse.Unmarshal(m, b)
}
func (m *RecallCoachLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RecallCoachLabelResponse.Marshal(b, m, deterministic)
}
func (dst *RecallCoachLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RecallCoachLabelResponse.Merge(dst, src)
}
func (m *RecallCoachLabelResponse) XXX_Size() int {
	return xxx_messageInfo_RecallCoachLabelResponse.Size(m)
}
func (m *RecallCoachLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_RecallCoachLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_RecallCoachLabelResponse proto.InternalMessageInfo

// 更新电竞标识
type UpdateCoachLabelRequest struct {
	RecordId             uint32      `protobuf:"varint,1,opt,name=record_id,json=recordId,proto3" json:"record_id,omitempty"`
	Label                *CoachLabel `protobuf:"bytes,2,opt,name=label,proto3" json:"label,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UpdateCoachLabelRequest) Reset()         { *m = UpdateCoachLabelRequest{} }
func (m *UpdateCoachLabelRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateCoachLabelRequest) ProtoMessage()    {}
func (*UpdateCoachLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{51}
}
func (m *UpdateCoachLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCoachLabelRequest.Unmarshal(m, b)
}
func (m *UpdateCoachLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCoachLabelRequest.Marshal(b, m, deterministic)
}
func (dst *UpdateCoachLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCoachLabelRequest.Merge(dst, src)
}
func (m *UpdateCoachLabelRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateCoachLabelRequest.Size(m)
}
func (m *UpdateCoachLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCoachLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCoachLabelRequest proto.InternalMessageInfo

func (m *UpdateCoachLabelRequest) GetRecordId() uint32 {
	if m != nil {
		return m.RecordId
	}
	return 0
}

func (m *UpdateCoachLabelRequest) GetLabel() *CoachLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

type UpdateCoachLabelResponse struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateCoachLabelResponse) Reset()         { *m = UpdateCoachLabelResponse{} }
func (m *UpdateCoachLabelResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateCoachLabelResponse) ProtoMessage()    {}
func (*UpdateCoachLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{52}
}
func (m *UpdateCoachLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateCoachLabelResponse.Unmarshal(m, b)
}
func (m *UpdateCoachLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateCoachLabelResponse.Marshal(b, m, deterministic)
}
func (dst *UpdateCoachLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateCoachLabelResponse.Merge(dst, src)
}
func (m *UpdateCoachLabelResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateCoachLabelResponse.Size(m)
}
func (m *UpdateCoachLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateCoachLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateCoachLabelResponse proto.InternalMessageInfo

// 检查时间冲突
type CheckTimeOverlappedRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTimeOverlappedRequest) Reset()         { *m = CheckTimeOverlappedRequest{} }
func (m *CheckTimeOverlappedRequest) String() string { return proto.CompactTextString(m) }
func (*CheckTimeOverlappedRequest) ProtoMessage()    {}
func (*CheckTimeOverlappedRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{53}
}
func (m *CheckTimeOverlappedRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTimeOverlappedRequest.Unmarshal(m, b)
}
func (m *CheckTimeOverlappedRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTimeOverlappedRequest.Marshal(b, m, deterministic)
}
func (dst *CheckTimeOverlappedRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTimeOverlappedRequest.Merge(dst, src)
}
func (m *CheckTimeOverlappedRequest) XXX_Size() int {
	return xxx_messageInfo_CheckTimeOverlappedRequest.Size(m)
}
func (m *CheckTimeOverlappedRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTimeOverlappedRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTimeOverlappedRequest proto.InternalMessageInfo

func (m *CheckTimeOverlappedRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *CheckTimeOverlappedRequest) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CheckTimeOverlappedRequest) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

type CheckTimeOverlappedResponse struct {
	ItemList             []*CheckTimeOverlappedResponse_OverlappedItem `protobuf:"bytes,1,rep,name=item_list,json=itemList,proto3" json:"item_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *CheckTimeOverlappedResponse) Reset()         { *m = CheckTimeOverlappedResponse{} }
func (m *CheckTimeOverlappedResponse) String() string { return proto.CompactTextString(m) }
func (*CheckTimeOverlappedResponse) ProtoMessage()    {}
func (*CheckTimeOverlappedResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{54}
}
func (m *CheckTimeOverlappedResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTimeOverlappedResponse.Unmarshal(m, b)
}
func (m *CheckTimeOverlappedResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTimeOverlappedResponse.Marshal(b, m, deterministic)
}
func (dst *CheckTimeOverlappedResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTimeOverlappedResponse.Merge(dst, src)
}
func (m *CheckTimeOverlappedResponse) XXX_Size() int {
	return xxx_messageInfo_CheckTimeOverlappedResponse.Size(m)
}
func (m *CheckTimeOverlappedResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTimeOverlappedResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTimeOverlappedResponse proto.InternalMessageInfo

func (m *CheckTimeOverlappedResponse) GetItemList() []*CheckTimeOverlappedResponse_OverlappedItem {
	if m != nil {
		return m.ItemList
	}
	return nil
}

type CheckTimeOverlappedResponse_OverlappedItem struct {
	Ttid                 string   `protobuf:"bytes,1,opt,name=ttid,proto3" json:"ttid,omitempty"`
	BeginTime            int64    `protobuf:"varint,2,opt,name=begin_time,json=beginTime,proto3" json:"begin_time,omitempty"`
	EndTime              int64    `protobuf:"varint,3,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckTimeOverlappedResponse_OverlappedItem) Reset() {
	*m = CheckTimeOverlappedResponse_OverlappedItem{}
}
func (m *CheckTimeOverlappedResponse_OverlappedItem) String() string {
	return proto.CompactTextString(m)
}
func (*CheckTimeOverlappedResponse_OverlappedItem) ProtoMessage() {}
func (*CheckTimeOverlappedResponse_OverlappedItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{54, 0}
}
func (m *CheckTimeOverlappedResponse_OverlappedItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckTimeOverlappedResponse_OverlappedItem.Unmarshal(m, b)
}
func (m *CheckTimeOverlappedResponse_OverlappedItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckTimeOverlappedResponse_OverlappedItem.Marshal(b, m, deterministic)
}
func (dst *CheckTimeOverlappedResponse_OverlappedItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckTimeOverlappedResponse_OverlappedItem.Merge(dst, src)
}
func (m *CheckTimeOverlappedResponse_OverlappedItem) XXX_Size() int {
	return xxx_messageInfo_CheckTimeOverlappedResponse_OverlappedItem.Size(m)
}
func (m *CheckTimeOverlappedResponse_OverlappedItem) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckTimeOverlappedResponse_OverlappedItem.DiscardUnknown(m)
}

var xxx_messageInfo_CheckTimeOverlappedResponse_OverlappedItem proto.InternalMessageInfo

func (m *CheckTimeOverlappedResponse_OverlappedItem) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *CheckTimeOverlappedResponse_OverlappedItem) GetBeginTime() int64 {
	if m != nil {
		return m.BeginTime
	}
	return 0
}

func (m *CheckTimeOverlappedResponse_OverlappedItem) GetEndTime() int64 {
	if m != nil {
		return m.EndTime
	}
	return 0
}

// 批量获取电竞指导标签
type BatchGetCoachLabelRequest struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCoachLabelRequest) Reset()         { *m = BatchGetCoachLabelRequest{} }
func (m *BatchGetCoachLabelRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachLabelRequest) ProtoMessage()    {}
func (*BatchGetCoachLabelRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{55}
}
func (m *BatchGetCoachLabelRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachLabelRequest.Unmarshal(m, b)
}
func (m *BatchGetCoachLabelRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachLabelRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachLabelRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachLabelRequest.Merge(dst, src)
}
func (m *BatchGetCoachLabelRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachLabelRequest.Size(m)
}
func (m *BatchGetCoachLabelRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachLabelRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachLabelRequest proto.InternalMessageInfo

func (m *BatchGetCoachLabelRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type BatchGetCoachLabelResponse struct {
	CoachLabelMap        map[uint32]*CoachLabel `protobuf:"bytes,1,rep,name=coach_label_map,json=coachLabelMap,proto3" json:"coach_label_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetCoachLabelResponse) Reset()         { *m = BatchGetCoachLabelResponse{} }
func (m *BatchGetCoachLabelResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachLabelResponse) ProtoMessage()    {}
func (*BatchGetCoachLabelResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{56}
}
func (m *BatchGetCoachLabelResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachLabelResponse.Unmarshal(m, b)
}
func (m *BatchGetCoachLabelResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachLabelResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachLabelResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachLabelResponse.Merge(dst, src)
}
func (m *BatchGetCoachLabelResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachLabelResponse.Size(m)
}
func (m *BatchGetCoachLabelResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachLabelResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachLabelResponse proto.InternalMessageInfo

func (m *BatchGetCoachLabelResponse) GetCoachLabelMap() map[uint32]*CoachLabel {
	if m != nil {
		return m.CoachLabelMap
	}
	return nil
}

type BatchGetCoachInfoByTTidRequest struct {
	TtidList             []string `protobuf:"bytes,1,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCoachInfoByTTidRequest) Reset()         { *m = BatchGetCoachInfoByTTidRequest{} }
func (m *BatchGetCoachInfoByTTidRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachInfoByTTidRequest) ProtoMessage()    {}
func (*BatchGetCoachInfoByTTidRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{57}
}
func (m *BatchGetCoachInfoByTTidRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachInfoByTTidRequest.Unmarshal(m, b)
}
func (m *BatchGetCoachInfoByTTidRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachInfoByTTidRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachInfoByTTidRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachInfoByTTidRequest.Merge(dst, src)
}
func (m *BatchGetCoachInfoByTTidRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachInfoByTTidRequest.Size(m)
}
func (m *BatchGetCoachInfoByTTidRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachInfoByTTidRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachInfoByTTidRequest proto.InternalMessageInfo

func (m *BatchGetCoachInfoByTTidRequest) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

type BatchGetCoachInfoByTTidResponse struct {
	InvalidIdList        []string                                              `protobuf:"bytes,1,rep,name=invalid_id_list,json=invalidIdList,proto3" json:"invalid_id_list,omitempty"`
	NotCoachList         []string                                              `protobuf:"bytes,2,rep,name=not_coach_list,json=notCoachList,proto3" json:"not_coach_list,omitempty"`
	InfoMap              map[string]*BatchGetCoachInfoByTTidResponse_CoachInfo `protobuf:"bytes,3,rep,name=info_map,json=infoMap,proto3" json:"info_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                              `json:"-"`
	XXX_unrecognized     []byte                                                `json:"-"`
	XXX_sizecache        int32                                                 `json:"-"`
}

func (m *BatchGetCoachInfoByTTidResponse) Reset()         { *m = BatchGetCoachInfoByTTidResponse{} }
func (m *BatchGetCoachInfoByTTidResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetCoachInfoByTTidResponse) ProtoMessage()    {}
func (*BatchGetCoachInfoByTTidResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{58}
}
func (m *BatchGetCoachInfoByTTidResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachInfoByTTidResponse.Unmarshal(m, b)
}
func (m *BatchGetCoachInfoByTTidResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachInfoByTTidResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachInfoByTTidResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachInfoByTTidResponse.Merge(dst, src)
}
func (m *BatchGetCoachInfoByTTidResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachInfoByTTidResponse.Size(m)
}
func (m *BatchGetCoachInfoByTTidResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachInfoByTTidResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachInfoByTTidResponse proto.InternalMessageInfo

func (m *BatchGetCoachInfoByTTidResponse) GetInvalidIdList() []string {
	if m != nil {
		return m.InvalidIdList
	}
	return nil
}

func (m *BatchGetCoachInfoByTTidResponse) GetNotCoachList() []string {
	if m != nil {
		return m.NotCoachList
	}
	return nil
}

func (m *BatchGetCoachInfoByTTidResponse) GetInfoMap() map[string]*BatchGetCoachInfoByTTidResponse_CoachInfo {
	if m != nil {
		return m.InfoMap
	}
	return nil
}

type BatchGetCoachInfoByTTidResponse_CoachInfo struct {
	EsportRole           uint32   `protobuf:"varint,1,opt,name=esport_role,json=esportRole,proto3" json:"esport_role,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	NickName             string   `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) Reset() {
	*m = BatchGetCoachInfoByTTidResponse_CoachInfo{}
}
func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetCoachInfoByTTidResponse_CoachInfo) ProtoMessage() {}
func (*BatchGetCoachInfoByTTidResponse_CoachInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{58, 0}
}
func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetCoachInfoByTTidResponse_CoachInfo.Unmarshal(m, b)
}
func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetCoachInfoByTTidResponse_CoachInfo.Marshal(b, m, deterministic)
}
func (dst *BatchGetCoachInfoByTTidResponse_CoachInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetCoachInfoByTTidResponse_CoachInfo.Merge(dst, src)
}
func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) XXX_Size() int {
	return xxx_messageInfo_BatchGetCoachInfoByTTidResponse_CoachInfo.Size(m)
}
func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetCoachInfoByTTidResponse_CoachInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetCoachInfoByTTidResponse_CoachInfo proto.InternalMessageInfo

func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) GetEsportRole() uint32 {
	if m != nil {
		return m.EsportRole
	}
	return 0
}

func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetCoachInfoByTTidResponse_CoachInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

type GetCoachByGuildReq struct {
	Page                 int32    `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 int32    `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	CoachUid             uint32   `protobuf:"varint,4,opt,name=coach_uid,json=coachUid,proto3" json:"coach_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachByGuildReq) Reset()         { *m = GetCoachByGuildReq{} }
func (m *GetCoachByGuildReq) String() string { return proto.CompactTextString(m) }
func (*GetCoachByGuildReq) ProtoMessage()    {}
func (*GetCoachByGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{59}
}
func (m *GetCoachByGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachByGuildReq.Unmarshal(m, b)
}
func (m *GetCoachByGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachByGuildReq.Marshal(b, m, deterministic)
}
func (dst *GetCoachByGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachByGuildReq.Merge(dst, src)
}
func (m *GetCoachByGuildReq) XXX_Size() int {
	return xxx_messageInfo_GetCoachByGuildReq.Size(m)
}
func (m *GetCoachByGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachByGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachByGuildReq proto.InternalMessageInfo

func (m *GetCoachByGuildReq) GetPage() int32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetCoachByGuildReq) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *GetCoachByGuildReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetCoachByGuildReq) GetCoachUid() uint32 {
	if m != nil {
		return m.CoachUid
	}
	return 0
}

type GetCoachByGuildResp struct {
	CoachUids            []uint32 `protobuf:"varint,1,rep,packed,name=coach_uids,json=coachUids,proto3" json:"coach_uids,omitempty"`
	Total                int32    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetCoachByGuildResp) Reset()         { *m = GetCoachByGuildResp{} }
func (m *GetCoachByGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetCoachByGuildResp) ProtoMessage()    {}
func (*GetCoachByGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{60}
}
func (m *GetCoachByGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCoachByGuildResp.Unmarshal(m, b)
}
func (m *GetCoachByGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCoachByGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetCoachByGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCoachByGuildResp.Merge(dst, src)
}
func (m *GetCoachByGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetCoachByGuildResp.Size(m)
}
func (m *GetCoachByGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCoachByGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCoachByGuildResp proto.InternalMessageInfo

func (m *GetCoachByGuildResp) GetCoachUids() []uint32 {
	if m != nil {
		return m.CoachUids
	}
	return nil
}

func (m *GetCoachByGuildResp) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type BatchGetGuildIdByCoachUidsRequest struct {
	CoachUids            []uint32 `protobuf:"varint,1,rep,packed,name=coach_uids,json=coachUids,proto3" json:"coach_uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetGuildIdByCoachUidsRequest) Reset()         { *m = BatchGetGuildIdByCoachUidsRequest{} }
func (m *BatchGetGuildIdByCoachUidsRequest) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildIdByCoachUidsRequest) ProtoMessage()    {}
func (*BatchGetGuildIdByCoachUidsRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{61}
}
func (m *BatchGetGuildIdByCoachUidsRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGuildIdByCoachUidsRequest.Unmarshal(m, b)
}
func (m *BatchGetGuildIdByCoachUidsRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGuildIdByCoachUidsRequest.Marshal(b, m, deterministic)
}
func (dst *BatchGetGuildIdByCoachUidsRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGuildIdByCoachUidsRequest.Merge(dst, src)
}
func (m *BatchGetGuildIdByCoachUidsRequest) XXX_Size() int {
	return xxx_messageInfo_BatchGetGuildIdByCoachUidsRequest.Size(m)
}
func (m *BatchGetGuildIdByCoachUidsRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGuildIdByCoachUidsRequest.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGuildIdByCoachUidsRequest proto.InternalMessageInfo

func (m *BatchGetGuildIdByCoachUidsRequest) GetCoachUids() []uint32 {
	if m != nil {
		return m.CoachUids
	}
	return nil
}

type BatchGetGuildIdByCoachUidsResponse struct {
	CoachUidGuildIdMap   map[uint32]uint32 `protobuf:"bytes,1,rep,name=coach_uid_guild_id_map,json=coachUidGuildIdMap,proto3" json:"coach_uid_guild_id_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetGuildIdByCoachUidsResponse) Reset()         { *m = BatchGetGuildIdByCoachUidsResponse{} }
func (m *BatchGetGuildIdByCoachUidsResponse) String() string { return proto.CompactTextString(m) }
func (*BatchGetGuildIdByCoachUidsResponse) ProtoMessage()    {}
func (*BatchGetGuildIdByCoachUidsResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{62}
}
func (m *BatchGetGuildIdByCoachUidsResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetGuildIdByCoachUidsResponse.Unmarshal(m, b)
}
func (m *BatchGetGuildIdByCoachUidsResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetGuildIdByCoachUidsResponse.Marshal(b, m, deterministic)
}
func (dst *BatchGetGuildIdByCoachUidsResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetGuildIdByCoachUidsResponse.Merge(dst, src)
}
func (m *BatchGetGuildIdByCoachUidsResponse) XXX_Size() int {
	return xxx_messageInfo_BatchGetGuildIdByCoachUidsResponse.Size(m)
}
func (m *BatchGetGuildIdByCoachUidsResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetGuildIdByCoachUidsResponse.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetGuildIdByCoachUidsResponse proto.InternalMessageInfo

func (m *BatchGetGuildIdByCoachUidsResponse) GetCoachUidGuildIdMap() map[uint32]uint32 {
	if m != nil {
		return m.CoachUidGuildIdMap
	}
	return nil
}

type GetLarkPoolRequest struct {
	// 由于每个池子可能有上万个，因此不做批量请求
	PoolTitle            string   `protobuf:"bytes,1,opt,name=pool_title,json=poolTitle,proto3" json:"pool_title,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLarkPoolRequest) Reset()         { *m = GetLarkPoolRequest{} }
func (m *GetLarkPoolRequest) String() string { return proto.CompactTextString(m) }
func (*GetLarkPoolRequest) ProtoMessage()    {}
func (*GetLarkPoolRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{63}
}
func (m *GetLarkPoolRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLarkPoolRequest.Unmarshal(m, b)
}
func (m *GetLarkPoolRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLarkPoolRequest.Marshal(b, m, deterministic)
}
func (dst *GetLarkPoolRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLarkPoolRequest.Merge(dst, src)
}
func (m *GetLarkPoolRequest) XXX_Size() int {
	return xxx_messageInfo_GetLarkPoolRequest.Size(m)
}
func (m *GetLarkPoolRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLarkPoolRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetLarkPoolRequest proto.InternalMessageInfo

func (m *GetLarkPoolRequest) GetPoolTitle() string {
	if m != nil {
		return m.PoolTitle
	}
	return ""
}

type GetLarkPoolResponse struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetLarkPoolResponse) Reset()         { *m = GetLarkPoolResponse{} }
func (m *GetLarkPoolResponse) String() string { return proto.CompactTextString(m) }
func (*GetLarkPoolResponse) ProtoMessage()    {}
func (*GetLarkPoolResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{64}
}
func (m *GetLarkPoolResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetLarkPoolResponse.Unmarshal(m, b)
}
func (m *GetLarkPoolResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetLarkPoolResponse.Marshal(b, m, deterministic)
}
func (dst *GetLarkPoolResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetLarkPoolResponse.Merge(dst, src)
}
func (m *GetLarkPoolResponse) XXX_Size() int {
	return xxx_messageInfo_GetLarkPoolResponse.Size(m)
}
func (m *GetLarkPoolResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetLarkPoolResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetLarkPoolResponse proto.InternalMessageInfo

func (m *GetLarkPoolResponse) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CheckInLarkPoolRequest struct {
	PoolTitle            string   `protobuf:"bytes,1,opt,name=pool_title,json=poolTitle,proto3" json:"pool_title,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckInLarkPoolRequest) Reset()         { *m = CheckInLarkPoolRequest{} }
func (m *CheckInLarkPoolRequest) String() string { return proto.CompactTextString(m) }
func (*CheckInLarkPoolRequest) ProtoMessage()    {}
func (*CheckInLarkPoolRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{65}
}
func (m *CheckInLarkPoolRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInLarkPoolRequest.Unmarshal(m, b)
}
func (m *CheckInLarkPoolRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInLarkPoolRequest.Marshal(b, m, deterministic)
}
func (dst *CheckInLarkPoolRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInLarkPoolRequest.Merge(dst, src)
}
func (m *CheckInLarkPoolRequest) XXX_Size() int {
	return xxx_messageInfo_CheckInLarkPoolRequest.Size(m)
}
func (m *CheckInLarkPoolRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInLarkPoolRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInLarkPoolRequest proto.InternalMessageInfo

func (m *CheckInLarkPoolRequest) GetPoolTitle() string {
	if m != nil {
		return m.PoolTitle
	}
	return ""
}

func (m *CheckInLarkPoolRequest) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type CheckInLarkPoolResponse struct {
	ResultMap            map[uint32]bool `protobuf:"bytes,1,rep,name=result_map,json=resultMap,proto3" json:"result_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *CheckInLarkPoolResponse) Reset()         { *m = CheckInLarkPoolResponse{} }
func (m *CheckInLarkPoolResponse) String() string { return proto.CompactTextString(m) }
func (*CheckInLarkPoolResponse) ProtoMessage()    {}
func (*CheckInLarkPoolResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_esport_role_f48903801ad7e4f6, []int{66}
}
func (m *CheckInLarkPoolResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckInLarkPoolResponse.Unmarshal(m, b)
}
func (m *CheckInLarkPoolResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckInLarkPoolResponse.Marshal(b, m, deterministic)
}
func (dst *CheckInLarkPoolResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckInLarkPoolResponse.Merge(dst, src)
}
func (m *CheckInLarkPoolResponse) XXX_Size() int {
	return xxx_messageInfo_CheckInLarkPoolResponse.Size(m)
}
func (m *CheckInLarkPoolResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckInLarkPoolResponse.DiscardUnknown(m)
}

var xxx_messageInfo_CheckInLarkPoolResponse proto.InternalMessageInfo

func (m *CheckInLarkPoolResponse) GetResultMap() map[uint32]bool {
	if m != nil {
		return m.ResultMap
	}
	return nil
}

func init() {
	proto.RegisterType((*ESportGuildAuditResultReq)(nil), "esport_role.ESportGuildAuditResultReq")
	proto.RegisterType((*ESportGuildAuditResultResp)(nil), "esport_role.ESportGuildAuditResultResp")
	proto.RegisterType((*ESportRiskAuditResultReq)(nil), "esport_role.ESportRiskAuditResultReq")
	proto.RegisterType((*ESportRiskAuditResultResp)(nil), "esport_role.ESportRiskAuditResultResp")
	proto.RegisterType((*ApplyESportRequset)(nil), "esport_role.ApplyESportRequset")
	proto.RegisterType((*ApplyESportResponse)(nil), "esport_role.ApplyESportResponse")
	proto.RegisterType((*GetUserESportRoleReq)(nil), "esport_role.GetUserESportRoleReq")
	proto.RegisterType((*GetUserESportRoleResp)(nil), "esport_role.GetUserESportRoleResp")
	proto.RegisterType((*BatchGetUserESportRoleReq)(nil), "esport_role.BatchGetUserESportRoleReq")
	proto.RegisterType((*UserESportRole)(nil), "esport_role.UserESportRole")
	proto.RegisterType((*BatchGetUserESportRoleResp)(nil), "esport_role.BatchGetUserESportRoleResp")
	proto.RegisterType((*ManualAddUserESportReq)(nil), "esport_role.ManualAddUserESportReq")
	proto.RegisterType((*ManualAddUserESportResp)(nil), "esport_role.ManualAddUserESportResp")
	proto.RegisterType((*ApplyESportRecord)(nil), "esport_role.ApplyESportRecord")
	proto.RegisterType((*GetApplyESportRecordReq)(nil), "esport_role.GetApplyESportRecordReq")
	proto.RegisterType((*GetApplyESportRecordResp)(nil), "esport_role.GetApplyESportRecordResp")
	proto.RegisterType((*OfficialHandleApplyESportReq)(nil), "esport_role.OfficialHandleApplyESportReq")
	proto.RegisterType((*OfficialHandleApplyESportResp)(nil), "esport_role.OfficialHandleApplyESportResp")
	proto.RegisterType((*ApplyBlackList)(nil), "esport_role.ApplyBlackList")
	proto.RegisterType((*GetApplyBlackListReq)(nil), "esport_role.GetApplyBlackListReq")
	proto.RegisterType((*GetApplyBlackListResp)(nil), "esport_role.GetApplyBlackListResp")
	proto.RegisterType((*UpdateApplyBlacklistFreezeTimeReq)(nil), "esport_role.UpdateApplyBlacklistFreezeTimeReq")
	proto.RegisterType((*UpdateApplyBlacklistFreezeTimeResp)(nil), "esport_role.UpdateApplyBlacklistFreezeTimeResp")
	proto.RegisterType((*SignTimeInfo)(nil), "esport_role.SignTimeInfo")
	proto.RegisterType((*ESportErInfo)(nil), "esport_role.ESportErInfo")
	proto.RegisterType((*GetESportErInfoListReq)(nil), "esport_role.GetESportErInfoListReq")
	proto.RegisterType((*GetESportErInfoListResp)(nil), "esport_role.GetESportErInfoListResp")
	proto.RegisterType((*ESportRoleInfo)(nil), "esport_role.ESportRoleInfo")
	proto.RegisterType((*GetAllESportRoleReq)(nil), "esport_role.GetAllESportRoleReq")
	proto.RegisterType((*GetAllESportRoleResp)(nil), "esport_role.GetAllESportRoleResp")
	proto.RegisterType((*ReclaimInfo)(nil), "esport_role.ReclaimInfo")
	proto.RegisterType((*ReclaimESportErIdentityReq)(nil), "esport_role.ReclaimESportErIdentityReq")
	proto.RegisterType((*ReclaimESportErIdentityResp)(nil), "esport_role.ReclaimESportErIdentityResp")
	proto.RegisterType((*GetReclaimInfoListReq)(nil), "esport_role.GetReclaimInfoListReq")
	proto.RegisterType((*GetReclaimInfoListResp)(nil), "esport_role.GetReclaimInfoListResp")
	proto.RegisterType((*BatCheckUserESportRoleReq)(nil), "esport_role.BatCheckUserESportRoleReq")
	proto.RegisterType((*BatCheckUserESportRoleResp)(nil), "esport_role.BatCheckUserESportRoleResp")
	proto.RegisterType((*CheckUserApplyESportReq)(nil), "esport_role.CheckUserApplyESportReq")
	proto.RegisterType((*CheckUserApplyESportResp)(nil), "esport_role.CheckUserApplyESportResp")
	proto.RegisterType((*GetBlackListHandleRecordReq)(nil), "esport_role.GetBlackListHandleRecordReq")
	proto.RegisterType((*BlackListHandleInfo)(nil), "esport_role.BlackListHandleInfo")
	proto.RegisterType((*GetBlackListHandleRecordResp)(nil), "esport_role.GetBlackListHandleRecordResp")
	proto.RegisterType((*CheckSignGuildRiskAuditingReq)(nil), "esport_role.CheckSignGuildRiskAuditingReq")
	proto.RegisterType((*CheckSignGuildRiskAuditingResp)(nil), "esport_role.CheckSignGuildRiskAuditingResp")
	proto.RegisterType((*CoachLabel)(nil), "esport_role.CoachLabel")
	proto.RegisterType((*BatchGrantCoachLabelRequest)(nil), "esport_role.BatchGrantCoachLabelRequest")
	proto.RegisterType((*BatchGrantCoachLabelResponse)(nil), "esport_role.BatchGrantCoachLabelResponse")
	proto.RegisterType((*GetCoachLabelRecordByPageRequest)(nil), "esport_role.GetCoachLabelRecordByPageRequest")
	proto.RegisterType((*GetCoachLabelRecordByPageResponse)(nil), "esport_role.GetCoachLabelRecordByPageResponse")
	proto.RegisterType((*GetCoachLabelRecordByPageResponse_CoachLabelRecord)(nil), "esport_role.GetCoachLabelRecordByPageResponse.CoachLabelRecord")
	proto.RegisterType((*RecallCoachLabelRequest)(nil), "esport_role.RecallCoachLabelRequest")
	proto.RegisterType((*RecallCoachLabelResponse)(nil), "esport_role.RecallCoachLabelResponse")
	proto.RegisterType((*UpdateCoachLabelRequest)(nil), "esport_role.UpdateCoachLabelRequest")
	proto.RegisterType((*UpdateCoachLabelResponse)(nil), "esport_role.UpdateCoachLabelResponse")
	proto.RegisterType((*CheckTimeOverlappedRequest)(nil), "esport_role.CheckTimeOverlappedRequest")
	proto.RegisterType((*CheckTimeOverlappedResponse)(nil), "esport_role.CheckTimeOverlappedResponse")
	proto.RegisterType((*CheckTimeOverlappedResponse_OverlappedItem)(nil), "esport_role.CheckTimeOverlappedResponse.OverlappedItem")
	proto.RegisterType((*BatchGetCoachLabelRequest)(nil), "esport_role.BatchGetCoachLabelRequest")
	proto.RegisterType((*BatchGetCoachLabelResponse)(nil), "esport_role.BatchGetCoachLabelResponse")
	proto.RegisterMapType((map[uint32]*CoachLabel)(nil), "esport_role.BatchGetCoachLabelResponse.CoachLabelMapEntry")
	proto.RegisterType((*BatchGetCoachInfoByTTidRequest)(nil), "esport_role.BatchGetCoachInfoByTTidRequest")
	proto.RegisterType((*BatchGetCoachInfoByTTidResponse)(nil), "esport_role.BatchGetCoachInfoByTTidResponse")
	proto.RegisterMapType((map[string]*BatchGetCoachInfoByTTidResponse_CoachInfo)(nil), "esport_role.BatchGetCoachInfoByTTidResponse.InfoMapEntry")
	proto.RegisterType((*BatchGetCoachInfoByTTidResponse_CoachInfo)(nil), "esport_role.BatchGetCoachInfoByTTidResponse.CoachInfo")
	proto.RegisterType((*GetCoachByGuildReq)(nil), "esport_role.GetCoachByGuildReq")
	proto.RegisterType((*GetCoachByGuildResp)(nil), "esport_role.GetCoachByGuildResp")
	proto.RegisterType((*BatchGetGuildIdByCoachUidsRequest)(nil), "esport_role.BatchGetGuildIdByCoachUidsRequest")
	proto.RegisterType((*BatchGetGuildIdByCoachUidsResponse)(nil), "esport_role.BatchGetGuildIdByCoachUidsResponse")
	proto.RegisterMapType((map[uint32]uint32)(nil), "esport_role.BatchGetGuildIdByCoachUidsResponse.CoachUidGuildIdMapEntry")
	proto.RegisterType((*GetLarkPoolRequest)(nil), "esport_role.GetLarkPoolRequest")
	proto.RegisterType((*GetLarkPoolResponse)(nil), "esport_role.GetLarkPoolResponse")
	proto.RegisterType((*CheckInLarkPoolRequest)(nil), "esport_role.CheckInLarkPoolRequest")
	proto.RegisterType((*CheckInLarkPoolResponse)(nil), "esport_role.CheckInLarkPoolResponse")
	proto.RegisterMapType((map[uint32]bool)(nil), "esport_role.CheckInLarkPoolResponse.ResultMapEntry")
	proto.RegisterEnum("esport_role.ESportErType", ESportErType_name, ESportErType_value)
	proto.RegisterEnum("esport_role.ApplyESportAuditType", ApplyESportAuditType_name, ApplyESportAuditType_value)
	proto.RegisterEnum("esport_role.QueryType", QueryType_name, QueryType_value)
	proto.RegisterEnum("esport_role.LabelSourceType", LabelSourceType_name, LabelSourceType_value)
	proto.RegisterEnum("esport_role.CoachLabelStatus", CoachLabelStatus_name, CoachLabelStatus_value)
	proto.RegisterEnum("esport_role.ApplyESportRequset_ESportApplyType", ApplyESportRequset_ESportApplyType_name, ApplyESportRequset_ESportApplyType_value)
	proto.RegisterEnum("esport_role.UpdateApplyBlacklistFreezeTimeReq_OpType", UpdateApplyBlacklistFreezeTimeReq_OpType_name, UpdateApplyBlacklistFreezeTimeReq_OpType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ESportRoleClient is the client API for ESportRole service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ESportRoleClient interface {
	// 工会审核结果通知
	ESportGuildAuditResult(ctx context.Context, in *ESportGuildAuditResultReq, opts ...grpc.CallOption) (*ESportGuildAuditResultResp, error)
	// 提交审核申请
	ApplyESportRequest(ctx context.Context, in *ApplyESportRequset, opts ...grpc.CallOption) (*ApplyESportResponse, error)
	// 根据uid查询用户电竞指导身份
	GetUserESportRole(ctx context.Context, in *GetUserESportRoleReq, opts ...grpc.CallOption) (*GetUserESportRoleResp, error)
	// T盾风控结果通知
	ESportRiskAuditResult(ctx context.Context, in *ESportRiskAuditResultReq, opts ...grpc.CallOption) (*ESportRiskAuditResultResp, error)
	// 批量查询用户电竞指导身份
	BatchGetUserESportRole(ctx context.Context, in *BatchGetUserESportRoleReq, opts ...grpc.CallOption) (*BatchGetUserESportRoleResp, error)
	// 手动新增用户身份信息
	ManualAddUserESport(ctx context.Context, in *ManualAddUserESportReq, opts ...grpc.CallOption) (*ManualAddUserESportResp, error)
	// 检查用户是否已提交申请
	CheckUserApplyESport(ctx context.Context, in *CheckUserApplyESportReq, opts ...grpc.CallOption) (*CheckUserApplyESportResp, error)
	// 检查用户时候有在内审中的签约工会申请
	CheckSignGuildRiskAuditing(ctx context.Context, in *CheckSignGuildRiskAuditingReq, opts ...grpc.CallOption) (*CheckSignGuildRiskAuditingResp, error)
	// =================== 审核后台接口 ===================
	// 获取电竞指导身份申请记录
	GetESportAuditList(ctx context.Context, in *GetApplyESportRecordReq, opts ...grpc.CallOption) (*GetApplyESportRecordResp, error)
	// 批量审核电指导身份申请
	OfficialHandleApplyESport(ctx context.Context, in *OfficialHandleApplyESportReq, opts ...grpc.CallOption) (*OfficialHandleApplyESportResp, error)
	// 分页获取电竞指导信息
	GetESportErInfoList(ctx context.Context, in *GetESportErInfoListReq, opts ...grpc.CallOption) (*GetESportErInfoListResp, error)
	// 分页获取所有电竞指导信息
	GetAllESportRole(ctx context.Context, in *GetAllESportRoleReq, opts ...grpc.CallOption) (*GetAllESportRoleResp, error)
	// 批量回收电竞指导身份
	ReclaimESportErIdentity(ctx context.Context, in *ReclaimESportErIdentityReq, opts ...grpc.CallOption) (*ReclaimESportErIdentityResp, error)
	// 获取黑名单管理记录
	GetApplyBlackList(ctx context.Context, in *GetApplyBlackListReq, opts ...grpc.CallOption) (*GetApplyBlackListResp, error)
	// 修改身份申请黑名单冻结时间
	UpdateApplyBlacklistFreezeTime(ctx context.Context, in *UpdateApplyBlacklistFreezeTimeReq, opts ...grpc.CallOption) (*UpdateApplyBlacklistFreezeTimeResp, error)
	// 获取身份回收记录
	GetReclaimInfoList(ctx context.Context, in *GetReclaimInfoListReq, opts ...grpc.CallOption) (*GetReclaimInfoListResp, error)
	// 判断用户是否拥有电竞指导身份
	BatCheckUserESportRole(ctx context.Context, in *BatCheckUserESportRoleReq, opts ...grpc.CallOption) (*BatCheckUserESportRoleResp, error)
	// 获取黑名单操作记录  【设为永久/移除】的记录
	GetBlackListHandleRecord(ctx context.Context, in *GetBlackListHandleRecordReq, opts ...grpc.CallOption) (*GetBlackListHandleRecordResp, error)
	// =============================== 电竞标识 ================================
	// 批量下发电竞标识
	BatchGrantCoachLabel(ctx context.Context, in *BatchGrantCoachLabelRequest, opts ...grpc.CallOption) (*BatchGrantCoachLabelResponse, error)
	// 分页获取电竞标识下发记录
	GetCoachLabelRecordByPage(ctx context.Context, in *GetCoachLabelRecordByPageRequest, opts ...grpc.CallOption) (*GetCoachLabelRecordByPageResponse, error)
	// 收回电竞标识
	RecallCoachLabel(ctx context.Context, in *RecallCoachLabelRequest, opts ...grpc.CallOption) (*RecallCoachLabelResponse, error)
	// 更新电竞标识
	UpdateCoachLabel(ctx context.Context, in *UpdateCoachLabelRequest, opts ...grpc.CallOption) (*UpdateCoachLabelResponse, error)
	// 检查时间冲突
	CheckTimeOverlapped(ctx context.Context, in *CheckTimeOverlappedRequest, opts ...grpc.CallOption) (*CheckTimeOverlappedResponse, error)
	// 批量获取用户电竞指导标识
	BatchGetCoachLabel(ctx context.Context, in *BatchGetCoachLabelRequest, opts ...grpc.CallOption) (*BatchGetCoachLabelResponse, error)
	// CheckCoachLabelTTid 检查ttid能否下发电竞标识
	BatchGetCoachInfoByTTid(ctx context.Context, in *BatchGetCoachInfoByTTidRequest, opts ...grpc.CallOption) (*BatchGetCoachInfoByTTidResponse, error)
	// 分页查询指定公会旗下的电竞大神
	GetCoachByGuild(ctx context.Context, in *GetCoachByGuildReq, opts ...grpc.CallOption) (*GetCoachByGuildResp, error)
	// 批量根据大神的id获取对应的公会id
	BatchGetGuildIdByCoachUids(ctx context.Context, in *BatchGetGuildIdByCoachUidsRequest, opts ...grpc.CallOption) (*BatchGetGuildIdByCoachUidsResponse, error)
	// 获取飞书池子
	GetLarkPool(ctx context.Context, in *GetLarkPoolRequest, opts ...grpc.CallOption) (*GetLarkPoolResponse, error)
	// 检查是否在飞书池子中
	CheckInLarkPool(ctx context.Context, in *CheckInLarkPoolRequest, opts ...grpc.CallOption) (*CheckInLarkPoolResponse, error)
}

type eSportRoleClient struct {
	cc *grpc.ClientConn
}

func NewESportRoleClient(cc *grpc.ClientConn) ESportRoleClient {
	return &eSportRoleClient{cc}
}

func (c *eSportRoleClient) ESportGuildAuditResult(ctx context.Context, in *ESportGuildAuditResultReq, opts ...grpc.CallOption) (*ESportGuildAuditResultResp, error) {
	out := new(ESportGuildAuditResultResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/ESportGuildAuditResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) ApplyESportRequest(ctx context.Context, in *ApplyESportRequset, opts ...grpc.CallOption) (*ApplyESportResponse, error) {
	out := new(ApplyESportResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/ApplyESportRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetUserESportRole(ctx context.Context, in *GetUserESportRoleReq, opts ...grpc.CallOption) (*GetUserESportRoleResp, error) {
	out := new(GetUserESportRoleResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetUserESportRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) ESportRiskAuditResult(ctx context.Context, in *ESportRiskAuditResultReq, opts ...grpc.CallOption) (*ESportRiskAuditResultResp, error) {
	out := new(ESportRiskAuditResultResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/ESportRiskAuditResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) BatchGetUserESportRole(ctx context.Context, in *BatchGetUserESportRoleReq, opts ...grpc.CallOption) (*BatchGetUserESportRoleResp, error) {
	out := new(BatchGetUserESportRoleResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/BatchGetUserESportRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) ManualAddUserESport(ctx context.Context, in *ManualAddUserESportReq, opts ...grpc.CallOption) (*ManualAddUserESportResp, error) {
	out := new(ManualAddUserESportResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/ManualAddUserESport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) CheckUserApplyESport(ctx context.Context, in *CheckUserApplyESportReq, opts ...grpc.CallOption) (*CheckUserApplyESportResp, error) {
	out := new(CheckUserApplyESportResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/CheckUserApplyESport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) CheckSignGuildRiskAuditing(ctx context.Context, in *CheckSignGuildRiskAuditingReq, opts ...grpc.CallOption) (*CheckSignGuildRiskAuditingResp, error) {
	out := new(CheckSignGuildRiskAuditingResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/CheckSignGuildRiskAuditing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetESportAuditList(ctx context.Context, in *GetApplyESportRecordReq, opts ...grpc.CallOption) (*GetApplyESportRecordResp, error) {
	out := new(GetApplyESportRecordResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetESportAuditList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) OfficialHandleApplyESport(ctx context.Context, in *OfficialHandleApplyESportReq, opts ...grpc.CallOption) (*OfficialHandleApplyESportResp, error) {
	out := new(OfficialHandleApplyESportResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/OfficialHandleApplyESport", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetESportErInfoList(ctx context.Context, in *GetESportErInfoListReq, opts ...grpc.CallOption) (*GetESportErInfoListResp, error) {
	out := new(GetESportErInfoListResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetESportErInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetAllESportRole(ctx context.Context, in *GetAllESportRoleReq, opts ...grpc.CallOption) (*GetAllESportRoleResp, error) {
	out := new(GetAllESportRoleResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetAllESportRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) ReclaimESportErIdentity(ctx context.Context, in *ReclaimESportErIdentityReq, opts ...grpc.CallOption) (*ReclaimESportErIdentityResp, error) {
	out := new(ReclaimESportErIdentityResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/ReclaimESportErIdentity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetApplyBlackList(ctx context.Context, in *GetApplyBlackListReq, opts ...grpc.CallOption) (*GetApplyBlackListResp, error) {
	out := new(GetApplyBlackListResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetApplyBlackList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) UpdateApplyBlacklistFreezeTime(ctx context.Context, in *UpdateApplyBlacklistFreezeTimeReq, opts ...grpc.CallOption) (*UpdateApplyBlacklistFreezeTimeResp, error) {
	out := new(UpdateApplyBlacklistFreezeTimeResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/UpdateApplyBlacklistFreezeTime", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetReclaimInfoList(ctx context.Context, in *GetReclaimInfoListReq, opts ...grpc.CallOption) (*GetReclaimInfoListResp, error) {
	out := new(GetReclaimInfoListResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetReclaimInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) BatCheckUserESportRole(ctx context.Context, in *BatCheckUserESportRoleReq, opts ...grpc.CallOption) (*BatCheckUserESportRoleResp, error) {
	out := new(BatCheckUserESportRoleResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/BatCheckUserESportRole", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetBlackListHandleRecord(ctx context.Context, in *GetBlackListHandleRecordReq, opts ...grpc.CallOption) (*GetBlackListHandleRecordResp, error) {
	out := new(GetBlackListHandleRecordResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetBlackListHandleRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) BatchGrantCoachLabel(ctx context.Context, in *BatchGrantCoachLabelRequest, opts ...grpc.CallOption) (*BatchGrantCoachLabelResponse, error) {
	out := new(BatchGrantCoachLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/BatchGrantCoachLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetCoachLabelRecordByPage(ctx context.Context, in *GetCoachLabelRecordByPageRequest, opts ...grpc.CallOption) (*GetCoachLabelRecordByPageResponse, error) {
	out := new(GetCoachLabelRecordByPageResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetCoachLabelRecordByPage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) RecallCoachLabel(ctx context.Context, in *RecallCoachLabelRequest, opts ...grpc.CallOption) (*RecallCoachLabelResponse, error) {
	out := new(RecallCoachLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/RecallCoachLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) UpdateCoachLabel(ctx context.Context, in *UpdateCoachLabelRequest, opts ...grpc.CallOption) (*UpdateCoachLabelResponse, error) {
	out := new(UpdateCoachLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/UpdateCoachLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) CheckTimeOverlapped(ctx context.Context, in *CheckTimeOverlappedRequest, opts ...grpc.CallOption) (*CheckTimeOverlappedResponse, error) {
	out := new(CheckTimeOverlappedResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/CheckTimeOverlapped", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) BatchGetCoachLabel(ctx context.Context, in *BatchGetCoachLabelRequest, opts ...grpc.CallOption) (*BatchGetCoachLabelResponse, error) {
	out := new(BatchGetCoachLabelResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/BatchGetCoachLabel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) BatchGetCoachInfoByTTid(ctx context.Context, in *BatchGetCoachInfoByTTidRequest, opts ...grpc.CallOption) (*BatchGetCoachInfoByTTidResponse, error) {
	out := new(BatchGetCoachInfoByTTidResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/BatchGetCoachInfoByTTid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetCoachByGuild(ctx context.Context, in *GetCoachByGuildReq, opts ...grpc.CallOption) (*GetCoachByGuildResp, error) {
	out := new(GetCoachByGuildResp)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetCoachByGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) BatchGetGuildIdByCoachUids(ctx context.Context, in *BatchGetGuildIdByCoachUidsRequest, opts ...grpc.CallOption) (*BatchGetGuildIdByCoachUidsResponse, error) {
	out := new(BatchGetGuildIdByCoachUidsResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/BatchGetGuildIdByCoachUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) GetLarkPool(ctx context.Context, in *GetLarkPoolRequest, opts ...grpc.CallOption) (*GetLarkPoolResponse, error) {
	out := new(GetLarkPoolResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/GetLarkPool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eSportRoleClient) CheckInLarkPool(ctx context.Context, in *CheckInLarkPoolRequest, opts ...grpc.CallOption) (*CheckInLarkPoolResponse, error) {
	out := new(CheckInLarkPoolResponse)
	err := c.cc.Invoke(ctx, "/esport_role.ESportRole/CheckInLarkPool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ESportRoleServer is the server API for ESportRole service.
type ESportRoleServer interface {
	// 工会审核结果通知
	ESportGuildAuditResult(context.Context, *ESportGuildAuditResultReq) (*ESportGuildAuditResultResp, error)
	// 提交审核申请
	ApplyESportRequest(context.Context, *ApplyESportRequset) (*ApplyESportResponse, error)
	// 根据uid查询用户电竞指导身份
	GetUserESportRole(context.Context, *GetUserESportRoleReq) (*GetUserESportRoleResp, error)
	// T盾风控结果通知
	ESportRiskAuditResult(context.Context, *ESportRiskAuditResultReq) (*ESportRiskAuditResultResp, error)
	// 批量查询用户电竞指导身份
	BatchGetUserESportRole(context.Context, *BatchGetUserESportRoleReq) (*BatchGetUserESportRoleResp, error)
	// 手动新增用户身份信息
	ManualAddUserESport(context.Context, *ManualAddUserESportReq) (*ManualAddUserESportResp, error)
	// 检查用户是否已提交申请
	CheckUserApplyESport(context.Context, *CheckUserApplyESportReq) (*CheckUserApplyESportResp, error)
	// 检查用户时候有在内审中的签约工会申请
	CheckSignGuildRiskAuditing(context.Context, *CheckSignGuildRiskAuditingReq) (*CheckSignGuildRiskAuditingResp, error)
	// =================== 审核后台接口 ===================
	// 获取电竞指导身份申请记录
	GetESportAuditList(context.Context, *GetApplyESportRecordReq) (*GetApplyESportRecordResp, error)
	// 批量审核电指导身份申请
	OfficialHandleApplyESport(context.Context, *OfficialHandleApplyESportReq) (*OfficialHandleApplyESportResp, error)
	// 分页获取电竞指导信息
	GetESportErInfoList(context.Context, *GetESportErInfoListReq) (*GetESportErInfoListResp, error)
	// 分页获取所有电竞指导信息
	GetAllESportRole(context.Context, *GetAllESportRoleReq) (*GetAllESportRoleResp, error)
	// 批量回收电竞指导身份
	ReclaimESportErIdentity(context.Context, *ReclaimESportErIdentityReq) (*ReclaimESportErIdentityResp, error)
	// 获取黑名单管理记录
	GetApplyBlackList(context.Context, *GetApplyBlackListReq) (*GetApplyBlackListResp, error)
	// 修改身份申请黑名单冻结时间
	UpdateApplyBlacklistFreezeTime(context.Context, *UpdateApplyBlacklistFreezeTimeReq) (*UpdateApplyBlacklistFreezeTimeResp, error)
	// 获取身份回收记录
	GetReclaimInfoList(context.Context, *GetReclaimInfoListReq) (*GetReclaimInfoListResp, error)
	// 判断用户是否拥有电竞指导身份
	BatCheckUserESportRole(context.Context, *BatCheckUserESportRoleReq) (*BatCheckUserESportRoleResp, error)
	// 获取黑名单操作记录  【设为永久/移除】的记录
	GetBlackListHandleRecord(context.Context, *GetBlackListHandleRecordReq) (*GetBlackListHandleRecordResp, error)
	// =============================== 电竞标识 ================================
	// 批量下发电竞标识
	BatchGrantCoachLabel(context.Context, *BatchGrantCoachLabelRequest) (*BatchGrantCoachLabelResponse, error)
	// 分页获取电竞标识下发记录
	GetCoachLabelRecordByPage(context.Context, *GetCoachLabelRecordByPageRequest) (*GetCoachLabelRecordByPageResponse, error)
	// 收回电竞标识
	RecallCoachLabel(context.Context, *RecallCoachLabelRequest) (*RecallCoachLabelResponse, error)
	// 更新电竞标识
	UpdateCoachLabel(context.Context, *UpdateCoachLabelRequest) (*UpdateCoachLabelResponse, error)
	// 检查时间冲突
	CheckTimeOverlapped(context.Context, *CheckTimeOverlappedRequest) (*CheckTimeOverlappedResponse, error)
	// 批量获取用户电竞指导标识
	BatchGetCoachLabel(context.Context, *BatchGetCoachLabelRequest) (*BatchGetCoachLabelResponse, error)
	// CheckCoachLabelTTid 检查ttid能否下发电竞标识
	BatchGetCoachInfoByTTid(context.Context, *BatchGetCoachInfoByTTidRequest) (*BatchGetCoachInfoByTTidResponse, error)
	// 分页查询指定公会旗下的电竞大神
	GetCoachByGuild(context.Context, *GetCoachByGuildReq) (*GetCoachByGuildResp, error)
	// 批量根据大神的id获取对应的公会id
	BatchGetGuildIdByCoachUids(context.Context, *BatchGetGuildIdByCoachUidsRequest) (*BatchGetGuildIdByCoachUidsResponse, error)
	// 获取飞书池子
	GetLarkPool(context.Context, *GetLarkPoolRequest) (*GetLarkPoolResponse, error)
	// 检查是否在飞书池子中
	CheckInLarkPool(context.Context, *CheckInLarkPoolRequest) (*CheckInLarkPoolResponse, error)
}

func RegisterESportRoleServer(s *grpc.Server, srv ESportRoleServer) {
	s.RegisterService(&_ESportRole_serviceDesc, srv)
}

func _ESportRole_ESportGuildAuditResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ESportGuildAuditResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).ESportGuildAuditResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/ESportGuildAuditResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).ESportGuildAuditResult(ctx, req.(*ESportGuildAuditResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_ApplyESportRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyESportRequset)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).ApplyESportRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/ApplyESportRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).ApplyESportRequest(ctx, req.(*ApplyESportRequset))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetUserESportRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserESportRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetUserESportRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetUserESportRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetUserESportRole(ctx, req.(*GetUserESportRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_ESportRiskAuditResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ESportRiskAuditResultReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).ESportRiskAuditResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/ESportRiskAuditResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).ESportRiskAuditResult(ctx, req.(*ESportRiskAuditResultReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_BatchGetUserESportRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUserESportRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).BatchGetUserESportRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/BatchGetUserESportRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).BatchGetUserESportRole(ctx, req.(*BatchGetUserESportRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_ManualAddUserESport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualAddUserESportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).ManualAddUserESport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/ManualAddUserESport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).ManualAddUserESport(ctx, req.(*ManualAddUserESportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_CheckUserApplyESport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserApplyESportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).CheckUserApplyESport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/CheckUserApplyESport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).CheckUserApplyESport(ctx, req.(*CheckUserApplyESportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_CheckSignGuildRiskAuditing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckSignGuildRiskAuditingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).CheckSignGuildRiskAuditing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/CheckSignGuildRiskAuditing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).CheckSignGuildRiskAuditing(ctx, req.(*CheckSignGuildRiskAuditingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetESportAuditList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyESportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetESportAuditList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetESportAuditList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetESportAuditList(ctx, req.(*GetApplyESportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_OfficialHandleApplyESport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OfficialHandleApplyESportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).OfficialHandleApplyESport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/OfficialHandleApplyESport",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).OfficialHandleApplyESport(ctx, req.(*OfficialHandleApplyESportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetESportErInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetESportErInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetESportErInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetESportErInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetESportErInfoList(ctx, req.(*GetESportErInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetAllESportRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllESportRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetAllESportRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetAllESportRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetAllESportRole(ctx, req.(*GetAllESportRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_ReclaimESportErIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReclaimESportErIdentityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).ReclaimESportErIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/ReclaimESportErIdentity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).ReclaimESportErIdentity(ctx, req.(*ReclaimESportErIdentityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetApplyBlackList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyBlackListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetApplyBlackList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetApplyBlackList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetApplyBlackList(ctx, req.(*GetApplyBlackListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_UpdateApplyBlacklistFreezeTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateApplyBlacklistFreezeTimeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).UpdateApplyBlacklistFreezeTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/UpdateApplyBlacklistFreezeTime",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).UpdateApplyBlacklistFreezeTime(ctx, req.(*UpdateApplyBlacklistFreezeTimeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetReclaimInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetReclaimInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetReclaimInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetReclaimInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetReclaimInfoList(ctx, req.(*GetReclaimInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_BatCheckUserESportRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatCheckUserESportRoleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).BatCheckUserESportRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/BatCheckUserESportRole",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).BatCheckUserESportRole(ctx, req.(*BatCheckUserESportRoleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetBlackListHandleRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBlackListHandleRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetBlackListHandleRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetBlackListHandleRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetBlackListHandleRecord(ctx, req.(*GetBlackListHandleRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_BatchGrantCoachLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGrantCoachLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).BatchGrantCoachLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/BatchGrantCoachLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).BatchGrantCoachLabel(ctx, req.(*BatchGrantCoachLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetCoachLabelRecordByPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachLabelRecordByPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetCoachLabelRecordByPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetCoachLabelRecordByPage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetCoachLabelRecordByPage(ctx, req.(*GetCoachLabelRecordByPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_RecallCoachLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecallCoachLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).RecallCoachLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/RecallCoachLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).RecallCoachLabel(ctx, req.(*RecallCoachLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_UpdateCoachLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCoachLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).UpdateCoachLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/UpdateCoachLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).UpdateCoachLabel(ctx, req.(*UpdateCoachLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_CheckTimeOverlapped_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckTimeOverlappedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).CheckTimeOverlapped(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/CheckTimeOverlapped",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).CheckTimeOverlapped(ctx, req.(*CheckTimeOverlappedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_BatchGetCoachLabel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCoachLabelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).BatchGetCoachLabel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/BatchGetCoachLabel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).BatchGetCoachLabel(ctx, req.(*BatchGetCoachLabelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_BatchGetCoachInfoByTTid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetCoachInfoByTTidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).BatchGetCoachInfoByTTid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/BatchGetCoachInfoByTTid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).BatchGetCoachInfoByTTid(ctx, req.(*BatchGetCoachInfoByTTidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetCoachByGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCoachByGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetCoachByGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetCoachByGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetCoachByGuild(ctx, req.(*GetCoachByGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_BatchGetGuildIdByCoachUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetGuildIdByCoachUidsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).BatchGetGuildIdByCoachUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/BatchGetGuildIdByCoachUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).BatchGetGuildIdByCoachUids(ctx, req.(*BatchGetGuildIdByCoachUidsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_GetLarkPool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLarkPoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).GetLarkPool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/GetLarkPool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).GetLarkPool(ctx, req.(*GetLarkPoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ESportRole_CheckInLarkPool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckInLarkPoolRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ESportRoleServer).CheckInLarkPool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/esport_role.ESportRole/CheckInLarkPool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ESportRoleServer).CheckInLarkPool(ctx, req.(*CheckInLarkPoolRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ESportRole_serviceDesc = grpc.ServiceDesc{
	ServiceName: "esport_role.ESportRole",
	HandlerType: (*ESportRoleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ESportGuildAuditResult",
			Handler:    _ESportRole_ESportGuildAuditResult_Handler,
		},
		{
			MethodName: "ApplyESportRequest",
			Handler:    _ESportRole_ApplyESportRequest_Handler,
		},
		{
			MethodName: "GetUserESportRole",
			Handler:    _ESportRole_GetUserESportRole_Handler,
		},
		{
			MethodName: "ESportRiskAuditResult",
			Handler:    _ESportRole_ESportRiskAuditResult_Handler,
		},
		{
			MethodName: "BatchGetUserESportRole",
			Handler:    _ESportRole_BatchGetUserESportRole_Handler,
		},
		{
			MethodName: "ManualAddUserESport",
			Handler:    _ESportRole_ManualAddUserESport_Handler,
		},
		{
			MethodName: "CheckUserApplyESport",
			Handler:    _ESportRole_CheckUserApplyESport_Handler,
		},
		{
			MethodName: "CheckSignGuildRiskAuditing",
			Handler:    _ESportRole_CheckSignGuildRiskAuditing_Handler,
		},
		{
			MethodName: "GetESportAuditList",
			Handler:    _ESportRole_GetESportAuditList_Handler,
		},
		{
			MethodName: "OfficialHandleApplyESport",
			Handler:    _ESportRole_OfficialHandleApplyESport_Handler,
		},
		{
			MethodName: "GetESportErInfoList",
			Handler:    _ESportRole_GetESportErInfoList_Handler,
		},
		{
			MethodName: "GetAllESportRole",
			Handler:    _ESportRole_GetAllESportRole_Handler,
		},
		{
			MethodName: "ReclaimESportErIdentity",
			Handler:    _ESportRole_ReclaimESportErIdentity_Handler,
		},
		{
			MethodName: "GetApplyBlackList",
			Handler:    _ESportRole_GetApplyBlackList_Handler,
		},
		{
			MethodName: "UpdateApplyBlacklistFreezeTime",
			Handler:    _ESportRole_UpdateApplyBlacklistFreezeTime_Handler,
		},
		{
			MethodName: "GetReclaimInfoList",
			Handler:    _ESportRole_GetReclaimInfoList_Handler,
		},
		{
			MethodName: "BatCheckUserESportRole",
			Handler:    _ESportRole_BatCheckUserESportRole_Handler,
		},
		{
			MethodName: "GetBlackListHandleRecord",
			Handler:    _ESportRole_GetBlackListHandleRecord_Handler,
		},
		{
			MethodName: "BatchGrantCoachLabel",
			Handler:    _ESportRole_BatchGrantCoachLabel_Handler,
		},
		{
			MethodName: "GetCoachLabelRecordByPage",
			Handler:    _ESportRole_GetCoachLabelRecordByPage_Handler,
		},
		{
			MethodName: "RecallCoachLabel",
			Handler:    _ESportRole_RecallCoachLabel_Handler,
		},
		{
			MethodName: "UpdateCoachLabel",
			Handler:    _ESportRole_UpdateCoachLabel_Handler,
		},
		{
			MethodName: "CheckTimeOverlapped",
			Handler:    _ESportRole_CheckTimeOverlapped_Handler,
		},
		{
			MethodName: "BatchGetCoachLabel",
			Handler:    _ESportRole_BatchGetCoachLabel_Handler,
		},
		{
			MethodName: "BatchGetCoachInfoByTTid",
			Handler:    _ESportRole_BatchGetCoachInfoByTTid_Handler,
		},
		{
			MethodName: "GetCoachByGuild",
			Handler:    _ESportRole_GetCoachByGuild_Handler,
		},
		{
			MethodName: "BatchGetGuildIdByCoachUids",
			Handler:    _ESportRole_BatchGetGuildIdByCoachUids_Handler,
		},
		{
			MethodName: "GetLarkPool",
			Handler:    _ESportRole_GetLarkPool_Handler,
		},
		{
			MethodName: "CheckInLarkPool",
			Handler:    _ESportRole_CheckInLarkPool_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/esport-role/esport-role.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/esport-role/esport-role.proto", fileDescriptor_esport_role_f48903801ad7e4f6)
}

var fileDescriptor_esport_role_f48903801ad7e4f6 = []byte{
	// 3756 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5b, 0xcd, 0x73, 0x1b, 0xc7,
	0x95, 0xf7, 0x00, 0x20, 0x09, 0x3c, 0x7e, 0x41, 0x4d, 0x8a, 0x04, 0x87, 0x22, 0x45, 0x8e, 0x68,
	0x89, 0x96, 0x2c, 0xd2, 0x2b, 0xad, 0x65, 0xaf, 0xd7, 0x5b, 0x2e, 0x90, 0x82, 0x68, 0x78, 0x29,
	0x12, 0x1e, 0x80, 0x96, 0xe5, 0xb2, 0x3d, 0x1e, 0x01, 0x4d, 0x72, 0x8a, 0x03, 0xcc, 0x70, 0x66,
	0xa0, 0x32, 0xb5, 0xb5, 0x07, 0x5f, 0xf6, 0xe8, 0xe3, 0xd6, 0x6e, 0xa5, 0x52, 0x39, 0xf8, 0x90,
	0x54, 0xe5, 0x96, 0xdc, 0x93, 0x43, 0xfe, 0x80, 0x1c, 0x72, 0xcc, 0x25, 0x39, 0x25, 0xff, 0x45,
	0x52, 0xfd, 0x31, 0x83, 0xee, 0xf9, 0x00, 0x41, 0x2b, 0x87, 0x9c, 0x88, 0x79, 0xfd, 0xba, 0xfb,
	0xf5, 0x7b, 0xbf, 0x7e, 0xaf, 0xdf, 0xeb, 0x26, 0xbc, 0x1d, 0x04, 0xdb, 0xe7, 0x7d, 0xab, 0x7d,
	0xe6, 0x5b, 0xf6, 0x4b, 0xec, 0x6d, 0x63, 0xdf, 0x75, 0xbc, 0xe0, 0xbe, 0xe7, 0xd8, 0x58, 0xfc,
	0xbd, 0xe5, 0x7a, 0x4e, 0xe0, 0xa0, 0x49, 0x46, 0x32, 0x08, 0x49, 0xdd, 0x4a, 0xef, 0xea, 0x9f,
	0x59, 0xb6, 0x2d, 0x7d, 0xb0, 0xce, 0xda, 0x5f, 0x14, 0x58, 0xaa, 0x35, 0x09, 0x79, 0xaf, 0x6f,
	0xd9, 0x9d, 0x6a, 0xbf, 0x63, 0x05, 0x3a, 0xf6, 0xfb, 0x76, 0xa0, 0xe3, 0x73, 0x74, 0x13, 0x26,
	0x4d, 0x42, 0x31, 0x02, 0xe7, 0x0c, 0xf7, 0x2a, 0xca, 0x9a, 0xb2, 0x59, 0xd2, 0x81, 0x92, 0x5a,
	0x84, 0x82, 0xca, 0x90, 0xef, 0x5b, 0x9d, 0x4a, 0x6e, 0x4d, 0xd9, 0x9c, 0xd6, 0xc9, 0x4f, 0xb4,
	0x04, 0xc5, 0x13, 0x32, 0x92, 0x61, 0x75, 0x2a, 0x79, 0x4a, 0x9e, 0xa0, 0xdf, 0xf5, 0x0e, 0x5a,
	0x01, 0xe0, 0xa3, 0x5d, 0xb8, 0xb8, 0x52, 0xa0, 0x8d, 0x25, 0x36, 0xd8, 0x85, 0x8b, 0xd1, 0x02,
	0x8c, 0x7b, 0xd8, 0xf4, 0x9d, 0x5e, 0x65, 0x8c, 0xce, 0xc3, 0xbf, 0xd0, 0x32, 0x94, 0x7c, 0xeb,
	0xa4, 0x67, 0x04, 0x56, 0x17, 0x57, 0xc6, 0x69, 0xaf, 0x22, 0x21, 0xb4, 0xac, 0x2e, 0x46, 0x9b,
	0x50, 0xa6, 0x8d, 0xf8, 0x5b, 0xd7, 0xf2, 0x30, 0xe3, 0x99, 0xa0, 0x3c, 0x33, 0x84, 0x5e, 0xa3,
	0x64, 0xc2, 0xa9, 0xdd, 0x00, 0x35, 0x6b, 0xa1, 0xbe, 0xab, 0xfd, 0xa0, 0x40, 0x85, 0x35, 0xeb,
	0x96, 0x7f, 0xf6, 0xfa, 0x6a, 0x90, 0xd7, 0x9a, 0xcf, 0x5e, 0x6b, 0x41, 0x5a, 0xeb, 0x0a, 0x80,
	0xdf, 0xc6, 0x3d, 0x6c, 0xb4, 0x9d, 0x0e, 0xe6, 0x7a, 0x28, 0x51, 0xca, 0xae, 0xd3, 0xc1, 0xda,
	0x72, 0x68, 0xac, 0x84, 0x90, 0xbe, 0xab, 0xfd, 0x39, 0x0f, 0xa8, 0xea, 0xba, 0xf6, 0x05, 0x67,
	0xc1, 0xe7, 0x7d, 0x1f, 0x07, 0x3f, 0xd2, 0x86, 0xed, 0xbe, 0x47, 0x01, 0x15, 0xda, 0xb0, 0xdd,
	0xf7, 0x74, 0xc7, 0xc6, 0x74, 0x5d, 0x64, 0x0e, 0xd9, 0x86, 0x84, 0x42, 0xd7, 0x75, 0x13, 0x26,
	0xad, 0x0e, 0xee, 0x05, 0xd6, 0xf1, 0x05, 0x01, 0x00, 0x5b, 0x00, 0x84, 0x24, 0x8e, 0x01, 0xd6,
	0x7f, 0x60, 0x4d, 0xde, 0x9f, 0x98, 0x53, 0x44, 0xcf, 0x84, 0x8c, 0x9e, 0x5b, 0x30, 0x4d, 0x2d,
	0xdd, 0xe9, 0x7b, 0x66, 0x60, 0x39, 0xbd, 0x4a, 0x91, 0xb6, 0x4f, 0x11, 0xe2, 0x63, 0x4e, 0x93,
	0xb1, 0x52, 0x1a, 0x01, 0x2b, 0x90, 0x8a, 0x95, 0xff, 0x55, 0x60, 0x96, 0x69, 0xb1, 0x1a, 0x2d,
	0x6d, 0x85, 0xe8, 0xbe, 0x71, 0xa8, 0xb7, 0x8c, 0x6a, 0xa3, 0xb1, 0xff, 0xdc, 0x68, 0x3d, 0x6f,
	0xd4, 0x8c, 0xfa, 0xc1, 0x67, 0xd5, 0xfd, 0xfa, 0xe3, 0xf2, 0x1b, 0x68, 0x95, 0xc0, 0x2b, 0xde,
	0xdc, 0xa8, 0xe9, 0xcd, 0xc3, 0x83, 0xea, 0x7e, 0x59, 0x41, 0xcb, 0xb0, 0x98, 0x6c, 0xdf, 0x3b,
	0xaa, 0xef, 0x3f, 0x2e, 0xe7, 0xd0, 0x1d, 0xb8, 0x95, 0xdd, 0xd9, 0x68, 0x1d, 0x72, 0xc6, 0xbc,
	0x76, 0x1d, 0xe6, 0x24, 0x13, 0xfb, 0xae, 0xd3, 0xf3, 0xb1, 0xb6, 0x09, 0xf3, 0x7b, 0x38, 0x38,
	0xf2, 0xb1, 0xc7, 0x1b, 0x1c, 0x1b, 0x13, 0xe0, 0x72, 0xd3, 0x2a, 0x91, 0x69, 0xb5, 0x26, 0x5c,
	0x4f, 0xe1, 0xf4, 0x5d, 0x62, 0x39, 0xc1, 0x8f, 0xf0, 0x2e, 0xc0, 0x48, 0xd4, 0xf2, 0xa2, 0x69,
	0x72, 0x92, 0x69, 0xb4, 0xfb, 0xb0, 0xb4, 0x63, 0x06, 0xed, 0xd3, 0xe1, 0x32, 0xe4, 0x43, 0x19,
	0xbe, 0x86, 0x19, 0x99, 0x2d, 0x29, 0x67, 0x5c, 0x9c, 0xdc, 0x50, 0x71, 0x64, 0x3f, 0xa3, 0x7d,
	0x06, 0x6a, 0x96, 0x38, 0xbe, 0x8b, 0xde, 0x87, 0x12, 0x19, 0xd2, 0xb0, 0x2d, 0x3f, 0xa0, 0x52,
	0x4d, 0x3e, 0x58, 0xde, 0x12, 0xe6, 0xda, 0x8a, 0xf5, 0x29, 0x12, 0xe2, 0xbe, 0xe5, 0x07, 0xda,
	0xef, 0x14, 0x58, 0x78, 0x6a, 0xf6, 0xfa, 0xa6, 0x5d, 0xed, 0x74, 0x04, 0xae, 0x34, 0x45, 0xbf,
	0xce, 0x02, 0xd0, 0x6d, 0x98, 0x65, 0x4d, 0x03, 0x2c, 0xb3, 0x9d, 0x36, 0x4d, 0xc9, 0xcd, 0x10,
	0xd0, 0x0f, 0x61, 0x41, 0xe0, 0x13, 0x61, 0x3d, 0x46, 0xd9, 0xe7, 0x22, 0x76, 0x01, 0xdb, 0x4b,
	0xb0, 0x98, 0xba, 0x08, 0xdf, 0xd5, 0x7e, 0x5e, 0x80, 0x6b, 0x12, 0xbc, 0xda, 0x8e, 0x47, 0xbd,
	0x01, 0xdb, 0xb2, 0xd1, 0x02, 0x27, 0xe8, 0x77, 0xbd, 0x93, 0xe2, 0x3a, 0x10, 0x14, 0x82, 0x80,
	0xaf, 0xa8, 0xa4, 0xd3, 0xdf, 0x48, 0x85, 0x62, 0xcf, 0x6a, 0x9f, 0xf5, 0x4c, 0xbe, 0x8e, 0x92,
	0x1e, 0x7d, 0x13, 0x47, 0x78, 0x82, 0x7b, 0x1d, 0xec, 0x71, 0x91, 0xf9, 0x17, 0x19, 0xd9, 0x3c,
	0x09, 0x1d, 0x04, 0xf9, 0x39, 0xcc, 0x35, 0xc8, 0x4e, 0xa5, 0x18, 0x77, 0x2a, 0xb2, 0xcf, 0x2a,
	0xc5, 0x7d, 0xd6, 0x3d, 0xb8, 0xd6, 0x76, 0x7a, 0x81, 0x67, 0xb6, 0x83, 0x81, 0x73, 0x61, 0x7e,
	0xa1, 0x1c, 0x36, 0x44, 0x0e, 0x66, 0x13, 0xca, 0xb6, 0x73, 0x62, 0xf5, 0x0c, 0x27, 0x38, 0xc5,
	0x9e, 0xd1, 0xb7, 0x3a, 0x7e, 0x65, 0x92, 0x42, 0x7b, 0x86, 0xd2, 0x0f, 0x09, 0xf9, 0xc8, 0xea,
	0xf8, 0x68, 0x1d, 0xa6, 0x3c, 0xdc, 0x3e, 0x35, 0xbd, 0x13, 0x6c, 0xf4, 0xfa, 0xdd, 0xca, 0x14,
	0x1d, 0x71, 0x32, 0xa4, 0x1d, 0xf4, 0xbb, 0xa8, 0x02, 0x13, 0xa7, 0x66, 0xaf, 0x63, 0x63, 0xaf,
	0x32, 0x4d, 0xf5, 0x12, 0x7e, 0x92, 0x16, 0x0f, 0x77, 0x4d, 0xef, 0xcc, 0xaf, 0xcc, 0xb0, 0x16,
	0xfe, 0x49, 0x86, 0x65, 0x8b, 0xf1, 0x03, 0x33, 0xe8, 0xfb, 0x95, 0x59, 0x36, 0x2c, 0xa5, 0x35,
	0x29, 0x29, 0xee, 0xf1, 0xcb, 0x09, 0x8f, 0x3f, 0x08, 0x4e, 0x44, 0x5f, 0xd7, 0xc4, 0xe0, 0x44,
	0xf4, 0xb5, 0x0e, 0x53, 0xcc, 0x63, 0x07, 0x17, 0x54, 0x72, 0x44, 0x07, 0x98, 0x0c, 0x69, 0x07,
	0xfd, 0xae, 0xf6, 0x93, 0x1c, 0x2c, 0xee, 0xe1, 0x20, 0x01, 0x16, 0xb2, 0x17, 0x56, 0x00, 0xce,
	0xfb, 0xd8, 0xe3, 0xea, 0x66, 0x88, 0x29, 0x51, 0x4a, 0x18, 0x22, 0xf8, 0xc6, 0xa0, 0xed, 0xd2,
	0xc6, 0xa0, 0x0c, 0x4b, 0x50, 0xec, 0x5b, 0x1d, 0xb6, 0x3f, 0xf3, 0x54, 0xb5, 0x13, 0x7d, 0xab,
	0x43, 0x76, 0xa0, 0x84, 0x81, 0x82, 0x8c, 0x01, 0x04, 0x05, 0x97, 0x20, 0x86, 0xc1, 0x88, 0xfe,
	0x26, 0xd1, 0x80, 0xfc, 0x35, 0x7c, 0xeb, 0x55, 0x74, 0x72, 0x20, 0x84, 0xa6, 0xf5, 0x8a, 0xca,
	0xc1, 0x54, 0xc8, 0x66, 0x9a, 0xa0, 0x33, 0x01, 0x23, 0xd1, 0xc9, 0x56, 0x00, 0x5e, 0x60, 0x62,
	0x6a, 0x11, 0x55, 0x94, 0x12, 0x86, 0x2a, 0xdc, 0xeb, 0x88, 0x91, 0x66, 0x02, 0xf7, 0x3a, 0x74,
	0x8b, 0x7d, 0x0b, 0x95, 0x74, 0xe5, 0xf8, 0x2e, 0xfa, 0x08, 0x08, 0x04, 0x1c, 0xaf, 0x23, 0x3a,
	0xa0, 0x55, 0xc9, 0x01, 0x25, 0x3b, 0x02, 0xeb, 0x42, 0xc5, 0x5a, 0x86, 0x52, 0xe0, 0x04, 0xa6,
	0x6d, 0xb4, 0x7b, 0x01, 0xd7, 0x5e, 0x91, 0x12, 0x76, 0x7b, 0x81, 0xf6, 0xbd, 0x02, 0x37, 0x0e,
	0x8f, 0x8f, 0xad, 0xb6, 0x65, 0xda, 0x1f, 0x53, 0x2c, 0xc9, 0x27, 0x82, 0xd8, 0x66, 0xce, 0x8b,
	0x9b, 0x59, 0x3e, 0xb2, 0xe4, 0xe2, 0x47, 0x16, 0x15, 0x8a, 0x8e, 0x8b, 0x3d, 0x33, 0x70, 0x3c,
	0xbe, 0xbb, 0xa3, 0x6f, 0x11, 0xae, 0x05, 0x09, 0xae, 0xda, 0x4d, 0x58, 0x19, 0x22, 0x8f, 0xef,
	0x6a, 0x7f, 0x52, 0x60, 0x86, 0xd2, 0x76, 0x6c, 0xb3, 0x7d, 0x46, 0x57, 0x98, 0x74, 0xa6, 0xb2,
	0x29, 0x72, 0xc3, 0x4c, 0x91, 0x97, 0x4c, 0x81, 0x16, 0x61, 0xc2, 0x71, 0x8d, 0xbe, 0x8f, 0xbd,
	0xf0, 0xa4, 0xe5, 0xb8, 0xc4, 0xeb, 0x11, 0xf3, 0xf7, 0xdd, 0x8e, 0x19, 0x48, 0x0e, 0x13, 0x18,
	0x89, 0xf6, 0x14, 0xd6, 0x34, 0x2e, 0x6f, 0x41, 0xd1, 0x9f, 0x4d, 0xc4, 0xfc, 0x59, 0xe8, 0xff,
	0x8a, 0x03, 0xff, 0xa7, 0x7d, 0xa7, 0xd0, 0xf0, 0x2c, 0xaf, 0x72, 0x84, 0x9d, 0x22, 0x6e, 0x84,
	0x9c, 0xbc, 0x11, 0x42, 0xb4, 0xe7, 0xb3, 0xd0, 0x5e, 0x90, 0xd1, 0xae, 0x79, 0x34, 0xee, 0xc7,
	0x45, 0xf0, 0x5d, 0xf4, 0x61, 0x1a, 0x1e, 0x97, 0x93, 0x78, 0x1c, 0xf4, 0x1a, 0x19, 0x8c, 0xff,
	0x93, 0x83, 0xf5, 0x23, 0xaa, 0xd0, 0xc1, 0x08, 0x64, 0x92, 0x27, 0x1e, 0xc6, 0xaf, 0xa8, 0x92,
	0xd3, 0x43, 0xa7, 0xa0, 0xf9, 0x9c, 0xac, 0x79, 0xc1, 0x9a, 0x79, 0xc9, 0x9a, 0xac, 0x41, 0x38,
	0x93, 0x8e, 0x3b, 0x6e, 0xa8, 0xc3, 0x08, 0x1a, 0xe3, 0xf2, 0x2e, 0x3d, 0x85, 0xf1, 0x43, 0xc6,
	0x34, 0x07, 0xb3, 0x87, 0x8d, 0xc1, 0x81, 0x8e, 0x1c, 0xb5, 0xde, 0x40, 0x4b, 0x70, 0x3d, 0x24,
	0x36, 0x6b, 0x2d, 0x72, 0x1a, 0x7b, 0x5a, 0x3d, 0xa8, 0x1d, 0xb4, 0xca, 0x0a, 0xaa, 0xc0, 0xbc,
	0xd8, 0x74, 0x74, 0xf0, 0xe4, 0x50, 0xff, 0xa2, 0x76, 0x50, 0xce, 0xa1, 0x79, 0x28, 0x8b, 0x2d,
	0xad, 0xfa, 0xd3, 0x5a, 0x39, 0xaf, 0x6d, 0x80, 0x76, 0x99, 0x1e, 0x7c, 0x57, 0x33, 0x61, 0x2a,
	0x8c, 0xec, 0xf5, 0xde, 0xb1, 0x43, 0x10, 0xea, 0xbc, 0x08, 0xcc, 0x10, 0xf5, 0xfc, 0x44, 0xc6,
	0x48, 0x14, 0xa1, 0xef, 0xc0, 0x7c, 0xdb, 0xec, 0xb5, 0xb1, 0x6d, 0x44, 0xf1, 0x4b, 0xd8, 0x1f,
	0x88, 0xb5, 0xed, 0xf2, 0x26, 0xba, 0xe4, 0xbf, 0x15, 0x60, 0x8a, 0xed, 0xbd, 0x9a, 0x47, 0xe7,
	0x48, 0x2a, 0xff, 0xaa, 0x01, 0x9c, 0x07, 0xea, 0xb1, 0x41, 0xa0, 0x1e, 0x84, 0xf4, 0x71, 0x29,
	0xa4, 0x6f, 0xc0, 0x0c, 0x47, 0x15, 0xf6, 0x98, 0xa9, 0x58, 0x18, 0x9f, 0x62, 0xd4, 0x9a, 0x47,
	0x6d, 0xa1, 0xf1, 0x63, 0x7e, 0xe4, 0xe7, 0x99, 0xe3, 0x9d, 0x24, 0xc4, 0x3d, 0xee, 0xeb, 0x17,
	0x61, 0x82, 0x9d, 0x8c, 0x7c, 0xee, 0x79, 0xc7, 0xe9, 0x19, 0xdf, 0x47, 0xab, 0x30, 0x19, 0x1e,
	0x85, 0x48, 0x23, 0x0b, 0xe2, 0x25, 0x7e, 0xb8, 0x6f, 0xf9, 0x44, 0x04, 0xae, 0x52, 0x7a, 0x04,
	0x0c, 0x48, 0xec, 0xa6, 0x22, 0x30, 0x2a, 0x39, 0x97, 0xb5, 0x7c, 0x74, 0x1f, 0xe6, 0x8e, 0x2d,
	0xcf, 0x0f, 0x8c, 0xc0, 0x3c, 0xc3, 0x86, 0xe3, 0x75, 0x88, 0xbc, 0x3e, 0x0f, 0xe0, 0x65, 0xda,
	0xd4, 0x32, 0xcf, 0xf0, 0x21, 0x69, 0x68, 0xf9, 0xa8, 0x0a, 0x33, 0x27, 0x66, 0x17, 0x1b, 0x56,
	0xef, 0xd8, 0x61, 0x9b, 0x68, 0x5a, 0xde, 0x44, 0x2c, 0xdf, 0x26, 0x38, 0x6d, 0x92, 0x5f, 0x44,
	0xf1, 0xfa, 0x14, 0xe9, 0x42, 0x7e, 0xd1, 0x6d, 0x24, 0xe0, 0x7a, 0x46, 0xc2, 0xf5, 0x1c, 0x8c,
	0x39, 0x74, 0x29, 0x2c, 0xcc, 0x17, 0x1c, 0xb2, 0x8a, 0x78, 0x7c, 0x2e, 0x27, 0xe2, 0x33, 0x7a,
	0xc4, 0xf3, 0x20, 0x2a, 0xce, 0x35, 0x2a, 0xce, 0x92, 0xb4, 0xa7, 0x45, 0xa4, 0xb1, 0x14, 0x89,
	0x0a, 0x72, 0x13, 0x26, 0x8f, 0x29, 0x2a, 0x19, 0x92, 0x10, 0xc3, 0xdc, 0x71, 0x04, 0x54, 0x21,
	0x71, 0x9d, 0x93, 0x12, 0xd7, 0xdb, 0x30, 0x2b, 0x98, 0x8d, 0x22, 0x65, 0x9e, 0x32, 0x4c, 0x47,
	0x86, 0x3b, 0x30, 0xbb, 0x58, 0xfb, 0xad, 0x02, 0x0b, 0x7b, 0x38, 0x10, 0x41, 0x38, 0xa2, 0x37,
	0xfc, 0x27, 0x39, 0x37, 0x68, 0x3d, 0x7a, 0xf2, 0x49, 0x2e, 0xc0, 0x77, 0x89, 0xd6, 0x07, 0x20,
	0x50, 0x52, 0xb4, 0x2e, 0xf6, 0xd2, 0x8b, 0x56, 0x68, 0x7e, 0xc9, 0x8b, 0xe6, 0x63, 0x5e, 0xb4,
	0x09, 0x33, 0x83, 0x6c, 0x84, 0x6e, 0xda, 0x19, 0xc8, 0x45, 0x7b, 0x36, 0x67, 0xa5, 0x9d, 0xc2,
	0x63, 0xc9, 0x47, 0x3e, 0x9e, 0x7c, 0x68, 0xbb, 0x30, 0x47, 0xc2, 0x81, 0x6d, 0xcb, 0xb9, 0xda,
	0x02, 0x8c, 0x3b, 0xc7, 0xc7, 0x3e, 0x0e, 0xf8, 0xe8, 0xfc, 0x0b, 0xcd, 0xc3, 0x98, 0x6d, 0x75,
	0xad, 0xd0, 0xc5, 0xb3, 0x0f, 0xad, 0xc1, 0xc2, 0x9a, 0x3c, 0x08, 0xcb, 0xb0, 0xe2, 0x6a, 0x58,
	0x4e, 0x51, 0x43, 0xb8, 0x9e, 0x81, 0x22, 0xb4, 0xcf, 0x61, 0x52, 0xc7, 0x6d, 0xdb, 0xb4, 0xba,
	0x19, 0xde, 0x89, 0xc2, 0x8f, 0xc4, 0x02, 0x1e, 0x19, 0xf8, 0x57, 0x1c, 0xb7, 0x64, 0xc1, 0x63,
	0x22, 0x6e, 0xb5, 0x2e, 0xa8, 0x7c, 0xe4, 0xc8, 0x06, 0x7c, 0xbb, 0x90, 0x75, 0xbf, 0x9b, 0x94,
	0xb8, 0x22, 0x49, 0x2c, 0x48, 0x25, 0xd8, 0x4d, 0x38, 0xbf, 0xe7, 0xa4, 0xf3, 0xbb, 0xb6, 0x02,
	0xcb, 0x99, 0xd3, 0xf9, 0xae, 0xf6, 0x53, 0x85, 0x86, 0x63, 0x61, 0xd4, 0xd7, 0x3f, 0x12, 0x0c,
	0xc9, 0x27, 0x43, 0x8c, 0x17, 0xb2, 0x30, 0x3e, 0x16, 0xc3, 0x78, 0x97, 0x6e, 0xd2, 0x84, 0x78,
	0xff, 0x28, 0x88, 0xc7, 0x0f, 0x0a, 0x8f, 0x68, 0xfd, 0x60, 0xf7, 0x14, 0xb7, 0xcf, 0x92, 0xf5,
	0x03, 0x71, 0xc9, 0x8a, 0xb4, 0x64, 0xed, 0x3d, 0x9a, 0xe8, 0xa7, 0xf6, 0xf3, 0xdd, 0x61, 0x1d,
	0x0f, 0x60, 0x31, 0xea, 0x15, 0x3b, 0x20, 0x27, 0x31, 0x17, 0xb9, 0xdb, 0x63, 0xe6, 0x6e, 0x73,
	0xa2, 0xbb, 0x3d, 0xa6, 0xe9, 0xd0, 0xaf, 0x15, 0xa8, 0xa4, 0x0f, 0xc8, 0xe4, 0xc8, 0xca, 0x9f,
	0xe5, 0xcc, 0x34, 0x17, 0xcf, 0x4c, 0xe3, 0xb9, 0x5e, 0xfe, 0xd2, 0x5c, 0xaf, 0x90, 0xc8, 0xf5,
	0x44, 0x58, 0x8c, 0xc9, 0x75, 0x12, 0x0b, 0x96, 0xf7, 0x70, 0x10, 0x1d, 0xec, 0xd8, 0xf9, 0x7c,
	0x90, 0xc7, 0x65, 0xeb, 0x2f, 0x02, 0x54, 0x2e, 0x0b, 0x50, 0xf9, 0x18, 0xa0, 0x7e, 0x91, 0x87,
	0xb9, 0xd8, 0x44, 0x97, 0x9c, 0x3f, 0x72, 0x19, 0xe7, 0x8f, 0x7c, 0xec, 0xfc, 0x91, 0x79, 0xf2,
	0xbb, 0x07, 0xc8, 0x71, 0x8d, 0x17, 0x64, 0x52, 0xba, 0x06, 0x16, 0x5e, 0x59, 0x45, 0x72, 0xd6,
	0x71, 0x23, 0x69, 0x68, 0x9c, 0x4d, 0x30, 0x0b, 0x07, 0x46, 0x91, 0x99, 0xc6, 0xc0, 0x6d, 0x98,
	0x97, 0x99, 0x79, 0x44, 0x64, 0xb9, 0xc0, 0x35, 0x81, 0x5d, 0x8f, 0xaa, 0xba, 0x3f, 0x2e, 0x93,
	0x44, 0xb7, 0x60, 0xba, 0x83, 0x8f, 0x3d, 0x8b, 0xb4, 0x53, 0xf9, 0x81, 0xce, 0x31, 0x15, 0x12,
	0xa9, 0xf0, 0x22, 0x13, 0x1d, 0x84, 0x1f, 0x6a, 0x42, 0x22, 0x1d, 0xe9, 0x0e, 0xcc, 0x46, 0x4c,
	0x5c, 0xde, 0x29, 0x3a, 0xd6, 0x4c, 0x48, 0x66, 0xc2, 0x6a, 0xe7, 0x70, 0x23, 0x1b, 0x15, 0xbe,
	0x8b, 0xfe, 0x15, 0x0a, 0xc2, 0xe6, 0x5f, 0x93, 0x36, 0x7f, 0x8a, 0x89, 0x75, 0xca, 0x3d, 0x3c,
	0xc4, 0xfd, 0x0b, 0xac, 0xd0, 0xdd, 0xd3, 0x0c, 0x8f, 0x0a, 0x51, 0x79, 0xdb, 0xea, 0x9d, 0xa4,
	0xd7, 0x31, 0xff, 0x1d, 0x56, 0x87, 0x75, 0x61, 0xdb, 0x2e, 0x02, 0xbe, 0x22, 0x03, 0xff, 0x2b,
	0x80, 0x5d, 0xc7, 0x6c, 0x9f, 0xee, 0x9b, 0x2f, 0xb0, 0x8d, 0xde, 0x81, 0x42, 0xe4, 0x6c, 0x67,
	0x1e, 0xdc, 0x90, 0x16, 0x44, 0x39, 0x9a, 0x4e, 0xdf, 0x6b, 0x63, 0x02, 0x2a, 0x9d, 0x72, 0xd2,
	0x2a, 0x3d, 0xa5, 0x19, 0x7d, 0xcf, 0xe6, 0x48, 0x2d, 0x31, 0xca, 0x91, 0x67, 0x6b, 0xff, 0xa7,
	0xc0, 0x32, 0x2b, 0x40, 0x7a, 0x66, 0x2f, 0x18, 0xcc, 0xa4, 0xe3, 0xf3, 0x3e, 0x16, 0xf3, 0xdb,
	0xb0, 0x22, 0x8a, 0xee, 0xc3, 0x98, 0x4d, 0x38, 0xe8, 0x58, 0x93, 0x0f, 0x16, 0x25, 0x19, 0x84,
	0x01, 0x18, 0x57, 0x0c, 0x4f, 0x44, 0x9b, 0xf9, 0x2c, 0x3c, 0x15, 0x68, 0x63, 0x94, 0xf3, 0xac,
	0xc2, 0x8d, 0x74, 0xc9, 0x78, 0x21, 0xf9, 0x07, 0x05, 0xd6, 0xf6, 0xb0, 0xd4, 0x42, 0x0c, 0xbf,
	0x73, 0xd1, 0x30, 0x4f, 0x70, 0x42, 0xfe, 0x68, 0xd3, 0xbe, 0x0b, 0xe3, 0xdc, 0x45, 0xe5, 0xa8,
	0x12, 0x57, 0x32, 0x16, 0xc0, 0x9c, 0x96, 0xce, 0x99, 0x89, 0xa0, 0xd4, 0x65, 0x10, 0xaf, 0xca,
	0x43, 0x16, 0xf9, 0x26, 0x07, 0xd8, 0xa1, 0xc9, 0xec, 0x6f, 0xf2, 0xb0, 0x3e, 0x44, 0x4a, 0xb6,
	0x16, 0xf4, 0x4d, 0x5a, 0x66, 0xfb, 0x91, 0x24, 0xd9, 0xa5, 0x83, 0x6c, 0xc5, 0x9b, 0x47, 0xce,
	0x7e, 0xd5, 0xff, 0xcf, 0x41, 0x39, 0xde, 0x9b, 0xf4, 0xe0, 0x32, 0x45, 0x0a, 0x2c, 0x32, 0x42,
	0x6a, 0x35, 0x55, 0x85, 0x22, 0xd9, 0xf5, 0xa2, 0xe3, 0x0b, 0xbf, 0x87, 0x26, 0x65, 0x04, 0xa0,
	0x81, 0xe9, 0x05, 0x83, 0xda, 0x46, 0x5e, 0x2f, 0x51, 0x4a, 0x02, 0x20, 0xe3, 0x12, 0x40, 0x06,
	0x48, 0x9c, 0x18, 0x09, 0x89, 0x03, 0xc3, 0x17, 0xaf, 0x60, 0x78, 0xed, 0x11, 0x2c, 0xea, 0xb8,
	0x6d, 0xda, 0x76, 0x72, 0x73, 0x0c, 0xd3, 0x90, 0xa6, 0x42, 0x25, 0xd9, 0x8f, 0x43, 0x17, 0xc3,
	0x22, 0x4b, 0xb2, 0xaf, 0x36, 0xe6, 0x15, 0xf7, 0x1e, 0x11, 0x21, 0x39, 0x0d, 0x17, 0xe1, 0x1c,
	0x54, 0xea, 0x94, 0x88, 0x26, 0x0f, 0x5f, 0x62, 0xcf, 0x36, 0x5d, 0x17, 0x77, 0x42, 0x29, 0x86,
	0xc4, 0xd3, 0x64, 0x7d, 0x2b, 0x3f, 0xac, 0xbe, 0x25, 0x6c, 0xe8, 0x3f, 0x28, 0xb0, 0x9c, 0x3a,
	0x27, 0xdf, 0x04, 0x2d, 0x28, 0x59, 0x01, 0xee, 0x8a, 0x5b, 0xe0, 0x3d, 0x79, 0x85, 0xd9, 0x9d,
	0xb7, 0x06, 0xa4, 0x7a, 0x80, 0xbb, 0x7a, 0x91, 0x8c, 0x44, 0xe4, 0x55, 0xbf, 0x86, 0x19, 0xb9,
	0x2d, 0x0a, 0xdb, 0x8a, 0x10, 0xb6, 0x7f, 0xfc, 0xaa, 0x1e, 0x0d, 0x2e, 0x94, 0x92, 0xd6, 0x1c,
	0x72, 0xae, 0xfb, 0xa3, 0x32, 0xb8, 0xfa, 0x49, 0xda, 0x07, 0xbd, 0x80, 0xd9, 0x36, 0xa1, 0x1a,
	0xd4, 0x94, 0x46, 0xd7, 0x74, 0xb9, 0x4a, 0x3e, 0x90, 0xa3, 0x58, 0xe6, 0x08, 0x02, 0x1e, 0x9e,
	0x9a, 0x6e, 0xad, 0x17, 0x78, 0x17, 0xfa, 0x74, 0x5b, 0xa4, 0xa9, 0xcf, 0x01, 0x25, 0x99, 0xc8,
	0xd6, 0x3e, 0xc3, 0x17, 0xa1, 0xcb, 0x3c, 0xc3, 0x17, 0x04, 0x76, 0x2f, 0x4d, 0xbb, 0x8f, 0x2f,
	0x85, 0x1d, 0xe5, 0xfa, 0x20, 0xf7, 0xbe, 0xa2, 0xfd, 0x07, 0xac, 0x4a, 0xa2, 0x91, 0xf0, 0xba,
	0x73, 0xd1, 0x6a, 0x59, 0x1d, 0x01, 0xe8, 0x44, 0xf3, 0x03, 0xdd, 0x94, 0xf4, 0x22, 0x21, 0x50,
	0xe5, 0xfc, 0x2c, 0x0f, 0x37, 0x33, 0xfb, 0x73, 0x0d, 0xdd, 0x86, 0x59, 0xab, 0xf7, 0xd2, 0xb4,
	0x2d, 0xb2, 0x55, 0xc4, 0x61, 0xa6, 0x39, 0xb9, 0xce, 0x00, 0xbb, 0x01, 0x33, 0x3d, 0x27, 0x30,
	0xb8, 0x36, 0xc3, 0x6c, 0xa4, 0xa4, 0x4f, 0xf5, 0x1c, 0xae, 0x36, 0xc2, 0xd5, 0x02, 0x9a, 0x00,
	0x50, 0x45, 0xe7, 0xa9, 0xa2, 0xff, 0x2d, 0x5b, 0xd1, 0x49, 0x69, 0xb6, 0x08, 0x29, 0xd2, 0xf3,
	0x84, 0xc5, 0xbe, 0xd4, 0x0e, 0x94, 0xa2, 0x0e, 0xaf, 0x73, 0x6d, 0x49, 0xb4, 0x45, 0x3c, 0xa6,
	0x11, 0x3f, 0x57, 0x1e, 0x98, 0x5d, 0xac, 0x7a, 0x30, 0x25, 0x4e, 0x2f, 0x5a, 0xb0, 0xc4, 0x2c,
	0xb8, 0x2f, 0x5b, 0xf0, 0xd1, 0x95, 0x96, 0x16, 0xd1, 0x45, 0x03, 0x07, 0x80, 0xc2, 0x2e, 0x3b,
	0x17, 0xec, 0x54, 0x83, 0xcf, 0xa3, 0xc3, 0xb6, 0x42, 0x93, 0x5a, 0x76, 0xd8, 0x46, 0x50, 0xa0,
	0x91, 0x31, 0xc7, 0x68, 0xe4, 0xf7, 0xb0, 0x04, 0x70, 0x19, 0x4a, 0xcc, 0x54, 0xfd, 0xa8, 0x00,
	0x52, 0xa4, 0x84, 0x23, 0xab, 0xa3, 0x7d, 0x42, 0x6b, 0x01, 0xf2, 0xac, 0xbe, 0x4b, 0x76, 0x6f,
	0xd4, 0xc7, 0xe7, 0x1b, 0xad, 0x14, 0x76, 0xf2, 0xd1, 0x3c, 0x8c, 0xd1, 0x50, 0xc7, 0x45, 0x60,
	0x1f, 0xda, 0x0e, 0xac, 0x87, 0x2b, 0xe7, 0xc5, 0xba, 0x9d, 0x8b, 0xdd, 0xb0, 0x4f, 0x88, 0xd2,
	0xe1, 0x23, 0x6b, 0x7f, 0x55, 0x40, 0x1b, 0x36, 0x08, 0x87, 0xea, 0x7f, 0xc1, 0x42, 0x34, 0x4a,
	0x54, 0x2d, 0x14, 0xf6, 0xf4, 0x5e, 0xaa, 0x3d, 0xb2, 0x07, 0xdc, 0x0a, 0x29, 0x9c, 0x25, 0x02,
	0x1e, 0x6a, 0x27, 0x1a, 0xd4, 0x1a, 0x2c, 0x66, 0xb0, 0xa7, 0x6c, 0xf5, 0x79, 0x11, 0x28, 0xd3,
	0xa2, 0xc1, 0x1f, 0x52, 0x83, 0xef, 0x9b, 0xde, 0x59, 0xc3, 0x71, 0x6c, 0x41, 0x3f, 0xae, 0xe3,
	0xd8, 0x46, 0x60, 0x05, 0x1c, 0xd2, 0x25, 0xbd, 0x44, 0x28, 0x2d, 0x42, 0xd0, 0xde, 0xa1, 0xf6,
	0x1a, 0x74, 0xe2, 0xfa, 0x18, 0xe2, 0x16, 0x75, 0x58, 0xa0, 0x6e, 0xbe, 0xde, 0xbb, 0xda, 0x54,
	0x43, 0xca, 0x0d, 0xda, 0x2f, 0x15, 0x9e, 0x43, 0x8b, 0x83, 0x72, 0x51, 0x74, 0x00, 0x8f, 0xbe,
	0x4b, 0x11, 0xcc, 0xf1, 0x30, 0x19, 0x75, 0x92, 0x3d, 0xb7, 0xd8, 0x73, 0x96, 0x48, 0xf5, 0x25,
	0x2f, 0xfc, 0x56, 0x3f, 0x84, 0x19, 0xb9, 0xf1, 0x32, 0x45, 0x17, 0x05, 0x45, 0xdf, 0xfd, 0x72,
	0x50, 0xf7, 0xa6, 0xc9, 0xe1, 0xe0, 0x35, 0x06, 0xad, 0xd5, 0x1f, 0x1d, 0x34, 0x1b, 0xb5, 0xdd,
	0xfa, 0x93, 0x7a, 0xed, 0x71, 0xf9, 0x0d, 0x54, 0x81, 0x79, 0xb1, 0x51, 0x78, 0xc4, 0x71, 0x1d,
	0xae, 0x89, 0x2d, 0xfc, 0xf9, 0xc6, 0xdd, 0xef, 0xf3, 0x30, 0x2f, 0x64, 0xfd, 0xd5, 0xe8, 0xce,
	0x4c, 0x78, 0x33, 0x72, 0xf4, 0xb8, 0xde, 0x8a, 0xbf, 0x19, 0x51, 0x61, 0x21, 0xad, 0xb9, 0xde,
	0x2a, 0x2b, 0x68, 0x1d, 0x56, 0x92, 0x6d, 0x7a, 0xbd, 0xf9, 0x9f, 0x86, 0x5e, 0xfb, 0xa4, 0xb6,
	0xdb, 0x2a, 0xe7, 0xd0, 0x4d, 0x58, 0xce, 0x60, 0x69, 0x54, 0x9b, 0xcd, 0x72, 0x1e, 0x6d, 0xc0,
	0x5a, 0x92, 0xe1, 0x59, 0xb5, 0xde, 0x32, 0x9e, 0x1c, 0xea, 0x5c, 0xfa, 0x02, 0x5a, 0x83, 0x1b,
	0x49, 0x2e, 0xda, 0xc8, 0xc6, 0x19, 0x43, 0x1a, 0xac, 0x66, 0x71, 0x70, 0x61, 0xc6, 0xc5, 0x27,
	0x2c, 0x29, 0x73, 0x35, 0xf6, 0xab, 0xad, 0x27, 0x87, 0xfa, 0xd3, 0xf2, 0x44, 0xfa, 0xa2, 0xe9,
	0x44, 0x45, 0x74, 0x03, 0x2a, 0x29, 0x2b, 0x62, 0x53, 0x94, 0xd0, 0x26, 0x6c, 0x64, 0x89, 0xc1,
	0x9e, 0xcd, 0xec, 0x56, 0x0f, 0x76, 0x6b, 0xfb, 0x65, 0xb8, 0x5b, 0x83, 0xd2, 0xa7, 0x51, 0xcd,
	0xac, 0x0c, 0x53, 0x9f, 0x1e, 0xd5, 0xf4, 0xe7, 0xc6, 0xce, 0x73, 0xe3, 0x88, 0xea, 0x7d, 0x1e,
	0xca, 0x84, 0x52, 0x23, 0x14, 0xda, 0xbf, 0xfe, 0xb8, 0xac, 0x48, 0x7c, 0xd5, 0xfd, 0xfd, 0x72,
	0xee, 0xee, 0x19, 0xcc, 0xc6, 0x12, 0x40, 0x62, 0x96, 0xfd, 0xea, 0x4e, 0x6d, 0xdf, 0x68, 0x1e,
	0x1e, 0xe9, 0xbb, 0xb5, 0x34, 0xf8, 0x2c, 0xc1, 0xf5, 0x24, 0x4b, 0xe3, 0x60, 0xaf, 0xac, 0x90,
	0xb5, 0x27, 0x9b, 0x9e, 0xd5, 0x76, 0x1a, 0xe5, 0xdc, 0xdd, 0x5f, 0x29, 0x62, 0xbe, 0xc0, 0xcb,
	0x3b, 0x1a, 0xac, 0xee, 0x1e, 0x56, 0x77, 0x3f, 0x36, 0x78, 0xb7, 0x56, 0xb5, 0x75, 0xd4, 0x8c,
	0xcd, 0xa7, 0xc2, 0x42, 0x0a, 0x0f, 0x59, 0x81, 0x82, 0x56, 0x41, 0x4d, 0x69, 0x23, 0x66, 0xa9,
	0x1f, 0xec, 0x95, 0x73, 0x19, 0xed, 0xcd, 0x8f, 0x0f, 0x9f, 0x91, 0xf6, 0x7c, 0x46, 0x7b, 0xed,
	0xf3, 0x46, 0x5d, 0xaf, 0x3d, 0x2e, 0x17, 0x1e, 0xfc, 0x7e, 0x11, 0x40, 0x78, 0xc7, 0x63, 0x11,
	0xdb, 0xa6, 0xbd, 0xb1, 0x43, 0xb7, 0x53, 0x8a, 0x84, 0x29, 0x2f, 0x0e, 0xd5, 0x3b, 0x23, 0xf1,
	0xf9, 0x2e, 0x7a, 0x96, 0x78, 0xec, 0x46, 0xfc, 0xd9, 0xcd, 0xec, 0x8b, 0x74, 0xfa, 0x1a, 0x4e,
	0x5d, 0xcb, 0x66, 0xe0, 0xce, 0xeb, 0x0b, 0xb8, 0x96, 0x78, 0x38, 0x84, 0xd6, 0xe3, 0x69, 0x63,
	0xa2, 0x4e, 0xa9, 0x6a, 0x97, 0xb1, 0xf8, 0x2e, 0x3a, 0x86, 0xeb, 0xa9, 0xef, 0xf7, 0xd0, 0x9b,
	0x69, 0xf5, 0xf1, 0xc4, 0x43, 0x44, 0xf5, 0xf6, 0x28, 0x6c, 0xbe, 0x4b, 0xec, 0x90, 0xfe, 0x02,
	0x2a, 0x66, 0x87, 0xcc, 0x57, 0x5b, 0x31, 0x3b, 0x0c, 0x79, 0x4e, 0xf5, 0x0d, 0xcc, 0xa5, 0x3c,
	0x27, 0x42, 0xb7, 0xa4, 0xfe, 0xe9, 0xaf, 0xa6, 0xd4, 0x8d, 0xcb, 0x99, 0x7c, 0x17, 0xb5, 0x61,
	0x3e, 0xad, 0xb6, 0x8a, 0x36, 0x92, 0x11, 0x25, 0x59, 0xcf, 0x55, 0xdf, 0x1c, 0x81, 0xcb, 0x77,
	0x51, 0x9f, 0xa7, 0x6e, 0xa9, 0xf5, 0x24, 0x74, 0x37, 0x39, 0x48, 0x56, 0xad, 0x4a, 0xbd, 0x37,
	0x32, 0xaf, 0xef, 0x22, 0x93, 0x1e, 0x00, 0x84, 0xb0, 0xc1, 0x4e, 0xd7, 0x71, 0x28, 0xa5, 0xbd,
	0xb3, 0x89, 0xad, 0x2c, 0xf3, 0xc1, 0x89, 0x07, 0x4b, 0x99, 0x2f, 0x30, 0xd0, 0x5b, 0xd2, 0x18,
	0xc3, 0x5e, 0x8e, 0xa8, 0x77, 0x47, 0x65, 0x65, 0xa0, 0x48, 0xb9, 0x23, 0x8b, 0x81, 0x22, 0xfd,
	0x1a, 0x50, 0xdd, 0xb8, 0x9c, 0x89, 0x6e, 0xff, 0x72, 0xfc, 0xee, 0x09, 0xad, 0x25, 0x14, 0x12,
	0xbb, 0xdf, 0x52, 0xd7, 0x2f, 0xe1, 0xf0, 0x5d, 0x64, 0xd3, 0xd2, 0x44, 0xda, 0xcd, 0x0d, 0xba,
	0x93, 0x76, 0x25, 0x94, 0x72, 0x9d, 0xa4, 0x6e, 0x8e, 0xc6, 0xe8, 0xbb, 0xdc, 0xd9, 0xc4, 0xde,
	0xbf, 0xac, 0xa7, 0x1a, 0x56, 0x7c, 0x39, 0x92, 0x74, 0x36, 0x29, 0x2f, 0x3b, 0xbe, 0x53, 0x60,
	0x75, 0xf8, 0xb3, 0x03, 0xb4, 0x25, 0x3f, 0x7c, 0xbc, 0xec, 0xad, 0x86, 0xba, 0x7d, 0x25, 0x7e,
	0xdf, 0x45, 0x5f, 0x51, 0x7c, 0xc7, 0x2e, 0x92, 0x50, 0x42, 0xfa, 0xe4, 0x45, 0x98, 0x7a, 0xeb,
	0x52, 0x9e, 0xc8, 0xcf, 0xa5, 0x5c, 0x00, 0x25, 0xfd, 0x5c, 0xfa, 0xed, 0x52, 0xd2, 0xcf, 0x65,
	0xdd, 0x26, 0x39, 0xf4, 0x4d, 0x57, 0x6a, 0x59, 0x1c, 0x6d, 0xc6, 0x65, 0xcd, 0xba, 0x53, 0x51,
	0xdf, 0x1a, 0x91, 0xd3, 0x77, 0xd1, 0x19, 0xcc, 0xa7, 0x95, 0x6a, 0x63, 0x93, 0x0d, 0xa9, 0x33,
	0xc7, 0x26, 0x1b, 0x56, 0xf7, 0x45, 0xaf, 0x60, 0x29, 0xb3, 0x16, 0x8a, 0xee, 0x8f, 0x5a, 0x33,
	0x65, 0xd3, 0x6e, 0x5d, 0xad, 0xc4, 0x8a, 0x0c, 0x28, 0xc7, 0x8b, 0x7a, 0x31, 0x0f, 0x98, 0x51,
	0x2b, 0x8c, 0x79, 0xc0, 0xac, 0xca, 0x20, 0x99, 0x20, 0x5e, 0xb2, 0x8b, 0x4d, 0x90, 0x51, 0x38,
	0x8c, 0x4d, 0x90, 0x55, 0xf7, 0x43, 0xa7, 0x30, 0x97, 0x52, 0x46, 0x8b, 0xf9, 0x8b, 0xec, 0xca,
	0x60, 0xcc, 0x5f, 0x0c, 0x2b, 0xe7, 0x61, 0x40, 0xc9, 0xea, 0x54, 0x46, 0x50, 0x4f, 0x2e, 0xe7,
	0xce, 0x88, 0x65, 0x2e, 0x14, 0xc0, 0x62, 0x46, 0x01, 0x03, 0xdd, 0x1b, 0xad, 0xcc, 0xc1, 0x26,
	0x7c, 0xfb, 0x2a, 0x35, 0x11, 0xd4, 0x82, 0xd9, 0x58, 0x21, 0x22, 0x76, 0x9e, 0x4b, 0x16, 0x47,
	0xd4, 0xb5, 0xe1, 0x0c, 0xbe, 0x8b, 0xfe, 0x7b, 0x50, 0x12, 0x4c, 0x26, 0xff, 0x31, 0x0f, 0x78,
	0x69, 0xed, 0x22, 0xe6, 0x01, 0x47, 0x28, 0x53, 0x34, 0x60, 0x52, 0xc8, 0xd6, 0x93, 0x0b, 0x8a,
	0x65, 0xe4, 0xc9, 0x05, 0x25, 0xb2, 0xeb, 0x2f, 0x61, 0x36, 0x96, 0x3e, 0xc7, 0x02, 0x6b, 0x7a,
	0xae, 0xaf, 0x6e, 0x8c, 0x92, 0x81, 0xef, 0x6c, 0x7f, 0x71, 0xff, 0xc4, 0xb1, 0xcd, 0xde, 0xc9,
	0xd6, 0xbb, 0x0f, 0x82, 0x60, 0xab, 0xed, 0x74, 0xb7, 0xe9, 0x7f, 0x0a, 0xb5, 0x1d, 0x7b, 0xdb,
	0xc7, 0xde, 0x4b, 0xab, 0x8d, 0xfd, 0x6d, 0x61, 0xa0, 0x17, 0xe3, 0xb4, 0xf9, 0xe1, 0xdf, 0x03,
	0x00, 0x00, 0xff, 0xff, 0x6e, 0x08, 0x37, 0x73, 0xb5, 0x34, 0x00, 0x00,
}
