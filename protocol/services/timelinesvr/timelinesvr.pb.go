// Code generated by protoc-gen-gogo.
// source: src/timelinesvr/timelinesvr.proto
// DO NOT EDIT!

/*
	Package Timeline is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/timelinesvr/timelinesvr.proto

	It has these top-level messages:
		ExtContentCase
		ExtContentPart
		ExtContent
		TimelineMsg
		ImMsg
		GuildApplyMsg
		DelImMsg
		QuitGuildMsg
		ImGuildGroupMemModifyMsg
		WriteTimelineMsgReq
		WriteTimelineMsgResp
		BatchWriteTimelineMsgReq
		BatchWriteTimelineMsgResp
		PullTimelineMsgReq
		PullTimelineMsgResp
		ImMsgWriteReq
		ImMsgWriteRsp
		PullImMsgReq
		PullImMsgRsp
		ReadStatus
		BatchSetReadStatusReq
		BatchSetReadStatusResp
		BatchGetReadStatusReq
		BatchGetReadStatusResp
		ReadStatusList
		CheckMsgKeyExistReq
		CheckMsgKeyExistResp
		SetMsgKeyReq
		SetMsgKeyResp
		TimelineKey
		TimelineAsyncJobReq
		GroupTimelineIndex
		ClearReadStatusReq
		ClearReadStatusResp
		SetPeerReadStatusReq
		SetPeerReadStatusResp
		BatchGetPeerReadStatusReq
		BatchGetPeerReadStatusResp
		PeerReadStatus
		ClearGroupTimelineMsgReq
		ClearGroupTimelineMsgResp
*/
package Timeline

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto2 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type PUBLIC_IM_TYPE int32

const (
	PUBLIC_IM_TYPE_COMMON_MSG   PUBLIC_IM_TYPE = 1
	PUBLIC_IM_TYPE_PREORDER_MSG PUBLIC_IM_TYPE = 2
)

var PUBLIC_IM_TYPE_name = map[int32]string{
	1: "COMMON_MSG",
	2: "PREORDER_MSG",
}
var PUBLIC_IM_TYPE_value = map[string]int32{
	"COMMON_MSG":   1,
	"PREORDER_MSG": 2,
}

func (x PUBLIC_IM_TYPE) Enum() *PUBLIC_IM_TYPE {
	p := new(PUBLIC_IM_TYPE)
	*p = x
	return p
}
func (x PUBLIC_IM_TYPE) String() string {
	return proto.EnumName(PUBLIC_IM_TYPE_name, int32(x))
}
func (x *PUBLIC_IM_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(PUBLIC_IM_TYPE_value, data, "PUBLIC_IM_TYPE")
	if err != nil {
		return err
	}
	*x = PUBLIC_IM_TYPE(value)
	return nil
}
func (PUBLIC_IM_TYPE) EnumDescriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{0} }

// 废弃定义 后续不再使用
type Platform int32

const (
	Platform_Android     Platform = 0
	Platform_iOS         Platform = 1
	Platform_UNSPECIFIED Platform = 65535
)

var Platform_name = map[int32]string{
	0:     "Android",
	1:     "iOS",
	65535: "UNSPECIFIED",
}
var Platform_value = map[string]int32{
	"Android":     0,
	"iOS":         1,
	"UNSPECIFIED": 65535,
}

func (x Platform) Enum() *Platform {
	p := new(Platform)
	*p = x
	return p
}
func (x Platform) String() string {
	return proto.EnumName(Platform_name, int32(x))
}
func (x *Platform) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(Platform_value, data, "Platform")
	if err != nil {
		return err
	}
	*x = Platform(value)
	return nil
}
func (Platform) EnumDescriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{1} }

type ExtContent_OP_TYPE int32

const (
	ExtContent_MEM_REMOVE ExtContent_OP_TYPE = 1
)

var ExtContent_OP_TYPE_name = map[int32]string{
	1: "MEM_REMOVE",
}
var ExtContent_OP_TYPE_value = map[string]int32{
	"MEM_REMOVE": 1,
}

func (x ExtContent_OP_TYPE) Enum() *ExtContent_OP_TYPE {
	p := new(ExtContent_OP_TYPE)
	*p = x
	return p
}
func (x ExtContent_OP_TYPE) String() string {
	return proto.EnumName(ExtContent_OP_TYPE_name, int32(x))
}
func (x *ExtContent_OP_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(ExtContent_OP_TYPE_value, data, "ExtContent_OP_TYPE")
	if err != nil {
		return err
	}
	*x = ExtContent_OP_TYPE(value)
	return nil
}
func (ExtContent_OP_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{2, 0}
}

type TimelineMsg_TYPE int32

const (
	TimelineMsg_IM_MSG                    TimelineMsg_TYPE = 1
	TimelineMsg_GUILD_APPLY               TimelineMsg_TYPE = 2
	TimelineMsg_DEL_IM_MSG                TimelineMsg_TYPE = 3
	TimelineMsg_GUILD_QUIT                TimelineMsg_TYPE = 4
	TimelineMsg_IM_GUILD_GROUP_MEM_MODIFY TimelineMsg_TYPE = 5
	TimelineMsg_GROUP_TL_INDEX            TimelineMsg_TYPE = 6
)

var TimelineMsg_TYPE_name = map[int32]string{
	1: "IM_MSG",
	2: "GUILD_APPLY",
	3: "DEL_IM_MSG",
	4: "GUILD_QUIT",
	5: "IM_GUILD_GROUP_MEM_MODIFY",
	6: "GROUP_TL_INDEX",
}
var TimelineMsg_TYPE_value = map[string]int32{
	"IM_MSG":                    1,
	"GUILD_APPLY":               2,
	"DEL_IM_MSG":                3,
	"GUILD_QUIT":                4,
	"IM_GUILD_GROUP_MEM_MODIFY": 5,
	"GROUP_TL_INDEX":            6,
}

func (x TimelineMsg_TYPE) Enum() *TimelineMsg_TYPE {
	p := new(TimelineMsg_TYPE)
	*p = x
	return p
}
func (x TimelineMsg_TYPE) String() string {
	return proto.EnumName(TimelineMsg_TYPE_name, int32(x))
}
func (x *TimelineMsg_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(TimelineMsg_TYPE_value, data, "TimelineMsg_TYPE")
	if err != nil {
		return err
	}
	*x = TimelineMsg_TYPE(value)
	return nil
}
func (TimelineMsg_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{3, 0}
}

type GuildApplyMsg_STATUS int32

const (
	GuildApplyMsg_UN_HANDLE GuildApplyMsg_STATUS = 0
	GuildApplyMsg_DONE      GuildApplyMsg_STATUS = 1
)

var GuildApplyMsg_STATUS_name = map[int32]string{
	0: "UN_HANDLE",
	1: "DONE",
}
var GuildApplyMsg_STATUS_value = map[string]int32{
	"UN_HANDLE": 0,
	"DONE":      1,
}

func (x GuildApplyMsg_STATUS) Enum() *GuildApplyMsg_STATUS {
	p := new(GuildApplyMsg_STATUS)
	*p = x
	return p
}
func (x GuildApplyMsg_STATUS) String() string {
	return proto.EnumName(GuildApplyMsg_STATUS_name, int32(x))
}
func (x *GuildApplyMsg_STATUS) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(GuildApplyMsg_STATUS_value, data, "GuildApplyMsg_STATUS")
	if err != nil {
		return err
	}
	*x = GuildApplyMsg_STATUS(value)
	return nil
}
func (GuildApplyMsg_STATUS) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{5, 0}
}

type QuitGuildMsg_QUIT_TYPE int32

const (
	QuitGuildMsg_QUIT   QuitGuildMsg_QUIT_TYPE = 1
	QuitGuildMsg_KICKED QuitGuildMsg_QUIT_TYPE = 2
)

var QuitGuildMsg_QUIT_TYPE_name = map[int32]string{
	1: "QUIT",
	2: "KICKED",
}
var QuitGuildMsg_QUIT_TYPE_value = map[string]int32{
	"QUIT":   1,
	"KICKED": 2,
}

func (x QuitGuildMsg_QUIT_TYPE) Enum() *QuitGuildMsg_QUIT_TYPE {
	p := new(QuitGuildMsg_QUIT_TYPE)
	*p = x
	return p
}
func (x QuitGuildMsg_QUIT_TYPE) String() string {
	return proto.EnumName(QuitGuildMsg_QUIT_TYPE_name, int32(x))
}
func (x *QuitGuildMsg_QUIT_TYPE) UnmarshalJSON(data []byte) error {
	value, err := proto.UnmarshalJSONEnum(QuitGuildMsg_QUIT_TYPE_value, data, "QuitGuildMsg_QUIT_TYPE")
	if err != nil {
		return err
	}
	*x = QuitGuildMsg_QUIT_TYPE(value)
	return nil
}
func (QuitGuildMsg_QUIT_TYPE) EnumDescriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{7, 0}
}

type ExtContentCase struct {
	CaseUidList []uint32 `protobuf:"varint,1,rep,name=case_uid_list,json=caseUidList" json:"case_uid_list,omitempty"`
	Content     string   `protobuf:"bytes,2,req,name=content" json:"content"`
}

func (m *ExtContentCase) Reset()                    { *m = ExtContentCase{} }
func (m *ExtContentCase) String() string            { return proto.CompactTextString(m) }
func (*ExtContentCase) ProtoMessage()               {}
func (*ExtContentCase) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{0} }

func (m *ExtContentCase) GetCaseUidList() []uint32 {
	if m != nil {
		return m.CaseUidList
	}
	return nil
}

func (m *ExtContentCase) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type ExtContentPart struct {
	CaseList       []*ExtContentCase `protobuf:"bytes,1,rep,name=case_list,json=caseList" json:"case_list,omitempty"`
	DetaultContent string            `protobuf:"bytes,2,req,name=detault_content,json=detaultContent" json:"detault_content"`
}

func (m *ExtContentPart) Reset()                    { *m = ExtContentPart{} }
func (m *ExtContentPart) String() string            { return proto.CompactTextString(m) }
func (*ExtContentPart) ProtoMessage()               {}
func (*ExtContentPart) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{1} }

func (m *ExtContentPart) GetCaseList() []*ExtContentCase {
	if m != nil {
		return m.CaseList
	}
	return nil
}

func (m *ExtContentPart) GetDetaultContent() string {
	if m != nil {
		return m.DetaultContent
	}
	return ""
}

// 群系统消息..
type ExtContent struct {
	ContentPartList []*ExtContentPart `protobuf:"bytes,1,rep,name=content_part_list,json=contentPartList" json:"content_part_list,omitempty"`
	OpType          uint32            `protobuf:"varint,2,opt,name=op_type,json=opType" json:"op_type"`
	TargetUids      []uint32          `protobuf:"varint,3,rep,name=target_uids,json=targetUids" json:"target_uids,omitempty"`
}

func (m *ExtContent) Reset()                    { *m = ExtContent{} }
func (m *ExtContent) String() string            { return proto.CompactTextString(m) }
func (*ExtContent) ProtoMessage()               {}
func (*ExtContent) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{2} }

func (m *ExtContent) GetContentPartList() []*ExtContentPart {
	if m != nil {
		return m.ContentPartList
	}
	return nil
}

func (m *ExtContent) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

func (m *ExtContent) GetTargetUids() []uint32 {
	if m != nil {
		return m.TargetUids
	}
	return nil
}

// ------------------------------------------
// timeline的通用存储结构，所有消息都是包含在这个结构体里面。
// ------------------------------------------
type TimelineMsg struct {
	Type   uint32 `protobuf:"varint,1,req,name=type" json:"type"`
	Seqid  uint32 `protobuf:"varint,2,req,name=seqid" json:"seqid"`
	MsgBin []byte `protobuf:"bytes,3,req,name=msg_bin,json=msgBin" json:"msg_bin"`
}

func (m *TimelineMsg) Reset()                    { *m = TimelineMsg{} }
func (m *TimelineMsg) String() string            { return proto.CompactTextString(m) }
func (*TimelineMsg) ProtoMessage()               {}
func (*TimelineMsg) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{3} }

func (m *TimelineMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *TimelineMsg) GetSeqid() uint32 {
	if m != nil {
		return m.Seqid
	}
	return 0
}

func (m *TimelineMsg) GetMsgBin() []byte {
	if m != nil {
		return m.MsgBin
	}
	return nil
}

// 1、IM聊天消息的结构体定义
type ImMsg struct {
	FromId             uint32      `protobuf:"varint,1,req,name=from_id,json=fromId" json:"from_id"`
	ToId               uint32      `protobuf:"varint,2,req,name=to_id,json=toId" json:"to_id"`
	FromName           string      `protobuf:"bytes,3,req,name=from_name,json=fromName" json:"from_name"`
	ToName             string      `protobuf:"bytes,4,req,name=to_name,json=toName" json:"to_name"`
	FromNick           string      `protobuf:"bytes,5,req,name=from_nick,json=fromNick" json:"from_nick"`
	ToNick             string      `protobuf:"bytes,6,req,name=to_nick,json=toNick" json:"to_nick"`
	Content            string      `protobuf:"bytes,7,req,name=content" json:"content"`
	Type               uint32      `protobuf:"varint,8,req,name=type" json:"type"`
	ClientMsgTime      uint32      `protobuf:"varint,9,req,name=client_msg_time,json=clientMsgTime" json:"client_msg_time"`
	Status             uint32      `protobuf:"varint,10,req,name=status" json:"status"`
	ServerMsgId        uint32      `protobuf:"varint,11,req,name=server_msg_id,json=serverMsgId" json:"server_msg_id"`
	ServerMsgTime      uint32      `protobuf:"varint,12,req,name=server_msg_time,json=serverMsgTime" json:"server_msg_time"`
	Thumb              []byte      `protobuf:"bytes,13,opt,name=thumb" json:"thumb"`
	HasAttachment      bool        `protobuf:"varint,14,req,name=has_attachment,json=hasAttachment" json:"has_attachment"`
	AttachmentProperty []byte      `protobuf:"bytes,15,opt,name=attachment_property,json=attachmentProperty" json:"attachment_property"`
	SenderLoginKey     string      `protobuf:"bytes,16,opt,name=sender_login_key,json=senderLoginKey" json:"sender_login_key"`
	ClientMsgId        uint32      `protobuf:"varint,17,opt,name=client_msg_id,json=clientMsgId" json:"client_msg_id"`
	SpecialContent     *ExtContent `protobuf:"bytes,18,opt,name=special_content,json=specialContent" json:"special_content,omitempty"`
	ImgFormat          uint32      `protobuf:"varint,19,opt,name=img_format,json=imgFormat" json:"img_format"`
	GuildId            uint32      `protobuf:"varint,20,opt,name=guild_id,json=guildId" json:"guild_id"`
	ExceedTime         uint32      `protobuf:"varint,21,opt,name=exceed_time,json=exceedTime" json:"exceed_time"`
	OldVerContent      string      `protobuf:"bytes,22,opt,name=old_ver_content,json=oldVerContent" json:"old_ver_content"`
	Ext                []byte      `protobuf:"bytes,23,opt,name=ext" json:"ext"`
	// v1.8.1公会优化，公会消息带上等级和勋章列表
	Level            uint32   `protobuf:"varint,24,opt,name=level" json:"level"`
	MedalList        []uint32 `protobuf:"varint,25,rep,name=medal_list,json=medalList" json:"medal_list,omitempty"`
	Origin           uint32   `protobuf:"varint,26,opt,name=origin" json:"origin"`
	SyncKey          uint32   `protobuf:"varint,27,opt,name=sync_key,json=syncKey" json:"sync_key"`
	Platform         uint32   `protobuf:"varint,28,opt,name=platform" json:"platform"`
	PublicType       uint32   `protobuf:"varint,29,opt,name=public_type,json=publicType" json:"public_type"`
	GuildMemLevel    uint32   `protobuf:"varint,30,opt,name=guild_mem_level,json=guildMemLevel" json:"guild_mem_level"`
	TargetMsgId      uint32   `protobuf:"varint,31,opt,name=target_msg_id,json=targetMsgId" json:"target_msg_id"`
	MsgSourceType    uint32   `protobuf:"varint,32,opt,name=msg_source_type,json=msgSourceType" json:"msg_source_type"`
	MsgSensitiveType uint32   `protobuf:"varint,33,opt,name=msg_sensitive_type,json=msgSensitiveType" json:"msg_sensitive_type"`
	Label            uint32   `protobuf:"varint,34,opt,name=label" json:"label"`
	MsgRedpointFlag  uint32   `protobuf:"varint,35,opt,name=msg_redpoint_flag,json=msgRedpointFlag" json:"msg_redpoint_flag"`
	MsgExposureFlag  uint32   `protobuf:"varint,36,opt,name=msg_exposure_flag,json=msgExposureFlag" json:"msg_exposure_flag"`
	AppName          string   `protobuf:"bytes,39,opt,name=app_name,json=appName" json:"app_name"`
	AppPlatform      string   `protobuf:"bytes,40,opt,name=app_platform,json=appPlatform" json:"app_platform"`
	SuperPlayerLevel uint32   `protobuf:"varint,41,opt,name=super_player_level,json=superPlayerLevel" json:"super_player_level"`
}

func (m *ImMsg) Reset()                    { *m = ImMsg{} }
func (m *ImMsg) String() string            { return proto.CompactTextString(m) }
func (*ImMsg) ProtoMessage()               {}
func (*ImMsg) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{4} }

func (m *ImMsg) GetFromId() uint32 {
	if m != nil {
		return m.FromId
	}
	return 0
}

func (m *ImMsg) GetToId() uint32 {
	if m != nil {
		return m.ToId
	}
	return 0
}

func (m *ImMsg) GetFromName() string {
	if m != nil {
		return m.FromName
	}
	return ""
}

func (m *ImMsg) GetToName() string {
	if m != nil {
		return m.ToName
	}
	return ""
}

func (m *ImMsg) GetFromNick() string {
	if m != nil {
		return m.FromNick
	}
	return ""
}

func (m *ImMsg) GetToNick() string {
	if m != nil {
		return m.ToNick
	}
	return ""
}

func (m *ImMsg) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ImMsg) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *ImMsg) GetClientMsgTime() uint32 {
	if m != nil {
		return m.ClientMsgTime
	}
	return 0
}

func (m *ImMsg) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *ImMsg) GetServerMsgId() uint32 {
	if m != nil {
		return m.ServerMsgId
	}
	return 0
}

func (m *ImMsg) GetServerMsgTime() uint32 {
	if m != nil {
		return m.ServerMsgTime
	}
	return 0
}

func (m *ImMsg) GetThumb() []byte {
	if m != nil {
		return m.Thumb
	}
	return nil
}

func (m *ImMsg) GetHasAttachment() bool {
	if m != nil {
		return m.HasAttachment
	}
	return false
}

func (m *ImMsg) GetAttachmentProperty() []byte {
	if m != nil {
		return m.AttachmentProperty
	}
	return nil
}

func (m *ImMsg) GetSenderLoginKey() string {
	if m != nil {
		return m.SenderLoginKey
	}
	return ""
}

func (m *ImMsg) GetClientMsgId() uint32 {
	if m != nil {
		return m.ClientMsgId
	}
	return 0
}

func (m *ImMsg) GetSpecialContent() *ExtContent {
	if m != nil {
		return m.SpecialContent
	}
	return nil
}

func (m *ImMsg) GetImgFormat() uint32 {
	if m != nil {
		return m.ImgFormat
	}
	return 0
}

func (m *ImMsg) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ImMsg) GetExceedTime() uint32 {
	if m != nil {
		return m.ExceedTime
	}
	return 0
}

func (m *ImMsg) GetOldVerContent() string {
	if m != nil {
		return m.OldVerContent
	}
	return ""
}

func (m *ImMsg) GetExt() []byte {
	if m != nil {
		return m.Ext
	}
	return nil
}

func (m *ImMsg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ImMsg) GetMedalList() []uint32 {
	if m != nil {
		return m.MedalList
	}
	return nil
}

func (m *ImMsg) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *ImMsg) GetSyncKey() uint32 {
	if m != nil {
		return m.SyncKey
	}
	return 0
}

func (m *ImMsg) GetPlatform() uint32 {
	if m != nil {
		return m.Platform
	}
	return 0
}

func (m *ImMsg) GetPublicType() uint32 {
	if m != nil {
		return m.PublicType
	}
	return 0
}

func (m *ImMsg) GetGuildMemLevel() uint32 {
	if m != nil {
		return m.GuildMemLevel
	}
	return 0
}

func (m *ImMsg) GetTargetMsgId() uint32 {
	if m != nil {
		return m.TargetMsgId
	}
	return 0
}

func (m *ImMsg) GetMsgSourceType() uint32 {
	if m != nil {
		return m.MsgSourceType
	}
	return 0
}

func (m *ImMsg) GetMsgSensitiveType() uint32 {
	if m != nil {
		return m.MsgSensitiveType
	}
	return 0
}

func (m *ImMsg) GetLabel() uint32 {
	if m != nil {
		return m.Label
	}
	return 0
}

func (m *ImMsg) GetMsgRedpointFlag() uint32 {
	if m != nil {
		return m.MsgRedpointFlag
	}
	return 0
}

func (m *ImMsg) GetMsgExposureFlag() uint32 {
	if m != nil {
		return m.MsgExposureFlag
	}
	return 0
}

func (m *ImMsg) GetAppName() string {
	if m != nil {
		return m.AppName
	}
	return ""
}

func (m *ImMsg) GetAppPlatform() string {
	if m != nil {
		return m.AppPlatform
	}
	return ""
}

func (m *ImMsg) GetSuperPlayerLevel() uint32 {
	if m != nil {
		return m.SuperPlayerLevel
	}
	return 0
}

// 2、用户申请加入工会
type GuildApplyMsg struct {
	FromId       uint32 `protobuf:"varint,1,req,name=from_id,json=fromId" json:"from_id"`
	FromName     string `protobuf:"bytes,2,req,name=from_name,json=fromName" json:"from_name"`
	FromNick     string `protobuf:"bytes,3,req,name=from_nick,json=fromNick" json:"from_nick"`
	GuildId      uint32 `protobuf:"varint,4,req,name=guild_id,json=guildId" json:"guild_id"`
	VerifyMsg    string `protobuf:"bytes,5,req,name=verify_msg,json=verifyMsg" json:"verify_msg"`
	Status       uint32 `protobuf:"varint,6,req,name=status" json:"status"`
	ApplyTime    uint32 `protobuf:"varint,7,req,name=apply_time,json=applyTime" json:"apply_time"`
	ApplyId      uint32 `protobuf:"varint,8,req,name=apply_id,json=applyId" json:"apply_id"`
	GroupAccount string `protobuf:"bytes,9,opt,name=group_account,json=groupAccount" json:"group_account"`
}

func (m *GuildApplyMsg) Reset()                    { *m = GuildApplyMsg{} }
func (m *GuildApplyMsg) String() string            { return proto.CompactTextString(m) }
func (*GuildApplyMsg) ProtoMessage()               {}
func (*GuildApplyMsg) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{5} }

func (m *GuildApplyMsg) GetFromId() uint32 {
	if m != nil {
		return m.FromId
	}
	return 0
}

func (m *GuildApplyMsg) GetFromName() string {
	if m != nil {
		return m.FromName
	}
	return ""
}

func (m *GuildApplyMsg) GetFromNick() string {
	if m != nil {
		return m.FromNick
	}
	return ""
}

func (m *GuildApplyMsg) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GuildApplyMsg) GetVerifyMsg() string {
	if m != nil {
		return m.VerifyMsg
	}
	return ""
}

func (m *GuildApplyMsg) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *GuildApplyMsg) GetApplyTime() uint32 {
	if m != nil {
		return m.ApplyTime
	}
	return 0
}

func (m *GuildApplyMsg) GetApplyId() uint32 {
	if m != nil {
		return m.ApplyId
	}
	return 0
}

func (m *GuildApplyMsg) GetGroupAccount() string {
	if m != nil {
		return m.GroupAccount
	}
	return ""
}

// 3、删除IM消息
type DelImMsg struct {
	TargetAccount string `protobuf:"bytes,1,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *DelImMsg) Reset()                    { *m = DelImMsg{} }
func (m *DelImMsg) String() string            { return proto.CompactTextString(m) }
func (*DelImMsg) ProtoMessage()               {}
func (*DelImMsg) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{6} }

func (m *DelImMsg) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

// 4、退出工会系统通知
type QuitGuildMsg struct {
	GuildId         uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GuildAccount    string `protobuf:"bytes,2,req,name=guild_account,json=guildAccount" json:"guild_account"`
	QuitUid         uint32 `protobuf:"varint,3,req,name=quit_uid,json=quitUid" json:"quit_uid"`
	QuitUserAccount string `protobuf:"bytes,4,req,name=quit_user_account,json=quitUserAccount" json:"quit_user_account"`
	OpUid           uint32 `protobuf:"varint,5,req,name=op_uid,json=opUid" json:"op_uid"`
	OpAccount       string `protobuf:"bytes,6,req,name=op_account,json=opAccount" json:"op_account"`
	QuitType        uint32 `protobuf:"varint,7,req,name=quit_type,json=quitType" json:"quit_type"`
}

func (m *QuitGuildMsg) Reset()                    { *m = QuitGuildMsg{} }
func (m *QuitGuildMsg) String() string            { return proto.CompactTextString(m) }
func (*QuitGuildMsg) ProtoMessage()               {}
func (*QuitGuildMsg) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{7} }

func (m *QuitGuildMsg) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *QuitGuildMsg) GetGuildAccount() string {
	if m != nil {
		return m.GuildAccount
	}
	return ""
}

func (m *QuitGuildMsg) GetQuitUid() uint32 {
	if m != nil {
		return m.QuitUid
	}
	return 0
}

func (m *QuitGuildMsg) GetQuitUserAccount() string {
	if m != nil {
		return m.QuitUserAccount
	}
	return ""
}

func (m *QuitGuildMsg) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

func (m *QuitGuildMsg) GetOpAccount() string {
	if m != nil {
		return m.OpAccount
	}
	return ""
}

func (m *QuitGuildMsg) GetQuitType() uint32 {
	if m != nil {
		return m.QuitType
	}
	return 0
}

// 5、群成员资料有变化
type ImGuildGroupMemModifyMsg struct {
	GuildId     uint32 `protobuf:"varint,1,req,name=guild_id,json=guildId" json:"guild_id"`
	GroupId     uint32 `protobuf:"varint,2,req,name=group_id,json=groupId" json:"group_id"`
	TargetGroup string `protobuf:"bytes,3,req,name=target_group,json=targetGroup" json:"target_group"`
	MemUid      uint32 `protobuf:"varint,4,req,name=mem_uid,json=memUid" json:"mem_uid"`
}

func (m *ImGuildGroupMemModifyMsg) Reset()         { *m = ImGuildGroupMemModifyMsg{} }
func (m *ImGuildGroupMemModifyMsg) String() string { return proto.CompactTextString(m) }
func (*ImGuildGroupMemModifyMsg) ProtoMessage()    {}
func (*ImGuildGroupMemModifyMsg) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{8}
}

func (m *ImGuildGroupMemModifyMsg) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ImGuildGroupMemModifyMsg) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *ImGuildGroupMemModifyMsg) GetTargetGroup() string {
	if m != nil {
		return m.TargetGroup
	}
	return ""
}

func (m *ImGuildGroupMemModifyMsg) GetMemUid() uint32 {
	if m != nil {
		return m.MemUid
	}
	return 0
}

// ------------------------------------------
// 读写协议
// ------------------------------------------
type WriteTimelineMsgReq struct {
	Id     uint32       `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string       `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	Msg    *TimelineMsg `protobuf:"bytes,3,req,name=msg" json:"msg,omitempty"`
}

func (m *WriteTimelineMsgReq) Reset()                    { *m = WriteTimelineMsgReq{} }
func (m *WriteTimelineMsgReq) String() string            { return proto.CompactTextString(m) }
func (*WriteTimelineMsgReq) ProtoMessage()               {}
func (*WriteTimelineMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{9} }

func (m *WriteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *WriteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *WriteTimelineMsgReq) GetMsg() *TimelineMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

type WriteTimelineMsgResp struct {
}

func (m *WriteTimelineMsgResp) Reset()                    { *m = WriteTimelineMsgResp{} }
func (m *WriteTimelineMsgResp) String() string            { return proto.CompactTextString(m) }
func (*WriteTimelineMsgResp) ProtoMessage()               {}
func (*WriteTimelineMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{10} }

type BatchWriteTimelineMsgReq struct {
	Id      uint32         `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix  string         `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	MsgList []*TimelineMsg `protobuf:"bytes,3,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *BatchWriteTimelineMsgReq) Reset()         { *m = BatchWriteTimelineMsgReq{} }
func (m *BatchWriteTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*BatchWriteTimelineMsgReq) ProtoMessage()    {}
func (*BatchWriteTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{11}
}

func (m *BatchWriteTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *BatchWriteTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *BatchWriteTimelineMsgReq) GetMsgList() []*TimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

type BatchWriteTimelineMsgResp struct {
}

func (m *BatchWriteTimelineMsgResp) Reset()         { *m = BatchWriteTimelineMsgResp{} }
func (m *BatchWriteTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*BatchWriteTimelineMsgResp) ProtoMessage()    {}
func (*BatchWriteTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{12}
}

type PullTimelineMsgReq struct {
	Id         uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix     string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
	StartSeqid uint32 `protobuf:"varint,3,req,name=start_seqid,json=startSeqid" json:"start_seqid"`
	Limit      uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
	SkipCache  bool   `protobuf:"varint,5,opt,name=skip_cache,json=skipCache" json:"skip_cache"`
}

func (m *PullTimelineMsgReq) Reset()                    { *m = PullTimelineMsgReq{} }
func (m *PullTimelineMsgReq) String() string            { return proto.CompactTextString(m) }
func (*PullTimelineMsgReq) ProtoMessage()               {}
func (*PullTimelineMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{13} }

func (m *PullTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PullTimelineMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

func (m *PullTimelineMsgReq) GetStartSeqid() uint32 {
	if m != nil {
		return m.StartSeqid
	}
	return 0
}

func (m *PullTimelineMsgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullTimelineMsgReq) GetSkipCache() bool {
	if m != nil {
		return m.SkipCache
	}
	return false
}

type PullTimelineMsgResp struct {
	MsgList []*TimelineMsg `protobuf:"bytes,1,rep,name=msg_list,json=msgList" json:"msg_list,omitempty"`
}

func (m *PullTimelineMsgResp) Reset()                    { *m = PullTimelineMsgResp{} }
func (m *PullTimelineMsgResp) String() string            { return proto.CompactTextString(m) }
func (*PullTimelineMsgResp) ProtoMessage()               {}
func (*PullTimelineMsgResp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{14} }

func (m *PullTimelineMsgResp) GetMsgList() []*TimelineMsg {
	if m != nil {
		return m.MsgList
	}
	return nil
}

// ////////////////
type ImMsgWriteReq struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Msg    *ImMsg `protobuf:"bytes,2,req,name=msg" json:"msg,omitempty"`
	Suffix string `protobuf:"bytes,3,opt,name=suffix" json:"suffix"`
}

func (m *ImMsgWriteReq) Reset()                    { *m = ImMsgWriteReq{} }
func (m *ImMsgWriteReq) String() string            { return proto.CompactTextString(m) }
func (*ImMsgWriteReq) ProtoMessage()               {}
func (*ImMsgWriteReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{15} }

func (m *ImMsgWriteReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ImMsgWriteReq) GetMsg() *ImMsg {
	if m != nil {
		return m.Msg
	}
	return nil
}

func (m *ImMsgWriteReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type ImMsgWriteRsp struct {
	Ret int32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *ImMsgWriteRsp) Reset()                    { *m = ImMsgWriteRsp{} }
func (m *ImMsgWriteRsp) String() string            { return proto.CompactTextString(m) }
func (*ImMsgWriteRsp) ProtoMessage()               {}
func (*ImMsgWriteRsp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{16} }

func (m *ImMsgWriteRsp) GetRet() int32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
type PullImMsgReq struct {
	Id         uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	StartSeqid uint32 `protobuf:"varint,2,req,name=start_seqid,json=startSeqid" json:"start_seqid"`
	Limit      uint32 `protobuf:"varint,3,opt,name=limit" json:"limit"`
	Suffix     string `protobuf:"bytes,4,opt,name=suffix" json:"suffix"`
}

func (m *PullImMsgReq) Reset()                    { *m = PullImMsgReq{} }
func (m *PullImMsgReq) String() string            { return proto.CompactTextString(m) }
func (*PullImMsgReq) ProtoMessage()               {}
func (*PullImMsgReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{17} }

func (m *PullImMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PullImMsgReq) GetStartSeqid() uint32 {
	if m != nil {
		return m.StartSeqid
	}
	return 0
}

func (m *PullImMsgReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullImMsgReq) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type PullImMsgRsp struct {
	Ret  uint32   `protobuf:"varint,1,req,name=ret" json:"ret"`
	Msgs []*ImMsg `protobuf:"bytes,2,rep,name=msgs" json:"msgs,omitempty"`
}

func (m *PullImMsgRsp) Reset()                    { *m = PullImMsgRsp{} }
func (m *PullImMsgRsp) String() string            { return proto.CompactTextString(m) }
func (*PullImMsgRsp) ProtoMessage()               {}
func (*PullImMsgRsp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{18} }

func (m *PullImMsgRsp) GetRet() uint32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *PullImMsgRsp) GetMsgs() []*ImMsg {
	if m != nil {
		return m.Msgs
	}
	return nil
}

// ////////////////
// 已读消息状态
type ReadStatus struct {
	TargetAccount string `protobuf:"bytes,1,req,name=target_account,json=targetAccount" json:"target_account"`
	SvrMsgId      uint32 `protobuf:"varint,2,req,name=svr_msg_id,json=svrMsgId" json:"svr_msg_id"`
}

func (m *ReadStatus) Reset()                    { *m = ReadStatus{} }
func (m *ReadStatus) String() string            { return proto.CompactTextString(m) }
func (*ReadStatus) ProtoMessage()               {}
func (*ReadStatus) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{19} }

func (m *ReadStatus) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

func (m *ReadStatus) GetSvrMsgId() uint32 {
	if m != nil {
		return m.SvrMsgId
	}
	return 0
}

// ////////////////
// 批量设置已读状态
type BatchSetReadStatusReq struct {
	Status       []*ReadStatus `protobuf:"bytes,1,rep,name=status" json:"status,omitempty"`
	ForceReplace bool          `protobuf:"varint,2,opt,name=force_replace,json=forceReplace" json:"force_replace"`
}

func (m *BatchSetReadStatusReq) Reset()         { *m = BatchSetReadStatusReq{} }
func (m *BatchSetReadStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetReadStatusReq) ProtoMessage()    {}
func (*BatchSetReadStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{20}
}

func (m *BatchSetReadStatusReq) GetStatus() []*ReadStatus {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *BatchSetReadStatusReq) GetForceReplace() bool {
	if m != nil {
		return m.ForceReplace
	}
	return false
}

type BatchSetReadStatusResp struct {
	Ret uint32 `protobuf:"varint,1,req,name=ret" json:"ret"`
}

func (m *BatchSetReadStatusResp) Reset()         { *m = BatchSetReadStatusResp{} }
func (m *BatchSetReadStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetReadStatusResp) ProtoMessage()    {}
func (*BatchSetReadStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{21}
}

func (m *BatchSetReadStatusResp) GetRet() uint32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

// ////////////////
// 批量读取已读状态
type BatchGetReadStatusReq struct {
	TargetAccount []string `protobuf:"bytes,1,rep,name=target_account,json=targetAccount" json:"target_account,omitempty"`
}

func (m *BatchGetReadStatusReq) Reset()         { *m = BatchGetReadStatusReq{} }
func (m *BatchGetReadStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetReadStatusReq) ProtoMessage()    {}
func (*BatchGetReadStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{22}
}

func (m *BatchGetReadStatusReq) GetTargetAccount() []string {
	if m != nil {
		return m.TargetAccount
	}
	return nil
}

type BatchGetReadStatusResp struct {
	Ret    uint32        `protobuf:"varint,1,req,name=ret" json:"ret"`
	Status []*ReadStatus `protobuf:"bytes,2,rep,name=status" json:"status,omitempty"`
}

func (m *BatchGetReadStatusResp) Reset()         { *m = BatchGetReadStatusResp{} }
func (m *BatchGetReadStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetReadStatusResp) ProtoMessage()    {}
func (*BatchGetReadStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{23}
}

func (m *BatchGetReadStatusResp) GetRet() uint32 {
	if m != nil {
		return m.Ret
	}
	return 0
}

func (m *BatchGetReadStatusResp) GetStatus() []*ReadStatus {
	if m != nil {
		return m.Status
	}
	return nil
}

type ReadStatusList struct {
	StatusList []*ReadStatus `protobuf:"bytes,1,rep,name=status_list,json=statusList" json:"status_list,omitempty"`
}

func (m *ReadStatusList) Reset()                    { *m = ReadStatusList{} }
func (m *ReadStatusList) String() string            { return proto.CompactTextString(m) }
func (*ReadStatusList) ProtoMessage()               {}
func (*ReadStatusList) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{24} }

func (m *ReadStatusList) GetStatusList() []*ReadStatus {
	if m != nil {
		return m.StatusList
	}
	return nil
}

type CheckMsgKeyExistReq struct {
	Key string `protobuf:"bytes,1,req,name=key" json:"key"`
}

func (m *CheckMsgKeyExistReq) Reset()                    { *m = CheckMsgKeyExistReq{} }
func (m *CheckMsgKeyExistReq) String() string            { return proto.CompactTextString(m) }
func (*CheckMsgKeyExistReq) ProtoMessage()               {}
func (*CheckMsgKeyExistReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{25} }

func (m *CheckMsgKeyExistReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

type CheckMsgKeyExistResp struct {
	Value []byte `protobuf:"bytes,1,opt,name=value" json:"value"`
}

func (m *CheckMsgKeyExistResp) Reset()                    { *m = CheckMsgKeyExistResp{} }
func (m *CheckMsgKeyExistResp) String() string            { return proto.CompactTextString(m) }
func (*CheckMsgKeyExistResp) ProtoMessage()               {}
func (*CheckMsgKeyExistResp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{26} }

func (m *CheckMsgKeyExistResp) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

type SetMsgKeyReq struct {
	Key   string `protobuf:"bytes,1,req,name=key" json:"key"`
	Value []byte `protobuf:"bytes,2,req,name=value" json:"value"`
	Ttl   uint32 `protobuf:"varint,3,req,name=ttl" json:"ttl"`
}

func (m *SetMsgKeyReq) Reset()                    { *m = SetMsgKeyReq{} }
func (m *SetMsgKeyReq) String() string            { return proto.CompactTextString(m) }
func (*SetMsgKeyReq) ProtoMessage()               {}
func (*SetMsgKeyReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{27} }

func (m *SetMsgKeyReq) GetKey() string {
	if m != nil {
		return m.Key
	}
	return ""
}

func (m *SetMsgKeyReq) GetValue() []byte {
	if m != nil {
		return m.Value
	}
	return nil
}

func (m *SetMsgKeyReq) GetTtl() uint32 {
	if m != nil {
		return m.Ttl
	}
	return 0
}

type SetMsgKeyResp struct {
}

func (m *SetMsgKeyResp) Reset()                    { *m = SetMsgKeyResp{} }
func (m *SetMsgKeyResp) String() string            { return proto.CompactTextString(m) }
func (*SetMsgKeyResp) ProtoMessage()               {}
func (*SetMsgKeyResp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{28} }

type TimelineKey struct {
	Id     uint32 `protobuf:"varint,1,req,name=id" json:"id"`
	Suffix string `protobuf:"bytes,2,req,name=suffix" json:"suffix"`
}

func (m *TimelineKey) Reset()                    { *m = TimelineKey{} }
func (m *TimelineKey) String() string            { return proto.CompactTextString(m) }
func (*TimelineKey) ProtoMessage()               {}
func (*TimelineKey) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{29} }

func (m *TimelineKey) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TimelineKey) GetSuffix() string {
	if m != nil {
		return m.Suffix
	}
	return ""
}

type TimelineAsyncJobReq struct {
	KeyList []*TimelineKey `protobuf:"bytes,1,rep,name=key_list,json=keyList" json:"key_list,omitempty"`
}

func (m *TimelineAsyncJobReq) Reset()                    { *m = TimelineAsyncJobReq{} }
func (m *TimelineAsyncJobReq) String() string            { return proto.CompactTextString(m) }
func (*TimelineAsyncJobReq) ProtoMessage()               {}
func (*TimelineAsyncJobReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{30} }

func (m *TimelineAsyncJobReq) GetKeyList() []*TimelineKey {
	if m != nil {
		return m.KeyList
	}
	return nil
}

type GroupTimelineIndex struct {
	GroupId uint32 `protobuf:"varint,1,req,name=group_id,json=groupId" json:"group_id"`
	SeqId   uint32 `protobuf:"varint,2,req,name=seq_id,json=seqId" json:"seq_id"`
}

func (m *GroupTimelineIndex) Reset()                    { *m = GroupTimelineIndex{} }
func (m *GroupTimelineIndex) String() string            { return proto.CompactTextString(m) }
func (*GroupTimelineIndex) ProtoMessage()               {}
func (*GroupTimelineIndex) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{31} }

func (m *GroupTimelineIndex) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupTimelineIndex) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type ClearReadStatusReq struct {
	Uid           uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	TargetAccount string `protobuf:"bytes,2,req,name=target_account,json=targetAccount" json:"target_account"`
}

func (m *ClearReadStatusReq) Reset()                    { *m = ClearReadStatusReq{} }
func (m *ClearReadStatusReq) String() string            { return proto.CompactTextString(m) }
func (*ClearReadStatusReq) ProtoMessage()               {}
func (*ClearReadStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{32} }

func (m *ClearReadStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ClearReadStatusReq) GetTargetAccount() string {
	if m != nil {
		return m.TargetAccount
	}
	return ""
}

type ClearReadStatusResp struct {
}

func (m *ClearReadStatusResp) Reset()                    { *m = ClearReadStatusResp{} }
func (m *ClearReadStatusResp) String() string            { return proto.CompactTextString(m) }
func (*ClearReadStatusResp) ProtoMessage()               {}
func (*ClearReadStatusResp) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{33} }

// ////////////////
// 设置对端已读消息ID
type SetPeerReadStatusReq struct {
	PeerUid     uint32 `protobuf:"varint,1,req,name=peer_uid,json=peerUid" json:"peer_uid"`
	SelfAccount string `protobuf:"bytes,2,req,name=self_account,json=selfAccount" json:"self_account"`
	MsgId       uint32 `protobuf:"varint,3,req,name=msg_id,json=msgId" json:"msg_id"`
}

func (m *SetPeerReadStatusReq) Reset()                    { *m = SetPeerReadStatusReq{} }
func (m *SetPeerReadStatusReq) String() string            { return proto.CompactTextString(m) }
func (*SetPeerReadStatusReq) ProtoMessage()               {}
func (*SetPeerReadStatusReq) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{34} }

func (m *SetPeerReadStatusReq) GetPeerUid() uint32 {
	if m != nil {
		return m.PeerUid
	}
	return 0
}

func (m *SetPeerReadStatusReq) GetSelfAccount() string {
	if m != nil {
		return m.SelfAccount
	}
	return ""
}

func (m *SetPeerReadStatusReq) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

type SetPeerReadStatusResp struct {
	PeerSeqId uint32 `protobuf:"varint,1,req,name=peer_seq_id,json=peerSeqId" json:"peer_seq_id"`
}

func (m *SetPeerReadStatusResp) Reset()         { *m = SetPeerReadStatusResp{} }
func (m *SetPeerReadStatusResp) String() string { return proto.CompactTextString(m) }
func (*SetPeerReadStatusResp) ProtoMessage()    {}
func (*SetPeerReadStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{35}
}

func (m *SetPeerReadStatusResp) GetPeerSeqId() uint32 {
	if m != nil {
		return m.PeerSeqId
	}
	return 0
}

type BatchGetPeerReadStatusReq struct {
	SelfUid         uint32   `protobuf:"varint,1,req,name=self_uid,json=selfUid" json:"self_uid"`
	PeerAccountList []string `protobuf:"bytes,2,rep,name=peer_account_list,json=peerAccountList" json:"peer_account_list,omitempty"`
	SelfSeqId       uint32   `protobuf:"varint,3,opt,name=self_seq_id,json=selfSeqId" json:"self_seq_id"`
}

func (m *BatchGetPeerReadStatusReq) Reset()         { *m = BatchGetPeerReadStatusReq{} }
func (m *BatchGetPeerReadStatusReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetPeerReadStatusReq) ProtoMessage()    {}
func (*BatchGetPeerReadStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{36}
}

func (m *BatchGetPeerReadStatusReq) GetSelfUid() uint32 {
	if m != nil {
		return m.SelfUid
	}
	return 0
}

func (m *BatchGetPeerReadStatusReq) GetPeerAccountList() []string {
	if m != nil {
		return m.PeerAccountList
	}
	return nil
}

func (m *BatchGetPeerReadStatusReq) GetSelfSeqId() uint32 {
	if m != nil {
		return m.SelfSeqId
	}
	return 0
}

type BatchGetPeerReadStatusResp struct {
	PeerAccountList []string          `protobuf:"bytes,1,rep,name=peer_account_list,json=peerAccountList" json:"peer_account_list,omitempty"`
	StatusList      []*PeerReadStatus `protobuf:"bytes,2,rep,name=status_list,json=statusList" json:"status_list,omitempty"`
}

func (m *BatchGetPeerReadStatusResp) Reset()         { *m = BatchGetPeerReadStatusResp{} }
func (m *BatchGetPeerReadStatusResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetPeerReadStatusResp) ProtoMessage()    {}
func (*BatchGetPeerReadStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{37}
}

func (m *BatchGetPeerReadStatusResp) GetPeerAccountList() []string {
	if m != nil {
		return m.PeerAccountList
	}
	return nil
}

func (m *BatchGetPeerReadStatusResp) GetStatusList() []*PeerReadStatus {
	if m != nil {
		return m.StatusList
	}
	return nil
}

type PeerReadStatus struct {
	MsgId uint32 `protobuf:"varint,1,req,name=msg_id,json=msgId" json:"msg_id"`
	SeqId uint32 `protobuf:"varint,2,req,name=seq_id,json=seqId" json:"seq_id"`
}

func (m *PeerReadStatus) Reset()                    { *m = PeerReadStatus{} }
func (m *PeerReadStatus) String() string            { return proto.CompactTextString(m) }
func (*PeerReadStatus) ProtoMessage()               {}
func (*PeerReadStatus) Descriptor() ([]byte, []int) { return fileDescriptorTimelinesvr, []int{38} }

func (m *PeerReadStatus) GetMsgId() uint32 {
	if m != nil {
		return m.MsgId
	}
	return 0
}

func (m *PeerReadStatus) GetSeqId() uint32 {
	if m != nil {
		return m.SeqId
	}
	return 0
}

type ClearGroupTimelineMsgReq struct {
	Id uint32 `protobuf:"varint,1,req,name=id" json:"id"`
}

func (m *ClearGroupTimelineMsgReq) Reset()         { *m = ClearGroupTimelineMsgReq{} }
func (m *ClearGroupTimelineMsgReq) String() string { return proto.CompactTextString(m) }
func (*ClearGroupTimelineMsgReq) ProtoMessage()    {}
func (*ClearGroupTimelineMsgReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{39}
}

func (m *ClearGroupTimelineMsgReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type ClearGroupTimelineMsgResp struct {
}

func (m *ClearGroupTimelineMsgResp) Reset()         { *m = ClearGroupTimelineMsgResp{} }
func (m *ClearGroupTimelineMsgResp) String() string { return proto.CompactTextString(m) }
func (*ClearGroupTimelineMsgResp) ProtoMessage()    {}
func (*ClearGroupTimelineMsgResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTimelinesvr, []int{40}
}

func init() {
	proto.RegisterType((*ExtContentCase)(nil), "Timeline.ExtContentCase")
	proto.RegisterType((*ExtContentPart)(nil), "Timeline.ExtContentPart")
	proto.RegisterType((*ExtContent)(nil), "Timeline.ExtContent")
	proto.RegisterType((*TimelineMsg)(nil), "Timeline.TimelineMsg")
	proto.RegisterType((*ImMsg)(nil), "Timeline.ImMsg")
	proto.RegisterType((*GuildApplyMsg)(nil), "Timeline.GuildApplyMsg")
	proto.RegisterType((*DelImMsg)(nil), "Timeline.DelImMsg")
	proto.RegisterType((*QuitGuildMsg)(nil), "Timeline.QuitGuildMsg")
	proto.RegisterType((*ImGuildGroupMemModifyMsg)(nil), "Timeline.ImGuildGroupMemModifyMsg")
	proto.RegisterType((*WriteTimelineMsgReq)(nil), "Timeline.WriteTimelineMsgReq")
	proto.RegisterType((*WriteTimelineMsgResp)(nil), "Timeline.WriteTimelineMsgResp")
	proto.RegisterType((*BatchWriteTimelineMsgReq)(nil), "Timeline.BatchWriteTimelineMsgReq")
	proto.RegisterType((*BatchWriteTimelineMsgResp)(nil), "Timeline.BatchWriteTimelineMsgResp")
	proto.RegisterType((*PullTimelineMsgReq)(nil), "Timeline.PullTimelineMsgReq")
	proto.RegisterType((*PullTimelineMsgResp)(nil), "Timeline.PullTimelineMsgResp")
	proto.RegisterType((*ImMsgWriteReq)(nil), "Timeline.ImMsgWriteReq")
	proto.RegisterType((*ImMsgWriteRsp)(nil), "Timeline.ImMsgWriteRsp")
	proto.RegisterType((*PullImMsgReq)(nil), "Timeline.PullImMsgReq")
	proto.RegisterType((*PullImMsgRsp)(nil), "Timeline.PullImMsgRsp")
	proto.RegisterType((*ReadStatus)(nil), "Timeline.ReadStatus")
	proto.RegisterType((*BatchSetReadStatusReq)(nil), "Timeline.BatchSetReadStatusReq")
	proto.RegisterType((*BatchSetReadStatusResp)(nil), "Timeline.BatchSetReadStatusResp")
	proto.RegisterType((*BatchGetReadStatusReq)(nil), "Timeline.BatchGetReadStatusReq")
	proto.RegisterType((*BatchGetReadStatusResp)(nil), "Timeline.BatchGetReadStatusResp")
	proto.RegisterType((*ReadStatusList)(nil), "Timeline.ReadStatusList")
	proto.RegisterType((*CheckMsgKeyExistReq)(nil), "Timeline.CheckMsgKeyExistReq")
	proto.RegisterType((*CheckMsgKeyExistResp)(nil), "Timeline.CheckMsgKeyExistResp")
	proto.RegisterType((*SetMsgKeyReq)(nil), "Timeline.SetMsgKeyReq")
	proto.RegisterType((*SetMsgKeyResp)(nil), "Timeline.SetMsgKeyResp")
	proto.RegisterType((*TimelineKey)(nil), "Timeline.TimelineKey")
	proto.RegisterType((*TimelineAsyncJobReq)(nil), "Timeline.TimelineAsyncJobReq")
	proto.RegisterType((*GroupTimelineIndex)(nil), "Timeline.GroupTimelineIndex")
	proto.RegisterType((*ClearReadStatusReq)(nil), "Timeline.ClearReadStatusReq")
	proto.RegisterType((*ClearReadStatusResp)(nil), "Timeline.ClearReadStatusResp")
	proto.RegisterType((*SetPeerReadStatusReq)(nil), "Timeline.SetPeerReadStatusReq")
	proto.RegisterType((*SetPeerReadStatusResp)(nil), "Timeline.SetPeerReadStatusResp")
	proto.RegisterType((*BatchGetPeerReadStatusReq)(nil), "Timeline.BatchGetPeerReadStatusReq")
	proto.RegisterType((*BatchGetPeerReadStatusResp)(nil), "Timeline.BatchGetPeerReadStatusResp")
	proto.RegisterType((*PeerReadStatus)(nil), "Timeline.PeerReadStatus")
	proto.RegisterType((*ClearGroupTimelineMsgReq)(nil), "Timeline.ClearGroupTimelineMsgReq")
	proto.RegisterType((*ClearGroupTimelineMsgResp)(nil), "Timeline.ClearGroupTimelineMsgResp")
	proto.RegisterEnum("Timeline.PUBLIC_IM_TYPE", PUBLIC_IM_TYPE_name, PUBLIC_IM_TYPE_value)
	proto.RegisterEnum("Timeline.Platform", Platform_name, Platform_value)
	proto.RegisterEnum("Timeline.ExtContent_OP_TYPE", ExtContent_OP_TYPE_name, ExtContent_OP_TYPE_value)
	proto.RegisterEnum("Timeline.TimelineMsg_TYPE", TimelineMsg_TYPE_name, TimelineMsg_TYPE_value)
	proto.RegisterEnum("Timeline.GuildApplyMsg_STATUS", GuildApplyMsg_STATUS_name, GuildApplyMsg_STATUS_value)
	proto.RegisterEnum("Timeline.QuitGuildMsg_QUIT_TYPE", QuitGuildMsg_QUIT_TYPE_name, QuitGuildMsg_QUIT_TYPE_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for TimeLineSvr service

type TimeLineSvrClient interface {
	ImMsgWrite(ctx context.Context, in *ImMsgWriteReq, opts ...grpc.CallOption) (*ImMsgWriteRsp, error)
	PullImMsg(ctx context.Context, in *PullImMsgReq, opts ...grpc.CallOption) (*PullImMsgRsp, error)
	BatchSetReadStatus(ctx context.Context, in *BatchSetReadStatusReq, opts ...grpc.CallOption) (*BatchSetReadStatusResp, error)
	BatchGetReadStatus(ctx context.Context, in *BatchGetReadStatusReq, opts ...grpc.CallOption) (*BatchGetReadStatusResp, error)
	CheckMsgKeyExist(ctx context.Context, in *CheckMsgKeyExistReq, opts ...grpc.CallOption) (*CheckMsgKeyExistResp, error)
	SetMsgKey(ctx context.Context, in *SetMsgKeyReq, opts ...grpc.CallOption) (*SetMsgKeyResp, error)
	WriteTimelineMsg(ctx context.Context, in *WriteTimelineMsgReq, opts ...grpc.CallOption) (*WriteTimelineMsgResp, error)
	PullTimelineMsg(ctx context.Context, in *PullTimelineMsgReq, opts ...grpc.CallOption) (*PullTimelineMsgResp, error)
	BatchWriteTimelineMsg(ctx context.Context, in *BatchWriteTimelineMsgReq, opts ...grpc.CallOption) (*BatchWriteTimelineMsgResp, error)
	ClearReadStatus(ctx context.Context, in *ClearReadStatusReq, opts ...grpc.CallOption) (*ClearReadStatusResp, error)
	SetPeerReadStatus(ctx context.Context, in *SetPeerReadStatusReq, opts ...grpc.CallOption) (*SetPeerReadStatusResp, error)
	BatchGetPeerReadStatus(ctx context.Context, in *BatchGetPeerReadStatusReq, opts ...grpc.CallOption) (*BatchGetPeerReadStatusResp, error)
	ClearGroupTimelineMsg(ctx context.Context, in *ClearGroupTimelineMsgReq, opts ...grpc.CallOption) (*ClearGroupTimelineMsgResp, error)
}

type timeLineSvrClient struct {
	cc *grpc.ClientConn
}

func NewTimeLineSvrClient(cc *grpc.ClientConn) TimeLineSvrClient {
	return &timeLineSvrClient{cc}
}

func (c *timeLineSvrClient) ImMsgWrite(ctx context.Context, in *ImMsgWriteReq, opts ...grpc.CallOption) (*ImMsgWriteRsp, error) {
	out := new(ImMsgWriteRsp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/ImMsgWrite", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) PullImMsg(ctx context.Context, in *PullImMsgReq, opts ...grpc.CallOption) (*PullImMsgRsp, error) {
	out := new(PullImMsgRsp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/PullImMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) BatchSetReadStatus(ctx context.Context, in *BatchSetReadStatusReq, opts ...grpc.CallOption) (*BatchSetReadStatusResp, error) {
	out := new(BatchSetReadStatusResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/BatchSetReadStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) BatchGetReadStatus(ctx context.Context, in *BatchGetReadStatusReq, opts ...grpc.CallOption) (*BatchGetReadStatusResp, error) {
	out := new(BatchGetReadStatusResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/BatchGetReadStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) CheckMsgKeyExist(ctx context.Context, in *CheckMsgKeyExistReq, opts ...grpc.CallOption) (*CheckMsgKeyExistResp, error) {
	out := new(CheckMsgKeyExistResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/CheckMsgKeyExist", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) SetMsgKey(ctx context.Context, in *SetMsgKeyReq, opts ...grpc.CallOption) (*SetMsgKeyResp, error) {
	out := new(SetMsgKeyResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/SetMsgKey", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) WriteTimelineMsg(ctx context.Context, in *WriteTimelineMsgReq, opts ...grpc.CallOption) (*WriteTimelineMsgResp, error) {
	out := new(WriteTimelineMsgResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/WriteTimelineMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) PullTimelineMsg(ctx context.Context, in *PullTimelineMsgReq, opts ...grpc.CallOption) (*PullTimelineMsgResp, error) {
	out := new(PullTimelineMsgResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/PullTimelineMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) BatchWriteTimelineMsg(ctx context.Context, in *BatchWriteTimelineMsgReq, opts ...grpc.CallOption) (*BatchWriteTimelineMsgResp, error) {
	out := new(BatchWriteTimelineMsgResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/BatchWriteTimelineMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) ClearReadStatus(ctx context.Context, in *ClearReadStatusReq, opts ...grpc.CallOption) (*ClearReadStatusResp, error) {
	out := new(ClearReadStatusResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/ClearReadStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) SetPeerReadStatus(ctx context.Context, in *SetPeerReadStatusReq, opts ...grpc.CallOption) (*SetPeerReadStatusResp, error) {
	out := new(SetPeerReadStatusResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/SetPeerReadStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) BatchGetPeerReadStatus(ctx context.Context, in *BatchGetPeerReadStatusReq, opts ...grpc.CallOption) (*BatchGetPeerReadStatusResp, error) {
	out := new(BatchGetPeerReadStatusResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/BatchGetPeerReadStatus", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timeLineSvrClient) ClearGroupTimelineMsg(ctx context.Context, in *ClearGroupTimelineMsgReq, opts ...grpc.CallOption) (*ClearGroupTimelineMsgResp, error) {
	out := new(ClearGroupTimelineMsgResp)
	err := grpc.Invoke(ctx, "/Timeline.TimeLineSvr/ClearGroupTimelineMsg", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for TimeLineSvr service

type TimeLineSvrServer interface {
	ImMsgWrite(context.Context, *ImMsgWriteReq) (*ImMsgWriteRsp, error)
	PullImMsg(context.Context, *PullImMsgReq) (*PullImMsgRsp, error)
	BatchSetReadStatus(context.Context, *BatchSetReadStatusReq) (*BatchSetReadStatusResp, error)
	BatchGetReadStatus(context.Context, *BatchGetReadStatusReq) (*BatchGetReadStatusResp, error)
	CheckMsgKeyExist(context.Context, *CheckMsgKeyExistReq) (*CheckMsgKeyExistResp, error)
	SetMsgKey(context.Context, *SetMsgKeyReq) (*SetMsgKeyResp, error)
	WriteTimelineMsg(context.Context, *WriteTimelineMsgReq) (*WriteTimelineMsgResp, error)
	PullTimelineMsg(context.Context, *PullTimelineMsgReq) (*PullTimelineMsgResp, error)
	BatchWriteTimelineMsg(context.Context, *BatchWriteTimelineMsgReq) (*BatchWriteTimelineMsgResp, error)
	ClearReadStatus(context.Context, *ClearReadStatusReq) (*ClearReadStatusResp, error)
	SetPeerReadStatus(context.Context, *SetPeerReadStatusReq) (*SetPeerReadStatusResp, error)
	BatchGetPeerReadStatus(context.Context, *BatchGetPeerReadStatusReq) (*BatchGetPeerReadStatusResp, error)
	ClearGroupTimelineMsg(context.Context, *ClearGroupTimelineMsgReq) (*ClearGroupTimelineMsgResp, error)
}

func RegisterTimeLineSvrServer(s *grpc.Server, srv TimeLineSvrServer) {
	s.RegisterService(&_TimeLineSvr_serviceDesc, srv)
}

func _TimeLineSvr_ImMsgWrite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ImMsgWriteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).ImMsgWrite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/ImMsgWrite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).ImMsgWrite(ctx, req.(*ImMsgWriteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_PullImMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullImMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).PullImMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/PullImMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).PullImMsg(ctx, req.(*PullImMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_BatchSetReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).BatchSetReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/BatchSetReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).BatchSetReadStatus(ctx, req.(*BatchSetReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_BatchGetReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).BatchGetReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/BatchGetReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).BatchGetReadStatus(ctx, req.(*BatchGetReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_CheckMsgKeyExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMsgKeyExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).CheckMsgKeyExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/CheckMsgKeyExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).CheckMsgKeyExist(ctx, req.(*CheckMsgKeyExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_SetMsgKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMsgKeyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).SetMsgKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/SetMsgKey",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).SetMsgKey(ctx, req.(*SetMsgKeyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_WriteTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).WriteTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/WriteTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).WriteTimelineMsg(ctx, req.(*WriteTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_PullTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).PullTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/PullTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).PullTimelineMsg(ctx, req.(*PullTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_BatchWriteTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchWriteTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).BatchWriteTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/BatchWriteTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).BatchWriteTimelineMsg(ctx, req.(*BatchWriteTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_ClearReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).ClearReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/ClearReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).ClearReadStatus(ctx, req.(*ClearReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_SetPeerReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPeerReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).SetPeerReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/SetPeerReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).SetPeerReadStatus(ctx, req.(*SetPeerReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_BatchGetPeerReadStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetPeerReadStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).BatchGetPeerReadStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/BatchGetPeerReadStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).BatchGetPeerReadStatus(ctx, req.(*BatchGetPeerReadStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimeLineSvr_ClearGroupTimelineMsg_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ClearGroupTimelineMsgReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimeLineSvrServer).ClearGroupTimelineMsg(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/Timeline.TimeLineSvr/ClearGroupTimelineMsg",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimeLineSvrServer).ClearGroupTimelineMsg(ctx, req.(*ClearGroupTimelineMsgReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _TimeLineSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "Timeline.TimeLineSvr",
	HandlerType: (*TimeLineSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ImMsgWrite",
			Handler:    _TimeLineSvr_ImMsgWrite_Handler,
		},
		{
			MethodName: "PullImMsg",
			Handler:    _TimeLineSvr_PullImMsg_Handler,
		},
		{
			MethodName: "BatchSetReadStatus",
			Handler:    _TimeLineSvr_BatchSetReadStatus_Handler,
		},
		{
			MethodName: "BatchGetReadStatus",
			Handler:    _TimeLineSvr_BatchGetReadStatus_Handler,
		},
		{
			MethodName: "CheckMsgKeyExist",
			Handler:    _TimeLineSvr_CheckMsgKeyExist_Handler,
		},
		{
			MethodName: "SetMsgKey",
			Handler:    _TimeLineSvr_SetMsgKey_Handler,
		},
		{
			MethodName: "WriteTimelineMsg",
			Handler:    _TimeLineSvr_WriteTimelineMsg_Handler,
		},
		{
			MethodName: "PullTimelineMsg",
			Handler:    _TimeLineSvr_PullTimelineMsg_Handler,
		},
		{
			MethodName: "BatchWriteTimelineMsg",
			Handler:    _TimeLineSvr_BatchWriteTimelineMsg_Handler,
		},
		{
			MethodName: "ClearReadStatus",
			Handler:    _TimeLineSvr_ClearReadStatus_Handler,
		},
		{
			MethodName: "SetPeerReadStatus",
			Handler:    _TimeLineSvr_SetPeerReadStatus_Handler,
		},
		{
			MethodName: "BatchGetPeerReadStatus",
			Handler:    _TimeLineSvr_BatchGetPeerReadStatus_Handler,
		},
		{
			MethodName: "ClearGroupTimelineMsg",
			Handler:    _TimeLineSvr_ClearGroupTimelineMsg_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/timelinesvr/timelinesvr.proto",
}

func (m *ExtContentCase) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtContentCase) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CaseUidList) > 0 {
		for _, num := range m.CaseUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	return i, nil
}

func (m *ExtContentPart) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtContentPart) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.CaseList) > 0 {
		for _, msg := range m.CaseList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.DetaultContent)))
	i += copy(dAtA[i:], m.DetaultContent)
	return i, nil
}

func (m *ExtContent) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ExtContent) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.ContentPartList) > 0 {
		for _, msg := range m.ContentPartList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.OpType))
	if len(m.TargetUids) > 0 {
		for _, num := range m.TargetUids {
			dAtA[i] = 0x18
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *TimelineMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimelineMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Seqid))
	if m.MsgBin != nil {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.MsgBin)))
		i += copy(dAtA[i:], m.MsgBin)
	}
	return i, nil
}

func (m *ImMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.FromId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ToId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.FromName)))
	i += copy(dAtA[i:], m.FromName)
	dAtA[i] = 0x22
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.ToName)))
	i += copy(dAtA[i:], m.ToName)
	dAtA[i] = 0x2a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.FromNick)))
	i += copy(dAtA[i:], m.FromNick)
	dAtA[i] = 0x32
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.ToNick)))
	i += copy(dAtA[i:], m.ToNick)
	dAtA[i] = 0x3a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Content)))
	i += copy(dAtA[i:], m.Content)
	dAtA[i] = 0x40
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Type))
	dAtA[i] = 0x48
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ClientMsgTime))
	dAtA[i] = 0x50
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x58
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ServerMsgId))
	dAtA[i] = 0x60
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ServerMsgTime))
	if m.Thumb != nil {
		dAtA[i] = 0x6a
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Thumb)))
		i += copy(dAtA[i:], m.Thumb)
	}
	dAtA[i] = 0x70
	i++
	if m.HasAttachment {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	if m.AttachmentProperty != nil {
		dAtA[i] = 0x7a
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.AttachmentProperty)))
		i += copy(dAtA[i:], m.AttachmentProperty)
	}
	dAtA[i] = 0x82
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.SenderLoginKey)))
	i += copy(dAtA[i:], m.SenderLoginKey)
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ClientMsgId))
	if m.SpecialContent != nil {
		dAtA[i] = 0x92
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SpecialContent.Size()))
		n1, err := m.SpecialContent.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ImgFormat))
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0xa8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ExceedTime))
	dAtA[i] = 0xb2
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.OldVerContent)))
	i += copy(dAtA[i:], m.OldVerContent)
	if m.Ext != nil {
		dAtA[i] = 0xba
		i++
		dAtA[i] = 0x1
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Ext)))
		i += copy(dAtA[i:], m.Ext)
	}
	dAtA[i] = 0xc0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Level))
	if len(m.MedalList) > 0 {
		for _, num := range m.MedalList {
			dAtA[i] = 0xc8
			i++
			dAtA[i] = 0x1
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0xd0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Origin))
	dAtA[i] = 0xd8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SyncKey))
	dAtA[i] = 0xe0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Platform))
	dAtA[i] = 0xe8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.PublicType))
	dAtA[i] = 0xf0
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GuildMemLevel))
	dAtA[i] = 0xf8
	i++
	dAtA[i] = 0x1
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.TargetMsgId))
	dAtA[i] = 0x80
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MsgSourceType))
	dAtA[i] = 0x88
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MsgSensitiveType))
	dAtA[i] = 0x90
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Label))
	dAtA[i] = 0x98
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MsgRedpointFlag))
	dAtA[i] = 0xa0
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MsgExposureFlag))
	dAtA[i] = 0xba
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.AppName)))
	i += copy(dAtA[i:], m.AppName)
	dAtA[i] = 0xc2
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.AppPlatform)))
	i += copy(dAtA[i:], m.AppPlatform)
	dAtA[i] = 0xc8
	i++
	dAtA[i] = 0x2
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SuperPlayerLevel))
	return i, nil
}

func (m *GuildApplyMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GuildApplyMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.FromId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.FromName)))
	i += copy(dAtA[i:], m.FromName)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.FromNick)))
	i += copy(dAtA[i:], m.FromNick)
	dAtA[i] = 0x20
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x2a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.VerifyMsg)))
	i += copy(dAtA[i:], m.VerifyMsg)
	dAtA[i] = 0x30
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Status))
	dAtA[i] = 0x38
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ApplyTime))
	dAtA[i] = 0x40
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.ApplyId))
	dAtA[i] = 0x4a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.GroupAccount)))
	i += copy(dAtA[i:], m.GroupAccount)
	return i, nil
}

func (m *DelImMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DelImMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *QuitGuildMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *QuitGuildMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.GuildAccount)))
	i += copy(dAtA[i:], m.GuildAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.QuitUid))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.QuitUserAccount)))
	i += copy(dAtA[i:], m.QuitUserAccount)
	dAtA[i] = 0x28
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.OpUid))
	dAtA[i] = 0x32
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.OpAccount)))
	i += copy(dAtA[i:], m.OpAccount)
	dAtA[i] = 0x38
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.QuitType))
	return i, nil
}

func (m *ImGuildGroupMemModifyMsg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImGuildGroupMemModifyMsg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GuildId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.TargetGroup)))
	i += copy(dAtA[i:], m.TargetGroup)
	dAtA[i] = 0x20
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MemUid))
	return i, nil
}

func (m *WriteTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x1a
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Msg.Size()))
		n2, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n2
	}
	return i, nil
}

func (m *WriteTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WriteTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *BatchWriteTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchWriteTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *BatchWriteTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchWriteTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *PullTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	dAtA[i] = 0x18
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.StartSeqid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x28
	i++
	if m.SkipCache {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *PullTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, msg := range m.MsgList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ImMsgWriteReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImMsgWriteReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	if m.Msg == nil {
		return 0, github_com_gogo_protobuf_proto1.NewRequiredNotSetError("msg")
	} else {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Msg.Size()))
		n3, err := m.Msg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n3
	}
	dAtA[i] = 0x1a
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *ImMsgWriteRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ImMsgWriteRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *PullImMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullImMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.StartSeqid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Limit))
	dAtA[i] = 0x22
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *PullImMsgRsp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PullImMsgRsp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Ret))
	if len(m.Msgs) > 0 {
		for _, msg := range m.Msgs {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ReadStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SvrMsgId))
	return i, nil
}

func (m *BatchSetReadStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetReadStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.Status) > 0 {
		for _, msg := range m.Status {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x10
	i++
	if m.ForceReplace {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *BatchSetReadStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchSetReadStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Ret))
	return i, nil
}

func (m *BatchGetReadStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetReadStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.TargetAccount) > 0 {
		for _, s := range m.TargetAccount {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	return i, nil
}

func (m *BatchGetReadStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetReadStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Ret))
	if len(m.Status) > 0 {
		for _, msg := range m.Status {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *ReadStatusList) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ReadStatusList) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StatusList) > 0 {
		for _, msg := range m.StatusList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *CheckMsgKeyExistReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckMsgKeyExistReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	return i, nil
}

func (m *CheckMsgKeyExistResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckMsgKeyExistResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if m.Value != nil {
		dAtA[i] = 0xa
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Value)))
		i += copy(dAtA[i:], m.Value)
	}
	return i, nil
}

func (m *SetMsgKeyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMsgKeyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0xa
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Key)))
	i += copy(dAtA[i:], m.Key)
	if m.Value != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Value)))
		i += copy(dAtA[i:], m.Value)
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Ttl))
	return i, nil
}

func (m *SetMsgKeyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetMsgKeyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *TimelineKey) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimelineKey) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.Suffix)))
	i += copy(dAtA[i:], m.Suffix)
	return i, nil
}

func (m *TimelineAsyncJobReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TimelineAsyncJobReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.KeyList) > 0 {
		for _, msg := range m.KeyList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GroupTimelineIndex) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GroupTimelineIndex) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.GroupId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SeqId))
	return i, nil
}

func (m *ClearReadStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearReadStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.TargetAccount)))
	i += copy(dAtA[i:], m.TargetAccount)
	return i, nil
}

func (m *ClearReadStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearReadStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *SetPeerReadStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetPeerReadStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.PeerUid))
	dAtA[i] = 0x12
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(len(m.SelfAccount)))
	i += copy(dAtA[i:], m.SelfAccount)
	dAtA[i] = 0x18
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MsgId))
	return i, nil
}

func (m *SetPeerReadStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetPeerReadStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.PeerSeqId))
	return i, nil
}

func (m *BatchGetPeerReadStatusReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPeerReadStatusReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SelfUid))
	if len(m.PeerAccountList) > 0 {
		for _, s := range m.PeerAccountList {
			dAtA[i] = 0x12
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	dAtA[i] = 0x18
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SelfSeqId))
	return i, nil
}

func (m *BatchGetPeerReadStatusResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *BatchGetPeerReadStatusResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.PeerAccountList) > 0 {
		for _, s := range m.PeerAccountList {
			dAtA[i] = 0xa
			i++
			l = len(s)
			for l >= 1<<7 {
				dAtA[i] = uint8(uint64(l)&0x7f | 0x80)
				l >>= 7
				i++
			}
			dAtA[i] = uint8(l)
			i++
			i += copy(dAtA[i:], s)
		}
	}
	if len(m.StatusList) > 0 {
		for _, msg := range m.StatusList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintTimelinesvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *PeerReadStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *PeerReadStatus) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.MsgId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.SeqId))
	return i, nil
}

func (m *ClearGroupTimelineMsgReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearGroupTimelineMsgReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTimelinesvr(dAtA, i, uint64(m.Id))
	return i, nil
}

func (m *ClearGroupTimelineMsgResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ClearGroupTimelineMsgResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Timelinesvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Timelinesvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintTimelinesvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ExtContentCase) Size() (n int) {
	var l int
	_ = l
	if len(m.CaseUidList) > 0 {
		for _, e := range m.CaseUidList {
			n += 1 + sovTimelinesvr(uint64(e))
		}
	}
	l = len(m.Content)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *ExtContentPart) Size() (n int) {
	var l int
	_ = l
	if len(m.CaseList) > 0 {
		for _, e := range m.CaseList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	l = len(m.DetaultContent)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *ExtContent) Size() (n int) {
	var l int
	_ = l
	if len(m.ContentPartList) > 0 {
		for _, e := range m.ContentPartList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	n += 1 + sovTimelinesvr(uint64(m.OpType))
	if len(m.TargetUids) > 0 {
		for _, e := range m.TargetUids {
			n += 1 + sovTimelinesvr(uint64(e))
		}
	}
	return n
}

func (m *TimelineMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Type))
	n += 1 + sovTimelinesvr(uint64(m.Seqid))
	if m.MsgBin != nil {
		l = len(m.MsgBin)
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	return n
}

func (m *ImMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.FromId))
	n += 1 + sovTimelinesvr(uint64(m.ToId))
	l = len(m.FromName)
	n += 1 + l + sovTimelinesvr(uint64(l))
	l = len(m.ToName)
	n += 1 + l + sovTimelinesvr(uint64(l))
	l = len(m.FromNick)
	n += 1 + l + sovTimelinesvr(uint64(l))
	l = len(m.ToNick)
	n += 1 + l + sovTimelinesvr(uint64(l))
	l = len(m.Content)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.Type))
	n += 1 + sovTimelinesvr(uint64(m.ClientMsgTime))
	n += 1 + sovTimelinesvr(uint64(m.Status))
	n += 1 + sovTimelinesvr(uint64(m.ServerMsgId))
	n += 1 + sovTimelinesvr(uint64(m.ServerMsgTime))
	if m.Thumb != nil {
		l = len(m.Thumb)
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	n += 2
	if m.AttachmentProperty != nil {
		l = len(m.AttachmentProperty)
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	l = len(m.SenderLoginKey)
	n += 2 + l + sovTimelinesvr(uint64(l))
	n += 2 + sovTimelinesvr(uint64(m.ClientMsgId))
	if m.SpecialContent != nil {
		l = m.SpecialContent.Size()
		n += 2 + l + sovTimelinesvr(uint64(l))
	}
	n += 2 + sovTimelinesvr(uint64(m.ImgFormat))
	n += 2 + sovTimelinesvr(uint64(m.GuildId))
	n += 2 + sovTimelinesvr(uint64(m.ExceedTime))
	l = len(m.OldVerContent)
	n += 2 + l + sovTimelinesvr(uint64(l))
	if m.Ext != nil {
		l = len(m.Ext)
		n += 2 + l + sovTimelinesvr(uint64(l))
	}
	n += 2 + sovTimelinesvr(uint64(m.Level))
	if len(m.MedalList) > 0 {
		for _, e := range m.MedalList {
			n += 2 + sovTimelinesvr(uint64(e))
		}
	}
	n += 2 + sovTimelinesvr(uint64(m.Origin))
	n += 2 + sovTimelinesvr(uint64(m.SyncKey))
	n += 2 + sovTimelinesvr(uint64(m.Platform))
	n += 2 + sovTimelinesvr(uint64(m.PublicType))
	n += 2 + sovTimelinesvr(uint64(m.GuildMemLevel))
	n += 2 + sovTimelinesvr(uint64(m.TargetMsgId))
	n += 2 + sovTimelinesvr(uint64(m.MsgSourceType))
	n += 2 + sovTimelinesvr(uint64(m.MsgSensitiveType))
	n += 2 + sovTimelinesvr(uint64(m.Label))
	n += 2 + sovTimelinesvr(uint64(m.MsgRedpointFlag))
	n += 2 + sovTimelinesvr(uint64(m.MsgExposureFlag))
	l = len(m.AppName)
	n += 2 + l + sovTimelinesvr(uint64(l))
	l = len(m.AppPlatform)
	n += 2 + l + sovTimelinesvr(uint64(l))
	n += 2 + sovTimelinesvr(uint64(m.SuperPlayerLevel))
	return n
}

func (m *GuildApplyMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.FromId))
	l = len(m.FromName)
	n += 1 + l + sovTimelinesvr(uint64(l))
	l = len(m.FromNick)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.GuildId))
	l = len(m.VerifyMsg)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.Status))
	n += 1 + sovTimelinesvr(uint64(m.ApplyTime))
	n += 1 + sovTimelinesvr(uint64(m.ApplyId))
	l = len(m.GroupAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *DelImMsg) Size() (n int) {
	var l int
	_ = l
	l = len(m.TargetAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *QuitGuildMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.GuildId))
	l = len(m.GuildAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.QuitUid))
	l = len(m.QuitUserAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.OpUid))
	l = len(m.OpAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.QuitType))
	return n
}

func (m *ImGuildGroupMemModifyMsg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.GuildId))
	n += 1 + sovTimelinesvr(uint64(m.GroupId))
	l = len(m.TargetGroup)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.MemUid))
	return n
}

func (m *WriteTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovTimelinesvr(uint64(l))
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	return n
}

func (m *WriteTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *BatchWriteTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovTimelinesvr(uint64(l))
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *BatchWriteTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *PullTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.StartSeqid))
	n += 1 + sovTimelinesvr(uint64(m.Limit))
	n += 2
	return n
}

func (m *PullTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	if len(m.MsgList) > 0 {
		for _, e := range m.MsgList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *ImMsgWriteReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	if m.Msg != nil {
		l = m.Msg.Size()
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	l = len(m.Suffix)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *ImMsgWriteRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Ret))
	return n
}

func (m *PullImMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	n += 1 + sovTimelinesvr(uint64(m.StartSeqid))
	n += 1 + sovTimelinesvr(uint64(m.Limit))
	l = len(m.Suffix)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *PullImMsgRsp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Ret))
	if len(m.Msgs) > 0 {
		for _, e := range m.Msgs {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *ReadStatus) Size() (n int) {
	var l int
	_ = l
	l = len(m.TargetAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.SvrMsgId))
	return n
}

func (m *BatchSetReadStatusReq) Size() (n int) {
	var l int
	_ = l
	if len(m.Status) > 0 {
		for _, e := range m.Status {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	n += 2
	return n
}

func (m *BatchSetReadStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Ret))
	return n
}

func (m *BatchGetReadStatusReq) Size() (n int) {
	var l int
	_ = l
	if len(m.TargetAccount) > 0 {
		for _, s := range m.TargetAccount {
			l = len(s)
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *BatchGetReadStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Ret))
	if len(m.Status) > 0 {
		for _, e := range m.Status {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *ReadStatusList) Size() (n int) {
	var l int
	_ = l
	if len(m.StatusList) > 0 {
		for _, e := range m.StatusList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *CheckMsgKeyExistReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *CheckMsgKeyExistResp) Size() (n int) {
	var l int
	_ = l
	if m.Value != nil {
		l = len(m.Value)
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	return n
}

func (m *SetMsgKeyReq) Size() (n int) {
	var l int
	_ = l
	l = len(m.Key)
	n += 1 + l + sovTimelinesvr(uint64(l))
	if m.Value != nil {
		l = len(m.Value)
		n += 1 + l + sovTimelinesvr(uint64(l))
	}
	n += 1 + sovTimelinesvr(uint64(m.Ttl))
	return n
}

func (m *SetMsgKeyResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *TimelineKey) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	l = len(m.Suffix)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *TimelineAsyncJobReq) Size() (n int) {
	var l int
	_ = l
	if len(m.KeyList) > 0 {
		for _, e := range m.KeyList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *GroupTimelineIndex) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.GroupId))
	n += 1 + sovTimelinesvr(uint64(m.SeqId))
	return n
}

func (m *ClearReadStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Uid))
	l = len(m.TargetAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	return n
}

func (m *ClearReadStatusResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *SetPeerReadStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.PeerUid))
	l = len(m.SelfAccount)
	n += 1 + l + sovTimelinesvr(uint64(l))
	n += 1 + sovTimelinesvr(uint64(m.MsgId))
	return n
}

func (m *SetPeerReadStatusResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.PeerSeqId))
	return n
}

func (m *BatchGetPeerReadStatusReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.SelfUid))
	if len(m.PeerAccountList) > 0 {
		for _, s := range m.PeerAccountList {
			l = len(s)
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	n += 1 + sovTimelinesvr(uint64(m.SelfSeqId))
	return n
}

func (m *BatchGetPeerReadStatusResp) Size() (n int) {
	var l int
	_ = l
	if len(m.PeerAccountList) > 0 {
		for _, s := range m.PeerAccountList {
			l = len(s)
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	if len(m.StatusList) > 0 {
		for _, e := range m.StatusList {
			l = e.Size()
			n += 1 + l + sovTimelinesvr(uint64(l))
		}
	}
	return n
}

func (m *PeerReadStatus) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.MsgId))
	n += 1 + sovTimelinesvr(uint64(m.SeqId))
	return n
}

func (m *ClearGroupTimelineMsgReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTimelinesvr(uint64(m.Id))
	return n
}

func (m *ClearGroupTimelineMsgResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovTimelinesvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozTimelinesvr(x uint64) (n int) {
	return sovTimelinesvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ExtContentCase) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExtContentCase: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExtContentCase: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.CaseUidList = append(m.CaseUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTimelinesvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTimelinesvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.CaseUidList = append(m.CaseUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field CaseUidList", wireType)
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExtContentPart) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExtContentPart: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExtContentPart: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CaseList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.CaseList = append(m.CaseList, &ExtContentCase{})
			if err := m.CaseList[len(m.CaseList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field DetaultContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.DetaultContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("detault_content")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ExtContent) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ExtContent: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ExtContent: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ContentPartList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ContentPartList = append(m.ContentPartList, &ExtContentPart{})
			if err := m.ContentPartList[len(m.ContentPartList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpType", wireType)
			}
			m.OpType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.TargetUids = append(m.TargetUids, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTimelinesvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTimelinesvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.TargetUids = append(m.TargetUids, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetUids", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimelineMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TimelineMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TimelineMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Seqid", wireType)
			}
			m.Seqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Seqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgBin", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgBin = append(m.MsgBin[:0], dAtA[iNdEx:postIndex]...)
			if m.MsgBin == nil {
				m.MsgBin = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seqid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_bin")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromId", wireType)
			}
			m.FromId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToId", wireType)
			}
			m.ToId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ToId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromNick", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromNick = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ToNick", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.ToNick = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Content", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Content = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientMsgTime", wireType)
			}
			m.ClientMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000100)
		case 10:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000200)
		case 11:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerMsgId", wireType)
			}
			m.ServerMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000400)
		case 12:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ServerMsgTime", wireType)
			}
			m.ServerMsgTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ServerMsgTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000800)
		case 13:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Thumb", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Thumb = append(m.Thumb[:0], dAtA[iNdEx:postIndex]...)
			if m.Thumb == nil {
				m.Thumb = []byte{}
			}
			iNdEx = postIndex
		case 14:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HasAttachment", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.HasAttachment = bool(v != 0)
			hasFields[0] |= uint64(0x00001000)
		case 15:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AttachmentProperty", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AttachmentProperty = append(m.AttachmentProperty[:0], dAtA[iNdEx:postIndex]...)
			if m.AttachmentProperty == nil {
				m.AttachmentProperty = []byte{}
			}
			iNdEx = postIndex
		case 16:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SenderLoginKey", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SenderLoginKey = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 17:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ClientMsgId", wireType)
			}
			m.ClientMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ClientMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 18:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SpecialContent", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.SpecialContent == nil {
				m.SpecialContent = &ExtContent{}
			}
			if err := m.SpecialContent.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 19:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ImgFormat", wireType)
			}
			m.ImgFormat = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ImgFormat |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 20:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 21:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ExceedTime", wireType)
			}
			m.ExceedTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ExceedTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 22:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OldVerContent", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OldVerContent = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 23:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ext", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Ext = append(m.Ext[:0], dAtA[iNdEx:postIndex]...)
			if m.Ext == nil {
				m.Ext = []byte{}
			}
			iNdEx = postIndex
		case 24:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 25:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.MedalList = append(m.MedalList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTimelinesvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTimelinesvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.MedalList = append(m.MedalList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field MedalList", wireType)
			}
		case 26:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Origin", wireType)
			}
			m.Origin = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Origin |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 27:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SyncKey", wireType)
			}
			m.SyncKey = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SyncKey |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 28:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Platform", wireType)
			}
			m.Platform = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Platform |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 29:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PublicType", wireType)
			}
			m.PublicType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PublicType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 30:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildMemLevel", wireType)
			}
			m.GuildMemLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildMemLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 31:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetMsgId", wireType)
			}
			m.TargetMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 32:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSourceType", wireType)
			}
			m.MsgSourceType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSourceType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 33:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgSensitiveType", wireType)
			}
			m.MsgSensitiveType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgSensitiveType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 34:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Label", wireType)
			}
			m.Label = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Label |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 35:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgRedpointFlag", wireType)
			}
			m.MsgRedpointFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgRedpointFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 36:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgExposureFlag", wireType)
			}
			m.MsgExposureFlag = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgExposureFlag |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 39:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 40:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AppPlatform", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.AppPlatform = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 41:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SuperPlayerLevel", wireType)
			}
			m.SuperPlayerLevel = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SuperPlayerLevel |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_name")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_name")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_nick")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("to_nick")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("content")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("type")
	}
	if hasFields[0]&uint64(0x00000100) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("client_msg_time")
	}
	if hasFields[0]&uint64(0x00000200) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000400) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("server_msg_id")
	}
	if hasFields[0]&uint64(0x00000800) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("server_msg_time")
	}
	if hasFields[0]&uint64(0x00001000) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("has_attachment")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GuildApplyMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GuildApplyMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GuildApplyMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromId", wireType)
			}
			m.FromId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromName", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromName = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromNick", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.FromNick = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VerifyMsg", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.VerifyMsg = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyTime", wireType)
			}
			m.ApplyTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyTime |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		case 8:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyId", wireType)
			}
			m.ApplyId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000080)
		case 9:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GroupAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_name")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("from_nick")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("verify_msg")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("status")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_time")
	}
	if hasFields[0]&uint64(0x00000080) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("apply_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DelImMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DelImMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DelImMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *QuitGuildMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: QuitGuildMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: QuitGuildMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.GuildAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuitUid", wireType)
			}
			m.QuitUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuitUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuitUserAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.QuitUserAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000020)
		case 7:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuitType", wireType)
			}
			m.QuitType = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuitType |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000040)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("quit_uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("quit_user_account")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_uid")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("op_account")
	}
	if hasFields[0]&uint64(0x00000040) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("quit_type")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImGuildGroupMemModifyMsg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImGuildGroupMemModifyMsg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImGuildGroupMemModifyMsg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GuildId", wireType)
			}
			m.GuildId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GuildId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetGroup", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetGroup = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemUid", wireType)
			}
			m.MemUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MemUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("guild_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_group")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("mem_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &TimelineMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WriteTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WriteTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchWriteTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchWriteTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchWriteTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &TimelineMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchWriteTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchWriteTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchWriteTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartSeqid", wireType)
			}
			m.StartSeqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSeqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SkipCache", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SkipCache = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_seqid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MsgList = append(m.MsgList, &TimelineMsg{})
			if err := m.MsgList[len(m.MsgList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImMsgWriteReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImMsgWriteReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImMsgWriteReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.Msg == nil {
				m.Msg = &ImMsg{}
			}
			if err := m.Msg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ImMsgWriteRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ImMsgWriteRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ImMsgWriteRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (int32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullImMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullImMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullImMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StartSeqid", wireType)
			}
			m.StartSeqid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StartSeqid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("start_seqid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PullImMsgRsp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PullImMsgRsp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PullImMsgRsp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Msgs", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Msgs = append(m.Msgs, &ImMsg{})
			if err := m.Msgs[len(m.Msgs)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReadStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReadStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SvrMsgId", wireType)
			}
			m.SvrMsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SvrMsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_account")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("svr_msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetReadStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetReadStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetReadStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Status = append(m.Status, &ReadStatus{})
			if err := m.Status[len(m.Status)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ForceReplace", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.ForceReplace = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchSetReadStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchSetReadStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchSetReadStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetReadStatusReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetReadStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetReadStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = append(m.TargetAccount, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetReadStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetReadStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetReadStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ret", wireType)
			}
			m.Ret = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ret |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Status = append(m.Status, &ReadStatus{})
			if err := m.Status[len(m.Status)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ret")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ReadStatusList) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ReadStatusList: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ReadStatusList: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatusList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatusList = append(m.StatusList, &ReadStatus{})
			if err := m.StatusList[len(m.StatusList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckMsgKeyExistReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckMsgKeyExistReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckMsgKeyExistReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckMsgKeyExistResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckMsgKeyExistResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckMsgKeyExistResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMsgKeyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMsgKeyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMsgKeyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Key", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Key = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Value", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + byteLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Value = append(m.Value[:0], dAtA[iNdEx:postIndex]...)
			if m.Value == nil {
				m.Value = []byte{}
			}
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Ttl", wireType)
			}
			m.Ttl = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Ttl |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("key")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("value")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("ttl")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetMsgKeyResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetMsgKeyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetMsgKeyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimelineKey) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TimelineKey: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TimelineKey: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Suffix", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Suffix = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("suffix")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TimelineAsyncJobReq) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TimelineAsyncJobReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TimelineAsyncJobReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field KeyList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.KeyList = append(m.KeyList, &TimelineKey{})
			if err := m.KeyList[len(m.KeyList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GroupTimelineIndex) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GroupTimelineIndex: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GroupTimelineIndex: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field GroupId", wireType)
			}
			m.GroupId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.GroupId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("group_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearReadStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearReadStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearReadStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.TargetAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("target_account")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearReadStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearReadStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearReadStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetPeerReadStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetPeerReadStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetPeerReadStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerUid", wireType)
			}
			m.PeerUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelfAccount", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.SelfAccount = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("peer_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("self_account")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetPeerReadStatusResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetPeerReadStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetPeerReadStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerSeqId", wireType)
			}
			m.PeerSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PeerSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("peer_seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPeerReadStatusReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPeerReadStatusReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPeerReadStatusReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelfUid", wireType)
			}
			m.SelfUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SelfUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerAccountList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerAccountList = append(m.PeerAccountList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelfSeqId", wireType)
			}
			m.SelfSeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SelfSeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("self_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *BatchGetPeerReadStatusResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: BatchGetPeerReadStatusResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: BatchGetPeerReadStatusResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PeerAccountList", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.PeerAccountList = append(m.PeerAccountList, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatusList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatusList = append(m.StatusList, &PeerReadStatus{})
			if err := m.StatusList[len(m.StatusList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *PeerReadStatus) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: PeerReadStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: PeerReadStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MsgId", wireType)
			}
			m.MsgId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MsgId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SeqId", wireType)
			}
			m.SeqId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SeqId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("msg_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("seq_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearGroupTimelineMsgReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearGroupTimelineMsgReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearGroupTimelineMsgReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto2.NewRequiredNotSetError("id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ClearGroupTimelineMsgResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ClearGroupTimelineMsgResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ClearGroupTimelineMsgResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTimelinesvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTimelinesvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipTimelinesvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTimelinesvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTimelinesvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthTimelinesvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowTimelinesvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipTimelinesvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthTimelinesvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTimelinesvr   = fmt2.Errorf("proto: integer overflow")
)

func init() { proto.RegisterFile("src/timelinesvr/timelinesvr.proto", fileDescriptorTimelinesvr) }

var fileDescriptorTimelinesvr = []byte{
	// 2757 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x58, 0xcd, 0x6f, 0x1b, 0xd7,
	0xb5, 0xf7, 0x90, 0x12, 0x3f, 0x0e, 0x45, 0x91, 0xba, 0xb2, 0x95, 0xb1, 0x6c, 0x49, 0xe3, 0x91,
	0xf2, 0xac, 0x7c, 0xc8, 0x31, 0xf4, 0x10, 0x3c, 0x44, 0x50, 0x08, 0xc8, 0x12, 0xad, 0xc7, 0x58,
	0x94, 0x18, 0x52, 0xca, 0x7b, 0x2e, 0xd0, 0x0e, 0x46, 0x9c, 0x2b, 0x6a, 0xc0, 0x19, 0xce, 0x78,
	0xee, 0x90, 0x10, 0x11, 0x14, 0x08, 0xd0, 0x16, 0x08, 0xba, 0x08, 0x8a, 0xec, 0xbb, 0x29, 0xbc,
	0x2b, 0x0a, 0x74, 0xd3, 0xff, 0x21, 0xcb, 0xae, 0xbb, 0x28, 0x8a, 0x74, 0x63, 0x14, 0xfd, 0x1f,
	0x52, 0x9c, 0x3b, 0x1f, 0xbc, 0x24, 0x47, 0x92, 0x5b, 0x64, 0x27, 0x9d, 0x73, 0xee, 0x39, 0xbf,
	0xf3, 0x7d, 0x86, 0xf0, 0x88, 0x79, 0xed, 0x8f, 0x7c, 0xd3, 0xa6, 0x96, 0xd9, 0xa3, 0x6c, 0xe0,
	0x89, 0x7f, 0x3f, 0x71, 0x3d, 0xc7, 0x77, 0x48, 0xee, 0x34, 0x24, 0x2d, 0x6f, 0xb4, 0x1d, 0xdb,
	0x76, 0x7a, 0x1f, 0xf9, 0xd6, 0xc0, 0x35, 0xdb, 0x5d, 0x8b, 0x7e, 0xc4, 0xba, 0xe7, 0x7d, 0xd3,
	0xf2, 0xcd, 0x9e, 0x3f, 0x74, 0x69, 0x20, 0xaf, 0x9e, 0xc2, 0x7c, 0xf5, 0xca, 0xdf, 0x77, 0x7a,
	0x3e, 0xed, 0xf9, 0xfb, 0x3a, 0xa3, 0x44, 0x85, 0x62, 0x5b, 0x67, 0x54, 0xeb, 0x9b, 0x86, 0x66,
	0x99, 0xcc, 0x97, 0x25, 0x25, 0xbd, 0x59, 0x6c, 0x16, 0x90, 0x78, 0x66, 0x1a, 0x47, 0x26, 0xf3,
	0xc9, 0x2a, 0x64, 0xdb, 0xc1, 0x13, 0x39, 0xa5, 0xa4, 0x36, 0xf3, 0xcf, 0x66, 0xbe, 0xfb, 0xeb,
	0xda, 0x9d, 0x66, 0x44, 0x54, 0x07, 0xa2, 0xd6, 0x86, 0xee, 0xf9, 0xe4, 0x63, 0xc8, 0x73, 0xad,
	0xb1, 0xc6, 0xc2, 0xb6, 0xfc, 0x24, 0xc2, 0xfa, 0x64, 0x1c, 0x42, 0x33, 0x87, 0xa2, 0xdc, 0xd0,
	0x16, 0x94, 0x0c, 0xea, 0xeb, 0x7d, 0xcb, 0xd7, 0x92, 0x0c, 0xce, 0x87, 0xcc, 0xf0, 0xb1, 0xfa,
	0x07, 0x09, 0x60, 0xa4, 0x8b, 0x1c, 0xc0, 0x42, 0xf8, 0x4a, 0x73, 0x75, 0xcf, 0xbf, 0xd5, 0x38,
	0x22, 0x6d, 0x96, 0xda, 0xa3, 0x7f, 0x38, 0x86, 0x15, 0xc8, 0x3a, 0xae, 0x86, 0x31, 0x93, 0x53,
	0x8a, 0xb4, 0x59, 0x0c, 0x6d, 0x67, 0x1c, 0xf7, 0x74, 0xe8, 0x52, 0xb2, 0x06, 0x05, 0x5f, 0xf7,
	0x3a, 0xd4, 0xc7, 0x88, 0x31, 0x39, 0xcd, 0xa3, 0x05, 0x01, 0xe9, 0xcc, 0x34, 0x98, 0x7a, 0x1f,
	0xb2, 0x27, 0x0d, 0xed, 0xf4, 0x65, 0xa3, 0x4a, 0xe6, 0x01, 0xea, 0xd5, 0xba, 0xd6, 0xac, 0xd6,
	0x4f, 0xbe, 0xa8, 0x96, 0x25, 0xf5, 0x2f, 0x12, 0x14, 0x22, 0x1c, 0x75, 0xd6, 0x21, 0x32, 0xcc,
	0x70, 0x3b, 0x92, 0x92, 0x8a, 0xed, 0x70, 0x0a, 0x59, 0x86, 0x59, 0x46, 0x5f, 0x99, 0x06, 0x77,
	0x3f, 0x62, 0x05, 0x24, 0x04, 0x68, 0xb3, 0x8e, 0x76, 0x6e, 0xf6, 0xe4, 0xb4, 0x92, 0xda, 0x9c,
	0x8b, 0x00, 0xda, 0xac, 0xf3, 0xcc, 0xec, 0xa9, 0x03, 0x98, 0xe1, 0xc6, 0x01, 0x32, 0xb5, 0xba,
	0x56, 0x6f, 0x1d, 0x96, 0x25, 0x52, 0x82, 0xc2, 0xe1, 0x59, 0xed, 0xe8, 0x40, 0xdb, 0x6b, 0x34,
	0x8e, 0x5e, 0x96, 0x53, 0x88, 0xec, 0xa0, 0x7a, 0xa4, 0x85, 0x02, 0x69, 0xfc, 0x3f, 0x10, 0xf8,
	0xfc, 0xac, 0x76, 0x5a, 0x9e, 0x21, 0x2b, 0x70, 0xbf, 0x56, 0xd7, 0x02, 0xd2, 0x61, 0xf3, 0xe4,
	0xac, 0xa1, 0xa1, 0x23, 0xf5, 0x93, 0x83, 0xda, 0xf3, 0x97, 0xe5, 0x59, 0x42, 0x60, 0x3e, 0xa0,
	0x9e, 0x1e, 0x69, 0xb5, 0xe3, 0x83, 0xea, 0xff, 0x97, 0x33, 0xea, 0x3f, 0x0a, 0x30, 0x5b, 0xb3,
	0xd1, 0xad, 0x15, 0xc8, 0x5e, 0x78, 0x8e, 0xad, 0x99, 0xc6, 0x98, 0x67, 0x19, 0x24, 0xd6, 0x0c,
	0x72, 0x1f, 0x66, 0x7d, 0x47, 0x9b, 0xf0, 0x6d, 0xc6, 0x77, 0x6a, 0x06, 0x79, 0x04, 0x79, 0xfe,
	0xb2, 0xa7, 0xdb, 0x94, 0x3b, 0x17, 0x65, 0x3e, 0x87, 0xe4, 0x63, 0xdd, 0xa6, 0xa8, 0xdc, 0x77,
	0x02, 0x81, 0x19, 0x41, 0x20, 0xe3, 0x3b, 0x9c, 0x1d, 0x6b, 0x30, 0xdb, 0x5d, 0x79, 0x76, 0x4a,
	0x83, 0xd9, 0xee, 0x46, 0x1a, 0x50, 0x20, 0x33, 0xa1, 0x01, 0xd9, 0x42, 0xb1, 0x67, 0x13, 0x8a,
	0x3d, 0x4e, 0x5a, 0x6e, 0x2a, 0x69, 0x1f, 0x42, 0xa9, 0x6d, 0x99, 0x58, 0x7e, 0x98, 0x1f, 0x6c,
	0x56, 0x39, 0x2f, 0x08, 0x15, 0x03, 0x66, 0x9d, 0x75, 0xb0, 0x06, 0xc8, 0x43, 0xc8, 0x30, 0x5f,
	0xf7, 0xfb, 0x4c, 0x06, 0x31, 0x48, 0x01, 0x8d, 0x6c, 0x42, 0x91, 0x51, 0x6f, 0x40, 0x3d, 0xae,
	0xcb, 0x34, 0xe4, 0x82, 0x20, 0x54, 0x08, 0x58, 0x75, 0xd6, 0xa9, 0x19, 0x68, 0x55, 0x90, 0xe4,
	0x56, 0xe7, 0x44, 0xab, 0xb1, 0x2c, 0xb7, 0xba, 0x0c, 0xb3, 0xfe, 0x65, 0xdf, 0x3e, 0x97, 0x8b,
	0x8a, 0x14, 0x97, 0x4e, 0x40, 0x22, 0x1f, 0xc0, 0xfc, 0xa5, 0xce, 0x34, 0xdd, 0xf7, 0xf5, 0xf6,
	0xa5, 0x8d, 0x01, 0x98, 0x57, 0x52, 0x9b, 0xb9, 0x48, 0xd1, 0xa5, 0xce, 0xf6, 0x62, 0x16, 0xf9,
	0x18, 0x16, 0x47, 0x82, 0x9a, 0xeb, 0x39, 0x2e, 0xf5, 0xfc, 0xa1, 0x5c, 0x12, 0xd4, 0x92, 0x91,
	0x40, 0x23, 0xe4, 0x93, 0x27, 0x50, 0x66, 0xb4, 0x67, 0x50, 0x4f, 0xb3, 0x9c, 0x8e, 0xd9, 0xd3,
	0xba, 0x74, 0x28, 0x97, 0x15, 0x69, 0xd4, 0xe2, 0x01, 0xf7, 0x08, 0x99, 0x2f, 0xe8, 0x10, 0xe3,
	0x20, 0xc4, 0xd4, 0x34, 0xe4, 0x05, 0xa1, 0x27, 0x0b, 0x71, 0x44, 0x6b, 0x06, 0xf9, 0x14, 0x4a,
	0xcc, 0xa5, 0x6d, 0x53, 0xb7, 0xe2, 0xd9, 0x41, 0x14, 0x69, 0xb3, 0xb0, 0x7d, 0x37, 0xa9, 0xf7,
	0x9b, 0xf3, 0xa1, 0x70, 0x34, 0x3c, 0xd6, 0x01, 0x4c, 0xbb, 0xa3, 0x5d, 0x38, 0x9e, 0xad, 0xfb,
	0xf2, 0xa2, 0x60, 0x25, 0x6f, 0xda, 0x9d, 0xe7, 0x9c, 0x4c, 0xd6, 0x20, 0xd7, 0xe9, 0x9b, 0x96,
	0x81, 0x40, 0xee, 0x0a, 0x22, 0x59, 0x4e, 0xad, 0x19, 0xe4, 0x5d, 0x28, 0xd0, 0xab, 0x36, 0xa5,
	0x46, 0x90, 0x88, 0x7b, 0x82, 0x0c, 0x04, 0x0c, 0x9e, 0x85, 0x0f, 0xa1, 0xe4, 0x58, 0x86, 0x86,
	0x49, 0x8b, 0xb0, 0x2e, 0x09, 0x41, 0x28, 0x3a, 0x96, 0xf1, 0x05, 0xf5, 0x22, 0x68, 0x4b, 0x90,
	0xa6, 0x57, 0xbe, 0xfc, 0x8e, 0x10, 0x5a, 0x24, 0x60, 0x2e, 0x2d, 0x3a, 0xa0, 0x96, 0x2c, 0x0b,
	0x66, 0x02, 0x12, 0x59, 0x01, 0xb0, 0xa9, 0xa1, 0x5b, 0xc1, 0x10, 0xbc, 0xcf, 0xa7, 0x54, 0x9e,
	0x53, 0xf8, 0x90, 0x7b, 0x08, 0x19, 0xc7, 0x33, 0x3b, 0x66, 0x4f, 0x5e, 0x1e, 0x9b, 0x71, 0x9c,
	0x86, 0x6e, 0xb2, 0x61, 0xaf, 0xcd, 0x93, 0xf3, 0x40, 0x74, 0x13, 0xa9, 0x98, 0x15, 0x05, 0x72,
	0xae, 0xa5, 0xfb, 0x18, 0x2c, 0xf9, 0xa1, 0x20, 0x10, 0x53, 0x31, 0x10, 0x6e, 0xff, 0xdc, 0x32,
	0xdb, 0xc1, 0x24, 0x5d, 0x11, 0x03, 0x11, 0x30, 0x4e, 0xc3, 0x96, 0x09, 0x02, 0x6a, 0x53, 0x5b,
	0x0b, 0x9c, 0x59, 0x15, 0x44, 0x8b, 0x9c, 0x59, 0xa7, 0xf6, 0x11, 0x77, 0x6a, 0x13, 0x8a, 0xe1,
	0xec, 0x0d, 0x8b, 0x61, 0x4d, 0x2c, 0x86, 0x80, 0x15, 0x37, 0x05, 0x8a, 0x30, 0xa7, 0xef, 0xb5,
	0x69, 0x00, 0x41, 0x11, 0xf5, 0xda, 0xac, 0xd3, 0xe2, 0x3c, 0x8e, 0x62, 0x1b, 0x08, 0x97, 0xa6,
	0x3d, 0x66, 0xfa, 0xe6, 0x20, 0x7c, 0xf0, 0x48, 0x78, 0x50, 0xc6, 0x07, 0x11, 0xfb, 0x34, 0x9c,
	0xd0, 0x96, 0x7e, 0x4e, 0x2d, 0x59, 0x1d, 0x0b, 0x3e, 0x92, 0xc8, 0x53, 0x58, 0x40, 0x7d, 0x1e,
	0x35, 0x5c, 0xc7, 0xec, 0xf9, 0xda, 0x85, 0xa5, 0x77, 0xe4, 0x75, 0x41, 0x0e, 0xc1, 0x35, 0x43,
	0xee, 0x73, 0x4b, 0xef, 0x44, 0x2f, 0xe8, 0x95, 0xeb, 0xb0, 0xbe, 0x47, 0x83, 0x17, 0x1b, 0x13,
	0x2f, 0xaa, 0x21, 0x97, 0xbf, 0x58, 0x83, 0x9c, 0xee, 0xba, 0xc1, 0x20, 0x7c, 0x2c, 0xd4, 0x4e,
	0x56, 0x77, 0x5d, 0x3e, 0x09, 0x1f, 0xc3, 0x1c, 0x0a, 0xc4, 0x79, 0xda, 0x14, 0x84, 0x0a, 0xba,
	0xeb, 0x36, 0xa2, 0x54, 0x6d, 0x03, 0x61, 0x7d, 0x97, 0x7a, 0x28, 0x3a, 0xc4, 0xc6, 0xe4, 0x69,
	0x78, 0x4f, 0xf4, 0x9e, 0xf3, 0x1b, 0x9c, 0xcd, 0x33, 0xa1, 0xfe, 0x33, 0x05, 0xc5, 0x43, 0xcc,
	0xcd, 0x9e, 0xeb, 0x5a, 0xc3, 0xb7, 0x18, 0xfa, 0x63, 0x93, 0x3d, 0x95, 0x38, 0xd9, 0xc7, 0x46,
	0x77, 0x3a, 0x71, 0x74, 0x8b, 0xfd, 0x37, 0x23, 0x58, 0x89, 0xfb, 0x6f, 0x1d, 0x60, 0x40, 0x3d,
	0xf3, 0x62, 0x88, 0x15, 0x32, 0x36, 0xff, 0xf3, 0x01, 0x1d, 0xa1, 0x8e, 0x26, 0x6f, 0x26, 0x61,
	0xf2, 0xae, 0x03, 0xe8, 0xe8, 0x54, 0xd0, 0xc1, 0x59, 0x41, 0x22, 0xcf, 0xe9, 0xbc, 0x81, 0x83,
	0xe8, 0x5b, 0x43, 0x04, 0x22, 0x2e, 0x82, 0x2c, 0xa7, 0xd6, 0x0c, 0xf2, 0x1e, 0x14, 0x3b, 0x9e,
	0xd3, 0x77, 0x35, 0xbd, 0xdd, 0x76, 0xfa, 0x3d, 0x5f, 0xce, 0x0b, 0xe1, 0x9f, 0xe3, 0xac, 0xbd,
	0x80, 0xa3, 0x3e, 0x82, 0x4c, 0xeb, 0x74, 0xef, 0xf4, 0xac, 0x45, 0x8a, 0x90, 0x3f, 0x3b, 0xd6,
	0xfe, 0x77, 0xef, 0xf8, 0xe0, 0xa8, 0x5a, 0xbe, 0x43, 0x72, 0x30, 0x73, 0x70, 0x72, 0x8c, 0x87,
	0xc3, 0xff, 0x40, 0xee, 0x80, 0x5a, 0xc1, 0x76, 0xfd, 0x00, 0xe6, 0xc3, 0x26, 0x88, 0x54, 0x4b,
	0x82, 0x9b, 0x61, 0x83, 0x44, 0xba, 0xff, 0x94, 0x82, 0xb9, 0xcf, 0xfb, 0xa6, 0xcf, 0x73, 0x85,
	0xaf, 0xc5, 0x08, 0x4a, 0x49, 0x11, 0x44, 0xe0, 0x5c, 0x20, 0xd2, 0x2e, 0x26, 0x6b, 0x8e, 0xb3,
	0x42, 0xe5, 0xa8, 0xeb, 0x55, 0xdf, 0xe4, 0x87, 0x10, 0xcf, 0x57, 0xac, 0x0b, 0xa9, 0x67, 0xa6,
	0x81, 0x55, 0x1d, 0x08, 0x30, 0xea, 0xc5, 0xfa, 0xc4, 0xad, 0x5d, 0xe2, 0x92, 0x8c, 0x7a, 0x91,
	0xca, 0x07, 0x90, 0x71, 0x5c, 0xae, 0x70, 0x56, 0x3c, 0x7c, 0x1c, 0x17, 0xd5, 0xad, 0x03, 0x38,
	0xa3, 0x80, 0x8a, 0xbb, 0x3b, 0xef, 0x44, 0xd1, 0xc4, 0x2a, 0xe2, 0x36, 0x79, 0x0b, 0x8b, 0xd9,
	0xe3, 0x58, 0xb1, 0x75, 0xd5, 0x47, 0x90, 0xc7, 0x33, 0x27, 0xb8, 0xd1, 0x72, 0x30, 0xc3, 0x6f,
	0x1e, 0x09, 0x0f, 0xa6, 0x17, 0xb5, 0xfd, 0x17, 0xd5, 0x83, 0x72, 0x4a, 0x7d, 0x2d, 0x81, 0x5c,
	0xb3, 0x79, 0xd4, 0x0e, 0x31, 0x57, 0x75, 0x6a, 0xd7, 0x1d, 0x23, 0xac, 0x9f, 0x5b, 0x63, 0x88,
	0x02, 0x3c, 0xf9, 0x13, 0x47, 0x4e, 0x96, 0x53, 0x6b, 0x06, 0xf6, 0x66, 0x98, 0x43, 0x4e, 0x19,
	0xab, 0xf6, 0x70, 0x8e, 0x71, 0xb3, 0xfc, 0xd6, 0xa3, 0x36, 0x0f, 0x88, 0x58, 0xef, 0x19, 0x9b,
	0xda, 0x67, 0xa6, 0xa1, 0x7a, 0xb0, 0xf8, 0x7f, 0x9e, 0xe9, 0x53, 0xe1, 0xa8, 0x6c, 0xd2, 0x57,
	0xe4, 0x2e, 0xa4, 0x26, 0xa0, 0xa5, 0x4c, 0x83, 0x97, 0x7d, 0xff, 0xe2, 0xc2, 0xbc, 0x1a, 0x4b,
	0x69, 0x48, 0x23, 0x8f, 0x21, 0x8d, 0x2d, 0x83, 0x48, 0x0a, 0xdb, 0xf7, 0x46, 0x2b, 0x53, 0x54,
	0x8d, 0x12, 0xea, 0x12, 0xdc, 0x9d, 0xb6, 0xc9, 0x5c, 0xf5, 0x2b, 0x09, 0xe4, 0x67, 0xba, 0xdf,
	0xbe, 0xfc, 0xb1, 0x10, 0x3d, 0x85, 0x1c, 0xce, 0x44, 0xbe, 0xc0, 0xd2, 0xfc, 0x8a, 0xbf, 0x06,
	0x16, 0x9e, 0xc3, 0xb8, 0xd5, 0xd4, 0x07, 0x70, 0xff, 0x1a, 0x04, 0xcc, 0x55, 0xff, 0x28, 0x01,
	0x69, 0xf4, 0x2d, 0xeb, 0x47, 0x40, 0xf6, 0x2e, 0x14, 0x98, 0x8f, 0x5f, 0x18, 0xc1, 0x8d, 0x2e,
	0xd6, 0x3e, 0x70, 0x46, 0x8b, 0x1f, 0xea, 0xb8, 0x22, 0x4c, 0xdb, 0xf4, 0xc7, 0x52, 0x17, 0x90,
	0xb0, 0x96, 0x59, 0xd7, 0x74, 0xb5, 0xb6, 0xde, 0xbe, 0xa4, 0xf2, 0xac, 0x22, 0xc5, 0x77, 0x56,
	0x1e, 0xe9, 0xfb, 0x48, 0x56, 0x0f, 0x61, 0x71, 0x0a, 0x31, 0x73, 0xc7, 0x02, 0x23, 0xbd, 0x55,
	0x60, 0x2e, 0xa0, 0xc8, 0x87, 0x07, 0x0f, 0xcc, 0xf5, 0x5e, 0x3f, 0x0a, 0x6a, 0x20, 0xc5, 0x6b,
	0xa0, 0x34, 0xd2, 0xc9, 0xdf, 0xf2, 0xec, 0x0b, 0x81, 0x49, 0x0b, 0x03, 0x2d, 0xa4, 0xa9, 0x8f,
	0xc7, 0xec, 0x30, 0x17, 0x4f, 0x17, 0x8f, 0x06, 0x13, 0x6a, 0x36, 0x3a, 0x5d, 0x3c, 0xea, 0xab,
	0xbf, 0x92, 0x60, 0x0e, 0x5d, 0x0b, 0x34, 0x5f, 0x0b, 0x68, 0x22, 0xd0, 0xa9, 0xdb, 0x02, 0x9d,
	0x1e, 0xdb, 0xc5, 0x3c, 0xd0, 0x23, 0xc0, 0x33, 0x09, 0x80, 0x5f, 0x88, 0x30, 0xc6, 0xf1, 0x16,
	0x05, 0xbc, 0x64, 0x1d, 0x66, 0x6c, 0xd6, 0x61, 0x72, 0x8a, 0x87, 0x7b, 0x2a, 0x34, 0x9c, 0xa9,
	0xfe, 0x14, 0xa0, 0x49, 0x75, 0xa3, 0x15, 0xec, 0x91, 0x7f, 0x67, 0x4e, 0x13, 0x15, 0x80, 0x0d,
	0xe2, 0x5b, 0x5f, 0xf4, 0x33, 0xc7, 0x06, 0xc1, 0xa1, 0xaf, 0xba, 0x70, 0x8f, 0x57, 0x77, 0x8b,
	0xfa, 0x23, 0x33, 0x18, 0xbb, 0x0f, 0xe3, 0x7d, 0x16, 0x54, 0x83, 0x70, 0xf0, 0x0a, 0x82, 0xd1,
	0x7e, 0x7b, 0x0f, 0x8a, 0x17, 0x0e, 0x5e, 0x45, 0x1e, 0x75, 0x2d, 0xbd, 0x1d, 0x7c, 0xe5, 0x46,
	0xc5, 0x37, 0xc7, 0x59, 0xcd, 0x80, 0xa3, 0x3e, 0x85, 0xa5, 0x24, 0x8b, 0xd7, 0xc7, 0x49, 0xad,
	0x84, 0x18, 0x0f, 0x27, 0x31, 0xbe, 0x9b, 0x10, 0x8d, 0xf4, 0x66, 0x7e, 0x72, 0x5f, 0xfd, 0x2c,
	0xb4, 0x78, 0xf8, 0xb6, 0x16, 0x05, 0xe7, 0x53, 0xb7, 0x3b, 0xaf, 0x1e, 0xc2, 0xfc, 0x88, 0xca,
	0x2f, 0xe1, 0x8f, 0x79, 0x89, 0xf9, 0x7d, 0x26, 0xf6, 0x53, 0xb2, 0x12, 0x60, 0xf1, 0x33, 0x75,
	0x0b, 0x16, 0xf7, 0x2f, 0x69, 0xbb, 0x5b, 0x67, 0x9d, 0x17, 0x74, 0x58, 0xbd, 0x32, 0x99, 0x8f,
	0x6e, 0x2e, 0x41, 0x1a, 0x8f, 0x66, 0x31, 0xd3, 0x48, 0x50, 0xb7, 0xe1, 0xee, 0xb4, 0x38, 0x73,
	0xb1, 0x72, 0x07, 0xba, 0xd5, 0xa7, 0xb2, 0x24, 0x7e, 0x8e, 0x71, 0x92, 0xfa, 0x13, 0x98, 0x6b,
	0xf1, 0x7b, 0xf6, 0x05, 0x1d, 0xde, 0xa0, 0x7b, 0xa4, 0x23, 0x25, 0xfc, 0x1a, 0x10, 0x90, 0xf0,
	0x8d, 0xef, 0x5b, 0x63, 0x13, 0x0a, 0x09, 0x6a, 0x09, 0x8a, 0x82, 0x6e, 0xe6, 0xaa, 0x7b, 0xa3,
	0x5f, 0x26, 0xf0, 0xc0, 0xff, 0x0f, 0xa6, 0x22, 0x4e, 0xab, 0x48, 0xc5, 0x1e, 0x7e, 0x28, 0x7c,
	0xe6, 0x9c, 0x23, 0xec, 0xa7, 0x90, 0xeb, 0xd2, 0xe1, 0x2d, 0xd3, 0x0a, 0x61, 0x64, 0xbb, 0x74,
	0xc8, 0x63, 0xdb, 0x04, 0xc2, 0xb7, 0x5f, 0xc4, 0xac, 0xf5, 0x0c, 0x7a, 0x35, 0xb6, 0x54, 0xa5,
	0xa4, 0xa5, 0xfa, 0x00, 0x32, 0x8c, 0xbe, 0xd2, 0x12, 0x7e, 0x34, 0xa9, 0x19, 0xea, 0x4b, 0x20,
	0xfb, 0x16, 0xd5, 0xbd, 0xf1, 0xaa, 0x5c, 0x82, 0x74, 0x7f, 0x42, 0x1d, 0x12, 0x12, 0x7a, 0x37,
	0x75, 0xfd, 0x8d, 0x75, 0x0f, 0x16, 0xa7, 0x54, 0x33, 0x57, 0xfd, 0x39, 0xdc, 0x6d, 0x51, 0xbf,
	0x41, 0xe9, 0x84, 0xcd, 0x35, 0xc8, 0xb9, 0x94, 0x7a, 0xda, 0xa4, 0xe1, 0x2c, 0x52, 0xf1, 0xcc,
	0x79, 0x0c, 0x73, 0x8c, 0x5a, 0x17, 0x89, 0xa6, 0x0b, 0xc8, 0x11, 0x8e, 0xa5, 0x70, 0x60, 0x88,
	0xf9, 0x9d, 0xb5, 0xf9, 0xb4, 0xf8, 0x14, 0xee, 0x25, 0x98, 0x67, 0x2e, 0xd9, 0x80, 0x02, 0xb7,
	0x1f, 0xc6, 0x4a, 0x84, 0x90, 0x47, 0x46, 0x8b, 0xc7, 0xeb, 0x1b, 0x29, 0xdc, 0xa5, 0x87, 0xd7,
	0xf8, 0xc0, 0x21, 0x4e, 0xf9, 0x80, 0x54, 0xf4, 0xe1, 0x7d, 0x58, 0xe0, 0x46, 0x42, 0x1f, 0x82,
	0xec, 0xa7, 0x78, 0xc7, 0x97, 0x90, 0x11, 0xba, 0xc0, 0x3b, 0x70, 0x03, 0xb8, 0x57, 0x11, 0x20,
	0x71, 0x86, 0xe7, 0x91, 0x11, 0x00, 0xfa, 0x85, 0x04, 0xcb, 0xd7, 0x01, 0x62, 0x6e, 0xb2, 0x41,
	0x29, 0xd9, 0xe0, 0x27, 0xe3, 0x2d, 0x9f, 0x9a, 0xfc, 0x85, 0x70, 0x42, 0xbd, 0xd8, 0xf6, 0x9f,
	0xc1, 0xfc, 0x38, 0x57, 0x48, 0x82, 0x34, 0x95, 0x84, 0x9b, 0x4b, 0xf2, 0x29, 0xc8, 0xbc, 0x6e,
	0xc6, 0x6a, 0xfd, 0xa6, 0x75, 0x88, 0xf7, 0xcd, 0x35, 0x2f, 0x98, 0xfb, 0xfe, 0x36, 0xcc, 0x37,
	0xce, 0x9e, 0x1d, 0xd5, 0xf6, 0xb5, 0x5a, 0x3d, 0xfe, 0xf9, 0x71, 0xff, 0xa4, 0x5e, 0x3f, 0x39,
	0x0e, 0x7f, 0x05, 0x2c, 0xc3, 0x5c, 0xa3, 0x59, 0x3d, 0x69, 0x1e, 0x54, 0x9b, 0x9c, 0x92, 0x7a,
	0xff, 0xbf, 0x21, 0x17, 0x7f, 0x06, 0x16, 0x20, 0xbb, 0xd7, 0x33, 0x3c, 0xc7, 0x34, 0xca, 0x77,
	0x48, 0x16, 0xd2, 0xe6, 0x49, 0xab, 0x2c, 0x91, 0x05, 0x28, 0x9c, 0x1d, 0xb7, 0x1a, 0xd5, 0xfd,
	0xda, 0xf3, 0x5a, 0xf5, 0xa0, 0xfc, 0xc3, 0x0f, 0xe9, 0xed, 0xdf, 0x17, 0x83, 0x59, 0x71, 0x64,
	0xf6, 0x68, 0x6b, 0xe0, 0x11, 0x0f, 0x60, 0xb4, 0xf4, 0xc9, 0x3b, 0x13, 0xbb, 0x31, 0x3a, 0x39,
	0x96, 0x93, 0x19, 0xcc, 0x55, 0xb7, 0xbf, 0x7a, 0xfd, 0x26, 0x2d, 0xfd, 0xfa, 0xf5, 0x9b, 0x74,
	0xa6, 0xbf, 0x63, 0xef, 0xb0, 0x9d, 0x6f, 0x5f, 0xbf, 0x49, 0xaf, 0x6c, 0xf5, 0x95, 0xdd, 0xbe,
	0x69, 0x54, 0x94, 0x2d, 0x5b, 0xd9, 0xb5, 0x59, 0xa7, 0xa2, 0x6c, 0x31, 0x65, 0x37, 0x88, 0x66,
	0x85, 0x0c, 0x20, 0x1f, 0xef, 0x6d, 0xb2, 0x24, 0xa4, 0x4e, 0xb8, 0x29, 0x96, 0x13, 0xe9, 0xcc,
	0x55, 0x3f, 0x41, 0x83, 0xa9, 0xd0, 0x20, 0xdb, 0xb1, 0xb8, 0xc1, 0x8d, 0x91, 0x41, 0xb4, 0x33,
	0xba, 0x3a, 0x2a, 0xca, 0x96, 0xa5, 0xec, 0xf2, 0x63, 0xa2, 0x42, 0x7e, 0x2b, 0x01, 0x99, 0x5e,
	0x89, 0x64, 0x6d, 0x64, 0x29, 0x71, 0x45, 0x2f, 0x2b, 0x37, 0x0b, 0x30, 0x57, 0x3d, 0x40, 0x50,
	0x69, 0x04, 0x95, 0xeb, 0xef, 0xf8, 0x3b, 0xe6, 0x8e, 0xc7, 0x61, 0x6d, 0x8d, 0x60, 0xf9, 0xca,
	0xae, 0xef, 0x44, 0x05, 0x5e, 0x51, 0xb6, 0x4c, 0x65, 0x97, 0xd3, 0x3d, 0x65, 0x37, 0x5c, 0xe5,
	0x15, 0xf2, 0xcb, 0x08, 0xdf, 0xe1, 0x8d, 0xf8, 0x0e, 0x6f, 0xc3, 0x37, 0xb5, 0x7f, 0xd5, 0x0f,
	0x10, 0xdf, 0x0c, 0xe2, 0x9b, 0x41, 0x7c, 0x88, 0x4d, 0xbe, 0x0e, 0x1b, 0xb1, 0xa0, 0x3c, 0xb9,
	0xee, 0xc8, 0xca, 0xc8, 0x44, 0xc2, 0xe6, 0x5c, 0x5e, 0xbd, 0x89, 0xcd, 0x5c, 0xf5, 0x3e, 0xda,
	0x9f, 0x45, 0xfb, 0xa9, 0x2e, 0xb7, 0x9e, 0xdb, 0xea, 0x2a, 0xbb, 0x5d, 0x3a, 0xac, 0x90, 0x73,
	0xc8, 0xc7, 0xcb, 0x4c, 0x2c, 0x06, 0x71, 0x7b, 0x8a, 0xe5, 0x37, 0xbe, 0xf9, 0x36, 0x50, 0x71,
	0x86, 0x3b, 0xd6, 0x0d, 0x1d, 0x5b, 0x88, 0x54, 0x07, 0x8e, 0xf9, 0x56, 0x85, 0x0c, 0xa1, 0x3c,
	0xf9, 0x55, 0x21, 0x7a, 0x94, 0xf0, 0xcd, 0x23, 0x7a, 0x94, 0xf8, 0x41, 0xf2, 0x5f, 0x68, 0x38,
	0xcb, 0x0d, 0x9b, 0x61, 0xd5, 0x2f, 0xc6, 0x49, 0xc5, 0x1a, 0xe4, 0x6b, 0xb5, 0x42, 0xbe, 0x91,
	0xa0, 0x34, 0xf1, 0x19, 0x40, 0x1e, 0x8e, 0x97, 0xf6, 0x84, 0xe5, 0x95, 0x1b, 0xb8, 0xcc, 0x55,
	0x2b, 0x68, 0x38, 0xc7, 0x4b, 0xcd, 0xdc, 0xb9, 0x8a, 0x3b, 0xe0, 0x71, 0x6c, 0xfc, 0x2a, 0x36,
	0x1e, 0x35, 0xdd, 0x58, 0x13, 0x0c, 0xc3, 0x23, 0x6f, 0x2a, 0x20, 0xea, 0x44, 0x15, 0x25, 0x45,
	0x65, 0xfd, 0x56, 0x19, 0xe6, 0xaa, 0xef, 0x20, 0xc2, 0x3c, 0x0f, 0x8d, 0xb2, 0xb5, 0xa5, 0x7c,
	0x1b, 0xfd, 0x41, 0xbe, 0x84, 0xd2, 0xc4, 0xae, 0x15, 0x43, 0x31, 0xbd, 0xe1, 0xc5, 0x50, 0x24,
	0x2d, 0x69, 0x5e, 0xd5, 0xf0, 0x96, 0x55, 0xfd, 0xb5, 0x04, 0x0b, 0x53, 0x3b, 0x95, 0xac, 0x8e,
	0x15, 0xd6, 0xd4, 0xae, 0x5c, 0x5e, 0xbb, 0x91, 0x1f, 0xcd, 0xbf, 0xc2, 0x18, 0x86, 0x35, 0xc4,
	0x10, 0x5d, 0x09, 0x01, 0x10, 0xf1, 0x24, 0xa8, 0x90, 0xdf, 0x49, 0xa3, 0x43, 0x79, 0x02, 0xcf,
	0xfa, 0x74, 0x2b, 0x4f, 0x83, 0xda, 0xb8, 0x5d, 0x88, 0xb9, 0xea, 0xa7, 0x88, 0x6c, 0x2e, 0x1c,
	0x94, 0x7e, 0x58, 0xa3, 0x9b, 0x88, 0x2d, 0xda, 0xfe, 0x01, 0x36, 0x71, 0xf3, 0x8e, 0x4a, 0x86,
	0x7c, 0x09, 0xf7, 0x12, 0xd7, 0x95, 0x58, 0x27, 0xd7, 0x6d, 0x40, 0xb1, 0x4e, 0xae, 0xdd, 0x79,
	0xc1, 0x50, 0x28, 0xf2, 0xa1, 0xd0, 0x0f, 0x87, 0x42, 0x98, 0xbc, 0xe5, 0xcc, 0xd7, 0xaf, 0xdf,
	0xa4, 0xbf, 0xa3, 0xcf, 0xca, 0xdf, 0x7d, 0xbf, 0x2a, 0xfd, 0xf9, 0xfb, 0x55, 0xe9, 0x6f, 0xdf,
	0xaf, 0x4a, 0xbf, 0xf9, 0xfb, 0xea, 0x9d, 0x7f, 0x05, 0x00, 0x00, 0xff, 0xff, 0xb7, 0x17, 0x5f,
	0xd5, 0x57, 0x1d, 0x00, 0x00,
}
