// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-music-rank/user-music-rank.proto

package user_music_rank // import "golang.52tt.com/protocol/services/user-music-rank"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type GloryLevel int32

const (
	GloryLevel_City     GloryLevel = 0
	GloryLevel_Province GloryLevel = 1
	GloryLevel_Country  GloryLevel = 2
)

var GloryLevel_name = map[int32]string{
	0: "City",
	1: "Province",
	2: "Country",
}
var GloryLevel_value = map[string]int32{
	"City":     0,
	"Province": 1,
	"Country":  2,
}

func (x GloryLevel) String() string {
	return proto.EnumName(GloryLevel_name, int32(x))
}
func (GloryLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{0}
}

type GetUserSingerScoreDetailReq_ReqType int32

const (
	GetUserSingerScoreDetailReq_Full   GetUserSingerScoreDetailReq_ReqType = 0
	GetUserSingerScoreDetailReq_Simple GetUserSingerScoreDetailReq_ReqType = 1
)

var GetUserSingerScoreDetailReq_ReqType_name = map[int32]string{
	0: "Full",
	1: "Simple",
}
var GetUserSingerScoreDetailReq_ReqType_value = map[string]int32{
	"Full":   0,
	"Simple": 1,
}

func (x GetUserSingerScoreDetailReq_ReqType) String() string {
	return proto.EnumName(GetUserSingerScoreDetailReq_ReqType_name, int32(x))
}
func (GetUserSingerScoreDetailReq_ReqType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{65, 0}
}

type ListUserMusicRecordsResp_SingType int32

const (
	ListUserMusicRecordsResp_Single  ListUserMusicRecordsResp_SingType = 0
	ListUserMusicRecordsResp_Tutti   ListUserMusicRecordsResp_SingType = 1
	ListUserMusicRecordsResp_RobSing ListUserMusicRecordsResp_SingType = 2
)

var ListUserMusicRecordsResp_SingType_name = map[int32]string{
	0: "Single",
	1: "Tutti",
	2: "RobSing",
}
var ListUserMusicRecordsResp_SingType_value = map[string]int32{
	"Single":  0,
	"Tutti":   1,
	"RobSing": 2,
}

func (x ListUserMusicRecordsResp_SingType) String() string {
	return proto.EnumName(ListUserMusicRecordsResp_SingType_name, int32(x))
}
func (ListUserMusicRecordsResp_SingType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{72, 0}
}

type UserMusicRankDialogConfirmReq_ReqType int32

const (
	UserMusicRankDialogConfirmReq_SessionDialog UserMusicRankDialogConfirmReq_ReqType = 0
	UserMusicRankDialogConfirmReq_GloryDialog   UserMusicRankDialogConfirmReq_ReqType = 1
)

var UserMusicRankDialogConfirmReq_ReqType_name = map[int32]string{
	0: "SessionDialog",
	1: "GloryDialog",
}
var UserMusicRankDialogConfirmReq_ReqType_value = map[string]int32{
	"SessionDialog": 0,
	"GloryDialog":   1,
}

func (x UserMusicRankDialogConfirmReq_ReqType) String() string {
	return proto.EnumName(UserMusicRankDialogConfirmReq_ReqType_name, int32(x))
}
func (UserMusicRankDialogConfirmReq_ReqType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{87, 0}
}

type AddBlackUidsReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBlackUidsReq) Reset()         { *m = AddBlackUidsReq{} }
func (m *AddBlackUidsReq) String() string { return proto.CompactTextString(m) }
func (*AddBlackUidsReq) ProtoMessage()    {}
func (*AddBlackUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{0}
}
func (m *AddBlackUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBlackUidsReq.Unmarshal(m, b)
}
func (m *AddBlackUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBlackUidsReq.Marshal(b, m, deterministic)
}
func (dst *AddBlackUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBlackUidsReq.Merge(dst, src)
}
func (m *AddBlackUidsReq) XXX_Size() int {
	return xxx_messageInfo_AddBlackUidsReq.Size(m)
}
func (m *AddBlackUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBlackUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddBlackUidsReq proto.InternalMessageInfo

func (m *AddBlackUidsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type AddBlackUidsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddBlackUidsResp) Reset()         { *m = AddBlackUidsResp{} }
func (m *AddBlackUidsResp) String() string { return proto.CompactTextString(m) }
func (*AddBlackUidsResp) ProtoMessage()    {}
func (*AddBlackUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{1}
}
func (m *AddBlackUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddBlackUidsResp.Unmarshal(m, b)
}
func (m *AddBlackUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddBlackUidsResp.Marshal(b, m, deterministic)
}
func (dst *AddBlackUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddBlackUidsResp.Merge(dst, src)
}
func (m *AddBlackUidsResp) XXX_Size() int {
	return xxx_messageInfo_AddBlackUidsResp.Size(m)
}
func (m *AddBlackUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddBlackUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddBlackUidsResp proto.InternalMessageInfo

type RemoveBlackUidsReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveBlackUidsReq) Reset()         { *m = RemoveBlackUidsReq{} }
func (m *RemoveBlackUidsReq) String() string { return proto.CompactTextString(m) }
func (*RemoveBlackUidsReq) ProtoMessage()    {}
func (*RemoveBlackUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{2}
}
func (m *RemoveBlackUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveBlackUidsReq.Unmarshal(m, b)
}
func (m *RemoveBlackUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveBlackUidsReq.Marshal(b, m, deterministic)
}
func (dst *RemoveBlackUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveBlackUidsReq.Merge(dst, src)
}
func (m *RemoveBlackUidsReq) XXX_Size() int {
	return xxx_messageInfo_RemoveBlackUidsReq.Size(m)
}
func (m *RemoveBlackUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveBlackUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveBlackUidsReq proto.InternalMessageInfo

func (m *RemoveBlackUidsReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type RemoveBlackUidsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RemoveBlackUidsResp) Reset()         { *m = RemoveBlackUidsResp{} }
func (m *RemoveBlackUidsResp) String() string { return proto.CompactTextString(m) }
func (*RemoveBlackUidsResp) ProtoMessage()    {}
func (*RemoveBlackUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{3}
}
func (m *RemoveBlackUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RemoveBlackUidsResp.Unmarshal(m, b)
}
func (m *RemoveBlackUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RemoveBlackUidsResp.Marshal(b, m, deterministic)
}
func (dst *RemoveBlackUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RemoveBlackUidsResp.Merge(dst, src)
}
func (m *RemoveBlackUidsResp) XXX_Size() int {
	return xxx_messageInfo_RemoveBlackUidsResp.Size(m)
}
func (m *RemoveBlackUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RemoveBlackUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_RemoveBlackUidsResp proto.InternalMessageInfo

type ListBlackUidsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListBlackUidsReq) Reset()         { *m = ListBlackUidsReq{} }
func (m *ListBlackUidsReq) String() string { return proto.CompactTextString(m) }
func (*ListBlackUidsReq) ProtoMessage()    {}
func (*ListBlackUidsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{4}
}
func (m *ListBlackUidsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListBlackUidsReq.Unmarshal(m, b)
}
func (m *ListBlackUidsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListBlackUidsReq.Marshal(b, m, deterministic)
}
func (dst *ListBlackUidsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListBlackUidsReq.Merge(dst, src)
}
func (m *ListBlackUidsReq) XXX_Size() int {
	return xxx_messageInfo_ListBlackUidsReq.Size(m)
}
func (m *ListBlackUidsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListBlackUidsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListBlackUidsReq proto.InternalMessageInfo

type ListBlackUidsResp struct {
	Records              []*ListBlackUidsResp_UidRecord `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *ListBlackUidsResp) Reset()         { *m = ListBlackUidsResp{} }
func (m *ListBlackUidsResp) String() string { return proto.CompactTextString(m) }
func (*ListBlackUidsResp) ProtoMessage()    {}
func (*ListBlackUidsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{5}
}
func (m *ListBlackUidsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListBlackUidsResp.Unmarshal(m, b)
}
func (m *ListBlackUidsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListBlackUidsResp.Marshal(b, m, deterministic)
}
func (dst *ListBlackUidsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListBlackUidsResp.Merge(dst, src)
}
func (m *ListBlackUidsResp) XXX_Size() int {
	return xxx_messageInfo_ListBlackUidsResp.Size(m)
}
func (m *ListBlackUidsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListBlackUidsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListBlackUidsResp proto.InternalMessageInfo

func (m *ListBlackUidsResp) GetRecords() []*ListBlackUidsResp_UidRecord {
	if m != nil {
		return m.Records
	}
	return nil
}

type ListBlackUidsResp_UidRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CreateAt             int64    `protobuf:"varint,2,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListBlackUidsResp_UidRecord) Reset()         { *m = ListBlackUidsResp_UidRecord{} }
func (m *ListBlackUidsResp_UidRecord) String() string { return proto.CompactTextString(m) }
func (*ListBlackUidsResp_UidRecord) ProtoMessage()    {}
func (*ListBlackUidsResp_UidRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{5, 0}
}
func (m *ListBlackUidsResp_UidRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListBlackUidsResp_UidRecord.Unmarshal(m, b)
}
func (m *ListBlackUidsResp_UidRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListBlackUidsResp_UidRecord.Marshal(b, m, deterministic)
}
func (dst *ListBlackUidsResp_UidRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListBlackUidsResp_UidRecord.Merge(dst, src)
}
func (m *ListBlackUidsResp_UidRecord) XXX_Size() int {
	return xxx_messageInfo_ListBlackUidsResp_UidRecord.Size(m)
}
func (m *ListBlackUidsResp_UidRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_ListBlackUidsResp_UidRecord.DiscardUnknown(m)
}

var xxx_messageInfo_ListBlackUidsResp_UidRecord proto.InternalMessageInfo

func (m *ListBlackUidsResp_UidRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListBlackUidsResp_UidRecord) GetCreateAt() int64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

type DelSongsReq struct {
	SongIds              []string `protobuf:"bytes,1,rep,name=song_ids,json=songIds,proto3" json:"song_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongsReq) Reset()         { *m = DelSongsReq{} }
func (m *DelSongsReq) String() string { return proto.CompactTextString(m) }
func (*DelSongsReq) ProtoMessage()    {}
func (*DelSongsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{6}
}
func (m *DelSongsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongsReq.Unmarshal(m, b)
}
func (m *DelSongsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongsReq.Marshal(b, m, deterministic)
}
func (dst *DelSongsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongsReq.Merge(dst, src)
}
func (m *DelSongsReq) XXX_Size() int {
	return xxx_messageInfo_DelSongsReq.Size(m)
}
func (m *DelSongsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongsReq proto.InternalMessageInfo

func (m *DelSongsReq) GetSongIds() []string {
	if m != nil {
		return m.SongIds
	}
	return nil
}

type DelSongsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelSongsResp) Reset()         { *m = DelSongsResp{} }
func (m *DelSongsResp) String() string { return proto.CompactTextString(m) }
func (*DelSongsResp) ProtoMessage()    {}
func (*DelSongsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{7}
}
func (m *DelSongsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelSongsResp.Unmarshal(m, b)
}
func (m *DelSongsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelSongsResp.Marshal(b, m, deterministic)
}
func (dst *DelSongsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelSongsResp.Merge(dst, src)
}
func (m *DelSongsResp) XXX_Size() int {
	return xxx_messageInfo_DelSongsResp.Size(m)
}
func (m *DelSongsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelSongsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelSongsResp proto.InternalMessageInfo

type UpsertCitiesReq struct {
	Cities               []*UpsertCitiesReq_City `protobuf:"bytes,1,rep,name=cities,proto3" json:"cities,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpsertCitiesReq) Reset()         { *m = UpsertCitiesReq{} }
func (m *UpsertCitiesReq) String() string { return proto.CompactTextString(m) }
func (*UpsertCitiesReq) ProtoMessage()    {}
func (*UpsertCitiesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{8}
}
func (m *UpsertCitiesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertCitiesReq.Unmarshal(m, b)
}
func (m *UpsertCitiesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertCitiesReq.Marshal(b, m, deterministic)
}
func (dst *UpsertCitiesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertCitiesReq.Merge(dst, src)
}
func (m *UpsertCitiesReq) XXX_Size() int {
	return xxx_messageInfo_UpsertCitiesReq.Size(m)
}
func (m *UpsertCitiesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertCitiesReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertCitiesReq proto.InternalMessageInfo

func (m *UpsertCitiesReq) GetCities() []*UpsertCitiesReq_City {
	if m != nil {
		return m.Cities
	}
	return nil
}

type UpsertCitiesReq_City struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertCitiesReq_City) Reset()         { *m = UpsertCitiesReq_City{} }
func (m *UpsertCitiesReq_City) String() string { return proto.CompactTextString(m) }
func (*UpsertCitiesReq_City) ProtoMessage()    {}
func (*UpsertCitiesReq_City) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{8, 0}
}
func (m *UpsertCitiesReq_City) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertCitiesReq_City.Unmarshal(m, b)
}
func (m *UpsertCitiesReq_City) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertCitiesReq_City.Marshal(b, m, deterministic)
}
func (dst *UpsertCitiesReq_City) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertCitiesReq_City.Merge(dst, src)
}
func (m *UpsertCitiesReq_City) XXX_Size() int {
	return xxx_messageInfo_UpsertCitiesReq_City.Size(m)
}
func (m *UpsertCitiesReq_City) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertCitiesReq_City.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertCitiesReq_City proto.InternalMessageInfo

func (m *UpsertCitiesReq_City) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertCitiesReq_City) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type UpsertCitiesResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertCitiesResp) Reset()         { *m = UpsertCitiesResp{} }
func (m *UpsertCitiesResp) String() string { return proto.CompactTextString(m) }
func (*UpsertCitiesResp) ProtoMessage()    {}
func (*UpsertCitiesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{9}
}
func (m *UpsertCitiesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertCitiesResp.Unmarshal(m, b)
}
func (m *UpsertCitiesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertCitiesResp.Marshal(b, m, deterministic)
}
func (dst *UpsertCitiesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertCitiesResp.Merge(dst, src)
}
func (m *UpsertCitiesResp) XXX_Size() int {
	return xxx_messageInfo_UpsertCitiesResp.Size(m)
}
func (m *UpsertCitiesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertCitiesResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertCitiesResp proto.InternalMessageInfo

type UpsertSchoolsReq struct {
	Schools              []*UpsertSchoolsReq_SchoolInfo `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *UpsertSchoolsReq) Reset()         { *m = UpsertSchoolsReq{} }
func (m *UpsertSchoolsReq) String() string { return proto.CompactTextString(m) }
func (*UpsertSchoolsReq) ProtoMessage()    {}
func (*UpsertSchoolsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{10}
}
func (m *UpsertSchoolsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSchoolsReq.Unmarshal(m, b)
}
func (m *UpsertSchoolsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSchoolsReq.Marshal(b, m, deterministic)
}
func (dst *UpsertSchoolsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSchoolsReq.Merge(dst, src)
}
func (m *UpsertSchoolsReq) XXX_Size() int {
	return xxx_messageInfo_UpsertSchoolsReq.Size(m)
}
func (m *UpsertSchoolsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSchoolsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSchoolsReq proto.InternalMessageInfo

func (m *UpsertSchoolsReq) GetSchools() []*UpsertSchoolsReq_SchoolInfo {
	if m != nil {
		return m.Schools
	}
	return nil
}

type UpsertSchoolsReq_SchoolInfo struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	InternalId           string   `protobuf:"bytes,4,opt,name=internal_id,json=internalId,proto3" json:"internal_id,omitempty"`
	CityId               string   `protobuf:"bytes,5,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertSchoolsReq_SchoolInfo) Reset()         { *m = UpsertSchoolsReq_SchoolInfo{} }
func (m *UpsertSchoolsReq_SchoolInfo) String() string { return proto.CompactTextString(m) }
func (*UpsertSchoolsReq_SchoolInfo) ProtoMessage()    {}
func (*UpsertSchoolsReq_SchoolInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{10, 0}
}
func (m *UpsertSchoolsReq_SchoolInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSchoolsReq_SchoolInfo.Unmarshal(m, b)
}
func (m *UpsertSchoolsReq_SchoolInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSchoolsReq_SchoolInfo.Marshal(b, m, deterministic)
}
func (dst *UpsertSchoolsReq_SchoolInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSchoolsReq_SchoolInfo.Merge(dst, src)
}
func (m *UpsertSchoolsReq_SchoolInfo) XXX_Size() int {
	return xxx_messageInfo_UpsertSchoolsReq_SchoolInfo.Size(m)
}
func (m *UpsertSchoolsReq_SchoolInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSchoolsReq_SchoolInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSchoolsReq_SchoolInfo proto.InternalMessageInfo

func (m *UpsertSchoolsReq_SchoolInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpsertSchoolsReq_SchoolInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *UpsertSchoolsReq_SchoolInfo) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *UpsertSchoolsReq_SchoolInfo) GetInternalId() string {
	if m != nil {
		return m.InternalId
	}
	return ""
}

func (m *UpsertSchoolsReq_SchoolInfo) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

type UpsertSchoolsResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertSchoolsResp) Reset()         { *m = UpsertSchoolsResp{} }
func (m *UpsertSchoolsResp) String() string { return proto.CompactTextString(m) }
func (*UpsertSchoolsResp) ProtoMessage()    {}
func (*UpsertSchoolsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{11}
}
func (m *UpsertSchoolsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertSchoolsResp.Unmarshal(m, b)
}
func (m *UpsertSchoolsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertSchoolsResp.Marshal(b, m, deterministic)
}
func (dst *UpsertSchoolsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertSchoolsResp.Merge(dst, src)
}
func (m *UpsertSchoolsResp) XXX_Size() int {
	return xxx_messageInfo_UpsertSchoolsResp.Size(m)
}
func (m *UpsertSchoolsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertSchoolsResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertSchoolsResp proto.InternalMessageInfo

type UpdateSchoolScoreReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Score                int64    `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSchoolScoreReq) Reset()         { *m = UpdateSchoolScoreReq{} }
func (m *UpdateSchoolScoreReq) String() string { return proto.CompactTextString(m) }
func (*UpdateSchoolScoreReq) ProtoMessage()    {}
func (*UpdateSchoolScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{12}
}
func (m *UpdateSchoolScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchoolScoreReq.Unmarshal(m, b)
}
func (m *UpdateSchoolScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchoolScoreReq.Marshal(b, m, deterministic)
}
func (dst *UpdateSchoolScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchoolScoreReq.Merge(dst, src)
}
func (m *UpdateSchoolScoreReq) XXX_Size() int {
	return xxx_messageInfo_UpdateSchoolScoreReq.Size(m)
}
func (m *UpdateSchoolScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchoolScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchoolScoreReq proto.InternalMessageInfo

func (m *UpdateSchoolScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateSchoolScoreReq) GetScore() int64 {
	if m != nil {
		return m.Score
	}
	return 0
}

type UpdateSchoolScoreResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateSchoolScoreResp) Reset()         { *m = UpdateSchoolScoreResp{} }
func (m *UpdateSchoolScoreResp) String() string { return proto.CompactTextString(m) }
func (*UpdateSchoolScoreResp) ProtoMessage()    {}
func (*UpdateSchoolScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{13}
}
func (m *UpdateSchoolScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateSchoolScoreResp.Unmarshal(m, b)
}
func (m *UpdateSchoolScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateSchoolScoreResp.Marshal(b, m, deterministic)
}
func (dst *UpdateSchoolScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateSchoolScoreResp.Merge(dst, src)
}
func (m *UpdateSchoolScoreResp) XXX_Size() int {
	return xxx_messageInfo_UpdateSchoolScoreResp.Size(m)
}
func (m *UpdateSchoolScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateSchoolScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateSchoolScoreResp proto.InternalMessageInfo

type GetMirrorDataReq struct {
	MirrorId             string   `protobuf:"bytes,1,opt,name=mirror_id,json=mirrorId,proto3" json:"mirror_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorDataReq) Reset()         { *m = GetMirrorDataReq{} }
func (m *GetMirrorDataReq) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataReq) ProtoMessage()    {}
func (*GetMirrorDataReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{14}
}
func (m *GetMirrorDataReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataReq.Unmarshal(m, b)
}
func (m *GetMirrorDataReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataReq.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataReq.Merge(dst, src)
}
func (m *GetMirrorDataReq) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataReq.Size(m)
}
func (m *GetMirrorDataReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataReq proto.InternalMessageInfo

func (m *GetMirrorDataReq) GetMirrorId() string {
	if m != nil {
		return m.MirrorId
	}
	return ""
}

type GetMirrorDataResp struct {
	User                 *GetMirrorDataResp_User           `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Schools              []*GetMirrorDataResp_SchoolInfo   `protobuf:"bytes,2,rep,name=schools,proto3" json:"schools,omitempty"`
	Users                []*GetMirrorDataResp_UserRankInfo `protobuf:"bytes,3,rep,name=users,proto3" json:"users,omitempty"`
	CityUsers            []*GetMirrorDataResp_UserRankInfo `protobuf:"bytes,4,rep,name=city_users,json=cityUsers,proto3" json:"city_users,omitempty"`
	CitySchools          []*GetMirrorDataResp_SchoolInfo   `protobuf:"bytes,5,rep,name=city_schools,json=citySchools,proto3" json:"city_schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetMirrorDataResp) Reset()         { *m = GetMirrorDataResp{} }
func (m *GetMirrorDataResp) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataResp) ProtoMessage()    {}
func (*GetMirrorDataResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{15}
}
func (m *GetMirrorDataResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataResp.Unmarshal(m, b)
}
func (m *GetMirrorDataResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataResp.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataResp.Merge(dst, src)
}
func (m *GetMirrorDataResp) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataResp.Size(m)
}
func (m *GetMirrorDataResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataResp proto.InternalMessageInfo

func (m *GetMirrorDataResp) GetUser() *GetMirrorDataResp_User {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *GetMirrorDataResp) GetSchools() []*GetMirrorDataResp_SchoolInfo {
	if m != nil {
		return m.Schools
	}
	return nil
}

func (m *GetMirrorDataResp) GetUsers() []*GetMirrorDataResp_UserRankInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *GetMirrorDataResp) GetCityUsers() []*GetMirrorDataResp_UserRankInfo {
	if m != nil {
		return m.CityUsers
	}
	return nil
}

func (m *GetMirrorDataResp) GetCitySchools() []*GetMirrorDataResp_SchoolInfo {
	if m != nil {
		return m.CitySchools
	}
	return nil
}

type GetMirrorDataResp_User struct {
	UserName             string   `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	UserAvatar           string   `protobuf:"bytes,2,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	HotScore             int64    `protobuf:"varint,3,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	SchoolId             string   `protobuf:"bytes,4,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	SchoolName           string   `protobuf:"bytes,5,opt,name=school_name,json=schoolName,proto3" json:"school_name,omitempty"`
	UserRank             int32    `protobuf:"varint,6,opt,name=user_rank,json=userRank,proto3" json:"user_rank,omitempty"`
	SchoolRank           int32    `protobuf:"varint,7,opt,name=school_rank,json=schoolRank,proto3" json:"school_rank,omitempty"`
	CityId               string   `protobuf:"bytes,8,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string   `protobuf:"bytes,9,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	CityRank             int32    `protobuf:"varint,10,opt,name=city_rank,json=cityRank,proto3" json:"city_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorDataResp_User) Reset()         { *m = GetMirrorDataResp_User{} }
func (m *GetMirrorDataResp_User) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataResp_User) ProtoMessage()    {}
func (*GetMirrorDataResp_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{15, 0}
}
func (m *GetMirrorDataResp_User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataResp_User.Unmarshal(m, b)
}
func (m *GetMirrorDataResp_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataResp_User.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataResp_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataResp_User.Merge(dst, src)
}
func (m *GetMirrorDataResp_User) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataResp_User.Size(m)
}
func (m *GetMirrorDataResp_User) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataResp_User.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataResp_User proto.InternalMessageInfo

func (m *GetMirrorDataResp_User) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *GetMirrorDataResp_User) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *GetMirrorDataResp_User) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *GetMirrorDataResp_User) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

func (m *GetMirrorDataResp_User) GetSchoolName() string {
	if m != nil {
		return m.SchoolName
	}
	return ""
}

func (m *GetMirrorDataResp_User) GetUserRank() int32 {
	if m != nil {
		return m.UserRank
	}
	return 0
}

func (m *GetMirrorDataResp_User) GetSchoolRank() int32 {
	if m != nil {
		return m.SchoolRank
	}
	return 0
}

func (m *GetMirrorDataResp_User) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *GetMirrorDataResp_User) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *GetMirrorDataResp_User) GetCityRank() int32 {
	if m != nil {
		return m.CityRank
	}
	return 0
}

type GetMirrorDataResp_UserRankInfo struct {
	UserName             string   `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	UserAvatar           string   `protobuf:"bytes,2,opt,name=user_avatar,json=userAvatar,proto3" json:"user_avatar,omitempty"`
	HotScore             int64    `protobuf:"varint,3,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	SchoolId             string   `protobuf:"bytes,4,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	SchoolName           string   `protobuf:"bytes,5,opt,name=school_name,json=schoolName,proto3" json:"school_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorDataResp_UserRankInfo) Reset()         { *m = GetMirrorDataResp_UserRankInfo{} }
func (m *GetMirrorDataResp_UserRankInfo) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataResp_UserRankInfo) ProtoMessage()    {}
func (*GetMirrorDataResp_UserRankInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{15, 1}
}
func (m *GetMirrorDataResp_UserRankInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataResp_UserRankInfo.Unmarshal(m, b)
}
func (m *GetMirrorDataResp_UserRankInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataResp_UserRankInfo.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataResp_UserRankInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataResp_UserRankInfo.Merge(dst, src)
}
func (m *GetMirrorDataResp_UserRankInfo) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataResp_UserRankInfo.Size(m)
}
func (m *GetMirrorDataResp_UserRankInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataResp_UserRankInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataResp_UserRankInfo proto.InternalMessageInfo

func (m *GetMirrorDataResp_UserRankInfo) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *GetMirrorDataResp_UserRankInfo) GetUserAvatar() string {
	if m != nil {
		return m.UserAvatar
	}
	return ""
}

func (m *GetMirrorDataResp_UserRankInfo) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *GetMirrorDataResp_UserRankInfo) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

func (m *GetMirrorDataResp_UserRankInfo) GetSchoolName() string {
	if m != nil {
		return m.SchoolName
	}
	return ""
}

type GetMirrorDataResp_SchoolInfo struct {
	HotScore             int64    `protobuf:"varint,1,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	SchoolId             string   `protobuf:"bytes,2,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	SchoolName           string   `protobuf:"bytes,3,opt,name=school_name,json=schoolName,proto3" json:"school_name,omitempty"`
	SchoolImg            string   `protobuf:"bytes,4,opt,name=school_img,json=schoolImg,proto3" json:"school_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorDataResp_SchoolInfo) Reset()         { *m = GetMirrorDataResp_SchoolInfo{} }
func (m *GetMirrorDataResp_SchoolInfo) String() string { return proto.CompactTextString(m) }
func (*GetMirrorDataResp_SchoolInfo) ProtoMessage()    {}
func (*GetMirrorDataResp_SchoolInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{15, 2}
}
func (m *GetMirrorDataResp_SchoolInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorDataResp_SchoolInfo.Unmarshal(m, b)
}
func (m *GetMirrorDataResp_SchoolInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorDataResp_SchoolInfo.Marshal(b, m, deterministic)
}
func (dst *GetMirrorDataResp_SchoolInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorDataResp_SchoolInfo.Merge(dst, src)
}
func (m *GetMirrorDataResp_SchoolInfo) XXX_Size() int {
	return xxx_messageInfo_GetMirrorDataResp_SchoolInfo.Size(m)
}
func (m *GetMirrorDataResp_SchoolInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorDataResp_SchoolInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorDataResp_SchoolInfo proto.InternalMessageInfo

func (m *GetMirrorDataResp_SchoolInfo) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *GetMirrorDataResp_SchoolInfo) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

func (m *GetMirrorDataResp_SchoolInfo) GetSchoolName() string {
	if m != nil {
		return m.SchoolName
	}
	return ""
}

func (m *GetMirrorDataResp_SchoolInfo) GetSchoolImg() string {
	if m != nil {
		return m.SchoolImg
	}
	return ""
}

type GetMirrorIdReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorIdReq) Reset()         { *m = GetMirrorIdReq{} }
func (m *GetMirrorIdReq) String() string { return proto.CompactTextString(m) }
func (*GetMirrorIdReq) ProtoMessage()    {}
func (*GetMirrorIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{16}
}
func (m *GetMirrorIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorIdReq.Unmarshal(m, b)
}
func (m *GetMirrorIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorIdReq.Marshal(b, m, deterministic)
}
func (dst *GetMirrorIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorIdReq.Merge(dst, src)
}
func (m *GetMirrorIdReq) XXX_Size() int {
	return xxx_messageInfo_GetMirrorIdReq.Size(m)
}
func (m *GetMirrorIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorIdReq proto.InternalMessageInfo

func (m *GetMirrorIdReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetMirrorIdResp struct {
	MirrorId             string   `protobuf:"bytes,1,opt,name=mirror_id,json=mirrorId,proto3" json:"mirror_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMirrorIdResp) Reset()         { *m = GetMirrorIdResp{} }
func (m *GetMirrorIdResp) String() string { return proto.CompactTextString(m) }
func (*GetMirrorIdResp) ProtoMessage()    {}
func (*GetMirrorIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{17}
}
func (m *GetMirrorIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMirrorIdResp.Unmarshal(m, b)
}
func (m *GetMirrorIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMirrorIdResp.Marshal(b, m, deterministic)
}
func (dst *GetMirrorIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMirrorIdResp.Merge(dst, src)
}
func (m *GetMirrorIdResp) XXX_Size() int {
	return xxx_messageInfo_GetMirrorIdResp.Size(m)
}
func (m *GetMirrorIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMirrorIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMirrorIdResp proto.InternalMessageInfo

func (m *GetMirrorIdResp) GetMirrorId() string {
	if m != nil {
		return m.MirrorId
	}
	return ""
}

type RebuildSchoolBindReq struct {
	SchoolId             string   `protobuf:"bytes,1,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildSchoolBindReq) Reset()         { *m = RebuildSchoolBindReq{} }
func (m *RebuildSchoolBindReq) String() string { return proto.CompactTextString(m) }
func (*RebuildSchoolBindReq) ProtoMessage()    {}
func (*RebuildSchoolBindReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{18}
}
func (m *RebuildSchoolBindReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildSchoolBindReq.Unmarshal(m, b)
}
func (m *RebuildSchoolBindReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildSchoolBindReq.Marshal(b, m, deterministic)
}
func (dst *RebuildSchoolBindReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildSchoolBindReq.Merge(dst, src)
}
func (m *RebuildSchoolBindReq) XXX_Size() int {
	return xxx_messageInfo_RebuildSchoolBindReq.Size(m)
}
func (m *RebuildSchoolBindReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildSchoolBindReq.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildSchoolBindReq proto.InternalMessageInfo

func (m *RebuildSchoolBindReq) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

type RebuildSchoolBindResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildSchoolBindResp) Reset()         { *m = RebuildSchoolBindResp{} }
func (m *RebuildSchoolBindResp) String() string { return proto.CompactTextString(m) }
func (*RebuildSchoolBindResp) ProtoMessage()    {}
func (*RebuildSchoolBindResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{19}
}
func (m *RebuildSchoolBindResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildSchoolBindResp.Unmarshal(m, b)
}
func (m *RebuildSchoolBindResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildSchoolBindResp.Marshal(b, m, deterministic)
}
func (dst *RebuildSchoolBindResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildSchoolBindResp.Merge(dst, src)
}
func (m *RebuildSchoolBindResp) XXX_Size() int {
	return xxx_messageInfo_RebuildSchoolBindResp.Size(m)
}
func (m *RebuildSchoolBindResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildSchoolBindResp.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildSchoolBindResp proto.InternalMessageInfo

type RebuildSchoolRankReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildSchoolRankReq) Reset()         { *m = RebuildSchoolRankReq{} }
func (m *RebuildSchoolRankReq) String() string { return proto.CompactTextString(m) }
func (*RebuildSchoolRankReq) ProtoMessage()    {}
func (*RebuildSchoolRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{20}
}
func (m *RebuildSchoolRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildSchoolRankReq.Unmarshal(m, b)
}
func (m *RebuildSchoolRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildSchoolRankReq.Marshal(b, m, deterministic)
}
func (dst *RebuildSchoolRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildSchoolRankReq.Merge(dst, src)
}
func (m *RebuildSchoolRankReq) XXX_Size() int {
	return xxx_messageInfo_RebuildSchoolRankReq.Size(m)
}
func (m *RebuildSchoolRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildSchoolRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildSchoolRankReq proto.InternalMessageInfo

type RebuildSchoolRankResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RebuildSchoolRankResp) Reset()         { *m = RebuildSchoolRankResp{} }
func (m *RebuildSchoolRankResp) String() string { return proto.CompactTextString(m) }
func (*RebuildSchoolRankResp) ProtoMessage()    {}
func (*RebuildSchoolRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{21}
}
func (m *RebuildSchoolRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RebuildSchoolRankResp.Unmarshal(m, b)
}
func (m *RebuildSchoolRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RebuildSchoolRankResp.Marshal(b, m, deterministic)
}
func (dst *RebuildSchoolRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RebuildSchoolRankResp.Merge(dst, src)
}
func (m *RebuildSchoolRankResp) XXX_Size() int {
	return xxx_messageInfo_RebuildSchoolRankResp.Size(m)
}
func (m *RebuildSchoolRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_RebuildSchoolRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_RebuildSchoolRankResp proto.InternalMessageInfo

type GetSchoolRanReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSchoolRanReq) Reset()         { *m = GetSchoolRanReq{} }
func (m *GetSchoolRanReq) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRanReq) ProtoMessage()    {}
func (*GetSchoolRanReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{22}
}
func (m *GetSchoolRanReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRanReq.Unmarshal(m, b)
}
func (m *GetSchoolRanReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRanReq.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRanReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRanReq.Merge(dst, src)
}
func (m *GetSchoolRanReq) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRanReq.Size(m)
}
func (m *GetSchoolRanReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRanReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRanReq proto.InternalMessageInfo

func (m *GetSchoolRanReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSchoolRanResp struct {
	Schools              []*GetSchoolRanResp_School `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetSchoolRanResp) Reset()         { *m = GetSchoolRanResp{} }
func (m *GetSchoolRanResp) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRanResp) ProtoMessage()    {}
func (*GetSchoolRanResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{23}
}
func (m *GetSchoolRanResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRanResp.Unmarshal(m, b)
}
func (m *GetSchoolRanResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRanResp.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRanResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRanResp.Merge(dst, src)
}
func (m *GetSchoolRanResp) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRanResp.Size(m)
}
func (m *GetSchoolRanResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRanResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRanResp proto.InternalMessageInfo

func (m *GetSchoolRanResp) GetSchools() []*GetSchoolRanResp_School {
	if m != nil {
		return m.Schools
	}
	return nil
}

type GetSchoolRanResp_School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	HotScore             int64    `protobuf:"varint,4,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSchoolRanResp_School) Reset()         { *m = GetSchoolRanResp_School{} }
func (m *GetSchoolRanResp_School) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRanResp_School) ProtoMessage()    {}
func (*GetSchoolRanResp_School) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{23, 0}
}
func (m *GetSchoolRanResp_School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRanResp_School.Unmarshal(m, b)
}
func (m *GetSchoolRanResp_School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRanResp_School.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRanResp_School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRanResp_School.Merge(dst, src)
}
func (m *GetSchoolRanResp_School) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRanResp_School.Size(m)
}
func (m *GetSchoolRanResp_School) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRanResp_School.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRanResp_School proto.InternalMessageInfo

func (m *GetSchoolRanResp_School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetSchoolRanResp_School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetSchoolRanResp_School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *GetSchoolRanResp_School) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

type GetUserRankInSchoolReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRankInSchoolReq) Reset()         { *m = GetUserRankInSchoolReq{} }
func (m *GetUserRankInSchoolReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInSchoolReq) ProtoMessage()    {}
func (*GetUserRankInSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{24}
}
func (m *GetUserRankInSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInSchoolReq.Unmarshal(m, b)
}
func (m *GetUserRankInSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInSchoolReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInSchoolReq.Merge(dst, src)
}
func (m *GetUserRankInSchoolReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInSchoolReq.Size(m)
}
func (m *GetUserRankInSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInSchoolReq proto.InternalMessageInfo

func (m *GetUserRankInSchoolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRankInSchoolResp struct {
	Users                []*GetUserRankInSchoolResp_User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetUserRankInSchoolResp) Reset()         { *m = GetUserRankInSchoolResp{} }
func (m *GetUserRankInSchoolResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInSchoolResp) ProtoMessage()    {}
func (*GetUserRankInSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{25}
}
func (m *GetUserRankInSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInSchoolResp.Unmarshal(m, b)
}
func (m *GetUserRankInSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInSchoolResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInSchoolResp.Merge(dst, src)
}
func (m *GetUserRankInSchoolResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInSchoolResp.Size(m)
}
func (m *GetUserRankInSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInSchoolResp proto.InternalMessageInfo

func (m *GetUserRankInSchoolResp) GetUsers() []*GetUserRankInSchoolResp_User {
	if m != nil {
		return m.Users
	}
	return nil
}

type GetUserRankInSchoolResp_User struct {
	Uid                  uint32                          `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string                          `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	School               *GetUserRankInSchoolResp_School `protobuf:"bytes,3,opt,name=school,proto3" json:"school,omitempty"`
	HotScore             int64                           `protobuf:"varint,4,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetUserRankInSchoolResp_User) Reset()         { *m = GetUserRankInSchoolResp_User{} }
func (m *GetUserRankInSchoolResp_User) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInSchoolResp_User) ProtoMessage()    {}
func (*GetUserRankInSchoolResp_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{25, 0}
}
func (m *GetUserRankInSchoolResp_User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInSchoolResp_User.Unmarshal(m, b)
}
func (m *GetUserRankInSchoolResp_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInSchoolResp_User.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInSchoolResp_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInSchoolResp_User.Merge(dst, src)
}
func (m *GetUserRankInSchoolResp_User) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInSchoolResp_User.Size(m)
}
func (m *GetUserRankInSchoolResp_User) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInSchoolResp_User.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInSchoolResp_User proto.InternalMessageInfo

func (m *GetUserRankInSchoolResp_User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRankInSchoolResp_User) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserRankInSchoolResp_User) GetSchool() *GetUserRankInSchoolResp_School {
	if m != nil {
		return m.School
	}
	return nil
}

func (m *GetUserRankInSchoolResp_User) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

type GetUserRankInSchoolResp_School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRankInSchoolResp_School) Reset()         { *m = GetUserRankInSchoolResp_School{} }
func (m *GetUserRankInSchoolResp_School) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInSchoolResp_School) ProtoMessage()    {}
func (*GetUserRankInSchoolResp_School) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{25, 1}
}
func (m *GetUserRankInSchoolResp_School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInSchoolResp_School.Unmarshal(m, b)
}
func (m *GetUserRankInSchoolResp_School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInSchoolResp_School.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInSchoolResp_School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInSchoolResp_School.Merge(dst, src)
}
func (m *GetUserRankInSchoolResp_School) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInSchoolResp_School.Size(m)
}
func (m *GetUserRankInSchoolResp_School) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInSchoolResp_School.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInSchoolResp_School proto.InternalMessageInfo

func (m *GetUserRankInSchoolResp_School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetUserRankInSchoolResp_School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserRankInSchoolResp_School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

type GetSchoolRankInCityReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSchoolRankInCityReq) Reset()         { *m = GetSchoolRankInCityReq{} }
func (m *GetSchoolRankInCityReq) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRankInCityReq) ProtoMessage()    {}
func (*GetSchoolRankInCityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{26}
}
func (m *GetSchoolRankInCityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRankInCityReq.Unmarshal(m, b)
}
func (m *GetSchoolRankInCityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRankInCityReq.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRankInCityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRankInCityReq.Merge(dst, src)
}
func (m *GetSchoolRankInCityReq) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRankInCityReq.Size(m)
}
func (m *GetSchoolRankInCityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRankInCityReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRankInCityReq proto.InternalMessageInfo

func (m *GetSchoolRankInCityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSchoolRankInCityResp struct {
	Schools              []*GetSchoolRankInCityResp_School `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	CityId               string                            `protobuf:"bytes,2,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string                            `protobuf:"bytes,3,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *GetSchoolRankInCityResp) Reset()         { *m = GetSchoolRankInCityResp{} }
func (m *GetSchoolRankInCityResp) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRankInCityResp) ProtoMessage()    {}
func (*GetSchoolRankInCityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{27}
}
func (m *GetSchoolRankInCityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRankInCityResp.Unmarshal(m, b)
}
func (m *GetSchoolRankInCityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRankInCityResp.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRankInCityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRankInCityResp.Merge(dst, src)
}
func (m *GetSchoolRankInCityResp) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRankInCityResp.Size(m)
}
func (m *GetSchoolRankInCityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRankInCityResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRankInCityResp proto.InternalMessageInfo

func (m *GetSchoolRankInCityResp) GetSchools() []*GetSchoolRankInCityResp_School {
	if m != nil {
		return m.Schools
	}
	return nil
}

func (m *GetSchoolRankInCityResp) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *GetSchoolRankInCityResp) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

type GetSchoolRankInCityResp_School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	HotScore             int64    `protobuf:"varint,4,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSchoolRankInCityResp_School) Reset()         { *m = GetSchoolRankInCityResp_School{} }
func (m *GetSchoolRankInCityResp_School) String() string { return proto.CompactTextString(m) }
func (*GetSchoolRankInCityResp_School) ProtoMessage()    {}
func (*GetSchoolRankInCityResp_School) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{27, 0}
}
func (m *GetSchoolRankInCityResp_School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSchoolRankInCityResp_School.Unmarshal(m, b)
}
func (m *GetSchoolRankInCityResp_School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSchoolRankInCityResp_School.Marshal(b, m, deterministic)
}
func (dst *GetSchoolRankInCityResp_School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSchoolRankInCityResp_School.Merge(dst, src)
}
func (m *GetSchoolRankInCityResp_School) XXX_Size() int {
	return xxx_messageInfo_GetSchoolRankInCityResp_School.Size(m)
}
func (m *GetSchoolRankInCityResp_School) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSchoolRankInCityResp_School.DiscardUnknown(m)
}

var xxx_messageInfo_GetSchoolRankInCityResp_School proto.InternalMessageInfo

func (m *GetSchoolRankInCityResp_School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetSchoolRankInCityResp_School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetSchoolRankInCityResp_School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *GetSchoolRankInCityResp_School) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

type GetUserRankInCityReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRankInCityReq) Reset()         { *m = GetUserRankInCityReq{} }
func (m *GetUserRankInCityReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInCityReq) ProtoMessage()    {}
func (*GetUserRankInCityReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{28}
}
func (m *GetUserRankInCityReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInCityReq.Unmarshal(m, b)
}
func (m *GetUserRankInCityReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInCityReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInCityReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInCityReq.Merge(dst, src)
}
func (m *GetUserRankInCityReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInCityReq.Size(m)
}
func (m *GetUserRankInCityReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInCityReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInCityReq proto.InternalMessageInfo

func (m *GetUserRankInCityReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserRankInCityResp struct {
	Users                []*GetUserRankInCityResp_User `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	CityId               string                        `protobuf:"bytes,2,opt,name=city_id,json=cityId,proto3" json:"city_id,omitempty"`
	CityName             string                        `protobuf:"bytes,3,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetUserRankInCityResp) Reset()         { *m = GetUserRankInCityResp{} }
func (m *GetUserRankInCityResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInCityResp) ProtoMessage()    {}
func (*GetUserRankInCityResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{29}
}
func (m *GetUserRankInCityResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInCityResp.Unmarshal(m, b)
}
func (m *GetUserRankInCityResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInCityResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInCityResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInCityResp.Merge(dst, src)
}
func (m *GetUserRankInCityResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInCityResp.Size(m)
}
func (m *GetUserRankInCityResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInCityResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInCityResp proto.InternalMessageInfo

func (m *GetUserRankInCityResp) GetUsers() []*GetUserRankInCityResp_User {
	if m != nil {
		return m.Users
	}
	return nil
}

func (m *GetUserRankInCityResp) GetCityId() string {
	if m != nil {
		return m.CityId
	}
	return ""
}

func (m *GetUserRankInCityResp) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

type GetUserRankInCityResp_User struct {
	Uid                  uint32                        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string                        `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	School               *GetUserRankInCityResp_School `protobuf:"bytes,3,opt,name=school,proto3" json:"school,omitempty"`
	HotScore             int64                         `protobuf:"varint,4,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetUserRankInCityResp_User) Reset()         { *m = GetUserRankInCityResp_User{} }
func (m *GetUserRankInCityResp_User) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInCityResp_User) ProtoMessage()    {}
func (*GetUserRankInCityResp_User) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{29, 0}
}
func (m *GetUserRankInCityResp_User) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInCityResp_User.Unmarshal(m, b)
}
func (m *GetUserRankInCityResp_User) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInCityResp_User.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInCityResp_User) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInCityResp_User.Merge(dst, src)
}
func (m *GetUserRankInCityResp_User) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInCityResp_User.Size(m)
}
func (m *GetUserRankInCityResp_User) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInCityResp_User.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInCityResp_User proto.InternalMessageInfo

func (m *GetUserRankInCityResp_User) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRankInCityResp_User) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserRankInCityResp_User) GetSchool() *GetUserRankInCityResp_School {
	if m != nil {
		return m.School
	}
	return nil
}

func (m *GetUserRankInCityResp_User) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

type GetUserRankInCityResp_School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRankInCityResp_School) Reset()         { *m = GetUserRankInCityResp_School{} }
func (m *GetUserRankInCityResp_School) String() string { return proto.CompactTextString(m) }
func (*GetUserRankInCityResp_School) ProtoMessage()    {}
func (*GetUserRankInCityResp_School) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{29, 1}
}
func (m *GetUserRankInCityResp_School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRankInCityResp_School.Unmarshal(m, b)
}
func (m *GetUserRankInCityResp_School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRankInCityResp_School.Marshal(b, m, deterministic)
}
func (dst *GetUserRankInCityResp_School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRankInCityResp_School.Merge(dst, src)
}
func (m *GetUserRankInCityResp_School) XXX_Size() int {
	return xxx_messageInfo_GetUserRankInCityResp_School.Size(m)
}
func (m *GetUserRankInCityResp_School) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRankInCityResp_School.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRankInCityResp_School proto.InternalMessageInfo

func (m *GetUserRankInCityResp_School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetUserRankInCityResp_School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserRankInCityResp_School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

type BindSchoolReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SchoolId             string   `protobuf:"bytes,2,opt,name=school_id,json=schoolId,proto3" json:"school_id,omitempty"`
	RealName             string   `protobuf:"bytes,3,opt,name=real_name,json=realName,proto3" json:"real_name,omitempty"`
	Phone                string   `protobuf:"bytes,4,opt,name=phone,proto3" json:"phone,omitempty"`
	App_ID               uint32   `protobuf:"varint,5,opt,name=app_ID,json=appID,proto3" json:"app_ID,omitempty"`
	Os                   uint32   `protobuf:"varint,6,opt,name=os,proto3" json:"os,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindSchoolReq) Reset()         { *m = BindSchoolReq{} }
func (m *BindSchoolReq) String() string { return proto.CompactTextString(m) }
func (*BindSchoolReq) ProtoMessage()    {}
func (*BindSchoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{30}
}
func (m *BindSchoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindSchoolReq.Unmarshal(m, b)
}
func (m *BindSchoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindSchoolReq.Marshal(b, m, deterministic)
}
func (dst *BindSchoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindSchoolReq.Merge(dst, src)
}
func (m *BindSchoolReq) XXX_Size() int {
	return xxx_messageInfo_BindSchoolReq.Size(m)
}
func (m *BindSchoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindSchoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindSchoolReq proto.InternalMessageInfo

func (m *BindSchoolReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BindSchoolReq) GetSchoolId() string {
	if m != nil {
		return m.SchoolId
	}
	return ""
}

func (m *BindSchoolReq) GetRealName() string {
	if m != nil {
		return m.RealName
	}
	return ""
}

func (m *BindSchoolReq) GetPhone() string {
	if m != nil {
		return m.Phone
	}
	return ""
}

func (m *BindSchoolReq) GetApp_ID() uint32 {
	if m != nil {
		return m.App_ID
	}
	return 0
}

func (m *BindSchoolReq) GetOs() uint32 {
	if m != nil {
		return m.Os
	}
	return 0
}

type BindSchoolResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindSchoolResp) Reset()         { *m = BindSchoolResp{} }
func (m *BindSchoolResp) String() string { return proto.CompactTextString(m) }
func (*BindSchoolResp) ProtoMessage()    {}
func (*BindSchoolResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{31}
}
func (m *BindSchoolResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindSchoolResp.Unmarshal(m, b)
}
func (m *BindSchoolResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindSchoolResp.Marshal(b, m, deterministic)
}
func (dst *BindSchoolResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindSchoolResp.Merge(dst, src)
}
func (m *BindSchoolResp) XXX_Size() int {
	return xxx_messageInfo_BindSchoolResp.Size(m)
}
func (m *BindSchoolResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BindSchoolResp.DiscardUnknown(m)
}

var xxx_messageInfo_BindSchoolResp proto.InternalMessageInfo

type ListSchoolsReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListSchoolsReq) Reset()         { *m = ListSchoolsReq{} }
func (m *ListSchoolsReq) String() string { return proto.CompactTextString(m) }
func (*ListSchoolsReq) ProtoMessage()    {}
func (*ListSchoolsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{32}
}
func (m *ListSchoolsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolsReq.Unmarshal(m, b)
}
func (m *ListSchoolsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolsReq.Marshal(b, m, deterministic)
}
func (dst *ListSchoolsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolsReq.Merge(dst, src)
}
func (m *ListSchoolsReq) XXX_Size() int {
	return xxx_messageInfo_ListSchoolsReq.Size(m)
}
func (m *ListSchoolsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolsReq proto.InternalMessageInfo

type ListSchoolsResp struct {
	Schools              []*ListSchoolsResp_School `protobuf:"bytes,1,rep,name=schools,proto3" json:"schools,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *ListSchoolsResp) Reset()         { *m = ListSchoolsResp{} }
func (m *ListSchoolsResp) String() string { return proto.CompactTextString(m) }
func (*ListSchoolsResp) ProtoMessage()    {}
func (*ListSchoolsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{33}
}
func (m *ListSchoolsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolsResp.Unmarshal(m, b)
}
func (m *ListSchoolsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolsResp.Marshal(b, m, deterministic)
}
func (dst *ListSchoolsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolsResp.Merge(dst, src)
}
func (m *ListSchoolsResp) XXX_Size() int {
	return xxx_messageInfo_ListSchoolsResp.Size(m)
}
func (m *ListSchoolsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolsResp proto.InternalMessageInfo

func (m *ListSchoolsResp) GetSchools() []*ListSchoolsResp_School {
	if m != nil {
		return m.Schools
	}
	return nil
}

type ListSchoolsResp_School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListSchoolsResp_School) Reset()         { *m = ListSchoolsResp_School{} }
func (m *ListSchoolsResp_School) String() string { return proto.CompactTextString(m) }
func (*ListSchoolsResp_School) ProtoMessage()    {}
func (*ListSchoolsResp_School) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{33, 0}
}
func (m *ListSchoolsResp_School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListSchoolsResp_School.Unmarshal(m, b)
}
func (m *ListSchoolsResp_School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListSchoolsResp_School.Marshal(b, m, deterministic)
}
func (dst *ListSchoolsResp_School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListSchoolsResp_School.Merge(dst, src)
}
func (m *ListSchoolsResp_School) XXX_Size() int {
	return xxx_messageInfo_ListSchoolsResp_School.Size(m)
}
func (m *ListSchoolsResp_School) XXX_DiscardUnknown() {
	xxx_messageInfo_ListSchoolsResp_School.DiscardUnknown(m)
}

var xxx_messageInfo_ListSchoolsResp_School proto.InternalMessageInfo

func (m *ListSchoolsResp_School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ListSchoolsResp_School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ListSchoolsResp_School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

type GetUserSchoolInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSchoolInfoReq) Reset()         { *m = GetUserSchoolInfoReq{} }
func (m *GetUserSchoolInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSchoolInfoReq) ProtoMessage()    {}
func (*GetUserSchoolInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{34}
}
func (m *GetUserSchoolInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSchoolInfoReq.Unmarshal(m, b)
}
func (m *GetUserSchoolInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSchoolInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSchoolInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSchoolInfoReq.Merge(dst, src)
}
func (m *GetUserSchoolInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSchoolInfoReq.Size(m)
}
func (m *GetUserSchoolInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSchoolInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSchoolInfoReq proto.InternalMessageInfo

func (m *GetUserSchoolInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserSchoolInfoResp struct {
	Uid                  uint32                        `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	HotScore             int64                         `protobuf:"varint,2,opt,name=hot_score,json=hotScore,proto3" json:"hot_score,omitempty"`
	School               *GetUserSchoolInfoResp_School `protobuf:"bytes,3,opt,name=school,proto3" json:"school,omitempty"`
	CityName             string                        `protobuf:"bytes,4,opt,name=city_name,json=cityName,proto3" json:"city_name,omitempty"`
	CityCode             string                        `protobuf:"bytes,5,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	CityRank             int32                         `protobuf:"varint,6,opt,name=city_rank,json=cityRank,proto3" json:"city_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetUserSchoolInfoResp) Reset()         { *m = GetUserSchoolInfoResp{} }
func (m *GetUserSchoolInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSchoolInfoResp) ProtoMessage()    {}
func (*GetUserSchoolInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{35}
}
func (m *GetUserSchoolInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSchoolInfoResp.Unmarshal(m, b)
}
func (m *GetUserSchoolInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSchoolInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSchoolInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSchoolInfoResp.Merge(dst, src)
}
func (m *GetUserSchoolInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSchoolInfoResp.Size(m)
}
func (m *GetUserSchoolInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSchoolInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSchoolInfoResp proto.InternalMessageInfo

func (m *GetUserSchoolInfoResp) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSchoolInfoResp) GetHotScore() int64 {
	if m != nil {
		return m.HotScore
	}
	return 0
}

func (m *GetUserSchoolInfoResp) GetSchool() *GetUserSchoolInfoResp_School {
	if m != nil {
		return m.School
	}
	return nil
}

func (m *GetUserSchoolInfoResp) GetCityName() string {
	if m != nil {
		return m.CityName
	}
	return ""
}

func (m *GetUserSchoolInfoResp) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

func (m *GetUserSchoolInfoResp) GetCityRank() int32 {
	if m != nil {
		return m.CityRank
	}
	return 0
}

type GetUserSchoolInfoResp_School struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Img                  string   `protobuf:"bytes,3,opt,name=img,proto3" json:"img,omitempty"`
	UserRank             int32    `protobuf:"varint,4,opt,name=user_rank,json=userRank,proto3" json:"user_rank,omitempty"`
	SchoolRank           int32    `protobuf:"varint,5,opt,name=school_rank,json=schoolRank,proto3" json:"school_rank,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSchoolInfoResp_School) Reset()         { *m = GetUserSchoolInfoResp_School{} }
func (m *GetUserSchoolInfoResp_School) String() string { return proto.CompactTextString(m) }
func (*GetUserSchoolInfoResp_School) ProtoMessage()    {}
func (*GetUserSchoolInfoResp_School) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{35, 0}
}
func (m *GetUserSchoolInfoResp_School) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSchoolInfoResp_School.Unmarshal(m, b)
}
func (m *GetUserSchoolInfoResp_School) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSchoolInfoResp_School.Marshal(b, m, deterministic)
}
func (dst *GetUserSchoolInfoResp_School) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSchoolInfoResp_School.Merge(dst, src)
}
func (m *GetUserSchoolInfoResp_School) XXX_Size() int {
	return xxx_messageInfo_GetUserSchoolInfoResp_School.Size(m)
}
func (m *GetUserSchoolInfoResp_School) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSchoolInfoResp_School.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSchoolInfoResp_School proto.InternalMessageInfo

func (m *GetUserSchoolInfoResp_School) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *GetUserSchoolInfoResp_School) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserSchoolInfoResp_School) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

func (m *GetUserSchoolInfoResp_School) GetUserRank() int32 {
	if m != nil {
		return m.UserRank
	}
	return 0
}

func (m *GetUserSchoolInfoResp_School) GetSchoolRank() int32 {
	if m != nil {
		return m.SchoolRank
	}
	return 0
}

type GetSeasonWebInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeasonWebInfoReq) Reset()         { *m = GetSeasonWebInfoReq{} }
func (m *GetSeasonWebInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetSeasonWebInfoReq) ProtoMessage()    {}
func (*GetSeasonWebInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{36}
}
func (m *GetSeasonWebInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeasonWebInfoReq.Unmarshal(m, b)
}
func (m *GetSeasonWebInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeasonWebInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetSeasonWebInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeasonWebInfoReq.Merge(dst, src)
}
func (m *GetSeasonWebInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetSeasonWebInfoReq.Size(m)
}
func (m *GetSeasonWebInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeasonWebInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeasonWebInfoReq proto.InternalMessageInfo

type GetSeasonWebInfoResp struct {
	TitleImg             string   `protobuf:"bytes,1,opt,name=title_img,json=titleImg,proto3" json:"title_img,omitempty"`
	DescImgs             []string `protobuf:"bytes,2,rep,name=desc_imgs,json=descImgs,proto3" json:"desc_imgs,omitempty"`
	RuleImgs             []string `protobuf:"bytes,3,rep,name=rule_imgs,json=ruleImgs,proto3" json:"rule_imgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSeasonWebInfoResp) Reset()         { *m = GetSeasonWebInfoResp{} }
func (m *GetSeasonWebInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetSeasonWebInfoResp) ProtoMessage()    {}
func (*GetSeasonWebInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{37}
}
func (m *GetSeasonWebInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSeasonWebInfoResp.Unmarshal(m, b)
}
func (m *GetSeasonWebInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSeasonWebInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetSeasonWebInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSeasonWebInfoResp.Merge(dst, src)
}
func (m *GetSeasonWebInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetSeasonWebInfoResp.Size(m)
}
func (m *GetSeasonWebInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSeasonWebInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSeasonWebInfoResp proto.InternalMessageInfo

func (m *GetSeasonWebInfoResp) GetTitleImg() string {
	if m != nil {
		return m.TitleImg
	}
	return ""
}

func (m *GetSeasonWebInfoResp) GetDescImgs() []string {
	if m != nil {
		return m.DescImgs
	}
	return nil
}

func (m *GetSeasonWebInfoResp) GetRuleImgs() []string {
	if m != nil {
		return m.RuleImgs
	}
	return nil
}

type UserMusicRankGloryTriggerReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicRankGloryTriggerReq) Reset()         { *m = UserMusicRankGloryTriggerReq{} }
func (m *UserMusicRankGloryTriggerReq) String() string { return proto.CompactTextString(m) }
func (*UserMusicRankGloryTriggerReq) ProtoMessage()    {}
func (*UserMusicRankGloryTriggerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{38}
}
func (m *UserMusicRankGloryTriggerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicRankGloryTriggerReq.Unmarshal(m, b)
}
func (m *UserMusicRankGloryTriggerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicRankGloryTriggerReq.Marshal(b, m, deterministic)
}
func (dst *UserMusicRankGloryTriggerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicRankGloryTriggerReq.Merge(dst, src)
}
func (m *UserMusicRankGloryTriggerReq) XXX_Size() int {
	return xxx_messageInfo_UserMusicRankGloryTriggerReq.Size(m)
}
func (m *UserMusicRankGloryTriggerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicRankGloryTriggerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicRankGloryTriggerReq proto.InternalMessageInfo

type UserMusicRankGloryTriggerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicRankGloryTriggerResp) Reset()         { *m = UserMusicRankGloryTriggerResp{} }
func (m *UserMusicRankGloryTriggerResp) String() string { return proto.CompactTextString(m) }
func (*UserMusicRankGloryTriggerResp) ProtoMessage()    {}
func (*UserMusicRankGloryTriggerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{39}
}
func (m *UserMusicRankGloryTriggerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicRankGloryTriggerResp.Unmarshal(m, b)
}
func (m *UserMusicRankGloryTriggerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicRankGloryTriggerResp.Marshal(b, m, deterministic)
}
func (dst *UserMusicRankGloryTriggerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicRankGloryTriggerResp.Merge(dst, src)
}
func (m *UserMusicRankGloryTriggerResp) XXX_Size() int {
	return xxx_messageInfo_UserMusicRankGloryTriggerResp.Size(m)
}
func (m *UserMusicRankGloryTriggerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicRankGloryTriggerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicRankGloryTriggerResp proto.InternalMessageInfo

type UserMusicRankTriggerReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicRankTriggerReq) Reset()         { *m = UserMusicRankTriggerReq{} }
func (m *UserMusicRankTriggerReq) String() string { return proto.CompactTextString(m) }
func (*UserMusicRankTriggerReq) ProtoMessage()    {}
func (*UserMusicRankTriggerReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{40}
}
func (m *UserMusicRankTriggerReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicRankTriggerReq.Unmarshal(m, b)
}
func (m *UserMusicRankTriggerReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicRankTriggerReq.Marshal(b, m, deterministic)
}
func (dst *UserMusicRankTriggerReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicRankTriggerReq.Merge(dst, src)
}
func (m *UserMusicRankTriggerReq) XXX_Size() int {
	return xxx_messageInfo_UserMusicRankTriggerReq.Size(m)
}
func (m *UserMusicRankTriggerReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicRankTriggerReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicRankTriggerReq proto.InternalMessageInfo

type UserMusicRankTriggerResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicRankTriggerResp) Reset()         { *m = UserMusicRankTriggerResp{} }
func (m *UserMusicRankTriggerResp) String() string { return proto.CompactTextString(m) }
func (*UserMusicRankTriggerResp) ProtoMessage()    {}
func (*UserMusicRankTriggerResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{41}
}
func (m *UserMusicRankTriggerResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicRankTriggerResp.Unmarshal(m, b)
}
func (m *UserMusicRankTriggerResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicRankTriggerResp.Marshal(b, m, deterministic)
}
func (dst *UserMusicRankTriggerResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicRankTriggerResp.Merge(dst, src)
}
func (m *UserMusicRankTriggerResp) XXX_Size() int {
	return xxx_messageInfo_UserMusicRankTriggerResp.Size(m)
}
func (m *UserMusicRankTriggerResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicRankTriggerResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicRankTriggerResp proto.InternalMessageInfo

type GetMusicRankUserInfoReq struct {
	NeedMic              bool     `protobuf:"varint,1,opt,name=need_mic,json=needMic,proto3" json:"need_mic,omitempty"`
	Uids                 []uint32 `protobuf:"varint,2,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMusicRankUserInfoReq) Reset()         { *m = GetMusicRankUserInfoReq{} }
func (m *GetMusicRankUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMusicRankUserInfoReq) ProtoMessage()    {}
func (*GetMusicRankUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{42}
}
func (m *GetMusicRankUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicRankUserInfoReq.Unmarshal(m, b)
}
func (m *GetMusicRankUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicRankUserInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMusicRankUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicRankUserInfoReq.Merge(dst, src)
}
func (m *GetMusicRankUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMusicRankUserInfoReq.Size(m)
}
func (m *GetMusicRankUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicRankUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicRankUserInfoReq proto.InternalMessageInfo

func (m *GetMusicRankUserInfoReq) GetNeedMic() bool {
	if m != nil {
		return m.NeedMic
	}
	return false
}

func (m *GetMusicRankUserInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type GetMusicRankUserInfoResp struct {
	UserGloryMap         map[uint32]*UserGloryInfo `protobuf:"bytes,1,rep,name=user_glory_map,json=userGloryMap,proto3" json:"user_glory_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetMusicRankUserInfoResp) Reset()         { *m = GetMusicRankUserInfoResp{} }
func (m *GetMusicRankUserInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMusicRankUserInfoResp) ProtoMessage()    {}
func (*GetMusicRankUserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{43}
}
func (m *GetMusicRankUserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMusicRankUserInfoResp.Unmarshal(m, b)
}
func (m *GetMusicRankUserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMusicRankUserInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMusicRankUserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMusicRankUserInfoResp.Merge(dst, src)
}
func (m *GetMusicRankUserInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMusicRankUserInfoResp.Size(m)
}
func (m *GetMusicRankUserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMusicRankUserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMusicRankUserInfoResp proto.InternalMessageInfo

func (m *GetMusicRankUserInfoResp) GetUserGloryMap() map[uint32]*UserGloryInfo {
	if m != nil {
		return m.UserGloryMap
	}
	return nil
}

type UserGloryInfo struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GloryResource        *GloryResource `protobuf:"bytes,2,opt,name=glory_resource,json=gloryResource,proto3" json:"glory_resource,omitempty"`
	DanImg               string         `protobuf:"bytes,3,opt,name=dan_img,json=danImg,proto3" json:"dan_img,omitempty"`
	DanBgImg             string         `protobuf:"bytes,4,opt,name=dan_bg_img,json=danBgImg,proto3" json:"dan_bg_img,omitempty"`
	DanName              string         `protobuf:"bytes,5,opt,name=dan_name,json=danName,proto3" json:"dan_name,omitempty"`
	MicImg               string         `protobuf:"bytes,6,opt,name=mic_img,json=micImg,proto3" json:"mic_img,omitempty"`
	DanMicImg            string         `protobuf:"bytes,7,opt,name=dan_mic_img,json=danMicImg,proto3" json:"dan_mic_img,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserGloryInfo) Reset()         { *m = UserGloryInfo{} }
func (m *UserGloryInfo) String() string { return proto.CompactTextString(m) }
func (*UserGloryInfo) ProtoMessage()    {}
func (*UserGloryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{44}
}
func (m *UserGloryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGloryInfo.Unmarshal(m, b)
}
func (m *UserGloryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGloryInfo.Marshal(b, m, deterministic)
}
func (dst *UserGloryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGloryInfo.Merge(dst, src)
}
func (m *UserGloryInfo) XXX_Size() int {
	return xxx_messageInfo_UserGloryInfo.Size(m)
}
func (m *UserGloryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGloryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserGloryInfo proto.InternalMessageInfo

func (m *UserGloryInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserGloryInfo) GetGloryResource() *GloryResource {
	if m != nil {
		return m.GloryResource
	}
	return nil
}

func (m *UserGloryInfo) GetDanImg() string {
	if m != nil {
		return m.DanImg
	}
	return ""
}

func (m *UserGloryInfo) GetDanBgImg() string {
	if m != nil {
		return m.DanBgImg
	}
	return ""
}

func (m *UserGloryInfo) GetDanName() string {
	if m != nil {
		return m.DanName
	}
	return ""
}

func (m *UserGloryInfo) GetMicImg() string {
	if m != nil {
		return m.MicImg
	}
	return ""
}

func (m *UserGloryInfo) GetDanMicImg() string {
	if m != nil {
		return m.DanMicImg
	}
	return ""
}

type BatchUserPowerInfoReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUserPowerInfoReq) Reset()         { *m = BatchUserPowerInfoReq{} }
func (m *BatchUserPowerInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchUserPowerInfoReq) ProtoMessage()    {}
func (*BatchUserPowerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{45}
}
func (m *BatchUserPowerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUserPowerInfoReq.Unmarshal(m, b)
}
func (m *BatchUserPowerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUserPowerInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchUserPowerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUserPowerInfoReq.Merge(dst, src)
}
func (m *BatchUserPowerInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchUserPowerInfoReq.Size(m)
}
func (m *BatchUserPowerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUserPowerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUserPowerInfoReq proto.InternalMessageInfo

func (m *BatchUserPowerInfoReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchUserPowerInfoResp struct {
	UserPowerMap         map[uint32]*UserPowerInfo `protobuf:"bytes,1,rep,name=user_power_map,json=userPowerMap,proto3" json:"user_power_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatchUserPowerInfoResp) Reset()         { *m = BatchUserPowerInfoResp{} }
func (m *BatchUserPowerInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchUserPowerInfoResp) ProtoMessage()    {}
func (*BatchUserPowerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{46}
}
func (m *BatchUserPowerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUserPowerInfoResp.Unmarshal(m, b)
}
func (m *BatchUserPowerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUserPowerInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchUserPowerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUserPowerInfoResp.Merge(dst, src)
}
func (m *BatchUserPowerInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchUserPowerInfoResp.Size(m)
}
func (m *BatchUserPowerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUserPowerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUserPowerInfoResp proto.InternalMessageInfo

func (m *BatchUserPowerInfoResp) GetUserPowerMap() map[uint32]*UserPowerInfo {
	if m != nil {
		return m.UserPowerMap
	}
	return nil
}

type BatchUserGloryReq struct {
	Uids                 []uint32 `protobuf:"varint,1,rep,packed,name=uids,proto3" json:"uids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchUserGloryReq) Reset()         { *m = BatchUserGloryReq{} }
func (m *BatchUserGloryReq) String() string { return proto.CompactTextString(m) }
func (*BatchUserGloryReq) ProtoMessage()    {}
func (*BatchUserGloryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{47}
}
func (m *BatchUserGloryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUserGloryReq.Unmarshal(m, b)
}
func (m *BatchUserGloryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUserGloryReq.Marshal(b, m, deterministic)
}
func (dst *BatchUserGloryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUserGloryReq.Merge(dst, src)
}
func (m *BatchUserGloryReq) XXX_Size() int {
	return xxx_messageInfo_BatchUserGloryReq.Size(m)
}
func (m *BatchUserGloryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUserGloryReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUserGloryReq proto.InternalMessageInfo

func (m *BatchUserGloryReq) GetUids() []uint32 {
	if m != nil {
		return m.Uids
	}
	return nil
}

type BatchUserGloryResp struct {
	UserGloryMap         map[uint32]*GloryResource `protobuf:"bytes,1,rep,name=user_glory_map,json=userGloryMap,proto3" json:"user_glory_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *BatchUserGloryResp) Reset()         { *m = BatchUserGloryResp{} }
func (m *BatchUserGloryResp) String() string { return proto.CompactTextString(m) }
func (*BatchUserGloryResp) ProtoMessage()    {}
func (*BatchUserGloryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{48}
}
func (m *BatchUserGloryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchUserGloryResp.Unmarshal(m, b)
}
func (m *BatchUserGloryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchUserGloryResp.Marshal(b, m, deterministic)
}
func (dst *BatchUserGloryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchUserGloryResp.Merge(dst, src)
}
func (m *BatchUserGloryResp) XXX_Size() int {
	return xxx_messageInfo_BatchUserGloryResp.Size(m)
}
func (m *BatchUserGloryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchUserGloryResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchUserGloryResp proto.InternalMessageInfo

func (m *BatchUserGloryResp) GetUserGloryMap() map[uint32]*GloryResource {
	if m != nil {
		return m.UserGloryMap
	}
	return nil
}

type ListUserGloriesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserGloriesReq) Reset()         { *m = ListUserGloriesReq{} }
func (m *ListUserGloriesReq) String() string { return proto.CompactTextString(m) }
func (*ListUserGloriesReq) ProtoMessage()    {}
func (*ListUserGloriesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{49}
}
func (m *ListUserGloriesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserGloriesReq.Unmarshal(m, b)
}
func (m *ListUserGloriesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserGloriesReq.Marshal(b, m, deterministic)
}
func (dst *ListUserGloriesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserGloriesReq.Merge(dst, src)
}
func (m *ListUserGloriesReq) XXX_Size() int {
	return xxx_messageInfo_ListUserGloriesReq.Size(m)
}
func (m *ListUserGloriesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserGloriesReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserGloriesReq proto.InternalMessageInfo

func (m *ListUserGloriesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ListUserGloriesResp struct {
	Glories              []*GloryResource `protobuf:"bytes,1,rep,name=glories,proto3" json:"glories,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListUserGloriesResp) Reset()         { *m = ListUserGloriesResp{} }
func (m *ListUserGloriesResp) String() string { return proto.CompactTextString(m) }
func (*ListUserGloriesResp) ProtoMessage()    {}
func (*ListUserGloriesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{50}
}
func (m *ListUserGloriesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserGloriesResp.Unmarshal(m, b)
}
func (m *ListUserGloriesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserGloriesResp.Marshal(b, m, deterministic)
}
func (dst *ListUserGloriesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserGloriesResp.Merge(dst, src)
}
func (m *ListUserGloriesResp) XXX_Size() int {
	return xxx_messageInfo_ListUserGloriesResp.Size(m)
}
func (m *ListUserGloriesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserGloriesResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserGloriesResp proto.InternalMessageInfo

func (m *ListUserGloriesResp) GetGlories() []*GloryResource {
	if m != nil {
		return m.Glories
	}
	return nil
}

type SetUserGloryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GloryId              string   `protobuf:"bytes,2,opt,name=glory_id,json=gloryId,proto3" json:"glory_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserGloryReq) Reset()         { *m = SetUserGloryReq{} }
func (m *SetUserGloryReq) String() string { return proto.CompactTextString(m) }
func (*SetUserGloryReq) ProtoMessage()    {}
func (*SetUserGloryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{51}
}
func (m *SetUserGloryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserGloryReq.Unmarshal(m, b)
}
func (m *SetUserGloryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserGloryReq.Marshal(b, m, deterministic)
}
func (dst *SetUserGloryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserGloryReq.Merge(dst, src)
}
func (m *SetUserGloryReq) XXX_Size() int {
	return xxx_messageInfo_SetUserGloryReq.Size(m)
}
func (m *SetUserGloryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserGloryReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserGloryReq proto.InternalMessageInfo

func (m *SetUserGloryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserGloryReq) GetGloryId() string {
	if m != nil {
		return m.GloryId
	}
	return ""
}

type SetUserGloryResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserGloryResp) Reset()         { *m = SetUserGloryResp{} }
func (m *SetUserGloryResp) String() string { return proto.CompactTextString(m) }
func (*SetUserGloryResp) ProtoMessage()    {}
func (*SetUserGloryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{52}
}
func (m *SetUserGloryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserGloryResp.Unmarshal(m, b)
}
func (m *SetUserGloryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserGloryResp.Marshal(b, m, deterministic)
}
func (dst *SetUserGloryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserGloryResp.Merge(dst, src)
}
func (m *SetUserGloryResp) XXX_Size() int {
	return xxx_messageInfo_SetUserGloryResp.Size(m)
}
func (m *SetUserGloryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserGloryResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserGloryResp proto.InternalMessageInfo

type ListUserHistoryGloriesReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	Offset               string   `protobuf:"bytes,3,opt,name=offset,proto3" json:"offset,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserHistoryGloriesReq) Reset()         { *m = ListUserHistoryGloriesReq{} }
func (m *ListUserHistoryGloriesReq) String() string { return proto.CompactTextString(m) }
func (*ListUserHistoryGloriesReq) ProtoMessage()    {}
func (*ListUserHistoryGloriesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{53}
}
func (m *ListUserHistoryGloriesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserHistoryGloriesReq.Unmarshal(m, b)
}
func (m *ListUserHistoryGloriesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserHistoryGloriesReq.Marshal(b, m, deterministic)
}
func (dst *ListUserHistoryGloriesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserHistoryGloriesReq.Merge(dst, src)
}
func (m *ListUserHistoryGloriesReq) XXX_Size() int {
	return xxx_messageInfo_ListUserHistoryGloriesReq.Size(m)
}
func (m *ListUserHistoryGloriesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserHistoryGloriesReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserHistoryGloriesReq proto.InternalMessageInfo

func (m *ListUserHistoryGloriesReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListUserHistoryGloriesReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListUserHistoryGloriesReq) GetOffset() string {
	if m != nil {
		return m.Offset
	}
	return ""
}

type ListUserHistoryGloriesResp struct {
	IsEnd                bool             `protobuf:"varint,1,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	Glories              []*GloryResource `protobuf:"bytes,2,rep,name=glories,proto3" json:"glories,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *ListUserHistoryGloriesResp) Reset()         { *m = ListUserHistoryGloriesResp{} }
func (m *ListUserHistoryGloriesResp) String() string { return proto.CompactTextString(m) }
func (*ListUserHistoryGloriesResp) ProtoMessage()    {}
func (*ListUserHistoryGloriesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{54}
}
func (m *ListUserHistoryGloriesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserHistoryGloriesResp.Unmarshal(m, b)
}
func (m *ListUserHistoryGloriesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserHistoryGloriesResp.Marshal(b, m, deterministic)
}
func (dst *ListUserHistoryGloriesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserHistoryGloriesResp.Merge(dst, src)
}
func (m *ListUserHistoryGloriesResp) XXX_Size() int {
	return xxx_messageInfo_ListUserHistoryGloriesResp.Size(m)
}
func (m *ListUserHistoryGloriesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserHistoryGloriesResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserHistoryGloriesResp proto.InternalMessageInfo

func (m *ListUserHistoryGloriesResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

func (m *ListUserHistoryGloriesResp) GetGlories() []*GloryResource {
	if m != nil {
		return m.Glories
	}
	return nil
}

type SetUserLocationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ip                   string   `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserLocationReq) Reset()         { *m = SetUserLocationReq{} }
func (m *SetUserLocationReq) String() string { return proto.CompactTextString(m) }
func (*SetUserLocationReq) ProtoMessage()    {}
func (*SetUserLocationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{55}
}
func (m *SetUserLocationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserLocationReq.Unmarshal(m, b)
}
func (m *SetUserLocationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserLocationReq.Marshal(b, m, deterministic)
}
func (dst *SetUserLocationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserLocationReq.Merge(dst, src)
}
func (m *SetUserLocationReq) XXX_Size() int {
	return xxx_messageInfo_SetUserLocationReq.Size(m)
}
func (m *SetUserLocationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserLocationReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserLocationReq proto.InternalMessageInfo

func (m *SetUserLocationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserLocationReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type SetUserLocationResp struct {
	Location             string   `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserLocationResp) Reset()         { *m = SetUserLocationResp{} }
func (m *SetUserLocationResp) String() string { return proto.CompactTextString(m) }
func (*SetUserLocationResp) ProtoMessage()    {}
func (*SetUserLocationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{56}
}
func (m *SetUserLocationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserLocationResp.Unmarshal(m, b)
}
func (m *SetUserLocationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserLocationResp.Marshal(b, m, deterministic)
}
func (dst *SetUserLocationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserLocationResp.Merge(dst, src)
}
func (m *SetUserLocationResp) XXX_Size() int {
	return xxx_messageInfo_SetUserLocationResp.Size(m)
}
func (m *SetUserLocationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserLocationResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserLocationResp proto.InternalMessageInfo

func (m *SetUserLocationResp) GetLocation() string {
	if m != nil {
		return m.Location
	}
	return ""
}

type GetUserLocationReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Ip                   string   `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	IsCache              bool     `protobuf:"varint,3,opt,name=is_cache,json=isCache,proto3" json:"is_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLocationReq) Reset()         { *m = GetUserLocationReq{} }
func (m *GetUserLocationReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLocationReq) ProtoMessage()    {}
func (*GetUserLocationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{57}
}
func (m *GetUserLocationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLocationReq.Unmarshal(m, b)
}
func (m *GetUserLocationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLocationReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLocationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLocationReq.Merge(dst, src)
}
func (m *GetUserLocationReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLocationReq.Size(m)
}
func (m *GetUserLocationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLocationReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLocationReq proto.InternalMessageInfo

func (m *GetUserLocationReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserLocationReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *GetUserLocationReq) GetIsCache() bool {
	if m != nil {
		return m.IsCache
	}
	return false
}

type GetUserLocationResp struct {
	Location             string   `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	IsSensitive          bool     `protobuf:"varint,2,opt,name=is_sensitive,json=isSensitive,proto3" json:"is_sensitive,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLocationResp) Reset()         { *m = GetUserLocationResp{} }
func (m *GetUserLocationResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLocationResp) ProtoMessage()    {}
func (*GetUserLocationResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{58}
}
func (m *GetUserLocationResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLocationResp.Unmarshal(m, b)
}
func (m *GetUserLocationResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLocationResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLocationResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLocationResp.Merge(dst, src)
}
func (m *GetUserLocationResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLocationResp.Size(m)
}
func (m *GetUserLocationResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLocationResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLocationResp proto.InternalMessageInfo

func (m *GetUserLocationResp) GetLocation() string {
	if m != nil {
		return m.Location
	}
	return ""
}

func (m *GetUserLocationResp) GetIsSensitive() bool {
	if m != nil {
		return m.IsSensitive
	}
	return false
}

// 获取用户权限
type GetUserLocationAuthReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLocationAuthReq) Reset()         { *m = GetUserLocationAuthReq{} }
func (m *GetUserLocationAuthReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLocationAuthReq) ProtoMessage()    {}
func (*GetUserLocationAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{59}
}
func (m *GetUserLocationAuthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLocationAuthReq.Unmarshal(m, b)
}
func (m *GetUserLocationAuthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLocationAuthReq.Marshal(b, m, deterministic)
}
func (dst *GetUserLocationAuthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLocationAuthReq.Merge(dst, src)
}
func (m *GetUserLocationAuthReq) XXX_Size() int {
	return xxx_messageInfo_GetUserLocationAuthReq.Size(m)
}
func (m *GetUserLocationAuthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLocationAuthReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLocationAuthReq proto.InternalMessageInfo

func (m *GetUserLocationAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserLocationAuthResp struct {
	IsAllowed            bool     `protobuf:"varint,1,opt,name=is_allowed,json=isAllowed,proto3" json:"is_allowed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserLocationAuthResp) Reset()         { *m = GetUserLocationAuthResp{} }
func (m *GetUserLocationAuthResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLocationAuthResp) ProtoMessage()    {}
func (*GetUserLocationAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{60}
}
func (m *GetUserLocationAuthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserLocationAuthResp.Unmarshal(m, b)
}
func (m *GetUserLocationAuthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserLocationAuthResp.Marshal(b, m, deterministic)
}
func (dst *GetUserLocationAuthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserLocationAuthResp.Merge(dst, src)
}
func (m *GetUserLocationAuthResp) XXX_Size() int {
	return xxx_messageInfo_GetUserLocationAuthResp.Size(m)
}
func (m *GetUserLocationAuthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserLocationAuthResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserLocationAuthResp proto.InternalMessageInfo

func (m *GetUserLocationAuthResp) GetIsAllowed() bool {
	if m != nil {
		return m.IsAllowed
	}
	return false
}

// 设置用户权限
type SetUserLocationAuthReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	IsAllowed            bool     `protobuf:"varint,2,opt,name=is_allowed,json=isAllowed,proto3" json:"is_allowed,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserLocationAuthReq) Reset()         { *m = SetUserLocationAuthReq{} }
func (m *SetUserLocationAuthReq) String() string { return proto.CompactTextString(m) }
func (*SetUserLocationAuthReq) ProtoMessage()    {}
func (*SetUserLocationAuthReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{61}
}
func (m *SetUserLocationAuthReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserLocationAuthReq.Unmarshal(m, b)
}
func (m *SetUserLocationAuthReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserLocationAuthReq.Marshal(b, m, deterministic)
}
func (dst *SetUserLocationAuthReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserLocationAuthReq.Merge(dst, src)
}
func (m *SetUserLocationAuthReq) XXX_Size() int {
	return xxx_messageInfo_SetUserLocationAuthReq.Size(m)
}
func (m *SetUserLocationAuthReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserLocationAuthReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserLocationAuthReq proto.InternalMessageInfo

func (m *SetUserLocationAuthReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetUserLocationAuthReq) GetIsAllowed() bool {
	if m != nil {
		return m.IsAllowed
	}
	return false
}

type SetUserLocationAuthResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserLocationAuthResp) Reset()         { *m = SetUserLocationAuthResp{} }
func (m *SetUserLocationAuthResp) String() string { return proto.CompactTextString(m) }
func (*SetUserLocationAuthResp) ProtoMessage()    {}
func (*SetUserLocationAuthResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{62}
}
func (m *SetUserLocationAuthResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserLocationAuthResp.Unmarshal(m, b)
}
func (m *SetUserLocationAuthResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserLocationAuthResp.Marshal(b, m, deterministic)
}
func (dst *SetUserLocationAuthResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserLocationAuthResp.Merge(dst, src)
}
func (m *SetUserLocationAuthResp) XXX_Size() int {
	return xxx_messageInfo_SetUserLocationAuthResp.Size(m)
}
func (m *SetUserLocationAuthResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserLocationAuthResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserLocationAuthResp proto.InternalMessageInfo

type ListUserSingerScoreReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserSingerScoreReq) Reset()         { *m = ListUserSingerScoreReq{} }
func (m *ListUserSingerScoreReq) String() string { return proto.CompactTextString(m) }
func (*ListUserSingerScoreReq) ProtoMessage()    {}
func (*ListUserSingerScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{63}
}
func (m *ListUserSingerScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserSingerScoreReq.Unmarshal(m, b)
}
func (m *ListUserSingerScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserSingerScoreReq.Marshal(b, m, deterministic)
}
func (dst *ListUserSingerScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserSingerScoreReq.Merge(dst, src)
}
func (m *ListUserSingerScoreReq) XXX_Size() int {
	return xxx_messageInfo_ListUserSingerScoreReq.Size(m)
}
func (m *ListUserSingerScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserSingerScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserSingerScoreReq proto.InternalMessageInfo

func (m *ListUserSingerScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type ListUserSingerScoreResp struct {
	Infos                []*ListUserSingerScoreResp_SingerScoreInfo `protobuf:"bytes,1,rep,name=infos,proto3" json:"infos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-"`
	XXX_unrecognized     []byte                                     `json:"-"`
	XXX_sizecache        int32                                      `json:"-"`
}

func (m *ListUserSingerScoreResp) Reset()         { *m = ListUserSingerScoreResp{} }
func (m *ListUserSingerScoreResp) String() string { return proto.CompactTextString(m) }
func (*ListUserSingerScoreResp) ProtoMessage()    {}
func (*ListUserSingerScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{64}
}
func (m *ListUserSingerScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserSingerScoreResp.Unmarshal(m, b)
}
func (m *ListUserSingerScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserSingerScoreResp.Marshal(b, m, deterministic)
}
func (dst *ListUserSingerScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserSingerScoreResp.Merge(dst, src)
}
func (m *ListUserSingerScoreResp) XXX_Size() int {
	return xxx_messageInfo_ListUserSingerScoreResp.Size(m)
}
func (m *ListUserSingerScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserSingerScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserSingerScoreResp proto.InternalMessageInfo

func (m *ListUserSingerScoreResp) GetInfos() []*ListUserSingerScoreResp_SingerScoreInfo {
	if m != nil {
		return m.Infos
	}
	return nil
}

type ListUserSingerScoreResp_SingerScoreInfo struct {
	SingerId             string         `protobuf:"bytes,1,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	SingerName           string         `protobuf:"bytes,2,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	SingerAvatar         string         `protobuf:"bytes,3,opt,name=singer_avatar,json=singerAvatar,proto3" json:"singer_avatar,omitempty"`
	Glory                *GloryResource `protobuf:"bytes,4,opt,name=glory,proto3" json:"glory,omitempty"`
	Score                uint32         `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	Desc                 string         `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *ListUserSingerScoreResp_SingerScoreInfo) Reset() {
	*m = ListUserSingerScoreResp_SingerScoreInfo{}
}
func (m *ListUserSingerScoreResp_SingerScoreInfo) String() string { return proto.CompactTextString(m) }
func (*ListUserSingerScoreResp_SingerScoreInfo) ProtoMessage()    {}
func (*ListUserSingerScoreResp_SingerScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{64, 0}
}
func (m *ListUserSingerScoreResp_SingerScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserSingerScoreResp_SingerScoreInfo.Unmarshal(m, b)
}
func (m *ListUserSingerScoreResp_SingerScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserSingerScoreResp_SingerScoreInfo.Marshal(b, m, deterministic)
}
func (dst *ListUserSingerScoreResp_SingerScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserSingerScoreResp_SingerScoreInfo.Merge(dst, src)
}
func (m *ListUserSingerScoreResp_SingerScoreInfo) XXX_Size() int {
	return xxx_messageInfo_ListUserSingerScoreResp_SingerScoreInfo.Size(m)
}
func (m *ListUserSingerScoreResp_SingerScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserSingerScoreResp_SingerScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserSingerScoreResp_SingerScoreInfo proto.InternalMessageInfo

func (m *ListUserSingerScoreResp_SingerScoreInfo) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *ListUserSingerScoreResp_SingerScoreInfo) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *ListUserSingerScoreResp_SingerScoreInfo) GetSingerAvatar() string {
	if m != nil {
		return m.SingerAvatar
	}
	return ""
}

func (m *ListUserSingerScoreResp_SingerScoreInfo) GetGlory() *GloryResource {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *ListUserSingerScoreResp_SingerScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *ListUserSingerScoreResp_SingerScoreInfo) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

type GetUserSingerScoreDetailReq struct {
	Uid                  uint32                              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SingerId             string                              `protobuf:"bytes,2,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	ReqType              GetUserSingerScoreDetailReq_ReqType `protobuf:"varint,3,opt,name=reqType,proto3,enum=user_music_rank.GetUserSingerScoreDetailReq_ReqType" json:"reqType,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-"`
	XXX_unrecognized     []byte                              `json:"-"`
	XXX_sizecache        int32                               `json:"-"`
}

func (m *GetUserSingerScoreDetailReq) Reset()         { *m = GetUserSingerScoreDetailReq{} }
func (m *GetUserSingerScoreDetailReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSingerScoreDetailReq) ProtoMessage()    {}
func (*GetUserSingerScoreDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{65}
}
func (m *GetUserSingerScoreDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingerScoreDetailReq.Unmarshal(m, b)
}
func (m *GetUserSingerScoreDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingerScoreDetailReq.Marshal(b, m, deterministic)
}
func (dst *GetUserSingerScoreDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingerScoreDetailReq.Merge(dst, src)
}
func (m *GetUserSingerScoreDetailReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSingerScoreDetailReq.Size(m)
}
func (m *GetUserSingerScoreDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingerScoreDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingerScoreDetailReq proto.InternalMessageInfo

func (m *GetUserSingerScoreDetailReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserSingerScoreDetailReq) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *GetUserSingerScoreDetailReq) GetReqType() GetUserSingerScoreDetailReq_ReqType {
	if m != nil {
		return m.ReqType
	}
	return GetUserSingerScoreDetailReq_Full
}

type GetUserSingerScoreDetailResp struct {
	SingerScoreInfo      *GetUserSingerScoreDetailResp_SingerScoreInfo `protobuf:"bytes,1,opt,name=singer_score_info,json=singerScoreInfo,proto3" json:"singer_score_info,omitempty"`
	Season               *Season                                       `protobuf:"bytes,2,opt,name=season,proto3" json:"season,omitempty"`
	SongScoreInfo        []*GetUserSingerScoreDetailResp_SongScoreInfo `protobuf:"bytes,3,rep,name=song_score_info,json=songScoreInfo,proto3" json:"song_score_info,omitempty"`
	Loc                  *Location                                     `protobuf:"bytes,4,opt,name=loc,proto3" json:"loc,omitempty"`
	RankMinScore         uint32                                        `protobuf:"varint,5,opt,name=rankMinScore,proto3" json:"rankMinScore,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *GetUserSingerScoreDetailResp) Reset()         { *m = GetUserSingerScoreDetailResp{} }
func (m *GetUserSingerScoreDetailResp) String() string { return proto.CompactTextString(m) }
func (*GetUserSingerScoreDetailResp) ProtoMessage()    {}
func (*GetUserSingerScoreDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{66}
}
func (m *GetUserSingerScoreDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingerScoreDetailResp.Unmarshal(m, b)
}
func (m *GetUserSingerScoreDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingerScoreDetailResp.Marshal(b, m, deterministic)
}
func (dst *GetUserSingerScoreDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingerScoreDetailResp.Merge(dst, src)
}
func (m *GetUserSingerScoreDetailResp) XXX_Size() int {
	return xxx_messageInfo_GetUserSingerScoreDetailResp.Size(m)
}
func (m *GetUserSingerScoreDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingerScoreDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingerScoreDetailResp proto.InternalMessageInfo

func (m *GetUserSingerScoreDetailResp) GetSingerScoreInfo() *GetUserSingerScoreDetailResp_SingerScoreInfo {
	if m != nil {
		return m.SingerScoreInfo
	}
	return nil
}

func (m *GetUserSingerScoreDetailResp) GetSeason() *Season {
	if m != nil {
		return m.Season
	}
	return nil
}

func (m *GetUserSingerScoreDetailResp) GetSongScoreInfo() []*GetUserSingerScoreDetailResp_SongScoreInfo {
	if m != nil {
		return m.SongScoreInfo
	}
	return nil
}

func (m *GetUserSingerScoreDetailResp) GetLoc() *Location {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *GetUserSingerScoreDetailResp) GetRankMinScore() uint32 {
	if m != nil {
		return m.RankMinScore
	}
	return 0
}

type GetUserSingerScoreDetailResp_SingerScoreInfo struct {
	SingerId             string         `protobuf:"bytes,1,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	SingerName           string         `protobuf:"bytes,2,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	SingerAvatar         string         `protobuf:"bytes,3,opt,name=singer_avatar,json=singerAvatar,proto3" json:"singer_avatar,omitempty"`
	Glory                *GloryResource `protobuf:"bytes,4,opt,name=glory,proto3" json:"glory,omitempty"`
	Score                uint32         `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	MaxScore             uint32         `protobuf:"varint,6,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	SangNum              uint32         `protobuf:"varint,7,opt,name=sang_num,json=sangNum,proto3" json:"sang_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) Reset() {
	*m = GetUserSingerScoreDetailResp_SingerScoreInfo{}
}
func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserSingerScoreDetailResp_SingerScoreInfo) ProtoMessage() {}
func (*GetUserSingerScoreDetailResp_SingerScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{66, 0}
}
func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingerScoreDetailResp_SingerScoreInfo.Unmarshal(m, b)
}
func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingerScoreDetailResp_SingerScoreInfo.Marshal(b, m, deterministic)
}
func (dst *GetUserSingerScoreDetailResp_SingerScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingerScoreDetailResp_SingerScoreInfo.Merge(dst, src)
}
func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) XXX_Size() int {
	return xxx_messageInfo_GetUserSingerScoreDetailResp_SingerScoreInfo.Size(m)
}
func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingerScoreDetailResp_SingerScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingerScoreDetailResp_SingerScoreInfo proto.InternalMessageInfo

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetSingerAvatar() string {
	if m != nil {
		return m.SingerAvatar
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetGlory() *GloryResource {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

func (m *GetUserSingerScoreDetailResp_SingerScoreInfo) GetSangNum() uint32 {
	if m != nil {
		return m.SangNum
	}
	return 0
}

type GetUserSingerScoreDetailResp_SongScoreInfo struct {
	SongId               string   `protobuf:"bytes,1,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Image                string   `protobuf:"bytes,2,opt,name=image,proto3" json:"image,omitempty"`
	Name                 string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	TotalScore           uint32   `protobuf:"varint,5,opt,name=total_score,json=totalScore,proto3" json:"total_score,omitempty"`
	SingerNamesDesc      string   `protobuf:"bytes,6,opt,name=singer_names_desc,json=singerNamesDesc,proto3" json:"singer_names_desc,omitempty"`
	OriginScore          uint32   `protobuf:"varint,7,opt,name=origin_score,json=originScore,proto3" json:"origin_score,omitempty"`
	BurstLightNum        uint32   `protobuf:"varint,8,opt,name=burst_light_num,json=burstLightNum,proto3" json:"burst_light_num,omitempty"`
	BurstLightCopy       string   `protobuf:"bytes,9,opt,name=burst_light_copy,json=burstLightCopy,proto3" json:"burst_light_copy,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) Reset() {
	*m = GetUserSingerScoreDetailResp_SongScoreInfo{}
}
func (m *GetUserSingerScoreDetailResp_SongScoreInfo) String() string {
	return proto.CompactTextString(m)
}
func (*GetUserSingerScoreDetailResp_SongScoreInfo) ProtoMessage() {}
func (*GetUserSingerScoreDetailResp_SongScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{66, 1}
}
func (m *GetUserSingerScoreDetailResp_SongScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSingerScoreDetailResp_SongScoreInfo.Unmarshal(m, b)
}
func (m *GetUserSingerScoreDetailResp_SongScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSingerScoreDetailResp_SongScoreInfo.Marshal(b, m, deterministic)
}
func (dst *GetUserSingerScoreDetailResp_SongScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSingerScoreDetailResp_SongScoreInfo.Merge(dst, src)
}
func (m *GetUserSingerScoreDetailResp_SongScoreInfo) XXX_Size() int {
	return xxx_messageInfo_GetUserSingerScoreDetailResp_SongScoreInfo.Size(m)
}
func (m *GetUserSingerScoreDetailResp_SongScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSingerScoreDetailResp_SongScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSingerScoreDetailResp_SongScoreInfo proto.InternalMessageInfo

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetTotalScore() uint32 {
	if m != nil {
		return m.TotalScore
	}
	return 0
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetSingerNamesDesc() string {
	if m != nil {
		return m.SingerNamesDesc
	}
	return ""
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetOriginScore() uint32 {
	if m != nil {
		return m.OriginScore
	}
	return 0
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetBurstLightNum() uint32 {
	if m != nil {
		return m.BurstLightNum
	}
	return 0
}

func (m *GetUserSingerScoreDetailResp_SongScoreInfo) GetBurstLightCopy() string {
	if m != nil {
		return m.BurstLightCopy
	}
	return ""
}

type GetStarRankReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetStarRankReq) Reset()         { *m = GetStarRankReq{} }
func (m *GetStarRankReq) String() string { return proto.CompactTextString(m) }
func (*GetStarRankReq) ProtoMessage()    {}
func (*GetStarRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{67}
}
func (m *GetStarRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarRankReq.Unmarshal(m, b)
}
func (m *GetStarRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarRankReq.Marshal(b, m, deterministic)
}
func (dst *GetStarRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarRankReq.Merge(dst, src)
}
func (m *GetStarRankReq) XXX_Size() int {
	return xxx_messageInfo_GetStarRankReq.Size(m)
}
func (m *GetStarRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarRankReq proto.InternalMessageInfo

func (m *GetStarRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetStarRankResp struct {
	UserStarInfo         []*GetStarRankResp_UserStarInfo `protobuf:"bytes,1,rep,name=user_star_info,json=userStarInfo,proto3" json:"user_star_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-"`
	XXX_unrecognized     []byte                          `json:"-"`
	XXX_sizecache        int32                           `json:"-"`
}

func (m *GetStarRankResp) Reset()         { *m = GetStarRankResp{} }
func (m *GetStarRankResp) String() string { return proto.CompactTextString(m) }
func (*GetStarRankResp) ProtoMessage()    {}
func (*GetStarRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{68}
}
func (m *GetStarRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarRankResp.Unmarshal(m, b)
}
func (m *GetStarRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarRankResp.Marshal(b, m, deterministic)
}
func (dst *GetStarRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarRankResp.Merge(dst, src)
}
func (m *GetStarRankResp) XXX_Size() int {
	return xxx_messageInfo_GetStarRankResp.Size(m)
}
func (m *GetStarRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarRankResp proto.InternalMessageInfo

func (m *GetStarRankResp) GetUserStarInfo() []*GetStarRankResp_UserStarInfo {
	if m != nil {
		return m.UserStarInfo
	}
	return nil
}

type GetStarRankResp_UserStarInfo struct {
	Uid                  uint32     `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name                 string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string     `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	StarLevel            *StarLevel `protobuf:"bytes,4,opt,name=star_level,json=starLevel,proto3" json:"star_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetStarRankResp_UserStarInfo) Reset()         { *m = GetStarRankResp_UserStarInfo{} }
func (m *GetStarRankResp_UserStarInfo) String() string { return proto.CompactTextString(m) }
func (*GetStarRankResp_UserStarInfo) ProtoMessage()    {}
func (*GetStarRankResp_UserStarInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{68, 0}
}
func (m *GetStarRankResp_UserStarInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetStarRankResp_UserStarInfo.Unmarshal(m, b)
}
func (m *GetStarRankResp_UserStarInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetStarRankResp_UserStarInfo.Marshal(b, m, deterministic)
}
func (dst *GetStarRankResp_UserStarInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetStarRankResp_UserStarInfo.Merge(dst, src)
}
func (m *GetStarRankResp_UserStarInfo) XXX_Size() int {
	return xxx_messageInfo_GetStarRankResp_UserStarInfo.Size(m)
}
func (m *GetStarRankResp_UserStarInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetStarRankResp_UserStarInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetStarRankResp_UserStarInfo proto.InternalMessageInfo

func (m *GetStarRankResp_UserStarInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetStarRankResp_UserStarInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetStarRankResp_UserStarInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GetStarRankResp_UserStarInfo) GetStarLevel() *StarLevel {
	if m != nil {
		return m.StarLevel
	}
	return nil
}

type GetSingerScoreRankReq struct {
	SingerId             string     `protobuf:"bytes,1,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	Level                GloryLevel `protobuf:"varint,2,opt,name=level,proto3,enum=user_music_rank.GloryLevel" json:"level,omitempty"`
	Uid                  uint32     `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetSingerScoreRankReq) Reset()         { *m = GetSingerScoreRankReq{} }
func (m *GetSingerScoreRankReq) String() string { return proto.CompactTextString(m) }
func (*GetSingerScoreRankReq) ProtoMessage()    {}
func (*GetSingerScoreRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{69}
}
func (m *GetSingerScoreRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingerScoreRankReq.Unmarshal(m, b)
}
func (m *GetSingerScoreRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingerScoreRankReq.Marshal(b, m, deterministic)
}
func (dst *GetSingerScoreRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingerScoreRankReq.Merge(dst, src)
}
func (m *GetSingerScoreRankReq) XXX_Size() int {
	return xxx_messageInfo_GetSingerScoreRankReq.Size(m)
}
func (m *GetSingerScoreRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingerScoreRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingerScoreRankReq proto.InternalMessageInfo

func (m *GetSingerScoreRankReq) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *GetSingerScoreRankReq) GetLevel() GloryLevel {
	if m != nil {
		return m.Level
	}
	return GloryLevel_City
}

func (m *GetSingerScoreRankReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSingerScoreRankResp struct {
	UserScoreInfo        []*GetSingerScoreRankResp_UserScoreInfo `protobuf:"bytes,1,rep,name=user_score_info,json=userScoreInfo,proto3" json:"user_score_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-"`
	XXX_unrecognized     []byte                                  `json:"-"`
	XXX_sizecache        int32                                   `json:"-"`
}

func (m *GetSingerScoreRankResp) Reset()         { *m = GetSingerScoreRankResp{} }
func (m *GetSingerScoreRankResp) String() string { return proto.CompactTextString(m) }
func (*GetSingerScoreRankResp) ProtoMessage()    {}
func (*GetSingerScoreRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{70}
}
func (m *GetSingerScoreRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingerScoreRankResp.Unmarshal(m, b)
}
func (m *GetSingerScoreRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingerScoreRankResp.Marshal(b, m, deterministic)
}
func (dst *GetSingerScoreRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingerScoreRankResp.Merge(dst, src)
}
func (m *GetSingerScoreRankResp) XXX_Size() int {
	return xxx_messageInfo_GetSingerScoreRankResp.Size(m)
}
func (m *GetSingerScoreRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingerScoreRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingerScoreRankResp proto.InternalMessageInfo

func (m *GetSingerScoreRankResp) GetUserScoreInfo() []*GetSingerScoreRankResp_UserScoreInfo {
	if m != nil {
		return m.UserScoreInfo
	}
	return nil
}

type GetSingerScoreRankResp_UserScoreInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Avatar               string   `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetSingerScoreRankResp_UserScoreInfo) Reset()         { *m = GetSingerScoreRankResp_UserScoreInfo{} }
func (m *GetSingerScoreRankResp_UserScoreInfo) String() string { return proto.CompactTextString(m) }
func (*GetSingerScoreRankResp_UserScoreInfo) ProtoMessage()    {}
func (*GetSingerScoreRankResp_UserScoreInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{70, 0}
}
func (m *GetSingerScoreRankResp_UserScoreInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetSingerScoreRankResp_UserScoreInfo.Unmarshal(m, b)
}
func (m *GetSingerScoreRankResp_UserScoreInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetSingerScoreRankResp_UserScoreInfo.Marshal(b, m, deterministic)
}
func (dst *GetSingerScoreRankResp_UserScoreInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetSingerScoreRankResp_UserScoreInfo.Merge(dst, src)
}
func (m *GetSingerScoreRankResp_UserScoreInfo) XXX_Size() int {
	return xxx_messageInfo_GetSingerScoreRankResp_UserScoreInfo.Size(m)
}
func (m *GetSingerScoreRankResp_UserScoreInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GetSingerScoreRankResp_UserScoreInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GetSingerScoreRankResp_UserScoreInfo proto.InternalMessageInfo

func (m *GetSingerScoreRankResp_UserScoreInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetSingerScoreRankResp_UserScoreInfo) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetSingerScoreRankResp_UserScoreInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *GetSingerScoreRankResp_UserScoreInfo) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type ListUserMusicRecordsReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Limit                uint32   `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	OffsetId             string   `protobuf:"bytes,3,opt,name=offset_id,json=offsetId,proto3" json:"offset_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ListUserMusicRecordsReq) Reset()         { *m = ListUserMusicRecordsReq{} }
func (m *ListUserMusicRecordsReq) String() string { return proto.CompactTextString(m) }
func (*ListUserMusicRecordsReq) ProtoMessage()    {}
func (*ListUserMusicRecordsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{71}
}
func (m *ListUserMusicRecordsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserMusicRecordsReq.Unmarshal(m, b)
}
func (m *ListUserMusicRecordsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserMusicRecordsReq.Marshal(b, m, deterministic)
}
func (dst *ListUserMusicRecordsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserMusicRecordsReq.Merge(dst, src)
}
func (m *ListUserMusicRecordsReq) XXX_Size() int {
	return xxx_messageInfo_ListUserMusicRecordsReq.Size(m)
}
func (m *ListUserMusicRecordsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserMusicRecordsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserMusicRecordsReq proto.InternalMessageInfo

func (m *ListUserMusicRecordsReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ListUserMusicRecordsReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *ListUserMusicRecordsReq) GetOffsetId() string {
	if m != nil {
		return m.OffsetId
	}
	return ""
}

type ListUserMusicRecordsResp struct {
	IsEnd                bool                               `protobuf:"varint,1,opt,name=is_end,json=isEnd,proto3" json:"is_end,omitempty"`
	Records              []*ListUserMusicRecordsResp_Record `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *ListUserMusicRecordsResp) Reset()         { *m = ListUserMusicRecordsResp{} }
func (m *ListUserMusicRecordsResp) String() string { return proto.CompactTextString(m) }
func (*ListUserMusicRecordsResp) ProtoMessage()    {}
func (*ListUserMusicRecordsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{72}
}
func (m *ListUserMusicRecordsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserMusicRecordsResp.Unmarshal(m, b)
}
func (m *ListUserMusicRecordsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserMusicRecordsResp.Marshal(b, m, deterministic)
}
func (dst *ListUserMusicRecordsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserMusicRecordsResp.Merge(dst, src)
}
func (m *ListUserMusicRecordsResp) XXX_Size() int {
	return xxx_messageInfo_ListUserMusicRecordsResp.Size(m)
}
func (m *ListUserMusicRecordsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserMusicRecordsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserMusicRecordsResp proto.InternalMessageInfo

func (m *ListUserMusicRecordsResp) GetIsEnd() bool {
	if m != nil {
		return m.IsEnd
	}
	return false
}

func (m *ListUserMusicRecordsResp) GetRecords() []*ListUserMusicRecordsResp_Record {
	if m != nil {
		return m.Records
	}
	return nil
}

type ListUserMusicRecordsResp_Record struct {
	CreateAt             uint64                            `protobuf:"varint,1,opt,name=create_at,json=createAt,proto3" json:"create_at,omitempty"`
	Desc                 string                            `protobuf:"bytes,2,opt,name=desc,proto3" json:"desc,omitempty"`
	StarLevel            int32                             `protobuf:"varint,3,opt,name=star_level,json=starLevel,proto3" json:"star_level,omitempty"`
	SingType             ListUserMusicRecordsResp_SingType `protobuf:"varint,4,opt,name=sing_type,json=singType,proto3,enum=user_music_rank.ListUserMusicRecordsResp_SingType" json:"sing_type,omitempty"`
	Score                uint32                            `protobuf:"varint,5,opt,name=score,proto3" json:"score,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-"`
	XXX_unrecognized     []byte                            `json:"-"`
	XXX_sizecache        int32                             `json:"-"`
}

func (m *ListUserMusicRecordsResp_Record) Reset()         { *m = ListUserMusicRecordsResp_Record{} }
func (m *ListUserMusicRecordsResp_Record) String() string { return proto.CompactTextString(m) }
func (*ListUserMusicRecordsResp_Record) ProtoMessage()    {}
func (*ListUserMusicRecordsResp_Record) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{72, 0}
}
func (m *ListUserMusicRecordsResp_Record) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ListUserMusicRecordsResp_Record.Unmarshal(m, b)
}
func (m *ListUserMusicRecordsResp_Record) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ListUserMusicRecordsResp_Record.Marshal(b, m, deterministic)
}
func (dst *ListUserMusicRecordsResp_Record) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ListUserMusicRecordsResp_Record.Merge(dst, src)
}
func (m *ListUserMusicRecordsResp_Record) XXX_Size() int {
	return xxx_messageInfo_ListUserMusicRecordsResp_Record.Size(m)
}
func (m *ListUserMusicRecordsResp_Record) XXX_DiscardUnknown() {
	xxx_messageInfo_ListUserMusicRecordsResp_Record.DiscardUnknown(m)
}

var xxx_messageInfo_ListUserMusicRecordsResp_Record proto.InternalMessageInfo

func (m *ListUserMusicRecordsResp_Record) GetCreateAt() uint64 {
	if m != nil {
		return m.CreateAt
	}
	return 0
}

func (m *ListUserMusicRecordsResp_Record) GetDesc() string {
	if m != nil {
		return m.Desc
	}
	return ""
}

func (m *ListUserMusicRecordsResp_Record) GetStarLevel() int32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

func (m *ListUserMusicRecordsResp_Record) GetSingType() ListUserMusicRecordsResp_SingType {
	if m != nil {
		return m.SingType
	}
	return ListUserMusicRecordsResp_Single
}

func (m *ListUserMusicRecordsResp_Record) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

type IsSongsInRankReq struct {
	SongIds              []string `protobuf:"bytes,1,rep,name=song_ids,json=songIds,proto3" json:"song_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IsSongsInRankReq) Reset()         { *m = IsSongsInRankReq{} }
func (m *IsSongsInRankReq) String() string { return proto.CompactTextString(m) }
func (*IsSongsInRankReq) ProtoMessage()    {}
func (*IsSongsInRankReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{73}
}
func (m *IsSongsInRankReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsSongsInRankReq.Unmarshal(m, b)
}
func (m *IsSongsInRankReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsSongsInRankReq.Marshal(b, m, deterministic)
}
func (dst *IsSongsInRankReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsSongsInRankReq.Merge(dst, src)
}
func (m *IsSongsInRankReq) XXX_Size() int {
	return xxx_messageInfo_IsSongsInRankReq.Size(m)
}
func (m *IsSongsInRankReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IsSongsInRankReq.DiscardUnknown(m)
}

var xxx_messageInfo_IsSongsInRankReq proto.InternalMessageInfo

func (m *IsSongsInRankReq) GetSongIds() []string {
	if m != nil {
		return m.SongIds
	}
	return nil
}

type IsSongsInRankResp struct {
	SongIdMap            map[string]bool `protobuf:"bytes,1,rep,name=song_id_map,json=songIdMap,proto3" json:"song_id_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *IsSongsInRankResp) Reset()         { *m = IsSongsInRankResp{} }
func (m *IsSongsInRankResp) String() string { return proto.CompactTextString(m) }
func (*IsSongsInRankResp) ProtoMessage()    {}
func (*IsSongsInRankResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{74}
}
func (m *IsSongsInRankResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IsSongsInRankResp.Unmarshal(m, b)
}
func (m *IsSongsInRankResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IsSongsInRankResp.Marshal(b, m, deterministic)
}
func (dst *IsSongsInRankResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IsSongsInRankResp.Merge(dst, src)
}
func (m *IsSongsInRankResp) XXX_Size() int {
	return xxx_messageInfo_IsSongsInRankResp.Size(m)
}
func (m *IsSongsInRankResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IsSongsInRankResp.DiscardUnknown(m)
}

var xxx_messageInfo_IsSongsInRankResp proto.InternalMessageInfo

func (m *IsSongsInRankResp) GetSongIdMap() map[string]bool {
	if m != nil {
		return m.SongIdMap
	}
	return nil
}

// 获取用户段位信息
type GetUserGloryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserGloryReq) Reset()         { *m = GetUserGloryReq{} }
func (m *GetUserGloryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserGloryReq) ProtoMessage()    {}
func (*GetUserGloryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{75}
}
func (m *GetUserGloryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGloryReq.Unmarshal(m, b)
}
func (m *GetUserGloryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGloryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserGloryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGloryReq.Merge(dst, src)
}
func (m *GetUserGloryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserGloryReq.Size(m)
}
func (m *GetUserGloryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGloryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGloryReq proto.InternalMessageInfo

func (m *GetUserGloryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserGloryResp struct {
	UserGlory            *GloryResource `protobuf:"bytes,1,opt,name=user_glory,json=userGlory,proto3" json:"user_glory,omitempty"`
	HeadImage            string         `protobuf:"bytes,2,opt,name=head_image,json=headImage,proto3" json:"head_image,omitempty"`
	NickName             string         `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserGloryResp) Reset()         { *m = GetUserGloryResp{} }
func (m *GetUserGloryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserGloryResp) ProtoMessage()    {}
func (*GetUserGloryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{76}
}
func (m *GetUserGloryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserGloryResp.Unmarshal(m, b)
}
func (m *GetUserGloryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserGloryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserGloryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserGloryResp.Merge(dst, src)
}
func (m *GetUserGloryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserGloryResp.Size(m)
}
func (m *GetUserGloryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserGloryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserGloryResp proto.InternalMessageInfo

func (m *GetUserGloryResp) GetUserGlory() *GloryResource {
	if m != nil {
		return m.UserGlory
	}
	return nil
}

func (m *GetUserGloryResp) GetHeadImage() string {
	if m != nil {
		return m.HeadImage
	}
	return ""
}

func (m *GetUserGloryResp) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

type GetUserMatchGloryReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SongId               string   `protobuf:"bytes,2,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	SingerName           string   `protobuf:"bytes,3,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMatchGloryReq) Reset()         { *m = GetUserMatchGloryReq{} }
func (m *GetUserMatchGloryReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMatchGloryReq) ProtoMessage()    {}
func (*GetUserMatchGloryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{77}
}
func (m *GetUserMatchGloryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMatchGloryReq.Unmarshal(m, b)
}
func (m *GetUserMatchGloryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMatchGloryReq.Marshal(b, m, deterministic)
}
func (dst *GetUserMatchGloryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMatchGloryReq.Merge(dst, src)
}
func (m *GetUserMatchGloryReq) XXX_Size() int {
	return xxx_messageInfo_GetUserMatchGloryReq.Size(m)
}
func (m *GetUserMatchGloryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMatchGloryReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMatchGloryReq proto.InternalMessageInfo

func (m *GetUserMatchGloryReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserMatchGloryReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *GetUserMatchGloryReq) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

type GetUserMatchGloryResp struct {
	Resource             *GloryResource `protobuf:"bytes,1,opt,name=resource,proto3" json:"resource,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserMatchGloryResp) Reset()         { *m = GetUserMatchGloryResp{} }
func (m *GetUserMatchGloryResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMatchGloryResp) ProtoMessage()    {}
func (*GetUserMatchGloryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{78}
}
func (m *GetUserMatchGloryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMatchGloryResp.Unmarshal(m, b)
}
func (m *GetUserMatchGloryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMatchGloryResp.Marshal(b, m, deterministic)
}
func (dst *GetUserMatchGloryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMatchGloryResp.Merge(dst, src)
}
func (m *GetUserMatchGloryResp) XXX_Size() int {
	return xxx_messageInfo_GetUserMatchGloryResp.Size(m)
}
func (m *GetUserMatchGloryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMatchGloryResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMatchGloryResp proto.InternalMessageInfo

func (m *GetUserMatchGloryResp) GetResource() *GloryResource {
	if m != nil {
		return m.Resource
	}
	return nil
}

// ***********************************GetUserMusicRankDialog********************************************
type GetUserMusicRankDialogReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserMusicRankDialogReq) Reset()         { *m = GetUserMusicRankDialogReq{} }
func (m *GetUserMusicRankDialogReq) String() string { return proto.CompactTextString(m) }
func (*GetUserMusicRankDialogReq) ProtoMessage()    {}
func (*GetUserMusicRankDialogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{79}
}
func (m *GetUserMusicRankDialogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMusicRankDialogReq.Unmarshal(m, b)
}
func (m *GetUserMusicRankDialogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMusicRankDialogReq.Marshal(b, m, deterministic)
}
func (dst *GetUserMusicRankDialogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMusicRankDialogReq.Merge(dst, src)
}
func (m *GetUserMusicRankDialogReq) XXX_Size() int {
	return xxx_messageInfo_GetUserMusicRankDialogReq.Size(m)
}
func (m *GetUserMusicRankDialogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMusicRankDialogReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMusicRankDialogReq proto.InternalMessageInfo

func (m *GetUserMusicRankDialogReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserMusicRankDialogResp struct {
	GloryDialog          *GloryDialog  `protobuf:"bytes,1,opt,name=glory_dialog,json=gloryDialog,proto3" json:"glory_dialog,omitempty"`
	SeasonDialog         *SeasonDialog `protobuf:"bytes,2,opt,name=season_dialog,json=seasonDialog,proto3" json:"season_dialog,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserMusicRankDialogResp) Reset()         { *m = GetUserMusicRankDialogResp{} }
func (m *GetUserMusicRankDialogResp) String() string { return proto.CompactTextString(m) }
func (*GetUserMusicRankDialogResp) ProtoMessage()    {}
func (*GetUserMusicRankDialogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{80}
}
func (m *GetUserMusicRankDialogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserMusicRankDialogResp.Unmarshal(m, b)
}
func (m *GetUserMusicRankDialogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserMusicRankDialogResp.Marshal(b, m, deterministic)
}
func (dst *GetUserMusicRankDialogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserMusicRankDialogResp.Merge(dst, src)
}
func (m *GetUserMusicRankDialogResp) XXX_Size() int {
	return xxx_messageInfo_GetUserMusicRankDialogResp.Size(m)
}
func (m *GetUserMusicRankDialogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserMusicRankDialogResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserMusicRankDialogResp proto.InternalMessageInfo

func (m *GetUserMusicRankDialogResp) GetGloryDialog() *GloryDialog {
	if m != nil {
		return m.GloryDialog
	}
	return nil
}

func (m *GetUserMusicRankDialogResp) GetSeasonDialog() *SeasonDialog {
	if m != nil {
		return m.SeasonDialog
	}
	return nil
}

type GloryDialog struct {
	CountryCount         uint32                     `protobuf:"varint,1,opt,name=country_count,json=countryCount,proto3" json:"country_count,omitempty"`
	ProvinceCount        uint32                     `protobuf:"varint,2,opt,name=province_count,json=provinceCount,proto3" json:"province_count,omitempty"`
	CityCount            uint32                     `protobuf:"varint,3,opt,name=city_count,json=cityCount,proto3" json:"city_count,omitempty"`
	Glories              []*GloryDialog_SingerGlory `protobuf:"bytes,4,rep,name=glories,proto3" json:"glories,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GloryDialog) Reset()         { *m = GloryDialog{} }
func (m *GloryDialog) String() string { return proto.CompactTextString(m) }
func (*GloryDialog) ProtoMessage()    {}
func (*GloryDialog) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{81}
}
func (m *GloryDialog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryDialog.Unmarshal(m, b)
}
func (m *GloryDialog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryDialog.Marshal(b, m, deterministic)
}
func (dst *GloryDialog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryDialog.Merge(dst, src)
}
func (m *GloryDialog) XXX_Size() int {
	return xxx_messageInfo_GloryDialog.Size(m)
}
func (m *GloryDialog) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryDialog.DiscardUnknown(m)
}

var xxx_messageInfo_GloryDialog proto.InternalMessageInfo

func (m *GloryDialog) GetCountryCount() uint32 {
	if m != nil {
		return m.CountryCount
	}
	return 0
}

func (m *GloryDialog) GetProvinceCount() uint32 {
	if m != nil {
		return m.ProvinceCount
	}
	return 0
}

func (m *GloryDialog) GetCityCount() uint32 {
	if m != nil {
		return m.CityCount
	}
	return 0
}

func (m *GloryDialog) GetGlories() []*GloryDialog_SingerGlory {
	if m != nil {
		return m.Glories
	}
	return nil
}

type GloryDialog_SingerGlory struct {
	SingerName           string         `protobuf:"bytes,1,opt,name=singer_name,json=singerName,proto3" json:"singer_name,omitempty"`
	SingerImage          string         `protobuf:"bytes,2,opt,name=singer_image,json=singerImage,proto3" json:"singer_image,omitempty"`
	Glory                *GloryResource `protobuf:"bytes,3,opt,name=glory,proto3" json:"glory,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GloryDialog_SingerGlory) Reset()         { *m = GloryDialog_SingerGlory{} }
func (m *GloryDialog_SingerGlory) String() string { return proto.CompactTextString(m) }
func (*GloryDialog_SingerGlory) ProtoMessage()    {}
func (*GloryDialog_SingerGlory) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{81, 0}
}
func (m *GloryDialog_SingerGlory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryDialog_SingerGlory.Unmarshal(m, b)
}
func (m *GloryDialog_SingerGlory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryDialog_SingerGlory.Marshal(b, m, deterministic)
}
func (dst *GloryDialog_SingerGlory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryDialog_SingerGlory.Merge(dst, src)
}
func (m *GloryDialog_SingerGlory) XXX_Size() int {
	return xxx_messageInfo_GloryDialog_SingerGlory.Size(m)
}
func (m *GloryDialog_SingerGlory) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryDialog_SingerGlory.DiscardUnknown(m)
}

var xxx_messageInfo_GloryDialog_SingerGlory proto.InternalMessageInfo

func (m *GloryDialog_SingerGlory) GetSingerName() string {
	if m != nil {
		return m.SingerName
	}
	return ""
}

func (m *GloryDialog_SingerGlory) GetSingerImage() string {
	if m != nil {
		return m.SingerImage
	}
	return ""
}

func (m *GloryDialog_SingerGlory) GetGlory() *GloryResource {
	if m != nil {
		return m.Glory
	}
	return nil
}

type SeasonDialog struct {
	FirstTitleImg        string                  `protobuf:"bytes,1,opt,name=first_title_img,json=firstTitleImg,proto3" json:"first_title_img,omitempty"`
	SecondTitleImg       string                  `protobuf:"bytes,2,opt,name=second_title_img,json=secondTitleImg,proto3" json:"second_title_img,omitempty"`
	PreStarLevel         *SeasonDialog_StarLevel `protobuf:"bytes,3,opt,name=pre_star_level,json=preStarLevel,proto3" json:"pre_star_level,omitempty"`
	CurStarLevel         *SeasonDialog_StarLevel `protobuf:"bytes,4,opt,name=cur_star_level,json=curStarLevel,proto3" json:"cur_star_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *SeasonDialog) Reset()         { *m = SeasonDialog{} }
func (m *SeasonDialog) String() string { return proto.CompactTextString(m) }
func (*SeasonDialog) ProtoMessage()    {}
func (*SeasonDialog) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{82}
}
func (m *SeasonDialog) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeasonDialog.Unmarshal(m, b)
}
func (m *SeasonDialog) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeasonDialog.Marshal(b, m, deterministic)
}
func (dst *SeasonDialog) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeasonDialog.Merge(dst, src)
}
func (m *SeasonDialog) XXX_Size() int {
	return xxx_messageInfo_SeasonDialog.Size(m)
}
func (m *SeasonDialog) XXX_DiscardUnknown() {
	xxx_messageInfo_SeasonDialog.DiscardUnknown(m)
}

var xxx_messageInfo_SeasonDialog proto.InternalMessageInfo

func (m *SeasonDialog) GetFirstTitleImg() string {
	if m != nil {
		return m.FirstTitleImg
	}
	return ""
}

func (m *SeasonDialog) GetSecondTitleImg() string {
	if m != nil {
		return m.SecondTitleImg
	}
	return ""
}

func (m *SeasonDialog) GetPreStarLevel() *SeasonDialog_StarLevel {
	if m != nil {
		return m.PreStarLevel
	}
	return nil
}

func (m *SeasonDialog) GetCurStarLevel() *SeasonDialog_StarLevel {
	if m != nil {
		return m.CurStarLevel
	}
	return nil
}

type SeasonDialog_StarLevel struct {
	Title                string   `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Img                  string   `protobuf:"bytes,2,opt,name=img,proto3" json:"img,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SeasonDialog_StarLevel) Reset()         { *m = SeasonDialog_StarLevel{} }
func (m *SeasonDialog_StarLevel) String() string { return proto.CompactTextString(m) }
func (*SeasonDialog_StarLevel) ProtoMessage()    {}
func (*SeasonDialog_StarLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{82, 0}
}
func (m *SeasonDialog_StarLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SeasonDialog_StarLevel.Unmarshal(m, b)
}
func (m *SeasonDialog_StarLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SeasonDialog_StarLevel.Marshal(b, m, deterministic)
}
func (dst *SeasonDialog_StarLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SeasonDialog_StarLevel.Merge(dst, src)
}
func (m *SeasonDialog_StarLevel) XXX_Size() int {
	return xxx_messageInfo_SeasonDialog_StarLevel.Size(m)
}
func (m *SeasonDialog_StarLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_SeasonDialog_StarLevel.DiscardUnknown(m)
}

var xxx_messageInfo_SeasonDialog_StarLevel proto.InternalMessageInfo

func (m *SeasonDialog_StarLevel) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *SeasonDialog_StarLevel) GetImg() string {
	if m != nil {
		return m.Img
	}
	return ""
}

// ***********************************UpdateUserPowerScore********************************************
type UpdateUserPowerScoreReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	SingerId             string   `protobuf:"bytes,2,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	SongId               string   `protobuf:"bytes,3,opt,name=song_id,json=songId,proto3" json:"song_id,omitempty"`
	Score                uint32   `protobuf:"varint,4,opt,name=score,proto3" json:"score,omitempty"`
	BurstLightNum        uint32   `protobuf:"varint,5,opt,name=burst_light_num,json=burstLightNum,proto3" json:"burst_light_num,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserPowerScoreReq) Reset()         { *m = UpdateUserPowerScoreReq{} }
func (m *UpdateUserPowerScoreReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserPowerScoreReq) ProtoMessage()    {}
func (*UpdateUserPowerScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{83}
}
func (m *UpdateUserPowerScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserPowerScoreReq.Unmarshal(m, b)
}
func (m *UpdateUserPowerScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserPowerScoreReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserPowerScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserPowerScoreReq.Merge(dst, src)
}
func (m *UpdateUserPowerScoreReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserPowerScoreReq.Size(m)
}
func (m *UpdateUserPowerScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserPowerScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserPowerScoreReq proto.InternalMessageInfo

func (m *UpdateUserPowerScoreReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserPowerScoreReq) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func (m *UpdateUserPowerScoreReq) GetSongId() string {
	if m != nil {
		return m.SongId
	}
	return ""
}

func (m *UpdateUserPowerScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *UpdateUserPowerScoreReq) GetBurstLightNum() uint32 {
	if m != nil {
		return m.BurstLightNum
	}
	return 0
}

type UpdateUserPowerScoreResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserPowerScoreResp) Reset()         { *m = UpdateUserPowerScoreResp{} }
func (m *UpdateUserPowerScoreResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserPowerScoreResp) ProtoMessage()    {}
func (*UpdateUserPowerScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{84}
}
func (m *UpdateUserPowerScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserPowerScoreResp.Unmarshal(m, b)
}
func (m *UpdateUserPowerScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserPowerScoreResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserPowerScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserPowerScoreResp.Merge(dst, src)
}
func (m *UpdateUserPowerScoreResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserPowerScoreResp.Size(m)
}
func (m *UpdateUserPowerScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserPowerScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserPowerScoreResp proto.InternalMessageInfo

// ***********************************UpdateUserStarLevel********************************************
type UpdateUserStarLevelReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	StarLevel            int32    `protobuf:"varint,2,opt,name=star_level,json=starLevel,proto3" json:"star_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserStarLevelReq) Reset()         { *m = UpdateUserStarLevelReq{} }
func (m *UpdateUserStarLevelReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserStarLevelReq) ProtoMessage()    {}
func (*UpdateUserStarLevelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{85}
}
func (m *UpdateUserStarLevelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserStarLevelReq.Unmarshal(m, b)
}
func (m *UpdateUserStarLevelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserStarLevelReq.Marshal(b, m, deterministic)
}
func (dst *UpdateUserStarLevelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserStarLevelReq.Merge(dst, src)
}
func (m *UpdateUserStarLevelReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserStarLevelReq.Size(m)
}
func (m *UpdateUserStarLevelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserStarLevelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserStarLevelReq proto.InternalMessageInfo

func (m *UpdateUserStarLevelReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UpdateUserStarLevelReq) GetStarLevel() int32 {
	if m != nil {
		return m.StarLevel
	}
	return 0
}

type UpdateUserStarLevelResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserStarLevelResp) Reset()         { *m = UpdateUserStarLevelResp{} }
func (m *UpdateUserStarLevelResp) String() string { return proto.CompactTextString(m) }
func (*UpdateUserStarLevelResp) ProtoMessage()    {}
func (*UpdateUserStarLevelResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{86}
}
func (m *UpdateUserStarLevelResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserStarLevelResp.Unmarshal(m, b)
}
func (m *UpdateUserStarLevelResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserStarLevelResp.Marshal(b, m, deterministic)
}
func (dst *UpdateUserStarLevelResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserStarLevelResp.Merge(dst, src)
}
func (m *UpdateUserStarLevelResp) XXX_Size() int {
	return xxx_messageInfo_UpdateUserStarLevelResp.Size(m)
}
func (m *UpdateUserStarLevelResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserStarLevelResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserStarLevelResp proto.InternalMessageInfo

// ***********************************UserMusicRankDialogConfirm********************************************
type UserMusicRankDialogConfirmReq struct {
	Uid                  uint32                                `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ReqType              UserMusicRankDialogConfirmReq_ReqType `protobuf:"varint,2,opt,name=req_type,json=reqType,proto3,enum=user_music_rank.UserMusicRankDialogConfirmReq_ReqType" json:"req_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-"`
	XXX_unrecognized     []byte                                `json:"-"`
	XXX_sizecache        int32                                 `json:"-"`
}

func (m *UserMusicRankDialogConfirmReq) Reset()         { *m = UserMusicRankDialogConfirmReq{} }
func (m *UserMusicRankDialogConfirmReq) String() string { return proto.CompactTextString(m) }
func (*UserMusicRankDialogConfirmReq) ProtoMessage()    {}
func (*UserMusicRankDialogConfirmReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{87}
}
func (m *UserMusicRankDialogConfirmReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicRankDialogConfirmReq.Unmarshal(m, b)
}
func (m *UserMusicRankDialogConfirmReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicRankDialogConfirmReq.Marshal(b, m, deterministic)
}
func (dst *UserMusicRankDialogConfirmReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicRankDialogConfirmReq.Merge(dst, src)
}
func (m *UserMusicRankDialogConfirmReq) XXX_Size() int {
	return xxx_messageInfo_UserMusicRankDialogConfirmReq.Size(m)
}
func (m *UserMusicRankDialogConfirmReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicRankDialogConfirmReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicRankDialogConfirmReq proto.InternalMessageInfo

func (m *UserMusicRankDialogConfirmReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserMusicRankDialogConfirmReq) GetReqType() UserMusicRankDialogConfirmReq_ReqType {
	if m != nil {
		return m.ReqType
	}
	return UserMusicRankDialogConfirmReq_SessionDialog
}

type UserMusicRankDialogConfirmResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserMusicRankDialogConfirmResp) Reset()         { *m = UserMusicRankDialogConfirmResp{} }
func (m *UserMusicRankDialogConfirmResp) String() string { return proto.CompactTextString(m) }
func (*UserMusicRankDialogConfirmResp) ProtoMessage()    {}
func (*UserMusicRankDialogConfirmResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{88}
}
func (m *UserMusicRankDialogConfirmResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserMusicRankDialogConfirmResp.Unmarshal(m, b)
}
func (m *UserMusicRankDialogConfirmResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserMusicRankDialogConfirmResp.Marshal(b, m, deterministic)
}
func (dst *UserMusicRankDialogConfirmResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserMusicRankDialogConfirmResp.Merge(dst, src)
}
func (m *UserMusicRankDialogConfirmResp) XXX_Size() int {
	return xxx_messageInfo_UserMusicRankDialogConfirmResp.Size(m)
}
func (m *UserMusicRankDialogConfirmResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserMusicRankDialogConfirmResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserMusicRankDialogConfirmResp proto.InternalMessageInfo

type GetRatingForScoreReq struct {
	Score                uint32   `protobuf:"varint,1,opt,name=score,proto3" json:"score,omitempty"`
	MaxScore             uint32   `protobuf:"varint,2,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRatingForScoreReq) Reset()         { *m = GetRatingForScoreReq{} }
func (m *GetRatingForScoreReq) String() string { return proto.CompactTextString(m) }
func (*GetRatingForScoreReq) ProtoMessage()    {}
func (*GetRatingForScoreReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{89}
}
func (m *GetRatingForScoreReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRatingForScoreReq.Unmarshal(m, b)
}
func (m *GetRatingForScoreReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRatingForScoreReq.Marshal(b, m, deterministic)
}
func (dst *GetRatingForScoreReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRatingForScoreReq.Merge(dst, src)
}
func (m *GetRatingForScoreReq) XXX_Size() int {
	return xxx_messageInfo_GetRatingForScoreReq.Size(m)
}
func (m *GetRatingForScoreReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRatingForScoreReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRatingForScoreReq proto.InternalMessageInfo

func (m *GetRatingForScoreReq) GetScore() uint32 {
	if m != nil {
		return m.Score
	}
	return 0
}

func (m *GetRatingForScoreReq) GetMaxScore() uint32 {
	if m != nil {
		return m.MaxScore
	}
	return 0
}

type GetRatingForScoreResp struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ScorePercentage      uint32   `protobuf:"varint,3,opt,name=score_percentage,json=scorePercentage,proto3" json:"score_percentage,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRatingForScoreResp) Reset()         { *m = GetRatingForScoreResp{} }
func (m *GetRatingForScoreResp) String() string { return proto.CompactTextString(m) }
func (*GetRatingForScoreResp) ProtoMessage()    {}
func (*GetRatingForScoreResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{90}
}
func (m *GetRatingForScoreResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRatingForScoreResp.Unmarshal(m, b)
}
func (m *GetRatingForScoreResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRatingForScoreResp.Marshal(b, m, deterministic)
}
func (dst *GetRatingForScoreResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRatingForScoreResp.Merge(dst, src)
}
func (m *GetRatingForScoreResp) XXX_Size() int {
	return xxx_messageInfo_GetRatingForScoreResp.Size(m)
}
func (m *GetRatingForScoreResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRatingForScoreResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRatingForScoreResp proto.InternalMessageInfo

func (m *GetRatingForScoreResp) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GetRatingForScoreResp) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *GetRatingForScoreResp) GetScorePercentage() uint32 {
	if m != nil {
		return m.ScorePercentage
	}
	return 0
}

// ***********************************公共参数************************************************
type UserPowerInfo struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Loc                  *Location      `protobuf:"bytes,2,opt,name=loc,proto3" json:"loc,omitempty"`
	Glory                *GloryResource `protobuf:"bytes,3,opt,name=glory,proto3" json:"glory,omitempty"`
	StarLevel            *StarLevel     `protobuf:"bytes,4,opt,name=star_level,json=starLevel,proto3" json:"star_level,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserPowerInfo) Reset()         { *m = UserPowerInfo{} }
func (m *UserPowerInfo) String() string { return proto.CompactTextString(m) }
func (*UserPowerInfo) ProtoMessage()    {}
func (*UserPowerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{91}
}
func (m *UserPowerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPowerInfo.Unmarshal(m, b)
}
func (m *UserPowerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPowerInfo.Marshal(b, m, deterministic)
}
func (dst *UserPowerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPowerInfo.Merge(dst, src)
}
func (m *UserPowerInfo) XXX_Size() int {
	return xxx_messageInfo_UserPowerInfo.Size(m)
}
func (m *UserPowerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPowerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserPowerInfo proto.InternalMessageInfo

func (m *UserPowerInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPowerInfo) GetLoc() *Location {
	if m != nil {
		return m.Loc
	}
	return nil
}

func (m *UserPowerInfo) GetGlory() *GloryResource {
	if m != nil {
		return m.Glory
	}
	return nil
}

func (m *UserPowerInfo) GetStarLevel() *StarLevel {
	if m != nil {
		return m.StarLevel
	}
	return nil
}

type Location struct {
	Country              string   `protobuf:"bytes,1,opt,name=country,proto3" json:"country,omitempty"`
	CountryCode          string   `protobuf:"bytes,2,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Province             string   `protobuf:"bytes,3,opt,name=province,proto3" json:"province,omitempty"`
	ProvinceCode         string   `protobuf:"bytes,4,opt,name=province_code,json=provinceCode,proto3" json:"province_code,omitempty"`
	City                 string   `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty"`
	CityCode             string   `protobuf:"bytes,6,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Location) Reset()         { *m = Location{} }
func (m *Location) String() string { return proto.CompactTextString(m) }
func (*Location) ProtoMessage()    {}
func (*Location) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{92}
}
func (m *Location) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Location.Unmarshal(m, b)
}
func (m *Location) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Location.Marshal(b, m, deterministic)
}
func (dst *Location) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Location.Merge(dst, src)
}
func (m *Location) XXX_Size() int {
	return xxx_messageInfo_Location.Size(m)
}
func (m *Location) XXX_DiscardUnknown() {
	xxx_messageInfo_Location.DiscardUnknown(m)
}

var xxx_messageInfo_Location proto.InternalMessageInfo

func (m *Location) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *Location) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *Location) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *Location) GetProvinceCode() string {
	if m != nil {
		return m.ProvinceCode
	}
	return ""
}

func (m *Location) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *Location) GetCityCode() string {
	if m != nil {
		return m.CityCode
	}
	return ""
}

type StarLevel struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	LevelDesc            string   `protobuf:"bytes,2,opt,name=level_desc,json=levelDesc,proto3" json:"level_desc,omitempty"`
	Star                 uint32   `protobuf:"varint,3,opt,name=star,proto3" json:"star,omitempty"`
	Image                string   `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	BgImage              string   `protobuf:"bytes,5,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	IsTopLevel           bool     `protobuf:"varint,6,opt,name=is_top_level,json=isTopLevel,proto3" json:"is_top_level,omitempty"`
	StarStyle            uint32   `protobuf:"varint,7,opt,name=star_style,json=starStyle,proto3" json:"star_style,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *StarLevel) Reset()         { *m = StarLevel{} }
func (m *StarLevel) String() string { return proto.CompactTextString(m) }
func (*StarLevel) ProtoMessage()    {}
func (*StarLevel) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{93}
}
func (m *StarLevel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StarLevel.Unmarshal(m, b)
}
func (m *StarLevel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StarLevel.Marshal(b, m, deterministic)
}
func (dst *StarLevel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StarLevel.Merge(dst, src)
}
func (m *StarLevel) XXX_Size() int {
	return xxx_messageInfo_StarLevel.Size(m)
}
func (m *StarLevel) XXX_DiscardUnknown() {
	xxx_messageInfo_StarLevel.DiscardUnknown(m)
}

var xxx_messageInfo_StarLevel proto.InternalMessageInfo

func (m *StarLevel) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *StarLevel) GetLevelDesc() string {
	if m != nil {
		return m.LevelDesc
	}
	return ""
}

func (m *StarLevel) GetStar() uint32 {
	if m != nil {
		return m.Star
	}
	return 0
}

func (m *StarLevel) GetImage() string {
	if m != nil {
		return m.Image
	}
	return ""
}

func (m *StarLevel) GetBgImage() string {
	if m != nil {
		return m.BgImage
	}
	return ""
}

func (m *StarLevel) GetIsTopLevel() bool {
	if m != nil {
		return m.IsTopLevel
	}
	return false
}

func (m *StarLevel) GetStarStyle() uint32 {
	if m != nil {
		return m.StarStyle
	}
	return 0
}

type Season struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StartAt              uint64   `protobuf:"varint,2,opt,name=start_at,json=startAt,proto3" json:"start_at,omitempty"`
	EndAt                uint64   `protobuf:"varint,3,opt,name=end_at,json=endAt,proto3" json:"end_at,omitempty"`
	FirstTitleImg        string   `protobuf:"bytes,4,opt,name=first_title_img,json=firstTitleImg,proto3" json:"first_title_img,omitempty"`
	SecondTitleImg       string   `protobuf:"bytes,5,opt,name=second_title_img,json=secondTitleImg,proto3" json:"second_title_img,omitempty"`
	TitleImg             string   `protobuf:"bytes,6,opt,name=title_img,json=titleImg,proto3" json:"title_img,omitempty"`
	DescImgs             []string `protobuf:"bytes,7,rep,name=desc_imgs,json=descImgs,proto3" json:"desc_imgs,omitempty"`
	RuleImgs             []string `protobuf:"bytes,8,rep,name=rule_imgs,json=ruleImgs,proto3" json:"rule_imgs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Season) Reset()         { *m = Season{} }
func (m *Season) String() string { return proto.CompactTextString(m) }
func (*Season) ProtoMessage()    {}
func (*Season) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{94}
}
func (m *Season) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Season.Unmarshal(m, b)
}
func (m *Season) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Season.Marshal(b, m, deterministic)
}
func (dst *Season) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Season.Merge(dst, src)
}
func (m *Season) XXX_Size() int {
	return xxx_messageInfo_Season.Size(m)
}
func (m *Season) XXX_DiscardUnknown() {
	xxx_messageInfo_Season.DiscardUnknown(m)
}

var xxx_messageInfo_Season proto.InternalMessageInfo

func (m *Season) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Season) GetStartAt() uint64 {
	if m != nil {
		return m.StartAt
	}
	return 0
}

func (m *Season) GetEndAt() uint64 {
	if m != nil {
		return m.EndAt
	}
	return 0
}

func (m *Season) GetFirstTitleImg() string {
	if m != nil {
		return m.FirstTitleImg
	}
	return ""
}

func (m *Season) GetSecondTitleImg() string {
	if m != nil {
		return m.SecondTitleImg
	}
	return ""
}

func (m *Season) GetTitleImg() string {
	if m != nil {
		return m.TitleImg
	}
	return ""
}

func (m *Season) GetDescImgs() []string {
	if m != nil {
		return m.DescImgs
	}
	return nil
}

func (m *Season) GetRuleImgs() []string {
	if m != nil {
		return m.RuleImgs
	}
	return nil
}

type GloryResource struct {
	GloryName            string     `protobuf:"bytes,1,opt,name=glory_name,json=gloryName,proto3" json:"glory_name,omitempty"`
	GloryImg             string     `protobuf:"bytes,2,opt,name=glory_img,json=gloryImg,proto3" json:"glory_img,omitempty"`
	GloryColor           string     `protobuf:"bytes,3,opt,name=glory_color,json=gloryColor,proto3" json:"glory_color,omitempty"`
	GloryRank            uint32     `protobuf:"varint,4,opt,name=glory_rank,json=gloryRank,proto3" json:"glory_rank,omitempty"`
	PlayColor            string     `protobuf:"bytes,5,opt,name=play_color,json=playColor,proto3" json:"play_color,omitempty"`
	BannerImg            string     `protobuf:"bytes,6,opt,name=banner_img,json=bannerImg,proto3" json:"banner_img,omitempty"`
	GloryBgImg           string     `protobuf:"bytes,7,opt,name=glory_bg_img,json=gloryBgImg,proto3" json:"glory_bg_img,omitempty"`
	GloryId              string     `protobuf:"bytes,8,opt,name=glory_id,json=gloryId,proto3" json:"glory_id,omitempty"`
	Level                GloryLevel `protobuf:"varint,9,opt,name=level,proto3,enum=user_music_rank.GloryLevel" json:"level,omitempty"`
	LocCode              string     `protobuf:"bytes,10,opt,name=loc_code,json=locCode,proto3" json:"loc_code,omitempty"`
	SingerId             string     `protobuf:"bytes,11,opt,name=singer_id,json=singerId,proto3" json:"singer_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GloryResource) Reset()         { *m = GloryResource{} }
func (m *GloryResource) String() string { return proto.CompactTextString(m) }
func (*GloryResource) ProtoMessage()    {}
func (*GloryResource) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_music_rank_b8b8c83fb86d4d3b, []int{95}
}
func (m *GloryResource) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GloryResource.Unmarshal(m, b)
}
func (m *GloryResource) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GloryResource.Marshal(b, m, deterministic)
}
func (dst *GloryResource) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GloryResource.Merge(dst, src)
}
func (m *GloryResource) XXX_Size() int {
	return xxx_messageInfo_GloryResource.Size(m)
}
func (m *GloryResource) XXX_DiscardUnknown() {
	xxx_messageInfo_GloryResource.DiscardUnknown(m)
}

var xxx_messageInfo_GloryResource proto.InternalMessageInfo

func (m *GloryResource) GetGloryName() string {
	if m != nil {
		return m.GloryName
	}
	return ""
}

func (m *GloryResource) GetGloryImg() string {
	if m != nil {
		return m.GloryImg
	}
	return ""
}

func (m *GloryResource) GetGloryColor() string {
	if m != nil {
		return m.GloryColor
	}
	return ""
}

func (m *GloryResource) GetGloryRank() uint32 {
	if m != nil {
		return m.GloryRank
	}
	return 0
}

func (m *GloryResource) GetPlayColor() string {
	if m != nil {
		return m.PlayColor
	}
	return ""
}

func (m *GloryResource) GetBannerImg() string {
	if m != nil {
		return m.BannerImg
	}
	return ""
}

func (m *GloryResource) GetGloryBgImg() string {
	if m != nil {
		return m.GloryBgImg
	}
	return ""
}

func (m *GloryResource) GetGloryId() string {
	if m != nil {
		return m.GloryId
	}
	return ""
}

func (m *GloryResource) GetLevel() GloryLevel {
	if m != nil {
		return m.Level
	}
	return GloryLevel_City
}

func (m *GloryResource) GetLocCode() string {
	if m != nil {
		return m.LocCode
	}
	return ""
}

func (m *GloryResource) GetSingerId() string {
	if m != nil {
		return m.SingerId
	}
	return ""
}

func init() {
	proto.RegisterType((*AddBlackUidsReq)(nil), "user_music_rank.AddBlackUidsReq")
	proto.RegisterType((*AddBlackUidsResp)(nil), "user_music_rank.AddBlackUidsResp")
	proto.RegisterType((*RemoveBlackUidsReq)(nil), "user_music_rank.RemoveBlackUidsReq")
	proto.RegisterType((*RemoveBlackUidsResp)(nil), "user_music_rank.RemoveBlackUidsResp")
	proto.RegisterType((*ListBlackUidsReq)(nil), "user_music_rank.ListBlackUidsReq")
	proto.RegisterType((*ListBlackUidsResp)(nil), "user_music_rank.ListBlackUidsResp")
	proto.RegisterType((*ListBlackUidsResp_UidRecord)(nil), "user_music_rank.ListBlackUidsResp.UidRecord")
	proto.RegisterType((*DelSongsReq)(nil), "user_music_rank.DelSongsReq")
	proto.RegisterType((*DelSongsResp)(nil), "user_music_rank.DelSongsResp")
	proto.RegisterType((*UpsertCitiesReq)(nil), "user_music_rank.UpsertCitiesReq")
	proto.RegisterType((*UpsertCitiesReq_City)(nil), "user_music_rank.UpsertCitiesReq.City")
	proto.RegisterType((*UpsertCitiesResp)(nil), "user_music_rank.UpsertCitiesResp")
	proto.RegisterType((*UpsertSchoolsReq)(nil), "user_music_rank.UpsertSchoolsReq")
	proto.RegisterType((*UpsertSchoolsReq_SchoolInfo)(nil), "user_music_rank.UpsertSchoolsReq.SchoolInfo")
	proto.RegisterType((*UpsertSchoolsResp)(nil), "user_music_rank.UpsertSchoolsResp")
	proto.RegisterType((*UpdateSchoolScoreReq)(nil), "user_music_rank.UpdateSchoolScoreReq")
	proto.RegisterType((*UpdateSchoolScoreResp)(nil), "user_music_rank.UpdateSchoolScoreResp")
	proto.RegisterType((*GetMirrorDataReq)(nil), "user_music_rank.GetMirrorDataReq")
	proto.RegisterType((*GetMirrorDataResp)(nil), "user_music_rank.GetMirrorDataResp")
	proto.RegisterType((*GetMirrorDataResp_User)(nil), "user_music_rank.GetMirrorDataResp.User")
	proto.RegisterType((*GetMirrorDataResp_UserRankInfo)(nil), "user_music_rank.GetMirrorDataResp.UserRankInfo")
	proto.RegisterType((*GetMirrorDataResp_SchoolInfo)(nil), "user_music_rank.GetMirrorDataResp.SchoolInfo")
	proto.RegisterType((*GetMirrorIdReq)(nil), "user_music_rank.GetMirrorIdReq")
	proto.RegisterType((*GetMirrorIdResp)(nil), "user_music_rank.GetMirrorIdResp")
	proto.RegisterType((*RebuildSchoolBindReq)(nil), "user_music_rank.RebuildSchoolBindReq")
	proto.RegisterType((*RebuildSchoolBindResp)(nil), "user_music_rank.RebuildSchoolBindResp")
	proto.RegisterType((*RebuildSchoolRankReq)(nil), "user_music_rank.RebuildSchoolRankReq")
	proto.RegisterType((*RebuildSchoolRankResp)(nil), "user_music_rank.RebuildSchoolRankResp")
	proto.RegisterType((*GetSchoolRanReq)(nil), "user_music_rank.GetSchoolRanReq")
	proto.RegisterType((*GetSchoolRanResp)(nil), "user_music_rank.GetSchoolRanResp")
	proto.RegisterType((*GetSchoolRanResp_School)(nil), "user_music_rank.GetSchoolRanResp.School")
	proto.RegisterType((*GetUserRankInSchoolReq)(nil), "user_music_rank.GetUserRankInSchoolReq")
	proto.RegisterType((*GetUserRankInSchoolResp)(nil), "user_music_rank.GetUserRankInSchoolResp")
	proto.RegisterType((*GetUserRankInSchoolResp_User)(nil), "user_music_rank.GetUserRankInSchoolResp.User")
	proto.RegisterType((*GetUserRankInSchoolResp_School)(nil), "user_music_rank.GetUserRankInSchoolResp.School")
	proto.RegisterType((*GetSchoolRankInCityReq)(nil), "user_music_rank.GetSchoolRankInCityReq")
	proto.RegisterType((*GetSchoolRankInCityResp)(nil), "user_music_rank.GetSchoolRankInCityResp")
	proto.RegisterType((*GetSchoolRankInCityResp_School)(nil), "user_music_rank.GetSchoolRankInCityResp.School")
	proto.RegisterType((*GetUserRankInCityReq)(nil), "user_music_rank.GetUserRankInCityReq")
	proto.RegisterType((*GetUserRankInCityResp)(nil), "user_music_rank.GetUserRankInCityResp")
	proto.RegisterType((*GetUserRankInCityResp_User)(nil), "user_music_rank.GetUserRankInCityResp.User")
	proto.RegisterType((*GetUserRankInCityResp_School)(nil), "user_music_rank.GetUserRankInCityResp.School")
	proto.RegisterType((*BindSchoolReq)(nil), "user_music_rank.BindSchoolReq")
	proto.RegisterType((*BindSchoolResp)(nil), "user_music_rank.BindSchoolResp")
	proto.RegisterType((*ListSchoolsReq)(nil), "user_music_rank.ListSchoolsReq")
	proto.RegisterType((*ListSchoolsResp)(nil), "user_music_rank.ListSchoolsResp")
	proto.RegisterType((*ListSchoolsResp_School)(nil), "user_music_rank.ListSchoolsResp.School")
	proto.RegisterType((*GetUserSchoolInfoReq)(nil), "user_music_rank.GetUserSchoolInfoReq")
	proto.RegisterType((*GetUserSchoolInfoResp)(nil), "user_music_rank.GetUserSchoolInfoResp")
	proto.RegisterType((*GetUserSchoolInfoResp_School)(nil), "user_music_rank.GetUserSchoolInfoResp.School")
	proto.RegisterType((*GetSeasonWebInfoReq)(nil), "user_music_rank.GetSeasonWebInfoReq")
	proto.RegisterType((*GetSeasonWebInfoResp)(nil), "user_music_rank.GetSeasonWebInfoResp")
	proto.RegisterType((*UserMusicRankGloryTriggerReq)(nil), "user_music_rank.UserMusicRankGloryTriggerReq")
	proto.RegisterType((*UserMusicRankGloryTriggerResp)(nil), "user_music_rank.UserMusicRankGloryTriggerResp")
	proto.RegisterType((*UserMusicRankTriggerReq)(nil), "user_music_rank.UserMusicRankTriggerReq")
	proto.RegisterType((*UserMusicRankTriggerResp)(nil), "user_music_rank.UserMusicRankTriggerResp")
	proto.RegisterType((*GetMusicRankUserInfoReq)(nil), "user_music_rank.GetMusicRankUserInfoReq")
	proto.RegisterType((*GetMusicRankUserInfoResp)(nil), "user_music_rank.GetMusicRankUserInfoResp")
	proto.RegisterMapType((map[uint32]*UserGloryInfo)(nil), "user_music_rank.GetMusicRankUserInfoResp.UserGloryMapEntry")
	proto.RegisterType((*UserGloryInfo)(nil), "user_music_rank.UserGloryInfo")
	proto.RegisterType((*BatchUserPowerInfoReq)(nil), "user_music_rank.BatchUserPowerInfoReq")
	proto.RegisterType((*BatchUserPowerInfoResp)(nil), "user_music_rank.BatchUserPowerInfoResp")
	proto.RegisterMapType((map[uint32]*UserPowerInfo)(nil), "user_music_rank.BatchUserPowerInfoResp.UserPowerMapEntry")
	proto.RegisterType((*BatchUserGloryReq)(nil), "user_music_rank.BatchUserGloryReq")
	proto.RegisterType((*BatchUserGloryResp)(nil), "user_music_rank.BatchUserGloryResp")
	proto.RegisterMapType((map[uint32]*GloryResource)(nil), "user_music_rank.BatchUserGloryResp.UserGloryMapEntry")
	proto.RegisterType((*ListUserGloriesReq)(nil), "user_music_rank.ListUserGloriesReq")
	proto.RegisterType((*ListUserGloriesResp)(nil), "user_music_rank.ListUserGloriesResp")
	proto.RegisterType((*SetUserGloryReq)(nil), "user_music_rank.SetUserGloryReq")
	proto.RegisterType((*SetUserGloryResp)(nil), "user_music_rank.SetUserGloryResp")
	proto.RegisterType((*ListUserHistoryGloriesReq)(nil), "user_music_rank.ListUserHistoryGloriesReq")
	proto.RegisterType((*ListUserHistoryGloriesResp)(nil), "user_music_rank.ListUserHistoryGloriesResp")
	proto.RegisterType((*SetUserLocationReq)(nil), "user_music_rank.SetUserLocationReq")
	proto.RegisterType((*SetUserLocationResp)(nil), "user_music_rank.SetUserLocationResp")
	proto.RegisterType((*GetUserLocationReq)(nil), "user_music_rank.GetUserLocationReq")
	proto.RegisterType((*GetUserLocationResp)(nil), "user_music_rank.GetUserLocationResp")
	proto.RegisterType((*GetUserLocationAuthReq)(nil), "user_music_rank.GetUserLocationAuthReq")
	proto.RegisterType((*GetUserLocationAuthResp)(nil), "user_music_rank.GetUserLocationAuthResp")
	proto.RegisterType((*SetUserLocationAuthReq)(nil), "user_music_rank.SetUserLocationAuthReq")
	proto.RegisterType((*SetUserLocationAuthResp)(nil), "user_music_rank.SetUserLocationAuthResp")
	proto.RegisterType((*ListUserSingerScoreReq)(nil), "user_music_rank.ListUserSingerScoreReq")
	proto.RegisterType((*ListUserSingerScoreResp)(nil), "user_music_rank.ListUserSingerScoreResp")
	proto.RegisterType((*ListUserSingerScoreResp_SingerScoreInfo)(nil), "user_music_rank.ListUserSingerScoreResp.SingerScoreInfo")
	proto.RegisterType((*GetUserSingerScoreDetailReq)(nil), "user_music_rank.GetUserSingerScoreDetailReq")
	proto.RegisterType((*GetUserSingerScoreDetailResp)(nil), "user_music_rank.GetUserSingerScoreDetailResp")
	proto.RegisterType((*GetUserSingerScoreDetailResp_SingerScoreInfo)(nil), "user_music_rank.GetUserSingerScoreDetailResp.SingerScoreInfo")
	proto.RegisterType((*GetUserSingerScoreDetailResp_SongScoreInfo)(nil), "user_music_rank.GetUserSingerScoreDetailResp.SongScoreInfo")
	proto.RegisterType((*GetStarRankReq)(nil), "user_music_rank.GetStarRankReq")
	proto.RegisterType((*GetStarRankResp)(nil), "user_music_rank.GetStarRankResp")
	proto.RegisterType((*GetStarRankResp_UserStarInfo)(nil), "user_music_rank.GetStarRankResp.UserStarInfo")
	proto.RegisterType((*GetSingerScoreRankReq)(nil), "user_music_rank.GetSingerScoreRankReq")
	proto.RegisterType((*GetSingerScoreRankResp)(nil), "user_music_rank.GetSingerScoreRankResp")
	proto.RegisterType((*GetSingerScoreRankResp_UserScoreInfo)(nil), "user_music_rank.GetSingerScoreRankResp.UserScoreInfo")
	proto.RegisterType((*ListUserMusicRecordsReq)(nil), "user_music_rank.ListUserMusicRecordsReq")
	proto.RegisterType((*ListUserMusicRecordsResp)(nil), "user_music_rank.ListUserMusicRecordsResp")
	proto.RegisterType((*ListUserMusicRecordsResp_Record)(nil), "user_music_rank.ListUserMusicRecordsResp.Record")
	proto.RegisterType((*IsSongsInRankReq)(nil), "user_music_rank.IsSongsInRankReq")
	proto.RegisterType((*IsSongsInRankResp)(nil), "user_music_rank.IsSongsInRankResp")
	proto.RegisterMapType((map[string]bool)(nil), "user_music_rank.IsSongsInRankResp.SongIdMapEntry")
	proto.RegisterType((*GetUserGloryReq)(nil), "user_music_rank.GetUserGloryReq")
	proto.RegisterType((*GetUserGloryResp)(nil), "user_music_rank.GetUserGloryResp")
	proto.RegisterType((*GetUserMatchGloryReq)(nil), "user_music_rank.GetUserMatchGloryReq")
	proto.RegisterType((*GetUserMatchGloryResp)(nil), "user_music_rank.GetUserMatchGloryResp")
	proto.RegisterType((*GetUserMusicRankDialogReq)(nil), "user_music_rank.GetUserMusicRankDialogReq")
	proto.RegisterType((*GetUserMusicRankDialogResp)(nil), "user_music_rank.GetUserMusicRankDialogResp")
	proto.RegisterType((*GloryDialog)(nil), "user_music_rank.GloryDialog")
	proto.RegisterType((*GloryDialog_SingerGlory)(nil), "user_music_rank.GloryDialog.SingerGlory")
	proto.RegisterType((*SeasonDialog)(nil), "user_music_rank.SeasonDialog")
	proto.RegisterType((*SeasonDialog_StarLevel)(nil), "user_music_rank.SeasonDialog.StarLevel")
	proto.RegisterType((*UpdateUserPowerScoreReq)(nil), "user_music_rank.UpdateUserPowerScoreReq")
	proto.RegisterType((*UpdateUserPowerScoreResp)(nil), "user_music_rank.UpdateUserPowerScoreResp")
	proto.RegisterType((*UpdateUserStarLevelReq)(nil), "user_music_rank.UpdateUserStarLevelReq")
	proto.RegisterType((*UpdateUserStarLevelResp)(nil), "user_music_rank.UpdateUserStarLevelResp")
	proto.RegisterType((*UserMusicRankDialogConfirmReq)(nil), "user_music_rank.UserMusicRankDialogConfirmReq")
	proto.RegisterType((*UserMusicRankDialogConfirmResp)(nil), "user_music_rank.UserMusicRankDialogConfirmResp")
	proto.RegisterType((*GetRatingForScoreReq)(nil), "user_music_rank.GetRatingForScoreReq")
	proto.RegisterType((*GetRatingForScoreResp)(nil), "user_music_rank.GetRatingForScoreResp")
	proto.RegisterType((*UserPowerInfo)(nil), "user_music_rank.UserPowerInfo")
	proto.RegisterType((*Location)(nil), "user_music_rank.Location")
	proto.RegisterType((*StarLevel)(nil), "user_music_rank.StarLevel")
	proto.RegisterType((*Season)(nil), "user_music_rank.Season")
	proto.RegisterType((*GloryResource)(nil), "user_music_rank.GloryResource")
	proto.RegisterEnum("user_music_rank.GloryLevel", GloryLevel_name, GloryLevel_value)
	proto.RegisterEnum("user_music_rank.GetUserSingerScoreDetailReq_ReqType", GetUserSingerScoreDetailReq_ReqType_name, GetUserSingerScoreDetailReq_ReqType_value)
	proto.RegisterEnum("user_music_rank.ListUserMusicRecordsResp_SingType", ListUserMusicRecordsResp_SingType_name, ListUserMusicRecordsResp_SingType_value)
	proto.RegisterEnum("user_music_rank.UserMusicRankDialogConfirmReq_ReqType", UserMusicRankDialogConfirmReq_ReqType_name, UserMusicRankDialogConfirmReq_ReqType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserMusicRankClient is the client API for UserMusicRank service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserMusicRankClient interface {
	// 批量获取用户段位称号
	GetMusicRankUserInfo(ctx context.Context, in *GetMusicRankUserInfoReq, opts ...grpc.CallOption) (*GetMusicRankUserInfoResp, error)
	// 批量获取用户实力信息
	BatchUserPowerInfo(ctx context.Context, in *BatchUserPowerInfoReq, opts ...grpc.CallOption) (*BatchUserPowerInfoResp, error)
	// 批量获取用户当前称号
	BatchUserGlory(ctx context.Context, in *BatchUserGloryReq, opts ...grpc.CallOption) (*BatchUserGloryResp, error)
	// 获取用户当前称号列表
	ListUserGlories(ctx context.Context, in *ListUserGloriesReq, opts ...grpc.CallOption) (*ListUserGloriesResp, error)
	// 设置用户当前外显称号
	SetUserGlory(ctx context.Context, in *SetUserGloryReq, opts ...grpc.CallOption) (*SetUserGloryResp, error)
	// 获取用户历史称号列表
	ListUserHistoryGlories(ctx context.Context, in *ListUserHistoryGloriesReq, opts ...grpc.CallOption) (*ListUserHistoryGloriesResp, error)
	// 设置用户当前位置
	SetUserLocation(ctx context.Context, in *SetUserLocationReq, opts ...grpc.CallOption) (*SetUserLocationResp, error)
	// 获取用户歌手唱力榜
	ListUserSingerScore(ctx context.Context, in *ListUserSingerScoreReq, opts ...grpc.CallOption) (*ListUserSingerScoreResp, error)
	// 获取用户歌手唱力详情
	GetUserSingerScoreDetail(ctx context.Context, in *GetUserSingerScoreDetailReq, opts ...grpc.CallOption) (*GetUserSingerScoreDetailResp, error)
	// 获取用户歌曲记录
	ListUserMusicRecords(ctx context.Context, in *ListUserMusicRecordsReq, opts ...grpc.CallOption) (*ListUserMusicRecordsResp, error)
	// 获取段位排行榜
	GetStarRank(ctx context.Context, in *GetStarRankReq, opts ...grpc.CallOption) (*GetStarRankResp, error)
	// 获取歌手唱力排行榜
	GetSingerScoreRank(ctx context.Context, in *GetSingerScoreRankReq, opts ...grpc.CallOption) (*GetSingerScoreRankResp, error)
	// 歌曲是否在在排行榜歌曲中
	IsSongsInRank(ctx context.Context, in *IsSongsInRankReq, opts ...grpc.CallOption) (*IsSongsInRankResp, error)
	// 获取用户位置
	GetUserLocation(ctx context.Context, in *GetUserLocationReq, opts ...grpc.CallOption) (*GetUserLocationResp, error)
	// 获取用户位置权限设置
	GetUserLocationAuth(ctx context.Context, in *GetUserLocationAuthReq, opts ...grpc.CallOption) (*GetUserLocationAuthResp, error)
	// 获取用户位置权限设置
	SetUserLocationAuth(ctx context.Context, in *SetUserLocationAuthReq, opts ...grpc.CallOption) (*SetUserLocationAuthResp, error)
	// 根据歌曲获取用户对应的称号
	GetUserMatchGlory(ctx context.Context, in *GetUserMatchGloryReq, opts ...grpc.CallOption) (*GetUserMatchGloryResp, error)
	GetUserMusicRankDialog(ctx context.Context, in *GetUserMusicRankDialogReq, opts ...grpc.CallOption) (*GetUserMusicRankDialogResp, error)
	UpdateUserPowerScore(ctx context.Context, in *UpdateUserPowerScoreReq, opts ...grpc.CallOption) (*UpdateUserPowerScoreResp, error)
	UpdateUserStarLevel(ctx context.Context, in *UpdateUserStarLevelReq, opts ...grpc.CallOption) (*UpdateUserStarLevelResp, error)
	UserMusicRankDialogConfirm(ctx context.Context, in *UserMusicRankDialogConfirmReq, opts ...grpc.CallOption) (*UserMusicRankDialogConfirmResp, error)
	UserMusicRankTrigger(ctx context.Context, in *UserMusicRankTriggerReq, opts ...grpc.CallOption) (*UserMusicRankTriggerResp, error)
	UserMusicRankGloryTrigger(ctx context.Context, in *UserMusicRankGloryTriggerReq, opts ...grpc.CallOption) (*UserMusicRankGloryTriggerResp, error)
	GetRatingForScore(ctx context.Context, in *GetRatingForScoreReq, opts ...grpc.CallOption) (*GetRatingForScoreResp, error)
	GetSeasonWebInfo(ctx context.Context, in *GetSeasonWebInfoReq, opts ...grpc.CallOption) (*GetSeasonWebInfoResp, error)
	GetUserSchoolInfo(ctx context.Context, in *GetUserSchoolInfoReq, opts ...grpc.CallOption) (*GetUserSchoolInfoResp, error)
	ListSchools(ctx context.Context, in *ListSchoolsReq, opts ...grpc.CallOption) (*ListSchoolsResp, error)
	BindSchool(ctx context.Context, in *BindSchoolReq, opts ...grpc.CallOption) (*BindSchoolResp, error)
	GetUserRankInSchool(ctx context.Context, in *GetUserRankInSchoolReq, opts ...grpc.CallOption) (*GetUserRankInSchoolResp, error)
	GetUserRankInCity(ctx context.Context, in *GetUserRankInCityReq, opts ...grpc.CallOption) (*GetUserRankInCityResp, error)
	GetSchoolRankInCity(ctx context.Context, in *GetSchoolRankInCityReq, opts ...grpc.CallOption) (*GetSchoolRankInCityResp, error)
	GetSchoolRank(ctx context.Context, in *GetSchoolRanReq, opts ...grpc.CallOption) (*GetSchoolRanResp, error)
	GetMirrorId(ctx context.Context, in *GetMirrorIdReq, opts ...grpc.CallOption) (*GetMirrorIdResp, error)
	GetMirrorData(ctx context.Context, in *GetMirrorDataReq, opts ...grpc.CallOption) (*GetMirrorDataResp, error)
	RebuildSchoolBind(ctx context.Context, in *RebuildSchoolBindReq, opts ...grpc.CallOption) (*RebuildSchoolBindResp, error)
	RebuildSchoolRank(ctx context.Context, in *RebuildSchoolRankReq, opts ...grpc.CallOption) (*RebuildSchoolRankResp, error)
	UpdateSchoolScore(ctx context.Context, in *UpdateSchoolScoreReq, opts ...grpc.CallOption) (*UpdateSchoolScoreResp, error)
	UpsertSchools(ctx context.Context, in *UpsertSchoolsReq, opts ...grpc.CallOption) (*UpsertSchoolsResp, error)
	UpsertCities(ctx context.Context, in *UpsertCitiesReq, opts ...grpc.CallOption) (*UpsertCitiesResp, error)
	DelSongs(ctx context.Context, in *DelSongsReq, opts ...grpc.CallOption) (*DelSongsResp, error)
	AddBlackUids(ctx context.Context, in *AddBlackUidsReq, opts ...grpc.CallOption) (*AddBlackUidsResp, error)
	RemoveBlackUids(ctx context.Context, in *RemoveBlackUidsReq, opts ...grpc.CallOption) (*RemoveBlackUidsResp, error)
	ListBlackUids(ctx context.Context, in *ListBlackUidsReq, opts ...grpc.CallOption) (*ListBlackUidsResp, error)
}

type userMusicRankClient struct {
	cc *grpc.ClientConn
}

func NewUserMusicRankClient(cc *grpc.ClientConn) UserMusicRankClient {
	return &userMusicRankClient{cc}
}

func (c *userMusicRankClient) GetMusicRankUserInfo(ctx context.Context, in *GetMusicRankUserInfoReq, opts ...grpc.CallOption) (*GetMusicRankUserInfoResp, error) {
	out := new(GetMusicRankUserInfoResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetMusicRankUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) BatchUserPowerInfo(ctx context.Context, in *BatchUserPowerInfoReq, opts ...grpc.CallOption) (*BatchUserPowerInfoResp, error) {
	out := new(BatchUserPowerInfoResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/BatchUserPowerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) BatchUserGlory(ctx context.Context, in *BatchUserGloryReq, opts ...grpc.CallOption) (*BatchUserGloryResp, error) {
	out := new(BatchUserGloryResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/BatchUserGlory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) ListUserGlories(ctx context.Context, in *ListUserGloriesReq, opts ...grpc.CallOption) (*ListUserGloriesResp, error) {
	out := new(ListUserGloriesResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/ListUserGlories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) SetUserGlory(ctx context.Context, in *SetUserGloryReq, opts ...grpc.CallOption) (*SetUserGloryResp, error) {
	out := new(SetUserGloryResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/SetUserGlory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) ListUserHistoryGlories(ctx context.Context, in *ListUserHistoryGloriesReq, opts ...grpc.CallOption) (*ListUserHistoryGloriesResp, error) {
	out := new(ListUserHistoryGloriesResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/ListUserHistoryGlories", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) SetUserLocation(ctx context.Context, in *SetUserLocationReq, opts ...grpc.CallOption) (*SetUserLocationResp, error) {
	out := new(SetUserLocationResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/SetUserLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) ListUserSingerScore(ctx context.Context, in *ListUserSingerScoreReq, opts ...grpc.CallOption) (*ListUserSingerScoreResp, error) {
	out := new(ListUserSingerScoreResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/ListUserSingerScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserSingerScoreDetail(ctx context.Context, in *GetUserSingerScoreDetailReq, opts ...grpc.CallOption) (*GetUserSingerScoreDetailResp, error) {
	out := new(GetUserSingerScoreDetailResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserSingerScoreDetail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) ListUserMusicRecords(ctx context.Context, in *ListUserMusicRecordsReq, opts ...grpc.CallOption) (*ListUserMusicRecordsResp, error) {
	out := new(ListUserMusicRecordsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/ListUserMusicRecords", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetStarRank(ctx context.Context, in *GetStarRankReq, opts ...grpc.CallOption) (*GetStarRankResp, error) {
	out := new(GetStarRankResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetStarRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetSingerScoreRank(ctx context.Context, in *GetSingerScoreRankReq, opts ...grpc.CallOption) (*GetSingerScoreRankResp, error) {
	out := new(GetSingerScoreRankResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetSingerScoreRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) IsSongsInRank(ctx context.Context, in *IsSongsInRankReq, opts ...grpc.CallOption) (*IsSongsInRankResp, error) {
	out := new(IsSongsInRankResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/IsSongsInRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserLocation(ctx context.Context, in *GetUserLocationReq, opts ...grpc.CallOption) (*GetUserLocationResp, error) {
	out := new(GetUserLocationResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserLocationAuth(ctx context.Context, in *GetUserLocationAuthReq, opts ...grpc.CallOption) (*GetUserLocationAuthResp, error) {
	out := new(GetUserLocationAuthResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserLocationAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) SetUserLocationAuth(ctx context.Context, in *SetUserLocationAuthReq, opts ...grpc.CallOption) (*SetUserLocationAuthResp, error) {
	out := new(SetUserLocationAuthResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/SetUserLocationAuth", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserMatchGlory(ctx context.Context, in *GetUserMatchGloryReq, opts ...grpc.CallOption) (*GetUserMatchGloryResp, error) {
	out := new(GetUserMatchGloryResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserMatchGlory", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserMusicRankDialog(ctx context.Context, in *GetUserMusicRankDialogReq, opts ...grpc.CallOption) (*GetUserMusicRankDialogResp, error) {
	out := new(GetUserMusicRankDialogResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserMusicRankDialog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UpdateUserPowerScore(ctx context.Context, in *UpdateUserPowerScoreReq, opts ...grpc.CallOption) (*UpdateUserPowerScoreResp, error) {
	out := new(UpdateUserPowerScoreResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UpdateUserPowerScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UpdateUserStarLevel(ctx context.Context, in *UpdateUserStarLevelReq, opts ...grpc.CallOption) (*UpdateUserStarLevelResp, error) {
	out := new(UpdateUserStarLevelResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UpdateUserStarLevel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UserMusicRankDialogConfirm(ctx context.Context, in *UserMusicRankDialogConfirmReq, opts ...grpc.CallOption) (*UserMusicRankDialogConfirmResp, error) {
	out := new(UserMusicRankDialogConfirmResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UserMusicRankDialogConfirm", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UserMusicRankTrigger(ctx context.Context, in *UserMusicRankTriggerReq, opts ...grpc.CallOption) (*UserMusicRankTriggerResp, error) {
	out := new(UserMusicRankTriggerResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UserMusicRankTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UserMusicRankGloryTrigger(ctx context.Context, in *UserMusicRankGloryTriggerReq, opts ...grpc.CallOption) (*UserMusicRankGloryTriggerResp, error) {
	out := new(UserMusicRankGloryTriggerResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UserMusicRankGloryTrigger", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetRatingForScore(ctx context.Context, in *GetRatingForScoreReq, opts ...grpc.CallOption) (*GetRatingForScoreResp, error) {
	out := new(GetRatingForScoreResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetRatingForScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetSeasonWebInfo(ctx context.Context, in *GetSeasonWebInfoReq, opts ...grpc.CallOption) (*GetSeasonWebInfoResp, error) {
	out := new(GetSeasonWebInfoResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetSeasonWebInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserSchoolInfo(ctx context.Context, in *GetUserSchoolInfoReq, opts ...grpc.CallOption) (*GetUserSchoolInfoResp, error) {
	out := new(GetUserSchoolInfoResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserSchoolInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) ListSchools(ctx context.Context, in *ListSchoolsReq, opts ...grpc.CallOption) (*ListSchoolsResp, error) {
	out := new(ListSchoolsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/ListSchools", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) BindSchool(ctx context.Context, in *BindSchoolReq, opts ...grpc.CallOption) (*BindSchoolResp, error) {
	out := new(BindSchoolResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/BindSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserRankInSchool(ctx context.Context, in *GetUserRankInSchoolReq, opts ...grpc.CallOption) (*GetUserRankInSchoolResp, error) {
	out := new(GetUserRankInSchoolResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserRankInSchool", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetUserRankInCity(ctx context.Context, in *GetUserRankInCityReq, opts ...grpc.CallOption) (*GetUserRankInCityResp, error) {
	out := new(GetUserRankInCityResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetUserRankInCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetSchoolRankInCity(ctx context.Context, in *GetSchoolRankInCityReq, opts ...grpc.CallOption) (*GetSchoolRankInCityResp, error) {
	out := new(GetSchoolRankInCityResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetSchoolRankInCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetSchoolRank(ctx context.Context, in *GetSchoolRanReq, opts ...grpc.CallOption) (*GetSchoolRanResp, error) {
	out := new(GetSchoolRanResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetSchoolRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetMirrorId(ctx context.Context, in *GetMirrorIdReq, opts ...grpc.CallOption) (*GetMirrorIdResp, error) {
	out := new(GetMirrorIdResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetMirrorId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) GetMirrorData(ctx context.Context, in *GetMirrorDataReq, opts ...grpc.CallOption) (*GetMirrorDataResp, error) {
	out := new(GetMirrorDataResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/GetMirrorData", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) RebuildSchoolBind(ctx context.Context, in *RebuildSchoolBindReq, opts ...grpc.CallOption) (*RebuildSchoolBindResp, error) {
	out := new(RebuildSchoolBindResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/RebuildSchoolBind", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) RebuildSchoolRank(ctx context.Context, in *RebuildSchoolRankReq, opts ...grpc.CallOption) (*RebuildSchoolRankResp, error) {
	out := new(RebuildSchoolRankResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/RebuildSchoolRank", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UpdateSchoolScore(ctx context.Context, in *UpdateSchoolScoreReq, opts ...grpc.CallOption) (*UpdateSchoolScoreResp, error) {
	out := new(UpdateSchoolScoreResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UpdateSchoolScore", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UpsertSchools(ctx context.Context, in *UpsertSchoolsReq, opts ...grpc.CallOption) (*UpsertSchoolsResp, error) {
	out := new(UpsertSchoolsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UpsertSchools", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) UpsertCities(ctx context.Context, in *UpsertCitiesReq, opts ...grpc.CallOption) (*UpsertCitiesResp, error) {
	out := new(UpsertCitiesResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/UpsertCities", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) DelSongs(ctx context.Context, in *DelSongsReq, opts ...grpc.CallOption) (*DelSongsResp, error) {
	out := new(DelSongsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/DelSongs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) AddBlackUids(ctx context.Context, in *AddBlackUidsReq, opts ...grpc.CallOption) (*AddBlackUidsResp, error) {
	out := new(AddBlackUidsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/AddBlackUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) RemoveBlackUids(ctx context.Context, in *RemoveBlackUidsReq, opts ...grpc.CallOption) (*RemoveBlackUidsResp, error) {
	out := new(RemoveBlackUidsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/RemoveBlackUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userMusicRankClient) ListBlackUids(ctx context.Context, in *ListBlackUidsReq, opts ...grpc.CallOption) (*ListBlackUidsResp, error) {
	out := new(ListBlackUidsResp)
	err := c.cc.Invoke(ctx, "/user_music_rank.UserMusicRank/ListBlackUids", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserMusicRankServer is the server API for UserMusicRank service.
type UserMusicRankServer interface {
	// 批量获取用户段位称号
	GetMusicRankUserInfo(context.Context, *GetMusicRankUserInfoReq) (*GetMusicRankUserInfoResp, error)
	// 批量获取用户实力信息
	BatchUserPowerInfo(context.Context, *BatchUserPowerInfoReq) (*BatchUserPowerInfoResp, error)
	// 批量获取用户当前称号
	BatchUserGlory(context.Context, *BatchUserGloryReq) (*BatchUserGloryResp, error)
	// 获取用户当前称号列表
	ListUserGlories(context.Context, *ListUserGloriesReq) (*ListUserGloriesResp, error)
	// 设置用户当前外显称号
	SetUserGlory(context.Context, *SetUserGloryReq) (*SetUserGloryResp, error)
	// 获取用户历史称号列表
	ListUserHistoryGlories(context.Context, *ListUserHistoryGloriesReq) (*ListUserHistoryGloriesResp, error)
	// 设置用户当前位置
	SetUserLocation(context.Context, *SetUserLocationReq) (*SetUserLocationResp, error)
	// 获取用户歌手唱力榜
	ListUserSingerScore(context.Context, *ListUserSingerScoreReq) (*ListUserSingerScoreResp, error)
	// 获取用户歌手唱力详情
	GetUserSingerScoreDetail(context.Context, *GetUserSingerScoreDetailReq) (*GetUserSingerScoreDetailResp, error)
	// 获取用户歌曲记录
	ListUserMusicRecords(context.Context, *ListUserMusicRecordsReq) (*ListUserMusicRecordsResp, error)
	// 获取段位排行榜
	GetStarRank(context.Context, *GetStarRankReq) (*GetStarRankResp, error)
	// 获取歌手唱力排行榜
	GetSingerScoreRank(context.Context, *GetSingerScoreRankReq) (*GetSingerScoreRankResp, error)
	// 歌曲是否在在排行榜歌曲中
	IsSongsInRank(context.Context, *IsSongsInRankReq) (*IsSongsInRankResp, error)
	// 获取用户位置
	GetUserLocation(context.Context, *GetUserLocationReq) (*GetUserLocationResp, error)
	// 获取用户位置权限设置
	GetUserLocationAuth(context.Context, *GetUserLocationAuthReq) (*GetUserLocationAuthResp, error)
	// 获取用户位置权限设置
	SetUserLocationAuth(context.Context, *SetUserLocationAuthReq) (*SetUserLocationAuthResp, error)
	// 根据歌曲获取用户对应的称号
	GetUserMatchGlory(context.Context, *GetUserMatchGloryReq) (*GetUserMatchGloryResp, error)
	GetUserMusicRankDialog(context.Context, *GetUserMusicRankDialogReq) (*GetUserMusicRankDialogResp, error)
	UpdateUserPowerScore(context.Context, *UpdateUserPowerScoreReq) (*UpdateUserPowerScoreResp, error)
	UpdateUserStarLevel(context.Context, *UpdateUserStarLevelReq) (*UpdateUserStarLevelResp, error)
	UserMusicRankDialogConfirm(context.Context, *UserMusicRankDialogConfirmReq) (*UserMusicRankDialogConfirmResp, error)
	UserMusicRankTrigger(context.Context, *UserMusicRankTriggerReq) (*UserMusicRankTriggerResp, error)
	UserMusicRankGloryTrigger(context.Context, *UserMusicRankGloryTriggerReq) (*UserMusicRankGloryTriggerResp, error)
	GetRatingForScore(context.Context, *GetRatingForScoreReq) (*GetRatingForScoreResp, error)
	GetSeasonWebInfo(context.Context, *GetSeasonWebInfoReq) (*GetSeasonWebInfoResp, error)
	GetUserSchoolInfo(context.Context, *GetUserSchoolInfoReq) (*GetUserSchoolInfoResp, error)
	ListSchools(context.Context, *ListSchoolsReq) (*ListSchoolsResp, error)
	BindSchool(context.Context, *BindSchoolReq) (*BindSchoolResp, error)
	GetUserRankInSchool(context.Context, *GetUserRankInSchoolReq) (*GetUserRankInSchoolResp, error)
	GetUserRankInCity(context.Context, *GetUserRankInCityReq) (*GetUserRankInCityResp, error)
	GetSchoolRankInCity(context.Context, *GetSchoolRankInCityReq) (*GetSchoolRankInCityResp, error)
	GetSchoolRank(context.Context, *GetSchoolRanReq) (*GetSchoolRanResp, error)
	GetMirrorId(context.Context, *GetMirrorIdReq) (*GetMirrorIdResp, error)
	GetMirrorData(context.Context, *GetMirrorDataReq) (*GetMirrorDataResp, error)
	RebuildSchoolBind(context.Context, *RebuildSchoolBindReq) (*RebuildSchoolBindResp, error)
	RebuildSchoolRank(context.Context, *RebuildSchoolRankReq) (*RebuildSchoolRankResp, error)
	UpdateSchoolScore(context.Context, *UpdateSchoolScoreReq) (*UpdateSchoolScoreResp, error)
	UpsertSchools(context.Context, *UpsertSchoolsReq) (*UpsertSchoolsResp, error)
	UpsertCities(context.Context, *UpsertCitiesReq) (*UpsertCitiesResp, error)
	DelSongs(context.Context, *DelSongsReq) (*DelSongsResp, error)
	AddBlackUids(context.Context, *AddBlackUidsReq) (*AddBlackUidsResp, error)
	RemoveBlackUids(context.Context, *RemoveBlackUidsReq) (*RemoveBlackUidsResp, error)
	ListBlackUids(context.Context, *ListBlackUidsReq) (*ListBlackUidsResp, error)
}

func RegisterUserMusicRankServer(s *grpc.Server, srv UserMusicRankServer) {
	s.RegisterService(&_UserMusicRank_serviceDesc, srv)
}

func _UserMusicRank_GetMusicRankUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMusicRankUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetMusicRankUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetMusicRankUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetMusicRankUserInfo(ctx, req.(*GetMusicRankUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_BatchUserPowerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUserPowerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).BatchUserPowerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/BatchUserPowerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).BatchUserPowerInfo(ctx, req.(*BatchUserPowerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_BatchUserGlory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchUserGloryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).BatchUserGlory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/BatchUserGlory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).BatchUserGlory(ctx, req.(*BatchUserGloryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_ListUserGlories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserGloriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).ListUserGlories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/ListUserGlories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).ListUserGlories(ctx, req.(*ListUserGloriesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_SetUserGlory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserGloryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).SetUserGlory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/SetUserGlory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).SetUserGlory(ctx, req.(*SetUserGloryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_ListUserHistoryGlories_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserHistoryGloriesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).ListUserHistoryGlories(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/ListUserHistoryGlories",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).ListUserHistoryGlories(ctx, req.(*ListUserHistoryGloriesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_SetUserLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserLocationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).SetUserLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/SetUserLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).SetUserLocation(ctx, req.(*SetUserLocationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_ListUserSingerScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserSingerScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).ListUserSingerScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/ListUserSingerScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).ListUserSingerScore(ctx, req.(*ListUserSingerScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserSingerScoreDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSingerScoreDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserSingerScoreDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserSingerScoreDetail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserSingerScoreDetail(ctx, req.(*GetUserSingerScoreDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_ListUserMusicRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListUserMusicRecordsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).ListUserMusicRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/ListUserMusicRecords",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).ListUserMusicRecords(ctx, req.(*ListUserMusicRecordsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetStarRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStarRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetStarRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetStarRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetStarRank(ctx, req.(*GetStarRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetSingerScoreRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSingerScoreRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetSingerScoreRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetSingerScoreRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetSingerScoreRank(ctx, req.(*GetSingerScoreRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_IsSongsInRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsSongsInRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).IsSongsInRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/IsSongsInRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).IsSongsInRank(ctx, req.(*IsSongsInRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLocationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserLocation(ctx, req.(*GetUserLocationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserLocationAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLocationAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserLocationAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserLocationAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserLocationAuth(ctx, req.(*GetUserLocationAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_SetUserLocationAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserLocationAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).SetUserLocationAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/SetUserLocationAuth",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).SetUserLocationAuth(ctx, req.(*SetUserLocationAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserMatchGlory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMatchGloryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserMatchGlory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserMatchGlory",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserMatchGlory(ctx, req.(*GetUserMatchGloryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserMusicRankDialog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserMusicRankDialogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserMusicRankDialog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserMusicRankDialog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserMusicRankDialog(ctx, req.(*GetUserMusicRankDialogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UpdateUserPowerScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserPowerScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UpdateUserPowerScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UpdateUserPowerScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UpdateUserPowerScore(ctx, req.(*UpdateUserPowerScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UpdateUserStarLevel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserStarLevelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UpdateUserStarLevel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UpdateUserStarLevel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UpdateUserStarLevel(ctx, req.(*UpdateUserStarLevelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UserMusicRankDialogConfirm_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserMusicRankDialogConfirmReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UserMusicRankDialogConfirm(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UserMusicRankDialogConfirm",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UserMusicRankDialogConfirm(ctx, req.(*UserMusicRankDialogConfirmReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UserMusicRankTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserMusicRankTriggerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UserMusicRankTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UserMusicRankTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UserMusicRankTrigger(ctx, req.(*UserMusicRankTriggerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UserMusicRankGloryTrigger_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserMusicRankGloryTriggerReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UserMusicRankGloryTrigger(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UserMusicRankGloryTrigger",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UserMusicRankGloryTrigger(ctx, req.(*UserMusicRankGloryTriggerReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetRatingForScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRatingForScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetRatingForScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetRatingForScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetRatingForScore(ctx, req.(*GetRatingForScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetSeasonWebInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSeasonWebInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetSeasonWebInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetSeasonWebInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetSeasonWebInfo(ctx, req.(*GetSeasonWebInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserSchoolInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSchoolInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserSchoolInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserSchoolInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserSchoolInfo(ctx, req.(*GetUserSchoolInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_ListSchools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSchoolsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).ListSchools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/ListSchools",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).ListSchools(ctx, req.(*ListSchoolsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_BindSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).BindSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/BindSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).BindSchool(ctx, req.(*BindSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserRankInSchool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRankInSchoolReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserRankInSchool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserRankInSchool",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserRankInSchool(ctx, req.(*GetUserRankInSchoolReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetUserRankInCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRankInCityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetUserRankInCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetUserRankInCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetUserRankInCity(ctx, req.(*GetUserRankInCityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetSchoolRankInCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchoolRankInCityReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetSchoolRankInCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetSchoolRankInCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetSchoolRankInCity(ctx, req.(*GetSchoolRankInCityReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetSchoolRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSchoolRanReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetSchoolRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetSchoolRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetSchoolRank(ctx, req.(*GetSchoolRanReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetMirrorId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMirrorIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetMirrorId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetMirrorId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetMirrorId(ctx, req.(*GetMirrorIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_GetMirrorData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMirrorDataReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).GetMirrorData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/GetMirrorData",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).GetMirrorData(ctx, req.(*GetMirrorDataReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_RebuildSchoolBind_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RebuildSchoolBindReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).RebuildSchoolBind(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/RebuildSchoolBind",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).RebuildSchoolBind(ctx, req.(*RebuildSchoolBindReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_RebuildSchoolRank_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RebuildSchoolRankReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).RebuildSchoolRank(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/RebuildSchoolRank",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).RebuildSchoolRank(ctx, req.(*RebuildSchoolRankReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UpdateSchoolScore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSchoolScoreReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UpdateSchoolScore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UpdateSchoolScore",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UpdateSchoolScore(ctx, req.(*UpdateSchoolScoreReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UpsertSchools_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertSchoolsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UpsertSchools(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UpsertSchools",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UpsertSchools(ctx, req.(*UpsertSchoolsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_UpsertCities_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertCitiesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).UpsertCities(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/UpsertCities",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).UpsertCities(ctx, req.(*UpsertCitiesReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_DelSongs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelSongsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).DelSongs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/DelSongs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).DelSongs(ctx, req.(*DelSongsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_AddBlackUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBlackUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).AddBlackUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/AddBlackUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).AddBlackUids(ctx, req.(*AddBlackUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_RemoveBlackUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveBlackUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).RemoveBlackUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/RemoveBlackUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).RemoveBlackUids(ctx, req.(*RemoveBlackUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserMusicRank_ListBlackUids_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBlackUidsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserMusicRankServer).ListBlackUids(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_music_rank.UserMusicRank/ListBlackUids",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserMusicRankServer).ListBlackUids(ctx, req.(*ListBlackUidsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserMusicRank_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_music_rank.UserMusicRank",
	HandlerType: (*UserMusicRankServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetMusicRankUserInfo",
			Handler:    _UserMusicRank_GetMusicRankUserInfo_Handler,
		},
		{
			MethodName: "BatchUserPowerInfo",
			Handler:    _UserMusicRank_BatchUserPowerInfo_Handler,
		},
		{
			MethodName: "BatchUserGlory",
			Handler:    _UserMusicRank_BatchUserGlory_Handler,
		},
		{
			MethodName: "ListUserGlories",
			Handler:    _UserMusicRank_ListUserGlories_Handler,
		},
		{
			MethodName: "SetUserGlory",
			Handler:    _UserMusicRank_SetUserGlory_Handler,
		},
		{
			MethodName: "ListUserHistoryGlories",
			Handler:    _UserMusicRank_ListUserHistoryGlories_Handler,
		},
		{
			MethodName: "SetUserLocation",
			Handler:    _UserMusicRank_SetUserLocation_Handler,
		},
		{
			MethodName: "ListUserSingerScore",
			Handler:    _UserMusicRank_ListUserSingerScore_Handler,
		},
		{
			MethodName: "GetUserSingerScoreDetail",
			Handler:    _UserMusicRank_GetUserSingerScoreDetail_Handler,
		},
		{
			MethodName: "ListUserMusicRecords",
			Handler:    _UserMusicRank_ListUserMusicRecords_Handler,
		},
		{
			MethodName: "GetStarRank",
			Handler:    _UserMusicRank_GetStarRank_Handler,
		},
		{
			MethodName: "GetSingerScoreRank",
			Handler:    _UserMusicRank_GetSingerScoreRank_Handler,
		},
		{
			MethodName: "IsSongsInRank",
			Handler:    _UserMusicRank_IsSongsInRank_Handler,
		},
		{
			MethodName: "GetUserLocation",
			Handler:    _UserMusicRank_GetUserLocation_Handler,
		},
		{
			MethodName: "GetUserLocationAuth",
			Handler:    _UserMusicRank_GetUserLocationAuth_Handler,
		},
		{
			MethodName: "SetUserLocationAuth",
			Handler:    _UserMusicRank_SetUserLocationAuth_Handler,
		},
		{
			MethodName: "GetUserMatchGlory",
			Handler:    _UserMusicRank_GetUserMatchGlory_Handler,
		},
		{
			MethodName: "GetUserMusicRankDialog",
			Handler:    _UserMusicRank_GetUserMusicRankDialog_Handler,
		},
		{
			MethodName: "UpdateUserPowerScore",
			Handler:    _UserMusicRank_UpdateUserPowerScore_Handler,
		},
		{
			MethodName: "UpdateUserStarLevel",
			Handler:    _UserMusicRank_UpdateUserStarLevel_Handler,
		},
		{
			MethodName: "UserMusicRankDialogConfirm",
			Handler:    _UserMusicRank_UserMusicRankDialogConfirm_Handler,
		},
		{
			MethodName: "UserMusicRankTrigger",
			Handler:    _UserMusicRank_UserMusicRankTrigger_Handler,
		},
		{
			MethodName: "UserMusicRankGloryTrigger",
			Handler:    _UserMusicRank_UserMusicRankGloryTrigger_Handler,
		},
		{
			MethodName: "GetRatingForScore",
			Handler:    _UserMusicRank_GetRatingForScore_Handler,
		},
		{
			MethodName: "GetSeasonWebInfo",
			Handler:    _UserMusicRank_GetSeasonWebInfo_Handler,
		},
		{
			MethodName: "GetUserSchoolInfo",
			Handler:    _UserMusicRank_GetUserSchoolInfo_Handler,
		},
		{
			MethodName: "ListSchools",
			Handler:    _UserMusicRank_ListSchools_Handler,
		},
		{
			MethodName: "BindSchool",
			Handler:    _UserMusicRank_BindSchool_Handler,
		},
		{
			MethodName: "GetUserRankInSchool",
			Handler:    _UserMusicRank_GetUserRankInSchool_Handler,
		},
		{
			MethodName: "GetUserRankInCity",
			Handler:    _UserMusicRank_GetUserRankInCity_Handler,
		},
		{
			MethodName: "GetSchoolRankInCity",
			Handler:    _UserMusicRank_GetSchoolRankInCity_Handler,
		},
		{
			MethodName: "GetSchoolRank",
			Handler:    _UserMusicRank_GetSchoolRank_Handler,
		},
		{
			MethodName: "GetMirrorId",
			Handler:    _UserMusicRank_GetMirrorId_Handler,
		},
		{
			MethodName: "GetMirrorData",
			Handler:    _UserMusicRank_GetMirrorData_Handler,
		},
		{
			MethodName: "RebuildSchoolBind",
			Handler:    _UserMusicRank_RebuildSchoolBind_Handler,
		},
		{
			MethodName: "RebuildSchoolRank",
			Handler:    _UserMusicRank_RebuildSchoolRank_Handler,
		},
		{
			MethodName: "UpdateSchoolScore",
			Handler:    _UserMusicRank_UpdateSchoolScore_Handler,
		},
		{
			MethodName: "UpsertSchools",
			Handler:    _UserMusicRank_UpsertSchools_Handler,
		},
		{
			MethodName: "UpsertCities",
			Handler:    _UserMusicRank_UpsertCities_Handler,
		},
		{
			MethodName: "DelSongs",
			Handler:    _UserMusicRank_DelSongs_Handler,
		},
		{
			MethodName: "AddBlackUids",
			Handler:    _UserMusicRank_AddBlackUids_Handler,
		},
		{
			MethodName: "RemoveBlackUids",
			Handler:    _UserMusicRank_RemoveBlackUids_Handler,
		},
		{
			MethodName: "ListBlackUids",
			Handler:    _UserMusicRank_ListBlackUids_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-music-rank/user-music-rank.proto",
}

func init() {
	proto.RegisterFile("user-music-rank/user-music-rank.proto", fileDescriptor_user_music_rank_b8b8c83fb86d4d3b)
}

var fileDescriptor_user_music_rank_b8b8c83fb86d4d3b = []byte{
	// 4267 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3c, 0x4d, 0x8f, 0x1c, 0x49,
	0x56, 0x93, 0xf5, 0x5d, 0xaf, 0xba, 0xba, 0xab, 0x63, 0x6c, 0x77, 0x77, 0x7a, 0x3c, 0x6e, 0xe7,
	0x60, 0xbb, 0xd6, 0xde, 0x2e, 0x63, 0xcf, 0xec, 0x6a, 0x76, 0x96, 0x19, 0xe4, 0x6e, 0x7b, 0x7a,
	0x0b, 0xb9, 0xbd, 0x9e, 0x2c, 0x7b, 0x11, 0x0c, 0x50, 0xca, 0xce, 0x4c, 0x57, 0xa7, 0x5c, 0x95,
	0x99, 0x9d, 0x91, 0xe5, 0xd9, 0x46, 0xc3, 0x15, 0x4e, 0x48, 0x08, 0x21, 0xb1, 0xe2, 0x82, 0x90,
	0x90, 0x90, 0x38, 0x21, 0xcd, 0x89, 0x03, 0x5a, 0x4e, 0xfb, 0x03, 0x10, 0x07, 0xc4, 0x81, 0x03,
	0x5c, 0xb8, 0x01, 0xda, 0x03, 0x1c, 0x51, 0x7c, 0x65, 0x46, 0x66, 0x46, 0x56, 0x67, 0x7b, 0xc4,
	0x88, 0x3d, 0x75, 0xe5, 0x8b, 0x17, 0x2f, 0x22, 0x5e, 0xbc, 0x78, 0x9f, 0x11, 0x0d, 0x37, 0x97,
	0xd8, 0x8d, 0xf6, 0x16, 0x4b, 0xec, 0xd9, 0x7b, 0x91, 0xe5, 0xbf, 0xba, 0x97, 0xfb, 0x1e, 0x85,
	0x51, 0x10, 0x07, 0x68, 0x83, 0x80, 0xa7, 0x14, 0x3c, 0x25, 0x60, 0xe3, 0x26, 0x6c, 0x3c, 0x74,
	0x9c, 0xfd, 0xb9, 0x65, 0xbf, 0x7a, 0xe1, 0x39, 0xd8, 0x74, 0x4f, 0x11, 0x82, 0xc6, 0xd2, 0x73,
	0xf0, 0xb6, 0xb6, 0x5b, 0x1f, 0xf6, 0x4d, 0xfa, 0xdb, 0x40, 0x30, 0xc8, 0xa2, 0xe1, 0xd0, 0x18,
	0x02, 0x32, 0xdd, 0x45, 0xf0, 0xda, 0x3d, 0xb7, 0xf7, 0x65, 0x78, 0xbb, 0x80, 0x89, 0x43, 0x42,
	0xf4, 0x89, 0x87, 0x63, 0xb9, 0xbb, 0xf1, 0xa7, 0x1a, 0x6c, 0xe6, 0x80, 0x38, 0x44, 0x9f, 0x42,
	0x3b, 0x72, 0xed, 0x20, 0xe2, 0x74, 0x7b, 0x0f, 0xbe, 0x3d, 0xca, 0x2d, 0x64, 0x54, 0xe8, 0x34,
	0x7a, 0xe1, 0x39, 0x26, 0xed, 0x64, 0x8a, 0xce, 0xfa, 0x47, 0xd0, 0x4d, 0xa0, 0x68, 0x00, 0xf5,
	0xa5, 0xe7, 0x6c, 0x6b, 0xbb, 0xda, 0xb0, 0x6f, 0x92, 0x9f, 0xe8, 0x2a, 0x74, 0xed, 0xc8, 0xb5,
	0x62, 0x77, 0x6a, 0xc5, 0xdb, 0xb5, 0x5d, 0x6d, 0x58, 0x37, 0x3b, 0x0c, 0xf0, 0x30, 0x36, 0x86,
	0xd0, 0x7b, 0xe4, 0xce, 0x27, 0x81, 0x3f, 0xa3, 0xeb, 0xdc, 0x81, 0x0e, 0x0e, 0xfc, 0xd9, 0x54,
	0xac, 0xb5, 0x6b, 0xb6, 0xc9, 0xf7, 0xd8, 0xc1, 0xc6, 0x3a, 0xac, 0xa5, 0x98, 0x38, 0x34, 0xbe,
	0x84, 0x8d, 0x17, 0x21, 0x76, 0xa3, 0xf8, 0xc0, 0x8b, 0x3d, 0x97, 0xf6, 0xfe, 0x18, 0x5a, 0x36,
	0xfd, 0xe0, 0xeb, 0xb9, 0x59, 0x58, 0x4f, 0xae, 0xc7, 0xe8, 0xc0, 0x8b, 0xcf, 0x4c, 0xde, 0x49,
	0xbf, 0x03, 0x0d, 0xf2, 0x8d, 0xd6, 0xa1, 0xc6, 0x57, 0xd0, 0x35, 0x6b, 0x9e, 0x43, 0x98, 0xef,
	0x5b, 0x0b, 0x97, 0xce, 0xbd, 0x6b, 0xd2, 0xdf, 0x84, 0xcb, 0x59, 0x5a, 0x38, 0x34, 0xfe, 0x49,
	0x13, 0xc0, 0x89, 0x7d, 0x12, 0x04, 0x73, 0x3a, 0xa7, 0x4f, 0xa1, 0x8d, 0xd9, 0x57, 0x29, 0x93,
	0xf3, 0x7d, 0x46, 0xec, 0xe7, 0xd8, 0x7f, 0x19, 0x98, 0xa2, 0xb3, 0xfe, 0x25, 0x40, 0x0a, 0xae,
	0x32, 0x45, 0xb2, 0x13, 0xde, 0x62, 0xb6, 0x5d, 0xa7, 0x20, 0xf2, 0x13, 0x5d, 0x87, 0x9e, 0xe7,
	0xc7, 0x6e, 0xe4, 0x5b, 0xf3, 0xa9, 0xe7, 0x6c, 0x37, 0x68, 0x0b, 0x08, 0xd0, 0xd8, 0x41, 0x5b,
	0xd0, 0xb6, 0xbd, 0xf8, 0x8c, 0x34, 0x36, 0x69, 0x23, 0x61, 0xcd, 0xd9, 0xd8, 0x31, 0xde, 0x86,
	0xcd, 0xdc, 0x2c, 0x71, 0x68, 0x7c, 0x02, 0x97, 0x5e, 0x84, 0x8e, 0x15, 0xbb, 0x0c, 0x38, 0xb1,
	0x83, 0xc8, 0x25, 0x4b, 0x2e, 0x8a, 0xc0, 0x25, 0x68, 0x62, 0xd2, 0xca, 0xb7, 0x9f, 0x7d, 0x18,
	0x5b, 0x70, 0x59, 0xd1, 0x1f, 0x87, 0xc6, 0x3d, 0x18, 0x1c, 0xba, 0xf1, 0x91, 0x17, 0x45, 0x41,
	0xf4, 0xc8, 0x8a, 0x2d, 0x42, 0xf4, 0x2a, 0x74, 0x17, 0x14, 0x30, 0x4d, 0x16, 0xde, 0x61, 0x80,
	0xb1, 0x63, 0xfc, 0x7b, 0x1b, 0x36, 0x73, 0x3d, 0x70, 0x88, 0xbe, 0x0f, 0x0d, 0xc2, 0x6a, 0x8a,
	0xdd, 0x7b, 0x70, 0xbb, 0xc0, 0xf7, 0x42, 0x8f, 0xd1, 0x0b, 0xec, 0x46, 0x26, 0xed, 0x84, 0x0e,
	0xd3, 0x7d, 0xab, 0xd1, 0x7d, 0xdb, 0xab, 0xd0, 0x5f, 0xb1, 0x71, 0xe8, 0x31, 0x34, 0x49, 0x47,
	0xbc, 0x5d, 0xa7, 0x64, 0xee, 0x55, 0x9d, 0x86, 0xe5, 0xbf, 0xa2, 0x84, 0x58, 0x6f, 0xf4, 0x14,
	0x80, 0x6e, 0x0d, 0xa3, 0xd5, 0x78, 0x33, 0x5a, 0x5d, 0x42, 0xe2, 0x05, 0xa5, 0xf7, 0x0c, 0xd6,
	0x28, 0x3d, 0xb1, 0xc8, 0xe6, 0x9b, 0x2c, 0xb2, 0x47, 0x48, 0x70, 0x91, 0xd0, 0xff, 0xa6, 0x06,
	0x0d, 0x42, 0x9b, 0x6c, 0x15, 0xa5, 0x42, 0x25, 0x92, 0x6f, 0x15, 0x01, 0x3c, 0x25, 0x52, 0x79,
	0x1d, 0x7a, 0xb4, 0xd1, 0x7a, 0x6d, 0xc5, 0x56, 0xc4, 0x05, 0x16, 0x08, 0xe8, 0x21, 0x85, 0x90,
	0xde, 0x27, 0x41, 0x3c, 0x65, 0xf2, 0x52, 0x67, 0xea, 0xe2, 0x24, 0x88, 0xa9, 0x74, 0x90, 0x46,
	0x36, 0xe1, 0x54, 0x7e, 0x3b, 0x0c, 0x30, 0x76, 0x08, 0x69, 0xde, 0x48, 0x47, 0x66, 0x12, 0x0c,
	0x0c, 0x44, 0xc7, 0x16, 0x13, 0x23, 0x0b, 0xdb, 0x6e, 0xed, 0x6a, 0xc3, 0x26, 0x9b, 0x18, 0xe1,
	0x8f, 0xd4, 0x9b, 0x36, 0xb7, 0x69, 0x33, 0xef, 0x4d, 0x11, 0xa4, 0xc3, 0xd1, 0x91, 0x0f, 0x07,
	0x55, 0x70, 0xa4, 0x81, 0x8e, 0xda, 0x65, 0x93, 0x22, 0x00, 0x31, 0x26, 0x6d, 0xa4, 0x44, 0x81,
	0x8d, 0x49, 0x00, 0x84, 0xa4, 0xfe, 0x57, 0x1a, 0xac, 0xc9, 0x1b, 0xf4, 0xff, 0x96, 0x75, 0xfa,
	0xef, 0x6b, 0x19, 0xfd, 0x93, 0x19, 0x49, 0x5b, 0x35, 0x52, 0x6d, 0xf5, 0x48, 0xf5, 0xc2, 0x26,
	0x5d, 0x03, 0x10, 0xbd, 0x17, 0x33, 0x3e, 0x51, 0x4e, 0x6f, 0xbc, 0x98, 0x19, 0x06, 0xac, 0x27,
	0x22, 0x39, 0x76, 0x94, 0xea, 0xc6, 0x18, 0xc1, 0x46, 0x06, 0x07, 0x87, 0xab, 0xd5, 0xc7, 0xfb,
	0x70, 0xc9, 0x74, 0x8f, 0x97, 0xde, 0xdc, 0x61, 0x4b, 0xdc, 0xf7, 0x7c, 0x87, 0xeb, 0x9c, 0x74,
	0x21, 0x5a, 0x76, 0x21, 0x44, 0x7b, 0x29, 0x3a, 0xe1, 0xd0, 0xb8, 0x92, 0xa3, 0x46, 0x36, 0x97,
	0x18, 0xe1, 0x7c, 0x07, 0x06, 0xc7, 0xa1, 0xf1, 0x1e, 0x9d, 0x6e, 0x02, 0x54, 0xaf, 0xe9, 0x2b,
	0x8d, 0x2a, 0x45, 0x09, 0x0b, 0x87, 0x68, 0x3f, 0x6f, 0x5c, 0x86, 0xaa, 0xf3, 0x9b, 0xe9, 0xc3,
	0x8f, 0x6f, 0x6a, 0x58, 0x3e, 0x87, 0x16, 0x03, 0xbd, 0xa1, 0x51, 0xc9, 0x88, 0x42, 0x23, 0x2b,
	0x0a, 0xc6, 0x1d, 0xb8, 0x72, 0xe8, 0xc6, 0xa9, 0x88, 0xf3, 0xc1, 0xd5, 0x2b, 0xac, 0xc1, 0x96,
	0x12, 0x19, 0x87, 0xe8, 0x40, 0x28, 0x51, 0xad, 0x5c, 0x4d, 0xa9, 0x3a, 0x32, 0xf5, 0xc7, 0xfa,
	0xea, 0x7f, 0xac, 0x71, 0x05, 0x55, 0x34, 0x50, 0xaa, 0xa5, 0x1e, 0x42, 0x8b, 0xf1, 0x88, 0xae,
	0xb6, 0x44, 0xdb, 0x2a, 0x07, 0xe5, 0x3f, 0x79, 0xf7, 0x95, 0x1c, 0xd2, 0x3f, 0xf9, 0x7a, 0xec,
	0xe7, 0x1c, 0x4e, 0x25, 0x6a, 0xec, 0x53, 0x9f, 0x46, 0xc9, 0xe1, 0xff, 0xd6, 0x28, 0x87, 0x8b,
	0xc8, 0x38, 0x44, 0xe3, 0xbc, 0x28, 0xdd, 0x5b, 0x29, 0x4a, 0x52, 0xd7, 0xbc, 0x44, 0xc9, 0x8a,
	0xb2, 0x56, 0xae, 0x28, 0xeb, 0x59, 0x45, 0xf9, 0x7f, 0x2b, 0x87, 0x43, 0xb8, 0x94, 0xd9, 0xac,
	0x72, 0x1e, 0xfd, 0x5b, 0x0d, 0x2e, 0x2b, 0x50, 0x71, 0x88, 0x1e, 0x66, 0x65, 0xf0, 0xee, 0x6a,
	0x71, 0x48, 0xb8, 0x23, 0x49, 0xe0, 0x1b, 0x72, 0xe6, 0x8f, 0x2e, 0x26, 0xb7, 0x8f, 0x73, 0x72,
	0xbb, 0x57, 0x71, 0xa2, 0xdf, 0xa4, 0xd4, 0xfe, 0x44, 0x83, 0x3e, 0x51, 0x98, 0x2b, 0xf4, 0xc1,
	0x6a, 0x33, 0x72, 0x15, 0xba, 0x91, 0x6b, 0x65, 0x8c, 0x48, 0x87, 0x00, 0xa8, 0x09, 0xb9, 0x04,
	0xcd, 0xf0, 0x24, 0xf0, 0x5d, 0x6e, 0x3d, 0xd8, 0x07, 0xba, 0x0c, 0x2d, 0x2b, 0x0c, 0xa7, 0xe3,
	0x47, 0xd4, 0xbc, 0xf5, 0xcd, 0xa6, 0x15, 0x86, 0xe3, 0x47, 0x64, 0x01, 0x01, 0xa6, 0xde, 0x40,
	0xdf, 0xac, 0x05, 0xd8, 0x18, 0xc0, 0xba, 0x3c, 0x33, 0x1c, 0x12, 0x08, 0x89, 0x83, 0x52, 0x07,
	0xdd, 0xf8, 0x13, 0x0d, 0x36, 0x32, 0x20, 0x2a, 0x1e, 0xb9, 0x03, 0x74, 0x5b, 0x19, 0x4d, 0x49,
	0x5d, 0x0a, 0xaa, 0xf8, 0xeb, 0x72, 0x35, 0x95, 0x72, 0xc9, 0x47, 0x53, 0x4a, 0xf9, 0xbf, 0xa6,
	0x52, 0x2e, 0xa3, 0xe2, 0x50, 0xbd, 0x0f, 0xa9, 0x20, 0xd4, 0x72, 0xb6, 0xbe, 0xb2, 0xb0, 0x65,
	0x87, 0x51, 0x08, 0x5b, 0x2a, 0xff, 0x8d, 0x12, 0x17, 0xca, 0x0e, 0x1c, 0xe1, 0x9a, 0xd0, 0xc6,
	0x83, 0xc0, 0xc9, 0xf9, 0x57, 0xad, 0x9c, 0x7f, 0xf5, 0xe5, 0xd7, 0xd7, 0x29, 0xa9, 0xc3, 0xd8,
	0x58, 0xed, 0x30, 0x36, 0xf3, 0x0e, 0x23, 0x09, 0xd0, 0x89, 0xca, 0x74, 0x2d, 0x1c, 0xf8, 0xbf,
	0xee, 0x1e, 0xf3, 0xdd, 0x30, 0x16, 0x74, 0x97, 0x72, 0x60, 0xe6, 0xa2, 0xc4, 0x5e, 0x3c, 0x77,
	0xa9, 0xdf, 0xc3, 0xbd, 0x0d, 0x0a, 0x18, 0xb3, 0x99, 0x38, 0x2e, 0xb6, 0x49, 0x1b, 0x0b, 0x48,
	0xba, 0x66, 0x87, 0x00, 0xc6, 0x8b, 0x19, 0xa6, 0x87, 0x61, 0xc9, 0x3a, 0xb2, 0x30, 0x83, 0x1c,
	0x86, 0x25, 0xed, 0x88, 0x8d, 0x77, 0xe1, 0x1d, 0xc2, 0xff, 0x23, 0xb2, 0x23, 0x64, 0x5a, 0x87,
	0xf3, 0x20, 0x3a, 0x7b, 0x1e, 0x79, 0xb3, 0x99, 0x1b, 0x91, 0xe9, 0x5c, 0x87, 0x6b, 0x2b, 0xda,
	0x71, 0x68, 0xec, 0xc0, 0x56, 0x06, 0x41, 0xea, 0xab, 0xc3, 0xb6, 0xba, 0x09, 0x87, 0xc6, 0x0f,
	0xa8, 0xad, 0x49, 0x9a, 0x08, 0x9e, 0x90, 0xc7, 0x1d, 0xe8, 0xf8, 0xae, 0xeb, 0x4c, 0x17, 0x9e,
	0x4d, 0x17, 0xda, 0x31, 0xdb, 0xe4, 0xfb, 0xc8, 0xb3, 0x93, 0x44, 0x47, 0x4d, 0x4a, 0x74, 0xfc,
	0x8b, 0x06, 0xdb, 0x6a, 0x52, 0x38, 0x44, 0x16, 0xac, 0xd3, 0x2d, 0x9a, 0x91, 0x69, 0x4f, 0x17,
	0x56, 0xc8, 0x4f, 0xdf, 0xf7, 0x95, 0x91, 0x8c, 0x8a, 0x04, 0xd5, 0xd0, 0x74, 0xd5, 0x47, 0x56,
	0xf8, 0xd8, 0x8f, 0xa3, 0x33, 0x73, 0x6d, 0x29, 0x81, 0xf4, 0x29, 0x6c, 0x16, 0x50, 0x88, 0xb0,
	0xbc, 0x72, 0xcf, 0xc4, 0x39, 0x79, 0xe5, 0x9e, 0xa1, 0x0f, 0xa0, 0xf9, 0xda, 0x9a, 0x2f, 0x99,
	0x4c, 0xf5, 0x1e, 0xbc, 0x5b, 0x8c, 0xf3, 0x05, 0x11, 0x16, 0xd7, 0x51, 0xe4, 0x8f, 0x6a, 0x1f,
	0x6a, 0xc6, 0xcf, 0x35, 0xe8, 0x67, 0x1a, 0x15, 0xa7, 0xf0, 0x31, 0xac, 0xb3, 0x25, 0x46, 0x2e,
	0x0e, 0x96, 0x91, 0x5d, 0x3e, 0x0c, 0xa5, 0x62, 0x72, 0x2c, 0xb3, 0x3f, 0x93, 0x3f, 0x89, 0x05,
	0x72, 0x2c, 0x7f, 0x9a, 0xca, 0x79, 0xcb, 0xb1, 0x7c, 0x22, 0x60, 0xef, 0x00, 0x90, 0x86, 0xe3,
	0x99, 0xe4, 0x76, 0x77, 0x1c, 0xcb, 0xdf, 0x9f, 0x91, 0xd6, 0x1d, 0x20, 0xbf, 0xe5, 0xe0, 0x80,
	0x90, 0xa1, 0xa7, 0x73, 0x0b, 0xda, 0x0b, 0x8f, 0x0a, 0x26, 0x3d, 0x7e, 0x5d, 0xb3, 0xb5, 0xf0,
	0x88, 0x58, 0xa2, 0x77, 0xa1, 0x47, 0xfa, 0x88, 0xc6, 0x36, 0xf3, 0xe4, 0x1d, 0xcb, 0x3f, 0xa2,
	0xed, 0xc6, 0x5d, 0xb8, 0xbc, 0x6f, 0xc5, 0xf6, 0x09, 0x59, 0xf9, 0xb3, 0xe0, 0x8b, 0x54, 0x3c,
	0x54, 0xc9, 0xae, 0x7f, 0xd6, 0xe0, 0x8a, 0x0a, 0x1b, 0x87, 0x68, 0xca, 0x25, 0x20, 0x24, 0x50,
	0x49, 0x02, 0xbe, 0x57, 0xe0, 0x8c, 0x9a, 0xc0, 0x28, 0x81, 0x64, 0xf7, 0x5f, 0x80, 0xc4, 0xfe,
	0x67, 0x50, 0xde, 0x74, 0xff, 0xd3, 0x91, 0xa5, 0xfd, 0xbf, 0x0d, 0x9b, 0xc9, 0xd4, 0xf8, 0xee,
	0xa9, 0xb9, 0xf0, 0x0f, 0x1a, 0xa0, 0x3c, 0x26, 0x0e, 0xd1, 0xe7, 0x25, 0x67, 0xe0, 0x3b, 0xe5,
	0x1c, 0x48, 0x3a, 0x7f, 0x63, 0xd2, 0x9f, 0x15, 0x4b, 0x69, 0xf5, 0xb7, 0x00, 0x11, 0xc3, 0x28,
	0x06, 0xe1, 0xb9, 0xbc, 0xa2, 0xcd, 0xfa, 0x21, 0xbc, 0x5d, 0xc0, 0xc3, 0x21, 0xfa, 0x10, 0xda,
	0x33, 0xf6, 0xc9, 0x57, 0x7d, 0xde, 0xd0, 0x02, 0xdd, 0xf8, 0x04, 0x36, 0x26, 0x6e, 0x9c, 0x61,
	0x7a, 0xf1, 0xdc, 0xed, 0x40, 0x87, 0xb1, 0x35, 0x71, 0x42, 0x68, 0xff, 0xb3, 0xb1, 0x63, 0x20,
	0x18, 0x64, 0xfb, 0xe3, 0xd0, 0xf8, 0x1c, 0x76, 0xc4, 0x24, 0x7f, 0xe0, 0xe1, 0x38, 0x88, 0xce,
	0x56, 0xad, 0x89, 0x78, 0x2a, 0x73, 0x6f, 0xe1, 0xb1, 0xbc, 0x68, 0xdf, 0x64, 0x1f, 0xe8, 0x0a,
	0xb4, 0x82, 0x97, 0x2f, 0xb1, 0x1b, 0x8b, 0x33, 0xca, 0xbe, 0x8c, 0x05, 0xe8, 0x65, 0xc4, 0x71,
	0x48, 0xfc, 0x1b, 0x0f, 0x4f, 0x5d, 0xdf, 0xe1, 0x3a, 0xb5, 0xe9, 0xe1, 0xc7, 0xbe, 0x23, 0xf3,
	0xa7, 0x76, 0x31, 0xfe, 0x7c, 0x17, 0x10, 0x5f, 0xdf, 0x93, 0xc0, 0xb6, 0x62, 0x2f, 0x50, 0x87,
	0xa6, 0xd4, 0xb6, 0x86, 0x9c, 0x39, 0x35, 0x2f, 0x34, 0xee, 0xc3, 0xdb, 0x85, 0x7e, 0x38, 0x44,
	0x3a, 0x74, 0xe6, 0xfc, 0x5b, 0x98, 0x37, 0xf1, 0x6d, 0x7c, 0x06, 0xe8, 0xf0, 0x0d, 0x86, 0x22,
	0xbb, 0xe3, 0xe1, 0xa9, 0x6d, 0xd9, 0x27, 0xcc, 0x0b, 0xec, 0x98, 0x6d, 0x0f, 0x1f, 0x90, 0x4f,
	0xe3, 0x39, 0xb5, 0xbe, 0x17, 0x99, 0x05, 0xba, 0x01, 0x6b, 0x1e, 0x9e, 0x62, 0xd7, 0xc7, 0x5e,
	0xec, 0xbd, 0x66, 0xa2, 0xdc, 0x31, 0x7b, 0x1e, 0x9e, 0x08, 0x90, 0x14, 0xd0, 0x0a, 0xaa, 0x0f,
	0x97, 0xf1, 0x89, 0x5a, 0x60, 0x3f, 0x4c, 0xe2, 0xd9, 0x2c, 0x2e, 0x0e, 0xd1, 0x35, 0x00, 0x0f,
	0x4f, 0xad, 0xf9, 0x3c, 0xf8, 0xc2, 0x15, 0xfb, 0xd5, 0xf5, 0xf0, 0x43, 0x06, 0x30, 0xc6, 0x70,
	0x65, 0x52, 0x71, 0x94, 0x1c, 0xa9, 0x5a, 0x9e, 0xd4, 0x0e, 0x6c, 0x4d, 0xd4, 0x93, 0x20, 0x6b,
	0x11, 0xe2, 0x34, 0xf1, 0xfc, 0x19, 0xf1, 0xd1, 0xca, 0x32, 0xb8, 0xc6, 0xdf, 0xd7, 0x60, 0x4b,
	0x89, 0x8c, 0x43, 0xf4, 0x14, 0x9a, 0x9e, 0xff, 0x32, 0x10, 0xe7, 0xef, 0x43, 0xa5, 0xdf, 0xab,
	0xe8, 0x38, 0x92, 0xbe, 0x99, 0x4a, 0xa4, 0x64, 0xf4, 0x7f, 0xd4, 0x60, 0x23, 0xd7, 0x44, 0x83,
	0x01, 0x0a, 0x92, 0x53, 0x31, 0x14, 0xc0, 0x73, 0x4a, 0xac, 0x51, 0xf2, 0xe9, 0x80, 0x81, 0xa8,
	0x8d, 0x7a, 0x0f, 0xfa, 0x1c, 0x81, 0xe7, 0xce, 0xd8, 0xb9, 0x5a, 0x63, 0x40, 0x9e, 0x3d, 0xfb,
	0x00, 0x9a, 0xf4, 0x64, 0x53, 0xe3, 0x57, 0x41, 0x83, 0x51, 0xe4, 0x34, 0xb5, 0xcd, 0x83, 0x0a,
	0xfa, 0x41, 0x94, 0x37, 0xf1, 0xce, 0xb8, 0x45, 0xa4, 0xbf, 0x8d, 0xbf, 0xd3, 0xe0, 0xaa, 0x70,
	0x86, 0xd3, 0xd5, 0x3d, 0x72, 0x63, 0xcb, 0x5b, 0x11, 0x01, 0x25, 0x8b, 0xae, 0xe5, 0x16, 0xfd,
	0x14, 0xda, 0x91, 0x7b, 0xfa, 0xfc, 0x2c, 0x64, 0x92, 0xbf, 0xfe, 0xe0, 0x83, 0x52, 0xd7, 0x5b,
	0x31, 0xda, 0xc8, 0x64, 0x7d, 0x4d, 0x41, 0xc4, 0xb8, 0x0e, 0x6d, 0x0e, 0x43, 0x1d, 0x68, 0x7c,
	0xba, 0x9c, 0xcf, 0x07, 0x6f, 0x21, 0x80, 0xd6, 0xc4, 0x5b, 0x84, 0x73, 0x77, 0xa0, 0x19, 0x3f,
	0x69, 0xc3, 0x3b, 0xe5, 0x14, 0x71, 0x88, 0x3c, 0xd8, 0xe4, 0xd3, 0xa5, 0x4c, 0x98, 0x92, 0xdd,
	0xe4, 0xc9, 0xf7, 0x8f, 0x2f, 0x30, 0x37, 0x85, 0x60, 0x6c, 0xe0, 0x9c, 0x38, 0xdc, 0x83, 0x16,
	0xa6, 0x0e, 0x34, 0x37, 0x37, 0x5b, 0x05, 0xfa, 0xcc, 0xbf, 0x36, 0x39, 0x1a, 0xb2, 0x61, 0x83,
	0x16, 0x96, 0xa4, 0x99, 0xd5, 0xcb, 0xfd, 0xc4, 0x15, 0x33, 0x0b, 0xfc, 0x59, 0x3a, 0xaf, 0x3e,
	0x96, 0x3f, 0xd1, 0x5d, 0xa8, 0xcf, 0x03, 0x9b, 0xcb, 0xcf, 0x4e, 0xf1, 0x18, 0x08, 0x3d, 0x44,
	0xb0, 0x90, 0x01, 0x6b, 0x04, 0x7a, 0xe4, 0xf9, 0x13, 0x49, 0x7e, 0x32, 0x30, 0xfd, 0x7f, 0x7e,
	0xf1, 0x4f, 0xc2, 0x55, 0xe8, 0x2e, 0xac, 0x1f, 0xf3, 0xe8, 0x91, 0x45, 0xd9, 0x9d, 0x85, 0xf5,
	0x63, 0x16, 0x3d, 0xee, 0x40, 0x07, 0x5b, 0xfe, 0x6c, 0xea, 0x2f, 0x17, 0xd4, 0x3f, 0xec, 0x9b,
	0x6d, 0xf2, 0xfd, 0x74, 0xb9, 0xd0, 0xff, 0xba, 0x06, 0xfd, 0x0c, 0xb3, 0x89, 0xa3, 0xc9, 0x6b,
	0x83, 0x7c, 0xd9, 0x2d, 0x56, 0x1a, 0x24, 0x03, 0x7b, 0x0b, 0x6b, 0x26, 0x96, 0xcb, 0x3e, 0x92,
	0x08, 0xaf, 0x2e, 0x45, 0x78, 0xc9, 0x14, 0x1b, 0xf2, 0x14, 0xaf, 0x43, 0x2f, 0x0e, 0x62, 0x6b,
	0x3e, 0x95, 0xa7, 0x0f, 0x14, 0xc4, 0xa6, 0x79, 0x27, 0x11, 0x6c, 0x42, 0x05, 0x4f, 0xa5, 0xa3,
	0xbd, 0x91, 0xf2, 0x16, 0x3f, 0x72, 0xb1, 0x4d, 0x6c, 0x48, 0x10, 0x79, 0x33, 0xcf, 0xe7, 0xd4,
	0xd8, 0xb2, 0x7a, 0x0c, 0xc6, 0xc8, 0xdd, 0x82, 0x8d, 0xe3, 0x65, 0x84, 0xe3, 0xe9, 0xdc, 0x9b,
	0x9d, 0xc4, 0x74, 0xf1, 0x1d, 0x8a, 0xd5, 0xa7, 0xe0, 0x27, 0x04, 0xfa, 0x74, 0xb9, 0x40, 0x43,
	0x18, 0xc8, 0x78, 0x76, 0x10, 0x9e, 0xf1, 0xf2, 0xc2, 0x7a, 0x8a, 0x78, 0x10, 0x84, 0x67, 0x3c,
	0x29, 0x3e, 0x89, 0xad, 0x88, 0x27, 0x9b, 0x15, 0x1a, 0xfc, 0x3f, 0x35, 0x96, 0x66, 0x4e, 0x90,
	0x70, 0x88, 0x26, 0xdc, 0x71, 0xc4, 0xb1, 0x15, 0x89, 0xe3, 0x5a, 0x9a, 0x5f, 0x95, 0x7b, 0x52,
	0xaf, 0x91, 0x00, 0xe8, 0x31, 0xa0, 0x0e, 0xa3, 0xf8, 0xd2, 0xff, 0x80, 0x17, 0x35, 0x04, 0xa0,
	0x62, 0xda, 0xea, 0x0a, 0xb4, 0x32, 0x22, 0xc9, 0xbf, 0xd0, 0xf7, 0x00, 0xe8, 0xf4, 0xe6, 0xee,
	0x6b, 0x77, 0xce, 0x25, 0x52, 0x2f, 0x1e, 0xf7, 0xd8, 0x8a, 0x9e, 0x10, 0x0c, 0xb3, 0x8b, 0xc5,
	0x4f, 0xe3, 0x8c, 0x26, 0x39, 0x64, 0xab, 0xc3, 0xb9, 0xb3, 0xf2, 0x0c, 0xdd, 0x87, 0x26, 0x1b,
	0xab, 0x46, 0xd5, 0xea, 0x55, 0xb5, 0xf4, 0xb3, 0xc1, 0x18, 0xa6, 0x58, 0x61, 0x3d, 0xe5, 0x36,
	0x89, 0x57, 0x54, 0x63, 0xe3, 0x10, 0xfd, 0x36, 0xb0, 0xfb, 0x02, 0x19, 0x25, 0xa9, 0x76, 0xd7,
	0xd5, 0x14, 0x18, 0xf3, 0x53, 0x25, 0xb4, 0x94, 0x3f, 0x75, 0x8b, 0xc5, 0x92, 0xe9, 0xb9, 0x49,
	0x53, 0x1f, 0xfd, 0xd2, 0xd4, 0x47, 0x19, 0xf3, 0x95, 0x07, 0xc6, 0xf8, 0x9d, 0xd4, 0x17, 0x60,
	0x11, 0x35, 0xbb, 0x08, 0x70, 0x11, 0x17, 0xf7, 0x2a, 0x74, 0x99, 0x53, 0x3b, 0xe5, 0x7c, 0xeb,
	0x9a, 0x1d, 0x06, 0x18, 0x3b, 0xc6, 0x7f, 0xd4, 0x60, 0x5b, 0x3d, 0x40, 0xb9, 0x9b, 0xfb, 0x6b,
	0xe9, 0x65, 0x06, 0xe6, 0xe6, 0xfe, 0x72, 0xa9, 0x1b, 0x92, 0x27, 0x39, 0xca, 0x5f, 0x68, 0xf8,
	0xa9, 0x06, 0x2d, 0x7e, 0x9d, 0x21, 0x73, 0x79, 0x81, 0x0c, 0xd8, 0x48, 0x2f, 0x2f, 0x24, 0x56,
	0xbe, 0x96, 0x5a, 0x79, 0x5a, 0xbe, 0x4a, 0xc5, 0xb5, 0x4e, 0x93, 0x42, 0xa9, 0x48, 0xa2, 0x1f,
	0x32, 0xc9, 0x9b, 0xc6, 0xc4, 0x6e, 0x37, 0xa8, 0x80, 0x3d, 0xa8, 0x3e, 0x51, 0x22, 0x0c, 0xd4,
	0x6a, 0x53, 0x69, 0xa5, 0xb6, 0x5a, 0xa9, 0x75, 0x8d, 0x11, 0x74, 0x04, 0x2e, 0xb3, 0xe1, 0xfe,
	0x6c, 0xee, 0x0e, 0xde, 0x42, 0x5d, 0x68, 0x3e, 0x5f, 0xc6, 0xb1, 0x37, 0xd0, 0x50, 0x0f, 0xda,
	0x66, 0x70, 0x4c, 0x5a, 0x06, 0x35, 0x63, 0x0f, 0x06, 0x63, 0x4c, 0xef, 0x56, 0x8c, 0x7d, 0x71,
	0x48, 0x56, 0xdc, 0xc5, 0xf8, 0x4b, 0x0d, 0x36, 0x73, 0xf8, 0x38, 0x44, 0x9f, 0x41, 0x8f, 0x77,
	0x90, 0x62, 0xd0, 0xfb, 0x85, 0xd5, 0x15, 0x3a, 0x52, 0xa3, 0x3a, 0x76, 0x92, 0xf8, 0xb3, 0x8b,
	0xc5, 0xb7, 0xfe, 0x2b, 0xb0, 0x9e, 0x6d, 0x94, 0x23, 0xcf, 0x2e, 0x8b, 0x3c, 0x2f, 0xc9, 0x91,
	0x67, 0x47, 0x8e, 0x2c, 0x59, 0x61, 0x6d, 0x75, 0x80, 0x67, 0xfc, 0x21, 0x2b, 0xac, 0x65, 0x23,
	0xea, 0x8f, 0x01, 0xd2, 0x88, 0x9a, 0xfb, 0x30, 0xe7, 0x99, 0xc1, 0x6e, 0x12, 0x36, 0x13, 0x21,
	0x38, 0x71, 0x2d, 0x67, 0x2a, 0x9b, 0xa5, 0x2e, 0x81, 0x8c, 0xa9, 0x69, 0xba, 0x0a, 0x5d, 0xdf,
	0xb3, 0x5f, 0x65, 0x92, 0xd7, 0x04, 0x40, 0xac, 0x88, 0x71, 0x9c, 0x24, 0x71, 0x8f, 0x48, 0xb0,
	0xbe, 0x22, 0x34, 0x95, 0x0c, 0x62, 0x2d, 0x63, 0x10, 0x73, 0x5e, 0x40, 0x3d, 0xef, 0x05, 0x18,
	0x93, 0x24, 0xfb, 0x2b, 0x8f, 0x81, 0x43, 0xf4, 0x11, 0x74, 0x92, 0xfc, 0x52, 0xb5, 0x55, 0x27,
	0xf8, 0xc6, 0x1e, 0xec, 0x08, 0xa2, 0x22, 0xcd, 0xf6, 0xc8, 0xb3, 0xe6, 0xc1, 0x4c, 0xcd, 0xf7,
	0xbf, 0xd0, 0x40, 0x2f, 0xc3, 0xc7, 0x21, 0xfa, 0x55, 0x58, 0x63, 0x71, 0xb7, 0x43, 0x61, 0x7c,
	0x36, 0xef, 0xa8, 0x67, 0xc3, 0xfb, 0xf5, 0x66, 0xe9, 0x07, 0xda, 0x87, 0x3e, 0xf3, 0xfd, 0x04,
	0x05, 0xe6, 0x29, 0x5e, 0x2b, 0xf1, 0x14, 0x39, 0x89, 0x35, 0x2c, 0x7d, 0x19, 0x3f, 0xab, 0x41,
	0x4f, 0x1a, 0x80, 0x78, 0x4f, 0x76, 0xb0, 0x24, 0x72, 0x38, 0xa5, 0x7f, 0xf9, 0x7a, 0xd6, 0x38,
	0xf0, 0x80, 0xfc, 0x41, 0x37, 0x61, 0x3d, 0x8c, 0x82, 0xd7, 0x9e, 0x6f, 0xbb, 0x1c, 0x8b, 0x69,
	0xbe, 0xbe, 0x80, 0x32, 0xb4, 0x6b, 0xfc, 0x42, 0x07, 0x43, 0x61, 0xa6, 0xa3, 0xcb, 0xd2, 0xda,
	0xa4, 0x79, 0x3f, 0x0d, 0xdb, 0x1b, 0x65, 0xa5, 0xdd, 0x74, 0x66, 0xdc, 0x63, 0x66, 0x5b, 0x23,
	0x3a, 0x12, 0x4b, 0xdc, 0x93, 0x1a, 0xf2, 0x72, 0xa1, 0x15, 0xbc, 0xc3, 0x1b, 0xb0, 0x26, 0xec,
	0xa2, 0x24, 0xb9, 0xbc, 0x13, 0x93, 0xdd, 0xc4, 0x37, 0xac, 0x5f, 0xc0, 0x37, 0x34, 0xbe, 0xaa,
	0xc1, 0x9a, 0xcc, 0x67, 0xe2, 0x03, 0xbd, 0xf4, 0x88, 0x6f, 0x93, 0x4f, 0x79, 0xf7, 0x29, 0xf8,
	0xb9, 0xc8, 0x7b, 0x0f, 0x61, 0x80, 0x5d, 0x3b, 0xf0, 0x1d, 0x09, 0x91, 0xcd, 0x6a, 0x9d, 0xc1,
	0x13, 0xcc, 0x23, 0xc2, 0x76, 0x77, 0x9a, 0x53, 0xbe, 0xaa, 0x32, 0x8c, 0x3c, 0x11, 0xc9, 0x71,
	0x58, 0x0b, 0x23, 0x37, 0xf9, 0x22, 0xe4, 0xec, 0x65, 0x34, 0x2d, 0xb8, 0x1e, 0xd5, 0xc9, 0xd9,
	0xcb, 0x28, 0xf9, 0xd2, 0xdf, 0x87, 0x6e, 0x4a, 0xfb, 0x12, 0x34, 0xe9, 0x6a, 0xf8, 0x92, 0xd9,
	0x87, 0x28, 0x3f, 0xd4, 0xd2, 0x7a, 0xce, 0x9f, 0x6b, 0xb0, 0xc5, 0x6e, 0x48, 0x25, 0xa9, 0xc3,
	0x15, 0x97, 0xac, 0x56, 0x46, 0x8b, 0x92, 0xae, 0xa8, 0xe7, 0x9d, 0x67, 0x85, 0x4b, 0xac, 0x70,
	0x51, 0x9b, 0x0a, 0x17, 0x95, 0x16, 0x00, 0x94, 0x13, 0xc4, 0xa1, 0x31, 0x86, 0x2b, 0x69, 0x5b,
	0xca, 0x97, 0xb2, 0x24, 0x86, 0xc4, 0xe9, 0x5a, 0xce, 0x6a, 0xd2, 0x12, 0x84, 0x8a, 0x14, 0x0e,
	0x8d, 0xaf, 0xb4, 0x5c, 0xfd, 0x82, 0x6d, 0xc4, 0x41, 0xe0, 0xbf, 0xf4, 0xa2, 0x85, 0x7a, 0xb4,
	0xcf, 0x88, 0x96, 0x3b, 0x65, 0x36, 0x98, 0x39, 0x79, 0xdf, 0x55, 0x26, 0x6b, 0x4b, 0x69, 0x16,
	0xa3, 0xe7, 0xbd, 0x34, 0x7a, 0xde, 0x84, 0xfe, 0xc4, 0xc5, 0xd8, 0x13, 0x32, 0x31, 0x78, 0x0b,
	0x6d, 0x64, 0xd4, 0xc8, 0x40, 0x33, 0x76, 0xe1, 0xdd, 0x55, 0x03, 0x50, 0xee, 0x11, 0x33, 0x60,
	0x5a, 0xb1, 0xe7, 0xcf, 0x3e, 0x0d, 0xd2, 0x7d, 0x4f, 0xf6, 0x4b, 0x2b, 0x8d, 0xb2, 0x6a, 0xd9,
	0x28, 0xcb, 0x78, 0x49, 0xb5, 0x7d, 0x9e, 0x14, 0x0e, 0x2b, 0x79, 0x86, 0xdf, 0x82, 0x01, 0x73,
	0x54, 0x43, 0x37, 0xb2, 0x5d, 0x3f, 0x26, 0x6a, 0x81, 0x29, 0xab, 0x0d, 0x0a, 0x7f, 0x96, 0x80,
	0x8d, 0x9f, 0xf2, 0x32, 0x46, 0x92, 0xe3, 0x56, 0xb0, 0x9e, 0x87, 0xc8, 0xb5, 0x4a, 0x21, 0xf2,
	0x1b, 0xe9, 0x9a, 0xaf, 0x13, 0x30, 0xfc, 0xad, 0x06, 0x1d, 0x31, 0x05, 0xb4, 0x0d, 0x6d, 0xae,
	0xd7, 0xf9, 0x39, 0x15, 0x9f, 0x44, 0x4d, 0xa6, 0x66, 0xc0, 0x49, 0xd4, 0x64, 0x62, 0x05, 0x1c,
	0x17, 0xe9, 0xd0, 0x11, 0xea, 0x5e, 0x58, 0x78, 0xf1, 0x4d, 0xac, 0x88, 0x64, 0x20, 0x1c, 0x51,
	0xf0, 0x5c, 0x4b, 0xed, 0x83, 0x43, 0xc3, 0x57, 0x62, 0x0c, 0x78, 0xb5, 0x85, 0xfe, 0xce, 0x16,
	0x42, 0x5b, 0xd9, 0x42, 0xa8, 0xf1, 0x33, 0x2d, 0xa7, 0x62, 0xd8, 0xfa, 0xb9, 0x98, 0xb0, 0x38,
	0xe5, 0x1a, 0x00, 0xfd, 0x31, 0x95, 0xdc, 0xd6, 0x2e, 0x85, 0xd0, 0xd8, 0x15, 0x41, 0x03, 0x8b,
	0x18, 0xa0, 0x6f, 0xd2, 0xdf, 0x69, 0x70, 0xdd, 0x90, 0x83, 0xeb, 0x1d, 0xe8, 0xd0, 0x4a, 0x11,
	0x69, 0xe0, 0xf5, 0xa0, 0xe3, 0x19, 0x33, 0x10, 0xbb, 0x34, 0x89, 0x1a, 0x07, 0x21, 0xdf, 0x80,
	0x16, 0xf5, 0xca, 0xc0, 0xc3, 0xcf, 0x83, 0xf0, 0x89, 0x98, 0x05, 0xdd, 0x20, 0x1c, 0x9f, 0xcd,
	0x45, 0x80, 0x4c, 0x37, 0x61, 0x42, 0x00, 0xc6, 0xcf, 0x35, 0x68, 0x31, 0x9d, 0x5a, 0x10, 0x50,
	0xe2, 0x92, 0xc6, 0x56, 0x14, 0x8b, 0x9b, 0xc4, 0x0d, 0xb3, 0x4d, 0xbf, 0x1f, 0xc6, 0x24, 0x2c,
	0x70, 0x7d, 0x87, 0x34, 0xd4, 0x69, 0x43, 0xd3, 0xf5, 0x9d, 0x87, 0xb1, 0xca, 0xce, 0x34, 0xaa,
	0xda, 0x99, 0xa6, 0xd2, 0xce, 0x64, 0xca, 0xb4, 0xad, 0x55, 0x65, 0xda, 0xf6, 0xaa, 0x32, 0x6d,
	0x27, 0x57, 0xa6, 0xfd, 0xaf, 0x1a, 0xf4, 0x33, 0xe2, 0x4c, 0xd8, 0xc4, 0x3c, 0x20, 0xc9, 0x58,
	0x77, 0x29, 0x44, 0x54, 0xc5, 0x79, 0x61, 0x22, 0x31, 0x1a, 0xac, 0x52, 0x31, 0x66, 0x37, 0x7d,
	0x59, 0xa3, 0x1d, 0xcc, 0x03, 0x11, 0xd4, 0x31, 0x72, 0x07, 0x04, 0x92, 0x12, 0x4f, 0x4a, 0xdb,
	0x7d, 0x4e, 0x9c, 0xd6, 0xb6, 0xaf, 0x01, 0x84, 0x73, 0x4b, 0x74, 0x67, 0x8c, 0xe8, 0x12, 0x48,
	0xd2, 0xfb, 0xd8, 0xf2, 0x7d, 0xea, 0x27, 0x08, 0x26, 0x74, 0x19, 0x84, 0x8c, 0xbe, 0x2b, 0x7c,
	0x37, 0x5e, 0x4d, 0x6c, 0x4b, 0xc3, 0x27, 0xf5, 0xc4, 0xa4, 0xaa, 0xd2, 0xc9, 0x54, 0x55, 0xd2,
	0xf0, 0xbb, 0x5b, 0x39, 0xfc, 0xde, 0xa1, 0x39, 0x7d, 0x76, 0x2c, 0x80, 0x51, 0x9b, 0x07, 0xb6,
	0xb8, 0x1e, 0x90, 0x1a, 0xc5, 0x5e, 0xd6, 0x28, 0xde, 0xb9, 0x0f, 0x90, 0x12, 0x43, 0x1d, 0x76,
	0xfd, 0x7b, 0xf0, 0x16, 0x5a, 0x83, 0xce, 0x33, 0x7e, 0x16, 0x59, 0xa0, 0x74, 0xc0, 0x4e, 0xf6,
	0xa0, 0xf6, 0xe0, 0xcf, 0x6e, 0x30, 0x1d, 0x97, 0x68, 0x6e, 0xf4, 0x8a, 0x2a, 0xea, 0x42, 0x65,
	0x19, 0x0d, 0x2b, 0x16, 0xa0, 0x4f, 0xf5, 0x6f, 0x55, 0x2e, 0x55, 0x23, 0x57, 0xaa, 0xff, 0xa5,
	0x6a, 0xf6, 0x56, 0xa5, 0x4a, 0xe7, 0xa9, 0x7e, 0xbb, 0x62, 0x45, 0x14, 0xfd, 0x06, 0xac, 0x67,
	0x2b, 0x85, 0xc8, 0x38, 0xb7, 0x94, 0x78, 0xaa, 0xbf, 0x57, 0xa1, 0xdc, 0x88, 0x7e, 0x8b, 0xdd,
	0x9c, 0x91, 0xaa, 0x78, 0xe8, 0xbd, 0xd2, 0x00, 0x38, 0xad, 0x9d, 0xe9, 0xbf, 0x74, 0x3e, 0x12,
	0x4d, 0x68, 0xad, 0xc9, 0x25, 0x39, 0xb4, 0xab, 0xf0, 0xd6, 0x32, 0x01, 0xa1, 0x7e, 0xe3, 0x1c,
	0x0c, 0x1c, 0xa2, 0xd3, 0xb4, 0x4e, 0x92, 0x2d, 0xbb, 0xa1, 0x3b, 0xa5, 0x93, 0x2a, 0x14, 0xff,
	0xf4, 0xbb, 0x95, 0x71, 0x19, 0x97, 0x72, 0x55, 0x1b, 0x05, 0x97, 0x8a, 0xc5, 0x39, 0x05, 0x97,
	0x54, 0x95, 0xb8, 0x93, 0xb4, 0x92, 0x2a, 0xa5, 0x97, 0xd0, 0xed, 0x6a, 0x85, 0x9b, 0x53, 0x7d,
	0x58, 0xb5, 0xc2, 0x83, 0xbe, 0xa0, 0x37, 0x37, 0x94, 0xe9, 0x74, 0xf4, 0xed, 0x8b, 0xd4, 0x2b,
	0xf4, 0xbd, 0x0b, 0xe5, 0xe9, 0xc9, 0xa9, 0x54, 0x65, 0x51, 0xd0, 0xb0, 0x62, 0xb2, 0x45, 0x75,
	0x2a, 0x4b, 0x53, 0x52, 0xcf, 0xa0, 0x27, 0xe5, 0x47, 0xd1, 0xf5, 0xd5, 0xd9, 0xd3, 0x53, 0x7d,
	0xf7, 0xbc, 0xf4, 0x2a, 0x39, 0xe7, 0xc5, 0xdc, 0x9f, 0xe2, 0x9c, 0x2b, 0xd3, 0x9b, 0xfa, 0xed,
	0x8a, 0x89, 0x44, 0xf4, 0x23, 0xe8, 0x67, 0xb2, 0x31, 0xe8, 0xc6, 0x79, 0xd9, 0x9a, 0x53, 0xdd,
	0x38, 0x3f, 0xa1, 0x43, 0xc4, 0xf7, 0xf0, 0x5c, 0xf1, 0x3d, 0xac, 0x22, 0xbe, 0x87, 0x6a, 0xf1,
	0x55, 0xd4, 0x55, 0xd1, 0xed, 0xf3, 0x3a, 0xf3, 0x1a, 0xaa, 0x3e, 0xac, 0x86, 0xc8, 0x46, 0x9a,
	0x54, 0x1a, 0x69, 0x52, 0x75, 0xa4, 0x92, 0x5a, 0x2c, 0x3a, 0xa6, 0x0f, 0x58, 0xb2, 0x19, 0x19,
	0x74, 0xb3, 0x6c, 0xa2, 0x99, 0xcc, 0x90, 0x7e, 0xab, 0x0a, 0x1a, 0xd3, 0x63, 0xea, 0x84, 0x8b,
	0x42, 0x8f, 0x95, 0x66, 0x72, 0xf4, 0xbb, 0x95, 0x71, 0xd9, 0x31, 0x54, 0xc5, 0x87, 0x8a, 0x63,
	0x58, 0x12, 0xe7, 0x2a, 0x8e, 0x61, 0x59, 0xc0, 0x49, 0x76, 0x4b, 0x11, 0x25, 0x2a, 0x76, 0x4b,
	0x1d, 0x96, 0xea, 0xc3, 0x6a, 0x88, 0x38, 0x44, 0xbf, 0x07, 0x7a, 0x79, 0xf8, 0x86, 0x46, 0x17,
	0x0b, 0x26, 0xf5, 0x7b, 0x17, 0xc2, 0xe7, 0x5c, 0x55, 0x5c, 0xbb, 0x53, 0x71, 0x55, 0x7d, 0x71,
	0x4f, 0xc5, 0xd5, 0x92, 0x7b, 0x7c, 0xe8, 0x77, 0x61, 0xa7, 0xf4, 0x7e, 0x20, 0xda, 0x5b, 0x4d,
	0x27, 0x77, 0xd7, 0x50, 0x1f, 0x5d, 0x04, 0x3d, 0x39, 0x15, 0xd9, 0xc8, 0x55, 0x7d, 0x2a, 0x0a,
	0x81, 0xb2, 0xfa, 0x54, 0x28, 0x82, 0xe0, 0x29, 0x7b, 0x57, 0x21, 0x5f, 0xc7, 0x44, 0x4a, 0x3d,
	0x94, 0xbf, 0xc8, 0xa9, 0xdf, 0xac, 0x80, 0x95, 0x39, 0xda, 0xd2, 0x03, 0x9a, 0x9b, 0x55, 0xee,
	0xc9, 0xae, 0x38, 0xda, 0xb9, 0x5b, 0xbb, 0xcf, 0xa0, 0x27, 0x5d, 0x2e, 0x56, 0x58, 0xa0, 0xec,
	0x05, 0x66, 0x85, 0x05, 0xca, 0x5f, 0x67, 0x3e, 0x02, 0x48, 0xaf, 0x41, 0xa3, 0x62, 0xe8, 0x9d,
	0xb9, 0xbd, 0xad, 0x5f, 0x5f, 0xd9, 0x9e, 0xd1, 0xd9, 0xf2, 0x6b, 0x89, 0x72, 0x9d, 0x9d, 0x7b,
	0x2e, 0x52, 0xae, 0xb3, 0x0b, 0x4f, 0x45, 0x52, 0x76, 0xa7, 0xf7, 0xdb, 0xcb, 0xd9, 0x9d, 0x79,
	0x0e, 0x50, 0xce, 0xee, 0xdc, 0x53, 0x00, 0xb6, 0x9a, 0xfc, 0x63, 0x08, 0xf5, 0x6a, 0x14, 0x4f,
	0x33, 0xf4, 0x61, 0xd5, 0xb7, 0x15, 0xe8, 0x39, 0xf4, 0x33, 0x4d, 0x68, 0xf7, 0x9c, 0x17, 0x3e,
	0x2a, 0x8f, 0xb6, 0xf0, 0x6e, 0x88, 0x39, 0x2c, 0xe2, 0x81, 0x94, 0xda, 0x61, 0x91, 0x9e, 0x58,
	0xa9, 0x1d, 0x96, 0xcc, 0xfb, 0xaa, 0x1f, 0xd1, 0x79, 0xa6, 0x2f, 0x05, 0xd1, 0x8d, 0xf3, 0x5e,
	0x12, 0xaa, 0x3c, 0x89, 0xe2, 0x1b, 0xce, 0x63, 0xd8, 0x2c, 0xbc, 0xb2, 0x52, 0xec, 0xa6, 0xea,
	0xf9, 0x96, 0x62, 0x37, 0x95, 0x0f, 0xb6, 0x0a, 0x63, 0x50, 0x3e, 0x9f, 0x33, 0x86, 0xf0, 0x86,
	0x6e, 0x55, 0x41, 0x63, 0x63, 0x14, 0xde, 0xba, 0xa2, 0x9b, 0x25, 0x06, 0x27, 0xfb, 0x9e, 0x56,
	0x31, 0x86, 0xf2, 0xd9, 0x2c, 0xd9, 0x83, 0xcc, 0x23, 0x5d, 0xc5, 0x1e, 0xe4, 0x9f, 0x1a, 0x2b,
	0xf6, 0xa0, 0xf0, 0xce, 0x97, 0x04, 0x55, 0xf2, 0x5b, 0x67, 0x85, 0x08, 0xe6, 0x9e, 0x55, 0xeb,
	0x37, 0xce, 0xc1, 0xc0, 0x21, 0x3a, 0x84, 0x8e, 0x78, 0xce, 0x8d, 0x8a, 0x55, 0x1d, 0xe9, 0x4d,
	0xb8, 0x7e, 0x6d, 0x45, 0x2b, 0x9b, 0x9d, 0xfc, 0x88, 0x5e, 0x31, 0xbb, 0xdc, 0x53, 0x7c, 0xc5,
	0xec, 0xf2, 0xaf, 0xf0, 0x89, 0x03, 0x9b, 0x7b, 0x5b, 0xaf, 0x70, 0x60, 0x8b, 0xef, 0xf4, 0x15,
	0x0e, 0xac, 0xe2, 0x89, 0x3e, 0xd9, 0xa8, 0xcc, 0xc3, 0x7a, 0xc5, 0x46, 0xe5, 0x9f, 0xf0, 0x2b,
	0x36, 0xaa, 0xf0, 0x36, 0x7f, 0xff, 0xfd, 0xdf, 0xbc, 0x3f, 0x0b, 0xe6, 0x96, 0x3f, 0x1b, 0x7d,
	0xe7, 0x41, 0x1c, 0x8f, 0xec, 0x60, 0x71, 0x8f, 0xfe, 0x83, 0x02, 0x3b, 0x98, 0xdf, 0xc3, 0x6e,
	0xf4, 0xda, 0xb3, 0x5d, 0x9c, 0xff, 0x17, 0x06, 0xc7, 0x2d, 0x8a, 0xf2, 0xfe, 0xff, 0x06, 0x00,
	0x00, 0xff, 0xff, 0xaa, 0x92, 0xd2, 0xda, 0xec, 0x40, 0x00, 0x00,
}
