// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user-tag-go/user-tag-go.proto

package user_tag_go // import "golang.52tt.com/protocol/services/user-tag-go"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type ClassifyTagType int32

const (
	ClassifyTagType_CLASSIFY_TAG_TYPE_INVALID  ClassifyTagType = 0
	ClassifyTagType_CLASSIFY_TAG_REGIST_MY_TAG ClassifyTagType = 11
)

var ClassifyTagType_name = map[int32]string{
	0:  "CLASSIFY_TAG_TYPE_INVALID",
	11: "CLASSIFY_TAG_REGIST_MY_TAG",
}
var ClassifyTagType_value = map[string]int32{
	"CLASSIFY_TAG_TYPE_INVALID":  0,
	"CLASSIFY_TAG_REGIST_MY_TAG": 11,
}

func (x ClassifyTagType) String() string {
	return proto.EnumName(ClassifyTagType_name, int32(x))
}
func (ClassifyTagType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{0}
}

// 想要找什么样的人 类型的标签
type EUserTagFindWhoType int32

const (
	EUserTagFindWhoType_ENUM_FINDWHOTAG_INVALID          EUserTagFindWhoType = 0
	EUserTagFindWhoType_ENUM_FINDWHOTAG_OPPOSITE_SEX     EUserTagFindWhoType = 1
	EUserTagFindWhoType_ENUM_FINDWHOTAG_ASSIGN_SEX       EUserTagFindWhoType = 2
	EUserTagFindWhoType_ENUM_FINDWHOTAG_CORRELATION_PAIR EUserTagFindWhoType = 3
	EUserTagFindWhoType_ENUM_FINDWHOTAG_GAME             EUserTagFindWhoType = 4
)

var EUserTagFindWhoType_name = map[int32]string{
	0: "ENUM_FINDWHOTAG_INVALID",
	1: "ENUM_FINDWHOTAG_OPPOSITE_SEX",
	2: "ENUM_FINDWHOTAG_ASSIGN_SEX",
	3: "ENUM_FINDWHOTAG_CORRELATION_PAIR",
	4: "ENUM_FINDWHOTAG_GAME",
}
var EUserTagFindWhoType_value = map[string]int32{
	"ENUM_FINDWHOTAG_INVALID":          0,
	"ENUM_FINDWHOTAG_OPPOSITE_SEX":     1,
	"ENUM_FINDWHOTAG_ASSIGN_SEX":       2,
	"ENUM_FINDWHOTAG_CORRELATION_PAIR": 3,
	"ENUM_FINDWHOTAG_GAME":             4,
}

func (x EUserTagFindWhoType) String() string {
	return proto.EnumName(EUserTagFindWhoType_name, int32(x))
}
func (EUserTagFindWhoType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{1}
}

type GetGameTypeListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGameTypeListReq) Reset()         { *m = GetGameTypeListReq{} }
func (m *GetGameTypeListReq) String() string { return proto.CompactTextString(m) }
func (*GetGameTypeListReq) ProtoMessage()    {}
func (*GetGameTypeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{0}
}
func (m *GetGameTypeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTypeListReq.Unmarshal(m, b)
}
func (m *GetGameTypeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTypeListReq.Marshal(b, m, deterministic)
}
func (dst *GetGameTypeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTypeListReq.Merge(dst, src)
}
func (m *GetGameTypeListReq) XXX_Size() int {
	return xxx_messageInfo_GetGameTypeListReq.Size(m)
}
func (m *GetGameTypeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTypeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTypeListReq proto.InternalMessageInfo

type GameType struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameType) Reset()         { *m = GameType{} }
func (m *GameType) String() string { return proto.CompactTextString(m) }
func (*GameType) ProtoMessage()    {}
func (*GameType) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{1}
}
func (m *GameType) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameType.Unmarshal(m, b)
}
func (m *GameType) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameType.Marshal(b, m, deterministic)
}
func (dst *GameType) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameType.Merge(dst, src)
}
func (m *GameType) XXX_Size() int {
	return xxx_messageInfo_GameType.Size(m)
}
func (m *GameType) XXX_DiscardUnknown() {
	xxx_messageInfo_GameType.DiscardUnknown(m)
}

var xxx_messageInfo_GameType proto.InternalMessageInfo

func (m *GameType) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *GameType) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetGameTypeListResp struct {
	GameTypeList         []*GameType `protobuf:"bytes,1,rep,name=game_type_list,json=gameTypeList,proto3" json:"game_type_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetGameTypeListResp) Reset()         { *m = GetGameTypeListResp{} }
func (m *GetGameTypeListResp) String() string { return proto.CompactTextString(m) }
func (*GetGameTypeListResp) ProtoMessage()    {}
func (*GetGameTypeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{2}
}
func (m *GetGameTypeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGameTypeListResp.Unmarshal(m, b)
}
func (m *GetGameTypeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGameTypeListResp.Marshal(b, m, deterministic)
}
func (dst *GetGameTypeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGameTypeListResp.Merge(dst, src)
}
func (m *GetGameTypeListResp) XXX_Size() int {
	return xxx_messageInfo_GetGameTypeListResp.Size(m)
}
func (m *GetGameTypeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGameTypeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGameTypeListResp proto.InternalMessageInfo

func (m *GetGameTypeListResp) GetGameTypeList() []*GameType {
	if m != nil {
		return m.GameTypeList
	}
	return nil
}

type AddRegistGameRelatedTagConfReq struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	GameName             string   `protobuf:"bytes,2,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	Title                string   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	MaxSelectCnt         uint32   `protobuf:"varint,4,opt,name=max_select_cnt,json=maxSelectCnt,proto3" json:"max_select_cnt,omitempty"`
	TagList              []string `protobuf:"bytes,5,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRegistGameRelatedTagConfReq) Reset()         { *m = AddRegistGameRelatedTagConfReq{} }
func (m *AddRegistGameRelatedTagConfReq) String() string { return proto.CompactTextString(m) }
func (*AddRegistGameRelatedTagConfReq) ProtoMessage()    {}
func (*AddRegistGameRelatedTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{3}
}
func (m *AddRegistGameRelatedTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRegistGameRelatedTagConfReq.Unmarshal(m, b)
}
func (m *AddRegistGameRelatedTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRegistGameRelatedTagConfReq.Marshal(b, m, deterministic)
}
func (dst *AddRegistGameRelatedTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRegistGameRelatedTagConfReq.Merge(dst, src)
}
func (m *AddRegistGameRelatedTagConfReq) XXX_Size() int {
	return xxx_messageInfo_AddRegistGameRelatedTagConfReq.Size(m)
}
func (m *AddRegistGameRelatedTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRegistGameRelatedTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddRegistGameRelatedTagConfReq proto.InternalMessageInfo

func (m *AddRegistGameRelatedTagConfReq) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *AddRegistGameRelatedTagConfReq) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

func (m *AddRegistGameRelatedTagConfReq) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *AddRegistGameRelatedTagConfReq) GetMaxSelectCnt() uint32 {
	if m != nil {
		return m.MaxSelectCnt
	}
	return 0
}

func (m *AddRegistGameRelatedTagConfReq) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type AddRegistGameRelatedTagConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddRegistGameRelatedTagConfResp) Reset()         { *m = AddRegistGameRelatedTagConfResp{} }
func (m *AddRegistGameRelatedTagConfResp) String() string { return proto.CompactTextString(m) }
func (*AddRegistGameRelatedTagConfResp) ProtoMessage()    {}
func (*AddRegistGameRelatedTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{4}
}
func (m *AddRegistGameRelatedTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddRegistGameRelatedTagConfResp.Unmarshal(m, b)
}
func (m *AddRegistGameRelatedTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddRegistGameRelatedTagConfResp.Marshal(b, m, deterministic)
}
func (dst *AddRegistGameRelatedTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddRegistGameRelatedTagConfResp.Merge(dst, src)
}
func (m *AddRegistGameRelatedTagConfResp) XXX_Size() int {
	return xxx_messageInfo_AddRegistGameRelatedTagConfResp.Size(m)
}
func (m *AddRegistGameRelatedTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddRegistGameRelatedTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddRegistGameRelatedTagConfResp proto.InternalMessageInfo

type DeleteRegistGameRelatedTagConfReq struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRegistGameRelatedTagConfReq) Reset()         { *m = DeleteRegistGameRelatedTagConfReq{} }
func (m *DeleteRegistGameRelatedTagConfReq) String() string { return proto.CompactTextString(m) }
func (*DeleteRegistGameRelatedTagConfReq) ProtoMessage()    {}
func (*DeleteRegistGameRelatedTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{5}
}
func (m *DeleteRegistGameRelatedTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRegistGameRelatedTagConfReq.Unmarshal(m, b)
}
func (m *DeleteRegistGameRelatedTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRegistGameRelatedTagConfReq.Marshal(b, m, deterministic)
}
func (dst *DeleteRegistGameRelatedTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRegistGameRelatedTagConfReq.Merge(dst, src)
}
func (m *DeleteRegistGameRelatedTagConfReq) XXX_Size() int {
	return xxx_messageInfo_DeleteRegistGameRelatedTagConfReq.Size(m)
}
func (m *DeleteRegistGameRelatedTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRegistGameRelatedTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRegistGameRelatedTagConfReq proto.InternalMessageInfo

func (m *DeleteRegistGameRelatedTagConfReq) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

type DeleteRegistGameRelatedTagConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteRegistGameRelatedTagConfResp) Reset()         { *m = DeleteRegistGameRelatedTagConfResp{} }
func (m *DeleteRegistGameRelatedTagConfResp) String() string { return proto.CompactTextString(m) }
func (*DeleteRegistGameRelatedTagConfResp) ProtoMessage()    {}
func (*DeleteRegistGameRelatedTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{6}
}
func (m *DeleteRegistGameRelatedTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteRegistGameRelatedTagConfResp.Unmarshal(m, b)
}
func (m *DeleteRegistGameRelatedTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteRegistGameRelatedTagConfResp.Marshal(b, m, deterministic)
}
func (dst *DeleteRegistGameRelatedTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteRegistGameRelatedTagConfResp.Merge(dst, src)
}
func (m *DeleteRegistGameRelatedTagConfResp) XXX_Size() int {
	return xxx_messageInfo_DeleteRegistGameRelatedTagConfResp.Size(m)
}
func (m *DeleteRegistGameRelatedTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteRegistGameRelatedTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteRegistGameRelatedTagConfResp proto.InternalMessageInfo

type GetRegistGameRelatedTagConfReq struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRegistGameRelatedTagConfReq) Reset()         { *m = GetRegistGameRelatedTagConfReq{} }
func (m *GetRegistGameRelatedTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetRegistGameRelatedTagConfReq) ProtoMessage()    {}
func (*GetRegistGameRelatedTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{7}
}
func (m *GetRegistGameRelatedTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRegistGameRelatedTagConfReq.Unmarshal(m, b)
}
func (m *GetRegistGameRelatedTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRegistGameRelatedTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetRegistGameRelatedTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegistGameRelatedTagConfReq.Merge(dst, src)
}
func (m *GetRegistGameRelatedTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetRegistGameRelatedTagConfReq.Size(m)
}
func (m *GetRegistGameRelatedTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegistGameRelatedTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegistGameRelatedTagConfReq proto.InternalMessageInfo

func (m *GetRegistGameRelatedTagConfReq) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

type GetRegistGameRelatedTagConfResp struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	MaxSelectCnt         uint32   `protobuf:"varint,3,opt,name=max_select_cnt,json=maxSelectCnt,proto3" json:"max_select_cnt,omitempty"`
	TagList              []string `protobuf:"bytes,4,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	GameName             string   `protobuf:"bytes,5,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRegistGameRelatedTagConfResp) Reset()         { *m = GetRegistGameRelatedTagConfResp{} }
func (m *GetRegistGameRelatedTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetRegistGameRelatedTagConfResp) ProtoMessage()    {}
func (*GetRegistGameRelatedTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{8}
}
func (m *GetRegistGameRelatedTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRegistGameRelatedTagConfResp.Unmarshal(m, b)
}
func (m *GetRegistGameRelatedTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRegistGameRelatedTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetRegistGameRelatedTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRegistGameRelatedTagConfResp.Merge(dst, src)
}
func (m *GetRegistGameRelatedTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetRegistGameRelatedTagConfResp.Size(m)
}
func (m *GetRegistGameRelatedTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRegistGameRelatedTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRegistGameRelatedTagConfResp proto.InternalMessageInfo

func (m *GetRegistGameRelatedTagConfResp) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *GetRegistGameRelatedTagConfResp) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *GetRegistGameRelatedTagConfResp) GetMaxSelectCnt() uint32 {
	if m != nil {
		return m.MaxSelectCnt
	}
	return 0
}

func (m *GetRegistGameRelatedTagConfResp) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *GetRegistGameRelatedTagConfResp) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetAllRegistGameRelatedTagConfReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllRegistGameRelatedTagConfReq) Reset()         { *m = GetAllRegistGameRelatedTagConfReq{} }
func (m *GetAllRegistGameRelatedTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllRegistGameRelatedTagConfReq) ProtoMessage()    {}
func (*GetAllRegistGameRelatedTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{9}
}
func (m *GetAllRegistGameRelatedTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllRegistGameRelatedTagConfReq.Unmarshal(m, b)
}
func (m *GetAllRegistGameRelatedTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllRegistGameRelatedTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllRegistGameRelatedTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllRegistGameRelatedTagConfReq.Merge(dst, src)
}
func (m *GetAllRegistGameRelatedTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllRegistGameRelatedTagConfReq.Size(m)
}
func (m *GetAllRegistGameRelatedTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllRegistGameRelatedTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllRegistGameRelatedTagConfReq proto.InternalMessageInfo

type RegistGameRelatedTagConf struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	Title                string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SelectCnt            uint32   `protobuf:"varint,3,opt,name=select_cnt,json=selectCnt,proto3" json:"select_cnt,omitempty"`
	TagList              []string `protobuf:"bytes,4,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	GameName             string   `protobuf:"bytes,5,opt,name=game_name,json=gameName,proto3" json:"game_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegistGameRelatedTagConf) Reset()         { *m = RegistGameRelatedTagConf{} }
func (m *RegistGameRelatedTagConf) String() string { return proto.CompactTextString(m) }
func (*RegistGameRelatedTagConf) ProtoMessage()    {}
func (*RegistGameRelatedTagConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{10}
}
func (m *RegistGameRelatedTagConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegistGameRelatedTagConf.Unmarshal(m, b)
}
func (m *RegistGameRelatedTagConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegistGameRelatedTagConf.Marshal(b, m, deterministic)
}
func (dst *RegistGameRelatedTagConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegistGameRelatedTagConf.Merge(dst, src)
}
func (m *RegistGameRelatedTagConf) XXX_Size() int {
	return xxx_messageInfo_RegistGameRelatedTagConf.Size(m)
}
func (m *RegistGameRelatedTagConf) XXX_DiscardUnknown() {
	xxx_messageInfo_RegistGameRelatedTagConf.DiscardUnknown(m)
}

var xxx_messageInfo_RegistGameRelatedTagConf proto.InternalMessageInfo

func (m *RegistGameRelatedTagConf) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *RegistGameRelatedTagConf) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *RegistGameRelatedTagConf) GetSelectCnt() uint32 {
	if m != nil {
		return m.SelectCnt
	}
	return 0
}

func (m *RegistGameRelatedTagConf) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *RegistGameRelatedTagConf) GetGameName() string {
	if m != nil {
		return m.GameName
	}
	return ""
}

type GetAllRegistGameRelatedTagConfResp struct {
	AllConfList          []*RegistGameRelatedTagConf `protobuf:"bytes,1,rep,name=all_conf_list,json=allConfList,proto3" json:"all_conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetAllRegistGameRelatedTagConfResp) Reset()         { *m = GetAllRegistGameRelatedTagConfResp{} }
func (m *GetAllRegistGameRelatedTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllRegistGameRelatedTagConfResp) ProtoMessage()    {}
func (*GetAllRegistGameRelatedTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{11}
}
func (m *GetAllRegistGameRelatedTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllRegistGameRelatedTagConfResp.Unmarshal(m, b)
}
func (m *GetAllRegistGameRelatedTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllRegistGameRelatedTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllRegistGameRelatedTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllRegistGameRelatedTagConfResp.Merge(dst, src)
}
func (m *GetAllRegistGameRelatedTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllRegistGameRelatedTagConfResp.Size(m)
}
func (m *GetAllRegistGameRelatedTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllRegistGameRelatedTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllRegistGameRelatedTagConfResp proto.InternalMessageInfo

func (m *GetAllRegistGameRelatedTagConfResp) GetAllConfList() []*RegistGameRelatedTagConf {
	if m != nil {
		return m.AllConfList
	}
	return nil
}

type GetClassifyListReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetClassifyListReq) Reset()         { *m = GetClassifyListReq{} }
func (m *GetClassifyListReq) String() string { return proto.CompactTextString(m) }
func (*GetClassifyListReq) ProtoMessage()    {}
func (*GetClassifyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{12}
}
func (m *GetClassifyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClassifyListReq.Unmarshal(m, b)
}
func (m *GetClassifyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClassifyListReq.Marshal(b, m, deterministic)
}
func (dst *GetClassifyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClassifyListReq.Merge(dst, src)
}
func (m *GetClassifyListReq) XXX_Size() int {
	return xxx_messageInfo_GetClassifyListReq.Size(m)
}
func (m *GetClassifyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClassifyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetClassifyListReq proto.InternalMessageInfo

func (m *GetClassifyListReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type ClassifyInfo struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName         string   `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClassifyInfo) Reset()         { *m = ClassifyInfo{} }
func (m *ClassifyInfo) String() string { return proto.CompactTextString(m) }
func (*ClassifyInfo) ProtoMessage()    {}
func (*ClassifyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{13}
}
func (m *ClassifyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClassifyInfo.Unmarshal(m, b)
}
func (m *ClassifyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClassifyInfo.Marshal(b, m, deterministic)
}
func (dst *ClassifyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClassifyInfo.Merge(dst, src)
}
func (m *ClassifyInfo) XXX_Size() int {
	return xxx_messageInfo_ClassifyInfo.Size(m)
}
func (m *ClassifyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ClassifyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ClassifyInfo proto.InternalMessageInfo

func (m *ClassifyInfo) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *ClassifyInfo) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

type GetClassifyListResp struct {
	Type                 uint32          `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ClassifyList         []*ClassifyInfo `protobuf:"bytes,2,rep,name=classify_list,json=classifyList,proto3" json:"classify_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetClassifyListResp) Reset()         { *m = GetClassifyListResp{} }
func (m *GetClassifyListResp) String() string { return proto.CompactTextString(m) }
func (*GetClassifyListResp) ProtoMessage()    {}
func (*GetClassifyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{14}
}
func (m *GetClassifyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClassifyListResp.Unmarshal(m, b)
}
func (m *GetClassifyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClassifyListResp.Marshal(b, m, deterministic)
}
func (dst *GetClassifyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClassifyListResp.Merge(dst, src)
}
func (m *GetClassifyListResp) XXX_Size() int {
	return xxx_messageInfo_GetClassifyListResp.Size(m)
}
func (m *GetClassifyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClassifyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetClassifyListResp proto.InternalMessageInfo

func (m *GetClassifyListResp) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetClassifyListResp) GetClassifyList() []*ClassifyInfo {
	if m != nil {
		return m.ClassifyList
	}
	return nil
}

type AddClassifyTagConfReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ClassifyId           uint32   `protobuf:"varint,2,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	TagList              []string `protobuf:"bytes,3,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddClassifyTagConfReq) Reset()         { *m = AddClassifyTagConfReq{} }
func (m *AddClassifyTagConfReq) String() string { return proto.CompactTextString(m) }
func (*AddClassifyTagConfReq) ProtoMessage()    {}
func (*AddClassifyTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{15}
}
func (m *AddClassifyTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddClassifyTagConfReq.Unmarshal(m, b)
}
func (m *AddClassifyTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddClassifyTagConfReq.Marshal(b, m, deterministic)
}
func (dst *AddClassifyTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddClassifyTagConfReq.Merge(dst, src)
}
func (m *AddClassifyTagConfReq) XXX_Size() int {
	return xxx_messageInfo_AddClassifyTagConfReq.Size(m)
}
func (m *AddClassifyTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddClassifyTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddClassifyTagConfReq proto.InternalMessageInfo

func (m *AddClassifyTagConfReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *AddClassifyTagConfReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *AddClassifyTagConfReq) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type AddClassifyTagConfResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddClassifyTagConfResp) Reset()         { *m = AddClassifyTagConfResp{} }
func (m *AddClassifyTagConfResp) String() string { return proto.CompactTextString(m) }
func (*AddClassifyTagConfResp) ProtoMessage()    {}
func (*AddClassifyTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{16}
}
func (m *AddClassifyTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddClassifyTagConfResp.Unmarshal(m, b)
}
func (m *AddClassifyTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddClassifyTagConfResp.Marshal(b, m, deterministic)
}
func (dst *AddClassifyTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddClassifyTagConfResp.Merge(dst, src)
}
func (m *AddClassifyTagConfResp) XXX_Size() int {
	return xxx_messageInfo_AddClassifyTagConfResp.Size(m)
}
func (m *AddClassifyTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddClassifyTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddClassifyTagConfResp proto.InternalMessageInfo

type GetClassifyTagConfReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	ClassifyId           uint32   `protobuf:"varint,2,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetClassifyTagConfReq) Reset()         { *m = GetClassifyTagConfReq{} }
func (m *GetClassifyTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetClassifyTagConfReq) ProtoMessage()    {}
func (*GetClassifyTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{17}
}
func (m *GetClassifyTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClassifyTagConfReq.Unmarshal(m, b)
}
func (m *GetClassifyTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClassifyTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetClassifyTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClassifyTagConfReq.Merge(dst, src)
}
func (m *GetClassifyTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetClassifyTagConfReq.Size(m)
}
func (m *GetClassifyTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClassifyTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetClassifyTagConfReq proto.InternalMessageInfo

func (m *GetClassifyTagConfReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func (m *GetClassifyTagConfReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

type GetClassifyTagConfResp struct {
	TagList              []string `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetClassifyTagConfResp) Reset()         { *m = GetClassifyTagConfResp{} }
func (m *GetClassifyTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetClassifyTagConfResp) ProtoMessage()    {}
func (*GetClassifyTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{18}
}
func (m *GetClassifyTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetClassifyTagConfResp.Unmarshal(m, b)
}
func (m *GetClassifyTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetClassifyTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetClassifyTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetClassifyTagConfResp.Merge(dst, src)
}
func (m *GetClassifyTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetClassifyTagConfResp.Size(m)
}
func (m *GetClassifyTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetClassifyTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetClassifyTagConfResp proto.InternalMessageInfo

func (m *GetClassifyTagConfResp) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type ClassifyTagInfo struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName         string   `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	TagList              []string `protobuf:"bytes,3,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ClassifyTagInfo) Reset()         { *m = ClassifyTagInfo{} }
func (m *ClassifyTagInfo) String() string { return proto.CompactTextString(m) }
func (*ClassifyTagInfo) ProtoMessage()    {}
func (*ClassifyTagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{19}
}
func (m *ClassifyTagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ClassifyTagInfo.Unmarshal(m, b)
}
func (m *ClassifyTagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ClassifyTagInfo.Marshal(b, m, deterministic)
}
func (dst *ClassifyTagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ClassifyTagInfo.Merge(dst, src)
}
func (m *ClassifyTagInfo) XXX_Size() int {
	return xxx_messageInfo_ClassifyTagInfo.Size(m)
}
func (m *ClassifyTagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ClassifyTagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ClassifyTagInfo proto.InternalMessageInfo

func (m *ClassifyTagInfo) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *ClassifyTagInfo) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *ClassifyTagInfo) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type GetAllClassifyTagConfReq struct {
	Type                 uint32   `protobuf:"varint,1,opt,name=type,proto3" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAllClassifyTagConfReq) Reset()         { *m = GetAllClassifyTagConfReq{} }
func (m *GetAllClassifyTagConfReq) String() string { return proto.CompactTextString(m) }
func (*GetAllClassifyTagConfReq) ProtoMessage()    {}
func (*GetAllClassifyTagConfReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{20}
}
func (m *GetAllClassifyTagConfReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllClassifyTagConfReq.Unmarshal(m, b)
}
func (m *GetAllClassifyTagConfReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllClassifyTagConfReq.Marshal(b, m, deterministic)
}
func (dst *GetAllClassifyTagConfReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllClassifyTagConfReq.Merge(dst, src)
}
func (m *GetAllClassifyTagConfReq) XXX_Size() int {
	return xxx_messageInfo_GetAllClassifyTagConfReq.Size(m)
}
func (m *GetAllClassifyTagConfReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllClassifyTagConfReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllClassifyTagConfReq proto.InternalMessageInfo

func (m *GetAllClassifyTagConfReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

type GetAllClassifyTagConfResp struct {
	AllTagList           []*ClassifyTagInfo `protobuf:"bytes,1,rep,name=all_tag_list,json=allTagList,proto3" json:"all_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAllClassifyTagConfResp) Reset()         { *m = GetAllClassifyTagConfResp{} }
func (m *GetAllClassifyTagConfResp) String() string { return proto.CompactTextString(m) }
func (*GetAllClassifyTagConfResp) ProtoMessage()    {}
func (*GetAllClassifyTagConfResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{21}
}
func (m *GetAllClassifyTagConfResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAllClassifyTagConfResp.Unmarshal(m, b)
}
func (m *GetAllClassifyTagConfResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAllClassifyTagConfResp.Marshal(b, m, deterministic)
}
func (dst *GetAllClassifyTagConfResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAllClassifyTagConfResp.Merge(dst, src)
}
func (m *GetAllClassifyTagConfResp) XXX_Size() int {
	return xxx_messageInfo_GetAllClassifyTagConfResp.Size(m)
}
func (m *GetAllClassifyTagConfResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAllClassifyTagConfResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAllClassifyTagConfResp proto.InternalMessageInfo

func (m *GetAllClassifyTagConfResp) GetAllTagList() []*ClassifyTagInfo {
	if m != nil {
		return m.AllTagList
	}
	return nil
}

// 年龄段 类型的标签
type UserAgeTagExt struct {
	AgeYearMin           uint32   `protobuf:"varint,1,opt,name=age_year_min,json=ageYearMin,proto3" json:"age_year_min,omitempty"`
	AgeYearMax           uint32   `protobuf:"varint,2,opt,name=age_year_max,json=ageYearMax,proto3" json:"age_year_max,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserAgeTagExt) Reset()         { *m = UserAgeTagExt{} }
func (m *UserAgeTagExt) String() string { return proto.CompactTextString(m) }
func (*UserAgeTagExt) ProtoMessage()    {}
func (*UserAgeTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{22}
}
func (m *UserAgeTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAgeTagExt.Unmarshal(m, b)
}
func (m *UserAgeTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAgeTagExt.Marshal(b, m, deterministic)
}
func (dst *UserAgeTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAgeTagExt.Merge(dst, src)
}
func (m *UserAgeTagExt) XXX_Size() int {
	return xxx_messageInfo_UserAgeTagExt.Size(m)
}
func (m *UserAgeTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAgeTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_UserAgeTagExt proto.InternalMessageInfo

func (m *UserAgeTagExt) GetAgeYearMin() uint32 {
	if m != nil {
		return m.AgeYearMin
	}
	return 0
}

func (m *UserAgeTagExt) GetAgeYearMax() uint32 {
	if m != nil {
		return m.AgeYearMax
	}
	return 0
}

// 创建普通标签配置
type CreateTagConfigReq struct {
	TagType              uint32       `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	Tag                  *UserTagBase `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *CreateTagConfigReq) Reset()         { *m = CreateTagConfigReq{} }
func (m *CreateTagConfigReq) String() string { return proto.CompactTextString(m) }
func (*CreateTagConfigReq) ProtoMessage()    {}
func (*CreateTagConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{23}
}
func (m *CreateTagConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTagConfigReq.Unmarshal(m, b)
}
func (m *CreateTagConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTagConfigReq.Marshal(b, m, deterministic)
}
func (dst *CreateTagConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTagConfigReq.Merge(dst, src)
}
func (m *CreateTagConfigReq) XXX_Size() int {
	return xxx_messageInfo_CreateTagConfigReq.Size(m)
}
func (m *CreateTagConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTagConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTagConfigReq proto.InternalMessageInfo

func (m *CreateTagConfigReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *CreateTagConfigReq) GetTag() *UserTagBase {
	if m != nil {
		return m.Tag
	}
	return nil
}

type CreateTagConfigResp struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateTagConfigResp) Reset()         { *m = CreateTagConfigResp{} }
func (m *CreateTagConfigResp) String() string { return proto.CompactTextString(m) }
func (*CreateTagConfigResp) ProtoMessage()    {}
func (*CreateTagConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{24}
}
func (m *CreateTagConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateTagConfigResp.Unmarshal(m, b)
}
func (m *CreateTagConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateTagConfigResp.Marshal(b, m, deterministic)
}
func (dst *CreateTagConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateTagConfigResp.Merge(dst, src)
}
func (m *CreateTagConfigResp) XXX_Size() int {
	return xxx_messageInfo_CreateTagConfigResp.Size(m)
}
func (m *CreateTagConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateTagConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateTagConfigResp proto.InternalMessageInfo

func (m *CreateTagConfigResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

// 修改
type ModifyTagConfigReq struct {
	TagId                uint32       `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	Tag                  *UserTagBase `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ModifyTagConfigReq) Reset()         { *m = ModifyTagConfigReq{} }
func (m *ModifyTagConfigReq) String() string { return proto.CompactTextString(m) }
func (*ModifyTagConfigReq) ProtoMessage()    {}
func (*ModifyTagConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{25}
}
func (m *ModifyTagConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyTagConfigReq.Unmarshal(m, b)
}
func (m *ModifyTagConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyTagConfigReq.Marshal(b, m, deterministic)
}
func (dst *ModifyTagConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyTagConfigReq.Merge(dst, src)
}
func (m *ModifyTagConfigReq) XXX_Size() int {
	return xxx_messageInfo_ModifyTagConfigReq.Size(m)
}
func (m *ModifyTagConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyTagConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyTagConfigReq proto.InternalMessageInfo

func (m *ModifyTagConfigReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *ModifyTagConfigReq) GetTag() *UserTagBase {
	if m != nil {
		return m.Tag
	}
	return nil
}

type ModifyTagConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyTagConfigResp) Reset()         { *m = ModifyTagConfigResp{} }
func (m *ModifyTagConfigResp) String() string { return proto.CompactTextString(m) }
func (*ModifyTagConfigResp) ProtoMessage()    {}
func (*ModifyTagConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{26}
}
func (m *ModifyTagConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyTagConfigResp.Unmarshal(m, b)
}
func (m *ModifyTagConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyTagConfigResp.Marshal(b, m, deterministic)
}
func (dst *ModifyTagConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyTagConfigResp.Merge(dst, src)
}
func (m *ModifyTagConfigResp) XXX_Size() int {
	return xxx_messageInfo_ModifyTagConfigResp.Size(m)
}
func (m *ModifyTagConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyTagConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyTagConfigResp proto.InternalMessageInfo

// 删除
type DelTagConfigReq struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	IsRecover            bool     `protobuf:"varint,2,opt,name=is_recover,json=isRecover,proto3" json:"is_recover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTagConfigReq) Reset()         { *m = DelTagConfigReq{} }
func (m *DelTagConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelTagConfigReq) ProtoMessage()    {}
func (*DelTagConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{27}
}
func (m *DelTagConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTagConfigReq.Unmarshal(m, b)
}
func (m *DelTagConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTagConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelTagConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTagConfigReq.Merge(dst, src)
}
func (m *DelTagConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelTagConfigReq.Size(m)
}
func (m *DelTagConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTagConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelTagConfigReq proto.InternalMessageInfo

func (m *DelTagConfigReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *DelTagConfigReq) GetIsRecover() bool {
	if m != nil {
		return m.IsRecover
	}
	return false
}

type DelTagConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelTagConfigResp) Reset()         { *m = DelTagConfigResp{} }
func (m *DelTagConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelTagConfigResp) ProtoMessage()    {}
func (*DelTagConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{28}
}
func (m *DelTagConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelTagConfigResp.Unmarshal(m, b)
}
func (m *DelTagConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelTagConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelTagConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelTagConfigResp.Merge(dst, src)
}
func (m *DelTagConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelTagConfigResp.Size(m)
}
func (m *DelTagConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelTagConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelTagConfigResp proto.InternalMessageInfo

// 排序
type SortTagConfigReq struct {
	TagType                uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagIdList              []uint32 `protobuf:"varint,2,rep,packed,name=tag_id_list,json=tagIdList,proto3" json:"tag_id_list,omitempty"`
	PersonaltypeClassifyId uint32   `protobuf:"varint,3,opt,name=personaltype_classify_id,json=personaltypeClassifyId,proto3" json:"personaltype_classify_id,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *SortTagConfigReq) Reset()         { *m = SortTagConfigReq{} }
func (m *SortTagConfigReq) String() string { return proto.CompactTextString(m) }
func (*SortTagConfigReq) ProtoMessage()    {}
func (*SortTagConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{29}
}
func (m *SortTagConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortTagConfigReq.Unmarshal(m, b)
}
func (m *SortTagConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortTagConfigReq.Marshal(b, m, deterministic)
}
func (dst *SortTagConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortTagConfigReq.Merge(dst, src)
}
func (m *SortTagConfigReq) XXX_Size() int {
	return xxx_messageInfo_SortTagConfigReq.Size(m)
}
func (m *SortTagConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SortTagConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_SortTagConfigReq proto.InternalMessageInfo

func (m *SortTagConfigReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SortTagConfigReq) GetTagIdList() []uint32 {
	if m != nil {
		return m.TagIdList
	}
	return nil
}

func (m *SortTagConfigReq) GetPersonaltypeClassifyId() uint32 {
	if m != nil {
		return m.PersonaltypeClassifyId
	}
	return 0
}

type SortTagConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortTagConfigResp) Reset()         { *m = SortTagConfigResp{} }
func (m *SortTagConfigResp) String() string { return proto.CompactTextString(m) }
func (*SortTagConfigResp) ProtoMessage()    {}
func (*SortTagConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{30}
}
func (m *SortTagConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortTagConfigResp.Unmarshal(m, b)
}
func (m *SortTagConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortTagConfigResp.Marshal(b, m, deterministic)
}
func (dst *SortTagConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortTagConfigResp.Merge(dst, src)
}
func (m *SortTagConfigResp) XXX_Size() int {
	return xxx_messageInfo_SortTagConfigResp.Size(m)
}
func (m *SortTagConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SortTagConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SortTagConfigResp proto.InternalMessageInfo

// 创建findwho类型的标签
type CreateFindWhoTypeTagConfigReq struct {
	FindwhoType          uint32   `protobuf:"varint,1,opt,name=findwho_type,json=findwhoType,proto3" json:"findwho_type,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	AssignSex            uint32   `protobuf:"varint,3,opt,name=assign_sex,json=assignSex,proto3" json:"assign_sex,omitempty"`
	CorrelationTagName   string   `protobuf:"bytes,4,opt,name=correlation_tag_name,json=correlationTagName,proto3" json:"correlation_tag_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateFindWhoTypeTagConfigReq) Reset()         { *m = CreateFindWhoTypeTagConfigReq{} }
func (m *CreateFindWhoTypeTagConfigReq) String() string { return proto.CompactTextString(m) }
func (*CreateFindWhoTypeTagConfigReq) ProtoMessage()    {}
func (*CreateFindWhoTypeTagConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{31}
}
func (m *CreateFindWhoTypeTagConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateFindWhoTypeTagConfigReq.Unmarshal(m, b)
}
func (m *CreateFindWhoTypeTagConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateFindWhoTypeTagConfigReq.Marshal(b, m, deterministic)
}
func (dst *CreateFindWhoTypeTagConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateFindWhoTypeTagConfigReq.Merge(dst, src)
}
func (m *CreateFindWhoTypeTagConfigReq) XXX_Size() int {
	return xxx_messageInfo_CreateFindWhoTypeTagConfigReq.Size(m)
}
func (m *CreateFindWhoTypeTagConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateFindWhoTypeTagConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateFindWhoTypeTagConfigReq proto.InternalMessageInfo

func (m *CreateFindWhoTypeTagConfigReq) GetFindwhoType() uint32 {
	if m != nil {
		return m.FindwhoType
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *CreateFindWhoTypeTagConfigReq) GetAssignSex() uint32 {
	if m != nil {
		return m.AssignSex
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigReq) GetCorrelationTagName() string {
	if m != nil {
		return m.CorrelationTagName
	}
	return ""
}

type CreateFindWhoTypeTagConfigResp struct {
	FindwhoType          uint32   `protobuf:"varint,1,opt,name=findwho_type,json=findwhoType,proto3" json:"findwho_type,omitempty"`
	TagId                uint32   `protobuf:"varint,2,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	CorrelationTagId     uint32   `protobuf:"varint,3,opt,name=correlation_tag_id,json=correlationTagId,proto3" json:"correlation_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateFindWhoTypeTagConfigResp) Reset()         { *m = CreateFindWhoTypeTagConfigResp{} }
func (m *CreateFindWhoTypeTagConfigResp) String() string { return proto.CompactTextString(m) }
func (*CreateFindWhoTypeTagConfigResp) ProtoMessage()    {}
func (*CreateFindWhoTypeTagConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{32}
}
func (m *CreateFindWhoTypeTagConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateFindWhoTypeTagConfigResp.Unmarshal(m, b)
}
func (m *CreateFindWhoTypeTagConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateFindWhoTypeTagConfigResp.Marshal(b, m, deterministic)
}
func (dst *CreateFindWhoTypeTagConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateFindWhoTypeTagConfigResp.Merge(dst, src)
}
func (m *CreateFindWhoTypeTagConfigResp) XXX_Size() int {
	return xxx_messageInfo_CreateFindWhoTypeTagConfigResp.Size(m)
}
func (m *CreateFindWhoTypeTagConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateFindWhoTypeTagConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateFindWhoTypeTagConfigResp proto.InternalMessageInfo

func (m *CreateFindWhoTypeTagConfigResp) GetFindwhoType() uint32 {
	if m != nil {
		return m.FindwhoType
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *CreateFindWhoTypeTagConfigResp) GetCorrelationTagId() uint32 {
	if m != nil {
		return m.CorrelationTagId
	}
	return 0
}

// 创建个性标签的分类
type CreateOptPersonalTagClassifyReq struct {
	ClassifyName         string   `protobuf:"bytes,1,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateOptPersonalTagClassifyReq) Reset()         { *m = CreateOptPersonalTagClassifyReq{} }
func (m *CreateOptPersonalTagClassifyReq) String() string { return proto.CompactTextString(m) }
func (*CreateOptPersonalTagClassifyReq) ProtoMessage()    {}
func (*CreateOptPersonalTagClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{33}
}
func (m *CreateOptPersonalTagClassifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOptPersonalTagClassifyReq.Unmarshal(m, b)
}
func (m *CreateOptPersonalTagClassifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOptPersonalTagClassifyReq.Marshal(b, m, deterministic)
}
func (dst *CreateOptPersonalTagClassifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOptPersonalTagClassifyReq.Merge(dst, src)
}
func (m *CreateOptPersonalTagClassifyReq) XXX_Size() int {
	return xxx_messageInfo_CreateOptPersonalTagClassifyReq.Size(m)
}
func (m *CreateOptPersonalTagClassifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOptPersonalTagClassifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOptPersonalTagClassifyReq proto.InternalMessageInfo

func (m *CreateOptPersonalTagClassifyReq) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

type CreateOptPersonalTagClassifyResp struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CreateOptPersonalTagClassifyResp) Reset()         { *m = CreateOptPersonalTagClassifyResp{} }
func (m *CreateOptPersonalTagClassifyResp) String() string { return proto.CompactTextString(m) }
func (*CreateOptPersonalTagClassifyResp) ProtoMessage()    {}
func (*CreateOptPersonalTagClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{34}
}
func (m *CreateOptPersonalTagClassifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CreateOptPersonalTagClassifyResp.Unmarshal(m, b)
}
func (m *CreateOptPersonalTagClassifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CreateOptPersonalTagClassifyResp.Marshal(b, m, deterministic)
}
func (dst *CreateOptPersonalTagClassifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CreateOptPersonalTagClassifyResp.Merge(dst, src)
}
func (m *CreateOptPersonalTagClassifyResp) XXX_Size() int {
	return xxx_messageInfo_CreateOptPersonalTagClassifyResp.Size(m)
}
func (m *CreateOptPersonalTagClassifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CreateOptPersonalTagClassifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_CreateOptPersonalTagClassifyResp proto.InternalMessageInfo

func (m *CreateOptPersonalTagClassifyResp) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

// 获取全量分类列表
type GetOptPersonalTagClassifyListReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOptPersonalTagClassifyListReq) Reset()         { *m = GetOptPersonalTagClassifyListReq{} }
func (m *GetOptPersonalTagClassifyListReq) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagClassifyListReq) ProtoMessage()    {}
func (*GetOptPersonalTagClassifyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{35}
}
func (m *GetOptPersonalTagClassifyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOptPersonalTagClassifyListReq.Unmarshal(m, b)
}
func (m *GetOptPersonalTagClassifyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOptPersonalTagClassifyListReq.Marshal(b, m, deterministic)
}
func (dst *GetOptPersonalTagClassifyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOptPersonalTagClassifyListReq.Merge(dst, src)
}
func (m *GetOptPersonalTagClassifyListReq) XXX_Size() int {
	return xxx_messageInfo_GetOptPersonalTagClassifyListReq.Size(m)
}
func (m *GetOptPersonalTagClassifyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOptPersonalTagClassifyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOptPersonalTagClassifyListReq proto.InternalMessageInfo

type GetOptPersonalTagClassifyListResp struct {
	ClassifyList         []*UserOptPersonalTagClassify `protobuf:"bytes,1,rep,name=classify_list,json=classifyList,proto3" json:"classify_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetOptPersonalTagClassifyListResp) Reset()         { *m = GetOptPersonalTagClassifyListResp{} }
func (m *GetOptPersonalTagClassifyListResp) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagClassifyListResp) ProtoMessage()    {}
func (*GetOptPersonalTagClassifyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{36}
}
func (m *GetOptPersonalTagClassifyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOptPersonalTagClassifyListResp.Unmarshal(m, b)
}
func (m *GetOptPersonalTagClassifyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOptPersonalTagClassifyListResp.Marshal(b, m, deterministic)
}
func (dst *GetOptPersonalTagClassifyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOptPersonalTagClassifyListResp.Merge(dst, src)
}
func (m *GetOptPersonalTagClassifyListResp) XXX_Size() int {
	return xxx_messageInfo_GetOptPersonalTagClassifyListResp.Size(m)
}
func (m *GetOptPersonalTagClassifyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOptPersonalTagClassifyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOptPersonalTagClassifyListResp proto.InternalMessageInfo

func (m *GetOptPersonalTagClassifyListResp) GetClassifyList() []*UserOptPersonalTagClassify {
	if m != nil {
		return m.ClassifyList
	}
	return nil
}

// 根据分类获取个性标签列表
type GetOptPersonalTagByClassifyReq struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOptPersonalTagByClassifyReq) Reset()         { *m = GetOptPersonalTagByClassifyReq{} }
func (m *GetOptPersonalTagByClassifyReq) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagByClassifyReq) ProtoMessage()    {}
func (*GetOptPersonalTagByClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{37}
}
func (m *GetOptPersonalTagByClassifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOptPersonalTagByClassifyReq.Unmarshal(m, b)
}
func (m *GetOptPersonalTagByClassifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOptPersonalTagByClassifyReq.Marshal(b, m, deterministic)
}
func (dst *GetOptPersonalTagByClassifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOptPersonalTagByClassifyReq.Merge(dst, src)
}
func (m *GetOptPersonalTagByClassifyReq) XXX_Size() int {
	return xxx_messageInfo_GetOptPersonalTagByClassifyReq.Size(m)
}
func (m *GetOptPersonalTagByClassifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOptPersonalTagByClassifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOptPersonalTagByClassifyReq proto.InternalMessageInfo

func (m *GetOptPersonalTagByClassifyReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

type GetOptPersonalTagByClassifyResp struct {
	TagList              []*UserTagBase `protobuf:"bytes,1,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetOptPersonalTagByClassifyResp) Reset()         { *m = GetOptPersonalTagByClassifyResp{} }
func (m *GetOptPersonalTagByClassifyResp) String() string { return proto.CompactTextString(m) }
func (*GetOptPersonalTagByClassifyResp) ProtoMessage()    {}
func (*GetOptPersonalTagByClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{38}
}
func (m *GetOptPersonalTagByClassifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOptPersonalTagByClassifyResp.Unmarshal(m, b)
}
func (m *GetOptPersonalTagByClassifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOptPersonalTagByClassifyResp.Marshal(b, m, deterministic)
}
func (dst *GetOptPersonalTagByClassifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOptPersonalTagByClassifyResp.Merge(dst, src)
}
func (m *GetOptPersonalTagByClassifyResp) XXX_Size() int {
	return xxx_messageInfo_GetOptPersonalTagByClassifyResp.Size(m)
}
func (m *GetOptPersonalTagByClassifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOptPersonalTagByClassifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOptPersonalTagByClassifyResp proto.InternalMessageInfo

func (m *GetOptPersonalTagByClassifyResp) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

// 修改个性标签的分类的名字
type ModifyOptPersonalTagClassifyNameReq struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName         string   `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyOptPersonalTagClassifyNameReq) Reset()         { *m = ModifyOptPersonalTagClassifyNameReq{} }
func (m *ModifyOptPersonalTagClassifyNameReq) String() string { return proto.CompactTextString(m) }
func (*ModifyOptPersonalTagClassifyNameReq) ProtoMessage()    {}
func (*ModifyOptPersonalTagClassifyNameReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{39}
}
func (m *ModifyOptPersonalTagClassifyNameReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyOptPersonalTagClassifyNameReq.Unmarshal(m, b)
}
func (m *ModifyOptPersonalTagClassifyNameReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyOptPersonalTagClassifyNameReq.Marshal(b, m, deterministic)
}
func (dst *ModifyOptPersonalTagClassifyNameReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyOptPersonalTagClassifyNameReq.Merge(dst, src)
}
func (m *ModifyOptPersonalTagClassifyNameReq) XXX_Size() int {
	return xxx_messageInfo_ModifyOptPersonalTagClassifyNameReq.Size(m)
}
func (m *ModifyOptPersonalTagClassifyNameReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyOptPersonalTagClassifyNameReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyOptPersonalTagClassifyNameReq proto.InternalMessageInfo

func (m *ModifyOptPersonalTagClassifyNameReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *ModifyOptPersonalTagClassifyNameReq) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

type ModifyOptPersonalTagClassifyNameResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyOptPersonalTagClassifyNameResp) Reset()         { *m = ModifyOptPersonalTagClassifyNameResp{} }
func (m *ModifyOptPersonalTagClassifyNameResp) String() string { return proto.CompactTextString(m) }
func (*ModifyOptPersonalTagClassifyNameResp) ProtoMessage()    {}
func (*ModifyOptPersonalTagClassifyNameResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{40}
}
func (m *ModifyOptPersonalTagClassifyNameResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyOptPersonalTagClassifyNameResp.Unmarshal(m, b)
}
func (m *ModifyOptPersonalTagClassifyNameResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyOptPersonalTagClassifyNameResp.Marshal(b, m, deterministic)
}
func (dst *ModifyOptPersonalTagClassifyNameResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyOptPersonalTagClassifyNameResp.Merge(dst, src)
}
func (m *ModifyOptPersonalTagClassifyNameResp) XXX_Size() int {
	return xxx_messageInfo_ModifyOptPersonalTagClassifyNameResp.Size(m)
}
func (m *ModifyOptPersonalTagClassifyNameResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyOptPersonalTagClassifyNameResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyOptPersonalTagClassifyNameResp proto.InternalMessageInfo

// 排序 个性标签的分类
type SortOptPersonalTagClassifyReq struct {
	ClassifyIdList       []uint32 `protobuf:"varint,1,rep,packed,name=classify_id_list,json=classifyIdList,proto3" json:"classify_id_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortOptPersonalTagClassifyReq) Reset()         { *m = SortOptPersonalTagClassifyReq{} }
func (m *SortOptPersonalTagClassifyReq) String() string { return proto.CompactTextString(m) }
func (*SortOptPersonalTagClassifyReq) ProtoMessage()    {}
func (*SortOptPersonalTagClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{41}
}
func (m *SortOptPersonalTagClassifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortOptPersonalTagClassifyReq.Unmarshal(m, b)
}
func (m *SortOptPersonalTagClassifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortOptPersonalTagClassifyReq.Marshal(b, m, deterministic)
}
func (dst *SortOptPersonalTagClassifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortOptPersonalTagClassifyReq.Merge(dst, src)
}
func (m *SortOptPersonalTagClassifyReq) XXX_Size() int {
	return xxx_messageInfo_SortOptPersonalTagClassifyReq.Size(m)
}
func (m *SortOptPersonalTagClassifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SortOptPersonalTagClassifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_SortOptPersonalTagClassifyReq proto.InternalMessageInfo

func (m *SortOptPersonalTagClassifyReq) GetClassifyIdList() []uint32 {
	if m != nil {
		return m.ClassifyIdList
	}
	return nil
}

type SortOptPersonalTagClassifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SortOptPersonalTagClassifyResp) Reset()         { *m = SortOptPersonalTagClassifyResp{} }
func (m *SortOptPersonalTagClassifyResp) String() string { return proto.CompactTextString(m) }
func (*SortOptPersonalTagClassifyResp) ProtoMessage()    {}
func (*SortOptPersonalTagClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{42}
}
func (m *SortOptPersonalTagClassifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SortOptPersonalTagClassifyResp.Unmarshal(m, b)
}
func (m *SortOptPersonalTagClassifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SortOptPersonalTagClassifyResp.Marshal(b, m, deterministic)
}
func (dst *SortOptPersonalTagClassifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SortOptPersonalTagClassifyResp.Merge(dst, src)
}
func (m *SortOptPersonalTagClassifyResp) XXX_Size() int {
	return xxx_messageInfo_SortOptPersonalTagClassifyResp.Size(m)
}
func (m *SortOptPersonalTagClassifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SortOptPersonalTagClassifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_SortOptPersonalTagClassifyResp proto.InternalMessageInfo

// 删除个性标签的分类
type DelOptPersonalTagClassifyReq struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	IsRecover            bool     `protobuf:"varint,2,opt,name=is_recover,json=isRecover,proto3" json:"is_recover,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelOptPersonalTagClassifyReq) Reset()         { *m = DelOptPersonalTagClassifyReq{} }
func (m *DelOptPersonalTagClassifyReq) String() string { return proto.CompactTextString(m) }
func (*DelOptPersonalTagClassifyReq) ProtoMessage()    {}
func (*DelOptPersonalTagClassifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{43}
}
func (m *DelOptPersonalTagClassifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelOptPersonalTagClassifyReq.Unmarshal(m, b)
}
func (m *DelOptPersonalTagClassifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelOptPersonalTagClassifyReq.Marshal(b, m, deterministic)
}
func (dst *DelOptPersonalTagClassifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelOptPersonalTagClassifyReq.Merge(dst, src)
}
func (m *DelOptPersonalTagClassifyReq) XXX_Size() int {
	return xxx_messageInfo_DelOptPersonalTagClassifyReq.Size(m)
}
func (m *DelOptPersonalTagClassifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelOptPersonalTagClassifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelOptPersonalTagClassifyReq proto.InternalMessageInfo

func (m *DelOptPersonalTagClassifyReq) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *DelOptPersonalTagClassifyReq) GetIsRecover() bool {
	if m != nil {
		return m.IsRecover
	}
	return false
}

type DelOptPersonalTagClassifyResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelOptPersonalTagClassifyResp) Reset()         { *m = DelOptPersonalTagClassifyResp{} }
func (m *DelOptPersonalTagClassifyResp) String() string { return proto.CompactTextString(m) }
func (*DelOptPersonalTagClassifyResp) ProtoMessage()    {}
func (*DelOptPersonalTagClassifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{44}
}
func (m *DelOptPersonalTagClassifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelOptPersonalTagClassifyResp.Unmarshal(m, b)
}
func (m *DelOptPersonalTagClassifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelOptPersonalTagClassifyResp.Marshal(b, m, deterministic)
}
func (dst *DelOptPersonalTagClassifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelOptPersonalTagClassifyResp.Merge(dst, src)
}
func (m *DelOptPersonalTagClassifyResp) XXX_Size() int {
	return xxx_messageInfo_DelOptPersonalTagClassifyResp.Size(m)
}
func (m *DelOptPersonalTagClassifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelOptPersonalTagClassifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelOptPersonalTagClassifyResp proto.InternalMessageInfo

type UserTagBase struct {
	TagType              uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagInfo              []byte   `protobuf:"bytes,4,opt,name=tag_info,json=tagInfo,proto3" json:"tag_info,omitempty"`
	IsDel                bool     `protobuf:"varint,5,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserTagBase) Reset()         { *m = UserTagBase{} }
func (m *UserTagBase) String() string { return proto.CompactTextString(m) }
func (*UserTagBase) ProtoMessage()    {}
func (*UserTagBase) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{45}
}
func (m *UserTagBase) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTagBase.Unmarshal(m, b)
}
func (m *UserTagBase) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTagBase.Marshal(b, m, deterministic)
}
func (dst *UserTagBase) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTagBase.Merge(dst, src)
}
func (m *UserTagBase) XXX_Size() int {
	return xxx_messageInfo_UserTagBase.Size(m)
}
func (m *UserTagBase) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTagBase.DiscardUnknown(m)
}

var xxx_messageInfo_UserTagBase proto.InternalMessageInfo

func (m *UserTagBase) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *UserTagBase) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *UserTagBase) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *UserTagBase) GetTagInfo() []byte {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *UserTagBase) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type GameScreenShot struct {
	AuditStatus             uint32   `protobuf:"varint,1,opt,name=audit_status,json=auditStatus,proto3" json:"audit_status,omitempty"`
	ImgUrl                  string   `protobuf:"bytes,2,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	ImgUrlInvisibleGamenick string   `protobuf:"bytes,3,opt,name=img_url_invisible_gamenick,json=imgUrlInvisibleGamenick,proto3" json:"img_url_invisible_gamenick,omitempty"`
	BeginAuditTime          uint32   `protobuf:"varint,4,opt,name=begin_audit_time,json=beginAuditTime,proto3" json:"begin_audit_time,omitempty"`
	Index                   uint32   `protobuf:"varint,5,opt,name=index,proto3" json:"index,omitempty"`
	UploadTime              uint32   `protobuf:"varint,6,opt,name=upload_time,json=uploadTime,proto3" json:"upload_time,omitempty"`
	XXX_NoUnkeyedLiteral    struct{} `json:"-"`
	XXX_unrecognized        []byte   `json:"-"`
	XXX_sizecache           int32    `json:"-"`
}

func (m *GameScreenShot) Reset()         { *m = GameScreenShot{} }
func (m *GameScreenShot) String() string { return proto.CompactTextString(m) }
func (*GameScreenShot) ProtoMessage()    {}
func (*GameScreenShot) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{46}
}
func (m *GameScreenShot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameScreenShot.Unmarshal(m, b)
}
func (m *GameScreenShot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameScreenShot.Marshal(b, m, deterministic)
}
func (dst *GameScreenShot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameScreenShot.Merge(dst, src)
}
func (m *GameScreenShot) XXX_Size() int {
	return xxx_messageInfo_GameScreenShot.Size(m)
}
func (m *GameScreenShot) XXX_DiscardUnknown() {
	xxx_messageInfo_GameScreenShot.DiscardUnknown(m)
}

var xxx_messageInfo_GameScreenShot proto.InternalMessageInfo

func (m *GameScreenShot) GetAuditStatus() uint32 {
	if m != nil {
		return m.AuditStatus
	}
	return 0
}

func (m *GameScreenShot) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *GameScreenShot) GetImgUrlInvisibleGamenick() string {
	if m != nil {
		return m.ImgUrlInvisibleGamenick
	}
	return ""
}

func (m *GameScreenShot) GetBeginAuditTime() uint32 {
	if m != nil {
		return m.BeginAuditTime
	}
	return 0
}

func (m *GameScreenShot) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *GameScreenShot) GetUploadTime() uint32 {
	if m != nil {
		return m.UploadTime
	}
	return 0
}

type UserGameTagExt struct {
	GameId               uint32            `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	BackImgUrl           string            `protobuf:"bytes,2,opt,name=back_img_url,json=backImgUrl,proto3" json:"back_img_url,omitempty"`
	OptList              []*UserGameTagOpt `protobuf:"bytes,3,rep,name=opt_list,json=optList,proto3" json:"opt_list,omitempty"`
	ThumbImgUrl          string            `protobuf:"bytes,4,opt,name=thumb_img_url,json=thumbImgUrl,proto3" json:"thumb_img_url,omitempty"`
	GameNickname         string            `protobuf:"bytes,5,opt,name=game_nickname,json=gameNickname,proto3" json:"game_nickname,omitempty"`
	GameScreenshot       *GameScreenShot   `protobuf:"bytes,6,opt,name=game_screenshot,json=gameScreenshot,proto3" json:"game_screenshot,omitempty"`
	GameScreenshotList   []*GameScreenShot `protobuf:"bytes,7,rep,name=game_screenshot_list,json=gameScreenshotList,proto3" json:"game_screenshot_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserGameTagExt) Reset()         { *m = UserGameTagExt{} }
func (m *UserGameTagExt) String() string { return proto.CompactTextString(m) }
func (*UserGameTagExt) ProtoMessage()    {}
func (*UserGameTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{47}
}
func (m *UserGameTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTagExt.Unmarshal(m, b)
}
func (m *UserGameTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTagExt.Marshal(b, m, deterministic)
}
func (dst *UserGameTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTagExt.Merge(dst, src)
}
func (m *UserGameTagExt) XXX_Size() int {
	return xxx_messageInfo_UserGameTagExt.Size(m)
}
func (m *UserGameTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTagExt proto.InternalMessageInfo

func (m *UserGameTagExt) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *UserGameTagExt) GetBackImgUrl() string {
	if m != nil {
		return m.BackImgUrl
	}
	return ""
}

func (m *UserGameTagExt) GetOptList() []*UserGameTagOpt {
	if m != nil {
		return m.OptList
	}
	return nil
}

func (m *UserGameTagExt) GetThumbImgUrl() string {
	if m != nil {
		return m.ThumbImgUrl
	}
	return ""
}

func (m *UserGameTagExt) GetGameNickname() string {
	if m != nil {
		return m.GameNickname
	}
	return ""
}

func (m *UserGameTagExt) GetGameScreenshot() *GameScreenShot {
	if m != nil {
		return m.GameScreenshot
	}
	return nil
}

func (m *UserGameTagExt) GetGameScreenshotList() []*GameScreenShot {
	if m != nil {
		return m.GameScreenshotList
	}
	return nil
}

type UserGameTagOpt struct {
	OptName              string               `protobuf:"bytes,1,opt,name=opt_name,json=optName,proto3" json:"opt_name,omitempty"`
	OptId                uint32               `protobuf:"varint,2,opt,name=opt_id,json=optId,proto3" json:"opt_id,omitempty"`
	IsSupportMutiSet     bool                 `protobuf:"varint,3,opt,name=is_support_muti_set,json=isSupportMutiSet,proto3" json:"is_support_muti_set,omitempty"`
	ValueConfList        []string             `protobuf:"bytes,4,rep,name=value_conf_list,json=valueConfList,proto3" json:"value_conf_list,omitempty"`
	ValueUsersetList     []string             `protobuf:"bytes,5,rep,name=value_userset_list,json=valueUsersetList,proto3" json:"value_userset_list,omitempty"`
	SupportMutiSetCnt    uint32               `protobuf:"varint,6,opt,name=support_muti_set_cnt,json=supportMutiSetCnt,proto3" json:"support_muti_set_cnt,omitempty"`
	PartitionId          uint32               `protobuf:"varint,7,opt,name=partition_id,json=partitionId,proto3" json:"partition_id,omitempty"`
	PropValueList        []*GameOptSecondProp `protobuf:"bytes,8,rep,name=prop_value_list,json=propValueList,proto3" json:"prop_value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UserGameTagOpt) Reset()         { *m = UserGameTagOpt{} }
func (m *UserGameTagOpt) String() string { return proto.CompactTextString(m) }
func (*UserGameTagOpt) ProtoMessage()    {}
func (*UserGameTagOpt) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{48}
}
func (m *UserGameTagOpt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserGameTagOpt.Unmarshal(m, b)
}
func (m *UserGameTagOpt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserGameTagOpt.Marshal(b, m, deterministic)
}
func (dst *UserGameTagOpt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserGameTagOpt.Merge(dst, src)
}
func (m *UserGameTagOpt) XXX_Size() int {
	return xxx_messageInfo_UserGameTagOpt.Size(m)
}
func (m *UserGameTagOpt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserGameTagOpt.DiscardUnknown(m)
}

var xxx_messageInfo_UserGameTagOpt proto.InternalMessageInfo

func (m *UserGameTagOpt) GetOptName() string {
	if m != nil {
		return m.OptName
	}
	return ""
}

func (m *UserGameTagOpt) GetOptId() uint32 {
	if m != nil {
		return m.OptId
	}
	return 0
}

func (m *UserGameTagOpt) GetIsSupportMutiSet() bool {
	if m != nil {
		return m.IsSupportMutiSet
	}
	return false
}

func (m *UserGameTagOpt) GetValueConfList() []string {
	if m != nil {
		return m.ValueConfList
	}
	return nil
}

func (m *UserGameTagOpt) GetValueUsersetList() []string {
	if m != nil {
		return m.ValueUsersetList
	}
	return nil
}

func (m *UserGameTagOpt) GetSupportMutiSetCnt() uint32 {
	if m != nil {
		return m.SupportMutiSetCnt
	}
	return 0
}

func (m *UserGameTagOpt) GetPartitionId() uint32 {
	if m != nil {
		return m.PartitionId
	}
	return 0
}

func (m *UserGameTagOpt) GetPropValueList() []*GameOptSecondProp {
	if m != nil {
		return m.PropValueList
	}
	return nil
}

// 选项二级属性
type GameOptSecondProp struct {
	OptProp              string   `protobuf:"bytes,1,opt,name=opt_prop,json=optProp,proto3" json:"opt_prop,omitempty"`
	ValueList            []string `protobuf:"bytes,2,rep,name=value_list,json=valueList,proto3" json:"value_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GameOptSecondProp) Reset()         { *m = GameOptSecondProp{} }
func (m *GameOptSecondProp) String() string { return proto.CompactTextString(m) }
func (*GameOptSecondProp) ProtoMessage()    {}
func (*GameOptSecondProp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{49}
}
func (m *GameOptSecondProp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameOptSecondProp.Unmarshal(m, b)
}
func (m *GameOptSecondProp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameOptSecondProp.Marshal(b, m, deterministic)
}
func (dst *GameOptSecondProp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameOptSecondProp.Merge(dst, src)
}
func (m *GameOptSecondProp) XXX_Size() int {
	return xxx_messageInfo_GameOptSecondProp.Size(m)
}
func (m *GameOptSecondProp) XXX_DiscardUnknown() {
	xxx_messageInfo_GameOptSecondProp.DiscardUnknown(m)
}

var xxx_messageInfo_GameOptSecondProp proto.InternalMessageInfo

func (m *GameOptSecondProp) GetOptProp() string {
	if m != nil {
		return m.OptProp
	}
	return ""
}

func (m *GameOptSecondProp) GetValueList() []string {
	if m != nil {
		return m.ValueList
	}
	return nil
}

// 想要找什么样的人 类型的标签
type UserFindWhoTagExt struct {
	FindwhoType          uint32   `protobuf:"varint,1,opt,name=findwho_type,json=findwhoType,proto3" json:"findwho_type,omitempty"`
	AssignSex            uint32   `protobuf:"varint,2,opt,name=assign_sex,json=assignSex,proto3" json:"assign_sex,omitempty"`
	CorrelationPairTagId uint32   `protobuf:"varint,3,opt,name=correlation_pair_tag_id,json=correlationPairTagId,proto3" json:"correlation_pair_tag_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserFindWhoTagExt) Reset()         { *m = UserFindWhoTagExt{} }
func (m *UserFindWhoTagExt) String() string { return proto.CompactTextString(m) }
func (*UserFindWhoTagExt) ProtoMessage()    {}
func (*UserFindWhoTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{50}
}
func (m *UserFindWhoTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserFindWhoTagExt.Unmarshal(m, b)
}
func (m *UserFindWhoTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserFindWhoTagExt.Marshal(b, m, deterministic)
}
func (dst *UserFindWhoTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserFindWhoTagExt.Merge(dst, src)
}
func (m *UserFindWhoTagExt) XXX_Size() int {
	return xxx_messageInfo_UserFindWhoTagExt.Size(m)
}
func (m *UserFindWhoTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserFindWhoTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_UserFindWhoTagExt proto.InternalMessageInfo

func (m *UserFindWhoTagExt) GetFindwhoType() uint32 {
	if m != nil {
		return m.FindwhoType
	}
	return 0
}

func (m *UserFindWhoTagExt) GetAssignSex() uint32 {
	if m != nil {
		return m.AssignSex
	}
	return 0
}

func (m *UserFindWhoTagExt) GetCorrelationPairTagId() uint32 {
	if m != nil {
		return m.CorrelationPairTagId
	}
	return 0
}

type UserOptPersonalTagClassify struct {
	ClassifyId           uint32         `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	ClassifyName         string         `protobuf:"bytes,2,opt,name=classify_name,json=classifyName,proto3" json:"classify_name,omitempty"`
	TagList              []*UserTagBase `protobuf:"bytes,3,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	IsDel                bool           `protobuf:"varint,4,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserOptPersonalTagClassify) Reset()         { *m = UserOptPersonalTagClassify{} }
func (m *UserOptPersonalTagClassify) String() string { return proto.CompactTextString(m) }
func (*UserOptPersonalTagClassify) ProtoMessage()    {}
func (*UserOptPersonalTagClassify) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{51}
}
func (m *UserOptPersonalTagClassify) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOptPersonalTagClassify.Unmarshal(m, b)
}
func (m *UserOptPersonalTagClassify) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOptPersonalTagClassify.Marshal(b, m, deterministic)
}
func (dst *UserOptPersonalTagClassify) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOptPersonalTagClassify.Merge(dst, src)
}
func (m *UserOptPersonalTagClassify) XXX_Size() int {
	return xxx_messageInfo_UserOptPersonalTagClassify.Size(m)
}
func (m *UserOptPersonalTagClassify) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOptPersonalTagClassify.DiscardUnknown(m)
}

var xxx_messageInfo_UserOptPersonalTagClassify proto.InternalMessageInfo

func (m *UserOptPersonalTagClassify) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *UserOptPersonalTagClassify) GetClassifyName() string {
	if m != nil {
		return m.ClassifyName
	}
	return ""
}

func (m *UserOptPersonalTagClassify) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *UserOptPersonalTagClassify) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type UserOptPersonalTagExt struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserOptPersonalTagExt) Reset()         { *m = UserOptPersonalTagExt{} }
func (m *UserOptPersonalTagExt) String() string { return proto.CompactTextString(m) }
func (*UserOptPersonalTagExt) ProtoMessage()    {}
func (*UserOptPersonalTagExt) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{52}
}
func (m *UserOptPersonalTagExt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserOptPersonalTagExt.Unmarshal(m, b)
}
func (m *UserOptPersonalTagExt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserOptPersonalTagExt.Marshal(b, m, deterministic)
}
func (dst *UserOptPersonalTagExt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserOptPersonalTagExt.Merge(dst, src)
}
func (m *UserOptPersonalTagExt) XXX_Size() int {
	return xxx_messageInfo_UserOptPersonalTagExt.Size(m)
}
func (m *UserOptPersonalTagExt) XXX_DiscardUnknown() {
	xxx_messageInfo_UserOptPersonalTagExt.DiscardUnknown(m)
}

var xxx_messageInfo_UserOptPersonalTagExt proto.InternalMessageInfo

func (m *UserOptPersonalTagExt) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

// 获取指定用户的全部标签列表
type GetUserTagReq struct {
	TargetUid            uint32   `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	IsNeedTagExt         bool     `protobuf:"varint,2,opt,name=is_need_tag_ext,json=isNeedTagExt,proto3" json:"is_need_tag_ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTagReq) Reset()         { *m = GetUserTagReq{} }
func (m *GetUserTagReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTagReq) ProtoMessage()    {}
func (*GetUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{53}
}
func (m *GetUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTagReq.Unmarshal(m, b)
}
func (m *GetUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTagReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTagReq.Merge(dst, src)
}
func (m *GetUserTagReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTagReq.Size(m)
}
func (m *GetUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTagReq proto.InternalMessageInfo

func (m *GetUserTagReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *GetUserTagReq) GetIsNeedTagExt() bool {
	if m != nil {
		return m.IsNeedTagExt
	}
	return false
}

type UserTagList struct {
	Uid                  uint32         `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	TagList              []*UserTagBase `protobuf:"bytes,2,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *UserTagList) Reset()         { *m = UserTagList{} }
func (m *UserTagList) String() string { return proto.CompactTextString(m) }
func (*UserTagList) ProtoMessage()    {}
func (*UserTagList) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{54}
}
func (m *UserTagList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTagList.Unmarshal(m, b)
}
func (m *UserTagList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTagList.Marshal(b, m, deterministic)
}
func (dst *UserTagList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTagList.Merge(dst, src)
}
func (m *UserTagList) XXX_Size() int {
	return xxx_messageInfo_UserTagList.Size(m)
}
func (m *UserTagList) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTagList.DiscardUnknown(m)
}

var xxx_messageInfo_UserTagList proto.InternalMessageInfo

func (m *UserTagList) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserTagList) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

type GetUserTagResp struct {
	List                 *UserTagList `protobuf:"bytes,1,opt,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetUserTagResp) Reset()         { *m = GetUserTagResp{} }
func (m *GetUserTagResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTagResp) ProtoMessage()    {}
func (*GetUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{55}
}
func (m *GetUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTagResp.Unmarshal(m, b)
}
func (m *GetUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTagResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTagResp.Merge(dst, src)
}
func (m *GetUserTagResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTagResp.Size(m)
}
func (m *GetUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTagResp proto.InternalMessageInfo

func (m *GetUserTagResp) GetList() *UserTagList {
	if m != nil {
		return m.List
	}
	return nil
}

// 设置用户自己的标签列表
type SetUserTagReq struct {
	TargetUid            uint32         `protobuf:"varint,1,opt,name=target_uid,json=targetUid,proto3" json:"target_uid,omitempty"`
	TagType              uint32         `protobuf:"varint,2,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagList              []*UserTagBase `protobuf:"bytes,3,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	SettingCnt           uint32         `protobuf:"varint,4,opt,name=setting_cnt,json=settingCnt,proto3" json:"setting_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *SetUserTagReq) Reset()         { *m = SetUserTagReq{} }
func (m *SetUserTagReq) String() string { return proto.CompactTextString(m) }
func (*SetUserTagReq) ProtoMessage()    {}
func (*SetUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{56}
}
func (m *SetUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserTagReq.Unmarshal(m, b)
}
func (m *SetUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserTagReq.Marshal(b, m, deterministic)
}
func (dst *SetUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserTagReq.Merge(dst, src)
}
func (m *SetUserTagReq) XXX_Size() int {
	return xxx_messageInfo_SetUserTagReq.Size(m)
}
func (m *SetUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserTagReq proto.InternalMessageInfo

func (m *SetUserTagReq) GetTargetUid() uint32 {
	if m != nil {
		return m.TargetUid
	}
	return 0
}

func (m *SetUserTagReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *SetUserTagReq) GetTagList() []*UserTagBase {
	if m != nil {
		return m.TagList
	}
	return nil
}

func (m *SetUserTagReq) GetSettingCnt() uint32 {
	if m != nil {
		return m.SettingCnt
	}
	return 0
}

type SetUserTagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserTagResp) Reset()         { *m = SetUserTagResp{} }
func (m *SetUserTagResp) String() string { return proto.CompactTextString(m) }
func (*SetUserTagResp) ProtoMessage()    {}
func (*SetUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{57}
}
func (m *SetUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserTagResp.Unmarshal(m, b)
}
func (m *SetUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserTagResp.Marshal(b, m, deterministic)
}
func (dst *SetUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserTagResp.Merge(dst, src)
}
func (m *SetUserTagResp) XXX_Size() int {
	return xxx_messageInfo_SetUserTagResp.Size(m)
}
func (m *SetUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserTagResp proto.InternalMessageInfo

// 批量获取用户标签
type BatGetUserTagReq struct {
	UidList              []uint32 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	IsNeedTagExt         bool     `protobuf:"varint,2,opt,name=is_need_tag_ext,json=isNeedTagExt,proto3" json:"is_need_tag_ext,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatGetUserTagReq) Reset()         { *m = BatGetUserTagReq{} }
func (m *BatGetUserTagReq) String() string { return proto.CompactTextString(m) }
func (*BatGetUserTagReq) ProtoMessage()    {}
func (*BatGetUserTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{58}
}
func (m *BatGetUserTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserTagReq.Unmarshal(m, b)
}
func (m *BatGetUserTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserTagReq.Marshal(b, m, deterministic)
}
func (dst *BatGetUserTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserTagReq.Merge(dst, src)
}
func (m *BatGetUserTagReq) XXX_Size() int {
	return xxx_messageInfo_BatGetUserTagReq.Size(m)
}
func (m *BatGetUserTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserTagReq proto.InternalMessageInfo

func (m *BatGetUserTagReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatGetUserTagReq) GetIsNeedTagExt() bool {
	if m != nil {
		return m.IsNeedTagExt
	}
	return false
}

type BatGetUserTagResp struct {
	List                 []*UserTagList `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *BatGetUserTagResp) Reset()         { *m = BatGetUserTagResp{} }
func (m *BatGetUserTagResp) String() string { return proto.CompactTextString(m) }
func (*BatGetUserTagResp) ProtoMessage()    {}
func (*BatGetUserTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{59}
}
func (m *BatGetUserTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatGetUserTagResp.Unmarshal(m, b)
}
func (m *BatGetUserTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatGetUserTagResp.Marshal(b, m, deterministic)
}
func (dst *BatGetUserTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatGetUserTagResp.Merge(dst, src)
}
func (m *BatGetUserTagResp) XXX_Size() int {
	return xxx_messageInfo_BatGetUserTagResp.Size(m)
}
func (m *BatGetUserTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatGetUserTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatGetUserTagResp proto.InternalMessageInfo

func (m *BatGetUserTagResp) GetList() []*UserTagList {
	if m != nil {
		return m.List
	}
	return nil
}

// 获取配置标签列表
type GetUserTagConfigListFromCacheReq struct {
	TagType              uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTagConfigListFromCacheReq) Reset()         { *m = GetUserTagConfigListFromCacheReq{} }
func (m *GetUserTagConfigListFromCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTagConfigListFromCacheReq) ProtoMessage()    {}
func (*GetUserTagConfigListFromCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{60}
}
func (m *GetUserTagConfigListFromCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTagConfigListFromCacheReq.Unmarshal(m, b)
}
func (m *GetUserTagConfigListFromCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTagConfigListFromCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTagConfigListFromCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTagConfigListFromCacheReq.Merge(dst, src)
}
func (m *GetUserTagConfigListFromCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTagConfigListFromCacheReq.Size(m)
}
func (m *GetUserTagConfigListFromCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTagConfigListFromCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTagConfigListFromCacheReq proto.InternalMessageInfo

func (m *GetUserTagConfigListFromCacheReq) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

type UserTagConf struct {
	TagType              uint32   `protobuf:"varint,1,opt,name=tag_type,json=tagType,proto3" json:"tag_type,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name,omitempty"`
	TagId                uint32   `protobuf:"varint,3,opt,name=tag_id,json=tagId,proto3" json:"tag_id,omitempty"`
	TagInfo              []byte   `protobuf:"bytes,4,opt,name=tag_info,json=tagInfo,proto3" json:"tag_info,omitempty"`
	IsDel                bool     `protobuf:"varint,5,opt,name=is_del,json=isDel,proto3" json:"is_del,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserTagConf) Reset()         { *m = UserTagConf{} }
func (m *UserTagConf) String() string { return proto.CompactTextString(m) }
func (*UserTagConf) ProtoMessage()    {}
func (*UserTagConf) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{61}
}
func (m *UserTagConf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserTagConf.Unmarshal(m, b)
}
func (m *UserTagConf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserTagConf.Marshal(b, m, deterministic)
}
func (dst *UserTagConf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserTagConf.Merge(dst, src)
}
func (m *UserTagConf) XXX_Size() int {
	return xxx_messageInfo_UserTagConf.Size(m)
}
func (m *UserTagConf) XXX_DiscardUnknown() {
	xxx_messageInfo_UserTagConf.DiscardUnknown(m)
}

var xxx_messageInfo_UserTagConf proto.InternalMessageInfo

func (m *UserTagConf) GetTagType() uint32 {
	if m != nil {
		return m.TagType
	}
	return 0
}

func (m *UserTagConf) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *UserTagConf) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *UserTagConf) GetTagInfo() []byte {
	if m != nil {
		return m.TagInfo
	}
	return nil
}

func (m *UserTagConf) GetIsDel() bool {
	if m != nil {
		return m.IsDel
	}
	return false
}

type GetUserTagConfigListFromCacheResp struct {
	ConfList             []*UserTagConf `protobuf:"bytes,1,rep,name=conf_list,json=confList,proto3" json:"conf_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserTagConfigListFromCacheResp) Reset()         { *m = GetUserTagConfigListFromCacheResp{} }
func (m *GetUserTagConfigListFromCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTagConfigListFromCacheResp) ProtoMessage()    {}
func (*GetUserTagConfigListFromCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{62}
}
func (m *GetUserTagConfigListFromCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTagConfigListFromCacheResp.Unmarshal(m, b)
}
func (m *GetUserTagConfigListFromCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTagConfigListFromCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTagConfigListFromCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTagConfigListFromCacheResp.Merge(dst, src)
}
func (m *GetUserTagConfigListFromCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTagConfigListFromCacheResp.Size(m)
}
func (m *GetUserTagConfigListFromCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTagConfigListFromCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTagConfigListFromCacheResp proto.InternalMessageInfo

func (m *GetUserTagConfigListFromCacheResp) GetConfList() []*UserTagConf {
	if m != nil {
		return m.ConfList
	}
	return nil
}

type GetPersonalTagClassifyConfFromCacheReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetPersonalTagClassifyConfFromCacheReq) Reset() {
	*m = GetPersonalTagClassifyConfFromCacheReq{}
}
func (m *GetPersonalTagClassifyConfFromCacheReq) String() string { return proto.CompactTextString(m) }
func (*GetPersonalTagClassifyConfFromCacheReq) ProtoMessage()    {}
func (*GetPersonalTagClassifyConfFromCacheReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{63}
}
func (m *GetPersonalTagClassifyConfFromCacheReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalTagClassifyConfFromCacheReq.Unmarshal(m, b)
}
func (m *GetPersonalTagClassifyConfFromCacheReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalTagClassifyConfFromCacheReq.Marshal(b, m, deterministic)
}
func (dst *GetPersonalTagClassifyConfFromCacheReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalTagClassifyConfFromCacheReq.Merge(dst, src)
}
func (m *GetPersonalTagClassifyConfFromCacheReq) XXX_Size() int {
	return xxx_messageInfo_GetPersonalTagClassifyConfFromCacheReq.Size(m)
}
func (m *GetPersonalTagClassifyConfFromCacheReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalTagClassifyConfFromCacheReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalTagClassifyConfFromCacheReq proto.InternalMessageInfo

type GetPersonalTagClassifyConfFromCacheResp struct {
	ClassifyList         []*UserOptPersonalTagClassify `protobuf:"bytes,1,rep,name=classify_list,json=classifyList,proto3" json:"classify_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetPersonalTagClassifyConfFromCacheResp) Reset() {
	*m = GetPersonalTagClassifyConfFromCacheResp{}
}
func (m *GetPersonalTagClassifyConfFromCacheResp) String() string { return proto.CompactTextString(m) }
func (*GetPersonalTagClassifyConfFromCacheResp) ProtoMessage()    {}
func (*GetPersonalTagClassifyConfFromCacheResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{64}
}
func (m *GetPersonalTagClassifyConfFromCacheResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetPersonalTagClassifyConfFromCacheResp.Unmarshal(m, b)
}
func (m *GetPersonalTagClassifyConfFromCacheResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetPersonalTagClassifyConfFromCacheResp.Marshal(b, m, deterministic)
}
func (dst *GetPersonalTagClassifyConfFromCacheResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetPersonalTagClassifyConfFromCacheResp.Merge(dst, src)
}
func (m *GetPersonalTagClassifyConfFromCacheResp) XXX_Size() int {
	return xxx_messageInfo_GetPersonalTagClassifyConfFromCacheResp.Size(m)
}
func (m *GetPersonalTagClassifyConfFromCacheResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetPersonalTagClassifyConfFromCacheResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetPersonalTagClassifyConfFromCacheResp proto.InternalMessageInfo

func (m *GetPersonalTagClassifyConfFromCacheResp) GetClassifyList() []*UserOptPersonalTagClassify {
	if m != nil {
		return m.ClassifyList
	}
	return nil
}

// -----begin---------------旧后台没迁移过来前的中间产物，这个RegistGameRelatedTag和ClassifyTag只在注册时用到，填完上报数据，并没有在端内展示---------
type RegistGameRelatedTagData struct {
	GameTagId            uint32   `protobuf:"varint,1,opt,name=game_tag_id,json=gameTagId,proto3" json:"game_tag_id,omitempty"`
	TagList              []string `protobuf:"bytes,2,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegistGameRelatedTagData) Reset()         { *m = RegistGameRelatedTagData{} }
func (m *RegistGameRelatedTagData) String() string { return proto.CompactTextString(m) }
func (*RegistGameRelatedTagData) ProtoMessage()    {}
func (*RegistGameRelatedTagData) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{65}
}
func (m *RegistGameRelatedTagData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegistGameRelatedTagData.Unmarshal(m, b)
}
func (m *RegistGameRelatedTagData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegistGameRelatedTagData.Marshal(b, m, deterministic)
}
func (dst *RegistGameRelatedTagData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegistGameRelatedTagData.Merge(dst, src)
}
func (m *RegistGameRelatedTagData) XXX_Size() int {
	return xxx_messageInfo_RegistGameRelatedTagData.Size(m)
}
func (m *RegistGameRelatedTagData) XXX_DiscardUnknown() {
	xxx_messageInfo_RegistGameRelatedTagData.DiscardUnknown(m)
}

var xxx_messageInfo_RegistGameRelatedTagData proto.InternalMessageInfo

func (m *RegistGameRelatedTagData) GetGameTagId() uint32 {
	if m != nil {
		return m.GameTagId
	}
	return 0
}

func (m *RegistGameRelatedTagData) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type RegistMyTagData struct {
	ClassifyId           uint32   `protobuf:"varint,1,opt,name=classify_id,json=classifyId,proto3" json:"classify_id,omitempty"`
	TagList              []string `protobuf:"bytes,2,rep,name=tag_list,json=tagList,proto3" json:"tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RegistMyTagData) Reset()         { *m = RegistMyTagData{} }
func (m *RegistMyTagData) String() string { return proto.CompactTextString(m) }
func (*RegistMyTagData) ProtoMessage()    {}
func (*RegistMyTagData) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{66}
}
func (m *RegistMyTagData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RegistMyTagData.Unmarshal(m, b)
}
func (m *RegistMyTagData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RegistMyTagData.Marshal(b, m, deterministic)
}
func (dst *RegistMyTagData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RegistMyTagData.Merge(dst, src)
}
func (m *RegistMyTagData) XXX_Size() int {
	return xxx_messageInfo_RegistMyTagData.Size(m)
}
func (m *RegistMyTagData) XXX_DiscardUnknown() {
	xxx_messageInfo_RegistMyTagData.DiscardUnknown(m)
}

var xxx_messageInfo_RegistMyTagData proto.InternalMessageInfo

func (m *RegistMyTagData) GetClassifyId() uint32 {
	if m != nil {
		return m.ClassifyId
	}
	return 0
}

func (m *RegistMyTagData) GetTagList() []string {
	if m != nil {
		return m.TagList
	}
	return nil
}

type SetRegistTagReq struct {
	Uid                  uint32                      `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GameRelatedTagList   []*RegistGameRelatedTagData `protobuf:"bytes,2,rep,name=game_related_tag_list,json=gameRelatedTagList,proto3" json:"game_related_tag_list,omitempty"`
	MyTagList            []*RegistMyTagData          `protobuf:"bytes,3,rep,name=my_tag_list,json=myTagList,proto3" json:"my_tag_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *SetRegistTagReq) Reset()         { *m = SetRegistTagReq{} }
func (m *SetRegistTagReq) String() string { return proto.CompactTextString(m) }
func (*SetRegistTagReq) ProtoMessage()    {}
func (*SetRegistTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{67}
}
func (m *SetRegistTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRegistTagReq.Unmarshal(m, b)
}
func (m *SetRegistTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRegistTagReq.Marshal(b, m, deterministic)
}
func (dst *SetRegistTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRegistTagReq.Merge(dst, src)
}
func (m *SetRegistTagReq) XXX_Size() int {
	return xxx_messageInfo_SetRegistTagReq.Size(m)
}
func (m *SetRegistTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRegistTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetRegistTagReq proto.InternalMessageInfo

func (m *SetRegistTagReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SetRegistTagReq) GetGameRelatedTagList() []*RegistGameRelatedTagData {
	if m != nil {
		return m.GameRelatedTagList
	}
	return nil
}

func (m *SetRegistTagReq) GetMyTagList() []*RegistMyTagData {
	if m != nil {
		return m.MyTagList
	}
	return nil
}

type SetRegistTagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetRegistTagResp) Reset()         { *m = SetRegistTagResp{} }
func (m *SetRegistTagResp) String() string { return proto.CompactTextString(m) }
func (*SetRegistTagResp) ProtoMessage()    {}
func (*SetRegistTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{68}
}
func (m *SetRegistTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetRegistTagResp.Unmarshal(m, b)
}
func (m *SetRegistTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetRegistTagResp.Marshal(b, m, deterministic)
}
func (dst *SetRegistTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetRegistTagResp.Merge(dst, src)
}
func (m *SetRegistTagResp) XXX_Size() int {
	return xxx_messageInfo_SetRegistTagResp.Size(m)
}
func (m *SetRegistTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetRegistTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetRegistTagResp proto.InternalMessageInfo

type NotifyChannelPlayChangeReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	PlayType             uint32   `protobuf:"varint,2,opt,name=play_type,json=playType,proto3" json:"play_type,omitempty"`
	PlayName             string   `protobuf:"bytes,3,opt,name=play_name,json=playName,proto3" json:"play_name,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyChannelPlayChangeReq) Reset()         { *m = NotifyChannelPlayChangeReq{} }
func (m *NotifyChannelPlayChangeReq) String() string { return proto.CompactTextString(m) }
func (*NotifyChannelPlayChangeReq) ProtoMessage()    {}
func (*NotifyChannelPlayChangeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{69}
}
func (m *NotifyChannelPlayChangeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyChannelPlayChangeReq.Unmarshal(m, b)
}
func (m *NotifyChannelPlayChangeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyChannelPlayChangeReq.Marshal(b, m, deterministic)
}
func (dst *NotifyChannelPlayChangeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyChannelPlayChangeReq.Merge(dst, src)
}
func (m *NotifyChannelPlayChangeReq) XXX_Size() int {
	return xxx_messageInfo_NotifyChannelPlayChangeReq.Size(m)
}
func (m *NotifyChannelPlayChangeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyChannelPlayChangeReq.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyChannelPlayChangeReq proto.InternalMessageInfo

func (m *NotifyChannelPlayChangeReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *NotifyChannelPlayChangeReq) GetPlayType() uint32 {
	if m != nil {
		return m.PlayType
	}
	return 0
}

func (m *NotifyChannelPlayChangeReq) GetPlayName() string {
	if m != nil {
		return m.PlayName
	}
	return ""
}

type NotifyChannelPlayChangeResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NotifyChannelPlayChangeResp) Reset()         { *m = NotifyChannelPlayChangeResp{} }
func (m *NotifyChannelPlayChangeResp) String() string { return proto.CompactTextString(m) }
func (*NotifyChannelPlayChangeResp) ProtoMessage()    {}
func (*NotifyChannelPlayChangeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{70}
}
func (m *NotifyChannelPlayChangeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NotifyChannelPlayChangeResp.Unmarshal(m, b)
}
func (m *NotifyChannelPlayChangeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NotifyChannelPlayChangeResp.Marshal(b, m, deterministic)
}
func (dst *NotifyChannelPlayChangeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NotifyChannelPlayChangeResp.Merge(dst, src)
}
func (m *NotifyChannelPlayChangeResp) XXX_Size() int {
	return xxx_messageInfo_NotifyChannelPlayChangeResp.Size(m)
}
func (m *NotifyChannelPlayChangeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NotifyChannelPlayChangeResp.DiscardUnknown(m)
}

var xxx_messageInfo_NotifyChannelPlayChangeResp proto.InternalMessageInfo

type GetRecommendStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendStatusReq) Reset()         { *m = GetRecommendStatusReq{} }
func (m *GetRecommendStatusReq) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStatusReq) ProtoMessage()    {}
func (*GetRecommendStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{71}
}
func (m *GetRecommendStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendStatusReq.Unmarshal(m, b)
}
func (m *GetRecommendStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendStatusReq.Marshal(b, m, deterministic)
}
func (dst *GetRecommendStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendStatusReq.Merge(dst, src)
}
func (m *GetRecommendStatusReq) XXX_Size() int {
	return xxx_messageInfo_GetRecommendStatusReq.Size(m)
}
func (m *GetRecommendStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendStatusReq proto.InternalMessageInfo

func (m *GetRecommendStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetRecommendStatusResp struct {
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetRecommendStatusResp) Reset()         { *m = GetRecommendStatusResp{} }
func (m *GetRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*GetRecommendStatusResp) ProtoMessage()    {}
func (*GetRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{72}
}
func (m *GetRecommendStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetRecommendStatusResp.Unmarshal(m, b)
}
func (m *GetRecommendStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetRecommendStatusResp.Marshal(b, m, deterministic)
}
func (dst *GetRecommendStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetRecommendStatusResp.Merge(dst, src)
}
func (m *GetRecommendStatusResp) XXX_Size() int {
	return xxx_messageInfo_GetRecommendStatusResp.Size(m)
}
func (m *GetRecommendStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetRecommendStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetRecommendStatusResp proto.InternalMessageInfo

func (m *GetRecommendStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 改变推荐状态（是否向别人推荐自己）
type ChangeRecommendStatusReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeRecommendStatusReq) Reset()         { *m = ChangeRecommendStatusReq{} }
func (m *ChangeRecommendStatusReq) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusReq) ProtoMessage()    {}
func (*ChangeRecommendStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{73}
}
func (m *ChangeRecommendStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRecommendStatusReq.Unmarshal(m, b)
}
func (m *ChangeRecommendStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRecommendStatusReq.Marshal(b, m, deterministic)
}
func (dst *ChangeRecommendStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRecommendStatusReq.Merge(dst, src)
}
func (m *ChangeRecommendStatusReq) XXX_Size() int {
	return xxx_messageInfo_ChangeRecommendStatusReq.Size(m)
}
func (m *ChangeRecommendStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRecommendStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRecommendStatusReq proto.InternalMessageInfo

func (m *ChangeRecommendStatusReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ChangeRecommendStatusReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type ChangeRecommendStatusResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ChangeRecommendStatusResp) Reset()         { *m = ChangeRecommendStatusResp{} }
func (m *ChangeRecommendStatusResp) String() string { return proto.CompactTextString(m) }
func (*ChangeRecommendStatusResp) ProtoMessage()    {}
func (*ChangeRecommendStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_user_tag_go_a64747235eb9db54, []int{74}
}
func (m *ChangeRecommendStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChangeRecommendStatusResp.Unmarshal(m, b)
}
func (m *ChangeRecommendStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChangeRecommendStatusResp.Marshal(b, m, deterministic)
}
func (dst *ChangeRecommendStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChangeRecommendStatusResp.Merge(dst, src)
}
func (m *ChangeRecommendStatusResp) XXX_Size() int {
	return xxx_messageInfo_ChangeRecommendStatusResp.Size(m)
}
func (m *ChangeRecommendStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChangeRecommendStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChangeRecommendStatusResp proto.InternalMessageInfo

func init() {
	proto.RegisterType((*GetGameTypeListReq)(nil), "user_tag_go.GetGameTypeListReq")
	proto.RegisterType((*GameType)(nil), "user_tag_go.GameType")
	proto.RegisterType((*GetGameTypeListResp)(nil), "user_tag_go.GetGameTypeListResp")
	proto.RegisterType((*AddRegistGameRelatedTagConfReq)(nil), "user_tag_go.AddRegistGameRelatedTagConfReq")
	proto.RegisterType((*AddRegistGameRelatedTagConfResp)(nil), "user_tag_go.AddRegistGameRelatedTagConfResp")
	proto.RegisterType((*DeleteRegistGameRelatedTagConfReq)(nil), "user_tag_go.DeleteRegistGameRelatedTagConfReq")
	proto.RegisterType((*DeleteRegistGameRelatedTagConfResp)(nil), "user_tag_go.DeleteRegistGameRelatedTagConfResp")
	proto.RegisterType((*GetRegistGameRelatedTagConfReq)(nil), "user_tag_go.GetRegistGameRelatedTagConfReq")
	proto.RegisterType((*GetRegistGameRelatedTagConfResp)(nil), "user_tag_go.GetRegistGameRelatedTagConfResp")
	proto.RegisterType((*GetAllRegistGameRelatedTagConfReq)(nil), "user_tag_go.GetAllRegistGameRelatedTagConfReq")
	proto.RegisterType((*RegistGameRelatedTagConf)(nil), "user_tag_go.RegistGameRelatedTagConf")
	proto.RegisterType((*GetAllRegistGameRelatedTagConfResp)(nil), "user_tag_go.GetAllRegistGameRelatedTagConfResp")
	proto.RegisterType((*GetClassifyListReq)(nil), "user_tag_go.GetClassifyListReq")
	proto.RegisterType((*ClassifyInfo)(nil), "user_tag_go.ClassifyInfo")
	proto.RegisterType((*GetClassifyListResp)(nil), "user_tag_go.GetClassifyListResp")
	proto.RegisterType((*AddClassifyTagConfReq)(nil), "user_tag_go.AddClassifyTagConfReq")
	proto.RegisterType((*AddClassifyTagConfResp)(nil), "user_tag_go.AddClassifyTagConfResp")
	proto.RegisterType((*GetClassifyTagConfReq)(nil), "user_tag_go.GetClassifyTagConfReq")
	proto.RegisterType((*GetClassifyTagConfResp)(nil), "user_tag_go.GetClassifyTagConfResp")
	proto.RegisterType((*ClassifyTagInfo)(nil), "user_tag_go.ClassifyTagInfo")
	proto.RegisterType((*GetAllClassifyTagConfReq)(nil), "user_tag_go.GetAllClassifyTagConfReq")
	proto.RegisterType((*GetAllClassifyTagConfResp)(nil), "user_tag_go.GetAllClassifyTagConfResp")
	proto.RegisterType((*UserAgeTagExt)(nil), "user_tag_go.UserAgeTagExt")
	proto.RegisterType((*CreateTagConfigReq)(nil), "user_tag_go.CreateTagConfigReq")
	proto.RegisterType((*CreateTagConfigResp)(nil), "user_tag_go.CreateTagConfigResp")
	proto.RegisterType((*ModifyTagConfigReq)(nil), "user_tag_go.ModifyTagConfigReq")
	proto.RegisterType((*ModifyTagConfigResp)(nil), "user_tag_go.ModifyTagConfigResp")
	proto.RegisterType((*DelTagConfigReq)(nil), "user_tag_go.DelTagConfigReq")
	proto.RegisterType((*DelTagConfigResp)(nil), "user_tag_go.DelTagConfigResp")
	proto.RegisterType((*SortTagConfigReq)(nil), "user_tag_go.SortTagConfigReq")
	proto.RegisterType((*SortTagConfigResp)(nil), "user_tag_go.SortTagConfigResp")
	proto.RegisterType((*CreateFindWhoTypeTagConfigReq)(nil), "user_tag_go.CreateFindWhoTypeTagConfigReq")
	proto.RegisterType((*CreateFindWhoTypeTagConfigResp)(nil), "user_tag_go.CreateFindWhoTypeTagConfigResp")
	proto.RegisterType((*CreateOptPersonalTagClassifyReq)(nil), "user_tag_go.CreateOptPersonalTagClassifyReq")
	proto.RegisterType((*CreateOptPersonalTagClassifyResp)(nil), "user_tag_go.CreateOptPersonalTagClassifyResp")
	proto.RegisterType((*GetOptPersonalTagClassifyListReq)(nil), "user_tag_go.GetOptPersonalTagClassifyListReq")
	proto.RegisterType((*GetOptPersonalTagClassifyListResp)(nil), "user_tag_go.GetOptPersonalTagClassifyListResp")
	proto.RegisterType((*GetOptPersonalTagByClassifyReq)(nil), "user_tag_go.GetOptPersonalTagByClassifyReq")
	proto.RegisterType((*GetOptPersonalTagByClassifyResp)(nil), "user_tag_go.GetOptPersonalTagByClassifyResp")
	proto.RegisterType((*ModifyOptPersonalTagClassifyNameReq)(nil), "user_tag_go.ModifyOptPersonalTagClassifyNameReq")
	proto.RegisterType((*ModifyOptPersonalTagClassifyNameResp)(nil), "user_tag_go.ModifyOptPersonalTagClassifyNameResp")
	proto.RegisterType((*SortOptPersonalTagClassifyReq)(nil), "user_tag_go.SortOptPersonalTagClassifyReq")
	proto.RegisterType((*SortOptPersonalTagClassifyResp)(nil), "user_tag_go.SortOptPersonalTagClassifyResp")
	proto.RegisterType((*DelOptPersonalTagClassifyReq)(nil), "user_tag_go.DelOptPersonalTagClassifyReq")
	proto.RegisterType((*DelOptPersonalTagClassifyResp)(nil), "user_tag_go.DelOptPersonalTagClassifyResp")
	proto.RegisterType((*UserTagBase)(nil), "user_tag_go.UserTagBase")
	proto.RegisterType((*GameScreenShot)(nil), "user_tag_go.GameScreenShot")
	proto.RegisterType((*UserGameTagExt)(nil), "user_tag_go.UserGameTagExt")
	proto.RegisterType((*UserGameTagOpt)(nil), "user_tag_go.UserGameTagOpt")
	proto.RegisterType((*GameOptSecondProp)(nil), "user_tag_go.GameOptSecondProp")
	proto.RegisterType((*UserFindWhoTagExt)(nil), "user_tag_go.UserFindWhoTagExt")
	proto.RegisterType((*UserOptPersonalTagClassify)(nil), "user_tag_go.UserOptPersonalTagClassify")
	proto.RegisterType((*UserOptPersonalTagExt)(nil), "user_tag_go.UserOptPersonalTagExt")
	proto.RegisterType((*GetUserTagReq)(nil), "user_tag_go.GetUserTagReq")
	proto.RegisterType((*UserTagList)(nil), "user_tag_go.UserTagList")
	proto.RegisterType((*GetUserTagResp)(nil), "user_tag_go.GetUserTagResp")
	proto.RegisterType((*SetUserTagReq)(nil), "user_tag_go.SetUserTagReq")
	proto.RegisterType((*SetUserTagResp)(nil), "user_tag_go.SetUserTagResp")
	proto.RegisterType((*BatGetUserTagReq)(nil), "user_tag_go.BatGetUserTagReq")
	proto.RegisterType((*BatGetUserTagResp)(nil), "user_tag_go.BatGetUserTagResp")
	proto.RegisterType((*GetUserTagConfigListFromCacheReq)(nil), "user_tag_go.GetUserTagConfigListFromCacheReq")
	proto.RegisterType((*UserTagConf)(nil), "user_tag_go.UserTagConf")
	proto.RegisterType((*GetUserTagConfigListFromCacheResp)(nil), "user_tag_go.GetUserTagConfigListFromCacheResp")
	proto.RegisterType((*GetPersonalTagClassifyConfFromCacheReq)(nil), "user_tag_go.GetPersonalTagClassifyConfFromCacheReq")
	proto.RegisterType((*GetPersonalTagClassifyConfFromCacheResp)(nil), "user_tag_go.GetPersonalTagClassifyConfFromCacheResp")
	proto.RegisterType((*RegistGameRelatedTagData)(nil), "user_tag_go.RegistGameRelatedTagData")
	proto.RegisterType((*RegistMyTagData)(nil), "user_tag_go.RegistMyTagData")
	proto.RegisterType((*SetRegistTagReq)(nil), "user_tag_go.SetRegistTagReq")
	proto.RegisterType((*SetRegistTagResp)(nil), "user_tag_go.SetRegistTagResp")
	proto.RegisterType((*NotifyChannelPlayChangeReq)(nil), "user_tag_go.NotifyChannelPlayChangeReq")
	proto.RegisterType((*NotifyChannelPlayChangeResp)(nil), "user_tag_go.NotifyChannelPlayChangeResp")
	proto.RegisterType((*GetRecommendStatusReq)(nil), "user_tag_go.GetRecommendStatusReq")
	proto.RegisterType((*GetRecommendStatusResp)(nil), "user_tag_go.GetRecommendStatusResp")
	proto.RegisterType((*ChangeRecommendStatusReq)(nil), "user_tag_go.ChangeRecommendStatusReq")
	proto.RegisterType((*ChangeRecommendStatusResp)(nil), "user_tag_go.ChangeRecommendStatusResp")
	proto.RegisterEnum("user_tag_go.ClassifyTagType", ClassifyTagType_name, ClassifyTagType_value)
	proto.RegisterEnum("user_tag_go.EUserTagFindWhoType", EUserTagFindWhoType_name, EUserTagFindWhoType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserTagConfMgrClient is the client API for UserTagConfMgr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserTagConfMgrClient interface {
	// --------------------旧后台没迁移过来前的中间产物，这个RegistGameRelatedTag和ClassifyTag只在注册时用到，填完上报数据，并没有在端内展示---------
	GetGameTypeList(ctx context.Context, in *GetGameTypeListReq, opts ...grpc.CallOption) (*GetGameTypeListResp, error)
	// 注册时填的，和游戏关联但又不属于游戏卡的游戏标签(不展示在端内游戏卡上)........实在拗口........
	// Add/modify接口,没有则创建，有则覆盖
	AddRegistGameRelatedTagConf(ctx context.Context, in *AddRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*AddRegistGameRelatedTagConfResp, error)
	DeleteRegistGameRelatedTagConf(ctx context.Context, in *DeleteRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*DeleteRegistGameRelatedTagConfResp, error)
	GetRegistGameRelatedTagConf(ctx context.Context, in *GetRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*GetRegistGameRelatedTagConfResp, error)
	GetAllRegistGameRelatedTagConf(ctx context.Context, in *GetAllRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*GetAllRegistGameRelatedTagConfResp, error)
	GetClassifyList(ctx context.Context, in *GetClassifyListReq, opts ...grpc.CallOption) (*GetClassifyListResp, error)
	// Add/modify接口,没有则创建，有则覆盖
	AddClassifyTagConf(ctx context.Context, in *AddClassifyTagConfReq, opts ...grpc.CallOption) (*AddClassifyTagConfResp, error)
	GetClassifyTagConf(ctx context.Context, in *GetClassifyTagConfReq, opts ...grpc.CallOption) (*GetClassifyTagConfResp, error)
	GetAllClassifyTagConf(ctx context.Context, in *GetAllClassifyTagConfReq, opts ...grpc.CallOption) (*GetAllClassifyTagConfResp, error)
	// 一般标签创建（ 年龄标签 游戏标签 个性标签 用这个创建）
	CreateTagConfig(ctx context.Context, in *CreateTagConfigReq, opts ...grpc.CallOption) (*CreateTagConfigResp, error)
	// 一般标签修改（ 就是全量覆盖，年龄标签 游戏标签 FindWho标签 个性标签 都可以用这个修改，包括修改名称 修改游戏属性 修改标签分类）
	ModifyTagConfig(ctx context.Context, in *ModifyTagConfigReq, opts ...grpc.CallOption) (*ModifyTagConfigResp, error)
	// 标签 删除 / 恢复
	DelTagConfig(ctx context.Context, in *DelTagConfigReq, opts ...grpc.CallOption) (*DelTagConfigResp, error)
	// 标签排序（ 年龄标签 游戏标签 FindWho标签的排序）
	SortTagConfig(ctx context.Context, in *SortTagConfigReq, opts ...grpc.CallOption) (*SortTagConfigResp, error)
	// findwho 标签的创建
	CreateFindWhoTypeTagConfig(ctx context.Context, in *CreateFindWhoTypeTagConfigReq, opts ...grpc.CallOption) (*CreateFindWhoTypeTagConfigResp, error)
	// 创建个性标签的分类
	CreateOptPersonalTagClassify(ctx context.Context, in *CreateOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*CreateOptPersonalTagClassifyResp, error)
	// 获取个性标签的分类列表
	GetOptPersonalTagClassifyList(ctx context.Context, in *GetOptPersonalTagClassifyListReq, opts ...grpc.CallOption) (*GetOptPersonalTagClassifyListResp, error)
	// 根据分类获取 这个分类下的个性标签的列表
	GetOptPersonalTagByClassify(ctx context.Context, in *GetOptPersonalTagByClassifyReq, opts ...grpc.CallOption) (*GetOptPersonalTagByClassifyResp, error)
	// 修改指定个性标签的分类的名字
	ModifyOptPersonalTagClassifyName(ctx context.Context, in *ModifyOptPersonalTagClassifyNameReq, opts ...grpc.CallOption) (*ModifyOptPersonalTagClassifyNameResp, error)
	// 个性标签的分类的排序
	SortOptPersonalTagClassify(ctx context.Context, in *SortOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*SortOptPersonalTagClassifyResp, error)
	// 个性标签的分类的 删除 / 恢复
	DelOptPersonalTagClassify(ctx context.Context, in *DelOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*DelOptPersonalTagClassifyResp, error)
}

type userTagConfMgrClient struct {
	cc *grpc.ClientConn
}

func NewUserTagConfMgrClient(cc *grpc.ClientConn) UserTagConfMgrClient {
	return &userTagConfMgrClient{cc}
}

func (c *userTagConfMgrClient) GetGameTypeList(ctx context.Context, in *GetGameTypeListReq, opts ...grpc.CallOption) (*GetGameTypeListResp, error) {
	out := new(GetGameTypeListResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetGameTypeList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) AddRegistGameRelatedTagConf(ctx context.Context, in *AddRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*AddRegistGameRelatedTagConfResp, error) {
	out := new(AddRegistGameRelatedTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/AddRegistGameRelatedTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) DeleteRegistGameRelatedTagConf(ctx context.Context, in *DeleteRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*DeleteRegistGameRelatedTagConfResp, error) {
	out := new(DeleteRegistGameRelatedTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/DeleteRegistGameRelatedTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetRegistGameRelatedTagConf(ctx context.Context, in *GetRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*GetRegistGameRelatedTagConfResp, error) {
	out := new(GetRegistGameRelatedTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetRegistGameRelatedTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetAllRegistGameRelatedTagConf(ctx context.Context, in *GetAllRegistGameRelatedTagConfReq, opts ...grpc.CallOption) (*GetAllRegistGameRelatedTagConfResp, error) {
	out := new(GetAllRegistGameRelatedTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetAllRegistGameRelatedTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetClassifyList(ctx context.Context, in *GetClassifyListReq, opts ...grpc.CallOption) (*GetClassifyListResp, error) {
	out := new(GetClassifyListResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetClassifyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) AddClassifyTagConf(ctx context.Context, in *AddClassifyTagConfReq, opts ...grpc.CallOption) (*AddClassifyTagConfResp, error) {
	out := new(AddClassifyTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/AddClassifyTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetClassifyTagConf(ctx context.Context, in *GetClassifyTagConfReq, opts ...grpc.CallOption) (*GetClassifyTagConfResp, error) {
	out := new(GetClassifyTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetClassifyTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetAllClassifyTagConf(ctx context.Context, in *GetAllClassifyTagConfReq, opts ...grpc.CallOption) (*GetAllClassifyTagConfResp, error) {
	out := new(GetAllClassifyTagConfResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetAllClassifyTagConf", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) CreateTagConfig(ctx context.Context, in *CreateTagConfigReq, opts ...grpc.CallOption) (*CreateTagConfigResp, error) {
	out := new(CreateTagConfigResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/CreateTagConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) ModifyTagConfig(ctx context.Context, in *ModifyTagConfigReq, opts ...grpc.CallOption) (*ModifyTagConfigResp, error) {
	out := new(ModifyTagConfigResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/ModifyTagConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) DelTagConfig(ctx context.Context, in *DelTagConfigReq, opts ...grpc.CallOption) (*DelTagConfigResp, error) {
	out := new(DelTagConfigResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/DelTagConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) SortTagConfig(ctx context.Context, in *SortTagConfigReq, opts ...grpc.CallOption) (*SortTagConfigResp, error) {
	out := new(SortTagConfigResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/SortTagConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) CreateFindWhoTypeTagConfig(ctx context.Context, in *CreateFindWhoTypeTagConfigReq, opts ...grpc.CallOption) (*CreateFindWhoTypeTagConfigResp, error) {
	out := new(CreateFindWhoTypeTagConfigResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/CreateFindWhoTypeTagConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) CreateOptPersonalTagClassify(ctx context.Context, in *CreateOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*CreateOptPersonalTagClassifyResp, error) {
	out := new(CreateOptPersonalTagClassifyResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/CreateOptPersonalTagClassify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetOptPersonalTagClassifyList(ctx context.Context, in *GetOptPersonalTagClassifyListReq, opts ...grpc.CallOption) (*GetOptPersonalTagClassifyListResp, error) {
	out := new(GetOptPersonalTagClassifyListResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetOptPersonalTagClassifyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) GetOptPersonalTagByClassify(ctx context.Context, in *GetOptPersonalTagByClassifyReq, opts ...grpc.CallOption) (*GetOptPersonalTagByClassifyResp, error) {
	out := new(GetOptPersonalTagByClassifyResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/GetOptPersonalTagByClassify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) ModifyOptPersonalTagClassifyName(ctx context.Context, in *ModifyOptPersonalTagClassifyNameReq, opts ...grpc.CallOption) (*ModifyOptPersonalTagClassifyNameResp, error) {
	out := new(ModifyOptPersonalTagClassifyNameResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/ModifyOptPersonalTagClassifyName", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) SortOptPersonalTagClassify(ctx context.Context, in *SortOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*SortOptPersonalTagClassifyResp, error) {
	out := new(SortOptPersonalTagClassifyResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/SortOptPersonalTagClassify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagConfMgrClient) DelOptPersonalTagClassify(ctx context.Context, in *DelOptPersonalTagClassifyReq, opts ...grpc.CallOption) (*DelOptPersonalTagClassifyResp, error) {
	out := new(DelOptPersonalTagClassifyResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagConfMgr/DelOptPersonalTagClassify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTagConfMgrServer is the server API for UserTagConfMgr service.
type UserTagConfMgrServer interface {
	// --------------------旧后台没迁移过来前的中间产物，这个RegistGameRelatedTag和ClassifyTag只在注册时用到，填完上报数据，并没有在端内展示---------
	GetGameTypeList(context.Context, *GetGameTypeListReq) (*GetGameTypeListResp, error)
	// 注册时填的，和游戏关联但又不属于游戏卡的游戏标签(不展示在端内游戏卡上)........实在拗口........
	// Add/modify接口,没有则创建，有则覆盖
	AddRegistGameRelatedTagConf(context.Context, *AddRegistGameRelatedTagConfReq) (*AddRegistGameRelatedTagConfResp, error)
	DeleteRegistGameRelatedTagConf(context.Context, *DeleteRegistGameRelatedTagConfReq) (*DeleteRegistGameRelatedTagConfResp, error)
	GetRegistGameRelatedTagConf(context.Context, *GetRegistGameRelatedTagConfReq) (*GetRegistGameRelatedTagConfResp, error)
	GetAllRegistGameRelatedTagConf(context.Context, *GetAllRegistGameRelatedTagConfReq) (*GetAllRegistGameRelatedTagConfResp, error)
	GetClassifyList(context.Context, *GetClassifyListReq) (*GetClassifyListResp, error)
	// Add/modify接口,没有则创建，有则覆盖
	AddClassifyTagConf(context.Context, *AddClassifyTagConfReq) (*AddClassifyTagConfResp, error)
	GetClassifyTagConf(context.Context, *GetClassifyTagConfReq) (*GetClassifyTagConfResp, error)
	GetAllClassifyTagConf(context.Context, *GetAllClassifyTagConfReq) (*GetAllClassifyTagConfResp, error)
	// 一般标签创建（ 年龄标签 游戏标签 个性标签 用这个创建）
	CreateTagConfig(context.Context, *CreateTagConfigReq) (*CreateTagConfigResp, error)
	// 一般标签修改（ 就是全量覆盖，年龄标签 游戏标签 FindWho标签 个性标签 都可以用这个修改，包括修改名称 修改游戏属性 修改标签分类）
	ModifyTagConfig(context.Context, *ModifyTagConfigReq) (*ModifyTagConfigResp, error)
	// 标签 删除 / 恢复
	DelTagConfig(context.Context, *DelTagConfigReq) (*DelTagConfigResp, error)
	// 标签排序（ 年龄标签 游戏标签 FindWho标签的排序）
	SortTagConfig(context.Context, *SortTagConfigReq) (*SortTagConfigResp, error)
	// findwho 标签的创建
	CreateFindWhoTypeTagConfig(context.Context, *CreateFindWhoTypeTagConfigReq) (*CreateFindWhoTypeTagConfigResp, error)
	// 创建个性标签的分类
	CreateOptPersonalTagClassify(context.Context, *CreateOptPersonalTagClassifyReq) (*CreateOptPersonalTagClassifyResp, error)
	// 获取个性标签的分类列表
	GetOptPersonalTagClassifyList(context.Context, *GetOptPersonalTagClassifyListReq) (*GetOptPersonalTagClassifyListResp, error)
	// 根据分类获取 这个分类下的个性标签的列表
	GetOptPersonalTagByClassify(context.Context, *GetOptPersonalTagByClassifyReq) (*GetOptPersonalTagByClassifyResp, error)
	// 修改指定个性标签的分类的名字
	ModifyOptPersonalTagClassifyName(context.Context, *ModifyOptPersonalTagClassifyNameReq) (*ModifyOptPersonalTagClassifyNameResp, error)
	// 个性标签的分类的排序
	SortOptPersonalTagClassify(context.Context, *SortOptPersonalTagClassifyReq) (*SortOptPersonalTagClassifyResp, error)
	// 个性标签的分类的 删除 / 恢复
	DelOptPersonalTagClassify(context.Context, *DelOptPersonalTagClassifyReq) (*DelOptPersonalTagClassifyResp, error)
}

func RegisterUserTagConfMgrServer(s *grpc.Server, srv UserTagConfMgrServer) {
	s.RegisterService(&_UserTagConfMgr_serviceDesc, srv)
}

func _UserTagConfMgr_GetGameTypeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGameTypeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetGameTypeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetGameTypeList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetGameTypeList(ctx, req.(*GetGameTypeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_AddRegistGameRelatedTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddRegistGameRelatedTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).AddRegistGameRelatedTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/AddRegistGameRelatedTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).AddRegistGameRelatedTagConf(ctx, req.(*AddRegistGameRelatedTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_DeleteRegistGameRelatedTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRegistGameRelatedTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).DeleteRegistGameRelatedTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/DeleteRegistGameRelatedTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).DeleteRegistGameRelatedTagConf(ctx, req.(*DeleteRegistGameRelatedTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetRegistGameRelatedTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRegistGameRelatedTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetRegistGameRelatedTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetRegistGameRelatedTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetRegistGameRelatedTagConf(ctx, req.(*GetRegistGameRelatedTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetAllRegistGameRelatedTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllRegistGameRelatedTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetAllRegistGameRelatedTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetAllRegistGameRelatedTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetAllRegistGameRelatedTagConf(ctx, req.(*GetAllRegistGameRelatedTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetClassifyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetClassifyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetClassifyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetClassifyList(ctx, req.(*GetClassifyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_AddClassifyTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddClassifyTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).AddClassifyTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/AddClassifyTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).AddClassifyTagConf(ctx, req.(*AddClassifyTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetClassifyTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClassifyTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetClassifyTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetClassifyTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetClassifyTagConf(ctx, req.(*GetClassifyTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetAllClassifyTagConf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllClassifyTagConfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetAllClassifyTagConf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetAllClassifyTagConf",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetAllClassifyTagConf(ctx, req.(*GetAllClassifyTagConfReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_CreateTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).CreateTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/CreateTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).CreateTagConfig(ctx, req.(*CreateTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_ModifyTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).ModifyTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/ModifyTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).ModifyTagConfig(ctx, req.(*ModifyTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_DelTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).DelTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/DelTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).DelTagConfig(ctx, req.(*DelTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_SortTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).SortTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/SortTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).SortTagConfig(ctx, req.(*SortTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_CreateFindWhoTypeTagConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFindWhoTypeTagConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).CreateFindWhoTypeTagConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/CreateFindWhoTypeTagConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).CreateFindWhoTypeTagConfig(ctx, req.(*CreateFindWhoTypeTagConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_CreateOptPersonalTagClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOptPersonalTagClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).CreateOptPersonalTagClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/CreateOptPersonalTagClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).CreateOptPersonalTagClassify(ctx, req.(*CreateOptPersonalTagClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetOptPersonalTagClassifyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOptPersonalTagClassifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetOptPersonalTagClassifyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetOptPersonalTagClassifyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetOptPersonalTagClassifyList(ctx, req.(*GetOptPersonalTagClassifyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_GetOptPersonalTagByClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOptPersonalTagByClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).GetOptPersonalTagByClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/GetOptPersonalTagByClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).GetOptPersonalTagByClassify(ctx, req.(*GetOptPersonalTagByClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_ModifyOptPersonalTagClassifyName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyOptPersonalTagClassifyNameReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).ModifyOptPersonalTagClassifyName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/ModifyOptPersonalTagClassifyName",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).ModifyOptPersonalTagClassifyName(ctx, req.(*ModifyOptPersonalTagClassifyNameReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_SortOptPersonalTagClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SortOptPersonalTagClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).SortOptPersonalTagClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/SortOptPersonalTagClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).SortOptPersonalTagClassify(ctx, req.(*SortOptPersonalTagClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagConfMgr_DelOptPersonalTagClassify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelOptPersonalTagClassifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagConfMgrServer).DelOptPersonalTagClassify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagConfMgr/DelOptPersonalTagClassify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagConfMgrServer).DelOptPersonalTagClassify(ctx, req.(*DelOptPersonalTagClassifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserTagConfMgr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_tag_go.UserTagConfMgr",
	HandlerType: (*UserTagConfMgrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGameTypeList",
			Handler:    _UserTagConfMgr_GetGameTypeList_Handler,
		},
		{
			MethodName: "AddRegistGameRelatedTagConf",
			Handler:    _UserTagConfMgr_AddRegistGameRelatedTagConf_Handler,
		},
		{
			MethodName: "DeleteRegistGameRelatedTagConf",
			Handler:    _UserTagConfMgr_DeleteRegistGameRelatedTagConf_Handler,
		},
		{
			MethodName: "GetRegistGameRelatedTagConf",
			Handler:    _UserTagConfMgr_GetRegistGameRelatedTagConf_Handler,
		},
		{
			MethodName: "GetAllRegistGameRelatedTagConf",
			Handler:    _UserTagConfMgr_GetAllRegistGameRelatedTagConf_Handler,
		},
		{
			MethodName: "GetClassifyList",
			Handler:    _UserTagConfMgr_GetClassifyList_Handler,
		},
		{
			MethodName: "AddClassifyTagConf",
			Handler:    _UserTagConfMgr_AddClassifyTagConf_Handler,
		},
		{
			MethodName: "GetClassifyTagConf",
			Handler:    _UserTagConfMgr_GetClassifyTagConf_Handler,
		},
		{
			MethodName: "GetAllClassifyTagConf",
			Handler:    _UserTagConfMgr_GetAllClassifyTagConf_Handler,
		},
		{
			MethodName: "CreateTagConfig",
			Handler:    _UserTagConfMgr_CreateTagConfig_Handler,
		},
		{
			MethodName: "ModifyTagConfig",
			Handler:    _UserTagConfMgr_ModifyTagConfig_Handler,
		},
		{
			MethodName: "DelTagConfig",
			Handler:    _UserTagConfMgr_DelTagConfig_Handler,
		},
		{
			MethodName: "SortTagConfig",
			Handler:    _UserTagConfMgr_SortTagConfig_Handler,
		},
		{
			MethodName: "CreateFindWhoTypeTagConfig",
			Handler:    _UserTagConfMgr_CreateFindWhoTypeTagConfig_Handler,
		},
		{
			MethodName: "CreateOptPersonalTagClassify",
			Handler:    _UserTagConfMgr_CreateOptPersonalTagClassify_Handler,
		},
		{
			MethodName: "GetOptPersonalTagClassifyList",
			Handler:    _UserTagConfMgr_GetOptPersonalTagClassifyList_Handler,
		},
		{
			MethodName: "GetOptPersonalTagByClassify",
			Handler:    _UserTagConfMgr_GetOptPersonalTagByClassify_Handler,
		},
		{
			MethodName: "ModifyOptPersonalTagClassifyName",
			Handler:    _UserTagConfMgr_ModifyOptPersonalTagClassifyName_Handler,
		},
		{
			MethodName: "SortOptPersonalTagClassify",
			Handler:    _UserTagConfMgr_SortOptPersonalTagClassify_Handler,
		},
		{
			MethodName: "DelOptPersonalTagClassify",
			Handler:    _UserTagConfMgr_DelOptPersonalTagClassify_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-tag-go/user-tag-go.proto",
}

// UserTagGoClient is the client API for UserTagGo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserTagGoClient interface {
	// --------------------旧后台没迁移过来前的中间产物，这个RegistGameRelatedTag和ClassifyTag只在注册时用到，填完上报数据，并没有在端内展示---------
	SetRegistTag(ctx context.Context, in *SetRegistTagReq, opts ...grpc.CallOption) (*SetRegistTagResp, error)
	// 覆盖式
	SetUserTag(ctx context.Context, in *SetUserTagReq, opts ...grpc.CallOption) (*SetUserTagResp, error)
	GetUserTag(ctx context.Context, in *GetUserTagReq, opts ...grpc.CallOption) (*GetUserTagResp, error)
	BatGetUserTag(ctx context.Context, in *BatGetUserTagReq, opts ...grpc.CallOption) (*BatGetUserTagResp, error)
	GetUserTagConfigListFromCache(ctx context.Context, in *GetUserTagConfigListFromCacheReq, opts ...grpc.CallOption) (*GetUserTagConfigListFromCacheResp, error)
	GetPersonalTagClassifyConfFromCache(ctx context.Context, in *GetPersonalTagClassifyConfFromCacheReq, opts ...grpc.CallOption) (*GetPersonalTagClassifyConfFromCacheResp, error)
	// 为了下掉C++服务，把所有接口迁移过来，之前接口已经是转发的了，但是很难推动调用方去改造，只能服务器兼容，再把聚合逻辑迁移过来，名字换新的
	NotifyChannelPlayChange(ctx context.Context, in *NotifyChannelPlayChangeReq, opts ...grpc.CallOption) (*NotifyChannelPlayChangeResp, error)
	GetRecommendStatus(ctx context.Context, in *GetRecommendStatusReq, opts ...grpc.CallOption) (*GetRecommendStatusResp, error)
	ChangeRecommendStatus(ctx context.Context, in *ChangeRecommendStatusReq, opts ...grpc.CallOption) (*ChangeRecommendStatusResp, error)
	GetOptPersonalTagClassifyList(ctx context.Context, in *GetOptPersonalTagClassifyListReq, opts ...grpc.CallOption) (*GetOptPersonalTagClassifyListResp, error)
}

type userTagGoClient struct {
	cc *grpc.ClientConn
}

func NewUserTagGoClient(cc *grpc.ClientConn) UserTagGoClient {
	return &userTagGoClient{cc}
}

func (c *userTagGoClient) SetRegistTag(ctx context.Context, in *SetRegistTagReq, opts ...grpc.CallOption) (*SetRegistTagResp, error) {
	out := new(SetRegistTagResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/SetRegistTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) SetUserTag(ctx context.Context, in *SetUserTagReq, opts ...grpc.CallOption) (*SetUserTagResp, error) {
	out := new(SetUserTagResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/SetUserTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) GetUserTag(ctx context.Context, in *GetUserTagReq, opts ...grpc.CallOption) (*GetUserTagResp, error) {
	out := new(GetUserTagResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/GetUserTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) BatGetUserTag(ctx context.Context, in *BatGetUserTagReq, opts ...grpc.CallOption) (*BatGetUserTagResp, error) {
	out := new(BatGetUserTagResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/BatGetUserTag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) GetUserTagConfigListFromCache(ctx context.Context, in *GetUserTagConfigListFromCacheReq, opts ...grpc.CallOption) (*GetUserTagConfigListFromCacheResp, error) {
	out := new(GetUserTagConfigListFromCacheResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/GetUserTagConfigListFromCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) GetPersonalTagClassifyConfFromCache(ctx context.Context, in *GetPersonalTagClassifyConfFromCacheReq, opts ...grpc.CallOption) (*GetPersonalTagClassifyConfFromCacheResp, error) {
	out := new(GetPersonalTagClassifyConfFromCacheResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/GetPersonalTagClassifyConfFromCache", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) NotifyChannelPlayChange(ctx context.Context, in *NotifyChannelPlayChangeReq, opts ...grpc.CallOption) (*NotifyChannelPlayChangeResp, error) {
	out := new(NotifyChannelPlayChangeResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/NotifyChannelPlayChange", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) GetRecommendStatus(ctx context.Context, in *GetRecommendStatusReq, opts ...grpc.CallOption) (*GetRecommendStatusResp, error) {
	out := new(GetRecommendStatusResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/GetRecommendStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) ChangeRecommendStatus(ctx context.Context, in *ChangeRecommendStatusReq, opts ...grpc.CallOption) (*ChangeRecommendStatusResp, error) {
	out := new(ChangeRecommendStatusResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/ChangeRecommendStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userTagGoClient) GetOptPersonalTagClassifyList(ctx context.Context, in *GetOptPersonalTagClassifyListReq, opts ...grpc.CallOption) (*GetOptPersonalTagClassifyListResp, error) {
	out := new(GetOptPersonalTagClassifyListResp)
	err := c.cc.Invoke(ctx, "/user_tag_go.UserTagGo/GetOptPersonalTagClassifyList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTagGoServer is the server API for UserTagGo service.
type UserTagGoServer interface {
	// --------------------旧后台没迁移过来前的中间产物，这个RegistGameRelatedTag和ClassifyTag只在注册时用到，填完上报数据，并没有在端内展示---------
	SetRegistTag(context.Context, *SetRegistTagReq) (*SetRegistTagResp, error)
	// 覆盖式
	SetUserTag(context.Context, *SetUserTagReq) (*SetUserTagResp, error)
	GetUserTag(context.Context, *GetUserTagReq) (*GetUserTagResp, error)
	BatGetUserTag(context.Context, *BatGetUserTagReq) (*BatGetUserTagResp, error)
	GetUserTagConfigListFromCache(context.Context, *GetUserTagConfigListFromCacheReq) (*GetUserTagConfigListFromCacheResp, error)
	GetPersonalTagClassifyConfFromCache(context.Context, *GetPersonalTagClassifyConfFromCacheReq) (*GetPersonalTagClassifyConfFromCacheResp, error)
	// 为了下掉C++服务，把所有接口迁移过来，之前接口已经是转发的了，但是很难推动调用方去改造，只能服务器兼容，再把聚合逻辑迁移过来，名字换新的
	NotifyChannelPlayChange(context.Context, *NotifyChannelPlayChangeReq) (*NotifyChannelPlayChangeResp, error)
	GetRecommendStatus(context.Context, *GetRecommendStatusReq) (*GetRecommendStatusResp, error)
	ChangeRecommendStatus(context.Context, *ChangeRecommendStatusReq) (*ChangeRecommendStatusResp, error)
	GetOptPersonalTagClassifyList(context.Context, *GetOptPersonalTagClassifyListReq) (*GetOptPersonalTagClassifyListResp, error)
}

func RegisterUserTagGoServer(s *grpc.Server, srv UserTagGoServer) {
	s.RegisterService(&_UserTagGo_serviceDesc, srv)
}

func _UserTagGo_SetRegistTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetRegistTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).SetRegistTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/SetRegistTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).SetRegistTag(ctx, req.(*SetRegistTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_SetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).SetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/SetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).SetUserTag(ctx, req.(*SetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_GetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).GetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/GetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).GetUserTag(ctx, req.(*GetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_BatGetUserTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatGetUserTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).BatGetUserTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/BatGetUserTag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).BatGetUserTag(ctx, req.(*BatGetUserTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_GetUserTagConfigListFromCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTagConfigListFromCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).GetUserTagConfigListFromCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/GetUserTagConfigListFromCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).GetUserTagConfigListFromCache(ctx, req.(*GetUserTagConfigListFromCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_GetPersonalTagClassifyConfFromCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersonalTagClassifyConfFromCacheReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).GetPersonalTagClassifyConfFromCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/GetPersonalTagClassifyConfFromCache",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).GetPersonalTagClassifyConfFromCache(ctx, req.(*GetPersonalTagClassifyConfFromCacheReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_NotifyChannelPlayChange_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NotifyChannelPlayChangeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).NotifyChannelPlayChange(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/NotifyChannelPlayChange",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).NotifyChannelPlayChange(ctx, req.(*NotifyChannelPlayChangeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_GetRecommendStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRecommendStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).GetRecommendStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/GetRecommendStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).GetRecommendStatus(ctx, req.(*GetRecommendStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_ChangeRecommendStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeRecommendStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).ChangeRecommendStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/ChangeRecommendStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).ChangeRecommendStatus(ctx, req.(*ChangeRecommendStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserTagGo_GetOptPersonalTagClassifyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOptPersonalTagClassifyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTagGoServer).GetOptPersonalTagClassifyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user_tag_go.UserTagGo/GetOptPersonalTagClassifyList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTagGoServer).GetOptPersonalTagClassifyList(ctx, req.(*GetOptPersonalTagClassifyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserTagGo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user_tag_go.UserTagGo",
	HandlerType: (*UserTagGoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SetRegistTag",
			Handler:    _UserTagGo_SetRegistTag_Handler,
		},
		{
			MethodName: "SetUserTag",
			Handler:    _UserTagGo_SetUserTag_Handler,
		},
		{
			MethodName: "GetUserTag",
			Handler:    _UserTagGo_GetUserTag_Handler,
		},
		{
			MethodName: "BatGetUserTag",
			Handler:    _UserTagGo_BatGetUserTag_Handler,
		},
		{
			MethodName: "GetUserTagConfigListFromCache",
			Handler:    _UserTagGo_GetUserTagConfigListFromCache_Handler,
		},
		{
			MethodName: "GetPersonalTagClassifyConfFromCache",
			Handler:    _UserTagGo_GetPersonalTagClassifyConfFromCache_Handler,
		},
		{
			MethodName: "NotifyChannelPlayChange",
			Handler:    _UserTagGo_NotifyChannelPlayChange_Handler,
		},
		{
			MethodName: "GetRecommendStatus",
			Handler:    _UserTagGo_GetRecommendStatus_Handler,
		},
		{
			MethodName: "ChangeRecommendStatus",
			Handler:    _UserTagGo_ChangeRecommendStatus_Handler,
		},
		{
			MethodName: "GetOptPersonalTagClassifyList",
			Handler:    _UserTagGo_GetOptPersonalTagClassifyList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user-tag-go/user-tag-go.proto",
}

func init() {
	proto.RegisterFile("user-tag-go/user-tag-go.proto", fileDescriptor_user_tag_go_a64747235eb9db54)
}

var fileDescriptor_user_tag_go_a64747235eb9db54 = []byte{
	// 2738 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x5a, 0xcd, 0x6f, 0xdb, 0xc8,
	0x15, 0x37, 0x6d, 0xc7, 0x1f, 0x4f, 0x96, 0xad, 0x4c, 0xe2, 0x44, 0x96, 0x23, 0x5b, 0x61, 0xb2,
	0x59, 0x6f, 0x36, 0x71, 0xb6, 0x4e, 0xb7, 0x58, 0x60, 0xdb, 0x45, 0x15, 0xdb, 0x51, 0x05, 0x44,
	0xb6, 0x41, 0xc9, 0xd9, 0x2f, 0xa0, 0x04, 0x23, 0x4e, 0x68, 0x22, 0x14, 0xc9, 0x70, 0x46, 0x89,
	0x5d, 0xa0, 0x40, 0x81, 0x1e, 0xda, 0x02, 0x7b, 0x2a, 0x7a, 0xef, 0xb1, 0xb7, 0xbd, 0x74, 0x7b,
	0xea, 0x5f, 0x53, 0xf4, 0xd6, 0xbf, 0xa2, 0x98, 0x19, 0x52, 0x22, 0x87, 0x1f, 0x92, 0x37, 0x01,
	0xda, 0x9b, 0x38, 0xf3, 0xe6, 0xf7, 0x3e, 0x66, 0xde, 0x9b, 0xf7, 0xde, 0x08, 0xea, 0x43, 0x82,
	0x83, 0x87, 0xd4, 0xb0, 0x1e, 0x5a, 0xde, 0xa3, 0xd8, 0xef, 0x5d, 0x3f, 0xf0, 0xa8, 0x87, 0x4a,
	0x6c, 0x48, 0xa7, 0x86, 0xa5, 0x5b, 0x9e, 0x7a, 0x1d, 0x50, 0x0b, 0xd3, 0x96, 0x31, 0xc0, 0xbd,
	0x0b, 0x1f, 0x3f, 0xb3, 0x09, 0xd5, 0xf0, 0x6b, 0xb5, 0x05, 0x4b, 0xd1, 0x10, 0xda, 0x82, 0x92,
	0x65, 0x0c, 0x30, 0x5f, 0x60, 0x9b, 0x55, 0xa5, 0xa1, 0xec, 0x94, 0xb5, 0x65, 0x36, 0xd4, 0x33,
	0xac, 0xb6, 0x89, 0x36, 0x81, 0x7f, 0xe8, 0xae, 0x31, 0xc0, 0xd5, 0xd9, 0x86, 0xb2, 0xb3, 0xac,
	0x2d, 0xb1, 0x81, 0x23, 0x63, 0x80, 0x55, 0x0d, 0xae, 0xa5, 0xe0, 0x89, 0x8f, 0x3e, 0x87, 0x55,
	0x81, 0x79, 0xe1, 0x63, 0xdd, 0xb1, 0x09, 0xad, 0x2a, 0x8d, 0xb9, 0x9d, 0xd2, 0xde, 0xfa, 0x6e,
	0x4c, 0xb6, 0xdd, 0x68, 0x99, 0xb6, 0x62, 0xc5, 0x00, 0xd4, 0x1f, 0x14, 0xd8, 0x6a, 0x9a, 0xa6,
	0x86, 0x2d, 0x9b, 0x70, 0x68, 0x0d, 0x3b, 0x06, 0xc5, 0x66, 0xcf, 0xb0, 0xf6, 0x3d, 0xf7, 0xa5,
	0x86, 0x5f, 0xbf, 0x93, 0xcc, 0xe8, 0x3a, 0x5c, 0xa1, 0x36, 0x75, 0x70, 0x75, 0x8e, 0x4f, 0x88,
	0x0f, 0x74, 0x17, 0x56, 0x07, 0xc6, 0xb9, 0x4e, 0xb0, 0x83, 0xfb, 0x54, 0xef, 0xbb, 0xb4, 0x3a,
	0xcf, 0x51, 0x57, 0x06, 0xc6, 0x79, 0x97, 0x0f, 0xee, 0xbb, 0x14, 0x6d, 0xc0, 0x12, 0xe3, 0xc9,
	0x55, 0xba, 0xd2, 0x98, 0xdb, 0x59, 0xd6, 0x16, 0xa9, 0x61, 0x71, 0xb1, 0x6f, 0xc3, 0x76, 0xa1,
	0xd4, 0xc4, 0x57, 0xf7, 0xe1, 0xf6, 0x01, 0x76, 0x30, 0xc5, 0xef, 0xa0, 0x9b, 0x7a, 0x17, 0xd4,
	0x49, 0x20, 0xc4, 0x57, 0x7f, 0x09, 0x5b, 0x2d, 0x4c, 0xdf, 0x85, 0xcf, 0x3f, 0x14, 0xd8, 0x2e,
	0x84, 0x20, 0xfe, 0xc4, 0x7d, 0x18, 0x99, 0x7a, 0xb6, 0xd8, 0xd4, 0x73, 0x13, 0x4c, 0x3d, 0x9f,
	0x30, 0x75, 0x72, 0x7b, 0xaf, 0x48, 0x47, 0xf2, 0x0e, 0xdc, 0x6e, 0x61, 0xda, 0x74, 0x9c, 0x02,
	0xe5, 0xd5, 0xbf, 0x29, 0x50, 0xcd, 0x9b, 0xff, 0x91, 0x5a, 0xd5, 0x01, 0x52, 0x1a, 0x2d, 0x93,
	0x77, 0x56, 0xc7, 0x03, 0x75, 0x92, 0x3a, 0xc4, 0x47, 0x6d, 0x28, 0x1b, 0x8e, 0xa3, 0xf7, 0x3d,
	0xf7, 0x65, 0xdc, 0xdf, 0x3e, 0x48, 0xf8, 0x5b, 0x2e, 0x42, 0xc9, 0x70, 0x1c, 0xf6, 0x83, 0x9f,
	0xe3, 0x1d, 0x1e, 0x31, 0xf6, 0x1d, 0x83, 0x10, 0xfb, 0xe5, 0x45, 0x18, 0x31, 0x10, 0x82, 0x79,
	0xe6, 0xcc, 0xa1, 0x31, 0xf8, 0x6f, 0xb5, 0x07, 0x2b, 0x11, 0x59, 0xdb, 0x7d, 0xe9, 0xa1, 0x6d,
	0x28, 0xf5, 0xc3, 0xef, 0xb1, 0xdd, 0x20, 0x1a, 0x6a, 0x9b, 0xe8, 0x0e, 0x94, 0x47, 0x04, 0x31,
	0xd7, 0x5c, 0x89, 0x06, 0xb9, 0xc2, 0x36, 0x0f, 0x29, 0x49, 0xfe, 0xc4, 0xcf, 0x12, 0x00, 0x7d,
	0x11, 0xc3, 0xe3, 0x5a, 0xcf, 0x72, 0xad, 0x37, 0x12, 0x5a, 0xc7, 0x45, 0x1c, 0xb3, 0xe2, 0xaa,
	0x5a, 0xb0, 0xde, 0x34, 0xcd, 0x88, 0x20, 0xe6, 0x1b, 0x59, 0xcc, 0x24, 0xed, 0x66, 0x53, 0xda,
	0xc5, 0x77, 0x78, 0x2e, 0x19, 0x1b, 0xaa, 0x70, 0x23, 0x8b, 0x11, 0xf1, 0xd5, 0x67, 0xb0, 0x1e,
	0xd3, 0xf6, 0x1d, 0x45, 0x50, 0x1f, 0xc3, 0x8d, 0x2c, 0x34, 0xe2, 0x27, 0x84, 0x53, 0x92, 0xc2,
	0x05, 0xb0, 0x16, 0x5b, 0xf1, 0xfe, 0x76, 0xb2, 0xc8, 0x20, 0xbb, 0x50, 0x15, 0xa7, 0x7a, 0x3a,
	0xcd, 0xd5, 0x6f, 0x61, 0x23, 0x87, 0x9e, 0xf8, 0xe8, 0x0b, 0x58, 0x61, 0x87, 0x3f, 0xa1, 0x5f,
	0x69, 0xef, 0x56, 0xe6, 0x29, 0x08, 0x35, 0xd4, 0xc0, 0x70, 0x9c, 0x5e, 0x28, 0x4c, 0x17, 0xca,
	0xa7, 0x04, 0x07, 0x4d, 0x8b, 0xf9, 0xf7, 0xe1, 0x39, 0x45, 0x0d, 0x58, 0x31, 0x2c, 0xac, 0x5f,
	0x60, 0x23, 0xd0, 0x07, 0xb6, 0x1b, 0xe9, 0x6f, 0x58, 0xf8, 0x6b, 0x6c, 0x04, 0x1d, 0xdb, 0x4d,
	0x52, 0x18, 0xe7, 0xd1, 0x56, 0x44, 0x14, 0xc6, 0xb9, 0xfa, 0x2d, 0xa0, 0xfd, 0x00, 0x1b, 0x14,
	0x87, 0x92, 0xda, 0x16, 0xd3, 0x2d, 0x34, 0x49, 0x4c, 0x3f, 0x66, 0x12, 0x7e, 0x0f, 0xdf, 0x87,
	0x39, 0x6a, 0x58, 0x1c, 0xa9, 0xb4, 0x57, 0x4d, 0x08, 0xcf, 0xa4, 0xeb, 0x19, 0xd6, 0x13, 0x83,
	0x60, 0x8d, 0x11, 0xa9, 0x0f, 0xe0, 0x5a, 0x0a, 0x9c, 0xf8, 0x68, 0x1d, 0x16, 0x12, 0x31, 0xeb,
	0x0a, 0xe5, 0x91, 0xfc, 0x4b, 0x40, 0x1d, 0xcf, 0x1c, 0x1b, 0x4d, 0x88, 0x92, 0x4d, 0x7c, 0x29,
	0x31, 0xd6, 0xe1, 0x5a, 0x0a, 0x98, 0xf8, 0x6a, 0x0b, 0xd6, 0x0e, 0xb0, 0x33, 0x0d, 0xb3, 0x3a,
	0x80, 0x4d, 0xf4, 0x00, 0xf7, 0xbd, 0x37, 0x38, 0xe0, 0x3c, 0x97, 0xb4, 0x65, 0x9b, 0x68, 0x62,
	0x40, 0x45, 0x50, 0x49, 0x02, 0x11, 0x5f, 0xfd, 0x83, 0x02, 0x95, 0xae, 0x17, 0xd0, 0x69, 0xcd,
	0xba, 0x05, 0x25, 0xc1, 0x79, 0x1c, 0x21, 0xca, 0xda, 0x32, 0x67, 0xcf, 0x83, 0xef, 0x67, 0x50,
	0xf5, 0x71, 0x40, 0x3c, 0xd7, 0x70, 0x78, 0xb6, 0x12, 0x3f, 0xf7, 0x22, 0x88, 0xdf, 0x88, 0xcf,
	0xef, 0x8f, 0x9d, 0xed, 0x1a, 0x5c, 0x95, 0x04, 0x21, 0xbe, 0xfa, 0xbd, 0x02, 0x75, 0xb1, 0x35,
	0x4f, 0x6d, 0xd7, 0xfc, 0xf2, 0xcc, 0x63, 0x42, 0x24, 0x64, 0xbd, 0x0d, 0x2b, 0x2f, 0x6d, 0xd7,
	0x7c, 0x7b, 0xe6, 0xc5, 0xe5, 0x2d, 0x85, 0x63, 0x5c, 0xe6, 0x50, 0x9d, 0x98, 0x63, 0x31, 0x75,
	0xb8, 0x4f, 0xd5, 0x01, 0x98, 0x00, 0x96, 0xab, 0x13, 0x7c, 0x1e, 0xdd, 0x32, 0x62, 0xa4, 0x8b,
	0xcf, 0xd1, 0x27, 0x70, 0xbd, 0xef, 0x05, 0x01, 0x0b, 0xef, 0xb6, 0xe7, 0xea, 0x23, 0x94, 0x79,
	0x8e, 0x82, 0x62, 0x73, 0x3d, 0x01, 0xa8, 0xfe, 0x51, 0x81, 0xad, 0x22, 0x81, 0x89, 0x3f, 0x8d,
	0xc4, 0xe3, 0xfd, 0x9d, 0x8d, 0xef, 0xef, 0x03, 0x40, 0xb2, 0x38, 0x23, 0xb3, 0x56, 0x92, 0xc2,
	0xb4, 0x4d, 0xf5, 0x29, 0x6c, 0x0b, 0x49, 0x8e, 0x7d, 0x7a, 0x12, 0xda, 0x9c, 0x49, 0x12, 0x9a,
	0x9c, 0x19, 0x2f, 0x15, 0x77, 0x94, 0x8c, 0x1b, 0x64, 0x1f, 0x1a, 0xc5, 0x38, 0xc4, 0x9f, 0x18,
	0xe1, 0x54, 0x15, 0x1a, 0x2d, 0x4c, 0xb3, 0x11, 0xa2, 0x34, 0xfa, 0x35, 0x4f, 0x35, 0x8a, 0x68,
	0x88, 0x8f, 0x9e, 0xc9, 0x97, 0x94, 0x08, 0x4f, 0x1f, 0xa6, 0x5c, 0x2b, 0x47, 0xda, 0xe4, 0x95,
	0xd5, 0xe4, 0x79, 0x5d, 0x92, 0xf4, 0xc9, 0x45, 0xdc, 0x44, 0x13, 0x35, 0x7b, 0xce, 0xf3, 0xba,
	0x7c, 0x08, 0xe2, 0xa3, 0xc7, 0xd2, 0x6d, 0x51, 0x14, 0x09, 0x46, 0x31, 0xfd, 0x15, 0xdc, 0x11,
	0xd1, 0x20, 0x5b, 0x91, 0x23, 0x9e, 0x77, 0xbc, 0x7e, 0x4f, 0x59, 0xc2, 0x3d, 0xb8, 0x3b, 0x99,
	0x19, 0xf1, 0xd5, 0x36, 0xd4, 0x99, 0x93, 0xe6, 0x9f, 0xa8, 0x1d, 0xa8, 0xc4, 0xc4, 0x19, 0xab,
	0x5c, 0xd6, 0x56, 0xc7, 0x32, 0x71, 0xfd, 0x1a, 0xb0, 0x55, 0x04, 0x45, 0x7c, 0xf5, 0xd7, 0x70,
	0xeb, 0x00, 0x3b, 0xf9, 0xbc, 0x26, 0xaa, 0x3e, 0x21, 0x1e, 0x6e, 0x43, 0xbd, 0x00, 0x9f, 0xf8,
	0xea, 0x77, 0x0a, 0x94, 0x62, 0x7b, 0x53, 0x14, 0x17, 0x0b, 0x62, 0xcc, 0xd8, 0x99, 0xe7, 0xe2,
	0xce, 0x1c, 0xae, 0xb0, 0xdd, 0x97, 0x1e, 0x8f, 0x27, 0x2b, 0x7c, 0x05, 0xcf, 0x17, 0xd6, 0x61,
	0xc1, 0x26, 0xba, 0x89, 0x1d, 0x9e, 0xbe, 0x2e, 0x69, 0x57, 0x6c, 0x72, 0x80, 0x1d, 0xf5, 0x3f,
	0x0a, 0xac, 0xb2, 0x74, 0xb3, 0xdb, 0x0f, 0x30, 0x76, 0xbb, 0x67, 0x1e, 0x65, 0xb1, 0xc4, 0x18,
	0x9a, 0x36, 0xd5, 0x09, 0x35, 0xe8, 0x90, 0x44, 0xb1, 0x84, 0x8f, 0x75, 0xf9, 0x10, 0xba, 0x09,
	0x8b, 0xf6, 0xc0, 0xd2, 0x87, 0x81, 0x13, 0x0a, 0xb6, 0x60, 0x0f, 0xac, 0xd3, 0xc0, 0x41, 0x9f,
	0x43, 0x2d, 0x9c, 0xd0, 0x6d, 0xf7, 0x8d, 0x4d, 0xec, 0x17, 0x0e, 0xd6, 0x59, 0xa2, 0xec, 0xda,
	0xfd, 0x57, 0x61, 0x35, 0x77, 0x53, 0xd0, 0xb6, 0xa3, 0xf9, 0x56, 0x38, 0xcd, 0xf6, 0xf9, 0x05,
	0xb6, 0x6c, 0x57, 0x17, 0xec, 0xa9, 0x1d, 0x46, 0xc5, 0xb2, 0xb6, 0xca, 0xc7, 0x9b, 0x6c, 0xb8,
	0x67, 0x8b, 0xfa, 0xd0, 0x76, 0x4d, 0x7c, 0xce, 0x75, 0x29, 0x6b, 0xe2, 0x83, 0xed, 0xdd, 0xd0,
	0x77, 0x3c, 0xc3, 0x14, 0x4b, 0x17, 0xc4, 0xde, 0x89, 0x21, 0xb6, 0x4c, 0xfd, 0xd7, 0x2c, 0xac,
	0x32, 0xdb, 0xb7, 0x44, 0x9d, 0xc0, 0xf2, 0x88, 0x9b, 0xb0, 0xc8, 0x13, 0xfb, 0xd1, 0x5e, 0x2f,
	0xb0, 0xcf, 0xb6, 0xc9, 0xd2, 0x87, 0x17, 0x46, 0xff, 0x95, 0x9e, 0xd4, 0x13, 0xd8, 0x58, 0x5b,
	0xe8, 0xfa, 0x33, 0x58, 0xf2, 0x7c, 0x3a, 0xce, 0x9d, 0x4a, 0x7b, 0x9b, 0x29, 0x0f, 0x0c, 0x39,
	0x1d, 0xfb, 0x54, 0x5b, 0xf4, 0x7c, 0xca, 0xaf, 0x33, 0x15, 0xca, 0xf4, 0x6c, 0x38, 0x78, 0x31,
	0x82, 0x16, 0x91, 0xbf, 0xc4, 0x07, 0x43, 0xec, 0x3b, 0x50, 0x16, 0xf5, 0x86, 0xdd, 0x7f, 0x15,
	0xab, 0x39, 0x78, 0x15, 0x7e, 0x14, 0x8e, 0xa1, 0x03, 0x58, 0xe3, 0x44, 0x84, 0xef, 0x1d, 0x39,
	0xf3, 0x28, 0xd7, 0x59, 0x96, 0x23, 0xb9, 0xbd, 0x1a, 0x2f, 0xfb, 0xbb, 0xa3, 0x25, 0xa8, 0x03,
	0xd7, 0x25, 0x14, 0xa1, 0xd2, 0x62, 0x86, 0x4a, 0x12, 0x14, 0x4a, 0x42, 0x71, 0x17, 0xfc, 0x77,
	0xd2, 0xc6, 0xc7, 0x3e, 0xaf, 0xab, 0x98, 0xa1, 0x62, 0x97, 0x01, 0xb3, 0x45, 0x74, 0x8e, 0xd9,
	0xd4, 0xf8, 0x52, 0xf2, 0x7c, 0xda, 0x36, 0xd1, 0x43, 0xb8, 0x66, 0x13, 0x9d, 0x0c, 0x7d, 0xdf,
	0x0b, 0xa8, 0x3e, 0x18, 0x52, 0x5b, 0x27, 0x58, 0x54, 0x6c, 0x4b, 0x5a, 0xc5, 0x26, 0x5d, 0x31,
	0xd3, 0x19, 0x52, 0xbb, 0x8b, 0x29, 0xba, 0x07, 0x6b, 0x6f, 0x0c, 0x67, 0x88, 0x63, 0xc5, 0x95,
	0xa8, 0xdf, 0xca, 0x7c, 0x38, 0xaa, 0x9b, 0xd8, 0x5d, 0x27, 0xe8, 0x98, 0x4e, 0x04, 0xd3, 0x78,
	0x93, 0xa0, 0xc2, 0x67, 0x4e, 0xc5, 0x04, 0xa7, 0x7e, 0x04, 0xd7, 0x65, 0x09, 0x78, 0xdd, 0x28,
	0xce, 0xd5, 0x55, 0x92, 0x90, 0x81, 0xd5, 0x8f, 0xb7, 0x61, 0xc5, 0x37, 0x02, 0x6a, 0xf3, 0x8b,
	0xd4, 0x36, 0xab, 0x8b, 0xc2, 0x71, 0x46, 0x63, 0x6d, 0x13, 0x3d, 0x85, 0x35, 0x3f, 0xf0, 0x7c,
	0x5d, 0x88, 0xc1, 0xd9, 0x2f, 0x71, 0x3b, 0x6f, 0xa5, 0xec, 0x7c, 0xec, 0xd3, 0x2e, 0xee, 0x7b,
	0xae, 0x79, 0x12, 0x78, 0xbe, 0x56, 0x66, 0xcb, 0x9e, 0xb3, 0x55, 0xdc, 0xca, 0x1d, 0xb8, 0x9a,
	0xa2, 0x89, 0xec, 0xcc, 0x28, 0x63, 0x76, 0xe6, 0x53, 0x75, 0x80, 0x18, 0xcb, 0x59, 0xae, 0xf1,
	0xf2, 0x9b, 0x11, 0xdc, 0x77, 0x0a, 0x5c, 0x65, 0xaa, 0x47, 0xf9, 0x85, 0xf0, 0x8d, 0x29, 0x92,
	0x8a, 0x64, 0xae, 0x33, 0x2b, 0xe7, 0x3a, 0x9f, 0xc2, 0xcd, 0x78, 0x72, 0xe1, 0x1b, 0x76, 0x90,
	0xcc, 0x30, 0xe2, 0xa9, 0xd0, 0x89, 0x61, 0x07, 0x22, 0xcb, 0xf8, 0x5e, 0x81, 0x5a, 0xfe, 0x75,
	0xfb, 0x9e, 0x4a, 0x9f, 0xc7, 0x52, 0xe9, 0x33, 0xcd, 0x05, 0x1a, 0x8b, 0xa2, 0xf3, 0xf1, 0x28,
	0xfa, 0x19, 0xac, 0xa7, 0xe5, 0x65, 0x26, 0x9c, 0x78, 0xd3, 0x9f, 0x42, 0xb9, 0x85, 0x69, 0xc8,
	0x8b, 0x5d, 0x40, 0x75, 0x00, 0x6a, 0x04, 0x16, 0xa6, 0xfa, 0x70, 0xdc, 0xd8, 0x10, 0x23, 0xa7,
	0xb6, 0x89, 0x3e, 0x80, 0x35, 0x9b, 0xe8, 0x2e, 0xc6, 0x26, 0x97, 0x13, 0x9f, 0xd3, 0xf0, 0x0e,
	0x5a, 0xb1, 0xc9, 0x11, 0xe6, 0xfd, 0x82, 0xc3, 0x73, 0xaa, 0xf6, 0x46, 0x97, 0x0c, 0x17, 0xbb,
	0x02, 0x73, 0x63, 0x34, 0xf6, 0x33, 0xa1, 0xfd, 0xec, 0xb4, 0xe9, 0xc3, 0x17, 0xb0, 0x1a, 0x17,
	0x96, 0xf8, 0xe8, 0x01, 0xcc, 0x87, 0xd7, 0x71, 0x6e, 0x2d, 0xc2, 0xb3, 0x2c, 0x4e, 0xa5, 0xfe,
	0x55, 0x81, 0x72, 0xf7, 0x32, 0xda, 0xc6, 0x2f, 0xc7, 0xd9, 0xe4, 0xe5, 0xf8, 0xa3, 0xb6, 0x6f,
	0x1b, 0x4a, 0x04, 0x53, 0x6a, 0xbb, 0x56, 0xac, 0x7d, 0x08, 0xe1, 0xd0, 0xbe, 0x4b, 0xd5, 0x0a,
	0xac, 0x76, 0x13, 0x1a, 0xaa, 0x3d, 0xa8, 0x3c, 0x31, 0x68, 0x72, 0x8f, 0x36, 0x60, 0x69, 0x98,
	0x4c, 0x44, 0x16, 0x87, 0xb6, 0xa8, 0x55, 0xa6, 0xdc, 0x9f, 0x26, 0x5c, 0x95, 0x50, 0x13, 0xc6,
	0x9c, 0x9b, 0xc2, 0x98, 0xbf, 0xe0, 0xd9, 0x6f, 0x38, 0x2e, 0x4a, 0x01, 0x36, 0xfb, 0x34, 0xf0,
	0x06, 0xfb, 0x46, 0xff, 0x0c, 0x17, 0x17, 0x5d, 0xf1, 0x3c, 0x84, 0x77, 0xd4, 0xfe, 0xc7, 0x79,
	0xc8, 0x37, 0x3c, 0x4f, 0x2f, 0xd2, 0x86, 0xf8, 0xe8, 0x53, 0x58, 0x96, 0xdb, 0x67, 0x99, 0x56,
	0xe2, 0x6d, 0x87, 0xa5, 0xfe, 0xb8, 0x5d, 0x76, 0xaf, 0x85, 0xb3, 0x22, 0x09, 0xa3, 0x8b, 0xdb,
	0x4b, 0x7d, 0x0b, 0x1f, 0x4e, 0x45, 0xf9, 0xde, 0x6b, 0x86, 0xd3, 0xec, 0x5e, 0xe7, 0x81, 0x41,
	0x8d, 0x89, 0xbd, 0xce, 0x0d, 0xc9, 0x95, 0x63, 0x3d, 0x9c, 0x0e, 0xac, 0x09, 0xd8, 0xce, 0x45,
	0x84, 0x36, 0x31, 0x78, 0x16, 0xc0, 0xfd, 0x53, 0x81, 0xb5, 0x6e, 0xd4, 0x6f, 0x0e, 0x7d, 0x21,
	0x1d, 0x5a, 0xbe, 0x82, 0x75, 0x2e, 0x6f, 0x20, 0xd4, 0xd0, 0xa5, 0x38, 0x33, 0xb9, 0xe1, 0xc9,
	0xe4, 0x14, 0xb9, 0xc5, 0x78, 0x8c, 0x3b, 0xd7, 0xcf, 0xa1, 0x34, 0xb8, 0xd0, 0x25, 0xb7, 0xbf,
	0x95, 0x81, 0x37, 0x52, 0x57, 0x5b, 0x1e, 0x5c, 0x44, 0x3d, 0x24, 0x04, 0x95, 0xa4, 0xf0, 0xc4,
	0x57, 0x87, 0x50, 0x3b, 0xf2, 0x28, 0xdb, 0xe0, 0x33, 0xc3, 0x75, 0xb1, 0x73, 0xe2, 0x18, 0xfc,
	0xa7, 0x85, 0xc3, 0xe8, 0xd4, 0x17, 0xe3, 0x31, 0xc3, 0x87, 0x23, 0xe2, 0x09, 0xc3, 0x77, 0x8c,
	0x8b, 0x78, 0x78, 0x5a, 0x62, 0x03, 0xdc, 0x69, 0xa2, 0x49, 0xee, 0x35, 0x22, 0xf1, 0xe5, 0x93,
	0xbc, 0x34, 0xaa, 0xc3, 0x66, 0x2e, 0x5b, 0xe2, 0xab, 0x1f, 0xf1, 0x8e, 0x23, 0x2b, 0x29, 0x06,
	0x03, 0xec, 0x9a, 0x22, 0xe9, 0xce, 0x34, 0xb6, 0xfa, 0x09, 0x6f, 0x27, 0xa6, 0x48, 0x89, 0x8f,
	0x6e, 0xc0, 0x42, 0x98, 0xc0, 0x0b, 0xd1, 0xc2, 0x2f, 0xf5, 0x00, 0xaa, 0x11, 0xab, 0xc9, 0xf8,
	0xb9, 0x28, 0x9b, 0xb0, 0x91, 0x83, 0x42, 0xfc, 0xfb, 0x27, 0x89, 0x76, 0x65, 0x98, 0x28, 0x6c,
	0xec, 0x3f, 0x6b, 0x76, 0xbb, 0xed, 0xa7, 0x5f, 0xeb, 0xbd, 0x66, 0x4b, 0xef, 0x7d, 0x7d, 0x72,
	0xa8, 0xb7, 0x8f, 0x9e, 0x37, 0x9f, 0xb5, 0x0f, 0x2a, 0x33, 0x68, 0x0b, 0x6a, 0x89, 0x69, 0xed,
	0xb0, 0xd5, 0xee, 0xf6, 0xf4, 0x0e, 0xff, 0xaa, 0x94, 0xee, 0xff, 0xa0, 0xc0, 0xb5, 0xc3, 0xd0,
	0xbb, 0x63, 0x4d, 0x10, 0xb4, 0x09, 0x37, 0x0f, 0x8f, 0x4e, 0x3b, 0xfa, 0xd3, 0xf6, 0xd1, 0xc1,
	0x97, 0xbf, 0x3a, 0x66, 0x4b, 0xc7, 0xa0, 0x0d, 0xb8, 0x25, 0x4f, 0x1e, 0x9f, 0x9c, 0x1c, 0x77,
	0xdb, 0xbd, 0x43, 0xbd, 0x7b, 0xf8, 0x55, 0x45, 0x61, 0x6c, 0x65, 0x0a, 0x26, 0x44, 0xeb, 0x88,
	0xcf, 0xcf, 0xa2, 0xbb, 0xd0, 0x90, 0xe7, 0xf7, 0x8f, 0x35, 0xed, 0xf0, 0x59, 0xb3, 0xd7, 0x3e,
	0x3e, 0xd2, 0x4f, 0x9a, 0x6d, 0xad, 0x32, 0x87, 0xaa, 0x70, 0x5d, 0xa6, 0x6a, 0x35, 0x3b, 0x87,
	0x95, 0xf9, 0xbd, 0xbf, 0x5f, 0x15, 0xc9, 0x70, 0x18, 0x93, 0x3a, 0x56, 0x80, 0x9e, 0xc3, 0x9a,
	0xf4, 0x1c, 0x87, 0xb6, 0x93, 0xb9, 0x5f, 0xea, 0x2d, 0xb0, 0xd6, 0x28, 0x26, 0x20, 0xbe, 0x3a,
	0x83, 0x7e, 0x03, 0x9b, 0x05, 0x6f, 0x5b, 0xe8, 0xe3, 0x04, 0x44, 0xf1, 0xdb, 0x5d, 0xed, 0xc1,
	0xf4, 0xc4, 0x9c, 0xf7, 0xef, 0x15, 0xd8, 0x2a, 0x7e, 0xf0, 0x42, 0xbb, 0x09, 0xc8, 0x89, 0x4f,
	0x6c, 0xb5, 0x47, 0x97, 0xa2, 0x8f, 0x2c, 0x50, 0xf0, 0x18, 0x26, 0x59, 0xa0, 0xf8, 0xe5, 0x4d,
	0xb2, 0xc0, 0x84, 0x37, 0xb6, 0xd0, 0x02, 0xc5, 0x6f, 0x40, 0x92, 0x05, 0x26, 0xbe, 0x7f, 0x49,
	0x16, 0x98, 0xfc, 0xc0, 0xa4, 0xce, 0x84, 0x67, 0x2b, 0xde, 0xde, 0x4a, 0x9f, 0x2d, 0xa9, 0x41,
	0x96, 0x3e, 0x5b, 0x72, 0x77, 0x4c, 0x9d, 0x41, 0x3a, 0xa0, 0xf4, 0xdb, 0x08, 0x52, 0xe5, 0x53,
	0x92, 0x7e, 0x28, 0xa8, 0xdd, 0x99, 0x48, 0x13, 0x31, 0x48, 0x3f, 0x8a, 0x48, 0x0c, 0x32, 0xdf,
	0x60, 0x24, 0x06, 0xd9, 0x2f, 0x2b, 0xea, 0x0c, 0x3a, 0xe3, 0x11, 0x35, 0xfd, 0x38, 0x81, 0x3e,
	0xc8, 0xb0, 0x72, 0x06, 0x9b, 0x7b, 0xd3, 0x90, 0x45, 0x7b, 0x20, 0xf5, 0xfd, 0xa5, 0x3d, 0x48,
	0x3f, 0x39, 0x48, 0x7b, 0x90, 0xf1, 0x6c, 0x20, 0x70, 0xa5, 0x46, 0xbe, 0x84, 0x9b, 0x7e, 0x3f,
	0x90, 0x70, 0xb3, 0xde, 0x01, 0x66, 0x50, 0x07, 0x56, 0xe2, 0x0d, 0x7c, 0x74, 0x4b, 0x76, 0xbc,
	0x04, 0x62, 0xbd, 0x60, 0x96, 0xc3, 0x9d, 0x40, 0x39, 0xd1, 0x71, 0x47, 0xc9, 0x15, 0xf2, 0xb3,
	0x40, 0x6d, 0xab, 0x68, 0x9a, 0x23, 0xbe, 0x85, 0x5a, 0x7e, 0xf3, 0x1b, 0xdd, 0xcf, 0x30, 0x5d,
	0x4e, 0x5b, 0xbf, 0xf6, 0xf1, 0xd4, 0xb4, 0x9c, 0xf1, 0x6f, 0xe1, 0x56, 0x51, 0x8f, 0x1a, 0x3d,
	0xc8, 0x80, 0xcb, 0x6d, 0x2c, 0xd6, 0x1e, 0x5e, 0x82, 0x9a, 0xb3, 0xff, 0x9d, 0x02, 0xf5, 0xc2,
	0xd6, 0x35, 0x7a, 0x28, 0x1f, 0xca, 0xc2, 0x56, 0x78, 0x6d, 0xf7, 0x32, 0xe4, 0xb1, 0x88, 0x9a,
	0xd7, 0x86, 0x4e, 0x47, 0xd4, 0x82, 0x9e, 0x77, 0x3a, 0xa2, 0x16, 0x75, 0xb7, 0xd5, 0x19, 0xf4,
	0x27, 0x05, 0x1a, 0x93, 0xda, 0xc7, 0xe8, 0x93, 0x8c, 0x03, 0x5e, 0xd8, 0xda, 0xae, 0xfd, 0xe4,
	0x92, 0x2b, 0xa2, 0x23, 0x98, 0xdf, 0x56, 0x96, 0x8e, 0x60, 0x61, 0x2b, 0x5b, 0x3a, 0x82, 0x13,
	0x7a, 0xd5, 0x33, 0x88, 0xc2, 0x46, 0x6e, 0x37, 0x19, 0x7d, 0x24, 0xfb, 0x62, 0x3e, 0xdb, 0xfb,
	0xd3, 0x92, 0x32, 0xae, 0x7b, 0x7f, 0x59, 0x82, 0xe5, 0x30, 0x6b, 0x69, 0x79, 0x2c, 0x40, 0xc4,
	0xd3, 0x66, 0x29, 0x40, 0x48, 0xe5, 0x80, 0x14, 0x20, 0x52, 0xf9, 0xf6, 0x0c, 0x6a, 0x01, 0x8c,
	0x2b, 0x6c, 0x54, 0x93, 0xc9, 0xc7, 0x55, 0x76, 0x6d, 0x33, 0x77, 0x2e, 0x02, 0x6a, 0xe5, 0x01,
	0xb5, 0x0a, 0x80, 0x5a, 0x32, 0xd0, 0x09, 0x94, 0x13, 0xb5, 0xb8, 0x14, 0xb2, 0xe4, 0xea, 0x5f,
	0x0a, 0x59, 0xa9, 0x32, 0x7e, 0xec, 0xba, 0xf9, 0xd5, 0x6c, 0xda, 0x75, 0x0b, 0xeb, 0xf8, 0xb4,
	0xeb, 0x16, 0x17, 0xca, 0xea, 0x0c, 0xfa, 0xb3, 0x02, 0x77, 0xa6, 0x28, 0x65, 0xd1, 0x63, 0x19,
	0x79, 0x8a, 0x32, 0xb9, 0xf6, 0xd3, 0xcb, 0x2f, 0xe2, 0x42, 0xb9, 0x70, 0x33, 0xa7, 0xec, 0x41,
	0xc9, 0xba, 0x39, 0xbf, 0x26, 0xab, 0xed, 0x4c, 0x47, 0x18, 0x4b, 0x2b, 0xa4, 0x0a, 0x25, 0x9d,
	0x56, 0xa4, 0x0b, 0xa1, 0x74, 0x5a, 0x91, 0x51, 0xe6, 0x88, 0xb4, 0x22, 0xb3, 0x0a, 0x92, 0xd2,
	0x8a, 0xbc, 0x7a, 0x4b, 0x4a, 0x2b, 0x72, 0x0b, 0xaa, 0xff, 0x8b, 0xdb, 0xe0, 0xc9, 0xa3, 0x6f,
	0x1e, 0x5a, 0x9e, 0x63, 0xb8, 0xd6, 0xee, 0xa7, 0x7b, 0x94, 0xee, 0xf6, 0xbd, 0xc1, 0x23, 0xfe,
	0x6f, 0xc6, 0xbe, 0xe7, 0x3c, 0x22, 0x38, 0x78, 0x63, 0xf7, 0x31, 0x89, 0xff, 0xd7, 0xf1, 0xc5,
	0x02, 0x9f, 0x7e, 0xfc, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff, 0x66, 0xfe, 0xd8, 0xdf, 0x0d, 0x29,
	0x00, 0x00,
}
