// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-present-runway/channel-present-runway.proto

package channel_present_runway // import "golang.52tt.com/protocol/services/channel-present-runway"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type RunwayConfig struct {
	Level                uint32   `protobuf:"varint,1,opt,name=level,proto3" json:"level,omitempty"`
	BeginValue           uint32   `protobuf:"varint,2,opt,name=begin_value,json=beginValue,proto3" json:"begin_value,omitempty"`
	EndValue             uint32   `protobuf:"varint,3,opt,name=end_value,json=endValue,proto3" json:"end_value,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RunwayConfig) Reset()         { *m = RunwayConfig{} }
func (m *RunwayConfig) String() string { return proto.CompactTextString(m) }
func (*RunwayConfig) ProtoMessage()    {}
func (*RunwayConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{0}
}
func (m *RunwayConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RunwayConfig.Unmarshal(m, b)
}
func (m *RunwayConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RunwayConfig.Marshal(b, m, deterministic)
}
func (dst *RunwayConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunwayConfig.Merge(dst, src)
}
func (m *RunwayConfig) XXX_Size() int {
	return xxx_messageInfo_RunwayConfig.Size(m)
}
func (m *RunwayConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RunwayConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RunwayConfig proto.InternalMessageInfo

func (m *RunwayConfig) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *RunwayConfig) GetBeginValue() uint32 {
	if m != nil {
		return m.BeginValue
	}
	return 0
}

func (m *RunwayConfig) GetEndValue() uint32 {
	if m != nil {
		return m.EndValue
	}
	return 0
}

type RunwayEffect struct {
	RunwayPresentBase    uint32   `protobuf:"varint,1,opt,name=runway_present_base,json=runwayPresentBase,proto3" json:"runway_present_base,omitempty"`
	RunwayAddSeconds     uint32   `protobuf:"varint,2,opt,name=runway_add_seconds,json=runwayAddSeconds,proto3" json:"runway_add_seconds,omitempty"`
	MaxRunwayAddSeconds  uint32   `protobuf:"varint,3,opt,name=max_runway_add_seconds,json=maxRunwayAddSeconds,proto3" json:"max_runway_add_seconds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RunwayEffect) Reset()         { *m = RunwayEffect{} }
func (m *RunwayEffect) String() string { return proto.CompactTextString(m) }
func (*RunwayEffect) ProtoMessage()    {}
func (*RunwayEffect) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{1}
}
func (m *RunwayEffect) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RunwayEffect.Unmarshal(m, b)
}
func (m *RunwayEffect) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RunwayEffect.Marshal(b, m, deterministic)
}
func (dst *RunwayEffect) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RunwayEffect.Merge(dst, src)
}
func (m *RunwayEffect) XXX_Size() int {
	return xxx_messageInfo_RunwayEffect.Size(m)
}
func (m *RunwayEffect) XXX_DiscardUnknown() {
	xxx_messageInfo_RunwayEffect.DiscardUnknown(m)
}

var xxx_messageInfo_RunwayEffect proto.InternalMessageInfo

func (m *RunwayEffect) GetRunwayPresentBase() uint32 {
	if m != nil {
		return m.RunwayPresentBase
	}
	return 0
}

func (m *RunwayEffect) GetRunwayAddSeconds() uint32 {
	if m != nil {
		return m.RunwayAddSeconds
	}
	return 0
}

func (m *RunwayEffect) GetMaxRunwayAddSeconds() uint32 {
	if m != nil {
		return m.MaxRunwayAddSeconds
	}
	return 0
}

type UserRunwayBrief struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	CurValue             uint32   `protobuf:"varint,2,opt,name=cur_value,json=curValue,proto3" json:"cur_value,omitempty"`
	RunwayLevel          uint32   `protobuf:"varint,3,opt,name=runway_level,json=runwayLevel,proto3" json:"runway_level,omitempty"`
	ExpiredTime          uint32   `protobuf:"varint,4,opt,name=expired_time,json=expiredTime,proto3" json:"expired_time,omitempty"`
	StartTime            uint32   `protobuf:"varint,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	ContLaunchTimes      uint32   `protobuf:"varint,6,opt,name=cont_launch_times,json=contLaunchTimes,proto3" json:"cont_launch_times,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRunwayBrief) Reset()         { *m = UserRunwayBrief{} }
func (m *UserRunwayBrief) String() string { return proto.CompactTextString(m) }
func (*UserRunwayBrief) ProtoMessage()    {}
func (*UserRunwayBrief) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{2}
}
func (m *UserRunwayBrief) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRunwayBrief.Unmarshal(m, b)
}
func (m *UserRunwayBrief) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRunwayBrief.Marshal(b, m, deterministic)
}
func (dst *UserRunwayBrief) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRunwayBrief.Merge(dst, src)
}
func (m *UserRunwayBrief) XXX_Size() int {
	return xxx_messageInfo_UserRunwayBrief.Size(m)
}
func (m *UserRunwayBrief) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRunwayBrief.DiscardUnknown(m)
}

var xxx_messageInfo_UserRunwayBrief proto.InternalMessageInfo

func (m *UserRunwayBrief) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserRunwayBrief) GetCurValue() uint32 {
	if m != nil {
		return m.CurValue
	}
	return 0
}

func (m *UserRunwayBrief) GetRunwayLevel() uint32 {
	if m != nil {
		return m.RunwayLevel
	}
	return 0
}

func (m *UserRunwayBrief) GetExpiredTime() uint32 {
	if m != nil {
		return m.ExpiredTime
	}
	return 0
}

func (m *UserRunwayBrief) GetStartTime() uint32 {
	if m != nil {
		return m.StartTime
	}
	return 0
}

func (m *UserRunwayBrief) GetContLaunchTimes() uint32 {
	if m != nil {
		return m.ContLaunchTimes
	}
	return 0
}

type UserRunwayInfo struct {
	BriefInfo            *UserRunwayBrief `protobuf:"bytes,1,opt,name=brief_info,json=briefInfo,proto3" json:"brief_info,omitempty"`
	RunwayCfg            *RunwayConfig    `protobuf:"bytes,2,opt,name=runway_cfg,json=runwayCfg,proto3" json:"runway_cfg,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *UserRunwayInfo) Reset()         { *m = UserRunwayInfo{} }
func (m *UserRunwayInfo) String() string { return proto.CompactTextString(m) }
func (*UserRunwayInfo) ProtoMessage()    {}
func (*UserRunwayInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{3}
}
func (m *UserRunwayInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRunwayInfo.Unmarshal(m, b)
}
func (m *UserRunwayInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRunwayInfo.Marshal(b, m, deterministic)
}
func (dst *UserRunwayInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRunwayInfo.Merge(dst, src)
}
func (m *UserRunwayInfo) XXX_Size() int {
	return xxx_messageInfo_UserRunwayInfo.Size(m)
}
func (m *UserRunwayInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRunwayInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserRunwayInfo proto.InternalMessageInfo

func (m *UserRunwayInfo) GetBriefInfo() *UserRunwayBrief {
	if m != nil {
		return m.BriefInfo
	}
	return nil
}

func (m *UserRunwayInfo) GetRunwayCfg() *RunwayConfig {
	if m != nil {
		return m.RunwayCfg
	}
	return nil
}

// 获取房间的火箭跑道信息
type GetChannelRunwayListReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelRunwayListReq) Reset()         { *m = GetChannelRunwayListReq{} }
func (m *GetChannelRunwayListReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelRunwayListReq) ProtoMessage()    {}
func (*GetChannelRunwayListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{4}
}
func (m *GetChannelRunwayListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRunwayListReq.Unmarshal(m, b)
}
func (m *GetChannelRunwayListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRunwayListReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelRunwayListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRunwayListReq.Merge(dst, src)
}
func (m *GetChannelRunwayListReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelRunwayListReq.Size(m)
}
func (m *GetChannelRunwayListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRunwayListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRunwayListReq proto.InternalMessageInfo

func (m *GetChannelRunwayListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetChannelRunwayListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetChannelRunwayListResp struct {
	RunwayList           []*UserRunwayInfo `protobuf:"bytes,1,rep,name=runway_list,json=runwayList,proto3" json:"runway_list,omitempty"`
	RunwayEffect         *RunwayEffect     `protobuf:"bytes,2,opt,name=runway_effect,json=runwayEffect,proto3" json:"runway_effect,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *GetChannelRunwayListResp) Reset()         { *m = GetChannelRunwayListResp{} }
func (m *GetChannelRunwayListResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelRunwayListResp) ProtoMessage()    {}
func (*GetChannelRunwayListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{5}
}
func (m *GetChannelRunwayListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelRunwayListResp.Unmarshal(m, b)
}
func (m *GetChannelRunwayListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelRunwayListResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelRunwayListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelRunwayListResp.Merge(dst, src)
}
func (m *GetChannelRunwayListResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelRunwayListResp.Size(m)
}
func (m *GetChannelRunwayListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelRunwayListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelRunwayListResp proto.InternalMessageInfo

func (m *GetChannelRunwayListResp) GetRunwayList() []*UserRunwayInfo {
	if m != nil {
		return m.RunwayList
	}
	return nil
}

func (m *GetChannelRunwayListResp) GetRunwayEffect() *RunwayEffect {
	if m != nil {
		return m.RunwayEffect
	}
	return nil
}

// 获取用户在房间的火箭跑道信息
type GetUserRunwayInfoReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	QueryReadyInfo       bool     `protobuf:"varint,2,opt,name=query_ready_info,json=queryReadyInfo,proto3" json:"query_ready_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserRunwayInfoReq) Reset()         { *m = GetUserRunwayInfoReq{} }
func (m *GetUserRunwayInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRunwayInfoReq) ProtoMessage()    {}
func (*GetUserRunwayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{6}
}
func (m *GetUserRunwayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRunwayInfoReq.Unmarshal(m, b)
}
func (m *GetUserRunwayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRunwayInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetUserRunwayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRunwayInfoReq.Merge(dst, src)
}
func (m *GetUserRunwayInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetUserRunwayInfoReq.Size(m)
}
func (m *GetUserRunwayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRunwayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRunwayInfoReq proto.InternalMessageInfo

func (m *GetUserRunwayInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetUserRunwayInfoReq) GetQueryReadyInfo() bool {
	if m != nil {
		return m.QueryReadyInfo
	}
	return false
}

type GetUserRunwayInfoResp struct {
	RunwayInfo           *UserRunwayInfo `protobuf:"bytes,1,opt,name=runway_info,json=runwayInfo,proto3" json:"runway_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetUserRunwayInfoResp) Reset()         { *m = GetUserRunwayInfoResp{} }
func (m *GetUserRunwayInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRunwayInfoResp) ProtoMessage()    {}
func (*GetUserRunwayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{7}
}
func (m *GetUserRunwayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserRunwayInfoResp.Unmarshal(m, b)
}
func (m *GetUserRunwayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserRunwayInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetUserRunwayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserRunwayInfoResp.Merge(dst, src)
}
func (m *GetUserRunwayInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetUserRunwayInfoResp.Size(m)
}
func (m *GetUserRunwayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserRunwayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserRunwayInfoResp proto.InternalMessageInfo

func (m *GetUserRunwayInfoResp) GetRunwayInfo() *UserRunwayInfo {
	if m != nil {
		return m.RunwayInfo
	}
	return nil
}

type UserPresentRecord struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	PresentValue         uint32   `protobuf:"varint,2,opt,name=present_value,json=presentValue,proto3" json:"present_value,omitempty"`
	MicroTs              uint64   `protobuf:"varint,3,opt,name=micro_ts,json=microTs,proto3" json:"micro_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserPresentRecord) Reset()         { *m = UserPresentRecord{} }
func (m *UserPresentRecord) String() string { return proto.CompactTextString(m) }
func (*UserPresentRecord) ProtoMessage()    {}
func (*UserPresentRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_present_runway_eafdb4d1130e23e9, []int{8}
}
func (m *UserPresentRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserPresentRecord.Unmarshal(m, b)
}
func (m *UserPresentRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserPresentRecord.Marshal(b, m, deterministic)
}
func (dst *UserPresentRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserPresentRecord.Merge(dst, src)
}
func (m *UserPresentRecord) XXX_Size() int {
	return xxx_messageInfo_UserPresentRecord.Size(m)
}
func (m *UserPresentRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_UserPresentRecord.DiscardUnknown(m)
}

var xxx_messageInfo_UserPresentRecord proto.InternalMessageInfo

func (m *UserPresentRecord) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserPresentRecord) GetPresentValue() uint32 {
	if m != nil {
		return m.PresentValue
	}
	return 0
}

func (m *UserPresentRecord) GetMicroTs() uint64 {
	if m != nil {
		return m.MicroTs
	}
	return 0
}

func init() {
	proto.RegisterType((*RunwayConfig)(nil), "channel_present_runway.RunwayConfig")
	proto.RegisterType((*RunwayEffect)(nil), "channel_present_runway.RunwayEffect")
	proto.RegisterType((*UserRunwayBrief)(nil), "channel_present_runway.UserRunwayBrief")
	proto.RegisterType((*UserRunwayInfo)(nil), "channel_present_runway.UserRunwayInfo")
	proto.RegisterType((*GetChannelRunwayListReq)(nil), "channel_present_runway.GetChannelRunwayListReq")
	proto.RegisterType((*GetChannelRunwayListResp)(nil), "channel_present_runway.GetChannelRunwayListResp")
	proto.RegisterType((*GetUserRunwayInfoReq)(nil), "channel_present_runway.GetUserRunwayInfoReq")
	proto.RegisterType((*GetUserRunwayInfoResp)(nil), "channel_present_runway.GetUserRunwayInfoResp")
	proto.RegisterType((*UserPresentRecord)(nil), "channel_present_runway.UserPresentRecord")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPresentRunwayClient is the client API for ChannelPresentRunway service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPresentRunwayClient interface {
	GetChannelRunwayList(ctx context.Context, in *GetChannelRunwayListReq, opts ...grpc.CallOption) (*GetChannelRunwayListResp, error)
	GetUserRunwayInfo(ctx context.Context, in *GetUserRunwayInfoReq, opts ...grpc.CallOption) (*GetUserRunwayInfoResp, error)
}

type channelPresentRunwayClient struct {
	cc *grpc.ClientConn
}

func NewChannelPresentRunwayClient(cc *grpc.ClientConn) ChannelPresentRunwayClient {
	return &channelPresentRunwayClient{cc}
}

func (c *channelPresentRunwayClient) GetChannelRunwayList(ctx context.Context, in *GetChannelRunwayListReq, opts ...grpc.CallOption) (*GetChannelRunwayListResp, error) {
	out := new(GetChannelRunwayListResp)
	err := c.cc.Invoke(ctx, "/channel_present_runway.ChannelPresentRunway/GetChannelRunwayList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPresentRunwayClient) GetUserRunwayInfo(ctx context.Context, in *GetUserRunwayInfoReq, opts ...grpc.CallOption) (*GetUserRunwayInfoResp, error) {
	out := new(GetUserRunwayInfoResp)
	err := c.cc.Invoke(ctx, "/channel_present_runway.ChannelPresentRunway/GetUserRunwayInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPresentRunwayServer is the server API for ChannelPresentRunway service.
type ChannelPresentRunwayServer interface {
	GetChannelRunwayList(context.Context, *GetChannelRunwayListReq) (*GetChannelRunwayListResp, error)
	GetUserRunwayInfo(context.Context, *GetUserRunwayInfoReq) (*GetUserRunwayInfoResp, error)
}

func RegisterChannelPresentRunwayServer(s *grpc.Server, srv ChannelPresentRunwayServer) {
	s.RegisterService(&_ChannelPresentRunway_serviceDesc, srv)
}

func _ChannelPresentRunway_GetChannelRunwayList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelRunwayListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresentRunwayServer).GetChannelRunwayList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_present_runway.ChannelPresentRunway/GetChannelRunwayList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresentRunwayServer).GetChannelRunwayList(ctx, req.(*GetChannelRunwayListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPresentRunway_GetUserRunwayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRunwayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPresentRunwayServer).GetUserRunwayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_present_runway.ChannelPresentRunway/GetUserRunwayInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPresentRunwayServer).GetUserRunwayInfo(ctx, req.(*GetUserRunwayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPresentRunway_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_present_runway.ChannelPresentRunway",
	HandlerType: (*ChannelPresentRunwayServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChannelRunwayList",
			Handler:    _ChannelPresentRunway_GetChannelRunwayList_Handler,
		},
		{
			MethodName: "GetUserRunwayInfo",
			Handler:    _ChannelPresentRunway_GetUserRunwayInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-present-runway/channel-present-runway.proto",
}

func init() {
	proto.RegisterFile("channel-present-runway/channel-present-runway.proto", fileDescriptor_channel_present_runway_eafdb4d1130e23e9)
}

var fileDescriptor_channel_present_runway_eafdb4d1130e23e9 = []byte{
	// 656 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x54, 0xdb, 0x6e, 0xd3, 0x4c,
	0x10, 0xfe, 0xdd, 0xd3, 0x9f, 0x4c, 0xd2, 0x43, 0xb6, 0xa5, 0x84, 0x22, 0x44, 0x6b, 0x10, 0x54,
	0xa8, 0x4d, 0x50, 0x2a, 0x24, 0xc4, 0x1d, 0x8d, 0xa0, 0x2a, 0xea, 0x05, 0x5a, 0x0a, 0x17, 0xdc,
	0x18, 0x67, 0x3d, 0x4e, 0x2d, 0x39, 0xbb, 0xe9, 0xee, 0xba, 0xb4, 0xe2, 0x01, 0x78, 0x0a, 0x2e,
	0x78, 0x01, 0xde, 0x85, 0x37, 0x42, 0x7b, 0x70, 0xe9, 0xc1, 0x01, 0x7a, 0xe7, 0xfd, 0xbe, 0xd9,
	0x99, 0xf9, 0xbe, 0x99, 0x35, 0xec, 0xb0, 0xa3, 0x98, 0x73, 0xcc, 0xb7, 0xc7, 0x12, 0x15, 0x72,
	0xbd, 0x2d, 0x0b, 0xfe, 0x39, 0x3e, 0xeb, 0x56, 0xc3, 0x9d, 0xb1, 0x14, 0x5a, 0x90, 0x55, 0xcf,
	0x46, 0x9e, 0x8d, 0x1c, 0x1b, 0x0e, 0xa0, 0x49, 0xed, 0x57, 0x5f, 0xf0, 0x34, 0x1b, 0x92, 0x15,
	0x98, 0xcd, 0xf1, 0x04, 0xf3, 0x76, 0xb0, 0x1e, 0x6c, 0xce, 0x53, 0x77, 0x20, 0xf7, 0xa1, 0x31,
	0xc0, 0x61, 0xc6, 0xa3, 0x93, 0x38, 0x2f, 0xb0, 0x3d, 0x65, 0x39, 0xb0, 0xd0, 0x07, 0x83, 0x90,
	0xbb, 0x50, 0x47, 0x9e, 0x78, 0x7a, 0xda, 0xd2, 0x35, 0xe4, 0x89, 0x25, 0xc3, 0xef, 0x41, 0x59,
	0xe4, 0x55, 0x9a, 0x22, 0xd3, 0xa4, 0x03, 0xcb, 0xae, 0xfc, 0x79, 0x37, 0x83, 0x58, 0xa1, 0x2f,
	0xd9, 0x72, 0xd4, 0x5b, 0xc7, 0xec, 0xc6, 0x0a, 0xc9, 0x16, 0x10, 0x1f, 0x1f, 0x27, 0x49, 0xa4,
	0x90, 0x09, 0x9e, 0x28, 0xdf, 0xc5, 0x92, 0x63, 0x5e, 0x26, 0xc9, 0x3b, 0x87, 0x93, 0x1d, 0x58,
	0x1d, 0xc5, 0xa7, 0x51, 0xc5, 0x0d, 0xd7, 0xd8, 0xf2, 0x28, 0x3e, 0xa5, 0x57, 0x2e, 0x85, 0x3f,
	0x03, 0x58, 0x7c, 0xaf, 0x50, 0x3a, 0x62, 0x57, 0x66, 0x98, 0x92, 0x25, 0x98, 0x2e, 0xb2, 0xc4,
	0xb7, 0x65, 0x3e, 0x8d, 0x4c, 0x56, 0xc8, 0x4b, 0x2e, 0xd4, 0x58, 0x21, 0x9d, 0x07, 0x1b, 0xd0,
	0xf4, 0x35, 0x9d, 0x83, 0xae, 0x5a, 0xc3, 0x61, 0x07, 0xd6, 0xc7, 0x0d, 0x68, 0xe2, 0xe9, 0x38,
	0x93, 0x98, 0x44, 0x3a, 0x1b, 0x61, 0x7b, 0xc6, 0x85, 0x78, 0xec, 0x30, 0x1b, 0x21, 0xb9, 0x07,
	0xa0, 0x74, 0x2c, 0xb5, 0x0b, 0x98, 0xb5, 0x01, 0x75, 0x8b, 0x58, 0xfa, 0x09, 0xb4, 0x98, 0xe0,
	0x3a, 0xca, 0xe3, 0x82, 0xb3, 0x23, 0x1b, 0xa4, 0xda, 0x73, 0x36, 0x6a, 0xd1, 0x10, 0x07, 0x16,
	0x37, 0xa1, 0x2a, 0xfc, 0x16, 0xc0, 0xc2, 0x6f, 0x4d, 0xfb, 0x3c, 0x15, 0xe4, 0x35, 0xc0, 0xc0,
	0x68, 0x8b, 0x32, 0x9e, 0x0a, 0xab, 0xac, 0xd1, 0x7b, 0xdc, 0xa9, 0xde, 0x8d, 0xce, 0x15, 0x3f,
	0x68, 0xdd, 0x5e, 0xb5, 0x79, 0xfa, 0x00, 0x5e, 0x2b, 0x4b, 0x87, 0xd6, 0x89, 0x46, 0xef, 0xe1,
	0xa4, 0x3c, 0x17, 0x17, 0x8c, 0xd6, 0x1d, 0xd8, 0x4f, 0x87, 0xe1, 0x1b, 0xb8, 0xbd, 0x87, 0xba,
	0xef, 0x2e, 0xb9, 0xa0, 0x83, 0x4c, 0x69, 0x8a, 0xc7, 0xc6, 0x85, 0x32, 0xd9, 0xf9, 0x04, 0xea,
	0x1e, 0xd9, 0x4f, 0xca, 0xc9, 0x4c, 0x9d, 0x4f, 0x26, 0xfc, 0x11, 0x40, 0xbb, 0x3a, 0x99, 0x1a,
	0x93, 0x3d, 0x68, 0x94, 0x93, 0xc9, 0x94, 0x6e, 0x07, 0xeb, 0xd3, 0x9b, 0x8d, 0xde, 0xa3, 0xbf,
	0xcb, 0x36, 0x52, 0xa9, 0x17, 0x6a, 0x92, 0x91, 0x7d, 0x98, 0xf7, 0x89, 0xd0, 0x6e, 0xf2, 0xbf,
	0x29, 0x77, 0x5b, 0x4f, 0xfd, 0x76, 0xb8, 0x53, 0x48, 0x61, 0x65, 0x0f, 0xf5, 0x95, 0x5a, 0x78,
	0x5c, 0xb1, 0x74, 0x9b, 0xb0, 0x74, 0x5c, 0xa0, 0x3c, 0x8b, 0x24, 0xc6, 0xc9, 0x99, 0x9b, 0x9c,
	0xa9, 0x5b, 0xa3, 0x0b, 0x16, 0xa7, 0x06, 0x36, 0xd7, 0xc3, 0x4f, 0x70, 0xab, 0x22, 0xe7, 0x25,
	0x03, 0x2e, 0xcc, 0xfd, 0x86, 0x06, 0xd8, 0x0a, 0x08, 0x2d, 0xc3, 0xfa, 0xc7, 0x49, 0x91, 0x09,
	0x99, 0x54, 0xb4, 0xfc, 0x00, 0xe6, 0xcb, 0x9c, 0x17, 0xdf, 0x4a, 0xd3, 0x83, 0xee, 0xbd, 0xdc,
	0x81, 0xda, 0x28, 0x63, 0x52, 0x44, 0xda, 0xbd, 0xcc, 0x19, 0xfa, 0xbf, 0x3d, 0x1f, 0xaa, 0xde,
	0xd7, 0x29, 0x58, 0xf1, 0xa3, 0x2c, 0x4b, 0xd9, 0x1e, 0xc8, 0x17, 0xeb, 0xda, 0xb5, 0x29, 0x93,
	0xee, 0x24, 0x2d, 0x13, 0x16, 0x6c, 0xed, 0xe9, 0xcd, 0x2e, 0xa8, 0x71, 0xf8, 0x1f, 0x91, 0xd0,
	0xba, 0x66, 0x2f, 0xd9, 0xfa, 0x43, 0xa2, 0x6b, 0xd3, 0x5d, 0xdb, 0xbe, 0x41, 0xb4, 0xa9, 0xb9,
	0xfb, 0xe2, 0xe3, 0xf3, 0xa1, 0xc8, 0x63, 0x3e, 0xec, 0x3c, 0xeb, 0x69, 0xdd, 0x61, 0x62, 0xd4,
	0xb5, 0x3f, 0x74, 0x26, 0xf2, 0xae, 0x42, 0x79, 0x92, 0x31, 0x54, 0x13, 0xfe, 0xfc, 0x83, 0x39,
	0x1b, 0xb9, 0xf3, 0x2b, 0x00, 0x00, 0xff, 0xff, 0x34, 0x2d, 0xe5, 0x46, 0x31, 0x06, 0x00, 0x00,
}
