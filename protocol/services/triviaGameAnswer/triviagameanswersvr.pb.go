// Code generated by protoc-gen-gogo.
// source: services/triviaGameAnswer/triviagameanswersvr.proto
// DO NOT EDIT!

/*
	Package triviagame_answer is a generated protocol buffer package.

	It is generated from these files:
		services/triviaGameAnswer/triviagameanswersvr.proto

	It has these top-level messages:
		ResultStat
		AnswerReq
		AnswerResp
		GetQuestionResultStatReq
		GetQuestionResultStatResp
		GetUserAnswerStatReq
		GetUserAnswerStatResp
		GetAnswerCorrectUidListReq
		GetAnswerCorrectUidListResp
		CheckUserAnswerQualifyReq
		CheckUserAnswerQualifyResp
		AwardMoneyToWinUserReq
		AwardMoneyToWinUserResp
		WashedoutUserReq
		WashedoutUserResp
*/
package triviagame_answer

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import _ "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

// 获取答题统计结果
type ResultStat struct {
	AnswerId  uint32 `protobuf:"varint,1,req,name=answer_id,json=answerId" json:"answer_id"`
	AnswerCnt uint32 `protobuf:"varint,2,req,name=answer_cnt,json=answerCnt" json:"answer_cnt"`
}

func (m *ResultStat) Reset()                    { *m = ResultStat{} }
func (m *ResultStat) String() string            { return proto.CompactTextString(m) }
func (*ResultStat) ProtoMessage()               {}
func (*ResultStat) Descriptor() ([]byte, []int) { return fileDescriptorTriviagameanswersvr, []int{0} }

func (m *ResultStat) GetAnswerId() uint32 {
	if m != nil {
		return m.AnswerId
	}
	return 0
}

func (m *ResultStat) GetAnswerCnt() uint32 {
	if m != nil {
		return m.AnswerCnt
	}
	return 0
}

type AnswerReq struct {
	ActiveId    uint32 `protobuf:"varint,1,req,name=active_id,json=activeId" json:"active_id"`
	QuestionIdx uint32 `protobuf:"varint,2,req,name=question_idx,json=questionIdx" json:"question_idx"`
	Uid         uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
	AnswerId    uint32 `protobuf:"varint,4,req,name=answer_id,json=answerId" json:"answer_id"`
}

func (m *AnswerReq) Reset()                    { *m = AnswerReq{} }
func (m *AnswerReq) String() string            { return proto.CompactTextString(m) }
func (*AnswerReq) ProtoMessage()               {}
func (*AnswerReq) Descriptor() ([]byte, []int) { return fileDescriptorTriviagameanswersvr, []int{1} }

func (m *AnswerReq) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *AnswerReq) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

func (m *AnswerReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnswerReq) GetAnswerId() uint32 {
	if m != nil {
		return m.AnswerId
	}
	return 0
}

type AnswerResp struct {
	ResulutStatus   uint32 `protobuf:"varint,1,req,name=resulut_status,json=resulutStatus" json:"resulut_status"`
	CorrectAnswerId uint32 `protobuf:"varint,2,req,name=correct_answer_id,json=correctAnswerId" json:"correct_answer_id"`
}

func (m *AnswerResp) Reset()                    { *m = AnswerResp{} }
func (m *AnswerResp) String() string            { return proto.CompactTextString(m) }
func (*AnswerResp) ProtoMessage()               {}
func (*AnswerResp) Descriptor() ([]byte, []int) { return fileDescriptorTriviagameanswersvr, []int{2} }

func (m *AnswerResp) GetResulutStatus() uint32 {
	if m != nil {
		return m.ResulutStatus
	}
	return 0
}

func (m *AnswerResp) GetCorrectAnswerId() uint32 {
	if m != nil {
		return m.CorrectAnswerId
	}
	return 0
}

type GetQuestionResultStatReq struct {
	ActiveId    uint32 `protobuf:"varint,1,req,name=active_id,json=activeId" json:"active_id"`
	QuestionIdx uint32 `protobuf:"varint,2,req,name=question_idx,json=questionIdx" json:"question_idx"`
}

func (m *GetQuestionResultStatReq) Reset()         { *m = GetQuestionResultStatReq{} }
func (m *GetQuestionResultStatReq) String() string { return proto.CompactTextString(m) }
func (*GetQuestionResultStatReq) ProtoMessage()    {}
func (*GetQuestionResultStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{3}
}

func (m *GetQuestionResultStatReq) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *GetQuestionResultStatReq) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

type GetQuestionResultStatResp struct {
	StatList []*ResultStat `protobuf:"bytes,1,rep,name=stat_list,json=statList" json:"stat_list,omitempty"`
}

func (m *GetQuestionResultStatResp) Reset()         { *m = GetQuestionResultStatResp{} }
func (m *GetQuestionResultStatResp) String() string { return proto.CompactTextString(m) }
func (*GetQuestionResultStatResp) ProtoMessage()    {}
func (*GetQuestionResultStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{4}
}

func (m *GetQuestionResultStatResp) GetStatList() []*ResultStat {
	if m != nil {
		return m.StatList
	}
	return nil
}

// (废弃)
type GetUserAnswerStatReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *GetUserAnswerStatReq) Reset()         { *m = GetUserAnswerStatReq{} }
func (m *GetUserAnswerStatReq) String() string { return proto.CompactTextString(m) }
func (*GetUserAnswerStatReq) ProtoMessage()    {}
func (*GetUserAnswerStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{5}
}

func (m *GetUserAnswerStatReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetUserAnswerStatResp struct {
}

func (m *GetUserAnswerStatResp) Reset()         { *m = GetUserAnswerStatResp{} }
func (m *GetUserAnswerStatResp) String() string { return proto.CompactTextString(m) }
func (*GetUserAnswerStatResp) ProtoMessage()    {}
func (*GetUserAnswerStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{6}
}

// 获取指定问题的 正确回答用户列表
type GetAnswerCorrectUidListReq struct {
	ActiveId    uint32 `protobuf:"varint,1,req,name=active_id,json=activeId" json:"active_id"`
	QuestionIdx uint32 `protobuf:"varint,2,req,name=question_idx,json=questionIdx" json:"question_idx"`
	BeginIdx    uint32 `protobuf:"varint,3,req,name=begin_idx,json=beginIdx" json:"begin_idx"`
	Limit       uint32 `protobuf:"varint,4,req,name=limit" json:"limit"`
}

func (m *GetAnswerCorrectUidListReq) Reset()         { *m = GetAnswerCorrectUidListReq{} }
func (m *GetAnswerCorrectUidListReq) String() string { return proto.CompactTextString(m) }
func (*GetAnswerCorrectUidListReq) ProtoMessage()    {}
func (*GetAnswerCorrectUidListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{7}
}

func (m *GetAnswerCorrectUidListReq) GetActiveId() uint32 {
	if m != nil {
		return m.ActiveId
	}
	return 0
}

func (m *GetAnswerCorrectUidListReq) GetQuestionIdx() uint32 {
	if m != nil {
		return m.QuestionIdx
	}
	return 0
}

func (m *GetAnswerCorrectUidListReq) GetBeginIdx() uint32 {
	if m != nil {
		return m.BeginIdx
	}
	return 0
}

func (m *GetAnswerCorrectUidListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetAnswerCorrectUidListResp struct {
	UidList  []uint32 `protobuf:"varint,1,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
	TotalCnt uint32   `protobuf:"varint,2,opt,name=total_cnt,json=totalCnt" json:"total_cnt"`
}

func (m *GetAnswerCorrectUidListResp) Reset()         { *m = GetAnswerCorrectUidListResp{} }
func (m *GetAnswerCorrectUidListResp) String() string { return proto.CompactTextString(m) }
func (*GetAnswerCorrectUidListResp) ProtoMessage()    {}
func (*GetAnswerCorrectUidListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{8}
}

func (m *GetAnswerCorrectUidListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetAnswerCorrectUidListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 检查用户在当前活动当前阶段是否有答题资格
type CheckUserAnswerQualifyReq struct {
	Uid uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
}

func (m *CheckUserAnswerQualifyReq) Reset()         { *m = CheckUserAnswerQualifyReq{} }
func (m *CheckUserAnswerQualifyReq) String() string { return proto.CompactTextString(m) }
func (*CheckUserAnswerQualifyReq) ProtoMessage()    {}
func (*CheckUserAnswerQualifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{9}
}

func (m *CheckUserAnswerQualifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckUserAnswerQualifyResp struct {
	IsQualify bool `protobuf:"varint,1,req,name=is_qualify,json=isQualify" json:"is_qualify"`
}

func (m *CheckUserAnswerQualifyResp) Reset()         { *m = CheckUserAnswerQualifyResp{} }
func (m *CheckUserAnswerQualifyResp) String() string { return proto.CompactTextString(m) }
func (*CheckUserAnswerQualifyResp) ProtoMessage()    {}
func (*CheckUserAnswerQualifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{10}
}

func (m *CheckUserAnswerQualifyResp) GetIsQualify() bool {
	if m != nil {
		return m.IsQualify
	}
	return false
}

// 给活动获胜用户发奖
type AwardMoneyToWinUserReq struct {
	ActId            uint32 `protobuf:"varint,1,req,name=act_id,json=actId" json:"act_id"`
	MoneypollFullcnt uint32 `protobuf:"varint,2,req,name=moneypoll_fullcnt,json=moneypollFullcnt" json:"moneypoll_fullcnt"`
}

func (m *AwardMoneyToWinUserReq) Reset()         { *m = AwardMoneyToWinUserReq{} }
func (m *AwardMoneyToWinUserReq) String() string { return proto.CompactTextString(m) }
func (*AwardMoneyToWinUserReq) ProtoMessage()    {}
func (*AwardMoneyToWinUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{11}
}

func (m *AwardMoneyToWinUserReq) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *AwardMoneyToWinUserReq) GetMoneypollFullcnt() uint32 {
	if m != nil {
		return m.MoneypollFullcnt
	}
	return 0
}

type AwardMoneyToWinUserResp struct {
	WinUserCnt uint32 `protobuf:"varint,1,req,name=win_user_cnt,json=winUserCnt" json:"win_user_cnt"`
	MoneyUnit  uint32 `protobuf:"varint,2,req,name=money_unit,json=moneyUnit" json:"money_unit"`
}

func (m *AwardMoneyToWinUserResp) Reset()         { *m = AwardMoneyToWinUserResp{} }
func (m *AwardMoneyToWinUserResp) String() string { return proto.CompactTextString(m) }
func (*AwardMoneyToWinUserResp) ProtoMessage()    {}
func (*AwardMoneyToWinUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{12}
}

func (m *AwardMoneyToWinUserResp) GetWinUserCnt() uint32 {
	if m != nil {
		return m.WinUserCnt
	}
	return 0
}

func (m *AwardMoneyToWinUserResp) GetMoneyUnit() uint32 {
	if m != nil {
		return m.MoneyUnit
	}
	return 0
}

// 淘汰用户
type WashedoutUserReq struct {
	ActId uint32 `protobuf:"varint,1,req,name=act_id,json=actId" json:"act_id"`
	Uid   uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *WashedoutUserReq) Reset()         { *m = WashedoutUserReq{} }
func (m *WashedoutUserReq) String() string { return proto.CompactTextString(m) }
func (*WashedoutUserReq) ProtoMessage()    {}
func (*WashedoutUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{13}
}

func (m *WashedoutUserReq) GetActId() uint32 {
	if m != nil {
		return m.ActId
	}
	return 0
}

func (m *WashedoutUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type WashedoutUserResp struct {
}

func (m *WashedoutUserResp) Reset()         { *m = WashedoutUserResp{} }
func (m *WashedoutUserResp) String() string { return proto.CompactTextString(m) }
func (*WashedoutUserResp) ProtoMessage()    {}
func (*WashedoutUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorTriviagameanswersvr, []int{14}
}

func init() {
	proto.RegisterType((*ResultStat)(nil), "triviagame_answer.ResultStat")
	proto.RegisterType((*AnswerReq)(nil), "triviagame_answer.AnswerReq")
	proto.RegisterType((*AnswerResp)(nil), "triviagame_answer.AnswerResp")
	proto.RegisterType((*GetQuestionResultStatReq)(nil), "triviagame_answer.GetQuestionResultStatReq")
	proto.RegisterType((*GetQuestionResultStatResp)(nil), "triviagame_answer.GetQuestionResultStatResp")
	proto.RegisterType((*GetUserAnswerStatReq)(nil), "triviagame_answer.GetUserAnswerStatReq")
	proto.RegisterType((*GetUserAnswerStatResp)(nil), "triviagame_answer.GetUserAnswerStatResp")
	proto.RegisterType((*GetAnswerCorrectUidListReq)(nil), "triviagame_answer.GetAnswerCorrectUidListReq")
	proto.RegisterType((*GetAnswerCorrectUidListResp)(nil), "triviagame_answer.GetAnswerCorrectUidListResp")
	proto.RegisterType((*CheckUserAnswerQualifyReq)(nil), "triviagame_answer.CheckUserAnswerQualifyReq")
	proto.RegisterType((*CheckUserAnswerQualifyResp)(nil), "triviagame_answer.CheckUserAnswerQualifyResp")
	proto.RegisterType((*AwardMoneyToWinUserReq)(nil), "triviagame_answer.AwardMoneyToWinUserReq")
	proto.RegisterType((*AwardMoneyToWinUserResp)(nil), "triviagame_answer.AwardMoneyToWinUserResp")
	proto.RegisterType((*WashedoutUserReq)(nil), "triviagame_answer.WashedoutUserReq")
	proto.RegisterType((*WashedoutUserResp)(nil), "triviagame_answer.WashedoutUserResp")
}
func (m *ResultStat) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ResultStat) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.AnswerId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.AnswerCnt))
	return i, nil
}

func (m *AnswerReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AnswerReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.QuestionIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.AnswerId))
	return i, nil
}

func (m *AnswerResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AnswerResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.ResulutStatus))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.CorrectAnswerId))
	return i, nil
}

func (m *GetQuestionResultStatReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQuestionResultStatReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.QuestionIdx))
	return i, nil
}

func (m *GetQuestionResultStatResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetQuestionResultStatResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.StatList) > 0 {
		for _, msg := range m.StatList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserAnswerStatReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAnswerStatReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetUserAnswerStatResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserAnswerStatResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetAnswerCorrectUidListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAnswerCorrectUidListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.ActiveId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.QuestionIdx))
	dAtA[i] = 0x18
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.BeginIdx))
	dAtA[i] = 0x20
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.Limit))
	return i, nil
}

func (m *GetAnswerCorrectUidListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetAnswerCorrectUidListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(num))
		}
	}
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.TotalCnt))
	return i, nil
}

func (m *CheckUserAnswerQualifyReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserAnswerQualifyReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *CheckUserAnswerQualifyResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckUserAnswerQualifyResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsQualify {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *AwardMoneyToWinUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardMoneyToWinUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.MoneypollFullcnt))
	return i, nil
}

func (m *AwardMoneyToWinUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *AwardMoneyToWinUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.WinUserCnt))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.MoneyUnit))
	return i, nil
}

func (m *WashedoutUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WashedoutUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.ActId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintTriviagameanswersvr(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *WashedoutUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *WashedoutUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func encodeFixed64Triviagameanswersvr(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Triviagameanswersvr(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintTriviagameanswersvr(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *ResultStat) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.AnswerId))
	n += 1 + sovTriviagameanswersvr(uint64(m.AnswerCnt))
	return n
}

func (m *AnswerReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.ActiveId))
	n += 1 + sovTriviagameanswersvr(uint64(m.QuestionIdx))
	n += 1 + sovTriviagameanswersvr(uint64(m.Uid))
	n += 1 + sovTriviagameanswersvr(uint64(m.AnswerId))
	return n
}

func (m *AnswerResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.ResulutStatus))
	n += 1 + sovTriviagameanswersvr(uint64(m.CorrectAnswerId))
	return n
}

func (m *GetQuestionResultStatReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.ActiveId))
	n += 1 + sovTriviagameanswersvr(uint64(m.QuestionIdx))
	return n
}

func (m *GetQuestionResultStatResp) Size() (n int) {
	var l int
	_ = l
	if len(m.StatList) > 0 {
		for _, e := range m.StatList {
			l = e.Size()
			n += 1 + l + sovTriviagameanswersvr(uint64(l))
		}
	}
	return n
}

func (m *GetUserAnswerStatReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.Uid))
	return n
}

func (m *GetUserAnswerStatResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetAnswerCorrectUidListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.ActiveId))
	n += 1 + sovTriviagameanswersvr(uint64(m.QuestionIdx))
	n += 1 + sovTriviagameanswersvr(uint64(m.BeginIdx))
	n += 1 + sovTriviagameanswersvr(uint64(m.Limit))
	return n
}

func (m *GetAnswerCorrectUidListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovTriviagameanswersvr(uint64(e))
		}
	}
	n += 1 + sovTriviagameanswersvr(uint64(m.TotalCnt))
	return n
}

func (m *CheckUserAnswerQualifyReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.Uid))
	return n
}

func (m *CheckUserAnswerQualifyResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	return n
}

func (m *AwardMoneyToWinUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.ActId))
	n += 1 + sovTriviagameanswersvr(uint64(m.MoneypollFullcnt))
	return n
}

func (m *AwardMoneyToWinUserResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.WinUserCnt))
	n += 1 + sovTriviagameanswersvr(uint64(m.MoneyUnit))
	return n
}

func (m *WashedoutUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovTriviagameanswersvr(uint64(m.ActId))
	n += 1 + sovTriviagameanswersvr(uint64(m.Uid))
	return n
}

func (m *WashedoutUserResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func sovTriviagameanswersvr(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozTriviagameanswersvr(x uint64) (n int) {
	return sovTriviagameanswersvr(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *ResultStat) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ResultStat: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ResultStat: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnswerId", wireType)
			}
			m.AnswerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnswerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnswerCnt", wireType)
			}
			m.AnswerCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnswerCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("answer_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("answer_cnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AnswerReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AnswerReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AnswerReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field AnswerId", wireType)
			}
			m.AnswerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.AnswerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("question_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("answer_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AnswerResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AnswerResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AnswerResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ResulutStatus", wireType)
			}
			m.ResulutStatus = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ResulutStatus |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CorrectAnswerId", wireType)
			}
			m.CorrectAnswerId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CorrectAnswerId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("resulut_status")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("correct_answer_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQuestionResultStatReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQuestionResultStatReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQuestionResultStatReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("question_idx")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetQuestionResultStatResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetQuestionResultStatResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetQuestionResultStatResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field StatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.StatList = append(m.StatList, &ResultStat{})
			if err := m.StatList[len(m.StatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAnswerStatReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAnswerStatReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAnswerStatReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserAnswerStatResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserAnswerStatResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserAnswerStatResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAnswerCorrectUidListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAnswerCorrectUidListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAnswerCorrectUidListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActiveId", wireType)
			}
			m.ActiveId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActiveId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field QuestionIdx", wireType)
			}
			m.QuestionIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.QuestionIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field BeginIdx", wireType)
			}
			m.BeginIdx = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.BeginIdx |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Limit", wireType)
			}
			m.Limit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Limit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("active_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("question_idx")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("begin_idx")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("limit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetAnswerCorrectUidListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetAnswerCorrectUidListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetAnswerCorrectUidListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTriviagameanswersvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowTriviagameanswersvr
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthTriviagameanswersvr
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowTriviagameanswersvr
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TotalCnt", wireType)
			}
			m.TotalCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TotalCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserAnswerQualifyReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserAnswerQualifyReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserAnswerQualifyReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckUserAnswerQualifyResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckUserAnswerQualifyResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckUserAnswerQualifyResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsQualify", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsQualify = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_qualify")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardMoneyToWinUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardMoneyToWinUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardMoneyToWinUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MoneypollFullcnt", wireType)
			}
			m.MoneypollFullcnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MoneypollFullcnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("act_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("moneypoll_fullcnt")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *AwardMoneyToWinUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: AwardMoneyToWinUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: AwardMoneyToWinUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field WinUserCnt", wireType)
			}
			m.WinUserCnt = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.WinUserCnt |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MoneyUnit", wireType)
			}
			m.MoneyUnit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MoneyUnit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("win_user_cnt")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("money_unit")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WashedoutUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WashedoutUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WashedoutUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ActId", wireType)
			}
			m.ActId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ActId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("act_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *WashedoutUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: WashedoutUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: WashedoutUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipTriviagameanswersvr(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthTriviagameanswersvr
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipTriviagameanswersvr(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowTriviagameanswersvr
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowTriviagameanswersvr
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthTriviagameanswersvr
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowTriviagameanswersvr
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipTriviagameanswersvr(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthTriviagameanswersvr = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowTriviagameanswersvr   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("services/triviaGameAnswer/triviagameanswersvr.proto", fileDescriptorTriviagameanswersvr)
}

var fileDescriptorTriviagameanswersvr = []byte{
	// 911 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0xcf, 0x6f, 0x1b, 0x45,
	0x14, 0xee, 0xd8, 0x49, 0x6a, 0xbf, 0x36, 0x10, 0x4f, 0x4a, 0xe2, 0x6c, 0x69, 0xd8, 0x4c, 0x2b,
	0x12, 0x28, 0x76, 0xa0, 0x85, 0x03, 0x56, 0x14, 0x29, 0x8d, 0x84, 0x15, 0x09, 0x24, 0x9a, 0x34,
	0xca, 0x81, 0xc3, 0x6a, 0xbb, 0x3b, 0x69, 0x46, 0x59, 0xef, 0x8e, 0x77, 0x66, 0xec, 0xe6, 0x86,
	0xe8, 0x05, 0x71, 0x82, 0x5e, 0xb8, 0xa3, 0x48, 0x88, 0x13, 0xff, 0x46, 0x8f, 0x5c, 0xb9, 0x20,
	0x14, 0x2e, 0xf9, 0x33, 0xd0, 0xec, 0x8c, 0xe3, 0x5f, 0xeb, 0x36, 0x91, 0x7a, 0xdc, 0xf7, 0xbe,
	0x37, 0xef, 0x7b, 0xdf, 0xbe, 0xf7, 0xc1, 0x43, 0x41, 0xd3, 0x0e, 0x0b, 0xa8, 0x58, 0x97, 0x29,
	0xeb, 0x30, 0xbf, 0xe9, 0xb7, 0xe8, 0x56, 0x2c, 0xba, 0x34, 0xb5, 0x81, 0x67, 0x7e, 0x8b, 0xfa,
	0x59, 0x40, 0x74, 0xd2, 0x3a, 0x4f, 0x13, 0x99, 0xe0, 0x4a, 0x3f, 0xe5, 0x99, 0x9c, 0x73, 0x2f,
	0x48, 0x5a, 0xad, 0x24, 0x5e, 0x97, 0x51, 0x87, 0xb3, 0xe0, 0x38, 0xa2, 0xeb, 0xe2, 0xf8, 0xa9,
	0x62, 0x91, 0x64, 0xb1, 0x3c, 0xe1, 0xd4, 0x14, 0x92, 0x27, 0x00, 0xbb, 0x54, 0xa8, 0x48, 0xee,
	0x49, 0x5f, 0xe2, 0x15, 0x28, 0x9b, 0x6a, 0x8f, 0x85, 0x55, 0xe4, 0x16, 0xd6, 0x66, 0x1f, 0x4d,
	0xbd, 0xfa, 0xe7, 0x83, 0x6b, 0xbb, 0x25, 0x13, 0xde, 0x09, 0xf1, 0x5d, 0x00, 0x0b, 0x09, 0x62,
	0x59, 0x2d, 0x0c, 0x60, 0x6c, 0xe9, 0x76, 0x2c, 0xc9, 0x4b, 0x04, 0x65, 0xc3, 0x79, 0x97, 0xb6,
	0xb3, 0x57, 0x03, 0xc9, 0x3a, 0x74, 0xfc, 0xd5, 0x2c, 0xbc, 0x13, 0xe2, 0x55, 0xb8, 0xd9, 0x56,
	0x54, 0x48, 0x96, 0xc4, 0x1e, 0x0b, 0x9f, 0x0f, 0xbd, 0x7b, 0xa3, 0x97, 0xd9, 0x09, 0x9f, 0xe3,
	0x05, 0x28, 0x2a, 0x16, 0x56, 0x8b, 0x03, 0x79, 0x1d, 0x18, 0x66, 0x3e, 0x95, 0xc7, 0x9c, 0x1c,
	0x03, 0xf4, 0x38, 0x09, 0x8e, 0xef, 0xc3, 0x3b, 0xa9, 0x1e, 0x5c, 0x49, 0x4f, 0x48, 0x5f, 0x2a,
	0x31, 0xc4, 0x6c, 0xd6, 0xe6, 0xf6, 0xb2, 0x14, 0xfe, 0x14, 0x2a, 0x41, 0x92, 0xa6, 0x34, 0x90,
	0x5e, 0xbf, 0xcb, 0x20, 0xc7, 0x77, 0x6d, 0x7a, 0xab, 0xd7, 0xec, 0x10, 0xaa, 0x4d, 0x2a, 0x1f,
	0x5b, 0xe6, 0x7d, 0x89, 0xdf, 0xb2, 0x1e, 0xe4, 0x00, 0x96, 0x26, 0xf4, 0x11, 0x1c, 0x37, 0xa0,
	0xac, 0x67, 0xf3, 0x22, 0x26, 0x64, 0x15, 0xb9, 0xc5, 0xb5, 0x1b, 0x0f, 0xee, 0xd4, 0xc7, 0x36,
	0xa5, 0x3e, 0x50, 0x55, 0xd2, 0xf8, 0xaf, 0x99, 0x90, 0xa4, 0x0e, 0xb7, 0x9a, 0x54, 0xee, 0x0b,
	0x9a, 0x9a, 0x99, 0x7a, 0xe4, 0xed, 0x0f, 0x40, 0x23, 0x3f, 0x80, 0x2c, 0xc2, 0x7b, 0x39, 0x78,
	0xc1, 0xc9, 0xef, 0x08, 0x9c, 0x26, 0xb5, 0xca, 0x6c, 0x1b, 0x99, 0xf6, 0x59, 0xa8, 0x9b, 0xbc,
	0xed, 0xe5, 0x58, 0x81, 0xf2, 0x53, 0xfa, 0x8c, 0x19, 0xd4, 0xe0, 0x8a, 0x94, 0xb2, 0xb0, 0x86,
	0x38, 0x30, 0x1d, 0xb1, 0x16, 0x93, 0x43, 0x3b, 0x62, 0x42, 0xe4, 0x3b, 0xb8, 0x3d, 0x91, 0xa8,
	0xe0, 0x78, 0x09, 0x4a, 0x8a, 0x85, 0x7d, 0x31, 0x67, 0x77, 0xaf, 0x2b, 0x93, 0xd6, 0x8d, 0x65,
	0x22, 0xfd, 0xc8, 0xde, 0x04, 0xea, 0x37, 0xce, 0xc2, 0xfa, 0x24, 0x1e, 0xc2, 0xd2, 0xf6, 0x11,
	0x0d, 0x8e, 0xfb, 0x0a, 0x3d, 0x56, 0x7e, 0xc4, 0x0e, 0x4f, 0x5e, 0x27, 0xea, 0x16, 0x38, 0x93,
	0x8a, 0x04, 0xd7, 0xa7, 0xc8, 0x84, 0xd7, 0x36, 0x91, 0xac, 0xb8, 0xd4, 0x3b, 0x45, 0x26, 0x2c,
	0x90, 0x1c, 0xc1, 0xc2, 0x56, 0xd7, 0x4f, 0xc3, 0x6f, 0x92, 0x98, 0x9e, 0x3c, 0x49, 0x0e, 0x58,
	0xac, 0x1f, 0xd3, 0x4d, 0x6f, 0xc3, 0x8c, 0x1f, 0xc8, 0x51, 0xd9, 0xa7, 0xfd, 0x40, 0xee, 0x84,
	0xf8, 0x33, 0xa8, 0xb4, 0x74, 0x05, 0x4f, 0xa2, 0xc8, 0x3b, 0x54, 0x51, 0x34, 0x7a, 0xed, 0x73,
	0x17, 0xe9, 0xaf, 0x4c, 0x96, 0x1c, 0xc2, 0x62, 0x6e, 0x27, 0xc1, 0xf1, 0x87, 0x70, 0xb3, 0xcb,
	0x62, 0x4f, 0x09, 0x6b, 0x1b, 0x83, 0x0d, 0xa1, 0x6b, 0x80, 0xdb, 0xb1, 0xd4, 0x13, 0x65, 0xcf,
	0x7a, 0x2a, 0x66, 0x23, 0xe6, 0x92, 0xc5, 0xf7, 0x63, 0x26, 0x49, 0x13, 0xe6, 0x0e, 0x7c, 0x71,
	0x44, 0xc3, 0x44, 0xc9, 0x4b, 0xcd, 0x62, 0xd5, 0x2d, 0x8c, 0xaa, 0x3b, 0x0f, 0x95, 0x91, 0x87,
	0x04, 0x7f, 0xf0, 0x77, 0x09, 0xe6, 0x47, 0x8d, 0x77, 0xaf, 0x93, 0xe2, 0x5f, 0x11, 0xcc, 0x98,
	0x2f, 0xfc, 0x7e, 0xce, 0x0d, 0x5d, 0xb8, 0x9d, 0x73, 0xe7, 0x35, 0x59, 0xc1, 0xc9, 0xb7, 0xdf,
	0x9f, 0x9e, 0x17, 0xd1, 0x4f, 0xa7, 0xe7, 0xc5, 0x92, 0xdf, 0x68, 0x37, 0x54, 0x43, 0x34, 0x5e,
	0x9e, 0x9e, 0x17, 0xbf, 0xac, 0xf9, 0xee, 0x86, 0xd9, 0x78, 0x6d, 0x2e, 0x9b, 0x6e, 0xad, 0xed,
	0x6e, 0x0c, 0x6e, 0xfd, 0xa6, 0x5b, 0x53, 0xee, 0x86, 0xca, 0x52, 0xc2, 0xdd, 0xb8, 0xb0, 0xa1,
	0x4d, 0xfc, 0x07, 0xca, 0x4e, 0x6f, 0xdc, 0x03, 0xf0, 0xfd, 0x1c, 0x2a, 0x93, 0x5c, 0xc9, 0xf9,
	0xe4, 0xf2, 0x60, 0xc1, 0xc9, 0x17, 0x7a, 0x8c, 0x82, 0x1e, 0x63, 0x4a, 0x8f, 0xa1, 0x47, 0x20,
	0x6f, 0x1e, 0x01, 0xff, 0x80, 0xa0, 0x32, 0x66, 0x13, 0x78, 0x35, 0xbf, 0xf5, 0x98, 0xf9, 0x38,
	0x6b, 0x97, 0x03, 0x0a, 0x4e, 0x1c, 0xcd, 0xaf, 0xa8, 0xf9, 0x15, 0x54, 0xc6, 0xae, 0x7c, 0x21,
	0x1f, 0xfe, 0x13, 0xc1, 0xe2, 0x84, 0x43, 0xc7, 0xb5, 0xfc, 0x0e, 0x13, 0xdc, 0xcb, 0xa9, 0x5f,
	0x05, 0xde, 0x93, 0x6d, 0xea, 0xca, 0xb2, 0xfd, 0x82, 0x60, 0x21, 0xdf, 0x08, 0x70, 0xde, 0x6f,
	0x9b, 0x68, 0x34, 0x4e, 0xed, 0x0a, 0xe8, 0x9e, 0x8a, 0xd3, 0xf9, 0x2a, 0xfe, 0x86, 0x60, 0x3e,
	0xe7, 0xde, 0xf1, 0x47, 0x79, 0xfb, 0x9f, 0xeb, 0x40, 0xce, 0xc7, 0x97, 0x85, 0x0a, 0x4e, 0x3e,
	0xd7, 0x54, 0x66, 0xac, 0x72, 0xe6, 0x66, 0x56, 0xac, 0x72, 0x6e, 0xef, 0x28, 0x32, 0x8f, 0x70,
	0x45, 0x90, 0xa4, 0xd4, 0xd5, 0x0e, 0xb2, 0x89, 0x5f, 0x20, 0x98, 0x1d, 0xba, 0x71, 0x7c, 0x37,
	0xa7, 0xe7, 0xa8, 0x9d, 0x38, 0xf7, 0xde, 0x0c, 0x12, 0x9c, 0xac, 0x6a, 0x4a, 0xd7, 0x2d, 0x25,
	0xa3, 0xcf, 0xad, 0x21, 0x4a, 0x56, 0x2b, 0x67, 0xe6, 0xc7, 0xd3, 0xf3, 0xe2, 0x8b, 0xee, 0xa3,
	0xb9, 0x57, 0x67, 0xcb, 0xe8, 0xaf, 0xb3, 0x65, 0xf4, 0xef, 0xd9, 0x32, 0xfa, 0xf9, 0xbf, 0xe5,
	0x6b, 0xff, 0x07, 0x00, 0x00, 0xff, 0xff, 0x0d, 0x23, 0x82, 0x13, 0xed, 0x09, 0x00, 0x00,
}
