// Code generated by protoc-gen-gogo.
// source: src/channelDatingGame/channeldatinggame.proto
// DO NOT EDIT!

/*
	Package channeldatinggame is a generated protocol buffer package.

	namespace

	It is generated from these files:
		src/channelDatingGame/channeldatinggame.proto

	It has these top-level messages:
		HatUser
		GetDatingGameCurInfoReq
		GetDatingGameCurInfoResp
		OpenDatingGameEntryReq
		OpenDatingGameEntryResp
		CloseDatingGameEntryReq
		CloseDatingGameEntryResp
		CheckDatingGameEntryReq
		CheckDatingGameEntryResp
		SetGamePhaseReq
		SetGamePhaseResp
		GetGamePhaseReq
		GetGamePhaseResp
		DatingMember
		InitDatingMemberReq
		InitDatingMemberResp
		GetUserLikeBeatValsReq
		GetUserLikeBeatValsResp
		GetUserRankLikeBeatValsReq
		GetUserRankLikeBeatValsResp
		UserLikeBeatInfo
		SetUserLikeBeatObjReq
		SetUserLikeBeatObjResp
		GetUserLikeBeatObjReq
		GetUserLikeBeatObjResp
		GetSelectLikeBeatObjUserReq
		GetSelectLikeBeatObjUserResp
		MatchLikeBeatInfo
		GetOpenLikeUserListReq
		GetOpenLikeUserListResp
		OpenLikeUserInfo
		UserApplyMicReq
		UserApplyMicResp
		GetApplyMicUserListReq
		GetApplyMicUserListResp
		DatingGameHatCfg
		GetVipMicUserReq
		GetVipMicUserResp
		ConfirmVipHoldMicReq
		ConfirmVipHoldMicResp
		TestDrawImageReq
*/
package channeldatinggame

import "github.com/gogo/protobuf/proto"
import "fmt"
import "math"
import "gitlab.ttyuyin.com/golang/svrkit/protocol/common/tlvpickle"

import (
	"golang.org/x/net/context"
	"google.golang.org/grpc"
)

import io1 "io"
import fmt2 "fmt"
import github_com_gogo_protobuf_proto1 "github.com/gogo/protobuf/proto"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion2 // please upgrade the proto package

type HatUser struct {
	Uid    uint32            `protobuf:"varint,1,opt,name=uid" json:"uid"`
	HatCfg *DatingGameHatCfg `protobuf:"bytes,2,opt,name=hat_cfg,json=hatCfg" json:"hat_cfg,omitempty"`
}

func (m *HatUser) Reset()                    { *m = HatUser{} }
func (m *HatUser) String() string            { return proto.CompactTextString(m) }
func (*HatUser) ProtoMessage()               {}
func (*HatUser) Descriptor() ([]byte, []int) { return fileDescriptorChanneldatinggame, []int{0} }

func (m *HatUser) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *HatUser) GetHatCfg() *DatingGameHatCfg {
	if m != nil {
		return m.HatCfg
	}
	return nil
}

// 获取相亲房信息
// 用于进房的时候拉取初始信息（比如麦上用户的心动值 / 当前帽子用户 / 当前土豪位用户 / 当前阶段 ）
type GetDatingGameCurInfoReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetDatingGameCurInfoReq) Reset()         { *m = GetDatingGameCurInfoReq{} }
func (m *GetDatingGameCurInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetDatingGameCurInfoReq) ProtoMessage()    {}
func (*GetDatingGameCurInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{1}
}

func (m *GetDatingGameCurInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetDatingGameCurInfoResp struct {
	Phase            uint32              `protobuf:"varint,1,req,name=phase" json:"phase"`
	VipUid           uint32              `protobuf:"varint,2,req,name=vip_uid,json=vipUid" json:"vip_uid"`
	HatUserList      []*HatUser          `protobuf:"bytes,3,rep,name=hat_user_list,json=hatUserList" json:"hat_user_list,omitempty"`
	LikeBeatList     []*UserLikeBeatInfo `protobuf:"bytes,4,rep,name=like_beat_list,json=likeBeatList" json:"like_beat_list,omitempty"`
	OpenLikeUserList []*OpenLikeUserInfo `protobuf:"bytes,5,rep,name=open_like_user_list,json=openLikeUserList" json:"open_like_user_list,omitempty"`
	ApplyMicLen      uint32              `protobuf:"varint,6,opt,name=apply_mic_len,json=applyMicLen" json:"apply_mic_len"`
}

func (m *GetDatingGameCurInfoResp) Reset()         { *m = GetDatingGameCurInfoResp{} }
func (m *GetDatingGameCurInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetDatingGameCurInfoResp) ProtoMessage()    {}
func (*GetDatingGameCurInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{2}
}

func (m *GetDatingGameCurInfoResp) GetPhase() uint32 {
	if m != nil {
		return m.Phase
	}
	return 0
}

func (m *GetDatingGameCurInfoResp) GetVipUid() uint32 {
	if m != nil {
		return m.VipUid
	}
	return 0
}

func (m *GetDatingGameCurInfoResp) GetHatUserList() []*HatUser {
	if m != nil {
		return m.HatUserList
	}
	return nil
}

func (m *GetDatingGameCurInfoResp) GetLikeBeatList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatList
	}
	return nil
}

func (m *GetDatingGameCurInfoResp) GetOpenLikeUserList() []*OpenLikeUserInfo {
	if m != nil {
		return m.OpenLikeUserList
	}
	return nil
}

func (m *GetDatingGameCurInfoResp) GetApplyMicLen() uint32 {
	if m != nil {
		return m.ApplyMicLen
	}
	return 0
}

// 开启相亲游戏入口
type OpenDatingGameEntryReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Level     uint32 `protobuf:"varint,2,opt,name=level" json:"level"`
}

func (m *OpenDatingGameEntryReq) Reset()         { *m = OpenDatingGameEntryReq{} }
func (m *OpenDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*OpenDatingGameEntryReq) ProtoMessage()    {}
func (*OpenDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{3}
}

func (m *OpenDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *OpenDatingGameEntryReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

type OpenDatingGameEntryResp struct {
}

func (m *OpenDatingGameEntryResp) Reset()         { *m = OpenDatingGameEntryResp{} }
func (m *OpenDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*OpenDatingGameEntryResp) ProtoMessage()    {}
func (*OpenDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{4}
}

type CloseDatingGameEntryReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CloseDatingGameEntryReq) Reset()         { *m = CloseDatingGameEntryReq{} }
func (m *CloseDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*CloseDatingGameEntryReq) ProtoMessage()    {}
func (*CloseDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{5}
}

func (m *CloseDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CloseDatingGameEntryResp struct {
}

func (m *CloseDatingGameEntryResp) Reset()         { *m = CloseDatingGameEntryResp{} }
func (m *CloseDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*CloseDatingGameEntryResp) ProtoMessage()    {}
func (*CloseDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{6}
}

type CheckDatingGameEntryReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *CheckDatingGameEntryReq) Reset()         { *m = CheckDatingGameEntryReq{} }
func (m *CheckDatingGameEntryReq) String() string { return proto.CompactTextString(m) }
func (*CheckDatingGameEntryReq) ProtoMessage()    {}
func (*CheckDatingGameEntryReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{7}
}

func (m *CheckDatingGameEntryReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type CheckDatingGameEntryResp struct {
	IsOpen bool   `protobuf:"varint,1,req,name=is_open,json=isOpen" json:"is_open"`
	Level  uint32 `protobuf:"varint,2,opt,name=level" json:"level"`
}

func (m *CheckDatingGameEntryResp) Reset()         { *m = CheckDatingGameEntryResp{} }
func (m *CheckDatingGameEntryResp) String() string { return proto.CompactTextString(m) }
func (*CheckDatingGameEntryResp) ProtoMessage()    {}
func (*CheckDatingGameEntryResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{8}
}

func (m *CheckDatingGameEntryResp) GetIsOpen() bool {
	if m != nil {
		return m.IsOpen
	}
	return false
}

func (m *CheckDatingGameEntryResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 设置当前阶段
type SetGamePhaseReq struct {
	ChannelId   uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	TargetPhase uint32 `protobuf:"varint,2,req,name=target_phase,json=targetPhase" json:"target_phase"`
	OpUid       uint32 `protobuf:"varint,3,opt,name=op_uid,json=opUid" json:"op_uid"`
}

func (m *SetGamePhaseReq) Reset()                    { *m = SetGamePhaseReq{} }
func (m *SetGamePhaseReq) String() string            { return proto.CompactTextString(m) }
func (*SetGamePhaseReq) ProtoMessage()               {}
func (*SetGamePhaseReq) Descriptor() ([]byte, []int) { return fileDescriptorChanneldatinggame, []int{9} }

func (m *SetGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetGamePhaseReq) GetTargetPhase() uint32 {
	if m != nil {
		return m.TargetPhase
	}
	return 0
}

func (m *SetGamePhaseReq) GetOpUid() uint32 {
	if m != nil {
		return m.OpUid
	}
	return 0
}

type SetGamePhaseResp struct {
	FromPhase uint32 `protobuf:"varint,1,req,name=from_phase,json=fromPhase" json:"from_phase"`
}

func (m *SetGamePhaseResp) Reset()         { *m = SetGamePhaseResp{} }
func (m *SetGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*SetGamePhaseResp) ProtoMessage()    {}
func (*SetGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{10}
}

func (m *SetGamePhaseResp) GetFromPhase() uint32 {
	if m != nil {
		return m.FromPhase
	}
	return 0
}

type GetGamePhaseReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetGamePhaseReq) Reset()         { *m = GetGamePhaseReq{} }
func (m *GetGamePhaseReq) String() string { return proto.CompactTextString(m) }
func (*GetGamePhaseReq) ProtoMessage()    {}
func (*GetGamePhaseReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{11}
}

func (m *GetGamePhaseReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetGamePhaseResp struct {
	CurrPhase uint32 `protobuf:"varint,1,req,name=curr_phase,json=currPhase" json:"curr_phase"`
}

func (m *GetGamePhaseResp) Reset()         { *m = GetGamePhaseResp{} }
func (m *GetGamePhaseResp) String() string { return proto.CompactTextString(m) }
func (*GetGamePhaseResp) ProtoMessage()    {}
func (*GetGamePhaseResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{12}
}

func (m *GetGamePhaseResp) GetCurrPhase() uint32 {
	if m != nil {
		return m.CurrPhase
	}
	return 0
}

// 初始化相亲游戏的成员(用于初始化 游戏开始时 已经在麦上的)
type DatingMember struct {
	Uid   uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	MicId uint32 `protobuf:"varint,2,req,name=mic_id,json=micId" json:"mic_id"`
	Sex   uint32 `protobuf:"varint,3,req,name=sex" json:"sex"`
}

func (m *DatingMember) Reset()                    { *m = DatingMember{} }
func (m *DatingMember) String() string            { return proto.CompactTextString(m) }
func (*DatingMember) ProtoMessage()               {}
func (*DatingMember) Descriptor() ([]byte, []int) { return fileDescriptorChanneldatinggame, []int{13} }

func (m *DatingMember) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DatingMember) GetMicId() uint32 {
	if m != nil {
		return m.MicId
	}
	return 0
}

func (m *DatingMember) GetSex() uint32 {
	if m != nil {
		return m.Sex
	}
	return 0
}

type InitDatingMemberReq struct {
	ChannelId  uint32          `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	MemberList []*DatingMember `protobuf:"bytes,2,rep,name=member_list,json=memberList" json:"member_list,omitempty"`
}

func (m *InitDatingMemberReq) Reset()         { *m = InitDatingMemberReq{} }
func (m *InitDatingMemberReq) String() string { return proto.CompactTextString(m) }
func (*InitDatingMemberReq) ProtoMessage()    {}
func (*InitDatingMemberReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{14}
}

func (m *InitDatingMemberReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *InitDatingMemberReq) GetMemberList() []*DatingMember {
	if m != nil {
		return m.MemberList
	}
	return nil
}

type InitDatingMemberResp struct {
}

func (m *InitDatingMemberResp) Reset()         { *m = InitDatingMemberResp{} }
func (m *InitDatingMemberResp) String() string { return proto.CompactTextString(m) }
func (*InitDatingMemberResp) ProtoMessage()    {}
func (*InitDatingMemberResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{15}
}

// 获取心动值
type GetUserLikeBeatValsReq struct {
	ChannelId uint32   `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	UidList   []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetUserLikeBeatValsReq) Reset()         { *m = GetUserLikeBeatValsReq{} }
func (m *GetUserLikeBeatValsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatValsReq) ProtoMessage()    {}
func (*GetUserLikeBeatValsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{16}
}

func (m *GetUserLikeBeatValsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserLikeBeatValsReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

type GetUserLikeBeatValsResp struct {
	LikeBeatInfoList []*UserLikeBeatInfo `protobuf:"bytes,2,rep,name=like_beat_info_list,json=likeBeatInfoList" json:"like_beat_info_list,omitempty"`
}

func (m *GetUserLikeBeatValsResp) Reset()         { *m = GetUserLikeBeatValsResp{} }
func (m *GetUserLikeBeatValsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatValsResp) ProtoMessage()    {}
func (*GetUserLikeBeatValsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{17}
}

func (m *GetUserLikeBeatValsResp) GetLikeBeatInfoList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatInfoList
	}
	return nil
}

type GetUserRankLikeBeatValsReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetUserRankLikeBeatValsReq) Reset()         { *m = GetUserRankLikeBeatValsReq{} }
func (m *GetUserRankLikeBeatValsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserRankLikeBeatValsReq) ProtoMessage()    {}
func (*GetUserRankLikeBeatValsReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{18}
}

func (m *GetUserRankLikeBeatValsReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetUserRankLikeBeatValsResp struct {
	LikeBeatInfoList []*UserLikeBeatInfo `protobuf:"bytes,2,rep,name=like_beat_info_list,json=likeBeatInfoList" json:"like_beat_info_list,omitempty"`
}

func (m *GetUserRankLikeBeatValsResp) Reset()         { *m = GetUserRankLikeBeatValsResp{} }
func (m *GetUserRankLikeBeatValsResp) String() string { return proto.CompactTextString(m) }
func (*GetUserRankLikeBeatValsResp) ProtoMessage()    {}
func (*GetUserRankLikeBeatValsResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{19}
}

func (m *GetUserRankLikeBeatValsResp) GetLikeBeatInfoList() []*UserLikeBeatInfo {
	if m != nil {
		return m.LikeBeatInfoList
	}
	return nil
}

type UserLikeBeatInfo struct {
	Uid          uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	LikeBeatVal  uint32 `protobuf:"varint,2,req,name=like_beat_val,json=likeBeatVal" json:"like_beat_val"`
	SelectStatus bool   `protobuf:"varint,3,opt,name=select_status,json=selectStatus" json:"select_status"`
}

func (m *UserLikeBeatInfo) Reset()         { *m = UserLikeBeatInfo{} }
func (m *UserLikeBeatInfo) String() string { return proto.CompactTextString(m) }
func (*UserLikeBeatInfo) ProtoMessage()    {}
func (*UserLikeBeatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{20}
}

func (m *UserLikeBeatInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserLikeBeatInfo) GetLikeBeatVal() uint32 {
	if m != nil {
		return m.LikeBeatVal
	}
	return 0
}

func (m *UserLikeBeatInfo) GetSelectStatus() bool {
	if m != nil {
		return m.SelectStatus
	}
	return false
}

// 设置心动对象
type SetUserLikeBeatObjReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	SelectUid uint32 `protobuf:"varint,2,req,name=select_uid,json=selectUid" json:"select_uid"`
	LikeUid   uint32 `protobuf:"varint,3,req,name=like_uid,json=likeUid" json:"like_uid"`
}

func (m *SetUserLikeBeatObjReq) Reset()         { *m = SetUserLikeBeatObjReq{} }
func (m *SetUserLikeBeatObjReq) String() string { return proto.CompactTextString(m) }
func (*SetUserLikeBeatObjReq) ProtoMessage()    {}
func (*SetUserLikeBeatObjReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{21}
}

func (m *SetUserLikeBeatObjReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetUserLikeBeatObjReq) GetSelectUid() uint32 {
	if m != nil {
		return m.SelectUid
	}
	return 0
}

func (m *SetUserLikeBeatObjReq) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

type SetUserLikeBeatObjResp struct {
}

func (m *SetUserLikeBeatObjResp) Reset()         { *m = SetUserLikeBeatObjResp{} }
func (m *SetUserLikeBeatObjResp) String() string { return proto.CompactTextString(m) }
func (*SetUserLikeBeatObjResp) ProtoMessage()    {}
func (*SetUserLikeBeatObjResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{22}
}

// 获取心动对象
type GetUserLikeBeatObjReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	OpenUid   uint32 `protobuf:"varint,2,req,name=open_uid,json=openUid" json:"open_uid"`
}

func (m *GetUserLikeBeatObjReq) Reset()         { *m = GetUserLikeBeatObjReq{} }
func (m *GetUserLikeBeatObjReq) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatObjReq) ProtoMessage()    {}
func (*GetUserLikeBeatObjReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{23}
}

func (m *GetUserLikeBeatObjReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetUserLikeBeatObjReq) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

type GetUserLikeBeatObjResp struct {
	LikeUid uint32 `protobuf:"varint,1,req,name=like_uid,json=likeUid" json:"like_uid"`
}

func (m *GetUserLikeBeatObjResp) Reset()         { *m = GetUserLikeBeatObjResp{} }
func (m *GetUserLikeBeatObjResp) String() string { return proto.CompactTextString(m) }
func (*GetUserLikeBeatObjResp) ProtoMessage()    {}
func (*GetUserLikeBeatObjResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{24}
}

func (m *GetUserLikeBeatObjResp) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

type GetSelectLikeBeatObjUserReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,3,req,name=uid" json:"uid"`
}

func (m *GetSelectLikeBeatObjUserReq) Reset()         { *m = GetSelectLikeBeatObjUserReq{} }
func (m *GetSelectLikeBeatObjUserReq) String() string { return proto.CompactTextString(m) }
func (*GetSelectLikeBeatObjUserReq) ProtoMessage()    {}
func (*GetSelectLikeBeatObjUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{25}
}

func (m *GetSelectLikeBeatObjUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetSelectLikeBeatObjUserReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetSelectLikeBeatObjUserResp struct {
	SelectUidList []uint32 `protobuf:"varint,1,rep,name=select_uid_list,json=selectUidList" json:"select_uid_list,omitempty"`
}

func (m *GetSelectLikeBeatObjUserResp) Reset()         { *m = GetSelectLikeBeatObjUserResp{} }
func (m *GetSelectLikeBeatObjUserResp) String() string { return proto.CompactTextString(m) }
func (*GetSelectLikeBeatObjUserResp) ProtoMessage()    {}
func (*GetSelectLikeBeatObjUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{26}
}

func (m *GetSelectLikeBeatObjUserResp) GetSelectUidList() []uint32 {
	if m != nil {
		return m.SelectUidList
	}
	return nil
}

// 相互心动信息
type MatchLikeBeatInfo struct {
	UidA         uint32 `protobuf:"varint,1,req,name=uid_a,json=uidA" json:"uid_a"`
	UidB         uint32 `protobuf:"varint,2,req,name=uid_b,json=uidB" json:"uid_b"`
	LikeBeatValA uint32 `protobuf:"varint,3,req,name=like_beat_val_a,json=likeBeatValA" json:"like_beat_val_a"`
	LikeBeatValB uint32 `protobuf:"varint,4,req,name=like_beat_val_b,json=likeBeatValB" json:"like_beat_val_b"`
}

func (m *MatchLikeBeatInfo) Reset()         { *m = MatchLikeBeatInfo{} }
func (m *MatchLikeBeatInfo) String() string { return proto.CompactTextString(m) }
func (*MatchLikeBeatInfo) ProtoMessage()    {}
func (*MatchLikeBeatInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{27}
}

func (m *MatchLikeBeatInfo) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *MatchLikeBeatInfo) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *MatchLikeBeatInfo) GetLikeBeatValA() uint32 {
	if m != nil {
		return m.LikeBeatValA
	}
	return 0
}

func (m *MatchLikeBeatInfo) GetLikeBeatValB() uint32 {
	if m != nil {
		return m.LikeBeatValB
	}
	return 0
}

// 获取已公布的用户
type GetOpenLikeUserListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetOpenLikeUserListReq) Reset()         { *m = GetOpenLikeUserListReq{} }
func (m *GetOpenLikeUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetOpenLikeUserListReq) ProtoMessage()    {}
func (*GetOpenLikeUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{28}
}

func (m *GetOpenLikeUserListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetOpenLikeUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetOpenLikeUserListResp struct {
	OpenInfoList []*OpenLikeUserInfo `protobuf:"bytes,1,rep,name=open_info_list,json=openInfoList" json:"open_info_list,omitempty"`
}

func (m *GetOpenLikeUserListResp) Reset()         { *m = GetOpenLikeUserListResp{} }
func (m *GetOpenLikeUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetOpenLikeUserListResp) ProtoMessage()    {}
func (*GetOpenLikeUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{29}
}

func (m *GetOpenLikeUserListResp) GetOpenInfoList() []*OpenLikeUserInfo {
	if m != nil {
		return m.OpenInfoList
	}
	return nil
}

type OpenLikeUserInfo struct {
	OpenUid uint32 `protobuf:"varint,1,req,name=open_uid,json=openUid" json:"open_uid"`
	LikeUid uint32 `protobuf:"varint,2,req,name=like_uid,json=likeUid" json:"like_uid"`
}

func (m *OpenLikeUserInfo) Reset()         { *m = OpenLikeUserInfo{} }
func (m *OpenLikeUserInfo) String() string { return proto.CompactTextString(m) }
func (*OpenLikeUserInfo) ProtoMessage()    {}
func (*OpenLikeUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{30}
}

func (m *OpenLikeUserInfo) GetOpenUid() uint32 {
	if m != nil {
		return m.OpenUid
	}
	return 0
}

func (m *OpenLikeUserInfo) GetLikeUid() uint32 {
	if m != nil {
		return m.LikeUid
	}
	return 0
}

// 排麦
type UserApplyMicReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
	IsCancel  bool   `protobuf:"varint,3,opt,name=is_cancel,json=isCancel" json:"is_cancel"`
}

func (m *UserApplyMicReq) Reset()         { *m = UserApplyMicReq{} }
func (m *UserApplyMicReq) String() string { return proto.CompactTextString(m) }
func (*UserApplyMicReq) ProtoMessage()    {}
func (*UserApplyMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{31}
}

func (m *UserApplyMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *UserApplyMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *UserApplyMicReq) GetIsCancel() bool {
	if m != nil {
		return m.IsCancel
	}
	return false
}

type UserApplyMicResp struct {
}

func (m *UserApplyMicResp) Reset()         { *m = UserApplyMicResp{} }
func (m *UserApplyMicResp) String() string { return proto.CompactTextString(m) }
func (*UserApplyMicResp) ProtoMessage()    {}
func (*UserApplyMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{32}
}

// 获取排麦用户列表
type GetApplyMicUserListReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
	Uid       uint32 `protobuf:"varint,2,req,name=uid" json:"uid"`
}

func (m *GetApplyMicUserListReq) Reset()         { *m = GetApplyMicUserListReq{} }
func (m *GetApplyMicUserListReq) String() string { return proto.CompactTextString(m) }
func (*GetApplyMicUserListReq) ProtoMessage()    {}
func (*GetApplyMicUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{33}
}

func (m *GetApplyMicUserListReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *GetApplyMicUserListReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type GetApplyMicUserListResp struct {
	UidList []uint32 `protobuf:"varint,2,rep,name=uid_list,json=uidList" json:"uid_list,omitempty"`
}

func (m *GetApplyMicUserListResp) Reset()         { *m = GetApplyMicUserListResp{} }
func (m *GetApplyMicUserListResp) String() string { return proto.CompactTextString(m) }
func (*GetApplyMicUserListResp) ProtoMessage()    {}
func (*GetApplyMicUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{34}
}

func (m *GetApplyMicUserListResp) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

// 帽子信息
type DatingGameHatCfg struct {
	HatId      uint32 `protobuf:"varint,1,req,name=hat_id,json=hatId" json:"hat_id"`
	Url        string `protobuf:"bytes,2,req,name=url" json:"url"`
	Md5        string `protobuf:"bytes,3,req,name=md5" json:"md5"`
	TbeanLimit uint32 `protobuf:"varint,4,req,name=tbean_limit,json=tbeanLimit" json:"tbean_limit"`
	IsMale     bool   `protobuf:"varint,5,req,name=is_male,json=isMale" json:"is_male"`
	Level      uint32 `protobuf:"varint,6,req,name=level" json:"level"`
}

func (m *DatingGameHatCfg) Reset()         { *m = DatingGameHatCfg{} }
func (m *DatingGameHatCfg) String() string { return proto.CompactTextString(m) }
func (*DatingGameHatCfg) ProtoMessage()    {}
func (*DatingGameHatCfg) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{35}
}

func (m *DatingGameHatCfg) GetHatId() uint32 {
	if m != nil {
		return m.HatId
	}
	return 0
}

func (m *DatingGameHatCfg) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DatingGameHatCfg) GetMd5() string {
	if m != nil {
		return m.Md5
	}
	return ""
}

func (m *DatingGameHatCfg) GetTbeanLimit() uint32 {
	if m != nil {
		return m.TbeanLimit
	}
	return 0
}

func (m *DatingGameHatCfg) GetIsMale() bool {
	if m != nil {
		return m.IsMale
	}
	return false
}

func (m *DatingGameHatCfg) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// 获取vip用户
type GetVipMicUserReq struct {
	ChannelId uint32 `protobuf:"varint,1,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *GetVipMicUserReq) Reset()         { *m = GetVipMicUserReq{} }
func (m *GetVipMicUserReq) String() string { return proto.CompactTextString(m) }
func (*GetVipMicUserReq) ProtoMessage()    {}
func (*GetVipMicUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{36}
}

func (m *GetVipMicUserReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetVipMicUserResp struct {
	VipUid uint32 `protobuf:"varint,1,req,name=vip_uid,json=vipUid" json:"vip_uid"`
}

func (m *GetVipMicUserResp) Reset()         { *m = GetVipMicUserResp{} }
func (m *GetVipMicUserResp) String() string { return proto.CompactTextString(m) }
func (*GetVipMicUserResp) ProtoMessage()    {}
func (*GetVipMicUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{37}
}

func (m *GetVipMicUserResp) GetVipUid() uint32 {
	if m != nil {
		return m.VipUid
	}
	return 0
}

// vip用户上麦确认
type ConfirmVipHoldMicReq struct {
	Uid       uint32 `protobuf:"varint,1,req,name=uid" json:"uid"`
	ChannelId uint32 `protobuf:"varint,2,req,name=channel_id,json=channelId" json:"channel_id"`
}

func (m *ConfirmVipHoldMicReq) Reset()         { *m = ConfirmVipHoldMicReq{} }
func (m *ConfirmVipHoldMicReq) String() string { return proto.CompactTextString(m) }
func (*ConfirmVipHoldMicReq) ProtoMessage()    {}
func (*ConfirmVipHoldMicReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{38}
}

func (m *ConfirmVipHoldMicReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ConfirmVipHoldMicReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type ConfirmVipHoldMicResp struct {
	PreVipUid uint32 `protobuf:"varint,1,req,name=pre_vip_uid,json=preVipUid" json:"pre_vip_uid"`
}

func (m *ConfirmVipHoldMicResp) Reset()         { *m = ConfirmVipHoldMicResp{} }
func (m *ConfirmVipHoldMicResp) String() string { return proto.CompactTextString(m) }
func (*ConfirmVipHoldMicResp) ProtoMessage()    {}
func (*ConfirmVipHoldMicResp) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{39}
}

func (m *ConfirmVipHoldMicResp) GetPreVipUid() uint32 {
	if m != nil {
		return m.PreVipUid
	}
	return 0
}

type TestDrawImageReq struct {
	Cid  uint32 `protobuf:"varint,1,req,name=cid" json:"cid"`
	UidA uint32 `protobuf:"varint,2,req,name=uid_a,json=uidA" json:"uid_a"`
	UidB uint32 `protobuf:"varint,3,req,name=uid_b,json=uidB" json:"uid_b"`
	VA   uint32 `protobuf:"varint,4,req,name=v_a,json=vA" json:"v_a"`
	VB   uint32 `protobuf:"varint,5,req,name=v_b,json=vB" json:"v_b"`
}

func (m *TestDrawImageReq) Reset()         { *m = TestDrawImageReq{} }
func (m *TestDrawImageReq) String() string { return proto.CompactTextString(m) }
func (*TestDrawImageReq) ProtoMessage()    {}
func (*TestDrawImageReq) Descriptor() ([]byte, []int) {
	return fileDescriptorChanneldatinggame, []int{40}
}

func (m *TestDrawImageReq) GetCid() uint32 {
	if m != nil {
		return m.Cid
	}
	return 0
}

func (m *TestDrawImageReq) GetUidA() uint32 {
	if m != nil {
		return m.UidA
	}
	return 0
}

func (m *TestDrawImageReq) GetUidB() uint32 {
	if m != nil {
		return m.UidB
	}
	return 0
}

func (m *TestDrawImageReq) GetVA() uint32 {
	if m != nil {
		return m.VA
	}
	return 0
}

func (m *TestDrawImageReq) GetVB() uint32 {
	if m != nil {
		return m.VB
	}
	return 0
}

func init() {
	proto.RegisterType((*HatUser)(nil), "channeldatinggame.HatUser")
	proto.RegisterType((*GetDatingGameCurInfoReq)(nil), "channeldatinggame.GetDatingGameCurInfoReq")
	proto.RegisterType((*GetDatingGameCurInfoResp)(nil), "channeldatinggame.GetDatingGameCurInfoResp")
	proto.RegisterType((*OpenDatingGameEntryReq)(nil), "channeldatinggame.OpenDatingGameEntryReq")
	proto.RegisterType((*OpenDatingGameEntryResp)(nil), "channeldatinggame.OpenDatingGameEntryResp")
	proto.RegisterType((*CloseDatingGameEntryReq)(nil), "channeldatinggame.CloseDatingGameEntryReq")
	proto.RegisterType((*CloseDatingGameEntryResp)(nil), "channeldatinggame.CloseDatingGameEntryResp")
	proto.RegisterType((*CheckDatingGameEntryReq)(nil), "channeldatinggame.CheckDatingGameEntryReq")
	proto.RegisterType((*CheckDatingGameEntryResp)(nil), "channeldatinggame.CheckDatingGameEntryResp")
	proto.RegisterType((*SetGamePhaseReq)(nil), "channeldatinggame.SetGamePhaseReq")
	proto.RegisterType((*SetGamePhaseResp)(nil), "channeldatinggame.SetGamePhaseResp")
	proto.RegisterType((*GetGamePhaseReq)(nil), "channeldatinggame.GetGamePhaseReq")
	proto.RegisterType((*GetGamePhaseResp)(nil), "channeldatinggame.GetGamePhaseResp")
	proto.RegisterType((*DatingMember)(nil), "channeldatinggame.DatingMember")
	proto.RegisterType((*InitDatingMemberReq)(nil), "channeldatinggame.InitDatingMemberReq")
	proto.RegisterType((*InitDatingMemberResp)(nil), "channeldatinggame.InitDatingMemberResp")
	proto.RegisterType((*GetUserLikeBeatValsReq)(nil), "channeldatinggame.GetUserLikeBeatValsReq")
	proto.RegisterType((*GetUserLikeBeatValsResp)(nil), "channeldatinggame.GetUserLikeBeatValsResp")
	proto.RegisterType((*GetUserRankLikeBeatValsReq)(nil), "channeldatinggame.GetUserRankLikeBeatValsReq")
	proto.RegisterType((*GetUserRankLikeBeatValsResp)(nil), "channeldatinggame.GetUserRankLikeBeatValsResp")
	proto.RegisterType((*UserLikeBeatInfo)(nil), "channeldatinggame.UserLikeBeatInfo")
	proto.RegisterType((*SetUserLikeBeatObjReq)(nil), "channeldatinggame.SetUserLikeBeatObjReq")
	proto.RegisterType((*SetUserLikeBeatObjResp)(nil), "channeldatinggame.SetUserLikeBeatObjResp")
	proto.RegisterType((*GetUserLikeBeatObjReq)(nil), "channeldatinggame.GetUserLikeBeatObjReq")
	proto.RegisterType((*GetUserLikeBeatObjResp)(nil), "channeldatinggame.GetUserLikeBeatObjResp")
	proto.RegisterType((*GetSelectLikeBeatObjUserReq)(nil), "channeldatinggame.GetSelectLikeBeatObjUserReq")
	proto.RegisterType((*GetSelectLikeBeatObjUserResp)(nil), "channeldatinggame.GetSelectLikeBeatObjUserResp")
	proto.RegisterType((*MatchLikeBeatInfo)(nil), "channeldatinggame.MatchLikeBeatInfo")
	proto.RegisterType((*GetOpenLikeUserListReq)(nil), "channeldatinggame.GetOpenLikeUserListReq")
	proto.RegisterType((*GetOpenLikeUserListResp)(nil), "channeldatinggame.GetOpenLikeUserListResp")
	proto.RegisterType((*OpenLikeUserInfo)(nil), "channeldatinggame.OpenLikeUserInfo")
	proto.RegisterType((*UserApplyMicReq)(nil), "channeldatinggame.UserApplyMicReq")
	proto.RegisterType((*UserApplyMicResp)(nil), "channeldatinggame.UserApplyMicResp")
	proto.RegisterType((*GetApplyMicUserListReq)(nil), "channeldatinggame.GetApplyMicUserListReq")
	proto.RegisterType((*GetApplyMicUserListResp)(nil), "channeldatinggame.GetApplyMicUserListResp")
	proto.RegisterType((*DatingGameHatCfg)(nil), "channeldatinggame.DatingGameHatCfg")
	proto.RegisterType((*GetVipMicUserReq)(nil), "channeldatinggame.GetVipMicUserReq")
	proto.RegisterType((*GetVipMicUserResp)(nil), "channeldatinggame.GetVipMicUserResp")
	proto.RegisterType((*ConfirmVipHoldMicReq)(nil), "channeldatinggame.ConfirmVipHoldMicReq")
	proto.RegisterType((*ConfirmVipHoldMicResp)(nil), "channeldatinggame.ConfirmVipHoldMicResp")
	proto.RegisterType((*TestDrawImageReq)(nil), "channeldatinggame.TestDrawImageReq")
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// Client API for ChannelDatingGame service

type ChannelDatingGameClient interface {
	OpenDatingGameEntry(ctx context.Context, in *OpenDatingGameEntryReq, opts ...grpc.CallOption) (*OpenDatingGameEntryResp, error)
	CheckDatingGameEntry(ctx context.Context, in *CheckDatingGameEntryReq, opts ...grpc.CallOption) (*CheckDatingGameEntryResp, error)
	SetGamePhase(ctx context.Context, in *SetGamePhaseReq, opts ...grpc.CallOption) (*SetGamePhaseResp, error)
	GetGamePhase(ctx context.Context, in *GetGamePhaseReq, opts ...grpc.CallOption) (*GetGamePhaseResp, error)
	GetUserLikeBeatVals(ctx context.Context, in *GetUserLikeBeatValsReq, opts ...grpc.CallOption) (*GetUserLikeBeatValsResp, error)
	GetUserRankLikeBeatVals(ctx context.Context, in *GetUserRankLikeBeatValsReq, opts ...grpc.CallOption) (*GetUserRankLikeBeatValsResp, error)
	SetUserLikeBeatObj(ctx context.Context, in *SetUserLikeBeatObjReq, opts ...grpc.CallOption) (*SetUserLikeBeatObjResp, error)
	GetUserLikeBeatObj(ctx context.Context, in *GetUserLikeBeatObjReq, opts ...grpc.CallOption) (*GetUserLikeBeatObjResp, error)
	UserApplyMic(ctx context.Context, in *UserApplyMicReq, opts ...grpc.CallOption) (*UserApplyMicResp, error)
	GetApplyMicUserList(ctx context.Context, in *GetApplyMicUserListReq, opts ...grpc.CallOption) (*GetApplyMicUserListResp, error)
	InitDatingMember(ctx context.Context, in *InitDatingMemberReq, opts ...grpc.CallOption) (*InitDatingMemberResp, error)
	GetVipMicUser(ctx context.Context, in *GetVipMicUserReq, opts ...grpc.CallOption) (*GetVipMicUserResp, error)
	GetSelectLikeBeatObjUser(ctx context.Context, in *GetSelectLikeBeatObjUserReq, opts ...grpc.CallOption) (*GetSelectLikeBeatObjUserResp, error)
	ConfirmVipHoldMic(ctx context.Context, in *ConfirmVipHoldMicReq, opts ...grpc.CallOption) (*ConfirmVipHoldMicResp, error)
	GetDatingGameCurInfo(ctx context.Context, in *GetDatingGameCurInfoReq, opts ...grpc.CallOption) (*GetDatingGameCurInfoResp, error)
	GetOpenLikeUserList(ctx context.Context, in *GetOpenLikeUserListReq, opts ...grpc.CallOption) (*GetOpenLikeUserListResp, error)
	CloseDatingGameEntry(ctx context.Context, in *CloseDatingGameEntryReq, opts ...grpc.CallOption) (*CloseDatingGameEntryResp, error)
	TestDrawImage(ctx context.Context, in *TestDrawImageReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

type channelDatingGameClient struct {
	cc *grpc.ClientConn
}

func NewChannelDatingGameClient(cc *grpc.ClientConn) ChannelDatingGameClient {
	return &channelDatingGameClient{cc}
}

func (c *channelDatingGameClient) OpenDatingGameEntry(ctx context.Context, in *OpenDatingGameEntryReq, opts ...grpc.CallOption) (*OpenDatingGameEntryResp, error) {
	out := new(OpenDatingGameEntryResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/OpenDatingGameEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) CheckDatingGameEntry(ctx context.Context, in *CheckDatingGameEntryReq, opts ...grpc.CallOption) (*CheckDatingGameEntryResp, error) {
	out := new(CheckDatingGameEntryResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/CheckDatingGameEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) SetGamePhase(ctx context.Context, in *SetGamePhaseReq, opts ...grpc.CallOption) (*SetGamePhaseResp, error) {
	out := new(SetGamePhaseResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/SetGamePhase", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetGamePhase(ctx context.Context, in *GetGamePhaseReq, opts ...grpc.CallOption) (*GetGamePhaseResp, error) {
	out := new(GetGamePhaseResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetGamePhase", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetUserLikeBeatVals(ctx context.Context, in *GetUserLikeBeatValsReq, opts ...grpc.CallOption) (*GetUserLikeBeatValsResp, error) {
	out := new(GetUserLikeBeatValsResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetUserLikeBeatVals", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetUserRankLikeBeatVals(ctx context.Context, in *GetUserRankLikeBeatValsReq, opts ...grpc.CallOption) (*GetUserRankLikeBeatValsResp, error) {
	out := new(GetUserRankLikeBeatValsResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetUserRankLikeBeatVals", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) SetUserLikeBeatObj(ctx context.Context, in *SetUserLikeBeatObjReq, opts ...grpc.CallOption) (*SetUserLikeBeatObjResp, error) {
	out := new(SetUserLikeBeatObjResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/SetUserLikeBeatObj", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetUserLikeBeatObj(ctx context.Context, in *GetUserLikeBeatObjReq, opts ...grpc.CallOption) (*GetUserLikeBeatObjResp, error) {
	out := new(GetUserLikeBeatObjResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetUserLikeBeatObj", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) UserApplyMic(ctx context.Context, in *UserApplyMicReq, opts ...grpc.CallOption) (*UserApplyMicResp, error) {
	out := new(UserApplyMicResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/UserApplyMic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetApplyMicUserList(ctx context.Context, in *GetApplyMicUserListReq, opts ...grpc.CallOption) (*GetApplyMicUserListResp, error) {
	out := new(GetApplyMicUserListResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetApplyMicUserList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) InitDatingMember(ctx context.Context, in *InitDatingMemberReq, opts ...grpc.CallOption) (*InitDatingMemberResp, error) {
	out := new(InitDatingMemberResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/InitDatingMember", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetVipMicUser(ctx context.Context, in *GetVipMicUserReq, opts ...grpc.CallOption) (*GetVipMicUserResp, error) {
	out := new(GetVipMicUserResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetVipMicUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetSelectLikeBeatObjUser(ctx context.Context, in *GetSelectLikeBeatObjUserReq, opts ...grpc.CallOption) (*GetSelectLikeBeatObjUserResp, error) {
	out := new(GetSelectLikeBeatObjUserResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetSelectLikeBeatObjUser", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) ConfirmVipHoldMic(ctx context.Context, in *ConfirmVipHoldMicReq, opts ...grpc.CallOption) (*ConfirmVipHoldMicResp, error) {
	out := new(ConfirmVipHoldMicResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/ConfirmVipHoldMic", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetDatingGameCurInfo(ctx context.Context, in *GetDatingGameCurInfoReq, opts ...grpc.CallOption) (*GetDatingGameCurInfoResp, error) {
	out := new(GetDatingGameCurInfoResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetDatingGameCurInfo", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) GetOpenLikeUserList(ctx context.Context, in *GetOpenLikeUserListReq, opts ...grpc.CallOption) (*GetOpenLikeUserListResp, error) {
	out := new(GetOpenLikeUserListResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/GetOpenLikeUserList", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) CloseDatingGameEntry(ctx context.Context, in *CloseDatingGameEntryReq, opts ...grpc.CallOption) (*CloseDatingGameEntryResp, error) {
	out := new(CloseDatingGameEntryResp)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/CloseDatingGameEntry", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelDatingGameClient) TestDrawImage(ctx context.Context, in *TestDrawImageReq, opts ...grpc.CallOption) (*tlvpickle.SKBuiltinEmpty_PB, error) {
	out := new(tlvpickle.SKBuiltinEmpty_PB)
	err := grpc.Invoke(ctx, "/channeldatinggame.channelDatingGame/TestDrawImage", in, out, c.cc, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Server API for ChannelDatingGame service

type ChannelDatingGameServer interface {
	OpenDatingGameEntry(context.Context, *OpenDatingGameEntryReq) (*OpenDatingGameEntryResp, error)
	CheckDatingGameEntry(context.Context, *CheckDatingGameEntryReq) (*CheckDatingGameEntryResp, error)
	SetGamePhase(context.Context, *SetGamePhaseReq) (*SetGamePhaseResp, error)
	GetGamePhase(context.Context, *GetGamePhaseReq) (*GetGamePhaseResp, error)
	GetUserLikeBeatVals(context.Context, *GetUserLikeBeatValsReq) (*GetUserLikeBeatValsResp, error)
	GetUserRankLikeBeatVals(context.Context, *GetUserRankLikeBeatValsReq) (*GetUserRankLikeBeatValsResp, error)
	SetUserLikeBeatObj(context.Context, *SetUserLikeBeatObjReq) (*SetUserLikeBeatObjResp, error)
	GetUserLikeBeatObj(context.Context, *GetUserLikeBeatObjReq) (*GetUserLikeBeatObjResp, error)
	UserApplyMic(context.Context, *UserApplyMicReq) (*UserApplyMicResp, error)
	GetApplyMicUserList(context.Context, *GetApplyMicUserListReq) (*GetApplyMicUserListResp, error)
	InitDatingMember(context.Context, *InitDatingMemberReq) (*InitDatingMemberResp, error)
	GetVipMicUser(context.Context, *GetVipMicUserReq) (*GetVipMicUserResp, error)
	GetSelectLikeBeatObjUser(context.Context, *GetSelectLikeBeatObjUserReq) (*GetSelectLikeBeatObjUserResp, error)
	ConfirmVipHoldMic(context.Context, *ConfirmVipHoldMicReq) (*ConfirmVipHoldMicResp, error)
	GetDatingGameCurInfo(context.Context, *GetDatingGameCurInfoReq) (*GetDatingGameCurInfoResp, error)
	GetOpenLikeUserList(context.Context, *GetOpenLikeUserListReq) (*GetOpenLikeUserListResp, error)
	CloseDatingGameEntry(context.Context, *CloseDatingGameEntryReq) (*CloseDatingGameEntryResp, error)
	TestDrawImage(context.Context, *TestDrawImageReq) (*tlvpickle.SKBuiltinEmpty_PB, error)
}

func RegisterChannelDatingGameServer(s *grpc.Server, srv ChannelDatingGameServer) {
	s.RegisterService(&_ChannelDatingGame_serviceDesc, srv)
}

func _ChannelDatingGame_OpenDatingGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OpenDatingGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).OpenDatingGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/OpenDatingGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).OpenDatingGameEntry(ctx, req.(*OpenDatingGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_CheckDatingGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckDatingGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).CheckDatingGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/CheckDatingGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).CheckDatingGameEntry(ctx, req.(*CheckDatingGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_SetGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).SetGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/SetGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).SetGamePhase(ctx, req.(*SetGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetGamePhase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGamePhaseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetGamePhase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetGamePhase",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetGamePhase(ctx, req.(*GetGamePhaseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetUserLikeBeatVals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLikeBeatValsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatVals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetUserLikeBeatVals",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatVals(ctx, req.(*GetUserLikeBeatValsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetUserRankLikeBeatVals_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRankLikeBeatValsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetUserRankLikeBeatVals(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetUserRankLikeBeatVals",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetUserRankLikeBeatVals(ctx, req.(*GetUserRankLikeBeatValsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_SetUserLikeBeatObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserLikeBeatObjReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).SetUserLikeBeatObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/SetUserLikeBeatObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).SetUserLikeBeatObj(ctx, req.(*SetUserLikeBeatObjReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetUserLikeBeatObj_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserLikeBeatObjReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatObj(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetUserLikeBeatObj",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetUserLikeBeatObj(ctx, req.(*GetUserLikeBeatObjReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_UserApplyMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserApplyMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).UserApplyMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/UserApplyMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).UserApplyMic(ctx, req.(*UserApplyMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetApplyMicUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetApplyMicUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetApplyMicUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetApplyMicUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetApplyMicUserList(ctx, req.(*GetApplyMicUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_InitDatingMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitDatingMemberReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).InitDatingMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/InitDatingMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).InitDatingMember(ctx, req.(*InitDatingMemberReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetVipMicUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVipMicUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetVipMicUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetVipMicUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetVipMicUser(ctx, req.(*GetVipMicUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetSelectLikeBeatObjUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSelectLikeBeatObjUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetSelectLikeBeatObjUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetSelectLikeBeatObjUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetSelectLikeBeatObjUser(ctx, req.(*GetSelectLikeBeatObjUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_ConfirmVipHoldMic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConfirmVipHoldMicReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).ConfirmVipHoldMic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/ConfirmVipHoldMic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).ConfirmVipHoldMic(ctx, req.(*ConfirmVipHoldMicReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetDatingGameCurInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDatingGameCurInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetDatingGameCurInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetDatingGameCurInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetDatingGameCurInfo(ctx, req.(*GetDatingGameCurInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_GetOpenLikeUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpenLikeUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).GetOpenLikeUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/GetOpenLikeUserList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).GetOpenLikeUserList(ctx, req.(*GetOpenLikeUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_CloseDatingGameEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseDatingGameEntryReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).CloseDatingGameEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/CloseDatingGameEntry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).CloseDatingGameEntry(ctx, req.(*CloseDatingGameEntryReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelDatingGame_TestDrawImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TestDrawImageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelDatingGameServer).TestDrawImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channeldatinggame.channelDatingGame/TestDrawImage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelDatingGameServer).TestDrawImage(ctx, req.(*TestDrawImageReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelDatingGame_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channeldatinggame.channelDatingGame",
	HandlerType: (*ChannelDatingGameServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OpenDatingGameEntry",
			Handler:    _ChannelDatingGame_OpenDatingGameEntry_Handler,
		},
		{
			MethodName: "CheckDatingGameEntry",
			Handler:    _ChannelDatingGame_CheckDatingGameEntry_Handler,
		},
		{
			MethodName: "SetGamePhase",
			Handler:    _ChannelDatingGame_SetGamePhase_Handler,
		},
		{
			MethodName: "GetGamePhase",
			Handler:    _ChannelDatingGame_GetGamePhase_Handler,
		},
		{
			MethodName: "GetUserLikeBeatVals",
			Handler:    _ChannelDatingGame_GetUserLikeBeatVals_Handler,
		},
		{
			MethodName: "GetUserRankLikeBeatVals",
			Handler:    _ChannelDatingGame_GetUserRankLikeBeatVals_Handler,
		},
		{
			MethodName: "SetUserLikeBeatObj",
			Handler:    _ChannelDatingGame_SetUserLikeBeatObj_Handler,
		},
		{
			MethodName: "GetUserLikeBeatObj",
			Handler:    _ChannelDatingGame_GetUserLikeBeatObj_Handler,
		},
		{
			MethodName: "UserApplyMic",
			Handler:    _ChannelDatingGame_UserApplyMic_Handler,
		},
		{
			MethodName: "GetApplyMicUserList",
			Handler:    _ChannelDatingGame_GetApplyMicUserList_Handler,
		},
		{
			MethodName: "InitDatingMember",
			Handler:    _ChannelDatingGame_InitDatingMember_Handler,
		},
		{
			MethodName: "GetVipMicUser",
			Handler:    _ChannelDatingGame_GetVipMicUser_Handler,
		},
		{
			MethodName: "GetSelectLikeBeatObjUser",
			Handler:    _ChannelDatingGame_GetSelectLikeBeatObjUser_Handler,
		},
		{
			MethodName: "ConfirmVipHoldMic",
			Handler:    _ChannelDatingGame_ConfirmVipHoldMic_Handler,
		},
		{
			MethodName: "GetDatingGameCurInfo",
			Handler:    _ChannelDatingGame_GetDatingGameCurInfo_Handler,
		},
		{
			MethodName: "GetOpenLikeUserList",
			Handler:    _ChannelDatingGame_GetOpenLikeUserList_Handler,
		},
		{
			MethodName: "CloseDatingGameEntry",
			Handler:    _ChannelDatingGame_CloseDatingGameEntry_Handler,
		},
		{
			MethodName: "TestDrawImage",
			Handler:    _ChannelDatingGame_TestDrawImage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "src/channelDatingGame/channeldatinggame.proto",
}

func (m *HatUser) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *HatUser) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	if m.HatCfg != nil {
		dAtA[i] = 0x12
		i++
		i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.HatCfg.Size()))
		n1, err := m.HatCfg.MarshalTo(dAtA[i:])
		if err != nil {
			return 0, err
		}
		i += n1
	}
	return i, nil
}

func (m *GetDatingGameCurInfoReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDatingGameCurInfoReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetDatingGameCurInfoResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetDatingGameCurInfoResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Phase))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.VipUid))
	if len(m.HatUserList) > 0 {
		for _, msg := range m.HatUserList {
			dAtA[i] = 0x1a
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.LikeBeatList) > 0 {
		for _, msg := range m.LikeBeatList {
			dAtA[i] = 0x22
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	if len(m.OpenLikeUserList) > 0 {
		for _, msg := range m.OpenLikeUserList {
			dAtA[i] = 0x2a
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	dAtA[i] = 0x30
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ApplyMicLen))
	return i, nil
}

func (m *OpenDatingGameEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenDatingGameEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *OpenDatingGameEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenDatingGameEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CloseDatingGameEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseDatingGameEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *CloseDatingGameEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CloseDatingGameEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *CheckDatingGameEntryReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckDatingGameEntryReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *CheckDatingGameEntryResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *CheckDatingGameEntryResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	if m.IsOpen {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *SetGamePhaseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePhaseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.TargetPhase))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.OpUid))
	return i, nil
}

func (m *SetGamePhaseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetGamePhaseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.FromPhase))
	return i, nil
}

func (m *GetGamePhaseReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePhaseReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetGamePhaseResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetGamePhaseResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.CurrPhase))
	return i, nil
}

func (m *DatingMember) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingMember) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.MicId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Sex))
	return i, nil
}

func (m *InitDatingMemberReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InitDatingMemberReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	if len(m.MemberList) > 0 {
		for _, msg := range m.MemberList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *InitDatingMemberResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *InitDatingMemberResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserLikeBeatValsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLikeBeatValsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *GetUserLikeBeatValsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLikeBeatValsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LikeBeatInfoList) > 0 {
		for _, msg := range m.LikeBeatInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *GetUserRankLikeBeatValsReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRankLikeBeatValsReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetUserRankLikeBeatValsResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserRankLikeBeatValsResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.LikeBeatInfoList) > 0 {
		for _, msg := range m.LikeBeatInfoList {
			dAtA[i] = 0x12
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *UserLikeBeatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserLikeBeatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.LikeBeatVal))
	dAtA[i] = 0x18
	i++
	if m.SelectStatus {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *SetUserLikeBeatObjReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserLikeBeatObjReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.SelectUid))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.LikeUid))
	return i, nil
}

func (m *SetUserLikeBeatObjResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *SetUserLikeBeatObjResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetUserLikeBeatObjReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLikeBeatObjReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.OpenUid))
	return i, nil
}

func (m *GetUserLikeBeatObjResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetUserLikeBeatObjResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.LikeUid))
	return i, nil
}

func (m *GetSelectLikeBeatObjUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSelectLikeBeatObjUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetSelectLikeBeatObjUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetSelectLikeBeatObjUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.SelectUidList) > 0 {
		for _, num := range m.SelectUidList {
			dAtA[i] = 0x8
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *MatchLikeBeatInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *MatchLikeBeatInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.UidA))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.UidB))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.LikeBeatValA))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.LikeBeatValB))
	return i, nil
}

func (m *GetOpenLikeUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOpenLikeUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetOpenLikeUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetOpenLikeUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.OpenInfoList) > 0 {
		for _, msg := range m.OpenInfoList {
			dAtA[i] = 0xa
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(msg.Size()))
			n, err := msg.MarshalTo(dAtA[i:])
			if err != nil {
				return 0, err
			}
			i += n
		}
	}
	return i, nil
}

func (m *OpenLikeUserInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OpenLikeUserInfo) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.OpenUid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.LikeUid))
	return i, nil
}

func (m *UserApplyMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserApplyMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x18
	i++
	if m.IsCancel {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	return i, nil
}

func (m *UserApplyMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *UserApplyMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	return i, nil
}

func (m *GetApplyMicUserListReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyMicUserListReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	return i, nil
}

func (m *GetApplyMicUserListResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetApplyMicUserListResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, num := range m.UidList {
			dAtA[i] = 0x10
			i++
			i = encodeVarintChanneldatinggame(dAtA, i, uint64(num))
		}
	}
	return i, nil
}

func (m *DatingGameHatCfg) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *DatingGameHatCfg) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.HatId))
	dAtA[i] = 0x12
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(len(m.Url)))
	i += copy(dAtA[i:], m.Url)
	dAtA[i] = 0x1a
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(len(m.Md5)))
	i += copy(dAtA[i:], m.Md5)
	dAtA[i] = 0x20
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.TbeanLimit))
	dAtA[i] = 0x28
	i++
	if m.IsMale {
		dAtA[i] = 1
	} else {
		dAtA[i] = 0
	}
	i++
	dAtA[i] = 0x30
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Level))
	return i, nil
}

func (m *GetVipMicUserReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVipMicUserReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *GetVipMicUserResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *GetVipMicUserResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.VipUid))
	return i, nil
}

func (m *ConfirmVipHoldMicReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmVipHoldMicReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Uid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.ChannelId))
	return i, nil
}

func (m *ConfirmVipHoldMicResp) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ConfirmVipHoldMicResp) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.PreVipUid))
	return i, nil
}

func (m *TestDrawImageReq) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalTo(dAtA)
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TestDrawImageReq) MarshalTo(dAtA []byte) (int, error) {
	var i int
	_ = i
	var l int
	_ = l
	dAtA[i] = 0x8
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.Cid))
	dAtA[i] = 0x10
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.UidA))
	dAtA[i] = 0x18
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.UidB))
	dAtA[i] = 0x20
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.VA))
	dAtA[i] = 0x28
	i++
	i = encodeVarintChanneldatinggame(dAtA, i, uint64(m.VB))
	return i, nil
}

func encodeFixed64Channeldatinggame(dAtA []byte, offset int, v uint64) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	dAtA[offset+4] = uint8(v >> 32)
	dAtA[offset+5] = uint8(v >> 40)
	dAtA[offset+6] = uint8(v >> 48)
	dAtA[offset+7] = uint8(v >> 56)
	return offset + 8
}
func encodeFixed32Channeldatinggame(dAtA []byte, offset int, v uint32) int {
	dAtA[offset] = uint8(v)
	dAtA[offset+1] = uint8(v >> 8)
	dAtA[offset+2] = uint8(v >> 16)
	dAtA[offset+3] = uint8(v >> 24)
	return offset + 4
}
func encodeVarintChanneldatinggame(dAtA []byte, offset int, v uint64) int {
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return offset + 1
}
func (m *HatUser) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	if m.HatCfg != nil {
		l = m.HatCfg.Size()
		n += 1 + l + sovChanneldatinggame(uint64(l))
	}
	return n
}

func (m *GetDatingGameCurInfoReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *GetDatingGameCurInfoResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.Phase))
	n += 1 + sovChanneldatinggame(uint64(m.VipUid))
	if len(m.HatUserList) > 0 {
		for _, e := range m.HatUserList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	if len(m.LikeBeatList) > 0 {
		for _, e := range m.LikeBeatList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	if len(m.OpenLikeUserList) > 0 {
		for _, e := range m.OpenLikeUserList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	n += 1 + sovChanneldatinggame(uint64(m.ApplyMicLen))
	return n
}

func (m *OpenDatingGameEntryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.Level))
	return n
}

func (m *OpenDatingGameEntryResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CloseDatingGameEntryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *CloseDatingGameEntryResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *CheckDatingGameEntryReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *CheckDatingGameEntryResp) Size() (n int) {
	var l int
	_ = l
	n += 2
	n += 1 + sovChanneldatinggame(uint64(m.Level))
	return n
}

func (m *SetGamePhaseReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.TargetPhase))
	n += 1 + sovChanneldatinggame(uint64(m.OpUid))
	return n
}

func (m *SetGamePhaseResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.FromPhase))
	return n
}

func (m *GetGamePhaseReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *GetGamePhaseResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.CurrPhase))
	return n
}

func (m *DatingMember) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	n += 1 + sovChanneldatinggame(uint64(m.MicId))
	n += 1 + sovChanneldatinggame(uint64(m.Sex))
	return n
}

func (m *InitDatingMemberReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	if len(m.MemberList) > 0 {
		for _, e := range m.MemberList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	return n
}

func (m *InitDatingMemberResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserLikeBeatValsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChanneldatinggame(uint64(e))
		}
	}
	return n
}

func (m *GetUserLikeBeatValsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LikeBeatInfoList) > 0 {
		for _, e := range m.LikeBeatInfoList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	return n
}

func (m *GetUserRankLikeBeatValsReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *GetUserRankLikeBeatValsResp) Size() (n int) {
	var l int
	_ = l
	if len(m.LikeBeatInfoList) > 0 {
		for _, e := range m.LikeBeatInfoList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	return n
}

func (m *UserLikeBeatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	n += 1 + sovChanneldatinggame(uint64(m.LikeBeatVal))
	n += 2
	return n
}

func (m *SetUserLikeBeatObjReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.SelectUid))
	n += 1 + sovChanneldatinggame(uint64(m.LikeUid))
	return n
}

func (m *SetUserLikeBeatObjResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetUserLikeBeatObjReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.OpenUid))
	return n
}

func (m *GetUserLikeBeatObjResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.LikeUid))
	return n
}

func (m *GetSelectLikeBeatObjUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	return n
}

func (m *GetSelectLikeBeatObjUserResp) Size() (n int) {
	var l int
	_ = l
	if len(m.SelectUidList) > 0 {
		for _, e := range m.SelectUidList {
			n += 1 + sovChanneldatinggame(uint64(e))
		}
	}
	return n
}

func (m *MatchLikeBeatInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.UidA))
	n += 1 + sovChanneldatinggame(uint64(m.UidB))
	n += 1 + sovChanneldatinggame(uint64(m.LikeBeatValA))
	n += 1 + sovChanneldatinggame(uint64(m.LikeBeatValB))
	return n
}

func (m *GetOpenLikeUserListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	return n
}

func (m *GetOpenLikeUserListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.OpenInfoList) > 0 {
		for _, e := range m.OpenInfoList {
			l = e.Size()
			n += 1 + l + sovChanneldatinggame(uint64(l))
		}
	}
	return n
}

func (m *OpenLikeUserInfo) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.OpenUid))
	n += 1 + sovChanneldatinggame(uint64(m.LikeUid))
	return n
}

func (m *UserApplyMicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	n += 2
	return n
}

func (m *UserApplyMicResp) Size() (n int) {
	var l int
	_ = l
	return n
}

func (m *GetApplyMicUserListReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	return n
}

func (m *GetApplyMicUserListResp) Size() (n int) {
	var l int
	_ = l
	if len(m.UidList) > 0 {
		for _, e := range m.UidList {
			n += 1 + sovChanneldatinggame(uint64(e))
		}
	}
	return n
}

func (m *DatingGameHatCfg) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.HatId))
	l = len(m.Url)
	n += 1 + l + sovChanneldatinggame(uint64(l))
	l = len(m.Md5)
	n += 1 + l + sovChanneldatinggame(uint64(l))
	n += 1 + sovChanneldatinggame(uint64(m.TbeanLimit))
	n += 2
	n += 1 + sovChanneldatinggame(uint64(m.Level))
	return n
}

func (m *GetVipMicUserReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *GetVipMicUserResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.VipUid))
	return n
}

func (m *ConfirmVipHoldMicReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.Uid))
	n += 1 + sovChanneldatinggame(uint64(m.ChannelId))
	return n
}

func (m *ConfirmVipHoldMicResp) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.PreVipUid))
	return n
}

func (m *TestDrawImageReq) Size() (n int) {
	var l int
	_ = l
	n += 1 + sovChanneldatinggame(uint64(m.Cid))
	n += 1 + sovChanneldatinggame(uint64(m.UidA))
	n += 1 + sovChanneldatinggame(uint64(m.UidB))
	n += 1 + sovChanneldatinggame(uint64(m.VA))
	n += 1 + sovChanneldatinggame(uint64(m.VB))
	return n
}

func sovChanneldatinggame(x uint64) (n int) {
	for {
		n++
		x >>= 7
		if x == 0 {
			break
		}
	}
	return n
}
func sozChanneldatinggame(x uint64) (n int) {
	return sovChanneldatinggame(uint64((x << 1) ^ uint64(int64(x) >> 63)))
}
func (m *HatUser) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: HatUser: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: HatUser: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HatCfg", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			if m.HatCfg == nil {
				m.HatCfg = &DatingGameHatCfg{}
			}
			if err := m.HatCfg.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDatingGameCurInfoReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDatingGameCurInfoReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDatingGameCurInfoReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetDatingGameCurInfoResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetDatingGameCurInfoResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetDatingGameCurInfoResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Phase", wireType)
			}
			m.Phase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Phase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VipUid", wireType)
			}
			m.VipUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VipUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HatUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.HatUserList = append(m.HatUserList, &HatUser{})
			if err := m.HatUserList[len(m.HatUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LikeBeatList = append(m.LikeBeatList, &UserLikeBeatInfo{})
			if err := m.LikeBeatList[len(m.LikeBeatList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenLikeUserList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpenLikeUserList = append(m.OpenLikeUserList, &OpenLikeUserInfo{})
			if err := m.OpenLikeUserList[len(m.OpenLikeUserList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ApplyMicLen", wireType)
			}
			m.ApplyMicLen = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ApplyMicLen |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("phase")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("vip_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenDatingGameEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OpenDatingGameEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OpenDatingGameEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenDatingGameEntryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OpenDatingGameEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OpenDatingGameEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseDatingGameEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CloseDatingGameEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CloseDatingGameEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CloseDatingGameEntryResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CloseDatingGameEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CloseDatingGameEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckDatingGameEntryReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckDatingGameEntryReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckDatingGameEntryReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *CheckDatingGameEntryResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: CheckDatingGameEntryResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: CheckDatingGameEntryResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsOpen", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsOpen = bool(v != 0)
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_open")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePhaseReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePhaseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePhaseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TargetPhase", wireType)
			}
			m.TargetPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TargetPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpUid", wireType)
			}
			m.OpUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("target_phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetGamePhaseResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetGamePhaseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetGamePhaseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field FromPhase", wireType)
			}
			m.FromPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FromPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("from_phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePhaseReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePhaseReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePhaseReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetGamePhaseResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetGamePhaseResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetGamePhaseResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field CurrPhase", wireType)
			}
			m.CurrPhase = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CurrPhase |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("curr_phase")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingMember) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingMember: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingMember: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MicId", wireType)
			}
			m.MicId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.MicId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Sex", wireType)
			}
			m.Sex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Sex |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("mic_id")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("sex")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InitDatingMemberReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InitDatingMemberReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InitDatingMemberReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field MemberList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.MemberList = append(m.MemberList, &DatingMember{})
			if err := m.MemberList[len(m.MemberList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *InitDatingMemberResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: InitDatingMemberResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: InitDatingMemberResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLikeBeatValsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLikeBeatValsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLikeBeatValsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChanneldatinggame
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChanneldatinggame
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLikeBeatValsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLikeBeatValsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLikeBeatValsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LikeBeatInfoList = append(m.LikeBeatInfoList, &UserLikeBeatInfo{})
			if err := m.LikeBeatInfoList[len(m.LikeBeatInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRankLikeBeatValsReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRankLikeBeatValsReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRankLikeBeatValsReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserRankLikeBeatValsResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserRankLikeBeatValsResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserRankLikeBeatValsResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.LikeBeatInfoList = append(m.LikeBeatInfoList, &UserLikeBeatInfo{})
			if err := m.LikeBeatInfoList[len(m.LikeBeatInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserLikeBeatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserLikeBeatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserLikeBeatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatVal", wireType)
			}
			m.LikeBeatVal = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeBeatVal |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelectStatus", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.SelectStatus = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("like_beat_val")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserLikeBeatObjReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserLikeBeatObjReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserLikeBeatObjReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelectUid", wireType)
			}
			m.SelectUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.SelectUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUid", wireType)
			}
			m.LikeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("select_uid")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("like_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *SetUserLikeBeatObjResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: SetUserLikeBeatObjResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: SetUserLikeBeatObjResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLikeBeatObjReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLikeBeatObjReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLikeBeatObjReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenUid", wireType)
			}
			m.OpenUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("open_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetUserLikeBeatObjResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetUserLikeBeatObjResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetUserLikeBeatObjResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUid", wireType)
			}
			m.LikeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("like_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSelectLikeBeatObjUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSelectLikeBeatObjUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSelectLikeBeatObjUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetSelectLikeBeatObjUserResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetSelectLikeBeatObjUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetSelectLikeBeatObjUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.SelectUidList = append(m.SelectUidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChanneldatinggame
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChanneldatinggame
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.SelectUidList = append(m.SelectUidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field SelectUidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *MatchLikeBeatInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: MatchLikeBeatInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: MatchLikeBeatInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidA", wireType)
			}
			m.UidA = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UidA |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidB", wireType)
			}
			m.UidB = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UidB |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatValA", wireType)
			}
			m.LikeBeatValA = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeBeatValA |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeBeatValB", wireType)
			}
			m.LikeBeatValB = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeBeatValB |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid_a")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid_b")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("like_beat_val_a")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("like_beat_val_b")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOpenLikeUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOpenLikeUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOpenLikeUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetOpenLikeUserListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetOpenLikeUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetOpenLikeUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenInfoList", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + msglen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.OpenInfoList = append(m.OpenInfoList, &OpenLikeUserInfo{})
			if err := m.OpenInfoList[len(m.OpenInfoList)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *OpenLikeUserInfo) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: OpenLikeUserInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: OpenLikeUserInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field OpenUid", wireType)
			}
			m.OpenUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OpenUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field LikeUid", wireType)
			}
			m.LikeUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.LikeUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("open_uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("like_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserApplyMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserApplyMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserApplyMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsCancel", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsCancel = bool(v != 0)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *UserApplyMicResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: UserApplyMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: UserApplyMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyMicUserListReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyMicUserListReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyMicUserListReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetApplyMicUserListResp) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetApplyMicUserListResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetApplyMicUserListResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 2:
			if wireType == 0 {
				var v uint32
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					v |= (uint32(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				m.UidList = append(m.UidList, v)
			} else if wireType == 2 {
				var packedLen int
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					packedLen |= (int(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				if packedLen < 0 {
					return ErrInvalidLengthChanneldatinggame
				}
				postIndex := iNdEx + packedLen
				if postIndex > l {
					return io1.ErrUnexpectedEOF
				}
				for iNdEx < postIndex {
					var v uint32
					for shift := uint(0); ; shift += 7 {
						if shift >= 64 {
							return ErrIntOverflowChanneldatinggame
						}
						if iNdEx >= l {
							return io1.ErrUnexpectedEOF
						}
						b := dAtA[iNdEx]
						iNdEx++
						v |= (uint32(b) & 0x7F) << shift
						if b < 0x80 {
							break
						}
					}
					m.UidList = append(m.UidList, v)
				}
			} else {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidList", wireType)
			}
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *DatingGameHatCfg) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: DatingGameHatCfg: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: DatingGameHatCfg: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field HatId", wireType)
			}
			m.HatId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.HatId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Url", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Url = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 2 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Md5", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= (uint64(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			postIndex := iNdEx + intStringLen
			if postIndex > l {
				return io1.ErrUnexpectedEOF
			}
			m.Md5 = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field TbeanLimit", wireType)
			}
			m.TbeanLimit = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.TbeanLimit |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field IsMale", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.IsMale = bool(v != 0)
			hasFields[0] |= uint64(0x00000010)
		case 6:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Level", wireType)
			}
			m.Level = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Level |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000020)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("hat_id")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("url")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("md5")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("tbean_limit")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("is_male")
	}
	if hasFields[0]&uint64(0x00000020) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("level")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVipMicUserReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetVipMicUserReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetVipMicUserReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *GetVipMicUserResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: GetVipMicUserResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: GetVipMicUserResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VipUid", wireType)
			}
			m.VipUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VipUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("vip_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmVipHoldMicReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmVipHoldMicReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmVipHoldMicReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Uid", wireType)
			}
			m.Uid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Uid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field ChannelId", wireType)
			}
			m.ChannelId = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ChannelId |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("channel_id")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *ConfirmVipHoldMicResp) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: ConfirmVipHoldMicResp: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: ConfirmVipHoldMicResp: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field PreVipUid", wireType)
			}
			m.PreVipUid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.PreVipUid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("pre_vip_uid")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func (m *TestDrawImageReq) Unmarshal(dAtA []byte) error {
	var hasFields [1]uint64
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt2.Errorf("proto: TestDrawImageReq: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt2.Errorf("proto: TestDrawImageReq: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field Cid", wireType)
			}
			m.Cid = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Cid |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000001)
		case 2:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidA", wireType)
			}
			m.UidA = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UidA |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000002)
		case 3:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field UidB", wireType)
			}
			m.UidB = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.UidB |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000004)
		case 4:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VA", wireType)
			}
			m.VA = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VA |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000008)
		case 5:
			if wireType != 0 {
				return fmt2.Errorf("proto: wrong wireType = %d for field VB", wireType)
			}
			m.VB = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.VB |= (uint32(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			hasFields[0] |= uint64(0x00000010)
		default:
			iNdEx = preIndex
			skippy, err := skipChanneldatinggame(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthChanneldatinggame
			}
			if (iNdEx + skippy) > l {
				return io1.ErrUnexpectedEOF
			}
			iNdEx += skippy
		}
	}
	if hasFields[0]&uint64(0x00000001) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("cid")
	}
	if hasFields[0]&uint64(0x00000002) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid_a")
	}
	if hasFields[0]&uint64(0x00000004) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("uid_b")
	}
	if hasFields[0]&uint64(0x00000008) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("v_a")
	}
	if hasFields[0]&uint64(0x00000010) == 0 {
		return github_com_gogo_protobuf_proto1.NewRequiredNotSetError("v_b")
	}

	if iNdEx > l {
		return io1.ErrUnexpectedEOF
	}
	return nil
}
func skipChanneldatinggame(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowChanneldatinggame
			}
			if iNdEx >= l {
				return 0, io1.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
			return iNdEx, nil
		case 1:
			iNdEx += 8
			return iNdEx, nil
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowChanneldatinggame
				}
				if iNdEx >= l {
					return 0, io1.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			iNdEx += length
			if length < 0 {
				return 0, ErrInvalidLengthChanneldatinggame
			}
			return iNdEx, nil
		case 3:
			for {
				var innerWire uint64
				var start = iNdEx
				for shift := uint(0); ; shift += 7 {
					if shift >= 64 {
						return 0, ErrIntOverflowChanneldatinggame
					}
					if iNdEx >= l {
						return 0, io1.ErrUnexpectedEOF
					}
					b := dAtA[iNdEx]
					iNdEx++
					innerWire |= (uint64(b) & 0x7F) << shift
					if b < 0x80 {
						break
					}
				}
				innerWireType := int(innerWire & 0x7)
				if innerWireType == 4 {
					break
				}
				next, err := skipChanneldatinggame(dAtA[start:])
				if err != nil {
					return 0, err
				}
				iNdEx = start + next
			}
			return iNdEx, nil
		case 4:
			return iNdEx, nil
		case 5:
			iNdEx += 4
			return iNdEx, nil
		default:
			return 0, fmt2.Errorf("proto: illegal wireType %d", wireType)
		}
	}
	panic("unreachable")
}

var (
	ErrInvalidLengthChanneldatinggame = fmt2.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowChanneldatinggame   = fmt2.Errorf("proto: integer overflow")
)

func init() {
	proto.RegisterFile("src/channelDatingGame/channeldatinggame.proto", fileDescriptorChanneldatinggame)
}

var fileDescriptorChanneldatinggame = []byte{
	// 1828 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x59, 0x5b, 0x6f, 0xdb, 0xc8,
	0x15, 0x5e, 0x4a, 0xb6, 0x6c, 0x1f, 0x49, 0x6b, 0x79, 0x7c, 0x89, 0xc2, 0xa4, 0xb6, 0x42, 0x67,
	0x77, 0x9d, 0x0d, 0x64, 0x03, 0xc6, 0x6e, 0x2f, 0x86, 0x57, 0xa8, 0xe5, 0xdd, 0x2a, 0x46, 0x63,
	0x64, 0x61, 0xaf, 0x8d, 0x5e, 0x50, 0x10, 0x14, 0x35, 0xb6, 0x58, 0x93, 0xd4, 0x84, 0x43, 0xa9,
	0xf6, 0x22, 0x40, 0x8b, 0x3e, 0x15, 0x05, 0xba, 0x48, 0x53, 0xb4, 0x0f, 0x2d, 0xf2, 0x14, 0x3f,
	0x14, 0xfd, 0x17, 0x7d, 0xcb, 0x63, 0x7f, 0x41, 0x50, 0xa4, 0x2f, 0xfe, 0x19, 0xc5, 0x0c, 0x29,
	0xf1, 0x36, 0x94, 0x19, 0xb7, 0xfb, 0x14, 0xeb, 0x70, 0xe6, 0x9c, 0x8f, 0xe7, 0x7c, 0xe7, 0xcc,
	0x37, 0x0c, 0xd4, 0xa9, 0xa3, 0x6f, 0xe8, 0x5d, 0xcd, 0xb6, 0xb1, 0xf9, 0xb9, 0xe6, 0x1a, 0xf6,
	0x69, 0x4b, 0xb3, 0xf0, 0xd0, 0xd2, 0xe1, 0x96, 0x53, 0xcd, 0xc2, 0xeb, 0xc4, 0xe9, 0xb9, 0x3d,
	0x34, 0x97, 0x78, 0x20, 0xdf, 0xd7, 0x7b, 0x96, 0xd5, 0xb3, 0x37, 0x5c, 0x73, 0x40, 0x0c, 0xfd,
	0xcc, 0xc4, 0x1b, 0xf4, 0xac, 0xdd, 0x37, 0x4c, 0xd7, 0xb0, 0xdd, 0x0b, 0xe2, 0x6f, 0x54, 0x54,
	0x98, 0x7a, 0xa4, 0xb9, 0x47, 0x14, 0x3b, 0x68, 0x09, 0xf2, 0x7d, 0xa3, 0x53, 0x95, 0x6a, 0xd2,
	0x5a, 0xb9, 0x39, 0xf1, 0xfa, 0xcd, 0xca, 0x7b, 0x07, 0xcc, 0x80, 0xb6, 0x61, 0xaa, 0xab, 0xb9,
	0xaa, 0x7e, 0x72, 0x5a, 0xcd, 0xd5, 0xa4, 0xb5, 0xe2, 0xe6, 0xea, 0x7a, 0x12, 0x46, 0x80, 0xf1,
	0x91, 0xe6, 0xee, 0x9e, 0x9c, 0x1e, 0x14, 0xba, 0xfc, 0x5f, 0xa5, 0x01, 0xb7, 0x5a, 0xd8, 0x0d,
	0x1e, 0xef, 0xf6, 0x9d, 0x3d, 0xfb, 0xa4, 0x77, 0x80, 0x9f, 0xa2, 0x55, 0x00, 0xdf, 0x91, 0xca,
	0xe3, 0xe6, 0x46, 0x71, 0x67, 0x7c, 0xfb, 0x5e, 0x47, 0x79, 0x93, 0x83, 0xaa, 0xd8, 0x01, 0x25,
	0x48, 0x86, 0x49, 0xd2, 0xd5, 0x28, 0x8e, 0x6c, 0xf6, 0x4c, 0xe8, 0x3b, 0x30, 0x35, 0x30, 0x88,
	0xca, 0x5e, 0x29, 0x17, 0x7a, 0x5a, 0x18, 0x18, 0xe4, 0xc8, 0xe8, 0xa0, 0x06, 0x94, 0xd9, 0x5b,
	0xf5, 0x29, 0x76, 0x54, 0xd3, 0xa0, 0x6e, 0x35, 0x5f, 0xcb, 0xaf, 0x15, 0x37, 0x65, 0xc1, 0xbb,
	0xf9, 0x09, 0x3a, 0x28, 0x76, 0xbd, 0x3f, 0x1e, 0x1b, 0xd4, 0x45, 0x7b, 0xf0, 0xbe, 0x69, 0x9c,
	0x61, 0xb5, 0x8d, 0x35, 0xd7, 0x73, 0x30, 0xc1, 0x1d, 0x88, 0x92, 0xe3, 0x6d, 0x3a, 0xc3, 0x4d,
	0xac, 0xb9, 0x1c, 0x7b, 0xc9, 0xf4, 0x7f, 0x71, 0x57, 0x07, 0x30, 0xdf, 0x23, 0xd8, 0x56, 0xb9,
	0xbf, 0x00, 0xd0, 0x64, 0xaa, 0xbf, 0x27, 0x04, 0xdb, 0xcc, 0x1f, 0xf3, 0xcb, 0xfd, 0x55, 0x7a,
	0x21, 0x0b, 0xf7, 0xb9, 0x06, 0x65, 0x8d, 0x10, 0xf3, 0x42, 0xb5, 0x0c, 0x5d, 0x35, 0xb1, 0x5d,
	0x2d, 0x84, 0xca, 0x5a, 0xe4, 0x8f, 0xf6, 0x0d, 0xfd, 0x31, 0xb6, 0x95, 0x9f, 0xc2, 0x12, 0xf3,
	0x17, 0x24, 0xf8, 0x0b, 0xdb, 0x75, 0x2e, 0xb2, 0xd6, 0x87, 0x95, 0xc0, 0xc4, 0x03, 0x6c, 0x72,
	0x6e, 0x8c, 0x4a, 0xc0, 0x4d, 0xca, 0x6d, 0xb8, 0x25, 0x74, 0x4d, 0x09, 0xa3, 0xc5, 0xae, 0xd9,
	0xa3, 0xf8, 0x86, 0x61, 0x15, 0x19, 0xaa, 0xe2, 0xfd, 0xbe, 0xef, 0x2e, 0xd6, 0xcf, 0x6e, 0xea,
	0xfb, 0x08, 0xaa, 0xe2, 0xfd, 0x94, 0x30, 0x56, 0x19, 0x54, 0x65, 0xe9, 0xe6, 0xbb, 0xa7, 0x87,
	0xac, 0x32, 0x28, 0x7b, 0xd3, 0xb1, 0xd9, 0x78, 0x06, 0xb3, 0x87, 0xd8, 0x65, 0xee, 0xbe, 0x64,
	0x04, 0xcd, 0x9c, 0xe1, 0x8f, 0xa0, 0xe4, 0x6a, 0xce, 0x29, 0x76, 0x55, 0x8f, 0xeb, 0x61, 0x36,
	0x17, 0xbd, 0x27, 0xdc, 0x21, 0xba, 0x03, 0x85, 0x9e, 0x47, 0xf8, 0x7c, 0x38, 0x7a, 0x8f, 0xf1,
	0x5d, 0xf9, 0x1e, 0x54, 0xa2, 0xd1, 0x29, 0x61, 0xe1, 0x4f, 0x9c, 0x9e, 0xa5, 0x26, 0x7b, 0x68,
	0x86, 0xd9, 0xf9, 0x42, 0xe5, 0xbb, 0x30, 0xdb, 0xba, 0x01, 0x6c, 0x16, 0xb0, 0x25, 0x08, 0xa8,
	0xf7, 0x1d, 0x47, 0x14, 0x90, 0xd9, 0xbd, 0x80, 0x3f, 0x87, 0x92, 0x97, 0xf9, 0x7d, 0x6c, 0xb5,
	0xc3, 0x73, 0x29, 0x17, 0x9d, 0x4b, 0x77, 0xa0, 0xc0, 0xc8, 0x1d, 0xeb, 0xef, 0x49, 0xcb, 0xd0,
	0xf7, 0x3a, 0x6c, 0x13, 0xc5, 0xe7, 0xd5, 0x7c, 0x78, 0x13, 0xc5, 0xe7, 0xca, 0x33, 0x98, 0xdf,
	0xb3, 0x0d, 0x37, 0x1c, 0x20, 0x73, 0x21, 0x7e, 0x08, 0x45, 0x8b, 0xef, 0xf0, 0xfa, 0x33, 0xc7,
	0xfb, 0x73, 0x25, 0x75, 0x18, 0xfa, 0xde, 0xc1, 0xdb, 0xc3, 0xba, 0x52, 0x59, 0x82, 0x85, 0x64,
	0x74, 0x4a, 0x94, 0x9f, 0xc0, 0x52, 0x0b, 0xbb, 0xe1, 0x31, 0x71, 0xac, 0x99, 0x34, 0x33, 0xb0,
	0xdb, 0x30, 0xdd, 0x37, 0x3a, 0x01, 0xaa, 0xf2, 0xc1, 0x54, 0xdf, 0xe8, 0xf0, 0x88, 0x16, 0x1f,
	0xbf, 0x49, 0xcf, 0x94, 0xb0, 0xb1, 0x13, 0x4c, 0x30, 0xc3, 0x3e, 0xe9, 0x85, 0x5f, 0x2b, 0xd3,
	0x18, 0xab, 0x98, 0xa1, 0x5f, 0x3c, 0xdc, 0x0e, 0xc8, 0x7e, 0xb8, 0x03, 0xcd, 0x3e, 0xbb, 0xc9,
	0xcb, 0x28, 0x4f, 0xe1, 0x4e, 0xaa, 0x8b, 0x6f, 0x09, 0xf5, 0xaf, 0xa1, 0x12, 0x5f, 0x95, 0xca,
	0xba, 0x35, 0x28, 0x07, 0xf1, 0x07, 0x9a, 0x19, 0x6d, 0x47, 0x33, 0x80, 0x8b, 0x1e, 0x40, 0x99,
	0x62, 0x13, 0xeb, 0xae, 0x4a, 0x5d, 0xcd, 0xed, 0x53, 0xde, 0x95, 0xc3, 0x81, 0x51, 0xf2, 0x1e,
	0x1d, 0xf2, 0x27, 0xca, 0x6f, 0x25, 0x58, 0x3c, 0x8c, 0x96, 0xe9, 0x49, 0xfb, 0x97, 0x99, 0xeb,
	0xbf, 0x0a, 0xe0, 0x47, 0x8a, 0x9f, 0x76, 0x33, 0x9e, 0x9d, 0x1d, 0x78, 0x2b, 0x30, 0xed, 0x1d,
	0x30, 0x7c, 0x3e, 0x04, 0x4b, 0xa6, 0x98, 0x95, 0x4d, 0x88, 0x2a, 0x2c, 0x89, 0x30, 0x50, 0xa2,
	0xfc, 0x02, 0x16, 0x5b, 0x37, 0x47, 0xb7, 0x02, 0xd3, 0xfc, 0x78, 0x8b, 0x63, 0x9b, 0x62, 0x56,
	0x16, 0xf8, 0x07, 0x09, 0xf6, 0xfb, 0x81, 0x23, 0x98, 0x25, 0x11, 0xe6, 0x9f, 0x71, 0xb2, 0x1c,
	0xf2, 0x97, 0x0c, 0x6d, 0xe6, 0xec, 0xc9, 0x8a, 0xcf, 0xaf, 0x74, 0x3e, 0x56, 0x69, 0xe5, 0x47,
	0x70, 0x37, 0xdd, 0x37, 0x25, 0xe8, 0x43, 0x98, 0x0d, 0xb2, 0xee, 0xb1, 0x50, 0xe2, 0xcd, 0x57,
	0x1e, 0x25, 0x9d, 0xb3, 0xeb, 0x95, 0x04, 0x73, 0xfb, 0x9a, 0xab, 0x77, 0x23, 0xfc, 0xba, 0x0d,
	0x93, 0x6c, 0x9b, 0x16, 0x41, 0x35, 0xd1, 0x37, 0x3a, 0x3b, 0xc3, 0x47, 0xed, 0x48, 0xb6, 0xd8,
	0xa3, 0x26, 0x7a, 0x08, 0xb3, 0x11, 0xf6, 0xa9, 0x5a, 0x04, 0x77, 0x29, 0xc4, 0xbf, 0x9d, 0xe4,
	0xe2, 0x76, 0x75, 0x22, 0x65, 0x71, 0x53, 0x39, 0xe2, 0x45, 0x78, 0x12, 0xd3, 0x11, 0xef, 0x9a,
	0xc4, 0x5c, 0x3c, 0x89, 0x1d, 0x3e, 0x7f, 0x92, 0x6e, 0x29, 0x61, 0x0a, 0x8a, 0xf3, 0x22, 0x68,
	0x62, 0x29, 0xbb, 0xe2, 0x29, 0xb1, 0xad, 0xa3, 0x06, 0xfe, 0x0a, 0x2a, 0xf1, 0x15, 0x11, 0xda,
	0x49, 0x02, 0xda, 0x45, 0xc8, 0x95, 0x13, 0x91, 0xeb, 0x29, 0xcc, 0x32, 0x6f, 0x3b, 0xbe, 0x58,
	0xfa, 0x5f, 0x73, 0x81, 0xee, 0xc1, 0x8c, 0x41, 0x55, 0x5d, 0xb3, 0x75, 0x6c, 0x46, 0x86, 0xc1,
	0xb4, 0x41, 0x77, 0xb9, 0x55, 0x41, 0xde, 0x24, 0x0a, 0x42, 0x52, 0xe2, 0x57, 0x66, 0x68, 0xfa,
	0xbf, 0x55, 0xe6, 0x13, 0x5e, 0x99, 0xa4, 0x5b, 0x4a, 0xc6, 0x9d, 0x27, 0xff, 0x94, 0xa0, 0x12,
	0xd7, 0xfa, 0xec, 0x24, 0x66, 0x5a, 0x3a, 0x86, 0x61, 0xb2, 0xab, 0xb9, 0x7e, 0x7c, 0xc7, 0x1b,
	0x93, 0x33, 0xa3, 0xf8, 0x8e, 0xc9, 0xec, 0x56, 0xe7, 0x53, 0x4e, 0xdf, 0x91, 0xdd, 0xea, 0x7c,
	0x8a, 0x3e, 0x80, 0xa2, 0xdb, 0xc6, 0x1a, 0x93, 0xc3, 0x96, 0xe1, 0x46, 0x18, 0x0b, 0xfc, 0xc1,
	0x63, 0x66, 0xf7, 0x85, 0x98, 0xa5, 0x99, 0xb8, 0x3a, 0x19, 0x15, 0x62, 0xfb, 0x9a, 0x89, 0x03,
	0x21, 0x56, 0x08, 0x23, 0xf2, 0x84, 0x98, 0xa7, 0x4c, 0x8e, 0x0d, 0xe2, 0xbf, 0x77, 0xe6, 0xa3,
	0x69, 0x13, 0xe6, 0x62, 0x1b, 0x3d, 0x45, 0x38, 0xbc, 0x67, 0x48, 0xc9, 0x7b, 0x86, 0x72, 0x08,
	0x0b, 0xbb, 0x3d, 0xfb, 0xc4, 0x70, 0xac, 0x63, 0x83, 0x3c, 0xea, 0x99, 0x1d, 0x9f, 0x49, 0x69,
	0xe7, 0x4b, 0x14, 0x48, 0x4e, 0x0c, 0xe4, 0x33, 0x58, 0x14, 0x38, 0xa5, 0x04, 0xdd, 0x87, 0x22,
	0x71, 0xb0, 0x2a, 0x02, 0x34, 0x43, 0x1c, 0x7c, 0xec, 0x61, 0xfa, 0x46, 0x82, 0xca, 0x57, 0x98,
	0xba, 0x9f, 0x3b, 0xda, 0xaf, 0xf6, 0x2c, 0xed, 0x14, 0xfb, 0x80, 0xf4, 0x38, 0x20, 0xdd, 0xe8,
	0x04, 0x83, 0x2a, 0x97, 0x3e, 0xa8, 0xf2, 0x89, 0x41, 0xb5, 0x08, 0xf9, 0x81, 0xaa, 0x45, 0xaa,
	0x97, 0x1b, 0xec, 0x78, 0xe6, 0x36, 0xaf, 0x58, 0x60, 0x6e, 0x6e, 0xfe, 0x7d, 0x11, 0xe6, 0x12,
	0x97, 0x5d, 0xf4, 0x37, 0x09, 0xe6, 0x05, 0xf7, 0x07, 0xf4, 0x20, 0x65, 0x40, 0x24, 0xf5, 0xbe,
	0xfc, 0x71, 0xd6, 0xa5, 0x94, 0x28, 0xf5, 0xdf, 0x5c, 0x5e, 0xe5, 0xa5, 0xdf, 0x5f, 0x5e, 0xe5,
	0x27, 0xce, 0xb7, 0xf0, 0xd6, 0x8b, 0xcb, 0xab, 0xbc, 0x5c, 0x3f, 0xaf, 0x6d, 0x07, 0xe5, 0x68,
	0xd4, 0xea, 0xb8, 0xb6, 0xcd, 0x49, 0xd4, 0x40, 0x2f, 0x24, 0x58, 0x10, 0x5d, 0x13, 0x90, 0x28,
	0x66, 0xca, 0x7d, 0x44, 0x7e, 0x98, 0x79, 0x2d, 0x25, 0xca, 0x3d, 0x06, 0x30, 0xc7, 0x00, 0xe6,
	0xce, 0x39, 0xbc, 0x4a, 0x1c, 0x1e, 0xfa, 0x83, 0x04, 0xa5, 0xb0, 0xcc, 0x47, 0x8a, 0x20, 0x40,
	0xec, 0x16, 0x22, 0xaf, 0x5e, 0xbb, 0x86, 0x12, 0xe5, 0x13, 0x16, 0x3c, 0xef, 0x67, 0xc7, 0xe5,
	0xe1, 0xef, 0x25, 0xb2, 0xe3, 0xd6, 0xb6, 0xbd, 0x0b, 0x49, 0x8d, 0x2b, 0xfc, 0x06, 0xfa, 0x1a,
	0x4a, 0xad, 0xeb, 0xe0, 0xb4, 0x32, 0xc0, 0x89, 0xdf, 0x24, 0xbc, 0x5c, 0x4c, 0x8c, 0xcd, 0xc5,
	0x4b, 0x09, 0xe6, 0x05, 0xda, 0x57, 0x48, 0x1f, 0xb1, 0xfa, 0x16, 0xd2, 0x27, 0x45, 0x4e, 0x2b,
	0x1b, 0x0c, 0xd1, 0xa4, 0x9f, 0xa0, 0x3e, 0xc7, 0x74, 0x37, 0x91, 0xa0, 0xfe, 0xf6, 0x70, 0xb0,
	0x36, 0xd0, 0x5f, 0xa5, 0x91, 0x36, 0x8f, 0x2b, 0x5d, 0x54, 0x4f, 0x0f, 0x2c, 0x10, 0xd6, 0xf2,
	0xfa, 0xbb, 0x2c, 0xa7, 0x44, 0xa9, 0x31, 0xac, 0x85, 0x50, 0xf6, 0x66, 0x63, 0x48, 0xd1, 0x2b,
	0x09, 0x50, 0x52, 0x0d, 0xa2, 0x35, 0x31, 0x55, 0x92, 0xd2, 0x50, 0x7e, 0x90, 0x71, 0x25, 0x25,
	0xca, 0xf7, 0x19, 0x9a, 0x29, 0x86, 0xa6, 0x70, 0xbe, 0xa5, 0x6d, 0xb5, 0x39, 0xa2, 0xd5, 0x44,
	0xee, 0x34, 0x9e, 0x3b, 0xad, 0x51, 0xab, 0xb7, 0xf9, 0x5f, 0xed, 0x06, 0xfa, 0xb3, 0x04, 0xa8,
	0x95, 0x0d, 0x65, 0x2b, 0x33, 0x4a, 0xb1, 0x16, 0x55, 0x3e, 0x66, 0x28, 0xa7, 0x23, 0xf5, 0xbd,
	0x25, 0xae, 0x6f, 0x03, 0xfd, 0x49, 0x82, 0x52, 0xf8, 0x1c, 0x17, 0xf2, 0x3e, 0xa6, 0x2d, 0xe4,
	0xd5, 0x6b, 0xd7, 0x50, 0xa2, 0x6c, 0x31, 0x14, 0x33, 0x7e, 0xae, 0xfa, 0x5b, 0x36, 0xc7, 0xf1,
	0x41, 0x0a, 0x8e, 0x5a, 0xdd, 0xde, 0x1e, 0x29, 0x8e, 0x06, 0xfa, 0xc6, 0x6b, 0x88, 0xf8, 0x91,
	0x9f, 0xd6, 0x10, 0x02, 0xc5, 0x91, 0xd6, 0x10, 0x22, 0x15, 0xe1, 0x91, 0x0c, 0xc6, 0x91, 0xec,
	0xb9, 0x04, 0x95, 0xf8, 0x7d, 0x18, 0x7d, 0x28, 0x08, 0x21, 0xb8, 0xb2, 0xcb, 0x1f, 0x65, 0x5a,
	0x47, 0x89, 0xf2, 0x90, 0xe1, 0x28, 0x46, 0x0a, 0x57, 0x4d, 0x26, 0xac, 0xe6, 0x55, 0xee, 0x19,
	0x94, 0x23, 0x47, 0x3c, 0x4a, 0x99, 0x46, 0x11, 0xf5, 0x20, 0xdf, 0xbf, 0x7e, 0xd1, 0x30, 0x21,
	0xa5, 0x71, 0x09, 0x79, 0x29, 0xf1, 0x8f, 0x9d, 0xc2, 0x3b, 0x07, 0x4a, 0x69, 0xf2, 0xb4, 0xcb,
	0x8f, 0xbc, 0xf1, 0x4e, 0xeb, 0x87, 0xf8, 0xca, 0xe3, 0xf0, 0xbd, 0x90, 0x60, 0x2e, 0x21, 0x3c,
	0x90, 0xa8, 0x12, 0x22, 0xcd, 0x23, 0xaf, 0x65, 0x5b, 0x38, 0x6c, 0xb6, 0xf7, 0xb3, 0x35, 0xdb,
	0x1f, 0x25, 0x58, 0x10, 0x7d, 0x21, 0x46, 0x29, 0x64, 0x15, 0x7d, 0x8b, 0x16, 0x1e, 0xc4, 0x69,
	0x9f, 0x9d, 0xbd, 0x44, 0xcd, 0x8e, 0x4b, 0x94, 0xdf, 0x6a, 0xf1, 0x7b, 0x4f, 0x5a, 0xab, 0x09,
	0xae, 0x5d, 0x69, 0xad, 0x26, 0xba, 0x4a, 0x79, 0x80, 0x2a, 0xe3, 0x00, 0xb1, 0x24, 0x89, 0x3e,
	0x98, 0x8a, 0xd5, 0x8a, 0xf8, 0xcb, 0xac, 0x58, 0xad, 0xa4, 0x7d, 0x85, 0xe5, 0x98, 0xe6, 0xc6,
	0x61, 0xfa, 0x87, 0x04, 0xe5, 0x88, 0x0c, 0x15, 0x36, 0x5b, 0x5c, 0xa8, 0xca, 0x77, 0xd7, 0x47,
	0xff, 0xa5, 0xb1, 0x7e, 0xf8, 0xe3, 0xa6, 0xf7, 0x5f, 0x1a, 0x5f, 0x58, 0xc4, 0xbd, 0x50, 0xbf,
	0x6c, 0x2a, 0xc7, 0x2c, 0x2c, 0x66, 0x61, 0xc1, 0xe0, 0x87, 0x89, 0xe5, 0x0f, 0xc9, 0xcf, 0xea,
	0x46, 0xfc, 0x40, 0xe1, 0x3d, 0xcf, 0x0f, 0x14, 0xfe, 0x57, 0xbb, 0x51, 0xab, 0x5b, 0xb5, 0xed,
	0x81, 0x66, 0xf6, 0x71, 0x8d, 0x99, 0xed, 0xe1, 0x8f, 0x76, 0x43, 0x2e, 0xfc, 0xee, 0xf2, 0x2a,
	0xff, 0x97, 0xaf, 0x9b, 0x95, 0xd7, 0x6f, 0x97, 0xa5, 0x7f, 0xbd, 0x5d, 0x96, 0xfe, 0xfd, 0x76,
	0x59, 0x7a, 0xfe, 0x9f, 0xe5, 0xf7, 0xfe, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xad, 0x0d, 0x47, 0x2f,
	0xab, 0x19, 0x00, 0x00,
}
