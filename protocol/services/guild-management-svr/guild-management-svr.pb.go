// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/guild-management-svr/guild-management-svr.proto

package guild_management_svr // import "golang.52tt.com/protocol/services/guild-management-svr"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 公会经营后台语音直播经营分析菜单栏 移位定义 1,2,4,8...
type GuildLiveDataMenuType int32

const (
	GuildLiveDataMenuType_GuildLiveDataInvalid       GuildLiveDataMenuType = 0
	GuildLiveDataMenuType_GuildLiveStatsTotalData    GuildLiveDataMenuType = 1
	GuildLiveDataMenuType_GuildLiveTaskData          GuildLiveDataMenuType = 2
	GuildLiveDataMenuType_GuildAnchorData            GuildLiveDataMenuType = 4
	GuildLiveDataMenuType_AnchorPractitionerAnalysis GuildLiveDataMenuType = 8
	GuildLiveDataMenuType_GuildBusinessDiagnosis     GuildLiveDataMenuType = 16
	GuildLiveDataMenuType_AnchorFaceCheckDetail      GuildLiveDataMenuType = 32
)

var GuildLiveDataMenuType_name = map[int32]string{
	0:  "GuildLiveDataInvalid",
	1:  "GuildLiveStatsTotalData",
	2:  "GuildLiveTaskData",
	4:  "GuildAnchorData",
	8:  "AnchorPractitionerAnalysis",
	16: "GuildBusinessDiagnosis",
	32: "AnchorFaceCheckDetail",
}
var GuildLiveDataMenuType_value = map[string]int32{
	"GuildLiveDataInvalid":       0,
	"GuildLiveStatsTotalData":    1,
	"GuildLiveTaskData":          2,
	"GuildAnchorData":            4,
	"AnchorPractitionerAnalysis": 8,
	"GuildBusinessDiagnosis":     16,
	"AnchorFaceCheckDetail":      32,
}

func (x GuildLiveDataMenuType) String() string {
	return proto.EnumName(GuildLiveDataMenuType_name, int32(x))
}
func (GuildLiveDataMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{0}
}

// 公会经营后台经纪人菜单栏 移位定义 1,2,4,8...
type GuildAgentMenuType int32

const (
	GuildAgentMenuType_GuildAgentInvalid GuildAgentMenuType = 0
	GuildAgentMenuType_GuildAgentManage  GuildAgentMenuType = 1
)

var GuildAgentMenuType_name = map[int32]string{
	0: "GuildAgentInvalid",
	1: "GuildAgentManage",
}
var GuildAgentMenuType_value = map[string]int32{
	"GuildAgentInvalid": 0,
	"GuildAgentManage":  1,
}

func (x GuildAgentMenuType) String() string {
	return proto.EnumName(GuildAgentMenuType_name, int32(x))
}
func (GuildAgentMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{1}
}

// 公会经营后台收益管理菜单栏 移位定义 1,2,4,8...
type GuildIncomeMenuType int32

const (
	GuildIncomeMenuType_GuildIncomeInvalid GuildIncomeMenuType = 0
	GuildIncomeMenuType_GuildIncomeManage  GuildIncomeMenuType = 1
)

var GuildIncomeMenuType_name = map[int32]string{
	0: "GuildIncomeInvalid",
	1: "GuildIncomeManage",
}
var GuildIncomeMenuType_value = map[string]int32{
	"GuildIncomeInvalid": 0,
	"GuildIncomeManage":  1,
}

func (x GuildIncomeMenuType) String() string {
	return proto.EnumName(GuildIncomeMenuType_name, int32(x))
}
func (GuildIncomeMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{2}
}

// 公会经营后台多人互动经营管理菜单栏 移位定义 1,2,4,8...
type GuildMultiPlayDataType int32

const (
	GuildMultiPlayDataType_GuildMultiPlayDataInvalid     GuildMultiPlayDataType = 0
	GuildMultiPlayDataType_MultiPlayOperationData        GuildMultiPlayDataType = 1
	GuildMultiPlayDataType_MultiPlayDetail               GuildMultiPlayDataType = 2
	GuildMultiPlayDataType_MultiPlayPractitionerAnalysis GuildMultiPlayDataType = 4
	GuildMultiPlayDataType_MultiPlayHallTask             GuildMultiPlayDataType = 8
	GuildMultiPlayDataType_MultiPlayScheduleData         GuildMultiPlayDataType = 16
	GuildMultiPlayDataType_MultiAnchorFaceCheckDetail    GuildMultiPlayDataType = 32
	GuildMultiPlayDataType_MultiPlayWeddingReserved      GuildMultiPlayDataType = 64
	GuildMultiPlayDataType_MultiPlayBusinessDiag         GuildMultiPlayDataType = 128
)

var GuildMultiPlayDataType_name = map[int32]string{
	0:   "GuildMultiPlayDataInvalid",
	1:   "MultiPlayOperationData",
	2:   "MultiPlayDetail",
	4:   "MultiPlayPractitionerAnalysis",
	8:   "MultiPlayHallTask",
	16:  "MultiPlayScheduleData",
	32:  "MultiAnchorFaceCheckDetail",
	64:  "MultiPlayWeddingReserved",
	128: "MultiPlayBusinessDiag",
}
var GuildMultiPlayDataType_value = map[string]int32{
	"GuildMultiPlayDataInvalid":     0,
	"MultiPlayOperationData":        1,
	"MultiPlayDetail":               2,
	"MultiPlayPractitionerAnalysis": 4,
	"MultiPlayHallTask":             8,
	"MultiPlayScheduleData":         16,
	"MultiAnchorFaceCheckDetail":    32,
	"MultiPlayWeddingReserved":      64,
	"MultiPlayBusinessDiag":         128,
}

func (x GuildMultiPlayDataType) String() string {
	return proto.EnumName(GuildMultiPlayDataType_name, int32(x))
}
func (GuildMultiPlayDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{3}
}

// 流量卡管理菜单栏 移位定义 1,2,4,8...
type FlowCardMenuType int32

const (
	FlowCardMenuType_FlowCardInValid FlowCardMenuType = 0
	FlowCardMenuType_AnchorFlowCard  FlowCardMenuType = 1
)

var FlowCardMenuType_name = map[int32]string{
	0: "FlowCardInValid",
	1: "AnchorFlowCard",
}
var FlowCardMenuType_value = map[string]int32{
	"FlowCardInValid": 0,
	"AnchorFlowCard":  1,
}

func (x FlowCardMenuType) String() string {
	return proto.EnumName(FlowCardMenuType_name, int32(x))
}
func (FlowCardMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{4}
}

// 签约管理菜单栏 移位定义 1,2,4,8...
type AnchorContractMenuType int32

const (
	AnchorContractMenuType_AnchorContractInValid   AnchorContractMenuType = 0
	AnchorContractMenuType_AnchorContractApplySign AnchorContractMenuType = 1
	AnchorContractMenuType_AnchorContractCancel    AnchorContractMenuType = 2
	AnchorContractMenuType_AnchorContractList      AnchorContractMenuType = 4
	AnchorContractMenuType_AnchorContractVio       AnchorContractMenuType = 8
)

var AnchorContractMenuType_name = map[int32]string{
	0: "AnchorContractInValid",
	1: "AnchorContractApplySign",
	2: "AnchorContractCancel",
	4: "AnchorContractList",
	8: "AnchorContractVio",
}
var AnchorContractMenuType_value = map[string]int32{
	"AnchorContractInValid":   0,
	"AnchorContractApplySign": 1,
	"AnchorContractCancel":    2,
	"AnchorContractList":      4,
	"AnchorContractVio":       8,
}

func (x AnchorContractMenuType) String() string {
	return proto.EnumName(AnchorContractMenuType_name, int32(x))
}
func (AnchorContractMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{5}
}

// 消息管理 移位定义 1,2,4,8...
type GuildMsgMenuType int32

const (
	GuildMsgMenuType_GuildMsgInValid GuildMsgMenuType = 0
	GuildMsgMenuType_FuWuHaoMsg      GuildMsgMenuType = 1
)

var GuildMsgMenuType_name = map[int32]string{
	0: "GuildMsgInValid",
	1: "FuWuHaoMsg",
}
var GuildMsgMenuType_value = map[string]int32{
	"GuildMsgInValid": 0,
	"FuWuHaoMsg":      1,
}

func (x GuildMsgMenuType) String() string {
	return proto.EnumName(GuildMsgMenuType_name, int32(x))
}
func (GuildMsgMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{6}
}

// 公会管理 移位定义 1,2,4,8...
type GuildManageMenuType int32

const (
	GuildManageMenuType_GuildManageInValid GuildManageMenuType = 0
	GuildManageMenuType_PersonnelManage    GuildManageMenuType = 1
	GuildManageMenuType_LiveManage         GuildManageMenuType = 2
)

var GuildManageMenuType_name = map[int32]string{
	0: "GuildManageInValid",
	1: "PersonnelManage",
	2: "LiveManage",
}
var GuildManageMenuType_value = map[string]int32{
	"GuildManageInValid": 0,
	"PersonnelManage":    1,
	"LiveManage":         2,
}

func (x GuildManageMenuType) String() string {
	return proto.EnumName(GuildManageMenuType_name, int32(x))
}
func (GuildManageMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{7}
}

// 首页管理 移位定义 1,2,4,8...
type HomePageMenuType int32

const (
	HomePageMenuType_HomePageMenuTypeInValid HomePageMenuType = 0
	HomePageMenuType_HomePageMenuTypeMulti   HomePageMenuType = 1
)

var HomePageMenuType_name = map[int32]string{
	0: "HomePageMenuTypeInValid",
	1: "HomePageMenuTypeMulti",
}
var HomePageMenuType_value = map[string]int32{
	"HomePageMenuTypeInValid": 0,
	"HomePageMenuTypeMulti":   1,
}

func (x HomePageMenuType) String() string {
	return proto.EnumName(HomePageMenuType_name, int32(x))
}
func (HomePageMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{8}
}

// 语音直播经营分析预览数据类型  移位定义 1,2,4,8...
type GuildLiveStatsDataType int32

const (
	GuildLiveStatsDataType_LiveStatsDataInvalid GuildLiveStatsDataType = 0
	GuildLiveStatsDataType_AnchorIncome         GuildLiveStatsDataType = 1
	GuildLiveStatsDataType_ChannelFee           GuildLiveStatsDataType = 2
	GuildLiveStatsDataType_ChannelPkgFeeRatio   GuildLiveStatsDataType = 4
	GuildLiveStatsDataType_PureNewAnchorFee     GuildLiveStatsDataType = 8
)

var GuildLiveStatsDataType_name = map[int32]string{
	0: "LiveStatsDataInvalid",
	1: "AnchorIncome",
	2: "ChannelFee",
	4: "ChannelPkgFeeRatio",
	8: "PureNewAnchorFee",
}
var GuildLiveStatsDataType_value = map[string]int32{
	"LiveStatsDataInvalid": 0,
	"AnchorIncome":         1,
	"ChannelFee":           2,
	"ChannelPkgFeeRatio":   4,
	"PureNewAnchorFee":     8,
}

func (x GuildLiveStatsDataType) String() string {
	return proto.EnumName(GuildLiveStatsDataType_name, int32(x))
}
func (GuildLiveStatsDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{9}
}

// 公会直播任务模块数据类型 移位定义 1,2,4,8...
type GuildLiveTaskDataType int32

const (
	GuildLiveTaskDataType_LiveTaskDataInvalid     GuildLiveTaskDataType = 0
	GuildLiveTaskDataType_MonthChannelFee         GuildLiveTaskDataType = 1
	GuildLiveTaskDataType_MonthValidAnchorCnt     GuildLiveTaskDataType = 2
	GuildLiveTaskDataType_MonthAddValidAnchorCnt  GuildLiveTaskDataType = 4
	GuildLiveTaskDataType_MonthPotentialAnchorCnt GuildLiveTaskDataType = 8
)

var GuildLiveTaskDataType_name = map[int32]string{
	0: "LiveTaskDataInvalid",
	1: "MonthChannelFee",
	2: "MonthValidAnchorCnt",
	4: "MonthAddValidAnchorCnt",
	8: "MonthPotentialAnchorCnt",
}
var GuildLiveTaskDataType_value = map[string]int32{
	"LiveTaskDataInvalid":     0,
	"MonthChannelFee":         1,
	"MonthValidAnchorCnt":     2,
	"MonthAddValidAnchorCnt":  4,
	"MonthPotentialAnchorCnt": 8,
}

func (x GuildLiveTaskDataType) String() string {
	return proto.EnumName(GuildLiveTaskDataType_name, int32(x))
}
func (GuildLiveTaskDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{10}
}

// 主播数据数据类型 移位定义 1,2,4,8...
type AnchorDataType int32

const (
	AnchorDataType_AnchorDataInvalid        AnchorDataType = 0
	AnchorDataType_ChannelFeeData           AnchorDataType = 1
	AnchorDataType_AnchorIncomeData         AnchorDataType = 2
	AnchorDataType_SignTsData               AnchorDataType = 4
	AnchorDataType_SignExpireTsData         AnchorDataType = 8
	AnchorDataType_ChannelPkgFeeRatioData   AnchorDataType = 16
	AnchorDataType_AnchorPkgIncomeRatioData AnchorDataType = 32
)

var AnchorDataType_name = map[int32]string{
	0:  "AnchorDataInvalid",
	1:  "ChannelFeeData",
	2:  "AnchorIncomeData",
	4:  "SignTsData",
	8:  "SignExpireTsData",
	16: "ChannelPkgFeeRatioData",
	32: "AnchorPkgIncomeRatioData",
}
var AnchorDataType_value = map[string]int32{
	"AnchorDataInvalid":        0,
	"ChannelFeeData":           1,
	"AnchorIncomeData":         2,
	"SignTsData":               4,
	"SignExpireTsData":         8,
	"ChannelPkgFeeRatioData":   16,
	"AnchorPkgIncomeRatioData": 32,
}

func (x AnchorDataType) String() string {
	return proto.EnumName(AnchorDataType_name, int32(x))
}
func (AnchorDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{11}
}

// 语音直播从业者数据类型 移位定义 1,2,4,8...
type AnchorPracDataType int32

const (
	AnchorPracDataType_AnchorPracDataInvalid AnchorPracDataType = 0
	AnchorPracDataType_PracChannelFee        AnchorPracDataType = 1
	AnchorPracDataType_PracAnchorIncome      AnchorPracDataType = 2
)

var AnchorPracDataType_name = map[int32]string{
	0: "AnchorPracDataInvalid",
	1: "PracChannelFee",
	2: "PracAnchorIncome",
}
var AnchorPracDataType_value = map[string]int32{
	"AnchorPracDataInvalid": 0,
	"PracChannelFee":        1,
	"PracAnchorIncome":      2,
}

func (x AnchorPracDataType) String() string {
	return proto.EnumName(AnchorPracDataType_name, int32(x))
}
func (AnchorPracDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{12}
}

// 多人互动经营管理数据明细数据类型 移位定义 1,2,4,8...
type MultiPlayDetailDataType int32

const (
	MultiPlayDetailDataType_MultiPlayDetailDataInvalid  MultiPlayDetailDataType = 0
	MultiPlayDetailDataType_MultiPlayDetailSignTs       MultiPlayDetailDataType = 1
	MultiPlayDetailDataType_MultiPlayDetailSignExpireTs MultiPlayDetailDataType = 2
	MultiPlayDetailDataType_MultiPlayDetailGuildFee     MultiPlayDetailDataType = 4
	MultiPlayDetailDataType_MultiPlayDetailChGiftFee    MultiPlayDetailDataType = 8
	MultiPlayDetailDataType_MultiPlayDetailChWolfFee    MultiPlayDetailDataType = 16
	MultiPlayDetailDataType_MultiPlayDetailChannelFee   MultiPlayDetailDataType = 32
)

var MultiPlayDetailDataType_name = map[int32]string{
	0:  "MultiPlayDetailDataInvalid",
	1:  "MultiPlayDetailSignTs",
	2:  "MultiPlayDetailSignExpireTs",
	4:  "MultiPlayDetailGuildFee",
	8:  "MultiPlayDetailChGiftFee",
	16: "MultiPlayDetailChWolfFee",
	32: "MultiPlayDetailChannelFee",
}
var MultiPlayDetailDataType_value = map[string]int32{
	"MultiPlayDetailDataInvalid":  0,
	"MultiPlayDetailSignTs":       1,
	"MultiPlayDetailSignExpireTs": 2,
	"MultiPlayDetailGuildFee":     4,
	"MultiPlayDetailChGiftFee":    8,
	"MultiPlayDetailChWolfFee":    16,
	"MultiPlayDetailChannelFee":   32,
}

func (x MultiPlayDetailDataType) String() string {
	return proto.EnumName(MultiPlayDetailDataType_name, int32(x))
}
func (MultiPlayDetailDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{13}
}

// 多人互动经营管理从业者分析数据类型 移位定义 1,2,4,8...
type MultiPlayPracAnalysisDataType int32

const (
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisDataInvalid   MultiPlayPracAnalysisDataType = 0
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGiftFee       MultiPlayPracAnalysisDataType = 1
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisSignTs        MultiPlayPracAnalysisDataType = 2
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisChannelFee    MultiPlayPracAnalysisDataType = 4
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisActiveFee     MultiPlayPracAnalysisDataType = 8
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFee    MultiPlayPracAnalysisDataType = 16
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisQualityFeeRat MultiPlayPracAnalysisDataType = 32
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisPracGiftFee   MultiPlayPracAnalysisDataType = 64
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisPkgFee        MultiPlayPracAnalysisDataType = 128
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisTbeanGiftFee  MultiPlayPracAnalysisDataType = 256
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGuildFee      MultiPlayPracAnalysisDataType = 512
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisChannelPkgFee MultiPlayPracAnalysisDataType = 1024
	MultiPlayPracAnalysisDataType_MultiPlayPracAnalysisGuildPkgFee   MultiPlayPracAnalysisDataType = 2048
)

var MultiPlayPracAnalysisDataType_name = map[int32]string{
	0:    "MultiPlayPracAnalysisDataInvalid",
	1:    "MultiPlayPracAnalysisGiftFee",
	2:    "MultiPlayPracAnalysisSignTs",
	4:    "MultiPlayPracAnalysisChannelFee",
	8:    "MultiPlayPracAnalysisActiveFee",
	16:   "MultiPlayPracAnalysisQualityFee",
	32:   "MultiPlayPracAnalysisQualityFeeRat",
	64:   "MultiPlayPracAnalysisPracGiftFee",
	128:  "MultiPlayPracAnalysisPkgFee",
	256:  "MultiPlayPracAnalysisTbeanGiftFee",
	512:  "MultiPlayPracAnalysisGuildFee",
	1024: "MultiPlayPracAnalysisChannelPkgFee",
	2048: "MultiPlayPracAnalysisGuildPkgFee",
}
var MultiPlayPracAnalysisDataType_value = map[string]int32{
	"MultiPlayPracAnalysisDataInvalid":   0,
	"MultiPlayPracAnalysisGiftFee":       1,
	"MultiPlayPracAnalysisSignTs":        2,
	"MultiPlayPracAnalysisChannelFee":    4,
	"MultiPlayPracAnalysisActiveFee":     8,
	"MultiPlayPracAnalysisQualityFee":    16,
	"MultiPlayPracAnalysisQualityFeeRat": 32,
	"MultiPlayPracAnalysisPracGiftFee":   64,
	"MultiPlayPracAnalysisPkgFee":        128,
	"MultiPlayPracAnalysisTbeanGiftFee":  256,
	"MultiPlayPracAnalysisGuildFee":      512,
	"MultiPlayPracAnalysisChannelPkgFee": 1024,
	"MultiPlayPracAnalysisGuildPkgFee":   2048,
}

func (x MultiPlayPracAnalysisDataType) String() string {
	return proto.EnumName(MultiPlayPracAnalysisDataType_name, int32(x))
}
func (MultiPlayPracAnalysisDataType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{14}
}

// EsportMenuType 电竞经营管理菜单栏 移位定义 1,2,4,8...
type EsportMenuType int32

const (
	EsportMenuType_EsportMenuTypeValid               EsportMenuType = 0
	EsportMenuType_EsportMenuTypeAudit               EsportMenuType = 1
	EsportMenuType_EsportMenuTypePerformanceAnalysis EsportMenuType = 2
)

var EsportMenuType_name = map[int32]string{
	0: "EsportMenuTypeValid",
	1: "EsportMenuTypeAudit",
	2: "EsportMenuTypePerformanceAnalysis",
}
var EsportMenuType_value = map[string]int32{
	"EsportMenuTypeValid":               0,
	"EsportMenuTypeAudit":               1,
	"EsportMenuTypePerformanceAnalysis": 2,
}

func (x EsportMenuType) String() string {
	return proto.EnumName(EsportMenuType_name, int32(x))
}
func (EsportMenuType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{15}
}

// ****** 功能权限 ******//
// 签约申请功能权限 移位定义 1,2,4,8...
type ContractApplySignFuncType int32

const (
	ContractApplySignFuncType_ContractApplySignInvalid  ContractApplySignFuncType = 0
	ContractApplySignFuncType_ContractApplySignQuery    ContractApplySignFuncType = 1
	ContractApplySignFuncType_ContractApplySignApproval ContractApplySignFuncType = 2
)

var ContractApplySignFuncType_name = map[int32]string{
	0: "ContractApplySignInvalid",
	1: "ContractApplySignQuery",
	2: "ContractApplySignApproval",
}
var ContractApplySignFuncType_value = map[string]int32{
	"ContractApplySignInvalid":  0,
	"ContractApplySignQuery":    1,
	"ContractApplySignApproval": 2,
}

func (x ContractApplySignFuncType) String() string {
	return proto.EnumName(ContractApplySignFuncType_name, int32(x))
}
func (ContractApplySignFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{16}
}

// 解约申请功能权限 移位定义 1,2,4,8...
type ContractCancelFuncType int32

const (
	ContractCancelFuncType_ContractCancelInvalid  ContractCancelFuncType = 0
	ContractCancelFuncType_ContractCancelQuery    ContractCancelFuncType = 1
	ContractCancelFuncType_ContractCancelApproval ContractCancelFuncType = 2
)

var ContractCancelFuncType_name = map[int32]string{
	0: "ContractCancelInvalid",
	1: "ContractCancelQuery",
	2: "ContractCancelApproval",
}
var ContractCancelFuncType_value = map[string]int32{
	"ContractCancelInvalid":  0,
	"ContractCancelQuery":    1,
	"ContractCancelApproval": 2,
}

func (x ContractCancelFuncType) String() string {
	return proto.EnumName(ContractCancelFuncType_name, int32(x))
}
func (ContractCancelFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{17}
}

// 签约成员列表功能权限 移位定义 1,2,4,8...
type ContractListFuncType int32

const (
	ContractListFuncType_ContractListInvalid        ContractListFuncType = 0
	ContractListFuncType_ContractListQuery          ContractListFuncType = 1
	ContractListFuncType_ContractListCancelApproval ContractListFuncType = 2
	ContractListFuncType_ContractListRenewApproval  ContractListFuncType = 4
	ContractListFuncType_ContractListExport         ContractListFuncType = 8
)

var ContractListFuncType_name = map[int32]string{
	0: "ContractListInvalid",
	1: "ContractListQuery",
	2: "ContractListCancelApproval",
	4: "ContractListRenewApproval",
	8: "ContractListExport",
}
var ContractListFuncType_value = map[string]int32{
	"ContractListInvalid":        0,
	"ContractListQuery":          1,
	"ContractListCancelApproval": 2,
	"ContractListRenewApproval":  4,
	"ContractListExport":         8,
}

func (x ContractListFuncType) String() string {
	return proto.EnumName(ContractListFuncType_name, int32(x))
}
func (ContractListFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{18}
}

// 违规记录功能权限 移位定义 1,2,4,8...
type ContractVioFuncType int32

const (
	ContractVioFuncType_ContractVioInvalid ContractVioFuncType = 0
	ContractVioFuncType_ContractVioQuery   ContractVioFuncType = 1
	ContractVioFuncType_ContractVioExport  ContractVioFuncType = 2
)

var ContractVioFuncType_name = map[int32]string{
	0: "ContractVioInvalid",
	1: "ContractVioQuery",
	2: "ContractVioExport",
}
var ContractVioFuncType_value = map[string]int32{
	"ContractVioInvalid": 0,
	"ContractVioQuery":   1,
	"ContractVioExport":  2,
}

func (x ContractVioFuncType) String() string {
	return proto.EnumName(ContractVioFuncType_name, int32(x))
}
func (ContractVioFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{19}
}

// 语音直播经营情况总览功能权限 移位定义 1,2,4,8...
type LiveTotalDataFuncType int32

const (
	LiveTotalDataFuncType_LiveTotalDataInvalid LiveTotalDataFuncType = 0
	LiveTotalDataFuncType_LiveTotalDataQuery   LiveTotalDataFuncType = 1
	LiveTotalDataFuncType_LiveTotalDataExport  LiveTotalDataFuncType = 2
)

var LiveTotalDataFuncType_name = map[int32]string{
	0: "LiveTotalDataInvalid",
	1: "LiveTotalDataQuery",
	2: "LiveTotalDataExport",
}
var LiveTotalDataFuncType_value = map[string]int32{
	"LiveTotalDataInvalid": 0,
	"LiveTotalDataQuery":   1,
	"LiveTotalDataExport":  2,
}

func (x LiveTotalDataFuncType) String() string {
	return proto.EnumName(LiveTotalDataFuncType_name, int32(x))
}
func (LiveTotalDataFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{20}
}

// 语音直播公会任务功能权限 移位定义 1,2,4,8...
type LiveTaskDataFuncType int32

const (
	LiveTaskDataFuncType_LiveTaskDataFuncInvalid LiveTaskDataFuncType = 0
	LiveTaskDataFuncType_LiveTaskDataQuery       LiveTaskDataFuncType = 1
)

var LiveTaskDataFuncType_name = map[int32]string{
	0: "LiveTaskDataFuncInvalid",
	1: "LiveTaskDataQuery",
}
var LiveTaskDataFuncType_value = map[string]int32{
	"LiveTaskDataFuncInvalid": 0,
	"LiveTaskDataQuery":       1,
}

func (x LiveTaskDataFuncType) String() string {
	return proto.EnumName(LiveTaskDataFuncType_name, int32(x))
}
func (LiveTaskDataFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{21}
}

// 语音直播主播数据功能权限 移位定义 1,2,4,8...
type AnchorDataFuncType int32

const (
	AnchorDataFuncType_AnchorDataFuncInvalid AnchorDataFuncType = 0
	AnchorDataFuncType_AnchorDataQuery       AnchorDataFuncType = 1
	AnchorDataFuncType_AnchorDataExport      AnchorDataFuncType = 2
)

var AnchorDataFuncType_name = map[int32]string{
	0: "AnchorDataFuncInvalid",
	1: "AnchorDataQuery",
	2: "AnchorDataExport",
}
var AnchorDataFuncType_value = map[string]int32{
	"AnchorDataFuncInvalid": 0,
	"AnchorDataQuery":       1,
	"AnchorDataExport":      2,
}

func (x AnchorDataFuncType) String() string {
	return proto.EnumName(AnchorDataFuncType_name, int32(x))
}
func (AnchorDataFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{22}
}

// 语音直播从业者分析功能权限 移位定义 1,2,4,8...
type AnchorPracAnalysisFuncType int32

const (
	AnchorPracAnalysisFuncType_AnchorPracAnalysisInvalid AnchorPracAnalysisFuncType = 0
	AnchorPracAnalysisFuncType_AnchorPracAnalysisQuery   AnchorPracAnalysisFuncType = 1
	AnchorPracAnalysisFuncType_AnchorPracAnalysisExport  AnchorPracAnalysisFuncType = 2
)

var AnchorPracAnalysisFuncType_name = map[int32]string{
	0: "AnchorPracAnalysisInvalid",
	1: "AnchorPracAnalysisQuery",
	2: "AnchorPracAnalysisExport",
}
var AnchorPracAnalysisFuncType_value = map[string]int32{
	"AnchorPracAnalysisInvalid": 0,
	"AnchorPracAnalysisQuery":   1,
	"AnchorPracAnalysisExport":  2,
}

func (x AnchorPracAnalysisFuncType) String() string {
	return proto.EnumName(AnchorPracAnalysisFuncType_name, int32(x))
}
func (AnchorPracAnalysisFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{23}
}

// 主播流量卡功能权限 移位定义 1,2,4,8...
type FlowCardFuncType int32

const (
	FlowCardFuncType_FlowCardInvalid FlowCardFuncType = 0
	FlowCardFuncType_FlowCardQuery   FlowCardFuncType = 1
	FlowCardFuncType_FlowCardUse     FlowCardFuncType = 2
	FlowCardFuncType_FlowCardGrant   FlowCardFuncType = 4
)

var FlowCardFuncType_name = map[int32]string{
	0: "FlowCardInvalid",
	1: "FlowCardQuery",
	2: "FlowCardUse",
	4: "FlowCardGrant",
}
var FlowCardFuncType_value = map[string]int32{
	"FlowCardInvalid": 0,
	"FlowCardQuery":   1,
	"FlowCardUse":     2,
	"FlowCardGrant":   4,
}

func (x FlowCardFuncType) String() string {
	return proto.EnumName(FlowCardFuncType_name, int32(x))
}
func (FlowCardFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{24}
}

// 多人互动经营管理数据明细功能权限 移位定义 1,2,4,8...
type MultiPlayDetailFuncType int32

const (
	MultiPlayDetailFuncType_MultiPlayDetailInvalid MultiPlayDetailFuncType = 0
	MultiPlayDetailFuncType_MultiPlayDetailQuery   MultiPlayDetailFuncType = 1
	MultiPlayDetailFuncType_MultiPlayDetailExport  MultiPlayDetailFuncType = 2
)

var MultiPlayDetailFuncType_name = map[int32]string{
	0: "MultiPlayDetailInvalid",
	1: "MultiPlayDetailQuery",
	2: "MultiPlayDetailExport",
}
var MultiPlayDetailFuncType_value = map[string]int32{
	"MultiPlayDetailInvalid": 0,
	"MultiPlayDetailQuery":   1,
	"MultiPlayDetailExport":  2,
}

func (x MultiPlayDetailFuncType) String() string {
	return proto.EnumName(MultiPlayDetailFuncType_name, int32(x))
}
func (MultiPlayDetailFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{25}
}

// 多人互动经营从业者分析功能权限 移位定义 1,2,4,8...
type MultiPlayPracAnalysisFuncType int32

const (
	MultiPlayPracAnalysisFuncType_MultiPlayPracAnalysisInvalid MultiPlayPracAnalysisFuncType = 0
	MultiPlayPracAnalysisFuncType_MultiPlayPracAnalysisQuery   MultiPlayPracAnalysisFuncType = 1
	MultiPlayPracAnalysisFuncType_MultiPlayPracAnalysisExport  MultiPlayPracAnalysisFuncType = 2
)

var MultiPlayPracAnalysisFuncType_name = map[int32]string{
	0: "MultiPlayPracAnalysisInvalid",
	1: "MultiPlayPracAnalysisQuery",
	2: "MultiPlayPracAnalysisExport",
}
var MultiPlayPracAnalysisFuncType_value = map[string]int32{
	"MultiPlayPracAnalysisInvalid": 0,
	"MultiPlayPracAnalysisQuery":   1,
	"MultiPlayPracAnalysisExport":  2,
}

func (x MultiPlayPracAnalysisFuncType) String() string {
	return proto.EnumName(MultiPlayPracAnalysisFuncType_name, int32(x))
}
func (MultiPlayPracAnalysisFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{26}
}

// 多人互动经营经营总览功能权限 移位定义 1,2,4,8...
type MultiPlayOperationFuncType int32

const (
	MultiPlayOperationFuncType_MultiPlayOperationInvalid MultiPlayOperationFuncType = 0
	MultiPlayOperationFuncType_MultiPlayOperationQuery   MultiPlayOperationFuncType = 1
)

var MultiPlayOperationFuncType_name = map[int32]string{
	0: "MultiPlayOperationInvalid",
	1: "MultiPlayOperationQuery",
}
var MultiPlayOperationFuncType_value = map[string]int32{
	"MultiPlayOperationInvalid": 0,
	"MultiPlayOperationQuery":   1,
}

func (x MultiPlayOperationFuncType) String() string {
	return proto.EnumName(MultiPlayOperationFuncType_name, int32(x))
}
func (MultiPlayOperationFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{27}
}

// 消息管理 功能权限 移位定义 1,2,4,8...
type GuildMsgFuncType int32

const (
	GuildMsgFuncType_GuildMsgInvalid GuildMsgFuncType = 0
	GuildMsgFuncType_GuildMsgQuery   GuildMsgFuncType = 1
)

var GuildMsgFuncType_name = map[int32]string{
	0: "GuildMsgInvalid",
	1: "GuildMsgQuery",
}
var GuildMsgFuncType_value = map[string]int32{
	"GuildMsgInvalid": 0,
	"GuildMsgQuery":   1,
}

func (x GuildMsgFuncType) String() string {
	return proto.EnumName(GuildMsgFuncType_name, int32(x))
}
func (GuildMsgFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{28}
}

// 开播管理 功能权限 移位定义 1,2,4,8...
type LiveManageFuncType int32

const (
	LiveManageFuncType_LiveManageInvalid LiveManageFuncType = 0
	LiveManageFuncType_LiveManageQuery   LiveManageFuncType = 1
	LiveManageFuncType_LiveManageExport  LiveManageFuncType = 2
)

var LiveManageFuncType_name = map[int32]string{
	0: "LiveManageInvalid",
	1: "LiveManageQuery",
	2: "LiveManageExport",
}
var LiveManageFuncType_value = map[string]int32{
	"LiveManageInvalid": 0,
	"LiveManageQuery":   1,
	"LiveManageExport":  2,
}

func (x LiveManageFuncType) String() string {
	return proto.EnumName(LiveManageFuncType_name, int32(x))
}
func (LiveManageFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{29}
}

// 电竞经营管理 技能功能权限 移位定义 1,2,4,8...
type EsportManageSkillFuncType int32

const (
	EsportManageSkillFuncType_EsportManageSkillInvalid EsportManageSkillFuncType = 0
	EsportManageSkillFuncType_EsportManageSkillQuery   EsportManageSkillFuncType = 1
	EsportManageSkillFuncType_EsportManageSkillEdit    EsportManageSkillFuncType = 2
)

var EsportManageSkillFuncType_name = map[int32]string{
	0: "EsportManageSkillInvalid",
	1: "EsportManageSkillQuery",
	2: "EsportManageSkillEdit",
}
var EsportManageSkillFuncType_value = map[string]int32{
	"EsportManageSkillInvalid": 0,
	"EsportManageSkillQuery":   1,
	"EsportManageSkillEdit":    2,
}

func (x EsportManageSkillFuncType) String() string {
	return proto.EnumName(EsportManageSkillFuncType_name, int32(x))
}
func (EsportManageSkillFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{30}
}

// 电竞经营管理 业绩分析功能权限 移位定义 1,2,4,8...
type EsportManagePerformanceFuncType int32

const (
	EsportManagePerformanceFuncType_EsportManagePerformanceInvalid EsportManagePerformanceFuncType = 0
	EsportManagePerformanceFuncType_EsportManagePerformanceQuery   EsportManagePerformanceFuncType = 1
	EsportManagePerformanceFuncType_EsportManagePerformanceExport  EsportManagePerformanceFuncType = 2
)

var EsportManagePerformanceFuncType_name = map[int32]string{
	0: "EsportManagePerformanceInvalid",
	1: "EsportManagePerformanceQuery",
	2: "EsportManagePerformanceExport",
}
var EsportManagePerformanceFuncType_value = map[string]int32{
	"EsportManagePerformanceInvalid": 0,
	"EsportManagePerformanceQuery":   1,
	"EsportManagePerformanceExport":  2,
}

func (x EsportManagePerformanceFuncType) String() string {
	return proto.EnumName(EsportManagePerformanceFuncType_name, int32(x))
}
func (EsportManagePerformanceFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{31}
}

// 接档管理 功能权限 移位定义 1,2,4,8...
type MultiScheduleDataFuncType int32

const (
	MultiScheduleDataFuncType_MultiScheduleDataInvalid  MultiScheduleDataFuncType = 0
	MultiScheduleDataFuncType_MultiScheduleDataQuery    MultiScheduleDataFuncType = 1
	MultiScheduleDataFuncType_MultiScheduleDataExport   MultiScheduleDataFuncType = 2
	MultiScheduleDataFuncType_MultiScheduleDataSetAdmin MultiScheduleDataFuncType = 4
)

var MultiScheduleDataFuncType_name = map[int32]string{
	0: "MultiScheduleDataInvalid",
	1: "MultiScheduleDataQuery",
	2: "MultiScheduleDataExport",
	4: "MultiScheduleDataSetAdmin",
}
var MultiScheduleDataFuncType_value = map[string]int32{
	"MultiScheduleDataInvalid":  0,
	"MultiScheduleDataQuery":    1,
	"MultiScheduleDataExport":   2,
	"MultiScheduleDataSetAdmin": 4,
}

func (x MultiScheduleDataFuncType) String() string {
	return proto.EnumName(MultiScheduleDataFuncType_name, int32(x))
}
func (MultiScheduleDataFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{32}
}

// 经营诊断 功能权限 移位定义 1,2,4,8...
type BusinessDiagnosisFuncType int32

const (
	BusinessDiagnosisFuncType_BusinessDiagnosisInvalid BusinessDiagnosisFuncType = 0
	BusinessDiagnosisFuncType_BusinessDiagnosisQuery   BusinessDiagnosisFuncType = 1
	BusinessDiagnosisFuncType_BusinessDiagnosisExport  BusinessDiagnosisFuncType = 2
	BusinessDiagnosisFuncType_BusinessDiagnosisAbility BusinessDiagnosisFuncType = 4
	BusinessDiagnosisFuncType_BusinessDiagnosisMgr     BusinessDiagnosisFuncType = 8
	BusinessDiagnosisFuncType_BusinessDiagnosisRevenue BusinessDiagnosisFuncType = 16
	BusinessDiagnosisFuncType_BusinessDiagnosisRecruit BusinessDiagnosisFuncType = 32
)

var BusinessDiagnosisFuncType_name = map[int32]string{
	0:  "BusinessDiagnosisInvalid",
	1:  "BusinessDiagnosisQuery",
	2:  "BusinessDiagnosisExport",
	4:  "BusinessDiagnosisAbility",
	8:  "BusinessDiagnosisMgr",
	16: "BusinessDiagnosisRevenue",
	32: "BusinessDiagnosisRecruit",
}
var BusinessDiagnosisFuncType_value = map[string]int32{
	"BusinessDiagnosisInvalid": 0,
	"BusinessDiagnosisQuery":   1,
	"BusinessDiagnosisExport":  2,
	"BusinessDiagnosisAbility": 4,
	"BusinessDiagnosisMgr":     8,
	"BusinessDiagnosisRevenue": 16,
	"BusinessDiagnosisRecruit": 32,
}

func (x BusinessDiagnosisFuncType) String() string {
	return proto.EnumName(BusinessDiagnosisFuncType_name, int32(x))
}
func (BusinessDiagnosisFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{33}
}

// 语音直播从业者本人验证 功能权限 移位定义 1,2,4,8...
type LiveAnchorFaceCheckDetailFuncType int32

const (
	LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailInvalid LiveAnchorFaceCheckDetailFuncType = 0
	LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailQuery   LiveAnchorFaceCheckDetailFuncType = 1
	LiveAnchorFaceCheckDetailFuncType_LiveAnchorFaceCheckDetailExport  LiveAnchorFaceCheckDetailFuncType = 2
)

var LiveAnchorFaceCheckDetailFuncType_name = map[int32]string{
	0: "LiveAnchorFaceCheckDetailInvalid",
	1: "LiveAnchorFaceCheckDetailQuery",
	2: "LiveAnchorFaceCheckDetailExport",
}
var LiveAnchorFaceCheckDetailFuncType_value = map[string]int32{
	"LiveAnchorFaceCheckDetailInvalid": 0,
	"LiveAnchorFaceCheckDetailQuery":   1,
	"LiveAnchorFaceCheckDetailExport":  2,
}

func (x LiveAnchorFaceCheckDetailFuncType) String() string {
	return proto.EnumName(LiveAnchorFaceCheckDetailFuncType_name, int32(x))
}
func (LiveAnchorFaceCheckDetailFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{34}
}

// 多人互动经营从业者本人验证 功能权限 移位定义 1,2,4,8...
type MultiAnchorFaceCheckDetailFuncType int32

const (
	MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailInvalid MultiAnchorFaceCheckDetailFuncType = 0
	MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailQuery   MultiAnchorFaceCheckDetailFuncType = 1
	MultiAnchorFaceCheckDetailFuncType_MultiAnchorFaceCheckDetailExport  MultiAnchorFaceCheckDetailFuncType = 2
)

var MultiAnchorFaceCheckDetailFuncType_name = map[int32]string{
	0: "MultiAnchorFaceCheckDetailInvalid",
	1: "MultiAnchorFaceCheckDetailQuery",
	2: "MultiAnchorFaceCheckDetailExport",
}
var MultiAnchorFaceCheckDetailFuncType_value = map[string]int32{
	"MultiAnchorFaceCheckDetailInvalid": 0,
	"MultiAnchorFaceCheckDetailQuery":   1,
	"MultiAnchorFaceCheckDetailExport":  2,
}

func (x MultiAnchorFaceCheckDetailFuncType) String() string {
	return proto.EnumName(MultiAnchorFaceCheckDetailFuncType_name, int32(x))
}
func (MultiAnchorFaceCheckDetailFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{35}
}

// 首页管理多人互动 功能权限 移位定义 1,2,4,8...
type HomePageMultiFuncType int32

const (
	HomePageMultiFuncType_HomePageMultiFuncInValid           HomePageMultiFuncType = 0
	HomePageMultiFuncType_HomePageMultiFuncGuildMultiKeyData HomePageMultiFuncType = 1
	HomePageMultiFuncType_HomePageMultiFuncBanner            HomePageMultiFuncType = 2
	HomePageMultiFuncType_HomePageMultiFuncMultiTopChannel   HomePageMultiFuncType = 4
)

var HomePageMultiFuncType_name = map[int32]string{
	0: "HomePageMultiFuncInValid",
	1: "HomePageMultiFuncGuildMultiKeyData",
	2: "HomePageMultiFuncBanner",
	4: "HomePageMultiFuncMultiTopChannel",
}
var HomePageMultiFuncType_value = map[string]int32{
	"HomePageMultiFuncInValid":           0,
	"HomePageMultiFuncGuildMultiKeyData": 1,
	"HomePageMultiFuncBanner":            2,
	"HomePageMultiFuncMultiTopChannel":   4,
}

func (x HomePageMultiFuncType) String() string {
	return proto.EnumName(HomePageMultiFuncType_name, int32(x))
}
func (HomePageMultiFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{36}
}

// 多人互动经营分析 功能权限 移位定义 1,2,4,8...
type MultiBusinessDiagFuncType int32

const (
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncInValid        MultiBusinessDiagFuncType = 0
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncRadarChart     MultiBusinessDiagFuncType = 1
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncRevenue        MultiBusinessDiagFuncType = 2
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncRecruit        MultiBusinessDiagFuncType = 4
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncHatch          MultiBusinessDiagFuncType = 8
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncSafety         MultiBusinessDiagFuncType = 16
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncStability      MultiBusinessDiagFuncType = 32
	MultiBusinessDiagFuncType_MultiBusinessDiagFuncRiskResistance MultiBusinessDiagFuncType = 64
)

var MultiBusinessDiagFuncType_name = map[int32]string{
	0:  "MultiBusinessDiagFuncInValid",
	1:  "MultiBusinessDiagFuncRadarChart",
	2:  "MultiBusinessDiagFuncRevenue",
	4:  "MultiBusinessDiagFuncRecruit",
	8:  "MultiBusinessDiagFuncHatch",
	16: "MultiBusinessDiagFuncSafety",
	32: "MultiBusinessDiagFuncStability",
	64: "MultiBusinessDiagFuncRiskResistance",
}
var MultiBusinessDiagFuncType_value = map[string]int32{
	"MultiBusinessDiagFuncInValid":        0,
	"MultiBusinessDiagFuncRadarChart":     1,
	"MultiBusinessDiagFuncRevenue":        2,
	"MultiBusinessDiagFuncRecruit":        4,
	"MultiBusinessDiagFuncHatch":          8,
	"MultiBusinessDiagFuncSafety":         16,
	"MultiBusinessDiagFuncStability":      32,
	"MultiBusinessDiagFuncRiskResistance": 64,
}

func (x MultiBusinessDiagFuncType) String() string {
	return proto.EnumName(MultiBusinessDiagFuncType_name, int32(x))
}
func (MultiBusinessDiagFuncType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{37}
}

// 采用移位定义， 0,1,2,4,8...
// 公会代理人类型
type AgentType int32

const (
	AgentType_InValidAgent AgentType = 0
	AgentType_AgentBroker  AgentType = 1
	AgentType_AgentAdmin   AgentType = 2
)

var AgentType_name = map[int32]string{
	0: "InValidAgent",
	1: "AgentBroker",
	2: "AgentAdmin",
}
var AgentType_value = map[string]int32{
	"InValidAgent": 0,
	"AgentBroker":  1,
	"AgentAdmin":   2,
}

func (x AgentType) String() string {
	return proto.EnumName(AgentType_name, int32(x))
}
func (AgentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{38}
}

type VerifyScene int32

const (
	VerifyScene_VerifySceneInvalid  VerifyScene = 0
	VerifyScene_VerifySceneAddAgent VerifyScene = 1
	VerifyScene_VerifySceneDelAgent VerifyScene = 2
)

var VerifyScene_name = map[int32]string{
	0: "VerifySceneInvalid",
	1: "VerifySceneAddAgent",
	2: "VerifySceneDelAgent",
}
var VerifyScene_value = map[string]int32{
	"VerifySceneInvalid":  0,
	"VerifySceneAddAgent": 1,
	"VerifySceneDelAgent": 2,
}

func (x VerifyScene) String() string {
	return proto.EnumName(VerifyScene_name, int32(x))
}
func (VerifyScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{39}
}

// 公会代理人邀请状态
type InviteStatusType int32

const (
	InviteStatusType_INVITE_STATUS_TYPE_UNSPECIFIED InviteStatusType = 0
	InviteStatusType_INVITE_STATUS_TYPE_PROCESSING  InviteStatusType = 1
	InviteStatusType_INVITE_STATUS_TYPE_ACCEPT      InviteStatusType = 2
	InviteStatusType_INVITE_STATUS_TYPE_REJECT      InviteStatusType = 3
	InviteStatusType_INVITE_STATUS_TYPE_TIME_OUT    InviteStatusType = 4
	InviteStatusType_INVITE_STATUS_TYPE_CANCEL_SIGN InviteStatusType = 5
	InviteStatusType_INVITE_STATUS_TYPE_CANCEL      InviteStatusType = 6
)

var InviteStatusType_name = map[int32]string{
	0: "INVITE_STATUS_TYPE_UNSPECIFIED",
	1: "INVITE_STATUS_TYPE_PROCESSING",
	2: "INVITE_STATUS_TYPE_ACCEPT",
	3: "INVITE_STATUS_TYPE_REJECT",
	4: "INVITE_STATUS_TYPE_TIME_OUT",
	5: "INVITE_STATUS_TYPE_CANCEL_SIGN",
	6: "INVITE_STATUS_TYPE_CANCEL",
}
var InviteStatusType_value = map[string]int32{
	"INVITE_STATUS_TYPE_UNSPECIFIED": 0,
	"INVITE_STATUS_TYPE_PROCESSING":  1,
	"INVITE_STATUS_TYPE_ACCEPT":      2,
	"INVITE_STATUS_TYPE_REJECT":      3,
	"INVITE_STATUS_TYPE_TIME_OUT":    4,
	"INVITE_STATUS_TYPE_CANCEL_SIGN": 5,
	"INVITE_STATUS_TYPE_CANCEL":      6,
}

func (x InviteStatusType) String() string {
	return proto.EnumName(InviteStatusType_name, int32(x))
}
func (InviteStatusType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{40}
}

type MultiChannelEmploymentType int32

const (
	MultiChannelEmploymentType_MultiChannelEmploymentTypeInvalid  MultiChannelEmploymentType = 0
	MultiChannelEmploymentType_MultiChannelEmploymentTypeAdmin    MultiChannelEmploymentType = 1
	MultiChannelEmploymentType_MultiChannelEmploymentTypeEmployee MultiChannelEmploymentType = 2
)

var MultiChannelEmploymentType_name = map[int32]string{
	0: "MultiChannelEmploymentTypeInvalid",
	1: "MultiChannelEmploymentTypeAdmin",
	2: "MultiChannelEmploymentTypeEmployee",
}
var MultiChannelEmploymentType_value = map[string]int32{
	"MultiChannelEmploymentTypeInvalid":  0,
	"MultiChannelEmploymentTypeAdmin":    1,
	"MultiChannelEmploymentTypeEmployee": 2,
}

func (x MultiChannelEmploymentType) String() string {
	return proto.EnumName(MultiChannelEmploymentType_name, int32(x))
}
func (MultiChannelEmploymentType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{41}
}

type MultiChannelEmploymentOpt int32

const (
	MultiChannelEmploymentOpt_MultiChannelEmploymentOptInvalid MultiChannelEmploymentOpt = 0
	MultiChannelEmploymentOpt_MultiChannelEmploymentOptAdd     MultiChannelEmploymentOpt = 1
	MultiChannelEmploymentOpt_MultiChannelEmploymentOptDel     MultiChannelEmploymentOpt = 2
)

var MultiChannelEmploymentOpt_name = map[int32]string{
	0: "MultiChannelEmploymentOptInvalid",
	1: "MultiChannelEmploymentOptAdd",
	2: "MultiChannelEmploymentOptDel",
}
var MultiChannelEmploymentOpt_value = map[string]int32{
	"MultiChannelEmploymentOptInvalid": 0,
	"MultiChannelEmploymentOptAdd":     1,
	"MultiChannelEmploymentOptDel":     2,
}

func (x MultiChannelEmploymentOpt) String() string {
	return proto.EnumName(MultiChannelEmploymentOpt_name, int32(x))
}
func (MultiChannelEmploymentOpt) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{42}
}

// 导出主播人脸核验类型
type FaceCheckExportType int32

const (
	FaceCheckExportType_FaceCheckTypeInvalid            FaceCheckExportType = 0
	FaceCheckExportType_FaceCheckTypeDailyDetail        FaceCheckExportType = 1
	FaceCheckExportType_FaceCheckExportTypeWeeklyDetail FaceCheckExportType = 2
	FaceCheckExportType_FaceCheckExportTypeWeeklySum    FaceCheckExportType = 3
	FaceCheckExportType_FaceCheckExportTypeDailySum     FaceCheckExportType = 4
)

var FaceCheckExportType_name = map[int32]string{
	0: "FaceCheckTypeInvalid",
	1: "FaceCheckTypeDailyDetail",
	2: "FaceCheckExportTypeWeeklyDetail",
	3: "FaceCheckExportTypeWeeklySum",
	4: "FaceCheckExportTypeDailySum",
}
var FaceCheckExportType_value = map[string]int32{
	"FaceCheckTypeInvalid":            0,
	"FaceCheckTypeDailyDetail":        1,
	"FaceCheckExportTypeWeeklyDetail": 2,
	"FaceCheckExportTypeWeeklySum":    3,
	"FaceCheckExportTypeDailySum":     4,
}

func (x FaceCheckExportType) String() string {
	return proto.EnumName(FaceCheckExportType_name, int32(x))
}
func (FaceCheckExportType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{43}
}

// 经营后台菜单权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type MenuPermission struct {
	GuildLiveDataMenuPer uint32   `protobuf:"varint,1,opt,name=guildLiveDataMenuPer,proto3" json:"guildLiveDataMenuPer,omitempty"`
	GuildAgentMenuPer    uint32   `protobuf:"varint,2,opt,name=guildAgentMenuPer,proto3" json:"guildAgentMenuPer,omitempty"`
	FlowCardMenuPer      uint32   `protobuf:"varint,3,opt,name=flowCardMenuPer,proto3" json:"flowCardMenuPer,omitempty"`
	Contract             uint32   `protobuf:"varint,4,opt,name=contract,proto3" json:"contract,omitempty"`
	MultiPlay            uint32   `protobuf:"varint,5,opt,name=multiPlay,proto3" json:"multiPlay,omitempty"`
	Msg                  uint32   `protobuf:"varint,6,opt,name=msg,proto3" json:"msg,omitempty"`
	GuildManage          uint32   `protobuf:"varint,7,opt,name=guildManage,proto3" json:"guildManage,omitempty"`
	EsportMenuPer        uint32   `protobuf:"varint,8,opt,name=esportMenuPer,proto3" json:"esportMenuPer,omitempty"`
	HomePageMenuPer      uint32   `protobuf:"varint,9,opt,name=homePageMenuPer,proto3" json:"homePageMenuPer,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MenuPermission) Reset()         { *m = MenuPermission{} }
func (m *MenuPermission) String() string { return proto.CompactTextString(m) }
func (*MenuPermission) ProtoMessage()    {}
func (*MenuPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{0}
}
func (m *MenuPermission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MenuPermission.Unmarshal(m, b)
}
func (m *MenuPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MenuPermission.Marshal(b, m, deterministic)
}
func (dst *MenuPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MenuPermission.Merge(dst, src)
}
func (m *MenuPermission) XXX_Size() int {
	return xxx_messageInfo_MenuPermission.Size(m)
}
func (m *MenuPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_MenuPermission.DiscardUnknown(m)
}

var xxx_messageInfo_MenuPermission proto.InternalMessageInfo

func (m *MenuPermission) GetGuildLiveDataMenuPer() uint32 {
	if m != nil {
		return m.GuildLiveDataMenuPer
	}
	return 0
}

func (m *MenuPermission) GetGuildAgentMenuPer() uint32 {
	if m != nil {
		return m.GuildAgentMenuPer
	}
	return 0
}

func (m *MenuPermission) GetFlowCardMenuPer() uint32 {
	if m != nil {
		return m.FlowCardMenuPer
	}
	return 0
}

func (m *MenuPermission) GetContract() uint32 {
	if m != nil {
		return m.Contract
	}
	return 0
}

func (m *MenuPermission) GetMultiPlay() uint32 {
	if m != nil {
		return m.MultiPlay
	}
	return 0
}

func (m *MenuPermission) GetMsg() uint32 {
	if m != nil {
		return m.Msg
	}
	return 0
}

func (m *MenuPermission) GetGuildManage() uint32 {
	if m != nil {
		return m.GuildManage
	}
	return 0
}

func (m *MenuPermission) GetEsportMenuPer() uint32 {
	if m != nil {
		return m.EsportMenuPer
	}
	return 0
}

func (m *MenuPermission) GetHomePageMenuPer() uint32 {
	if m != nil {
		return m.HomePageMenuPer
	}
	return 0
}

// 语音直播数据权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type LiveDataPermission struct {
	GuildLiveStatsDataPer uint32   `protobuf:"varint,1,opt,name=guildLiveStatsDataPer,proto3" json:"guildLiveStatsDataPer,omitempty"`
	GuildTaskDataPer      uint32   `protobuf:"varint,2,opt,name=guildTaskDataPer,proto3" json:"guildTaskDataPer,omitempty"`
	AnchorDataPer         uint32   `protobuf:"varint,3,opt,name=anchorDataPer,proto3" json:"anchorDataPer,omitempty"`
	AnchorPracDataPer     uint32   `protobuf:"varint,4,opt,name=anchorPracDataPer,proto3" json:"anchorPracDataPer,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *LiveDataPermission) Reset()         { *m = LiveDataPermission{} }
func (m *LiveDataPermission) String() string { return proto.CompactTextString(m) }
func (*LiveDataPermission) ProtoMessage()    {}
func (*LiveDataPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{1}
}
func (m *LiveDataPermission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LiveDataPermission.Unmarshal(m, b)
}
func (m *LiveDataPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LiveDataPermission.Marshal(b, m, deterministic)
}
func (dst *LiveDataPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LiveDataPermission.Merge(dst, src)
}
func (m *LiveDataPermission) XXX_Size() int {
	return xxx_messageInfo_LiveDataPermission.Size(m)
}
func (m *LiveDataPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_LiveDataPermission.DiscardUnknown(m)
}

var xxx_messageInfo_LiveDataPermission proto.InternalMessageInfo

func (m *LiveDataPermission) GetGuildLiveStatsDataPer() uint32 {
	if m != nil {
		return m.GuildLiveStatsDataPer
	}
	return 0
}

func (m *LiveDataPermission) GetGuildTaskDataPer() uint32 {
	if m != nil {
		return m.GuildTaskDataPer
	}
	return 0
}

func (m *LiveDataPermission) GetAnchorDataPer() uint32 {
	if m != nil {
		return m.AnchorDataPer
	}
	return 0
}

func (m *LiveDataPermission) GetAnchorPracDataPer() uint32 {
	if m != nil {
		return m.AnchorPracDataPer
	}
	return 0
}

// 功能权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type FunctionPermission struct {
	ApplySign                      uint32   `protobuf:"varint,1,opt,name=applySign,proto3" json:"applySign,omitempty"`
	CancelSign                     uint32   `protobuf:"varint,2,opt,name=cancelSign,proto3" json:"cancelSign,omitempty"`
	ContractList                   uint32   `protobuf:"varint,3,opt,name=contractList,proto3" json:"contractList,omitempty"`
	ContractVio                    uint32   `protobuf:"varint,4,opt,name=contractVio,proto3" json:"contractVio,omitempty"`
	LiveTotalData                  uint32   `protobuf:"varint,5,opt,name=liveTotalData,proto3" json:"liveTotalData,omitempty"`
	LiveTaskData                   uint32   `protobuf:"varint,6,opt,name=liveTaskData,proto3" json:"liveTaskData,omitempty"`
	AnchorData                     uint32   `protobuf:"varint,7,opt,name=anchorData,proto3" json:"anchorData,omitempty"`
	AnchorPracAnalysis             uint32   `protobuf:"varint,8,opt,name=anchorPracAnalysis,proto3" json:"anchorPracAnalysis,omitempty"`
	FlowCard                       uint32   `protobuf:"varint,9,opt,name=flowCard,proto3" json:"flowCard,omitempty"`
	MultiPlayDetail                uint32   `protobuf:"varint,10,opt,name=multiPlayDetail,proto3" json:"multiPlayDetail,omitempty"`
	MultiPlayPracAnalysis          uint32   `protobuf:"varint,11,opt,name=multiPlayPracAnalysis,proto3" json:"multiPlayPracAnalysis,omitempty"`
	Msg                            uint32   `protobuf:"varint,12,opt,name=msg,proto3" json:"msg,omitempty"`
	MultiPlayOper                  uint32   `protobuf:"varint,13,opt,name=multiPlayOper,proto3" json:"multiPlayOper,omitempty"`
	MultiPlayHallTask              uint32   `protobuf:"varint,14,opt,name=multiPlayHallTask,proto3" json:"multiPlayHallTask,omitempty"`
	LiveManage                     uint32   `protobuf:"varint,15,opt,name=liveManage,proto3" json:"liveManage,omitempty"`
	EsportManageSkillFuncPer       uint32   `protobuf:"varint,16,opt,name=esportManageSkillFuncPer,proto3" json:"esportManageSkillFuncPer,omitempty"`
	EsportManagePerformanceFuncPer uint32   `protobuf:"varint,17,opt,name=esportManagePerformanceFuncPer,proto3" json:"esportManagePerformanceFuncPer,omitempty"`
	MultiScheduleDataFuncPer       uint32   `protobuf:"varint,18,opt,name=multiScheduleDataFuncPer,proto3" json:"multiScheduleDataFuncPer,omitempty"`
	BusinessDiag                   uint32   `protobuf:"varint,19,opt,name=businessDiag,proto3" json:"businessDiag,omitempty"`
	LiveAnchorFaceCheckDetail      uint32   `protobuf:"varint,20,opt,name=liveAnchorFaceCheckDetail,proto3" json:"liveAnchorFaceCheckDetail,omitempty"`
	MultiAnchorFaceCheckDetail     uint32   `protobuf:"varint,21,opt,name=multiAnchorFaceCheckDetail,proto3" json:"multiAnchorFaceCheckDetail,omitempty"`
	MultiWeddingReservedFuncPer    uint32   `protobuf:"varint,22,opt,name=multiWeddingReservedFuncPer,proto3" json:"multiWeddingReservedFuncPer,omitempty"`
	HomePageMultiFuncPer           uint32   `protobuf:"varint,23,opt,name=homePageMultiFuncPer,proto3" json:"homePageMultiFuncPer,omitempty"`
	MultiBusinessDiagFunc          uint32   `protobuf:"varint,24,opt,name=multiBusinessDiagFunc,proto3" json:"multiBusinessDiagFunc,omitempty"`
	XXX_NoUnkeyedLiteral           struct{} `json:"-"`
	XXX_unrecognized               []byte   `json:"-"`
	XXX_sizecache                  int32    `json:"-"`
}

func (m *FunctionPermission) Reset()         { *m = FunctionPermission{} }
func (m *FunctionPermission) String() string { return proto.CompactTextString(m) }
func (*FunctionPermission) ProtoMessage()    {}
func (*FunctionPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{2}
}
func (m *FunctionPermission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_FunctionPermission.Unmarshal(m, b)
}
func (m *FunctionPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_FunctionPermission.Marshal(b, m, deterministic)
}
func (dst *FunctionPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FunctionPermission.Merge(dst, src)
}
func (m *FunctionPermission) XXX_Size() int {
	return xxx_messageInfo_FunctionPermission.Size(m)
}
func (m *FunctionPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_FunctionPermission.DiscardUnknown(m)
}

var xxx_messageInfo_FunctionPermission proto.InternalMessageInfo

func (m *FunctionPermission) GetApplySign() uint32 {
	if m != nil {
		return m.ApplySign
	}
	return 0
}

func (m *FunctionPermission) GetCancelSign() uint32 {
	if m != nil {
		return m.CancelSign
	}
	return 0
}

func (m *FunctionPermission) GetContractList() uint32 {
	if m != nil {
		return m.ContractList
	}
	return 0
}

func (m *FunctionPermission) GetContractVio() uint32 {
	if m != nil {
		return m.ContractVio
	}
	return 0
}

func (m *FunctionPermission) GetLiveTotalData() uint32 {
	if m != nil {
		return m.LiveTotalData
	}
	return 0
}

func (m *FunctionPermission) GetLiveTaskData() uint32 {
	if m != nil {
		return m.LiveTaskData
	}
	return 0
}

func (m *FunctionPermission) GetAnchorData() uint32 {
	if m != nil {
		return m.AnchorData
	}
	return 0
}

func (m *FunctionPermission) GetAnchorPracAnalysis() uint32 {
	if m != nil {
		return m.AnchorPracAnalysis
	}
	return 0
}

func (m *FunctionPermission) GetFlowCard() uint32 {
	if m != nil {
		return m.FlowCard
	}
	return 0
}

func (m *FunctionPermission) GetMultiPlayDetail() uint32 {
	if m != nil {
		return m.MultiPlayDetail
	}
	return 0
}

func (m *FunctionPermission) GetMultiPlayPracAnalysis() uint32 {
	if m != nil {
		return m.MultiPlayPracAnalysis
	}
	return 0
}

func (m *FunctionPermission) GetMsg() uint32 {
	if m != nil {
		return m.Msg
	}
	return 0
}

func (m *FunctionPermission) GetMultiPlayOper() uint32 {
	if m != nil {
		return m.MultiPlayOper
	}
	return 0
}

func (m *FunctionPermission) GetMultiPlayHallTask() uint32 {
	if m != nil {
		return m.MultiPlayHallTask
	}
	return 0
}

func (m *FunctionPermission) GetLiveManage() uint32 {
	if m != nil {
		return m.LiveManage
	}
	return 0
}

func (m *FunctionPermission) GetEsportManageSkillFuncPer() uint32 {
	if m != nil {
		return m.EsportManageSkillFuncPer
	}
	return 0
}

func (m *FunctionPermission) GetEsportManagePerformanceFuncPer() uint32 {
	if m != nil {
		return m.EsportManagePerformanceFuncPer
	}
	return 0
}

func (m *FunctionPermission) GetMultiScheduleDataFuncPer() uint32 {
	if m != nil {
		return m.MultiScheduleDataFuncPer
	}
	return 0
}

func (m *FunctionPermission) GetBusinessDiag() uint32 {
	if m != nil {
		return m.BusinessDiag
	}
	return 0
}

func (m *FunctionPermission) GetLiveAnchorFaceCheckDetail() uint32 {
	if m != nil {
		return m.LiveAnchorFaceCheckDetail
	}
	return 0
}

func (m *FunctionPermission) GetMultiAnchorFaceCheckDetail() uint32 {
	if m != nil {
		return m.MultiAnchorFaceCheckDetail
	}
	return 0
}

func (m *FunctionPermission) GetMultiWeddingReservedFuncPer() uint32 {
	if m != nil {
		return m.MultiWeddingReservedFuncPer
	}
	return 0
}

func (m *FunctionPermission) GetHomePageMultiFuncPer() uint32 {
	if m != nil {
		return m.HomePageMultiFuncPer
	}
	return 0
}

func (m *FunctionPermission) GetMultiBusinessDiagFunc() uint32 {
	if m != nil {
		return m.MultiBusinessDiagFunc
	}
	return 0
}

// 数据权限配置
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type DataPermission struct {
	LiveStats             uint32   `protobuf:"varint,1,opt,name=liveStats,proto3" json:"liveStats,omitempty"`
	TaskData              uint32   `protobuf:"varint,2,opt,name=taskData,proto3" json:"taskData,omitempty"`
	AnchorData            uint32   `protobuf:"varint,3,opt,name=anchorData,proto3" json:"anchorData,omitempty"`
	AnchorPrac            uint32   `protobuf:"varint,4,opt,name=anchorPrac,proto3" json:"anchorPrac,omitempty"`
	MultiPlayDetail       uint32   `protobuf:"varint,5,opt,name=multiPlayDetail,proto3" json:"multiPlayDetail,omitempty"`
	MultiPlayPracAnalysis uint32   `protobuf:"varint,6,opt,name=MultiPlayPracAnalysis,proto3" json:"MultiPlayPracAnalysis,omitempty"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-"`
	XXX_unrecognized      []byte   `json:"-"`
	XXX_sizecache         int32    `json:"-"`
}

func (m *DataPermission) Reset()         { *m = DataPermission{} }
func (m *DataPermission) String() string { return proto.CompactTextString(m) }
func (*DataPermission) ProtoMessage()    {}
func (*DataPermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{3}
}
func (m *DataPermission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPermission.Unmarshal(m, b)
}
func (m *DataPermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPermission.Marshal(b, m, deterministic)
}
func (dst *DataPermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPermission.Merge(dst, src)
}
func (m *DataPermission) XXX_Size() int {
	return xxx_messageInfo_DataPermission.Size(m)
}
func (m *DataPermission) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPermission.DiscardUnknown(m)
}

var xxx_messageInfo_DataPermission proto.InternalMessageInfo

func (m *DataPermission) GetLiveStats() uint32 {
	if m != nil {
		return m.LiveStats
	}
	return 0
}

func (m *DataPermission) GetTaskData() uint32 {
	if m != nil {
		return m.TaskData
	}
	return 0
}

func (m *DataPermission) GetAnchorData() uint32 {
	if m != nil {
		return m.AnchorData
	}
	return 0
}

func (m *DataPermission) GetAnchorPrac() uint32 {
	if m != nil {
		return m.AnchorPrac
	}
	return 0
}

func (m *DataPermission) GetMultiPlayDetail() uint32 {
	if m != nil {
		return m.MultiPlayDetail
	}
	return 0
}

func (m *DataPermission) GetMultiPlayPracAnalysis() uint32 {
	if m != nil {
		return m.MultiPlayPracAnalysis
	}
	return 0
}

// 公会经营后台权限
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GuildManagePermission struct {
	GuildManageMenuPer   *MenuPermission     `protobuf:"bytes,1,opt,name=guildManageMenuPer,proto3" json:"guildManageMenuPer,omitempty"`
	LiveDataPermission   *LiveDataPermission `protobuf:"bytes,2,opt,name=liveDataPermission,proto3" json:"liveDataPermission,omitempty"`
	FunctionPer          *FunctionPermission `protobuf:"bytes,3,opt,name=functionPer,proto3" json:"functionPer,omitempty"`
	DataPer              *DataPermission     `protobuf:"bytes,4,opt,name=dataPer,proto3" json:"dataPer,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GuildManagePermission) Reset()         { *m = GuildManagePermission{} }
func (m *GuildManagePermission) String() string { return proto.CompactTextString(m) }
func (*GuildManagePermission) ProtoMessage()    {}
func (*GuildManagePermission) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{4}
}
func (m *GuildManagePermission) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildManagePermission.Unmarshal(m, b)
}
func (m *GuildManagePermission) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildManagePermission.Marshal(b, m, deterministic)
}
func (dst *GuildManagePermission) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildManagePermission.Merge(dst, src)
}
func (m *GuildManagePermission) XXX_Size() int {
	return xxx_messageInfo_GuildManagePermission.Size(m)
}
func (m *GuildManagePermission) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildManagePermission.DiscardUnknown(m)
}

var xxx_messageInfo_GuildManagePermission proto.InternalMessageInfo

func (m *GuildManagePermission) GetGuildManageMenuPer() *MenuPermission {
	if m != nil {
		return m.GuildManageMenuPer
	}
	return nil
}

func (m *GuildManagePermission) GetLiveDataPermission() *LiveDataPermission {
	if m != nil {
		return m.LiveDataPermission
	}
	return nil
}

func (m *GuildManagePermission) GetFunctionPer() *FunctionPermission {
	if m != nil {
		return m.FunctionPer
	}
	return nil
}

func (m *GuildManagePermission) GetDataPer() *DataPermission {
	if m != nil {
		return m.DataPer
	}
	return nil
}

// 公会代理人
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type GuildAgent struct {
	Uid                  uint32                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Permission           *GuildManagePermission `protobuf:"bytes,2,opt,name=permission,proto3" json:"permission,omitempty"`
	AddTs                uint32                 `protobuf:"varint,3,opt,name=add_ts,json=addTs,proto3" json:"add_ts,omitempty"`
	Guild_Id             uint32                 `protobuf:"varint,4,opt,name=guild_Id,json=guildId,proto3" json:"guild_Id,omitempty"`
	AgentType            uint32                 `protobuf:"varint,6,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	AdminAddTs           uint32                 `protobuf:"varint,7,opt,name=admin_add_ts,json=adminAddTs,proto3" json:"admin_add_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *GuildAgent) Reset()         { *m = GuildAgent{} }
func (m *GuildAgent) String() string { return proto.CompactTextString(m) }
func (*GuildAgent) ProtoMessage()    {}
func (*GuildAgent) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{5}
}
func (m *GuildAgent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildAgent.Unmarshal(m, b)
}
func (m *GuildAgent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildAgent.Marshal(b, m, deterministic)
}
func (dst *GuildAgent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildAgent.Merge(dst, src)
}
func (m *GuildAgent) XXX_Size() int {
	return xxx_messageInfo_GuildAgent.Size(m)
}
func (m *GuildAgent) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildAgent.DiscardUnknown(m)
}

var xxx_messageInfo_GuildAgent proto.InternalMessageInfo

func (m *GuildAgent) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildAgent) GetPermission() *GuildManagePermission {
	if m != nil {
		return m.Permission
	}
	return nil
}

func (m *GuildAgent) GetAddTs() uint32 {
	if m != nil {
		return m.AddTs
	}
	return 0
}

func (m *GuildAgent) GetGuild_Id() uint32 {
	if m != nil {
		return m.Guild_Id
	}
	return 0
}

func (m *GuildAgent) GetAgentType() uint32 {
	if m != nil {
		return m.AgentType
	}
	return 0
}

func (m *GuildAgent) GetAdminAddTs() uint32 {
	if m != nil {
		return m.AdminAddTs
	}
	return 0
}

// 获取公会代理人列表
type GetGuildAgentListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	AgentType            uint32   `protobuf:"varint,3,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAgentListReq) Reset()         { *m = GetGuildAgentListReq{} }
func (m *GetGuildAgentListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAgentListReq) ProtoMessage()    {}
func (*GetGuildAgentListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{6}
}
func (m *GetGuildAgentListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAgentListReq.Unmarshal(m, b)
}
func (m *GetGuildAgentListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAgentListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAgentListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAgentListReq.Merge(dst, src)
}
func (m *GetGuildAgentListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAgentListReq.Size(m)
}
func (m *GetGuildAgentListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAgentListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAgentListReq proto.InternalMessageInfo

func (m *GetGuildAgentListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildAgentListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetGuildAgentListReq) GetAgentType() uint32 {
	if m != nil {
		return m.AgentType
	}
	return 0
}

type GetGuildAgentListResp struct {
	AgentList            []*GuildAgent `protobuf:"bytes,1,rep,name=agent_list,json=agentList,proto3" json:"agent_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetGuildAgentListResp) Reset()         { *m = GetGuildAgentListResp{} }
func (m *GetGuildAgentListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAgentListResp) ProtoMessage()    {}
func (*GetGuildAgentListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{7}
}
func (m *GetGuildAgentListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAgentListResp.Unmarshal(m, b)
}
func (m *GetGuildAgentListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAgentListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAgentListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAgentListResp.Merge(dst, src)
}
func (m *GetGuildAgentListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAgentListResp.Size(m)
}
func (m *GetGuildAgentListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAgentListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAgentListResp proto.InternalMessageInfo

func (m *GetGuildAgentListResp) GetAgentList() []*GuildAgent {
	if m != nil {
		return m.AgentList
	}
	return nil
}

// 新增公会代理人
type AddGuildAgentReq struct {
	GuildId              uint32                 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AgentUid             uint32                 `protobuf:"varint,2,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	Permission           *GuildManagePermission `protobuf:"bytes,3,opt,name=permission,proto3" json:"permission,omitempty"`
	AgentType            uint32                 `protobuf:"varint,4,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *AddGuildAgentReq) Reset()         { *m = AddGuildAgentReq{} }
func (m *AddGuildAgentReq) String() string { return proto.CompactTextString(m) }
func (*AddGuildAgentReq) ProtoMessage()    {}
func (*AddGuildAgentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{8}
}
func (m *AddGuildAgentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGuildAgentReq.Unmarshal(m, b)
}
func (m *AddGuildAgentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGuildAgentReq.Marshal(b, m, deterministic)
}
func (dst *AddGuildAgentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGuildAgentReq.Merge(dst, src)
}
func (m *AddGuildAgentReq) XXX_Size() int {
	return xxx_messageInfo_AddGuildAgentReq.Size(m)
}
func (m *AddGuildAgentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGuildAgentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddGuildAgentReq proto.InternalMessageInfo

func (m *AddGuildAgentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddGuildAgentReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *AddGuildAgentReq) GetPermission() *GuildManagePermission {
	if m != nil {
		return m.Permission
	}
	return nil
}

func (m *AddGuildAgentReq) GetAgentType() uint32 {
	if m != nil {
		return m.AgentType
	}
	return 0
}

type AddGuildAgentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddGuildAgentResp) Reset()         { *m = AddGuildAgentResp{} }
func (m *AddGuildAgentResp) String() string { return proto.CompactTextString(m) }
func (*AddGuildAgentResp) ProtoMessage()    {}
func (*AddGuildAgentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{9}
}
func (m *AddGuildAgentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddGuildAgentResp.Unmarshal(m, b)
}
func (m *AddGuildAgentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddGuildAgentResp.Marshal(b, m, deterministic)
}
func (dst *AddGuildAgentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddGuildAgentResp.Merge(dst, src)
}
func (m *AddGuildAgentResp) XXX_Size() int {
	return xxx_messageInfo_AddGuildAgentResp.Size(m)
}
func (m *AddGuildAgentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddGuildAgentResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddGuildAgentResp proto.InternalMessageInfo

// 删除公会代理人
type DelGuildAgentReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AgentUid             uint32   `protobuf:"varint,2,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AgentType            uint32   `protobuf:"varint,3,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGuildAgentReq) Reset()         { *m = DelGuildAgentReq{} }
func (m *DelGuildAgentReq) String() string { return proto.CompactTextString(m) }
func (*DelGuildAgentReq) ProtoMessage()    {}
func (*DelGuildAgentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{10}
}
func (m *DelGuildAgentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGuildAgentReq.Unmarshal(m, b)
}
func (m *DelGuildAgentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGuildAgentReq.Marshal(b, m, deterministic)
}
func (dst *DelGuildAgentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGuildAgentReq.Merge(dst, src)
}
func (m *DelGuildAgentReq) XXX_Size() int {
	return xxx_messageInfo_DelGuildAgentReq.Size(m)
}
func (m *DelGuildAgentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGuildAgentReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelGuildAgentReq proto.InternalMessageInfo

func (m *DelGuildAgentReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DelGuildAgentReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *DelGuildAgentReq) GetAgentType() uint32 {
	if m != nil {
		return m.AgentType
	}
	return 0
}

type DelGuildAgentResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelGuildAgentResp) Reset()         { *m = DelGuildAgentResp{} }
func (m *DelGuildAgentResp) String() string { return proto.CompactTextString(m) }
func (*DelGuildAgentResp) ProtoMessage()    {}
func (*DelGuildAgentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{11}
}
func (m *DelGuildAgentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelGuildAgentResp.Unmarshal(m, b)
}
func (m *DelGuildAgentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelGuildAgentResp.Marshal(b, m, deterministic)
}
func (dst *DelGuildAgentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelGuildAgentResp.Merge(dst, src)
}
func (m *DelGuildAgentResp) XXX_Size() int {
	return xxx_messageInfo_DelGuildAgentResp.Size(m)
}
func (m *DelGuildAgentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelGuildAgentResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelGuildAgentResp proto.InternalMessageInfo

// 更新代理人的数据权限
type UpdateAgentDataPermissionReq struct {
	GuildId              uint32                 `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	AgentUid             uint32                 `protobuf:"varint,2,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	Permission           *GuildManagePermission `protobuf:"bytes,3,opt,name=permission,proto3" json:"permission,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *UpdateAgentDataPermissionReq) Reset()         { *m = UpdateAgentDataPermissionReq{} }
func (m *UpdateAgentDataPermissionReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAgentDataPermissionReq) ProtoMessage()    {}
func (*UpdateAgentDataPermissionReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{12}
}
func (m *UpdateAgentDataPermissionReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAgentDataPermissionReq.Unmarshal(m, b)
}
func (m *UpdateAgentDataPermissionReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAgentDataPermissionReq.Marshal(b, m, deterministic)
}
func (dst *UpdateAgentDataPermissionReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAgentDataPermissionReq.Merge(dst, src)
}
func (m *UpdateAgentDataPermissionReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAgentDataPermissionReq.Size(m)
}
func (m *UpdateAgentDataPermissionReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAgentDataPermissionReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAgentDataPermissionReq proto.InternalMessageInfo

func (m *UpdateAgentDataPermissionReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *UpdateAgentDataPermissionReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *UpdateAgentDataPermissionReq) GetPermission() *GuildManagePermission {
	if m != nil {
		return m.Permission
	}
	return nil
}

type UpdateAgentDataPermissionResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAgentDataPermissionResp) Reset()         { *m = UpdateAgentDataPermissionResp{} }
func (m *UpdateAgentDataPermissionResp) String() string { return proto.CompactTextString(m) }
func (*UpdateAgentDataPermissionResp) ProtoMessage()    {}
func (*UpdateAgentDataPermissionResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{13}
}
func (m *UpdateAgentDataPermissionResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAgentDataPermissionResp.Unmarshal(m, b)
}
func (m *UpdateAgentDataPermissionResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAgentDataPermissionResp.Marshal(b, m, deterministic)
}
func (dst *UpdateAgentDataPermissionResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAgentDataPermissionResp.Merge(dst, src)
}
func (m *UpdateAgentDataPermissionResp) XXX_Size() int {
	return xxx_messageInfo_UpdateAgentDataPermissionResp.Size(m)
}
func (m *UpdateAgentDataPermissionResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAgentDataPermissionResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAgentDataPermissionResp proto.InternalMessageInfo

// 获取代理人的公会信息
type GetAgentGuildReq struct {
	AgentUid             uint32   `protobuf:"varint,1,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAgentGuildReq) Reset()         { *m = GetAgentGuildReq{} }
func (m *GetAgentGuildReq) String() string { return proto.CompactTextString(m) }
func (*GetAgentGuildReq) ProtoMessage()    {}
func (*GetAgentGuildReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{14}
}
func (m *GetAgentGuildReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAgentGuildReq.Unmarshal(m, b)
}
func (m *GetAgentGuildReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAgentGuildReq.Marshal(b, m, deterministic)
}
func (dst *GetAgentGuildReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAgentGuildReq.Merge(dst, src)
}
func (m *GetAgentGuildReq) XXX_Size() int {
	return xxx_messageInfo_GetAgentGuildReq.Size(m)
}
func (m *GetAgentGuildReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAgentGuildReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAgentGuildReq proto.InternalMessageInfo

func (m *GetAgentGuildReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

type GetAgentGuildResp struct {
	Info                 *GuildAgent `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetAgentGuildResp) Reset()         { *m = GetAgentGuildResp{} }
func (m *GetAgentGuildResp) String() string { return proto.CompactTextString(m) }
func (*GetAgentGuildResp) ProtoMessage()    {}
func (*GetAgentGuildResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{15}
}
func (m *GetAgentGuildResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAgentGuildResp.Unmarshal(m, b)
}
func (m *GetAgentGuildResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAgentGuildResp.Marshal(b, m, deterministic)
}
func (dst *GetAgentGuildResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAgentGuildResp.Merge(dst, src)
}
func (m *GetAgentGuildResp) XXX_Size() int {
	return xxx_messageInfo_GetAgentGuildResp.Size(m)
}
func (m *GetAgentGuildResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAgentGuildResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAgentGuildResp proto.InternalMessageInfo

func (m *GetAgentGuildResp) GetInfo() *GuildAgent {
	if m != nil {
		return m.Info
	}
	return nil
}

// 主播信息
type AgentAnchorInfo struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	AgentUid             uint32   `protobuf:"varint,2,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AgentAnchorInfo) Reset()         { *m = AgentAnchorInfo{} }
func (m *AgentAnchorInfo) String() string { return proto.CompactTextString(m) }
func (*AgentAnchorInfo) ProtoMessage()    {}
func (*AgentAnchorInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{16}
}
func (m *AgentAnchorInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AgentAnchorInfo.Unmarshal(m, b)
}
func (m *AgentAnchorInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AgentAnchorInfo.Marshal(b, m, deterministic)
}
func (dst *AgentAnchorInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AgentAnchorInfo.Merge(dst, src)
}
func (m *AgentAnchorInfo) XXX_Size() int {
	return xxx_messageInfo_AgentAnchorInfo.Size(m)
}
func (m *AgentAnchorInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_AgentAnchorInfo.DiscardUnknown(m)
}

var xxx_messageInfo_AgentAnchorInfo proto.InternalMessageInfo

func (m *AgentAnchorInfo) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

func (m *AgentAnchorInfo) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

// 获取经纪人的主播信息列表
type GetAgentAnchorListReq struct {
	AgentUid             uint32   `protobuf:"varint,1,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	Page                 uint32   `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAgentAnchorListReq) Reset()         { *m = GetAgentAnchorListReq{} }
func (m *GetAgentAnchorListReq) String() string { return proto.CompactTextString(m) }
func (*GetAgentAnchorListReq) ProtoMessage()    {}
func (*GetAgentAnchorListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{17}
}
func (m *GetAgentAnchorListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAgentAnchorListReq.Unmarshal(m, b)
}
func (m *GetAgentAnchorListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAgentAnchorListReq.Marshal(b, m, deterministic)
}
func (dst *GetAgentAnchorListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAgentAnchorListReq.Merge(dst, src)
}
func (m *GetAgentAnchorListReq) XXX_Size() int {
	return xxx_messageInfo_GetAgentAnchorListReq.Size(m)
}
func (m *GetAgentAnchorListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAgentAnchorListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAgentAnchorListReq proto.InternalMessageInfo

func (m *GetAgentAnchorListReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetAgentAnchorListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetAgentAnchorListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetAgentAnchorListResp struct {
	InfoList             []*AgentAnchorInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	NextPage             uint32             `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32             `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetAgentAnchorListResp) Reset()         { *m = GetAgentAnchorListResp{} }
func (m *GetAgentAnchorListResp) String() string { return proto.CompactTextString(m) }
func (*GetAgentAnchorListResp) ProtoMessage()    {}
func (*GetAgentAnchorListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{18}
}
func (m *GetAgentAnchorListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAgentAnchorListResp.Unmarshal(m, b)
}
func (m *GetAgentAnchorListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAgentAnchorListResp.Marshal(b, m, deterministic)
}
func (dst *GetAgentAnchorListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAgentAnchorListResp.Merge(dst, src)
}
func (m *GetAgentAnchorListResp) XXX_Size() int {
	return xxx_messageInfo_GetAgentAnchorListResp.Size(m)
}
func (m *GetAgentAnchorListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAgentAnchorListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAgentAnchorListResp proto.InternalMessageInfo

func (m *GetAgentAnchorListResp) GetInfoList() []*AgentAnchorInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetAgentAnchorListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetAgentAnchorListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 获取经纪人的指定主播信息
type GetAgentAnchorReq struct {
	AgentUid             uint32   `protobuf:"varint,1,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AnchorUid            uint32   `protobuf:"varint,2,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAgentAnchorReq) Reset()         { *m = GetAgentAnchorReq{} }
func (m *GetAgentAnchorReq) String() string { return proto.CompactTextString(m) }
func (*GetAgentAnchorReq) ProtoMessage()    {}
func (*GetAgentAnchorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{19}
}
func (m *GetAgentAnchorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAgentAnchorReq.Unmarshal(m, b)
}
func (m *GetAgentAnchorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAgentAnchorReq.Marshal(b, m, deterministic)
}
func (dst *GetAgentAnchorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAgentAnchorReq.Merge(dst, src)
}
func (m *GetAgentAnchorReq) XXX_Size() int {
	return xxx_messageInfo_GetAgentAnchorReq.Size(m)
}
func (m *GetAgentAnchorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAgentAnchorReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAgentAnchorReq proto.InternalMessageInfo

func (m *GetAgentAnchorReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *GetAgentAnchorReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetAgentAnchorResp struct {
	Info                 *AgentAnchorInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAgentAnchorResp) Reset()         { *m = GetAgentAnchorResp{} }
func (m *GetAgentAnchorResp) String() string { return proto.CompactTextString(m) }
func (*GetAgentAnchorResp) ProtoMessage()    {}
func (*GetAgentAnchorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{20}
}
func (m *GetAgentAnchorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAgentAnchorResp.Unmarshal(m, b)
}
func (m *GetAgentAnchorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAgentAnchorResp.Marshal(b, m, deterministic)
}
func (dst *GetAgentAnchorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAgentAnchorResp.Merge(dst, src)
}
func (m *GetAgentAnchorResp) XXX_Size() int {
	return xxx_messageInfo_GetAgentAnchorResp.Size(m)
}
func (m *GetAgentAnchorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAgentAnchorResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAgentAnchorResp proto.InternalMessageInfo

func (m *GetAgentAnchorResp) GetInfo() *AgentAnchorInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取主播的经纪人信息
type GetAnchorAgentReq struct {
	AnchorUid            uint32   `protobuf:"varint,1,opt,name=anchor_uid,json=anchorUid,proto3" json:"anchor_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetAnchorAgentReq) Reset()         { *m = GetAnchorAgentReq{} }
func (m *GetAnchorAgentReq) String() string { return proto.CompactTextString(m) }
func (*GetAnchorAgentReq) ProtoMessage()    {}
func (*GetAnchorAgentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{21}
}
func (m *GetAnchorAgentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorAgentReq.Unmarshal(m, b)
}
func (m *GetAnchorAgentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorAgentReq.Marshal(b, m, deterministic)
}
func (dst *GetAnchorAgentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorAgentReq.Merge(dst, src)
}
func (m *GetAnchorAgentReq) XXX_Size() int {
	return xxx_messageInfo_GetAnchorAgentReq.Size(m)
}
func (m *GetAnchorAgentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorAgentReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorAgentReq proto.InternalMessageInfo

func (m *GetAnchorAgentReq) GetAnchorUid() uint32 {
	if m != nil {
		return m.AnchorUid
	}
	return 0
}

type GetAnchorAgentResp struct {
	Info                 *AgentAnchorInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-"`
	XXX_unrecognized     []byte           `json:"-"`
	XXX_sizecache        int32            `json:"-"`
}

func (m *GetAnchorAgentResp) Reset()         { *m = GetAnchorAgentResp{} }
func (m *GetAnchorAgentResp) String() string { return proto.CompactTextString(m) }
func (*GetAnchorAgentResp) ProtoMessage()    {}
func (*GetAnchorAgentResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{22}
}
func (m *GetAnchorAgentResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAnchorAgentResp.Unmarshal(m, b)
}
func (m *GetAnchorAgentResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAnchorAgentResp.Marshal(b, m, deterministic)
}
func (dst *GetAnchorAgentResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAnchorAgentResp.Merge(dst, src)
}
func (m *GetAnchorAgentResp) XXX_Size() int {
	return xxx_messageInfo_GetAnchorAgentResp.Size(m)
}
func (m *GetAnchorAgentResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAnchorAgentResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAnchorAgentResp proto.InternalMessageInfo

func (m *GetAnchorAgentResp) GetInfo() *AgentAnchorInfo {
	if m != nil {
		return m.Info
	}
	return nil
}

type AnchorErrorMsg struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ErrMsg               string   `protobuf:"bytes,2,opt,name=err_msg,json=errMsg,proto3" json:"err_msg,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AnchorErrorMsg) Reset()         { *m = AnchorErrorMsg{} }
func (m *AnchorErrorMsg) String() string { return proto.CompactTextString(m) }
func (*AnchorErrorMsg) ProtoMessage()    {}
func (*AnchorErrorMsg) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{23}
}
func (m *AnchorErrorMsg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AnchorErrorMsg.Unmarshal(m, b)
}
func (m *AnchorErrorMsg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AnchorErrorMsg.Marshal(b, m, deterministic)
}
func (dst *AnchorErrorMsg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AnchorErrorMsg.Merge(dst, src)
}
func (m *AnchorErrorMsg) XXX_Size() int {
	return xxx_messageInfo_AnchorErrorMsg.Size(m)
}
func (m *AnchorErrorMsg) XXX_DiscardUnknown() {
	xxx_messageInfo_AnchorErrorMsg.DiscardUnknown(m)
}

var xxx_messageInfo_AnchorErrorMsg proto.InternalMessageInfo

func (m *AnchorErrorMsg) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AnchorErrorMsg) GetErrMsg() string {
	if m != nil {
		return m.ErrMsg
	}
	return ""
}

// 增加经纪人管理主播
type AddAgentAnchorReq struct {
	AgentUid             uint32   `protobuf:"varint,1,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddAgentAnchorReq) Reset()         { *m = AddAgentAnchorReq{} }
func (m *AddAgentAnchorReq) String() string { return proto.CompactTextString(m) }
func (*AddAgentAnchorReq) ProtoMessage()    {}
func (*AddAgentAnchorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{24}
}
func (m *AddAgentAnchorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAgentAnchorReq.Unmarshal(m, b)
}
func (m *AddAgentAnchorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAgentAnchorReq.Marshal(b, m, deterministic)
}
func (dst *AddAgentAnchorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAgentAnchorReq.Merge(dst, src)
}
func (m *AddAgentAnchorReq) XXX_Size() int {
	return xxx_messageInfo_AddAgentAnchorReq.Size(m)
}
func (m *AddAgentAnchorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAgentAnchorReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddAgentAnchorReq proto.InternalMessageInfo

func (m *AddAgentAnchorReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *AddAgentAnchorReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *AddAgentAnchorReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type AddAgentAnchorResp struct {
	ErrList              []*AnchorErrorMsg `protobuf:"bytes,1,rep,name=err_list,json=errList,proto3" json:"err_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *AddAgentAnchorResp) Reset()         { *m = AddAgentAnchorResp{} }
func (m *AddAgentAnchorResp) String() string { return proto.CompactTextString(m) }
func (*AddAgentAnchorResp) ProtoMessage()    {}
func (*AddAgentAnchorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{25}
}
func (m *AddAgentAnchorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddAgentAnchorResp.Unmarshal(m, b)
}
func (m *AddAgentAnchorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddAgentAnchorResp.Marshal(b, m, deterministic)
}
func (dst *AddAgentAnchorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddAgentAnchorResp.Merge(dst, src)
}
func (m *AddAgentAnchorResp) XXX_Size() int {
	return xxx_messageInfo_AddAgentAnchorResp.Size(m)
}
func (m *AddAgentAnchorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddAgentAnchorResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddAgentAnchorResp proto.InternalMessageInfo

func (m *AddAgentAnchorResp) GetErrList() []*AnchorErrorMsg {
	if m != nil {
		return m.ErrList
	}
	return nil
}

// 删除经纪人主播
type DelAgentAnchorReq struct {
	AgentUid             uint32   `protobuf:"varint,1,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAgentAnchorReq) Reset()         { *m = DelAgentAnchorReq{} }
func (m *DelAgentAnchorReq) String() string { return proto.CompactTextString(m) }
func (*DelAgentAnchorReq) ProtoMessage()    {}
func (*DelAgentAnchorReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{26}
}
func (m *DelAgentAnchorReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAgentAnchorReq.Unmarshal(m, b)
}
func (m *DelAgentAnchorReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAgentAnchorReq.Marshal(b, m, deterministic)
}
func (dst *DelAgentAnchorReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAgentAnchorReq.Merge(dst, src)
}
func (m *DelAgentAnchorReq) XXX_Size() int {
	return xxx_messageInfo_DelAgentAnchorReq.Size(m)
}
func (m *DelAgentAnchorReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAgentAnchorReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelAgentAnchorReq proto.InternalMessageInfo

func (m *DelAgentAnchorReq) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *DelAgentAnchorReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DelAgentAnchorReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type DelAgentAnchorResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelAgentAnchorResp) Reset()         { *m = DelAgentAnchorResp{} }
func (m *DelAgentAnchorResp) String() string { return proto.CompactTextString(m) }
func (*DelAgentAnchorResp) ProtoMessage()    {}
func (*DelAgentAnchorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{27}
}
func (m *DelAgentAnchorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelAgentAnchorResp.Unmarshal(m, b)
}
func (m *DelAgentAnchorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelAgentAnchorResp.Marshal(b, m, deterministic)
}
func (dst *DelAgentAnchorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelAgentAnchorResp.Merge(dst, src)
}
func (m *DelAgentAnchorResp) XXX_Size() int {
	return xxx_messageInfo_DelAgentAnchorResp.Size(m)
}
func (m *DelAgentAnchorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelAgentAnchorResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelAgentAnchorResp proto.InternalMessageInfo

// 校验是否需要拉起验证
type CheckIsNeedVerifyReq struct {
	Scene                uint32   `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIsNeedVerifyReq) Reset()         { *m = CheckIsNeedVerifyReq{} }
func (m *CheckIsNeedVerifyReq) String() string { return proto.CompactTextString(m) }
func (*CheckIsNeedVerifyReq) ProtoMessage()    {}
func (*CheckIsNeedVerifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{28}
}
func (m *CheckIsNeedVerifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIsNeedVerifyReq.Unmarshal(m, b)
}
func (m *CheckIsNeedVerifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIsNeedVerifyReq.Marshal(b, m, deterministic)
}
func (dst *CheckIsNeedVerifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIsNeedVerifyReq.Merge(dst, src)
}
func (m *CheckIsNeedVerifyReq) XXX_Size() int {
	return xxx_messageInfo_CheckIsNeedVerifyReq.Size(m)
}
func (m *CheckIsNeedVerifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIsNeedVerifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIsNeedVerifyReq proto.InternalMessageInfo

func (m *CheckIsNeedVerifyReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *CheckIsNeedVerifyReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type CheckIsNeedVerifyResp struct {
	IsNeed               bool     `protobuf:"varint,1,opt,name=is_need,json=isNeed,proto3" json:"is_need,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckIsNeedVerifyResp) Reset()         { *m = CheckIsNeedVerifyResp{} }
func (m *CheckIsNeedVerifyResp) String() string { return proto.CompactTextString(m) }
func (*CheckIsNeedVerifyResp) ProtoMessage()    {}
func (*CheckIsNeedVerifyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{29}
}
func (m *CheckIsNeedVerifyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckIsNeedVerifyResp.Unmarshal(m, b)
}
func (m *CheckIsNeedVerifyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckIsNeedVerifyResp.Marshal(b, m, deterministic)
}
func (dst *CheckIsNeedVerifyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckIsNeedVerifyResp.Merge(dst, src)
}
func (m *CheckIsNeedVerifyResp) XXX_Size() int {
	return xxx_messageInfo_CheckIsNeedVerifyResp.Size(m)
}
func (m *CheckIsNeedVerifyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckIsNeedVerifyResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckIsNeedVerifyResp proto.InternalMessageInfo

func (m *CheckIsNeedVerifyResp) GetIsNeed() bool {
	if m != nil {
		return m.IsNeed
	}
	return false
}

// 设置验证成功标识
type SetVerifyFlagReq struct {
	Scene                uint32   `protobuf:"varint,1,opt,name=scene,proto3" json:"scene,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVerifyFlagReq) Reset()         { *m = SetVerifyFlagReq{} }
func (m *SetVerifyFlagReq) String() string { return proto.CompactTextString(m) }
func (*SetVerifyFlagReq) ProtoMessage()    {}
func (*SetVerifyFlagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{30}
}
func (m *SetVerifyFlagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVerifyFlagReq.Unmarshal(m, b)
}
func (m *SetVerifyFlagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVerifyFlagReq.Marshal(b, m, deterministic)
}
func (dst *SetVerifyFlagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVerifyFlagReq.Merge(dst, src)
}
func (m *SetVerifyFlagReq) XXX_Size() int {
	return xxx_messageInfo_SetVerifyFlagReq.Size(m)
}
func (m *SetVerifyFlagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVerifyFlagReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetVerifyFlagReq proto.InternalMessageInfo

func (m *SetVerifyFlagReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *SetVerifyFlagReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type SetVerifyFlagResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetVerifyFlagResp) Reset()         { *m = SetVerifyFlagResp{} }
func (m *SetVerifyFlagResp) String() string { return proto.CompactTextString(m) }
func (*SetVerifyFlagResp) ProtoMessage()    {}
func (*SetVerifyFlagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{31}
}
func (m *SetVerifyFlagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetVerifyFlagResp.Unmarshal(m, b)
}
func (m *SetVerifyFlagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetVerifyFlagResp.Marshal(b, m, deterministic)
}
func (dst *SetVerifyFlagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetVerifyFlagResp.Merge(dst, src)
}
func (m *SetVerifyFlagResp) XXX_Size() int {
	return xxx_messageInfo_SetVerifyFlagResp.Size(m)
}
func (m *SetVerifyFlagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetVerifyFlagResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetVerifyFlagResp proto.InternalMessageInfo

// EsportPractitionerMonthly 电竞从业者月信息
type EsportPractitionerMonthly struct {
	DateTime             uint32   `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderAmt             uint32   `protobuf:"varint,4,opt,name=order_amt,json=orderAmt,proto3" json:"order_amt,omitempty"`
	OrderCnt             uint32   `protobuf:"varint,5,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	OrderDayCnt          uint32   `protobuf:"varint,6,opt,name=order_day_cnt,json=orderDayCnt,proto3" json:"order_day_cnt,omitempty"`
	ActiveDayCnt         uint32   `protobuf:"varint,7,opt,name=active_day_cnt,json=activeDayCnt,proto3" json:"active_day_cnt,omitempty"`
	ServedUserCnt        uint32   `protobuf:"varint,8,opt,name=served_user_cnt,json=servedUserCnt,proto3" json:"served_user_cnt,omitempty"`
	NewPayUserCnt        uint32   `protobuf:"varint,9,opt,name=new_pay_user_cnt,json=newPayUserCnt,proto3" json:"new_pay_user_cnt,omitempty"`
	RepayUserCnt         uint32   `protobuf:"varint,10,opt,name=repay_user_cnt,json=repayUserCnt,proto3" json:"repay_user_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportPractitionerMonthly) Reset()         { *m = EsportPractitionerMonthly{} }
func (m *EsportPractitionerMonthly) String() string { return proto.CompactTextString(m) }
func (*EsportPractitionerMonthly) ProtoMessage()    {}
func (*EsportPractitionerMonthly) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{32}
}
func (m *EsportPractitionerMonthly) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportPractitionerMonthly.Unmarshal(m, b)
}
func (m *EsportPractitionerMonthly) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportPractitionerMonthly.Marshal(b, m, deterministic)
}
func (dst *EsportPractitionerMonthly) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportPractitionerMonthly.Merge(dst, src)
}
func (m *EsportPractitionerMonthly) XXX_Size() int {
	return xxx_messageInfo_EsportPractitionerMonthly.Size(m)
}
func (m *EsportPractitionerMonthly) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportPractitionerMonthly.DiscardUnknown(m)
}

var xxx_messageInfo_EsportPractitionerMonthly proto.InternalMessageInfo

func (m *EsportPractitionerMonthly) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetOrderAmt() uint32 {
	if m != nil {
		return m.OrderAmt
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetOrderDayCnt() uint32 {
	if m != nil {
		return m.OrderDayCnt
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetActiveDayCnt() uint32 {
	if m != nil {
		return m.ActiveDayCnt
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetServedUserCnt() uint32 {
	if m != nil {
		return m.ServedUserCnt
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetNewPayUserCnt() uint32 {
	if m != nil {
		return m.NewPayUserCnt
	}
	return 0
}

func (m *EsportPractitionerMonthly) GetRepayUserCnt() uint32 {
	if m != nil {
		return m.RepayUserCnt
	}
	return 0
}

// EsportPractitionerDaily 电竞从业者日信息
type EsportPractitionerDaily struct {
	DateTime             uint32   `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,3,opt,name=uid,proto3" json:"uid,omitempty"`
	OrderAmt             uint32   `protobuf:"varint,4,opt,name=order_amt,json=orderAmt,proto3" json:"order_amt,omitempty"`
	OrderCnt             uint32   `protobuf:"varint,5,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	ServedUserCnt        uint32   `protobuf:"varint,6,opt,name=served_user_cnt,json=servedUserCnt,proto3" json:"served_user_cnt,omitempty"`
	NewPayUserCnt        uint32   `protobuf:"varint,7,opt,name=new_pay_user_cnt,json=newPayUserCnt,proto3" json:"new_pay_user_cnt,omitempty"`
	NewRepayUserCnt      uint32   `protobuf:"varint,8,opt,name=new_repay_user_cnt,json=newRepayUserCnt,proto3" json:"new_repay_user_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportPractitionerDaily) Reset()         { *m = EsportPractitionerDaily{} }
func (m *EsportPractitionerDaily) String() string { return proto.CompactTextString(m) }
func (*EsportPractitionerDaily) ProtoMessage()    {}
func (*EsportPractitionerDaily) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{33}
}
func (m *EsportPractitionerDaily) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportPractitionerDaily.Unmarshal(m, b)
}
func (m *EsportPractitionerDaily) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportPractitionerDaily.Marshal(b, m, deterministic)
}
func (dst *EsportPractitionerDaily) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportPractitionerDaily.Merge(dst, src)
}
func (m *EsportPractitionerDaily) XXX_Size() int {
	return xxx_messageInfo_EsportPractitionerDaily.Size(m)
}
func (m *EsportPractitionerDaily) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportPractitionerDaily.DiscardUnknown(m)
}

var xxx_messageInfo_EsportPractitionerDaily proto.InternalMessageInfo

func (m *EsportPractitionerDaily) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *EsportPractitionerDaily) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportPractitionerDaily) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *EsportPractitionerDaily) GetOrderAmt() uint32 {
	if m != nil {
		return m.OrderAmt
	}
	return 0
}

func (m *EsportPractitionerDaily) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *EsportPractitionerDaily) GetServedUserCnt() uint32 {
	if m != nil {
		return m.ServedUserCnt
	}
	return 0
}

func (m *EsportPractitionerDaily) GetNewPayUserCnt() uint32 {
	if m != nil {
		return m.NewPayUserCnt
	}
	return 0
}

func (m *EsportPractitionerDaily) GetNewRepayUserCnt() uint32 {
	if m != nil {
		return m.NewRepayUserCnt
	}
	return 0
}

// EsportGameMonthlyStat 电竞游戏月统计信息
type EsportGameMonthlyStat struct {
	DateTime             uint32   `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GameId               uint32   `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	OrderAmt             uint32   `protobuf:"varint,4,opt,name=order_amt,json=orderAmt,proto3" json:"order_amt,omitempty"`
	OrderCnt             uint32   `protobuf:"varint,5,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	CoachCnt             uint32   `protobuf:"varint,6,opt,name=coach_cnt,json=coachCnt,proto3" json:"coach_cnt,omitempty"`
	TakeOrdersCoachCnt   uint32   `protobuf:"varint,7,opt,name=take_orders_coach_cnt,json=takeOrdersCoachCnt,proto3" json:"take_orders_coach_cnt,omitempty"`
	ServedUserCnt        uint32   `protobuf:"varint,8,opt,name=served_user_cnt,json=servedUserCnt,proto3" json:"served_user_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportGameMonthlyStat) Reset()         { *m = EsportGameMonthlyStat{} }
func (m *EsportGameMonthlyStat) String() string { return proto.CompactTextString(m) }
func (*EsportGameMonthlyStat) ProtoMessage()    {}
func (*EsportGameMonthlyStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{34}
}
func (m *EsportGameMonthlyStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportGameMonthlyStat.Unmarshal(m, b)
}
func (m *EsportGameMonthlyStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportGameMonthlyStat.Marshal(b, m, deterministic)
}
func (dst *EsportGameMonthlyStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportGameMonthlyStat.Merge(dst, src)
}
func (m *EsportGameMonthlyStat) XXX_Size() int {
	return xxx_messageInfo_EsportGameMonthlyStat.Size(m)
}
func (m *EsportGameMonthlyStat) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportGameMonthlyStat.DiscardUnknown(m)
}

var xxx_messageInfo_EsportGameMonthlyStat proto.InternalMessageInfo

func (m *EsportGameMonthlyStat) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetOrderAmt() uint32 {
	if m != nil {
		return m.OrderAmt
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetCoachCnt() uint32 {
	if m != nil {
		return m.CoachCnt
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetTakeOrdersCoachCnt() uint32 {
	if m != nil {
		return m.TakeOrdersCoachCnt
	}
	return 0
}

func (m *EsportGameMonthlyStat) GetServedUserCnt() uint32 {
	if m != nil {
		return m.ServedUserCnt
	}
	return 0
}

type EsportGuildMonthlyStat struct {
	DateTime             uint32   `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	OrderAmt             uint32   `protobuf:"varint,3,opt,name=order_amt,json=orderAmt,proto3" json:"order_amt,omitempty"`
	OrderCnt             uint32   `protobuf:"varint,4,opt,name=order_cnt,json=orderCnt,proto3" json:"order_cnt,omitempty"`
	ServedUserCnt        uint32   `protobuf:"varint,5,opt,name=served_user_cnt,json=servedUserCnt,proto3" json:"served_user_cnt,omitempty"`
	CoachCnt             uint32   `protobuf:"varint,6,opt,name=coach_cnt,json=coachCnt,proto3" json:"coach_cnt,omitempty"`
	NewCoachCnt          uint32   `protobuf:"varint,7,opt,name=new_coach_cnt,json=newCoachCnt,proto3" json:"new_coach_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportGuildMonthlyStat) Reset()         { *m = EsportGuildMonthlyStat{} }
func (m *EsportGuildMonthlyStat) String() string { return proto.CompactTextString(m) }
func (*EsportGuildMonthlyStat) ProtoMessage()    {}
func (*EsportGuildMonthlyStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{35}
}
func (m *EsportGuildMonthlyStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportGuildMonthlyStat.Unmarshal(m, b)
}
func (m *EsportGuildMonthlyStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportGuildMonthlyStat.Marshal(b, m, deterministic)
}
func (dst *EsportGuildMonthlyStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportGuildMonthlyStat.Merge(dst, src)
}
func (m *EsportGuildMonthlyStat) XXX_Size() int {
	return xxx_messageInfo_EsportGuildMonthlyStat.Size(m)
}
func (m *EsportGuildMonthlyStat) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportGuildMonthlyStat.DiscardUnknown(m)
}

var xxx_messageInfo_EsportGuildMonthlyStat proto.InternalMessageInfo

func (m *EsportGuildMonthlyStat) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *EsportGuildMonthlyStat) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportGuildMonthlyStat) GetOrderAmt() uint32 {
	if m != nil {
		return m.OrderAmt
	}
	return 0
}

func (m *EsportGuildMonthlyStat) GetOrderCnt() uint32 {
	if m != nil {
		return m.OrderCnt
	}
	return 0
}

func (m *EsportGuildMonthlyStat) GetServedUserCnt() uint32 {
	if m != nil {
		return m.ServedUserCnt
	}
	return 0
}

func (m *EsportGuildMonthlyStat) GetCoachCnt() uint32 {
	if m != nil {
		return m.CoachCnt
	}
	return 0
}

func (m *EsportGuildMonthlyStat) GetNewCoachCnt() uint32 {
	if m != nil {
		return m.NewCoachCnt
	}
	return 0
}

type EsportGuildDailyStat struct {
	DateTime             uint32   `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	IncomeAmt            uint32   `protobuf:"varint,3,opt,name=income_amt,json=incomeAmt,proto3" json:"income_amt,omitempty"`
	OrderAmt             uint32   `protobuf:"varint,4,opt,name=order_amt,json=orderAmt,proto3" json:"order_amt,omitempty"`
	LstDIncomeAmt        uint32   `protobuf:"varint,5,opt,name=lst_d_income_amt,json=lstDIncomeAmt,proto3" json:"lst_d_income_amt,omitempty"`
	LstDOrderAmt         uint32   `protobuf:"varint,6,opt,name=lst_d_order_amt,json=lstDOrderAmt,proto3" json:"lst_d_order_amt,omitempty"`
	IncomeAmtDod         float64  `protobuf:"fixed64,7,opt,name=income_amt_dod,json=incomeAmtDod,proto3" json:"income_amt_dod,omitempty"`
	OrderAmtDod          float64  `protobuf:"fixed64,8,opt,name=order_amt_dod,json=orderAmtDod,proto3" json:"order_amt_dod,omitempty"`
	LstMIncomeAmt        uint32   `protobuf:"varint,9,opt,name=lst_m_income_amt,json=lstMIncomeAmt,proto3" json:"lst_m_income_amt,omitempty"`
	LstMOrderAmt         uint32   `protobuf:"varint,10,opt,name=lst_m_order_amt,json=lstMOrderAmt,proto3" json:"lst_m_order_amt,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EsportGuildDailyStat) Reset()         { *m = EsportGuildDailyStat{} }
func (m *EsportGuildDailyStat) String() string { return proto.CompactTextString(m) }
func (*EsportGuildDailyStat) ProtoMessage()    {}
func (*EsportGuildDailyStat) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{36}
}
func (m *EsportGuildDailyStat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EsportGuildDailyStat.Unmarshal(m, b)
}
func (m *EsportGuildDailyStat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EsportGuildDailyStat.Marshal(b, m, deterministic)
}
func (dst *EsportGuildDailyStat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EsportGuildDailyStat.Merge(dst, src)
}
func (m *EsportGuildDailyStat) XXX_Size() int {
	return xxx_messageInfo_EsportGuildDailyStat.Size(m)
}
func (m *EsportGuildDailyStat) XXX_DiscardUnknown() {
	xxx_messageInfo_EsportGuildDailyStat.DiscardUnknown(m)
}

var xxx_messageInfo_EsportGuildDailyStat proto.InternalMessageInfo

func (m *EsportGuildDailyStat) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *EsportGuildDailyStat) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *EsportGuildDailyStat) GetIncomeAmt() uint32 {
	if m != nil {
		return m.IncomeAmt
	}
	return 0
}

func (m *EsportGuildDailyStat) GetOrderAmt() uint32 {
	if m != nil {
		return m.OrderAmt
	}
	return 0
}

func (m *EsportGuildDailyStat) GetLstDIncomeAmt() uint32 {
	if m != nil {
		return m.LstDIncomeAmt
	}
	return 0
}

func (m *EsportGuildDailyStat) GetLstDOrderAmt() uint32 {
	if m != nil {
		return m.LstDOrderAmt
	}
	return 0
}

func (m *EsportGuildDailyStat) GetIncomeAmtDod() float64 {
	if m != nil {
		return m.IncomeAmtDod
	}
	return 0
}

func (m *EsportGuildDailyStat) GetOrderAmtDod() float64 {
	if m != nil {
		return m.OrderAmtDod
	}
	return 0
}

func (m *EsportGuildDailyStat) GetLstMIncomeAmt() uint32 {
	if m != nil {
		return m.LstMIncomeAmt
	}
	return 0
}

func (m *EsportGuildDailyStat) GetLstMOrderAmt() uint32 {
	if m != nil {
		return m.LstMOrderAmt
	}
	return 0
}

// 获取电竞从业者日信息列表 (取左右闭区间)
type GetEsportPractitionerDailyReq struct {
	BeginDateTime        uint32   `protobuf:"varint,1,opt,name=begin_date_time,json=beginDateTime,proto3" json:"begin_date_time,omitempty"`
	EndDateTime          uint32   `protobuf:"varint,2,opt,name=end_date_time,json=endDateTime,proto3" json:"end_date_time,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	GuildList            []uint32 `protobuf:"varint,6,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportPractitionerDailyReq) Reset()         { *m = GetEsportPractitionerDailyReq{} }
func (m *GetEsportPractitionerDailyReq) String() string { return proto.CompactTextString(m) }
func (*GetEsportPractitionerDailyReq) ProtoMessage()    {}
func (*GetEsportPractitionerDailyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{37}
}
func (m *GetEsportPractitionerDailyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportPractitionerDailyReq.Unmarshal(m, b)
}
func (m *GetEsportPractitionerDailyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportPractitionerDailyReq.Marshal(b, m, deterministic)
}
func (dst *GetEsportPractitionerDailyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportPractitionerDailyReq.Merge(dst, src)
}
func (m *GetEsportPractitionerDailyReq) XXX_Size() int {
	return xxx_messageInfo_GetEsportPractitionerDailyReq.Size(m)
}
func (m *GetEsportPractitionerDailyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportPractitionerDailyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportPractitionerDailyReq proto.InternalMessageInfo

func (m *GetEsportPractitionerDailyReq) GetBeginDateTime() uint32 {
	if m != nil {
		return m.BeginDateTime
	}
	return 0
}

func (m *GetEsportPractitionerDailyReq) GetEndDateTime() uint32 {
	if m != nil {
		return m.EndDateTime
	}
	return 0
}

func (m *GetEsportPractitionerDailyReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetEsportPractitionerDailyReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetEsportPractitionerDailyReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetEsportPractitionerDailyReq) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

type GetEsportPractitionerDailyResp struct {
	InfoList             []*EsportPractitionerDaily `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                     `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-"`
	XXX_unrecognized     []byte                     `json:"-"`
	XXX_sizecache        int32                      `json:"-"`
}

func (m *GetEsportPractitionerDailyResp) Reset()         { *m = GetEsportPractitionerDailyResp{} }
func (m *GetEsportPractitionerDailyResp) String() string { return proto.CompactTextString(m) }
func (*GetEsportPractitionerDailyResp) ProtoMessage()    {}
func (*GetEsportPractitionerDailyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{38}
}
func (m *GetEsportPractitionerDailyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportPractitionerDailyResp.Unmarshal(m, b)
}
func (m *GetEsportPractitionerDailyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportPractitionerDailyResp.Marshal(b, m, deterministic)
}
func (dst *GetEsportPractitionerDailyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportPractitionerDailyResp.Merge(dst, src)
}
func (m *GetEsportPractitionerDailyResp) XXX_Size() int {
	return xxx_messageInfo_GetEsportPractitionerDailyResp.Size(m)
}
func (m *GetEsportPractitionerDailyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportPractitionerDailyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportPractitionerDailyResp proto.InternalMessageInfo

func (m *GetEsportPractitionerDailyResp) GetInfoList() []*EsportPractitionerDaily {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetEsportPractitionerDailyResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取电竞从业者月信息列表 (取左右闭区间)
type GetEsportPractitionerMonthlyReq struct {
	BeginDateTime        uint32   `protobuf:"varint,1,opt,name=begin_date_time,json=beginDateTime,proto3" json:"begin_date_time,omitempty"`
	EndDateTime          uint32   `protobuf:"varint,2,opt,name=end_date_time,json=endDateTime,proto3" json:"end_date_time,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	GuildList            []uint32 `protobuf:"varint,6,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportPractitionerMonthlyReq) Reset()         { *m = GetEsportPractitionerMonthlyReq{} }
func (m *GetEsportPractitionerMonthlyReq) String() string { return proto.CompactTextString(m) }
func (*GetEsportPractitionerMonthlyReq) ProtoMessage()    {}
func (*GetEsportPractitionerMonthlyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{39}
}
func (m *GetEsportPractitionerMonthlyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportPractitionerMonthlyReq.Unmarshal(m, b)
}
func (m *GetEsportPractitionerMonthlyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportPractitionerMonthlyReq.Marshal(b, m, deterministic)
}
func (dst *GetEsportPractitionerMonthlyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportPractitionerMonthlyReq.Merge(dst, src)
}
func (m *GetEsportPractitionerMonthlyReq) XXX_Size() int {
	return xxx_messageInfo_GetEsportPractitionerMonthlyReq.Size(m)
}
func (m *GetEsportPractitionerMonthlyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportPractitionerMonthlyReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportPractitionerMonthlyReq proto.InternalMessageInfo

func (m *GetEsportPractitionerMonthlyReq) GetBeginDateTime() uint32 {
	if m != nil {
		return m.BeginDateTime
	}
	return 0
}

func (m *GetEsportPractitionerMonthlyReq) GetEndDateTime() uint32 {
	if m != nil {
		return m.EndDateTime
	}
	return 0
}

func (m *GetEsportPractitionerMonthlyReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetEsportPractitionerMonthlyReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetEsportPractitionerMonthlyReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *GetEsportPractitionerMonthlyReq) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

type GetEsportPractitionerMonthlyResp struct {
	InfoList             []*EsportPractitionerMonthly `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-"`
	XXX_unrecognized     []byte                       `json:"-"`
	XXX_sizecache        int32                        `json:"-"`
}

func (m *GetEsportPractitionerMonthlyResp) Reset()         { *m = GetEsportPractitionerMonthlyResp{} }
func (m *GetEsportPractitionerMonthlyResp) String() string { return proto.CompactTextString(m) }
func (*GetEsportPractitionerMonthlyResp) ProtoMessage()    {}
func (*GetEsportPractitionerMonthlyResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{40}
}
func (m *GetEsportPractitionerMonthlyResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportPractitionerMonthlyResp.Unmarshal(m, b)
}
func (m *GetEsportPractitionerMonthlyResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportPractitionerMonthlyResp.Marshal(b, m, deterministic)
}
func (dst *GetEsportPractitionerMonthlyResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportPractitionerMonthlyResp.Merge(dst, src)
}
func (m *GetEsportPractitionerMonthlyResp) XXX_Size() int {
	return xxx_messageInfo_GetEsportPractitionerMonthlyResp.Size(m)
}
func (m *GetEsportPractitionerMonthlyResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportPractitionerMonthlyResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportPractitionerMonthlyResp proto.InternalMessageInfo

func (m *GetEsportPractitionerMonthlyResp) GetInfoList() []*EsportPractitionerMonthly {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetEsportPractitionerMonthlyResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取电竞游戏月统计信息 (取左右闭区间)
type GetEsportGameMonthlyStatReq struct {
	BeginDateTime        uint32   `protobuf:"varint,1,opt,name=begin_date_time,json=beginDateTime,proto3" json:"begin_date_time,omitempty"`
	EndDateTime          uint32   `protobuf:"varint,2,opt,name=end_date_time,json=endDateTime,proto3" json:"end_date_time,omitempty"`
	GuildList            []uint32 `protobuf:"varint,3,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	GameId               []uint32 `protobuf:"varint,4,rep,packed,name=game_id,json=gameId,proto3" json:"game_id,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGameMonthlyStatReq) Reset()         { *m = GetEsportGameMonthlyStatReq{} }
func (m *GetEsportGameMonthlyStatReq) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameMonthlyStatReq) ProtoMessage()    {}
func (*GetEsportGameMonthlyStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{41}
}
func (m *GetEsportGameMonthlyStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameMonthlyStatReq.Unmarshal(m, b)
}
func (m *GetEsportGameMonthlyStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameMonthlyStatReq.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameMonthlyStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameMonthlyStatReq.Merge(dst, src)
}
func (m *GetEsportGameMonthlyStatReq) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameMonthlyStatReq.Size(m)
}
func (m *GetEsportGameMonthlyStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameMonthlyStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameMonthlyStatReq proto.InternalMessageInfo

func (m *GetEsportGameMonthlyStatReq) GetBeginDateTime() uint32 {
	if m != nil {
		return m.BeginDateTime
	}
	return 0
}

func (m *GetEsportGameMonthlyStatReq) GetEndDateTime() uint32 {
	if m != nil {
		return m.EndDateTime
	}
	return 0
}

func (m *GetEsportGameMonthlyStatReq) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

func (m *GetEsportGameMonthlyStatReq) GetGameId() []uint32 {
	if m != nil {
		return m.GameId
	}
	return nil
}

func (m *GetEsportGameMonthlyStatReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetEsportGameMonthlyStatReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetEsportGameMonthlyStatResp struct {
	InfoList             []*EsportGameMonthlyStat `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-"`
	XXX_unrecognized     []byte                   `json:"-"`
	XXX_sizecache        int32                    `json:"-"`
}

func (m *GetEsportGameMonthlyStatResp) Reset()         { *m = GetEsportGameMonthlyStatResp{} }
func (m *GetEsportGameMonthlyStatResp) String() string { return proto.CompactTextString(m) }
func (*GetEsportGameMonthlyStatResp) ProtoMessage()    {}
func (*GetEsportGameMonthlyStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{42}
}
func (m *GetEsportGameMonthlyStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGameMonthlyStatResp.Unmarshal(m, b)
}
func (m *GetEsportGameMonthlyStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGameMonthlyStatResp.Marshal(b, m, deterministic)
}
func (dst *GetEsportGameMonthlyStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGameMonthlyStatResp.Merge(dst, src)
}
func (m *GetEsportGameMonthlyStatResp) XXX_Size() int {
	return xxx_messageInfo_GetEsportGameMonthlyStatResp.Size(m)
}
func (m *GetEsportGameMonthlyStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGameMonthlyStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGameMonthlyStatResp proto.InternalMessageInfo

func (m *GetEsportGameMonthlyStatResp) GetInfoList() []*EsportGameMonthlyStat {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetEsportGameMonthlyStatResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取电竞公会月统计信息 (取左右闭区间)
type GetEsportGuildMonthlyStatReq struct {
	BeginDateTime        uint32   `protobuf:"varint,1,opt,name=begin_date_time,json=beginDateTime,proto3" json:"begin_date_time,omitempty"`
	EndDateTime          uint32   `protobuf:"varint,2,opt,name=end_date_time,json=endDateTime,proto3" json:"end_date_time,omitempty"`
	GuildList            []uint32 `protobuf:"varint,3,rep,packed,name=guild_list,json=guildList,proto3" json:"guild_list,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGuildMonthlyStatReq) Reset()         { *m = GetEsportGuildMonthlyStatReq{} }
func (m *GetEsportGuildMonthlyStatReq) String() string { return proto.CompactTextString(m) }
func (*GetEsportGuildMonthlyStatReq) ProtoMessage()    {}
func (*GetEsportGuildMonthlyStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{43}
}
func (m *GetEsportGuildMonthlyStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGuildMonthlyStatReq.Unmarshal(m, b)
}
func (m *GetEsportGuildMonthlyStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGuildMonthlyStatReq.Marshal(b, m, deterministic)
}
func (dst *GetEsportGuildMonthlyStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGuildMonthlyStatReq.Merge(dst, src)
}
func (m *GetEsportGuildMonthlyStatReq) XXX_Size() int {
	return xxx_messageInfo_GetEsportGuildMonthlyStatReq.Size(m)
}
func (m *GetEsportGuildMonthlyStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGuildMonthlyStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGuildMonthlyStatReq proto.InternalMessageInfo

func (m *GetEsportGuildMonthlyStatReq) GetBeginDateTime() uint32 {
	if m != nil {
		return m.BeginDateTime
	}
	return 0
}

func (m *GetEsportGuildMonthlyStatReq) GetEndDateTime() uint32 {
	if m != nil {
		return m.EndDateTime
	}
	return 0
}

func (m *GetEsportGuildMonthlyStatReq) GetGuildList() []uint32 {
	if m != nil {
		return m.GuildList
	}
	return nil
}

func (m *GetEsportGuildMonthlyStatReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetEsportGuildMonthlyStatReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetEsportGuildMonthlyStatResp struct {
	InfoList             []*EsportGuildMonthlyStat `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                    `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-"`
	XXX_unrecognized     []byte                    `json:"-"`
	XXX_sizecache        int32                     `json:"-"`
}

func (m *GetEsportGuildMonthlyStatResp) Reset()         { *m = GetEsportGuildMonthlyStatResp{} }
func (m *GetEsportGuildMonthlyStatResp) String() string { return proto.CompactTextString(m) }
func (*GetEsportGuildMonthlyStatResp) ProtoMessage()    {}
func (*GetEsportGuildMonthlyStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{44}
}
func (m *GetEsportGuildMonthlyStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGuildMonthlyStatResp.Unmarshal(m, b)
}
func (m *GetEsportGuildMonthlyStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGuildMonthlyStatResp.Marshal(b, m, deterministic)
}
func (dst *GetEsportGuildMonthlyStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGuildMonthlyStatResp.Merge(dst, src)
}
func (m *GetEsportGuildMonthlyStatResp) XXX_Size() int {
	return xxx_messageInfo_GetEsportGuildMonthlyStatResp.Size(m)
}
func (m *GetEsportGuildMonthlyStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGuildMonthlyStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGuildMonthlyStatResp proto.InternalMessageInfo

func (m *GetEsportGuildMonthlyStatResp) GetInfoList() []*EsportGuildMonthlyStat {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *GetEsportGuildMonthlyStatResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取电竞公会日统计信息
type GetEsportGuildDailyStatReq struct {
	DateTime             uint32   `protobuf:"varint,1,opt,name=date_time,json=dateTime,proto3" json:"date_time,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetEsportGuildDailyStatReq) Reset()         { *m = GetEsportGuildDailyStatReq{} }
func (m *GetEsportGuildDailyStatReq) String() string { return proto.CompactTextString(m) }
func (*GetEsportGuildDailyStatReq) ProtoMessage()    {}
func (*GetEsportGuildDailyStatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{45}
}
func (m *GetEsportGuildDailyStatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGuildDailyStatReq.Unmarshal(m, b)
}
func (m *GetEsportGuildDailyStatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGuildDailyStatReq.Marshal(b, m, deterministic)
}
func (dst *GetEsportGuildDailyStatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGuildDailyStatReq.Merge(dst, src)
}
func (m *GetEsportGuildDailyStatReq) XXX_Size() int {
	return xxx_messageInfo_GetEsportGuildDailyStatReq.Size(m)
}
func (m *GetEsportGuildDailyStatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGuildDailyStatReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGuildDailyStatReq proto.InternalMessageInfo

func (m *GetEsportGuildDailyStatReq) GetDateTime() uint32 {
	if m != nil {
		return m.DateTime
	}
	return 0
}

func (m *GetEsportGuildDailyStatReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetEsportGuildDailyStatResp struct {
	Info                 *EsportGuildDailyStat `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetEsportGuildDailyStatResp) Reset()         { *m = GetEsportGuildDailyStatResp{} }
func (m *GetEsportGuildDailyStatResp) String() string { return proto.CompactTextString(m) }
func (*GetEsportGuildDailyStatResp) ProtoMessage()    {}
func (*GetEsportGuildDailyStatResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{46}
}
func (m *GetEsportGuildDailyStatResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetEsportGuildDailyStatResp.Unmarshal(m, b)
}
func (m *GetEsportGuildDailyStatResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetEsportGuildDailyStatResp.Marshal(b, m, deterministic)
}
func (dst *GetEsportGuildDailyStatResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetEsportGuildDailyStatResp.Merge(dst, src)
}
func (m *GetEsportGuildDailyStatResp) XXX_Size() int {
	return xxx_messageInfo_GetEsportGuildDailyStatResp.Size(m)
}
func (m *GetEsportGuildDailyStatResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetEsportGuildDailyStatResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetEsportGuildDailyStatResp proto.InternalMessageInfo

func (m *GetEsportGuildDailyStatResp) GetInfo() *EsportGuildDailyStat {
	if m != nil {
		return m.Info
	}
	return nil
}

// 获取生成白鲸后台的access_token
type GetWhiteWhaleAccessTokenReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteWhaleAccessTokenReq) Reset()         { *m = GetWhiteWhaleAccessTokenReq{} }
func (m *GetWhiteWhaleAccessTokenReq) String() string { return proto.CompactTextString(m) }
func (*GetWhiteWhaleAccessTokenReq) ProtoMessage()    {}
func (*GetWhiteWhaleAccessTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{47}
}
func (m *GetWhiteWhaleAccessTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteWhaleAccessTokenReq.Unmarshal(m, b)
}
func (m *GetWhiteWhaleAccessTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteWhaleAccessTokenReq.Marshal(b, m, deterministic)
}
func (dst *GetWhiteWhaleAccessTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteWhaleAccessTokenReq.Merge(dst, src)
}
func (m *GetWhiteWhaleAccessTokenReq) XXX_Size() int {
	return xxx_messageInfo_GetWhiteWhaleAccessTokenReq.Size(m)
}
func (m *GetWhiteWhaleAccessTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteWhaleAccessTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteWhaleAccessTokenReq proto.InternalMessageInfo

func (m *GetWhiteWhaleAccessTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GetWhiteWhaleAccessTokenReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type GetWhiteWhaleAccessTokenResp struct {
	AccessToken          string   `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	ExpireTs             uint32   `protobuf:"varint,2,opt,name=expire_ts,json=expireTs,proto3" json:"expire_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetWhiteWhaleAccessTokenResp) Reset()         { *m = GetWhiteWhaleAccessTokenResp{} }
func (m *GetWhiteWhaleAccessTokenResp) String() string { return proto.CompactTextString(m) }
func (*GetWhiteWhaleAccessTokenResp) ProtoMessage()    {}
func (*GetWhiteWhaleAccessTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{48}
}
func (m *GetWhiteWhaleAccessTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetWhiteWhaleAccessTokenResp.Unmarshal(m, b)
}
func (m *GetWhiteWhaleAccessTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetWhiteWhaleAccessTokenResp.Marshal(b, m, deterministic)
}
func (dst *GetWhiteWhaleAccessTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetWhiteWhaleAccessTokenResp.Merge(dst, src)
}
func (m *GetWhiteWhaleAccessTokenResp) XXX_Size() int {
	return xxx_messageInfo_GetWhiteWhaleAccessTokenResp.Size(m)
}
func (m *GetWhiteWhaleAccessTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetWhiteWhaleAccessTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetWhiteWhaleAccessTokenResp proto.InternalMessageInfo

func (m *GetWhiteWhaleAccessTokenResp) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

func (m *GetWhiteWhaleAccessTokenResp) GetExpireTs() uint32 {
	if m != nil {
		return m.ExpireTs
	}
	return 0
}

// 校验白鲸后台的access_token
type CheckWhiteWhaleAccessTokenReq struct {
	AccessToken          string   `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32   `protobuf:"varint,3,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckWhiteWhaleAccessTokenReq) Reset()         { *m = CheckWhiteWhaleAccessTokenReq{} }
func (m *CheckWhiteWhaleAccessTokenReq) String() string { return proto.CompactTextString(m) }
func (*CheckWhiteWhaleAccessTokenReq) ProtoMessage()    {}
func (*CheckWhiteWhaleAccessTokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{49}
}
func (m *CheckWhiteWhaleAccessTokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckWhiteWhaleAccessTokenReq.Unmarshal(m, b)
}
func (m *CheckWhiteWhaleAccessTokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckWhiteWhaleAccessTokenReq.Marshal(b, m, deterministic)
}
func (dst *CheckWhiteWhaleAccessTokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckWhiteWhaleAccessTokenReq.Merge(dst, src)
}
func (m *CheckWhiteWhaleAccessTokenReq) XXX_Size() int {
	return xxx_messageInfo_CheckWhiteWhaleAccessTokenReq.Size(m)
}
func (m *CheckWhiteWhaleAccessTokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckWhiteWhaleAccessTokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_CheckWhiteWhaleAccessTokenReq proto.InternalMessageInfo

func (m *CheckWhiteWhaleAccessTokenReq) GetAccessToken() string {
	if m != nil {
		return m.AccessToken
	}
	return ""
}

func (m *CheckWhiteWhaleAccessTokenReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *CheckWhiteWhaleAccessTokenReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

type CheckWhiteWhaleAccessTokenResp struct {
	IsValid              bool     `protobuf:"varint,1,opt,name=is_valid,json=isValid,proto3" json:"is_valid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CheckWhiteWhaleAccessTokenResp) Reset()         { *m = CheckWhiteWhaleAccessTokenResp{} }
func (m *CheckWhiteWhaleAccessTokenResp) String() string { return proto.CompactTextString(m) }
func (*CheckWhiteWhaleAccessTokenResp) ProtoMessage()    {}
func (*CheckWhiteWhaleAccessTokenResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{50}
}
func (m *CheckWhiteWhaleAccessTokenResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CheckWhiteWhaleAccessTokenResp.Unmarshal(m, b)
}
func (m *CheckWhiteWhaleAccessTokenResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CheckWhiteWhaleAccessTokenResp.Marshal(b, m, deterministic)
}
func (dst *CheckWhiteWhaleAccessTokenResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CheckWhiteWhaleAccessTokenResp.Merge(dst, src)
}
func (m *CheckWhiteWhaleAccessTokenResp) XXX_Size() int {
	return xxx_messageInfo_CheckWhiteWhaleAccessTokenResp.Size(m)
}
func (m *CheckWhiteWhaleAccessTokenResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CheckWhiteWhaleAccessTokenResp.DiscardUnknown(m)
}

var xxx_messageInfo_CheckWhiteWhaleAccessTokenResp proto.InternalMessageInfo

func (m *CheckWhiteWhaleAccessTokenResp) GetIsValid() bool {
	if m != nil {
		return m.IsValid
	}
	return false
}

// 发送公会代理人邀请
// buf:lint:ignore FIELD_LOWER_SNAKE_CASE
type SendGuildAgentInviteReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guildId,proto3" json:"guildId,omitempty"`
	InviteUid            uint32   `protobuf:"varint,2,opt,name=invite_uid,json=inviteUid,proto3" json:"invite_uid,omitempty"`
	InvitedUid           uint32   `protobuf:"varint,3,opt,name=invited_uid,json=invitedUid,proto3" json:"invited_uid,omitempty"`
	AgentType            uint32   `protobuf:"varint,4,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGuildAgentInviteReq) Reset()         { *m = SendGuildAgentInviteReq{} }
func (m *SendGuildAgentInviteReq) String() string { return proto.CompactTextString(m) }
func (*SendGuildAgentInviteReq) ProtoMessage()    {}
func (*SendGuildAgentInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{51}
}
func (m *SendGuildAgentInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGuildAgentInviteReq.Unmarshal(m, b)
}
func (m *SendGuildAgentInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGuildAgentInviteReq.Marshal(b, m, deterministic)
}
func (dst *SendGuildAgentInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGuildAgentInviteReq.Merge(dst, src)
}
func (m *SendGuildAgentInviteReq) XXX_Size() int {
	return xxx_messageInfo_SendGuildAgentInviteReq.Size(m)
}
func (m *SendGuildAgentInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGuildAgentInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_SendGuildAgentInviteReq proto.InternalMessageInfo

func (m *SendGuildAgentInviteReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SendGuildAgentInviteReq) GetInviteUid() uint32 {
	if m != nil {
		return m.InviteUid
	}
	return 0
}

func (m *SendGuildAgentInviteReq) GetInvitedUid() uint32 {
	if m != nil {
		return m.InvitedUid
	}
	return 0
}

func (m *SendGuildAgentInviteReq) GetAgentType() uint32 {
	if m != nil {
		return m.AgentType
	}
	return 0
}

type SendGuildAgentInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SendGuildAgentInviteResp) Reset()         { *m = SendGuildAgentInviteResp{} }
func (m *SendGuildAgentInviteResp) String() string { return proto.CompactTextString(m) }
func (*SendGuildAgentInviteResp) ProtoMessage()    {}
func (*SendGuildAgentInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{52}
}
func (m *SendGuildAgentInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SendGuildAgentInviteResp.Unmarshal(m, b)
}
func (m *SendGuildAgentInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SendGuildAgentInviteResp.Marshal(b, m, deterministic)
}
func (dst *SendGuildAgentInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SendGuildAgentInviteResp.Merge(dst, src)
}
func (m *SendGuildAgentInviteResp) XXX_Size() int {
	return xxx_messageInfo_SendGuildAgentInviteResp.Size(m)
}
func (m *SendGuildAgentInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SendGuildAgentInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_SendGuildAgentInviteResp proto.InternalMessageInfo

// 撤回邀请
type CancelGuildAgentInviteReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelGuildAgentInviteReq) Reset()         { *m = CancelGuildAgentInviteReq{} }
func (m *CancelGuildAgentInviteReq) String() string { return proto.CompactTextString(m) }
func (*CancelGuildAgentInviteReq) ProtoMessage()    {}
func (*CancelGuildAgentInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{53}
}
func (m *CancelGuildAgentInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelGuildAgentInviteReq.Unmarshal(m, b)
}
func (m *CancelGuildAgentInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelGuildAgentInviteReq.Marshal(b, m, deterministic)
}
func (dst *CancelGuildAgentInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelGuildAgentInviteReq.Merge(dst, src)
}
func (m *CancelGuildAgentInviteReq) XXX_Size() int {
	return xxx_messageInfo_CancelGuildAgentInviteReq.Size(m)
}
func (m *CancelGuildAgentInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelGuildAgentInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_CancelGuildAgentInviteReq proto.InternalMessageInfo

func (m *CancelGuildAgentInviteReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

type CancelGuildAgentInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CancelGuildAgentInviteResp) Reset()         { *m = CancelGuildAgentInviteResp{} }
func (m *CancelGuildAgentInviteResp) String() string { return proto.CompactTextString(m) }
func (*CancelGuildAgentInviteResp) ProtoMessage()    {}
func (*CancelGuildAgentInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{54}
}
func (m *CancelGuildAgentInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CancelGuildAgentInviteResp.Unmarshal(m, b)
}
func (m *CancelGuildAgentInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CancelGuildAgentInviteResp.Marshal(b, m, deterministic)
}
func (dst *CancelGuildAgentInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CancelGuildAgentInviteResp.Merge(dst, src)
}
func (m *CancelGuildAgentInviteResp) XXX_Size() int {
	return xxx_messageInfo_CancelGuildAgentInviteResp.Size(m)
}
func (m *CancelGuildAgentInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CancelGuildAgentInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_CancelGuildAgentInviteResp proto.InternalMessageInfo

// 公会代理人邀请
type GuildAgentInvite struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	Ts                   uint32   `protobuf:"varint,3,opt,name=ts,proto3" json:"ts,omitempty"`
	AgentType            uint32   `protobuf:"varint,4,opt,name=agent_type,json=agentType,proto3" json:"agent_type,omitempty"`
	InviteStatus         uint32   `protobuf:"varint,5,opt,name=invite_status,json=inviteStatus,proto3" json:"invite_status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GuildAgentInvite) Reset()         { *m = GuildAgentInvite{} }
func (m *GuildAgentInvite) String() string { return proto.CompactTextString(m) }
func (*GuildAgentInvite) ProtoMessage()    {}
func (*GuildAgentInvite) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{55}
}
func (m *GuildAgentInvite) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildAgentInvite.Unmarshal(m, b)
}
func (m *GuildAgentInvite) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildAgentInvite.Marshal(b, m, deterministic)
}
func (dst *GuildAgentInvite) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildAgentInvite.Merge(dst, src)
}
func (m *GuildAgentInvite) XXX_Size() int {
	return xxx_messageInfo_GuildAgentInvite.Size(m)
}
func (m *GuildAgentInvite) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildAgentInvite.DiscardUnknown(m)
}

var xxx_messageInfo_GuildAgentInvite proto.InternalMessageInfo

func (m *GuildAgentInvite) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GuildAgentInvite) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *GuildAgentInvite) GetTs() uint32 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *GuildAgentInvite) GetAgentType() uint32 {
	if m != nil {
		return m.AgentType
	}
	return 0
}

func (m *GuildAgentInvite) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

// 获取公会代理人邀请列表
type GetGuildAgentInviteListReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	InviteStatus         uint32   `protobuf:"varint,3,opt,name=invite_status,json=inviteStatus,proto3" json:"invite_status,omitempty"`
	Page                 uint32   `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAgentInviteListReq) Reset()         { *m = GetGuildAgentInviteListReq{} }
func (m *GetGuildAgentInviteListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAgentInviteListReq) ProtoMessage()    {}
func (*GetGuildAgentInviteListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{56}
}
func (m *GetGuildAgentInviteListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAgentInviteListReq.Unmarshal(m, b)
}
func (m *GetGuildAgentInviteListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAgentInviteListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAgentInviteListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAgentInviteListReq.Merge(dst, src)
}
func (m *GetGuildAgentInviteListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAgentInviteListReq.Size(m)
}
func (m *GetGuildAgentInviteListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAgentInviteListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAgentInviteListReq proto.InternalMessageInfo

func (m *GetGuildAgentInviteListReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *GetGuildAgentInviteListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetGuildAgentInviteListReq) GetInviteStatus() uint32 {
	if m != nil {
		return m.InviteStatus
	}
	return 0
}

func (m *GetGuildAgentInviteListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetGuildAgentInviteListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetGuildAgentInviteListResp struct {
	InviteList           []*GuildAgentInvite `protobuf:"bytes,1,rep,name=invite_list,json=inviteList,proto3" json:"invite_list,omitempty"`
	NextPage             uint32              `protobuf:"varint,2,opt,name=next_page,json=nextPage,proto3" json:"next_page,omitempty"`
	TotalCnt             uint32              `protobuf:"varint,3,opt,name=total_cnt,json=totalCnt,proto3" json:"total_cnt,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetGuildAgentInviteListResp) Reset()         { *m = GetGuildAgentInviteListResp{} }
func (m *GetGuildAgentInviteListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAgentInviteListResp) ProtoMessage()    {}
func (*GetGuildAgentInviteListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{57}
}
func (m *GetGuildAgentInviteListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAgentInviteListResp.Unmarshal(m, b)
}
func (m *GetGuildAgentInviteListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAgentInviteListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAgentInviteListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAgentInviteListResp.Merge(dst, src)
}
func (m *GetGuildAgentInviteListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAgentInviteListResp.Size(m)
}
func (m *GetGuildAgentInviteListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAgentInviteListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAgentInviteListResp proto.InternalMessageInfo

func (m *GetGuildAgentInviteListResp) GetInviteList() []*GuildAgentInvite {
	if m != nil {
		return m.InviteList
	}
	return nil
}

func (m *GetGuildAgentInviteListResp) GetNextPage() uint32 {
	if m != nil {
		return m.NextPage
	}
	return 0
}

func (m *GetGuildAgentInviteListResp) GetTotalCnt() uint32 {
	if m != nil {
		return m.TotalCnt
	}
	return 0
}

// 处理公会代理人邀请
type ProcGuildAgentInviteReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Id                   uint32   `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	IsAccept             bool     `protobuf:"varint,3,opt,name=is_accept,json=isAccept,proto3" json:"is_accept,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcGuildAgentInviteReq) Reset()         { *m = ProcGuildAgentInviteReq{} }
func (m *ProcGuildAgentInviteReq) String() string { return proto.CompactTextString(m) }
func (*ProcGuildAgentInviteReq) ProtoMessage()    {}
func (*ProcGuildAgentInviteReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{58}
}
func (m *ProcGuildAgentInviteReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcGuildAgentInviteReq.Unmarshal(m, b)
}
func (m *ProcGuildAgentInviteReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcGuildAgentInviteReq.Marshal(b, m, deterministic)
}
func (dst *ProcGuildAgentInviteReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcGuildAgentInviteReq.Merge(dst, src)
}
func (m *ProcGuildAgentInviteReq) XXX_Size() int {
	return xxx_messageInfo_ProcGuildAgentInviteReq.Size(m)
}
func (m *ProcGuildAgentInviteReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcGuildAgentInviteReq.DiscardUnknown(m)
}

var xxx_messageInfo_ProcGuildAgentInviteReq proto.InternalMessageInfo

func (m *ProcGuildAgentInviteReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ProcGuildAgentInviteReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ProcGuildAgentInviteReq) GetIsAccept() bool {
	if m != nil {
		return m.IsAccept
	}
	return false
}

type ProcGuildAgentInviteResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ProcGuildAgentInviteResp) Reset()         { *m = ProcGuildAgentInviteResp{} }
func (m *ProcGuildAgentInviteResp) String() string { return proto.CompactTextString(m) }
func (*ProcGuildAgentInviteResp) ProtoMessage()    {}
func (*ProcGuildAgentInviteResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{59}
}
func (m *ProcGuildAgentInviteResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProcGuildAgentInviteResp.Unmarshal(m, b)
}
func (m *ProcGuildAgentInviteResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProcGuildAgentInviteResp.Marshal(b, m, deterministic)
}
func (dst *ProcGuildAgentInviteResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProcGuildAgentInviteResp.Merge(dst, src)
}
func (m *ProcGuildAgentInviteResp) XXX_Size() int {
	return xxx_messageInfo_ProcGuildAgentInviteResp.Size(m)
}
func (m *ProcGuildAgentInviteResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ProcGuildAgentInviteResp.DiscardUnknown(m)
}

var xxx_messageInfo_ProcGuildAgentInviteResp proto.InternalMessageInfo

// 增加违规数据导出记录
type AddVioExportRecordReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Uid                  uint32   `protobuf:"varint,2,opt,name=uid,proto3" json:"uid,omitempty"`
	BeginTs              uint64   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint64   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVioExportRecordReq) Reset()         { *m = AddVioExportRecordReq{} }
func (m *AddVioExportRecordReq) String() string { return proto.CompactTextString(m) }
func (*AddVioExportRecordReq) ProtoMessage()    {}
func (*AddVioExportRecordReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{60}
}
func (m *AddVioExportRecordReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVioExportRecordReq.Unmarshal(m, b)
}
func (m *AddVioExportRecordReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVioExportRecordReq.Marshal(b, m, deterministic)
}
func (dst *AddVioExportRecordReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVioExportRecordReq.Merge(dst, src)
}
func (m *AddVioExportRecordReq) XXX_Size() int {
	return xxx_messageInfo_AddVioExportRecordReq.Size(m)
}
func (m *AddVioExportRecordReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVioExportRecordReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddVioExportRecordReq proto.InternalMessageInfo

func (m *AddVioExportRecordReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *AddVioExportRecordReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *AddVioExportRecordReq) GetBeginTs() uint64 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *AddVioExportRecordReq) GetEndTs() uint64 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

type AddVioExportRecordResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddVioExportRecordResp) Reset()         { *m = AddVioExportRecordResp{} }
func (m *AddVioExportRecordResp) String() string { return proto.CompactTextString(m) }
func (*AddVioExportRecordResp) ProtoMessage()    {}
func (*AddVioExportRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{61}
}
func (m *AddVioExportRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddVioExportRecordResp.Unmarshal(m, b)
}
func (m *AddVioExportRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddVioExportRecordResp.Marshal(b, m, deterministic)
}
func (dst *AddVioExportRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddVioExportRecordResp.Merge(dst, src)
}
func (m *AddVioExportRecordResp) XXX_Size() int {
	return xxx_messageInfo_AddVioExportRecordResp.Size(m)
}
func (m *AddVioExportRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddVioExportRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddVioExportRecordResp proto.InternalMessageInfo

// 获取多人互房间动任职信息列表
type GetMultiChannelEmploymentInfoListReq struct {
	GuildIdList          []uint32 `protobuf:"varint,1,rep,packed,name=guild_id_list,json=guildIdList,proto3" json:"guild_id_list,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,2,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	UidList              []uint32 `protobuf:"varint,3,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Offset               uint32   `protobuf:"varint,4,opt,name=offset,proto3" json:"offset,omitempty"`
	Limit                uint32   `protobuf:"varint,5,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiChannelEmploymentInfoListReq) Reset()         { *m = GetMultiChannelEmploymentInfoListReq{} }
func (m *GetMultiChannelEmploymentInfoListReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiChannelEmploymentInfoListReq) ProtoMessage()    {}
func (*GetMultiChannelEmploymentInfoListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{62}
}
func (m *GetMultiChannelEmploymentInfoListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoListReq.Unmarshal(m, b)
}
func (m *GetMultiChannelEmploymentInfoListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoListReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiChannelEmploymentInfoListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiChannelEmploymentInfoListReq.Merge(dst, src)
}
func (m *GetMultiChannelEmploymentInfoListReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoListReq.Size(m)
}
func (m *GetMultiChannelEmploymentInfoListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiChannelEmploymentInfoListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiChannelEmploymentInfoListReq proto.InternalMessageInfo

func (m *GetMultiChannelEmploymentInfoListReq) GetGuildIdList() []uint32 {
	if m != nil {
		return m.GuildIdList
	}
	return nil
}

func (m *GetMultiChannelEmploymentInfoListReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *GetMultiChannelEmploymentInfoListReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *GetMultiChannelEmploymentInfoListReq) GetOffset() uint32 {
	if m != nil {
		return m.Offset
	}
	return 0
}

func (m *GetMultiChannelEmploymentInfoListReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type MultiChannelEmploymentInfo struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	AdminList            []uint32 `protobuf:"varint,4,rep,packed,name=admin_list,json=adminList,proto3" json:"admin_list,omitempty"`
	EmployeeList         []uint32 `protobuf:"varint,6,rep,packed,name=employee_list,json=employeeList,proto3" json:"employee_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MultiChannelEmploymentInfo) Reset()         { *m = MultiChannelEmploymentInfo{} }
func (m *MultiChannelEmploymentInfo) String() string { return proto.CompactTextString(m) }
func (*MultiChannelEmploymentInfo) ProtoMessage()    {}
func (*MultiChannelEmploymentInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{63}
}
func (m *MultiChannelEmploymentInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MultiChannelEmploymentInfo.Unmarshal(m, b)
}
func (m *MultiChannelEmploymentInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MultiChannelEmploymentInfo.Marshal(b, m, deterministic)
}
func (dst *MultiChannelEmploymentInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MultiChannelEmploymentInfo.Merge(dst, src)
}
func (m *MultiChannelEmploymentInfo) XXX_Size() int {
	return xxx_messageInfo_MultiChannelEmploymentInfo.Size(m)
}
func (m *MultiChannelEmploymentInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MultiChannelEmploymentInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MultiChannelEmploymentInfo proto.InternalMessageInfo

func (m *MultiChannelEmploymentInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *MultiChannelEmploymentInfo) GetAdminList() []uint32 {
	if m != nil {
		return m.AdminList
	}
	return nil
}

func (m *MultiChannelEmploymentInfo) GetEmployeeList() []uint32 {
	if m != nil {
		return m.EmployeeList
	}
	return nil
}

type GetMultiChannelEmploymentInfoListResp struct {
	EmploymentInfoList   []*MultiChannelEmploymentInfo `protobuf:"bytes,1,rep,name=employment_info_list,json=employmentInfoList,proto3" json:"employment_info_list,omitempty"`
	Total                uint32                        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *GetMultiChannelEmploymentInfoListResp) Reset()         { *m = GetMultiChannelEmploymentInfoListResp{} }
func (m *GetMultiChannelEmploymentInfoListResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiChannelEmploymentInfoListResp) ProtoMessage()    {}
func (*GetMultiChannelEmploymentInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{64}
}
func (m *GetMultiChannelEmploymentInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoListResp.Unmarshal(m, b)
}
func (m *GetMultiChannelEmploymentInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoListResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiChannelEmploymentInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiChannelEmploymentInfoListResp.Merge(dst, src)
}
func (m *GetMultiChannelEmploymentInfoListResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoListResp.Size(m)
}
func (m *GetMultiChannelEmploymentInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiChannelEmploymentInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiChannelEmploymentInfoListResp proto.InternalMessageInfo

func (m *GetMultiChannelEmploymentInfoListResp) GetEmploymentInfoList() []*MultiChannelEmploymentInfo {
	if m != nil {
		return m.EmploymentInfoList
	}
	return nil
}

func (m *GetMultiChannelEmploymentInfoListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 获取多人互房间动任职信息
type GetMultiChannelEmploymentInfoReq struct {
	ChannelId            uint32   `protobuf:"varint,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetMultiChannelEmploymentInfoReq) Reset()         { *m = GetMultiChannelEmploymentInfoReq{} }
func (m *GetMultiChannelEmploymentInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetMultiChannelEmploymentInfoReq) ProtoMessage()    {}
func (*GetMultiChannelEmploymentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{65}
}
func (m *GetMultiChannelEmploymentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoReq.Unmarshal(m, b)
}
func (m *GetMultiChannelEmploymentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetMultiChannelEmploymentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiChannelEmploymentInfoReq.Merge(dst, src)
}
func (m *GetMultiChannelEmploymentInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoReq.Size(m)
}
func (m *GetMultiChannelEmploymentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiChannelEmploymentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiChannelEmploymentInfoReq proto.InternalMessageInfo

func (m *GetMultiChannelEmploymentInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

type GetMultiChannelEmploymentInfoResp struct {
	EmploymentInfo       *MultiChannelEmploymentInfo `protobuf:"bytes,1,opt,name=employment_info,json=employmentInfo,proto3" json:"employment_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetMultiChannelEmploymentInfoResp) Reset()         { *m = GetMultiChannelEmploymentInfoResp{} }
func (m *GetMultiChannelEmploymentInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetMultiChannelEmploymentInfoResp) ProtoMessage()    {}
func (*GetMultiChannelEmploymentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{66}
}
func (m *GetMultiChannelEmploymentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoResp.Unmarshal(m, b)
}
func (m *GetMultiChannelEmploymentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetMultiChannelEmploymentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetMultiChannelEmploymentInfoResp.Merge(dst, src)
}
func (m *GetMultiChannelEmploymentInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetMultiChannelEmploymentInfoResp.Size(m)
}
func (m *GetMultiChannelEmploymentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetMultiChannelEmploymentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetMultiChannelEmploymentInfoResp proto.InternalMessageInfo

func (m *GetMultiChannelEmploymentInfoResp) GetEmploymentInfo() *MultiChannelEmploymentInfo {
	if m != nil {
		return m.EmploymentInfo
	}
	return nil
}

// 设置房间任职成员
type SetMultiChannelEmploymentInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	EmployeeUidList      []uint32 `protobuf:"varint,3,rep,packed,name=employee_uid_list,json=employeeUidList,proto3" json:"employee_uid_list,omitempty"`
	OperatorUid          uint32   `protobuf:"varint,4,opt,name=operator_uid,json=operatorUid,proto3" json:"operator_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMultiChannelEmploymentInfoReq) Reset()         { *m = SetMultiChannelEmploymentInfoReq{} }
func (m *SetMultiChannelEmploymentInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetMultiChannelEmploymentInfoReq) ProtoMessage()    {}
func (*SetMultiChannelEmploymentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{67}
}
func (m *SetMultiChannelEmploymentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMultiChannelEmploymentInfoReq.Unmarshal(m, b)
}
func (m *SetMultiChannelEmploymentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMultiChannelEmploymentInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetMultiChannelEmploymentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMultiChannelEmploymentInfoReq.Merge(dst, src)
}
func (m *SetMultiChannelEmploymentInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetMultiChannelEmploymentInfoReq.Size(m)
}
func (m *SetMultiChannelEmploymentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMultiChannelEmploymentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMultiChannelEmploymentInfoReq proto.InternalMessageInfo

func (m *SetMultiChannelEmploymentInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetMultiChannelEmploymentInfoReq) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *SetMultiChannelEmploymentInfoReq) GetEmployeeUidList() []uint32 {
	if m != nil {
		return m.EmployeeUidList
	}
	return nil
}

func (m *SetMultiChannelEmploymentInfoReq) GetOperatorUid() uint32 {
	if m != nil {
		return m.OperatorUid
	}
	return 0
}

type SetMultiChannelEmploymentInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMultiChannelEmploymentInfoResp) Reset()         { *m = SetMultiChannelEmploymentInfoResp{} }
func (m *SetMultiChannelEmploymentInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetMultiChannelEmploymentInfoResp) ProtoMessage()    {}
func (*SetMultiChannelEmploymentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{68}
}
func (m *SetMultiChannelEmploymentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMultiChannelEmploymentInfoResp.Unmarshal(m, b)
}
func (m *SetMultiChannelEmploymentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMultiChannelEmploymentInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetMultiChannelEmploymentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMultiChannelEmploymentInfoResp.Merge(dst, src)
}
func (m *SetMultiChannelEmploymentInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetMultiChannelEmploymentInfoResp.Size(m)
}
func (m *SetMultiChannelEmploymentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMultiChannelEmploymentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMultiChannelEmploymentInfoResp proto.InternalMessageInfo

// 批量设置任职成员
type BatchSetMultiChannelEmploymentInfoReq struct {
	EmploymentList       []*BatchSetMultiChannelEmploymentInfoReq_Item `protobuf:"bytes,1,rep,name=employment_list,json=employmentList,proto3" json:"employment_list,omitempty"`
	OperatorUid          uint32                                        `protobuf:"varint,4,opt,name=operator_uid,json=operatorUid,proto3" json:"operator_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-"`
	XXX_unrecognized     []byte                                        `json:"-"`
	XXX_sizecache        int32                                         `json:"-"`
}

func (m *BatchSetMultiChannelEmploymentInfoReq) Reset()         { *m = BatchSetMultiChannelEmploymentInfoReq{} }
func (m *BatchSetMultiChannelEmploymentInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchSetMultiChannelEmploymentInfoReq) ProtoMessage()    {}
func (*BatchSetMultiChannelEmploymentInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{69}
}
func (m *BatchSetMultiChannelEmploymentInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq.Unmarshal(m, b)
}
func (m *BatchSetMultiChannelEmploymentInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchSetMultiChannelEmploymentInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq.Merge(dst, src)
}
func (m *BatchSetMultiChannelEmploymentInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq.Size(m)
}
func (m *BatchSetMultiChannelEmploymentInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq proto.InternalMessageInfo

func (m *BatchSetMultiChannelEmploymentInfoReq) GetEmploymentList() []*BatchSetMultiChannelEmploymentInfoReq_Item {
	if m != nil {
		return m.EmploymentList
	}
	return nil
}

func (m *BatchSetMultiChannelEmploymentInfoReq) GetOperatorUid() uint32 {
	if m != nil {
		return m.OperatorUid
	}
	return 0
}

type BatchSetMultiChannelEmploymentInfoReq_Item struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelId            uint32   `protobuf:"varint,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	EmployeeUid          uint32   `protobuf:"varint,3,opt,name=employee_uid,json=employeeUid,proto3" json:"employee_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetMultiChannelEmploymentInfoReq_Item) Reset() {
	*m = BatchSetMultiChannelEmploymentInfoReq_Item{}
}
func (m *BatchSetMultiChannelEmploymentInfoReq_Item) String() string {
	return proto.CompactTextString(m)
}
func (*BatchSetMultiChannelEmploymentInfoReq_Item) ProtoMessage() {}
func (*BatchSetMultiChannelEmploymentInfoReq_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{69, 0}
}
func (m *BatchSetMultiChannelEmploymentInfoReq_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq_Item.Unmarshal(m, b)
}
func (m *BatchSetMultiChannelEmploymentInfoReq_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq_Item.Marshal(b, m, deterministic)
}
func (dst *BatchSetMultiChannelEmploymentInfoReq_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq_Item.Merge(dst, src)
}
func (m *BatchSetMultiChannelEmploymentInfoReq_Item) XXX_Size() int {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq_Item.Size(m)
}
func (m *BatchSetMultiChannelEmploymentInfoReq_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq_Item.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetMultiChannelEmploymentInfoReq_Item proto.InternalMessageInfo

func (m *BatchSetMultiChannelEmploymentInfoReq_Item) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchSetMultiChannelEmploymentInfoReq_Item) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *BatchSetMultiChannelEmploymentInfoReq_Item) GetEmployeeUid() uint32 {
	if m != nil {
		return m.EmployeeUid
	}
	return 0
}

type BatchSetMultiChannelEmploymentInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchSetMultiChannelEmploymentInfoResp) Reset() {
	*m = BatchSetMultiChannelEmploymentInfoResp{}
}
func (m *BatchSetMultiChannelEmploymentInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchSetMultiChannelEmploymentInfoResp) ProtoMessage()    {}
func (*BatchSetMultiChannelEmploymentInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{70}
}
func (m *BatchSetMultiChannelEmploymentInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoResp.Unmarshal(m, b)
}
func (m *BatchSetMultiChannelEmploymentInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchSetMultiChannelEmploymentInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchSetMultiChannelEmploymentInfoResp.Merge(dst, src)
}
func (m *BatchSetMultiChannelEmploymentInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchSetMultiChannelEmploymentInfoResp.Size(m)
}
func (m *BatchSetMultiChannelEmploymentInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchSetMultiChannelEmploymentInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchSetMultiChannelEmploymentInfoResp proto.InternalMessageInfo

// 设置房间厅管
type SetMultiChannelAdminInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	ChannelAdminUid      uint32   `protobuf:"varint,2,opt,name=channel_admin_uid,json=channelAdminUid,proto3" json:"channel_admin_uid,omitempty"`
	ChannelIdList        []uint32 `protobuf:"varint,3,rep,packed,name=channel_id_list,json=channelIdList,proto3" json:"channel_id_list,omitempty"`
	OperatorUid          uint32   `protobuf:"varint,4,opt,name=operator_uid,json=operatorUid,proto3" json:"operator_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMultiChannelAdminInfoReq) Reset()         { *m = SetMultiChannelAdminInfoReq{} }
func (m *SetMultiChannelAdminInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetMultiChannelAdminInfoReq) ProtoMessage()    {}
func (*SetMultiChannelAdminInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{71}
}
func (m *SetMultiChannelAdminInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMultiChannelAdminInfoReq.Unmarshal(m, b)
}
func (m *SetMultiChannelAdminInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMultiChannelAdminInfoReq.Marshal(b, m, deterministic)
}
func (dst *SetMultiChannelAdminInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMultiChannelAdminInfoReq.Merge(dst, src)
}
func (m *SetMultiChannelAdminInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetMultiChannelAdminInfoReq.Size(m)
}
func (m *SetMultiChannelAdminInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMultiChannelAdminInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetMultiChannelAdminInfoReq proto.InternalMessageInfo

func (m *SetMultiChannelAdminInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *SetMultiChannelAdminInfoReq) GetChannelAdminUid() uint32 {
	if m != nil {
		return m.ChannelAdminUid
	}
	return 0
}

func (m *SetMultiChannelAdminInfoReq) GetChannelIdList() []uint32 {
	if m != nil {
		return m.ChannelIdList
	}
	return nil
}

func (m *SetMultiChannelAdminInfoReq) GetOperatorUid() uint32 {
	if m != nil {
		return m.OperatorUid
	}
	return 0
}

type SetMultiChannelAdminInfoResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetMultiChannelAdminInfoResp) Reset()         { *m = SetMultiChannelAdminInfoResp{} }
func (m *SetMultiChannelAdminInfoResp) String() string { return proto.CompactTextString(m) }
func (*SetMultiChannelAdminInfoResp) ProtoMessage()    {}
func (*SetMultiChannelAdminInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{72}
}
func (m *SetMultiChannelAdminInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetMultiChannelAdminInfoResp.Unmarshal(m, b)
}
func (m *SetMultiChannelAdminInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetMultiChannelAdminInfoResp.Marshal(b, m, deterministic)
}
func (dst *SetMultiChannelAdminInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetMultiChannelAdminInfoResp.Merge(dst, src)
}
func (m *SetMultiChannelAdminInfoResp) XXX_Size() int {
	return xxx_messageInfo_SetMultiChannelAdminInfoResp.Size(m)
}
func (m *SetMultiChannelAdminInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetMultiChannelAdminInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetMultiChannelAdminInfoResp proto.InternalMessageInfo

// 批量设置厅管
type GetGuildAdminListReq struct {
	GuildId              []uint32 `protobuf:"varint,1,rep,packed,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAdminListReq) Reset()         { *m = GetGuildAdminListReq{} }
func (m *GetGuildAdminListReq) String() string { return proto.CompactTextString(m) }
func (*GetGuildAdminListReq) ProtoMessage()    {}
func (*GetGuildAdminListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{73}
}
func (m *GetGuildAdminListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAdminListReq.Unmarshal(m, b)
}
func (m *GetGuildAdminListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAdminListReq.Marshal(b, m, deterministic)
}
func (dst *GetGuildAdminListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAdminListReq.Merge(dst, src)
}
func (m *GetGuildAdminListReq) XXX_Size() int {
	return xxx_messageInfo_GetGuildAdminListReq.Size(m)
}
func (m *GetGuildAdminListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAdminListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAdminListReq proto.InternalMessageInfo

func (m *GetGuildAdminListReq) GetGuildId() []uint32 {
	if m != nil {
		return m.GuildId
	}
	return nil
}

type GetGuildAdminListResp struct {
	AdminList            []uint32 `protobuf:"varint,1,rep,packed,name=admin_list,json=adminList,proto3" json:"admin_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetGuildAdminListResp) Reset()         { *m = GetGuildAdminListResp{} }
func (m *GetGuildAdminListResp) String() string { return proto.CompactTextString(m) }
func (*GetGuildAdminListResp) ProtoMessage()    {}
func (*GetGuildAdminListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{74}
}
func (m *GetGuildAdminListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetGuildAdminListResp.Unmarshal(m, b)
}
func (m *GetGuildAdminListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetGuildAdminListResp.Marshal(b, m, deterministic)
}
func (dst *GetGuildAdminListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetGuildAdminListResp.Merge(dst, src)
}
func (m *GetGuildAdminListResp) XXX_Size() int {
	return xxx_messageInfo_GetGuildAdminListResp.Size(m)
}
func (m *GetGuildAdminListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetGuildAdminListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetGuildAdminListResp proto.InternalMessageInfo

func (m *GetGuildAdminListResp) GetAdminList() []uint32 {
	if m != nil {
		return m.AdminList
	}
	return nil
}

type GetChannelListByAdminUidReq struct {
	AdminUid             uint32   `protobuf:"varint,1,opt,name=admin_uid,json=adminUid,proto3" json:"admin_uid,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelListByAdminUidReq) Reset()         { *m = GetChannelListByAdminUidReq{} }
func (m *GetChannelListByAdminUidReq) String() string { return proto.CompactTextString(m) }
func (*GetChannelListByAdminUidReq) ProtoMessage()    {}
func (*GetChannelListByAdminUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{75}
}
func (m *GetChannelListByAdminUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListByAdminUidReq.Unmarshal(m, b)
}
func (m *GetChannelListByAdminUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListByAdminUidReq.Marshal(b, m, deterministic)
}
func (dst *GetChannelListByAdminUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListByAdminUidReq.Merge(dst, src)
}
func (m *GetChannelListByAdminUidReq) XXX_Size() int {
	return xxx_messageInfo_GetChannelListByAdminUidReq.Size(m)
}
func (m *GetChannelListByAdminUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListByAdminUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListByAdminUidReq proto.InternalMessageInfo

func (m *GetChannelListByAdminUidReq) GetAdminUid() uint32 {
	if m != nil {
		return m.AdminUid
	}
	return 0
}

type GetChannelListByAdminUidResp struct {
	ChannelList          []uint32 `protobuf:"varint,1,rep,packed,name=channel_list,json=channelList,proto3" json:"channel_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetChannelListByAdminUidResp) Reset()         { *m = GetChannelListByAdminUidResp{} }
func (m *GetChannelListByAdminUidResp) String() string { return proto.CompactTextString(m) }
func (*GetChannelListByAdminUidResp) ProtoMessage()    {}
func (*GetChannelListByAdminUidResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{76}
}
func (m *GetChannelListByAdminUidResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetChannelListByAdminUidResp.Unmarshal(m, b)
}
func (m *GetChannelListByAdminUidResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetChannelListByAdminUidResp.Marshal(b, m, deterministic)
}
func (dst *GetChannelListByAdminUidResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetChannelListByAdminUidResp.Merge(dst, src)
}
func (m *GetChannelListByAdminUidResp) XXX_Size() int {
	return xxx_messageInfo_GetChannelListByAdminUidResp.Size(m)
}
func (m *GetChannelListByAdminUidResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetChannelListByAdminUidResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetChannelListByAdminUidResp proto.InternalMessageInfo

func (m *GetChannelListByAdminUidResp) GetChannelList() []uint32 {
	if m != nil {
		return m.ChannelList
	}
	return nil
}

type BatchGetDailyFaceCheckInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Identity             uint32   `protobuf:"varint,2,opt,name=identity,proto3" json:"identity,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	UidList              []uint32 `protobuf:"varint,7,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	TtidList             []string `protobuf:"bytes,8,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetDailyFaceCheckInfoReq) Reset()         { *m = BatchGetDailyFaceCheckInfoReq{} }
func (m *BatchGetDailyFaceCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetDailyFaceCheckInfoReq) ProtoMessage()    {}
func (*BatchGetDailyFaceCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{77}
}
func (m *BatchGetDailyFaceCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDailyFaceCheckInfoReq.Unmarshal(m, b)
}
func (m *BatchGetDailyFaceCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDailyFaceCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetDailyFaceCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDailyFaceCheckInfoReq.Merge(dst, src)
}
func (m *BatchGetDailyFaceCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetDailyFaceCheckInfoReq.Size(m)
}
func (m *BatchGetDailyFaceCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDailyFaceCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDailyFaceCheckInfoReq proto.InternalMessageInfo

func (m *BatchGetDailyFaceCheckInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetDailyFaceCheckInfoReq) GetIdentity() uint32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *BatchGetDailyFaceCheckInfoReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BatchGetDailyFaceCheckInfoReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *BatchGetDailyFaceCheckInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *BatchGetDailyFaceCheckInfoReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *BatchGetDailyFaceCheckInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *BatchGetDailyFaceCheckInfoReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

type BatchGetDailyFaceCheckInfoResp struct {
	InfoList             []*DailyFaceCheckInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *BatchGetDailyFaceCheckInfoResp) Reset()         { *m = BatchGetDailyFaceCheckInfoResp{} }
func (m *BatchGetDailyFaceCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetDailyFaceCheckInfoResp) ProtoMessage()    {}
func (*BatchGetDailyFaceCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{78}
}
func (m *BatchGetDailyFaceCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDailyFaceCheckInfoResp.Unmarshal(m, b)
}
func (m *BatchGetDailyFaceCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDailyFaceCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetDailyFaceCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDailyFaceCheckInfoResp.Merge(dst, src)
}
func (m *BatchGetDailyFaceCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetDailyFaceCheckInfoResp.Size(m)
}
func (m *BatchGetDailyFaceCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDailyFaceCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDailyFaceCheckInfoResp proto.InternalMessageInfo

func (m *BatchGetDailyFaceCheckInfoResp) GetInfoList() []*DailyFaceCheckInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *BatchGetDailyFaceCheckInfoResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// DailyFaceCheckInfo 主播人脸核验日明细数据
type DailyFaceCheckInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Date                 string   `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Ttid                 string   `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	SignStartTs          int64    `protobuf:"varint,6,opt,name=sign_start_ts,json=signStartTs,proto3" json:"sign_start_ts,omitempty"`
	SignEndTs            int64    `protobuf:"varint,7,opt,name=sign_end_ts,json=signEndTs,proto3" json:"sign_end_ts,omitempty"`
	AgentUid             uint32   `protobuf:"varint,8,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AgentNickname        string   `protobuf:"bytes,9,opt,name=agent_nickname,json=agentNickname,proto3" json:"agent_nickname,omitempty"`
	RuleType             string   `protobuf:"bytes,10,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	ChannelId            uint32   `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,12,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,13,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,14,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	RoomGiftAmt          string   `protobuf:"bytes,15,opt,name=room_gift_amt,json=roomGiftAmt,proto3" json:"room_gift_amt,omitempty"`
	GuildGiftAmt         string   `protobuf:"bytes,16,opt,name=guild_gift_amt,json=guildGiftAmt,proto3" json:"guild_gift_amt,omitempty"`
	LessCommissionAmt    string   `protobuf:"bytes,17,opt,name=less_commission_amt,json=lessCommissionAmt,proto3" json:"less_commission_amt,omitempty"`
	IdentityType         string   `protobuf:"bytes,18,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DailyFaceCheckInfo) Reset()         { *m = DailyFaceCheckInfo{} }
func (m *DailyFaceCheckInfo) String() string { return proto.CompactTextString(m) }
func (*DailyFaceCheckInfo) ProtoMessage()    {}
func (*DailyFaceCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{79}
}
func (m *DailyFaceCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DailyFaceCheckInfo.Unmarshal(m, b)
}
func (m *DailyFaceCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DailyFaceCheckInfo.Marshal(b, m, deterministic)
}
func (dst *DailyFaceCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DailyFaceCheckInfo.Merge(dst, src)
}
func (m *DailyFaceCheckInfo) XXX_Size() int {
	return xxx_messageInfo_DailyFaceCheckInfo.Size(m)
}
func (m *DailyFaceCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DailyFaceCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DailyFaceCheckInfo proto.InternalMessageInfo

func (m *DailyFaceCheckInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetSignStartTs() int64 {
	if m != nil {
		return m.SignStartTs
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetSignEndTs() int64 {
	if m != nil {
		return m.SignEndTs
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetAgentNickname() string {
	if m != nil {
		return m.AgentNickname
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetRuleType() string {
	if m != nil {
		return m.RuleType
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *DailyFaceCheckInfo) GetRoomGiftAmt() string {
	if m != nil {
		return m.RoomGiftAmt
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetGuildGiftAmt() string {
	if m != nil {
		return m.GuildGiftAmt
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetLessCommissionAmt() string {
	if m != nil {
		return m.LessCommissionAmt
	}
	return ""
}

func (m *DailyFaceCheckInfo) GetIdentityType() string {
	if m != nil {
		return m.IdentityType
	}
	return ""
}

// WeeklyFaceCheckInfo 主播人脸核验周明细数据
type WeeklyFaceCheckInfo struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Date                 string   `protobuf:"bytes,2,opt,name=date,proto3" json:"date,omitempty"`
	Account              string   `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Nickname             string   `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Ttid                 string   `protobuf:"bytes,5,opt,name=ttid,proto3" json:"ttid,omitempty"`
	SignStartTs          int64    `protobuf:"varint,6,opt,name=sign_start_ts,json=signStartTs,proto3" json:"sign_start_ts,omitempty"`
	SignEndTs            int64    `protobuf:"varint,7,opt,name=sign_end_ts,json=signEndTs,proto3" json:"sign_end_ts,omitempty"`
	AgentUid             uint32   `protobuf:"varint,8,opt,name=agent_uid,json=agentUid,proto3" json:"agent_uid,omitempty"`
	AgentNickname        string   `protobuf:"bytes,9,opt,name=agent_nickname,json=agentNickname,proto3" json:"agent_nickname,omitempty"`
	RuleType             string   `protobuf:"bytes,10,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	ChannelId            uint32   `protobuf:"varint,11,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	ChannelDisplayId     uint32   `protobuf:"varint,12,opt,name=channel_display_id,json=channelDisplayId,proto3" json:"channel_display_id,omitempty"`
	GuildId              uint32   `protobuf:"varint,13,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	GuildShortId         uint32   `protobuf:"varint,14,opt,name=guild_short_id,json=guildShortId,proto3" json:"guild_short_id,omitempty"`
	RoomGiftAmt          string   `protobuf:"bytes,15,opt,name=room_gift_amt,json=roomGiftAmt,proto3" json:"room_gift_amt,omitempty"`
	GuildGiftAmt         string   `protobuf:"bytes,16,opt,name=guild_gift_amt,json=guildGiftAmt,proto3" json:"guild_gift_amt,omitempty"`
	LessCommissionAmt    string   `protobuf:"bytes,17,opt,name=less_commission_amt,json=lessCommissionAmt,proto3" json:"less_commission_amt,omitempty"`
	IdentityType         string   `protobuf:"bytes,18,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *WeeklyFaceCheckInfo) Reset()         { *m = WeeklyFaceCheckInfo{} }
func (m *WeeklyFaceCheckInfo) String() string { return proto.CompactTextString(m) }
func (*WeeklyFaceCheckInfo) ProtoMessage()    {}
func (*WeeklyFaceCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{80}
}
func (m *WeeklyFaceCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_WeeklyFaceCheckInfo.Unmarshal(m, b)
}
func (m *WeeklyFaceCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_WeeklyFaceCheckInfo.Marshal(b, m, deterministic)
}
func (dst *WeeklyFaceCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_WeeklyFaceCheckInfo.Merge(dst, src)
}
func (m *WeeklyFaceCheckInfo) XXX_Size() int {
	return xxx_messageInfo_WeeklyFaceCheckInfo.Size(m)
}
func (m *WeeklyFaceCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_WeeklyFaceCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_WeeklyFaceCheckInfo proto.InternalMessageInfo

func (m *WeeklyFaceCheckInfo) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetTtid() string {
	if m != nil {
		return m.Ttid
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetSignStartTs() int64 {
	if m != nil {
		return m.SignStartTs
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetSignEndTs() int64 {
	if m != nil {
		return m.SignEndTs
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetAgentUid() uint32 {
	if m != nil {
		return m.AgentUid
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetAgentNickname() string {
	if m != nil {
		return m.AgentNickname
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetRuleType() string {
	if m != nil {
		return m.RuleType
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetChannelId() uint32 {
	if m != nil {
		return m.ChannelId
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetChannelDisplayId() uint32 {
	if m != nil {
		return m.ChannelDisplayId
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetGuildShortId() uint32 {
	if m != nil {
		return m.GuildShortId
	}
	return 0
}

func (m *WeeklyFaceCheckInfo) GetRoomGiftAmt() string {
	if m != nil {
		return m.RoomGiftAmt
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetGuildGiftAmt() string {
	if m != nil {
		return m.GuildGiftAmt
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetLessCommissionAmt() string {
	if m != nil {
		return m.LessCommissionAmt
	}
	return ""
}

func (m *WeeklyFaceCheckInfo) GetIdentityType() string {
	if m != nil {
		return m.IdentityType
	}
	return ""
}

// 获取公会主播人脸核验周明细列表
type BatchGetWeeklyFaceCheckInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Identity             uint32   `protobuf:"varint,2,opt,name=identity,proto3" json:"identity,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TtidList             []string `protobuf:"bytes,7,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWeeklyFaceCheckInfoReq) Reset()         { *m = BatchGetWeeklyFaceCheckInfoReq{} }
func (m *BatchGetWeeklyFaceCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeeklyFaceCheckInfoReq) ProtoMessage()    {}
func (*BatchGetWeeklyFaceCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{81}
}
func (m *BatchGetWeeklyFaceCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeeklyFaceCheckInfoReq.Unmarshal(m, b)
}
func (m *BatchGetWeeklyFaceCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeeklyFaceCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeeklyFaceCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeeklyFaceCheckInfoReq.Merge(dst, src)
}
func (m *BatchGetWeeklyFaceCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeeklyFaceCheckInfoReq.Size(m)
}
func (m *BatchGetWeeklyFaceCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeeklyFaceCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeeklyFaceCheckInfoReq proto.InternalMessageInfo

func (m *BatchGetWeeklyFaceCheckInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetWeeklyFaceCheckInfoReq) GetIdentity() uint32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *BatchGetWeeklyFaceCheckInfoReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BatchGetWeeklyFaceCheckInfoReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *BatchGetWeeklyFaceCheckInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *BatchGetWeeklyFaceCheckInfoReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *BatchGetWeeklyFaceCheckInfoReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

type BatchGetWeeklyFaceCheckInfoResp struct {
	InfoList             []*WeeklyFaceCheckInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetWeeklyFaceCheckInfoResp) Reset()         { *m = BatchGetWeeklyFaceCheckInfoResp{} }
func (m *BatchGetWeeklyFaceCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeeklyFaceCheckInfoResp) ProtoMessage()    {}
func (*BatchGetWeeklyFaceCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{82}
}
func (m *BatchGetWeeklyFaceCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeeklyFaceCheckInfoResp.Unmarshal(m, b)
}
func (m *BatchGetWeeklyFaceCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeeklyFaceCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeeklyFaceCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeeklyFaceCheckInfoResp.Merge(dst, src)
}
func (m *BatchGetWeeklyFaceCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeeklyFaceCheckInfoResp.Size(m)
}
func (m *BatchGetWeeklyFaceCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeeklyFaceCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeeklyFaceCheckInfoResp proto.InternalMessageInfo

func (m *BatchGetWeeklyFaceCheckInfoResp) GetInfoList() []*WeeklyFaceCheckInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *BatchGetWeeklyFaceCheckInfoResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// GuildWeeklySumFaceCheckInfo 主播人脸核验公会每周汇总明细数据
type GuildWeeklySumFaceCheckInfo struct {
	Date                   string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	RuleType               string   `protobuf:"bytes,2,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	ActionUserCnt          string   `protobuf:"bytes,3,opt,name=action_user_cnt,json=actionUserCnt,proto3" json:"action_user_cnt,omitempty"`
	FaceUserCnt            string   `protobuf:"bytes,4,opt,name=face_user_cnt,json=faceUserCnt,proto3" json:"face_user_cnt,omitempty"`
	NotHimselfUserCnt      string   `protobuf:"bytes,5,opt,name=not_himself_user_cnt,json=notHimselfUserCnt,proto3" json:"not_himself_user_cnt,omitempty"`
	FaceRatio              string   `protobuf:"bytes,6,opt,name=face_ratio,json=faceRatio,proto3" json:"face_ratio,omitempty"`
	HimselfRatio           string   `protobuf:"bytes,7,opt,name=himself_ratio,json=himselfRatio,proto3" json:"himself_ratio,omitempty"`
	FaceHimselfRatio       string   `protobuf:"bytes,8,opt,name=face_himself_ratio,json=faceHimselfRatio,proto3" json:"face_himself_ratio,omitempty"`
	NotHimselfGuildGiftAmt string   `protobuf:"bytes,9,opt,name=not_himself_guild_gift_amt,json=notHimselfGuildGiftAmt,proto3" json:"not_himself_guild_gift_amt,omitempty"`
	LessCommissionAmt      string   `protobuf:"bytes,10,opt,name=less_commission_amt,json=lessCommissionAmt,proto3" json:"less_commission_amt,omitempty"`
	IdentityType           string   `protobuf:"bytes,11,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GuildWeeklySumFaceCheckInfo) Reset()         { *m = GuildWeeklySumFaceCheckInfo{} }
func (m *GuildWeeklySumFaceCheckInfo) String() string { return proto.CompactTextString(m) }
func (*GuildWeeklySumFaceCheckInfo) ProtoMessage()    {}
func (*GuildWeeklySumFaceCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{83}
}
func (m *GuildWeeklySumFaceCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildWeeklySumFaceCheckInfo.Unmarshal(m, b)
}
func (m *GuildWeeklySumFaceCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildWeeklySumFaceCheckInfo.Marshal(b, m, deterministic)
}
func (dst *GuildWeeklySumFaceCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildWeeklySumFaceCheckInfo.Merge(dst, src)
}
func (m *GuildWeeklySumFaceCheckInfo) XXX_Size() int {
	return xxx_messageInfo_GuildWeeklySumFaceCheckInfo.Size(m)
}
func (m *GuildWeeklySumFaceCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildWeeklySumFaceCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildWeeklySumFaceCheckInfo proto.InternalMessageInfo

func (m *GuildWeeklySumFaceCheckInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetRuleType() string {
	if m != nil {
		return m.RuleType
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetActionUserCnt() string {
	if m != nil {
		return m.ActionUserCnt
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetFaceUserCnt() string {
	if m != nil {
		return m.FaceUserCnt
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetNotHimselfUserCnt() string {
	if m != nil {
		return m.NotHimselfUserCnt
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetFaceRatio() string {
	if m != nil {
		return m.FaceRatio
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetHimselfRatio() string {
	if m != nil {
		return m.HimselfRatio
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetFaceHimselfRatio() string {
	if m != nil {
		return m.FaceHimselfRatio
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetNotHimselfGuildGiftAmt() string {
	if m != nil {
		return m.NotHimselfGuildGiftAmt
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetLessCommissionAmt() string {
	if m != nil {
		return m.LessCommissionAmt
	}
	return ""
}

func (m *GuildWeeklySumFaceCheckInfo) GetIdentityType() string {
	if m != nil {
		return m.IdentityType
	}
	return ""
}

// 获取公会主播人脸核验公会每周汇总
type BatchGetWeeklySumFaceCheckInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Identity             uint32   `protobuf:"varint,2,opt,name=identity,proto3" json:"identity,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWeeklySumFaceCheckInfoReq) Reset()         { *m = BatchGetWeeklySumFaceCheckInfoReq{} }
func (m *BatchGetWeeklySumFaceCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeeklySumFaceCheckInfoReq) ProtoMessage()    {}
func (*BatchGetWeeklySumFaceCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{84}
}
func (m *BatchGetWeeklySumFaceCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoReq.Unmarshal(m, b)
}
func (m *BatchGetWeeklySumFaceCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeeklySumFaceCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoReq.Merge(dst, src)
}
func (m *BatchGetWeeklySumFaceCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoReq.Size(m)
}
func (m *BatchGetWeeklySumFaceCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoReq proto.InternalMessageInfo

func (m *BatchGetWeeklySumFaceCheckInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetWeeklySumFaceCheckInfoReq) GetIdentity() uint32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *BatchGetWeeklySumFaceCheckInfoReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BatchGetWeeklySumFaceCheckInfoReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *BatchGetWeeklySumFaceCheckInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *BatchGetWeeklySumFaceCheckInfoReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type BatchGetWeeklySumFaceCheckInfoResp struct {
	InfoList             []*GuildWeeklySumFaceCheckInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                         `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-"`
	XXX_unrecognized     []byte                         `json:"-"`
	XXX_sizecache        int32                          `json:"-"`
}

func (m *BatchGetWeeklySumFaceCheckInfoResp) Reset()         { *m = BatchGetWeeklySumFaceCheckInfoResp{} }
func (m *BatchGetWeeklySumFaceCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetWeeklySumFaceCheckInfoResp) ProtoMessage()    {}
func (*BatchGetWeeklySumFaceCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{85}
}
func (m *BatchGetWeeklySumFaceCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoResp.Unmarshal(m, b)
}
func (m *BatchGetWeeklySumFaceCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetWeeklySumFaceCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoResp.Merge(dst, src)
}
func (m *BatchGetWeeklySumFaceCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoResp.Size(m)
}
func (m *BatchGetWeeklySumFaceCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWeeklySumFaceCheckInfoResp proto.InternalMessageInfo

func (m *BatchGetWeeklySumFaceCheckInfoResp) GetInfoList() []*GuildWeeklySumFaceCheckInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *BatchGetWeeklySumFaceCheckInfoResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// GuildDailySumFaceCheckInfo 主播人脸核验公会每日汇总数据
type GuildDailySumFaceCheckInfo struct {
	Date                   string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	RuleType               string   `protobuf:"bytes,2,opt,name=rule_type,json=ruleType,proto3" json:"rule_type,omitempty"`
	ActionUserCnt          string   `protobuf:"bytes,3,opt,name=action_user_cnt,json=actionUserCnt,proto3" json:"action_user_cnt,omitempty"`
	FaceUserCnt            string   `protobuf:"bytes,4,opt,name=face_user_cnt,json=faceUserCnt,proto3" json:"face_user_cnt,omitempty"`
	NotHimselfUserCnt      string   `protobuf:"bytes,5,opt,name=not_himself_user_cnt,json=notHimselfUserCnt,proto3" json:"not_himself_user_cnt,omitempty"`
	FaceRatio              string   `protobuf:"bytes,6,opt,name=face_ratio,json=faceRatio,proto3" json:"face_ratio,omitempty"`
	HimselfRatio           string   `protobuf:"bytes,7,opt,name=himself_ratio,json=himselfRatio,proto3" json:"himself_ratio,omitempty"`
	FaceHimselfRatio       string   `protobuf:"bytes,8,opt,name=face_himself_ratio,json=faceHimselfRatio,proto3" json:"face_himself_ratio,omitempty"`
	NotHimselfGuildGiftAmt string   `protobuf:"bytes,9,opt,name=not_himself_guild_gift_amt,json=notHimselfGuildGiftAmt,proto3" json:"not_himself_guild_gift_amt,omitempty"`
	LessCommissionAmt      string   `protobuf:"bytes,10,opt,name=less_commission_amt,json=lessCommissionAmt,proto3" json:"less_commission_amt,omitempty"`
	IdentityType           string   `protobuf:"bytes,11,opt,name=identity_type,json=identityType,proto3" json:"identity_type,omitempty"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-"`
	XXX_unrecognized       []byte   `json:"-"`
	XXX_sizecache          int32    `json:"-"`
}

func (m *GuildDailySumFaceCheckInfo) Reset()         { *m = GuildDailySumFaceCheckInfo{} }
func (m *GuildDailySumFaceCheckInfo) String() string { return proto.CompactTextString(m) }
func (*GuildDailySumFaceCheckInfo) ProtoMessage()    {}
func (*GuildDailySumFaceCheckInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{86}
}
func (m *GuildDailySumFaceCheckInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GuildDailySumFaceCheckInfo.Unmarshal(m, b)
}
func (m *GuildDailySumFaceCheckInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GuildDailySumFaceCheckInfo.Marshal(b, m, deterministic)
}
func (dst *GuildDailySumFaceCheckInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GuildDailySumFaceCheckInfo.Merge(dst, src)
}
func (m *GuildDailySumFaceCheckInfo) XXX_Size() int {
	return xxx_messageInfo_GuildDailySumFaceCheckInfo.Size(m)
}
func (m *GuildDailySumFaceCheckInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_GuildDailySumFaceCheckInfo.DiscardUnknown(m)
}

var xxx_messageInfo_GuildDailySumFaceCheckInfo proto.InternalMessageInfo

func (m *GuildDailySumFaceCheckInfo) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetRuleType() string {
	if m != nil {
		return m.RuleType
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetActionUserCnt() string {
	if m != nil {
		return m.ActionUserCnt
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetFaceUserCnt() string {
	if m != nil {
		return m.FaceUserCnt
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetNotHimselfUserCnt() string {
	if m != nil {
		return m.NotHimselfUserCnt
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetFaceRatio() string {
	if m != nil {
		return m.FaceRatio
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetHimselfRatio() string {
	if m != nil {
		return m.HimselfRatio
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetFaceHimselfRatio() string {
	if m != nil {
		return m.FaceHimselfRatio
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetNotHimselfGuildGiftAmt() string {
	if m != nil {
		return m.NotHimselfGuildGiftAmt
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetLessCommissionAmt() string {
	if m != nil {
		return m.LessCommissionAmt
	}
	return ""
}

func (m *GuildDailySumFaceCheckInfo) GetIdentityType() string {
	if m != nil {
		return m.IdentityType
	}
	return ""
}

// 获取公会主播人脸核验公会每日汇总列表
type BatchGetDailySumFaceCheckInfoReq struct {
	GuildId              uint32   `protobuf:"varint,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	Identity             uint32   `protobuf:"varint,2,opt,name=identity,proto3" json:"identity,omitempty"`
	BeginTs              uint32   `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32   `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	Page                 uint32   `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetDailySumFaceCheckInfoReq) Reset()         { *m = BatchGetDailySumFaceCheckInfoReq{} }
func (m *BatchGetDailySumFaceCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetDailySumFaceCheckInfoReq) ProtoMessage()    {}
func (*BatchGetDailySumFaceCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{87}
}
func (m *BatchGetDailySumFaceCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDailySumFaceCheckInfoReq.Unmarshal(m, b)
}
func (m *BatchGetDailySumFaceCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDailySumFaceCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetDailySumFaceCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDailySumFaceCheckInfoReq.Merge(dst, src)
}
func (m *BatchGetDailySumFaceCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetDailySumFaceCheckInfoReq.Size(m)
}
func (m *BatchGetDailySumFaceCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDailySumFaceCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDailySumFaceCheckInfoReq proto.InternalMessageInfo

func (m *BatchGetDailySumFaceCheckInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *BatchGetDailySumFaceCheckInfoReq) GetIdentity() uint32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *BatchGetDailySumFaceCheckInfoReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *BatchGetDailySumFaceCheckInfoReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *BatchGetDailySumFaceCheckInfoReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *BatchGetDailySumFaceCheckInfoReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type BatchGetDailySumFaceCheckInfoResp struct {
	InfoList             []*GuildDailySumFaceCheckInfo `protobuf:"bytes,1,rep,name=info_list,json=infoList,proto3" json:"info_list,omitempty"`
	Total                uint32                        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-"`
	XXX_unrecognized     []byte                        `json:"-"`
	XXX_sizecache        int32                         `json:"-"`
}

func (m *BatchGetDailySumFaceCheckInfoResp) Reset()         { *m = BatchGetDailySumFaceCheckInfoResp{} }
func (m *BatchGetDailySumFaceCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetDailySumFaceCheckInfoResp) ProtoMessage()    {}
func (*BatchGetDailySumFaceCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{88}
}
func (m *BatchGetDailySumFaceCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetDailySumFaceCheckInfoResp.Unmarshal(m, b)
}
func (m *BatchGetDailySumFaceCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetDailySumFaceCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetDailySumFaceCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetDailySumFaceCheckInfoResp.Merge(dst, src)
}
func (m *BatchGetDailySumFaceCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetDailySumFaceCheckInfoResp.Size(m)
}
func (m *BatchGetDailySumFaceCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetDailySumFaceCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetDailySumFaceCheckInfoResp proto.InternalMessageInfo

func (m *BatchGetDailySumFaceCheckInfoResp) GetInfoList() []*GuildDailySumFaceCheckInfo {
	if m != nil {
		return m.InfoList
	}
	return nil
}

func (m *BatchGetDailySumFaceCheckInfoResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

// 根据类型导出公会主播人脸核验明细数据
type ExportFaceCheckInfoReq struct {
	Uid                  uint32              `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	GuildId              uint32              `protobuf:"varint,2,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	BeginTs              uint32              `protobuf:"varint,3,opt,name=begin_ts,json=beginTs,proto3" json:"begin_ts,omitempty"`
	EndTs                uint32              `protobuf:"varint,4,opt,name=end_ts,json=endTs,proto3" json:"end_ts,omitempty"`
	UidList              []uint32            `protobuf:"varint,5,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	ExportType           FaceCheckExportType `protobuf:"varint,6,opt,name=export_type,json=exportType,proto3,enum=guild_management_svr.FaceCheckExportType" json:"export_type,omitempty"`
	Identity             uint32              `protobuf:"varint,7,opt,name=identity,proto3" json:"identity,omitempty"`
	TtidList             []string            `protobuf:"bytes,8,rep,name=ttid_list,json=ttidList,proto3" json:"ttid_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *ExportFaceCheckInfoReq) Reset()         { *m = ExportFaceCheckInfoReq{} }
func (m *ExportFaceCheckInfoReq) String() string { return proto.CompactTextString(m) }
func (*ExportFaceCheckInfoReq) ProtoMessage()    {}
func (*ExportFaceCheckInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{89}
}
func (m *ExportFaceCheckInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportFaceCheckInfoReq.Unmarshal(m, b)
}
func (m *ExportFaceCheckInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportFaceCheckInfoReq.Marshal(b, m, deterministic)
}
func (dst *ExportFaceCheckInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportFaceCheckInfoReq.Merge(dst, src)
}
func (m *ExportFaceCheckInfoReq) XXX_Size() int {
	return xxx_messageInfo_ExportFaceCheckInfoReq.Size(m)
}
func (m *ExportFaceCheckInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportFaceCheckInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExportFaceCheckInfoReq proto.InternalMessageInfo

func (m *ExportFaceCheckInfoReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExportFaceCheckInfoReq) GetGuildId() uint32 {
	if m != nil {
		return m.GuildId
	}
	return 0
}

func (m *ExportFaceCheckInfoReq) GetBeginTs() uint32 {
	if m != nil {
		return m.BeginTs
	}
	return 0
}

func (m *ExportFaceCheckInfoReq) GetEndTs() uint32 {
	if m != nil {
		return m.EndTs
	}
	return 0
}

func (m *ExportFaceCheckInfoReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *ExportFaceCheckInfoReq) GetExportType() FaceCheckExportType {
	if m != nil {
		return m.ExportType
	}
	return FaceCheckExportType_FaceCheckTypeInvalid
}

func (m *ExportFaceCheckInfoReq) GetIdentity() uint32 {
	if m != nil {
		return m.Identity
	}
	return 0
}

func (m *ExportFaceCheckInfoReq) GetTtidList() []string {
	if m != nil {
		return m.TtidList
	}
	return nil
}

// 导出主播人脸核验数据响应
type ExportFaceCheckInfoResp struct {
	DownloadUrl          string   `protobuf:"bytes,1,opt,name=download_url,json=downloadUrl,proto3" json:"download_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ExportFaceCheckInfoResp) Reset()         { *m = ExportFaceCheckInfoResp{} }
func (m *ExportFaceCheckInfoResp) String() string { return proto.CompactTextString(m) }
func (*ExportFaceCheckInfoResp) ProtoMessage()    {}
func (*ExportFaceCheckInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{90}
}
func (m *ExportFaceCheckInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExportFaceCheckInfoResp.Unmarshal(m, b)
}
func (m *ExportFaceCheckInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExportFaceCheckInfoResp.Marshal(b, m, deterministic)
}
func (dst *ExportFaceCheckInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExportFaceCheckInfoResp.Merge(dst, src)
}
func (m *ExportFaceCheckInfoResp) XXX_Size() int {
	return xxx_messageInfo_ExportFaceCheckInfoResp.Size(m)
}
func (m *ExportFaceCheckInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExportFaceCheckInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExportFaceCheckInfoResp proto.InternalMessageInfo

func (m *ExportFaceCheckInfoResp) GetDownloadUrl() string {
	if m != nil {
		return m.DownloadUrl
	}
	return ""
}

// 预创建上传文件
type PreCreateUploadFileReq struct {
	Uid                  uint32   `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	FileName             string   `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
	FileSize             uint64   `protobuf:"varint,3,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	FileType             string   `protobuf:"bytes,4,opt,name=file_type,json=fileType,proto3" json:"file_type,omitempty"`
	FileMd5              string   `protobuf:"bytes,5,opt,name=file_md5,json=fileMd5,proto3" json:"file_md5,omitempty"`
	FileExt              string   `protobuf:"bytes,6,opt,name=file_ext,json=fileExt,proto3" json:"file_ext,omitempty"`
	Scene                string   `protobuf:"bytes,7,opt,name=scene,proto3" json:"scene,omitempty"`
	Scope                string   `protobuf:"bytes,8,opt,name=scope,proto3" json:"scope,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreCreateUploadFileReq) Reset()         { *m = PreCreateUploadFileReq{} }
func (m *PreCreateUploadFileReq) String() string { return proto.CompactTextString(m) }
func (*PreCreateUploadFileReq) ProtoMessage()    {}
func (*PreCreateUploadFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{91}
}
func (m *PreCreateUploadFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreCreateUploadFileReq.Unmarshal(m, b)
}
func (m *PreCreateUploadFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreCreateUploadFileReq.Marshal(b, m, deterministic)
}
func (dst *PreCreateUploadFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreCreateUploadFileReq.Merge(dst, src)
}
func (m *PreCreateUploadFileReq) XXX_Size() int {
	return xxx_messageInfo_PreCreateUploadFileReq.Size(m)
}
func (m *PreCreateUploadFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PreCreateUploadFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_PreCreateUploadFileReq proto.InternalMessageInfo

func (m *PreCreateUploadFileReq) GetUid() uint32 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *PreCreateUploadFileReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

func (m *PreCreateUploadFileReq) GetFileSize() uint64 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *PreCreateUploadFileReq) GetFileType() string {
	if m != nil {
		return m.FileType
	}
	return ""
}

func (m *PreCreateUploadFileReq) GetFileMd5() string {
	if m != nil {
		return m.FileMd5
	}
	return ""
}

func (m *PreCreateUploadFileReq) GetFileExt() string {
	if m != nil {
		return m.FileExt
	}
	return ""
}

func (m *PreCreateUploadFileReq) GetScene() string {
	if m != nil {
		return m.Scene
	}
	return ""
}

func (m *PreCreateUploadFileReq) GetScope() string {
	if m != nil {
		return m.Scope
	}
	return ""
}

type PreCreateUploadFileResp struct {
	FileId               string   `protobuf:"bytes,2,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PreCreateUploadFileResp) Reset()         { *m = PreCreateUploadFileResp{} }
func (m *PreCreateUploadFileResp) String() string { return proto.CompactTextString(m) }
func (*PreCreateUploadFileResp) ProtoMessage()    {}
func (*PreCreateUploadFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{92}
}
func (m *PreCreateUploadFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PreCreateUploadFileResp.Unmarshal(m, b)
}
func (m *PreCreateUploadFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PreCreateUploadFileResp.Marshal(b, m, deterministic)
}
func (dst *PreCreateUploadFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PreCreateUploadFileResp.Merge(dst, src)
}
func (m *PreCreateUploadFileResp) XXX_Size() int {
	return xxx_messageInfo_PreCreateUploadFileResp.Size(m)
}
func (m *PreCreateUploadFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PreCreateUploadFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_PreCreateUploadFileResp proto.InternalMessageInfo

func (m *PreCreateUploadFileResp) GetFileId() string {
	if m != nil {
		return m.FileId
	}
	return ""
}

// 完成上传文件
type CompleteUploadFileReq struct {
	FileId               string   `protobuf:"bytes,1,opt,name=file_id,json=fileId,proto3" json:"file_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompleteUploadFileReq) Reset()         { *m = CompleteUploadFileReq{} }
func (m *CompleteUploadFileReq) String() string { return proto.CompactTextString(m) }
func (*CompleteUploadFileReq) ProtoMessage()    {}
func (*CompleteUploadFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{93}
}
func (m *CompleteUploadFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteUploadFileReq.Unmarshal(m, b)
}
func (m *CompleteUploadFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteUploadFileReq.Marshal(b, m, deterministic)
}
func (dst *CompleteUploadFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteUploadFileReq.Merge(dst, src)
}
func (m *CompleteUploadFileReq) XXX_Size() int {
	return xxx_messageInfo_CompleteUploadFileReq.Size(m)
}
func (m *CompleteUploadFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteUploadFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteUploadFileReq proto.InternalMessageInfo

func (m *CompleteUploadFileReq) GetFileId() string {
	if m != nil {
		return m.FileId
	}
	return ""
}

type CompleteUploadFileResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *CompleteUploadFileResp) Reset()         { *m = CompleteUploadFileResp{} }
func (m *CompleteUploadFileResp) String() string { return proto.CompactTextString(m) }
func (*CompleteUploadFileResp) ProtoMessage()    {}
func (*CompleteUploadFileResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{94}
}
func (m *CompleteUploadFileResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CompleteUploadFileResp.Unmarshal(m, b)
}
func (m *CompleteUploadFileResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CompleteUploadFileResp.Marshal(b, m, deterministic)
}
func (dst *CompleteUploadFileResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CompleteUploadFileResp.Merge(dst, src)
}
func (m *CompleteUploadFileResp) XXX_Size() int {
	return xxx_messageInfo_CompleteUploadFileResp.Size(m)
}
func (m *CompleteUploadFileResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CompleteUploadFileResp.DiscardUnknown(m)
}

var xxx_messageInfo_CompleteUploadFileResp proto.InternalMessageInfo

// 获取上传文件URL
type BatchGetUploadFileUrlReq struct {
	FileKeyList          []string `protobuf:"bytes,1,rep,name=file_key_list,json=fileKeyList,proto3" json:"file_key_list,omitempty"`
	ExpirationSec        uint32   `protobuf:"varint,2,opt,name=expiration_sec,json=expirationSec,proto3" json:"expiration_sec,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetUploadFileUrlReq) Reset()         { *m = BatchGetUploadFileUrlReq{} }
func (m *BatchGetUploadFileUrlReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetUploadFileUrlReq) ProtoMessage()    {}
func (*BatchGetUploadFileUrlReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{95}
}
func (m *BatchGetUploadFileUrlReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUploadFileUrlReq.Unmarshal(m, b)
}
func (m *BatchGetUploadFileUrlReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUploadFileUrlReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetUploadFileUrlReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUploadFileUrlReq.Merge(dst, src)
}
func (m *BatchGetUploadFileUrlReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetUploadFileUrlReq.Size(m)
}
func (m *BatchGetUploadFileUrlReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUploadFileUrlReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUploadFileUrlReq proto.InternalMessageInfo

func (m *BatchGetUploadFileUrlReq) GetFileKeyList() []string {
	if m != nil {
		return m.FileKeyList
	}
	return nil
}

func (m *BatchGetUploadFileUrlReq) GetExpirationSec() uint32 {
	if m != nil {
		return m.ExpirationSec
	}
	return 0
}

type BatchGetUploadFileUrlResp struct {
	FileUrlMap           map[string]string `protobuf:"bytes,1,rep,name=file_url_map,json=fileUrlMap,proto3" json:"file_url_map,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *BatchGetUploadFileUrlResp) Reset()         { *m = BatchGetUploadFileUrlResp{} }
func (m *BatchGetUploadFileUrlResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetUploadFileUrlResp) ProtoMessage()    {}
func (*BatchGetUploadFileUrlResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{96}
}
func (m *BatchGetUploadFileUrlResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetUploadFileUrlResp.Unmarshal(m, b)
}
func (m *BatchGetUploadFileUrlResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetUploadFileUrlResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetUploadFileUrlResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetUploadFileUrlResp.Merge(dst, src)
}
func (m *BatchGetUploadFileUrlResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetUploadFileUrlResp.Size(m)
}
func (m *BatchGetUploadFileUrlResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetUploadFileUrlResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetUploadFileUrlResp proto.InternalMessageInfo

func (m *BatchGetUploadFileUrlResp) GetFileUrlMap() map[string]string {
	if m != nil {
		return m.FileUrlMap
	}
	return nil
}

// 平台资讯banner
type BannerInfo struct {
	ImgUrl               string   `protobuf:"bytes,1,opt,name=img_url,json=imgUrl,proto3" json:"img_url,omitempty"`
	JumpUrl              string   `protobuf:"bytes,2,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BannerInfo) Reset()         { *m = BannerInfo{} }
func (m *BannerInfo) String() string { return proto.CompactTextString(m) }
func (*BannerInfo) ProtoMessage()    {}
func (*BannerInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{97}
}
func (m *BannerInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BannerInfo.Unmarshal(m, b)
}
func (m *BannerInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BannerInfo.Marshal(b, m, deterministic)
}
func (dst *BannerInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BannerInfo.Merge(dst, src)
}
func (m *BannerInfo) XXX_Size() int {
	return xxx_messageInfo_BannerInfo.Size(m)
}
func (m *BannerInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BannerInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BannerInfo proto.InternalMessageInfo

func (m *BannerInfo) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *BannerInfo) GetJumpUrl() string {
	if m != nil {
		return m.JumpUrl
	}
	return ""
}

// 获取公会平台资讯banner
type GetBannerInfoReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetBannerInfoReq) Reset()         { *m = GetBannerInfoReq{} }
func (m *GetBannerInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetBannerInfoReq) ProtoMessage()    {}
func (*GetBannerInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{98}
}
func (m *GetBannerInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerInfoReq.Unmarshal(m, b)
}
func (m *GetBannerInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerInfoReq.Marshal(b, m, deterministic)
}
func (dst *GetBannerInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerInfoReq.Merge(dst, src)
}
func (m *GetBannerInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetBannerInfoReq.Size(m)
}
func (m *GetBannerInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerInfoReq proto.InternalMessageInfo

type GetBannerInfoResp struct {
	BannerList           []*BannerInfo `protobuf:"bytes,1,rep,name=banner_list,json=bannerList,proto3" json:"banner_list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetBannerInfoResp) Reset()         { *m = GetBannerInfoResp{} }
func (m *GetBannerInfoResp) String() string { return proto.CompactTextString(m) }
func (*GetBannerInfoResp) ProtoMessage()    {}
func (*GetBannerInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_guild_management_svr_38ed9aba41a644c3, []int{99}
}
func (m *GetBannerInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetBannerInfoResp.Unmarshal(m, b)
}
func (m *GetBannerInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetBannerInfoResp.Marshal(b, m, deterministic)
}
func (dst *GetBannerInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetBannerInfoResp.Merge(dst, src)
}
func (m *GetBannerInfoResp) XXX_Size() int {
	return xxx_messageInfo_GetBannerInfoResp.Size(m)
}
func (m *GetBannerInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetBannerInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetBannerInfoResp proto.InternalMessageInfo

func (m *GetBannerInfoResp) GetBannerList() []*BannerInfo {
	if m != nil {
		return m.BannerList
	}
	return nil
}

func init() {
	proto.RegisterType((*MenuPermission)(nil), "guild_management_svr.MenuPermission")
	proto.RegisterType((*LiveDataPermission)(nil), "guild_management_svr.LiveDataPermission")
	proto.RegisterType((*FunctionPermission)(nil), "guild_management_svr.FunctionPermission")
	proto.RegisterType((*DataPermission)(nil), "guild_management_svr.DataPermission")
	proto.RegisterType((*GuildManagePermission)(nil), "guild_management_svr.GuildManagePermission")
	proto.RegisterType((*GuildAgent)(nil), "guild_management_svr.GuildAgent")
	proto.RegisterType((*GetGuildAgentListReq)(nil), "guild_management_svr.GetGuildAgentListReq")
	proto.RegisterType((*GetGuildAgentListResp)(nil), "guild_management_svr.GetGuildAgentListResp")
	proto.RegisterType((*AddGuildAgentReq)(nil), "guild_management_svr.AddGuildAgentReq")
	proto.RegisterType((*AddGuildAgentResp)(nil), "guild_management_svr.AddGuildAgentResp")
	proto.RegisterType((*DelGuildAgentReq)(nil), "guild_management_svr.DelGuildAgentReq")
	proto.RegisterType((*DelGuildAgentResp)(nil), "guild_management_svr.DelGuildAgentResp")
	proto.RegisterType((*UpdateAgentDataPermissionReq)(nil), "guild_management_svr.UpdateAgentDataPermissionReq")
	proto.RegisterType((*UpdateAgentDataPermissionResp)(nil), "guild_management_svr.UpdateAgentDataPermissionResp")
	proto.RegisterType((*GetAgentGuildReq)(nil), "guild_management_svr.GetAgentGuildReq")
	proto.RegisterType((*GetAgentGuildResp)(nil), "guild_management_svr.GetAgentGuildResp")
	proto.RegisterType((*AgentAnchorInfo)(nil), "guild_management_svr.AgentAnchorInfo")
	proto.RegisterType((*GetAgentAnchorListReq)(nil), "guild_management_svr.GetAgentAnchorListReq")
	proto.RegisterType((*GetAgentAnchorListResp)(nil), "guild_management_svr.GetAgentAnchorListResp")
	proto.RegisterType((*GetAgentAnchorReq)(nil), "guild_management_svr.GetAgentAnchorReq")
	proto.RegisterType((*GetAgentAnchorResp)(nil), "guild_management_svr.GetAgentAnchorResp")
	proto.RegisterType((*GetAnchorAgentReq)(nil), "guild_management_svr.GetAnchorAgentReq")
	proto.RegisterType((*GetAnchorAgentResp)(nil), "guild_management_svr.GetAnchorAgentResp")
	proto.RegisterType((*AnchorErrorMsg)(nil), "guild_management_svr.AnchorErrorMsg")
	proto.RegisterType((*AddAgentAnchorReq)(nil), "guild_management_svr.AddAgentAnchorReq")
	proto.RegisterType((*AddAgentAnchorResp)(nil), "guild_management_svr.AddAgentAnchorResp")
	proto.RegisterType((*DelAgentAnchorReq)(nil), "guild_management_svr.DelAgentAnchorReq")
	proto.RegisterType((*DelAgentAnchorResp)(nil), "guild_management_svr.DelAgentAnchorResp")
	proto.RegisterType((*CheckIsNeedVerifyReq)(nil), "guild_management_svr.CheckIsNeedVerifyReq")
	proto.RegisterType((*CheckIsNeedVerifyResp)(nil), "guild_management_svr.CheckIsNeedVerifyResp")
	proto.RegisterType((*SetVerifyFlagReq)(nil), "guild_management_svr.SetVerifyFlagReq")
	proto.RegisterType((*SetVerifyFlagResp)(nil), "guild_management_svr.SetVerifyFlagResp")
	proto.RegisterType((*EsportPractitionerMonthly)(nil), "guild_management_svr.EsportPractitionerMonthly")
	proto.RegisterType((*EsportPractitionerDaily)(nil), "guild_management_svr.EsportPractitionerDaily")
	proto.RegisterType((*EsportGameMonthlyStat)(nil), "guild_management_svr.EsportGameMonthlyStat")
	proto.RegisterType((*EsportGuildMonthlyStat)(nil), "guild_management_svr.EsportGuildMonthlyStat")
	proto.RegisterType((*EsportGuildDailyStat)(nil), "guild_management_svr.EsportGuildDailyStat")
	proto.RegisterType((*GetEsportPractitionerDailyReq)(nil), "guild_management_svr.GetEsportPractitionerDailyReq")
	proto.RegisterType((*GetEsportPractitionerDailyResp)(nil), "guild_management_svr.GetEsportPractitionerDailyResp")
	proto.RegisterType((*GetEsportPractitionerMonthlyReq)(nil), "guild_management_svr.GetEsportPractitionerMonthlyReq")
	proto.RegisterType((*GetEsportPractitionerMonthlyResp)(nil), "guild_management_svr.GetEsportPractitionerMonthlyResp")
	proto.RegisterType((*GetEsportGameMonthlyStatReq)(nil), "guild_management_svr.GetEsportGameMonthlyStatReq")
	proto.RegisterType((*GetEsportGameMonthlyStatResp)(nil), "guild_management_svr.GetEsportGameMonthlyStatResp")
	proto.RegisterType((*GetEsportGuildMonthlyStatReq)(nil), "guild_management_svr.GetEsportGuildMonthlyStatReq")
	proto.RegisterType((*GetEsportGuildMonthlyStatResp)(nil), "guild_management_svr.GetEsportGuildMonthlyStatResp")
	proto.RegisterType((*GetEsportGuildDailyStatReq)(nil), "guild_management_svr.GetEsportGuildDailyStatReq")
	proto.RegisterType((*GetEsportGuildDailyStatResp)(nil), "guild_management_svr.GetEsportGuildDailyStatResp")
	proto.RegisterType((*GetWhiteWhaleAccessTokenReq)(nil), "guild_management_svr.GetWhiteWhaleAccessTokenReq")
	proto.RegisterType((*GetWhiteWhaleAccessTokenResp)(nil), "guild_management_svr.GetWhiteWhaleAccessTokenResp")
	proto.RegisterType((*CheckWhiteWhaleAccessTokenReq)(nil), "guild_management_svr.CheckWhiteWhaleAccessTokenReq")
	proto.RegisterType((*CheckWhiteWhaleAccessTokenResp)(nil), "guild_management_svr.CheckWhiteWhaleAccessTokenResp")
	proto.RegisterType((*SendGuildAgentInviteReq)(nil), "guild_management_svr.SendGuildAgentInviteReq")
	proto.RegisterType((*SendGuildAgentInviteResp)(nil), "guild_management_svr.SendGuildAgentInviteResp")
	proto.RegisterType((*CancelGuildAgentInviteReq)(nil), "guild_management_svr.CancelGuildAgentInviteReq")
	proto.RegisterType((*CancelGuildAgentInviteResp)(nil), "guild_management_svr.CancelGuildAgentInviteResp")
	proto.RegisterType((*GuildAgentInvite)(nil), "guild_management_svr.GuildAgentInvite")
	proto.RegisterType((*GetGuildAgentInviteListReq)(nil), "guild_management_svr.GetGuildAgentInviteListReq")
	proto.RegisterType((*GetGuildAgentInviteListResp)(nil), "guild_management_svr.GetGuildAgentInviteListResp")
	proto.RegisterType((*ProcGuildAgentInviteReq)(nil), "guild_management_svr.ProcGuildAgentInviteReq")
	proto.RegisterType((*ProcGuildAgentInviteResp)(nil), "guild_management_svr.ProcGuildAgentInviteResp")
	proto.RegisterType((*AddVioExportRecordReq)(nil), "guild_management_svr.AddVioExportRecordReq")
	proto.RegisterType((*AddVioExportRecordResp)(nil), "guild_management_svr.AddVioExportRecordResp")
	proto.RegisterType((*GetMultiChannelEmploymentInfoListReq)(nil), "guild_management_svr.GetMultiChannelEmploymentInfoListReq")
	proto.RegisterType((*MultiChannelEmploymentInfo)(nil), "guild_management_svr.MultiChannelEmploymentInfo")
	proto.RegisterType((*GetMultiChannelEmploymentInfoListResp)(nil), "guild_management_svr.GetMultiChannelEmploymentInfoListResp")
	proto.RegisterType((*GetMultiChannelEmploymentInfoReq)(nil), "guild_management_svr.GetMultiChannelEmploymentInfoReq")
	proto.RegisterType((*GetMultiChannelEmploymentInfoResp)(nil), "guild_management_svr.GetMultiChannelEmploymentInfoResp")
	proto.RegisterType((*SetMultiChannelEmploymentInfoReq)(nil), "guild_management_svr.SetMultiChannelEmploymentInfoReq")
	proto.RegisterType((*SetMultiChannelEmploymentInfoResp)(nil), "guild_management_svr.SetMultiChannelEmploymentInfoResp")
	proto.RegisterType((*BatchSetMultiChannelEmploymentInfoReq)(nil), "guild_management_svr.BatchSetMultiChannelEmploymentInfoReq")
	proto.RegisterType((*BatchSetMultiChannelEmploymentInfoReq_Item)(nil), "guild_management_svr.BatchSetMultiChannelEmploymentInfoReq.Item")
	proto.RegisterType((*BatchSetMultiChannelEmploymentInfoResp)(nil), "guild_management_svr.BatchSetMultiChannelEmploymentInfoResp")
	proto.RegisterType((*SetMultiChannelAdminInfoReq)(nil), "guild_management_svr.SetMultiChannelAdminInfoReq")
	proto.RegisterType((*SetMultiChannelAdminInfoResp)(nil), "guild_management_svr.SetMultiChannelAdminInfoResp")
	proto.RegisterType((*GetGuildAdminListReq)(nil), "guild_management_svr.GetGuildAdminListReq")
	proto.RegisterType((*GetGuildAdminListResp)(nil), "guild_management_svr.GetGuildAdminListResp")
	proto.RegisterType((*GetChannelListByAdminUidReq)(nil), "guild_management_svr.GetChannelListByAdminUidReq")
	proto.RegisterType((*GetChannelListByAdminUidResp)(nil), "guild_management_svr.GetChannelListByAdminUidResp")
	proto.RegisterType((*BatchGetDailyFaceCheckInfoReq)(nil), "guild_management_svr.BatchGetDailyFaceCheckInfoReq")
	proto.RegisterType((*BatchGetDailyFaceCheckInfoResp)(nil), "guild_management_svr.BatchGetDailyFaceCheckInfoResp")
	proto.RegisterType((*DailyFaceCheckInfo)(nil), "guild_management_svr.DailyFaceCheckInfo")
	proto.RegisterType((*WeeklyFaceCheckInfo)(nil), "guild_management_svr.WeeklyFaceCheckInfo")
	proto.RegisterType((*BatchGetWeeklyFaceCheckInfoReq)(nil), "guild_management_svr.BatchGetWeeklyFaceCheckInfoReq")
	proto.RegisterType((*BatchGetWeeklyFaceCheckInfoResp)(nil), "guild_management_svr.BatchGetWeeklyFaceCheckInfoResp")
	proto.RegisterType((*GuildWeeklySumFaceCheckInfo)(nil), "guild_management_svr.GuildWeeklySumFaceCheckInfo")
	proto.RegisterType((*BatchGetWeeklySumFaceCheckInfoReq)(nil), "guild_management_svr.BatchGetWeeklySumFaceCheckInfoReq")
	proto.RegisterType((*BatchGetWeeklySumFaceCheckInfoResp)(nil), "guild_management_svr.BatchGetWeeklySumFaceCheckInfoResp")
	proto.RegisterType((*GuildDailySumFaceCheckInfo)(nil), "guild_management_svr.GuildDailySumFaceCheckInfo")
	proto.RegisterType((*BatchGetDailySumFaceCheckInfoReq)(nil), "guild_management_svr.BatchGetDailySumFaceCheckInfoReq")
	proto.RegisterType((*BatchGetDailySumFaceCheckInfoResp)(nil), "guild_management_svr.BatchGetDailySumFaceCheckInfoResp")
	proto.RegisterType((*ExportFaceCheckInfoReq)(nil), "guild_management_svr.ExportFaceCheckInfoReq")
	proto.RegisterType((*ExportFaceCheckInfoResp)(nil), "guild_management_svr.ExportFaceCheckInfoResp")
	proto.RegisterType((*PreCreateUploadFileReq)(nil), "guild_management_svr.PreCreateUploadFileReq")
	proto.RegisterType((*PreCreateUploadFileResp)(nil), "guild_management_svr.PreCreateUploadFileResp")
	proto.RegisterType((*CompleteUploadFileReq)(nil), "guild_management_svr.CompleteUploadFileReq")
	proto.RegisterType((*CompleteUploadFileResp)(nil), "guild_management_svr.CompleteUploadFileResp")
	proto.RegisterType((*BatchGetUploadFileUrlReq)(nil), "guild_management_svr.BatchGetUploadFileUrlReq")
	proto.RegisterType((*BatchGetUploadFileUrlResp)(nil), "guild_management_svr.BatchGetUploadFileUrlResp")
	proto.RegisterMapType((map[string]string)(nil), "guild_management_svr.BatchGetUploadFileUrlResp.FileUrlMapEntry")
	proto.RegisterType((*BannerInfo)(nil), "guild_management_svr.BannerInfo")
	proto.RegisterType((*GetBannerInfoReq)(nil), "guild_management_svr.GetBannerInfoReq")
	proto.RegisterType((*GetBannerInfoResp)(nil), "guild_management_svr.GetBannerInfoResp")
	proto.RegisterEnum("guild_management_svr.GuildLiveDataMenuType", GuildLiveDataMenuType_name, GuildLiveDataMenuType_value)
	proto.RegisterEnum("guild_management_svr.GuildAgentMenuType", GuildAgentMenuType_name, GuildAgentMenuType_value)
	proto.RegisterEnum("guild_management_svr.GuildIncomeMenuType", GuildIncomeMenuType_name, GuildIncomeMenuType_value)
	proto.RegisterEnum("guild_management_svr.GuildMultiPlayDataType", GuildMultiPlayDataType_name, GuildMultiPlayDataType_value)
	proto.RegisterEnum("guild_management_svr.FlowCardMenuType", FlowCardMenuType_name, FlowCardMenuType_value)
	proto.RegisterEnum("guild_management_svr.AnchorContractMenuType", AnchorContractMenuType_name, AnchorContractMenuType_value)
	proto.RegisterEnum("guild_management_svr.GuildMsgMenuType", GuildMsgMenuType_name, GuildMsgMenuType_value)
	proto.RegisterEnum("guild_management_svr.GuildManageMenuType", GuildManageMenuType_name, GuildManageMenuType_value)
	proto.RegisterEnum("guild_management_svr.HomePageMenuType", HomePageMenuType_name, HomePageMenuType_value)
	proto.RegisterEnum("guild_management_svr.GuildLiveStatsDataType", GuildLiveStatsDataType_name, GuildLiveStatsDataType_value)
	proto.RegisterEnum("guild_management_svr.GuildLiveTaskDataType", GuildLiveTaskDataType_name, GuildLiveTaskDataType_value)
	proto.RegisterEnum("guild_management_svr.AnchorDataType", AnchorDataType_name, AnchorDataType_value)
	proto.RegisterEnum("guild_management_svr.AnchorPracDataType", AnchorPracDataType_name, AnchorPracDataType_value)
	proto.RegisterEnum("guild_management_svr.MultiPlayDetailDataType", MultiPlayDetailDataType_name, MultiPlayDetailDataType_value)
	proto.RegisterEnum("guild_management_svr.MultiPlayPracAnalysisDataType", MultiPlayPracAnalysisDataType_name, MultiPlayPracAnalysisDataType_value)
	proto.RegisterEnum("guild_management_svr.EsportMenuType", EsportMenuType_name, EsportMenuType_value)
	proto.RegisterEnum("guild_management_svr.ContractApplySignFuncType", ContractApplySignFuncType_name, ContractApplySignFuncType_value)
	proto.RegisterEnum("guild_management_svr.ContractCancelFuncType", ContractCancelFuncType_name, ContractCancelFuncType_value)
	proto.RegisterEnum("guild_management_svr.ContractListFuncType", ContractListFuncType_name, ContractListFuncType_value)
	proto.RegisterEnum("guild_management_svr.ContractVioFuncType", ContractVioFuncType_name, ContractVioFuncType_value)
	proto.RegisterEnum("guild_management_svr.LiveTotalDataFuncType", LiveTotalDataFuncType_name, LiveTotalDataFuncType_value)
	proto.RegisterEnum("guild_management_svr.LiveTaskDataFuncType", LiveTaskDataFuncType_name, LiveTaskDataFuncType_value)
	proto.RegisterEnum("guild_management_svr.AnchorDataFuncType", AnchorDataFuncType_name, AnchorDataFuncType_value)
	proto.RegisterEnum("guild_management_svr.AnchorPracAnalysisFuncType", AnchorPracAnalysisFuncType_name, AnchorPracAnalysisFuncType_value)
	proto.RegisterEnum("guild_management_svr.FlowCardFuncType", FlowCardFuncType_name, FlowCardFuncType_value)
	proto.RegisterEnum("guild_management_svr.MultiPlayDetailFuncType", MultiPlayDetailFuncType_name, MultiPlayDetailFuncType_value)
	proto.RegisterEnum("guild_management_svr.MultiPlayPracAnalysisFuncType", MultiPlayPracAnalysisFuncType_name, MultiPlayPracAnalysisFuncType_value)
	proto.RegisterEnum("guild_management_svr.MultiPlayOperationFuncType", MultiPlayOperationFuncType_name, MultiPlayOperationFuncType_value)
	proto.RegisterEnum("guild_management_svr.GuildMsgFuncType", GuildMsgFuncType_name, GuildMsgFuncType_value)
	proto.RegisterEnum("guild_management_svr.LiveManageFuncType", LiveManageFuncType_name, LiveManageFuncType_value)
	proto.RegisterEnum("guild_management_svr.EsportManageSkillFuncType", EsportManageSkillFuncType_name, EsportManageSkillFuncType_value)
	proto.RegisterEnum("guild_management_svr.EsportManagePerformanceFuncType", EsportManagePerformanceFuncType_name, EsportManagePerformanceFuncType_value)
	proto.RegisterEnum("guild_management_svr.MultiScheduleDataFuncType", MultiScheduleDataFuncType_name, MultiScheduleDataFuncType_value)
	proto.RegisterEnum("guild_management_svr.BusinessDiagnosisFuncType", BusinessDiagnosisFuncType_name, BusinessDiagnosisFuncType_value)
	proto.RegisterEnum("guild_management_svr.LiveAnchorFaceCheckDetailFuncType", LiveAnchorFaceCheckDetailFuncType_name, LiveAnchorFaceCheckDetailFuncType_value)
	proto.RegisterEnum("guild_management_svr.MultiAnchorFaceCheckDetailFuncType", MultiAnchorFaceCheckDetailFuncType_name, MultiAnchorFaceCheckDetailFuncType_value)
	proto.RegisterEnum("guild_management_svr.HomePageMultiFuncType", HomePageMultiFuncType_name, HomePageMultiFuncType_value)
	proto.RegisterEnum("guild_management_svr.MultiBusinessDiagFuncType", MultiBusinessDiagFuncType_name, MultiBusinessDiagFuncType_value)
	proto.RegisterEnum("guild_management_svr.AgentType", AgentType_name, AgentType_value)
	proto.RegisterEnum("guild_management_svr.VerifyScene", VerifyScene_name, VerifyScene_value)
	proto.RegisterEnum("guild_management_svr.InviteStatusType", InviteStatusType_name, InviteStatusType_value)
	proto.RegisterEnum("guild_management_svr.MultiChannelEmploymentType", MultiChannelEmploymentType_name, MultiChannelEmploymentType_value)
	proto.RegisterEnum("guild_management_svr.MultiChannelEmploymentOpt", MultiChannelEmploymentOpt_name, MultiChannelEmploymentOpt_value)
	proto.RegisterEnum("guild_management_svr.FaceCheckExportType", FaceCheckExportType_name, FaceCheckExportType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// GuildManagementSvrClient is the client API for GuildManagementSvr service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type GuildManagementSvrClient interface {
	GetGuildAgentList(ctx context.Context, in *GetGuildAgentListReq, opts ...grpc.CallOption) (*GetGuildAgentListResp, error)
	AddGuildAgent(ctx context.Context, in *AddGuildAgentReq, opts ...grpc.CallOption) (*AddGuildAgentResp, error)
	DelGuildAgent(ctx context.Context, in *DelGuildAgentReq, opts ...grpc.CallOption) (*DelGuildAgentResp, error)
	UpdateAgentDataPermission(ctx context.Context, in *UpdateAgentDataPermissionReq, opts ...grpc.CallOption) (*UpdateAgentDataPermissionResp, error)
	GetAgentGuild(ctx context.Context, in *GetAgentGuildReq, opts ...grpc.CallOption) (*GetAgentGuildResp, error)
	GetAgentAnchorList(ctx context.Context, in *GetAgentAnchorListReq, opts ...grpc.CallOption) (*GetAgentAnchorListResp, error)
	GetAgentAnchor(ctx context.Context, in *GetAgentAnchorReq, opts ...grpc.CallOption) (*GetAgentAnchorResp, error)
	GetAnchorAgent(ctx context.Context, in *GetAnchorAgentReq, opts ...grpc.CallOption) (*GetAnchorAgentResp, error)
	AddAgentAnchor(ctx context.Context, in *AddAgentAnchorReq, opts ...grpc.CallOption) (*AddAgentAnchorResp, error)
	DelAgentAnchor(ctx context.Context, in *DelAgentAnchorReq, opts ...grpc.CallOption) (*DelAgentAnchorResp, error)
	CheckIsNeedVerify(ctx context.Context, in *CheckIsNeedVerifyReq, opts ...grpc.CallOption) (*CheckIsNeedVerifyResp, error)
	SetVerifyFlag(ctx context.Context, in *SetVerifyFlagReq, opts ...grpc.CallOption) (*SetVerifyFlagResp, error)
	// 电竞从业者日信息
	GetEsportPractitionerDaily(ctx context.Context, in *GetEsportPractitionerDailyReq, opts ...grpc.CallOption) (*GetEsportPractitionerDailyResp, error)
	// 电竞从业者月信息
	GetEsportPractitionerMonthly(ctx context.Context, in *GetEsportPractitionerMonthlyReq, opts ...grpc.CallOption) (*GetEsportPractitionerMonthlyResp, error)
	// 电竞游戏月统计信息
	GetEsportGameMonthlyStat(ctx context.Context, in *GetEsportGameMonthlyStatReq, opts ...grpc.CallOption) (*GetEsportGameMonthlyStatResp, error)
	// 电竞公会月统计信息
	GetEsportGuildMonthlyStat(ctx context.Context, in *GetEsportGuildMonthlyStatReq, opts ...grpc.CallOption) (*GetEsportGuildMonthlyStatResp, error)
	// 电竞公会日统计信息
	GetEsportGuildDailyStat(ctx context.Context, in *GetEsportGuildDailyStatReq, opts ...grpc.CallOption) (*GetEsportGuildDailyStatResp, error)
	GetWhiteWhaleAccessToken(ctx context.Context, in *GetWhiteWhaleAccessTokenReq, opts ...grpc.CallOption) (*GetWhiteWhaleAccessTokenResp, error)
	CheckWhiteWhaleAccessToken(ctx context.Context, in *CheckWhiteWhaleAccessTokenReq, opts ...grpc.CallOption) (*CheckWhiteWhaleAccessTokenResp, error)
	// 发送公会代理人邀请
	SendGuildAgentInvite(ctx context.Context, in *SendGuildAgentInviteReq, opts ...grpc.CallOption) (*SendGuildAgentInviteResp, error)
	// 获取公会代理人邀请列表
	GetGuildAgentInviteList(ctx context.Context, in *GetGuildAgentInviteListReq, opts ...grpc.CallOption) (*GetGuildAgentInviteListResp, error)
	// 撤回邀请
	CancelGuildAgentInvite(ctx context.Context, in *CancelGuildAgentInviteReq, opts ...grpc.CallOption) (*CancelGuildAgentInviteResp, error)
	// 处理公会代理人邀请
	ProcGuildAgentInvite(ctx context.Context, in *ProcGuildAgentInviteReq, opts ...grpc.CallOption) (*ProcGuildAgentInviteResp, error)
	// 增加违规数据导出记录
	AddVioExportRecord(ctx context.Context, in *AddVioExportRecordReq, opts ...grpc.CallOption) (*AddVioExportRecordResp, error)
	// 获取管理员列表
	GetGuildAdminList(ctx context.Context, in *GetGuildAdminListReq, opts ...grpc.CallOption) (*GetGuildAdminListResp, error)
	// 获取多人互房间动任职信息列表
	GetMultiChannelEmploymentInfoList(ctx context.Context, in *GetMultiChannelEmploymentInfoListReq, opts ...grpc.CallOption) (*GetMultiChannelEmploymentInfoListResp, error)
	// 获取多人互房间动任职信息
	GetMultiChannelEmploymentInfo(ctx context.Context, in *GetMultiChannelEmploymentInfoReq, opts ...grpc.CallOption) (*GetMultiChannelEmploymentInfoResp, error)
	// 设置房间任职成员
	SetMultiChannelEmploymentInfo(ctx context.Context, in *SetMultiChannelEmploymentInfoReq, opts ...grpc.CallOption) (*SetMultiChannelEmploymentInfoResp, error)
	// 设置房间厅管
	SetMultiChannelAdminInfo(ctx context.Context, in *SetMultiChannelAdminInfoReq, opts ...grpc.CallOption) (*SetMultiChannelAdminInfoResp, error)
	// 批量设置任职成员
	BatchSetMultiChannelEmploymentInfo(ctx context.Context, in *BatchSetMultiChannelEmploymentInfoReq, opts ...grpc.CallOption) (*BatchSetMultiChannelEmploymentInfoResp, error)
	// 获取厅管房间
	GetChannelListByAdminUid(ctx context.Context, in *GetChannelListByAdminUidReq, opts ...grpc.CallOption) (*GetChannelListByAdminUidResp, error)
	// 公会主播人脸核验展示相关
	// 获取公会主播人脸核验日明细列表
	BatchGetDailyFaceCheckInfo(ctx context.Context, in *BatchGetDailyFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetDailyFaceCheckInfoResp, error)
	// 获取公会主播人脸核验周明细列表
	BatchGetWeeklyFaceCheckInfo(ctx context.Context, in *BatchGetWeeklyFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetWeeklyFaceCheckInfoResp, error)
	// 获取公会主播人脸核验公会每周汇总
	BatchGetWeeklySumFaceCheckInfo(ctx context.Context, in *BatchGetWeeklySumFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetWeeklySumFaceCheckInfoResp, error)
	// 获取公会主播人脸核验公会每日汇总列表
	BatchGetDailySumFaceCheckInfo(ctx context.Context, in *BatchGetDailySumFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetDailySumFaceCheckInfoResp, error)
	// 根据类型导出公会主播人脸核验明细数据
	ExportFaceCheckInfo(ctx context.Context, in *ExportFaceCheckInfoReq, opts ...grpc.CallOption) (*ExportFaceCheckInfoResp, error)
	// 预创建上传文件
	PreCreateUploadFile(ctx context.Context, in *PreCreateUploadFileReq, opts ...grpc.CallOption) (*PreCreateUploadFileResp, error)
	// 完成上传文件
	CompleteUploadFile(ctx context.Context, in *CompleteUploadFileReq, opts ...grpc.CallOption) (*CompleteUploadFileResp, error)
	// 获取上传文件Url
	BatchGetUploadFileUrl(ctx context.Context, in *BatchGetUploadFileUrlReq, opts ...grpc.CallOption) (*BatchGetUploadFileUrlResp, error)
	// 获取公会平台资讯banner
	GetBannerInfo(ctx context.Context, in *GetBannerInfoReq, opts ...grpc.CallOption) (*GetBannerInfoResp, error)
}

type guildManagementSvrClient struct {
	cc *grpc.ClientConn
}

func NewGuildManagementSvrClient(cc *grpc.ClientConn) GuildManagementSvrClient {
	return &guildManagementSvrClient{cc}
}

func (c *guildManagementSvrClient) GetGuildAgentList(ctx context.Context, in *GetGuildAgentListReq, opts ...grpc.CallOption) (*GetGuildAgentListResp, error) {
	out := new(GetGuildAgentListResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetGuildAgentList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) AddGuildAgent(ctx context.Context, in *AddGuildAgentReq, opts ...grpc.CallOption) (*AddGuildAgentResp, error) {
	out := new(AddGuildAgentResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/AddGuildAgent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) DelGuildAgent(ctx context.Context, in *DelGuildAgentReq, opts ...grpc.CallOption) (*DelGuildAgentResp, error) {
	out := new(DelGuildAgentResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/DelGuildAgent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) UpdateAgentDataPermission(ctx context.Context, in *UpdateAgentDataPermissionReq, opts ...grpc.CallOption) (*UpdateAgentDataPermissionResp, error) {
	out := new(UpdateAgentDataPermissionResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/UpdateAgentDataPermission", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetAgentGuild(ctx context.Context, in *GetAgentGuildReq, opts ...grpc.CallOption) (*GetAgentGuildResp, error) {
	out := new(GetAgentGuildResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetAgentGuild", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetAgentAnchorList(ctx context.Context, in *GetAgentAnchorListReq, opts ...grpc.CallOption) (*GetAgentAnchorListResp, error) {
	out := new(GetAgentAnchorListResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetAgentAnchorList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetAgentAnchor(ctx context.Context, in *GetAgentAnchorReq, opts ...grpc.CallOption) (*GetAgentAnchorResp, error) {
	out := new(GetAgentAnchorResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetAgentAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetAnchorAgent(ctx context.Context, in *GetAnchorAgentReq, opts ...grpc.CallOption) (*GetAnchorAgentResp, error) {
	out := new(GetAnchorAgentResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetAnchorAgent", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) AddAgentAnchor(ctx context.Context, in *AddAgentAnchorReq, opts ...grpc.CallOption) (*AddAgentAnchorResp, error) {
	out := new(AddAgentAnchorResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/AddAgentAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) DelAgentAnchor(ctx context.Context, in *DelAgentAnchorReq, opts ...grpc.CallOption) (*DelAgentAnchorResp, error) {
	out := new(DelAgentAnchorResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/DelAgentAnchor", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) CheckIsNeedVerify(ctx context.Context, in *CheckIsNeedVerifyReq, opts ...grpc.CallOption) (*CheckIsNeedVerifyResp, error) {
	out := new(CheckIsNeedVerifyResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/CheckIsNeedVerify", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) SetVerifyFlag(ctx context.Context, in *SetVerifyFlagReq, opts ...grpc.CallOption) (*SetVerifyFlagResp, error) {
	out := new(SetVerifyFlagResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/SetVerifyFlag", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetEsportPractitionerDaily(ctx context.Context, in *GetEsportPractitionerDailyReq, opts ...grpc.CallOption) (*GetEsportPractitionerDailyResp, error) {
	out := new(GetEsportPractitionerDailyResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetEsportPractitionerDaily", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetEsportPractitionerMonthly(ctx context.Context, in *GetEsportPractitionerMonthlyReq, opts ...grpc.CallOption) (*GetEsportPractitionerMonthlyResp, error) {
	out := new(GetEsportPractitionerMonthlyResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetEsportPractitionerMonthly", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetEsportGameMonthlyStat(ctx context.Context, in *GetEsportGameMonthlyStatReq, opts ...grpc.CallOption) (*GetEsportGameMonthlyStatResp, error) {
	out := new(GetEsportGameMonthlyStatResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetEsportGameMonthlyStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetEsportGuildMonthlyStat(ctx context.Context, in *GetEsportGuildMonthlyStatReq, opts ...grpc.CallOption) (*GetEsportGuildMonthlyStatResp, error) {
	out := new(GetEsportGuildMonthlyStatResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetEsportGuildMonthlyStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetEsportGuildDailyStat(ctx context.Context, in *GetEsportGuildDailyStatReq, opts ...grpc.CallOption) (*GetEsportGuildDailyStatResp, error) {
	out := new(GetEsportGuildDailyStatResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetEsportGuildDailyStat", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetWhiteWhaleAccessToken(ctx context.Context, in *GetWhiteWhaleAccessTokenReq, opts ...grpc.CallOption) (*GetWhiteWhaleAccessTokenResp, error) {
	out := new(GetWhiteWhaleAccessTokenResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetWhiteWhaleAccessToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) CheckWhiteWhaleAccessToken(ctx context.Context, in *CheckWhiteWhaleAccessTokenReq, opts ...grpc.CallOption) (*CheckWhiteWhaleAccessTokenResp, error) {
	out := new(CheckWhiteWhaleAccessTokenResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/CheckWhiteWhaleAccessToken", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) SendGuildAgentInvite(ctx context.Context, in *SendGuildAgentInviteReq, opts ...grpc.CallOption) (*SendGuildAgentInviteResp, error) {
	out := new(SendGuildAgentInviteResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/SendGuildAgentInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetGuildAgentInviteList(ctx context.Context, in *GetGuildAgentInviteListReq, opts ...grpc.CallOption) (*GetGuildAgentInviteListResp, error) {
	out := new(GetGuildAgentInviteListResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetGuildAgentInviteList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) CancelGuildAgentInvite(ctx context.Context, in *CancelGuildAgentInviteReq, opts ...grpc.CallOption) (*CancelGuildAgentInviteResp, error) {
	out := new(CancelGuildAgentInviteResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/CancelGuildAgentInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) ProcGuildAgentInvite(ctx context.Context, in *ProcGuildAgentInviteReq, opts ...grpc.CallOption) (*ProcGuildAgentInviteResp, error) {
	out := new(ProcGuildAgentInviteResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/ProcGuildAgentInvite", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) AddVioExportRecord(ctx context.Context, in *AddVioExportRecordReq, opts ...grpc.CallOption) (*AddVioExportRecordResp, error) {
	out := new(AddVioExportRecordResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/AddVioExportRecord", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetGuildAdminList(ctx context.Context, in *GetGuildAdminListReq, opts ...grpc.CallOption) (*GetGuildAdminListResp, error) {
	out := new(GetGuildAdminListResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetGuildAdminList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetMultiChannelEmploymentInfoList(ctx context.Context, in *GetMultiChannelEmploymentInfoListReq, opts ...grpc.CallOption) (*GetMultiChannelEmploymentInfoListResp, error) {
	out := new(GetMultiChannelEmploymentInfoListResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetMultiChannelEmploymentInfoList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetMultiChannelEmploymentInfo(ctx context.Context, in *GetMultiChannelEmploymentInfoReq, opts ...grpc.CallOption) (*GetMultiChannelEmploymentInfoResp, error) {
	out := new(GetMultiChannelEmploymentInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetMultiChannelEmploymentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) SetMultiChannelEmploymentInfo(ctx context.Context, in *SetMultiChannelEmploymentInfoReq, opts ...grpc.CallOption) (*SetMultiChannelEmploymentInfoResp, error) {
	out := new(SetMultiChannelEmploymentInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/SetMultiChannelEmploymentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) SetMultiChannelAdminInfo(ctx context.Context, in *SetMultiChannelAdminInfoReq, opts ...grpc.CallOption) (*SetMultiChannelAdminInfoResp, error) {
	out := new(SetMultiChannelAdminInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/SetMultiChannelAdminInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) BatchSetMultiChannelEmploymentInfo(ctx context.Context, in *BatchSetMultiChannelEmploymentInfoReq, opts ...grpc.CallOption) (*BatchSetMultiChannelEmploymentInfoResp, error) {
	out := new(BatchSetMultiChannelEmploymentInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/BatchSetMultiChannelEmploymentInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetChannelListByAdminUid(ctx context.Context, in *GetChannelListByAdminUidReq, opts ...grpc.CallOption) (*GetChannelListByAdminUidResp, error) {
	out := new(GetChannelListByAdminUidResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetChannelListByAdminUid", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) BatchGetDailyFaceCheckInfo(ctx context.Context, in *BatchGetDailyFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetDailyFaceCheckInfoResp, error) {
	out := new(BatchGetDailyFaceCheckInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/BatchGetDailyFaceCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) BatchGetWeeklyFaceCheckInfo(ctx context.Context, in *BatchGetWeeklyFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetWeeklyFaceCheckInfoResp, error) {
	out := new(BatchGetWeeklyFaceCheckInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/BatchGetWeeklyFaceCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) BatchGetWeeklySumFaceCheckInfo(ctx context.Context, in *BatchGetWeeklySumFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetWeeklySumFaceCheckInfoResp, error) {
	out := new(BatchGetWeeklySumFaceCheckInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/BatchGetWeeklySumFaceCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) BatchGetDailySumFaceCheckInfo(ctx context.Context, in *BatchGetDailySumFaceCheckInfoReq, opts ...grpc.CallOption) (*BatchGetDailySumFaceCheckInfoResp, error) {
	out := new(BatchGetDailySumFaceCheckInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/BatchGetDailySumFaceCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) ExportFaceCheckInfo(ctx context.Context, in *ExportFaceCheckInfoReq, opts ...grpc.CallOption) (*ExportFaceCheckInfoResp, error) {
	out := new(ExportFaceCheckInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/ExportFaceCheckInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) PreCreateUploadFile(ctx context.Context, in *PreCreateUploadFileReq, opts ...grpc.CallOption) (*PreCreateUploadFileResp, error) {
	out := new(PreCreateUploadFileResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/PreCreateUploadFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) CompleteUploadFile(ctx context.Context, in *CompleteUploadFileReq, opts ...grpc.CallOption) (*CompleteUploadFileResp, error) {
	out := new(CompleteUploadFileResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/CompleteUploadFile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) BatchGetUploadFileUrl(ctx context.Context, in *BatchGetUploadFileUrlReq, opts ...grpc.CallOption) (*BatchGetUploadFileUrlResp, error) {
	out := new(BatchGetUploadFileUrlResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/BatchGetUploadFileUrl", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *guildManagementSvrClient) GetBannerInfo(ctx context.Context, in *GetBannerInfoReq, opts ...grpc.CallOption) (*GetBannerInfoResp, error) {
	out := new(GetBannerInfoResp)
	err := c.cc.Invoke(ctx, "/guild_management_svr.GuildManagementSvr/GetBannerInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GuildManagementSvrServer is the server API for GuildManagementSvr service.
type GuildManagementSvrServer interface {
	GetGuildAgentList(context.Context, *GetGuildAgentListReq) (*GetGuildAgentListResp, error)
	AddGuildAgent(context.Context, *AddGuildAgentReq) (*AddGuildAgentResp, error)
	DelGuildAgent(context.Context, *DelGuildAgentReq) (*DelGuildAgentResp, error)
	UpdateAgentDataPermission(context.Context, *UpdateAgentDataPermissionReq) (*UpdateAgentDataPermissionResp, error)
	GetAgentGuild(context.Context, *GetAgentGuildReq) (*GetAgentGuildResp, error)
	GetAgentAnchorList(context.Context, *GetAgentAnchorListReq) (*GetAgentAnchorListResp, error)
	GetAgentAnchor(context.Context, *GetAgentAnchorReq) (*GetAgentAnchorResp, error)
	GetAnchorAgent(context.Context, *GetAnchorAgentReq) (*GetAnchorAgentResp, error)
	AddAgentAnchor(context.Context, *AddAgentAnchorReq) (*AddAgentAnchorResp, error)
	DelAgentAnchor(context.Context, *DelAgentAnchorReq) (*DelAgentAnchorResp, error)
	CheckIsNeedVerify(context.Context, *CheckIsNeedVerifyReq) (*CheckIsNeedVerifyResp, error)
	SetVerifyFlag(context.Context, *SetVerifyFlagReq) (*SetVerifyFlagResp, error)
	// 电竞从业者日信息
	GetEsportPractitionerDaily(context.Context, *GetEsportPractitionerDailyReq) (*GetEsportPractitionerDailyResp, error)
	// 电竞从业者月信息
	GetEsportPractitionerMonthly(context.Context, *GetEsportPractitionerMonthlyReq) (*GetEsportPractitionerMonthlyResp, error)
	// 电竞游戏月统计信息
	GetEsportGameMonthlyStat(context.Context, *GetEsportGameMonthlyStatReq) (*GetEsportGameMonthlyStatResp, error)
	// 电竞公会月统计信息
	GetEsportGuildMonthlyStat(context.Context, *GetEsportGuildMonthlyStatReq) (*GetEsportGuildMonthlyStatResp, error)
	// 电竞公会日统计信息
	GetEsportGuildDailyStat(context.Context, *GetEsportGuildDailyStatReq) (*GetEsportGuildDailyStatResp, error)
	GetWhiteWhaleAccessToken(context.Context, *GetWhiteWhaleAccessTokenReq) (*GetWhiteWhaleAccessTokenResp, error)
	CheckWhiteWhaleAccessToken(context.Context, *CheckWhiteWhaleAccessTokenReq) (*CheckWhiteWhaleAccessTokenResp, error)
	// 发送公会代理人邀请
	SendGuildAgentInvite(context.Context, *SendGuildAgentInviteReq) (*SendGuildAgentInviteResp, error)
	// 获取公会代理人邀请列表
	GetGuildAgentInviteList(context.Context, *GetGuildAgentInviteListReq) (*GetGuildAgentInviteListResp, error)
	// 撤回邀请
	CancelGuildAgentInvite(context.Context, *CancelGuildAgentInviteReq) (*CancelGuildAgentInviteResp, error)
	// 处理公会代理人邀请
	ProcGuildAgentInvite(context.Context, *ProcGuildAgentInviteReq) (*ProcGuildAgentInviteResp, error)
	// 增加违规数据导出记录
	AddVioExportRecord(context.Context, *AddVioExportRecordReq) (*AddVioExportRecordResp, error)
	// 获取管理员列表
	GetGuildAdminList(context.Context, *GetGuildAdminListReq) (*GetGuildAdminListResp, error)
	// 获取多人互房间动任职信息列表
	GetMultiChannelEmploymentInfoList(context.Context, *GetMultiChannelEmploymentInfoListReq) (*GetMultiChannelEmploymentInfoListResp, error)
	// 获取多人互房间动任职信息
	GetMultiChannelEmploymentInfo(context.Context, *GetMultiChannelEmploymentInfoReq) (*GetMultiChannelEmploymentInfoResp, error)
	// 设置房间任职成员
	SetMultiChannelEmploymentInfo(context.Context, *SetMultiChannelEmploymentInfoReq) (*SetMultiChannelEmploymentInfoResp, error)
	// 设置房间厅管
	SetMultiChannelAdminInfo(context.Context, *SetMultiChannelAdminInfoReq) (*SetMultiChannelAdminInfoResp, error)
	// 批量设置任职成员
	BatchSetMultiChannelEmploymentInfo(context.Context, *BatchSetMultiChannelEmploymentInfoReq) (*BatchSetMultiChannelEmploymentInfoResp, error)
	// 获取厅管房间
	GetChannelListByAdminUid(context.Context, *GetChannelListByAdminUidReq) (*GetChannelListByAdminUidResp, error)
	// 公会主播人脸核验展示相关
	// 获取公会主播人脸核验日明细列表
	BatchGetDailyFaceCheckInfo(context.Context, *BatchGetDailyFaceCheckInfoReq) (*BatchGetDailyFaceCheckInfoResp, error)
	// 获取公会主播人脸核验周明细列表
	BatchGetWeeklyFaceCheckInfo(context.Context, *BatchGetWeeklyFaceCheckInfoReq) (*BatchGetWeeklyFaceCheckInfoResp, error)
	// 获取公会主播人脸核验公会每周汇总
	BatchGetWeeklySumFaceCheckInfo(context.Context, *BatchGetWeeklySumFaceCheckInfoReq) (*BatchGetWeeklySumFaceCheckInfoResp, error)
	// 获取公会主播人脸核验公会每日汇总列表
	BatchGetDailySumFaceCheckInfo(context.Context, *BatchGetDailySumFaceCheckInfoReq) (*BatchGetDailySumFaceCheckInfoResp, error)
	// 根据类型导出公会主播人脸核验明细数据
	ExportFaceCheckInfo(context.Context, *ExportFaceCheckInfoReq) (*ExportFaceCheckInfoResp, error)
	// 预创建上传文件
	PreCreateUploadFile(context.Context, *PreCreateUploadFileReq) (*PreCreateUploadFileResp, error)
	// 完成上传文件
	CompleteUploadFile(context.Context, *CompleteUploadFileReq) (*CompleteUploadFileResp, error)
	// 获取上传文件Url
	BatchGetUploadFileUrl(context.Context, *BatchGetUploadFileUrlReq) (*BatchGetUploadFileUrlResp, error)
	// 获取公会平台资讯banner
	GetBannerInfo(context.Context, *GetBannerInfoReq) (*GetBannerInfoResp, error)
}

func RegisterGuildManagementSvrServer(s *grpc.Server, srv GuildManagementSvrServer) {
	s.RegisterService(&_GuildManagementSvr_serviceDesc, srv)
}

func _GuildManagementSvr_GetGuildAgentList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildAgentListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetGuildAgentList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetGuildAgentList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetGuildAgentList(ctx, req.(*GetGuildAgentListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_AddGuildAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddGuildAgentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).AddGuildAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/AddGuildAgent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).AddGuildAgent(ctx, req.(*AddGuildAgentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_DelGuildAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGuildAgentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).DelGuildAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/DelGuildAgent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).DelGuildAgent(ctx, req.(*DelGuildAgentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_UpdateAgentDataPermission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAgentDataPermissionReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).UpdateAgentDataPermission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/UpdateAgentDataPermission",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).UpdateAgentDataPermission(ctx, req.(*UpdateAgentDataPermissionReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetAgentGuild_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentGuildReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetAgentGuild(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetAgentGuild",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetAgentGuild(ctx, req.(*GetAgentGuildReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetAgentAnchorList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentAnchorListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetAgentAnchorList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetAgentAnchorList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetAgentAnchorList(ctx, req.(*GetAgentAnchorListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetAgentAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetAgentAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetAgentAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetAgentAnchor(ctx, req.(*GetAgentAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetAnchorAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnchorAgentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetAnchorAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetAnchorAgent",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetAnchorAgent(ctx, req.(*GetAnchorAgentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_AddAgentAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddAgentAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).AddAgentAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/AddAgentAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).AddAgentAnchor(ctx, req.(*AddAgentAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_DelAgentAnchor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelAgentAnchorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).DelAgentAnchor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/DelAgentAnchor",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).DelAgentAnchor(ctx, req.(*DelAgentAnchorReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_CheckIsNeedVerify_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIsNeedVerifyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).CheckIsNeedVerify(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/CheckIsNeedVerify",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).CheckIsNeedVerify(ctx, req.(*CheckIsNeedVerifyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_SetVerifyFlag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetVerifyFlagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).SetVerifyFlag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/SetVerifyFlag",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).SetVerifyFlag(ctx, req.(*SetVerifyFlagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetEsportPractitionerDaily_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportPractitionerDailyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetEsportPractitionerDaily(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetEsportPractitionerDaily",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetEsportPractitionerDaily(ctx, req.(*GetEsportPractitionerDailyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetEsportPractitionerMonthly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportPractitionerMonthlyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetEsportPractitionerMonthly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetEsportPractitionerMonthly",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetEsportPractitionerMonthly(ctx, req.(*GetEsportPractitionerMonthlyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetEsportGameMonthlyStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportGameMonthlyStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetEsportGameMonthlyStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetEsportGameMonthlyStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetEsportGameMonthlyStat(ctx, req.(*GetEsportGameMonthlyStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetEsportGuildMonthlyStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportGuildMonthlyStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetEsportGuildMonthlyStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetEsportGuildMonthlyStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetEsportGuildMonthlyStat(ctx, req.(*GetEsportGuildMonthlyStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetEsportGuildDailyStat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEsportGuildDailyStatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetEsportGuildDailyStat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetEsportGuildDailyStat",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetEsportGuildDailyStat(ctx, req.(*GetEsportGuildDailyStatReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetWhiteWhaleAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetWhiteWhaleAccessTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetWhiteWhaleAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetWhiteWhaleAccessToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetWhiteWhaleAccessToken(ctx, req.(*GetWhiteWhaleAccessTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_CheckWhiteWhaleAccessToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckWhiteWhaleAccessTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).CheckWhiteWhaleAccessToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/CheckWhiteWhaleAccessToken",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).CheckWhiteWhaleAccessToken(ctx, req.(*CheckWhiteWhaleAccessTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_SendGuildAgentInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendGuildAgentInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).SendGuildAgentInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/SendGuildAgentInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).SendGuildAgentInvite(ctx, req.(*SendGuildAgentInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetGuildAgentInviteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildAgentInviteListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetGuildAgentInviteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetGuildAgentInviteList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetGuildAgentInviteList(ctx, req.(*GetGuildAgentInviteListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_CancelGuildAgentInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelGuildAgentInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).CancelGuildAgentInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/CancelGuildAgentInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).CancelGuildAgentInvite(ctx, req.(*CancelGuildAgentInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_ProcGuildAgentInvite_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcGuildAgentInviteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).ProcGuildAgentInvite(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/ProcGuildAgentInvite",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).ProcGuildAgentInvite(ctx, req.(*ProcGuildAgentInviteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_AddVioExportRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddVioExportRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).AddVioExportRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/AddVioExportRecord",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).AddVioExportRecord(ctx, req.(*AddVioExportRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetGuildAdminList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGuildAdminListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetGuildAdminList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetGuildAdminList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetGuildAdminList(ctx, req.(*GetGuildAdminListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetMultiChannelEmploymentInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiChannelEmploymentInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetMultiChannelEmploymentInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetMultiChannelEmploymentInfoList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetMultiChannelEmploymentInfoList(ctx, req.(*GetMultiChannelEmploymentInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetMultiChannelEmploymentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMultiChannelEmploymentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetMultiChannelEmploymentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetMultiChannelEmploymentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetMultiChannelEmploymentInfo(ctx, req.(*GetMultiChannelEmploymentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_SetMultiChannelEmploymentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMultiChannelEmploymentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).SetMultiChannelEmploymentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/SetMultiChannelEmploymentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).SetMultiChannelEmploymentInfo(ctx, req.(*SetMultiChannelEmploymentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_SetMultiChannelAdminInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMultiChannelAdminInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).SetMultiChannelAdminInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/SetMultiChannelAdminInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).SetMultiChannelAdminInfo(ctx, req.(*SetMultiChannelAdminInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_BatchSetMultiChannelEmploymentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchSetMultiChannelEmploymentInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).BatchSetMultiChannelEmploymentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/BatchSetMultiChannelEmploymentInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).BatchSetMultiChannelEmploymentInfo(ctx, req.(*BatchSetMultiChannelEmploymentInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetChannelListByAdminUid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChannelListByAdminUidReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetChannelListByAdminUid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetChannelListByAdminUid",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetChannelListByAdminUid(ctx, req.(*GetChannelListByAdminUidReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_BatchGetDailyFaceCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetDailyFaceCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).BatchGetDailyFaceCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/BatchGetDailyFaceCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).BatchGetDailyFaceCheckInfo(ctx, req.(*BatchGetDailyFaceCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_BatchGetWeeklyFaceCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWeeklyFaceCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).BatchGetWeeklyFaceCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/BatchGetWeeklyFaceCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).BatchGetWeeklyFaceCheckInfo(ctx, req.(*BatchGetWeeklyFaceCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_BatchGetWeeklySumFaceCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWeeklySumFaceCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).BatchGetWeeklySumFaceCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/BatchGetWeeklySumFaceCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).BatchGetWeeklySumFaceCheckInfo(ctx, req.(*BatchGetWeeklySumFaceCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_BatchGetDailySumFaceCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetDailySumFaceCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).BatchGetDailySumFaceCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/BatchGetDailySumFaceCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).BatchGetDailySumFaceCheckInfo(ctx, req.(*BatchGetDailySumFaceCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_ExportFaceCheckInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExportFaceCheckInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).ExportFaceCheckInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/ExportFaceCheckInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).ExportFaceCheckInfo(ctx, req.(*ExportFaceCheckInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_PreCreateUploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PreCreateUploadFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).PreCreateUploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/PreCreateUploadFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).PreCreateUploadFile(ctx, req.(*PreCreateUploadFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_CompleteUploadFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteUploadFileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).CompleteUploadFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/CompleteUploadFile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).CompleteUploadFile(ctx, req.(*CompleteUploadFileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_BatchGetUploadFileUrl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetUploadFileUrlReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).BatchGetUploadFileUrl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/BatchGetUploadFileUrl",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).BatchGetUploadFileUrl(ctx, req.(*BatchGetUploadFileUrlReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _GuildManagementSvr_GetBannerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBannerInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GuildManagementSvrServer).GetBannerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/guild_management_svr.GuildManagementSvr/GetBannerInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GuildManagementSvrServer).GetBannerInfo(ctx, req.(*GetBannerInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _GuildManagementSvr_serviceDesc = grpc.ServiceDesc{
	ServiceName: "guild_management_svr.GuildManagementSvr",
	HandlerType: (*GuildManagementSvrServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetGuildAgentList",
			Handler:    _GuildManagementSvr_GetGuildAgentList_Handler,
		},
		{
			MethodName: "AddGuildAgent",
			Handler:    _GuildManagementSvr_AddGuildAgent_Handler,
		},
		{
			MethodName: "DelGuildAgent",
			Handler:    _GuildManagementSvr_DelGuildAgent_Handler,
		},
		{
			MethodName: "UpdateAgentDataPermission",
			Handler:    _GuildManagementSvr_UpdateAgentDataPermission_Handler,
		},
		{
			MethodName: "GetAgentGuild",
			Handler:    _GuildManagementSvr_GetAgentGuild_Handler,
		},
		{
			MethodName: "GetAgentAnchorList",
			Handler:    _GuildManagementSvr_GetAgentAnchorList_Handler,
		},
		{
			MethodName: "GetAgentAnchor",
			Handler:    _GuildManagementSvr_GetAgentAnchor_Handler,
		},
		{
			MethodName: "GetAnchorAgent",
			Handler:    _GuildManagementSvr_GetAnchorAgent_Handler,
		},
		{
			MethodName: "AddAgentAnchor",
			Handler:    _GuildManagementSvr_AddAgentAnchor_Handler,
		},
		{
			MethodName: "DelAgentAnchor",
			Handler:    _GuildManagementSvr_DelAgentAnchor_Handler,
		},
		{
			MethodName: "CheckIsNeedVerify",
			Handler:    _GuildManagementSvr_CheckIsNeedVerify_Handler,
		},
		{
			MethodName: "SetVerifyFlag",
			Handler:    _GuildManagementSvr_SetVerifyFlag_Handler,
		},
		{
			MethodName: "GetEsportPractitionerDaily",
			Handler:    _GuildManagementSvr_GetEsportPractitionerDaily_Handler,
		},
		{
			MethodName: "GetEsportPractitionerMonthly",
			Handler:    _GuildManagementSvr_GetEsportPractitionerMonthly_Handler,
		},
		{
			MethodName: "GetEsportGameMonthlyStat",
			Handler:    _GuildManagementSvr_GetEsportGameMonthlyStat_Handler,
		},
		{
			MethodName: "GetEsportGuildMonthlyStat",
			Handler:    _GuildManagementSvr_GetEsportGuildMonthlyStat_Handler,
		},
		{
			MethodName: "GetEsportGuildDailyStat",
			Handler:    _GuildManagementSvr_GetEsportGuildDailyStat_Handler,
		},
		{
			MethodName: "GetWhiteWhaleAccessToken",
			Handler:    _GuildManagementSvr_GetWhiteWhaleAccessToken_Handler,
		},
		{
			MethodName: "CheckWhiteWhaleAccessToken",
			Handler:    _GuildManagementSvr_CheckWhiteWhaleAccessToken_Handler,
		},
		{
			MethodName: "SendGuildAgentInvite",
			Handler:    _GuildManagementSvr_SendGuildAgentInvite_Handler,
		},
		{
			MethodName: "GetGuildAgentInviteList",
			Handler:    _GuildManagementSvr_GetGuildAgentInviteList_Handler,
		},
		{
			MethodName: "CancelGuildAgentInvite",
			Handler:    _GuildManagementSvr_CancelGuildAgentInvite_Handler,
		},
		{
			MethodName: "ProcGuildAgentInvite",
			Handler:    _GuildManagementSvr_ProcGuildAgentInvite_Handler,
		},
		{
			MethodName: "AddVioExportRecord",
			Handler:    _GuildManagementSvr_AddVioExportRecord_Handler,
		},
		{
			MethodName: "GetGuildAdminList",
			Handler:    _GuildManagementSvr_GetGuildAdminList_Handler,
		},
		{
			MethodName: "GetMultiChannelEmploymentInfoList",
			Handler:    _GuildManagementSvr_GetMultiChannelEmploymentInfoList_Handler,
		},
		{
			MethodName: "GetMultiChannelEmploymentInfo",
			Handler:    _GuildManagementSvr_GetMultiChannelEmploymentInfo_Handler,
		},
		{
			MethodName: "SetMultiChannelEmploymentInfo",
			Handler:    _GuildManagementSvr_SetMultiChannelEmploymentInfo_Handler,
		},
		{
			MethodName: "SetMultiChannelAdminInfo",
			Handler:    _GuildManagementSvr_SetMultiChannelAdminInfo_Handler,
		},
		{
			MethodName: "BatchSetMultiChannelEmploymentInfo",
			Handler:    _GuildManagementSvr_BatchSetMultiChannelEmploymentInfo_Handler,
		},
		{
			MethodName: "GetChannelListByAdminUid",
			Handler:    _GuildManagementSvr_GetChannelListByAdminUid_Handler,
		},
		{
			MethodName: "BatchGetDailyFaceCheckInfo",
			Handler:    _GuildManagementSvr_BatchGetDailyFaceCheckInfo_Handler,
		},
		{
			MethodName: "BatchGetWeeklyFaceCheckInfo",
			Handler:    _GuildManagementSvr_BatchGetWeeklyFaceCheckInfo_Handler,
		},
		{
			MethodName: "BatchGetWeeklySumFaceCheckInfo",
			Handler:    _GuildManagementSvr_BatchGetWeeklySumFaceCheckInfo_Handler,
		},
		{
			MethodName: "BatchGetDailySumFaceCheckInfo",
			Handler:    _GuildManagementSvr_BatchGetDailySumFaceCheckInfo_Handler,
		},
		{
			MethodName: "ExportFaceCheckInfo",
			Handler:    _GuildManagementSvr_ExportFaceCheckInfo_Handler,
		},
		{
			MethodName: "PreCreateUploadFile",
			Handler:    _GuildManagementSvr_PreCreateUploadFile_Handler,
		},
		{
			MethodName: "CompleteUploadFile",
			Handler:    _GuildManagementSvr_CompleteUploadFile_Handler,
		},
		{
			MethodName: "BatchGetUploadFileUrl",
			Handler:    _GuildManagementSvr_BatchGetUploadFileUrl_Handler,
		},
		{
			MethodName: "GetBannerInfo",
			Handler:    _GuildManagementSvr_GetBannerInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "tt/quicksilver/guild-management-svr/guild-management-svr.proto",
}

func init() {
	proto.RegisterFile("tt/quicksilver/guild-management-svr/guild-management-svr.proto", fileDescriptor_guild_management_svr_38ed9aba41a644c3)
}

var fileDescriptor_guild_management_svr_38ed9aba41a644c3 = []byte{
	// 6359 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x3d, 0x5b, 0x6c, 0x1c, 0xc9,
	0x71, 0x9c, 0xe5, 0x8a, 0xdc, 0x2d, 0xbe, 0x46, 0xcd, 0xf7, 0x52, 0x0f, 0x6a, 0x4e, 0xd2, 0x29,
	0x7b, 0x77, 0xd2, 0x9d, 0xce, 0x77, 0xe7, 0xe8, 0x9c, 0xf3, 0x51, 0x24, 0x45, 0x51, 0x16, 0x25,
	0x7a, 0x97, 0x94, 0xce, 0x06, 0xe2, 0xc5, 0x68, 0xb7, 0x49, 0x8e, 0xb9, 0x3b, 0x33, 0x37, 0x33,
	0x4b, 0x1d, 0x0d, 0xd8, 0x16, 0x2e, 0x88, 0xe1, 0x18, 0x01, 0x92, 0x18, 0x88, 0x8d, 0x38, 0x48,
	0x60, 0x23, 0x3f, 0x01, 0xf2, 0x11, 0x04, 0xc8, 0x03, 0xc8, 0xe3, 0x27, 0x9f, 0x01, 0x12, 0x20,
	0x09, 0xe0, 0x04, 0xf9, 0x08, 0x02, 0x04, 0xc8, 0x47, 0x80, 0x7c, 0x04, 0xc9, 0x5f, 0x7e, 0x82,
	0x7e, 0x4e, 0xcf, 0xa3, 0x67, 0x49, 0xe9, 0x12, 0x9c, 0x01, 0x7f, 0x71, 0xa7, 0xaa, 0xba, 0xba,
	0xba, 0xba, 0xba, 0xaa, 0x7a, 0xa6, 0xab, 0x09, 0xef, 0x45, 0xd1, 0x8d, 0x0f, 0xfb, 0x4e, 0xfb,
	0x30, 0x74, 0xba, 0x47, 0x38, 0xb8, 0xb1, 0xdf, 0x77, 0xba, 0x9d, 0xd7, 0x7a, 0xb6, 0x6b, 0xef,
	0xe3, 0x1e, 0x76, 0xa3, 0xd7, 0xc2, 0xa3, 0x7c, 0xe0, 0x75, 0x3f, 0xf0, 0x22, 0x0f, 0xcd, 0x50,
	0x5c, 0x2b, 0xc6, 0xb5, 0xc2, 0xa3, 0xc0, 0xfa, 0xa7, 0x12, 0x4c, 0x6e, 0x61, 0xb7, 0xbf, 0x8d,
	0x83, 0x9e, 0x13, 0x86, 0x8e, 0xe7, 0xa2, 0x9b, 0xc0, 0x48, 0xef, 0x3b, 0x47, 0x78, 0xcd, 0x8e,
	0x6c, 0x8e, 0x5e, 0x30, 0x96, 0x8d, 0x6b, 0x13, 0x8d, 0x5c, 0x1c, 0x7a, 0x15, 0xce, 0x52, 0xf8,
	0xca, 0x3e, 0x76, 0x23, 0xd1, 0xa0, 0x44, 0x1b, 0x64, 0x11, 0xe8, 0x1a, 0x4c, 0xed, 0x75, 0xbd,
	0xa7, 0xab, 0x76, 0xd0, 0x11, 0xb4, 0xc3, 0x94, 0x36, 0x0d, 0x46, 0x35, 0xa8, 0xb4, 0x3d, 0x37,
	0x0a, 0xec, 0x76, 0xb4, 0x50, 0xa6, 0x24, 0xf2, 0x19, 0x9d, 0x83, 0x6a, 0xaf, 0xdf, 0x8d, 0x9c,
	0xed, 0xae, 0x7d, 0xbc, 0x70, 0x86, 0x22, 0x63, 0x00, 0x32, 0x61, 0xb8, 0x17, 0xee, 0x2f, 0x8c,
	0x50, 0x38, 0xf9, 0x89, 0x96, 0x61, 0x8c, 0x8a, 0xb2, 0x45, 0x35, 0xb0, 0x30, 0x4a, 0x31, 0x2a,
	0x08, 0x5d, 0x86, 0x09, 0x1c, 0xfa, 0x5e, 0x20, 0x47, 0x50, 0xa1, 0x34, 0x49, 0x20, 0x91, 0xfe,
	0xc0, 0xeb, 0xe1, 0x6d, 0x7b, 0x1f, 0x0b, 0xba, 0x2a, 0x93, 0x3e, 0x05, 0xb6, 0xfe, 0xca, 0x00,
	0x24, 0x34, 0xa5, 0x28, 0xf8, 0x33, 0x30, 0x2b, 0x95, 0xd8, 0x8c, 0xec, 0x28, 0xe4, 0x78, 0xae,
	0xe1, 0x7c, 0x24, 0xaa, 0x83, 0x49, 0x11, 0x3b, 0x76, 0x78, 0x28, 0x1a, 0x30, 0x0d, 0x67, 0xe0,
	0x64, 0x20, 0xb6, 0xdb, 0x3e, 0xf0, 0x02, 0x41, 0xc8, 0xd4, 0x9b, 0x04, 0x92, 0x49, 0x63, 0x80,
	0xed, 0xc0, 0x6e, 0x0b, 0x4a, 0xa6, 0xe5, 0x2c, 0xc2, 0xfa, 0xcb, 0x0a, 0xa0, 0x3b, 0x7d, 0xb7,
	0x1d, 0x39, 0x9e, 0xab, 0x0c, 0xe6, 0x1c, 0x54, 0x6d, 0xdf, 0xef, 0x1e, 0x37, 0x9d, 0x7d, 0x97,
	0x0f, 0x20, 0x06, 0xa0, 0x0b, 0x00, 0x6d, 0xdb, 0x6d, 0xe3, 0x2e, 0x45, 0x33, 0x71, 0x15, 0x08,
	0xb2, 0x60, 0x5c, 0xcc, 0xe7, 0x7d, 0x27, 0x8c, 0xb8, 0x9c, 0x09, 0x18, 0x99, 0x37, 0xf1, 0xfc,
	0xc8, 0xf1, 0xb8, 0x80, 0x2a, 0x88, 0x0c, 0xb7, 0xeb, 0x1c, 0xe1, 0x1d, 0x2f, 0xb2, 0xbb, 0x44,
	0x5c, 0x6e, 0x0d, 0x49, 0x20, 0xe9, 0x8b, 0x02, 0xb8, 0x9e, 0xb8, 0x69, 0x24, 0x60, 0x44, 0xde,
	0x58, 0x47, 0xdc, 0x44, 0x14, 0x08, 0xba, 0x0e, 0x28, 0xd6, 0xcc, 0x8a, 0x6b, 0x77, 0x8f, 0x43,
	0x27, 0xe4, 0x66, 0x92, 0x83, 0x21, 0xf6, 0x2b, 0x4c, 0x9a, 0x1b, 0x89, 0x7c, 0x26, 0x76, 0x24,
	0xcd, 0x75, 0x0d, 0x47, 0xb6, 0xd3, 0x5d, 0x00, 0x66, 0x47, 0x29, 0x30, 0x31, 0x18, 0x09, 0x4a,
	0x74, 0x3c, 0xc6, 0x0c, 0x26, 0x17, 0x29, 0x56, 0xc0, 0x78, 0xbc, 0x02, 0x2e, 0xc3, 0x84, 0x24,
	0x7d, 0xe8, 0xe3, 0x60, 0x61, 0x82, 0xe9, 0x29, 0x01, 0x24, 0x66, 0x21, 0x01, 0x77, 0xed, 0x6e,
	0x97, 0x28, 0x67, 0x61, 0x92, 0x99, 0x45, 0x06, 0x41, 0x34, 0x46, 0x34, 0xc8, 0x17, 0xd5, 0x14,
	0xd3, 0x58, 0x0c, 0x41, 0xb7, 0x60, 0x81, 0x2f, 0x1f, 0xfa, 0xdc, 0x3c, 0x74, 0xba, 0x5d, 0x62,
	0x46, 0xc4, 0xd6, 0x4c, 0x4a, 0xad, 0xc5, 0xa3, 0x3b, 0x70, 0x41, 0xc5, 0x6d, 0xe3, 0x60, 0xcf,
	0x0b, 0x7a, 0xc4, 0x7a, 0x04, 0x87, 0xb3, 0x94, 0xc3, 0x00, 0x2a, 0x22, 0x03, 0x15, 0xbc, 0xd9,
	0x3e, 0xc0, 0x9d, 0x7e, 0x97, 0xae, 0x47, 0xc1, 0x01, 0x31, 0x19, 0x74, 0x78, 0x62, 0x35, 0x4f,
	0xfa, 0xa1, 0xe3, 0xe2, 0x30, 0x5c, 0x73, 0xec, 0xfd, 0x85, 0x69, 0x66, 0x35, 0x2a, 0x0c, 0x7d,
	0x0e, 0x16, 0xc9, 0x88, 0x57, 0xe8, 0xfc, 0xdf, 0xb1, 0xdb, 0x78, 0xf5, 0x00, 0xb7, 0x0f, 0xf9,
	0x9c, 0xce, 0xd0, 0x06, 0x7a, 0x02, 0xf4, 0x1e, 0xd4, 0x68, 0xef, 0xf9, 0xcd, 0x67, 0x69, 0xf3,
	0x02, 0x0a, 0xf4, 0x3e, 0x2c, 0x51, 0xec, 0x63, 0xdc, 0xe9, 0x38, 0xee, 0x7e, 0x03, 0x87, 0x38,
	0x38, 0xc2, 0x1d, 0x31, 0xc0, 0x39, 0xca, 0xa0, 0x88, 0x84, 0x78, 0x7c, 0xe9, 0xba, 0x08, 0x99,
	0x68, 0x3a, 0xcf, 0x3c, 0x7e, 0x1e, 0x4e, 0xda, 0xe4, 0x6d, 0x45, 0x11, 0x04, 0xb7, 0xb0, 0xa0,
	0xd8, 0x64, 0x1a, 0x69, 0xfd, 0xbb, 0x01, 0x93, 0x29, 0x6f, 0x78, 0x0e, 0xaa, 0x5d, 0xe1, 0xeb,
	0x84, 0x03, 0x91, 0x00, 0xb2, 0x80, 0x22, 0xb1, 0x60, 0x99, 0xfb, 0x90, 0xcf, 0xa9, 0xc5, 0x3a,
	0x9c, 0x59, 0xac, 0x12, 0x4f, 0x96, 0x05, 0xf7, 0x1b, 0x0a, 0x24, 0x6f, 0x01, 0x9e, 0xd1, 0x2e,
	0xc0, 0xad, 0xdc, 0x05, 0xc8, 0x7c, 0x48, 0x3e, 0xd2, 0xfa, 0xfb, 0x12, 0xcc, 0x6e, 0xc4, 0xe1,
	0x45, 0x19, 0xf3, 0x0e, 0x20, 0x25, 0xee, 0xa8, 0x01, 0x76, 0xec, 0xe6, 0xe5, 0xeb, 0x79, 0x81,
	0xfa, 0x7a, 0x32, 0x48, 0x37, 0x72, 0xda, 0xa3, 0x0f, 0x00, 0x75, 0x33, 0xd1, 0x86, 0x6a, 0x6d,
	0xec, 0xe6, 0xb5, 0x7c, 0xae, 0xd9, 0xe8, 0xd4, 0xc8, 0xe1, 0x81, 0xee, 0xc1, 0xd8, 0x5e, 0xec,
	0xfa, 0xa9, 0xaa, 0xb5, 0x2c, 0xb3, 0x31, 0xa2, 0xa1, 0x36, 0x46, 0xef, 0xc1, 0x68, 0x47, 0x89,
	0x35, 0xda, 0x01, 0xa7, 0xc4, 0x12, 0x8d, 0xac, 0x7f, 0x36, 0x00, 0x36, 0x64, 0x4a, 0x41, 0xbc,
	0x5c, 0xdf, 0xe9, 0x70, 0xc3, 0x21, 0x3f, 0xd1, 0x17, 0x00, 0xfc, 0xf4, 0xf0, 0x5f, 0xc9, 0xef,
	0x23, 0x77, 0x76, 0x1a, 0x4a, 0x73, 0x34, 0x0b, 0x23, 0x76, 0xa7, 0xd3, 0x8a, 0x42, 0x6e, 0x5f,
	0x67, 0xec, 0x4e, 0x67, 0x27, 0x44, 0x8b, 0x50, 0x61, 0x0c, 0x37, 0x3b, 0xdc, 0xb0, 0x46, 0xe9,
	0xf3, 0x66, 0x07, 0x9d, 0x07, 0xb0, 0x89, 0x64, 0xad, 0xe8, 0xd8, 0xc7, 0xdc, 0x40, 0xaa, 0x14,
	0xb2, 0x73, 0xec, 0x63, 0xb4, 0x0c, 0xe3, 0x76, 0xa7, 0xe7, 0xb8, 0x2d, 0xce, 0x56, 0xc4, 0x18,
	0x02, 0x5b, 0x21, 0xbc, 0xad, 0x43, 0x98, 0xd9, 0xc0, 0x51, 0x3c, 0x44, 0x12, 0x04, 0x1b, 0xf8,
	0xc3, 0xb8, 0x4f, 0x39, 0x5c, 0xd9, 0xe7, 0x22, 0x54, 0xfa, 0x4e, 0xa7, 0xd5, 0x25, 0x21, 0xb4,
	0xb4, 0x3c, 0x4c, 0x50, 0x7d, 0xa7, 0x43, 0xa3, 0x67, 0x52, 0x9c, 0xe1, 0x94, 0x38, 0xd6, 0x07,
	0x30, 0x9b, 0xd3, 0x59, 0xe8, 0xa3, 0xcf, 0x8b, 0x76, 0x94, 0xa9, 0xb1, 0x3c, 0x7c, 0x6d, 0xec,
	0xe6, 0x72, 0x81, 0x16, 0x69, 0x6b, 0xce, 0x99, 0x30, 0xb1, 0xfe, 0xd8, 0x00, 0x73, 0xa5, 0xd3,
	0x51, 0x90, 0xc5, 0x63, 0x58, 0x02, 0xd6, 0xb8, 0x45, 0xa6, 0x93, 0x2f, 0x75, 0x0a, 0xd8, 0xcd,
	0xcc, 0xe9, 0xf0, 0x8b, 0xcd, 0x69, 0x52, 0x25, 0xe5, 0xb4, 0x4a, 0xa6, 0xe1, 0x6c, 0x4a, 0xee,
	0xd0, 0xb7, 0x1c, 0x30, 0xd7, 0x70, 0xf7, 0x93, 0x19, 0xcc, 0x80, 0x29, 0x99, 0x86, 0xb3, 0xa9,
	0xae, 0x42, 0xdf, 0xfa, 0x1d, 0x03, 0xce, 0xed, 0xfa, 0x1d, 0x3b, 0xc2, 0x14, 0x96, 0x5a, 0x1c,
	0x9f, 0x12, 0xcd, 0x5a, 0x17, 0xe1, 0x7c, 0x81, 0x90, 0xa1, 0x6f, 0xdd, 0x00, 0x73, 0x03, 0x47,
	0x14, 0x4b, 0xb9, 0x11, 0xc9, 0x13, 0xe2, 0x19, 0x49, 0xf1, 0xac, 0x4d, 0x38, 0x9b, 0x6a, 0x10,
	0xfa, 0xe8, 0x33, 0x50, 0x76, 0xdc, 0x3d, 0x8f, 0x3b, 0xcc, 0xc1, 0x56, 0x49, 0xa9, 0xad, 0x2d,
	0x98, 0xa2, 0x8f, 0x2c, 0x8a, 0x6e, 0xba, 0x7b, 0x1e, 0x9d, 0x09, 0xfa, 0xa4, 0xf4, 0x5d, 0x65,
	0x10, 0xa2, 0x9b, 0x22, 0xc5, 0x59, 0x98, 0xae, 0x1c, 0x85, 0xa3, 0x58, 0xa7, 0x45, 0xe3, 0x41,
	0x08, 0xca, 0x3e, 0x49, 0x94, 0x18, 0x37, 0xfa, 0x9b, 0x34, 0x20, 0x7f, 0x5b, 0xa1, 0xf3, 0x35,
	0x61, 0x0e, 0x15, 0x02, 0x68, 0x3a, 0x5f, 0xc3, 0xd6, 0xf7, 0x0c, 0x98, 0xcb, 0xeb, 0x27, 0xf4,
	0xd1, 0x6d, 0xa8, 0x92, 0x81, 0xa9, 0x2b, 0xf4, 0x4a, 0xbe, 0x2e, 0x52, 0xe3, 0x6e, 0x54, 0x48,
	0x3b, 0xea, 0x1e, 0x96, 0xa0, 0xea, 0xe2, 0x8f, 0xa2, 0x96, 0x22, 0x54, 0x85, 0x00, 0xb6, 0xb9,
	0x60, 0x11, 0x49, 0x9f, 0x5b, 0x6d, 0x57, 0xa4, 0xe6, 0x15, 0x0a, 0x58, 0x75, 0x23, 0xeb, 0x61,
	0x3c, 0x33, 0x8c, 0xf3, 0xc0, 0xb1, 0x27, 0xb5, 0x5d, 0x4a, 0x69, 0xdb, 0x7a, 0x08, 0x28, 0xcd,
	0x30, 0xf4, 0xd1, 0xcf, 0x26, 0xe6, 0xfa, 0x84, 0xe3, 0x63, 0x13, 0x7e, 0x93, 0x49, 0x48, 0xc1,
	0x72, 0xd1, 0x16, 0x4f, 0xb9, 0x10, 0x42, 0x6d, 0xf3, 0x62, 0x42, 0xbc, 0x0b, 0x93, 0x0c, 0xb6,
	0x1e, 0x04, 0x5e, 0xb0, 0x15, 0xee, 0xe7, 0x44, 0xac, 0x79, 0x18, 0xc5, 0x41, 0xd0, 0x22, 0xd9,
	0x3a, 0xd1, 0x4a, 0xb5, 0x31, 0x82, 0x03, 0x42, 0x6a, 0xed, 0x51, 0x57, 0x74, 0x1a, 0x1d, 0x17,
	0x44, 0x02, 0xd5, 0x43, 0x0c, 0x27, 0x3c, 0x84, 0xb5, 0x0b, 0x28, 0xdd, 0x0f, 0x0d, 0x01, 0x15,
	0x22, 0x96, 0x62, 0x5e, 0x9a, 0x50, 0x9d, 0x1c, 0x60, 0x83, 0x0c, 0x86, 0x86, 0x80, 0x3d, 0xea,
	0xc9, 0xfe, 0xef, 0xc5, 0x9f, 0x01, 0x94, 0xee, 0x27, 0xf4, 0xad, 0xf7, 0x60, 0x86, 0xa6, 0xc9,
	0x9b, 0xe1, 0x03, 0x8c, 0x3b, 0x8f, 0x70, 0xe0, 0xec, 0x1d, 0x13, 0x01, 0x66, 0xe0, 0x4c, 0xd8,
	0xc6, 0x2e, 0xe6, 0x9d, 0xb3, 0x07, 0x31, 0x2b, 0x25, 0x39, 0x2b, 0xd6, 0xeb, 0x30, 0x9b, 0xd3,
	0x3e, 0xf4, 0xc9, 0x74, 0x39, 0x61, 0xcb, 0xc5, 0x98, 0xc9, 0x5f, 0x69, 0x8c, 0x38, 0x94, 0xc4,
	0xba, 0x05, 0x66, 0x13, 0x47, 0x8c, 0xf2, 0x4e, 0xd7, 0xde, 0x3f, 0x4d, 0x6f, 0xd3, 0x70, 0x36,
	0xd5, 0x36, 0xf4, 0xad, 0x7f, 0x29, 0xc1, 0xe2, 0x3a, 0xdd, 0xdb, 0x90, 0xc4, 0x32, 0x72, 0x48,
	0x0e, 0x85, 0x83, 0x2d, 0xcf, 0x8d, 0x0e, 0xba, 0xc7, 0x44, 0x93, 0xc4, 0xd7, 0xb6, 0x22, 0xa7,
	0x27, 0xd8, 0x57, 0x08, 0x60, 0xc7, 0xe9, 0xe1, 0x84, 0xba, 0x4a, 0xc9, 0x78, 0xc0, 0x3b, 0x1f,
	0x8e, 0x0d, 0x70, 0x09, 0xaa, 0x5e, 0xd0, 0xc1, 0x41, 0xcb, 0xee, 0xc9, 0xf7, 0x2c, 0x14, 0xb0,
	0xd2, 0x8b, 0x62, 0x24, 0xf1, 0x02, 0x67, 0x14, 0xe4, 0xaa, 0x1b, 0x21, 0x0b, 0x26, 0x18, 0xb2,
	0x63, 0x1f, 0x53, 0x02, 0x96, 0xf0, 0x8c, 0x51, 0xe0, 0x9a, 0x7d, 0x4c, 0x68, 0x2e, 0xc3, 0x24,
	0x11, 0xfe, 0x08, 0x4b, 0x22, 0x96, 0xf4, 0x8c, 0x33, 0x28, 0xa7, 0xba, 0x0a, 0x53, 0x6c, 0x57,
	0xd2, 0xea, 0x87, 0xbc, 0x33, 0xfe, 0xfa, 0x85, 0x81, 0x77, 0x43, 0xd6, 0xe3, 0xcb, 0x60, 0xba,
	0xf8, 0x69, 0xcb, 0xb7, 0x8f, 0x63, 0x42, 0xb6, 0xb5, 0x9e, 0x70, 0xf1, 0xd3, 0x6d, 0xfb, 0x58,
	0x10, 0x5e, 0x86, 0xc9, 0x00, 0x27, 0xc8, 0xd8, 0xf6, 0x7a, 0x9c, 0x42, 0x39, 0x95, 0xf5, 0x9b,
	0x25, 0x98, 0xcf, 0xaa, 0x78, 0xcd, 0x76, 0x3e, 0x25, 0x0a, 0xce, 0x51, 0xcb, 0xc8, 0x49, 0xd5,
	0x32, 0x9a, 0xa7, 0x96, 0x57, 0x00, 0x11, 0xc2, 0x94, 0x6a, 0x98, 0xaa, 0xa7, 0x5c, 0xfc, 0xb4,
	0xa1, 0x6a, 0xe7, 0x07, 0x25, 0x98, 0x65, 0xda, 0xd9, 0xb0, 0x7b, 0x98, 0x1b, 0x1e, 0xd9, 0x99,
	0x3d, 0xb7, 0x6e, 0xe6, 0x61, 0x74, 0xdf, 0xee, 0xe1, 0x78, 0x15, 0x8f, 0x90, 0xc7, 0xcd, 0x17,
	0x51, 0xd1, 0x12, 0x54, 0xdb, 0x9e, 0xdd, 0x3e, 0x50, 0x94, 0x53, 0xa1, 0x00, 0x82, 0x7c, 0x03,
	0x66, 0x23, 0xfb, 0x10, 0xb7, 0x28, 0x75, 0xd8, 0x8a, 0x09, 0x99, 0x72, 0x10, 0x41, 0x3e, 0xa4,
	0xb8, 0x55, 0xd1, 0xe4, 0x84, 0x96, 0x68, 0xfd, 0x97, 0x01, 0x73, 0x5c, 0x39, 0x34, 0x33, 0xfa,
	0x04, 0xb4, 0x93, 0x50, 0xc2, 0x70, 0x91, 0x12, 0xca, 0x83, 0xed, 0xe4, 0x4c, 0x9e, 0x9d, 0x14,
	0x2a, 0xcb, 0x02, 0x62, 0x2c, 0x19, 0x25, 0x8d, 0xb9, 0xf8, 0xa9, 0xd0, 0x8e, 0xf5, 0xdf, 0x25,
	0x98, 0x51, 0x46, 0x4d, 0x57, 0xca, 0x0b, 0x8d, 0xf9, 0x3c, 0x80, 0xe3, 0xb6, 0xbd, 0x1e, 0x56,
	0x06, 0x5d, 0x65, 0x90, 0xc4, 0xa8, 0xf3, 0xec, 0xe2, 0x65, 0x30, 0xbb, 0x61, 0xd4, 0xea, 0xb4,
	0x14, 0x0e, 0xe2, 0xe5, 0x5f, 0x18, 0xad, 0x6d, 0x4a, 0x2e, 0x57, 0x60, 0x8a, 0x11, 0xc6, 0xbc,
	0xc4, 0xfb, 0xbf, 0x30, 0x5a, 0x7b, 0x28, 0xf8, 0x5d, 0x86, 0xc9, 0x98, 0x53, 0xab, 0xe3, 0x75,
	0xa8, 0x06, 0x8c, 0xc6, 0xb8, 0x94, 0x67, 0xcd, 0xeb, 0xc4, 0x4e, 0x4f, 0x10, 0x55, 0x28, 0xd1,
	0x98, 0x10, 0x8b, 0xd0, 0x70, 0xc9, 0x7a, 0xaa, 0x64, 0x55, 0x29, 0xd9, 0x56, 0x46, 0xb2, 0x9e,
	0x22, 0x19, 0x48, 0xc9, 0xb6, 0x84, 0x64, 0xd6, 0x8f, 0x0d, 0x38, 0xbf, 0x81, 0x23, 0x8d, 0xab,
	0x22, 0x91, 0xe6, 0x2a, 0x4c, 0x3d, 0xc1, 0xfb, 0x8e, 0xdb, 0x4a, 0xcf, 0xc2, 0x04, 0x05, 0xaf,
	0x89, 0xa9, 0xb0, 0x60, 0x02, 0xbb, 0x1d, 0x85, 0x8a, 0xcd, 0xc7, 0x18, 0x76, 0x3b, 0x6b, 0xca,
	0x74, 0xc9, 0x38, 0x3c, 0x9c, 0x8c, 0xc3, 0x22, 0x83, 0x2d, 0xeb, 0x32, 0xd8, 0x33, 0xc9, 0x0c,
	0x96, 0xcc, 0x2f, 0x9b, 0x7a, 0xca, 0x6d, 0x84, 0x72, 0xab, 0xf2, 0x77, 0xdc, 0x61, 0x64, 0x7d,
	0x6c, 0xc0, 0x85, 0xa2, 0x81, 0x85, 0x3e, 0xba, 0x97, 0x4d, 0x74, 0x5f, 0xcb, 0xcf, 0x44, 0x74,
	0x5c, 0xe2, 0x84, 0x77, 0x06, 0xce, 0xd0, 0x14, 0x96, 0x8f, 0x9a, 0x3d, 0x58, 0xff, 0x68, 0xc0,
	0xc5, 0x5c, 0x21, 0xf8, 0xa2, 0xfe, 0x09, 0xd6, 0xef, 0xb7, 0x0c, 0x58, 0x2e, 0x1e, 0x5a, 0xe8,
	0xa3, 0xfb, 0x59, 0x0d, 0xdf, 0x38, 0xa9, 0x86, 0x05, 0x9f, 0x41, 0x3a, 0xfe, 0x3b, 0x03, 0x96,
	0xa4, 0x20, 0xa9, 0x70, 0xf2, 0x49, 0xeb, 0x37, 0xa9, 0x93, 0xe1, 0x94, 0x4e, 0xd4, 0x20, 0x54,
	0xa6, 0x38, 0x11, 0x84, 0x84, 0xf2, 0xcf, 0xe8, 0x94, 0x3f, 0x92, 0xda, 0x9e, 0x7d, 0x03, 0xce,
	0xe9, 0xc7, 0x14, 0xfa, 0xe8, 0x6e, 0x56, 0xb1, 0xaf, 0x14, 0x29, 0x36, 0xcd, 0x63, 0x90, 0x52,
	0xff, 0xd4, 0x50, 0x05, 0x48, 0x85, 0xa1, 0xff, 0x67, 0xad, 0x9e, 0xd6, 0x72, 0xad, 0x67, 0xaa,
	0x4f, 0xcb, 0x0a, 0x1f, 0xfa, 0x68, 0x33, 0xab, 0xbe, 0x57, 0x0b, 0xd5, 0x97, 0x66, 0x32, 0x48,
	0x7f, 0x3b, 0x50, 0x4b, 0x4a, 0x20, 0xe3, 0x19, 0xdf, 0xab, 0x3c, 0x4f, 0x48, 0xb3, 0x7e, 0x5e,
	0xb5, 0xf4, 0x14, 0xd7, 0xd0, 0x47, 0xef, 0x25, 0xb6, 0x93, 0xf5, 0x81, 0x03, 0x8a, 0x5b, 0xb3,
	0x3d, 0xe5, 0x3d, 0xca, 0xfe, 0xf1, 0x81, 0x13, 0xe1, 0xc7, 0x07, 0x76, 0x17, 0xaf, 0xb4, 0xdb,
	0x38, 0x0c, 0x77, 0xbc, 0x43, 0x4c, 0x5f, 0x05, 0x65, 0x37, 0x98, 0x05, 0xa2, 0x7e, 0x85, 0xda,
	0x8f, 0x86, 0x57, 0xe8, 0xa3, 0x4b, 0x30, 0x6e, 0x53, 0x50, 0x2b, 0x22, 0x30, 0xca, 0xb5, 0xda,
	0x18, 0xb3, 0x63, 0x32, 0xa2, 0x25, 0xfc, 0x91, 0xef, 0x04, 0xb8, 0x15, 0x85, 0xe2, 0x1d, 0x02,
	0x03, 0xec, 0x84, 0x96, 0x07, 0xe7, 0xe9, 0x2e, 0x4a, 0x2b, 0xed, 0x09, 0x3a, 0xc8, 0xec, 0x96,
	0x8a, 0x36, 0x83, 0xef, 0xc2, 0x85, 0xa2, 0x0e, 0x43, 0x9f, 0x34, 0x76, 0xc2, 0xd6, 0x91, 0xdd,
	0x75, 0xc4, 0x06, 0x6e, 0xd4, 0x09, 0x1f, 0x91, 0x47, 0xeb, 0xbb, 0x06, 0xcc, 0x37, 0xb1, 0xab,
	0xbc, 0xfd, 0xdb, 0x74, 0x8f, 0x9c, 0x08, 0x13, 0x41, 0x17, 0x40, 0xf4, 0x91, 0x7e, 0xc1, 0x46,
	0x33, 0x18, 0x42, 0xa6, 0xbe, 0xd8, 0x60, 0x10, 0xb2, 0xa9, 0xbd, 0x08, 0x63, 0xec, 0xa1, 0xd3,
	0x8a, 0xb7, 0x05, 0xbc, 0x45, 0x27, 0xfb, 0x42, 0x30, 0xf3, 0x42, 0xb2, 0x06, 0x0b, 0xf9, 0x32,
	0x85, 0xbe, 0xf5, 0x0a, 0x2c, 0xae, 0xd2, 0xcf, 0xa9, 0x79, 0x12, 0x4f, 0x42, 0x49, 0xda, 0x41,
	0xc9, 0xe9, 0x58, 0xe7, 0xa0, 0xa6, 0x23, 0x0e, 0x7d, 0xeb, 0xdb, 0x06, 0x98, 0x69, 0x44, 0x9a,
	0x45, 0xce, 0x54, 0x4c, 0x42, 0x49, 0xbe, 0x1d, 0x2f, 0x45, 0xe1, 0x80, 0xc1, 0xa0, 0x97, 0x60,
	0x82, 0xeb, 0x2a, 0x8c, 0xec, 0xa8, 0x1f, 0x72, 0xa7, 0x30, 0xce, 0x80, 0x4d, 0x0a, 0xb3, 0x7e,
	0xd7, 0xa0, 0xcb, 0x32, 0x2d, 0xcd, 0x8b, 0xbd, 0x09, 0xcf, 0xf4, 0x3c, 0x9c, 0xed, 0xf9, 0xf4,
	0x3e, 0xec, 0x87, 0x2c, 0xaa, 0xe5, 0x8b, 0x1a, 0xfa, 0x68, 0x43, 0x4c, 0xbe, 0xea, 0xc3, 0xae,
	0x0e, 0x7a, 0x65, 0xc9, 0xa7, 0x85, 0x1b, 0xc9, 0x0b, 0xbe, 0xa9, 0xfb, 0x00, 0xe6, 0xb7, 0x03,
	0xaf, 0x9d, 0x67, 0x21, 0x59, 0x57, 0xc1, 0x26, 0xbc, 0x24, 0x27, 0x7c, 0x09, 0xaa, 0x4e, 0xd8,
	0x22, 0xab, 0xd1, 0x67, 0x9c, 0x2b, 0x8d, 0x8a, 0x13, 0xae, 0xd0, 0x67, 0x62, 0x99, 0xf9, 0x9c,
	0x43, 0xdf, 0x3a, 0x82, 0xd9, 0x95, 0x4e, 0xe7, 0x91, 0xe3, 0xad, 0x7f, 0x44, 0x1c, 0x59, 0x03,
	0xb7, 0xbd, 0xa0, 0x33, 0x60, 0xf6, 0x72, 0x17, 0x3a, 0x0b, 0x5f, 0xdc, 0xc6, 0xca, 0x8d, 0x51,
	0xfa, 0xbc, 0x13, 0xa2, 0x59, 0x18, 0x21, 0x11, 0x2b, 0x0a, 0xe9, 0x64, 0x95, 0x1b, 0x67, 0xb0,
	0xdb, 0xd9, 0x09, 0xad, 0x05, 0x98, 0xcb, 0xeb, 0x37, 0xf4, 0xad, 0x3f, 0x33, 0xe0, 0xf2, 0x06,
	0x8e, 0xe8, 0xc7, 0xba, 0xd5, 0x03, 0xdb, 0x75, 0x71, 0x77, 0xbd, 0xe7, 0x77, 0xbd, 0xe3, 0x1e,
	0x95, 0x9c, 0xc5, 0x09, 0x22, 0xa1, 0x05, 0x13, 0x42, 0xc2, 0x78, 0xd6, 0xc4, 0x59, 0x91, 0x4d,
	0x66, 0x4d, 0x57, 0x61, 0xaa, 0xcd, 0x78, 0xb4, 0x92, 0xf6, 0x36, 0xc1, 0xc1, 0x9b, 0xf2, 0xb5,
	0x95, 0x2e, 0xd3, 0x9b, 0x83, 0x11, 0x6f, 0x6f, 0x2f, 0xc4, 0x62, 0x5b, 0xc3, 0x9f, 0x48, 0xa4,
	0xea, 0x3a, 0x3d, 0x47, 0xec, 0x64, 0xd8, 0x83, 0xf5, 0x4d, 0xa8, 0xe9, 0x25, 0x27, 0xab, 0x2e,
	0x16, 0x47, 0xbc, 0xd6, 0x94, 0x92, 0xd0, 0x45, 0x49, 0xbf, 0x3a, 0x51, 0x39, 0x58, 0xce, 0x53,
	0xa5, 0x10, 0xb1, 0x34, 0x30, 0xe5, 0x87, 0xb1, 0x9a, 0x45, 0x8e, 0x0b, 0x20, 0x4d, 0x24, 0x7f,
	0x64, 0xc0, 0x95, 0x13, 0xa8, 0x2f, 0xf4, 0xd1, 0x13, 0x98, 0xc1, 0x12, 0xd3, 0x4a, 0x07, 0xf0,
	0xd7, 0x35, 0x1f, 0x38, 0xb5, 0x7c, 0x1b, 0x08, 0x67, 0xfa, 0xd1, 0x84, 0xf3, 0x15, 0x9a, 0xeb,
	0x16, 0xb0, 0x62, 0x6f, 0x80, 0x0b, 0x54, 0x65, 0x7d, 0x03, 0x2e, 0x0d, 0x60, 0x11, 0xfa, 0xe8,
	0x4b, 0x30, 0x95, 0x1a, 0x21, 0x0f, 0xe6, 0xa7, 0x1f, 0xdc, 0x64, 0x72, 0x70, 0xd6, 0xef, 0x19,
	0xb0, 0xdc, 0x1c, 0x34, 0x86, 0x82, 0x35, 0x94, 0x1c, 0x5e, 0x29, 0x6d, 0x09, 0x75, 0x38, 0x2b,
	0xa7, 0x3a, 0x65, 0x98, 0x53, 0x02, 0xb1, 0xcb, 0x0d, 0xf4, 0x12, 0x8c, 0x7b, 0x3e, 0x0e, 0xec,
	0x88, 0xbf, 0x2d, 0xe7, 0x47, 0x6f, 0x04, 0x6c, 0xd7, 0xe9, 0x58, 0x2f, 0xc1, 0xa5, 0xe6, 0x20,
	0x6d, 0x59, 0x3f, 0x28, 0xc1, 0x95, 0xdb, 0x76, 0xd4, 0x3e, 0x18, 0x38, 0x2e, 0x27, 0xa1, 0x57,
	0xc5, 0x68, 0xde, 0xcf, 0xd7, 0xeb, 0x89, 0xb8, 0x5e, 0xdf, 0x8c, 0x70, 0x4f, 0xd5, 0xf3, 0x09,
	0x07, 0x57, 0x6b, 0x43, 0x99, 0x34, 0x7d, 0x01, 0x6d, 0x5f, 0x82, 0x71, 0x55, 0xdb, 0xdc, 0x35,
	0x8f, 0x29, 0x8a, 0xb6, 0xae, 0xc1, 0xd5, 0x93, 0x8c, 0x22, 0xf4, 0xad, 0xdf, 0x37, 0x60, 0x29,
	0x45, 0xb5, 0x42, 0x96, 0xf0, 0x09, 0x8c, 0xa2, 0x0e, 0x67, 0x85, 0x98, 0xcc, 0x0f, 0xc4, 0x6e,
	0x56, 0xb8, 0x31, 0xca, 0x8a, 0x64, 0x23, 0x39, 0x9e, 0x6d, 0x38, 0xcf, 0xb3, 0x9d, 0xc0, 0x3a,
	0x2e, 0xc0, 0x39, 0xbd, 0xc0, 0xa1, 0x6f, 0xbd, 0xa1, 0x7c, 0xea, 0x16, 0xce, 0x28, 0x3b, 0x92,
	0x61, 0x35, 0xbd, 0x7b, 0x5b, 0xf9, 0x60, 0x1d, 0x37, 0x09, 0xfd, 0x94, 0x8b, 0x33, 0x52, 0x2e,
	0xce, 0xba, 0x45, 0xc3, 0x34, 0x97, 0x82, 0x40, 0x6e, 0x1f, 0x8b, 0x11, 0x8b, 0xaf, 0x12, 0x52,
	0x31, 0xe2, 0xab, 0x04, 0xc7, 0x5b, 0x2b, 0x34, 0x47, 0xd6, 0xb4, 0x65, 0x39, 0xb2, 0xd0, 0x98,
	0x1a, 0x2e, 0xda, 0x71, 0x03, 0xeb, 0x3f, 0x0d, 0x38, 0x4f, 0xa7, 0x79, 0x03, 0x47, 0x34, 0x9d,
	0x97, 0xa7, 0x78, 0x4e, 0x30, 0x7b, 0x35, 0xa8, 0x38, 0x1d, 0xec, 0x46, 0x4e, 0x74, 0x2c, 0x22,
	0xbf, 0x78, 0xce, 0x04, 0xc8, 0x09, 0x5d, 0x80, 0x9c, 0xe0, 0x01, 0xf2, 0xd4, 0x7b, 0xdc, 0x44,
	0x08, 0x1b, 0x4d, 0x86, 0x30, 0x92, 0x77, 0x44, 0x02, 0x57, 0x59, 0x1e, 0xbe, 0x56, 0x6d, 0x54,
	0x08, 0x80, 0x8e, 0xf9, 0xeb, 0x70, 0xa1, 0x68, 0xc8, 0xa1, 0x8f, 0xd6, 0xb3, 0xdb, 0xbb, 0x6b,
	0xba, 0xd3, 0x20, 0x19, 0x06, 0x83, 0xb6, 0x76, 0x3f, 0x2e, 0x03, 0xca, 0x36, 0xcb, 0x49, 0x79,
	0x10, 0x94, 0xc9, 0xa6, 0x8e, 0x7f, 0x7b, 0xa3, 0xbf, 0x49, 0xb2, 0x6f, 0xb7, 0xdb, 0x5e, 0x9f,
	0xa7, 0x53, 0xd5, 0x86, 0x78, 0x24, 0x93, 0xe1, 0x3a, 0xed, 0x43, 0xd7, 0xee, 0xb1, 0x2c, 0xb1,
	0xda, 0x90, 0xcf, 0x84, 0x13, 0x19, 0x3d, 0x55, 0x6d, 0xb5, 0x41, 0x7f, 0x93, 0x64, 0x22, 0x74,
	0xf6, 0x5d, 0x92, 0x74, 0x06, 0x11, 0x99, 0x0c, 0xa2, 0xde, 0xe1, 0xc6, 0x18, 0x01, 0x36, 0x09,
	0x6c, 0x27, 0x44, 0x17, 0x80, 0x3e, 0xb6, 0xf8, 0x74, 0x8d, 0x52, 0x8a, 0x2a, 0x01, 0xad, 0xd3,
	0x29, 0x4b, 0x7c, 0x33, 0xab, 0xa4, 0xbe, 0x99, 0x5d, 0x81, 0x49, 0x86, 0x94, 0x62, 0x55, 0x69,
	0xf7, 0x13, 0x14, 0xfa, 0x40, 0xc8, 0xb6, 0x04, 0xd5, 0xa0, 0xdf, 0xc5, 0x2c, 0x2d, 0x07, 0x26,
	0x38, 0x01, 0xd0, 0xac, 0x3c, 0xe9, 0xc6, 0xc6, 0xd2, 0x6e, 0xec, 0x55, 0x40, 0x02, 0xdd, 0x71,
	0x42, 0xbf, 0x6b, 0x1f, 0x13, 0x32, 0x76, 0xb2, 0xd0, 0xe4, 0x98, 0x35, 0x86, 0xd8, 0x4c, 0x6e,
	0xce, 0x26, 0x92, 0x96, 0x7c, 0x19, 0x26, 0x19, 0x2a, 0x3c, 0xf0, 0x82, 0x88, 0x10, 0xb0, 0x83,
	0x85, 0xe3, 0x14, 0xda, 0x24, 0xc0, 0x4d, 0xaa, 0xb2, 0xc0, 0xf3, 0x7a, 0xad, 0x7d, 0x67, 0x2f,
	0xa2, 0x2f, 0x44, 0xa7, 0xd8, 0x9e, 0x90, 0x00, 0x37, 0x9c, 0xbd, 0x88, 0xbf, 0xa9, 0x65, 0x9c,
	0x24, 0x91, 0x49, 0x89, 0x18, 0x27, 0x41, 0x75, 0x1d, 0xa6, 0xbb, 0x64, 0x6b, 0xd9, 0xf6, 0x7a,
	0xfc, 0x18, 0x02, 0x25, 0x3d, 0x4b, 0x49, 0xcf, 0x12, 0xd4, 0xaa, 0xc4, 0x10, 0x7a, 0xb2, 0x47,
	0xe0, 0x2b, 0x8b, 0x29, 0x0a, 0x31, 0xa6, 0x02, 0x48, 0xf7, 0x63, 0xff, 0x50, 0x86, 0xe9, 0xc7,
	0x18, 0x1f, 0xfe, 0xd4, 0xb2, 0x7e, 0x6a, 0x59, 0x9f, 0xb0, 0x65, 0x19, 0xb1, 0xcb, 0xcc, 0xb1,
	0xb0, 0x4f, 0x77, 0x98, 0x48, 0xc4, 0x82, 0xd1, 0x54, 0x2c, 0xf8, 0x26, 0x5c, 0x2c, 0x1c, 0x58,
	0xe8, 0xa3, 0x3b, 0xd9, 0x60, 0xf0, 0x33, 0xf9, 0xc1, 0x20, 0x8f, 0xc3, 0xa0, 0x68, 0xf0, 0x37,
	0xc3, 0xb0, 0x44, 0xb3, 0x06, 0xd6, 0xb8, 0xd9, 0xef, 0x25, 0x17, 0xaf, 0x58, 0xaa, 0x86, 0xb2,
	0x54, 0x13, 0x86, 0x5d, 0x4a, 0x19, 0xf6, 0x55, 0x98, 0xb2, 0xe9, 0xa1, 0xc6, 0xf8, 0x83, 0xdb,
	0x30, 0x5f, 0x1d, 0x14, 0x2c, 0x3e, 0xb8, 0x59, 0x30, 0xb1, 0x67, 0xb7, 0x71, 0x4c, 0xc5, 0x96,
	0xf6, 0x18, 0x01, 0x0a, 0x9a, 0x1b, 0x30, 0xe3, 0x7a, 0x51, 0xeb, 0xc0, 0xe9, 0x85, 0xb8, 0xbb,
	0x97, 0xfc, 0x82, 0x57, 0x6d, 0x9c, 0x75, 0xbd, 0xe8, 0x2e, 0x43, 0x89, 0x06, 0xe7, 0x01, 0x28,
	0xd3, 0xc0, 0x8e, 0x1c, 0x8f, 0xce, 0x44, 0xb5, 0x51, 0x25, 0x90, 0x06, 0x01, 0x10, 0x63, 0x13,
	0xbc, 0x18, 0xc5, 0x28, 0x33, 0x36, 0x0e, 0x64, 0x44, 0xaf, 0x02, 0xa2, 0x3c, 0x92, 0x94, 0x15,
	0x4a, 0x69, 0x12, 0xcc, 0x5d, 0x95, 0xfa, 0x16, 0xd4, 0x54, 0x11, 0x53, 0x2b, 0x84, 0xf9, 0x85,
	0xb9, 0x58, 0xd0, 0x8d, 0x13, 0xac, 0x15, 0x38, 0xf1, 0x5a, 0x19, 0xcb, 0x59, 0x2b, 0x7f, 0x61,
	0xc0, 0xa5, 0xa4, 0x49, 0xa5, 0xe7, 0xf4, 0x53, 0xbd, 0x5c, 0xac, 0xef, 0x18, 0x60, 0x0d, 0x92,
	0x3f, 0xf4, 0xd1, 0x83, 0xec, 0xaa, 0x78, 0xa3, 0xe0, 0xed, 0x91, 0x86, 0xd3, 0xa0, 0xd5, 0xf1,
	0xd7, 0xc3, 0x50, 0x53, 0x5e, 0x35, 0xff, 0x74, 0x71, 0xfc, 0x84, 0x2f, 0x8e, 0x3f, 0x37, 0x60,
	0x39, 0x91, 0x7b, 0xff, 0x64, 0xad, 0x8d, 0x6f, 0x2b, 0x6b, 0x5b, 0x23, 0x7e, 0xe8, 0xa3, 0xad,
	0xec, 0xd2, 0x78, 0xbd, 0x60, 0x69, 0xe4, 0x33, 0x1a, 0xb4, 0x32, 0xbe, 0x5f, 0x82, 0x39, 0xf6,
	0x26, 0x31, 0xa3, 0xbf, 0xd3, 0x7c, 0x67, 0x79, 0x0e, 0xad, 0xa9, 0xdb, 0xae, 0x33, 0xc9, 0x6d,
	0xd7, 0x3d, 0x18, 0xc3, 0x54, 0xa6, 0xf8, 0x90, 0xf9, 0xa4, 0x2e, 0x58, 0x4a, 0xb1, 0xd9, 0x28,
	0x88, 0x79, 0x34, 0x00, 0xcb, 0xdf, 0x89, 0xa9, 0x1e, 0x4d, 0x4d, 0x75, 0xe1, 0xf6, 0xee, 0x73,
	0x30, 0x9f, 0xab, 0x18, 0xb6, 0x21, 0xee, 0x78, 0x4f, 0xdd, 0xae, 0x67, 0x77, 0x5a, 0xfd, 0xa0,
	0x2b, 0xbe, 0xe9, 0x08, 0xd8, 0x6e, 0xd0, 0xb5, 0xfe, 0xd5, 0x80, 0xb9, 0xed, 0x00, 0xaf, 0x06,
	0xd8, 0x8e, 0xf0, 0xae, 0x4f, 0xc0, 0x77, 0x9c, 0xae, 0xe6, 0xa5, 0xf4, 0x12, 0x54, 0xf7, 0x9c,
	0x2e, 0x6e, 0xd1, 0x1c, 0x94, 0xfb, 0x1a, 0x02, 0x78, 0xc0, 0xd3, 0x4f, 0x8a, 0x94, 0xc7, 0x67,
	0xcb, 0x0c, 0x29, 0x92, 0x12, 0x8a, 0x94, 0x1f, 0x23, 0x78, 0x4b, 0x3a, 0xf4, 0x45, 0xa0, 0xbf,
	0x5b, 0xbd, 0xce, 0x5b, 0xdc, 0x9b, 0x8c, 0x92, 0xe7, 0xad, 0xce, 0x5b, 0x12, 0x85, 0x3f, 0x8a,
	0xb8, 0x07, 0xa1, 0xa8, 0xf5, 0x8f, 0xa2, 0xf8, 0x44, 0x1f, 0xf3, 0x1b, 0xfc, 0x44, 0x1f, 0x85,
	0x7a, 0x3e, 0xe6, 0x3e, 0x82, 0x3d, 0x58, 0x37, 0x61, 0x3e, 0x77, 0x90, 0xec, 0x14, 0x21, 0xed,
	0x81, 0x9b, 0x4a, 0xb5, 0x31, 0x42, 0x1e, 0x37, 0xd9, 0xb9, 0x43, 0xaf, 0xe7, 0x77, 0x71, 0x5a,
	0x2f, 0x4a, 0x0b, 0x23, 0xd1, 0x62, 0x01, 0xe6, 0xf2, 0x5a, 0x84, 0xbe, 0x85, 0x61, 0x41, 0xac,
	0xa3, 0x18, 0xb3, 0x1b, 0x74, 0xf9, 0x5b, 0x6e, 0xca, 0xee, 0x10, 0x1f, 0xc7, 0x4b, 0x88, 0xf8,
	0x5e, 0xa7, 0x8b, 0xbf, 0x80, 0x8f, 0xa9, 0xa1, 0x5d, 0x81, 0x49, 0xfa, 0x25, 0xcf, 0xa6, 0xbe,
	0x3c, 0xc4, 0x6d, 0x6e, 0xd6, 0x13, 0x31, 0xb4, 0x89, 0xdb, 0x24, 0x16, 0x2f, 0x6a, 0xfa, 0x09,
	0x7d, 0x64, 0xc3, 0x38, 0xed, 0xa8, 0x1f, 0x74, 0x5b, 0x3d, 0xdb, 0xe7, 0x4b, 0xf5, 0xf3, 0x05,
	0x6f, 0xf4, 0xf2, 0xd8, 0x5c, 0xe7, 0xbf, 0xb7, 0x6c, 0x7f, 0xdd, 0x8d, 0x82, 0xe3, 0x06, 0xec,
	0x49, 0x40, 0xed, 0xe7, 0x60, 0x2a, 0x85, 0x26, 0x56, 0x74, 0x88, 0x8f, 0xb9, 0xa6, 0xc8, 0x4f,
	0x32, 0x45, 0x47, 0x76, 0xb7, 0x2f, 0x2c, 0x88, 0x3d, 0xdc, 0x2a, 0x7d, 0xd6, 0xb0, 0xde, 0x07,
	0xb8, 0x4d, 0xb6, 0x1a, 0xec, 0x54, 0xf8, 0x3c, 0x8c, 0x3a, 0xbd, 0x7d, 0xc5, 0x70, 0x47, 0x9c,
	0xde, 0xfe, 0x6e, 0xd0, 0x25, 0x46, 0xf1, 0xd5, 0x7e, 0xcf, 0xa7, 0x18, 0xc6, 0x63, 0x94, 0x3c,
	0x13, 0x73, 0x46, 0xf4, 0x60, 0x7b, 0xcc, 0xa4, 0x81, 0x3f, 0xb4, 0x1e, 0xd1, 0xf3, 0xc7, 0x2a,
	0x2c, 0xf4, 0xd1, 0x0a, 0x8c, 0x3d, 0xa1, 0x90, 0x13, 0x14, 0x56, 0x28, 0x4d, 0x81, 0x35, 0x22,
	0x93, 0x52, 0xff, 0x5b, 0x83, 0xd7, 0x15, 0xa9, 0x55, 0xb8, 0xd4, 0xa0, 0x17, 0x60, 0x26, 0x81,
	0xd8, 0x74, 0xe9, 0x57, 0x4e, 0x73, 0x08, 0x2d, 0xc1, 0xfc, 0x46, 0xa2, 0xac, 0x54, 0xd6, 0x45,
	0x9a, 0x06, 0x9a, 0x85, 0xb3, 0x12, 0x29, 0x4a, 0x21, 0xcd, 0x12, 0x9a, 0x86, 0x29, 0xf6, 0x9e,
	0x4d, 0x96, 0x54, 0x99, 0x65, 0x74, 0x01, 0x6a, 0x2b, 0xb2, 0x84, 0x4a, 0x1c, 0x01, 0x11, 0x25,
	0x4f, 0x66, 0x05, 0xd5, 0x60, 0x8e, 0x36, 0x52, 0x4b, 0xbf, 0x5c, 0x8f, 0xe0, 0x4c, 0xb4, 0x08,
	0xb3, 0xb9, 0x25, 0x6c, 0xe6, 0x72, 0x7d, 0x05, 0xd0, 0x46, 0xa2, 0x4e, 0x98, 0x8e, 0x47, 0x08,
	0x26, 0xbe, 0x2d, 0xf1, 0xc1, 0xcc, 0xa8, 0x1f, 0x2a, 0x59, 0x41, 0x82, 0x69, 0xd4, 0xd7, 0x60,
	0x9a, 0x42, 0xd9, 0xd1, 0x2a, 0xc9, 0x63, 0x8e, 0x73, 0x66, 0xe0, 0x98, 0x89, 0xe0, 0xcd, 0xc9,
	0x05, 0x17, 0xe2, 0xef, 0xd9, 0x29, 0x02, 0x59, 0x03, 0x66, 0x47, 0x36, 0xdf, 0xca, 0x2e, 0x66,
	0x31, 0x31, 0xc3, 0x1a, 0xcc, 0x6d, 0xa9, 0x85, 0x94, 0x74, 0x75, 0x70, 0x0d, 0x4f, 0xc3, 0xd4,
	0x56, 0xb2, 0xa6, 0xcc, 0x2c, 0xa1, 0x4b, 0x70, 0x3e, 0x51, 0x38, 0x96, 0xd1, 0x66, 0x99, 0x08,
	0xb9, 0x95, 0x2e, 0xb9, 0x34, 0x2b, 0x44, 0x91, 0x12, 0xac, 0x16, 0x2d, 0x9a, 0x26, 0x99, 0x9f,
	0x2d, 0x6d, 0xad, 0xa0, 0xb9, 0x8c, 0xce, 0xc1, 0x82, 0x6c, 0x9a, 0x2a, 0x07, 0x34, 0xdf, 0x47,
	0x35, 0x85, 0xb1, 0x3a, 0x83, 0xe6, 0x33, 0xa3, 0xfe, 0x2e, 0x98, 0x77, 0x94, 0xf2, 0x6c, 0xaa,
	0x92, 0x69, 0x98, 0x12, 0xb0, 0x4d, 0xf7, 0x11, 0x57, 0x04, 0x12, 0x47, 0xde, 0x05, 0xca, 0x34,
	0xea, 0xbf, 0x61, 0xc0, 0x1c, 0x03, 0xae, 0xf2, 0xc2, 0x5d, 0xc9, 0x43, 0x5a, 0x85, 0xc0, 0xc4,
	0x9c, 0x96, 0x60, 0x3e, 0x89, 0x5a, 0x11, 0x95, 0xc5, 0xa6, 0x41, 0x8c, 0x3d, 0x89, 0x64, 0xdf,
	0xb6, 0xcd, 0x12, 0x99, 0xf2, 0x24, 0x86, 0x2c, 0x1b, 0xa6, 0xcd, 0x24, 0xfc, 0x91, 0xe3, 0x99,
	0x95, 0xfa, 0x3b, 0xdc, 0x9c, 0xb6, 0xc2, 0x7d, 0x75, 0x60, 0x02, 0x16, 0x8b, 0x33, 0x09, 0x70,
	0xa7, 0xff, 0xb8, 0x7f, 0xd7, 0xf6, 0xb6, 0xc2, 0x7d, 0xd3, 0xa8, 0x37, 0xb8, 0xc5, 0xc5, 0x65,
	0x78, 0x09, 0x8b, 0x63, 0xe0, 0xb8, 0xf9, 0x34, 0x4c, 0x6d, 0xe3, 0x20, 0xf4, 0x5c, 0x17, 0x77,
	0x85, 0xbd, 0x11, 0x9e, 0xf7, 0x65, 0xb5, 0xac, 0x59, 0xaa, 0xdf, 0x03, 0xf3, 0xae, 0x52, 0x46,
	0x4e, 0x19, 0x2e, 0xc1, 0x7c, 0x1a, 0x16, 0x73, 0x5d, 0x84, 0xd9, 0x34, 0x92, 0x4e, 0xa1, 0x69,
	0xd4, 0x3f, 0x36, 0xb8, 0x2d, 0x27, 0x8a, 0xc9, 0x85, 0xa7, 0x48, 0x00, 0x63, 0x33, 0x36, 0x61,
	0x5c, 0x14, 0x31, 0x90, 0x85, 0xc1, 0x44, 0xe4, 0xef, 0xbe, 0xef, 0x60, 0xcc, 0xd4, 0xcb, 0x9f,
	0xb7, 0x0f, 0xf7, 0xef, 0x60, 0x96, 0x56, 0x9b, 0x65, 0xb2, 0x2c, 0xb7, 0xfb, 0x01, 0x7e, 0x80,
	0x9f, 0xf2, 0xe9, 0xc7, 0xd8, 0xac, 0xd4, 0xbf, 0xaf, 0x7a, 0x2b, 0xe1, 0x5d, 0xa8, 0x0c, 0xf3,
	0x30, 0xad, 0xc2, 0x62, 0x11, 0xc8, 0x6a, 0xf1, 0xdc, 0xe8, 0x40, 0xe9, 0xd5, 0x20, 0xd4, 0x14,
	0x48, 0xc7, 0xcd, 0xa7, 0xd1, 0x8d, 0xcc, 0x12, 0x5d, 0x77, 0x04, 0xb1, 0xd2, 0xe9, 0xa4, 0x70,
	0x65, 0xa2, 0x39, 0x8a, 0xdb, 0xf6, 0x22, 0x92, 0xd2, 0xd8, 0xdd, 0x18, 0x59, 0xa9, 0xff, 0x81,
	0x21, 0x0c, 0x55, 0x8a, 0x24, 0x2d, 0x24, 0x29, 0x10, 0x82, 0xc9, 0x58, 0x16, 0xbe, 0xa4, 0x67,
	0xc0, 0x54, 0xf5, 0xc4, 0x7d, 0xe6, 0x24, 0x00, 0x31, 0xcf, 0x9d, 0x90, 0xbb, 0xcb, 0x19, 0x30,
	0xc9, 0xf3, 0x3a, 0x3f, 0x0e, 0x43, 0xa1, 0xd4, 0x49, 0x66, 0x35, 0xc8, 0x17, 0xf0, 0x39, 0x58,
	0xe0, 0x0e, 0xf6, 0x70, 0x9f, 0xb1, 0x8e, 0xb1, 0xcb, 0xf5, 0x2f, 0x09, 0xd3, 0x16, 0xa5, 0xf9,
	0xc9, 0x25, 0x24, 0xa0, 0x09, 0xd1, 0x09, 0x30, 0xa1, 0x4a, 0x32, 0x51, 0xb4, 0x50, 0x55, 0x99,
	0xe6, 0x52, 0xfd, 0x3f, 0x0c, 0x98, 0x4f, 0x39, 0x29, 0xd9, 0x81, 0xf0, 0x2a, 0x49, 0x54, 0xdc,
	0x8b, 0xea, 0x90, 0x18, 0x9e, 0x69, 0xc1, 0x34, 0xd0, 0x45, 0x58, 0xca, 0x41, 0x09, 0x85, 0x98,
	0x25, 0x3a, 0x47, 0x49, 0x02, 0x6a, 0x2e, 0x44, 0xd4, 0x72, 0xc2, 0x5d, 0x31, 0xe4, 0xea, 0x01,
	0xd9, 0x39, 0x51, 0xdb, 0xca, 0xc5, 0x3e, 0xf6, 0xba, 0x7b, 0x04, 0x6b, 0x12, 0x7f, 0x9d, 0xc1,
	0x4a, 0x2d, 0x2c, 0xd7, 0x3f, 0x2e, 0xa7, 0xfc, 0xaf, 0xf0, 0xbb, 0x72, 0xd4, 0x97, 0x61, 0x59,
	0x4b, 0x10, 0x8f, 0x7d, 0x19, 0xce, 0xe5, 0x52, 0x09, 0x31, 0x93, 0x2a, 0x50, 0x29, 0xb8, 0x8e,
	0x4a, 0xe8, 0x25, 0xb8, 0x98, 0x4b, 0xa0, 0xc8, 0x5b, 0x46, 0x16, 0x5c, 0xc8, 0x25, 0x5a, 0xa1,
	0x55, 0x14, 0x4c, 0x21, 0x3a, 0x46, 0x5f, 0xec, 0xdb, 0x5d, 0x27, 0x3a, 0x66, 0x7a, 0xb9, 0x0a,
	0xd6, 0x00, 0xa2, 0x86, 0x1d, 0x99, 0xcb, 0xda, 0xe1, 0x93, 0xdf, 0x62, 0x70, 0xef, 0xa3, 0x65,
	0xcd, 0xe0, 0x98, 0x65, 0x9b, 0xcf, 0x0c, 0x74, 0x15, 0x2e, 0xe5, 0x52, 0xec, 0x3c, 0xc1, 0xb6,
	0x2b, 0x18, 0x3d, 0x2b, 0x21, 0x4b, 0x33, 0x1f, 0xd2, 0x1c, 0x9e, 0x95, 0xd1, 0xcb, 0x1a, 0xd9,
	0x13, 0xcb, 0xc9, 0x7c, 0x56, 0x41, 0x57, 0x34, 0xc2, 0x53, 0x66, 0x82, 0xcc, 0xac, 0x3b, 0x30,
	0xb9, 0x2e, 0x6f, 0xf7, 0x10, 0x5e, 0x29, 0x09, 0x11, 0x8e, 0x36, 0x83, 0x58, 0xe9, 0x77, 0x9c,
	0xc8, 0x34, 0xd0, 0x15, 0xb8, 0x94, 0x44, 0x28, 0x57, 0x10, 0xc8, 0x58, 0x5e, 0xaa, 0x47, 0xb0,
	0x98, 0x09, 0x63, 0x77, 0xfa, 0x6e, 0x9b, 0xf6, 0x7a, 0x0e, 0x16, 0x32, 0xc8, 0x44, 0x6a, 0x91,
	0xc1, 0x7e, 0xb1, 0x8f, 0x83, 0x63, 0xd3, 0x20, 0x56, 0x9e, 0xc1, 0xad, 0xf8, 0x7e, 0xe0, 0x1d,
	0xd9, 0x5d, 0xb3, 0x54, 0x3f, 0x88, 0x9b, 0xb2, 0xf8, 0x28, 0xbb, 0x5c, 0x24, 0xfb, 0x0c, 0x15,
	0x13, 0xf7, 0x37, 0x0f, 0xd3, 0x49, 0x94, 0xe8, 0xac, 0x96, 0xe6, 0xa6, 0xf4, 0xf4, 0x5b, 0x06,
	0xcc, 0xa8, 0x01, 0x57, 0x76, 0xa4, 0x70, 0x23, 0xf0, 0x44, 0x0a, 0xa6, 0x22, 0x44, 0x27, 0x17,
	0xa0, 0xa6, 0x82, 0xd3, 0x1d, 0xa9, 0x23, 0x66, 0x5f, 0x7f, 0x5d, 0xfc, 0x54, 0xa2, 0xcb, 0x34,
	0x3c, 0x29, 0x68, 0xb6, 0x47, 0x35, 0x2b, 0xf5, 0x2f, 0xc7, 0x62, 0x3c, 0x72, 0x3c, 0x29, 0x9d,
	0x42, 0xfe, 0xc8, 0xf1, 0x12, 0x49, 0xa6, 0x02, 0x17, 0xb2, 0x29, 0x22, 0xcb, 0x23, 0x46, 0x66,
	0xa9, 0xfe, 0x04, 0x66, 0xef, 0xab, 0x97, 0x8d, 0x48, 0xee, 0x3c, 0xce, 0x4a, 0x44, 0xcc, 0x7f,
	0x8e, 0xdd, 0x0d, 0x23, 0x31, 0xa2, 0x07, 0x11, 0x15, 0x05, 0x5c, 0xf6, 0x71, 0x8f, 0xb3, 0xe2,
	0xe1, 0x52, 0x76, 0xb1, 0x04, 0xf3, 0x69, 0x78, 0x42, 0xc5, 0x2a, 0x92, 0x77, 0x52, 0xff, 0xb2,
	0x08, 0x23, 0x09, 0x4e, 0x32, 0x8c, 0x64, 0xf9, 0x4c, 0xc3, 0x54, 0x8c, 0x12, 0xa2, 0xca, 0x10,
	0x98, 0x90, 0xf3, 0x48, 0xdd, 0x21, 0x08, 0xfb, 0x97, 0x7d, 0x9c, 0x87, 0xc5, 0x2c, 0x36, 0xb1,
	0x4f, 0xc9, 0xa2, 0x45, 0x7f, 0x71, 0x68, 0x54, 0x90, 0xb2, 0xdf, 0xaf, 0xc4, 0xf9, 0xa9, 0xec,
	0x2d, 0x91, 0x9f, 0x8a, 0x3e, 0xce, 0xc2, 0x84, 0x00, 0x0a, 0xce, 0x53, 0x30, 0x26, 0x40, 0xbb,
	0x21, 0xc9, 0x71, 0x14, 0x9a, 0x8d, 0xc0, 0x26, 0xb9, 0x44, 0xfd, 0xab, 0x99, 0x38, 0x25, 0xbb,
	0x51, 0x53, 0x7f, 0x86, 0x8a, 0x7b, 0x5b, 0x80, 0x99, 0x14, 0x4e, 0x74, 0x9a, 0x0d, 0x9a, 0x72,
	0x2c, 0x1f, 0x1b, 0x1a, 0x5f, 0x28, 0xbb, 0xd4, 0x45, 0x9d, 0xb8, 0x63, 0x35, 0x66, 0xe7, 0x69,
	0x53, 0x17, 0x95, 0xa4, 0x10, 0x1f, 0x28, 0x0c, 0xe4, 0x86, 0x46, 0x9d, 0xc8, 0x2c, 0x36, 0x31,
	0x91, 0x59, 0xb4, 0x30, 0xbf, 0x5b, 0x71, 0xc6, 0xad, 0x4e, 0x55, 0x9c, 0x71, 0x2b, 0x53, 0x25,
	0x80, 0xa2, 0xed, 0x23, 0xb6, 0x6e, 0x58, 0xc2, 0x2c, 0x5b, 0x73, 0x3b, 0x17, 0x29, 0xb7, 0x62,
	0xb6, 0x31, 0x58, 0x31, 0xdb, 0x18, 0x28, 0x47, 0xeb, 0x8b, 0x52, 0xcb, 0xd4, 0x45, 0x34, 0xc2,
	0x3d, 0x67, 0x90, 0x09, 0xf7, 0x9c, 0xc1, 0x2a, 0x93, 0x9c, 0xc1, 0xad, 0x93, 0xb8, 0x51, 0xaa,
	0x7f, 0xc7, 0x80, 0x8b, 0xeb, 0xfa, 0x9b, 0x6b, 0x68, 0xc7, 0x16, 0x5c, 0xd0, 0x90, 0x24, 0x12,
	0x10, 0x0d, 0x8d, 0x10, 0xe2, 0x12, 0x9c, 0xd7, 0x50, 0xc8, 0xe1, 0xff, 0x9a, 0xc1, 0xe7, 0x33,
	0x7d, 0x09, 0x8e, 0x18, 0x7f, 0x06, 0x99, 0xdd, 0xf9, 0xaa, 0x58, 0xd1, 0xb5, 0xb0, 0x03, 0x15,
	0x27, 0x3a, 0x95, 0x36, 0xa4, 0x22, 0x9b, 0x38, 0xa2, 0x87, 0x6b, 0xcc, 0x72, 0xfd, 0xdf, 0x0c,
	0x58, 0xcc, 0xbc, 0x47, 0x50, 0x65, 0xca, 0x20, 0x13, 0x32, 0x65, 0xb0, 0x8a, 0x4c, 0x19, 0x9c,
	0x94, 0x29, 0x8f, 0xed, 0xca, 0x13, 0x87, 0x64, 0x46, 0x66, 0x99, 0xac, 0xe6, 0x0c, 0x76, 0x6b,
	0x3f, 0x60, 0xb9, 0x68, 0x06, 0xd3, 0xc0, 0x47, 0xd8, 0xed, 0x63, 0x96, 0xd5, 0xe7, 0x60, 0xdb,
	0x41, 0xdf, 0x89, 0xcc, 0xe5, 0xfa, 0x77, 0x0d, 0xb8, 0x74, 0x5f, 0x77, 0x41, 0x90, 0x1c, 0xf0,
	0x65, 0x58, 0xd6, 0x12, 0xc5, 0x03, 0xb7, 0xe0, 0x82, 0x96, 0x4a, 0x28, 0xe0, 0x25, 0xb8, 0xa8,
	0xa5, 0x91, 0x16, 0xf1, 0x3d, 0x83, 0xe7, 0x5a, 0xc5, 0x52, 0x5d, 0xe1, 0xd9, 0xdd, 0x00, 0xb1,
	0x44, 0x66, 0x5a, 0x28, 0x97, 0xc8, 0x38, 0x8b, 0x05, 0xfb, 0xa1, 0xa1, 0x6c, 0x79, 0xc5, 0x9d,
	0x44, 0xc2, 0x24, 0x32, 0x88, 0x78, 0xa7, 0x7c, 0x15, 0xac, 0x0c, 0x36, 0x7e, 0xa1, 0xf3, 0x05,
	0x7c, 0xcc, 0x77, 0x76, 0xea, 0x76, 0x5b, 0xd0, 0xb1, 0x57, 0x71, 0x66, 0x89, 0x88, 0x98, 0x41,
	0xd2, 0x1f, 0x3b, 0x9e, 0xcf, 0x93, 0x50, 0xb3, 0x5c, 0xff, 0xc3, 0x12, 0xb7, 0xec, 0xf4, 0x0d,
	0x48, 0x09, 0xdf, 0x9d, 0x46, 0xc6, 0xa2, 0x0a, 0x6d, 0xa5, 0x29, 0x1a, 0x76, 0xc7, 0x0e, 0x56,
	0x0f, 0xec, 0x80, 0xe4, 0x9d, 0x3a, 0x36, 0xc2, 0xea, 0x4a, 0x05, 0x14, 0xcc, 0xf2, 0xca, 0x32,
	0x48, 0xa4, 0x29, 0xee, 0xda, 0x51, 0xfb, 0xc0, 0xac, 0xc8, 0x20, 0x91, 0xc6, 0x37, 0xed, 0x3d,
	0x1c, 0x1d, 0x9b, 0xa6, 0xdc, 0x95, 0x64, 0x08, 0x22, 0x9b, 0x2f, 0x9a, 0x65, 0xf4, 0x32, 0xbc,
	0x94, 0x2f, 0x86, 0x13, 0x1e, 0x36, 0x70, 0xe8, 0x84, 0x11, 0x71, 0x45, 0xe6, 0xfb, 0xf5, 0xf7,
	0xa0, 0xba, 0x22, 0x2b, 0x05, 0x4c, 0x18, 0xe7, 0x0a, 0xa1, 0x30, 0x73, 0x88, 0x44, 0x69, 0xfa,
	0xf3, 0x76, 0xe0, 0x1d, 0xe2, 0x80, 0xbd, 0x99, 0x60, 0x55, 0xff, 0xd4, 0x61, 0x94, 0xea, 0x8f,
	0x61, 0x8c, 0x55, 0xd0, 0x37, 0xe9, 0x3b, 0xf9, 0x39, 0x40, 0xca, 0x63, 0x22, 0xbd, 0x55, 0xe0,
	0xe2, 0xe6, 0x03, 0x96, 0x7b, 0x29, 0x08, 0x71, 0xa7, 0x80, 0x59, 0xaa, 0xff, 0x8f, 0x01, 0xe6,
	0xa6, 0x52, 0x27, 0x20, 0x7c, 0xf3, 0xe6, 0x83, 0x47, 0x9b, 0x3b, 0xeb, 0xad, 0xe6, 0xce, 0xca,
	0xce, 0x6e, 0xb3, 0xb5, 0xf3, 0xa5, 0xed, 0xf5, 0xd6, 0xee, 0x83, 0xe6, 0xf6, 0xfa, 0xea, 0xe6,
	0x9d, 0xcd, 0xf5, 0x35, 0x73, 0x88, 0x78, 0xde, 0x1c, 0x9a, 0xed, 0xc6, 0xc3, 0xd5, 0xf5, 0x66,
	0x73, 0xf3, 0xc1, 0x06, 0x4b, 0xe0, 0x73, 0x48, 0x56, 0x56, 0x57, 0xd7, 0xb7, 0x77, 0x98, 0x8f,
	0xcc, 0x41, 0x37, 0xd6, 0xef, 0xad, 0xaf, 0xee, 0x98, 0xc3, 0x64, 0x82, 0x72, 0xd0, 0x3b, 0x9b,
	0x5b, 0xeb, 0xad, 0x87, 0xbb, 0x3b, 0x6c, 0xdb, 0x98, 0x43, 0xb0, 0xba, 0xf2, 0x60, 0x75, 0xfd,
	0x7e, 0xab, 0xb9, 0xb9, 0xf1, 0xc0, 0x3c, 0xa3, 0xe9, 0x83, 0xd1, 0x98, 0x23, 0xf5, 0x5f, 0x35,
	0x74, 0x67, 0xcf, 0x13, 0x1e, 0x20, 0x17, 0x9b, 0xf5, 0x00, 0xb9, 0x64, 0x6c, 0x06, 0x0d, 0xb9,
	0x37, 0xcd, 0x25, 0x5a, 0xe7, 0xc7, 0x66, 0xcd, 0x52, 0xfd, 0x17, 0x45, 0xb8, 0xca, 0x10, 0x3e,
	0xf4, 0x23, 0xe9, 0x47, 0xf2, 0x90, 0xd9, 0x8d, 0x7b, 0x1e, 0xd5, 0x4a, 0xa7, 0xa3, 0xac, 0xb0,
	0x3c, 0x8a, 0x35, 0x4c, 0x36, 0x3d, 0x7f, 0x64, 0xc0, 0x74, 0xce, 0x17, 0x36, 0x12, 0x27, 0x24,
	0x38, 0xa9, 0x86, 0x73, 0xb0, 0x90, 0xc0, 0xd0, 0xcf, 0x92, 0xfc, 0xf5, 0x2c, 0xf5, 0xcc, 0x39,
	0xec, 0xd8, 0x57, 0x7d, 0xf9, 0xe2, 0x78, 0x19, 0xce, 0x69, 0x89, 0x9a, 0xfd, 0x1e, 0xb3, 0x8a,
	0x1c, 0x0a, 0xf1, 0x05, 0xd4, 0x2c, 0xdf, 0xfc, 0x93, 0x97, 0x13, 0x2f, 0x29, 0xc9, 0x98, 0x9a,
	0x47, 0x01, 0x72, 0xe9, 0x27, 0x8b, 0xe4, 0x75, 0x50, 0x48, 0x53, 0xa0, 0x96, 0x77, 0x49, 0x55,
	0xed, 0x95, 0x13, 0xd3, 0x86, 0xbe, 0x35, 0x84, 0x9e, 0xc0, 0x44, 0xe2, 0xae, 0x25, 0xa4, 0xa9,
	0x8c, 0x49, 0x5f, 0x24, 0x55, 0x7b, 0xf9, 0x44, 0x74, 0xa2, 0x8f, 0xc4, 0x7d, 0x4a, 0xba, 0x3e,
	0xd2, 0xf7, 0x3b, 0xe9, 0xfa, 0xc8, 0x5e, 0xce, 0x34, 0x84, 0xbe, 0x65, 0xc0, 0xa2, 0xf6, 0xe6,
	0x23, 0x74, 0x33, 0x9f, 0x51, 0xd1, 0x7d, 0x4e, 0xb5, 0x37, 0x4f, 0xdd, 0x46, 0x0c, 0x36, 0x71,
	0x5f, 0x92, 0x6e, 0xb0, 0xe9, 0x5b, 0x98, 0x74, 0x83, 0xcd, 0x5c, 0xbe, 0x64, 0x0d, 0xa1, 0x0f,
	0xd3, 0x17, 0xf5, 0x50, 0x2b, 0x79, 0xa5, 0x98, 0x41, 0xe2, 0x8e, 0xa4, 0xda, 0xab, 0x27, 0x27,
	0xa6, 0x5d, 0x62, 0x98, 0x4c, 0xe2, 0xd0, 0xcb, 0x27, 0xe1, 0x40, 0xba, 0xba, 0x76, 0x32, 0x42,
	0xb5, 0x9b, 0xf8, 0xf6, 0x9f, 0xa2, 0x6e, 0x12, 0xf7, 0x0a, 0x15, 0x75, 0x93, 0xbc, 0x4c, 0x88,
	0x75, 0x93, 0xbc, 0x6e, 0x07, 0xe9, 0xcd, 0xf9, 0x64, 0xa3, 0xc9, 0xde, 0xde, 0xc3, 0xba, 0x49,
	0x5e, 0x8b, 0x83, 0xf4, 0x16, 0x7d, 0xb2, 0x6e, 0x72, 0x6e, 0xd9, 0x19, 0x22, 0x3e, 0x23, 0x73,
	0x4f, 0x8e, 0xce, 0x67, 0xe4, 0x5d, 0xc8, 0xa3, 0xf3, 0x19, 0xb9, 0x97, 0xef, 0x30, 0x13, 0x4f,
	0xdc, 0x94, 0xa3, 0x33, 0xf1, 0xf4, 0x55, 0x3c, 0x3a, 0x13, 0xcf, 0x5e, 0xbb, 0x33, 0x84, 0x7e,
	0xc9, 0x50, 0xea, 0x82, 0xb3, 0x17, 0xc3, 0xbc, 0xa9, 0x9d, 0x6c, 0xfd, 0xfd, 0x0c, 0xb5, 0xcf,
	0x9c, 0xbe, 0x11, 0x95, 0xe5, 0x57, 0xd4, 0x12, 0xef, 0xbc, 0x7b, 0x80, 0xde, 0x3a, 0x05, 0xe3,
	0xf8, 0x3e, 0x83, 0xda, 0xdb, 0xcf, 0xd3, 0x8c, 0x4a, 0xf4, 0x0b, 0x06, 0x2c, 0xe8, 0xaa, 0xde,
	0xd1, 0x1b, 0x03, 0xd8, 0x66, 0x2b, 0xff, 0x6b, 0x37, 0x4f, 0xdb, 0x44, 0xfa, 0x5c, 0x6d, 0xf5,
	0x38, 0x1a, 0xc8, 0x33, 0x5b, 0x2b, 0x5f, 0x7b, 0xf3, 0xd4, 0x6d, 0xa8, 0x20, 0xcf, 0x0c, 0x98,
	0xd7, 0x94, 0x7b, 0xa3, 0xd7, 0x4f, 0xc2, 0x52, 0xad, 0x39, 0xaf, 0xbd, 0x71, 0xca, 0x16, 0xea,
	0x8c, 0xe4, 0xd6, 0x3c, 0x17, 0xcc, 0x88, 0xae, 0x28, 0xbb, 0x60, 0x46, 0xb4, 0x65, 0xd5, 0x7c,
	0xd5, 0xe8, 0x6b, 0xaf, 0x75, 0xab, 0xa6, 0xb0, 0x3c, 0x5c, 0xb7, 0x6a, 0x8a, 0x4b, 0xbc, 0xad,
	0x21, 0xf4, 0x14, 0x66, 0xf2, 0x8a, 0xa6, 0xd1, 0x6b, 0x3a, 0x27, 0x90, 0x5b, 0xf4, 0x5d, 0xbb,
	0x7e, 0x1a, 0x72, 0xd5, 0x1a, 0xf2, 0x0a, 0x82, 0x0b, 0xac, 0x41, 0x53, 0xea, 0x5c, 0x60, 0x0d,
	0xba, 0x8a, 0x63, 0x6b, 0x08, 0x7d, 0x1d, 0xe6, 0xf2, 0xeb, 0xbc, 0x91, 0xe6, 0x52, 0x0f, 0x6d,
	0x09, 0x79, 0xed, 0xf5, 0xd3, 0x35, 0x10, 0xaa, 0xcf, 0xab, 0x0a, 0xd6, 0xa9, 0x5e, 0x53, 0x9b,
	0xac, 0x53, 0xbd, 0xb6, 0xe0, 0x98, 0x26, 0x26, 0xd9, 0xd2, 0x5f, 0x5d, 0x62, 0x92, 0x5b, 0x9c,
	0xac, 0x4b, 0x4c, 0x34, 0x15, 0xc5, 0x43, 0x89, 0x84, 0x59, 0x96, 0xd3, 0x0e, 0x4a, 0x98, 0x95,
	0x52, 0xb7, 0x81, 0x09, 0xb3, 0x5a, 0xe3, 0x66, 0x0d, 0xa1, 0xdf, 0x36, 0x06, 0x94, 0xa7, 0x52,
	0x01, 0x6e, 0x69, 0x99, 0x0e, 0x2c, 0x7e, 0xae, 0xbd, 0xfb, 0xdc, 0x6d, 0xa9, 0x80, 0xdf, 0x65,
	0x77, 0x7a, 0x14, 0x94, 0x2a, 0xbf, 0xfd, 0x1c, 0x1d, 0x10, 0xc1, 0xde, 0x79, 0xae, 0x76, 0x52,
	0xa8, 0xe6, 0xf3, 0x08, 0xd5, 0x7c, 0x4e, 0xa1, 0x9a, 0x27, 0x10, 0x8a, 0xf8, 0x6c, 0x5d, 0x75,
	0xa4, 0xce, 0x67, 0x17, 0x94, 0x7f, 0xea, 0x7c, 0x76, 0x61, 0x01, 0xe6, 0x10, 0xfa, 0x91, 0x38,
	0x86, 0x5e, 0xac, 0x9f, 0x77, 0x5f, 0xa0, 0xfe, 0xb6, 0xf6, 0xb9, 0xe7, 0x6f, 0xac, 0x46, 0xb7,
	0xdc, 0x02, 0xcc, 0x82, 0xe8, 0xa6, 0x2b, 0xf6, 0x2c, 0x88, 0x6e, 0xda, 0x1a, 0x4f, 0x1e, 0xdd,
	0xf4, 0xf5, 0x8c, 0xba, 0xe8, 0x56, 0x58, 0xf4, 0xa9, 0x8b, 0x6e, 0xc5, 0x65, 0x93, 0xd6, 0x10,
	0xfa, 0x65, 0x03, 0x96, 0x0a, 0xea, 0x69, 0xd0, 0x00, 0xbe, 0xf9, 0xb5, 0x45, 0xb5, 0xb7, 0x9e,
	0xa3, 0x15, 0x15, 0xe7, 0xd7, 0x33, 0x75, 0x4b, 0x99, 0x12, 0x82, 0x77, 0x4e, 0xc2, 0x3b, 0xe7,
	0x94, 0x7a, 0xed, 0xb3, 0xcf, 0xd7, 0x50, 0xae, 0xfb, 0xc2, 0x73, 0xe4, 0xba, 0x75, 0x3f, 0xe8,
	0xec, 0x7c, 0xed, 0x9d, 0xe7, 0x6a, 0x47, 0x85, 0x8a, 0x60, 0x3a, 0xe7, 0xdc, 0x34, 0xd2, 0xdd,
	0x6b, 0x94, 0x7b, 0xf6, 0xbc, 0xf6, 0xda, 0x29, 0xa8, 0x45, 0xaf, 0x39, 0x27, 0x91, 0x75, 0xbd,
	0xe6, 0x9f, 0xcc, 0xae, 0xbd, 0x76, 0x0a, 0x6a, 0x11, 0x91, 0xb3, 0x27, 0x93, 0x75, 0x11, 0x39,
	0xf7, 0xd4, 0xb3, 0x2e, 0x22, 0x6b, 0x0e, 0x3c, 0x0f, 0xa1, 0xaf, 0xc1, 0x6c, 0xee, 0x19, 0x62,
	0x74, 0xfd, 0x54, 0x07, 0x8e, 0x3f, 0xac, 0xdd, 0x38, 0xe5, 0x01, 0x65, 0xf9, 0xf6, 0x45, 0x39,
	0x4a, 0xac, 0x7f, 0xfb, 0x92, 0x38, 0x2a, 0x5c, 0xf0, 0xf6, 0x25, 0x79, 0x7c, 0xd8, 0x1a, 0xba,
	0xfd, 0xd9, 0x2f, 0xbf, 0xbd, 0xef, 0x75, 0x6d, 0x77, 0xff, 0xfa, 0x5b, 0x37, 0xa3, 0xe8, 0x7a,
	0xdb, 0xeb, 0xdd, 0xa0, 0xff, 0xe0, 0xa7, 0xed, 0x75, 0x6f, 0x84, 0x38, 0x38, 0x72, 0xda, 0x38,
	0xcc, 0xfd, 0x3f, 0x40, 0x4f, 0x46, 0x28, 0xdd, 0x9b, 0xff, 0x1b, 0x00, 0x00, 0xff, 0xff, 0xd9,
	0xe5, 0xc6, 0x6b, 0x4a, 0x68, 0x00, 0x00,
}
