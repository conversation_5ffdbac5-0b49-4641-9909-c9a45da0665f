// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/esport_hall (interfaces: EsportHallClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_hall "golang.52tt.com/protocol/services/esport_hall"
	grpc "google.golang.org/grpc"
)

// MockEsportHallClient is a mock of EsportHallClient interface.
type MockEsportHallClient struct {
	ctrl     *gomock.Controller
	recorder *MockEsportHallClientMockRecorder
}

// MockEsportHallClientMockRecorder is the mock recorder for MockEsportHallClient.
type MockEsportHallClientMockRecorder struct {
	mock *MockEsportHallClient
}

// NewMockEsportHallClient creates a new mock instance.
func NewMockEsportHallClient(ctrl *gomock.Controller) *MockEsportHallClient {
	mock := &MockEsportHallClient{ctrl: ctrl}
	mock.recorder = &MockEsportHallClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportHallClient) EXPECT() *MockEsportHallClientMockRecorder {
	return m.recorder
}

// AddCoachRecommend mocks base method.
func (m *MockEsportHallClient) AddCoachRecommend(arg0 context.Context, arg1 *esport_hall.AddCoachRecommendRequest, arg2 ...grpc.CallOption) (*esport_hall.AddCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddCoachRecommend", varargs...)
	ret0, _ := ret[0].(*esport_hall.AddCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddCoachRecommend indicates an expected call of AddCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) AddCoachRecommend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).AddCoachRecommend), varargs...)
}

// AddIgnoreRecommendCoach mocks base method.
func (m *MockEsportHallClient) AddIgnoreRecommendCoach(arg0 context.Context, arg1 *esport_hall.AddIgnoreRecommendCoachRequest, arg2 ...grpc.CallOption) (*esport_hall.AddIgnoreRecommendCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddIgnoreRecommendCoach", varargs...)
	ret0, _ := ret[0].(*esport_hall.AddIgnoreRecommendCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddIgnoreRecommendCoach indicates an expected call of AddIgnoreRecommendCoach.
func (mr *MockEsportHallClientMockRecorder) AddIgnoreRecommendCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIgnoreRecommendCoach", reflect.TypeOf((*MockEsportHallClient)(nil).AddIgnoreRecommendCoach), varargs...)
}

// BatchGetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallClient) BatchGetQuickReceiveSwitch(arg0 context.Context, arg1 *esport_hall.BatchGetQuickReceiveSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.BatchGetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetQuickReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.BatchGetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetQuickReceiveSwitch indicates an expected call of BatchGetQuickReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) BatchGetQuickReceiveSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).BatchGetQuickReceiveSwitch), varargs...)
}

// BatchGetSkillProductInfo mocks base method.
func (m *MockEsportHallClient) BatchGetSkillProductInfo(arg0 context.Context, arg1 *esport_hall.BatchGetSkillProductInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.BatchGetSkillProductInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetSkillProductInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.BatchGetSkillProductInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetSkillProductInfo indicates an expected call of BatchGetSkillProductInfo.
func (mr *MockEsportHallClientMockRecorder) BatchGetSkillProductInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetSkillProductInfo", reflect.TypeOf((*MockEsportHallClient)(nil).BatchGetSkillProductInfo), varargs...)
}

// CheckFirstRoundOrderRight mocks base method.
func (m *MockEsportHallClient) CheckFirstRoundOrderRight(arg0 context.Context, arg1 *esport_hall.CheckFirstRoundOrderRightRequest, arg2 ...grpc.CallOption) (*esport_hall.CheckFirstRoundOrderRightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckFirstRoundOrderRight", varargs...)
	ret0, _ := ret[0].(*esport_hall.CheckFirstRoundOrderRightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFirstRoundOrderRight indicates an expected call of CheckFirstRoundOrderRight.
func (mr *MockEsportHallClientMockRecorder) CheckFirstRoundOrderRight(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFirstRoundOrderRight", reflect.TypeOf((*MockEsportHallClient)(nil).CheckFirstRoundOrderRight), varargs...)
}

// ClearFirstRoundOrder mocks base method.
func (m *MockEsportHallClient) ClearFirstRoundOrder(arg0 context.Context, arg1 *esport_hall.ClearFirstRoundOrderRequest, arg2 ...grpc.CallOption) (*esport_hall.ClearFirstRoundOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearFirstRoundOrder", varargs...)
	ret0, _ := ret[0].(*esport_hall.ClearFirstRoundOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearFirstRoundOrder indicates an expected call of ClearFirstRoundOrder.
func (mr *MockEsportHallClientMockRecorder) ClearFirstRoundOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearFirstRoundOrder", reflect.TypeOf((*MockEsportHallClient)(nil).ClearFirstRoundOrder), varargs...)
}

// ClearNewCustomerOrder mocks base method.
func (m *MockEsportHallClient) ClearNewCustomerOrder(arg0 context.Context, arg1 *esport_hall.ClearNewCustomerOrderRequest, arg2 ...grpc.CallOption) (*esport_hall.ClearNewCustomerOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClearNewCustomerOrder", varargs...)
	ret0, _ := ret[0].(*esport_hall.ClearNewCustomerOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ClearNewCustomerOrder indicates an expected call of ClearNewCustomerOrder.
func (mr *MockEsportHallClientMockRecorder) ClearNewCustomerOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearNewCustomerOrder", reflect.TypeOf((*MockEsportHallClient)(nil).ClearNewCustomerOrder), varargs...)
}

// CreateEsportGameCard mocks base method.
func (m *MockEsportHallClient) CreateEsportGameCard(arg0 context.Context, arg1 *esport_hall.CreateEsportGameCardRequest, arg2 ...grpc.CallOption) (*esport_hall.CreateEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateEsportGameCard", varargs...)
	ret0, _ := ret[0].(*esport_hall.CreateEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEsportGameCard indicates an expected call of CreateEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) CreateEsportGameCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).CreateEsportGameCard), varargs...)
}

// DelCoachRecommend mocks base method.
func (m *MockEsportHallClient) DelCoachRecommend(arg0 context.Context, arg1 *esport_hall.DelCoachRecommendRequest, arg2 ...grpc.CallOption) (*esport_hall.DelCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelCoachRecommend", varargs...)
	ret0, _ := ret[0].(*esport_hall.DelCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelCoachRecommend indicates an expected call of DelCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) DelCoachRecommend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).DelCoachRecommend), varargs...)
}

// DelSkillProduct mocks base method.
func (m *MockEsportHallClient) DelSkillProduct(arg0 context.Context, arg1 *esport_hall.DelSkillProductRequest, arg2 ...grpc.CallOption) (*esport_hall.DelSkillProductResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DelSkillProduct", varargs...)
	ret0, _ := ret[0].(*esport_hall.DelSkillProductResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DelSkillProduct indicates an expected call of DelSkillProduct.
func (mr *MockEsportHallClientMockRecorder) DelSkillProduct(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelSkillProduct", reflect.TypeOf((*MockEsportHallClient)(nil).DelSkillProduct), varargs...)
}

// DeleteEsportGameCard mocks base method.
func (m *MockEsportHallClient) DeleteEsportGameCard(arg0 context.Context, arg1 *esport_hall.DeleteEsportGameCardRequest, arg2 ...grpc.CallOption) (*esport_hall.DeleteEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteEsportGameCard", varargs...)
	ret0, _ := ret[0].(*esport_hall.DeleteEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteEsportGameCard indicates an expected call of DeleteEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) DeleteEsportGameCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).DeleteEsportGameCard), varargs...)
}

// GetAllSkillList mocks base method.
func (m *MockEsportHallClient) GetAllSkillList(arg0 context.Context, arg1 *esport_hall.GetAllSkillListRequest, arg2 ...grpc.CallOption) (*esport_hall.GetAllSkillListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllSkillList", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetAllSkillListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllSkillList indicates an expected call of GetAllSkillList.
func (mr *MockEsportHallClientMockRecorder) GetAllSkillList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllSkillList", reflect.TypeOf((*MockEsportHallClient)(nil).GetAllSkillList), varargs...)
}

// GetCoachIncentiveAddition mocks base method.
func (m *MockEsportHallClient) GetCoachIncentiveAddition(arg0 context.Context, arg1 *esport_hall.GetCoachIncentiveAdditionRequest, arg2 ...grpc.CallOption) (*esport_hall.GetCoachIncentiveAdditionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachIncentiveAddition", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetCoachIncentiveAdditionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachIncentiveAddition indicates an expected call of GetCoachIncentiveAddition.
func (mr *MockEsportHallClientMockRecorder) GetCoachIncentiveAddition(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachIncentiveAddition", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachIncentiveAddition), varargs...)
}

// GetCoachIncentiveTaskInfo mocks base method.
func (m *MockEsportHallClient) GetCoachIncentiveTaskInfo(arg0 context.Context, arg1 *esport_hall.GetCoachIncentiveTaskInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.GetCoachIncentiveTaskInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachIncentiveTaskInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetCoachIncentiveTaskInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachIncentiveTaskInfo indicates an expected call of GetCoachIncentiveTaskInfo.
func (mr *MockEsportHallClientMockRecorder) GetCoachIncentiveTaskInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachIncentiveTaskInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachIncentiveTaskInfo), varargs...)
}

// GetCoachMinPrice mocks base method.
func (m *MockEsportHallClient) GetCoachMinPrice(arg0 context.Context, arg1 *esport_hall.GetCoachMinPriceRequest, arg2 ...grpc.CallOption) (*esport_hall.GetCoachMinPriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachMinPrice", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetCoachMinPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachMinPrice indicates an expected call of GetCoachMinPrice.
func (mr *MockEsportHallClientMockRecorder) GetCoachMinPrice(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachMinPrice", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachMinPrice), varargs...)
}

// GetCoachRecommend mocks base method.
func (m *MockEsportHallClient) GetCoachRecommend(arg0 context.Context, arg1 *esport_hall.GetCoachRecommendRequest, arg2 ...grpc.CallOption) (*esport_hall.GetCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCoachRecommend", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoachRecommend indicates an expected call of GetCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) GetCoachRecommend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).GetCoachRecommend), varargs...)
}

// GetEsportGameCardInfo mocks base method.
func (m *MockEsportHallClient) GetEsportGameCardInfo(arg0 context.Context, arg1 *esport_hall.GetEsportGameCardInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.GetEsportGameCardInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEsportGameCardInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetEsportGameCardInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameCardInfo indicates an expected call of GetEsportGameCardInfo.
func (mr *MockEsportHallClientMockRecorder) GetEsportGameCardInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameCardInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetEsportGameCardInfo), varargs...)
}

// GetEsportGameCardList mocks base method.
func (m *MockEsportHallClient) GetEsportGameCardList(arg0 context.Context, arg1 *esport_hall.GetEsportGameCardListRequest, arg2 ...grpc.CallOption) (*esport_hall.GetEsportGameCardListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEsportGameCardList", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetEsportGameCardListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEsportGameCardList indicates an expected call of GetEsportGameCardList.
func (mr *MockEsportHallClientMockRecorder) GetEsportGameCardList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEsportGameCardList", reflect.TypeOf((*MockEsportHallClient)(nil).GetEsportGameCardList), varargs...)
}

// GetFirstRoundDiscountGameList mocks base method.
func (m *MockEsportHallClient) GetFirstRoundDiscountGameList(arg0 context.Context, arg1 *esport_hall.GetFirstRoundDiscountGameListRequest, arg2 ...grpc.CallOption) (*esport_hall.GetFirstRoundDiscountGameListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundDiscountGameList", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetFirstRoundDiscountGameListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundDiscountGameList indicates an expected call of GetFirstRoundDiscountGameList.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundDiscountGameList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundDiscountGameList", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundDiscountGameList), varargs...)
}

// GetFirstRoundDiscountInfo mocks base method.
func (m *MockEsportHallClient) GetFirstRoundDiscountInfo(arg0 context.Context, arg1 *esport_hall.GetFirstRoundDiscountInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.GetFirstRoundDiscountInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundDiscountInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetFirstRoundDiscountInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundDiscountInfo indicates an expected call of GetFirstRoundDiscountInfo.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundDiscountInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundDiscountInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundDiscountInfo), varargs...)
}

// GetFirstRoundLabelBySkill mocks base method.
func (m *MockEsportHallClient) GetFirstRoundLabelBySkill(arg0 context.Context, arg1 *esport_hall.GetFirstRoundLabelBySkillRequest, arg2 ...grpc.CallOption) (*esport_hall.GetFirstRoundLabelBySkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundLabelBySkill", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetFirstRoundLabelBySkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundLabelBySkill indicates an expected call of GetFirstRoundLabelBySkill.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundLabelBySkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundLabelBySkill", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundLabelBySkill), varargs...)
}

// GetFirstRoundLabelByUid mocks base method.
func (m *MockEsportHallClient) GetFirstRoundLabelByUid(arg0 context.Context, arg1 *esport_hall.GetFirstRoundLabelByUidRequest, arg2 ...grpc.CallOption) (*esport_hall.GetFirstRoundLabelByUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundLabelByUid", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetFirstRoundLabelByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundLabelByUid indicates an expected call of GetFirstRoundLabelByUid.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundLabelByUid(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundLabelByUid", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundLabelByUid), varargs...)
}

// GetFirstRoundOrderRight mocks base method.
func (m *MockEsportHallClient) GetFirstRoundOrderRight(arg0 context.Context, arg1 *esport_hall.GetFirstRoundOrderRightRequest, arg2 ...grpc.CallOption) (*esport_hall.GetFirstRoundOrderRightResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstRoundOrderRight", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetFirstRoundOrderRightResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstRoundOrderRight indicates an expected call of GetFirstRoundOrderRight.
func (mr *MockEsportHallClientMockRecorder) GetFirstRoundOrderRight(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstRoundOrderRight", reflect.TypeOf((*MockEsportHallClient)(nil).GetFirstRoundOrderRight), varargs...)
}

// GetGameCoachList mocks base method.
func (m *MockEsportHallClient) GetGameCoachList(arg0 context.Context, arg1 *esport_hall.GetGameCoachListRequest, arg2 ...grpc.CallOption) (*esport_hall.GetCoachListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameCoachList", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetCoachListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameCoachList indicates an expected call of GetGameCoachList.
func (mr *MockEsportHallClientMockRecorder) GetGameCoachList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameCoachList", reflect.TypeOf((*MockEsportHallClient)(nil).GetGameCoachList), varargs...)
}

// GetGameCoachListByUid mocks base method.
func (m *MockEsportHallClient) GetGameCoachListByUid(arg0 context.Context, arg1 *esport_hall.GetGameCoachListByUidRequest, arg2 ...grpc.CallOption) (*esport_hall.GetGameCoachListByUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGameCoachListByUid", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetGameCoachListByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGameCoachListByUid indicates an expected call of GetGameCoachListByUid.
func (mr *MockEsportHallClientMockRecorder) GetGameCoachListByUid(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGameCoachListByUid", reflect.TypeOf((*MockEsportHallClient)(nil).GetGameCoachListByUid), varargs...)
}

// GetGamePriceProperty mocks base method.
func (m *MockEsportHallClient) GetGamePriceProperty(arg0 context.Context, arg1 *esport_hall.GetGamePricePropertyRequest, arg2 ...grpc.CallOption) (*esport_hall.GetGamePricePropertyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGamePriceProperty", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetGamePricePropertyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGamePriceProperty indicates an expected call of GetGamePriceProperty.
func (mr *MockEsportHallClientMockRecorder) GetGamePriceProperty(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGamePriceProperty", reflect.TypeOf((*MockEsportHallClient)(nil).GetGamePriceProperty), varargs...)
}

// GetGlobalRcmdCoach mocks base method.
func (m *MockEsportHallClient) GetGlobalRcmdCoach(arg0 context.Context, arg1 *esport_hall.GetGlobalRcmdCoachRequest, arg2 ...grpc.CallOption) (*esport_hall.GetGlobalRcmdCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGlobalRcmdCoach", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetGlobalRcmdCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGlobalRcmdCoach indicates an expected call of GetGlobalRcmdCoach.
func (mr *MockEsportHallClientMockRecorder) GetGlobalRcmdCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGlobalRcmdCoach", reflect.TypeOf((*MockEsportHallClient)(nil).GetGlobalRcmdCoach), varargs...)
}

// GetNewCustomerDiscountInfo mocks base method.
func (m *MockEsportHallClient) GetNewCustomerDiscountInfo(arg0 context.Context, arg1 *esport_hall.GetNewCustomerDiscountInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.GetNewCustomerDiscountInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewCustomerDiscountInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetNewCustomerDiscountInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerDiscountInfo indicates an expected call of GetNewCustomerDiscountInfo.
func (mr *MockEsportHallClientMockRecorder) GetNewCustomerDiscountInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerDiscountInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetNewCustomerDiscountInfo), varargs...)
}

// GetNewCustomerPriceBySkill mocks base method.
func (m *MockEsportHallClient) GetNewCustomerPriceBySkill(arg0 context.Context, arg1 *esport_hall.GetNewCustomerPriceBySkillRequest, arg2 ...grpc.CallOption) (*esport_hall.GetNewCustomerPriceBySkillResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewCustomerPriceBySkill", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetNewCustomerPriceBySkillResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerPriceBySkill indicates an expected call of GetNewCustomerPriceBySkill.
func (mr *MockEsportHallClientMockRecorder) GetNewCustomerPriceBySkill(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerPriceBySkill", reflect.TypeOf((*MockEsportHallClient)(nil).GetNewCustomerPriceBySkill), varargs...)
}

// GetNewCustomerPriceByUid mocks base method.
func (m *MockEsportHallClient) GetNewCustomerPriceByUid(arg0 context.Context, arg1 *esport_hall.GetNewCustomerPriceByUidRequest, arg2 ...grpc.CallOption) (*esport_hall.GetNewCustomerPriceByUidResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNewCustomerPriceByUid", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetNewCustomerPriceByUidResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNewCustomerPriceByUid indicates an expected call of GetNewCustomerPriceByUid.
func (mr *MockEsportHallClientMockRecorder) GetNewCustomerPriceByUid(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNewCustomerPriceByUid", reflect.TypeOf((*MockEsportHallClient)(nil).GetNewCustomerPriceByUid), varargs...)
}

// GetOperationCoachRecommendWithoutExposed mocks base method.
func (m *MockEsportHallClient) GetOperationCoachRecommendWithoutExposed(arg0 context.Context, arg1 *esport_hall.GetOperationCoachRecommendWithoutExposedRequest, arg2 ...grpc.CallOption) (*esport_hall.GetOperationCoachRecommendWithoutExposedResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOperationCoachRecommendWithoutExposed", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetOperationCoachRecommendWithoutExposedResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOperationCoachRecommendWithoutExposed indicates an expected call of GetOperationCoachRecommendWithoutExposed.
func (mr *MockEsportHallClientMockRecorder) GetOperationCoachRecommendWithoutExposed(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOperationCoachRecommendWithoutExposed", reflect.TypeOf((*MockEsportHallClient)(nil).GetOperationCoachRecommendWithoutExposed), varargs...)
}

// GetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallClient) GetQuickReceiveSwitch(arg0 context.Context, arg1 *esport_hall.GetQuickReceiveSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.GetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetQuickReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetQuickReceiveSwitch indicates an expected call of GetQuickReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) GetQuickReceiveSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).GetQuickReceiveSwitch), varargs...)
}

// GetReCoachForUGC mocks base method.
func (m *MockEsportHallClient) GetReCoachForUGC(arg0 context.Context, arg1 *esport_hall.GetReCoachForUGCRequest, arg2 ...grpc.CallOption) (*esport_hall.GetReCoachForUGCResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReCoachForUGC", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetReCoachForUGCResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReCoachForUGC indicates an expected call of GetReCoachForUGC.
func (mr *MockEsportHallClientMockRecorder) GetReCoachForUGC(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReCoachForUGC", reflect.TypeOf((*MockEsportHallClient)(nil).GetReCoachForUGC), varargs...)
}

// GetReceiveTimeFrame mocks base method.
func (m *MockEsportHallClient) GetReceiveTimeFrame(arg0 context.Context, arg1 *esport_hall.GetReceiveTimeFrameRequest, arg2 ...grpc.CallOption) (*esport_hall.GetReceiveTimeFrameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetReceiveTimeFrame", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetReceiveTimeFrameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetReceiveTimeFrame indicates an expected call of GetReceiveTimeFrame.
func (mr *MockEsportHallClientMockRecorder) GetReceiveTimeFrame(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetReceiveTimeFrame", reflect.TypeOf((*MockEsportHallClient)(nil).GetReceiveTimeFrame), varargs...)
}

// GetRecommendSkillProduct mocks base method.
func (m *MockEsportHallClient) GetRecommendSkillProduct(arg0 context.Context, arg1 *esport_hall.GetRecommendSkillProductReq, arg2 ...grpc.CallOption) (*esport_hall.GetRecommendSkillProductResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecommendSkillProduct", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetRecommendSkillProductResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendSkillProduct indicates an expected call of GetRecommendSkillProduct.
func (mr *MockEsportHallClientMockRecorder) GetRecommendSkillProduct(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendSkillProduct", reflect.TypeOf((*MockEsportHallClient)(nil).GetRecommendSkillProduct), varargs...)
}

// GetSkillProductByUidGameId mocks base method.
func (m *MockEsportHallClient) GetSkillProductByUidGameId(arg0 context.Context, arg1 *esport_hall.GetSkillProductByUidGameIdRequest, arg2 ...grpc.CallOption) (*esport_hall.GetSkillProductByUidGameIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillProductByUidGameId", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetSkillProductByUidGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductByUidGameId indicates an expected call of GetSkillProductByUidGameId.
func (mr *MockEsportHallClientMockRecorder) GetSkillProductByUidGameId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductByUidGameId", reflect.TypeOf((*MockEsportHallClient)(nil).GetSkillProductByUidGameId), varargs...)
}

// GetSkillProductInfo mocks base method.
func (m *MockEsportHallClient) GetSkillProductInfo(arg0 context.Context, arg1 *esport_hall.GetSkillProductInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.GetSkillProductInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillProductInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetSkillProductInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductInfo indicates an expected call of GetSkillProductInfo.
func (mr *MockEsportHallClientMockRecorder) GetSkillProductInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductInfo", reflect.TypeOf((*MockEsportHallClient)(nil).GetSkillProductInfo), varargs...)
}

// GetSkillProductInfoByGameId mocks base method.
func (m *MockEsportHallClient) GetSkillProductInfoByGameId(arg0 context.Context, arg1 *esport_hall.GetSkillProductInfoByGameIdRequest, arg2 ...grpc.CallOption) (*esport_hall.GetSkillProductInfoByGameIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSkillProductInfoByGameId", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetSkillProductInfoByGameIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSkillProductInfoByGameId indicates an expected call of GetSkillProductInfoByGameId.
func (mr *MockEsportHallClientMockRecorder) GetSkillProductInfoByGameId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSkillProductInfoByGameId", reflect.TypeOf((*MockEsportHallClient)(nil).GetSkillProductInfoByGameId), varargs...)
}

// GetUserTopGamePreSelectConfig mocks base method.
func (m *MockEsportHallClient) GetUserTopGamePreSelectConfig(arg0 context.Context, arg1 *esport_hall.GetUserTopGamePreSelectConfigRequest, arg2 ...grpc.CallOption) (*esport_hall.GetUserTopGamePreSelectConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserTopGamePreSelectConfig", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetUserTopGamePreSelectConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserTopGamePreSelectConfig indicates an expected call of GetUserTopGamePreSelectConfig.
func (mr *MockEsportHallClientMockRecorder) GetUserTopGamePreSelectConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserTopGamePreSelectConfig", reflect.TypeOf((*MockEsportHallClient)(nil).GetUserTopGamePreSelectConfig), varargs...)
}

// GetVisibleSkillProductList mocks base method.
func (m *MockEsportHallClient) GetVisibleSkillProductList(arg0 context.Context, arg1 *esport_hall.GetVisibleSkillProductListRequest, arg2 ...grpc.CallOption) (*esport_hall.GetVisibleSkillProductListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVisibleSkillProductList", varargs...)
	ret0, _ := ret[0].(*esport_hall.GetVisibleSkillProductListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVisibleSkillProductList indicates an expected call of GetVisibleSkillProductList.
func (mr *MockEsportHallClientMockRecorder) GetVisibleSkillProductList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVisibleSkillProductList", reflect.TypeOf((*MockEsportHallClient)(nil).GetVisibleSkillProductList), varargs...)
}

// HandleInviteOrder mocks base method.
func (m *MockEsportHallClient) HandleInviteOrder(arg0 context.Context, arg1 *esport_hall.HandleInviteOrderRequest, arg2 ...grpc.CallOption) (*esport_hall.HandleInviteOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HandleInviteOrder", varargs...)
	ret0, _ := ret[0].(*esport_hall.HandleInviteOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HandleInviteOrder indicates an expected call of HandleInviteOrder.
func (mr *MockEsportHallClientMockRecorder) HandleInviteOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HandleInviteOrder", reflect.TypeOf((*MockEsportHallClient)(nil).HandleInviteOrder), varargs...)
}

// HasFamousPlayer mocks base method.
func (m *MockEsportHallClient) HasFamousPlayer(arg0 context.Context, arg1 *esport_hall.HasFamousPlayerRequest, arg2 ...grpc.CallOption) (*esport_hall.HasFamousPlayerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HasFamousPlayer", varargs...)
	ret0, _ := ret[0].(*esport_hall.HasFamousPlayerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasFamousPlayer indicates an expected call of HasFamousPlayer.
func (mr *MockEsportHallClientMockRecorder) HasFamousPlayer(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasFamousPlayer", reflect.TypeOf((*MockEsportHallClient)(nil).HasFamousPlayer), varargs...)
}

// InitUserSkillInfo mocks base method.
func (m *MockEsportHallClient) InitUserSkillInfo(arg0 context.Context, arg1 *esport_hall.InitUserSkillInfoRequest, arg2 ...grpc.CallOption) (*esport_hall.InitUserSkillInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitUserSkillInfo", varargs...)
	ret0, _ := ret[0].(*esport_hall.InitUserSkillInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitUserSkillInfo indicates an expected call of InitUserSkillInfo.
func (mr *MockEsportHallClientMockRecorder) InitUserSkillInfo(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitUserSkillInfo", reflect.TypeOf((*MockEsportHallClient)(nil).InitUserSkillInfo), varargs...)
}

// InviteOrder mocks base method.
func (m *MockEsportHallClient) InviteOrder(arg0 context.Context, arg1 *esport_hall.InviteOrderRequest, arg2 ...grpc.CallOption) (*esport_hall.InviteOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InviteOrder", varargs...)
	ret0, _ := ret[0].(*esport_hall.InviteOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InviteOrder indicates an expected call of InviteOrder.
func (mr *MockEsportHallClientMockRecorder) InviteOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteOrder", reflect.TypeOf((*MockEsportHallClient)(nil).InviteOrder), varargs...)
}

// ReBuildUserFirstRoundCache mocks base method.
func (m *MockEsportHallClient) ReBuildUserFirstRoundCache(arg0 context.Context, arg1 *esport_hall.ReBuildUserFirstRoundCacheRequest, arg2 ...grpc.CallOption) (*esport_hall.ReBuildUserFirstRoundCacheResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReBuildUserFirstRoundCache", varargs...)
	ret0, _ := ret[0].(*esport_hall.ReBuildUserFirstRoundCacheResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReBuildUserFirstRoundCache indicates an expected call of ReBuildUserFirstRoundCache.
func (mr *MockEsportHallClientMockRecorder) ReBuildUserFirstRoundCache(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReBuildUserFirstRoundCache", reflect.TypeOf((*MockEsportHallClient)(nil).ReBuildUserFirstRoundCache), varargs...)
}

// ReportExposeCoach mocks base method.
func (m *MockEsportHallClient) ReportExposeCoach(arg0 context.Context, arg1 *esport_hall.ReportExposeCoachRequest, arg2 ...grpc.CallOption) (*esport_hall.ReportExposeCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReportExposeCoach", varargs...)
	ret0, _ := ret[0].(*esport_hall.ReportExposeCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReportExposeCoach indicates an expected call of ReportExposeCoach.
func (mr *MockEsportHallClientMockRecorder) ReportExposeCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReportExposeCoach", reflect.TypeOf((*MockEsportHallClient)(nil).ReportExposeCoach), varargs...)
}

// SaveUserTopGamePreSelectConfig mocks base method.
func (m *MockEsportHallClient) SaveUserTopGamePreSelectConfig(arg0 context.Context, arg1 *esport_hall.SaveUserTopGamePreSelectConfigRequest, arg2 ...grpc.CallOption) (*esport_hall.SaveUserTopGamePreSelectConfigResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SaveUserTopGamePreSelectConfig", varargs...)
	ret0, _ := ret[0].(*esport_hall.SaveUserTopGamePreSelectConfigResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SaveUserTopGamePreSelectConfig indicates an expected call of SaveUserTopGamePreSelectConfig.
func (mr *MockEsportHallClientMockRecorder) SaveUserTopGamePreSelectConfig(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveUserTopGamePreSelectConfig", reflect.TypeOf((*MockEsportHallClient)(nil).SaveUserTopGamePreSelectConfig), varargs...)
}

// SearchCoach mocks base method.
func (m *MockEsportHallClient) SearchCoach(arg0 context.Context, arg1 *esport_hall.SearchCoachRequest, arg2 ...grpc.CallOption) (*esport_hall.SearchCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SearchCoach", varargs...)
	ret0, _ := ret[0].(*esport_hall.SearchCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchCoach indicates an expected call of SearchCoach.
func (mr *MockEsportHallClientMockRecorder) SearchCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchCoach", reflect.TypeOf((*MockEsportHallClient)(nil).SearchCoach), varargs...)
}

// SendEsportGameCard mocks base method.
func (m *MockEsportHallClient) SendEsportGameCard(arg0 context.Context, arg1 *esport_hall.SendEsportGameCardRequest, arg2 ...grpc.CallOption) (*esport_hall.SendEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendEsportGameCard", varargs...)
	ret0, _ := ret[0].(*esport_hall.SendEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEsportGameCard indicates an expected call of SendEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) SendEsportGameCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).SendEsportGameCard), varargs...)
}

// SetFirstRoundSwitch mocks base method.
func (m *MockEsportHallClient) SetFirstRoundSwitch(arg0 context.Context, arg1 *esport_hall.SetFirstRoundSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.SetFirstRoundSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetFirstRoundSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetFirstRoundSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetFirstRoundSwitch indicates an expected call of SetFirstRoundSwitch.
func (mr *MockEsportHallClientMockRecorder) SetFirstRoundSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFirstRoundSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetFirstRoundSwitch), varargs...)
}

// SetGuaranteeWinSwitch mocks base method.
func (m *MockEsportHallClient) SetGuaranteeWinSwitch(arg0 context.Context, arg1 *esport_hall.SetGuaranteeWinSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.SetGuaranteeWinSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGuaranteeWinSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetGuaranteeWinSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGuaranteeWinSwitch indicates an expected call of SetGuaranteeWinSwitch.
func (mr *MockEsportHallClientMockRecorder) SetGuaranteeWinSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGuaranteeWinSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetGuaranteeWinSwitch), varargs...)
}

// SetNewCustomerSwitch mocks base method.
func (m *MockEsportHallClient) SetNewCustomerSwitch(arg0 context.Context, arg1 *esport_hall.SetNewCustomerSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.SetNewCustomerSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetNewCustomerSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetNewCustomerSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetNewCustomerSwitch indicates an expected call of SetNewCustomerSwitch.
func (mr *MockEsportHallClientMockRecorder) SetNewCustomerSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNewCustomerSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetNewCustomerSwitch), varargs...)
}

// SetQuickReceiveSwitch mocks base method.
func (m *MockEsportHallClient) SetQuickReceiveSwitch(arg0 context.Context, arg1 *esport_hall.SetQuickReceiveSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.SetQuickReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetQuickReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetQuickReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetQuickReceiveSwitch indicates an expected call of SetQuickReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) SetQuickReceiveSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQuickReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetQuickReceiveSwitch), varargs...)
}

// SetReceiveTimeFrame mocks base method.
func (m *MockEsportHallClient) SetReceiveTimeFrame(arg0 context.Context, arg1 *esport_hall.SetReceiveTimeFrameRequest, arg2 ...grpc.CallOption) (*esport_hall.SetReceiveTimeFrameResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetReceiveTimeFrame", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetReceiveTimeFrameResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetReceiveTimeFrame indicates an expected call of SetReceiveTimeFrame.
func (mr *MockEsportHallClientMockRecorder) SetReceiveTimeFrame(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetReceiveTimeFrame", reflect.TypeOf((*MockEsportHallClient)(nil).SetReceiveTimeFrame), varargs...)
}

// SetSkillPrice mocks base method.
func (m *MockEsportHallClient) SetSkillPrice(arg0 context.Context, arg1 *esport_hall.SetSkillPriceRequest, arg2 ...grpc.CallOption) (*esport_hall.SetSkillPriceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSkillPrice", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetSkillPriceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSkillPrice indicates an expected call of SetSkillPrice.
func (mr *MockEsportHallClientMockRecorder) SetSkillPrice(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSkillPrice", reflect.TypeOf((*MockEsportHallClient)(nil).SetSkillPrice), varargs...)
}

// SetSkillReceiveSwitch mocks base method.
func (m *MockEsportHallClient) SetSkillReceiveSwitch(arg0 context.Context, arg1 *esport_hall.SetSkillReceiveSwitchRequest, arg2 ...grpc.CallOption) (*esport_hall.SetSkillReceiveSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetSkillReceiveSwitch", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetSkillReceiveSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetSkillReceiveSwitch indicates an expected call of SetSkillReceiveSwitch.
func (mr *MockEsportHallClientMockRecorder) SetSkillReceiveSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSkillReceiveSwitch", reflect.TypeOf((*MockEsportHallClient)(nil).SetSkillReceiveSwitch), varargs...)
}

// SetUserToRealCoach mocks base method.
func (m *MockEsportHallClient) SetUserToRealCoach(arg0 context.Context, arg1 *esport_hall.SetUserToRealCoachRequest, arg2 ...grpc.CallOption) (*esport_hall.SetUserToRealCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetUserToRealCoach", varargs...)
	ret0, _ := ret[0].(*esport_hall.SetUserToRealCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetUserToRealCoach indicates an expected call of SetUserToRealCoach.
func (mr *MockEsportHallClientMockRecorder) SetUserToRealCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserToRealCoach", reflect.TypeOf((*MockEsportHallClient)(nil).SetUserToRealCoach), varargs...)
}

// Test mocks base method.
func (m *MockEsportHallClient) Test(arg0 context.Context, arg1 *esport_hall.TestRequest, arg2 ...grpc.CallOption) (*esport_hall.TestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Test", varargs...)
	ret0, _ := ret[0].(*esport_hall.TestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Test indicates an expected call of Test.
func (mr *MockEsportHallClientMockRecorder) Test(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Test", reflect.TypeOf((*MockEsportHallClient)(nil).Test), varargs...)
}

// UpdateCoachRecommend mocks base method.
func (m *MockEsportHallClient) UpdateCoachRecommend(arg0 context.Context, arg1 *esport_hall.UpdateCoachRecommendRequest, arg2 ...grpc.CallOption) (*esport_hall.UpdateCoachRecommendResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCoachRecommend", varargs...)
	ret0, _ := ret[0].(*esport_hall.UpdateCoachRecommendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCoachRecommend indicates an expected call of UpdateCoachRecommend.
func (mr *MockEsportHallClientMockRecorder) UpdateCoachRecommend(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCoachRecommend", reflect.TypeOf((*MockEsportHallClient)(nil).UpdateCoachRecommend), varargs...)
}

// UpdateEsportGameCard mocks base method.
func (m *MockEsportHallClient) UpdateEsportGameCard(arg0 context.Context, arg1 *esport_hall.UpdateEsportGameCardRequest, arg2 ...grpc.CallOption) (*esport_hall.UpdateEsportGameCardResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEsportGameCard", varargs...)
	ret0, _ := ret[0].(*esport_hall.UpdateEsportGameCardResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEsportGameCard indicates an expected call of UpdateEsportGameCard.
func (mr *MockEsportHallClientMockRecorder) UpdateEsportGameCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEsportGameCard", reflect.TypeOf((*MockEsportHallClient)(nil).UpdateEsportGameCard), varargs...)
}
