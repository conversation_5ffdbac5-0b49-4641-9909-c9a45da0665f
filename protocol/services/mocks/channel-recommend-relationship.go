// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/channel-recommend-relationship (interfaces: ChannelRecommendRelationshipServiceClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	channel_recommend_relationship "golang.52tt.com/protocol/services/channel-recommend-relationship"
	grpc "google.golang.org/grpc"
)

// MockChannelRecommendRelationshipServiceClient is a mock of ChannelRecommendRelationshipServiceClient interface.
type MockChannelRecommendRelationshipServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockChannelRecommendRelationshipServiceClientMockRecorder
}

// MockChannelRecommendRelationshipServiceClientMockRecorder is the mock recorder for MockChannelRecommendRelationshipServiceClient.
type MockChannelRecommendRelationshipServiceClientMockRecorder struct {
	mock *MockChannelRecommendRelationshipServiceClient
}

// NewMockChannelRecommendRelationshipServiceClient creates a new mock instance.
func NewMockChannelRecommendRelationshipServiceClient(ctrl *gomock.Controller) *MockChannelRecommendRelationshipServiceClient {
	mock := &MockChannelRecommendRelationshipServiceClient{ctrl: ctrl}
	mock.recorder = &MockChannelRecommendRelationshipServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockChannelRecommendRelationshipServiceClient) EXPECT() *MockChannelRecommendRelationshipServiceClientMockRecorder {
	return m.recorder
}

// BatchGetRecommendRelationshipByChannelId mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) BatchGetRecommendRelationshipByChannelId(arg0 context.Context, arg1 *channel_recommend_relationship.BatchGetRecommendRelationshipByChannelIdReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.BatchGetRecommendRelationshipByChannelIdRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetRecommendRelationshipByChannelId", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.BatchGetRecommendRelationshipByChannelIdRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetRecommendRelationshipByChannelId indicates an expected call of BatchGetRecommendRelationshipByChannelId.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) BatchGetRecommendRelationshipByChannelId(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetRecommendRelationshipByChannelId", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).BatchGetRecommendRelationshipByChannelId), varargs...)
}

// CreateRecommendPoolChannel mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) CreateRecommendPoolChannel(arg0 context.Context, arg1 *channel_recommend_relationship.CreateRecommendPoolChannelReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRecommendPoolChannel", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRecommendPoolChannel indicates an expected call of CreateRecommendPoolChannel.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) CreateRecommendPoolChannel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRecommendPoolChannel", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).CreateRecommendPoolChannel), varargs...)
}

// CreateRecommendPoolTagWeight mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) CreateRecommendPoolTagWeight(arg0 context.Context, arg1 *channel_recommend_relationship.CreateRecommendPoolTagWeightReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRecommendPoolTagWeight", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRecommendPoolTagWeight indicates an expected call of CreateRecommendPoolTagWeight.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) CreateRecommendPoolTagWeight(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRecommendPoolTagWeight", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).CreateRecommendPoolTagWeight), varargs...)
}

// DeleteRecommendPoolChannel mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) DeleteRecommendPoolChannel(arg0 context.Context, arg1 *channel_recommend_relationship.DeleteRecommendPoolChannelReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRecommendPoolChannel", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRecommendPoolChannel indicates an expected call of DeleteRecommendPoolChannel.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) DeleteRecommendPoolChannel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecommendPoolChannel", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).DeleteRecommendPoolChannel), varargs...)
}

// DeleteRecommendPoolTagWeight mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) DeleteRecommendPoolTagWeight(arg0 context.Context, arg1 *channel_recommend_relationship.DeleteRecommendPoolTagWeightReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.EmptyResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRecommendPoolTagWeight", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.EmptyResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRecommendPoolTagWeight indicates an expected call of DeleteRecommendPoolTagWeight.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) DeleteRecommendPoolTagWeight(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRecommendPoolTagWeight", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).DeleteRecommendPoolTagWeight), varargs...)
}

// GetChannelRecommendRelationship mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) GetChannelRecommendRelationship(arg0 context.Context, arg1 *channel_recommend_relationship.GetChannelRecommendRelationshipReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.GetChannelRecommendRelationshipRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetChannelRecommendRelationship", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.GetChannelRecommendRelationshipRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChannelRecommendRelationship indicates an expected call of GetChannelRecommendRelationship.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) GetChannelRecommendRelationship(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChannelRecommendRelationship", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).GetChannelRecommendRelationship), varargs...)
}

// GetRecommendPoolChannelList mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) GetRecommendPoolChannelList(arg0 context.Context, arg1 *channel_recommend_relationship.GetRecommendPoolChannelListReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.GetRecommendPoolChannelListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecommendPoolChannelList", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.GetRecommendPoolChannelListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendPoolChannelList indicates an expected call of GetRecommendPoolChannelList.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) GetRecommendPoolChannelList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendPoolChannelList", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).GetRecommendPoolChannelList), varargs...)
}

// GetRecommendPoolChannelTags mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) GetRecommendPoolChannelTags(arg0 context.Context, arg1 *channel_recommend_relationship.GetRecommendPoolChannelTagsReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.GetRecommendPoolChannelTagsResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecommendPoolChannelTags", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.GetRecommendPoolChannelTagsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendPoolChannelTags indicates an expected call of GetRecommendPoolChannelTags.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) GetRecommendPoolChannelTags(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendPoolChannelTags", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).GetRecommendPoolChannelTags), varargs...)
}

// GetRecommendPoolTagWeightList mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) GetRecommendPoolTagWeightList(arg0 context.Context, arg1 *channel_recommend_relationship.GetRecommendPoolTagWeightListReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.GetRecommendPoolTagWeightListResp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRecommendPoolTagWeightList", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.GetRecommendPoolTagWeightListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecommendPoolTagWeightList indicates an expected call of GetRecommendPoolTagWeightList.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) GetRecommendPoolTagWeightList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecommendPoolTagWeightList", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).GetRecommendPoolTagWeightList), varargs...)
}

// GetUGCHomePageRecommendChannel mocks base method.
func (m *MockChannelRecommendRelationshipServiceClient) GetUGCHomePageRecommendChannel(arg0 context.Context, arg1 *channel_recommend_relationship.GetUGCHomePageRecommendChannelReq, arg2 ...grpc.CallOption) (*channel_recommend_relationship.GetUGCHomePageRecommendChannelRsp, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUGCHomePageRecommendChannel", varargs...)
	ret0, _ := ret[0].(*channel_recommend_relationship.GetUGCHomePageRecommendChannelRsp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUGCHomePageRecommendChannel indicates an expected call of GetUGCHomePageRecommendChannel.
func (mr *MockChannelRecommendRelationshipServiceClientMockRecorder) GetUGCHomePageRecommendChannel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUGCHomePageRecommendChannel", reflect.TypeOf((*MockChannelRecommendRelationshipServiceClient)(nil).GetUGCHomePageRecommendChannel), varargs...)
}
