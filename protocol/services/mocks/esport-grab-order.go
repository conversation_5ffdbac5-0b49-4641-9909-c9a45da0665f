// Code generated by MockGen. DO NOT EDIT.
// Source: golang.52tt.com/protocol/services/esport_grab_order (interfaces: EsportGrabOrderClient)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	esport_grab_order "golang.52tt.com/protocol/services/esport_grab_order"
	grpc "google.golang.org/grpc"
)

// MockEsportGrabOrderClient is a mock of EsportGrabOrderClient interface.
type MockEsportGrabOrderClient struct {
	ctrl     *gomock.Controller
	recorder *MockEsportGrabOrderClientMockRecorder
}

// MockEsportGrabOrderClientMockRecorder is the mock recorder for MockEsportGrabOrderClient.
type MockEsportGrabOrderClientMockRecorder struct {
	mock *MockEsportGrabOrderClient
}

// NewMockEsportGrabOrderClient creates a new mock instance.
func NewMockEsportGrabOrderClient(ctrl *gomock.Controller) *MockEsportGrabOrderClient {
	mock := &MockEsportGrabOrderClient{ctrl: ctrl}
	mock.recorder = &MockEsportGrabOrderClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEsportGrabOrderClient) EXPECT() *MockEsportGrabOrderClientMockRecorder {
	return m.recorder
}

// CancelOneKeyFindCoach mocks base method.
func (m *MockEsportGrabOrderClient) CancelOneKeyFindCoach(arg0 context.Context, arg1 *esport_grab_order.CancelOneKeyFindCoachRequest, arg2 ...grpc.CallOption) (*esport_grab_order.CancelOneKeyFindCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CancelOneKeyFindCoach", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.CancelOneKeyFindCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CancelOneKeyFindCoach indicates an expected call of CancelOneKeyFindCoach.
func (mr *MockEsportGrabOrderClientMockRecorder) CancelOneKeyFindCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelOneKeyFindCoach", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).CancelOneKeyFindCoach), varargs...)
}

// CheckIfCouldPublish mocks base method.
func (m *MockEsportGrabOrderClient) CheckIfCouldPublish(arg0 context.Context, arg1 *esport_grab_order.CheckIfCouldPublishRequest, arg2 ...grpc.CallOption) (*esport_grab_order.CheckIfCouldPublishResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfCouldPublish", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.CheckIfCouldPublishResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfCouldPublish indicates an expected call of CheckIfCouldPublish.
func (mr *MockEsportGrabOrderClientMockRecorder) CheckIfCouldPublish(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfCouldPublish", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).CheckIfCouldPublish), varargs...)
}

// EsportRegionHeartbeat mocks base method.
func (m *MockEsportGrabOrderClient) EsportRegionHeartbeat(arg0 context.Context, arg1 *esport_grab_order.EsportRegionHeartbeatRequest, arg2 ...grpc.CallOption) (*esport_grab_order.EsportRegionHeartbeatResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EsportRegionHeartbeat", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.EsportRegionHeartbeatResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EsportRegionHeartbeat indicates an expected call of EsportRegionHeartbeat.
func (mr *MockEsportGrabOrderClientMockRecorder) EsportRegionHeartbeat(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EsportRegionHeartbeat", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).EsportRegionHeartbeat), varargs...)
}

// FinishChoseCoach mocks base method.
func (m *MockEsportGrabOrderClient) FinishChoseCoach(arg0 context.Context, arg1 *esport_grab_order.FinishChoseCoachRequest, arg2 ...grpc.CallOption) (*esport_grab_order.FinishChoseCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FinishChoseCoach", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.FinishChoseCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FinishChoseCoach indicates an expected call of FinishChoseCoach.
func (mr *MockEsportGrabOrderClientMockRecorder) FinishChoseCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FinishChoseCoach", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).FinishChoseCoach), varargs...)
}

// GetGoingOneKeyFindCoach mocks base method.
func (m *MockEsportGrabOrderClient) GetGoingOneKeyFindCoach(arg0 context.Context, arg1 *esport_grab_order.GetGoingOneKeyFindCoachRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GetGoingOneKeyFindCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGoingOneKeyFindCoach", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GetGoingOneKeyFindCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGoingOneKeyFindCoach indicates an expected call of GetGoingOneKeyFindCoach.
func (mr *MockEsportGrabOrderClientMockRecorder) GetGoingOneKeyFindCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGoingOneKeyFindCoach", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GetGoingOneKeyFindCoach), varargs...)
}

// GetGrabAudioAuditStatus mocks base method.
func (m *MockEsportGrabOrderClient) GetGrabAudioAuditStatus(arg0 context.Context, arg1 *esport_grab_order.GetGrabAudioAuditStatusRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GetGrabAudioAuditStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGrabAudioAuditStatus", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GetGrabAudioAuditStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGrabAudioAuditStatus indicates an expected call of GetGrabAudioAuditStatus.
func (mr *MockEsportGrabOrderClientMockRecorder) GetGrabAudioAuditStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGrabAudioAuditStatus", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GetGrabAudioAuditStatus), varargs...)
}

// GetGrabCenterOverview mocks base method.
func (m *MockEsportGrabOrderClient) GetGrabCenterOverview(arg0 context.Context, arg1 *esport_grab_order.GetGrabCenterOverviewRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GetGrabCenterOverviewResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGrabCenterOverview", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GetGrabCenterOverviewResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGrabCenterOverview indicates an expected call of GetGrabCenterOverview.
func (mr *MockEsportGrabOrderClientMockRecorder) GetGrabCenterOverview(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGrabCenterOverview", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GetGrabCenterOverview), varargs...)
}

// GetGrabbedOrderList mocks base method.
func (m *MockEsportGrabOrderClient) GetGrabbedOrderList(arg0 context.Context, arg1 *esport_grab_order.GetGrabbedOrderListRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GetGrabbedOrderListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetGrabbedOrderList", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GetGrabbedOrderListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGrabbedOrderList indicates an expected call of GetGrabbedOrderList.
func (mr *MockEsportGrabOrderClientMockRecorder) GetGrabbedOrderList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGrabbedOrderList", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GetGrabbedOrderList), varargs...)
}

// GetPendingGrabOrderList mocks base method.
func (m *MockEsportGrabOrderClient) GetPendingGrabOrderList(arg0 context.Context, arg1 *esport_grab_order.GetPendingGrabOrderListRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GetPendingGrabOrderListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPendingGrabOrderList", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GetPendingGrabOrderListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPendingGrabOrderList indicates an expected call of GetPendingGrabOrderList.
func (mr *MockEsportGrabOrderClientMockRecorder) GetPendingGrabOrderList(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPendingGrabOrderList", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GetPendingGrabOrderList), varargs...)
}

// GetSwitch mocks base method.
func (m *MockEsportGrabOrderClient) GetSwitch(arg0 context.Context, arg1 *esport_grab_order.GetSwitchRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GetSwitchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSwitch", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GetSwitchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSwitch indicates an expected call of GetSwitch.
func (mr *MockEsportGrabOrderClientMockRecorder) GetSwitch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitch", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GetSwitch), varargs...)
}

// GrabOrder mocks base method.
func (m *MockEsportGrabOrderClient) GrabOrder(arg0 context.Context, arg1 *esport_grab_order.GrabOrderRequest, arg2 ...grpc.CallOption) (*esport_grab_order.GrabOrderResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GrabOrder", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.GrabOrderResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GrabOrder indicates an expected call of GrabOrder.
func (mr *MockEsportGrabOrderClientMockRecorder) GrabOrder(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GrabOrder", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).GrabOrder), varargs...)
}

// PublishOneKeyFindCoach mocks base method.
func (m *MockEsportGrabOrderClient) PublishOneKeyFindCoach(arg0 context.Context, arg1 *esport_grab_order.PublishOneKeyFindCoachRequest, arg2 ...grpc.CallOption) (*esport_grab_order.PublishOneKeyFindCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PublishOneKeyFindCoach", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.PublishOneKeyFindCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PublishOneKeyFindCoach indicates an expected call of PublishOneKeyFindCoach.
func (mr *MockEsportGrabOrderClientMockRecorder) PublishOneKeyFindCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishOneKeyFindCoach", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).PublishOneKeyFindCoach), varargs...)
}

// SetGrabAudioAuditStatus mocks base method.
func (m *MockEsportGrabOrderClient) SetGrabAudioAuditStatus(arg0 context.Context, arg1 *esport_grab_order.SetGrabAudioAuditStatusRequest, arg2 ...grpc.CallOption) (*esport_grab_order.SetGrabAudioAuditStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetGrabAudioAuditStatus", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.SetGrabAudioAuditStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetGrabAudioAuditStatus indicates an expected call of SetGrabAudioAuditStatus.
func (mr *MockEsportGrabOrderClientMockRecorder) SetGrabAudioAuditStatus(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGrabAudioAuditStatus", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).SetGrabAudioAuditStatus), varargs...)
}

// StickOneKeyFindCoach mocks base method.
func (m *MockEsportGrabOrderClient) StickOneKeyFindCoach(arg0 context.Context, arg1 *esport_grab_order.StickOneKeyFindCoachRequest, arg2 ...grpc.CallOption) (*esport_grab_order.StickOneKeyFindCoachResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StickOneKeyFindCoach", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.StickOneKeyFindCoachResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StickOneKeyFindCoach indicates an expected call of StickOneKeyFindCoach.
func (mr *MockEsportGrabOrderClientMockRecorder) StickOneKeyFindCoach(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StickOneKeyFindCoach", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).StickOneKeyFindCoach), varargs...)
}

// TestPushMsg mocks base method.
func (m *MockEsportGrabOrderClient) TestPushMsg(arg0 context.Context, arg1 *esport_grab_order.TestPushMsgRequest, arg2 ...grpc.CallOption) (*esport_grab_order.TestPushMsgResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TestPushMsg", varargs...)
	ret0, _ := ret[0].(*esport_grab_order.TestPushMsgResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TestPushMsg indicates an expected call of TestPushMsg.
func (mr *MockEsportGrabOrderClientMockRecorder) TestPushMsg(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TestPushMsg", reflect.TypeOf((*MockEsportGrabOrderClient)(nil).TestPushMsg), varargs...)
}
