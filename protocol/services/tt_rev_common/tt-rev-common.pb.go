// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tt/quicksilver/tt-rev-common/tt-rev-common.proto

package tt_rev_common // import "golang.52tt.com/protocol/services/tt_rev_common"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

// 消费场景枚举
type ConsumeSceneType int32

const (
	ConsumeSceneType_CONSUME_SCENE_TYPE_NONE                       ConsumeSceneType = 0
	ConsumeSceneType_CONSUME_SCENE_TYPE_FELLOW_HOUSE               ConsumeSceneType = 1
	ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_GIFT_PK            ConsumeSceneType = 2
	ConsumeSceneType_CONSUME_SCENE_TYPE_ESPORT                     ConsumeSceneType = 3
	ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_RED_PACKET         ConsumeSceneType = 4
	ConsumeSceneType_CONSUME_SCENE_TYPE_JOIN_KNIGHT                ConsumeSceneType = 5
	ConsumeSceneType_CONSUME_SCENE_TYPE_UKW                        ConsumeSceneType = 6
	ConsumeSceneType_CONSUME_SCENE_TYPE_COMMON_GIFT                ConsumeSceneType = 7
	ConsumeSceneType_CONSUME_SCENE_TYPE_BACKPACK_GIFT              ConsumeSceneType = 8
	ConsumeSceneType_CONSUME_SCENE_TYPE_MAGIC_SPIRIT               ConsumeSceneType = 9
	ConsumeSceneType_CONSUME_SCENE_TYPE_SUPER_PLAYER               ConsumeSceneType = 10
	ConsumeSceneType_CONSUME_SCENE_TYPE_EXCHANGE_TBEAN_POP         ConsumeSceneType = 11
	ConsumeSceneType_CONSUME_SCENE_TYPE_VIRTUAL_IMAGE              ConsumeSceneType = 12
	ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_EXT_GAME_ZDXX      ConsumeSceneType = 13
	ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_WEDDING_CHIAR_GAME ConsumeSceneType = 14
	ConsumeSceneType_CONSUME_SCENE_TYPE_CHANNEL_WEDDING_BUY        ConsumeSceneType = 15
	ConsumeSceneType_CONSUME_SCENE_TYPE_THIRDPART_GAME_ZDXX        ConsumeSceneType = 16
)

var ConsumeSceneType_name = map[int32]string{
	0:  "CONSUME_SCENE_TYPE_NONE",
	1:  "CONSUME_SCENE_TYPE_FELLOW_HOUSE",
	2:  "CONSUME_SCENE_TYPE_CHANNEL_GIFT_PK",
	3:  "CONSUME_SCENE_TYPE_ESPORT",
	4:  "CONSUME_SCENE_TYPE_CHANNEL_RED_PACKET",
	5:  "CONSUME_SCENE_TYPE_JOIN_KNIGHT",
	6:  "CONSUME_SCENE_TYPE_UKW",
	7:  "CONSUME_SCENE_TYPE_COMMON_GIFT",
	8:  "CONSUME_SCENE_TYPE_BACKPACK_GIFT",
	9:  "CONSUME_SCENE_TYPE_MAGIC_SPIRIT",
	10: "CONSUME_SCENE_TYPE_SUPER_PLAYER",
	11: "CONSUME_SCENE_TYPE_EXCHANGE_TBEAN_POP",
	12: "CONSUME_SCENE_TYPE_VIRTUAL_IMAGE",
	13: "CONSUME_SCENE_TYPE_CHANNEL_EXT_GAME_ZDXX",
	14: "CONSUME_SCENE_TYPE_CHANNEL_WEDDING_CHIAR_GAME",
	15: "CONSUME_SCENE_TYPE_CHANNEL_WEDDING_BUY",
	16: "CONSUME_SCENE_TYPE_THIRDPART_GAME_ZDXX",
}
var ConsumeSceneType_value = map[string]int32{
	"CONSUME_SCENE_TYPE_NONE":                       0,
	"CONSUME_SCENE_TYPE_FELLOW_HOUSE":               1,
	"CONSUME_SCENE_TYPE_CHANNEL_GIFT_PK":            2,
	"CONSUME_SCENE_TYPE_ESPORT":                     3,
	"CONSUME_SCENE_TYPE_CHANNEL_RED_PACKET":         4,
	"CONSUME_SCENE_TYPE_JOIN_KNIGHT":                5,
	"CONSUME_SCENE_TYPE_UKW":                        6,
	"CONSUME_SCENE_TYPE_COMMON_GIFT":                7,
	"CONSUME_SCENE_TYPE_BACKPACK_GIFT":              8,
	"CONSUME_SCENE_TYPE_MAGIC_SPIRIT":               9,
	"CONSUME_SCENE_TYPE_SUPER_PLAYER":               10,
	"CONSUME_SCENE_TYPE_EXCHANGE_TBEAN_POP":         11,
	"CONSUME_SCENE_TYPE_VIRTUAL_IMAGE":              12,
	"CONSUME_SCENE_TYPE_CHANNEL_EXT_GAME_ZDXX":      13,
	"CONSUME_SCENE_TYPE_CHANNEL_WEDDING_CHIAR_GAME": 14,
	"CONSUME_SCENE_TYPE_CHANNEL_WEDDING_BUY":        15,
	"CONSUME_SCENE_TYPE_THIRDPART_GAME_ZDXX":        16,
}

func (x ConsumeSceneType) String() string {
	return proto.EnumName(ConsumeSceneType_name, int32(x))
}
func (ConsumeSceneType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_tt_rev_common_93130b4d2adae752, []int{0}
}

func init() {
	proto.RegisterEnum("tt_rev_common.ConsumeSceneType", ConsumeSceneType_name, ConsumeSceneType_value)
}

func init() {
	proto.RegisterFile("tt/quicksilver/tt-rev-common/tt-rev-common.proto", fileDescriptor_tt_rev_common_93130b4d2adae752)
}

var fileDescriptor_tt_rev_common_93130b4d2adae752 = []byte{
	// 408 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0xd2, 0xdd, 0x6e, 0xd3, 0x30,
	0x14, 0x07, 0x70, 0xbe, 0x36, 0xc0, 0x30, 0xb0, 0x7c, 0x01, 0x02, 0xc4, 0x87, 0x06, 0x4c, 0x30,
	0xd1, 0x86, 0x81, 0x78, 0x00, 0xd7, 0x39, 0x4b, 0x4c, 0x12, 0xdb, 0x72, 0x1c, 0xda, 0xee, 0xe6,
	0x08, 0x22, 0x6b, 0xaa, 0x68, 0x9b, 0x91, 0x7a, 0x95, 0x78, 0x1f, 0x1e, 0x14, 0xad, 0xbb, 0x61,
	0x52, 0x52, 0x71, 0x99, 0xf8, 0x77, 0xac, 0xbf, 0xff, 0x3a, 0xe4, 0x63, 0x08, 0xd1, 0xaf, 0xf3,
	0x59, 0xfd, 0x73, 0x35, 0x9b, 0xaf, 0x7d, 0x1b, 0x85, 0x30, 0x68, 0xfd, 0x7a, 0x50, 0x37, 0x8b,
	0x45, 0xb3, 0xbc, 0xfa, 0x35, 0x3c, 0x6b, 0x9b, 0xd0, 0xb0, 0xbd, 0x10, 0xb0, 0xf5, 0x6b, 0xbc,
	0xfc, 0x79, 0xf8, 0x67, 0x87, 0x50, 0xd1, 0x2c, 0x57, 0xe7, 0x0b, 0x5f, 0xd6, 0x7e, 0xe9, 0xdd,
	0xef, 0x33, 0xcf, 0x9e, 0x91, 0xc7, 0x42, 0xab, 0xb2, 0x2a, 0x00, 0x4b, 0x01, 0x0a, 0xd0, 0x4d,
	0x0d, 0xa0, 0xd2, 0x0a, 0xe8, 0x35, 0xf6, 0x9a, 0xbc, 0xec, 0x38, 0x3c, 0x86, 0x3c, 0xd7, 0x63,
	0x4c, 0x75, 0x55, 0x02, 0xbd, 0xce, 0x0e, 0xc8, 0x7e, 0x07, 0x12, 0x29, 0x57, 0x0a, 0x72, 0x4c,
	0xe4, 0xb1, 0x43, 0x93, 0xd1, 0x1b, 0xec, 0x39, 0x79, 0xd2, 0xe1, 0xa0, 0x34, 0xda, 0x3a, 0x7a,
	0x93, 0xbd, 0x27, 0x6f, 0xb7, 0x5c, 0x63, 0x21, 0x46, 0xc3, 0x45, 0x06, 0x8e, 0xde, 0x62, 0xfb,
	0xe4, 0x45, 0x07, 0xfd, 0xaa, 0xa5, 0xc2, 0x4c, 0xc9, 0x24, 0x75, 0x74, 0x87, 0x3d, 0x25, 0x8f,
	0x3a, 0x4c, 0x95, 0x8d, 0xe9, 0x6e, 0xcf, 0xbc, 0xd0, 0x45, 0xa1, 0xd5, 0x26, 0x30, 0xbd, 0xcd,
	0xde, 0x90, 0x57, 0x1d, 0x66, 0xc4, 0x45, 0x76, 0x11, 0xe3, 0x52, 0xdd, 0xe9, 0x29, 0xa8, 0xe0,
	0x89, 0x14, 0x58, 0x1a, 0x69, 0xa5, 0xa3, 0x77, 0x7b, 0x50, 0x59, 0x19, 0xb0, 0x68, 0x72, 0x3e,
	0x05, 0x4b, 0x49, 0xcf, 0xf3, 0x61, 0x72, 0x51, 0x40, 0x02, 0xe8, 0x46, 0xc0, 0x15, 0x1a, 0x6d,
	0xe8, 0xbd, 0x9e, 0x68, 0xdf, 0xa4, 0x75, 0x15, 0xcf, 0x51, 0x16, 0x3c, 0x01, 0x7a, 0x9f, 0x7d,
	0x20, 0xef, 0xb6, 0xf4, 0x09, 0x13, 0x87, 0x09, 0x2f, 0x00, 0x4f, 0xe2, 0xc9, 0x84, 0xee, 0xb1,
	0x23, 0x32, 0xd8, 0xa2, 0xc7, 0x10, 0xc7, 0x52, 0x25, 0x28, 0x52, 0xc9, 0xed, 0x66, 0x8e, 0x3e,
	0x60, 0x87, 0xe4, 0xe0, 0x3f, 0x46, 0x46, 0xd5, 0x94, 0x3e, 0xec, 0xb1, 0x2e, 0x95, 0x36, 0x36,
	0xdc, 0xfe, 0x1b, 0x85, 0x8e, 0x8e, 0x4e, 0xa2, 0xd3, 0x66, 0xfe, 0x7d, 0x79, 0x3a, 0xfc, 0xf2,
	0x29, 0x84, 0x61, 0xdd, 0x2c, 0xa2, 0xcd, 0x3a, 0xd7, 0xcd, 0x3c, 0x5a, 0xf9, 0x76, 0x3d, 0xab,
	0xfd, 0x2a, 0xba, 0xb2, 0xd9, 0x3f, 0x76, 0x37, 0xe0, 0xf3, 0xdf, 0x00, 0x00, 0x00, 0xff, 0xff,
	0xad, 0x5e, 0xa8, 0xdd, 0x23, 0x03, 0x00, 0x00,
}
