// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel-play-tab/channel-play-tab.proto

package channel_play_tab // import "golang.52tt.com/protocol/services/channel-play-tab"

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

import (
	context "golang.org/x/net/context"
	grpc "google.golang.org/grpc"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion2 // please upgrade the proto package

type TabSubType int32

const (
	TabSubType_GAME                  TabSubType = 0
	TabSubType_COMPREHENSIVE_CHANNEL TabSubType = 1
)

var TabSubType_name = map[int32]string{
	0: "GAME",
	1: "COMPREHENSIVE_CHANNEL",
}
var TabSubType_value = map[string]int32{
	"GAME":                  0,
	"COMPREHENSIVE_CHANNEL": 1,
}

func (x TabSubType) String() string {
	return proto.EnumName(TabSubType_name, int32(x))
}
func (TabSubType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{0}
}

type MinorSupervisionScene int32

const (
	MinorSupervisionScene_None      MinorSupervisionScene = 0
	MinorSupervisionScene_EnterRoom MinorSupervisionScene = 1
	MinorSupervisionScene_JoinGame  MinorSupervisionScene = 2
)

var MinorSupervisionScene_name = map[int32]string{
	0: "None",
	1: "EnterRoom",
	2: "JoinGame",
}
var MinorSupervisionScene_value = map[string]int32{
	"None":      0,
	"EnterRoom": 1,
	"JoinGame":  2,
}

func (x MinorSupervisionScene) String() string {
	return proto.EnumName(MinorSupervisionScene_name, int32(x))
}
func (MinorSupervisionScene) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{1}
}

type MinorSupervisionConfigType int32

const (
	MinorSupervisionConfigType_ConfigTypeNone     MinorSupervisionConfigType = 0
	MinorSupervisionConfigType_ConfigTypeTab      MinorSupervisionConfigType = 1
	MinorSupervisionConfigType_ConfigTypeCategory MinorSupervisionConfigType = 2
)

var MinorSupervisionConfigType_name = map[int32]string{
	0: "ConfigTypeNone",
	1: "ConfigTypeTab",
	2: "ConfigTypeCategory",
}
var MinorSupervisionConfigType_value = map[string]int32{
	"ConfigTypeNone":     0,
	"ConfigTypeTab":      1,
	"ConfigTypeCategory": 2,
}

func (x MinorSupervisionConfigType) String() string {
	return proto.EnumName(MinorSupervisionConfigType_name, int32(x))
}
func (MinorSupervisionConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{2}
}

// 快速匹配相关的
type QuickMatchConfigType int32

const (
	QuickMatchConfigType_Invalid                      QuickMatchConfigType = 0
	QuickMatchConfigType_NormalQuickMatchList         QuickMatchConfigType = 1
	QuickMatchConfigType_MoreCardTabList              QuickMatchConfigType = 2
	QuickMatchConfigType_NewQuickMatch                QuickMatchConfigType = 3
	QuickMatchConfigType_MiniZoneExposeQuickMatchList QuickMatchConfigType = 4
	QuickMatchConfigType_MiniZoneMoreTabList          QuickMatchConfigType = 5
	QuickMatchConfigType_MiniZoneHotAreaGameList      QuickMatchConfigType = 6
)

var QuickMatchConfigType_name = map[int32]string{
	0: "Invalid",
	1: "NormalQuickMatchList",
	2: "MoreCardTabList",
	3: "NewQuickMatch",
	4: "MiniZoneExposeQuickMatchList",
	5: "MiniZoneMoreTabList",
	6: "MiniZoneHotAreaGameList",
}
var QuickMatchConfigType_value = map[string]int32{
	"Invalid":                      0,
	"NormalQuickMatchList":         1,
	"MoreCardTabList":              2,
	"NewQuickMatch":                3,
	"MiniZoneExposeQuickMatchList": 4,
	"MiniZoneMoreTabList":          5,
	"MiniZoneHotAreaGameList":      6,
}

func (x QuickMatchConfigType) String() string {
	return proto.EnumName(QuickMatchConfigType_name, int32(x))
}
func (QuickMatchConfigType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{3}
}

// 批量获取玩法可见用户白名单
type BatchGetWhiteUidListByTabIdsReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	Source               string   `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	NoCache              bool     `protobuf:"varint,3,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWhiteUidListByTabIdsReq) Reset()         { *m = BatchGetWhiteUidListByTabIdsReq{} }
func (m *BatchGetWhiteUidListByTabIdsReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetWhiteUidListByTabIdsReq) ProtoMessage()    {}
func (*BatchGetWhiteUidListByTabIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{0}
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Unmarshal(m, b)
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetWhiteUidListByTabIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Merge(dst, src)
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.Size(m)
}
func (m *BatchGetWhiteUidListByTabIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWhiteUidListByTabIdsReq proto.InternalMessageInfo

func (m *BatchGetWhiteUidListByTabIdsReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BatchGetWhiteUidListByTabIdsReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *BatchGetWhiteUidListByTabIdsReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

type BatchGetWhiteUidListByTabIdsResp struct {
	// map中没有对应玩法key是没有配置白名单,只返回有白名单列表不为空的玩法白名单列表
	TabListMap           map[uint32]*BatchGetWhiteUidListByTabIdsResp_WhiteUidList `protobuf:"bytes,1,rep,name=tab_list_map,json=tabListMap,proto3" json:"tab_list_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                                                  `json:"-"`
	XXX_unrecognized     []byte                                                    `json:"-"`
	XXX_sizecache        int32                                                     `json:"-"`
}

func (m *BatchGetWhiteUidListByTabIdsResp) Reset()         { *m = BatchGetWhiteUidListByTabIdsResp{} }
func (m *BatchGetWhiteUidListByTabIdsResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetWhiteUidListByTabIdsResp) ProtoMessage()    {}
func (*BatchGetWhiteUidListByTabIdsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{1}
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Unmarshal(m, b)
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetWhiteUidListByTabIdsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Merge(dst, src)
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.Size(m)
}
func (m *BatchGetWhiteUidListByTabIdsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp proto.InternalMessageInfo

func (m *BatchGetWhiteUidListByTabIdsResp) GetTabListMap() map[uint32]*BatchGetWhiteUidListByTabIdsResp_WhiteUidList {
	if m != nil {
		return m.TabListMap
	}
	return nil
}

type BatchGetWhiteUidListByTabIdsResp_WhiteUidList struct {
	List                 []uint32 `protobuf:"varint,1,rep,packed,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) Reset() {
	*m = BatchGetWhiteUidListByTabIdsResp_WhiteUidList{}
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) String() string {
	return proto.CompactTextString(m)
}
func (*BatchGetWhiteUidListByTabIdsResp_WhiteUidList) ProtoMessage() {}
func (*BatchGetWhiteUidListByTabIdsResp_WhiteUidList) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{1, 0}
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Unmarshal(m, b)
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Marshal(b, m, deterministic)
}
func (dst *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Merge(dst, src)
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_Size() int {
	return xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.Size(m)
}
func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetWhiteUidListByTabIdsResp_WhiteUidList proto.InternalMessageInfo

func (m *BatchGetWhiteUidListByTabIdsResp_WhiteUidList) GetList() []uint32 {
	if m != nil {
		return m.List
	}
	return nil
}

// 保存玩法白名单,仅运营后台用
type SetWhiteUidListByTabIdReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	UidList              []uint32 `protobuf:"varint,2,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list,omitempty"`
	Source               string   `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWhiteUidListByTabIdReq) Reset()         { *m = SetWhiteUidListByTabIdReq{} }
func (m *SetWhiteUidListByTabIdReq) String() string { return proto.CompactTextString(m) }
func (*SetWhiteUidListByTabIdReq) ProtoMessage()    {}
func (*SetWhiteUidListByTabIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{2}
}
func (m *SetWhiteUidListByTabIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWhiteUidListByTabIdReq.Unmarshal(m, b)
}
func (m *SetWhiteUidListByTabIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWhiteUidListByTabIdReq.Marshal(b, m, deterministic)
}
func (dst *SetWhiteUidListByTabIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWhiteUidListByTabIdReq.Merge(dst, src)
}
func (m *SetWhiteUidListByTabIdReq) XXX_Size() int {
	return xxx_messageInfo_SetWhiteUidListByTabIdReq.Size(m)
}
func (m *SetWhiteUidListByTabIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWhiteUidListByTabIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetWhiteUidListByTabIdReq proto.InternalMessageInfo

func (m *SetWhiteUidListByTabIdReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SetWhiteUidListByTabIdReq) GetUidList() []uint32 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *SetWhiteUidListByTabIdReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

type SetWhiteUidListByTabIdResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetWhiteUidListByTabIdResp) Reset()         { *m = SetWhiteUidListByTabIdResp{} }
func (m *SetWhiteUidListByTabIdResp) String() string { return proto.CompactTextString(m) }
func (*SetWhiteUidListByTabIdResp) ProtoMessage()    {}
func (*SetWhiteUidListByTabIdResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{3}
}
func (m *SetWhiteUidListByTabIdResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetWhiteUidListByTabIdResp.Unmarshal(m, b)
}
func (m *SetWhiteUidListByTabIdResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetWhiteUidListByTabIdResp.Marshal(b, m, deterministic)
}
func (dst *SetWhiteUidListByTabIdResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetWhiteUidListByTabIdResp.Merge(dst, src)
}
func (m *SetWhiteUidListByTabIdResp) XXX_Size() int {
	return xxx_messageInfo_SetWhiteUidListByTabIdResp.Size(m)
}
func (m *SetWhiteUidListByTabIdResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetWhiteUidListByTabIdResp.DiscardUnknown(m)
}

var xxx_messageInfo_SetWhiteUidListByTabIdResp proto.InternalMessageInfo

type Tab struct {
	TabId                uint32     `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string     `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	ImageUrl             string     `protobuf:"bytes,3,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	TabSubType           TabSubType `protobuf:"varint,4,opt,name=tab_sub_type,json=tabSubType,proto3,enum=channel_play_tab.TabSubType" json:"tab_sub_type,omitempty"`
	IsTabVisible         bool       `protobuf:"varint,5,opt,name=is_tab_visible,json=isTabVisible,proto3" json:"is_tab_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *Tab) Reset()         { *m = Tab{} }
func (m *Tab) String() string { return proto.CompactTextString(m) }
func (*Tab) ProtoMessage()    {}
func (*Tab) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{4}
}
func (m *Tab) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Tab.Unmarshal(m, b)
}
func (m *Tab) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Tab.Marshal(b, m, deterministic)
}
func (dst *Tab) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Tab.Merge(dst, src)
}
func (m *Tab) XXX_Size() int {
	return xxx_messageInfo_Tab.Size(m)
}
func (m *Tab) XXX_DiscardUnknown() {
	xxx_messageInfo_Tab.DiscardUnknown(m)
}

var xxx_messageInfo_Tab proto.InternalMessageInfo

func (m *Tab) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *Tab) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *Tab) GetImageUrl() string {
	if m != nil {
		return m.ImageUrl
	}
	return ""
}

func (m *Tab) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

func (m *Tab) GetIsTabVisible() bool {
	if m != nil {
		return m.IsTabVisible
	}
	return false
}

type UpsertTabReq struct {
	Tab                  *Tab     `protobuf:"bytes,1,opt,name=tab,proto3" json:"tab,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTabReq) Reset()         { *m = UpsertTabReq{} }
func (m *UpsertTabReq) String() string { return proto.CompactTextString(m) }
func (*UpsertTabReq) ProtoMessage()    {}
func (*UpsertTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{5}
}
func (m *UpsertTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTabReq.Unmarshal(m, b)
}
func (m *UpsertTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTabReq.Marshal(b, m, deterministic)
}
func (dst *UpsertTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTabReq.Merge(dst, src)
}
func (m *UpsertTabReq) XXX_Size() int {
	return xxx_messageInfo_UpsertTabReq.Size(m)
}
func (m *UpsertTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTabReq proto.InternalMessageInfo

func (m *UpsertTabReq) GetTab() *Tab {
	if m != nil {
		return m.Tab
	}
	return nil
}

type UpsertTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertTabResp) Reset()         { *m = UpsertTabResp{} }
func (m *UpsertTabResp) String() string { return proto.CompactTextString(m) }
func (*UpsertTabResp) ProtoMessage()    {}
func (*UpsertTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{6}
}
func (m *UpsertTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertTabResp.Unmarshal(m, b)
}
func (m *UpsertTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertTabResp.Marshal(b, m, deterministic)
}
func (dst *UpsertTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertTabResp.Merge(dst, src)
}
func (m *UpsertTabResp) XXX_Size() int {
	return xxx_messageInfo_UpsertTabResp.Size(m)
}
func (m *UpsertTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertTabResp proto.InternalMessageInfo

type GetTabsByTabSubTypeReq struct {
	TabSubType           TabSubType `protobuf:"varint,1,opt,name=tab_sub_type,json=tabSubType,proto3,enum=channel_play_tab.TabSubType" json:"tab_sub_type,omitempty"`
	Source               string     `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	NoCache              bool       `protobuf:"varint,3,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *GetTabsByTabSubTypeReq) Reset()         { *m = GetTabsByTabSubTypeReq{} }
func (m *GetTabsByTabSubTypeReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsByTabSubTypeReq) ProtoMessage()    {}
func (*GetTabsByTabSubTypeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{7}
}
func (m *GetTabsByTabSubTypeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsByTabSubTypeReq.Unmarshal(m, b)
}
func (m *GetTabsByTabSubTypeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsByTabSubTypeReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsByTabSubTypeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsByTabSubTypeReq.Merge(dst, src)
}
func (m *GetTabsByTabSubTypeReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsByTabSubTypeReq.Size(m)
}
func (m *GetTabsByTabSubTypeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsByTabSubTypeReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsByTabSubTypeReq proto.InternalMessageInfo

func (m *GetTabsByTabSubTypeReq) GetTabSubType() TabSubType {
	if m != nil {
		return m.TabSubType
	}
	return TabSubType_GAME
}

func (m *GetTabsByTabSubTypeReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *GetTabsByTabSubTypeReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

type GetTabsByTabSubTypeResp struct {
	Tabs                 []*Tab   `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsByTabSubTypeResp) Reset()         { *m = GetTabsByTabSubTypeResp{} }
func (m *GetTabsByTabSubTypeResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsByTabSubTypeResp) ProtoMessage()    {}
func (*GetTabsByTabSubTypeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{8}
}
func (m *GetTabsByTabSubTypeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsByTabSubTypeResp.Unmarshal(m, b)
}
func (m *GetTabsByTabSubTypeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsByTabSubTypeResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsByTabSubTypeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsByTabSubTypeResp.Merge(dst, src)
}
func (m *GetTabsByTabSubTypeResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsByTabSubTypeResp.Size(m)
}
func (m *GetTabsByTabSubTypeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsByTabSubTypeResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsByTabSubTypeResp proto.InternalMessageInfo

func (m *GetTabsByTabSubTypeResp) GetTabs() []*Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type DeleteTabReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabReq) Reset()         { *m = DeleteTabReq{} }
func (m *DeleteTabReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTabReq) ProtoMessage()    {}
func (*DeleteTabReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{9}
}
func (m *DeleteTabReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabReq.Unmarshal(m, b)
}
func (m *DeleteTabReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabReq.Marshal(b, m, deterministic)
}
func (dst *DeleteTabReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabReq.Merge(dst, src)
}
func (m *DeleteTabReq) XXX_Size() int {
	return xxx_messageInfo_DeleteTabReq.Size(m)
}
func (m *DeleteTabReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabReq proto.InternalMessageInfo

func (m *DeleteTabReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type DeleteTabResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabResp) Reset()         { *m = DeleteTabResp{} }
func (m *DeleteTabResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTabResp) ProtoMessage()    {}
func (*DeleteTabResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{10}
}
func (m *DeleteTabResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabResp.Unmarshal(m, b)
}
func (m *DeleteTabResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabResp.Marshal(b, m, deterministic)
}
func (dst *DeleteTabResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabResp.Merge(dst, src)
}
func (m *DeleteTabResp) XXX_Size() int {
	return xxx_messageInfo_DeleteTabResp.Size(m)
}
func (m *DeleteTabResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabResp proto.InternalMessageInfo

type GetTabsReq struct {
	NoCache              bool     `protobuf:"varint,1,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	OnlyVisible          bool     `protobuf:"varint,2,opt,name=only_visible,json=onlyVisible,proto3" json:"only_visible,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsReq) Reset()         { *m = GetTabsReq{} }
func (m *GetTabsReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsReq) ProtoMessage()    {}
func (*GetTabsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{11}
}
func (m *GetTabsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsReq.Unmarshal(m, b)
}
func (m *GetTabsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsReq.Merge(dst, src)
}
func (m *GetTabsReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsReq.Size(m)
}
func (m *GetTabsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsReq proto.InternalMessageInfo

func (m *GetTabsReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

func (m *GetTabsReq) GetOnlyVisible() bool {
	if m != nil {
		return m.OnlyVisible
	}
	return false
}

type GetTabsResp struct {
	Tabs                 []*Tab   `protobuf:"bytes,1,rep,name=tabs,proto3" json:"tabs,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsResp) Reset()         { *m = GetTabsResp{} }
func (m *GetTabsResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsResp) ProtoMessage()    {}
func (*GetTabsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{12}
}
func (m *GetTabsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsResp.Unmarshal(m, b)
}
func (m *GetTabsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsResp.Merge(dst, src)
}
func (m *GetTabsResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsResp.Size(m)
}
func (m *GetTabsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsResp proto.InternalMessageInfo

func (m *GetTabsResp) GetTabs() []*Tab {
	if m != nil {
		return m.Tabs
	}
	return nil
}

type MinorSupervisionConfig struct {
	Id                    string                     `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                 uint32                     `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	CategoryId            uint32                     `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	MarketIds             []uint32                   `protobuf:"varint,4,rep,packed,name=market_ids,json=marketIds,proto3" json:"market_ids,omitempty"`
	SwitchOnScenes        []MinorSupervisionScene    `protobuf:"varint,5,rep,packed,name=switch_on_scenes,json=switchOnScenes,proto3,enum=channel_play_tab.MinorSupervisionScene" json:"switch_on_scenes,omitempty"`
	NeedCheckRegisterTime int64                      `protobuf:"varint,6,opt,name=need_check_register_time,json=needCheckRegisterTime,proto3" json:"need_check_register_time,omitempty"`
	ConfigType            MinorSupervisionConfigType `protobuf:"varint,7,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.MinorSupervisionConfigType" json:"config_type,omitempty"`
	XXX_NoUnkeyedLiteral  struct{}                   `json:"-"`
	XXX_unrecognized      []byte                     `json:"-"`
	XXX_sizecache         int32                      `json:"-"`
}

func (m *MinorSupervisionConfig) Reset()         { *m = MinorSupervisionConfig{} }
func (m *MinorSupervisionConfig) String() string { return proto.CompactTextString(m) }
func (*MinorSupervisionConfig) ProtoMessage()    {}
func (*MinorSupervisionConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{13}
}
func (m *MinorSupervisionConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MinorSupervisionConfig.Unmarshal(m, b)
}
func (m *MinorSupervisionConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MinorSupervisionConfig.Marshal(b, m, deterministic)
}
func (dst *MinorSupervisionConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MinorSupervisionConfig.Merge(dst, src)
}
func (m *MinorSupervisionConfig) XXX_Size() int {
	return xxx_messageInfo_MinorSupervisionConfig.Size(m)
}
func (m *MinorSupervisionConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_MinorSupervisionConfig.DiscardUnknown(m)
}

var xxx_messageInfo_MinorSupervisionConfig proto.InternalMessageInfo

func (m *MinorSupervisionConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *MinorSupervisionConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *MinorSupervisionConfig) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *MinorSupervisionConfig) GetMarketIds() []uint32 {
	if m != nil {
		return m.MarketIds
	}
	return nil
}

func (m *MinorSupervisionConfig) GetSwitchOnScenes() []MinorSupervisionScene {
	if m != nil {
		return m.SwitchOnScenes
	}
	return nil
}

func (m *MinorSupervisionConfig) GetNeedCheckRegisterTime() int64 {
	if m != nil {
		return m.NeedCheckRegisterTime
	}
	return 0
}

func (m *MinorSupervisionConfig) GetConfigType() MinorSupervisionConfigType {
	if m != nil {
		return m.ConfigType
	}
	return MinorSupervisionConfigType_ConfigTypeNone
}

type UpsertMinorSupervisionConfigReq struct {
	Config               *MinorSupervisionConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *UpsertMinorSupervisionConfigReq) Reset()         { *m = UpsertMinorSupervisionConfigReq{} }
func (m *UpsertMinorSupervisionConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertMinorSupervisionConfigReq) ProtoMessage()    {}
func (*UpsertMinorSupervisionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{14}
}
func (m *UpsertMinorSupervisionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertMinorSupervisionConfigReq.Unmarshal(m, b)
}
func (m *UpsertMinorSupervisionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertMinorSupervisionConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertMinorSupervisionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertMinorSupervisionConfigReq.Merge(dst, src)
}
func (m *UpsertMinorSupervisionConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertMinorSupervisionConfigReq.Size(m)
}
func (m *UpsertMinorSupervisionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertMinorSupervisionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertMinorSupervisionConfigReq proto.InternalMessageInfo

func (m *UpsertMinorSupervisionConfigReq) GetConfig() *MinorSupervisionConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertMinorSupervisionConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertMinorSupervisionConfigResp) Reset()         { *m = UpsertMinorSupervisionConfigResp{} }
func (m *UpsertMinorSupervisionConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertMinorSupervisionConfigResp) ProtoMessage()    {}
func (*UpsertMinorSupervisionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{15}
}
func (m *UpsertMinorSupervisionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertMinorSupervisionConfigResp.Unmarshal(m, b)
}
func (m *UpsertMinorSupervisionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertMinorSupervisionConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertMinorSupervisionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertMinorSupervisionConfigResp.Merge(dst, src)
}
func (m *UpsertMinorSupervisionConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertMinorSupervisionConfigResp.Size(m)
}
func (m *UpsertMinorSupervisionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertMinorSupervisionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertMinorSupervisionConfigResp proto.InternalMessageInfo

type BatchGetMinorSupervisionConfigReq struct {
	TabIds               []uint32 `protobuf:"varint,1,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32 `protobuf:"varint,2,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	Source               string   `protobuf:"bytes,3,opt,name=source,proto3" json:"source,omitempty"`
	NoCache              bool     `protobuf:"varint,4,opt,name=no_cache,json=noCache,proto3" json:"no_cache,omitempty"`
	ReturnAll            bool     `protobuf:"varint,5,opt,name=return_all,json=returnAll,proto3" json:"return_all,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetMinorSupervisionConfigReq) Reset()         { *m = BatchGetMinorSupervisionConfigReq{} }
func (m *BatchGetMinorSupervisionConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetMinorSupervisionConfigReq) ProtoMessage()    {}
func (*BatchGetMinorSupervisionConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{16}
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Unmarshal(m, b)
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetMinorSupervisionConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Merge(dst, src)
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigReq.Size(m)
}
func (m *BatchGetMinorSupervisionConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMinorSupervisionConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMinorSupervisionConfigReq proto.InternalMessageInfo

func (m *BatchGetMinorSupervisionConfigReq) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *BatchGetMinorSupervisionConfigReq) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

func (m *BatchGetMinorSupervisionConfigReq) GetSource() string {
	if m != nil {
		return m.Source
	}
	return ""
}

func (m *BatchGetMinorSupervisionConfigReq) GetNoCache() bool {
	if m != nil {
		return m.NoCache
	}
	return false
}

func (m *BatchGetMinorSupervisionConfigReq) GetReturnAll() bool {
	if m != nil {
		return m.ReturnAll
	}
	return false
}

type BatchGetMinorSupervisionConfigResp struct {
	TabConfigs           map[uint32]*MinorSupervisionConfig `protobuf:"bytes,1,rep,name=tab_configs,json=tabConfigs,proto3" json:"tab_configs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	CategoryConfigs      map[uint32]*MinorSupervisionConfig `protobuf:"bytes,2,rep,name=category_configs,json=categoryConfigs,proto3" json:"category_configs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-"`
	XXX_unrecognized     []byte                             `json:"-"`
	XXX_sizecache        int32                              `json:"-"`
}

func (m *BatchGetMinorSupervisionConfigResp) Reset()         { *m = BatchGetMinorSupervisionConfigResp{} }
func (m *BatchGetMinorSupervisionConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetMinorSupervisionConfigResp) ProtoMessage()    {}
func (*BatchGetMinorSupervisionConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{17}
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Unmarshal(m, b)
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetMinorSupervisionConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Merge(dst, src)
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetMinorSupervisionConfigResp.Size(m)
}
func (m *BatchGetMinorSupervisionConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetMinorSupervisionConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetMinorSupervisionConfigResp proto.InternalMessageInfo

func (m *BatchGetMinorSupervisionConfigResp) GetTabConfigs() map[uint32]*MinorSupervisionConfig {
	if m != nil {
		return m.TabConfigs
	}
	return nil
}

func (m *BatchGetMinorSupervisionConfigResp) GetCategoryConfigs() map[uint32]*MinorSupervisionConfig {
	if m != nil {
		return m.CategoryConfigs
	}
	return nil
}

type GetTabsRealNameConfigsReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count                uint32   `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetTabsRealNameConfigsReq) Reset()         { *m = GetTabsRealNameConfigsReq{} }
func (m *GetTabsRealNameConfigsReq) String() string { return proto.CompactTextString(m) }
func (*GetTabsRealNameConfigsReq) ProtoMessage()    {}
func (*GetTabsRealNameConfigsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{18}
}
func (m *GetTabsRealNameConfigsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsRealNameConfigsReq.Unmarshal(m, b)
}
func (m *GetTabsRealNameConfigsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsRealNameConfigsReq.Marshal(b, m, deterministic)
}
func (dst *GetTabsRealNameConfigsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsRealNameConfigsReq.Merge(dst, src)
}
func (m *GetTabsRealNameConfigsReq) XXX_Size() int {
	return xxx_messageInfo_GetTabsRealNameConfigsReq.Size(m)
}
func (m *GetTabsRealNameConfigsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsRealNameConfigsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsRealNameConfigsReq proto.InternalMessageInfo

func (m *GetTabsRealNameConfigsReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetTabsRealNameConfigsReq) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

type TabsRealNameConfig struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	CliVer               string   `protobuf:"bytes,3,opt,name=cli_ver,json=cliVer,proto3" json:"cli_ver,omitempty"`
	SwitchStatus         bool     `protobuf:"varint,4,opt,name=switch_status,json=switchStatus,proto3" json:"switch_status,omitempty"`
	TabIds               []uint32 `protobuf:"varint,5,rep,packed,name=tab_ids,json=tabIds,proto3" json:"tab_ids,omitempty"`
	CategoryIds          []uint32 `protobuf:"varint,6,rep,packed,name=category_ids,json=categoryIds,proto3" json:"category_ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TabsRealNameConfig) Reset()         { *m = TabsRealNameConfig{} }
func (m *TabsRealNameConfig) String() string { return proto.CompactTextString(m) }
func (*TabsRealNameConfig) ProtoMessage()    {}
func (*TabsRealNameConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{19}
}
func (m *TabsRealNameConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TabsRealNameConfig.Unmarshal(m, b)
}
func (m *TabsRealNameConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TabsRealNameConfig.Marshal(b, m, deterministic)
}
func (dst *TabsRealNameConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TabsRealNameConfig.Merge(dst, src)
}
func (m *TabsRealNameConfig) XXX_Size() int {
	return xxx_messageInfo_TabsRealNameConfig.Size(m)
}
func (m *TabsRealNameConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_TabsRealNameConfig.DiscardUnknown(m)
}

var xxx_messageInfo_TabsRealNameConfig proto.InternalMessageInfo

func (m *TabsRealNameConfig) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *TabsRealNameConfig) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *TabsRealNameConfig) GetCliVer() string {
	if m != nil {
		return m.CliVer
	}
	return ""
}

func (m *TabsRealNameConfig) GetSwitchStatus() bool {
	if m != nil {
		return m.SwitchStatus
	}
	return false
}

func (m *TabsRealNameConfig) GetTabIds() []uint32 {
	if m != nil {
		return m.TabIds
	}
	return nil
}

func (m *TabsRealNameConfig) GetCategoryIds() []uint32 {
	if m != nil {
		return m.CategoryIds
	}
	return nil
}

type GetTabsRealNameConfigsResp struct {
	Configs              []*TabsRealNameConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total                uint32                `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *GetTabsRealNameConfigsResp) Reset()         { *m = GetTabsRealNameConfigsResp{} }
func (m *GetTabsRealNameConfigsResp) String() string { return proto.CompactTextString(m) }
func (*GetTabsRealNameConfigsResp) ProtoMessage()    {}
func (*GetTabsRealNameConfigsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{20}
}
func (m *GetTabsRealNameConfigsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetTabsRealNameConfigsResp.Unmarshal(m, b)
}
func (m *GetTabsRealNameConfigsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetTabsRealNameConfigsResp.Marshal(b, m, deterministic)
}
func (dst *GetTabsRealNameConfigsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetTabsRealNameConfigsResp.Merge(dst, src)
}
func (m *GetTabsRealNameConfigsResp) XXX_Size() int {
	return xxx_messageInfo_GetTabsRealNameConfigsResp.Size(m)
}
func (m *GetTabsRealNameConfigsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetTabsRealNameConfigsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetTabsRealNameConfigsResp proto.InternalMessageInfo

func (m *GetTabsRealNameConfigsResp) GetConfigs() []*TabsRealNameConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

func (m *GetTabsRealNameConfigsResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

type UpdateTabsRealNameConfigReq struct {
	Config               *TabsRealNameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *UpdateTabsRealNameConfigReq) Reset()         { *m = UpdateTabsRealNameConfigReq{} }
func (m *UpdateTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateTabsRealNameConfigReq) ProtoMessage()    {}
func (*UpdateTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{21}
}
func (m *UpdateTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *UpdateTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTabsRealNameConfigReq.Merge(dst, src)
}
func (m *UpdateTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateTabsRealNameConfigReq.Size(m)
}
func (m *UpdateTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTabsRealNameConfigReq proto.InternalMessageInfo

func (m *UpdateTabsRealNameConfigReq) GetConfig() *TabsRealNameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpdateTabsRealNameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateTabsRealNameConfigResp) Reset()         { *m = UpdateTabsRealNameConfigResp{} }
func (m *UpdateTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateTabsRealNameConfigResp) ProtoMessage()    {}
func (*UpdateTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{22}
}
func (m *UpdateTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *UpdateTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateTabsRealNameConfigResp.Merge(dst, src)
}
func (m *UpdateTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateTabsRealNameConfigResp.Size(m)
}
func (m *UpdateTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateTabsRealNameConfigResp proto.InternalMessageInfo

type AddTabsRealNameConfigReq struct {
	Config               *TabsRealNameConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *AddTabsRealNameConfigReq) Reset()         { *m = AddTabsRealNameConfigReq{} }
func (m *AddTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*AddTabsRealNameConfigReq) ProtoMessage()    {}
func (*AddTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{23}
}
func (m *AddTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *AddTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *AddTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTabsRealNameConfigReq.Merge(dst, src)
}
func (m *AddTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_AddTabsRealNameConfigReq.Size(m)
}
func (m *AddTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_AddTabsRealNameConfigReq proto.InternalMessageInfo

func (m *AddTabsRealNameConfigReq) GetConfig() *TabsRealNameConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type AddTabsRealNameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *AddTabsRealNameConfigResp) Reset()         { *m = AddTabsRealNameConfigResp{} }
func (m *AddTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*AddTabsRealNameConfigResp) ProtoMessage()    {}
func (*AddTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{24}
}
func (m *AddTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AddTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *AddTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AddTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *AddTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AddTabsRealNameConfigResp.Merge(dst, src)
}
func (m *AddTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_AddTabsRealNameConfigResp.Size(m)
}
func (m *AddTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AddTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_AddTabsRealNameConfigResp proto.InternalMessageInfo

type DeleteTabsRealNameConfigReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	CliVer               string   `protobuf:"bytes,3,opt,name=cli_ver,json=cliVer,proto3" json:"cli_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabsRealNameConfigReq) Reset()         { *m = DeleteTabsRealNameConfigReq{} }
func (m *DeleteTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteTabsRealNameConfigReq) ProtoMessage()    {}
func (*DeleteTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{25}
}
func (m *DeleteTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *DeleteTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabsRealNameConfigReq.Merge(dst, src)
}
func (m *DeleteTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteTabsRealNameConfigReq.Size(m)
}
func (m *DeleteTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabsRealNameConfigReq proto.InternalMessageInfo

func (m *DeleteTabsRealNameConfigReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *DeleteTabsRealNameConfigReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *DeleteTabsRealNameConfigReq) GetCliVer() string {
	if m != nil {
		return m.CliVer
	}
	return ""
}

type DeleteTabsRealNameConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteTabsRealNameConfigResp) Reset()         { *m = DeleteTabsRealNameConfigResp{} }
func (m *DeleteTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteTabsRealNameConfigResp) ProtoMessage()    {}
func (*DeleteTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{26}
}
func (m *DeleteTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *DeleteTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteTabsRealNameConfigResp.Merge(dst, src)
}
func (m *DeleteTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteTabsRealNameConfigResp.Size(m)
}
func (m *DeleteTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteTabsRealNameConfigResp proto.InternalMessageInfo

type GetUserTabsRealNameConfigReq struct {
	MarketId             uint32   `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ChannelPkg           string   `protobuf:"bytes,2,opt,name=channel_pkg,json=channelPkg,proto3" json:"channel_pkg,omitempty"`
	CliVer               uint32   `protobuf:"varint,3,opt,name=cli_ver,json=cliVer,proto3" json:"cli_ver,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserTabsRealNameConfigReq) Reset()         { *m = GetUserTabsRealNameConfigReq{} }
func (m *GetUserTabsRealNameConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetUserTabsRealNameConfigReq) ProtoMessage()    {}
func (*GetUserTabsRealNameConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{27}
}
func (m *GetUserTabsRealNameConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTabsRealNameConfigReq.Unmarshal(m, b)
}
func (m *GetUserTabsRealNameConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTabsRealNameConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetUserTabsRealNameConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTabsRealNameConfigReq.Merge(dst, src)
}
func (m *GetUserTabsRealNameConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetUserTabsRealNameConfigReq.Size(m)
}
func (m *GetUserTabsRealNameConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTabsRealNameConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTabsRealNameConfigReq proto.InternalMessageInfo

func (m *GetUserTabsRealNameConfigReq) GetMarketId() uint32 {
	if m != nil {
		return m.MarketId
	}
	return 0
}

func (m *GetUserTabsRealNameConfigReq) GetChannelPkg() string {
	if m != nil {
		return m.ChannelPkg
	}
	return ""
}

func (m *GetUserTabsRealNameConfigReq) GetCliVer() uint32 {
	if m != nil {
		return m.CliVer
	}
	return 0
}

type GetUserTabsRealNameConfigResp struct {
	Configs              *TabsRealNameConfig `protobuf:"bytes,1,opt,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetUserTabsRealNameConfigResp) Reset()         { *m = GetUserTabsRealNameConfigResp{} }
func (m *GetUserTabsRealNameConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetUserTabsRealNameConfigResp) ProtoMessage()    {}
func (*GetUserTabsRealNameConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{28}
}
func (m *GetUserTabsRealNameConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserTabsRealNameConfigResp.Unmarshal(m, b)
}
func (m *GetUserTabsRealNameConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserTabsRealNameConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetUserTabsRealNameConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserTabsRealNameConfigResp.Merge(dst, src)
}
func (m *GetUserTabsRealNameConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetUserTabsRealNameConfigResp.Size(m)
}
func (m *GetUserTabsRealNameConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserTabsRealNameConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserTabsRealNameConfigResp proto.InternalMessageInfo

func (m *GetUserTabsRealNameConfigResp) GetConfigs() *TabsRealNameConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type SceneInfo struct {
	Id         string               `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId      uint32               `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabType    uint32               `protobuf:"varint,3,opt,name=tab_type,json=tabType,proto3" json:"tab_type,omitempty"`
	CategoryId uint32               `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	ConfigType QuickMatchConfigType `protobuf:"varint,5,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.QuickMatchConfigType" json:"config_type,omitempty"`
	// 休闲互动专区快速匹配配置字段
	GameDisplayName string   `protobuf:"bytes,6,opt,name=game_display_name,json=gameDisplayName,proto3" json:"game_display_name,omitempty"`
	BgColors        []string `protobuf:"bytes,7,rep,name=bg_colors,json=bgColors,proto3" json:"bg_colors,omitempty"`
	ButtonEffect    uint32   `protobuf:"varint,8,opt,name=button_effect,json=buttonEffect,proto3" json:"button_effect,omitempty"`
	JumpLink        string   `protobuf:"bytes,9,opt,name=jump_link,json=jumpLink,proto3" json:"jump_link,omitempty"`
	// 休闲互动专区重点区域配置额外字段
	MiniGameExtraInfo    *HotMiniGameExtraInfo `protobuf:"bytes,10,opt,name=mini_game_extra_info,json=miniGameExtraInfo,proto3" json:"mini_game_extra_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-"`
	XXX_unrecognized     []byte                `json:"-"`
	XXX_sizecache        int32                 `json:"-"`
}

func (m *SceneInfo) Reset()         { *m = SceneInfo{} }
func (m *SceneInfo) String() string { return proto.CompactTextString(m) }
func (*SceneInfo) ProtoMessage()    {}
func (*SceneInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{29}
}
func (m *SceneInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SceneInfo.Unmarshal(m, b)
}
func (m *SceneInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SceneInfo.Marshal(b, m, deterministic)
}
func (dst *SceneInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SceneInfo.Merge(dst, src)
}
func (m *SceneInfo) XXX_Size() int {
	return xxx_messageInfo_SceneInfo.Size(m)
}
func (m *SceneInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SceneInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SceneInfo proto.InternalMessageInfo

func (m *SceneInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *SceneInfo) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *SceneInfo) GetTabType() uint32 {
	if m != nil {
		return m.TabType
	}
	return 0
}

func (m *SceneInfo) GetCategoryId() uint32 {
	if m != nil {
		return m.CategoryId
	}
	return 0
}

func (m *SceneInfo) GetConfigType() QuickMatchConfigType {
	if m != nil {
		return m.ConfigType
	}
	return QuickMatchConfigType_Invalid
}

func (m *SceneInfo) GetGameDisplayName() string {
	if m != nil {
		return m.GameDisplayName
	}
	return ""
}

func (m *SceneInfo) GetBgColors() []string {
	if m != nil {
		return m.BgColors
	}
	return nil
}

func (m *SceneInfo) GetButtonEffect() uint32 {
	if m != nil {
		return m.ButtonEffect
	}
	return 0
}

func (m *SceneInfo) GetJumpLink() string {
	if m != nil {
		return m.JumpLink
	}
	return ""
}

func (m *SceneInfo) GetMiniGameExtraInfo() *HotMiniGameExtraInfo {
	if m != nil {
		return m.MiniGameExtraInfo
	}
	return nil
}

type HotMiniGameExtraInfo struct {
	// 介绍文案
	IntroText string `protobuf:"bytes,1,opt,name=intro_text,json=introText,proto3" json:"intro_text,omitempty"`
	// 背景图 url
	BgUrl string `protobuf:"bytes,2,opt,name=bg_url,json=bgUrl,proto3" json:"bg_url,omitempty"`
	// 背景颜色
	BgColor string `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// 按钮文案
	ButtonText string `protobuf:"bytes,4,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	// 按钮颜色组
	ButtonColors []string `protobuf:"bytes,5,rep,name=button_colors,json=buttonColors,proto3" json:"button_colors,omitempty"`
	// 动图 lottie url
	LottieUrl            string   `protobuf:"bytes,6,opt,name=lottie_url,json=lottieUrl,proto3" json:"lottie_url,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *HotMiniGameExtraInfo) Reset()         { *m = HotMiniGameExtraInfo{} }
func (m *HotMiniGameExtraInfo) String() string { return proto.CompactTextString(m) }
func (*HotMiniGameExtraInfo) ProtoMessage()    {}
func (*HotMiniGameExtraInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{30}
}
func (m *HotMiniGameExtraInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HotMiniGameExtraInfo.Unmarshal(m, b)
}
func (m *HotMiniGameExtraInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HotMiniGameExtraInfo.Marshal(b, m, deterministic)
}
func (dst *HotMiniGameExtraInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HotMiniGameExtraInfo.Merge(dst, src)
}
func (m *HotMiniGameExtraInfo) XXX_Size() int {
	return xxx_messageInfo_HotMiniGameExtraInfo.Size(m)
}
func (m *HotMiniGameExtraInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_HotMiniGameExtraInfo.DiscardUnknown(m)
}

var xxx_messageInfo_HotMiniGameExtraInfo proto.InternalMessageInfo

func (m *HotMiniGameExtraInfo) GetIntroText() string {
	if m != nil {
		return m.IntroText
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetBgUrl() string {
	if m != nil {
		return m.BgUrl
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetBgColor() string {
	if m != nil {
		return m.BgColor
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *HotMiniGameExtraInfo) GetButtonColors() []string {
	if m != nil {
		return m.ButtonColors
	}
	return nil
}

func (m *HotMiniGameExtraInfo) GetLottieUrl() string {
	if m != nil {
		return m.LottieUrl
	}
	return ""
}

type GetQuickMatchConfigReq struct {
	ConfigType           QuickMatchConfigType `protobuf:"varint,1,opt,name=config_type,json=configType,proto3,enum=channel_play_tab.QuickMatchConfigType" json:"config_type,omitempty"`
	Page                 uint32               `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Limit                uint32               `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetQuickMatchConfigReq) Reset()         { *m = GetQuickMatchConfigReq{} }
func (m *GetQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchConfigReq) ProtoMessage()    {}
func (*GetQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{31}
}
func (m *GetQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *GetQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchConfigReq.Merge(dst, src)
}
func (m *GetQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchConfigReq.Size(m)
}
func (m *GetQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchConfigReq proto.InternalMessageInfo

func (m *GetQuickMatchConfigReq) GetConfigType() QuickMatchConfigType {
	if m != nil {
		return m.ConfigType
	}
	return QuickMatchConfigType_Invalid
}

func (m *GetQuickMatchConfigReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetQuickMatchConfigReq) GetLimit() uint32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

type GetQuickMatchConfigResp struct {
	SceneInfo            []*SceneInfo `protobuf:"bytes,1,rep,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *GetQuickMatchConfigResp) Reset()         { *m = GetQuickMatchConfigResp{} }
func (m *GetQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetQuickMatchConfigResp) ProtoMessage()    {}
func (*GetQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{32}
}
func (m *GetQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *GetQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetQuickMatchConfigResp.Merge(dst, src)
}
func (m *GetQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetQuickMatchConfigResp.Size(m)
}
func (m *GetQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetQuickMatchConfigResp proto.InternalMessageInfo

func (m *GetQuickMatchConfigResp) GetSceneInfo() []*SceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

type UpdateQuickMatchConfigReq struct {
	SceneInfo            *SceneInfo `protobuf:"bytes,1,opt,name=scene_info,json=sceneInfo,proto3" json:"scene_info,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UpdateQuickMatchConfigReq) Reset()         { *m = UpdateQuickMatchConfigReq{} }
func (m *UpdateQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpdateQuickMatchConfigReq) ProtoMessage()    {}
func (*UpdateQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{33}
}
func (m *UpdateQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *UpdateQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpdateQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateQuickMatchConfigReq.Merge(dst, src)
}
func (m *UpdateQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpdateQuickMatchConfigReq.Size(m)
}
func (m *UpdateQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateQuickMatchConfigReq proto.InternalMessageInfo

func (m *UpdateQuickMatchConfigReq) GetSceneInfo() *SceneInfo {
	if m != nil {
		return m.SceneInfo
	}
	return nil
}

type UpdateQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateQuickMatchConfigResp) Reset()         { *m = UpdateQuickMatchConfigResp{} }
func (m *UpdateQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpdateQuickMatchConfigResp) ProtoMessage()    {}
func (*UpdateQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{34}
}
func (m *UpdateQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *UpdateQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpdateQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateQuickMatchConfigResp.Merge(dst, src)
}
func (m *UpdateQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpdateQuickMatchConfigResp.Size(m)
}
func (m *UpdateQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateQuickMatchConfigResp proto.InternalMessageInfo

type DeleteQuickMatchConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteQuickMatchConfigReq) Reset()         { *m = DeleteQuickMatchConfigReq{} }
func (m *DeleteQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*DeleteQuickMatchConfigReq) ProtoMessage()    {}
func (*DeleteQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{35}
}
func (m *DeleteQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *DeleteQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *DeleteQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteQuickMatchConfigReq.Merge(dst, src)
}
func (m *DeleteQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_DeleteQuickMatchConfigReq.Size(m)
}
func (m *DeleteQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteQuickMatchConfigReq proto.InternalMessageInfo

func (m *DeleteQuickMatchConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DeleteQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DeleteQuickMatchConfigResp) Reset()         { *m = DeleteQuickMatchConfigResp{} }
func (m *DeleteQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*DeleteQuickMatchConfigResp) ProtoMessage()    {}
func (*DeleteQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{36}
}
func (m *DeleteQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DeleteQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *DeleteQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DeleteQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *DeleteQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DeleteQuickMatchConfigResp.Merge(dst, src)
}
func (m *DeleteQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_DeleteQuickMatchConfigResp.Size(m)
}
func (m *DeleteQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DeleteQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DeleteQuickMatchConfigResp proto.InternalMessageInfo

type ResortQuickMatchConfigReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortQuickMatchConfigReq) Reset()         { *m = ResortQuickMatchConfigReq{} }
func (m *ResortQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*ResortQuickMatchConfigReq) ProtoMessage()    {}
func (*ResortQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{37}
}
func (m *ResortQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *ResortQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *ResortQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortQuickMatchConfigReq.Merge(dst, src)
}
func (m *ResortQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_ResortQuickMatchConfigReq.Size(m)
}
func (m *ResortQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_ResortQuickMatchConfigReq proto.InternalMessageInfo

func (m *ResortQuickMatchConfigReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type ResortQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ResortQuickMatchConfigResp) Reset()         { *m = ResortQuickMatchConfigResp{} }
func (m *ResortQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*ResortQuickMatchConfigResp) ProtoMessage()    {}
func (*ResortQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{38}
}
func (m *ResortQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ResortQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *ResortQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ResortQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *ResortQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ResortQuickMatchConfigResp.Merge(dst, src)
}
func (m *ResortQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_ResortQuickMatchConfigResp.Size(m)
}
func (m *ResortQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ResortQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_ResortQuickMatchConfigResp proto.InternalMessageInfo

type NewQuickMatchConfig struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	TabId                uint32   `protobuf:"varint,2,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	TabName              string   `protobuf:"bytes,3,opt,name=tab_name,json=tabName,proto3" json:"tab_name,omitempty"`
	Title                string   `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	ButtonText           string   `protobuf:"bytes,5,opt,name=button_text,json=buttonText,proto3" json:"button_text,omitempty"`
	Position             uint32   `protobuf:"varint,6,opt,name=position,proto3" json:"position,omitempty"`
	UpdatedAt            int64    `protobuf:"varint,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *NewQuickMatchConfig) Reset()         { *m = NewQuickMatchConfig{} }
func (m *NewQuickMatchConfig) String() string { return proto.CompactTextString(m) }
func (*NewQuickMatchConfig) ProtoMessage()    {}
func (*NewQuickMatchConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{39}
}
func (m *NewQuickMatchConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NewQuickMatchConfig.Unmarshal(m, b)
}
func (m *NewQuickMatchConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NewQuickMatchConfig.Marshal(b, m, deterministic)
}
func (dst *NewQuickMatchConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NewQuickMatchConfig.Merge(dst, src)
}
func (m *NewQuickMatchConfig) XXX_Size() int {
	return xxx_messageInfo_NewQuickMatchConfig.Size(m)
}
func (m *NewQuickMatchConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_NewQuickMatchConfig.DiscardUnknown(m)
}

var xxx_messageInfo_NewQuickMatchConfig proto.InternalMessageInfo

func (m *NewQuickMatchConfig) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *NewQuickMatchConfig) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

func (m *NewQuickMatchConfig) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *NewQuickMatchConfig) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *NewQuickMatchConfig) GetButtonText() string {
	if m != nil {
		return m.ButtonText
	}
	return ""
}

func (m *NewQuickMatchConfig) GetPosition() uint32 {
	if m != nil {
		return m.Position
	}
	return 0
}

func (m *NewQuickMatchConfig) GetUpdatedAt() int64 {
	if m != nil {
		return m.UpdatedAt
	}
	return 0
}

type UpsertNewQuickMatchConfigReq struct {
	Config               *NewQuickMatchConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *UpsertNewQuickMatchConfigReq) Reset()         { *m = UpsertNewQuickMatchConfigReq{} }
func (m *UpsertNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*UpsertNewQuickMatchConfigReq) ProtoMessage()    {}
func (*UpsertNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{40}
}
func (m *UpsertNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *UpsertNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *UpsertNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *UpsertNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_UpsertNewQuickMatchConfigReq.Size(m)
}
func (m *UpsertNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *UpsertNewQuickMatchConfigReq) GetConfig() *NewQuickMatchConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

type UpsertNewQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpsertNewQuickMatchConfigResp) Reset()         { *m = UpsertNewQuickMatchConfigResp{} }
func (m *UpsertNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*UpsertNewQuickMatchConfigResp) ProtoMessage()    {}
func (*UpsertNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{41}
}
func (m *UpsertNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpsertNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *UpsertNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpsertNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *UpsertNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpsertNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *UpsertNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_UpsertNewQuickMatchConfigResp.Size(m)
}
func (m *UpsertNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpsertNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_UpsertNewQuickMatchConfigResp proto.InternalMessageInfo

type BatchGetNewQuickMatchConfigReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BatchGetNewQuickMatchConfigReq) Reset()         { *m = BatchGetNewQuickMatchConfigReq{} }
func (m *BatchGetNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*BatchGetNewQuickMatchConfigReq) ProtoMessage()    {}
func (*BatchGetNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{42}
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *BatchGetNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigReq.Size(m)
}
func (m *BatchGetNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *BatchGetNewQuickMatchConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type BatchGetNewQuickMatchConfigResp struct {
	Configs              []*NewQuickMatchConfig `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-"`
	XXX_unrecognized     []byte                 `json:"-"`
	XXX_sizecache        int32                  `json:"-"`
}

func (m *BatchGetNewQuickMatchConfigResp) Reset()         { *m = BatchGetNewQuickMatchConfigResp{} }
func (m *BatchGetNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*BatchGetNewQuickMatchConfigResp) ProtoMessage()    {}
func (*BatchGetNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{43}
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *BatchGetNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_BatchGetNewQuickMatchConfigResp.Size(m)
}
func (m *BatchGetNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchGetNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_BatchGetNewQuickMatchConfigResp proto.InternalMessageInfo

func (m *BatchGetNewQuickMatchConfigResp) GetConfigs() []*NewQuickMatchConfig {
	if m != nil {
		return m.Configs
	}
	return nil
}

type DelNewQuickMatchConfigReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelNewQuickMatchConfigReq) Reset()         { *m = DelNewQuickMatchConfigReq{} }
func (m *DelNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*DelNewQuickMatchConfigReq) ProtoMessage()    {}
func (*DelNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{44}
}
func (m *DelNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *DelNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *DelNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *DelNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_DelNewQuickMatchConfigReq.Size(m)
}
func (m *DelNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DelNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_DelNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *DelNewQuickMatchConfigReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type DelNewQuickMatchConfigResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *DelNewQuickMatchConfigResp) Reset()         { *m = DelNewQuickMatchConfigResp{} }
func (m *DelNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*DelNewQuickMatchConfigResp) ProtoMessage()    {}
func (*DelNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{45}
}
func (m *DelNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DelNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *DelNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DelNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *DelNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DelNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *DelNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_DelNewQuickMatchConfigResp.Size(m)
}
func (m *DelNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DelNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_DelNewQuickMatchConfigResp proto.InternalMessageInfo

type GetNewQuickMatchConfigReq struct {
	TabId                uint32   `protobuf:"varint,1,opt,name=tab_id,json=tabId,proto3" json:"tab_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetNewQuickMatchConfigReq) Reset()         { *m = GetNewQuickMatchConfigReq{} }
func (m *GetNewQuickMatchConfigReq) String() string { return proto.CompactTextString(m) }
func (*GetNewQuickMatchConfigReq) ProtoMessage()    {}
func (*GetNewQuickMatchConfigReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{46}
}
func (m *GetNewQuickMatchConfigReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Unmarshal(m, b)
}
func (m *GetNewQuickMatchConfigReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Marshal(b, m, deterministic)
}
func (dst *GetNewQuickMatchConfigReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewQuickMatchConfigReq.Merge(dst, src)
}
func (m *GetNewQuickMatchConfigReq) XXX_Size() int {
	return xxx_messageInfo_GetNewQuickMatchConfigReq.Size(m)
}
func (m *GetNewQuickMatchConfigReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewQuickMatchConfigReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewQuickMatchConfigReq proto.InternalMessageInfo

func (m *GetNewQuickMatchConfigReq) GetTabId() uint32 {
	if m != nil {
		return m.TabId
	}
	return 0
}

type GetNewQuickMatchConfigResp struct {
	Config               *NewQuickMatchConfig `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-"`
	XXX_unrecognized     []byte               `json:"-"`
	XXX_sizecache        int32                `json:"-"`
}

func (m *GetNewQuickMatchConfigResp) Reset()         { *m = GetNewQuickMatchConfigResp{} }
func (m *GetNewQuickMatchConfigResp) String() string { return proto.CompactTextString(m) }
func (*GetNewQuickMatchConfigResp) ProtoMessage()    {}
func (*GetNewQuickMatchConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_channel_play_tab_53c946d10d565e08, []int{47}
}
func (m *GetNewQuickMatchConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Unmarshal(m, b)
}
func (m *GetNewQuickMatchConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Marshal(b, m, deterministic)
}
func (dst *GetNewQuickMatchConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetNewQuickMatchConfigResp.Merge(dst, src)
}
func (m *GetNewQuickMatchConfigResp) XXX_Size() int {
	return xxx_messageInfo_GetNewQuickMatchConfigResp.Size(m)
}
func (m *GetNewQuickMatchConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetNewQuickMatchConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetNewQuickMatchConfigResp proto.InternalMessageInfo

func (m *GetNewQuickMatchConfigResp) GetConfig() *NewQuickMatchConfig {
	if m != nil {
		return m.Config
	}
	return nil
}

func init() {
	proto.RegisterType((*BatchGetWhiteUidListByTabIdsReq)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsReq")
	proto.RegisterType((*BatchGetWhiteUidListByTabIdsResp)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsResp")
	proto.RegisterMapType((map[uint32]*BatchGetWhiteUidListByTabIdsResp_WhiteUidList)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsResp.TabListMapEntry")
	proto.RegisterType((*BatchGetWhiteUidListByTabIdsResp_WhiteUidList)(nil), "channel_play_tab.BatchGetWhiteUidListByTabIdsResp.WhiteUidList")
	proto.RegisterType((*SetWhiteUidListByTabIdReq)(nil), "channel_play_tab.SetWhiteUidListByTabIdReq")
	proto.RegisterType((*SetWhiteUidListByTabIdResp)(nil), "channel_play_tab.SetWhiteUidListByTabIdResp")
	proto.RegisterType((*Tab)(nil), "channel_play_tab.Tab")
	proto.RegisterType((*UpsertTabReq)(nil), "channel_play_tab.UpsertTabReq")
	proto.RegisterType((*UpsertTabResp)(nil), "channel_play_tab.UpsertTabResp")
	proto.RegisterType((*GetTabsByTabSubTypeReq)(nil), "channel_play_tab.GetTabsByTabSubTypeReq")
	proto.RegisterType((*GetTabsByTabSubTypeResp)(nil), "channel_play_tab.GetTabsByTabSubTypeResp")
	proto.RegisterType((*DeleteTabReq)(nil), "channel_play_tab.DeleteTabReq")
	proto.RegisterType((*DeleteTabResp)(nil), "channel_play_tab.DeleteTabResp")
	proto.RegisterType((*GetTabsReq)(nil), "channel_play_tab.GetTabsReq")
	proto.RegisterType((*GetTabsResp)(nil), "channel_play_tab.GetTabsResp")
	proto.RegisterType((*MinorSupervisionConfig)(nil), "channel_play_tab.MinorSupervisionConfig")
	proto.RegisterType((*UpsertMinorSupervisionConfigReq)(nil), "channel_play_tab.UpsertMinorSupervisionConfigReq")
	proto.RegisterType((*UpsertMinorSupervisionConfigResp)(nil), "channel_play_tab.UpsertMinorSupervisionConfigResp")
	proto.RegisterType((*BatchGetMinorSupervisionConfigReq)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigReq")
	proto.RegisterType((*BatchGetMinorSupervisionConfigResp)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigResp")
	proto.RegisterMapType((map[uint32]*MinorSupervisionConfig)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigResp.CategoryConfigsEntry")
	proto.RegisterMapType((map[uint32]*MinorSupervisionConfig)(nil), "channel_play_tab.BatchGetMinorSupervisionConfigResp.TabConfigsEntry")
	proto.RegisterType((*GetTabsRealNameConfigsReq)(nil), "channel_play_tab.GetTabsRealNameConfigsReq")
	proto.RegisterType((*TabsRealNameConfig)(nil), "channel_play_tab.TabsRealNameConfig")
	proto.RegisterType((*GetTabsRealNameConfigsResp)(nil), "channel_play_tab.GetTabsRealNameConfigsResp")
	proto.RegisterType((*UpdateTabsRealNameConfigReq)(nil), "channel_play_tab.UpdateTabsRealNameConfigReq")
	proto.RegisterType((*UpdateTabsRealNameConfigResp)(nil), "channel_play_tab.UpdateTabsRealNameConfigResp")
	proto.RegisterType((*AddTabsRealNameConfigReq)(nil), "channel_play_tab.AddTabsRealNameConfigReq")
	proto.RegisterType((*AddTabsRealNameConfigResp)(nil), "channel_play_tab.AddTabsRealNameConfigResp")
	proto.RegisterType((*DeleteTabsRealNameConfigReq)(nil), "channel_play_tab.DeleteTabsRealNameConfigReq")
	proto.RegisterType((*DeleteTabsRealNameConfigResp)(nil), "channel_play_tab.DeleteTabsRealNameConfigResp")
	proto.RegisterType((*GetUserTabsRealNameConfigReq)(nil), "channel_play_tab.GetUserTabsRealNameConfigReq")
	proto.RegisterType((*GetUserTabsRealNameConfigResp)(nil), "channel_play_tab.GetUserTabsRealNameConfigResp")
	proto.RegisterType((*SceneInfo)(nil), "channel_play_tab.SceneInfo")
	proto.RegisterType((*HotMiniGameExtraInfo)(nil), "channel_play_tab.HotMiniGameExtraInfo")
	proto.RegisterType((*GetQuickMatchConfigReq)(nil), "channel_play_tab.GetQuickMatchConfigReq")
	proto.RegisterType((*GetQuickMatchConfigResp)(nil), "channel_play_tab.GetQuickMatchConfigResp")
	proto.RegisterType((*UpdateQuickMatchConfigReq)(nil), "channel_play_tab.UpdateQuickMatchConfigReq")
	proto.RegisterType((*UpdateQuickMatchConfigResp)(nil), "channel_play_tab.UpdateQuickMatchConfigResp")
	proto.RegisterType((*DeleteQuickMatchConfigReq)(nil), "channel_play_tab.DeleteQuickMatchConfigReq")
	proto.RegisterType((*DeleteQuickMatchConfigResp)(nil), "channel_play_tab.DeleteQuickMatchConfigResp")
	proto.RegisterType((*ResortQuickMatchConfigReq)(nil), "channel_play_tab.ResortQuickMatchConfigReq")
	proto.RegisterType((*ResortQuickMatchConfigResp)(nil), "channel_play_tab.ResortQuickMatchConfigResp")
	proto.RegisterType((*NewQuickMatchConfig)(nil), "channel_play_tab.NewQuickMatchConfig")
	proto.RegisterType((*UpsertNewQuickMatchConfigReq)(nil), "channel_play_tab.UpsertNewQuickMatchConfigReq")
	proto.RegisterType((*UpsertNewQuickMatchConfigResp)(nil), "channel_play_tab.UpsertNewQuickMatchConfigResp")
	proto.RegisterType((*BatchGetNewQuickMatchConfigReq)(nil), "channel_play_tab.BatchGetNewQuickMatchConfigReq")
	proto.RegisterType((*BatchGetNewQuickMatchConfigResp)(nil), "channel_play_tab.BatchGetNewQuickMatchConfigResp")
	proto.RegisterType((*DelNewQuickMatchConfigReq)(nil), "channel_play_tab.DelNewQuickMatchConfigReq")
	proto.RegisterType((*DelNewQuickMatchConfigResp)(nil), "channel_play_tab.DelNewQuickMatchConfigResp")
	proto.RegisterType((*GetNewQuickMatchConfigReq)(nil), "channel_play_tab.GetNewQuickMatchConfigReq")
	proto.RegisterType((*GetNewQuickMatchConfigResp)(nil), "channel_play_tab.GetNewQuickMatchConfigResp")
	proto.RegisterEnum("channel_play_tab.TabSubType", TabSubType_name, TabSubType_value)
	proto.RegisterEnum("channel_play_tab.MinorSupervisionScene", MinorSupervisionScene_name, MinorSupervisionScene_value)
	proto.RegisterEnum("channel_play_tab.MinorSupervisionConfigType", MinorSupervisionConfigType_name, MinorSupervisionConfigType_value)
	proto.RegisterEnum("channel_play_tab.QuickMatchConfigType", QuickMatchConfigType_name, QuickMatchConfigType_value)
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// ChannelPlayTabClient is the client API for ChannelPlayTab service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ChannelPlayTabClient interface {
	// 批量获取玩法可见用户白名单
	BatchGetWhiteUidListByTabIds(ctx context.Context, in *BatchGetWhiteUidListByTabIdsReq, opts ...grpc.CallOption) (*BatchGetWhiteUidListByTabIdsResp, error)
	// 保存玩法白名单,仅运营后台用
	SetWhiteUidListByTabId(ctx context.Context, in *SetWhiteUidListByTabIdReq, opts ...grpc.CallOption) (*SetWhiteUidListByTabIdResp, error)
	// 获取指定类型的tab
	GetTabsByTabSubType(ctx context.Context, in *GetTabsByTabSubTypeReq, opts ...grpc.CallOption) (*GetTabsByTabSubTypeResp, error)
	// 插入/更新tab,仅运营后台用
	UpsertTab(ctx context.Context, in *UpsertTabReq, opts ...grpc.CallOption) (*UpsertTabResp, error)
	// 删除tab,仅运营后台用
	DeleteTab(ctx context.Context, in *DeleteTabReq, opts ...grpc.CallOption) (*DeleteTabResp, error)
	// 获取所有tab,读缓存
	GetTabs(ctx context.Context, in *GetTabsReq, opts ...grpc.CallOption) (*GetTabsResp, error)
	// 插入/更新玩法或者分类的未成年监管控制开关
	UpsertMinorSupervisionConfig(ctx context.Context, in *UpsertMinorSupervisionConfigReq, opts ...grpc.CallOption) (*UpsertMinorSupervisionConfigResp, error)
	// 批量获取玩法或者分类的未成年监管控制开关
	BatchGetMinorSupervisionConfig(ctx context.Context, in *BatchGetMinorSupervisionConfigReq, opts ...grpc.CallOption) (*BatchGetMinorSupervisionConfigResp, error)
	// （未成年）玩法开关协议配置 运营后台
	GetTabsRealNameConfigs(ctx context.Context, in *GetTabsRealNameConfigsReq, opts ...grpc.CallOption) (*GetTabsRealNameConfigsResp, error)
	UpdateTabsRealNameConfig(ctx context.Context, in *UpdateTabsRealNameConfigReq, opts ...grpc.CallOption) (*UpdateTabsRealNameConfigResp, error)
	AddTabsRealNameConfig(ctx context.Context, in *AddTabsRealNameConfigReq, opts ...grpc.CallOption) (*AddTabsRealNameConfigResp, error)
	DeleteTabsRealNameConfig(ctx context.Context, in *DeleteTabsRealNameConfigReq, opts ...grpc.CallOption) (*DeleteTabsRealNameConfigResp, error)
	// 用户入口筛选tab开关展示
	GetUserTabsRealNameConfig(ctx context.Context, in *GetUserTabsRealNameConfigReq, opts ...grpc.CallOption) (*GetUserTabsRealNameConfigResp, error)
	// 快速匹配相关新接口 旧的首页玩法卡GetHomePagePlayCards相关和GetDetailTabInfoOfScene，GetSceneTabsForTT都不用了，后面统一使用新的
	GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq, opts ...grpc.CallOption) (*GetQuickMatchConfigResp, error)
	UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq, opts ...grpc.CallOption) (*UpdateQuickMatchConfigResp, error)
	DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq, opts ...grpc.CallOption) (*DeleteQuickMatchConfigResp, error)
	ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq, opts ...grpc.CallOption) (*ResortQuickMatchConfigResp, error)
	// 新版首页快速匹配入口配置 运营后台
	UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq, opts ...grpc.CallOption) (*UpsertNewQuickMatchConfigResp, error)
	BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*BatchGetNewQuickMatchConfigResp, error)
	DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq, opts ...grpc.CallOption) (*DelNewQuickMatchConfigResp, error)
	// 客户端获取配置
	GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*GetNewQuickMatchConfigResp, error)
}

type channelPlayTabClient struct {
	cc *grpc.ClientConn
}

func NewChannelPlayTabClient(cc *grpc.ClientConn) ChannelPlayTabClient {
	return &channelPlayTabClient{cc}
}

func (c *channelPlayTabClient) BatchGetWhiteUidListByTabIds(ctx context.Context, in *BatchGetWhiteUidListByTabIdsReq, opts ...grpc.CallOption) (*BatchGetWhiteUidListByTabIdsResp, error) {
	out := new(BatchGetWhiteUidListByTabIdsResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchGetWhiteUidListByTabIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) SetWhiteUidListByTabId(ctx context.Context, in *SetWhiteUidListByTabIdReq, opts ...grpc.CallOption) (*SetWhiteUidListByTabIdResp, error) {
	out := new(SetWhiteUidListByTabIdResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/SetWhiteUidListByTabId", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetTabsByTabSubType(ctx context.Context, in *GetTabsByTabSubTypeReq, opts ...grpc.CallOption) (*GetTabsByTabSubTypeResp, error) {
	out := new(GetTabsByTabSubTypeResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetTabsByTabSubType", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertTab(ctx context.Context, in *UpsertTabReq, opts ...grpc.CallOption) (*UpsertTabResp, error) {
	out := new(UpsertTabResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DeleteTab(ctx context.Context, in *DeleteTabReq, opts ...grpc.CallOption) (*DeleteTabResp, error) {
	out := new(DeleteTabResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DeleteTab", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetTabs(ctx context.Context, in *GetTabsReq, opts ...grpc.CallOption) (*GetTabsResp, error) {
	out := new(GetTabsResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetTabs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertMinorSupervisionConfig(ctx context.Context, in *UpsertMinorSupervisionConfigReq, opts ...grpc.CallOption) (*UpsertMinorSupervisionConfigResp, error) {
	out := new(UpsertMinorSupervisionConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertMinorSupervisionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) BatchGetMinorSupervisionConfig(ctx context.Context, in *BatchGetMinorSupervisionConfigReq, opts ...grpc.CallOption) (*BatchGetMinorSupervisionConfigResp, error) {
	out := new(BatchGetMinorSupervisionConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchGetMinorSupervisionConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetTabsRealNameConfigs(ctx context.Context, in *GetTabsRealNameConfigsReq, opts ...grpc.CallOption) (*GetTabsRealNameConfigsResp, error) {
	out := new(GetTabsRealNameConfigsResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetTabsRealNameConfigs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpdateTabsRealNameConfig(ctx context.Context, in *UpdateTabsRealNameConfigReq, opts ...grpc.CallOption) (*UpdateTabsRealNameConfigResp, error) {
	out := new(UpdateTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpdateTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) AddTabsRealNameConfig(ctx context.Context, in *AddTabsRealNameConfigReq, opts ...grpc.CallOption) (*AddTabsRealNameConfigResp, error) {
	out := new(AddTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/AddTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DeleteTabsRealNameConfig(ctx context.Context, in *DeleteTabsRealNameConfigReq, opts ...grpc.CallOption) (*DeleteTabsRealNameConfigResp, error) {
	out := new(DeleteTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DeleteTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetUserTabsRealNameConfig(ctx context.Context, in *GetUserTabsRealNameConfigReq, opts ...grpc.CallOption) (*GetUserTabsRealNameConfigResp, error) {
	out := new(GetUserTabsRealNameConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetUserTabsRealNameConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetQuickMatchConfig(ctx context.Context, in *GetQuickMatchConfigReq, opts ...grpc.CallOption) (*GetQuickMatchConfigResp, error) {
	out := new(GetQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpdateQuickMatchConfig(ctx context.Context, in *UpdateQuickMatchConfigReq, opts ...grpc.CallOption) (*UpdateQuickMatchConfigResp, error) {
	out := new(UpdateQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpdateQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DeleteQuickMatchConfig(ctx context.Context, in *DeleteQuickMatchConfigReq, opts ...grpc.CallOption) (*DeleteQuickMatchConfigResp, error) {
	out := new(DeleteQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DeleteQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) ResortQuickMatchConfig(ctx context.Context, in *ResortQuickMatchConfigReq, opts ...grpc.CallOption) (*ResortQuickMatchConfigResp, error) {
	out := new(ResortQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/ResortQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) UpsertNewQuickMatchConfig(ctx context.Context, in *UpsertNewQuickMatchConfigReq, opts ...grpc.CallOption) (*UpsertNewQuickMatchConfigResp, error) {
	out := new(UpsertNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/UpsertNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) BatchGetNewQuickMatchConfig(ctx context.Context, in *BatchGetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*BatchGetNewQuickMatchConfigResp, error) {
	out := new(BatchGetNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/BatchGetNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) DelNewQuickMatchConfig(ctx context.Context, in *DelNewQuickMatchConfigReq, opts ...grpc.CallOption) (*DelNewQuickMatchConfigResp, error) {
	out := new(DelNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/DelNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelPlayTabClient) GetNewQuickMatchConfig(ctx context.Context, in *GetNewQuickMatchConfigReq, opts ...grpc.CallOption) (*GetNewQuickMatchConfigResp, error) {
	out := new(GetNewQuickMatchConfigResp)
	err := c.cc.Invoke(ctx, "/channel_play_tab.ChannelPlayTab/GetNewQuickMatchConfig", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelPlayTabServer is the server API for ChannelPlayTab service.
type ChannelPlayTabServer interface {
	// 批量获取玩法可见用户白名单
	BatchGetWhiteUidListByTabIds(context.Context, *BatchGetWhiteUidListByTabIdsReq) (*BatchGetWhiteUidListByTabIdsResp, error)
	// 保存玩法白名单,仅运营后台用
	SetWhiteUidListByTabId(context.Context, *SetWhiteUidListByTabIdReq) (*SetWhiteUidListByTabIdResp, error)
	// 获取指定类型的tab
	GetTabsByTabSubType(context.Context, *GetTabsByTabSubTypeReq) (*GetTabsByTabSubTypeResp, error)
	// 插入/更新tab,仅运营后台用
	UpsertTab(context.Context, *UpsertTabReq) (*UpsertTabResp, error)
	// 删除tab,仅运营后台用
	DeleteTab(context.Context, *DeleteTabReq) (*DeleteTabResp, error)
	// 获取所有tab,读缓存
	GetTabs(context.Context, *GetTabsReq) (*GetTabsResp, error)
	// 插入/更新玩法或者分类的未成年监管控制开关
	UpsertMinorSupervisionConfig(context.Context, *UpsertMinorSupervisionConfigReq) (*UpsertMinorSupervisionConfigResp, error)
	// 批量获取玩法或者分类的未成年监管控制开关
	BatchGetMinorSupervisionConfig(context.Context, *BatchGetMinorSupervisionConfigReq) (*BatchGetMinorSupervisionConfigResp, error)
	// （未成年）玩法开关协议配置 运营后台
	GetTabsRealNameConfigs(context.Context, *GetTabsRealNameConfigsReq) (*GetTabsRealNameConfigsResp, error)
	UpdateTabsRealNameConfig(context.Context, *UpdateTabsRealNameConfigReq) (*UpdateTabsRealNameConfigResp, error)
	AddTabsRealNameConfig(context.Context, *AddTabsRealNameConfigReq) (*AddTabsRealNameConfigResp, error)
	DeleteTabsRealNameConfig(context.Context, *DeleteTabsRealNameConfigReq) (*DeleteTabsRealNameConfigResp, error)
	// 用户入口筛选tab开关展示
	GetUserTabsRealNameConfig(context.Context, *GetUserTabsRealNameConfigReq) (*GetUserTabsRealNameConfigResp, error)
	// 快速匹配相关新接口 旧的首页玩法卡GetHomePagePlayCards相关和GetDetailTabInfoOfScene，GetSceneTabsForTT都不用了，后面统一使用新的
	GetQuickMatchConfig(context.Context, *GetQuickMatchConfigReq) (*GetQuickMatchConfigResp, error)
	UpdateQuickMatchConfig(context.Context, *UpdateQuickMatchConfigReq) (*UpdateQuickMatchConfigResp, error)
	DeleteQuickMatchConfig(context.Context, *DeleteQuickMatchConfigReq) (*DeleteQuickMatchConfigResp, error)
	ResortQuickMatchConfig(context.Context, *ResortQuickMatchConfigReq) (*ResortQuickMatchConfigResp, error)
	// 新版首页快速匹配入口配置 运营后台
	UpsertNewQuickMatchConfig(context.Context, *UpsertNewQuickMatchConfigReq) (*UpsertNewQuickMatchConfigResp, error)
	BatchGetNewQuickMatchConfig(context.Context, *BatchGetNewQuickMatchConfigReq) (*BatchGetNewQuickMatchConfigResp, error)
	DelNewQuickMatchConfig(context.Context, *DelNewQuickMatchConfigReq) (*DelNewQuickMatchConfigResp, error)
	// 客户端获取配置
	GetNewQuickMatchConfig(context.Context, *GetNewQuickMatchConfigReq) (*GetNewQuickMatchConfigResp, error)
}

func RegisterChannelPlayTabServer(s *grpc.Server, srv ChannelPlayTabServer) {
	s.RegisterService(&_ChannelPlayTab_serviceDesc, srv)
}

func _ChannelPlayTab_BatchGetWhiteUidListByTabIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetWhiteUidListByTabIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchGetWhiteUidListByTabIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchGetWhiteUidListByTabIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchGetWhiteUidListByTabIds(ctx, req.(*BatchGetWhiteUidListByTabIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_SetWhiteUidListByTabId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetWhiteUidListByTabIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).SetWhiteUidListByTabId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/SetWhiteUidListByTabId",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).SetWhiteUidListByTabId(ctx, req.(*SetWhiteUidListByTabIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetTabsByTabSubType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsByTabSubTypeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetTabsByTabSubType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetTabsByTabSubType",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetTabsByTabSubType(ctx, req.(*GetTabsByTabSubTypeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertTab(ctx, req.(*UpsertTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DeleteTab_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTabReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DeleteTab(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DeleteTab",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DeleteTab(ctx, req.(*DeleteTabReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetTabs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetTabs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetTabs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetTabs(ctx, req.(*GetTabsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertMinorSupervisionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertMinorSupervisionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertMinorSupervisionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertMinorSupervisionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertMinorSupervisionConfig(ctx, req.(*UpsertMinorSupervisionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_BatchGetMinorSupervisionConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetMinorSupervisionConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchGetMinorSupervisionConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchGetMinorSupervisionConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchGetMinorSupervisionConfig(ctx, req.(*BatchGetMinorSupervisionConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetTabsRealNameConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTabsRealNameConfigsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetTabsRealNameConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetTabsRealNameConfigs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetTabsRealNameConfigs(ctx, req.(*GetTabsRealNameConfigsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpdateTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpdateTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpdateTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpdateTabsRealNameConfig(ctx, req.(*UpdateTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_AddTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).AddTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/AddTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).AddTabsRealNameConfig(ctx, req.(*AddTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DeleteTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DeleteTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DeleteTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DeleteTabsRealNameConfig(ctx, req.(*DeleteTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetUserTabsRealNameConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserTabsRealNameConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetUserTabsRealNameConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetUserTabsRealNameConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetUserTabsRealNameConfig(ctx, req.(*GetUserTabsRealNameConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetQuickMatchConfig(ctx, req.(*GetQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpdateQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpdateQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpdateQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpdateQuickMatchConfig(ctx, req.(*UpdateQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DeleteQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DeleteQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DeleteQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DeleteQuickMatchConfig(ctx, req.(*DeleteQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_ResortQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResortQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).ResortQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/ResortQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).ResortQuickMatchConfig(ctx, req.(*ResortQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_UpsertNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpsertNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).UpsertNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/UpsertNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).UpsertNewQuickMatchConfig(ctx, req.(*UpsertNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_BatchGetNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).BatchGetNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/BatchGetNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).BatchGetNewQuickMatchConfig(ctx, req.(*BatchGetNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_DelNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).DelNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/DelNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).DelNewQuickMatchConfig(ctx, req.(*DelNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelPlayTab_GetNewQuickMatchConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNewQuickMatchConfigReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelPlayTabServer).GetNewQuickMatchConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/channel_play_tab.ChannelPlayTab/GetNewQuickMatchConfig",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelPlayTabServer).GetNewQuickMatchConfig(ctx, req.(*GetNewQuickMatchConfigReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChannelPlayTab_serviceDesc = grpc.ServiceDesc{
	ServiceName: "channel_play_tab.ChannelPlayTab",
	HandlerType: (*ChannelPlayTabServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BatchGetWhiteUidListByTabIds",
			Handler:    _ChannelPlayTab_BatchGetWhiteUidListByTabIds_Handler,
		},
		{
			MethodName: "SetWhiteUidListByTabId",
			Handler:    _ChannelPlayTab_SetWhiteUidListByTabId_Handler,
		},
		{
			MethodName: "GetTabsByTabSubType",
			Handler:    _ChannelPlayTab_GetTabsByTabSubType_Handler,
		},
		{
			MethodName: "UpsertTab",
			Handler:    _ChannelPlayTab_UpsertTab_Handler,
		},
		{
			MethodName: "DeleteTab",
			Handler:    _ChannelPlayTab_DeleteTab_Handler,
		},
		{
			MethodName: "GetTabs",
			Handler:    _ChannelPlayTab_GetTabs_Handler,
		},
		{
			MethodName: "UpsertMinorSupervisionConfig",
			Handler:    _ChannelPlayTab_UpsertMinorSupervisionConfig_Handler,
		},
		{
			MethodName: "BatchGetMinorSupervisionConfig",
			Handler:    _ChannelPlayTab_BatchGetMinorSupervisionConfig_Handler,
		},
		{
			MethodName: "GetTabsRealNameConfigs",
			Handler:    _ChannelPlayTab_GetTabsRealNameConfigs_Handler,
		},
		{
			MethodName: "UpdateTabsRealNameConfig",
			Handler:    _ChannelPlayTab_UpdateTabsRealNameConfig_Handler,
		},
		{
			MethodName: "AddTabsRealNameConfig",
			Handler:    _ChannelPlayTab_AddTabsRealNameConfig_Handler,
		},
		{
			MethodName: "DeleteTabsRealNameConfig",
			Handler:    _ChannelPlayTab_DeleteTabsRealNameConfig_Handler,
		},
		{
			MethodName: "GetUserTabsRealNameConfig",
			Handler:    _ChannelPlayTab_GetUserTabsRealNameConfig_Handler,
		},
		{
			MethodName: "GetQuickMatchConfig",
			Handler:    _ChannelPlayTab_GetQuickMatchConfig_Handler,
		},
		{
			MethodName: "UpdateQuickMatchConfig",
			Handler:    _ChannelPlayTab_UpdateQuickMatchConfig_Handler,
		},
		{
			MethodName: "DeleteQuickMatchConfig",
			Handler:    _ChannelPlayTab_DeleteQuickMatchConfig_Handler,
		},
		{
			MethodName: "ResortQuickMatchConfig",
			Handler:    _ChannelPlayTab_ResortQuickMatchConfig_Handler,
		},
		{
			MethodName: "UpsertNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_UpsertNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "BatchGetNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_BatchGetNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "DelNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_DelNewQuickMatchConfig_Handler,
		},
		{
			MethodName: "GetNewQuickMatchConfig",
			Handler:    _ChannelPlayTab_GetNewQuickMatchConfig_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "channel-play-tab/channel-play-tab.proto",
}

func init() {
	proto.RegisterFile("channel-play-tab/channel-play-tab.proto", fileDescriptor_channel_play_tab_53c946d10d565e08)
}

var fileDescriptor_channel_play_tab_53c946d10d565e08 = []byte{
	// 2222 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xbc, 0x19, 0xcd, 0x6e, 0xdb, 0xc8,
	0xd9, 0x94, 0x2c, 0xdb, 0xfa, 0x2c, 0x3b, 0xda, 0x89, 0xed, 0xc8, 0xb2, 0x9d, 0x28, 0xec, 0x6e,
	0xe3, 0x75, 0x12, 0xa7, 0xd1, 0x6e, 0x91, 0xc5, 0xa2, 0xcd, 0xd6, 0x71, 0x04, 0xc7, 0x8b, 0xd8,
	0xbb, 0x4b, 0xcb, 0x49, 0xb1, 0x41, 0x41, 0x0c, 0xa9, 0x89, 0xcc, 0x35, 0x45, 0x32, 0x9c, 0x51,
	0x36, 0x02, 0x8a, 0x1e, 0x7a, 0xe9, 0xa1, 0xe8, 0xb5, 0x97, 0xbe, 0x42, 0x2f, 0xbd, 0x16, 0xe8,
	0x03, 0x14, 0xe8, 0x03, 0x14, 0x7d, 0x9a, 0x62, 0x86, 0x43, 0xfd, 0x50, 0x33, 0x8a, 0xe4, 0x22,
	0xbd, 0x71, 0xbe, 0x99, 0xf9, 0xfe, 0xe7, 0xfb, 0x23, 0xdc, 0x71, 0x2f, 0x70, 0x10, 0x10, 0xff,
	0x7e, 0xe4, 0xe3, 0xde, 0x7d, 0x86, 0x9d, 0x07, 0x59, 0xc0, 0x7e, 0x14, 0x87, 0x2c, 0x44, 0x65,
	0x09, 0xb7, 0x39, 0xdc, 0x66, 0xd8, 0x31, 0x3b, 0x70, 0xeb, 0x09, 0x66, 0xee, 0xc5, 0x11, 0x61,
	0x2f, 0x2f, 0x3c, 0x46, 0xce, 0xbd, 0xd6, 0x73, 0x8f, 0xb2, 0x27, 0xbd, 0x26, 0x76, 0x8e, 0x5b,
	0xd4, 0x22, 0x6f, 0xd0, 0x0d, 0x58, 0x64, 0xd8, 0xb1, 0xbd, 0x16, 0xad, 0x18, 0xb5, 0xfc, 0xee,
	0x8a, 0xb5, 0xc0, 0xc4, 0x1e, 0xda, 0x80, 0x05, 0x1a, 0x76, 0x63, 0x97, 0x54, 0x72, 0x35, 0x63,
	0xb7, 0x68, 0xc9, 0x15, 0xda, 0x84, 0xa5, 0x20, 0xb4, 0x5d, 0xec, 0x5e, 0x90, 0x4a, 0xbe, 0x66,
	0xec, 0x2e, 0x59, 0x8b, 0x41, 0x78, 0xc8, 0x97, 0xe6, 0x5f, 0x73, 0x50, 0x9b, 0x4c, 0x8f, 0x46,
	0xa8, 0x05, 0x25, 0x4e, 0xd0, 0xf7, 0x28, 0xb3, 0x3b, 0x38, 0x12, 0x54, 0x97, 0xeb, 0x4f, 0xf6,
	0xb3, 0xcc, 0xef, 0xbf, 0x0f, 0xd3, 0x7e, 0x13, 0x3b, 0x1c, 0x76, 0x82, 0xa3, 0x46, 0xc0, 0xe2,
	0x9e, 0x05, 0xac, 0x0f, 0xa8, 0x9a, 0x50, 0x1a, 0xbe, 0x87, 0x10, 0xcc, 0x73, 0x8a, 0x52, 0x46,
	0xf1, 0x5d, 0xfd, 0x1d, 0x5c, 0xcb, 0xa0, 0x40, 0x65, 0xc8, 0x5f, 0x92, 0x5e, 0xc5, 0xa8, 0x19,
	0xbb, 0x2b, 0x16, 0xff, 0x44, 0xe7, 0x50, 0x78, 0x8b, 0xfd, 0x6e, 0xa2, 0x85, 0xe5, 0xfa, 0x57,
	0x57, 0xe0, 0x73, 0x78, 0xc3, 0x4a, 0xb0, 0x7d, 0x99, 0xfb, 0xc2, 0x30, 0x09, 0x6c, 0x9e, 0x29,
	0xaf, 0x71, 0xbb, 0xac, 0xc3, 0x42, 0x62, 0x17, 0xc9, 0x4c, 0x41, 0x98, 0x85, 0x6b, 0xbf, 0xeb,
	0xb5, 0x84, 0xf6, 0x2a, 0x39, 0x21, 0xcb, 0x62, 0x57, 0x8a, 0x38, 0x30, 0x58, 0x7e, 0xd8, 0x60,
	0xe6, 0x36, 0x54, 0x75, 0x64, 0x68, 0x64, 0xfe, 0xdd, 0x80, 0x7c, 0x13, 0x3b, 0x13, 0xe8, 0x71,
	0x70, 0x80, 0x3b, 0xa9, 0x1f, 0x70, 0x77, 0x39, 0xc5, 0x1d, 0x82, 0xb6, 0xa0, 0xe8, 0x75, 0x70,
	0x9b, 0xd8, 0xdd, 0xd8, 0x97, 0x24, 0x97, 0x04, 0xe0, 0x3c, 0xf6, 0xd1, 0xe3, 0xc4, 0xca, 0xb4,
	0xeb, 0xd8, 0xac, 0x17, 0x91, 0xca, 0x7c, 0xcd, 0xd8, 0x5d, 0xad, 0x6f, 0x8f, 0x6b, 0xaf, 0x89,
	0x9d, 0xb3, 0xae, 0xd3, 0xec, 0x45, 0x44, 0xd8, 0x4f, 0x7e, 0xa3, 0x8f, 0x61, 0xd5, 0xa3, 0xfc,
	0x80, 0xfd, 0xd6, 0xa3, 0x9e, 0xe3, 0x93, 0x4a, 0x41, 0xf8, 0x5a, 0xc9, 0xa3, 0x4d, 0xec, 0xbc,
	0x48, 0x60, 0xe6, 0x23, 0x28, 0x9d, 0x47, 0x94, 0xc4, 0xac, 0x89, 0x1d, 0xae, 0xb4, 0x3b, 0x90,
	0x67, 0xd8, 0x11, 0x12, 0x2c, 0xd7, 0xd7, 0x95, 0xc4, 0x2c, 0x7e, 0xc2, 0xbc, 0x06, 0x2b, 0x43,
	0x17, 0x69, 0x64, 0xfe, 0xd1, 0x80, 0x8d, 0x23, 0xc2, 0x97, 0x54, 0x68, 0x27, 0xe5, 0x89, 0xbc,
	0x19, 0x13, 0xc5, 0x98, 0x51, 0x94, 0x2b, 0x3c, 0xa4, 0xa7, 0x70, 0x43, 0xc9, 0x0c, 0x8d, 0xd0,
	0xa7, 0x30, 0xcf, 0xb0, 0x43, 0xe5, 0xb3, 0xd1, 0xc8, 0x28, 0x8e, 0x98, 0x9f, 0x40, 0xe9, 0x29,
	0xf1, 0x09, 0x23, 0x52, 0x3b, 0x6a, 0x13, 0x73, 0x5d, 0x0c, 0x1d, 0xa3, 0x91, 0xf9, 0x35, 0x80,
	0xa4, 0xce, 0x6f, 0x0d, 0xb3, 0x69, 0x8c, 0xb0, 0x89, 0x6e, 0x43, 0x29, 0x0c, 0xfc, 0x5e, 0xdf,
	0x44, 0x39, 0xb1, 0xbd, 0xcc, 0x61, 0xa9, 0x85, 0xbe, 0x80, 0xe5, 0x3e, 0xae, 0xd9, 0xb8, 0xff,
	0x4f, 0x0e, 0x36, 0x4e, 0xbc, 0x20, 0x8c, 0xcf, 0xba, 0x11, 0x89, 0x39, 0x89, 0x30, 0x38, 0x0c,
	0x83, 0xd7, 0x5e, 0x1b, 0xad, 0x42, 0x4e, 0x0a, 0x51, 0xb4, 0x72, 0x5e, 0x6b, 0x48, 0xb0, 0xdc,
	0xb0, 0xef, 0xde, 0x82, 0x65, 0x17, 0x33, 0xd2, 0x0e, 0xe3, 0x1e, 0xdf, 0xcb, 0x8b, 0x3d, 0x48,
	0x41, 0xc7, 0x2d, 0xb4, 0x03, 0xd0, 0xc1, 0xf1, 0x25, 0x61, 0x22, 0xfc, 0xcd, 0x8b, 0xe7, 0x54,
	0x4c, 0x20, 0x3c, 0x02, 0x7e, 0x07, 0x65, 0xfa, 0xa3, 0xc7, 0xdc, 0x0b, 0x3b, 0x0c, 0x6c, 0xea,
	0x92, 0x80, 0xd0, 0x4a, 0xa1, 0x96, 0xdf, 0x5d, 0xad, 0xdf, 0x19, 0x67, 0x3c, 0xcb, 0xea, 0x19,
	0x3f, 0x6f, 0xad, 0x26, 0x08, 0xbe, 0x49, 0x96, 0x14, 0x3d, 0x82, 0x4a, 0x40, 0x48, 0xcb, 0x76,
	0x2f, 0x88, 0x7b, 0x69, 0xc7, 0xa4, 0xed, 0x51, 0x46, 0x62, 0x9b, 0x79, 0x1d, 0x52, 0x59, 0xa8,
	0x19, 0xbb, 0x79, 0x6b, 0x9d, 0xef, 0x1f, 0xf2, 0x6d, 0x4b, 0xee, 0x36, 0xbd, 0x0e, 0x41, 0x27,
	0xb0, 0xec, 0x0a, 0xe1, 0x13, 0x1f, 0x5c, 0x14, 0x3e, 0x78, 0xef, 0xfd, 0x6c, 0x24, 0x1a, 0x4b,
	0x7c, 0xd2, 0xed, 0x7f, 0x9b, 0x2e, 0xdc, 0x4a, 0xfc, 0x5f, 0x7d, 0x9e, 0xdb, 0xfd, 0x57, 0xb0,
	0x90, 0x5c, 0x90, 0xcf, 0x69, 0x77, 0x5a, 0x62, 0x96, 0xbc, 0x67, 0x9a, 0x50, 0x9b, 0x4c, 0x84,
	0x46, 0xe6, 0xdf, 0x0c, 0xb8, 0x9d, 0x06, 0x50, 0x3d, 0x2f, 0xda, 0x24, 0x75, 0x1b, 0x4a, 0x43,
	0x26, 0xa6, 0x32, 0x24, 0x2e, 0x0f, 0x6c, 0x4c, 0x75, 0x61, 0x71, 0xc4, 0xaf, 0xe7, 0x47, 0xfd,
	0x7a, 0x07, 0x20, 0x26, 0xac, 0x1b, 0x07, 0x36, 0xf6, 0x7d, 0x19, 0x78, 0x8a, 0x09, 0xe4, 0xc0,
	0xf7, 0xcd, 0x7f, 0xe7, 0xc1, 0x7c, 0x1f, 0xcf, 0x34, 0x42, 0x04, 0x96, 0x39, 0xd3, 0x89, 0x32,
	0x52, 0x97, 0x7f, 0xaa, 0xcf, 0x1f, 0x7a, 0x54, 0xfc, 0x55, 0x24, 0x2b, 0x3a, 0xc8, 0x74, 0x12,
	0x80, 0x18, 0x94, 0xfb, 0x2a, 0x48, 0x69, 0xe5, 0x04, 0xad, 0xe3, 0x2b, 0xd1, 0x3a, 0x94, 0xc8,
	0x46, 0x08, 0x5e, 0x73, 0x47, 0xa1, 0xd5, 0xb6, 0xc8, 0x9d, 0xc3, 0x67, 0x14, 0xb9, 0xf3, 0xf1,
	0x68, 0xee, 0x9c, 0xde, 0x83, 0x06, 0x49, 0xb2, 0xea, 0xc3, 0x9a, 0x8a, 0xa3, 0x0f, 0x43, 0xcd,
	0x6c, 0xc0, 0x66, 0x3f, 0x5c, 0x61, 0x9f, 0xa7, 0x39, 0x49, 0x94, 0x7b, 0x21, 0x82, 0xf9, 0x08,
	0xb7, 0x89, 0xa4, 0x29, 0xbe, 0xd1, 0x1a, 0x14, 0xdc, 0xb0, 0x1b, 0xb0, 0x34, 0xf2, 0x88, 0x85,
	0xf9, 0x2f, 0x03, 0xd0, 0x38, 0x12, 0x9e, 0x31, 0xfb, 0xf1, 0x46, 0x62, 0x59, 0x4a, 0xc3, 0x8d,
	0x88, 0x56, 0x29, 0xc3, 0x97, 0x6d, 0x99, 0x2b, 0x40, 0x82, 0xbe, 0xbd, 0x6c, 0xf3, 0x47, 0xe0,
	0xfa, 0x9e, 0xfd, 0x96, 0xc4, 0xa9, 0x27, 0xbb, 0xbe, 0xf7, 0x82, 0xc4, 0xe8, 0x27, 0xb0, 0x22,
	0xe3, 0x14, 0x65, 0x98, 0x75, 0xa9, 0x74, 0xe7, 0x52, 0x02, 0x3c, 0x13, 0xb0, 0xe1, 0x27, 0x54,
	0x98, 0xf8, 0x84, 0x16, 0xc6, 0x9e, 0x90, 0x19, 0x43, 0x55, 0xa7, 0x15, 0x1a, 0xa1, 0xc7, 0xb0,
	0x38, 0xea, 0xe3, 0x1f, 0x2b, 0xc3, 0x7a, 0xe6, 0xae, 0x95, 0x5e, 0xe2, 0x2a, 0x64, 0x21, 0xc3,
	0x7e, 0x3f, 0x78, 0xf3, 0x85, 0xf9, 0x0a, 0xb6, 0xce, 0xa3, 0x16, 0x16, 0x59, 0x29, 0x7b, 0x95,
	0xbc, 0x41, 0xbf, 0xc8, 0x44, 0xa7, 0xe9, 0x68, 0xa6, 0x91, 0xe9, 0x26, 0x6c, 0xeb, 0x91, 0xd3,
	0xc8, 0xfc, 0x35, 0x54, 0x0e, 0x5a, 0xad, 0x0f, 0x41, 0x79, 0x0b, 0x36, 0x35, 0x98, 0x69, 0x64,
	0x32, 0xd8, 0xea, 0x67, 0x62, 0x05, 0xe5, 0x0f, 0xe3, 0x3e, 0x5c, 0x19, 0x7a, 0xaa, 0x34, 0x32,
	0xbb, 0xb0, 0x7d, 0x44, 0xd8, 0x39, 0x25, 0xf1, 0xff, 0x81, 0xad, 0x95, 0x3e, 0x5b, 0x36, 0xec,
	0x4c, 0x20, 0x9b, 0xf5, 0x3b, 0x63, 0x66, 0xbf, 0x33, 0xff, 0x92, 0x87, 0xa2, 0x48, 0xcb, 0xc7,
	0xc1, 0xeb, 0x70, 0xda, 0x9a, 0x42, 0xd6, 0xc3, 0x22, 0x09, 0x27, 0xfc, 0xf2, 0x67, 0x25, 0xea,
	0xbc, 0x4c, 0xb9, 0x31, 0x3f, 0x56, 0x6e, 0x1c, 0x8d, 0xe6, 0xf0, 0x82, 0xc8, 0xe1, 0x3f, 0x1d,
	0x67, 0xfa, 0xbb, 0xae, 0xe7, 0x5e, 0x9e, 0xf0, 0x48, 0xad, 0xce, 0xde, 0x68, 0x0f, 0x3e, 0x6a,
	0xe3, 0x0e, 0xb1, 0x5b, 0x1e, 0x15, 0x97, 0x44, 0x75, 0xbe, 0x20, 0x58, 0xbf, 0xc6, 0x37, 0x9e,
	0x26, 0xf0, 0xb4, 0x4a, 0x77, 0xda, 0xb6, 0x1b, 0xfa, 0x61, 0x4c, 0x2b, 0x8b, 0xb5, 0x3c, 0xaf,
	0xd2, 0x9d, 0xf6, 0xa1, 0x58, 0xf3, 0xc8, 0xe1, 0x74, 0x19, 0x0b, 0x03, 0x9b, 0xbc, 0x7e, 0x4d,
	0x5c, 0x56, 0x59, 0x12, 0x4c, 0x97, 0x12, 0x60, 0x43, 0xc0, 0x38, 0x86, 0x1f, 0xba, 0x9d, 0xc8,
	0xf6, 0xbd, 0xe0, 0xb2, 0x52, 0x4c, 0xea, 0x7c, 0x0e, 0x78, 0xee, 0x05, 0x97, 0xe8, 0x25, 0xac,
	0x75, 0xbc, 0xc0, 0xb3, 0x05, 0x3f, 0xe4, 0x1d, 0x8b, 0xb1, 0xed, 0x05, 0xaf, 0xc3, 0x0a, 0x08,
	0x8b, 0x28, 0x84, 0x7b, 0x16, 0xf2, 0xe4, 0xe3, 0x1d, 0xe1, 0x0e, 0x69, 0xf0, 0xe3, 0x5c, 0xf9,
	0xd6, 0x47, 0x9d, 0x2c, 0x88, 0x87, 0xd0, 0x35, 0xd5, 0x59, 0x9e, 0x9c, 0xbd, 0x80, 0xc5, 0xa1,
	0xcd, 0xc8, 0x3b, 0x26, 0x0d, 0x56, 0x14, 0x90, 0x26, 0x79, 0xc7, 0xb8, 0xdd, 0x9c, 0xb6, 0x68,
	0x49, 0x12, 0x5f, 0x2b, 0x38, 0x6d, 0xde, 0x8f, 0x6c, 0xc2, 0x52, 0xaa, 0x06, 0xe9, 0xfe, 0x8b,
	0x52, 0x0b, 0xdc, 0x6e, 0x52, 0x09, 0x02, 0xe3, 0x7c, 0xe2, 0xa2, 0x09, 0x48, 0xa0, 0x1c, 0x68,
	0x49, 0xaa, 0xb1, 0x20, 0xd4, 0x28, 0xb5, 0x24, 0x55, 0xb9, 0x03, 0xe0, 0x87, 0x8c, 0x79, 0x49,
	0x3b, 0x94, 0x18, 0xa3, 0x98, 0x40, 0xce, 0x63, 0x3f, 0xed, 0x2f, 0xb2, 0xa6, 0xe5, 0xef, 0x27,
	0xe3, 0x16, 0xc6, 0x95, 0xdd, 0x22, 0xcd, 0x4f, 0xb9, 0xd1, 0xfc, 0xe4, 0x7b, 0x1d, 0x8f, 0x49,
	0x67, 0x4d, 0x16, 0xe6, 0xb9, 0xe8, 0x2f, 0xc6, 0x99, 0xa1, 0x11, 0xfa, 0x12, 0x40, 0x94, 0xba,
	0x89, 0x19, 0x93, 0x80, 0xbe, 0x35, 0xce, 0x4c, 0xff, 0xe1, 0x58, 0x45, 0x9a, 0x7e, 0x9a, 0x2f,
	0x61, 0x33, 0x09, 0xab, 0x2a, 0x31, 0xb3, 0x88, 0x8d, 0x19, 0x10, 0x6f, 0x43, 0x55, 0x87, 0x98,
	0x46, 0xe6, 0x5d, 0xd8, 0x4c, 0x02, 0x98, 0x8a, 0x6c, 0xe6, 0x5d, 0x73, 0x54, 0xba, 0xc3, 0x34,
	0x32, 0xef, 0xc3, 0xa6, 0x45, 0x68, 0x18, 0x2b, 0x0d, 0x55, 0x86, 0x7c, 0x5a, 0x81, 0x16, 0x2d,
	0xfe, 0xc9, 0x91, 0xe9, 0x8e, 0xd3, 0xc8, 0xfc, 0xa7, 0x01, 0xd7, 0x4f, 0xc9, 0x8f, 0xd9, 0xbd,
	0x19, 0x43, 0x8d, 0x78, 0xdc, 0xf9, 0xd1, 0xd6, 0x9b, 0xa7, 0x4c, 0x8f, 0xf9, 0x44, 0x3a, 0x6b,
	0xb2, 0xc8, 0x3a, 0x72, 0x61, 0xcc, 0x91, 0xab, 0xb0, 0x14, 0x85, 0xd4, 0x63, 0x5e, 0x18, 0x08,
	0x0f, 0x5d, 0xb1, 0xfa, 0x6b, 0xee, 0xbf, 0x5d, 0xa1, 0xe2, 0x96, 0x8d, 0x99, 0xe8, 0x2f, 0xf2,
	0x56, 0x51, 0x42, 0x0e, 0x98, 0xf9, 0x1b, 0x9e, 0x31, 0x79, 0x2d, 0xaf, 0x10, 0x88, 0xeb, 0xe6,
	0x97, 0x99, 0xac, 0xf8, 0xc9, 0xb8, 0x65, 0x55, 0x37, 0xd3, 0xb4, 0x78, 0x0b, 0x76, 0x26, 0xa0,
	0xa7, 0x91, 0xf9, 0x08, 0x6e, 0xa6, 0xb5, 0xab, 0x86, 0x03, 0x4d, 0x77, 0xeb, 0x0c, 0x46, 0x60,
	0x1a, 0xdc, 0xe8, 0xab, 0x6c, 0x01, 0x33, 0x25, 0xf3, 0xfd, 0x4c, 0x92, 0x38, 0xa0, 0x86, 0x2f,
	0xb5, 0x03, 0xea, 0xe4, 0xac, 0x8b, 0x02, 0x74, 0x36, 0x11, 0x5f, 0x89, 0xf2, 0x4c, 0x27, 0xdd,
	0xff, 0x66, 0x99, 0xbd, 0x87, 0x00, 0x83, 0x09, 0x04, 0x5a, 0x82, 0xf9, 0xa3, 0x83, 0x93, 0x46,
	0x79, 0x0e, 0x6d, 0xc2, 0xfa, 0xe1, 0x37, 0x27, 0xdf, 0x5a, 0x8d, 0x67, 0x8d, 0xd3, 0xb3, 0xe3,
	0x17, 0x0d, 0xfb, 0xf0, 0xd9, 0xc1, 0xe9, 0x69, 0xe3, 0x79, 0xd9, 0xd8, 0x7b, 0x0c, 0xeb, 0xca,
	0x6e, 0x98, 0xdf, 0x3e, 0x0d, 0x03, 0x52, 0x9e, 0x43, 0x2b, 0x50, 0x6c, 0x04, 0x8c, 0xc4, 0x56,
	0x18, 0x76, 0xca, 0x06, 0x2a, 0xc1, 0xd2, 0xd7, 0xa1, 0x17, 0xf0, 0x40, 0x5f, 0xce, 0xed, 0xbd,
	0x82, 0xaa, 0xbe, 0x8d, 0x45, 0x08, 0x56, 0x07, 0x2b, 0x89, 0xee, 0x23, 0x58, 0x19, 0xc0, 0x9a,
	0xd8, 0x29, 0x1b, 0x68, 0x03, 0xd0, 0x00, 0x94, 0x76, 0x10, 0xe5, 0xdc, 0xde, 0x3f, 0x0c, 0x58,
	0x53, 0x45, 0x52, 0xb4, 0x0c, 0x8b, 0xc7, 0xc1, 0x5b, 0xec, 0x7b, 0xad, 0xf2, 0x1c, 0xaa, 0xc0,
	0xda, 0x69, 0x18, 0x77, 0xb0, 0x3f, 0x38, 0xfa, 0xdc, 0xa3, 0xac, 0x6c, 0xa0, 0xeb, 0x70, 0xed,
	0x24, 0x8c, 0xc9, 0x21, 0x8e, 0x5b, 0x72, 0x78, 0x58, 0xce, 0x71, 0xfa, 0x23, 0x3a, 0x2c, 0xe7,
	0x51, 0x0d, 0xb6, 0x79, 0xee, 0xfa, 0x3e, 0x0c, 0x48, 0xe3, 0x5d, 0x14, 0x52, 0x92, 0xc1, 0x34,
	0x8f, 0x6e, 0xc0, 0xf5, 0xf4, 0x04, 0xc7, 0x98, 0x62, 0x2b, 0xa0, 0x2d, 0xb8, 0x91, 0x6e, 0x3c,
	0x0b, 0xd9, 0x41, 0x4c, 0x30, 0x57, 0x8c, 0xd8, 0x5c, 0xa8, 0xff, 0xf9, 0x3a, 0xac, 0x1e, 0xca,
	0xf2, 0xc9, 0xc7, 0xbd, 0x26, 0x76, 0xd0, 0x1f, 0x0c, 0xd8, 0x9e, 0x34, 0x84, 0x44, 0x0f, 0x67,
	0x1d, 0x5a, 0xbe, 0xa9, 0xd6, 0x67, 0x9f, 0x73, 0x9a, 0x73, 0xa8, 0x0b, 0x1b, 0xea, 0x51, 0x23,
	0xba, 0xab, 0x88, 0xf4, 0xba, 0xd9, 0x67, 0xf5, 0xde, 0xf4, 0x87, 0x05, 0x59, 0x1f, 0xae, 0x2b,
	0xc6, 0x65, 0x48, 0xd1, 0x01, 0xaa, 0x47, 0x7c, 0xd5, 0x4f, 0xa7, 0x3c, 0x29, 0xa8, 0x9d, 0x42,
	0xb1, 0x3f, 0x3b, 0x44, 0x37, 0xc7, 0x6f, 0x0e, 0x4f, 0x24, 0xab, 0xb7, 0x26, 0xee, 0xa7, 0xf8,
	0xfa, 0xf5, 0xb7, 0x0a, 0xdf, 0xf0, 0x0c, 0x4f, 0x85, 0x6f, 0x74, 0x78, 0x37, 0x87, 0x9e, 0xc1,
	0xa2, 0x64, 0x1e, 0x6d, 0x6b, 0xe5, 0xe2, 0xb8, 0x76, 0x26, 0xec, 0x0a, 0x4c, 0xdc, 0xb1, 0x26,
	0x4d, 0x70, 0x54, 0x8e, 0xf5, 0x9e, 0xb1, 0x92, 0xca, 0xb1, 0xde, 0x3b, 0x24, 0x9a, 0x43, 0x7f,
	0x32, 0x06, 0xf1, 0x5f, 0xc3, 0xcb, 0x67, 0xb3, 0x4f, 0x3b, 0xde, 0x54, 0x3f, 0xbf, 0xca, 0x88,
	0xc4, 0x9c, 0x43, 0xb4, 0x3f, 0x2d, 0xce, 0x74, 0xc4, 0x2a, 0x47, 0xd7, 0x4e, 0x14, 0x54, 0x8e,
	0x3e, 0xa1, 0xd1, 0xee, 0x41, 0x45, 0xd7, 0xb5, 0xa2, 0xfb, 0x2a, 0xb5, 0x6a, 0xdb, 0xe7, 0xea,
	0xfe, 0x2c, 0xc7, 0x69, 0x84, 0x22, 0x58, 0x57, 0xb6, 0xad, 0x68, 0x6f, 0x1c, 0x91, 0xae, 0x73,
	0xae, 0xde, 0x9d, 0xfa, 0x6c, 0x22, 0xac, 0xae, 0x2b, 0x55, 0x09, 0x3b, 0xa1, 0x6f, 0x56, 0x09,
	0x3b, 0xa9, 0xe1, 0x45, 0xbf, 0x15, 0x39, 0x58, 0xdd, 0x79, 0xa2, 0x7d, 0xa5, 0xc9, 0xb4, 0xdd,
	0x71, 0xf5, 0xc1, 0x4c, 0xe7, 0x69, 0x84, 0x7e, 0x10, 0xc1, 0x6c, 0xac, 0x68, 0x54, 0x07, 0x33,
	0x45, 0x95, 0xa0, 0x09, 0x66, 0xca, 0xda, 0x80, 0xc2, 0x86, 0xba, 0xae, 0x56, 0xb9, 0xb1, 0xb6,
	0xb4, 0x57, 0xb9, 0xb1, 0xbe, 0x5c, 0xe7, 0x44, 0xd5, 0x15, 0xb8, 0x8a, 0xa8, 0xb6, 0xb0, 0x57,
	0x11, 0xd5, 0x17, 0xf6, 0x9c, 0xa8, 0xba, 0x52, 0x57, 0x11, 0xd5, 0xb6, 0x00, 0x2a, 0xa2, 0xfa,
	0x06, 0x80, 0x3b, 0x92, 0xb6, 0xaa, 0x45, 0xfb, 0xba, 0x40, 0xa8, 0x2e, 0xfe, 0x54, 0x8e, 0x34,
	0xb1, 0x64, 0x46, 0xbf, 0x37, 0x60, 0x6b, 0x42, 0xe9, 0x8b, 0x7e, 0xa6, 0x8f, 0x7d, 0x1a, 0x16,
	0x1e, 0xce, 0x78, 0xa3, 0x6f, 0x6c, 0x15, 0x79, 0xb5, 0xb1, 0x35, 0x94, 0xef, 0x4d, 0x7f, 0x38,
	0x21, 0xaa, 0x91, 0x59, 0x1d, 0x9d, 0xa7, 0x27, 0xaa, 0x97, 0xf4, 0xc9, 0xe7, 0xdf, 0xd7, 0xdb,
	0xa1, 0x8f, 0x83, 0xf6, 0xfe, 0xcf, 0xeb, 0x8c, 0xed, 0xbb, 0x61, 0xe7, 0x81, 0xf8, 0x2d, 0xef,
	0x86, 0xfe, 0x03, 0xca, 0x93, 0x88, 0x4b, 0xe8, 0xd8, 0x9f, 0x7b, 0x67, 0x41, 0x9c, 0xf9, 0xec,
	0xbf, 0x01, 0x00, 0x00, 0xff, 0xff, 0xe9, 0xc2, 0xa4, 0x73, 0xe5, 0x1f, 0x00, 0x00,
}
